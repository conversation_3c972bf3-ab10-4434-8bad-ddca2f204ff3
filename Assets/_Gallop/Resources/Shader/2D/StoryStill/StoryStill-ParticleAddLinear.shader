Shader "Gallop/2D/StoryStill/Still-AdditiveLinear" {
Properties {
    _MainTex ("Texture", 2D) = "white" {}
    _Color("Color", Color) = (0.5,0.5,0.5,1.0)
}

CGINCLUDE
#include "UnityCG.cginc"
#include "UnityUI.cginc"
ENDCG

Category {
    SubShader {
        Tags { "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" }


        Fog { Mode Off }
        Cull Off
        ZWrite Off

        Blend One One

        Lighting Off
        
        
        Pass {

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            CBUFFER_START(UnityPerMaterial)
            sampler2D _MainTex;
            half4 _Color;
            CBUFFER_END

            struct appdata_t
            {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
                fixed4 color : COLOR;
            };
    
            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 texcoord : TEXCOORD0;
                fixed4 color : COLOR;
            };

            v2f o;
            v2f vert (appdata_t v)
            {
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord = v.texcoord;
                o.color = v.color * _Color;
                return o;
            }
                
            fixed4 frag (v2f IN) : COLOR
            {
                half4 col = tex2D(_MainTex, IN.texcoord) * IN.color;
                return col;
            }
            ENDCG
        }
    }
}
}