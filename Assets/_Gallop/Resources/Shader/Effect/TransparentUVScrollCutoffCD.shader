// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:11Z
shader "Cygames/Effect/TransparentCustom/TransparentUVScrollCutoffCD"
{
    Properties
    {
        _Color("Color", Color) = (1, 1, 1, 1)
        _MainTex("Base (RGBA)", 2D) = "white" {}
        _MaskTex("Mask (RGBA) ", 2D) = "white" {}
        [HideInInspector]_FillColor("Fill Color", Color) = (1, 1, 1, 1)
        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue" = "Transparent"
            "RenderType" = "Transparent"
            "IgnoreProjector" = "True"
            "LightMode" = "UniversalForward"
        }

        //render state
        Blend SrcAlpha OneMinusSrcAlpha
        Cull[_CullMode]
        ZTest[_ZTestMode]
        Lighting Off
        ZWrite Off
        ColorMask RGB
        LOD 100

        HLSLINCLUDE
        #define USE_TEXTURE0 // for texture
        #define USE_COLOR // for color
        #define USE_VERTEX_COLOR // for vertex color
        #define USE_CUSTOM_UVSCROLL_TEXCOORD1 // for uvscroll
        #define USE_MASKTEX // for mask
        #define USE_CUTOFF_CUSTOM // for cutoff
        #define USE_FILLCOLOR // for fill color
        #define USE_CUSTOM // for customdata
        #define USE_DEFAULT_RANGE // for default range

        #include "../Common/Common.hlsl"
        #include "../Common/Effect.hlsl"
        ENDHLSL

        PASS
        {
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma vertex VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
    }

}
