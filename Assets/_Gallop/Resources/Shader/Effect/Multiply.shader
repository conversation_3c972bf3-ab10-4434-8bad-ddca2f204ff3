// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:10Z
shader "Cygames/Effect/_Other/Multiply"
{
    Properties
    {
        _Color  ( "Color",       Color ) = ( 0.5, 0.5, 0.5, 0.5 )
        _MainTex( "Base (RGBA)", 2D )    = "white" {}
        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }
    
    SubShader
    {
        Tags
        {
            "Queue"           = "Transparent" 
            "RenderType"      = "Transparent"
            "IgnoreProjector" = "True"
            "LightMode" = "UniversalForward"
        }
        
        //render state
        Blend Zero SrcColor
        Cull[_CullMode]
        ZTest[_ZTestMode]
        Lighting Off
        ZWrite Off
        Fog { Color (1,1,1,1) }
        LOD 100    
        
        HLSLINCLUDE
        #define USE_TEXTURE0     // for texture
        #define USE_COLOR        // for color
        #define USE_VERTEX_COLOR // for vertex color
        #define USE_MULTIPLY     // for multiply

        #include "../Common/Common.hlsl"
        #include "../Common/Effect.hlsl"
        ENDHLSL
        
        Pass 
        {
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma target 3.0
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
    }
}
