// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:10Z
Shader "Cygames/Effect/Transparent/TransparentPolarCoordinate"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _MaskTex ("Mask Texture" , 2D) = "white"{}
        _Color("Color", Color) = (1,1,1,1)
        _SclSpd("スクロール速度", Float) = 0
        _RotSpd("回転速度", Float) = 0
        _SclMsk("マスクスクロール速度", Float) = 0
        _RotMsk("マスク回転速度", Float) = 0
        _XOffs("X中心オフセット", Float) = 0
        _YOffs("Y中心オフセット", Float) = 0
        _MainTileX("Main Tile X", Float) = 1
        _MainTileY("Main Tile Y", Float) = 1
        _MainOffsX("Main Offset X", Float) = 0
        _MainOffsY("Main Offset Y", Float) = 0
        _MaskTileX("Mask Tile X", Float) = 1
        _MaskTileY("Mask Tile Y", Float) = 1
        _MaskOffsX("Mask Offset X", Float) = 0
        _MaskOffsY("Mask Offset Y", Float) = 0
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
        [Enum(UnityEngine.Rendering.BlendMode)]_BlendSrc("Blend Src", Float) = 5
        [Enum(UnityEngine.Rendering.BlendMode)]_BlendDst("Blend Dst", Float) = 10

        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }
    SubShader
    {
        
        Tags { "RenderType"="Transparent" "Queue" = "Transparent"}
        LOD 100
        Cull [_Cull]
        Blend [_BlendSrc][_BlendDst]
        Lighting Off
        ZWrite Off
        ZTest[_ZTestMode]


        HLSLINCLUDE
        #define USE_TEXTURE0      // for texture
        #define USE_MASKTEX
        #define USE_COLOR         // for color
        #define USE_VERTEX_COLOR  // for vertex color
        #define USE_DEFAULT_RANGE // for default range
        #define USE_POLARCOORDINATE
        #define USE_POLAR_TRANSPARENT
        #define USE_LIGHTPROBE

        #include "../Common/Common.hlsl"
        #include "../Common/Effect.hlsl"
        ENDHLSL

        Pass 
        {
            Tags{ "LightMode" = "UniversalForward" }
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma target 3.0
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
}
