// フォトカード用にUIマスクに対応させたShader
// Cygames/Effect/Additive/AdditiveMultiplyの差し替え用

shader "Gallop/Effect/SupportCard/AdditiveMultiply"
{
    Properties
    {
        _Color      ( "Color",       Color ) = ( 0.5, 0.5, 0.5, 0.5 )
        _MainTex    ( "Base (RGBA)", 2D )    = "white" {}
        _MultiplyTex( "Multiply (RGBA)", 2D )    = "white" {}

        // マスク処理
        _PictureTex("Picture", 2D) = "white" {}
        _ClipRect("ClipRect", Vector) = (0,0,0,0)
        _MaskRect("MaskRect", Vector) = (0,0,0,0)
        _UseClip("_UseClip", Float) = 0

        // Alpha(画像のフェードで使用する数値)
        _AlphaParamEnable("AlphaParamEnable", Float) = 0
        _AlphaParam("AlphaParam", Vector) = (0,0,0,0)
        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2
    }
    
    SubShader
    {
        Tags
        {
            "Queue"           = "Transparent" 
            "RenderType"      = "Transparent"
            "IgnoreProjector" = "True"
        }
        
        //render state
        Blend One OneMinusSrcAlpha
        ColorMask RGB
        Cull[_CullMode]
        ZTest[_ZTestMode]
        Lighting Off
        ZWrite Off
        Fog { Color (0,0,0,0) }
        LOD 100    
        
        HLSLINCLUDE
        #define USE_TEXTURE0          // for texture
        #define USE_COLOR             // for color
        #define USE_VERTEX_COLOR      // for vertex color
        #define USE_ADDITIVE_MULTIPLY // for multiply
        #define USE_GALLOP_UI_MASK
        #define USE_GALLOP_UI_PREMULTIPLED

        #include "../../Common/Common.hlsl"
        #include "../../Common/Effect.hlsl"
        ENDHLSL
        
        Pass 
        {
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma target 3.0
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
    }
}
