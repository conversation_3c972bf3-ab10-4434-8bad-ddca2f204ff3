//画面に対して加算するシェーダー

Shader "Gallop/UIAnimation/AdditiveTintColorMaskable" 
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _Color("Color", Color) = (0.5,0.5,0.5,1.0)
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2
                
        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
        _ColorMask ("Color Mask", Float) = 15
         
        [HideInInspector] _TintAreaStatus("TintAreaStatus", float) = 0
        [HideInInspector] _TintColorStatus("TintColorStatus", float) = 0
        [HideInInspector] _TintBlendRate("TintBlendRate", Range(0.0,1.0)) = 1.0
        [HideInInspector] _TintProgress("TintProgress", Range(0.0,1.0)) = 1.0
        [HideInInspector] _TintProgressCoord("TintProgressCoord", float) = 0
        [HideInInspector] _TintSharpness("TintSharpness", Range(0.0,1.0)) = 1.0
        [HideInInspector] _TintSharpnessCoord("TintSharpnessCoord", float) = 0
        [HideInInspector][Toggle]_TintInverseValue("TintInverseValue", float) = 0
        [HideInInspector][HDR] _TintColor("TintColor", Color) = (1, 1, 1, 1)
        [HideInInspector] _TintMap("TintMap", 2D) = "white" {}
        [HideInInspector] _IsUseInverseGradientThreshold("IsUseGradetion", float) = 1
        [HideInInspector] _TintInverseGradientThreshold("TintThreshold",Range(0.0,1.0)) = 1.0
    }

    Category 
    {
        Tags { "Queue"="Transparent" "IgnoreProjector"="True" "RenderType"="Transparent" }
        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }
        Blend SrcAlpha One
        ColorMask RGBA
        Cull Off
        Lighting Off
        ZWrite Off
        ZTest[_ZTestMode]
        Fog { Color (0,0,0,0) }
    
        SubShader 
        {
            Pass 
            {
                HLSLPROGRAM
                #include "hlsl/TintColor.hlsl"

                #pragma vertex TintColorVertexShader
                #pragma fragment TintColorFragmentShader

                #pragma multi_compile __ UNITY_UI_CLIP_RECT
                #pragma multi_compile __ UNITY_UI_ALPHACLIP
                
                ENDHLSL
            }
            Pass
            {
                Name "Projector"
                Tags{ "LightMode" = "Projector" }
            
                HLSLPROGRAM
                #pragma vertex vertexProjector
                #pragma fragment fragmentProjector
                float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
                half4 fragmentProjector() : SV_TARGET { return 0; }
            
                ENDHLSL
            }
        }

        CustomEditor "Gallop.TintColorShaderGUI"
    }
}