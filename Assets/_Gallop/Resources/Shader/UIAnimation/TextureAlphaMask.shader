Shader "Gallop/UIAnimation/TextureAlphaMask" {
    Properties{
        _MainTex("Main Texture", 2D) = "white" {}
        _MaskTex("Mask Texture", 2D) = "white" {}
        _BaseColor("Base Color", Color) = (1.0,1.0,1.0,1.0)
    }

    CGINCLUDE
    #include "UnityCG.cginc"
    #include "UnityUI.cginc"
    ENDCG
    
    SubShader{
        Tags{
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
        }

        Blend SrcAlpha OneMinusSrcAlpha

        Pass {

            CGPROGRAM
            #pragma vertex vert_img
            #pragma fragment frag
            CBUFFER_START(UnityPerMaterial)
            sampler2D _MainTex;
            sampler2D _MaskTex;
            float4 _BaseColor;
            CBUFFER_END

            fixed4 frag(v2f_img  IN) : COLOR
            {
                float4 col = tex2D(_MainTex, IN.uv);
                col *= _BaseColor;
                col.a *= tex2D(_MaskTex, IN.uv).a;
                return col;
            }
            ENDCG
        }
    }
}
