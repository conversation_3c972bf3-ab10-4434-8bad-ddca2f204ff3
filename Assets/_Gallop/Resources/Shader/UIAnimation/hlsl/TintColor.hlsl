#ifndef _TINT_COLOR_HLSL_INC_
#define _TINT_COLOR_HLSL_INC_

#include "../../Common/ShaderCommon.hlsl"
#include "UnityUI.cginc"

CBUFFER_START(UnityPerMaterial)
sampler2D _MainTex;
float4 _Color;
sampler2D _TintMap;
float4 _TintColor;
float _TintAreaStatus;
float _TintColorStatus;
float _TintInverseValue;
float _TintBlendRate;
float _TintProgress;
float _TintProgressCoord;
float _TintSharpness;
float _TintSharpnessCoord;
float _IsUseInverseGradientThreshold;
float _TintInverseGradientThreshold;
float4 _ClipRect; // GUI Maskする場合に使用
CBUFFER_END

struct appdata_t
{
    float4 vertex : POSITION;
    float3 normal : NORMAL;
    float2 texcoord : TEXCOORD0;
    float4 customTexCoord1 : TEXCOORD1;
    float4 customTexCoord2 : TEXCOORD2;
    fixed4 color : COLOR;
};
        
struct fragmentBuffer
{
    float4 vertex : SV_POSITION;
    float2 texcoord : TEXCOORD0;
    float3 worldNormal : TEXCOORD1;
    float3 worldViewDir : TEXCOORD2;
    float4 customTexCoord1 : TEXCOORD3;
    float4 customTexCoord2 : TEXCOORD4;
    float4 worldPosition : TEXCOORD5; // GUI Maskする場合に使用
    fixed4 color : COLOR;
};

//TintColor用のリムのしきい値を取得する処理
float GetRimValue(half rim, half progress, half sharpness, half inverse)
{
    int isInverse = step(inverse, 0.5f);
    rim = lerp(rim, 1.0f - rim, isInverse);
    progress = min(1.0f, progress);
    half width = 1.0f - min(1.0f, sharpness);
    half start = lerp(-width, 1.0f, progress);
    half last = lerp(0.0f, 1.0f + width, progress);

    return smoothstep(start, last, rim);
}

//ParticleSystemで設定されたCustomTexCoordの１と２のいずれかの軸の情報を取得する処理
//coordに入る値は0 ~ 23までで 10の位がどのfloat4を利用するか、 1の位はどの軸を利用するかの情報が格納されている
// index1 : 0 = float4(0,0,0,0), 1 = input1, 2 = input2
// index2 : 0 = x_axis, 1 = y_axis, 2 = z_axis, 3 = w_axis
float GetCustomTexcoordValue(half coord, float4 input1, float4 input2)
{
    int index1 = coord % 10;
    int index2 = coord / 10;
    const int isUseDefaultValue = step(index1, COMMON_EPSILON);
    int isUseXAxis = index2 == 0;
    int isUseYAxis = index2 == 1;
    int isUseZAxis = index2 == 2;
    int isUseWAxis = index2 == 3;
    
    float4 vertex = lerp(input1 * isUseDefaultValue,input2,saturate(index1 - 1.0f));
    return vertex.x * step(COMMON_EPSILON, isUseXAxis)
            + vertex.y * step(COMMON_EPSILON, isUseYAxis)
            + vertex.z * step(COMMON_EPSILON, isUseZAxis)
            + vertex.w * step(COMMON_EPSILON, isUseWAxis);
}

//TintColorを反映したカラーを取得するメソッド
//対象色に対して線形補間で出力するのでカラーの乗算とは出力が異なる
half4 GetTintColor(half4 color, float2 uv, float3 worldNormal, float3 worldViewDir, float4 customCoord1, float4 customCoord2)
{
    half isApplyTintColor = saturate(_TintColorStatus);
    half isApplyMap = saturate(_TintColorStatus - 1.0f);
    
    //TintColor反映処理
    //tintColor = lerp(NotApplyTintColor, (ApplyTintColor)lerp(SingleColor, mapColor, isApplyMap), isApplyTintColor);
    half4 tintColor = lerp(half4(1,1,1,1), lerp(_TintColor, tex2D(_TintMap,uv), isApplyMap), isApplyTintColor);
    
    //リムのしきい値を取得
    half rimProgress = _TintProgress * GetCustomTexcoordValue(_TintProgressCoord, customCoord1, customCoord2);
    half rimSharpness = _TintSharpness * GetCustomTexcoordValue(_TintSharpnessCoord, customCoord1, customCoord2);
    half rim = 1.0f - abs(dot(worldNormal, worldViewDir));
    rim = GetRimValue(rim, rimProgress, rimSharpness, _TintInverseValue);
    
    //TintColor用のリム反映処理
    half isApplyRim = saturate(_TintAreaStatus);
    //tintBlendRate = lerp(NotApplyRim, ApplyRim, isApplyRim);
    half tintBlendRate = lerp(_TintBlendRate , _TintBlendRate * rim, isApplyRim);
    
    //グレースケールがベースである認識なので赤色成分を取得している
    //(3カラーの平均化処理を飛ばしているので通常色のマップを利用する場合は注意が必要)
    half isOverThreshold = step(_TintInverseGradientThreshold, color.r - COMMON_EPSILON); 
    half threshold = saturate(isOverThreshold + isOverThreshold * color.r);
    half4 blendColor = lerp(tintColor, color, threshold * _IsUseInverseGradientThreshold);
    
    //ベースのテクスチャのアルファ値を優先する
    blendColor.a = color.a;
    
    //TintColorの適応範囲の反転処理
    //applyInverseColor = lerp( NotInverseColor, InverseColor, isInverse);
    half4 applyInverseColor = lerp(color * lerp(half4(1,1,1,1), blendColor, tintBlendRate), lerp(color, blendColor, tintBlendRate), _TintInverseValue);
    
    //最終出力
    // lerp(defaultColor, applyInverseColor, isApplyTint)
    return lerp(color, applyInverseColor, isApplyTintColor);
}

fragmentBuffer TintColorVertexShader (appdata_t v)
{
    fragmentBuffer o;
    o.vertex = GallopObjectToClipPos(v.vertex);
    o.texcoord = v.texcoord;
    o.color = v.color * _Color;
    o.worldNormal = TransformObjectToWorldNormal(v.normal,true);
    o.worldViewDir = GetWorldSpaceViewDir(TransformObjectToWorld(v.vertex.xyz));
    o.customTexCoord1 = v.customTexCoord1;
    o.customTexCoord2 = v.customTexCoord2;
    
    #ifdef UNITY_UI_CLIP_RECT
    o.worldPosition = v.vertex;
    #endif
    
    return o;
}

fixed4 TintColorFragmentShader (fragmentBuffer IN) : COLOR
{
    half4 col = tex2D(_MainTex, IN.texcoord) * IN.color;
    col = GetTintColor(col,IN.texcoord, IN.worldNormal, IN.worldViewDir, IN.customTexCoord1, IN.customTexCoord2);
    
    #ifdef UNITY_UI_CLIP_RECT
    col.a *= UnityGet2DClipping(IN.worldPosition.xy, _ClipRect);
    #endif
                    
    #ifdef UNITY_UI_ALPHACLIP
    clip (col.a - 0.001);
    #endif
    
    return col;
}

#endif