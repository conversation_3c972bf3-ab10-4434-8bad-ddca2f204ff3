#ifndef _STAGEPROJECTOR_ALPHA_HLSLINC_
#define _STAGEPROJECTOR_ALPHA_HLSLINC_

#include "../../../Common/ShaderCommon.hlsl"

CBUFFER_START(UnityPerMaterial)

//Projectorの場合Unityから渡される
fixed4  _ProjectorMulColor0;        // 乗算する色
half    _ProjectorColorPower;    // 色の強さ

TEXTURE2D_SAMPLER(_LightTex);

#ifdef ENABLE_ANIMATION
TEXTURE2D_SAMPLER(_AnimationTex);
uniform fixed4 _UVAdjust;
#endif

CBUFFER_END

struct appdata
{
    float4 vertex : POSITION;
    fixed4 color : COLOR;
};

struct v2f
{
    float4 pos : SV_POSITION;
    float4 posProj : TEXCOORD0;
    fixed4 color : TEXCOORD1;
};

v2f vert(appdata i)
{
    v2f v;

    v.pos = GallopObjectToClipPos(i.vertex);
    v.posProj = GetProjectorPosition(i.vertex);

    v.color.rgb = _ProjectorMulColor0.rgb * _ProjectorColorPower;
    v.color.a = i.color.a;
    return v;
}

fixed4 frag(v2f i) : COLOR
{
    fixed4 color;
    if (i.posProj.w > 0.0)
    {
        half2 uv = i.posProj.xy / i.posProj.w;
        color = TEX2D_SAMPLE(_LightTex, uv);
        color = color * i.color;

#ifdef ENABLE_ANIMATION
        fixed rate = step(0.0, uv.x);
        rate *= 1.0 - step(1.0, uv.x);
        rate *= step(0.0, uv.y);
        rate *= 1.0 - step(1.0, uv.y);

        uv *= _UVAdjust.xy;
        uv += _UVAdjust.zw;

        fixed4 animeColor = TEX2D_SAMPLE(_AnimationTex, uv) * rate;
        color *= animeColor;
#endif

        color *= i.color.a;
        color.a = 1.0;
    }
    else
    {
        color = fixed4(0, 0, 0, 0);
    }
    return color;
}

#endif