// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

#include "../../../Common/ShaderCommon.hlsl"

struct appdata
{
    float4 vertex : POSITION;
    float3 normal : NORMAL;
#ifdef    ENABLE_VERTEXCOLOR
    fixed4    color            :    COLOR;
#endif
    float2 uv : TEXCOORD0;
};

struct v2f
{
    float2 uv : TEXCOORD0;
    float3 normal : TEXCOORD1;
//    頂点カラーを使う
#if    defined(ENABLE_VERTEXCOLOR) || defined(ENABLE_MULCOLOR)
    half4    color        :    TEXCOORD2;
#endif
    float4 vertex : SV_POSITION;
#ifdef ENABLE_SUNCATCHER
    float4 fresnel : TEXCOORD3;
#endif
};

#if defined(ENABLE_AMBIENTCOLOR)
#if defined(ENABLE_GALLOP_AMBIENTCOLOR)
    uniform        fixed4    _Global_LightmapShadowColor;
#else
    //    アンビエントカラーを持つ
    uniform        half3    _AmbientColor;
#endif
#endif

CBUFFER_START(UnityPerMaterial)

TEXTURE2D_SAMPLER_TO(_MainTex);
TEXTURE2D_SAMPLER(_DiscoTex);
uniform float _Bias;

#if defined(ENABLE_MULCOLOR)
uniform float4 _MulColor0;
#endif

#ifdef ENABLE_SUNCATCHER
uniform float4 _LightColor;
uniform float _Fresnel; // 正面から見た時の反射率
uniform float _FresnelThreshold; // フレネス反射を適用する閾値
uniform float _PrismThresholdMin; // プリズム表現を適用する最小閾値
uniform float _PrismThresholdMax; // プリズム表現を適用する最大閾値
uniform float _PrismRate;
uniform float _LightRate;
uniform float _AppTime;
#endif

CBUFFER_END

//-----------------------------------------------------
//    ステージのカラーの計算を行う
//-----------------------------------------------------
inline    half4    GetStageColor( fixed4 srcColor )
{
    half4    dstColor;
    
//    カラーの設定
#if    defined(ENABLE_VERTEXCOLOR)

    #if    defined(ENABLE_AMBIENTCOLOR)
        #if defined(ENABLE_GALLOP_AMBIENTCOLOR)
            dstColor.rgb = srcColor.rgb * _Global_LightmapShadowColor.rgb;
        #else
            dstColor.rgb = srcColor.rgb * _AmbientColor;
        #endif
        dstColor.a = srcColor.a;
    #else
        dstColor = srcColor;
    #endif

    #if    defined(ENABLE_MULCOLOR)
        dstColor *= _MulColor0;
    #endif
#else
    #if    defined(ENABLE_MULCOLOR)
        dstColor = _MulColor0;
    #endif
#endif
    return    dstColor;
}

inline float3x3 QuaternionToMatrix(float3 axis, float thita)
{
    float3 a = normalize(axis);
    thita *= 3.14159265;
    float s = sin(thita);
    float c = cos(thita);
    float r = 1.0 - c;
    return float3x3(
        a.x * a.x * r + c, a.y * a.x * r + a.z * s, a.z * a.x * r - a.y * s,
        a.x * a.y * r - a.z * s, a.y * a.y * r + c, a.z * a.y * r + a.x * s,
        a.x * a.z * r + a.y * s, a.y * a.z * r - a.x * s, a.z * a.z * r + c
    );
}

v2f vert (appdata v)
{
    v2f o;
    o.vertex = GallopObjectToClipPos(v.vertex);
    o.normal = normalize(mul(UNITY_MATRIX_MV, fixed4(v.normal,0))).xyz;
    o.uv = TRANSFORM_TEX(v.uv, _MainTex);
#if defined(ENABLE_VERTEXCOLOR) || defined(ENABLE_MULCOLOR)
    o.color = GetStageColor(v.color);
#endif

#ifdef ENABLE_SUNCATCHER
    float3 objEyeVector = WorldSpaceViewDir(v.vertex);
    float3 objEyeVectorNormalize = normalize(objEyeVector);
    float4 worldNormal = mul(unity_ObjectToWorld, float4(v.normal, 0));
    float3 normalObject = normalize(worldNormal.xyz);

    // フレネル反射率を算出する。
    float power = dot(objEyeVectorNormalize, normalObject);
    o.fresnel.a = _Fresnel + (1.0 - _Fresnel) * pow(1.0 - power, 5);

    float3 axis = float3(random(objEyeVector.yz), random(objEyeVector.xz), random(objEyeVector.xy));
    o.fresnel.xyz = mul(QuaternionToMatrix(axis, _AppTime), normalObject);
#endif

    return o;
}

fixed4 frag (v2f i) : SV_Target
{
    fixed4 col = TEX2D_SAMPLE(_MainTex, i.uv);
    // 映り込みテクスチャを合成する。
    col.rgb *= TEX2D_SAMPLE(_DiscoTex, (i.normal.xy + 1)*0.5).rgb * _Bias;
#if defined(ENABLE_VERTEXCOLOR) || defined(ENABLE_MULCOLOR)
    col *= i.color;
#endif

#ifdef ENABLE_SUNCATCHER
    float fresnel = i.fresnel.a;
    float3 baseColor = col.rgb;
    float3 prismColor = col.rgb * (1.0 - _PrismRate) + i.fresnel.rgb * _PrismRate;
    float3 lightColor = col.rgb * (1.0 - _LightRate) + _LightColor.rgb * _LightRate;

    // フレネル反射率によって色を変化させる。
    //                  0 ~ _PrismThresholdMin = baseColor
    // _PrismThresholdMin ~ _PrismThresholdMax = baseColor ~ prismColor
    // _PrismThresholdMax ~ _FresnelThreshold  = prismColor ~ lightColor
    // _FresnelThreshold  ~ 1                  = lightColor

    float baseToPrismRate = (fresnel - _PrismThresholdMin) / (_PrismThresholdMax - _PrismThresholdMin);
    float prismToLightRate = (fresnel - _PrismThresholdMax) / (_FresnelThreshold - _PrismThresholdMax);

    col.rgb = lerp(baseColor,
                   lerp(lerp(baseColor, prismColor, baseToPrismRate),
                        lerp(lerp(prismColor, lightColor, prismToLightRate),
                             lightColor,
                             step(_FresnelThreshold, fresnel)),
                        step(_PrismThresholdMax, fresnel)),
                   step(_PrismThresholdMin, fresnel));
#endif

    return col;
}
