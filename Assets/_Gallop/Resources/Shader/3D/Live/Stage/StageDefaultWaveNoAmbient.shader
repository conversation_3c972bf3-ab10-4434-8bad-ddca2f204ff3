Shader "Gallop/3D/Live/Stage/DefaultWaveNoAmbient"
{
    Properties 
    {
        _MainTex ("Diffuse Texture", 2D) = "white" {}
        _MulColor0("Mul Color 0", Color) = (1.0, 1.0, 1.0, 1.0)
        _ColorPower("Color Power", Float) = 1.0

        _WaveDir ("Wave Dir", Vector) = (1, 0, 0, 0)
        _WaveFreq ("Wave Freq", Range(0, 30)) = 0
        _WaveSpeed ("Wave Speed", Range(0, 30)) = 0
        _WaveSize ("Wave Size", Range(0, 30)) = 0
        _IsWorldWave ("Is World Wave", Range(0, 1)) = 0
        _IsWaveVertexPower ("Is Wave Vertex Power", Range(0, 1)) = 1 // 頂点カラーのアルファ値で揺れの強弱を調整するかどうか。
        _IsResetWaveVertexAlpha ("Is Reset Wave Vertex Alpha", Range(0, 1)) = 0 // 揺れの強弱を調整後、頂点カラーのアルファ値を1にするかどうか。
        _IsWaveDirNormal ("Is Wave Dir Normal", Range(0, 1)) = 0 // 揺れの向きを法線の向きにするかどうか。
        _IsWaveHorizontal ("Is Wave Horizontal", Range(0, 1)) = 0 // 揺れを水平方向で計算するかどうか。

        // カリングモード
        [Header(Culling)]
        [Enum(UnityEngine.Rendering.CullMode)] _CullMode("Cull Mode", Float) = 2// Back
        
        [HideInInspector] _AppTime("AppTime", float) = 0
    }

    SubShader 
    {
        Tags
        {
            "Queue" = "Geometry"
            "RenderType" = "Opaque"
            "Mirror" = "Stage"
        }
        Lighting Off
        ZWrite On

        // Main pass
        Pass 
        {
            Cull [_CullMode]
            Blend One Zero, Zero One

            HLSLPROGRAM
            #pragma target         3.0
            #pragma vertex         UIVS
            #pragma fragment       UIPS

            //#define    ENABLE_AMBIENTCOLOR    //    アンビエントカラーを有効にする
            #define ENABLE_MULCOLOR_ALPHA
            #define ENABLE_COLORPOWER
            #define ENABLE_VERTEXCOLOR
            #define USE_VERTEXWAVE
            #define USE_VERTEXWAVE_HORIZONTAL // 水中の揺らぎ表現用に水平方向に振幅する

            #include "../hlsl/StageCommon.hlsl"
            ENDHLSL
        }

        // Main Pass Shadow Caster
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }
        
            Fog
            {
                Mode Off
            }
            ZWrite On
            ZTest LEqual
            Cull Back
            Offset 1, 1


            HLSLPROGRAM
            #pragma vertex VSMain
            #pragma fragment PSMain

            #define SHADOWS_DEPTH


            #include "../hlsl/DepthCaster.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "SimpleMirrorCaster"
            Tags{ "LightMode" = "SimpleMirrorCaster" }

            Cull Back
            Blend One Zero, Zero One
            Offset 0, 0

            HLSLPROGRAM

            #pragma target         3.0
            #pragma vertex         UIVS
            #pragma fragment       UIPS
            #pragma fragmentoption ARB_precision_hint_fastest

            #define PASS PASS_SIMPLE_MIRROR_CASTER

            #define ENABLE_MULCOLOR_ALPHA
            #define ENABLE_COLORPOWER
            #define ENABLE_VERTEXCOLOR
            #define USE_VERTEXWAVE
            #define USE_VERTEXWAVE_HORIZONTAL // 水中の揺らぎ表現用に水平方向に振幅する

            #include "../hlsl/StageCommon.hlsl"

            ENDHLSL
        }
    }

    FallBack Off
}