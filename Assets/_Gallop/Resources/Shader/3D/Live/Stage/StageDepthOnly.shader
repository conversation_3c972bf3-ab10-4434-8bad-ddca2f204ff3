Shader "Gallop/3D/Live/Stage/StageDepthOnly"
{
    SubShader
    {
        Tags { "Queue" = "Geometry" "RenderType" = "Opaque" }
        Lighting Off
        ZWrite On

        // Main Pass Shadow Caster
        Pass
        {
            Name "ShadowCaster"
            Tags { "LightMode" = "ShadowCaster" }

            Fog { Mode Off }
            ZWrite On
            ZTest LEqual
            Offset 1, 1

            HLSLPROGRAM
            #pragma vertex VSMain
            #pragma fragment PSMain
            #define SHADOWS_DEPTH
            #pragma fragmentoption ARB_precision_hint_fastest

            #include "../hlsl/DepthCaster.hlsl"
            ENDHLSL
        }
    }
}
