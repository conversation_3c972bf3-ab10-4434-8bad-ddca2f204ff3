Shader "Gallop/3D/Live/Stage/DefaultTransparentNoAmbient"
{
    Properties
    {
        _MainTex("Diffuse Texture", 2D) = "white" {}
        _MulColor0("Mul Color 0", Color) = (1.0, 1.0, 1.0, 1.0)
        _ColorPower("Color Power", Float) = 1.0
        [HideInInspector]_AmbientColor("Ambient Color", Color) = (1.0, 1.0, 1.0, 1.0)
    }

    SubShader
    {
        Tags
        {
            "Queue" = "Transparent"
            "RenderType" = "Transparent"
        }

        Lighting Off
        ZWrite Off

        // Main pass
        Pass
        {
            Tags{ "LightMode" = "UniversalForward" }

            Cull Back
            ColorMask RGBA
            Blend SrcAlpha OneMinusSrcAlpha

            HLSLPROGRAM
            #pragma target         3.0
            #pragma vertex         UIVS
            #pragma fragment       UIPS

            #define PASS PASS_MAIN_COLOR
#define PASS_OPTION_DEFINE_ENABLE_AMBIENTCOLOR

            //#define ENABLE_AMBIENTCOLOR // アンビエントカラーを有効にする。
            #define ENABLE_VERTEXCOLOR // 頂点カラーを有効にする。
            #define ENABLE_MULCOLOR // 乗算カラーを有効にする。
            #define ENABLE_COLORPOWER // 色の強さを有効にする。

            #include "../hlsl/StageCommon.hlsl"
            ENDHLSL
        }

        // Main Pass Shadow Caster
        Pass
        {
            Name "ShadowCaster"
            
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            Fog
            {
                Mode Off
            }

            ZWrite On
            ZTest LEqual
            Cull Back
            Offset 1, 1

            HLSLPROGRAM
            #pragma vertex VSMain
            #pragma fragment PSMain

            #define SHADOWS_DEPTH

            #include "../hlsl/DepthCaster.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "SimpleMirrorCaster"
            Tags{ "LightMode" = "SimpleMirrorCaster" }

            Cull Back
            ColorMask RGBA
            Blend SrcAlpha OneMinusSrcAlpha

            HLSLPROGRAM
            #pragma target         3.0
            #pragma vertex         UIVS
            #pragma fragment       UIPS
            #pragma fragmentoption ARB_precision_hint_fastest

#define PASS PASS_SIMPLE_MIRROR_CASTER
#define PASS_OPTION_MIRROR_TRANSPARENT

#define PASS_OPTION_DEFINE_ENABLE_MULCOLOR
#define PASS_OPTION_DEFINE_ENABLE_COLORPOWER

            #define ENABLE_AMBIENTCOLOR // アンビエントカラーを有効にする。
            #define ENABLE_VERTEXCOLOR // 頂点カラーを有効にする。

            #include "../hlsl/StageCommon.hlsl"

            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
    FallBack Off
}