Shader "Gallop/3D/Bg/LightmapCutoffFogFill"
{
    Properties{
        _MainTex("_MainTex", 2D) = "white" {}
        _LightTex("Lightmap", 2D) = "gray" {}
        _Cutoff("_Cutoff", Range(0,1)) = 0.98
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
        _MulColor0("Fill Color", Color) =  (1, 1, 1, 1)
        
        [HideInInspector] _MultimapRate("Multimap Rate",float) = 0
    }

    SubShader
    {
        Tags{ "RenderType" = "TransparentCutout" "Queue" = "AlphaTest" "ForceNoShadowCasting" = "True" }
        
        Cull [_Cull]
        Lighting Off
        ZWrite On
        Offset -1,-1

        Pass
        {
            Tags{ "LightMode" = "UniversalForward" }

            ColorMask RGBA
            Blend SrcAlpha OneMinusSrcAlpha

            Stencil
            {
                Ref 0
                Comp Always
                Pass Replace
            }

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest

#define USE_LIGHTMAP
#define USE_FOG
#define USE_SUBCOLOR
#define USE_CUTOFF
#define USE_LIGHTMAPDENSITY
#define USE_FILLCOLOR

#include "hlsl/GallopBgMain.hlsl"

            ENDHLSL
        }
    }
}
