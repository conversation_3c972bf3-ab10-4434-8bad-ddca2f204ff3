Shader "Gallop/3D/Bg/LightmapColorAlphaFogGrass"
{
    Properties{
        _MainTex("_MainTex", 2D) = "white" {}
        _MulColor0("_MulColor0", Color) = (1, 1, 1, 1)
        _LightTex("Lightmap", 2D) = "gray" {}
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
        _WindDirTex("_WindDirTex",2D) = "gray" {}
        _WindScale("_WindScale",float) = 1
        _WindParam("_WindParam",Vector) = (0,0,0,0)
    }


    SubShader
    {
        Tags{ "RenderType" = "Transparent" "Queue" = "Transparent" "ForceNoShadowCasting" = "True" "Mirror" = "Stage" }
        LOD 100

        Cull [_Cull]
        Lighting Off
        ZWrite On
        Offset -1,-1

        Pass
        {
            ColorMask RGBA
            Blend SrcAlpha OneMinusSrcAlpha

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest

#define USE_LIGHTMAP
#define USE_FILLCOLOR
#define USE_FOG
#define USE_LIGHTMAPCOLOR
#define USE_LIGHTMAPDENSITY
#define USE_ALPHACLIPING
#define USE_GRASS
#include "hlsl/GallopBgMain.hlsl"

            ENDHLSL
        }
    }
}
