Shader "Gallop/3D/Bg/LampScrollFog"
{
    Properties
    {
        _MainTex ("_MainTex", 2D) = "black" {}

        // UVスクロールエミッシヴ
        [Header(UVEmissive)]
        _UVEmissiveTexBase("_UVEmissiveTexBase",2D) = "gray" {}
        _UVEmissiveTex("_UVEmissiveTex",2D) = "black" {}
        _UVEmissiveMaskTex("_UVEmissiveMaskTex",2D) = "black" {}
        _UVEmissiveScroll("_UVEmissiveScroll",Vector) = (0,0,0,0)
        _UVEmissivePower("_UVEmissivePower",Range(0,100)) = 1.0
        _EmissiveColor("_EmissiveColor", Color) = (1,1,1,1)

        _LampOnOffValue("_LampOnOffValue",Range(0,1)) = 0
        
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        Cull [_Cull]
        Lighting Off
        LOD 100

        Pass
        {
            Stencil
            {
                Ref 0
                Comp Always
                Pass Replace
            }

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest

#define USE_UVSCROLL_EMISSIVE
#define USE_VERTEXCOLOR
#define USE_LIGHTMAPDENSITY
#define USE_FOG

#include "../../Common/ShaderCommon.hlsl"
#include "../../Common/FogCommon.hlsl"

            struct appdata
            {
                float3 vertex : POSITION;
                float2 uv : TEXCOORD0;
#if defined(USE_VERTEXCOLOR)
                float4 color : COLOR;
#endif
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
#if defined(USE_VERTEXCOLOR)
                float4 color : TEXCOORD1;
#endif
#if defined(USE_FOG)
                float2 fogParam : TEXCOORD2;
#endif
            };

            CBUFFER_START(UnityPerMaterial)

            TEXTURE2D_SAMPLER_TO(_MainTex);

#if defined(USE_UVSCROLL_EMISSIVE)
            TEXTURE2D_SAMPLER(_UVEmissiveTexBase);
            TEXTURE2D_SAMPLER(_UVEmissiveTex);
            TEXTURE2D_SAMPLER(_UVEmissiveMaskTex);
            uniform float4 _UVEmissiveScroll;
            uniform float _UVEmissivePower;
            uniform fixed4 _EmissiveColor;
            uniform float _LampOnOffValue;
#endif

            CBUFFER_END

#if defined(USE_LIGHTMAPDENSITY)
            float4 _Global_LightmapDensityAddColor;
            float4 _Global_LightmapModulateColor;
#endif


            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = GallopObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
#if defined(USE_VERTEXCOLOR)
                o.color = v.color;
#endif
#if defined(USE_FOG)
                o.fogParam = CalcFogParamFromVertex(float4(v.vertex,1.0));
#endif
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 originColor = TEX2D_SAMPLE(_MainTex, i.uv);

#if defined(USE_LIGHTMAPDENSITY)
                originColor.rgb *= _Global_LightmapModulateColor.rgb + _Global_LightmapDensityAddColor.rgb;
#endif

#if defined(USE_FOG)
                originColor.rgb = ApplyFog(i.fogParam.x, i.fogParam.y, originColor).rgb;
#endif

#if defined(USE_UVSCROLL_EMISSIVE)
                half emissivePower = lerp(1.0, _UVEmissivePower, _LampOnOffValue);
                half emissiveMask = TEX2D_SAMPLE(_UVEmissiveMaskTex, i.uv).r * emissivePower;
                half2 uvEmissiveUV = i.uv + _UVEmissiveScroll.xy;
                half4 uvEmissiveColor = TEX2D_SAMPLE(_UVEmissiveTex, uvEmissiveUV);
                half4 uvEmissiveColorBase = TEX2D_SAMPLE(_UVEmissiveTexBase, uvEmissiveUV);
                uvEmissiveColor.rgb = lerp(uvEmissiveColorBase.rgb, uvEmissiveColor.rgb, _LampOnOffValue);
                originColor.rgb = originColor.rgb + (uvEmissiveColor.rgb * _EmissiveColor.rgb) * emissiveMask;
#endif

#if defined(USE_VERTEXCOLOR)
                // 最終的な陰影づけを頂点カラーでここで行う
                originColor *= i.color;
#endif
                return originColor;
            }
            ENDHLSL
        }

        Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            float4 vert(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 frag() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
}
