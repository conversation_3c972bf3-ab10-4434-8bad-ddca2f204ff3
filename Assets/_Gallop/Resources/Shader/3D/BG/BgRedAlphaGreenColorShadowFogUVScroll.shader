Shader "Gallop/3D/Bg/RedAlphaGreenColorShadowFogUVScroll"
{
    //メッシュの上から影のようにかぶせて使用するUVシェーダーです(育成の雲影に使用)

    Properties
    {
        _MainTex("Texture", 2D) = "white" {}
        _SubTex("Texture", 2D) = "white" {}
        _ScrollSpeedUV("_ScrollSpeedUV",Vector) = (1,1,1,1)
        
        // 基本的に外部から設定はせず、CutoffProjector用で下記判定に使用する
        // ・シェーダそのものがCutoffに対応しているか
        // ・対応しているならどんなCutoff方法に対応しているか
        [HideInInspector][Enum(Gallop.RenderPipeline.CutoffType)] _CutoffType("_SupportCutoffType", Int) = 5 // AlphaBlendSrcTexR
        
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2

        _StencilComp("Stencil Comparison", Float) = 8
        _StencilMask("Stencil ID", Float) = 0
        _StencilOp("Stencil Operation", Float) = 2
        [HideInInspector] _Color("_Color",Vector) = (0,0,0,0)
    }

    SubShader
    {
        Tags{ "RenderType" = "Transparent" "Queue" = "Transparent" "ForceNoShadowCasting" = "True" }
        LOD 100

        Blend SrcAlpha OneMinusSrcAlpha
        Cull [_Cull]
        Lighting Off
        ZWrite Off
        Offset -1,-1

        Pass
        {
            Tags{ "LightMode" = "UniversalForward" }
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
                Pass[_StencilOp]
            }

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest
//テクスチャのRはUVテクスチャアルファチャンネル、2つめをカラーチャンネル、αチャンネルにはTransParentでα抜きしているメッシュなどにUVを上から適用したくない場合などに使用
#define USE_R_ALPHA
#define USE_G_COLOR
#define USE_FOG
#define USE_UVSCROLL
#define USE_UVSCROLL_ALPHA

#include "hlsl/GallopBgMain.hlsl"
        ENDHLSL
        }
    }
}
