Shader "Gallop/3D/Bg/LightmapColorNoSub"
{
    Properties
    {
        _MainTex("_MainTex", 2D) = "white" {}
        _LightTex("Lightmap", 2D) = "gray" {}
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
    }

    SubShader
    {
        Tags {"Queue" = "Geometry" "RenderType" = "Opaque"}
        Cull [_Cull]
        LOD 100

        Pass
        {
            Stencil
            {
                Ref 0
                Comp Always
                Pass Replace
            }

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest

#define USE_LIGHTMAP
#define USE_LIGHTMAPCOLOR
#define USE_LIGHTMAPDENSITY
#include "hlsl/GallopBgMain.hlsl"

            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags{ "LightMode" = "ShadowCaster" }

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag

#define PASS PASS_SHADOW_CASTER
//デプス収集やShadowmap生成時にはAndroid固有のデプス補正処理は行わない
#define DISABLE_LOG_DEPTH

#include "hlsl/GallopBgMain.hlsl"

            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
}