Shader "Gallop/3D/Bg/Special/SeaNoRefl"
{
    Properties{
        [NoScaleOffset] _Fresnel("Fresnel (A) ", 2D) = "gray" {}
        [NoScaleOffset] _BumpMap("Normalmap ", 2D) = "bump" {}
        [NoScaleOffset] _WaterSurface("WaterSurface", 2D) = "black" {}

        _WaveHeight("▲ 頂点波の高さ", Range(0,0.4) ) = 0.05
        _WaveScale("▲ 頂点波の幅", Range(0.02,0.15)) = 0.063
         WaveSpeed("♋Wave speed (map1 x,y; map2 x,y)", Vector) = (19,9,-16,-7)

        _ReflDistort("● 反射歪率", Range(0,1.5)) = 0.44
        _RefrDistort("● 屈折歪率", Range(0,1.5)) = 0.40
        _ReflVColor("● 反射垂直色", COLOR) = (.34, .85, .92, 1)
        _ReflHColor("● 反射水平色", COLOR) = (.34, .85, .92, 1)
        _RefrColor("● 屈折に乗算する色", COLOR) = (.34, .85, .92, 1)
        _ReflectionDisableDistance("● 反射無効の距離", Range(0,5)) = 0.44
        _ReflectionDisablePow("● 反射無効の乗数", Range(0,10)) = 0.44
        _WaterSurfaceScale("◆ 斑点模様の幅", Range(1.0, 10.0)) = 2.0
        _WaterSurfaceBrightness("◆ 斑点模様の加算率", Range(0.0,1.0) ) = 0.0
        _WaterSurfaceDistortion("◆ 斑点模様の歪み率", Range(0.0, 1.0)) = 0.1
        _WaterSurfaceMoveSpeed("◆ 斑点模様の動きの速度", Range(0.0, 1.0)) = 0.1
        _WaterSurfaceMoveSize("◆ 斑点模様の動きの幅", Range(0.0, 1.0)) = 0.1

        _AlphaBaseColorDistance("αカラー距離", Range(0.0, 1.0)) = 0.1
        _AlphaBaseColorDistanceMul("αカラー距離Mul", Range(1.0, 10.0)) = 1.0

        [HideInInspector] _ReflectionTex("Internal Reflection", 2D) = "" {}
        [HideInInspector] _RefractionTex("Internal Refraction", 2D) = "" {}
        [HideInInspector] _ReflectionBias("Reflection Bias", Range(0.0, 1.0)) = 0.0
        [HideInInspector] _BumpSpecular("Bump Specular", Range(0.0, 5.0)) = 0.3

        [HideInInspector] _WaveScale4("_WaveScale4", Vector) = (0,0,0,0)
        [HideInInspector] _WaveOffset("_WaveOffset", Vector) = (0,0,0,0)
        [HideInInspector] _AppTime("AppTime", float) = 0
    }


    // -----------------------------------------------------------
    // Fragment program cards


    Subshader
    {
        Tags{ "WaterMode" = "Refractive" "RenderType" = "Opaque" }
        Cull Off 

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile WATER_INSIDE __
            #ifdef WATER_INSIDE
            #define HAS_REFLECTION_TEX 0
            #else
            #define HAS_REFLECTION_TEX 0
            #endif

            #include "../../Common/ShaderCommon.hlsl"

            CBUFFER_START(UnityPerMaterial)
            uniform float4 _WaveScale4;
            uniform float4 _WaveOffset; 

            uniform float _ReflDistort;
            uniform float _RefrDistort;
            uniform float _ReflectionBias;
            uniform float _BumpSpecular;
            uniform float _WaveHeight;
            uniform float _WaterSurfaceScale;
            uniform float _WaterSurfaceDistortion;
            uniform float _WaterSurfaceMoveSpeed;
            uniform float _WaterSurfaceMoveSize;
            uniform float _AlphaBaseColorDistance;
            uniform float _AlphaBaseColorDistanceMul;
            uniform float4 _ReflVColor;
            uniform float4 _ReflHColor;

        #if HAS_REFLECTION_TEX
            TEXTURE2D_SAMPLER(_ReflectionTex);
        #endif
            float _WaterSurfaceBrightness;
            TEXTURE2D_SAMPLER(_Fresnel);
            TEXTURE2D_SAMPLER(_RefractionTex);
            uniform float4 _RefrColor;
            TEXTURE2D_SAMPLER(_BumpMap);
            TEXTURE2D_SAMPLER(_WaterSurface);
            float _ReflectionDisableDistance;
            float _ReflectionDisablePow;
            float _AppTime;

            CBUFFER_END

            struct appdata {
                float3 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
                float4 color : COLOR;
            };

            struct v2f {
                float4 pos : SV_POSITION;
                float4 ref : TEXCOORD0;
                float2 bumpuv0 : TEXCOORD1;
                float2 bumpuv1 : TEXCOORD2;
                float4 viewDirDist : TEXCOORD3;
                float2 uv: TEXCOORD4;
                float2 surfuv : TEXCOORD5;
                float4 color : TEXCOORD6;
            };

            float4x4 MakeCameraProjector()
            {
                //CameraProjectorが外部から渡されることはない
                //またfloat4x4はPropertiesに記載出来ないので必要な場合は
                //float4 * 4個を渡してシェーダー側で生成する事
                return float4x4(1, 0, 0, 0,
                    0, 1, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1);
            }

            v2f vert(appdata v)
            {
                v2f o;
                half3 p = v.vertex;
                
                float timeX = _AppTime * 0.05;  // _Time.xの代用
                half t = (-timeX * 6 + v.uv.y + v.uv.x)*3.141592654 * 6;

                float3 wpos = TransformObjectToWorld(v.vertex);

                p.y += sin(t) * _WaveHeight;
                o.pos = GallopObjectToClipPos(p);

                // scroll bump waves
                float4 temp;
                temp.xyzw = wpos.xzxz * _WaveScale4 + _WaveOffset;
                o.bumpuv0 = temp.xy;
                o.bumpuv1 = temp.wz;

                float4 projectedXYZW = mul(MakeCameraProjector(), float4(wpos,1));
                o.uv.x = (projectedXYZW.x / projectedXYZW.w) / 2.0f + 0.5f;
                o.uv.y = (projectedXYZW.y / projectedXYZW.w) / 2.0f + 0.5f;
        //        if (0 > projectedXYZW.z)
        //            o.uv = float2(-1, -1);

                // object space view direction (will normalize per pixel)
                o.viewDirDist.xzy = WorldSpaceViewDir(v.vertex);
                o.viewDirDist.w = length( wpos - _WorldSpaceCameraPos ); //カメラからの距離

                o.surfuv = v.uv * 50 / _WaterSurfaceScale;
                float t2 = (-timeX * _WaterSurfaceMoveSpeed * 1.2  + o.uv.x*1.2 * _WaterSurfaceMoveSize )*20;
                float t3 = (-timeX * _WaterSurfaceMoveSpeed * 1.7 + o.uv.y*1.8 * _WaterSurfaceMoveSize )*20;
                o.surfuv.x += sin( t2 * 1.2 ) * _WaterSurfaceMoveSize * 0.1;
                o.surfuv.y += sin( t3 * 1.6 ) * _WaterSurfaceMoveSize * 0.1;

                o.ref = ComputeScreenPos(o.pos);

                o.color = v.color;

                return o;
            }

            half4 frag(v2f i) : SV_Target
            {
                float3 viewDir = i.viewDirDist.xyz;
                float cameraDistance = i.viewDirDist.w;
                viewDir = normalize(viewDir);

                // combine two scrolling bumpmaps into one
                half3 bump1 = UnpackNormal(TEX2D_SAMPLE(_BumpMap, i.bumpuv0)).rgb;
                half3 bump2 = UnpackNormal(TEX2D_SAMPLE(_BumpMap, i.bumpuv1)).rgb;
                half3 bump = (bump1 + bump2) * 0.5;
                bump = normalize(bump);

                // fresnel factor
                half fresnelFac = dot(viewDir, bump);

                half fresnelFac2 = pow( 1.0 - abs(dot(viewDir, half3( 0.0, 0.0, 1.0 ) )), 3.0 );

                float4 uv1 = i.ref;
                uv1.xy += bump.xy * _ReflDistort;
            #if HAS_REFLECTION_TEX
                half4 refl = TEX2D_PROJ(_ReflectionTex, uv1);
            #else
            //    half4 refl = _RefrColor;
                half4 refl = half4(1,1,1,1);
            #endif
                float4 uv2 = i.ref; uv2.xy -= bump.xy * _RefrDistort;
                half4 refr = TEX2D_PROJ(_RefractionTex, uv2);
            #ifndef WATER_INSIDE
                refr *= lerp( float4(1,1,1,1), _RefrColor, i.color.a );
            #endif

                // 水中を映す
                half fresnel = TEX2D_SAMPLE(_Fresnel, float2(fresnelFac,fresnelFac)).a;

                float4 reflectColor;
                reflectColor = refl;

                half4 color = refr;                                // プール内部

                float4 watersurface = TEX2D_SAMPLE( _WaterSurface, i.surfuv + bump.xy * _WaterSurfaceDistortion );

                // 演出的に、カメラに近いところは反射を抑える
                float fresnelFac3 = fresnelFac2 - (pow(max(0,1.0/cameraDistance), _ReflectionDisablePow ) * _ReflectionDisableDistance);

                fresnelFac3 = clamp( (_ReflectionBias) + fresnelFac3, 0, 1 );
                color = lerp( color, reflectColor, fresnelFac3 );

            //#if !HAS_REFLECTION_TEX
                color = lerp( _ReflVColor, _ReflHColor, fresnelFac2 );    // 合わせのカラー
            //#endif

                float4 bumpSpecular = ( pow(fresnel, 4)*_BumpSpecular + _WaterSurfaceBrightness );
                color.rgb += bumpSpecular.rgb;                        // 白い反射

                float a0 = pow( saturate( i.color.a * (1-_AlphaBaseColorDistance)/(_AlphaBaseColorDistance) ), 1/_AlphaBaseColorDistanceMul );

                color = lerp( refr, color, a0 );
                return color;
            }
            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
}
