//UNITY_SHADER_NO_UPGRADE
#include "GallopBgCommon.cginc"

#define LIGHTMAP_BIAS (4)//Maya上で生成されたLightMapは0.5が基準値となっているため、２倍する必要がある。テクスチャと頂点カラーぶんがあるので４倍となる。

uniform sampler2D _MainTex;
uniform float4 _MainTex_ST;

#if defined(USE_SUBCOLOR)
uniform sampler2D _SubTex;
uniform half _MultimapRate;
#endif

#if defined(USE_LIGHTMAP)
uniform sampler2D _LightTex;
#endif

#if defined(USE_UVSCROLL)
float4 _ScrollSpeedUV;
#endif

#if defined(USE_UVANIME)
//x:横枚数　y:縦枚数　z:表示番号
float4    _AnimeNo;
#endif

#if defined(USE_UVANIME)||defined(USE_LIGHTMAP)||defined(USE_WAVE)
uniform float _AppTime;
#define _CurrentTime _AppTime
#endif

#if defined(USE_WAVE)

#define SCALE_TIME (5)
#define COLOR_BIAS (2)    //頂点カラーが0.5基準となっているので2倍する必要がある
#define _WaveCurrentTime _AppTime

#endif

uniform float _Cutoff;

struct appdata
{
    float4 vertex : POSITION;
    float2 uv: TEXCOORD0;
#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)
    float2 uv2: TEXCOORD1;
#endif

#if defined(USE_WAVE)
    float4 normal : NORMAL;
#endif

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)||defined(USE_WAVE)
    float4 color : COLOR;
#endif
};

struct v2f
{
    float4 pos : SV_POSITION;
    float2 uv : TEXCOORD0;

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)||defined(USE_WAVE)
    float4 color:TEXCOORD1;
#endif

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)
    float2 uv2 : TEXCOORD2;
#endif

#if defined(USE_FOG)
    float2 fogParam : TEXCOORD3;
#endif

};

v2f vert(appdata v)
{
    v2f o;
    UNITY_INITIALIZE_OUTPUT(v2f, o);

    o.pos = GallopObjectToClipPos(v.vertex);
    o.uv = TRANSFORM_TEX(v.uv, _MainTex).xy;

#if defined(USE_UVANIME)
    float animeNo = _CurrentTime / _AnimeNo.z;
    //_AnimeNo.zから場所を求める
    int x_no = (int)fmod(animeNo, _AnimeNo.x);
    int y_no = -(int)(animeNo / _AnimeNo.x);

    fixed w = 1.0 / _AnimeNo.x;
    fixed h = 1.0 / _AnimeNo.y;

    o.uv.x = (o.uv.x * w) + (w * x_no);
    o.uv.y = ((o.uv.y - 1.0) * h) + (h * y_no);
#endif

#if defined(USE_UVSCROLL)
    o.uv = o.uv + (_ScrollSpeedUV.xy * _CurrentTime);
#endif

#if defined(USE_LIGHTMAP)
    o.uv2 = v.uv2;

#if defined(USE_UVSCROLL)
    o.uv2 = o.uv2 + (_ScrollSpeedUV.zw * _CurrentTime);
#endif

#endif

#if defined(USE_FOG)
    o.fogParam = CalcFogParamFromVertex(v.vertex);
#endif

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)
    o.color = v.color;
#endif

    return o;
}

fixed4 frag(v2f i) : COLOR
{
#if defined(USE_MIPMAPBIAS)
    float4 uv = float4(i.uv.x, i.uv.y, 0, -2);//mipmapかかりにくくする
    fixed4 originColor = tex2Dbias(_MainTex, uv);//tex2DlodはOpenGLES2.0だと使えない
#else
    fixed4 originColor = tex2D(_MainTex, i.uv);
#endif

#if defined(USE_VERTEXCOLOR)
    originColor *= i.color;
#endif
#if defined(USE_LIGHTMAP)
    //ライトマップ使用時、頂点カラーのaには半透明情報が入っているので、aだけ先に適応する（rgbにはライト強度の情報が入っている）
    originColor.a *= i.color.a;
#endif

#if defined(USE_CUTOFF)
    clip(originColor.a - 0.0001 - _Cutoff);
#endif

#if defined(USE_SUBCOLOR)
#if defined(USE_MIPMAPBIAS)
    fixed4 subColor = tex2Dbias(_SubTex, uv);
    originColor = lerp(originColor, subColor, _MultimapRate);
#else
    fixed4 subColor = tex2D(_SubTex, i.uv);
    originColor = lerp(originColor, subColor, _MultimapRate);
#endif
#endif

#if defined(USE_LIGHTMAP)
#if defined(USE_MIPMAPBIAS)
    float4 uv2 = float4(i.uv2.x, i.uv2.y, 0, -1);
    fixed4 lightmap = tex2Dbias(_LightTex, uv2);
#else
    fixed4 lightmap = tex2D(_LightTex, i.uv2);
#endif

    float4 l = lightmap;

    l.rgb *= i.color.rgb;
    l.rgb *= LIGHTMAP_BIAS;

#if defined(USE_LIGHTMAPCOLOR)
    l.rgb = lerp(_Global_LightmapShadowColor, _Global_LightmapColor, l.r);
#endif

#if defined(USE_LIGHTMAPDENSITY)
    l.rgb = (l.rgb * _Global_LightmapModulateColor) + _Global_LightmapDensityAddColor;
#endif

    originColor.rgb *= l.rgb;
#endif

#if defined(USE_R_ALPHA)
    originColor.a = originColor.r;
#endif

#if defined(USE_G_COLOR)
    originColor.rgb = originColor.ggg;
#endif

#if defined(USE_FOG)
    originColor.rgb = ApplyFog(i.fogParam.x, i.fogParam.y, originColor).rgb;
#endif

    return originColor;
}

#if defined(USE_WAVE)

//揺れシェーダ用の処理
v2f vertWave(appdata v)
{
    v2f o;

    //Wave
    float4 worldPos = mul(unity_ObjectToWorld, v.vertex);
    float4 pos = worldPos;

    fixed3 axis = v.normal;
    fixed3 move = cos((_WaveCurrentTime * SCALE_TIME) + (pos.x + pos.y + pos.z)) * v.color.a * axis;
    pos.xyz += move;
    pos.xyz = mul(unity_WorldToObject, pos).xyz;

    //頂点
    o.pos = GallopObjectToClipPos(pos);

    //カラーテクスチャ
    o.uv = TRANSFORM_TEX(v.uv, _MainTex);

    //頂点カラー
    o.color = v.color;

#if defined(USE_FOG)
    float3 viewPos = UnityObjectToViewPos(v.vertex);
    o.fogParam = CalcFogParam(worldPos.y, -viewPos.z, worldPos);
#endif

    return o;
}

fixed4 fragWave(v2f i) : COLOR
{
    //トータル
    fixed4 originColor = 1.0;

    //カラーテクスチャ
    originColor = tex2D(_MainTex, i.uv);

    //頂点カラーのRにブレンド率が入っている
#if defined(USE_LIGHTMAPCOLOR)
    originColor.rgb *= lerp(_Global_LightmapColor, _Global_LightmapShadowColor, i.color.rgb.r);
#else
    originColor.rgb *= i.color.rgb;
#endif

#if defined(USE_LIGHTMAPDENSITY)
    originColor.rgb = (originColor.rgb * _Global_LightmapModulateColor) + _Global_LightmapDensityAddColor;
#endif

    originColor.rgb *= COLOR_BIAS;

#if defined(USE_FOG)
    originColor.rgb = ApplyFog(i.fogParam.x, i.fogParam.y, originColor).rgb;
#endif

    return originColor;
}

#endif
