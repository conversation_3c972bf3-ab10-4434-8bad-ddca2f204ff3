Shader "Gallop/3D/Bg/Special/Water"
{
    Properties
    {
        _WaveScale("Wave scale", Range(0.02,0.15)) = 0.063
        _WaterSurfaceScale("WaterSurfaceScale", Range(1.0, 10.0)) = 2.0
        _ReflDistort("Reflection distort", Range(0,1.5)) = 0.44
        _RefrDistort("Refraction distort", Range(0,1.5)) = 0.40
        _RefrColor("Refraction color", COLOR) = (.34, .85, .92, 1)
        _WaterSurfaceBrightness("WaterSurfaceBrightness", Range(0.0,1.0) ) = 0.0
        _WaterSurfaceDistortion("WaterSurfaceDistortion", Range(0.0, 1.0)) = 0.1
        _WaterSurfaceMoveSpeed("WaterSurfaceMoveSpeed", Range(0.0, 1.0)) = 0.1
        _WaterSurfaceMoveSize("WaterSurfaceMoveSize", Range(0.0, 1.0)) = 0.1
         WaveSpeed("Wave speed (map1 x,y; map2 x,y)", Vector) = (19,9,-16,-7)
        _WaveHeight("WaveHeight", Range(0,0.4) ) = 0.05
        _ReflectionDisableDistance("ReflectionDisableDistance", Range(0,5)) = 0.44
        _ReflectionDisablePow("ReflectionDisablePowe", Range(0,10)) = 0.44
        [NoScaleOffset] _Fresnel("Fresnel (A) ", 2D) = "gray" {}
        [NoScaleOffset] _BumpMap("Normalmap ", 2D) = "bump" {}
        [HideInInspector] _ReflectionTex("Internal Reflection", 2D) = "" {}
        [HideInInspector] _RefractionTex("Internal Refraction", 2D) = "" {}
        _WaterSurface("WaterSurface", 2D) = "black" {}
        [HideInInspector] _ReflectionBias("Reflection Bias", Range(0.0, 1.0)) = 0.0
        [HideInInspector] _BumpSpecular("Bump Specular", Range(0.0, 5.0)) = 0.3
        _WaveHumanCenter( "WaveCenter", Vector ) = ( 0, 0, 0, 0 )
        _WaveHumanHeight("WaveHumanHeight", Range(0,0.4) ) = 0.05
        _ReflectionSimpleCololr("ReflectionSimpleCololr", COLOR) = (.60, .80, 1, 1)

        [HideInInspector] _WaveScale4("_WaveScale4", Vector) = (0,0,0,0)
        [HideInInspector] _WaveOffset("_WaveOffset", Vector) = (0,0,0,0)
        [HideInInspector] _AppTime("AppTime", float) = 0
    }
    
    Subshader
    {
        Tags
        { 
            "WaterMode" = "Refractive"
            "RenderType" = "Opaque"
        }
        Cull Off 

        Pass
        {
            Tags
            { 
                "LightMode" = "UniversalForward"
            }

            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile WATER_INSIDE __
            #ifdef WATER_INSIDE
            #define HAS_REFLECTION_TEX 1
            #else
            #define HAS_REFLECTION_TEX 0
            #endif
            
            #include "GallopBgSpecialWaterCommon.hlsl"
            ENDHLSL

        }
        Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
}
