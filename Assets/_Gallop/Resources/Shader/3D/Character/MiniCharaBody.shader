Shader "Gallop/3D/Chara/MiniCharaBody"
{
    Properties
    {
        _MainTex("Diffuse Map", 2D) = "white" {}
        _CharaColor("_CharaColor", Color) = (1,1,1,1)
        _StencilMask ("Stencil Mask", int) = 100
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("Stencil Operation",int) = 2    //UnityEngine.Rendering.StencilOp.Replace

        // エミッシブ
        [Header(Emissive)]
        [NoScaleOffset]_EmissiveTex("_EmissiveTex", 2D) = "black" {}
        _EmissiveColor("_EmissiveColor", Color) = (1,1,1,1)

        // アウトライン
        [Header(Outline)]
        _OutlineWidth("_OutlineWidth",Range(0.01,5)) = 1
        _OutlineColor("_OutlineColor",Color) = (0.125,0.047,0,0.098)
        _Saturation("_Saturation", Range(0,1)) = 1
        
        [HideInInspector] _LightProbeColor("_LightProbeColor", Color) = (1,1,1,1)
    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque"
            "Queue" = "Geometry-1"
        }

        Pass
        {
            Name "MiniCharaBody"
            Tags{//DirectionalLightの情報を取得するために必須
                "LightMode" = "UniversalForward"
            }

            Stencil
            {
                Ref [_StencilMask]
                Comp [_StencilComp]
                Pass [_StencilOp]
            }

            Cull Back
            ZTest LEqual

HLSLPROGRAM
#pragma target 3.0
#pragma vertex vert
#pragma fragment frag

#define PASS PASS_MAIN_COLOR

#define USE_EMISSIVE
#include "hlsl/GallopCharaMain.hlsl"
ENDHLSL
        }

        Pass
        {
            Name "MiniCharaBodyOutline"
            Tags{//DirectionalLightの情報を取得するために必須
                "LightMode" = "Outline"
            }

            Cull Front
            ZTest Less
            /*UNUSE_ALPHA*/Blend One Zero
            ///*USE_ALPHA*/Blend SrcAlpha OneMinusSrcAlpha
            Offset 1, -1    //    メインパスがアウトラインに負けないように変更

            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
                Pass[_StencilOp]
            }

            HLSLPROGRAM
#pragma target 3.0
#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest

#define PASS PASS_OUTLINE

#define USE_EMISSIVE
#include "hlsl/GallopCharaMain.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags{ "LightMode" = "ShadowCaster" }

            // depthを書き込んだ際に隙間ができてしまう問題への対処
            // * 法線をスムーズにしている場合は起きず、ハードエッジにしたときにメッシュが分割されて発生する
            // * xxxxx発生していないのはメイン法線がアウトライン法線がであり、その法線はスムーズであるため
            // * Gallopはメイン法線がトゥーン法線（ハードエッジ）
            // * 解決方法はメイン法線をアウトライン法線に変えるか、DOF時のカリング設定を両面にする
            // * メイン法線を変更するのは作業コストが発生するので避けたい
            Cull Off

            HLSLPROGRAM
#pragma vertex vert
#pragma fragment frag

#define PASS PASS_SHADOW_CASTER
//デプス収集やShadowmap生成時にはAndroid固有のデプス補正処理は行わない
#define DISABLE_LOG_DEPTH

#define USE_EMISSIVE
#include "hlsl/GallopCharaMain.hlsl"


            ENDHLSL
        }

        Pass
        {
            Name "SimpleMirrorCaster"
            Tags{ "LightMode" = "SimpleMirrorCaster" }

            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
                Pass[_StencilOp]
            }

            Cull Back
            ZTest LEqual
            Blend One Zero, Zero One
            Offset 0, 0

            HLSLPROGRAM

#pragma target 3.0
#pragma vertex vert
#pragma fragment frag

#define PASS PASS_SIMPLE_MIRROR_CASTER
#define USE_EMISSIVE
#include "hlsl/GallopCharaMain.hlsl"

            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
    FallBack Off
}
