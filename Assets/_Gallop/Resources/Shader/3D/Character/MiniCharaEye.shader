Shader "Gallop/3D/Chara/MiniCharaEye"
{
    Properties
    {
        _MainTex("Diffuse Map", 2D) = "white" {}
        _CharaColor("_CharaColor", Color) = (1,1,1,1)
        _StencilMask ("Stencil Mask", int) = 100
        _UVOffset("_UVOffset",Vector) = (0,0,0,0)
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Disable
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("Stencil Operation",int) = 2    //UnityEngine.Rendering.StencilOp.Keep

    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque"
            "Queue" = "Geometry-1"
            "LightMode" = "UniversalForward"
        }

        Pass
        {
            Name "Unlit"

            Stencil
            {
                Ref [_StencilMask]
                Comp [_StencilComp]
                Pass [_StencilOp]
            }

            Cull Back
            ZTest LEqual
HLSLPROGRAM
#pragma target 3.0
#pragma vertex vert
#pragma fragment frag
#define MINI_EYE
#include "hlsl/GallopCharaMain.hlsl"
ENDHLSL
        }

        Pass
        {
            Name "SimpleMirrorCaster"
            Tags{ "LightMode" = "SimpleMirrorCaster" }

            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
                Pass[_StencilOp]
            }

            Cull Back
            ZTest LEqual
            Blend One Zero, Zero One
            Offset 0, 0

            HLSLPROGRAM

#pragma target 3.0
#pragma vertex vert
#pragma fragment frag

#define PASS PASS_SIMPLE_MIRROR_CASTER
#define MINI_EYE
#include "hlsl/GallopCharaMain.hlsl"

            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
    FallBack Off
}
