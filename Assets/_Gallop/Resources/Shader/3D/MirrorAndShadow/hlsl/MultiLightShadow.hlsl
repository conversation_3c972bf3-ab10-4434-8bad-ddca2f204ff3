#ifndef _MULTILIGHTSHADOW_HLSLINC_
#define _MULTILIGHTSHADOW_HLSLINC_

/***************************
    MultiLightShadow.cginc
    ライトの光を加算せず影のみを描くことを目的としたもの。
    影を受ける設定のメッシュのシェーダーに追加する想定。

    DirectionalLightを使う場合Tagsに"LightMode" = "UniversalForward"を設定する。
    #pragma multi_compile_fwdbase
    #pragma multi_compile _ USE_SHADOW_PARAM
    を追加し
    頂点シェーダーの引数でUNITY_SHADOW_COORDSを、
    頂点シェーダーでUNITY_TRANSFER_SHADOWを、
    フラグメントシェーダーでUNITY_SHADOW_ATTENUATIONのマクロを使用し影の情報を取得する。
    GetShadowColor()で影の色を取得することができる。
    
    上記マクロを使用する場合マクロ内で変数名が勝手に定義されているのでそれに合わせる必要がある。
    具体的には
    ・頂点シェーダーの引数名はv,
    ・頂点シェーダーの引数のPOSITIONの変数はvertex
    ・フラグメントシェーダーの引数のPOSITIONの変数はpos


    Pointライト・Spotライトを使う場合以下のパスを追加するだけでOK。
    Pass
    {
        Tags{ "LightMode" = "ForwardAdd" }
        Blend DstColor Zero

        CGPROGRAM
        #pragma target         3.0
        #pragma vertex         VS_ShadowAdd
        #pragma fragment       PS_ShadowAdd

        #include "MultiLightShadow.cginc"
        #pragma multi_compile_fwdadd_fullshadows
        #pragma multi_compile _ USE_SHADOW_PARAM
        ENDCG
    }
    DirectionalLightを使わずにPointライト・Spotライトのみを使う場合は上記パスを追加せず直接変更してもOK。
    （直接変更すれば１パス減って負荷が減る。


    Propertiesに設定するとき用のメモ
        _ShadowFadeCenter("_ShadowFadeCenter", Vector) = (0,0,0)
        _ShadowFadeFront("_ShadowFadeFront", Vector) = (0,0,1)
        _ShadowFadeParam("_ShadowFadeParam", Vector) = (100, 100, 0, 1)
        _ShadowStartColor("_ShadowStartColor", Color) = (0,0,0,1)
        _ShadowEndColor("_ShadowEndColor", Color) = (0,0,0,1)

*/

#define USE_AUTO_LIGHT
#include "../../../Common/ShaderCommon.hlsl"

//変数はMultiLightShadowPropsに移動

//-------------------------------------------------------------
// VS Input
//-------------------------------------------------------------
struct ShadowInput
{
    float3 vertex : POSITION;
};

//-------------------------------------------------------------
// PS Input
//-------------------------------------------------------------
struct ShadowV2P
{
    float4 pos : POSITION;
    float3 posWorld : TEXCOORD0;

#if defined(BUILDIN_PIPELINE)
    UNITY_LIGHTING_COORDS(1, 2)
#else
    float4 shadowCoord : TEXCOORD1;
#endif
};


//-------------------------------------------------------------
//    VS
//-------------------------------------------------------------
ShadowV2P VS_ShadowAdd(ShadowInput v )
{
    ShadowV2P OUT = (ShadowV2P)0;
    VertexPositionInputs vpi = GallopGetVertexPositionInputs(v.vertex);

    OUT.pos = vpi.positionCS;
    OUT.posWorld = vpi.positionWS;

#if defined(BUILDIN_PIPELINE)
    UNITY_TRANSFER_LIGHTING(OUT, half2(0, 0));
#else
    OUT.shadowCoord = GetShadowCoord(vpi);
#endif
    return OUT;
}

// 影の色を取得する。
fixed3 GetShadowColor(float3 posWorld, float shadowRate)
{
#if defined(USE_SHADOW_PARAM)
    float3 l2w = posWorld - _ShadowFadeCenter;
    l2w = lerp(l2w, dot(l2w, _ShadowFadeFront) * _ShadowFadeFront, _ShadowFadeParam.z);
    float distance = length(l2w);
    float fadeRate = saturate((distance - _ShadowFadeParam.x) / max(0.001, _ShadowFadeParam.y));
    fadeRate = lerp(1 - fadeRate, fadeRate, _ShadowFadeParam.w);

    return lerp(lerp(lerp(_ShadowStartColor, 1, shadowRate), lerp(_ShadowEndColor, 1, shadowRate), fadeRate), 1, fadeRate);
#else
#if defined(USE_DIR_SHADOW_COLOR)
    // ShadowRateを2値化、有効でない場合はそのまま
    shadowRate = lerp(shadowRate, saturate(shadowRate - abs(step(1, shadowRate) - 1)), _IsBinarization);
    // 影の色と濃淡を制御
    return lerp(_DirShadowColor, 1, lerp(1, shadowRate, _DirShadowIntensity));
#else
    return shadowRate;
#endif
#endif
}

//-------------------------------------------------------------
//    PS
//-------------------------------------------------------------
fixed4 PS_ShadowAdd(ShadowV2P IN ) : COLOR
{
    fixed4 diffuseColor = 1;

#if defined(BUILDIN_PIPELINE)

    // POINT, SPOT, DIRECTIONAL はそれぞれライトによって定義される。
#ifdef POINT
    UNITY_LIGHT_ATTENUATION(atten, IN, IN.posWorld);
    // lightCoord, shadow は↑の UNITY_LIGHT_ATTENUATION 内で宣言されている。
    fixed3 shadowColor = GetShadowColor(IN.posWorld, shadow);
    diffuseColor.rgb = max(min(1, length(lightCoord.rgb)), shadowColor);
#endif
#ifdef SPOT
    // スポットライト外の影が表示されず、不自然だが一旦許容。
    UNITY_LIGHT_ATTENUATION(atten, IN, IN.posWorld);
    // lightCoord, shadow は↑の UNITY_LIGHT_ATTENUATION 内で宣言されている。
    fixed3 shadowColor = GetShadowColor(IN.posWorld, shadow);
    diffuseColor.rgb = max((1 - tex2D(_LightTexture0, lightCoord.xy / lightCoord.w + 0.5).w), shadowColor);
#endif
#ifdef DIRECTIONAL
    // DirectionalLightの２個め以降はここに来るが
    // 使うためにはCascaded ShadowをONにする必要があり
    // 重いので非対応となった。
#endif

#else

    //LightMapを使用する場合はSAMPLE_SHADOWMASK(input.lightmapUV)からShadowMaskを求める必要がある
    //無い場合はunity_ProbesOcclusionから取得
    half4 shadowMask = unity_ProbesOcclusion;
    //1 pass内でで全ライト処理するのであれば以下を有効にすべきだが、追加ライトだけを考慮しているので、ここでは考慮しない
    //URPではこの関数自体不要になるはず
    /*
    Light lightData = GetMainLight(IN.shadowCoord);
    float shadow = lightData.shadowAttenuation;
    */

    Light lightData;
    float shadow = 1.0;

    //URPでは_ADDITIONAL_LIGHT_SHADOWSでまとめてライト情報が来る
#ifdef _ADDITIONAL_LIGHT_SHADOWS
    int lightCount = GetAdditionalLightsCount();
    for (int i = 0;i < lightCount;i++)
    {
        lightData = GetAdditionalLight(i, IN.posWorld, shadowMask);
        shadow *= lightData.shadowAttenuation;
    }
    shadow = saturate(shadow);
#endif

    fixed3 shadowColor = GetShadowColor(IN.posWorld, shadow);
    diffuseColor.rgb = shadowColor;

#endif

    return diffuseColor;
}

#endif
