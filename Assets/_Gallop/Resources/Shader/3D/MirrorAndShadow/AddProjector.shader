Shader "Gallop/3D/MirrorAndShadow/AddProjector"
{
    Properties
    {
        _MainTex("Projection", 2D) = "white" {}
        _Color("_Color",Color) = (0,0,0,1)
        _SecondColor("_SecondColor",Color) = (0,0,0,1)
        _OutlineLightModeTag("_OutlineLightModeTag",Int) = 2
        
        // Stencil
        _StencilMask ("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("Stencil Operation",int) = 0    //UnityEngine.Rendering.StencilOp.Keep
    }

    SubShader
    {
        Tags{ "IgnoreProjector" = "True" }
        Lighting Off
        ZWrite Off

        Pass
        {
            Name "AddProjectorMainPass"
            ColorMask RGB
            Blend One One
            
            Stencil
            {
                Ref [_StencilMask]
                Comp [_StencilComp]
                Pass [_StencilOp]
            }

            HLSLPROGRAM
            #pragma target         3.0
            #pragma vertex         vert
            #pragma fragment       frag
            #pragma fragmentoption ARB_precision_hint_fastest

            #define PROJECTOR_ADD
            #define PROJECTOR_FALLCUTTOFF
            #define USE_SECOND_COLOR

            #include "hlsl/ProjectorCommon.hlsl"

            ENDHLSL
        }

        /*
        // Outline Pass
        Pass
        {
            Tags{ "LightMode" = "Outline" }

            ColorMask RGB
            Blend One One

            Cull Front
            ZTest LEqual
            Offset 1, -1    //    メインパスがアウトラインに負けないように変更

            HLSLPROGRAM

            #pragma target         3.0
            #pragma vertex         vertOutline
            #pragma fragment       frag
            #pragma fragmentoption ARB_precision_hint_fastest

            #define PROJECTOR_ADD
            #define PROJECTOR_FALLCUTTOFF

            #include "hlsl/ProjectorCommon.hlsl"

            ENDHLSL
        }
        */
    }

    FallBack Off 
}