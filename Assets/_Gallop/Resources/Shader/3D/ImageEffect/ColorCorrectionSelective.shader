Shader "Gallop/ImageEffects/ColorCorrectionSelective" {
    Properties {
        _MainTex ("Base (RGB)", 2D) = "" {}

        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("Stencil Operation",int) = 0    //UnityEngine.Rendering.StencilOp.Keep
    }

    CGINCLUDE

    #include "UnityCG.cginc"

    struct v2f {
        float4 pos : SV_POSITION;
        float2 uv : TEXCOORD0;
    };

    sampler2D _MainTex;

    float4 selColor;
    float4 targetColor;

    v2f vert( appdata_img v ) {
        v2f o;
        o.pos = UnityObjectToClipPos (v.vertex);
        o.uv = v.texcoord.xy;
        return o;
    }

    fixed4 frag(v2f i) : SV_Target {
        fixed4 color = tex2D (_MainTex, i.uv);

        fixed diff = saturate (2.0 * length (color.rgb - selColor.rgb));
        color = lerp (targetColor, color, diff);
        return color;
    }

    ENDCG

Subshader {
 Pass {
      ZTest Always Cull Off ZWrite Off

      Stencil
      {
        Ref [_StencilMask]
        Comp [_StencilComp]
        Pass [_StencilOp]
      }

      CGPROGRAM

      #pragma vertex vert
      #pragma fragment frag

      ENDCG
  }
}

Fallback off

}
