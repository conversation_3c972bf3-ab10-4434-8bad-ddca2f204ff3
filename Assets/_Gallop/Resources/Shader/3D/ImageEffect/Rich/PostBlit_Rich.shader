 Shader "Gallop/ImageEffects/Rich/PostBlit_Rich"
 {
    Properties {
        _MainTex ("Base", 2D) = "" {}
        _DimmerColor("Dimmer Color", Color) = (1.0,1.0,1.0,1.0)
    }

    HLSLINCLUDE
    #include "../hlsl/PostBlit_Rich.hlsl"
    #include "../hlsl/ScreenOverlay.hlsl"
    ENDHLSL
    
    
    SubShader
    {
        // pass 0 DimmerOnly
        Pass
        {
            ZTest Off Cull Off ZWrite Off
            Fog { Mode off }
            Name "FLAG_POSTFILM_DIMMER_ONLY"

            HLSLPROGRAM
            #pragma exclude_renderers flash
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma vertex vert
            #pragma fragment frag_DimmerOnly
            ENDHLSL
        }

        // pass 1 ScreenOverlay1
        Pass
        {
            ZTest Off Cull Off ZWrite Off
            Fog { Mode off }
            Name "FRAG_SCREEN_OVERLAY1"

            HLSLPROGRAM
            #pragma exclude_renderers flash
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma vertex vert
            
            #pragma multi_compile MODE_LERP MODE_ADD MODE_MUL MODE_VIGNETTE_LERP MODE_VIGNETTE_ADD MODE_VIGNETTE_MUL MODE_MONOCHROME MODE_SCREENBLEND MODE_VIGNETT_SCREENBLEND
            #pragma multi_compile COLOR_ONLY BLEND_NONE BLEND_LERP BLEND_ADD BLEND_MUL                

            #include "../hlsl/ScreenOverlay1.hlsl"

            #pragma fragment fragScreenOverlay1
            
            ENDHLSL
        }

        // pass 2 ScreenOverlay1 - INVERSE_VIGNETTE
        Pass
        {
            ZTest Off Cull Off ZWrite Off
            Fog { Mode off }
            Name "FRAG_POSTFILM2_INVERSE_VIGNETTE"

            HLSLPROGRAM
            #pragma exclude_renderers flash
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma vertex vert
            
            #pragma multi_compile MODE_VIGNETTE_LERP MODE_VIGNETTE_ADD MODE_VIGNETTE_MUL MODE_VIGNETT_SCREENBLEND
            #pragma multi_compile COLOR_ONLY BLEND_NONE BLEND_LERP BLEND_ADD BLEND_MUL

            #include "../hlsl/ScreenOverlay1.hlsl"

            #pragma fragment fragScreenOverlay1
            
            ENDHLSL
        }


        // pass 3 ScreenOverlay2
        Pass
        {
            ZTest Off Cull Off ZWrite Off
            Fog { Mode off }
            Name "FRAG_SCREEN_OVERLAY2"

            HLSLPROGRAM
            #pragma exclude_renderers flash
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma vertex vert
            
            #pragma multi_compile MODE_LERP MODE_ADD MODE_MUL MODE_VIGNETTE_LERP MODE_VIGNETTE_ADD MODE_VIGNETTE_MUL MODE_MONOCHROME MODE_SCREENBLEND MODE_VIGNETT_SCREENBLEND
            #pragma multi_compile COLOR_ONLY BLEND_NONE BLEND_LERP BLEND_ADD BLEND_MUL   

            #include "../hlsl/ScreenOverlay2.hlsl"

            #pragma fragment fragScreenOverlay2
            
            ENDHLSL
        }

        // pass 4 ScreenOverlay2 - INVERSE_VIGNETTE
        Pass
        {
            ZTest Off Cull Off ZWrite Off
            Fog { Mode off }
            Name "FRAG_POSTFILM2_INVERSE_VIGNETTE"

            HLSLPROGRAM
            #pragma exclude_renderers flash
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma vertex vert
            
            #pragma multi_compile MODE_VIGNETTE_LERP MODE_VIGNETTE_ADD MODE_VIGNETTE_MUL MODE_VIGNETT_SCREENBLEND
            #pragma multi_compile COLOR_ONLY BLEND_NONE BLEND_LERP BLEND_ADD BLEND_MUL

            #include "../hlsl/ScreenOverlay2.hlsl"

            #pragma fragment fragScreenOverlay2
            
            ENDHLSL
        }

        
    }

    FallBack Off
}