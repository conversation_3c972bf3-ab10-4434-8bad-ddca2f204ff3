//BuildInPipelineでサポートしていたがUnityHLSLではサポートしていない関数のヘルパー
#ifndef _UNIVERSAL_HLSL_HELPER_INC_
#define _UNIVERSAL_HLSL_HELPER_INC_

#define UNITY_TRANSFER_FOG(o,outpos) o.fogCoord.x = (outpos).z
#define UNITY_APPLY_FOG_COLOR(coord,col,fogCol) float unityFogFactor = ComputeFogFactor((coord).x); col.rgb = MixFogColor(col,fogCol,unityFogFactor)
#define UNITY_APPLY_FOG(coord,col) UNITY_APPLY_FOG_COLOR(coord,col.rgb,unity_FogColor.rgb)

//UnityCGであったよく使うInput構造体
struct appdata_base
{
    float3 vertex : POSITION;

#ifdef ENABLE_ALPHA_TEST
    float2 texcoord : TEXCOORD0;
#else
    float4 texcoord : TEXCOORD0;
#endif
};

struct appdata_img
{
    float3 vertex : POSITION;
    float2 texcoord : TEXCOORD0;
};

struct appdata_full
{
    float4 vertex : POSITION;
    float4 tangent : TANGENT;
    float3 normal : NORMAL;
    float4 texcoord : TEXCOORD0;
    float4 texcoord1 : TEXCOORD1;
    float4 texcoord2 : TEXCOORD2;
    float4 texcoord3 : TEXCOORD3;
    fixed4 color : COLOR;
    UNITY_VERTEX_INPUT_INSTANCE_ID
};

//URPではfloat3のObject->World変換しかないのでfloat4はここでサポート(float3とwの扱いが違うので注意が必要)
float3 TransformObjectToWorld(float4 positionOS)
{
    return mul(GetObjectToWorldMatrix(), positionOS).xyz;
}

float4 TransformObjectToHClip(float4 positionOS)
{
    //UnityObjectToClipPos準拠
    return TransformObjectToHClip(positionOS.xyz);
}

float3 TransformObjectToWorldNoTranslate(in float3 positionOS)
{
    return mul(GetObjectToWorldMatrix(), float4(positionOS,0)).xyz;
}

float3 TransformLocalToView(float3 localPos)
{
    float3 worldPos = TransformObjectToWorld(localPos);
    return TransformWorldToView(worldPos);
}

float3 TransformLocalToView(float4 localPos)
{
    float3 worldPos = TransformObjectToWorld(localPos);
    return TransformWorldToView(worldPos);
}

float SampleSceneDepthProj(float4 uv)
{
    return SampleSceneDepth(uv.xy / uv.w);
}

inline float3 ObjSpaceViewDir(in float3 localPos)
{
    float3 objSpaceCameraPos = TransformWorldToObject(_WorldSpaceCameraPos.xyz);
    return objSpaceCameraPos - localPos;
}

inline float3 ObjSpaceViewDir(in float4 localPos)
{
    return ObjSpaceViewDir(localPos.xyz);
}

//Worldで渡される事が無いので全てワールド変換を行う
inline float3 WorldSpaceViewDir(in float3 localPos)
{
    //Translate考慮
    float3 worldPos = TransformObjectToWorld(localPos);
    return _WorldSpaceCameraPos.xyz - worldPos;    //正規化しない
}

inline float3 WorldSpaceViewDir(in float4 localPos)
{
    //Translateはそのまま
    float3 worldPos = TransformObjectToWorld(localPos);
    return _WorldSpaceCameraPos.xyz - worldPos;    //正規化しない
}

//ProjectorClipの減衰率を取得する
inline float GetProjectorClipFalloff(in float4 positionOS)
{
    float4x4 projectorWorld = mul(unity_ProjectorClip, GetObjectToWorldMatrix());
    return mul( projectorWorld, positionOS).x;
}

inline float GetProjectorClipFalloff(in float3 positionOS)
{
    return GetProjectorClipFalloff(float4(positionOS, 1));
}

//Projectorスペースへの変換
inline float4 GetProjectorPosition(in float4 positionOS)
{
    float4x4 projectorWorld = mul(unity_Projector, GetObjectToWorldMatrix());
    return mul(projectorWorld, positionOS);
}

inline float4 GetProjectorPosition(in float3 positionOS)
{
    return GetProjectorPosition(float4(positionOS, 1));
}

#endif