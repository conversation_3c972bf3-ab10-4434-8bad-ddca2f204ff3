using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using Gallop.RenderPipeline;
#if UNITY_EDITOR
using UnityEditor;
#endif//UNITY_EDITOR

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// モニターカメラTimelineGroup
            /// </summary>
            [System.Serializable]
            public class TimelineMonitorCameraGroupData : ITimelineSetGroupData
            {
                [System.NonSerialized]
                private static readonly CutInTimelineKeyDataType[] Types =
                {
                    CutInTimelineKeyDataType.MonitorCameraEnable,
                    CutInTimelineKeyDataType.CameraMotion,
                    CutInTimelineKeyDataType.CameraParam,
                    CutInTimelineKeyDataType.CameraTargetChara,
                    CutInTimelineKeyDataType.CameraPos,
                    CutInTimelineKeyDataType.CameraLookAt,
                    CutInTimelineKeyDataType.CameraFov,
                };

                public class MonitorCamera
                {
                    private const string MONITORCAMERA_ROOT_NAME = "MonitorCameraRoot";
                    private const string MONITORCAMERA_ANIMATION_NAME = "CameraAnimation";
                    private const string MONITORCAMERA_CAMERA_NAME = "Camera";

#if UNITY_EDITOR
                    private bool _isSetup;
#endif
                    /// <summary>
                    /// モニターカメラが描画するテクスチャ
                    /// これを必要なオブジェクトに渡してあげる
                    /// </summary>
                    private RenderTexture _targetRenderTexture;
                    public RenderTexture TargetRenderTexture { get { return _targetRenderTexture; } }

                    public bool IsDisableLookAtOnRotation = false;

                    public GameObject GameObject;
                    public Transform RootTransform;

                    public bool IsSetTarget;

                    public CutInTimelineMotionCamera MotionCamera;

                    public CameraData CameraData = null;
                    private bool _isUseMSAA = true;

                    public void Setup(CutInTimelineController controller,int depthOffset, bool useMSAA)
                    {
#if UNITY_EDITOR
                        if (_isSetup)
                        {
                            //エディターにてBackgroundIndexが変更されたときにセットアップ後に再度セットアップが呼び出される事がある
                            MotionCamera.targetCamera.targetTexture = _targetRenderTexture;
                            IsSetTarget = _targetRenderTexture != null;
                            return;
                        }
#endif
                        //MonitorCameraコンポーネント側がTextureを持っていない場合は共通のTextureになるので自前で用意する
                        //MonitorCameraコンポーネントは自前でカメラを持っていない場合がありその場合はカット側のカメラがTextureを持つのでここでカット管理のモニターカメラのTextureを作成
                        int width, height;
                        controller.GetMonitorCameraTextureSize(out width, out height);

                        _targetRenderTexture = new RenderTexture(width, height, 0);
#if UNITY_EDITOR
                        _targetRenderTexture.name = "CutIn.MonitorTargetRenderTexture";
#endif
                        _targetRenderTexture.Create();

                        _targetRenderTexture.filterMode = FilterMode.Bilinear;


                        var masterCamera = controller.MotionCamera.targetCamera;

                        var rootObject = new GameObject(MONITORCAMERA_ROOT_NAME + depthOffset);
                        rootObject.transform.SetParent(controller.MotionCamera.transform.parent.parent);   //CutInCameraRootと同列にする
                        rootObject.transform.localPosition = Math.VECTOR3_ZERO;
                        rootObject.transform.localScale = Math.VECTOR3_ONE;
                        rootObject.transform.localRotation = Math.QUATERNION_IDENTITY;

                        var animationObject = new GameObject(MONITORCAMERA_ANIMATION_NAME);
                        animationObject.transform.SetParent(rootObject.transform);
                        // Latch処理が走らないように初期値を設定する。Animationを使用している場合Scaleはそちらから更新される
                        animationObject.transform.localScale = StaticVariableDefine.CG3D.MonitorCamera.DEFAULT_SCALE;
                        animationObject.AddComponent<Animator>();

                        var cameraObject = new GameObject(MONITORCAMERA_CAMERA_NAME, typeof(Camera),typeof(FlareLayer));
                        cameraObject.transform.SetParent(animationObject.transform, false);
                        cameraObject.transform.localPosition = Math.VECTOR3_ZERO;
                        cameraObject.transform.localRotation = Quaternion.Euler(0.0f,180.0f,0.0f);
                        cameraObject.transform.localScale = StaticVariableDefine.CG3D.MonitorCamera.DEFAULT_SCALE;  //Latch処理が走らないように初期値を設定する

                        var camera = cameraObject.GetComponent<Camera>();
                        camera.cullingMask = masterCamera.cullingMask;
                        camera.allowHDR = false;
                        camera.allowMSAA = useMSAA;
                        camera.clearFlags = CameraClearFlags.Depth;
                        camera.fieldOfView = masterCamera.fieldOfView;
                        camera.depth = masterCamera.depth - depthOffset;
                        camera.targetTexture = _targetRenderTexture;
                        IsSetTarget = _targetRenderTexture != null;

                        //自身の変数に設定
                        GameObject = rootObject;
                        RootTransform = rootObject.transform;

                        MotionCamera = animationObject.AddComponent<CutInTimelineMotionCamera>();
                        MotionCamera.targetTransform = camera.transform;
                        MotionCamera.Setup(controller);
                        MotionCamera.SetTargetCamera(camera);

                        CameraData = camera.GetCameraData();

#if UNITY_EDITOR
                        _isSetup = true;
#endif
                    }

                    /// <summary>
                    /// 解放処理
                    /// </summary>
                    public void CleanUp()
                    {
                        if( _targetRenderTexture != null )
                        {
                            _targetRenderTexture.Release();
                            _targetRenderTexture = null;
                        }

                        if (GameObject != null)
                        {
                            GameObject.Destroy(GameObject);
                            GameObject = null;
                        }
                    }
                }

                [System.NonSerialized]
                public MonitorCamera TargetCamera;

                public TimelineKeyCameraMotionDataList MotionKeys = new TimelineKeyCameraMotionDataList();
                public TimelineKeyCameraParamDataList CameraParamKeys = new TimelineKeyCameraParamDataList();
                public TimelineKeyCameraTargetCharaDataList CameraTargetKeys = new TimelineKeyCameraTargetCharaDataList();
                public TimelineKeyMonitorCameraEnableDataList MonitorCameraEnableKeys = new TimelineKeyMonitorCameraEnableDataList();
                public TimelineKeyCameraPositionDataList CameraPositionKeys = new TimelineKeyCameraPositionDataList();
                public TimelineKeyCameraLookAtDataList CameraLookAtKeys = new TimelineKeyCameraLookAtDataList();
                public TimelineKeyCameraFovDataList CameraFovKeys = new TimelineKeyCameraFovDataList();

                public void Cleanup()
                {
                    if(TargetCamera != null)
                    {
                        TargetCamera.CleanUp();
                        TargetCamera = null;
                    }

                }

                public bool IsExistKey(CutInTimelineKey target)
                {
                    bool isExist = MotionKeys.IsExistKey(target);
                    isExist |= CameraParamKeys.IsExistKey(target);
                    isExist |= CameraTargetKeys.IsExistKey(target);
                    isExist |= MonitorCameraEnableKeys.IsExistKey(target);
                    isExist |= CameraPositionKeys.IsExistKey(target);
                    isExist |= CameraLookAtKeys.IsExistKey(target);
                    isExist |= CameraFovKeys.IsExistKey(target);

                    return isExist;
                }

                public List<ITimelineKeyDataList> GetKeyLists()
                {
                    var ret = new List<ITimelineKeyDataList>();
                    ret.Add(MonitorCameraEnableKeys);
                    ret.Add(MotionKeys);
                    ret.Add(CameraParamKeys);
                    ret.Add(CameraTargetKeys);
                    ret.Add(CameraPositionKeys);
                    ret.Add(CameraLookAtKeys);
                    ret.Add(CameraFovKeys);
                    return ret;
                }

                public ITimelineKeyDataList GetKeyList(int index)
                {
                    switch (GetKeyType(index))
                    {
                        case CutInTimelineKeyDataType.CameraMotion:
                            return MotionKeys;
                        case CutInTimelineKeyDataType.CameraParam:
                            return CameraParamKeys;
                        case CutInTimelineKeyDataType.CameraTargetChara:
                            return CameraTargetKeys;
                        case CutInTimelineKeyDataType.MonitorCameraEnable:
                            return MonitorCameraEnableKeys;
                        case CutInTimelineKeyDataType.CameraPos:
                            return CameraPositionKeys;
                        case CutInTimelineKeyDataType.CameraLookAt:
                            return CameraLookAtKeys;
                        case CutInTimelineKeyDataType.CameraFov:
                            return CameraFovKeys;
                    }
                    return null;
                }

                public CutInTimelineKeyDataType GetKeyType(int index)
                {
                    return Types[index];
                }

                public int GetKeyTypeNum()
                {
                    return Types.Length;
                }

                public object GetArgumentValue(int index)
                {
                    return null;
                }

                private void CameraTargetCharaUpdate(CutInTimelineController controller, int currentFrame)
                {
                    // データ無し
                    if (TargetCamera == null || TargetCamera.MotionCamera == null)
                    {
                        return;
                    }

                    var keys = CameraTargetKeys;
                    // 無効属性
                    if (keys.IsNotPlayable(controller))
                    {
                        return;
                    }

                    CutInTimelineController.FindTimelineKey(
                        out TimelineKeyCameraTargetCharaData curData,
                        out TimelineKeyCameraTargetCharaData nextData,
                        keys, currentFrame, controller.availableFindKeyCache);
                    if (curData == null)
                    {
                        return;
                    }

                    controller.UpdateCameraScale(TargetCamera.RootTransform, keys, curData, nextData);
                    if (controller.OnTargetCharaChange != null)
                    {
                        CutInTimelineController.CameraTargetChangeContext context;
                        context.MotionCamera = TargetCamera.MotionCamera;
                        context.CameraTargetKeyList = keys;
                        context.CameraTargetKey = curData;
                        context.NextCameraTargetKey = nextData;
                        context.CameraMotionKeyList = MotionKeys;
                        controller.OnTargetCharaChange(ref context);
                    }
                }

                /// <summary>
                /// 一時作業データ
                /// </summary>
                private TimelineKeyCameraParamData _tempTimelineKeyCameraParamData;
                private void CameraParamUpdate(CutInTimelineController controller, int currentFrame)
                {
                    if (TargetCamera == null)
                    {
                        return;
                    }

                    // データ無し
                    var camera = TargetCamera.MotionCamera.targetCamera;
                    if (camera == null)
                    {
                        return;
                    }

                    var cameraData = TargetCamera.CameraData;
                    if (cameraData == null)
                    {
                        return;
                    }

                    var keys = CameraParamKeys;
                    // 無効属性
                    if (keys.IsNotPlayable(controller))
                    {
                        return;
                    }

                    // キー取得
                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    CutInTimelineController.FindTimelineKey(out curKey, out nextKey, keys, currentFrame, controller.availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }

                    var curData = curKey as TimelineKeyCameraParamData;
                    var nextData = nextKey as TimelineKeyCameraParamData;
                    if (curData == null)
                    {
                        return;
                    }

                    if (curData.IsValidBillBoardMode)
                    {
                        var bbCtrl = camera.GetComponent<CourseBillboardController>();
                        if (bbCtrl != null)
                        {
                            bbCtrl.SetRotationType(curData.BillBoardRotationType);
                        }
                    }

                    // キャラを描画するレイヤーの使用切替
                    TimelineKeyCameraParamData.LayerCheck(camera, curData);

                    if ((nextData == null) || !nextData.IsInterpolateKey())
                    {
                        camera.nearClipPlane = curData.NearClipPlane.Value;
                        camera.farClipPlane = curData.FarClipPlane.Value;
                        cameraData.RenderingLayerMask = curData.RenderingLayerMask.IsValid ? (uint)curData.RenderingLayerMask.Value : CameraData.RENDERING_LAYER_MASK_DEFAULT;
                        return;
                    }

                    //補間した値を求めて、パラメータに適用
                    var t = CutInTimelineController.CalculateInterpolationValue(curData, nextData, currentFrame);
                    nextData.Interpolate(curData,ref _tempTimelineKeyCameraParamData, t);
                    camera.nearClipPlane = _tempTimelineKeyCameraParamData.NearClipPlane.Value;
                    camera.farClipPlane = _tempTimelineKeyCameraParamData.FarClipPlane.Value;
                    cameraData.RenderingLayerMask = _tempTimelineKeyCameraParamData.RenderingLayerMask.IsValid ? (uint)_tempTimelineKeyCameraParamData.RenderingLayerMask.Value : CameraData.RENDERING_LAYER_MASK_DEFAULT;
                }

                private void MotionUpdate(CutInTimelineController controller, int currentFrame)
                {
                    var keys = MotionKeys;

                    // 無効属性
                    if (keys.IsNotPlayable(controller))
                    {
                        return;
                    }

                    // キー取得
                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    CutInTimelineController.FindTimelineKey(out curKey, out nextKey, keys, currentFrame, controller.availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }

                    var curData = curKey as TimelineKeyCameraMotionData;
                    CutInTimelineController.CameraMotionChangeContext context;
                    context.MotionCamera = TargetCamera.MotionCamera;
                    context.CameraMotionKeyList = keys;
                    context.CameraMotionKey = curData;
                    context.CameraTargetCharaKeyList = CameraTargetKeys;
                    controller.OnCameraMotionChange(ref context);
                }

                private void EnableUpdate(CutInTimelineController controller, int currentFrame)
                {
                    var keys = MonitorCameraEnableKeys;

                    // 無効属性
                    if (keys.IsNotPlayable(controller))
                    {
                        return;
                    }

                    // キー取得
                    CutInTimelineKey curKey = null;
                    CutInTimelineController.FindTimelineKeyCurrent(out curKey, keys, currentFrame, controller.availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }

                    var curData = curKey as TimelineKeyMonitorCameraEnableData;
                    SetEnable(curData.IsEnable);
                }

                private void SetEnable(bool enable)
                {
                    //モニター描画先がない=対象モニターが指定されていない時は常に無効
                    if (TargetCamera.IsSetTarget)
                    {
                        TargetCamera.MotionCamera.targetCamera.enabled = enable;
                        TargetCamera.MotionCamera.enabled = enable;
                    }
                    else
                    {
                        TargetCamera.MotionCamera.targetCamera.enabled = false;
                        TargetCamera.MotionCamera.enabled = false;
                    }
                }

                private void CameraPositionUpdate(CutInTimelineController controller, int currentFrame)
                {
                    if (TargetCamera == null)
                    {
                        return;
                    }
                    
                    var camera = TargetCamera.MotionCamera.targetCamera;
                    if (camera == null)
                    {
                        return;
                    }

                    if (!CalculateCameraPos(controller, currentFrame, out var setPos))
                    {
                        return;
                    }

                    TargetCamera.MotionCamera.MyTransform.localPosition = setPos;
                }

                /// <summary>
                /// CameraPosのトラックからモニターカメラの位置を求める
                /// </summary>
                /// <param name="controller"></param>
                /// <param name="currentFrame"></param>
                /// <param name="position"></param>
                /// <returns></returns>
                private bool CalculateCameraPos(CutInTimelineController controller, int currentFrame, out Vector3 position)
                {
                    position = Vector3.zero;
                    var keys = CameraPositionKeys;
                    if (keys.IsNotPlayable(controller))
                    {
                        return false;
                    }
                    
                    // キー取得
                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    CutInTimelineController.FindTimelineKey(out curKey, out nextKey, keys, currentFrame,
                        controller.availableFindKeyCache);
                    if (curKey == null)
                    {
                        return false;
                    }
                    
                    TimelineKeyCameraPositionData curData = curKey as TimelineKeyCameraPositionData;
                    TimelineKeyCameraPositionData nextData = nextKey as TimelineKeyCameraPositionData;
                    if (curData == null || !curData.IsValid)
                    {
                        return false;
                    }

                    TargetCamera.IsDisableLookAtOnRotation = curData.IsDisableLookAt;

                    if (nextData != null && nextData.GetInterpolateType() != CutInInterpolateType.None)
                    {
                        // 補完処理
                        float t = CutInTimelineController.CalculateInterpolationValue(curData, nextData, currentFrame);
                        
                        switch (nextData.GetBezierPointCount())
                        {
                            default:
                            case 0:
                                position = CutInTimelineController.LerpWithoutClamp(curData.GetValue(),
                                    nextData.GetValue(), t);
                                break;
                            case 1:
                                curData.GetValue().Bezier(
                                    nextData.GetValue(),
                                    nextData.GetBezierPoint(0),
                                    t,
                                    out position,
                                    nextData._frame - curData._frame
                                );
                                break;
                            case 2:
                                curData.GetValue().Bezier(
                                    nextData.GetValue(),
                                    nextData.GetBezierPoint(0),
                                    nextData.GetBezierPoint(1),
                                    t,
                                    out position,
                                    nextData._frame - curData._frame
                                );
                                break;
                        }
                    }
                    else
                    {
                        position = curData.GetValue();
                    }

                    return true;
                }

                public void CameraLookAtUpdate(CutInTimelineController controller, int currentFrame)
                {
                    if (TargetCamera == null)
                    {
                        return;
                    }

                    var camera = TargetCamera.MotionCamera.targetCamera;
                    if (camera == null)
                    {
                        return;
                    }

                    // カメラの回転を求める
                    if (!CalculateCameraLookAt(controller, currentFrame, out var lookAtPos))
                    {
                        return;
                    }

                    Vector3 forward = lookAtPos - TargetCamera.MotionCamera.PositionOffset;
                    TargetCamera.MotionCamera.MyTransform.localRotation =
                        Quaternion.LookRotation(forward.normalized, Math.VECTOR3_UP);
                }

                public bool CalculateCameraLookAt(CutInTimelineController controller, int currentFrame, out Vector3 lookAtPos)
                {
                    lookAtPos = Vector3.zero;
                    var keys = CameraLookAtKeys;
                    if (keys.IsNotPlayable(controller))
                    {
                        return false;
                    }
                    
                    // キー取得
                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    CutInTimelineController.FindTimelineKey(out curKey, out nextKey, keys, currentFrame,
                        controller.availableFindKeyCache);
                    if (curKey == null)
                    {
                        return false;
                    }
                    
                    var curData = curKey as TimelineKeyCameraLookAtData;
                    var nextData = nextKey as TimelineKeyCameraLookAtData;
                    if (curData == null || !curData.IsValid)
                    {
                        return false;
                    }
                    
#if UNITY_EDITOR
                    if (TargetCamera.MotionCamera == null || TargetCamera.MotionCamera.targetTransform == null)
                    {
                        return false;
                    }
#endif

                    Vector3 cameraPos;
                    
                    // カメラ位置を取得
#if !UNITY_EDITOR
                    if (TargetCamera.MotionCamera.transformObject != null)
                    {
                        cameraPos = TargetCamera.MotionCamera.transformObject.timelinePos;
                    }
                    else
                    {
                        cameraPos = TargetCamera.MotionCamera.targetTransform.position;
                    }
#else
                    CalculateCameraPos(controller, currentFrame, out cameraPos);
#endif

                    var isIgnoreCameraPos = TargetCamera.IsDisableLookAtOnRotation;
                    if (nextData != null && nextData.GetInterpolateType() != CutInInterpolateType.None)
                    {
                        // 補完処理
                        float t = CutInTimelineController.CalculateInterpolationValue(curData, nextData, currentFrame);
                        switch (nextData.GetBezierPointCount())
                        {
                            default:
                            case 0:
                                {
                                    lookAtPos = CutInTimelineController.LerpWithoutClamp(curData.GetValue(cameraPos),
                                        nextData.GetValue(cameraPos, isIgnoreCameraPos), t);
                                }
                                break;
                            case 1:
                                {
                                    Vector3 nextCameraPos = Vector3.zero;
                                    CalculateCameraPos(controller, nextData._frame, out nextCameraPos);
                                    curData.GetValue(cameraPos, isIgnoreCameraPos).Bezier(
                                        nextData.GetValue(nextCameraPos),
                                        nextData.GetBezierPoint(0, nextCameraPos),
                                        t,
                                        out lookAtPos,
                                        nextData._frame - curData._frame
                                    );
                                }
                                break;
                            case 2:
                                {
                                    Vector3 nextCameraPos = Vector3.zero;
                                    CalculateCameraPos(controller, nextData._frame, out nextCameraPos);
                                    curData.GetValue(cameraPos, isIgnoreCameraPos).Bezier(
                                        nextData.GetValue(nextCameraPos),
                                        nextData.GetBezierPoint(0, nextCameraPos),
                                        nextData.GetBezierPoint(1, nextCameraPos),
                                        t,
                                        out lookAtPos,
                                        nextData._frame - curData._frame
                                    );
                                }
                                break;
                        }
                    }
                    else
                    {
                        // 補間の指定がないときはCurrentKeyの値に設定
                        lookAtPos = curData.GetValue(cameraPos, isIgnoreCameraPos);
                    }

                    return true;
                }

                private void CameraFovUpdate(CutInTimelineController controller, int currentFrame)
                {
                    const float DEFAULT_FOV = 80f;

                    if (TargetCamera == null)
                    {
                        return;
                    }

                    var camera = TargetCamera.MotionCamera.targetCamera;
                    if (camera == null)
                    {
                        return;
                    }

                    var keys = CameraFovKeys;
                    if (keys.IsNotPlayable(controller))
                    {
                        return;
                    }

                    // キーを取得
                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    CutInTimelineController.FindTimelineKey(out curKey, out nextKey, keys, currentFrame,
                        controller.availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }
                    
                    TimelineKeyCameraFovData curData = curKey as TimelineKeyCameraFovData;
                    TimelineKeyCameraFovData nextData = nextKey as TimelineKeyCameraFovData;
                    if (curData == null || !curData.IsValid)
                    {
                        return;
                    }

                    float setFov = DEFAULT_FOV;
                    if (nextData != null && nextData.GetInterpolateType() != CutInInterpolateType.None)
                    {
                        // 補間処理
                        float t = CutInTimelineController.CalculateInterpolationValue(curData, nextData, currentFrame);
                        setFov = CutInTimelineController.LerpWithoutClamp(curData.fov, nextData.fov, t);
                    }
                    else
                    {
                        // 補間設定がなければCurrentKeyの値に設定する
                        switch (curData.fovType)
                        {
                            case CameraFovType.Direct:
                                setFov = curData.fov;
                                break;
                        }
                    }

                    TargetCamera.MotionCamera.targetCamera.fieldOfView = setFov;
                }

                /// <summary>
                /// モニターカメラ更新処理
                /// </summary>
                /// <param name="controller"></param>
                /// <param name="currentFrame"></param>
                public void AlterUpdate(CutInTimelineController controller, int currentFrame,int lineIndex)
                {
                    CreateTargetCamera(controller, lineIndex);

                    // アニメーションは遅延しないようにAlterUpdateで更新する
                    // Attachが参照する
                    MotionUpdate(controller, currentFrame);
                }

                /// <summary>
                /// モニターカメラ更新処理
                /// </summary>
                /// <param name="controller"></param>
                /// <param name="currentFrame"></param>
                public void AlterLateUpdate(CutInTimelineController controller, int currentFrame, int lineIndex)
                {
                    CreateTargetCamera(controller, lineIndex);

                    EnableUpdate(controller, currentFrame);
                    if (TargetCamera.MotionCamera.enabled)
                    {
                        CameraParamUpdate(controller, currentFrame);
                        CameraTargetCharaUpdate(controller,currentFrame);
                        CameraPositionUpdate(controller, currentFrame);
                        CameraLookAtUpdate(controller, currentFrame);
                    }
                    TargetCamera.MotionCamera.AlterLateUpdate(Math.VECTOR3_ZERO, Math.QUATERNION_IDENTITY);

                    if (TargetCamera.MotionCamera.enabled)
                    {
                        // AlterLateUpdateでFOVが上書きされるので後から更新する
                        CameraFovUpdate(controller, currentFrame);
                    }
                }

                private void CreateTargetCamera(CutInTimelineController controller, int lineIndex)
                {
                    if (TargetCamera == null)
                    {
                        TargetCamera = new MonitorCamera();
                        //AlterLateUpdate呼び出し時にnull判定を行っているので、onCreateMultiCameraチェックはしない
                        controller.OnCreateMonitorCamera(controller, TargetCamera);

                        //CutInエディターでは使用しない
                        if (TargetCamera.MotionCamera != null)
                        {
                            TargetCamera.MotionCamera.LineIndex = lineIndex + 1;    //0はMainMotionCameraで使用する
                        }

                        SetEnable(false);
                    }
                }

                public void OnDelete()
                {
                    if(TargetCamera != null)
                    {
                        TargetCamera.CleanUp();
                        TargetCamera = null;
                    }
                }
            }
        }//namespace Cutt
    }
}
