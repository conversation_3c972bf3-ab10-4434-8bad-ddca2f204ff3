using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
#endif // UNITY_EDITOR

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            [System.Serializable]
            public class TimelineKeyCameraTargetCharaDataList :
                TimelineKeyDataListTemplate<TimelineKeyCameraTargetCharaData>
            {
                public const int INVALID_TARGET_ID = -1;

                private static readonly List<TimelineKeyCameraTargetCharaData> TMP_SEARCH_KEY_LIST =
                    new List<TimelineKeyCameraTargetCharaData>();

                /// <summary>
                /// カメラをキャラに紐づけるチェックを外している場合に、この値で毎フレーム上書かれる
                /// 0なら1番目のキャラに常にカメラが追従する
                /// </summary>
                [SerializeField]
                private int _targetCharacterId = INVALID_TARGET_ID;
                public int TargetCharacterId
                {
                    get => _targetCharacterId;
                    set => _targetCharacterId = value;
                }

                /// <summary>
                /// フレームの範囲内のキーを含む一時リストを返す
                /// <remarks>startFrame以上endFrame未満のキーが列挙される</remarks>
                /// </summary>
                /// <param name="startFrame">範囲開始</param>
                /// <param name="endFrame">範囲終了</param>
                /// <returns></returns>
                public List<TimelineKeyCameraTargetCharaData> GetTempKeyListBetweenFrame(int startFrame, int endFrame)
                {
                    FindKeyList(TMP_SEARCH_KEY_LIST, startFrame, endFrame);
                    return TMP_SEARCH_KEY_LIST;
                }

#if UNITY_EDITOR
                public override void OnGUI(GUIContext ctx)
                {
                    base.OnGUI(ctx);
                    _targetCharacterId = EditorGUILayout.IntField("ターゲットキャラ", _targetCharacterId);
                }
#endif
            }

            [System.Serializable]
            public class TimelineKeyCameraTargetCharaData : TimelineKeyWithInterpolate
            {
                public override CutInTimelineKeyDataType dataType => CutInTimelineKeyDataType.CameraTargetChara;

                /// <summary>
                /// 個別ターゲットキャラ
                /// </summary>
                [SerializeField]
                private bool _useTargetCharacter = false;
                public bool UseTargetCharacter
                {
                    set => _useTargetCharacter = value;
                    get => _isHighestCharacter || _isMainAndSubAverage || _useTargetCharacter;
                }

                [SerializeField]
                private int _targetCharacterId = -1;
                public int TargetCharacterId
                {
                    set => _targetCharacterId = value;
                    get => _isHighestCharacter || _isMainAndSubAverage ? 0 : _targetCharacterId; // 一番でかい人、メインとサブの平均をターゲットにしている場合はメイン（０）
                }

                [SerializeField]
                private bool _isBlendPersonalityMotion = false;
                public bool IsBlendPersonalityMotion
                {
                    get => _isBlendPersonalityMotion;
                    set => _isBlendPersonalityMotion = value;
                }

                /// <summary>
                /// カメラスケールを別途設定するか
                /// </summary>
                [SerializeField]
                private bool _individualScale = false;
                public bool IndividualScale => _individualScale;

                /// <summary>
                /// スケール用のターゲットキャラ番号
                /// </summary>
                [SerializeField]
                private int[] _targetCharacterIndices = {};
                public int[] TargetCharacterIndices => _targetCharacterIndices;

                /// <summary>
                /// キャラの中で最も背の高い人
                /// </summary>
                [SerializeField]
                private bool _isHighestCharacter = false;
                public bool IsHighestCharacter
                {
                    get => _isHighestCharacter;
                    set => _isHighestCharacter = value;
                }

                /// <summary>
                /// センターとサブの一番大きい人の間で平均をとる
                /// </summary>
                [SerializeField]
                private bool _isMainAndSubAverage = false;
                public bool IsMainAndSubAverage
                {
                    get => _isMainAndSubAverage;
                    set => _isMainAndSubAverage = value;
                }

#if UNITY_EDITOR
                public override void Copy(CutInTimelineKey dest)
                {
                    base.Copy(dest);

                    var d = (TimelineKeyCameraTargetCharaData)dest;
                    d._useTargetCharacter = _useTargetCharacter;
                    d._targetCharacterId = _targetCharacterId;
                    d._individualScale = _individualScale;
                    d._targetCharacterIndices = _targetCharacterIndices.ToArray();
                }

                public override CutInTimelineKey InterpolateForEdit(CutInTimelineKey interpKey,
                    CutInTimelineKey prevKey, float t)
                {
                    var outData = (TimelineKeyCameraTargetCharaData)interpKey;
                    var prevData = (TimelineKeyCameraTargetCharaData)prevKey;
                    outData._useTargetCharacter = prevData._useTargetCharacter;
                    outData._targetCharacterId = prevData._targetCharacterId;
                    outData._individualScale = prevData._individualScale;
                    outData._targetCharacterIndices = prevData._targetCharacterIndices.ToArray();
                    return outData;
                }

                public override void OnGUI(GUIContext ctx, CutInTimelineController controller)
                {
                    base.OnGUI(ctx, controller);

                    // #144168 タブ切り替え直後のInitialUpdateで_characterListが空になるため、その場合は更新しない
                    if (controller.CharacterList.IsNullOrEmpty())
                    {
                        return;
                    }

                    // ターゲットキャラ指定
                    _useTargetCharacter = EditorGUILayout.Toggle("個別ターゲットキャラ指定", _useTargetCharacter);
                    if (_useTargetCharacter)
                    {
                        TargetCharacterId = EditorGUILayout.IntField(TextUtil.Format("ターゲットキャラ"), _targetCharacterId);
                        TargetCharacterId = Mathf.Clamp(TargetCharacterId, -1, controller.CharacterList.Count - 1);

                        // ターゲットキャラ == -1の場合、固定カメラのアニメーションを呼び出すことになっているので、
                        // スケール個別設定は使わせない
                        var useFixedCameraAnimation = (_targetCharacterId == -1);
                        using (new EditorGUI.DisabledScope(useFixedCameraAnimation))
                        {
                            _individualScale = EditorGUILayout.Toggle("スケール個別設定", _individualScale);
                        }
                        if (useFixedCameraAnimation)
                        {
                            _individualScale = false;
                        }
                        if (_individualScale)
                        {
                            using (new EditorGUI.IndentLevelScope())
                            {
                                var length = EditorGUILayout.IntField("スケール平均人数", _targetCharacterIndices.Length);
                                length = Mathf.Clamp(length, 1, 2);
                                if (length != _targetCharacterIndices.Length)
                                {
                                    System.Array.Resize(ref _targetCharacterIndices, length);
                                }

                                using (new EditorGUI.IndentLevelScope())
                                {
                                    for (var i = 0; i < _targetCharacterIndices.Length; i++)
                                    {
                                        _targetCharacterIndices[i] = EditorGUILayout.IntField(
                                            TextUtil.Format("{0}人目", (i + 1).ToString()), _targetCharacterIndices[i]);
                                        _targetCharacterIndices[i] = Mathf.Clamp(_targetCharacterIndices[i], -1,
                                            controller.CharacterList.Count - 1);
                                    }
                                }
                            }
                        }
                    }
                    // メインとサブの一番大きい人の間で平均をとる
                    _isMainAndSubAverage = EditorGUILayout.Toggle("メインとサブの一番大きい人の間で平均", _isMainAndSubAverage);

                    // 最も背の高い人
                    _isHighestCharacter = EditorGUILayout.Toggle("最も背の高い人に合わせる", _isHighestCharacter);

                    // TODO:@kanda_kento: カメラモーションの性格値分岐は使用していないらしいので、余裕があるときに削除する
                    _isBlendPersonalityMotion = EditorGUILayout.Toggle("性格別モーションのブレンド", _isBlendPersonalityMotion);
                }
#endif // UNITY_EDITOR
            }
        }
    }
}