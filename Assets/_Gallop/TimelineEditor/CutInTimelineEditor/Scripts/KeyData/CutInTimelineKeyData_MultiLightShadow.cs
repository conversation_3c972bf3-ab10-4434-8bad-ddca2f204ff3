using UnityEngine;
using System.Collections;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif//UNITY_EDITOR

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {

            [System.Serializable]
            public class TimelineKeyMultiLightShadowDataList : TimelineKeyDataListTemplate<TimelineKeyMultiLightShadowData> { };

            //-----------------------------------------------------------------------------
            //    Camera Motion Key
            //-----------------------------------------------------------------------------
            [System.Serializable]
            public class TimelineKeyMultiLightShadowData : TimelineKeyWithInterpolate
            {
                public override CutInTimelineKeyDataType dataType { get { return CutInTimelineKeyDataType.MultiLightShadow; } }

                public bool IsUseParam = false;
                public Vector3 ShadowCenterPosition = Math.VECTOR3_ZERO;
                public Vector3 ShadowForward = Math.VECTOR3_ZERO;
                public float ShadowFadeStart = 100f;
                public float ShadowFadeLength = 100f;
                public bool IsUseShadowFront = false;
                public bool IsShadowNearStart = true;
                public Color ShadowStartColor = Color.black;
                public Color ShadowEndColor = Color.black;
                public float ShadowDistance = GameDefine.DEFAULT_SHADOW_DISTANCE;
                public Color FillColor = GameDefine.COLOR_WHITE;

#if UNITY_EDITOR

                /// <summary>
                /// 要素コピー
                /// </summary>
                /// <param name="dest"></param>
                public override void Copy(CutInTimelineKey dest)
                {
                    base.Copy(dest);
                    var d = dest as TimelineKeyMultiLightShadowData;
                    d.IsUseParam = IsUseParam;
                    d.ShadowCenterPosition = ShadowCenterPosition;
                    d.ShadowForward = ShadowForward;
                    d.ShadowFadeStart = ShadowFadeStart;
                    d.ShadowFadeLength = ShadowFadeLength;
                    d.IsUseShadowFront = IsUseShadowFront;
                    d.IsShadowNearStart = IsShadowNearStart;
                    d.ShadowStartColor = ShadowStartColor;
                    d.ShadowEndColor = ShadowEndColor;
                    d.ShadowDistance = ShadowDistance;
                    d.FillColor = FillColor;
                }
                /// <summary>
                /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
                /// </summary>
                public override CutInTimelineKey InterpolateForEdit(
                    CutInTimelineKey interpKey,
                    CutInTimelineKey prevKey,
                    float t
                    )
                {
                    TimelineKeyMultiLightShadowData outData = interpKey as TimelineKeyMultiLightShadowData;
                    TimelineKeyMultiLightShadowData prevData = prevKey as TimelineKeyMultiLightShadowData;
                    outData.ShadowCenterPosition = Vector3.Lerp(prevData.ShadowCenterPosition, ShadowCenterPosition, t);
                    outData.ShadowForward = Vector3.Lerp(prevData.ShadowForward, ShadowForward, t);
                    outData.ShadowFadeStart = Mathf.Lerp(prevData.ShadowFadeStart, ShadowFadeStart, t);
                    outData.ShadowFadeLength = Mathf.Lerp(prevData.ShadowFadeLength, ShadowFadeLength, t);
                    outData.ShadowStartColor = Color.Lerp(prevData.ShadowStartColor, ShadowStartColor, t);
                    outData.ShadowEndColor = Color.Lerp(prevData.ShadowEndColor, ShadowEndColor, t);
                    outData.ShadowDistance = Mathf.Lerp(prevData.ShadowDistance, ShadowDistance, t);
                    outData.FillColor = Color.Lerp(prevData.FillColor, FillColor, t);

                    // 以下補間しない。
                    outData.IsUseParam = IsUseParam;
                    outData.IsUseShadowFront = IsUseShadowFront;
                    outData.IsShadowNearStart = IsShadowNearStart;
                    return outData;
                }


                /// <summary>
                ///  エディタGUI
                /// </summary>
                /// <param name="ctx"></param>
                /// <param name="timelineController/param>
                public override void OnGUI(Cutt.GUIContext ctx, CutInTimelineController timelineController)
                {
                    base.OnGUI(ctx, timelineController);
                    if (ctx.ME_Toggle("IsUseParam", ref IsUseParam))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.IsUseParam = IsUseParam);
                    }
                    if (!IsUseParam)
                    {// パラメータ使わないならここまで。
                        return;
                    }
                    if (ctx.ME_RoundVector3Field("影の中心", ref ShadowCenterPosition, Math.VECTOR3_ZERO))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowCenterPosition = ShadowCenterPosition);
                    }
                    if (ctx.ME_RoundVector3Field("影の方向", ref ShadowForward, Math.VECTOR3_FORWARD))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowForward = ShadowForward);
                    }
                    if (ctx.ME_FloatField("FadeStart", ref ShadowFadeStart))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowFadeStart = ShadowFadeStart);
                    }
                    if (ctx.ME_FloatField("FadeLength", ref ShadowFadeLength))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowFadeLength = ShadowFadeLength);
                    }
                    if (ctx.ME_Toggle("方向を使う", ref IsUseShadowFront))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.IsUseShadowFront = IsUseShadowFront);
                    }
                    if (ctx.ME_Toggle("範囲内に影を描く", ref IsShadowNearStart))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.IsShadowNearStart = IsShadowNearStart);
                    }
                    if (ctx.ME_ColorField("StartColor", ref ShadowStartColor))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowStartColor = ShadowStartColor);
                    }
                    if (ctx.ME_ColorField("EndColor", ref ShadowEndColor))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowEndColor = ShadowEndColor);
                    }
                    if(ctx.ME_FloatField("ShadowDistance",ref ShadowDistance))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.ShadowDistance = ShadowDistance);
                    }
                    if (ctx.ME_ColorField("FillColor", ref FillColor))
                    {
                        ctx.MultiKeyEditApply<TimelineKeyMultiLightShadowData>(x => x.FillColor = FillColor);
                    }
                }
#endif//UNITY_EDITOR
            }

        }//namespace Cutt
    }
}
