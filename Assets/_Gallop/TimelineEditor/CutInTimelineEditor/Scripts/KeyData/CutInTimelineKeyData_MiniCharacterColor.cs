using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif//UNITY_EDITOR

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// キャラカラー
            /// </summary>
            [System.Serializable]
            public class TimelineKeyMiniCharacterColorDataList : TimelineKeyDataListTemplate<TimelineKeyMiniCharacterColorData>
            {
                public CoursePostFilmSetGroup.TimeData TimeData;

#if UNITY_EDITOR
                /// <summary>
                /// ctor
                /// </summary>
                public TimelineKeyMiniCharacterColorDataList()
                {
                    InitializeTimelineData(ref TimeData);
                }

                /// <summary>
                ///  エディタGUI
                /// </summary>
                public override void OnGUI(Cutt.GUIContext ctx)
                {
                    base.OnGUI(ctx);

                    var oldTimeData = TimeData;    //structなのでコピーを取る
                    OnGUI_TimeData(ctx, ref TimeData);
                }
#endif
            };

            [System.Serializable]
            public class TimelineKeyMiniCharacterColorData : TimelineKeyWithInterpolate
            {
                // 定数
                // キャラカラーデフォルト
                public static readonly Color DEFAULT_CHARACOLOR = GameDefine.COLOR_WHITE;
                public const float DEFAULT_COLORPOWER = 1.0f;

                // アウトラインデフォルト
                public const float DEFAULT_OUTLINEWIDTHPOWER = ModelController.DEFAULT_OUTLINE_WIDTH_POWER;
                public static readonly Color DEFAULT_OUTLINECOLOR = StaticVariableDefine.CG3D.ModelController.DEFAULT_OUTLINE_COLOR_EDIT;
                public static readonly ModelController.OutlineColorBlend DEFAULT_OUTLINECOLORBLEND = StaticVariableDefine.CG3D.ModelController.DEFAULT_OUTLINE_COLOR_BLEND;

                // 変数
                #region CharaColor

                // キャラカラー
                public KeyColor CharaColor = new KeyColor( "CharaColor", DEFAULT_CHARACOLOR );
                public KeyFloat ColorPower = new KeyFloat( "ColorPower", DEFAULT_COLORPOWER );

                // アウトライン
                public KeyFloat OutlineWidthPower = new KeyFloat("OutlineWidthPower", DEFAULT_OUTLINEWIDTHPOWER);
                public KeyColor OutlineColor = new KeyColor("OutlineColor", DEFAULT_OUTLINECOLOR);
                public KeyEnum OutlineColorBlendType = new KeyEnum("OutlineColorBlend", DEFAULT_OUTLINECOLORBLEND);

                #endregion CharaColor

                // 関数
                public override CutInTimelineKeyDataType dataType { get { return CutInTimelineKeyDataType.MiniCharacterColor; } }

                /// <summary>
                /// Default値を考慮したカラーデータを生成する
                /// </summary>
                /// <returns></returns>
                public void CreateDataDefault( ref TimelineKeyMiniCharacterColorData keyData )
                {
                    SetValue(CharaColor, keyData.CharaColor, DEFAULT_CHARACOLOR);
                    SetValue(ColorPower, keyData.ColorPower, DEFAULT_COLORPOWER);
                    SetValue(OutlineWidthPower, keyData.OutlineWidthPower, DEFAULT_OUTLINEWIDTHPOWER);
                    SetValue(OutlineColor, keyData.OutlineColor, DEFAULT_OUTLINECOLOR);
                    SetValue(OutlineColorBlendType, keyData.OutlineColorBlendType, DEFAULT_OUTLINECOLORBLEND);
                }

                /// <summary>
                /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
                /// </summary>
                public void Interpolate( TimelineKeyMiniCharacterColorData prevKey, ref TimelineKeyMiniCharacterColorData outKey, float t )
                {
                    outKey.Interpolate(prevKey, this, t);
                }

                /// <summary>
                /// 補間キー
                /// </summary>
                public void Interpolate(TimelineKeyMiniCharacterColorData src1, TimelineKeyMiniCharacterColorData src2, float t)
                {
                    CharaColor.InterpolateFrom(src1.CharaColor, src2.CharaColor, t, DEFAULT_CHARACOLOR);
                    ColorPower.InterpolateFrom(src1.ColorPower, src2.ColorPower, t, DEFAULT_COLORPOWER);
                    OutlineWidthPower.InterpolateFrom(src1.OutlineWidthPower, src2.OutlineWidthPower, t, DEFAULT_OUTLINEWIDTHPOWER);
                    OutlineColor.InterpolateFrom(src1.OutlineColor, src2.OutlineColor, t, DEFAULT_OUTLINECOLOR);
                    OutlineColorBlendType.IsValid = src2.OutlineColorBlendType.IsValid;
                    OutlineColorBlendType.Value = src2.OutlineColorBlendType.IsValid ? src2.OutlineColorBlendType.Value : DEFAULT_OUTLINECOLORBLEND;
                }

                /// <summary>
                /// パラメータコピー
                /// </summary>
                public void CopyParam(TimelineKeyMiniCharacterColorData dest)
                {
                    dest.CharaColor.IsValid = CharaColor.IsValid;
                    dest.CharaColor.Value = CharaColor.Value;
                    dest.ColorPower.IsValid = ColorPower.IsValid;
                    dest.ColorPower.Value = ColorPower.Value;
                    dest.OutlineWidthPower.IsValid = OutlineWidthPower.IsValid;
                    dest.OutlineWidthPower.Value = OutlineWidthPower.Value;
                    dest.OutlineColor.IsValid = OutlineColor.IsValid;
                    dest.OutlineColor.Value = OutlineColor.Value;
                    dest.OutlineColorBlendType.IsValid = OutlineColorBlendType.IsValid;
                    dest.OutlineColorBlendType.Value = OutlineColorBlendType.Value;
                }

#if UNITY_EDITOR

                /// <summary>
                /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
                /// </summary>
                public override CutInTimelineKey InterpolateForEdit(
                    CutInTimelineKey outKey,
                    CutInTimelineKey prevKey,
                    float t
                    )
                {
                    var outData = outKey as TimelineKeyMiniCharacterColorData;
                    var prevData = prevKey as TimelineKeyMiniCharacterColorData;
                    Interpolate(prevData, ref outData, t);
                    return outData;
                }

                /// <summary>
                /// 要素コピー
                /// </summary>
                public override void Copy(CutInTimelineKey dest)
                {
                    base.Copy(dest);
                    var d = dest as TimelineKeyMiniCharacterColorData;
                    CopyParam(d);
                }

                /// <summary>
                ///  エディタGUI
                /// </summary>
                public override void OnGUI(Cutt.GUIContext ctx, CutInTimelineController timelineController)
                {
                    base.OnGUI(ctx, timelineController);
                    OnInspectorGUI();

                    EditorGUILayout.Space();
                    EditorGUILayout.Space();
                    EditorGUILayout.Space();
                    EditorGUILayout.Space();
                    EditorGUILayout.Space();
                }

                /// <summary>
                /// インスペクタ
                /// </summary>
                public void OnInspectorGUI()
                {
                    CharaColor.OnGui();
                    ColorPower.OnGui();

                    OutlineWidthPower.OnGui();
                    OutlineColor.OnGui();
                    OutlineColorBlendType.OnGui();
                }

                /// <summary>
                /// 全要素有効化
                /// </summary>
                public void SetAllParamValid()
                {
                    CharaColor.IsValid = true;
                    ColorPower.IsValid = true;
                    OutlineWidthPower.IsValid = true;
                    OutlineColor.IsValid = true;
                    OutlineColorBlendType.IsValid = true;
                }
#endif//UNITY_EDITOR
            }

        }//namespace Cutt
    }
}
