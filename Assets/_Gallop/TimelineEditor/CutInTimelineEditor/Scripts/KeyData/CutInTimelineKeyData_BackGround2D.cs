using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using System.Linq;
#endif

namespace Gallop
{
    namespace CutIn.Cutt
    {
        using static CutInTimelineController;

        /// <summary>
        /// 2D背景TimelineGroup
        /// </summary>
        [System.Serializable]
        public class TimelineBackGround2D : ITimelineGroupData
        {
#if UNITY_EDITOR
            public const string GROUP_NAME = "2D背景";
#endif

            public TimelineKeyBackGround2DList DataList = new TimelineKeyBackGround2DList();
            public ITimelineKeyDataList Keys => DataList;
            public bool IsExistKey(CutInTimelineKey target) => DataList.IsExistKey(target);

            /// <summary>
            /// 更新処理
            /// </summary>
            public void AlterUpdate(CutInTimelineController controller, int currentFrame)
            {
                if (DataList.IsNotPlayable(controller))
                {
                    return;
                }

                DataList.AlterUpdate(controller, currentFrame);
            }

            /// <summary>
            /// 後片付け
            /// </summary>
            public void Cleanup()
            {
                DataList.Cleanup();
            }
        }

        /// <summary>
        /// 2D背景TimelineKeyList
        /// </summary>
        [System.Serializable]
        public class TimelineKeyBackGround2DList : TimelineKeyDataListTemplate<TimelineKeyBackGround2D>
        {
            /// <summary>
            /// 背景の位置や向きを管理するためのプレハブ
            /// </summary>
            public GameObject Prefab;

            #region 保存しない変数

            public int BgId { get; set; }

            /// <summary> 背景X軸座標調整を行う（CSVデータ参照するかどうかのフラグ） </summary>
            public bool IsBgPositionCustom { get; set; }

            /// <summary> 調整する背景の座標値 </summary>
            public float BgPosX { get; set; }

            //#115100 背景に紐づける環境音再生はカット終了時自動的に止めるかを指定するフラグ
            public bool IsStopEnvSEOnEndCutIn { get; set; } = true;

            //#120801 背景に紐づける環境音以外のものを指定するためのパラメータ
            public string EnvCueName { get; set; } = string.Empty;
            public string EnvCueSheetName { get; set; } = string.Empty;

            private readonly CutInTimelineBg2DManager _manager = new CutInTimelineBg2DManager();

            #endregion 保存しない変数

            #region 更新処理
            /// <summary>
            /// 更新処理
            /// </summary>
            public void AlterUpdate(CutInTimelineController controller, int currentFrame)
            {
                FindTimelineKey(out var curKey, out var nextKey, this, currentFrame, controller.availableFindKeyCache);
                if (!(curKey is TimelineKeyBackGround2D curData))
                {
                    return;
                }

                if (nextKey is TimelineKeyBackGround2D nextData && nextData.IsInterpolateKey())
                {
                    // 補間処理
                    float t = CalculateInterpolationValue(curData, nextData, currentFrame);

                    _manager.Position = LerpWithoutClamp(curData._pos, nextData._pos, t);
                    _manager.Rotation = LerpWithoutClamp(curData._rot, nextData._rot, t);
                    _manager.Scale = LerpWithoutClamp(curData._scale, nextData._scale, t);
                }
                else
                {
                    // CurrentKeyの値に設定
#if UNITY_EDITOR && CYG_DEBUG
                    if (_editorChangeBgPosX)
                    {
                        //プランナーさんが手動調整した背景座標を適用
                        var pos = curData._pos;
                        pos.x = _editorBgPosX;
                        _manager.Position = pos;
                    }
                    else
#endif
                    {
                        //うまさんぽカット改修で背景にX軸の調整が入る場合
                        //CSVから指定された座標値を適用（現状は補間が要らないと思うが、要望出てきた場合対応が必要）
                        var curPos = curData._pos;
                        if(IsBgPositionCustom)
                        {
                            curPos.x = BgPosX;
                        }

                        _manager.Position = curPos;
                    }
                    _manager.Rotation = curData._rot;
                    _manager.Scale = curData._scale;
                }

                // 座標変更や画像更新
                _manager.AlterUpdate(controller, Prefab, BgId, curData, IsStopEnvSEOnEndCutIn, EnvCueName, EnvCueSheetName);
            }
            #endregion 更新処理

            #region 後片付け
            /// <summary>
            /// 後片付け
            /// </summary>
            public void Cleanup()
            {
                _manager.Cleanup();
            }
            #endregion 後片付け

            #region エディタ用
#if CYG_DEBUG && UNITY_EDITOR
            private Texture2D _previewTexture;

            /// <summary>
            /// エディタ上で背景X軸座標調整するフラグ
            /// </summary>
            private bool _editorChangeBgPosX = false;
            /// <summary>
            /// エディタ上で調整する背景X軸座標
            /// </summary>
            private float _editorBgPosX = 0f;
            /// <summary>
            /// エディタ上で適用するLocationデータId
            /// </summary>
            private int _editorChangeBgPosXLocationId = 0;
            /// <summary>
            /// エディタ上で適用するLocationデータId（テキスト）
            /// </summary>
            private string _editorChangeBgPosXLocationIdStr = "";
            /// <summary>
            /// エディタ上で利用中のLocationデータ
            /// </summary>
            private MasterCampaignWalkingLocation.CampaignWalkingLocation _editorCampaignWalkingLocation = null;

            /// <summary>
            ///  エディタGUI
            /// </summary>
            public override void OnGUI(GUIContext ctx)
            {
                base.OnGUI(ctx);

                Prefab = EditorGUILayout.ObjectField("◆板ポリPrefab", Prefab, typeof(GameObject), false) as GameObject;

                EditorGUILayout.LabelField("◆BgId", BgId.ToString());
                EditorGUI.BeginChangeCheck();
                _previewTexture = EditorGUILayout.ObjectField("◆背景プレビュー", _previewTexture, typeof(Texture2D), false) as Texture2D;
                if (EditorGUI.EndChangeCheck())
                {
                    OnChangePreviewTexture(ctx._tool.TimelineController);
                }

                //キャンペーンカットの場合、プランナーさん側で背景X軸調整用の機能表示
                //設定値はアセットに保存しない
                if (CutInCutTool.instance.selectedCutInEditorPrefab == CutInCutTool.SelectedCutInEditorPrefab.Campaign)
                {
                    DrawOptionChangeBackgroundPositionX(ctx);
                }
            }

            /// <summary>
            /// プレビュー画像を変更した時の処理
            /// </summary>
            private void OnChangePreviewTexture(CutInTimelineController controller)
            {
                if (_previewTexture == null) return;

                // 背景の名前からIDを特定する
                var path = AssetDatabase.GetAssetPath(_previewTexture);
                var bgName = System.IO.Path.GetFileNameWithoutExtension(path);
                if (!TryGetBgId(bgName, out int mainBgId, out int subBgId))
                {
                    return;
                }

                // マスターデータに存在する背景ならIDと画像を差し替える
                var data = MasterDataManager.Instance.masterBackgroundData.GetWithBgIdAndBgSub(mainBgId, subBgId);
                if (data == null) return;
                BgId = data.Id;
                _manager.OnChangePreviewTexture(data, _previewTexture, controller);
            }

            /// <summary>
            /// 背景ファイル名からBgIdとBgSubIdを取得する
            /// </summary>
            private bool TryGetBgId(string bgName, out int mainBgId, out int subBgId)
            {
                // Bgフォルダ以外の画像を設定された場合にint.Parseが失敗するので例外をキャッチしておく
                try
                {
                    mainBgId = StoryBgUtil.GetMainBgId(bgName);
                    subBgId = StoryBgUtil.GetSubBgId(bgName);
                    return true;
                }
                catch
                {
                    EditorUtility.DisplayDialog("エラー", "Bundle/Resources/Bg\nから選択して下さい", "OK");
                    mainBgId = 0;
                    subBgId = 0;
                    return false;
                }
            }

            /// <summary>
            /// #101816　背景表示座標のｘ軸を調整する機能、この機能で設定したX座標はアセットに適用しないもので、
            /// 主にプランナー側で設定を調整するための機能です
            /// </summary>
            /// <param name="ctx"></param>
            private void DrawOptionChangeBackgroundPositionX(GUIContext ctx)
            {
                foreach (var key in thisList)
                {
                    key.IsEditorChangeBgPosX = false;
                }

                if (_manager.BGMeshRender != null)
                {
                    _editorChangeBgPosX = EditorGUILayout.Toggle("◆Editor背景X軸座標調整", _editorChangeBgPosX);

                    var motionCamera = ctx._tool.TimelineController.MotionCamera.targetCamera;

                    var bgMeshBounds = _manager.BGMeshRender.bounds;
                    var bgMeshBoundMin = bgMeshBounds.min;
                    var bgMeshBoundMax = bgMeshBounds.max;

                    if (_editorChangeBgPosX)
                    {
                        foreach (var key in thisList)
                        {
                            key.IsEditorChangeBgPosX = true;
                        }

                        EditorGUI.BeginChangeCheck();
                        _editorChangeBgPosXLocationIdStr = EditorGUILayout.TextField("CSV Location Id:", _editorChangeBgPosXLocationIdStr);
                        if (EditorGUI.EndChangeCheck())
                        {
                            if (int.TryParse(_editorChangeBgPosXLocationIdStr, out var locationDataId))
                            {
                                _editorChangeBgPosXLocationId = locationDataId;
                                _editorCampaignWalkingLocation = MasterDataManager.Instance.masterCampaignWalkingLocation.GetList().FirstOrDefault(data => data.Id == _editorChangeBgPosXLocationId);
                            }
                            else
                            {
                                _editorCampaignWalkingLocation = null;
                                _editorChangeBgPosXLocationId = 0;
                            }
                        }
                        if (_editorCampaignWalkingLocation != null)
                        {
                            GUILayout.Label($"localtion Id: {_editorCampaignWalkingLocation.Id}; BgPositionCustom: {_editorCampaignWalkingLocation.BgPositionCustom}; BgPositionX: {_editorCampaignWalkingLocation.BgPositionX}");
                            if (GUILayout.Button("CSVに設定されているX軸座標を適用"))
                            {
                                if (_editorCampaignWalkingLocation != null)
                                {
                                    _editorBgPosX = _editorCampaignWalkingLocation.GetBackGroundPositionX();
                                }
                                else
                                {
                                    Debug.LogWarning($"背景座標設定が失敗しました、locationDataId == {_editorChangeBgPosXLocationId}のマスターデータを確認してください！");
                                }
                            }
                        }

                        _editorBgPosX = EditorGUILayout.FloatField("X座標", _editorBgPosX);
                        {
                            //bgMeshViewMinPos.x > 0なら画面の左側に見切れが生じていない
                            var bgMeshViewMinPos = motionCamera.WorldToViewportPoint(bgMeshBoundMin);
                            //bgMeshViewMaxPos.x < 1 なら画面の右側に見切れが生じていない
                            var bgMeshViewMaxPos = motionCamera.WorldToViewportPoint(bgMeshBoundMax);

                            //画面両サイドに見切れが発生しないように上限値超えないように自動補正をかける
                            if (bgMeshViewMinPos.x > 0f || bgMeshViewMaxPos.x < 1f)
                            {
                                if (bgMeshViewMinPos.x > 0f)
                                {
                                    var tmp = motionCamera.ViewportToWorldPoint(new Vector3(0f, bgMeshViewMinPos.y, bgMeshViewMinPos.z));

                                    var distance = bgMeshBoundMin.x - tmp.x;
                                    _editorBgPosX += distance;
                                }
                                else if (bgMeshViewMaxPos.x < 1f)
                                {
                                    var tmp = motionCamera.ViewportToWorldPoint(new Vector3(1f, bgMeshViewMaxPos.y, bgMeshViewMaxPos.z));

                                    var distance = tmp.x - bgMeshBoundMax.x;
                                    _editorBgPosX -= distance;
                                }
                            }
                        }
                    }
                    else
                    {
                        //デフォルト座標を代入
                        var timelineController = ctx._tool.TimelineController;
                        var curKey = FindCurrentKey(timelineController.CurrentFrame);
                        if (curKey.key is TimelineKeyBackGround2D bg2DKey)
                        {
                            _editorBgPosX = bg2DKey._pos.x;
                        }
                    }
                }
            }
#endif
            #endregion エディタ用
        }

        /// <summary>
        /// 2D背景TimelineKey
        /// </summary>
        [System.Serializable]
        public class TimelineKeyBackGround2D : TimelineKeyTransformData
        {
            public override CutInTimelineKeyDataType dataType => CutInTimelineKeyDataType.BackGround2D;

            public bool IsVisible = true;
            public bool UsePosition;
            public bool UseRotation;
            public bool UseScale;
            public bool ApplyImageEffect;
            public bool OverrideEnvParam;
            //#102951キャラのLightProbeColorを小物に適用するフラグ、デフォルトは適用しない
            public bool ApplyCharaLightProbeColorToProp = false;
            public TimelineKeyCharacterColorData CharacterColorData;
            #region Bg_Node
#if UNITY_EDITOR

            /// <summary>BGノードに適用されるデフォルト座標（UsePositionがオフの場合直接適用）</summary>
            private readonly Vector3 BG_NODE_DEFAULT_POS = new Vector3(9f, 38f, -3f);
#endif
            //#103250 背景に回転状態を吸収するノードを適用するフラグ、新規作成されたキーに対して自動的にTrue設定
            public bool IsValidBgNode = false;
            // 背景ノードに適用するデータは外部からの書き込みを避けたいデータなのでここだけprivateで宣言
            [SerializeField]
            private Vector3 _bgBasePos = Vector3.zero;
            public Vector3 BgBasePos => _bgBasePos;
            [SerializeField]
            private Vector3 _bgBaseRot = Vector3.zero;
            public Vector3 BgBaseRot => _bgBaseRot;
            [SerializeField]
            private Vector3 _bgBaseScale = Vector3.one;
            public Vector3 BgBaseScale => _bgBaseScale;
            #endregion

            /// <summary>
            /// コンストラクタ
            /// </summary>
            public TimelineKeyBackGround2D()
            {
                // 個数な数だけ補間パラメータを作成
                _params = new InterpolateParameter[System.Enum.GetNames(typeof(Interpolate)).Length];
                for (int i = 0; i < _params.Length; i++)
                {
                    _params[i] = new InterpolateParameter();
                }
            }

            /// <summary>
            /// データロード時の処理
            /// </summary>
            public override void OnLoad(CutInTimelineController timelineController)
            {
                // 補間パラメータの個数が不足していたら作り直しておく
                if (_params.Length >= INTERPOLATE_NUM) return;
                var newParam = new InterpolateParameter[INTERPOLATE_NUM];
                for (int i = 0; i < INTERPOLATE_NUM; i++)
                {
                    newParam[i] = new InterpolateParameter();
                }
                System.Array.Copy(_params, newParam, _params.Length);
                _params = newParam;
            }

            #region 補間
            /// <summary>
            /// 補間indexが正しいか調べる
            /// </summary>
            public override bool IsValidInterpolate(int index = 0)
            {
                if (index >= 0 && index < System.Enum.GetNames(typeof(Interpolate)).Length)
                {
                    return true;
                }
                Debug.LogError("TimelineKeyWithInterpolates index error");
                return false;
            }

            /// <summary>
            /// 補間プロパティを持つキーでTrueを返すようにOverride
            /// </summary>
            public override bool IsInterpolateKey(int index = 0)
            {
                return (Interpolate)index switch
                {
                    Interpolate.TransformPosition => UsePosition,
                    Interpolate.TransformRotation => UseRotation,
                    Interpolate.TransformScale => UseScale,
                    _ => false
                };
            }
            #endregion 補間

            #region エディタ用
#if UNITY_EDITOR
            /// <summary>
            /// 表示中の背景に紐づいたEnvParamのキャラカラー設定 (編集中に設定を戻せるようにキャッシュする)
            /// </summary>
            public TimelineKeyCharacterColorData EnvParamColorData { get; } = new TimelineKeyCharacterColorData();

            /// <summary>
            /// 要素コピー
            /// </summary>
            public override void Copy(CutInTimelineKey dest)
            {
                base.Copy(dest);
                var d = (TimelineKeyBackGround2D)dest;
                d.IsVisible = IsVisible;
                d.UsePosition = UsePosition;
                d.UseRotation = UseRotation;
                d.UseScale = UseScale;
                d.ApplyImageEffect = ApplyImageEffect;
                d.OverrideEnvParam = OverrideEnvParam;
                d.ApplyCharaLightProbeColorToProp = ApplyCharaLightProbeColorToProp;
                d.CharacterColorData = CopyCharacterColorData();
                d.IsValidBgNode = IsValidBgNode;
                d._bgBasePos = _bgBasePos;
                d._bgBaseRot = _bgBaseRot;
                d._bgBaseScale = _bgBaseScale;
            }

            /// <summary>
            /// キー作成された時のコールバック
            /// </summary>
            protected override void OnCreate()
            {
                base.OnCreate();

                //新規に作成されたキーはノード適用をオンにする
                IsValidBgNode = true;
                //デフォルト座標の設定
                _bgBasePos = BG_NODE_DEFAULT_POS;
            }

            /// <summary>
            /// キャラカラーを要素コピー
            /// </summary>
            private TimelineKeyCharacterColorData CopyCharacterColorData()
            {
                if (CharacterColorData == null) return null;

                var dest = new TimelineKeyCharacterColorData();
                CharacterColorData.CopyParam(dest);
                return dest;
            }

            /// <summary>
            /// プランナーさん側で背景X軸座標調整機能をオンにしているかどうか
            /// </summary>
            [System.NonSerialized]
            public bool IsEditorChangeBgPosX = false;

            /// <summary>
            /// エディタGUI
            /// </summary>
            public override void OnGUI(GUIContext ctx, CutInTimelineController controller)
            {
                OnGUI_Frame(ctx);

                EditorGUILayout.Space();

                ctx.ME_Toggle("表示", ref IsVisible);
                if (!IsVisible)
                {
                    EditorGUILayout.LabelField("環境音のみ反映します");
                    return;
                }

                //プランナー側で座標設定機能をオンにする際に、アセット側に保存できる座標調整機能を無効にする
                if(IsEditorChangeBgPosX)
                {
                    UsePosition = false;
                }

                EditorGUI.BeginDisabledGroup(IsEditorChangeBgPosX);

                if(ctx.ME_Toggle("背景にノードを適用", ref IsValidBgNode))
                {
                    OnCreateBgNodeTransform();
                    ctx.MultiKeyEditApply<TimelineKeyBackGround2D>(x =>
                    {
                        x.IsValidBgNode = IsValidBgNode;
                        OnCreateBgNodeTransform();
                    });
                }

                if (ctx.ME_Toggle("■Position", ref UsePosition))
                {
                    if (!UsePosition) ResetInterpolateSettings();
                    ctx.MultiKeyEditApply<TimelineKeyBackGround2D>(x =>
                    {
                        x.UsePosition = UsePosition;
                        if (!x.UsePosition) x.ResetInterpolateSettings();
                    });
                }
                if (UsePosition)
                {
                    OnGUI_Interpolate(ctx);
                    OnGUI_Position(ctx);
                }
                EditorGUI.EndDisabledGroup();

                if (ctx.ME_Toggle("■Rotation", ref UseRotation))
                {
                    if (!UseRotation) ResetInterpolateSettings((int)Interpolate.TransformRotation);
                    ctx.MultiKeyEditApply<TimelineKeyBackGround2D>(x =>
                    {
                        x.UseRotation = UseRotation;
                        if (!x.UseRotation) x.ResetInterpolateSettings((int)Interpolate.TransformRotation);
                    });
                }
                if (UseRotation)
                {
                    OnGUI_Interpolate(ctx, (int)Interpolate.TransformRotation);
                    OnGUI_Rotation(ctx);
                }

                if (ctx.ME_Toggle("■Scale", ref UseScale))
                {
                    if (!UseScale) ResetInterpolateSettings((int)Interpolate.TransformScale);
                    ctx.MultiKeyEditApply<TimelineKeyBackGround2D>(x =>
                    {
                        x.UseScale = UseScale;
                        if (!x.UseScale) x.ResetInterpolateSettings((int)Interpolate.TransformScale);
                    });
                }
                if (UseScale)
                {
                    OnGUI_Interpolate(ctx, (int)Interpolate.TransformScale);
                    OnGUI_Scale(ctx);
                }

                EditorGUILayout.Space();

                EditorGUILayout.LabelField("■ImageEffect設定");
                ctx.ME_Toggle("背景のEnvを使う", ref ApplyImageEffect);

                EditorGUILayout.Space();

                //#102951対応、小物にキャラのLightProbeColorを参照するかしないかのチェックボックスを追加
                EditorGUILayout.LabelField("■PropColor設定");
                ctx.ME_Toggle("キャラLightProbeColor適用", ref ApplyCharaLightProbeColorToProp);

                EditorGUILayout.Space();

                EditorGUILayout.LabelField("■CharaColor設定");
                ctx.ME_Toggle("背景のEnvを上書き", ref OverrideEnvParam);
                if (OverrideEnvParam)
                {
                    OnGUI_CharacterColorData(ctx, controller);
                }
            }

            /// <summary>
            /// キャラカラーのエディタGUI
            /// </summary>
            private void OnGUI_CharacterColorData(GUIContext ctx, CutInTimelineController controller)
            {
                // まだデータがなければ作成
                CharacterColorData ??= new TimelineKeyCharacterColorData();

                // GUI表示
                EditorGUI.BeginChangeCheck();
                CharacterColorData.OnGUI(ctx, controller, false);
                if (EditorGUI.EndChangeCheck())
                {
                    OnValueChanged_CharacterColorData(controller);
                }
            }

            /// <summary>
            /// キャラカラーの設定が変更された時の処理
            /// </summary>
            private void OnValueChanged_CharacterColorData(CutInTimelineController controller)
            {
                // 背景側のキャラカラー設定をベースにして一部設定をKeyData側のキャラカラーで上書き...という構図なので
                // まずは背景側の設定をコピーしてそれからKeyData側の設定をコピーする
                var colorData = new TimelineKeyCharacterColorData();
                EnvParamColorData.CopyParam(colorData);
                CharacterColorData.CopyParamWithCheck(colorData);

                foreach (var character in controller.CharacterList)
                {
                    GallopCharacterImageEffectParameter.ApplayCharacterColor(colorData, character._cutInCharacter.Model);
                }
            }

            /// <summary>
            /// 背景ノード作成される際にトランスフォーム情報を更新
            /// </summary>
            private void OnCreateBgNodeTransform()
            {
                if (IsValidBgNode)
                {
                    var basePos = BgBasePos;
                    var baseRot = BgBaseRot;
                    var baseScale = BgBaseScale;
                    if (UsePosition)
                    {
                        basePos = _pos;
                    }

                    if (UseRotation)
                    {
                        baseRot = _rot;
                    }

                    if (UseScale)
                    {
                        baseScale = _scale;
                    }

                    _bgBasePos = basePos;
                    _bgBaseRot = baseRot;
                    _bgBaseScale = baseScale;

                    //ノード適用されたらツール側で設定するためのパラメーターをリセット、以降の設定はオフセットとなる
                    _pos = Math.VECTOR3_ZERO;
                    _rot = Math.VECTOR3_ZERO;
                    _scale = Math.VECTOR3_ONE;
                }
            }

            /// <summary>
            /// 背景ノード削除された
            /// </summary>
            /// <param name="BgObject"></param>
            public void OnDeleteBgNode(Transform BgObject)
            {
                if (BgObject != null && !IsValidBgNode)
                {
                    //ノードが外された時ノード座標と角度とスケールを持って背景に適用
                    if (UsePosition)
                    {
                        if (BgObject != null)
                        {
                            _pos = BgObject.localPosition;
                        }
                        else
                        {
                            _pos = BgBasePos;
                        }
                    }

                    if (UseRotation)
                    {
                        if (BgObject != null)
                        {
                            _rot = BgObject.localRotation.eulerAngles;
                        }
                        else
                        {
                            _rot = BgBaseRot;
                        }
                    }

                    if (UseScale)
                    {
                        if (BgObject != null)
                        {
                            _scale = BgObject.localScale;
                        }
                        else
                        {
                            _scale = BgBaseScale;
                        }
                    }
                }
            }
#endif
            #endregion エディタ用
        }
    }
}
