#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// マルチカメラEditTimeline
            /// </summary>
            public class EditTimelineMultiCamera : EditTimeline
            {
                public TimelineMultiCameraGroupData _lineData;

                public override ITimelineGroupDataBase GetGroupData()
                {
                    return _lineData;
                }

                protected EditTimelineMultiCamera() : base()
                {
                }

                public override void OnGUI(GUIContext ctx, EditSheet sheet)
                {
                    base.OnGUI(ctx, sheet);
                }

                /// <summary>
                /// タイムラインプロパティのコピー
                /// </summary>
                /// <param name="srcData"></param>
                public override void CopyProperty(EditTimeline srcData)
                {
                    base.CopyProperty(srcData);
                }

                public void AddAllKey(TimelineMultiCameraGroupData groupData)
                {
                    foreach (var key in _lineData.MotionKeys)
                    {
                        groupData.MotionKeys.Add(key.Clone());
                    }
                    foreach (var key in _lineData.CameraParamKeys)
                    {
                        groupData.CameraParamKeys.Add(key.Clone());
                    }
                    foreach (var key in _lineData.CameraTargetKeys)
                    {
                        groupData.CameraTargetKeys.Add(key.Clone());
                    }
                    foreach (var key in _lineData.MultiCameraEnableKeys)
                    {
                        groupData.MultiCameraEnableKeys.Add(key.Clone());
                    }
                }
            }
        }
    }
}
#endif
