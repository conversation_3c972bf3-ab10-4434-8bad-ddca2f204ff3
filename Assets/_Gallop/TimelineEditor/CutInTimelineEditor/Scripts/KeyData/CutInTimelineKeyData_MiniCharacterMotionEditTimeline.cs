#if UNITY_EDITOR
using UnityEditor;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// ミニキャラクタモーションEditTimeline
            /// </summary>
            public class EditTimelineMiniCharacterMotion : EditTimeline
            {
                public TimelineMiniCharacterGroupData _lineData;

                public override ITimelineGroupDataBase GetGroupData()
                {
                    return _lineData;
                }
                protected EditTimelineMiniCharacterMotion() : base()
                {
                }

                public override void OnGUI(GUIContext ctx, EditSheet sheet)
                {
                    base.OnGUI(ctx, sheet);
                    _lineData.CharacterMotionName = EditorGUILayout.TextField("Name", _lineData.CharacterMotionName);
                    name = _lineData.CharacterMotionName;
                }

                /// <summary>
                /// タイムラインプロパティのコピー
                /// </summary>
                public override void CopyProperty(EditTimeline srcData)
                {
                    base.CopyProperty(srcData);
                    EditTimelineMiniCharacterMotion src = srcData as EditTimelineMiniCharacterMotion;
                    _lineData.CharacterMotionName = src._lineData.CharacterMotionName;
                    name = src.name;
                }
            }
        }
    }
}
#endif
