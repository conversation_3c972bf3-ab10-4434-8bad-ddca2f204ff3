using UnityEngine;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// CameraLayerの表示制御をまとめる
            /// </summary>
            public partial class CutInTimelineController
            {
                // ポジション補正
                // refで用いるので変数とゲッターを分ける
                private Vector3 _cameraLayerPositionOffset = Math.VECTOR3_ZERO;
                public Vector3 CameraLayerPositionOffset => _cameraLayerPositionOffset;
                // FOV補正
                public float CameraLayerFovOffset { get; private set; } = 0.0f;
                public float CameraTargetCharaHeight { get; private set; } = CutDefine.BASE_CHARA_HEIGHT;

                /// <summary>
                /// カレントのCameraLayerキーからターゲットとなるキャラの身長を取得する.
                /// </summary>
                private void AlterUpdate_CameraLayer(CutInTimelineWorkSheet sheet)
                {
                    CameraTargetCharaHeight = CutDefine.BASE_CHARA_HEIGHT;

                    if (sheet.CameraLayerKeys.IsNotPlayable(this))
                    {
                        return;
                    }

                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    FindTimelineKey(out curKey, out nextKey, sheet.CameraLayerKeys, _currentFrame, availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }

                    var curData = curKey as TimelineKeyCameraLayerData;
                    if(!curData.IsValid())
                    {
                        return;
                    }

                    CameraTargetCharaHeight = GetHeightAvgWithCharacters(curData.TargetCharaIds);
                }

                /// <summary>
                /// 対象の身長からカメラにオフセットを加える
                /// </summary>
                private void AlterLateUpdate_CameraLayer(CutInTimelineWorkSheet sheet)
                {
                    if (sheet.CameraLayerKeys.IsNotPlayable(this))
                    {
                        return;
                    }

                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    FindTimelineKey(out curKey, out nextKey, sheet.CameraLayerKeys, _currentFrame, availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }

                    var positionOffsetMin = Math.VECTOR3_ZERO;
                    var positionOffsetMax = Math.VECTOR3_ZERO;
                    var fovOffsetMin = 0.0f;
                    var fovOffsetMax = 0.0f;

                    var curData = curKey as TimelineKeyCameraLayerData;
                    var nextData = nextKey as TimelineKeyCameraLayerData;
                    if ((nextData != null) && nextData.IsInterpolateKey())
                    {
                        //補間処理
                        var t = CalculateInterpolationValue(curData, nextData, _currentFrame);
                        positionOffsetMax = LerpWithoutClamp(curData.OffsetMaxPosition, nextData.OffsetMaxPosition,  t);
                        positionOffsetMin = LerpWithoutClamp(curData.OffsetMinPosition, nextData.OffsetMinPosition,  t);
                        fovOffsetMax = LerpWithoutClamp(curData.OffsetMaxFov, nextData.OffsetMaxFov,  t);
                        fovOffsetMin = LerpWithoutClamp(curData.OffsetMinFov, nextData.OffsetMinFov,  t);
                    }
                    else
                    {
                        positionOffsetMax = curData.OffsetMaxPosition;
                        positionOffsetMin = curData.OffsetMinPosition;
                        fovOffsetMax = curData.OffsetMaxFov;
                        fovOffsetMin = curData.OffsetMinFov;
                    }

                    var positionOffsetDiff = positionOffsetMax - positionOffsetMin;
                    var fovDiff = fovOffsetMax - fovOffsetMin;

                    _cameraLayerPositionOffset = Math.VECTOR3_ZERO;
                    var height = GetHeightAvgWithCharacters(curData.TargetCharaIds);
                    if ( height > 0f)
                    {
                        var rate = (height - CutDefine.BASE_CHARA_HEIGHT_MIN) / CutDefine.BASE_CHARA_HEIGHT_DIFF;
                        _cameraLayerPositionOffset = positionOffsetMin + (positionOffsetDiff * rate);
                        CameraLayerFovOffset = fovOffsetMin + (fovDiff * rate);
                    }

                    if (MotionCamera != null)
                    {
                        if (!Math.IsFloatEqualLight(_cameraLayerPositionOffset.x, 0.0f) ||
                            !Math.IsFloatEqualLight(_cameraLayerPositionOffset.y, 0.0f) ||
                            !Math.IsFloatEqualLight(_cameraLayerPositionOffset.z, 0.0f))
                        {
                            // ポジションのオフセットがあれば補正
                            var transform = MotionCamera.MyTransform;
                            transform.position += (transform.rotation * _cameraLayerPositionOffset);
                        }
                        if (!Math.IsFloatEqualLight(CameraLayerFovOffset, 0.0f))
                        {
                            // FOVのオフセットがあれば補正
                            MotionCamera.targetCamera.fieldOfView += CameraLayerFovOffset;
                        }
                    }
                }

                // 対象キャラ達の平均身長を求める
                public float GetHeightAvgWithCharacters(int[] charaIds)
                {
                    if (charaIds == null || charaIds.Length == 0)
                    {
                        return 0.0f;
                    }

                    float avg = 0.0f;
                    int validIdNum = 0;
                    var charaList = GetCharacters();
                    for (var i = 0; i < charaIds.Length; ++i)
                    {
                        if (charaIds[i] >= charaList.Count)
                        {
                            // インデックス外キャラが指定されている
                            continue;
                        }
                        if (Math.IsFloatEqualLight(charaList[charaIds[i]].Height,0.0f))
                        {
                            // 高さが設定されていなければ除外
                            continue;
                        }
                        avg += charaList[charaIds[i]].Height;
                        validIdNum++;
                    }
                    if (validIdNum > 1) // check zero divide
                    {
                        avg /= (float)validIdNum;
                    }
                    return avg;
                }
            }
        }
    }
}
