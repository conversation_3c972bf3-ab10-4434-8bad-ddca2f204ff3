#if UNITY_EDITOR && CYG_DEBUG
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    public class SingleModeRushInCutInTimelineEditor : CutInTimelineEditor
    {
        private SingleModeRushInCutInHelper _cutInHelper = null;

        public SingleModeRushInCutInHelper CutInHelper => _cutInHelper;

        protected override void InitializeTimeline()
        {
            CutInModelController.DefaultClothCategory = CySpringDataContainer.Category.Story;

            base.InitializeTimeline();

            if (_cutInHelper != null)
            {
                _cutInHelper.CleanupPlaying();
                _cutInHelper = null;
            }

            _cutInHelper = new SingleModeRushInCutInHelper();
            _cutInHelper.InitializeEditor(_timelineController);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (_cutInHelper != null)
            {
                _cutInHelper.FinalizeEditor();
                _cutInHelper = null;
            }
        }
    }
}

#endif