using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// モデルを反転する処理をまとめたクラス
    /// </summary>
    public class ModelMirrorController
    {
        /// <summary>
        /// Transform情報
        /// </summary>
        public class TransformInfo
        {
            private readonly Vector3 _defaultPosition;
            public Vector3 DefaultPosition
            {
                get { return _defaultPosition; }
            }
            private readonly Quaternion _defaultRotation;
            private readonly Vector3 _defaultScale;
            public Vector3 DefaultScale
            {
                get { return _defaultScale; }
            }

            public Transform _transform;

            public bool _isValidPositionAnimation;

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="transform"></param>
            /// <param name="path"></param>
            public TransformInfo(Transform transform, string path = "")
            {
                if (string.IsNullOrEmpty(path))
                {
                    _transform = transform;
                }
                else
                {
                    _transform = transform.Find(path);
                    if (_transform == null)
                    {
                        Debug.LogError("Not Found Transform! : " + path);
                        return;
                    }
                }
                _defaultPosition = _transform.localPosition;
                _defaultRotation = _transform.localRotation;
                _defaultScale = _transform.localScale;
            }

            public void Reset()
            {
                if (_transform == null)
                {
                    return;
                }

                _transform.SetLocalPositionAndRotation(_defaultPosition, _defaultRotation);
                _transform.localScale = _defaultScale;
            }
        }

        /// <summary>
        /// ノード単位で入れ替えるパターンの反転を検証
        /// </summary>
        public class SwapTransformPair
        {
            public TransformInfo _first;
            public TransformInfo _second;
            public Vector4 _rotationScale = Vector4.one;
            public Vector3 _positionScale = Math.VECTOR3_ONE;
            public Vector3 _scaleScale = Math.VECTOR3_ONE;

            /// <summary>
            /// コンストラクタ　Findでたどる版
            /// </summary>
            /// <param name="transform"></param>
            /// <param name="firstPath"></param>
            /// <param name="secondPath"></param>
            public SwapTransformPair(
                Transform transform,
                string firstPath,
                string secondPath)
            {
                _first = new TransformInfo(transform, firstPath);
                _second = new TransformInfo(transform, secondPath);
            }
            /// <summary>
            /// コンストラクタ　Transform直指定番
            /// </summary>
            /// <param name="transform"></param>
            /// <param name="firstPath"></param>
            /// <param name="secondPath"></param>
            public SwapTransformPair(
                Transform firstTransform,
                Transform secondTransform)
            {
                _first = new TransformInfo(firstTransform);
                _second = new TransformInfo(secondTransform);
            }

            public void Swap()
            {
                var firstLocalPosition = _first._transform.localPosition;
                var firstLocalRotation = _first._transform.localRotation;
                var firstScale = _first._transform.localScale;

                var secondLocalPosition = _second._transform.localPosition;
                var secondLocalRotation = _second._transform.localRotation;
                var secondScale = _second._transform.localScale;

                firstLocalPosition.x *= _positionScale.x;
                secondLocalPosition.x *= _positionScale.x;

                firstLocalPosition.y *= _positionScale.y;
                secondLocalPosition.y *= _positionScale.y;

                firstScale.x *= _scaleScale.x;
                firstScale.y *= _scaleScale.y;
                firstScale.z *= _scaleScale.z;
                secondScale.x *= _scaleScale.x;
                secondScale.y *= _scaleScale.y;
                secondScale.z *= _scaleScale.z;

                firstLocalRotation.x *= _rotationScale.x;
                firstLocalRotation.y *= _rotationScale.y;
                firstLocalRotation.z *= _rotationScale.z;
                firstLocalRotation.w *= _rotationScale.w;

                secondLocalRotation.x *= _rotationScale.x;
                secondLocalRotation.y *= _rotationScale.y;
                secondLocalRotation.z *= _rotationScale.z;
                secondLocalRotation.w *= _rotationScale.w;

                _first._transform.SetLocalPositionAndRotation(secondLocalPosition, secondLocalRotation);
                _first._transform.localScale = secondScale;

                _second._transform.SetLocalPositionAndRotation(firstLocalPosition, firstLocalRotation);
                _second._transform.localScale = firstScale;
            }

            public void Reset()
            {
                _first.Reset();
                _second.Reset();
            }
        }

        private ModelControllerBehaviour _model;
        private Animator _animator;
        private bool _isCompleteSetup;
        public bool IsCompleteSetup
        {
            get { return _isCompleteSetup; }
        }

        public bool IsEnable { get; set; } = true;

        private readonly List<SwapTransformPair> _swapTransformPairs = new List<SwapTransformPair>(32);
        private readonly List<SwapTransformPair> _swapSkirtTransformPairs = new List<SwapTransformPair>(16);
        private readonly List<TransformInfo> _reverseTransforms = new List<TransformInfo>(32);
        private readonly List<TransformInfo> _reverseSkirtTransforms = new List<TransformInfo>(4);
        //視線の反転
        private TransformInfo _eyeBallAll;
        private SwapTransformPair _eye;

        private AnimationClip _currentClip;
        private AnimationClip _currentClipOld;

        //表情の反転
        private readonly List<SwapTransformPair> _swapFaceTransformPairsList = new List<SwapTransformPair>(64);

        //しっぽの反転（CySpringを使わないときにだけ仕様）

        /// <summary>
        /// 使用準備
        /// </summary>
        /// <param name="transform"></param>
        /// <param name="animator">監視対象のANimator</param>
        public void Setup(ModelControllerBehaviour model , bool isMiniModel = false)
        {
            _model = model;
            //Swap対象のTransformをリストアップする
            var pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Thigh_L), _model.FindTransform(CharaNodeName.Thigh_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Knee_L), _model.FindTransform(CharaNodeName.Knee_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Ankle_L), _model.FindTransform(CharaNodeName.Ankle_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Ankle_offset_L), _model.FindTransform(CharaNodeName.Ankle_offset_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.TOE_L), _model.FindTransform(CharaNodeName.TOE_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.SP_CH_BUST0_L_00), _model.FindTransform(CharaNodeName.SP_CH_BUST0_R_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.SP_CH_BUST0_L_01), _model.FindTransform(CharaNodeName.SP_CH_BUST0_R_01));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(1f, -1f, -1f, 1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Shoulder_L), _model.FindTransform(CharaNodeName.Shoulder_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Arm_L), _model.FindTransform(CharaNodeName.Arm_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.ShoulderRoll_L), _model.FindTransform(CharaNodeName.ShoulderRoll_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, 1f, -1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Elbow_L), _model.FindTransform(CharaNodeName.Elbow_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, -1f, 1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.ArmRoll_L), _model.FindTransform(CharaNodeName.ArmRoll_R));
            if (pair != null)
            {
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Wrist_L), _model.FindTransform(CharaNodeName.Wrist_R));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, -1f, 1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Hand_Attach_L), _model.FindTransform(CharaNodeName.Hand_Attach_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Index_01_L), _model.FindTransform(CharaNodeName.Index_01_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Index_02_L), _model.FindTransform(CharaNodeName.Index_02_R));
            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Index_03_L), _model.FindTransform(CharaNodeName.Index_03_R));
            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Middle_01_L), _model.FindTransform(CharaNodeName.Middle_01_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Middle_02_L), _model.FindTransform(CharaNodeName.Middle_02_R));
            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Middle_03_L), _model.FindTransform(CharaNodeName.Middle_03_R));
            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Pinky_01_L), _model.FindTransform(CharaNodeName.Pinky_01_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Pinky_02_L), _model.FindTransform(CharaNodeName.Pinky_02_R));
            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Pinky_03_L), _model.FindTransform(CharaNodeName.Pinky_03_R));
            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Ring_01_L), _model.FindTransform(CharaNodeName.Ring_01_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Ring_02_L), _model.FindTransform(CharaNodeName.Ring_02_R));
            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Ring_03_L), _model.FindTransform(CharaNodeName.Ring_03_R));
            pair = AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Thumb_01_L), _model.FindTransform(CharaNodeName.Thumb_01_R));
            if (pair != null)
            {
                pair._scaleScale = new Vector3(-1f, -1f, 1f);
                pair._rotationScale = new Vector4(1f, -1f, 1f, -1f);
                pair._positionScale = new Vector3(1f, -1f, 1f);
            }

            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Thumb_02_L), _model.FindTransform(CharaNodeName.Thumb_02_R));
            AddSwapTransform(_swapTransformPairs, _model.FindTransform(CharaNodeName.Thumb_03_L), _model.FindTransform(CharaNodeName.Thumb_03_R));

            //軸反転のみするTransformをリストアップ
            if (!isMiniModel)
            {
                // ミニモデルには存在しない
                _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.UP_BODY_CTRL)));
                _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.Spine)));

            }
            _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.Hip)));
            _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.Waist)));
            _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.Chest)));
            _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.Neck)));
            _reverseTransforms.Add(new TransformInfo(_model.FindTransform(CharaNodeName.Head)));

            SetupEye(model.DrivenKeyComponent);
            //スカート、しっぽは必要な時だけ反転
            SetupTail();
            SetupSkirt();

            _isCompleteSetup = true;
        }

        /// <summary>
        /// 視線、ウィンクの反転準備
        /// </summary>
        /// <param name="drivenKey"></param>
        private void SetupEye(DrivenKeyComponent drivenKey)
        {
            //視線
            _eyeBallAll = new TransformInfo(drivenKey.AllEyeTransform);

            _eye = new SwapTransformPair(drivenKey.LeftEyeTransform, drivenKey.RightEyeTransform);
            _eye._scaleScale = Math.VECTOR3_ONE;
            _eye._rotationScale = Vector4.one;
            _eye._positionScale = new Vector3(-1f, 1f, 1f);

            //DrivenKeyの反転
            var eyeLRoot = drivenKey.GetDrivenKeyRootTransform(FaceGroupType.EyeL);
            var eyeRRoot = drivenKey.GetDrivenKeyRootTransform(FaceGroupType.EyeR);
            var eyebrowLRoot = drivenKey.GetDrivenKeyRootTransform(FaceGroupType.EyebrowL);
            var eyebrowRRoot = drivenKey.GetDrivenKeyRootTransform(FaceGroupType.EyebrowR);

            var eyeTypeArray = FaceTypeUtil.GetEyeTypeArray();
            for (int index = 0; index < eyeTypeArray.Length; ++index)
            {
                if (eyeTypeArray[index] == FaceEyeType.Base)
                {
                    continue;
                }
                string eyeType = eyeTypeArray[index].ToString();
                string eyeL = DrivenKeyComponent.EYE_L + DrivenKeyComponent.FACE_TYPE_NAME_DIVIDER + eyeType;
                string eyeR = DrivenKeyComponent.EYE_R + DrivenKeyComponent.FACE_TYPE_NAME_DIVIDER + eyeType;
                Transform eyeLTrs = eyeLRoot.Find(eyeL);
                Transform eyeRTrs = eyeRRoot.Find(eyeR);
                if (eyeLTrs != null && eyeRTrs != null)
                {
                    _swapFaceTransformPairsList.Add(new SwapTransformPair(eyeLTrs, eyeRTrs));
                }
            }
            var eyebrowTypeArray = FaceTypeUtil.GetEyebrowTypeArray();
            for (int index = 0; index < eyebrowTypeArray.Length; ++index)
            {
                if (eyebrowTypeArray[index] == FaceEyebrowType.Base)
                {
                    continue;
                }
                string eyebrowType = eyebrowTypeArray[index].ToString();
                string eyebrowL = DrivenKeyComponent.EYEBROW_L + DrivenKeyComponent.FACE_TYPE_NAME_DIVIDER + eyebrowType;
                string eyebrowR = DrivenKeyComponent.EYEBROW_R + DrivenKeyComponent.FACE_TYPE_NAME_DIVIDER + eyebrowType;
                Transform eyebrowLTrs = eyebrowLRoot.Find(eyebrowL);
                Transform eyebrowRTrs = eyebrowRRoot.Find(eyebrowR);
                if (eyebrowLTrs != null && eyebrowRTrs != null)
                {
                    _swapFaceTransformPairsList.Add(new SwapTransformPair(eyebrowLTrs, eyebrowRTrs));
                }
            }
        }

        /// <summary>
        /// しっぽの準備
        /// 基本はCySpringで動くが、まれにモーションが流し込まれるので反転対応が必要
        /// </summary>
        private void SetupTail()
        {
            //しっぽは水平反転
            var tail = _model.FindTransform(CharaNodeName.TAIL_CTRL);
            if (tail != null)
            {
                _reverseTransforms.Add(new TransformInfo(tail));
            }
            tail = _model.FindTransform(CharaNodeName.SP_HI_TAIL0_B_00);
            if (tail != null)
            {
                _reverseTransforms.Add(new TransformInfo(tail));
            }
            tail = _model.FindTransform(CharaNodeName.SP_HI_TAIL0_B_01);
            if (tail != null)
            {
                _reverseTransforms.Add(new TransformInfo(tail));
            }
            tail = _model.FindTransform(CharaNodeName.SP_HI_TAIL0_B_02);
            if (tail != null)
            {
                _reverseTransforms.Add(new TransformInfo(tail));
            }
            tail = _model.FindTransform(CharaNodeName.SP_HI_TAIL0_B_03);
            if (tail != null)
            {
                _reverseTransforms.Add(new TransformInfo(tail));
            }
            tail = _model.FindTransform(CharaNodeName.SP_HI_TAIL0_B_04);
            if (tail != null)
            {
                _reverseTransforms.Add(new TransformInfo(tail));
            }
        }

        /// <summary>
        /// スカートの準備
        /// 基本はCySpringで動くが、まれにモーションが流し込まれるので反転対応が必要
        /// </summary>
        private void SetupSkirt()
        {
            //ミニスカート
            var skirt = _model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_B_00);
            if (skirt != null)
            {
                _reverseSkirtTransforms.Add(new TransformInfo(skirt));
            }
            skirt = _model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_F_00);
            if (skirt != null)
            {
                _reverseSkirtTransforms.Add(new TransformInfo(skirt));
            }
            var pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_BL_00),_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_BR_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_FL_00),_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_FR_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_L_00),_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_R_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_FLL_00),_model.FindTransform(CharaNodeName.SP_HI_MSKIRT0_FRR_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            //ロングスカート
            skirt = _model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_B_00);
            if (skirt != null)
            {
                _reverseSkirtTransforms.Add(new TransformInfo(skirt));
            }
            skirt = _model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_F_00);
            if (skirt != null)
            {
                _reverseSkirtTransforms.Add(new TransformInfo(skirt));
            }
            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_BL_00),_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_BR_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_FL_00),_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_FR_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_L_00),_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_R_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_FLL_00),_model.FindTransform(CharaNodeName.SP_HI_LSKIRT0_FRR_00));
            if (pair != null)
            {
                pair._rotationScale = new Vector4(-1f, 1f, 1f, -1f);
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }

            //後ろのリボン
            pair = TryAddSwapTransform(_swapSkirtTransformPairs,_model.FindTransform(CharaNodeName.Sp_Hi_Ribbon0_L_00),_model.FindTransform(CharaNodeName.Sp_Hi_Ribbon0_R_00));
            if (pair != null)
            {
                pair._positionScale = new Vector3(-1f, 1f, 1f);
            }
        }



        /// <summary>
        /// 入れ替え対象のTransformを登録
        /// </summary>
        /// <param name="targetA"></param>
        /// <param name="targetB"></param>
        private static SwapTransformPair AddSwapTransform(List<SwapTransformPair> pairs,
            Transform targetA,
            Transform targetB)
        {
            if (targetA == null)
            {
                return null;
            }
            if (targetB == null)
            {
                return null;
            }
            var pair = new SwapTransformPair(targetA, targetB);
            pairs.Add(pair);
            return pair;
        }

        private static SwapTransformPair TryAddSwapTransform(List<SwapTransformPair> pairs,
            Transform targetA,
            Transform targetB)
        {
            if (targetA == null)
            {
                return null;
            }
            if (targetB == null)
            {
                return null;
            }

            var pair = new SwapTransformPair(targetA, targetB);
            pairs.Add(pair);
            return pair;
        }

        /// <summary>
        /// アニメーション開始前にデフォルトに戻す
        /// </summary>
        public void Update()
        {
            if (!IsEnable)
            {
                return;
            }
            
            if (_model == null)
            {
                return;
            }

            UpdateBody();
            UpdateSkirt();
            UpdateEye();
        }

        private void UpdateBody()
        {
            foreach (var swap in _swapTransformPairs)
            {
                swap.Reset();
            }

            foreach (var reverse in _reverseTransforms)
            {
                reverse.Reset();
            }
        }

        private void UpdateSkirt()
        {
            if (_model.IsEnableSkirtController)
            {
                return;
            }

            //スカートのCySpringが無効の時だけ位置戻しを行う
            foreach (var swap in _swapSkirtTransformPairs)
            {
                swap.Reset();
            }
            foreach (var reverse in _reverseSkirtTransforms)
            {
                reverse.Reset();
            }
        }

        private void UpdateEye()
        {
            if (_eyeBallAll != null)
            {
                _eyeBallAll.Reset();
            }
            if (_eye != null)
            {
                _eye.Reset();
            }

            int count = _swapFaceTransformPairsList.Count;
            for (int index = 0; index < count; ++index)
            {
                _swapFaceTransformPairsList[index].Reset();
            }
        }

        /// <summary>
        /// LateUpdateタイミングで呼ばれる処理
        /// </summary>
        /// <param name="isMirror"></param>
        public void LateUpdate(bool isMirror)
        {
            if (!IsEnable)
            {
                return;
            }
            
            if (_model == null)
            {
                return;
            }

            if (!isMirror)
            {
                //反転しないなら抜ける
                return;
            }

            LateUpdateBody();
            LateUpdateSkirt();
            LateUpdateEye();
        }

        private void LateUpdateBody()
        {
            //入れ替え
            foreach (var swap in _swapTransformPairs)
            {
                swap.Swap();
            }

            //左右反転
            foreach (var reverse in _reverseTransforms)
            {
                ReverseTransformInfo(reverse);
            }
        }

        /// <summary>
        /// スカートのLateUpdate
        /// </summary>
        private void LateUpdateSkirt()
        {
            if (_model.IsEnableSkirtController)
            {
                //スカートのCySpringが有効なときは処理しない
                return;
            }

            //スカートのCySpringが無効な時だけ
            foreach (var swap in _swapSkirtTransformPairs)
            {
                swap.Swap();
            }
            //左右反転
            foreach (var reverse in _reverseSkirtTransforms)
            {
                ReverseTransformInfo(reverse);
            }
        }

        /// <summary>
        /// 目のLateUpdate
        /// </summary>
        private void LateUpdateEye()
        {
            if (_eyeBallAll != null)
            {
                var position = _eyeBallAll._transform.localPosition;
                position.x *= -1f;
                _eyeBallAll._transform.localPosition = position;
            }

            if (_eye != null)
            {
                _eye.Swap();
            }

            int count = _swapFaceTransformPairsList.Count;
            for (int index = 0; index < count; ++index)
            {
                _swapFaceTransformPairsList[index].Swap();
            }
        }

        private static void ReverseTransformInfo( TransformInfo reverse )
        {
            //変換前の値をキープしておく
            var localRotation = reverse._transform.localRotation;
            localRotation.y *= -1f;
            localRotation.z *= -1f;
            var pos = reverse._transform.localPosition;
            pos.x *= -1f;
            reverse._transform.SetLocalPositionAndRotation(pos, localRotation);
        }

        /// <summary>
        /// 反転対象のTransformを返す
        /// </summary>
        /// <param name="target"></param>
        /// <returns></returns>
        public Transform SearchSwapTransform(Transform target)
        {
            var findPair = _swapTransformPairs.Find(pair => pair._first._transform == target || pair._second._transform == target);
            if (findPair == null)
            {
                return target;
            }

            if (findPair._first._transform == target)
            {
                return findPair._second._transform;
            }

            return findPair._first._transform;
        }

        public void Cleanup()
        {
            _swapTransformPairs.Clear();
            _reverseTransforms.Clear();
            _isCompleteSetup = false;
            _model = null;
        }
    }
}
