using UnityEngine;
using System.Collections;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// Attach機能処理部分
            /// Timelineに持たせる
            /// </summary>
            public class CutInTimelineAttachModule
            {
                // クラス内フラグが多いのでまとめる
                enum Flags
                {
                    Attach = 0,
                    Detach,
                    IsApplyAttachPos,
                    IsApplyAttachPosX,
                    IsApplyAttachPosY,
                    IsApplyAttachPosZ,
                    IsApplyAttachRotation,
                    IsApplyAttachScale,
                    DelayDettach,       // モーションを加味してLateUpdateで最終値をキャッシュ
                    IsApplyAttachCharaScale,
                    Max
                }

                // アタッチ先のロケーター検索が増えたのでキャッシュを用意
                private Transform _lastAttachLocator = null;
                private int _lastAttachLocatorIndex = -1;
                private string _lastAttachLocatorName = string.Empty;
                private Transform _attachNode = null;

                private Vector3 _detachPosition = Math.VECTOR3_ZERO;
                private Quaternion _detachRotation = Math.QUATERNION_IDENTITY;
                private Vector3 _detachScale = Math.VECTOR3_ONE;

                // Ownerのプロパティ側で座標を設定する場合は
                // アタッチ後のTransformから計算する
                private Vector3 _position = Math.VECTOR3_ZERO;
                private Quaternion _rotation = Math.QUATERNION_IDENTITY;
                private Vector3 _scale = Math.VECTOR3_ONE;

                private int _flgValue = 0;
                private Transform _ownerTrans = null;

                /// <summary>
                /// アタッチ先のキャラ、
                /// 設定がキャラ以外の場合ここはnullになる想定
                /// </summary>
                public ModelController AttachTargetCharacter { get; private set; } = null;

                /// <summary>
                /// アタッチ用の座標計算をするか
                /// </summary>
                public bool IsUpdate()
                {
                    // アタッチ中は親ノードのTransformから計算する
                    // デタッチ後はデタッチ時のTransformから計算する
                    return GetFlg(Flags.Attach) || GetFlg(Flags.Detach) ;
                }

                /// <summary>
                /// フラグのリセット
                /// </summary>
                private void ResetFlg()
                {
                    _flgValue = 0;
                }

                /// <summary>
                /// フラグをオンに
                /// </summary>
                private void SetFlg(Flags flg)
                {
                    FlagUtil.AddFlag(ref _flgValue, (int)flg);
                }

                /// <summary>
                /// フラグ更新
                /// </summary>
                private void SetFlg(Flags flg, bool b)
                {
                    if (b)
                    {
                        FlagUtil.AddFlag(ref _flgValue, (int)flg);
                    }
                    else
                    {
                        FlagUtil.RemoveFlag(ref _flgValue, (int)flg);
                    }
                }

                /// <summary>
                /// フラグをオフに
                /// </summary>
                private void UnSetFlg(Flags flg)
                {
                    FlagUtil.RemoveFlag(ref _flgValue, (int)flg);
                }

                /// <summary>
                /// フラグ取得
                /// </summary>
                private bool GetFlg(Flags flg)
                {
                    return FlagUtil.HasFlag( _flgValue, (int)flg);
                }

                /// <summary>
                /// LateUpdate
                /// </summary>
                public void LateUpdate(Transform trans, bool isPositionUpdate)
                {
                    if (trans == null)
                    {
                        return;
                    }
                    if (!IsUpdate())
                    {
                        return;
                    }

                    _ownerTrans = trans;
                    LateUpdateDetach();
                    LateUpdateAttachTransform(isPositionUpdate);
                }

                /// <summary>
                /// デタッチ時の更新処理
                /// </summary>
                private void LateUpdateDetach()
                {
                    if (GetFlg(Flags.DelayDettach))
                    {
                        // デタッチする瞬間の座標を記録する。
                        _detachPosition = _attachNode.position;
                        _detachRotation = _attachNode.rotation;
                        _detachScale = _attachNode.lossyScale;
                        UnSetFlg(Flags.DelayDettach);
                        _attachNode = null;
                    }
                }

                /// <summary>
                /// 親ノード取得
                /// </summary>
                private Transform GetParentNode()
                {
                    return _attachNode != null ? _attachNode: _ownerTrans.parent;
                }

                /// <summary>
                /// トランスフォーム情報を更新
                /// </summary>
                private void LateUpdateAttachTransform(bool isPositionUpdate)
                {
                    //_attachNodeがnullの可能性があるので、その場合_ownerTransの親（更新を呼び出しオブジェクト）が帰ってくる
                    //_ownerTransに親がない場合nullとなります
                    Transform parent = GetParentNode();

                    // プロパティのTransformで設定されていたら各値が入っている
                    // 特に更新がなければ初期値
                    var offsetPos = _position;
                    var offsetRot = _rotation;
                    var offsetScale = Math.VECTOR3_ONE;

                    // スキルカットはモーションで座標が指定されることがある
                    var newPos = (isPositionUpdate) ? _ownerTrans.position : Math.VECTOR3_ZERO;
                    var newRot = (isPositionUpdate) ? _ownerTrans.rotation : Math.QUATERNION_IDENTITY;
                    var newScale = Math.VECTOR3_ONE;

                    var parentPos = parent != null ? parent.position : _ownerTrans.position;
                    var parentRot = parent != null ? parent.rotation : _ownerTrans.rotation;
                    //親の下に配置する際に元々オブジェクトのスケールを持って計算させる
                    var parentLocalScale = parent != null ? Vector3.Scale(parent.transform.localScale, _ownerTrans.localScale) : _ownerTrans.localScale;

                    if (GetFlg(Flags.Attach))
                    {
                        // 回転更新
                        newRot *= GetFlg(Flags.IsApplyAttachRotation) ? parentRot * _rotation : offsetRot;

                        // 座標更新
                        // 座標は各成分で適用するかのフラグがある
                        if(GetFlg(Flags.IsApplyAttachPos))
                        {
                            newPos.x += (GetFlg(Flags.IsApplyAttachPosX)) ? parentPos.x : 0.0f;
                            newPos.y += (GetFlg(Flags.IsApplyAttachPosY)) ? parentPos.y : 0.0f;
                            newPos.z += (GetFlg(Flags.IsApplyAttachPosZ)) ? parentPos.z : 0.0f;
                        }
                        newPos += _ownerTrans.rotation * offsetPos;

                        // スケール更新
                        newScale = Vector3.Scale(_ownerTrans.localScale, offsetScale);

                        if(GetFlg(Flags.IsApplyAttachScale))
                        {
                            newScale = Vector3.Scale(parentLocalScale, offsetScale);
                        }
                        else
                        {
                            //キャラ身長差によってスケール調整
                            if(GetFlg(Flags.IsApplyAttachCharaScale)
                                && AttachTargetCharacter != null)
                            {
                                RescaleBgModelOnAttachCharacter(ref newScale);
                            }
                        }
                    }
                    else
                    {
                        // Flags.Detachが有効
                        // アタッチしていない時のトランスフォーム更新
                        newRot *= (offsetRot * _detachRotation);
                        newPos += (_detachPosition + _detachRotation * offsetPos);
                        newScale = Vector3.Scale(_detachScale, offsetScale);
                    }

                    _ownerTrans.localScale = newScale;
                    _ownerTrans.SetPositionAndRotation(newPos, newRot);

                    _position = Math.VECTOR3_ZERO;
                    _rotation = Math.QUATERNION_IDENTITY;
                    _scale = Math.VECTOR3_ONE;
                }

                /// <summary>
                /// キャラクターにアタッチした際にモデルのスケールを調整
                /// </summary>
                private void RescaleBgModelOnAttachCharacter(ref Vector3 scale)
                {
                    if (AttachTargetCharacter == null || _ownerTrans == null)
                    {
                        return;
                    }

                    if(AttachTargetCharacter.PositionNode == null)
                    {
                        return;
                    }

                    //直接にPositionノードのスケールを使って計算
                    scale = Vector3.Scale(scale, AttachTargetCharacter.PositionNode.localScale);
                }

                /// <summary>
                /// アタッチ
                /// </summary>
                public void Attach(CutInTimelineController controller, CutInTimelineAttachData attachData)
                {
                    _attachNode = GetLocator(controller , attachData.AttachIndex, attachData.AttachLocatorSearchId, attachData.AttachLocator);

                    _detachPosition = Math.VECTOR3_ZERO;
                    _detachRotation = Math.QUATERNION_IDENTITY;
                    _detachScale = Math.VECTOR3_ONE;
                    SetFlg(Flags.Attach);
                    UnSetFlg(Flags.Detach);

                    _lastAttachLocatorName = attachData.AttachLocator;
                    _lastAttachLocatorIndex = attachData.AttachIndex;
                    _lastAttachLocator = _attachNode;

                    SetFlg(Flags.IsApplyAttachPos , attachData.IsApplyAttachPos);
                    SetFlg(Flags.IsApplyAttachPosX , attachData.IsApplyAttachPosX);
                    SetFlg(Flags.IsApplyAttachPosY , attachData.IsApplyAttachPosY);
                    SetFlg(Flags.IsApplyAttachPosZ , attachData.IsApplyAttachPosZ);
                    SetFlg(Flags.IsApplyAttachRotation , attachData.IsApplyAttachRotation);
                    SetFlg(Flags.IsApplyAttachScale, attachData.IsApplyAttachScale);
                    SetFlg(Flags.IsApplyAttachCharaScale, attachData.IsApplyCharaScale);
                }

                /// <summary>
                /// デタッチ
                /// </summary>
                public void Detach(bool isDetachStayTransform, bool isWorld)
                {
                    if (isDetachStayTransform)
                    {
                        SetFlg(Flags.DelayDettach,  (_attachNode != null) ? true : false);
                    }
                    else
                    {
                        _detachPosition = Math.VECTOR3_ZERO;
                        _detachRotation = Math.QUATERNION_IDENTITY;
                        _detachScale = Math.VECTOR3_ONE;
                        _attachNode = null;
                    }
                    SetFlg(Flags.Detach);

                    UnSetFlg(Flags.IsApplyAttachPos);
                    UnSetFlg(Flags.IsApplyAttachPosX);
                    UnSetFlg(Flags.IsApplyAttachPosY);
                    UnSetFlg(Flags.IsApplyAttachPosZ);
                    UnSetFlg(Flags.IsApplyAttachRotation);
                    UnSetFlg(Flags.IsApplyAttachScale);
                    UnSetFlg(Flags.IsApplyAttachCharaScale);
                    UnSetFlg(Flags.Attach);

                    //デタッチしたらアタッチ先のキャラの情報を消す
                    AttachTargetCharacter = null;
                }

                /// <summary>
                /// リセット
                /// </summary>
                public void Reset()
                {
                    UnSetFlg(Flags.Attach);
                    UnSetFlg(Flags.Detach);
                }

                /// <summary>
                /// 座標設定。
                /// </summary>
                public void SetPosition(Vector3 pos)
                {
                    _position = pos;
                }
                /// <summary>
                /// 回転設定。
                /// </summary>
                public void SetRotation(Quaternion rot)
                {
                    _rotation = rot;
                }
                /// <summary>
                /// スケール設定
                /// </summary>
                public void SetScale(Vector3 scale)
                {
                    _scale = scale;
                }

                /// <summary>
                /// キャッシュ済みロケータを使いまわしてもよいか
                /// </summary>
                public bool IsValidCachedLocator(int attachIndex, string attachLocatorName)
                {
                    if (_lastAttachLocator == null)
                    {
                        return false;
                    }
                    if (_lastAttachLocatorIndex != attachIndex)
                    {
                        return false;
                    }
                    if ( _lastAttachLocatorName != attachLocatorName)
                    {
                        return false;
                    }
                    return true;
                }

                /// <summary>
                /// ロケーターの取得
                /// NOTE：他タイムラインのアタッチ処理のロケーター取得関数もまとめられそう
                /// </summary>
                public Transform GetLocator(
                        CutInTimelineController controller,
                        int attachIndex,
                        string attachLocatorSearchId,
                        string attachLocatorName)
                {
                    // すでに検索済みならそれを返す
                    if (IsValidCachedLocator(attachIndex, attachLocatorName))
                    {
                        return _lastAttachLocator;
                    }

                    if (attachIndex == TimelineKeyCharaPropData.INVALID_ATTACH_INDEX)
                    {
                        return null;
                    }

                    // KeyDataのAttachIndexが設定されていたら他のタイムラインからアタッチ対象を検索
                    if (attachIndex == TimelineKeyCharaPropData.CAMERA_TARGET_INDEX)
                    {
                        var camera = controller.MotionCamera;
                        var locator = GameObjectUtil.FindChild(attachLocatorName, camera.transform);
                        if (locator != null)
                        {
                            return locator;
                        }
                        return camera.transform;
                    }

                    if (attachIndex == TimelineKeyCharaPropData.MULTI_CAMERA_TARGET_INDEX)
                    {
                        var cameraList = controller.GetActiveSheetMultiCamera();
                        if (cameraList == null || cameraList.Count == 0)
                        {
                            return null;
                        }
                        TimelineMultiCameraGroupData.MultiCamera multiCamera = cameraList[0].TargetCamera;
                        if(multiCamera == null )
                        {
                            return null;
                        }
                        // ロケーターを探す
                        var locator = GameObjectUtil.FindChild(attachLocatorName, multiCamera.MotionCamera.transform);
                        if (locator != null)
                        {
                            return locator;
                        }
                        return multiCamera.MotionCamera.transform;
                    }

                    if (attachIndex == TimelineKeyCharaPropData.MONITOR_CAMERA_TARGET_INDEX)
                    {
                        var cameraList = controller.GetActiveSheetMonitorCamera();
                        if (cameraList == null || cameraList.Count == 0)
                        {
                            return null;
                        }
                        TimelineMonitorCameraGroupData.MonitorCamera monitorCamera = cameraList[0].TargetCamera;
                        if (monitorCamera == null)
                        {
                            return null;
                        }
                        // ロケーターを探す
                        var locator = GameObjectUtil.FindChild(attachLocatorName, monitorCamera.MotionCamera.transform);
                        if (locator != null)
                        {
                            return locator;
                        }
                        return monitorCamera.MotionCamera.transform;
                    }

                    if (attachIndex == TimelineKeyCharaPropData.BACKGROUND_TARGET_INDEX)
                    {
                        return controller.GetBackgroundTransform(attachLocatorSearchId, attachLocatorName);
                    }

                    if (attachIndex == TimelineKeyCharaPropData.PREFAB_TARGET_INDEX)
                    {
                        return controller.GetEffectTransform(attachLocatorSearchId, attachLocatorName);
                    }

                    if (attachIndex == TimelineKeyCharaPropData.CHARAPROP_TAREGET_INDEX)
                    {
                        return controller.GetCharaPropTransform(attachLocatorSearchId, attachLocatorName);
                    }

                    if (attachIndex >= TimelineKeyCharaPropData.MINICHARA_TARGET_OFFSET &&
                        attachIndex <= TimelineKeyCharaPropData.MINICHARA_TARGET_MAX)
                    {
                        var miniCharacters = controller.GetActiveWorkSheet().MiniCharacterDataList;

                        // アタッチターゲットはミニキャラ
                        var index = attachIndex - TimelineKeyEffectData.MiniCharaTargetOffset;
                        if( index >= miniCharacters.Count )
                        {
                            // 値が不正
                            return null;
                        }
                        var miniCharaGroup = miniCharacters[index];
                        if (miniCharaGroup != null)
                        {
                            var miniChara = miniCharaGroup.Chara;
                            if (miniChara != null && miniChara.Transform != null)
                            {
                                // ロケーターを探す
                                var locator = GameObjectUtil.FindChild(attachLocatorName, miniChara.Transform);
                                if (locator != null)
                                {
                                    if (controller.IsMirror)
                                    {
                                        //左右反転するなら
                                        //反転対象のロケータを持ってくる
                                        locator = miniChara.MirrorCtrl.SearchSwapTransform(locator);
                                    }
                                    return locator;
                                }

                                return miniChara.Transform;
                            }
                        }
                    }

                    var characters = controller.GetActiveWorkSheet()._characterList;
                    if (attachIndex >= characters.Count)
                    {
                        return null;
                    }

                    var character = characters[attachIndex];
                    if (character != null &&
                        character.Chara != null &&
                        character.Chara._transform != null)
                    {
                        //アタッチ先のキャラを取得しておく
                        AttachTargetCharacter = character.Chara._cutInCharacter.Model;

                        // ロケーターを探す
                        var locator = GameObjectUtil.FindChild(attachLocatorName, character.Chara._transform);
                        if (locator != null)
                        {
                            if (controller.IsMirror)
                            {
                                locator = character.Chara._modelMirrorController.SearchSwapTransform(locator);
                            }
                            return locator;
                        }
                        return character.Chara._transform;

                    }
                    return null;
                }
            }


            /// <summary>
            /// アタッチ処理のデータ部分
            /// KeyDataに持たせる
            /// NOTE:データは既に各タイムラインで定義されているので、
            /// 既存の処理をまとめるのは難しそう（カットデータの全更新が必要）
            /// 新規でアタッチ処理が必要な場合はこちらを流用したい
            /// </summary>
            [System.Serializable]
            public class CutInTimelineAttachData
            {
                // プロパティ表示用
                public const string BACKGROUND_NAME = "BackGround";
                public const string CHARA_PROP_NAME = "CharaProp";

                // Indexでタイプと番号（キャラのみ）を識別
                public const int BACKGROUND_TARGET_INDEX = 3000;
                public const int MINICHARA_TARGET_OFFSET = 6000;
                public const int MINICHARA_TARGET_MAX = MINICHARA_TARGET_OFFSET + 999;
                public const int CHARAPROP_TAREGET_INDEX= 7000;
                public const int CHARA_TARGET_OFFSET = 0;
                public const int INVALID_ATTACH_INDEX = -1;

                /// <summary>
                /// 保存されるデータ
                /// アタッチ関連データ部
                /// </summary>
                public bool IsAttach = false;    //!< アタッチフラグ
                public bool IsDetach = false;    //!< デタッチフラグ
                public bool IsAttachPreLateUpdate = false; //!< アタッチの早期アップデートフラグ このフラグはアタッチ・デタッチフラグと連動しなくてOK
                public bool IsDetachStayTransform = false;       //デタッチ時にキャラクターのスケールを適用するか
                public string AttachLocator = "";                //!< アタッチロケーター名
                public string AttachLocatorSearchId = "";        //!< アタッチロケーターのトラックのSearchId
                public int AttachIndex = INVALID_ATTACH_INDEX;
                public bool IsApplyAttachPos = true;
                public bool IsApplyAttachPosX = true;
                public bool IsApplyAttachPosY = true;
                public bool IsApplyAttachPosZ = true;
                public bool IsApplyAttachRotation = true;
                public bool IsApplyAttachScale = true;
                public bool IsApplyCharaScale = true;   //キャラスケールを適用する

                // 以下保存されないデータ

                public string AttachObjectName { get; set;}   // アタッチオブジェクト名

                [System.NonSerialized]
                private GameObject _targetGameObject; // アタッチ対象のオブジェクトキャッシュ

                /// <summary>
                /// Copy関数
                /// </summary>
                public void Copy(CutInTimelineAttachData from)
                {
                    IsAttach = from.IsAttach;
                    IsDetach = from.IsDetach;
                    IsDetachStayTransform = from.IsDetachStayTransform;
                    AttachLocator = from.AttachLocator;
                    AttachLocatorSearchId = from.AttachLocatorSearchId;
                    AttachIndex = from.AttachIndex;
                    IsAttachPreLateUpdate = from.IsAttachPreLateUpdate;
                    IsApplyAttachPos = from.IsApplyAttachPos;
                    IsApplyAttachPosX = from.IsApplyAttachPosX;
                    IsApplyAttachPosY = from.IsApplyAttachPosY;
                    IsApplyAttachPosZ = from.IsApplyAttachPosZ;
                    IsApplyAttachRotation = from.IsApplyAttachRotation;
                    IsApplyAttachScale = from.IsApplyAttachScale;
                }

#if UNITY_EDITOR

                // 以下インスペクターへのプロパティ描画

                // デバッグ用のコンスト定義なのでここに
                const float TARGET_NAME_BUTTON_WIDTH = 256;

                /// <summary>
                /// ヘッダー
                /// </summary>
                public void OnGUI_Header(GUIContext ctx, CutInTimelineController controller)
                {
                    EditorGUILayout.LabelField("■Attach control");
                }

                /// <summary>
                /// Attachトグル
                /// </summary>
                public void OnGUI_IsAttach<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T: TimelineKeyTransformData
                {
                    EditorGUILayout.BeginHorizontal();
                    if (ctx.ME_Toggle("Attach", ref IsAttach))
                    {
                        if (IsAttach)
                        {
                            IsDetach = false;
                        }

                        ctx.MultiKeyEditApply<T>(x =>
                        {
                            x.AttachData.IsAttach = IsAttach;
                            x.AttachData.IsDetach = IsDetach;
                        });

                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// アタッチロケーターのテキストフィールド
                /// </summary>
                public void OnGUI_Locator<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (ctx.ME_TextField("locator", ref AttachLocator))
                    {
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.AttachLocator = AttachLocator);
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// 使われやすいボーンを指定ためのボタン
                /// </summary>
                public void OnGUI_LocatorPreset<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (GUILayout.Button("左手"))
                    {
                        AttachLocator = CharaNodeName.Hand_Attach_L;
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.AttachLocator = AttachLocator);
                        onChange?.Invoke();

                    }
                    if (GUILayout.Button("右手"))
                    {
                        AttachLocator = CharaNodeName.Hand_Attach_R;
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.AttachLocator = AttachLocator);
                        onChange?.Invoke();
                    }
                    if(GUILayout.Button("Position"))
                    {
                        AttachLocator = CharaNodeName.Position;
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.AttachLocator = AttachLocator);
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// ポジションに反映させるかトグル
                /// </summary>
                public void OnGUI_IsApplyPosition<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (ctx.ME_ToggleLeft("use position", ref IsApplyAttachPos))
                    {
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.IsApplyAttachPos = IsApplyAttachPos);
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// ポジションの各要素に反映させるかトグル
                /// </summary>
                public void OnGUI_IsApplyPositionAxis<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }
                    if (!IsApplyAttachPos)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    using (var changeScope = new EditorGUI.ChangeCheckScope())
                    {
                        IsApplyAttachPosX = EditorGUILayout.ToggleLeft("x", IsApplyAttachPosX);
                        IsApplyAttachPosY = EditorGUILayout.ToggleLeft("y", IsApplyAttachPosY);
                        IsApplyAttachPosZ = EditorGUILayout.ToggleLeft("z", IsApplyAttachPosZ);
                        if (changeScope.changed)
                        {
                            ctx.MultiKeyEditApply<T>(x =>
                            {
                                x.AttachData.IsApplyAttachPosX = IsApplyAttachPosX;
                                x.AttachData.IsApplyAttachPosY = IsApplyAttachPosY;
                                x.AttachData.IsApplyAttachPosZ = IsApplyAttachPosZ;
                            });
                            onChange?.Invoke();
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// ロ＝テーションに反映させるかトグル
                /// </summary>
                public void OnGUI_IsApplyRotation<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (ctx.ME_ToggleLeft("use rotation", ref IsApplyAttachRotation))
                    {
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.IsApplyAttachRotation = IsApplyAttachRotation);
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// スケールに反映させるかトグル
                /// </summary>
                public void OnGUI_IsApplyScale<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (ctx.ME_ToggleLeft("use scale", ref IsApplyAttachScale))
                    {
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.IsApplyAttachScale = IsApplyAttachScale);
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();

                    //キャラ以外にアタッチしている
                    if (AttachIndex != INVALID_ATTACH_INDEX && AttachIndex > ctx._curOnGUISheet._data._characterList.Count)
                    {
                        EditorGUILayout.HelpBox("キャラ以外にアタッチしている場合、「apply chara position scale」フラグは動作しない", MessageType.Warning);
                        IsApplyAttachScale = false;
                    }

                    //[use scale]フラグは優先適用する
                    if (IsApplyAttachScale)
                    {
                        EditorGUILayout.HelpBox("use scaleフラグの設定が「apply chara position scale」より優先される", MessageType.Info);

                        IsApplyCharaScale = false;
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.IsApplyCharaScale = IsApplyCharaScale);
                    }
                    EditorGUILayout.BeginHorizontal();
                    EditorGUI.BeginDisabledGroup(IsApplyAttachScale);
                    EditorGUILayout.Space();
                    if (ctx.ME_ToggleLeft("apply chara position scale", ref IsApplyCharaScale))
                    {
                        ctx.MultiKeyEditApply<T>(x => x.AttachData.IsApplyCharaScale = IsApplyCharaScale);
                    }
                    EditorGUI.EndDisabledGroup();
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// デタッチトグル
                /// </summary>
                public void OnGUI_Detach<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    EditorGUILayout.BeginHorizontal();
                    if (ctx.ME_Toggle("Detach", ref IsDetach))
                    {
                        if (IsDetach)
                        {
                            IsAttach = false;
                        }

                        ctx.MultiKeyEditApply<T>(x =>
                        {
                            x.AttachData.IsAttach = IsAttach;
                            x.AttachData.IsDetach = IsDetach;
                        });
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// PreUpdateトグル
                /// </summary>
                public void OnGUI_PreUpdate<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    EditorGUILayout.HelpBox("このフラグは現状Characterタイムラインにのみ対応している", MessageType.Info);
                    EditorGUILayout.BeginHorizontal();
                    if(ctx.ME_Toggle("PreLateUpdate", ref IsAttachPreLateUpdate))
                    {
                        ctx.MultiKeyEditApply<T>(x =>
                        {
                            x.AttachData.IsAttachPreLateUpdate = IsAttachPreLateUpdate;
                        });
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// デタッチ時のTransformを引き継ぐかトグル
                /// </summary>
                public void OnGUI_DetachStayTransform<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsDetach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    if (ctx.ME_Toggle("Transformを引き継ぐ", ref IsDetachStayTransform))
                    {
                        ctx.MultiKeyEditApply<T>(x => { x.AttachData.IsDetachStayTransform = IsDetachStayTransform; });
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// どの対象にアタッチ設定しているか可視化
                /// </summary>
                public void OnGUI_DisplayTargetName(GUIContext ctx, CutInTimelineController controller)
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    if (AttachIndex == INVALID_ATTACH_INDEX)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();

                    if (AttachIndex < ctx._curOnGUISheet._data._characterList.Count)
                    {
                        var charaId = ctx._curOnGUISheet._data._characterList[AttachIndex].Chara._cutInCharacter.Model.GetCharaID();
                        var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
                        AttachObjectName = charaData?.Name ?? "マスターデータにキャラがない"; // + ": Chara " + AttachIndex;
                    }
                    else if(AttachIndex >= MINICHARA_TARGET_OFFSET &&
                            AttachIndex <= MINICHARA_TARGET_MAX )
                    {
                        // ミニキャラのインデックス範囲内
                        var miniCharaIdx = AttachIndex - MINICHARA_TARGET_OFFSET;
                        if (miniCharaIdx >= ctx._curOnGUISheet._data.MiniCharacterDataList.Count)
                        {
                            // 対象が見つからなかった
                            AttachObjectName = "Invalid";
                        }
                        else
                        {
                            AttachObjectName = "(ミニ)" + ctx._curOnGUISheet._data.MiniCharacterDataList[miniCharaIdx].CharacterName  + ": Chara " + AttachIndex;
                        }
                    }
                    else switch (AttachIndex)
                    {
                        case BACKGROUND_TARGET_INDEX:
                            AttachObjectName = BACKGROUND_NAME;
                            break;
                        case CHARAPROP_TAREGET_INDEX:
                            AttachObjectName = CHARA_PROP_NAME;
                            break;
                        default:
                            AttachObjectName = "Invalid";
                            break;
                    }

                    EditorGUILayout.LabelField("アタッチキャラ", AttachObjectName);
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// アタッチ対象の選択ボタン
                /// </summary>
                public void OnGUI_SetAttachTargetHeader(GUIContext ctx, CutInTimelineController controller)
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("アタッチ候補");
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// アタッチ対象の選択ボタン
                /// </summary>
                public void OnGUI_SetAttachTargetBackGround<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (GUILayout.Button(BACKGROUND_NAME,GUILayout.Width( TARGET_NAME_BUTTON_WIDTH)))
                    {
                        AttachIndex = BACKGROUND_TARGET_INDEX;
                        ctx.MultiKeyEditApply<T>(x => { x.AttachData.AttachIndex = AttachIndex; });
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// アタッチ対象の選択ボタン
                /// </summary>
                public void OnGUI_SetAttachTargetCharaProp<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    if (GUILayout.Button(CHARA_PROP_NAME,GUILayout.Width( TARGET_NAME_BUTTON_WIDTH)))
                    {
                        AttachIndex = CHARAPROP_TAREGET_INDEX;
                        ctx.MultiKeyEditApply<T>(x => { x.AttachData.AttachIndex = AttachIndex; });
                        onChange?.Invoke();
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// アタッチ対象の選択ボタン
                /// </summary>
                public void OnGUI_SetAttachTargetCharacter<T>(
                    GUIContext ctx,
                    CutInTimelineController controller,
                    System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }
                    var charaCount = ctx._curOnGUISheet._data._characterList.Count;
                    for (int i = 0; i < charaCount; ++i)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.Space();
                        var charaData = ctx._curOnGUISheet._data._characterList[i];
                        var charaId = charaData.Chara._cutInCharacter.Model.GetCharaID();
                        if (GUILayout.Button($"CharaId: {charaId}",GUILayout.Width( TARGET_NAME_BUTTON_WIDTH)))
                        {
                            AttachIndex = i;
                            ctx.MultiKeyEditApply<T>(x =>
                            {
                                x.AttachData.AttachIndex = AttachIndex;
                                x.AttachData.AttachObjectName = AttachObjectName;
                            });
                            onChange?.Invoke();
                        }

                        EditorGUILayout.EndHorizontal();
                    }
                }

                /// <summary>
                /// アタッチ対象の選択ボタン
                /// </summary>
                public void OnGUI_SetAttachTargetMiniCharacter<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }
                    var charaCount = ctx._curOnGUISheet._data.MiniCharacterDataList.Count;
                    for (int i = 0; i < charaCount; ++i)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.Space();
                        if (GUILayout.Button("MiniChara " + i))
                        {
                            AttachIndex = MINICHARA_TARGET_OFFSET + i;
                            ctx.MultiKeyEditApply<T>(x =>
                            {
                                x.AttachData.AttachIndex = AttachIndex;
                                x.AttachData.AttachObjectName = AttachObjectName;
                            });
                            onChange?.Invoke();
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                }

                /// <summary>
                /// 別のタイムラインが管理するオブジェクトへのアタッチ設定
                /// </summary>
                public void OnGUI_SearchAttachTarget<T>(GUIContext ctx, CutInTimelineController controller, System.Action onChange = null) where T : TimelineKeyTransformData
                {
                    if (!IsAttach)
                    {
                        return;
                    }

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    // ID可視化
                    using (new EditorGUI.DisabledScope(true))
                    {
                        ctx.ME_TextField("検索用Id", ref AttachLocatorSearchId);
                    }
                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.Space();
                    // アタッチ設定があるならここで検索してキャッシュ
                    if (_targetGameObject == null &&
                        (!string.IsNullOrEmpty(AttachLocator) ||
                         !string.IsNullOrEmpty(AttachLocatorSearchId)))
                    {
                        Transform transform = null;
                        switch (AttachIndex)
                        {
                            case BACKGROUND_TARGET_INDEX:
                                transform = controller.GetBackgroundTransform(AttachLocatorSearchId, AttachLocator);
                                break;
                            case CHARAPROP_TAREGET_INDEX:
                                transform = controller.GetCharaPropTransform(AttachLocatorSearchId, AttachLocator);
                                break;

                        }

                        if (transform != null)
                        {
                            _targetGameObject = transform.gameObject;
                        }
                    }

                    // TimelineKeyEffectDataの処理を移植
                    // ヒエラルキーからのD&Dまたは参照一覧からの更新を検知して
                    // 対象のカテゴリとノード名を保存する
                    // SearchIdは対象のオブジェクトを管理するキーデータに設定する
                    using (var changeScope = new EditorGUI.ChangeCheckScope())
                    {
                        _targetGameObject = EditorGUILayout.ObjectField("GameObject", _targetGameObject,
                            typeof(GameObject), allowSceneObjects: true) as GameObject;

                        if (changeScope.changed)
                        {
                            if (_targetGameObject != null)
                            {
                                if (controller.FindTrackingTargetParametersInBackground(
                                        _targetGameObject.transform, ref AttachLocatorSearchId,
                                    ref AttachLocator))
                                {
                                    AttachIndex = BACKGROUND_TARGET_INDEX;
                                }
                                else if (controller.FindTargetCharaProp(
                                             _targetGameObject.transform,
                                             ref AttachLocatorSearchId,
                                             ref AttachLocator))
                                {
                                    AttachIndex = CHARAPROP_TAREGET_INDEX;
                                }
                                else
                                {
                                    UnityEditor.EditorUtility.DisplayDialog("エラー", "GameObject指定はBackGroundとCharaPropに対応しています", "OK");
                                }
                            }
                            else
                            {
                                AttachLocator = string.Empty;
                            }

                            ctx.MultiKeyEditApply<T>(x =>
                            {
                                x.AttachData.AttachIndex = AttachIndex;
                                x.AttachData.AttachLocatorSearchId = AttachLocatorSearchId;
                                x.AttachData.AttachLocator = AttachLocator;
                            }
                        );
                            onChange?.Invoke();
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }

                /// <summary>
                /// アタッチモジュールのぱためーたー表示
                /// マルチキー編集をサポート
                /// </summary>
                public void OnGUI_All<T>(GUIContext ctx, CutInTimelineController controller) where T: TimelineKeyTransformData
                {
                    OnGUI_Header(ctx, controller);
                    EditorGUI.indentLevel++;
                    OnGUI_IsAttach<T>(ctx, controller);
                    OnGUI_IsApplyPosition<T>(ctx, controller);
                    OnGUI_IsApplyPositionAxis<T>(ctx, controller);
                    OnGUI_IsApplyRotation<T>(ctx, controller);
                    OnGUI_IsApplyScale<T>(ctx, controller);
                    OnGUI_DisplayTargetName(ctx, controller);
                    OnGUI_SetAttachTargetHeader(ctx, controller);
                    OnGUI_SetAttachTargetBackGround<T>(ctx, controller);
                    OnGUI_SetAttachTargetCharaProp<T>(ctx, controller);
                    OnGUI_SetAttachTargetCharacter<T>(ctx, controller);
                    OnGUI_SetAttachTargetMiniCharacter<T>(ctx, controller);
                    OnGUI_Locator<T>(ctx, controller);
                    OnGUI_LocatorPreset<T>(ctx, controller);
                    OnGUI_SearchAttachTarget<T>(ctx, controller);
                    OnGUI_Detach<T>(ctx, controller);
                    OnGUI_PreUpdate<T>(ctx, controller);
                    OnGUI_DetachStayTransform<T>(ctx, controller);
                    EditorGUI.indentLevel--;
                }
#endif
            }

        }//namespace Cutt
    }
}
