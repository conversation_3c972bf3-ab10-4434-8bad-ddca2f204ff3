using UnityEngine;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// CameraBackGroundの制御
            /// </summary>
            public partial class CutInTimelineController
            {
                /// <summary>
                /// 背景カメラ(カットを使うシーンによってはないこともあるので注意)
                /// </summary>
                private Camera _backGroundCamera = null;

                public Camera BackGroundCamera
                {
                    set
                    {
                        _backGroundCamera = value;
                    }
                    get
                    {
                        return _backGroundCamera;
                    }
                }

                /// <summary>
                /// カメラの背景色更新
                /// </summary>
                private void AlterUpdate_CameraBackGround(CutInTimelineWorkSheet sheet)
                {
                    if (sheet.CameraBackGroundKeys.IsNotPlayable(this))
                    {
                        return;
                    }

                    if (BackGroundCamera == null)
                    {
                        return;
                    }

                    CutInTimelineKey curKey = null;
                    CutInTimelineKey nextKey = null;
                    FindTimelineKey(out curKey, out nextKey, sheet.CameraBackGroundKeys, _currentFrame, availableFindKeyCache);
                    if (curKey == null)
                    {
                        return;
                    }

                    Color backGroundColorData = GameDefine.COLOR_BLACK;
                    var isOrthoSize = false;
                    var orthoSize = CutInHelper.BACKGROUND_CAMERA_ORTHOGRAPHIC_SIZE;
                    var curData = curKey as TimelineKeyCameraBackGroundData;
                    var nextData = nextKey as TimelineKeyCameraBackGroundData;
                    if ((nextData != null) && nextData.IsInterpolateKey())
                    {
                        //補間処理
                        var t = CalculateInterpolationValue(curData, nextData, _currentFrame);
                        backGroundColorData = Color.Lerp(curData.BackGroundColor, nextData.BackGroundColor,  t);

                        isOrthoSize = curData.IsOrthoSize && nextData.IsOrthoSize;
                        orthoSize = Mathf.Lerp(curData.OrthoSize, nextData.OrthoSize, t);
                    }
                    else
                    {
                        backGroundColorData = curData.BackGroundColor;
                        isOrthoSize = curData.IsOrthoSize;
                        orthoSize = curData.OrthoSize;
                    }

                    BackGroundCamera.backgroundColor = backGroundColorData;
                    if (isOrthoSize)
                    {
                        BackGroundCamera.orthographicSize = orthoSize;
                    }
                }
            }
        }
    }
}
