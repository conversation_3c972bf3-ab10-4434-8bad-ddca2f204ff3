using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            public partial class CutInTimelineController
            {
                #region 更新

                /// <summary>
                /// FovOffsetタイムラインの更新
                /// </summary>
                private void AlterUpdate_FovOffset(CutInTimelineWorkSheet sheet)
                {
                    var list = sheet.FovOffsetDataList;
                    int count =　list.Count;
                    for (int index = 0; index < count; ++index)
                    {
                        var data = list[index];
                        if (data == null)
                        {
                            continue;
                        }
                        data.AlterUpdate(this, _currentFrame);
                    }
                }

                #endregion 更新

            }
        }
    }
}
