using Gallop.Model;
using Gallop.Model.Component;
using UnityEngine;

namespace Gallop
{
    namespace CutIn
    {
        /// <summary>
        /// カットイン中のミニキャラ管理
        /// </summary>
        public sealed class CutInMiniCharacter : CutInCharacterBase
        {
            private MiniModelController _miniModel;
            public MiniModelController MiniModel => _miniModel;
            public override ModelControllerBehaviour ModelBehaviour => _miniModel;

            /// <summary>
            /// クリーンアップ
            /// </summary>
            public override void Cleanup()
            {
                 CleanupFootShadowController();
                _modelRendererHolder = null;
                if (_miniModel)
                {
                    GameObject.Destroy(_miniModel.gameObject);
                    _miniModel = null;
                }                
            }

            /// <summary>
            /// ミニキャラモデル生成
            /// </summary>
            public void CreateModel(CharacterBuildInfo info , Transform parent)
            {
                var model = ModelLoader.CreateModel(info);
                var controller = model.GetComponent<MiniModelController>();
                _miniModel = controller;
                var context = new CharaCutInAnimator.Context()
                {
                    IsCreateAnimator = true,
                    Animator = controller.Animator
                };
                _animator = controller.AddModelComponent(new MiniCharaCutInAnimator(ref context));
                controller.SetEnableRandomIdleEarMotion(false);
                controller.SetEnableRandomIdleTailMotion(false);
                IsEnableChangeCullingLayer = true;
                SetLayer(GraphicSettings.GetLayer(GraphicSettings.LayerIndex.LayerCutIn3D));
                SetParent(controller, parent);
            }

            /// <summary>
            /// セットアップ最終工程
            /// </summary>
            public void SetupModel()
            {
                if (MiniModel == null)
                {
                    return;
                }
                MiniModel.gameObject.SetActive(true);
                MiniModel.ResumeCySpring();
                SetupFootShadowController(MiniModel);
                _modelRendererHolder = MiniModel.GetModelComponent<RendererHolder>();
            }

            /// <summary>
            /// ModelにShadowControllerコンポーネントを付与する
            /// </summary>
            private void SetupFootShadowController(ModelControllerBehaviour model)
            {
                _isAddFootShadowController = false;
                if (!model.TryGetModelComponent(out _footShadowController))
                {
                    // CutInModelControllerならコンポーネントが追加される
                    var component = CharaModelUtil.AddTrainingShadowComponent(model);
                    if (component == null)
                    {
                        return;
                    }

                    _isAddFootShadowController = true;
                    _footShadowController = component;
                    _footShadowController.IsVisible = false;
                }
            }

            /// <summary>
            /// ステンシルOPを変更する
            /// </summary>
            public void SetStencilOp(UnityEngine.Rendering.CompareFunction compFunc, UnityEngine.Rendering.StencilOp op)
            {
                if (MiniModel == null)
                {
                    return;
                }

                if((_compareFunction == compFunc) && (_stencilOp == op))
                {
                    return;
                }

                _compareFunction = compFunc;
                _stencilOp = op;
                MiniModel.SetStencilOp(compFunc, op);
            }

            /// <summary>
            /// エミッシブカラーの更新
            /// </summary>
            public void SetEmissiveColor(Color color)
            {
                if (MiniModel == null)
                {
                    return;
                }
                MiniModel.SetEmissiveColor(color);
                MiniModel.UpdateMaterialPropertyBlock();
            }
        }
    }
}
