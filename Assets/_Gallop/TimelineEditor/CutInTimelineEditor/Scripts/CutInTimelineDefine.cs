using UnityEngine;
using System.Collections;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// タイムライン用定義
            /// </summary>
            public static class TimelineDefine
            {
                public const string SheetAssetLabel = "CuttSheet";
                public const int CameraMaxNum = 1;//Cutt管理カメラ（TimelineCamera）の最大数

                public const int BODY_ANIMATION_CLIP_NUM = 3; // 体モーションの数(3つあるが身長差差分対応なら２、比較身長差差分対応なら３)
                public const int BODY_ANIMATION_CLIP_HEIGHT_NUM = 2;            // 身長差差分対応
                public const int BODY_ANIMATION_CLIP_COMPARE_HEIGHT_NUM = 3;    // 比較身長差差分対応

#if UNITY_EDITOR
                public const string PathOfResourcesDirectory = "Assets/_GallopResources/Bundle/Resources";
                public const string DefaultCuttResourceDirectory = PathOfResourcesDirectory + "/Cutt";  //リソースは場合によって切り替わる

                public static string CuttResourceDirectory = DefaultCuttResourceDirectory;  //リソースは場合によって切り替わる

                public const string CuttEditorDirectory = "Assets/_Gallop/Editor/TimelineEditor/CutInTimelineEditor";
                public const string NewPrefabTemplatePath = CuttEditorDirectory + "/Prefab/TimelineControl_Template.prefab";
                public const string NewSheetName = "NewSheet";
                public const int KeyPropertyUndoLimit = 16;//KeyProperyUndoの最大数
                public const float KeyframeAreaWheelVScrollRate = 5f;
                public const float KeyframeAreaWheelHScrollRate = 5f;
                public const float KeyHitRectExtendValue = 3f;//このドット数分Keyへのクリック判定が拡大する

                #region EditorPrefsキー名
                //CuttEditor起動状態制御
                public const string PrefsKeyName_ToolLaunchControl = "TimelineEditor_Launch_Control";
                //ViewScaleの調整方法
                public const string PrefsKeyName_ViewScaleAdjustMethod = "TimelineEditor_MiscSettings";
                //シェルフ開閉状態
                public const string PrefsKeyName_WorkSheetOpenShelf = "TimelineEditor_OpenShelfStatus";
                //シート切り替え時に自動でクリーンアップするか
                public const string PREFS_KEY_NAME_AUTO_CLEANUP = "TimelineEditor_AutoCleanup";
                //EditSheetのKeyPropertyEditAreaの幅
                public const string PrefsKeyName_KeyPropAreaWidth = "TimelineEditor_WorkSheet_KeyPropAreaWidth";
                //Color検索時の近似率
                public const string PrefsKeyName_SearchColorNearRate = "Timeline_SearchKey_ColorNearRate";

                //SettingsWindowの各Foldout状態
                private const string SettingsEditorPrefsPrefix = "TimelineEditor_SettingsWindow_";
                public const string PrefsKeyName_SettingsFoldoutCutt = SettingsEditorPrefsPrefix + "VisCuttSettings";
                public const string PrefsKeyName_SettingsFoldoutChara = SettingsEditorPrefsPrefix + "VisCharaSettings";
                public const string PrefsKeyName_SettingsFoldoutSheet = SettingsEditorPrefsPrefix + "VisSheetSettings";
                public const string PrefsKeyName_SettingsFoldoutMisc = SettingsEditorPrefsPrefix + "MiscSettings";
                public const string PrefsKeyName_SettingsFoldoutImageEffect= SettingsEditorPrefsPrefix + "ImageEffectSettings";
                public const string PrefsKeyName_SettingsFoldoutHotKey = SettingsEditorPrefsPrefix + "VisHotKeySettings";
                public const string PrefsKeyName_SettingsFoldoutLineFilter = SettingsEditorPrefsPrefix + "VisLineFilterSettings";
                public const string PrefsKeyName_SettingsFoldoutTraining = SettingsEditorPrefsPrefix + "TrainingSettings";
                public const string PrefsKeyName_SettingsFoldoutA2U = SettingsEditorPrefsPrefix + "A2USettings";
                public const string PrefsKeyName_SettingsFoldoutPrefabTimeline = SettingsEditorPrefsPrefix + "PrefabTimelineSettings";
                public const string PrefsKeyName_SettingsFoldoutCutSettingsCommon = SettingsEditorPrefsPrefix + "VisCommonCutSettings";
                public const string PrefsKeyName_SettingsFoldoutCutSettingsSkill = SettingsEditorPrefsPrefix + "VisSkillCutSettings";
                public const string PrefsKeyName_SettingsFoldoutCutSettingsSingleModeTraining = SettingsEditorPrefsPrefix + "VisSingleModeTrainingCutSettings";
                public const string PrefsKeyName_SettingsFoldoutCutSettingsResult = SettingsEditorPrefsPrefix + "VisResultCutSettings";
                public const string PrefsKeyName_SettingsFoldoutCutSettingsSet = SettingsEditorPrefsPrefix + "VisSetCutSettings";
                public const string PrefsKeyName_SettingsFoldoutCySpringControl = SettingsEditorPrefsPrefix + "CySpringControlSettings";
                public const string PrefsKeyName_SettingsLiveLight = SettingsEditorPrefsPrefix + "LiveLight";
                public const string PrefsKeyName_SettingsFoldoutLightShafts = SettingsEditorPrefsPrefix + "LightShafts";
                public const string PrefsKeyName_SettingsFoldoutBlinkLight = SettingsEditorPrefsPrefix + "BlinkLight";
                public const string PrefsKeyName_SettingsFoldoutSpotLight = SettingsEditorPrefsPrefix + "SpotLight";
                public const string PrefsKeyName_SettingsFoldoutLightProjection = SettingsEditorPrefsPrefix + "LightProjection";
                public const string PrefsKeyName_SettingsFoldoutMirrorScanLight = SettingsEditorPrefsPrefix + "MirrorScanLight";
                public const string PrefsKeyName_SettingsMonitorCamera = SettingsEditorPrefsPrefix + "MonitorCamera";
                public const string PrefsKeyName_SettingsEffectCamera = SettingsEditorPrefsPrefix + "EffectCamera";

                // 実行時に別途生成するオブジェクト
                public const string PrefsKeyName_ExtraObject = "{0}ExtraObject{1}";
                public const string PrefsKeyName_ExtraObjectCount = "{0}ExtraObjectCount";

                // Cutt検索時のフィルタ
                public const string PrefsKeyName_CuttNameFilter = "TimelineEditor_CuttNameFilter";

                // 速度グラフのスケール値
                public const string PrefsKeyName_SpeedGraphSpeedScale = "TimelineEditor_SpeedGraph_SpeedScale{0}";
                public const string PrefsKeyName_SpeedGraphFrameScale = "TimelineEditor_SpeedGraph_FrameScale{0}";
                #endregion EditorPrefsキー名

                public static readonly Color[] KeyPropertySectionColors = new Color[(int)CutInTimelineKeyDataType.Max] {
                    CutInCutTool.GenColor(190, 225, 235),//CameraPos,
                    CutInCutTool.GenColor(235, 190, 211),//CameraLookAt,
                    CutInCutTool.GenColor(190, 235, 209),//CameraFov,
                    CutInCutTool.GenColor(190, 235, 209),//CameraRoll,
                    CutInCutTool.GenColor(233, 235, 191),//Event,
                    // Gallop CutIn Timeline
                    CutInCutTool.GenColor(190, 235, 209),//Character,
                    CutInCutTool.GenColor(190, 235, 209),//CharacterMotion,
                    CutInCutTool.GenColor(190, 235, 209),//CameraMotion,
                    CutInCutTool.GenColor(190, 235, 209),//Sound,
                    CutInCutTool.GenColor(190, 209, 235),//Prefab,
                    CutInCutTool.GenColor(190, 235, 209),//BackGround,
                    CutInCutTool.GenColor(190, 235, 209),//PostEffect,
                    CutInCutTool.GenColor(190, 235, 209),//TimeScale,
                    CutInCutTool.GenColor(190, 235, 209),//FootSmoke,
                    CutInCutTool.GenColor(190, 235, 209),//Light
                    CutInCutTool.GenColor(190, 235, 209),//Fog
                    CutInCutTool.GenColor(190, 235, 209),//Wind
                    CutInCutTool.GenColor(190, 235, 209),//CharacterShadow,
                    CutInCutTool.GenColor(190, 235, 209),//CameraParam,
                    CutInCutTool.GenColor(190, 235, 209),//CharacterSweat,
                    CutInCutTool.GenColor(190, 235, 209),//Reflection
                    CutInCutTool.GenColor(190, 235, 209),//A2UConfig
                    CutInCutTool.GenColor(190, 235, 209),//A2U
                    CutInCutTool.GenColor(190, 235, 209),//A2UComposition
                    CutInCutTool.GenColor(190, 235, 209),//Collision,
                    CutInCutTool.GenColor(190, 235, 209),//Word,
                    CutInCutTool.GenColor(190, 235, 209),//CharacterColor,
                    CutInCutTool.GenColor(190, 235, 209),//CharacterIK,
                    CutInCutTool.GenColor(190, 235, 209),//SpringCollision,
                    CutInCutTool.GenColor(190, 235, 209),//CameraCharaHeight
                    CutInCutTool.GenColor(190, 235, 209),//MultiLightShadow
                    CutInCutTool.GenColor(190, 235, 209),//AdditionalLight
                    CutInCutTool.GenColor(190, 235, 209),//MultiCameraEnable
                    CutInCutTool.GenColor(190, 235, 209),
                    CutInCutTool.GenColor(190, 235, 209),//CharacterEye
                    CutInCutTool.GenColor(190, 235, 209),//CharaProp
                    CutInCutTool.GenColor(190, 235, 209),//Audience
                    CutInCutTool.GenColor(190, 235, 209),//CyalumeControl
                    CutInCutTool.GenColor(190, 235, 209),//MobControl
                    CutInCutTool.GenColor(190, 235, 209),//ChoreographyCyalume
                    CutInCutTool.GenColor(190, 235, 209),//MonitorCameraEnable
                    CutInCutTool.GenColor(190, 235, 209),//CharacterParts
                    CutInCutTool.GenColor(190, 235, 209),//SupportCard
                    CutInCutTool.GenColor(190, 235, 209),//MaskCamera
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterModel
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterMotion
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterColor
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterShadow
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterParts
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterWind
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterCollision
                    CutInCutTool.GenColor(190, 235, 209),//MiniCharacterSpringCollision
                    CutInCutTool.GenColor(190, 235, 209),//LightShafts
                    CutInCutTool.GenColor(190, 235, 209),//BlinkLight
                    CutInCutTool.GenColor(190, 235, 209),//MirrorScanLight
                    CutInCutTool.GenColor(190, 235, 209),//LightProjection
                    CutInCutTool.GenColor(190, 235, 209),//SpotLight
                    CutInCutTool.GenColor(190, 235, 209),//CameraLayer
                    CutInCutTool.GenColor(190, 235, 209),//CharacterDirt
                    CutInCutTool.GenColor(190, 235, 209),//BackGround2D,
                    CutInCutTool.GenColor(190, 235, 209),//StencilMask,
                    CutInCutTool.GenColor(190, 235, 209),//UVScrollLight,
                    CutInCutTool.GenColor(190, 235, 209),//CharacterNode
                    CutInCutTool.GenColor(190, 235, 209),//CharacterAura
                    CutInCutTool.GenColor(190, 235, 209),//CharacterMotionBlend
                    CutInCutTool.GenColor(190, 235, 209),//FovOffset
                    CutInCutTool.GenColor(190, 235, 209),//SuperviseHeader
                    CutInCutTool.GenColor(190, 235, 209),//SuperviseEyeMotion
                    CutInCutTool.GenColor(190, 235, 209),//SuperviseEyebrowMotion
                    CutInCutTool.GenColor(190, 235, 209),//SuperviseMouthMotion
                    CutInCutTool.GenColor(190, 235, 209),//SuperviseEyeTarget
                    CutInCutTool.GenColor(190, 235, 209), //Hologram
                    CutInCutTool.GenColor(200, 235, 209), //CutInCameraBackGround
                    CutInCutTool.GenColor(190, 235, 209),//CharacterScreenMapping
                    CutInCutTool.GenColor(190, 235, 209),//CharacterAlpha
                    CutInCutTool.GenColor(190, 235, 209),//AudienceModel
                    CutInCutTool.GenColor(190, 235, 209),//AudienceMotion
                    CutInCutTool.GenColor(190, 235, 209),//AudienceColor
                    CutInCutTool.GenColor(190, 235, 209),//CharacterScaleFactor
                };

                //Bookmarkリストとかで、選択状態のLabelを描画するときに使用するBackgroundColor
                public static readonly Color EditorSelectedLineBgColor = new Color(0.1f, 0.1f, 0.9f, 0.8f);

                public static void ChangeDirectory(string path = DefaultCuttResourceDirectory)
                {
                    CuttResourceDirectory = path;
                }

#endif//UNITY_EDITOR

                /// <summary>
                /// Static コンストラクタ
                /// </summary>
                static TimelineDefine()
                {
#if UNITY_EDITOR
#endif//UNITY_EDITOR
                }
            }

            //--------------------------------------------------------------------------------
            public static class CuttVector3_Helper
            {
                public static Vector3 Round(this Vector3 This)
                {
                    var kFigure = 100f * 10f;// 1/1000 == 1mm
                    This.x = Mathf.Round(This.x * kFigure) / kFigure;
                    This.y = Mathf.Round(This.y * kFigure) / kFigure;
                    This.z = Mathf.Round(This.z * kFigure) / kFigure;
                    return This;
                }
            }

            //--------------------------------------------------------------------------------
            public static class CuttRect_Helper
            {
                //Center基準で拡張
                public static Rect Extend(this Rect rect, float extendVal)
                {
                    var extendHalf = extendVal / 2f;
                    rect.x -= extendHalf;
                    rect.y -= extendHalf;
                    rect.width += extendVal;
                    rect.height += extendVal;
                    return rect;
                }

                public static Rect GetTopEdge(this Rect rect, float width)
                {
                    rect.height = width;
                    return rect;
                }
                public static Rect GetLeftEdge(this Rect rect, float width)
                {
                    rect.width = width;
                    return rect;
                }
                public static Rect GetRightEdge(this Rect rect, float width)
                {
                    rect.x = rect.x + rect.width - width;
                    rect.width = width;
                    return rect;
                }
                public static Rect GetBottomEdge(this Rect rect, float width)
                {
                    rect.y = rect.y + rect.height - width;
                    rect.height = width;
                    return rect;
                }
            }

            //--------------------------------------------------------------------------------
            public class DropOutStack<T>
                where T : class
            {
                T[] Items;
                private int Top = 0;
                public int Count = 0;
                int Capacity = 0;

                public DropOutStack(int capacity)
                {
                    Items = new T[capacity];
                    Capacity = capacity;
                }

                public void Push(T item)
                {
                    Items[Top] = item;
                    Top = (Top + 1) % Capacity;
                    Count = System.Math.Min((Count + 1), Capacity);
                }

                public T Pop()
                {
                    Top = (Capacity + Top - 1) % Capacity;
                    Count = System.Math.Max((Count - 1), 0);
                    var item = Items[Top];
                    Items[Top] = null;
                    return item;
                }

                public T At(int index)
                {
                    if (index < Count)
                    {
                        int head = (Capacity + Top - 1) % Capacity;
                        index = (index > head) ? Capacity + (head - index) : head - index;
                        return Items[index];
                    }
                    return null;
                }

                public void Clear()
                {
                    for (int i = 0; i < Capacity; i++)
                    {
                        Items[i] = null;
                    }
                    Top = 0;
                    Count = 0;
                }
            }

            //--------------------------------------------------------------------------------
            public static class FNVHash
            {
                static uint FNV_OFFSET_BASIS_32 = 2166136261U;
                static uint FNV_PRIME_32 = 16777619U;
                public static int Generate(string seed)
                {
                    var bytes = System.Text.Encoding.UTF8.GetBytes(seed);
                    var hash = FNV_OFFSET_BASIS_32;
                    for (int i = 0; i < bytes.Length; i++)
                    {
                        hash = (FNV_PRIME_32 * hash) ^ bytes[i];
                    }
                    return (int)hash;
                }
            }

        }//namespace Cutt
    }
}
