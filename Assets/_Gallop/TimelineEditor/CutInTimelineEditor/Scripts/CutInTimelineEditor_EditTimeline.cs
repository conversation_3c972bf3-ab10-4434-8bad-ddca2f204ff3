#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Gallop
{
    namespace CutIn
    {

        namespace Cutt
        {
            /// <summary>
            /// TimelineとTimeline配列を所持するクラスの抽象基底クラス
            /// </summary>
            public interface IEditTimeline
            {
                bool CheckKeyDataType(CutInTimelineKeyDataType dataType);
                string name { get; set; }
            }

            /// <summary>
            /// Timelineを複数所持するやつのInterface
            /// </summary>
            public interface IEditTimelinesHolder : IEditTimeline
            {
                /// <summary>
                /// 各Line
                /// </summary>
                EditTimeline[] editLines { get; }

                /// <summary>
                /// グループ表示のフラグ（親）
                /// </summary>
                bool guiFoldout { get; set; }
                /// <summary>
                /// グループ表示のフラグ（子）
                /// </summary>
                bool[] subGuiFoldoutArray { get; set; }
            }

            /// <summary>
            /// ジェネリックな（型の違う）TimelineGroupを統一して扱うためのInterface
            /// </summary>
            public interface IEditTimelineGroup : IEditTimelinesHolder
            {
                EditTimeline AddEditLine();
                EditTimeline InsertEditLine(int index);
                void RemoveEditLine(EditTimeline removeLine);
                bool allowEmpty { get; set; }
                void SwapLine(int index1, int index2);
            }

            /// <summary>
            /// ジェネリックな（型の違う）TimelineSetGroupを統一して扱うためのInterface
            /// </summary>
            public interface IEditTimelineSetGroup : IEditTimelineGroup
            {
                List<EditTimeline> AddEditLineSet();
                List<EditTimeline> GetLastEditLineSet();
                int itemNum { get; }
                int GetKeyTypeNum(int groupNo);
                int GetStartIndex(int groupNo);
                int GetEndIndex(int groupNo);
                int GetGroupNoFromIndex(int index);
                void AddExtraEditLine(EditTimeline srcEditLine,IEditTimelineSetGroup newTimeline,List<EditTimeline> editLine);
            }

            /// <summary>
            /// TimelineSet
            /// 増減しない静的に決められたTimeline配列を所持する
            /// UI的には折りたたみ可能になっている
            /// </summary>
            public class EditTimelineSet : IEditTimelinesHolder
            {
                public bool CheckKeyDataType(CutInTimelineKeyDataType dataType) { return false; }

                private string _name = "";
                private bool _guiFoldout = true;
                List<EditTimeline> _editLines = new List<EditTimeline>();
                private EditSheet _editSheet = null;

                public string name { get { return _name; } set { _name = value; } }
                public bool guiFoldout { get { return _guiFoldout; } set { _guiFoldout = value; } }
                public bool[] subGuiFoldoutArray { get; set; }
                public EditTimeline[] editLines { get { return _editLines.ToArray(); } }
                public EditSheet editSheet { get { return _editSheet; } set { _editSheet = value; } }

                /// <summary>
                /// コンストラクタ
                /// </summary>
                /// <param name="editSheet_"></param>
                /// <param name="keysArray_"></param>
                /// <param name="keyTypeArray_"></param>
                /// <param name="keysNames_"></param>
                public EditTimelineSet(
                    EditSheet editSheet_,
                    //下記3つは同じサイズである必要がある
                    ITimelineKeyDataList[] keysArray_,
                    CutInTimelineKeyDataType[] keyTypeArray_,
                    string[] keysNames_ = null
                    )
                {
                    _editSheet = editSheet_;
                    if (keysArray_.Length != keyTypeArray_.Length)
                    {
                        Debug.LogError("Array size missmatch.");
                        Debug.Break();
                    }
                    if (keysNames_ != null)
                    {
                        if (keysArray_.Length != keysNames_.Length)
                        {
                            Debug.LogError("Array size missmatch names.");
                            Debug.Break();
                        }
                    }
                    for (int i = 0; i < keysArray_.Length; i++)
                    {
                        var keys = keysArray_[i];
                        var keyType = keyTypeArray_[i];

                        var line = EditTimeline.Create(_editSheet, keys, keyType);
                        line._owner = this;
                        if (keysNames_ != null)
                        {
                            line.name = keysNames_[i];
                        }
                        _editLines.Add(line);
                    }
                }

                public int IndexOf(EditTimeline editLine)
                {
                    return _editLines.IndexOf(editLine);
                }
            }

            /// <summary>
            /// TimelineGroup
            /// 任意の型のTimelineの動的な配列を所持する
            /// UI的には折りたたみ可能で。「Addボタン」を備える
            /// </summary>
            /// <typeparam name="TypeTimeline"></typeparam>
            public class EditTimelineGroup<TypeTimeline>
                : IEditTimelineGroup
                where TypeTimeline : ITimelineGroupData, new()
            {
                public bool CheckKeyDataType(CutInTimelineKeyDataType dataType) { return _dataType == dataType; }

                private string _name = "";
                private bool _guiFoldout = true;
                private bool _allowEmpty = false;//Trueの場合editLine無しを許容する
                List<EditTimeline> _editLines = new List<EditTimeline>();
                List<TypeTimeline> _timelineDataList;
                CutInTimelineKeyDataType _dataType;
                private EditSheet _editSheet = null;

                public string name { get { return _name; } set { _name = value; } }
                public bool guiFoldout { get { return _guiFoldout; } set { _guiFoldout = value; } }
                public bool[] subGuiFoldoutArray { get; set; }
                public bool allowEmpty { get { return _allowEmpty; } set { _allowEmpty = value; } }
                public EditTimeline[] editLines { get { return _editLines.ToArray(); } }
                public EditSheet editSheet { get { return _editSheet; } set { _editSheet = value; } }
                public event Action<EditTimeline, int> OnRemoveEditLine;
                /// <summary>
                /// タイムライン読み込み時、作成時両方で呼ばれる
                /// </summary>
                public event Action<EditTimeline, TypeTimeline> OnCreateEditLine;
                /// <summary>
                /// タイムライン新規作成時に呼ばれる
                /// </summary>
                public event Action<EditTimeline, TypeTimeline> OnCreateNewEditLine;

                /// <summary>
                /// コンストラクタ
                /// </summary>
                /// <param name="editSheet_"></param>
                /// <param name="list"></param>
                /// <param name="dataType"></param>
                public EditTimelineGroup(EditSheet editSheet_, List<TypeTimeline> list, CutInTimelineKeyDataType dataType)
                {
                    _timelineDataList = list;
                    _dataType = dataType;
                    _editSheet = editSheet_;
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                public void Init()
                {
                    if (_timelineDataList.Count > 0)
                    {
                        foreach (var tl in _timelineDataList)
                        {
                            var el = CreateEditTimeline(tl, _dataType);
                            if (OnCreateEditLine != null)
                            {
                                OnCreateEditLine(el, tl);
                            }
                            _editLines.Add(el);
                        }
                    }
                    else
                    {
                        if (!_allowEmpty)
                        {
                            //初期データは空なので、１つ追加してやる
                            AddEditLine();
                        }
                    }
                }

                /// <summary>
                /// タイムライン要素の入れ替え
                /// </summary>
                /// <param name="index1"></param>
                /// <param name="index2"></param>
                public void SwapLine(int index1, int index2)
                {
                    if(index1 < 0 || index1 >= _timelineDataList.Count)
                    {
                        return;
                    }

                    if (index2 < 0 || index2 >= _timelineDataList.Count)
                    {
                        return;
                    }

                    var data = _timelineDataList[index1];
                    _timelineDataList[index1] = _timelineDataList[index2];
                    _timelineDataList[index2] = data;

                    var dataLine = _editLines[index1];
                    _editLines[index1] = _editLines[index2];
                    _editLines[index2] = dataLine;
                }

                /// <summary>
                /// タイムライン追加
                /// </summary>
                /// <returns></returns>
                public EditTimeline AddEditLine()
                {
                    var newData = new TypeTimeline();
                    _timelineDataList.Add(newData);
                    var addLine = CreateEditTimeline(newData, _dataType);
                    if (addLine == null)
                    {
                        Debug.LogError("addline is null");
                    }
                    if (OnCreateEditLine != null)
                    {
                        OnCreateEditLine(addLine, newData);
                    }
                    OnCreateNewEditLine?.Invoke(addLine, newData);
                    _editLines.Add(addLine);
                    return addLine;
                }

                /// <summary>
                /// タイムライン挿入
                /// </summary>
                /// <returns></returns>
                public EditTimeline InsertEditLine(int index)
                {
                    var newData = new TypeTimeline();
                    _timelineDataList.Insert(index, newData);
                    var insertLine = CreateEditTimeline(newData, _dataType);
                    if (insertLine == null)
                    {
                        Debug.LogError("insertLine is null");
                    }
                    if (OnCreateEditLine != null)
                    {
                        OnCreateEditLine(insertLine, newData);
                    }
                    OnCreateNewEditLine?.Invoke(insertLine, newData);
                    _editLines.Insert(index, insertLine);
                    return insertLine;
                }

                /// <summary>
                /// タイムライン削除
                /// </summary>
                /// <param name="removeLine"></param>
                public void RemoveEditLine(EditTimeline removeLine)
                {
                    if (!_allowEmpty)
                    {
                        if (_editLines.Count < 2)
                        {
                            return;
                        }
                    }
                    var index = _editLines.IndexOf(removeLine);
                    if (index >= 0)
                    {
                        if (index < _timelineDataList.Count)
                        {
                            _timelineDataList.RemoveAt(index);
                            if (OnRemoveEditLine != null)
                            {
                                OnRemoveEditLine(removeLine, index);
                            }
                        }
                        _editLines.Remove(removeLine);
                    }
                }

                /// <summary>
                /// タイムラインオブジェクト生成
                /// </summary>
                /// <param name="timelineData"></param>
                /// <param name="type"></param>
                /// <returns></returns>
                private EditTimeline CreateEditTimeline(TypeTimeline timelineData, CutInTimelineKeyDataType type)
                {
                    EditTimeline line = null;
                    line = EditTimeline.Create(_editSheet, timelineData.Keys, type);
                    line._owner = this;
                    return line;
                }
            }

            /// <summary>
            /// TimelineSetGroup
            /// 擬似的にTimelineSetをグループ化
            /// </summary>
            /// <typeparam name="TypeTimelineSet"></typeparam>
            public class EditTimelineSetGroup<TypeTimelineSet>
                : IEditTimelineSetGroup
                where TypeTimelineSet : ITimelineSetGroupData, new()
            {
                /// <summary>
                /// タイムラインを生成した時の操作
                /// データ読み込みのタイミングで作られたのか、ユーザー操作で新たに追加されたのか
                /// </summary>
                public enum CreateType
                {
                    Init,       //初期化
                    Add,        //追加
                    Insert,     //差し込み
                }

                public bool CheckKeyDataType(CutInTimelineKeyDataType dataType) { return false; }

                private string _name = "";
                private bool _guiFoldout = true;
                private bool _allowEmpty = false;//Trueの場合editLine無しを許容する
                List<EditTimeline> _editLines = new List<EditTimeline>();
                List<TypeTimelineSet> _timelineSetList;
                private EditSheet _editSheet = null;

                public event Action<EditTimeline, IEditTimelineSetGroup, List<EditTimeline>> OnCreateExtraEditLine;

                public string name { get { return _name; } set { _name = value; } }
                public bool guiFoldout { get { return _guiFoldout; } set { _guiFoldout = value; } }
                public bool[] subGuiFoldoutArray { get; set; }
                public bool allowEmpty { get { return _allowEmpty; } set { _allowEmpty = value; } }
                public EditTimeline[] editLines { get { return _editLines.ToArray(); } }
                public T GetEditLine<T>()
                    where T : EditTimeline
                {
                    for ( int i = 0, length = editLines.Length ; i < length ;++i )
                    {
                        T ret = editLines[i] as T;
                        if (ret != null)
                        {
                            return ret;
                        }
                    }
                    return null;
                }
                public EditSheet editSheet { get { return _editSheet; } set { _editSheet = value; } }
                public event Action<EditTimeline, TypeTimelineSet, CreateType, int> OnCreateEditLine;

                public int keyTypeNum { get { return _timelineSetList == null || _timelineSetList.Count < 1 ? 0 : _timelineSetList[0].GetKeyTypeNum(); } }

                public int GetKeyTypeNum(int groupNo)
                {
                    if (_timelineSetList == null || _timelineSetList.Count < groupNo)
                        return 0;
                    return _timelineSetList[groupNo].GetKeyTypeNum();
                }

                public int itemNum
                {
                    get
                    {
                        if (_timelineSetList == null)
                            return 0;

                        return _timelineSetList.Count;
                    }
                }

                public int GetStartIndex(int groupNo)
                {
                    int startNo = 0;

                    if (_timelineSetList == null || _timelineSetList.Count < groupNo)
                        return 0;

                    for(int i=0;i<groupNo;i++)
                    {
                        startNo += _timelineSetList[i].GetKeyTypeNum();
                    }
                    return startNo;
                }

                public int GetEndIndex(int groupNo)
                {
                    int endNo = 0;

                    if (_timelineSetList == null || _timelineSetList.Count <= groupNo)
                        return 0;

                    for (int i = 0; i <= groupNo; i++)
                    {
                        endNo += _timelineSetList[i].GetKeyTypeNum();
                    }
                    return endNo;
                }

                public int GetGroupNoFromIndex(int index)
                {
                    int num = itemNum;
                    for (int i = 0; i < num; i++)
                    {
                        if (GetStartIndex(i) <= index && index < GetEndIndex(i))
                        {
                            return i;
                        }
                    }
                    return 0;
                }

                /// <summary>
                /// タイムライン要素の入れ替え
                /// </summary>
                /// <param name="index1"></param>
                /// <param name="index2"></param>
                public void SwapLine(int index1, int index2)
                {
                    if (index1 < 0 || index1 >= _timelineSetList.Count)
                    {
                        return;
                    }

                    if (index2 < 0 || index2 >= _timelineSetList.Count)
                    {
                        return;
                    }

                    var data = _timelineSetList[index1];
                    _timelineSetList[index1] = _timelineSetList[index2];
                    _timelineSetList[index2] = data;

                    var dataLine = _editLines[index1];
                    _editLines[index1] = _editLines[index2];
                    _editLines[index2] = dataLine;
                }

                /// <summary>
                /// コンストラクタ
                /// </summary>
                /// <param name="editSheet"></param>
                /// <param name="list"></param>
                public EditTimelineSetGroup(EditSheet editSheet, List<TypeTimelineSet> list)
                {
                    _timelineSetList = list;
                    _editSheet = editSheet;
                }

                public TypeTimelineSet GetLastTimelineSet()
                {
                    return _timelineSetList.Last();
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                public void Init()
                {
                    //Init時には内容を一旦クリアする
                    _editLines.Clear();
                    if (_timelineSetList.Count > 0)
                    {
                        int index = 0;
                        foreach (var tlSet in _timelineSetList)
                        {
                            for (int i = 0; i < tlSet.GetKeyTypeNum(); ++i)
                            {
                                var el = CreateEditTimeline(tlSet.GetKeyList(i), tlSet.GetKeyType(i), tlSet.GetArgumentValue(i));
                                if (OnCreateEditLine != null)
                                    OnCreateEditLine(el, tlSet, CreateType.Init, index);
                                _editLines.Add(el);
                            }
                            index++;
                        }
                    }
                    else
                    {
                        if (!_allowEmpty)
                        {
                            //初期データは空なので、１つ追加してやる
                            AddEditLine();
                        }
                    }
                }

                public void SwapEditLine(EditTimeline timeline,int index)
                {
                    if (_editLines == null)
                        return;

                    if (_editLines.Count <= index)
                        return;

                    _editLines[index] = timeline;
                }

                /// <summary>
                /// タイムラインをセットで追加（IEditTimelineSetGroup互換の都合上Firstを返す）
                /// </summary>
                /// <returns></returns>
                public EditTimeline AddEditLine()
                {
                    return AddEditLineSet().First();
                }

                /// <summary>
                /// タイムラインをセットで追加し、リストで返す
                /// </summary>
                /// <returns></returns>
                public List<EditTimeline> AddEditLineSet()
                {
                    var newData = new TypeTimelineSet();
                    _timelineSetList.Add(newData);
                    int index = _timelineSetList.IndexOf(newData);

                    List<EditTimeline> addEditlineList = new List<EditTimeline>();
                    for (int i = 0; i < newData.GetKeyTypeNum(); ++i)
                    {
                        var addLine = CreateEditTimeline(newData.GetKeyList(i), newData.GetKeyType(i));
                        if (addLine == null)
                        {
                            Debug.LogError("addline is null");
                        }
                        if (OnCreateEditLine != null)
                        {
                            OnCreateEditLine(addLine, newData, CreateType.Add, index);
                        }
                        _editLines.Add(addLine);
                        addEditlineList.Add(addLine);
                    }

                    return addEditlineList;
                }

                /// <summary>
                /// タイムラインをセットで挿入（IEditTimelineSetGroup互換の都合上Firstを返す）
                /// </summary>
                /// <returns></returns>
                public EditTimeline InsertEditLine(int index)
                {
                    return InsertEditLineSet(index).First();
                }
                /// <summary>
                /// タイムラインをセットで挿入し、リストで返す
                /// </summary>
                /// <returns></returns>
                public List<EditTimeline> InsertEditLineSet(int index)
                {
                    index /= 2;

                    var newData = new TypeTimelineSet();
                    _timelineSetList.Insert(index, newData);

                    List<EditTimeline> insertEditlineList = new List<EditTimeline>();
                    for (int i = 0; i < newData.GetKeyTypeNum(); ++i)
                    {
                        var insertLine = CreateEditTimeline(newData.GetKeyList(i), newData.GetKeyType(i));
                        if (insertLine == null)
                        {
                            Debug.LogError("insertLine is null");
                        }
                        if (OnCreateEditLine != null)
                        {
                            OnCreateEditLine(insertLine, newData, CreateType.Insert, index);
                        }
                        _editLines.Insert(index * newData.GetKeyTypeNum() + i, insertLine);
                        insertEditlineList.Add(insertLine);
                    }

                    return insertEditlineList;
                }

                /// <summary>
                /// タイムライン削除
                /// </summary>
                /// <param name="removeLine"></param>
                public void RemoveEditLine(EditTimeline removeLine)
                {
                    if (!_allowEmpty)
                    {
                        if (_timelineSetList.Count <= 1)
                            return;
                    }
                    var index = _editLines.IndexOf(removeLine);
                    if (index >= 0 && _timelineSetList.Count > 0)
                    {
                        int groupNo = GetGroupNoFromIndex(index);

                        int startIndex = GetStartIndex(groupNo);
                        int keyTypeNum = GetKeyTypeNum(groupNo);
                        if (startIndex >= 0 && keyTypeNum > 0)
                        {
                            _editLines.RemoveRange(startIndex, keyTypeNum);
                        }

                        if (groupNo < _timelineSetList.Count)
                        {
                            _timelineSetList[groupNo].OnDelete();
                            _timelineSetList.RemoveAt(groupNo);
                        }
                    }
                }

                /// <summary>
                /// タイムラインオブジェクト生成
                /// </summary>
                /// <param name="keyList"></param>
                /// <param name="type"></param>
                /// <returns></returns>
                private EditTimeline CreateEditTimeline(ITimelineKeyDataList keyList, CutInTimelineKeyDataType type, object argumentValue = null)
                {
                    EditTimeline line = null;
                    line = EditTimeline.Create(_editSheet, keyList, type, argumentValue);
                    line._owner = this;
                    return line;
                }

                /// <summary>
                /// 最後に追加したタイムラインセットを取得
                /// </summary>
                /// <returns></returns>
                public List<EditTimeline> GetLastEditLineSet()
                {
                    List<EditTimeline> editlineList = new List<EditTimeline>();
                    for (int i = _editLines.Count - keyTypeNum; i < _editLines.Count; ++i)
                    {
                        editlineList.Add(_editLines[i]);
                    }
                    return editlineList;
                }

                public void AddExtraEditLine(EditTimeline srcEditLine,IEditTimelineSetGroup newTimeline,List<EditTimeline> editLine)
                {
                    if(OnCreateExtraEditLine != null)
                    {
                        OnCreateExtraEditLine(srcEditLine, newTimeline,editLine);
                    }
                }

            }

            /// <summary>
            /// Timeline具象クラス
            /// </summary>
            public class EditTimeline : IEditTimeline
            {
                public virtual ITimelineGroupDataBase GetGroupData()
                {
                    return null;
                }

                protected virtual void SetArgumentValue(object value)
                {

                }

                public bool CheckKeyDataType(CutInTimelineKeyDataType dataType) { return dataType == keyDataType; }

                protected string _name = "";
                public virtual string name { get { return _name; } set { _name = value; } }

                //存在するEditTimelineを一意に識別するためのID
                //このIDはシリアライズされないため、ツール起動毎に異なる可能性があるので注意
                public int ID;
                private static int IDSeed = 0;
                //TimelineDataType
                public CutInTimelineKeyDataType keyDataType { get { return _keyDataType; } }
                private CutInTimelineKeyDataType _keyDataType = CutInTimelineKeyDataType.Event;
                //常にFrameの昇順に整列されている
                public ITimelineKeyDataList _editKeyList = null;
                //Frame数からランダムアクセス可能なように用意
                public Dictionary<int, CutInTimelineKey> _editKeyFrameDict = new Dictionary<int, CutInTimelineKey>();

                //このEditTimelineを保持するHolder。Holderに保持されていない場合Null
                public IEditTimelinesHolder _owner = null;
                //このEditTimelineが所属するシート
                public EditSheet _editSheet = null;

                //タイムラインの横に表示されるラベル
                public virtual string timelineLabel { get { return name; } }

                public virtual bool IsSuperviseTimeline => false;
                public virtual bool IsCustomizedDrawTimeline => false;
                public virtual bool IsSkipShortcutTimeline => false;
                public virtual bool IsSkipDisplayTimeline => false;

                //各KeyDataTypeで生成するEditTimeline派生クラスのType配列
                //Nullが指定されたKeyDataTypeはEditTimelineで生成される
                static Type[] s_editTimelineTypes = new Type[(int)CutInTimelineKeyDataType.Max] {
                    null,    //CameraPos,
                    null,    //CameraLookAt,
                    null,    //CameraFov,
                    null,    //CameraRoll,
                    null,    // Event,
                    // Gallop CutIn Timeline
                    typeof( EditTimelineCharacter ),        // CharacterModel,
                    typeof( EditTimelineCharacterMotion ),    // CharacterMotion,

                    null,    // CameraMotion,
                    typeof( EditTimelineSound ),            // Sound,
                    typeof( EditTimelineEffect ),            // Prefab,
                    typeof( EditTimelineBackGround ),        // BackGround,
                    typeof( EditTimelinePostEffect ),        // PostEffect,
                    null,                                    //timelineScale
                    typeof( EditTimelineCharacterFootSmoke ),// CharacterFootSmoke,
                    null,                                    //Light
                    null,                                    //Fog
                    typeof(EditTimelineCharacterWind),       //Wind
                    typeof( EditTimelineCharacterShadow ),   //CharacterShadow
                    null,   // CameraParam
                    typeof( EditTimelineCharacterSweat ),   // CharacterSweat
                    null,   //Reflection
                    null,  //A2UConfig
                    null,  //A2U
                    null,  //A2UComposition
                    typeof( EditTimelineCharacterCollision ),   // CharacterCollision
                    null, //Word
                    typeof(EditTimelineCharacterColor),
                    typeof(EditTimelineCharacterIK), // CharacterIK
                    typeof( EditTimelineCharacterSpringCollision ),   // CharacterSpringCollision
                    null,    // CameraCharaHeight,
                    null,    // MultiLightShadow,
                    typeof( EditTimelineAdditionalLight ),            // AdditionalLight,
                    null,   // MultiCameraEnable
                    typeof(EditTimelineConditionCameraParam),
                    null,    // CharacterEye
                    typeof( EditTimelineCharaProp ),            // CharaProp,
                    typeof(EditTimelineAudience),               // Audience
                    typeof(EditTimelineCyalumeControl),         // CyalumeControl
                    typeof(EditTimelineMobControl),             // MobControl
                    typeof(EditTimelineChoreographyCyalume),    // ChoreographyCyalume
                    null,                                       // MonitorCameraEnable
                    typeof(EditTimelineCharacterParts),         // CharacterParts
                    null,                                       // SupportCard
                    null,                                       // MaskCamera
                    typeof( EditTimelineMiniCharacter ),        // MiniCharacterModel
                    typeof( EditTimelineMiniCharacterMotion ),  // MiniCharacterMotion
                    typeof( EditTimelineMiniCharacterColor ),   // MiniCharacterColor
                    typeof( EditTimelineMiniCharacterShadow ),  // MiniCharacterShadow
                    typeof( EditTimelineMiniCharacterParts),    // MiniCharacterParts
                    typeof( EditTimelineMiniCharacterWind),     // MiniCharacterWind
                    typeof( EditTimelineMiniCharacterCollision),// MiniCharacterCollision
                    typeof( EditTimelineMiniCharacterSpringCollision),// MiniCharacterSpringCollision
                    typeof(EditTimelineLightShafts),            // LightShafts
                    typeof(EditTimelineBlinkLight),             // BlinkLight
                    typeof(EditTimelineMirrorScanLight),        // MirrorScanLight
                    typeof(EditTimelineLightProjection),        // LightProjection
                    typeof(EditTimelineSpotLight),              // SpotLight
                    null,                                       // CameraLayer
                    typeof(EditTimelineCharacterDirt),         // CharacterDirt
                    null,                                       // BackGround2D
                    null,                                       // StencilMask
                    typeof(EditTimelineUVScrollLight),          // UVScrollLight
                    typeof(EditTimelineCharacterNode),          // CharacterNode
                    typeof(EditTimelineCharacterAura),          // CharacterAura
                    typeof(EditTimelineCharacterMotionBlend),   // CharacterMotionBlend
                    null,                                       // StencilMask
                    typeof(EditTimelineSuperviseHeader),       //SuperviseHeader
                    typeof(EditTimelineFacialMotion),           //SuperviseEyeMotion
                    typeof(EditTimelineFacialMotion),           //SuperviseEyebrowMotion
                    typeof(EditTimelineFacialMotion),           //SuperviseMouthMotion
                    typeof(EditTimelineSuperviseEyeTarget),    //SuperviseEyeTarget
                    typeof(EditTimelineCharacterHologram), //CharaHologram
                    null,                                       //CameraBackGround
                    typeof(EditTimelineCharacterScreenMapping), //CharaScreenMapping
                    typeof(EditTimelineCharacterAlpha),

                    typeof( EditTimelineAudienceModel ), //観客モデル
                    typeof( EditTimelineAudienceMotion ), //観客モーション
                    typeof(EditTimelineAudienceModelColor), //観客色

                    typeof(EditTimelineCharacterScaleFactor),//CharacterScaleFactor
                };

                /// <summary>
                /// EditTimelineインスタンス生成
                /// </summary>
                /// <param name="editSheet"></param>
                /// <param name="keys"></param>
                /// <param name="keyDataType"></param>
                /// <returns></returns>
                public static EditTimeline Create(EditSheet editSheet, ITimelineKeyDataList keys, CutInTimelineKeyDataType keyDataType,object argumentValue = null)
                {
                    System.Type classType = s_editTimelineTypes[(int)keyDataType];
                    if (classType == null)
                    {
                        classType = typeof(EditTimeline);
                    }

                    var typeArray = argumentValue != null
                        ? new System.Type[] { typeof(object) }
                        : new System.Type[] { };
                    var argument = argumentValue != null
                        ? new[] { argumentValue }
                        : null;

                    var ci = classType.GetConstructor(BindingFlags.Instance | BindingFlags.NonPublic, null, typeArray, null);
                    if (ci == null)
                    {
                        //コンストラクタが見つからない
                        //EditTimelineの継承クラスでprotedtedコンストラクタを実装し忘れてないですか？
                        //また、baseの（つまりEditTimelineの）コンストラクタ呼び出しが必須なのでそれも注意
                        Debug.LogError("Missing Constructor. KeyDataType:" + keyDataType);
                        return null;
                    }
                    var line = ci.Invoke(argument) as EditTimeline;
                    line._keyDataType = keyDataType;
                    line.name = keyDataType.ToString();
                    line.SetKeyList(keys);
                    line._editSheet = editSheet;
                    return line;
                }

                /// <summary>
                /// コンストラクタ
                /// </summary>
                protected EditTimeline()
                {
                    ID = IDSeed++;//他のEditTimelineとかぶらなきゃなんでもいい
                }

                protected EditTimeline(object value) : this()
                {
                    SetArgumentValue(value);
                }

                /// <summary>
                /// ワークシート内での自身を特定できる文字列（Path）を返す
                /// ※ただし、Group内のタイムライン同士で名前がかぶっている場合、
                /// 　このPathで一意にタイムラインを特定することはできないので注意
                /// </summary>
                /// <returns></returns>
                public string MakeInSheetPath()
                {
                    if (_owner != null)
                    {
                        return string.Format("{0},{1}", _owner.name, name);
                    }
                    else
                    {
                        return string.Format("{0}", name);
                    }
                }

                /// <summary>
                /// Keyリストをセットする
                /// KeyはFrameの昇順でソートされている前提で扱われる
                /// </summary>
                /// <param name="list"></param>
                public void SetKeyList(ITimelineKeyDataList list)
                {
                    _editKeyList = list;
                    if (_editKeyList == null)
                    {
                        Debug.LogError( "EditKeyList is Null!" );
                        return;
                    }
                    //editKeyList.Sort((a,b)=>{return a.frame - b.frame;});//AddKeyframe時点でSort済みの前提
                    _editKeyFrameDict.Clear();
                    foreach (var k in _editKeyList)
                    {
                        _editKeyFrameDict[k._frame] = k;
                    }
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                public virtual void OnGUI(GUIContext ctx, EditSheet sheet)
                {
                    //ID
                    {
                        GUI.enabled = false;
                        EditorGUILayout.IntField("ID (Edit time only)", ID);
                        GUI.enabled = true;
                    }

                    _editKeyList.OnGUI(ctx);

                    if (_owner != null && _owner is IEditTimelineGroup)
                    {
                        if (GUILayout.Button("Delete Timeline"))
                        {
                            if (EditorUtility.DisplayDialog("Timeline削除", "Timelineを削除します。よろしいですか？", "OK", "キャンセル"))
                            {
                                sheet.CtxMenuCB_DeleteTimeline(this);
                                ctx._requireRepaint = true;
                            }
                        }
                        GUILayout.Space(10);
                    }
                }

                public virtual void CopyProperty(EditTimeline src)
                {
                    _editKeyList.SetGroupId(src._editKeyList.GroupId);
                    _editKeyList.SetBaseColor(src._editKeyList.BaseColor);
                    _editKeyList.SetDescription(src._editKeyList.Description);
                }

                public CutInTimelineKey CreateKey()
                {
                    return CutInTimelineKey.Create( keyDataType );
                }

                public virtual void OnEditKey(CutInTimelineKey key)
                {

                }

                /// <summary>
                /// ベースのタイムラインとは別にアレンジされたタイムラインを描画する
                /// </summary>
                public virtual void OnCustomizedDrawTimeline(GUIContext context, Rect rectKeyframeLabel)
                {
                    
                }

                /// <summary>
                /// 全体に影響するフラグも込みでショートカットをスキップするか確認する
                /// </summary>
                public virtual bool CheckSkipShortcut(bool isSkipTotalArea)
                    => IsSkipShortcutTimeline;

                /// <summary>
                /// キー情報を挿入した際に更新する
                /// </summary>
                public virtual void OnUpdateAfterInsert()
                {

                }
            }
        }//namespace Cutt
    }
}
#endif//UNITY_EDITOR
