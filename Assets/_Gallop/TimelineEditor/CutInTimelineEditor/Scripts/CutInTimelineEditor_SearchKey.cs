#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// </summary>
            public class CutTool_SearchKeyWindow : EditorWindow
            {
                private enum Routine
                {
                    Prepare,
                    Searching,
                }

                private enum SearchKind : int
                {
                    Color = 0,
                }

                public static CutTool_SearchKeyWindow instance = null;
                private CutInCutTool _tool = null;
                private Routine _routine = Routine.Prepare;
                SearchKind _searchKind = SearchKind.Color;

                private class HitKey
                {
                    public int _editLineID = 0;
                    public int _frame = 0;
                }

                //--------------------------------------------------------------------------------
                #region Color検索
                //--------------------------------------------------------------------------------
                private class ColorSearchWork
                {
                    public Color _color = Color.white;
                    public float _nearRate = 0.95f;
                    public bool _ignoreAlpha = true;
                    public bool _onlySelectLine = true;//選択中ラインのみ検索対象にする場合True
                }
                //--------------------------------------------------------------------------------
                ColorSearchWork _colorSearchWork = new ColorSearchWork();
                /// <summary>
                /// 色の近傍率計算
                /// 1~0の値。1が完全一致、0だとどの色に対してもHITする
                /// </summary>
                /// <param name="c1"></param>
                /// <param name="c2"></param>
                /// <param name="ignoreAlpha"></param>
                /// <returns></returns>
                float CalcNearRate(Color c1, Color c2, bool ignoreAlpha)
                {
                    var v1 = new Vector4(c1.r, c1.g, c1.b, c1.a);
                    var v2 = new Vector4(c2.r, c2.g, c2.b, c2.a);
                    if (ignoreAlpha)
                    {
                        v1.w = v2.w = 1f;
                    }
                    var mag = (v1 - v2).SqrMagnitude();
                    //var magMin = 0f;//完全一致
                    var magMax = (Vector4.one).SqrMagnitude();//全然違う
                    return 1f - Mathf.Clamp01(mag / magMax);//1を完全一致にするため反転
                }
                //--------------------------------------------------------------------------------
                #endregion Color検索
                //--------------------------------------------------------------------------------


                private int _searchSheetID = 0;
                private HitKey[] _hitKeyArray = null;
                private int _hitKeyCursor = 0;//HitしたKeyを巡回するときのカーソル
                private bool _requireFocusCursorKey = false;
                private Vector2 _resultScrollValue = Vector2.zero;

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                public static CutTool_SearchKeyWindow Open(CutInCutTool tool)
                {
                    if (instance == null)
                    {
                        instance = ScriptableObject.CreateInstance<CutTool_SearchKeyWindow>();
                        instance.titleContent = new GUIContent("Cutt/Search");
                        instance.Show();
                    }
                    else
                    {
                        instance.Focus();
                    }
                    instance.Init(tool);
                    instance.Repaint();

                    return instance;
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void Init(CutInCutTool tool)
                {
                    _tool = tool;

                    if (EditorPrefs.HasKey(TimelineDefine.PrefsKeyName_SearchColorNearRate))
                    {
                        _colorSearchWork._nearRate = EditorPrefs.GetFloat(TimelineDefine.PrefsKeyName_SearchColorNearRate);
                    }
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void OnDestroy()
                {
                    if (instance == this)
                    {
                        if (_tool != null)
                        {
                            _tool.SearchKeyWindowOnDestroyCallback();
                        }
                        instance = null;
                    }
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void Update()
                {
                    switch (_routine)
                    {
                        case Routine.Prepare:
                            {
                                //検索結果リスト上でカーソルのあるKeyをTimeline上でフォーカスする
                                if (_requireFocusCursorKey)
                                {
                                    _requireFocusCursorKey = false;
                                    EditSheet editSheet = EditSheet.GetInstance(_searchSheetID);
                                    if (editSheet != null && _hitKeyArray != null)
                                    {
                                        HitKey hitKey = _hitKeyArray[_hitKeyCursor];
                                        EditTimeline editLine = editSheet.QueryEditTimeline(hitKey._editLineID);
                                        editSheet.ScrollKeyframeAreaWithFrame(hitKey._frame);
                                        editSheet.ScrollKeyframeAreaWithLineID(editLine.ID);
                                        _tool._currentFrame = hitKey._frame;
                                        _tool.RepaintAll();
                                    }
                                }
                            }
                            break;
                        case Routine.Searching:
                            switch (_searchKind)
                            {
                                case SearchKind.Color:
                                    SearchColor();
                                    break;
                            }
                            _routine = Routine.Prepare;
                            break;
                    }
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void SearchColor()
                {
                    EditSheet editingSheet = _tool.GetEditingSheet();
                    if (editingSheet == null)
                    {
                        return;
                    }
                    var hitKeysDict = new Dictionary<EditTimeline, List<CutInTimelineKey>>();
                    {
                        List<EditTimeline> timelines = editingSheet.QueryAllEditTimelines();
                        foreach (EditTimeline line in timelines)
                        {
                            if (_colorSearchWork._onlySelectLine)
                            {
                                if (!editingSheet._selectionWork.IsExistsLine(line))
                                {
                                    continue;
                                }
                            }
                            var hitKeys = new List<CutInTimelineKey>();
                            foreach (CutInTimelineKey key in line._editKeyList)
                            {
                                if (key is ITimelineKeyHasColor)
                                {
                                    var keyHasColor = key as ITimelineKeyHasColor;
                                    float nearRate = CalcNearRate(keyHasColor.GetRepresentativeColor(), _colorSearchWork._color, _colorSearchWork._ignoreAlpha);
                                    if (nearRate >= _colorSearchWork._nearRate)
                                    {
                                        hitKeys.Add(key);
                                    }
                                }
                                else
                                {
                                    //色を持たないKeyTypeは無視
                                    break;
                                }
                            }
                            if (hitKeys.Count > 0)
                            {
                                hitKeysDict[line] = hitKeys;
                            }
                        }
                    }

                    MakeHitResult(editingSheet, hitKeysDict);
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void MakeHitResult(EditSheet editingSheet, Dictionary<EditTimeline, List<CutInTimelineKey>> hitKeysDict)
                {
                    _searchSheetID = editingSheet.ID;
                    _hitKeyCursor = 0;

                    var hitKeyList = new List<HitKey>();
                    editingSheet._selectionWork.Clear();
                    foreach (var pair in hitKeysDict)
                    {
                        EditTimeline editLine = pair.Key;
                        CutInTimelineKey[] keys = pair.Value.ToArray();
                        editingSheet._selectionWork.SetKeys(editLine, keys);
                        hitKeyList.AddRange(keys.Select(x => new HitKey() { _editLineID = editLine.ID, _frame = x._frame }));
                    }
                    _hitKeyArray = hitKeyList.ToArray();
                    _requireFocusCursorKey = _hitKeyArray.Length > 0;
                    editingSheet.Repaint();

                    Repaint();
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void OnGUI()
                {
                    //GUIメイン
                    try
                    {
                        EditorGUIUtility.labelWidth = 0f;
                        EditorGUIUtility.fieldWidth = 0f;
                        GUILayout.BeginVertical();
                        OnGUI_Main();
                        GUILayout.EndVertical();
                    }
                    catch (System.Exception exception)
                    {
                        //OnGUIでエラーが出るとコントロールを失ってUnity自体をShutdownするほかなくなるので
                        //例外キャッチしてBreakする
                        if (exception is ExitGUIException)
                        {
                            throw exception;//Unity独特の無害な奴はRethrow
                        }
                        else
                        {
                            Debug.LogError(exception.ToString());
                            Debug.DebugBreak();
                        }
                    }
                }

                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                void OnGUI_Main()
                {

                    // 現状Odinでは検索項目がないが、SearchKindを0個に出来ないので代わりにテキスト表示
                    EditorGUILayout.LabelField("検索項目がありません。");
                    EditorGUILayout.LabelField("検索項目の追加はCutTool_SearchKeyWindow.SearchKindで定義して下さい。");
                    /*
                    EditorGUILayout.LabelField("検索タイプ");
                    _searchKind = (SearchKind)GUILayout.Toolbar((int)_searchKind, System.Enum.GetNames(typeof(SearchKind)), EditorStyles.toolbarButton);

                    GUILayout.Space(10);

                    switch(_searchKind) {
                    case SearchKind.Color:
                        OnGUI_Color();
                        break;
                    }

                    OnGUI_SearchResult();
                    */
                }
                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void OnGUI_Color()
                {
                    GUILayout.BeginVertical();
                    {
                        GUILayout.Label("■Color検索 ---------------");

                        EditorGUI.indentLevel++;

                        _colorSearchWork._color = EditorGUILayout.ColorField("キーカラー", _colorSearchWork._color);
                        EditorGUI.BeginChangeCheck();
                        _colorSearchWork._nearRate = EditorGUILayout.Slider("一致率", _colorSearchWork._nearRate, 0f, 1f);
                        if (EditorGUI.EndChangeCheck())
                        {
                            EditorPrefs.SetFloat(TimelineDefine.PrefsKeyName_SearchColorNearRate, _colorSearchWork._nearRate);
                        }
                        _colorSearchWork._ignoreAlpha = EditorGUILayout.Toggle("Alpha無視", _colorSearchWork._ignoreAlpha);
                        _colorSearchWork._onlySelectLine = EditorGUILayout.Toggle("選択ラインのみ検索", _colorSearchWork._onlySelectLine);

                        EditorGUI.indentLevel--;
                    }
                    GUILayout.EndVertical();
                }
                //--------------------------------------------------------------------------------
                //--------------------------------------------------------------------------------
                private void OnGUI_SearchResult()
                {
                    int hitCount = _hitKeyArray != null ? _hitKeyArray.Length : 0;
                    GUILayout.BeginHorizontal();
                    {
                        EditorGUILayout.LabelField(string.Format("検索HIT数:{0}", hitCount));

                        GUILayout.FlexibleSpace();

                        GUI.enabled = _routine == Routine.Prepare;
                        if (GUILayout.Button("検索", GUILayout.Width(50)))
                        {
                            _routine = Routine.Searching;
                        }
                        GUI.enabled = true;
                    }
                    GUILayout.EndHorizontal();

                    GUILayout.BeginHorizontal();
                    {
                        GUILayout.FlexibleSpace();
                        GUI.enabled = hitCount > 0;
                        bool getInput = Event.current.type == EventType.KeyDown && Event.current.keyCode == KeyCode.UpArrow;
                        if (GUILayout.Button("前へ", GUILayout.Width(50)) || getInput)
                        {
                            _hitKeyCursor = _hitKeyCursor - 1;
                            if (_hitKeyCursor < 0)
                            {
                                _hitKeyCursor = hitCount - 1;
                            }
                            _requireFocusCursorKey = true;
                            Repaint();
                        }
                        getInput = Event.current.type == EventType.KeyDown && Event.current.keyCode == KeyCode.DownArrow;
                        if (GUILayout.Button("次へ", GUILayout.Width(50)) || getInput)
                        {
                            _hitKeyCursor = (_hitKeyCursor + 1) % hitCount;
                            _requireFocusCursorKey = true;
                            Repaint();
                        }
                        GUI.enabled = true;
                    }
                    GUILayout.EndHorizontal();

                    _resultScrollValue = EditorGUILayout.BeginScrollView(_resultScrollValue);
                    {
                        EditSheet editSheet = EditSheet.GetInstance(_searchSheetID);
                        if (editSheet != null && _hitKeyArray != null)
                        {
                            var guiStyle = new GUIStyle(EditorStyles.label);
                            guiStyle.normal.background = Texture2D.whiteTexture;
                            Color bgColorbackup = GUI.backgroundColor;

                            for (int iKey = 0; iKey < hitCount; iKey++)
                            {
                                HitKey hitKey = _hitKeyArray[iKey];
                                EditTimeline editLine = editSheet.QueryEditTimeline(hitKey._editLineID);
                                GUIStyle style = EditorStyles.label;
                                if (iKey == _hitKeyCursor)
                                {
                                    GUI.backgroundColor = new Color(0.1f, 0.1f, 0.9f, 0.8f);
                                    style = guiStyle;
                                }
                                else
                                {
                                    GUI.backgroundColor = bgColorbackup;
                                }
                                EditorGUILayout.LabelField(
                                    string.Format("{0}: {2}F - {1}",
                                    iKey + 1,
                                    editLine != null ? editLine.MakeInSheetPath() : "[Missing]",
                                    hitKey._frame
                                    ),
                                    style);

                                if (Event.current.type == EventType.MouseDown)
                                {
                                    if (GUILayoutUtility.GetLastRect().Contains(Event.current.mousePosition))
                                    {
                                        _hitKeyCursor = iKey;
                                        _requireFocusCursorKey = true;
                                        Event.current.Use();
                                        Repaint();
                                    }
                                }
                            }

                            GUI.backgroundColor = bgColorbackup;
                        }
                    }
                    EditorGUILayout.EndScrollView();
                }
            }
        }//namespace Cutt
    }
}
#endif//UNITY_EDITOR
