using System;
using UnityEngine;
using UnityEngine.Rendering;
using Gallop.RenderPipeline;

namespace Gallop
{
    /// <summary>
    /// 伝説編アーモンドアイ登場演出用ヘルパークラス
    /// </summary>
    public class SingleModeScenarioLegendRivalCutInHelper : SetCutInHelper
    {
        private Action _onSetWaitTap;

        private RenderTexture _renderTexture;

        public void Setup(Action onSetWaitTap)
        {
            _onSetWaitTap = onSetWaitTap;

            //153712 UIカメラにカットの描画結果をコピーするために、事前に書き込み先のRTを用意
            if (_renderTexture == null)
            {
                var resolution3D = GraphicSettings.Instance.GetVirtualResolution3D();
                _renderTexture = RuntimeObjectManager.NewRenderTextureOnView(resolution3D.x, resolution3D.y, depth: 0);
                _renderTexture.DebugSetName("SingleModeScenarioLegendRivalCutInHelper@RT");
            }
        }

        protected override void OnPlayCutIn(string loadPath, Transform parent)
        {
            base.OnPlayCutIn(loadPath, parent);
            //カットのカメラに出力先を指定
            var camera = TimelineController.MotionCamera.targetCamera;
            camera.targetTexture = _renderTexture;

            //153712 UIカメラにカットの描画結果をコピーする
            CopyCuttToUICamera();
        }

        public override void RegisterCallback()
        {
            base.RegisterCallback();
            _timelineController.OnSetWaitTap = _ =>
            {
                if (_onSetWaitTap != null)
                {
                    _onSetWaitTap.Invoke();
                    _onSetWaitTap = null; // 発動は1回のみ
                }
            };
        }

        public override void CleanupPlaying()
        {
            base.CleanupPlaying();

            //153712 作成していたRTを消す
            if (_renderTexture != null)
            {
                RuntimeObjectManager.Destroy(_renderTexture);
                _renderTexture = null;
            }
        }

        /// <summary>
        /// フレームスキップを実行
        /// </summary>
        public void SkipRuntime()
        {
            _timelineController.SkipRuntime();
        }
    }
}
