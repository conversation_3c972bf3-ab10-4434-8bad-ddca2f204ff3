
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// 各TimelineKeyデータの基底クラス
            /// </summary>
            [System.Serializable]
            public abstract class CutInTimelineKey
            {
                public abstract CutInTimelineKeyDataType dataType { get; }
                public int _frame;

                /// <summary>
                /// イベント、キー間の紐付けID
                /// </summary>
                //public int _groupId = -1;

                public TimelineKeyAttribute _attribute;//多目的ビットフラグ

                /// <summary>
                /// 補間プロパティを持つキーでTrueを返すようにOverride
                /// </summary>
                /// <returns></returns>
                public virtual bool IsInterpolateKey(int index = 0)
                {
                    return false;
                }

                /// <summary>
                /// 補間プロパティ有効判定
                /// </summary>
                /// <returns></returns>
                public virtual bool IsValidInterpolate(CutInTimelineKey previousKey, int index = 0)
                {
                    return true;
                }

                /// <summary>
                /// データロード時のコールバック。セットアップなどに使う
                /// </summary>
                /// <param name="timelineController"></param>
                /// <param name="workSheet"></param>
                public virtual void OnLoad(CutInTimelineController timelineController, CutInTimelineWorkSheet workSheet)
                {
                    OnLoad(timelineController);
                }

                public virtual void OnLoad(CutInTimelineController timelineController)
                {
                }

#if UNITY_EDITOR
                //この辺はframeプロパティ編集時のワークアラウンド用（See. OnGUI）
                int _oldFrame = -1;
                public void RevertFrameToOld()
                {
                    _frame = _oldFrame;
                    _oldFrame = -1;
                }
                public bool HasChangedFrame()
                {
                    return _oldFrame >= 0;
                }
                int _frameForEdit = 0;
                public void OnStartEditing()
                {
                    _frameForEdit = _frame;
                }

                //キークラスのTypeリスト。Createに使われる
                static System.Type[] sKeyDataTypes = new System.Type[(int)CutInTimelineKeyDataType.Max] {
                    typeof(TimelineKeyCameraPositionData),
                    typeof(TimelineKeyCameraLookAtData),
                    typeof(TimelineKeyCameraFovData),
                    typeof(TimelineKeyCameraRollData),
                    typeof(TimelineKeyEventData),
                    // Gallop CutIn Timeline
                    typeof(TimelineKeyCharacterData),
                    typeof(TimelineKeyCharacterMotionData),
                    typeof(TimelineKeyCameraMotionData),
                    typeof(TimelineKeySoundData),
                    typeof(TimelineKeyEffectData),
                    typeof(TimelineKeyBackGroundData),
                    typeof(TimelineKeyPostEffectData),
                    typeof(TimelineKeyTimeScaleData),
                    typeof(TimelineKeyCharacterFootSmokeData),
                    typeof(TimelineKeyLightData),
                    typeof(TimelineKeyFogData),
                    typeof(TimelineKeyWindData),
                    typeof(TimelineKeyCharacterShadowData),
                    typeof(TimelineKeyCameraParamData),
                    typeof(TimelineKeyCharacterSweatData),
                    typeof(TimelineKeyReflectionData),
                    null,//A2UConfig
                    null,//A2U
                    null,//A2UComposition
                    typeof(TimelineKeyCharacterCollisionData),
                    typeof(TimelineKeyWordData),
                    typeof(TimelineKeyCharacterColorData),
                    typeof(TimelineKeyCharacterIKData),
                    typeof(TimelineKeyCharacterSpringCollisionData),
                    typeof(TimelineKeyCameraTargetCharaData),
                    typeof(TimelineKeyMultiLightShadowData),
                    typeof(TimelineKeyAdditionalLightData),
                    typeof(TimelineKeyMultiCameraEnableData),
                    typeof(TimelineKeyConditionCameraParamData),
                    typeof(TimelineKeyCharacterEyeData),
                    typeof(TimelineKeyCharaPropData),
                    typeof(TimelineKeyAudienceData),
                    typeof(TimelineKeyCyalumeControlData),
                    typeof(TimelineKeyMobControlData),
                    typeof(TimelineKeyChoreographyCyalumeData),
                    typeof(TimelineKeyMonitorCameraEnableData),
                    typeof(TimelineKeyCharacterPartsData),
                    typeof(TimelineKeySupportCardData),
                    typeof(CutInTimelineKeyMaskCameraData),
                    typeof(TimelineKeyMiniCharacterData),
                    typeof(TimelineKeyMiniCharacterMotionData),
                    typeof(TimelineKeyMiniCharacterColorData),
                    typeof(TimelineKeyMiniCharacterShadowData),
                    typeof(TimelineKeyMiniCharacterPartsData),
                    typeof(TimelineKeyMiniCharacterWindData),
                    typeof(TimelineKeyMiniCharacterCollisionData),
                    typeof(TimelineKeyMiniCharacterSpringCollisionData),
                    typeof(TimelineKeyLightShaftsData),
                    typeof(TimelineKeyBlinkLightData),
                    typeof(TimelineKeyMirrorScanLightData),
                    typeof(TimelineKeyLightProjectionData),
                    typeof(TimelineKeySpotLightData),
                    typeof(TimelineKeyCameraLayerData),
                    typeof(TimelineKeyCharacterDirtData),
                    typeof(TimelineKeyBackGround2D),
                    typeof(TimelineKeyStencilMaskData),
                    typeof(TimelineKeyUVScrollLightData),
                    typeof(TimelineKeyCharacterNodeData),
                    typeof(TimelineKeyCharacterAuraData),
                    typeof(TimelineKeyCharacterMotionBlendData),
                    typeof(TimelineKeyFovOffsetData),
                    typeof(TimelineKeyGroupHeaderData),
                    typeof(TimelineKeySuperViseEyeData),
                    typeof(TimelineKeySuperviseEyebrowData),
                    typeof(TimelineKeySuperviseMouthData),
                    typeof(TimelineKeyCharacterEyeData),
                    typeof(TimelineKeyCharacterHologramData),
                    typeof(TimelineKeyCameraBackGroundData),
                    typeof(TimelineKeyCharacterScreenMappingData),
                    typeof(TimelineKeyCharacterAlphaData),

                    typeof(TimelineKeyAudienceModelData),
                    typeof(TimelineKeyAudienceCharacterMotionData),
                    typeof(TimelineaKeyAudienceModelColorData),

                    typeof(TimelineKeyCharacterScaleFactorData),
                };

                /// <summary>
                /// srcがdestにコピー可能かどうかを判定
                /// 通常はsrcとdestが一致しないとコピー不可だが、一部KeyTypeで相互にコピー可能となっている
                /// </summary>
                /// <param name="src"></param>
                /// <param name="dest"></param>
                /// <returns></returns>
                public static bool IsCopyableDataType(CutInTimelineKeyDataType src, CutInTimelineKeyDataType dest)
                {
                    if (src == dest)
                    {
                        return true;
                    }
                    //異なるKeyType間でCopyを実装する場合はここに追加
                    return false;
                }

                /// <summary>
                /// キーインスタンスの生成
                /// </summary>
                /// <param name="dataType"></param>
                /// <returns></returns>
                public static CutInTimelineKey Create(CutInTimelineKeyDataType dataType)
                {
                    var createType = sKeyDataTypes[(int)dataType];
                    var ret = System.Activator.CreateInstance(createType) as CutInTimelineKey;
                    if (ret == null)
                    {
                        return null;
                    }
                    ret.OnCreate();
                    return ret;
                }

                /// <summary>
                /// キーを最初に作った時にCallback
                /// データロード時は呼ばれない、データロード時のコールバックはOnLoadが担当
                /// </summary>
                protected virtual void OnCreate()
                {

                }

                /// <summary>
                /// キーの複製
                /// </summary>
                /// <returns></returns>
                public CutInTimelineKey Clone()
                {
                    return Clone(dataType);
                }
                /// <summary>
                /// キーの複製だが。指定のKeyTypeのKeyを生成して、それに対してCopyを行うことで複製処理とする
                /// </summary>
                /// <param name="dt"></param>
                /// <returns></returns>
                public CutInTimelineKey Clone(CutInTimelineKeyDataType dt)
                {
                    var newInstance = Create(dt);
                    if (newInstance == null)
                    {
                        return null;
                    }
                    Copy(newInstance);//KeyTypeが異なる場合は、それに対応するコードがCopy()に実装されている必要がある
                    return newInstance;
                }

                /************************************************************************
                 * !!!!!!!! 各派生クラスでCopyの実装を忘れないように !!!!!!!!!!
                 ************************************************************************/
                public virtual void Copy(CutInTimelineKey dest)
                {
                    dest._frame = _frame;
                    //dest._groupId = _groupId;
                    if (dest.dataType == dataType)
                    {
                        dest._attribute = _attribute;
                    }
                    else
                    {
                        //別のKeyTypeへCopyする際はキー共通属性だけをコピー
                        dest._attribute = _attribute & TimelineKeyAttribute.KeyCommonBitMask;
                    }
                }

                /// <summary>
                /// キーのマージを行う（派生クラスで継承する必要がある）
                /// </summary>
                /// <param name="srcKey"></param>
                /// <param name="destKey"></param>
                /// <returns></returns>
                public virtual CutInTimelineKey Merge(CutInTimelineKey srcKey, CutInTimelineKey destKey)
                {
                    Debug.LogWarning("cannot merge this timeline!");
                    return null;
                }

                /// <summary>
                /// データ保存時のコールバック
                /// </summary>
                /// <param name="timelineController"></param>
                public virtual void OnSave(CutInTimelineController timelineController)
                {
                }

                public virtual void OnGUI(GUIContext ctx, CutInTimelineController controller)
                {
                    OnGUI_Frame(ctx);
                }

                /// <summary>
                /// Frame GUI描画
                /// 継承先から任意順で呼べるように関数化
                /// </summary>
                /// <param name="ctx"></param>
                protected void OnGUI_Frame(GUIContext ctx)
                {
                    //この辺のframeの操作はOnGUIの特有な挙動（RepaintとかLayoutとかイベント別に何回も実行されるとか）
                    //による望まれない動作に対するワークアラウンド。
                    //この辺に手を入れるときはKeyのFrame移動チェックとか入念にした方が良い
                    _frameForEdit = EditorGUILayout.IntField("frame", _frameForEdit);
                    if (EditorGUIUtility.editingTextField)
                    {
                        if ((ctx._event.keyCode == KeyCode.Return || ctx._event.keyCode == KeyCode.KeypadEnter) &&//Return押さないと確定させない
                            _frameForEdit != _frame &&
                            !ctx._curOnGUISheet._editingLine._editKeyFrameDict.ContainsKey(_frameForEdit)//frameの空きチェック
                            )
                        {
                            //Debug.Log("KeyReturn "+frameForEdit+", "+frame);
                            _oldFrame = _frame;
                            _frame = _frameForEdit;
                        }
                    }
                    else
                    {
                        _frameForEdit = _frame;
                    }

                    // グループID
                    //_groupId = EditorGUILayout.IntField("GroupId", _groupId);

#if false//各KeyのOnGUIで実装（Enumで選択できると誤操作する可能性があるので）
            attribute = (TimelineKeyAttribute)EditorGUILayout.EnumMaskField("Attribute", attribute);
#endif
                    EditorGUILayout.Space();
                }

                public void OnGUI_LocatorTransform<T>(
                    Cutt.GUIContext ctx,
                    System.Reflection.MethodInfo findLocatorMethodInfo,
                    System.Reflection.FieldInfo nameFieldInfo,
                    System.Reflection.FieldInfo transformFieldInfo,
                    string dataFieldName,
                    CutInTimelineController timelineController
                    ) where T : MonoBehaviour
                {
                    var transformName = nameFieldInfo.GetValue(this) as string;
                    var transform = transformFieldInfo.GetValue(this) as Transform;
                    if (transform == null)
                    {
                        transform = findLocatorMethodInfo.Invoke(timelineController, new object[] { transformName }) as Transform;
                    }
                    {
                        var tmp = EditorGUILayout.ObjectField("Locator", transform, typeof(T), true) as MonoBehaviour;
                        if (tmp != null)
                        {
                            transform = tmp.transform;
                        }
                    }
                    if (transform != null)
                    {
                        transformName = transform.name;
                    }
                    EditorGUILayout.LabelField("LocatorName", transformName);
                    if (transform == null && string.IsNullOrEmpty(transformName) == false)
                    {
                        //名前の設定はあるけどシーン中に該当するTransformがない場合、名前変更によるLink切れと判断
                        int pickerControlID = EditorGUIUtility.GetControlID(FocusType.Passive);
                        if (GUILayout.Button("fix link"))
                        {
                            //FixLinkは、Timeline中の他のKeyも含めてtransformNameの置換を行う
                            EditorGUIUtility.ShowObjectPicker<T>(null, true, "", pickerControlID);
                        }
                        if (Event.current.commandName == "ObjectSelectorClosed")
                        {
                            if (EditorGUIUtility.GetObjectPickerControlID() == pickerControlID)
                            {
                                var obj = EditorGUIUtility.GetObjectPickerObject();
                                var selectedObject = obj as GameObject;
                                if (selectedObject != null)
                                {
                                    //全KeyのtransformNameを選択されたものに変更
                                    //※ここ、ScratchSheet未対応
                                    if (ctx._tool == null)
                                    {
                                        Debug.LogError("timeline editor must be instanced");
                                    }
                                    else
                                    {
                                        transformName = selectedObject.name;
                                        transform = selectedObject.transform;
                                        ctx._requireRepaint = true;
                                    }
                                }
                            }
                        }
                    }
                    nameFieldInfo.SetValue(this, transformName);
                    transformFieldInfo.SetValue(this, transform);
                }

#endif//UNITY_EDITOR
            }
        }
    }
}

