using System.Collections.Generic;
using Gallop.CutIn.Cutt;
using UnityEngine;

namespace Gallop
{
    using FlashMaster = MasterTrainingCuttFlash.TrainingCuttFlash;

    /// <summary>
    /// 育成トレーニングカットHelperのうちSimpleCommand関連を分離したもの
    /// </summary>
    public partial class SingleModeTrainingCutInHelper
    {
        #region 定数

        // シンプルコマンドのStringParam
        private const string FLASH_COMMAND_PREFIX = "tra_flash";

        // フラッシュのID関連
        private const int FLASH_ID_INDEX = 2;
        private const string FLASH_DEFAULT_ID = "00";

        #endregion 定数

        #region 変数

        private readonly Dictionary<int, FlashPlayer> _loadedFlashDict = new Dictionary<int, FlashPlayer>();

        #endregion 変数

        /// <summary>
        /// シンプルコマンドを実行
        /// </summary>
        private void OnSimpleCommand(CuttEventParam_SimpleCommand param)
        {
            if (param.StringParam.StartsWith(FLASH_COMMAND_PREFIX))
            {
                OnSimpleCommand_Flash(param);
            }
        }

        /// <summary>
        /// シンプルコマンドで制御しているオブジェクトに対してポーズをかける
        /// </summary>
        private void PauseSimpleCommand(bool isPause)
        {
            if (isPause)
            {
                PauseFlash();
            }
            else
            {
                ResumeFlash();
            }
        }

        #region Flash制御

        /// <summary>
        /// Flash制御のシンプルコマンドを実行
        /// </summary>
        private void OnSimpleCommand_Flash(CuttEventParam_SimpleCommand param)
        {
            if (param.IsTriggerParam)
            {
                PlayFlash(param);
            }
            else
            {
                StopFlash(param);
            }
        }

        #region 再生
        /// <summary>
        /// Flashを再生する
        /// </summary>
        private void PlayFlash(CuttEventParam_SimpleCommand param)
        {
            // 再生済みかチェック
            int hash = param.StringParam.GetHashCode();
            if (_loadedFlashDict.ContainsKey(hash)) return;

            // StringParamにはtra_flash_01などの値が設定される
            // 末尾に仕込んであるIDを抽出しておく
            var split = param.StringParam.Split('_');
            var flashId = split.Length > FLASH_ID_INDEX ? split[FLASH_ID_INDEX] : FLASH_DEFAULT_ID;

            // マスターデータ取得
            var master = MasterDataManager.Instance.masterTrainingCuttFlash.Get(_context.CommandId);
            if (master == null)
            {
                DebugUtils.LogErrorOnce($"training_cutt_flashにデータがない: CommandId = {_context.CommandId}");
                return;
            }

            // FlashのPathを取得して存在チェック
            var path = ResourcePath.GetTrainingCutInFlashPath(master.SubFolder, master.FileFormat, flashId);
            if (!ResourceManager.IsExistAsset(path))
            {
                DebugUtils.LogErrorOnce($"Flashが存在しない: Path = {path}");
                return;
            }

            // Flash生成
            var flashParent = GetFlashParent();
            var flash = FlashLoader.LoadOnHash(path, flashParent, hash: ResourceManager.ResourceHash.TrainingCutInFlash);
            if (flash == null)
            {
                DebugUtils.LogErrorOnce($"Flashのロードに失敗: Path = {path}");
                return;
            }

            // #120100対応: シンプルコマンドのパラメータによるオフセット指定をサポート
            // カットツール内のParamGeneratorがLitJsonを採用しているせいで自前structを使っており, Vector3へのキャストが必要
            if (param.UseOffset)
            {
                var flashTransform = flash.transform;
                flashTransform.localPosition = (Vector3)param.PositionOffset;
                flashTransform.localRotation = Quaternion.Euler((Vector3)param.RotationOffset);
                flashTransform.localScale = (Vector3)param.ScaleOffset;
            }

            // 辞書に登録
            _loadedFlashDict[hash] = flash;

            // Flash再生
            SingleModeTrainingCutFlashPlayer.Play(flash, master, _context, IsHighSpeedMode());
        }

        /// <summary>
        /// Flashの親Transformを返す
        /// </summary>
        private static Transform GetFlashParent()
        {
            var currentView = SceneManager.Instance.GetCurrentViewController();
            var flashParent = currentView?.GetContentsRoot() as Transform;
            if (flashParent == null)
            {
                flashParent = UIManager.Instance.GameCanvasRoot.transform;
            }

            return flashParent;
        }
        #endregion 再生

        #region 停止
        /// <summary>
        /// Flashを停止する
        /// </summary>
        private void StopFlash(CuttEventParam_SimpleCommand param)
        {
            int hash = param.StringParam.GetHashCode();
            if (!_loadedFlashDict.TryGetValue(hash, out var flash)) return;

            flash.Stop();
            Object.Destroy(flash.gameObject);
            _loadedFlashDict.Remove(hash);
        }
        #endregion 停止

        #region ポーズ

        /// <summary>
        /// Flashをポーズする
        /// </summary>
        private void PauseFlash()
        {
            foreach (var flash in _loadedFlashDict.Values)
            {
                flash.Pause();
            }
        }

        /// <summary>
        /// Flashのポーズを解除する
        /// </summary>
        private void ResumeFlash()
        {
            foreach (var flash in _loadedFlashDict.Values)
            {
                flash.Resume();
            }
        }

        #endregion ポーズ

        #region 後片付け
        /// <summary>
        /// Flashの後片付け
        /// </summary>
        private void CleanupFlash()
        {
            // オブジェクトを破棄
            foreach (var flash in _loadedFlashDict.Values)
            {
                Object.Destroy(flash.gameObject);
            }
            _loadedFlashDict.Clear();

            // Hash指定でアンロード
            ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.TrainingCutInFlash);
        }
        #endregion 後片付け

        #endregion Flash制御
    }
}
