using UnityEngine;
using Gallop.RenderPipeline;
using UnityEngine.Rendering;

namespace Gallop
{
    namespace CutIn
    {
        namespace Cutt
        {
            /// <summary>
            /// Cutt用マスクカメラの制御クラス
            /// </summary>
            public class CutInTimelineMaskCamera : MonoBehaviour
            {
                // 対応するprefab
                public const string PREFAB_PATH = "Race/Skill/SkillCutInMaskCamera";

                // 基本サイズ
                public const int DEFAULT_WIDTH = 256;
                public const int DEFAULT_HEIGHT = 256;

                // カメラの基本設定
                public const float DEFAULT_CAMERA_FOV = 24f;

                // 描画順
                public const int DEFAULT_RENDER_QUEUE = (int)UnityEngine.Rendering.RenderQueue.Geometry;

                // 描画先RenderTextureのDepth
                private const int DEPTH_BUFFER_BIT_SIZE = 16;

                [SerializeField]
                protected Camera _camera;

                [SerializeField]
                protected MeshRenderer _renderer;

                private Transform _cameraTrans;
                private Transform _rendererTrans;
                private int _idMaskTex;
                private int _idAlpha;
                private int _idCutoff;
                private RenderTexture _renderTexture;
                // 編集のマスク切替検知用
                private Texture _maskTex = null;

#if UNITY_EDITOR
                // 編集中のマスク可視化用
                private MeshRenderer _debugMaskRenderer = null;
                private Transform _debugRendererTrans;
#endif
                /// <summary>
                /// マスクカメラの初期化
                /// </summary>
                public void Initialize(TimelineKeyMaskCameraDataList list)
                {
                    // Transformをキャッシュしておく
                    _cameraTrans = _camera.transform;
                    _rendererTrans = _renderer.transform;

                    // シェーダーロード
                    var shader = ShaderManager.GetShader(ShaderManager.ShaderKinds.SpriteCutoff);

                    // マテリアル設定
                    _renderer.material = new Material(shader);

                    // マテリアルのプロパティIDをキャッシュ
                    _idMaskTex = ShaderManager.GetPropertyId(ShaderManager.PropertyId._MaskTex);
                    _idAlpha = ShaderManager.GetPropertyId(ShaderManager.PropertyId._Alpha);
                    _idCutoff = ShaderManager.GetPropertyId(ShaderManager.PropertyId._Cutoff);

                    // マスク素材設定
                    _renderer.material.SetTexture(_idMaskTex, list.MaskTexture);

                    // 描画先のテクスチャ設定
                    //URP:置き換え対応
                    //内部でDepthバッファは作られるので、不要
                    _renderTexture = new RenderTexture(list.MaskWidth, list.MaskHeight, 0);
                    //_renderTexture = new RenderTexture(list.MaskWidth, list.MaskHeight, DEPTH_BUFFER_BIT_SIZE);
                    //URP:置き換え対応
                    /*
                    _renderTexture.antiAliasing = GraphicSettings.Instance.Get3DAntiAliasingLevel(false);
                    */
#if CYG_DEBUG
                    _renderTexture.name = "MaskCameraMainTex";
#endif
                    _renderTexture.Create();
                    _renderer.material.mainTexture = _renderTexture;

                    // カメラ設定
                    _camera.targetTexture = _renderTexture;

                    //URP:置き換え対応
                    _camera.allowMSAA = false;

#if UNITY_EDITOR
                    // エディタ上でマスク変更検知用のキャッシュ
                    _maskTex = list.MaskTexture;

                    // マスクの可視化デバッグ
                    // デバッグ用のリソースはあまり増やしたくないので
                    // 初期化時にGameObjectと描画に必要なコンポーネントを追加します
                    var originalMeshFilter = _renderer.gameObject.GetComponent<MeshFilter>();
                    if (originalMeshFilter)
                    {
                        var debugObj = new GameObject();
                        debugObj.transform.SetParent(this.transform);
                        debugObj.transform.localPosition = Math.VECTOR3_ZERO;
                        debugObj.transform.localRotation = Math.QUATERNION_IDENTITY;
                        debugObj.SetLayerRecursively( GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3D));
                        debugObj.name = "MaskCameraDebugDraw";

                        // メッシュはRendererTextureと同じインスタンスを使用
                        var meshFilter = debugObj.AddComponent<MeshFilter>();
                        meshFilter.mesh = originalMeshFilter.mesh;
                        // レンダラーの初期化
                        _debugMaskRenderer = debugObj.AddComponent<MeshRenderer>();
                        _debugMaskRenderer.enabled = false;
                        _debugMaskRenderer.receiveShadows = false;
                        _debugMaskRenderer.shadowCastingMode = ShadowCastingMode.Off;
                        _debugMaskRenderer.lightProbeUsage = LightProbeUsage.Off;
                        _debugMaskRenderer.reflectionProbeUsage = ReflectionProbeUsage.Off;
                        _debugRendererTrans = _debugMaskRenderer.transform;
                        // SpriteCutoffのMainTexに設定することで代用
                        var debugShader = ShaderManager.GetShader(ShaderManager.ShaderKinds.SpriteCutoff);
                        _debugMaskRenderer.material = new Material(debugShader);
                        _debugMaskRenderer.material.mainTexture = list.MaskTexture;
                        _debugMaskRenderer.material.SetTexture(_idMaskTex, list.MaskTexture);
                    }
#endif
                }

                /// <summary>
                /// マスクカメラの更新
                /// </summary>
                public void AlterUpdate(MaskCameraUpdateInfo info)
                {
                    _camera.fieldOfView = info.FieldOfView;
                    _cameraTrans.localPosition = info.CameraPosition;
                    _cameraTrans.localRotation = info.CameraRotation;
                    _rendererTrans.localPosition = info.Position;
                    _rendererTrans.localRotation = info.Rotation;
                    _rendererTrans.localScale = info.Scale;
                    _renderer.material.renderQueue = info.RenderQueue;
                    _renderer.material.SetFloat(_idAlpha, info.Alpha);
                    _renderer.material.SetFloat(_idCutoff, info.Cuttoff);

                    if (_maskTex != info.MaskTexture)
                    {
                        _renderer.material.SetTexture(_idMaskTex, info.MaskTexture);
                        _maskTex = info.MaskTexture;
#if UNITY_EDITOR
                        if (_debugMaskRenderer != null)
                        {
                            _debugMaskRenderer.material.mainTexture = info.MaskTexture;
                            _debugMaskRenderer.material.SetTexture(_idMaskTex, info.MaskTexture);
                        }
#endif
                    }


#if UNITY_EDITOR
                    // マスク可視化用のレンダラーにもパラメータを設定する
                    if (_debugRendererTrans != null)
                    {
                        _debugRendererTrans.localPosition = info.Position;
                        _debugRendererTrans.localRotation = info.Rotation;
                        _debugRendererTrans.localScale = info.Scale;
                    }
                    if (_debugMaskRenderer != null)
                    {
                        _debugMaskRenderer.material.renderQueue = info.RenderQueue;
                        _debugMaskRenderer.material.SetFloat(_idAlpha, info.Alpha);
                        _debugMaskRenderer.material.SetFloat(_idCutoff, info.Cuttoff);
                        _debugMaskRenderer.enabled = info.IsVisibleMask;
                    }
#endif
                }

                /// <summary>
                /// マスクカメラの後始末
                /// </summary>
                public void Cleanup()
                {
                    _cameraTrans = null;
                    _rendererTrans = null;
                    _camera.targetTexture = null;

                    if (_renderer.material != null)
                    {
                        _renderer.material.mainTexture = null;
                        _renderer.material.SetTexture(_idMaskTex, null);
                        Destroy(_renderer.material);
                        _renderer.material = null;
                    }

                    if (_renderTexture != null)
                    {
                        _renderTexture.Release();
                        Destroy(_renderTexture);
                        _renderTexture = null;
                    }
                }
            }
        }
    }
}
