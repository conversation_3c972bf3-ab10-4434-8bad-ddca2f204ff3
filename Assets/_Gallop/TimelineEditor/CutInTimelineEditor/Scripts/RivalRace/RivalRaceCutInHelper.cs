using System;
using Gallop.CutIn;
using Gallop.CutIn.Cutt;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace Gallop
{
    public class RivalRaceCutInHelper : CutInHelper
    {

        #region 起動パラメータ

        // -------------------------------------------------------------------------------------------------
        // 起動パラメータ
        // -------------------------------------------------------------------------------------------------
        public class Context
        {
            /// <summary>
            /// 演出差分ID
            /// </summary>
            public int SubId = 1;

            /// <summary>
            /// キャラの人数(カットのファイル名に影響)
            /// </summary>
            public int CharaNum = 2;

            /// <summary>
            /// 使用するキャラのIDリスト
            /// </summary>
            public List<int> CharaIdList = new List<int>();

            /// <summary>
            /// 使用する衣装のIDリスト
            /// </summary>
            public List<int> DressIdList = new List<int>();
            
            public Context()
            {
            }
        }

        /// <summary>
        /// 保存用のContext
        /// </summary>
        [Serializable]
        public class SerializableContext : Context
        {
            public SerializableContext() { }

            public SerializableContext(Context context)
            {
                SubId = context.SubId;
                CharaNum = context.CharaNum;
                CharaIdList = context.CharaIdList;
            }
        }

        /// <summary>
        /// 起動パラメータ
        /// </summary>
        private Context _context;

        #endregion

        public void Init(Context context)
        {
            base.Init();

            _context = context;
        }

        #region CutHelper override

        /// <summary>
        /// キャラの作成のキャッシュの種類を取得
        /// </summary>
        protected override CutInCharacterCreateType GetCharacterCreateType(TimelineKeyCharacterType type)
        {
            //モデル全体は別で管理されているので、非アクティブにだけする
            return CutInCharacterCreateType.PopFromCache;
        }

        /// <summary>
        /// 実行時専用：再生開始(Playから呼ばれる)
        /// </summary>
        protected override void OnPlayCutIn(string loadPath, Transform parent)
        {
            // タイムラインの生成(すでに生成済みのやつが辞書に登録されていればそれを使うのでキーを渡す)
            bool isInstanceSuccess = InstantiateTimeline(loadPath, parent, preInstantiateKey: loadPath);
            if (!isInstanceSuccess)
            {
                OnEnd();
                return;
            }

            // フェードアウトが終わり、画面が暗転するまではカットイン再生待機状態とする。
            Status = CutInStatus.Idle;

            // Globalな値が変更される可能性があるのでキープしておく
            KeepGlobalShaderParam();

            //再生時毎回終端時間使用はリセットしておく
            _timelineController.UseResumeEndFrame(false);

            // 参照渡し
            _cutInCameraRootTransform = _timelineController.CameraRoot;

            // キャラとカメラのモーションをリストアップ
            SetupMotionResourceDic();

            // モーションリソースロード
            LoadMotion();           

            // ロード完了
            _timelineController.SetLoadFinish();

            //コールバックの登録
            RegisterCallback();

            //開始コールバック
            OnStart();

            // イベントの購読
            _timelineController.SubscribeEvent();

            // オート再生（自前でUpdate呼ばない）
            _timelineController.IsAutoPlay = true;

            // DirectionalLightのON
            DirectionalLightManager.Instance.SetEnable(true);

            // DirectionalLightの設定(env設定がないので白色)
            _timelineController.SetTargetLight(DirectionalLightManager.Instance.DirectionalLight, Color.white);

            // カットイン再生中状態へ。
            Status = CutInStatus.Playing;

            // UIに触れなくなるため、フェードスプライトのコリジョンOFF。
            FadeManager.Instance.SetEnableFadeCollision(false);

            // CutInTimelineDataにCySpringControlが設定されていれば、事前に読み込んでおく
            _cyspringControlTargetController.LoadCySpringControlTargetData(_timelineController.Data._cyspringControlSettings);
        }

        /// <summary>
        /// 再生終了 (TimelineControllerがendActionを呼び出し)
        /// </summary>
        protected override void OnEndCutIn()
        {
            Status = CutInStatus.ToEnd;
            EndCutIn_impl();    // サウンド停止、キャラToonColor戻す
            Status = CutInStatus.End;
        }

        /// <summary>
        /// Timelineのイベントコールバックを設定
        /// </summary>
        public override void RegisterCallback()
        {
            base.RegisterCallback();
            
            _timelineController.OnCharacterMotionChange = OnTimelineCharacterMotionChange;   // キャラモーション
            _timelineController.OnCameraMotionChange = OnCameraMotionChange;         
        }

        #endregion

        #region モーション
        // -------------------------------------------------------------------------------------------------
        // モーション
        // -------------------------------------------------------------------------------------------------

        /// <summary>ファイル名からモーション名を取得するためのパターン文字列</summary>
        private const string REGEX_PATTERN_FILE_TO_MOTION_NAME = "_chr[0-9]{4}_[0-9]{2}_";
        
        private int _changeCameraMotionIndex = -1;

        private Dictionary<int, Dictionary<int, string>>[] _bodyMotionResourceDicArray = null;
        private Dictionary<int, Dictionary<int, string>> _facialMotionResourceDic = null;
        private Dictionary<int, Dictionary<int, string>> _earMotionResourceDic = null;
        private Dictionary<int, Dictionary<int, string>> _positionMotionResourceDic = null;
        private Dictionary<int, List<string>> _cameraMotionResourceDic = null;
        private Dictionary<int, List<string>>[] _multiCameraMotionResourceDic = null;

        private Dictionary<int, Dictionary<int, AnimationClip>> _positionAnimationDic = null;
        private Dictionary<int, Dictionary<int, AnimationClip>>[] _bodyAnimationDicArray = null;
        private Dictionary<int, Dictionary<int, DrivenKeyAnimation>> _facialAnimationDic = null;
        private Dictionary<int, Dictionary<int, DrivenKeyAnimation>> _earAnimationDic = null;
        private Dictionary<int, List<AnimationClip>>[] _cameraAnimationDic = null;
        
        /// <summary>
        /// モーション入れ替えに使用するアセット
        /// </summary>
        private EventSwapMotionRuntimeData _swapMotionData = null;

        /// <summary>
        /// モーションリソース辞書作成
        /// モーションリソース、カメラリソースの辞書作成
        /// </summary>
        private void SetupMotionResourceDic()
        {
            // アクティブなシートを取得
            var workSheet = _timelineController.GetActiveWorkSheet();
            
            List<int> changeCameraMotionIndexList = new List<int>{-1, -1};

            List<TimelineCharacterGroupData> charaDataList = workSheet._characterList;

            // キャラクターモーションリソース登録
            _bodyMotionResourceDicArray = new Dictionary<int, Dictionary<int, string>>[TimelineDefine.BODY_ANIMATION_CLIP_NUM];
            for (var i = 0; i < TimelineDefine.BODY_ANIMATION_CLIP_NUM; i++)
            {
                _bodyMotionResourceDicArray[i] = new Dictionary<int, Dictionary<int, string>>();
            }
            _facialMotionResourceDic = new Dictionary<int, Dictionary<int, string>>();
            _earMotionResourceDic = new Dictionary<int, Dictionary<int, string>>();
            _positionMotionResourceDic = new Dictionary<int, Dictionary<int, string>>();
            var charaBuildInfoList = new List<CharacterBuildInfo>();
            for (int j = 0; j < charaDataList.Count; ++j)
            {
                // 対象GroupIdではないのでパス
                if (_timelineController.IsPlayGroup(charaDataList[j]._characterMotionKeys.GroupId) == false)
                {
                    continue;
                }

                var charaIndex = charaDataList[j]._characterKeys.CharaIndex;
                var charaId = _context.CharaIdList[j];
                var dressId = _context.DressIdList[j];
                var charaBuildInfo = new CharacterBuildInfo(0, charaId, dressId, ModelLoader.ControllerType.SingleRace);
                charaBuildInfoList.Add(charaBuildInfo);
                for (var bodyMotionIndex = 0; bodyMotionIndex < TimelineDefine.BODY_ANIMATION_CLIP_NUM; bodyMotionIndex++)
                {
                    SetCharacterMotionResource(charaId, j, charaIndex,
                        charaDataList[j]._characterMotionKeys,
                        changeCameraMotionIndexList, dressId);
                }
            }

            // カメラモーションリソース登録
            if (_timelineController.IsMainCutt == true)
            {
                _cameraMotionResourceDic =
                    CreateCameraMotionResourceDictionary(workSheet, changeCameraMotionIndexList[0],
                        charaBuildInfoList, null);

                var multiCameraGroupArray = workSheet._multiCameraList;
                _multiCameraMotionResourceDic = new Dictionary<int, List<string>>[multiCameraGroupArray.Count];
                int index = 0;
                foreach (var multiCameraGroup in multiCameraGroupArray)
                {
                    _multiCameraMotionResourceDic[index] = new Dictionary<int, List<string>>();
                    CreateCameraMotionResourceDictionary(_multiCameraMotionResourceDic[index], changeCameraMotionIndexList[1], charaBuildInfoList,
                                                            multiCameraGroup.MotionKeys, multiCameraGroup.CameraTargetKeys, null);
                }
            }
        }

        private static void CreateCameraMotionResourceDictionary(
            Dictionary<int, List<string>> cameraMotionResourceDic,
            int centerCharaMotionIndex,
            List<CharacterBuildInfo> charaBuildInfoList,
            TimelineKeyCameraMotionDataList motionDataKeyList,
            TimelineKeyCameraTargetCharaDataList cameraTargetKeyList,
            Context context)
        {
            TimelineKeyCameraMotionDataList cameraMotionDataList = motionDataKeyList;

            for (int j = 0, motionCount = cameraMotionDataList.Count; j < motionCount; ++j)
            {
                // カメラにモーションが設定されていなければスルー
                var cameraMotionData = cameraMotionDataList.thisList[j];
                if (cameraMotionData._clip == null)
                {
                    continue;
                }

                // センターのキャラの性格によるモーション読み替え
                string cameraAnimationName = cameraMotionData._clip.name;
                if (cameraMotionData.ReplacableClips != null && centerCharaMotionIndex >= 0 &&
                    cameraMotionData.ReplacableClips.Count > centerCharaMotionIndex)
                {
                    string setName = cameraMotionData.ReplacableClips[centerCharaMotionIndex].name;
                    if (setName != IGNORE_MOTION_NAME)
                    {
                        cameraAnimationName = setName;
                    }
                }

                // ターゲットキャラによって性格別モーションを読み替えるので、
                // ターゲットキャラ指定が変わるすべてのフレームのキャッシュを作成する
                var motionStart = cameraMotionData._frame;
                var motionEnd = j < cameraMotionDataList.thisList.Count
                    ? cameraMotionDataList.thisList[j]._frame
                    : int.MaxValue;
                var targetCharaList = cameraTargetKeyList;
                var targetCharaKeyList =
                    targetCharaList.GetTempKeyListBetweenFrame(motionStart, motionEnd);

                // 単発再生またはキャラ差分なしのリソース登録
                if (!cameraMotionData.IsUseTypeMotion || targetCharaKeyList.Count == 0)
                {
                    var personalityId = COMMON_PERSONALITY_ID;
                    var cameraFilePath = GetCameraMotionPath(personalityId);
                    cameraMotionResourceDic.Add(cameraMotionData._frame, new List<string> { cameraFilePath });
                }
                // ブレンド再生のリソース登録
                else
                {
                    // ターゲットキャラが変わってから次に変わるまでの間に使う2つのモーション単位でキャッシュする
                    for (int k = 0, count = targetCharaKeyList.Count; k < count; ++k)
                    {
                        var frame = targetCharaKeyList[k]._frame;
                        if (!cameraMotionResourceDic.ContainsKey(frame))
                        {
                            // このフレームで読み込むモーションが既にキャッシュ済みなのでスキップ
                            continue;
                        }

                        var motionList = new List<string>();
                        // ブレンド分のアニメーションを確保したいので次のフレームも調べる
                        for (int indexOffset = 0; indexOffset < 2; indexOffset++)
                        {
                            if (TryGetCameraMotionPath(k + indexOffset, out var cameraAnimationPath))
                            {
                                motionList.Add(cameraAnimationPath);
                            }
                        }
                        cameraMotionResourceDic.Add(frame, motionList);
                    }

                    // ターゲットキャラ指定がない期間のモーションをキャッシュする
                    // （CameraMotionの初めからCameraCharaHeightの初めの間）
                    if (!cameraMotionResourceDic.ContainsKey(motionStart))
                    {
                        var cameraFilePath = GetCameraMotionPath(0);
                        cameraMotionResourceDic.Add(motionStart, new List<string> { cameraFilePath });
                    }
                }

                #region インナー関数
                // ターゲットキャラの性格に合わせて読み替えたアニメーションクリップのパスを保存
                bool TryGetCameraMotionPath(int index, out string path)
                {
                    if (index < 0 || targetCharaList.Count <= index)
                    {
                        // 異常：キャラがいない
                        path = null;
                        return false;
                    }

                    var personalityId = COMMON_PERSONALITY_ID;
                    path = GetCameraMotionPath(personalityId);
                    return true;
                }
                #endregion

                string GetCameraMotionPath(int personality)
                    => ResourcePath.GetVsCameraMotionPath(
                        cameraMotionData,
                        MotionType.Camera, personality, centerCharaMotionIndex);
            }
        }

        public static Dictionary<int, List<string>> CreateCameraMotionResourceDictionary(
            CutInTimelineWorkSheet workSheet, int centerCharaMotionIndex,
            List<CharacterBuildInfo> charaBuildInfoList,
            Context context)
        {
            var cameraMotionResourceDic = new Dictionary<int, List<string>>();
            CreateCameraMotionResourceDictionary(cameraMotionResourceDic, centerCharaMotionIndex, charaBuildInfoList, 
                                                workSheet._cameraMotionKeys, workSheet._cameraTargetCharaKeys, context);

            return cameraMotionResourceDic;
        }

        /// <summary>
        /// モーションリソースの登録
        /// </summary>
        private void SetCharacterMotionResource(
            int charaId, int characterIndex, int charaVariationIndex,
            TimelineKeyCharacterMotionDataList motionKeyList,
            List<int> changeCameraMotionIndexList, int dressId)
        {
            //モーションの読み込み
            //キーをすべて舐めて必要なリソースをダウンロード、その際にキャラ別に読み込み先を変える必要がないかをチェックする
            //まずは読み替え用リストを取得する
            int changedIndex = -1;

            // 入れ替えランタイムデータ作成
            _swapMotionData = EventSwapMotionRuntimeData.Create(dressId, charaId);
            
            //キャラIDとindexがあっていれば読み替え対象なのでリストを読み込む
            var cuttCharaDataList = MasterDataManager.Instance.masterLegendRaceCuttCharaData.GetListWithSubIdAndCharaNumOrderByCharaIdAsc(1, 2);
            
            for (int k = 0; k < cuttCharaDataList.Count; ++k)
            {
                //index
                if (cuttCharaDataList[k].CharaId == charaId && cuttCharaDataList[k].TargetTimeline == characterIndex)
                {
                    // NOTE:VSカットではBodyモーションが元々キャラ別にロードされるので、育成と違ってカメラの切り替えだけで十分かもしれない
                    changedIndex = cuttCharaDataList[k].TargetListIndex;

                    //キャラのモーション読み替えが発生した場合は、カメラのモーションも変更する
                    changeCameraMotionIndexList[characterIndex] = changedIndex;
                    break;
                }
            }

            //モーションのキーをすべて取得し、変更する必要があれば変更する
            for (int k = 0; k < motionKeyList.Count; ++k)
            {
                TimelineKeyCharacterMotionData motionDataKey = motionKeyList.thisList[k];
                if (motionDataKey == null)
                {
                    continue;
                }

                var variationId = COMMON_PERSONALITY_ID;

                //Body
                for (var bodyClipIndex = 0; bodyClipIndex < TimelineDefine.BODY_ANIMATION_CLIP_NUM; bodyClipIndex++)
                {
                    if (bodyClipIndex > 0 && !motionDataKey.IsUseHeightDiffMotion) // 体アニメーションは1番目以降の要素がNullの場合があるのでNullの場合は無視
                    {
                        continue;
                    }

                    var bodyFilePath = ResourcePath.GetVsCharaMotionPath(motionDataKey, MotionType.Body, charaId, variationId, changedIndex, bodyClipIndex: bodyClipIndex);

                    // EventSwapMotionでパスを置き換えたファイルパスの取得
                    bodyFilePath = GetEventSwapMotionBodyFilePath(bodyFilePath);
                    
                    if (!_bodyMotionResourceDicArray[bodyClipIndex].ContainsKey(characterIndex))
                    {
                        _bodyMotionResourceDicArray[bodyClipIndex].Add(characterIndex, new Dictionary<int, string>());
                    }

                    if (!_bodyMotionResourceDicArray[bodyClipIndex][characterIndex].ContainsKey(motionDataKey._frame))
                    {
                        _bodyMotionResourceDicArray[bodyClipIndex][characterIndex].Add(motionDataKey._frame, bodyFilePath);
                    }
                }

                //Facial
                string facialFilePath = ResourcePath.GetVsCharaMotionPath(motionDataKey, MotionType.Facial, charaId, variationId, changedIndex);
                if (!_facialMotionResourceDic.ContainsKey(characterIndex))
                {
                    _facialMotionResourceDic.Add(characterIndex, new Dictionary<int, string>());
                }
                if (!_facialMotionResourceDic[characterIndex].ContainsKey(motionDataKey._frame))
                {
                    _facialMotionResourceDic[characterIndex].Add(motionDataKey._frame, facialFilePath);
                }

                //Ear
                string earFilePath = ResourcePath.GetVsCharaMotionPath(motionDataKey, MotionType.Ear, charaId, variationId, changedIndex);
                if (!_earMotionResourceDic.ContainsKey(characterIndex))
                {
                    _earMotionResourceDic.Add(characterIndex, new Dictionary<int, string>());
                }
                if (!_earMotionResourceDic[characterIndex].ContainsKey(motionDataKey._frame))
                {
                    _earMotionResourceDic[characterIndex].Add(motionDataKey._frame, earFilePath);
                }

                //Position
                string positionFilePath = ResourcePath.GetVsCharaMotionPath(motionDataKey, MotionType.Position, charaId, variationId, changedIndex);
                if (!_positionMotionResourceDic.ContainsKey(characterIndex))
                {
                    _positionMotionResourceDic.Add(characterIndex, new Dictionary<int, string>());
                }
                if (!_positionMotionResourceDic[characterIndex].ContainsKey(motionDataKey._frame))
                {
                    _positionMotionResourceDic[characterIndex].Add(motionDataKey._frame, positionFilePath);
                }
            }
        }

        /// <summary>
        /// BodyのモーションをEventSwapMotionでVS用に置き換えたパスを取得する
        /// </summary>
        /// <param name="bodyFilePath"></param>
        /// <returns></returns>
        private string GetEventSwapMotionBodyFilePath(string bodyFilePath)
        {
            // _chrxxxx_xx_モーション名を含むパスをEventSwapMotionで置き換え
            string clipName = Path.GetFileName(bodyFilePath);
            var match = System.Text.RegularExpressions.Regex.Match(clipName, REGEX_PATTERN_FILE_TO_MOTION_NAME);
            if (match.Index > 0)
            {
                clipName = clipName.Substring(match.Index + match.Length);
            }
            return _swapMotionData.GetMotionClipName(clipName, bodyFilePath);
        }
        
        private void LoadMotion(Dictionary<int, List<AnimationClip>> animationDictionary, Dictionary<int, List<string>> animationPathDictionary)
        {
            foreach (KeyValuePair<int, List<string>> resourcePair in animationPathDictionary)
            {
                if (animationDictionary.ContainsKey(resourcePair.Key))
                {
                    continue;
                }

                var count = resourcePair.Value.Count;
                var clipList = new List<AnimationClip>(count);
                for (var i = 0; i < count; i++)
                {
                    clipList.Add(ResourceManager.LoadOnScene<AnimationClip>(resourcePair.Value[i]));
                }
                animationDictionary.Add(resourcePair.Key, clipList);
            }
        }

        /// <summary>
        /// モーションリソース辞書に登録されたモーションロード
        /// </summary>
        private void LoadMotion()
        {
            _bodyAnimationDicArray = new Dictionary<int, Dictionary<int, AnimationClip>>[TimelineDefine.BODY_ANIMATION_CLIP_NUM];
            for (var i = 0; i < TimelineDefine.BODY_ANIMATION_CLIP_NUM; i++)
            {
                _bodyAnimationDicArray[i] = SetMotionDic(ref _bodyMotionResourceDicArray[i]);
            }
            int cameraAnimationLineNum = 1 + _multiCameraMotionResourceDic.Length;

            _facialAnimationDic = SetFacialMotionDic(ref _facialMotionResourceDic);
            _earAnimationDic = SetFacialMotionDic(ref _earMotionResourceDic);
            _positionAnimationDic = SetMotionDic(ref _positionMotionResourceDic);
            _cameraAnimationDic = new Dictionary<int, List<AnimationClip>>[cameraAnimationLineNum];
            _cameraAnimationDic[0] = new Dictionary<int, List<AnimationClip>>(_cameraMotionResourceDic.Count);
            LoadMotion(_cameraAnimationDic[0], _cameraMotionResourceDic);
            for(int i=1;i< cameraAnimationLineNum; i++)
            {
                _cameraAnimationDic[i] = new Dictionary<int, List<AnimationClip>>(_multiCameraMotionResourceDic[i - 1].Count);
                LoadMotion(_cameraAnimationDic[i], _multiCameraMotionResourceDic[i-1]);
            }
        }

        private Dictionary<int, Dictionary<int, AnimationClip>> SetMotionDic(ref Dictionary<int, Dictionary<int, string>> resourceInfoDic)
        {
            Dictionary<int, Dictionary<int, AnimationClip>> setAnimationDic = new Dictionary<int, Dictionary<int, AnimationClip>>();
            if (resourceInfoDic != null)
            {
                foreach (KeyValuePair<int, Dictionary<int, string>> resourceBasePair in resourceInfoDic)
                {
                    int timelineIndex = resourceBasePair.Key;
                    if (!setAnimationDic.ContainsKey(timelineIndex))
                    {
                        setAnimationDic.Add(timelineIndex, new Dictionary<int, AnimationClip>());
                    }

                    foreach (KeyValuePair<int, string> resourcePair in resourceBasePair.Value)
                    {
                        if (setAnimationDic[timelineIndex].ContainsKey(resourcePair.Key))
                        {
                            continue;
                        }

                        setAnimationDic[timelineIndex].Add(resourcePair.Key,
                            ResourceManager.LoadOnScene<AnimationClip>(resourcePair.Value));
                    }
                }
            }

            return setAnimationDic;
        }

        private Dictionary<int, Dictionary<int, DrivenKeyAnimation>> SetFacialMotionDic(ref Dictionary<int, Dictionary<int, string>> resourceInfoDic)
        {
            Dictionary<int, Dictionary<int, DrivenKeyAnimation>> setAnimationDic = new Dictionary<int, Dictionary<int, DrivenKeyAnimation>>();
            foreach (KeyValuePair<int, Dictionary<int, string>> resourceBasePair in resourceInfoDic)
            {
                int timelineIndex = resourceBasePair.Key;
                if (!setAnimationDic.ContainsKey(timelineIndex))
                {
                    setAnimationDic.Add(timelineIndex, new Dictionary<int, DrivenKeyAnimation>());
                }
                foreach (KeyValuePair<int, string> resourcePair in resourceBasePair.Value)
                {
                    if (setAnimationDic[timelineIndex].ContainsKey(resourcePair.Key))
                    {
                        continue;
                    }
                    setAnimationDic[timelineIndex].Add(resourcePair.Key, ResourceManager.LoadOnScene<DrivenKeyAnimation>(resourcePair.Value));
                }
            }

            return setAnimationDic;
        }

        /// <summary>
        /// タイムラインイベント：キャラモーション変更
        /// </summary>
        private void OnTimelineCharacterMotionChange(int timelineIndex, CutInTimelineController cutInTimelineController, TimelineCharacter character, int charaVariationIndex, TimelineKeyCharacterMotionData key)
        {
            if (key == null) return;
            if (character._cutInCharacter.Model == null) return;

            // Body
            int animationCount = TimelineDefine.BODY_ANIMATION_CLIP_HEIGHT_NUM; // 身長差対応しかないので２個
            for (var bodyAnimationIndex = 0;
                bodyAnimationIndex < animationCount;
                bodyAnimationIndex++)
            {
                // モーション設定がなければ処理しない
                if (key.BodyClips[bodyAnimationIndex] == null)
                {
                    if (bodyAnimationIndex == 0 || key.IsUseHeightDiffMotion)
                    {
                        Debug.LogWarningFormat("キーにモーションが刺さっていない@{0}フレーム", key._frame.ToString());
                    }

                    continue;
                }

                // クリップをロード（入れ替えがあれば入れ替わる）
                var bodyAnimeClip = ReplaceVsMotion(timelineIndex, MotionType.Body, key, charaVariationIndex,
                    character.LoadedBodyClipArray[bodyAnimationIndex], character, bodyAnimationIndex)
                    ?? key.BodyClips[bodyAnimationIndex];

                // 前回と違うクリップだったら保持しなおす
                if (character.LoadedBodyClipArray[bodyAnimationIndex] != bodyAnimeClip)
                {
                    character.LoadedBodyClipArray[bodyAnimationIndex] = bodyAnimeClip;
                }

                // AnimatorOverrideControllerを通じてAnimatorのクリップを切り替え
                var setIndex = CutInTimelineController.OVERRIDE_TARGET_CLIP_ARRAY[bodyAnimationIndex];
                var animator = character._cutInCharacter.Animator;
                if (!animator.IsClip(setIndex) || ((bodyAnimeClip != null) && (animator.GetClip(setIndex) != bodyAnimeClip)))
                {
                    animator.SetClip(setIndex, bodyAnimeClip);
                    if (key.IsResetCyspring == true)
                    {
                        // 体モーションの切替とセットでCySpringをリセットかける
                        // - 昔は体モーション切替時に常にリセットされていたので、体モーション縛りをかけている
                        character._cutInCharacter.Model.ReserveWarmingUpCySpring();
                    }
                }
            }

            // Facial
            var facialClip = ReplaceVsFacialMotion(timelineIndex, MotionType.Facial, key, charaVariationIndex, character.LoadedFacialClip, character)
                ?? key.FacialClip;

            if (facialClip != null && character.LoadedFacialClip != facialClip)
            {
                var keep = character.LoadedFacialClip;
                character.LoadedFacialClip = facialClip;
                if (character.LoadedFacialClip != null)
                {
                    character._cutInCharacter.Model.PlayDrivenKey(character.LoadedFacialClip);
                }
                else
                {
                    if (keep != null)
                    {
                        character._cutInCharacter.Model.PlayDrivenKey(null);
                    }
                }
            }

            // Ear
            var earClip = ReplaceVsFacialMotion(timelineIndex, MotionType.Ear, key, charaVariationIndex, character.LoadedEarClip, character)
                ?? key.EarClip;

            if (earClip != null && character.LoadedEarClip != earClip)
            {
                var keep = character.LoadedEarClip;
                character.LoadedEarClip = earClip;
                if (character.LoadedEarClip != null)
                {
                    character._cutInCharacter.Model.PlayEarDrivenKey(character.LoadedEarClip);
                }
                else
                {
                    if (keep != null)
                    {
                        character._cutInCharacter.Model.PlayEarDrivenKey(null);
                    }
                }
            }

            // Position
            var positionClip = ReplaceVsMotion(timelineIndex, MotionType.Position, key, charaVariationIndex, character.LoadedPositionClip, character)
                ?? key.PositionClip;

            if (positionClip != null && character.LoadedPositionClip != positionClip)
            {
                var keep = character.LoadedPositionClip;
                character.LoadedPositionClip = positionClip;
                if (character.LoadedPositionClip != null)
                {
                    character._positionLocator.SetClip(character.LoadedPositionClip);
                    character._positionLocator.SetTargetTransform(character._cutInCharacter.Model.transform);
                }
                else
                {
                    if (keep != null)
                    {
                        character._positionLocator.SetClip(null);
                    }
                }
            }
        }
        
        /// <summary>
        /// モーションファイル名をラベルに関連づいた内容に置き換える
        /// </summary>
        private AnimationClip ReplaceVsMotion(int timelineIndex, MotionType motionType,
            TimelineKeyCharacterMotionData key, int charaVariationIndex, AnimationClip currentAnime = null,
            TimelineCharacter characterClass = null, int bodyAnimationIndex = 0)
        {
            if (_timelineController == null)
            {
                return null;
            }
            if (key == null)
            {
                return null;
            }

            AnimationClip replaceAnimationClip = null;


#if CYG_DEBUG && UNITY_EDITOR
            //ワークシートは1枚しか存在しない想定で必ず取得できる想定
            CutInTimelineWorkSheet workSheet = _timelineController.GetActiveWorkSheet();
            if (workSheet == null)
            {
                return null;
            }
            //キャラのリストも必ず存在する想定
            List<TimelineCharacterGroupData> charaDataList = workSheet._characterList;
            if (charaDataList == null || charaDataList.Count <= timelineIndex)
            {
                return null;
            }

            //まずは読み替え用リストを取得する
            int changedIndex = -1;
            //キャラIDとindexがあっていれば読み替え対象なのでリストを読み込む
            if (characterClass != null)
            {
                //キャラIDとindexがあっていれば読み替え対象なのでリストを読み込む
                int subId = 1;
                int charaNum = 2;

                var cuttCharaDataList = MasterDataManager.Instance.masterLegendRaceCuttCharaData.GetListWithSubIdAndCharaNumOrderByCharaIdAsc(subId, charaNum);
                for (int k = 0; k < cuttCharaDataList.Count; ++k)
                {
                    //index
                    if (cuttCharaDataList[k].CharaId == characterClass._cutInCharacter.Model.GetCharaID() && cuttCharaDataList[k].TargetTimeline == timelineIndex)
                    {
                        changedIndex = cuttCharaDataList[k].TargetListIndex;
                        break;
                    }
                }
            }

            //センターキャラの場合はカメラのモーション変更indexも変更する
            if (timelineIndex == 0)
            {
                _changeCameraMotionIndex = changedIndex;
            }
#endif

            switch (motionType)
            {
                case MotionType.Body:
                    //本番環境では辞書があるので、ここで引っ張ってこれるはず。本番環境はこれ以降の処理に入らない想定
                    if (_bodyAnimationDicArray?[bodyAnimationIndex] != null && _bodyAnimationDicArray[bodyAnimationIndex].ContainsKey(timelineIndex))
                    {
                        if (_bodyAnimationDicArray[bodyAnimationIndex][timelineIndex] != null && _bodyAnimationDicArray[bodyAnimationIndex][timelineIndex].ContainsKey(key._frame))
                        {
                            return _bodyAnimationDicArray[bodyAnimationIndex][timelineIndex][key._frame];
                        }
                    }

#if CYG_DEBUG && UNITY_EDITOR
                    {
                        //主にCutt上になるが、リアルタイムで変える必要があるので文字列連結でロードする
                        if (key.BodyClips[bodyAnimationIndex] == null)
                        {
                            break;
                        }
                        //読み替えがあるなら読み替える
                        var charaLine = charaDataList[timelineIndex];

                        // 読み替えたクリップの名前を取得
                        string animeName = key.BodyClips[bodyAnimationIndex].name;
                        var replaceClips = key.BodyReplaceClipList[bodyAnimationIndex];
                        if (replaceClips != null && changedIndex >= 0 && replaceClips.Count > changedIndex)
                        {
                            if (replaceClips[changedIndex] != null)
                            {
                                string setName = replaceClips[changedIndex].name;
                                if (setName != IGNORE_MOTION_NAME)
                                {
                                    animeName = setName;
                                }
                            }
                        }

                        //毎回ロードするのは嫌なので、アニメーション名が現在と同じ名前ならそのままを返す
                        if (currentAnime != null && animeName == currentAnime.name)
                        {
                            return currentAnime;
                        }

                        int personalityId = COMMON_PERSONALITY_ID;
                        //アニメ名が違うのでロード
                        string bodyFilePath = ResourcePath.GetVsCharaMotionPathByName(animeName, MotionType.Body, characterClass._cutInCharacter.Model.GetCharaID(), personalityId);
                        replaceAnimationClip = ResourceManager.IsExistAsset(bodyFilePath)
                            ? ResourceManager.LoadOnView<AnimationClip>(bodyFilePath)
                            : default;
                    }
#endif
                    break;

                case MotionType.Position:
                    //本番環境では辞書があるので、ここで引っ張ってこれるはず。本番環境はこれ以降の処理に入らない想定
                    if (_positionAnimationDic != null && _positionAnimationDic.ContainsKey(timelineIndex))
                    {
                        if (_positionAnimationDic[timelineIndex] != null && _positionAnimationDic[timelineIndex].ContainsKey(key._frame))
                        {
                            return _positionAnimationDic[timelineIndex][key._frame];
                        }
                    }

#if CYG_DEBUG && UNITY_EDITOR
                    {
                        bool isSuccess = _timelineController.RequireType != CutInTimelineController.PlayRequireType.Failure;
                        //主にCutt上になるが、リアルタイムで変える必要があるので文字列連結でロードする
                        if (key.PositionClip == null)
                        {
                            break;
                        }

                        string positionAnimeName = key.PositionClip.name;
                        var charaLine = charaDataList[timelineIndex];
                        //読み替えがあるなら読み替える
                        if (key.PositionReplacableClips != null && changedIndex >= 0 && key.PositionReplacableClips.Count > changedIndex)
                        {
                            if (key.PositionReplacableClips[changedIndex] != null)
                            {
                                string setName = key.PositionReplacableClips[changedIndex].name;
                                if (setName != IGNORE_MOTION_NAME)
                                {
                                    positionAnimeName = setName;
                                }
                            }
                        }

                        //毎回ロードするのは嫌なので、アニメーション名が現在と同じ名前ならそのままを返す
                        if (currentAnime != null && positionAnimeName == currentAnime.name)
                        {
                            return currentAnime;
                        }

                        int personalityId = COMMON_PERSONALITY_ID;

                        //アニメ名が違うのでロード
                        string positionFilePath = ResourcePath.GetVsCharaMotionPathByName(positionAnimeName, MotionType.Position, characterClass._cutInCharacter.Model.GetCharaID(), personalityId);
                        replaceAnimationClip = ResourceManager.IsExistAsset(positionFilePath)
                            ? ResourceManager.LoadOnView<AnimationClip>(positionFilePath)
                            : default;
                    }
#endif
                    break;
            }

            return replaceAnimationClip;
        }

        /// <summary>
        /// モーションファイル名をラベルに関連づいた内容に置き換える
        /// </summary>
        private DrivenKeyAnimation ReplaceVsFacialMotion(int timelineIndex, MotionType motionType, TimelineKeyCharacterMotionData key, int charaVariationIndex, DrivenKeyAnimation currentAnime = null, TimelineCharacter characterClass = null)
        {
            if (_timelineController == null)
            {
                return null;
            }
            if (key == null)
            {
                return null;
            }

            DrivenKeyAnimation replaceAnimationClip = null;

#if CYG_DEBUG && UNITY_EDITOR
            //ワークシートは1枚しか存在しない想定で必ず取得できる想定
            CutInTimelineWorkSheet workSheet = _timelineController.GetActiveWorkSheet();
            if (workSheet == null)
            {
                return null;
            }
            //キャラのリストも必ず存在する想定
            List<TimelineCharacterGroupData> charaDataList = workSheet._characterList;
            if (charaDataList == null || charaDataList.Count <= timelineIndex)
            {
                return null;
            }

            //まずは読み替え用リストを取得する
            int changedIndex = -1;
            //キャラIDとindexがあっていれば読み替え対象なのでリストを読み込む
            if (characterClass != null)
            {
                //キャラIDとindexがあっていれば読み替え対象なのでリストを読み込む
                int subId = 1;
                int charaNum = 2;

                var cuttCharaDataList = MasterDataManager.Instance.masterLegendRaceCuttCharaData.GetListWithSubIdAndCharaNumOrderByCharaIdAsc(subId, charaNum);
                for (int k = 0; k < cuttCharaDataList.Count; ++k)
                {
                    //index
                    if (cuttCharaDataList[k].CharaId == characterClass._cutInCharacter.Model.GetCharaID() && cuttCharaDataList[k].TargetTimeline == timelineIndex)
                    {
                        changedIndex = cuttCharaDataList[k].TargetListIndex;
                        break;
                    }
                }
            }

            //センターキャラの場合はカメラのモーション変更indexも変更する
            if (timelineIndex == 0)
            {
                _changeCameraMotionIndex = changedIndex;
            }
#endif

            switch (motionType)
            {
                case MotionType.Facial:
                    //本番環境では辞書があるので、ここで引っ張ってこれるはず。本番環境はこれ以降の処理に入らない想定
                    if (_facialAnimationDic != null && _facialAnimationDic.ContainsKey(timelineIndex))
                    {
                        if (_facialAnimationDic[timelineIndex] != null && _facialAnimationDic[timelineIndex].ContainsKey(key._frame))
                        {
                            return _facialAnimationDic[timelineIndex][key._frame];
                        }
                    }

#if CYG_DEBUG && UNITY_EDITOR
                    {
                        bool isSuccess = _timelineController.RequireType != CutInTimelineController.PlayRequireType.Failure;
                        //主にCutt上になるが、リアルタイムで変える必要があるので文字列連結でロードする
                        if (key.FacialClip == null)
                        {
                            break;
                        }
                        string facialAnimeName = key.FacialClip.name;

                        var charaLine = charaDataList[timelineIndex];
                        //読み替えがあるなら読み替える
                        if (key.FacialReplacableClips != null && changedIndex >= 0 && key.FacialReplacableClips.Count > changedIndex)
                        {
                            if (key.FacialReplacableClips[changedIndex] != null)
                            {
                                string setName = key.FacialReplacableClips[changedIndex].name;
                                if (setName != IGNORE_MOTION_NAME)
                                {
                                    facialAnimeName = setName;
                                }
                            }
                        }

                        //毎回ロードするのは嫌なので、アニメーション名が現在と同じ名前ならそのままを返す
                        if (currentAnime != null && facialAnimeName == currentAnime.name)
                        {
                            return currentAnime;
                        }

                        int personalityId = COMMON_PERSONALITY_ID;

                        //アニメ名が違うのでロード
                        string facialFilePath = ResourcePath.GetVsCharaMotionPathByName(facialAnimeName, MotionType.Facial, characterClass._cutInCharacter.Model.GetCharaID(), personalityId);
                        replaceAnimationClip = ResourceManager.IsExistAsset(facialFilePath)
                            ? ResourceManager.LoadOnView<DrivenKeyAnimation>(facialFilePath)
                            : default;
                    }
#endif
                    break;

                case MotionType.Ear:
                    //本番環境では辞書があるので、ここで引っ張ってこれるはず。本番環境はこれ以降の処理に入らない想定
                    if (_earAnimationDic != null && _earAnimationDic.ContainsKey(timelineIndex))
                    {
                        if (_earAnimationDic[timelineIndex] != null && _earAnimationDic[timelineIndex].ContainsKey(key._frame))
                        {
                            return _earAnimationDic[timelineIndex][key._frame];
                        }
                    }

#if CYG_DEBUG && UNITY_EDITOR
                    {
                        bool isSuccess = _timelineController.RequireType != CutInTimelineController.PlayRequireType.Failure;
                        //主にCutt上になるが、リアルタイムで変える必要があるので文字列連結でロードする
                        if (key.FacialClip == null)
                        {
                            break;
                        }
                        string facialAnimeName = key.FacialClip.name;

                        var charaLine = charaDataList[timelineIndex];
                        //読み替えがあるなら読み替える
                        if (key.EarReplacableClips != null && changedIndex >= 0 && key.EarReplacableClips.Count > changedIndex)
                        {
                            if (key.EarReplacableClips[changedIndex] != null)
                            {
                                string setName = key.EarReplacableClips[changedIndex].name;
                                if (setName != IGNORE_MOTION_NAME)
                                {
                                    facialAnimeName = setName;
                                }
                            }
                        }

                        //毎回ロードするのは嫌なので、アニメーション名が現在と同じ名前ならそのままを返す
                        if (currentAnime != null && facialAnimeName == currentAnime.name)
                        {
                            return currentAnime;
                        }

                        int personalityId = COMMON_PERSONALITY_ID;

                        //アニメ名が違うのでロード
                        string facialFilePath = ResourcePath.GetVsCharaMotionPathByName(facialAnimeName, MotionType.Ear, characterClass._cutInCharacter.Model.GetCharaID(), personalityId);
                        replaceAnimationClip = ResourceManager.IsExistAsset(facialFilePath)
                            ? ResourceManager.LoadOnView<DrivenKeyAnimation>(facialFilePath)
                            : default;
                    }
#endif
                    break;

            }

            return replaceAnimationClip;
        }

        #endregion
        
        #region カメラ
        // -------------------------------------------------------------------------------------------------
        // カメラ
        // -------------------------------------------------------------------------------------------------

        /// <summary>
        /// 後更新の前更新：カメラ
        /// カメラの座標と回転をロケーターに追従させる
        /// </summary>
        private void OnPreLateUpdateForCamera()
        {
            FollowCharacterCamera();
        }
        
        /// <summary>
        /// カメラモーション変更時のコールバック
        /// </summary>
        private void OnCameraMotionChange(ref CutInTimelineController.CameraMotionChangeContext context)
        {
            var key = context.CameraMotionKey;
            var motionCamera = context.MotionCamera;

            var targetCharaData = context.CameraTargetCharaKeyList;
            var targetCharaKey = targetCharaData.FindCurrentKey(_timelineController.CurrentFrame).key as TimelineKeyCameraTargetCharaData;
            SetCameraMotion(motionCamera, context.CameraMotionKeyList, key, targetCharaData, targetCharaKey);

            // 時間設定
            var startTime = key._frame / (float)_timelineController.TargetFps;
            var setTime = _timelineController.CurrentTime - startTime - key._motionHeadTime;
            var currentTime = setTime * key._playSpeed;
            motionCamera.MyAnimator.Play(CutInPlayableAnimator.LayerIndex.Base, currentTime);
            motionCamera.MyAnimator.Speed = 0;
            motionCamera.MyAnimator.UpdateMotion(0);
        }

        /// <summary>
        /// カメラのモーションの読み替えが発生した場合に呼び出す関数
        /// CameraMotionとCameraCharaHeightトラックの状況に基づいてモーションを読み替える
        /// </summary>
        /// <param name="motionList">CameraMotionトラック。非Null</param>
        /// <param name="motionKey">CameraMotionキー。発火時に非Null</param>
        /// <param name="targetCharaList">CameraCharaHeightトラック。非Null</param>
        /// <param name="targetCharaKey">CameraCharaHeightキー。発火時に非Null</param>
        private void SetCameraMotion(CutInTimelineMotionCamera motionCamera,
            TimelineKeyCameraMotionDataList motionList, TimelineKeyCameraMotionData motionKey,
            TimelineKeyCameraTargetCharaDataList targetCharaList, TimelineKeyCameraTargetCharaData targetCharaKey)
        {
            var frame = motionKey?._frame ?? targetCharaKey._frame; // 発火したキーのフレーム番号
            if (motionKey == null)
            {
                // ターゲットキャラ指定がアップデートした場合はCameraMotionのキーを取得する
                motionKey = motionList.FindCurrentKey(frame).key as TimelineKeyCameraMotionData;
                if (motionKey == null)
                {
                    Debug.LogWarning("ターゲットキャラ指定がカメラモーションより前に発火した");
                    return;
                }
            }
            motionList.FindKeyLinear(out _, out var nextMotionKey, frame);
            targetCharaList.FindKeyLinear(out _, out var nextCharaKeyBase, frame);
            var nextCharaKey = nextCharaKeyBase as TimelineKeyCameraTargetCharaData;
            if (nextCharaKey != null &&
                nextMotionKey != null &&
                nextCharaKey._frame >= nextMotionKey._frame)
            {
                nextCharaKey = null;
            }
            string[] animationNameArray;
            {
                animationNameArray = ReplaceVsCameraMotionName(motionCamera, motionKey, targetCharaKey);

#if CYG_DEBUG && UNITY_EDITOR
                if (animationNameArray == null)
                {
                    animationNameArray = ReplaceVsCameraMotionNameEditor(motionList,motionKey, targetCharaKey, nextCharaKey);
                }
#endif
                if (animationNameArray == null || animationNameArray.Length == 0)
                {
                    // 再生する必要がなければスルー
                    return;
                }
            }

            // トレーニング結果によって読み替える
            var clipName = animationNameArray[0];

            var isChangeAnimation = (!string.IsNullOrEmpty(clipName) && !motionCamera.IsPlaying(clipName));

            if (isChangeAnimation)
            {
                Dictionary<int,List<AnimationClip>> animationDic = null;
                var isAnimationCached = false;
                if (_cameraAnimationDic != null)
                {
                    int cameraIndex = motionCamera.LineIndex;
                    animationDic = _cameraAnimationDic[cameraIndex];
                    isAnimationCached = (animationDic != null && animationDic.ContainsKey(frame));
                }
                // Cuttツールでの編集時以外では
                // アニメーション切り替えタイミングごとにキャッシュされた確定済みのAnimationClipのリストを使う
                if (isAnimationCached)
                {
                    var clipList = animationDic[frame];
                    var clipCmn = clipList[0];
                    var clipCmn2 = nextCharaKey?.IsBlendPersonalityMotion == true && clipList.Count > 0
                        ? clipList[1]
                        : null;
                    motionCamera.PlayNewMotion(clipName, clipCmn, clipCmn2);
                }
                // Cuttツールでの編集時ではキャッシュがないので都度必要なAnimationClipを探す
#if CYG_DEBUG && UNITY_EDITOR
                else
                {
                    //アニメ名が違うのでロード
                    var animationClipFrom = LoadAnimationClip(targetCharaKey, animationNameArray[0]);
                    var animationClipTo = default(AnimationClip);
                    if (nextCharaKey?.IsBlendPersonalityMotion == true)
                    {
                        animationClipTo = LoadAnimationClip(nextCharaKey, animationNameArray[1]);
                    }

                    motionCamera.PlayNewMotion(clipName, animationClipFrom, animationClipTo);

                    AnimationClip LoadAnimationClip(TimelineKeyCameraTargetCharaData k, string animationName)
                    {
                        var workSheet = _timelineController.GetActiveWorkSheet();
                        var personalityId = COMMON_PERSONALITY_ID;

                        var cameraFilePath = ResourcePath.GetVsCameraMotionPathByName(
                            animationName, MotionType.Camera, personalityId);
                        return ResourceManager.LoadOnView<AnimationClip>(cameraFilePath);
                    }
                }
#endif // CYG_DEBUG && UNITY_EDITOR
            }


            // カメラをキャラに追従させるかどうか調べる
            CheckFollowCharacter(motionCamera, targetCharaList, targetCharaKey, nextCharaKey);

        }

        private string[] ReplaceVsCameraMotionName(
            CutInTimelineMotionCamera motionCamera,
            TimelineKeyCameraMotionData motionKey,
            TimelineKeyCameraTargetCharaData targetCharaKey)
        {
            if (motionKey == null)
            {
                return null;
            }

            if(_cameraAnimationDic == null)
            {
                return null;
            }

            var frame = Mathf.Max(motionKey._frame, targetCharaKey?._frame ?? 0);

            int cameraIndex = motionCamera.LineIndex;
            var animationDic = _cameraAnimationDic[cameraIndex];
            if (animationDic.TryGetValue(frame, out var animationList))
            {
                var animationNameArray = new string[animationList.Count];
                for (int i = 0; i < animationNameArray.Length; i++)
                {
                    animationNameArray[i] = animationList[i].name;
                }
                return animationNameArray;
            }

            return null;
        }

        private string[] ReplaceVsCameraMotionNameEditor(
            TimelineKeyCameraMotionDataList motionKeyList,
            TimelineKeyCameraMotionData motionKey,
            TimelineKeyCameraTargetCharaData targetCharaKey, TimelineKeyCameraTargetCharaData nextTargetCharaKey)
        {
#if CYG_DEBUG && UNITY_EDITOR
            if (_timelineController == null)
            {
                return new string[0];
            }
            //ワークシートは1枚しか存在しない想定で必ず取得できる想定
            CutInTimelineWorkSheet workSheet = _timelineController.GetActiveWorkSheet();
            if (workSheet == null)
            {
                return new string[0];
            }
            //キャラのリストも必ず存在する想定
            TimelineKeyCameraMotionDataList motionDataList = motionKeyList;
            if (motionDataList == null || motionDataList.Count <= 0)
            {
                return new string[0];
            }

            //Cutt上はこの値を取得する。Cutt上でのみリアルタイムで変える必要があるので文字列連結を許容する
            var cameraClip = motionKey.GetAnimationClip();
            if (cameraClip == null)
            {
                return new string[0];
            }
            var cameraAnimeName = cameraClip.name;
            //読み替えがあるなら読み替える
            if (motionKey.ReplacableClips != null && _changeCameraMotionIndex >= 0 && motionKey.ReplacableClips.Count > _changeCameraMotionIndex)
            {
                if (motionKey.ReplacableClips[_changeCameraMotionIndex] != null)
                {
                    string setName = motionKey.ReplacableClips[_changeCameraMotionIndex].name;
                    if (setName != IGNORE_MOTION_NAME)
                    {
                        cameraAnimeName = setName;
                    }
                }
            }

            // ターゲットキャラ指定がないまたはブレンド対象がない場合
            if (targetCharaKey == null || nextTargetCharaKey == null)
            {
                return new[] {cameraAnimeName};
            }
            else
            {
                // ターゲットキャラ指定によって性格別モーションを複数ブレンドする場合はその数分だけの要素を返す
                return new[] {cameraAnimeName, cameraAnimeName};
            }
#else
            return new string[0];
#endif
        }

        protected override void OnTargetCharaChange(ref CutInTimelineController.CameraTargetChangeContext context)
        {
            var motionKeyList = context.CameraMotionKeyList;
            var motionKey = motionKeyList.FindCurrentKey(_timelineController.CurrentFrame).key as TimelineKeyCameraMotionData;
            SetCameraMotion(context.MotionCamera, motionKeyList, motionKey,  context.CameraTargetKeyList, context.CameraTargetKey);

            base.OnTargetCharaChange(ref context);
        }

        #endregion

    }
}
