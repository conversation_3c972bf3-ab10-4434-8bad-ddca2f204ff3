using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR





/*=============================================================================
 * LiveTimelineKeyCameraFovData
 */
namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveCameraFovType
    /// </summary>
    public enum LiveCameraFovType
    {
        Direct,
        //Direct以外増やすとしたら、LookAtキャラが全員映る範囲とか？
    }    


    /// <summary>
    /// LiveTimelineKeyCameraFovData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyCameraFovData : LiveTimelineKeyWithInterpolate
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.CameraFov; } }

        public LiveCameraFovType fovType = LiveCameraFovType.Direct;
        public float fov = 30f;


#if UNITY_EDITOR
        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);
            var d = dest as LiveTimelineKeyCameraFovData;

            d.fovType = fovType;
            d.fov = fov;
        }


        /// <summary>
        /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
        /// </summary>
        public override LiveTimelineKey InterpolateForEdit(
            LiveTimelineKey interpKey,
            LiveTimelineKey prevKey,
            float t
            )
        {
            var outData = interpKey as LiveTimelineKeyCameraFovData;
            var prevData = prevKey as LiveTimelineKeyCameraFovData;
            outData.fov = Mathf.Lerp(prevData.fov, fov, t);
            return outData;
        }


        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl, editSheet);

            fovType = (LiveCameraFovType)EditorGUILayout.EnumPopup("Type", fovType);

            switch (fovType)
            {
                case LiveCameraFovType.Direct:
                    if (ctx.ME_FloatField("FOV", ref fov))
                    {
                        ctx.MultiKeyEditApply<LiveTimelineKeyCameraFovData>(x => x.fov = fov);
                    }
                    break;
            }
        }
#endif//UNITY_EDITOR
    }
}