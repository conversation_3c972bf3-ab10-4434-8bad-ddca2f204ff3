using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR

namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveTimelineKeyPropsAttachData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyPropsAttachData : LiveTimelineKeyWithInterpolate
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.PropsAttach; } }

        public string _attachJointName = "";
        public int _attachJointHash = 0;
        public string _copyPositionJointName = "";
        public int _copyPositionJointHash = 0;
        public int _settingFlags = 0;
        public int _propsId = -1;
        public Vector3 _offsetPosition = Math.VECTOR3_ZERO;
        public Vector3 OffsetRotate = Math.VECTOR3_ZERO;
        public Vector3 OffsetScale = Math.VECTOR3_ONE;
        /// <summary> 133535 Propがアタッチしているボーンと連動する（現在は手だけ対応している） </summary>
        public bool IsLinkAttachBone = false;

        // 保存しないパラメータ
        public Quaternion OffsetRotation { get; private set; } = Math.QUATERNION_IDENTITY;

        public override void OnLoad(LiveTimelineControl timelineControl)
        {
            base.OnLoad(timelineControl);

            UpdateParam();
        }

        private void UpdateParam()
        {
            // 負荷軽減のため事前にQuaternionを取得しておく。
            OffsetRotation = Quaternion.Euler(OffsetRotate);
        }

#if UNITY_EDITOR
        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);
            var d = dest as LiveTimelineKeyPropsAttachData;

            d._attachJointName = _attachJointName;
            d._attachJointHash = _attachJointHash;
            d._copyPositionJointName = _copyPositionJointName;
            d._copyPositionJointHash = _copyPositionJointHash;
            d._settingFlags = _settingFlags;
            d._propsId = _propsId;
            d._offsetPosition = _offsetPosition;
            d.OffsetRotate = OffsetRotate;
            d.OffsetScale = OffsetScale;
            d.IsLinkAttachBone = IsLinkAttachBone;

            UpdateParam();
        }

        /// <summary>
        /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
        /// </summary>
        public override LiveTimelineKey InterpolateForEdit(
            LiveTimelineKey interpKey,
            LiveTimelineKey prevKey,
            float t
            )
        {
            var outData = interpKey as LiveTimelineKeyPropsAttachData;
            var prevData = prevKey as LiveTimelineKeyPropsAttachData;

            outData._offsetPosition = Vector3.Lerp(prevData._offsetPosition, _offsetPosition, t);
            outData.OffsetRotate = Vector3.Lerp(prevData.OffsetRotate, OffsetRotate, t);
            outData.OffsetScale = Vector3.Lerp(prevData.OffsetScale, OffsetScale, t);

            outData.UpdateParam();

            return outData;
        }

        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl, editSheet);

            using (new EditorGUILayout.VerticalScope("box"))
            {
                EditTimelinePropsAttach editTimelinePropsAttach = editSheet._editingLine as EditTimelinePropsAttach;

                if (editTimelinePropsAttach == null)
                {
                    return;
                }

                var chara = (LiveCharaPositionFlag)_settingFlags;

                if (ctx.ME_CharaPosToggle("Character", ref chara))
                {
                    _settingFlags = (int)chara;
                    ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x => x._settingFlags = _settingFlags);
                }

                if (ctx.ME_TextField("Attach Joint Name", ref _attachJointName))
                {
                    _attachJointHash = FNVHash.Generate(_attachJointName);
                    ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x =>
                    {
                        x._attachJointName = _attachJointName;
                        x._attachJointHash = _attachJointHash;
                    });
                }
                EditorGUI.indentLevel++;
                ctx.LockedIntField("Attach Joint Hash", _attachJointHash);
                EditorGUI.indentLevel--;

                if (ctx.ME_TextField("CopyPos Joint Name", ref _copyPositionJointName))
                {
                    _copyPositionJointHash = FNVHash.Generate(_copyPositionJointName);
                    ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x =>
                    {
                        x._copyPositionJointName = _copyPositionJointName;
                        x._copyPositionJointHash = _copyPositionJointHash;
                    });
                }
                EditorGUI.indentLevel++;
                ctx.LockedIntField("CopyPos Joint Hash", _copyPositionJointHash);
                EditorGUI.indentLevel--;

                using (new EditorGUILayout.VerticalScope("box"))
                {
                    if (ctx.ME_IntField("Props ID", ref _propsId))
                    {
                        ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x => x._propsId = _propsId);
                    }
                    EditorGUI.indentLevel++;
                    EditorGUILayout.LabelField("※各キャラごとの通し番号（-1で全小物対象）");
                    EditorGUI.indentLevel--;
                    // 対象の小物アセットの名前を表示する。
                    if (Director.HasInstance)
                    {
                        EditorGUILayout.LabelField("対象小物一覧");

                        EditorGUI.indentLevel++;

                        var propsManager = Director.Instance.PropsManager;

                        propsManager.TargetPropsAction(editTimelinePropsAttach._lineData.nameHash, _settingFlags, _propsId, (props, charaIndex, dressIndex, propsIndex) =>
                        {
                            ctx.LockedTextField(LiveTimelineDefine.kLiveCharaPositionFlagDisplayOpts[charaIndex], props.name);
                        });

                        EditorGUI.indentLevel--;
                    }
                }

                {
                    var backup = _offsetPosition;
                    if (ctx.ME_Vector3Field("Offset Position", ref _offsetPosition))
                    {
                        SetChangeValueOnly_Vector3(ref _offsetPosition, backup, _offsetPosition);
                        ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x => SetChangeValueOnly_Vector3(ref x._offsetPosition, backup, _offsetPosition));
                    }
                }
                {
                    var backup = OffsetRotate;
                    if (ctx.ME_Vector3Field("Offset Rotate", ref OffsetRotate))
                    {
                        SetChangeValueOnly_Vector3(ref OffsetRotate, backup, OffsetRotate);
                        OffsetRotation = Quaternion.Euler(OffsetRotate);
                        ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x =>
                        {
                            SetChangeValueOnly_Vector3(ref x.OffsetRotate, backup, OffsetRotate);
                            x.OffsetRotation = Quaternion.Euler(x.OffsetRotate);
                        });
                    }
                }
                {
                    var backup = OffsetScale;
                    if (ctx.ME_Vector3Field("Offset Scale", ref OffsetScale))
                    {
                        SetChangeValueOnly_Vector3(ref OffsetScale, backup, OffsetScale);
                        ctx.MultiKeyEditApply<LiveTimelineKeyPropsAttachData>(x => SetChangeValueOnly_Vector3(ref x.OffsetScale, backup, OffsetScale));
                    }
                }

                ctx.ME_Toggle("ボーンと連動する", ref IsLinkAttachBone);
            }
        }
#endif//UNITY_EDITOR
    }
}
