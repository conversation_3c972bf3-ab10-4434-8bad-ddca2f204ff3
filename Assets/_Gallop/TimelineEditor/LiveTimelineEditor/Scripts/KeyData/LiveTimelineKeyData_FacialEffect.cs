using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

#if UNITY_EDITOR
using UnityEditor;
#endif//UNITY_EDITOR

namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveTimelineKeyFacialEffectData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyFacialEffectData : LiveTimelineKey
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.FacialEffect; } }

        public int cheekType = 0;
        public int tearyType = 0;
        public int tearfulType = 0;
        [Serializable] public class TeardropConfig { public int teardropIndex; public int teardropSlot; public bool isReversed; public float Alpha = 1f; public float Speed = 1f; public Color Color = GameDefine.COLOR_WHITE; }
        public TeardropConfig[] teardropConfigs = new TeardropConfig[1] { new TeardropConfig() };
        public int mangameIndex = 0;

        public const int kAttrCheek = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 0));
        public const int kAttrTeary = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 1));
        public const int kAttrTearful = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 2));
        public const int kAttrTeardrop = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 3));
        public const int kAttrMangame = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 5));
        public const int kAttrFaceShadow = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 6));
        public const int kAttrFaceShadowVisible = 1 << ((int)(LiveTimelineKeyAttributeBit.KeySubClassBitHead + 7));

        public bool IsEnabledCheek
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrCheek);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrCheek, value);
        }
        public bool IsEnabledTeary
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrTeary);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrTeary, value);
        }
        public bool IsEnabledTearful
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrTearful);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrTearful, value);
        }
        public bool IsEnabledTeardrop
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrTeardrop);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrTeardrop, value);
        }
        public bool IsEnabledMangame
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrMangame);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrMangame, value);
        }
        public bool IsEnabledFaceShadow
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrFaceShadow);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrFaceShadow, value);
        }
        public bool IsVisibleFaceShadow
        {
            get => this.attribute.hasFlag((LiveTimelineKeyAttribute)kAttrFaceShadowVisible);
            set => this.attribute = this.attribute.addFlag((LiveTimelineKeyAttribute)kAttrFaceShadowVisible, value);
        }

        public override void OnLoad(LiveTimelineControl timelineControl)
        {
            base.OnLoad(timelineControl);

#if UNITY_EDITOR
            _isDisplayCheek = IsEnabledCheek;
            _isDisplayTeary = IsEnabledTeary;
            _isDisplayTearful = IsEnabledTearful;
            _isDisplayTeardrop = IsEnabledTeardrop;
            _isDisplayMangame = IsEnabledMangame;
            _isDisplayFaceShadow = IsEnabledFaceShadow;
#endif
        }

#if UNITY_EDITOR
        private bool _isDisplayCheek = false;
        private bool _isDisplayTeary = false;
        private bool _isDisplayTearful = false;
        private bool _isDisplayTeardrop = false;
        private bool _isDisplayMangame = false;
        private bool _isDisplayFaceShadow = false;

        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);
            var d = dest as LiveTimelineKeyFacialEffectData;
            d.cheekType = cheekType;
            d.tearyType = tearyType;
            d.tearfulType = tearfulType;
            d.mangameIndex = mangameIndex;
            d.teardropConfigs = teardropConfigs
                .Select(c => new TeardropConfig()
                {
                    teardropIndex = c != null ? c.teardropIndex : 0,
                    teardropSlot = c != null ? c.teardropSlot : 0,
                    isReversed = c != null ? c.isReversed : false,
                    Alpha = c != null ? c.Alpha : 1f,
                    Speed = c != null ? c.Speed : 1f,
                    Color = c != null ? c.Color : GameDefine.COLOR_WHITE,
                })
                .ToArray();
        }

        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl);

            _isDisplayCheek = GUIUtil.Foldout("チーク", _isDisplayCheek);
            if (_isDisplayCheek)
            {
                {
                    bool on = IsEnabledCheek;
                    if (ctx.ME_ToggleLeft("有効", ref on))
                    {
                        IsEnabledCheek = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsEnabledCheek = on);
                    }
                    editSheet.OnGUI_SetEnableGUI(this, on);
                }
                {
                    var options = LiveTimelineDefine.FacialEffectCheekNameArray;
                    var value = cheekType;
                    if (ctx.ME_Popup("段階", ref value, options))
                    {
                        cheekType = value;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.cheekType = cheekType);
                    }
                }
                editSheet.OnGUI_SetEnableGUI(this, true);
            }

            _isDisplayTeary = GUIUtil.Foldout("瞳うるうる", _isDisplayTeary);
            if (_isDisplayTeary)
            {
                {
                    bool on = IsEnabledTeary;
                    if (ctx.ME_ToggleLeft("有効", ref on))
                    {
                        IsEnabledTeary = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsEnabledTeary = on);
                    }
                    editSheet.OnGUI_SetEnableGUI(this, on);
                }
                {
                    var options = LiveTimelineDefine.FacialEffectTearyNameArray;
                    var value = tearyType;
                    if (ctx.ME_Popup("タイプ", ref value, options))
                    {
                        tearyType = value;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.tearyType = tearyType);
                    }
                }
                editSheet.OnGUI_SetEnableGUI(this, true);
            }

            _isDisplayTearful = GUIUtil.Foldout("目尻の涙", _isDisplayTearful);
            if (_isDisplayTearful)
            {
                {
                    bool on = IsEnabledTearful;
                    if (ctx.ME_ToggleLeft("有効", ref on))
                    {
                        IsEnabledTearful = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsEnabledTearful = on);
                    }
                    editSheet.OnGUI_SetEnableGUI(this, on);
                }
                {
                    var options = LiveTimelineDefine.FacialEffectTearfulNameArray;
                    var value = tearfulType;
                    if (ctx.ME_Popup("段階", ref value, options))
                    {
                        tearfulType = value;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.tearfulType = tearfulType);
                    }
                }
                editSheet.OnGUI_SetEnableGUI(this, true);
            }

            _isDisplayTeardrop = GUIUtil.Foldout("涙を流す", _isDisplayTeardrop);
            if (_isDisplayTeardrop)
            {
                {
                    bool on = IsEnabledTeardrop;
                    if (ctx.ME_ToggleLeft("有効", ref on))
                    {
                        IsEnabledTeardrop = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsEnabledTeardrop = on);
                    }
                    editSheet.OnGUI_SetEnableGUI(this, on);
                }
                for (var i = 0; i < teardropConfigs.Length; ++i)
                {
                    var index = i;
                    var teardropConfig = teardropConfigs[index];
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField($"設定 {i}", GUILayout.MaxWidth(60));
                    GUILayout.FlexibleSpace();
                    if (GUILayout.Button("削除"))
                    {
                        teardropConfigs = teardropConfigs.Where((c, idx) => idx != i).ToArray();
                    }
                    EditorGUILayout.EndHorizontal();
                    EditorGUI.indentLevel += 1;
                    {
                        var options = LiveTimelineDefine.FacialEffectTeardropIndexNameArray;
                        var value = teardropConfig.teardropIndex;
                        if (ctx.ME_Popup("涙を流す", ref value, options))
                        {
                            teardropConfig.teardropIndex = value;
                            ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x =>
                            {
                                if (x.teardropConfigs.Length > index)
                                {
                                    x.teardropConfigs[index].teardropIndex = teardropConfig.teardropIndex;
                                }
                            });
                        }
                    }
                    {
                        var options = LiveTimelineDefine.FacialEffectTeardropSlotNameArray;
                        var value = teardropConfig.teardropSlot;
                        if (ctx.ME_Toolbar("涙スロット", ref value, options))
                        {
                            teardropConfig.teardropSlot = value;
                            ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x =>
                            {
                                if (x.teardropConfigs.Length > index)
                                {
                                    x.teardropConfigs[index].teardropSlot = value;
                                }
                            });
                        }
                    }
                    {
                        var options = LiveTimelineDefine.FacialEffectTeardropReverseNameArray;
                        var value = teardropConfig.isReversed ? 1 : 0;
                        if (ctx.ME_Toolbar("涙位置", ref value, options))
                        {
                            var isReversed = value == 1;
                            teardropConfig.isReversed = isReversed;
                            ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x =>
                            {
                                if (x.teardropConfigs.Length > index)
                                {
                                    x.teardropConfigs[index].isReversed = isReversed;
                                }
                            });
                        }
                    }
                    if (ctx.ME_FloatField("Alpha", ref teardropConfig.Alpha))
                    {
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x =>
                        {
                            if (x.teardropConfigs.Length > index)
                            {
                                x.teardropConfigs[index].Alpha = teardropConfig.Alpha;
                            }
                        });
                    }
                    if (ctx.ME_FloatField("Speed", ref teardropConfig.Speed))
                    {
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x =>
                        {
                            if (x.teardropConfigs.Length > index)
                            {
                                x.teardropConfigs[index].Speed = teardropConfig.Speed;
                            }
                        });
                    }
                    if (ctx.ME_ColorField("Color", ref teardropConfig.Color))
                    {
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x =>
                        {
                            if (x.teardropConfigs.Length > index)
                            {
                                x.teardropConfigs[index].Color = teardropConfig.Color;
                            }
                        });
                    }
                    EditorGUI.indentLevel -= 1;
                }
                if (GUILayout.Button("涙設定追加"))
                {
                    teardropConfigs = teardropConfigs.Append(new TeardropConfig()).ToArray();
                }
                editSheet.OnGUI_SetEnableGUI(this, true);
            }

            _isDisplayMangame = GUIUtil.Foldout("漫画目", _isDisplayMangame);
            if (_isDisplayMangame)
            {
                {
                    bool on = IsEnabledMangame;
                    if (ctx.ME_ToggleLeft("有効", ref on))
                    {
                        IsEnabledMangame = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsEnabledMangame = on);
                    }
                    editSheet.OnGUI_SetEnableGUI(this, on);
                }
                {
                    var options = LiveTimelineDefine.FacialEffectMangameIndexNameArray;
                    var value = mangameIndex;
                    if (ctx.ME_Popup("漫画目", ref value, options))
                    {
                        mangameIndex = value;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.mangameIndex = mangameIndex);
                    }
                }
                editSheet.OnGUI_SetEnableGUI(this, true);
            }

            _isDisplayFaceShadow = GUIUtil.Foldout("顔影", _isDisplayFaceShadow);
            if (_isDisplayFaceShadow)
            {
                {
                    bool on = IsEnabledFaceShadow;
                    if (ctx.ME_ToggleLeft("有効", ref on))
                    {
                        IsEnabledFaceShadow = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsEnabledFaceShadow = on);
                    }
                    editSheet.OnGUI_SetEnableGUI(this, on);
                }
                {
                    bool on = IsVisibleFaceShadow;
                    if (ctx.ME_Toggle("顔影を表示", ref on))
                    {
                        IsVisibleFaceShadow = on;
                        ctx.MultiKeyEditApply<LiveTimelineKeyFacialEffectData>(x => x.IsVisibleFaceShadow = on);
                    }
                }
                editSheet.OnGUI_SetEnableGUI(this, true);
            }
        }
#endif//UNITY_EDITOR
    }
}