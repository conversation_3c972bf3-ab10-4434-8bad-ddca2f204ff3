using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR

namespace Gallop.Live.Cutt
{
    [System.Serializable]
    public class LiveTimelineKeyEyeCameraPositionData : LiveTimelineKeyCameraPositionData
    {
        public override LiveTimelineKeyDataType dataType => LiveTimelineKeyDataType.EyeCameraPos;

        public bool IsEnabled = false;
        public float Power = 0.5f;
        public float Roll = 0.0f;
        public float Fov = 30.0f;
        public LiveCameraCullingLayer CullingMask;
        public Texture2D MaskTexture;

        /// <summary>
        /// カメラのカリングマスクを取得する
        /// </summary>
        /// <returns></returns>
        public override int GetCullingMask()
        {
            return LiveCameraCullingLayer_Helper.GetCullingMask(CullingMask);
        }

        public override bool GetLayerOffset(LiveTimelineControl timelineControl, out Vector3 offset)
        {
            offset = Math.VECTOR3_ZERO;
            return false;
        }

#if UNITY_EDITOR
        protected override void OnCreate()
        {
            base.OnCreate();

            CullingMask = LiveCameraCullingLayer_Helper.AllCullingLayer();
        }

        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);

            var d = dest as LiveTimelineKeyEyeCameraPositionData;
            d.IsEnabled = IsEnabled;
            d.Power = Power;
            d.Roll = Roll;
            d.Fov = Fov;
            d.CullingMask = CullingMask;
            d.MaskTexture = MaskTexture;
        }

        /// <summary>
        /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
        /// </summary>
        public override LiveTimelineKey InterpolateForEdit(
            LiveTimelineKey interpKey,
            LiveTimelineKey prevKey,
            float t
            )
        {
            base.InterpolateForEdit(interpKey, prevKey, t);

            var outData = interpKey as LiveTimelineKeyEyeCameraPositionData;
            var prevData = prevKey as LiveTimelineKeyEyeCameraPositionData;

            outData.Power = Mathf.Lerp(prevData.Power, Power, t);
            outData.Roll = Mathf.Lerp(prevData.Roll, Roll, t);
            outData.Fov = Mathf.Lerp(prevData.Fov, Fov, t);

            return outData;
        }

        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            if (ctx.ME_Toggle("Eye Camera ON", ref IsEnabled))
            {
                ctx.MultiKeyEditApply<LiveTimelineKeyEyeCameraPositionData>(x => x.IsEnabled = IsEnabled);
            }
            if (!IsEnabled)
            {
                return;
            }

            base.OnGUI(ctx, timelineControl, editSheet);

            if (ctx.ME_FloatField("角度（右回り）", ref Roll))
            {
                ctx.MultiKeyEditApply<LiveTimelineKeyEyeCameraPositionData>(x => x.Roll = Roll);
            }

            if (ctx.ME_FloatField("FOV", ref Fov))
            {
                ctx.MultiKeyEditApply<LiveTimelineKeyEyeCameraPositionData>(x => x.Fov = Fov);
            }

            if (ctx.ME_Slider("Power", ref Power, 0f, 1f))
            {
                ctx.MultiKeyEditApply<LiveTimelineKeyEyeCameraPositionData>(x => x.Power = Power);
            }

            Enum layer = CullingMask;
            if (ctx.ME_EnumMask("Culling Mask", ref layer))
            {
                CullingMask = LiveCameraCullingLayer_Helper.GetEnumMaskToCullingLayer(layer);
                ctx.MultiKeyEditApply<LiveTimelineKeyEyeCameraPositionData>(x => x.CullingMask = CullingMask);
            }

            UnityEngine.Object maskTexture = MaskTexture;
            if (ctx.ME_ObjectField<Texture2D>("Mask", ref maskTexture))
            {
                if (maskTexture is Texture2D)
                {
                    MaskTexture = maskTexture as Texture2D;
                }
                else
                {
                    MaskTexture = null;
                }
                ctx.MultiKeyEditApply<LiveTimelineKeyEyeCameraPositionData>(x => x.MaskTexture = MaskTexture);
            }
        }
#endif
    }
}
