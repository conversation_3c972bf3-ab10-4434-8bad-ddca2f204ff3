using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR





/*=============================================================================
 * LiveTimelineKeyMobControlData
 */
namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveTimelineKeyMobControlData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyMobControlData : LiveTimelineKeyWithInterpolate, ITransformCPBundleData
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.MobControl; } }

        public Vector3 Position = Math.VECTOR3_ZERO;
        public Vector3 Angle = Math.VECTOR3_ZERO;
        public Vector3 Scale = Math.VECTOR3_ONE;
#if UNITY_EDITOR
        public Vector3 CPPosition { get { return Position; } set { Position = value; } }
        public Vector3 CPRotate { get { return Angle; } set { Angle = value; Rotation = Quaternion.Euler(Angle); } }
        public Vector3 CPScale { get { return Scale; } set { Scale = value; } }
#endif

        [System.NonSerialized]
        public Quaternion Rotation = Math.QUATERNION_IDENTITY;

        public override void OnLoad(LiveTimelineControl timelineControl)
        {
            base.OnLoad(timelineControl);
            Rotation = Quaternion.Euler(Angle);
        }

#if UNITY_EDITOR
        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);
            var d = dest as LiveTimelineKeyMobControlData;
            d.Position = Position;
            d.Angle = Angle;
            d.Rotation = Rotation;
            d.Scale = Scale;
        }


        /// <summary>
        /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
        /// </summary>
        public override LiveTimelineKey InterpolateForEdit(
            LiveTimelineKey interpKey,
            LiveTimelineKey prevKey,
            float t
            )
        {
            var outData = interpKey as LiveTimelineKeyMobControlData;
            var prevData = prevKey as LiveTimelineKeyMobControlData;

            outData.Position = Vector3.Lerp(prevData.Position, Position, t);
            outData.Angle = Vector3.Lerp(prevData.Angle, Angle, t);
            outData.Scale = Vector3.Lerp(prevData.Scale, Scale, t);
            outData.Rotation = Quaternion.Euler(outData.Angle);

            return outData;
        }


        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl, editSheet);

            {
                var backup = Position;
                if (ctx.ME_Vector3Field("Position", ref Position))
                {
                    ctx.MultiKeyEditApply<LiveTimelineKeyMobControlData>(x => SetChangeValueOnly_Vector3(ref x.Position,backup, Position));
                }
            }

            {
                var backup = Angle;
                if (ctx.ME_Vector3Field("Angle", ref Angle))
                {
                    Rotation = Quaternion.Euler(Angle);
                    ctx.MultiKeyEditApply<LiveTimelineKeyMobControlData>(x =>
                    {
                        SetChangeValueOnly_Vector3(ref x.Angle, backup, Angle);
                        x.Rotation = Quaternion.Euler(x.Angle);
                    });
                }
            }

            {
                var backup = Scale;
                if (ctx.ME_Vector3Field("Scale", ref Scale, null, Vector3.one))
                {
                    ctx.MultiKeyEditApply<LiveTimelineKeyMobControlData>(x => SetChangeValueOnly_Vector3(ref x.Scale, backup, Scale));
                }
            }
        }
#endif//UNITY_EDITOR
    }
}