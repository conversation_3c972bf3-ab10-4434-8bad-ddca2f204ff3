using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif

namespace Gallop.Live.Cutt
{
    [System.Serializable]
    public class LiveTimelineKeyVoiceData : LiveTimelineKey
    {
        public override LiveTimelineKeyDataType dataType => LiveTimelineKeyDataType.Voice;

        #region SerializeField

        // 再生するCueID
        public int CueId = 0;

        #region Attribute

        #endregion Attribute

        #endregion SerializeField

#if UNITY_EDITOR
        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);

            if (dest is LiveTimelineKeyVoiceData d)
            {
                d.CueId = CueId;
            }
        }

        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl, editSheet);

            using (new GUILayout.VerticalScope("box"))
            {
                if (ctx.ME_IntField("CueID", ref CueId))
                {
                    ctx.MultiKeyEditApply<LiveTimelineKeyVoiceData>(x => x.CueId = CueId);
                }
            }
        }
#endif
    }
}
