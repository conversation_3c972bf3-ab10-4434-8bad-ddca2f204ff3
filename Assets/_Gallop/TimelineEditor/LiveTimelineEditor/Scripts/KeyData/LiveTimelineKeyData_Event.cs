using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR





/*=============================================================================
 * LiveTimelineKeyEventData
 */
namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveTimelineKeyEventData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyEventData : LiveTimelineKey
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.Event; } }

        /// <summary>
        /// EventData
        /// </summary>
        [System.Serializable]
        public class EventData
        {
            public LiveTimelineEventID eventId = LiveTimelineEventID.NOT_AVAILABLE;

            CuttEventParamBase eventParam = null;
            public string serializedParamter = "";


            // Paramterオブジェクト取得
            public TypeValue GetParameter<TypeValue>() where TypeValue : CuttEventParamBase
            {
                return eventParam as TypeValue;
            }

            public void OnLoad()
            {
                if (!string.IsNullOrEmpty(serializedParamter))
                {
                    LiveTimelineEventPublisher.IParamGenerator paramGen;
                    if (LiveTimelineEventPublisher.paramGeneratorDict.TryGetValue(eventId, out paramGen))
                    {
                        eventParam = null;
                        if (paramGen != null)
                        {
                            eventParam = paramGen.ToObject(serializedParamter);
                        }
                    }
                }

                return;
            }

#if UNITY_EDITOR
            public void Copy(EventData dest)
            {
                var d = dest as EventData;
                d.eventId = eventId;

                if (eventParam != null)
                {
                    LiveTimelineEventPublisher.IParamGenerator paramGen;

                    if (LiveTimelineEventPublisher.paramGeneratorDict.TryGetValue(eventId, out paramGen))
                    {
                        var s = LitJsonUtil.JsonMapper.ToJson(eventParam);
                        d.eventParam = paramGen.ToObject(s);
                    }
                }

                return;
            }


            public void OnSave()
            {
                if (eventParam == null)
                {
                    serializedParamter = null;
                }
                else
                {
                    serializedParamter = LitJsonUtil.JsonMapper.ToJson(eventParam);
                }

                return;
            }


            public void OnGUI(Cutt.GUIContext ctx)
            {
                var eventIdOld = eventId;
                eventId = (LiveTimelineEventID)EditorGUILayout.EnumPopup("EventKind", eventId);

                if (eventId != eventIdOld)
                {
                    LiveTimelineEventPublisher.IParamGenerator paramgen;

                    if (LiveTimelineEventPublisher.paramGeneratorDict.TryGetValue(eventId, out paramgen))
                    {
                        eventParam = null;
                        if (paramgen != null)
                        {
                            eventParam = paramgen.Create();
                        }
                    }
                }

                if (eventParam != null)
                {
                    eventParam.OnGUI();
                }

                return;
            }
#endif//UNITY_EDITOR
        }//class EventData

        public List<EventData> eventList = new List<EventData>();

        public override void OnLoad(LiveTimelineControl timelineControl)
        {
            base.OnLoad(timelineControl);

            //Desirialize
            foreach (var eventData in eventList)
            {
                eventData.OnLoad();
            }

            return;
        }

#if UNITY_EDITOR
        public override void OnSave(LiveTimelineControl timelineControl)
        {
            base.OnSave(timelineControl);

            foreach (var eventData in eventList)
            {
                eventData.OnSave();
            }

            return;
        }
#endif//UNITY_EDITOR


#if UNITY_EDITOR
        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);
            var d = dest as LiveTimelineKeyEventData;

            d.eventList.Clear();

            foreach (var e in eventList)
            {
                if (e == null)
                    continue;

                var de = new EventData();
                e.Copy(de);
                d.eventList.Add(de);
            }

            return;
        }


        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl);

            {
                var disable = attribute.hasFlag(LiveTimelineKeyAttribute.Disable);
                disable = EditorGUILayout.Toggle("Key無効", disable);
                attribute = attribute.addFlag(LiveTimelineKeyAttribute.Disable, disable);
            }

            int eventIndex = 0;
            var deletEvnetList = new List<EventData>();

            foreach (var e in eventList)
            {
                GUILayout.Label(string.Format("------ Event{0} ------", eventIndex + 1));

                if (GUILayout.Button("Remove Event"))
                {
                    deletEvnetList.Add(e);
                }

                e.OnGUI(ctx);
                eventIndex++;
            }

            foreach (var e in deletEvnetList)
            {
                eventList.Remove(e);
            }

            if (GUILayout.Button("Add Event"))
            {
                eventList.Add(new EventData());
            }

            GUILayout.Space(5);
            if (GUILayout.Button("Fire Event"))
            {
                timelineControl.eventPublisher.FireEvent(this);
            }

            return;
        }
#endif//UNITY_EDITOR
    }
}