
using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR





/*=============================================================================
 * LiveTimelineKeyMultiCameraTiltShiftData
 */
namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveTimelineKeyMultiCameraTiltShiftData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyMultiCameraTiltShiftData : LiveTimelineKeyTiltShiftData
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.MultiCameraTiltShift; } }
    }
}
