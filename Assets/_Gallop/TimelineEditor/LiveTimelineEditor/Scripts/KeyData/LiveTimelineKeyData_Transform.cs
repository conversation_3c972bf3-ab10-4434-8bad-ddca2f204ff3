using UnityEngine;
using System.Collections;
using System.Collections.Generic;

#if UNITY_EDITOR
using System;
using UnityEditor;
#endif//UNITY_EDITOR





/*=============================================================================
 * LiveTimelineKeyTransformData
 */
namespace Gallop.Live.Cutt
{
    /// <summary>
    /// LiveTimelineKeyTransformData
    /// </summary>
    [System.Serializable]
    public class LiveTimelineKeyTransformData : LiveTimelineKeyWithInterpolate, ITransformCPBundleData
    {
        public override LiveTimelineKeyDataType dataType { get { return LiveTimelineKeyDataType.Transform; } }

        public Vector3 position = Math.VECTOR3_ZERO;
        public Vector3 rotate = Math.VECTOR3_ZERO;
        public Vector3 scale = Math.VECTOR3_ONE;
#if UNITY_EDITOR
        public Vector3 CPPosition { get { return position; } set { position = value; } }
        public Vector3 CPRotate { get { return rotate; } set { rotate = value; } }
        public Vector3 CPScale { get { return scale; } set { scale = value; } }
#endif



#if UNITY_EDITOR
        public override void Copy(LiveTimelineKey dest)
        {
            base.Copy(dest);
            var d = dest as LiveTimelineKeyTransformData;
            d.position = position;
            d.rotate = rotate;
            d.scale = scale;
        }


        /// <summary>
        /// キー補間（Cuttで補間キーの間にキーを打つ時につかう）
        /// </summary>
        public override LiveTimelineKey InterpolateForEdit(
            LiveTimelineKey interpKey,
            LiveTimelineKey prevKey,
            float t
            )
        {
            var outData = interpKey as LiveTimelineKeyTransformData;
            var prevData = prevKey as LiveTimelineKeyTransformData;

            outData.position = Vector3.Lerp(prevData.position, position, t);
            outData.rotate = Vector3.Lerp(prevData.rotate, rotate, t);
            outData.scale = Vector3.Lerp(prevData.scale, scale, t);

            return outData;
        }


        public override void OnGUI(Cutt.GUIContext ctx, LiveTimelineControl timelineControl, EditSheet editSheet)
        {
            base.OnGUI(ctx, timelineControl, editSheet);

            EditTimelineTransform editTimelineTransform = editSheet._editingLine as EditTimelineTransform;

            if (editTimelineTransform == null)
            {
                return;
            }

            editSheet.OnGUI_SetEnableGUI(this, editTimelineTransform._lineData.enablePosition);
            {
                var backup = position;
                if (ctx.ME_Vector3Field("Position", ref position))
                {
                    ctx.MultiKeyEditApply<LiveTimelineKeyTransformData>(x => SetChangeValueOnly_Vector3(ref x.position,backup,position));
                }
            }

            editSheet.OnGUI_SetEnableGUI(this, editTimelineTransform._lineData.enableRotate);
            {
                var backup = rotate;
                if (ctx.ME_Vector3Field("Rotate", ref rotate))
                {
                    ctx.MultiKeyEditApply<LiveTimelineKeyTransformData>(x => SetChangeValueOnly_Vector3(ref x.rotate,backup,rotate));
                }
            }

            editSheet.OnGUI_SetEnableGUI(this, editTimelineTransform._lineData.enableScale);
            {
                var backup = scale;
                if (ctx.ME_Vector3Field("Scale", ref scale, null, Vector3.one))
                {
                    ctx.MultiKeyEditApply<LiveTimelineKeyTransformData>(x => SetChangeValueOnly_Vector3(ref x.scale,backup,scale));
                }
            }
            editSheet.OnGUI_SetEnableGUI(this, true);
        }
#endif//UNITY_EDITOR
    }
}