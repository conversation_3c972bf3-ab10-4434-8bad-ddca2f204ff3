#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Gallop.Live.Cutt
{
    public class EditTimelineTimescale : EditTimeline
    {
        public LiveTimelineKeyTimescaleDataList LineData { get; set; } = null;

        protected EditTimelineTimescale() : base()
        {
            return;
        }

        public override void OnGUI(GUIContext ctx, EditSheet sheet)
        {
            base.OnGUI(ctx, sheet);

            LineData.IsCheckSameFrame = EditorGUILayout.Toggle("同一フレームの計算回避", LineData.IsCheckSameFrame);
        }
    }
}
#endif // UNITY_EDITOR
