#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Gallop.Live.Cutt
{
    public class EditTimelineBgColor1 : EditTimeline
    {
        public LiveTimelineBgColor1Data TimelineData;

        protected EditTimelineBgColor1() : base()
        {
            return;
        }

        public override void OnGUI(GUIContext ctx, EditSheet sheet)
        {
            base.OnGUI(ctx, sheet);

            OnGUI_HashTextField(TimelineData, sheet);

            EditorGUILayout.Space();

            using (new EditorGUILayout.VerticalScope("box"))
            {
                using (new EditorGUILayout.HorizontalScope())
                {
                    EditorGUILayout.LabelField("対象キャラ");

                    int value = EditorGUILayout.IntField("設定数", TimelineData.TargetCharaIdArray.Length);
                    if (value != TimelineData.TargetCharaIdArray.Length)
                    {
                        TimelineData.ResizeTargetCharaIdArray(value);
                        return;
                    }
                }
                EditorGUI.indentLevel++;
                for (int i = 0; i < TimelineData.TargetCharaIdArray.Length; i++)
                {
                    TimelineData.TargetCharaIdArray[i] = EditorGUILayout.IntField("キャラID", TimelineData.TargetCharaIdArray[i]);
                }
                EditorGUI.indentLevel--;
            }

            using (new EditorGUILayout.VerticalScope("box"))
            {
                using (new EditorGUILayout.HorizontalScope())
                {
                    EditorGUILayout.LabelField("対象衣装");

                    int value = EditorGUILayout.IntField("設定数", TimelineData.TargetDressIdArray.Length);
                    if (value != TimelineData.TargetDressIdArray.Length)
                    {
                        TimelineData.ResizeTargetDressIdArray(value);
                        return;
                    }
                }
                EditorGUI.indentLevel++;
                for (int i = 0; i < TimelineData.TargetDressIdArray.Length; i++)
                {
                    TimelineData.TargetDressIdArray[i] = EditorGUILayout.IntField("衣装ID", TimelineData.TargetDressIdArray[i]);
                }
                EditorGUI.indentLevel--;
            }
        }
    }
}
#endif
