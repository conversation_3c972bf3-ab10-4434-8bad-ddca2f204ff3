#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Gallop.Live.Cutt
{
    public class EditTimelineCharaCollision : EditTimeline
    {
        public LiveTimelineCharaCollisionData LineData { get; set; } = null;

        protected EditTimelineCharaCollision() : base()
        {
            return;
        }

        public override void OnGUI(GUIContext ctx, EditSheet sheet)
        {
            base.OnGUI(ctx, sheet);
            OnGUI_HashTextField(LineData, sheet);
        }
    }
}
#endif // UNITY_EDITOR
