//Modified from https://github.com/keijiro/Reaktion/blob/master/Assets/Reaktion/Utility/JitterMotion.cs
using UnityEngine;
using UnityEngine.Rendering;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop.Live
{
#if UNITY_EDITOR
    [CustomEditor(typeof(LiveTimelineCamera))]
    public class LiveTimelineCamera_Editor : Editor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            var obj = (LiveTimelineCamera)target;
            obj.ApplyNoise = EditorGUILayout.Toggle("Enable", obj.ApplyNoise);
        }
    }
#endif

    public class LiveTimelineCamera : MonoBehaviour
    {
        //---------JitterMotion
        private bool _applyNoise = true;
        public bool ApplyNoise
        {
            get { return _applyNoise; }
            set
            {
                if ((_applyNoise != value) && (AttachedCamera != null))
                {
                    AttachedCamera.ResetWorldToCameraMatrix();
                }
                _applyNoise = value;
            }
        }

        // 回転及び座標の変更頻度を指定します。大きいほうが頻繁に移動します。
        private const float PositionFrequency = 0.2f;
        private const float RotationFrequency = 0.2f;

        public static float kPositionAmountInitVal { get { return 0.0f; } }
        public static float kRotationAmountInitVal { get { return 3.0f; } }
        public static Vector3 kPositionComponentsInitVal { get { return Vector3.one; } }
        public static Vector3 kRotationComponentsInitVal { get { return new Vector3(1, 1, 0); } }

        // 座標及び回転する勢いを指定します。大きい方が遠くまで移動します。
        public float positionAmount = kPositionAmountInitVal;
        public float rotationAmount = kRotationAmountInitVal;

        // 移動範囲を指定します。小さいほうがブレないです。
        public Vector3 positionComponents = kPositionComponentsInitVal;
        public Vector3 rotationComponents = kRotationComponentsInitVal;

        private int positionOctave = 2;
        private int rotationOctave = 2;

        private float _timePosition;
        private float _timeRotation;
        private float _initTimePosition;
        private float _initTimeRotation;

        private Vector2[] noiseVectors;

        private Camera _attachedCamera = null;
        private Camera AttachedCamera
        {
            get
            {
                if (_attachedCamera == null)
                {
                    if (!TryGetComponent(out _attachedCamera))
                        return null;
                }
                return _attachedCamera;
            }
        }

        /// <summary>
        /// DisableなGameObjectにアタッチしてもAwakeがCallされないっぽいので代替
        /// </summary>
        public void AlterAwake()
        {
            ApplyNoise = false;
            // 手ブレ具合を調整したい場合は、下記ランダム範囲を変更してみてください
            _initTimePosition = Random.value * 10;
            _initTimeRotation = Random.value * 10;

            noiseVectors = new Vector2[6];
            for (var i = 0; i < noiseVectors.Length; i++)
            {
                var theta = Random.value * Mathf.PI * 2;
                noiseVectors[i].Set(Mathf.Cos(theta), Mathf.Sin(theta));
            }

            //URP:置き換え対応
            if(AttachedCamera != null)
            {
                var callback = gameObject.AddComponent<CameraEventCallback>();
                callback.BeginCameraRenderingCallback = OnBeforeRendering;
            }
        }

        public void AlterUpdate(float liveTime)
        {
            if (!AttachedCamera.isActiveAndEnabled)
            {
                return;
            }
            if (!ApplyNoise)
            {
                return;
            }
            _timePosition = _initTimePosition + liveTime * PositionFrequency;
            _timeRotation = _initTimeRotation + liveTime * RotationFrequency;

            if (!Math.IsFloatEqual(positionAmount, 0.0f))
            {
                var p = new Vector3(
                    Fbm(noiseVectors[0] * _timePosition, positionOctave),
                    Fbm(noiseVectors[1] * _timePosition, positionOctave),
                    Fbm(noiseVectors[2] * _timePosition, positionOctave)
                );
                p = Vector3.Scale(p, positionComponents) * positionAmount * 2;
                posOffset = p;
            }

            if (!Math.IsFloatEqual(rotationAmount, 0.0f))
            {
                var r = new Vector3(
                    Fbm(noiseVectors[3] * _timeRotation, rotationOctave),
                    Fbm(noiseVectors[4] * _timeRotation, rotationOctave),
                    Fbm(noiseVectors[5] * _timeRotation, rotationOctave)
                );
                r = Vector3.Scale(r, rotationComponents) * rotationAmount * 2;
                rotOffset = Quaternion.Euler(r);
            }
        }

        static float Fbm(Vector2 coord, int octave)
        {
            var f = 0.0f;
            var w = 1.0f;
            for (var i = 0; i < octave; i++)
            {
                f += w * (Mathf.PerlinNoise(coord.x, coord.y) - 0.5f);
                coord *= 2;
                w *= 0.5f;
            }
            return f;
        }

        private Vector3 posOffset = Vector3.zero;
        private Quaternion rotOffset = Quaternion.identity;
        private static readonly Vector3 ScaleOffset = new Vector3(1, 1, -1); // GLとUnityのZフォワードの違いを考慮
        //URP:置き換え対応
        //private void OnPreCull()
        private void OnBeforeRendering(ScriptableRenderContext _,Camera camera)
        {
            if ((AttachedCamera == null) || !AttachedCamera.isActiveAndEnabled)
            {
                return;
            }
            if (ApplyNoise)
            {
                Matrix4x4 m = Matrix4x4.TRS(posOffset, rotOffset, ScaleOffset);
                AttachedCamera.worldToCameraMatrix = m * AttachedCamera.transform.worldToLocalMatrix;
            }
        }
    }
}
