namespace Gallop
{
    /// <summary>
    /// モーションSEのカメラ範囲調整
    /// </summary>
    [System.Serializable]
    public class StoryTimelineMotionSeCameraRangeTrackData : StoryTimelineTrackData
    {
        #region abstract

        /// <summary>Trackの種別</summary>
        public override TrackType Type => TrackType.MotionSeRange;

        /// <summary>キー種別</summary>
        public override ClipKeyType KeyType => ClipKeyType.Frame;

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// Clipを追加する際に前のクリップをコピーしてくるかどうか
        /// </summary>
        public override bool IsCopyLastClip => true;

        /// <summary>
        /// タイムラインのツール上に表示される名前
        /// </summary>
        public override string TrackDispName => "SE範囲";

        /// <summary>どのツールで表示するか</summary>
        protected override int DispToolFlag => (int)DispToolFlagType.StoryAndTutorial;

#endif

        #endregion abstract

        public override void StartTrackData(int frameCount)
        {
            base.StartTrackData(frameCount);

            if (TryGetClip(frameCount, out StoryTimelineMotionSeCameraRangeClipData prevClip))
            {
                prevClip.UpdateClipData(frameCount: frameCount,
                    currentTime: 0f, prevClip: null, isWaiting: false, nextClipData: null);
            }
        }

        #region Editor

#if CYG_DEBUG && UNITY_EDITOR

        public override void DrawProperty(ref float posY)
        {
            posY = StoryTimelineEditorPropertyWindow.AddButton(posY, "ModelController選択",
                ((StoryTimelineCharaTrackData) ParentTrack).SelectModelController);
        }

#endif

        #endregion Editor
    }
}