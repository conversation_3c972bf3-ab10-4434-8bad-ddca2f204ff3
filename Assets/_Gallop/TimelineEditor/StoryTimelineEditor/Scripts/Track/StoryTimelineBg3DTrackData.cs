using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 背景：画像：親
    /// </summary>
    [System.Serializable]
    public class StoryTimelineBg3DTrackData : StoryTimelineTrackData
    {
        #region abstract

        /// <summary>Trackの種別</summary>
        public override TrackType Type => TrackType.Bg3D;

        /// <summary>キー種別</summary>
        public override ClipKeyType KeyType => ClipKeyType.Frame;

        /// <summary>Update順</summary>
        public override int UpdatePriority => (int)TrackUpdatePriority.Bg;

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// Clipを追加する際に前のクリップをコピーしてくるかどうか
        /// </summary>
        public override bool IsCopyLastClip => true;

        /// <summary>
        /// タイムラインのツール上に表示される名前
        /// </summary>
        public override string TrackDispName => "背景3D";

        /// <summary>
        /// タイムラインのツール上に表示されるグループ名前
        /// </summary>
        public override string TrackGroupDispName => "背景3D";
        
        /// <summary>Lerp設定をユーザーにさせるかどうか</summary>
        public override bool SupportLerp => true;

        public override bool IsSupportFullLerpType => true;

        /// <summary>
        /// 作業用一時ファイルを作る時のコピー処理
        /// </summary>
        public override void CopyFrom(StoryTimelineEditorWindow editorWindow, StoryTimelineTrackData src,
            HashSet<int> trackIndexSet = null)
        {
            if ((trackIndexSet == null || trackIndexSet.Contains(TrackIndex)) &&
                src is StoryTimelineBg3DTrackData trackData)
            {
                SelectBgType = trackData.SelectBgType;
                MainId = trackData.MainId;
                Season = trackData.Season;
                Weather = trackData.Weather;
                BgTime = trackData.BgTime;
            }

            base.CopyFrom(editorWindow, src, trackIndexSet);
        }

        /// <summary>
        /// どのツールで表示するか: Paddockシーンで表示
        /// </summary>
        protected override int DispToolFlag => (int)DispToolFlagType.Paddock;

        /// <summary>
        /// ツール別に表示するかどうか
        /// </summary>
        public override bool IsDisplayTool(StoryTimelineData timelineData)
        {
            // 3D背景が有効 or Paddockシーンなら表示する
            return timelineData.Use3DBg || base.IsDisplayTool(timelineData);
        }
#endif
        #endregion abstract

        #region 定数
        /// <summary>
        /// 背景の種別 (データ作り直しになるので値や並び順の変更は禁止)
        /// </summary>
        public enum BgType
        {
            Paddock = 0,
            Training = 1,
        }

        #endregion 定数

        // 保存されるデータを追加したらCopyFrom関数も実装してください
        #region 保存されるデータ

        public BgType SelectBgType = BgType.Paddock;

        public int MainId = -1;

        public GameDefine.BgSeason Season = GameDefine.BgSeason.Spring;

        public RaceDefine.Weather Weather = RaceDefine.Weather.Sunny;

        public RaceDefine.Time BgTime = RaceDefine.Time.Morning;

        #endregion 保存されるデータ

        #region 作業中のデータ
        /// <summary>
        /// 背景環境設定データ
        /// </summary>
        private TrainingEnvParam _envParam = null;
        public TrainingEnvParam EnvParam { get { return _envParam; } set { _envParam = value; } }

        #endregion 作業中のデータ

        public override void Initialize()
        {
            // StoryTimelineData.Use3DBg=trueの場合はHeadトラックのときに影用のDirectionarlLightをリセットしておく
            if (IsUse3DBgHeadTrack())
            {
                DirectionalLightManager.Instance.ResetSubLight(); //親のライトで設定を初期化するために必要
                DirectionalLightManager.Instance.SetEnableSubLight(false);
            }
            base.Initialize();
        }

        public override void OnFinish()
        {
            // StoryTimelineData.Use3DBg=trueの場合はHeadトラックのときに影用のDirectionarlLightを作成しているので破棄
            if (IsUse3DBgHeadTrack())
            {
                TimelineController.Background3DController.ResetBackGround3d();
                if (DirectionalLightManager.Instance.SubDirectionalLight != null)
                {
                    DirectionalLightManager.Instance.SetEnableSubLight(false);
                }
            }
            base.OnFinish();
        }

        /// <summary>
        /// StoryTimelineData.Use3DBg=trueで自身がHeadTrackの場合はtrueを返す
        /// </summary>
        private bool IsUse3DBgHeadTrack()
        {
            if (TimelineController == null) return false;
            if (TimelineController.TimelineData == null) return false;

            if (TimelineController.TimelineData.Use3DBg == false) return false;
            if (HeadTrack != this) return false;

            return true;
        }

        /// <summary>
        /// Clipが配置されていないときに呼び出されるUpdate
        /// </summary>
        protected override void UpdateNonClip(StoryTimelineClipData prevClip, int frameCount)
        {
            if (prevClip == null)
            {
#if UNITY_EDITOR
                // TimelineEditor実行時に戻る際に前クリップがない場合は背景を消す
                TimelineController.Background3DController?.ShowBackgroundByClip(null);
#endif
                return;
            }

            // Skipされた時にも背景が切り替わるようにエディタ以外でも実行が必要
            prevClip.StartClipData(0, null, false);
        }

        /// <summary>
        /// 環境設定ファイルを返す
        /// </summary>
        /// <remarks>
        /// すでにEnvParamが存在する場合は再生成をしない（再生成が必要かどうかのチェックもしない）。
        /// なぜなら複数Cuttを切り替える場合、別のCuttで同じ背景を使用している場合は内部だけではわからず、外部で判定してもらう必要があるため
        /// </remarks>
        public bool MakeEnvParam()
        {
            // note: _envParamがScriptObjectなので、==演算子でのnullチェックだとLoad後であってもtrueになってしまう
            //       なので、ここではis演算子で厳密なnullチェックを行う必要がある
            if (_envParam is null == false)
            {
#if CYG_DEBUG && UNITY_EDITOR
                // タイムラインツールで作業中の時は毎回背景作り直しにしたい
                if (SceneManager.Instance.GetCurrentSceneId() != SceneDefine.SceneId.StoryTimelineEditor)
                {
                    // StoryTimelineEditorシーンじゃないので再生成しない
                    return true;
                }
#else
                // 実機の場合は再生成しない
                return true;
#endif
            }

            string path = MakeEnvParamResourcePath();

            #region EnvParamが存在しない場合のエラーログ
            if (ResourceManager.IsExistAsset(path) == false)
            {
                //本番環境で海外在住の場合にロードが失敗しているような挙動をしていることがある。調査のために本番環境でもログを出す 2022/02/22 -inada
                var msg = string.Format("3D背景の環境ファイルがない: {0}", System.IO.Path.GetFileName(path));
                if (RaceManager.RaceInfo != null && RaceManager.RaceInfo.RaceInstanceMaster != null)
                {
                    var raceDate = RaceManager.RaceInfo.RaceInstanceMaster.GetDate();
                    msg += " RaceDate:" + raceDate;

                    var list = MasterDataManager.Instance.masterSeasonData.GetListWithType((int)TimeUtil.SeasonType.Race);
                    if (list != null)
                    {
                        foreach (var masterData in list)
                        {
                            if (masterData == null)
                            {
                                continue;
                            }
                            //季節が秋と冬の時をチェック
                            if (masterData.Season == 3 || masterData.Season == 4)
                            {
                                DateTime start = masterData.StartJstDateTime;
                                DateTime end = masterData.EndJstDateTime;

                                msg += "  季節:" + masterData.Season + " startDate:" + masterData.StartJstDateTime + " endDate:" + masterData.EndJstDateTime;
                            }
                        }
                    }

                }

#if ENABLE_FIREBASE
                Exception e = new Exception(msg);
                Firebase.Crashlytics.Crashlytics.LogException(e);
#else
                Debug.LogError(msg);
#endif
                return false;
            }
            #endregion EnvParamが存在しない場合のエラーログ

            // #156787: パドックに遷移するとEnvParamが破棄される問題に対応
            // RegisterDownloadの時にPath解決のためにEnvParamもロードされるがChangeSceneのプロセス中にそれが破棄される
            // Hashが指定された場合はLoadOnHashを使って途中で破棄されるのを防ぐ
            var hash = BlockData.TimelineData.LoadedHash;
            _envParam = hash != ResourceManager.ResourceHash.InvalidHash
                ? ResourceManager.LoadOnHash<TrainingEnvParam>(path, hash)
                : ResourceManager.LoadOnView<TrainingEnvParam>(path);

            // EnvParamのロードに成功したら反映処理も行う (このような副作用がある実装は避けたいところだが2019年からこの形なのでそのままにしておく)
            if (TimelineController != null)
            {
                TimelineController.ChangeEnvFrom3DBg(this);
            }

            return _envParam != null;
        }

        /// <summary>
        /// 読み込む環境ファイルのパスを返す
        /// </summary>
        /// <returns></returns>
        public string MakeEnvParamResourcePath()
        {
            string path = string.Empty;

            int envId = GallopUtil.ToEnvId(Season, Weather, BgTime);

            // Track情報からパスを作る
            switch (SelectBgType)
            {
                case StoryTimelineBg3DTrackData.BgType.Paddock:
                    path = ResourcePath.GetSetEnvParamPath(MainId, envId);
                    break;
            }

            return path;
        }

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// Trackのプロパティを表示
        /// </summary>
        public override void DrawProperty(ref float posY)
        {
            if (BlockData.TimelineData.SceneType == StoryTimelineData.SupportedSceneType.Paddock)
            {
                DrawPropertyForPaddock(ref posY);
            }
        }

        /// <summary>
        /// Paddock用のプロパティを表示
        /// </summary>
        private void DrawPropertyForPaddock(ref float posY)
        {
            int bgType = (int)SelectBgType;
            posY = StoryTimelineEditorPropertyWindow.AddEnumInputField(posY, "背景の種類", ref bgType, typeof( BgType ), string.Empty, () =>
            {
                SelectBgType = ( BgType )bgType;

                TimelineController.LoadBg3D(this, true);
            } );

            posY = StoryTimelineEditorPropertyWindow.AddIntInputField(posY, "背景メインID", ref MainId, () => { TimelineController.LoadBg3D(this, true); });

            posY = StoryTimelineEditorPropertyWindow.AddEnumInputField<GameDefine.BgSeason>(posY, "季節", ref Season, string.Empty, () => { TimelineController.LoadBg3D(this, true); });

            posY = StoryTimelineEditorPropertyWindow.AddEnumInputField<RaceDefine.Weather>(posY, "天候", ref Weather, string.Empty, () => { TimelineController.LoadBg3D(this, true); });

            posY = StoryTimelineEditorPropertyWindow.AddEnumInputField<RaceDefine.Time>(posY, "時間帯", ref BgTime, string.Empty, () => { TimelineController.LoadBg3D(this, true); });
        }

#endif

        /// <summary>
        /// 必要なアセットの登録
        /// </summary>
        public override void SaveTrackResources(StoryTimelineData timelineData, StoryTimelineResourceList resourceList,
            TrackType[] filterTrackTypeArray = null)
        {
            base.SaveTrackResources(timelineData, resourceList);

            if (MainId >= 0 )
            {
                if( _envParam == null )
                {
                    if (!MakeEnvParam())
                    {
                        // ここにくる状態では正しくリソース一覧が更新できない
                        // デバッグビルドではMakeEnvParamの時点でエラーを出すようにしている
                        return;
                    }
                }

                resourceList.SaveResourcesPath(MakeEnvParamResourcePath(), Type);
                resourceList.SaveResourcesPath(_envParam.GetResourcesFileName(), Type);
            }
        }
    }

}
