#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// Timeline上のキャラ表情(口)用Clip１つ分のデータ
    /// </summary>
    public class StoryTimelineCharaFaceMouthClipData : StoryTimelineCharaFaceClipData
    {
        /// <summary>
        /// 口が開いている状態をデフォルトとするか
        /// </summary>
        public bool IsOpenDefault = false;

        /// <summary>
        /// 口は自前でやる
        /// </summary>
        /// <param name="frameCount"></param>
        /// <param name="currentTime"></param>
        /// <param name="prevClip"></param>
        /// <param name="isWaiting"></param>
        /// <param name="nextClipData"></param>
        public override void UpdateClipData(int frameCount, float currentTime, StoryTimelineClipData prevClip, bool isWaiting, StoryTimelineClipData nextClipData = null)
        {
            var model = TrackData.ParentTrack.GetModelControllerFromCache();
            if (model == null)
            {
                return;
            }

            if (UseTransitionCurve && nextClipData != null)
            {
                //遷移カーブを利用する場合の処理
                var nextClipIsOpenDefault = (nextClipData as StoryTimelineCharaFaceMouthClipData).IsOpenDefault;

                // 2つのクリップデータ間のフレーム数からトータルの経過時間を計算
                var durationTime = (nextClipData.FixedStartFrame - FixedStartFrame) * GameDefine.BASE_FPS_TIME;

                //アニメーションカーブの経過割合
                var t = Mathf.Abs(Curve.Evaluate((float)(frameCount - FixedStartFrame) * GameDefine.BASE_FPS_TIME / durationTime));

                FaceParts[] nextClipDrivenKeyTypeArray1 = null;

                FaceParts[] currentClipDrivenKeyTypeArray1 = null;

                //前後のクリップデータに口に対して同じ表情を設定且つ同じOpenDefaultが設定されているかどうかをチェック
                bool ContainFacePartsAndSameOpenDefaultState(FaceParts[] checkArray, int targetFaceParts)
                {
                    if(checkArray != null)
                    {
                        for(int i = 0; i < checkArray.Length; i++)
                        {
                            if(checkArray[i]._faceParts == targetFaceParts)
                            {
                                return true && (nextClipIsOpenDefault == IsOpenDefault);
                            }
                        }
                    }

                    return false;
                }

                if (nextClipIsOpenDefault)
                {
                    currentClipDrivenKeyTypeArray1 = MakeFaceParts(FaceInfoList1, typeof(FaceMouthType));

                    nextClipDrivenKeyTypeArray1 = MakeFaceParts((nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList1, typeof(FaceMouthType));

                    for (int i = 0; i < nextClipDrivenKeyTypeArray1.Length; i++)
                    {
                        var value = nextClipDrivenKeyTypeArray1[i];
                        var targetWeight = value._originalWeight;
                        value._weight = ContainFacePartsAndSameOpenDefaultState(currentClipDrivenKeyTypeArray1, value._faceParts) ? targetWeight : Mathf.Lerp(0f, 1f, t) * targetWeight;
                        nextClipDrivenKeyTypeArray1[i] = value;
                    }

                    for (int i = 0; i < currentClipDrivenKeyTypeArray1.Length; i++)
                    {
                        var value = currentClipDrivenKeyTypeArray1[i];
                        var targetWeight = value._originalWeight;
                        value._weight = ContainFacePartsAndSameOpenDefaultState(nextClipDrivenKeyTypeArray1, value._faceParts) ? 0f : Mathf.Lerp(1f, 0f, t) * targetWeight;
                        currentClipDrivenKeyTypeArray1[i] = value;
                    }
                }
                else
                {
                    currentClipDrivenKeyTypeArray1 = MakeFaceParts(FaceInfoList1, typeof(FaceMouthType));

                    nextClipDrivenKeyTypeArray1 = MakeFaceParts((nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList1, typeof(FaceMouthType));

                    for (int i = 0; i < nextClipDrivenKeyTypeArray1.Length; i++)
                    {
                        var value = nextClipDrivenKeyTypeArray1[i];
                        var targetWeight = value._originalWeight;
                        value._weight = ContainFacePartsAndSameOpenDefaultState(currentClipDrivenKeyTypeArray1, value._faceParts) ? 0f : Mathf.Lerp(1f, 0f, t) * targetWeight;
                        nextClipDrivenKeyTypeArray1[i] = value;
                    }

                    for (int i = 0; i < currentClipDrivenKeyTypeArray1.Length; i++)
                    {
                        var value = currentClipDrivenKeyTypeArray1[i];
                        var targetWeight = value._originalWeight;
                        value._weight = ContainFacePartsAndSameOpenDefaultState(nextClipDrivenKeyTypeArray1, value._faceParts) ? targetWeight : Mathf.Lerp(0f, 1f, t) * targetWeight;
                        currentClipDrivenKeyTypeArray1[i] = value;
                    }
                }

                model.SetFaceMouthTypeWithoutInverseMouth(currentClipDrivenKeyTypeArray1, nextClipIsOpenDefault, FaceGroupType.Mouth, FacialDurationTime, false, false);
                model.SetFaceMouthTypeWithoutInverseMouth(nextClipDrivenKeyTypeArray1, nextClipIsOpenDefault, FaceGroupType.Mouth, FacialDurationTime, false, false);
            }
            else
            {
                SetFace(model, FacialDurationTime);
            }
        }

        public override void UpdateFace(float durationTime)
        {
            var modelController = TrackData.ParentTrack.GetModelControllerFromCache();
            if (modelController == null)
            {
                return;
            }
           
            SetFace(modelController, durationTime);
        }
        
        private void SetFace(EventTimelineModelController modelController, float durationTime)
        {
            //遷移カーブ適用せず、今まで通りの通常動き
            FaceParts[] drivenKeyTypeArray1 = null;
            FaceParts[] drivenKeyTypeArray2 = null;

            if (IsOpenDefault)
            {
                // ModelController.LipSyncOpenMouthにdrivenKeyTypeArray1がセットされる
                // → FaceInfoList1が開き口
                drivenKeyTypeArray1 = MakeFaceParts(FaceInfoList1, typeof(FaceMouthType));
                drivenKeyTypeArray2 = MakeFaceParts(FaceInfoList2, typeof(FaceMouthType));
            }
            else
            {
                // ModelController.LipSyncOpenMouthにdrivenKeyTypeArray2がセットされる
                // → FaceInfoList1が開き口
                drivenKeyTypeArray1 = MakeFaceParts(FaceInfoList2, typeof(FaceMouthType));
                drivenKeyTypeArray2 = MakeFaceParts(FaceInfoList1, typeof(FaceMouthType));
            }

            if (drivenKeyTypeArray1 != null && drivenKeyTypeArray2 != null)
            {
                modelController.SetFaceMouthType(drivenKeyTypeArray1, IsOpenDefault, drivenKeyTypeArray2, FaceGroupType.Mouth, durationTime, false, false);
            }
        }        

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// プロパティ描画
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="posY"></param>
        public override void DrawProperty(SerializedObject obj, ref float posY)
        {
            base.DrawProperty(obj, ref posY);

            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "開いた状態をデフォルト", ref IsOpenDefault);
        }

        /// <summary>
        /// クリップデータを作成する
        /// </summary>
        public override StoryTimelineClipData CreateClipData(StoryTimelineTrackData trackData)
        {
            var path = _editorWindow.AssetPath;
            return CreateClipData<StoryTimelineCharaFaceMouthClipData>(path, trackData);
        }

#endif
    }
}
