using UnityEngine;
using Gallop.Model.Component;

#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// キャラの加算モーションClipデータクラス
    /// </summary>
    public class StoryTimelineCharaAddMotionClipData : StoryTimelineMotionClipDataBase
    {
        #region 保存されるデータ

        /// <summary>
        /// 加算モーションのウェイト
        /// </summary>
        public float BlendWeight = 1.0f;

        /// <summary>
        /// IK
        /// </summary>
        public StoryTimelineIKData LeftWrist = new StoryTimelineIKData(false, true);
        public StoryTimelineIKData RightWrist = new StoryTimelineIKData(false, true);
        public StoryTimelineIKData LeftAnkle = new StoryTimelineIKData(false, false);
        public StoryTimelineIKData RightAnkle = new StoryTimelineIKData(false, false);

        #endregion 保存されるデータ

        #region 作業中のデータ

        #endregion 作業中のデータ

        #region メソッド
        /// <summary>
        /// タイムラインデータ読み込み時に全Clipに対して走る処理
        /// </summary>
        public override void Initialize()
        {
            // 新規作成と選択中Clipの置き換えでStateTypeが違ってしまうバグが起きていた
            // 既存のデータが狂っている可能性があるので正しい値に補正する
            StateType = MotionStateType.None;
        }

        /// <summary>
        /// Update処理
        /// </summary>
        /// <param name="frameCount"></param>
        /// <param name="currentTime"></param>
        /// <param name="prevClip"></param>
        /// <param name="isWaiting"></param>
        /// <param name="nextClipData"></param>
        public override void UpdateClipData(int frameCount, float currentTime, StoryTimelineClipData prevClip, bool isWaiting, StoryTimelineClipData nextClipData = null)
        {
            base.UpdateClipData(frameCount, currentTime, prevClip, isWaiting, nextClipData);

            // IK周りのチェック
            if( SettingIK(out var modelController, out var ikCtrl, out var parentTransform) == false )
            {
                return;
            }

            float time = (currentTime - FixedStartFrame) / (ClipLength * GameDefine.BASE_FPS_TIME);
            time = Mathf.Clamp01(time);

            float blendRate = Curve.Evaluate(time);

            LeftWrist.UpdateIK(ikCtrl, parentTransform,GallopLimbIK.LimbIndex.LeftWrist, blendRate);
            RightWrist.UpdateIK(ikCtrl, parentTransform,GallopLimbIK.LimbIndex.RightWrist, blendRate);
            LeftAnkle.UpdateIK(ikCtrl, parentTransform,GallopLimbIK.LimbIndex.LeftAnkle, blendRate);
            RightAnkle.UpdateIK(ikCtrl, parentTransform,GallopLimbIK.LimbIndex.RightAnkle, blendRate);
        }
        #endregion メソッド

        #region エディタ用
#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// キャラモーション用のプロパティを表示
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="posY"></param>
        public override void DrawProperty(SerializedObject obj, ref float posY)
        {
            GUIContent content;

            // 再生時間変更やモーション再生速度変更を受ける前のクリップの長さ（ロードされたモーション本来の長さ: 変更不可）
            posY = StoryTimelineEditorPropertyWindow.AddLabelField(posY, "OriginalClipLength", OriginalClipLength);

            // モーションの時間をもとに戻す
            posY = StoryTimelineEditorPropertyWindow.AddButton(posY, "デフォルトのフレーム数に戻す", () =>
            {
                SetClipLength();
                OnMotionTimeChanged();
            });

            // モーション名の表示
            posY = StoryTimelineEditorPropertyWindow.AddLabelField(posY, "モーション名", MotionName);

            // モーションのブレンド時間の設定
            content = new GUIContent
            {
                text = "BlendTime",
                tooltip = "ブレンド時間 [float]"
            };
            posY = StoryTimelineEditorPropertyWindow.AddFloatInputField(posY, obj, content, OnBlendTimeChanged);

            // モーションのブレンドカーブ
            posY = StoryTimelineEditorPropertyWindow.AddCurveField(posY, "Blend Curve", Curve);

            // モーションの再生速度指定
            content = new GUIContent
            {
                text = "MotionSpeed",
                tooltip = "モーション速度 [float]"
            };
            posY = StoryTimelineEditorPropertyWindow.AddFloatInputField(posY, obj, content, OnMotionTimeChanged);

            // モーションの一時停止開始フレーム
            posY = StoryTimelineEditorPropertyWindow.AddIntInputField(posY, "一時停止開始フレーム", ref PauseStartFrame, OnMotionTimeChanged);

            // モーションの一時停止時間フレーム
            posY = StoryTimelineEditorPropertyWindow.AddIntInputField(posY, "一時停止フレーム数", ref PauseLength, OnMotionTimeChanged);

            // モーションのウェイト値
            posY = StoryTimelineEditorPropertyWindow.AddFloatInputField(posY, "ウェイト", ref BlendWeight);

            // 左手首
            posY = LeftWrist.DrawIKProperty(obj, "左手首", posY);

            // 右手首
            posY = RightWrist.DrawIKProperty(obj, "右手首", posY);

            // 左足首
            posY = LeftAnkle.DrawIKProperty(obj, "左足首", posY);

            // 右足首
            posY = RightAnkle.DrawIKProperty(obj, "右足首", posY);
        }

        /// <summary>
        /// 範囲タイプのClipテキスト表示を行う場合の処理
        /// </summary>
        public override string GetDrawKeyFrameRangeText()
        {
            return MotionName;
        }

        /// <summary>
        /// クリップデータを作成する
        /// </summary>
        public override StoryTimelineClipData CreateClipData(StoryTimelineTrackData trackData)
        {
            var path = _editorWindow.AssetPath;
            return CreateClipData<StoryTimelineCharaAddMotionClipData>(path, trackData);
        }

        public override void SetMotionName(string name)
        {
            base.SetMotionName(name);
            //IKの有り無しで、有効、無効を自動設定する
            if (name.Contains("_IK"))
            {
                LeftWrist.IsEnable = true;
                RightWrist.IsEnable = true;
            }
            else
            {
                LeftWrist.IsEnable = false;
                RightWrist.IsEnable = false;
            }
        }

        public override void OnMotionChanged(bool needsSetClipLength)
        {
            base.OnMotionChanged(needsSetClipLength);

            // モーションClipDataの情報を更新する（StateTypeやBlendTimeを設定）
            StoryTimelineCharaAddMotionClipData prevClipData = TrackData.GetStepClipData(this, -1) as StoryTimelineCharaAddMotionClipData;

            if (prevClipData == null)
            {
                // トラックで最初のクリップの場合は何もしない
                return;
            }

            // 前のクリップに対応する加算戻しモーションの場合はウェイト値をそろえる
            if (NeedSameWeightWithPrevClip(prevClipData))
            {
                BlendWeight = prevClipData.BlendWeight;
            }
        }

        /// <summary>
        /// 前のクリップとウェイトを揃える必要があるか
        /// </summary>
        /// <remarks>leftB, leftBEのように前のクリップに対応するエンドモーションの場合はウェイトをそろえる必要がある</remarks>
        /// <param name="prevClipData"></param>
        /// <returns></returns>
        private bool NeedSameWeightWithPrevClip(StoryTimelineCharaAddMotionClipData prevClipData)
        {
            if (IsEndMotion() == false)
            {
                // エンドモーションでない場合は不要
                return false;
            }

            // 前のクリップのモーションがこのクリップに対応しているか名前で判定する
            var motionNameWithoutSuffix = GetMotionNameWithoutSuffix();
            if (IsIK())
            {
                if (prevClipData.MotionName.Equals($"{motionNameWithoutSuffix}_IK"))
                {
                    return true;
                }
            }
            else
            {
                if (prevClipData.MotionName.Equals(motionNameWithoutSuffix))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// IKを適用するモーションか
        /// </summary>
        /// <returns></returns>
        private bool IsIK()
        {
            return MotionName.Contains("_IK");
        }

        /// <summary>
        /// 設定されているのがエンドモーションか
        /// </summary>
        /// <returns></returns>
        private bool IsEndMotion()
        {
            // モーション名のうち末尾の"_IK"があれば除いた部分
            var normalMotionName = IsIK() ? MotionName.Split('_')[0] : MotionName;

            return normalMotionName.EndsWith("E");
        }

        /// <summary>
        /// モーション名からIKやエンドモーションの接尾辞を除いた部分を取得する
        /// 例: leftAE_IK -> leftA, leftB -> leftB
        /// </summary>
        /// <returns></returns>
        private string GetMotionNameWithoutSuffix()
        {
            // モーション名のうち末尾の"_IK"があれば除いた部分
            var normalMotionName = IsIK() ? MotionName.Split('_')[0] : MotionName;

            if (IsEndMotion())
            {
                // 末尾の"E"を除いた部分
                return normalMotionName.Substring(0, normalMotionName.Length - 1);
            }
            else
            {
                return normalMotionName;
            }
        }
#endif
        #endregion エディタ用
    }
}
