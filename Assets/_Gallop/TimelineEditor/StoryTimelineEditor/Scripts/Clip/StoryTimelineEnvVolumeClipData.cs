namespace Gallop
{
    public class StoryTimelineEnvVolumeClipData : StoryTimelineAudioVolumeClipData
    {
        public override void StartClipData(int frameCount, StoryTimelineClipData prevClip, bool isWaiting, StoryTimelineClipData nextClip = null)
        {
            TimelineController.SetBgEnvSoundVolume(Volume, FadeDuration, FadeCurveId);
        }

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// クリップデータを作成する
        /// </summary>
        public override StoryTimelineClipData CreateClipData(StoryTimelineTrackData trackData)
        {
            var path = _editorWindow.AssetPath;
            return CreateClipData<StoryTimelineEnvVolumeClipData>(path, trackData);
        }

#endif
    }
}