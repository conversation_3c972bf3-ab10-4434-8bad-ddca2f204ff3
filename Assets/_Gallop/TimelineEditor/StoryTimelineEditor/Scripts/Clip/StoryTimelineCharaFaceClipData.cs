using System.Collections.Generic;
using System;
using UnityEngine;
#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// Timeline上のキャラ表情(全体)用Clip１つ分のデータ
    /// </summary>
    public abstract class StoryTimelineCharaFaceClipData : StoryTimelineClipData
    {
        /// <summary>
        /// 表情の遷移時間
        /// </summary>
        /// <remarks>
        /// #63968 ローディング明けに表情遷移が中途半端に見えてしまうので準備ブロックでは即時反映する
        /// 遷移カーブを利用する場合、顔の切り替えは二つのクリップデータの間のフレーム数を利用
        /// 育成イベント高速スキップの場合も遷移時間を0にしてすぐに反映させる
        /// </remarks>
        protected float FacialDurationTime => BlockData.IsSettingBlock || UseTransitionCurve || IsImmediate || TimelineController.IsSuperHighSpeedSkipMode() ? 0f : EventTimelineModelController.FACIAL_DURATION_TIME;

        /// <summary>モーションブレンドカーブ</summary>
        public AnimationCurve Curve = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);

        /// <summary>
        /// 表情遷移にカーブ適用かどうか
        /// </summary>
        public bool UseTransitionCurve = false;

        /// <summary>
        /// #94638 表情反映が即時かどうか
        /// </summary>
        public bool IsImmediate = false;

        /// <summary>
        /// 表情を構成しているパーツの情報
        /// </summary>
        [Serializable]
        public class FaceInfo
        {
            public string Name = string.Empty;
            public float Weight = 1.0f;

            #region Editor

            /// <summary> 表情入力のカテゴリ入力で選択していたFaceType </summary>
            public FaceType FaceType = FaceType.Base;

            /// <summary> 表情入力のカテゴリ入力で選択していたFaceTypeカテゴリ </summary>
            public string FaceTypeCategory = string.Empty;

            #endregion

            public FaceInfo()
            {
            }

            public FaceInfo( string name, float weight )
            {
                Name = name;
                Weight = weight;
            }

            public FaceInfo(FaceInfo other)
            {
                Name = other.Name;
                Weight = other.Weight;
                FaceType = other.FaceType;
                FaceTypeCategory = other.FaceTypeCategory;
            }

            public static FaceInfo Clone(FaceInfo faceInfo)
                => new FaceInfo(faceInfo);
        }

        /// <summary>
        /// 表情パーツリスト1
        /// 表情は複数の表情の組み合わせでできる
        /// </summary>
        public List<FaceInfo> FaceInfoList1 = new List<FaceInfo>();

        /// <summary>
        /// 表情パーツリスト2（複数構成用）
        /// 表情は複数の表情の組み合わせでできる
        /// </summary>
        public List<FaceInfo> FaceInfoList2 = new List<FaceInfo>();

        // 更新系メソッド
        #region Update

        public override void StartClipData(int frameCount, StoryTimelineClipData prevClip, bool isWaiting, StoryTimelineClipData nextClip = null)
        {
            base.StartClipData(frameCount, prevClip, isWaiting, nextClip);

            if (UseTransitionCurve)
            {
                //カーブ利用前に一回カレントデータの表情を反映する
                UpdateClipDataWithoutAnimationCurve();
            }

        }

        /// <summary>
        /// キャラの表情を設定する
        /// </summary>
        public override void UpdateClipData(int frameCount, float currentTime, StoryTimelineClipData prevClip, bool isWaiting, StoryTimelineClipData nextClipData = null)
        {
            if (UseTransitionCurve)
            {
                UpdateClipDataWithAnimationCurve(frameCount, nextClipData);
                return;
            }

            UpdateClipDataWithoutAnimationCurve();
        }

        #region 通常の表情変更
        /// <summary>
        /// 既存のUpdate処理
        /// </summary>
        private void UpdateClipDataWithoutAnimationCurve()
        {
            var modelController = TrackData.ParentTrack.GetModelControllerFromCache();
            if (modelController == null)
            {
                return;
            }

            switch (TrackData.HeadTrack.Type)
            {
                case StoryTimelineTrackData.TrackType.CharaFaceEyebrow:
                    {
                        var durationTime = GetDurationTime_Eyebrow();
                        var drivenKeyTypeArray1 = MakeFaceParts(FaceInfoList1, typeof(FaceEyebrowType));
                        if (drivenKeyTypeArray1 != null)
                        {
                            modelController.SetFaceEyebrowType(drivenKeyTypeArray1, FaceGroupType.EyebrowL, durationTime, false, false);
                        }
                        var drivenKeyTypeArray2 = MakeFaceParts(FaceInfoList2, typeof(FaceEyebrowType));
                        if (drivenKeyTypeArray2 != null)
                        {
                            modelController.SetFaceEyebrowType(drivenKeyTypeArray2, FaceGroupType.EyebrowR, durationTime, false, false);
                        }
                    }
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEye:
                    {
                        // ローディング明けに目が半開きになっているのが見えてしまうので準備ブロックでは即時反映する
                        // 連打中と倍速中は即時反映する
                        float durationTime = GetDurationTime_Eye();

                        var drivenKeyTypeArray1 = MakeFaceParts(FaceInfoList1, typeof(FaceEyeType));
                        if (drivenKeyTypeArray1 != null)
                        {
                            modelController.SetFaceEyeType(drivenKeyTypeArray1, FaceGroupType.EyeL, durationTime, false, false);
                            //強制目パチ発生判定
                            if (IsRequestBlink(modelController, drivenKeyTypeArray1, BlockData.IsSettingBlock))
                            {
                                modelController.RequestBlink(true);
                            }
                        }
                        var drivenKeyTypeArray2 = MakeFaceParts(FaceInfoList2, typeof(FaceEyeType));
                        if (drivenKeyTypeArray2 != null)
                        {
                            modelController.SetFaceEyeType(drivenKeyTypeArray2, FaceGroupType.EyeR, durationTime, false, false);
                            //強制目パチ発生判定
                            if (IsRequestBlink(modelController, drivenKeyTypeArray2, BlockData.IsSettingBlock))
                            {
                                modelController.RequestBlink(true);
                            }
                        }
                    }
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceMouth:
                    {
                        FaceParts[] drivenKeyTypeArray1 = MakeFaceParts(FaceInfoList1, typeof(FaceMouthType));
                        FaceParts[] drivenKeyTypeArray2 = MakeFaceParts(FaceInfoList2, typeof(FaceMouthType));
                        if (drivenKeyTypeArray1 != null)
                        {
                            var durationTime = GetDurationTime_Mouth();
                            modelController.SetFaceMouthType(drivenKeyTypeArray1, true, drivenKeyTypeArray2, FaceGroupType.Mouth, durationTime, false, false);
                        }
                    }
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEar:
                    if (FaceInfoList1.Count > 0)
                    {
                        //#94638 表情即時適用対応で遷移時間も渡す
                        var durationTime = GetDurationTime_Ear();
                        modelController.PlayFacialAnimation(FaceInfoList1[0].Name, EventTimelineModelController.FaceGroupName.Ear, durationTime);
                    }
                    break;
            }
        }
        
        /// <summary>
        /// 外部から設定されている表情に遷移させる
        /// </summary>
        /// <param name="durationTime"></param>
        public abstract void UpdateFace(float durationTime);

        /// <summary>
        /// 表情変更をする時の遷移時間を取得 (共通版)
        /// </summary>
        /// <remarks>
        /// 準備ブロックやフラグ指定ありの場合はプロパティ側で処理され, いずれも即時反映になる.
        /// <seealso cref="FacialDurationTime"/>
        /// </remarks>
        private float GetDurationTime_Common()
        {
            // 倍速なら即時反映する
            if (StoryTimelineController.IsHighSpeedMode())
            {
                return 0f;
            }

            // #142003対応: タッチ操作によるBlock移動時は即時反映する
            if (TimelineController.IsMovedBlockByTouch)
            {
                return 0f;
            }

            return FacialDurationTime;
        }

        /// <summary>
        /// 眉の表情変更をする時の遷移時間を取得
        /// </summary>
        /// <remarks>連打中の扱いが通常版と違う</remarks>
        private float GetDurationTime_Eyebrow()
        {
            // 105268対応: 倍速中は即時反映する
            return GetDurationTime_Common();
        }

        /// <summary>
        /// 目の表情変更をする時の遷移時間を取得
        /// </summary>
        /// <remarks>連打中の扱いが通常版と違う</remarks>
        private float GetDurationTime_Eye()
        {
            // 53967対応: 連打中は即時反映する
            if (TimelineController != null && TimelineController.IsContinuousTouch)
            {
                return 0f;
            }

            // 104506対応: 倍速・準備ブロック・フラグ指定ありの場合は即時反映する
            return GetDurationTime_Common();
        }

        /// <summary>
        /// 口の表情変更をする時の遷移時間を取得
        /// </summary>
        /// <returns></returns>
        private float GetDurationTime_Mouth()
        {
            // #142003対応: タッチ操作によるBlock移動時は即時反映する
            if (TimelineController.IsMovedBlockByTouch)
            {
                return 0f;
            }

            return FacialDurationTime;
        }

        /// <summary>
        /// 耳の表情変更をする時の遷移時間を取得
        /// </summary>
        /// <remarks>準備ブロックの扱いが通常版と違う</remarks>
        private float GetDurationTime_Ear()
        {
            // 94638, 105268対応: 倍速・準備ブロック・フラグ指定ありの場合は即時反映する
            var durationTime = GetDurationTime_Common();

            // 96222対応: パドックで必ずIsSettingBlockがTrueとなるのでDurationTimeが常に0となってしまう
            // その対策としてIsImmediateフラグがFalseの場合はデフォルト遷移時間(0.2秒)を適用
            if (BlockData.IsSettingBlock && !IsImmediate)
            {
                durationTime = EventTimelineModelController.FACIAL_DURATION_TIME;
            }

            return durationTime;
        }
        #endregion 通常の表情変更

        #region 表情変更にアニメーションカーブを使う場合
        /// <summary>
        /// 表情変更にアニメーションカーブを使う場合の処理
        /// </summary>
        /// <remarks>
        /// 口は口クリップクラス内でUpdateClipDataをオーバーライトしているので処理をそっちに追加している
        /// ここは目、眉毛、耳の処理を扱っている
        /// AnimationCurveを使ってフレーム経過につれ前後二つのClipDataのweightを流し込む
        /// </remarks>
        protected void UpdateClipDataWithAnimationCurve(int frameCount, StoryTimelineClipData nextClipData)
        {
            var modelController = TrackData.ParentTrack.GetModelControllerFromCache();
            if (modelController == null || nextClipData == null)
            {
                return;
            }

            // 2つのクリップデータ間のフレーム数からトータルの経過時間を計算
            var durationTime = (nextClipData.FixedStartFrame - FixedStartFrame) * GameDefine.BASE_FPS_TIME;

            //アニメーションカーブの経過割合
            var t = Mathf.Abs(Curve.Evaluate((float)(frameCount - FixedStartFrame) * GameDefine.BASE_FPS_TIME / durationTime));

            void SetUpAnimationBlendData<T>(List<FaceInfo> nextClipFaceInfoList, List<FaceInfo> currentClipFaceInfoList, T faceType, Action<FaceParts[]> action) where T : Type
            {
                var nextClipDrivenKeyTypeArray = MakeFaceParts(nextClipFaceInfoList, faceType);
                var currentClipDrivenKeyTypeArray = MakeFaceParts(currentClipFaceInfoList, faceType);

                if (nextClipDrivenKeyTypeArray != null)
                {
                    for (int i = 0; i < nextClipDrivenKeyTypeArray.Length; i++)
                    {
                        var value = nextClipDrivenKeyTypeArray[i];
                        var targetWeight = value._originalWeight;
                        var weight = Mathf.Lerp(0f, 1f, t) * targetWeight;
                        if (currentClipDrivenKeyTypeArray != null)
                        {
                            for(int y = 0; y < currentClipDrivenKeyTypeArray.Length; y++)
                            {
                                if(value._faceParts == currentClipDrivenKeyTypeArray[y]._faceParts)
                                {
                                    weight = 0f;
                                }
                            }
                        }
                        value._weight = weight;
                        nextClipDrivenKeyTypeArray[i] = value;
                    }

                    action(nextClipDrivenKeyTypeArray);
                }

                if (currentClipDrivenKeyTypeArray != null)
                {
                    for (int i = 0; i < currentClipDrivenKeyTypeArray.Length; i++)
                    {
                        var value = currentClipDrivenKeyTypeArray[i];
                        var targetWeight = value._originalWeight;
                        var weight = Mathf.Lerp(1f, 0f, t) * targetWeight;
                        if (nextClipDrivenKeyTypeArray != null)
                        {
                            for (int y = 0; y < nextClipDrivenKeyTypeArray.Length; y++)
                            {
                                if (value._faceParts == nextClipDrivenKeyTypeArray[y]._faceParts)
                                {
                                    weight = targetWeight;
                                }
                            }
                        }
                        value._weight = weight;
                        currentClipDrivenKeyTypeArray[i] = value;
                    }

                    action(currentClipDrivenKeyTypeArray);
                }
            }

            switch (TrackData.HeadTrack.Type)
            {
                case StoryTimelineTrackData.TrackType.CharaFaceEyebrow:
                    {
                        var nextClipFaceInfoList1 = (nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList1;
                        var prevClipFaceInfoList1 = FaceInfoList1;

                        SetUpAnimationBlendData(nextClipFaceInfoList1, prevClipFaceInfoList1, typeof(FaceEyebrowType),
                        (parts) =>
                        {
                            modelController.SetFaceEyebrowType(parts, FaceGroupType.EyebrowL, FacialDurationTime, true, false);
                        });

                        var nextClipFaceInfoList2 = (nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList2;
                        var prevClipFaceInfoList2 = FaceInfoList2;

                        SetUpAnimationBlendData(nextClipFaceInfoList2, prevClipFaceInfoList2, typeof(FaceEyebrowType),
                        (parts) =>
                        {
                            modelController.SetFaceEyebrowType(parts, FaceGroupType.EyebrowR, FacialDurationTime, true, false);
                        });
                    }
                    break;
                case StoryTimelineTrackData.TrackType.CharaFaceEye:
                    {
                        var nextClipFaceInfoList1 = (nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList1;
                        var prevClipFaceInfoList1 = FaceInfoList1;

                        SetUpAnimationBlendData(nextClipFaceInfoList1, prevClipFaceInfoList1, typeof(FaceEyeType),
                        (parts) => {
                            modelController.SetFaceEyeType(parts, FaceGroupType.EyeL, FacialDurationTime, true, false);
                        });

                        var nextClipFaceInfoList2 = (nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList2;
                        var prevClipFaceInfoList2 = FaceInfoList2;

                        SetUpAnimationBlendData(nextClipFaceInfoList2, prevClipFaceInfoList2, typeof(FaceEyeType),
                        (parts) =>
                        {
                            modelController.SetFaceEyeType(parts, FaceGroupType.EyeR, FacialDurationTime, true, false);
                        });
                    }
                    break;

                case StoryTimelineTrackData.TrackType.CharaFaceEar:
                    {
                        var nextClipFaceInfoList1 = (nextClipData as StoryTimelineCharaFaceClipData).FaceInfoList1;
                        var nextEarType = EventTimelineModelController.GetEarType(nextClipFaceInfoList1[0].Name);
                        var currentClipFaceInfoList1 = FaceInfoList1;
                        var currentEarType = EventTimelineModelController.GetEarType(currentClipFaceInfoList1[0].Name);

                        //耳も違うタイプの場合だけ補間する
                        if(nextEarType != currentEarType)
                        {
                            modelController.SetEar(currentEarType, Mathf.Lerp(1f, 0f, t) * currentClipFaceInfoList1[0].Weight, FacialDurationTime, true);
                            modelController.SetEar(nextEarType, Mathf.Lerp(0f, 1f, t) * nextClipFaceInfoList1[0].Weight, FacialDurationTime, true);
                        }
                        else
                        {
                            modelController.SetEar(currentEarType, currentClipFaceInfoList1[0].Weight, FacialDurationTime);
                        }
                    }
                    break;
            }
        }
        #endregion 表情変更にアニメーションカーブを使う場合

        /// <summary>
        /// 再生に使用するデータに変換する
        /// </summary>
        protected virtual FaceParts[] MakeFaceParts(List<FaceInfo> faceInfoList, Type type )
        {
            int count = faceInfoList.Count;
            if( count == 0 )
            {
                return null;
            }

            FaceParts[] drivenKeyTypeArray = new FaceParts[count];
            for (int i = 0; i < count; i++)
            {
                drivenKeyTypeArray[i] = new FaceParts
                {
                    _faceParts = (int)Enum.Parse(type, faceInfoList[i].Name),
                    _weight = faceInfoList[i].Weight,
                    _originalWeight = faceInfoList[i].Weight,
                };
            }

            return drivenKeyTypeArray;
        }

        protected bool IsRequestBlink(EventTimelineModelController model, FaceParts[] facePartsArray,bool isSettingBlock)
        {
            //準備ブロックの場合は無視
            if (isSettingBlock)
            {
                return false;
            }

            //育成会話とホーム会話以外は除外
            if(!TimelineController.TimelineData.IsSingleMode
            && (TimelineController.TimelineData.SceneType != StoryTimelineData.SupportedSceneType.Home))
            {
                return false;
            }

            int num = facePartsArray.Length;
            for(int i=0;i<num;i++)
            {
                if(model.IsForceEyeBlinkTarget(facePartsArray[i]._faceParts))
                {
                    //指定の目であれば対象
                    return true;
                }
            }

            return false;
        }

        #endregion Update

        // エディタ用
        #region Editor

#if CYG_DEBUG && UNITY_EDITOR

        // リソース登録
        #region Resources

        public void Clear()
        {
            FaceInfoList1.Clear();
            FaceInfoList2.Clear();
        }

        // SaveClipResources()で耳モーションの登録は不要

        #endregion Resources

        // プロパティ表示エリアの描画
        #region Property
        public override void DrawProperty(SerializedObject obj, ref float posY)
        {
            posY = StoryTimelineEditorPropertyWindow.AddLabelField(posY, "FaceTypePart1");
            int count = FaceInfoList1.Count;
            for (int i = 0; i < count; i++)
            {
                posY = StoryTimelineEditorPropertyWindow.AddStringInputField(posY, i.ToString(), ref FaceInfoList1[i].Name );

                posY = StoryTimelineEditorPropertyWindow.AddSliderField( posY, "Weight", ref FaceInfoList1[i].Weight, 0.0f, 2.0f, null );
            }

            posY = StoryTimelineEditorPropertyWindow.AddLabelField(posY, "FaceTypePart2");
            count = FaceInfoList2.Count;
            for (int i = 0; i < count; i++)
            {
                posY = StoryTimelineEditorPropertyWindow.AddStringInputField(posY, i.ToString(), ref FaceInfoList2[i].Name);

                posY = StoryTimelineEditorPropertyWindow.AddSliderField(posY, "Weight", ref FaceInfoList2[i].Weight, 0.0f, 2.0f, null);
            }

            // パドックだけ表情遷移カーブ補間処理を適用する
            // #92289 Mayaカメラを使う場合も設定可能にする
            if (TimelineData.SceneType == StoryTimelineData.SupportedSceneType.Paddock ||
                TimelineData.UseMayaCamera)
            {
                DrawPropertyTransitionCurve(ref posY);
            }

            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "表情を即時反映させる", ref IsImmediate);
        }

        //#94638 複数表情クリップ選択する際に表情即時反映フラグを表示する
        public static void DrawPropertyMultiFaceClip(ref float posY, List<StoryTimelineClipData> selectedClipList, EditorWindow baseEditorWindow)
        {
            var count = selectedClipList.Count;
            var isImmediate = false;
            var showWarningBox = false;
            void CheckShowWaringBox(int index, bool clipIsImmediate)
            {
                if (index == 0)
                {
                    isImmediate = clipIsImmediate;
                }
                else
                {
                    if (isImmediate != clipIsImmediate)
                    {
                        //警告を出す
                        showWarningBox = true;
                    }
                }
            }

            for (int i = 0; i < count; i++)
            {
                var clip = selectedClipList[i];
                if (clip is StoryTimelineCharaFaceClipData faceClip)
                {
                    CheckShowWaringBox(i, faceClip.IsImmediate);
                }

                if (showWarningBox)
                {
                    break;
                }
            }

            if (showWarningBox)
            {
                posY = StoryTimelineEditorPropertyWindow.AddHelpBox(posY, "<color=red>表情を即時反映させる✓の有無が統一されておりません</color>", MessageType.Error, 13, true);
            }

            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "表情を即時反映させる", ref isImmediate, onValueChanged: () =>
            {
                foreach (var clip in selectedClipList)
                {
                    if (clip is StoryTimelineCharaFaceClipData faceClip)
                    {
                        faceClip.IsImmediate = isImmediate;
                    }
                }
            });
        }

        /// <summary>
        /// 表情遷移のプロパティ
        /// </summary>
        private void DrawPropertyTransitionCurve(ref float posY)
        {
            //ブロック内最後のキーは補間対象がないため、この設定を非活性にしておく
            EditorGUI.BeginDisabledGroup(GetNextClipData() == null);
            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "遷移カーブ使用", ref UseTransitionCurve, () =>
            {
                //カーブ設定が有効になった場合必ず毎フレーム呼ばれたいので、補間処理呼び出しタイプを設定
                Lerp = UseTransitionCurve ? LerpType.Lerp : LerpType.None;
            });

            if (UseTransitionCurve)
            {
                // 顔の遷移カーブ
                posY = StoryTimelineEditorPropertyWindow.AddCurveField(posY, "Transition Curve", Curve);
            }
            EditorGUI.EndDisabledGroup();
        }
        #endregion Property

#endif

        #endregion Editor
    }
}
