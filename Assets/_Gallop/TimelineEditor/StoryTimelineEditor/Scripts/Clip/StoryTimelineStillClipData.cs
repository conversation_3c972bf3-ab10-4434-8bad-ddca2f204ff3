using System.Collections.Generic;
using System.Linq;
using UnityEngine;
#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// Timeline上のStill用Clip１つ分のデータ
    /// </summary>
    public class StoryTimelineStillClipData : StoryTimelineClipData
    {
        /// <summary>スチル絵の表示状態</summary>
        public bool IsVisible = true;

        /// <summary>汎用パーティクルの使用</summary>
        public bool UseCommonParticle = false;

        /// <summary>表示するStillのID</summary>
        [System.Obsolete]
        public int StoryStillId = StoryTimelineController.INVALID_VALUE;

        /// <summary>表示するStillのID</summary>
        public int StoryStillGroupId = StoryTimelineController.INVALID_VALUE;

        /// <summary>環境音設定</summary>
        public StoryTimelineBgClipData.BgmInfo BgmInfo = new StoryTimelineBgClipData.BgmInfo();

        private bool IsStillEnabled => TimelineController.StillController != null;

        #region ConstantValue

        private const int CURRENT_DATA_VERSION = 1;

        #endregion

        public override void Initialize()
        {
            // スチルに環境音再生が実装される前に作られたクリップでは環境を制御しないようにする
            if (BgmInfo.CueName == null && BgmInfo.PlayEnvBgm)
            {
                BgmInfo.PlayEnvBgm = false;
            }
        }

        public override void StartClipData(int frameCount, StoryTimelineClipData prevClip, bool isWaiting, StoryTimelineClipData nextClip = null)
        {
            if (!IsStillEnabled) return;
            if (!(TrackData is StoryTimelineStillTrackData stillTrackData)) return;

            stillTrackData.SetStoryStill(this);
        }

        #region Editor
#if CYG_DEBUG && UNITY_EDITOR
        private bool _initialized = false;
        private Dictionary<int, (string[] textArray, List<int> valueList)> _stillIdGroupedByChapterIdDic;
        private List<int> _stillChapterIdList;
        private string[] _stillChapterIdTextArray;
        private int _selectedStillChapterId;
        private List<int> _stillIdList;
        private string[] _stillIdTextArray;

        #region スチル絵のカメラサイズの計算方法
        /// <summary>カメラサイズの計算方法</summary>
        private enum FittingMode
        {
            旧形式,
            イラストチーム想定
        }

        /// <summary>現在のモード（全クリップで同じなのでstatic変数）</summary>
        private static FittingMode _fittingMode;

        /// <summary>カメラサイズの計算方法を変えた時の処理</summary>
        private void OnFittingModeChanged()
        {
            // フラグ切り替え
            var isLegacy = _fittingMode == FittingMode.旧形式;
            TimelineData.IsLegacyFittingMode = isLegacy;
            TimelineController.StillController.IsLegacyFittingMode = isLegacy;

            // 最大サイズ調整
            TimelineController.StillController.UpdateCameraSizeByDisplayMode();
        }
        #endregion スチル絵のカメラサイズの計算方法

        /// <summary>
        /// BGMオーバーライド設定
        /// </summary>
        private StoryTimelineBgClipData.BgmDropdownCache _bgmDropdownCache;

        /// <summary>
        /// プロパティ表示
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="posY"></param>
        public override void DrawProperty(SerializedObject obj, ref float posY)
        {
            if (!MasterDataManager.HasInstance())
            {
                return;
            }
            if (!_initialized)
            {
                _initialized = true;
                // マスターに登録されたStoryStillIdを上位5桁以上でグループ化して取得する
                _stillIdGroupedByChapterIdDic = MasterDataManager.Instance.masterStoryStill
                    .GetStoryStillGroupIdEnumerable()
                    .GroupBy(StillGroupIdToStillChapterId)
                    .ToDictionary(
                        group => group.Key,
                        group => (
                            group.Select(stillId => stillId.ToString()).ToArray(),
                            group.ToList()));
                _stillChapterIdList = _stillIdGroupedByChapterIdDic.Keys.ToList();
                _stillChapterIdTextArray = _stillChapterIdList.Select(id => id.ToString()).ToArray();
                _selectedStillChapterId = StillGroupIdToStillChapterId(StoryStillGroupId);
                UpdateStillIdList();
            }

            // カメラサイズの計算方法を設定
            _fittingMode = TimelineData.IsLegacyFittingMode ? FittingMode.旧形式 : FittingMode.イラストチーム想定;
            posY = StoryTimelineEditorPropertyWindow.AddEnumInputField(posY, "スチル絵のEtoE", ref _fittingMode, onValueChanged: OnFittingModeChanged);

            // 表示状態の設定
            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "表示", ref IsVisible, UpdateClip);

            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "汎用パーティクル", ref UseCommonParticle);

            // StoryStillGroupIdをフィルターする際のチャプターIDの選択
            posY = StoryTimelineEditorPropertyWindow.AddPullDownField(posY, "Chapter",
                ref _selectedStillChapterId, _stillChapterIdList, _stillChapterIdTextArray, UpdateStillIdList);

            // StoryStillIdの設定
            posY = StoryTimelineEditorPropertyWindow.AddPullDownField(posY, "StillId",
                ref StoryStillGroupId, _stillIdList, _stillIdTextArray, UpdateClip);

            DrawBgmProperty(ref posY);

            // クリップを評価する（Editorイベント内でStoryStill内のスケールを調整できないのでCoroutineを使う）
            void UpdateClip()
            {
                if (TrackData.TryGetLatestClip(out StoryTimelineStillClipData stillClipData) &&
                    stillClipData == this)
                {
                    EditorApplication.delayCall += () => StartClipData(
                        TimelineController.FrameCount, null, false);
                }
            }

            // StoryStillGroupIdから章番号までを抽出する
            int StillGroupIdToStillChapterId(int groupId) => groupId / 10000;

            // 章番号の選択状況に合わせてStoryStillGroupIdのリストを選択する
            void UpdateStillIdList()
            {
                if (!_stillIdGroupedByChapterIdDic.TryGetValue(_selectedStillChapterId, out var tuple)) return;
                _stillIdList = tuple.valueList;
                _stillIdTextArray = tuple.textArray;
            }
        }

        /// <summary>
        /// BGM設定を表示
        /// </summary>
        private void DrawBgmProperty(ref float posY)
        {
            posY = StoryTimelineEditorPropertyWindow.AddLabelField(posY, "環境音");

            posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "再生", ref BgmInfo.PlayEnvBgm);

            using (new EditorGUI.DisabledScope(!BgmInfo.PlayEnvBgm))
            {
                // プルダウン情報収集
                if (!_bgmDropdownCache.IsLoaded)
                {
                    _bgmDropdownCache = new StoryTimelineBgClipData.BgmDropdownCache(
                        MasterDataManager.Instance.masterBackgroundData.dictionary.Values,
                        StoryTimelineEditorWindow.PropertyWindow.BgSeCsv);
                }

                if (_bgmDropdownCache.IsLoaded)
                {
                    // プルダウン表示
                    var cueName = BgmInfo.CueName;
                    posY = StoryTimelineEditorPropertyWindow.AddPullDownField(posY, "CueName", ref cueName,
                        _bgmDropdownCache.ValueList, _bgmDropdownCache.NameArray,
                        onValueChanged: () => { BgmInfo.CueName = cueName; });
                }

                // 直接入力
                posY = StoryTimelineEditorPropertyWindow.AddStringInputField(posY, "直接入力", ref BgmInfo.CueName);

                posY = StoryTimelineEditorPropertyWindow.AddBoolInputField(posY, "汎用キューシート", ref BgmInfo.UseCommonCueSheet);
            }
        }

        public override void SaveClipResources(StoryTimelineData timelineData, StoryTimelineResourceList resourceList)
        {
            if (StoryStillGroupId == StoryTimelineController.INVALID_VALUE)
            {
                return;
            }

            // 環境音の共通キューシート
            resourceList.SaveSoundSePath(AudioManager.SE_ATMOS_COMMON_CUE_SHEET_NAME, TrackData.Type);

            foreach (var path in MasterDataManager.Instance.masterStoryStill
                .GetStoryStillListByGroupId(StoryStillGroupId)
                .Select(storyStill => storyStill.GetAssetPath()))
            {
                resourceList.SaveResourcesPath(path, TrackData.Type);
            }

            // 汎用パーティクルの使用
            if (UseCommonParticle)
            {
                var path = ResourcePath.GetSingleModeStillParticlePath();
                resourceList.SaveResourcesPath(path, TrackData.Type);
            }
        }

        /// <summary>
        /// クリップデータを作成する
        /// </summary>
        public override StoryTimelineClipData CreateClipData(StoryTimelineTrackData trackData)
        {
            var path = _editorWindow.AssetPath;
            return CreateClipData<StoryTimelineStillClipData>(path, trackData);
        }

#endif
        #endregion Editor
    }

    public static class StoryStillClipExtension
    {
        public static int GetVisibleStoryStillId(this StoryTimelineStillClipData clipData)
        {
            return clipData != null && clipData.IsVisible
                ? clipData.StoryStillGroupId
                : StoryTimelineController.INVALID_VALUE;
        }
    }
}
