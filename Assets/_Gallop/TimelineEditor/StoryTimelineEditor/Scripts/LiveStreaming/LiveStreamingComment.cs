using UnityEngine;
using DG.Tweening;
using static Gallop.StaticVariableDefine.Story.StoryLiveStreaming;

namespace Gallop
{
    /// <summary>
    /// 配信風フィルター用コメントPrefabをまとめたクラス、まとめたコメントを保持して使い分ける
    /// </summary>
    /// <remarks> 本当はプールする形式にしたかったが、Prefabのメモリサイズが小さくかったのと工数でこの形式にした </remarks>
    public class LiveStreamingComment
    {
        #region 定数

        /// <summary> コメントのスクロール量 </summary>
        public const float SCROLL_DELTA_Y = 103.5f;
        
        /// <summary> Flashで設定するユーザー名のパス </summary>
        private const string USER_NAME_TEXT_PATH = "OBJ_txt_stream_user_name{0:00}/offset/MOT_txt_stream_user_name{0:00}/TXT_txt_stream_user_name{0:00}";
        
        /// <summary> Flashで設定するテキストのパス </summary>
        private const string TEXT_PATH = "OBJ_txt_stream_comment{0:00}/MOT_txt_stream_comment{0:00}/TXT_txt_stream_comment{0:00}";
        /// <summary> Flashで設定するテクスチャのパス </summary>
        private const string TEXTURE_PATH = "PLN_dum_stream_comment_icon{0:00}";
        
        /// <summary> アウトラインのオフセット </summary>
        private const float COMMENT_OUTLINE_OFFSET = 2;
        
        // 以下、Flashのラベル名
        // コメントのFlash
        private const string COMMENT_FLASH_LABEL_FADE = "fade";
        private const string COMMENT_FLASH_LABEL_LOOP_2 = "loop_2";

        #endregion

        #region Enum
        
        /// <summary>
        /// コメントの種類(内部で使用するFlashが異なるため)
        /// </summary>
        /// <remarks> CommentTypeがパスの番号と一致していることを前提にした実装があるため番号の変更不可 </remarks>
        public enum CommentType
        {
            NPCCommentOneLine = 0,  // NPCコメント(1行)
            NPCCommentTwoLine = 1,  // NPCコメント(2行)
            UserComment       = 2,  // ユーザーコメント
        }

        #endregion
        
        #region 変数
        
        /// <summary> 現在表示されているコメントの種類 </summary>
        private CommentType _currentCommentType; 
        
        /// <summary> 現在表示しているコメントのFlashPlayer </summary>
        private FlashPlayer _currentFlashPlayer;
        
        /// <summary> 現在表示しているコメントのTransform </summary>
        private Transform _currentTransform;
        
        private readonly FlashPlayer _npcCommentOneLineFlashPlayer;  // NPCコメント(1行)のFlashPlayer
        private readonly FlashPlayer _npcCommentTwoLineFlashPlayer;  // NPCコメント(2行)のFlashPlayer
        private readonly FlashPlayer _userCommentFlashPlayer;        // ユーザーコメントのFlashPlayer
        private readonly Transform _npcCommentOneLineTransform;      // NPCコメント(1行)のTransform
        private readonly Transform _npcCommentTwoLineTransform;      // NPCコメント(2行)のTransform
        private readonly Transform _userCommentTransform;            // ユーザーコメントのTransform

        #endregion
        
        #region コンストラクタ
        
        /// <summary>
        /// コンストラクタ
        /// </summary>
        public LiveStreamingComment(GameObject[] commentFlashObjArray, Transform rootObjTransform, string comment, CommentType commentType, Sprite commentIcon, bool iconExist, int index, int commentCount, Color outlineColor)
        {
            // コメントのFlashPlayerをロード、初期化時に非表示にするためにoutラベルを再生
            _npcCommentOneLineFlashPlayer = FlashLoader.Load(commentFlashObjArray[(int)CommentType.NPCCommentOneLine], rootObjTransform);
            _npcCommentOneLineFlashPlayer.gameObject.transform.localPosition = COMMENT_INIT_LOCAL_POS;
            _npcCommentOneLineFlashPlayer.Play(GameDefine.A2U_OUT_LABEL);
            _npcCommentTwoLineFlashPlayer = FlashLoader.Load(commentFlashObjArray[(int)CommentType.NPCCommentTwoLine], rootObjTransform);
            _npcCommentTwoLineFlashPlayer.gameObject.transform.localPosition = COMMENT_INIT_LOCAL_POS;
            _npcCommentTwoLineFlashPlayer.Play(GameDefine.A2U_OUT_LABEL);
            _userCommentFlashPlayer = FlashLoader.Load(commentFlashObjArray[(int)CommentType.UserComment], rootObjTransform);
            _userCommentFlashPlayer.gameObject.transform.localPosition = COMMENT_INIT_LOCAL_POS;
            _userCommentFlashPlayer.Play(GameDefine.A2U_OUT_LABEL);

            // コメントのTransformを設定
            _npcCommentOneLineTransform = _npcCommentOneLineFlashPlayer.gameObject.transform;
            _npcCommentTwoLineTransform = _npcCommentTwoLineFlashPlayer.gameObject.transform;
            _userCommentTransform = _userCommentFlashPlayer.gameObject.transform;
            
            // コメントの設定
            // NOTE : コメントの種類によってCurrentTransformが変わるため、コメントの設定はここで行う
            SetComment(comment,  commentType,  commentIcon,  iconExist);
            
            // コメントの初期位置とFlashのラベルを設定
            _currentTransform.localPosition += new Vector3(0, SCROLL_DELTA_Y * index, 0);
            string playLabel = GameDefine.A2U_LOOP_LABEL; // デフォルト表示用
            if (index == 0) // 最初は非表示用のラベルにする
            {
                playLabel = GameDefine.A2U_OUT_LABEL;
            }
            else if (index == commentCount - 1) // 最後は半透明表示用のラベルにする
            {
                playLabel = COMMENT_FLASH_LABEL_LOOP_2;
            }
            _currentFlashPlayer.Play(playLabel);
            
            // ユーザーコメントの文字の色設定
            _userCommentFlashPlayer.SetTextColor(USER_TEXT_COLOR, TextUtil.Format(USER_NAME_TEXT_PATH, 0));
            _userCommentFlashPlayer.SetTextColor(USER_TEXT_COLOR, GetTextPath(CommentType.UserComment));
            // アウトラインの文字色とオフセット設定
            _npcCommentOneLineFlashPlayer.SetTextOutline(outlineColor, COMMENT_OUTLINE_OFFSET, GetTextPath(CommentType.NPCCommentOneLine));
            _npcCommentTwoLineFlashPlayer.SetTextOutline(outlineColor, COMMENT_OUTLINE_OFFSET, GetTextPath(CommentType.NPCCommentTwoLine));
            _userCommentFlashPlayer.SetTextOutline(outlineColor, COMMENT_OUTLINE_OFFSET, TextUtil.Format(USER_NAME_TEXT_PATH, 0));
            _userCommentFlashPlayer.SetTextOutline(outlineColor, COMMENT_OUTLINE_OFFSET, GetTextPath(CommentType.UserComment));
        }

        /// <summary> 引数無しのコンストラクタは外部に公開しない </summary>
        private LiveStreamingComment() { }
        
        #endregion

        #region 関数
        
        #region 設定関連
        
        /// <summary>
        /// 今のコメントの種類によって現在のFlashPlayerを設定
        /// </summary>
        /// <param name="commentType"></param>
        private void SetCurrentFlashPlayer(CommentType commentType)
        {
            _currentFlashPlayer = commentType switch
            {
                CommentType.NPCCommentOneLine => _npcCommentOneLineFlashPlayer,
                CommentType.NPCCommentTwoLine => _npcCommentTwoLineFlashPlayer,
                CommentType.UserComment => _userCommentFlashPlayer,
                _ => null,
            };
        }
        
        /// <summary>
        /// 今のコメントの種類によって現在のTransformを設定
        /// </summary>
        /// <param name="commentType"></param>
        private void SetCurrentTransform(CommentType commentType)
        {
            _currentTransform = commentType switch
            {
                CommentType.NPCCommentOneLine => _npcCommentOneLineTransform,
                CommentType.NPCCommentTwoLine => _npcCommentTwoLineTransform,
                CommentType.UserComment => _userCommentTransform,
                _ => null,
            };
        }
        
        /// <summary>
        /// コメントの内容を設定
        /// </summary>
        /// <param name="comment">コメント</param>
        /// <param name="commentType">コメントの種類</param>
        /// <param name="commentIcon">コメントしているユーザーのアイコン</param>
        /// <param name="iconExist">アイコンが存在しているかどうか</param>
        /// <remarks> アイコンが存在していない場合があるのでそれを考慮している </remarks>>
        public void SetComment(string comment, CommentType commentType, Sprite commentIcon, bool iconExist)
        {
            // 現在のコメントの種類を設定
            _currentCommentType = commentType;
            // 現在のFlashPlayerを設定
            SetCurrentFlashPlayer(_currentCommentType);
            // 現在のTransformを設定
            SetCurrentTransform(_currentCommentType);
            
            var flashPlayer = _currentFlashPlayer;
            // コメントを設定
            flashPlayer.SetText(comment, GetTextPath(commentType));
            // コメントの種類によってアイコンを設定
            if (iconExist)
            {
                flashPlayer.SetSprite(GetTexturePath(commentType), commentIcon);
            }
            // ユーザーコメントの場合はユーザー名を設定
            var isUserComment = commentType == CommentType.UserComment;
            if (isUserComment)
            {
                flashPlayer.SetText(GallopUtil.GetUserName(), TextUtil.Format(USER_NAME_TEXT_PATH, 0));
            }
        }
        
        #endregion
        
        #region 移動関連

        /// <summary>
        /// 要素が見えないようにラベルを変更し、初期位置に戻す
        /// </summary>
        public void PopUp()
        {
            var flashPlayer = _currentFlashPlayer;
            // コメントの内容を設定
            flashPlayer.Play(GameDefine.A2U_OUT_LABEL);
            flashPlayer.transform.localPosition = COMMENT_INIT_LOCAL_POS;
        }
        
        /// <summary>
        /// 移動しながらフェードアウトして半透明になる
        /// </summary>
        public void FadeOut(float duration, TweenCallback onComplete = null)
        {
            var flashPlayer = _currentFlashPlayer;
            flashPlayer.Play(COMMENT_FLASH_LABEL_FADE);
            Scroll(duration, onComplete);
        }
        
        /// <summary>
        /// 移動しながらフェードインして表示
        /// </summary>
        public void FadeIn(float duration, TweenCallback onComplete = null)
        {
            var flashPlayer = _currentFlashPlayer;
            flashPlayer.Play(GameDefine.A2U_IN_LABEL);
            Scroll(duration, onComplete);
        }
        
        /// <summary>
        /// 一つ上のコメント位置まで移動する処理
        /// </summary>
        public void Scroll(float duration, TweenCallback onComplete = null)
        {
            var flashPlayerTransform = _currentTransform;
            flashPlayerTransform.DOLocalMoveY(flashPlayerTransform.localPosition.y + SCROLL_DELTA_Y, duration).SetEase(Ease.OutQuad).OnComplete(() =>
            {
                onComplete?.Invoke();
            });
        }
        
        #endregion

        #region パスの設定
        
        /// <summary>
        /// Flashのテキストのパスを取得
        /// </summary>
        /// <returns></returns>
        private string GetTextPath(CommentType commentType)
        {
            // コメントの内容を設定
            int index = commentType switch
            {
                CommentType.NPCCommentOneLine => 0,
                CommentType.NPCCommentTwoLine => 1,
                CommentType.UserComment => 0,
                _ => -1, // ここには来ない想定だが万が一呼ばれた時にエラーになるように-1を設定
            };
            return TextUtil.Format(TEXT_PATH, index);
        }
        
        /// <summary>
        /// Flashのテクスチャのパスを取得
        /// </summary>
        /// <returns></returns>
        private string GetTexturePath(CommentType commentType)
        {
            // コメントの内容を設定
            int index = commentType switch
            {
                CommentType.NPCCommentOneLine => 0,
                CommentType.NPCCommentTwoLine => 0,
                CommentType.UserComment => 1,
                _ => -1, // ここには来ない想定だが万が一呼ばれた時にエラーになるように-1を設定
            };
            return TextUtil.Format(TEXTURE_PATH, index);
        }
        
        #endregion
        
        #endregion
    }
}
