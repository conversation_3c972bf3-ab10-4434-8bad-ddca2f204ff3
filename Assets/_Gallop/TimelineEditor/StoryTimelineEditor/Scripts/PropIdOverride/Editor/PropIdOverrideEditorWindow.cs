#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace Gallop
{
    using static StoryTimelineEditorPropertyWindow;
    using static StoryTimelineCharaPropClipData;

    using OverrideInfo = PropIdOverrideData.OverrideInfo;
    using StringPullDown = StoryTimelineEditorPropertyWindow.PullDownFieldCache<string>;
    using PropTypePullDown = StoryTimelineEditorPropertyWindow.PullDownFieldCache<StoryTimelineCharaPropClipData.PropType>;

    /// <summary>
    /// <see cref="PropIdOverrideData"/>を編集するためのエディタウィンドウ
    /// </summary>
    public class PropIdOverrideEditorWindow : EditorWindow
    {
        private const string SAVE_PATH = ResourcePath.BundleResourcesAssetsPath + ResourcePath.PROP_ID_OVERRIDE_DATA_PATH + ".asset";
        private const float DELETE_BUTTON_WIDTH = 50;

        private PropIdOverrideData _data;
        private Vector2 _scrollPos;

        /// <summary>
        /// エディタウィンドウを開いているか
        /// </summary>
        public static bool IsOpen()
        {
            return HasOpenInstances<PropIdOverrideEditorWindow>();
        }

        /// <summary>
        /// エディタウィンドウを開く
        /// </summary>
        public static void Open()
        {
            var win = GetWindow<PropIdOverrideEditorWindow>();
            win.Init();
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Init()
        {
            minSize = new Vector2(400, 400);

            // データをロードして初期化
            _data = LoadData();
        }

        /// <summary>
        /// アセットをロードする
        /// </summary>
        private static PropIdOverrideData LoadData()
        {
            var data = PropIdOverrideData.Load();
            if (data != null) return data;

            // まだないので新規作成
            data = CreateInstance<PropIdOverrideData>();
            data.Init();
            AssetDatabase.CreateAsset(data, SAVE_PATH);
            return data;
        }

        /// <summary>
        /// アセットを保存する
        /// </summary>
        public void Save()
        {
            if (_data is null) return;
            EditorUtility.SetDirty(_data);
            AssetDatabase.SaveAssets();
        }

        /// <summary>
        /// GUI描画
        /// </summary>
        private void OnGUI()
        {
            if (StoryTimelineEditorWindow.PropertyWindow is null)
            {
                EditorGUILayout.HelpBox("タイムラインが起動していないためモーションリストを取得できません", MessageType.Warning);
                return;
            }

            _scrollPos = EditorGUILayout.BeginScrollView(_scrollPos);

            // データの表示
            EditorGUILayout.LabelField($"PropIdの上書き設定 (データ数: {_data.Length})");
            using (new GUILayout.VerticalScope(GUI.skin.box))
            {
                OnGUI_PropIdOverrideData(_data);
            }

            // 追加ボタン
            EditorGUILayout.Space();
            if (GUILayout.Button("追加"))
            {
                GUIUtility.keyboardControl = 0;
                _data.Add();
            }

            // ソートボタン
            EditorGUILayout.Space();
            if (GUILayout.Button("キャラID順でソート"))
            {
                GUIUtility.keyboardControl = 0;
                _data.Sort();
            }

            // 保存ボタン
            EditorGUILayout.Space();
            if (GUILayout.Button("保存"))
            {
                Save();
            }

            EditorGUILayout.EndScrollView();
        }

        /// <summary>
        /// PropIdOverrideDataのGUI描画
        /// </summary>
        private static void OnGUI_PropIdOverrideData(PropIdOverrideData data)
        {
            for (int i = 0; i < data.Length; i++)
            {
                var info = data[i];

                // サマリー表示と削除ボタン
                EditorGUILayout.BeginHorizontal();
                OnGUI_Summary(i, data, info);
                EditorGUILayout.EndHorizontal();

                // 個別設定
                if (info.IsFoldoutOpen)
                {
                    OnGUI_OverrideInfo(info);
                }
            }
        }

        /// <summary>
        /// OverrideInfoの概要と削除ボタンのGUI描画
        /// </summary>
        private static void OnGUI_Summary(int i, PropIdOverrideData data, OverrideInfo info)
        {
            info.IsFoldoutOpen = EditorGUILayout.Foldout(info.IsFoldoutOpen, $"設定{i + 1} ({info.Summary})");

            EditorGUILayout.Space();

            if (GUILayout.Button("削除", GUILayout.Width(DELETE_BUTTON_WIDTH)))
            {
                data.Remove(i);
            }
        }

        /// <summary>
        /// OverrideInfoのGUI描画
        /// </summary>
        private static void OnGUI_OverrideInfo(OverrideInfo info)
        {
            EditorGUI.indentLevel++;
            info.OnGUI();
            EditorGUI.indentLevel--;
        }
    }

    #region 編集サポート用の拡張

    public partial class PropIdOverrideData
    {
        #region PropDetailの拡張
        /// <summary>
        /// PropDetailのOnGUIを実装したパーシャルクラス
        /// </summary>
        public partial class PropDetail
        {
            private StringPullDown _motionTagPullDown = new() { Title = "所属" };
            private string _motionTagFilter;
            private string _motionName;
            private string[] _motionNameArray;
            private string _motionNameFilter;
            private int _selectMotionIndex = -1;
            private PropTypePullDown _propTypePullDown = new() { Title = "表示種別" };
            private PropType _propType;

            public void OnGUI(int charaId)
            {
                // 小物の種類
                OnGUI_PropTypePullDown();

                EditorGUILayout.BeginHorizontal();
                PropMainId = EditorGUILayout.IntField("小物ID", PropMainId);
                PropSubId = EditorGUILayout.IntField("サブID", PropSubId);
                EditorGUILayout.EndHorizontal();

                SwapTextureKey = EditorGUILayout.IntField("入替テクスチャ番号", SwapTextureKey);
                Bone = (AttachBone)EditorGUILayout.EnumPopup("アタッチ先", Bone);

                // プルダウンでモーションが選べるようにする
                OnGUI_MotionPullDown(charaId);

                MirrorMotion = EditorGUILayout.Toggle("反転", MirrorMotion);
                StateType = (MotionStateType)EditorGUILayout.EnumPopup("モーションの種類", StateType);
            }

            private void OnGUI_PropTypePullDown()
            {
                InitPropTypePullDown();

                _propType = Type;
                _propTypePullDown.AddPullDownField(ref _propType, () => { Type = _propType; });
            }

            private void OnGUI_MotionPullDown(int charaId)
            {
                // 初期化直後はまだモーションリストがセットアップされていないので更新をかけておく
                UpdateMotionPullDown(charaId);

                // タグ選択
                _motionTagPullDown.AddPullDownField(ref _motionTagFilter, () =>
                {
                    UniqueMotion = _motionTagFilter.Equals(MOTION_TAG_UNIQUE);
                    UpdateMotionPullDownForce(charaId);
                });

                EditorGUILayout.BeginHorizontal();
                {
                    // 絞り込み付きモーション選択
                    GUIUtil.ShowCharaViewerPopup
                    (
                        "モーション", _selectMotionIndex, _motionNameArray, null, null,
                        (index, _) =>
                        {
                            _selectMotionIndex = index;
                            _motionName = _motionNameArray[index];

                            // モーション名とサフィックスを取得
                            var motionName = _motionName == NULL_MOTION_VALUE ? string.Empty : _motionName;
                            var suffix = Suffix;
                            const bool isDoll = false;
                            (MotionName, Suffix) = GetMotionNameAndSuffix(motionName, suffix, isDoll);
                        },
                        onLateUpdatePopup: () =>
                        {
                            EditorGUI.BeginChangeCheck();
                            _motionNameFilter = EditorGUILayout.TextField(_motionNameFilter);
                            if (EditorGUI.EndChangeCheck())
                            {
                                UpdateMotionPullDownForce(charaId);
                            }
                        },
                        isUseLabelField: true
                    );
                }
                EditorGUILayout.EndHorizontal();
            }

            #region リスト初期化

            private void UpdateMotionPullDownForce(int charaId)
            {
                UpdateMotionPullDown(charaId, force: true);
            }

            private void UpdateMotionPullDown(int charaId, bool force = false)
            {
                if (_motionTagPullDown.ParamList == null)
                {
                    _motionTagPullDown.UpdateList(
                        new[] { MOTION_TAG_COMMON, MOTION_TAG_UNIQUE },
                        new[] { "汎用", "固有" });

                    _motionTagFilter = UniqueMotion ? MOTION_TAG_UNIQUE : MOTION_TAG_COMMON;
                    _motionName = string.IsNullOrEmpty(MotionName) ? NULL_MOTION_VALUE : MotionName;
                }

                if (force == false && _motionNameArray != null)
                {
                    return;
                }

                var suffixArray = new[] { MOTION_SUFFIX_LEFT, MOTION_SUFFIX_RIGHT };

                var resultMotionNameList = new List<string> { NULL_MOTION_VALUE };
                var propMotionList = StoryTimelineEditorWindow.PropertyWindow.GetPropMotionEnumerable(
                        filterName: _motionNameFilter,
                        filterTag: MOTION_TAG_UNIQUE,
                        inclusiveTag: UniqueMotion,
                        checkRosource: false,
                        charaId: charaId,
                        subId: MOTION_SUB_ID,
                        suffixArray: suffixArray)
                    .Where(motionName => !motionName.EndsWith(ResourcePath.MirrorSuffix));
                resultMotionNameList.AddRange(propMotionList);

                if (!string.IsNullOrEmpty(_motionName) && !resultMotionNameList.Contains(_motionName))
                {
                    // 既存の値がリストに存在しない場合は既存値をリストに追加
                    // アセットが存在しなくなった場合などにプロパティ表示した時に編集データを保護する
                    resultMotionNameList.Add(_motionName);
                }

                _motionNameArray = resultMotionNameList.ToArray();

                //_motionNameが空の状態だった場合は一番目のNoneの状態として扱う
                _selectMotionIndex = string.IsNullOrEmpty(_motionName)
                    ? 0
                    : Array.IndexOf(_motionNameArray, _motionName);
            }

            private void InitPropTypePullDown()
            {
                if (_propTypePullDown.ParamList == null)
                {
                    // スクリプター要望で通常PropとToonPropの2つをサポート
                    _propTypePullDown.UpdateList(
                        new []{ PropType.Prop, PropType.ToonProp },
                        new []{ "Prop", "ToonProp" });
                }
            }
            #endregion リスト初期化
        }
        #endregion PropDetailの拡張

        #region OverrideInfoの拡張
        /// <summary>
        /// OverrideInfoのOnGUIを実装したパーシャルクラス
        /// </summary>
        public partial class OverrideInfo
        {
            private const int MIN_PROP_COUNT = 1;
            private const int MAX_PROP_COUNT = StoryTimelineCharaPropTrackData.TRACK_COUNT;

            public bool IsFoldoutOpen { get; set; }
            public string Summary => $"キャラID:{CharaId}, Propの数:{Length}";

            public void OnGUI()
            {
                CharaId = EditorGUILayout.IntField("キャラID", CharaId);

                // 小物の数
                int count = _detailArray.Length;
                count = EditorGUILayout.IntField("小物の数", count);
                count = Mathf.Clamp(count, MIN_PROP_COUNT, MAX_PROP_COUNT);

                // 配列の数が違う場合はリサイズし, 増やす場合は初期化もする
                int oldCount = _detailArray.Length;
                if (count != oldCount)
                {
                    Array.Resize(ref _detailArray, count);
                    for (int i = oldCount; i < count; i++)
                    {
                        _detailArray[i] = new PropDetail();
                    }
                }

                // 個別設定
                for (int i = 0; i < _detailArray.Length; i++)
                {
                    OnGUI_PropDetail(i, CharaId, _detailArray[i]);
                }
            }

            private void OnGUI_PropDetail(int index, int charaId, PropDetail detail)
            {
                EditorGUILayout.LabelField($"-- {index + 1}個目 --");

                EditorGUI.indentLevel++;
                detail.OnGUI(charaId);
                EditorGUI.indentLevel--;
            }
        }
        #endregion OverrideInfoの拡張

        #region PropIdOverrideDataの拡張

        public void Init()
        {
            _infoArray = Array.Empty<OverrideInfo>();
        }

        public void Add()
        {
            ArrayUtility.Add(ref _infoArray, new OverrideInfo());
        }

        public void Remove(int i)
        {
            ArrayUtility.RemoveAt(ref _infoArray, i);
        }

        public void Sort()
        {
            Array.Sort(_infoArray, (infoA, infoB) => infoA.CharaId - infoB.CharaId);
        }
        #endregion PropIdOverrideDataの拡張
    }
    #endregion 編集サポート用の拡張
}
#endif
