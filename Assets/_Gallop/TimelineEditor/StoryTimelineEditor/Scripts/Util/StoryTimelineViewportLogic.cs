using UnityEngine;

namespace Gallop.StoryTimeline
{
    public static class ViewportLogic
    {
        private const float ASPECT_16_9 = GameDefine.BASE_ASPECT_RATIO;
        private const float ASPECT_MIN = 4f / 3f;
        private const float ASPECT_MAX = 19.5f / 9f;

        public static Vector2 CalcIdealSizeFrom(Vector2 imageSize)
        {
            return CalcIdealSizeFrom(imageSize.x);
        }

        public static Vector2 CalcIdealSizeFrom(float imageWidth)
        {
            return new Vector2(imageWidth, Mathf.Floor(imageWidth / ASPECT_16_9));
        }

        public static Vector2 CalcFitSize(Vector2 minSize, Vector2 maxSize, float cameraAspect)
        {
            if (IsHorizontalFit(cameraAspect))
            {
                return CalcHorizontalFitSize(minSize, maxSize, cameraAspect);
            }

            return CalcVerticalFitSize(minSize, maxSize, cameraAspect);
        }

        public static bool IsHorizontalFit(float cameraAspect)
        {
            return cameraAspect >= ASPECT_16_9;
        }

        public static Vector2 CalcHorizontalFitSize(Vector2 minSize, Vector2 maxSize, float cameraAspect)
        {
            var t = (cameraAspect - ASPECT_16_9) / (ASPECT_MAX - ASPECT_16_9);
            var width = Mathf.Lerp(minSize.x, maxSize.x, t);
            var height = width / cameraAspect;
            if (height > maxSize.y)
            {
                height = maxSize.y;
                width = height * cameraAspect;
            }

            return new Vector2(width, height);
        }

        public static Vector2 CalcVerticalFitSize(Vector2 minSize, Vector2 maxSize, float cameraAspect)
        {
            var t = (cameraAspect - ASPECT_MIN) / (ASPECT_16_9 - ASPECT_MIN);
            var height = Mathf.Lerp(maxSize.y, minSize.y, t);
            var width = height * cameraAspect;
            if (width > maxSize.x)
            {
                width = maxSize.x;
                height = width / cameraAspect;
            }

            return new Vector2(width, height);
        }
    }
}
