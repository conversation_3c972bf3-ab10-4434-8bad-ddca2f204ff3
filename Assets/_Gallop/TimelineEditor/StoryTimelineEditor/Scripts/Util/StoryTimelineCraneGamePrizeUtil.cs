using System.Collections.Generic;
using System.Linq;
using LitJson;
using UnityEngine.UI;

namespace Gallop
{
    using ResultState = CraneGameDefines.ServerGameResult;
    using PrizeInfo = CraneGameInfo.PrizeInfo;
    
    /// <summary>
    /// StoryTimelineCharaPropDataで表示するクレーンゲーム景品のためのUtilクラス
    /// </summary>
    /// <remarks>
    /// クレーンゲームの景品はミニモデルになっている
    /// 座標調整のために小物をかませている
    /// 階層としては以下の形になる
    ///   キャラのアタッチノード
    ///    + 小物 (ロケーターとしての役割)
    ///      + ミニモデル1
    ///      + ミニモデル2
    ///      + ミニモデル3
    /// </remarks>
    public static class StoryTimelineCraneGamePrizeUtil
    {
        #region 定数
        // クレーンゲーム景品用小物のモーション
        private const string CRANE_GAME_PROP_MOTION_GREAT_SUCCESS = "doll01";
        private const string CRANE_GAME_PROP_MOTION_GREAT_SUCCESS_BIG = "doll04";

        // クレーンゲーム景品の数
        public const int CRANE_GAME_PRIZE_SUCCESS = 1;
        public const int CRANE_GAME_PRIZE_SUCCESS_BIG = 1;
        public const int CRANE_GAME_PRIZE_GREAT_SUCCESS = 6;
        public const int CRANE_GAME_PRIZE_GREAT_SUCCESS_BIG = 3;

        // クレーンゲーム景品(Miniモデル)のスケール
        public const float PRIZE_MODEL_SCALE = 0.15f;
        public const float PRIZE_MODEL_SCALE_BIG = PRIZE_MODEL_SCALE * 1.5f;

        // クレーンゲーム景品(Miniモデル)のモーションと表情
        public const string PRIZE_MODEL_MOTION = "Catch01";
        public const string PRIZE_MODEL_MOTION_BIG = "Catch12";
        public const string PRIZE_MODEL_FACE = "PrizeA";
        
        // クレーンゲームのダミー設定値
        public const int PRIZE_MODEL_CHARA_ID = 1001;
        public const int PRIZE_MODEL_DRESS_ID = 2;
        
        // 開発用・デバッグ用定数
#if CYG_DEBUG
        public const int MAX_PRIZE_COUNT = CRANE_GAME_PRIZE_GREAT_SUCCESS;
        public const int CRANE_GAME_PRIZE_PROP_ID = 1103;
        public const int CRANE_GAME_PRIZE_PROP_SUB_ID = 0;
#endif
        #endregion 定数

        #region エディタ用enum
#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// クレーンゲーム結果のID
        /// エディタ用に表示用ラベルも設定している
        /// </summary>
        public enum ResultId
        {
            [EnumDisplayName("失敗")]
            Failure = ResultState.Failed,

            [EnumDisplayName("成功")]
            Success = ResultState.Success,

            [EnumDisplayName("大成功")]
            GreatSuccess = ResultState.GreatSuccess,

            [EnumDisplayName("成功 (大)")]
            SuccessBig = ResultState.SuccessWithBig,

            [EnumDisplayName("大成功 (大)")]
            GreatSuccessBig = ResultState.GreatSuccessWithBig,
        }
#endif
        #endregion enum
        
        /// <summary>
        /// クレーンゲームの設定
        /// </summary>
        public enum CraneGamePrizeSetting
        {
            None,
            Success,
            SuccessBig,
            GreatSuccess,
            GreatSuccessBig,
            FreeInput,
        }

        #region データクラス
        /// <summary>
        /// ランタイムで使う結果情報
        /// </summary>
        public class ResultInfo
        {
            public int CharaId;
            public int DressId;
            public string Motion;
            public string Face;
            public float Scale;
        }

        /// <summary>
        /// ランタイムで使うクレーンゲーム情報 (TempDataで保持される)
        /// </summary>
        public class CraneGameResultData
        {
            public ResultState ResultState { get; }
            public ResultInfo[] ResultArray { get; }

            public CraneGameResultData(ResultState state, ResultInfo[] array)
            {
                ResultState = state;
                ResultArray = array;
            }
        }
        #endregion データクラス

        #region TempDataに保存
        
        /// <summary>
        /// クレーンゲーム景品情報を外部からTempDataに設定する
        /// </summary>
        public static void SetCraneGameResultData(CraneGamePrizeSetting craneGamePrizeIndex, int craneGamePrizeCount, int craneGameBigPrizeCount,bool isGalleryMode = false)
        {
            int count = 0;
            int bigCount = 0;

            switch (craneGamePrizeIndex)
            {
                // 個数固定 (4パターン)
                // 巨大ぬいぐるみを含む場合は1個とする
                case CraneGamePrizeSetting.Success:
                    count = CRANE_GAME_PRIZE_SUCCESS;
                    break;
                case CraneGamePrizeSetting.SuccessBig:
                    count = CRANE_GAME_PRIZE_SUCCESS_BIG;
                    bigCount = 1;
                    break;
                case CraneGamePrizeSetting.GreatSuccess:
                    count = CRANE_GAME_PRIZE_GREAT_SUCCESS;
                    break;
                case CraneGamePrizeSetting.GreatSuccessBig:
                    count = CRANE_GAME_PRIZE_GREAT_SUCCESS_BIG;
                    bigCount = 1;
                    break;

                // 自由入力
                case CraneGamePrizeSetting.FreeInput:
                    count = craneGamePrizeCount + craneGameBigPrizeCount;
                    bigCount = craneGameBigPrizeCount;
                    if (count == 0)
                    {
                        // 0個の場合は失敗とする
                        SetCraneGameResult(null);
                        return;
                    }
                    break;

                default:
                    // 未設定は失敗とする
                    SetCraneGameResult(null);
                    return;
            }

            var array = new PrizeInfo[count];
            
            //ギャラリー用なら見た目を気にしてランダムにしておく
            if (isGalleryMode)
            {
                //キャラのIDリスト
                var charaDictValues = MasterDataManager.Instance.masterCharaData.GetDictInTerm().Values;
                List<int> charaIdList = new List<int>();
                foreach (var charaData in charaDictValues)
                {
                    //通常キャラのみ抽出
                    if (charaData.CharaCategory == 0)
                    {
                        charaIdList.Add(charaData.Id);
                    }
                }
                //表情リスト構築
                var fixFaceList = MasterDataManager.Instance.masterCraneGamePrizeFace.dictionary.Values.OrderBy(d => d.Id).ToList();
                
                for (int i = 0; i < count; i++)
                {
                    //キャラID抽選
                    int randomIndex = UnityEngine.Random.Range(0, charaIdList.Count -1);
                    int charaId = 0;
                    if (charaIdList != null)
                    {
                        charaId = charaIdList[randomIndex];
                    }
                    // 設定された個数のダミーデータを用意
                    var prizeIdSet = new CharaDressIdSet(charaId, PRIZE_MODEL_DRESS_ID);
                    bool isBig = i < bigCount;
                    string motionName = PRIZE_MODEL_MOTION;
                    if (isBig)
                    {
                        motionName = PRIZE_MODEL_MOTION_BIG;
                    }
                    //表情ID抽選
                    int faceIndex = UnityEngine.Random.Range(0, fixFaceList.Count -1);
                    var face = fixFaceList[faceIndex].FaceType;
                    array[i] = new PrizeInfo(prizeIdSet, face, motionName, isBig);
                }   
            }
            else
            {
                //ギャラリーじゃなければ(今はEditorのデバッグのみなので)ダミーデータで十分
                // 設定された個数のダミーデータを用意
                var prizeIdSet = new CharaDressIdSet(PRIZE_MODEL_CHARA_ID, PRIZE_MODEL_DRESS_ID);
                for (int i = 0; i < count; i++)
                {
                    bool isBig = i < bigCount;
                    array[i] = new PrizeInfo(prizeIdSet, PRIZE_MODEL_FACE, PRIZE_MODEL_MOTION, isBig);
                }   
            }

            var saveArray = new CraneGameInfo.PrizeInfoSave[array.Length];
            for(int i = 0; i< array.Length; i++)
            {
                saveArray[i] = new CraneGameInfo.PrizeInfoSave(array[i]);
            }

            var result = CraneGameDefines.GetResult(array.ToList());
            SetCraneGameResult(CraneGameDefines.CreateServerResultData(result, saveArray));
        }
        
        /// <summary>
        /// サーバから受け取ったクレーンゲーム結果情報をTempDataに保存する
        /// </summary>
        public static void SetCraneGameResult(MinigameResult result)
        {
            if (result == null)
            {
                SetCraneGameResult(ResultState.Failed, null);
                return;
            }

            var state = (ResultState)result.result_state;
            var array = CreateCraneGameResultArray(state, result.result_detail_array);

            SetCraneGameResult(state, array);
        }

        /// <summary>
        /// クレーンゲーム結果情報をTempDataに保存する
        /// </summary>
        private static void SetCraneGameResult(ResultState state, ResultInfo[] array)
        {
            if (TempData.HasInstance())
            {
                var data = new CraneGameResultData(state, array);
                TempData.Instance.SingleModeData.SetCraneGameResult(data);
            }
        }

        /// <summary>
        /// クレーンゲーム詳細をもとに表示用の配列を作成する
        /// </summary>
        private static ResultInfo[] CreateCraneGameResultArray(ResultState state, MinigameResultDetail[] detailArray)
        {
            bool isBigPrize = false;

            switch (state)
            {
                case ResultState.Success:
                case ResultState.GreatSuccess:
                    break;
                case ResultState.SuccessWithBig:
                case ResultState.GreatSuccessWithBig:
                    isBigPrize = true;
                    break;
                default:
                    // 失敗の場合はnullを返す
                    return null;
            }

            int length = state switch
            {
                ResultState.Success => CRANE_GAME_PRIZE_SUCCESS,
                ResultState.SuccessWithBig => CRANE_GAME_PRIZE_SUCCESS_BIG,
                ResultState.GreatSuccess => CRANE_GAME_PRIZE_GREAT_SUCCESS,
                ResultState.GreatSuccessWithBig => CRANE_GAME_PRIZE_GREAT_SUCCESS_BIG,
                _ => 0,
            };

            length = UnityEngine.Mathf.Min(length,detailArray.Length);

            // 表示順であるget_idで昇順ソート
            System.Array.Sort(detailArray, (x, y) => x.get_id - y.get_id);
            
            var array = new ResultInfo[length];
            for (int i = 0; i < length; i++)
            {
                // 巨大ぬいぐるみを表示する場合は先頭だけ大きくする
                bool isBig = i == 0 && isBigPrize;

                var detail = detailArray[i];
                array[i] = new ResultInfo()
                {
                    CharaId = detail.chara_id,
                    DressId = detail.dress_id,
                    Motion = detail.motion,
                    Face  = detail.face,
                    Scale = isBig ? PRIZE_MODEL_SCALE_BIG : PRIZE_MODEL_SCALE,
                };
            }

            return array;
        }

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// クレーンゲームの結果を保存 (StoryTimelineEditor用)
        /// </summary>
        public static void SetCraneGameResultForEditor(ResultInfo[] infoArray)
        {
            if (infoArray == null || infoArray.Length == 0)
            {
                SetCraneGameResult(ResultState.Failed, null);
                return;
            }

            // 先頭のサイズで巨大ぬいぐるみを表示するか判定
            bool isBig = infoArray[0].Scale > PRIZE_MODEL_SCALE;

            // 保存
            var state = GetResultStateForEditor(isBig, infoArray.Length);
            SetCraneGameResult(state, infoArray);
        }

        /// <summary>
        /// 表示数から結果IDを取得する (Editor用に簡略化されたロジック)
        /// </summary>
        private static ResultState GetResultStateForEditor(bool isBig, int count)
        {
            // 巨大ぬいぐるみでも通常サイズでも成功の場合は1個だけ表示する仕様
            // スクリプターが複数のぬいぐるみを指定している場合は大成功と判定する

            if (isBig)
            {
                // 巨大ぬいぐるみを表示する場合
                return count == 1 ? ResultState.SuccessWithBig : ResultState.GreatSuccessWithBig;
            }

            return count == 1 ? ResultState.Success : ResultState.GreatSuccess;
        }

#endif
        #endregion TempDataに保存

        #region クレーンゲーム結果を取得
        /// <summary>
        /// TempDataからクレーンゲーム情報を取得する
        /// </summary>
        public static CraneGameResultData GetCraneGameResult()
        {
            if (TempData.HasInstance())
            {
                return TempData.Instance.SingleModeData.CraneGameResult;
            }

            return null;
        }
        #endregion クレーンゲーム結果を取得

        #region クリア
        /// <summary>
        /// TempDataの結果情報をクリア
        /// </summary>
        public static void ClearCraneGameResult()
        {
            if (TempData.HasInstance())
            {
                TempData.Instance.SingleModeData.ClearCraneGameResult();
            }
        }
        #endregion クリア

        #region Propのモーション情報
        /// <summary>
        /// クレーンゲーム景品を表示する時の小物モーションを取得する
        /// </summary>
        /// <remarks>
        /// クレーンゲームの景品はミニキャラのモデルであり
        /// そのモデルの配置場所を指定するのがPropの役割
        /// モーションを指定することで配置場所の座標が変化するようになっている
        /// </remarks>
        public static void GetCraneGamePrizePropMotion(ResultState state, out string motionName, out bool motionEnabled)
        {
            switch (state)
            {
                case ResultState.GreatSuccess:
                    motionName = CRANE_GAME_PROP_MOTION_GREAT_SUCCESS;
                    motionEnabled = true;
                    break;

                case ResultState.GreatSuccessWithBig:
                    motionName = CRANE_GAME_PROP_MOTION_GREAT_SUCCESS_BIG;
                    motionEnabled = true;
                    break;

                default:
                    // 表示数が1体になるケースではモーションを利用しない
                    motionName = string.Empty;
                    motionEnabled = false;
                    break;
            }
        }
        #endregion Propのモーション情報
    }
}
