#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
using System;

namespace Gallop.StoryTimeline
{
    class ScaleTransformGUI
    {
        private bool _isOpen = true;

        private readonly string FROM_PROP_NAME;
        private readonly string TO_PROP_NAME;

        public ScaleTransformGUI(string propertyName, bool isOpen)
        {
            FROM_PROP_NAME = $"{propertyName}.from";
            TO_PROP_NAME = $"{propertyName}.to";

            _isOpen = isOpen;
        }

        public float DrawProperty(float posY, string title, SerializedObject obj, float minScale, Action onFromValueChanged = null, Action onToValueChanged = null)
        {
            posY = StoryTimelineEditorPropertyWindow.AddFoldout(posY, title, ref _isOpen);
            if (_isOpen)
            {
                posY = StoryStillGUI.AddFloatInputField(posY, obj, FROM_PROP_NAME, "From", onFromValueChanged, minValue: minScale);
                posY = StoryStillGUI.AddFloatInputField(posY, obj, TO_PROP_NAME, "To", onToValueChanged, minValue: minScale);
            }

            return posY;
        }
    }
}
#endif
