using System;

#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// StoryTimelineで小物のカラー関連を上書きするデータクラス
    /// </summary>
    [Serializable]
    public class StoryTimelinePropColorOverrideData : ICloneable
    {
        public StoryTimelinePropFillColorData FillColorData = new();
        public StoryTimelinePropOutlineData OutlineData = new();

        public object Clone()
        {
            var data = new StoryTimelinePropColorOverrideData();
            data.FillColorData.CopyFrom(FillColorData);
            data.OutlineData.CopyFrom(OutlineData);
            return data;
        }

        public void Apply(StoryCharaPropController propController, float normalizedTime)
        {
            FillColorData.Apply(propController, normalizedTime);
            OutlineData.Apply(propController, normalizedTime);
        }

#if UNITY_EDITOR && CYG_DEBUG
        private bool _isFoldoutOpen;

        public void DrawProperty(ref float posY)
        {
            posY = StoryTimelineEditorPropertyWindow.AddFoldout(posY, "小物の色を上書き", ref _isFoldoutOpen);
            if (!_isFoldoutOpen) return;

            EditorGUI.indentLevel++;
            {
                FillColorData.DrawProperty(ref posY);
                OutlineData.DrawProperty(ref posY);
            }
            EditorGUI.indentLevel--;
        }
#endif
    }
}
