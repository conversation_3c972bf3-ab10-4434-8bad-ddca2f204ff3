#if UNITY_EDITOR && CYG_DEBUG
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// プロップのオフセットのプリセットをコピーする機能のエディタウィンドウ
    /// </summary>
    public class StoryCharaPropOffsetPresetCopyAndPasteEditorWindow : EditorWindow
    {
        private StoryCharaPropOffsetPresetCopyAndPaste _model;

        private int _sourceCharacterSelectPopupIndex;
        private PropOffsetIdentifier _sourcePropOffsetIdentifier;
        private PropOffset _sourcePropOffset;

        private int _destCharacterSelectPopupIndex;

        /// <summary>
        /// popupのフィルター
        /// </summary>
        private Dictionary<string, string> _filterTable = new Dictionary<string, string>();

        private bool _isFoldSourceProp;

        public static void OpenWindow()
        {
            GetWindow<StoryCharaPropOffsetPresetCopyAndPasteEditorWindow>();
        }

        public static void CloseWindow()
        {
            var window = GetWindow<StoryCharaPropOffsetPresetCopyAndPasteEditorWindow>();
            window.Close();
        }

        private void OnEnable()
        {
            _model = new StoryCharaPropOffsetPresetCopyAndPaste();
        }
        
        private void OnGUI()
        {
            SelectCopySourceGUI();
            DrawUILine();

            if (_sourcePropOffset == null)
            {
                EditorGUILayout.LabelField("コピー元のプリセットを選択してください。");
                return;
            }
            
            CopyAndPasteGUI();
        }

        private void SelectCopySourceGUI()
        {
            SelectPopupGUI("コピー元のキャラクター選択", _sourceCharacterSelectPopupIndex, _model.GetAllCharacterName(),
                (index, popup) =>
                {
                    _sourceCharacterSelectPopupIndex = index;
                    _sourcePropOffset = null;
                    _sourcePropOffsetIdentifier = null;
                    _isFoldSourceProp = false;
                }
            );
            
            var charaId = _model.GetCharaData(_sourceCharacterSelectPopupIndex).CharaId;
            var hasPropData = _model.HasPropOffset(charaId);
            using (new EditorGUI.DisabledScope(!hasPropData))
            {
                if (GUILayout.Button("コピー元のプロップを選択する"))
                {
                    var window = CreateInstance<CharaPropOffsetTableViewEditorWindow>();
                    window.ShowWindow(charaId, _model.GetCharacterPropNameDataSet(), _model.GetPropOffsetDataSet(), (element) =>
                    {
                        _sourcePropOffset = element.PropOffset;
                        _sourcePropOffsetIdentifier = element.PropOffsetIdentifier;
                    });
                }
            }
            if (!hasPropData)
            {
                EditorGUILayout.LabelField("プロップのデータが1つもありません。\n新規作成する場合、StoryTimelineEditorのプロップのクリップから作成する必要があります。", GUILayout.Height(EditorGUIUtility.singleLineHeight * 2 + EditorGUIUtility.standardVerticalSpacing));
            }
        }

        private void CopyAndPasteGUI()
        {
            EditorGUILayout.LabelField("コピーする");
            var propName = _model.GetCharacterPropName(_sourcePropOffsetIdentifier.PropIdentifier);
            _isFoldSourceProp = EditorGUILayout.Foldout(_isFoldSourceProp, $"選択中のプロップ ID:{_sourcePropOffsetIdentifier.PropIdentifier.PropMainId}_{_sourcePropOffsetIdentifier.PropIdentifier.PropSubId}:{propName}", true);
            using (new EditorGUI.IndentLevelScope())
            {
                if (_isFoldSourceProp)
                {
                    EditorGUILayout.LabelField($"使用するシーン:{_sourcePropOffsetIdentifier.UseSceneType.GetLabel()}");
                    EditorGUILayout.LabelField($"アタッチ先:{(StoryTimelineCharaPropClipData.PropTarget)_sourcePropOffsetIdentifier.AttachBone}");
                    EditorGUILayout.LabelField($"頭差分ID:{_sourcePropOffsetIdentifier.HeadSubId}");
                    EditorGUILayout.LabelField($"衣装差分ID:{_sourcePropOffsetIdentifier.DressId}");
                    EditorGUILayout.LabelField($"移動オフセット:{_sourcePropOffset.GetPositionString()}");
                    EditorGUILayout.LabelField($"回転オフセット:{_sourcePropOffset.GetRotationString()}");
                    EditorGUILayout.LabelField($"スケールオフセット:{_sourcePropOffset.GetScaleString()}");
                }
            }

            using (new EditorGUI.DisabledScope(_sourcePropOffsetIdentifier == null))
            {
                if (GUILayout.Button("全てのキャラにコピー"))
                {
                    if (EditorUtility.DisplayDialog("確認", "全てのキャラにプリセットをコピーしますか？\n(既にプリセットが存在する場合はコピーされません)", "OK", "キャンセル"))
                    {
                        _model.CopyToAllCharacter(_sourcePropOffsetIdentifier, _sourcePropOffset, false);
                    }
                }
                if (GUILayout.Button("全てのキャラにコピー(強制)"))
                {
                    if (EditorUtility.DisplayDialog("確認", "全てのキャラにプリセットをコピーしますか？\n(既にプリセットが存在する場合は上書きされます)", "OK", "キャンセル"))
                    {
                        _model.CopyToAllCharacter(_sourcePropOffsetIdentifier, _sourcePropOffset, true);
                    }
                }
                
                SelectPopupGUI("コピー先のキャラクター選択", _destCharacterSelectPopupIndex, _model.GetAllCharacterName(),
                    (index, popup) =>
                    {
                        _destCharacterSelectPopupIndex = index;
                    }
                );

                var destCharacter = _model.GetCharaData(_destCharacterSelectPopupIndex);
                var result = _model.IsValidDestCharacter(_sourcePropOffsetIdentifier, destCharacter);
                var resultLabel = DrawResult(result);
                if (!string.IsNullOrEmpty(resultLabel))
                {
                    EditorGUILayout.LabelField(resultLabel);
                }
                using (new EditorGUI.DisabledScope(result != StoryCharaPropOffsetPresetCopyAndPaste.Result.Valid))
                {
                    if (GUILayout.Button("選択したキャラクターにのみコピー"))
                    {
                        if (EditorUtility.DisplayDialog("確認", $"{_model.GetCharacterName(destCharacter.CharaId)}にプリセットをコピーしますか？\n(既にプリセットが存在する場合は上書きされます)", "OK", "キャンセル"))
                        {
                            var destCharaId = destCharacter.CharaId;
                            _model.CopyToCharacter(_sourcePropOffsetIdentifier, _sourcePropOffset, destCharaId);
                        }
                    }
                }
            }
        }
        
        private string DrawResult(StoryCharaPropOffsetPresetCopyAndPaste.Result result)
        {
            string ret = "";

            switch (result)
            {
                case StoryCharaPropOffsetPresetCopyAndPaste.Result.PropNotFound:
                    ret = "存在しないプロップです";
                    break;
                
                case StoryCharaPropOffsetPresetCopyAndPaste.Result.NotIncludeHead:
                    ret = "対応する頭差分が存在しません";
                    break;
                
                case StoryCharaPropOffsetPresetCopyAndPaste.Result.NotIncludeDress:
                    ret = "対応する衣装差分が存在しません";
                    break;
            }
            return ret;
        }

        private void DrawUILine(int height = 1)
        {
            GUILayout.Space(4);
 
            Rect rect = GUILayoutUtility.GetRect(10, height, GUILayout.ExpandWidth(true));
            rect.height = height;
            rect.xMin = 0;
            rect.xMax = EditorGUIUtility.currentViewWidth;
 
            Color lineColor = new Color(0.10196f, 0.10196f, 0.10196f, 1);
            EditorGUI.DrawRect(rect, lineColor);
            GUILayout.Space(4);
        }
        
        private void SelectPopupGUI(string label, int selectIndex, string[] menuList,
            Action<int, CharaViewerPopup> action,
            GUIStyle labelStyle = null,
            bool isAllowEmptySelected = false,
            bool isUseLabelField = true,
            bool isButtonRed = false,
            int buttonWidthSize = -1,
            float popUpRectWidth = 200f,
            bool isWithAction = false)
        {
            if (isUseLabelField)
            {
                EditorGUILayout.LabelField(label, labelStyle == null ? GUI.skin.label : labelStyle);
            }
            else
            {
                // GetLastRectで有効な値を返すためにWidth0で実行する。
                // (ボタンがほんの少し右にずれてしまうのが気になるので何とかしたい)
                EditorGUILayout.LabelField(string.Empty, GUILayout.Width(0));
            }
            var rect = GUILayoutUtility.GetLastRect();

            var buttonStyle = new GUIStyle(GUI.skin.button);

            // 選択行のボタンのテキストを赤くする。
            // メンバ変数に定義するとなぜか正常に動作しないので直接的な値として処理
            if (isButtonRed)
            {
                buttonStyle.active.textColor = Color.red;
                buttonStyle.normal.textColor = Color.red;
                buttonStyle.focused.textColor = Color.red;
            }

            string btnText = (selectIndex < 0) ? "--- 未選択 ---" : menuList[selectIndex];
            List<GUILayoutOption> options = new List<GUILayoutOption>();
            if (buttonWidthSize > 0)
            {
                options.Add(GUILayout.Width(buttonWidthSize));
            }

            if (GUILayout.Button(btnText, buttonStyle, options.ToArray()))
            {
                var popUpRect = rect;
                popUpRect.x += popUpRect.width;
                popUpRect.y += popUpRect.height - GUI.skin.button.CalcSize(new GUIContent("")).y;
                popUpRect.width = popUpRectWidth;

                var size = Vector2.zero;
                size.x = popUpRect.width;

                CharaViewerPopup popUp = new CharaViewerPopup();
                popUp.SetLabel(label);
                popUp.SetFilterDictionary(_filterTable);
                popUp.Initialize(menuList,
                    selectIndex,
                    size,
                    action,
                    this,
                    () => { },
                    isAllowEmptySelected: isAllowEmptySelected,
                    isWithAction: isWithAction);
                PopupWindow.Show(popUpRect, popUp);
            }
        }
    }
}
#endif
