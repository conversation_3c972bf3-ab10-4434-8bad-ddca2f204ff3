using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using static Gallop.StaticVariableDefine.Story.StoryLiveStreaming;
#if CYG_DEBUG
using UnityEngine.UI;
#endif    

namespace Gallop
{ 
    /// <summary>
    /// 配信風フィルタのコントローラ
    /// </summary>
    public class TimelineLiveStreamingController : MonoBehaviour
    {
        #region 定数
        
        /// <summary> Flashに設定するNPC用Spriteの名前 </summary>
        private const string NPC_SPRITE_NAME = "utx_ico_account_npc_{0:00}";
        /// <summary> Flashに設定するユーザー用Spriteの名前 </summary>
        private const string USER_SPRITE_NAME = "utx_ico_account_user_{0:00}";
        /// <summary> NPC用コメントのSpriteの数 </summary>
        private const int NPC_COMMENT_COUNT = 9;
        /// <summary> コメントパターンIDの初期値 </summary>
        private const int INITIALIZE_COMMENT_PATTERN_ID = 1;

        #endregion

        #region Enum

        /// <summary> タップ時のFlashの種類 </summary>
        private enum TapFlashType
        {
            Heart = 0,  // ハート
            Carrot = 1, // にんじん
        }

        #endregion
        
        #region 変数
        
        /// <summary> フィルターの表示を許可するフラグ </summary>
        public bool IsEnable => _isEnable;
        private bool _isEnable = false;
        
        /// <summary>
        /// コメントのパターンリスト
        /// </summary>
        private Dictionary<int, IReadOnlyList<string>> _commentPatternDictionary = new Dictionary<int, IReadOnlyList<string>>();
        
        /// <summary> 現在のアカウントの設定データ </summary>
        public LiveStreamingFilterAccountSettingData CurrentAccountSettingData { get; private set; }
        
        /// <summary> 配信者のアカウントアイコン </summary>
        [SerializeField]
        private RawImageCommon _accountIconRawImage;
        
        /// <summary> 配信者のアカウント名 </summary>
        [SerializeField]
        private TextCommon _accountNameText;
        
        /// <summary> 同時接続者数の表示テキスト </summary>
        [SerializeField]
        private TextCommon _viewerCountText;
        
        /// <summary> ユーザーが押すことが可能なハート型のボタン </summary>
        [SerializeField]
        private ButtonCommon _heartButton;
        
        /// <summary> ユーザーがボタンを押すと流れるスタンプFlashのPrefab配列 </summary>
        /// <remarks>
        /// <see cref="TapFlashType"/>と同じ数だけ要素が必要で、同じ順番で要素を格納する必要がある
        /// </remarks>
        [SerializeField]
        private GameObject[] _tapFlashObjArray;
        
        /// <summary> 常時流れているハート(にんじん)スタンプParticleのPrefab配列 </summary>
        /// <remarks>
        /// <see cref="StoryTimelineLiveStreamingClipData.ExcitementLevel"/>と同じ数だけ要素が必要で、同じ順番で要素を格納する必要がある(<see cref="StoryTimelineLiveStreamingClipData.ExcitementLevel.Zero"/>は除く)
        /// </remarks>
        [SerializeField]
        private GameObject[] _stampParticleObjArray;

        /// <summary> コメント欄のRootオブジェクト </summary>
        [SerializeField] 
        private GameObject _commentRootObj;
        
        /// <summary> コメントのFlashPrefabの配列 </summary>
        /// <remarks>
        /// <see cref="LiveStreamingComment.CommentType"/>と同じ数だけ要素が必要で、同じ順番で要素を格納する必要がある
        /// </remarks>
        [SerializeField]
        private GameObject[] _commentFlashObjArray;

        /// <summary> 横画面ストーリーの縦画面時の処理用に設定するRectTransform </summary>
        [SerializeField]
        private RectTransform[] _settingParentRectTransformArray;

        /// <summary> プレハブに初期設定されているUIの位置を保存 </summary>
        private List<Vector3> _initUiPos = new List<Vector3>();

        /// <summary> 現在のコメントパターンID </summary>
        /// <remarks> 初期値は1を想定している </remarks>
        private int _currentCommentPatternId = INITIALIZE_COMMENT_PATTERN_ID;
        
        /// <summary> 現在の盛り上がり度 </summary>
        private StoryTimelineLiveStreamingClipData.ExcitementLevel _currentExcitementLevel = StoryTimelineLiveStreamingClipData.ExcitementLevel.Middle;
        
        /// <summary> LiveStreamingCommentのリスト </summary>
        private readonly LinkedList<LiveStreamingComment> _liveStreamingCommentList = new LinkedList<LiveStreamingComment>();
        
        /// <summary> ユーザーがボタンを押すと流れるスタンプFlashの内部リスト </summary>
        private readonly List<FlashPlayer> _internalTapFlashList = new List<FlashPlayer>();
        
        /// <summary> 常時流れているハート(にんじん)Particleの内部リスト </summary>
        private readonly List<StampParticleParent> _internalStampParticleList = new List<StampParticleParent>();

        /// <summary> コメントのスクロール中かどうか </summary>
        private bool _isScrollingComment = false;
        
        /// <summary> 前回のスクロール時のフレーム数 </summary>
        private int _prevScrollFrameCount = 0;
        
        /// <summary> コメントのスクロール間隔 </summary>
        private int _frameCountInterval = 30;
        
        /// <summary> コントローラ内部でタップ待ちをしているかどうか </summary>
        private bool _isWaiting = false;
        
        /// <summary> 優先コメントリスト </summary>
        private List<string> _priorityCommentList = new List<string>();
        
        /// <summary> ユーザーコメントリスト </summary>
        private readonly Queue<string> _userCommentQueue = new ();

#if CYG_DEBUG
        private int _currentSeedOffset = 0;
#endif        
        
        /// <summary> ストーリー再生時に再現性を持たせるための乱数生成器 </summary>
        private System.Random _random = null;
        
        /// <summary> 乱数のシード値 </summary>
        private int _randomSeed = 0;
        
        #endregion

        #region クラス

        /// <summary>
        /// スタンプParticleをまとめたクラス
        /// </summary>
        private class StampParticleParent
        {
            private ParticleSystem[] _childParticleArray; // スタンプParticle

            /// <summary>
            /// particleのスケールを変更
            /// </summary>
            /// <remarks>すでに表示しているparticleには影響しないので注意</remarks>
            public void UpdateParticleScale(float scale)
            {
                foreach (var particleSystem in _childParticleArray)
                {
                    var particleTransform = particleSystem.transform;
                    var currentScale = particleTransform.localScale;
                    currentScale.x = scale;
                    currentScale.y = scale;
                    particleTransform.localScale = currentScale;
                }
            }

            /// <summary>
            /// コンストラクタ
            /// </summary>
            public StampParticleParent(ParticleSystem[] stampParticleArray)
            {
                _childParticleArray = stampParticleArray;
            }
            /// <summary> デフォルトコンストラクタは公開しない </summary>
            private StampParticleParent() { }
            
            /// <summary>
            /// 子のパーティクルをまとめて再生
            /// </summary>
            public void Play()
            {
                foreach (var particleSystem in _childParticleArray)
                {
                    particleSystem.Play();
                }
            }
            
            /// <summary>
            /// 子のパーティクルをまとめて停止
            /// </summary>
            public void Stop()
            {
                foreach (var particleSystem in _childParticleArray)
                {
                    particleSystem.Stop();
                }
            }
            
            /// <summary>
            /// 子のパーティクルをまとめて一時停止
            /// </summary>
            public void Pause()
            {
                foreach (var particleSystem in _childParticleArray)
                {
                    particleSystem.Pause();
                }
            }
        }
        
        #endregion
        
        #region 関数

        #region フィルターの設定

        /// <summary>
        /// 配信しているアカウントの設定変更
        /// </summary>
        /// <param name="accountSettingData">変更後のアカウント設定データ</param>
        public void ChangeAccount(LiveStreamingFilterAccountSettingData accountSettingData)
        {
            CurrentAccountSettingData = accountSettingData;
            _accountIconRawImage.texture = accountSettingData.Icon;
            _accountNameText.text = accountSettingData.Name;
        }
        
        /// <summary>
        /// コメントのパターンリストを作成
        /// </summary>
        /// <param name="commentList">パターンリスト</param>
        public void CreateDictionary(IReadOnlyList<CommentSetting> commentList)
        {
            var count = commentList.Count;
            for (int i = 0; i < count; i++)
            {
                _commentPatternDictionary.Add(commentList[i].CommentPattern_ID, commentList[i].ReadOnlyCommentList);
            }
        }

        #endregion
        
        #region コメント欄

        /// <summary>
        /// ユーザーコメントのQueueにコメントを渡す
        /// </summary>
        /// <param name="comment">Enqueueするコメント</param>
        public void EnqueueUserComment(string comment)
        {
            _userCommentQueue.Enqueue(comment);
        }
        
        /// <summary>
        /// コメントの速度変更
        /// </summary>
        private void ChangeCommentSpeed(StoryTimelineLiveStreamingClipData.ExcitementLevel excitementLevel)
        {
            const int FRAME_COUNT_INTERVAL_MIDDLE = 30;
            const int FRAME_COUNT_INTERVAL_HIGH = 15;
            const int FRAME_COUNT_INTERVAL_VERY_HIGH = 5;
            // 盛り上がり度によってコメントのスクロール間隔を変更
            _frameCountInterval = excitementLevel switch
            {
                StoryTimelineLiveStreamingClipData.ExcitementLevel.Middle => FRAME_COUNT_INTERVAL_MIDDLE,
                StoryTimelineLiveStreamingClipData.ExcitementLevel.High => FRAME_COUNT_INTERVAL_HIGH,
                StoryTimelineLiveStreamingClipData.ExcitementLevel.VeryHigh => FRAME_COUNT_INTERVAL_VERY_HIGH,
                _ => FRAME_COUNT_INTERVAL_MIDDLE,
            };
        }

        /// <summary>
        /// コメントの速度をランダムに変更する
        /// </summary>
        private void RandomizeCommentSpeed(StoryTimelineLiveStreamingClipData.ExcitementLevel excitementLevel)
        {
            const int RANDOMIZE_INTERVAL_PERCENT = 21;
            const int DELAY_PROBABILITY_PERCENTAGE = 5;
            const int DELAY_FRAME_COUNT_INTERVAL = 20;
            ChangeCommentSpeed(excitementLevel);
            // コメントのスクロール間隔を一定範囲で乱数を振って変更
            _frameCountInterval = (int)(_frameCountInterval * (1 + _random.Next(- RANDOMIZE_INTERVAL_PERCENT, RANDOMIZE_INTERVAL_PERCENT) / 100));
            // コメント最高速のみ確率で固定フレームの遅延を入れる
            var delayVeryHighSpeedComment = _currentExcitementLevel == StoryTimelineLiveStreamingClipData.ExcitementLevel.VeryHigh && _random.Next(0, 100) < DELAY_PROBABILITY_PERCENTAGE;
            if (delayVeryHighSpeedComment)
            {
                _frameCountInterval += DELAY_FRAME_COUNT_INTERVAL;
            }
        }
        
        /// <summary>
        /// コメントの内容リストの切り替え
        /// </summary>
        private void ChangeCommentPattern(int commentPatternId)
        {
            _currentCommentPatternId = commentPatternId;
        }
        
        /// <summary>
        /// コメントの抽選処理
        /// </summary>
        private string LotteryComment()
        {
            //Dictionaryが万が一無ければ、空文字にしてゲームを止めないようにする
            if (_commentPatternDictionary.IsNullOrEmpty() || !_commentPatternDictionary.ContainsKey(_currentCommentPatternId))
            {
                return "";
            }
            //あれば抽選
            var commentList = _commentPatternDictionary[_currentCommentPatternId];
            return commentList[_random.Next(0, commentList.Count)];
        }

        /// <summary>
        /// コメントのアイコン抽選処理
        /// </summary>
        private bool TryLotteryCommentIcon(bool isUserComment, out Sprite commentIcon)
        {
            commentIcon = null;
            if (StoryManager.StoryAtlas == null)
            {
                return false;
            }
            commentIcon = StoryManager.StoryAtlas.GetSprite(GetSpriteName(isUserComment, isUserComment ? 0 : _random.Next(0, NPC_COMMENT_COUNT)));
            return commentIcon != null;
        }

        /// <summary>
        /// コメントの移動処理
        /// </summary>
        private void ScrollComment()
        {
            const float SCROLL_DURATION = 0.133f;
            
            // すでにスクロール中なら何もしない
            if(_isScrollingComment) return;
            
            _isScrollingComment = true;
            // 最後の要素を取得
            var lastElement = _liveStreamingCommentList.Last;
            foreach (var liveStreamingComment in _liveStreamingCommentList)
            {
                if (liveStreamingComment == lastElement.Value)
                {
                    // リストの一番最後の要素は見えないようにラベルを変更し、初期位置に戻す
                    liveStreamingComment.PopUp();
                }
                else if (liveStreamingComment == lastElement.Previous.Value) 
                {
                    // 最後から2番目の要素は半透明表示になる、スクロール演出の最後なので終了したらフラグを戻す
                    liveStreamingComment.FadeOut(SCROLL_DURATION, () =>
                    {
                        _isScrollingComment = false;
                    });
                }
                else if (liveStreamingComment == _liveStreamingCommentList.First.Value)
                {
                    // 一番最初の要素は新しいコメント情報を設定して表示
                    var (selectedComment, commentType, commentIcon, iconExist) = ChoiceCommentInfo();
                    liveStreamingComment.SetComment(selectedComment, commentType, commentIcon, iconExist);
                    
                    // フェードインしながら表示
                    liveStreamingComment.FadeIn(SCROLL_DURATION);
                }
                else
                {
                    // それ以外の要素は移動処理のみ
                    liveStreamingComment.Scroll(SCROLL_DURATION);
                }
            }
            // 一番最後の要素を一番最初の要素にする
            _liveStreamingCommentList.RemoveLast();
            _liveStreamingCommentList.AddFirst(lastElement);
            
            // コメントのスクロールが終了したら次のスクロールまでのフレーム数を設定
            RandomizeCommentSpeed(_currentExcitementLevel);
        }

        /// <summary>
        /// コメントの表示に必要な情報を抽選する
        /// </summary>
        private (string, LiveStreamingComment.CommentType, Sprite, bool) ChoiceCommentInfo()
        {
            // コメントの内容を選択
            ChoiceComment(out var selectedComment, out var commentType);
            
            // コメントのアイコンを抽選
            var iconExist = TryLotteryCommentIcon(commentType == LiveStreamingComment.CommentType.UserComment, out var commentIcon);

            return (selectedComment, commentType, commentIcon, iconExist);
        }

        /// <summary>
        /// コメントの内容を選択
        /// </summary>
        private void ChoiceComment(out string comment, out LiveStreamingComment.CommentType commentType)
        {
            // ユーザーコメントの場合はリストの前からコメントを取得
            if (_userCommentQueue.Count > 0)
            {
                comment = _userCommentQueue.Dequeue();
                commentType = LiveStreamingComment.CommentType.UserComment;
            }
            // 優先コメントの場合はリストの前からコメントを取得、改行の有無で1行か2行かを判定
            else if (_priorityCommentList.Count > 0)
            {
                comment = _priorityCommentList[0];
                _priorityCommentList.RemoveAt(0);
                comment = TextUtil.ReplaceNewLine(comment); // 改行の置き換え
                commentType = comment.Contains("\n") ? LiveStreamingComment.CommentType.NPCCommentTwoLine : LiveStreamingComment.CommentType.NPCCommentOneLine;
            }
            // 通常NPCコメントの場合は抽選してコメントを取得
            else
            {
                comment = LotteryComment();
                comment = TextUtil.ReplaceNewLine(comment); // 改行の置き換え
                commentType = comment.Contains("\n") ? LiveStreamingComment.CommentType.NPCCommentTwoLine : LiveStreamingComment.CommentType.NPCCommentOneLine;
            }
        }

        /// <summary>
        /// Flashにセットするスプライトの名前を取得
        /// </summary>
        /// <returns></returns>
        private string GetSpriteName(bool isUserComment, int index = 0)
        {
            return isUserComment ? TextUtil.Format(USER_SPRITE_NAME, index) : TextUtil.Format(NPC_SPRITE_NAME, index);
        }
        
        #endregion
        
        #region スタンプ関連

        /// <summary>
        /// 常設スタンプパーティクルの再生
        /// </summary>
        private void PlayStampParticle(StoryTimelineLiveStreamingClipData.ExcitementLevel excitementLevel)
        {
            // 盛り上がり度によって放出するパーティクルを変更
            for (int i = 0; i < _internalStampParticleList.Count; i++)
            {
                // (int)excitementLevel==0(excitementLevel.Zero)は全部停止する想定
                if (i + 1 == (int)excitementLevel)
                {
                    _internalStampParticleList[i].Play();
                }
                else
                {
                    _internalStampParticleList[i].Stop();
                }
            }
        }
        
        /// <summary>
        /// 常設スタンプParticleのスケールを更新
        /// </summary>
        private void UpdateStampParticleScale()
        {
            float particleScale = GetParticleScale(StoryTimelineController.CurrentDisplayMode, Screen.Height, Screen.Width);
            foreach (var stampParticleParent in _internalStampParticleList)
            {
                stampParticleParent.UpdateParticleScale(particleScale);
            }
        }

        /// <summary>
        /// particleのスケールを取得
        /// </summary>
        /// <returns></returns>
        private float GetParticleScale(StoryTimelineController.DisplayMode targetDisplayMode, float screenHeight, float screenWidth)
        {
            // 横画面はスケールは１
            if (targetDisplayMode == StoryTimelineController.DisplayMode.Landscape)
            {
                return 1f;
            }

            // 縦画面はアスペクト比によってスケールを変更
            float aspect = screenHeight / screenWidth;
            var scale = GameDefine.BASE_ASPECT_RATIO_LANDSCAPE * GameDefine.BASE_ASPECT_RATIO / aspect;
            return scale;
        }

        /// <summary>
        /// 常設スタンプパーティクルの停止
        /// </summary>
        private void StopStampParticle()
        {
            foreach (var particleParent in _internalStampParticleList)
            {
                particleParent.Stop();
            }
        }
        
        /// <summary>
        /// 常設スタンプパーティクルの一時停止
        /// </summary>
        private void PauseStampParticle()
        {
            foreach (var particleParent in _internalStampParticleList)
            {
                particleParent.Pause();
            }
        }

        /// <summary>
        /// ユーザー用スタンプParticleの生成
        /// </summary>
        private void InstantiateUserParticle()
        {
            const int SORT_OFFSET = 10; //ハートの白フレームより前になる値、間に別のものを挟む可能性を考えて10単位で設定
            for (int i = 0; i < _tapFlashObjArray.Length; i++)
            {
                var tapFlashActionPlayer = Instantiate(_tapFlashObjArray[i], _heartButton.transform)
                    .GetComponent<FlashActionPlayer>();
                // 生成したパーティクルを使いまわす設定
                tapFlashActionPlayer.SetKeepInstanceAll(true);
                var tapFlashPlayer = tapFlashActionPlayer.LoadFlashPlayer();
                // #153044 ハートのタップエフェクトをハートの白フレームの上に表示させるためにSortLayerとSortOffsetを設定
                tapFlashPlayer.SortLayer = UIManager.CANVAS_SORTING_LAYER_UI;
                tapFlashPlayer.SortOffset = SORT_OFFSET;
                tapFlashPlayer.Play(GameDefine.A2U_END_LABEL);

                // パーティクル生成時にスケールを調整（縦画面の場合はスケールを調整）
                tapFlashActionPlayer.SetOnInstantiatedParticle(instantiatedParticleArray =>
                {
                    if (StoryTimelineController.CurrentDisplayMode == StoryTimelineController.DisplayMode.Portrait)
                    {
                        var scale = GetParticleScale(StoryTimelineController.CurrentDisplayMode, Screen.Height, Screen.Width);

                        foreach (var p in instantiatedParticleArray)
                        {
                            var particleTransform = p.transform;
                            var currentScale = particleTransform.localScale;
                            currentScale.x = scale;
                            currentScale.y = scale;
                            particleTransform.localScale = currentScale;
                        }
                    }
                });

                _internalTapFlashList.Add(tapFlashPlayer);
            }
        }
        
        /// <summary>
        /// ユーザー用スタンプFlashの再生
        /// </summary>
        private void PlayOneShotUserParticle()
        {
            // スタンプが再生されるまでボタンを押せないようにする
            _heartButton.enabled = false;
            // 確率で再生するFlashを変更
            const int HEART_FLASH_PROBABILITY_PERCENTAGE = 80;

            // ユーザー操作の乱数は他の乱数生成に影響するのでインスタンスを使用しない
            int index = UnityEngine.Random.Range(0, 100) < HEART_FLASH_PROBABILITY_PERCENTAGE ? (int)TapFlashType.Heart : (int)TapFlashType.Carrot;
            _internalTapFlashList[index].Play(GameDefine.A2U_IN_LABEL, () => _heartButton.enabled = true);
        }

        /// <summary>
        /// パーティクルの放出量変更
        /// </summary>
        public void ChangeStampParticleVolume(StoryTimelineLiveStreamingClipData.ExcitementLevel excitementLevel)
        {
            PlayStampParticle(excitementLevel);
        }

        #endregion

        #region 同時接続者数

        /// <summary>
        /// 同時接続者数の変更
        /// </summary>
        public void SetViewerCount(int viewerCount)
        {
            _viewerCountText.text = FormatViewerCountText(viewerCount);
        }
        
        /// <summary>
        /// 同時接続者数のテキストを整形
        /// </summary>
        /// <returns></returns>
        private string FormatViewerCountText(int viewerCount)
        {
            if (viewerCount< 10000)
            {
                // "そのまま"の形式に変換 （コンマ区切り）
                return TextUtil.Format("{0:N0}", viewerCount);
            }
            else
            {
                // "万単位"の形式に変換
                float result = (float)viewerCount / 10000;
                return TextUtil.Format(TextId.Story647001.Text(), result);
            }
        }

        #endregion

        #region 表示・非表示

        /// <summary>
        /// アタッチした先のオブジェクトを表示・非表示する
        /// </summary>
        /// <param name="isActive">trueだと表示</param>
        private void SetActiveFilter(bool isActive)
        {
            gameObject.SetActive(isActive);
        }

        /// <summary>
        /// フィルタを非表示にする
        /// </summary>
        public void HideFilter()
        {
            // 非表示時はパーティクルの再生を一時停止
            PauseStampParticle();
            SetActiveFilter(false);
        }
        
        /// <summary>
        /// フィルタを表示する
        /// </summary>
        public void ShowFilter()
        {
            SetActiveFilter(true);
            if (!_isWaiting)
            {
                // タップ待ちでない時はパーティクルを再生
                PlayStampParticle(_currentExcitementLevel);
            }
        }

        #endregion
        
        #region 初期化・更新
        
        /// <summary>
        /// 初期化処理
        /// </summary>
        /// <param name="frameCount"></param>
        /// <param name="randomSeed"></param>
        public void Initialize(int frameCount, int randomSeed)
        {
            const int COMMENT_COUNT = 5;
            
            // 乱数の初期化
            _randomSeed = randomSeed;
            _random = new System.Random(_randomSeed);
            
            // コメントに設定するアウトラインカラーの取得
            Color32 outlineColor = ColorPreset.GetOutlineColor(OutlineColorType.LiveStreamingSemiTransparentBlack);
            _prevScrollFrameCount = frameCount;
            for (int i = 0; i < COMMENT_COUNT; i++)
            {
                // コメントの内容を設定
                ChoiceComment(out var comment, out var commentType);
                // コメントのアイコンを抽選
                var iconExist = TryLotteryCommentIcon(commentType == LiveStreamingComment.CommentType.UserComment, out var commentIcon);
                
                // liveStreamingCommentの生成
                var liveStreamingComment = new LiveStreamingComment(_commentFlashObjArray, _commentRootObj.transform, comment, commentType, commentIcon, iconExist, i, COMMENT_COUNT, outlineColor);
                _liveStreamingCommentList.AddLast(liveStreamingComment);
            }
            
            // スタンプ機能の初期化
            _heartButton.onClick.AddListener(PlayOneShotUserParticle);
            for (int i = 0; i < _stampParticleObjArray.Length; i++)
            {
                 var stampParticleArray = Instantiate(_stampParticleObjArray[i], _heartButton.transform).GetComponentsInChildren<ParticleSystem>();
                 // フィルターをオフにしてから再度再生すると全パーティクルが再生されてしまうため、playOnAwakeをオフにしておく
                 foreach (var stampParticle in stampParticleArray)
                 {
                     var mainParticleSystem = stampParticle.main;
                     mainParticleSystem.playOnAwake = false;
                 }
                 _internalStampParticleList.Add(new StampParticleParent(stampParticleArray));
                 // 初期状態は盛り上がり度中のみ再生し、それ以外は停止して盛り上がり度合いの切り替えまで待機
                 // 149844 時点での実装では盛り上がり度合いと配列のインデックスは1ずれているため、インデックスの比較は+1している
                 if (i + 1 != (int)StoryTimelineLiveStreamingClipData.ExcitementLevel.Middle)
                 {
                     _internalStampParticleList[i].Stop();
                 }
            }
            // 常設パーティクルのスケールを更新
            UpdateStampParticleScale();
            // タップ用スタンプFlashの初期化
            InstantiateUserParticle();

            // プレハブに設定されているUI配置の保存と初期化
            if (_initUiPos.Count == 0)
            {
                for (int i = 0; i < _settingParentRectTransformArray.Length; i++)
                {
                    _initUiPos.Add(_settingParentRectTransformArray[i].anchoredPosition);
                }
                SetupUiPreOrientation(StoryTimelineController.CurrentDisplayMode);
            }
        }
        
        /// <summary>
        /// 更新処理
        /// </summary>
        /// <param name="frameCount">現在のフレームカウント</param>
        public void AlterUpdate(int frameCount)
        {
            var hasCommentQueue = _userCommentQueue.Count > 0 || _priorityCommentList.Count > 0;
            // 優先コメントはコメントが流れていない時はすぐに表示、通常コメントのインターバルとは別で動作させる
            if (hasCommentQueue && !_isScrollingComment)
            {
                ScrollComment();
            }
            // 通常コメントはインターバルの間隔ごとにコメントをスクロールさせる、現在の盛り上がり度がゼロの時はコメントを流さない
            if (frameCount - _prevScrollFrameCount >= _frameCountInterval && _currentExcitementLevel != StoryTimelineLiveStreamingClipData.ExcitementLevel.Zero)
            {
                ScrollComment();
                _prevScrollFrameCount = frameCount;
            }
            // タップ待ちの切り替わりタイミングでスタンプの再生停止を制御
            if (_isWaiting != StoryManager.Instance.TimelineController.IsWaiting)
            {
                _isWaiting = StoryManager.Instance.TimelineController.IsWaiting;
                if (StoryManager.Instance.TimelineController.IsWaiting)
                {
                    StopStampParticle();
                }
                else
                {
                    PlayStampParticle(_currentExcitementLevel);
                }
            }
        }
        
        /// <summary>
        /// クリップのパラメータを適用
        /// </summary>
        public void ApplyFromClip(StoryTimelineLiveStreamingClipData liveStreamingClipData)
        {
            // ユーザーのタップ操作によって乱数の呼び出し回数がズレてしまう
            // コメントパターンの切り替わりはこの関数内で処理しているので、このタイミングで乱数リセットして同じコメントが流れるようにする
            // コメントパターンが変わっていなくても乱数がリセットされるため注意
            // 1つの会話内で複数回同じコメントパターンを使用する時に、異なる抽選結果を出すためにクリップからオフセット値を足し合わせるようにしている
            _random = new System.Random(_randomSeed + liveStreamingClipData.SeedOffset);
            
            // フィルタの表示・非表示を切り替え
            _isEnable = liveStreamingClipData.ShowFilter;
            if (_isEnable)
            {
                ShowFilter();
            }
            else
            {
                HideFilter();
            }
            // 同時接続者数をテキストに反映
            SetViewerCount(liveStreamingClipData.ViewerCount);
            // 盛り上がり度を変更
            _currentExcitementLevel = liveStreamingClipData.StreamingExcitementLevel;
            // パーティクルの放出量を変更
            ChangeStampParticleVolume(liveStreamingClipData.StreamingExcitementLevel);
            // コメントの速度変更
            ChangeCommentSpeed(liveStreamingClipData.StreamingExcitementLevel);
            // コメント内容リストの切り替え
            ChangeCommentPattern(liveStreamingClipData.CommentPatern);
            // 優先コメントがあれば表示
            if (liveStreamingClipData.PriorityCommentList.Count > 0)
            {
                _priorityCommentList = new List<string>(liveStreamingClipData.PriorityCommentList);
                // コメントが流れていなかったら優先コメントを流し始める
                if (!_isScrollingComment)
                {
                    ScrollComment();
                }   
            }
#if CYG_DEBUG
            _currentSeedOffset = liveStreamingClipData.SeedOffset;
            SetDebugStreamingInfo();
#endif
        }
        
        #endregion

        #region UIの位置調整
        
        /// <summary>
        /// UIの位置を設定する（基本は回転時の処理）
        /// </summary>
        public void SetupUiPreOrientation(StoryTimelineController.DisplayMode targetDisplayMode)
        {
            if (_settingParentRectTransformArray == null || _settingParentRectTransformArray.Length == 0)
                return;

            // 横画面の場合（プレハブの位置を設定）
            if (targetDisplayMode == StoryTimelineController.DisplayMode.Landscape)
            {
                for (int i = 0; i < _settingParentRectTransformArray.Length; i++)
                {
                    _settingParentRectTransformArray[i].localScale = Math.VECTOR3_ONE;
                    _settingParentRectTransformArray[i].anchoredPosition = _initUiPos[i];
                }
            }
            // 縦画面の場合（指定の座標）
            else
            {
                bool isChange = targetDisplayMode != StoryTimelineController.CurrentDisplayMode;

                // 画面の幅と高さを取得
                var (normalScreenHeight, normalScreenWidth, normalSafeHeight, normalSafeWidth) = GetNormalScreenSize(isChange);

                float aspectHW = normalScreenHeight / normalScreenWidth;
                float aspectWH = normalScreenWidth / normalScreenHeight;

                float portraitScale = aspectWH;
                float posScale = GameDefine.BASE_ASPECT_RATIO / aspectHW;

                for (int i = 0; i < _settingParentRectTransformArray.Length; i++)
                {
                    _settingParentRectTransformArray[i].localScale = new Vector3(portraitScale, portraitScale, 1);
                    if (_anchorBottomFlags[i])
                    {
                        _settingParentRectTransformArray[i].anchoredPosition = GetPosAnchorBottom(i, posScale, normalScreenHeight, normalScreenWidth, normalSafeHeight);
                    }
                    else
                    {
                        _settingParentRectTransformArray[i].anchoredPosition = _portraitPosArray[i] * posScale;
                    }
                }
            }
        }

        /// <summary>
        /// 回転したときのUIの設定
        /// </summary>
        public void SetupUiOrientation()
        {
            if (_settingParentRectTransformArray == null || _settingParentRectTransformArray.Length == 0)
                return;
            
            // 回転後はパーティクルのサイズが変わっているのでサイズ調整
            // 常時スタンプパーティクルのサイズ調整
            UpdateStampParticleScale();
            // ユーザー用スタンプParticleは基本的に取得していないのでここで個別に取得してサイズ調整実行する
            var particleSize = GetParticleScale(StoryTimelineController.CurrentDisplayMode, Screen.Height, Screen.Width);
            foreach (var userFlashPlayer in _internalTapFlashList)
            {
                var tapParticleArray = userFlashPlayer.GetComponentsInChildren<ParticleSystem>();
                var tapParticleParent = new StampParticleParent(tapParticleArray);
                tapParticleParent.UpdateParticleScale(particleSize);
            }

            // 横画面の場合は処理しない
            if (StoryTimelineController.CurrentDisplayMode == StoryTimelineController.DisplayMode.Landscape)
            {
                return;
            }

            // 画面の幅と高さを取得
            var (normalScreenHeight, normalScreenWidth, normalSafeHeight, normalSafeWidth) = GetNormalScreenSize(false);
            // 下付けのUIの位置を調整
            float posScale = GameDefine.BASE_ASPECT_RATIO / (normalScreenHeight / normalScreenWidth);

            for (int i = 0; i < _settingParentRectTransformArray.Length; i++)
            {
                if (_anchorBottomFlags[i])
                {
                    _settingParentRectTransformArray[i].anchoredPosition = GetPosAnchorBottom(i, posScale, normalScreenHeight, normalScreenWidth, normalSafeHeight);
                }
            }
        }

        /// <summary>
        /// スクリーンの縦横サイズとセーフエリアの縦横サイズを取得する
        /// 簡易版に設定していても返ってくる値が通常版の値になるように補正した値を返す
        /// </summary>
        /// <param name="isChange">画面を回転したかどうか</param>
        /// <returns>左から順に、screenHeight, screenWidth, safeHeight, safeWidth</returns>
        private (float, float, float, float) GetNormalScreenSize(bool isChange)
        {
            // 画面の幅と高さを取得（回転するかでどちらを採用するかは変わる）
            float screenHeight = isChange ? (float)Screen.Width : (float)Screen.Height;
            float screenWidth = isChange ? (float)Screen.Height : (float)Screen.Width;
            // セーフエリアの計算で使う高さ取得
            var safeArea = UIManager.Instance.GetSafeArea();
            float safeHeight = safeArea.height > safeArea.width ? safeArea.height : safeArea.width;
            float safeWidth = safeArea.height > safeArea.width ? safeArea.width : safeArea.height;
#if !UNITY_EDITOR
            // 簡易版だとScreen.Heightが簡易版の値になってしまっているので計算時に差が出てしまう。そのため、補正をかける
            var resolutionScale = SaveDataManager.Instance.SaveLoader.GameQuality == GraphicSettings.GameQuality.Light ? GraphicSettings.LIGHT_RESOLUTION_SCALE : GraphicSettings.NORMAL_RESOLUTION_SCALE;
            screenHeight /= resolutionScale;
            screenWidth /= resolutionScale;
            safeHeight /= resolutionScale;
            safeWidth /= resolutionScale;
#endif
            return (screenHeight, screenWidth, safeHeight, safeWidth);
        }

        /// <summary>
        /// 下付けのUIを縦画面にしたときの座標を取得
        /// </summary>
        private Vector2 GetPosAnchorBottom(int index, float scale, float screenHeight, float screenWidth, float safeHeight)
        {
            if (_portraitPosArray.Length <= index)
            {
                Debug.LogError("GetPosAnchorBottom index out of range");
                return Vector2.zero;
            }

            // 16:9の縦画面での高さ算出（ベース画面横幅1080で計算して、幅1080ｘ9÷16で 607.5 ）
            const float basePortraitHeight = GameDefine.BASE_SCREEN_WIDTH * GameDefine.BASE_ASPECT_RATIO_LANDSCAPE;
            // 縦画面の画面アスペクト比
            float aspectHW = screenHeight / screenWidth;
            // 縦画面のアスペクト比から16:9との比率を計算
            float aspect = aspectHW / GameDefine.BASE_ASPECT_RATIO;
            // 16:9でプレハブの値は設定してあるので、例えば21：9の場合、縦に伸びた分、UIを上にずらしてやる必要がある
            float height = GameDefine.BASE_SCREEN_HEIGHT * aspect;
            float baseAdjustY = GameDefine.BASE_SCREEN_HEIGHT - basePortraitHeight;
            float adjustY = height - basePortraitHeight;
            float diffY = adjustY - baseAdjustY;
            
            Vector3 pos = _portraitPosArray[index];
            // 16:9より縦長端末のときは縦にのみ補正
            if (aspectHW > GameDefine.BASE_ASPECT_RATIO)
            {
                pos.y += diffY - (screenHeight - safeHeight);
            }
            // 16:9より横長端末のときは横方向にも補正をかける必要があるのでscaleをかける
            else
            {
                pos *= scale;
                pos.y += (diffY - (screenHeight - safeHeight)) * scale;
            }

            return pos;
        }

        #endregion

#if CYG_DEBUG        
        #region Debug
        /// <summary>
        /// 配信フィルタ情報表示
        /// </summary>
        private void SetDebugStreamingInfo()
        {
            if (DebugManager.Instance == null || DebugManager.Instance.DebugUiRoot == null)
                return;

            if (DebugManager.Instance.DebugUiRoot.DisplayStreamingFilterInfoObj == null)
            {
                DebugManager.Instance.DebugUiRoot.InitStoryStreamingFilterDebugInfo();
            } 
            DebugManager.Instance.DebugUiRoot.SetDisplayStreamingFilterInfo(_currentCommentPatternId, (int)_currentExcitementLevel, _currentSeedOffset);
        }    
        #endregion     
#endif          
        #endregion
    }
}


