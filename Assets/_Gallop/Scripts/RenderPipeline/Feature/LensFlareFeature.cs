using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// LensFlareを処理するFeature
    /// </summary>
    public class LensFlareFeature : ScriptableRendererFeature
    {
        #region 変数

        private LensFlareSetupPass _setupPass;
        private LensFlareRenderPass _renderPass;

        #endregion

        #region 関数

        public override void Create()
        {
            Dispose();

            _setupPass = new LensFlareSetupPass(RenderPassEvent.BeforeRendering);
            _renderPass = new LensFlareRenderPass(RenderPassEvent.AfterRenderingPostProcessing);
        }

        protected override void Dispose(bool disposing)
        {
            if (_setupPass != null)
                _setupPass.Dispose();

            if (_renderPass != null)
                _renderPass.Dispose();
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            if(!renderingData.cameraData.camera.TryGetComponent<CameraData>(out var cameraData))
            {
                return;
            }

            if (!GraphicSettings.HasInstance())
                return;
            if (!GraphicSettings.Instance.RenderingManager.LensFlareManager.IsEnable)
                return;
            //UIではLensFlareは無効
            if (cameraData.IsUIRendering)
                return;

            renderer.EnqueuePass(_setupPass);
            if(_renderPass.Setup(cameraData))
                renderer.EnqueuePass(_renderPass);
        }

        #endregion
    }
}