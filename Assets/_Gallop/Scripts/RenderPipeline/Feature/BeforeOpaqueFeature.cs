using UnityEngine.Rendering.Universal;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// Opaque前に処理を行うFeature
    /// </summary>
    public class BeforeOpaqueFeature : ScriptableRendererFeature
    {
        #region 定数

        #endregion

        #region クラス

        public class Parameter
        {
            public SimpleBlitPass.Parameter Background = SimpleBlitPass.Parameter.Default();
            //GallopFrameBufferをUIに転送する時に使用する
            //URP以前ではGallopFrameBuffer等がcamera.AddCommandBufferのAfterImageEffectのタイミングで「3Dのカメラの最終処理でUIに転送」していた
            //URPではカメラの描画開始時に必ずクリア処理が走るため、上記のタイミングで処理してもUIのカメラが描画を始めたときコピーしたものが消えてしまう
            //なので、3D>>UIに移行した最初のUI描画直前のタイミングでコピーをすることになり、以下のFrameBufferToUIを使用して処理することになった
            //そのため、3D>>自前のUIカメラ>>システム側のUIカメラのようなイレギュラーな割り込みには対応できなくなった。（システム側のUIカメラの描画のタイミングでコピーが走るので、自前のUIカメラの描画内容が消える）
            //基本的にはGallopではそのようなイレギュラー割り込みは実装NG
            public CopySurfacePass.Parameter FrameBufferToUI = CopySurfacePass.Parameter.Default(); 
            public Background3DRenderPass.Parameter BagkroundTo3D = Background3DRenderPass.Parameter.Default();

#if UNITY_EDITOR
            private bool _foldOut = false;
            public void OnInspectorGUI()
            {
                _foldOut = EditorGUILayout.Foldout(_foldOut, "BeforeOpaque");
                if (!_foldOut)
                    return;
                EditorGUI.indentLevel++;

                Background.OnInspectorGUI("Background");
                FrameBufferToUI.OnInspectorGUI("FrameBufferToUI");
                BagkroundTo3D.OnInspectorGUI();

                EditorGUI.indentLevel--;
            }
#endif
        }

        #endregion

        private SimpleBlitPass _backgroundPass; //背景画像設定
        private Background3DRenderPass _background3DPass;   //背景画像を3D背景に使用する
        private CopySurfacePass _copySurfacePass;    //GallopFrameBufferをUIに転送する用

        public override void Create()
        {
            Dispose();
            //最速Opaqueで処理されることを装置している
            //AfterRenderingPrePassesの直後を指定したいが、その場合Clearより前になってしまうので都合が悪い
            _backgroundPass = new SimpleBlitPass(RenderPassEvent.BeforeRenderingOpaques);
            _copySurfacePass = new CopySurfacePass(RenderPassEvent.BeforeRenderingOpaques);
            _background3DPass = new Background3DRenderPass(RenderPassEvent.BeforeRenderingOpaques);
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            var camera = renderingData.cameraData.camera;
            if (!camera.TryGetComponent<CameraData>(out var cameraData))
                return;

#if UNITY_EDITOR
            //エディター停止時にnull状態がありえる
            if (cameraData.BeforeOpaqueParameter == null)
                return;
#endif
            if(_background3DPass.Setup(cameraData,ref cameraData.BeforeOpaqueParameter.BagkroundTo3D))
            {
                renderer.EnqueuePass(_background3DPass);
            }

            if(_backgroundPass.Setup(cameraData,ref cameraData.BeforeOpaqueParameter.Background))
            {
                renderer.EnqueuePass(_backgroundPass);
            }

            if(_copySurfacePass.Setup(ref cameraData.BeforeOpaqueParameter.FrameBufferToUI))
            {
                renderer.EnqueuePass(_copySurfacePass);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (_background3DPass != null)
                _background3DPass.Dispose();

            if (_backgroundPass != null)
                _backgroundPass.Dispose();

            if (_copySurfacePass != null)
                _copySurfacePass.Dispose();
        }
    }
}