using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// PoolWaterをレンダリングするパス
    /// Camera.targetTextureではなく
    /// 水面テクスチャを生成するのに使用する
    /// </summary>
    public class PoolWaterRenderPass : ScriptableRenderPass
    {
        #region 定数

        private const string POOL_NAME = nameof(PoolWaterRenderPass);

        #endregion

        #region 変数

        private CameraData _cameraData;

        #endregion

        public PoolWaterRenderPass(RenderPassEvent evt)
        {
            profilingSampler = new ProfilingSampler(nameof(PoolWaterRenderPass));
            renderPassEvent = evt;
        }

        public void Dispose()
        {

        }

        public bool Setup(CameraData cameraData)
        {
            _cameraData = null;

            if (!GraphicSettings.HasInstance())
                return false;
            if (GraphicSettings.Instance.RenderingManager.PoolWaterManager.GetRenderingCount() <= 0)
                return false;
            _cameraData = cameraData;
            return true;
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
#if UNITY_EDITOR
            var camera = renderingData.cameraData.camera;
            //URP:置き換え対応
            //エディタでのみ、シーンカメラとプレビューカメラは対象外とする
            if (camera.cameraType == CameraType.SceneView || camera.cameraType == CameraType.Preview)
                return;
#endif
            GraphicSettings.Instance.RenderingManager.PoolWaterManager.OnRenderPoolWater(context, ref renderingData, this);
        }
    }
}