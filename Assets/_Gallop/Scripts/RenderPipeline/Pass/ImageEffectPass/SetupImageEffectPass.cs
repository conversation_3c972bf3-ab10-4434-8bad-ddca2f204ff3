using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// イメージエフェクトの初期SourceRT設定パス
    /// 通常のForwardRendererであればAddRenderPassesで設定が行えるが
    /// カスタマイズしたRendererではAddRenderPassesでcameraColorTargetを取得すると
    /// 警告が出るので、その対策用としてのパス
    /// </summary>
    public class SetSourceRTPass : ScriptableRenderPass
    {
        #region 定数

        private const string POOL_NAME = nameof(SetSourceRTPass);
        private static readonly int TEMP_RT_NAME = Shader.PropertyToID(POOL_NAME + "_RT0");

        #endregion

        #region 変数

        private CameraData _cameraData;
        private PostImageEffectFeature _feature;

        #endregion

        #region 関数

        public SetSourceRTPass(RenderPassEvent evt)
        {
            profilingSampler = new ProfilingSampler(nameof(SetSourceRTPass));
            renderPassEvent = evt;
        }

        public void Dispose()
        {

        }

        public bool Setup(CameraData cameraData,PostImageEffectFeature feature)
        {
            _cameraData = cameraData;
            _feature = feature;
            return true;
        }

        private void ApplyParameter(CommandBuffer cmd, ref RenderingData renderingData)
        {
            // Androidだと_ZBufferParamsの計算結果が違うので自前で計算して渡す必要がある
            // _ZBufferParamsはUnityの定義済み変数なのでそこを手動で上書き

            //イメージエフェクトカメラと対象のカメラが違う場合はここでZBufferParamを設定する
            var camera = _cameraData.ImageEffectParameter.TargetCamera;
            RenderUtils.SetZBufferParam(cmd, camera, true);
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            ref var param = ref _cameraData.ImageEffectParameter;
            ref var descriptor = ref renderingData.cameraData.cameraTargetDescriptor;
            var cmd = CommandBufferPool.Get(POOL_NAME);

            ApplyParameter(cmd,ref renderingData);
            _feature.ResetSourceRT(null, ref renderingData);

            //エフェクト前の結果をコピーする
            if(param.PreEffectTexture != null)
            {
                cmd.Blit(_feature.SourceRT.RtId, param.PreEffectTexture);
                context.ExecuteCommandBuffer(cmd);
                cmd.Clear();
            }

            if(param.ForegroundTexture != null)
            {
                RenderTextureHandle sourceTexture = RenderTextureHandle.Make(TEMP_RT_NAME, descriptor.width, descriptor.height);
                sourceTexture.GetTemporaryRT(cmd, FilterMode.Bilinear);
                cmd.Blit(param.ForegroundTexture, sourceTexture.RtId);
                //デフォルトの入力を入れ替え
                _feature.SetSourceRT(sourceTexture, cmd, ref renderingData);
            }

            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }

        #endregion
    }

}
