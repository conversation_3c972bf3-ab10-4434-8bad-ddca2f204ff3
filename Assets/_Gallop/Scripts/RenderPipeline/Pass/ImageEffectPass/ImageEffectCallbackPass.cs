using System;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// ImageEffectのRenderPassの間でコールバックを行うためのパス
    /// </summary>
    public class ImageEffectCallbackPass : ScriptableRenderPass
    {
        #region クラス

        public delegate void Action(ScriptableRenderContext context, ref RenderingData renderingData, RenderTargetIdentifier sourceRT, RenderTargetIdentifier destinationRT);

        #endregion

        #region 変数

        private CameraData _cameraData;
        private PostImageEffectFeature _feature;
        //入力(最終出力先),描画途中結果の順番
        private Action _action;

        #endregion

        #region 関数

        public ImageEffectCallbackPass(RenderPassEvent evt)
        {
            renderPassEvent = evt;
        }

        public bool Setup(CameraData cameraData,PostImageEffectFeature feature, Action action)
        {
            _cameraData = null;
            _feature = null;
            _action = null;

            if (action == null)
                return false;
            _action = action;
            _feature = feature;
            _cameraData = cameraData;
            
            return true;
        }

        public void Dispose()
        {
            _feature = null;
            _cameraData = null;
            _action = null;
        }

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            var renderer = renderingData.cameraData.renderer;
            if (renderer == null)
            {
                var message = TextUtil.Format("NullErrorCheck：ImageEffectCallbackPass：renderer：{0}", SceneManager.Instance.GetCurrentViewId().ToString());
#if ENABLE_FIREBASE
                Exception e = new Exception(message);
                Firebase.Crashlytics.Crashlytics.LogException(e);
#else
                Debug.LogErrorFormat(message);
#endif
                return;
            }
            _action(context, ref renderingData, _feature.SourceRT.RtId, renderer.cameraColorTarget);
        }

        #endregion
    }

}