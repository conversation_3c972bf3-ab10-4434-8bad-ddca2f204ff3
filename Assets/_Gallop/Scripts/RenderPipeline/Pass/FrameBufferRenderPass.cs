using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// FrameBufferへ描画するパス
    /// </summary>
    public class FrameBufferRenderPass : ScriptableRenderPass
    {
        private const string POOL_NAME = nameof(FrameBufferRenderPass);

        private Material _blitMaterial;
        private Texture _copySrcTexture;    //転送元テクスチャ

        public FrameBufferRenderPass(Material material)
        {
            profilingSampler = new ProfilingSampler(nameof(FrameBufferRenderPass));
            renderPassEvent = RenderPassEvent.AfterRenderingPostProcessing;
            _blitMaterial = material;
        }

        public void Setup(RenderTexture srcTexture)
        {
            _copySrcTexture = srcTexture;
        }

        public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraTextureDescriptor)
        {
            //FrameBufferへ描画するだけ＆このパスしかないのでクリアする必要がない
            ConfigureTarget(BuiltinRenderTextureType.CurrentActive);
            ConfigureClear(ClearFlag.None, Color.black);
        }        

        public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
        {
            var cmd = CommandBufferPool.Get(POOL_NAME);
#if ANDROID_PC
            //ANDROIDPC版は画面の真ん中に配置する必要があるのでViewPortを設定する必要がある
            cmd.SetViewport(renderingData.cameraData.camera.pixelRect);
#endif
            if (_copySrcTexture == null)
            {
                cmd.ClearRenderTarget(true, true, GameDefine.COLOR_WHITE, 0);
            }
            else
            {
                cmd.Blit(_copySrcTexture, BuiltinRenderTextureType.CurrentActive, _blitMaterial);
            }
            context.ExecuteCommandBuffer(cmd);
            CommandBufferPool.Release(cmd);
        }
    }

}