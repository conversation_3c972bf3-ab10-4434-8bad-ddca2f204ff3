using UnityEngine;
using Unity.Burst;
using Unity.Jobs;
using Unity.Collections;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;

namespace Gallop.RenderPipeline
{
    /// <summary>
    /// Projector全体の管理
    /// Renderingなどもこのクラスで行う
    /// </summary>
    public class ProjectorManager
    {
        #region 定数

        private const int DEFAULT_CAPACITY_NUM = 32;

        #endregion

        #region クラス

        /// <summary>
        /// Job計算に使用する構造体
        /// </summary>
        private struct ProjectorEntry
        {
            public bool IsOrtho;
            public float OrthoSize;
            public float NearClipPlane;
            public float FarClipPlane;
            public float FieldOfView;
            public float AspectRatio;
            public Vector3 Position;
            public Quaternion Rotation;
            public int DataIndex;
        }

        /// <summary>
        /// Job計算の結果を受け取る構造体
        /// </summary>
        public struct ProjectorSetting
        {
            public Matrix4x4 FrustumMatrix;
            public Matrix4x4 Projection;
            public Matrix4x4 Distance;
            public Matrix4x4 Clipping;
            public Vector3 Position;
            public int DataIndex;
        }

        #region Job処理

        /// <summary>
        /// ProjectorのMatrix計算
        /// </summary>
        [BurstCompile]
        private struct CalculateMatrixJob : IJobParallelFor
        {
            [ReadOnly]
            public NativeArray<ProjectorEntry> EntryArray;

            [WriteOnly]
            public NativeArray<ProjectorSetting> ProjectorSettingArray;

            private Matrix4x4 CalculationProjectionMatrix(ref ProjectorEntry projector)
            {
                Matrix4x4 projection;
                if (projector.IsOrtho)
                {
                    float size = projector.OrthoSize * projector.AspectRatio;
                    projection = Matrix4x4.Ortho(-size, size, -projector.OrthoSize, projector.OrthoSize, projector.NearClipPlane, projector.FarClipPlane);
                }
                else
                {
                    projection = Matrix4x4.Perspective(projector.FieldOfView, projector.AspectRatio, projector.NearClipPlane, projector.FarClipPlane);
                }
                return projection;
            }

            private void SetupProjectorMatrix(ref ProjectorEntry projector, out ProjectorSetting result)
            {
                var projectionMatrix = CalculationProjectionMatrix(ref projector);
                var zscale = Matrix4x4.Scale(new Vector3(1f, 1f, -1f));
                var rotation = Quaternion.Inverse(projector.Rotation);
                var projectorToWorld = Matrix4x4.TRS(rotation * -projector.Position, rotation, Math.VECTOR3_ONE);

                // functor.projection = temp2 * projectionMatrix * zscale * temp1 * projectorToWorld
                var temp1 = Matrix4x4.Scale(new Vector3(0.5f, 0.5f, 1f));
                var temp2 = Matrix4x4.Translate(new Vector3(0.5f, 0.5f, 0f));
                result.Projection = (((temp2 * projectionMatrix) * zscale) * temp1) * projectorToWorld;

                temp2 = Math.MATRIX4X4_IDENTITY;
                temp2.m00 = 0.0f; temp2.m01 = 0.0f; temp2.m02 = 1.0f;
                // functor.distance = temp2 * temp1 * projectorToWorld
                {
                    float scale = 1.0f / projector.FarClipPlane;
                    temp1 = Matrix4x4.Scale(new Vector3(scale, scale, scale));
                    result.Distance = (temp2 * temp1) * projectorToWorld;
                }

                // functor.clipping = temp2 * temp1 * temp3 * projectorToWorld
                {
                    // X-axis texture cull (use with an alpha map to do alpha-tested clip planes)
                    float scale = 1.0f / (projector.FarClipPlane - projector.NearClipPlane);
                    temp1 = Matrix4x4.Scale(new Vector3(scale, scale, scale));
                    var temp3 = Matrix4x4.Translate(new Vector3(-projector.NearClipPlane, -projector.NearClipPlane, -projector.NearClipPlane));
                    result.Clipping = ((temp2 * temp1) * temp3) * projectorToWorld;
                }

                /// Setup culling planes to be the projector area without any layer based distance culling.
                // finalProj = projectionMatrix * zscale * projectorToWorld
                // projectionMatrix == Frustomとなるはずなので、これでClipPlaneを求める
                result.FrustumMatrix = (projectionMatrix * zscale) * projectorToWorld;
                result.DataIndex = projector.DataIndex;
                result.Position = projector.Position;
            }

            public void Execute(int index)
            {
                var projectorEntry = EntryArray[index];
                SetupProjectorMatrix(ref projectorEntry, out var projectorSetting);
                ProjectorSettingArray[index] = projectorSetting;
            }
        }

        #endregion

        #endregion

        #region 変数
        
        private List<CustomProjector> _projectorList;
        private List<ProjectorSetting> _settingList;

        private Plane[] _cullingPlane;  //内部計算に使用するPlane

        public int ProjectorNum { get; private set; } = 0;

        #endregion

        public void Initialize()
        {
            const int PLANE_NUM = 6;
            _cullingPlane = new Plane[PLANE_NUM];   //left,right,top,bottom,near,farで6面
            _projectorList = new List<CustomProjector>(DEFAULT_CAPACITY_NUM);
            _settingList = new List<ProjectorSetting>(DEFAULT_CAPACITY_NUM);
            ProjectorNum = 0;
        }

        public void Release()
        {
            ProjectorNum = 0;
            _projectorList = null;
            _settingList = null;
            _cullingPlane = null;
        }

        public int AddProjector(CustomProjector projector)
        {
            if (projector.HandleID != -1)
                return projector.HandleID;

            if (_projectorList == null)
            {
#if CYG_DEBUG
                Debug.LogWarning("ProjectorManager.Initializeが呼び出されていません");
#endif
                return -1;
            }
            int num = _projectorList.Count;
            for (int i = 0; i < num; i++)
            {
                if (_projectorList[i] == null)
                {
                    projector.CullingPlane = _cullingPlane;
                    ProjectorNum++;
                    _projectorList[i] = projector;
                    return i;
                }
            }
            
            projector.CullingPlane = _cullingPlane;
            ProjectorNum++;
            _settingList.Add(default);
            _projectorList.Add(projector);
            return _projectorList.Count - 1;
        }

        public void RemoveProjector(CustomProjector projector)
        {
            if (_projectorList == null)
            {
#if CYG_DEBUG
                Debug.LogWarning("ProjectorManager.Initializeが呼び出されていません");
#endif
                return;
            }

            if (projector.HandleID < 0
            || projector.HandleID >= _projectorList.Count)
                return;

            ProjectorNum--;
            _projectorList[projector.HandleID] = null;
        }

        /// <summary>
        /// 内部パラメータを更新する
        /// </summary>
        /// <param name="camera"></param>
        public void Update(Camera camera)
        {
            if (_projectorList == null || _projectorList.Count <= 0)
                return;

            int num = _projectorList.Count;
            NativeArray<ProjectorEntry> jobData = new NativeArray<ProjectorEntry>(num, Allocator.TempJob);
            int regIndex = 0;
            for (int i = 0; i < num; i++)
            {
                var projector = _projectorList[i];
                if (projector == null)
                    continue;
                if (!projector.isActiveAndEnabled)
                    continue;

                int layer = camera.cullingMask & (~projector.IgnoreLayer);
                //写すべきレイヤーが存在しない
                if (layer == 0)
                {
                    continue;
                }

                ProjectorEntry entry;
                entry.IsOrtho = projector.OrthoGraphic;
                entry.OrthoSize = projector.OrthoGraphicSize;
                entry.NearClipPlane = projector.NearClipPlane;
                entry.FarClipPlane = projector.FarClipPlane;
                entry.FieldOfView = projector.FieldOfView;
                entry.AspectRatio = projector.AspectRatio;
                entry.Position = projector.CacheTransform.position;
                entry.Rotation = projector.CacheTransform.rotation;
                entry.DataIndex = i;
                jobData[regIndex] = entry;
                regIndex++;
            }

            if (regIndex > 0)
            {
                var job = new CalculateMatrixJob();
                job.EntryArray = jobData;
                job.ProjectorSettingArray = new NativeArray<ProjectorSetting>(regIndex, Allocator.TempJob);
                var jobHandle = job.Schedule(regIndex, 0);
                jobHandle.Complete();

                for (int i = 0; i < regIndex; i++)
                {
                    _settingList[job.ProjectorSettingArray[i].DataIndex] = job.ProjectorSettingArray[i];
                }
                job.ProjectorSettingArray.Dispose();
            }
            jobData.Dispose();
        }

        public void RenderingProjector( ScriptableRenderPass pass,
                                        ScriptableRenderContext context, CommandBuffer cmd, ref RenderingData renderingData)
        {
            var drawingSettings = RenderUtils.CreateDrawingSettings(RenderUtils.PROJECTOR_SHADERTAG_ARRAY, ref renderingData, SortingCriteria.CommonOpaque);
            var renderQueue = RenderQueueRange.all;
            RenderingProjector(pass, context, cmd, ref renderingData, ref drawingSettings, ref renderQueue);
        }

        public void RenderingProjector( ScriptableRenderPass pass,
                                        ScriptableRenderContext context, CommandBuffer cmd, ref RenderingData renderingData,
                                        ref DrawingSettings drawingSettings, ref RenderQueueRange queueRange)
        {
            if (_projectorList == null || _projectorList.Count <= 0)
                return;

            ref var cameraData = ref renderingData.cameraData;
            drawingSettings.perObjectData = PerObjectData.None;
            var num = _projectorList.Count;
            for (int i = 0; i < num; i++)
            {
                var projector = _projectorList[i];
                if (projector == null)
                    continue;
                if (!projector.isActiveAndEnabled)
                    continue;

                // Projectorがカメラの処理対象かどうか
                if ((cameraData.camera.cullingMask & (1 << projector.CacheGameObject.layer)) == 0)
                {
                    continue;
                }
                
                int layer = cameraData.camera.cullingMask & (~projector.IgnoreLayer);
                //カメラがProjectorそのものを描画対象としているかは条件と同じタイミングで行う
                //Projectorが写すべきレイヤーが存在しない
                if (layer == 0)
                {
                    continue;
                }

                // Projectorコンポーネントの実装毎に処理を行う
                var setting = _settingList[i];
                projector.DrawProjector(pass, context, cmd, ref renderingData, ref drawingSettings, ref queueRange, ref setting);
            }
        }
        
        /// <summary>
        /// CutoffProjectorで描画サポートをするシェーダかどうか
        /// </summary>
        /// <param name="targetKind"></param>
        /// <returns></returns>
        public static bool CheckSupportedRenderingCutoffProjector(Material checkMaterial, out CutoffType supportedCutoffType)
        {
            supportedCutoffType = CutoffType.Invalid;

            var cutoffTypePropId = ShaderManager.GetPropertyId(ShaderManager.PropertyId._CutoffType);
            if (!checkMaterial.HasProperty(cutoffTypePropId)) return false;
            
            // 念のため取得した値が有効かチェック
            var cutoffTypeOfInt = checkMaterial.GetInt(cutoffTypePropId);
            if (cutoffTypeOfInt < (int)CutoffType.Invalid || cutoffTypeOfInt >= (int)CutoffType.Max) return false;

            supportedCutoffType = (CutoffType)cutoffTypeOfInt;
            return true;
        }
    }
}