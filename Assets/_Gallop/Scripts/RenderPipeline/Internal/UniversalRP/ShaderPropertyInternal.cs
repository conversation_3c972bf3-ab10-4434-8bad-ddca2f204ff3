using UnityEngine;
using UnityEngine.Rendering;
using UniversalInternal = UnityEngine.Rendering.Universal;

#if UNITY_EDITOR
using UnityEditor;
#endif
namespace Gallop.RenderPipeline.Internal
{
    /// <summary>
    /// Universal RPのShaderPropertyアクセス用
    /// </summary>
    public static class ShaderPropertyIdInternal
    {
        public static readonly int scaleBiasRt = UniversalInternal.ShaderPropertyId.scaleBiasRt;
        public static readonly int worldSpaceCameraPos = UniversalInternal.ShaderPropertyId.worldSpaceCameraPos;
        public static readonly int screenParams = UniversalInternal.ShaderPropertyId.screenParams;
        public static readonly int zBufferParams = UniversalInternal.ShaderPropertyId.zBufferParams;
    }
}