using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 頭部モデルに関連する機能のオミット設定
    /// </summary>
    /// <remarks>
    /// <para>
    /// メカ編のモデルには眉毛やチークが無かったりするが、基本的な構成パーツとして「ある前提」になっているので、
    /// リソース側にコレらが無いと動作不良を起こす。<br/>
    /// 使わないリソースを用意しなくても済むように、この設定データから不要なパーツを参照する機能をオミットする
    /// </para>
    /// <para>
    /// このデータは対象となる頭部モデルのAssetHolderに登録して利用する想定
    /// </para>
    /// </remarks>
    [Serializable]
    [CreateAssetMenu(menuName = "Scriptable/Chara/CharaHeadModelOmitSetting")]
    public class CharaHeadModelOmitSetting : ScriptableObject
    {
        // 眉毛（M_Mayu）
        public bool IsMayu;
        // チーク（M_Cheek）
        public bool IsCheek;
        // ハイライトアニメーション（anm_chr◯◯◯◯_◯◯_facial_eye00、anm_chr◯◯◯◯_◯◯_facial_eye01）
        public bool IsEyeHighlightAnimation;
        // 涙モデル（M_Tear_L、M_Tear_R）
        public bool IsTearModel;
        // 汗（pfb_chr◯◯◯◯_◯◯_sweatlocator.prefab）
        public bool IsSweat;
        // 涙アニメーション（anm_chr◯◯◯◯_◯◯_tear000_00）
        public bool IsTearAnimation;
        // 漫画目（Eye_target_locator_L、Eye_target_locator_R）
        public bool IsMangame;
        // 青ざめ（Head_shade_start）
        public bool IsShade;
    }
}

