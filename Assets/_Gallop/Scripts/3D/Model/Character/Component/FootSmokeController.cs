using UnityEngine;
using System;
using Object = UnityEngine.Object;
using static Gallop.StaticVariableDefine.CG3D.FootSmokeController;
using System.Collections.Generic;

namespace Gallop
{
    namespace Model.Component
    {
        /// <summary>
        /// 足元エフェクトのベースコンポーネントクラス
        /// これを派生して、Race、Training用に分かれている
        /// </summary>
        public class FootSmokeController : ModelComponentBase
                                         , IModelComponentPreLateUpdate, IModelComponentFixedUpdate
        {
            #region 定数

            protected enum LegIndex
            {
                Left,
                Right,
                Num,
            }

            protected const int LEGINDEX_NUM = (int)LegIndex.Num;

            #endregion

            #region クラス

            /// <summary>
            /// 片足分のエフェクト管理クラス
            /// </summary>
            protected struct LegObject
            {
                public GameObject EffectObject;
                public Transform EffectTransform;
                public ParticleSystem[] ParticleArray;
                public Renderer[] RendererArray;
                /// <summary> マスク内描画マテリアル管理クラスの配列 </summary>
                public SetParamMaterial[] DrawInMaskMaterialArray;
                public ParticlePauseController PauseController;
                public Action PlayCallback;
                public bool IsEffectPlay;
                public bool IsForcePlay;

                public void Initialize()
                {
                    ParticleArray = Array.Empty<ParticleSystem>();
                    RendererArray = Array.Empty<Renderer>();
                    DrawInMaskMaterialArray = Array.Empty<SetParamMaterial>();
                    IsEffectPlay = false;
                    IsForcePlay = false;
                }

                public void Release()
                {
                    if (EffectObject != null)
                    {
                        Object.Destroy(EffectObject);
                        EffectObject = null;
                    }
                    EffectTransform = null;
                    ParticleArray = null;
                    RendererArray = null;
                    PauseController = null;

                    if (DrawInMaskMaterialArray != null)
                    {
                        for (int i = 0; i < DrawInMaskMaterialArray.Length; i++)
                        {
                            DrawInMaskMaterialArray[i].Release();
                        }
                        DrawInMaskMaterialArray = null;
                    }
                }

                public void SetSimulationSpeed(float speed)
                {
                    foreach (var particle in ParticleArray)
                    {
                        var main = particle.main;
                        var oneFrameSpeed = ((main.startLifetime.Evaluate(0) * main.startLifetimeMultiplier)) / GameDefine.BASE_FPS_TIME;
                        main.simulationSpeed = Mathf.Min(speed, oneFrameSpeed);
                    }
                }

                public void SetActive(bool isActive)
                {
                    if (EffectObject == null) return;
                    EffectObject.SetActive(isActive);
                }

                public void CreatePauseController()
                {
                    PauseController = new ParticlePauseController(EffectObject);
                }

                public void CollectParticleRenderer()
                {
                    if (EffectObject == null)
                        return;

                    RendererArray = EffectObject.GetComponentsInChildren<Renderer>();

                    var drawInMaskMaterialList = new List<SetParamMaterial>();

                    // Renderer.materialの複製によるメモリリークを防ぐためMaterialをキャッシュしておく
                    DrawInMaskMaterialArray = new SetParamMaterial[RendererArray.Length];
                    for (int i = 0; i < RendererArray.Length; i++)
                    {
                        var materials = RendererArray[i].materials;
                        for (int j = 0; j < materials.Length; j++)
                        {
                            drawInMaskMaterialList.Add(new SetParamMaterial(materials[j], true));
                        }
                        DrawInMaskMaterialArray[i] = new SetParamMaterial(RendererArray[i].material, true);
                    }

                    DrawInMaskMaterialArray = drawInMaskMaterialList.ToArray();
                }

                public void Play()
                {
                    if (IsForcePlay)
                    {
                        ParticleArray[0].Stop();
                    }

                    ParticleArray[0].Play();
                    PlayCallback?.Invoke();
                    IsEffectPlay = true;
                }

                public void Pause()
                {
                    PauseController?.Pause(true);
                }

                public void Resume()
                {
                    PauseController?.Resume();
                }

                public bool IsEnable => (ParticleArray.Length > 0);
            }

            #endregion

            #region 変数

            protected LegObject[] _legObjectArray = new LegObject[LEGINDEX_NUM];

            protected Transform _ownerTransform;
            protected bool _isVisible;
            protected bool _isPause = false;

            protected MaterialPropertyBlock _materialPropertyBlock = null;
            protected int _lastPlayedLoopNum = 0;

            // 65160 足跡用エフェクトはSetActiveで止めると復元できないので
            // 必要に応じて継承先で処理を変える。
            public virtual bool IsVisible
            {
                get => _isVisible;
                set
                {
                    if(_isVisible != value)
                    {
                        _isVisible = value;
                        for (int i = 0; i < LEGINDEX_NUM; i++)
                        {
                            _legObjectArray[i].SetActive(value);
                        }
                    }
                }
            }

            #endregion

            public FootSmokeController()
            {
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].Initialize();
                }
            }

            #region ModelComponent

            public override void OnDestroy()
            {
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].Release();
                }
                _legObjectArray = null;
            }

            public override void OnInitialize()
            {
                base.OnInitialize();

                var owner = GetOwner();

                _ownerTransform = owner.transform;
                _isVisible = true;
                _materialPropertyBlock = new MaterialPropertyBlock();
            }

            public override void FixedUpdate()
            {
                PreLateUpdate();
            }

            public override void PreLateUpdate()
            {
                if (!IsUpdateEnable())
                {
                    return;
                }

                float normalizedTime = GetPlayNormalizeTime();
                int curPlayingLoopNum = (int)(normalizedTime);
                if (curPlayingLoopNum != _lastPlayedLoopNum)
                {
                    for (int i = 0; i < LEGINDEX_NUM; i++)
                    {
                        _legObjectArray[i].IsEffectPlay = false;
                    }
                    _lastPlayedLoopNum = curPlayingLoopNum;
                }

                for(var legIndex = LegIndex.Left;legIndex <= LegIndex.Right;legIndex++)
                {
                    if (IsShouldPlay(legIndex, normalizedTime))
                    {
                        _legObjectArray[(int)legIndex].Play();
                        foreach (var renderer in _legObjectArray[(int)legIndex].RendererArray)
                        {
                            renderer.SetPropertyBlock(_materialPropertyBlock);
                        }
                    }
                }
            }

            #endregion

            /// <summary>
            /// エフェクト再生速度設定。
            /// </summary>
            public void SetSpeed(float speed)
            {
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].SetSimulationSpeed(speed);
                }
            }

            public void SetProbeColor(Color color)
            {
                _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._LightProbeColor), color);
            }

            /// <summary>
            /// エフェクト生成
            /// </summary>
            /// <param name="footEffectPrefab"></param>
            public virtual void CreateEffectObject(GameObject footEffectPrefab)
            {
                //破棄して作り直す
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].Release();
                }

                OnCreateSmokeEffect(ref _legObjectArray[(int)LegIndex.Left], FOOTEFFECT_POS_LEFT, footEffectPrefab);
                OnCreateSmokeEffect(ref _legObjectArray[(int)LegIndex.Right], FOOTEFFECT_POS_RIGHT, footEffectPrefab);

                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].CreatePauseController();
                }
            }

            public void Pause()
            {
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].Pause();
                }
                _isPause = true;
            }

            public void Resume()
            {
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].Resume();
                }
                _isPause = false;
            }

            public void ResetParticle()
            {
                for (int i = 0; i < LEGINDEX_NUM; i++)
                {
                    _legObjectArray[i].SetActive(false);
                    _legObjectArray[i].SetActive(true);
                }
            }

            /// <summary>
            /// 右足エフェクト再生時のコールバック登録
            /// </summary>
            /// <param name="callback"></param>
            public void AddRightPlayCallBack(Action callback) => AddPlayCallback(callback, LegIndex.Right);

            /// <summary>
            /// 左足エフェクト再生時のコールバック登録
            /// </summary>
            /// <param name="callback"></param>
            public void AddLeftPlayCallBack(Action callback) => AddPlayCallback(callback, LegIndex.Left);

            public void DelRightPlayCallBack(Action callback) => DelPlayCallback(callback, LegIndex.Right);

            public void DelLeftPlayCallBack(Action callback) => DelPlayCallback(callback, LegIndex.Left);

            #region 内部関数

            protected virtual GameObject CreateSmokeEffect(Transform owner, GameObject footEffectPrefab)
            {
                //継承先でoverride使用してエフェクトオブジェクトを生成してください
                return null;
            }

            /// <summary>
            /// 砂煙エフェクト生成。
            /// </summary>
            /// <param name="legObject"></param>
            /// <param name="localPos"></param>
            /// <param name="footEffectPrefab"></param>
            protected void OnCreateSmokeEffect(ref LegObject legObject, Vector3 localPos, GameObject footEffectPrefab)
            {
                if (footEffectPrefab == null) return;

                legObject.EffectObject = CreateSmokeEffect(_ownerTransform, footEffectPrefab);
                if (null != legObject.EffectObject)
                {
                    var cacheTransform = legObject.EffectObject.transform;
                    cacheTransform.transform.localPosition = localPos;
                    legObject.EffectTransform = cacheTransform;

                    legObject.ParticleArray = legObject.EffectObject.GetComponentsInChildren<ParticleSystem>();
                    legObject.ParticleArray[0].Pause();
                    legObject.CollectParticleRenderer();
                    legObject.IsEffectPlay = false;
                }
            }

            /// <summary>
            /// エフェクトを再生していいタイミングか
            /// </summary>
            /// <returns></returns>
            protected virtual bool IsShouldPlay(LegIndex index, float normalizedTime)
            {
                return true;
            }

            /// <summary>
            /// モーションの正規化した再生時間
            /// </summary>
            /// <returns></returns>
            protected virtual float GetPlayNormalizeTime() => 0.0f;

            protected bool IsUpdateEnable()
            {
                // ポーズ中は更新不要。
                if (_isPause)
                {
                    return false;
                }

                if (!IsEnable)
                    return false;

                // オブジェクトへの参照が不正な場合は更新不要。
                for (int i=0;i< LEGINDEX_NUM; i++)
                {
                    if (!_legObjectArray[i].IsEnable)
                        return false;
                }

                return true;
            }

            private void AddPlayCallback(Action callback, LegIndex index)
            {
                _legObjectArray[(int)index].PlayCallback += callback;
            }

            private void DelPlayCallback(Action callback, LegIndex index)
            {
                _legObjectArray[(int)index].PlayCallback -= callback;
            }

            // NormalizedTimeの範囲は0.0 ~ 1.0になるべき
            // UnityのNormalizedTimeはループした分も含まれているので取り除く
            protected static float GetNormalizedLength(float loopedNormalizedTime)
            {
                int looped = (int)loopedNormalizedTime;
                return loopedNormalizedTime - looped;
            }

            #endregion
        }
    }
}
