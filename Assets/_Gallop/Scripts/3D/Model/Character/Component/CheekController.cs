using System;
using UnityEngine;

namespace Gallop
{
    namespace Model.Component
    {
        public abstract class CheekOwner
        {
            public abstract GameObject GetCheekObject();
            public abstract Texture GetCheekTexture(int index);
            public abstract void SetVisible_Cheek(bool isVisible, int cheekIndex = 0);
            /// <summary>
            /// 155532 CheekRateに対して追加計算を行う
            /// </summary>
            public virtual void SetMulCheekRate(float cheekRate) { }
        }

        /// <summary>
        /// チーク制御クラス
        /// </summary>
        public class CheekController : ModelComponentBase, IModelComponentLateUpdate
        {
            #region 定数

            /// <summary>
            /// ドリブンキーによるチーク変化で使用
            /// ドリブンキーの中から探すオブジェクト名
            /// このオブジェクトのスケール値が変化すると自動でチークを使い分ける処理を走らせる
            /// </summary>
            public const string CHEEKCTRL_NAME = "Cheek_Ctrl";

            #endregion 定数

            #region クラス

            public enum ControlType
            {
                ScaleXY = 0,
                PositionXZ = 1,
            }
            public struct Context
            {
                public Transform DrivenKeyLocatorRootTransform;
                public CheekOwner CheekOwner;
                public bool IsDrivenKeyControl;
                public ControlType CtrlType;
            }

            #endregion

            #region 変数

            private Context _context;

            /// <summary>
            /// オーナー
            /// </summary>
            private CheekOwner _owner;

            /// <summary>
            /// チークのオブジェクト
            /// </summary>
            private GameObject _object = null;

            private Material[] _rendererMaterialArray;
            private SetParamMaterial[] _drawInMaskMaterialArray;

            private Material _material = null;

            /// <summary>
            /// チークで使用するテクスチャ
            /// </summary>
            private Texture _texture0 = null;

            /// <summary>
            /// チークで使用するテクスチャ
            /// </summary>
            private Texture _texture1 = null;

            private Texture _whiteTexture = null;

            /// <summary>
            /// 初期化済みフラグ
            /// </summary>
            private bool _isInitialized = false;

            /// <summary>
            /// ドリブンキーによるリーク変化フラグ
            /// </summary>
            private bool _isDrivenKeyControll = false;
            public bool IsDrivenKeyControll
            {
                set { _isDrivenKeyControll = value; }
                get { return _isDrivenKeyControll; }
            }

            /// <summary>
            /// 表示フラグ
            /// </summary>
            private bool _isCheekVisible = false;
            public bool IsCheekVisible
            {
                get { return _isCheekVisible; }
            }

            /// <summary>
            /// Objectのアクティブ
            /// </summary>
            public bool IsActiveObject
            {
                get => (_object == null) ? false : _object.activeSelf;
                set => _object.SetActiveWithCheck(value);
            }

            /// <summary>
            /// 現在表示中のチーク
            /// </summary>
            private int _currentCheekIndex = 0;
            public int CurrentCheekIndex
            {
                get { return _currentCheekIndex; }
            }

            /// <summary>
            /// チークのしきい値
            /// </summary>
            private float _cheekThresholdValue = 0f;

            /// <summary>
            /// チークのしきい値に対して加算する値
            /// </summary>
            private float _cheekThresholdAddend;

            /// <summary>
            /// しきい値を設定する外部関数（設定しない場合はリニアに増減）
            /// </summary>
            private Func<bool, float, float, float> _calcThresholdValueCallback = null;
            public void SetCalcCheekThresholdCallback(Func<bool, float, float, float> callback)
            {
                _calcThresholdValueCallback = callback;
            }
            public bool IsCalcCheekThreshold() => _calcThresholdValueCallback != null;

            /// <summary>
            /// 時間
            /// </summary>
            private float _timeCount = 0f;

            /// <summary>
            /// 遷移までの時間
            /// </summary>
            private float _timeMax = 0f;

            /// <summary>
            /// ドリブンキー変化を取得するチークのTransform
            /// </summary>
            private Transform _cheekLocator;

            /// <summary>
            /// ドリブンキーから表示したか。
            /// </summary>
            private bool _isVisibleFromDrivenKey = false;
            #endregion 変数

            #region メソッド

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="modelController"></param>
            public CheekController(ref Context context)
            {
                _context = context;

#if CYG_DEBUG
                if (context.CheekOwner == null)
                {
                    Debug.LogError("CheekOwner == null");
                }

                if (context.DrivenKeyLocatorRootTransform == null)
                {
                    Debug.LogError("DrivenKeyLocatorRootTransform== null");
                }
#endif
            }

            /// <summary>
            /// シェーダーを入れ替える
            /// </summary>
            /// <param name="shader"></param>
            public Shader Shader
            {
                set
                {
                    if (_material == null)
                        return;
                    _material.shader = value;
                }
            }
            /// <summary>
            /// 描画順番を変更する
            /// </summary>
            public int RenderQueue
            {
                get
                {
                    if (_material == null)
                        return -1;

                    return _material.renderQueue;
                }

                set
                {
                    if (_material == null)
                        return;

                    _material.renderQueue = value;
                }
            }

            /// <summary>
            /// 実行可能か
            /// </summary>
            /// <returns></returns>
            public bool IsValid()
            {
                return _isInitialized;
            }

            #region ModelComponent

            public override void OnInitialize()
            {
                base.OnInitialize();

                var owner = GetOwner();
                var rendererHolder = owner.GetModelComponent<RendererHolder>();
                if (rendererHolder == null)
                {
                    Debug.LogError("RendererHolder == null");
                    return;
                }

                _owner = _context.CheekOwner;
                _object = null;
                _material = null;
                _texture0 = null;
                _texture1 = null;
                _whiteTexture = Texture2D.whiteTexture;
                _isInitialized = false;
                _rendererMaterialArray = null;

                _object = _owner.GetCheekObject();
                if (_object == null) return;

                _cheekLocator = _context.DrivenKeyLocatorRootTransform.Find(CHEEKCTRL_NAME);
                var holderIndex = rendererHolder.FindIndex(_object);
                if (holderIndex < 0)
                    return;

                _rendererMaterialArray = rendererHolder.GetMaterialArray(holderIndex);
                if (_rendererMaterialArray.Length > 0)
                {
                    _material = _rendererMaterialArray[0];

                    // これが必用なのはレースモデルだけ
                    if (owner is RaceModelController)
                    {
                        _drawInMaskMaterialArray = new SetParamMaterial[_rendererMaterialArray.Length];
                        for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                        {
                            _drawInMaskMaterialArray[i] = new SetParamMaterial(_rendererMaterialArray[i], true);
                        }
                    }
                }

                _texture0 = _owner.GetCheekTexture(0);
                _texture1 = _owner.GetCheekTexture(1);

                if (_material == null) return;
                if (_texture0 == null) return;
                if (_texture1 == null) return;

                _isInitialized = true;
                _isDrivenKeyControll = _context.IsDrivenKeyControl;

                _currentCheekIndex = -1; // 最初に↓でテクスチャ設定しておきたいので.
                SetVisible(false, 0, 0f);
            }

            public override void OnDestroy()
            {
                base.OnDestroy();

                if (_drawInMaskMaterialArray != null)
                {
                    for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                    {
                        _drawInMaskMaterialArray[i].Release();
                    }

                    _drawInMaskMaterialArray = null;
                }

                _isInitialized = false;
                _texture0 = _texture1 = null;
                _whiteTexture = null;
                _rendererMaterialArray = null;
                _material = null;
            }

            public override void LateUpdate()
            {
                base.LateUpdate();
                OnUpdate(GetOwner().DeltaTime);
            }

            #endregion

            /// <summary>
            /// Update処理
            /// </summary>
            /// <param name="deltaTime"></param>
            private void OnUpdate(float deltaTime)
            {
                if (!_isInitialized) return;

                // ドリブンキー変化による更新
                UpdateFromLocator();

                if (_timeMax > 0f)
                {
                    _timeCount += deltaTime;
                    if (_timeCount >= _timeMax)
                    {
                        if (_isCheekVisible)
                        {
                            _cheekThresholdValue = 1f;
                            SetCheekRate(_cheekThresholdValue);
                        }
                        else
                        {
                            _cheekThresholdValue = 0f;
                            _object.SetActive(false);
                        }

                        _timeCount = 0f;
                        _timeMax = 0f;
                    }
                    else
                    {
                        // 外部から渡された関数を使って計算するか
                        if (IsCalcCheekThreshold())
                        {
                            _cheekThresholdValue = _calcThresholdValueCallback.Invoke(_isCheekVisible, _timeCount, _timeMax);
                        }
                        else
                        {
                            _cheekThresholdValue += (_cheekThresholdAddend * deltaTime);
                        }
                        SetCheekRate(_cheekThresholdValue);
                    }
                }
            }

            /// <summary>
            /// チークロケーターによるチーク変化Update処理
            /// _isDrivenKeyControllがtrueじゃないと動きません
            /// </summary>
            private void UpdateFromLocator()
            {
                // 機能を使用するかチェック
                if (!_isDrivenKeyControll)
                {
                    return;
                }

                //外部から呼び出す更新処理
                if (_owner == null || _cheekLocator == null)
                    return;

                float cheek1Threshold = 0f;
                float cheek2Threshold = 0f;
                switch (_context.CtrlType)
                {
                    case ControlType.ScaleXY:
                        {
                            cheek1Threshold = Mathf.Max(0.0f, _cheekLocator.localScale.x - 0.0001f);
                            cheek2Threshold = Mathf.Max(0.0f, _cheekLocator.localScale.y - 0.0001f);
                            break;
                        }
                    case ControlType.PositionXZ:
                        {
                            const int MAYA_SCALE = 100;
                            cheek1Threshold = _cheekLocator.localPosition.x * MAYA_SCALE;
                            cheek2Threshold = _cheekLocator.localPosition.z * MAYA_SCALE;
                            break;
                        }
                    default:
                        {
                            Debug.LogError("ControlTypeが追加されたらここにも追加。");
                            break;
                        }
                }
                if (cheek1Threshold > 0)
                {
                    _owner.SetVisible_Cheek(true, 0);
                    SetCheekRate(cheek1Threshold);
                    _isVisibleFromDrivenKey = true;
                }
                else if (cheek2Threshold > 0)
                {
                    _owner.SetVisible_Cheek(true, 1);
                    SetCheekRate(cheek2Threshold);
                    _isVisibleFromDrivenKey = true;
                }
                else
                {
                    if (_isVisibleFromDrivenKey)
                    {
                        _owner.SetVisible_Cheek(false);
                        _isVisibleFromDrivenKey = false;
                    }
                }
            }

            /// <summary>
            /// チークの補完値を設定する
            /// </summary>
            public void SetCheekRate(float cheekThresholdValue)
            {
                if (!_isInitialized) return;

                var cheekRate = Mathf.Clamp01(cheekThresholdValue);
                foreach (var material in _rendererMaterialArray)
                {
                    material.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CheekRate), cheekRate);
                }

                //155532 キャラモデルにもCheekRateの計算がありますので、マテリアルの設定後モデル側に設定値を渡す
                //現状ミニキャラ向けに処理の実装はしていない
                _owner.SetMulCheekRate(cheekThresholdValue);
            }

            /// <summary>
            /// レースでのマルチカメラステンシル対応
            /// </summary>
            /// <param name="multiCameraIndex"></param>
            public void SetupDrawInMask(
                int? stencilId, UnityEngine.Rendering.CompareFunction? stencilComp, UnityEngine.Rendering.StencilOp? stencilOp
            )
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }

                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    _drawInMaskMaterialArray[i].SetMaterialParams(stencilId, stencilComp, stencilOp);
                }
            }

            /// <summary>
            /// DrawInMaskのリセット
            /// </summary>
            public void ResetDrawInMask()
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }

                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    _drawInMaskMaterialArray[i].ResetMaterialParams();
                }
            }

            /// <summary>
            /// DrawInMaskのリセット
            /// </summary>
            /// <param name="propertyId"> リセットしたいシェーダープロパティID </param>
            public void ResetDrawInMask(ShaderManager.PropertyId propertyId)
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }

                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    _drawInMaskMaterialArray[i].ResetMaterialParam(propertyId);
                }
            }

            /// <summary>
            /// 表示切替
            /// </summary>
            public void SetVisible(bool isVisible, int cheekIndex = 0, float time = 0f, bool isPreviousVisible = false, int previousCheekIndex = 0)
            {
                if (!_isInitialized) return;

                if ((_isCheekVisible == isVisible) && (_currentCheekIndex == cheekIndex))
                    return;

                _cheekThresholdValue = 0f;
                //補完前のテクスチャ情報を取得
                var previousCheekTexture = isPreviousVisible
                    ? (previousCheekIndex <= 0) ? _texture0
                    : (previousCheekIndex >= 1) ? _texture1
                        : null
                    : _whiteTexture;

                _material.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PreviousCheekTex), previousCheekTexture);

                if (isVisible)
                {
                    if (cheekIndex <= 0)
                    {
                        _material.mainTexture = _texture0;
                    }
                    else if (cheekIndex >= 1)
                    {
                        _material.mainTexture = _texture1;
                    }
                }
                else
                {
                    _material.mainTexture = _whiteTexture;
                }

#if !CYG_PRODUCT
                if (MeasurementSettingSceneController.IsNoCharaThings)
                {
                    // 有効にしない。
                }
                else
#endif
                {
                    _object.SetActive(true);
                }

                var value = 1f - _cheekThresholdValue;
                _timeMax = time * value;
                if (_timeMax > 0f)
                {
                    _cheekThresholdAddend = value / time;
                    SetCheekRate(_cheekThresholdValue);
                }
                else
                {
                    _cheekThresholdValue = 1f;
                    SetCheekRate(_cheekThresholdValue);
                    _object.SetActive(isVisible);
                }

                _isCheekVisible = isVisible;
                _currentCheekIndex = cheekIndex;
            }

            #endregion

#if UNITY_EDITOR
            protected override bool IsDrawInspectorGUI() => true;

            protected override void OnDrawInspectorGUI()
            {
                base.OnDrawInspectorGUI();
                using (new UnityEditor.EditorGUI.DisabledGroupScope(true))
                {
                    UnityEditor.EditorGUILayout.Toggle("表示", _isCheekVisible);
                    UnityEditor.EditorGUILayout.IntField("チーク番号", _currentCheekIndex);
                    UnityEditor.EditorGUILayout.FloatField("しきい値", _cheekThresholdValue);
                    UnityEditor.EditorGUILayout.Toggle("DrivenKey制御", _isDrivenKeyControll);
                    UnityEditor.EditorGUILayout.ObjectField("ロケーター", _cheekLocator, typeof(Transform), false);
                }
            }
#endif
        }
    }
}
