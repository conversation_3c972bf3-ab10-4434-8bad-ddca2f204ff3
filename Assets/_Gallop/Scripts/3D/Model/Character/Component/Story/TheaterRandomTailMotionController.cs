namespace Gallop
{
    using static MasterRandomEarTailMotion;

    /// <summary>
    /// しっぽのランダム再生 (シアター)
    /// </summary>
    public class TheaterRandomTailMotionController : RandomTailMotionController
    {
        // シアターのランダム尻尾モーションを抽出
        protected override System.Func<RandomEarTailMotion, bool> MotionFilterFunction =>
            (data) => data.UseTheater;

        protected override System.Func<MasterCharaData.CharaData, int> TailRandomTimeMinGetter =>
            (charaData) => charaData.TailRandomTimeMin;

        protected override System.Func<MasterCharaData.CharaData, int> TailRandomTimeMaxGetter =>
            (charaData) => charaData.TailRandomTimeMax;

        /// <summary>
        /// シアターの場合はファイルPathではなくモーション名をそのまま使う
        /// </summary>
        protected override System.Func<string, string> TailMotionPathGetter =>
            (motionName) => motionName;
        
#if UNITY_EDITOR && CYG_DEBUG
        public System.Func<string, string> ToolMotionPathGetter { get; set; } = null;

        public override string GetMotionPathForTutorialAssetCollect(string motionName)
        {
            if (ToolMotionPathGetter != null)
            {
                return ToolMotionPathGetter.Invoke(motionName);
            }

            return motionName;
        }
#endif        
    }
}
