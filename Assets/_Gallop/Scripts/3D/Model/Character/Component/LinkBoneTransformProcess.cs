using System.Collections.Generic;
using System;
using UnityEngine;

namespace Gallop
{
    namespace Model.Component
    {
        /// <summary>
        /// キャラのボーンと連動させるためのコンポーネント
        /// 例えば手袋が手のモーションと連動して動作する時で使う
        /// Propと対象となるキャラBodyのボーンの階層構造と数が一致することが条件
        /// </summary>
        public abstract class LinkBoneTransformProcess : TransformProcess
        {
            /// <summary>
            /// 連動する骨データ、
            /// キャラの骨と連動しようとするものの骨は一対一の関係となる
            /// </summary>
            private Dictionary<Transform, Transform> _linkBoneDataDictionary = new Dictionary<Transform, Transform>();

            /// <summary>
            /// 参照元の骨を非表示にする対象一覧
            /// </summary>
            private Dictionary<Transform, Transform> _hideReferenceBoneDataDictionary = new Dictionary<Transform, Transform>();

            /// <summary>
            /// 参照元の骨を非表示にする前のスケール値のキャッシュ
            /// </summary>
            private Dictionary<Transform, Vector3> _referenceBoneScaleCacheDictionary = new Dictionary<Transform, Vector3>();

            /// <summary>
            /// このフレームで参照元の骨を非表示にする前のスケール値をキャッシュ済みか
            /// </summary>
            /// <remarks>
            /// 非表示にする直前にキャッシュしてからPreUpdateでリセット処理に使うまでtrue<br/>
            /// 非表示処理が1フレームで複数回呼ばれても大丈夫なようにしている
            /// </remarks>
            private bool _isCurrentFrameReferenceBoneScaleCached;


            /// <summary>
            /// 位置情報を連動（基本true）
            /// </summary>
            public bool IsLinkPosition { get; set; } = true;

            /// <summary>
            /// 回転情報を連動（基本true）
            /// </summary>
            public bool IsLinkRotation { get; set; } = true;

            /// <summary>
            /// スケール情報を連動（基本true）
            /// </summary>
            public bool IsLinkScale { get; set; } = true;

            /// <summary>
            /// 参照元の骨を非表示にする
            /// </summary>
            public bool IsHideReferenceBone { get; set; } = false;

            public LinkBoneTransformProcess() : base(null, UpdateTimingType.LateUpdate) { }

            /// <summary>
            /// リンクする骨を追加
            /// 現状は同じキャラの骨に参照対象の上書きをしていない
            /// </summary>
            /// <param name="charaBodyBone"></param>
            /// <param name="targetBone"></param>
            public void AddLinkBone(Transform charaBodyBone, Transform targetBone)
            {
                _linkBoneDataDictionary.TryAddValue(charaBodyBone, targetBone);

                if (IsHideReferenceBone)
                {
                    if (IsHideTargetCharacterBone(charaBodyBone))
                    {
                        _hideReferenceBoneDataDictionary.TryAddValue(charaBodyBone, targetBone);
                    }
                }
            }

            public override void Clear()
            {
                _linkBoneDataDictionary.Clear();
                _hideReferenceBoneDataDictionary.Clear();
                _referenceBoneScaleCacheDictionary.Clear();
            }

            /// <summary>
            /// _processを利用しないため、上書きしてエラーログを発生させない
            /// </summary>
            public override void OnInitialize() { }

            /// <summary>
            /// 骨連動する前に参照元のキャラの骨のスケールを戻す
            /// </summary>
            public override void PreUpdate()
            {
                if (IsHideReferenceBone)
                {
                    ResetReferenceCharacterBoneScale();
                }
                else
                {
                    base.PreUpdate();
                }
            }

            /// <summary>
            /// 骨の連動処理後参照元のキャラの骨を非表示にする
            /// </summary>
            public override void AfterLateUpdate()
            {
                if (IsHideReferenceBone)
                {
                    HideReferenceCharacterBone();
                }
                else
                {
                    base.AfterLateUpdate();
                }
            }

            /// <summary>
            /// 更新処理を上書き
            /// </summary>
            protected override void OnProcess()
            {
                //参照先と適用先のボーンが一致することが必要
                if (_linkBoneDataDictionary.IsNullOrEmpty())
                {
                    Debug.LogError("連動する骨情報がないため、連動処理が実行できません！");
                    return;
                }

                foreach (var linkBoneSet in _linkBoneDataDictionary)
                {
                    var bodyBone = linkBoneSet.Key;
                    var targetBone = linkBoneSet.Value;

                    if (bodyBone != null && targetBone != null)
                    {
                        LinkProcess(bodyBone, targetBone);
                        OnProcess(bodyBone, targetBone);
                    }
                }
            }

            /// <summary>
            /// 更新時の処理
            /// </summary>
            private void LinkProcess(Transform baseBone, Transform targetBone)
            {
                // #141870 0blockに設定されると軍手のスケールが0になってしまう件の対応
                // _isCurrentFrameReferenceBoneScaleCachedがtrueになるタイミング的に
                // この関数でtrueなら1フレームで2回以上軍手の処理が呼ばれている事になるが
                // 2回目以降は指を消した後なので指のスケールのリセットを挟む
                if (IsHideReferenceBone && _isCurrentFrameReferenceBoneScaleCached)
                {
                    ResetReferenceCharacterBoneScale();
                }
                
                if (IsLinkPosition)
                {
                    targetBone.localPosition = baseBone.localPosition;
                }

                if (IsLinkRotation)
                {
                    targetBone.localRotation = baseBone.localRotation;
                }

                if (IsLinkScale)
                {
                    targetBone.localScale = baseBone.localScale;
                }
            }

            /// <summary>
            /// 参照元の骨のスケールを戻す
            /// </summary>
            private void ResetReferenceCharacterBoneScale()
            {
                if (_linkBoneDataDictionary.IsNullOrEmpty())
                {
                    return;
                }

                foreach (var linkBoneSet in _linkBoneDataDictionary)
                {
                    var referenceBone = linkBoneSet.Key;

                    if (referenceBone != null && _referenceBoneScaleCacheDictionary.TryGetValue(referenceBone, out var scale))
                    {
                        // キャッシュしておいた隠す前の値を再適用
                        referenceBone.localScale = scale;
                    }
                }
                // キャッシュを適用したので更新を可能な状態にする
                _isCurrentFrameReferenceBoneScaleCached = false;
            }

            /// <summary>
            /// 参照元の骨を非表示に
            /// </summary>
            private void HideReferenceCharacterBone()
            {
                if (_hideReferenceBoneDataDictionary.IsNullOrEmpty())
                {
                    return;
                }

                var isReferenceBoneScaleCached = false;
                foreach (var linkBoneSet in _hideReferenceBoneDataDictionary)
                {
                    var referenceBone = linkBoneSet.Key;
                    if (referenceBone == null)
                    {
                        continue;
                    }

                    // 非表示にする前の値をキャッシュしておく
                    if (!_isCurrentFrameReferenceBoneScaleCached)
                    {
                        _referenceBoneScaleCacheDictionary[referenceBone] = referenceBone.localScale;
                        isReferenceBoneScaleCached = true;
                    }
                    referenceBone.transform.localScale = Math.VECTOR3_ZERO;
                }

                // キャッシュが行われたら同一フレームで再びキャッシュしないようにフラグを立てる
                if (!_isCurrentFrameReferenceBoneScaleCached)
                {
                    _isCurrentFrameReferenceBoneScaleCached = isReferenceBoneScaleCached;
                }
            }

            /// <summary>
            /// リンクすべき骨を設定
            /// </summary>
            public abstract void SetupLinkBone(Func<string, Transform> getLinkBone, ModelController owner);

            /// <summary>
            /// 骨連動処理を行う際に参照先の骨を非表示する対象骨であるかどうか
            /// 判定処理は継承先で決める
            /// </summary>
            /// <param name="charaBone"></param>
            /// <returns></returns>
            protected virtual bool IsHideTargetCharacterBone(Transform charaBone) => _hideReferenceBoneDataDictionary.ContainsKey(charaBone);

            /// <summary>
            /// Process処理実装後の追加処理
            /// 継承先に応じて実装
            /// </summary>
            /// <param name="charaBone"></param>
            /// <param name="propBone"></param>
            protected virtual void OnProcess(Transform charaBone, Transform propBone) { }

#if CYG_DEBUG
            //実際に使わない親の実装を隠蔽して開発段階で警告を出してあげる
            public new void AddTransform(Transform transform) => Debug.LogWarning("LinkBoneではこちらの関数を利用しないでください！");

            public new void AddTransform(ModelControllerBehaviour owner, string nodeName) => Debug.LogWarning("LinkBoneではこちらの関数を利用しないでください！");
#endif
        }
    }
}
