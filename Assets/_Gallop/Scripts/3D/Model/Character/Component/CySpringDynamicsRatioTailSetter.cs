#if UNITY_EDITOR
#define CHECK_INTEGRATED_SUBMESH
#endif

using System;
using System.Collections.Generic;
using System.Linq;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;
using static Gallop.StaticVariableDefine.CG3D.CySpringDynamicsRatioTailSetterStatic;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// 尻尾のCySpringDynamicsRatioを変更する
    /// </summary>
    public class CySpringDynamicsRatioTailSetter
    {
        private const string IGNORE_CYSPRING_DYNAMICS_RATIO_TARGET_NAME_PARTS_1 = CharaNodeName.SP_HI_TAIL0_B_00;
        private const string IGNORE_CYSPRING_DYNAMICS_RATIO_TARGET_NAME_PARTS_2 = CharaNodeName.SP_HI_TAIL0_B_01;

        /// <summary>
        /// 揺れものコントローラー
        /// </summary>
        protected CySpringController _cySpringController = null;

        /// <summary>
        /// アニメーションの時にDynamicsが変更されるBone
        /// </summary>
        private List<CySpringBoneBase> _cySpringDynamicsTailBoneAll = null;

        /// <summary>
        /// アニメーション対象か(全体)
        /// </summary>
        private bool[] _cySpringDynamicsRatioTailAnimationTarget;

        /// <summary>
        /// 尻尾先端のCyspringRootBoneのインデックス
        /// </summary>
        protected int[] _cySpringDynamicsTailBoneIndex = null;

        /// <summary>
        /// _cySpringDynamicsTailBoneの登録数
        /// </summary>
        private int _cySpringDynamicsTailBoneNum = 0;

        /// <summary>
        /// 尻尾CySpringのブレンド率
        /// </summary>
        protected float _cySpringTailDynamicsRatio = 1.0f;

        /// <summary>
        /// 尻尾の初期骨数(Tail_)
        /// </summary>
        private int _cySpringDynamicsTailTotalBoneNum = 5;

        /// <summary>
        /// 尻尾の根本骨
        /// </summary>
        private string[] _cySpringAnimTailBoneNameArray = null;

        /// <summary>
        /// 尻尾のDynamicRatioを変更中か？
        /// </summary>
        public bool IsDurationDynamicsRatioTail = false;

        /// <summary>
        /// 尻尾のDynamicRatioのブレンド補間を止めるフラグ
        /// </summary>
        public bool _stopBlendTweenDynamicsRatioTail = false;

        /// <summary>
        /// Tailの対象を列挙する
        /// </summary>
        /// <param name="boneName"></param>
        /// <returns></returns>
        public static bool IsCySpringDynamicRatioTarget(string boneName)
        {
            if (boneName.Contains(CharaNodeName.TAIL_PREFIX))
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// DynamicsRatioにCySpringの影響度を設定する
        /// </summary>
        /// <param name="ratio"></param>
        public static void SetCySpringDynamicsRatioAction(float ratio, CySpringDynamicsRatioTailSetter obj,
            int cySpringDynamicsTailTotalBoneNum)
        {
            // 全ボーンをループで回して, 個別変更対象とその他で処理を分ける
            for (int i = 0; i < cySpringDynamicsTailTotalBoneNum; i++)
            {
                bool isExist = false;

                // 個別変更対象が必ずしもi=0,1にあるとは限らないのでチェックする
                for (int j = 0; j < obj._cySpringDynamicsTailBoneIndex.Length; j++)
                {
                    if (obj._cySpringDynamicsTailBoneIndex[j] == i)
                    {
                        isExist = true;
                        break;
                    }
                }

                if (isExist)
                {
                    // 個別変更の対象なら更新フラグに応じて変更かける
                    if (obj._cySpringDynamicsRatioTailAnimationTarget[i])
                        obj._cySpringDynamicsTailBoneAll[i].DynamicsRatio = ratio;
                }
                else
                {
                    // 残りのボーンは全てCySpringに委ねる
                    // このやり方では瞬時にDynamicRatioが変化する (急に変化して都合悪いなら別途更新関数が必要)
                    obj._cySpringDynamicsTailBoneAll[i].DynamicsRatio = ModelController.CySpringDynamicsRatio;
                }
            }
        }

        /// <summary>
        /// DynamicsRatioにCySpringの影響度を設定する(全体)
        /// </summary>
        /// <param name="ratio"></param>
        public static void SetDynamicsRatioTailAllAction(float ratio, CySpringDynamicsRatioTailSetter obj,
            int cySpringDynamicsTailTotalBoneNum)
        {
            int count = obj._cySpringDynamicsTailBoneAll.Count;
            for (int i = 0; i < count; i++)
            {
                if (obj._cySpringDynamicsRatioTailAnimationTarget[i])
                    obj._cySpringDynamicsTailBoneAll[i].DynamicsRatio = ratio;
            }
        }

        /// <summary>
        /// 初期化。
        /// </summary>
        public void Init(CySpringController cySpringController, int cySpringDynamicsTailTotalBoneNum, string[] cySpringAnimTailBoneNameArray)
        {
            _cySpringController = cySpringController;
            _cySpringDynamicsTailTotalBoneNum = cySpringDynamicsTailTotalBoneNum;
            _cySpringAnimTailBoneNameArray = cySpringAnimTailBoneNameArray;
            IsDurationDynamicsRatioTail = false;
        }

        /// <summary>
        /// 全ての尻尾に対してDynamicsRatioを変更する
        /// </summary>
        /// <param name="targetRatio"></param>
        /// <param name="duration"></param>
        public void SetDynamicsRatioTailAll(float targetRatio, float duration)
        {
            SetDynamicsRatioTail(targetRatio, duration, SetAllCySpringDynamicsRatioCallback);
        }

        /// <summary>
        /// 尻尾のブレンド補間を停止
        /// </summary>
        public void StopBlendTween()
        {
            SetDynamicsRatioTail(1,0);
            _stopBlendTweenDynamicsRatioTail = true;
        }

        /// <summary>
        ///  Tailの動きにおけるCySpringの割合を設定する
        /// </summary>
        /// <param name="targetRatio">targetRatioが0.0fの時はモーションデータで動く。1.0fの時はシミュレーションでのみ動く。</param>
        /// <param name="duration"></param>
        public void SetDynamicsRatioTail(float targetRatio, float duration, System.Action<float, CySpringDynamicsRatioTailSetter, int> setDynamicsRatioAction = null)
        {
            //会話モデルの場合は、全体に適用する場合と一部に適用する場合の2パターン存在する
            {
                if (_cySpringDynamicsTailBoneNum == 0)
                {
                    _cySpringDynamicsTailBoneAll = new List<CySpringBoneBase>(_cySpringDynamicsTailTotalBoneNum);
                    _cySpringDynamicsTailBoneIndex = new int[_cySpringAnimTailBoneNameArray.Length];
                    _cySpringController.FindSpring(_cySpringDynamicsTailBoneAll, IsCySpringDynamicRatioTargetCallback, CySpringController.Parts.Tail);
                    //対象を増やす場合、_cySpringDynamicsTailBone.Lengthを超えないように注意する事
                    int index = 0, count = _cySpringDynamicsTailBoneAll.Count;
                    for (int i = 0; i < count; i++)
                    {
                        if (_cySpringAnimTailBoneNameArray.Contains(_cySpringDynamicsTailBoneAll[i].BoneName))
                        {
                            _cySpringDynamicsTailBoneIndex[index] = i;
                            index++;
                        }
                    }
                    _cySpringDynamicsRatioTailAnimationTarget = new bool[count];
                    _cySpringDynamicsTailBoneNum = index;
                }

                if (_cySpringDynamicsTailBoneNum > 0)
                {
                    if (setDynamicsRatioAction == null)
                        setDynamicsRatioAction = SetCySpringDynamicsRatioCallback;

                    var start = _cySpringTailDynamicsRatio;
                    var end = targetRatio;
                    for (int i = 0; i < _cySpringDynamicsRatioTailAnimationTarget.Length; i++)
                    {
                        float ratio = _cySpringDynamicsTailBoneAll[i].DynamicsRatio;
                        //今回の対象と数値が違うものだけをDynamicsRatioの変更対象とする
                        _cySpringDynamicsRatioTailAnimationTarget[i] = !Math.IsFloatEqualLight(ratio, end);
                    }

                    _cySpringTailDynamicsRatio = targetRatio;

                    //ブレンドをしたくないとき
                    if (_stopBlendTweenDynamicsRatioTail)
                    {
                        IsDurationDynamicsRatioTail = false;
                        return;
                    }

                    IsDurationDynamicsRatioTail = true;
                    DG.Tweening.DOTween.To(
                        ratio =>
                        {
                            setDynamicsRatioAction(ratio, this, _cySpringDynamicsTailTotalBoneNum);
                        },
                        start,
                        end,
                        duration
                    ).onComplete = () => { IsDurationDynamicsRatioTail = false; };
                }
            }
        }
    }
}
