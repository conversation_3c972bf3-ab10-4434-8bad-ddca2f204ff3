using UnityEngine;
using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 小物コントローラー
    /// Miniキャラはいろんな画面で使われ、小物の存在もアニメーションが再生リクエストされた瞬間にわかるので
    /// 事前に読み込まず、再生時にアニメーションをセットする方式をとっている。
    /// </summary>
    public class MiniCharaPropController : CharaPropController
    {
        public void OverrideAnimation(string clipName, AnimationClip newClip, string stateName)
        {
            // 使用するステートとして登録する。
            _hasStateList.Add(Animator.StringToHash(stateName));

            _animatorOverride[clipName] = newClip;
        }

    }
}
