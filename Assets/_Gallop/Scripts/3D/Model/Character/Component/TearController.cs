using UnityEngine;
using System.Collections.Generic;
using Gallop.RenderPipeline;
using Gallop.MotionSe;
using static Gallop.StaticVariableDefine.CG3D.TeardropController;

namespace Gallop
{
    namespace Model.Component
    {
        /// <summary>
        /// 涙制御
        /// </summary>
        public class TearController : ModelComponentBase
        {
            #region 涙流しクラス

            private class TeardropController
            {
                private const string TEARDROP_ANIM_STATE_NAME = "tear";
                private const string TEARDROP_ANIM_KEY = "anm_tear{0:000}_00";
                private const string TEARDROP_ANIM_DEFAULT_NAME = "anm_tear000_00";
                private const string TEARDROP_ATTACH_TYPE_KEY = "attach_type";
                private const string TEARDROP_TEXTURE_WIDTH_KEY = "texture_width";
                private const string TEARDROP_TEXTURE_HEIGHT_KEY = "texture_height";
                private const string TEARDROP_TEXTURE_PART_WIDTH_KEY = "texture_part_width";
                private const string TEARDROP_TEXTURE_PART_HEIGHT_KEY = "texture_part_height";
                private const string TEARDROP_LOOP_KEY = "loop";
                private const string TEARDROP_DEFAULT_ANIM_KEY = "default_animation";
                private const string TEARDROP_NORMALIZE_MIN_TIME_KEY = "normalize_min_time";
                private const string TEARDROP_NORMALIZE_MAX_TIME_KEY = "normalize_max_time";
                private const int TEARDROP_TEXTURE_FPS = 30;
                private const float VISIBLE_FADE_TIME = 0.2f;

                private class TeardropAttacher
                {
                    private enum Attach
                    {
                        Type00 = 0,
                        Type01,
                    };
                    public virtual void Start(ref TeardropInfo info) { }
                    public virtual void Update(ref TeardropInfo info) { }

                    public static TeardropAttacher Create(int id)
                    {
                        switch ((Attach)id)
                        {
                            case Attach.Type00: { return new Attach00(); }
                            case Attach.Type01: { return new Attach01(); }
                            default:
                                {
                                    Debug.LogError("新しい涙のattach_type[" + id + "]が指定されています。プログラム対応が必要です。");
                                    break;
                                }
                        }
                        return null;
                    }
                }
                /// <summary>
                /// 涙を１本流す。
                /// </summary>
                private class Attach00 : TeardropAttacher
                {
                    public override void Start(ref TeardropInfo info)
                    {
                        UpdateTransform(ref info);
                    }
                    public override void Update(ref TeardropInfo info)
                    {
                        if (!info.ModelController.IsEyeBlinking())
                        {
                            UpdateTransform(ref info);
                        }
                    }
                    private void UpdateTransform(ref TeardropInfo info)
                    {
                        //fixNomalizeTimeが0の場合は位置は発動時点の位置、スケールは割合を移動値を考慮したものを使用、
                        //1に近いほど位置は顎位置を考慮したデフォルト位置、スケールもデフォルトのものを使用しています。

                        // ---------
                        //NomalizeTimeの算出

                        //(info.NormalizeMaxTime - info.NormalizeMinTime) はPrepareModelでの処理により0になることは無い

                        AnimatorStateInfo stateInfo = info.TeardropAnimator.GetCurrentAnimatorStateInfo(0);

                        float fixNomalizeTime =
                            Mathf.Clamp((stateInfo.normalizedTime - info.NormailzeMinTime) / (info.NormailzeMaxTime - info.NormailzeMinTime), 0f, 1f);

                        // ---------
                        //位置の算出

                        Vector3 teardropDstLocalPosition = info.TeardropDefaultStartPos03;

                        teardropDstLocalPosition.y += info.TeardropStartChinTrs.localPosition.y - info.TeardropDefaultStartChinPos.y;
                        teardropDstLocalPosition.z += info.TeardropStartChinTrs.localPosition.z - info.TeardropDefaultStartChinPos.z;

                        info.TeardropTrs.localPosition =
                            Vector3.Lerp(info.TeardropStartTrs03.localPosition, teardropDstLocalPosition, fixNomalizeTime);

                        // ---------
                        //スケールの算出

                        // ChinとEye_tear_attach_03が最初の状態から動いた割合を涙のローカルスケールとする
                        Vector3 teardropLocalScale = info.TeardropTrs.localScale;
                        Vector3 teardropDstLocalScale = Vector3.one;

                        //TeardropStartTrs03は目の付近のオブジェクトなのでX座標が0になることは無い
                        teardropLocalScale.x =
                            Mathf.Abs(info.TeardropStartTrs03.localPosition.x) /
                            Mathf.Abs(info.TeardropDefaultStartPos03.x);

                        //TeardropStartTrs03は目の付近のオブジェクトであり、Chinは顎下のオブジェクトなのでY座標差分が0になることは無い
                        teardropLocalScale.y =
                            Mathf.Abs(info.TeardropStartTrs03.localPosition.y - info.TeardropStartChinTrs.localPosition.y) /
                            Mathf.Abs(info.TeardropDefaultStartPos03.y - info.TeardropDefaultStartChinPos.y);

                        //デフォルトとの差分が微小であり、そのままだと想定より大きい値となるので、0.1fを足して補正
                        teardropLocalScale.z =
                            (Mathf.Abs(info.TeardropStartTrs03.localPosition.z - info.TeardropStartChinTrs.localPosition.z) + 0.1f) /
                            (Mathf.Abs(info.TeardropDefaultStartPos03.z - info.TeardropDefaultStartChinPos.z) + 0.1f);

                        if (info.IsReverse)
                        {
                            teardropLocalScale.x *= -1;
                            teardropDstLocalScale.x *= -1;
                        }

                        info.TeardropTrs.localScale =
                            Vector3.Lerp(teardropLocalScale, teardropDstLocalScale, fixNomalizeTime);
                    }
                }
                /// <summary>
                /// 涙をうるうるためた後流す。
                /// </summary>
                private class Attach01 : TeardropAttacher
                {
                    public override void Start(ref TeardropInfo info)
                    {
                        info.Joint000 = info.TeardropTrs.Find("joint000");
                        info.Joint001 = info.TeardropTrs.Find("joint000/joint001");
                        info.Joint002 = info.TeardropTrs.Find("joint000/joint002");
                        UpdateJoint(ref info);
                    }
                    public override void Update(ref TeardropInfo info)
                    {
                        UpdateJoint(ref info);
                    }
                    private void UpdateJoint(ref TeardropInfo info)
                    {
                        //座標を合わせる
                        info.Joint000.position = info.TeardropStartTrs02.position;
                        info.Joint001.position = info.TeardropStartTrs01.position;
                        info.Joint002.position = info.TeardropStartTrs03.position;
                        //涙にスケールをかけられるようにする
                        info.Joint000.localScale = info.TeardropStartTrs02.localScale;
                        info.Joint001.localScale = info.TeardropStartTrs01.localScale;
                        info.Joint002.localScale = info.TeardropStartTrs03.localScale;
                    }
                }

                private struct TeardropInfo
                {
                    // 作成時に決まって保存されている値。
                    public int Id;
                    public TeardropAttacher Attacher;
                    public ModelController ModelController;
                    public GameObject TeardropAnimObj;
                    public Animator TeardropAnimator;
                    public AnimatorOverrideController AnimatorOverride;
                    public float TeardropAnimLength;
                    public SkinnedMeshRenderer TeardropRenderer;
                    public MaterialPropertyBlock TeardropMaterialBlock;
                    public Material TeardropMat;
                    public Transform TeardropTrs;
                    public int TextureWidthNum;
                    public float TextureU;
                    public float TextureV;
                    public bool IsLoop;
                    public float NormailzeMinTime;
                    public float NormailzeMaxTime;

                    // 再生ごとに更新される値。
                    public bool IsStarted;
                    public bool IsReverse;
                    public Transform TeardropStartTrs01;
                    public Transform TeardropStartTrs02;
                    public Transform TeardropStartTrs03;
                    public Transform TeardropStartChinTrs;
                    public Vector3 TeardropDefaultStartPos01;
                    public Vector3 TeardropDefaultStartPos02;
                    public Vector3 TeardropDefaultStartPos03;
                    public Vector3 TeardropDefaultStartChinPos;
                    public Transform Joint000;
                    public Transform Joint001;
                    public Transform Joint002;

                    public bool IsNull()
                    {
                        return Id < 0;
                    }
                    public static TeardropInfo Null()
                    {
                        var ret = new TeardropInfo();
                        ret.Id = -1;
                        return ret;
                    }
                    public string GetAnimKey()
                    {
                        return string.Format(TEARDROP_ANIM_KEY, Id);
                    }
                }

                private bool _isTeardrop = false;
                private ModelController _modelController = null;
                private TeardropStartPosInfo _posInfo = null;
                private bool _isTeardropUpdate = false;
                private int _teardropRequestId = -1;
                private bool _isTeardropRequestReverse = false;
                private Shader _requestShader = null;
                private Dictionary<int, TeardropInfo> _createdDic = new Dictionary<int, TeardropInfo>();
                private Transform _root = null;
                private TeardropInfo _curPlayInfo = TeardropInfo.Null();
                private bool _isVisible = false;
                private float _fadeTime = 0f;
                private float _alpha = 0f;          // アニメーションで設定された透明度
                private float _mulAlpha = 1.0f;     // 乗算用の透明度 (StoryTimelineのFaceEffectClipで使用中)
                private float _speed = 1.0f;
                private Transform _ctrlLocator = null;
                private Color _color = GameDefine.COLOR_WHITE;  // UnlitTearシェーダーではRGBしか参照しない点に注意

                // 外部から色と透明度を直指定 (LiveCutInで使用中)
                // こちらで表示する場合は毎フレーム呼び続ける必要があります。
                private bool _isOrder = false;
                private float _orderAlpha = 1f;
                private float _orderNormalizedTime = 0f;
                private Color _orderColor = GameDefine.COLOR_WHITE;

                /// <summary> RenderQueue </summary>
                private int _renderQueue;

                public void Initialize(ModelController modelController, TeardropStartPosInfo posInfo)
                {
                    _modelController = modelController;
                    _posInfo = posInfo;
                    _root = _modelController.FindTransform(CharaNodeName.Head);
                    _curPlayInfo = TeardropInfo.Null();
                    _isTeardrop = true;
                }
                public void InitDrivenKey(Transform locator)
                {
                    _ctrlLocator = locator;
                }

                //URP:置き換え対応
                /// <summary>
                /// 破棄処理
                /// </summary>
                public void Destroy()
                {
                    foreach(var value in _createdDic.Values)
                    {
                        var material = value.TeardropMat;
                        RenderUtils.Destroy(ref material);
                    }
                    _createdDic.Clear();
                }

                public void Update()
                {
                    if (_ctrlLocator != null && _ctrlLocator.localScale.x > Math.EPSILON)
                    {
                        // ロケーターから制御。
                        _teardropRequestId = -1;
                        UpdateFromLocator();
                    }
                    else if (_isOrder)
                    {
                        // 指定されたパラメータで更新。
                        _teardropRequestId = -1;
                        UpdateFromOrder();
                    }
                    else
                    {
                        // 自身で更新。
                        if (_teardropRequestId >= 0 && !_modelController.IsEyeBlinking())
                        {// まばたき終わったので再生。
                            PlayTeardrop(_teardropRequestId, _isTeardropRequestReverse, true);
                            _teardropRequestId = -1;
                        }
                        if (_isTeardropUpdate)
                        {
                            UpdateTeardrop();
                        }
                    }
                }
                /// <summary>
                /// 制御パラメータを更新。こちらで表示する場合は毎フレーム呼び続ける必要があります。
                /// </summary>
                public void UpdateParameter(float alpha, float normalizedTime, Color color)
                {
                    _orderAlpha = alpha;
                    _orderNormalizedTime = normalizedTime;
                    _orderColor = color;
                    _isOrder = true;
                }

                /// <summary>
                /// RenderQueueを設定する
                /// </summary>
                /// <param name="renderQueue">RenderQueue</param>
                public void SetRenderQueue(int renderQueue)
                {
                    _renderQueue = renderQueue;

                    if (!_curPlayInfo.IsNull() && _curPlayInfo.TeardropMat != null)
                    {
                        _curPlayInfo.TeardropMat.renderQueue = _renderQueue;
                    }
                }
                
                public void PrepareModel(int id, bool isReverse)
                {
                    if (IsCreated(id, isReverse))
                    {
                        // 既に作られてる。
                        return;
                    }
                    TeardropInfo info;
                    if (!_createdDic.TryGetValue(id, out info))
                    {
                        // キャラ共通で使用するモデルなのでシーン紐づけで共通で使いまわす。
                        var teardropAnimPfb = ResourceManager.LoadOnScene<GameObject>(ResourcePath.GetTeardropAnimModelPath(id));
                        if (teardropAnimPfb == null)
                        {
                            Debug.LogError($"涙モデルのロード失敗: Id={id}\nPath={ResourcePath.GetTeardropAnimModelPath(id)}");
                            return;
                        }
                        var assetHolder = teardropAnimPfb.GetComponent<AssetHolder>();
                        if (assetHolder == null)
                        {
                            Debug.LogError("アセットホルダーが取得できない");
                            return;
                        }
                        info = new TeardropInfo();
                        info.Id = id;
                        info.Attacher = TeardropAttacher.Create(Math.Round(assetHolder.GetValue(TEARDROP_ATTACH_TYPE_KEY)));
                        int textureWidth = Math.Round(assetHolder.GetValue(TEARDROP_TEXTURE_WIDTH_KEY));
                        int textureHeight = Math.Round(assetHolder.GetValue(TEARDROP_TEXTURE_HEIGHT_KEY));
                        int texturePartWidth = Math.Round(assetHolder.GetValue(TEARDROP_TEXTURE_PART_WIDTH_KEY));
                        int texturePartHeight = Math.Round(assetHolder.GetValue(TEARDROP_TEXTURE_PART_HEIGHT_KEY));
                        if (textureWidth <= 0 || textureHeight <= 0 || texturePartWidth <= 0 || texturePartHeight <= 0 || info.Attacher == null)
                        {// データ整合性チェック。
                            // Attacherがnullの場合はTeardropAttacher.Createでエラーログが出るので割愛
                            Debug.LogError($"データ不整合: textureWidth={textureWidth}, textureHeight={textureHeight}, texturePartWidth={texturePartWidth}, texturePartHeight={texturePartHeight}");
                            return;
                        }
                        info.TextureWidthNum = textureWidth / texturePartWidth;
                        info.TextureU = (float)texturePartWidth / (float)textureWidth;
                        info.TextureV = (float)texturePartHeight / (float)textureHeight;
                        info.IsLoop = Math.Round(assetHolder.GetValue(TEARDROP_LOOP_KEY)) >= 1;

                        float normalizeMinTime = assetHolder.GetValue(TEARDROP_NORMALIZE_MIN_TIME_KEY);
                        float normalizeMaxTime = assetHolder.GetValue(TEARDROP_NORMALIZE_MAX_TIME_KEY);

                        if (normalizeMaxTime <= 0f)
                        {
                            normalizeMaxTime = 1f;
                        }

                        if (normalizeMinTime >= normalizeMaxTime)
                        {
                            normalizeMinTime = 0f;
                            normalizeMaxTime = 1f;
                        }

                        info.NormailzeMinTime = normalizeMinTime;
                        info.NormailzeMaxTime = normalizeMaxTime;

                        info.ModelController = _modelController;

                        info.TeardropAnimObj = GameObject.Instantiate(teardropAnimPfb);
                        info.TeardropAnimObj.SetLayerRecursively(GraphicSettings.GetLayer(GraphicSettings.LayerIndex.LayerCHAR));
                        info.TeardropAnimObj.transform.SetParent(_root, false);
                        info.TeardropAnimator = info.TeardropAnimObj.GetComponent<Animator>();
                        info.TeardropAnimator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
                        var animKey = info.GetAnimKey();
                        AnimationClip teardropAnim = null;
                        if (_modelController.IsValueFromHeadAssetHolder(animKey))
                        {
                            // キャラ個別アニメーションを使用するなら1を使用しないなら0が設定されている。
                            if (AssetHolder.BOOL_THRESHOLD < _modelController.GetValueFromHeadAssetHolder(animKey))
                            {
                                var buildInfo = _modelController.GetBuildInfo();
                                // キャラ個別データなのでキャラ紐づけでロードする。
                                teardropAnim = ResourceManager.LoadOnHash<AnimationClip>(ResourcePath.GetTeardropAnimPath(buildInfo, id), buildInfo.LoadHashKey);
                            }
                        }
                        if (teardropAnim == null && _modelController.IsExistFromHeadAssetHolder(animKey))
                        {
                            teardropAnim = _modelController.GetFromHeadAssetHolder<AnimationClip>(animKey);
                        }
                        if (teardropAnim == null)
                        {// キャラ別のものが無かったら涙についているアニメーションを使う。
                            if (assetHolder.IsExist(TEARDROP_DEFAULT_ANIM_KEY))
                            {
                                teardropAnim = assetHolder.Get<AnimationClip>(TEARDROP_DEFAULT_ANIM_KEY);
                            }
                        }
                        if (teardropAnim != null)
                        {
                            info.AnimatorOverride = new AnimatorOverrideController();
#if UNITY_EDITOR || CYG_DEBUG
                            info.AnimatorOverride.name = "TearController." + id;
#endif
                            info.AnimatorOverride.runtimeAnimatorController = info.TeardropAnimator.runtimeAnimatorController;
                            info.TeardropAnimator.runtimeAnimatorController = info.AnimatorOverride;
                            info.AnimatorOverride[TEARDROP_ANIM_DEFAULT_NAME] = teardropAnim;
                            info.TeardropAnimLength = teardropAnim.length;
                        }
                        info.TeardropRenderer = info.TeardropAnimObj.GetComponentInChildren<SkinnedMeshRenderer>();
                        //URP:置き換え対応
                        /*
#if UNITY_EDITOR
                        info.TeardropRenderer.sharedMaterial = Material.Instantiate(info.TeardropRenderer.sharedMaterial);
#endif
                        */
                        //URP:置き換え対応
                        //Instanceしたものを使う事で共有メモリを汚さない
                        info.TeardropMat = RenderUtils.GetMaterial(info.TeardropRenderer);
                        /*
                        info.TeardropMat = info.TeardropRenderer.sharedMaterial;
                        */
                        info.TeardropMat.renderQueue = _renderQueue;
                        info.TeardropMaterialBlock = new MaterialPropertyBlock();
                        info.TeardropAnimObj.SetActive(false);
                        info.TeardropTrs = info.TeardropAnimObj.transform;

                        Vector4 uv = Math.VECTOR4_ONE;
                        uv.z = 0f;
                        uv.w = 0f;
                        /*
                        //URP:置き換え対応
                        RenderUtils.SetVector(info.TeardropMat, ShaderManager.PropertyId._MainTex_ST, uv);
                        */
                        info.TeardropMaterialBlock.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex_ST), uv);
                        _createdDic.Add(id, info);
                    }
                    _curPlayInfo = info;
                    // 再生時に決まるもの
                    _curPlayInfo.TeardropStartTrs01 = (isReverse) ? _posInfo.TeardropStartRTrs01 : _posInfo.TeardropStartLTrs01;
                    _curPlayInfo.TeardropStartTrs02 = (isReverse) ? _posInfo.TeardropStartRTrs02 : _posInfo.TeardropStartLTrs02;
                    _curPlayInfo.TeardropStartTrs03 = (isReverse) ? _posInfo.TeardropStartRTrs03 : _posInfo.TeardropStartLTrs03;
                    _curPlayInfo.TeardropStartChinTrs = _posInfo.TeardropStartChinTrs;
                    _curPlayInfo.TeardropDefaultStartPos01 = (isReverse) ? _posInfo.TeardropDefaultStartRPos01 : _posInfo.TeardropDefaultStartLPos01;
                    _curPlayInfo.TeardropDefaultStartPos02 = (isReverse) ? _posInfo.TeardropDefaultStartRPos02 : _posInfo.TeardropDefaultStartLPos02;
                    _curPlayInfo.TeardropDefaultStartPos03 = (isReverse) ? _posInfo.TeardropDefaultStartRPos03 : _posInfo.TeardropDefaultStartLPos03;
                    _curPlayInfo.TeardropDefaultStartChinPos = _posInfo.TeardropDefaultStartChinPos;
                    _curPlayInfo.IsReverse = isReverse;
                    _curPlayInfo.TeardropTrs.localScale = (isReverse) ? REVERSE_SCALE : Math.VECTOR3_ONE;
                    _curPlayInfo.TeardropAnimObj.SetActive(false);
                    _curPlayInfo.IsStarted = false;
                }
                private void StartModel()
                {
                    _curPlayInfo.TeardropAnimObj.SetActive(true);
                    _curPlayInfo.TeardropAnimator.Play(TEARDROP_ANIM_STATE_NAME, 0, 0f);
                    _curPlayInfo.TeardropAnimator.speed = _speed;

                    _isTeardropUpdate = true;
                    _curPlayInfo.Attacher.Start(ref _curPlayInfo);
                    if (_requestShader != null)
                    {
                        _curPlayInfo.TeardropMat.shader = _requestShader;
                        _requestShader = null;
                    }
                    _isVisible = true;
                    _fadeTime = 0f;
                    _curPlayInfo.IsStarted = true;
                }

                private bool IsCreated(int id, bool isReverse)
                {
                    return (!_curPlayInfo.IsNull() && _curPlayInfo.Id == id && _curPlayInfo.IsReverse == isReverse);
                }
                
                /// <summary>
                /// 涙を流す。
                /// </summary>
                public void PlayTeardrop(int id, bool isReverse, bool isWaitEyeBlink)
                {
                    if (!_isTeardrop)
                    {
                        return;
                    }

                    if (isWaitEyeBlink && _modelController.IsEyeBlinking())
                    {// まばたき中はちょっと待つ。
                        _teardropRequestId = id;
                        _isTeardropRequestReverse = isReverse;
                        return;
                    }
                    
                    if (IsCreated(id, isReverse))
                    {   // #88665 同じものを再生しなおすと一瞬ちらつくので、既に同じものが再生されている場合は何もせず終了。
                        return;
                    }
                    
                    // 再生中のものがあれば強制的に止める。
                    DisableTeardrop();
                    PrepareModel(id, isReverse);
                    StartModel();
                }

                /// <summary>
                /// アニメーションスピードの単純指定
                /// </summary>
                /// <param name="speed"></param>
                public void SetSpeed(float speed)
                {
                    _speed = speed;
                }

                /// <summary>
                /// 乗算αの設定
                /// </summary>
                /// <param name="alpha"></param>
                public void SetMulAlpha(float alpha)
                {
                    _mulAlpha = alpha;
                }

                /// <summary>
                /// 現在再生中の涙を止める。フェードあり。
                /// </summary>
                public void StopTeardrop(bool immediate = false)
                {
                    if (_curPlayInfo.IsNull())
                    {
                        return;
                    }
                    _isVisible = false;
                    _fadeTime = 0f;

                    //非表示の時など即時停止させる
                    if(immediate)
                    {
                        DisableTeardrop();
                    }
                }

                /// <summary>
                /// 強制的に止める。
                /// </summary>
                private void DisableTeardrop()
                {
                    if (_curPlayInfo.IsNull())
                    {
                        return;
                    }
                    _isTeardropUpdate = false;
                    _curPlayInfo.TeardropAnimObj.SetActive(false);
                    _curPlayInfo = TeardropInfo.Null();
                }

                /// <summary>
                /// 涙を流す用更新。
                /// </summary>
                private void UpdateTeardrop()
                {
                    AnimatorStateInfo stateInfo = _curPlayInfo.TeardropAnimator.GetCurrentAnimatorStateInfo(0);
                    float normalizedTime = stateInfo.normalizedTime;
                    if (1f < normalizedTime)
                    {// 再生しきった。
                        normalizedTime = normalizedTime % 1f; // 小数点以下の数値を取得
                        if (!_curPlayInfo.IsLoop)
                        {
                            DisableTeardrop();
                            return;
                        }
                    }
                    _curPlayInfo.Attacher.Update(ref _curPlayInfo);

                    // フェード処理。
                    _fadeTime += Time.deltaTime;
                    if (_fadeTime > VISIBLE_FADE_TIME)
                    {
                        _fadeTime = VISIBLE_FADE_TIME;
                    }
                    if (_isVisible)
                    {
                        _alpha = _fadeTime / VISIBLE_FADE_TIME;
                    }
                    else
                    {
                        _alpha = 1f - (_fadeTime / VISIBLE_FADE_TIME);
                    }

                    // パラメータ更新。
                    SetParameterForMaterial(normalizedTime);

                    // 終了判定。
                    if (!_isVisible && _alpha <= 0f)
                    {
                        DisableTeardrop();
                    }
                }
                /// <summary>
                /// パラメータをマテリアルに設定。
                /// </summary>
                /// <param name="normalizedTime"></param>
                private void SetParameterForMaterial(float normalizedTime)
                {
                    // UV更新。
                    var param = Math.VECTOR4_ZERO;
                    float passTime = _curPlayInfo.TeardropAnimLength * normalizedTime;
                    int texIndex = (int)(TEARDROP_TEXTURE_FPS * passTime);
                    int x = texIndex % _curPlayInfo.TextureWidthNum;
                    int y = texIndex / _curPlayInfo.TextureWidthNum;
                    param.x = x * _curPlayInfo.TextureU;
                    param.y = -y * _curPlayInfo.TextureV;
                    // アルファ。
                    param.w = _alpha * _mulAlpha;
                    /*
                    //URP:置き換え対応
                    RenderUtils.SetVector(_curPlayInfo.TeardropMat, ShaderManager.PropertyId._TexScrollParam, param);
                    RenderUtils.SetColor(_curPlayInfo.TeardropMat, ShaderManager.PropertyId._CharaColor, _color);
                    */
                    _curPlayInfo.TeardropMaterialBlock.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._TexScrollParam), param);
                    _curPlayInfo.TeardropMaterialBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CharaColor), _color);
                    _curPlayInfo.TeardropRenderer.SetPropertyBlock(_curPlayInfo.TeardropMaterialBlock);
                }
                /// <summary>
                /// ロケーターから更新。
                /// </summary>
                private void UpdateFromLocator()
                {
                    var localScale = _ctrlLocator.localScale;
                    _alpha = localScale.x;
                    var id = Math.Round(localScale.y);
                    var speed = localScale.z;
                    var isReverse = !Math.IsFloatEqual(_ctrlLocator.localPosition.y, 0f);
                    if (_curPlayInfo.IsNull() || _curPlayInfo.Id != id || _curPlayInfo.IsReverse != isReverse)
                    {
                        // 新しく再生する。
                        PlayTeardrop(id, isReverse, false);
                    }
                    if (!_curPlayInfo.IsStarted)
                    {
                        StartModel();
                    }
                    _curPlayInfo.Attacher.Update(ref _curPlayInfo);
                    _curPlayInfo.TeardropAnimator.speed = speed;
                    AnimatorStateInfo stateInfo = _curPlayInfo.TeardropAnimator.GetCurrentAnimatorStateInfo(0);
                    float normalizedTime = stateInfo.normalizedTime;
                    if (_curPlayInfo.IsLoop)
                    {
                        normalizedTime = stateInfo.normalizedTime % 1f;
                    }
                    else
                    {
                        if (1f < normalizedTime)
                        {
                            normalizedTime = 1f;
                        }
                    }
                    SetParameterForMaterial(normalizedTime);

                    // アニメーションが切れたらその状態から消えてほしいので以下の設定。
                    _isVisible = false;
                    _isTeardropUpdate = true;
                    _fadeTime = VISIBLE_FADE_TIME;
                }
                /// <summary>
                /// 指定されたパラメータから更新。
                /// </summary>
                private void UpdateFromOrder()
                {
                    if (!_curPlayInfo.IsStarted)
                    {
                        StartModel();
                    }
                    _alpha = _orderAlpha;
                    _color = _orderColor;
                    _curPlayInfo.Attacher.Update(ref _curPlayInfo);
                    _curPlayInfo.TeardropAnimator.Play(TEARDROP_ANIM_STATE_NAME, 0, _orderNormalizedTime);
                    _curPlayInfo.TeardropAnimator.speed = 0f;
                    AnimatorStateInfo stateInfo = _curPlayInfo.TeardropAnimator.GetCurrentAnimatorStateInfo(0);
                    float normalizedTime = stateInfo.normalizedTime;
                    if (_curPlayInfo.IsLoop)
                    {
                        normalizedTime = stateInfo.normalizedTime % 1f;
                    }
                    else
                    {
                        if (1f < normalizedTime)
                        {
                            normalizedTime = 1f;
                        }
                    }
                    SetParameterForMaterial(normalizedTime);

                    // アニメーションが切れたらその状態から消えてほしいので以下の設定。
                    _isVisible = false;
                    _isTeardropUpdate = true;
                    _fadeTime = VISIBLE_FADE_TIME;
                    _isOrder = false;
                }
                /// <summary>
                /// シェーダー設定。
                /// </summary>
                public void SetShader(Shader tearShader)
                {
                    if (_isTeardrop)
                    {
                        if (_curPlayInfo.IsNull())
                        {
                            _requestShader = tearShader;
                        }
                        else
                        {
                            _curPlayInfo.TeardropMat.shader = tearShader;
                        }
                    }
                }
                /// <summary>
                /// アニメーションの長さを取得。
                /// </summary>
                public float GetAnimLength()
                {
                    if (_curPlayInfo.IsNull())
                    {
                        return 0f;
                    }
                    return _curPlayInfo.TeardropAnimLength;
                }

                public void SetRenderingLayerMask(uint renderingLayerMask)
                {
                    if (_curPlayInfo.IsNull()) return;
                    _curPlayInfo.TeardropRenderer.renderingLayerMask = renderingLayerMask;
                }

                /// <summary>
                /// レイヤーを設定する。
                /// </summary>
                /// <param name="layer"></param>
                public void SetLayer(int layer)
                {
                    if (_curPlayInfo.IsNull()) return;
                    _curPlayInfo.TeardropAnimObj.layer = layer;
                }
            }
            #endregion

            #region 定数

            public enum TearType
            {
                TearRight = 0,
                TearLeft,

                TearCount,
            };

            // キーの更新時間
            private const float ANIMATIONKEY_OFFSET = 1.0f / 5.0f;

            // 涙のフェード時間
            private const float ANIMATIONFADE_TIME = 0.2f;
            // 涙の最大数。
            public const int TEARDROP_MAX_NUM = 4;
            // 涙の制御用ロケータ名
            private const string TEARDROP_LOCATOR_OBJ_NAME = "Tear{0}_Ctrl";

            public const string TEARMESH_L_KEY = "tearmesh_l";
            public const string TEARMESH_R_KEY = "tearmesh_r";

            //URP:置き換え対応
            //涙オブジェクトのマテリアル数
            private const int MATERIAL_NUM = 2;
            private const int LEFT_MATERIAL_INDEX = 0;
            private const int RIGHT_MATERIAL_INDEX = 1;

            // 揺れのアニメーションデータ
            // 涙のUVスクロールはオミットになった
#if false
        private static readonly float[] AnimationKeyData =
        {
            -0.042f,
            0.0158f,
            -0.13f,
            0.0196f,
            -0.0594f,
            0.0825f,
            -0.0163f,
            0.0498f,
            -0.042f,
        };
#endif
            #endregion

            #region クラス

            public struct Context
            {
                public Transform DrivenKeyLocatorRootTransform;
            }

            private struct TearInfo
            {
                public GameObject _object;
                public Renderer _renderer;
            }
            /// <summary>
            /// 涙を流す初期位置情報。
            /// </summary>
            private class TeardropStartPosInfo
            {
                public Transform TeardropStartLTrs01;
                public Transform TeardropStartLTrs02;
                public Transform TeardropStartLTrs03;
                public Transform TeardropStartRTrs01;
                public Transform TeardropStartRTrs02;
                public Transform TeardropStartRTrs03;
                public Transform TeardropStartChinTrs;

                public Vector3 TeardropDefaultStartLPos01;
                public Vector3 TeardropDefaultStartLPos02;
                public Vector3 TeardropDefaultStartLPos03;
                public Vector3 TeardropDefaultStartRPos01;
                public Vector3 TeardropDefaultStartRPos02;
                public Vector3 TeardropDefaultStartRPos03;
                public Vector3 TeardropDefaultStartChinPos;
            }

            #endregion

            #region 変数

            private Context _context;
            //URP:置き換え対応
            /*
            private Material _tearLeftMaterial;
            private Material _tearRightMaterial;
            */
            private Material[] _tearMaterialArray;
            /// <summary> マスク内描画マテリアルの配列 </summary>
            private SetParamMaterial[] _drawInMaskMaterialArray;

            // 制御するパラメーター
            private TearInfo[] _tearObjects = null;
            /*
            //URP:置き換え対応
            */
            private MaterialPropertyBlock _tearMaterialBlock = null;
            private bool _isTearMaterialBlock = false;
            private Vector4 _tearOffset = Math.VECTOR4_ZERO;

            private bool _isInitialized = false;
            private bool _isActive = false;
            private bool _isVisible = false;
            public bool IsVisible
            {
                get => _isVisible;

                set
                {
                    if (IsVisibleFade)
                    {
                        //同じ場合は何もしない
                        if (value == _isVisible)
                        {
                            return;
                        }

                        if (value)
                        {
                            OnSetVisible(value);
                        }
                        //フェードアウトは中で非アクティブにする
                        PlayFadeAnimation(value);
                    }
                    else
                    {
                        OnSetVisible(value);
                    }
                }
            }

            private bool _isUpdate = false;
            private bool _isFade = false;
            private bool _isCharacterFade = false;
            
            /// <summary> 涙の描画に使われているRenderQueue </summary>
            private int _renderQueue;

            private float _animLerpRate = 0.0f;
            private float _targetFadeAlpha = 1.0f;
            private float _preFadeAlpha = 0.0f;
            private float _currentTearAlpha = 0.0f;
            private float _currentFadeTime = 0.0f;

            private TeardropController[] _teardropCtrlArray = null;
            private TeardropStartPosInfo _teardropStartPosInfo;

            //表示切り替えを行う時にフェードしながら表示するか
            public bool IsVisibleFade { get; set; } = true;

            public float TearAlpha
            {
                get => _currentTearAlpha;
                set => _currentTearAlpha = value;
            }

            #endregion

            public TearController(ref Context context)
            {
                _context = context;
            }

            #region ModelComponent

            public override void OnInitialize()
            {
                base.OnInitialize();

                var owner = GetOwner() as ModelController;
                if(owner == null)
                {
                    ModelComponent.ErrorLogModelControllerOnlyComponent(this);
                    return;
                }

                var rendererHolder = owner.GetModelComponent<RendererHolder>();
                if (rendererHolder == null)
                {
                    Debug.LogError("RendererHolder == null");
                    return;
                }

                var tearLeftObject = owner.GetFromHeadAssetHolder<GameObject>(TEARMESH_L_KEY);
                var tearRightObject = owner.GetFromHeadAssetHolder<GameObject>(TEARMESH_R_KEY);

                if (tearLeftObject == null || tearRightObject == null)
                {
                    Debug.LogWarning("必要なオブジェクトがAssetHolderに登録されていない");
                    return;
                }

                /*
                //URP:置き換え対応
                Initialize(owner, tearLeftObject, tearRightObject, rendererHolder);
                */
                Initialize(owner.MaterialBlock, owner, tearLeftObject, tearRightObject, rendererHolder);
            }

            /// <summary>
            /// マスク内描画用にマテリアルを設定する
            /// </summary>
            /// <param name="multiCameraIndex"></param>
            public void SetupDrawInMask(
                int? stencilId, UnityEngine.Rendering.CompareFunction? stencilComp, UnityEngine.Rendering.StencilOp? stencilOp
            )
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }
                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    if (_drawInMaskMaterialArray[i] != null)
                    {
                        _drawInMaskMaterialArray[i].SetMaterialParams(stencilId, stencilComp, stencilOp);
                    }
                }

            }

            /// <summary>
            /// マスク内描画用に設定していたマテリアルをリセットする
            /// </summary>
            public void ResetDrawInMask()
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }
                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    if (_drawInMaskMaterialArray[i] != null)
                    {
                        _drawInMaskMaterialArray[i].ResetMaterialParams();
                    }
                }
            }

            /// <summary>
            /// マスク内描画用に設定していたマテリアルをリセットする
            /// </summary>
            /// <param name="propertyId"> リセットしたいシェーダープロパティID </param>
            public void ResetDrawInMask(ShaderManager.PropertyId propertyId)
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }
                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    if (_drawInMaskMaterialArray[i] != null)
                    {
                        _drawInMaskMaterialArray[i].ResetMaterialParam(propertyId);
                    }
                }
            }

            /// <summary>
            /// マスク内描画用マテリアルを解放する
            /// </summary>
            private void ReleaseDrawInMask()
            {
                if (_drawInMaskMaterialArray == null)
                {
                    return;
                }
                for (int i = 0; i < _drawInMaskMaterialArray.Length; i++)
                {
                    if (_drawInMaskMaterialArray[i] != null)
                    {
                        _drawInMaskMaterialArray[i].Release();
                    }
                }

                _drawInMaskMaterialArray = null;
            }

            public override void OnDestroy()
            {
                //URP:置き換え対応
                if (_teardropCtrlArray != null)
                {
                    foreach (var tearDrop in _teardropCtrlArray)
                    {
                        tearDrop.Destroy();
                    }
                    _teardropCtrlArray = null;
                }
                ReleaseDrawInMask();

                //URP:置き換え対応
                /*
                _tearLeftMaterial = null;
                _tearRightMaterial = null;
                */
                _tearMaterialArray = null;
                base.OnDestroy();
            }

            #endregion

            /*
            //URP:置き換え対応
            private void Initialize(ModelController modelController,
                                    GameObject tearLeftObject, GameObject tearRightObject,
                                    RendererHolder rendererHolder)
            {
                _tearObjects = new TearInfo[(int)TearType.TearCount];
                //URP:置き換え対応
                //_tearMaterialBlock = materialBlock;
                //_isTearMaterialBlock = materialBlock != null;
                _tearOffset = Math.VECTOR4_ZERO;

                _isActive = false;
                _isUpdate = true;
                _isInitialized = true;
                _isFade = false;

                _teardropStartPosInfo = new TeardropStartPosInfo();
                _teardropStartPosInfo.TeardropStartLTrs01 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_01_L);
                _teardropStartPosInfo.TeardropStartLTrs02 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_02_L);
                _teardropStartPosInfo.TeardropStartLTrs03 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_03_L);
                _teardropStartPosInfo.TeardropStartRTrs01 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_01_R);
                _teardropStartPosInfo.TeardropStartRTrs02 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_02_R);
                _teardropStartPosInfo.TeardropStartRTrs03 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_03_R);
                _teardropStartPosInfo.TeardropStartChinTrs = modelController.FindTransform(CharaNodeName.CHIN);
                if (_teardropStartPosInfo.TeardropStartLTrs01 == null || _teardropStartPosInfo.TeardropStartLTrs02 == null || _teardropStartPosInfo.TeardropStartLTrs03 == null ||
                    _teardropStartPosInfo.TeardropStartRTrs01 == null || _teardropStartPosInfo.TeardropStartRTrs02 == null || _teardropStartPosInfo.TeardropStartRTrs03 == null ||
                    _teardropStartPosInfo.TeardropStartChinTrs == null)
                {
                    return;
                }
                _teardropStartPosInfo.TeardropDefaultStartLPos01 = _teardropStartPosInfo.TeardropStartLTrs01.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartLPos02 = _teardropStartPosInfo.TeardropStartLTrs02.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartLPos03 = _teardropStartPosInfo.TeardropStartLTrs03.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartRPos01 = _teardropStartPosInfo.TeardropStartRTrs01.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartRPos02 = _teardropStartPosInfo.TeardropStartRTrs02.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartRPos03 = _teardropStartPosInfo.TeardropStartRTrs03.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartChinPos = _teardropStartPosInfo.TeardropStartChinTrs.localPosition;

                _teardropCtrlArray = new TeardropController[TEARDROP_MAX_NUM];
                for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                {
                    _teardropCtrlArray[index] = new TeardropController();
                    _teardropCtrlArray[index].Initialize(modelController, _teardropStartPosInfo);
                }

                AssignControlObject(TearType.TearLeft, tearLeftObject);
                AssignControlObject(TearType.TearRight, tearRightObject);

                //シェーダー切り替え時に設定する対象マテリアル
                _tearMaterialArray = new Material[MATERIAL_NUM];
                {
                    var tearIndex = rendererHolder.FindIndex(tearLeftObject);
                    if (tearIndex >= 0)
                    {
                        _tearMaterialArray[LEFT_MATERIAL_INDEX] = rendererHolder.GetMaterial(tearIndex);
                    }
                }
                {
                    var tearIndex = rendererHolder.FindIndex(tearRightObject);
                    if (tearIndex >= 0)
                    {
                        _tearMaterialArray[RIGHT_MATERIAL_INDEX] = rendererHolder.GetMaterial(tearIndex);
                    }
                }

                //ドリブンキー制御を行うためのlocator検索
                for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                {
                    _teardropCtrlArray[index].InitDrivenKey(_context.DrivenKeyLocatorRootTransform.Find(string.Format(TEARDROP_LOCATOR_OBJ_NAME, index)));
                }
            }
            */

            private void Initialize(MaterialPropertyBlock materialBlock, ModelController modelController,
                                    GameObject tearLeftObject, GameObject tearRightObject,
                                    RendererHolder rendererHolder)
            {
                _tearObjects = new TearInfo[(int)TearType.TearCount];
                _tearMaterialBlock = materialBlock;
                _isTearMaterialBlock = materialBlock != null;
                _tearOffset = Math.VECTOR4_ZERO;

                _isActive = false;
                _isUpdate = true;
                _isInitialized = true;
                _isFade = false;

                _teardropStartPosInfo = new TeardropStartPosInfo();
                _teardropStartPosInfo.TeardropStartLTrs01 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_01_L);
                _teardropStartPosInfo.TeardropStartLTrs02 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_02_L);
                _teardropStartPosInfo.TeardropStartLTrs03 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_03_L);
                _teardropStartPosInfo.TeardropStartRTrs01 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_01_R);
                _teardropStartPosInfo.TeardropStartRTrs02 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_02_R);
                _teardropStartPosInfo.TeardropStartRTrs03 = modelController.FindTransform(CharaNodeName.EYE_TEAR_ATTACH_03_R);
                _teardropStartPosInfo.TeardropStartChinTrs = modelController.FindTransform(CharaNodeName.CHIN);
                if (_teardropStartPosInfo.TeardropStartLTrs01 == null || _teardropStartPosInfo.TeardropStartLTrs02 == null || _teardropStartPosInfo.TeardropStartLTrs03 == null ||
                    _teardropStartPosInfo.TeardropStartRTrs01 == null || _teardropStartPosInfo.TeardropStartRTrs02 == null || _teardropStartPosInfo.TeardropStartRTrs03 == null ||
                    _teardropStartPosInfo.TeardropStartChinTrs == null)
                {
                    return;
                }
                _teardropStartPosInfo.TeardropDefaultStartLPos01 = _teardropStartPosInfo.TeardropStartLTrs01.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartLPos02 = _teardropStartPosInfo.TeardropStartLTrs02.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartLPos03 = _teardropStartPosInfo.TeardropStartLTrs03.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartRPos01 = _teardropStartPosInfo.TeardropStartRTrs01.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartRPos02 = _teardropStartPosInfo.TeardropStartRTrs02.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartRPos03 = _teardropStartPosInfo.TeardropStartRTrs03.localPosition;
                _teardropStartPosInfo.TeardropDefaultStartChinPos = _teardropStartPosInfo.TeardropStartChinTrs.localPosition;

                _teardropCtrlArray = new TeardropController[TEARDROP_MAX_NUM];
                for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                {
                    _teardropCtrlArray[index] = new TeardropController();
                    _teardropCtrlArray[index].Initialize(modelController, _teardropStartPosInfo);
                }

                AssignControlObject(TearType.TearLeft, tearLeftObject);
                AssignControlObject(TearType.TearRight, tearRightObject);

                //シェーダー切り替え時に設定する対象マテリアル
                _tearMaterialArray = new Material[MATERIAL_NUM];
                {
                    var tearIndex = rendererHolder.FindIndex(tearLeftObject);
                    if (tearIndex >= 0)
                    {
                        _tearMaterialArray[LEFT_MATERIAL_INDEX] = rendererHolder.GetMaterial(tearIndex);
                        _renderQueue = _tearMaterialArray[LEFT_MATERIAL_INDEX].renderQueue;
                    }
                }
                {
                    var tearIndex = rendererHolder.FindIndex(tearRightObject);
                    if (tearIndex >= 0)
                    {
                        _tearMaterialArray[RIGHT_MATERIAL_INDEX] = rendererHolder.GetMaterial(tearIndex);
                        _renderQueue = _tearMaterialArray[RIGHT_MATERIAL_INDEX].renderQueue;
                    }
                }

                // RenderQueueの初期値をキャッシュ(両方同じはずなので片方だけ
                if (_tearMaterialArray[LEFT_MATERIAL_INDEX] != null)
                {
                    _renderQueue = _tearMaterialArray[LEFT_MATERIAL_INDEX].renderQueue;
                }
                else if (_tearMaterialArray[RIGHT_MATERIAL_INDEX] != null)
                {
                    _renderQueue = _tearMaterialArray[RIGHT_MATERIAL_INDEX].renderQueue;
                }

                // これを使用するのは現状レースモデルだけ
                if (modelController is RaceModelController)
                {
                    // RenderQueueの初期値をキャッシュ(両方同じはずなので片方だけ
                    _drawInMaskMaterialArray = new SetParamMaterial[(int)TearType.TearCount];
                    if (_tearMaterialArray[LEFT_MATERIAL_INDEX] != null)
                    {
                        _drawInMaskMaterialArray[(int)TearType.TearLeft] = new SetParamMaterial(_tearMaterialArray[LEFT_MATERIAL_INDEX], true);
                    }
                    else if (_tearMaterialArray[RIGHT_MATERIAL_INDEX] != null)
                    {
                        _drawInMaskMaterialArray[(int)TearType.TearRight] = new SetParamMaterial(_tearMaterialArray[RIGHT_MATERIAL_INDEX], true);
                    }
                }

                //ドリブンキー制御を行うためのlocator検索
                for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                {
                    _teardropCtrlArray[index].InitDrivenKey(_context.DrivenKeyLocatorRootTransform.Find(string.Format(TEARDROP_LOCATOR_OBJ_NAME, index)));
                }
            }

            /// <summary>
            /// 左右目に涙オブジェクトをアタッチする
            /// </summary>
            /// <param name="type"></param>
            /// <param name="controlObject"></param>
            private void AssignControlObject(TearType type, GameObject controlObject)
            {
                if (controlObject == null)
                {
                    return;
                }
                if (_tearObjects == null)
                {
                    return;
                }
                if (_tearObjects[(int)type]._object != null)
                {
                    Debug.Log("既にオブジェクトがアサインされています");
                    return;
                }

                int index = (int)type;
                _tearObjects[index]._object = controlObject;
                _tearObjects[index]._renderer = controlObject.GetComponent<Renderer>();
            }

            /// <summary>
            /// マテリアルプロパティブロックを反映する
            /// 外部で独自にRendererを制御している時に呼び出す、ModelControllerのSetPropertyBlockで反映している場合には不要
            /// </summary>
            /*
            //URP:置き換え対応
            */
            public void ApplyMaterialPropertyBlock()
            {
                if (_tearObjects == null)
                    return;
                for (int i = 0; i < _tearObjects.Length; i++)
                    _tearObjects[i]._renderer.SetPropertyBlock(_tearMaterialBlock);
            }

            private void UpdateTearAnimation()
            {
                //ここのフェードはシステムフェード
                if (_isFade)
                {
                    _currentTearAlpha = Mathf.Lerp(_preFadeAlpha, _targetFadeAlpha, _currentFadeTime / ANIMATIONFADE_TIME);
                    _currentFadeTime += Time.deltaTime;

                    if (_targetFadeAlpha > 0.0f)
                    {
                        if (_currentTearAlpha >= 1.0f)
                        {
                            _isFade = false;
                        }
                    }
                    else
                    {
                        if (_currentTearAlpha <= 0.0f)
                        {
                            _isFade = false;
                        }
                    }

                    if (!_isFade)
                    {
                        // フェードアウト完了
                        if (_targetFadeAlpha < 1.0f)
                        {
                            // オブジェクトごとOFF
                            OnDeactive();
                        }
                    }
                }

                // アニメーションキーの補完して更新
                _tearOffset.w = _currentTearAlpha;

                if (_isCharacterFade)
                {
                    // #125505対応: キャラがフェード中ならキャラの透明度を乗算する
                    if (GetOwner() is ModelController model)
                    {
                        _tearOffset.w *= model.GetCharacterAlpha();
                    }
                }

                // 時間のスケール考慮
                _animLerpRate += ANIMATIONKEY_OFFSET * Time.timeScale;

                if (_animLerpRate > 1.0f)
                {
                    // ループさせる
                    _animLerpRate = 0.0f;
                }
            }

            public void AfterApplyDrivenKey()
            {
                if (!_isInitialized)
                {
                    return;
                }

                if (_isActive)
                {
                    if (_isUpdate)
                    {
                        UpdateTearAnimation();
                    }
                    /*
                    //URP:置き換え対応
                    RenderUtils.SetVector(_tearMaterialArray, ShaderManager.PropertyId._TexScrollParam, _tearOffset,true);
                    */
                    if (_isTearMaterialBlock)
                    {
                        _tearMaterialBlock.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._TexScrollParam), _tearOffset);
                        ApplyMaterialPropertyBlock();
                    }
                }
                if (_teardropCtrlArray != null)
                {
                    for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                    {
                        _teardropCtrlArray[index].Update();
                    }
                }
            }

            /// <summary>
            /// 涙の表示/非表示切り替え
            /// </summary>
            /// <param name="visible"></param>
            private void OnSetVisible(bool visible)
            {
                if (!_isInitialized)
                {
                    return;
                }

                _isActive = true;
                _isVisible = visible;
                if (visible)
                {
                    _isUpdate = true;
                }

                // #86937 フェードなしで切り替える場合は透明度も更新しておく
                if (!_isFade)
                {
                    _currentTearAlpha = visible ? 1f : 0f;
                }

                for (int i = 0; i < _tearObjects.Length; i++)
                {
                    _tearObjects[i]._object.SetActive(visible);
                }
            }

            private void PlayFadeAnimation(bool fadeIn)
            {
                if (_isFade)
                {
                    //同じものを再生しようとしているか調べる
                    if (fadeIn && Math.IsFloatEqualLight(_targetFadeAlpha, 1.0f) && Math.IsFloatEqualLight(_preFadeAlpha, 0.0f))
                    {
                        return;
                    }
                    if (!fadeIn && Math.IsFloatEqualLight(_targetFadeAlpha, 0.0f) && Math.IsFloatEqualLight(_preFadeAlpha, 1.0f))
                    {
                        return;
                    }
                }

                if (fadeIn)
                {
                    _targetFadeAlpha = 1.0f;
                    _preFadeAlpha = 0.0f;
                }
                else
                {
                    _targetFadeAlpha = 0.0f;
                    _preFadeAlpha = 1.0f;
                }

                _currentTearAlpha = 0.0f;
                _currentFadeTime = 0.0f;
                _isFade = true;

                _animLerpRate = 0.0f;
            }

            private void OnDeactive()
            {
                _isActive = false;
                _isUpdate = false;
                _isFade = false;
                _isVisible = false;

                for (int i = 0; i < _tearObjects.Length; i++)
                {
                    _tearObjects[i]._object.SetActive(false);
                }
            }
            /// <summary>
            /// 涙の再生準備。
            /// </summary>
            public void PrepareTeardropModel(int slot, int id, bool isReverse)
            {
                if (_teardropCtrlArray != null && 0 <= slot && slot < _teardropCtrlArray.Length)
                {
                    _teardropCtrlArray[slot].PrepareModel(id, isReverse);
                }
            }
            /// <summary>
            /// 涙の制御パラメータを更新。こちらで表示する場合は毎フレーム呼び続ける必要があります。
            /// PrepareTeardropModelを事前に呼んでおく必要があります。
            /// </summary>
            public void UpdateTeardropParameter(int slot, float alpha, float normalizedTime, Color color)
            {
                if (_teardropCtrlArray != null && 0 <= slot && slot < _teardropCtrlArray.Length)
                {
                    _teardropCtrlArray[slot].UpdateParameter(alpha, normalizedTime, color);
                }
            }
            /// <summary>
            /// 涙を流す。
            /// </summary>
            public void PlayTeardrop(int id, int slot, bool isReverse)
            {
                if (_teardropCtrlArray != null && 0 <= slot && slot < _teardropCtrlArray.Length)
                {
                    _teardropCtrlArray[slot].PlayTeardrop(id, isReverse, true);
                }
            }
            /// <summary>
            /// スピード設定
            /// </summary>
            public void SetTeardropSpeed(int slot, float speed)
            {
                if (_teardropCtrlArray != null && 0 <= slot && slot < _teardropCtrlArray.Length)
                {
                    _teardropCtrlArray[slot].SetSpeed(speed);
                }
            }
            /// <summary>
            /// 乗算α設定（簡易）
            /// </summary>
            public void SetTeardropMulAlpha(int slot, float alpha)
            {
                if (_teardropCtrlArray != null && 0 <= slot && slot < _teardropCtrlArray.Length)
                {
                    _teardropCtrlArray[slot].SetMulAlpha(alpha);
                }
            }
            /// <summary>
            /// 涙を止める。
            /// </summary>
            /// <param name="slot"></param>
            public void StopTeardrop(int slot,bool immediate = false)
            {
                if (_teardropCtrlArray != null)
                {
                    if (0 <= slot && slot < _teardropCtrlArray.Length)
                    {
                        _teardropCtrlArray[slot].StopTeardrop(immediate);
                    }
                    else
                    {
                        for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                        {
                            _teardropCtrlArray[index].StopTeardrop(immediate);
                        }
                    }
                }
            }
            /// <summary>
            /// シェーダー設定。
            /// </summary>
            public void SetShader(Shader tearShader)
            {
                //URP:置き換え対応
                foreach(var material in _tearMaterialArray)
                {
                    if (material == null)
                        continue;
                    material.shader = tearShader;
                }
                /*
                if (_tearLeftMaterial != null)
                    _tearLeftMaterial.shader = tearShader;

                if (_tearRightMaterial != null)
                    _tearRightMaterial.shader = tearShader;
                */
                if (_teardropCtrlArray != null)
                {
                    foreach(var teardrop in _teardropCtrlArray)
                    {
                        teardrop.SetShader(tearShader);
                    }
                }
            }

            /// <summary>
            /// RenderQueueを設定する
            /// </summary>
            /// <param name="renderQueue">RenderQueue</param>
            /// <remarks>#117139対応 メガネより先に描画するためにRenderQueue変更</remarks>
            public void SetRenderQueue(int renderQueue)
            {
                _renderQueue = renderQueue;
                
                if (_tearMaterialArray[LEFT_MATERIAL_INDEX] != null)
                {
                    _tearMaterialArray[LEFT_MATERIAL_INDEX].renderQueue = _renderQueue;
                }
                if (_tearMaterialArray[RIGHT_MATERIAL_INDEX] != null)
                {
                    _tearMaterialArray[RIGHT_MATERIAL_INDEX].renderQueue = _renderQueue;
                }
                
                for (int index = 0; index < _teardropCtrlArray.Length; ++index)
                {
                    _teardropCtrlArray[index].SetRenderQueue(_renderQueue);
                }
            }
            
            /// <summary>
            /// アニメーションの長さを取得。
            /// </summary>
            public float GetTeardropAnimLength(int slot)
            {
                if (_teardropCtrlArray != null && 0 <= slot && slot < _teardropCtrlArray.Length)
                {
                    return _teardropCtrlArray[slot].GetAnimLength();
                }
                return 0f;
            }

            /// <summary>
            /// 有効な状態になっているか
            /// </summary>
            /// <returns></returns>
            public bool IsValidate() => _isInitialized;

            /// <summary>
            /// 涙(流れ、溜め)のRendererにRenderingLayerMaskを設定します。
            /// </summary>
            /// <param name="layerMask"></param>
            public void SetRenderingLayerMask(uint layerMask)
            {
                if (_teardropCtrlArray == null) return;
                foreach (var teardropCtrl in _teardropCtrlArray)
                {
                    teardropCtrl.SetRenderingLayerMask(layerMask);
                }
            }

            /// <summary>
            /// 涙(流れ、溜め)のRendererのレイヤーを設定する。
            /// </summary>
            /// <param name="layer"></param>
            public void SetLayer(int layer)
            {
                if (_teardropCtrlArray == null) return;
                foreach (var teardropCtrl in _teardropCtrlArray)
                {
                    teardropCtrl.SetLayer(layer);
                }
            }

            /// <summary>
            /// キャラがフェード中かどうかを設定
            /// </summary>
            public void SetIsCharacterFade(bool isFade)
            {
                _isCharacterFade = isFade;
            }
        }
    }
}
