using UnityEngine;
using System.Linq;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 同一アニメーショングループのリストをラップしたクラス
    /// </summary>
    public class MiniBgObjectAnimGroupInfo
    {
        public int GroupId { get; private set; }
        public int CharaNum { get; private set; }
        public int Priority { get; private set; }
        public bool IsFixedRenderOrder { get; private set; }
        public List<MiniBgObjectAnimInfo> AnimList { get; private set; }
        public List<NavigationGridObject> ObjectList { get; private set; }
        public bool IsEmpty
        {
            get
            {   //Anyとどっち速いか知らんけど
                foreach (var o in ObjectList)
                    if (o.IsEmpty() == false)
                        return false;
                return true;
            }
        }

        private Dictionary<int, Mini.MiniCharaTimelineGraph> _timelineInstanceDic = new Dictionary<int, Mini.MiniCharaTimelineGraph>();

        public MiniBgObjectAnimGroupInfo(List<MiniBgObjectAnimInfo> animList)
        {
            if (animList == null || animList.Count == 0)
                return; //ダメです

            AnimList = animList;
            ObjectList = new List<NavigationGridObject>();
            foreach(var anim in AnimList)
            {
                if (ObjectList.Contains(anim.Owner))
                    continue;

                ObjectList.Add(anim.Owner);
            }

            GroupId = animList[0].GroupId;
            CharaNum = animList[0].CharaNum;
            Priority = animList[0].Priority;
            IsFixedRenderOrder = true;
            for(int i = 0; i< animList.Count; i++)
            {
                if(animList[i].FixedRenderOrder == 0)
                {
                    //１つでも０があるなら認めない
                    IsFixedRenderOrder = false;
                    break;
                }
            }

            //CSVエラーチェック
            if (CharaNum != animList.Count)
                Debug.LogError(" 使用するキャラの数と配列の数が合わない group : " + AnimList[0].GroupId + " animName = " + AnimList[0].AnimName);
            if (AnimList.Sum(a => a.CharaNum) != CharaNum * AnimList.Count)
                Debug.LogError( " キャラの数が一致しない group : " + AnimList[0].GroupId);
            if (AnimList.Sum(a => a.Priority) != Priority * AnimList.Count)
                Debug.LogError( " 優先度が一致しない group : " + AnimList[0].GroupId);
        }

        /// <summary>
        /// タイムラインインスタンスが無いなら読み込んで返す
        /// 同一のサブグループ同士は同じインスタンスを共有する
        /// </summary>
        /// <returns></returns>
        public Mini.MiniCharaTimelineGraph LoadTimeline(int subGroupId)
        {
            if (AnimList.Count == 0)
                return null;

            if (_timelineInstanceDic.TryGetValue(subGroupId, out var instance))
            {
                return instance;
            }

            for(int i = 0, num = AnimList.Count; i < num; i++)
            {
                if(AnimList[i].SubGroupId == subGroupId)
                {
                    instance = AnimList[i].LoadTimeline();
                    break;
                }
            }

            if(instance == null)
            {
                Debug.LogError("subGroupIdが見つからない subGroup = " + subGroupId+ " , group = "+GroupId);
                return instance;
            }

            _timelineInstanceDic.Add(subGroupId, instance);
            return instance;
        }

        /// <summary>
        /// タイムラインインスタンスの破棄
        /// </summary>
        public void DestroyTimeline(int subGroupId)
        {
            if(_timelineInstanceDic.TryGetValue(subGroupId, out var instance))
            {
                instance.Clear();
                Object.Destroy(instance);
                _timelineInstanceDic.Remove(subGroupId);
            }
        }

        public void ClearCacheTimeline()
        {
            foreach (var data in _timelineInstanceDic.Values)
            {
                data.Clear();
                Object.Destroy(data);
            }
            _timelineInstanceDic.Clear();
        }
    }
}
