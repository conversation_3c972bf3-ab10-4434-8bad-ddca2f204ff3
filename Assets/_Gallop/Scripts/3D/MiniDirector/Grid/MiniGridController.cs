using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ミニ演出：グリッド配置制御
    /// </summary>
#if CYG_DEBUG
    public class MiniGridController : MiniDirectorControllerBase, IMiniDebug
#else
    public class MiniGridController : MiniDirectorControllerBase
#endif
    {

        [SerializeField]
        private NavigationGrid _grid = null;

        private Dictionary<int, NavigationGrid> _gridDic = new Dictionary<int, NavigationGrid>();
        private MiniDirector _director;

        public NavigationGrid GetGrid(int bgId)
        {
            if(_gridDic.TryGetValue(bgId, out var grid))
            {
                return grid;
            }
            return null;
        }

#region override

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="director"></param>
        public override void OnDirectorInitialize(MiniDirector director)
        {
            _director = director;

            //既存のグリッドを全削除
            foreach (var g in _gridDic)
            {
                if (g.Value == null)
                    continue;

                Destroy(g.Value.gameObject);
            }
            _gridDic.Clear();

            _grid.SetActiveWithCheck(true);
            for (int i = 0; i < director.BgArray.Length; i++)
            {
                var grid = Instantiate(_grid, _grid.transform.parent);
                var bg = director.BgArray[i];
                var offset = new Vector3(-bg.FGridOffsetX, 0, bg.FGridOffsetY) * MiniDirectorDefines.GRID_SCALE;
                var size = new Vector2Int(bg.SizeX, bg.SizeY);
                grid.CreateGrid(size, offset, MiniDirectorDefines.GRID_SCALE );
                grid.SetActiveWithCheck(false);
                _gridDic.Add(bg.Id, grid);
            }
            _grid.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 更新
        /// </summary>
        public override void OnDirectorUpdate()
        {
            if(_gridDic.TryGetValue(_director.CurrentBg.Id, out var grid))
            {
                grid.OnUpdate();
            }
        }

#endregion

        //以下デバッグメニュー

#if CYG_DEBUG

        public void SetDebugEnable(bool enable)
        {
            foreach(var kv in _gridDic)
            {
                if(kv.Key == _director.CurrentBg.Id)
                {
                    if (enable)
                    {
                        kv.Value.gameObject.SetActive(true);
                        kv.Value.DispPosition();
                    }
                    else
                    {
                        kv.Value.gameObject.SetActive(false);
                        kv.Value.HidePosition();
                    }
                }
                else
                {
                    //他のが表示されっぱなしのまま残ってしまうことが多々あるのでついでに消す。
                    kv.Value.gameObject.SetActive(false);
                    kv.Value.HidePosition();
                }
            }
        }

#endif
    }
}