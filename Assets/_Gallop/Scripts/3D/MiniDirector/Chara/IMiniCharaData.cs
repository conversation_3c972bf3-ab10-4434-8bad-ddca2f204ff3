namespace Gallop
{
    /// <summary>
    /// ミニキャラの見た目を決めるパラメータの共通インターフェース
    /// </summary>
    public interface IMiniCharaData
    {
        long UniqueId { get; }   //コレクション内部でユニークなID、育成済みデータなら育成済みIDなど
        string Name { get; }    //表示する名前
        int CharaId { get; }    //chara_data.id
        int DressId { get; }     //dress_data.id
        int DressColorId { get; }   // カラーバリエーションID
        bool IsVisibleMessage { get; }  //吹き出し出すか？
    }

#if CYG_DEBUG
    /// <summary>
    /// デバッグ用インターフェース
    /// </summary>
    public interface IMiniCharaDebugData
    {
        int DebugBgCharaMotionId { get; }
    }
#endif

    //たずなや検証用のミニキャラで使用するデータ
    [System.Serializable]
    public sealed class MiniCharaData : IMiniCharaData
    {
        public long UniqueId { get; private set; }
        public string Name { get; set; }
        public int CharaId { get; set; }
        public int DressId { get; set; }
        public int DressColorId => CharacterBuildInfo.BACKDANCER_COLOR_ID_NULL;
        public bool IsVisibleMessage => false;
        public MiniCharaData(string name, int charaId, int dressId)
        {
            Name = name;
            CharaId = charaId;
            DressId = dressId;

            _uniqueId++;
            UniqueId = _uniqueId;
        }
        private static long _uniqueId = 0;   //autoincrement
    }

    /// <summary>
    /// 料理を作る演出用キャラデータ
    /// </summary>
    public sealed class CookingMiniCharaData : IMiniCharaData
    {
        public long UniqueId { get; private set; }
        public string Name { get; set; }
        public int CharaId { get; set; }
        public int DressId { get; set; }
        public int DressColorId => CharacterBuildInfo.BACKDANCER_COLOR_ID_NULL;
        public bool IsVisibleMessage => false;
        public SingleModeScenarioCookDefine.CookingMemberIndex MemberIndex { get; set; }

        public CookingMiniCharaData(string name, int charaId, int dressId, SingleModeScenarioCookDefine.CookingMemberIndex memberIndex)
        {
            Name = name;
            CharaId = charaId;
            DressId = dressId;
            MemberIndex = memberIndex;

            //演出に登場するメンバーのインデックスを設定
            UniqueId = (int)memberIndex;
        }
    }

    //目標イベントのリザルト画面用のクラス
    [System.Serializable]
    public sealed class CookingResultMiniCharaData : IMiniCharaData
    {
        public long UniqueId { get; private set; }
        public string Name { get; set; }
        public int CharaId { get; set; }
        public int DressId { get; set; }
        public int DressColorId => CharacterBuildInfo.BACKDANCER_COLOR_ID_NULL;
        public bool IsVisibleMessage => false;
        public int Index { get; set; }
        public CookingResultMiniCharaData(string name, int charaId, int dressId, int index)
        {
            Name = name;
            CharaId = charaId;
            DressId = dressId;
            Index = index;

            _uniqueId++;
            UniqueId = _uniqueId;
        }
        private static long _uniqueId = 0;   //autoincrement
    }
}