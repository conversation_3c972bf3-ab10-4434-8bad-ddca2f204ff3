using UnityEngine;
using Gallop.Model.Component;

namespace Gallop
{
    /// <summary>
    /// ミニキャラ：全国興行の待機状態
    /// </summary>
    public class MiniCharaStateJobIdle : MiniCharaStateBase
    {
        private const string MOTION_NAME_OH = "Oh01";
        public const float WAIT_TIME = 0.6f;    // オーのアニメを待つ時間

        private MiniCharaCommand _command;
        private bool _execJob = false;

        public MiniCharaStateJobIdle(MiniCharaObject chara) : base(chara)
        {
        }

        public override void Init(MiniCharaCommand command)
        {
            _command = command;
            _execJob = false;

            if (_command.ObjectAnimInfo != null)
            {
                Chara.SetVisibleShadow(_command.ObjectAnimInfo.CharaShadow);
                // 今のところIdleは足煙を出すことはない
                Chara.SetEnableFootSmoke(false);
                if (_command.ObjectAnimInfo.PositionLocator != null && _command.ObjectAnimInfo.UseGridPosJobSelect == false)
                {
                    // 追従指定
                    Chara.SetFollowTarget(command.ObjectAnimInfo.PositionLocator);
                }
                else
                {
                    // 高さ指定
                    var charaPos = Chara.transform.localPosition;
                    charaPos.y = _command.ObjectAnimInfo.CharaPosY;
                    Chara.transform.localPosition = charaPos;
                    // 向き指定
                    Chara.Agent.SetDirection(command.ObjectAnimInfo.Direction);
                    if (_command.ObjectAnimInfo.Owner != null)
                    {
                        var objRotation = command.ObjectAnimInfo.Owner.transform.localRotation;
                        var rotation = Quaternion.Euler(NavigationGridAgent.DirectionToRotation(command.ObjectAnimInfo.Direction)) * objRotation;
                        Chara.Agent.SetRotation(rotation.eulerAngles);
                    }
                }
            }

            //146204 すでに同じモーションになっている場合2重更新を呼ばない、
            //PlayMotionで「ResetCyspring」が呼ばれてしまうため、髪の毛などの揺れ物が一瞬不自然に動く現象を回避する
            var currentMotionLabel = Chara.Model.GetCurrentMotionLabel();
            if (string.Equals(currentMotionLabel, MiniCharaStateIdle.IDLE))
            {
                return;
            }

            PlayMotion(MiniCharaStateIdle.IDLE, null, startType: MiniMotionSetController.MotionStartType.Loop);
        }

        public override void Final()
        {
            if (Chara.IsVisible)
            {
                Chara.SetVisibleShadow(true);
            }
            // 追従指定解除
            Chara.UnSetFollowTarget();
        }

        public override void Update()
        {
            var viewController = SceneManager.Instance.GetCurrentViewController<JobsHubViewController>();
            if (viewController == null)
            {
                return;
            }

            var viewId = viewController.ChildCurrentController.GetViewId();
            if (viewId == SceneDefine.ViewId.JobsConfirm && WorkDataManager.Instance.JobsData.IsExecStartMotion && _execJob == false)
            {
                // オー！をやる
                _execJob = true;
                PlayMotion(MOTION_NAME_OH, null);

                // 演出の先頭にいるキャラでボイス再生
                var chara = WorkDataManager.Instance.JobsData.PreStartInfo.MiniCharaDataList[0];
                if (chara.CharaId == Chara.CharaId)
                {
                    var systemText = AudioManager.Instance.PlaySystemVoice_JobStart(Chara.CharaId);
                    Chara.Model.PlayLipSync(systemText);
                }
            }
        }
    }
}
