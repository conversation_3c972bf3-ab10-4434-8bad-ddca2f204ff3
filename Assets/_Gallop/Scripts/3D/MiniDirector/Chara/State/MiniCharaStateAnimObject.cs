using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;
using Gallop.Model.Component;

namespace Gallop
{
    /// <summary>
    /// キャラ：オブジェクトアニメ
    /// </summary>
    public class MiniCharaStateAnimObject : MiniCharaStateBase //, ICircleCharaStateClickable, ICircleCharaStateStashable
    {

        public string MotionName => _motionName;

        private string _motionName;
        private MiniDirectorDefines.GridDirection _targetDirection;
        protected Tween _tween;
        private bool _isMotionEnd = false;
        protected bool _isCallbackRegisted = false;
        protected MiniCharaCommand _command;
        private Vector3 _targetRotation;
        protected bool _isSkipStart = false;  //初回遷移時はStartをスキップ
        protected Vector3 _defaultPosition;
        private bool _isPlayMotion = false;
        private List<MasterMiniBgCharaMotion.SEInfo> _seList;
        private List<MasterMiniBgCharaMotion.EffectInfo> _effectList;
        private float _prevMotionTime;

        public MiniCharaStateAnimObject(MiniCharaObject chara) : base(chara) { }

        //Direction - アニメーションする時の向き
        //ObjectAnimInfo.AnimName - 再生するモーション名
        public override void Init(MiniCharaCommand command)
        {
            if (command.ObjectAnimInfo == null)
            {
                Debug.LogWarning("ObjectAnimInfoの指定が必須");
                Chara.NextState();
                return;
            }

            _command = command;

            _isCallbackRegisted = false;
            _isMotionEnd = false;
            _targetDirection = command.ObjectAnimInfo.Direction;

            //オブジェクトが回転してる場合はその向きに合わす
            var owner = command.ObjectAnimInfo.Owner;
            if (owner != null)
            {
                var objRotation = command.ObjectAnimInfo.Owner.transform.localRotation;
                var rotation = Quaternion.Euler(NavigationGridAgent.DirectionToRotation(_targetDirection)) * objRotation;
                _targetRotation = rotation.eulerAngles;
            }

            Chara.SetVisibleShadow(_command.ObjectAnimInfo.CharaShadow);

            _isSkipStart = true;
            _motionName = command.ObjectAnimInfo.AnimName;

            _defaultPosition = Chara.transform.position;

            //モーション再生済みフラグ
            _isPlayMotion = false;
            _prevMotionTime = 0;

            //エフェクトの初期化
            _seList = _command.ObjectAnimInfo.SEList;
            _effectList = _command.ObjectAnimInfo.EffectList;
            ResetLoopCheck();

            PlayAnimObject();

            var footSmoke = FootSmokeEffectInfo();
            if (footSmoke != null)
            {
                Chara.CreateFootSmokeEffect(footSmoke.Path);
            }

            // StateAnimObjectではEnvParam側に設定があれば足煙を出す
            Chara.SetEnableFootSmoke(true);
            Chara.SetVisibleFootSmoke(true);
        }

        protected void PlayAnimObject(bool isResetCySpring = true)
        {
            //高さ指定
            void SetCharaPosY()
            {
                if (_command.ObjectAnimInfo.PositionLocator != null)
                    return;
                var charaPos = Chara.transform.localPosition;
                charaPos.y = _command.ObjectAnimInfo.CharaPosY;
                Chara.transform.localPosition = charaPos;
            }
            //回転指定
            void SetDirection()
            {
                if (_command.ObjectAnimInfo.PositionLocator != null)
                    return;
                //Directionによる回転値を上書きしているが、回転値がDirectionと同期しないことがあり得るので両方入れる
                Chara.Agent.SetDirection(_targetDirection);
                if (_command.ObjectAnimInfo.Owner != null)
                {
                    Chara.Agent.SetRotation(_targetRotation);
                }
            }
            //フォロワー指定
            void SetFollower()
            {
                // 第二ロケーターが設定してあったら優先する
                var locator = _command.ObjectAnimInfo.SecondPositionLocator != null
                    ? _command.ObjectAnimInfo.SecondPositionLocator
                    : _command.ObjectAnimInfo.PositionLocator;

                if (locator == null)
                    return;

                Chara.SetFollowTarget(locator);
            }

            if (_isSkipStart)
            {
                _isSkipStart = false;

                //姿勢を決定
                SetCharaPosY();
                SetDirection();
                SetFollower();

                //モーション再生開始
                PlayMotion(isResetCySpring);
                CreateMob();
                _isPlayMotion = true;
                OnPlayMotion();
                return;
            }

            var rotateDuration = 0.0f;
            if (!Math.IsFloatEqual(_targetRotation.y, Chara.Agent.transform.localRotation.y))
            {
                //回転中に直前のモーションが再生されっぱなしになるためアイドルをブレンドさせつつ停止させる
                rotateDuration = NavigationGridAgent.DEFAULT_ROTATE_DURATION_STAY;
                PlayMotion(MiniCharaStateIdle.IDLE, _command, isResetCySpring: isResetCySpring);
                OnPlayMotion();
            }

            //アニメーションする向きに回転、もともとその向きならDuration無し
            _tween = Chara.Agent.RotateTo(_targetRotation, rotateDuration, 0, onComplete: () =>
            {
                //向き終わってからモーション開始
                if (!_isCallbackRegisted)
                {
                    _isCallbackRegisted = true;
                    if (Chara.Model != null)
                    {
                        Chara.Model.RegisterEndMotionDelegate(OnMotionEnd);
                    }
                }
                //姿勢を決定
                SetCharaPosY();
                SetDirection();
                SetFollower();

                //モーション再生開始
                PlayMotion(_motionName, _command, isResetCySpring: isResetCySpring);
                CreateMob();
                _isPlayMotion = true;
                OnPlayMotion();
            });
        }

        /// <summary>
        /// モーション再生後の処理
        /// </summary>
        protected virtual void OnPlayMotion() { }

        /// <summary>
        /// モブの生成
        /// </summary>
        private void CreateMob()
        {
            if (_command == null || _command.ObjectAnimInfo == null)
                return;

            if (_command.ObjectAnimInfo.IsMob)
                return; //そもそもモブ用なら不要

            var mobList = _command.ObjectAnimInfo.GetSubGroupMobList();
            if (mobList == null)
                return;

            foreach (var mobInfo in mobList)
            {
                var mobObj = Chara.AddSlaveMob(mobInfo.Owner.Rect.position);
                var mobCommand = MiniCharaCommandLottery.CreateCommand(MiniDirectorDefines.CharaState.AnimObject, mobInfo, _command.ObjectAnimGroupInfo);
                mobObj.SetCommand(mobCommand);
            }
        }

        protected virtual void PlayMotion(bool isResetCySpring)
        {
            PlayMotion(_motionName, _command, startType: MiniMotionSetController.MotionStartType.Loop, isResetCySpring: isResetCySpring);
        }

        protected void OnMotionEnd(MasterMiniMotionSet.MiniMotionSet motion)
        {
            _isMotionEnd = true;
        }

        public override void Update()
        {
            if (_isPlayMotion)
            {
                CheckPlayEffect();
            }
            if (_isMotionEnd)
            {
                Chara.NextState();
            }
        }

        public override void Final()
        {
            if (_command.ObjectAnimInfo.IsControllTimeline)
            {
                //タイムラインの停止と破棄
                Chara.TimelineActor.Stop();
                if (_command.ObjectAnimInfo.PositionLocator != null)
                {
                    _command.ObjectAnimInfo.PositionLocator.StopAnim();
                    _command.ObjectAnimInfo.PositionLocator.ForceUpdateAnimator();
                }
                _command.ObjectAnimGroupInfo.DestroyTimeline(_command.ObjectAnimInfo.SubGroupId);
            }

            if (string.IsNullOrEmpty(_command.ObjectAnimInfo.PositionAnim) == false && _command.ObjectAnimInfo.PositionLocator != null)
            {
                //位置連動の停止
                Chara.StopPositionMotion();
            }

            _isCallbackRegisted = false;
            if (Chara.Model != null)
            {
                Chara.Model.UnRegistEndMotionDelegate(OnMotionEnd);
            }
            if (Chara.IsVisible)
            {
                Chara.SetVisibleShadow(true);
            }
            //追従指定解除
            Chara.UnSetFollowTarget();
            Chara.transform.position = _defaultPosition;
            //正面に戻す
            Chara.Agent.SetDirection(MiniDirectorDefines.GridDirection.Forward);
            if (_tween != null)
            {
                _tween.Kill();
                _tween = null;
            }
            //モブの破棄
            Chara.ClearMobList();
        }

        //SE再生チェック
        //単発モーションの場合上手く動くが、タイムラインなど時間が不定な場合に未対応
        private void CheckPlayEffect()
        {
            if (Chara.Model == null)
            {
                return;
            }
            var playTime = Chara.Model.GetCurrentMotionTime();
            //140668 モーションSEとエフェクトもループ再生する場合のみリセットをかける
            if (playTime < _prevMotionTime && Chara.IsEnableLoopMotionSEAndEffect)
            {
                //モーションがループしてるのでフラグ下げる
                ResetLoopCheck();
            }

            if (_seList != null)
            {
                foreach (var se in _seList)
                {
                    if (se.StartSec <= playTime && se.IsPlayed == false)
                    {
                        Chara.PlaySe(se.CueSheet, se.CueName);
                        se.IsPlayed = true;
                    }
                }
            }
            if (_effectList != null)
            {
                foreach (var effect in _effectList)
                {
                    if (IsFootSmokeEffect(effect))
                    {
                        continue;
                    }
                    if (effect.StartSec <= playTime && effect.IsPlayed == false)
                    {
                        Chara.Model.PlayEffect(effect.Id);
                        effect.IsPlayed = true;
                    }
                }
            }
            _prevMotionTime = playTime;
        }

        //ループフラグをリセット
        private void ResetLoopCheck()
        {
            if (_seList != null)
            {
                foreach (var se in _seList)
                    se.IsPlayed = false;
            }
            if (_effectList != null)
            {
                foreach (var eff in _effectList)
                    eff.IsPlayed = false;
            }
        }

        /// <summary>
        /// 砂煙エフェクトか
        /// </summary>
        private bool IsFootSmokeEffect(MasterMiniBgCharaMotion.EffectInfo effInfo)
        {
            var id = (MasterMiniBgCharaMotion.EffectInfo.EffectId)effInfo.Id;

            switch (id)
            {
                case MasterMiniBgCharaMotion.EffectInfo.EffectId.FootSmokeDirt:
                case MasterMiniBgCharaMotion.EffectInfo.EffectId.FootSmokeTurf:
                case MasterMiniBgCharaMotion.EffectInfo.EffectId.FootSmokeTurfLast:
                case MasterMiniBgCharaMotion.EffectInfo.EffectId.FootSmokeDirtLast:
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 砂煙エフェクトの情報取得
        /// </summary>
        private MasterMiniBgCharaMotion.EffectInfo FootSmokeEffectInfo()
        {
            if (_effectList.Count <= 0)
                return null;

            foreach (var effect in _effectList)
            {
                if (IsFootSmokeEffect(effect))
                    return effect;
            }

            return null;
        }

#if CYG_DEBUG
        /// <summary>
        /// リフレッシュ前処理
        /// </summary>
        public override void OnPreRefresh()
        {
            if (_command != null &&
                _command.IsValidTimeline())
            {
                Chara.TimelineActor.Stop();
                // ロケーターアニメーションが動いていた場合、一旦停止
                if (_command.ObjectAnimInfo.PositionLocator != null)
                {
                    _command.ObjectAnimInfo.PositionLocator.StopAnim();
                    _command.ObjectAnimInfo.PositionLocator.ForceUpdateAnimator();
                }
                // MiniCharaTimelineは１体目のキャラがロード
                // 二回目以降はキャッシュを渡すことで同じインスタンスを共有している
                // リアルタイム反映する場合は全クリア後に通常ロードをかける
                _command.ObjectAnimGroupInfo.ClearCacheTimeline();
            }
        }

        /// <summary>
        /// リフレッシュ処理
        /// </summary>
        public override void OnRefresh()
        {
            if (_command != null &&
                _command.IsValidTimeline())
            {
                // MiniCharaタイムラインで動いているオブジェクトは
                // ランタイム反映のためにデータの保存にひっかけて再読み込みさせる
                Chara.TimelineActor.Play(
                    _command.ObjectAnimGroupInfo.LoadTimeline(_command.ObjectAnimInfo.SubGroupId), Chara,
                    _command.ObjectAnimInfo.TimelineActor);
            }
        }
#endif
        //以下の処理はタッチで反応させるためにやってた処理
        //ループモーションの位置で固定されてるのでその場で立たせてモーションさせると椅子貫通して問題になる
        //やるなら座りモーションなど位置移動を含むモーションに対応する必要があります

#if false

        //クリック時に画面向いてエモート
        public void OnClick()
        {
            if (_clickCoolTime >= 0)
            {
                return;
            }

            //連続クリック防止
            _clickCoolTime = CLICK_COOL_TIME;

            var command = CircleCharaStateEmote.CreateOnTouchCommand();
            command.BlendPrevMotion = false;
            Chara.InterruptCommand(command);
        }


        #region ポーズ

        public void OnStash()
        {
            if (_tween != null)
            { 
                _tween.Kill();
                _tween = null;
            }

            //一回外す
            _isCallbackRegisted = false;
            Chara.Model.UnRegistEndMotionDelegate(OnMotionEnd);
        }

        public void OnPop()
        {
            //モーション再開
            PlayAnimObject();
        }

        #endregion

#endif
    }
}