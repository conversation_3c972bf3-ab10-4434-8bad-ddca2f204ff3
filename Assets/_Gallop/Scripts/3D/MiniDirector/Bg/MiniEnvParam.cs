using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using Gallop.CutIn.Cutt;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// ミニ環境設定
    /// </summary>
    [Serializable]
    public class MiniEnvParam : ScriptableObject
    {
        public static Color DEFAULT_CHARA_SHADOW_COLOR = new Color(38f/255f, 22f/255f, 22f/255f, 1);

        public const string SKY_MAT_PREFIX = "material_sky_";

        public int EnvId;
        public int EventId;
        public int BaseModelId;

        //天球を使用するか
        public bool UseSky = true;

        //LightMapのグローバルシェーダーパラメータ
        public Color LightmapDensityColor = GraphicSettings.DefaultLightmapDensityColor;
        public float LightmapDensity = GraphicSettings.DefaultLightmapDensity;
        public float LightmapMinDensity = GraphicSettings.DefaultLightmapMinDensity;

        // ライトの方向
        public Vector3 LightRotation = new Vector3(50f, -90f, 0f);

        //キャラクターの色に関する設定
        public Color LightProbeColor = GraphicSettings.DefaultLightmapColor;
        public Color CharaShadowColor = DEFAULT_CHARA_SHADOW_COLOR;

        // 空設定
        public float SkydomeRotationAngle = 0.3f;   //1秒間にどの程度天球が回転するか

        public MaterialTexturePair[] SwapTextures;  //テクスチャ入れ替え

        //マスターデータの取得
        public MasterMiniBg.MiniBg GetMasterData() => MasterDataManager.Instance.masterMiniBg.Get(BaseModelId);

        // アウトライン
        public MiniEnvParamCharaOutline CharaOutline = new MiniEnvParamCharaOutline();

        // 背景エフェクトプレファブリスト
        public List<GameObject> BackGroundEffectList = new List<GameObject>();

        // カメラ設定
        public MiniEnvParamCamera EnvParamCamera = new MiniEnvParamCamera();

#if UNITY_EDITOR
        public void CopyFrom(MiniEnvParam srcParam)
        {
            EnvId = srcParam.EnvId;
            BaseModelId = srcParam.BaseModelId;
            EventId = srcParam.EventId;
            LightmapDensityColor = srcParam.LightmapDensityColor;
            LightmapDensity = srcParam.LightmapDensity;
            LightmapMinDensity = srcParam.LightmapMinDensity;
            LightRotation = srcParam.LightRotation;
            LightProbeColor = srcParam.LightProbeColor;
            CharaShadowColor = srcParam.CharaShadowColor;
            UseSky = srcParam.UseSky;

            SkydomeRotationAngle = srcParam.SkydomeRotationAngle;

            SwapTextures = null;
            if (srcParam.SwapTextures != null)
            {
                var srcSwapTextues = srcParam.SwapTextures;
                SwapTextures = new MaterialTexturePair[srcSwapTextues.Length];
                for (int i = 0; i < srcSwapTextues.Length; i++)
                {
                    SwapTextures[i].materialName = srcSwapTextues[i].materialName;
                    SwapTextures[i].textureName = srcSwapTextues[i].textureName;
                }
            }

            CharaOutline.CopyFrom(srcParam.CharaOutline);

            BackGroundEffectList.Clear();
            BackGroundEffectList.AddRange(srcParam.BackGroundEffectList);

            EnvParamCamera.CopyFrom(srcParam.EnvParamCamera);
        }
#endif

#if UNITY_EDITOR
        /// <summary>
        /// ロード、エディタの場合無いなら作る
        /// </summary>
        /// <param name="baseModelId"></param>
        /// <param name="eventId"></param>
        /// <param name="envId"></param>
        /// <returns></returns>
        public static MiniEnvParam Load(int baseModelId, int eventId, int envId)
        {
            var path = ResourcePath.GetMiniEnvParamPath(baseModelId, eventId, envId);

            if (AssetBundleHelper.UsingAssetBundleResource(path))
            {
                return ResourceManager.LoadOnView<MiniEnvParam>(path);
            }

            var param = Resources.Load<MiniEnvParam>(path);
            if (param == null)
            {
                var newParam = CreateInstance<MiniEnvParam>();
                newParam.BaseModelId = baseModelId;
                newParam.EnvId = envId;
                newParam.EventId = eventId;
                AssetDatabase.CreateAsset(newParam, ResourcePath.BundleResourcesAssetsPath + path + ".asset");
                param = newParam;
            }
            return param;
        }
#endif

#if UNITY_EDITOR
        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="newParam"></param>
        public static void Save(MiniEnvParam newParam)
        {
            var path = ResourcePath.GetMiniEnvParamPath(newParam.BaseModelId, newParam.EventId, newParam.EnvId);
            if (AssetBundleHelper.UsingAssetBundleResource(path))
            {
                Debug.LogWarning("アセバン起動時は保存不可、リソースのある環境で「アセバン強制使用」をOFFにして使ってください。");
                return;
            }

            var param = Load(newParam.BaseModelId, newParam.EventId, newParam.EnvId);
            param.CopyFrom(newParam);
            EditorUtility.SetDirty(param);
            AssetDatabase.SaveAssets();
        }
#endif
    }

    [System.Serializable]
    public class MiniEnvParamCharaOutline
    {
        public KeyFloat WidthPower = new KeyFloat("OutlineWidthPower", ModelController.DEFAULT_OUTLINE_WIDTH_POWER);
        public KeyColor Color = new KeyColor("OutlineColor", GameDefine.COLOR_WHITE);
        public KeyEnum ColorBlendType = new KeyEnum("OutlineColorBlend", MiniModelController.OutlineColorBlend.Blend);

        /// <summary>
        /// マテリアルプロパティを更新するかの判定で用いる
        /// </summary>
        public bool IsValid()
        {
            return WidthPower.IsValid || Color.IsValid;
        }

#if UNITY_EDITOR
        public void CopyFrom(MiniEnvParamCharaOutline src)
        {
            WidthPower.IsValid = src.WidthPower.IsValid;
            WidthPower.Value = src.WidthPower.Value;
            Color.IsValid = src.Color.IsValid;
            Color.Value = src.Color.Value;
            ColorBlendType.IsValid = src.ColorBlendType.IsValid;
            ColorBlendType.Value = src.ColorBlendType.Value;
        }

        public void OnGUi()
        {
            WidthPower.OnGui();
            Color.OnGui();
            ColorBlendType.OnGui();
        }
#endif
    }

    [System.Serializable]
    public class MiniEnvParamCamera
    {
        public bool IsValid = false;
        public Vector3 CameraPosition;
        public Quaternion CameraRotation;
        public float CameraFov = 60.0f;

#if UNITY_EDITOR
        public void CopyFrom(MiniEnvParamCamera src)
        {
            IsValid= src.IsValid;
            CameraPosition = src.CameraPosition;
            CameraRotation = src.CameraRotation;
            CameraFov= src.CameraFov;
        }

        public void CopyFromCamera(Camera src)
        {
            CameraPosition = src.transform.position;
            CameraRotation = src.transform.rotation;
            CameraFov= src.fieldOfView;
        }

        public void CopyTpCamera(Camera dst)
        {
            dst.transform.position = CameraPosition;
            dst.transform.rotation = CameraRotation;
            dst.fieldOfView = CameraFov;
        }

        public void OnGUi()
        {
        }
#endif
    }
}
