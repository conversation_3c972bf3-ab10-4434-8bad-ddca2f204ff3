using System.Collections;
using System.Collections.Generic;
using Gallop.ImageEffect;
using Gallop.Model.Component;
using Gallop.RenderPipeline;
using UnityEngine;

namespace Gallop
{

    /// <summary>
    /// キャラクターのオーラを設定制御するクラス
    /// </summary>
    public class AuraController
    {
        private GallopImageEffect _imageEffect;

        //URP:置き換え対応
        /*
        private ImageEffect.Aura _aura;
        */


        public void Initialize(GallopImageEffect imageEffect)
        {
            if (imageEffect == null)
            {
                return;
            }

            _imageEffect = imageEffect;
        }

        public void AlterUpdate()
        {
            //URP:置き換え対応
            /*
                if (_aura != null)
                {
                    _aura.UpdateParam();
                }
            */
            float deltaTime = Time.deltaTime;
            _imageEffect?.Aura.UpdateParam(deltaTime);

        }

        /// <summary>
        /// キャラクターに対するオーラを設定
        /// </summary>
        public void SetupCharacterAura(int characterIndex, AuraParam srcParam, ModelController modelController, Camera characterCamera = null)
        {
            //URP:置き換え対応
            /*
            if (_aura == null)
            {
                return;
            }
            var auraParam = _aura.GetParam(characterIndex);
            */
            var auraParam = _imageEffect?.Aura.GetParam(characterIndex);


            // renderer登録
            if (modelController != null)
            {
                var partsHolder = modelController.GetModelComponent<CharaPartsHolder>();
                if (partsHolder != null)
                {
                    var renderers = new List<Renderer>();
                    partsHolder.GetPartsData(out var partsData);
                    if (partsData.IsBody)
                    {
                        renderers.Add(partsData.BodyRenderer);
                    }
                    if (partsData.IsBodyProp)
                    {
                        renderers.AddRange(partsData.BodyPropRendererArray);
                    }
                    if (partsData.IsFace)
                    {
                        renderers.Add(partsData.FaceRenderer);
                    }
                    if (partsData.IsHair)
                    {
                        renderers.Add(partsData.HairRenderer);
                    }
                    if (partsData.IsHairProp)
                    {
                        renderers.AddRange(partsData.HairPropRendererArray);
                    }
                    if (partsData.IsTail)
                    {
                        renderers.Add(partsData.TailRenderer);
                    }
                    if (partsData.IsEye)
                    {
                        renderers.Add(partsData.EyeRenderer);
                    }
                    auraParam.RendererList = renderers;
                }
            }

            // カメラ登録
            auraParam.CharacterCamera = characterCamera;

            // パラメータ設定
            if (srcParam != null)
            {
                auraParam.Setup(srcParam);
            }
        }

        /// <summary>
        /// オーラのパラメータ設定
        /// </summary>
        public void SetAuraParam(int characterIndex, bool isEnable, float fresnel, float threshold, float intensity, float spread, float distortion, float fluctuation, float speed, Color color, Vector3 offsetViewDirection)
        {
            //URP:置き換え対応
            /*
            if (_aura == null)
            {
                return;
            }
            var auraParam = _aura.GetParam(characterIndex);
            */
            var auraParam = _imageEffect?.Aura.GetParam(characterIndex);

            auraParam.IsEnable = isEnable;
            auraParam.SetParamFresnel(fresnel);
            auraParam.SetParamThreshold(threshold);
            auraParam.SetParamIntensity(intensity);
            auraParam.SetParamSpread(spread);
            auraParam.SetParamDistortion(distortion);
            auraParam.SetParamFluctuation(fluctuation);
            auraParam.SetParamSpeed(speed);
            auraParam.SetParamAuraColor(ref color);
            auraParam.SetParamOffsetViewDir(ref offsetViewDirection);
        }

        /// <summary>
        /// オーラのパラメータ設定
        /// </summary>
        public void SetAuraParam(int characterIndex, AuraParam srcParam)
        {
            //URP:置き換え対応
            /*
            if (_aura == null)
            {
                return;
            }
            var auraParam = _aura.GetParam(characterIndex);
            */
            var auraParam = _imageEffect?.Aura.GetParam(characterIndex);

            auraParam.Setup(srcParam);
        }

        public AuraParam GetAuraParam(int characterIndex)
        {
            //URP:置き換え対応
            /*
            if (_aura == null)
            {
                return;
            }
            return _aura.GetParam(characterIndex);
            */
            return _imageEffect?.Aura.GetParam(characterIndex);
        }

        /// <summary>
        /// オーラの一時オンオフコントロール
        /// </summary>
        /// <param name="characterIndex"></param>
        /// <param name="isVisible"></param>
        public void SetAuraEnable(int characterIndex, bool isVisible)
        {
            //URP:置き換え対応
            //_aura.EnableRenderImage(characterIndex, isVisible);
            var auraParam = _imageEffect?.Aura.GetParam(characterIndex);
            auraParam.IsEnable = isVisible;
        }

        /// <summary>
        /// 外部から貰ったイメージエフェクトへの参照を外す（破棄はしない）
        /// </summary>
        public void ClearImageEffect()
        {
            _imageEffect = null;
            //URP:置き換え対応
            //_aura = null;
        }
    }

#if CYG_DEBUG
    public class DebugAuraController : MonoBehaviour
    {
        private AuraController _auraController;

        public bool EnabelDebugMode = false;
        public int CharacterNum;

        private AuraParam _debugAuraParam = new AuraParam();
        public float Fresnel;
        public float Threshold;
        public float Intensity;
        public float Spread;
        public float Distortion;
        public float Fluctuation;
        public float Speed;
        public Color AuraColor;
        public Vector3 OffsetViewDirection;

        public static DebugAuraController Create(AuraController auraController)
        {
            var debugAuraController = new GameObject("DebugAuraController").AddComponent<DebugAuraController>();
            debugAuraController._auraController = auraController;
            return debugAuraController;
        }

        public void CopyFrom(AuraParam srcParam)
        {
            _debugAuraParam.Setup(srcParam);

            Fresnel = _debugAuraParam.Fresnel;
            Threshold = _debugAuraParam.LuminousParam.x;
            Intensity = _debugAuraParam.LuminousParam.y;
            Spread = _debugAuraParam.LuminousParam.z;
            Distortion = _debugAuraParam.DistortionParam.y;
            Fluctuation = _debugAuraParam.DistortionParam.z;
            Speed = _debugAuraParam.DistortionParam.w;
            AuraColor = _debugAuraParam.AuraColor;
            OffsetViewDirection = _debugAuraParam.OffsetViewDir;
        }

        private void Update()
        {
            // 時間更新
            _debugAuraParam.UpdateOffset(Time.deltaTime);

            if (!EnabelDebugMode)
            {
                return;
            }

            // インスペクターからの値を反映
            _debugAuraParam.SetParamFresnel(Fresnel);
            _debugAuraParam.SetParamThreshold(Threshold);
            _debugAuraParam.SetParamIntensity(Intensity);
            _debugAuraParam.SetParamSpread(Spread);
            _debugAuraParam.SetParamDistortion(Distortion);
            _debugAuraParam.SetParamFluctuation(Fluctuation);
            _debugAuraParam.SetParamSpeed(Speed);
            _debugAuraParam.SetParamAuraColor(ref AuraColor);
            _debugAuraParam.SetParamOffsetViewDir(ref OffsetViewDirection);

            for (int charaIndex = 0; charaIndex < CharacterNum; charaIndex++)
            {
                _auraController.SetAuraParam(charaIndex, _debugAuraParam);
            }
        }
    }
#endif
}
