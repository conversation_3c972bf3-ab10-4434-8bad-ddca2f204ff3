using System;
using UnityEngine;
using UnityEngine.Rendering;
using Gallop.RenderPipeline;
using static Gallop.StaticVariableDefine.CG3D.ColorCorrection;
#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop.ImageEffect
{
    //URP:置き換え対応
    /*
    public class ColorCorrection : IEffectCommandBuffer
    {
        /// <summary>
        /// Commandバッファ使用時のEnableフラグの保存用
        /// </summary>
        private bool _isStoredEnable = false;

        public bool UseCommandBuffer => IsEnable;

        /// <summary>
        /// パラメータクラス
        /// ※とりあえずNewで作成しますが、外部から渡される可能性あり。作成しておかないと設定されるまでエラーになる可能性あるので
        /// </summary>
        private ColorCorrectionParam _param = new ColorCorrectionParam();
        public ColorCorrectionParam Param
        {
            get { return _param; }
            set
            {
                if (_param != value)
                {
                    // 古いパラメータを削除する。
                    _param?.Destroy();
                }
                _param = value;
            }
        }

        /// <summary>
        /// StoryRaceのようにマルチカメラかつカラコレが複数ある場合、Indexで区別
        /// 現在はStencil値に利用
        /// </summary>
        public int ParentMultiCameraIndex = -1;

        /// <summary>
        /// 有効/無効
        /// </summary>
        public bool IsEnable
        {
            get { return _param.IsValidity; }
        }

        /// <summary>
        /// 深度テクスチャ使用フラグ
        /// </summary>
        public bool IsUseDepthTexture
        {
            get { return IsEnable && Param.IsUseDepthCorrection; }
        }

        private void SetupMaterialParameter(int width, int height)
        {
            if ((_param != null) && (_param.CameraDepthTexture != null))
            {
                _param.CcDepthMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CameraDepthTexture), _param.CameraDepthTexture);
            }

            Material ccMaterial = _param.CcMaterial;
            Material ccDepthMaterial = _param.CcDepthMaterial;
            Material selectiveCcMaterial = _param.SelectiveCcMaterial;

            float saturation = _param.Saturation;
            float aspect = width / (float)height;

            if (_param.IsUseDepthCorrection)
            {
                ccDepthMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._RgbTex), _param.RgbChannelTex);
                ccDepthMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ZCurve), _param.ZCurveTex);
                ccDepthMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._RgbDepthTex), _param.RgbDepthChannelTex);
                ccDepthMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._Saturation), saturation);

                SetupMaterialVignette(ccDepthMaterial, aspect);
            }
            else
            {
                ccMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._RgbTex), _param.RgbChannelTex);
                ccMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._Saturation), saturation);

                SetupMaterialVignette(ccMaterial, aspect);
            }

            if (_param.IsSelectiveCc)
            {
                selectiveCcMaterial.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId.selColor), _param.SelectiveFromColor);
                selectiveCcMaterial.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId.targetColor), _param.SelectiveToColor);
            }
        }

        /// <summary>
        /// マテリアルにビネット関係のパラメータを設定する。
        /// </summary>
        /// <param name="material"></param>
        /// <param name="sourceAspect"></param>
        private void SetupMaterialVignette(Material material, float sourceAspect)
        {
            bool isUseMask = false;
            Vector4 option = Math.VECTOR4_ZERO;
            switch (_param.GetMaskType())
            {
                case ColorCorrectionParam.MaskType.None:
                default:
                    material.DisableKeyword("MASK_RECTANGLE");
                    material.DisableKeyword("MASK_VIGNETTE");
                    break;
                case ColorCorrectionParam.MaskType.Rectangle:
                    material.EnableKeyword("MASK_RECTANGLE");
                    material.DisableKeyword("MASK_VIGNETTE");
                    isUseMask = true;
                    option = _param.MaskCornerPower;
                    break;
                case ColorCorrectionParam.MaskType.Vignette:
                    material.DisableKeyword("MASK_RECTANGLE");
                    material.EnableKeyword("MASK_VIGNETTE");
                    isUseMask = true;
                    option = _param.MaskVignetteOption;
                    break;
            }

            if (isUseMask)
            {
                material.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmPower), _param.MaskPower);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmOffsetParam), _param.MaskOffset);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmOptionParam), option);
                //aspect比を入れておく
                _param.SetMaskAspect(sourceAspect);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmRollParameter), _param.MaskRollParameter);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmScaleParameter), _param.MaskScaleParameter);
                material.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmIsInverseVignette), (_param.IsInverseMaskVignette) ? 1f : 0f);
            }
            else
            {
                material.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmPower), 0f);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmOffsetParam), Math.VECTOR2_ZERO);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmOptionParam), Math.VECTOR4_ZERO);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmRollParameter), Math.VECTOR4_ZERO);
                material.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmScaleParameter), Math.VECTOR4_ONE);
                material.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._PostFilmIsInverseVignette), 0f);
            }
        }

        /// <summary>
        /// コマンドバッファに描画登録を行う
        /// </summary>
        /// <param name="commandBuffer"></param>
        public void AddCommandBuffer(CommandBuffer commandBuffer, RenderTexture source, int destinationPropertyID)
        {
            if (!UseCommandBuffer)
                return;

            SetupMaterialParameter(source.width, source.height);
            Material ccMaterial;
            Material selectiveCcMaterial = _param.SelectiveCcMaterial;

            if (_param.IsUseDepthCorrection)
            {
                ccMaterial = _param.CcDepthMaterial;
            }
            else
            {
                ccMaterial = _param.CcMaterial;
            }

            if (_param.IsSelectiveCc)
            {
                commandBuffer.GetTemporaryRT(TEMPORALLYSHADERID, source.width, source.height);
                commandBuffer.Blit(source, TEMPORALLYSHADERID, ccMaterial);
                commandBuffer.Blit(TEMPORALLYSHADERID, destinationPropertyID, selectiveCcMaterial);
                commandBuffer.ReleaseTemporaryRT(TEMPORALLYSHADERID);
            }
            else
            {
                commandBuffer.Blit(source, destinationPropertyID, ccMaterial);
            }

            commandBuffer.Blit(destinationPropertyID, source);
        }

        public void StoreFlag()
        {
            _isStoredEnable = _param.IsEnable;
            _param.IsEnable = false;
        }

        public void RestoreFlag()
        {
            _param.IsEnable = _isStoredEnable;
        }

        public void DisableRenderImage()
        {
            _param.IsEnable = false;
        }

        /// <summary>
        /// カラーコレクション処理(テンポラリ描画)
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destination"></param>
        public RenderTexture OnRenderImage(RenderTexture source)
        {
            // 一時RenderTextureを元サイズと同じサイズで作成
            RenderTexture sourceTexture = RenderTexture.GetTemporary(source.width, source.height);
#if CYG_DEBUG
            sourceTexture.name = "ColorCorrection.OnRenderImage.sourceTexture";
#endif
            OnRenderImage(source, sourceTexture);

            return sourceTexture;
        }

        /// <summary>
        /// カラーコレクション処理
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destination"></param>
        public void OnRenderImage(RenderTexture source, RenderTexture destination)
        {
            SetupMaterialParameter(source.width, source.height);

            Material ccMaterial = _param.CcMaterial;
            Material ccDepthMaterial = _param.CcDepthMaterial;
            Material selectiveCcMaterial = _param.SelectiveCcMaterial;

            RenderTexture renderTarget2Use = destination;

            if (_param.IsSelectiveCc)
            {
                renderTarget2Use = RenderTexture.GetTemporary(source.width, source.height);
#if CYG_DEBUG
                renderTarget2Use.name = "ColorCorrection.OnRenderImage.renderTarget2Use";
#endif
            }

            if (_param.IsUseDepthCorrection)
            {
                Graphics.Blit(source, renderTarget2Use, ccDepthMaterial);
            }
            else
            {
                Graphics.Blit(source, renderTarget2Use, ccMaterial);
            }

            if (_param.IsSelectiveCc)
            {
                Graphics.Blit(renderTarget2Use, destination, selectiveCcMaterial);
                RenderTexture.ReleaseTemporary(renderTarget2Use);
            }
        }

        private struct StencilParamSet
        {
            public int StencilId;
            public int StencilComp;
            public int StencilOp;

            public StencilParamSet(int stencilId, int stencilComp, int stencilOp)
            {
                StencilId = stencilId;
                StencilComp = stencilComp;
                StencilOp = stencilOp;
            }
        }

        private StencilParamSet GetStencilPropertyValue(Material mat)
        {
            int stencilMaskProperty = ShaderManager.GetPropertyId(ShaderManager.PropertyId._StencilMask);
            int stencilCompProperty = ShaderManager.GetPropertyId(ShaderManager.PropertyId._StencilComp);
            int stencilOpProperty = ShaderManager.GetPropertyId(ShaderManager.PropertyId._StencilOp);

            int stencilMaskValue = 0;
            if (mat.HasProperty(stencilMaskProperty))
            {
                stencilMaskValue = mat.GetInt(stencilMaskProperty);
            }

            int stencilCompValue = 0;
            if (mat.HasProperty(stencilCompProperty))
            {
                stencilCompValue = mat.GetInt(stencilCompProperty);
            }

            int stencilOpValue = 0;
            if (mat.HasProperty(stencilOpProperty))
            {
                stencilOpValue = mat.GetInt(stencilOpProperty);
            }

            return new StencilParamSet(
                stencilMaskValue,
                stencilCompValue,
                stencilOpValue
                );
        }

        private void SetStencilParam(Material mat, int stencilIdValue, int stencilCompValue, int stencilOpValue)
        {
            int stencilMaskProperty = ShaderManager.GetPropertyId(ShaderManager.PropertyId._StencilMask);
            int stencilCompProperty = ShaderManager.GetPropertyId(ShaderManager.PropertyId._StencilComp);
            int stencilOpProperty = ShaderManager.GetPropertyId(ShaderManager.PropertyId._StencilOp);

            if(mat.HasProperty(stencilMaskProperty))
            {
                mat.SetInt(stencilMaskProperty, stencilIdValue);
            }
            if(mat.HasProperty(stencilCompProperty))
            {
                mat.SetInt(stencilCompProperty, stencilCompValue);
            }
            if(mat.HasProperty(stencilOpProperty))
            {
                mat.SetInt(stencilOpProperty, stencilOpValue);
            }
        }

        /// <summary>
        /// カラーコレクション処理(ステンシル利用)
        /// StoryRaceで現在利用
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destination"></param>
        public void OnRenderImageUseStencil(RenderTexture source, RenderTexture destination, int stencilMaskId)
        {
            //現在のステンシル取得
            StencilParamSet defaultStencilParamCcMaterial = GetStencilPropertyValue(_param.CcMaterial);
            StencilParamSet defaultStencilParamDepthMaterial = GetStencilPropertyValue(_param.CcDepthMaterial);
            StencilParamSet defaultStencilParamSelectiveMaterial = GetStencilPropertyValue(_param.SelectiveCcMaterial);

            //ステンシル変更
            SetStencilParam(_param.CcMaterial, stencilMaskId, (int)CompareFunction.Equal, (int)StencilOp.Keep);
            SetStencilParam(_param.CcDepthMaterial, stencilMaskId, (int)CompareFunction.Equal, (int)StencilOp.Keep);
            SetStencilParam(_param.SelectiveCcMaterial, stencilMaskId, (int)CompareFunction.Equal, (int)StencilOp.Keep);

            //カラコレ実行
            OnRenderImage(source, destination);

            //元に戻す
            SetStencilParam(_param.CcMaterial, defaultStencilParamCcMaterial.StencilId, defaultStencilParamCcMaterial.StencilComp, defaultStencilParamCcMaterial.StencilOp);
            SetStencilParam(_param.CcDepthMaterial, defaultStencilParamDepthMaterial.StencilId, defaultStencilParamDepthMaterial.StencilComp, defaultStencilParamDepthMaterial.StencilOp);
            SetStencilParam(_param.SelectiveCcMaterial, defaultStencilParamSelectiveMaterial.StencilId, defaultStencilParamSelectiveMaterial.StencilComp, defaultStencilParamSelectiveMaterial.StencilOp);
        }

        /// <summary>
        /// DepthTextureを外す
        /// </summary>
        public void ResetDepthTexture()
        {
            if ((_param != null) && (_param.CcDepthMaterial != null))
            {
                _param.CcDepthMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CameraDepthTexture), null);
            }
        }

        public void Destroy()
        {
            if (_param != null)
            {
                _param.Destroy();
                _param = null;
            }
        }

#if UNITY_EDITOR && CYG_DEBUG
        private bool _foldOut;
        public void OnInspectorGUI()
        {
            _foldOut = UnityEditor.EditorGUILayout.Foldout(_foldOut,"Color Correction");
            if (!_foldOut)
            {
                return;
            }
            _param.OnInspectorGUI();
        }
#endif
    }
    */

    /// <summary>
    /// ColorCorrectionに使用するパラメータを外部で一括管理したいので、まとめたクラス
    /// </summary>
    [Serializable]
    public class ColorCorrectionParam
    {
        public enum ColorCorrectionMode
        {
            Simple = 0,
            Advanced = 1
        }

        // マスクの種類
        public enum MaskType
        {
            None = 0, // マスクなし
            Rectangle, // 矩形マスク
            Vignette, // ビネットマスク
        }

        private const int TEXTURE_WIDTH = 256;
        private const int TEXTURE_HEIGHT = 4;
        private static AnimationCurve MakeDefaultCurve() => AnimationCurve.Linear(0f, 0f, 1f, 1f);

        #region SerializeData
        public bool IsEnable = false;

        public bool IsValidity
        {
            get
            {
                if(!GraphicSettings.IsColorCorrection)
                {
                    return false;
                }
                return IsEnable;
            }
        }

        public AnimationCurve RedChannel = MakeDefaultCurve();
        public AnimationCurve GreenChannel = MakeDefaultCurve();
        public AnimationCurve BlueChannel = MakeDefaultCurve();

        // RGBチャンネル補間用パラメータ（データでは持たないのでSerializeしない）
        private AnimationCurve _redChannel2 = MakeDefaultCurve();
        private AnimationCurve _greenChannel2 = MakeDefaultCurve();
        private AnimationCurve _blueChannel2 = MakeDefaultCurve();
        private float _blendColorChannelRate = 0f;

        public AnimationCurve ZCurve = MakeDefaultCurve();
        public AnimationCurve DepthRedChannel = MakeDefaultCurve();
        public AnimationCurve DepthGreenChannel = MakeDefaultCurve();
        public AnimationCurve DepthBlueChannel = MakeDefaultCurve();

        public float Saturation = 1.0f;

        public bool IsSelectiveCc = false;

        public Color SelectiveFromColor = Color.white;
        public Color SelectiveToColor = Color.white;

        public ColorCorrectionMode Mode = ColorCorrectionMode.Simple;
        #endregion // SerializeData

#if CYG_DEBUG && UNITY_EDITOR
        [NonSerialized]
        public bool KeepInstance = false;
#endif

        private Color[] _rgbChannelColorArray = null;
        private Color[] _rgbDepthChannelColorArray = null;
        private Color[] _zCurveColorArray = null;

        public bool IsUseDepthCorrection => (Mode == ColorCorrectionMode.Advanced) ? true : false;

        public Material CcMaterial { get; private set; }
        public Material CcDepthMaterial { get; private set; }
        public Material SelectiveCcMaterial { get; private set; }

        public Texture2D RgbChannelTex { get; private set; }
        public Texture2D RgbDepthChannelTex { get; private set; }
        public Texture2D ZCurveTex { get; private set; }

        private Shader _curvesShader = null;
        private Shader _curvesSimpleShader = null;
        private Shader _selectiveShader = null;

        private MaskType _maskType = MaskType.None;
        public float MaskPower { get; set; } = 0.0f;
        public Vector2 MaskOffset { get; set; } = Math.VECTOR2_ZERO;
        public float MaskRollAngle { get; private set; } = 0.0f;
        private Vector4 _maskRollParameter = Math.VECTOR4_ZERO;
        public Vector4 MaskRollParameter { get => _maskRollParameter; }
        public Vector2 MaskScale { get; private set; } = Math.VECTOR2_ONE;
        private Vector4 _maskScaleParameter = Math.VECTOR4_ONE;
        public Vector4 MaskScaleParameter { get => _maskScaleParameter; }
        public Vector4 MaskCornerPower { get; set; } = Math.VECTOR4_ZERO;
        public Vector4 MaskVignetteOption { get; set; } = Math.VECTOR4_ZERO;
        public bool IsInverseMaskVignette { get; set; } = false;

        /// <summary>
        /// カメラのDepthTexture
        /// 参照のみ
        /// 共通リソースになりますので、渡してください。
        /// </summary>
        public RenderTexture CameraDepthTexture { get; set; } = null;

        public MaskType GetMaskType()
        {
            return _maskType;
        }
        public void SetMaskType(MaskType maskType)
        {
            _maskType = maskType;
        }

        public void SetMaskRollAngle(float angle)
        {
            MaskRollAngle = angle;
            _maskRollParameter.x = Math.Sinf(angle);
            _maskRollParameter.y = Math.Cosf(angle);
        }

        public void SetMaskAspect(float aspect)
        {
            _maskRollParameter.z = aspect;
        }

        public void SetMaskScale(Vector2 scale)
        {
            MaskScale = scale;
            _maskScaleParameter.x = 1.0f / scale.x;
            _maskScaleParameter.y = 1.0f / scale.y;
        }

        /// <summary>
        /// シェーダー読み込み
        /// </summary>
        private void LoadShader()
        {
            _curvesSimpleShader = ShaderManager.GetShader(ShaderManager.ShaderKinds.ColorCorrectionCurvesSimple);
            _curvesShader = ShaderManager.GetShader(ShaderManager.ShaderKinds.ColorCorrectionCurves);
            _selectiveShader = ShaderManager.GetShader(ShaderManager.ShaderKinds.ColorCorrectionSelective);
        }

        /// <summary>
        /// マテリアル作成
        /// </summary>
        private bool CreateMaterial()
        {
            bool isSupported = true;

            CcMaterial = PostEffectsBase.CheckShaderAndCreateMaterial(_curvesSimpleShader, CcMaterial, ref isSupported);
            CcDepthMaterial = PostEffectsBase.CheckShaderAndCreateMaterial(_curvesShader, CcDepthMaterial, ref isSupported);
            SelectiveCcMaterial = PostEffectsBase.CheckShaderAndCreateMaterial(_selectiveShader, SelectiveCcMaterial, ref isSupported);

            return isSupported;
        }

        /// <summary>
        /// マテリアル破棄
        /// </summary>
        private void DestroyMaterial()
        {
            if (CcMaterial != null)
            {
                UnityEngine.Object.DestroyImmediate(CcMaterial);
                CcMaterial = null;
            }
            if (CcDepthMaterial != null)
            {
                UnityEngine.Object.DestroyImmediate(CcDepthMaterial);
                CcDepthMaterial = null;
            }
            if (SelectiveCcMaterial != null)
            {
                UnityEngine.Object.DestroyImmediate(SelectiveCcMaterial);
                SelectiveCcMaterial = null;
            }
        }

        /// <summary>
        /// 初期化。
        /// Initializeを呼び出したらDestroyの呼び出しが必要。
        /// </summary>
        public void Initialize()
        {
            LoadShader();

            InitializeBase();
        }

        public void Initialize(Shader simpleShader, Shader curvesShader, Shader selectiveShader)
        {
            _curvesSimpleShader = simpleShader;
            _curvesShader = curvesShader;
            _selectiveShader = selectiveShader;

            InitializeBase();
        }

        public void InitializeResources()
        {
            LoadShader();
            CreateMaterial();
        }

        /// <summary>
        /// 必要なリソースの初期化などを行う
        /// </summary>
        private void InitializeBase()
        {
            CreateMaterial();

            _rgbChannelColorArray = new Color[TEXTURE_WIDTH * TEXTURE_HEIGHT];
            _rgbDepthChannelColorArray = new Color[TEXTURE_WIDTH * TEXTURE_HEIGHT];
            _zCurveColorArray = new Color[TEXTURE_WIDTH * 1];

            if (!RgbChannelTex)
            {
                RgbChannelTex = new Texture2D(TEXTURE_WIDTH, TEXTURE_HEIGHT, TextureFormat.ARGB32, false, true);
#if CYG_DEBUG
                RgbChannelTex.name = "Gallop.ColorCorrection.RgbChannelTex";
#endif
            }
            if (!RgbDepthChannelTex)
            {
                RgbDepthChannelTex = new Texture2D(TEXTURE_WIDTH, TEXTURE_HEIGHT, TextureFormat.ARGB32, false, true);
#if CYG_DEBUG
                RgbDepthChannelTex.name = "Gallop.ColorCorrection.RgbDepthChannelTex";
#endif
            }
            if (!ZCurveTex)
            {
                ZCurveTex = new Texture2D(TEXTURE_WIDTH, 1, TextureFormat.ARGB32, false, true);
#if CYG_DEBUG
                ZCurveTex.name = "Gallop.ColorCorrection.ZCurveTex";
#endif
            }

            RgbChannelTex.hideFlags = HideFlags.DontSave;
            RgbDepthChannelTex.hideFlags = HideFlags.DontSave;
            ZCurveTex.hideFlags = HideFlags.DontSave;

            RgbChannelTex.wrapMode = TextureWrapMode.Clamp;
            RgbDepthChannelTex.wrapMode = TextureWrapMode.Clamp;
            ZCurveTex.wrapMode = TextureWrapMode.Clamp;

            AnimationCurve zCurve = new AnimationCurve(new Keyframe(0f, 0f), new Keyframe(1f, 1f));
            for (float i = 0.0f; i <= 1.0f; i += 1.0f / 255.0f)
            {
                float zC = Mathf.Clamp01(zCurve.Evaluate(i));
                ZCurveTex.SetPixel((int)Mathf.Floor(i * 255.0f), 0, new Color(zC, zC, zC));
            }
            ZCurveTex.Apply();

            SetMaskType(MaskType.None);
            MaskPower = 0.0f;
            MaskOffset = Math.VECTOR2_ZERO;
            SetMaskRollAngle(0.0f);
            SetMaskScale(Math.VECTOR2_ONE);
            MaskCornerPower = Math.VECTOR4_ZERO;
            MaskVignetteOption = Math.VECTOR4_ZERO;
            IsInverseMaskVignette = false;
        }

        /// <summary>
        /// Initializeの破棄
        /// </summary>
        public void Destroy()
        {
#if CYG_DEBUG && UNITY_EDITOR
            if (KeepInstance)
            {
                return;
            }
#endif

            if (RgbChannelTex != null)
            {
                UnityEngine.Object.DestroyImmediate(RgbChannelTex);
                RgbChannelTex = null;
            }
            if (RgbDepthChannelTex != null)
            {
                UnityEngine.Object.DestroyImmediate(RgbDepthChannelTex);
                RgbDepthChannelTex = null;
            }
            if (ZCurveTex != null)
            {
                UnityEngine.Object.DestroyImmediate(ZCurveTex);
                ZCurveTex = null;
            }

            _rgbChannelColorArray = null;
            _rgbDepthChannelColorArray = null;
            _zCurveColorArray = null;

            DestroyMaterial();
        }

        /// <summary>
        /// パラメータの更新
        /// </summary>
        /// <param name="redChannel"></param>
        /// <param name="greenChannel"></param>
        /// <param name="blueChannel"></param>
        public void UpdateParameters(AnimationCurve redChannel, AnimationCurve greenChannel, AnimationCurve blueChannel)
        {
            if (Mode != ColorCorrectionMode.Simple)
            {
                Debug.LogWarning("ColorCorrectionModeがSimpleではありません。SimpleModeに変更します。設定されていたMode=" + Mode.ToString());
                Mode = ColorCorrectionMode.Simple;
            }

            RedChannel = redChannel;
            GreenChannel = greenChannel;
            BlueChannel = blueChannel;

            _blendColorChannelRate = 0f;

            UpdateParameters();
        }

        /// <summary>
        /// パラメータの更新
        /// </summary>
        /// <param name="redChannel"></param>
        /// <param name="greenChannel"></param>
        /// <param name="blueChannel"></param>
        public void UpdateParameters(AnimationCurve redChannel, AnimationCurve greenChannel, AnimationCurve blueChannel,
                                     AnimationCurve redChannel2, AnimationCurve greenChannel2, AnimationCurve blueChannel2, float blendRate)
        {
            if (Mode != ColorCorrectionMode.Simple)
            {
                Debug.LogWarning("ColorCorrectionModeがSimpleではありません。SimpleModeに変更します。設定されていたMode=" + Mode.ToString());
                Mode = ColorCorrectionMode.Simple;
            }

            RedChannel = redChannel;
            GreenChannel = greenChannel;
            BlueChannel = blueChannel;

            _redChannel2 = redChannel2;
            _greenChannel2 = greenChannel2;
            _blueChannel2 = blueChannel2;
            _blendColorChannelRate = blendRate;

            UpdateParameters();
        }

        /// <summary>
        /// パラメータの更新
        /// </summary>
        public void UpdateParameters()
        {
            if (_rgbChannelColorArray == null ||
                _rgbChannelColorArray.Length == 0)// Scriptableファイルを上書き保存するとき、既存ファイルの配列が空の場合があった
            {
                return;
            }

            Color tempCol;
            tempCol.a = 1f;
            float add = 1.0f / 255.0f;

            for (float i = 0.0f; i <= 1.0f; i += add)
            {
                // カラーチャンネル情報を更新する。
                float rCh = Mathf.Clamp01(RedChannel.Evaluate(i));
                float gCh = Mathf.Clamp01(GreenChannel.Evaluate(i));
                float bCh = Mathf.Clamp01(BlueChannel.Evaluate(i));

                if (_blendColorChannelRate > 0f)
                {
                    rCh = Mathf.Lerp(rCh, Mathf.Clamp01(_redChannel2.Evaluate(i)), _blendColorChannelRate);
                    gCh = Mathf.Lerp(gCh, Mathf.Clamp01(_greenChannel2.Evaluate(i)), _blendColorChannelRate);
                    bCh = Mathf.Lerp(bCh, Mathf.Clamp01(_blueChannel2.Evaluate(i)), _blendColorChannelRate);
                }

                int x = (int)Mathf.Floor(i * 255.0f);
                tempCol.r = rCh;
                tempCol.g = rCh;
                tempCol.b = rCh;
                _rgbChannelColorArray[x] = tempCol;

                tempCol.r = gCh;
                tempCol.g = gCh;
                tempCol.b = gCh;
                _rgbChannelColorArray[x + TEXTURE_WIDTH] = tempCol;

                tempCol.r = bCh;
                tempCol.g = bCh;
                tempCol.b = bCh;
                _rgbChannelColorArray[x + (TEXTURE_WIDTH * 2)] = tempCol;
            }
            RgbChannelTex.SetPixels(_rgbChannelColorArray);
            RgbChannelTex.Apply();

            if (!IsUseDepthCorrection)
            {
                return;
            }

            for (float i = 0.0f; i <= 1.0f; i += add)
            {
                int x = (int)Mathf.Floor(i * 255.0f);

                // 深度カラーチャンネル情報を更新する。
                float rCh = Mathf.Clamp01(DepthRedChannel.Evaluate(i));
                float gCh = Mathf.Clamp01(DepthGreenChannel.Evaluate(i));
                float bCh = Mathf.Clamp01(DepthBlueChannel.Evaluate(i));
                tempCol.r = rCh;
                tempCol.g = rCh;
                tempCol.b = rCh;
                _rgbDepthChannelColorArray[x] = tempCol;

                tempCol.r = gCh;
                tempCol.g = gCh;
                tempCol.b = gCh;
                _rgbDepthChannelColorArray[x + TEXTURE_WIDTH] = tempCol;

                tempCol.r = bCh;
                tempCol.g = bCh;
                tempCol.b = bCh;
                _rgbDepthChannelColorArray[x + (TEXTURE_WIDTH * 2)] = tempCol;

                // 深度ブレンド情報を更新する。
                float zC = Mathf.Clamp01(ZCurve.Evaluate(i));
                tempCol.r = zC;
                tempCol.g = zC;
                tempCol.b = zC;
                _zCurveColorArray[x] = tempCol;
            }
            RgbDepthChannelTex.SetPixels(_rgbDepthChannelColorArray);
            RgbDepthChannelTex.Apply();
            ZCurveTex.SetPixels(_zCurveColorArray);
            ZCurveTex.Apply();
        }

        public void Setup(ColorCorrectionPass.Parameter src)
        {
            IsEnable = src.IsEnable;
            Saturation = src.Saturation;
            RedChannel = src.RedChannel;
            GreenChannel = src.GreenChannel;
            BlueChannel = src.BlueChannel;

            _blendColorChannelRate = 0f;

            ZCurve = src.ZCurve;
            DepthRedChannel = src.ZCurve;
            DepthGreenChannel = src.DepthGreenChannel;
            DepthBlueChannel = src.DepthBlueChannel;

            IsSelectiveCc = src.IsSelectiveCc;
            SelectiveFromColor = src.SelectiveFromColor;
            SelectiveToColor = src.SelectiveToColor;

            Mode = src.Mode;
        }

        public void Setup(ColorCorrectionParam src)
        {
            IsEnable = src.IsEnable;
            Saturation = src.Saturation;
            RedChannel = src.RedChannel;
            GreenChannel = src.GreenChannel;
            BlueChannel = src.BlueChannel;

            _blendColorChannelRate = 0f;

            ZCurve = src.ZCurve;
            DepthRedChannel = src.ZCurve;
            DepthGreenChannel = src.DepthGreenChannel;
            DepthBlueChannel = src.DepthBlueChannel;

            IsSelectiveCc = src.IsSelectiveCc;
            SelectiveFromColor = src.SelectiveFromColor;
            SelectiveToColor = src.SelectiveToColor;

            Mode = src.Mode;
            UpdateParameters();

            SetMaskType(src._maskType);
            MaskPower = src.MaskPower;
            MaskOffset = src.MaskOffset;
            SetMaskRollAngle(src.MaskRollAngle);
            SetMaskScale(src.MaskScale);
            MaskCornerPower = src.MaskCornerPower;
            MaskVignetteOption = src.MaskVignetteOption;
            IsInverseMaskVignette = src.IsInverseMaskVignette;
        }
        /// <summary>
        /// 補間処理。補間できないパラメータ(boolとか)は触りません。
        /// </summary>
        public void Lerp(ColorCorrectionParam src1, ColorCorrectionParam src2, float t)
        {
            Saturation = Mathf.Lerp(src1.Saturation, src2.Saturation, t);
            SelectiveFromColor = Color.Lerp(src1.SelectiveFromColor, src2.SelectiveFromColor, t);
            SelectiveToColor = Color.Lerp(src1.SelectiveToColor, src2.SelectiveToColor, t);
            UpdateParameters();

            MaskPower = Mathf.Lerp(src1.MaskPower, src2.MaskPower, t);
            MaskOffset = Vector2.Lerp(src1.MaskOffset, src2.MaskOffset, t);
            SetMaskRollAngle(Mathf.Lerp(src1.MaskRollAngle, src2.MaskRollAngle, t));
            SetMaskScale(Vector2.Lerp(src1.MaskScale, src2.MaskScale, t));
            MaskCornerPower = Vector4.Lerp(src1.MaskCornerPower, src2.MaskCornerPower, t);
            MaskVignetteOption = Vector4.Lerp(src1.MaskVignetteOption, src2.MaskVignetteOption, t);
        }

#if UNITY_EDITOR && CYG_DEBUG
        private bool _foldOut;
        public void OnInspectorGUI()
        {
            IsEnable = EditorGUILayout.Toggle("IsEnable", IsEnable);
            RedChannel = EditorGUILayout.CurveField("Red Channel", RedChannel);
            GreenChannel = EditorGUILayout.CurveField("Green Channel", GreenChannel);
            BlueChannel = EditorGUILayout.CurveField("Blue Channel", BlueChannel);

            ZCurve = EditorGUILayout.CurveField("Z Curve", ZCurve);
            DepthRedChannel = EditorGUILayout.CurveField("Depth Red Channel", DepthRedChannel);
            DepthGreenChannel = EditorGUILayout.CurveField("Depth Green Channel", DepthGreenChannel);
            DepthBlueChannel = EditorGUILayout.CurveField("Depth Blue Channel", DepthBlueChannel);

            Saturation = EditorGUILayout.FloatField("Saturation", Saturation);
            IsSelectiveCc = EditorGUILayout.Toggle("IsSelective Cc", IsSelectiveCc);
            SelectiveFromColor = EditorGUILayout.ColorField("Selective From Color", SelectiveFromColor);
            SelectiveToColor = EditorGUILayout.ColorField("Selective To Color", SelectiveToColor);

            Mode = (ColorCorrectionMode)EditorGUILayout.EnumPopup("Selective To Color", Mode);
        }
#endif
    }
}
