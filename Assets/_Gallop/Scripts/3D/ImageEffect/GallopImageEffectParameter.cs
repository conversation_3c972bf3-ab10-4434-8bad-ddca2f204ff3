using System;
using UnityEngine;
using UnityEngine.Rendering;

#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// ImageEffectのパラメータ保存アセットクラス
/// </summary>
namespace Gallop
{
    [Serializable]
    public class GallopImageEffectParameter : ScriptableObject
    {
        /// <summary> GlobalFog:パラメータ </summary>
        public ImageEffect.GlobalFogParam GlobalFogParam = new ImageEffect.GlobalFogParam();

        /// <summary> SunShafts:パラメータ </summary>
        public ImageEffect.SunShaftsParam SunShaftsParam = new ImageEffect.SunShaftsParam();

        /// <summary> TiltShift:パラメータ </summary>
        public ImageEffect.TiltShiftParam TiltShiftParam = new ImageEffect.TiltShiftParam();

        /// <summary> IndirectLightShafts:パラメータ </summary>
        public ImageEffect.IndirectLightShaftsParam IndirectLightShaftsParam = new ImageEffect.IndirectLightShaftsParam();

        /// <summary> RadialBlur:パラメータ </summary>
        public ImageEffect.RadialBlurParam RadialBlurParam = new ImageEffect.RadialBlurParam();

        /// <summary> DofDiffusionBloomOverlay:パラメータ </summary>
        public ImageEffect.DofDiffusionBloomOverlayParam DofDiffusionBloomOverlayParam = new ImageEffect.DofDiffusionBloomOverlayParam();

        /// <summary> LensDistortion:パラメータ </summary>
        public ImageEffect.LensDistortionParam LensDistortionParam = new ImageEffect.LensDistortionParam();

        /// <summary> ColorCorrection:パラメータ </summary>
        public ImageEffect.ColorCorrectionParam ColorCorrectionParam = new ImageEffect.ColorCorrectionParam();

        public ImageEffect.FluctuationParam FluctuationParam = new ImageEffect.FluctuationParam();

        public ImageEffect.ChromaticAberrationParameter ChromaticAberrationParam = new ImageEffect.ChromaticAberrationParameter();

        public ImageEffect.ToneCurveParam ToneCurveParam = new ImageEffect.ToneCurveParam();
        public ImageEffect.ExposureParam ExposureParam = new ImageEffect.ExposureParam();

        protected virtual void OnCopyFrom(GallopImageEffectParameter src)
        {
            //独自で処理を追加したい場合はこれをoverrideしてください
        }

        protected virtual void OnCopyFrom(GallopImageEffect src)
        {
            //独自で処理を追加したい場合はこれをoverrideしてください
        }

        protected virtual void OnCopyTo(GallopImageEffect src)
        {
            //独自で処理を追加したい場合はこれをoverrideしてください
        }

        /// <summary>
        /// 補間処理。補間できないパラメータ(boolとか)は触りません。
        /// </summary>
        protected virtual void OnLerp(GallopImageEffectParameter src1, GallopImageEffectParameter src2, float t)
        {
            //独自で処理を追加したい場合はこれをoverrideしてください
        }

        public void CopyFrom(GallopImageEffect src)
        {
            GlobalFogParam.Setup(src.GlobalFogParam);
            SunShaftsParam.Setup(src.SunShaftsParam);
            TiltShiftParam.Setup(src.TiltShiftParam);
            IndirectLightShaftsParam.Setup(src.IndirectLightShaftsParam);
            RadialBlurParam.Setup(src.RadialBlurParam);
            DofDiffusionBloomOverlayParam.Setup(src.DofDiffusionBloomOverlayParam);
            LensDistortionParam.Setup(src.LensDistortionParam);
            ColorCorrectionParam.Setup(src.ColorCorrectionParam);
            //ChromaticAberrationParamへ内容をコピーする
            ChromaticAberrationParam.Setup(src.ChromaticAberration);
            ToneCurveParam.Setup(src.ToneCurveParam);
            ExposureParam.Setup(src.ExposureParam);
            OnCopyFrom(src);
        }

        public void CopyFrom(GallopImageEffectParameter src)
        {
            GlobalFogParam.Setup(src.GlobalFogParam);
            SunShaftsParam.Setup(src.SunShaftsParam);
            TiltShiftParam.Setup(src.TiltShiftParam);
            IndirectLightShaftsParam.Setup(src.IndirectLightShaftsParam);
            RadialBlurParam.Setup(src.RadialBlurParam);
            DofDiffusionBloomOverlayParam.Setup(src.DofDiffusionBloomOverlayParam);
            LensDistortionParam.Setup(src.LensDistortionParam);
            ColorCorrectionParam.Setup(src.ColorCorrectionParam);
            ChromaticAberrationParam.Setup(src.ChromaticAberrationParam);
            ToneCurveParam.Setup(src.ToneCurveParam);
            ExposureParam.Setup(src.ExposureParam);
            OnCopyFrom(src);
        }

        public void CopyTo(GallopImageEffect target)
        {
            if(!target.IsInitialized)
            {
#if CYG_DEBUG
                Debug.LogAssertion("Initializeが呼び出される前にパラメータを反映しようとしました");
#endif
                return;
            }
            target.GlobalFogParam.Setup(GlobalFogParam);
            target.SunShaftsParam.Setup(SunShaftsParam);
            target.TiltShiftParam.Setup(TiltShiftParam);
            target.IndirectLightShaftsParam.Setup(IndirectLightShaftsParam);
            target.RadialBlurParam.Setup(RadialBlurParam);
            target.DofDiffusionBloomOverlayParam.Setup(DofDiffusionBloomOverlayParam);
            target.LensDistortionParam.Setup(LensDistortionParam);
            target.FluctuationParam.Setup(FluctuationParam);
            target.ColorCorrectionParam.Setup(ColorCorrectionParam);
            target.ChromaticAberration.Setup(ChromaticAberrationParam);
            target.ToneCurveParam.Setup(ToneCurveParam);
            target.ExposureParam.Setup(ExposureParam);

            OnCopyTo(target);
        }

        /// <summary>
        /// 補間処理。補間できないパラメータ(boolとか)は触りません。
        /// </summary>
        public void Lerp(GallopImageEffectParameter src1, GallopImageEffectParameter src2, float t)
        {
            GlobalFogParam.Lerp(src1.GlobalFogParam, src2.GlobalFogParam, t);
            SunShaftsParam.Lerp(src1.SunShaftsParam, src2.SunShaftsParam, t);
            TiltShiftParam.Lerp(src1.TiltShiftParam, src2.TiltShiftParam, t);
            IndirectLightShaftsParam.Lerp(src1.IndirectLightShaftsParam, src2.IndirectLightShaftsParam, t);
            RadialBlurParam.Lerp(src1.RadialBlurParam, src2.RadialBlurParam, t);
            DofDiffusionBloomOverlayParam.Lerp(src1.DofDiffusionBloomOverlayParam, src2.DofDiffusionBloomOverlayParam, t);
            LensDistortionParam.Lerp(src1.LensDistortionParam, src2.LensDistortionParam, t);
            ColorCorrectionParam.Lerp(src1.ColorCorrectionParam, src2.ColorCorrectionParam, t);
            ChromaticAberrationParam.Lerp(src1.ChromaticAberrationParam, src2.ChromaticAberrationParam, t);
            ToneCurveParam.Lerp(src1.ToneCurveParam, src2.ToneCurveParam, t);
            ExposureParam.Lerp(src1.ExposureParam, src2.ExposureParam, t);
            OnLerp(src1, src2, t);
        }

#if UNITY_EDITOR
        [NonSerialized]
        private bool _isFoldOutGlobalFog = false;
        [NonSerialized]
        private bool _isFoldOutSunShafts = false;
        [NonSerialized]
        private bool _isFoldOutTiltShift = false;
        [NonSerialized]
        private bool _isFoldOutIndirectLightShafts = false;
        [NonSerialized]
        private bool _isFoldOutRadialBlur = false;
        [NonSerialized]
        private bool _isFoldOutDofDiffusionBloomOverlay = false;
        [NonSerialized]
        private bool _isFoldOutLensDistortion = false;
        [NonSerialized]
        private bool _isFoldOutColorCorrection = false;
        [NonSerialized]
        private bool _isFoldOutChromaticAberration = false;
        [NonSerialized]
        private bool _isFoldOutToneCurveParam = false;
        [NonSerialized]
        private bool _isFoldOutExposureParam = false;
        public void OnInspectorGUI()
        {
#if CYG_DEBUG
            _isFoldOutGlobalFog = UnityEditor.EditorGUILayout.Foldout(_isFoldOutGlobalFog, "GlobalFog");
            if (_isFoldOutGlobalFog)
            {
                UnityEditor.EditorGUI.indentLevel++;
                GlobalFogParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutSunShafts = UnityEditor.EditorGUILayout.Foldout(_isFoldOutSunShafts, "SunShafts");
            if (_isFoldOutSunShafts)
            {
                UnityEditor.EditorGUI.indentLevel++;
                SunShaftsParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutTiltShift = UnityEditor.EditorGUILayout.Foldout(_isFoldOutTiltShift, "TiltShift");
            if (_isFoldOutTiltShift)
            {
                UnityEditor.EditorGUI.indentLevel++;
                TiltShiftParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutIndirectLightShafts = UnityEditor.EditorGUILayout.Foldout(_isFoldOutIndirectLightShafts, "IndirectLightShafts");
            if (_isFoldOutIndirectLightShafts)
            {
                UnityEditor.EditorGUI.indentLevel++;
                IndirectLightShaftsParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutRadialBlur = UnityEditor.EditorGUILayout.Foldout(_isFoldOutRadialBlur, "RadialBlur");
            if (_isFoldOutRadialBlur)
            {
                UnityEditor.EditorGUI.indentLevel++;
                RadialBlurParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutDofDiffusionBloomOverlay = UnityEditor.EditorGUILayout.Foldout(_isFoldOutDofDiffusionBloomOverlay, "DofDiffusionBloomOverlay");
            if (_isFoldOutDofDiffusionBloomOverlay)
            {
                UnityEditor.EditorGUI.indentLevel++;
                DofDiffusionBloomOverlayParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutLensDistortion = UnityEditor.EditorGUILayout.Foldout(_isFoldOutLensDistortion, "LensDistortion");
            if (_isFoldOutLensDistortion)
            {
                UnityEditor.EditorGUI.indentLevel++;
                LensDistortionParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutColorCorrection = UnityEditor.EditorGUILayout.Foldout(_isFoldOutColorCorrection, "ColorCorrection");
            if (_isFoldOutColorCorrection)
            {
                UnityEditor.EditorGUI.indentLevel++;
                ColorCorrectionParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutChromaticAberration = UnityEditor.EditorGUILayout.Foldout(_isFoldOutChromaticAberration, "ChromaticAberration");
            if (_isFoldOutChromaticAberration)
            {
                UnityEditor.EditorGUI.indentLevel++;
                ChromaticAberrationParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutToneCurveParam = UnityEditor.EditorGUILayout.Foldout(_isFoldOutToneCurveParam, "ToneCurveParam");
            if (_isFoldOutToneCurveParam)
            {
                UnityEditor.EditorGUI.indentLevel++;
                ToneCurveParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
            _isFoldOutExposureParam = UnityEditor.EditorGUILayout.Foldout(_isFoldOutExposureParam, "ExposureParam");
            if (_isFoldOutExposureParam)
            {
                UnityEditor.EditorGUI.indentLevel++;
                ExposureParam.OnInspectorGUI();
                UnityEditor.EditorGUI.indentLevel--;
            }
#endif
            OnInspectorGUIForOverride();
        }
        protected virtual void OnInspectorGUIForOverride()
        {
            //独自で処理を追加したい場合はこれをoverrideしてください
        }
#endif
    }
}
