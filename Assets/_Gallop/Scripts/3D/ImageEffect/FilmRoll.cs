using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    [System.Serializable]
    public class FilmRollParamData
    {
        /// <summary>
        /// 機能のOn/Off
        /// </summary>
        public bool IsEnable = false;

        /// <summary>
        /// UVスクロール位置
        /// </summary>
        public Vector2 RollPosition = Math.VECTOR2_ZERO;

        /// <summary>
        /// 枠線の大きさ
        /// </summary>
        public Vector2 RollMaskSize = Math.VECTOR2_ZERO;

        /// <summary>
        /// 枠線のフェードする大きさ
        /// </summary>
        public Vector2 RollMaskFadeSize = Math.VECTOR2_ZERO;

        /// <summary>
        /// 枠線の色
        /// </summary>
        public Color RollMaskColor = GameDefine.COLOR_BLACK;

        /// <summary>
        /// ノイズの強さ
        /// </summary>
        public float NoisePower = 0;

        /// <summary>
        /// ノイズの色をランダムにするかどうか
        /// </summary>
        public bool IsNoiseRandom = false;

        /// <summary>
        /// ノイズの色
        /// </summary>
        public Color NoiseColor = GameDefine.COLOR_BLACK;

        /// <summary>
        /// ノイズの明るさの幅（加算、減算）
        /// </summary>
        public Vector2 NoiseRange = new Vector2(-1, 1);

        /// <summary>
        /// 走査線の速さ
        /// </summary>
        public float ScanLineSpeed = 0;

        /// <summary>
        /// 走査線の大きさ
        /// </summary>
        public float ScanLineSize = 0;

        /// <summary>
        /// 走査線にかかるブラーの大きさ
        /// </summary>
        public Vector2 ScanlineBlurSize = Math.VECTOR2_ZERO;

        /// <summary>
        /// 走査線にかかる色味の強さ
        /// </summary>
        public float ScanLinePower = 1.5f;

        /// <summary>
        /// 処理のタイミング
        /// </summary>
        public Gallop.RenderPipeline.FilmRollPass.Parameter.EnqueueTimingType EnqueueTimiing;

#if UNITY_EDITOR
        public void CopyFrom(FilmRollParamData src)
        {
            IsEnable = src.IsEnable;
            IsNoiseRandom = src.IsNoiseRandom;
            EnqueueTimiing = src.EnqueueTimiing;

            RollPosition = src.RollPosition;
            RollMaskSize = src.RollMaskSize;
            RollMaskFadeSize = src.RollMaskFadeSize;
            RollMaskColor = src.RollMaskColor;

            NoisePower = src.NoisePower;
            NoiseColor = src.NoiseColor;
            NoiseRange = src.NoiseRange;

            ScanLineSpeed = src.ScanLineSpeed;
            ScanLineSize = src.ScanLineSize;
            ScanLinePower = src.ScanLinePower;
            ScanlineBlurSize = src.ScanlineBlurSize;
        }

#endif

    }
}