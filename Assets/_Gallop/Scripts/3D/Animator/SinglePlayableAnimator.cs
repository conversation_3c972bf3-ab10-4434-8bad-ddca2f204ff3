using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace Gallop
{
    /// <summary>
    /// 単発再生用のシンプルなAnimator
    /// 単発とクロスフェードをサポートする
    /// </summary>
    public class SinglePlayableAnimator : PlayableAnimator
    {
        #region 定数

        /// <summary>
        /// アニメーションのレイヤー番号
        /// </summary>
        public enum Layer
        {
            Base,
            Max
        }

        /// <summary>
        /// 各レイヤー内のモーション補間用
        /// </summary>
        protected enum Mixer
        {
            Mixer1,
            Mixer2,

            Max,
        }

        /// <summary>
        /// 状態
        /// </summary>
        public enum State
        {
            Motion_Invalid = -1,
            Motion_Single,
        }

        private enum ClipState
        {
            Invalid = -1,
            //CrossFade用にState*2必要になる
            Motion_Single_0,
            Motion_Single_1,
        }

        private const int LAYER_NUM = (int)Layer.Max;
        private const int MIXER_NUM = (int)Mixer.Max;

        #endregion

        #region クラス

        /// <summary>
        /// AnimationClipと再生オブジェクトの紐づけ定義
        /// </summary>
        private class SingleClipPlayableState : ClipPlayableState
        {
            public ClipState ClipState;
        }

        /// <summary>
        /// 各レイヤーの再生状態
        /// </summary>
        private class LayerState
        {
            public ClipPlayableState CurrentPlayableState;  //今再生しているステート情報

            public ClipState CurrentState = ClipState.Invalid;
            public ClipState PrevState = ClipState.Invalid;
            public float PlayTime;
            public float PrevTime;

            //クロスフェード状態管理
            public float BlendTime;
            public float BlendElapsedTime;
            public float BlendWeight;

            public ClipState[] ConnectedClipStateArray;

            public void Initialize()
            {
                PlayTime = PrevTime = 0.0f;
                BlendTime = 0.0f;
                BlendElapsedTime = 0.0f;
                BlendWeight = 1.0f;
                ConnectedClipStateArray = new ClipState[MIXER_NUM]
                {
                    ClipState.Invalid,
                    ClipState.Invalid,
                };
            }
        }

        #endregion

        #region 変数

        //Layerの状態(Mixer,再生時間など)管理
        private readonly LayerState[] _layerStateArray = new LayerState[LAYER_NUM];

        private readonly Dictionary<int, SingleClipPlayableState> _clipDictionary = new Dictionary<int, SingleClipPlayableState>();

        private int _clipStatePage = 0;

        #endregion

        protected override void OnInitialize()
        {
            const int STATE_NUM = (int)ClipState.Motion_Single_1 + 1;

            //先に登録だけしておく
            for (var i = 0; i < STATE_NUM; i++)
            {
                _clipDictionary.Add(i, new SingleClipPlayableState() { ClipState = (ClipState)i });
            }

            // アニメーションレイヤーミキサーの作成
            CreateLayerMixer(LAYER_NUM, MIXER_NUM);

            // 変数初期化
            for (int i = 0; i < LAYER_NUM; i++)
            {
                _layerStateArray[i] = new LayerState();
                _layerStateArray[i].Initialize();
            }
        }

        protected override void OnDestroy()
        {
            foreach (var clipState in _clipDictionary.Values)
            {
                if (clipState.ClipPlayable.IsValid())
                {
                    clipState.ClipPlayable.Destroy();
                }
            }
            _clipDictionary.Clear();
        }

        public SinglePlayableAnimator(Animator animator,string debugName = null) : base(animator,debugName)
        {
        }

        // モーション再生系のメソッド
        public float GetPlayTime(Layer layer) => _layerStateArray[(int)layer].PlayTime;

        public void SetPlayTime(Layer layer, float time) => _layerStateArray[(int)layer].PlayTime = time;

        public float GetNormalizePlayTimeWithLoop(Layer layer)
        {
            if (_layerStateArray[(int)layer].CurrentPlayableState == null)
                return 0.0f;
            var clipState = _layerStateArray[(int)layer].CurrentPlayableState;
            return _layerStateArray[(int)layer].PlayTime / clipState.Clip.length;
        }

        public void SetNormalizePlayTimeWithLoop(Layer layer,float normalizeTime)
        {
            if (_layerStateArray[(int)layer].CurrentPlayableState == null)
                return;
            var clipState = _layerStateArray[(int)layer].CurrentPlayableState;
            _layerStateArray[(int)layer].PlayTime = clipState.Clip.length * normalizeTime;
        }

        public float GetClipLength(Layer layer)
        {
            if (_layerStateArray[(int)layer].CurrentPlayableState == null)
                return 0.0f;
            return _layerStateArray[(int)layer].CurrentPlayableState.Clip.length;
        }

        public override float CurrentPlayTime
        {
            get => GetPlayTime(Layer.Base);
            set => SetPlayTime(Layer.Base, value);
        }

        public override float ClipLength => GetClipLength(Layer.Base);

        public override float CurrentNormalizePlayTimeWithLoop
        {
            get => GetNormalizePlayTimeWithLoop(Layer.Base);
            set => SetNormalizePlayTimeWithLoop(Layer.Base, value);
        }

        /// <summary>
        /// モーション再生時間更新
        /// </summary>
        /// <param name="deltaTime"></param>
        protected override void OnUpdate(float deltaTime)
        {
            UpdateState(deltaTime * Speed);
            UpdateBaseAnimation();
        }

        /// <summary>
        /// クリップを設定する
        /// </summary>
        /// <param name="index"></param>
        /// <param name="clip"></param>
        public void SetClip(State state, AnimationClip clip)
        {
            int clipIndex = (int)GetClipState((State)state);
            if (!_clipDictionary.TryGetValue(clipIndex, out var clipState))
            {
                //初期化されていない
                return;
            }

            if (clipState.Clip == clip)
                return;

            if (clipState.ClipPlayable.IsValid())
            {
                clipState.ClipPlayable.Destroy();
            }

            clipState.Clip = clip;
            if (clip != null)
            {
                clipState.ClipPlayable = AnimationClipPlayable.Create(_playableGraph, clip);
            }
            //nullだった時はDisConnectを行わせる
            clipState.IsForceDisconnected = true;
        }

        private void Play(Layer layer, ClipState clipState, float time,float blendTime)
        {
            bool isCrossFade = (blendTime > 0.0f);
            var currentState = _layerStateArray[(int)layer];
            currentState.BlendElapsedTime = 0;
            currentState.BlendTime = blendTime;
            if (isCrossFade)
            {
                currentState.BlendWeight = 0.0f;
            }
            else
            {
                currentState.BlendWeight = 1.0f;
            }
            currentState.PrevState = currentState.CurrentState;
            currentState.PrevTime = currentState.PlayTime;

            currentState.PlayTime = time;
            currentState.CurrentState = clipState;
        }

        public void Play(Layer layer, AnimationClip clip, float time)
        {
            SetClip(State.Motion_Single, clip);
            Play(layer, GetClipState(State.Motion_Single), time, 0);
        }

        public void CrossFade(Layer layer, AnimationClip clip, float time, float blendTime)
        {
            if(blendTime > 0.0f)
            {
                //CrossFadeが実行されるまでは同じ個所を上書き
                _clipStatePage = (_clipStatePage + 1) % MIXER_NUM;
            }
            //ブレンド後のモーション設定
            SetClip(State.Motion_Single, clip);
            Play(layer, GetClipState(State.Motion_Single), time, blendTime);
        }

        private ClipState GetClipState(State state)
        {
            return (ClipState)(((int)state * 2) + _clipStatePage);
        }

        /// <summary>
        /// MixerにモーションをConnectする必要があるか
        /// </summary>
        private bool NeedConnect(SingleClipPlayableState clipState, SingleClipPlayableState prevClipState,
                                ClipState[] connectedClipStateArray,bool isCrossFade)
        {
            // Connect済みモーションとClipのモーションが異なるかどうか判定するローカル関数
            bool IsDifferentMotion(ClipState state, SingleClipPlayableState clip)
            {
                if (clip == null)
                {
                    if (state != ClipState.Invalid)
                        return true;
                }
                else
                {
                    if (state != clip.ClipState || clip.IsForceDisconnected)
                    {
                        clip.IsForceDisconnected = false;
                        return true;
                    }
                }
                return false;
            }

            // これから再生するClipについて判定
            if (IsDifferentMotion(connectedClipStateArray[(int)Mixer.Mixer1],clipState))
            {
                return true;
            }

            // ブレンドする場合は前のClipの判定も必要
            if (isCrossFade)
            {
                if (IsDifferentMotion(connectedClipStateArray[(int)Mixer.Mixer2], prevClipState))
                {
                    return true;
                }
            }
            else
            {
                //CrossFadeじゃないのに設定残っている場合は外す
                if (connectedClipStateArray[(int)Mixer.Mixer2] != ClipState.Invalid)
                {
                    if(prevClipState != null)
                    {
                        prevClipState.IsForceDisconnected = false;
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// ミキサーにウェイトを設定する
        /// </summary>
        /// <param name="mixer"></param>
        /// <param name="weight"></param>
        /// <param name="needPose"></param>
        /// <param name="needPosePrev"></param>
        private void SetInputWeight(ref AnimationMixerPlayable mixer, float blendWeight)
        {
            float weight1 = blendWeight;
            float weight2 = (1.0f - blendWeight);
            mixer.SetInputWeight((int)Mixer.Mixer1, weight1);
            mixer.SetInputWeight((int)Mixer.Mixer2, weight2);
        }

        /// <summary>
        /// ブレンドを無効にする
        /// </summary>
        private void ClearInputWeight(ref AnimationMixerPlayable mixer)
        {
            if(mixer.GetInputWeight((int)Mixer.Mixer1) > 0.0f)
                mixer.SetInputWeight((int)Mixer.Mixer1, 0.0f);

            if (mixer.GetInputWeight((int)Mixer.Mixer2) > 0.0f)
                mixer.SetInputWeight((int)Mixer.Mixer2, 0.0f);
        }

        /// <summary>
        /// モーションを再生する
        /// </summary>
        private void PlayMotion(
            float fixedTime,
            SingleClipPlayableState clipPlayableState,
            ClipState[] connectedClipArray,
            bool needConnect,
            ref AnimationMixerPlayable mixer,
            Mixer index)
        {
            if (needConnect)
            {
                _playableGraph.Connect(clipPlayableState.ClipPlayable, 0, mixer, (int)index);
                connectedClipArray[(int)index] = clipPlayableState.ClipState;
            }

            clipPlayableState.ClipPlayable.SetTime(fixedTime);
        }

        private bool IsCrossFade(float blendWeight, ref SingleClipPlayableState clipData, ref SingleClipPlayableState prevClipData)
        {
            // ブレンド開始地点にいる場合は、次のClipを再生する
            var isCrossFade = false;
            if (prevClipData == null)
                return false;

            if (blendWeight < 1.0f)
            {
                //前Clipが無効の場合にはブレンド対象にしない
                if (prevClipData.Clip != null && prevClipData.Clip != clipData.Clip)
                {
                    isCrossFade = true;
                }
            }
            return isCrossFade;
        }

        /// <summary>
        /// Playableの関連性を外す
        /// </summary>
        /// <param name="motionClipArray"></param>
        private void Disconnect(ref AnimationMixerPlayable mixer, ClipState[] connectedClipArray)
        {
            // 紐づけ必要になった場合に、まずすでに紐づけているモーションを外す
            for (int i = 0; i < MIXER_NUM; i++)
            {
                _playableGraph.Disconnect(mixer, i);
                connectedClipArray[i] = ClipState.Invalid;
            }
        }

        private bool DisconnectFromNeedConnect(ref AnimationMixerPlayable mixer,
                                                SingleClipPlayableState clipState, SingleClipPlayableState prevClipState,
                                                ClipState[] connectedClipArray,bool isCrossFade = false)
        {
            // 紐づけ（connect）が必要かどうか（connectを連続して呼ぶことはできない、モーションが切り替わるタイミングでDisconnectを呼ばないといけない）
            bool needConnect = NeedConnect(clipState, prevClipState, connectedClipArray, isCrossFade);
            if (needConnect)
            {
                // 紐づけ必要になった場合に、まずすでに紐づけているモーションを外す
                Disconnect(ref mixer, connectedClipArray);
            }
            _isDisconnect |= needConnect;

            return needConnect;
        }

        private void AddPlayTime(LayerState[] layerStateArray, float deltaTime)
        {
            foreach (var state in layerStateArray)
            {
                state.PlayTime += deltaTime;
                state.PrevTime += deltaTime;
            }
        }

        private void UpdateState(float deltaTime)
        {
            AddPlayTime(_layerStateArray, deltaTime);

            //ブレンド率を求める
            foreach (var layerState in _layerStateArray)
            {
                if (layerState.BlendTime <= 0.0f)
                    continue;

                layerState.BlendElapsedTime += deltaTime;

                //1.0で通常に戻る
                if (layerState.BlendElapsedTime < layerState.BlendTime)
                {
                    layerState.BlendWeight = layerState.BlendElapsedTime / layerState.BlendTime;
                }
                else
                {
                    //ブレンド終了
                    layerState.BlendWeight = 1.0f;
                    layerState.BlendTime = 0.0f;
                }
            }
        }

        private void UpdateBaseAnimation()
        {
            const int BASE_INDEX = (int)Layer.Base;
            UpdateAnimation(_layerStateArray[BASE_INDEX], ref _mixerPlayableMotion[BASE_INDEX]);
        }

        private void UpdateAnimation(LayerState targetLayer, ref AnimationMixerPlayable mixerPlayable)
        {
            // 設定がない場合はスルー
            if (!_clipDictionary.TryGetValue((int)targetLayer.CurrentState,out var currentClipState))
            {
                ClearInputWeight(ref mixerPlayable);
                return;
            }

            if(currentClipState.Clip == null)
            {
                ClearInputWeight(ref mixerPlayable);
                return;
            }

            SingleClipPlayableState prevClipState = null;
            if(targetLayer.PrevState != ClipState.Invalid)
            {
                prevClipState = _clipDictionary[(int)targetLayer.PrevState];
            }
            //最新状態に設定する
            targetLayer.CurrentPlayableState = currentClipState;

            //CrossFadeのブレンド
            bool isCrossFade = IsCrossFade(targetLayer.BlendWeight, ref currentClipState, ref prevClipState);

            // 紐づけ（connect）が必要かどうか（connectを連続して呼ぶことはできない、モーションが切り替わるタイミングでDisconnectを呼ばないといけない）
            bool needConnect = DisconnectFromNeedConnect(ref mixerPlayable, currentClipState, prevClipState, targetLayer.ConnectedClipStateArray, isCrossFade);
            //再生時にクリップ長さが取れる
            if (isCrossFade)
            {
                // ブレンドカーブを計算してウェイト設定(合計が1になるように調整する)
                SetInputWeight(ref mixerPlayable, targetLayer.BlendWeight);

                PlayMotion(targetLayer.PlayTime, currentClipState, targetLayer.ConnectedClipStateArray, needConnect, ref mixerPlayable, Mixer.Mixer1);
                PlayMotion(targetLayer.PrevTime, prevClipState, targetLayer.ConnectedClipStateArray, needConnect, ref mixerPlayable, Mixer.Mixer2);
            }
            else
            {
                // 単発再生
                SetInputWeight(ref mixerPlayable, 1.0f);
                PlayMotion(targetLayer.PlayTime, currentClipState, targetLayer.ConnectedClipStateArray, needConnect, ref mixerPlayable, Mixer.Mixer1);
            }
        }
    }
}
