/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// TrainingReportIndexAPIのリクエスト
    /// </summary>
    public sealed class TrainingReportIndexRequest : RequestBase<TrainingReportIndexResponse>
    {

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<TrainingReportIndexResponse> onSuccess,
            Action<ErrorType, int, TrainingReportIndexResponse> onError)
        {
            return new TrainingReportIndexTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// TrainingReportIndexAPIのレスポンス
    /// </summary>
    public sealed class TrainingReportIndexResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// トレーニング報告書ユーザー情報
            /// </summary>
            public TrainingReportInfo training_report_info;
            /// <summary>
            /// 報酬一覧(表示用）
            /// </summary>
            public DisplayRewardInfo[] reward_info_array;
            /// <summary>
            /// 報酬情報(アイテムデータ更新用）
            /// </summary>
            public RewardSummaryInfo reward_summary_info;
            /// <summary>
            /// トレーニングパスシーズン変更フラグ(0:非表示/1:シーズン切替ダイアログ表示）
            /// </summary>
            public bool season_change_flag;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class TrainingReportIndexTask : IHttpTask
    {
        private byte[] postData;
        private Action<TrainingReportIndexResponse> onSuccess;
        private Action<ErrorType, int, TrainingReportIndexResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "TrainingReportIndexTask" class.
        /// </summary>
        public TrainingReportIndexTask(
           TrainingReportIndexRequest request,
           Action<TrainingReportIndexResponse> onSuccess,
           Action<ErrorType, int, TrainingReportIndexResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/training_report/index", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            TrainingReportIndexResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<TrainingReportIndexResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【TrainingReportIndexResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
