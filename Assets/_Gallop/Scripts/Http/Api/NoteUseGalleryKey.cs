/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// NoteUseGalleryKeyAPIのリクエスト
    /// </summary>
    public sealed class NoteUseGalleryKeyRequest : RequestBase<NoteUseGalleryKeyResponse>
    {
        /// <summary>
        /// ギャラリー区分（0:育成イベントギャラリー 1:トークギャラリー 2:システムボイス）
        /// </summary>
        public int gallery_type;
        /// <summary>
        /// クライアント所持数
        /// </summary>
        public int client_own_num;
        /// <summary>
        /// ストーリーID配列
        /// </summary>
        public int[] story_id_array;
        /// <summary>
        /// 既読システムボイスID配列（クライアント保持登録用）
        /// </summary>
        public NoteDataForRegist[] voice_data_array;
        /// <summary>
        /// ストーリーidが全キャラ共通のストーリーに関する情報
        /// </summary>
        public GalleryCommonStoryIdInfo[] common_story_id_info;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<NoteUseGalleryKeyResponse> onSuccess,
            Action<ErrorType, int, NoteUseGalleryKeyResponse> onError)
        {
            return new NoteUseGalleryKeyTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// NoteUseGalleryKeyAPIのレスポンス
    /// </summary>
    public sealed class NoteUseGalleryKeyResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 使用したアイテム情報
            /// </summary>
            public UserItem[] item_info_array;
            /// <summary>
            /// 既読システムボイスID配列（キャラ別）
            /// </summary>
            public NoteDataForDisplay[] event_data_array;
            /// <summary>
            /// 既読ホーム画面ストーリーID配列
            /// </summary>
            public HomeStoryDataForDisplay[] home_story_data_array;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class NoteUseGalleryKeyTask : IHttpTask
    {
        private byte[] postData;
        private Action<NoteUseGalleryKeyResponse> onSuccess;
        private Action<ErrorType, int, NoteUseGalleryKeyResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "NoteUseGalleryKeyTask" class.
        /// </summary>
        public NoteUseGalleryKeyTask(
           NoteUseGalleryKeyRequest request,
           Action<NoteUseGalleryKeyResponse> onSuccess,
           Action<ErrorType, int, NoteUseGalleryKeyResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/note/use_gallery_key", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            NoteUseGalleryKeyResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<NoteUseGalleryKeyResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【NoteUseGalleryKeyResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
