/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeFreeChangeRunningStyleAPIのリクエスト
    /// </summary>
    public sealed class SingleModeFreeChangeRunningStyleRequest : RequestBase<SingleModeFreeChangeRunningStyleResponse>
    {
        /// <summary>
        /// シングルモードレースプログラムID
        /// </summary>
        public int program_id;
        /// <summary>
        /// 走法
        /// </summary>
        public int running_style;
        /// <summary>
        /// 現在ターン
        /// </summary>
        public int current_turn;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeFreeChangeRunningStyleResponse> onSuccess,
            Action<ErrorType, int, SingleModeFreeChangeRunningStyleResponse> onError)
        {
            return new SingleModeFreeChangeRunningStyleTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeFreeChangeRunningStyleAPIのレスポンス
    /// </summary>
    public sealed class SingleModeFreeChangeRunningStyleResponse : ResponseCommon
    {
        public class CommonResponse
        {
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeFreeChangeRunningStyleTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeFreeChangeRunningStyleResponse> onSuccess;
        private Action<ErrorType, int, SingleModeFreeChangeRunningStyleResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeFreeChangeRunningStyleTask" class.
        /// </summary>
        public SingleModeFreeChangeRunningStyleTask(
           SingleModeFreeChangeRunningStyleRequest request,
           Action<SingleModeFreeChangeRunningStyleResponse> onSuccess,
           Action<ErrorType, int, SingleModeFreeChangeRunningStyleResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_free/change_running_style", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeFreeChangeRunningStyleResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeFreeChangeRunningStyleResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeFreeChangeRunningStyleResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
