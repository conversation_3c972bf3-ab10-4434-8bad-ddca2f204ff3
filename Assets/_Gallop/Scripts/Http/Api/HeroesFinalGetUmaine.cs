/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// HeroesFinalGetUmaineAPIのリクエスト
    /// </summary>
    public sealed class HeroesFinalGetUmaineRequest : RequestBase<HeroesFinalGetUmaineResponse>
    {

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<HeroesFinalGetUmaineResponse> onSuccess,
            Action<ErrorType, int, HeroesFinalGetUmaineResponse> onError)
        {
            return new HeroesFinalGetUmaineTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// HeroesFinalGetUmaineAPIのレスポンス
    /// </summary>
    public sealed class HeroesFinalGetUmaineResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// レース情報 ブロック1
            /// </summary>
            public HeroesFinalRaceInfo[] race_info_array_1;
            /// <summary>
            /// レース情報 ブロック2
            /// </summary>
            public HeroesFinalRaceInfo[] race_info_array_2;
            /// <summary>
            /// 初回ウマいね報酬を受け取り済みのグループID配列
            /// </summary>
            public int[] received_reward_group_array;
            /// <summary>
            /// ウマいねTIPS表示フラグ
            /// </summary>
            public int first_access_umaine_tips_flag;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class HeroesFinalGetUmaineTask : IHttpTask
    {
        private byte[] postData;
        private Action<HeroesFinalGetUmaineResponse> onSuccess;
        private Action<ErrorType, int, HeroesFinalGetUmaineResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "HeroesFinalGetUmaineTask" class.
        /// </summary>
        public HeroesFinalGetUmaineTask(
           HeroesFinalGetUmaineRequest request,
           Action<HeroesFinalGetUmaineResponse> onSuccess,
           Action<ErrorType, int, HeroesFinalGetUmaineResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/heroes/final_get_umaine", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            HeroesFinalGetUmaineResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<HeroesFinalGetUmaineResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【HeroesFinalGetUmaineResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
