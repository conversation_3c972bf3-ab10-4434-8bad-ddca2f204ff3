/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// HeroesStage1EditTeamNameAPIのリクエスト
    /// </summary>
    public sealed class HeroesStage1EditTeamNameRequest : RequestBase<HeroesStage1EditTeamNameResponse>
    {
        /// <summary>
        /// 変更したいチーム名
        /// </summary>
        public string name;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<HeroesStage1EditTeamNameResponse> onSuccess,
            Action<ErrorType, int, HeroesStage1EditTeamNameResponse> onError)
        {
            return new HeroesStage1EditTeamNameTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// HeroesStage1EditTeamNameAPIのレスポンス
    /// </summary>
    public sealed class HeroesStage1EditTeamNameResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 変更後のチーム名
            /// </summary>
            public string name;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class HeroesStage1EditTeamNameTask : IHttpTask
    {
        private byte[] postData;
        private Action<HeroesStage1EditTeamNameResponse> onSuccess;
        private Action<ErrorType, int, HeroesStage1EditTeamNameResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "HeroesStage1EditTeamNameTask" class.
        /// </summary>
        public HeroesStage1EditTeamNameTask(
           HeroesStage1EditTeamNameRequest request,
           Action<HeroesStage1EditTeamNameResponse> onSuccess,
           Action<ErrorType, int, HeroesStage1EditTeamNameResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/heroes/stage1_edit_team_name", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            HeroesStage1EditTeamNameResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<HeroesStage1EditTeamNameResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【HeroesStage1EditTeamNameResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
