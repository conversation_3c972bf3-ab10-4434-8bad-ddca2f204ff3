/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// PaymentItemListAPIのリクエスト
    /// </summary>
    public sealed class PaymentItemListRequest : RequestBase<PaymentItemListResponse>
    {

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<PaymentItemListResponse> onSuccess,
            Action<ErrorType, int, PaymentItemListResponse> onError)
        {
            return new PaymentItemListTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// PaymentItemListAPIのレスポンス
    /// </summary>
    public sealed class PaymentItemListResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 課金アイテムリスト
            /// </summary>
            public PaymentPurchaseItemParam[] data;
            /// <summary>
            /// ユーザー期間パック購入情報
            /// </summary>
            public PaymentSeasonPackInfo season_pack_info;
            /// <summary>
            /// 前回の課金アイテムリスト表示時間（unixtime)
            /// </summary>
            public int last_checked_time;
            /// <summary>
            /// 新人トレーナー応援引換券情報
            /// </summary>
            public StartDashExchangePayment[] start_dash_exchange_array;
            /// <summary>
            /// 因子課金パス購入情報
            /// </summary>
            public FactorPremiumPassInfo factor_premium_pass_info;
            /// <summary>
            /// 指定因子パス購入情報
            /// </summary>
            public FactorOrderPassInfo factor_order_pass_info;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class PaymentItemListTask : IHttpTask
    {
        private byte[] postData;
        private Action<PaymentItemListResponse> onSuccess;
        private Action<ErrorType, int, PaymentItemListResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "PaymentItemListTask" class.
        /// </summary>
        public PaymentItemListTask(
           PaymentItemListRequest request,
           Action<PaymentItemListResponse> onSuccess,
           Action<ErrorType, int, PaymentItemListResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/payment/item_list", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            PaymentItemListResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<PaymentItemListResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【PaymentItemListResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
