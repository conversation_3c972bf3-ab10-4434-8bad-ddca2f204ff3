/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeMechaGetChoiceRewardAPIのリクエスト
    /// </summary>
    public sealed class SingleModeMechaGetChoiceRewardRequest : RequestBase<SingleModeMechaGetChoiceRewardResponse>
    {
        /// <summary>
        /// イベントID
        /// </summary>
        public int event_id;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeMechaGetChoiceRewardResponse> onSuccess,
            Action<ErrorType, int, SingleModeMechaGetChoiceRewardResponse> onError)
        {
            return new SingleModeMechaGetChoiceRewardTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeMechaGetChoiceRewardAPIのレスポンス
    /// </summary>
    public sealed class SingleModeMechaGetChoiceRewardResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// イベント選択肢報酬配列
            /// </summary>
            public EventChoiceReward[] choice_reward_array;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeMechaGetChoiceRewardTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeMechaGetChoiceRewardResponse> onSuccess;
        private Action<ErrorType, int, SingleModeMechaGetChoiceRewardResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeMechaGetChoiceRewardTask" class.
        /// </summary>
        public SingleModeMechaGetChoiceRewardTask(
           SingleModeMechaGetChoiceRewardRequest request,
           Action<SingleModeMechaGetChoiceRewardResponse> onSuccess,
           Action<ErrorType, int, SingleModeMechaGetChoiceRewardResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_mecha/get_choice_reward", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeMechaGetChoiceRewardResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeMechaGetChoiceRewardResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeMechaGetChoiceRewardResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
