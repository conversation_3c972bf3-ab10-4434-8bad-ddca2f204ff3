/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// TeamStadiumTeamEditAPIのリクエスト
    /// </summary>
    public sealed class TeamStadiumTeamEditRequest : RequestBase<TeamStadiumTeamEditResponse>
    {
        /// <summary>
        /// チーム競技場チーム情報
        /// </summary>
        public TeamStadiumTeamData[] team_data_array;
        /// <summary>
        /// チーム競技場チーム総合評価点
        /// </summary>
        public int team_evaluation_point;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<TeamStadiumTeamEditResponse> onSuccess,
            Action<ErrorType, int, TeamStadiumTeamEditResponse> onError)
        {
            return new TeamStadiumTeamEditTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// TeamStadiumTeamEditAPIのレスポンス
    /// </summary>
    public sealed class TeamStadiumTeamEditResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 過去最高クラス更新報酬受け取り情報
            /// </summary>
            public DisplayRewardInfo[] reward_info_array;
            /// <summary>
            /// 変更前ランク
            /// </summary>
            public int before_rank;
            /// <summary>
            /// 変更後ランク
            /// </summary>
            public int after_rank;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class TeamStadiumTeamEditTask : IHttpTask
    {
        private byte[] postData;
        private Action<TeamStadiumTeamEditResponse> onSuccess;
        private Action<ErrorType, int, TeamStadiumTeamEditResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "TeamStadiumTeamEditTask" class.
        /// </summary>
        public TeamStadiumTeamEditTask(
           TeamStadiumTeamEditRequest request,
           Action<TeamStadiumTeamEditResponse> onSuccess,
           Action<ErrorType, int, TeamStadiumTeamEditResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/team_stadium/team_edit", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            TeamStadiumTeamEditResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<TeamStadiumTeamEditResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【TeamStadiumTeamEditResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
