/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// LiveTheaterIndexAPIのリクエスト
    /// </summary>
    public sealed class LiveTheaterIndexRequest : RequestBase<LiveTheaterIndexResponse>
    {

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<LiveTheaterIndexResponse> onSuccess,
            Action<ErrorType, int, LiveTheaterIndexResponse> onError)
        {
            return new LiveTheaterIndexTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// LiveTheaterIndexAPIのレスポンス
    /// </summary>
    public sealed class LiveTheaterIndexResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// ライブシアターの各楽曲の保存情報
            /// </summary>
            public LiveTheaterSaveInfo[] live_theater_save_info_array;
            /// <summary>
            /// ライブシアターに最後に入室した時刻(前回分)
            /// </summary>
            public long live_theater_last_checked_time;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class LiveTheaterIndexTask : IHttpTask
    {
        private byte[] postData;
        private Action<LiveTheaterIndexResponse> onSuccess;
        private Action<ErrorType, int, LiveTheaterIndexResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "LiveTheaterIndexTask" class.
        /// </summary>
        public LiveTheaterIndexTask(
           LiveTheaterIndexRequest request,
           Action<LiveTheaterIndexResponse> onSuccess,
           Action<ErrorType, int, LiveTheaterIndexResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/live_theater/index", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            LiveTheaterIndexResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<LiveTheaterIndexResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【LiveTheaterIndexResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
