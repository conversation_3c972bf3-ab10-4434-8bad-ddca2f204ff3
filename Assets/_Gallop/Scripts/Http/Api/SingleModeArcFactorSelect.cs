/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeArcFactorSelectAPIのリクエスト
    /// </summary>
    public sealed class SingleModeArcFactorSelectRequest : RequestBase<SingleModeArcFactorSelectResponse>
    {
        /// <summary>
        /// 現在ターン
        /// </summary>
        public int current_turn;
        /// <summary>
        /// 取得アップデートスキルID配列
        /// </summary>
        public int[] skill_id_array;
        /// <summary>
        /// 獲得進化スキル配列
        /// </summary>
        public SingleModeGainSkillUpgrade[] gain_skill_upgrade_info_array;
        /// <summary>
        /// 指定因子リクエスト
        /// </summary>
        public FactorOrderRequest factor_order_request;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeArcFactorSelectResponse> onSuccess,
            Action<ErrorType, int, SingleModeArcFactorSelectResponse> onError)
        {
            return new SingleModeArcFactorSelectTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeArcFactorSelectAPIのレスポンス
    /// </summary>
    public sealed class SingleModeArcFactorSelectResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// api factor_select共通パラメータ
            /// </summary>
            public SingleModeFactorSelectCommon single_mode_factor_select_common;
            /// <summary>
            /// api factor_order共通パラメータ
            /// </summary>
            public SingleModeFactorOrderCommon single_mode_factor_order_common;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeArcFactorSelectTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeArcFactorSelectResponse> onSuccess;
        private Action<ErrorType, int, SingleModeArcFactorSelectResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeArcFactorSelectTask" class.
        /// </summary>
        public SingleModeArcFactorSelectTask(
           SingleModeArcFactorSelectRequest request,
           Action<SingleModeArcFactorSelectResponse> onSuccess,
           Action<ErrorType, int, SingleModeArcFactorSelectResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_arc/factor_select", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeArcFactorSelectResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeArcFactorSelectResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeArcFactorSelectResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
