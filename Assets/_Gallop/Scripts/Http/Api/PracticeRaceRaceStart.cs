/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// PracticeRaceRaceStartAPIのリクエスト
    /// </summary>
    public sealed class PracticeRaceRaceStartRequest : RequestBase<PracticeRaceRaceStartResponse>
    {
        /// <summary>
        /// レースインスタンスID
        /// </summary>
        public int race_instance_id;
        /// <summary>
        /// 季節
        /// </summary>
        public int season;
        /// <summary>
        /// 天候
        /// </summary>
        public int weather;
        /// <summary>
        /// バ場状態
        /// </summary>
        public int ground_condition;
        /// <summary>
        /// 時間帯
        /// </summary>
        public int race_time;
        /// <summary>
        /// やる気
        /// </summary>
        public int motivation;
        /// <summary>
        /// 参加人数
        /// </summary>
        public int entry_num;
        /// <summary>
        /// 参加キャラ情報
        /// </summary>
        public PracticeRaceEntryChara[] entry_chara_array;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<PracticeRaceRaceStartResponse> onSuccess,
            Action<ErrorType, int, PracticeRaceRaceStartResponse> onError)
        {
            return new PracticeRaceRaceStartTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// PracticeRaceRaceStartAPIのレスポンス
    /// </summary>
    public sealed class PracticeRaceRaceStartResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 育成済みキャラ情報
            /// </summary>
            public TrainedChara[] trained_chara_array;
            /// <summary>
            /// レース結果情報
            /// </summary>
            public RaceResultInfo race_result_info;
            /// <summary>
            /// エントリー情報
            /// </summary>
            public PracticeRaceEntryInfo[] entry_info_array;
            /// <summary>
            /// 練習レースID
            /// </summary>
            public int practice_race_id;
            /// <summary>
            /// 進行状態（0:通常/1:レース出走中）
            /// </summary>
            public int state;
            /// <summary>
            /// 練習パートナーオーナー情報配列
            /// </summary>
            public PracticePartnerOwnerInfo[] practice_partner_owner_info_array;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class PracticeRaceRaceStartTask : IHttpTask
    {
        private byte[] postData;
        private Action<PracticeRaceRaceStartResponse> onSuccess;
        private Action<ErrorType, int, PracticeRaceRaceStartResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "PracticeRaceRaceStartTask" class.
        /// </summary>
        public PracticeRaceRaceStartTask(
           PracticeRaceRaceStartRequest request,
           Action<PracticeRaceRaceStartResponse> onSuccess,
           Action<ErrorType, int, PracticeRaceRaceStartResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/practice_race/race_start", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            PracticeRaceRaceStartResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<PracticeRaceRaceStartResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【PracticeRaceRaceStartResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
