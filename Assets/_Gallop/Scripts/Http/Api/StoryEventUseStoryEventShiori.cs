/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// StoryEventUseStoryEventShioriAPIのリクエスト
    /// </summary>
    public sealed class StoryEventUseStoryEventShioriRequest : RequestBase<StoryEventUseStoryEventShioriResponse>
    {
        /// <summary>
        /// story_event.csvのid
        /// </summary>
        public int story_event_id;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<StoryEventUseStoryEventShioriResponse> onSuccess,
            Action<ErrorType, int, StoryEventUseStoryEventShioriResponse> onError)
        {
            return new StoryEventUseStoryEventShioriTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// StoryEventUseStoryEventShioriAPIのレスポンス
    /// </summary>
    public sealed class StoryEventUseStoryEventShioriResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 交換で使用したアイテムの更新情報
            /// </summary>
            public UserItem[] use_item_info;
            /// <summary>
            /// ストーリーイベント解放情報
            /// </summary>
            public StoryEventStoryData[] story_event_story_data;
            /// <summary>
            /// 回想のしおり使用状況
            /// </summary>
            public StoryEventShioriData[] story_event_shiori_data;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class StoryEventUseStoryEventShioriTask : IHttpTask
    {
        private byte[] postData;
        private Action<StoryEventUseStoryEventShioriResponse> onSuccess;
        private Action<ErrorType, int, StoryEventUseStoryEventShioriResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "StoryEventUseStoryEventShioriTask" class.
        /// </summary>
        public StoryEventUseStoryEventShioriTask(
           StoryEventUseStoryEventShioriRequest request,
           Action<StoryEventUseStoryEventShioriResponse> onSuccess,
           Action<ErrorType, int, StoryEventUseStoryEventShioriResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/story_event/use_story_event_shiori", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            StoryEventUseStoryEventShioriResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<StoryEventUseStoryEventShioriResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【StoryEventUseStoryEventShioriResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
