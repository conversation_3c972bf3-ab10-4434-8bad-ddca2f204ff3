/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// JobsCheckCompleteAPIのリクエスト
    /// </summary>
    public sealed class JobsCheckCompleteRequest : RequestBase<JobsCheckCompleteResponse>
    {
        /// <summary>
        /// 現在TP情報
        /// </summary>
        public TpInfo tp_info;
        /// <summary>
        /// TP消費量情報. まとめて確認の場合はデータが入らない
        /// </summary>
        public JobsReduceTp[] jobs_reduce_tp_array;
        /// <summary>
        /// 興行完了時使用TP回復アイテム情報。使用しない場合はデータが入らない
        /// </summary>
        public JobsUseItemInfo jobs_use_item_info;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<JobsCheckCompleteResponse> onSuccess,
            Action<ErrorType, int, JobsCheckCompleteResponse> onError)
        {
            return new JobsCheckCompleteTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// JobsCheckCompleteAPIのレスポンス
    /// </summary>
    public sealed class JobsCheckCompleteResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 変化後TP情報
            /// </summary>
            public TpInfo tp_info;
            /// <summary>
            /// 完了した各興行の結果情報（要素数は0～3）（完了した興行がある場合のみ送る）
            /// </summary>
            public JobsResultInfo[] jobs_result_info_array;
            /// <summary>
            /// 興行の報酬の獲得上限情報
            /// </summary>
            public JobsResultLimitInfo jobs_result_limit_info;
            /// <summary>
            /// 進行中の興行情報配列（要素数は0～3）。無い時はnull。
            /// </summary>
            public JobsGoingInfo[] jobs_going_info_array;
            /// <summary>
            /// ストーリーイベント情報(未開催ならnull)
            /// </summary>
            public StoryEventSingleModeResult story_event_info;
            /// <summary>
            /// 因子研究イベントゲージ情報(未開催ならnull)
            /// </summary>
            public FactorResearchGaugeInfo factor_research_gauge_info;
            /// <summary>
            /// トレーナー技能試験情報（未開催ならnull）
            /// </summary>
            public TrainingChallengeResult training_challenge_result;
            /// <summary>
            /// 限定興行情報配列（無い場合、期間外はnull）
            /// </summary>
            public JobsLimitedInfo[] jobs_limited_info_array;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class JobsCheckCompleteTask : IHttpTask
    {
        private byte[] postData;
        private Action<JobsCheckCompleteResponse> onSuccess;
        private Action<ErrorType, int, JobsCheckCompleteResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "JobsCheckCompleteTask" class.
        /// </summary>
        public JobsCheckCompleteTask(
           JobsCheckCompleteRequest request,
           Action<JobsCheckCompleteResponse> onSuccess,
           Action<ErrorType, int, JobsCheckCompleteResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/jobs/check_complete", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            JobsCheckCompleteResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<JobsCheckCompleteResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【JobsCheckCompleteResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
