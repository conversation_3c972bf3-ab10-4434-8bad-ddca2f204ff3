/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// DailyLegendRaceRaceEntryAPIのリクエスト
    /// </summary>
    public sealed class DailyLegendRaceRaceEntryRequest : RequestBase<DailyLegendRaceRaceEntryResponse>
    {
        /// <summary>
        /// デイリーレジェンドレースID
        /// </summary>
        public int daily_legend_race_id;
        /// <summary>
        /// 育成済みウマ娘ID
        /// </summary>
        public int trained_chara_id;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<DailyLegendRaceRaceEntryResponse> onSuccess,
            Action<ErrorType, int, DailyLegendRaceRaceEntryResponse> onError)
        {
            return new DailyLegendRaceRaceEntryTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// DailyLegendRaceRaceEntryAPIのレスポンス
    /// </summary>
    public sealed class DailyLegendRaceRaceEntryResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 出走ウマ娘データ
            /// </summary>
            public RaceHorseData[] race_horse_data_array;
            /// <summary>
            /// 季節
            /// </summary>
            public int season;
            /// <summary>
            /// 天候
            /// </summary>
            public int weather;
            /// <summary>
            /// 馬場状態
            /// </summary>
            public int ground_condition;
            /// <summary>
            /// 乱数シード
            /// </summary>
            public int random_seed;
            /// <summary>
            /// レースインスタンスID
            /// </summary>
            public int race_instance_id;
            /// <summary>
            /// レースの進行状態
            /// </summary>
            public int state;
            /// <summary>
            /// レース出走中のキャラID
            /// </summary>
            public int trained_chara_id;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class DailyLegendRaceRaceEntryTask : IHttpTask
    {
        private byte[] postData;
        private Action<DailyLegendRaceRaceEntryResponse> onSuccess;
        private Action<ErrorType, int, DailyLegendRaceRaceEntryResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "DailyLegendRaceRaceEntryTask" class.
        /// </summary>
        public DailyLegendRaceRaceEntryTask(
           DailyLegendRaceRaceEntryRequest request,
           Action<DailyLegendRaceRaceEntryResponse> onSuccess,
           Action<ErrorType, int, DailyLegendRaceRaceEntryResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/daily_legend_race/race_entry", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            DailyLegendRaceRaceEntryResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<DailyLegendRaceRaceEntryResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【DailyLegendRaceRaceEntryResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
