/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeCookRaceStartAPIのリクエスト
    /// </summary>
    public sealed class SingleModeCookRaceStartRequest : RequestBase<SingleModeCookRaceStartResponse>
    {
        /// <summary>
        /// 0:通常, 1:短縮版
        /// </summary>
        public int is_short;
        /// <summary>
        /// 現在ターン
        /// </summary>
        public int current_turn;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeCookRaceStartResponse> onSuccess,
            Action<ErrorType, int, SingleModeCookRaceStartResponse> onError)
        {
            return new SingleModeCookRaceStartTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeCookRaceStartAPIのレスポンス
    /// </summary>
    public sealed class SingleModeCookRaceStartResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// シングルレーススタート情報
            /// </summary>
            public SingleRaceStartInfo race_start_info;
            /// <summary>
            /// レース展開(シミュレート結果)
            /// </summary>
            public string race_scenario;
            /// <summary>
            /// お料理編用の情報
            /// </summary>
            public SingleModeCookDataSet cook_data_set;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeCookRaceStartTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeCookRaceStartResponse> onSuccess;
        private Action<ErrorType, int, SingleModeCookRaceStartResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeCookRaceStartTask" class.
        /// </summary>
        public SingleModeCookRaceStartTask(
           SingleModeCookRaceStartRequest request,
           Action<SingleModeCookRaceStartResponse> onSuccess,
           Action<ErrorType, int, SingleModeCookRaceStartResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_cook/race_start", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeCookRaceStartResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeCookRaceStartResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeCookRaceStartResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
