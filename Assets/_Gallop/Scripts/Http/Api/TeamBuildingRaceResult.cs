/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// TeamBuildingRaceResultAPIのリクエスト
    /// </summary>
    public sealed class TeamBuildingRaceResultRequest : RequestBase<TeamBuildingRaceResultResponse>
    {

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<TeamBuildingRaceResultResponse> onSuccess,
            Action<ErrorType, int, TeamBuildingRaceResultResponse> onError)
        {
            return new TeamBuildingRaceResultTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// TeamBuildingRaceResultAPIのレスポンス
    /// </summary>
    public sealed class TeamBuildingRaceResultResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// レースローテーション情報配列
            /// </summary>
            public RaceRotationInfo race_rotation_info;
            /// <summary>
            /// チームスコア
            /// </summary>
            public int team_score;
            /// <summary>
            /// スカウトポイント情報
            /// </summary>
            public TeamBuildingScoutPointInfo scout_point_info;
            /// <summary>
            /// 基本報酬アイテム配列
            /// </summary>
            public ResponseItem[] basic_reward_array;
            /// <summary>
            /// 累計勝利報酬アイテム配列
            /// </summary>
            public ResponseItem[] win_reward_array;
            /// <summary>
            /// スーパースペシャルレース特別報酬アイテム配列
            /// </summary>
            public ResponseItem[] super_special_reward_array;
            /// <summary>
            /// チームスコア報酬配列
            /// </summary>
            public ResponseItem[] team_score_reward_array;
            /// <summary>
            /// 報酬情報
            /// </summary>
            public RewardSummaryInfo reward_summary_info;
            /// <summary>
            /// 新規登録図鑑キャラ情報配列
            /// </summary>
            public TeamBuildingCollectionChara[] add_collection_chara_array;
            /// <summary>
            /// 新規登録図鑑グループID配列
            /// </summary>
            public int[] add_collection_group_id_array;
            /// <summary>
            /// 進行状態(0:未出走, 1:パドック, 2:レース, 3:スカウト, 4:レーススキップ, 5:初回遷移)
            /// </summary>
            public int state;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class TeamBuildingRaceResultTask : IHttpTask
    {
        private byte[] postData;
        private Action<TeamBuildingRaceResultResponse> onSuccess;
        private Action<ErrorType, int, TeamBuildingRaceResultResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "TeamBuildingRaceResultTask" class.
        /// </summary>
        public TeamBuildingRaceResultTask(
           TeamBuildingRaceResultRequest request,
           Action<TeamBuildingRaceResultResponse> onSuccess,
           Action<ErrorType, int, TeamBuildingRaceResultResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/team_building/race_result", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            TeamBuildingRaceResultResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<TeamBuildingRaceResultResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【TeamBuildingRaceResultResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
