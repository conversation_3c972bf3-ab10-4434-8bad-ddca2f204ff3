/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeFreeFactorLotteryAPIのリクエスト
    /// </summary>
    public sealed class SingleModeFreeFactorLotteryRequest : RequestBase<SingleModeFreeFactorLotteryResponse>
    {
        /// <summary>
        /// 抽選回数
        /// </summary>
        public int lottery_count;
        /// <summary>
        /// tp情報
        /// </summary>
        public TpInfo tp_info;
        /// <summary>
        /// 消費するTP情報
        /// </summary>
        public int use_tp;
        /// <summary>
        /// 抽選タイプ
        /// </summary>
        public int request_lottery_type;
        /// <summary>
        /// 消費する有償コイン
        /// </summary>
        public int use_coin;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeFreeFactorLotteryResponse> onSuccess,
            Action<ErrorType, int, SingleModeFreeFactorLotteryResponse> onError)
        {
            return new SingleModeFreeFactorLotteryTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeFreeFactorLotteryAPIのレスポンス
    /// </summary>
    public sealed class SingleModeFreeFactorLotteryResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// api factor_lottery共通パラメータ
            /// </summary>
            public SingleModeFactorLotteryCommon single_mode_factor_lottery_common;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeFreeFactorLotteryTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeFreeFactorLotteryResponse> onSuccess;
        private Action<ErrorType, int, SingleModeFreeFactorLotteryResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeFreeFactorLotteryTask" class.
        /// </summary>
        public SingleModeFreeFactorLotteryTask(
           SingleModeFreeFactorLotteryRequest request,
           Action<SingleModeFreeFactorLotteryResponse> onSuccess,
           Action<ErrorType, int, SingleModeFreeFactorLotteryResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_free/factor_lottery", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeFreeFactorLotteryResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeFreeFactorLotteryResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeFreeFactorLotteryResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
