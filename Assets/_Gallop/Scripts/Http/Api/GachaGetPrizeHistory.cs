/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// GachaGetPrizeHistoryAPIのリクエスト
    /// </summary>
    public sealed class GachaGetPrizeHistoryRequest : RequestBase<GachaGetPrizeHistoryResponse>
    {
        /// <summary>
        /// ガチャID
        /// </summary>
        public int gacha_id;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<GachaGetPrizeHistoryResponse> onSuccess,
            Action<ErrorType, int, GachaGetPrizeHistoryResponse> onError)
        {
            return new GachaGetPrizeHistoryTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// GachaGetPrizeHistoryAPIのレスポンス
    /// </summary>
    public sealed class GachaGetPrizeHistoryResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// プライズ履歴配列
            /// </summary>
            public GachaPrizeHistory[] prize_history_array;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class GachaGetPrizeHistoryTask : IHttpTask
    {
        private byte[] postData;
        private Action<GachaGetPrizeHistoryResponse> onSuccess;
        private Action<ErrorType, int, GachaGetPrizeHistoryResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "GachaGetPrizeHistoryTask" class.
        /// </summary>
        public GachaGetPrizeHistoryTask(
           GachaGetPrizeHistoryRequest request,
           Action<GachaGetPrizeHistoryResponse> onSuccess,
           Action<ErrorType, int, GachaGetPrizeHistoryResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/gacha/get_prize_history", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            GachaGetPrizeHistoryResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<GachaGetPrizeHistoryResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【GachaGetPrizeHistoryResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
