/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeSportUseItemAPIのリクエスト
    /// </summary>
    public sealed class SingleModeSportUseItemRequest : RequestBase<SingleModeSportUseItemResponse>
    {
        /// <summary>
        /// アイテムID
        /// </summary>
        public int item_id;
        /// <summary>
        /// 選択番号1
        /// </summary>
        public int select_id_1;
        /// <summary>
        /// 選択番号2
        /// </summary>
        public int select_id_2;
        /// <summary>
        /// 選択番号3
        /// </summary>
        public int select_id_3;
        /// <summary>
        /// 現在アイテム数
        /// </summary>
        public int current_num;
        /// <summary>
        /// 現在ターン
        /// </summary>
        public int current_turn;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeSportUseItemResponse> onSuccess,
            Action<ErrorType, int, SingleModeSportUseItemResponse> onError)
        {
            return new SingleModeSportUseItemTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeSportUseItemAPIのレスポンス
    /// </summary>
    public sealed class SingleModeSportUseItemResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 変化後のキャラ情報
            /// </summary>
            public SingleModeChara chara_info;
            /// <summary>
            /// コマンドの選択などを行うホーム画面に必要な情報
            /// </summary>
            public SingleModeHomeInfo home_info;
            /// <summary>
            /// スポーツ編用の情報
            /// </summary>
            public SingleModeSportDataSet sport_data_set;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeSportUseItemTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeSportUseItemResponse> onSuccess;
        private Action<ErrorType, int, SingleModeSportUseItemResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeSportUseItemTask" class.
        /// </summary>
        public SingleModeSportUseItemTask(
           SingleModeSportUseItemRequest request,
           Action<SingleModeSportUseItemResponse> onSuccess,
           Action<ErrorType, int, SingleModeSportUseItemResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_sport/use_item", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeSportUseItemResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeSportUseItemResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeSportUseItemResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
