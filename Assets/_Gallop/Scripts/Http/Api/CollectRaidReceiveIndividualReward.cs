/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// CollectRaidReceiveIndividualRewardAPIのリクエスト
    /// </summary>
    public sealed class CollectRaidReceiveIndividualRewardRequest : RequestBase<CollectRaidReceiveIndividualRewardResponse>
    {
        /// <summary>
        /// 個人報酬ID配列
        /// </summary>
        public int[] individual_reward_id_array;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<CollectRaidReceiveIndividualRewardResponse> onSuccess,
            Action<ErrorType, int, CollectRaidReceiveIndividualRewardResponse> onError)
        {
            return new CollectRaidReceiveIndividualRewardTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// CollectRaidReceiveIndividualRewardAPIのレスポンス
    /// </summary>
    public sealed class CollectRaidReceiveIndividualRewardResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 報酬アイテム配列
            /// </summary>
            public ResponseItem[] reward_array;
            /// <summary>
            /// 報酬情報
            /// </summary>
            public RewardSummaryInfo reward_summary_info;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class CollectRaidReceiveIndividualRewardTask : IHttpTask
    {
        private byte[] postData;
        private Action<CollectRaidReceiveIndividualRewardResponse> onSuccess;
        private Action<ErrorType, int, CollectRaidReceiveIndividualRewardResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "CollectRaidReceiveIndividualRewardTask" class.
        /// </summary>
        public CollectRaidReceiveIndividualRewardTask(
           CollectRaidReceiveIndividualRewardRequest request,
           Action<CollectRaidReceiveIndividualRewardResponse> onSuccess,
           Action<ErrorType, int, CollectRaidReceiveIndividualRewardResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/collect_raid/receive_individual_reward", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            CollectRaidReceiveIndividualRewardResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<CollectRaidReceiveIndividualRewardResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【CollectRaidReceiveIndividualRewardResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
