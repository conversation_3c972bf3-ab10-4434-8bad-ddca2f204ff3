/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeFreeStartAPIのリクエスト
    /// </summary>
    public sealed class SingleModeFreeStartRequest : RequestBase<SingleModeFreeStartResponse>
    {
        /// <summary>
        /// スタートキャラクター情報
        /// </summary>
        public SingleModeStartChara start_chara;
        /// <summary>
        /// 現在のTP情報
        /// </summary>
        public TpInfo tp_info;
        /// <summary>
        /// 現在のマニー
        /// </summary>
        public int current_money;
        /// <summary>
        /// 消費するTP情報
        /// </summary>
        public int use_tp;
        /// <summary>
        /// クライアントで算出した相性評価値
        /// </summary>
        public int current_succession_rank_point;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeFreeStartResponse> onSuccess,
            Action<ErrorType, int, SingleModeFreeStartResponse> onError)
        {
            return new SingleModeFreeStartTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeFreeStartAPIのレスポンス
    /// </summary>
    public sealed class SingleModeFreeStartResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// シングルモード スタート共通レスポンス
            /// </summary>
            public SingleModeStartCommon single_mode_start_common;
            /// <summary>
            /// フリー編用の情報
            /// </summary>
            public SingleModeFreeDataSet free_data_set;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeFreeStartTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeFreeStartResponse> onSuccess;
        private Action<ErrorType, int, SingleModeFreeStartResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeFreeStartTask" class.
        /// </summary>
        public SingleModeFreeStartTask(
           SingleModeFreeStartRequest request,
           Action<SingleModeFreeStartResponse> onSuccess,
           Action<ErrorType, int, SingleModeFreeStartResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_free/start", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeFreeStartResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeFreeStartResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeFreeStartResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
