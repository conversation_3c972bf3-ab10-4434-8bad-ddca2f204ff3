/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeVenusFactorReloadAPIのリクエスト
    /// </summary>
    public sealed class SingleModeVenusFactorReloadRequest : RequestBase<SingleModeVenusFactorReloadResponse>
    {
        /// <summary>
        /// 抽選回数
        /// </summary>
        public int lottery_count;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeVenusFactorReloadResponse> onSuccess,
            Action<ErrorType, int, SingleModeVenusFactorReloadResponse> onError)
        {
            return new SingleModeVenusFactorReloadTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeVenusFactorReloadAPIのレスポンス
    /// </summary>
    public sealed class SingleModeVenusFactorReloadResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// api factor_lottery共通パラメータ
            /// </summary>
            public SingleModeFactorLotteryCommon single_mode_factor_lottery_common;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeVenusFactorReloadTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeVenusFactorReloadResponse> onSuccess;
        private Action<ErrorType, int, SingleModeVenusFactorReloadResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeVenusFactorReloadTask" class.
        /// </summary>
        public SingleModeVenusFactorReloadTask(
           SingleModeVenusFactorReloadRequest request,
           Action<SingleModeVenusFactorReloadResponse> onSuccess,
           Action<ErrorType, int, SingleModeVenusFactorReloadResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_venus/factor_reload", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeVenusFactorReloadResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeVenusFactorReloadResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeVenusFactorReloadResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
