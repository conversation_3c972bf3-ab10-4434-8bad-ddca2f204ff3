/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System;
using System.Collections.Generic;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    /// <summary>
    /// SingleModeVenusSpiritUseAPIのリクエスト
    /// </summary>
    public sealed class SingleModeVenusSpiritUseRequest : RequestBase<SingleModeVenusSpiritUseResponse>
    {
        /// <summary>
        /// 現在ターン
        /// </summary>
        public int current_turn;

        /// <summary>
        /// タスク生成
        /// </summary>
        /// <param name="onSuccess">API通信成功時のコールバック</param>
        /// <param name="onError">API通信失敗時のコールバック</param>
        /// <returns>作成したタスク</returns>
        public override IHttpTask CreateTask(
            Action<SingleModeVenusSpiritUseResponse> onSuccess,
            Action<ErrorType, int, SingleModeVenusSpiritUseResponse> onError)
        {
            return new SingleModeVenusSpiritUseTask( this, onSuccess, onError );
        }
    }

    /// <summary>
    /// SingleModeVenusSpiritUseAPIのレスポンス
    /// </summary>
    public sealed class SingleModeVenusSpiritUseResponse : ResponseCommon
    {
        public class CommonResponse
        {
            /// <summary>
            /// 変化後のキャラ情報
            /// </summary>
            public SingleModeChara chara_info;
            /// <summary>
            /// コマンドの選択などを行うホーム画面に必要な情報
            /// </summary>
            public SingleModeHomeInfo home_info;
            /// <summary>
            /// ヴィーナス編用の情報
            /// </summary>
            public SingleModeVenusDataSet venus_data_set;
        }
        public CommonResponse data;
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public sealed class SingleModeVenusSpiritUseTask : IHttpTask
    {
        private byte[] postData;
        private Action<SingleModeVenusSpiritUseResponse> onSuccess;
        private Action<ErrorType, int, SingleModeVenusSpiritUseResponse> onError;
        private Dictionary<string, string> headers = new Dictionary<string, string>();
        private IWebRequest request;

        public int CacheTime => 0;

        public bool IsCompressed => true;

        /// <summary>
        /// Initializes a new instance of the "SingleModeVenusSpiritUseTask" class.
        /// </summary>
        public SingleModeVenusSpiritUseTask(
           SingleModeVenusSpiritUseRequest request,
           Action<SingleModeVenusSpiritUseResponse> onSuccess,
           Action<ErrorType, int, SingleModeVenusSpiritUseResponse> onError)
        {
            this.onSuccess = onSuccess;
            this.onError = onError;

            postData = MessagePack.MessagePackSerializer.Serialize(request);
            this.request = HttpManager.Instance.CreateRequest();
        }

        public void SetHeader(string key, string value)
        {
            headers[key] = value;
        }

        public IWebRequest Send(string url)
        {
            var sendHeaders = new Dictionary<string, string>(HttpManager.Instance.DefaultHeader);
            var e = headers.GetEnumerator();
            while (e.MoveNext())
            {
                sendHeaders[e.Current.Key] = e.Current.Value;
            }

            var sendData = postData;
            if (IsCompressed && HttpManager.Instance.CompressFunc != null)
            {
                sendData = HttpManager.Instance.CompressFunc(postData);
            }

            request.Post(url + "/single_mode_venus/spirit_use", sendData, sendHeaders);
            return request;
        }

        public bool Deserialize(byte[] body)
        {
            SingleModeVenusSpiritUseResponse response = null;
            try
            {
                response = MessagePack.MessagePackSerializer.Deserialize<SingleModeVenusSpiritUseResponse>(body);
            }
            catch (Exception e)
            {
                Debug.LogError("【SingleModeVenusSpiritUseResponse】Deserialize error at field: " + e.Message);
                OnError(ErrorType.DeserializeError, 0);
            }

            if (response != null)
            {
                if (response.data_headers == null )
                {
                    OnError(ErrorType.DeserializeError, 0);
                }
                else if (response.data_headers.result_code != ResultCode.API_RESULT_SUCCESS_CODE)
                {
                    onError(ErrorType.ResultCode, response.data_headers.result_code, response);
                }
                else
                {
                    onSuccess(response);
                    return true;
                }
            }

            return false;
        }

        public void OnError(ErrorType errorType, int resultCode)
        {
            onError(errorType, resultCode, null);
        }
    }
}
