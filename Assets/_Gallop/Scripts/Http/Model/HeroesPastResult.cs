/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class HeroesPastResult
    {
        /// <summary>
        /// heroes_data.csv の ID
        /// </summary>
        public int heroes_id;
        /// <summary>
        /// リーグランク
        /// </summary>
        public int league_rank;
        /// <summary>
        /// リーグスコア
        /// </summary>
        public int league_score;
        /// <summary>
        /// エクストラ出走者かどうか (0:出走者ではない 1:出走者)
        /// </summary>
        public int is_finalist;
    }
}
