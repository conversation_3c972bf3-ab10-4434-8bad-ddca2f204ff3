/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class CircleRequest
    {
        /// <summary>
        /// サークルID
        /// </summary>
        public int circle_id;
        /// <summary>
        /// ユーザId
        /// </summary>
        public long viewer_id;
        /// <summary>
        /// リクエストを出した時間
        /// </summary>
        public string update_time;
    }
}
