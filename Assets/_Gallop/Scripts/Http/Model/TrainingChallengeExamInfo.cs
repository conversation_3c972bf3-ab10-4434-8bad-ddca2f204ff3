/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class TrainingChallengeExamInfo
    {
        /// <summary>
        /// 試験ID
        /// </summary>
        public int exam_id;
        /// <summary>
        /// ハイスコア値
        /// </summary>
        public int high_score_value;
        /// <summary>
        /// クリア状態(1:未クリア, 2:可, 3:良, 4:優)
        /// </summary>
        public int result_type;
        /// <summary>
        /// ハイスコアウマ娘情報
        /// </summary>
        public TrainedChara high_score_trained_chara;
        /// <summary>
        /// １度挑戦済みか
        /// </summary>
        public bool is_challenged;
    }
}
