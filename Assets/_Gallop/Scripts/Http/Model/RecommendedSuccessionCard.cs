/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class RecommendedSuccessionCard
    {
        /// <summary>
        /// 育成ウマ娘のカードID
        /// </summary>
        public int card_id;
        /// <summary>
        /// 選択した走法が「逃げ」のときに推奨するかどうか
        /// </summary>
        public bool running_style_nige;
        /// <summary>
        /// 選択した走法が「先行」のときに推奨するかどうか
        /// </summary>
        public bool running_style_senko;
        /// <summary>
        /// 選択した走法が「差し」のときに推奨するかどうか
        /// </summary>
        public bool running_style_sashi;
        /// <summary>
        /// 選択した走法が「追込」のときに推奨するかどうか
        /// </summary>
        public bool running_style_oikomi;
    }
}
