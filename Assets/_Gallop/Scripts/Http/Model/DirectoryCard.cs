/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class DirectoryCard
    {
        /// <summary>
        /// カードID
        /// </summary>
        public int card_id;
        /// <summary>
        /// 歴代N位
        /// </summary>
        public int directory_ranking;
        /// <summary>
        /// 育成済みウマ娘情報
        /// </summary>
        public TrainedChara trained_chara;
    }
}
