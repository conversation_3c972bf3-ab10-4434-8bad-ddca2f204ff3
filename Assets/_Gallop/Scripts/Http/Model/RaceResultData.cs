/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class RaceResultData
    {
        /// <summary>
        /// ユーザーID(npcの場合は0)
        /// </summary>
        public long viewer_id;
        /// <summary>
        /// 着順
        /// </summary>
        public int finish_order;
        /// <summary>
        /// 着タイム
        /// </summary>
        public int finish_time;
        /// <summary>
        /// 着タイム（実時間。チート検出用なのでゲーム中ではこれを着タイムとして扱わないでください）
        /// </summary>
        public int finish_time_raw;
        /// <summary>
        /// 後着との着差。実数を10,000倍した値。
        /// </summary>
        public int bashin_diff_from_behind;
    }
}
