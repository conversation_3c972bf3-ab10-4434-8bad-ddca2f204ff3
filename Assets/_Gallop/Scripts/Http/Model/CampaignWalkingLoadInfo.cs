/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class CampaignWalkingLoadInfo
    {
        /// <summary>
        /// おさんぽゲージ
        /// </summary>
        public int walking_gauge;
        /// <summary>
        /// おさんぽゲージの最大値
        /// </summary>
        public int walking_gauge_max;
        /// <summary>
        /// 本日おさんぽした回数
        /// </summary>
        public int today_walking_num;
        /// <summary>
        /// ログインおさんぽボーナス演出を再生するか
        /// </summary>
        public bool show_login_bonus;
        /// <summary>
        /// TIPSを表示させるか
        /// </summary>
        public bool show_tips;
        /// <summary>
        /// 進行状況(0..なし, 1..中断データあり)
        /// </summary>
        public int resume_state;
        /// <summary>
        /// 中断データがある場合のキャラID(無ければ0)
        /// </summary>
        public int resume_chara_id;
        /// <summary>
        /// 中断データがある場合の場所ID(無ければ0)
        /// </summary>
        public int resume_location_id;
    }
}
