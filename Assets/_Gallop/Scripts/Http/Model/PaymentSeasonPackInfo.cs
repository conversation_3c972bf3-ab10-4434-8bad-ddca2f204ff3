/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class PaymentSeasonPackInfo
    {
        /// <summary>
        /// デイリージュエルパックの物品ID
        /// </summary>
        public int product_master_id;
        /// <summary>
        /// パック期間終了時間（unixtime)
        /// </summary>
        public int season_end_time;
        /// <summary>
        /// パック再購入可能時間（unixtime)
        /// </summary>
        public int repurchase_time;
        /// <summary>
        /// 通知ステータス（0:通知なし,1:再購入可能通知,2:期限切れ通知）
        /// </summary>
        public int notice_status;
    }
}
