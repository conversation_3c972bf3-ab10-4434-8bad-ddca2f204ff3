/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class GachaLimitItemInfo
    {
        /// <summary>
        /// 紐づくガチャID
        /// </summary>
        public int gacha_id;
        /// <summary>
        /// 個数
        /// </summary>
        public int num;
        /// <summary>
        /// アイテムに変換された個数
        /// </summary>
        public int converted_item_num;
    }
}
