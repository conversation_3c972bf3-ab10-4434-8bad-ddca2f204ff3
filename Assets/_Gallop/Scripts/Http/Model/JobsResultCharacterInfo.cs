/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class JobsResultCharacterInfo
    {
        /// <summary>
        /// キャラID
        /// </summary>
        public int chara_id;
        /// <summary>
        /// 衣装のID
        /// </summary>
        public int dress_id;
        /// <summary>
        /// 変化前のファン数
        /// </summary>
        public ulong before_fan_num;
        /// <summary>
        /// 変化後のファン数
        /// </summary>
        public ulong after_fan_num;
        /// <summary>
        /// 親愛度報酬情報
        /// </summary>
        public LovePointInfo love_point_info;
    }
}
