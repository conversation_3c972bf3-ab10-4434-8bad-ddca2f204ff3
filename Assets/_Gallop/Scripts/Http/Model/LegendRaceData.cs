/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class LegendRaceData
    {
        /// <summary>
        /// レジェンドレースID
        /// </summary>
        public int legend_race_id;
        /// <summary>
        /// クリア済み (0=未クリア, 1=クリア済)
        /// </summary>
        public int is_cleared;
        /// <summary>
        /// プレイ済み (0=未プレイ, 1=プレイ済)
        /// </summary>
        public int is_played;
        /// <summary>
        /// ボスデータ
        /// </summary>
        public RaceHorseData boss_data;
    }
}
