/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ProductOpenInfo
    {
        /// <summary>
        /// 商品ID一覧
        /// </summary>
        public int[] product_master_id_array;
        /// <summary>
        /// 購入期限
        /// </summary>
        public string purchase_limit_time;
    }
}
