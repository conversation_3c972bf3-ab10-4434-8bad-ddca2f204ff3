/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class HeroesCharaRaceStats
    {
        /// <summary>
        /// 育成済みキャラID
        /// </summary>
        public int trained_chara_id;
        /// <summary>
        /// 近走レース結果 新しい方が先頭
        /// </summary>
        public HeroesCharaRaceHistory[] race_history_array;
        /// <summary>
        /// 1着の回数
        /// </summary>
        public int rank_count_1;
        /// <summary>
        /// 2着の回数
        /// </summary>
        public int rank_count_2;
        /// <summary>
        /// 3着の回数
        /// </summary>
        public int rank_count_3;
        /// <summary>
        /// 着外の回数
        /// </summary>
        public int rank_count_unplaced;
    }
}
