/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ChampionsNewsCharaInfo
    {
        /// <summary>
        /// ユーザーid
        /// </summary>
        public long viewer_id;
        /// <summary>
        /// チームID
        /// </summary>
        public int team_id;
        /// <summary>
        /// チームメンバーID
        /// </summary>
        public int team_member_id;
        /// <summary>
        /// 月刊トゥインクル:キャラID
        /// </summary>
        public int[] news_chara_id_array;
        /// <summary>
        /// 月刊トゥインクル:キャラコメントID
        /// </summary>
        public int news_chara_comment_id;
    }
}
