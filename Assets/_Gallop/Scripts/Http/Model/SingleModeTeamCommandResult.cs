/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeTeamCommandResult
    {
        /// <summary>
        /// 特訓で獲得したスキル
        /// </summary>
        public SkillTips[] skill_tips_array;
        /// <summary>
        /// 魂爆発で獲得したスキル
        /// </summary>
        public TeamSkillTips[] soul_skill_tips_array;
        /// <summary>
        /// 極・魂爆発で獲得したスキル
        /// </summary>
        public TeamSkillTips[] sp_soul_skill_tips_array;
    }
}
