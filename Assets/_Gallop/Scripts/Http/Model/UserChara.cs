/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class UserChara
    {
        /// <summary>
        /// キャラクタID
        /// </summary>
        public int chara_id;
        /// <summary>
        /// 育成回数
        /// </summary>
        public int training_num;
        /// <summary>
        /// 親愛度
        /// </summary>
        public int love_point;
        /// <summary>
        /// 親愛度ポイントのプール値(値なしの場合はkeyなし)
        /// </summary>
        public int love_point_pool;
        /// <summary>
        /// ファン数
        /// </summary>
        public ulong fan;
    }
}
