/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ScenarioData
    {
        /// <summary>
        /// データタイプ
        /// </summary>
        public int type;
        /// <summary>
        /// データ値
        /// </summary>
        public int value;
    }
}
