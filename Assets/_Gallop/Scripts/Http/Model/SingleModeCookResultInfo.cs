/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeCookResultInfo
    {
        /// <summary>
        /// 目標タイプ
        /// </summary>
        public int cook_type;
        /// <summary>
        /// 結果(1：失敗、2：成功、3：大成功)
        /// </summary>
        public int result_state;
    }
}
