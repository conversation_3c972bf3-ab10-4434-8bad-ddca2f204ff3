/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class LiveNotUpParameterInfo
    {
        /// <summary>
        /// パフォーマンス(1:dance, 2:passion, 3:vocal, 4:visual, 5:mental)
        /// </summary>
        public int[] performance_type_array;
    }
}
