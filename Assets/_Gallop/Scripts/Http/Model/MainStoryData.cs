/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class MainStoryData
    {
        public int episode_id;
        /// <summary>
        /// クリアフラグ（0:未読,1:クリア済,2:未クリア）
        /// </summary>
        public int state;
    }
}
