/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ChampionsNewsTrainerInfo
    {
        /// <summary>
        /// ユーザーid
        /// </summary>
        public long viewer_id;
        /// <summary>
        /// 総勝利数
        /// </summary>
        public int total_win;
        /// <summary>
        /// 総レース数
        /// </summary>
        public int total_race_count;
        /// <summary>
        /// 直近5レース勝利数
        /// </summary>
        public int race_win_5;
        /// <summary>
        /// 月刊トゥインクル:トレーナーID
        /// </summary>
        public int[] news_trainer_id_array;
    }
}
