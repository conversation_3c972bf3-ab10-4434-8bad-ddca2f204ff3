/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeArcSelectionRivalInfo
    {
        /// <summary>
        /// キャラID
        /// </summary>
        public int chara_id;
        /// <summary>
        /// 勝敗予想印
        /// </summary>
        public int mark;
        /// <summary>
        /// 勝利時支持ポイント増減値
        /// </summary>
        public int win_approval_point;
        /// <summary>
        /// 敗北時支持ポイント増減値
        /// </summary>
        public int lose_approval_point;
        /// <summary>
        /// ライバル勝利時支持ポイント増減値
        /// </summary>
        public int rival_win_approval_point;
        /// <summary>
        /// ライバル敗北時支持ポイント増減値
        /// </summary>
        public int rival_lose_approval_point;
    }
}
