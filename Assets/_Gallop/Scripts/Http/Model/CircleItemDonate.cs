/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class CircleItemDonate
    {
        /// <summary>
        /// 寄付ID
        /// </summary>
        public int donate_id;
        /// <summary>
        /// アイテムリクエストID
        /// </summary>
        public int request_id;
        /// <summary>
        /// リクエストに寄付したユーザー
        /// </summary>
        public long viewer_id;
        /// <summary>
        /// 寄付した数
        /// </summary>
        public int item_num;
        /// <summary>
        /// 受取状況 0:未受取 1:受取済み 2:破棄
        /// </summary>
        public int state;
        /// <summary>
        /// 寄付日時
        /// </summary>
        public string create_time;
    }
}
