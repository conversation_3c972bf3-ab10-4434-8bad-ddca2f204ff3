/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeVenusEvaluationInfo
    {
        /// <summary>
        /// トレーニングパートナーID
        /// </summary>
        public int target_id;
        /// <summary>
        /// キャラID
        /// </summary>
        public int chara_id;
        /// <summary>
        /// メンバー状態(0:編成サポカ、1:特殊キャラ、2:野良ウマ娘)
        /// </summary>
        public int member_state;
    }
}
