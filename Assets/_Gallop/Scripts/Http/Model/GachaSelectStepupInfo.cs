/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class GachaSelectStepupInfo
    {
        /// <summary>
        /// ステップアップID
        /// </summary>
        public int stepup_id;
        /// <summary>
        /// 何周目か
        /// </summary>
        public int loop_count;
        /// <summary>
        /// 選択内容配列
        /// </summary>
        public GachaSelectStepupSelectedInfo[] selected_info_array;
        /// <summary>
        /// 前回の選択内容配列
        /// </summary>
        public GachaSelectStepupSelectedInfo[] before_selected_info_array;
    }
}
