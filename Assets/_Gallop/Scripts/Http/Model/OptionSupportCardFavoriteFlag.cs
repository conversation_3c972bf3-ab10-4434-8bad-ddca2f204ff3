/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class OptionSupportCardFavoriteFlag
    {
        /// <summary>
        /// レアリティ
        /// </summary>
        public int rarity;
        /// <summary>
        /// 保護状態 (0:常に保護 1:初回のみ 2:OFF)
        /// </summary>
        public int status;
    }
}
