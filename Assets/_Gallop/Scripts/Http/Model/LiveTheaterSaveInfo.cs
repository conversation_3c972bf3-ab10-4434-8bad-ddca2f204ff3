/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class LiveTheaterSaveInfo
    {
        /// <summary>
        /// 楽曲ID
        /// </summary>
        public int music_id;
        /// <summary>
        /// メンバー構成配列
        /// </summary>
        public LiveTheaterMemberInfo[] member_info_array;
        /// <summary>
        /// 寸劇をスキップするか(0：しない、1：する)
        /// </summary>
        public int is_skip_story;
    }
}
