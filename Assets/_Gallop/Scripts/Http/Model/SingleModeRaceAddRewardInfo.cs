/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeRaceAddRewardInfo
    {
        /// <summary>
        /// キャンペーンID
        /// </summary>
        public int campaign_id;
        /// <summary>
        /// プログラム
        /// </summary>
        public int program_id;
        /// <summary>
        /// 報酬獲得日(YYYYMMDD)
        /// </summary>
        public int reward_date;
        /// <summary>
        /// 報酬獲得回数
        /// </summary>
        public int reward_count;
    }
}
