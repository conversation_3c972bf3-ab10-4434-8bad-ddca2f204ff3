/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class HeroesFinalIndexInfo
    {
        /// <summary>
        /// メインステージのランキング(エクストラ出走者のみ 0:エクストラ未出走ユーザー)
        /// </summary>
        public int stage1_rank;
        /// <summary>
        /// エクストラステージ出走時ブロック(0:未出走ユーザー)
        /// </summary>
        public int race_group_id;
        /// <summary>
        /// エクストラステージ出走時レース番号(0:未出走ユーザー)
        /// </summary>
        public int race_num;
        /// <summary>
        /// 次の強制再生情報 なければnull
        /// </summary>
        public HeroesFinalNextPlayInfo next_play_info;
        /// <summary>
        /// メインステージ報酬として手に入れた称号データ ないときはnull
        /// </summary>
        public HonorData honor_data;
        /// <summary>
        /// エクストラステージ編成変更フラグ（0:編成未変更 1:編成変更済み）エクストラ未進出はnull
        /// </summary>
        public int team_edit_change_flag;
        /// <summary>
        /// トップ画面で表示するキャラクターの情報
        /// </summary>
        public HeroesFinalIndexCharaInfo[] index_chara_array;
        /// <summary>
        /// 初回ウマいね報酬を受け取り済みのグループID配列
        /// </summary>
        public int[] received_reward_group_array;
    }
}
