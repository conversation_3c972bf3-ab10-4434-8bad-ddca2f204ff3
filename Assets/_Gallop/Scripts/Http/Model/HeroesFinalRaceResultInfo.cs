/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class HeroesFinalRaceResultInfo
    {
        /// <summary>
        /// レース情報
        /// </summary>
        public HeroesFinalRaceInfo race_info;
        /// <summary>
        /// レースシナリオ
        /// </summary>
        public string race_scenario;
        /// <summary>
        /// ランダムシード
        /// </summary>
        public int random_seed;
    }
}
