/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ExtraCmData
    {
        /// <summary>
        /// CMムービー画面にNEWを表示するか（true:表示する）
        /// </summary>
        public bool is_extra_cm_new;
        /// <summary>
        /// cm保存殿堂入りウマ娘ID一覧
        /// </summary>
        public int[] cm_save_trained_chara_id_array;
    }
}
