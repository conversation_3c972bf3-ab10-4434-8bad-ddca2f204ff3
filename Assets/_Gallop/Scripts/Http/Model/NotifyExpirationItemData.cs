/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class NotifyExpirationItemData
    {
        /// <summary>
        /// 使用期限通知を出すアイテムID
        /// </summary>
        public int item_id;
        /// <summary>
        /// 通知を出す開始時刻(timestamp)
        /// </summary>
        public long start_date;
        /// <summary>
        /// 通知を出す終了時刻(timestamp)
        /// </summary>
        public long end_date;
    }
}
