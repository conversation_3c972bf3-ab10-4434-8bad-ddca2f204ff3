/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class RaceRewardLimitMoreData
    {
        /// <summary>
        /// アイテムマスタID(single_mode_race_limit_reward.csv:id)
        /// </summary>
        public int reward_id;
        /// <summary>
        /// 受け取り可能な残り回数
        /// </summary>
        public int rest_count;
    }
}
