/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class CollectRaidWatchableEpisode
    {
        /// <summary>
        /// collect_raid_story マスタの主キー
        /// </summary>
        public int episode_id;
        /// <summary>
        /// 0 => 未読、1 => 既読
        /// </summary>
        public int state;
    }
}
