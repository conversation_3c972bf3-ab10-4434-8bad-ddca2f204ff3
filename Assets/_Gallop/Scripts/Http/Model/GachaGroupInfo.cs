/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class GachaGroupInfo
    {
        /// <summary>
        /// グループガチャID
        /// </summary>
        public int group_id;
        /// <summary>
        /// グループガチャを引ける残り回数
        /// </summary>
        public int remain_draw_num;
    }
}
