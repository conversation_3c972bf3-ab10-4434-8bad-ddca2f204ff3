/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class CollectRaidLoginInfo
    {
        /// <summary>
        /// 収集イベントイベントID
        /// </summary>
        public int collect_raid_id;
        /// <summary>
        /// 初回遷移フラグ
        /// </summary>
        public int first_access_flag;
        /// <summary>
        /// 結果発表を初めて視聴するとき 1、そうでなければ 0
        /// </summary>
        public int is_first_time_watching_result;
        /// <summary>
        /// 個人報酬制覇フラグ
        /// </summary>
        public int complete_individual_reward_flag;
        /// <summary>
        /// 個人報酬未受け取りフラグ
        /// </summary>
        public int unreceived_individual_reward_flag;
        /// <summary>
        /// 追加収集アイテム情報
        /// </summary>
        public ResponseItem[] add_collect_item_reward_array;
        /// <summary>
        /// 閲覧可能なエピソードのIDと既読状況のマップからなる配列
        /// </summary>
        public CollectRaidWatchableEpisode[] watchable_episodes;
    }
}
