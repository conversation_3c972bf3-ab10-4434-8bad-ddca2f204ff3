/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ChallengeMatchLoadInfo
    {
        /// <summary>
        /// チャレンジマッチID
        /// </summary>
        public int challenge_match_id;
        /// <summary>
        /// マッチポイント
        /// </summary>
        public int match_point;
        /// <summary>
        /// レースの進行状態
        /// </summary>
        public int resume_state;
        /// <summary>
        /// 出走している殿堂入りウマ娘
        /// </summary>
        public int resume_trained_chara_id;
    }
}
