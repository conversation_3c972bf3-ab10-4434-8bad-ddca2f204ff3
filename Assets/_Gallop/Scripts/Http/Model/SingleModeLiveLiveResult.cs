/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SingleModeLiveLiveResult
    {
        /// <summary>
        /// ライブタイプ
        /// </summary>
        public int live_type;
        /// <summary>
        /// 結果(1:成功, 2:大成功)
        /// </summary>
        public int result_state;
    }
}
