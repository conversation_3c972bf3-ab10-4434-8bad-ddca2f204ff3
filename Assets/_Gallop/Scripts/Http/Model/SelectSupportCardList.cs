/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class SelectSupportCardList
    {
        /// <summary>
        /// 併せウマID
        /// </summary>
        public int partner_id;
        /// <summary>
        /// カードID
        /// </summary>
        public int card_id;
        /// <summary>
        /// レベル
        /// </summary>
        public int level;
        /// <summary>
        /// 限界突破数
        /// </summary>
        public int limit_break_count;
    }
}
