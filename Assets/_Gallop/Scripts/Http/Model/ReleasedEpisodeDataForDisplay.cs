/// This file was created from api.yml.
/// Don't edit this file manually!
using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;
using System.Text;
using Cute.Http;
using MessagePack;

namespace Gallop
{
    public class ReleasedEpisodeDataForDisplay
    {
        /// <summary>
        /// キャラストーリーエピソードID（chara_story_data.csv:id）
        /// </summary>
        public int id;
    }
}
