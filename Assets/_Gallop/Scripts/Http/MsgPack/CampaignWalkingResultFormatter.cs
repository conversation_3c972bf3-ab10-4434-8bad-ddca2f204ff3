#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class CampaignWalkingResultFormatter : global::MessagePack.Formatters.IMessagePackFormatter< CampaignWalkingResult >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public CampaignWalkingResultFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"chara_id", 0},
                {"love_point_info", 1},
                {"reward_list", 2},
                {"new_walking_act", 3},
                {"new_chara_profile_array", 4},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("chara_id"),
                global::System.Text.Encoding.UTF8.GetBytes("love_point_info"),
                global::System.Text.Encoding.UTF8.GetBytes("reward_list"),
                global::System.Text.Encoding.UTF8.GetBytes("new_walking_act"),
                global::System.Text.Encoding.UTF8.GetBytes("new_chara_profile_array"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, CampaignWalkingResult value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 5);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.chara_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<LovePointInfo>().Serialize(ref bytes, offset, value.love_point_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<DisplayRewardInfo[]>().Serialize(ref bytes, offset, value.reward_list, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<NoteDataForDisplay>().Serialize(ref bytes, offset, value.new_walking_act, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<CharaProfileData[]>().Serialize(ref bytes, offset, value.new_chara_profile_array, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.CampaignWalkingResult Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __chara_id__ = default(int);
            var __love_point_info__ = default(LovePointInfo);
            var __reward_list__ = default(DisplayRewardInfo[]);
            var __new_walking_act__ = default(NoteDataForDisplay);
            var __new_chara_profile_array__ = default(CharaProfileData[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __chara_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __love_point_info__ = formatterResolver.GetFormatterWithVerify<LovePointInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __reward_list__ = formatterResolver.GetFormatterWithVerify<DisplayRewardInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __new_walking_act__ = formatterResolver.GetFormatterWithVerify<NoteDataForDisplay>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __new_chara_profile_array__ = formatterResolver.GetFormatterWithVerify<CharaProfileData[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new CampaignWalkingResult();
            ____result.chara_id = __chara_id__;
            ____result.love_point_info = __love_point_info__;
            ____result.reward_list = __reward_list__;
            ____result.new_walking_act = __new_walking_act__;
            ____result.new_chara_profile_array = __new_chara_profile_array__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
