#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeTeamTeamRaceEndResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeTeamTeamRaceEndResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeTeamTeamRaceEndResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"chara_info", 0},
                {"team_data_set", 1},
                {"add_music", 2},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("chara_info"),
                global::System.Text.Encoding.UTF8.GetBytes("team_data_set"),
                global::System.Text.Encoding.UTF8.GetBytes("add_music"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeTeamTeamRaceEndResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 3);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeChara>().Serialize(ref bytes, offset, value.chara_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeTeamDataSet>().Serialize(ref bytes, offset, value.team_data_set, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<UserMusic>().Serialize(ref bytes, offset, value.add_music, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeTeamTeamRaceEndResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __chara_info__ = default(SingleModeChara);
            var __team_data_set__ = default(SingleModeTeamDataSet);
            var __add_music__ = default(UserMusic);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __chara_info__ = formatterResolver.GetFormatterWithVerify<SingleModeChara>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __team_data_set__ = formatterResolver.GetFormatterWithVerify<SingleModeTeamDataSet>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __add_music__ = formatterResolver.GetFormatterWithVerify<UserMusic>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeTeamTeamRaceEndResponse.CommonResponse();
            ____result.chara_info = __chara_info__;
            ____result.team_data_set = __team_data_set__;
            ____result.add_music = __add_music__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
