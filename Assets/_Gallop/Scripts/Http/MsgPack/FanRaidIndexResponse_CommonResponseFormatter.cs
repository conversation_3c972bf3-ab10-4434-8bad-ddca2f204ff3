#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class FanRaidIndexResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< FanRaidIndexResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public FanRaidIndexResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"fan_raid_fans_info", 0},
                {"checked_fan_raid_all_reward_id_array", 1},
                {"fan_raid_polling_interval", 2},
                {"is_cleared_fan_raid_condition", 3},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("fan_raid_fans_info"),
                global::System.Text.Encoding.UTF8.GetBytes("checked_fan_raid_all_reward_id_array"),
                global::System.Text.Encoding.UTF8.GetBytes("fan_raid_polling_interval"),
                global::System.Text.Encoding.UTF8.GetBytes("is_cleared_fan_raid_condition"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, FanRaidIndexResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 4);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<FanRaidFansInfo>().Serialize(ref bytes, offset, value.fan_raid_fans_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<int[]>().Serialize(ref bytes, offset, value.checked_fan_raid_all_reward_id_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.fan_raid_polling_interval);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.is_cleared_fan_raid_condition);
            return offset - startOffset;
        }

        public global::Gallop.FanRaidIndexResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __fan_raid_fans_info__ = default(FanRaidFansInfo);
            var __checked_fan_raid_all_reward_id_array__ = default(int[]);
            var __fan_raid_polling_interval__ = default(int);
            var __is_cleared_fan_raid_condition__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __fan_raid_fans_info__ = formatterResolver.GetFormatterWithVerify<FanRaidFansInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __checked_fan_raid_all_reward_id_array__ = formatterResolver.GetFormatterWithVerify<int[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __fan_raid_polling_interval__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __is_cleared_fan_raid_condition__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new FanRaidIndexResponse.CommonResponse();
            ____result.fan_raid_fans_info = __fan_raid_fans_info__;
            ____result.checked_fan_raid_all_reward_id_array = __checked_fan_raid_all_reward_id_array__;
            ____result.fan_raid_polling_interval = __fan_raid_polling_interval__;
            ____result.is_cleared_fan_raid_condition = __is_cleared_fan_raid_condition__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
