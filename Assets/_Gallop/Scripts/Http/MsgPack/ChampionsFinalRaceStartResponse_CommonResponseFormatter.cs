#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class ChampionsFinalRaceStartResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< ChampionsFinalRaceStartResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public ChampionsFinalRaceStartResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"room_info", 0},
                {"room_user_array", 1},
                {"race_horse_data_array", 2},
                {"trained_chara_array", 3},
                {"state", 4},
                {"played_live_flag", 5},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("room_info"),
                global::System.Text.Encoding.UTF8.GetBytes("room_user_array"),
                global::System.Text.Encoding.UTF8.GetBytes("race_horse_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("trained_chara_array"),
                global::System.Text.Encoding.UTF8.GetBytes("state"),
                global::System.Text.Encoding.UTF8.GetBytes("played_live_flag"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, ChampionsFinalRaceStartResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 6);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<ChampionsRoomInfo>().Serialize(ref bytes, offset, value.room_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<ChampionsRoomUser[]>().Serialize(ref bytes, offset, value.room_user_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<RaceHorseData[]>().Serialize(ref bytes, offset, value.race_horse_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Serialize(ref bytes, offset, value.trained_chara_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.state);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.played_live_flag);
            return offset - startOffset;
        }

        public global::Gallop.ChampionsFinalRaceStartResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __room_info__ = default(ChampionsRoomInfo);
            var __room_user_array__ = default(ChampionsRoomUser[]);
            var __race_horse_data_array__ = default(RaceHorseData[]);
            var __trained_chara_array__ = default(TrainedChara[]);
            var __state__ = default(int);
            var __played_live_flag__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __room_info__ = formatterResolver.GetFormatterWithVerify<ChampionsRoomInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __room_user_array__ = formatterResolver.GetFormatterWithVerify<ChampionsRoomUser[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __race_horse_data_array__ = formatterResolver.GetFormatterWithVerify<RaceHorseData[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __trained_chara_array__ = formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __state__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __played_live_flag__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new ChampionsFinalRaceStartResponse.CommonResponse();
            ____result.room_info = __room_info__;
            ____result.room_user_array = __room_user_array__;
            ____result.race_horse_data_array = __race_horse_data_array__;
            ____result.trained_chara_array = __trained_chara_array__;
            ____result.state = __state__;
            ____result.played_live_flag = __played_live_flag__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
