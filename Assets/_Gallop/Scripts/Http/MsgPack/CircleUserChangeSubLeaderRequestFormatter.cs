#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class CircleUserChangeSubLeaderRequestFormatter : global::MessagePack.Formatters.IMessagePackFormatter< CircleUserChangeSubLeaderRequest >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public CircleUserChangeSubLeaderRequestFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"target_viewer_id", 0},
                {"is_set", 1},
                {"viewer_id", 2},
                {"device", 3},
                {"device_id", 4},
                {"device_name", 5},
                {"graphics_device_name", 6},
                {"ip_address", 7},
                {"platform_os_version", 8},
                {"carrier", 9},
                {"keychain", 10},
                {"locale", 11},
                {"button_info", 12},
                {"dmm_viewer_id", 13},
                {"dmm_onetime_token", 14},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("target_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("is_set"),
                global::System.Text.Encoding.UTF8.GetBytes("viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device"),
                global::System.Text.Encoding.UTF8.GetBytes("device_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("graphics_device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("ip_address"),
                global::System.Text.Encoding.UTF8.GetBytes("platform_os_version"),
                global::System.Text.Encoding.UTF8.GetBytes("carrier"),
                global::System.Text.Encoding.UTF8.GetBytes("keychain"),
                global::System.Text.Encoding.UTF8.GetBytes("locale"),
                global::System.Text.Encoding.UTF8.GetBytes("button_info"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_onetime_token"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, CircleUserChangeSubLeaderRequest value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 15);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.target_viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteBoolean(ref bytes, offset, value.is_set);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.device);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.graphics_device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.ip_address, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.platform_os_version, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.carrier, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.keychain);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.locale, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[12]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.button_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[13]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_viewer_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[14]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_onetime_token, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.CircleUserChangeSubLeaderRequest Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __target_viewer_id__ = default(long);
            var __is_set__ = default(bool);
            var __viewer_id__ = default(long);
            var __device__ = default(int);
            var __device_id__ = default(string);
            var __device_name__ = default(string);
            var __graphics_device_name__ = default(string);
            var __ip_address__ = default(string);
            var __platform_os_version__ = default(string);
            var __carrier__ = default(string);
            var __keychain__ = default(long);
            var __locale__ = default(string);
            var __button_info__ = default(string);
            var __dmm_viewer_id__ = default(string);
            var __dmm_onetime_token__ = default(string);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __target_viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 1:
                        if (bytes[offset] == 0x00 || bytes[offset] == 0x01)
                        {
                            var temp = default(int);
                            temp = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                            __is_set__ = temp == 0 ? false : true;
                        }
                        else
                        {
                            __is_set__ = global::MessagePack.MessagePackBinary.ReadBoolean(bytes, offset, out readSize);
                        }
                        break;
                    case 2:
                        __viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 3:
                        __device__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __device_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __graphics_device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 7:
                        __ip_address__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __platform_os_version__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 9:
                        __carrier__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 10:
                        __keychain__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 11:
                        __locale__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 12:
                        __button_info__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 13:
                        __dmm_viewer_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 14:
                        __dmm_onetime_token__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new CircleUserChangeSubLeaderRequest();
            ____result.target_viewer_id = __target_viewer_id__;
            ____result.is_set = __is_set__;
            ____result.viewer_id = __viewer_id__;
            ____result.device = __device__;
            ____result.device_id = __device_id__;
            ____result.device_name = __device_name__;
            ____result.graphics_device_name = __graphics_device_name__;
            ____result.ip_address = __ip_address__;
            ____result.platform_os_version = __platform_os_version__;
            ____result.carrier = __carrier__;
            ____result.keychain = __keychain__;
            ____result.locale = __locale__;
            ____result.button_info = __button_info__;
            ____result.dmm_viewer_id = __dmm_viewer_id__;
            ____result.dmm_onetime_token = __dmm_onetime_token__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
