#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class PaymentFinishResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< PaymentFinishResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public PaymentFinishResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"first_time", 0},
                {"purchased_times_data", 1},
                {"purchase_id", 2},
                {"before_paid_coin", 3},
                {"before_free_coin", 4},
                {"after_paid_coin", 5},
                {"after_free_coin", 6},
                {"season_pack_info", 7},
                {"training_report_info", 8},
                {"factor_premium_pass_info", 9},
                {"factor_order_pass_info", 10},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("first_time"),
                global::System.Text.Encoding.UTF8.GetBytes("purchased_times_data"),
                global::System.Text.Encoding.UTF8.GetBytes("purchase_id"),
                global::System.Text.Encoding.UTF8.GetBytes("before_paid_coin"),
                global::System.Text.Encoding.UTF8.GetBytes("before_free_coin"),
                global::System.Text.Encoding.UTF8.GetBytes("after_paid_coin"),
                global::System.Text.Encoding.UTF8.GetBytes("after_free_coin"),
                global::System.Text.Encoding.UTF8.GetBytes("season_pack_info"),
                global::System.Text.Encoding.UTF8.GetBytes("training_report_info"),
                global::System.Text.Encoding.UTF8.GetBytes("factor_premium_pass_info"),
                global::System.Text.Encoding.UTF8.GetBytes("factor_order_pass_info"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, PaymentFinishResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 11);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteBoolean(ref bytes, offset, value.first_time);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<PaymentPurchaseTimesData>().Serialize(ref bytes, offset, value.purchased_times_data, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.purchase_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.before_paid_coin);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.before_free_coin);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.after_paid_coin);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.after_free_coin);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<PaymentSeasonPackInfo>().Serialize(ref bytes, offset, value.season_pack_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<TrainingReportInfo>().Serialize(ref bytes, offset, value.training_report_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += formatterResolver.GetFormatterWithVerify<FactorPremiumPassInfo>().Serialize(ref bytes, offset, value.factor_premium_pass_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += formatterResolver.GetFormatterWithVerify<FactorOrderPassInfo>().Serialize(ref bytes, offset, value.factor_order_pass_info, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.PaymentFinishResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __first_time__ = default(bool);
            var __purchased_times_data__ = default(PaymentPurchaseTimesData);
            var __purchase_id__ = default(string);
            var __before_paid_coin__ = default(int);
            var __before_free_coin__ = default(int);
            var __after_paid_coin__ = default(int);
            var __after_free_coin__ = default(int);
            var __season_pack_info__ = default(PaymentSeasonPackInfo);
            var __training_report_info__ = default(TrainingReportInfo);
            var __factor_premium_pass_info__ = default(FactorPremiumPassInfo);
            var __factor_order_pass_info__ = default(FactorOrderPassInfo);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        if (bytes[offset] == 0x00 || bytes[offset] == 0x01)
                        {
                            var temp = default(int);
                            temp = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                            __first_time__ = temp == 0 ? false : true;
                        }
                        else
                        {
                            __first_time__ = global::MessagePack.MessagePackBinary.ReadBoolean(bytes, offset, out readSize);
                        }
                        break;
                    case 1:
                        __purchased_times_data__ = formatterResolver.GetFormatterWithVerify<PaymentPurchaseTimesData>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __purchase_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __before_paid_coin__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __before_free_coin__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __after_paid_coin__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 6:
                        __after_free_coin__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 7:
                        __season_pack_info__ = formatterResolver.GetFormatterWithVerify<PaymentSeasonPackInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __training_report_info__ = formatterResolver.GetFormatterWithVerify<TrainingReportInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 9:
                        __factor_premium_pass_info__ = formatterResolver.GetFormatterWithVerify<FactorPremiumPassInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 10:
                        __factor_order_pass_info__ = formatterResolver.GetFormatterWithVerify<FactorOrderPassInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new PaymentFinishResponse.CommonResponse();
            ____result.first_time = __first_time__;
            ____result.purchased_times_data = __purchased_times_data__;
            ____result.purchase_id = __purchase_id__;
            ____result.before_paid_coin = __before_paid_coin__;
            ____result.before_free_coin = __before_free_coin__;
            ____result.after_paid_coin = __after_paid_coin__;
            ____result.after_free_coin = __after_free_coin__;
            ____result.season_pack_info = __season_pack_info__;
            ____result.training_report_info = __training_report_info__;
            ____result.factor_premium_pass_info = __factor_premium_pass_info__;
            ____result.factor_order_pass_info = __factor_order_pass_info__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
