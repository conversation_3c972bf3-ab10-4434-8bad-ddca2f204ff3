#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeArcExecCommandRequestFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeArcExecCommandRequest >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeArcExecCommandRequestFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"command_type", 0},
                {"command_id", 1},
                {"command_group_id", 2},
                {"select_id", 3},
                {"current_turn", 4},
                {"current_vital", 5},
                {"exec_auto_play_plan_id", 6},
                {"viewer_id", 7},
                {"device", 8},
                {"device_id", 9},
                {"device_name", 10},
                {"graphics_device_name", 11},
                {"ip_address", 12},
                {"platform_os_version", 13},
                {"carrier", 14},
                {"keychain", 15},
                {"locale", 16},
                {"button_info", 17},
                {"dmm_viewer_id", 18},
                {"dmm_onetime_token", 19},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("command_type"),
                global::System.Text.Encoding.UTF8.GetBytes("command_id"),
                global::System.Text.Encoding.UTF8.GetBytes("command_group_id"),
                global::System.Text.Encoding.UTF8.GetBytes("select_id"),
                global::System.Text.Encoding.UTF8.GetBytes("current_turn"),
                global::System.Text.Encoding.UTF8.GetBytes("current_vital"),
                global::System.Text.Encoding.UTF8.GetBytes("exec_auto_play_plan_id"),
                global::System.Text.Encoding.UTF8.GetBytes("viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device"),
                global::System.Text.Encoding.UTF8.GetBytes("device_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("graphics_device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("ip_address"),
                global::System.Text.Encoding.UTF8.GetBytes("platform_os_version"),
                global::System.Text.Encoding.UTF8.GetBytes("carrier"),
                global::System.Text.Encoding.UTF8.GetBytes("keychain"),
                global::System.Text.Encoding.UTF8.GetBytes("locale"),
                global::System.Text.Encoding.UTF8.GetBytes("button_info"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_onetime_token"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeArcExecCommandRequest value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteMapHeader(ref bytes, offset, 20);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.command_type);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.command_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.command_group_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.select_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.current_turn);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.current_vital);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.exec_auto_play_plan_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.device);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.graphics_device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[12]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.ip_address, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[13]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.platform_os_version, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[14]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.carrier, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[15]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.keychain);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[16]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.locale, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[17]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.button_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[18]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_viewer_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[19]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_onetime_token, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeArcExecCommandRequest Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __command_type__ = default(int);
            var __command_id__ = default(int);
            var __command_group_id__ = default(int);
            var __select_id__ = default(int);
            var __current_turn__ = default(int);
            var __current_vital__ = default(int);
            var __exec_auto_play_plan_id__ = default(int);
            var __viewer_id__ = default(long);
            var __device__ = default(int);
            var __device_id__ = default(string);
            var __device_name__ = default(string);
            var __graphics_device_name__ = default(string);
            var __ip_address__ = default(string);
            var __platform_os_version__ = default(string);
            var __carrier__ = default(string);
            var __keychain__ = default(long);
            var __locale__ = default(string);
            var __button_info__ = default(string);
            var __dmm_viewer_id__ = default(string);
            var __dmm_onetime_token__ = default(string);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __command_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __command_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __command_group_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __select_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __current_turn__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __current_vital__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 6:
                        __exec_auto_play_plan_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 7:
                        __viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 8:
                        __device__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 9:
                        __device_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 10:
                        __device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 11:
                        __graphics_device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 12:
                        __ip_address__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 13:
                        __platform_os_version__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 14:
                        __carrier__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 15:
                        __keychain__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 16:
                        __locale__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 17:
                        __button_info__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 18:
                        __dmm_viewer_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 19:
                        __dmm_onetime_token__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeArcExecCommandRequest();
            ____result.command_type = __command_type__;
            ____result.command_id = __command_id__;
            ____result.command_group_id = __command_group_id__;
            ____result.select_id = __select_id__;
            ____result.current_turn = __current_turn__;
            ____result.current_vital = __current_vital__;
            ____result.exec_auto_play_plan_id = __exec_auto_play_plan_id__;
            ____result.viewer_id = __viewer_id__;
            ____result.device = __device__;
            ____result.device_id = __device_id__;
            ____result.device_name = __device_name__;
            ____result.graphics_device_name = __graphics_device_name__;
            ____result.ip_address = __ip_address__;
            ____result.platform_os_version = __platform_os_version__;
            ____result.carrier = __carrier__;
            ____result.keychain = __keychain__;
            ____result.locale = __locale__;
            ____result.button_info = __button_info__;
            ____result.dmm_viewer_id = __dmm_viewer_id__;
            ____result.dmm_onetime_token = __dmm_onetime_token__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
