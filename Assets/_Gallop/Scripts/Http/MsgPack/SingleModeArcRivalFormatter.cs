#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeArcRivalFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeArcRival >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeArcRivalFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"chara_id", 0},
                {"speed", 1},
                {"stamina", 2},
                {"power", 3},
                {"guts", 4},
                {"wiz", 5},
                {"command_id", 6},
                {"rival_boost", 7},
                {"star_lv", 8},
                {"rank", 9},
                {"approval_point", 10},
                {"potential_array", 11},
                {"selection_peff_array", 12},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("chara_id"),
                global::System.Text.Encoding.UTF8.GetBytes("speed"),
                global::System.Text.Encoding.UTF8.GetBytes("stamina"),
                global::System.Text.Encoding.UTF8.GetBytes("power"),
                global::System.Text.Encoding.UTF8.GetBytes("guts"),
                global::System.Text.Encoding.UTF8.GetBytes("wiz"),
                global::System.Text.Encoding.UTF8.GetBytes("command_id"),
                global::System.Text.Encoding.UTF8.GetBytes("rival_boost"),
                global::System.Text.Encoding.UTF8.GetBytes("star_lv"),
                global::System.Text.Encoding.UTF8.GetBytes("rank"),
                global::System.Text.Encoding.UTF8.GetBytes("approval_point"),
                global::System.Text.Encoding.UTF8.GetBytes("potential_array"),
                global::System.Text.Encoding.UTF8.GetBytes("selection_peff_array"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeArcRival value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 13);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.chara_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.speed);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.stamina);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.power);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.guts);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.wiz);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.command_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rival_boost);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.star_lv);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.approval_point);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += formatterResolver.GetFormatterWithVerify<ArcRivalPotential[]>().Serialize(ref bytes, offset, value.potential_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[12]);
            offset += formatterResolver.GetFormatterWithVerify<ArcSelectionPeff[]>().Serialize(ref bytes, offset, value.selection_peff_array, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeArcRival Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __chara_id__ = default(int);
            var __speed__ = default(int);
            var __stamina__ = default(int);
            var __power__ = default(int);
            var __guts__ = default(int);
            var __wiz__ = default(int);
            var __command_id__ = default(int);
            var __rival_boost__ = default(int);
            var __star_lv__ = default(int);
            var __rank__ = default(int);
            var __approval_point__ = default(int);
            var __potential_array__ = default(ArcRivalPotential[]);
            var __selection_peff_array__ = default(ArcSelectionPeff[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __chara_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __speed__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __stamina__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __power__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __guts__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __wiz__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 6:
                        __command_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 7:
                        __rival_boost__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 8:
                        __star_lv__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 9:
                        __rank__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 10:
                        __approval_point__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 11:
                        __potential_array__ = formatterResolver.GetFormatterWithVerify<ArcRivalPotential[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 12:
                        __selection_peff_array__ = formatterResolver.GetFormatterWithVerify<ArcSelectionPeff[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeArcRival();
            ____result.chara_id = __chara_id__;
            ____result.speed = __speed__;
            ____result.stamina = __stamina__;
            ____result.power = __power__;
            ____result.guts = __guts__;
            ____result.wiz = __wiz__;
            ____result.command_id = __command_id__;
            ____result.rival_boost = __rival_boost__;
            ____result.star_lv = __star_lv__;
            ____result.rank = __rank__;
            ____result.approval_point = __approval_point__;
            ____result.potential_array = __potential_array__;
            ____result.selection_peff_array = __selection_peff_array__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
