#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeArcSelectionInfoFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeArcSelectionInfo >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeArcSelectionInfoFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"all_win_approval_point", 0},
                {"params_inc_dec_info_array", 1},
                {"selection_rival_info_array", 2},
                {"is_special_match", 3},
                {"bonus_params_inc_dec_info_array", 4},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("all_win_approval_point"),
                global::System.Text.Encoding.UTF8.GetBytes("params_inc_dec_info_array"),
                global::System.Text.Encoding.UTF8.GetBytes("selection_rival_info_array"),
                global::System.Text.Encoding.UTF8.GetBytes("is_special_match"),
                global::System.Text.Encoding.UTF8.GetBytes("bonus_params_inc_dec_info_array"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeArcSelectionInfo value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 5);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.all_win_approval_point);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeParamsIncDecInfo[]>().Serialize(ref bytes, offset, value.params_inc_dec_info_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeArcSelectionRivalInfo[]>().Serialize(ref bytes, offset, value.selection_rival_info_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.is_special_match);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeParamsIncDecInfo[]>().Serialize(ref bytes, offset, value.bonus_params_inc_dec_info_array, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeArcSelectionInfo Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __all_win_approval_point__ = default(int);
            var __params_inc_dec_info_array__ = default(SingleModeParamsIncDecInfo[]);
            var __selection_rival_info_array__ = default(SingleModeArcSelectionRivalInfo[]);
            var __is_special_match__ = default(int);
            var __bonus_params_inc_dec_info_array__ = default(SingleModeParamsIncDecInfo[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __all_win_approval_point__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __params_inc_dec_info_array__ = formatterResolver.GetFormatterWithVerify<SingleModeParamsIncDecInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __selection_rival_info_array__ = formatterResolver.GetFormatterWithVerify<SingleModeArcSelectionRivalInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __is_special_match__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __bonus_params_inc_dec_info_array__ = formatterResolver.GetFormatterWithVerify<SingleModeParamsIncDecInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeArcSelectionInfo();
            ____result.all_win_approval_point = __all_win_approval_point__;
            ____result.params_inc_dec_info_array = __params_inc_dec_info_array__;
            ____result.selection_rival_info_array = __selection_rival_info_array__;
            ____result.is_special_match = __is_special_match__;
            ____result.bonus_params_inc_dec_info_array = __bonus_params_inc_dec_info_array__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
