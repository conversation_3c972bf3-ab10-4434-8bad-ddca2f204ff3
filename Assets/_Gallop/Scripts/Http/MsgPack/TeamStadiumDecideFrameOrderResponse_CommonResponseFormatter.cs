#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class TeamStadiumDecideFrameOrderResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< TeamStadiumDecideFrameOrderResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public TeamStadiumDecideFrameOrderResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"frame_order_info_array", 0},
                {"user_team_data_array_copy", 1},
                {"user_trained_chara_array_copy", 2},
                {"opponent_info_copy", 3},
                {"opponent_chara_info_array_latest_copy", 4},
                {"winning_reward_guarantee_status", 5},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("frame_order_info_array"),
                global::System.Text.Encoding.UTF8.GetBytes("user_team_data_array_copy"),
                global::System.Text.Encoding.UTF8.GetBytes("user_trained_chara_array_copy"),
                global::System.Text.Encoding.UTF8.GetBytes("opponent_info_copy"),
                global::System.Text.Encoding.UTF8.GetBytes("opponent_chara_info_array_latest_copy"),
                global::System.Text.Encoding.UTF8.GetBytes("winning_reward_guarantee_status"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, TeamStadiumDecideFrameOrderResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 6);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<TeamStadiumFrameOrder[]>().Serialize(ref bytes, offset, value.frame_order_info_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<TeamStadiumTeamData[]>().Serialize(ref bytes, offset, value.user_team_data_array_copy, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Serialize(ref bytes, offset, value.user_trained_chara_array_copy, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<TeamStadiumOpponent>().Serialize(ref bytes, offset, value.opponent_info_copy, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Serialize(ref bytes, offset, value.opponent_chara_info_array_latest_copy, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.winning_reward_guarantee_status);
            return offset - startOffset;
        }

        public global::Gallop.TeamStadiumDecideFrameOrderResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __frame_order_info_array__ = default(TeamStadiumFrameOrder[]);
            var __user_team_data_array_copy__ = default(TeamStadiumTeamData[]);
            var __user_trained_chara_array_copy__ = default(TrainedChara[]);
            var __opponent_info_copy__ = default(TeamStadiumOpponent);
            var __opponent_chara_info_array_latest_copy__ = default(TrainedChara[]);
            var __winning_reward_guarantee_status__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __frame_order_info_array__ = formatterResolver.GetFormatterWithVerify<TeamStadiumFrameOrder[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __user_team_data_array_copy__ = formatterResolver.GetFormatterWithVerify<TeamStadiumTeamData[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __user_trained_chara_array_copy__ = formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __opponent_info_copy__ = formatterResolver.GetFormatterWithVerify<TeamStadiumOpponent>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __opponent_chara_info_array_latest_copy__ = formatterResolver.GetFormatterWithVerify<TrainedChara[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __winning_reward_guarantee_status__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new TeamStadiumDecideFrameOrderResponse.CommonResponse();
            ____result.frame_order_info_array = __frame_order_info_array__;
            ____result.user_team_data_array_copy = __user_team_data_array_copy__;
            ____result.user_trained_chara_array_copy = __user_trained_chara_array_copy__;
            ____result.opponent_info_copy = __opponent_info_copy__;
            ____result.opponent_chara_info_array_latest_copy = __opponent_chara_info_array_latest_copy__;
            ____result.winning_reward_guarantee_status = __winning_reward_guarantee_status__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
