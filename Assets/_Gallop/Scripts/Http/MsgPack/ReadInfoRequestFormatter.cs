#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class ReadInfoRequestFormatter : global::MessagePack.Formatters.IMessagePackFormatter< ReadInfoRequest >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public ReadInfoRequestFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"add_home_story_data_array", 0},
                {"add_short_episode_data_array", 1},
                {"add_home_poster_data_array", 2},
                {"add_tutorial_guide_data_array", 3},
                {"add_released_episode_data_array", 4},
                {"add_home_banner_data_array", 5},
                {"add_campaign_walking_data", 6},
                {"add_viewed_story_array", 7},
                {"product_open_data_array", 8},
                {"viewer_id", 9},
                {"device", 10},
                {"device_id", 11},
                {"device_name", 12},
                {"graphics_device_name", 13},
                {"ip_address", 14},
                {"platform_os_version", 15},
                {"carrier", 16},
                {"keychain", 17},
                {"locale", 18},
                {"button_info", 19},
                {"dmm_viewer_id", 20},
                {"dmm_onetime_token", 21},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("add_home_story_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_short_episode_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_home_poster_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_tutorial_guide_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_released_episode_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_home_banner_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_campaign_walking_data"),
                global::System.Text.Encoding.UTF8.GetBytes("add_viewed_story_array"),
                global::System.Text.Encoding.UTF8.GetBytes("product_open_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device"),
                global::System.Text.Encoding.UTF8.GetBytes("device_id"),
                global::System.Text.Encoding.UTF8.GetBytes("device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("graphics_device_name"),
                global::System.Text.Encoding.UTF8.GetBytes("ip_address"),
                global::System.Text.Encoding.UTF8.GetBytes("platform_os_version"),
                global::System.Text.Encoding.UTF8.GetBytes("carrier"),
                global::System.Text.Encoding.UTF8.GetBytes("keychain"),
                global::System.Text.Encoding.UTF8.GetBytes("locale"),
                global::System.Text.Encoding.UTF8.GetBytes("button_info"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("dmm_onetime_token"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, ReadInfoRequest value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteMapHeader(ref bytes, offset, 22);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<HomeStoryDataForRegist[]>().Serialize(ref bytes, offset, value.add_home_story_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<ShortEpisodeDataForRegist[]>().Serialize(ref bytes, offset, value.add_short_episode_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<HomePosterDataForRegist[]>().Serialize(ref bytes, offset, value.add_home_poster_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<TutorialGuideDataForRegist[]>().Serialize(ref bytes, offset, value.add_tutorial_guide_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<ReleasedEpisodeDataForRegist[]>().Serialize(ref bytes, offset, value.add_released_episode_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<HomeBannerDataForRegist[]>().Serialize(ref bytes, offset, value.add_home_banner_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += formatterResolver.GetFormatterWithVerify<CampaignWalkingDataForRegist>().Serialize(ref bytes, offset, value.add_campaign_walking_data, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<LogStoryView[]>().Serialize(ref bytes, offset, value.add_viewed_story_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<ProductOpenDataForRegist[]>().Serialize(ref bytes, offset, value.product_open_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.device);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[12]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[13]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.graphics_device_name, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[14]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.ip_address, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[15]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.platform_os_version, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[16]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.carrier, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[17]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.keychain);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[18]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.locale, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[19]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.button_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[20]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_viewer_id, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[21]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.dmm_onetime_token, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.ReadInfoRequest Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __add_home_story_data_array__ = default(HomeStoryDataForRegist[]);
            var __add_short_episode_data_array__ = default(ShortEpisodeDataForRegist[]);
            var __add_home_poster_data_array__ = default(HomePosterDataForRegist[]);
            var __add_tutorial_guide_data_array__ = default(TutorialGuideDataForRegist[]);
            var __add_released_episode_data_array__ = default(ReleasedEpisodeDataForRegist[]);
            var __add_home_banner_data_array__ = default(HomeBannerDataForRegist[]);
            var __add_campaign_walking_data__ = default(CampaignWalkingDataForRegist);
            var __add_viewed_story_array__ = default(LogStoryView[]);
            var __product_open_data_array__ = default(ProductOpenDataForRegist[]);
            var __viewer_id__ = default(long);
            var __device__ = default(int);
            var __device_id__ = default(string);
            var __device_name__ = default(string);
            var __graphics_device_name__ = default(string);
            var __ip_address__ = default(string);
            var __platform_os_version__ = default(string);
            var __carrier__ = default(string);
            var __keychain__ = default(long);
            var __locale__ = default(string);
            var __button_info__ = default(string);
            var __dmm_viewer_id__ = default(string);
            var __dmm_onetime_token__ = default(string);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __add_home_story_data_array__ = formatterResolver.GetFormatterWithVerify<HomeStoryDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __add_short_episode_data_array__ = formatterResolver.GetFormatterWithVerify<ShortEpisodeDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __add_home_poster_data_array__ = formatterResolver.GetFormatterWithVerify<HomePosterDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __add_tutorial_guide_data_array__ = formatterResolver.GetFormatterWithVerify<TutorialGuideDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __add_released_episode_data_array__ = formatterResolver.GetFormatterWithVerify<ReleasedEpisodeDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __add_home_banner_data_array__ = formatterResolver.GetFormatterWithVerify<HomeBannerDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __add_campaign_walking_data__ = formatterResolver.GetFormatterWithVerify<CampaignWalkingDataForRegist>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 7:
                        __add_viewed_story_array__ = formatterResolver.GetFormatterWithVerify<LogStoryView[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __product_open_data_array__ = formatterResolver.GetFormatterWithVerify<ProductOpenDataForRegist[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 9:
                        __viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 10:
                        __device__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 11:
                        __device_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 12:
                        __device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 13:
                        __graphics_device_name__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 14:
                        __ip_address__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 15:
                        __platform_os_version__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 16:
                        __carrier__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 17:
                        __keychain__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 18:
                        __locale__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 19:
                        __button_info__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 20:
                        __dmm_viewer_id__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 21:
                        __dmm_onetime_token__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new ReadInfoRequest();
            ____result.add_home_story_data_array = __add_home_story_data_array__;
            ____result.add_short_episode_data_array = __add_short_episode_data_array__;
            ____result.add_home_poster_data_array = __add_home_poster_data_array__;
            ____result.add_tutorial_guide_data_array = __add_tutorial_guide_data_array__;
            ____result.add_released_episode_data_array = __add_released_episode_data_array__;
            ____result.add_home_banner_data_array = __add_home_banner_data_array__;
            ____result.add_campaign_walking_data = __add_campaign_walking_data__;
            ____result.add_viewed_story_array = __add_viewed_story_array__;
            ____result.product_open_data_array = __product_open_data_array__;
            ____result.viewer_id = __viewer_id__;
            ____result.device = __device__;
            ____result.device_id = __device_id__;
            ____result.device_name = __device_name__;
            ____result.graphics_device_name = __graphics_device_name__;
            ____result.ip_address = __ip_address__;
            ____result.platform_os_version = __platform_os_version__;
            ____result.carrier = __carrier__;
            ____result.keychain = __keychain__;
            ____result.locale = __locale__;
            ____result.button_info = __button_info__;
            ____result.dmm_viewer_id = __dmm_viewer_id__;
            ____result.dmm_onetime_token = __dmm_onetime_token__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
