#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeTeamRaceResultFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeTeamRaceResult >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeTeamRaceResultFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"distance_type", 0},
                {"race_instance_id", 1},
                {"season", 2},
                {"weather", 3},
                {"ground_condition", 4},
                {"random_seed", 5},
                {"race_horse_data_array", 6},
                {"race_scenario", 7},
                {"round", 8},
                {"win_type", 9},
                {"chara_result_array", 10},
                {"continue_num", 11},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("distance_type"),
                global::System.Text.Encoding.UTF8.GetBytes("race_instance_id"),
                global::System.Text.Encoding.UTF8.GetBytes("season"),
                global::System.Text.Encoding.UTF8.GetBytes("weather"),
                global::System.Text.Encoding.UTF8.GetBytes("ground_condition"),
                global::System.Text.Encoding.UTF8.GetBytes("random_seed"),
                global::System.Text.Encoding.UTF8.GetBytes("race_horse_data_array"),
                global::System.Text.Encoding.UTF8.GetBytes("race_scenario"),
                global::System.Text.Encoding.UTF8.GetBytes("round"),
                global::System.Text.Encoding.UTF8.GetBytes("win_type"),
                global::System.Text.Encoding.UTF8.GetBytes("chara_result_array"),
                global::System.Text.Encoding.UTF8.GetBytes("continue_num"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeTeamRaceResult value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 12);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.distance_type);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.race_instance_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.season);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.weather);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.ground_condition);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.random_seed);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += formatterResolver.GetFormatterWithVerify<RaceHorseData[]>().Serialize(ref bytes, offset, value.race_horse_data_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<string>().Serialize(ref bytes, offset, value.race_scenario, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.round);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.win_type);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeTeamRaceCharaResult[]>().Serialize(ref bytes, offset, value.chara_result_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.continue_num);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeTeamRaceResult Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __distance_type__ = default(int);
            var __race_instance_id__ = default(int);
            var __season__ = default(int);
            var __weather__ = default(int);
            var __ground_condition__ = default(int);
            var __random_seed__ = default(int);
            var __race_horse_data_array__ = default(RaceHorseData[]);
            var __race_scenario__ = default(string);
            var __round__ = default(int);
            var __win_type__ = default(int);
            var __chara_result_array__ = default(SingleModeTeamRaceCharaResult[]);
            var __continue_num__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __distance_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __race_instance_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __season__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __weather__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __ground_condition__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __random_seed__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 6:
                        __race_horse_data_array__ = formatterResolver.GetFormatterWithVerify<RaceHorseData[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 7:
                        __race_scenario__ = formatterResolver.GetFormatterWithVerify<string>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __round__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 9:
                        __win_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 10:
                        __chara_result_array__ = formatterResolver.GetFormatterWithVerify<SingleModeTeamRaceCharaResult[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 11:
                        __continue_num__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeTeamRaceResult();
            ____result.distance_type = __distance_type__;
            ____result.race_instance_id = __race_instance_id__;
            ____result.season = __season__;
            ____result.weather = __weather__;
            ____result.ground_condition = __ground_condition__;
            ____result.random_seed = __random_seed__;
            ____result.race_horse_data_array = __race_horse_data_array__;
            ____result.race_scenario = __race_scenario__;
            ____result.round = __round__;
            ____result.win_type = __win_type__;
            ____result.chara_result_array = __chara_result_array__;
            ____result.continue_num = __continue_num__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
