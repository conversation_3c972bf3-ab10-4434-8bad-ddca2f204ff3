#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class HeroesFinalCheckRaceResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< HeroesFinalCheckRaceResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public HeroesFinalCheckRaceResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"reward_summary_info", 0},
                {"next_play_info", 1},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("reward_summary_info"),
                global::System.Text.Encoding.UTF8.GetBytes("next_play_info"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, HeroesFinalCheckRaceResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 2);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<RewardSummaryInfo>().Serialize(ref bytes, offset, value.reward_summary_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<HeroesFinalNextPlayInfo>().Serialize(ref bytes, offset, value.next_play_info, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.HeroesFinalCheckRaceResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __reward_summary_info__ = default(RewardSummaryInfo);
            var __next_play_info__ = default(HeroesFinalNextPlayInfo);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __reward_summary_info__ = formatterResolver.GetFormatterWithVerify<RewardSummaryInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __next_play_info__ = formatterResolver.GetFormatterWithVerify<HeroesFinalNextPlayInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new HeroesFinalCheckRaceResponse.CommonResponse();
            ____result.reward_summary_info = __reward_summary_info__;
            ____result.next_play_info = __next_play_info__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
