#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class ChampionsNewsCharaResultInfoFormatter : global::MessagePack.Formatters.IMessagePackFormatter< ChampionsNewsCharaResultInfo >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public ChampionsNewsCharaResultInfoFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"viewer_id", 0},
                {"team_id", 1},
                {"team_member_id", 2},
                {"rank_count_1", 3},
                {"rank_count_2", 4},
                {"rank_count_3", 5},
                {"rank_count_unplaced", 6},
                {"race_win_5", 7},
                {"news_chara_race_history_array", 8},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("viewer_id"),
                global::System.Text.Encoding.UTF8.GetBytes("team_id"),
                global::System.Text.Encoding.UTF8.GetBytes("team_member_id"),
                global::System.Text.Encoding.UTF8.GetBytes("rank_count_1"),
                global::System.Text.Encoding.UTF8.GetBytes("rank_count_2"),
                global::System.Text.Encoding.UTF8.GetBytes("rank_count_3"),
                global::System.Text.Encoding.UTF8.GetBytes("rank_count_unplaced"),
                global::System.Text.Encoding.UTF8.GetBytes("race_win_5"),
                global::System.Text.Encoding.UTF8.GetBytes("news_chara_race_history_array"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, ChampionsNewsCharaResultInfo value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 9);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt64(ref bytes, offset, value.viewer_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.team_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.team_member_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank_count_1);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank_count_2);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank_count_3);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rank_count_unplaced);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.race_win_5);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<ChampionsNewsCharaRaceHistory[]>().Serialize(ref bytes, offset, value.news_chara_race_history_array, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.ChampionsNewsCharaResultInfo Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __viewer_id__ = default(long);
            var __team_id__ = default(int);
            var __team_member_id__ = default(int);
            var __rank_count_1__ = default(int);
            var __rank_count_2__ = default(int);
            var __rank_count_3__ = default(int);
            var __rank_count_unplaced__ = default(int);
            var __race_win_5__ = default(int);
            var __news_chara_race_history_array__ = default(ChampionsNewsCharaRaceHistory[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __viewer_id__ = global::MessagePack.MessagePackBinary.ReadInt64(bytes, offset, out readSize);
                        break;
                    case 1:
                        __team_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __team_member_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __rank_count_1__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __rank_count_2__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __rank_count_3__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 6:
                        __rank_count_unplaced__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 7:
                        __race_win_5__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 8:
                        __news_chara_race_history_array__ = formatterResolver.GetFormatterWithVerify<ChampionsNewsCharaRaceHistory[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new ChampionsNewsCharaResultInfo();
            ____result.viewer_id = __viewer_id__;
            ____result.team_id = __team_id__;
            ____result.team_member_id = __team_member_id__;
            ____result.rank_count_1 = __rank_count_1__;
            ____result.rank_count_2 = __rank_count_2__;
            ____result.rank_count_3 = __rank_count_3__;
            ____result.rank_count_unplaced = __rank_count_unplaced__;
            ____result.race_win_5 = __race_win_5__;
            ____result.news_chara_race_history_array = __news_chara_race_history_array__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
