#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeCookRaceEndResponse_CommonResponseFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeCookRaceEndResponse.CommonResponse >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeCookRaceEndResponse_CommonResponseFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"race_reward_info", 0},
                {"chara_info", 1},
                {"home_info", 2},
                {"add_music", 3},
                {"race_history", 4},
                {"win_saddle_id_array", 5},
                {"race_condition_array", 6},
                {"add_trophy_info", 7},
                {"trophy_reward_info", 8},
                {"reward_summary_info", 9},
                {"race_add_reward_info", 10},
                {"cook_data_set", 11},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("race_reward_info"),
                global::System.Text.Encoding.UTF8.GetBytes("chara_info"),
                global::System.Text.Encoding.UTF8.GetBytes("home_info"),
                global::System.Text.Encoding.UTF8.GetBytes("add_music"),
                global::System.Text.Encoding.UTF8.GetBytes("race_history"),
                global::System.Text.Encoding.UTF8.GetBytes("win_saddle_id_array"),
                global::System.Text.Encoding.UTF8.GetBytes("race_condition_array"),
                global::System.Text.Encoding.UTF8.GetBytes("add_trophy_info"),
                global::System.Text.Encoding.UTF8.GetBytes("trophy_reward_info"),
                global::System.Text.Encoding.UTF8.GetBytes("reward_summary_info"),
                global::System.Text.Encoding.UTF8.GetBytes("race_add_reward_info"),
                global::System.Text.Encoding.UTF8.GetBytes("cook_data_set"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeCookRaceEndResponse.CommonResponse value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 12);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<CharaRaceReward>().Serialize(ref bytes, offset, value.race_reward_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeChara>().Serialize(ref bytes, offset, value.chara_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeHomeInfo>().Serialize(ref bytes, offset, value.home_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += formatterResolver.GetFormatterWithVerify<UserMusic>().Serialize(ref bytes, offset, value.add_music, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += formatterResolver.GetFormatterWithVerify<SingleRaceHistory[]>().Serialize(ref bytes, offset, value.race_history, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += formatterResolver.GetFormatterWithVerify<int[]>().Serialize(ref bytes, offset, value.win_saddle_id_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[6]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeRaceCondition[]>().Serialize(ref bytes, offset, value.race_condition_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[7]);
            offset += formatterResolver.GetFormatterWithVerify<LoginUserTrophyInfo>().Serialize(ref bytes, offset, value.add_trophy_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[8]);
            offset += formatterResolver.GetFormatterWithVerify<RaceRewardData>().Serialize(ref bytes, offset, value.trophy_reward_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[9]);
            offset += formatterResolver.GetFormatterWithVerify<RewardSummaryInfo>().Serialize(ref bytes, offset, value.reward_summary_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[10]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeRaceAddRewardInfo[]>().Serialize(ref bytes, offset, value.race_add_reward_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[11]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeCookDataSet>().Serialize(ref bytes, offset, value.cook_data_set, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeCookRaceEndResponse.CommonResponse Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __race_reward_info__ = default(CharaRaceReward);
            var __chara_info__ = default(SingleModeChara);
            var __home_info__ = default(SingleModeHomeInfo);
            var __add_music__ = default(UserMusic);
            var __race_history__ = default(SingleRaceHistory[]);
            var __win_saddle_id_array__ = default(int[]);
            var __race_condition_array__ = default(SingleModeRaceCondition[]);
            var __add_trophy_info__ = default(LoginUserTrophyInfo);
            var __trophy_reward_info__ = default(RaceRewardData);
            var __reward_summary_info__ = default(RewardSummaryInfo);
            var __race_add_reward_info__ = default(SingleModeRaceAddRewardInfo[]);
            var __cook_data_set__ = default(SingleModeCookDataSet);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __race_reward_info__ = formatterResolver.GetFormatterWithVerify<CharaRaceReward>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __chara_info__ = formatterResolver.GetFormatterWithVerify<SingleModeChara>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __home_info__ = formatterResolver.GetFormatterWithVerify<SingleModeHomeInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 3:
                        __add_music__ = formatterResolver.GetFormatterWithVerify<UserMusic>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 4:
                        __race_history__ = formatterResolver.GetFormatterWithVerify<SingleRaceHistory[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 5:
                        __win_saddle_id_array__ = formatterResolver.GetFormatterWithVerify<int[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 6:
                        __race_condition_array__ = formatterResolver.GetFormatterWithVerify<SingleModeRaceCondition[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 7:
                        __add_trophy_info__ = formatterResolver.GetFormatterWithVerify<LoginUserTrophyInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 8:
                        __trophy_reward_info__ = formatterResolver.GetFormatterWithVerify<RaceRewardData>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 9:
                        __reward_summary_info__ = formatterResolver.GetFormatterWithVerify<RewardSummaryInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 10:
                        __race_add_reward_info__ = formatterResolver.GetFormatterWithVerify<SingleModeRaceAddRewardInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 11:
                        __cook_data_set__ = formatterResolver.GetFormatterWithVerify<SingleModeCookDataSet>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeCookRaceEndResponse.CommonResponse();
            ____result.race_reward_info = __race_reward_info__;
            ____result.chara_info = __chara_info__;
            ____result.home_info = __home_info__;
            ____result.add_music = __add_music__;
            ____result.race_history = __race_history__;
            ____result.win_saddle_id_array = __win_saddle_id_array__;
            ____result.race_condition_array = __race_condition_array__;
            ____result.add_trophy_info = __add_trophy_info__;
            ____result.trophy_reward_info = __trophy_reward_info__;
            ____result.reward_summary_info = __reward_summary_info__;
            ____result.race_add_reward_info = __race_add_reward_info__;
            ____result.cook_data_set = __cook_data_set__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
