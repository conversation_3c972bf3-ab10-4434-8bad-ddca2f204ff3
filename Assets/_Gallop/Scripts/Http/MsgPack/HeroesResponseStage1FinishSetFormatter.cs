#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class HeroesResponseStage1FinishSetFormatter : global::MessagePack.Formatters.IMessagePackFormatter< HeroesResponseStage1FinishSet >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public HeroesResponseStage1FinishSetFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"reward_array", 0},
                {"reward_summary_info", 1},
                {"hero_gauge", 2},
                {"hero_skill_id", 3},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("reward_array"),
                global::System.Text.Encoding.UTF8.GetBytes("reward_summary_info"),
                global::System.Text.Encoding.UTF8.GetBytes("hero_gauge"),
                global::System.Text.Encoding.UTF8.GetBytes("hero_skill_id"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, HeroesResponseStage1FinishSet value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 4);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += formatterResolver.GetFormatterWithVerify<ResponseItem[]>().Serialize(ref bytes, offset, value.reward_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<RewardSummaryInfo>().Serialize(ref bytes, offset, value.reward_summary_info, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.hero_gauge);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.hero_skill_id);
            return offset - startOffset;
        }

        public global::Gallop.HeroesResponseStage1FinishSet Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __reward_array__ = default(ResponseItem[]);
            var __reward_summary_info__ = default(RewardSummaryInfo);
            var __hero_gauge__ = default(int);
            var __hero_skill_id__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __reward_array__ = formatterResolver.GetFormatterWithVerify<ResponseItem[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 1:
                        __reward_summary_info__ = formatterResolver.GetFormatterWithVerify<RewardSummaryInfo>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __hero_gauge__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __hero_skill_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new HeroesResponseStage1FinishSet();
            ____result.reward_array = __reward_array__;
            ____result.reward_summary_info = __reward_summary_info__;
            ____result.hero_gauge = __hero_gauge__;
            ____result.hero_skill_id = __hero_skill_id__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
