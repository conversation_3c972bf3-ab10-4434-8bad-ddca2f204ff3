#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class TrainedCharaSupportCardListFormatter : global::MessagePack.Formatters.IMessagePackFormatter< TrainedCharaSupportCardList >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public TrainedCharaSupportCardListFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"position", 0},
                {"support_card_id", 1},
                {"exp", 2},
                {"limit_break_count", 3},
                {"rental_type", 4},
                {"friend_type", 5},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("position"),
                global::System.Text.Encoding.UTF8.GetBytes("support_card_id"),
                global::System.Text.Encoding.UTF8.GetBytes("exp"),
                global::System.Text.Encoding.UTF8.GetBytes("limit_break_count"),
                global::System.Text.Encoding.UTF8.GetBytes("rental_type"),
                global::System.Text.Encoding.UTF8.GetBytes("friend_type"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, TrainedCharaSupportCardList value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 6);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.position);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.support_card_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.exp);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[3]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.limit_break_count);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[4]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.rental_type);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[5]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.friend_type);
            return offset - startOffset;
        }

        public global::Gallop.TrainedCharaSupportCardList Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __position__ = default(int);
            var __support_card_id__ = default(int);
            var __exp__ = default(int);
            var __limit_break_count__ = default(int);
            var __rental_type__ = default(int);
            var __friend_type__ = default(int);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __position__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __support_card_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 2:
                        __exp__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 3:
                        __limit_break_count__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 4:
                        __rental_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 5:
                        __friend_type__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new TrainedCharaSupportCardList();
            ____result.position = __position__;
            ____result.support_card_id = __support_card_id__;
            ____result.exp = __exp__;
            ____result.limit_break_count = __limit_break_count__;
            ____result.rental_type = __rental_type__;
            ____result.friend_type = __friend_type__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
