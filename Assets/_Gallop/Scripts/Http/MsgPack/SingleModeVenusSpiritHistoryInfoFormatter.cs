#pragma warning disable 618
#pragma warning disable 612
#pragma warning disable 414
#pragma warning disable 168

namespace Gallop.MsgPack.Formatters
{
    using System;
    using MessagePack;
    using Gallop;

    public sealed class SingleModeVenusSpiritHistoryInfoFormatter : global::MessagePack.Formatters.IMessagePackFormatter< SingleModeVenusSpiritHistoryInfo >
    {
        readonly global::MessagePack.Internal.AutomataDictionary ____keyMapping;
        readonly byte[][] ____stringByteKeys;

        public SingleModeVenusSpiritHistoryInfoFormatter()
        {
            this.____keyMapping = new global::MessagePack.Internal.AutomataDictionary()
            {
                {"set_id", 0},
                {"spirit_info_array", 1},
                {"venus_spirit_active_effect_info_array", 2},
            };

            this.____stringByteKeys = new byte[][]
            {
                global::System.Text.Encoding.UTF8.GetBytes("set_id"),
                global::System.Text.Encoding.UTF8.GetBytes("spirit_info_array"),
                global::System.Text.Encoding.UTF8.GetBytes("venus_spirit_active_effect_info_array"),
            };
        }

        public int Serialize(ref byte[] bytes, int offset, SingleModeVenusSpiritHistoryInfo value, global::MessagePack.IFormatterResolver formatterResolver)
        {
            if (value == null)
            {
                return global::MessagePack.MessagePackBinary.WriteNil(ref bytes, offset);
            }

            var startOffset = offset;
            offset += global::MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(ref bytes, offset, 3);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[0]);
            offset += global::MessagePack.MessagePackBinary.WriteInt32(ref bytes, offset, value.set_id);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[1]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeVenusSpiritInfo[]>().Serialize(ref bytes, offset, value.spirit_info_array, formatterResolver);
            offset += global::MessagePack.MessagePackBinary.WriteStringBytes(ref bytes, offset, this.____stringByteKeys[2]);
            offset += formatterResolver.GetFormatterWithVerify<SingleModeVenusActiveSpiritEffect[]>().Serialize(ref bytes, offset, value.venus_spirit_active_effect_info_array, formatterResolver);
            return offset - startOffset;
        }

        public global::Gallop.SingleModeVenusSpiritHistoryInfo Deserialize(byte[] bytes, int offset, global::MessagePack.IFormatterResolver formatterResolver, out int readSize)
        {
            if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
            {
                readSize = 1;
                return null;
            }

            var startOffset = offset;
            var length = 0;
            readSize = 1;
            if (bytes[offset] != 0x90)
            {
                length = global::MessagePack.MessagePackBinary.ReadMapHeader(bytes, offset, out readSize);
            }
            offset += readSize;
            
            var __set_id__ = default(int);
            var __spirit_info_array__ = default(SingleModeVenusSpiritInfo[]);
            var __venus_spirit_active_effect_info_array__ = default(SingleModeVenusActiveSpiritEffect[]);

            for (int i = 0; i < length; i++)
            {
                var stringKey = global::MessagePack.MessagePackBinary.ReadStringSegment(bytes, offset, out readSize);
                offset += readSize;
                int key;
                if (!____keyMapping.TryGetValueSafe(stringKey, out key))
                {
                    readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                    goto NEXT_LOOP;
                }
            
                if (global::MessagePack.MessagePackBinary.IsNil(bytes, offset))
                {
                    // null項目はdefault値を差し込みたいので読み込みをスキップする
                    readSize = 1;
                    goto NEXT_LOOP;
                }

                switch (key)
                {
                    case 0:
                        __set_id__ = global::MessagePack.MessagePackBinary.ReadInt32(bytes, offset, out readSize);
                        break;
                    case 1:
                        __spirit_info_array__ = formatterResolver.GetFormatterWithVerify<SingleModeVenusSpiritInfo[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    case 2:
                        __venus_spirit_active_effect_info_array__ = formatterResolver.GetFormatterWithVerify<SingleModeVenusActiveSpiritEffect[]>().Deserialize(bytes, offset, formatterResolver, out readSize);
                        break;
                    default:
                        readSize = global::MessagePack.MessagePackBinary.ReadNextBlock(bytes, offset);
                        break;
                }

                NEXT_LOOP:
                offset += readSize;
            }

            readSize = offset - startOffset;

            var ____result = new SingleModeVenusSpiritHistoryInfo();
            ____result.set_id = __set_id__;
            ____result.spirit_info_array = __spirit_info_array__;
            ____result.venus_spirit_active_effect_info_array = __venus_spirit_active_effect_info_array__;
            return ____result;
        }
    }
}

#pragma warning restore 168
#pragma warning restore 414
#pragma warning restore 618
#pragma warning restore 612
