namespace Gallop
{
    namespace LitJsonUtil
    {
        ///<sammary>
        /// LitJsonを扱いやすくするWrapper
        ///  Lit<PERSON>sonの最新化時に廃止予定
        ///</sammary>
        public static class JsonMapper
        {
            static JsonMapper()
            {
                //float<>double, int<>long の変換
                LitJson.JsonMapper.RegisterExporter<float>((obj, writer) =>
                {
                    writer.Write(System.Convert.ToDouble(obj));
                });
                LitJson.JsonMapper.RegisterImporter<System.Double, float>((input) =>
                {
                    return System.Convert.ToSingle(input);
                });
                LitJson.JsonMapper.RegisterImporter<System.Int32, long>((input) =>
                {
                    return System.Convert.ToInt64(input);
                });
            }

            public static LitJson.JsonData ToObject(string json)
            {
                return LitJson.JsonMapper.ToObject(json);
            }

            public static T ToObject<T>(string json)
            {
                return LitJson.JsonMapper.ToObject<T>(json);
            }

            public static string ToJson(object obj)
            {
                return LitJson.JsonMapper.ToJson(obj);
            }
        }
    }
}