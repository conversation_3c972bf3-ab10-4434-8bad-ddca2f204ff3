using System.Collections.Generic;
using System.Linq;
using CodeStage.AntiCheat.ObscuredTypes;
using UnityEngine;

namespace Gallop
{
    public partial class WorkSingleModeChangeParameterInfo
    {
        public WorkSingleModeChangeParameterInfoScenarioMecha ScenarioMecha = new WorkSingleModeChangeParameterInfoScenarioMecha();
    }
    
    /// <summary>
    /// メカ編用
    /// </summary>
    public class WorkSingleModeChangeParameterInfoScenarioMecha
    {
        /// <summary> ライバル情報（研究Lvなど） </summary>
        public WorkSingleModeScenarioMecha.WorkRivalData RivalData { get; } = new WorkSingleModeScenarioMecha.WorkRivalData();
        /// <summary> 変化前のライバル情報（研究Lvなど） </summary>
        public WorkSingleModeScenarioMecha.WorkRivalData PrevRivalData { get; private set; }
        
        /// <summary> 研究進捗度 </summary>
        public SingleModeScenarioMechaDefine.ProgressResultType ProgressResultType = SingleModeScenarioMechaDefine.ProgressResultType.None;

        /// <summary> 上昇しなかった研究Lv </summary>
        public int[] NotUpStatusTypeArray { get; private set; }
        /// <summary> ソート済み上昇しなかった研究Lv (スピ、スタ、パワーの順)</summary>
        public int[] SortedNotUpStatusTypeArray => NotUpStatusTypeArray.OrderBy(x => System.Array.IndexOf(TrainingDefine.OrderedTrainingCommandIdArray, (TrainingDefine.TrainingCommandId)x)).ToArray();

        /// <summary> 変化前のオーバードライブ情報</summary>
        public WorkSingleModeScenarioMecha.WorkOverdriveInfo PrevOverdriveInfo { get; private set; }
        /// <summary> オーバードライブ使用可能回数 </summary>
        public ObscuredInt OverdriveRemainNum{ get; private set; }
        /// <summary> オーバードライブ回数はすでに最大 </summary>
        public ObscuredBool OverdriveNumMaxFlag{ get; private set; }
        
        /// <summary> オーバードライブ獲得エネルギー </summary>
        public ObscuredInt OverdriveEnergyNum{ get; private set; }

        /// <summary> チューニングポイント </summary>
        public ObscuredInt TuningPoint{ get; private set; }

        /// <summary> キャラクター加入 </summary>
        public readonly List<int> JoinCharaIdList = new List<int>();


        public void Clear()
        {
            RivalData.Clear();
            PrevRivalData = null;
            ProgressResultType = 0;
            ProgressResultType = SingleModeScenarioMechaDefine.ProgressResultType.None;
            NotUpStatusTypeArray = null;
            PrevOverdriveInfo = null;
            OverdriveRemainNum = 0;
            OverdriveNumMaxFlag = false;
            OverdriveEnergyNum = 0;
            TuningPoint = 0;
            JoinCharaIdList.Clear();
        }
        
        public void Set(
            WorkSingleModeData srcData,
            SingleModeMechaDataSet destDataSet)
        {
            if (destDataSet == null || srcData == null) return;
            var srcMecha = srcData.ScenarioMecha;

            // ライバル情報（研究Lvなど）
            if (destDataSet.rival_info != null)
            {
                var srcRivalData = srcMecha.RivalData;
                PrevRivalData = srcRivalData;
                RivalData.SetDelta(srcRivalData, destDataSet.rival_info);
                var destRivalData = new WorkSingleModeScenarioMecha.WorkRivalData(destDataSet.rival_info);//更新後のライバル情報

                // 研究進捗度アップ
                if (srcMecha.RivalData.ProgressResultType < destRivalData.ProgressResultType)
                {
                    ProgressResultType = destRivalData.ProgressResultType;
                }
            }

            // 上昇しなかった研究Lv
            if (destDataSet.not_up_macha_parameter_info != null)
            {
                var statusTypeArray = destDataSet.not_up_macha_parameter_info.status_type_array;
                if (statusTypeArray != null)
                {
                    NotUpStatusTypeArray = statusTypeArray;
                }

                // オーバードライブ回数はすでに最大
                OverdriveNumMaxFlag = destDataSet.not_up_macha_parameter_info.overdrive_num_max_flag;
            }
            
            // オーバードライブ
            if (destDataSet.overdrive_info != null)
            {
                PrevOverdriveInfo = srcMecha.OverdriveInfo;
                var srcOverdriveRemainNum = srcMecha.OverdriveInfo.RemainNnum;
                var destOverdriveRemainNum = destDataSet.overdrive_info.remain_num;
                var deltaRemainNum = System.Math.Max(0, destOverdriveRemainNum - srcOverdriveRemainNum);//回数が増えたか（使用して減った時は検出しない）
                if (deltaRemainNum > 0)
                {
                    // OD回数回復
                    OverdriveRemainNum = destOverdriveRemainNum;
                }
                var remainNumToEnergy = srcMecha.OverdriveInfo.MaxEnergyNum * deltaRemainNum;//増えた回数分のエネルギー量
                //オーバードライブエネルギー変化量（ストックに変換されていればenergy_numは減少するがremainNumToEnergy分増えている)
                OverdriveEnergyNum = destDataSet.overdrive_info.energy_num - srcMecha.OverdriveInfo.EnergyNum + remainNumToEnergy;
            }

            TuningPoint = destDataSet.tuning_point - srcMecha.TuningPoint;
            
            // 研究メンバー加入
            if (destDataSet.evaluation_info_array != null)
            {
                foreach (var destEvaluationInfo in destDataSet.evaluation_info_array)
                {
                    if (destEvaluationInfo.is_research_member == 0) continue;//まだメンバーじゃないので演出なし
                    
                    var evaluation = srcData.Character.GetEvaluation(destEvaluationInfo.target_id);
                    if (evaluation == null)
                    {
                        JoinCharaIdList.Add(destEvaluationInfo.chara_id);//新規加入（野良、シナリオリンクなどはEvaluationない状況から加入してくる)
                    }
                    else
                    {
                        if (evaluation.IsMechaResearchMember) continue;//すでにメンバーなので演出なし
                        JoinCharaIdList.Add(destEvaluationInfo.chara_id);//新規加入
                    }
                }
            }
            
#if CYG_DEBUG
            if (DebugPageSingleModeScenarioMecha.OverrideStoryEventParamChange)
            {
                SetDebugData();   
            }
#endif
        }
        
        /// <summary>
        /// メカの研究Lvに変動があるか
        /// </summary>
        public bool IsChangeRivalStatus()
        {
            if (0 < RivalData.Speed || 0 < RivalData.Stamina || 0 < RivalData.Pow || 0 < RivalData.Guts || 0 < RivalData.Wiz) return true;
            return false;
        }
        
        /// <summary>
        /// メカの研究Lvの変動全て同じ値か
        /// </summary>
        public bool IsChangeRivalStatusAllSame()
        {
            return RivalData.Speed == RivalData.Stamina && 
                   RivalData.Speed== RivalData.Pow && 
                   RivalData.Speed== RivalData.Guts && 
                   RivalData.Speed== RivalData.Wiz;
        }
        
        /// <summary>
        /// メカの研究Lv上限に変動があるか
        /// </summary>
        public bool IsChangeRivalStatusLimit()
        {
            if (0 < RivalData.SpeedLimit || 0 < RivalData.StaminaLimit || 0 < RivalData.PowLimit || 0 < RivalData.GutsLimit || 0 < RivalData.WizLimit) return true;
            return false;
        }
        
        /// <summary>
        /// メカの研究Lv上限の全てに変動があるか
        /// </summary>
        public bool IsChangeRivalStatusLevelLimitAll()
        {
            return 0 < RivalData.SpeedLimit && 0 < RivalData.StaminaLimit && 0 < RivalData.PowLimit && 0 < RivalData.GutsLimit && 0 < RivalData.WizLimit;
        }
        
#if CYG_DEBUG
        /// <summary>
        /// デバッグ機能でパラメータアップ演出用の値を設定
        /// </summary>
        public void SetDebugData()
        {
            // 研究Lvアップ or 研究Lv上限アップ
            var rivalInfo = (Random.Range(0, 2) == 0)
                ? new SingleModeMechaRivalInfo
                {
                    speed = 5,
                    stamina = 5,
                    power = 5,
                    guts = 5,
                    wiz = 5,
                }
                : new SingleModeMechaRivalInfo
                {
                    speed_limit = 10,
                    stamina_limit = 10,
                    power_limit = 10,
                    guts_limit = 10,
                    wiz_limit = 10,
                };
            RivalData.Set(rivalInfo);
            var rivalData = WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData;
            PrevRivalData = new WorkSingleModeScenarioMecha.WorkRivalData(new SingleModeMechaRivalInfo
            {
                speed = rivalData.Speed - RivalData.Speed,
                stamina = rivalData.Stamina - RivalData.Stamina,
                power = rivalData.Pow - RivalData.Pow,
                guts = rivalData.Guts - RivalData.Guts,
                wiz = rivalData.Wiz - RivalData.Wiz,
                speed_limit = rivalData.SpeedLimit - RivalData.SpeedLimit,
                stamina_limit = rivalData.StaminaLimit - RivalData.StaminaLimit,
                power_limit = rivalData.PowLimit - RivalData.PowLimit,
                guts_limit = rivalData.GutsLimit - RivalData.GutsLimit,
                wiz_limit = rivalData.WizLimit - RivalData.WizLimit,
            });
            // 研究進捗
            ProgressResultType = SingleModeScenarioMechaDefine.ProgressResultType.GreatSuccess;
            // オーバードライブ
            OverdriveRemainNum = 1;
            OverdriveEnergyNum = 1;
            PrevOverdriveInfo = new WorkSingleModeScenarioMecha.WorkOverdriveInfo(new SingleModeMechaOverDriveInfo
            {
                remain_num = 1,
                energy_num = 1,
            });
            // 研究メンバー加入
            JoinCharaIdList.Add(1001);
            JoinCharaIdList.Add(1002);
            JoinCharaIdList.Add(1003);
            JoinCharaIdList.Add(1004);
            JoinCharaIdList.Add(1005);
            JoinCharaIdList.Add(1006);
            JoinCharaIdList.Add(1007);
            JoinCharaIdList.Add(1008);
            JoinCharaIdList.Add(1009);
            JoinCharaIdList.Add(1010);
            JoinCharaIdList.Add(1011);
            JoinCharaIdList.Add(1012);
        }        
#endif
    }
}
