using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    public class WorkTrainingReportData
    {
        public sealed class RewardData
        {
            /// <summary>
            /// アイテムカテゴリー
            /// </summary>
            public readonly GameDefine.ItemCategory ItemCategory;

            /// <summary>
            /// アイテムid
            /// </summary>
            public readonly int ItemId;

            /// <summary>
            /// 数
            /// </summary>
            public readonly int ItemNum;

            /// <summary>
            /// 獲得条件となるポイント
            /// </summary>
            public readonly int ConditionPoint;

            /// <summary>
            /// 報酬を取得済みかどうか
            /// </summary>
            public readonly bool IsReceived;

            /// <summary>
            /// 有償パス未購入でロックされているかどうか
            /// </summary>
            public readonly bool IsLocked;

            /// <summary>
            /// ピックアップ報酬かどうか
            /// </summary>
            public readonly bool IsPickup;

            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RewardData(GameDefine.ItemCategory itemCategory = 0, int itemId = 0, int itemNum = 0, int conditionPoint = 0, bool isReceived = false, bool isLocked = false, bool isPickup = false)
            {
                this.ItemCategory = itemCategory;
                this.ItemId = itemId;
                this.ItemNum = itemNum;
                this.ConditionPoint = conditionPoint;
                this.IsReceived = isReceived;
                this.IsLocked = isLocked;
                this.IsPickup = isPickup;
            }
        }

        #region Variable, Property

        /// <summary>
        /// 受取済みの報酬一覧（表示用）
        /// </summary>
        private readonly List<DisplayRewardInfo> _receivedRewardInfoList = new List<DisplayRewardInfo>();
        public List<DisplayRewardInfo> ReceivedRewardInfoList => _receivedRewardInfoList;

        /// <summary>
        /// トレーニングレポート無償報酬情報
        /// </summary>
        private readonly List<RewardData> _normalRewardList = new List<RewardData>();
        public List<RewardData> NormalRewardList => _normalRewardList;

        /// <summary>
        /// トレーニングレポート有償報酬情報
        /// </summary>
        private readonly List<RewardData> _paidRewardList = new List<RewardData>();
        public List<RewardData> PaidRewardList => _paidRewardList;

        /// <summary>
        /// トレーニングレポート表示用報酬情報
        /// </summary>
        private readonly List<Dictionary<RewardType, RewardData>> _displayRewardList = new List<Dictionary<RewardType, RewardData>>();
        public List<Dictionary<RewardType, RewardData>> DisplayRewardList => _displayRewardList;

        /// <summary>
        /// トレーニングレポートインフレ計算用CSVデータ
        /// </summary>
        private MasterTrainingReportInflation.TrainingReportInflation[] _trainingReportInflationList = null;

        /// <summary>
        /// シーズンID
        /// </summary>
        public ObscuredInt SeasonId { get; set; } = 0;

        /// <summary>
        /// 獲得ポイント
        /// </summary>
        public ObscuredInt Point { get; set; } = 0;

        /// <summary>
        /// 次に獲得できる報酬の必要ポイント
        /// </summary>
        public int NextRewardPoint { get; set; } = 0;

        /// <summary>
        /// 次に獲得できる報酬のインデックス値
        /// </summary>
        public int NextRewardIndex { get; set; } = INVALID_REWARD_INDEX;

        /// <summary>
        /// 次に獲得できる無償ピックアップ報酬のインデックス（_normalRewardListのインデックス値）
        /// </summary>
        public int NextPickupRewardIndexNormal { get; set; } = INVALID_REWARD_INDEX;

        /// <summary>
        /// 次に獲得できる有償ピックアップ報酬のインデックス（_paidRewardListのインデックス値）
        /// </summary>
        public int NextPickupRewardIndexPaid { get; set; } = INVALID_REWARD_INDEX;

        /// <summary>
        /// 有償パスを所持しているかどうか
        /// </summary>
        public ObscuredBool HasPaidPass { get; set; } = false;

        /// <summary>
        /// シーズンが切り替わった初回遷移時にON（通知ダイアログを出すため）
        /// </summary>
        public ObscuredBool SeasonChangeFlag { get; set; } = false;

        /// <summary>
        /// 受取可能な報酬が存在するかどうか（ホームメニューの通知バッジ表示用）
        /// </summary>
        public ObscuredBool EnableReceiveReward { get; set; } = false;

        /// <summary>
        /// 全ての報酬を獲得できるポイントに到達したかどうか
        /// </summary>
        public ObscuredBool IsCompleted { get; set; } = false;

        /// <summary>
        /// 報酬タイプ
        /// </summary>
        public enum RewardType
        {
            Normal = 0, // 無償
            Paid = 1, // 有償
        }

        // 無効な報酬インデックス値（対象となるものがないときに設定される）
        private const int INVALID_REWARD_INDEX = -1;

        #endregion

        #region Method

        /// <summary>
        /// 更新（シーントップAPI）
        /// </summary>
        /// <param name="response"></param>
        //public void Update(LTrainingReportIndexResponse response)
        public void Update(TrainingReportIndexResponse response)
        {
            _receivedRewardInfoList.Clear();
            SeasonId = 0;
            Point = 0;
            NextRewardPoint = 0;
            NextRewardIndex = INVALID_REWARD_INDEX;
            NextPickupRewardIndexNormal = INVALID_REWARD_INDEX;
            NextPickupRewardIndexPaid = INVALID_REWARD_INDEX;
            HasPaidPass = false;
            SeasonChangeFlag = false;
            EnableReceiveReward = false;
            IsCompleted = true;

            _normalRewardList.Clear();
            _paidRewardList.Clear();
            _displayRewardList.Clear();

            if (response == null)
                return;

            if (response.data.training_report_info != null)
            {
                SeasonId = response.data.training_report_info.season_id;
                Point = response.data.training_report_info.point;
                HasPaidPass = (response.data.training_report_info.is_payment != 0);
            }

            if (response.data.reward_info_array != null && !response.data.reward_info_array.IsNullOrEmpty())
            {
                _receivedRewardInfoList.AddRange(response.data.reward_info_array);
            }

            if (response.data.reward_summary_info != null)
            {
                WorkDataUtil.SetRewardSummaryInfo(response.data.reward_summary_info);
            }

            SeasonChangeFlag = response.data.season_change_flag;

            // 必要ポイントの計算で使用するので予めCSVをロードしておく
            _trainingReportInflationList = MasterDataManager.Instance.masterTrainingReportInflation.dictionary.Values
                .OrderByDescending(d => d.PointThreshold).ToArray(); // 降順でソート
            // 各種報酬リストと表示用の報酬リストを作成
            CreateNormalRewardList();
            CreatePaidRewardList();
            CreateDisplayRewardList();
        }

        /// <summary>
        /// 無償報酬のリスト作成
        /// </summary>
        /// <returns></returns>
        private void CreateNormalRewardList()
        {
            var masterTrainingReportPass = MasterDataManager.Instance.masterTrainingReportPass.GetWithSeasonId(SeasonId);
            var rewardList =
                MasterDataManager.Instance.masterTrainingReportNormalReward.GetListWithRewardSetOrderByIdAsc(
                    masterTrainingReportPass.NormalRewardSet);

            var count = rewardList.Count;
            var lastPickupIndex = INVALID_REWARD_INDEX;
            for (var i = 0; i < count; i++)
            {
                var rewardData = rewardList[i];
                var conditionPoint = CalcConditionPoint(rewardData.BasePoint, masterTrainingReportPass.InflationCoef);

                _normalRewardList.Add(new RewardData
                (
                    (GameDefine.ItemCategory)rewardData.RewardItemCategory,
                    rewardData.RewardItemId,
                    rewardData.RewardItemNum,
                    conditionPoint,
                    Point >= conditionPoint,
                    isPickup: rewardData.IsPickup
                ));

                // 未獲得の報酬関連チェック
                if (Point < conditionPoint)
                {
                    // 直近で獲得できる報酬のポイント更新
                    if (conditionPoint < NextRewardPoint || NextRewardPoint == 0)
                    {
                        NextRewardPoint = conditionPoint;
                    }

                    // 直近で獲得できるピックアップ報酬のインデックス値更新
                    if (rewardData.IsPickup && NextPickupRewardIndexNormal == INVALID_REWARD_INDEX)
                    {
                        NextPickupRewardIndexNormal = i;
                    }

                    // 全ての報酬を獲得できるポイントに到達していないのでフラグを落とす
                    IsCompleted = false;
                }

                // 報酬最後のピックアップ報酬を記憶する
                if (rewardData.IsPickup)
                {
                    lastPickupIndex = i;
                }
            }

            // 直近で獲得できるピックアップ報酬がなければ最後のピックアップ報酬を表示する
            if (NextPickupRewardIndexNormal == INVALID_REWARD_INDEX)
            {
                NextPickupRewardIndexNormal = lastPickupIndex;
            }
        }

        /// <summary>
        /// 有償報酬のリスト作成
        /// </summary>
        /// <returns></returns>
        private void CreatePaidRewardList()
        {
            var masterTrainingReportPass = MasterDataManager.Instance.masterTrainingReportPass.GetWithSeasonId(SeasonId);
            var rewardList =
                MasterDataManager.Instance.masterTrainingReportSpecialReward.GetListWithRewardSetOrderByIdAsc(
                    masterTrainingReportPass.SpecialRewardSet);

            var count = rewardList.Count;
            var lastPickupIndex = INVALID_REWARD_INDEX;
            for (var i = 0; i < count; i++)
            {
                var rewardData = rewardList[i];
                var conditionPoint = CalcConditionPoint(rewardData.BasePoint, masterTrainingReportPass.InflationCoef);

                _paidRewardList.Add(new RewardData
                (
                    (GameDefine.ItemCategory)rewardData.RewardItemCategory,
                    rewardData.RewardItemId,
                    rewardData.RewardItemNum,
                    conditionPoint,
                    HasPaidPass && Point >= conditionPoint,
                    !HasPaidPass,
                    isPickup: rewardData.IsPickup
                ));

                // 未獲得の報酬関連チェック
                if (Point < conditionPoint)
                {
                    if (HasPaidPass)
                    {
                        // 直近で獲得できる報酬のポイント更新
                        if (conditionPoint < NextRewardPoint || NextRewardPoint == 0)
                        {
                            NextRewardPoint = conditionPoint;
                        }
                    }
                    // 直近で獲得できるピックアップ報酬のインデックス値更新（パスの所持に関わらず現在の所持しているポイント基準で表示）
                    if (rewardData.IsPickup && NextPickupRewardIndexPaid == INVALID_REWARD_INDEX)
                    {
                        NextPickupRewardIndexPaid = i;
                    }

                    // 全ての報酬を獲得できるポイントに到達していないのでフラグを落とす
                    IsCompleted = false;
                }
                // 報酬最後のピックアップ報酬を記憶する
                if (rewardData.IsPickup)
                {
                    lastPickupIndex = i;
                }
            }

            // 直近で獲得できるピックアップ報酬がなければ最後のピックアップ報酬を表示する
            if (NextPickupRewardIndexPaid == INVALID_REWARD_INDEX)
            {
                NextPickupRewardIndexPaid = lastPickupIndex;
            }
        }

        /// <summary>
        /// トレーニングレポート報酬の表示用リスト作成
        /// </summary>
        /// <returns></returns>
        private void CreateDisplayRewardList()
        {
            int normalCount = _normalRewardList.Count;
            int paidCount = _paidRewardList.Count;

            // ConditionPointが低い順に表示するのでその順番で配列に格納していく
            for (int totalCount = 0, idxNormal = 0, idxPaid = 0; idxNormal < normalCount && idxPaid < paidCount; totalCount++)
            {
                var normalReward = normalCount > idxNormal ? _normalRewardList[idxNormal] : null;
                var paidReward = paidCount > idxPaid ? _paidRewardList[idxPaid] : null;

                // 無償と有償で表示候補が残っている場合はConditionPointをチェックし低い方を優先（同値だった場合は同じ行に表示するので両方Dicに格納）
                if (normalReward != null && paidReward != null)
                {
                    if (normalReward.ConditionPoint > paidReward.ConditionPoint)
                    {
                        normalReward = null;
                    }
                    else if (normalReward.ConditionPoint < paidReward.ConditionPoint)
                    {
                        paidReward = null;
                    }
                }

                var dic = new Dictionary<RewardType, RewardData>();
                if (normalReward != null)
                {
                    dic.Add(RewardType.Normal, normalReward);
                    idxNormal++;

                    // 次に獲得できる報酬にスクロール位置を合わせるためインデックスを保存
                    if (NextRewardPoint == normalReward.ConditionPoint)
                    {
                        NextRewardIndex = totalCount;
                    }
                }

                if (paidReward != null)
                {
                    dic.Add(RewardType.Paid, paidReward);
                    idxPaid++;

                    // 次に獲得できる報酬にスクロール位置を合わせるためインデックスを保存
                    if (HasPaidPass && NextRewardPoint == paidReward.ConditionPoint)
                    {
                        NextRewardIndex = totalCount;
                    }
                }

                _displayRewardList.Add(dic);
            }
        }

        /// <summary>
        /// 報酬獲得条件となる必要ポイント計算
        /// </summary>
        /// <returns></returns>
        private int CalcConditionPoint(int basePoint, int inflationCoef)
        {
            var conditionPoint = basePoint;
            var count = _trainingReportInflationList.Length;
            for (var i = 0; i < count; i++)
            {
                var trainingReportInflation = _trainingReportInflationList[i];
                // 降順でチェックを行い、basePointが閾値を超えるレコードのvalueを適用する
                if (trainingReportInflation.PointThreshold <= basePoint)
                {
                    conditionPoint += inflationCoef * trainingReportInflation.Value / 10000;
                    break;
                }
            }

            return conditionPoint;
        }

        /// <summary>
        /// トレーニングレポート報酬の表示用データから獲得条件となるポイントを取得
        /// </summary>
        /// <returns>獲得条件ポイント</returns>
        public int GetConditionPoint(Dictionary<RewardType, RewardData>　rewardDataDic)
        {
            if (rewardDataDic.TryGetValue(WorkTrainingReportData.RewardType.Normal, out var rewardDataNormal))
            {
                return rewardDataNormal.ConditionPoint;
            }
            else if (rewardDataDic.TryGetValue(WorkTrainingReportData.RewardType.Paid, out var rewardDataPaid))
            {
                return rewardDataPaid.ConditionPoint;
            }

            return 0;
        }

        /// <summary>
        /// 更新（タイトルからのロード）
        /// </summary>
        /// <param name="response"></param>
        public void UpdateLoad(TrainingReportInfo response)
        {
            SeasonId = 0;
            Point = 0;
            HasPaidPass = false;
            // ロードのタイミングでmaster情報が更新される場合があるのでリセットする
            MasterDataManager.Instance.masterTrainingReportPass.Reset();

            if (response == null)
                return;

            SeasonId = response.season_id;
            Point = response.point;
            HasPaidPass = (response.is_payment != 0);
        }

        /// <summary>
        /// 受け取れる報酬がある通知表示フラグの更新
        /// </summary>
        /// <param name="info"></param>
        public void UpdateNotification(NotificationInfo info)
        {
            if (info == null)
            {
                return;
            }

            EnableReceiveReward = info.training_report_receivable_reward_flag != 0;
        }

        /// <summary>
        /// 更新（課金購入）
        /// </summary>
        /// <param name="response"></param>
        public void UpdatePaymentFinish(TrainingReportInfo response)
        {
            // TrainingReportInfoがnullであれば別の商品を購入した時なので無視
            if (response == null)
                return;

            SeasonId = response.season_id;
            Point = response.point;
            HasPaidPass = (response.is_payment != 0);
        }

        /// <summary>
        /// 次に獲得できるピックアップ報酬情報を取得する
        /// </summary>
        public (RewardData normalRewardData, RewardData paidRewardData) GetPickupRewardData()
        {
            var normalReward = NextPickupRewardIndexNormal != INVALID_REWARD_INDEX
                ? _normalRewardList[NextPickupRewardIndexNormal] : null;
            var paidReward = NextPickupRewardIndexPaid != INVALID_REWARD_INDEX
                ? _paidRewardList[NextPickupRewardIndexPaid] : null;
            return (normalReward, paidReward);
        }

        /// <summary>
        /// ピックアップ報酬の配列を取得する
        /// </summary>
        public Dictionary<RewardType, RewardData>[] GetPickupRewardArray()
        {
            return _displayRewardList.Where(IsContainPickupReward).ToArray();
        }

        /// <summary>
        /// ピックアップ報酬が含まれているかどうかチェック
        /// </summary>
        private bool IsContainPickupReward(Dictionary<RewardType, RewardData> rewardDataDic)
        {
            if (rewardDataDic.TryGetValue(RewardType.Normal, out var rewardDataNormal)
                && rewardDataNormal.IsPickup)
            {
                return true;
            }

            if (rewardDataDic.TryGetValue(RewardType.Paid, out var rewardDataPaid)
                && rewardDataPaid.IsPickup)
            {
                return true;
            }

            return false;
        }

        #endregion
    }
}
