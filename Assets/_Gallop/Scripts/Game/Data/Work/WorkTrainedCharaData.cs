using System;
using CodeStage.AntiCheat.ObscuredTypes;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 殿堂入りウマ娘
    /// </summary>
    public class WorkTrainedCharaData
    {
        #region Const, Enum

        //不正ID
        public const int INVALID_ID = -1;

        // 自分のキャラのowner_viewer_id
        private const long MY_OWNER_VIEWER_ID = 0;

        #endregion

        #region Variable, Property

        /// <summary>
        /// 使用可能な殿堂入りキャラ辞書
        /// ** isSaved = true のデータ。（育成でレンタルしているウマ娘はisSaved = trueで入っているのでこの辞書に入っている）
        /// なのでこの辞書==レースで使用可能な一覧ではないので注意。使用可能なもの一覧は「List」で取得する
        /// </summary>
        private readonly Dictionary<int, TrainedCharaData> _dataDic = new Dictionary<int, TrainedCharaData>();

        /// <summary>
        /// isSaved=falseのデータも含めた継承で使われているものを含めた殿堂入りデータ辞書。（継承につかわれていないウマ娘で、削除されている殿堂入りウマ娘は入っていない）
        /// ** 継承ツリーで過去の殿堂入りデータも見えるようにしておく必要があるため保持している。
        /// レンタル中のウマ娘など、自分のウマ娘として保存されているものはこちらに全て含まれている
        /// </summary>
        private readonly Dictionary<int, TrainedCharaData> _allDataDic = new Dictionary<int, TrainedCharaData>();

        /// <summary>
        /// 使用可能な殿堂入りキャラのリスト（他人のウマ娘やレンタルウマ娘を省く）
        /// </summary>
        public List<TrainedCharaData> List { get; private set; } = new List<TrainedCharaData>();

        /// <summary>
        /// お気に入り辞書
        /// </summary>
        private readonly Dictionary<int, FavoriteData> _favoriteDataDict = new Dictionary<int, FavoriteData>();

        /// <summary>
        /// 拡張した殿堂入りウマ娘登録枠
        /// </summary>
        public static int TrainedCharaExtendSaveNum { get; private set; } = 0;

        //所持可能枠数取得
        public static int TrainedCharaNumMax => ServerDefine.TrainedCharaDefaultSaveNum + TrainedCharaExtendSaveNum;

        #endregion

        #region Method

        /// <summary>
        /// 殿堂入りキャラIDからデータ取得
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <param name="all">allDataDicからも検索する。継承関係の取得の場合などで使用する。</param>
        /// <returns></returns>
        public TrainedCharaData Get(int trainedCharaId, bool all = false)
        {
            var targetDic = all ? _allDataDic : _dataDic;

            if (targetDic == null)
            {
                return null;
            }

            targetDic.TryGetValue(trainedCharaId, out var data);
            return data;
        }

        /// <summary>
        /// 対象の殿堂入りウマ娘の有無
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public bool HasData(int trainedCharaId)
        {
            var chara = Get(trainedCharaId);
            return chara != null && chara.IsPlayer;// 育成継承レンタルを除ける
        }

        /// <summary>
        /// データ一括更新
        /// </summary>
        /// <param name="trainedCharaArray"></param>
        /// <returns></returns>
        public void UpdateAll(TrainedChara[] trainedCharaArray)
        {
            _dataDic.Clear();
            _allDataDic.Clear();
            AddTrainedCharaArray(trainedCharaArray);
        }

        /// <summary>
        /// 殿堂入り追加
        /// </summary>
        public void AddTrainedCharaArray(TrainedChara[] trainedCharaArray)
        {
            for (int i = 0; i < trainedCharaArray.Length; ++i)
            {
                TrainedCharaData charaData = new TrainedCharaData(trainedCharaArray[i]);
                if (charaData.IsSaved)
                {
                    _dataDic.Add(charaData.Id, charaData);
                }
                _allDataDic.Add(charaData.Id, charaData);
            }
            UpdateList();
        }

        /// <summary>
        /// データ更新（単体）
        /// </summary>
        /// <param name="trainedChara"></param>
        public void Update(TrainedChara trainedChara)
        {
            if (trainedChara == null) return;

            int trainedCharaId = trainedChara.trained_chara_id;
            TrainedCharaData charaData = null;
            if (_dataDic.TryGetValue(trainedCharaId, out charaData))
            {
                charaData.Update(trainedChara);
            }

            if (_allDataDic.TryGetValue(trainedCharaId, out charaData))
            {
                charaData.Update(trainedChara);
            }
        }

        /// <summary>
        /// リストの更新
        /// </summary>
        private void UpdateList()
        {
            if (_dataDic == null)
                return;

            var myCharaList = new List<TrainedCharaData>();
            foreach (var charaData in _dataDic.Values)
            {
                //自分のウマ娘だけをリストに追加する
                if (charaData.IsPlayer)
                {
                    myCharaList.Add(charaData);
                }
            }

            List = myCharaList;
        }

        /// <summary>
        /// 走法スタイルを更新する
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <param name="runningStyle"></param>
        public void UpdateRunningStyle(int trainedCharaId, int runningStyle)
        {
            TrainedCharaData charaData = null;
            if (_dataDic.TryGetValue(trainedCharaId, out charaData))
            {
                charaData.UpdateRunningStyle(runningStyle);
            }
        }

        /// <summary>
        /// お気に入り情報を更新
        /// </summary>
        /// <param name="favoriteArray"></param>
        public void UpdateFavoriteData(TrainedCharaFavorite[] favoriteArray)
        {
            if (favoriteArray == null)
            {
                return;
            }

            foreach (var newFavorite in favoriteArray)
            {
                if (_favoriteDataDict.TryGetValue(newFavorite.trained_chara_id, out var data))
                {
                    // Entityが既にあれば更新
                    data.Update(newFavorite);
                }
                else
                {
                    // Entityがなければ新規作成
                    _favoriteDataDict.Add(newFavorite.trained_chara_id, new FavoriteData(newFavorite));
                }
            }
        }

        /// <summary>
        /// お気に入り情報を取得
        /// </summary>
        /// <returns></returns>
        private FavoriteData GetFavoriteData(int trainedCharaId)
        {
            if (_favoriteDataDict.ContainsKey(trainedCharaId) == false)
            {
                var data = new FavoriteData(trainedCharaId, FavoriteData.FavoriteType.Default, String.Empty);
                _favoriteDataDict.Add(trainedCharaId, data);
                return data;
            }
            
            return _favoriteDataDict[trainedCharaId];
        }

        /// <summary>
        /// 殿堂入りウマ娘のお気に入り設定APIを送受信
        /// </summary>
        /// <param name="trainedCharaIdArray"></param>
        /// <param name="isLock"></param>
        /// <param name="favoriteType"></param>
        /// <param name="onSuccess"></param>
        public static void SendChangeLockApi(
            int[] trainedCharaIdArray,
            bool isLock,
            FavoriteData.FavoriteType favoriteType,
            Action<TrainedCharaChangeLockMultiResponse> onSuccess = null)
        {
            var req = new TrainedCharaChangeLockMultiRequest
            {
                trained_chara_id_array = trainedCharaIdArray,
                lock_flag = isLock ? 1 : 0,
                icon_type = (int) favoriteType
            };
            req.Send(onSuccess, stallOneSecond:true);
        }

        /// <summary>
        /// 殿堂入りウマ娘のメモ更新APIを送受信
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <param name="memo"></param>
        /// <param name="onSuccess"></param>
        public static void SendChangeMemoApi(
            int trainedCharaId,
            string memo,
            Action<TrainedCharaChangeMemoResponse> onSuccess = null)
        {
            var req = new TrainedCharaChangeMemoRequest
            {
                trained_chara_id = trainedCharaId,
                memo = memo
            };
            req.Send(onSuccess);
        }

        /// <summary>
        /// 登録枠拡張数更新
        /// </summary>
        public void UpdateExtendSaveNum(int extendSaveNum)
        {
            TrainedCharaExtendSaveNum = extendSaveNum;
        }
        
        #endregion

        #region Class

        /// <summary>
        /// 育成済みウマ娘データ
        /// </summary>
        public class TrainedCharaData
        {
            public enum UseType
            {
                None = 0, //自身のウマ娘、もしくは完全に他人のウマ娘など、特殊なフラグが立っていないキャラ
                Rental = 1, //育成のレンタルウマ娘
                Ghost = 2, //練習パートナー
            }
            
            /// <summary>
            /// 継承ウマ娘の世代
            /// </summary>
            public enum SuccessionCharaPosition
            {
                Self = 1,       // 自分自身
                First1 = 10,    // 1代(1)
                First2 = 20,    // 1代(2)
                Second1_1 = 11, // 2代(1-1)
                Second1_2 = 12, // 2代(1-2)
                Second2_1 = 21, // 2代(2-1)
                Second2_2 = 22, // 2代(2-2)
            }

            /// <summary>
            /// シナリオごとに保存される追加データ
            /// </summary>
            public class ScenarioAdditionalData
            {
                public RaceDefine.ScenarioAdditionalDataType Type { get; }
                public ObscuredInt Value { get; }

                public ScenarioAdditionalData(ScenarioData data)
                {
                    Type = (RaceDefine.ScenarioAdditionalDataType)data.type;
                    Value = data.value;
                }
                
            }
            
            /// <summary>
            /// 継承ウマ娘
            /// </summary>
            public class SuccessionCharaData
            {
                public class FactorData
                {
                    private class UpgradeHistory
                    {
                        public ObscuredInt FactorId { get; }
                        public ObscuredLong UpgradeDate { get; }

                        public UpgradeHistory(FactorExtend extend)
                        {
                            FactorId = extend.factor_id;
                            UpgradeDate = TimeUtil.ToUnixTimeFromJstString(extend.register_time);
                        }
                    }

                    public ObscuredInt FactorLv { get; }

                    /// <summary> 強化アイテムの使用等を加味した最新の因子Id </summary>
                    public ObscuredInt FactorId { get; }

                    /// <summary> 育成完了した際についたもともとの因子Id </summary>
                    private ObscuredInt _baseFactorId;

                    /// <summary> 因子強化履歴 </summary>
                    private List<UpgradeHistory> _upgradeHistoryList;

                    public FactorData(int factorId, int factorLv)
                    {
                        _baseFactorId = factorId;
                        _upgradeHistoryList = null;

                        FactorId = factorId;
                        FactorLv = factorLv;
                    }

                    public FactorData(int factorId, int factorLv, IEnumerable<FactorExtend> factorExtendEnumerable)
                    {
                        if (factorExtendEnumerable?.Count() <= 0)
                        {
                            // 強化履歴がない場合は、もともとの因子 = 最新の因子
                            _upgradeHistoryList = null;
                            FactorId = factorId;
                            _baseFactorId = factorId;
                            FactorLv = factorLv;
                            return;
                        }

                        _baseFactorId = factorId;
                        _upgradeHistoryList = factorExtendEnumerable
                            .Select(extend => new UpgradeHistory(extend))
                            .OrderByDescending(history => history.UpgradeDate) // 強化された日付が新しい順に並べる
                            .ToList();
                        // 最新の強化情報から、現在の因子Idを取ってくる
                        FactorId = _upgradeHistoryList.First().FactorId;
                        FactorLv = factorLv;
                    }

                    /// <summary>
                    /// 同じ因子か or 強化前は同じ因子だったかを判定する
                    /// </summary>
                    public bool IsSameOrUpgradedFactor(int factorId)
                    {
                        // いま同じ因子 or 獲得したタイミングでは同じ因子だったならtrue
                        if (FactorId == factorId || _baseFactorId == factorId)
                        {
                            return true;
                        }

                        // 強化履歴があれば、過去に同じ因子だった履歴があればTrue
                        if (!_upgradeHistoryList.IsNullOrEmpty())
                        {
                            return _upgradeHistoryList.Any(factor => factor.FactorId == factorId);
                        }

                        return false;
                    }

                    /// <summary>
                    /// 強化履歴を参照して、過去のある時刻での因子Idを取得する
                    /// </summary>
                    public int GetFactorIdAtRefTime(long refTime)
                    {
                        if (refTime <= 0 || _upgradeHistoryList.IsNullOrEmpty())
                        {
                            return FactorId; // 強化履歴が存在しない or 時間が不正なら最新の因子Idを返す
                        }

                        var latestUpgradeHistry = _upgradeHistoryList.FirstOrDefault(history => history.UpgradeDate <= refTime);
                        return latestUpgradeHistry?.FactorId ?? _baseFactorId;
                    }
                }
                private ObscuredInt _positionId;
                public SuccessionCharaPosition PositionId => (SuccessionCharaPosition)_positionId.GetDecrypted();
                public ObscuredInt CardId { get; }
                public int CharaId => MasterDataManager.Instance.masterCardData.Get(CardId).CharaId;
                public ObscuredInt Rarity { get; }
                public ObscuredInt Level { get; }
                private ObscuredInt _rank;
                public GameDefine.FinalTrainingRank Rank => (GameDefine.FinalTrainingRank)_rank.GetDecrypted();                
                /// <summary> 因子リスト </summary>
                public FactorData[] FactorDataArray { get; }
                /// <summary> ソート済み因子リスト </summary>
                private List<MasterSuccessionFactor.SuccessionFactor> _sortedFactorList;
                /// <summary> ソート済み因子リスト </summary>
                private List<MasterSuccessionFactor.SuccessionFactor> _sortedFactorListForProfileCard;
                /// <summary> 育成したユーザーID </summary>
                private ObscuredLong _ownerViewerId;
                public ObscuredLong OwnerViewerId{get{return _ownerViewerId;}}
                /// <summary> レンタルか </summary>
                public bool IsRental => _ownerViewerId != 0;
                /// <summary> 自分のウマ娘かどうか １世代目(userTrainedChara)の情報を使用する </summary>
                private bool _isPlayer;
                /// <summary> 自分のウマ娘かどうか </summary>
                public bool IsPlayer => _isPlayer;

                /// <summary>
                /// 勝鞍リスト
                /// </summary>
                public MasterSingleModeWinsSaddle.SingleModeWinsSaddle[] WinSaddleArray
                {
                    get
                    {
                        if (_winSaddleArray == null)
                        {
                            _winSaddleArray = WorkDataUtil.GetMasterSaddleArray(_winSaddleIdArray.Select(a=>a.GetDecrypted()).ToArray());
                        }
                        return _winSaddleArray;
                    }
                }
                private MasterSingleModeWinsSaddle.SingleModeWinsSaddle[] _winSaddleArray;
                private ObscuredInt[] _winSaddleIdArray;
                
                /// <summary>
                /// コンストラクタ
                /// </summary>
                public SuccessionCharaData(SuccessionChara serverData, FactorExtend[] factorExtendArray, bool isPlayer)
                {
                    _positionId = serverData.position_id;
                    CardId = serverData.card_id;
                    Rarity = serverData.rarity;
                    Level = serverData.talent_level;
                    _rank = serverData.rank;
                    _ownerViewerId = serverData.owner_viewer_id;

                    _isPlayer = isPlayer;

                    // 因子データ作成
                    if (FactorDataArray == null)
                    {
                        FactorDataArray = new FactorData[serverData.factor_info_array.Length];
                    }
                    for (int i = 0; i < serverData.factor_info_array.Length; i++)
                    {
                        if (!factorExtendArray.IsNullOrEmpty())
                        {
                            // 強化履歴があったら、対象の因子の強化履歴を収集して、FactorDataを生成する
                            var factorExtendEnumerable = factorExtendArray.Where(extend =>
                            {
                                var isOwnExtendInfo = extend.position_id == _positionId;
                                return isOwnExtendInfo && (extend.base_factor_id == serverData.factor_info_array[i].factor_id);
                            });
                            var extendedFactorData = new FactorData(serverData.factor_info_array[i].factor_id,
                                serverData.factor_info_array[i].level,
                                factorExtendEnumerable);
                            FactorDataArray[i] = extendedFactorData;

                            continue;
                        }

                        var factorData = new FactorData(serverData.factor_info_array[i].factor_id,
                            serverData.factor_info_array[i].level);
                        FactorDataArray[i] = factorData;
                    }

                    // 勝鞍
                    if (serverData.win_saddle_id_array != null)
                    {
                        _winSaddleIdArray = serverData.win_saddle_id_array.Select(a => (ObscuredInt) a).ToArray();
                    }
                }

                /// <summary>
                /// 因子をソートした状態で取得
                /// </summary>
                public List<MasterSuccessionFactor.SuccessionFactor> GetSortedFactorList()
                {
                    if(_sortedFactorList == null)
                    {
                        _sortedFactorList = CreateSortedFactorList(FactorDataArray);
                    }
                    return _sortedFactorList;
                }

                /// <summary>
                /// 因子をソートした状態で取得
                /// </summary>
                public List<MasterSuccessionFactor.SuccessionFactor> GetSortedFactorListForProfielCard()
                {
                    if (_sortedFactorListForProfileCard == null)
                    {
                        _sortedFactorListForProfileCard = CreateSortedFactorListProfileCard(FactorDataArray);
                    }
                    return _sortedFactorListForProfileCard;
                }

                /// <summary>
                /// 現在有効な因子の数を取得(期間内の因子)
                /// </summary>
                public int GetCountIntermFactor()
                {
                    if(_sortedFactorList == null)
                    {
                        _sortedFactorList = CreateSortedFactorList(FactorDataArray);
                    }
                    var intermFactorCount = 0;
                    for (int i = 0; i < _sortedFactorList.Count; i++)
                    {
                        // 因子データがない
                        if (_sortedFactorList[i] == null)
                        {
                            continue;
                        }

                        // レーシングカーニバルの因子の場合期間内且つ自分のウマ娘だけカウント
                        if (ChallengeMatchUtil.IsChallengeMatchBonusFactor(_sortedFactorList[i]))
                        {
                            // 期間外
                            if (!ChallengeMatchUtil.IsInTermFactor(_sortedFactorList[i]))
                            {
                                continue;
                            }

                            // 他のプレイヤーのウマ娘
                            if (!IsPlayer)
                            {
                                continue;
                            }
                        }

                        intermFactorCount++;
                    }

                    return intermFactorCount;
                }
            }
            
            private ObscuredInt _id;
            public int Id => _id;

            private ObscuredBool _isSaved;
            public bool IsSaved => _isSaved; //保存フラグ、falseの場合継承用に参照されるが、ユーザーが使用することはできない。
            private ObscuredLong _viewerId;
            public long ViewerId => _viewerId;
            private ObscuredLong _ownerViewerId;
            public ObscuredLong OwnerViewerId => _ownerViewerId;
            private ObscuredInt _ownerTrainedCharaId;
            public ObscuredInt OwnerTrainedCharaId => _ownerTrainedCharaId;
            private UseType _useType;

            
            private ObscuredInt _cardId;
            public int CardId => _cardId;

            public string Name { get => MasterCharaData.Name; }

            private ObscuredInt _nickNameId;
            public int NickNameId => _nickNameId;
            //#55623 トレーナー情報→ウマ娘詳細（ここで２つ名変更）→閉じてトレーナー情報に戻る→再度ウマ娘詳細を開くとニックネームが変わらないバグの対策
            public void SetNickNameId(int nickNameId) => _nickNameId = nickNameId;
            public string NickName
            {
                get
                {
                    if (NickNameId == 0) return string.Empty;
                    var data = MasterDataManager.Instance.masterNickname.Get(NickNameId);
                    return data != null ? data.Name : string.Empty;
                }
            }
            public MasterNickname.Nickname MasterNickname
            {
                get 
                { 
                    if (NickNameId == 0) return null;
                    return MasterDataManager.Instance.masterNickname.Get(NickNameId);
                }
            }
            public bool IsSetNickName => NickNameId != 0;

            private ObscuredInt[] _nickNameIdArray;
            public int[] AcquiredNickNameIdArray
            {
                get
                {
                    return _nickNameIdArray.Select(x => x.GetDecrypted()).ToArray();
                }
            }

            private ObscuredInt _stamina;
            public int Stamina => _stamina;
            private ObscuredInt _speed;
            public int Speed => _speed;
            private ObscuredInt _power;
            public int Power => _power;
            private ObscuredInt _guts;
            public int Guts => _guts;
            private ObscuredInt _wiz;
            public int Wiz => _wiz;

            private ObscuredInt _fans;
            public int Fans => _fans;
            private ObscuredInt _rank;
            public GameDefine.FinalTrainingRank Rank => (GameDefine.FinalTrainingRank)_rank.GetDecrypted();

            private ObscuredInt _rankScore;
            public int RankScore => _rankScore;

            private ObscuredInt _runningStyle;
            public RaceDefine.RunningStyle RunningStyle => (RaceDefine.RunningStyle)_runningStyle.GetDecrypted();

            // バ場適性
            private ObscuredInt _properGroundTurf;
            public RaceDefine.ProperGrade ProperGroundTurf => (RaceDefine.ProperGrade)_properGroundTurf.GetDecrypted();
            private ObscuredInt _properGroundDirt;
            public RaceDefine.ProperGrade ProperGroundDirt => (RaceDefine.ProperGrade)_properGroundDirt.GetDecrypted();

            // 距離適性
            private ObscuredInt _properDistanceShort;
            public RaceDefine.ProperGrade ProperDistanceShort => (RaceDefine.ProperGrade) _properDistanceShort.GetDecrypted();
            private ObscuredInt _properDistanceMile;
            public RaceDefine.ProperGrade ProperDistanceMile => (RaceDefine.ProperGrade) _properDistanceMile.GetDecrypted();
            private ObscuredInt _properDistanceMiddle;
            public RaceDefine.ProperGrade ProperDistanceMiddle => (RaceDefine.ProperGrade) _properDistanceMiddle.GetDecrypted();
            private ObscuredInt _properDistanceLong;
            public RaceDefine.ProperGrade ProperDistanceLong => (RaceDefine.ProperGrade) _properDistanceLong.GetDecrypted();

            // 脚質適性
            private ObscuredInt _properRunningStyleNige;
            public RaceDefine.ProperGrade ProperRunningStyleNige => (RaceDefine.ProperGrade)_properRunningStyleNige.GetDecrypted();
            private ObscuredInt _properRunningStyleSenko;
            public RaceDefine.ProperGrade ProperRunningStyleSenko => (RaceDefine.ProperGrade)_properRunningStyleSenko.GetDecrypted();
            private ObscuredInt _properRunningStyleSashi;
            public RaceDefine.ProperGrade ProperRunningStyleSashi => (RaceDefine.ProperGrade)_properRunningStyleSashi.GetDecrypted();
            private ObscuredInt _properRunningStyleOikomi;
            public RaceDefine.ProperGrade ProperRunningStyleOikomi => (RaceDefine.ProperGrade)_properRunningStyleOikomi.GetDecrypted();

            public ObscuredInt SuccessionCount {get; private set;}
            public SuccessionCharaData.FactorData[] FactorDataArray { get;private set; }
            public ObscuredString CreateTime { get; set; }
            public ObscuredInt ScenarioId { get; private set; }
            public ObscuredInt TalentLevel { get; private set; } = 1;
            public ObscuredInt CharaGrade { get; private set; }
            public ObscuredInt Rarity { get; private set; }
            public ScenarioAdditionalData[] ScenarioAdditionalDataArray { get; private set; }
            
            /// <summary>
            /// お気に入り中かどうか
            /// </summary>
            public ObscuredBool IsLock { get; set; }

            /// <summary>
            /// お気に入りタイプ
            /// </summary>
            public FavoriteData.FavoriteType FavoriteType
            {
                get
                {
                    if (_favoriteData == null)
                    {
                        _favoriteData = WorkDataManager.Instance.TrainedCharaData.GetFavoriteData(_id);
                    }

                    return _favoriteData?.Type ?? FavoriteData.FavoriteType.Default;
                }
                set
                {
                    if (_favoriteData == null)
                    {
                        _favoriteData = WorkDataManager.Instance.TrainedCharaData.GetFavoriteData(_id);
                    }

                    _favoriteData.Type = value;
                }
            }

            /// <summary>
            /// お気に入りタイプ
            /// </summary>
            public string Memo
            {
                get
                {
                    if (_favoriteData == null)
                    {
                        _favoriteData = WorkDataManager.Instance.TrainedCharaData.GetFavoriteData(_id);
                    }

                    return _favoriteData?.Memo ?? string.Empty;
                }
                set
                {
                    if (_favoriteData == null)
                    {
                        _favoriteData = WorkDataManager.Instance.TrainedCharaData.GetFavoriteData(_id);
                    }

                    _favoriteData.Memo = value;
                }
            }

            /// <summary>
            /// お気に入り情報
            /// </summary>
            private FavoriteData _favoriteData;

            private ObscuredLong _cachedCreateTimeTimeStamp = 0;
            private List<MasterSuccessionFactor.SuccessionFactor> _sortedFactorList;
            private List<MasterSuccessionFactor.SuccessionFactor> _sortedFactorProfileCardList;
            private List<MasterSuccessionFactor.SuccessionFactor> _factorListIncludingSuccession;

            /// <summary>
            /// 全国興行参加順
            /// </summary>
            public int JobEntryIndex { get; set; } = -1;

            /// <summary>
            /// 興行中か
            /// </summary>
            public ObscuredBool IsJob => JobEntryIndex != -1;

            /// <summary>
            /// 継承ウマ娘リスト
            /// </summary>
            public List<SuccessionCharaData> SuccessionCharaList { get; private set; } = new List<SuccessionCharaData>();

            /// <summary>
            /// 継承履歴リストが初期化済みかどうか
            /// </summary>
            public bool IsSuccessionHistoryInitialized = false;
            
            /// <summary>
            /// 継承履歴リスト
            /// </summary>
            public List<SuccessionHistory> SuccessionHistoryList { get; private set;} = new List<SuccessionHistory>();
            
            /// <summary>
            /// 継承ウマ１(1世代目）
            /// </summary>
            public SuccessionCharaData SuccessionChara1 => SuccessionCharaList.Find(a => a.PositionId == SuccessionCharaPosition.First1);
            
            /// <summary>
            /// 継承ウマ２(1世代目）
            /// </summary>
            public SuccessionCharaData SuccessionChara2 => SuccessionCharaList.Find(a => a.PositionId == SuccessionCharaPosition.First2);

            /// <summary>
            /// 作成時間を取得
            /// </summary>
            /// <returns></returns>
            public ObscuredLong GetCreateTimeStamp()
            {
                //ToUnixTimeでGCが発生してしまうので一度計算したらキャッシュしておく
                if (_cachedCreateTimeTimeStamp == 0)
                {
                    MakeCreateTimeStampCache();
                }
                return _cachedCreateTimeTimeStamp;
            }

            /// <summary>
            /// 衣装変更された衣装IDを取得
            /// </summary>
            /// <remarks>
            /// 衣装変更の仕様上自分が所有しているウマ娘の時しか値を返しません
            /// </remarks>
            /// <returns></returns>
            public int GetChangedModelDressId()
            {
                // 自分が所持しているウマ娘ないなら衣装変更を反映させない
                if (!IsPlayer)
                {
                    return 0;
                }

                return WorkDataManager.Instance.CardData.GetChangedModelDressId(_cardId);
            }

            /// <summary>
            /// レース衣装IDを取得
            /// </summary>
            /// <remarks> 今後の対応漏れを防ぐためにデフォルト引数は使用していません </remarks>
            /// <param name="isApplyDressChange"> 衣装変更を適用するかどうか(trueにすることで衣装変更機能で変更された衣装のIDが返されます) /param>
            /// <returns></returns>
            public int GetRaceDressId(bool isApplyDressChange)
            {
# if CYG_DEBUG
                if (DebugPageDressChange.IsForceCancelDressChange)
                {
                    isApplyDressChange = false;
                }
#endif

                // 衣装変更を適用するならそっちを返す
                if (isApplyDressChange)
                {
                    int changedModelDressId = GetChangedModelDressId();
                    if (changedModelDressId > 0)
                    {
                        return changedModelDressId;
                    }
                }

                var rarityData = MasterCardRarityData;
                if (rarityData != null)
                {
                    return rarityData.RaceDressId;
                }
                return 0;
            }

            /// <summary>
            /// レアリティ指定でレース衣装IDを取得
            /// </summary>
            /// <remarks>
            /// #137845 ☆2キャラが☆3衣装用のモーションを再生させるため作成
            /// </remarks>
            public int GetRaceDressIdWithRarity(GameDefine.CardRarity rarity)
            {
                var rarityData = MasterDataManager.Instance.masterCardRarityData.GetWithCardIdAndRarity(CardId, (int)rarity);
                if (rarityData != null)
                {
                    return rarityData.RaceDressId;
                }
                return 0;
            }

            private void MakeCreateTimeStampCache()
            {
                _cachedCreateTimeTimeStamp = TimeUtil.ToUnixTimeFromJstString(CreateTime);
            }

            /// <summary>
            /// 使用可能なスキル
            /// </summary>
            public WorkSkillData.AcquiredSkill[] AcquiredSkillArray { get; private set; }

            /// <summary>
            /// 育成時のフォト
            /// </summary>
            public SupportCardData[] SupportCardArray { get; private set; }

            /// <summary>
            /// シングルモードでのレース結果
            /// </summary>
            public SingleModeUtils.RaceHistoryInfo[] SingleModeRaceResultArray { get; private set; }

            /// <summary>
            /// 勝鞍リスト
            /// </summary>
            public MasterSingleModeWinsSaddle.SingleModeWinsSaddle[] WinSaddleArray
            {
                get
                {
                    if (_winSaddleArray == null)
                    {
                        _winSaddleArray = WorkDataUtil.GetMasterSaddleArray(_winSaddleIdArray.Select(a=>a.GetDecrypted()).ToArray());
                    }
                    return _winSaddleArray;
                }
            }
            private MasterSingleModeWinsSaddle.SingleModeWinsSaddle[] _winSaddleArray;
            private ObscuredInt[] _winSaddleIdArray;

            /// <summary>
            /// キャラID
            /// </summary>
            public ObscuredInt CharaId
            {
                get
                {
                    if (_cacheCharaId == INVALID_ID)
                    {
                        if (MasterCardData == null)
                        {
                            Debug.LogError("MasterCardDataが取得できません");
                            return INVALID_ID;
                        }

                        _cacheCharaId = MasterCardData.CharaId;
                    }
                    return _cacheCharaId;
                }
            }
            private ObscuredInt _cacheCharaId = INVALID_ID;

            /// <summary>
            /// カードのマスターデータ
            /// </summary>
            public MasterCardData.CardData MasterCardData
            {
                get
                {
                    if (_masterCardData == null)
                    {
                        if (MasterDataManager.Instance == null || MasterDataManager.Instance.masterCardData == null)
                        {
                            return null;
                        }

                        _masterCardData = MasterDataManager.Instance.masterCardData.Get(CardId);
                    }
                    return _masterCardData;
                }
            }
            private MasterCardData.CardData _masterCardData = null;

            /// <summary>
            /// キャラのマスターデータ
            /// </summary>
            public MasterCharaData.CharaData MasterCharaData
            {
                get
                {
                    if (_masterCharaData == null)
                    {
                        _masterCharaData = MasterDataManager.Instance.masterCharaData.Get(CharaId);
                    }
                    return _masterCharaData;
                }
            }
            private MasterCharaData.CharaData _masterCharaData = null;


            /// <summary>
            /// レアリティのマスターデータ
            /// </summary>
            public MasterCardRarityData.CardRarityData MasterCardRarityData
            {
                get
                {
                    if (_masterCardRarityData == null)
                    {
                        _masterCardRarityData = MasterDataManager.Instance.masterCardRarityData.GetWithCardIdAndRarity(CardId, Rarity);
                    }
                    return _masterCardRarityData;
                }
            }
            private MasterCardRarityData.CardRarityData _masterCardRarityData = null;
            
            /// <summary>
            /// シングルモードでの出走回数
            /// </summary>
            public int SingleTotalRaceNum { get; private set; }

            /// <summary>
            /// シングルモードでの勝利回数
            /// </summary>
            public int SingleWinNum
            {
                get
                {
                    if (_singleWinNum == -1 && SingleModeRaceResultArray != null)
                        CalcSingleWinNum();

                    return _singleWinNum;
                }
            }
            private ObscuredInt _singleWinNum = -1;
            
            /// <summary>
            /// プレイヤーのウマ娘か
            /// </summary>
            public bool IsPlayer => ViewerId == Certification.ViewerId && OwnerViewerId == MY_OWNER_VIEWER_ID;

            /// <summary>
            /// 練習パートナーか
            /// </summary>
            /// <remarks>used_typeのみで判定でもいいが、念のためViewerIdでの判定もいれておく</remarks>
            public bool IsGhost => ViewerId == Certification.ViewerId && OwnerViewerId != MY_OWNER_VIEWER_ID && _useType == UseType.Ghost;

            /// <summary>
            /// 育成のレンタルウマ娘かどうか
            /// </summary>
            /// <remarks>レンタル中はSavedのフラグが立った状態で殿堂入りウマ娘一覧として送られてくる.used_typeのみで判定でもいいが、念のためViewerIdでの判定もいれておく</remarks>
            public bool IsRental => ViewerId == Certification.ViewerId && OwnerViewerId != MY_OWNER_VIEWER_ID && _useType == UseType.Rental;

            /// <summary>
            /// 他人のウマ娘か
            /// </summary>
            public bool IsOthers => ViewerId != Certification.ViewerId;

            /// <summary>
            /// 殿堂入りウマ娘データアクセッサ
            /// </summary>
            public ITrainedCharaDataAccessor TrainedCharaDataAccessor { get; set; } = PlayerTrainedCharaDataAccessor.Instance;

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="serverData"></param>
            public TrainedCharaData(TrainedChara serverData)
            {
                Setup(serverData);
            }

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="serverData"></param>
            public TrainedCharaData(UserTrainedCharaAtFriend serverData, SuccessionChara[] successionCharaArray = null)
            {
                Setup(serverData, successionCharaArray);
            }

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="serverData"></param>
            protected TrainedCharaData(RoomMatchTrainedChara serverData)
            {
                Setup(serverData);
            }

            /// <summary>
            /// データ再更新
            /// </summary>
            /// <param name="serverData"></param>
            public void Update(TrainedChara serverData)
            {
                ClearMasterDataCache();
                Setup(serverData);
            }

            public void SetSuccessionCharaDataList(List<SuccessionCharaData> successionCharaDataList)
            {
                SuccessionCharaList = successionCharaDataList;
            }

            /// <summary>
            /// マスターのキャッシュをクリア
            /// </summary>
            private void ClearMasterDataCache()
            {
                _masterCardData = null;
                _masterCharaData = null;
                _masterCardRarityData = null;
            }

            /// <summary>
            /// データセットアップ
            /// </summary>
            /// <param name="serverData"></param>
            private void Setup(TrainedChara serverData)
            {
                if (serverData == null) return;
                _isSaved = serverData.is_saved == 1;
                _viewerId = serverData.viewer_id;
                _ownerViewerId = serverData.owner_viewer_id;
                _ownerTrainedCharaId = serverData.owner_trained_chara_id;
                _useType = (UseType)serverData.use_type;
                _id = serverData.trained_chara_id;
                _cardId = serverData.card_id;
                _nickNameId = serverData.nickname_id;
                if (serverData.nickname_id_array != null)
                {
                    _nickNameIdArray = serverData.nickname_id_array.Select(x => (ObscuredInt)x).ToArray();
                }
                else
                {
                    _nickNameIdArray = new ObscuredInt[0];
                    Debug.LogError("TrainedCharaData : nickname_id_array is null.");
                }
                _stamina = serverData.stamina;
                _speed = serverData.speed;
                _power = serverData.power;
                _guts = serverData.guts;
                _wiz = serverData.wiz;
                _fans = serverData.fans;
                _rankScore = serverData.rank_score;
                _rank = serverData.rank;
                _runningStyle = serverData.running_style;
                _properDistanceShort = serverData.proper_distance_short;
                _properDistanceMile = serverData.proper_distance_mile;
                _properDistanceMiddle = serverData.proper_distance_middle;
                _properDistanceLong = serverData.proper_distance_long;
                _properRunningStyleNige = serverData.proper_running_style_nige;
                _properRunningStyleSenko = serverData.proper_running_style_senko;
                _properRunningStyleSashi = serverData.proper_running_style_sashi;
                _properRunningStyleOikomi = serverData.proper_running_style_oikomi;
                _properGroundTurf = serverData.proper_ground_turf;
                _properGroundDirt = serverData.proper_ground_dirt;
                SuccessionCount = serverData.succession_num;
                
                // 因子
                SetupFactor(serverData.factor_info_array, serverData.factor_extend_array);
                
                // 継承ウマ娘
                SuccessionCharaList.Clear();
                if (serverData.succession_chara_array != null)
                {
                    foreach (var successionChara in serverData.succession_chara_array)
                    {
                        SuccessionCharaList.Add(new SuccessionCharaData(successionChara, serverData.factor_extend_array, IsPlayer));
                    }
                }

                TalentLevel = serverData.talent_level;
                CharaGrade = serverData.chara_grade;
                Rarity = serverData.rarity;
                CreateTime = serverData.create_time;
                ScenarioId = serverData.scenario_id != 0 ? serverData.scenario_id : 1; // 旧データはシナリオIDがないのでID1とする
                IsLock = serverData.is_locked > 0;

                // シナリオごとの追加データ
                if (serverData.scenario_data_array != null)
                {
                    ScenarioAdditionalDataArray = new ScenarioAdditionalData[serverData.scenario_data_array.Length];
                    for (var i = 0; i < serverData.scenario_data_array.Length; i++)
                    {
                        ScenarioAdditionalDataArray[i] = new ScenarioAdditionalData(serverData.scenario_data_array[i]);
                    }
                }

                //獲得スキル
                AcquiredSkillArray = serverData.skill_array == null
                    ? new WorkSkillData.AcquiredSkill[0]
                    : WorkSkillData.AcquiredSkill.Convert(serverData.skill_array);

                //フォト
                if (serverData.support_card_list == null)
                {
                    SupportCardArray = new SupportCardData[0];
                }
                else
                {
                    SupportCardArray = new SupportCardData[serverData.support_card_list.Length];
                    for (int i = 0; i < SupportCardArray.Length; i++)
                    {
                        SupportCardArray[i] = new SupportCardData(serverData.support_card_list[i]);
                    }
                }

                //戦績
                if (serverData.race_result_list != null)
                {
                    SingleModeRaceResultArray = new SingleModeUtils.RaceHistoryInfo[serverData.race_result_list.Length];
                    for (int i = 0; i < serverData.race_result_list.Length; i++)
                    {
                        SingleModeRaceResultArray[i] = new SingleModeUtils.RaceHistoryInfo(serverData.race_result_list[i], ScenarioId);
                    }
                }
                else
                {
                    SingleModeRaceResultArray = new SingleModeUtils.RaceHistoryInfo[0];
                }
                SingleTotalRaceNum = SingleModeRaceResultArray.Length;

                // 勝鞍
                if (serverData.win_saddle_id_array != null)
                {
                    _winSaddleIdArray = serverData.win_saddle_id_array.Select(a => (ObscuredInt) a).ToArray();
                }
                else
                {
                    _winSaddleIdArray = new ObscuredInt[0];
                    Debug.LogError("TrainedCharaData : win_saddle_id_array is null.");
                }
            }

            /// <summary>
            /// フレンド情報用セットアップ
            /// </summary>
            /// <param name="serverData"></param>
            private void Setup(UserTrainedCharaAtFriend serverData, SuccessionChara[] successionCharaArray)
            {
                if(serverData == null)
                {
                    return;
                }

                _viewerId = serverData.viewer_id;
                _cardId = serverData.card_id;
                _useType = UseType.None; //フレンドはUseTypeは0で固定
                _rankScore = serverData.rank_score;
                _rank = serverData.rank;
                _properDistanceShort = serverData.proper_distance_short;
                _properDistanceMile = serverData.proper_distance_mile;
                _properDistanceMiddle = serverData.proper_distance_middle;
                _properDistanceLong = serverData.proper_distance_long;
                _properRunningStyleNige = serverData.proper_running_style_nige;
                _properRunningStyleSenko = serverData.proper_running_style_senko;
                _properRunningStyleSashi = serverData.proper_running_style_sashi;
                _properRunningStyleOikomi = serverData.proper_running_style_oikomi;
                _properGroundTurf = serverData.proper_ground_turf;
                _properGroundDirt = serverData.proper_ground_dirt;
                Rarity = serverData.rarity;
                TalentLevel = serverData.talent_level;
                CreateTime = serverData.register_time;
                
                // 因子
                SetupFactor(serverData.factor_info_array, serverData.factor_extend_array);

                // UserTrainedCharaAtFriendにはデータが含まれていないので空データを渡す
                // 継承ウマ娘
                SuccessionCharaList.Clear();
                // フレンド画面ではSuccessionCharaArrayが別で渡されるため、そのデータを使って継承ウマ娘を設定する
                if (successionCharaArray != null)
                {
                    foreach (var successionChara in successionCharaArray)
                    {
                        SuccessionCharaList.Add(new SuccessionCharaData(successionChara, serverData.factor_extend_array, IsPlayer));
                    }
                }

                // 獲得スキル
                AcquiredSkillArray = new WorkSkillData.AcquiredSkill[serverData.skill_count];
                
                // サポカ一覧
                SupportCardArray = new SupportCardData[0];

                // 戦績
                SingleModeRaceResultArray = new SingleModeUtils.RaceHistoryInfo[0];
                SingleTotalRaceNum = 0;

                // 勝鞍
                _winSaddleIdArray = new ObscuredInt[0];
            }

            /// <summary>
            /// データセットアップ(ルームマッチ)
            /// </summary>
            /// <param name="serverData"></param>
            private void Setup(RoomMatchTrainedChara serverData)
            {
                if (serverData == null) return;
                _isSaved = serverData.is_saved == 1;
                _viewerId = serverData.viewer_id;
                _ownerViewerId = serverData.owner_viewer_id;
                _useType = (UseType)serverData.use_type;
                _id = serverData.trained_chara_id;
                _cardId = serverData.card_id;
                _nickNameId = serverData.nickname_id;
                _nickNameIdArray = serverData.nickname_id_array.Select(x => (ObscuredInt)x).ToArray();
                _stamina = serverData.stamina;
                _speed = serverData.speed;
                _power = serverData.power;
                _guts = serverData.guts;
                _wiz = serverData.wiz;
                _fans = serverData.fans;
                _rankScore = serverData.rank_score;
                _rank = serverData.rank;
                _runningStyle = serverData.running_style;
                _properDistanceShort = serverData.proper_distance_short;
                _properDistanceMile = serverData.proper_distance_mile;
                _properDistanceMiddle = serverData.proper_distance_middle;
                _properDistanceLong = serverData.proper_distance_long;
                _properRunningStyleNige = serverData.proper_running_style_nige;
                _properRunningStyleSenko = serverData.proper_running_style_senko;
                _properRunningStyleSashi = serverData.proper_running_style_sashi;
                _properRunningStyleOikomi = serverData.proper_running_style_oikomi;
                _properGroundTurf = serverData.proper_ground_turf;
                _properGroundDirt = serverData.proper_ground_dirt;
                SuccessionCount = serverData.succession_num;

                // 因子
                SetupFactor(serverData.factor_info_array, serverData.factor_extend_array);

                // 継承ウマ娘
                SuccessionCharaList.Clear();
                if (serverData.succession_chara_array != null)
                {
                    foreach (var successionChara in serverData.succession_chara_array)
                    {
                        SuccessionCharaList.Add(new SuccessionCharaData(successionChara, serverData.factor_extend_array, IsPlayer));
                    }
                }

                TalentLevel = serverData.talent_level;
                CharaGrade = serverData.chara_grade;
                Rarity = serverData.rarity;
                CreateTime = serverData.create_time;
                ScenarioId = serverData.scenario_id != 0 ? serverData.scenario_id : 1; // 旧データはシナリオIDがないのでID1とする
                IsLock = serverData.is_locked > 0;
                
                // シナリオごとの追加データ
                if (serverData.scenario_data_array != null)
                {
                    ScenarioAdditionalDataArray = new ScenarioAdditionalData[serverData.scenario_data_array.Length];
                    for (var i = 0; i < serverData.scenario_data_array.Length; i++)
                    {
                        ScenarioAdditionalDataArray[i] = new ScenarioAdditionalData(serverData.scenario_data_array[i]);
                    }
                }

                //獲得スキル
                AcquiredSkillArray = serverData.skill_array == null
                    ? new WorkSkillData.AcquiredSkill[0]
                    : WorkSkillData.AcquiredSkill.Convert(serverData.skill_array);

                //フォト
                if (serverData.support_card_list == null)
                {
                    SupportCardArray = new SupportCardData[0];
                }
                else
                {
                    SupportCardArray = new SupportCardData[serverData.support_card_list.Length];
                    for (int i = 0; i < SupportCardArray.Length; i++)
                    {
                        SupportCardArray[i] = new SupportCardData(serverData.support_card_list[i]);
                    }
                }

                //戦績
                if (serverData.race_result_list != null)
                {
                    SingleModeRaceResultArray = new SingleModeUtils.RaceHistoryInfo[serverData.race_result_list.Length];
                    for (int i = 0; i < serverData.race_result_list.Length; i++)
                    {
                        SingleModeRaceResultArray[i] = new SingleModeUtils.RaceHistoryInfo(serverData.race_result_list[i], ScenarioId);
                    }
                }
                else
                {
                    SingleModeRaceResultArray = new SingleModeUtils.RaceHistoryInfo[0];
                }
                SingleTotalRaceNum = SingleModeRaceResultArray.Length;

                // 勝鞍
                _winSaddleIdArray = serverData.win_saddle_id_array.Select(a => (ObscuredInt)a).ToArray();
            }

            /// <summary>
            /// 因子のセットアップ
            /// </summary>
            /// <param name="factorInfoArray"></param>
            private void SetupFactor(FactorInfo[] factorInfoArray, FactorExtend[] factorExtendArray)
            {
                if (factorInfoArray == null)
                {
                    FactorDataArray = new SuccessionCharaData.FactorData[0];
                }
                else if (factorExtendArray.IsNullOrEmpty())
                {
                    FactorDataArray = new SuccessionCharaData.FactorData[factorInfoArray.Length];
                    for (int i = 0; i < factorInfoArray.Length; i++)
                    {
                        var factorData = new SuccessionCharaData.FactorData(factorInfoArray[i].factor_id, factorInfoArray[i].level);
                        FactorDataArray[i] = factorData;
                    }
                }
                else
                {
                    FactorDataArray = new SuccessionCharaData.FactorData[factorInfoArray.Length];
                    for (int i = 0; i < factorInfoArray.Length; i++)
                    {
                        // 対象の因子の強化履歴を収集して、FactorDataを生成する
                        var factorUpgradeData = factorExtendArray.Where(extend =>
                        {
                            var isOwnExtendInfo = extend.position_id == (int)SuccessionCharaPosition.Self;
                            return isOwnExtendInfo && (extend.base_factor_id == factorInfoArray[i].factor_id);
                        });
                        var factorData = new SuccessionCharaData.FactorData(factorInfoArray[i].factor_id, factorInfoArray[i].level, factorUpgradeData);
                        FactorDataArray[i] = factorData;
                    }
                }

                //キャッシュされている因子リストはクリアしておく
                if (_sortedFactorList != null)
                {
                    _sortedFactorList.Clear();
                    _sortedFactorList = null;
                }

                if (_sortedFactorProfileCardList != null)
                {
                    _sortedFactorProfileCardList.Clear();
                    _sortedFactorProfileCardList = null;
                }

                if (_factorListIncludingSuccession != null)
                {
                    _factorListIncludingSuccession.Clear();
                    _factorListIncludingSuccession = null;
                }
            }
            
            /// <summary>
            /// 因子のセットアップ(10004500より前)
            /// </summary>
            /// <param name="factorIdArray"></param>
            private void SetupFactor(int[] factorIdArray)
            {
                if (factorIdArray == null)
                {
                    FactorDataArray = new SuccessionCharaData.FactorData[0];
                }
                else
                {
                    FactorDataArray = new SuccessionCharaData.FactorData[factorIdArray.Length];
                    for (int i = 0; i < factorIdArray.Length; i++)
                    {
                        FactorDataArray[i] = new SuccessionCharaData.FactorData(factorIdArray[i], 0);
                    }
                }
            }

            /// <summary>
            /// 走法スタイルを更新する
            /// </summary>
            /// <param name="runningStyle"></param>
            public void UpdateRunningStyle(int runningStyle)
            {
                _runningStyle = runningStyle;
            }
            
            /// <summary>
            /// 継承履歴リストを更新する
            /// </summary>
            public void UpdateSuccessionHistoryList(TrainedCharaGetSuccessionHistoryArrayResponse response)
            {
                SuccessionHistoryList.Clear();
                if (response?.data?.succession_history_array == null) return;

                IsSuccessionHistoryInitialized = true;
                SuccessionHistoryList.AddRange(response.data.succession_history_array);
            }

            /// <summary>
            /// 二つ名を追加する
            /// （※作成済みの殿堂入りウマ娘に対して【後から二つ名を追加】したい場合に使う関数です。（例えばレース系イベントで、出走させた殿堂入りウマ娘に限定二つ名を付与したい場合など。））
            /// （※サーバーとの整合性が取れる事をサーバーエンジニアさんとすり合わせた上で使用してください。）
            /// </summary>
            public void AddNickName(int addNickNameId)
            {
                if (addNickNameId == 0)
                    return;

                if (_nickNameIdArray.Contains(addNickNameId))
                    return; // 既に持っている二つ名は追加できない

                if (_nickNameIdArray != null)
                {
                    Array.Resize(ref _nickNameIdArray, _nickNameIdArray.Length + 1);
                    _nickNameIdArray[_nickNameIdArray.Length - 1] = addNickNameId;
                }
                else
                {
                    _nickNameIdArray = new ObscuredInt[1] { addNickNameId };
                }
            }

            /// <summary>
            /// Enum指定でパラメータを取得する
            /// </summary>
            /// <param name="paramType"></param>
            /// <returns></returns>
            public int GetParam(GameDefine.ParameterType paramType)
            {
                switch (paramType)
                {
                    case GameDefine.ParameterType.Speed: return Speed;
                    case GameDefine.ParameterType.Stamina: return Stamina;
                    case GameDefine.ParameterType.Power: return Power;
                    case GameDefine.ParameterType.Guts: return Guts;
                    case GameDefine.ParameterType.Wiz: return Wiz;
                }
                return 0;
            }

            /// <summary>
            /// キャラの最大パラメータ取得
            /// </summary>
            public Dictionary<GameDefine.ParameterType, int> GetMaxParameterDic()
            {
                Dictionary<GameDefine.ParameterType, int> outDic = new Dictionary<GameDefine.ParameterType, int>();
                var masterCardData = MasterDataManager.Instance.masterCardData.Get(_cardId);
                if (masterCardData == null) return outDic;

                MasterDataUtil.CalcCardMaxStatus(masterCardData, Rarity, out int maxSpeed, out int maxStamina, out int maxPower, out int maxGuts, out int maxWiz);
                outDic[GameDefine.ParameterType.Speed] = maxSpeed;
                outDic[GameDefine.ParameterType.Stamina] = maxStamina;
                outDic[GameDefine.ParameterType.Power] = maxPower;
                outDic[GameDefine.ParameterType.Guts] = maxGuts;
                outDic[GameDefine.ParameterType.Wiz] = maxWiz;

                return outDic;
            }

            /// <summary>
            /// シングルモードでの勝利回数の計算
            /// </summary>
            private void CalcSingleWinNum()
            {
                _singleWinNum = 0;
                for (int i = 0; i < SingleTotalRaceNum; i++)
                {
                    var resultData = SingleModeRaceResultArray[i];
                    if (resultData.ResultRank == SingleModeDefine.RACE_RANK_FIRST)
                    {
                        _singleWinNum++;
                    }
                }
            }

            /// <summary>
            /// singleModeProgramIdに勝利した数を計算。
            /// </summary>
            public int CalcSingleModeProgramIdWinNum(int singleModeProgramId)
            {
                int retCnt = 0;
                
                for (int i = 0; i < SingleTotalRaceNum; i++)
                {
                    var resultData = SingleModeRaceResultArray[i];
                    if (resultData.ResultRank == SingleModeDefine.RACE_RANK_FIRST)
                    {
                        var masterProgram = MasterDataManager.Instance.masterSingleModeProgram.Get(resultData.ProgramId);
                        if (masterProgram != null)
                        {
                            // csvにbase_program_idが入力されている場合は、それとの比較を行う。
                            if (masterProgram.BaseProgramId > 0)
                            {
                                if (masterProgram.BaseProgramId == singleModeProgramId)
                                {
                                    ++retCnt;
                                }
                            }
                            // そうでなければ単純にresultDataのprogramIdとの比較を行う。
                            else if (resultData.ProgramId == singleModeProgramId) 
                            {
                                ++retCnt;
                            }
                            
                        }
                    }
                }

                return retCnt;
            }

            /// <summary>
            /// 因子をソートした状態で取得
            /// </summary>
            /// <returns></returns>
            public List<MasterSuccessionFactor.SuccessionFactor> GetSortedFactorList()
            {
                if (_sortedFactorList == null)
                {
                    _sortedFactorList = CreateSortedFactorList(FactorDataArray);
                }

                // 育成の進行中に継承ウマ娘の因子を取得する場合は、育成開始時点での因子を参照する
                var workSingleMode = WorkDataManager.Instance.SingleMode;
                if (workSingleMode.IsPlaying && workSingleMode.Character != null)
                {
                    var isSuccession = Id == workSingleMode.Character.SuccessionTrainedCharaInfoFirst.TrainedCharaId
                                    || Id == workSingleMode.Character.SuccessionTrainedCharaInfoSecond.TrainedCharaId;

                    if (isSuccession)
                    {
                        var singleModeStartTime = TimeUtil.ToUnixTimeFromJstString(workSingleMode.Character.StartTime);
                        return GetSortedFactorListAtRefTime(singleModeStartTime);
                    }
                }

                return _sortedFactorList;
            }

            /// <summary>
            /// ある時刻での殿堂入りウマ娘の因子一覧をソートした状態で取得
            /// </summary>
            private List<MasterSuccessionFactor.SuccessionFactor> GetSortedFactorListAtRefTime(long refTime)
            {
                if (_sortedFactorList == null)
                {
                    _sortedFactorList = CreateSortedFactorList(FactorDataArray);
                }

                if (refTime == 0)
                {
                    return _sortedFactorList;
                }

                var sortedFactorListReferenceTime = _sortedFactorList
                    .Select(GetFactorAtRefTime)
                    .ToList();

                return sortedFactorListReferenceTime;

                // 引数で渡した因子が、過去のある時間にどの因子だったかを取得
                MasterSuccessionFactor.SuccessionFactor GetFactorAtRefTime(MasterSuccessionFactor.SuccessionFactor successionFactor)
                {
                    if (successionFactor == null)
                    {
                        return null;
                    }

                    var factorData = FactorDataArray.FirstOrDefault(factorData => factorData.IsSameOrUpgradedFactor(successionFactor.FactorId));
                    if (factorData == null)
                    {
                        Debug.LogError("FactorDataArrayにない因子のデータがsortedFactorListにあります");
                        return successionFactor;
                    }

                    var factorIdAtRefTime = factorData.GetFactorIdAtRefTime(refTime);
                    if (successionFactor.FactorId == factorIdAtRefTime)
                    {
                        // 現在と因子が変わっていなければそのまま返す
                        return successionFactor;
                    }

                    return MasterDataManager.Instance.masterSuccessionFactor.Get(factorIdAtRefTime);
                }
            }

            /// <summary>
            /// 現在有効な因子の数を取得(期間内の因子)
            /// </summary>
            public int GetCountIntermFactor()
            {
                if(_sortedFactorList == null)
                {
                    _sortedFactorList = CreateSortedFactorList(FactorDataArray);
                }
                var intermFactorCount = 0;
                for (int i = 0; i < _sortedFactorList.Count; i++)
                {
                    // 因子データがない
                    if (_sortedFactorList[i] == null)
                    {
                        continue;
                    }

                    // レーシングカーニバルの因子の場合期間内且つ自分のウマ娘だけカウント
                    if (ChallengeMatchUtil.IsChallengeMatchBonusFactor(_sortedFactorList[i]))
                    {
                        // 期間外
                        if (!ChallengeMatchUtil.IsInTermFactor(_sortedFactorList[i]))
                        {
                            continue;
                        }

                        // 他のプレイヤーのウマ娘
                        if (!IsPlayer)
                        {
                            continue;
                        }
                    }

                    intermFactorCount++;
                }

                return intermFactorCount;
            }
            
            public static List<MasterSuccessionFactor.SuccessionFactor> CreateSortedFactorList(SuccessionCharaData.FactorData[] factorDataArray)
            {
                var sortedFactorList = new List<MasterSuccessionFactor.SuccessionFactor>();
                
                // 入力がnullなら空で返却
                if (factorDataArray == null)
                {
                    return sortedFactorList;
                }
                
                var tempList = factorDataArray.Select(factor => MasterDataManager.Instance.masterSuccessionFactor.Get(factor.FactorId));

                //基礎値,適性,キャラ因子の順番で追加 nullが入る可能性があるのは問題ない
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARAM));
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PROPER));
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_CHARA));
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && ChallengeMatchUtil.IsChallengeMatchBonusFactor(f) && ChallengeMatchUtil.IsInTermFactor(f)));
                
                //残りはサブ因子(チャレンジボーナスはさっき追加したので省く)
                sortedFactorList.AddRange(tempList.Where(f => f != null &&
                                                              f.FactorType > MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_CHARA && 
                                                              f.FactorType != MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_MATCH_BONUS));
                return sortedFactorList;
            }

            /// <summary>
            /// 因子をソートした状態で取得(名刺向け)
            /// </summary>
            /// <returns></returns>
            public List<MasterSuccessionFactor.SuccessionFactor> GetSortedFactorListForProfileCard()
            {
                if (_sortedFactorProfileCardList == null)
                {
                    _sortedFactorProfileCardList = CreateSortedFactorListProfileCard(FactorDataArray);
                }
                return _sortedFactorProfileCardList;
            }

            private static List<MasterSuccessionFactor.SuccessionFactor> CreateSortedFactorListProfileCard(SuccessionCharaData.FactorData[] factorDataArray)
            {
                var sortedFactorList = new List<MasterSuccessionFactor.SuccessionFactor>();

                // 入力がnullなら空で返却
                if (factorDataArray == null)
                {
                    return sortedFactorList;
                }

                var tempList = factorDataArray.Select(factor => MasterDataManager.Instance.masterSuccessionFactor.Get(factor.FactorId));

                //基礎値,適性,キャラの順番で追加
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARAM));
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PROPER));
                sortedFactorList.Add(tempList.FirstOrDefault(f => f != null && f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_CHARA));

                //レース、スキルに関しては空きがあれば追加という形なので列挙
                //星の数が大きい順、同星数であればIDが若い順から.
                //#124024 シナリオ因子が複数になったため列挙で取得できるように調整
                sortedFactorList.AddRange
                (
                    tempList
                        .Where
                        (f =>
                            f != null &&
                            (f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_SKILL ||
                             f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_RACE ||
                             f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_SEANARIO ||
                             f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_SPECIAL_CONDITION ||
                             f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARENT_STAR ||
                             f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARENT_STAR_AD1 ||
                             f.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARENT_STAR_AD2)
                        )
                        // シナリオ,レース,スキルの中でシナリオ因子が最優先に並んでほしいため
                        .OrderByDescending(x => x.FactorType == MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_SEANARIO)
                        .ThenByDescending(f => f.Rarity)
                );

                // NULLが2個以上あると空欄を埋める際に厄介なのでNULLはリストから除外しておく.
                for(int i= sortedFactorList.Count-1; i>0; --i)
                {
                    if (sortedFactorList[i] == null)
                    {
                        sortedFactorList.RemoveAt(i);
                    }
                }
                return sortedFactorList;
            }

            /// <summary>
            /// 因子を取得（継承元の因子も含む、ソートはしていない）
            /// </summary>
            /// <returns></returns>
            public List<MasterSuccessionFactor.SuccessionFactor> GetFactorListIncludingSuccession()
            {
                if(_factorListIncludingSuccession == null)
                {
                    _factorListIncludingSuccession = CreateSortedFactorList(FactorDataArray);
                    
                    if (SuccessionChara1 != null)
                    {
                        _factorListIncludingSuccession.AddRange(CreateSortedFactorList(SuccessionChara1.FactorDataArray));
                    }

                    if (SuccessionChara2 != null)
                    {
                        _factorListIncludingSuccession.AddRange(CreateSortedFactorList(SuccessionChara2.FactorDataArray));   
                    }
                }
                
                return _factorListIncludingSuccession;
            }
            
            /// <summary>
            /// 等価判定: Id + ViewId で等価とみなす
            /// </summary>
            /// <param name="obj"></param>
            /// <returns></returns>
            public bool CheckEquals(object obj)
            {
                if (obj is TrainedCharaData trainedCharaData)
                {
                    return Id == trainedCharaData.Id && ViewerId == trainedCharaData.ViewerId;
                }
                return false;
            }
            
            #region ソート関連

            /// <summary>
            /// バ場適性の最大値を取得
            /// </summary>
            /// <param name="priorToSmallEnum">同値があった場合に小さい方を優先するか</param>
            /// <returns></returns>
            public RaceDefine.GroundType GetMaxProperGroundType(bool priorToSmallEnum)
            {
                var turf = ProperGroundTurf;
                var dirt = ProperGroundDirt;
                if (turf == dirt)
                    return priorToSmallEnum ? RaceDefine.GroundType.Turf : RaceDefine.GroundType.Dirt;

                return turf > dirt ? RaceDefine.GroundType.Turf : RaceDefine.GroundType.Dirt;
            }

            /// <summary>
            /// 馬場適性を取得
            /// </summary>
            /// <param name="groundType"></param>
            /// <returns></returns>
            public int GetProperGround(RaceDefine.GroundType groundType)
            {
                switch (groundType)
                {
                    case RaceDefine.GroundType.Turf:
                        return (int) ProperGroundTurf;
                    case RaceDefine.GroundType.Dirt:
                        return (int) ProperGroundDirt;
                }

                return int.MinValue;
            }

            /// <summary>
            /// 距離適性の最大値を取得
            /// </summary>
            /// <param name="priorToSmallEnum">同値があった場合に小さい方を優先するか</param>
            /// <returns></returns>
            public RaceDefine.CourseDistanceType GetMaxProperDistanceType(bool priorToSmallEnum)
            {
                var maxProperDistanceType = RaceDefine.CourseDistanceType.Short;
                var maxGrade = RaceDefine.ProperGrade.Null;
                for (
                    RaceDefine.CourseDistanceType i = priorToSmallEnum ? RaceDefine.CourseDistanceType.Short : RaceDefine.CourseDistanceType.Long;
                    priorToSmallEnum ? i <= RaceDefine.CourseDistanceType.Long : i >= RaceDefine.CourseDistanceType.Short;
                    i = priorToSmallEnum ? i + 1 : i - 1
                )
                {
                    switch (i)
                    {
                        case RaceDefine.CourseDistanceType.Short:
                            var shortGrade = ProperDistanceShort;
                            if (shortGrade > maxGrade)
                            {
                                maxProperDistanceType = RaceDefine.CourseDistanceType.Short;
                                maxGrade = shortGrade;
                            }

                            break;
                        case RaceDefine.CourseDistanceType.Mile:
                            var mileGrade = ProperDistanceMile;
                            if (mileGrade > maxGrade)
                            {
                                maxProperDistanceType = RaceDefine.CourseDistanceType.Mile;
                                maxGrade = mileGrade;
                            }

                            break;
                        case RaceDefine.CourseDistanceType.Middle:
                            var middleGrade = ProperDistanceMiddle;
                            if (middleGrade > maxGrade)
                            {
                                maxProperDistanceType = RaceDefine.CourseDistanceType.Middle;
                                maxGrade = middleGrade;
                            }

                            break;
                        case RaceDefine.CourseDistanceType.Long:
                            var longGrade = ProperDistanceLong;
                            if (longGrade > maxGrade)
                            {
                                maxProperDistanceType = RaceDefine.CourseDistanceType.Long;
                                maxGrade = longGrade;
                            }

                            break;
                    }
                }

                return maxProperDistanceType;
            }

            /// <summary>
            /// 距離適性を取得
            /// </summary>
            /// <param name="distanceType"></param>
            /// <returns></returns>
            public int GetProperDistance(RaceDefine.CourseDistanceType distanceType)
            {
                switch (distanceType)
                {
                    case RaceDefine.CourseDistanceType.Short:
                        return (int) ProperDistanceShort;
                    case RaceDefine.CourseDistanceType.Mile:
                        return (int) ProperDistanceMile;
                    case RaceDefine.CourseDistanceType.Middle:
                        return (int) ProperDistanceMiddle;
                    case RaceDefine.CourseDistanceType.Long:
                        return (int) ProperDistanceLong;
                }

                return int.MinValue;
            }

            /// <summary>
            /// 脚質適性の最大値を取得
            /// </summary>
            /// <param name="priorToSmallEnum">同値があった場合に小さい方を優先するか</param>
            /// <returns></returns>
            public RaceDefine.RunningStyle GetMaxProperRunningStyle(bool priorToSmallEnum)
            {
                var maxProperRunningStyle = RaceDefine.RunningStyle.None;
                var maxGrade = RaceDefine.ProperGrade.Null;
                for (
                    RaceDefine.RunningStyle i = priorToSmallEnum ? RaceDefine.RunningStyle.Nige : RaceDefine.RunningStyle.Oikomi;
                    priorToSmallEnum ? i <= RaceDefine.RunningStyle.Oikomi : i >= RaceDefine.RunningStyle.Nige;
                    i = priorToSmallEnum ? i + 1 : i - 1
                )
                {
                    switch (i)
                    {
                        case RaceDefine.RunningStyle.Nige:
                            var nige = ProperRunningStyleNige;
                            if (nige > maxGrade)
                            {
                                maxProperRunningStyle = RaceDefine.RunningStyle.Nige;
                                maxGrade = nige;
                            }

                            break;
                        case RaceDefine.RunningStyle.Senko:
                            var senko = ProperRunningStyleSenko;
                            if (senko > maxGrade)
                            {
                                maxProperRunningStyle = RaceDefine.RunningStyle.Senko;
                                maxGrade = senko;
                            }

                            break;
                        case RaceDefine.RunningStyle.Sashi:
                            var sashi = ProperRunningStyleSashi;
                            if (sashi > maxGrade)
                            {
                                maxProperRunningStyle = RaceDefine.RunningStyle.Sashi;
                                maxGrade = sashi;
                            }

                            break;
                        case RaceDefine.RunningStyle.Oikomi:
                            var oikomi = ProperRunningStyleOikomi;
                            if (oikomi > maxGrade)
                            {
                                maxProperRunningStyle = RaceDefine.RunningStyle.Oikomi;
                                maxGrade = oikomi;
                            }

                            break;
                    }
                }

                return maxProperRunningStyle;
            }

            /// <summary>
            /// 脚質適性を取得
            /// </summary>
            /// <param name="runningStyle"></param>
            /// <returns></returns>
            public int GetProperRunningStyle(RaceDefine.RunningStyle runningStyle)
            {
                switch (runningStyle)
                {
                    case RaceDefine.RunningStyle.Nige:
                        return (int) ProperRunningStyleNige;
                    case RaceDefine.RunningStyle.Senko:
                        return (int) ProperRunningStyleSenko;
                    case RaceDefine.RunningStyle.Sashi:
                        return (int) ProperRunningStyleSashi;
                    case RaceDefine.RunningStyle.Oikomi:
                        return (int) ProperRunningStyleOikomi;
                }

                return int.MinValue;
            }

            public void SetIsSaved(ObscuredBool isSaved)
            {
                _isSaved = isSaved;
            }

            public void SetOwnerViewerId(ObscuredLong viewerId)
            {
                _ownerViewerId = viewerId;
            }

            #endregion
            
        }//end

        /// <summary>
        /// 育成時のフォトデータ
        /// </summary>
        public class SupportCardData
        {
            public ObscuredInt Position { get; private set; }
            //シリアルではなく、CSVのフォトカードID
            public ObscuredInt SupportCardId { get; private set; }
            public ObscuredInt LimitBreakCount { get; private set; }
            public ObscuredInt Exp { get; private set; }
            public WorkSingleModeCharaData.SupportRentalType RentalType { get; private set; }
            public bool IsRental => RentalType == WorkSingleModeCharaData.SupportRentalType.CampaignRentalCard;
            public WorkSingleModeCharaData.SupportFriendType FriendType { get; private set; }
            public bool IsFriend => FriendType == WorkSingleModeCharaData.SupportFriendType.Friend;

            public MasterSupportCardData.SupportCardData MasterSupportCardData
            {
                get
                {
                    return MasterDataManager.Instance.masterSupportCardData.Get(SupportCardId);
                }
            }
            public int Level
            {
                get
                {
                    var levelData = MasterDataManager.Instance.masterSupportCardLevel.GetSupportCardLevelData(Exp, MasterSupportCardData.Rarity);
                    return levelData != null ? (int)levelData.Level : 0;
                }
            }
            
            public SupportCardData(TrainedCharaSupportCardList support)
            {
                Position = support.position;
                SupportCardId = support.support_card_id;
                LimitBreakCount = support.limit_break_count;
                Exp = support.exp;
                RentalType = (WorkSingleModeCharaData.SupportRentalType)support.rental_type;
                FriendType = (WorkSingleModeCharaData.SupportFriendType)support.friend_type;
            }

            public SupportCardData(WorkSingleModeCharaData.EquipSupportCard support)
            {
                Position = support.Position;
                SupportCardId = support.SupportCardId;
                LimitBreakCount = support.LimitBreakCount;
                Exp = support.Exp;
                RentalType = support.RentalType;
                FriendType = support.IsFriend ? WorkSingleModeCharaData.SupportFriendType.Friend : WorkSingleModeCharaData.SupportFriendType.None;
            }
        }

        /// <summary>
        /// お気に入り情報
        /// </summary>
        public class FavoriteData
        {
            #region Enum

            /// <summary>
            /// お気に入り設定タイプ
            /// </summary>
            public enum FavoriteType
            {
                Default = 0,        // ニンジン
                Common01 = 1,       // 山盛りご飯
                Common02 = 2,       // はちみつドリンク
                Common03 = 3,       // チョコレート
                Common04 = 4,       // ケーキ
                Common05 = 5,       // ダイヤ
                Common06 = 6,       // スペード
                Common07 = 7,       // ハート
                Common08 = 8,       // クラブ
            
                ProperShort = 9,    // 短距離
                ProperMile = 10,    // マイル
                ProperMiddle = 11,  // 中距離
                ProperLong = 12,    // 長距離
                ProperDirt = 13,    // ダート
                ProperFriend = 14,  // フレンド
            }
            
            #endregion
            
            #region Property, Variable

            /// <summary>
            /// ID（殿堂入りウマ娘）
            /// </summary>
            public readonly int TrainedCharaId;

            /// <summary>
            /// お気に入りタイプ
            /// </summary>
            public FavoriteType Type = FavoriteType.Default;

            /// <summary>
            /// メモ
            /// </summary>
            public string Memo = string.Empty;

            #endregion
            
            #region Method

            private FavoriteData() {}
            
            public FavoriteData(TrainedCharaFavorite resData) : this(resData.trained_chara_id, (FavoriteType) resData.icon_type, resData.memo) {}

            public FavoriteData(int id, FavoriteType type, string memo)
            {
                TrainedCharaId = id;
                Type = type;
                Memo = memo;
            }

            /// <summary>
            /// 更新
            /// </summary>
            public void Update(TrainedCharaFavorite resData)
            {
                Type = (FavoriteType) resData.icon_type;
                Memo = resData.memo;
            }

            #endregion
        }
        
        /// <summary>
        /// 殿堂入りウマ娘IDを元に、配列を更新
        /// </summary>
        public void UpdateTrainedChara(int[] updateTrainedCharaIdArray, int[] removeTrainedCharaIdArray)
        {
            // 削除されたIDが入っていれば
            if (removeTrainedCharaIdArray != null)
            {
                int len = removeTrainedCharaIdArray.Length;
                if (len >= 1)
                {
                    // _allDataDicと_dataDicから削除
                    for (int i = 0; i < len; i++)
                    {
                        if (_allDataDic.ContainsKey(removeTrainedCharaIdArray[i]))
                        {
                            _allDataDic.Remove(removeTrainedCharaIdArray[i]);
                        }

                        if (_dataDic.ContainsKey(removeTrainedCharaIdArray[i]))
                        {
                            _dataDic.Remove(removeTrainedCharaIdArray[i]);
                        }
                    }
                }
            }
            
            // is_savedが落とされたIDが入っていれば
            if (updateTrainedCharaIdArray != null)
            {
                int len = updateTrainedCharaIdArray.Length;
                if (len >= 1)
                {
                    // _allDataDicを更新。_dataDicからは削除
                    for (int i = 0; i < len; i++)
                    {
                        if (_allDataDic.ContainsKey(updateTrainedCharaIdArray[i]))
                        {
                            _allDataDic[updateTrainedCharaIdArray[i]].SetIsSaved(false);
                        }
                        
                        if (_dataDic.ContainsKey(updateTrainedCharaIdArray[i]))
                        {
                            _dataDic.Remove(updateTrainedCharaIdArray[i]);
                        }
                    }
                }
            }
            
            UpdateList();
        }
        
        #endregion
    }
}
