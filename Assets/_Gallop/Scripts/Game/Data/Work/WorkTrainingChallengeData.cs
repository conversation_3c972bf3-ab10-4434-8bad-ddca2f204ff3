using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Gallop.TrainingChallenge;

namespace Gallop
{
    namespace TrainingChallenge
    {
        /// <summary>
        /// 通常のアイテムではなく、イベント用アイテムとして更新されるデータ更新者
        /// </summary>
        /// <remarks>
        /// 他にイベントコインが必要になった時に外に出して独立させていいかもしれない
        /// </remarks>
        public class UserEventItemUpdater
        {
            private List<int /* PayItemId */> _limitedPayItemIds;
            private bool _isInitialized;

            private WorkItemData WorkItem => WorkDataManager.Instance.ItemData;


            /// <summary>
            /// 数を追加する
            /// </summary>
            public void Add(int itemId, int num)
            {
                if (!Exists(itemId)) return;

                WorkItem.Add(itemId, num);
            }

            public void Set(int itemId, int num)
            {
                if (!Exists(itemId)) return;

                WorkItem.Set(itemId, num);
            }

            /// <summary>
            /// 再更新させる
            /// </summary>
            public void Reset()
            {
                _isInitialized = false;
            }

            /// <summary>
            /// 指定したアイテムIDを持っているか
            /// </summary>
            public bool Exists(int itemId)
            {
                UpdateEventAll();

                return _limitedPayItemIds.Exists(x => x == itemId);
            }

            public void UpdateEventAll()
            {
                if (_isInitialized) return;

                var exchangeTopMaster = MasterDataManager.Instance.masterItemExchangeTop;
                var exchangeMaster = MasterDataManager.Instance.masterItemExchange;

                // 期間限定の物だけを取得する
                var topMasters = exchangeTopMaster
                    .GetListWithItemTopCategoryOrderByItemExchangeDispOrderAsc((byte)ShopDefine.ItemTopCategoryType.EventLimited);

                // 期間限定アイテムのIDを保持
                var limitedMasters = topMasters
                    .Select(topMaster => exchangeMaster.GetListWithItemExchangeTopIdOrderByIdAsc(topMaster.Id)?.FirstOrDefault())
                    .Where(x => x != null);

                _limitedPayItemIds = limitedMasters
                    .Select(x => x.PayItemId)
                    .ToList();

                _isInitialized = true;
            }
        }
    }

    /// <summary>
    /// 育成チャレンジ
    /// </summary>
    public class WorkTrainingChallengeData : ISingleModeTpBoostProvider
    {

        #region class, 定数

        /// <summary>
        /// support_card_ranking/get_ranking レスポンスの試験インデックス読み替え用の値
        /// </summary>
        private enum SupportRankingExamId
        {
            Extra = 99,
            Free = 100,
        }

        #endregion

        #region public変数
        #endregion

        #region private, protected変数

        private Dictionary<int/* examId */, SupportCardRankingItem[]> _supportRankingDict = new Dictionary<int, SupportCardRankingItem[]>();
        private TrainingChallengeExamInfoRepository _examInfoRepository;

        #endregion

        #region プロパティ

        public TrainingChallengeUserInfo UserInfo { get; private set; }

        /// <summary>
        /// アイテムID(定数)
        /// </summary>
        public int ItemId => GameDefine.TRAINING_CHALLENGE_EVENT_COIN_ID;

        /// <summary>
        /// 現在のポイント数
        /// </summary>

        public int Coin => WorkDataManager.Instance.ItemData.GetHaveItemNum(ItemId);

        /// <summary>
        /// 選択中の試験IDを返す
        /// </summary>
        public int SelectedExamId { get; private set; }

        /// <summary>
        /// <see cref="TrainingChallengeExamInfo"/>の情報を保持する
        /// </summary>
        public TrainingChallengeExamInfoRepository ExamInfoRepository => _examInfoRepository ?? (_examInfoRepository = new TrainingChallengeExamInfoRepository());

        /// <summary>
        /// 現在選択されている試験情報を返す。未選択はNULL
        /// </summary>
        public ExamDataEntity SelectedExamEntity => ExamInfoRepository.FindEntity(SelectedExamId);

        /// <summary>
        /// ランキング取得通信が必要の場合true
        /// </summary>
        /// <remarks>
        /// ・第４回開催から集計時刻が正午になったため、常に取得通信するよう変更した
        /// 　キャッシュを用いるなどして負荷軽減する可能性もあるため、プロパティ自体は残しておく
        /// </remarks>
        public bool NeedsRankingRequest => true;

        /// <summary>
        /// サポカランキング取得通信が必要な場合true
        /// </summary>
        /// <remarks>
        /// ・第４回開催から集計時刻が正午になったため、常に取得通信するよう変更した
        /// 　キャッシュを用いるなどして負荷軽減する可能性もあるため、プロパティ自体は残しておく
        /// 
        /// ・集計中のステイタス(IsSupportRankingCounting)の場合も取得通信を投げる必要がある
        /// </remarks>
        public bool NeedsSupportRankingRequest => true;

        /// <summary>
        /// ランキングユーザー情報
        /// </summary>
        public TrainingChallengeRankUser[] RankUsers { get; private set; }

        /// <summary>
        /// サポカランキンググループ情報
        /// </summary>
        public SupportCardRankingGroup[] SupportRankingGroups { get; private set; }

        /// <summary>
        /// サポカランキングが集計中の場合true
        /// </summary>
        public bool IsSupportRankingCounting { get; private set; }

        /// <summary>
        /// イベントに遷移済みの場合はtrue
        /// まだ一度もイベントに遷移していない場合はfalse
        /// (試験情報とUserInfoが両方存在していればイベント遷移済)
        /// </summary>
        public bool IsTransitioned => !ExamInfoRepository.Entities.IsNullOrEmpty() && UserInfo != null;

        /// <summary>
        /// イベントアイテム所持数更新を担当するクラス
        /// </summary>
        public UserEventItemUpdater EventItemUpdater { get; private set; } = new UserEventItemUpdater();

        /// <summary>
        /// 育成開始前画面でサポカランキングボタンを表示する必要がある場合true
        /// </summary>
        public bool NeedsShowSupportCardRankingAtSingleModeStart { get; set; }
        
        /// <summary>
        /// イベントブーストを利用できるか
        /// </summary>
        public bool EnableTpBoost 
        {
            get
            {
                // 本戦期間中でない場合は無効
                if (!MasterDataManager.Instance.masterTrainingChallengeMaster.IsInMainTerm())
                {
                    return false;
                }

                // データ上利用できない場合も無効
                return TpBoostData.EnableTpBoost;
            }
        }

        /// <summary>
        /// 有効なイベントブーストデータを返す
        /// </summary>
        public ISingleModeTpBoostData TpBoostData =>
            MasterDataManager.Instance.masterTrainingChallengeMaster.CurrentEntity;


        private static MasterDataManager MasterManager => MasterDataManager.Instance;

        #endregion

        #region publicメソッド

        /// <summary>
        /// 試験IDから試験マスタを検索する
        /// </summary>
        public MasterTrainingChallengeExam.TrainingChallengeExam FindExamMaster(int examId)
        {
            var currentMaster = MasterManager.masterTrainingChallengeMaster.CurrentEntity;
            if (currentMaster == null) return null;

            return currentMaster.FindExamMaster(examId);
        }

        /// <summary>
        /// 試験IDからIndex値を検索する(無ければ-1)
        /// </summary>
        public int FindIndex(int examId)
        {
            var currentMaster = MasterManager.masterTrainingChallengeMaster.CurrentEntity;
            if (currentMaster == null) return -1;

            return currentMaster.GetIndex(examId);
        }

        /// <summary>
        /// 試験IDからサポカランキングのレスポンス用Index値を検索する(無ければ-1)
        /// </summary>
        public int FindSuportRankingExamIndex(int examId)
        {
            var currentMaster = MasterManager.masterTrainingChallengeMaster.CurrentEntity;
            if (currentMaster == null) return -1;

            // エクストラ、フリー課程はサポカランキング用の試験インデックスに読み替える
            if (currentMaster.IsExExamId(examId))
            {
                return (int)SupportRankingExamId.Extra;
            }

            if (currentMaster.IsFreeExamId(examId))
            {
                return (int)SupportRankingExamId.Free;
            }

            return currentMaster.GetIndex(examId);
        }

        /// <summary>
        /// ログインの通信で更新処理をかける
        /// </summary>
        public void UpdateAtLoginApi(TrainingChallengeUserInfo userInfo, TrainingChallengeExamInfo[] examInfos)
        {
            // どちらかがNullの場合実行できない為予め弾く
            if (userInfo == null || examInfos == null) return;

            // まずはマスタの更新処理をかける
            // 結果、参加状態じゃない場合は更新できない
            var master = MasterDataManager.Instance.masterTrainingChallengeMaster;
            master.UpdateStatus();

            if (master.Status == TrainingChallengeDefine.EventStatus.None) return;

            UpdateUserInfo(userInfo);
            UpdateExamInfos(examInfos);
        }

        /// <summary>
        /// ユーザー情報の更新
        /// </summary>
        public void UpdateUserInfo(TrainingChallengeUserInfo userInfo)
        {
            UserInfo = userInfo;

            SetSelectedExamId(userInfo?.select_exam_id ?? 0);

            // nullの場合は0
            EventItemUpdater.Set(ItemId, userInfo?.event_coin ?? 0);
        }

        public void SetSelectedExamId(int examId)
        {
            SelectedExamId = examId;
        }

        /// <summary>
        /// 試験情報の更新
        /// </summary>
        public void UpdateExamInfos(TrainingChallengeExamInfo[] examInfos)
        {
            if (examInfos.IsNullOrEmpty()) return;

            ExamInfoRepository.Update(examInfos);
        }

        /// <summary>
        /// ランキングユーザーの更新
        /// </summary>
        public void UpdateRankUsers(TrainingChallengeRankUser[] rankUsers)
        {
            RankUsers = rankUsers;
        }

        /// <summary>
        /// サポカランキンググループの更新
        /// </summary>
        public void UpdateSupportRankGroups(SupportCardRankingGroup[] groups, bool isCounting)
        {
            SupportRankingGroups = groups;
            IsSupportRankingCounting = isCounting;

            // キャッシュを作成しておく
            _supportRankingDict = groups.ToDictionary(group => group.exam_index, group => group.items);
        }

        public bool TryGetSupportRankingItems(int examId, out SupportCardRankingItem[] result) =>
            _supportRankingDict.TryGetValue(examId, out result);

        /// <summary>
        /// 可能であればTOPにシーン遷移する
        /// </summary>
        public bool ChangeSceneIfNeeded(SceneDefine.ViewId viewId = SceneDefine.ViewId.TrainingChallengeTop)
        {
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.TrainingChallengeHub,
                new HubViewControllerBase.HubViewInfo() { DefaultViewId = viewId });

            return true;
        }

        /// <summary>
        /// アトラスの読み込み
        /// </summary>
        public Cute.UI.AtlasReference LoadAtlas() => 
            UIManager.Instance.LoadAtlas(TargetAtlasType.TrainingChallenge);

        public Cute.UI.AtlasReference LoadCommonAtlas() =>
            UIManager.Instance.LoadAtlas(TargetAtlasType.Common);

        /// <summary>
        /// アトラスからスプライトを取得する
        /// </summary>
        public Sprite GetSpriteByAtlas(string name) =>
            LoadAtlas().GetSprite(name);

        public Sprite GetSpriteByCommonAtlas(string name) =>
            LoadCommonAtlas().GetSprite(name);


        /// <summary>
        /// 指定されたショップが育成チャレンジ用のものかどうか判定
        /// </summary>
        public bool ConfirmTrainingChallengeShop(int shopId)
        {
            var master = MasterDataManager.Instance.masterTrainingChallengeMaster.CurrentEntity;
            if (master == null) return false;

            return master.ShopId == shopId;
        }

        /// <summary>
        /// バッジ表示が必要な場合true
        /// </summary>
        public bool NeedsShowBadge()
        {
            // 条件1 ミッション受け取り出来る場合
            if (TempData.Instance.TrainingChallengeData.MissionClearNum > 0)
            {
                return true;
            }

            // バッジは出さない
            return false;
        }

        /// <summary>
        /// 育成開始前のサポカランキングボタン
        /// </summary>
        public void UpdateSingleModeStartSupportCardRankingButton(TrainingChallengeDefine.SupportCardRankingState state)
        {
            if (state != TrainingChallengeDefine.SupportCardRankingState.Open)
            {
                NeedsShowSupportCardRankingAtSingleModeStart = false;
                return;
            }

            // 開催期間中を一応見ておく
            MasterDataManager.Instance.masterTrainingChallengeMaster.UpdateStatus();
            if (MasterDataManager.Instance.masterTrainingChallengeMaster.Status == TrainingChallengeDefine.EventStatus.None)
            {
                NeedsShowSupportCardRankingAtSingleModeStart = false;
                return;
            }

            // 表示できる
            NeedsShowSupportCardRankingAtSingleModeStart = true;
        }

        #endregion

        #region privateメソッド

        #endregion
    }
}
