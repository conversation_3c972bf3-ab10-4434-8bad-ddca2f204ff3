using System;
using CodeStage.AntiCheat.ObscuredTypes;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成シナリオレコード
    /// </summary>
    public class WorkScenarioRecordData
    {
        /// <summary>
        /// 最高レコード情報があるか
        /// </summary>
        public bool HasMaxRecordData => _maxRecordData != null;
        /// <summary>
        /// シナリオ別最高レコードデータ
        /// </summary>
        private Dictionary<int, ObscuredInt> _maxRecordData = null;

        /// <summary>
        /// シナリオID別シナリオレコードデータ
        /// </summary>
        private readonly Dictionary<int, List<ScenarioRecordData>> _dataDic = new Dictionary<int, List<ScenarioRecordData>>();

        /// <summary>
        /// シナリオ別データリスト取得
        /// </summary>
        public List<ScenarioRecordData> GetDataList(int scenarioId)
        {
            if (_dataDic == null || !_dataDic.ContainsKey(scenarioId))
            {
                return null;
            }
            return _dataDic[scenarioId];
        }
        
        /// <summary>
        /// シナリオ別最高レコード
        /// </summary>
        public int GetRecord(int scenarioId)
        {
            if (_maxRecordData == null)
            {
                Debug.LogError("最高シナリオレコード値情報が未初期化です。サーバーから値を取得してください。");
                return 0;
            }

            if (!_maxRecordData.TryGetValue(scenarioId, out var value))
            {
                return 0;
            }
            return value;
        }

        /// <summary>
        /// 最高レコード情報を返す
        /// </summary>
        /// <param name="scenarioId"></param>
        /// <returns></returns>
        public ScenarioRecordData GetHighestScenarioRecordData(int scenarioId)
        {
            var list = GetDataList(scenarioId);
            if (list == null || list.Count == 0)
            {
                Debug.LogError("シナリオレコードの情報がありません");
                return null;
            }

            return list.OrderByDescending(data => data.TrainedCharaData.RankScore).First();
        }
        
        /// <summary>
        /// データ更新
        /// </summary>
        public void UpdateData(ScenarioRecord[] dataArray)
        {
            if (dataArray == null)
            {
                return;
            }
            
            for (int i = 0; i < dataArray.Length; ++i)
            {
                var scenarioId = dataArray[i].scenario_id;

                if (_dataDic.ContainsKey(scenarioId))
                {
                    _dataDic[scenarioId].Clear();
                }
            }
            AddDataArray(dataArray);
            
        }

        /// <summary>
        /// 最高シナリオレコード値を更新
        /// </summary>
        /// <param name="dataArray"></param>
        public void UpdateMaxRecordData(ScenarioRecordHighestScore[] dataArray)
        {
            if (dataArray == null)
            {
                return;
            }

            _maxRecordData = new Dictionary<int, ObscuredInt>();
            foreach (var data in dataArray)
            {
                _maxRecordData.Add(data.scenario_id, data.highest_rank_score);
            }
        }

        /// <summary>
        /// 最高シナリオレコード値を更新
        /// </summary>
        /// <param name="scenarioId"></param>
        /// <param name="record"></param>
        public void UpdateMaxRecordData(int scenarioId, int record)
        {
            if (_maxRecordData == null)
            {
                // nullの場合は使う時に全データが初期化されるので保持する必要はない
                return;
            }

            if (!_maxRecordData.ContainsKey(scenarioId) || _maxRecordData[scenarioId] < record)
            {
                _maxRecordData[scenarioId] = record;
            }
        }
        
        /// <summary>
        /// 対応する殿堂入りウマ娘がレコード情報に含まれる場合、TrainedCharaDataを更新
        /// </summary>
        public void UpdateTrainedCharaDataIfExist(TrainedChara charaInfo)
        {
            if (charaInfo == null || _dataDic == null || !_dataDic.ContainsKey(charaInfo.scenario_id))
            {
                return;
            }
            var data = _dataDic[charaInfo.scenario_id].FirstOrDefault(d => d.TrainedCharaData.Id == charaInfo.trained_chara_id);
            if(data != null)
            {
                data.TrainedCharaData.Update(charaInfo);
            }
        }

        /// <summary>
        /// データ追加
        /// </summary>
        private void AddDataArray(ScenarioRecord[] dataArray)
        {
            for (int i = 0; i < dataArray.Length; ++i)
            {
                var data = new ScenarioRecordData(dataArray[i]);
                int scenarioId = data.ScenarioId;
                if (!_dataDic.ContainsKey(scenarioId))
                {
                    _dataDic.Add(scenarioId , new List<ScenarioRecordData>());
                }

                var list = _dataDic[scenarioId];
                list.Add(data);
            }
            
            //ランキング順にリストに入るようにソートする
            foreach (var dataListPair in _dataDic)
            {
                var list = dataListPair.Value;
                list.OrderBy(d => d.Ranking);
            }
        }

        /// <summary>
        /// 育成シナリオレコード
        /// </summary>
        public class ScenarioRecordData
        {
            public ObscuredInt ScenarioId { get; }
            public ObscuredInt Ranking { get; }
            public WorkTrainedCharaData.TrainedCharaData TrainedCharaData { get; }

            public ScenarioRecordData(ScenarioRecord data)
            {
                ScenarioId = data.scenario_id;
                Ranking = data.directory_ranking;
                TrainedCharaData = new WorkTrainedCharaData.TrainedCharaData(data.trained_chara);
            }
        }
    }
}
