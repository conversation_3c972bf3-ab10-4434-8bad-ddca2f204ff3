using System.Linq;
using System.Collections.Generic;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    /// <summary>
    /// 「CM見返し機能」のワークデータ
    /// ┗ ※育成シナリオ伝説編（The Twinkle Legends）のエンディングで流れるCMムービーを見返すための機能
    /// ┗ ※画面の場所は、ストーリー＞エクストラ＞ムービー＞CMムービー画面
    /// </summary>
    public class WorkExtraCommercialData
    {
        /// <summary>
        /// CM1つの情報
        /// </summary>
        public class CmInfo
        {
            /// <summary>CM詳細ID（single_mode_10_cm_detail.csvのid）</summary>
            public ObscuredInt CmDetailId { get; private set; } = 0;

            /// <summary>殿堂入りウマ娘</summary>
            public WorkTrainedCharaData.TrainedCharaData WorkTrainedCharaData { get; private set; } = null;

            /// <summary>保存済みフラグ</summary>
            public ObscuredBool IsSaved { get; set; } = false;

            /// <summary>自動削除日時</summary>
            private ObscuredString _deleteTime = string.Empty;
            private ObscuredLong _deleteTimeStamp = 0;

            /// <summary>CM詳細マスター（single_mode_10_cm_detail.csvのデータ）</summary>
            public MasterSingleMode10CmDetail.SingleMode10CmDetail MasterSingleMode10CmDetail { get; private set; } = null;

            /// <summary>コンストラクタ（CMムービー画面に入った時に呼ばれる）</summary>
            public CmInfo(ExtraCmIndexInfo extraCmIndexInfo)
            {
                CmDetailId = extraCmIndexInfo.cm_detail_id;
                MasterSingleMode10CmDetail = MasterDataManager.Instance.masterSingleMode10CmDetail.Get(CmDetailId);

                WorkTrainedCharaData = WorkDataManager.Instance.TrainedCharaData.Get(extraCmIndexInfo.trained_chara_id);

                IsSaved = extraCmIndexInfo.is_saved;

                _deleteTime = extraCmIndexInfo.delete_time;
                _deleteTimeStamp = TimeUtil.ToUnixTimeFromJstString(_deleteTime);

                Debug.Assert((WorkTrainedCharaData != null), "殿堂入りウマ娘が見つかりませんでした。trained_chara_id:" + extraCmIndexInfo.trained_chara_id);
                Debug.Assert((MasterSingleMode10CmDetail != null), "single_mode_10_cm_detail.csvのデータが見つかりませんでした。cm_detail_id:" + CmDetailId);
            }

            public CmInfo(int cmDetailId, WorkTrainedCharaData.TrainedCharaData workTrainedCharaData, bool isSaved)
            {
                CmDetailId = cmDetailId;
                MasterSingleMode10CmDetail = MasterDataManager.Instance.masterSingleMode10CmDetail.Get(CmDetailId);

                WorkTrainedCharaData = workTrainedCharaData;

                IsSaved = isSaved;
            }

            /// <summary>サーバーからのデータに問題が無いかをチェック</summary>
            public static bool IsValidExtraCmIndexInfo(ExtraCmIndexInfo extraCmIndexInfo)
            {
                if (extraCmIndexInfo == null)
                    return false;

                var workTrainedCharaData = WorkDataManager.Instance.TrainedCharaData.Get(extraCmIndexInfo.trained_chara_id);
                if (workTrainedCharaData == null)
                    return false; // 殿堂入りウマ情報が見つからない（移籍したなど）

                return true;
            }

            /// <summary>自動削除日時を過ぎているか</summary>
            public bool IsPastDeleteTime()
            {
                if (_deleteTimeStamp == 0)
                    return true; // サーバー側ではCMを元データと保存データで分けて持っている。元データが無いとdelete_timeが0になる。元データが無いので削除するしかない

                var remainingTimeSeconds = _deleteTimeStamp - TimeUtil.GetServerTimeStamp();

                if (remainingTimeSeconds < 0)
                    return true;

                return false;
            }
        }

        /// <summary>CM情報一覧</summary>
        public List<CmInfo> CmInfoList { get; private set; } = new List<CmInfo>();

        /// <summary>CM1つのレース結果情報（※CMを再生する時にだけ一時的に必要。再生実行時にサーバーから値が入る。メモリを圧迫するので再生が終わったら速やかに破棄する事。）</summary>
        public RaceResultInfo RaceResultInfo { get; private set; } = null;

        /// <summary>CM保存数</summary>
        public ObscuredInt SaveCmCount { get; private set; } = 0;
        /// <summary>CM保存数の上限</summary>
        public ObscuredInt SaveCmCountLimit { get; private set; } = 0;
        /// <summary>保存されているCMの殿堂入りウマ娘ID一覧（殿堂入りウマ娘一覧画面で移籍を禁止するのに使用）</summary>
        public List<ObscuredInt> SaveCmTranedCharaIdList { get; private set; } = new List<ObscuredInt>();

        /// <summary>NEWマークの有無</summary>
        public ObscuredBool CanAttachNewBadge { get; private set; } = false;

        /// <summary>最後に再生したCM</summary>
        public ObscuredInt LastPlayCmTrainedCharaId = 0;


        /// <summary>CM情報1つを取得</summary>
        public CmInfo GetCmInfo(int trainedCharaId)
        {
            if (trainedCharaId == 0)
                return null;

            return CmInfoList.FirstOrDefault(x => x.WorkTrainedCharaData.Id == trainedCharaId);
        }

        /// <summary>指定した殿堂入りウマ娘のCMが保存されているか</summary>
        public bool IsSavedCm(int trainedCharaId)
        {
            var cmInfo = GetCmInfo(trainedCharaId);
            if (cmInfo == null)
                return false;

            return cmInfo.IsSaved;
        }

        /// <summary>保存されているCM情報一覧を取得する</summary>
        public List<CmInfo> GetSavedCmInfoList()
        {
            var outCmInfoList = new List<CmInfo>();

            if (CmInfoList.IsNullOrEmpty())
                return outCmInfoList;

            return CmInfoList.Where(x => x.IsSaved).ToList();
        }

        /// <summary>レース関連情報を破棄する（レース関連情報はCMを再生する時にだけ必要。メモリを圧迫するため再生が終わったら速やかに破棄する事。）</summary>
        public void ReleaseRaceResultInfo()
        {
            RaceManager.RaceInfo = null;
            RaceResultInfo = null;
        }

        #region APIによる更新

        /// <summary>
        /// ログイン時のAPIによる更新
        /// </summary>
        /// <param name="serverData"></param>
        public void Apply(ExtraStoryData serverData)
        {
            if (serverData == null)
                return;
            var extraCmData = serverData.extra_cm_data;
            if (extraCmData == null)
                return;

            // 保存されているCMの殿堂入りウマ娘ID一覧
            SaveCmTranedCharaIdList.Clear();
            if (!extraCmData.cm_save_trained_chara_id_array.IsNullOrEmpty())
            {
                foreach (var trainedCharaId in extraCmData.cm_save_trained_chara_id_array)
                {
                    SaveCmTranedCharaIdList.Add(trainedCharaId);
                }
            }

            // NEWマークの有無
            CanAttachNewBadge = extraCmData.is_extra_cm_new;
        }

        /// <summary>
        /// 「CMムービー画面」に遷移した時のAPIによる更新
        /// </summary>
        public void Apply(TrainedCharaExtraCmIndexResponse.CommonResponse serverData)
        {
            if (serverData == null)
                return;

            // CM情報一覧
            {
                CmInfoList.Clear();
                if (!serverData.extra_cm_index_info_array.IsNullOrEmpty())
                {
                    foreach (var extraCmIndexInfo in serverData.extra_cm_index_info_array)
                    {
                        if (!CmInfo.IsValidExtraCmIndexInfo(extraCmIndexInfo))
                            continue;

                        var cmInfo = new CmInfo(extraCmIndexInfo);
                        CmInfoList.Add(cmInfo);
                    }
                }
            }

            // CM保存数
            SaveCmCount = CmInfoList.Count(x => x.IsSaved);
            // CM保存数の上限
            SaveCmCountLimit = serverData.save_limit_num;

            // NEWマークを外す
            CanAttachNewBadge = false;
        }

        /// <summary>
        /// CMを再生する時のAPIによる更新
        /// </summary>
        public void Apply(TrainedCharaExtraCmPlayResponse.CommonResponse serverData, int trainedCharaId)
        {
            if (serverData == null)
                return;

            RaceResultInfo = serverData.race_result_info;

            LastPlayCmTrainedCharaId = trainedCharaId;
        }

        /// <summary>
        /// CMを保存した時のAPIによる更新
        /// </summary>
        public void Apply(TrainedCharaExtraCmSaveResponse.CommonResponse serverData, List<WorkTrainedCharaData.TrainedCharaData> selectedTrainedCharaList)
        {
            if (serverData == null)
                return;

            int cmCountBefore = CmInfoList.Count;
            int cmCountAfter = (serverData.extra_cm_index_info_array.IsNullOrEmpty()) ? 0 : serverData.extra_cm_index_info_array.Length;

            // 削除されたCMがある場合
            if (cmCountAfter < cmCountBefore)
            {
                // リストの項目数が減るのでリストを作り直す
                CmInfoList.Clear();
                if (!serverData.extra_cm_index_info_array.IsNullOrEmpty())
                {
                    foreach (var extraCmIndexInfo in serverData.extra_cm_index_info_array)
                    {
                        if (!CmInfo.IsValidExtraCmIndexInfo(extraCmIndexInfo))
                            continue;

                        var cmInfo = new CmInfo(extraCmIndexInfo);
                        CmInfoList.Add(cmInfo);
                    }
                }
            }
            // 削除されなかった場合
            else
            {
                // 各CM情報を更新
                foreach (var cmInfo in CmInfoList)
                {
                    cmInfo.IsSaved = (selectedTrainedCharaList.Any(x => x.Id == cmInfo.WorkTrainedCharaData.Id));
                }
            }

            // CM保存数を更新
            SaveCmCount = CmInfoList.Count(x => x.IsSaved);
        }

        #endregion
    }
}