using System.Collections.Generic;
using CodeStage.AntiCheat.ObscuredTypes;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// マスターズチャレンジWorkData
    /// </summary>
    public class WorkUltimateRaceData
    {
        #region enum, class
        
        /// <summary>
        /// レース1つのデータ
        /// </summary>
        public class UltimateRaceContentsData
        {
            /// <summary>出走制限がかかる出走回数</summary>
            public const int ENTRY_LIMIT_START_NUM = 3;
            
            /// <summary>マスターズチャレンジID(ultimate_race_contents.csvのid)</summary>
            public ObscuredInt UltimateRaceId { get; }

            /// <summary>クリア済みフラグ</summary>
            public ObscuredBool IsCleared { get; set; }

            /// <summary>プレイ済みフラグ</summary>
            public ObscuredBool IsPlayed { get; }
            
            /// <summary>クリア済み特殊条件キャラID</summary>
            public ObscuredInt[] SpecialClearedCharaIdArray { get; set; }

            public MasterUltimateRaceContents.UltimateRaceContents RaceContentsMaster =>
                MasterDataManager.Instance.masterUltimateRaceContents.GetWithId(UltimateRaceId);

            public void SetSpecialClearedCharaIdArray(int[] charaIds)
            {
                if (charaIds == null) return;
                SpecialClearedCharaIdArray = charaIds.Select(a=>(ObscuredInt)a).ToArray();
            }
            
            /// <summary>ボス情報。固定の対戦相手</summary>
            public RaceHorseData[] BossDataArray { get; }

            /// <summary>殿堂入りウマ娘出走情報</summary>
            public StartTrainedChara[] StartTrainedCharaArray => _startTrainedCharaArray;

            private StartTrainedChara[] _startTrainedCharaArray;

            /// <summary>メモリー獲得済みキャラ一覧</summary>
            public ObscuredInt[] ClearedMemoryCharaIdArray { get; private set; }

            public UltimateRaceContentsData(UltimateRaceData data)
            {
                UltimateRaceId = data.ultimate_race_id;
                IsCleared = (data.is_cleared != 0);
                IsPlayed = (data.is_played != 0);
                _startTrainedCharaArray = data.start_trained_chara_array;
                BossDataArray = data.boss_data;
                if (data.cleared_memory_chara_id_array != null)
                {
                    ClearedMemoryCharaIdArray = data.cleared_memory_chara_id_array.Select(x => (ObscuredInt)x).ToArray();
                }
            }
            
            /// <summary>殿堂入りウマ娘出走回数取得</summary>
            public int GetTrainedCharaStartCount(int trainedCharaId)
            {
                if (StartTrainedCharaArray == null) return 0;
                return StartTrainedCharaArray.FirstOrDefault(a => a.trained_chara_id == trainedCharaId)?.count ?? 0;
            }
            
            /// <summary>
            /// >殿堂入りウマ娘出走回数を加算
            /// </summary>
            public void AddTrainedCharaStartCount(int trainedCharaId)
            {
                if (_startTrainedCharaArray == null)
                {
                    //新規
                    _startTrainedCharaArray = new[]
                    {
                        new StartTrainedChara()
                        {
                            trained_chara_id = trainedCharaId,
                            count = 1,
                        },
                    };
                }
                else
                {
                    var lostChara = StartTrainedCharaArray.FirstOrDefault(a => a.trained_chara_id == trainedCharaId);
                    if (lostChara != null)
                    {
                        //既存カウントアップ
                        lostChara.count++;
                    }
                    else
                    {
                        //配列末尾に新規追加
                        System.Array.Resize(ref _startTrainedCharaArray, _startTrainedCharaArray.Length + 1);
                        lostChara = new StartTrainedChara()
                        {
                            trained_chara_id = trainedCharaId,
                            count = 1,
                        };
                        StartTrainedCharaArray[_startTrainedCharaArray.Length - 1] = lostChara;
                    }
                }
            }
            
            /// <summary>殿堂入りウマが出走制限か。出走回数3回でtrue</summary>
            public bool IsEntryLimitTrainedChara(int trainedCharaId)
            {
                return GetTrainedCharaStartCount(trainedCharaId) >= ENTRY_LIMIT_START_NUM;
            }
        }

        /// <summary>
        /// イベントの開催状態
        /// </summary>
        public enum PeriodState
        {
            Close,          // イベント開催期間外
            InSession,      // イベント開催中
            AdvanceNotice,  // 開催前の予告期間
        }
        
        /// <summary>
        /// 強敵出現カットインの再生状態
        /// </summary>
        public enum FECutinState
        {
            None, // 未解放
            Play, // カットイン再生
            End,  // カットイン再生済み
        }
        
        #endregion
        
        #region メンバ、プロパティ
        
        /// <summary>開催中のマスターズチャレンジのイベントID(ultimate_race_data.csvのid)</summary>
        public ObscuredInt Id { get; private set; }
        
        /// <summary>Newフラグ</summary>
        public ObscuredBool IsNew { get; private set; }

        /// <summary>開催中のマスターズチャレンジのマスターデータ(ultimate_race_dataのレコード)</summary>
        private MasterUltimateRaceData.UltimateRaceData _masterUltimateRaceData = null;

        public MasterUltimateRaceData.UltimateRaceData MasterUltimateRaceData => _masterUltimateRaceData;
        
        /// <summary>レースの進行状態</summary>
        private ObscuredInt _state;
        public WorkRaceStateData.State State => (WorkRaceStateData.State)(int)_state;

        public ObscuredBool IsAllClear { get; private set; }

        /// <summary>出走中の殿堂入りウマ娘ID</summary>
        public int EntryTrainedCharaId { get; private set; }

        // サーバーから送られてきたデータ
        private readonly List<UltimateRaceContentsData> _contentsDataList = new List<UltimateRaceContentsData>();
        // _contentsDataListから未解放の隠しレースを除いたデータ
        private List<UltimateRaceContentsData> _contentsOpenedDataList = new List<UltimateRaceContentsData>();
        public UltimateRaceContentsData GetContentsData(int raceContentsId) => _contentsOpenedDataList.Find(a => a.UltimateRaceId == raceContentsId);

        public List<MasterUltimateRaceContents.UltimateRaceContents> GetContentsMasterList() =>
            _contentsOpenedDataList.Select(data => data.RaceContentsMaster).ToList();

        /// <summary>各レースのクリア履歴</summary>
        private Dictionary<int, ClearedTrainedChara[]> _clearedTrainedCharaDictionary = new Dictionary<int, ClearedTrainedChara[]>();
        public Dictionary<int, ClearedTrainedChara[]> ClearedTrainedCharaDictionary => _clearedTrainedCharaDictionary;
        
        /// <summary>解放されたレースID一覧</summary>
        private List<int> _unlockedRaceIdList = new List<int>();
        public List<int> UnlockedRaceIdList => _unlockedRaceIdList;
        
        #endregion
        
        #region 通信反映

        /// <summary>
        /// ログインAPIのResponseを反映
        /// </summary>
        public void Update(UltimateRaceLoadInfo info)
        {
            if (info == null)return;
            Id = info.event_id;
            _state = info.state;
            IsNew = info.new_flag;
            SetEntryTrainedCharaId(info.resume_trained_chara_id);
            ApplyIsAllClear(info.all_clear_flag);

            _masterUltimateRaceData = MasterDataManager.Instance.masterUltimateRaceData.Get(info.event_id);
        }

        /// <summary>
        /// Index
        /// </summary>
        public void Apply(UltimateRaceIndexResponse.CommonResponse res)
        {
            if (res == null)return;
            
            _contentsDataList.Clear();
            _contentsOpenedDataList.Clear();
            if (res.ultimate_race_record_array != null)
            {
                foreach (var ultimateRaceData in res.ultimate_race_record_array)
                {
                    _contentsDataList.Add(new UltimateRaceContentsData(ultimateRaceData));
                }

                // 隠しレースで未解放のものはremoveする
                _contentsOpenedDataList = _contentsDataList.Where(x => IsOpenExtraRace(x.UltimateRaceId)).ToList();
                // Level1~5の隠しレースは、Level1のみに条件が指定されているので、Level1が未解放の場合、↑で削除されているので2~5も削除
                var masterList = GetContentsMasterList();
                foreach (var ultimateRaceContents in masterList)
                {
                    if (ultimateRaceContents.IsHiddenRace != 0 && ultimateRaceContents.Difficulty < UltimateRaceDefine.DifficultyLvEx && 
                        !masterList.Exists(a => a.GroupId == ultimateRaceContents.GroupId && a.Difficulty == 1))
                    {
                        _contentsOpenedDataList.RemoveAll(x => x.UltimateRaceId == ultimateRaceContents.Id);
                    }
                }
                
                IsNew = _contentsOpenedDataList.Any(item =>
                    !IsLockedRaceContents(item.UltimateRaceId) && IsNewRaceContents(item.UltimateRaceId));
            }

            // 解放されたレースID一覧
            if (res.cutin_info_array != null)
            {
                _unlockedRaceIdList = res.cutin_info_array.Where(x => x.state == (int)FECutinState.Play).Select(x => x.id).ToList();
            }
        }

        /// <summary>
        /// 隠しレース解放済みかどうか
        /// </summary>
        public bool IsOpenExtraRace(int ultimateRaceId)
        {
            // csvからデータ取得
            var masterRaceContents = MasterDataManager.Instance.masterUltimateRaceContents.GetWithId(ultimateRaceId);
            if (masterRaceContents == null)
            {
                return false;
            }

            // 隠しレースじゃない又は、解放条件がない場合はtrue
            if (masterRaceContents.IsHiddenRace == 0 || masterRaceContents.UnlockConditionId == 0)
            {
                return true;
            }

            // 隠しレースの場合、解放条件を満たしているか
            var masterRaceUnlockCondition = MasterDataManager.Instance.masterUltimateRaceUnlockCondition.Get(masterRaceContents.UnlockConditionId);
            return IsCleardRace(masterRaceUnlockCondition.ConditionRaceContentsId1) &&
                   IsCleardRace(masterRaceUnlockCondition.ConditionRaceContentsId2) &&
                   IsCleardRace(masterRaceUnlockCondition.ConditionRaceContentsId3) &&
                   IsCleardRace(masterRaceUnlockCondition.ConditionRaceContentsId4) &&
                   IsCleardRace(masterRaceUnlockCondition.ConditionRaceContentsId5);
        }

        /// <summary>
        /// 指定レースクリア済みかどうか
        /// </summary>
        private bool IsCleardRace(int raceContentsId)
        {
            if (raceContentsId != 0)
            {
                var data = _contentsDataList.Find(x => x.UltimateRaceId == raceContentsId);
                if (data == null || !data.IsCleared)
                {
                    // 未クリア
                    return false;
                }
            }

            // クリア済み
            return true;
        }

        /// <summary>
        /// Resume
        /// </summary>
        public void Apply(UltimateRaceResumeResponse.CommonResponse res)
        {
            var masterRaceContents = UltimateRaceUtil.GetMasterUltimateRaceContentsByRaceInstanceId(res.race_instance_id);
            if (masterRaceContents == null) return;
            
            var contentsData = GetContentsData(masterRaceContents.Id);
            if (contentsData == null)
            {
                //Index前のResumeした場合、新規データでパドックへ行く。パドックで短縮レース可能かクリア状態が必要。パドックからレース詳細用の特殊キャラクリア状態が必要。
                contentsData = new UltimateRaceContentsData(
                        new UltimateRaceData
                        {
                            ultimate_race_id = masterRaceContents.Id,
                            is_cleared = res.is_cleared,
                            start_trained_chara_array = new []
                            {
                                // 出走中キャラの出走回数（レースリザルトのリトライ判定に必要）
                                new StartTrainedChara()
                                {
                                    trained_chara_id = EntryTrainedCharaId,
                                    count = res.start_count
                                }
                            },
                        }
                    );
                _contentsOpenedDataList.Add(contentsData);
            }
            else
            {
                contentsData.IsCleared = res.is_cleared != 0;
            }
        }

        public void ApplyState(int state)
        {
            _state = state;
        }

        public void ApplyIsAllClear(bool isAllClear)
        {
            IsAllClear = isAllClear;
        }

        #endregion
        
        #region method

        /// <summary>
        /// 予告期間中か
        /// </summary>
        /// <returns></returns>
        public bool IsAdvanceNotice()
        {
            return GetEventPeriodState() == PeriodState.AdvanceNotice;
        }
        
        /// <summary>
        /// 開催期間中か
        /// </summary>
        /// <returns></returns>
        public bool IsInSession()
        {
            return GetEventPeriodState() == PeriodState.InSession;
        }

        /// <summary>
        /// 開催期間外か
        /// </summary>
        /// <returns></returns>
        public bool IsClose()
        {
            return GetEventPeriodState() == PeriodState.Close;
        }

        /// <summary>
        /// 開催状態を取得する
        /// </summary>
        /// <returns></returns>
        public PeriodState GetEventPeriodState()
        {
            if (Id > 0)
            {
                var ultimateRaceData = MasterDataManager.Instance.masterUltimateRaceData.Get(Id);
                if (ultimateRaceData != null)
                {
                    var serverTimeStamp = TimeUtil.GetServerTimeStamp();
                    var noticeDate = ultimateRaceData.NoticeDate;
                    var startDate = ultimateRaceData.StartDate;
                    var endDate = ultimateRaceData.EndDate;

                    if (noticeDate <= serverTimeStamp && serverTimeStamp <= startDate) 
                        return PeriodState.AdvanceNotice; // 予告期間
                    if (startDate <= serverTimeStamp && serverTimeStamp <= endDate)
                        return PeriodState.InSession; // 開催期間
                }
            }

            return PeriodState.Close; // 開催期間外
        }
        
        /// <summary>
        /// 指定のレースコンテンツがNewか
        /// </summary>
        public bool IsNewRaceContents(int raceContentsId)
        {
            var workContentsData = GetContentsData(raceContentsId);
            if (workContentsData == null) return true;
            return workContentsData.IsCleared == false && workContentsData.IsPlayed == false;
        }
        
        /// <summary>
        /// 指定のレースコンテンツがロックされているか
        /// </summary>
        /// <param name="raceContentsId"></param>
        /// <returns></returns>
        public bool IsLockedRaceContents(int raceContentsId)
        {
            var raceContentsMaster = MasterDataManager.Instance.masterUltimateRaceContents.GetWithId(raceContentsId);
            if (raceContentsMaster == null) return false;
            if (raceContentsMaster.Difficulty == 1) return false; // Level1は初めから解放されている
            
            // 一つ下の難易度のデータ
            var prevContentsMaster =
                MasterDataManager.Instance.masterUltimateRaceContents.GetWithEventIdAndGroupIdAndDifficulty(Id,
                    raceContentsMaster.GroupId, raceContentsMaster.Difficulty - 1);
            if (prevContentsMaster == null) return false;
            var prevContents = _contentsOpenedDataList.Find(contents => contents.UltimateRaceId == prevContentsMaster.Id);
            if (prevContents == null) return false;
            
            // 一つ下の難易度をクリアしていない場合はロック中
            return !prevContents.IsCleared;
        }

        /// <summary>
        /// 出走中の殿堂入りウマ娘IDを設定する
        /// </summary>
        /// <param name="trainedCharaId"></param>
        public void SetEntryTrainedCharaId(int trainedCharaId)
        {
            EntryTrainedCharaId = trainedCharaId;
        }

        /// <summary>
        /// 殿堂入りウマ娘が出走中かどうか
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public bool IsEntryTrainedChara(int trainedCharaId)
        {
            return EntryTrainedCharaId == trainedCharaId;
        }

        /// <summary>
        /// ホームの「レース」タブでNEWバッジを表示するかどうか
        /// </summary>
        /// <returns></returns>
        public bool IsHomeNewBadgeNeeded()
        {
            return IsNew && IsInSession();
        }

        /// <summary>
        /// 出走中かどうか
        /// </summary>
        /// <returns></returns>
        public bool HasRunStarted()
        {
            return State != WorkRaceStateData.State.None;
        }
        #endregion

        #region クリア履歴

        /// <summary>
        /// クリア履歴のデータを登録する
        /// </summary>
        /// <param name="clearedRaceInfoArray"></param>
        public void ApplyClearHistoryData(ClearedRaceInfo[] clearedRaceInfoArray)
        {
            ResetClearHistoryDictionary();
            
            var clearedRaceInfoCount = clearedRaceInfoArray.Length;
            for (int i = 0; i < clearedRaceInfoCount; i++)
            {
                var clearedRaceInfo = clearedRaceInfoArray[i];
                _clearedTrainedCharaDictionary.Add(clearedRaceInfo.ultimate_race_id,
                    clearedRaceInfo.cleared_trained_chara_array);
            }
        }

        /// <summary>
        /// クリア履歴のデータを全て削除する
        /// </summary>
        public void ResetClearHistoryDictionary()
        {
            _clearedTrainedCharaDictionary.Clear();
        }

        #endregion
   }
}