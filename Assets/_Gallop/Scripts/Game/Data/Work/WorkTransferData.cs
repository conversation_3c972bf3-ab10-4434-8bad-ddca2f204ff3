using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 特別移籍イベントのワークデータ（基底）
    /// 特別移籍系の共通処理はこちらに
    /// それ以外のWorkに跨る共通処理の場合は、WorkDataUtilを利用してください
    /// </summary>
    public abstract class WorkTransferData
    {
        public abstract class DetailDataBase
        {
            /// <summary>あと何回移籍できるか</summary>
            public int RemainingNum { get; }
            
            /// <summary>抽選後の報酬データ</summary>
            public List<LotteryRewardData> LotteryRewardDataList = null;

            /// <summary>条件に一致する殿堂入りウマ娘一覧データ</summary>
            public Dictionary<int, WorkTrainedCharaData.TrainedCharaData> TrainedCharaDataDict = null;

            /// <summary>募集条件に合う殿堂入りウマ娘がいるか</summary>
            private bool _existTrainedChara = false;
            
            #region 抽象メソッド
            /// <summary> Masterの有効性チェック </summary>
            /// <returns>trueならnullではない</returns>
            protected abstract bool IsValidMaster();

            /// <summary> 回数制限のタイプをMasterから取得 </summary>
            /// <returns> 回数制限がない場合、もしくはMasterが取得できない場合は0を返す</returns>
            protected abstract int GetLimitedType();

            /// <summary> 期待度をMasterから取得 </summary>
            /// <returns> Masterが取得できない場合は0を返す</returns>
            public abstract int GetDifficulty();

            /// <summary> 期間中かどうか </summary>
            /// <returns>trueなら期間中</returns>
            protected abstract bool IsInTerm();
            #endregion
            
            protected DetailDataBase(TransferEventDetailInfo detailInfo)
            {
                if (detailInfo == null)
                    return;

                RemainingNum = detailInfo.remaining_num;
                _existTrainedChara = detailInfo.exists_trained_chara;

                LotteryRewardDataList = null;
                TrainedCharaDataDict = null;
            }

            /// <summary>
            /// バッジが必要かチェック
            /// </summary>
            /// <returns></returns>
            public bool CheckNeedBadge()
            {
                if (IsValidMaster() == false)
                    return false;

                // 移籍可能な残り回数が0ならバッジ不要
                if (RemainingNum <= 0)
                    return false;

                // 期間中チェック
                if (IsInTerm() == false)
                    return false;
                
                return _existTrainedChara;   // 募集条件に合う殿堂入りウマ娘がいるならバッジが必要
            }

            /// <summary>
            /// クリア済みか（回数制限があり、残り回数が0ならクリア済みとする）
            /// </summary>
            public bool IsClear()
                => GetLimitedType() != 0 && RemainingNum <= 0;
        }
        
        /// <summary>
        /// 抽選後の報酬1件のデータ
        /// </summary>
        public class LotteryRewardData
        {
            /// <summary>対象の殿堂入りウマ娘</summary>
            public int TrainedCharaId = 0;

            /// <summary>報酬情報</summary>
            public List<WorkDataUtil.RewardItemInfo> RewardItemInfoList = new List<WorkDataUtil.RewardItemInfo>();

            public LotteryRewardData(TransferEventRewardInfo rewardInfo)
            {
                if (rewardInfo == null)
                    return;

                TrainedCharaId = rewardInfo.trained_chara_id;

                if (rewardInfo.reward_array == null || rewardInfo.reward_array.Length <= 0)
                {
                    Debug.LogWarning("【特別移籍】サーバーから送られた TransferEventRewardInfo に reward_array が入っていません。（TrainedCharaId:" + TrainedCharaId + "）");
                    return;
                }

                foreach (var responseItem in rewardInfo.reward_array)
                {
                    var itemCategory = (GameDefine.ItemCategory)responseItem.item_type;
                    var itemId = responseItem.item_id;
                    var itemName = MasterDataManager.Instance.masterItemData.GetName(itemId);
                    var itemNum = responseItem.item_num;
                    if (itemCategory == GameDefine.ItemCategory.CARD_PIECE && itemId == 0)
                    {
                        // 報酬がピースでitemIdが空の場合、itemIdにカードIDを入れる
                        var trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(TrainedCharaId);
                        if (trainedChara != null)
                        {
                            itemId = trainedChara.CardId;
                        }
                    }
                    RewardItemInfoList.Add(new WorkDataUtil.RewardItemInfo(itemCategory, itemId, itemName, itemNum));
                }
            }
        }
        
        /// <summary>開催日当日5:00～開催中の特別移籍ID （期間外なら0が入ります）</summary>
        public int TransferId => _transferId;
        protected int _transferId;
        
        /// <summary>最大期待度</summary>
        public int MaxDifficulty => _maxDifficulty;
        protected int _maxDifficulty = 0;
        
#region 抽象メソッド
        protected abstract IEnumerable<DetailDataBase> GetDetailDataEnumerable();
        protected abstract DetailDataBase GetSelectedDetailData();
        protected abstract void ApplyDetailData(TransferEventDetailInfo[] detailInfo);
#endregion

        /// <summary>
        /// イベントTOPへ遷移時の情報の更新
        /// </summary>
        protected void UpdateAtIndex(int transferId, TransferEventDetailInfo[] transferDetailArray)
        {
            _transferId = transferId;

            if (transferDetailArray == null)
            {
                return;
            }
            
            // 募集情報更新
            ApplyDetailData(transferDetailArray);
        }

        /// <summary>
        /// 詳細画面へ遷移時の情報の更新
        /// </summary>
        /// <param name="rewardInfos"></param>
        protected void UpdateAtDetail(TransferEventRewardInfo[] rewardInfos)
        {
            var selectedDetailData = GetSelectedDetailData();
            
            if (selectedDetailData == null)
            {
                Debug.LogWarning("【特別移籍（常設）】API「TransferRotationDetail」で受け取った情報の設定先が見つかりません。");
                return; // ここには来ないはず
            }

            selectedDetailData.LotteryRewardDataList?.Clear(); 
            selectedDetailData.TrainedCharaDataDict?.Clear();

            if (rewardInfos == null || rewardInfos.Length <= 0)
                return;

            selectedDetailData.LotteryRewardDataList = new List<LotteryRewardData>();
            selectedDetailData.TrainedCharaDataDict = new Dictionary<int, WorkTrainedCharaData.TrainedCharaData>();
            foreach (var rewardInfo in rewardInfos)
            {
                // 抽選後の報酬データを収集
                var lotteryRewardData = new LotteryRewardData(rewardInfo);
                selectedDetailData.LotteryRewardDataList.Add(lotteryRewardData);

                // 条件に一致する殿堂入りウマ娘のデータを収集
                var trainedCharaId = lotteryRewardData.TrainedCharaId;
                if (selectedDetailData.TrainedCharaDataDict.ContainsKey(trainedCharaId))
                    continue;
                
                var trainedCharaData = WorkDataManager.Instance.TrainedCharaData.Get(trainedCharaId);
                if (trainedCharaData == null)
                    continue;

                selectedDetailData.TrainedCharaDataDict.Add(trainedCharaId, trainedCharaData);
            }
        }
        
        /// <summary>
        /// 特別移籍ボタンにバッジが必要かチェック
        /// </summary>
        /// <returns></returns>
        public bool CheckNeedBadgeAtTransferButton()
        {
            var detailDataEnumerable = GetDetailDataEnumerable();
            if (detailDataEnumerable.Any() == false)
                return false;
            
            foreach (var detailData in detailDataEnumerable)
            {
                // バッジが必要な募集が1件でもあるならバッジ必要
                if (detailData.CheckNeedBadge())
                    return true;
            }

            return false;
        }
        
        /// <summary>
        /// 移籍可能な回数が残っているかどうか
        /// </summary>
        /// <returns></returns>
        public bool RemainingTransferCount()
        {
            var detailDataEnumerable = GetDetailDataEnumerable();
            if (detailDataEnumerable.Any() == false)
                return false;
            
            foreach (var detailData in detailDataEnumerable)
            {
                // 移籍可能な回数が1つでもあるのであればtrue
                if (detailData.RemainingNum > 0)
                    return true;
            }

            return false;
        }
        
        /// <summary>
        /// 本日の募集の中で最大の期待度を探す
        /// </summary>
        protected int FindMaxDifficulty(IEnumerable<DetailDataBase> detailDataList)
        {
            if (detailDataList == null || detailDataList.Any() == false)
                return 0;

            var maxDifficulty = 0;
            foreach (var detailData in detailDataList)
            {
                var difficulty = detailData.GetDifficulty();
                if (difficulty > maxDifficulty)
                    maxDifficulty = difficulty;
            }

            return maxDifficulty;
        }
                
        /// <summary>
        /// 現在並んでいる募集の内、未クリアな募集の数を数える
        /// </summary>
        /// <returns></returns>
        public int CountNotClearDetailDataNum()
        {
            var detailDataEnumerable = GetDetailDataEnumerable();
            if (detailDataEnumerable.Any() == false)
                return 0;

            return detailDataEnumerable.Count(d => d.IsClear() == false);
        }
    }
}