using System.Linq;
using System.Collections.Generic;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    public partial class WorkSingleModeChangeParameterInfo
    {
        public WorkSingleModeChangeParameterInfoScenarioTeamRace ScenarioTeamRace = new WorkSingleModeChangeParameterInfoScenarioTeamRace();
    }
    /// <summary>
    /// チーム対抗戦編用
    /// </summary>
    public class WorkSingleModeChangeParameterInfoScenarioTeamRace
    {
        /// <summary>
        /// メンバーステータス上昇
        /// </summary>
        public class TeamRaceMember
        {
            public int CharacterId { get; set; }

            public string CharacterName { get; set; }
            public int Speed { get; set; }
            public int Stamina { get; set; }
            public int Power { get; set; }
            public int Guts { get; set; }
            public int Wiz { get; set; }
            
            /// <summary> 上限を超えて上昇したか </summary>
            public bool IsMaxUpSpeed { get; set; }
            public bool IsMaxUpStamina { get; set; }
            public bool IsMaxUpPower { get; set; }
            public bool IsMaxUpGuts { get; set; }
            public bool IsMaxUpWiz { get; set; }
        }

        /// <summary> チーム対抗戦：メンバーステータス上昇 </summary>
        public readonly List<TeamRaceMember> SemiMemberStatusList = new List<TeamRaceMember>();
        
        /// <summary> チーム対抗戦：セミメンバー化 </summary>
        public readonly List<TeamRaceMember> SemiMemberList = new List<TeamRaceMember>();
        
        /// <summary> チーム対抗戦：チームメンバー化 </summary>
        public readonly List<TeamRaceMember> JoinMemberList = new List<TeamRaceMember>();
        
        /// <summary> チーム対抗戦：メンバー解除 </summary>
        public readonly List<TeamRaceMember> LeaveMemberList = new List<TeamRaceMember>();

        
        /// <summary> チーム対抗戦：チームランキング上昇時のランキング値 </summary>
        public ObscuredInt RankingUp { get; private set; }
        /// <summary> チーム対抗戦：チームランキング下降時のランキング値 </summary>
        public ObscuredInt RankingDown { get; private set; }
        /// <summary> チーム対抗戦：チームステータス変化 </summary>
        public Dictionary<SingleModeDefine.ParameterType,int> TeamStatusUpDictionary { get; private set; } = new Dictionary<SingleModeDefine.ParameterType,int>();
        /// <summary> チーム対抗戦：チーム総合力UP </summary>
        public ObscuredInt TotalPower { get; private set; }

        public void Clear()
        {
            SemiMemberStatusList.Clear();
            SemiMemberList.Clear();
            JoinMemberList.Clear();
            LeaveMemberList.Clear();
            RankingUp = 0;
            TeamStatusUpDictionary.Clear();
            RankingDown = 0;
            TotalPower = 0;
        }
        
        /// <summary>
        /// チーム対抗戦パラメータ差分保存
        /// </summary>
        public void Set(
            WorkSingleModeData srcData,
            SingleModeTeamDataSet destTeamDataSet,
            WorkSingleModeChangeParameterInfo rootInfo)
        {
            if (destTeamDataSet == null || srcData == null) return;
            WorkSingleModeCharaData srcCharaInfo = srcData.Character;
            if (srcCharaInfo == null) return;
            
            var srcTeamRace = srcCharaInfo.TeamRace;
            var destTeamInfo = destTeamDataSet.team_info;
            if (destTeamInfo == null) return;

            //特攻キャラ一覧を取得
            var specialCharaArray = rootInfo.AppearCharaIdList.Where(id =>
            {
                //特攻キャラは登場時からメンバー化しているため登場キャラ一覧の興味状態を参照する
                var eval = destTeamDataSet.evaluation_info_array.FirstOrDefault(e => e.target_id == id);
                if(eval == null)
                {
                    return false;
                }
                return (SingleModeScenarioTeamRaceDefine.InterestState)eval.member_state == SingleModeScenarioTeamRaceDefine.InterestState.TeamMember;
            }).ToArray(); //即時評価する
            
            //特攻キャラ一をトレーニング出現一覧から消す
            for(var i = 0 ; i < specialCharaArray.Length ; ++i)
            {
                var appearId = specialCharaArray[i];
                //登場演出は表示しないのでこちらからは消す
                rootInfo.AppearCharaIdList.Remove(appearId);
            }
            
            // 興味変化チェック
            foreach (var destEvaluationInfo in destTeamDataSet.evaluation_info_array)
            {
                var srcEvaluationInfo = srcCharaInfo.EvaluationList.Find(a => a.TargetId == destEvaluationInfo.target_id);
                var destInterestState = (SingleModeScenarioTeamRaceDefine.InterestState)destEvaluationInfo.member_state;
                if (srcEvaluationInfo == null)
                {
                    //レース後メンバー加入するのでsrcに情報がない
                    if(destInterestState == SingleModeScenarioTeamRaceDefine.InterestState.TeamMember)
                    {
                        var member = new TeamRaceMember {CharacterId = destEvaluationInfo.chara_id, CharacterName = MasterDataUtil.GetCharaNameByCharaId(destEvaluationInfo.chara_id)};
                        JoinMemberList.Add(member);
                    }
                    continue;
                }

                // 興味に変化あり
                if (srcEvaluationInfo.InterestState != destInterestState)
                {
                    var member = new TeamRaceMember {CharacterId = srcEvaluationInfo.GuestCharaId, CharacterName = MasterDataUtil.GetCharaNameByCharaId(srcEvaluationInfo.GuestCharaId)};
                    switch (destInterestState)
                    {
                        case SingleModeScenarioTeamRaceDefine.InterestState.Guest:
                            LeaveMemberList.Add(member);
                            break;
                        case SingleModeScenarioTeamRaceDefine.InterestState.SemiMember:
                            SemiMemberList.Add(member);
                            break;
                        case SingleModeScenarioTeamRaceDefine.InterestState.TeamMember:
                            JoinMemberList.Add(member);
                            break;
                    }
                }
            }

            // チームメンバーのステータス上昇
            if (destTeamInfo.team_chara_info_array != null)
            {
                foreach (var destMemberInfo in destTeamInfo.team_chara_info_array)
                {
                    var charaId = SingleModeUtils.GetCharaIdByAllPosition(destMemberInfo.training_partner_id);
                    //NOTE:@guo_xu 新規メンバーが参加した時ここも通るので、参加したフレームでTeamMemberがnullになりえるので、ワーニングを抑制する
                    var srcMember = SingleModeScenarioTeamRaceUtils.GetTeamMemberByCharaId(charaId, false);
                    if (srcMember == null) continue;
                    
                    // 上昇差分
                    var deltaSpeed = destMemberInfo.speed - srcMember.Speed;
                    var deltaStamina = destMemberInfo.stamina - srcMember.Stamina;
                    var deltaPower = destMemberInfo.power - srcMember.Power;
                    var deltaGuts = destMemberInfo.guts - srcMember.Guts;
                    var deltaWiz = destMemberInfo.wiz - srcMember.Wiz;
                    // 差分なし
                    if( deltaSpeed == 0 && deltaStamina == 0 && deltaPower == 0 && deltaGuts == 0 &&deltaWiz == 0)continue;
                    
                    var member = new TeamRaceMember
                    {
                        CharacterId = srcMember.CharaId,
                        CharacterName = MasterDataUtil.GetCharaNameByCharaId(srcMember.CharaId),
                        Speed = deltaSpeed, Stamina = deltaStamina, Power = deltaPower, Guts = deltaGuts, Wiz = deltaWiz,
                        IsMaxUpSpeed   = srcMember.SpeedLimit   < destMemberInfo.speed,
                        IsMaxUpStamina = srcMember.StaminaLimit < destMemberInfo.stamina,
                        IsMaxUpPower   = srcMember.PowerLimit   < destMemberInfo.power,
                        IsMaxUpGuts    = srcMember.GutsLimit    < destMemberInfo.guts,
                        IsMaxUpWiz     = srcMember.WizLimit     < destMemberInfo.wiz,
                    };
                    SemiMemberStatusList.Add(member);
                }
            }

            // ランキング
            if (srcTeamRace.TeamRanking > destTeamInfo.team_rank)
            {
                RankingUp = destTeamInfo.team_rank;
            }
            else if (srcTeamRace.TeamRanking < destTeamInfo.team_rank)
            {
                RankingDown = destTeamInfo.team_rank;
            }
            
            var deltaTeamSpeed =  destTeamInfo.speed_rank - (int)srcTeamRace.TeamParameterRankSpeed; 
            if( deltaTeamSpeed != 0)
            {
                TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Speed,destTeamInfo.speed_rank);
            }

            var deltaTeamStamina =  destTeamInfo.stamina_rank - (int)srcTeamRace.TeamParameterRankStamina; 
            if( deltaTeamStamina != 0)
            {
                TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Stamina,destTeamInfo.stamina_rank);
            }

            var deltaTeamPower =  destTeamInfo.power_rank - (int)srcTeamRace.TeamParameterRankPower; 
            if( deltaTeamPower > 0)
            {
                TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Power,destTeamInfo.power_rank);
            }

            var deltaTeamGuts =  destTeamInfo.guts_rank - (int)srcTeamRace.TeamParameterRankGuts; 
            if( deltaTeamGuts > 0)
            {
                TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Guts,destTeamInfo.guts_rank);
            }

            var deltaTeamWiz =  destTeamInfo.wiz_rank - (int)srcTeamRace.TeamParameterRankWiz; 
            if( deltaTeamWiz > 0)
            {
                TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Wiz,destTeamInfo.wiz_rank);
            }


            // 総合力
            if ((int)srcTeamRace.TeamTotalPower != destTeamInfo.team_power)
            {
                TotalPower = destTeamInfo.team_power;
            }
            
#if CYG_DEBUG
            if (DebugPageSingleModeScenarioTeamRace.OverrideStoryEventParamChangeTeamRace)
            {
                SetDebugDataTeamRace();   
            }
#endif
        }
        
#if CYG_DEBUG
        /// <summary>
        /// デバッグ機能でパラメータアップ演出用の値を設定：チーム対抗戦
        /// </summary>
        private void SetDebugDataTeamRace()
        {
            var cardData = WorkDataManager.Instance.SingleMode.Character.CardData;
            var charaName = cardData.Charaname;
            var charaId = cardData.CharaId;
            var member = new TeamRaceMember {CharacterId = charaId, CharacterName = charaName, Speed = 123, Stamina = 9, Power = 34, Guts = 567, Wiz = 89};
            for(int i = 0 ; i < DebugPageSingleModeScenarioTeamRace.OverrideStoryEventParamChangeTeamRaceMemberJoinNum ; ++i)
            {
                JoinMemberList.Add(member);
            }
            RankingUp = 98;
            RankingDown = 76;
            TotalPower = (int)SingleModeScenarioTeamRaceDefine.TeamTotalPower.A;
            TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Speed,(int)SingleModeScenarioTeamRaceDefine.TeamParameterRank.A);
            TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Stamina,(int)SingleModeScenarioTeamRaceDefine.TeamParameterRank.B);
            TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Power,(int)SingleModeScenarioTeamRaceDefine.TeamParameterRank.C);
            TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Guts,(int)SingleModeScenarioTeamRaceDefine.TeamParameterRank.D);
            TeamStatusUpDictionary.Add(SingleModeDefine.ParameterType.Wiz,(int)SingleModeScenarioTeamRaceDefine.TeamParameterRank.E);
        }        
#endif
    }
}
