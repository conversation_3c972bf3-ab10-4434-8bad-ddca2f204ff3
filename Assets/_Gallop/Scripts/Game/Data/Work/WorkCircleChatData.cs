using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using CodeStage.AntiCheat.ObscuredTypes;
using System;

namespace Gallop
{

    /// <summary>
    /// チャット関連データ
    /// </summary>
    public class WorkCircleChatData
    {
        #region 定数

        /// <summary>
        /// チャットメッセージタイプ
        /// </summary>
        public enum MessageType
        {
            /// <summary>システムメッセージ</summary>
            SYSTEM_MESSAGE_USER_NAME = 0,
            /// <summary>メッセージ</summary>
            MESSAGE = 1,
            /// <summary>スタンプ</summary>
            STAMP = 2,
            /// <summary>システムメッセージ(可変)</summary>
            SYSTEM_MESSAGE_VARIABLE = 3,
            /// <summary>ルームマッチ招待</summary>
            ROOM_MATCH_INVITE = 4,
            /// <summary> アイテムリクエスト </summary>
            ITEM_REQUEST = 5,
            /// <summary>未読(クライアント内部のみ使用するパラメータ)</summary>
            UNREAD = 6,
            /// <summary> 練習パートナー共有 </summary>
            PRACTICE_PARTNER_SHARE = 7,
            /// <summary> 練習パートナーコメント </summary>
            PRACTICE_PARTNER_SHARE_COMMENT = 8,
            
            
            /// <summary>無効値</summary>
            INVALID_VALUE = -1,
        }

        /// <summary>
        /// チャットのシステムメッセージID
        /// </summary>
        public enum SYSTEM_MESSAGE_ID
        {
            /// <summary>結成</summary>
            ORGANIZATION　= 1,
            /// <summary>加入</summary>
            JOIN,
            /// <summary>脱退</summary>
            LEAVE,
            /// <summary>リーダー就任</summary>
            LEADER,
            /// <summary>サブリーダー就任</summary>
            SUB_LEADER,
            /// <summary>サポート発動</summary>
            CLUB_SUPPORT_ON,
            /// <summary>サポート終了</summary>
            CLUB_SUPPORT_OFF,
            /// <summary>クラブ名変更</summary>
            NAME_CHANGE,
            /// <summary>無効値</summary>
            INVALID_VALUE = -1,
        }

        /// <summary>チャット最大表示数</summary>
        public const int CHAT_MAX_NUM = 100;

        #endregion

        #region チャットデータリスト

        /// <summary>
        /// チャットデータリスト
        /// ** 表示件数よりも多く保存している
        /// </summary>
        public List<ChatMessageInfo> ChatDataList { get { return _chatDataList; } }
        private List<ChatMessageInfo> _chatDataList = new List<ChatMessageInfo>();
        // チャットのメンバー情報 KeyはViewId
        public Dictionary<long, ChatMemberInfo> ChatMemberInfoDic { get; } = new Dictionary<long, ChatMemberInfo>();

        #region アイテムリクエスト

        //アイテムリクエストリスト
        //RequestIdでユニーク
        public Dictionary<int, ItemRequestInfo> ItemRequestDic { get; private set; } = new Dictionary<int, ItemRequestInfo>();

        //毎回ポーリングで呼ばれるからある時だけリストのインスタンスを返す
        private List<CircleItemDonate> GetDonateList(int requestId , CircleItemDonate[] itemDonateDataArray)
        {
            if (itemDonateDataArray == null || itemDonateDataArray.Length == 0)
                return null;

            List<CircleItemDonate> donateList = null;
            for (int i = 0; i < itemDonateDataArray.Length; i++)
            {
                if (itemDonateDataArray[i].request_id == requestId)
                {
                    if(donateList == null)
                    {
                        donateList = new List<CircleItemDonate>();
                    }
                    donateList.Add(itemDonateDataArray[i]);
                }
            }
            return donateList;
        }

        /// <summary>
        /// アイテムリクエスト辞書の更新
        /// </summary>
        public void UpdateItemRequestList(CircleItemRequest[] itemRequestDataArray, CircleItemDonate[] itemDonateDataArray)
        {
            //リクエストデータの更新
            for (int i = 0; i < itemRequestDataArray.Length; i++)
            {
                var request = itemRequestDataArray[i];
                var key = request.request_id;
                if (ItemRequestDic.ContainsKey(key))
                    ItemRequestDic[key].Update(request);    //インスタンスは保ったまま更新する
                else
                    ItemRequestDic.Add(key, new ItemRequestInfo(request));
            }
            //寄付の更新、ポーリングだと寄付データだけ別で送られてくるので別でループ回す
            foreach(var kv in ItemRequestDic)
            {
                var donateList = GetDonateList(kv.Key, itemDonateDataArray);
                kv.Value.UpdateDonateInfoList(donateList);
            }
        }

        /// <summary>
        /// アイテムリクエスト辞書を初期化
        /// </summary>
        public void InitializeItemRequestList(CircleItemRequest[] itemRequestDataArray, CircleItemDonate[] itemDonateDataArray)
        {
            if (itemRequestDataArray == null || itemDonateDataArray == null)
                return;

            ItemRequestDic.Clear();
            UpdateItemRequestList(itemRequestDataArray, itemDonateDataArray);
        }

        /// <summary>
        /// 引数ユーザーがアイテムリクエストをかけた一番最後のデータを取得する(終了してたとしても取得する)
        /// </summary>
        /// <param name="viewerId"></param>
        /// <returns></returns>
        public ItemRequestInfo GetItemRequestMessageInfo(long viewerId)
        {
            ItemRequestInfo info = null;
            long tempTime = 0;
            foreach(var kv in ItemRequestDic)
            {
                if(kv.Value.ViewerId == viewerId && tempTime < kv.Value.EndTimeStamp)
                {
                    info = kv.Value;
                    tempTime = kv.Value.EndTimeStamp;
                }
            }
            return info;
        }

        /// <summary>
        /// データ削除
        /// </summary>
        /// <param name="requestId"></param>
        public void RemoveItemRequestData(int requestId)
        {
            var key = requestId;
            if(!ItemRequestDic.TryGetValue(key, out var data))
            {
                Debug.LogWarning("keyが無い reqId = " + requestId);
                return;
            }

            ItemRequestDic.Remove(key);
        }

        /// <summary>
        /// データ取得
        /// </summary>
        /// <param name="requestId"></param>
        /// <returns></returns>
        public ItemRequestInfo GetItemRequestInfo(int requestId)
        {
            var key = requestId;
            if (!ItemRequestDic.TryGetValue(key, out var data))
            {
                Debug.LogWarning("keyが無い reqId = " + requestId);
                return null;
            }
            return data;
        }

        /// <summary>
        /// 現在有効なアイテムリクエストの数を返す
        /// </summary>
        /// <returns></returns>
        public int GetEnableItemReqCount()
        {
            var num = 0;
            foreach(var val in ItemRequestDic.Values)
            {
                if (val.IsEnd)
                    continue;

                num++;
            }
            return num;
        }

        #endregion


        #region ルームマッチ招待

        // ルームマッチ招待.
        // RequestId＋そこから取れるViewIdでユニーク.
        public Dictionary<int, RoomMatchInviteInfo> RoomMatchInviteDic { get; private set; } = new Dictionary<int, RoomMatchInviteInfo>();
        public Dictionary<int, long> RoomMatchInviteEndDic { get; private set; } = new Dictionary<int, long>();

        /// <summary>
        /// ルームマッチ招待辞書を初期化
        /// </summary>
        public void InitializeRoomMatchInviteList(RoomMatchRoomInfo[] roomMatchInviteDataArray, CircleChatMessage[] circleChatMessageArray)
        {
            if (roomMatchInviteDataArray == null || circleChatMessageArray == null)
                return;

            RoomMatchInviteDic.Clear();
            RoomMatchInviteEndDic.Clear();
            UpdateRoomMatchInviteList(roomMatchInviteDataArray, circleChatMessageArray);
        }

        /// <summary>
        /// ルームマッチ招待辞書の更新
        /// </summary>
        public void UpdateRoomMatchInviteList(RoomMatchRoomInfo[] roomMatchInviteDataArray, CircleChatMessage[] circleChatMessageArray)
        {
            //ルームマッチ招待データの更新
            for (int i = 0; i < roomMatchInviteDataArray.Length; i++)
            {
                var invite = roomMatchInviteDataArray[i];
                var key = invite.room_id;
                if (RoomMatchInviteDic.ContainsKey(key))
                    RoomMatchInviteDic[key].Update(invite);    //インスタンスは保ったまま更新する
                else
                    RoomMatchInviteDic.Add(key, new RoomMatchInviteInfo(invite));
            }

            // 入室時に終了済ルームマッチ辞書の更新(IDのみ記憶).
            for (int i = 0; i < circleChatMessageArray.Length; i++)
            {
                if (circleChatMessageArray[i].message_type != (int)MessageType.ROOM_MATCH_INVITE)
                {
                    continue;
                }

                var key = circleChatMessageArray[i].message_id;
                if (!RoomMatchInviteDic.ContainsKey(key)
                 && !RoomMatchInviteEndDic.ContainsKey(key))
                {
                    RoomMatchInviteEndDic.Add(key, circleChatMessageArray[i].viewer_id);
                }
            }
        }

        #endregion
        
        #region 練習パートナー共有
        
        // 投稿されたウマ娘はViewID+育成ウマ娘IDでユニーク
        // サークルで共有されているウマ娘のリストを持っておき、データ操作の補助として自身が投稿したウマ娘のDictionaryとメンバーが何回パートナーを投稿したかのDictionaryを持つ
        
        /// <summary>
        /// 練習パートナー共有リスト
        /// </summary>
        public List<PracticePartnerInfo> PostPracticePartnerList = new List<PracticePartnerInfo>();

        /// <summary>
        /// 自身が共有した殿堂入りウマ娘のパートナーID
        /// </summary>
        public Dictionary<int, PracticePartnerInfo> PostPracticePartnerMineDictionary { get; private set; } = new Dictionary<int, PracticePartnerInfo>();

        /// <summary>
        /// サークルメンバーがそれぞれ何回練習パートナーを投稿したか(ViewID、回数)
        /// </summary>
        public Dictionary<long, int> PracticePartnerPostCount { get; private set; } = new Dictionary<long, int>();

        /// <summary>
        /// viewID、育成ウマ娘IDで既にサークル内で共有されているか確かめる,されていたらListの要素を返す
        /// </summary>
        /// <param name="viewerID"></param>
        /// <returns></returns>
        public PracticePartnerInfo GetPostedPracticePartner(long viewerID, int trainedCharaID)
        {
            if (PostPracticePartnerList.Count > 0)
            {
                for (int i = 0; i < PostPracticePartnerList.Count; i++)
                {
                    if (PostPracticePartnerList[i].TrainedCharaData.ViewerId == viewerID && PostPracticePartnerList[i].TrainedCharaData.Id == trainedCharaID)
                    {
                        return PostPracticePartnerList[i];
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// 有効な投稿の数を返す
        /// </summary>
        /// <returns></returns>
        public int GetEnablePartnerShareCount()
        {
            return PostPracticePartnerList.Count;
        }

        /// <summary>
        /// 共有されているウマ娘リストの外部からの初期化
        /// </summary>
        public void ClearPracticePostPartnerList()
        {
            PostPracticePartnerList.Clear();
            PostPracticePartnerMineDictionary.Clear();
            PracticePartnerPostCount.Clear();
        }
        
        /// <summary>
        /// 練習パートナー共有辞書の更新
        /// </summary>
        public void UpdatePracticePartnerList(CirclePostPartner[] circlePostPartnerArray)
        {
            //練習パートナー共有の更新
            for (int i = 0; i < circlePostPartnerArray.Length; i++)
            {
                var practicePartnerInfo = circlePostPartnerArray[i].practice_partner_info;
                var practicePartnerInstance = GetPostedPracticePartner(practicePartnerInfo.viewer_id, practicePartnerInfo.trained_chara_id);
                if (practicePartnerInstance != null)
                {
                    practicePartnerInstance.Update(circlePostPartnerArray[i]);    //インスタンスは保ったまま更新する   
                }
                else
                    PostPracticePartnerList.Add(new PracticePartnerInfo(circlePostPartnerArray[i]));
                //自身のviewIDなら
                if (circlePostPartnerArray[i].practice_partner_info.viewer_id == Certification.ViewerId)
                {
                    var key = practicePartnerInfo.trained_chara_id;
                    if (PostPracticePartnerMineDictionary.ContainsKey(key))
                    {
                        PostPracticePartnerMineDictionary[key].Update(circlePostPartnerArray[i]);    //インスタンスは保ったまま更新する
                    }
                    else
                    {
                        PostPracticePartnerMineDictionary.Add(key, new PracticePartnerInfo(circlePostPartnerArray[i]));
                    }
                }

                // 回数辞書を作っておく(投稿後、pollingでのチェックに使用)
                if (PracticePartnerPostCount.ContainsKey(practicePartnerInfo.viewer_id))
                {
                    PracticePartnerPostCount[practicePartnerInfo.viewer_id]++;
                }
                else
                {
                    PracticePartnerPostCount.Add(practicePartnerInfo.viewer_id, 1);
                }
            }
        }

        /// <summary>
        /// 練習パートナー共有辞書を初期化
        /// </summary>
        public void InitializePracticePartnerList(CirclePostPartner[] circlePostPartnerArray)
        {
            if (circlePostPartnerArray == null)
                return;

            PostPracticePartnerList.Clear();
            PostPracticePartnerMineDictionary.Clear();
            PracticePartnerPostCount.Clear();
            UpdatePracticePartnerList(circlePostPartnerArray);
        }

        #endregion

        /// <summary>
        /// チャットデータの初期化
        /// </summary>
        public void InitializeChatDataList(CircleRoomEnterResponse.CommonResponse response)
        {
            _chatDataList.Clear();
            UpdateChatData(response);
        }


        /// <summary>
        /// チャット情報更新
        /// </summary>
        /// <param name="messageList"></param>
        /// <param name="userArray"></param>
        public void UpdateChatData(List<ChatMessageInfo> messageList, CircleChatUser[] userArray)
        {
            foreach (var message in messageList)
            {
                var chatData = _chatDataList.FirstOrDefault(a => a.MessageId == message.MessageId);
                //post_idが同じなら新しいので上書き
                if(chatData != null)
                {
                    _chatDataList[_chatDataList.IndexOf(chatData)] = message;
                }
                else
                {
                    _chatDataList.Add(message);
                }
            }

            UpdateChatUserData(userArray);
        }

        // レスポンス毎のオーバーロード用受け子
        public List<ChatMessageInfo> UpdateChatData(
            int interval,
            CircleChatUser[] userList,
            CircleChatMessage[] messageList,
            CircleItemRequest[] itemRequestArray,
            CircleItemDonate[] itemDonateArray,
            RoomMatchRoomInfo[] roomMatchInfoArray,
            CirclePostPartner[] circlePostPartnerInfoArray)
        {
            ChatWaitTime = interval;

            //アイテムリクエストの更新
            UpdateItemRequestList(itemRequestArray, itemDonateArray);

            var messages = GetMessageInfoList(
                userList,
                messageList,
                itemRequestArray,
                itemDonateArray,
                roomMatchInfoArray,
                circlePostPartnerInfoArray
            );

            UpdateChatData(messages, userList);

            return messages;
        }
        // レスポンス毎のオーバーロード
        public List<ChatMessageInfo> UpdateChatData(CircleRoomEnterResponse.CommonResponse response)
        {
            if (response == null || response.summary_user_info_array == null)
                return null; //０人はおかしい

            //チャットユーザーレスポンスに変換する
            var chatUserArray = new CircleChatUser[response.summary_user_info_array.Length];
            for(int i = 0; i< response.summary_user_info_array.Length;i++)
            {
                var userInfo = response.summary_user_info_array[i];
                var chatUser = new CircleChatUser();
                chatUser.viewer_id = userInfo.viewer_id;
                chatUser.leader_chara_id = userInfo.leader_chara_id;
                chatUser.leader_chara_dress_id = userInfo.leader_chara_dress_id;
                chatUser.name = userInfo.name;
                chatUserArray[i] = chatUser;
            }

            return UpdateChatData(
                response.chat_polling_interval,
                chatUserArray,
                response.circle_chat_message_array,
                response.circle_item_request_array,
                response.circle_item_donate_array,
                response.room_match_info_array,
                response.circle_post_partner_array
            );
        }
        public List<ChatMessageInfo> UpdateChatData(CircleChatSendMessageResponse.CommonResponse response)
        {
            return UpdateChatData(
                response.chat_polling_interval,
                response.circle_chat_user_array,
                response.circle_chat_message_array,
                response.circle_item_request_array,
                response.circle_item_donate_array,
                response.room_match_info_array,
                null
            );
        }
        public List<ChatMessageInfo> UpdateChatData(CircleChatSendStampResponse.CommonResponse response)
        {
            return UpdateChatData(
                response.chat_polling_interval,
                response.circle_chat_user_array,
                response.circle_chat_message_array,
                response.circle_item_request_array,
                response.circle_item_donate_array,
                response.room_match_info_array,
                null
            );
        }
        public List<ChatMessageInfo> UpdateChatData(CircleChatSendItemRequestResponse.CommonResponse response)
        {
            return UpdateChatData(
                response.chat_polling_interval,
                response.circle_chat_user_array,
                response.circle_chat_message_array,
                response.circle_item_request_array,
                response.circle_item_donate_array,
                response.room_match_info_array,
                null
            );
        }
        
        public List<ChatMessageInfo> UpdateChatData(CircleChatPostPartnerResponse.CommonResponse response)
        {
            return UpdateChatData(
                response.chat_polling_interval,
                response.circle_chat_user_array,
                response.circle_chat_message_array,
                response.circle_item_request_array,
                response.circle_item_donate_array,
                response.room_match_info_array,
                response.circle_post_partner_array
            );
        }

        public List<ChatMessageInfo> UpdateChatData(CircleChatPollingResponse.CommonResponse response)
        {
            return UpdateChatData(
                response.chat_polling_interval,
                response.circle_chat_user_array,
                response.circle_chat_message_array,
                response.circle_item_request_array,
                response.circle_item_donate_array,
                response.room_match_info_array,
                response.circle_post_partner_array
            );
        }

        #region Private Method

        /// <summary>
        /// メッセージ情報のリストを取得
        /// </summary>
        /// <param name="userArray"></param>
        /// <param name="messageArray"></param>
        /// <param name="raceInfoArray"></param>
        /// <param name="itemRequestArray"></param>
        /// <returns></returns>
        private List<ChatMessageInfo> GetMessageInfoList(
            CircleChatUser[] userArray,
            CircleChatMessage[] messageArray,
            CircleItemRequest[] itemRequestArray,
            CircleItemDonate[] itemDonateArray,
            RoomMatchRoomInfo[] roomMatchInfoArray,
            CirclePostPartner[] circlePostPartnerArray)
        {
            var messageList = new List<ChatMessageInfo>();
            if (messageArray == null || userArray == null)
                return messageList;
                
            for (int i = 0; i < messageArray.Length; i++)
            {
                var message = messageArray[i];
                MessageType type = (MessageType)message.message_type;

                // タイプ毎に表示データセット
                string messageParam = string.Empty;
                int stampId = 0;
                SYSTEM_MESSAGE_ID systemMessageId = SYSTEM_MESSAGE_ID.INVALID_VALUE;
                ItemRequestInfo itemRequestInfo = null;
                RoomMatchInviteInfo roomMatchInviteInfo = null;
                PracticePartnerInfo practicePartnerInfo = null;
                switch (type)
                {
                    case MessageType.SYSTEM_MESSAGE_USER_NAME:
                        // ユーザー名をパラメータに
                        var userName = "";
                        foreach(var user in userArray)
                        {
                            if(user.viewer_id == message.viewer_id)
                            {
                                userName = user.name;
                                break;
                            }
                        }
                        messageParam = userName;
                        systemMessageId = (SYSTEM_MESSAGE_ID)message.message_id;
                        break;
                    case MessageType.SYSTEM_MESSAGE_VARIABLE:
                        // サーバーからのmessage_dataをパラメータに
                        messageParam = message.message_data;
                        systemMessageId = (SYSTEM_MESSAGE_ID)message.message_id;
                        break;
                    case MessageType.MESSAGE:
                    case MessageType.PRACTICE_PARTNER_SHARE_COMMENT:
                        messageParam = message.message_data;
                        break;
                    case MessageType.STAMP:
                        stampId = message.message_id;
                        break;
                    case MessageType.ITEM_REQUEST:
                        //アイテムリクエスト
                        if(!ItemRequestDic.TryGetValue(message.message_id, out itemRequestInfo))
                        {
                            //受け取りが終了したデータはここに来る、終了扱いにする
                            itemRequestInfo = new ItemRequestInfo(message.viewer_id);
                        }
                        break;
                    case MessageType.ROOM_MATCH_INVITE:
                        // ルームマッチ招待.
                        {
                            if (RoomMatchInviteEndDic.ContainsKey(message.message_id))
                            {
                                // 終了辞書に既にあるデータは再度送られてきても終了済に.
                                roomMatchInviteInfo = new RoomMatchInviteInfo();
                            }
                            else if (!RoomMatchInviteDic.TryGetValue(message.message_id, out roomMatchInviteInfo))
                            {
                                // 登録辞書にも終了辞書にもないなら登録辞書に追加.
                                for (int j = 0; j < roomMatchInfoArray.Length; j++)
                                {
                                    if (roomMatchInfoArray[j].room_id != message.message_id)
                                    {
                                        continue;
                                    }
                                    var key = message.message_id;
                                    if (!RoomMatchInviteDic.ContainsKey(key))
                                    {
                                        roomMatchInviteInfo = new RoomMatchInviteInfo(roomMatchInfoArray[j]);
                                        RoomMatchInviteDic.Add(key, roomMatchInviteInfo);
                                    }
                                }

                                if (roomMatchInviteInfo == null)
                                {
                                    // ポーリング間に開催されて終了した場合
                                    roomMatchInviteInfo = new RoomMatchInviteInfo();
                                    RoomMatchInviteEndDic.Add(message.message_id, message.viewer_id);
                                }
                            }
                        }
                        break;
                    case MessageType.PRACTICE_PARTNER_SHARE:
                        for (int j = 0; j < circlePostPartnerArray.Length; j++)
                        {
                            //メッセージIDが違う、もしくはViewrIDが違えば飛ばす
                            if (circlePostPartnerArray[j].practice_partner_info.trained_chara_id != message.message_id || circlePostPartnerArray[j].practice_partner_info.viewer_id != message.viewer_id)
                            {
                                continue;
                            }
                            practicePartnerInfo = new PracticePartnerInfo(circlePostPartnerArray[j]);
                            // リストにないなら追加.
                            if (GetPostedPracticePartner(practicePartnerInfo.TrainedCharaData.ViewerId, practicePartnerInfo.TrainedCharaData.Id) == null)
                            {
                                PostPracticePartnerList.Add(practicePartnerInfo);
                                
                                //自身の投稿ウマ娘辞書チェック
                                var key = message.message_id;
                                if (!PostPracticePartnerMineDictionary.ContainsKey(key) && practicePartnerInfo.TrainedCharaData.ViewerId == Certification.ViewerId)
                                { 
                                    PostPracticePartnerMineDictionary.Add(key, practicePartnerInfo);
                                }

                                long viewerID = circlePostPartnerArray[j].practice_partner_info.viewer_id;
                                // メンバーの初投稿なら辞書追加、二回目以降ならカウント増やす
                                if (!PracticePartnerPostCount.ContainsKey(viewerID))
                                {
                                    PracticePartnerPostCount.Add(viewerID, 1);
                                }
                                else
                                {
                                    PracticePartnerPostCount[viewerID]++;
                                }
                            }
                        }
                        break;
                    default:
                        Debug.LogError("定義されていないMessageTypeです");
                        break;
                }

                var chatMessage = new ChatMessageInfo(
                    type,
                    message.post_id,
                    message.viewer_id,
                    TimeUtil.ToLocalDateTimeFromJstString(message.create_time),
                    messageParam,
                    stampId,
                    systemMessageId,
                    itemRequestInfo,
                    roomMatchInviteInfo,
                    practicePartnerInfo
                );
                messageList.Add(chatMessage);
            }

            // ウマ娘削除チェック
            CheckPostPracticePartner();
            
            return messageList;
        }

        /// <summary>
        /// 5件以上ウマ娘が共有されている時、レスポンスから削除されているウマ娘が無いか調べる
        /// </summary>
        private void CheckPostPracticePartner()
        {
            bool removed = false;
            foreach (var count in PracticePartnerPostCount)
            {
                if (count.Value > ServerDefine.CircleChatPostPartnerHoldCount)
                {
                    RemovePostPracticePartner(count.Key, count.Value - ServerDefine.CircleChatPostPartnerHoldCount);
                    removed = true;
                }
            }

            // ウマ娘が削除されていたら値を設定しなおし
            if (removed)
            {
                PracticePartnerPostCount.Clear();
                foreach (var practicePartnerInfo in PostPracticePartnerList)
                {
                    long viewerID = practicePartnerInfo.TrainedCharaData.ViewerId;
                    if (PracticePartnerPostCount.ContainsKey(viewerID))
                    {
                        PracticePartnerPostCount[viewerID]++;
                    }
                    else
                    {
                        PracticePartnerPostCount.Add(viewerID, 1);
                    }
                }
                
            }
        }

        /// <summary>
        /// 古いウマ娘共有削除
        /// </summary>
        /// <param name="messageList"></param>
        private void RemovePostPracticePartner(long viewID, int deleteCount)
        {
            List<PracticePartnerInfo> memberPostPartnerList = new List<PracticePartnerInfo>();
            // 1人のメンバーの練習ウマ娘投稿を抜き出す
            foreach (var practicePartnerInfo in PostPracticePartnerList)
            {
                if (practicePartnerInfo.TrainedCharaData.ViewerId == viewID)
                {
                    memberPostPartnerList.Add(practicePartnerInfo);
                }
            }
            //TimeStamp順にソート
            var memberPostDicTimeStamp = memberPostPartnerList.OrderBy(x => x.Time);
            
            //deleteCount文だけ削除用のキーを抜き出す
            List<int> deleteKeyList = new List<int>();
            int keyCount = 0;
            foreach (var memberPostInfo in memberPostDicTimeStamp)
            {
                deleteKeyList.Add(memberPostInfo.TrainedCharaData.Id);
                keyCount++;
                if(keyCount >= deleteCount)
                    break;
            }

            // 削除するキーが決まったら実際に削除
            for (int i = 0; i < deleteKeyList.Count; i++)
            {
                //チャットからウマ娘の投稿を消す
                RemovePracticePartnerChat(viewID, deleteKeyList[i]);
                
                //リスト内からデータを消しておく
                if (PostPracticePartnerMineDictionary.ContainsKey(deleteKeyList[i]))
                {
                    PostPracticePartnerMineDictionary.Remove(deleteKeyList[i]);
                }
                var practiceInfoInstance = GetPostedPracticePartner(viewID, deleteKeyList[i]);
                if (practiceInfoInstance != null)
                {
                    PostPracticePartnerList.Remove(practiceInfoInstance);
                }
            }
        }

        /// <summary>
        /// チャット欄から昔の共有ウマ娘を消す
        /// </summary>
        private void RemovePracticePartnerChat(long viewID, int trainedCharaId)
        {
            //チャットデータの中から削除するデータを探す
            var deleteChatMessageCharaInfoList = _chatDataList.FindAll(x =>　x.PracticePartnerInfo != null &&  x.ViewerId == viewID && x.PracticePartnerInfo.TrainedCharaData.Id == trainedCharaId);
            if(deleteChatMessageCharaInfoList != null)
            {
                foreach (var deleteChatData in deleteChatMessageCharaInfoList)
                {
                    //データを無効にする(UI更新用)
                    deleteChatData.PracticePartnerInfo.Enable = false;
                    //削除するデータに紐づくコメント
                    var deleteChatMessageComment = _chatDataList.Find(x => x.MessageId == deleteChatData.PracticePartnerInfo.CommentLinkID);
                    //チャットからコメントを消す
                    _chatDataList.Remove(deleteChatMessageComment);
                    
                }
            }
        }

        /// <summary>
        /// チャットユーザー情報更新
        /// </summary>
        /// <param name="userArray"></param>
        public void UpdateChatUserData(CircleChatUser[] userArray)
        {
            if (userArray == null)
            {
                return;
            }

            foreach (var user in userArray)
            {
                if (ChatMemberInfoDic.TryGetValue(user.viewer_id, out var data))
                {
                    data.Update(user);
                }
                else
                {
                    ChatMemberInfoDic.Add(user.viewer_id, new ChatMemberInfo(user));
                }
            }
        }
        #endregion

        #endregion



        #region データ取得

        /// <summary>チャット待ち時間</summary>
        public ObscuredInt ChatWaitTime = 0;

       /// <summary>
       /// viewer_idからチャットのメンバー情報を取得
       /// </summary>
       /// <param name="viewerId"></param>
       /// <returns></returns>
        public ChatMemberInfo GetChatMemberInfo(long viewerId)
        {
            if (ChatMemberInfoDic == null)
            {
                return null;
            }

            var chatMemberInfoList = ChatMemberInfoDic.Values.ToList();
                
            ChatMemberInfo memberInfo = null;
            
            for (int i = 0, count = chatMemberInfoList.Count; i < count; i++)
            {
                ChatMemberInfo tempMemberInfo = chatMemberInfoList[i];

                if (tempMemberInfo.ViewerId == viewerId)
                {
                    memberInfo = tempMemberInfo;

                    break;
                }
            }

            return memberInfo;
        }

        #endregion

        #region チャットデータ

        /// <summary>
        /// クラブチャット関連データ
        /// </summary>
        public class ChatMessageInfo
        {
            #region 変数、プロパティ

            public MessageType Type { get; private set; }

            //UI構築時にこのIDが頻繁に検索に使用されるため生のintで保持。
            public int MessageId { get; private set; }

            public long ViewerId { get; private set; }

            public DateTime Time { get; private set; }

            //  メッセージパラメータ（通常：メッセージ スタンプ:null システムメッセージ：パラメータ）
            public string MessageParam { get; private set; }

            //  スタンプ
            public int StampId { get; private set; }

            // システムメッセージID
            public SYSTEM_MESSAGE_ID SystemMessageId { get; private set; }

            // 表示キャラクターID
            public (int CharaId, int DressId) ChatCharaIds
            {
                get
                {
                    var memberList = WorkDataManager.Instance.CircleChatData.ChatMemberInfoDic;
                    if(memberList.TryGetValue(ViewerId, out var member))
                    {
                        // まずはチャットユーザーから取得、退会済みユーザーも含む
                        return (member.CharaId, member.DressId);
                    }
                    else if (CircleInternalDataManager.HasInstance() && CircleInternalDataManager.Instance.MemberDic.TryGetValue(ViewerId, out var userdata))
                    {
                        //チャットユーザーは１００件までしか持ってないので流れてしまったユーザーはサークル内データから取る
                        return (userdata.CharaId, userdata.DressId);
                    }
                    else
                    {
                        return (0, 0);
                    }
                }
            }

            // 名前
            public string Name
            {
                get
                {
                    var memberList = WorkDataManager.Instance.CircleChatData.ChatMemberInfoDic;
                    if (memberList.TryGetValue(ViewerId, out var member))
                    {
                        return member.Name;
                    }
                    else if (CircleInternalDataManager.HasInstance() && CircleInternalDataManager.Instance.MemberDic.TryGetValue(ViewerId, out var userdata))
                    {
                        //チャットユーザーは１００件までしか持ってないので流れてしまったユーザーはサークル内データから取る
                        return userdata.Name;
                    }
                    else
                    {
                        return string.Empty;
                    }
                }
            }
            
            // アイテムリクエスト
            public ItemRequestInfo ItemRequestInfo { get; private set; } = null;

            // ルーム紹介.
            public RoomMatchInviteInfo RoomMatchInviteInfo { get; private set; } = null;

            /// <summary>
            /// 練習パートナー共有
            /// </summary>
            public PracticePartnerInfo PracticePartnerInfo { get; private set; } = null;

            #endregion

            #region 初期化、セットアップ

            /// <summary>
            /// コンストラクタ
            /// </summary>
            public ChatMessageInfo(MessageType type, int messageId = 0, long viewerId = 0, DateTime time = default, string messageParam = null, int stampId = 0, WorkCircleChatData.SYSTEM_MESSAGE_ID systemMessageId = SYSTEM_MESSAGE_ID.INVALID_VALUE, ItemRequestInfo itemRequestInfo = null, RoomMatchInviteInfo roomMatchInviteInfo = null, PracticePartnerInfo trainedCharaInfo = null)
            {
                Type = type;
                MessageId = messageId;
                ViewerId = viewerId;
                Time = time;

                MessageParam = messageParam;
                StampId = stampId;
                SystemMessageId = systemMessageId;

                ItemRequestInfo = itemRequestInfo;

                RoomMatchInviteInfo = roomMatchInviteInfo;

                PracticePartnerInfo = trainedCharaInfo;
            }
            #endregion
        }
        /// <summary>
        /// チャットメンバー情報
        /// </summary>
        public class ChatMemberInfo
        {
            /// <summary>メンバーのViewrId</summary>
            public ObscuredLong ViewerId { get; private set; }

            /// <summary>名前</summary>
            public ObscuredString Name { get; private set; }

            /// <summary>リーダーキャラクター</summary>
            public ObscuredInt CharaId { get; private set; }

            /// <summary>リーダードレスID</summary>
            public ObscuredInt DressId { get; private set; }

            public ChatMemberInfo(CircleChatUser chatUser)
            {
                Update(chatUser);
            }

            public void Update(CircleChatUser chatUser)
            {
                ViewerId = chatUser.viewer_id;
                Name = chatUser.name;
                CharaId = chatUser.leader_chara_id;
                DressId = chatUser.leader_chara_dress_id;
            }
        }

        public class ItemRequestInfo
        {
            public int RequestId { get; private set; }
            public long ViewerId { get; private set; }
            public int ItemId { get; private set; }
            public string EndTime { get; private set; } = "";
            public List<ItemDonateInfo> ItemDonateInfoList { get; private set; } = new List<ItemDonateInfo>();
            public bool IsEnd   //終了しているか？
            {
                get
                {
                    if(EndTimeStamp == 0 || IsMax)
                    {
                        return true;
                    }
                    return EndTimeStamp < TimeUtil.GetServerTimeStamp();
                }
            }
            public ObscuredLong EndTimeStamp { get; private set; } = 0;
            public ObscuredBool IsMax => GetDonatedNum() >= ServerDefine.MaxDonateNumTotal;

            /// <summary>
            /// 自分が最大数寄付済みか
            /// </summary>
            public bool IsDonatedFromMe => GetDonateNumFromMe() >= ServerDefine.MaxDonateNumForRequest;

            public ItemRequestInfo(long viewerId)
            {
                //終了扱いデータ用
                ViewerId = viewerId;
                EndTimeStamp = 0;
            }

            public ItemRequestInfo(CircleItemRequest itemRequest)
            {
                if (itemRequest == null)
                {
                    Debug.LogWarning("itemRequestがnullです");
                    return;
                }
                Update(itemRequest);
            }

            //自身のフィールドを引数のフィールドで上書きする
            public void Update(CircleItemRequest itemRequest)
            {
                if (itemRequest == null)
                    return;

                RequestId = itemRequest.request_id;
                ViewerId = itemRequest.viewer_id;
                ItemId = itemRequest.item_id;
                var prevEndTime = EndTime;
                EndTime = itemRequest.end_time;
                if (EndTime != prevEndTime)
                {
                    //変わった時だけ変える、重いから
                    EndTimeStamp = TimeUtil.ToUnixTimeFromJstString(EndTime);
                }
            }

            //寄付の更新
            //寄付の取り消しは想定してない
            public void UpdateDonateInfoList(IList<CircleItemDonate> donateList)
            {
                if (donateList == null)
                    return; //nullもあり得る

                for(int i = 0; i< donateList.Count; i++)
                {
                    var donate = donateList[i];
                    var find = false;
                    for(int j = 0; j < ItemDonateInfoList.Count; j++)
                    {
                        if(ItemDonateInfoList[j].DonateId == donate.donate_id)
                        {
                            ItemDonateInfoList[j].Update(donate);
                            find = true;
                            break;
                        }
                    }

                    //未登録データが来たら追加
                    if (!find)
                    {
                        ItemDonateInfoList.Add(new ItemDonateInfo(donate));
                    }
                }
            }

            /// <summary>
            /// 現在集まってる寄付数を返す
            /// </summary>
            /// <returns></returns>
            public int GetDonatedNum()
            {
                if (ItemDonateInfoList == null)
                    return 0;

                var num = 0;
                for (int i = 0; i < ItemDonateInfoList.Count; i++)
                {
                    num += ItemDonateInfoList[i].ItemNum;
                }
                return num;
            }

            /// <summary>
            /// 自分が出したアイテムリクエストか？
            /// </summary>
            /// <returns></returns>
            public bool IsMine()
            {
                return ViewerId == WorkDataManager.Instance.UserData.ViewerId;
            }

            /// <summary>
            /// 残り時間文字列取得
            /// </summary>
            /// <returns></returns>
            public string GetRemainTimeString()
            {
                return TimeUtil.GetRestTimeString(EndTimeStamp);
            }

            /// <summary>
            /// 自分が寄付した数を返す
            /// </summary>
            /// <returns></returns>
            public int GetDonateNumFromMe()
            {
                if (ItemDonateInfoList == null)
                    return 0;

                var num = 0;
                for (int i = 0; i < ItemDonateInfoList.Count; i++)
                {
                    if(ItemDonateInfoList[i].ViewerId == Certification.ViewerId)
                    {
                        num += ItemDonateInfoList[i].ItemNum;
                    }
                }
                return num;
            }

            /// <summary>
            /// 寄付可能な最大数を返す
            /// </summary>
            /// <returns></returns>
            public int GetDonatableNum()
            {
                if (ItemDonateInfoList == null)
                {
                    return 0;
                }

                var currentDonatedNum = 0;
                var myDonatedNum = 0;
                for (int i = 0; i < ItemDonateInfoList.Count; i++)
                {
                    currentDonatedNum += ItemDonateInfoList[i].ItemNum;
                    if(ItemDonateInfoList[i].ViewerId == Certification.ViewerId)
                    {
                        myDonatedNum += ItemDonateInfoList[i].ItemNum;
                    }
                }
                
                // 全体の寄付可能な数と自分の寄付可能な数を比較して小さい方を返す
                return System.Math.Min(ServerDefine.MaxDonateNumTotal - currentDonatedNum, ServerDefine.MaxDonateNumForRequest - myDonatedNum);
            }

            /// <summary>
            /// 最後に自分が寄付した個数の取得
            /// </summary>
            /// <returns></returns>
            public int GetDonateNumFromMeLast()
            {
                long lastCreateTime = 0;
                var num = 0;
                for (int i = 0; i < ItemDonateInfoList.Count; i++)
                {
                    if (ItemDonateInfoList[i].ViewerId == Certification.ViewerId && ItemDonateInfoList[i].CreateTimeStamp >= lastCreateTime)
                    {
                        lastCreateTime = ItemDonateInfoList[i].CreateTimeStamp;
                        num = ItemDonateInfoList[i].ItemNum;
                    }
                }
                return num;
            }
        }

        public class ItemDonateInfo
        {
            public int DonateId { get; private set; }
            public int RequestId { get; private set; }
            public long ViewerId { get; private set; }
            public int ItemNum { get; private set; }
            public long CreateTimeStamp { get; set; } = 0;

            public ItemDonateInfo(CircleItemDonate itemDonate)
            {
                if (itemDonate == null)
                {
                    Debug.LogWarning("itemDonateがnullです");
                    return;
                }

                DonateId = itemDonate.donate_id;

                //時間の変換は重めの処理なのでnewされたときだけ実行
                CreateTimeStamp = TimeUtil.ToUnixTimeFromJstString(itemDonate.create_time);
                Update(itemDonate);
            }

            public void Update(CircleItemDonate itemDonate)
            {
                if (itemDonate == null)
                {
                    Debug.LogWarning("itemDonateがnullです");
                    return;
                }
                RequestId = itemDonate.request_id;
                ViewerId = itemDonate.viewer_id;
                ItemNum = itemDonate.item_num;
            }
        }

        public class RoomMatchInviteInfo

        {
            // 他ボタンのように情報を絞ろうとしても、最終的にRoomMatchRoomInfoでダイアログ生成するのでそのまま受け渡す.
            public RoomMatchRoomInfo RoomInfo { get; private set; }

            public string EndTime { get; private set; } = "";

            // 強制出走・開催キャンセルしたか.
            public bool ForceEnd { get; set; } = false;

            // 参加(ホスト・ゲスト・観戦)しているか.
            public bool IsEntry { get; set; } = false;

            // 現在の参加数.
            public int CurrentEntryNum { get; set; } = 0;

            //終了しているか？
            public bool IsEnd
            {
                get
                {
                    if (ForceEnd)
                    {
                        EndTimeStamp = 0;
                        return true;
                    }

                    if (EndTimeStamp == 0)
                    {
                        return true;
                    }
                    return EndTimeStamp < TimeUtil.GetServerTimeStamp();
                }
            }
            public ObscuredLong EndTimeStamp { get; private set; } = 0;

            public RoomMatchInviteInfo()
            {
                // 辞書にない不正データなので終了扱いにする.
                RoomInfo = null;
                EndTimeStamp = 0;
                ForceEnd = false;
                IsEntry = false;
                CurrentEntryNum = 0;
            }

            public RoomMatchInviteInfo(RoomMatchRoomInfo roomInfo)
            {
                RoomInfo = roomInfo;
                EndTimeStamp = 0;
                ForceEnd = false;
                IsEntry = false;
                CurrentEntryNum = 0;

                Update(roomInfo);
            }

            //自身のフィールドを引数のフィールドで上書きする
            public void Update(RoomMatchRoomInfo roomInfo)
            {
                if (roomInfo == null)
                    return;

                var prevEndTime = EndTime;
                EndTime = roomInfo.start_time;
                if (EndTime != prevEndTime)
                {
                    EndTimeStamp = TimeUtil.ToUnixTimeFromJstString(EndTime);
                }

                var roomList = WorkDataManager.Instance.RoomMatchData.MyEntryRoomList;
                if (roomList != null)
                {
                    IsEntry = roomList.Exists(x => x.RoomId == roomInfo.room_id);
                }

                CurrentEntryNum = roomInfo.current_entry_num;
            }

            /// <summary>
            /// 残り時間文字列取得
            /// </summary>
            /// <returns></returns>
            public string GetRemainTimeString()
            {
                return TimeUtil.GetRestTimeString(EndTimeStamp);
            }
        }
        
        /// <summary>
        /// 練習パートナー共有
        /// </summary>
        public class PracticePartnerInfo
        {
            public WorkTrainedCharaData.TrainedCharaData TrainedCharaData { get; private set; }
            public DateTime Time { get; private set; }
            public int CommentLinkID { get; private set; }
            public string TrainerName { get; private set; }
            public int LeaderCharaId { get; private set; }
            public int LeaderDressId { get; private set; }

            //有効か無効か(6人目の判定に使用)
            public bool Enable = true;

            public PracticePartnerInfo(CirclePostPartner circlePostPartner)
            {
                if (circlePostPartner == null)
                {
                    Debug.LogWarning("circlePostPartnerがnullです");
                    return;
                }
                Update(circlePostPartner);
            }

            //自身のフィールドを引数のフィールドで上書きする
            public void Update(CirclePostPartner circlePostPartner)
            {
                if (circlePostPartner == null)
                    return;
                
                TrainedCharaData = new WorkTrainedCharaData.TrainedCharaData(circlePostPartner.practice_partner_info);
                Time = TimeUtil.ToLocalDateTimeFromJstString(circlePostPartner.post_time);
                CommentLinkID = circlePostPartner.post_comment_id;
                
                if (CircleInternalDataManager.HasInstance() && WorkDataManager.Instance.CircleChatData.ChatMemberInfoDic.TryGetValue(TrainedCharaData.ViewerId, out var chatMember))
                {
                    //チャットから取得
                    TrainerName = chatMember.Name;
                    LeaderCharaId = chatMember.CharaId;
                    LeaderDressId = chatMember.DressId;
                }
                else if (CircleInternalDataManager.HasInstance() && CircleInternalDataManager.Instance.MemberDic.TryGetValue(TrainedCharaData.ViewerId, out var circleMember))
                {
                    //サークルメンバーから取得
                    TrainerName = circleMember.Name;
                    LeaderCharaId = circleMember.CharaId;
                    LeaderDressId = circleMember.DressId;
                }
                else
                {
                    TrainerName = string.Empty;
                    LeaderCharaId = GameDefine.INVALID_CHARA_ID;
                    LeaderDressId = GameDefine.INVALID_DRESS_ID;
                }

            }
        }
        
        #endregion
    }
}
