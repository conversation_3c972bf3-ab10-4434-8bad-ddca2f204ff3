using CodeStage.AntiCheat.ObscuredTypes;
using System.Collections.Generic;
using System.Linq;
using System;
using Cute.Http;

namespace Gallop
{
    /// <summary>
    /// 練習のWorkData
    /// </summary>
    public class WorkPracticeRaceData
    {
        #region クラス定義

        /// <summary>
        /// レースの状態(サーバー合わせ)
        /// </summary>
        public enum RaceState
        {
            None = 0,       // なし
            RaceStart = 1,  // レース開始
        }

        /// <summary>
        /// レース用データのベースクラス
        /// </summary>
        public class ExhibitionRaceDataBase
        {
            /// <summary> レースインスタンスId </summary>
            public int RaceInstanceId => _raceInstanceId;
            protected ObscuredInt _raceInstanceId;

            /// <summary> 天気 </summary>
            public ExhibitionRaceDefine.Weather Weather => (ExhibitionRaceDefine.Weather)_weather.GetDecrypted();
            protected ObscuredInt _weather = (int)ExhibitionRaceDefine.Weather.Random;

            /// <summary> バ場状態 </summary>
            public ExhibitionRaceDefine.GroundCondition GroundCondition => (ExhibitionRaceDefine.GroundCondition)_groundCondition.GetDecrypted();
            protected ObscuredInt _groundCondition = (int)ExhibitionRaceDefine.GroundCondition.Random;

            /// <summary> 季節 </summary>
            public ExhibitionRaceDefine.Season Season => (ExhibitionRaceDefine.Season)_season.GetDecrypted();
            protected ObscuredInt _season = (int)ExhibitionRaceDefine.Season.Random;

            /// <summary> やる気 </summary>
            public ExhibitionRaceDefine.Motivation Motivation => (ExhibitionRaceDefine.Motivation)_motivation.GetDecrypted();
            protected ObscuredInt _motivation = (int)ExhibitionRaceDefine.Motivation.Random;

            /// <summary> 合計出走人数 </summary>
            public int EntryNum => _entryNum;
            protected ObscuredInt _entryNum;
        }

        /// <summary>
        /// 練習レースデータ
        /// </summary>
        public class RaceResultData : ExhibitionRaceDataBase
        {
            #region Member, Property

            /// <summary> 練習レースID(サーバーのレース保持ID) </summary>
            public int PracticeRaceId => _practiceRaceId;
            private ObscuredInt _practiceRaceId;

            /// <summary> レースシナリオ </summary>
            public string RaceScenario => _raceScenario;
            private ObscuredString _raceScenario;

            /// <summary> ランダムシード </summary>
            public int RandomSeed => _randomSeed;
            private ObscuredInt _randomSeed;

            /// <summary> 開催設定の天気 </summary>
            public ExhibitionRaceDefine.Weather BeforeWeather => (ExhibitionRaceDefine.Weather)_beforeWeather.GetDecrypted();
            protected ObscuredInt _beforeWeather = (int)ExhibitionRaceDefine.Weather.Random;

            /// <summary> 開催設定のバ場状態 </summary>
            public ExhibitionRaceDefine.GroundCondition BeforeGroundCondition => (ExhibitionRaceDefine.GroundCondition)_beforeGroundCondition.GetDecrypted();
            protected ObscuredInt _beforeGroundCondition = (int)ExhibitionRaceDefine.GroundCondition.Random;

            /// <summary> 開催設定の季節 </summary>
            public ExhibitionRaceDefine.Season BeforeSeason => (ExhibitionRaceDefine.Season)_beforeSeason.GetDecrypted();
            protected ObscuredInt _beforeSeason = (int)ExhibitionRaceDefine.Season.Random;

            /// <summary> 開催設定のやる気 </summary>
            public ExhibitionRaceDefine.Motivation BeforeMotivation => (ExhibitionRaceDefine.Motivation)_beforeMotivation.GetDecrypted();
            protected ObscuredInt _beforeMotivation = (int)ExhibitionRaceDefine.Motivation.Random;

            #endregion

            #region Method

            /// <summary>
            /// 空のデータで初期化
            /// </summary>
            /// <param name="response"></param>
            public RaceResultData(int practiceRaceId)
            {
                _practiceRaceId = practiceRaceId;
            }

            /// <summary>
            /// サーバーのレスポンスから初期化
            /// </summary>
            /// <param name="response"></param>
            public RaceResultData(PracticeRaceRaceStartResponse.CommonResponse response)
            {
                _practiceRaceId = response.practice_race_id;
                Setup(response.race_result_info);
            }

            /// <summary>
            /// セットアップ
            /// </summary>
            /// <param name="resultInfo"></param>
            private void Setup(RaceResultInfo resultInfo)
            {
                _raceInstanceId = resultInfo.race_instance_id;
                _season = resultInfo.season;
                _weather = resultInfo.weather;
                _groundCondition = resultInfo.ground_condition;
                _entryNum = resultInfo.race_horse_data_array.Length;

                _raceScenario = resultInfo.race_scenario;
                _randomSeed = resultInfo.random_seed;
            }

            /// <summary>
            /// リプレイ時の情報追加
            /// </summary>
            /// <param name="response"></param>
            public void Setup(PracticeRaceRaceReplayResponse.CommonResponse response)
            {
                Setup(response.race_result_info);

                _beforeWeather = response.before_weather;
                _beforeGroundCondition = response.before_ground_condition;
                _beforeSeason = response.before_season;
                _beforeMotivation = response.before_motivation;
            }

            /// <summary>
            /// 開催情報を設定
            /// </summary>
            /// <param name="raceSettingInfo"></param>
            public void SetRaceSettingInfo(PracticeRaceRaceSettingInfo raceSettingInfo)
            {
                _beforeWeather = (int)raceSettingInfo.Weather;
                _beforeGroundCondition = (int)raceSettingInfo.GroundCondition;
                _beforeSeason = (int)raceSettingInfo.Season;
                _beforeMotivation = (int)raceSettingInfo.Motivation;
            }

            #endregion
        }

        /// <summary>
        /// 保存レース用データ
        /// </summary>
        public class SavedRaceData : RaceResultData
        {
            /// <summary> サーバーのお気に入りOn </summary>
            public const int SERVER_FAVORITE_VALUE = 1;

            /// <summary> お気に入りフラグ </summary>
            public bool IsFavorite => _isFavorite;
            private ObscuredBool _isFavorite;

            /// <summary> NPCを除く参加人数 </summary>
            public int CurrentEntryNum => _currentEntryNum;
            private ObscuredInt _currentEntryNum;

            /// <summary> 保存日時 </summary>
            public string SavedTime => _savedTime;
            private ObscuredString _savedTime;

            /// <summary> 保存日時 </summary>
            public long SavedUnixTime { get; private set; }

            /// <summary>
            /// コンストラクタ
            /// </summary>
            /// <param name="savedRaceInfo"></param>
            public SavedRaceData(PracticeRaceSavedRaceInfo savedRaceInfo)
                : base(savedRaceInfo.practice_race_id)
            {
                _raceInstanceId = savedRaceInfo.race_instance_id;
                _entryNum = savedRaceInfo.entry_num;
                _currentEntryNum = savedRaceInfo.user_entry_num;
                _isFavorite = savedRaceInfo.is_favorite == SERVER_FAVORITE_VALUE;
                _savedTime = savedRaceInfo.save_time;
                SavedUnixTime = TimeUtil.ToUnixTimeFromJstString(_savedTime);
            }

            /// <summary>
            /// お気に入り状態更新
            /// </summary>
            /// <param name="serverParam"></param>
            public void UpdateFavorite(int serverParam)
            {
                _isFavorite = serverParam == SERVER_FAVORITE_VALUE;
            }
        }

        /// <summary>
        /// 練習パートナー情報
        /// </summary>
        public class PracticePartnerData
        {
            #region Member, Property

            /// <summary> 殿堂入りウマ娘id </summary>
            public int TrainedCharaId => _trainedCharaId;
            private ObscuredInt _trainedCharaId;

            /// <summary> 元の所有者のViewerId </summary>
            public long OwnerViewerId => _ownerViewerId;
            private ObscuredLong _ownerViewerId;

            /// <summary> 元の所有者の時の殿堂入りウマ娘id </summary>
            public int OwnerTrainedCharaId => _ownerTrainedCharaId;
            private ObscuredInt _ownerTrainedCharaId;

            /// <summary> 殿堂入りウマ娘id </summary>
            public string OwnerName => _ownerName;
            private ObscuredString _ownerName;
            
            /// <summary> フレンド情報 </summary>
            public int FriendState => _friendState;
            private ObscuredInt _friendState;

            /// <summary> 殿堂入りウマ娘データ </summary>
            public WorkTrainedCharaData.TrainedCharaData TrainedCharaData => _trainedCharaData;
            private WorkTrainedCharaData.TrainedCharaData _trainedCharaData = null;

            #endregion

            #region Method

            /// <summary>
            /// オーナー情報から初期化
            /// </summary>
            /// <param name="ownerInfo"></param>
            public PracticePartnerData(PracticePartnerOwnerInfo ownerInfo)
            {
                _ownerName = ownerInfo.owner_name;
                _ownerViewerId = ownerInfo.owner_viewer_id;
                _ownerTrainedCharaId = ownerInfo.owner_trained_chara_id;
                _trainedCharaId = ownerInfo.partner_trained_chara_id;
                _friendState = ownerInfo.friend_state;
            }

            /// <summary>
            /// TrainedCharaData付きで初期化
            /// </summary>
            /// <param name="ownerInfo"></param>
            /// <param name="trainedChara"></param>
            public PracticePartnerData(PracticePartnerOwnerInfo ownerInfo, TrainedChara trainedChara)
                : this(ownerInfo)
            {
                SetTrainedCharaData(trainedChara);
            }

            /// <summary>
            /// フォローで初期化
            /// </summary>
            /// <param name="name"></param>
            /// <param name="state"></param>
            /// <param name="trainedChara"></param>
            public PracticePartnerData(string name, int state, WorkTrainedCharaData.TrainedCharaData trainedChara)
            {
                _ownerName = name;
                _ownerViewerId = trainedChara.ViewerId;
                _ownerTrainedCharaId = trainedChara.Id;
                _trainedCharaId = 0;// 他人のウマ娘
                _friendState = state;
                _trainedCharaData = trainedChara;
            }

            /// <summary>
            /// 殿堂入りウマ娘を設定
            /// </summary>
            /// <param name="trainedChara"></param>
            public void SetTrainedCharaData(TrainedChara trainedChara)
            {
                _trainedCharaData = new WorkTrainedCharaData.TrainedCharaData(trainedChara);
            }

            /// <summary>
            /// 殿堂入りウマ娘を設定
            /// </summary>
            /// <param name="trainedChara"></param>
            public void SetTrainedCharaData(WorkTrainedCharaData.TrainedCharaData trainedChara)
            {
                _trainedCharaData = trainedChara;
            }

            #endregion
        }

        #endregion

        #region Member, Property

        /// <summary> レースの出走状態 </summary>
        public RaceState State => (RaceState)_state.GetDecrypted();
        private ObscuredInt _state;
        
        /// <summary> 中断中のレースの情報(stateがレース中の時のみ有効な値が入ってる) </summary>
        public int ResumePracticeRaceId => _resumePracticeRaceId;
        private ObscuredInt _resumePracticeRaceId;

        /// <summary> 選択中のレースの情報 </summary>
        public RaceResultData CurrentRaceData => _currentRaceData;
        private RaceResultData _currentRaceData;

        /// <summary> 保存レース一覧 </summary>
        public List<SavedRaceData> SavedRaceList => _savedRaceList;
        private List<SavedRaceData> _savedRaceList = new List<SavedRaceData>();

        /// <summary> 直前のレースの出走キャラ情報 </summary>
        public List<WorkDataUtil.PresetEntryCharaData> CurrentRaceEntryCharaList => _currentRaceEntryCharaList;
        private List<WorkDataUtil.PresetEntryCharaData> _currentRaceEntryCharaList = new List<WorkDataUtil.PresetEntryCharaData>();

        /// <summary> レースに出走している練習パートナー情報 </summary>
        public List<PracticePartnerData> CurrentRaceEntryPracticePartnerList => _currentRaceEntryPracticePartnerList;
        private List<PracticePartnerData> _currentRaceEntryPracticePartnerList = new List<PracticePartnerData>();
        
        /// <summary> 練習パートナー情報(キーはpartner_trained_chara_id) </summary>
        public Dictionary<int, PracticePartnerData> PracticePartnerDataDict => _practicePartnerDataDict;
        private Dictionary<int, PracticePartnerData> _practicePartnerDataDict = new Dictionary<int, PracticePartnerData>();

        /// <summary> フォローユーザーの代表ウマ娘情報 </summary>
        public List<PracticePartnerData> FollowUserCharaDataList => _followUserCharaDataList;
        private List<PracticePartnerData> _followUserCharaDataList = new List<PracticePartnerData>();

        /// <summary> 検索結果サークル練習パ－トナー </summary>
        public List<PracticePartnerData> CircleUserPartnerCharaList => _circleUserPartnerCharaList;
        private List<PracticePartnerData> _circleUserPartnerCharaList = new List<PracticePartnerData>();
        
        /// <summary> 検索結果おすすめ練習パ－トナー </summary>
        public List<PracticePartnerData> RecommendPartnerCharaList => _recommendPartnerCharaList;
        private List<PracticePartnerData> _recommendPartnerCharaList = new List<PracticePartnerData>();
        
        /// <summary> パートナー取得結果の情報 </summary>
        public (int, PracticePartnerData) TempSearchResultPracticePartner => _tempSearchResultPracticePartner;
        private (int, PracticePartnerData) _tempSearchResultPracticePartner = (0, null);

        /// <summary> 編成情報 </summary>
        public Dictionary<int, WorkDataUtil.RacePresetData> DeckDict => _deckDict;
        private Dictionary<int, WorkDataUtil.RacePresetData> _deckDict = new Dictionary<int, WorkDataUtil.RacePresetData>();

        /// <summary> マイ出走リスト保存時、練習パートナーがみつからないときのフラグ </summary>
        public bool IsSavedFollowerExcluded => _isSavedFollowerCharaExcluded;
        private ObscuredBool _isSavedFollowerCharaExcluded = false;

        #endregion

        #region Method

        #region 通信関係

        /// <summary>
        /// ログイン時に更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(LoginResponse.CommonResponse responseData)
        {
            _state = responseData.practice_race_state;

            _practicePartnerDataDict.Clear();
            if (responseData.practice_partner_owner_info_array != null && responseData.practice_partner_chara_array != null)
            {
                for (int i = 0; i < responseData.practice_partner_owner_info_array.Length; i++)
                {
                    _practicePartnerDataDict.Add(
                        responseData.practice_partner_owner_info_array[i].partner_trained_chara_id,
                        new PracticePartnerData(responseData.practice_partner_owner_info_array[i])
                        );
                }

                for (int i = 0; i < responseData.practice_partner_chara_array.Length; i++)
                {
                    var trainedCharaId = responseData.practice_partner_chara_array[i].trained_chara_id;
                    if (_practicePartnerDataDict.ContainsKey(trainedCharaId))
                    {
                        _practicePartnerDataDict[trainedCharaId].SetTrainedCharaData(responseData.practice_partner_chara_array[i]);
                    }
                    else
                    {
                        Debug.LogWarning("practice_partner_owner_info_arrayに存在しないキャラが送られています。trained_chara_id: " + trainedCharaId);
                    }
                }
            }
        }

        /// <summary>
        /// トップAPIで更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceIndexResponse.CommonResponse responseData)
        {
            _state = responseData.state;
            _resumePracticeRaceId = responseData.practice_race_id;
        }

        /// <summary>
        /// レース開始APIで更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceRaceStartResponse.CommonResponse responseData)
        {
            _state = responseData.state;
            _currentRaceData = new RaceResultData(responseData);
            UpdateCurrentRaceEntryCharaData(responseData.entry_info_array, responseData.race_result_info.race_horse_data_array, responseData.trained_chara_array);
            UpdateCurrentRaceEntryPracticePartner(responseData.practice_partner_owner_info_array, responseData.trained_chara_array);
        }

        /// <summary>
        /// レース再開APIで更新
        /// </summary>
        /// <param name="practiceRaceId"></param>
        /// <param name="responseData"></param>
        public void Update(int practiceRaceId, PracticeRaceRaceReplayResponse.CommonResponse responseData)
        {
            var savedRaceData = _savedRaceList.FirstOrDefault(d => d.PracticeRaceId == practiceRaceId);
            if (savedRaceData != null)
            {
                // 保存レースならそのまま使う
                _currentRaceData = savedRaceData;
            }
            else
            {
                _currentRaceData = new RaceResultData(practiceRaceId);
            }

            _currentRaceData.Setup(responseData);
            UpdateCurrentRaceEntryCharaData(responseData.entry_info_array, responseData.race_result_info.race_horse_data_array, responseData.trained_chara_array);
            UpdateCurrentRaceEntryPracticePartner(responseData.practice_partner_owner_info_array, responseData.trained_chara_array);
        }

        /// <summary>
        /// キャラ編成情報を設定
        /// </summary>
        /// <param name="entryInfoArray"></param>
        /// <param name="raceHorseDataArray"></param>
        /// <param name="trainedCharaArray"></param>
        private void UpdateCurrentRaceEntryCharaData(PracticeRaceEntryInfo[] entryInfoArray, RaceHorseData[] raceHorseDataArray, TrainedChara[] trainedCharaArray)
        {
            (TrainedChara TrainedChara, int RunningStyle) GetTrainedCharaDataAndRunningStyle(int frameOrder)
            {
                long viewerId = 0;
                int trainedCharaId = 0;
                int runningStyle = (int)RaceDefine.RunningStyle.None;
                for (int i = 0; i < raceHorseDataArray.Length; i++)
                {
                    if (raceHorseDataArray[i].frame_order == frameOrder)
                    {
                        runningStyle = raceHorseDataArray[i].running_style;
                        viewerId = raceHorseDataArray[i].viewer_id;
                        trainedCharaId = raceHorseDataArray[i].trained_chara_id;
                        break;
                    }
                }

                for (int i = 0; i < trainedCharaArray.Length; i++)
                {
                    if (trainedCharaArray[i].viewer_id == viewerId && trainedCharaArray[i].trained_chara_id == trainedCharaId)
                    {
                        return (trainedCharaArray[i], runningStyle);
                    }
                }
                Debug.LogWarning("サーバーから送られてきたTrainedCharaDataが不正です。frameOrder: " + frameOrder);
                return (null, runningStyle);
            }

            _currentRaceEntryCharaList.Clear();
            for (int i = 0; i < entryInfoArray.Length; i++)
            {
                var data = GetTrainedCharaDataAndRunningStyle(entryInfoArray[i].frame_order);
                _currentRaceEntryCharaList.Add(new WorkDataUtil.PresetEntryCharaData(entryInfoArray[i].entry_id, data.TrainedChara, data.RunningStyle));
            }
        }

        /// <summary>
        /// 出走中の練習パートナーの情報を設定
        /// </summary>
        /// <param name="practicePartnerOwnerInfoArray"></param>
        private void UpdateCurrentRaceEntryPracticePartner(PracticePartnerOwnerInfo[] practicePartnerOwnerInfoArray, TrainedChara[] trainedCharaArray)
        {
            var partnerCharaArray = trainedCharaArray.Select(chara => new WorkTrainedCharaData.TrainedCharaData(chara)).Where(chara => chara.IsGhost).ToArray();
            WorkTrainedCharaData.TrainedCharaData GetTrainedChara(int trainedCharaId)
            {
                for (int i = 0; i < partnerCharaArray.Length; i++)
                {
                    if (partnerCharaArray[i].Id == trainedCharaId)
                    {
                        return partnerCharaArray[i];
                    }
                }

                Debug.LogWarning("練習パートナーのTrainedCharaDataが見つかりません id: " + trainedCharaId);
                return null;
            }

            _currentRaceEntryPracticePartnerList.Clear();
            for (int i = 0; i < practicePartnerOwnerInfoArray.Length; i++)
            {
                _currentRaceEntryPracticePartnerList.Add(new PracticePartnerData(practicePartnerOwnerInfoArray[i]));
                _currentRaceEntryPracticePartnerList[i].SetTrainedCharaData(GetTrainedChara(practicePartnerOwnerInfoArray[i].partner_trained_chara_id));
            }
        }
        
        /// <summary>
        /// パートナー登録APIで追加
        /// </summary>
        /// <param name="responseData"></param>
        public void AddPartner(PracticeRaceSavePartnerResponse.CommonResponse responseData)
        {
            var partner = new PracticePartnerData(responseData.practice_partner_owner_info, responseData.add_partner_info);
            if (!_practicePartnerDataDict.ContainsKey(partner.TrainedCharaId))
            {
                _practicePartnerDataDict.Add(partner.TrainedCharaId, partner);
            }
        }
        
        /// <summary>
        /// パートナー削除APIで更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceDeletePartnerResponse.CommonResponse responseData)
        {
            if (_practicePartnerDataDict.IsNullOrEmpty())
                return;

            if (responseData.practice_partner_chara_array.IsNullOrEmpty())
            {
                _practicePartnerDataDict.Clear();
                return;
            }

            var resPartnerCharaArray = responseData.practice_partner_chara_array;
            var removeTargetList = new List<PracticePartnerData>();
            var practicePartnerDataList = _practicePartnerDataDict.Values.ToList();
            for (int i = 0, n = practicePartnerDataList.Count; i < n; i++)
            {
                var practicePartnerData = practicePartnerDataList[i];
                if (resPartnerCharaArray.Any(x => x.trained_chara_id == practicePartnerData.TrainedCharaId))
                {
                    continue;
                }
                
                removeTargetList.Add(practicePartnerData);
            }

            for (int i = 0, n = removeTargetList.Count; i < n; i++)
            {
                _practicePartnerDataDict.Remove(removeTargetList[i].TrainedCharaId);
            }
        }

        /// <summary>
        /// 保存レース取得APIで更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceGetSavedRaceListResponse.CommonResponse responseData)
        {
            if (responseData.saved_race_array == null)
            {
                return;
            }

            _savedRaceList.Clear();
            for (int i = 0; i < responseData.saved_race_array.Length; i++)
            {
                _savedRaceList.Add(new SavedRaceData(responseData.saved_race_array[i]));
            }
        }

        /// <summary>
        /// お気に入りレース更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceChangeFavoriteRaceResponse.CommonResponse responseData)
        {
            var data = _savedRaceList.FirstOrDefault(d => d.PracticeRaceId == responseData.practice_race_id);
            if (data != null)
            {
                data.UpdateFavorite(responseData.is_favorite);
            }
        }

        /// <summary>
        /// レース終了APIで更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceRaceEndResponse.CommonResponse responseData)
        {
            _state = responseData.state;
        }

        /// <summary>
        /// レース削除APIで更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceDeleteRaceResponse.CommonResponse responseData)
        {
            _state = responseData.state;
        }

        /// <summary>
        /// フォローユーザー代表ウマ娘取得で更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceGetFollowUserDataResponse.CommonResponse responseData)
        {
            _followUserCharaDataList.Clear();
            for (int i = 0; i < responseData.practice_partner_owner_info_array.Length; i++)
            {
                _followUserCharaDataList.Add(
                    new PracticePartnerData(responseData.practice_partner_owner_info_array[i], responseData.follow_user_chara_array[i])
                    );
            }
        }

        /// <summary>
        /// パートナー検索APIで更新
        /// </summary>
        /// <param name="responseData"></param>
        /// <param name="isFirst">true:初回、false:おすすめのリロード</param>
        public void Update(PracticeRaceSearchPartnerResponse.CommonResponse responseData, bool isFirst)
        {
            var recommendPartnerArray = responseData.recommend_partner_chara_array;
            var circleUserPartnerCharaArray = responseData.circle_user_partner_chara_array;
            var practicePartnerOwnerInfoArray = responseData.practice_partner_owner_info_array;

            if (practicePartnerOwnerInfoArray != null)
            {
                // サークル
                if (circleUserPartnerCharaArray != null && isFirst)
                {
                    _circleUserPartnerCharaList.Clear();

                    for (int i = 0; i < circleUserPartnerCharaArray.Length; i++)
                    {
                        var ownerInfo = practicePartnerOwnerInfoArray.FirstOrDefault(x => x.owner_viewer_id == circleUserPartnerCharaArray[i].viewer_id);
                        if (ownerInfo != null)
                        {
                            var partner = new PracticePartnerData(ownerInfo, circleUserPartnerCharaArray[i]);
                            _circleUserPartnerCharaList.Add(partner);
                        }
                    }
                }

                // おすすめ
                if (recommendPartnerArray != null)
                {
                    _recommendPartnerCharaList.Clear();

                    for (int i = 0; i < recommendPartnerArray.Length; i++)
                    {
                        var ownerInfo = practicePartnerOwnerInfoArray.FirstOrDefault(x => x.owner_viewer_id == recommendPartnerArray[i].viewer_id);
                        if (ownerInfo != null)
                        {
                            var partner = new PracticePartnerData(ownerInfo, recommendPartnerArray[i]);
                            _recommendPartnerCharaList.Add(partner);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// パートナーIDでの練習パートナー情報取得APIの結果保存
        /// </summary>
        /// <param name="responseData"></param>
        public PracticePartnerData SaveTempSearchResultPracticePartner(PracticeRaceGetPartnerInfoResponse.CommonResponse responseData)
        {
            var partner = new PracticePartnerData(responseData.practice_partner_owner_info, responseData.practice_partner_info);
            _tempSearchResultPracticePartner = (partner.TrainedCharaId, partner);

            return partner;
        }

        /// <summary>
        /// フォローで更新
        /// </summary>
        /// <param name="name"></param>
        /// <param name="userFriend"></param>
        /// <param name="trainedCharaData"></param>
        public void UpdateFriendFollow(string name, UserFriend userFriend, WorkTrainedCharaData.TrainedCharaData trainedCharaData)
        {
            _followUserCharaDataList.Add(new PracticePartnerData(name, userFriend.state, trainedCharaData));
        }

        /// <summary>
        /// フォロー解除で更新
        /// </summary>
        /// <param name="userFriend"></param>
        public void UpdateFriendUnfollow(UserFriend userFriend)
        {
            _followUserCharaDataList.RemoveAll(data => data.OwnerViewerId == userFriend.friend_viewer_id);
        }

        /// <summary>
        /// プリセット取得で更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceGetPresetArrayResponse.CommonResponse responseData)
        {
            UpdatePresetInfo(responseData.preset_info_array);
        }

        /// <summary>
        /// プリセット情報を更新
        /// </summary>
        /// <param name="presetInfoArray"></param>
        private void UpdatePresetInfo(PracticeRacePresetInfo[] presetInfoArray)
        {
            _deckDict.Clear();
            for (int i = 0; i < presetInfoArray.Length; i++)
            {
                _deckDict.Add(presetInfoArray[i].preset_id, new WorkDataUtil.RacePresetData(presetInfoArray[i]));
            }
        }

        /// <summary>
        /// プリセット保存で更新(練習パートナーを追加)
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceSavePresetResponse.CommonResponse responseData)
        {
            TrainedChara GetTrainedCharaData(int trainedCharaId)
            {
                for (int i = 0; i < responseData.add_partner_info_array.Length; i++)
                {
                    if (trainedCharaId == responseData.add_partner_info_array[i].trained_chara_id)
                    {
                        return responseData.add_partner_info_array[i];
                    }
                }

                Debug.LogWarning("指定されたidの殿堂入りウマ娘が送られていません: " + trainedCharaId);
                return null;
            }

            // プリセット
            for (int i = 0; i < responseData.preset_info_array.Length; i++)
            {
                _deckDict[responseData.preset_info_array[i].preset_id] = new WorkDataUtil.RacePresetData(responseData.preset_info_array[i]);
            }

            // 練習パートナー
            for (int i = 0; i < responseData.practice_partner_owner_info_array.Length; i++)
            {
                var ownerInfo = responseData.practice_partner_owner_info_array[i];
                _practicePartnerDataDict.Add(
                    ownerInfo.partner_trained_chara_id,
                    new PracticePartnerData(ownerInfo, GetTrainedCharaData(ownerInfo.partner_trained_chara_id))
                    );
            }

            // 他人のウマ娘を保存したときに移籍済みだった場合
            const int SERVER_IS_EXCLUDED = 1;
            _isSavedFollowerCharaExcluded = responseData.is_excluded_flag == SERVER_IS_EXCLUDED;
        }

        /// <summary>
        /// マイ出走リスト保存時の
        /// </summary>
        public void ResetIsSavedFollowerCharaExcluded()
        {
            _isSavedFollowerCharaExcluded = false;
        }

        /// <summary>
        /// プリセット名変更で更新
        /// </summary>
        /// <param name="responseData"></param>
        public void Update(PracticeRaceChangePresetNameResponse.CommonResponse responseData)
        {
            if (_deckDict.ContainsKey(responseData.preset_info.preset_id))
            {
                _deckDict[responseData.preset_info.preset_id] = new WorkDataUtil.RacePresetData(responseData.preset_info);
            }
        }

        #endregion

        /// <summary>
        /// 編成可能な練習パートナーの一覧を取得(練習パートナー＋フォローユーザーの代表ウマ娘)
        /// </summary>
        /// <returns></returns>
        public List<WorkTrainedCharaData.TrainedCharaData> GetSelectableTrainedCharaList()
        {
            var charaList = WorkDataManager.Instance.PracticeRaceData.PracticePartnerDataDict.Values
                .Select(d => d.TrainedCharaData).ToList();
            charaList.AddRange(WorkDataManager.Instance.PracticeRaceData.FollowUserCharaDataList
                .Select(d => d.TrainedCharaData)
                .Where(chara =>
                {
                    // 重複を削除
                    var partner = WorkDataManager.Instance.PracticeRaceData.PracticePartnerDataDict.Values.FirstOrDefault(p => p.OwnerTrainedCharaId == chara.Id && p.OwnerViewerId == chara.ViewerId);
                    return partner == null;
                }));

            return charaList;
        }

        /// <summary>
        /// TrainedCharaIdから練習パートナーを取得
        /// (存在しなければnull)
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public WorkTrainedCharaData.TrainedCharaData GetPracticePartnerTrainedCharaData(int trainedCharaId)
        {
            _practicePartnerDataDict.TryGetValue(trainedCharaId, out var data);
            return data?.TrainedCharaData;
        }

        /// <summary>
        /// TrainedCharaIdから殿堂入りウマ娘または練習パートナーを取得
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public WorkTrainedCharaData.TrainedCharaData GetTrainedCharaData(int trainedCharaId)
        {
            return WorkDataManager.Instance.TrainedCharaData.HasData(trainedCharaId)
                ? WorkDataManager.Instance.TrainedCharaData.Get(trainedCharaId)
                : WorkDataManager.Instance.PracticeRaceData.GetPracticePartnerTrainedCharaData(trainedCharaId);
        }

        /// <summary>
        /// パートナー情報を取得
        /// (存在しなければnull)
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public PracticePartnerData GetPartnerData(int trainedCharaId)
        {
            _practicePartnerDataDict.TryGetValue(trainedCharaId, out var data);
            return data;
        }

        /// <summary>
        /// フォローユーザー情報を取得
        /// </summary>
        /// <param name="viewerId"></param>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public PracticePartnerData GetFollowUserPartnerData(long viewerId, int trainedCharaId)
        {
            return _followUserCharaDataList.FirstOrDefault(d => d.OwnerViewerId == viewerId && d.TrainedCharaData.Id == trainedCharaId);
        }

        /// <summary>
        /// 練習パートナー、フォローユーザーのトレーナー名を取得
        /// </summary>
        public string GetPartnerOwnerName(WorkTrainedCharaData.TrainedCharaData trainedChara)
        {
            if (trainedChara.IsGhost)
            {
                // 練習パートナー一覧から検索
                var partnerData = _practicePartnerDataDict.Values.FirstOrDefault(x =>
                    x.OwnerViewerId == trainedChara.OwnerViewerId && x.OwnerTrainedCharaId == trainedChara.OwnerTrainedCharaId);
                if (partnerData != null)
                {
                    return partnerData.OwnerName;
                }
            }
            else
            {
                // フォローユーザーの代表ウマ娘から当該キャラを検索して持ち主の名前を返す
                var partnerData = GetFollowUserPartnerData(trainedChara.ViewerId, trainedChara.Id);
                if (partnerData != null)
                {
                    return partnerData.OwnerName;
                }
            }

            Debug.LogWarning("練習パートナーに登録されていない && フォローユーザーの代表ウマ娘以外のウマ娘が指定されている");
            return string.Empty;
        }

        /// <summary>
        /// フォローユーザー代表ウマ娘を取得
        /// </summary>
        /// <param name="viewerId"></param>
        /// <param name="trainedCharaId"></param>
        /// <returns></returns>
        public WorkTrainedCharaData.TrainedCharaData GetFollowUserCharaData(long viewerId, int trainedCharaId)
        {
            return _followUserCharaDataList.FirstOrDefault(d => d.OwnerViewerId == viewerId && d.TrainedCharaData.Id == trainedCharaId)?.TrainedCharaData;
        }

        /// <summary>
        /// 同一キャラがいたら取得
        /// </summary>
        /// <param name="oldPracticePartnerData"></param>
        /// <returns></returns>
        public WorkTrainedCharaData.TrainedCharaData FindSameRentalTrainedChara(PracticePartnerData oldPracticePartnerData)
        {
            return FindSameRentalTrainedChara(oldPracticePartnerData?.TrainedCharaData, oldPracticePartnerData);
        }

        /// <summary>
        /// 同一キャラがいたら取得
        /// </summary>
        /// <param name="oldTrainedCharaData"></param>
        /// <param name="oldPracticePartnerData"></param>
        /// <returns></returns>
        public WorkTrainedCharaData.TrainedCharaData FindSameRentalTrainedChara(WorkTrainedCharaData.TrainedCharaData oldTrainedCharaData, PracticePartnerData oldPracticePartnerData)
        {
            if (oldTrainedCharaData == null)
            {
                return null;
            }

            if (oldTrainedCharaData.IsGhost)
            {
                if (oldPracticePartnerData == null)
                {
                    return null;
                }

                // 練習パートナーを再登録するとidが変わるため先に調査
                var practicePartnerData = _practicePartnerDataDict.Values.FirstOrDefault(x =>
                    (x.OwnerViewerId == oldPracticePartnerData.OwnerViewerId) && x.OwnerTrainedCharaId == oldPracticePartnerData.OwnerTrainedCharaId);
                if (practicePartnerData != null)
                {
                    return practicePartnerData.TrainedCharaData;
                }

                // 練習パートナーならフォロワーの代表ウマ娘から探す
                return FollowUserCharaDataList
                    .FirstOrDefault(d => d.OwnerViewerId == oldTrainedCharaData.OwnerViewerId && d.OwnerTrainedCharaId == oldPracticePartnerData.OwnerTrainedCharaId)
                    ?.TrainedCharaData;
            }
            else if (oldTrainedCharaData.IsOthers)
            {
                // フォロワーの代表ウマ娘なら練習パートナーから探す
                return PracticePartnerDataDict.Values
                    .FirstOrDefault(d => d.OwnerViewerId == oldTrainedCharaData.ViewerId && d.OwnerTrainedCharaId == oldTrainedCharaData.Id)
                    ?.TrainedCharaData;
            }

            // 自分のウマ娘
            return null;
        }

        /// <summary>
        /// そのキャラを練習パートナーウマ娘として登録してるか判定
        /// </summary>
        /// <returns></returns>
        public bool IsRegisterPracticePartner(WorkTrainedCharaData.TrainedCharaData trainedChara)
        {
            if (!_practicePartnerDataDict.IsNullOrEmpty())
            {
                var practicePartnerChara = _practicePartnerDataDict.Values.FirstOrDefault(x =>
                    x.OwnerViewerId == trainedChara.ViewerId && x.OwnerTrainedCharaId == trainedChara.Id);

                if (practicePartnerChara != null)
                {
                    return true;
                }
            }
            else
            {
                return false;
            }
            return false;
        }

        /// <summary>
        /// レース削除
        /// </summary>
        /// <param name="practiceRaceId"></param>
        public void DeleteRace(int practiceRaceId)
        {
            // 現在のレース情報をがあれば削除
            if (_currentRaceData != null && _currentRaceData.PracticeRaceId == practiceRaceId)
            {
                _currentRaceData = null;
                _currentRaceEntryCharaList.Clear();
            }

            // 中断レース情報と一致すれば削除
            // (シミュレーターの更新などで現在のレース結果が見れなくなった場合に発生)
            if (State == RaceState.RaceStart && _resumePracticeRaceId == practiceRaceId)
            {
                _state = (int)RaceState.None;
                _resumePracticeRaceId = 0;
            }

            // 保存レースにあれば削除
            _savedRaceList.RemoveAll(r => r.PracticeRaceId == practiceRaceId);
        }

        /// <summary>
        /// サークルで共有されてるパートナー一覧を呼び出す
        /// </summary>
        /// <param name="onSuccessCallback"></param>
        public void GetCircleSharedPartnerList(Action<CirclePostPartner[]> onSuccessCallback)
        {
            //練習パートナー投稿情報のウマ娘IDで再度情報取得、取得出来れば詳細表示 できなければ移籍されたか古くて消された
            var req = new CircleGetPostPartnerDataRequest();
            Action<CircleGetPostPartnerDataResponse> onSuccess = (res) =>
            {
                onSuccessCallback(res.data.circle_post_partner_array);
            };
            HttpManager.Instance.Send(req, onSuccess, (errorType, resultCode) => { });
        }

        #endregion
    }
}