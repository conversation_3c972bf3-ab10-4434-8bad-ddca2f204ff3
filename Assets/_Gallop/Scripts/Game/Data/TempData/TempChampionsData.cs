using UnityEngine;
using System.Collections.Generic;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    /// <summary>
    /// チャンピオンズミーティングのTempData
    /// </summary>
    public sealed partial class TempData : Singleton<TempData>
    {
        #region 限定ミッション

        /// <summary>
        /// ミッション達成数
        /// ** 達成してかつ未確認の数
        /// </summary>
        public ObscuredInt GetChampionsMissionClearNum(long timeStamp = 0)
        {
            var num = 0;
            if (timeStamp == 0) 
            {
                timeStamp = TimeUtil.GetServerTimeStamp();
            }

            foreach (var id in _championsClearMissionIdList)
            {
                var missionData = MasterDataManager.Instance.masterMissionData.Get(id);
                if (missionData != null && missionData.IsInTerm(timeStamp))
                {
                    //現在時間で受け取り可能なら加算
                    num++;
                }
            }

            return num;
        }

        private List<int> _championsClearMissionIdList = new List<int>();

        /// <summary>
        /// 未確認のミッション達成数を更新する
        /// </summary>
        public void SetChampionsMissionClearData(UserMission[] missionArray)
        {
            _championsClearMissionIdList.Clear();
            if (missionArray == null)
                return; //nullなら無いってことなので内部リストを空にしちゃっていい

            AddChampionsMissionClearData(missionArray);
        }

        /// <summary>
        /// ミッションクリア時に未受け取りのクリアミッション数を加算する
        /// </summary>
        public void AddChampionsMissionClearData(UserMission[] missionArray)
        {
            if (missionArray == null)
                return;

            const int CLEAR_STATE = (int)GameDefine.MissionState.Clear;
            foreach (var mission in missionArray)
            {
                if (mission == null)
                    continue;

                var master = MasterDataManager.Instance.masterMissionData.Get(mission.mission_id);
                if (master == null)
                    continue;   //CSVが存在しない

                if (mission.mission_status != CLEAR_STATE)
                    continue;   //クリアしてない

                if(!_championsClearMissionIdList.Contains(mission.mission_id))
                {
                    _championsClearMissionIdList.Add(mission.mission_id);
                }
            }
        }

        /// <summary>
        /// ミッションクリアを確認したときに確認したぶんを減算する
        /// </summary>
        public void SubChampionsMissionClearData(int[] missionIdArray)
        {
            if (missionIdArray == null)
                return;

            foreach(var id in missionIdArray)
            {
                if(_championsClearMissionIdList.Contains(id))
                {
                    _championsClearMissionIdList.Remove(id);
                }
            }
        }

        #endregion

        public class ChampionsTempData 
        {
            public bool IsReplay { get; set; } = false;

            /// <summary>
            /// チャンミではライブリプレイ後にはリザルトダイアログを出さない
            /// </summary>
            public bool IsAfterLiveReplay { get; set; } = false;
            public ChampionsRaceInfo RaceInfo { get; set; } = new ChampionsRaceInfo();
            public int RaceTitleResourceId { get; private set; }
            public int RaceSubTitleResourceId { get; private set; }
            /// <summary>
            /// 期間終了ダイアログが開いているかどうか判定するフラグ。ポーリングをしている関係で2重に期間エラーが返ってくるケースがあり、2重に開かれるのを防ぐ
            /// </summary>
            /// <remarks>期間判定エラーはエラーダイアログとして扱いつつも、期間判定以外の他のエラーダイアログが呼ばれたら出さないといけないので、専用でフラグを持っている</remarks>
            public bool IsOpenScheduleChangeErrorDialog { get; set; } = false;

            public void Clear()
            {
                IsReplay = false;
                RaceInfo.Clear();
                RaceTitleResourceId = 0;
                RaceSubTitleResourceId = 0;
            }
            public void SetRaceTitleResourceId(int raceTitleResourceId, int raceSubTitleResourceId)
            {
                RaceTitleResourceId = raceTitleResourceId;
                RaceSubTitleResourceId = raceSubTitleResourceId;
            }
        }

        /// <summary>
        /// チャンピオンズミーティングのレースで使用する情報
        /// レース終了後に表示する着順表でリプレイの再生に必要なのと、
        /// </summary>
        public class ChampionsRaceInfo
        {
            public bool IsSet { get; private set; } //念のためのチェックフラグ
            public int RaceNum { get; private set; } = -1;  //第何レースか、０始まり
            public ChampionsRoomInfo RoomInfo { get; private set; }
            public ChampionsRoomUser[] RoomUserArray { get; private set; }
            public RaceHorseData[] RaceHorseDataArray { get; private set; }
            public TrainedChara[] TrainedCharaArray { get; private set; }

            public void Update(
                int raceNum,
                ChampionsRoomInfo roomInfo,
                ChampionsRoomUser[] roomUserArray,
                RaceHorseData[] raceHorseDataArray,
                TrainedChara[] trainedCharaArray
            )
            {
                IsSet = true;
                RaceNum = raceNum;
                RoomInfo = roomInfo;
                RoomUserArray = roomUserArray;
                RaceHorseDataArray = raceHorseDataArray;
                TrainedCharaArray = trainedCharaArray;
            }

            public void Clear()
            {
                IsSet = false;
                RaceNum = -1;
                RoomInfo = null;
                RoomUserArray = null;
                RaceHorseDataArray = null;
                TrainedCharaArray = null;
            }
        }

        /// <summary>
        /// ChampionsMeetingで使用する一時的な値を格納する
        /// レースシーンを介してデータを保持する場合に使用し、拠点遷移時にClearを実行する
        /// </summary>
        public ChampionsTempData ChampionsData { get; private set; } = new ChampionsTempData();

        /// <summary>
        /// 報酬情報
        /// ** 報酬データはトップ画面で受け取ったら以降使い回すためにTempで保持
        /// ** 一度確保されると確保されっぱなしになるため断片化の点からはあまりよくない・・・
        /// </summary>
        public ChampionsRewardInfo[] ChampionsRewardInfoArray { get; set; } = null;
    }
}
