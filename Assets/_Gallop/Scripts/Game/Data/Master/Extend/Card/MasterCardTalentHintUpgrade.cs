using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// キャラカード覚醒レベル管理
    /// </summary>
    public partial class MasterCardTalentHintUpgrade
    {
        public partial class CardTalentHintUpgrade
        {
            /// <summary>
            /// 素質強化に必要なアイテムのデータ
            /// </summary>
            public class UpgradeItemData
            {
                public GameDefine.ItemCategory Category { get; private set; }
                public int ItemId { get; private set; }
                public int ItemNum { get; private set; }
                public int SortOrder { get; private set; }

                public UpgradeItemData(GameDefine.ItemCategory category, int itemId, int itemNum, int sortOrder)
                {
                    Category = category;
                    ItemId = itemId;
                    ItemNum = itemNum;
                    SortOrder = sortOrder;
                }

                /// <summary>
                /// アイテム所持数を追加
                /// </summary>
                public void AddItemNum(int addNum)
                {
                    ItemNum += addNum;
                }
            }

            /// <summary>
            /// 素質強化に必要なアイテムのリストを取得
            /// </summary>
            public List<UpgradeItemData> GetNeedItemList(int charaId)
            {
                var itemList = new List<UpgradeItemData>();
                AddItemToList(ref itemList, ItemCategory1, ItemId1, ItemNum1, ItemDispOrder1, charaId);
                AddItemToList(ref itemList, ItemCategory2, ItemId2, ItemNum2, ItemDispOrder2, charaId);
                AddItemToList(ref itemList, ItemCategory3, ItemId3, ItemNum3, ItemDispOrder3, charaId);
                AddItemToList(ref itemList, ItemCategory4, ItemId4, ItemNum4, ItemDispOrder4, charaId);

                return itemList;
            }

            /// <summary>
            /// アイテムデータを辞書に追加
            /// </summary>
            private void AddItemToList(ref List<UpgradeItemData> list, int category, int itemId, int itemNum, int sortOrder, int charaId)
            {
                if (itemId <= 0) return;

                GameDefine.ItemCategory itemCategory = (GameDefine.ItemCategory)category;

                int realItemId = itemId;
                if (itemCategory == GameDefine.ItemCategory.CHARA_MEMORY && itemId == MasterMemoryData.GENERAL_MEMORY_ID)
                {
                    // 汎用メモリーの場合、アイテムIDをキャラのメモリーのIDに上書き
                    realItemId = MasterDataManager.Instance.masterMemoryData.GetWithCharaId(charaId).Id;
                }

                var data = list.Find(x => x.Category == itemCategory && x.ItemId == realItemId);
                if (data != null)
                {
                    data.AddItemNum(itemNum);
                }
                else
                {
                    list.Add(new UpgradeItemData(itemCategory, realItemId, itemNum, sortOrder));
                }
            }
        }
    }
}