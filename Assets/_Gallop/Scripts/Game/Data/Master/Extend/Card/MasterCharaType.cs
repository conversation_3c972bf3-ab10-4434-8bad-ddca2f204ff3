using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using static Gallop.StaticVariableDefine.Master.MasterCharaType;

namespace Gallop
{
    public partial class MasterCharaType
    {
        public enum PersonalityType
        {
            RaceDefault,
            MiniDefault
        }
        
        public struct TargetValues
        {
            public int TargetScene;
            public int TargetCut;
            public int TargetType;

            public TargetValues(int scene, int cut, int type)
            {
                TargetScene = scene;
                TargetCut = cut;
                TargetType = type;
            }
        }

        public enum TargetSceneId : int
        {
            Home = 0,
            Race = 1,
            SingleModeTraining = 2,
            Mini = 5,
            Max
        }

        /// <summary>
        /// ホームのフッター配置キャラクターに使用するtarget_cut
        /// </summary>
        private enum HomeFooterTargetCut : int
        {
            Character = 3,
            Story = 4,
            Race = 5,
        }

        //ミニはカットの代わりにこれを使う
        //ここを追加した場合はMaterMiniMotionSet.GetCharaTypeTargetCutも対応する必要あり
        public enum MiniSceneType : int
        {
            None = 0,
            Job = 1,
            Circle = 2,
            Set = 9,
        }
        
        // ミニ（target_scene:5)で使用するtarget_cut
        public enum MiniTargetCut : int
        {
            SingleMode = 0,
        }        
        // ミニ（target_scene:5)育成(target_cut:0)で使用するtarget_type
        public enum MiniSingleModeTargetType : int
        {
            TargetClear = 1,    // 目標クリア
            TargetAllClear = 2, // 目標全クリア
            ClassDialog = 3,    // キャラクラスダイアログ
        }

        /// <summary>
        /// ミニキャラ用に取得
        /// </summary>
        /// <param name="charaId"></param>
        /// <returns></returns>
        public int GetMini(int charaId, MasterMiniMotionSet.MiniMotionSet motionSet)
        {
            var miniSceneType = motionSet.GetCharaTypeTargetCut();
            if(miniSceneType == MiniSceneType.Set)
            {
                //Setの元に配置するモーションは汎用モーションとなるため、直接に0を返す
                return 0;
            }
            else
            {

                var targetType = motionSet.CharaTypeTarget;
                var data = GetListWithTargetSceneAndTargetCutOrderByTargetSceneAsc((int)TargetSceneId.Mini, (int)miniSceneType)
                .FirstOrDefault(charaType => charaType.CharaId == charaId && charaType.TargetType == targetType);
                if(data == null)
                {
                    return 0;
                }
                return data.Value;
            }
        }
        
        /// <summary>
        /// キャラID、ターゲットシーンID、ターゲットカットID、ターゲットタイプの4つから生成したIDでキャラ固有値を取得する
        /// </summary>
        public ModelLoader.DefaultPersonalityType GetDefaultPersonality(int charaId , PersonalityType personalityType = PersonalityType.RaceDefault)
        {
            if (!PERSONARITY_VALUE_DIC.ContainsKey(personalityType))
            {
                Debug.LogWarning("対象の性格別データが存在しません");
                return ModelLoader.DefaultPersonalityType.Bright;
            }
            
            var targetValues = PERSONARITY_VALUE_DIC[personalityType];
            var data = GetListWithTargetSceneAndTargetCutOrderByTargetSceneAsc(targetValues.TargetScene, targetValues.TargetCut)
                .FirstOrDefault(charaType => charaType.CharaId == charaId && charaType.TargetType == targetValues.TargetType);
            return data != null ? (ModelLoader.DefaultPersonalityType)data.Value : ModelLoader.DefaultPersonalityType.Bright;
        }


        /// <summary>
        /// キャラID、ターゲットシーンID、ターゲットカットID、ターゲットタイプの4つから生成したIDでキャラ固有値を取得する
        /// </summary>
        /// <param name="charaId">キャラID</param>
        /// <param name="targetScene">chara_data.csv固有のシーン識別ID</param>
        /// <param name="targetCut">(chara_data.csv固有の)シーン内でのカット番号</param>
        /// <param name="targetType">(chara_data.csv固有の)カット内での識別番号。1カットに複数の性格値を用いる場合に使用される。</param>
        /// <returns></returns>
        public CharaType Get(int charaId, int targetScene, int targetCut, int targetType)
        {
            // 複合キーの数の制限により、SQLだけでレコードを特定できない。
            // そのため、まずTargetSceneとTargetCutでリストを取得し、その後でCharaTypeとTargetTypeのフィルターを行う。
            // GetListWithTargetSceneAndTargetCutOrderByTargetSceneAscは
            // TargetSceneとTargetCutから一致するレコードをTargetSceneの昇順で取得する
            return GetListWithTargetSceneAndTargetCutOrderByTargetSceneAsc(targetScene, targetCut)
                .FirstOrDefault(charaType => charaType.CharaId == charaId && charaType.TargetType == targetType);
        }

        /// <summary>
        /// Cutの読み替え向けのパラメーターでCharaTypeを取得する
        /// </summary>
        /// <param name="charaId">キャラID</param>
        /// <param name="commandId">コマンドID</param>
        /// <param name="targetCharaIndex">何番目のキャラの性格値を使うか</param>
        /// <returns>キャラ固有値</returns>
        public CharaType GetForSingleModeTrainingCutPath(int charaId, int commandId, int targetCharaIndex)
        {
            const int TARGET_SCENE = (int) TargetSceneId.SingleModeTraining;
            return Get(charaId, TARGET_SCENE, commandId, targetCharaIndex);
        }

        /// <summary>
        /// キャラクター、立ち位置に対応するホームのモーションの性格値を取得する
        /// </summary>
        /// <param name="buildInfo"></param>
        /// <param name="standPos"></param>
        /// <returns></returns>
        public static int GetStandPosPersonality(CharacterBuildInfo buildInfo, HomeDefine.StandPos standPos)
        {
            // ホーム配置キャラクターのtarget_type
            const int HOME_STAND_MODEL_TARGET_TYPE = 0;
            const int DEFAULT_TARGET_CUT = -1;
            
            // 立ち位置に対応したtarget_cutを取得する
            int targetCut = standPos switch
            {
                HomeDefine.StandPos.Character => (int)HomeFooterTargetCut.Character,
                HomeDefine.StandPos.Story     => (int)HomeFooterTargetCut.Story,
                HomeDefine.StandPos.Race      => (int)HomeFooterTargetCut.Race,
                _ => DEFAULT_TARGET_CUT
            };
            
            // 立ち位置が強化編成、ストーリー、レース(受付前)以外の場合
            if (targetCut == DEFAULT_TARGET_CUT)
            {
                return (int)buildInfo.DefaultPersonalityType;
            }

            var charaType = MasterDataManager.Instance.masterCharaType.Get(
                buildInfo.CharaId,
                (int)TargetSceneId.Home,
                targetCut,
                HOME_STAND_MODEL_TARGET_TYPE);
            
            return charaType.Value;
        }
    }
}
