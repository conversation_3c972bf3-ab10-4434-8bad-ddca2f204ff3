using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    public partial class MasterCharaCategoryMotion
    {
        private Dictionary<int, List<int>> _standbyMotionDict = new Dictionary<int, List<int>>();

        public List<int> GetCharaStandbyMotionList(int charaId)
        {
            if (!_standbyMotionDict.ContainsKey(charaId))
            {
                // キャッシュ作成しておく
                List<int> list = new List<int>();
                var masterCharaCategoryMotion = Get(charaId);
                if (masterCharaCategoryMotion == null)
                {
                    Debug.LogWarning("masterCharaCategoryMotionがnullです");
                    return list;
                }

                if (masterCharaCategoryMotion.StandbyMotion1 != 0)
                {
                    list.Add(masterCharaCategoryMotion.StandbyMotion1);
                }

                if (masterCharaCategoryMotion.StandbyMotion2 != 0)
                {
                    list.Add(masterCharaCategoryMotion.StandbyMotion2);
                }

                if (masterCharaCategoryMotion.StandbyMotion3 != 0)
                {
                    list.Add(masterCharaCategoryMotion.StandbyMotion3);
                }

                if (masterCharaCategoryMotion.StandbyMotion4 != 0)
                {
                    list.Add(masterCharaCategoryMotion.StandbyMotion4);
                }

                if (masterCharaCategoryMotion.StandbyMotion5 != 0)
                {
                    list.Add(masterCharaCategoryMotion.StandbyMotion5);
                }

                if (masterCharaCategoryMotion.StandbyMotion6 != 0)
                {
                    list.Add(masterCharaCategoryMotion.StandbyMotion6);
                }

                _standbyMotionDict.Add(charaId, list);
            }
            return _standbyMotionDict[charaId];
        }
    }
}