using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    using TrainingChallenge;

    public partial class MasterTrainingChallengeScore
    {
        public partial class TrainingChallengeScore
        {
        }

        /// <summary>
        /// <see cref="TrainingChallengeScore"/>をScoreType毎に纏めたもの
        /// </summary>
        public class ScoreGroupData
        {
            private Dictionary<TrainingChallengeDefine.ScoreType, List<TrainingChallengeScore>> _dict = new Dictionary<TrainingChallengeDefine.ScoreType, List<TrainingChallengeScore>>();

            public void Add(TrainingChallengeDefine.ScoreType scoreType, TrainingChallengeScore master)
            {
                if (!_dict.TryGetValue(scoreType, out var list))
                {
                    list = new List<TrainingChallengeScore>();
                    _dict.Add(scoreType, list);
                }

                list.Add(master);
            }

            public List<TrainingChallengeScore> GetValues(TrainingChallengeDefine.ScoreType scoreType)
            {
                if (!_dict.TryGetValue(scoreType, out var list))
                {
                    list = new List<TrainingChallengeScore>();
                    _dict.Add(scoreType, list);
                }

                return list;
            }
        }


        private Dictionary<int, ScoreGroupData> _groupDict = new Dictionary<int, ScoreGroupData>();


        /// <summary>
        /// ScoreGroupDataを取得する
        /// </summary>
        public ScoreGroupData GetOrCreateScoreGroup(int scoreGroupId)
        {
            if (!_groupDict.TryGetValue(scoreGroupId, out var groupData))
            {
                groupData = new ScoreGroupData();

                foreach (var master in GetListWithScoreGroupIdOrderByDispOrderAsc(scoreGroupId))
                {
                    // ScoreType毎に分ける
                    groupData.Add((TrainingChallengeDefine.ScoreType)master.ScoreType, master);
                }

                _groupDict.Add(scoreGroupId, groupData);
            }

            return groupData;
        }

        /// <summary>
        /// 指定した<see cref="TrainingChallengeDefine.ScoreType"/>のリストを取得する
        /// </summary>
        public List<TrainingChallengeScore> GetOrCreateScoreMasters(int scoreGroupId, TrainingChallengeDefine.ScoreType scoreType)
        {
            var group = GetOrCreateScoreGroup(scoreGroupId);

            return group.GetValues(scoreType);
        }

        /// <summary>
        /// 指定したIDリストから<see cref="TrainingChallengeDefine.ScoreType"/>をKeyとした辞書を作成する
        /// </summary>
        /// <remarks>
        /// シナリオギミックが1000以上で固定されていないため型はintでとる
        /// </remarks>
        public Dictionary<int /*TrainingChallengeDefine.ScoreType */, List<TrainingChallengeScore>> CreateScoreTypeDictionaryForIds(int[] ids)
        {
            // Nullを弾いたmaster
            var masters = ids
                .Select(id => Get(id))
                .Where(master => master != null);

            // 辞書作成
            return masters
                .GroupBy(master => (int)master.ScoreType)
                .ToDictionary(group => group.Key, group => group.OrderBy(master => master.DispOrder).ToList());
        }

        /// <summary>
        /// 内部のスコアマスタ全てを辞書で取得する
        /// </summary>
        public Dictionary<int /* TrainingChallengeDefine.ScoreType */, List<TrainingChallengeScore>> CreateDictionaryForAll(int scoreGroupId)
        {
            var masters = GetListWithScoreGroupIdOrderByDispOrderAsc(scoreGroupId);

            // 辞書作成
            return masters
                .GroupBy(master => (int)master.ScoreType)
                .ToDictionary(group => group.Key, group => group.OrderBy(master => master.DispOrder).ToList());
        }

        /// <summary>
        /// スキルがトレーナー技能試験のボーナス対象かどうかチェック
        /// </summary>
        public static bool IsScoreBonusTarget(MasterSkillData.SkillData masterSkillData)
        {
            // ボーナスの対象になるのはGeneralSkillかつバッドスキルでないものだけ
            return masterSkillData.IsGeneralSkill == 1 && masterSkillData.GradeValue > 0;
        }

    }

    /// <summary>
    /// <see cref="MasterTrainingChallengeScore"/>関連の拡張メソッド
    /// </summary>
    public static class MasterTrainingChallengeScoreExtensions
    {
        /// <summary>
        /// 合計スコアを計算して返す
        /// </summary>
        public static int CalcTotalScore(this List<MasterTrainingChallengeScore.TrainingChallengeScore> self) =>
            self.Sum(elm => elm.BonusScore);

        /// <summary>
        /// 合計報酬コインを計算して返す
        /// </summary>
        public static int CalcTotalCoin(this List<MasterTrainingChallengeScore.TrainingChallengeScore> self) =>
            self.Sum(elm => elm.RewardCoin);
    }
}
