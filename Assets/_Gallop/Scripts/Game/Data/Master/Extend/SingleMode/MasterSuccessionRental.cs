using UnityEngine;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 継承キャラレンタルマスター
    /// </summary>
    public partial class MasterSuccessionRental : AbstractMasterData
    {

        /// <summary>
        /// レンタルできる最大回数を取得
        /// </summary>
        /// <returns></returns>
        public int GetMaxRentalCount()
        {
            return dictionary.Values.Max(data => data.RentalNum);
        }
    }
}