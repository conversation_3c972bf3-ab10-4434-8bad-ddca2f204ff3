using UnityEngine;
using System.Collections;

namespace Gallop
{
    /// <summary>
    /// ピースCSV
    /// </summary>
    public partial class MasterPieceData : AbstractMasterData
    {
        public partial class PieceData
        {
            /// <summary>
            /// 名前
            /// </summary>
            public string Name
            {
                get
                {
                    if(_name == string.Empty)
                    {
                        _name = TextUtil.GetMasterText(MasterString.Category.MasterPieceName, Id);
                    }
                    return _name;
                }
            }
            private string _name = string.Empty;

            /// <summary>
            /// 説明
            /// </summary>
            public string Description
            {
                get
                {
                    if (_description == string.Empty)
                    {
                        _description = TextUtil.GetMasterText(MasterString.Category.MasterPieceDescription, Id);
                    }
                    return _description;
                }
            }
            private string _description = string.Empty;
        }
    }
}