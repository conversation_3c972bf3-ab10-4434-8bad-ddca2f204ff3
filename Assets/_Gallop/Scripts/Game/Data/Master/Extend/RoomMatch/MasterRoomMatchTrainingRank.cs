using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// ルームマッチで指定出来るランクのマスターデータ
    /// </summary>
    public partial class MasterRoomMatchTrainingRank
    {
        private List<RoomMatchTrainingRank> _rankList; // 一覧のキャッシュ
        
        /// <summary>
        /// マスター全要素を取得
        /// </summary>
        public List<RoomMatchTrainingRank> GetList()
        {
            if (_rankList == null)
            {
                _rankList = dictionary.Values.OrderBy(x => x.Id).ToList();
            }
            return _rankList;
        }
    }
}