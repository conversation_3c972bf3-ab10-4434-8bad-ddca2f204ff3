
namespace Gallop
{
    /// <summary>
    /// 全国興行：場所
    /// </summary>
    public partial class MasterJobsPlace
    {
        public partial class JobsPlace
        {
            // "東京レース場"のようなテキスト
            public string Name
            {
                get
                {
                    if (_name == null)
                    {
                        var masterRaceTrack = MasterDataManager.Instance.masterRaceTrack.Get(RaceTrackId);
                        if (masterRaceTrack != null)
                            _name = masterRaceTrack.Name;
                    }
                    return _name;
                }
            }
            private string _name = null;
        }
    }
}
