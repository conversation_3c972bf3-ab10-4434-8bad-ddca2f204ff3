using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 開催ごとの全体スケジュールや、お知らせid、チケット上限などを指定するマスターデータ
    /// </summary>
    public partial class MasterHeroesData
    {
        /// <summary>無効な heroes_id</summary>
        public const int INVALID_HEROES_ID = 0;

        /// <summary>
        /// 現在時刻を基準に「リーグオブヒーローズ」データを取得
        /// </summary>
        public HeroesData GetRealTimeActiveData()
        {
            if (dictionary == null)
                return null;

            var heroesDataList = dictionary.Values.ToList();

            foreach (var heroesData in heroesDataList)
            {
                if (heroesData.IsInTermRealTime())
                    return heroesData;
            }

            return null;
        }

        /// <summary>
        /// 第1回のデータを取得する
        /// </summary>
        public HeroesData GetFirstEventData()
        {
            if (dictionary == null)
                return null;

            var heroesDataList = dictionary.Values.ToList();
            if (heroesDataList == null || heroesDataList.Count <= 0)
                return null;

            return heroesDataList.OrderBy(data => data.StartDate).FirstOrDefault();
        }

        public partial class HeroesData
        {
            /// <summary>
            /// イベントの開催期間中か
            /// </summary>
            public bool IsInTermRealTime()
            {
                return MasterDataManager.Instance.masterHeroesStageSchedule.IsInTermRealTime(HeroesId);
            }
        }
    }
}