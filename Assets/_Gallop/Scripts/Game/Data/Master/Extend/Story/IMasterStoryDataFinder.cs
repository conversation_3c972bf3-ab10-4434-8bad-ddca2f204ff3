using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 各種StoryDataの章を取得するための共通インターフェイス
    /// </summary>
    public interface IMasterStoryDataFinder
    {
        /// <summary>
        /// 章を表すIDから検索する
        /// </summary>
        IStoryDataModel Find(int id);
    }

    /// <summary>
    /// 各種StoryDataの話を取得するための共通インターフェイス
    /// </summary>
    public interface IMasterStoryDataStoryFinder
    {
        /// <summary>
        /// IDから検索する
        /// </summary>
        IStoryDataStoryModel Find(int id);

        /// <summary>
        /// StoryIDとEpisodeIndexIDから検索する
        /// </summary>
        IStoryDataStoryModel FindByEpisodeIndexId(int storyId, int episodeIndexId);
    }
}
