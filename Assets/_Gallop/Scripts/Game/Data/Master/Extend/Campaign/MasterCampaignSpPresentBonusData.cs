using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static MasterCampaignWalkingLocation;

    public partial class MasterCampaignSpPresentBonusData
    {
        /// <summary>
        /// バレンタインで利用するデータを取得
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static CampaignSpPresentBonusData GetValentineDataWithCardId(int cardId)
        {
            return MasterDataManager.Instance.masterCampaignSpPresentBonusData
                .GetListWithCardIdOrderByIdAsc(cardId)
                .FirstOrDefault(data => data.TargetType == (int)MasterCampaignData.TargetCategory.Valentine);
        }

        /// <summary>
        /// バレンタインメッセージカードに表示する名前
        /// </summary>
        /// <returns></returns>
        public static string GetPresentName(int cardId)
        {
            return MasterDataManager.Instance.masterString.
                GetText(MasterString.Category.CampaignValentinePresentName, cardId);
        }

        /// <summary>
        /// バレンタインメッセージカードに表示するメッセージ（男性）
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetPresentMessageMale(int cardId)
        {
            return MasterDataManager.Instance.masterString.
                GetText(MasterString.Category.CampaignValentinePresentMessageMale, cardId);
        }

        /// <summary>
        /// バレンタインメッセージカードに表示するメッセージ（女性）
        /// </summary>
        /// <param name="cardId"></param>
        /// <returns></returns>
        public static string GetPresentMessageFemale(int cardId)
        {
            return MasterDataManager.Instance.masterString.
                GetText(MasterString.Category.CampaignValentinePresentMessageFemale, cardId);
        }
    }   
    
    public static class MasterCampaignSpPersentBonusDataUtils
    {
        /// <summary>
        /// うまさんぽローケーションデータを取得、背景などを引っ張りだす必要がある
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static CampaignWalkingLocation GetLocationData(this MasterCampaignSpPresentBonusData.CampaignSpPresentBonusData data)
        {
            return MasterDataManager.Instance.masterCampaignWalkingLocation.Get(data.LocationId);
        }

        /// <summary>
        /// プレゼント小物モデルプレハブを取得
        /// </summary>
        /// <param name="data"></param>
        /// <param name="loadHash"></param>
        /// <returns></returns>
        public static GameObject GetPropPrefab(this MasterCampaignSpPresentBonusData.CampaignSpPresentBonusData data, ResourceManager.ResourceHash loadHash)
        {
            var propPath = ResourcePath.GetCharaPropPath(data.PropId);
            var obj = ResourceManager.LoadOnHash<GameObject>(propPath, loadHash);
            if (obj == null)
            {
                Debug.LogError("指定された小物が見つかりませんでした。CSVかデータを確認してください。ID=" + data.PropId);
                return null;
            }

            return obj;
        }

        /// <summary>
        /// プレゼントアイコンパスを取得
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string GetMessageCardPresentIconPath(this MasterCampaignSpPresentBonusData.CampaignSpPresentBonusData data)
        {
            return TextUtil.Format(ResourcePath.CAMPAIGN_VALENTINE_CHOCOLATE_ICON_PATH, data.ItemId1);
        }

        /// <summary>
        /// アイテムポップアップに表示するアイコンパスを取得
        /// </summary>
        public static string GetItemPopUpIconPath(this MasterCampaignSpPresentBonusData.CampaignSpPresentBonusData data)
        {
            return TextUtil.Format(ResourcePath.CAMPAIGN_VALENTINE_ITEM_ICON_PATH, data.ItemId1);
        }

        /// <summary>
        /// キャラアイコンパスを取得
        /// </summary>
        /// <returns></returns>
        public static string GetMessageCardPresentCharaIconPath(this MasterCampaignSpPresentBonusData.CampaignSpPresentBonusData data)
        {
            var cardData = MasterDataManager.Instance.masterCardData.Get(data.CardId);
            if(cardData == null)
            {
                Debug.LogWarning($"card_id == {data.CardId}が見つかりませんでした!キャラアイコンパス取得が失敗しました！");
                return string.Empty;
            }

            var charaId = cardData.CharaId;

            return TextUtil.Format(ResourcePath.CAMPAIGN_VALENTINE_CHARA_ICON_PATH, charaId, data.CardId);
        }

        /// <summary>
        /// アイテムポップアップで表示するアイテム名
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string GetItemPopupPresentName(this MasterCampaignSpPresentBonusData.CampaignSpPresentBonusData data)
        {
            var cardData = MasterDataManager.Instance.masterCardData.Get(data.CardId);
            if (cardData == null)
            {
                Debug.LogWarning($"card_id == {data.CardId}が見つかりませんでした!");
                return string.Empty;
            }

            return TextUtil.Format(TextId.Present423001.Text(), cardData.Charaname);
        }
    }
}
