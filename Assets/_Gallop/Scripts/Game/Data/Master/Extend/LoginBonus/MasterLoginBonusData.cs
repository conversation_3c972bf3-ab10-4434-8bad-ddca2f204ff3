using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;

namespace Gallop
{
    public partial class MasterLoginBonusData
    {
        /// <summary>
        /// ログインボーナスのタイプ
        /// </summary>
        public enum BonusType
        {
            Normal = 1,      // 通常
            StartDash = 2,   // スタートダッシュ   
            Campaign = 3,    // キャンペーン
            MultipleStepBonus = 4, // 複数段階ボーナス種があるログインボーナス
        }

        public partial class LoginBonusData
        {
            /// <summary>
            /// ログインボーナス名
            /// </summary>
            public string Name
            {
                get { return TextUtil.GetMasterText(MasterString.Category.MasterLoginBonusName, this.Id); }
            }

            /// <summary>
            /// ログインボーナスのタイプ（型キャスト済み）
            /// </summary>
            public BonusType BonusType => (BonusType) Type;

            /// <summary>
            /// 対象のログインボーナスでもらえるLoginBonusDetailのリストをかえす
            /// </summary>
            public List<MasterLoginBonusDetail.LoginBonusDetail> GetLoginBonusDetailList()
            {
                if (GroupId == 0)
                {
                    return MasterDataManager.Instance.masterLoginBonusDetail.GetListWithLoginBonusIdOrderByCountAsc(Id);
                }

                // GroupIdの値が０ではない場合は、同一GroupIdのログインボーナスのLoginBonusDetailをすべてリストに入れる
                var masterLoginBonusList = MasterDataManager.Instance.masterLoginBonusData.GetListWithGroupIdOrderByStepAsc(GroupId);
                var loginBonusDetailList = new List<MasterLoginBonusDetail.LoginBonusDetail>();
                foreach(var masterLoginBonus in masterLoginBonusList)
                {
                    loginBonusDetailList.AddRange(MasterDataManager.Instance.masterLoginBonusDetail.GetListWithLoginBonusIdOrderByCountAsc(masterLoginBonus.Id));
                }

                return loginBonusDetailList;
            }

            public int GetCountNum()
            {
                if (GroupId == 0)
                {
                    return CountNum;
                }

                // GroupIdの値が０ではない場合は、同一GroupIdのログインボーナスのCountNumをすべて加算
                var totalCount = 0;
                var masterLoginBonusList = MasterDataManager.Instance.masterLoginBonusData.GetListWithGroupIdOrderByStepAsc(GroupId);
                foreach (var masterLoginBonus in masterLoginBonusList)
                {
                    totalCount += masterLoginBonus.CountNum;
                }

                return totalCount;
            }

            /// <summary>
            /// 翌日の報酬の有無にかかわらずNEXT_INを再生する必要があるか
            /// </summary>
            /// <returns></returns>
            public bool IsForcePlayNextShow()
            {
                return StaticVariableDefine.Home.LoginBonus.LOGIN_BONUS_LIST_FORCE_PLAY_NEXT_IN.Contains(Id);
            }

            /// <summary>
            /// 背景画像のIdを取得
            /// </summary>
            public int GetBgId()
            {
                if(BgId == 0)
                {
                    // 背景Idが0なら、ログボのIdを背景のIdとして使う
                    return Id;
                }

                return BgId;
            }
        }
    }
}