using System.Linq;

namespace Gallop
{
    public partial class MasterDeckRecommendOrganize
    {
        /// <summary>
        /// csvに記載された、枠ごとの種別 (優先配置するロジックが枠ごとに異なる)
        /// </summary>
        public enum SelectPriorityType
        {
            None = 0,       // 編成なし
            Turf = 101,     // スピード
            Pool = 105,     // スタミナ
            Dirt = 102,     // パワー
            Slope = 103,    // 根性
            Study = 106,    // 賢さ
            Scenario = 901, // シナリオ枠
        }
        
        /// <summary> 編成ロジックにおいて、ウマ娘の距離適性を考慮しない設定を取得する際のdistance_type </summary>
        private const int DEFAULT_SETTING_DISTANCE_TYPE = 0;
        
        /// <summary>
        /// 強化編成画面など、距離帯を考慮せずに編成をおこなう場合の設定取得
        /// </summary>
        /// <returns></returns>
        public DeckRecommendOrganize GetDefaultSetting()
        {
            var defaultList = GetListWithDistanceTypeOrderByPriorityAsc(DEFAULT_SETTING_DISTANCE_TYPE);
            return defaultList.FirstOrDefault(data => data.IsInTerm);
        }

        /// <summary>
        /// 距離帯を考慮した編成をおこなう場合の設定取得
        /// </summary>
        public DeckRecommendOrganize GetSettingByCourseDistanceType(RaceDefine.CourseDistanceType courseDistanceType)
        {
            var targetList = GetListWithDistanceTypeOrderByPriorityAsc((int)courseDistanceType);
            return targetList.FirstOrDefault(data => data.IsInTerm);
        }

        public partial class DeckRecommendOrganize
        {
            public bool IsInTerm => TimeUtil.IsInTerm(TimeUtil.GetDateTime(StartDate), TimeUtil.GetDateTime(EndDate));

            /// <summary>
            /// CommandId1~6カラムの各値をSelectPriorityTypeに変換した配列を取得する
            /// </summary>
            public SelectPriorityType[] SelectPriorityTypeArray
                => _selectPriorityTypeArrayCache ??=
                    (new[] { CommandId1, CommandId2, CommandId3, CommandId4, CommandId5, CommandId6 }).Select(id => (SelectPriorityType)id).ToArray();
            
            private SelectPriorityType[] _selectPriorityTypeArrayCache;
        }
    }
}
