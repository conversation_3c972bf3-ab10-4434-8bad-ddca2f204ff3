using System.Linq;
using System.Collections.Generic;

namespace Gallop
{
    public partial class MasterStoryEventStoryData : IMasterStoryDataStoryFinder
    {
        /// <summary>
        /// ストーリーの種類
        /// </summary>
        public enum StoryConditionType
        {
            Normal  = 0,   // 通常のストーリー
            Opening = 1,   // オープニング
            Ending  = 2,   // エンディング
        }

        public partial class StoryEventStoryData : IStoryDataStoryModel
        {
            #region IStoryDataStoryModel

            int IStoryDataStoryModel.EpisodeId => Id;

            int IStoryDataStoryModel.StoryId => StoryEventId;

            int IStoryDataStoryModel.EpisodeIndexId => EpisodeIndexId;

            bool IStoryDataStoryModel.InTerm => TimeUtil.IsTermTime(StartDate);

            string IStoryDataStoryModel.ThumbPath => ResourcePath.GetStoryEventExtraStoryThumbnailPath(StoryEventId, EpisodeIndexId);

            string IStoryDataStoryModel.PartTitle => StoryEventUtil.GetEventName(StoryEventId);

            /// <summary>
            /// 再生演出の配列
            /// </summary>
            /// <returns></returns>
            SubStoryDataSet[] IStoryDataStoryModel.GetSubStoryArray()
            {
                return new[]
                {
                    new SubStoryDataSet(StoryType1, StoryId1),
                    new SubStoryDataSet(StoryType2, StoryId2),
                    new SubStoryDataSet(StoryType3, StoryId3),
                    new SubStoryDataSet(StoryType4, StoryId4),
                    new SubStoryDataSet(StoryType5, StoryId5)
                };
            }


            /// <summary>
            /// ライブIDを取得。なければ-1。
            /// </summary>
            int IStoryDataStoryModel.GetLiveSetId()
            {
                // ライブはは1つしか設定されない想定
                if ((MainStoryDefine.StoryType)StoryType1 == MainStoryDefine.StoryType.Live) return StoryId1;
                if ((MainStoryDefine.StoryType)StoryType2 == MainStoryDefine.StoryType.Live) return StoryId2;
                if ((MainStoryDefine.StoryType)StoryType3 == MainStoryDefine.StoryType.Live) return StoryId3;
                if ((MainStoryDefine.StoryType)StoryType4 == MainStoryDefine.StoryType.Live) return StoryId4;
                if ((MainStoryDefine.StoryType)StoryType5 == MainStoryDefine.StoryType.Live) return StoryId5;
                return -1;
            }

            #endregion

            /// <summary> 各話タイトル </summary>
            public string Title => TextUtil.GetMasterText(MasterString.Category.StoryEventEpisodeTitle, Id);

            /// <summary> ストーリーの種類 </summary>
            public StoryConditionType ConditionType => (StoryConditionType) StoryConditionType;

            /// <summary>
            /// 前のストーリーがあれば取得
            /// </summary>
            /// <returns></returns>
            public StoryEventStoryData GetPrevStoryData()
            {
                return MasterDataManager.Instance.masterStoryEventStoryData
                    .GetListWithStoryEventIdOrderByIdAsc(StoryEventId)
                    .FirstOrDefault(s => s.EpisodeIndexId == EpisodeIndexId - 1);
            }

            /// <summary>
            /// 指定の報酬が設定されているか
            /// </summary>
            public bool IsExistsAddReward(int adRewardCategory, int addRewardId)
            {
                if (AddRewardCategory1 == adRewardCategory && AddRewardId1 == addRewardId)
                    return true;
                if (AddRewardCategory2 == adRewardCategory && AddRewardId2 == addRewardId)
                    return true;

                return false;
            }
        }


        #region IMasterStoryDataStoryFinder

        /// <summary>
        /// ストーリーIDからModelを検索する
        /// </summary>
        IStoryDataStoryModel IMasterStoryDataStoryFinder.Find(int id) => Get(id);


        /// <summary>
        /// StoryIDとEpisodeIndexIDから検索する
        /// </summary>
        IStoryDataStoryModel IMasterStoryDataStoryFinder.FindByEpisodeIndexId(int storyId, int episodeIndexId) =>
            GetListWithStoryEventIdOrderByIdAsc(storyId)?.FirstOrDefault(entity => entity.EpisodeIndexId == episodeIndexId);

        #endregion
    }
}