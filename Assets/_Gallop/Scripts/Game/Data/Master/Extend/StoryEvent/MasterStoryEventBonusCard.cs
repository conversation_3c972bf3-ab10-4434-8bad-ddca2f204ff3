namespace Gallop
{
    public partial class MasterStoryEventBonusCard
    {
        /// <summary>
        /// ストーリーイベント開催期間中の育成でボーナスを受けるカード情報
        /// </summary>
        public partial class StoryEventBonusCard
        {
            /// <summary>
            /// ボーナスを受けられるカードか
            /// </summary>
            public bool IsCanReceiveBonus(MasterCardData.CardData cardData)
            {
                if (CardId <= 0 && CharaId <= 0)
                    return false; // 仕様上ありえないデータ

                // 期間でフィルタリング
                var serverTimeStamp = TimeUtil.GetServerTimeStamp();
                if (serverTimeStamp < StartDate || EndDate < serverTimeStamp)
                    return false; // 期間外

                // カードIDが指定されているならカードIDでフィルタリング
                if (CardId > 0)
                {
                    if (CardId != cardData.Id)
                        return false;
                }
                // カードIDが指定されていないならキャラIDでフィルタリング
                else if (CharaId > 0)
                {
                    if (CharaId > 0)
                    {
                        if (CharaId != cardData.CharaId)
                            return false;
                    }
                }

                return true; // ボーナスを受けられる
            }

            /// <summary>
            /// ボーナスの倍率を返す
            /// </summary>
            /// <returns></returns>
            public int GetBonusRate(GameDefine.CardRarity cardRarity)
            {
                switch (cardRarity)
                {
                    case GameDefine.CardRarity.Rare1:
                        return Rarity1;
                    case GameDefine.CardRarity.Rare2:
                        return Rarity2;
                    case GameDefine.CardRarity.Rare3:
                        return Rarity3;
                    case GameDefine.CardRarity.Rare4:
                        return Rarity4;
                    case GameDefine.CardRarity.Rare5:
                        return Rarity5;
                    default:
                        break;
                }
                return 0;
            }
        }
    }
}
