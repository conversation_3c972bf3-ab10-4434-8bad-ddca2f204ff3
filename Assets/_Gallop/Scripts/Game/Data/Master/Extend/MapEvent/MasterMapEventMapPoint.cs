
namespace Gallop
{
    public partial class MasterMapEventMapPoint
    {
        public partial class MapEventMapPoint
        {
            /// <summary> マスの名称 </summary>
            public string Name => TextUtil.GetMasterText(MasterString.Category.MapEventMapPointName, Id);

            /// <summary> 動画タイトル </summary>
            public string MovieTitle => TextId.MapEvent497011.Format(NextEpisodeId);

            /// <summary> 動画予告テキスト1 </summary>
            public string MovieText1 => TextUtil.GetMasterText(MasterString.Category.MapEventMovieText1, Id);

            /// <summary> 動画予告テキスト2 </summary>
            public string MovieText2 => TextUtil.GetMasterText(MasterString.Category.MapEventMovieText2, Id);

            /// <summary> 動画予告テキスト3 </summary>
            public string MovieText3 => TextUtil.GetMasterText(MasterString.Category.MapEventMovieText3, Id);

            /// <summary> サムネイルパス </summary>
            public string ThumbPath => ResourcePath.GetMapEventMovieThumbnailPath(EventId, NextEpisodeId);
        }
    }
}
