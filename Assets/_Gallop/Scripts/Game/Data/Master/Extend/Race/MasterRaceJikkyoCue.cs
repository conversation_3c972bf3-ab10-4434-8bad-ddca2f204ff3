using System.Linq;
using System.Collections.Generic;

namespace Gallop
{
    public partial class MasterRaceJikkyoCue
    {
        public partial class RaceJikkyoCue
        {
            private bool? _isPassJikkyoCommandCheck = null;
            
            public bool IsConditionMatch(Jikkyo.JikkyoSceneType sceneType, RaceDefine.RaceType raceType, List<int> charaIdList)
            {
#if CYG_DEBUG
                // 実況トリガーを無条件パスにしている場合は、全部ロードしておく必要がある
                _isPassJikkyoCommandCheck ??= RaceDebugger.IsPassJikkyoCommandCheckDic.ContainsValue(true);
                if (_isPassJikkyoCommandCheck == true && RaceManager.RaceInfo.IsRaceDirectRace)
                {
                    return true;
                }
#endif
                if (!IsConditionMatchInner((Jikkyo.JikkyoCueConditionType)ConditionType1, ConditionValue1, sceneType, raceType, charaIdList)) return false;
                if (!IsConditionMatchInner((Jikkyo.JikkyoCueConditionType)ConditionType2, ConditionValue2, sceneType, raceType, charaIdList)) return false;
                if (!IsConditionMatchInner((Jikkyo.JikkyoCueConditionType)ConditionType3, ConditionValue3, sceneType, raceType, charaIdList)) return false;

                return true;
            }

            private static bool IsConditionMatchInner(Jikkyo.JikkyoCueConditionType conditionType, int conditionValue, Jikkyo.JikkyoSceneType sceneType, RaceDefine.RaceType raceType, List<int> charaIdList)
            {
                switch (conditionType)
                {
                    case Jikkyo.JikkyoCueConditionType.Null:
                        return true;
                    
                    case Jikkyo.JikkyoCueConditionType.JikkyoType:
                        return (int)sceneType == conditionValue;
                    
                    case Jikkyo.JikkyoCueConditionType.RaceType:
                        if (conditionValue == (int)RaceDefine.RaceType.Single)
                        {
                            return RaceUtil.IsSingleMode(raceType);
                        }
                        else
                        {
                            return (int)raceType == conditionValue;
                        }
                    
                    case Jikkyo.JikkyoCueConditionType.SingleModeScenario:
                        return (int)WorkDataManager.Instance.SingleMode.GetScenarioId() == conditionValue;

                    case Jikkyo.JikkyoCueConditionType.CharaId:
                        return charaIdList.IndexOf(conditionValue) >= 0;

                    default:
                        Debug.LogWarning($"未定義のconditionType : {conditionType}");
                        return false;
                }
            }
        }
    }
}