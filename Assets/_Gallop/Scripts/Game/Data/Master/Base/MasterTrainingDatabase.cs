// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: training
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterTrainingDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterTrainingCuttCharaData masterTrainingCuttCharaData { get; private set; }
        public MasterTrainingCuttData masterTrainingCuttData           { get; private set; }
        public MasterTrainingCuttGroupData masterTrainingCuttGroupData { get; private set; }
        public MasterTrainingCuttFlash masterTrainingCuttFlash         { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTrainingCuttCharaData = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterTrainingCuttFlash     = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_trainingCuttCharaData_commandId_subId = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_trainingCuttData_commandId_subId      = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_trainingCuttGroupData_groupId         = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterTrainingDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterTrainingCuttCharaData = new MasterTrainingCuttCharaData(this);
            this.masterTrainingCuttData      = new MasterTrainingCuttData(this);
            this.masterTrainingCuttGroupData = new MasterTrainingCuttGroupData(this);
            this.masterTrainingCuttFlash     = new MasterTrainingCuttFlash(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet training database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterTrainingCuttCharaData != null) { _selectQuery_masterTrainingCuttCharaData.Dispose(); _selectQuery_masterTrainingCuttCharaData = null; }
            if (_indexedSelectQuery_trainingCuttCharaData_commandId_subId != null) { _indexedSelectQuery_trainingCuttCharaData_commandId_subId.Dispose(); _indexedSelectQuery_trainingCuttCharaData_commandId_subId = null; }
            if (_indexedSelectQuery_trainingCuttData_commandId_subId != null) { _indexedSelectQuery_trainingCuttData_commandId_subId.Dispose(); _indexedSelectQuery_trainingCuttData_commandId_subId = null; }
            if (_indexedSelectQuery_trainingCuttGroupData_groupId != null) { _indexedSelectQuery_trainingCuttGroupData_groupId.Dispose(); _indexedSelectQuery_trainingCuttGroupData_groupId = null; }
            if (_selectQuery_masterTrainingCuttFlash != null) { _selectQuery_masterTrainingCuttFlash.Dispose(); _selectQuery_masterTrainingCuttFlash = null; }
            if (this.masterTrainingCuttCharaData != null) { this.masterTrainingCuttCharaData.Unload(); }
            if (this.masterTrainingCuttData != null) { this.masterTrainingCuttData.Unload(); }
            if (this.masterTrainingCuttGroupData != null) { this.masterTrainingCuttGroupData.Unload(); }
            if (this.masterTrainingCuttFlash != null) { this.masterTrainingCuttFlash.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for training/training_cutt_chara_data
        
        /// <summary>
        /// SELECT `command_id`,`sub_id`,`chara_num`,`chara_id`,`dress_id`,`target_timeline`,`target_list_index`,`prop_target`,`selector_label` FROM `training_cutt_chara_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TrainingCuttCharaData()
        {
            if (_selectQuery_masterTrainingCuttCharaData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTrainingCuttCharaData = connection.PreparedQuery("SELECT `command_id`,`sub_id`,`chara_num`,`chara_id`,`dress_id`,`target_timeline`,`target_list_index`,`prop_target`,`selector_label` FROM `training_cutt_chara_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterTrainingCuttCharaData;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_num`,`chara_id`,`dress_id`,`target_timeline`,`target_list_index`,`prop_target`,`selector_label` FROM `training_cutt_chara_data` WHERE `command_id`=? AND `sub_id`=? ORDER BY `chara_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TrainingCuttCharaData_CommandId_SubId()
        {
            if (_indexedSelectQuery_trainingCuttCharaData_commandId_subId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_trainingCuttCharaData_commandId_subId = connection.PreparedQuery("SELECT `id`,`chara_num`,`chara_id`,`dress_id`,`target_timeline`,`target_list_index`,`prop_target`,`selector_label` FROM `training_cutt_chara_data` WHERE `command_id`=? AND `sub_id`=? ORDER BY `chara_id` ASC;");
            }
        
            return _indexedSelectQuery_trainingCuttCharaData_commandId_subId;
        }
        
        /// <summary>
        /// SELECT `id`,`command_id`,`sub_id`,`chara_num`,`chara_id`,`dress_id`,`target_timeline`,`target_list_index`,`prop_target`,`selector_label` FROM `training_cutt_chara_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TrainingCuttCharaData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`command_id`,`sub_id`,`chara_num`,`chara_id`,`dress_id`,`target_timeline`,`target_list_index`,`prop_target`,`selector_label` FROM `training_cutt_chara_data`;");
        }
        
        // SQL statements for training/training_cutt_data
        
        /// <summary>
        /// SELECT `chara_num`,`target_chara_index`,`target_value`,`cutt_index`,`success_flg` FROM `training_cutt_data` WHERE `command_id`=? AND `sub_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TrainingCuttData_CommandId_SubId()
        {
            if (_indexedSelectQuery_trainingCuttData_commandId_subId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_trainingCuttData_commandId_subId = connection.PreparedQuery("SELECT `chara_num`,`target_chara_index`,`target_value`,`cutt_index`,`success_flg` FROM `training_cutt_data` WHERE `command_id`=? AND `sub_id`=?;");
            }
        
            return _indexedSelectQuery_trainingCuttData_commandId_subId;
        }
        
        /// <summary>
        /// SELECT `command_id`,`sub_id`,`chara_num`,`target_chara_index`,`target_value`,`cutt_index`,`success_flg` FROM `training_cutt_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TrainingCuttData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `command_id`,`sub_id`,`chara_num`,`target_chara_index`,`target_value`,`cutt_index`,`success_flg` FROM `training_cutt_data`;");
        }
        
        // SQL statements for training/training_cutt_group_data
        
        /// <summary>
        /// SELECT `order`,`command_id`,`sub_id` FROM `training_cutt_group_data` WHERE `group_id`=? ORDER BY `order` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_TrainingCuttGroupData_GroupId()
        {
            if (_indexedSelectQuery_trainingCuttGroupData_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_trainingCuttGroupData_groupId = connection.PreparedQuery("SELECT `order`,`command_id`,`sub_id` FROM `training_cutt_group_data` WHERE `group_id`=? ORDER BY `order` ASC;");
            }
        
            return _indexedSelectQuery_trainingCuttGroupData_groupId;
        }
        
        /// <summary>
        /// SELECT `group_id`,`order`,`command_id`,`sub_id` FROM `training_cutt_group_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TrainingCuttGroupData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `group_id`,`order`,`command_id`,`sub_id` FROM `training_cutt_group_data`;");
        }
        
        // SQL statements for training/training_cutt_flash
        
        /// <summary>
        /// SELECT `sub_folder`,`file_format`,`control_type` FROM `training_cutt_flash` WHERE `command_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_TrainingCuttFlash()
        {
            if (_selectQuery_masterTrainingCuttFlash == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterTrainingCuttFlash = connection.PreparedQuery("SELECT `sub_folder`,`file_format`,`control_type` FROM `training_cutt_flash` WHERE `command_id`=?;");
            }
        
            return _selectQuery_masterTrainingCuttFlash;
        }
        
        /// <summary>
        /// SELECT `command_id`,`sub_folder`,`file_format`,`control_type` FROM `training_cutt_flash`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_TrainingCuttFlash()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `command_id`,`sub_folder`,`file_format`,`control_type` FROM `training_cutt_flash`;");
        }
    }
}