// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: support_deck/deck_recommend_lv_correction
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterDeckRecommendLvCorrection : AbstractMasterData
    {
        public const string TABLE_NAME = "deck_recommend_lv_correction";

        MasterSupportDeckDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, DeckRecommendLvCorrection> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, DeckRecommendLvCorrection> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterDeckRecommendLvCorrection");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterDeckRecommendLvCorrection(MasterSupportDeckDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, DeckRecommendLvCorrection>();
            _db = db;
        }


        public DeckRecommendLvCorrection Get(int conditionType)
        {
            int key = (int)conditionType;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterDeckRecommendLvCorrection");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(conditionType);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterDeckRecommendLvCorrection", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private DeckRecommendLvCorrection _SelectOne(int conditionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_DeckRecommendLvCorrection();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for DeckRecommendLvCorrection");
                return null;
            }

            // SELECT `correction_lv` FROM `deck_recommend_lv_correction` WHERE `condition_type`=?;
            if (!query.BindInt(1, conditionType)) { return null; }

            DeckRecommendLvCorrection orm = null;

            if (query.Step())
            {
                int correctionLv = (int)query.GetInt(0);

                orm = new DeckRecommendLvCorrection(conditionType, correctionLv);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", conditionType));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_DeckRecommendLvCorrection()) {
                while (query.Step()) {
                    int conditionType = (int)query.GetInt(0);
                    int correctionLv  = (int)query.GetInt(1);

                    int key = (int)conditionType;
                    DeckRecommendLvCorrection orm = new DeckRecommendLvCorrection(conditionType, correctionLv);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class DeckRecommendLvCorrection
        {
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: correction_lv) </summary>
            public readonly int CorrectionLv;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public DeckRecommendLvCorrection(int conditionType = 0, int correctionLv = 0)
            {
                this.ConditionType = conditionType;
                this.CorrectionLv  = correctionLv;
            }
        }
    }
}
