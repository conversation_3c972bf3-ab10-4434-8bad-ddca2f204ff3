// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: fan_raid/fan_raid_individual_reward
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:fan_raid_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterFanRaidIndividualReward : AbstractMasterData
    {
        public const string TABLE_NAME = "fan_raid_individual_reward";

        MasterFanRaidDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, FanRaidIndividualReward> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<FanRaidIndividualReward>> _dictionaryWithFanRaidId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterFanRaidIndividualReward(MasterFanRaidDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, FanRaidIndividualReward>();
            _dictionaryWithFanRaidId = new Dictionary<int, List<FanRaidIndividualReward>>();
            _db = db;
        }


        public FanRaidIndividualReward Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterFanRaidIndividualReward");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterFanRaidIndividualReward", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private FanRaidIndividualReward _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_FanRaidIndividualReward();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FanRaidIndividualReward");
                return null;
            }

            // SELECT `fan_raid_id`,`individual_fan`,`item_category`,`item_id`,`item_num` FROM `fan_raid_individual_reward` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            FanRaidIndividualReward orm = null;

            if (query.Step())
            {
                int fanRaidId     = (int)query.GetInt(0);
                int individualFan = (int)query.GetInt(1);
                int itemCategory  = (int)query.GetInt(2);
                int itemId        = (int)query.GetInt(3);
                int itemNum       = (int)query.GetInt(4);

                orm = new FanRaidIndividualReward(id, fanRaidId, individualFan, itemCategory, itemId, itemNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public FanRaidIndividualReward GetWithFanRaidIdOrderByIdAsc(int fanRaidId)
        {
            FanRaidIndividualReward orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithFanRaidIdOrderByIdAsc(fanRaidId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", fanRaidId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private FanRaidIndividualReward _SelectWithFanRaidIdOrderByIdAsc(int fanRaidId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_FanRaidIndividualReward_FanRaidId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FanRaidIndividualReward");
                return null;
            }

            // SELECT `id`,`individual_fan`,`item_category`,`item_id`,`item_num` FROM `fan_raid_individual_reward` WHERE `fan_raid_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, fanRaidId)) { return null; }

            FanRaidIndividualReward orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithFanRaidIdOrderByIdAsc(query, fanRaidId);
            }

            query.Reset();

            return orm;
        }

        public List<FanRaidIndividualReward> GetListWithFanRaidIdOrderByIdAsc(int fanRaidId)
        {
            int key = (int)fanRaidId;
            
            if (!_dictionaryWithFanRaidId.ContainsKey(key)) {
                _dictionaryWithFanRaidId.Add(key, _ListSelectWithFanRaidIdOrderByIdAsc(fanRaidId));
            }

            return _dictionaryWithFanRaidId[key];
      
        }

        public List<FanRaidIndividualReward> MaybeListWithFanRaidIdOrderByIdAsc(int fanRaidId)
        {
            List<FanRaidIndividualReward> list = GetListWithFanRaidIdOrderByIdAsc(fanRaidId);
            return list.Count > 0 ? list : null;
        }

        private List<FanRaidIndividualReward> _ListSelectWithFanRaidIdOrderByIdAsc(int fanRaidId)
        {
            List<FanRaidIndividualReward> _list = new List<FanRaidIndividualReward>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_FanRaidIndividualReward_FanRaidId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for FanRaidIndividualReward");
                return null;
            }

            // SELECT `id`,`individual_fan`,`item_category`,`item_id`,`item_num` FROM `fan_raid_individual_reward` WHERE `fan_raid_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, fanRaidId)) { return null; }

            while (query.Step()) {
                FanRaidIndividualReward orm = _CreateOrmByQueryResultWithFanRaidIdOrderByIdAsc(query, fanRaidId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private FanRaidIndividualReward _CreateOrmByQueryResultWithFanRaidIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int fanRaidId)
        {
            int id            = (int)query.GetInt(0);
            int individualFan = (int)query.GetInt(1);
            int itemCategory  = (int)query.GetInt(2);
            int itemId        = (int)query.GetInt(3);
            int itemNum       = (int)query.GetInt(4);

            return new FanRaidIndividualReward(id, fanRaidId, individualFan, itemCategory, itemId, itemNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithFanRaidId.Clear();
        }



        public sealed partial class FanRaidIndividualReward
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: fan_raid_id) </summary>
            public readonly int FanRaidId;
            /// <summary> (CSV column: individual_fan) </summary>
            public readonly int IndividualFan;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: item_num) </summary>
            public readonly int ItemNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public FanRaidIndividualReward(int id = 0, int fanRaidId = 0, int individualFan = 0, int itemCategory = 0, int itemId = 0, int itemNum = 0)
            {
                this.Id            = id;
                this.FanRaidId     = fanRaidId;
                this.IndividualFan = individualFan;
                this.ItemCategory  = itemCategory;
                this.ItemId        = itemId;
                this.ItemNum       = itemNum;
            }
        }
    }
}
