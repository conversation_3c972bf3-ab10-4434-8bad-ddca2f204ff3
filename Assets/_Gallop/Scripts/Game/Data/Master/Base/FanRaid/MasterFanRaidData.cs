// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: fan_raid/fan_raid_data
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterFanRaidData : AbstractMasterData
    {
        public const string TABLE_NAME = "fan_raid_data";

        MasterFanRaidDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, FanRaidData> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterFanRaidData(MasterFanRaidDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, FanRaidData>();
            _db = db;
        }


        public FanRaidData Get(int fanRaidId)
        {
            int key = (int)fanRaidId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterFanRaidData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(fanRaidId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterFanRaidData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private FanRaidData _SelectOne(int fanRaidId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_FanRaidData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FanRaidData");
                return null;
            }

            // SELECT `condition_type`,`condition_value`,`chara_id`,`dress_id`,`result_se_cue_name`,`result_se_cuesheet_name`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `fan_raid_data` WHERE `fan_raid_id`=?;
            if (!query.BindInt(1, fanRaidId)) { return null; }

            FanRaidData orm = null;

            if (query.Step())
            {
                byte conditionType          = (byte)query.GetInt(0);
                byte conditionValue         = (byte)query.GetInt(1);
                int charaId                 = (int)query.GetInt(2);
                int dressId                 = (int)query.GetInt(3);
                string resultSeCueName      = query.GetText(4);
                string resultSeCuesheetName = query.GetText(5);
                long startDate              = (long)query.GetLong(6);
                long calcStartDate          = (long)query.GetLong(7);
                long calcEndDate            = (long)query.GetLong(8);
                long endDate                = (long)query.GetLong(9);

                orm = new FanRaidData(fanRaidId, conditionType, conditionValue, charaId, dressId, resultSeCueName, resultSeCuesheetName, startDate, calcStartDate, calcEndDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", fanRaidId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class FanRaidData
        {
            /// <summary> (CSV column: fan_raid_id) </summary>
            public readonly int FanRaidId;
            /// <summary>
            /// 
            /// (CSV column: condition_type)
            /// (enum eConditionType)
            /// </summary>
            public readonly byte ConditionType;
            /// <summary>
            /// 
            /// (CSV column: condition_value)
            /// (enum eConditionValue)
            /// </summary>
            public readonly byte ConditionValue;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: dress_id) </summary>
            public readonly int DressId;
            /// <summary> (CSV column: result_se_cue_name) </summary>
            public readonly string ResultSeCueName;
            /// <summary> (CSV column: result_se_cuesheet_name) </summary>
            public readonly string ResultSeCuesheetName;
            /// <summary> (CSV column: start_date) </summary>
            public readonly long StartDate;
            /// <summary> (CSV column: calc_start_date) </summary>
            public readonly long CalcStartDate;
            /// <summary> (CSV column: calc_end_date) </summary>
            public readonly long CalcEndDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public FanRaidData(int fanRaidId = 0, byte conditionType = 0, byte conditionValue = 0, int charaId = 0, int dressId = 0, string resultSeCueName = "", string resultSeCuesheetName = "", long startDate = 0, long calcStartDate = 0, long calcEndDate = 0, long endDate = 0)
            {
                this.FanRaidId            = fanRaidId;
                this.ConditionType        = conditionType;
                this.ConditionValue       = conditionValue;
                this.CharaId              = charaId;
                this.DressId              = dressId;
                this.ResultSeCueName      = resultSeCueName;
                this.ResultSeCuesheetName = resultSeCuesheetName;
                this.StartDate            = startDate;
                this.CalcStartDate        = calcStartDate;
                this.CalcEndDate          = calcEndDate;
                this.EndDate              = endDate;
            }
        }
    }
}
