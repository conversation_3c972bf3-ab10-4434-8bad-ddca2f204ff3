// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: collect_raid
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterCollectRaidDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterCollectRaidMaster masterCollectRaidMaster                     { get; private set; }
        public MasterCollectRaidIndividualReward masterCollectRaidIndividualReward { get; private set; }
        public MasterCollectRaidAllReward masterCollectRaidAllReward               { get; private set; }
        public MasterCollectRaidMissionTopChara masterCollectRaidMissionTopChara   { get; private set; }
        public MasterCollectRaidStagingMiniChara masterCollectRaidStagingMiniChara { get; private set; }
        public MasterCollectRaidStory masterCollectRaidStory                       { get; private set; }
        public MasterCollectRaidMission masterCollectRaidMission                   { get; private set; }
        public MasterCollectRaidSegmentCutt masterCollectRaidSegmentCutt           { get; private set; }
        public MasterCollectRaidTopData masterCollectRaidTopData                   { get; private set; }
        public MasterCollectRaidDressColor masterCollectRaidDressColor             { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidMaster           = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidIndividualReward = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidAllReward        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidMissionTopChara  = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidStagingMiniChara = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidStory            = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidMission          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidSegmentCutt      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidTopData          = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterCollectRaidDressColor       = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidMaster_individualRewardSetId                         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidAllReward_allRewardSetId                             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId               = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId             = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidStory_collectRaidId                                  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId                   = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidMission_missionDataId                                = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidTopData_collectRaidId                                = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_collectRaidDressColor_collectRaidId                             = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterCollectRaidDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterCollectRaidMaster           = new MasterCollectRaidMaster(this);
            this.masterCollectRaidIndividualReward = new MasterCollectRaidIndividualReward(this);
            this.masterCollectRaidAllReward        = new MasterCollectRaidAllReward(this);
            this.masterCollectRaidMissionTopChara  = new MasterCollectRaidMissionTopChara(this);
            this.masterCollectRaidStagingMiniChara = new MasterCollectRaidStagingMiniChara(this);
            this.masterCollectRaidStory            = new MasterCollectRaidStory(this);
            this.masterCollectRaidMission          = new MasterCollectRaidMission(this);
            this.masterCollectRaidSegmentCutt      = new MasterCollectRaidSegmentCutt(this);
            this.masterCollectRaidTopData          = new MasterCollectRaidTopData(this);
            this.masterCollectRaidDressColor       = new MasterCollectRaidDressColor(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet collect_raid database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterCollectRaidMaster != null) { _selectQuery_masterCollectRaidMaster.Dispose(); _selectQuery_masterCollectRaidMaster = null; }
            if (_indexedSelectQuery_collectRaidMaster_individualRewardSetId != null) { _indexedSelectQuery_collectRaidMaster_individualRewardSetId.Dispose(); _indexedSelectQuery_collectRaidMaster_individualRewardSetId = null; }
            if (_selectQuery_masterCollectRaidIndividualReward != null) { _selectQuery_masterCollectRaidIndividualReward.Dispose(); _selectQuery_masterCollectRaidIndividualReward = null; }
            if (_indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId != null) { _indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId.Dispose(); _indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId = null; }
            if (_selectQuery_masterCollectRaidAllReward != null) { _selectQuery_masterCollectRaidAllReward.Dispose(); _selectQuery_masterCollectRaidAllReward = null; }
            if (_indexedSelectQuery_collectRaidAllReward_allRewardSetId != null) { _indexedSelectQuery_collectRaidAllReward_allRewardSetId.Dispose(); _indexedSelectQuery_collectRaidAllReward_allRewardSetId = null; }
            if (_selectQuery_masterCollectRaidMissionTopChara != null) { _selectQuery_masterCollectRaidMissionTopChara.Dispose(); _selectQuery_masterCollectRaidMissionTopChara = null; }
            if (_indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId != null) { _indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId.Dispose(); _indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId = null; }
            if (_selectQuery_masterCollectRaidStagingMiniChara != null) { _selectQuery_masterCollectRaidStagingMiniChara.Dispose(); _selectQuery_masterCollectRaidStagingMiniChara = null; }
            if (_indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId != null) { _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId.Dispose(); _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId = null; }
            if (_indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType != null) { _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType.Dispose(); _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType = null; }
            if (_selectQuery_masterCollectRaidStory != null) { _selectQuery_masterCollectRaidStory.Dispose(); _selectQuery_masterCollectRaidStory = null; }
            if (_indexedSelectQuery_collectRaidStory_collectRaidId != null) { _indexedSelectQuery_collectRaidStory_collectRaidId.Dispose(); _indexedSelectQuery_collectRaidStory_collectRaidId = null; }
            if (_indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId != null) { _indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId.Dispose(); _indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId = null; }
            if (_selectQuery_masterCollectRaidMission != null) { _selectQuery_masterCollectRaidMission.Dispose(); _selectQuery_masterCollectRaidMission = null; }
            if (_indexedSelectQuery_collectRaidMission_missionDataId != null) { _indexedSelectQuery_collectRaidMission_missionDataId.Dispose(); _indexedSelectQuery_collectRaidMission_missionDataId = null; }
            if (_selectQuery_masterCollectRaidSegmentCutt != null) { _selectQuery_masterCollectRaidSegmentCutt.Dispose(); _selectQuery_masterCollectRaidSegmentCutt = null; }
            if (_selectQuery_masterCollectRaidTopData != null) { _selectQuery_masterCollectRaidTopData.Dispose(); _selectQuery_masterCollectRaidTopData = null; }
            if (_indexedSelectQuery_collectRaidTopData_collectRaidId != null) { _indexedSelectQuery_collectRaidTopData_collectRaidId.Dispose(); _indexedSelectQuery_collectRaidTopData_collectRaidId = null; }
            if (_selectQuery_masterCollectRaidDressColor != null) { _selectQuery_masterCollectRaidDressColor.Dispose(); _selectQuery_masterCollectRaidDressColor = null; }
            if (_indexedSelectQuery_collectRaidDressColor_collectRaidId != null) { _indexedSelectQuery_collectRaidDressColor_collectRaidId.Dispose(); _indexedSelectQuery_collectRaidDressColor_collectRaidId = null; }
            if (this.masterCollectRaidMaster != null) { this.masterCollectRaidMaster.Unload(); }
            if (this.masterCollectRaidIndividualReward != null) { this.masterCollectRaidIndividualReward.Unload(); }
            if (this.masterCollectRaidAllReward != null) { this.masterCollectRaidAllReward.Unload(); }
            if (this.masterCollectRaidMissionTopChara != null) { this.masterCollectRaidMissionTopChara.Unload(); }
            if (this.masterCollectRaidStagingMiniChara != null) { this.masterCollectRaidStagingMiniChara.Unload(); }
            if (this.masterCollectRaidStory != null) { this.masterCollectRaidStory.Unload(); }
            if (this.masterCollectRaidMission != null) { this.masterCollectRaidMission.Unload(); }
            if (this.masterCollectRaidSegmentCutt != null) { this.masterCollectRaidSegmentCutt.Unload(); }
            if (this.masterCollectRaidTopData != null) { this.masterCollectRaidTopData.Unload(); }
            if (this.masterCollectRaidDressColor != null) { this.masterCollectRaidDressColor.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for collect_raid/collect_raid_master
        
        /// <summary>
        /// SELECT `use_mission`,`use_transition_dialog`,`story_extra_id`,`story_voice_type`,`use_all_reward_voice`,`collect_item_id`,`gain_collect_item_rate`,`gain_collect_item_min`,`individual_reward_set_id`,`all_reward_set_id`,`mission_top_chara_group_id`,`staging_mini_chara_group_id`,`story_list_bg_id`,`story_list_bg_sub_id`,`collect_event_map_master_id`,`notice_date`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `collect_raid_master` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidMaster()
        {
            if (_selectQuery_masterCollectRaidMaster == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidMaster = connection.PreparedQuery("SELECT `use_mission`,`use_transition_dialog`,`story_extra_id`,`story_voice_type`,`use_all_reward_voice`,`collect_item_id`,`gain_collect_item_rate`,`gain_collect_item_min`,`individual_reward_set_id`,`all_reward_set_id`,`mission_top_chara_group_id`,`staging_mini_chara_group_id`,`story_list_bg_id`,`story_list_bg_sub_id`,`collect_event_map_master_id`,`notice_date`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `collect_raid_master` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidMaster;
        }
        
        /// <summary>
        /// SELECT `id`,`use_mission`,`use_transition_dialog`,`story_extra_id`,`story_voice_type`,`use_all_reward_voice`,`collect_item_id`,`gain_collect_item_rate`,`gain_collect_item_min`,`all_reward_set_id`,`mission_top_chara_group_id`,`staging_mini_chara_group_id`,`story_list_bg_id`,`story_list_bg_sub_id`,`collect_event_map_master_id`,`notice_date`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `collect_raid_master` WHERE `individual_reward_set_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidMaster_IndividualRewardSetId()
        {
            if (_indexedSelectQuery_collectRaidMaster_individualRewardSetId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidMaster_individualRewardSetId = connection.PreparedQuery("SELECT `id`,`use_mission`,`use_transition_dialog`,`story_extra_id`,`story_voice_type`,`use_all_reward_voice`,`collect_item_id`,`gain_collect_item_rate`,`gain_collect_item_min`,`all_reward_set_id`,`mission_top_chara_group_id`,`staging_mini_chara_group_id`,`story_list_bg_id`,`story_list_bg_sub_id`,`collect_event_map_master_id`,`notice_date`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `collect_raid_master` WHERE `individual_reward_set_id`=?;");
            }
        
            return _indexedSelectQuery_collectRaidMaster_individualRewardSetId;
        }
        
        /// <summary>
        /// SELECT `id`,`use_mission`,`use_transition_dialog`,`story_extra_id`,`story_voice_type`,`use_all_reward_voice`,`collect_item_id`,`gain_collect_item_rate`,`gain_collect_item_min`,`individual_reward_set_id`,`all_reward_set_id`,`mission_top_chara_group_id`,`staging_mini_chara_group_id`,`story_list_bg_id`,`story_list_bg_sub_id`,`collect_event_map_master_id`,`notice_date`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `collect_raid_master`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidMaster()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`use_mission`,`use_transition_dialog`,`story_extra_id`,`story_voice_type`,`use_all_reward_voice`,`collect_item_id`,`gain_collect_item_rate`,`gain_collect_item_min`,`individual_reward_set_id`,`all_reward_set_id`,`mission_top_chara_group_id`,`staging_mini_chara_group_id`,`story_list_bg_id`,`story_list_bg_sub_id`,`collect_event_map_master_id`,`notice_date`,`start_date`,`calc_start_date`,`calc_end_date`,`end_date` FROM `collect_raid_master`;");
        }
        
        // SQL statements for collect_raid/collect_raid_individual_reward
        
        /// <summary>
        /// SELECT `individual_reward_set_id`,`individual_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_individual_reward` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidIndividualReward()
        {
            if (_selectQuery_masterCollectRaidIndividualReward == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidIndividualReward = connection.PreparedQuery("SELECT `individual_reward_set_id`,`individual_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_individual_reward` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidIndividualReward;
        }
        
        /// <summary>
        /// SELECT `id`,`individual_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_individual_reward` WHERE `individual_reward_set_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidIndividualReward_IndividualRewardSetId()
        {
            if (_indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId = connection.PreparedQuery("SELECT `id`,`individual_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_individual_reward` WHERE `individual_reward_set_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidIndividualReward_individualRewardSetId;
        }
        
        /// <summary>
        /// SELECT `id`,`individual_reward_set_id`,`individual_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_individual_reward`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidIndividualReward()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`individual_reward_set_id`,`individual_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_individual_reward`;");
        }
        
        // SQL statements for collect_raid/collect_raid_all_reward
        
        /// <summary>
        /// SELECT `all_reward_set_id`,`all_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_all_reward` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidAllReward()
        {
            if (_selectQuery_masterCollectRaidAllReward == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidAllReward = connection.PreparedQuery("SELECT `all_reward_set_id`,`all_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_all_reward` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidAllReward;
        }
        
        /// <summary>
        /// SELECT `id`,`all_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_all_reward` WHERE `all_reward_set_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidAllReward_AllRewardSetId()
        {
            if (_indexedSelectQuery_collectRaidAllReward_allRewardSetId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidAllReward_allRewardSetId = connection.PreparedQuery("SELECT `id`,`all_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_all_reward` WHERE `all_reward_set_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidAllReward_allRewardSetId;
        }
        
        /// <summary>
        /// SELECT `id`,`all_reward_set_id`,`all_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_all_reward`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidAllReward()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`all_reward_set_id`,`all_collect_item_num`,`item_category`,`item_id`,`item_num` FROM `collect_raid_all_reward`;");
        }
        
        // SQL statements for collect_raid/collect_raid_mission_top_chara
        
        /// <summary>
        /// SELECT `mission_top_chara_group_id`,`ending_flag`,`chara_id`,`dress_id`,`bg_id`,`bg_sub_id` FROM `collect_raid_mission_top_chara` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidMissionTopChara()
        {
            if (_selectQuery_masterCollectRaidMissionTopChara == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidMissionTopChara = connection.PreparedQuery("SELECT `mission_top_chara_group_id`,`ending_flag`,`chara_id`,`dress_id`,`bg_id`,`bg_sub_id` FROM `collect_raid_mission_top_chara` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidMissionTopChara;
        }
        
        /// <summary>
        /// SELECT `id`,`ending_flag`,`chara_id`,`dress_id`,`bg_id`,`bg_sub_id` FROM `collect_raid_mission_top_chara` WHERE `mission_top_chara_group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidMissionTopChara_MissionTopCharaGroupId()
        {
            if (_indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId = connection.PreparedQuery("SELECT `id`,`ending_flag`,`chara_id`,`dress_id`,`bg_id`,`bg_sub_id` FROM `collect_raid_mission_top_chara` WHERE `mission_top_chara_group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidMissionTopChara_missionTopCharaGroupId;
        }
        
        /// <summary>
        /// SELECT `id`,`mission_top_chara_group_id`,`ending_flag`,`chara_id`,`dress_id`,`bg_id`,`bg_sub_id` FROM `collect_raid_mission_top_chara`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidMissionTopChara()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`mission_top_chara_group_id`,`ending_flag`,`chara_id`,`dress_id`,`bg_id`,`bg_sub_id` FROM `collect_raid_mission_top_chara`;");
        }
        
        // SQL statements for collect_raid/collect_raid_staging_mini_chara
        
        /// <summary>
        /// SELECT `staging_mini_chara_group_id`,`staging_type`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidStagingMiniChara()
        {
            if (_selectQuery_masterCollectRaidStagingMiniChara == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidStagingMiniChara = connection.PreparedQuery("SELECT `staging_mini_chara_group_id`,`staging_type`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidStagingMiniChara;
        }
        
        /// <summary>
        /// SELECT `id`,`staging_type`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara` WHERE `staging_mini_chara_group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidStagingMiniChara_StagingMiniCharaGroupId()
        {
            if (_indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId = connection.PreparedQuery("SELECT `id`,`staging_type`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara` WHERE `staging_mini_chara_group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara` WHERE `staging_mini_chara_group_id`=? AND `staging_type`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidStagingMiniChara_StagingMiniCharaGroupId_StagingType()
        {
            if (_indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType = connection.PreparedQuery("SELECT `id`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara` WHERE `staging_mini_chara_group_id`=? AND `staging_type`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidStagingMiniChara_stagingMiniCharaGroupId_stagingType;
        }
        
        /// <summary>
        /// SELECT `id`,`staging_mini_chara_group_id`,`staging_type`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidStagingMiniChara()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`staging_mini_chara_group_id`,`staging_type`,`chara_id`,`dress_id`,`mini_motion_id`,`chara_angle`,`chara_id_2`,`dress_id_2`,`mini_motion_id_2`,`chara_angle_2`,`chara_id_3`,`dress_id_3`,`mini_motion_id_3`,`chara_angle_3`,`chara_id_4`,`dress_id_4`,`mini_motion_id_4`,`chara_angle_4`,`rate` FROM `collect_raid_staging_mini_chara`;");
        }
        
        // SQL statements for collect_raid/collect_raid_story
        
        /// <summary>
        /// SELECT `collect_raid_id`,`story_condition_type`,`unlock_condition`,`episode_index_id`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidStory()
        {
            if (_selectQuery_masterCollectRaidStory == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidStory = connection.PreparedQuery("SELECT `collect_raid_id`,`story_condition_type`,`unlock_condition`,`episode_index_id`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidStory;
        }
        
        /// <summary>
        /// SELECT `id`,`story_condition_type`,`unlock_condition`,`episode_index_id`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story` WHERE `collect_raid_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidStory_CollectRaidId()
        {
            if (_indexedSelectQuery_collectRaidStory_collectRaidId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidStory_collectRaidId = connection.PreparedQuery("SELECT `id`,`story_condition_type`,`unlock_condition`,`episode_index_id`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story` WHERE `collect_raid_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidStory_collectRaidId;
        }
        
        /// <summary>
        /// SELECT `id`,`story_condition_type`,`unlock_condition`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story` WHERE `collect_raid_id`=? AND `episode_index_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidStory_CollectRaidId_EpisodeIndexId()
        {
            if (_indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId = connection.PreparedQuery("SELECT `id`,`story_condition_type`,`unlock_condition`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story` WHERE `collect_raid_id`=? AND `episode_index_id`=?;");
            }
        
            return _indexedSelectQuery_collectRaidStory_collectRaidId_episodeIndexId;
        }
        
        /// <summary>
        /// SELECT `id`,`collect_raid_id`,`story_condition_type`,`unlock_condition`,`episode_index_id`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidStory()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`collect_raid_id`,`story_condition_type`,`unlock_condition`,`episode_index_id`,`story_type_1`,`story_id_1`,`story_type_2`,`story_id_2`,`story_type_3`,`story_id_3` FROM `collect_raid_story`;");
        }
        
        // SQL statements for collect_raid/collect_raid_mission
        
        /// <summary>
        /// SELECT `collect_raid_master_id`,`mission_data_id` FROM `collect_raid_mission` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidMission()
        {
            if (_selectQuery_masterCollectRaidMission == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidMission = connection.PreparedQuery("SELECT `collect_raid_master_id`,`mission_data_id` FROM `collect_raid_mission` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidMission;
        }
        
        /// <summary>
        /// SELECT `id`,`collect_raid_master_id` FROM `collect_raid_mission` WHERE `mission_data_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidMission_MissionDataId()
        {
            if (_indexedSelectQuery_collectRaidMission_missionDataId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidMission_missionDataId = connection.PreparedQuery("SELECT `id`,`collect_raid_master_id` FROM `collect_raid_mission` WHERE `mission_data_id`=?;");
            }
        
            return _indexedSelectQuery_collectRaidMission_missionDataId;
        }
        
        /// <summary>
        /// SELECT `id`,`collect_raid_master_id`,`mission_data_id` FROM `collect_raid_mission`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidMission()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`collect_raid_master_id`,`mission_data_id` FROM `collect_raid_mission`;");
        }
        
        // SQL statements for collect_raid/collect_raid_segment_cutt
        
        /// <summary>
        /// SELECT `cut_id`,`cut_sub_id` FROM `collect_raid_segment_cutt` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidSegmentCutt()
        {
            if (_selectQuery_masterCollectRaidSegmentCutt == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidSegmentCutt = connection.PreparedQuery("SELECT `cut_id`,`cut_sub_id` FROM `collect_raid_segment_cutt` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidSegmentCutt;
        }
        
        /// <summary>
        /// SELECT `id`,`cut_id`,`cut_sub_id` FROM `collect_raid_segment_cutt`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidSegmentCutt()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`cut_id`,`cut_sub_id` FROM `collect_raid_segment_cutt`;");
        }
        
        // SQL statements for collect_raid/collect_raid_top_data
        
        /// <summary>
        /// SELECT `collect_raid_id`,`cut_id`,`cut_sub_id`,`bgm_cue_name`,`bgm_cuesheet_name`,`env_cue_name`,`env_cuesheet_name`,`segment_cutt_id`,`condition_type`,`condition_value` FROM `collect_raid_top_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidTopData()
        {
            if (_selectQuery_masterCollectRaidTopData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidTopData = connection.PreparedQuery("SELECT `collect_raid_id`,`cut_id`,`cut_sub_id`,`bgm_cue_name`,`bgm_cuesheet_name`,`env_cue_name`,`env_cuesheet_name`,`segment_cutt_id`,`condition_type`,`condition_value` FROM `collect_raid_top_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidTopData;
        }
        
        /// <summary>
        /// SELECT `id`,`cut_id`,`cut_sub_id`,`bgm_cue_name`,`bgm_cuesheet_name`,`env_cue_name`,`env_cuesheet_name`,`segment_cutt_id`,`condition_type`,`condition_value` FROM `collect_raid_top_data` WHERE `collect_raid_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidTopData_CollectRaidId()
        {
            if (_indexedSelectQuery_collectRaidTopData_collectRaidId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidTopData_collectRaidId = connection.PreparedQuery("SELECT `id`,`cut_id`,`cut_sub_id`,`bgm_cue_name`,`bgm_cuesheet_name`,`env_cue_name`,`env_cuesheet_name`,`segment_cutt_id`,`condition_type`,`condition_value` FROM `collect_raid_top_data` WHERE `collect_raid_id`=?;");
            }
        
            return _indexedSelectQuery_collectRaidTopData_collectRaidId;
        }
        
        /// <summary>
        /// SELECT `id`,`collect_raid_id`,`cut_id`,`cut_sub_id`,`bgm_cue_name`,`bgm_cuesheet_name`,`env_cue_name`,`env_cuesheet_name`,`segment_cutt_id`,`condition_type`,`condition_value` FROM `collect_raid_top_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidTopData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`collect_raid_id`,`cut_id`,`cut_sub_id`,`bgm_cue_name`,`bgm_cuesheet_name`,`env_cue_name`,`env_cuesheet_name`,`segment_cutt_id`,`condition_type`,`condition_value` FROM `collect_raid_top_data`;");
        }
        
        // SQL statements for collect_raid/collect_raid_dress_color
        
        /// <summary>
        /// SELECT `collect_raid_id`,`chara_id`,`dress_id`,`color_id` FROM `collect_raid_dress_color` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_CollectRaidDressColor()
        {
            if (_selectQuery_masterCollectRaidDressColor == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterCollectRaidDressColor = connection.PreparedQuery("SELECT `collect_raid_id`,`chara_id`,`dress_id`,`color_id` FROM `collect_raid_dress_color` WHERE `id`=?;");
            }
        
            return _selectQuery_masterCollectRaidDressColor;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`dress_id`,`color_id` FROM `collect_raid_dress_color` WHERE `collect_raid_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_CollectRaidDressColor_CollectRaidId()
        {
            if (_indexedSelectQuery_collectRaidDressColor_collectRaidId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_collectRaidDressColor_collectRaidId = connection.PreparedQuery("SELECT `id`,`chara_id`,`dress_id`,`color_id` FROM `collect_raid_dress_color` WHERE `collect_raid_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_collectRaidDressColor_collectRaidId;
        }
        
        /// <summary>
        /// SELECT `id`,`collect_raid_id`,`chara_id`,`dress_id`,`color_id` FROM `collect_raid_dress_color`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_CollectRaidDressColor()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`collect_raid_id`,`chara_id`,`dress_id`,`color_id` FROM `collect_raid_dress_color`;");
        }
    }
}