// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: mini_game/crane_game_arm_swing
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCraneGameArmSwing : AbstractMasterData
    {
        public const string TABLE_NAME = "crane_game_arm_swing";

        MasterMiniGameDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CraneGameArmSwing> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CraneGameArmSwing> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCraneGameArmSwing");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCraneGameArmSwing(MasterMiniGameDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CraneGameArmSwing>();
            _db = db;
        }


        public CraneGameArmSwing Get(int resultType)
        {
            int key = (int)resultType;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCraneGameArmSwing");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(resultType);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCraneGameArmSwing", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private CraneGameArmSwing _SelectOne(int resultType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CraneGameArmSwing();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CraneGameArmSwing");
                return null;
            }

            // SELECT `odds_1`,`odds_2`,`odds_3` FROM `crane_game_arm_swing` WHERE `result_type`=?;
            if (!query.BindInt(1, resultType)) { return null; }

            CraneGameArmSwing orm = null;

            if (query.Step())
            {
                int odds1 = (int)query.GetInt(0);
                int odds2 = (int)query.GetInt(1);
                int odds3 = (int)query.GetInt(2);

                orm = new CraneGameArmSwing(resultType, odds1, odds2, odds3);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", resultType));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CraneGameArmSwing()) {
                while (query.Step()) {
                    int resultType = (int)query.GetInt(0);
                    int odds1      = (int)query.GetInt(1);
                    int odds2      = (int)query.GetInt(2);
                    int odds3      = (int)query.GetInt(3);

                    int key = (int)resultType;
                    CraneGameArmSwing orm = new CraneGameArmSwing(resultType, odds1, odds2, odds3);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class CraneGameArmSwing
        {
            /// <summary> (CSV column: result_type) </summary>
            public readonly int ResultType;
            /// <summary> (CSV column: odds_1) </summary>
            public readonly int Odds1;
            /// <summary> (CSV column: odds_2) </summary>
            public readonly int Odds2;
            /// <summary> (CSV column: odds_3) </summary>
            public readonly int Odds3;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CraneGameArmSwing(int resultType = 0, int odds1 = 0, int odds2 = 0, int odds3 = 0)
            {
                this.ResultType = resultType;
                this.Odds1      = odds1;
                this.Odds2      = odds2;
                this.Odds3      = odds3;
            }
        }
    }
}
