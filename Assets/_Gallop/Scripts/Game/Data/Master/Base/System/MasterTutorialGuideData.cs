// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: system/tutorial_guide_data
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTutorialGuideData : AbstractMasterData
    {
        public const string TABLE_NAME = "tutorial_guide_data";

        MasterSystemDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;


        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTutorialGuideData(MasterSystemDatabase db) : base(db)
        {
            _db = db;
        }


        public TutorialGuideData Get(int id)
        {
            int key = (int)id;


            TutorialGuideData orm = null;
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTutorialGuideData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTutorialGuideData", key));
                        }
 
                    }
                }
            }

            return orm;
        }

        private TutorialGuideData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TutorialGuideData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TutorialGuideData");
                return null;
            }

            // SELECT `group_id`,`page_index`,`image_index` FROM `tutorial_guide_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TutorialGuideData orm = null;

            if (query.Step())
            {
                int groupId    = (int)query.GetInt(0);
                int pageIndex  = (int)query.GetInt(1);
                int imageIndex = (int)query.GetInt(2);

                orm = new TutorialGuideData(id, groupId, pageIndex, imageIndex);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TutorialGuideData GetWithGroupIdOrderByPageIndexAsc(int groupId)
        {
            TutorialGuideData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdOrderByPageIndexAsc(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                }
            }

            return orm;
        }

        private TutorialGuideData _SelectWithGroupIdOrderByPageIndexAsc(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TutorialGuideData_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TutorialGuideData");
                return null;
            }

            // SELECT `id`,`page_index`,`image_index` FROM `tutorial_guide_data` WHERE `group_id`=? ORDER BY `page_index` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            TutorialGuideData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdOrderByPageIndexAsc(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<TutorialGuideData> GetListWithGroupIdOrderByPageIndexAsc(int groupId)
        {
            int key = (int)groupId;
   
            return _ListSelectWithGroupIdOrderByPageIndexAsc(groupId);
      
        }

        public List<TutorialGuideData> MaybeListWithGroupIdOrderByPageIndexAsc(int groupId)
        {
            List<TutorialGuideData> list = GetListWithGroupIdOrderByPageIndexAsc(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<TutorialGuideData> _ListSelectWithGroupIdOrderByPageIndexAsc(int groupId)
        {
            List<TutorialGuideData> _list = new List<TutorialGuideData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TutorialGuideData_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TutorialGuideData");
                return null;
            }

            // SELECT `id`,`page_index`,`image_index` FROM `tutorial_guide_data` WHERE `group_id`=? ORDER BY `page_index` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                TutorialGuideData orm = _CreateOrmByQueryResultWithGroupIdOrderByPageIndexAsc(query, groupId);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TutorialGuideData _CreateOrmByQueryResultWithGroupIdOrderByPageIndexAsc(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id         = (int)query.GetInt(0);
            int pageIndex  = (int)query.GetInt(1);
            int imageIndex = (int)query.GetInt(2);

            return new TutorialGuideData(id, groupId, pageIndex, imageIndex);
        }

        public TutorialGuideData GetWithGroupIdAndPageIndex(int groupId, int pageIndex)
        {
            TutorialGuideData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdAndPageIndex(groupId, pageIndex);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", groupId, pageIndex));
                }
            }

            return orm;
        }

        private TutorialGuideData _SelectWithGroupIdAndPageIndex(int groupId, int pageIndex)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TutorialGuideData_GroupId_PageIndex();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TutorialGuideData");
                return null;
            }

            // SELECT `id`,`image_index` FROM `tutorial_guide_data` WHERE `group_id`=? AND `page_index`=?;
            if (!query.BindInt(1, groupId))   { return null; }
            if (!query.BindInt(2, pageIndex)) { return null; }

            TutorialGuideData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdAndPageIndex(query, groupId, pageIndex);
            }

            query.Reset();

            return orm;
        }

        private TutorialGuideData _CreateOrmByQueryResultWithGroupIdAndPageIndex(LibNative.Sqlite3.PreparedQuery query, int groupId, int pageIndex)
        {
            int id         = (int)query.GetInt(0);
            int imageIndex = (int)query.GetInt(1);

            return new TutorialGuideData(id, groupId, pageIndex, imageIndex);
        }

        public override void Unload()
        {
        }


        public List<TutorialGuideData> GetListAllEntries()
        {
            List<TutorialGuideData> _list = new List<TutorialGuideData>();
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TutorialGuideData()) {
                while (query.Step()) {
                    int id         = (int)query.GetInt(0);
                    int groupId    = (int)query.GetInt(1);
                    int pageIndex  = (int)query.GetInt(2);
                    int imageIndex = (int)query.GetInt(3);

                    TutorialGuideData orm = new TutorialGuideData(id, groupId, pageIndex, imageIndex);

                    _list.Add(orm);
                }
            }
            return _list;
        }

        public sealed partial class TutorialGuideData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: page_index) </summary>
            public readonly int PageIndex;
            /// <summary> (CSV column: image_index) </summary>
            public readonly int ImageIndex;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TutorialGuideData(int id = 0, int groupId = 0, int pageIndex = 0, int imageIndex = 0)
            {
                this.Id         = id;
                this.GroupId    = groupId;
                this.PageIndex  = pageIndex;
                this.ImageIndex = imageIndex;
            }
        }
    }
}
