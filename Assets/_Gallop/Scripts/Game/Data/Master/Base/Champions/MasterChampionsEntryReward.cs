// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: champions/champions_entry_reward
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:champions_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterChampionsEntryReward : AbstractMasterData
    {
        public const string TABLE_NAME = "champions_entry_reward";

        MasterChampionsDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<ulong> _notFounds = null;

        // cache dictionary
        private Dictionary<ulong, ChampionsEntryReward> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<ChampionsEntryReward>> _dictionaryWithChampionsId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<ulong, ChampionsEntryReward> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterChampionsEntryReward");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterChampionsEntryReward(MasterChampionsDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<ulong, ChampionsEntryReward>();
            _dictionaryWithChampionsId = new Dictionary<int, List<ChampionsEntryReward>>();
            _db = db;
        }


        public ulong GetKey(int championsId, int itemId)
        {
            return ((uint)unchecked((ulong)((int)championsId))) | ((((ulong)unchecked((ulong)((int)itemId)))) << 32);
        }

        public ChampionsEntryReward Get(ulong key)
        {
            int championsId = (int)unchecked((int)((key >> 0) & 0xFFFFFFFF));
            int itemId      = (int)unchecked((int)((key >> 32) & 0xFFFFFFFF));

            return Get(championsId, itemId);
        }

        public ChampionsEntryReward Get(int championsId, int itemId)
        {
            ulong key = ((uint)unchecked((ulong)((int)championsId))) | ((((ulong)unchecked((ulong)((int)itemId)))) << 32);

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterChampionsEntryReward");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(championsId, itemId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<ulong>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterChampionsEntryReward", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private ChampionsEntryReward _SelectOne(int championsId, int itemId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_ChampionsEntryReward();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChampionsEntryReward");
                return null;
            }

            // SELECT `item_category`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? AND `item_id`=?;
            if (!query.BindInt(1, championsId)) { return null; }
            if (!query.BindInt(2, itemId))      { return null; }

            ChampionsEntryReward orm = null;

            if (query.Step())
            {
                int itemCategory = (int)query.GetInt(0);
                int itemNum      = (int)query.GetInt(1);

                orm = new ChampionsEntryReward(championsId, itemCategory, itemId, itemNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0} {1}", championsId, itemId));
            }

            query.Reset();

            return orm;
        }

        public ChampionsEntryReward GetWithChampionsIdOrderByItemIdAsc(int championsId)
        {
            ChampionsEntryReward orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithChampionsIdOrderByItemIdAsc(championsId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", championsId));
                } else {
                    ulong key = ((uint)unchecked((ulong)((int)orm.ChampionsId))) | ((((ulong)unchecked((ulong)((int)orm.ItemId)))) << 32);

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private ChampionsEntryReward _SelectWithChampionsIdOrderByItemIdAsc(int championsId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChampionsEntryReward_ChampionsId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for ChampionsEntryReward");
                return null;
            }

            // SELECT `item_category`,`item_id`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? ORDER BY `item_id` ASC;
            if (!query.BindInt(1, championsId)) { return null; }

            ChampionsEntryReward orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithChampionsIdOrderByItemIdAsc(query, championsId);
            }

            query.Reset();

            return orm;
        }

        public List<ChampionsEntryReward> GetListWithChampionsIdOrderByItemIdAsc(int championsId)
        {
            int key = (int)championsId;
            
            if (!_dictionaryWithChampionsId.ContainsKey(key)) {
                _dictionaryWithChampionsId.Add(key, _ListSelectWithChampionsIdOrderByItemIdAsc(championsId));
            }

            return _dictionaryWithChampionsId[key];
      
        }

        public List<ChampionsEntryReward> MaybeListWithChampionsIdOrderByItemIdAsc(int championsId)
        {
            List<ChampionsEntryReward> list = GetListWithChampionsIdOrderByItemIdAsc(championsId);
            return list.Count > 0 ? list : null;
        }

        private List<ChampionsEntryReward> _ListSelectWithChampionsIdOrderByItemIdAsc(int championsId)
        {
            List<ChampionsEntryReward> _list = new List<ChampionsEntryReward>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_ChampionsEntryReward_ChampionsId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for ChampionsEntryReward");
                return null;
            }

            // SELECT `item_category`,`item_id`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? ORDER BY `item_id` ASC;
            if (!query.BindInt(1, championsId)) { return null; }

            while (query.Step()) {
                ChampionsEntryReward orm = _CreateOrmByQueryResultWithChampionsIdOrderByItemIdAsc(query, championsId);
                ulong key = ((uint)unchecked((ulong)((int)orm.ChampionsId))) | ((((ulong)unchecked((ulong)((int)orm.ItemId)))) << 32);

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private ChampionsEntryReward _CreateOrmByQueryResultWithChampionsIdOrderByItemIdAsc(LibNative.Sqlite3.PreparedQuery query, int championsId)
        {
            int itemCategory = (int)query.GetInt(0);
            int itemId       = (int)query.GetInt(1);
            int itemNum      = (int)query.GetInt(2);

            return new ChampionsEntryReward(championsId, itemCategory, itemId, itemNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithChampionsId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_ChampionsEntryReward()) {
                while (query.Step()) {
                    int championsId  = (int)query.GetInt(0);
                    int itemCategory = (int)query.GetInt(1);
                    int itemId       = (int)query.GetInt(2);
                    int itemNum      = (int)query.GetInt(3);

                    ulong key = ((uint)unchecked((ulong)((int)championsId))) | ((((ulong)unchecked((ulong)((int)itemId)))) << 32);
                    ChampionsEntryReward orm = new ChampionsEntryReward(championsId, itemCategory, itemId, itemNum);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class ChampionsEntryReward
        {
            /// <summary> (CSV column: champions_id) </summary>
            public readonly int ChampionsId;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: item_num) </summary>
            public readonly int ItemNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public ChampionsEntryReward(int championsId = 0, int itemCategory = 0, int itemId = 0, int itemNum = 0)
            {
                this.ChampionsId  = championsId;
                this.ItemCategory = itemCategory;
                this.ItemId       = itemId;
                this.ItemNum      = itemNum;
            }
        }
    }
}
