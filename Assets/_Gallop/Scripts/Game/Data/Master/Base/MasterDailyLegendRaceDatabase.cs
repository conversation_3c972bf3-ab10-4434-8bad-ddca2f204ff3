// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: daily_legend_race
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterDailyLegendRaceDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterDailyLegendRace masterDailyLegendRace { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterDailyLegendRace = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_dailyLegendRace_groupId        = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_dailyLegendRace_raceInstanceId = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterDailyLegendRaceDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterDailyLegendRace = new MasterDailyLegendRace(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet daily_legend_race database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterDailyLegendRace != null) { _selectQuery_masterDailyLegendRace.Dispose(); _selectQuery_masterDailyLegendRace = null; }
            if (_indexedSelectQuery_dailyLegendRace_groupId != null) { _indexedSelectQuery_dailyLegendRace_groupId.Dispose(); _indexedSelectQuery_dailyLegendRace_groupId = null; }
            if (_indexedSelectQuery_dailyLegendRace_raceInstanceId != null) { _indexedSelectQuery_dailyLegendRace_raceInstanceId.Dispose(); _indexedSelectQuery_dailyLegendRace_raceInstanceId = null; }
            if (this.masterDailyLegendRace != null) { this.masterDailyLegendRace.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for daily_legend_race/daily_legend_race
        
        /// <summary>
        /// SELECT `group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_DailyLegendRace()
        {
            if (_selectQuery_masterDailyLegendRace == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterDailyLegendRace = connection.PreparedQuery("SELECT `group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race` WHERE `id`=?;");
            }
        
            return _selectQuery_masterDailyLegendRace;
        }
        
        /// <summary>
        /// SELECT `id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race` WHERE `group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_DailyLegendRace_GroupId()
        {
            if (_indexedSelectQuery_dailyLegendRace_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_dailyLegendRace_groupId = connection.PreparedQuery("SELECT `id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race` WHERE `group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_dailyLegendRace_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_DailyLegendRace_RaceInstanceId()
        {
            if (_indexedSelectQuery_dailyLegendRace_raceInstanceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_dailyLegendRace_raceInstanceId = connection.PreparedQuery("SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_dailyLegendRace_raceInstanceId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_DailyLegendRace()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`start_date` FROM `daily_legend_race`;");
        }
    }
}