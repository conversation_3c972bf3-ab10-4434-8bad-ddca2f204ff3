// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: gacha/gacha_top_bg
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:gacha_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterGachaTopBg : AbstractMasterData
    {
        public const string TABLE_NAME = "gacha_top_bg";

        MasterGachaDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, GachaTopBg> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<GachaTopBg>> _dictionaryWithGachaId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterGachaTopBg(MasterGachaDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, GachaTopBg>();
            _dictionaryWithGachaId = new Dictionary<int, List<GachaTopBg>>();
            _db = db;
        }


        public GachaTopBg Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterGachaTopBg");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterGachaTopBg", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private GachaTopBg _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_GachaTopBg();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for GachaTopBg");
                return null;
            }

            // SELECT `gacha_id`,`logo_size_x`,`logo_size_y` FROM `gacha_top_bg` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            GachaTopBg orm = null;

            if (query.Step())
            {
                int gachaId   = (int)query.GetInt(0);
                int logoSizeX = (int)query.GetInt(1);
                int logoSizeY = (int)query.GetInt(2);

                orm = new GachaTopBg(id, gachaId, logoSizeX, logoSizeY);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public GachaTopBg GetWithGachaId(int gachaId)
        {
            GachaTopBg orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGachaId(gachaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", gachaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private GachaTopBg _SelectWithGachaId(int gachaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_GachaTopBg_GachaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for GachaTopBg");
                return null;
            }

            // SELECT `id`,`logo_size_x`,`logo_size_y` FROM `gacha_top_bg` WHERE `gacha_id`=?;
            if (!query.BindInt(1, gachaId)) { return null; }

            GachaTopBg orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGachaId(query, gachaId);
            }

            query.Reset();

            return orm;
        }

        public List<GachaTopBg> GetListWithGachaId(int gachaId)
        {
            int key = (int)gachaId;
            
            if (!_dictionaryWithGachaId.ContainsKey(key)) {
                _dictionaryWithGachaId.Add(key, _ListSelectWithGachaId(gachaId));
            }

            return _dictionaryWithGachaId[key];
      
        }

        public List<GachaTopBg> MaybeListWithGachaId(int gachaId)
        {
            List<GachaTopBg> list = GetListWithGachaId(gachaId);
            return list.Count > 0 ? list : null;
        }

        private List<GachaTopBg> _ListSelectWithGachaId(int gachaId)
        {
            List<GachaTopBg> _list = new List<GachaTopBg>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_GachaTopBg_GachaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for GachaTopBg");
                return null;
            }

            // SELECT `id`,`logo_size_x`,`logo_size_y` FROM `gacha_top_bg` WHERE `gacha_id`=?;
            if (!query.BindInt(1, gachaId)) { return null; }

            while (query.Step()) {
                GachaTopBg orm = _CreateOrmByQueryResultWithGachaId(query, gachaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private GachaTopBg _CreateOrmByQueryResultWithGachaId(LibNative.Sqlite3.PreparedQuery query, int gachaId)
        {
            int id        = (int)query.GetInt(0);
            int logoSizeX = (int)query.GetInt(1);
            int logoSizeY = (int)query.GetInt(2);

            return new GachaTopBg(id, gachaId, logoSizeX, logoSizeY);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGachaId.Clear();
        }



        public sealed partial class GachaTopBg
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: gacha_id) </summary>
            public readonly int GachaId;
            /// <summary> (CSV column: logo_size_x) </summary>
            public readonly int LogoSizeX;
            /// <summary> (CSV column: logo_size_y) </summary>
            public readonly int LogoSizeY;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public GachaTopBg(int id = 0, int gachaId = 0, int logoSizeX = 0, int logoSizeY = 0)
            {
                this.Id        = id;
                this.GachaId   = gachaId;
                this.LogoSizeX = logoSizeX;
                this.LogoSizeY = logoSizeY;
            }
        }
    }
}
