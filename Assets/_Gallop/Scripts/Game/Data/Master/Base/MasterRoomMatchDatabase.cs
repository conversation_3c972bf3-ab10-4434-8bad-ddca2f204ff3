// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: room_match
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterRoomMatchDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterRoomMatchTrainingRank masterRoomMatchTrainingRank { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterRoomMatchTrainingRank = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_roomMatchTrainingRank_rank = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterRoomMatchDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterRoomMatchTrainingRank = new MasterRoomMatchTrainingRank(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet room_match database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterRoomMatchTrainingRank != null) { _selectQuery_masterRoomMatchTrainingRank.Dispose(); _selectQuery_masterRoomMatchTrainingRank = null; }
            if (_indexedSelectQuery_roomMatchTrainingRank_rank != null) { _indexedSelectQuery_roomMatchTrainingRank_rank.Dispose(); _indexedSelectQuery_roomMatchTrainingRank_rank = null; }
            if (this.masterRoomMatchTrainingRank != null) { this.masterRoomMatchTrainingRank.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for room_match/room_match_training_rank
        
        /// <summary>
        /// SELECT `rank`,`rule_line`,`start_date` FROM `room_match_training_rank` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_RoomMatchTrainingRank()
        {
            if (_selectQuery_masterRoomMatchTrainingRank == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterRoomMatchTrainingRank = connection.PreparedQuery("SELECT `rank`,`rule_line`,`start_date` FROM `room_match_training_rank` WHERE `id`=?;");
            }
        
            return _selectQuery_masterRoomMatchTrainingRank;
        }
        
        /// <summary>
        /// SELECT `id`,`rule_line`,`start_date` FROM `room_match_training_rank` WHERE `rank`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_RoomMatchTrainingRank_Rank()
        {
            if (_indexedSelectQuery_roomMatchTrainingRank_rank == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_roomMatchTrainingRank_rank = connection.PreparedQuery("SELECT `id`,`rule_line`,`start_date` FROM `room_match_training_rank` WHERE `rank`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_roomMatchTrainingRank_rank;
        }
        
        /// <summary>
        /// SELECT `id`,`rank`,`rule_line`,`start_date` FROM `room_match_training_rank`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_RoomMatchTrainingRank()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`rank`,`rule_line`,`start_date` FROM `room_match_training_rank`;");
        }
    }
}