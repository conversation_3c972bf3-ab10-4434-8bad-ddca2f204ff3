// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/chara_dress_color_set_default
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:chara_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCharaDressColorSetDefault : AbstractMasterData
    {
        public const string TABLE_NAME = "chara_dress_color_set_default";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CharaDressColorSetDefault> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<CharaDressColorSetDefault>> _dictionaryWithCharaId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCharaDressColorSetDefault(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CharaDressColorSetDefault>();
            _dictionaryWithCharaId = new Dictionary<int, List<CharaDressColorSetDefault>>();
            _db = db;
        }


        public CharaDressColorSetDefault Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCharaDressColorSetDefault");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCharaDressColorSetDefault", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private CharaDressColorSetDefault _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CharaDressColorSetDefault();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDressColorSetDefault");
                return null;
            }

            // SELECT `chara_id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CharaDressColorSetDefault orm = null;

            if (query.Step())
            {
                int charaId    = (int)query.GetInt(0);
                int colorSetId = (int)query.GetInt(1);

                orm = new CharaDressColorSetDefault(id, charaId, colorSetId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public CharaDressColorSetDefault GetWithCharaId(int charaId)
        {
            CharaDressColorSetDefault orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaId(charaId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private CharaDressColorSetDefault _SelectWithCharaId(int charaId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaDressColorSetDefault_CharaId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDressColorSetDefault");
                return null;
            }

            // SELECT `id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            CharaDressColorSetDefault orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
            }

            query.Reset();

            return orm;
        }

        public List<CharaDressColorSetDefault> GetListWithCharaId(int charaId)
        {
            int key = (int)charaId;
            
            if (!_dictionaryWithCharaId.ContainsKey(key)) {
                _dictionaryWithCharaId.Add(key, _ListSelectWithCharaId(charaId));
            }

            return _dictionaryWithCharaId[key];
      
        }

        public List<CharaDressColorSetDefault> MaybeListWithCharaId(int charaId)
        {
            List<CharaDressColorSetDefault> list = GetListWithCharaId(charaId);
            return list.Count > 0 ? list : null;
        }

        private List<CharaDressColorSetDefault> _ListSelectWithCharaId(int charaId)
        {
            List<CharaDressColorSetDefault> _list = new List<CharaDressColorSetDefault>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_CharaDressColorSetDefault_CharaId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for CharaDressColorSetDefault");
                return null;
            }

            // SELECT `id`,`color_set_id` FROM `chara_dress_color_set_default` WHERE `chara_id`=?;
            if (!query.BindInt(1, charaId)) { return null; }

            while (query.Step()) {
                CharaDressColorSetDefault orm = _CreateOrmByQueryResultWithCharaId(query, charaId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private CharaDressColorSetDefault _CreateOrmByQueryResultWithCharaId(LibNative.Sqlite3.PreparedQuery query, int charaId)
        {
            int id         = (int)query.GetInt(0);
            int colorSetId = (int)query.GetInt(1);

            return new CharaDressColorSetDefault(id, charaId, colorSetId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharaId.Clear();
        }



        public sealed partial class CharaDressColorSetDefault
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: color_set_id) </summary>
            public readonly int ColorSetId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CharaDressColorSetDefault(int id = 0, int charaId = 0, int colorSetId = 0)
            {
                this.Id         = id;
                this.CharaId    = charaId;
                this.ColorSetId = colorSetId;
            }
        }
    }
}
