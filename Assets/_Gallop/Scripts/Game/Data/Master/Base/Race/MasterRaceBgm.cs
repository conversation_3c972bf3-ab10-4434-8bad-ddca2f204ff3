// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_bgm
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:race_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceBgm : AbstractMasterData
    {
        public const string TABLE_NAME = "race_bgm";

        MasterRaceDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;


        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceBgm(MasterRaceDatabase db) : base(db)
        {
            _db = db;
        }


        public RaceBgm Get(int id)
        {
            int key = (int)id;


            RaceBgm orm = null;
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceBgm");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceBgm", key));
                        }
 
                    }
                }
            }

            return orm;
        }

        private RaceBgm _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceBgm();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgm");
                return null;
            }

            // SELECT `race_type`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceBgm orm = null;

            if (query.Step())
            {
                int raceType                       = (int)query.GetInt(0);
                int raceInstanceId                 = (int)query.GetInt(1);
                int grade                          = (int)query.GetInt(2);
                int singleModeRouteRaceId          = (int)query.GetInt(3);
                int singleModeProgramId            = (int)query.GetInt(4);
                int difficulty                     = (int)query.GetInt(5);
                int conditionType                  = (int)query.GetInt(6);
                string paddockBgmCueName           = query.GetText(7);
                string paddockBgmCuesheetName      = query.GetText(8);
                string entrytableBgmCueName        = query.GetText(9);
                string entrytableBgmCuesheetName   = query.GetText(10);
                int firstBgmPattern                = (int)query.GetInt(11);
                int secondBgmPattern               = (int)query.GetInt(12);
                string resultCutinBgmCueName1      = query.GetText(13);
                string resultCutinBgmCuesheetName1 = query.GetText(14);
                string resultCutinBgmCueName2      = query.GetText(15);
                string resultCutinBgmCuesheetName2 = query.GetText(16);
                string resultCutinBgmCueName3      = query.GetText(17);
                string resultCutinBgmCuesheetName3 = query.GetText(18);
                string resultListBgmCueName1       = query.GetText(19);
                string resultListBgmCuesheetName1  = query.GetText(20);
                string resultListBgmCueName2       = query.GetText(21);
                string resultListBgmCuesheetName2  = query.GetText(22);
                string resultListBgmCueName3       = query.GetText(23);
                string resultListBgmCuesheetName3  = query.GetText(24);

                orm = new RaceBgm(id, raceType, raceInstanceId, grade, singleModeRouteRaceId, singleModeProgramId, difficulty, conditionType, paddockBgmCueName, paddockBgmCuesheetName, entrytableBgmCueName, entrytableBgmCuesheetName, firstBgmPattern, secondBgmPattern, resultCutinBgmCueName1, resultCutinBgmCuesheetName1, resultCutinBgmCueName2, resultCutinBgmCuesheetName2, resultCutinBgmCueName3, resultCutinBgmCuesheetName3, resultListBgmCueName1, resultListBgmCuesheetName1, resultListBgmCueName2, resultListBgmCuesheetName2, resultListBgmCueName3, resultListBgmCuesheetName3);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceBgm GetWithRaceTypeOrderByIdAsc(int raceType)
        {
            RaceBgm orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceTypeOrderByIdAsc(raceType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceType));
                }
            }

            return orm;
        }

        private RaceBgm _SelectWithRaceTypeOrderByIdAsc(int raceType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgm_RaceType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgm");
                return null;
            }

            // SELECT `id`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `race_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceType)) { return null; }

            RaceBgm orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceTypeOrderByIdAsc(query, raceType);
            }

            query.Reset();

            return orm;
        }

        public List<RaceBgm> GetListWithRaceTypeOrderByIdAsc(int raceType)
        {
            int key = (int)raceType;
   
            return _ListSelectWithRaceTypeOrderByIdAsc(raceType);
      
        }

        public List<RaceBgm> MaybeListWithRaceTypeOrderByIdAsc(int raceType)
        {
            List<RaceBgm> list = GetListWithRaceTypeOrderByIdAsc(raceType);
            return list.Count > 0 ? list : null;
        }

        private List<RaceBgm> _ListSelectWithRaceTypeOrderByIdAsc(int raceType)
        {
            List<RaceBgm> _list = new List<RaceBgm>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceBgm_RaceType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceBgm");
                return null;
            }

            // SELECT `id`,`race_instance_id`,`grade`,`single_mode_route_race_id`,`single_mode_program_id`,`difficulty`,`condition_type`,`paddock_bgm_cue_name`,`paddock_bgm_cuesheet_name`,`entrytable_bgm_cue_name`,`entrytable_bgm_cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern`,`result_cutin_bgm_cue_name_1`,`result_cutin_bgm_cuesheet_name_1`,`result_cutin_bgm_cue_name_2`,`result_cutin_bgm_cuesheet_name_2`,`result_cutin_bgm_cue_name_3`,`result_cutin_bgm_cuesheet_name_3`,`result_list_bgm_cue_name_1`,`result_list_bgm_cuesheet_name_1`,`result_list_bgm_cue_name_2`,`result_list_bgm_cuesheet_name_2`,`result_list_bgm_cue_name_3`,`result_list_bgm_cuesheet_name_3` FROM `race_bgm` WHERE `race_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceType)) { return null; }

            while (query.Step()) {
                RaceBgm orm = _CreateOrmByQueryResultWithRaceTypeOrderByIdAsc(query, raceType);

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceBgm _CreateOrmByQueryResultWithRaceTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceType)
        {
            int id                             = (int)query.GetInt(0);
            int raceInstanceId                 = (int)query.GetInt(1);
            int grade                          = (int)query.GetInt(2);
            int singleModeRouteRaceId          = (int)query.GetInt(3);
            int singleModeProgramId            = (int)query.GetInt(4);
            int difficulty                     = (int)query.GetInt(5);
            int conditionType                  = (int)query.GetInt(6);
            string paddockBgmCueName           = query.GetText(7);
            string paddockBgmCuesheetName      = query.GetText(8);
            string entrytableBgmCueName        = query.GetText(9);
            string entrytableBgmCuesheetName   = query.GetText(10);
            int firstBgmPattern                = (int)query.GetInt(11);
            int secondBgmPattern               = (int)query.GetInt(12);
            string resultCutinBgmCueName1      = query.GetText(13);
            string resultCutinBgmCuesheetName1 = query.GetText(14);
            string resultCutinBgmCueName2      = query.GetText(15);
            string resultCutinBgmCuesheetName2 = query.GetText(16);
            string resultCutinBgmCueName3      = query.GetText(17);
            string resultCutinBgmCuesheetName3 = query.GetText(18);
            string resultListBgmCueName1       = query.GetText(19);
            string resultListBgmCuesheetName1  = query.GetText(20);
            string resultListBgmCueName2       = query.GetText(21);
            string resultListBgmCuesheetName2  = query.GetText(22);
            string resultListBgmCueName3       = query.GetText(23);
            string resultListBgmCuesheetName3  = query.GetText(24);

            return new RaceBgm(id, raceType, raceInstanceId, grade, singleModeRouteRaceId, singleModeProgramId, difficulty, conditionType, paddockBgmCueName, paddockBgmCuesheetName, entrytableBgmCueName, entrytableBgmCuesheetName, firstBgmPattern, secondBgmPattern, resultCutinBgmCueName1, resultCutinBgmCuesheetName1, resultCutinBgmCueName2, resultCutinBgmCuesheetName2, resultCutinBgmCueName3, resultCutinBgmCuesheetName3, resultListBgmCueName1, resultListBgmCuesheetName1, resultListBgmCueName2, resultListBgmCuesheetName2, resultListBgmCueName3, resultListBgmCuesheetName3);
        }

        public override void Unload()
        {
        }


        public List<RaceBgm> GetListAllEntries()
        {
            List<RaceBgm> _list = new List<RaceBgm>();
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceBgm()) {
                while (query.Step()) {
                    int id                             = (int)query.GetInt(0);
                    int raceType                       = (int)query.GetInt(1);
                    int raceInstanceId                 = (int)query.GetInt(2);
                    int grade                          = (int)query.GetInt(3);
                    int singleModeRouteRaceId          = (int)query.GetInt(4);
                    int singleModeProgramId            = (int)query.GetInt(5);
                    int difficulty                     = (int)query.GetInt(6);
                    int conditionType                  = (int)query.GetInt(7);
                    string paddockBgmCueName           = query.GetText(8);
                    string paddockBgmCuesheetName      = query.GetText(9);
                    string entrytableBgmCueName        = query.GetText(10);
                    string entrytableBgmCuesheetName   = query.GetText(11);
                    int firstBgmPattern                = (int)query.GetInt(12);
                    int secondBgmPattern               = (int)query.GetInt(13);
                    string resultCutinBgmCueName1      = query.GetText(14);
                    string resultCutinBgmCuesheetName1 = query.GetText(15);
                    string resultCutinBgmCueName2      = query.GetText(16);
                    string resultCutinBgmCuesheetName2 = query.GetText(17);
                    string resultCutinBgmCueName3      = query.GetText(18);
                    string resultCutinBgmCuesheetName3 = query.GetText(19);
                    string resultListBgmCueName1       = query.GetText(20);
                    string resultListBgmCuesheetName1  = query.GetText(21);
                    string resultListBgmCueName2       = query.GetText(22);
                    string resultListBgmCuesheetName2  = query.GetText(23);
                    string resultListBgmCueName3       = query.GetText(24);
                    string resultListBgmCuesheetName3  = query.GetText(25);

                    RaceBgm orm = new RaceBgm(id, raceType, raceInstanceId, grade, singleModeRouteRaceId, singleModeProgramId, difficulty, conditionType, paddockBgmCueName, paddockBgmCuesheetName, entrytableBgmCueName, entrytableBgmCuesheetName, firstBgmPattern, secondBgmPattern, resultCutinBgmCueName1, resultCutinBgmCuesheetName1, resultCutinBgmCueName2, resultCutinBgmCuesheetName2, resultCutinBgmCueName3, resultCutinBgmCuesheetName3, resultListBgmCueName1, resultListBgmCuesheetName1, resultListBgmCueName2, resultListBgmCuesheetName2, resultListBgmCueName3, resultListBgmCuesheetName3);

                    _list.Add(orm);
                }
            }
            return _list;
        }

        public sealed partial class RaceBgm
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: race_type) </summary>
            public readonly int RaceType;
            /// <summary> (CSV column: race_instance_id) </summary>
            public readonly int RaceInstanceId;
            /// <summary> (CSV column: grade) </summary>
            public readonly int Grade;
            /// <summary> (CSV column: single_mode_route_race_id) </summary>
            public readonly int SingleModeRouteRaceId;
            /// <summary> (CSV column: single_mode_program_id) </summary>
            public readonly int SingleModeProgramId;
            /// <summary> (CSV column: difficulty) </summary>
            public readonly int Difficulty;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: paddock_bgm_cue_name) </summary>
            public readonly string PaddockBgmCueName;
            /// <summary> (CSV column: paddock_bgm_cuesheet_name) </summary>
            public readonly string PaddockBgmCuesheetName;
            /// <summary> (CSV column: entrytable_bgm_cue_name) </summary>
            public readonly string EntrytableBgmCueName;
            /// <summary> (CSV column: entrytable_bgm_cuesheet_name) </summary>
            public readonly string EntrytableBgmCuesheetName;
            /// <summary> (CSV column: first_bgm_pattern) </summary>
            public readonly int FirstBgmPattern;
            /// <summary> (CSV column: second_bgm_pattern) </summary>
            public readonly int SecondBgmPattern;
            /// <summary> (CSV column: result_cutin_bgm_cue_name_1) </summary>
            public readonly string ResultCutinBgmCueName1;
            /// <summary> (CSV column: result_cutin_bgm_cuesheet_name_1) </summary>
            public readonly string ResultCutinBgmCuesheetName1;
            /// <summary> (CSV column: result_cutin_bgm_cue_name_2) </summary>
            public readonly string ResultCutinBgmCueName2;
            /// <summary> (CSV column: result_cutin_bgm_cuesheet_name_2) </summary>
            public readonly string ResultCutinBgmCuesheetName2;
            /// <summary> (CSV column: result_cutin_bgm_cue_name_3) </summary>
            public readonly string ResultCutinBgmCueName3;
            /// <summary> (CSV column: result_cutin_bgm_cuesheet_name_3) </summary>
            public readonly string ResultCutinBgmCuesheetName3;
            /// <summary> (CSV column: result_list_bgm_cue_name_1) </summary>
            public readonly string ResultListBgmCueName1;
            /// <summary> (CSV column: result_list_bgm_cuesheet_name_1) </summary>
            public readonly string ResultListBgmCuesheetName1;
            /// <summary> (CSV column: result_list_bgm_cue_name_2) </summary>
            public readonly string ResultListBgmCueName2;
            /// <summary> (CSV column: result_list_bgm_cuesheet_name_2) </summary>
            public readonly string ResultListBgmCuesheetName2;
            /// <summary> (CSV column: result_list_bgm_cue_name_3) </summary>
            public readonly string ResultListBgmCueName3;
            /// <summary> (CSV column: result_list_bgm_cuesheet_name_3) </summary>
            public readonly string ResultListBgmCuesheetName3;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceBgm(int id = 0, int raceType = 0, int raceInstanceId = 0, int grade = 0, int singleModeRouteRaceId = 0, int singleModeProgramId = 0, int difficulty = 0, int conditionType = 0, string paddockBgmCueName = "", string paddockBgmCuesheetName = "", string entrytableBgmCueName = "", string entrytableBgmCuesheetName = "", int firstBgmPattern = 0, int secondBgmPattern = 0, string resultCutinBgmCueName1 = "", string resultCutinBgmCuesheetName1 = "", string resultCutinBgmCueName2 = "", string resultCutinBgmCuesheetName2 = "", string resultCutinBgmCueName3 = "", string resultCutinBgmCuesheetName3 = "", string resultListBgmCueName1 = "", string resultListBgmCuesheetName1 = "", string resultListBgmCueName2 = "", string resultListBgmCuesheetName2 = "", string resultListBgmCueName3 = "", string resultListBgmCuesheetName3 = "")
            {
                this.Id                          = id;
                this.RaceType                    = raceType;
                this.RaceInstanceId              = raceInstanceId;
                this.Grade                       = grade;
                this.SingleModeRouteRaceId       = singleModeRouteRaceId;
                this.SingleModeProgramId         = singleModeProgramId;
                this.Difficulty                  = difficulty;
                this.ConditionType               = conditionType;
                this.PaddockBgmCueName           = paddockBgmCueName;
                this.PaddockBgmCuesheetName      = paddockBgmCuesheetName;
                this.EntrytableBgmCueName        = entrytableBgmCueName;
                this.EntrytableBgmCuesheetName   = entrytableBgmCuesheetName;
                this.FirstBgmPattern             = firstBgmPattern;
                this.SecondBgmPattern            = secondBgmPattern;
                this.ResultCutinBgmCueName1      = resultCutinBgmCueName1;
                this.ResultCutinBgmCuesheetName1 = resultCutinBgmCuesheetName1;
                this.ResultCutinBgmCueName2      = resultCutinBgmCueName2;
                this.ResultCutinBgmCuesheetName2 = resultCutinBgmCuesheetName2;
                this.ResultCutinBgmCueName3      = resultCutinBgmCueName3;
                this.ResultCutinBgmCuesheetName3 = resultCutinBgmCuesheetName3;
                this.ResultListBgmCueName1       = resultListBgmCueName1;
                this.ResultListBgmCuesheetName1  = resultListBgmCuesheetName1;
                this.ResultListBgmCueName2       = resultListBgmCueName2;
                this.ResultListBgmCuesheetName2  = resultListBgmCuesheetName2;
                this.ResultListBgmCueName3       = resultListBgmCueName3;
                this.ResultListBgmCuesheetName3  = resultListBgmCuesheetName3;
            }
        }
    }
}
