// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_jikkyo_base
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceJikkyoBase : AbstractMasterData
    {
        public const string TABLE_NAME = "race_jikkyo_base";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceJikkyoBase> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceJikkyoBase> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceJikkyoBase");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceJikkyoBase(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceJikkyoBase>();
            _db = db;
        }


        public RaceJikkyoBase Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceJikkyoBase");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceJikkyoBase", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoBase _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceJikkyoBase();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoBase");
                return null;
            }

            // SELECT `mode`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceJikkyoBase orm = null;

            if (query.Step())
            {
                int mode                    = (int)query.GetInt(0);
                int subMode                 = (int)query.GetInt(1);
                int subModeJump             = (int)query.GetInt(2);
                int situation               = (int)query.GetInt(3);
                int subSituation            = (int)query.GetInt(4);
                int situationEnd            = (int)query.GetInt(5);
                int disableReentrySituation = (int)query.GetInt(6);
                int trigger0                = (int)query.GetInt(7);
                int trigger1                = (int)query.GetInt(8);
                int trigger2                = (int)query.GetInt(9);
                int trigger3                = (int)query.GetInt(10);
                int trigger4                = (int)query.GetInt(11);
                int trigger5                = (int)query.GetInt(12);
                int trigger6                = (int)query.GetInt(13);
                int trigger7                = (int)query.GetInt(14);
                int trigger8                = (int)query.GetInt(15);
                int trigger9                = (int)query.GetInt(16);
                int messageGroup            = (int)query.GetInt(17);
                int commentGroup            = (int)query.GetInt(18);
                int priority                = (int)query.GetInt(19);
                int per                     = (int)query.GetInt(20);
                int immediate               = (int)query.GetInt(21);
                int discard                 = (int)query.GetInt(22);
                int tension                 = (int)query.GetInt(23);
                int cameraId                = (int)query.GetInt(24);
                int cameraHorse1            = (int)query.GetInt(25);
                int cameraHorse2            = (int)query.GetInt(26);
                int isForcePlayEventCamera  = (int)query.GetInt(27);
                int disableReuse            = (int)query.GetInt(28);

                orm = new RaceJikkyoBase(id, mode, subMode, subModeJump, situation, subSituation, situationEnd, disableReentrySituation, trigger0, trigger1, trigger2, trigger3, trigger4, trigger5, trigger6, trigger7, trigger8, trigger9, messageGroup, commentGroup, priority, per, immediate, discard, tension, cameraId, cameraHorse1, cameraHorse2, isForcePlayEventCamera, disableReuse);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceJikkyoBase GetWithMode(int mode)
        {
            RaceJikkyoBase orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithMode(mode);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", mode));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoBase _SelectWithMode(int mode)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoBase_Mode();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoBase");
                return null;
            }

            // SELECT `id`,`sub_mode`,`sub_mode_jump`,`situation`,`sub_situation`,`situation_end`,`disable_reentry_situation`,`trigger0`,`trigger1`,`trigger2`,`trigger3`,`trigger4`,`trigger5`,`trigger6`,`trigger7`,`trigger8`,`trigger9`,`message_group`,`comment_group`,`priority`,`per`,`immediate`,`discard`,`tension`,`camera_id`,`camera_horse1`,`camera_horse2`,`is_force_play_event_camera`,`disable_reuse` FROM `race_jikkyo_base` WHERE `mode`=?;
            if (!query.BindInt(1, mode)) { return null; }

            RaceJikkyoBase orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithMode(query, mode);
            }

            query.Reset();

            return orm;
        }

        private RaceJikkyoBase _CreateOrmByQueryResultWithMode(LibNative.Sqlite3.PreparedQuery query, int mode)
        {
            int id                      = (int)query.GetInt(0);
            int subMode                 = (int)query.GetInt(1);
            int subModeJump             = (int)query.GetInt(2);
            int situation               = (int)query.GetInt(3);
            int subSituation            = (int)query.GetInt(4);
            int situationEnd            = (int)query.GetInt(5);
            int disableReentrySituation = (int)query.GetInt(6);
            int trigger0                = (int)query.GetInt(7);
            int trigger1                = (int)query.GetInt(8);
            int trigger2                = (int)query.GetInt(9);
            int trigger3                = (int)query.GetInt(10);
            int trigger4                = (int)query.GetInt(11);
            int trigger5                = (int)query.GetInt(12);
            int trigger6                = (int)query.GetInt(13);
            int trigger7                = (int)query.GetInt(14);
            int trigger8                = (int)query.GetInt(15);
            int trigger9                = (int)query.GetInt(16);
            int messageGroup            = (int)query.GetInt(17);
            int commentGroup            = (int)query.GetInt(18);
            int priority                = (int)query.GetInt(19);
            int per                     = (int)query.GetInt(20);
            int immediate               = (int)query.GetInt(21);
            int discard                 = (int)query.GetInt(22);
            int tension                 = (int)query.GetInt(23);
            int cameraId                = (int)query.GetInt(24);
            int cameraHorse1            = (int)query.GetInt(25);
            int cameraHorse2            = (int)query.GetInt(26);
            int isForcePlayEventCamera  = (int)query.GetInt(27);
            int disableReuse            = (int)query.GetInt(28);

            return new RaceJikkyoBase(id, mode, subMode, subModeJump, situation, subSituation, situationEnd, disableReentrySituation, trigger0, trigger1, trigger2, trigger3, trigger4, trigger5, trigger6, trigger7, trigger8, trigger9, messageGroup, commentGroup, priority, per, immediate, discard, tension, cameraId, cameraHorse1, cameraHorse2, isForcePlayEventCamera, disableReuse);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceJikkyoBase()) {
                while (query.Step()) {
                    int id                      = (int)query.GetInt(0);
                    int mode                    = (int)query.GetInt(1);
                    int subMode                 = (int)query.GetInt(2);
                    int subModeJump             = (int)query.GetInt(3);
                    int situation               = (int)query.GetInt(4);
                    int subSituation            = (int)query.GetInt(5);
                    int situationEnd            = (int)query.GetInt(6);
                    int disableReentrySituation = (int)query.GetInt(7);
                    int trigger0                = (int)query.GetInt(8);
                    int trigger1                = (int)query.GetInt(9);
                    int trigger2                = (int)query.GetInt(10);
                    int trigger3                = (int)query.GetInt(11);
                    int trigger4                = (int)query.GetInt(12);
                    int trigger5                = (int)query.GetInt(13);
                    int trigger6                = (int)query.GetInt(14);
                    int trigger7                = (int)query.GetInt(15);
                    int trigger8                = (int)query.GetInt(16);
                    int trigger9                = (int)query.GetInt(17);
                    int messageGroup            = (int)query.GetInt(18);
                    int commentGroup            = (int)query.GetInt(19);
                    int priority                = (int)query.GetInt(20);
                    int per                     = (int)query.GetInt(21);
                    int immediate               = (int)query.GetInt(22);
                    int discard                 = (int)query.GetInt(23);
                    int tension                 = (int)query.GetInt(24);
                    int cameraId                = (int)query.GetInt(25);
                    int cameraHorse1            = (int)query.GetInt(26);
                    int cameraHorse2            = (int)query.GetInt(27);
                    int isForcePlayEventCamera  = (int)query.GetInt(28);
                    int disableReuse            = (int)query.GetInt(29);

                    int key = (int)id;
                    RaceJikkyoBase orm = new RaceJikkyoBase(id, mode, subMode, subModeJump, situation, subSituation, situationEnd, disableReentrySituation, trigger0, trigger1, trigger2, trigger3, trigger4, trigger5, trigger6, trigger7, trigger8, trigger9, messageGroup, commentGroup, priority, per, immediate, discard, tension, cameraId, cameraHorse1, cameraHorse2, isForcePlayEventCamera, disableReuse);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class RaceJikkyoBase
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: mode) </summary>
            public readonly int Mode;
            /// <summary> (CSV column: sub_mode) </summary>
            public readonly int SubMode;
            /// <summary> (CSV column: sub_mode_jump) </summary>
            public readonly int SubModeJump;
            /// <summary> (CSV column: situation) </summary>
            public readonly int Situation;
            /// <summary> (CSV column: sub_situation) </summary>
            public readonly int SubSituation;
            /// <summary> (CSV column: situation_end) </summary>
            public readonly int SituationEnd;
            /// <summary> (CSV column: disable_reentry_situation) </summary>
            public readonly int DisableReentrySituation;
            /// <summary> (CSV column: trigger0) </summary>
            public readonly int Trigger0;
            /// <summary> (CSV column: trigger1) </summary>
            public readonly int Trigger1;
            /// <summary> (CSV column: trigger2) </summary>
            public readonly int Trigger2;
            /// <summary> (CSV column: trigger3) </summary>
            public readonly int Trigger3;
            /// <summary> (CSV column: trigger4) </summary>
            public readonly int Trigger4;
            /// <summary> (CSV column: trigger5) </summary>
            public readonly int Trigger5;
            /// <summary> (CSV column: trigger6) </summary>
            public readonly int Trigger6;
            /// <summary> (CSV column: trigger7) </summary>
            public readonly int Trigger7;
            /// <summary> (CSV column: trigger8) </summary>
            public readonly int Trigger8;
            /// <summary> (CSV column: trigger9) </summary>
            public readonly int Trigger9;
            /// <summary> (CSV column: message_group) </summary>
            public readonly int MessageGroup;
            /// <summary> (CSV column: comment_group) </summary>
            public readonly int CommentGroup;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary> (CSV column: per) </summary>
            public readonly int Per;
            /// <summary> (CSV column: immediate) </summary>
            public readonly int Immediate;
            /// <summary> (CSV column: discard) </summary>
            public readonly int Discard;
            /// <summary> (CSV column: tension) </summary>
            public readonly int Tension;
            /// <summary> (CSV column: camera_id) </summary>
            public readonly int CameraId;
            /// <summary> (CSV column: camera_horse1) </summary>
            public readonly int CameraHorse1;
            /// <summary> (CSV column: camera_horse2) </summary>
            public readonly int CameraHorse2;
            /// <summary> (CSV column: is_force_play_event_camera) </summary>
            public readonly int IsForcePlayEventCamera;
            /// <summary> (CSV column: disable_reuse) </summary>
            public readonly int DisableReuse;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceJikkyoBase(int id = 0, int mode = 0, int subMode = 0, int subModeJump = 0, int situation = 0, int subSituation = 0, int situationEnd = 0, int disableReentrySituation = 0, int trigger0 = 0, int trigger1 = 0, int trigger2 = 0, int trigger3 = 0, int trigger4 = 0, int trigger5 = 0, int trigger6 = 0, int trigger7 = 0, int trigger8 = 0, int trigger9 = 0, int messageGroup = 0, int commentGroup = 0, int priority = 0, int per = 0, int immediate = 0, int discard = 0, int tension = 0, int cameraId = 0, int cameraHorse1 = 0, int cameraHorse2 = 0, int isForcePlayEventCamera = 0, int disableReuse = 0)
            {
                this.Id                      = id;
                this.Mode                    = mode;
                this.SubMode                 = subMode;
                this.SubModeJump             = subModeJump;
                this.Situation               = situation;
                this.SubSituation            = subSituation;
                this.SituationEnd            = situationEnd;
                this.DisableReentrySituation = disableReentrySituation;
                this.Trigger0                = trigger0;
                this.Trigger1                = trigger1;
                this.Trigger2                = trigger2;
                this.Trigger3                = trigger3;
                this.Trigger4                = trigger4;
                this.Trigger5                = trigger5;
                this.Trigger6                = trigger6;
                this.Trigger7                = trigger7;
                this.Trigger8                = trigger8;
                this.Trigger9                = trigger9;
                this.MessageGroup            = messageGroup;
                this.CommentGroup            = commentGroup;
                this.Priority                = priority;
                this.Per                     = per;
                this.Immediate               = immediate;
                this.Discard                 = discard;
                this.Tension                 = tension;
                this.CameraId                = cameraId;
                this.CameraHorse1            = cameraHorse1;
                this.CameraHorse2            = cameraHorse2;
                this.IsForcePlayEventCamera  = isForcePlayEventCamera;
                this.DisableReuse            = disableReuse;
            }
        }
    }
}
