// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_course_set
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:race_track_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceCourseSet : AbstractMasterData
    {
        public const string TABLE_NAME = "race_course_set";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceCourseSet> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceCourseSet>> _dictionaryWithRaceTrackId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceCourseSet> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceCourseSet");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceCourseSet(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceCourseSet>();
            _dictionaryWithRaceTrackId = new Dictionary<int, List<RaceCourseSet>>();
            _db = db;
        }


        public RaceCourseSet Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceCourseSet");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceCourseSet", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private RaceCourseSet _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceCourseSet();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCourseSet");
                return null;
            }

            // SELECT `race_track_id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceCourseSet orm = null;

            if (query.Step())
            {
                int raceTrackId              = (int)query.GetInt(0);
                int distance                 = (int)query.GetInt(1);
                int ground                   = (int)query.GetInt(2);
                int inout                    = (int)query.GetInt(3);
                int turn                     = (int)query.GetInt(4);
                bool runOutside              = (query.GetInt(5) == 1);
                int exCamera                 = (int)query.GetInt(6);
                int fenceSet                 = (int)query.GetInt(7);
                int floatLaneMax             = (int)query.GetInt(8);
                int courseSetStatusId        = (int)query.GetInt(9);
                int finishTimeMin            = (int)query.GetInt(10);
                int finishTimeMinRandomRange = (int)query.GetInt(11);
                int finishTimeMax            = (int)query.GetInt(12);
                int finishTimeMaxRandomRange = (int)query.GetInt(13);

                orm = new RaceCourseSet(id, raceTrackId, distance, ground, inout, turn, runOutside, exCamera, fenceSet, floatLaneMax, courseSetStatusId, finishTimeMin, finishTimeMinRandomRange, finishTimeMax, finishTimeMaxRandomRange);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceCourseSet GetWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            RaceCourseSet orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRaceTrackIdOrderByIdAsc(raceTrackId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", raceTrackId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceCourseSet _SelectWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceCourseSet_RaceTrackId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCourseSet");
                return null;
            }

            // SELECT `id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `race_track_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceTrackId)) { return null; }

            RaceCourseSet orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRaceTrackIdOrderByIdAsc(query, raceTrackId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceCourseSet> GetListWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            int key = (int)raceTrackId;
            
            if (!_dictionaryWithRaceTrackId.ContainsKey(key)) {
                _dictionaryWithRaceTrackId.Add(key, _ListSelectWithRaceTrackIdOrderByIdAsc(raceTrackId));
            }

            return _dictionaryWithRaceTrackId[key];
      
        }

        public List<RaceCourseSet> MaybeListWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            List<RaceCourseSet> list = GetListWithRaceTrackIdOrderByIdAsc(raceTrackId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceCourseSet> _ListSelectWithRaceTrackIdOrderByIdAsc(int raceTrackId)
        {
            List<RaceCourseSet> _list = new List<RaceCourseSet>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceCourseSet_RaceTrackId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceCourseSet");
                return null;
            }

            // SELECT `id`,`distance`,`ground`,`inout`,`turn`,`run_outside`,`ex_camera`,`fence_set`,`float_lane_max`,`course_set_status_id`,`finish_time_min`,`finish_time_min_random_range`,`finish_time_max`,`finish_time_max_random_range` FROM `race_course_set` WHERE `race_track_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, raceTrackId)) { return null; }

            while (query.Step()) {
                RaceCourseSet orm = _CreateOrmByQueryResultWithRaceTrackIdOrderByIdAsc(query, raceTrackId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceCourseSet _CreateOrmByQueryResultWithRaceTrackIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int raceTrackId)
        {
            int id                       = (int)query.GetInt(0);
            int distance                 = (int)query.GetInt(1);
            int ground                   = (int)query.GetInt(2);
            int inout                    = (int)query.GetInt(3);
            int turn                     = (int)query.GetInt(4);
            bool runOutside              = (query.GetInt(5) == 1);
            int exCamera                 = (int)query.GetInt(6);
            int fenceSet                 = (int)query.GetInt(7);
            int floatLaneMax             = (int)query.GetInt(8);
            int courseSetStatusId        = (int)query.GetInt(9);
            int finishTimeMin            = (int)query.GetInt(10);
            int finishTimeMinRandomRange = (int)query.GetInt(11);
            int finishTimeMax            = (int)query.GetInt(12);
            int finishTimeMaxRandomRange = (int)query.GetInt(13);

            return new RaceCourseSet(id, raceTrackId, distance, ground, inout, turn, runOutside, exCamera, fenceSet, floatLaneMax, courseSetStatusId, finishTimeMin, finishTimeMinRandomRange, finishTimeMax, finishTimeMaxRandomRange);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithRaceTrackId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceCourseSet()) {
                while (query.Step()) {
                    int id                       = (int)query.GetInt(0);
                    int raceTrackId              = (int)query.GetInt(1);
                    int distance                 = (int)query.GetInt(2);
                    int ground                   = (int)query.GetInt(3);
                    int inout                    = (int)query.GetInt(4);
                    int turn                     = (int)query.GetInt(5);
                    bool runOutside              = (query.GetInt(6) == 1);
                    int exCamera                 = (int)query.GetInt(7);
                    int fenceSet                 = (int)query.GetInt(8);
                    int floatLaneMax             = (int)query.GetInt(9);
                    int courseSetStatusId        = (int)query.GetInt(10);
                    int finishTimeMin            = (int)query.GetInt(11);
                    int finishTimeMinRandomRange = (int)query.GetInt(12);
                    int finishTimeMax            = (int)query.GetInt(13);
                    int finishTimeMaxRandomRange = (int)query.GetInt(14);

                    int key = (int)id;
                    RaceCourseSet orm = new RaceCourseSet(id, raceTrackId, distance, ground, inout, turn, runOutside, exCamera, fenceSet, floatLaneMax, courseSetStatusId, finishTimeMin, finishTimeMinRandomRange, finishTimeMax, finishTimeMaxRandomRange);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class RaceCourseSet
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: race_track_id) </summary>
            public readonly int RaceTrackId;
            /// <summary> (CSV column: distance) </summary>
            public readonly int Distance;
            /// <summary> (CSV column: ground) </summary>
            public readonly int Ground;
            /// <summary> (CSV column: inout) </summary>
            public readonly int Inout;
            /// <summary> (CSV column: turn) </summary>
            public readonly int Turn;
            /// <summary> (CSV column: run_outside) </summary>
            public readonly bool RunOutside;
            /// <summary> (CSV column: ex_camera) </summary>
            public readonly int ExCamera;
            /// <summary> (CSV column: fence_set) </summary>
            public readonly int FenceSet;
            /// <summary> (CSV column: float_lane_max) </summary>
            public readonly int FloatLaneMax;
            /// <summary> (CSV column: course_set_status_id) </summary>
            public readonly int CourseSetStatusId;
            /// <summary> (CSV column: finish_time_min) </summary>
            public readonly int FinishTimeMin;
            /// <summary> (CSV column: finish_time_min_random_range) </summary>
            public readonly int FinishTimeMinRandomRange;
            /// <summary> (CSV column: finish_time_max) </summary>
            public readonly int FinishTimeMax;
            /// <summary> (CSV column: finish_time_max_random_range) </summary>
            public readonly int FinishTimeMaxRandomRange;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceCourseSet(int id = 0, int raceTrackId = 0, int distance = 0, int ground = 0, int inout = 0, int turn = 0, bool runOutside = false, int exCamera = 0, int fenceSet = 0, int floatLaneMax = 0, int courseSetStatusId = 0, int finishTimeMin = 0, int finishTimeMinRandomRange = 0, int finishTimeMax = 0, int finishTimeMaxRandomRange = 0)
            {
                this.Id                       = id;
                this.RaceTrackId              = raceTrackId;
                this.Distance                 = distance;
                this.Ground                   = ground;
                this.Inout                    = inout;
                this.Turn                     = turn;
                this.RunOutside               = runOutside;
                this.ExCamera                 = exCamera;
                this.FenceSet                 = fenceSet;
                this.FloatLaneMax             = floatLaneMax;
                this.CourseSetStatusId        = courseSetStatusId;
                this.FinishTimeMin            = finishTimeMin;
                this.FinishTimeMinRandomRange = finishTimeMinRandomRange;
                this.FinishTimeMax            = finishTimeMax;
                this.FinishTimeMaxRandomRange = finishTimeMaxRandomRange;
            }
        }
    }
}
