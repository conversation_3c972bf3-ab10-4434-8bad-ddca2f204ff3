// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: legend_race
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterLegendRaceDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterLegendRace masterLegendRace                           { get; private set; }
        public MasterLegendRaceBossNpc masterLegendRaceBossNpc             { get; private set; }
        public MasterLegendRaceNpc masterLegendRaceNpc                     { get; private set; }
        public MasterLegendRaceBilling masterLegendRaceBilling             { get; private set; }
        public MasterLegendRaceCuttCharaData masterLegendRaceCuttCharaData { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLegendRace              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLegendRaceBossNpc       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLegendRaceNpc           = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLegendRaceBilling       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterLegendRaceCuttCharaData = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_legendRace_groupId                     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_legendRace_raceInstanceId              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_legendRace_legendRaceBossNpcId         = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_legendRaceBossNpc_charaId              = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterLegendRaceDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterLegendRace              = new MasterLegendRace(this);
            this.masterLegendRaceBossNpc       = new MasterLegendRaceBossNpc(this);
            this.masterLegendRaceNpc           = new MasterLegendRaceNpc(this);
            this.masterLegendRaceBilling       = new MasterLegendRaceBilling(this);
            this.masterLegendRaceCuttCharaData = new MasterLegendRaceCuttCharaData(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet legend_race database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterLegendRace != null) { _selectQuery_masterLegendRace.Dispose(); _selectQuery_masterLegendRace = null; }
            if (_indexedSelectQuery_legendRace_groupId != null) { _indexedSelectQuery_legendRace_groupId.Dispose(); _indexedSelectQuery_legendRace_groupId = null; }
            if (_indexedSelectQuery_legendRace_raceInstanceId != null) { _indexedSelectQuery_legendRace_raceInstanceId.Dispose(); _indexedSelectQuery_legendRace_raceInstanceId = null; }
            if (_indexedSelectQuery_legendRace_legendRaceBossNpcId != null) { _indexedSelectQuery_legendRace_legendRaceBossNpcId.Dispose(); _indexedSelectQuery_legendRace_legendRaceBossNpcId = null; }
            if (_selectQuery_masterLegendRaceBossNpc != null) { _selectQuery_masterLegendRaceBossNpc.Dispose(); _selectQuery_masterLegendRaceBossNpc = null; }
            if (_indexedSelectQuery_legendRaceBossNpc_charaId != null) { _indexedSelectQuery_legendRaceBossNpc_charaId.Dispose(); _indexedSelectQuery_legendRaceBossNpc_charaId = null; }
            if (_selectQuery_masterLegendRaceNpc != null) { _selectQuery_masterLegendRaceNpc.Dispose(); _selectQuery_masterLegendRaceNpc = null; }
            if (_selectQuery_masterLegendRaceBilling != null) { _selectQuery_masterLegendRaceBilling.Dispose(); _selectQuery_masterLegendRaceBilling = null; }
            if (_selectQuery_masterLegendRaceCuttCharaData != null) { _selectQuery_masterLegendRaceCuttCharaData.Dispose(); _selectQuery_masterLegendRaceCuttCharaData = null; }
            if (_indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum != null) { _indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum.Dispose(); _indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum = null; }
            if (this.masterLegendRace != null) { this.masterLegendRace.Unload(); }
            if (this.masterLegendRaceBossNpc != null) { this.masterLegendRaceBossNpc.Unload(); }
            if (this.masterLegendRaceNpc != null) { this.masterLegendRaceNpc.Unload(); }
            if (this.masterLegendRaceBilling != null) { this.masterLegendRaceBilling.Unload(); }
            if (this.masterLegendRaceCuttCharaData != null) { this.masterLegendRaceCuttCharaData.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for legend_race/legend_race
        
        /// <summary>
        /// SELECT `group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LegendRace()
        {
            if (_selectQuery_masterLegendRace == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLegendRace = connection.PreparedQuery("SELECT `group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `id`=?;");
            }
        
            return _selectQuery_masterLegendRace;
        }
        
        /// <summary>
        /// SELECT `id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `group_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LegendRace_GroupId()
        {
            if (_indexedSelectQuery_legendRace_groupId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_legendRace_groupId = connection.PreparedQuery("SELECT `id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `group_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_legendRace_groupId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LegendRace_RaceInstanceId()
        {
            if (_indexedSelectQuery_legendRace_raceInstanceId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_legendRace_raceInstanceId = connection.PreparedQuery("SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `race_instance_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_legendRace_raceInstanceId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `legend_race_boss_npc_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LegendRace_LegendRaceBossNpcId()
        {
            if (_indexedSelectQuery_legendRace_legendRaceBossNpcId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_legendRace_legendRaceBossNpcId = connection.PreparedQuery("SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race` WHERE `legend_race_boss_npc_id`=?;");
            }
        
            return _indexedSelectQuery_legendRace_legendRaceBossNpcId;
        }
        
        /// <summary>
        /// SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LegendRace()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`group_id`,`legend_bg_id`,`legend_bg_sub_id`,`has_unique_bg_env`,`image_id`,`difficulty`,`race_instance_id`,`season`,`weather`,`ground`,`legend_race_boss_npc_id`,`cost_num`,`drop_reward_odds_id`,`victory_reward_odds_id`,`first_clear_item_category_1`,`first_clear_item_id_1`,`first_clear_item_num_1`,`first_clear_item_category_2`,`first_clear_item_id_2`,`first_clear_item_num_2`,`first_clear_item_category_3`,`first_clear_item_id_3`,`first_clear_item_num_3`,`pick_up_item_category_1`,`pick_up_item_id_1`,`pick_up_item_num_1`,`pick_up_item_category_2`,`pick_up_item_id_2`,`pick_up_item_num_2`,`pick_up_item_category_3`,`pick_up_item_id_3`,`pick_up_item_num_3`,`notice_date`,`start_date`,`end_date` FROM `legend_race`;");
        }
        
        // SQL statements for legend_race/legend_race_boss_npc
        
        /// <summary>
        /// SELECT `chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LegendRaceBossNpc()
        {
            if (_selectQuery_masterLegendRaceBossNpc == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLegendRaceBossNpc = connection.PreparedQuery("SELECT `chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc` WHERE `id`=?;");
            }
        
            return _selectQuery_masterLegendRaceBossNpc;
        }
        
        /// <summary>
        /// SELECT `id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc` WHERE `chara_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LegendRaceBossNpc_CharaId()
        {
            if (_indexedSelectQuery_legendRaceBossNpc_charaId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_legendRaceBossNpc_charaId = connection.PreparedQuery("SELECT `id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc` WHERE `chara_id`=?;");
            }
        
            return _indexedSelectQuery_legendRaceBossNpc_charaId;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LegendRaceBossNpc()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`chara_id`,`race_dress_id`,`nickname_id`,`card_rarity_data_id`,`post`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_boss_npc`;");
        }
        
        // SQL statements for legend_race/legend_race_npc
        
        /// <summary>
        /// SELECT `npc_group_id`,`chara_id`,`mob_id`,`race_dress_id`,`race_instance_id`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_npc` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LegendRaceNpc()
        {
            if (_selectQuery_masterLegendRaceNpc == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLegendRaceNpc = connection.PreparedQuery("SELECT `npc_group_id`,`chara_id`,`mob_id`,`race_dress_id`,`race_instance_id`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_npc` WHERE `id`=?;");
            }
        
            return _selectQuery_masterLegendRaceNpc;
        }
        
        /// <summary>
        /// SELECT `id`,`npc_group_id`,`chara_id`,`mob_id`,`race_dress_id`,`race_instance_id`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_npc`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LegendRaceNpc()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`npc_group_id`,`chara_id`,`mob_id`,`race_dress_id`,`race_instance_id`,`speed`,`stamina`,`pow`,`guts`,`wiz`,`proper_distance_short`,`proper_distance_mile`,`proper_distance_middle`,`proper_distance_long`,`proper_running_style_nige`,`proper_running_style_senko`,`proper_running_style_sashi`,`proper_running_style_oikomi`,`proper_ground_turf`,`proper_ground_dirt`,`skill_set_id` FROM `legend_race_npc`;");
        }
        
        // SQL statements for legend_race/legend_race_billing
        
        /// <summary>
        /// SELECT `frequency`,`pay_cost` FROM `legend_race_billing` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LegendRaceBilling()
        {
            if (_selectQuery_masterLegendRaceBilling == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLegendRaceBilling = connection.PreparedQuery("SELECT `frequency`,`pay_cost` FROM `legend_race_billing` WHERE `id`=?;");
            }
        
            return _selectQuery_masterLegendRaceBilling;
        }
        
        /// <summary>
        /// SELECT `id`,`frequency`,`pay_cost` FROM `legend_race_billing`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LegendRaceBilling()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`frequency`,`pay_cost` FROM `legend_race_billing`;");
        }
        
        // SQL statements for legend_race/legend_race_cutt_chara_data
        
        /// <summary>
        /// SELECT `sub_id`,`chara_num`,`chara_id`,`target_timeline`,`target_list_index` FROM `legend_race_cutt_chara_data` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_LegendRaceCuttCharaData()
        {
            if (_selectQuery_masterLegendRaceCuttCharaData == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterLegendRaceCuttCharaData = connection.PreparedQuery("SELECT `sub_id`,`chara_num`,`chara_id`,`target_timeline`,`target_list_index` FROM `legend_race_cutt_chara_data` WHERE `id`=?;");
            }
        
            return _selectQuery_masterLegendRaceCuttCharaData;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`target_timeline`,`target_list_index` FROM `legend_race_cutt_chara_data` WHERE `sub_id`=? AND `chara_num`=? ORDER BY `chara_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_LegendRaceCuttCharaData_SubId_CharaNum()
        {
            if (_indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum = connection.PreparedQuery("SELECT `id`,`chara_id`,`target_timeline`,`target_list_index` FROM `legend_race_cutt_chara_data` WHERE `sub_id`=? AND `chara_num`=? ORDER BY `chara_id` ASC;");
            }
        
            return _indexedSelectQuery_legendRaceCuttCharaData_subId_charaNum;
        }
        
        /// <summary>
        /// SELECT `id`,`sub_id`,`chara_num`,`chara_id`,`target_timeline`,`target_list_index` FROM `legend_race_cutt_chara_data`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_LegendRaceCuttCharaData()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`sub_id`,`chara_num`,`chara_id`,`target_timeline`,`target_list_index` FROM `legend_race_cutt_chara_data`;");
        }
    }
}