// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: cg/highlight_interpolate
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterHighlightInterpolate : AbstractMasterData
    {
        public const string TABLE_NAME = "highlight_interpolate";

        MasterCgDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, HighlightInterpolate> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterHighlightInterpolate(MasterCgDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, HighlightInterpolate>();
            _db = db;
        }


        public HighlightInterpolate Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterHighlightInterpolate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterHighlightInterpolate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private HighlightInterpolate _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_HighlightInterpolate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for HighlightInterpolate");
                return null;
            }

            // SELECT `in_time`,`out_time` FROM `highlight_interpolate` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            HighlightInterpolate orm = null;

            if (query.Step())
            {
                int inTime  = (int)query.GetInt(0);
                int outTime = (int)query.GetInt(1);

                orm = new HighlightInterpolate(id, inTime, outTime);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class HighlightInterpolate
        {
            /// <summary>
            /// “id”
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary>
            /// “”
            /// (CSV column: in_time)
            /// </summary>
            public readonly int InTime;
            /// <summary>
            /// “”
            /// (CSV column: out_time)
            /// </summary>
            public readonly int OutTime;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public HighlightInterpolate(int id = 0, int inTime = 0, int outTime = 0)
            {
                this.Id      = id;
                this.InTime  = inTime;
                this.OutTime = outTime;
            }
        }
    }
}
