// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: mission/mission_data
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:mission_type], [:item_category]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterMissionData : AbstractMasterData
    {
        public const string TABLE_NAME = "mission_data";

        MasterMissionDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, MissionData> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<MissionData>> _dictionaryWithMissionType = null;
        private Dictionary<int, List<MissionData>> _dictionaryWithItemCategory = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, MissionData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterMissionData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterMissionData(MasterMissionDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, MissionData>();
            _dictionaryWithMissionType = new Dictionary<int, List<MissionData>>();
            _dictionaryWithItemCategory = new Dictionary<int, List<MissionData>>();
            _db = db;
        }


        public MissionData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterMissionData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterMissionData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private MissionData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_MissionData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionData");
                return null;
            }

            // SELECT `mission_type`,`condition_type`,`condition_value_1`,`condition_value_2`,`condition_value_3`,`condition_value_4`,`condition_num`,`step_group_id`,`step_order`,`disp_order`,`item_category`,`item_id`,`item_num`,`event_id`,`date_check_flg`,`transition_type`,`start_date`,`end_date` FROM `mission_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            MissionData orm = null;

            if (query.Step())
            {
                int missionType     = (int)query.GetInt(0);
                int conditionType   = (int)query.GetInt(1);
                int conditionValue1 = (int)query.GetInt(2);
                int conditionValue2 = (int)query.GetInt(3);
                int conditionValue3 = (int)query.GetInt(4);
                int conditionValue4 = (int)query.GetInt(5);
                int conditionNum    = (int)query.GetInt(6);
                int stepGroupId     = (int)query.GetInt(7);
                int stepOrder       = (int)query.GetInt(8);
                int dispOrder       = (int)query.GetInt(9);
                int itemCategory    = (int)query.GetInt(10);
                int itemId          = (int)query.GetInt(11);
                int itemNum         = (int)query.GetInt(12);
                int eventId         = (int)query.GetInt(13);
                int dateCheckFlg    = (int)query.GetInt(14);
                int transitionType  = (int)query.GetInt(15);
                string startDate    = query.GetText(16);
                string endDate      = query.GetText(17);

                orm = new MissionData(id, missionType, conditionType, conditionValue1, conditionValue2, conditionValue3, conditionValue4, conditionNum, stepGroupId, stepOrder, dispOrder, itemCategory, itemId, itemNum, eventId, dateCheckFlg, transitionType, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public MissionData GetWithMissionTypeOrderByIdAsc(int missionType)
        {
            MissionData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithMissionTypeOrderByIdAsc(missionType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", missionType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private MissionData _SelectWithMissionTypeOrderByIdAsc(int missionType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_MissionData_MissionType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionData");
                return null;
            }

            // SELECT `id`,`condition_type`,`condition_value_1`,`condition_value_2`,`condition_value_3`,`condition_value_4`,`condition_num`,`step_group_id`,`step_order`,`disp_order`,`item_category`,`item_id`,`item_num`,`event_id`,`date_check_flg`,`transition_type`,`start_date`,`end_date` FROM `mission_data` WHERE `mission_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, missionType)) { return null; }

            MissionData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithMissionTypeOrderByIdAsc(query, missionType);
            }

            query.Reset();

            return orm;
        }

        public List<MissionData> GetListWithMissionTypeOrderByIdAsc(int missionType)
        {
            int key = (int)missionType;
            
            if (!_dictionaryWithMissionType.ContainsKey(key)) {
                _dictionaryWithMissionType.Add(key, _ListSelectWithMissionTypeOrderByIdAsc(missionType));
            }

            return _dictionaryWithMissionType[key];
      
        }

        public List<MissionData> MaybeListWithMissionTypeOrderByIdAsc(int missionType)
        {
            List<MissionData> list = GetListWithMissionTypeOrderByIdAsc(missionType);
            return list.Count > 0 ? list : null;
        }

        private List<MissionData> _ListSelectWithMissionTypeOrderByIdAsc(int missionType)
        {
            List<MissionData> _list = new List<MissionData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_MissionData_MissionType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionData");
                return null;
            }

            // SELECT `id`,`condition_type`,`condition_value_1`,`condition_value_2`,`condition_value_3`,`condition_value_4`,`condition_num`,`step_group_id`,`step_order`,`disp_order`,`item_category`,`item_id`,`item_num`,`event_id`,`date_check_flg`,`transition_type`,`start_date`,`end_date` FROM `mission_data` WHERE `mission_type`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, missionType)) { return null; }

            while (query.Step()) {
                MissionData orm = _CreateOrmByQueryResultWithMissionTypeOrderByIdAsc(query, missionType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private MissionData _CreateOrmByQueryResultWithMissionTypeOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int missionType)
        {
            int id              = (int)query.GetInt(0);
            int conditionType   = (int)query.GetInt(1);
            int conditionValue1 = (int)query.GetInt(2);
            int conditionValue2 = (int)query.GetInt(3);
            int conditionValue3 = (int)query.GetInt(4);
            int conditionValue4 = (int)query.GetInt(5);
            int conditionNum    = (int)query.GetInt(6);
            int stepGroupId     = (int)query.GetInt(7);
            int stepOrder       = (int)query.GetInt(8);
            int dispOrder       = (int)query.GetInt(9);
            int itemCategory    = (int)query.GetInt(10);
            int itemId          = (int)query.GetInt(11);
            int itemNum         = (int)query.GetInt(12);
            int eventId         = (int)query.GetInt(13);
            int dateCheckFlg    = (int)query.GetInt(14);
            int transitionType  = (int)query.GetInt(15);
            string startDate    = query.GetText(16);
            string endDate      = query.GetText(17);

            return new MissionData(id, missionType, conditionType, conditionValue1, conditionValue2, conditionValue3, conditionValue4, conditionNum, stepGroupId, stepOrder, dispOrder, itemCategory, itemId, itemNum, eventId, dateCheckFlg, transitionType, startDate, endDate);
        }

        public MissionData GetWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            MissionData orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithItemCategoryOrderByIdAsc(itemCategory);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", itemCategory));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private MissionData _SelectWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_MissionData_ItemCategory();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionData");
                return null;
            }

            // SELECT `id`,`mission_type`,`condition_type`,`condition_value_1`,`condition_value_2`,`condition_value_3`,`condition_value_4`,`condition_num`,`step_group_id`,`step_order`,`disp_order`,`item_id`,`item_num`,`event_id`,`date_check_flg`,`transition_type`,`start_date`,`end_date` FROM `mission_data` WHERE `item_category`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, itemCategory)) { return null; }

            MissionData orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithItemCategoryOrderByIdAsc(query, itemCategory);
            }

            query.Reset();

            return orm;
        }

        public List<MissionData> GetListWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            int key = (int)itemCategory;
            
            if (!_dictionaryWithItemCategory.ContainsKey(key)) {
                _dictionaryWithItemCategory.Add(key, _ListSelectWithItemCategoryOrderByIdAsc(itemCategory));
            }

            return _dictionaryWithItemCategory[key];
      
        }

        public List<MissionData> MaybeListWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            List<MissionData> list = GetListWithItemCategoryOrderByIdAsc(itemCategory);
            return list.Count > 0 ? list : null;
        }

        private List<MissionData> _ListSelectWithItemCategoryOrderByIdAsc(int itemCategory)
        {
            List<MissionData> _list = new List<MissionData>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_MissionData_ItemCategory();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionData");
                return null;
            }

            // SELECT `id`,`mission_type`,`condition_type`,`condition_value_1`,`condition_value_2`,`condition_value_3`,`condition_value_4`,`condition_num`,`step_group_id`,`step_order`,`disp_order`,`item_id`,`item_num`,`event_id`,`date_check_flg`,`transition_type`,`start_date`,`end_date` FROM `mission_data` WHERE `item_category`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, itemCategory)) { return null; }

            while (query.Step()) {
                MissionData orm = _CreateOrmByQueryResultWithItemCategoryOrderByIdAsc(query, itemCategory);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private MissionData _CreateOrmByQueryResultWithItemCategoryOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int itemCategory)
        {
            int id              = (int)query.GetInt(0);
            int missionType     = (int)query.GetInt(1);
            int conditionType   = (int)query.GetInt(2);
            int conditionValue1 = (int)query.GetInt(3);
            int conditionValue2 = (int)query.GetInt(4);
            int conditionValue3 = (int)query.GetInt(5);
            int conditionValue4 = (int)query.GetInt(6);
            int conditionNum    = (int)query.GetInt(7);
            int stepGroupId     = (int)query.GetInt(8);
            int stepOrder       = (int)query.GetInt(9);
            int dispOrder       = (int)query.GetInt(10);
            int itemId          = (int)query.GetInt(11);
            int itemNum         = (int)query.GetInt(12);
            int eventId         = (int)query.GetInt(13);
            int dateCheckFlg    = (int)query.GetInt(14);
            int transitionType  = (int)query.GetInt(15);
            string startDate    = query.GetText(16);
            string endDate      = query.GetText(17);

            return new MissionData(id, missionType, conditionType, conditionValue1, conditionValue2, conditionValue3, conditionValue4, conditionNum, stepGroupId, stepOrder, dispOrder, itemCategory, itemId, itemNum, eventId, dateCheckFlg, transitionType, startDate, endDate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithMissionType.Clear();
            _dictionaryWithItemCategory.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_MissionData()) {
                while (query.Step()) {
                    int id              = (int)query.GetInt(0);
                    int missionType     = (int)query.GetInt(1);
                    int conditionType   = (int)query.GetInt(2);
                    int conditionValue1 = (int)query.GetInt(3);
                    int conditionValue2 = (int)query.GetInt(4);
                    int conditionValue3 = (int)query.GetInt(5);
                    int conditionValue4 = (int)query.GetInt(6);
                    int conditionNum    = (int)query.GetInt(7);
                    int stepGroupId     = (int)query.GetInt(8);
                    int stepOrder       = (int)query.GetInt(9);
                    int dispOrder       = (int)query.GetInt(10);
                    int itemCategory    = (int)query.GetInt(11);
                    int itemId          = (int)query.GetInt(12);
                    int itemNum         = (int)query.GetInt(13);
                    int eventId         = (int)query.GetInt(14);
                    int dateCheckFlg    = (int)query.GetInt(15);
                    int transitionType  = (int)query.GetInt(16);
                    string startDate    = query.GetText(17);
                    string endDate      = query.GetText(18);

                    int key = (int)id;
                    MissionData orm = new MissionData(id, missionType, conditionType, conditionValue1, conditionValue2, conditionValue3, conditionValue4, conditionNum, stepGroupId, stepOrder, dispOrder, itemCategory, itemId, itemNum, eventId, dateCheckFlg, transitionType, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class MissionData
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: mission_type) </summary>
            public readonly int MissionType;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: condition_value_1) </summary>
            public readonly int ConditionValue1;
            /// <summary> (CSV column: condition_value_2) </summary>
            public readonly int ConditionValue2;
            /// <summary> (CSV column: condition_value_3) </summary>
            public readonly int ConditionValue3;
            /// <summary> (CSV column: condition_value_4) </summary>
            public readonly int ConditionValue4;
            /// <summary> (CSV column: condition_num) </summary>
            public readonly int ConditionNum;
            /// <summary> (CSV column: step_group_id) </summary>
            public readonly int StepGroupId;
            /// <summary> (CSV column: step_order) </summary>
            public readonly int StepOrder;
            /// <summary> (CSV column: disp_order) </summary>
            public readonly int DispOrder;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: item_num) </summary>
            public readonly int ItemNum;
            /// <summary> (CSV column: event_id) </summary>
            public readonly int EventId;
            /// <summary> (CSV column: date_check_flg) </summary>
            public readonly int DateCheckFlg;
            /// <summary> (CSV column: transition_type) </summary>
            public readonly int TransitionType;
            /// <summary> (CSV column: start_date) </summary>
            public readonly string StartDate;
            /// <summary> (CSV column: end_date) </summary>
            public readonly string EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public MissionData(int id = 0, int missionType = 0, int conditionType = 0, int conditionValue1 = 0, int conditionValue2 = 0, int conditionValue3 = 0, int conditionValue4 = 0, int conditionNum = 0, int stepGroupId = 0, int stepOrder = 0, int dispOrder = 0, int itemCategory = 0, int itemId = 0, int itemNum = 0, int eventId = 0, int dateCheckFlg = 0, int transitionType = 0, string startDate = "", string endDate = "")
            {
                this.Id              = id;
                this.MissionType     = missionType;
                this.ConditionType   = conditionType;
                this.ConditionValue1 = conditionValue1;
                this.ConditionValue2 = conditionValue2;
                this.ConditionValue3 = conditionValue3;
                this.ConditionValue4 = conditionValue4;
                this.ConditionNum    = conditionNum;
                this.StepGroupId     = stepGroupId;
                this.StepOrder       = stepOrder;
                this.DispOrder       = dispOrder;
                this.ItemCategory    = itemCategory;
                this.ItemId          = itemId;
                this.ItemNum         = itemNum;
                this.EventId         = eventId;
                this.DateCheckFlg    = dateCheckFlg;
                this.TransitionType  = transitionType;
                this.StartDate       = startDate;
                this.EndDate         = endDate;
            }
        }
    }
}
