// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: mission/mission_race_equate
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:input_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterMissionRaceEquate : AbstractMasterData
    {
        public const string TABLE_NAME = "mission_race_equate";

        MasterMissionDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, MissionRaceEquate> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<MissionRaceEquate>> _dictionaryWithInputId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterMissionRaceEquate(MasterMissionDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, MissionRaceEquate>();
            _dictionaryWithInputId = new Dictionary<int, List<MissionRaceEquate>>();
            _db = db;
        }


        public MissionRaceEquate Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterMissionRaceEquate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterMissionRaceEquate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private MissionRaceEquate _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_MissionRaceEquate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionRaceEquate");
                return null;
            }

            // SELECT `input_id`,`equate_id` FROM `mission_race_equate` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            MissionRaceEquate orm = null;

            if (query.Step())
            {
                int inputId  = (int)query.GetInt(0);
                int equateId = (int)query.GetInt(1);

                orm = new MissionRaceEquate(id, inputId, equateId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public MissionRaceEquate GetWithInputIdOrderByIdAsc(int inputId)
        {
            MissionRaceEquate orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithInputIdOrderByIdAsc(inputId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", inputId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private MissionRaceEquate _SelectWithInputIdOrderByIdAsc(int inputId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_MissionRaceEquate_InputId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionRaceEquate");
                return null;
            }

            // SELECT `id`,`equate_id` FROM `mission_race_equate` WHERE `input_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, inputId)) { return null; }

            MissionRaceEquate orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithInputIdOrderByIdAsc(query, inputId);
            }

            query.Reset();

            return orm;
        }

        public List<MissionRaceEquate> GetListWithInputIdOrderByIdAsc(int inputId)
        {
            int key = (int)inputId;
            
            if (!_dictionaryWithInputId.ContainsKey(key)) {
                _dictionaryWithInputId.Add(key, _ListSelectWithInputIdOrderByIdAsc(inputId));
            }

            return _dictionaryWithInputId[key];
      
        }

        public List<MissionRaceEquate> MaybeListWithInputIdOrderByIdAsc(int inputId)
        {
            List<MissionRaceEquate> list = GetListWithInputIdOrderByIdAsc(inputId);
            return list.Count > 0 ? list : null;
        }

        private List<MissionRaceEquate> _ListSelectWithInputIdOrderByIdAsc(int inputId)
        {
            List<MissionRaceEquate> _list = new List<MissionRaceEquate>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_MissionRaceEquate_InputId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for MissionRaceEquate");
                return null;
            }

            // SELECT `id`,`equate_id` FROM `mission_race_equate` WHERE `input_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, inputId)) { return null; }

            while (query.Step()) {
                MissionRaceEquate orm = _CreateOrmByQueryResultWithInputIdOrderByIdAsc(query, inputId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private MissionRaceEquate _CreateOrmByQueryResultWithInputIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int inputId)
        {
            int id       = (int)query.GetInt(0);
            int equateId = (int)query.GetInt(1);

            return new MissionRaceEquate(id, inputId, equateId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithInputId.Clear();
        }



        public sealed partial class MissionRaceEquate
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: input_id) </summary>
            public readonly int InputId;
            /// <summary> (CSV column: equate_id) </summary>
            public readonly int EquateId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public MissionRaceEquate(int id = 0, int inputId = 0, int equateId = 0)
            {
                this.Id       = id;
                this.InputId  = inputId;
                this.EquateId = equateId;
            }
        }
    }
}
