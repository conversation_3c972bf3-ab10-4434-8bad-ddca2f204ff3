// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: item/price_change
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterPriceChange : AbstractMasterData
    {
        public const string TABLE_NAME = "price_change";

        MasterItemDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, PriceChange> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<PriceChange>> _dictionaryWithGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, PriceChange> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterPriceChange");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterPriceChange(MasterItemDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, PriceChange>();
            _dictionaryWithGroupId = new Dictionary<int, List<PriceChange>>();
            _db = db;
        }


        public PriceChange Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterPriceChange");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterPriceChange", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private PriceChange _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_PriceChange();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for PriceChange");
                return null;
            }

            // SELECT `group_id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            PriceChange orm = null;

            if (query.Step())
            {
                int groupId    = (int)query.GetInt(0);
                int minNum     = (int)query.GetInt(1);
                int maxNum     = (int)query.GetInt(2);
                int payItemNum = (int)query.GetInt(3);

                orm = new PriceChange(id, groupId, minNum, maxNum, payItemNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public PriceChange GetWithGroupIdOrderByIdAsc(int groupId)
        {
            PriceChange orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupIdOrderByIdAsc(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private PriceChange _SelectWithGroupIdOrderByIdAsc(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_PriceChange_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for PriceChange");
                return null;
            }

            // SELECT `id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            PriceChange orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<PriceChange> GetListWithGroupIdOrderByIdAsc(int groupId)
        {
            int key = (int)groupId;
            
            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupIdOrderByIdAsc(groupId));
            }

            return _dictionaryWithGroupId[key];
      
        }

        public List<PriceChange> MaybeListWithGroupIdOrderByIdAsc(int groupId)
        {
            List<PriceChange> list = GetListWithGroupIdOrderByIdAsc(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<PriceChange> _ListSelectWithGroupIdOrderByIdAsc(int groupId)
        {
            List<PriceChange> _list = new List<PriceChange>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_PriceChange_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for PriceChange");
                return null;
            }

            // SELECT `id`,`min_num`,`max_num`,`pay_item_num` FROM `price_change` WHERE `group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                PriceChange orm = _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(query, groupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private PriceChange _CreateOrmByQueryResultWithGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id         = (int)query.GetInt(0);
            int minNum     = (int)query.GetInt(1);
            int maxNum     = (int)query.GetInt(2);
            int payItemNum = (int)query.GetInt(3);

            return new PriceChange(id, groupId, minNum, maxNum, payItemNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_PriceChange()) {
                while (query.Step()) {
                    int id         = (int)query.GetInt(0);
                    int groupId    = (int)query.GetInt(1);
                    int minNum     = (int)query.GetInt(2);
                    int maxNum     = (int)query.GetInt(3);
                    int payItemNum = (int)query.GetInt(4);

                    int key = (int)id;
                    PriceChange orm = new PriceChange(id, groupId, minNum, maxNum, payItemNum);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class PriceChange
        {
            /// <summary>
            /// id
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: min_num) </summary>
            public readonly int MinNum;
            /// <summary> (CSV column: max_num) </summary>
            public readonly int MaxNum;
            /// <summary> (CSV column: pay_item_num) </summary>
            public readonly int PayItemNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public PriceChange(int id = 0, int groupId = 0, int minNum = 0, int maxNum = 0, int payItemNum = 0)
            {
                this.Id         = id;
                this.GroupId    = groupId;
                this.MinNum     = minNum;
                this.MaxNum     = maxNum;
                this.PayItemNum = payItemNum;
            }
        }
    }
}
