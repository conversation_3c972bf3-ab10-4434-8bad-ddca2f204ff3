// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: champions
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop {
    public class MasterChampionsDatabase : AbstractMasterDatabase
    {
        // member master wrapper classes
        public MasterChampionsSchedule masterChampionsSchedule                 { get; private set; }
        public MasterChampionsRoundSchedule masterChampionsRoundSchedule       { get; private set; }
        public MasterChampionsRoundDetail masterChampionsRoundDetail           { get; private set; }
        public MasterChampionsEvaluationRate masterChampionsEvaluationRate     { get; private set; }
        public MasterChampionsRaceCondition masterChampionsRaceCondition       { get; private set; }
        public MasterChampionsStandMotion masterChampionsStandMotion           { get; private set; }
        public MasterChampionsRewardRate masterChampionsRewardRate             { get; private set; }
        public MasterChampionsNewsTitle masterChampionsNewsTitle               { get; private set; }
        public MasterChampionsNewsRace masterChampionsNewsRace                 { get; private set; }
        public MasterChampionsNewsCharaComment masterChampionsNewsCharaComment { get; private set; }
        public MasterChampionsBgm masterChampionsBgm                           { get; private set; }
        public MasterChampionsEntryReward masterChampionsEntryReward           { get; private set; }
        public MasterChampionsNewsWinComment masterChampionsNewsWinComment     { get; private set; }
        public MasterChampionsNewsWinTitle masterChampionsNewsWinTitle         { get; private set; }

        // prepared sql statement caches
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsSchedule         = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsRoundSchedule    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsRoundDetail      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsEvaluationRate   = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsRaceCondition    = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsStandMotion      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsRewardRate       = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsNewsTitle        = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsNewsRace         = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsNewsCharaComment = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsBgm              = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsEntryReward      = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsNewsWinComment   = null;
        private LibNative.Sqlite3.PreparedQuery _selectQuery_masterChampionsNewsWinTitle     = null;

        // prepared indexed sql statement caches
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsRoundSchedule_championsId          = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsRoundSchedule_championsId_round    = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsRoundDetail_championsId_leagueType = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsRewardRate_championsId_roundId     = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsNewsTitle_roundId                  = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsNewsRace_roundId                   = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsNewsCharaComment_roundId           = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber   = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsEntryReward_championsId            = null;
        private LibNative.Sqlite3.PreparedQuery _indexedSelectQuery_championsNewsWinComment_roundId             = null;

        private LibNative.Sqlite3.Connection _connection = null;

        public MasterChampionsDatabase(LibNative.Sqlite3.Connection connection)
        {
            _connection = connection;

            this.masterChampionsSchedule         = new MasterChampionsSchedule(this);
            this.masterChampionsRoundSchedule    = new MasterChampionsRoundSchedule(this);
            this.masterChampionsRoundDetail      = new MasterChampionsRoundDetail(this);
            this.masterChampionsEvaluationRate   = new MasterChampionsEvaluationRate(this);
            this.masterChampionsRaceCondition    = new MasterChampionsRaceCondition(this);
            this.masterChampionsStandMotion      = new MasterChampionsStandMotion(this);
            this.masterChampionsRewardRate       = new MasterChampionsRewardRate(this);
            this.masterChampionsNewsTitle        = new MasterChampionsNewsTitle(this);
            this.masterChampionsNewsRace         = new MasterChampionsNewsRace(this);
            this.masterChampionsNewsCharaComment = new MasterChampionsNewsCharaComment(this);
            this.masterChampionsBgm              = new MasterChampionsBgm(this);
            this.masterChampionsEntryReward      = new MasterChampionsEntryReward(this);
            this.masterChampionsNewsWinComment   = new MasterChampionsNewsWinComment(this);
            this.masterChampionsNewsWinTitle     = new MasterChampionsNewsWinTitle(this);
        }

        private LibNative.Sqlite3.Connection _GetOpenedConnection()
        {
            if (!_connection.IsOpened()) {
#if UNITY_EDITOR
                if (UnityEditor.EditorApplication.isPlaying && GallopUtil.IsCompiling())
                {
                    DebugUtils.Assert(false, "コンパイル中にDBは開けません");
                }
#endif
                if (_connection.Open(_connection.dbPath)) {
                    Debug.Log("DB Open: master.mdb");
                } else {
                    Debug.LogError("Failed to connet champions database: " + _connection.dbPath);
                }
            }

            return _connection;
        }

        public override void Unload()
        {
            if (_selectQuery_masterChampionsSchedule != null) { _selectQuery_masterChampionsSchedule.Dispose(); _selectQuery_masterChampionsSchedule = null; }
            if (_selectQuery_masterChampionsRoundSchedule != null) { _selectQuery_masterChampionsRoundSchedule.Dispose(); _selectQuery_masterChampionsRoundSchedule = null; }
            if (_indexedSelectQuery_championsRoundSchedule_championsId != null) { _indexedSelectQuery_championsRoundSchedule_championsId.Dispose(); _indexedSelectQuery_championsRoundSchedule_championsId = null; }
            if (_indexedSelectQuery_championsRoundSchedule_championsId_round != null) { _indexedSelectQuery_championsRoundSchedule_championsId_round.Dispose(); _indexedSelectQuery_championsRoundSchedule_championsId_round = null; }
            if (_selectQuery_masterChampionsRoundDetail != null) { _selectQuery_masterChampionsRoundDetail.Dispose(); _selectQuery_masterChampionsRoundDetail = null; }
            if (_indexedSelectQuery_championsRoundDetail_championsId_leagueType != null) { _indexedSelectQuery_championsRoundDetail_championsId_leagueType.Dispose(); _indexedSelectQuery_championsRoundDetail_championsId_leagueType = null; }
            if (_selectQuery_masterChampionsEvaluationRate != null) { _selectQuery_masterChampionsEvaluationRate.Dispose(); _selectQuery_masterChampionsEvaluationRate = null; }
            if (_selectQuery_masterChampionsRaceCondition != null) { _selectQuery_masterChampionsRaceCondition.Dispose(); _selectQuery_masterChampionsRaceCondition = null; }
            if (_selectQuery_masterChampionsStandMotion != null) { _selectQuery_masterChampionsStandMotion.Dispose(); _selectQuery_masterChampionsStandMotion = null; }
            if (_selectQuery_masterChampionsRewardRate != null) { _selectQuery_masterChampionsRewardRate.Dispose(); _selectQuery_masterChampionsRewardRate = null; }
            if (_indexedSelectQuery_championsRewardRate_championsId_roundId != null) { _indexedSelectQuery_championsRewardRate_championsId_roundId.Dispose(); _indexedSelectQuery_championsRewardRate_championsId_roundId = null; }
            if (_selectQuery_masterChampionsNewsTitle != null) { _selectQuery_masterChampionsNewsTitle.Dispose(); _selectQuery_masterChampionsNewsTitle = null; }
            if (_indexedSelectQuery_championsNewsTitle_roundId != null) { _indexedSelectQuery_championsNewsTitle_roundId.Dispose(); _indexedSelectQuery_championsNewsTitle_roundId = null; }
            if (_selectQuery_masterChampionsNewsRace != null) { _selectQuery_masterChampionsNewsRace.Dispose(); _selectQuery_masterChampionsNewsRace = null; }
            if (_indexedSelectQuery_championsNewsRace_roundId != null) { _indexedSelectQuery_championsNewsRace_roundId.Dispose(); _indexedSelectQuery_championsNewsRace_roundId = null; }
            if (_selectQuery_masterChampionsNewsCharaComment != null) { _selectQuery_masterChampionsNewsCharaComment.Dispose(); _selectQuery_masterChampionsNewsCharaComment = null; }
            if (_indexedSelectQuery_championsNewsCharaComment_roundId != null) { _indexedSelectQuery_championsNewsCharaComment_roundId.Dispose(); _indexedSelectQuery_championsNewsCharaComment_roundId = null; }
            if (_selectQuery_masterChampionsBgm != null) { _selectQuery_masterChampionsBgm.Dispose(); _selectQuery_masterChampionsBgm = null; }
            if (_indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber != null) { _indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber.Dispose(); _indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber = null; }
            if (_selectQuery_masterChampionsEntryReward != null) { _selectQuery_masterChampionsEntryReward.Dispose(); _selectQuery_masterChampionsEntryReward = null; }
            if (_indexedSelectQuery_championsEntryReward_championsId != null) { _indexedSelectQuery_championsEntryReward_championsId.Dispose(); _indexedSelectQuery_championsEntryReward_championsId = null; }
            if (_selectQuery_masterChampionsNewsWinComment != null) { _selectQuery_masterChampionsNewsWinComment.Dispose(); _selectQuery_masterChampionsNewsWinComment = null; }
            if (_indexedSelectQuery_championsNewsWinComment_roundId != null) { _indexedSelectQuery_championsNewsWinComment_roundId.Dispose(); _indexedSelectQuery_championsNewsWinComment_roundId = null; }
            if (_selectQuery_masterChampionsNewsWinTitle != null) { _selectQuery_masterChampionsNewsWinTitle.Dispose(); _selectQuery_masterChampionsNewsWinTitle = null; }
            if (this.masterChampionsSchedule != null) { this.masterChampionsSchedule.Unload(); }
            if (this.masterChampionsRoundSchedule != null) { this.masterChampionsRoundSchedule.Unload(); }
            if (this.masterChampionsRoundDetail != null) { this.masterChampionsRoundDetail.Unload(); }
            if (this.masterChampionsEvaluationRate != null) { this.masterChampionsEvaluationRate.Unload(); }
            if (this.masterChampionsRaceCondition != null) { this.masterChampionsRaceCondition.Unload(); }
            if (this.masterChampionsStandMotion != null) { this.masterChampionsStandMotion.Unload(); }
            if (this.masterChampionsRewardRate != null) { this.masterChampionsRewardRate.Unload(); }
            if (this.masterChampionsNewsTitle != null) { this.masterChampionsNewsTitle.Unload(); }
            if (this.masterChampionsNewsRace != null) { this.masterChampionsNewsRace.Unload(); }
            if (this.masterChampionsNewsCharaComment != null) { this.masterChampionsNewsCharaComment.Unload(); }
            if (this.masterChampionsBgm != null) { this.masterChampionsBgm.Unload(); }
            if (this.masterChampionsEntryReward != null) { this.masterChampionsEntryReward.Unload(); }
            if (this.masterChampionsNewsWinComment != null) { this.masterChampionsNewsWinComment.Unload(); }
            if (this.masterChampionsNewsWinTitle != null) { this.masterChampionsNewsWinTitle.Unload(); }
        }

        // arbitrary sql interface
        public override LibNative.Sqlite3.Query Query(string sql)
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query(sql);
        }

        // SQL statements for champions/champions_schedule
        
        /// <summary>
        /// SELECT `resource_id`,`info_detail_1`,`info_detail_2`,`champions_bg_id`,`champions_league_select_bg_sub_id`,`champions_bg_sub_id`,`champions_finish_bg_sub_id`,`champions_bg_position_x`,`champions_chara_select_bg_id`,`champions_chara_select_bg_sub_id`,`start_date`,`end_date`,`info_detail`,`champions_finish_bg_id`,`champions_finish_bg_position_x`,`notice_date` FROM `champions_schedule` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsSchedule()
        {
            if (_selectQuery_masterChampionsSchedule == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsSchedule = connection.PreparedQuery("SELECT `resource_id`,`info_detail_1`,`info_detail_2`,`champions_bg_id`,`champions_league_select_bg_sub_id`,`champions_bg_sub_id`,`champions_finish_bg_sub_id`,`champions_bg_position_x`,`champions_chara_select_bg_id`,`champions_chara_select_bg_sub_id`,`start_date`,`end_date`,`info_detail`,`champions_finish_bg_id`,`champions_finish_bg_position_x`,`notice_date` FROM `champions_schedule` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsSchedule;
        }
        
        /// <summary>
        /// SELECT `id`,`resource_id`,`info_detail_1`,`info_detail_2`,`champions_bg_id`,`champions_league_select_bg_sub_id`,`champions_bg_sub_id`,`champions_finish_bg_sub_id`,`champions_bg_position_x`,`champions_chara_select_bg_id`,`champions_chara_select_bg_sub_id`,`start_date`,`end_date`,`info_detail`,`champions_finish_bg_id`,`champions_finish_bg_position_x`,`notice_date` FROM `champions_schedule`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsSchedule()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`resource_id`,`info_detail_1`,`info_detail_2`,`champions_bg_id`,`champions_league_select_bg_sub_id`,`champions_bg_sub_id`,`champions_finish_bg_sub_id`,`champions_bg_position_x`,`champions_chara_select_bg_id`,`champions_chara_select_bg_sub_id`,`start_date`,`end_date`,`info_detail`,`champions_finish_bg_id`,`champions_finish_bg_position_x`,`notice_date` FROM `champions_schedule`;");
        }
        
        // SQL statements for champions/champions_round_schedule
        
        /// <summary>
        /// SELECT `champions_id`,`round`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsRoundSchedule()
        {
            if (_selectQuery_masterChampionsRoundSchedule == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsRoundSchedule = connection.PreparedQuery("SELECT `champions_id`,`round`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsRoundSchedule;
        }
        
        /// <summary>
        /// SELECT `id`,`round`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule` WHERE `champions_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsRoundSchedule_ChampionsId()
        {
            if (_indexedSelectQuery_championsRoundSchedule_championsId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsRoundSchedule_championsId = connection.PreparedQuery("SELECT `id`,`round`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule` WHERE `champions_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_championsRoundSchedule_championsId;
        }
        
        /// <summary>
        /// SELECT `id`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule` WHERE `champions_id`=? AND `round`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsRoundSchedule_ChampionsId_Round()
        {
            if (_indexedSelectQuery_championsRoundSchedule_championsId_round == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsRoundSchedule_championsId_round = connection.PreparedQuery("SELECT `id`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule` WHERE `champions_id`=? AND `round`=?;");
            }
        
            return _indexedSelectQuery_championsRoundSchedule_championsId_round;
        }
        
        /// <summary>
        /// SELECT `id`,`champions_id`,`round`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsRoundSchedule()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`champions_id`,`round`,`round_detail`,`start_date`,`end_date`,`interval_start_time`,`interval_end_time` FROM `champions_round_schedule`;");
        }
        
        // SQL statements for champions/champions_round_detail
        
        /// <summary>
        /// SELECT `champions_id`,`league_type`,`round_id`,`round`,`tier`,`round_number`,`breakthrough_number_1`,`breakthrough_number_2`,`entry_number`,`free_entry_number` FROM `champions_round_detail` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsRoundDetail()
        {
            if (_selectQuery_masterChampionsRoundDetail == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsRoundDetail = connection.PreparedQuery("SELECT `champions_id`,`league_type`,`round_id`,`round`,`tier`,`round_number`,`breakthrough_number_1`,`breakthrough_number_2`,`entry_number`,`free_entry_number` FROM `champions_round_detail` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsRoundDetail;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`round`,`tier`,`round_number`,`breakthrough_number_1`,`breakthrough_number_2`,`entry_number`,`free_entry_number` FROM `champions_round_detail` WHERE `champions_id`=? AND `league_type`=? ORDER BY `round_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsRoundDetail_ChampionsId_LeagueType()
        {
            if (_indexedSelectQuery_championsRoundDetail_championsId_leagueType == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsRoundDetail_championsId_leagueType = connection.PreparedQuery("SELECT `id`,`round_id`,`round`,`tier`,`round_number`,`breakthrough_number_1`,`breakthrough_number_2`,`entry_number`,`free_entry_number` FROM `champions_round_detail` WHERE `champions_id`=? AND `league_type`=? ORDER BY `round_id` ASC;");
            }
        
            return _indexedSelectQuery_championsRoundDetail_championsId_leagueType;
        }
        
        /// <summary>
        /// SELECT `id`,`champions_id`,`league_type`,`round_id`,`round`,`tier`,`round_number`,`breakthrough_number_1`,`breakthrough_number_2`,`entry_number`,`free_entry_number` FROM `champions_round_detail`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsRoundDetail()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`champions_id`,`league_type`,`round_id`,`round`,`tier`,`round_number`,`breakthrough_number_1`,`breakthrough_number_2`,`entry_number`,`free_entry_number` FROM `champions_round_detail`;");
        }
        
        // SQL statements for champions/champions_evaluation_rate
        
        /// <summary>
        /// SELECT `proper_type`,`proper_rank`,`rate` FROM `champions_evaluation_rate` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsEvaluationRate()
        {
            if (_selectQuery_masterChampionsEvaluationRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsEvaluationRate = connection.PreparedQuery("SELECT `proper_type`,`proper_rank`,`rate` FROM `champions_evaluation_rate` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsEvaluationRate;
        }
        
        /// <summary>
        /// SELECT `id`,`proper_type`,`proper_rank`,`rate` FROM `champions_evaluation_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsEvaluationRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`proper_type`,`proper_rank`,`rate` FROM `champions_evaluation_rate`;");
        }
        
        // SQL statements for champions/champions_race_condition
        
        /// <summary>
        /// SELECT `race_instance_id`,`race_condition_id` FROM `champions_race_condition` WHERE `champions_id`=? AND `round_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsRaceCondition()
        {
            if (_selectQuery_masterChampionsRaceCondition == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsRaceCondition = connection.PreparedQuery("SELECT `race_instance_id`,`race_condition_id` FROM `champions_race_condition` WHERE `champions_id`=? AND `round_id`=?;");
            }
        
            return _selectQuery_masterChampionsRaceCondition;
        }
        
        /// <summary>
        /// SELECT `champions_id`,`round_id`,`race_instance_id`,`race_condition_id` FROM `champions_race_condition`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsRaceCondition()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `champions_id`,`round_id`,`race_instance_id`,`race_condition_id` FROM `champions_race_condition`;");
        }
        
        // SQL statements for champions/champions_stand_motion
        
        /// <summary>
        /// SELECT `race_dress_id`,`motion_set` FROM `champions_stand_motion` WHERE `chara_id`=? AND `type`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsStandMotion()
        {
            if (_selectQuery_masterChampionsStandMotion == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsStandMotion = connection.PreparedQuery("SELECT `race_dress_id`,`motion_set` FROM `champions_stand_motion` WHERE `chara_id`=? AND `type`=?;");
            }
        
            return _selectQuery_masterChampionsStandMotion;
        }
        
        /// <summary>
        /// SELECT `chara_id`,`type`,`race_dress_id`,`motion_set` FROM `champions_stand_motion`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsStandMotion()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `chara_id`,`type`,`race_dress_id`,`motion_set` FROM `champions_stand_motion`;");
        }
        
        // SQL statements for champions/champions_reward_rate
        
        /// <summary>
        /// SELECT `champions_id`,`league_type`,`round_id`,`win_count`,`ranking`,`box_grade` FROM `champions_reward_rate` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsRewardRate()
        {
            if (_selectQuery_masterChampionsRewardRate == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsRewardRate = connection.PreparedQuery("SELECT `champions_id`,`league_type`,`round_id`,`win_count`,`ranking`,`box_grade` FROM `champions_reward_rate` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsRewardRate;
        }
        
        /// <summary>
        /// SELECT `id`,`league_type`,`win_count`,`ranking`,`box_grade` FROM `champions_reward_rate` WHERE `champions_id`=? AND `round_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsRewardRate_ChampionsId_RoundId()
        {
            if (_indexedSelectQuery_championsRewardRate_championsId_roundId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsRewardRate_championsId_roundId = connection.PreparedQuery("SELECT `id`,`league_type`,`win_count`,`ranking`,`box_grade` FROM `champions_reward_rate` WHERE `champions_id`=? AND `round_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_championsRewardRate_championsId_roundId;
        }
        
        /// <summary>
        /// SELECT `id`,`champions_id`,`league_type`,`round_id`,`win_count`,`ranking`,`box_grade` FROM `champions_reward_rate`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsRewardRate()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`champions_id`,`league_type`,`round_id`,`win_count`,`ranking`,`box_grade` FROM `champions_reward_rate`;");
        }
        
        // SQL statements for champions/champions_news_title
        
        /// <summary>
        /// SELECT `round_id`,`win_percent_type`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather`,`title`,`sub_title` FROM `champions_news_title` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsNewsTitle()
        {
            if (_selectQuery_masterChampionsNewsTitle == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsNewsTitle = connection.PreparedQuery("SELECT `round_id`,`win_percent_type`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather`,`title`,`sub_title` FROM `champions_news_title` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsNewsTitle;
        }
        
        /// <summary>
        /// SELECT `id`,`win_percent_type`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather`,`title`,`sub_title` FROM `champions_news_title` WHERE `round_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsNewsTitle_RoundId()
        {
            if (_indexedSelectQuery_championsNewsTitle_roundId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsNewsTitle_roundId = connection.PreparedQuery("SELECT `id`,`win_percent_type`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather`,`title`,`sub_title` FROM `champions_news_title` WHERE `round_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_championsNewsTitle_roundId;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`win_percent_type`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather`,`title`,`sub_title` FROM `champions_news_title`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsNewsTitle()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`round_id`,`win_percent_type`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather`,`title`,`sub_title` FROM `champions_news_title`;");
        }
        
        // SQL statements for champions/champions_news_race
        
        /// <summary>
        /// SELECT `round_id`,`win_percent_type`,`text_number`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather` FROM `champions_news_race` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsNewsRace()
        {
            if (_selectQuery_masterChampionsNewsRace == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsNewsRace = connection.PreparedQuery("SELECT `round_id`,`win_percent_type`,`text_number`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather` FROM `champions_news_race` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsNewsRace;
        }
        
        /// <summary>
        /// SELECT `id`,`win_percent_type`,`text_number`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather` FROM `champions_news_race` WHERE `round_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsNewsRace_RoundId()
        {
            if (_indexedSelectQuery_championsNewsRace_roundId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsNewsRace_roundId = connection.PreparedQuery("SELECT `id`,`win_percent_type`,`text_number`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather` FROM `champions_news_race` WHERE `round_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_championsNewsRace_roundId;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`win_percent_type`,`text_number`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather` FROM `champions_news_race`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsNewsRace()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`round_id`,`win_percent_type`,`text_number`,`resource_id`,`race_track_id`,`ground`,`distance`,`weather` FROM `champions_news_race`;");
        }
        
        // SQL statements for champions/champions_news_chara_comment
        
        /// <summary>
        /// SELECT `round_id`,`chara_id`,`big_flag` FROM `champions_news_chara_comment` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsNewsCharaComment()
        {
            if (_selectQuery_masterChampionsNewsCharaComment == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsNewsCharaComment = connection.PreparedQuery("SELECT `round_id`,`chara_id`,`big_flag` FROM `champions_news_chara_comment` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsNewsCharaComment;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`big_flag` FROM `champions_news_chara_comment` WHERE `round_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsNewsCharaComment_RoundId()
        {
            if (_indexedSelectQuery_championsNewsCharaComment_roundId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsNewsCharaComment_roundId = connection.PreparedQuery("SELECT `id`,`chara_id`,`big_flag` FROM `champions_news_chara_comment` WHERE `round_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_championsNewsCharaComment_roundId;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`chara_id`,`big_flag` FROM `champions_news_chara_comment`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsNewsCharaComment()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`round_id`,`chara_id`,`big_flag` FROM `champions_news_chara_comment`;");
        }
        
        // SQL statements for champions/champions_bgm
        
        /// <summary>
        /// SELECT `round_id`,`scene_type`,`race_number`,`cue_name`,`cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern` FROM `champions_bgm` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsBgm()
        {
            if (_selectQuery_masterChampionsBgm == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsBgm = connection.PreparedQuery("SELECT `round_id`,`scene_type`,`race_number`,`cue_name`,`cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern` FROM `champions_bgm` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsBgm;
        }
        
        /// <summary>
        /// SELECT `id`,`cue_name`,`cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern` FROM `champions_bgm` WHERE `round_id`=? AND `scene_type`=? AND `race_number`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsBgm_RoundId_SceneType_RaceNumber()
        {
            if (_indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber = connection.PreparedQuery("SELECT `id`,`cue_name`,`cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern` FROM `champions_bgm` WHERE `round_id`=? AND `scene_type`=? AND `race_number`=?;");
            }
        
            return _indexedSelectQuery_championsBgm_roundId_sceneType_raceNumber;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`scene_type`,`race_number`,`cue_name`,`cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern` FROM `champions_bgm`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsBgm()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`round_id`,`scene_type`,`race_number`,`cue_name`,`cuesheet_name`,`first_bgm_pattern`,`second_bgm_pattern` FROM `champions_bgm`;");
        }
        
        // SQL statements for champions/champions_entry_reward
        
        /// <summary>
        /// SELECT `item_category`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? AND `item_id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsEntryReward()
        {
            if (_selectQuery_masterChampionsEntryReward == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsEntryReward = connection.PreparedQuery("SELECT `item_category`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? AND `item_id`=?;");
            }
        
            return _selectQuery_masterChampionsEntryReward;
        }
        
        /// <summary>
        /// SELECT `item_category`,`item_id`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? ORDER BY `item_id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsEntryReward_ChampionsId()
        {
            if (_indexedSelectQuery_championsEntryReward_championsId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsEntryReward_championsId = connection.PreparedQuery("SELECT `item_category`,`item_id`,`item_num` FROM `champions_entry_reward` WHERE `champions_id`=? ORDER BY `item_id` ASC;");
            }
        
            return _indexedSelectQuery_championsEntryReward_championsId;
        }
        
        /// <summary>
        /// SELECT `champions_id`,`item_category`,`item_id`,`item_num` FROM `champions_entry_reward`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsEntryReward()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `champions_id`,`item_category`,`item_id`,`item_num` FROM `champions_entry_reward`;");
        }
        
        // SQL statements for champions/champions_news_win_comment
        
        /// <summary>
        /// SELECT `round_id`,`chara_id`,`big_flag` FROM `champions_news_win_comment` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsNewsWinComment()
        {
            if (_selectQuery_masterChampionsNewsWinComment == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsNewsWinComment = connection.PreparedQuery("SELECT `round_id`,`chara_id`,`big_flag` FROM `champions_news_win_comment` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsNewsWinComment;
        }
        
        /// <summary>
        /// SELECT `id`,`chara_id`,`big_flag` FROM `champions_news_win_comment` WHERE `round_id`=? ORDER BY `id` ASC;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQueryWithIndex_ChampionsNewsWinComment_RoundId()
        {
            if (_indexedSelectQuery_championsNewsWinComment_roundId == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _indexedSelectQuery_championsNewsWinComment_roundId = connection.PreparedQuery("SELECT `id`,`chara_id`,`big_flag` FROM `champions_news_win_comment` WHERE `round_id`=? ORDER BY `id` ASC;");
            }
        
            return _indexedSelectQuery_championsNewsWinComment_roundId;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`chara_id`,`big_flag` FROM `champions_news_win_comment`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsNewsWinComment()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`round_id`,`chara_id`,`big_flag` FROM `champions_news_win_comment`;");
        }
        
        // SQL statements for champions/champions_news_win_title
        
        /// <summary>
        /// SELECT `round_id`,`win_percent_type`,`resource_id`,`bashin_min`,`bashin_max`,`running_style`,`race_track_id`,`ground`,`distance`,`weather`,`sub_title` FROM `champions_news_win_title` WHERE `id`=?;
        /// </summary>
        public LibNative.Sqlite3.PreparedQuery GetSelectQuery_ChampionsNewsWinTitle()
        {
            if (_selectQuery_masterChampionsNewsWinTitle == null) {
                LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
                _selectQuery_masterChampionsNewsWinTitle = connection.PreparedQuery("SELECT `round_id`,`win_percent_type`,`resource_id`,`bashin_min`,`bashin_max`,`running_style`,`race_track_id`,`ground`,`distance`,`weather`,`sub_title` FROM `champions_news_win_title` WHERE `id`=?;");
            }
        
            return _selectQuery_masterChampionsNewsWinTitle;
        }
        
        /// <summary>
        /// SELECT `id`,`round_id`,`win_percent_type`,`resource_id`,`bashin_min`,`bashin_max`,`running_style`,`race_track_id`,`ground`,`distance`,`weather`,`sub_title` FROM `champions_news_win_title`;
        /// </summary>
        public LibNative.Sqlite3.Query GetSelectAllQuery_ChampionsNewsWinTitle()
        {
            LibNative.Sqlite3.Connection connection = _GetOpenedConnection();
            return connection.Query("SELECT `id`,`round_id`,`win_percent_type`,`resource_id`,`bashin_min`,`bashin_max`,`running_style`,`race_track_id`,`ground`,`distance`,`weather`,`sub_title` FROM `champions_news_win_title`;");
        }
    }
}