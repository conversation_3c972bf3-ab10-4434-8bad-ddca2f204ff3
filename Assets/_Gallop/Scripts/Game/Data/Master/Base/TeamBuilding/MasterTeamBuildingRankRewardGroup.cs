// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: team_building/team_building_rank_reward_group
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:reward_group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterTeamBuildingRankRewardGroup : AbstractMasterData
    {
        public const string TABLE_NAME = "team_building_rank_reward_group";

        MasterTeamBuildingDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, TeamBuildingRankRewardGroup> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<TeamBuildingRankRewardGroup>> _dictionaryWithRewardGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, TeamBuildingRankRewardGroup> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterTeamBuildingRankRewardGroup");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterTeamBuildingRankRewardGroup(MasterTeamBuildingDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, TeamBuildingRankRewardGroup>();
            _dictionaryWithRewardGroupId = new Dictionary<int, List<TeamBuildingRankRewardGroup>>();
            _db = db;
        }


        public TeamBuildingRankRewardGroup Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterTeamBuildingRankRewardGroup");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterTeamBuildingRankRewardGroup", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private TeamBuildingRankRewardGroup _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_TeamBuildingRankRewardGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamBuildingRankRewardGroup");
                return null;
            }

            // SELECT `reward_group_id`,`item_category`,`item_id`,`item_num` FROM `team_building_rank_reward_group` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            TeamBuildingRankRewardGroup orm = null;

            if (query.Step())
            {
                int rewardGroupId = (int)query.GetInt(0);
                int itemCategory  = (int)query.GetInt(1);
                int itemId        = (int)query.GetInt(2);
                int itemNum       = (int)query.GetInt(3);

                orm = new TeamBuildingRankRewardGroup(id, rewardGroupId, itemCategory, itemId, itemNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public TeamBuildingRankRewardGroup GetWithRewardGroupIdOrderByIdAsc(int rewardGroupId)
        {
            TeamBuildingRankRewardGroup orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithRewardGroupIdOrderByIdAsc(rewardGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", rewardGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private TeamBuildingRankRewardGroup _SelectWithRewardGroupIdOrderByIdAsc(int rewardGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamBuildingRankRewardGroup_RewardGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamBuildingRankRewardGroup");
                return null;
            }

            // SELECT `id`,`item_category`,`item_id`,`item_num` FROM `team_building_rank_reward_group` WHERE `reward_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, rewardGroupId)) { return null; }

            TeamBuildingRankRewardGroup orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithRewardGroupIdOrderByIdAsc(query, rewardGroupId);
            }

            query.Reset();

            return orm;
        }

        public List<TeamBuildingRankRewardGroup> GetListWithRewardGroupIdOrderByIdAsc(int rewardGroupId)
        {
            int key = (int)rewardGroupId;
            
            if (!_dictionaryWithRewardGroupId.ContainsKey(key)) {
                _dictionaryWithRewardGroupId.Add(key, _ListSelectWithRewardGroupIdOrderByIdAsc(rewardGroupId));
            }

            return _dictionaryWithRewardGroupId[key];
      
        }

        public List<TeamBuildingRankRewardGroup> MaybeListWithRewardGroupIdOrderByIdAsc(int rewardGroupId)
        {
            List<TeamBuildingRankRewardGroup> list = GetListWithRewardGroupIdOrderByIdAsc(rewardGroupId);
            return list.Count > 0 ? list : null;
        }

        private List<TeamBuildingRankRewardGroup> _ListSelectWithRewardGroupIdOrderByIdAsc(int rewardGroupId)
        {
            List<TeamBuildingRankRewardGroup> _list = new List<TeamBuildingRankRewardGroup>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_TeamBuildingRankRewardGroup_RewardGroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for TeamBuildingRankRewardGroup");
                return null;
            }

            // SELECT `id`,`item_category`,`item_id`,`item_num` FROM `team_building_rank_reward_group` WHERE `reward_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, rewardGroupId)) { return null; }

            while (query.Step()) {
                TeamBuildingRankRewardGroup orm = _CreateOrmByQueryResultWithRewardGroupIdOrderByIdAsc(query, rewardGroupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private TeamBuildingRankRewardGroup _CreateOrmByQueryResultWithRewardGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int rewardGroupId)
        {
            int id           = (int)query.GetInt(0);
            int itemCategory = (int)query.GetInt(1);
            int itemId       = (int)query.GetInt(2);
            int itemNum      = (int)query.GetInt(3);

            return new TeamBuildingRankRewardGroup(id, rewardGroupId, itemCategory, itemId, itemNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithRewardGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_TeamBuildingRankRewardGroup()) {
                while (query.Step()) {
                    int id            = (int)query.GetInt(0);
                    int rewardGroupId = (int)query.GetInt(1);
                    int itemCategory  = (int)query.GetInt(2);
                    int itemId        = (int)query.GetInt(3);
                    int itemNum       = (int)query.GetInt(4);

                    int key = (int)id;
                    TeamBuildingRankRewardGroup orm = new TeamBuildingRankRewardGroup(id, rewardGroupId, itemCategory, itemId, itemNum);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class TeamBuildingRankRewardGroup
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: reward_group_id) </summary>
            public readonly int RewardGroupId;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: item_num) </summary>
            public readonly int ItemNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public TeamBuildingRankRewardGroup(int id = 0, int rewardGroupId = 0, int itemCategory = 0, int itemId = 0, int itemNum = 0)
            {
                this.Id            = id;
                this.RewardGroupId = rewardGroupId;
                this.ItemCategory  = itemCategory;
                this.ItemId        = itemId;
                this.ItemNum       = itemNum;
            }
        }
    }
}
