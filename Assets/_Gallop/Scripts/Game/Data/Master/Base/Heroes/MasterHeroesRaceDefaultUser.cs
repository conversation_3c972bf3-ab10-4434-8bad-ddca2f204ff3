// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: heroes/heroes_race_default_user
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterHeroesRaceDefaultUser : AbstractMasterData
    {
        public const string TABLE_NAME = "heroes_race_default_user";

        MasterHeroesDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, HeroesRaceDefaultUser> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, HeroesRaceDefaultUser> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterHeroesRaceDefaultUser");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterHeroesRaceDefaultUser(MasterHeroesDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, HeroesRaceDefaultUser>();
            _db = db;
        }


        public HeroesRaceDefaultUser Get(int defaultNpcTeamId)
        {
            int key = (int)defaultNpcTeamId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterHeroesRaceDefaultUser");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(defaultNpcTeamId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterHeroesRaceDefaultUser", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private HeroesRaceDefaultUser _SelectOne(int defaultNpcTeamId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_HeroesRaceDefaultUser();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for HeroesRaceDefaultUser");
                return null;
            }

            // SELECT * FROM `heroes_race_default_user` WHERE `default_npc_team_id`=?;
            if (!query.BindInt(1, defaultNpcTeamId)) { return null; }

            HeroesRaceDefaultUser orm = null;

            if (query.Step())
            {
                

                orm = new HeroesRaceDefaultUser(defaultNpcTeamId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", defaultNpcTeamId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_HeroesRaceDefaultUser()) {
                while (query.Step()) {
                    int defaultNpcTeamId = (int)query.GetInt(0);

                    int key = (int)defaultNpcTeamId;
                    HeroesRaceDefaultUser orm = new HeroesRaceDefaultUser(defaultNpcTeamId);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class HeroesRaceDefaultUser
        {
            /// <summary> (CSV column: default_npc_team_id) </summary>
            public readonly int DefaultNpcTeamId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public HeroesRaceDefaultUser(int defaultNpcTeamId = 0)
            {
                this.DefaultNpcTeamId = defaultNpcTeamId;
            }
        }
    }
}
