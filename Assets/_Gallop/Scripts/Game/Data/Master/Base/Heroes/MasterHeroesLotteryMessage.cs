// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: heroes/heroes_lottery_message
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:view_type]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterHeroesLotteryMessage : AbstractMasterData
    {
        public const string TABLE_NAME = "heroes_lottery_message";

        MasterHeroesDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, HeroesLotteryMessage> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<HeroesLotteryMessage>> _dictionaryWithViewType = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, HeroesLotteryMessage> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterHeroesLotteryMessage");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterHeroesLotteryMessage(MasterHeroesDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, HeroesLotteryMessage>();
            _dictionaryWithViewType = new Dictionary<int, List<HeroesLotteryMessage>>();
            _db = db;
        }


        public HeroesLotteryMessage Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterHeroesLotteryMessage");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterHeroesLotteryMessage", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private HeroesLotteryMessage _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_HeroesLotteryMessage();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for HeroesLotteryMessage");
                return null;
            }

            // SELECT `view_type`,`condition_flag` FROM `heroes_lottery_message` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            HeroesLotteryMessage orm = null;

            if (query.Step())
            {
                int viewType      = (int)query.GetInt(0);
                int conditionFlag = (int)query.GetInt(1);

                orm = new HeroesLotteryMessage(id, viewType, conditionFlag);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public HeroesLotteryMessage GetWithViewType(int viewType)
        {
            HeroesLotteryMessage orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithViewType(viewType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", viewType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private HeroesLotteryMessage _SelectWithViewType(int viewType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_HeroesLotteryMessage_ViewType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for HeroesLotteryMessage");
                return null;
            }

            // SELECT `id`,`condition_flag` FROM `heroes_lottery_message` WHERE `view_type`=?;
            if (!query.BindInt(1, viewType)) { return null; }

            HeroesLotteryMessage orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithViewType(query, viewType);
            }

            query.Reset();

            return orm;
        }

        public List<HeroesLotteryMessage> GetListWithViewType(int viewType)
        {
            int key = (int)viewType;
            
            if (!_dictionaryWithViewType.ContainsKey(key)) {
                _dictionaryWithViewType.Add(key, _ListSelectWithViewType(viewType));
            }

            return _dictionaryWithViewType[key];
      
        }

        public List<HeroesLotteryMessage> MaybeListWithViewType(int viewType)
        {
            List<HeroesLotteryMessage> list = GetListWithViewType(viewType);
            return list.Count > 0 ? list : null;
        }

        private List<HeroesLotteryMessage> _ListSelectWithViewType(int viewType)
        {
            List<HeroesLotteryMessage> _list = new List<HeroesLotteryMessage>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_HeroesLotteryMessage_ViewType();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for HeroesLotteryMessage");
                return null;
            }

            // SELECT `id`,`condition_flag` FROM `heroes_lottery_message` WHERE `view_type`=?;
            if (!query.BindInt(1, viewType)) { return null; }

            while (query.Step()) {
                HeroesLotteryMessage orm = _CreateOrmByQueryResultWithViewType(query, viewType);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private HeroesLotteryMessage _CreateOrmByQueryResultWithViewType(LibNative.Sqlite3.PreparedQuery query, int viewType)
        {
            int id            = (int)query.GetInt(0);
            int conditionFlag = (int)query.GetInt(1);

            return new HeroesLotteryMessage(id, viewType, conditionFlag);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithViewType.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_HeroesLotteryMessage()) {
                while (query.Step()) {
                    int id            = (int)query.GetInt(0);
                    int viewType      = (int)query.GetInt(1);
                    int conditionFlag = (int)query.GetInt(2);

                    int key = (int)id;
                    HeroesLotteryMessage orm = new HeroesLotteryMessage(id, viewType, conditionFlag);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class HeroesLotteryMessage
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary>
            /// 使用箇所
            /// (CSV column: view_type)
            /// </summary>
            public readonly int ViewType;
            /// <summary>
            /// 条件の有無
            /// (CSV column: condition_flag)
            /// </summary>
            public readonly int ConditionFlag;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public HeroesLotteryMessage(int id = 0, int viewType = 0, int conditionFlag = 0)
            {
                this.Id            = id;
                this.ViewType      = viewType;
                this.ConditionFlag = conditionFlag;
            }
        }
    }
}
