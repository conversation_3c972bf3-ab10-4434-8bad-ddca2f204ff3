// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: campaign/campaign_rental_support_data
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterCampaignRentalSupportData : AbstractMasterData
    {
        public const string TABLE_NAME = "campaign_rental_support_data";

        MasterCampaignDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, CampaignRentalSupportData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, CampaignRentalSupportData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterCampaignRentalSupportData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterCampaignRentalSupportData(MasterCampaignDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, CampaignRentalSupportData>();
            _db = db;
        }


        public CampaignRentalSupportData Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterCampaignRentalSupportData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterCampaignRentalSupportData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private CampaignRentalSupportData _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_CampaignRentalSupportData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for CampaignRentalSupportData");
                return null;
            }

            // SELECT `rental_limit`,`start_date`,`end_date` FROM `campaign_rental_support_data` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            CampaignRentalSupportData orm = null;

            if (query.Step())
            {
                int rentalLimit = (int)query.GetInt(0);
                long startDate  = (long)query.GetLong(1);
                long endDate    = (long)query.GetLong(2);

                orm = new CampaignRentalSupportData(id, rentalLimit, startDate, endDate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_CampaignRentalSupportData()) {
                while (query.Step()) {
                    int id          = (int)query.GetInt(0);
                    int rentalLimit = (int)query.GetInt(1);
                    long startDate  = (long)query.GetLong(2);
                    long endDate    = (long)query.GetLong(3);

                    int key = (int)id;
                    CampaignRentalSupportData orm = new CampaignRentalSupportData(id, rentalLimit, startDate, endDate);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class CampaignRentalSupportData
        {
            /// <summary>
            /// サポートカードレンタルの開催回毎のID
            /// (CSV column: id)
            /// </summary>
            public readonly int Id;
            /// <summary>
            /// 1日のレンタル上限回数
            /// (CSV column: rental_limit)
            /// </summary>
            public readonly int RentalLimit;
            /// <summary>
            /// 開始日時
            /// (CSV column: start_date)
            /// </summary>
            public readonly long StartDate;
            /// <summary>
            /// 終了日時
            /// (CSV column: end_date)
            /// </summary>
            public readonly long EndDate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public CampaignRentalSupportData(int id = 0, int rentalLimit = 0, long startDate = 0, long endDate = 0)
            {
                this.Id          = id;
                this.RentalLimit = rentalLimit;
                this.StartDate   = startDate;
                this.EndDate     = endDate;
            }
        }
    }
}
