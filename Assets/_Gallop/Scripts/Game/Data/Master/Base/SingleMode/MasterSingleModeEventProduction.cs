// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_event_production
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeEventProduction : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_event_production";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;


        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeEventProduction(MasterSingleModeDatabase db) : base(db)
        {
            _db = db;
        }


        public SingleModeEventProduction Get(int storyId)
        {
            int key = (int)storyId;


            SingleModeEventProduction orm = null;
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeEventProduction");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(storyId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeEventProduction", key));
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeEventProduction _SelectOne(int storyId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeEventProduction();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeEventProduction");
                return null;
            }

            // SELECT `event_category_id`,`max_item_id`,`item_dir`,`item_name` FROM `single_mode_event_production` WHERE `story_id`=?;
            if (!query.BindInt(1, storyId)) { return null; }

            SingleModeEventProduction orm = null;

            if (query.Step())
            {
                int eventCategoryId = (int)query.GetInt(0);
                int maxItemId       = (int)query.GetInt(1);
                string itemDir      = query.GetText(2);
                string itemName     = query.GetText(3);

                orm = new SingleModeEventProduction(storyId, eventCategoryId, maxItemId, itemDir, itemName);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", storyId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
        }


        public List<SingleModeEventProduction> GetListAllEntries()
        {
            List<SingleModeEventProduction> _list = new List<SingleModeEventProduction>();
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SingleModeEventProduction()) {
                while (query.Step()) {
                    int storyId         = (int)query.GetInt(0);
                    int eventCategoryId = (int)query.GetInt(1);
                    int maxItemId       = (int)query.GetInt(2);
                    string itemDir      = query.GetText(3);
                    string itemName     = query.GetText(4);

                    SingleModeEventProduction orm = new SingleModeEventProduction(storyId, eventCategoryId, maxItemId, itemDir, itemName);

                    _list.Add(orm);
                }
            }
            return _list;
        }

        public sealed partial class SingleModeEventProduction
        {
            /// <summary> (CSV column: story_id) </summary>
            public readonly int StoryId;
            /// <summary> (CSV column: event_category_id) </summary>
            public readonly int EventCategoryId;
            /// <summary> (CSV column: max_item_id) </summary>
            public readonly int MaxItemId;
            /// <summary> (CSV column: item_dir) </summary>
            public readonly string ItemDir;
            /// <summary> (CSV column: item_name) </summary>
            public readonly string ItemName;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeEventProduction(int storyId = 0, int eventCategoryId = 0, int maxItemId = 0, string itemDir = "", string itemName = "")
            {
                this.StoryId         = storyId;
                this.EventCategoryId = eventCategoryId;
                this.MaxItemId       = maxItemId;
                this.ItemDir         = itemDir;
                this.ItemName        = itemName;
            }
        }
    }
}
