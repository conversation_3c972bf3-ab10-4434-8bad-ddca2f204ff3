// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/omakase_filter
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scenario_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterOmakaseFilter : AbstractMasterData
    {
        public const string TABLE_NAME = "omakase_filter";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, OmakaseFilter> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<OmakaseFilter>> _dictionaryWithScenarioId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterOmakaseFilter(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, OmakaseFilter>();
            _dictionaryWithScenarioId = new Dictionary<int, List<OmakaseFilter>>();
            _db = db;
        }


        public OmakaseFilter Get(int filterId)
        {
            int key = (int)filterId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterOmakaseFilter");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(filterId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterOmakaseFilter", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private OmakaseFilter _SelectOne(int filterId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_OmakaseFilter();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseFilter");
                return null;
            }

            // SELECT `is_modifiable`,`command_id`,`scenario_id`,`condition_set_id`,`priority` FROM `omakase_filter` WHERE `filter_id`=?;
            if (!query.BindInt(1, filterId)) { return null; }

            OmakaseFilter orm = null;

            if (query.Step())
            {
                int isModifiable   = (int)query.GetInt(0);
                int commandId      = (int)query.GetInt(1);
                int scenarioId     = (int)query.GetInt(2);
                int conditionSetId = (int)query.GetInt(3);
                int priority       = (int)query.GetInt(4);

                orm = new OmakaseFilter(filterId, isModifiable, commandId, scenarioId, conditionSetId, priority);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", filterId));
            }

            query.Reset();

            return orm;
        }

        public OmakaseFilter GetWithScenarioIdOrderByFilterIdAsc(int scenarioId)
        {
            OmakaseFilter orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithScenarioIdOrderByFilterIdAsc(scenarioId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", scenarioId));
                } else {
                    int key = (int)orm.FilterId;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private OmakaseFilter _SelectWithScenarioIdOrderByFilterIdAsc(int scenarioId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_OmakaseFilter_ScenarioId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseFilter");
                return null;
            }

            // SELECT `filter_id`,`is_modifiable`,`command_id`,`condition_set_id`,`priority` FROM `omakase_filter` WHERE `scenario_id`=? ORDER BY `filter_id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            OmakaseFilter orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithScenarioIdOrderByFilterIdAsc(query, scenarioId);
            }

            query.Reset();

            return orm;
        }

        public List<OmakaseFilter> GetListWithScenarioIdOrderByFilterIdAsc(int scenarioId)
        {
            int key = (int)scenarioId;
            
            if (!_dictionaryWithScenarioId.ContainsKey(key)) {
                _dictionaryWithScenarioId.Add(key, _ListSelectWithScenarioIdOrderByFilterIdAsc(scenarioId));
            }

            return _dictionaryWithScenarioId[key];
      
        }

        public List<OmakaseFilter> MaybeListWithScenarioIdOrderByFilterIdAsc(int scenarioId)
        {
            List<OmakaseFilter> list = GetListWithScenarioIdOrderByFilterIdAsc(scenarioId);
            return list.Count > 0 ? list : null;
        }

        private List<OmakaseFilter> _ListSelectWithScenarioIdOrderByFilterIdAsc(int scenarioId)
        {
            List<OmakaseFilter> _list = new List<OmakaseFilter>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_OmakaseFilter_ScenarioId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseFilter");
                return null;
            }

            // SELECT `filter_id`,`is_modifiable`,`command_id`,`condition_set_id`,`priority` FROM `omakase_filter` WHERE `scenario_id`=? ORDER BY `filter_id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            while (query.Step()) {
                OmakaseFilter orm = _CreateOrmByQueryResultWithScenarioIdOrderByFilterIdAsc(query, scenarioId);
                int key = (int)orm.FilterId;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private OmakaseFilter _CreateOrmByQueryResultWithScenarioIdOrderByFilterIdAsc(LibNative.Sqlite3.PreparedQuery query, int scenarioId)
        {
            int filterId       = (int)query.GetInt(0);
            int isModifiable   = (int)query.GetInt(1);
            int commandId      = (int)query.GetInt(2);
            int conditionSetId = (int)query.GetInt(3);
            int priority       = (int)query.GetInt(4);

            return new OmakaseFilter(filterId, isModifiable, commandId, scenarioId, conditionSetId, priority);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithScenarioId.Clear();
        }



        public sealed partial class OmakaseFilter
        {
            /// <summary> (CSV column: filter_id) </summary>
            public readonly int FilterId;
            /// <summary> (CSV column: is_modifiable) </summary>
            public readonly int IsModifiable;
            /// <summary> (CSV column: command_id) </summary>
            public readonly int CommandId;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: condition_set_id) </summary>
            public readonly int ConditionSetId;
            /// <summary> (CSV column: priority) </summary>
            public readonly int Priority;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public OmakaseFilter(int filterId = 0, int isModifiable = 0, int commandId = 0, int scenarioId = 0, int conditionSetId = 0, int priority = 0)
            {
                this.FilterId       = filterId;
                this.IsModifiable   = isModifiable;
                this.CommandId      = commandId;
                this.ScenarioId     = scenarioId;
                this.ConditionSetId = conditionSetId;
                this.Priority       = priority;
            }
        }
    }
}
