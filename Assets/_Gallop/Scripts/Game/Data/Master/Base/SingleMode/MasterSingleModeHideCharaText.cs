// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_hide_chara_text
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:character_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeHideCharaText : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_hide_chara_text";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeHideCharaText> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SingleModeHideCharaText>> _dictionaryWithCharacterId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeHideCharaText(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeHideCharaText>();
            _dictionaryWithCharacterId = new Dictionary<int, List<SingleModeHideCharaText>>();
            _db = db;
        }


        public SingleModeHideCharaText Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeHideCharaText");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeHideCharaText", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeHideCharaText _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeHideCharaText();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeHideCharaText");
                return null;
            }

            // SELECT `character_id`,`voice_id` FROM `single_mode_hide_chara_text` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeHideCharaText orm = null;

            if (query.Step())
            {
                int characterId = (int)query.GetInt(0);
                int voiceId     = (int)query.GetInt(1);

                orm = new SingleModeHideCharaText(id, characterId, voiceId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeHideCharaText GetWithCharacterId(int characterId)
        {
            SingleModeHideCharaText orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharacterId(characterId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", characterId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeHideCharaText _SelectWithCharacterId(int characterId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeHideCharaText_CharacterId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeHideCharaText");
                return null;
            }

            // SELECT `id`,`voice_id` FROM `single_mode_hide_chara_text` WHERE `character_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }

            SingleModeHideCharaText orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharacterId(query, characterId);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeHideCharaText> GetListWithCharacterId(int characterId)
        {
            int key = (int)characterId;
            
            if (!_dictionaryWithCharacterId.ContainsKey(key)) {
                _dictionaryWithCharacterId.Add(key, _ListSelectWithCharacterId(characterId));
            }

            return _dictionaryWithCharacterId[key];
      
        }

        public List<SingleModeHideCharaText> MaybeListWithCharacterId(int characterId)
        {
            List<SingleModeHideCharaText> list = GetListWithCharacterId(characterId);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeHideCharaText> _ListSelectWithCharacterId(int characterId)
        {
            List<SingleModeHideCharaText> _list = new List<SingleModeHideCharaText>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeHideCharaText_CharacterId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeHideCharaText");
                return null;
            }

            // SELECT `id`,`voice_id` FROM `single_mode_hide_chara_text` WHERE `character_id`=?;
            if (!query.BindInt(1, characterId)) { return null; }

            while (query.Step()) {
                SingleModeHideCharaText orm = _CreateOrmByQueryResultWithCharacterId(query, characterId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeHideCharaText _CreateOrmByQueryResultWithCharacterId(LibNative.Sqlite3.PreparedQuery query, int characterId)
        {
            int id      = (int)query.GetInt(0);
            int voiceId = (int)query.GetInt(1);

            return new SingleModeHideCharaText(id, characterId, voiceId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCharacterId.Clear();
        }



        public sealed partial class SingleModeHideCharaText
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: character_id) </summary>
            public readonly int CharacterId;
            /// <summary> (CSV column: voice_id) </summary>
            public readonly int VoiceId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeHideCharaText(int id = 0, int characterId = 0, int voiceId = 0)
            {
                this.Id          = id;
                this.CharacterId = characterId;
                this.VoiceId     = voiceId;
            }
        }
    }
}
