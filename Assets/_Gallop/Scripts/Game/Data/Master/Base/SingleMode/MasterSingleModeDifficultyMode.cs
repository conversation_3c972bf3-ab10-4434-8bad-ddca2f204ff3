// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_difficulty_mode
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeDifficultyMode : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_difficulty_mode";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeDifficultyMode> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeDifficultyMode(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeDifficultyMode>();
            _db = db;
        }


        public SingleModeDifficultyMode Get(int difficultyId)
        {
            int key = (int)difficultyId;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeDifficultyMode");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(difficultyId);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeDifficultyMode", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeDifficultyMode _SelectOne(int difficultyId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeDifficultyMode();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeDifficultyMode");
                return null;
            }

            // SELECT `guide_id`,`consume_tp_ratio`,`consume_tp_bonus_ratio`,`chara_id`,`dress_id`,`in_motion`,`stand_motion`,`play_motion`,`gauge_motion`,`gauge_motion2`,`gauge_motion3`,`popout_motion`,`firstclear_motion`,`prize_motion1`,`prize_motion2`,`prize_motion3`,`bgm_cue_name`,`bgm_cuesheet_name`,`reward_type`,`reward_set_id`,`gauge_max`,`gauge_up`,`item_category`,`item_id`,`max_num` FROM `single_mode_difficulty_mode` WHERE `difficulty_id`=?;
            if (!query.BindInt(1, difficultyId)) { return null; }

            SingleModeDifficultyMode orm = null;

            if (query.Step())
            {
                int guideId             = (int)query.GetInt(0);
                int consumeTpRatio      = (int)query.GetInt(1);
                int consumeTpBonusRatio = (int)query.GetInt(2);
                int charaId             = (int)query.GetInt(3);
                int dressId             = (int)query.GetInt(4);
                int inMotion            = (int)query.GetInt(5);
                int standMotion         = (int)query.GetInt(6);
                int playMotion          = (int)query.GetInt(7);
                int gaugeMotion         = (int)query.GetInt(8);
                int gaugeMotion2        = (int)query.GetInt(9);
                int gaugeMotion3        = (int)query.GetInt(10);
                int popoutMotion        = (int)query.GetInt(11);
                int firstclearMotion    = (int)query.GetInt(12);
                int prizeMotion1        = (int)query.GetInt(13);
                int prizeMotion2        = (int)query.GetInt(14);
                int prizeMotion3        = (int)query.GetInt(15);
                string bgmCueName       = query.GetText(16);
                string bgmCuesheetName  = query.GetText(17);
                int rewardType          = (int)query.GetInt(18);
                int rewardSetId         = (int)query.GetInt(19);
                int gaugeMax            = (int)query.GetInt(20);
                int gaugeUp             = (int)query.GetInt(21);
                int itemCategory        = (int)query.GetInt(22);
                int itemId              = (int)query.GetInt(23);
                int maxNum              = (int)query.GetInt(24);

                orm = new SingleModeDifficultyMode(difficultyId, guideId, consumeTpRatio, consumeTpBonusRatio, charaId, dressId, inMotion, standMotion, playMotion, gaugeMotion, gaugeMotion2, gaugeMotion3, popoutMotion, firstclearMotion, prizeMotion1, prizeMotion2, prizeMotion3, bgmCueName, bgmCuesheetName, rewardType, rewardSetId, gaugeMax, gaugeUp, itemCategory, itemId, maxNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", difficultyId));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class SingleModeDifficultyMode
        {
            /// <summary> (CSV column: difficulty_id) </summary>
            public readonly int DifficultyId;
            /// <summary> (CSV column: guide_id) </summary>
            public readonly int GuideId;
            /// <summary>
            /// 消費TP量(ブーストモード利用時)
            /// (CSV column: consume_tp_ratio)
            /// </summary>
            public readonly int ConsumeTpRatio;
            /// <summary>
            /// ブーストモード利用のボーナス率
            /// (CSV column: consume_tp_bonus_ratio)
            /// </summary>
            public readonly int ConsumeTpBonusRatio;
            /// <summary> (CSV column: chara_id) </summary>
            public readonly int CharaId;
            /// <summary> (CSV column: dress_id) </summary>
            public readonly int DressId;
            /// <summary> (CSV column: in_motion) </summary>
            public readonly int InMotion;
            /// <summary> (CSV column: stand_motion) </summary>
            public readonly int StandMotion;
            /// <summary> (CSV column: play_motion) </summary>
            public readonly int PlayMotion;
            /// <summary> (CSV column: gauge_motion) </summary>
            public readonly int GaugeMotion;
            /// <summary> (CSV column: gauge_motion2) </summary>
            public readonly int GaugeMotion2;
            /// <summary> (CSV column: gauge_motion3) </summary>
            public readonly int GaugeMotion3;
            /// <summary> (CSV column: popout_motion) </summary>
            public readonly int PopoutMotion;
            /// <summary> (CSV column: firstclear_motion) </summary>
            public readonly int FirstclearMotion;
            /// <summary> (CSV column: prize_motion1) </summary>
            public readonly int PrizeMotion1;
            /// <summary> (CSV column: prize_motion2) </summary>
            public readonly int PrizeMotion2;
            /// <summary> (CSV column: prize_motion3) </summary>
            public readonly int PrizeMotion3;
            /// <summary> (CSV column: bgm_cue_name) </summary>
            public readonly string BgmCueName;
            /// <summary> (CSV column: bgm_cuesheet_name) </summary>
            public readonly string BgmCuesheetName;
            /// <summary> (CSV column: reward_type) </summary>
            public readonly int RewardType;
            /// <summary> (CSV column: reward_set_id) </summary>
            public readonly int RewardSetId;
            /// <summary> (CSV column: gauge_max) </summary>
            public readonly int GaugeMax;
            /// <summary> (CSV column: gauge_up) </summary>
            public readonly int GaugeUp;
            /// <summary> (CSV column: item_category) </summary>
            public readonly int ItemCategory;
            /// <summary> (CSV column: item_id) </summary>
            public readonly int ItemId;
            /// <summary> (CSV column: max_num) </summary>
            public readonly int MaxNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeDifficultyMode(int difficultyId = 0, int guideId = 0, int consumeTpRatio = 0, int consumeTpBonusRatio = 0, int charaId = 0, int dressId = 0, int inMotion = 0, int standMotion = 0, int playMotion = 0, int gaugeMotion = 0, int gaugeMotion2 = 0, int gaugeMotion3 = 0, int popoutMotion = 0, int firstclearMotion = 0, int prizeMotion1 = 0, int prizeMotion2 = 0, int prizeMotion3 = 0, string bgmCueName = "", string bgmCuesheetName = "", int rewardType = 0, int rewardSetId = 0, int gaugeMax = 0, int gaugeUp = 0, int itemCategory = 0, int itemId = 0, int maxNum = 0)
            {
                this.DifficultyId        = difficultyId;
                this.GuideId             = guideId;
                this.ConsumeTpRatio      = consumeTpRatio;
                this.ConsumeTpBonusRatio = consumeTpBonusRatio;
                this.CharaId             = charaId;
                this.DressId             = dressId;
                this.InMotion            = inMotion;
                this.StandMotion         = standMotion;
                this.PlayMotion          = playMotion;
                this.GaugeMotion         = gaugeMotion;
                this.GaugeMotion2        = gaugeMotion2;
                this.GaugeMotion3        = gaugeMotion3;
                this.PopoutMotion        = popoutMotion;
                this.FirstclearMotion    = firstclearMotion;
                this.PrizeMotion1        = prizeMotion1;
                this.PrizeMotion2        = prizeMotion2;
                this.PrizeMotion3        = prizeMotion3;
                this.BgmCueName          = bgmCueName;
                this.BgmCuesheetName     = bgmCuesheetName;
                this.RewardType          = rewardType;
                this.RewardSetId         = rewardSetId;
                this.GaugeMax            = gaugeMax;
                this.GaugeUp             = gaugeUp;
                this.ItemCategory        = itemCategory;
                this.ItemId              = itemId;
                this.MaxNum              = maxNum;
            }
        }
    }
}
