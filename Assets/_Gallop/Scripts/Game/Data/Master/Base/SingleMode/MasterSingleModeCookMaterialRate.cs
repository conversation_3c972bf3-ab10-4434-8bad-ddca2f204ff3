// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_cook_material_rate
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeCookMaterialRate : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_cook_material_rate";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeCookMaterialRate> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeCookMaterialRate(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeCookMaterialRate>();
            _db = db;
        }


        public SingleModeCookMaterialRate Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeCookMaterialRate");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeCookMaterialRate", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeCookMaterialRate _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeCookMaterialRate();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookMaterialRate");
                return null;
            }

            // SELECT `boost_type`,`material_count`,`effect_type`,`material_quantity_rate` FROM `single_mode_cook_material_rate` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeCookMaterialRate orm = null;

            if (query.Step())
            {
                int boostType            = (int)query.GetInt(0);
                int materialCount        = (int)query.GetInt(1);
                int effectType           = (int)query.GetInt(2);
                int materialQuantityRate = (int)query.GetInt(3);

                orm = new SingleModeCookMaterialRate(id, boostType, materialCount, effectType, materialQuantityRate);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeCookMaterialRate GetWithMaterialCountAndBoostType(int materialCount, int boostType)
        {
            SingleModeCookMaterialRate orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithMaterialCountAndBoostType(materialCount, boostType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", materialCount, boostType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookMaterialRate _SelectWithMaterialCountAndBoostType(int materialCount, int boostType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookMaterialRate_MaterialCount_BoostType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookMaterialRate");
                return null;
            }

            // SELECT `id`,`effect_type`,`material_quantity_rate` FROM `single_mode_cook_material_rate` WHERE `material_count`=? AND `boost_type`=?;
            if (!query.BindInt(1, materialCount)) { return null; }
            if (!query.BindInt(2, boostType))     { return null; }

            SingleModeCookMaterialRate orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithMaterialCountAndBoostType(query, materialCount, boostType);
            }

            query.Reset();

            return orm;
        }

        private SingleModeCookMaterialRate _CreateOrmByQueryResultWithMaterialCountAndBoostType(LibNative.Sqlite3.PreparedQuery query, int materialCount, int boostType)
        {
            int id                   = (int)query.GetInt(0);
            int effectType           = (int)query.GetInt(1);
            int materialQuantityRate = (int)query.GetInt(2);

            return new SingleModeCookMaterialRate(id, boostType, materialCount, effectType, materialQuantityRate);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class SingleModeCookMaterialRate
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: boost_type) </summary>
            public readonly int BoostType;
            /// <summary> (CSV column: material_count) </summary>
            public readonly int MaterialCount;
            /// <summary> (CSV column: effect_type) </summary>
            public readonly int EffectType;
            /// <summary> (CSV column: material_quantity_rate) </summary>
            public readonly int MaterialQuantityRate;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeCookMaterialRate(int id = 0, int boostType = 0, int materialCount = 0, int effectType = 0, int materialQuantityRate = 0)
            {
                this.Id                   = id;
                this.BoostType            = boostType;
                this.MaterialCount        = materialCount;
                this.EffectType           = effectType;
                this.MaterialQuantityRate = materialQuantityRate;
            }
        }
    }
}
