// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_training_bg_chara
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scenario_id], [:command_id, :command_level]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeTrainingBgChara : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_training_bg_chara";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeTrainingBgChara> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SingleModeTrainingBgChara>> _dictionaryWithScenarioId = null;
        private Dictionary<ulong, List<SingleModeTrainingBgChara>> _dictionaryWithCommandIdAndCommandLevel = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeTrainingBgChara(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeTrainingBgChara>();
            _dictionaryWithScenarioId = new Dictionary<int, List<SingleModeTrainingBgChara>>();
            _dictionaryWithCommandIdAndCommandLevel = new Dictionary<ulong, List<SingleModeTrainingBgChara>>();
            _db = db;
        }


        public SingleModeTrainingBgChara Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeTrainingBgChara");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeTrainingBgChara", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeTrainingBgChara _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeTrainingBgChara();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeTrainingBgChara");
                return null;
            }

            // SELECT `scenario_id`,`command_id`,`command_level`,`cut_id` FROM `single_mode_training_bg_chara` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeTrainingBgChara orm = null;

            if (query.Step())
            {
                int scenarioId   = (int)query.GetInt(0);
                int commandId    = (int)query.GetInt(1);
                int commandLevel = (int)query.GetInt(2);
                int cutId        = (int)query.GetInt(3);

                orm = new SingleModeTrainingBgChara(id, scenarioId, commandId, commandLevel, cutId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeTrainingBgChara GetWithScenarioId(int scenarioId)
        {
            SingleModeTrainingBgChara orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithScenarioId(scenarioId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", scenarioId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeTrainingBgChara _SelectWithScenarioId(int scenarioId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeTrainingBgChara_ScenarioId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeTrainingBgChara");
                return null;
            }

            // SELECT `id`,`command_id`,`command_level`,`cut_id` FROM `single_mode_training_bg_chara` WHERE `scenario_id`=?;
            if (!query.BindInt(1, scenarioId)) { return null; }

            SingleModeTrainingBgChara orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithScenarioId(query, scenarioId);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeTrainingBgChara> GetListWithScenarioId(int scenarioId)
        {
            int key = (int)scenarioId;
            
            if (!_dictionaryWithScenarioId.ContainsKey(key)) {
                _dictionaryWithScenarioId.Add(key, _ListSelectWithScenarioId(scenarioId));
            }

            return _dictionaryWithScenarioId[key];
      
        }

        public List<SingleModeTrainingBgChara> MaybeListWithScenarioId(int scenarioId)
        {
            List<SingleModeTrainingBgChara> list = GetListWithScenarioId(scenarioId);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeTrainingBgChara> _ListSelectWithScenarioId(int scenarioId)
        {
            List<SingleModeTrainingBgChara> _list = new List<SingleModeTrainingBgChara>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeTrainingBgChara_ScenarioId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeTrainingBgChara");
                return null;
            }

            // SELECT `id`,`command_id`,`command_level`,`cut_id` FROM `single_mode_training_bg_chara` WHERE `scenario_id`=?;
            if (!query.BindInt(1, scenarioId)) { return null; }

            while (query.Step()) {
                SingleModeTrainingBgChara orm = _CreateOrmByQueryResultWithScenarioId(query, scenarioId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeTrainingBgChara _CreateOrmByQueryResultWithScenarioId(LibNative.Sqlite3.PreparedQuery query, int scenarioId)
        {
            int id           = (int)query.GetInt(0);
            int commandId    = (int)query.GetInt(1);
            int commandLevel = (int)query.GetInt(2);
            int cutId        = (int)query.GetInt(3);

            return new SingleModeTrainingBgChara(id, scenarioId, commandId, commandLevel, cutId);
        }

        public SingleModeTrainingBgChara GetWithCommandIdAndCommandLevel(int commandId, int commandLevel)
        {
            SingleModeTrainingBgChara orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCommandIdAndCommandLevel(commandId, commandLevel);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", commandId, commandLevel));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeTrainingBgChara _SelectWithCommandIdAndCommandLevel(int commandId, int commandLevel)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeTrainingBgChara_CommandId_CommandLevel();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeTrainingBgChara");
                return null;
            }

            // SELECT `id`,`scenario_id`,`cut_id` FROM `single_mode_training_bg_chara` WHERE `command_id`=? AND `command_level`=?;
            if (!query.BindInt(1, commandId))    { return null; }
            if (!query.BindInt(2, commandLevel)) { return null; }

            SingleModeTrainingBgChara orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCommandIdAndCommandLevel(query, commandId, commandLevel);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeTrainingBgChara> GetListWithCommandIdAndCommandLevel(int commandId, int commandLevel)
        {
            ulong key = ((uint)unchecked((ulong)((int)commandId))) | ((((ulong)unchecked((ulong)((int)commandLevel)))) << 32);
            
            if (!_dictionaryWithCommandIdAndCommandLevel.ContainsKey(key)) {
                _dictionaryWithCommandIdAndCommandLevel.Add(key, _ListSelectWithCommandIdAndCommandLevel(commandId, commandLevel));
            }

            return _dictionaryWithCommandIdAndCommandLevel[key];
      
        }

        public List<SingleModeTrainingBgChara> MaybeListWithCommandIdAndCommandLevel(int commandId, int commandLevel)
        {
            List<SingleModeTrainingBgChara> list = GetListWithCommandIdAndCommandLevel(commandId, commandLevel);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeTrainingBgChara> _ListSelectWithCommandIdAndCommandLevel(int commandId, int commandLevel)
        {
            List<SingleModeTrainingBgChara> _list = new List<SingleModeTrainingBgChara>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeTrainingBgChara_CommandId_CommandLevel();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeTrainingBgChara");
                return null;
            }

            // SELECT `id`,`scenario_id`,`cut_id` FROM `single_mode_training_bg_chara` WHERE `command_id`=? AND `command_level`=?;
            if (!query.BindInt(1, commandId))    { return null; }
            if (!query.BindInt(2, commandLevel)) { return null; }

            while (query.Step()) {
                SingleModeTrainingBgChara orm = _CreateOrmByQueryResultWithCommandIdAndCommandLevel(query, commandId, commandLevel);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeTrainingBgChara _CreateOrmByQueryResultWithCommandIdAndCommandLevel(LibNative.Sqlite3.PreparedQuery query, int commandId, int commandLevel)
        {
            int id         = (int)query.GetInt(0);
            int scenarioId = (int)query.GetInt(1);
            int cutId      = (int)query.GetInt(2);

            return new SingleModeTrainingBgChara(id, scenarioId, commandId, commandLevel, cutId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithScenarioId.Clear();
            _dictionaryWithCommandIdAndCommandLevel.Clear();
        }



        public sealed partial class SingleModeTrainingBgChara
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: command_id) </summary>
            public readonly int CommandId;
            /// <summary> (CSV column: command_level) </summary>
            public readonly int CommandLevel;
            /// <summary> (CSV column: cut_id) </summary>
            public readonly int CutId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeTrainingBgChara(int id = 0, int scenarioId = 0, int commandId = 0, int commandLevel = 0, int cutId = 0)
            {
                this.Id           = id;
                this.ScenarioId   = scenarioId;
                this.CommandId    = commandId;
                this.CommandLevel = commandLevel;
                this.CutId        = cutId;
            }
        }
    }
}
