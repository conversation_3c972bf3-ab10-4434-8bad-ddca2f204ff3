// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_cook_garden_level
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:facility_id], [:facility_id, :garden_lv]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeCookGardenLevel : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_cook_garden_level";

        MasterSingleModeDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeCookGardenLevel> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SingleModeCookGardenLevel>> _dictionaryWithFacilityId = null;
        private Dictionary<ulong, List<SingleModeCookGardenLevel>> _dictionaryWithFacilityIdAndGardenLv = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SingleModeCookGardenLevel> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSingleModeCookGardenLevel");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeCookGardenLevel(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeCookGardenLevel>();
            _dictionaryWithFacilityId = new Dictionary<int, List<SingleModeCookGardenLevel>>();
            _dictionaryWithFacilityIdAndGardenLv = new Dictionary<ulong, List<SingleModeCookGardenLevel>>();
            _db = db;
        }


        public SingleModeCookGardenLevel Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeCookGardenLevel");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeCookGardenLevel", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeCookGardenLevel _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeCookGardenLevel();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookGardenLevel");
                return null;
            }

            // SELECT `facility_id`,`garden_lv`,`facility_lv`,`effect_group_id`,`coin_num` FROM `single_mode_cook_garden_level` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeCookGardenLevel orm = null;

            if (query.Step())
            {
                int facilityId    = (int)query.GetInt(0);
                int gardenLv      = (int)query.GetInt(1);
                int facilityLv    = (int)query.GetInt(2);
                int effectGroupId = (int)query.GetInt(3);
                int coinNum       = (int)query.GetInt(4);

                orm = new SingleModeCookGardenLevel(id, facilityId, gardenLv, facilityLv, effectGroupId, coinNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeCookGardenLevel GetWithFacilityIdOrderByFacilityLvAsc(int facilityId)
        {
            SingleModeCookGardenLevel orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithFacilityIdOrderByFacilityLvAsc(facilityId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", facilityId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookGardenLevel _SelectWithFacilityIdOrderByFacilityLvAsc(int facilityId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookGardenLevel_FacilityId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookGardenLevel");
                return null;
            }

            // SELECT `id`,`garden_lv`,`facility_lv`,`effect_group_id`,`coin_num` FROM `single_mode_cook_garden_level` WHERE `facility_id`=? ORDER BY `facility_lv` ASC;
            if (!query.BindInt(1, facilityId)) { return null; }

            SingleModeCookGardenLevel orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithFacilityIdOrderByFacilityLvAsc(query, facilityId);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeCookGardenLevel> GetListWithFacilityIdOrderByFacilityLvAsc(int facilityId)
        {
            int key = (int)facilityId;
            
            if (!_dictionaryWithFacilityId.ContainsKey(key)) {
                _dictionaryWithFacilityId.Add(key, _ListSelectWithFacilityIdOrderByFacilityLvAsc(facilityId));
            }

            return _dictionaryWithFacilityId[key];
      
        }

        public List<SingleModeCookGardenLevel> MaybeListWithFacilityIdOrderByFacilityLvAsc(int facilityId)
        {
            List<SingleModeCookGardenLevel> list = GetListWithFacilityIdOrderByFacilityLvAsc(facilityId);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeCookGardenLevel> _ListSelectWithFacilityIdOrderByFacilityLvAsc(int facilityId)
        {
            List<SingleModeCookGardenLevel> _list = new List<SingleModeCookGardenLevel>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookGardenLevel_FacilityId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookGardenLevel");
                return null;
            }

            // SELECT `id`,`garden_lv`,`facility_lv`,`effect_group_id`,`coin_num` FROM `single_mode_cook_garden_level` WHERE `facility_id`=? ORDER BY `facility_lv` ASC;
            if (!query.BindInt(1, facilityId)) { return null; }

            while (query.Step()) {
                SingleModeCookGardenLevel orm = _CreateOrmByQueryResultWithFacilityIdOrderByFacilityLvAsc(query, facilityId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeCookGardenLevel _CreateOrmByQueryResultWithFacilityIdOrderByFacilityLvAsc(LibNative.Sqlite3.PreparedQuery query, int facilityId)
        {
            int id            = (int)query.GetInt(0);
            int gardenLv      = (int)query.GetInt(1);
            int facilityLv    = (int)query.GetInt(2);
            int effectGroupId = (int)query.GetInt(3);
            int coinNum       = (int)query.GetInt(4);

            return new SingleModeCookGardenLevel(id, facilityId, gardenLv, facilityLv, effectGroupId, coinNum);
        }

        public SingleModeCookGardenLevel GetWithGardenLv(int gardenLv)
        {
            SingleModeCookGardenLevel orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGardenLv(gardenLv);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", gardenLv));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookGardenLevel _SelectWithGardenLv(int gardenLv)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookGardenLevel_GardenLv();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookGardenLevel");
                return null;
            }

            // SELECT `id`,`facility_id`,`facility_lv`,`effect_group_id`,`coin_num` FROM `single_mode_cook_garden_level` WHERE `garden_lv`=?;
            if (!query.BindInt(1, gardenLv)) { return null; }

            SingleModeCookGardenLevel orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGardenLv(query, gardenLv);
            }

            query.Reset();

            return orm;
        }

        private SingleModeCookGardenLevel _CreateOrmByQueryResultWithGardenLv(LibNative.Sqlite3.PreparedQuery query, int gardenLv)
        {
            int id            = (int)query.GetInt(0);
            int facilityId    = (int)query.GetInt(1);
            int facilityLv    = (int)query.GetInt(2);
            int effectGroupId = (int)query.GetInt(3);
            int coinNum       = (int)query.GetInt(4);

            return new SingleModeCookGardenLevel(id, facilityId, gardenLv, facilityLv, effectGroupId, coinNum);
        }

        public SingleModeCookGardenLevel GetWithFacilityIdAndGardenLv(int facilityId, int gardenLv)
        {
            SingleModeCookGardenLevel orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithFacilityIdAndGardenLv(facilityId, gardenLv);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", facilityId, gardenLv));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookGardenLevel _SelectWithFacilityIdAndGardenLv(int facilityId, int gardenLv)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookGardenLevel_FacilityId_GardenLv();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookGardenLevel");
                return null;
            }

            // SELECT `id`,`facility_lv`,`effect_group_id`,`coin_num` FROM `single_mode_cook_garden_level` WHERE `facility_id`=? AND `garden_lv`=?;
            if (!query.BindInt(1, facilityId)) { return null; }
            if (!query.BindInt(2, gardenLv))   { return null; }

            SingleModeCookGardenLevel orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithFacilityIdAndGardenLv(query, facilityId, gardenLv);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeCookGardenLevel> GetListWithFacilityIdAndGardenLv(int facilityId, int gardenLv)
        {
            ulong key = ((uint)unchecked((ulong)((int)facilityId))) | ((((ulong)unchecked((ulong)((int)gardenLv)))) << 32);
            
            if (!_dictionaryWithFacilityIdAndGardenLv.ContainsKey(key)) {
                _dictionaryWithFacilityIdAndGardenLv.Add(key, _ListSelectWithFacilityIdAndGardenLv(facilityId, gardenLv));
            }

            return _dictionaryWithFacilityIdAndGardenLv[key];
      
        }

        public List<SingleModeCookGardenLevel> MaybeListWithFacilityIdAndGardenLv(int facilityId, int gardenLv)
        {
            List<SingleModeCookGardenLevel> list = GetListWithFacilityIdAndGardenLv(facilityId, gardenLv);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeCookGardenLevel> _ListSelectWithFacilityIdAndGardenLv(int facilityId, int gardenLv)
        {
            List<SingleModeCookGardenLevel> _list = new List<SingleModeCookGardenLevel>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookGardenLevel_FacilityId_GardenLv();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookGardenLevel");
                return null;
            }

            // SELECT `id`,`facility_lv`,`effect_group_id`,`coin_num` FROM `single_mode_cook_garden_level` WHERE `facility_id`=? AND `garden_lv`=?;
            if (!query.BindInt(1, facilityId)) { return null; }
            if (!query.BindInt(2, gardenLv))   { return null; }

            while (query.Step()) {
                SingleModeCookGardenLevel orm = _CreateOrmByQueryResultWithFacilityIdAndGardenLv(query, facilityId, gardenLv);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeCookGardenLevel _CreateOrmByQueryResultWithFacilityIdAndGardenLv(LibNative.Sqlite3.PreparedQuery query, int facilityId, int gardenLv)
        {
            int id            = (int)query.GetInt(0);
            int facilityLv    = (int)query.GetInt(1);
            int effectGroupId = (int)query.GetInt(2);
            int coinNum       = (int)query.GetInt(3);

            return new SingleModeCookGardenLevel(id, facilityId, gardenLv, facilityLv, effectGroupId, coinNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithFacilityId.Clear();
            _dictionaryWithFacilityIdAndGardenLv.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SingleModeCookGardenLevel()) {
                while (query.Step()) {
                    int id            = (int)query.GetInt(0);
                    int facilityId    = (int)query.GetInt(1);
                    int gardenLv      = (int)query.GetInt(2);
                    int facilityLv    = (int)query.GetInt(3);
                    int effectGroupId = (int)query.GetInt(4);
                    int coinNum       = (int)query.GetInt(5);

                    int key = (int)id;
                    SingleModeCookGardenLevel orm = new SingleModeCookGardenLevel(id, facilityId, gardenLv, facilityLv, effectGroupId, coinNum);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class SingleModeCookGardenLevel
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: facility_id) </summary>
            public readonly int FacilityId;
            /// <summary> (CSV column: garden_lv) </summary>
            public readonly int GardenLv;
            /// <summary> (CSV column: facility_lv) </summary>
            public readonly int FacilityLv;
            /// <summary> (CSV column: effect_group_id) </summary>
            public readonly int EffectGroupId;
            /// <summary> (CSV column: coin_num) </summary>
            public readonly int CoinNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeCookGardenLevel(int id = 0, int facilityId = 0, int gardenLv = 0, int facilityLv = 0, int effectGroupId = 0, int coinNum = 0)
            {
                this.Id            = id;
                this.FacilityId    = facilityId;
                this.GardenLv      = gardenLv;
                this.FacilityLv    = facilityLv;
                this.EffectGroupId = effectGroupId;
                this.CoinNum       = coinNum;
            }
        }
    }
}
