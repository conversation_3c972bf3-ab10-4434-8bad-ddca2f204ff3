// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/succession_factor_effect
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:factor_group_id], [:factor_group_id, :effect_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSuccessionFactorEffect : AbstractMasterData
    {
        public const string TABLE_NAME = "succession_factor_effect";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SuccessionFactorEffect> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SuccessionFactorEffect>> _dictionaryWithFactorGroupId = null;
        private Dictionary<ulong, List<SuccessionFactorEffect>> _dictionaryWithFactorGroupIdAndEffectId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSuccessionFactorEffect(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SuccessionFactorEffect>();
            _dictionaryWithFactorGroupId = new Dictionary<int, List<SuccessionFactorEffect>>();
            _dictionaryWithFactorGroupIdAndEffectId = new Dictionary<ulong, List<SuccessionFactorEffect>>();
            _db = db;
        }


        public SuccessionFactorEffect Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSuccessionFactorEffect");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSuccessionFactorEffect", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SuccessionFactorEffect _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SuccessionFactorEffect();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SuccessionFactorEffect");
                return null;
            }

            // SELECT `factor_group_id`,`effect_id`,`target_type`,`value_1`,`value_2` FROM `succession_factor_effect` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SuccessionFactorEffect orm = null;

            if (query.Step())
            {
                int factorGroupId = (int)query.GetInt(0);
                int effectId      = (int)query.GetInt(1);
                int targetType    = (int)query.GetInt(2);
                int value1        = (int)query.GetInt(3);
                int value2        = (int)query.GetInt(4);

                orm = new SuccessionFactorEffect(id, factorGroupId, effectId, targetType, value1, value2);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SuccessionFactorEffect GetWithFactorGroupId(int factorGroupId)
        {
            SuccessionFactorEffect orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithFactorGroupId(factorGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", factorGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SuccessionFactorEffect _SelectWithFactorGroupId(int factorGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SuccessionFactorEffect_FactorGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SuccessionFactorEffect");
                return null;
            }

            // SELECT `id`,`effect_id`,`target_type`,`value_1`,`value_2` FROM `succession_factor_effect` WHERE `factor_group_id`=?;
            if (!query.BindInt(1, factorGroupId)) { return null; }

            SuccessionFactorEffect orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithFactorGroupId(query, factorGroupId);
            }

            query.Reset();

            return orm;
        }

        public List<SuccessionFactorEffect> GetListWithFactorGroupId(int factorGroupId)
        {
            int key = (int)factorGroupId;
            
            if (!_dictionaryWithFactorGroupId.ContainsKey(key)) {
                _dictionaryWithFactorGroupId.Add(key, _ListSelectWithFactorGroupId(factorGroupId));
            }

            return _dictionaryWithFactorGroupId[key];
      
        }

        public List<SuccessionFactorEffect> MaybeListWithFactorGroupId(int factorGroupId)
        {
            List<SuccessionFactorEffect> list = GetListWithFactorGroupId(factorGroupId);
            return list.Count > 0 ? list : null;
        }

        private List<SuccessionFactorEffect> _ListSelectWithFactorGroupId(int factorGroupId)
        {
            List<SuccessionFactorEffect> _list = new List<SuccessionFactorEffect>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SuccessionFactorEffect_FactorGroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SuccessionFactorEffect");
                return null;
            }

            // SELECT `id`,`effect_id`,`target_type`,`value_1`,`value_2` FROM `succession_factor_effect` WHERE `factor_group_id`=?;
            if (!query.BindInt(1, factorGroupId)) { return null; }

            while (query.Step()) {
                SuccessionFactorEffect orm = _CreateOrmByQueryResultWithFactorGroupId(query, factorGroupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SuccessionFactorEffect _CreateOrmByQueryResultWithFactorGroupId(LibNative.Sqlite3.PreparedQuery query, int factorGroupId)
        {
            int id         = (int)query.GetInt(0);
            int effectId   = (int)query.GetInt(1);
            int targetType = (int)query.GetInt(2);
            int value1     = (int)query.GetInt(3);
            int value2     = (int)query.GetInt(4);

            return new SuccessionFactorEffect(id, factorGroupId, effectId, targetType, value1, value2);
        }

        public SuccessionFactorEffect GetWithFactorGroupIdAndEffectId(int factorGroupId, int effectId)
        {
            SuccessionFactorEffect orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithFactorGroupIdAndEffectId(factorGroupId, effectId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", factorGroupId, effectId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SuccessionFactorEffect _SelectWithFactorGroupIdAndEffectId(int factorGroupId, int effectId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SuccessionFactorEffect_FactorGroupId_EffectId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SuccessionFactorEffect");
                return null;
            }

            // SELECT `id`,`target_type`,`value_1`,`value_2` FROM `succession_factor_effect` WHERE `factor_group_id`=? AND `effect_id`=?;
            if (!query.BindInt(1, factorGroupId)) { return null; }
            if (!query.BindInt(2, effectId))      { return null; }

            SuccessionFactorEffect orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithFactorGroupIdAndEffectId(query, factorGroupId, effectId);
            }

            query.Reset();

            return orm;
        }

        public List<SuccessionFactorEffect> GetListWithFactorGroupIdAndEffectId(int factorGroupId, int effectId)
        {
            ulong key = ((uint)unchecked((ulong)((int)factorGroupId))) | ((((ulong)unchecked((ulong)((int)effectId)))) << 32);
            
            if (!_dictionaryWithFactorGroupIdAndEffectId.ContainsKey(key)) {
                _dictionaryWithFactorGroupIdAndEffectId.Add(key, _ListSelectWithFactorGroupIdAndEffectId(factorGroupId, effectId));
            }

            return _dictionaryWithFactorGroupIdAndEffectId[key];
      
        }

        public List<SuccessionFactorEffect> MaybeListWithFactorGroupIdAndEffectId(int factorGroupId, int effectId)
        {
            List<SuccessionFactorEffect> list = GetListWithFactorGroupIdAndEffectId(factorGroupId, effectId);
            return list.Count > 0 ? list : null;
        }

        private List<SuccessionFactorEffect> _ListSelectWithFactorGroupIdAndEffectId(int factorGroupId, int effectId)
        {
            List<SuccessionFactorEffect> _list = new List<SuccessionFactorEffect>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SuccessionFactorEffect_FactorGroupId_EffectId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SuccessionFactorEffect");
                return null;
            }

            // SELECT `id`,`target_type`,`value_1`,`value_2` FROM `succession_factor_effect` WHERE `factor_group_id`=? AND `effect_id`=?;
            if (!query.BindInt(1, factorGroupId)) { return null; }
            if (!query.BindInt(2, effectId))      { return null; }

            while (query.Step()) {
                SuccessionFactorEffect orm = _CreateOrmByQueryResultWithFactorGroupIdAndEffectId(query, factorGroupId, effectId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SuccessionFactorEffect _CreateOrmByQueryResultWithFactorGroupIdAndEffectId(LibNative.Sqlite3.PreparedQuery query, int factorGroupId, int effectId)
        {
            int id         = (int)query.GetInt(0);
            int targetType = (int)query.GetInt(1);
            int value1     = (int)query.GetInt(2);
            int value2     = (int)query.GetInt(3);

            return new SuccessionFactorEffect(id, factorGroupId, effectId, targetType, value1, value2);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithFactorGroupId.Clear();
            _dictionaryWithFactorGroupIdAndEffectId.Clear();
        }



        public sealed partial class SuccessionFactorEffect
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: factor_group_id) </summary>
            public readonly int FactorGroupId;
            /// <summary> (CSV column: effect_id) </summary>
            public readonly int EffectId;
            /// <summary> (CSV column: target_type) </summary>
            public readonly int TargetType;
            /// <summary> (CSV column: value_1) </summary>
            public readonly int Value1;
            /// <summary> (CSV column: value_2) </summary>
            public readonly int Value2;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SuccessionFactorEffect(int id = 0, int factorGroupId = 0, int effectId = 0, int targetType = 0, int value1 = 0, int value2 = 0)
            {
                this.Id            = id;
                this.FactorGroupId = factorGroupId;
                this.EffectId      = effectId;
                this.TargetType    = targetType;
                this.Value1        = value1;
                this.Value2        = value2;
            }
        }
    }
}
