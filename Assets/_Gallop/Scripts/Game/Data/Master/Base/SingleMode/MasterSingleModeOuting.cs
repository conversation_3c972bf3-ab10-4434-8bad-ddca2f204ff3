// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_outing
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:command_group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeOuting : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_outing";

        MasterSingleModeDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeOuting> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SingleModeOuting>> _dictionaryWithCommandGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SingleModeOuting> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSingleModeOuting");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeOuting(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeOuting>();
            _dictionaryWithCommandGroupId = new Dictionary<int, List<SingleModeOuting>>();
            _db = db;
        }


        public SingleModeOuting Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeOuting");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeOuting", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeOuting _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeOuting();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeOuting");
                return null;
            }

            // SELECT `command_group_id`,`condition`,`is_play_cutt` FROM `single_mode_outing` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeOuting orm = null;

            if (query.Step())
            {
                int commandGroupId = (int)query.GetInt(0);
                int condition      = (int)query.GetInt(1);
                int isPlayCutt     = (int)query.GetInt(2);

                orm = new SingleModeOuting(id, commandGroupId, condition, isPlayCutt);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeOuting GetWithCommandGroupIdOrderByIdAsc(int commandGroupId)
        {
            SingleModeOuting orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCommandGroupIdOrderByIdAsc(commandGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", commandGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeOuting _SelectWithCommandGroupIdOrderByIdAsc(int commandGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeOuting_CommandGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeOuting");
                return null;
            }

            // SELECT `id`,`condition`,`is_play_cutt` FROM `single_mode_outing` WHERE `command_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, commandGroupId)) { return null; }

            SingleModeOuting orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCommandGroupIdOrderByIdAsc(query, commandGroupId);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeOuting> GetListWithCommandGroupIdOrderByIdAsc(int commandGroupId)
        {
            int key = (int)commandGroupId;
            
            if (!_dictionaryWithCommandGroupId.ContainsKey(key)) {
                _dictionaryWithCommandGroupId.Add(key, _ListSelectWithCommandGroupIdOrderByIdAsc(commandGroupId));
            }

            return _dictionaryWithCommandGroupId[key];
      
        }

        public List<SingleModeOuting> MaybeListWithCommandGroupIdOrderByIdAsc(int commandGroupId)
        {
            List<SingleModeOuting> list = GetListWithCommandGroupIdOrderByIdAsc(commandGroupId);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeOuting> _ListSelectWithCommandGroupIdOrderByIdAsc(int commandGroupId)
        {
            List<SingleModeOuting> _list = new List<SingleModeOuting>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeOuting_CommandGroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeOuting");
                return null;
            }

            // SELECT `id`,`condition`,`is_play_cutt` FROM `single_mode_outing` WHERE `command_group_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, commandGroupId)) { return null; }

            while (query.Step()) {
                SingleModeOuting orm = _CreateOrmByQueryResultWithCommandGroupIdOrderByIdAsc(query, commandGroupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeOuting _CreateOrmByQueryResultWithCommandGroupIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int commandGroupId)
        {
            int id         = (int)query.GetInt(0);
            int condition  = (int)query.GetInt(1);
            int isPlayCutt = (int)query.GetInt(2);

            return new SingleModeOuting(id, commandGroupId, condition, isPlayCutt);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCommandGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SingleModeOuting()) {
                while (query.Step()) {
                    int id             = (int)query.GetInt(0);
                    int commandGroupId = (int)query.GetInt(1);
                    int condition      = (int)query.GetInt(2);
                    int isPlayCutt     = (int)query.GetInt(3);

                    int key = (int)id;
                    SingleModeOuting orm = new SingleModeOuting(id, commandGroupId, condition, isPlayCutt);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class SingleModeOuting
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: command_group_id) </summary>
            public readonly int CommandGroupId;
            /// <summary> (CSV column: condition) </summary>
            public readonly int Condition;
            /// <summary> (CSV column: is_play_cutt) </summary>
            public readonly int IsPlayCutt;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeOuting(int id = 0, int commandGroupId = 0, int condition = 0, int isPlayCutt = 0)
            {
                this.Id             = id;
                this.CommandGroupId = commandGroupId;
                this.Condition      = condition;
                this.IsPlayCutt     = isPlayCutt;
            }
        }
    }
}
