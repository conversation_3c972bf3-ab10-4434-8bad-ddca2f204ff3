// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/omakase_state
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scenario_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterOmakaseState : AbstractMasterData
    {
        public const string TABLE_NAME = "omakase_state";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, OmakaseState> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<OmakaseState>> _dictionaryWithScenarioId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterOmakaseState(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, OmakaseState>();
            _dictionaryWithScenarioId = new Dictionary<int, List<OmakaseState>>();
            _db = db;
        }


        public OmakaseState Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterOmakaseState");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterOmakaseState", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private OmakaseState _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_OmakaseState();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseState");
                return null;
            }

            // SELECT `scenario_id`,`state_id`,`setting_id` FROM `omakase_state` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            OmakaseState orm = null;

            if (query.Step())
            {
                int scenarioId = (int)query.GetInt(0);
                int stateId    = (int)query.GetInt(1);
                int settingId  = (int)query.GetInt(2);

                orm = new OmakaseState(id, scenarioId, stateId, settingId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public OmakaseState GetWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            OmakaseState orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithScenarioIdOrderByIdAsc(scenarioId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", scenarioId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private OmakaseState _SelectWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_OmakaseState_ScenarioId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseState");
                return null;
            }

            // SELECT `id`,`state_id`,`setting_id` FROM `omakase_state` WHERE `scenario_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            OmakaseState orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(query, scenarioId);
            }

            query.Reset();

            return orm;
        }

        public List<OmakaseState> GetListWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            int key = (int)scenarioId;
            
            if (!_dictionaryWithScenarioId.ContainsKey(key)) {
                _dictionaryWithScenarioId.Add(key, _ListSelectWithScenarioIdOrderByIdAsc(scenarioId));
            }

            return _dictionaryWithScenarioId[key];
      
        }

        public List<OmakaseState> MaybeListWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            List<OmakaseState> list = GetListWithScenarioIdOrderByIdAsc(scenarioId);
            return list.Count > 0 ? list : null;
        }

        private List<OmakaseState> _ListSelectWithScenarioIdOrderByIdAsc(int scenarioId)
        {
            List<OmakaseState> _list = new List<OmakaseState>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_OmakaseState_ScenarioId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseState");
                return null;
            }

            // SELECT `id`,`state_id`,`setting_id` FROM `omakase_state` WHERE `scenario_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, scenarioId)) { return null; }

            while (query.Step()) {
                OmakaseState orm = _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(query, scenarioId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private OmakaseState _CreateOrmByQueryResultWithScenarioIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int scenarioId)
        {
            int id        = (int)query.GetInt(0);
            int stateId   = (int)query.GetInt(1);
            int settingId = (int)query.GetInt(2);

            return new OmakaseState(id, scenarioId, stateId, settingId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithScenarioId.Clear();
        }



        public sealed partial class OmakaseState
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: state_id) </summary>
            public readonly int StateId;
            /// <summary> (CSV column: setting_id) </summary>
            public readonly int SettingId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public OmakaseState(int id = 0, int scenarioId = 0, int stateId = 0, int settingId = 0)
            {
                this.Id         = id;
                this.ScenarioId = scenarioId;
                this.StateId    = stateId;
                this.SettingId  = settingId;
            }
        }
    }
}
