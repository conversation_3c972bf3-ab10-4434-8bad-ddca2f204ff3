// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_story_root
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:story_id], [:condition_story_id_1], [:condition_story_id_2], [:condition_story_id_3]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeStoryRoot : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_story_root";

        MasterSingleModeDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeStoryRoot> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SingleModeStoryRoot>> _dictionaryWithStoryId = null;
        private Dictionary<int, List<SingleModeStoryRoot>> _dictionaryWithConditionStoryId1 = null;
        private Dictionary<int, List<SingleModeStoryRoot>> _dictionaryWithConditionStoryId2 = null;
        private Dictionary<int, List<SingleModeStoryRoot>> _dictionaryWithConditionStoryId3 = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, SingleModeStoryRoot> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterSingleModeStoryRoot");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeStoryRoot(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeStoryRoot>();
            _dictionaryWithStoryId = new Dictionary<int, List<SingleModeStoryRoot>>();
            _dictionaryWithConditionStoryId1 = new Dictionary<int, List<SingleModeStoryRoot>>();
            _dictionaryWithConditionStoryId2 = new Dictionary<int, List<SingleModeStoryRoot>>();
            _dictionaryWithConditionStoryId3 = new Dictionary<int, List<SingleModeStoryRoot>>();
            _db = db;
        }


        public SingleModeStoryRoot Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeStoryRoot");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeStoryRoot", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeStoryRoot _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeStoryRoot();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `story_id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeStoryRoot orm = null;

            if (query.Step())
            {
                int storyId           = (int)query.GetInt(0);
                int suggestSelect     = (int)query.GetInt(1);
                int suggestIndex      = (int)query.GetInt(2);
                int conditionType     = (int)query.GetInt(3);
                int conditionStoryId1 = (int)query.GetInt(4);
                int gainSelect1       = (int)query.GetInt(5);
                int selectIndex1      = (int)query.GetInt(6);
                int conditionStoryId2 = (int)query.GetInt(7);
                int gainSelect2       = (int)query.GetInt(8);
                int selectIndex2      = (int)query.GetInt(9);
                int conditionStoryId3 = (int)query.GetInt(10);
                int gainSelect3       = (int)query.GetInt(11);
                int selectIndex3      = (int)query.GetInt(12);

                orm = new SingleModeStoryRoot(id, storyId, suggestSelect, suggestIndex, conditionType, conditionStoryId1, gainSelect1, selectIndex1, conditionStoryId2, gainSelect2, selectIndex2, conditionStoryId3, gainSelect3, selectIndex3);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeStoryRoot GetWithStoryId(int storyId)
        {
            SingleModeStoryRoot orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithStoryId(storyId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", storyId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeStoryRoot _SelectWithStoryId(int storyId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_StoryId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `story_id`=?;
            if (!query.BindInt(1, storyId)) { return null; }

            SingleModeStoryRoot orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithStoryId(query, storyId);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeStoryRoot> GetListWithStoryId(int storyId)
        {
            int key = (int)storyId;
            
            if (!_dictionaryWithStoryId.ContainsKey(key)) {
                _dictionaryWithStoryId.Add(key, _ListSelectWithStoryId(storyId));
            }

            return _dictionaryWithStoryId[key];
      
        }

        public List<SingleModeStoryRoot> MaybeListWithStoryId(int storyId)
        {
            List<SingleModeStoryRoot> list = GetListWithStoryId(storyId);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeStoryRoot> _ListSelectWithStoryId(int storyId)
        {
            List<SingleModeStoryRoot> _list = new List<SingleModeStoryRoot>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_StoryId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `story_id`=?;
            if (!query.BindInt(1, storyId)) { return null; }

            while (query.Step()) {
                SingleModeStoryRoot orm = _CreateOrmByQueryResultWithStoryId(query, storyId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeStoryRoot _CreateOrmByQueryResultWithStoryId(LibNative.Sqlite3.PreparedQuery query, int storyId)
        {
            int id                = (int)query.GetInt(0);
            int suggestSelect     = (int)query.GetInt(1);
            int suggestIndex      = (int)query.GetInt(2);
            int conditionType     = (int)query.GetInt(3);
            int conditionStoryId1 = (int)query.GetInt(4);
            int gainSelect1       = (int)query.GetInt(5);
            int selectIndex1      = (int)query.GetInt(6);
            int conditionStoryId2 = (int)query.GetInt(7);
            int gainSelect2       = (int)query.GetInt(8);
            int selectIndex2      = (int)query.GetInt(9);
            int conditionStoryId3 = (int)query.GetInt(10);
            int gainSelect3       = (int)query.GetInt(11);
            int selectIndex3      = (int)query.GetInt(12);

            return new SingleModeStoryRoot(id, storyId, suggestSelect, suggestIndex, conditionType, conditionStoryId1, gainSelect1, selectIndex1, conditionStoryId2, gainSelect2, selectIndex2, conditionStoryId3, gainSelect3, selectIndex3);
        }

        public SingleModeStoryRoot GetWithConditionStoryId1(int conditionStoryId1)
        {
            SingleModeStoryRoot orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionStoryId1(conditionStoryId1);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionStoryId1));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeStoryRoot _SelectWithConditionStoryId1(int conditionStoryId1)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_ConditionStoryId1();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`story_id`,`suggest_select`,`suggest_index`,`condition_type`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `condition_story_id_1`=?;
            if (!query.BindInt(1, conditionStoryId1)) { return null; }

            SingleModeStoryRoot orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionStoryId1(query, conditionStoryId1);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeStoryRoot> GetListWithConditionStoryId1(int conditionStoryId1)
        {
            int key = (int)conditionStoryId1;
            
            if (!_dictionaryWithConditionStoryId1.ContainsKey(key)) {
                _dictionaryWithConditionStoryId1.Add(key, _ListSelectWithConditionStoryId1(conditionStoryId1));
            }

            return _dictionaryWithConditionStoryId1[key];
      
        }

        public List<SingleModeStoryRoot> MaybeListWithConditionStoryId1(int conditionStoryId1)
        {
            List<SingleModeStoryRoot> list = GetListWithConditionStoryId1(conditionStoryId1);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeStoryRoot> _ListSelectWithConditionStoryId1(int conditionStoryId1)
        {
            List<SingleModeStoryRoot> _list = new List<SingleModeStoryRoot>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_ConditionStoryId1();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`story_id`,`suggest_select`,`suggest_index`,`condition_type`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `condition_story_id_1`=?;
            if (!query.BindInt(1, conditionStoryId1)) { return null; }

            while (query.Step()) {
                SingleModeStoryRoot orm = _CreateOrmByQueryResultWithConditionStoryId1(query, conditionStoryId1);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeStoryRoot _CreateOrmByQueryResultWithConditionStoryId1(LibNative.Sqlite3.PreparedQuery query, int conditionStoryId1)
        {
            int id                = (int)query.GetInt(0);
            int storyId           = (int)query.GetInt(1);
            int suggestSelect     = (int)query.GetInt(2);
            int suggestIndex      = (int)query.GetInt(3);
            int conditionType     = (int)query.GetInt(4);
            int gainSelect1       = (int)query.GetInt(5);
            int selectIndex1      = (int)query.GetInt(6);
            int conditionStoryId2 = (int)query.GetInt(7);
            int gainSelect2       = (int)query.GetInt(8);
            int selectIndex2      = (int)query.GetInt(9);
            int conditionStoryId3 = (int)query.GetInt(10);
            int gainSelect3       = (int)query.GetInt(11);
            int selectIndex3      = (int)query.GetInt(12);

            return new SingleModeStoryRoot(id, storyId, suggestSelect, suggestIndex, conditionType, conditionStoryId1, gainSelect1, selectIndex1, conditionStoryId2, gainSelect2, selectIndex2, conditionStoryId3, gainSelect3, selectIndex3);
        }

        public SingleModeStoryRoot GetWithConditionStoryId2(int conditionStoryId2)
        {
            SingleModeStoryRoot orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionStoryId2(conditionStoryId2);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionStoryId2));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeStoryRoot _SelectWithConditionStoryId2(int conditionStoryId2)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_ConditionStoryId2();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`story_id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `condition_story_id_2`=?;
            if (!query.BindInt(1, conditionStoryId2)) { return null; }

            SingleModeStoryRoot orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionStoryId2(query, conditionStoryId2);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeStoryRoot> GetListWithConditionStoryId2(int conditionStoryId2)
        {
            int key = (int)conditionStoryId2;
            
            if (!_dictionaryWithConditionStoryId2.ContainsKey(key)) {
                _dictionaryWithConditionStoryId2.Add(key, _ListSelectWithConditionStoryId2(conditionStoryId2));
            }

            return _dictionaryWithConditionStoryId2[key];
      
        }

        public List<SingleModeStoryRoot> MaybeListWithConditionStoryId2(int conditionStoryId2)
        {
            List<SingleModeStoryRoot> list = GetListWithConditionStoryId2(conditionStoryId2);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeStoryRoot> _ListSelectWithConditionStoryId2(int conditionStoryId2)
        {
            List<SingleModeStoryRoot> _list = new List<SingleModeStoryRoot>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_ConditionStoryId2();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`story_id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`gain_select_2`,`select_index_2`,`condition_story_id_3`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `condition_story_id_2`=?;
            if (!query.BindInt(1, conditionStoryId2)) { return null; }

            while (query.Step()) {
                SingleModeStoryRoot orm = _CreateOrmByQueryResultWithConditionStoryId2(query, conditionStoryId2);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeStoryRoot _CreateOrmByQueryResultWithConditionStoryId2(LibNative.Sqlite3.PreparedQuery query, int conditionStoryId2)
        {
            int id                = (int)query.GetInt(0);
            int storyId           = (int)query.GetInt(1);
            int suggestSelect     = (int)query.GetInt(2);
            int suggestIndex      = (int)query.GetInt(3);
            int conditionType     = (int)query.GetInt(4);
            int conditionStoryId1 = (int)query.GetInt(5);
            int gainSelect1       = (int)query.GetInt(6);
            int selectIndex1      = (int)query.GetInt(7);
            int gainSelect2       = (int)query.GetInt(8);
            int selectIndex2      = (int)query.GetInt(9);
            int conditionStoryId3 = (int)query.GetInt(10);
            int gainSelect3       = (int)query.GetInt(11);
            int selectIndex3      = (int)query.GetInt(12);

            return new SingleModeStoryRoot(id, storyId, suggestSelect, suggestIndex, conditionType, conditionStoryId1, gainSelect1, selectIndex1, conditionStoryId2, gainSelect2, selectIndex2, conditionStoryId3, gainSelect3, selectIndex3);
        }

        public SingleModeStoryRoot GetWithConditionStoryId3(int conditionStoryId3)
        {
            SingleModeStoryRoot orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithConditionStoryId3(conditionStoryId3);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", conditionStoryId3));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeStoryRoot _SelectWithConditionStoryId3(int conditionStoryId3)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_ConditionStoryId3();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`story_id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `condition_story_id_3`=?;
            if (!query.BindInt(1, conditionStoryId3)) { return null; }

            SingleModeStoryRoot orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithConditionStoryId3(query, conditionStoryId3);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeStoryRoot> GetListWithConditionStoryId3(int conditionStoryId3)
        {
            int key = (int)conditionStoryId3;
            
            if (!_dictionaryWithConditionStoryId3.ContainsKey(key)) {
                _dictionaryWithConditionStoryId3.Add(key, _ListSelectWithConditionStoryId3(conditionStoryId3));
            }

            return _dictionaryWithConditionStoryId3[key];
      
        }

        public List<SingleModeStoryRoot> MaybeListWithConditionStoryId3(int conditionStoryId3)
        {
            List<SingleModeStoryRoot> list = GetListWithConditionStoryId3(conditionStoryId3);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeStoryRoot> _ListSelectWithConditionStoryId3(int conditionStoryId3)
        {
            List<SingleModeStoryRoot> _list = new List<SingleModeStoryRoot>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeStoryRoot_ConditionStoryId3();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeStoryRoot");
                return null;
            }

            // SELECT `id`,`story_id`,`suggest_select`,`suggest_index`,`condition_type`,`condition_story_id_1`,`gain_select_1`,`select_index_1`,`condition_story_id_2`,`gain_select_2`,`select_index_2`,`gain_select_3`,`select_index_3` FROM `single_mode_story_root` WHERE `condition_story_id_3`=?;
            if (!query.BindInt(1, conditionStoryId3)) { return null; }

            while (query.Step()) {
                SingleModeStoryRoot orm = _CreateOrmByQueryResultWithConditionStoryId3(query, conditionStoryId3);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeStoryRoot _CreateOrmByQueryResultWithConditionStoryId3(LibNative.Sqlite3.PreparedQuery query, int conditionStoryId3)
        {
            int id                = (int)query.GetInt(0);
            int storyId           = (int)query.GetInt(1);
            int suggestSelect     = (int)query.GetInt(2);
            int suggestIndex      = (int)query.GetInt(3);
            int conditionType     = (int)query.GetInt(4);
            int conditionStoryId1 = (int)query.GetInt(5);
            int gainSelect1       = (int)query.GetInt(6);
            int selectIndex1      = (int)query.GetInt(7);
            int conditionStoryId2 = (int)query.GetInt(8);
            int gainSelect2       = (int)query.GetInt(9);
            int selectIndex2      = (int)query.GetInt(10);
            int gainSelect3       = (int)query.GetInt(11);
            int selectIndex3      = (int)query.GetInt(12);

            return new SingleModeStoryRoot(id, storyId, suggestSelect, suggestIndex, conditionType, conditionStoryId1, gainSelect1, selectIndex1, conditionStoryId2, gainSelect2, selectIndex2, conditionStoryId3, gainSelect3, selectIndex3);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithStoryId.Clear();
            _dictionaryWithConditionStoryId1.Clear();
            _dictionaryWithConditionStoryId2.Clear();
            _dictionaryWithConditionStoryId3.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_SingleModeStoryRoot()) {
                while (query.Step()) {
                    int id                = (int)query.GetInt(0);
                    int storyId           = (int)query.GetInt(1);
                    int suggestSelect     = (int)query.GetInt(2);
                    int suggestIndex      = (int)query.GetInt(3);
                    int conditionType     = (int)query.GetInt(4);
                    int conditionStoryId1 = (int)query.GetInt(5);
                    int gainSelect1       = (int)query.GetInt(6);
                    int selectIndex1      = (int)query.GetInt(7);
                    int conditionStoryId2 = (int)query.GetInt(8);
                    int gainSelect2       = (int)query.GetInt(9);
                    int selectIndex2      = (int)query.GetInt(10);
                    int conditionStoryId3 = (int)query.GetInt(11);
                    int gainSelect3       = (int)query.GetInt(12);
                    int selectIndex3      = (int)query.GetInt(13);

                    int key = (int)id;
                    SingleModeStoryRoot orm = new SingleModeStoryRoot(id, storyId, suggestSelect, suggestIndex, conditionType, conditionStoryId1, gainSelect1, selectIndex1, conditionStoryId2, gainSelect2, selectIndex2, conditionStoryId3, gainSelect3, selectIndex3);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }



        public sealed partial class SingleModeStoryRoot
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: story_id) </summary>
            public readonly int StoryId;
            /// <summary> (CSV column: suggest_select) </summary>
            public readonly int SuggestSelect;
            /// <summary> (CSV column: suggest_index) </summary>
            public readonly int SuggestIndex;
            /// <summary> (CSV column: condition_type) </summary>
            public readonly int ConditionType;
            /// <summary> (CSV column: condition_story_id_1) </summary>
            public readonly int ConditionStoryId1;
            /// <summary> (CSV column: gain_select_1) </summary>
            public readonly int GainSelect1;
            /// <summary> (CSV column: select_index_1) </summary>
            public readonly int SelectIndex1;
            /// <summary> (CSV column: condition_story_id_2) </summary>
            public readonly int ConditionStoryId2;
            /// <summary> (CSV column: gain_select_2) </summary>
            public readonly int GainSelect2;
            /// <summary> (CSV column: select_index_2) </summary>
            public readonly int SelectIndex2;
            /// <summary> (CSV column: condition_story_id_3) </summary>
            public readonly int ConditionStoryId3;
            /// <summary> (CSV column: gain_select_3) </summary>
            public readonly int GainSelect3;
            /// <summary> (CSV column: select_index_3) </summary>
            public readonly int SelectIndex3;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeStoryRoot(int id = 0, int storyId = 0, int suggestSelect = 0, int suggestIndex = 0, int conditionType = 0, int conditionStoryId1 = 0, int gainSelect1 = 0, int selectIndex1 = 0, int conditionStoryId2 = 0, int gainSelect2 = 0, int selectIndex2 = 0, int conditionStoryId3 = 0, int gainSelect3 = 0, int selectIndex3 = 0)
            {
                this.Id                = id;
                this.StoryId           = storyId;
                this.SuggestSelect     = suggestSelect;
                this.SuggestIndex      = suggestIndex;
                this.ConditionType     = conditionType;
                this.ConditionStoryId1 = conditionStoryId1;
                this.GainSelect1       = gainSelect1;
                this.SelectIndex1      = selectIndex1;
                this.ConditionStoryId2 = conditionStoryId2;
                this.GainSelect2       = gainSelect2;
                this.SelectIndex2      = selectIndex2;
                this.ConditionStoryId3 = conditionStoryId3;
                this.GainSelect3       = gainSelect3;
                this.SelectIndex3      = selectIndex3;
            }
        }
    }
}
