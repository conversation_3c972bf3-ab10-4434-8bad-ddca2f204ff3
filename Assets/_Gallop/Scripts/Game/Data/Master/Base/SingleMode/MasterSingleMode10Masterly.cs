// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_10_masterly
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleMode10Masterly : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_10_masterly";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleMode10Masterly> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleMode10Masterly(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleMode10Masterly>();
            _db = db;
        }


        public SingleMode10Masterly Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleMode10Masterly");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleMode10Masterly", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleMode10Masterly _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleMode10Masterly();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleMode10Masterly");
                return null;
            }

            // SELECT `legend_id`,`buff_num`,`condition_group_id`,`effect_group_id` FROM `single_mode_10_masterly` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleMode10Masterly orm = null;

            if (query.Step())
            {
                int legendId         = (int)query.GetInt(0);
                int buffNum          = (int)query.GetInt(1);
                int conditionGroupId = (int)query.GetInt(2);
                int effectGroupId    = (int)query.GetInt(3);

                orm = new SingleMode10Masterly(id, legendId, buffNum, conditionGroupId, effectGroupId);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleMode10Masterly GetWithLegendId(int legendId)
        {
            SingleMode10Masterly orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithLegendId(legendId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", legendId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleMode10Masterly _SelectWithLegendId(int legendId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleMode10Masterly_LegendId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleMode10Masterly");
                return null;
            }

            // SELECT `id`,`buff_num`,`condition_group_id`,`effect_group_id` FROM `single_mode_10_masterly` WHERE `legend_id`=?;
            if (!query.BindInt(1, legendId)) { return null; }

            SingleMode10Masterly orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithLegendId(query, legendId);
            }

            query.Reset();

            return orm;
        }

        private SingleMode10Masterly _CreateOrmByQueryResultWithLegendId(LibNative.Sqlite3.PreparedQuery query, int legendId)
        {
            int id               = (int)query.GetInt(0);
            int buffNum          = (int)query.GetInt(1);
            int conditionGroupId = (int)query.GetInt(2);
            int effectGroupId    = (int)query.GetInt(3);

            return new SingleMode10Masterly(id, legendId, buffNum, conditionGroupId, effectGroupId);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class SingleMode10Masterly
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: legend_id) </summary>
            public readonly int LegendId;
            /// <summary> (CSV column: buff_num) </summary>
            public readonly int BuffNum;
            /// <summary> (CSV column: condition_group_id) </summary>
            public readonly int ConditionGroupId;
            /// <summary> (CSV column: effect_group_id) </summary>
            public readonly int EffectGroupId;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleMode10Masterly(int id = 0, int legendId = 0, int buffNum = 0, int conditionGroupId = 0, int effectGroupId = 0)
            {
                this.Id               = id;
                this.LegendId         = legendId;
                this.BuffNum          = buffNum;
                this.ConditionGroupId = conditionGroupId;
                this.EffectGroupId    = effectGroupId;
            }
        }
    }
}
