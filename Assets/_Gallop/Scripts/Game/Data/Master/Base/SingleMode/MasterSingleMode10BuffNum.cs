// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_10_buff_num
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:legend_id, :num]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleMode10BuffNum : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_10_buff_num";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleMode10BuffNum> _lazyPrimaryKeyDictionary = null;
        private Dictionary<ulong, List<SingleMode10BuffNum>> _dictionaryWithLegendIdAndNum = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleMode10BuffNum(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleMode10BuffNum>();
            _dictionaryWithLegendIdAndNum = new Dictionary<ulong, List<SingleMode10BuffNum>>();
            _db = db;
        }


        public SingleMode10BuffNum Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleMode10BuffNum");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleMode10BuffNum", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleMode10BuffNum _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleMode10BuffNum();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleMode10BuffNum");
                return null;
            }

            // SELECT `legend_id`,`gauge_min`,`gauge_max`,`num`,`highest_num`,`link_num` FROM `single_mode_10_buff_num` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleMode10BuffNum orm = null;

            if (query.Step())
            {
                int legendId   = (int)query.GetInt(0);
                int gaugeMin   = (int)query.GetInt(1);
                int gaugeMax   = (int)query.GetInt(2);
                int num        = (int)query.GetInt(3);
                int highestNum = (int)query.GetInt(4);
                int linkNum    = (int)query.GetInt(5);

                orm = new SingleMode10BuffNum(id, legendId, gaugeMin, gaugeMax, num, highestNum, linkNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleMode10BuffNum GetWithLegendIdAndNumOrderByIdAsc(int legendId, int num)
        {
            SingleMode10BuffNum orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithLegendIdAndNumOrderByIdAsc(legendId, num);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", legendId, num));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleMode10BuffNum _SelectWithLegendIdAndNumOrderByIdAsc(int legendId, int num)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleMode10BuffNum_LegendId_Num();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleMode10BuffNum");
                return null;
            }

            // SELECT `id`,`gauge_min`,`gauge_max`,`highest_num`,`link_num` FROM `single_mode_10_buff_num` WHERE `legend_id`=? AND `num`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, legendId)) { return null; }
            if (!query.BindInt(2, num))      { return null; }

            SingleMode10BuffNum orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithLegendIdAndNumOrderByIdAsc(query, legendId, num);
            }

            query.Reset();

            return orm;
        }

        public List<SingleMode10BuffNum> GetListWithLegendIdAndNumOrderByIdAsc(int legendId, int num)
        {
            ulong key = ((uint)unchecked((ulong)((int)legendId))) | ((((ulong)unchecked((ulong)((int)num)))) << 32);
            
            if (!_dictionaryWithLegendIdAndNum.ContainsKey(key)) {
                _dictionaryWithLegendIdAndNum.Add(key, _ListSelectWithLegendIdAndNumOrderByIdAsc(legendId, num));
            }

            return _dictionaryWithLegendIdAndNum[key];
      
        }

        public List<SingleMode10BuffNum> MaybeListWithLegendIdAndNumOrderByIdAsc(int legendId, int num)
        {
            List<SingleMode10BuffNum> list = GetListWithLegendIdAndNumOrderByIdAsc(legendId, num);
            return list.Count > 0 ? list : null;
        }

        private List<SingleMode10BuffNum> _ListSelectWithLegendIdAndNumOrderByIdAsc(int legendId, int num)
        {
            List<SingleMode10BuffNum> _list = new List<SingleMode10BuffNum>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleMode10BuffNum_LegendId_Num();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleMode10BuffNum");
                return null;
            }

            // SELECT `id`,`gauge_min`,`gauge_max`,`highest_num`,`link_num` FROM `single_mode_10_buff_num` WHERE `legend_id`=? AND `num`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, legendId)) { return null; }
            if (!query.BindInt(2, num))      { return null; }

            while (query.Step()) {
                SingleMode10BuffNum orm = _CreateOrmByQueryResultWithLegendIdAndNumOrderByIdAsc(query, legendId, num);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleMode10BuffNum _CreateOrmByQueryResultWithLegendIdAndNumOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int legendId, int num)
        {
            int id         = (int)query.GetInt(0);
            int gaugeMin   = (int)query.GetInt(1);
            int gaugeMax   = (int)query.GetInt(2);
            int highestNum = (int)query.GetInt(3);
            int linkNum    = (int)query.GetInt(4);

            return new SingleMode10BuffNum(id, legendId, gaugeMin, gaugeMax, num, highestNum, linkNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithLegendIdAndNum.Clear();
        }



        public sealed partial class SingleMode10BuffNum
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: legend_id) </summary>
            public readonly int LegendId;
            /// <summary> (CSV column: gauge_min) </summary>
            public readonly int GaugeMin;
            /// <summary> (CSV column: gauge_max) </summary>
            public readonly int GaugeMax;
            /// <summary> (CSV column: num) </summary>
            public readonly int Num;
            /// <summary> (CSV column: highest_num) </summary>
            public readonly int HighestNum;
            /// <summary> (CSV column: link_num) </summary>
            public readonly int LinkNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleMode10BuffNum(int id = 0, int legendId = 0, int gaugeMin = 0, int gaugeMax = 0, int num = 0, int highestNum = 0, int linkNum = 0)
            {
                this.Id         = id;
                this.LegendId   = legendId;
                this.GaugeMin   = gaugeMin;
                this.GaugeMax   = gaugeMax;
                this.Num        = num;
                this.HighestNum = highestNum;
                this.LinkNum    = linkNum;
            }
        }
    }
}
