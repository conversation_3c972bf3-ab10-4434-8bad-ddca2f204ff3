// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/omakase_base_pt
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:command_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterOmakaseBasePt : AbstractMasterData
    {
        public const string TABLE_NAME = "omakase_base_pt";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, OmakaseBasePt> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<OmakaseBasePt>> _dictionaryWithCommandId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterOmakaseBasePt(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, OmakaseBasePt>();
            _dictionaryWithCommandId = new Dictionary<int, List<OmakaseBasePt>>();
            _db = db;
        }


        public OmakaseBasePt Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterOmakaseBasePt");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterOmakaseBasePt", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private OmakaseBasePt _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_OmakaseBasePt();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseBasePt");
                return null;
            }

            // SELECT `command_id`,`condition_set_id`,`add_value` FROM `omakase_base_pt` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            OmakaseBasePt orm = null;

            if (query.Step())
            {
                int commandId      = (int)query.GetInt(0);
                int conditionSetId = (int)query.GetInt(1);
                int addValue       = (int)query.GetInt(2);

                orm = new OmakaseBasePt(id, commandId, conditionSetId, addValue);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public OmakaseBasePt GetWithCommandIdOrderByIdAsc(int commandId)
        {
            OmakaseBasePt orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCommandIdOrderByIdAsc(commandId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", commandId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private OmakaseBasePt _SelectWithCommandIdOrderByIdAsc(int commandId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_OmakaseBasePt_CommandId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseBasePt");
                return null;
            }

            // SELECT `id`,`condition_set_id`,`add_value` FROM `omakase_base_pt` WHERE `command_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, commandId)) { return null; }

            OmakaseBasePt orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCommandIdOrderByIdAsc(query, commandId);
            }

            query.Reset();

            return orm;
        }

        public List<OmakaseBasePt> GetListWithCommandIdOrderByIdAsc(int commandId)
        {
            int key = (int)commandId;
            
            if (!_dictionaryWithCommandId.ContainsKey(key)) {
                _dictionaryWithCommandId.Add(key, _ListSelectWithCommandIdOrderByIdAsc(commandId));
            }

            return _dictionaryWithCommandId[key];
      
        }

        public List<OmakaseBasePt> MaybeListWithCommandIdOrderByIdAsc(int commandId)
        {
            List<OmakaseBasePt> list = GetListWithCommandIdOrderByIdAsc(commandId);
            return list.Count > 0 ? list : null;
        }

        private List<OmakaseBasePt> _ListSelectWithCommandIdOrderByIdAsc(int commandId)
        {
            List<OmakaseBasePt> _list = new List<OmakaseBasePt>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_OmakaseBasePt_CommandId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for OmakaseBasePt");
                return null;
            }

            // SELECT `id`,`condition_set_id`,`add_value` FROM `omakase_base_pt` WHERE `command_id`=? ORDER BY `id` ASC;
            if (!query.BindInt(1, commandId)) { return null; }

            while (query.Step()) {
                OmakaseBasePt orm = _CreateOrmByQueryResultWithCommandIdOrderByIdAsc(query, commandId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private OmakaseBasePt _CreateOrmByQueryResultWithCommandIdOrderByIdAsc(LibNative.Sqlite3.PreparedQuery query, int commandId)
        {
            int id             = (int)query.GetInt(0);
            int conditionSetId = (int)query.GetInt(1);
            int addValue       = (int)query.GetInt(2);

            return new OmakaseBasePt(id, commandId, conditionSetId, addValue);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithCommandId.Clear();
        }



        public sealed partial class OmakaseBasePt
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: command_id) </summary>
            public readonly int CommandId;
            /// <summary> (CSV column: condition_set_id) </summary>
            public readonly int ConditionSetId;
            /// <summary> (CSV column: add_value) </summary>
            public readonly int AddValue;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public OmakaseBasePt(int id = 0, int commandId = 0, int conditionSetId = 0, int addValue = 0)
            {
                this.Id             = id;
                this.CommandId      = commandId;
                this.ConditionSetId = conditionSetId;
                this.AddValue       = addValue;
            }
        }
    }
}
