// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_live_master_bonus
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeLiveMasterBonus : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_live_master_bonus";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeLiveMasterBonus> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeLiveMasterBonus(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeLiveMasterBonus>();
            _db = db;
        }


        public SingleModeLiveMasterBonus Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeLiveMasterBonus");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeLiveMasterBonus", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeLiveMasterBonus _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeLiveMasterBonus();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeLiveMasterBonus");
                return null;
            }

            // SELECT `master_bonus_type`,`master_bonus_type_value`,`master_bonus_gain_type_1`,`master_bonus_gain_value_1_1`,`master_bonus_gain_value_1_2`,`master_bonus_gain_value_1_3`,`master_bonus_gain_value_1_4`,`master_bonus_gain_type_2`,`master_bonus_gain_value_2_1`,`master_bonus_gain_value_2_2`,`master_bonus_gain_value_2_3`,`master_bonus_gain_value_2_4`,`master_bonus_gain_type_3`,`master_bonus_gain_value_3_1`,`master_bonus_gain_value_3_2`,`master_bonus_gain_value_3_3`,`master_bonus_gain_value_3_4` FROM `single_mode_live_master_bonus` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeLiveMasterBonus orm = null;

            if (query.Step())
            {
                int masterBonusType        = (int)query.GetInt(0);
                int masterBonusTypeValue   = (int)query.GetInt(1);
                int masterBonusGainType1   = (int)query.GetInt(2);
                int masterBonusGainValue11 = (int)query.GetInt(3);
                int masterBonusGainValue12 = (int)query.GetInt(4);
                int masterBonusGainValue13 = (int)query.GetInt(5);
                int masterBonusGainValue14 = (int)query.GetInt(6);
                int masterBonusGainType2   = (int)query.GetInt(7);
                int masterBonusGainValue21 = (int)query.GetInt(8);
                int masterBonusGainValue22 = (int)query.GetInt(9);
                int masterBonusGainValue23 = (int)query.GetInt(10);
                int masterBonusGainValue24 = (int)query.GetInt(11);
                int masterBonusGainType3   = (int)query.GetInt(12);
                int masterBonusGainValue31 = (int)query.GetInt(13);
                int masterBonusGainValue32 = (int)query.GetInt(14);
                int masterBonusGainValue33 = (int)query.GetInt(15);
                int masterBonusGainValue34 = (int)query.GetInt(16);

                orm = new SingleModeLiveMasterBonus(id, masterBonusType, masterBonusTypeValue, masterBonusGainType1, masterBonusGainValue11, masterBonusGainValue12, masterBonusGainValue13, masterBonusGainValue14, masterBonusGainType2, masterBonusGainValue21, masterBonusGainValue22, masterBonusGainValue23, masterBonusGainValue24, masterBonusGainType3, masterBonusGainValue31, masterBonusGainValue32, masterBonusGainValue33, masterBonusGainValue34);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class SingleModeLiveMasterBonus
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: master_bonus_type) </summary>
            public readonly int MasterBonusType;
            /// <summary> (CSV column: master_bonus_type_value) </summary>
            public readonly int MasterBonusTypeValue;
            /// <summary> (CSV column: master_bonus_gain_type_1) </summary>
            public readonly int MasterBonusGainType1;
            /// <summary> (CSV column: master_bonus_gain_value_1_1) </summary>
            public readonly int MasterBonusGainValue11;
            /// <summary> (CSV column: master_bonus_gain_value_1_2) </summary>
            public readonly int MasterBonusGainValue12;
            /// <summary> (CSV column: master_bonus_gain_value_1_3) </summary>
            public readonly int MasterBonusGainValue13;
            /// <summary> (CSV column: master_bonus_gain_value_1_4) </summary>
            public readonly int MasterBonusGainValue14;
            /// <summary> (CSV column: master_bonus_gain_type_2) </summary>
            public readonly int MasterBonusGainType2;
            /// <summary> (CSV column: master_bonus_gain_value_2_1) </summary>
            public readonly int MasterBonusGainValue21;
            /// <summary> (CSV column: master_bonus_gain_value_2_2) </summary>
            public readonly int MasterBonusGainValue22;
            /// <summary> (CSV column: master_bonus_gain_value_2_3) </summary>
            public readonly int MasterBonusGainValue23;
            /// <summary> (CSV column: master_bonus_gain_value_2_4) </summary>
            public readonly int MasterBonusGainValue24;
            /// <summary> (CSV column: master_bonus_gain_type_3) </summary>
            public readonly int MasterBonusGainType3;
            /// <summary> (CSV column: master_bonus_gain_value_3_1) </summary>
            public readonly int MasterBonusGainValue31;
            /// <summary> (CSV column: master_bonus_gain_value_3_2) </summary>
            public readonly int MasterBonusGainValue32;
            /// <summary> (CSV column: master_bonus_gain_value_3_3) </summary>
            public readonly int MasterBonusGainValue33;
            /// <summary> (CSV column: master_bonus_gain_value_3_4) </summary>
            public readonly int MasterBonusGainValue34;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeLiveMasterBonus(int id = 0, int masterBonusType = 0, int masterBonusTypeValue = 0, int masterBonusGainType1 = 0, int masterBonusGainValue11 = 0, int masterBonusGainValue12 = 0, int masterBonusGainValue13 = 0, int masterBonusGainValue14 = 0, int masterBonusGainType2 = 0, int masterBonusGainValue21 = 0, int masterBonusGainValue22 = 0, int masterBonusGainValue23 = 0, int masterBonusGainValue24 = 0, int masterBonusGainType3 = 0, int masterBonusGainValue31 = 0, int masterBonusGainValue32 = 0, int masterBonusGainValue33 = 0, int masterBonusGainValue34 = 0)
            {
                this.Id                     = id;
                this.MasterBonusType        = masterBonusType;
                this.MasterBonusTypeValue   = masterBonusTypeValue;
                this.MasterBonusGainType1   = masterBonusGainType1;
                this.MasterBonusGainValue11 = masterBonusGainValue11;
                this.MasterBonusGainValue12 = masterBonusGainValue12;
                this.MasterBonusGainValue13 = masterBonusGainValue13;
                this.MasterBonusGainValue14 = masterBonusGainValue14;
                this.MasterBonusGainType2   = masterBonusGainType2;
                this.MasterBonusGainValue21 = masterBonusGainValue21;
                this.MasterBonusGainValue22 = masterBonusGainValue22;
                this.MasterBonusGainValue23 = masterBonusGainValue23;
                this.MasterBonusGainValue24 = masterBonusGainValue24;
                this.MasterBonusGainType3   = masterBonusGainType3;
                this.MasterBonusGainValue31 = masterBonusGainValue31;
                this.MasterBonusGainValue32 = masterBonusGainValue32;
                this.MasterBonusGainValue33 = masterBonusGainValue33;
                this.MasterBonusGainValue34 = masterBonusGainValue34;
            }
        }
    }
}
