// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_scenario_record
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:scenario_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeScenarioRecord : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_scenario_record";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeScenarioRecord> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SingleModeScenarioRecord>> _dictionaryWithScenarioId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeScenarioRecord(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeScenarioRecord>();
            _dictionaryWithScenarioId = new Dictionary<int, List<SingleModeScenarioRecord>>();
            _db = db;
        }


        public SingleModeScenarioRecord Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeScenarioRecord");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeScenarioRecord", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeScenarioRecord _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeScenarioRecord();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeScenarioRecord");
                return null;
            }

            // SELECT `scenario_id`,`need_record_min`,`reward_item_category`,`reward_item_id`,`reward_num` FROM `single_mode_scenario_record` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeScenarioRecord orm = null;

            if (query.Step())
            {
                int scenarioId         = (int)query.GetInt(0);
                int needRecordMin      = (int)query.GetInt(1);
                int rewardItemCategory = (int)query.GetInt(2);
                int rewardItemId       = (int)query.GetInt(3);
                int rewardNum          = (int)query.GetInt(4);

                orm = new SingleModeScenarioRecord(id, scenarioId, needRecordMin, rewardItemCategory, rewardItemId, rewardNum);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeScenarioRecord GetWithScenarioId(int scenarioId)
        {
            SingleModeScenarioRecord orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithScenarioId(scenarioId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", scenarioId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeScenarioRecord _SelectWithScenarioId(int scenarioId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeScenarioRecord_ScenarioId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeScenarioRecord");
                return null;
            }

            // SELECT `id`,`need_record_min`,`reward_item_category`,`reward_item_id`,`reward_num` FROM `single_mode_scenario_record` WHERE `scenario_id`=?;
            if (!query.BindInt(1, scenarioId)) { return null; }

            SingleModeScenarioRecord orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithScenarioId(query, scenarioId);
            }

            query.Reset();

            return orm;
        }

        public List<SingleModeScenarioRecord> GetListWithScenarioId(int scenarioId)
        {
            int key = (int)scenarioId;
            
            if (!_dictionaryWithScenarioId.ContainsKey(key)) {
                _dictionaryWithScenarioId.Add(key, _ListSelectWithScenarioId(scenarioId));
            }

            return _dictionaryWithScenarioId[key];
      
        }

        public List<SingleModeScenarioRecord> MaybeListWithScenarioId(int scenarioId)
        {
            List<SingleModeScenarioRecord> list = GetListWithScenarioId(scenarioId);
            return list.Count > 0 ? list : null;
        }

        private List<SingleModeScenarioRecord> _ListSelectWithScenarioId(int scenarioId)
        {
            List<SingleModeScenarioRecord> _list = new List<SingleModeScenarioRecord>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeScenarioRecord_ScenarioId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeScenarioRecord");
                return null;
            }

            // SELECT `id`,`need_record_min`,`reward_item_category`,`reward_item_id`,`reward_num` FROM `single_mode_scenario_record` WHERE `scenario_id`=?;
            if (!query.BindInt(1, scenarioId)) { return null; }

            while (query.Step()) {
                SingleModeScenarioRecord orm = _CreateOrmByQueryResultWithScenarioId(query, scenarioId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SingleModeScenarioRecord _CreateOrmByQueryResultWithScenarioId(LibNative.Sqlite3.PreparedQuery query, int scenarioId)
        {
            int id                 = (int)query.GetInt(0);
            int needRecordMin      = (int)query.GetInt(1);
            int rewardItemCategory = (int)query.GetInt(2);
            int rewardItemId       = (int)query.GetInt(3);
            int rewardNum          = (int)query.GetInt(4);

            return new SingleModeScenarioRecord(id, scenarioId, needRecordMin, rewardItemCategory, rewardItemId, rewardNum);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithScenarioId.Clear();
        }



        public sealed partial class SingleModeScenarioRecord
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: scenario_id) </summary>
            public readonly int ScenarioId;
            /// <summary> (CSV column: need_record_min) </summary>
            public readonly int NeedRecordMin;
            /// <summary> (CSV column: reward_item_category) </summary>
            public readonly int RewardItemCategory;
            /// <summary> (CSV column: reward_item_id) </summary>
            public readonly int RewardItemId;
            /// <summary> (CSV column: reward_num) </summary>
            public readonly int RewardNum;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeScenarioRecord(int id = 0, int scenarioId = 0, int needRecordMin = 0, int rewardItemCategory = 0, int rewardItemId = 0, int rewardNum = 0)
            {
                this.Id                 = id;
                this.ScenarioId         = scenarioId;
                this.NeedRecordMin      = needRecordMin;
                this.RewardItemCategory = rewardItemCategory;
                this.RewardItemId       = rewardItemId;
                this.RewardNum          = rewardNum;
            }
        }
    }
}
