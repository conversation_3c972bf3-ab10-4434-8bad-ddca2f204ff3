// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: single_mode/single_mode_cook_dish_cutt_se
// Author: ykst <<EMAIL>>

using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        disable_cache - false
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSingleModeCookDishCuttSe : AbstractMasterData
    {
        public const string TABLE_NAME = "single_mode_cook_dish_cutt_se";

        MasterSingleModeDatabase _db = null;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, SingleModeCookDishCuttSe> _lazyPrimaryKeyDictionary = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSingleModeCookDishCuttSe(MasterSingleModeDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, SingleModeCookDishCuttSe>();
            _db = db;
        }


        public SingleModeCookDishCuttSe Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
   
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSingleModeCookDishCuttSe");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSingleModeCookDishCuttSe", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
 
                    }
                }
            }

            return orm;
        }

        private SingleModeCookDishCuttSe _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SingleModeCookDishCuttSe();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookDishCuttSe");
                return null;
            }

            // SELECT `command_group_id`,`chara_type`,`se_cue_name`,`se_cuesheet_name` FROM `single_mode_cook_dish_cutt_se` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            SingleModeCookDishCuttSe orm = null;

            if (query.Step())
            {
                int commandGroupId    = (int)query.GetInt(0);
                int charaType         = (int)query.GetInt(1);
                string seCueName      = query.GetText(2);
                string seCuesheetName = query.GetText(3);

                orm = new SingleModeCookDishCuttSe(id, commandGroupId, charaType, seCueName, seCuesheetName);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public SingleModeCookDishCuttSe GetWithCommandGroupId(int commandGroupId)
        {
            SingleModeCookDishCuttSe orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCommandGroupId(commandGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", commandGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookDishCuttSe _SelectWithCommandGroupId(int commandGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookDishCuttSe_CommandGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookDishCuttSe");
                return null;
            }

            // SELECT `id`,`chara_type`,`se_cue_name`,`se_cuesheet_name` FROM `single_mode_cook_dish_cutt_se` WHERE `command_group_id`=?;
            if (!query.BindInt(1, commandGroupId)) { return null; }

            SingleModeCookDishCuttSe orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCommandGroupId(query, commandGroupId);
            }

            query.Reset();

            return orm;
        }

        private SingleModeCookDishCuttSe _CreateOrmByQueryResultWithCommandGroupId(LibNative.Sqlite3.PreparedQuery query, int commandGroupId)
        {
            int id                = (int)query.GetInt(0);
            int charaType         = (int)query.GetInt(1);
            string seCueName      = query.GetText(2);
            string seCuesheetName = query.GetText(3);

            return new SingleModeCookDishCuttSe(id, commandGroupId, charaType, seCueName, seCuesheetName);
        }

        public SingleModeCookDishCuttSe GetWithCharaType(int charaType)
        {
            SingleModeCookDishCuttSe orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaType(charaType);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", charaType));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookDishCuttSe _SelectWithCharaType(int charaType)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookDishCuttSe_CharaType();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookDishCuttSe");
                return null;
            }

            // SELECT `id`,`command_group_id`,`se_cue_name`,`se_cuesheet_name` FROM `single_mode_cook_dish_cutt_se` WHERE `chara_type`=?;
            if (!query.BindInt(1, charaType)) { return null; }

            SingleModeCookDishCuttSe orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaType(query, charaType);
            }

            query.Reset();

            return orm;
        }

        private SingleModeCookDishCuttSe _CreateOrmByQueryResultWithCharaType(LibNative.Sqlite3.PreparedQuery query, int charaType)
        {
            int id                = (int)query.GetInt(0);
            int commandGroupId    = (int)query.GetInt(1);
            string seCueName      = query.GetText(2);
            string seCuesheetName = query.GetText(3);

            return new SingleModeCookDishCuttSe(id, commandGroupId, charaType, seCueName, seCuesheetName);
        }

        public SingleModeCookDishCuttSe GetWithCharaTypeAndCommandGroupId(int charaType, int commandGroupId)
        {
            SingleModeCookDishCuttSe orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCharaTypeAndCommandGroupId(charaType, commandGroupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0} {1}", charaType, commandGroupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SingleModeCookDishCuttSe _SelectWithCharaTypeAndCommandGroupId(int charaType, int commandGroupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SingleModeCookDishCuttSe_CharaType_CommandGroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SingleModeCookDishCuttSe");
                return null;
            }

            // SELECT `id`,`se_cue_name`,`se_cuesheet_name` FROM `single_mode_cook_dish_cutt_se` WHERE `chara_type`=? AND `command_group_id`=?;
            if (!query.BindInt(1, charaType))      { return null; }
            if (!query.BindInt(2, commandGroupId)) { return null; }

            SingleModeCookDishCuttSe orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCharaTypeAndCommandGroupId(query, charaType, commandGroupId);
            }

            query.Reset();

            return orm;
        }

        private SingleModeCookDishCuttSe _CreateOrmByQueryResultWithCharaTypeAndCommandGroupId(LibNative.Sqlite3.PreparedQuery query, int charaType, int commandGroupId)
        {
            int id                = (int)query.GetInt(0);
            string seCueName      = query.GetText(1);
            string seCuesheetName = query.GetText(2);

            return new SingleModeCookDishCuttSe(id, commandGroupId, charaType, seCueName, seCuesheetName);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
        }



        public sealed partial class SingleModeCookDishCuttSe
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: command_group_id) </summary>
            public readonly int CommandGroupId;
            /// <summary> (CSV column: chara_type) </summary>
            public readonly int CharaType;
            /// <summary> (CSV column: se_cue_name) </summary>
            public readonly string SeCueName;
            /// <summary> (CSV column: se_cuesheet_name) </summary>
            public readonly string SeCuesheetName;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SingleModeCookDishCuttSe(int id = 0, int commandGroupId = 0, int charaType = 0, string seCueName = "", string seCuesheetName = "")
            {
                this.Id             = id;
                this.CommandGroupId = commandGroupId;
                this.CharaType      = charaType;
                this.SeCueName      = seCueName;
                this.SeCuesheetName = seCuesheetName;
            }
        }
    }
}
