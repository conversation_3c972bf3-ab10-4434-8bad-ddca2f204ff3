using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using CodeStage.AntiCheat.ObscuredTypes;
using DG.Tweening;
using static Gallop.StaticVariableDefine.Parts.Header;

namespace Gallop
{
    using static TeamStadiumDefine;

    /// <summary>
    /// ヘッダー
    /// </summary>
    [AddComponentMenu("")]
    public class Header : Mono<PERSON>ehaviour
    {
        // レベルやTPの現在値との比較でイコールにならないような値
        private const int DEFAULT_CACHE_VALUE = int.MinValue;

        // 表示状態ならtrue
        public bool IsActive { get; private set; } = true;

        [SerializeField]
        private TpDisplaySet _tpDisplaySet = null;

        [SerializeField]
        private RpDisplaySet _rpDisplaySet = null;

        [SerializeField]
        private TextCommon _moneyText = null;

        [SerializeField]
        private TextCommon _carrotStoneText = null;

        [SerializeField]
        private ButtonCommon _carrotStoneBuyButton = null;

        [SerializeField]
        private ButtonCommon _menuButton = null;

        [SerializeField]
        private ButtonCommon _teamDetailButton = null;

        [SerializeField]
        private RawImageCommon _teamRankFrame = null;

        [SerializeField]
        private RawImageCommon _teamRankIcon = null;

        [SerializeField]
        private TextCommon _teamRankPoint = null;

        // マニーのキャッシュ
        private ObscuredInt _cachedMoney = DEFAULT_CACHE_VALUE;

        // ユーザーの石のキャッシュ
        private ObscuredInt _cachedFCoin = DEFAULT_CACHE_VALUE;
        private ObscuredInt _cachedCoin = DEFAULT_CACHE_VALUE;

        // チームランクのキャッシュ
        private ObscuredInt _cachedTeamRankPoint = DEFAULT_CACHE_VALUE;

        private Sequence _sequence;
        private string _rankFramePath;
        private string _rankIconPath;
        private FlashPlayer _notifyBadge;
        private GameObject _newIcon;

        private List<Func<IEnumerator>> _onTransitionInterceptorList = new List<Func<IEnumerator>>();

        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize()
        {
            // UIセットアップ
            SetupUI();

            //初回更新を自前で呼ぶ
            Update();
        }


        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            DialogTeamEvaluationPoint.RegisterDownload(register);
        }

        /// <summary>
        /// 全てのボタンの押下可否を設定
        /// </summary>
        /// <param name="interactable"></param>
        public void SetInteractableAll(bool interactable)
        {
            _tpDisplaySet.SetInteractablePlusButton(interactable);
            _rpDisplaySet.SetInteractablePlusButton(interactable);
            _carrotStoneBuyButton.SetInteractable(interactable);
            _menuButton.SetInteractable(interactable);
            _teamDetailButton.SetInteractable(interactable);
            _notifyBadge.SetActiveWithCheck(interactable);
            _newIcon.SetActiveWithCheck(interactable);
            if (interactable)
            {
                UpdateBadge();
            }
        }

        /// <summary>
        /// UIセットアップ
        /// </summary>
        private void SetupUI()
        {
            _tpDisplaySet.Initialize();
            _rpDisplaySet.Initialize();
            _carrotStoneBuyButton.SetOnClick(OnClickCarrotStoneButton);
            _menuButton.SetOnClick(OnClickMenuButton);
            _teamDetailButton.SetOnClick(OnClickTeamDetailButton);
            UpdateTeamRank();
            CreateNotifyBadge();
            CreateNewIcon();
            UpdateBadge();
        }

        /// <summary>
        /// 通知バッジ生成
        /// </summary>
        private void CreateNotifyBadge()
        {
            _notifyBadge = UIUtil.CreateNotifyBadgeIconFlash(_menuButton.transform, UIManager.CanvasSoringOrder.Header, _menuButton.image.canvasRenderer);
            _notifyBadge.transform.localPosition = MENU_BADGE_POS;
        }

        /// <summary>
        /// Newアイコン生成
        /// </summary>
        private void CreateNewIcon()
        {
            _newIcon = UIUtil.CreateNewIcon(_menuButton.transform);
            _newIcon.transform.localPosition = NEW_ICON_POS;
        }

        /// <summary>
        /// バッジの表示確認
        /// </summary>
        public void UpdateBadge()
        {
            bool needNotifyBadge = DialogHomeMenuMain.NeedHeaderNotifyBadge();
            bool needNewIcon = DialogHomeMenuMain.NeedHeaderNewIcon();
            if (needNotifyBadge)
            {
                // 通知バッジを優先
                needNewIcon = false;
            }
            
            if (_notifyBadge != null)
            {
                _notifyBadge.SetActiveWithCheck(needNotifyBadge);
            }

            if (_newIcon != null)
            {
                _newIcon.SetActive(needNewIcon);
            }
        }

        /// <summary>
        /// 表示更新
        /// </summary>
        private void Update()
        {
            if (WorkDataManager.HasInstance() == false)
                return;

            UpdateCoin();
            UpdateMoney();
            UpdateTeamRank();
        }

        /// <summary>
        /// 表示
        /// </summary>
        public void Show(bool immediate = false)
        {
            UpdateBadge();
            _sequence?.Complete(true);
            gameObject.SetActiveWithCheck(true);
            _sequence = TweenAnimationBuilder.CreateSequence(gameObject, TweenAnimation.PresetType.HomeInFromUp);
            _sequence.onComplete = () =>
            {
                _sequence = null;
            };
            if (immediate)
                _sequence.Complete(true);
            IsActive = true;
        }

        /// <summary>
        /// 退場
        /// </summary>
        public void Hide(bool immediate = false, Action onFinish = null)
        {
            _sequence?.Complete(true);
            _sequence = TweenAnimationBuilder.CreateSequence(gameObject, TweenAnimation.PresetType.HomeOutToUp);
            _sequence.onComplete = () =>
            {
                gameObject.SetActiveWithCheck(false);
                onFinish?.Invoke();
                _sequence = null;
            };
            if (immediate)
                _sequence.Complete(true);
            IsActive = false;
        }

        /// <summary>
        /// メニューバッジのモーション再生
        /// </summary>
        public void PlayInMenuBadge()
        {
            if (_notifyBadge == null)
                return;

            if (_notifyBadge.gameObject.activeSelf)
            {
                UIUtil.ReplayBadgeIconFlashText(_notifyBadge);
                // ホームのバッジと同時にアニメ再生しても微妙にタイミングがずれるのを回避
                _notifyBadge.Root._deltaTime = 0f;
            }

            _notifyBadge.SetActiveWithCheck(_notifyBadge.gameObject.activeSelf);
        }

        /// <summary>
        /// キャロットストーン更新
        /// </summary>
        private void UpdateCoin()
        {
            if (_carrotStoneText == null)
            {
                return;
            }

            var currentCoin = WorkDataManager.Instance.UserData.CarrotStone.ChargeCoin;
            var currentFCoin = WorkDataManager.Instance.UserData.CarrotStone.FreeCoin;
            // 内部的にキャッシュしているコイン数が違ったらUI更新
            if (_cachedCoin != currentCoin || _cachedFCoin != currentFCoin)
            {
                _carrotStoneText.text = WorkDataManager.Instance.UserData.CarrotStone.TotalCoinStr;
                _cachedCoin = currentCoin;
                _cachedFCoin = currentFCoin;
            }
        }

        /// <summary>
        /// マニー更新
        /// </summary>
        private void UpdateMoney()
        {
            if (_moneyText == null)
            {
                return;
            }

            var currentMoney = WorkDataManager.Instance.ItemData.GetMoneyNum();
            // 内部的にキャッシュしているコイン数が違ったらUI更新
            if (_cachedMoney != currentMoney)
            {
                _moneyText.text = currentMoney.ToString(TextUtil.CommaSeparatedFormat);
                _cachedMoney = currentMoney;
            }
        }

        /// <summary>
        /// チームランク更新
        /// </summary>
        private void UpdateTeamRank()
        {
            var teamStadiumData = WorkDataManager.Instance.TeamStadiumData;
            int teamRankPoint = teamStadiumData.TeamStadiumDeckInfo.GetTeamRankPoint();

            // 内部的にキャッシュしているポイントが違ったらUI更新
            if (_cachedTeamRankPoint != teamRankPoint)
            {
                TeamRank teamRank = TeamStadiumUtil.GetTeamRank(teamRankPoint);
                TeamRankGroup teamRankGroup = teamRank.Group();

                // 既にロードされていればアンロード
                if (!string.IsNullOrEmpty(_rankFramePath))
                {
                    ResourceManager.Unload(_rankFramePath, ResourceManager.ResourceHash.HeaderFooter);
                }

                if (!string.IsNullOrEmpty(_rankIconPath))
                {
                    ResourceManager.Unload(_rankIconPath, ResourceManager.ResourceHash.HeaderFooter);
                }

                if (teamRankGroup != TeamRankGroup.None)
                {
                    _rankFramePath = ResourcePath.GetTeamRankFrame(teamRankGroup);
                    _teamRankFrame.texture = ResourceManager.LoadOnHash<Texture>(_rankFramePath, ResourceManager.ResourceHash.HeaderFooter);
                }
                if (teamRank != TeamRank.None)
                {
                    _rankIconPath = ResourcePath.GetTeamRankIcon(teamRank);
                    _teamRankIcon.texture = ResourceManager.LoadOnHash<Texture>(_rankIconPath, ResourceManager.ResourceHash.HeaderFooter);
                }
                
                _teamRankPoint.text = teamRankPoint.ToString();
                _cachedTeamRankPoint = teamRankPoint;
            }
        }

        /// <summary>
        /// 押下：チーム評価詳細ボタン
        /// </summary>
        private void OnClickTeamDetailButton()
        {
            DialogTeamEvaluationPoint.Open(CheckTransitionInterceptor);
        }

        /// <summary>
        /// 押下：石購入ボタン
        /// </summary>
        private void OnClickCarrotStoneButton()
        {
            PaymentUtility.Instance.OpenBuyJewelDialog(null);
        }

        /// <summary>
        /// 押下：メニューボタン
        /// </summary>
        private void OnClickMenuButton()
        {
            if (DialogHomeMenuMain.IsOpen()) return;

            DialogHomeMenuMain.Open(null, CheckTransitionInterceptor);
        }

        /// <summary>
        /// TransitionInterceptor登録
        /// </summary>
        /// <param name="interceptor"></param>
        public void RegisterTransitionInterceptor(Func<IEnumerator> interceptor)
        {
            if (!_onTransitionInterceptorList.Contains(interceptor))
            {
                _onTransitionInterceptorList.Add(interceptor);
            }
        }

        /// <summary>
        /// TransitionInterceptor登録解除
        /// </summary>
        /// <param name="interceptor"></param>
        public void UnRegisterTransitionInterceptor(Func<IEnumerator> interceptor)
        {
            _onTransitionInterceptorList.Remove(interceptor);
        }

        /// <summary>
        /// TransitionInterceptor登録全解除
        /// </summary>
        public void ClearRegisterTransitionInterceptor()
        {
            _onTransitionInterceptorList.Clear();
        }

        /// <summary>
        /// TransitionInterceptorチェック処理実行
        /// </summary>
        public void CheckTransitionInterceptor(Action transitionFunc)
        {
            if (_onTransitionInterceptorList.IsNullOrEmpty())
            {
                transitionFunc?.Invoke();
                return;
            }

            StartCoroutine(CheckTransitionInterceptorImpl(transitionFunc));
        }

        /// <summary>
        /// TransitionInterceptorチェック処理コルーチン
        /// </summary>
        private IEnumerator CheckTransitionInterceptorImpl(Action transitionFunc)
        {
            var onTransitionInterceptorList = new List<Func<IEnumerator>>(_onTransitionInterceptorList);
            for (int i = 0, count = onTransitionInterceptorList.Count; i < count; i++)
            {
                var func = onTransitionInterceptorList[i];

                var coroutine = func();
                yield return coroutine;

                var isIntercept = Convert.ToBoolean(coroutine.Current);
                if (isIntercept)
                {
                    // 中断判定になった場合は遷移せずに終了
                    yield break;
                }

                // 中断判定にならなかった場合はチェック対象から外す
                UnRegisterTransitionInterceptor(func);
            }

            // 最終的に中断判定にならなかったので遷移処理を実行
            transitionFunc?.Invoke();
        }
    }
}