using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 楽曲を複数獲得した時のダイアログのリストアイテム
    /// </summary>
    public class PartsGetLiveMusicListItem : LoopScrollItemBase
    {
        [SerializeField]
        private RawImageCommon _jacketImage = null;

        [SerializeField]
        private TextCommon _songName = null;

        /// <summary>
        /// 更新処理 （外部のLoopScrollから呼ばれます）
        /// </summary>
        public void UpdateItem(int musicId)
        {
            _jacketImage.texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetJacketPath(musicId));
            _songName.text = TextUtil.GetMasterText(MasterString.Category.MasterLiveTitle, musicId);
        }
    }
}
