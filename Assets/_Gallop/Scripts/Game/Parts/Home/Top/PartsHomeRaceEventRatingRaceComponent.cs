using System;
using UnityEngine;

namespace Gallop
{
    public class PartsHomeRaceEventRatingRaceComponent : Mono<PERSON><PERSON><PERSON><PERSON>, IPartsHomeRaceEventButtonEventComponent
    {
        [Header("RP表記")]
        [SerializeField] private GameObject _rpRoot;
        [SerializeField] private TextCommon _rpText;

        [Header("フェーズ表記")] 
        [SerializeField] private GameObject _phaseImageRoot;
        [SerializeField] private RawImageCommon _phaseImage;

        public void Setup(HomeRaceEventButtonEventComponentModelBase componentModelBase)
        {
            if (!(componentModelBase is PartsHomeRaceEventRatingRaceComponentModel model)) return;

            // RP表示
            var isRpActive = model.IsShowRpUI(out string rpText);
            SetRpUI(isRpActive, rpText);
            
            // フェーズ表示
            var isPhaseImageActive = model.IsShowPhaseImage(out string texturePath);
            SetPhaseImage(isPhaseImageActive, texturePath);
        }

        private void SetRpUI(bool isActive, string rpText)
        {
            _rpRoot.SetActiveWithCheck(isActive);
            if (!isActive) return;

            _rpText.text = rpText;
        }

        private void SetPhaseImage(bool isActive, string texturePath)
        {
            _phaseImageRoot.SetActiveWithCheck(isActive);
            if (!isActive) return;

            if (!string.IsNullOrEmpty(texturePath))
            {
                _phaseImage.texture = ResourceManager.LoadOnView<Texture2D>(texturePath);
            }
        }
    }   
}
