using System;
using UnityEngine;

namespace Gallop
{
    public class PartsHomeRaceEventChampionsComponentModel : HomeRaceEventButtonEventComponentModelBase
    {
        public class SetupParameter
        {
            /// <summary>イベントが開催中か</summary>
            public bool IsOpen { get; set; }
            ///<summary>無料エントリー可能か</summary>
            public bool IsFree { get; set; }
            ///<summary>現在のラウンド</summary>
            public ChampionsDefines.Round CurrentRound { get; set; }
            ///<summary>現在の決勝開催状態</summary>
            public ChampionsDefines.FinalState CurrentFinalState { get; set; }
        }

        private SetupParameter _setupParam = null;
        
        /// <summary>
        /// コンストラクタ
        /// </summary>
        public PartsHomeRaceEventChampionsComponentModel(SetupParameter setupParam)
        {
            _setupParam = setupParam;
        }

        /// <summary>
        /// チケット表記を使用するか
        /// </summary>
        /// <param name="ticketSprite"></param>
        /// <param name="ticketNumText"></param>
        /// <returns></returns>
        public bool IsShowTicketUI(out Sprite ticketSprite, out string ticketNumText)
        {
            ticketSprite = null;
            ticketNumText = string.Empty;

            // 機能解放前は非表示
            if (!ChampionsUtils.IsReleasedContent())
            {
                return false;
            }
            
            // リーグ選択前と決勝開催中は表示しない
            if (_setupParam.CurrentRound == ChampionsDefines.Round.LeagueSelect ||
                _setupParam.CurrentRound == ChampionsDefines.Round.Final)
            {
                return false;
            }

            var championsData = WorkDataManager.Instance.ChampionsData;

            // リーグ未選択の場合は表示しない
            if (championsData.GetState() == ChampionsDefines.RoundState.None)
            {
                return false;
            }
            
            // 無料エントリー時は表示しない
            var entryCount = championsData.EntryTimes;
            var noEntry = championsData.NoEntry();
            if (_setupParam.IsFree || // 無料エントリー可能
                (entryCount == 0 && !noEntry)) // 無料参加中
            {
                return false;
            }

            var ticketNum = championsData.GetTicketNum();

            // エントリーチケットが未受け取りかつチケット未所持の場合は表示しない
            if (championsData.IsEntryTicketReceivable && ticketNum == 0)
            {
                return false;
            }

            if (ticketNum > 0)
            {
                // チケットがあれば優先して使用する
                ticketSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.CHAMPIONS_TICKET_ICON);
                ticketNumText = ticketNum.ToString();
            }
            else
            {
                // 必要ジュエル数を表示する
                ticketSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.STONE_ICON);
                ticketNumText = ServerDefine.ChampionsMeatingEntryCost.ToString();
            }

            return true;
        }

        /// <summary>
        /// ラウンド表記の画像を表示するか
        /// </summary>
        /// <param name="roundImagePath"></param>
        /// <returns></returns>
        public bool IsShowRoundImage(out string roundImagePath)
        {
            roundImagePath = string.Empty;
            if (_setupParam.IsOpen == false)
            {
                return false;
            }

            roundImagePath =
                ResourcePath.GetChampionsHomeButtonRoundPath(_setupParam.CurrentRound, _setupParam.CurrentFinalState);
            return true;
        }
    }   
}
