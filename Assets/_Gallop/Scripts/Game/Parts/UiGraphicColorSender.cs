using UnityEngine;
using UnityEngine.UI;
namespace Gallop
{
    /// <inheritdoc />
    /// <summary>
    /// 特定のGraphicの色をチェック＆保存＆子に通知するクラス
    /// </summary>
    [DisallowMultipleComponent]
    public class UiGraphicColorSender : ColorSender
    {
        /// <summary>
        /// 色を拾う対象
        /// </summary>
        public Graphic TargetGraphic { get; set; }
        #region MonoBehaviour

        protected override void Awake()
        {
            UpdateTargetGraphic();
            base.Awake();
        }
      
        /// <summary>
        /// 親が切り替わったとき
        /// </summary>
        public override void OnTransformParentChanged()
        {
            UpdateTargetGraphic();
            base.OnTransformParentChanged();
        }

        #endregion

        #region Methods

        /// <summary>
        /// TargetGraphicを探す（自分を含めて親方向のGraphicを対象とする）
        /// </summary>
        protected virtual void UpdateTargetGraphic()
        {
            //まず自分のGraphicを参照する
            var newTargetGraphic = transform.GetComponent<Graphic>();
            if (newTargetGraphic == null)
            {
                //無ければ親のGraphicsを参照する
                newTargetGraphic = transform.GetComponentInParent<Graphic>();
            }

            //変化があったときだけDirtyフラグを立てる
            if ( TargetGraphic != newTargetGraphic )
            {
                TargetGraphic = newTargetGraphic;
                SetDirty();
            }
        }

        /// <inheritdoc />
        /// <summary>
        /// SendColorを更新
        /// 親のCanvasColorを取得する
        /// </summary>
        /// <returns></returns>
        protected override void UpdateSendColor()
        {
            if ( TargetGraphic == null )
            {
                _sendColor = Color.white;
                return;
            }
            _sendColor = TargetGraphic.color * TargetGraphic.canvasRenderer.GetColor();
        }

        #endregion
    }
}
