using System;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Gallop
{
    /// <summary>
    /// フリック判定
    /// </summary>
    public sealed class FlickHandler : UIBehaviour
    {
        public struct FlickStatus
        {
            public bool IsNone { get { return !IsLeft && !IsRight && !IsUp && !IsDown; } }
            public bool IsLeft;
            public bool IsRight;
            public bool IsUp;
            public bool IsDown;

            public void Clear()
            {
                IsLeft = false;
                IsRight = false;
                IsUp = false;
                IsDown = false;
            }

            /// <summary>
            /// 左右どちらかのフリックかどうか
            /// </summary>
            public bool IsSideFlick()
            {
                return IsLeft || IsRight;
            }
        }

        /// <summary>
        /// フリックでSEを鳴らすか
        /// </summary>
        [SerializeField]
        private bool _isPlaySe = true;

        public bool IsPlaySe
        {
            get => _isPlaySe;
            set => _isPlaySe = value;
        }

        /// <summary>
        /// ドラック時間
        /// </summary>
        private float _draggingSec = 0f;

        /// <summary>
        /// タッチしているか
        /// </summary>
        private bool _isTouch = false;
        public bool IsTouch => _isTouch;

        /// <summary>
        /// コールバック
        /// </summary>
        private Action<FlickStatus> _eventCallback = null;

        /// <summary>
        /// フリック基準
        /// </summary>
        private float _flickThresholdX = TouchManager.FLICK_THRESHOLD_X;
        private float _flickThresholdY = TouchManager.FLICK_THRESHOLD_Y;
        /// <summary>
        /// フリック基準セット
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        public void SetFlickThreshold(float x, float y)
        {
            _flickThresholdX = x;
            _flickThresholdY = y;
        }
        
        /// <summary>
        /// タッチ開始
        /// </summary>
        public void StartFlick(PointerEventData eventData)
        {
            if (_isTouch)
            {
                return;
            }

            _isTouch = true;
            _draggingSec = 0f;
        }

        /// <summary>
        /// 指を離したとき
        /// </summary>
        public void EndFlick(PointerEventData eventData)
        {
            if (!_isTouch)
            {
                return;
            }

            _isTouch = false;
            FlickStatus flickStatus = CheckFlick(eventData);
            if (flickStatus.IsNone)
                return;

            if (_isPlaySe)
                PlaySe(flickStatus);

            _eventCallback?.Invoke(flickStatus);

        }

        public void Update()
        {
            if (!_isTouch)
            {
                return;
            }
            //タッチ中なら指をつけている時間を計測（OnDragでやると指が動いていない間の時間が取れないのでアップデートで取得）
            _draggingSec += Time.deltaTime;

            //FlickableButton外で指が離れるとEndFlickが呼ばれずタッチ中フラグが残り続けるのでタッチ状況を監視する
            if(Input.touchCount <= 0 && !Input.GetMouseButton(0))
            {
                _isTouch = false;
            }
        }

        /// <summary>
        /// コールバックの設定
        /// </summary>
        /// <param name="callback"></param>
        public void SetCallback(System.Action<FlickStatus> callback)
        {
            _eventCallback = callback;
        }

        /// <summary>
        /// フリックをチェックする
        /// </summary>
        public FlickStatus CheckFlick(PointerEventData eventData)
        {
            FlickStatus flickStatus = new FlickStatus();
            flickStatus.Clear();
            
            if (eventData == null)
                return flickStatus;
            
            // 時間と距離をもとにフリックの判定
            Vector3 distanceVector = GetVec(eventData);
            if (_draggingSec < TouchManager.FLICK_SEC)
            {
                Vector2 sub = eventData.position - eventData.pressPosition;
                float angle = Math.GetAng(sub);
                if (_flickThresholdX < Mathf.Abs(distanceVector.x))
                {
                    if (distanceVector.x > 0 && CheckFlickDirection(angle , TouchManager.FlickType.RIGHT))
                    {
                        flickStatus.IsRight = true;
                    }
                    else if (distanceVector.x < 0 && CheckFlickDirection(angle, TouchManager.FlickType.LEFT))
                    {
                        flickStatus.IsLeft = true;
                    }
                }
                if (_flickThresholdY < Mathf.Abs(distanceVector.y))
                {
                    if (distanceVector.y > 0 && CheckFlickDirection(angle, TouchManager.FlickType.UP))
                    {
                        flickStatus.IsUp = true;
                    }
                    else if (distanceVector.y < 0 && CheckFlickDirection(angle, TouchManager.FlickType.BOTTOM))
                    {
                        flickStatus.IsDown = true;
                    }
                }
            }

            return flickStatus;
        }


        /// <summary>
        /// ベクトルを取得
        /// </summary>
        private static Vector3 GetVec(PointerEventData eventData)
        {
            return eventData.position - eventData.pressPosition;
        }

        /// <summary>
        /// アングルが該当のフリックタイプに該当するかをチェック。スクロールしたい時に暴発が多いため
        /// </summary>
        private static bool CheckFlickDirection(float angle , TouchManager.FlickType flickType)
        {
            switch (flickType)
            {
                case TouchManager.FlickType.UP:
                    // angleが360度で返ってくるのであればFLICK_RIGHT_UP_ANGLE以下、もしくはFLICK_LEFT_UP_ANGLE以上で成立でOK.
                    return angle < TouchManager.FLICK_RIGHT_UP_ANGLE || angle > TouchManager.FLICK_LEFT_UP_ANGLE;
                case TouchManager.FlickType.BOTTOM:
                    return angle > TouchManager.FLICK_RIGHT_DOWN_ANGLE && angle < TouchManager.FLICK_LEFT_DOWN_ANGLE;
                case TouchManager.FlickType.LEFT:
                    return angle > TouchManager.FLICK_LEFT_DOWN_ANGLE && angle < TouchManager.FLICK_LEFT_UP_ANGLE;
                case TouchManager.FlickType.RIGHT:
                    return angle < TouchManager.FLICK_RIGHT_DOWN_ANGLE && angle > TouchManager.FLICK_RIGHT_UP_ANGLE;
            }

            return false;
        }

        /// <summary>
        /// FlickStatusに基づきSEを再生する
        /// </summary>
        /// <param name="status"></param>
        public static void PlaySe(FlickStatus status)
        {
            if (status.IsLeft || status.IsRight)
                AudioManager.Instance.PlaySe(AudioId.SFX_UI_SWIPE_01);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            _eventCallback = null;
        }
    }
}
