using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 交換所アイテム入手箇所ダイアログ
    /// </summary>
    public sealed class DialogItemAvailablePlace : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// アイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _icon;

        /// <summary>
        /// アイテム名
        /// </summary>
        [SerializeField]
        private TextCommon _itemName;

        /// <summary>
        /// 説明
        /// </summary>
        [SerializeField]
        private TextCommon _description;

        /// <summary>
        /// 所持数
        /// </summary>
        [SerializeField]
        private TextCommon _itemNum;

        /// <summary>
        /// 使用期限
        /// </summary>
        [SerializeField]
        private TextCommon _limitText;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く関数
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        public static void Open(GameDefine.ItemCategory itemCategory, int itemId)
        {
            const string PATH = ResourcePath.DIALOG_ITEM_AVAILABLE_PLACE;
            var dialogContent = LoadAndInstantiatePrefab<DialogItemAvailablePlace>(PATH);
            dialogContent.Setup(itemCategory, itemId);

            var data = dialogContent.CreateDialogData();
            data.Title = TextId.Outgame0047.Text();
            data.CenterButtonText = TextId.Common0007.Text();

            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        private void Setup(GameDefine.ItemCategory itemCategory, int itemId)
        {
            _icon.SetData(itemCategory, itemId, 0, numDisp: false);
            _icon.SetButtonEnabled(false);
            _itemName.text = GetItemName(itemCategory, itemId);
            _itemNum.text = GallopUtil.GetHaveItemNum(itemCategory, itemId).ToCommaSeparatedString();
            _description.text = GallopUtil.GetItemAvailablePlace(itemCategory, itemId);

            var endDateTime = GallopUtil.GetEndDateTime(itemCategory, itemId);
            if (TimeUtil.IsInTerm(GameDefine.ITEM_USE_LIMIT_IGNORE_DATE_TIME, null, endDateTime))
            {
                _limitText.FontColor = FontColorType.Brown;
                _limitText.text = TextId.Common0099.Text(); //なし
                _limitText.UpdateColor();
            }
            else
            {
                _limitText.FontColor = FontColorType.Orange;
                var limitText = GallopUtil.GetLimitTimeText(itemCategory, itemId, isForce: true);
                _limitText.text = string.Format(TextId.Common0015.Text(), limitText);
                _limitText.UpdateColor();
            }
        }

        /// <summary>
        /// アイテム名を取得
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        private static string GetItemName(GameDefine.ItemCategory itemCategory, int itemId)
        {
            var itemName = GallopUtil.GetItemName(itemCategory, itemId);

            // 専用ピースはカードのタイトル名をつける
            if (itemCategory == GameDefine.ItemCategory.CARD_PIECE)
            {
                const string FORMAT = "<size=32>{0}</size> {1}";
                var masterCard = MasterDataManager.Instance.masterCardData.Get(itemId);
                if (masterCard == null)
                    return itemName;

                itemName = string.Format(FORMAT, masterCard.Titlename, itemName);
            }

            return itemName;
        }

        #endregion
    }
}
