using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// アイテム詳細確認時のダイアログ表示
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogItemInformation : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// アイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _icon;

        /// <summary>
        /// 交換所名
        /// </summary>
        [SerializeField]
        private TextCommon _itemName;

        /// <summary>
        /// 交換所説明
        /// </summary>
        [SerializeField]
        private TextCommon _description;

        /// <summary>
        /// 所持数
        /// </summary>
        [SerializeField]
        private TextCommon _itemNum;

        /// <summary>
        /// 使用期限
        /// </summary>
        [SerializeField]
        private TextCommon _limitText;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く関数
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        public static void Open(GameDefine.ItemCategory itemCategory, int itemId)
        {
            const string PATH = ResourcePath.DIALOG_ITEM_INFORMATION;
            var dialogContent = LoadAndInstantiatePrefab<DialogItemInformation>(PATH);
            dialogContent.Setup(itemCategory, itemId);

            var data = dialogContent.CreateDialogData();
            data.Title = TextId.Outgame0047.Text();
            data.CenterButtonText = TextId.Common0007.Text();

            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        private void Setup(GameDefine.ItemCategory itemCategory, int itemId)
        {
            _icon.SetData(itemCategory, itemId, 0, numDisp: false);
            _icon.SetButtonEnabled(false);
            _itemName.text = GetItemName(itemCategory, itemId);
            _itemNum.text = GallopUtil.GetHaveItemNum(itemCategory, itemId).ToCommaSeparatedString();
            _description.text = GallopUtil.GetItemDesc(itemCategory, itemId);
            SetupLimitText(_limitText, itemCategory, itemId);
        }

        /// <summary>
        /// 「使用期限」のテキストのセットアップ
        /// </summary>
        public static void SetupLimitText(TextCommon outText, GameDefine.ItemCategory itemCategory, int itemId)
        {
            var endDateTime = GallopUtil.GetEndDateTime(itemCategory, itemId);
            if (TimeUtil.IsInTerm(GameDefine.ITEM_USE_LIMIT_IGNORE_DATE_TIME, null, endDateTime))
            {
                outText.FontColor = FontColorType.Brown;
                outText.text = TextId.Common0099.Text(); //なし
                outText.UpdateColor();
            }
            else
            {
                var remainTime = TimeUtil.ToUnixTime(endDateTime) - TimeUtil.GetServerTimeStamp();
                var remainTimeParam = new Cute.Core.TimeUtil.TimeLeftParam(remainTime, 0);
                if (remainTimeParam.day == 0) //残り24時間
                {
                    outText.FontColor = FontColorType.Orange;
                }
                else
                {
                    outText.FontColor = FontColorType.Brown;
                }

                var limitText = GallopUtil.GetLimitTimeText(itemCategory, itemId, isForce: true);
                outText.text = limitText;
                outText.UpdateColor();
            }
        }

        /// <summary>
        /// アイテム名を取得
        /// </summary>
        /// <param name="itemCategory"></param>
        /// <param name="itemId"></param>
        private static string GetItemName(GameDefine.ItemCategory itemCategory, int itemId)
        {
            var itemName = GallopUtil.GetItemName(itemCategory, itemId);

            // 専用ピースはカードのタイトル名をつける
            if (itemCategory == GameDefine.ItemCategory.CARD_PIECE)
            {
                const string FORMAT = "{0} {1}";
                var masterCard = MasterDataManager.Instance.masterCardData.Get(itemId);
                if (masterCard == null)
                    return itemName;

                itemName = string.Format(FORMAT, masterCard.Titlename, itemName);
            }

            return itemName;
        }

        #endregion
    }
}
