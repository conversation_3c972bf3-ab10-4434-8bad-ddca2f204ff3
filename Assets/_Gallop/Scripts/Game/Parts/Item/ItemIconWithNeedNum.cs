using UnityEngine;
using System;

namespace Gallop
{
    [AddComponentMenu("")]
    public class ItemIconWithNeedNum : MonoBehaviour
    {
        public class SetupInfo
        {
            public SetupInfo(GameDefine.ItemCategory itemCategory, int itemId, int needNum, bool animationEnabled = false, Action onClick = null)
            {
                ItemCategory = itemCategory;
                ItemId = itemId;
                NeedNum = needNum;
                AnimationEnabled = animationEnabled;
                OnClick = onClick;
            }
            public GameDefine.ItemCategory ItemCategory;
            public int ItemId;
            public int NeedNum;
            public bool AnimationEnabled;
            public Action OnClick = null;
        }

        /// <summary>
        /// 必要数が一定の長さを超えると入らないので長さを変更する値
        /// </summary>
        private const int TEXT_SCALE_CHANGE_LENGTH = 7;

        [SerializeField]
        private ItemIcon _itemIcon = null;
        [SerializeField]
        private TextCommon _textNeedCount = null;
        [SerializeField]
        private Transform _animRoot = null;
        /// <summary> 右上に表示する虫眼鏡画像 </summary>
        [SerializeField]
        private GameObject _loupeObject = null;

        public bool HasEnoughItems { get { return _haveCount >= _needNum; } }

        private GameDefine.ItemCategory _itemCategory;
        private int _itemId;
        private int _needNum;
        private IconBase.SizeType _sizeType;
        private bool _animationEnabled = false;
        private int _haveCount;
        private Action _onClick = null;
        // フレームのサイズはアイコンサイズに対して1.2倍
        private const float ItemFrameScale = 1.2f;
        // TATのパス
        private const string ItemIconFrameTimelinePath = ResourcePath.FLASH_COMBINE_ROOT + "Timeline/Chara/tat_chara_icon_itemframe00";
        // TAT生成先変数
        private TweenAnimationTimelineComponent _tweenIcon = null;

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterPathTween(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ItemIconFrameTimelinePath);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(GameDefine.ItemCategory itemCategory, int itemId, int needNum, IconBase.SizeType sizeType = IconBase.SizeType.Common_M, bool animationEnabled = false, Action onClick = null)
        {
            _itemCategory = itemCategory;
            _itemId = itemId;
            _needNum = needNum;
            _sizeType = sizeType;
            _animationEnabled = animationEnabled;
            _haveCount = GallopUtil.GetHaveItemNum(_itemCategory, _itemId);
            _onClick = onClick;

            if (_tweenIcon == null && _animationEnabled)
            {
                var prefab = Instantiate<GameObject>(ResourceManager.LoadOnView<GameObject>(ItemIconFrameTimelinePath), _animRoot);
                _tweenIcon = prefab.GetComponent<TweenAnimationTimelineComponent>();
                _tweenIcon.SetActiveWithCheck(false);
            }

            ApplyView();
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="info"></param>
        public void Setup(SetupInfo info)
        {
            Setup(info.ItemCategory, info.ItemId, info.NeedNum, animationEnabled: info.AnimationEnabled, onClick: info.OnClick);
        }

        /// <summary>
        /// 見た目反映
        /// </summary>
        private void ApplyView()
        {
            SetActiveLoupe(false);
            _itemIcon.SetData(_itemCategory, _itemId, 0, numDisp: false, isInfoPop: true, onClick: _onClick);
            _itemIcon.SetSize(_sizeType);
            int haveNumLength = _haveCount.ToString().Length;
            int needNumLength = _needNum.ToString().Length;
            _textNeedCount.text = TextId.Common0177.Format(_haveCount, _needNum);
            //特定の長さ以上の場合はアンカーを中央に設定する。アイコンから少しはみ出すがそれは許容。（はみ出し設定はPrefab側）
            if ((haveNumLength + needNumLength) >= TEXT_SCALE_CHANGE_LENGTH)
            {
                _textNeedCount.FontSize = TextFormat.GetFontSize(TextFormat.FontSize.Size_25);
                _textNeedCount.alignment = TextAnchor.LowerCenter;
            }
            else
            {
                _textNeedCount.FontSize = TextFormat.GetFontSize(TextFormat.FontSize.Size_28);
                _textNeedCount.alignment = TextAnchor.LowerRight;
            }

            _textNeedCount.FontColor = HasEnoughItems ? FontColorType.Brown : FontColorType.Warning;
            _itemIcon.SetButtonColorEnough(HasEnoughItems);

            if (_onClick != null)
            {
                // コールバックがあるならタップにSEを付ける
                _itemIcon.Button.SeType = ButtonCommon.ButtonSeType.DecideM01;
            }

            ApplyAnimation();
        }

        /// <summary>
        /// アニメーション反映
        /// </summary>
        private void ApplyAnimation()
        {
            if(!_animationEnabled)
            {
                return;
            }
            if(_tweenIcon==null)
            {
                return;
            }

            _tweenIcon.SetActiveWithCheck(HasEnoughItems);
            _tweenIcon.GetComponent<RectTransform>().sizeDelta = IconBase.GetSizeTypeVector2(_itemIcon.CurrentSizeType) * ItemFrameScale;
            if (HasEnoughItems)
            {
                _tweenIcon.Stop();
                _tweenIcon.PlayLoop();
            }
        }

        /// <summary>
        /// 必要数変更
        /// </summary>
        /// <param name="needNum"></param>
        public void SetNeedNum(int needNum)
        {
            _needNum = needNum;
            ApplyView();
        }

        /// <summary>
        /// 所持数変更
        /// </summary>
        public void SetHaveCount(int haveCount)
        {
            _haveCount = haveCount;
            ApplyView();
        }

        /// <summary>
        /// 虫眼鏡画像の表示/非表示を切り替える
        /// (仕様的には必要数が足りない時に表示しているみたいだが影響範囲の関係で使用する箇所で明示的に呼んで表示させる)
        /// </summary>
        /// <param name="isActive"> 表示させるかどうか </param>
        public void SetActiveLoupe(bool isActive)
        {
            _loupeObject.SetActiveWithCheck(isActive);
        }

        /// <summary>
        /// 長押しした時の処理を設定
        /// </summary>
        public void SetOnBeginLongTap(Action onBeginLongTap)
        {
            _itemIcon.OnBeginLongTap = onBeginLongTap;
        }

        /// <summary>
        /// 長押しが終わった時の処理を設定
        /// </summary>
        public void SetOnEndLongTap(Action onEndLongTap)
        {
            _itemIcon.OnEndLongTap = onEndLongTap;
        }
    }
}