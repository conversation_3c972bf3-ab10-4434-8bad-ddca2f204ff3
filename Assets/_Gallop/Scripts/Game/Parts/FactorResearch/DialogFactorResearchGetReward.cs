using System;
using System.Linq;
using UnityEngine;
using DG.Tweening;

namespace Gallop
{
    using AnimSequenceHelper;
    
    /// <summary>
    /// 因子研究：研究成果報酬獲得演出
    /// </summary>
    [AddComponentMenu("")]
    public class DialogFactorResearchGetReward : DialogInnerBase
    {
        #region 定数、enum

        private const string MOT_TURN = "MOT_mc_turn00";
        private const string LABEL_DIGIT1 = "in_turn01";
        private const string LABEL_DIGIT2 = "in_turn10";

        private const string MOT_DIGIT = "MOT_num_img_turn00";
        private const string OBJ_DIGIT1 = "OBJ_num_img_turn00";
        private const string OBJ_DIGIT2 = "OBJ_num_img_turn01";

        private const int MINI_CHARA_RESOLUTION = 670;

        /// <summary>
        /// 描画順制御
        /// </summary>
        /// <remarks>値は10000以内で設定すること</remarks>
        private enum SortOrder
        {
            MiniCharaBgEffect = 10,
            ItemList = 50,
            RewardFlash = 100,
            MiniChara = 200,
            TitleFlash = 300,
            Effect = 300,
            UI = 400,
        }
        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.WITHOUT_FRAME;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();

            dialogData.AutoClose = false;
            dialogData.CancelSe = AudioId.INVALID;
            dialogData.DestroyCallBack = OnDestroyDialog;
            dialogData.DisableAnimation = true;

            return dialogData;
        }

        #endregion

        #region SerializeField

        /// <summary> フレームのルート </summary>
        [SerializeField]
        private GameObject _frameRoot;

        /// <summary> 「研究成果獲得報酬」タイトルFlashのルート </summary>
        [SerializeField]
        private GameObject _titleRoot;

        /// <summary> 獲得演出Flashのルート </summary>
        [SerializeField]
        private GameObject _flashRoot;

        /// <summary> 閉じるボタン </summary>
        [SerializeField]
        private ButtonCommon _closeButton;

        /// <summary> 報酬一覧のヘッダーの親オブジェクト </summary>
        [SerializeField]
        private GameObject _rewardListHeaderRoot;
        
        /// <summary> ボックスガチャ残数表示 </summary>
        [SerializeField] private TextCommon _rewardRemainText;
        
        /// <summary> ボックスガチャ詳細iボタン </summary>
        [SerializeField] private ButtonCommon _rewardInfoButton;

        /// <summary> 報酬一覧の親オブジェクト </summary>
        [SerializeField]
        private GameObject _rewardListRoot;

        /// <summary> 報酬一覧リスト </summary>
        [SerializeField]
        private PartsDisplayRewardItemList _partsFactorResearchRewardItemList;
        
        /// <summary> UIをFlashより上に置く用Canvas </summary>
        [SerializeField] private Canvas _flashOverlayCanvas;
        
        /// <summary> UIを置く用Canvas </summary>
        [SerializeField] private Canvas _itemListCanvas;

        /// <summary> キャラをエフェクトより下にする用Canvas </summary>
        [SerializeField] private Canvas _miniCharaCanvas;
        
        /// <summary> スキップボタン </summary>
        [SerializeField] private ButtonCommon _skipButton;

        /// <summary> ミニキャラ表示用のRawImageとキャラエフェクトのCanvasを保持しているParts </summary>
        [SerializeField] private PartsFactorResearchGetRewardMiniCharaImageHolder[] _miniCharaImageHolderArray;
        #endregion

        #region メンバ変数

        /// <summary> 演出背景のフレーム </summary>
        private AnimationFrameCross _animationFrameCross = null;

        /// <summary> 演出上部のミニキャラViewer </summary>
        private MiniCharaViewer[] _miniCharaViewerArray;

        /// <summary> 「研究成果報酬」のFlash </summary>
        private FlashPlayer _titleFlashPlayer;

        /// <summary> 報酬獲得演出のFlash </summary>
        private FlashActionPlayer _getRewardFlashActionPlayer;

        /// <summary> 閉じた時に実行 </summary>
        private System.Action _onDestroy = null;
        
        /// <summary> ミニキャラまわりに出すパーティクル </summary>
        private ParticleSystem _miniCharaEffectParticle;

        /// <summary> 表示用データ </summary>
        private IDialogFactorResearchGetRewardVM _dialogFactorResearchGetRewardVM;
        #endregion

        #region プロパティ
        private PartsFactorResearchGetRewardMiniCharaImageHolder TargetMiniCharaImageHolder =>
            _miniCharaImageHolderArray[_dialogFactorResearchGetRewardVM.MiniCharaImageHolderTargetIndex];
        #endregion

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.FACTOR_RESEARCH_GET_REWARD_TITLE_FLASH);
            register.RegisterPathWithoutInfo(ResourcePath.FACTOR_RESEARCH_GET_REWARD_FLASH);
            // 通常版・当たり報酬が無い場合用のキャラ周りのエフェクトパス
            register.RegisterPathWithoutInfo(ResourcePath.DefaultFactorResearchGetRewardCharaParticle);
            if (WorkDataManager.Instance.FactorResearchData.IsSpecialEvent)
            {
                var specialEventType = WorkDataManager.Instance.FactorResearchData.SpecialEventType;
                // 特別版では当たり報酬がある場合には専用のエフェクトを使用する
                register.RegisterPathWithoutInfo(ResourcePath.GetFactorResearchGetRewardCharaParticle(specialEventType));
            }
            ItemIcon.RegisterPath(register);

            AnimationFrameCross.RegisterDownload(register);

            // SE
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_FACTOR_RESEARCH_GET_REWARD);
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_FACTOR_RESEARCH_TITLE_IN);
        }

        /// <summary>
        /// 報酬獲得演出ダイアログを開く
        /// </summary>
        public static void Play(IFactorResearchDisplayRewardDataModel displayRewardDataModel, System.Action onDestroy = null)
        {
            var component = LoadAndInstantiatePrefab<DialogFactorResearchGetReward>(ResourcePath.DIALOG_FACTOR_RESEARCH_GET_REWARD);
            component._onDestroy = onDestroy;
            var dialogData = component.CreateDialogData();
            DialogManager.PushDialog(dialogData);

            var specialEventType = WorkDataManager.Instance.FactorResearchData.SpecialEventType;
            // 当たり報酬がある場合はイベント種別によってエフェクトを変える
            var containWinItemCharaParticlePath = ResourcePath.GetFactorResearchGetRewardCharaParticle(specialEventType);
            // 当たり報酬が無い場合は通常エフェクトを指定する
            var notContainWinItemCharaParticlePath = ResourcePath.DefaultFactorResearchGetRewardCharaParticle;
            var vm = new DialogFactorResearchGetRewardVM(displayRewardDataModel, containWinItemCharaParticlePath, notContainWinItemCharaParticlePath);
            component.Setup(vm);
            component.PlayIn();
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(IDialogFactorResearchGetRewardVM vm)
        {
            _dialogFactorResearchGetRewardVM = vm;

            // ミニキャラ表示用のRawImage群を全て非表示にしておく
            for (int i = 0; i < _miniCharaImageHolderArray.Length; i++)
            {
                _miniCharaImageHolderArray[i].SetActiveWithCheck(false);
            }

            // 閉じるボタンセットアップ
            _closeButton.SetOnClick(PlayOut);
            _closeButton.SetActiveWithCheck(false);

            // スキップボタン
            _skipButton.SetActiveWithCheck(false);
            
            // ミニキャラ初期化
            SetupMiniChara();

            // 背景フレームの生成
            _animationFrameCross = UIUtil.CreateAnimationFrameCross(_frameRoot.transform, DialogHash);
            _animationFrameCross.transform.SetAsFirstSibling();

            // 報酬一覧の生成
            SetupRewardListHeader();
            _partsFactorResearchRewardItemList.SetupList(_dialogFactorResearchGetRewardVM.RewardInfoArray);

            _rewardListHeaderRoot.SetActiveWithCheck(false);
            _rewardListRoot.SetActiveWithCheck(false);
        }

        /// <summary>
        /// ミニキャラ初期化
        /// </summary>
        private void SetupMiniChara()
        {
            // 再生が必要な分だけアニメーションデータを取得
            var animationDataArray = _dialogFactorResearchGetRewardVM.MiniCharaAnimationDataArray;
            // アニメーションデータに紐づくMiniCharaViewerを生成
            _miniCharaViewerArray = animationDataArray
                .Select((data, i) => MiniCharaViewer.Create(data.CharaId, data.DressId, string.Empty, i, resolution: MINI_CHARA_RESOLUTION))
                .ToArray();

            // 生成したMiniCharaViewerからレンダリングされる表示を反映する先に登録
            var renderTextureArray = _miniCharaViewerArray
                .Select(miniCharaViewer => miniCharaViewer.RenderTexture)
                .ToArray();
            TargetMiniCharaImageHolder.Setup(renderTextureArray, _dialogFactorResearchGetRewardVM.PrizeType);

            // 各ミニキャラの初期状態を設定
            for (int i = 0; i < _miniCharaViewerArray.Length; i++)
            {
                var miniCharaViewer = _miniCharaViewerArray[i];
                var animationData = animationDataArray[i];
                miniCharaViewer.Model.PlayMotion(animationData.StandMotion, isForce: true, bodyBlendTime : 0f);
                miniCharaViewer.Model.transform.localRotation = Quaternion.Euler(0, animationData.CharaAngle, 0);
            }
        }

        /// <summary>
        /// 報酬リストヘッダー部分初期化
        /// </summary>
        private void SetupRewardListHeader()
        {
            var boxRewardRemainNum = WorkDataManager.Instance.FactorResearchData.GetBoxRewardRemainNum();
            var boxRewardMaxNum = WorkDataManager.Instance.FactorResearchData.GetBoxRewardMaxNum();
            _rewardRemainText.text = TextUtil.Format(TextId.Common0089.Text(), boxRewardRemainNum, boxRewardMaxNum);
            
            _rewardInfoButton.SetOnClick(DialogFactorResearchRewardList.Open);
        }

        /// <summary>
        /// FlashActionPlayerを生成
        /// </summary>
        private FlashActionPlayer CreateFlashActionPlayer(string path, Transform parent)
        {
            var prefab = ResourceManager.LoadOnHash<GameObject>(path, DialogHash);
            var flashActionPlayer = GameObject.Instantiate(prefab, parent).GetComponent<FlashActionPlayer>();

            var flashPlayer = flashActionPlayer.LoadFlashPlayer();
            flashPlayer.Init();
            flashActionPlayer.gameObject.SetLayerRecursively(UIManager.DialogCanvas.gameObject.layer);

            return flashActionPlayer;
        }

        /// <summary>
        /// ダイアログ破棄時コールバック
        /// </summary>
        private void OnDestroyDialog()
        {
            if (!_miniCharaViewerArray.IsNullOrEmpty())
            {
                for (int i = 0; i < _miniCharaViewerArray.Length; i++)
                {
                    if (_miniCharaViewerArray[i] == null) continue;

                    Destroy(_miniCharaViewerArray[i].gameObject);
                    _miniCharaViewerArray[i] = null;
                }
            }

            _miniCharaViewerArray = null;
            _onDestroy?.Invoke();
        }

        /// <summary>
        /// スキップボタン押下時の挙動
        /// </summary>
        private void OnClickSkip()
        {
            _skipButton.SetActiveWithCheck(false);
            
            _partsFactorResearchRewardItemList.SkipPlayInRewardListItem();
        }
        
        #region 描画順

        /// <summary>
        /// 全UIのソートオーダーを設定
        /// </summary>
        private void UpdateSortOrderAll(int baseSortOrder, string sortingLayerName)
        {
            _miniCharaCanvas.sortingLayerName = sortingLayerName;
            _miniCharaCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.MiniChara);
            TargetMiniCharaImageHolder.SetEffectSorting(sortingLayerName, GetSortOrderValue(baseSortOrder, SortOrder.Effect));
            _itemListCanvas.sortingLayerName = sortingLayerName;
            _itemListCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.ItemList);
            _flashOverlayCanvas.sortingLayerName = sortingLayerName;
            _flashOverlayCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.UI);

            _partsFactorResearchRewardItemList.SetSortOrder(GetSortOrderValue(baseSortOrder, SortOrder.ItemList), sortingLayerName);

            if (_titleFlashPlayer != null)
            {
                _titleFlashPlayer.SortLayer = sortingLayerName;
                _titleFlashPlayer.SortOffset = GetSortOrderValue(baseSortOrder, SortOrder.TitleFlash);
            }
            if (_getRewardFlashActionPlayer != null)
            {
                _getRewardFlashActionPlayer.SetSortLayer(sortingLayerName);
                _getRewardFlashActionPlayer.SetSortOffset(GetSortOrderValue(baseSortOrder, SortOrder.RewardFlash));
            }
            
            if (_miniCharaEffectParticle != null)
            {
                UIUtil.SetParticleSortOrder(_miniCharaEffectParticle.gameObject, sortingLayerName, GetSortOrderValue(baseSortOrder, SortOrder.MiniCharaBgEffect));
            }

            // SortOrder値を取得
            int GetSortOrderValue(int baseOrder, SortOrder sortOrder)
            {
                return baseOrder + (int)sortOrder;
            }
        }


        #endregion

        #region アニメーション

        /// <summary>
        /// イリアニメ再生開始
        /// </summary>
        private void PlayIn()
        {
            const float PLAY_IN_TITLE_INTERVAL = 0.133f;
            const float PLAY_IN_CHARA_INTERVAL = 0.1f;
            const float PLAY_IN_STOCK_INTERVAL = 0.133f;

            UIManager.Instance.LockGameCanvas();
            TargetMiniCharaImageHolder.SetActiveWithCheck(true);

            // 「研究成果報酬獲得」タイトルFlash
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.FACTOR_RESEARCH_GET_REWARD_TITLE_FLASH, DialogHash);
            _titleFlashPlayer = FlashLoader.Load(prefab, _titleRoot.transform);
            _titleFlashPlayer.gameObject.SetLayerRecursively(UIManager.DialogCanvas.gameObject.layer);

            // 報酬獲得演出のFlash
            _getRewardFlashActionPlayer = CreateFlashActionPlayer(ResourcePath.FACTOR_RESEARCH_GET_REWARD_FLASH, _flashRoot.transform);
            _getRewardFlashActionPlayer.SetOnInstantiatedParticle(particleArray => UIUtil.AdjustParticleScale(particleArray));

            // キャラまわりのパーティクル
            var miniEffect = ResourceManager.LoadOnHash<GameObject>(_dialogFactorResearchGetRewardVM.CharaEffectPath, DialogHash);
            _miniCharaEffectParticle = Instantiate(miniEffect, TargetMiniCharaImageHolder.GetEffectCanvasTransform())
                .GetComponentInChildren<ParticleSystem>();
            UIUtil.AdjustParticleScaleRecursively(_miniCharaEffectParticle.gameObject);
            _miniCharaEffectParticle.gameObject.SetLayerRecursively(UIManager.DialogCanvas.gameObject.layer);
            _miniCharaEffectParticle.gameObject.SetActiveWithCheck(false);
            
            UpdateSortOrderAll(UIManager.DialogCanvas.sortingOrder, UIManager.DialogCanvas.sortingLayerName);

            var sequence = DOTween.Sequence();
            sequence.AppendCallback(() =>
            {
                _animationFrameCross.PlayIn(AnimationFrameCross.DefineFrameType.FrameType04);
            });

            sequence.AppendInterval(PLAY_IN_TITLE_INTERVAL);
            sequence.AppendCallback(() =>
            {
                // タイトル
                if (_titleFlashPlayer != null)
                {
                    _titleFlashPlayer.Play(GameDefine.A2U_IN_LABEL);
                    AudioManager.Instance.PlaySe(AudioId.SFX_FACTOR_RESEARCH_TITLE_IN);
                }
            });

            sequence.AppendInterval(PLAY_IN_CHARA_INTERVAL);
            sequence.AppendCallback(() =>
            {
                PlayInChara();
            });

            sequence.AppendInterval(PLAY_IN_STOCK_INTERVAL);
            sequence.AppendCallback(() =>
            {
                const string EFF_IN2 = "eff_in2";

                // 報酬獲得演出
                if (_getRewardFlashActionPlayer != null &&
                    _getRewardFlashActionPlayer.FlashPlayer != null)
                {
                    _getRewardFlashActionPlayer.FlashPlayer.Play(GameDefine.A2U_IN_LABEL);

                    // 【in】再生開始時
                    _getRewardFlashActionPlayer.FlashPlayer.AddActionCallBack(GameDefine.A2U_IN_LABEL, _ =>
                    {
                        PlayInStock();
                        AudioManager.Instance.PlaySe(AudioId.SFX_FACTOR_RESEARCH_GET_REWARD);
                    }, AnimateToUnity.AnMotionActionTypes.Start);

                    // 【eff_in2】再生開始時
                    _getRewardFlashActionPlayer.FlashPlayer.AddActionCallBack(EFF_IN2, _ =>
                    {
                        PlayInRewardList();
                        _miniCharaEffectParticle.gameObject.SetActiveWithCheck(true);
                        if (!_miniCharaViewerArray.IsNullOrEmpty())
                        {
                            for (int i = 0; i < _miniCharaViewerArray.Length; i++)
                            {
                                var miniCharaViewer = _miniCharaViewerArray[i];
                                if (miniCharaViewer == null) continue;
                                miniCharaViewer.Model.PlayMotion(_dialogFactorResearchGetRewardVM.MiniCharaMotionLabelArray[i]);
                            }
                        }
                    }, AnimateToUnity.AnMotionActionTypes.Start);
                }
            });

        }

        /// <summary>
        /// ストック数(フラスコ)演出のラベル再生
        /// </summary>
        private void PlayInStock()
        {
            var rewardInfoArray = _dialogFactorResearchGetRewardVM.RewardInfoArray;
            var digit1 = rewardInfoArray.Length % 10;
            var digit2 = rewardInfoArray.Length / 10;
            var turnMot = _getRewardFlashActionPlayer.FlashPlayer.GetMotion(MOT_TURN);
            var label = digit2 > 0 ? LABEL_DIGIT2 : LABEL_DIGIT1;
            turnMot?.SetMotionPlay(label);

            var digit1Obj = _getRewardFlashActionPlayer.FlashPlayer.GetObj(OBJ_DIGIT1);
            var digit1Mot = _getRewardFlashActionPlayer.FlashPlayer.GetMotion(MOT_DIGIT, rootGameObject:digit1Obj.GameObject);
            digit1Mot?.SetMotionPlay("t" + digit1);

            if (digit2 > 0)
            {
                var digit2Obj = _getRewardFlashActionPlayer.FlashPlayer.GetObj(OBJ_DIGIT2);
                var digit2Mot = _getRewardFlashActionPlayer.FlashPlayer.GetMotion(MOT_DIGIT, rootGameObject:digit2Obj.GameObject);
                digit2Mot?.SetMotionPlay("t" + digit2);
            }
        }

        /// <summary>
        /// ミニキャラ入り・モーション再生
        /// </summary>
        private void PlayInChara()
        {
            if (_miniCharaViewerArray.IsNullOrEmpty()) return;

            TargetMiniCharaImageHolder.Show(_dialogFactorResearchGetRewardVM.PrizeType);
            for (int i = 0; i < _miniCharaViewerArray.Length; i++)
            {
                var miniCharaViewer = _miniCharaViewerArray[i];
                var animationData = _dialogFactorResearchGetRewardVM.MiniCharaAnimationDataArray[i];
                miniCharaViewer.Model.PlayMotion(animationData.InMotion);
            }
        }

        /// <summary>
        /// 報酬リスト入り
        /// </summary>
        private void PlayInRewardList()
        {
            var seq = DOTween.Sequence();
            var totalDelay = 0f;

            totalDelay += GameDefine.BASE_FPS_TIME * 4f;
            // ヘッダー・リスト背景イリ
            seq.InsertCallback(totalDelay, () =>
                {
                    _rewardListHeaderRoot.SetActiveWithCheck(true);
                    _rewardListRoot.SetActiveWithCheck(true);
                })
                .Join(_rewardListHeaderRoot.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade)
                .Join(_rewardListRoot.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade);
            
            // 報酬リストアイコンイリ (ここからSkip許可)
            seq.AppendCallback(() =>
            {
                UIManager.Instance.UnlockGameCanvas();
                _skipButton.SetActiveWithCheck(true);
                _skipButton.SetOnClick(OnClickSkip);
                _partsFactorResearchRewardItemList.PlayInRewardListItem(OnLastItemInCallback, OnCompleteRewardListAnim);
            });
        }

        private void OnLastItemInCallback()
        {
            // 報酬を表示し終わったら閉じるボタン入り
            _closeButton.SetActiveWithCheck(true);
            DOTween.Sequence().Append(_closeButton.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade);

            // スキップイベントを削除
            _skipButton.SetOnClick(null);
        }

        private void OnCompleteRewardListAnim()
        {
            _skipButton.SetActiveWithCheck(false);
        }

        /// <summary>
        /// ハケアニメ再生
        /// </summary>
        private void PlayOut()
        {
            UIManager.Instance.LockGameCanvas();

            _animationFrameCross.PlayOut();
            _titleFlashPlayer.Play(GameDefine.A2U_OUT_LABEL);
            
            _miniCharaEffectParticle.gameObject.SetActiveWithCheck(false);
            
            const TweenAnimation.PresetType PRESET = TweenAnimation.PresetType.PartsOutMoveAndFade;
            var seq = DOTween.Sequence();
            seq.Join(TargetMiniCharaImageHolder.gameObject, PRESET)
                .Join(_closeButton.gameObject, PRESET)
                .Join(_rewardListRoot, PRESET);
            
            if (GetDialog() is DialogCommon dialogCommon)
            {
                dialogCommon.DialogData.DisableAnimation = false;
                dialogCommon.Close(UIManager.Instance.UnlockGameCanvas);
            }
        }

        #endregion

    }

}
