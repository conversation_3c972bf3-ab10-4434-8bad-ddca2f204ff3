using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// UIPartsの参照復旧を半自動で行いたい場合に使用するインターフェース
    /// </summary>
    public interface IUIAssetReconnector
    {
        /// <summary>
        /// 参照復旧を実行
        /// </summary>
        void ReconnectReference();

#if UNITY_EDITOR
        /// <summary>
        /// AssetのImport時に必要な変換処理を実行
        /// </summary>
        ///
        /// <returns>
        /// trueを返すとアセットが保存される。
        /// trueを返し続けるとインポートが無限ループするので注意
        /// </returns>
        bool ConvertOnAssetImport(string assetFilePath);
#endif
    }
}
