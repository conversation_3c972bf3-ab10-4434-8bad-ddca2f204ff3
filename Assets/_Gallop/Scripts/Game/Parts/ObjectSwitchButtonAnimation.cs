namespace Gallop
{
    /// <summary>
    /// ボタンのアニメーション管理クラス
    /// </summary>
    public class ObjectSwitchButtonAnimation : ButtonCommonAnimation
    {
        private const string IN_ANIMATION_NAME = "In";
        private const string OUT_ANIMATION_NAME = "Out";

        //プッシュ中かそれ以外かを判定する
        private bool _isIn = false;
        
        // ベースとなるツイーンのアニメーション
        public TweenAnimationTimelineComponent TimelineAnimation = null;
        public ButtonCommon TargetButton = null; //アニメ対象のボタン

        #region メソッド
        /// <summary>
        /// アニメーション中かどうか
        /// </summary>
        /// <returns></returns>
        public override bool IsAnimating()
        {
            return TimelineAnimation != null ? TimelineAnimation.IsPlaying() : false;
        }

        public override void OnPointerUp()
        {
            PlayAnim(false);
        }

        public override void OnPointerClick()
        {
            //決定した時には特殊なアニメーション遷移
            //もしボタンが非アクティブ状態なら従来と同じアニメーションを再生
            if (TargetButton != null && !TargetButton.interactable)
            {
                _isIn = false;
                base.PlayBack();
                return;
            }

            PlayOutAnimation();
        }

        public override void OnDestroy()
        {
            //何もしなくてよい
        }
        
        /// <summary>
        /// アニメーション再生
        /// </summary>
        /// <param name="isForward"></param>
        protected override void PlayAnim(bool isForward)
        {
            //もしボタンが非アクティブ状態なら従来と同じアニメーションを再生
            if (TargetButton != null && !TargetButton.interactable)
            {
                if (isForward)
                {
                    _isIn = true;
                    base.PlayForward();
                }
                else
                {
                    _isIn = false;
                    base.PlayBack();
                }
                return;
            }

            if (isForward)
            {
                PlayForward();
            }
            else
            {
                PlayBack();
            }
        }

        /// <summary>
        /// 順再生
        /// </summary>
        protected override void PlayForward()
        {
            _isIn = true;
            
            // 順方向にアニメーション再生
            OnStartForwardAnim?.Invoke();
            TimelineAnimation.Play(IN_ANIMATION_NAME);
        }

        /// <summary>
        /// 逆再生
        /// </summary>
        protected override void PlayBack()
        {
            //ボタンを押してない時でもPointerExitが呼ばれてここに入ってくるので、フラグで判定。CurrentButtonのnullチェックでやった場合はinteractible = falseの時にダメだった
            if (!_isIn)
            {
                return;
            }

            _isIn = false;
            // 逆順方向にアニメーション再生（もとに戻す）
            OnStartBackwardAnim?.Invoke();
            TimelineAnimation.PlayBackwards(IN_ANIMATION_NAME);
        }
        
        /// <summary>
        /// 決定時のOut
        /// </summary>
        private void PlayOutAnimation()
        {
            _isIn = false;
            // 逆順方向にアニメーション再生
            TimelineAnimation.Stop(IN_ANIMATION_NAME);//In再生中なら停止
            TimelineAnimation.Play(OUT_ANIMATION_NAME);
        }

        #endregion
    }
}
