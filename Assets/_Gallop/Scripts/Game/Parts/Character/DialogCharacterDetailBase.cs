using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using static Gallop.StaticVariableDefine.OutGame.DialogCharacterDetailBase;

namespace Gallop
{
    /// <summary>
    /// ウマ娘詳細ダイアログ：基底クラス
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCharacterDetailBase : DialogInnerBase
    {
        #region DialogInnerBase
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Root;
        }
        #endregion

        #region 定義

        /// <summary>
        /// 表示に必要となるパラメータを定義
        /// </summary>
        public class SetupParameter : IPartsSetupParameter
        {
            public int CardId = GameDefine.INVALID_CARD_ID;

            private readonly IDictionary<Type, IPartsSetupParameter> _partsParameterDic = new Dictionary<Type, IPartsSetupParameter>();

            /// <summary>
            /// 指定された型のパーツパラメータを取得
            /// </summary>
            /// <typeparam name="T"></typeparam>
            /// <param name="callback"></param>
            public void GetPartsParameter<T>(Action<T> callback = null) where T : IPartsSetupParameter
            {
                if (_partsParameterDic.TryGetValue(typeof(T), out var parameter))
                {
                    var t = (T)parameter;
                    if (t != null)
                        callback?.Invoke(t);
                }
            }

            /// <summary>
            /// パーツパラメータを追加
            /// </summary>
            /// <param name="setupParameter"></param>
            public void AddPartsParameter(IPartsSetupParameter setupParameter)
            {
                _partsParameterDic.Add(setupParameter.GetType(), setupParameter);
            }

            /// <summary> ステータス </summary>
            public PartsCharacterDetailStatus.SetupParameter StatusParameter;

            /// <summary> 適性 </summary>
            public PartsCharacterDetailProper.SetupParameter ProperParameter;

            /// <summary> 成長率 </summary>
            public PartsCharacterDetailTalent.SetupParameter TalentParameter;

            /// <summary> スキル表示パラメータ </summary>
            public PartsSingleModeSkillList.SetupParameter SkillParameter;
            
            /// <summary> サポートカード </summary>
            public PartsCharacterDetailSupport.SetupParameter SupportCardParameter;

            /// <summary> 継承ウマ娘 </summary>
            public PartsCharacterDetailInherit.SetupParameter InheritedCharaParameter;

            /// <summary> 戦績表示パラメータ </summary>
            public PartsCharacterDetailRaceHistory.SetupParameter HistoryParameter;

            /// <summary> 出走履歴パラメータ </summary>
            public PartsCharacterDatailRaceResultList.SetupParameter ResultListParameter;

            /// <summary> 最大パラメータ表示ボタン </summary>
            public PartsCharacterDetailMaxParameterButton.SetupParameter MaxParameterButtonParameter;

            /// <summary> トグルグループのタイプ </summary>
            public ToggleGroupType ToggleGroupType = ToggleGroupType.Three;

            /// <summary> トグルグループをセットアップするか </summary>
            public bool SetupToggle = true;
            
            /// <summary> トレーナーガイドのダイアログ表示 </summary>
            public bool IsRaceFitAssistance = false;
            
            /// <summary> トレーナーガイドのおすすめスキルのラベル表示 </summary>
            public bool IsEnableRecommendLabel = false;
            /// <summary> スキル表示で進化スキルを表示するかどうか </summary>
            public bool IsDisplayUpgradeSkill = true;

            /// <summary> ダイアログの右ボタンを押した時のコールバック </summary>
            public Action OnClickRightButton;
        }

        /// <summary>
        /// 矢印ボタンをセットアップするためのパラメタ
        /// </summary>
        public class CharaSwitchButtonSetupParameter : IPartsSetupParameter
        {
            /// <summary>
            /// 次のキャラを表示するためのSetupParameterを取得
            /// </summary>
            public Action<Action<SetupParameter>> GetNextSetupParam { get; set; }

            /// <summary>
            /// 前のキャラを表示するためのSetupParameterを取得
            /// </summary>
            public Action<Action<SetupParameter>> GetPrevSetupParam { get; set; }

            /// <summary>
            /// 矢印ボタンでキャラを切り替えたときのコールバック
            /// </summary>
            public Action OnChangeChara { get; set; }

            /// <summary>
            /// ダイアログを閉じるときのコールバック
            /// 引数：矢印ボタンでキャラ切り替えが行われたかどうか
            /// </summary>
            public Action<bool> OnCloseDialogWithCharaSwitchButton { get; set; }
        }

        /// <summary>
        /// トグルグループのタイプ
        /// </summary>
        public enum ToggleGroupType
        {
            None,
            Two,
            Three,
        }

        /// <summary> キャラ切り替えをした際にUnloadする必要のあるリソース用のハッシュ </summary>
        /// <remarks> e.g. ヘッダーの背景画像、育成情報のサポカ画像など </remarks>
        protected static ResourceManager.ResourceHash HASH_UNLOAD_ON_CHANGE_CHARA = ResourceManager.ResourceHash.DialogCharacterDetail;

        #endregion

        #region SerializeField
        [Header("ステータス")]
        [SerializeField]
        protected PartsCharacterDetailStatus _statusParts = null;
        [SerializeField]
        protected PartsCharacterDetailProper _properParts = null;
        [SerializeField]
        protected PartsCharacterDetailTalent _talentParts = null;

        [Header("スキル")]
        [SerializeField]
        protected GameObject _skillToggleViewRoot = null;
        [SerializeField]
        protected PartsSingleModeSkillList _skillListParts = null;

        [Header("イベント")]
        [SerializeField]
        protected GameObject _eventListRoot = null;
        [SerializeField]
        protected LoopScroll _eventListScroll = null;


        [Header("育成情報")]
        [SerializeField]
        protected GameObject _trainingToggleViewRoot = null;
        [SerializeField]
        protected PartsCharacterDetailSupport _supportParts = null;
        [SerializeField]
        protected PartsCharacterDetailInherit _inheritParts = null;
        [SerializeField]
        protected PartsCharacterDetailRaceHistory _raceHistoryParts = null;
        [SerializeField]
        protected PartsCharacterDatailRaceResultList _raceResultListParts = null;

        [Header("最大パラメーター")]
        [SerializeField]
        protected PartsCharacterDetailMaxParameterButton _maxParameterButton;

        [Header("フリックトグル")]
        [SerializeField]
        private FlickToggleGroupCommon _categoryToggleGroup2 = null;
        [SerializeField]
        private FlickToggleGroupCommon _categoryToggleGroup3 = null;
        [SerializeField]
        protected FlickableObject _flickableObject = null;
        protected ToggleGroupType _toggleGroupType;

        [Header("スクロール")]
        [SerializeField]
        protected RectTransform _scrollRootRect;

        #endregion

        #region member
        private bool _isEventTabSetup = false;
        private int _cardId = 0;
        protected Action _onClickRightButton;

        protected Action<bool> _onCloseDialogWithCharaSwitchButton;
        protected PartsFlickableArrowButtonPair _partsArrowButtonPair;

        /// <summary> ダイアログを開いた後にキャラの切り替えが行われたかどうか </summary>
        protected bool _isDispCharaChanged = false;

        /// <summary> ダイアログを破棄したときに明示的なリソース破棄が必要かどうか </summary>
        private bool _needUnloadResourceOnDestroy = false;

        /// <summary> 表示キャラの変更中か </summary>
        private bool _isChangingDispChara = false;

        /// <summary>
        /// キャラ切り替えをした際にUnloadする必要のあるリソース用のハッシュ
        /// </summary>
        /// <remarks> #121659 スライドできないウマ娘詳細なら通常の DialogHash で管理 </remarks>
        protected ResourceManager.ResourceHash UnloadOnChangeCharaHash
        {
            get => _partsArrowButtonPair != null ? HASH_UNLOAD_ON_CHANGE_CHARA : DialogHash;
        }

        #endregion

        #region メソッド

        /// <summary>
        /// セットアップ
        /// </summary>
        public virtual void Setup(SetupParameter parameter)
        {
            if (parameter == null)
            {
                Debug.LogError("SetupParameterがnullです");
                return;
            }

            // 必要なら矢印ボタンのセットアップ
            SetupCharaSwitchButton(parameter);

            if (_raceHistoryParts != null && parameter.HistoryParameter != null)
            {
                _raceHistoryParts.Setup(parameter.HistoryParameter);
            }

            if (_raceResultListParts != null)
            {
                _raceResultListParts.SetActiveWithCheck(parameter.ResultListParameter != null);

                if (parameter.ResultListParameter != null)
                {
                    _raceResultListParts.Setup(parameter.ResultListParameter);
                }
            }

            if (_statusParts != null && parameter.StatusParameter != null)
            {
                _statusParts.Setup(parameter.StatusParameter);
            }

            if (_skillListParts != null && parameter.SkillParameter != null)
            {
                _skillListParts.Setup(parameter.SkillParameter);
            }
            
            if (_properParts != null && parameter.ProperParameter != null)
            {
                _properParts.Setup(parameter.ProperParameter);
            }

            if (_supportParts != null && parameter.SupportCardParameter != null)
            {
                _supportParts.Setup(parameter.SupportCardParameter, UnloadOnChangeCharaHash);
            }

            if (_inheritParts != null)
            {
                var isParameterExists = parameter.InheritedCharaParameter != null;
                _inheritParts.SetActiveWithCheck(isParameterExists);
                if (isParameterExists)
                    _inheritParts.Setup(parameter.InheritedCharaParameter, DialogHash);
            }

            if (_talentParts != null)
            {
                _talentParts.SetActiveWithCheck(parameter.TalentParameter != null); // パラメータなければ非表示
                if (parameter.TalentParameter != null)
                {
                    _talentParts.Setup(parameter.TalentParameter);
                }
            }

            if (_maxParameterButton != null)
            {
                const int BASE_RECT_BOTTOM = 206;
                const int BASE_RECT_BOTTOM_WITH_NOTIFICATION_FIELD = 255;
                const int BASE_RECT_BOTTOM_RACEFITASSISTANCE = 300;
                int rectBottom = BASE_RECT_BOTTOM;
                _maxParameterButton.SetActiveWithCheck(parameter.MaxParameterButtonParameter != null);
                if (parameter.MaxParameterButtonParameter != null)
                {
                    _maxParameterButton.Setup(parameter.MaxParameterButtonParameter);
                    rectBottom = BASE_RECT_BOTTOM_WITH_NOTIFICATION_FIELD;
                }

                if (parameter.IsRaceFitAssistance)
                {
                    rectBottom = BASE_RECT_BOTTOM_RACEFITASSISTANCE;
                }

                if (_scrollRootRect != null)
                {
                    _scrollRootRect.offsetMin = new Vector2(_scrollRootRect.offsetMin.x, rectBottom);
                }
            }

            _cardId = parameter.CardId;
            _isEventTabSetup = false;
            
            //TempDataにデータが存在するならイベントタブは先に設定していい(無ければタブクリックしたタイミングで通信してから設定)
            if (TempData.Instance.CardEventSkillDictionary != null)
            {
                if (TempData.Instance.CardEventSkillDictionary.ContainsKey(_cardId))
                {
                    SetUpEventTab();
                }
            }
            
            // トグルのセットアップ
            if (parameter.SetupToggle)
                SetupToggleGroup(parameter.ToggleGroupType);

            // 右ボタンを押したときのコールバック
            _onClickRightButton = parameter.OnClickRightButton;
        }

        protected virtual void SetupCharaSwitchButton(SetupParameter setupParameter)
        {
            setupParameter.GetPartsParameter<CharaSwitchButtonSetupParameter>(param =>
            {
                if (_partsArrowButtonPair == null)
                {
                    var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.PARTS_FLICKABLE_ARROW_BUTTON_PAIR, DialogHash);
                    var partsObj = Instantiate<GameObject>(prefab, this.transform);
                    _partsArrowButtonPair = partsObj.GetComponent<PartsFlickableArrowButtonPair>();
                }

                if (_partsArrowButtonPair != null)
                {
                    var parameter = new PartsFlickableArrowButtonPair.SetupParameter
                    {
                        OnClickRightArrow = () => OnClickSwitchButton(param.GetNextSetupParam, param.OnChangeChara),
                        OnClickLeftArrow = () => OnClickSwitchButton(param.GetPrevSetupParam, param.OnChangeChara),
                        ButtonCollisionSize = ARROW_BUTTON_COLLISION_SIZE,
                    };
                    _partsArrowButtonPair.Setup(parameter);

                    // ダイアログを閉じる際のコールバック
                    _onCloseDialogWithCharaSwitchButton = param.OnCloseDialogWithCharaSwitchButton;
                    _needUnloadResourceOnDestroy = true;
                }
            });
        }

        /// <summary>
        /// 表示するキャラクターを切り替える矢印ボタンを押した際の処理
        /// </summary>
        protected void OnClickSwitchButton(Action<Action<SetupParameter>/* onComplete */> getNextSetupParam, Action onChangeChara)
        {
            if (_isChangingDispChara == true)
            {
                Debug.LogWarning("すでに表示キャラ切り替え処理中なので、切り替え処理が出来ません\n" +
                    "矢印ボタンの連打以外でこのWarningが出る場合は、SetupParameterの取得処理が正しく動いていないため確認してください");
                return;
            }

            if (getNextSetupParam == null)
            {
                return;
            }

            _isChangingDispChara = true;
            onChangeChara?.Invoke();

            // 次に表示するデータののSetupParameterを取得
            getNextSetupParam.Invoke(resultSetupParam =>
            {
                // 切り替えで不要になる画像（ヘッダー背景・育成情報のサポカ）をアンロード
                ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.DialogCharacterDetail);

                Setup(resultSetupParam);
                
                OnCompleteChangeDispChara();

                _isChangingDispChara = false;
            });

            _isDispCharaChanged = true;
        }

        /// <summary>
        /// キャラ切り替えが完了したときのコールバック
        /// </summary>
        protected virtual void OnCompleteChangeDispChara()
        {
            // タブの切り替え
            var activeToggleIndex = GetIsOnToggleIndex();
            ChangeToggleView(activeToggleIndex);
        }

        /// <summary>
        /// トグルグループをセットアップ
        /// </summary>
        /// <param name="type"></param>
        protected virtual void SetupToggleGroup(ToggleGroupType type)
        {
            _toggleGroupType = type;
            switch (type)
            {
                case ToggleGroupType.Two:
                    _categoryToggleGroup3.SetActiveWithCheck(false);
                    _categoryToggleGroup2.SetActiveWithCheck(true);
                    _categoryToggleGroup2.SetOnSelectCallback(ChangeToggleView);
                    _flickableObject.SetFlickCallback((status) => FlickCallbackSub(status, _categoryToggleGroup2));
                    break;
                case ToggleGroupType.Three:
                    _categoryToggleGroup2.SetActiveWithCheck(false);
                    _categoryToggleGroup3.SetActiveWithCheck(true);
                    _categoryToggleGroup3.SetOnSelectCallback(ChangeToggleView);
                    _flickableObject.SetFlickCallback((status) => FlickCallbackSub(status, _categoryToggleGroup3));
                    break;
                case ToggleGroupType.None:
                    _categoryToggleGroup2.SetActiveWithCheck(false);
                    _categoryToggleGroup3.SetActiveWithCheck(false);
                    break;
            }

            ChangeToggleView(0);
        }

        /// <summary>
        /// イベントタブの初回設定
        /// </summary>
        /// <param name="cardId"></param>
        private void SetUpEventTab()
        {
            var cardData = MasterDataManager.Instance.masterCardData.Get(_cardId);
            if (_eventListScroll != null && cardData != null)
            {
                //イベント一覧
                var eventList = MasterDataUtil.CreateGalleryCardDressStoryList(cardData);
                _eventListScroll.Setup<PartsSupportCardEventListItem>(eventList.Count, item => item.Init(PartsSupportCardEventListItem.DialogType.CardCharacter,_cardId,eventList[item.ItemIndex]));
            }
            _isEventTabSetup = true;
        }


        protected void FlickCallbackSub(FlickHandler.FlickStatus status, FlickToggleGroupCommon toggleGroup)
        {
            FlickHandler.PlaySe(status); //トグルがないときは鳴らしたくないので自前で鳴らす
            toggleGroup.OnFlick(status);
        }

        /// <summary>
        /// トグルが二つの時の表示切替
        /// </summary>
        /// <param name="index"></param>
        protected virtual void ChangeToggleAtTwo(int index)
        {
            switch (index)
            {
                case 0:
                    _skillToggleViewRoot.SetActiveWithCheck(true);
                    _trainingToggleViewRoot.SetActiveWithCheck(false);
                    _eventListRoot.SetActiveWithCheck(false);
                    break;
                case 1:
                    _skillToggleViewRoot.SetActiveWithCheck(false);
                    _trainingToggleViewRoot.SetActiveWithCheck(false);
                    //イベント設定が終わっていたら
                    if (_isEventTabSetup)
                    {
                        _eventListRoot.SetActiveWithCheck(true);
                    }
                    else
                    {
                        UIManager.Instance.LockGameCanvas();
                        var stallOneSec = _partsArrowButtonPair != null; // キャラ切り替えボタンがある場合は連続リクエストの回避を入れる

                        //TempDataにデータが存在しなければ通信開始
                        SendCardEventSkillAPI(_cardId, res =>
                        {
                            //TempDataに通信の値を保存
                            TempData.Instance.AddCardEventSkillDictionary(_cardId, res.data.event_skill_list);
                            //イベントタブ初回設定
                            SetUpEventTab();
                            _eventListRoot.SetActiveWithCheck(true);
                            UIManager.Instance.UnlockGameCanvas();
                        },
                        stallOneSec);
                    }
                    break;
                default:
                    Debug.LogError("不正な入力");
                    return;
            }
        }
        
        /// <summary>
        /// API送信：カードイベント・スキル取得
        /// </summary>
        private static void SendCardEventSkillAPI(int cardId, Action<CardGetCardEventSkillResponse> onSuccess, bool stallOneSec = false)
        {
            var req = new CardGetCardEventSkillRequest();
            req.card_id = cardId;
            Cute.Http.HttpManager.Instance.Send(req,onSuccess, stallOneSecond: stallOneSec);
        }

        /// <summary>
        /// トグルが三つの時の表示切替
        /// </summary>
        /// <param name="index"></param>
        protected virtual void ChangeToggleAtThree(int index)
        {
            switch (index)
            {
                case 0:
                    _skillToggleViewRoot.SetActiveWithCheck(true);
                    _trainingToggleViewRoot.SetActiveWithCheck(false);
                    _eventListRoot.SetActiveWithCheck(false);
                    break;
                case 1:
                    _skillToggleViewRoot.SetActiveWithCheck(false);
                    _trainingToggleViewRoot.SetActiveWithCheck(true);
                    _eventListRoot.SetActiveWithCheck(false);
                    break;
                case 2:
                    _skillToggleViewRoot.SetActiveWithCheck(false);
                    _trainingToggleViewRoot.SetActiveWithCheck(false);
                    _eventListRoot.SetActiveWithCheck(true);
                    break;
                default:
                    Debug.LogError("不正な入力");
                    return;
            }
        }

        /// <summary>
        /// トグルタイプがNoneの時の表示切替
        /// </summary>
        protected virtual void ChangeToggleAtNone()
        {
        }

        /// <summary>
        /// トグル変更による画面切り替え
        /// </summary>
        /// <param name="type"></param>
        protected virtual void ChangeToggleView(int index)
        {
            switch(_toggleGroupType)
            {
                case ToggleGroupType.Two:
                    ChangeToggleAtTwo(index);
                    break;
                case ToggleGroupType.Three:
                    ChangeToggleAtThree(index);
                    break;
                case ToggleGroupType.None:
                    ChangeToggleAtNone();
                    break;
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(transform as RectTransform);
        }

        /// <summary>
        /// トグルの縦位置変更
        /// </summary>
        protected void SetToggleAnchoredPositionY(ToggleGroupType type, float posY)
        {
            RectTransform rect = null;
            switch (type)
            {
                case ToggleGroupType.Two:
                    rect = _categoryToggleGroup2.transform.GetComponent<RectTransform>();
                    break;
                case ToggleGroupType.Three:
                    rect = _categoryToggleGroup3.transform.GetComponent<RectTransform>();
                    break;
            }

            if (rect != null)
            {
                rect.SetAnchoredPositionY(posY);
            }
        }

        /// <summary>
        /// 現在ActiveになっているトグルのIndexを取得
        /// </summary>
        protected int GetIsOnToggleIndex()
        {
            switch (_toggleGroupType)
            {
                case ToggleGroupType.Two:
                    return _categoryToggleGroup2.GetIsOnToggleIndex();
                case ToggleGroupType.Three:
                    return _categoryToggleGroup3.GetIsOnToggleIndex();
            }

            Debug.LogWarningFormat("トグルが存在しないのに、選択中のトグルのIndexを求めようとしている");
            return 0;
        }

        /// <summary>
        /// 右ボタンを押した際のコールバック
        /// </summary>
        protected void OnClickRightButton()
        {
            _onClickRightButton?.Invoke();
        }

        /// <summary>
        /// ダイアログが閉じはじめた際のコールバック
        /// </summary>
        protected void OnBeginCloseDialog()
        {
            // Note:
            // キャラ切り替え機能付きの詳細ダイアログが閉じる時、3Dモデルの更新がされる（育成の継承ウマ娘選択等）
            // DG.Tweening.Sequenceのコールバック(OnDestroyDialogなど)でモデルの更新をするとカクツキの原因になるので、
            // OnBeginCloseのタイミングでコールバックを実行する必要がある
            _onCloseDialogWithCharaSwitchButton?.Invoke(_isDispCharaChanged);
        }

        private void OnDestroy()
        {
            if (_partsArrowButtonPair != null)
            {
                // リソースの破棄
                ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.DialogCharacterDetail);
            }

            // キャラ切り替えで背景画像のロード等が何度もされている可能性があるので、GCも呼び出して可能な限り解放を促す
            if (_needUnloadResourceOnDestroy)
            {
                System.GC.Collect();
                Resources.UnloadUnusedAssets();
            }
        }

        #endregion
    }
}
