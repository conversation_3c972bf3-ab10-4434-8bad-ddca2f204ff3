using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 購入完了確認ダイアログ(殿堂入りウマ娘枠拡張用)
    /// </summary>
    [AddComponentMenu("")]
    public class DialogTrainedCharaCompleteExtendSlot : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// アイテムアイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _itemIcon;

        /// <summary>
        /// 所持数
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _havingCount;

        /// <summary>
        /// 消費アイテム
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _payItemCount;

        /// <summary>
        /// 消費アイテムアイコン
        /// </summary>
        [SerializeField]
        private ImageCommon _payItemIcon;

        /// <summary>
        /// 消費アイテムアイコン
        /// </summary>
        [SerializeField]
        private RawImageCommon _payItemIconRawImage;

        /// <summary>
        /// 残り購入回数
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _exchangeableCount;

        #endregion

        #region Member

        /// <summary>
        /// 購入した拡張回数
        /// </summary>
        private int _exchangeCount;

        #endregion

        #region Property

        /// <summary>
        /// 購入アイテムのデータ
        /// </summary>
        private MasterItemData.ItemData ItemData => MasterDataManager.Instance.masterItemData.Get(GameDefine.TRAINED_CHARA_SLOT_EXTENSION_ID);

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// 開く
        /// </summary>
        /// <param name="extendCount"> 購入した拡張回数 </param>
        public static void Open(int extendCount)
        {
            const string PATH = ResourcePath.DIALOG_TRAINED_CHARA_COMPLETE_EXTEND_SLOT_PATH;
            var content = LoadAndInstantiatePrefab<DialogTrainedCharaCompleteExtendSlot>(PATH);
            var dialogData = content.CreateDialogData();

            content.Setup(extendCount);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Shop0001.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.CenterButtonColor = DialogCommon.ButtonColor.White;

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(int extendCount)
        {
            _exchangeCount = extendCount;
            SetupIcon();
            SetupItemCount();
            SetupPayItemCount();
            SetupExchangeCount();
        }

        /// <summary>
        /// アイテムアイコンをセットアップ
        /// </summary>
        private void SetupIcon()
        {
            _itemIcon.SetData(
                ItemData.ItemCategory,
                ItemData.Id,
                ServerDefine.TrainedCharaSaveExtendIncreaseNum * _exchangeCount,
                numDisp: true
            );
            _itemIcon.SetButtonEnabled(false);
        }

        /// <summary>
        /// 登録枠数をセットアップ
        /// </summary>
        private void SetupItemCount()
        {
            int after = WorkTrainedCharaData.TrainedCharaNumMax;
            int before = after - _exchangeCount * ServerDefine.TrainedCharaSaveExtendIncreaseNum;
            _havingCount.Setup(before.ToCommaSeparatedString(), after.ToCommaSeparatedString());
        }

        /// <summary>
        /// 消費アイテム数をセットアップ
        /// </summary>
        private void SetupPayItemCount()
        {
            int afterNum = WorkDataManager.Instance.UserData.CarrotStone.TotalCoin;
            int beforeNum = afterNum + _exchangeCount * ServerDefine.TrainedCharaSaveExtendUseCoinNum;
            _payItemCount.Setup(beforeNum.ToCommaSeparatedString(), afterNum.ToCommaSeparatedString());

            GallopUtil.SetUseIconImage(GameDefine.ItemCategory.FREE_CARROT, GameDefine.CALOTTE_STONE_ID, _payItemIcon , _payItemIconRawImage);
        }

        /// <summary>
        /// 購入回数をセットアップ
        /// </summary>
        private void SetupExchangeCount()
        {
            int totalExchangeCount = WorkTrainedCharaData.TrainedCharaExtendSaveNum / ServerDefine.TrainedCharaSaveExtendIncreaseNum;
            int after = ServerDefine.TrainedCharaSaveExtendCountLimit - totalExchangeCount;
            int before = after + _exchangeCount;

            string beforeRemainText = TextUtil.Format(TextId.Common0305.Text(), before);
            string afterRemainText = TextUtil.Format(TextId.Common0305.Text(), after);
            _exchangeableCount.Setup(beforeRemainText, afterRemainText);
            _exchangeableCount.SetBeforeFontColor(FontColorType.Brown);
            _exchangeableCount.SetAfterFontColor(FontColorType.Minus);
        }

        #endregion
    }
}
