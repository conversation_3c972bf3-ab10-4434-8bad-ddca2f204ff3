using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// お気に入りウマ娘設定
    /// ** サークルプロフィールなどで流用するため中身をパーツ化
    /// </summary>
    [AddComponentMenu("")]
    public class PartsFavoriteCharacterTop : MonoBehaviour
    {

        #region SerializeField, 変数

        public int CurrentCharaId => _currentCharaId;
        public int CurrentDressId => _currentDressId;

        [SerializeField]
        private TextCommon _characterName;

        [SerializeField]
        private CharacterButton _characterButton;

        [SerializeField]
        private RawImageCommon _dressImage;

        [SerializeField]
        private ButtonCommon _dressButton;

        private int _prevCharaId;
        private int _currentCharaId;
        private int _prevDressId;
        private int _currentDressId;
        private string _dialogName;
        private DialogCommon _dialog;
        private Action<int, int> _onChangeChracterDress;
        private bool _isOnlyMiniCharaDress = true;  // 衣装選択の時、ミニキャラが着られる衣装だけを並べるか
        private bool _isEnablePrivateDress = false; // 衣装選択の時、私服を並べるか

        /// <summary>
        /// 表示設定
        /// </summary>
        /// <param name="dialogCommon"></param>
        public void Setup(DialogCommon dialogCommon, int defaultCharaId, int defaultDressId, string dialogName = null)
        {
            _dialog = dialogCommon;
            _dialogName = dialogName;
            _prevCharaId = defaultCharaId;
            _currentCharaId = defaultCharaId;
            _prevDressId = defaultDressId;
            _currentDressId = defaultDressId;

            UpdateCharaUI(_currentCharaId);
            UpdateDressButton(_currentCharaId, _currentDressId);
            UpdateButton();

            _dressButton.SetOnClick(OnClickDressButton);
        }

        /// <summary>
        /// 表示設定(ダイアログを使わないバージョン)
        /// </summary>
        /// <param name="defaultCharaId"></param>
        /// <param name="defaultDressId"></param>
        /// <param name="onChangeChracterDress"></param>
        /// <param name="isOnlyMiniCharaDress">衣装選択の時、ミニキャラが着られる衣装だけを並べるか</param>
        /// <param name="isEnablePrivateDress">衣装選択の時、私服を並べるか</param>
        public void SetUp(int defaultCharaId, int defaultDressId, Action<int, int> onChangeChracterDress = null,
                          bool isOnlyMiniCharaDress = true, bool isEnablePrivateDress = false)
        {
            _prevCharaId = defaultCharaId;
            _currentCharaId = defaultCharaId;
            _prevDressId = defaultDressId;
            _currentDressId = defaultDressId;
            _onChangeChracterDress = onChangeChracterDress;
            _isOnlyMiniCharaDress = isOnlyMiniCharaDress;
            _isEnablePrivateDress = isEnablePrivateDress;

            UpdateCharaUI(_currentCharaId);
            UpdateDressButton(_currentCharaId, _currentDressId);
            _dressButton.SetOnClick(OnClickDressButton);
            
            UpdateButton();

        }


        /// <summary>
        /// 選択キャラクターのUI更新
        /// </summary>
        /// <param name="charaId">キャラID</param>
        private void UpdateCharaUI(int charaId)
        {
            var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (_characterName != null)
            {
                _characterName.text = charaData.Name;
            }

            var buttonInfo = CharacterButtonInfo.CreateChara(charaId);
            _characterButton.Setup(buttonInfo);
            _characterButton.MyButton.SetOnClick(OnClickCharaButton);
        }

        /// <summary>
        /// 所持している衣装かチェックして更新
        /// </summary>
        private void UpdateHaveDress(int charaId)
        {
            var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            var haveDressArray = WorkDataManager.Instance.DressData.GetDressListByCharaId(charaId, GameDefine.CharacterClothMode.Home).Where(x => x.HaveMini).ToArray();
            var dressData = haveDressArray.FirstOrDefault(x => x.Id == _currentDressId);
            if (dressData == null)
            {
                _currentDressId = haveDressArray[0].Id;
            }
            var newDressData = MasterDataManager.Instance.masterDressData.Get(_currentDressId);
            var texturePath = WorkDressData.GetDressIconPath(newDressData, charaData.Sex);
            _dressImage.texture = ResourceManager.LoadOnView<Texture>(texturePath);
        }

        /// <summary>
        /// 選択衣装ボタン更新
        /// </summary>
        private void UpdateDressButton(int charaId, int dressId)
        {
            var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            var dressData = MasterDataManager.Instance.masterDressData.Get(dressId);
            var texturePath = WorkDressData.GetDressIconPath(dressData, charaData.Sex);
            _dressImage.texture = ResourceManager.LoadOnView<Texture>(texturePath);
        }

        /// <summary>
        /// キャラボタン選択
        /// </summary>
        private void OnClickCharaButton()
        {
            DialogCharacterSelect.PushDialog(_currentCharaId, newCharaId =>
            {
                _currentCharaId = newCharaId;

                UpdateCharaUI(newCharaId);
                UpdateHaveDress(newCharaId);
                UpdateButton();
                
                _onChangeChracterDress?.Invoke(_currentCharaId, _currentDressId);
            }, _dialogName);
        }

        /// <summary>
        /// 衣装ボタン選択
        /// </summary>
        private void OnClickDressButton()
        {
            DialogFavoriteDressSelect.Open(_currentCharaId, _currentDressId, newDressId =>
            {
                _currentDressId = newDressId;

                UpdateDressButton(_currentCharaId, newDressId);
                UpdateButton();
                
                _onChangeChracterDress?.Invoke(_currentCharaId, _currentDressId);
            }, _dialogName,
            isOnlyMiniCharaDress: _isOnlyMiniCharaDress,
            isEnablePrivateDress: _isEnablePrivateDress);
        }


        /// <summary>
        /// ボタン更新
        /// </summary>
        private void UpdateButton()
        {
            if (_dialog != null)
            {
                var rightButton = _dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
                rightButton.SetInteractable(_prevCharaId != _currentCharaId || _prevDressId != _currentDressId);
            }
        }

        #endregion
    }
}