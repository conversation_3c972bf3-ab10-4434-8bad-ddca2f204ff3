using UnityEngine;
using System.Text.RegularExpressions;

namespace Gallop
{
    /// <summary>
    /// データ引継ぎ
    /// ** パスワード設定 or 引継ぐ
    /// </summary>
    public class DialogDataTransitionSetPassword : MonoBehaviour
    {
        [SerializeField]
        private InputFieldCommon _inputPassword = null;
        [SerializeField]
        private InputFieldCommon _inputPasswordConfirm = null;
        [SerializeField]
        private ButtonCommon _privacyButton = null;
        [SerializeField]
        private ToggleCommon _checkPlivacy = null;

        public string Password { get; private set; }

        private DialogCommon _dialog;

        public void Initialize(DialogCommon dialog)
        {
            _dialog = dialog;
            _privacyButton.SetOnClick(OnClickPrivacyButton);
        }

        private void Update()
        {
            if (_dialog == null) return;

            Password = _inputPassword.text;

            var noticeText = string.Empty;
            if(Password.Length < GameDefine.DATA_LINK_PASSWORD_MIN)
            {
                //短すぎる
                noticeText = TextId.AccoutDataLink0107.Text();
            }
            else if(!GallopUtil.IsPasswordText(Password))
            {
                //半角英数字じゃない
                noticeText = TextId.AccoutDataLink0108.Text();
            }
            else if (_inputPassword.text != _inputPasswordConfirm.text)
            {
                //一致しない
                noticeText = TextId.AccoutDataLink0106.Text();
            }
            else if(_checkPlivacy.isOn == false)
            {
                //同意が必要
                noticeText = TextId.AccoutDataLink0109.Text();
            }

            _dialog.CurrentDialogObj.RightButton.SetNotificationMessage(noticeText);
            _dialog.CurrentDialogObj.RightButton.interactable = string.IsNullOrEmpty(noticeText);
        }

        private void OnClickPrivacyButton()
        {
            var dialogData = new DialogCommon.Data();
            dialogData.FormType = DialogCommonBase.FormType.BIG_ONE_BUTTON;
            dialogData.Title = TextId.AccoutDataLink0087.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();

            // ダイアログ表示
            WebViewManager.Instance.Open(WebViewDefine.Url.PrivacyPolicy, dialogData);
        }
    }
}