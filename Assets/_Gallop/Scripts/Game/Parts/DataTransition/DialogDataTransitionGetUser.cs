using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 引継ぎ先ユーザー
    /// </summary>
    [AddComponentMenu("")]
    public class DialogDataTransitionGetUser : MonoBehaviour
    {
        [SerializeField]
        private TextCommon _viewerId = null;
        [SerializeField]
        private TextCommon _userName = null;

        public void Initialize(long viewerId, string userName)
        {
            // 3桁ごとにスペースを入れて表記
            _viewerId.text = TextUtil.ToStringViewerId(viewerId);
            _userName.text = string.IsNullOrEmpty(userName) ? "-" : userName;
        }
    }
}