using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 引継ぎ先ユーザー
    /// </summary>
    [AddComponentMenu("")]
    public class DialogDataTransitionSetPasswordEnd : MonoBehaviour
    {
        [SerializeField]
        private TextCommon _viewerId = null;
        [SerializeField]
        private TextCommon _pass = null;

        public void Initialize(long viewerId, string pass)
        {
            // 3桁ごとにスペースを入れて表記
            _viewerId.text = TextUtil.ToStringViewerId(viewerId);
            _viewerId.color = TextFormat.GetFontColor(FontColorType.Orange);
            _pass.text = string.IsNullOrEmpty(pass) ? "-" : pass;
        }
    }
}