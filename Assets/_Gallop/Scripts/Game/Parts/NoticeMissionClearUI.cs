using System;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;
using Gallop.TrainingChallenge;
using Gallop.Tutorial;
using static Gallop.StaticVariableDefine.Parts.NoticeMissionClearUI;

namespace Gallop
{
    [AddComponentMenu("")]
    public class NoticeMissionClearUI : MonoBehaviour
    {
        #region 定義, 定数

        private enum AnimState
        {
            WaitNext,       // 次の通知待ち
            In,             // 画面内に移動
            Idle,           // 画面内で何もしない
            DetectFlick,    // フリック検知
            Out,            // 画面外に移動
            ForceOut,       // 強制Out
        }
        
        private const string ANIMATION_NAME_IN = "InMove";
        private const string ANIMATION_NAME_OUT = "OutMove";
        private const float IDLE_TIME = 2.0f;

        #endregion

        #region SerializeField, 変数

        [field: SerializeField, RenameField]
        public RectTransform ContentsRoot = null;

        [SerializeField]
        private TextCommon _missionTitleText = null;

        [SerializeField]
        private GameObject _window = null;

        /// <summary>
        /// イベントのミッションをクリアした場合に表示するロゴ画像
        /// </summary>
        [SerializeField]
        private RawImageCommon _eventLogoImage;

        [SerializeField]
        private TweenAnimationTimelineComponent _inMoveTimeLine = null;
        
        [SerializeField]
        private TweenAnimationTimelineComponent _outMoveTimeLine = null;
        
        [SerializeField]
        private TweenAnimationTimelineComponent _shiftOnTimeLine = null;
        
        [SerializeField]
        private TweenAnimationTimelineComponent _shiftOffTimeLine = null;

        [SerializeField]
        public BoxCollider _flickTarget = null;

        private TouchManager.FlickParam _flickParam;
        private AnimState _currentState = AnimState.WaitNext;
        private string _currentMissionName = string.Empty;
        private Func<TempData.MissionNotice, bool> _isForceShowMissionNoticePredicate = null;
        private bool IsForceShowMissionNotice => _isForceShowMissionNoticePredicate != null;
        private readonly CompositeDisposable _subscription = new CompositeDisposable();
        /// <summary> 一時的なミッション通知表示無効な画面一覧 </summary>
        private readonly HashSet<SceneDefine.ViewId> _tempIgnoreShowViewHashSet = new HashSet<SceneDefine.ViewId>();

        #endregion


        #region メソッド

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            //チュートリアル中は何もしない
            if (TutorialManager.IsTutorialExecuting())
            {
                return;
            }
            // ストーリーイベントが開催中、または近日開催予定ならイベントロゴ画像をダウンロードしておく。チュートリアル中は行わない
            var workStoryEventData = WorkDataManager.Instance.StoryEventData;
            var storyEventId = workStoryEventData.StoryEventId;
            if (storyEventId != 0)
            {
                var storyEventLogoPath = ResourcePath.GetStoryEventLogoPath(storyEventId);
                register.RegisterPathWithoutInfo(storyEventLogoPath);
            }
            
            // 育成チャレンジが開催中、または今日開催予定ならイベントロゴ画像をダウンロードしておく
            var masterTrainingChallengeMaster = MasterDataManager.Instance.masterTrainingChallengeMaster;
            masterTrainingChallengeMaster.UpdateStatus();
            if (masterTrainingChallengeMaster.Status.IsOpen() || masterTrainingChallengeMaster.TodayOpenEntity != null)
            {
                var trainingChallengeLogoPath = ResourcePath.TRAINING_CHALLENGE_LOGO;
                register.RegisterPathWithoutInfo(trainingChallengeLogoPath);
            }
            
            // 収集イベントが開催中、または今日開催予定ならイベントロゴ画像をダウンロードしておく
            var masterCollectRaidMaster = MasterDataManager.Instance.masterCollectRaidMaster.GetTodayOpenEntity();
            if (masterCollectRaidMaster != null)
            {
                var logoPath = ResourcePath.GetCollectRaidLogoTexturePath(masterCollectRaidMaster.Id);
                register.RegisterPathWithoutInfo(logoPath);
            }
            
        }

        /// <summary>
        /// ミッション達成を通知（通知を出したくないViewだった場合はすぐには出さない）
        /// </summary>
        public bool IsShowableView(SceneDefine.ViewId viewId)
        {
            if (IsIgnoreMissionShowView(viewId))
            {
                // 現在のViewでは通知できない場合は何もしない
                return false;
            }

            return true;
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public void Init()
        {
            _shiftOnTimeLine.Stop();
            _shiftOffTimeLine.Play().Complete();
            _inMoveTimeLine.Stop();
            _outMoveTimeLine.Play().Complete();
        }

        /// <summary>
        /// ミッション達成を通知
        /// </summary>
        public void Show()
        {
            if (_currentState != AnimState.WaitNext) return;

            // 強制表示フラグが立っていて、条件に合うミッションが存在しない場合は強制表示フラグを折る
            if (IsForceShowMissionNotice && TempData.Instance.MissionNoticeQueue.FilteredQueueCount(_isForceShowMissionNoticePredicate) <= 0)
            {
                _isForceShowMissionNoticePredicate = null;
                return;
            }

            if (TempData.Instance.MissionNoticeQueue.Count == 0) return;

            if (MasterDataManager.Instance == null || MasterDataManager.Instance.masterMissionData == null) return;
            
#if CYG_DEBUG
            //DirectScene上で表示されると鬱陶しいのでスキップする
            var currentScene = SceneManager.Instance.GetCurrentSceneController();
            if (currentScene != null && currentScene.GetSceneBase() as DirectSceneBase)
            {
                //ミッション通知は消してしまう
                TempData.Instance.MissionNoticeQueue.Clear();
                return;
            }
#endif
            var info = TempData.Instance.MissionNoticeQueue.FilterDequeue(_isForceShowMissionNoticePredicate);

            _currentMissionName = GetMissionName(info);
            if (string.IsNullOrEmpty(_currentMissionName))
            {
                return;
            }
            
#if !DMM
            if (info.InfoType == TempData.NoticeInfoType.Mission)
            {
                // ここで同時に実績(Achievement)の通知も出す
                AchievementImpl.Instance.ReleaseAchievement(info.MissionId);
            }
#endif
            
            // ロゴ状態を更新
            SetEventLogo(info);

            _currentState = AnimState.WaitNext;
            ChangeState();
        }

        /// <summary>
        /// 条件に合うミッションを強制表示する
        /// </summary>
        public void ForceShow(Func<TempData.MissionNotice, bool> predicate)
        {
            // 強制表示データを用意
            _isForceShowMissionNoticePredicate = predicate;

            Show();
        }

        /// <summary>
        /// 通知に表示するミッション名を取得
        /// </summary>
        private string GetMissionName(TempData.MissionNotice info)
        {
            switch (info.InfoType)
            {
                case TempData.NoticeInfoType.StoryEventMission:
                    var storyEventMissionData = MasterDataManager.Instance.masterStoryEventMission.Get(info.MissionId);
                    return storyEventMissionData?.Name;
                
                case TempData.NoticeInfoType.CollectRaidIndividualReward:
                    var rewardData = MasterDataManager.Instance.masterCollectRaidIndividualReward.Get(info.MissionId);
                    if (rewardData == null)
                    {
                        return string.Empty;
                    }
                    // 収集イベント個人報酬は特殊文面のため、整形した通知文言をミッション名として扱う
                    return CollectRaidUtil.GetIndividualRewardNotificationText(rewardData.IndividualCollectItemNum);
                
                case TempData.NoticeInfoType.Mission:
                    var missionData = MasterDataManager.Instance.masterMissionData.Get(info.MissionId);
                    return missionData?.Name;
                
                default:
                    Debug.LogError("NoticeInfoTypeに定義が存在しないMissionNotice");
                    return string.Empty;
            }
        }

        /// <summary>
        /// バナーに出すロゴの状態(表示/非表示・テクスチャ)を更新
        /// </summary>
        private void SetEventLogo(TempData.MissionNotice info)
        {
            _eventLogoImage.gameObject.SetActiveWithCheck(false);
            
            // ロゴパスを通知内容に応じて取得
            var eventLogoPath = GetEventLogoPath(info);
            if (string.IsNullOrEmpty(eventLogoPath))
            {
                return;
            }
            
            _eventLogoImage.gameObject.SetActiveWithCheck(true);
            _eventLogoImage.texture = ResourceManager.LoadOnHash<Texture>(eventLogoPath, ResourceManager.ResourceHash.Notification);

            // ロゴの位置・サイズ調整をおこなう(イベントロゴごとに余白やサイズが異なるため)
            switch (info.InfoType)
            {
                case TempData.NoticeInfoType.StoryEventMission:
                    // ストーリーイベント
                    _eventLogoImage.rectTransform.anchoredPosition = DEFAULT_LOGO_IMAGE_POS;
                    _eventLogoImage.transform.localScale = DEFAULT_LOGO_IMAGE_SCALE;
                    break;
                
                case TempData.NoticeInfoType.CollectRaidIndividualReward:
                    // 収集イベントの個人報酬
                    _eventLogoImage.rectTransform.anchoredPosition = COLLECT_RAID_LOGO_IMAGE_POS;
                    _eventLogoImage.transform.localScale = COLLECT_RAID_LOGO_IMAGE_SCALE;
                    break;
                
                case TempData.NoticeInfoType.Mission:
                    // 通常のミッションに対するロゴ表示
                    var missionType = (GameDefine.MissionType)MasterDataManager.Instance.masterMissionData.Get(info.MissionId).MissionType;
                    
                    switch (missionType)
                    {
                        case GameDefine.MissionType.TrainingChallengeDaily:
                        case GameDefine.MissionType.TrainingChallengeLimited:
                            _eventLogoImage.rectTransform.anchoredPosition = DEFAULT_LOGO_IMAGE_POS;
                            _eventLogoImage.transform.localScale = DEFAULT_LOGO_IMAGE_SCALE;
                            break;
                        
                        case GameDefine.MissionType.CollectRaidDaily:
                        case GameDefine.MissionType.CollectRaidLimited:
                            _eventLogoImage.rectTransform.anchoredPosition = COLLECT_RAID_LOGO_IMAGE_POS;
                            _eventLogoImage.transform.localScale = COLLECT_RAID_LOGO_IMAGE_SCALE;
                            break;
                    }
                    break;
                default:
                    Debug.LogError("NoticeInfoTypeに定義が存在しないMissionNotice");
                    return;
            }
        }

        /// <summary>
        /// 通知に出すロゴを取得
        /// </summary>
        private string GetEventLogoPath(TempData.MissionNotice info)
        {
            switch (info.InfoType)
            {
                case TempData.NoticeInfoType.StoryEventMission:
                    // ストーリーイベント
                    var storyEventId = MasterDataManager.Instance.masterStoryEventMission.Get(info.MissionId).StoryEventId;
                    return storyEventId == 0 ? string.Empty : ResourcePath.GetStoryEventLogoPath(storyEventId);

                case TempData.NoticeInfoType.CollectRaidIndividualReward:
                    // 収集イベントの個人報酬
                    var rewardMaster = MasterDataManager.Instance.masterCollectRaidIndividualReward.Get(info.MissionId);
                    if (rewardMaster == null)
                    {
                        return string.Empty;
                    }
                    var collectRaidMaster = MasterDataManager.Instance.masterCollectRaidMaster.GetWithIndividualRewardSetId(rewardMaster.IndividualRewardSetId);
                    // Note: 第2回以降の収集イベントで個人報酬セットを使いまわす場合は要改修(rewardSetId -> eventId が単射ではなくなるため)
                    return  collectRaidMaster == null ? string.Empty : ResourcePath.GetCollectRaidLogoTexturePath(collectRaidMaster.Id);

                case TempData.NoticeInfoType.Mission:
                    // 通常のミッションに対するロゴ表示
                    var missionType = (GameDefine.MissionType)MasterDataManager.Instance.masterMissionData.Get(info.MissionId).MissionType;
                    
                    switch (missionType)
                    {
                        case GameDefine.MissionType.TrainingChallengeDaily:
                        case GameDefine.MissionType.TrainingChallengeLimited:
                            return ResourcePath.TRAINING_CHALLENGE_LOGO;
                        
                        case GameDefine.MissionType.CollectRaidDaily:
                        case GameDefine.MissionType.CollectRaidLimited:
                            var collectRaidMission = MasterDataManager.Instance.masterCollectRaidMission.GetWithMissionDataId(info.MissionId);
                            return collectRaidMission == null ? string.Empty : ResourcePath.GetCollectRaidLogoTexturePath(collectRaidMission.CollectRaidMasterId);
                    }
                    break;
                default:
                    Debug.LogError("NoticeInfoTypeに定義が存在しないMissionNotice");
                    break;
            }
            return string.Empty;
        }

        /// <summary>
        /// state更新
        /// </summary>
        private void ChangeState()
        {
            switch (_currentState)
            {
                case AnimState.WaitNext:
                    _currentState = AnimState.In;
                    break;
                case AnimState.In:
                    _currentState = AnimState.Idle;
                    break;
                case AnimState.Idle:
                    _currentState = AnimState.Out;
                    break;
                case AnimState.DetectFlick:
                    _currentState = AnimState.ForceOut;
                    break;
                case AnimState.Out:
                    _currentState = AnimState.WaitNext;
                    break;
                case AnimState.ForceOut:
                    _currentState = AnimState.WaitNext;
                    break;
                default:
                    return;
            }

            PlayStateAnimation();
        }

        /// <summary>
        /// 現在のstateに適したアニメーションを再生する
        /// </summary>
        private void PlayStateAnimation()
        {
            if (_currentMissionName == string.Empty)
            {
                Debug.LogWarning("Mission Data is null");
                return;
            }

            switch (_currentState)
            {
                case AnimState.WaitNext:
                    _window.SetActive(false);
                    _missionTitleText.text = string.Empty;
                    _inMoveTimeLine.Stop();
                    _outMoveTimeLine.Stop();
                    var currentViewId = SceneManager.Instance.GetCurrentViewId();
                    if (IsForceShowMissionNotice || IsShowableView(currentViewId))
                    {
                        Show();
                    }
                    break;

                case AnimState.In:
                    _window.SetActive(true);
                    _missionTitleText.text = _currentMissionName;
                    _outMoveTimeLine.Stop();
                    _inMoveTimeLine.Play(ANIMATION_NAME_IN, ChangeState);
                    break;

                case AnimState.Idle:
                    IEnumerator CheckStateChange()
                    {
                        bool isWait = true;
                        var coroutine = UIManager.Instance.StartCoroutine(DelayAction(IDLE_TIME, () => isWait = false));
                        while (isWait)
                        {
                            if (_currentState != AnimState.Idle)
                            {
                                // アイドル時間を待たずに状態が変化した場合
                                UIManager.Instance.StopCoroutine(coroutine);
                                yield break;
                            }
                            yield return null;
                        }
                        // アイドル時間が完了したら状態を変化
                        ChangeState();
                    }

                    UIManager.Instance.StartCoroutine(CheckStateChange());
                    break;

                case AnimState.DetectFlick:
                    // 何もしない
                    Debug.LogWarning("不正な呼び出し");
                    break;

                case AnimState.Out:
                case AnimState.ForceOut:    // フリック時の挙動を変えたい場合は処理を分ける
                    _inMoveTimeLine.Stop();
                    _outMoveTimeLine.Play(ANIMATION_NAME_OUT, ChangeState);
                    break;
            }
        }

        /// <summary>
        /// アニメーション一時停止
        /// </summary>
        public void PauseAnimation()
        {
            _inMoveTimeLine.Pause();
            _outMoveTimeLine.Pause();
        }
        
        /// <summary>
        /// アニメーション再開
        /// </summary>
        public void ResumeAnimation()
        {
            _inMoveTimeLine.Resume();
            _outMoveTimeLine.Resume();
        }

        /// <summary>
        /// 処理実行を指定秒数遅らせる
        /// </summary>
        /// <param name="second"></param>
        /// <param name="action"></param>
        /// <returns></returns>
        private IEnumerator DelayAction(float second, Action action)
        {
            yield return new WaitForSeconds(second);
            action?.Invoke();
        }

        /// <summary>
        /// コールバック：上方向フリック
        /// </summary>
        private void OnFlickUp()
        {
            switch (_currentState)
            {
                case AnimState.In:
                case AnimState.Idle:
                    _currentState = AnimState.DetectFlick;
                    ChangeState();
                    break;
            }
        }

        private void Start()
        {
            _flickParam = TouchManager.Instance.AddFlick(_flickTarget, t =>
            {
                switch (t)
                {
                    case TouchManager.FlickType.UP: OnFlickUp(); break;
                }
            });
        }

        private void OnDestroy()
        {
            if (_flickParam != null)
            {
                TouchManager.Instance.RemoveFlick(_flickParam);
            }

            // イベントロゴがロードされていたならアンロード
            if (_eventLogoImage.texture != null)
            {
                ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.Notification);
                _eventLogoImage.texture = null;
            }
            
            // イベント購読破棄
            _subscription?.Dispose();
        }
        
        /// <summary>
        /// レスポンスがミッション表示無効対象APIかチェック
        /// </summary>
        public static bool IsIgnoreMissionShowAPI(Type responseType)
        {
            //スキップ対象のAPIに入っているかどうか
            return SKIP_MISSON_SHOW_API.Contains(responseType);
        }


        /// <summary>
        /// 現在のViewがミッション表示無効対象かチェック
        /// </summary>
        public bool IsIgnoreMissionShowView(SceneDefine.ViewId viewId)
        {
            // 一時的な表示無効か
            if (_tempIgnoreShowViewHashSet.Contains(viewId))
            {
                return true;
            }
            //スキップ対象のViewに入っているかどうか
            return SKIP_MISSON_SHOW_VIEW.Contains(viewId);
        }

        /// <summary>
        /// 一時的なミッション通知表示無効な画面を登録
        /// </summary>
        public void AddTempIgnoreShowView(SceneDefine.ViewId viewId)
        {
            _tempIgnoreShowViewHashSet.Add(viewId);
        }
        
        /// <summary>
        /// 一時的なミッション通知表示無効な画面を解除
        /// </summary>
        public void RemoveTempIgnoreShowView(SceneDefine.ViewId viewId)
        {
            _tempIgnoreShowViewHashSet.Remove(viewId);
        }

        #region イベント購読

        /// <summary>
        /// イベント購読
        /// </summary>
        public void Subscribe(EventSubject<NoticeAnnounceEvent> subject)
        {
            _subscription.Clear();
            _subscription.Add(subject.Subscribe(OnChangeAnnounceEvent));
        }

        /// <summary>
        /// イベント: アナウンス告知変更
        /// </summary>
        /// <param name="info"></param>
        private void OnChangeAnnounceEvent(NoticeAnnounceEvent info)
        {
            if (info.IsShow)
            {
                _shiftOffTimeLine.Stop();
                _shiftOnTimeLine.Play();
            }
            else
            {
                _shiftOnTimeLine.Stop();
                _shiftOffTimeLine.Play();
            }
        }

        /// <summary>
        /// アニメーション再開前提で一度非表示
        /// </summary>
        public void HideWithAnimation()
        {
            PauseAnimation();
            gameObject.SetActive(false);
        }

        /// <summary>
        /// アニメーション付きで再開
        /// </summary>
        public void ShowWithResume()
        {
            gameObject.SetActive(true);
            ResumeAnimation();
        }
        
        #endregion

        #endregion
    }
}