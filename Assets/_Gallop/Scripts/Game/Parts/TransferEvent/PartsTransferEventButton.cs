using System;
using UnityEngine;
using System.Linq;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// ホームの特別移籍（イベント）ボタン
    /// 常設用の PartsTransferRotationButton
    /// 他の画面用の小さいボタン版 PartsTransferEventButtonMini も存在します
    /// </summary>
    [AddComponentMenu("")]
    public class PartsTransferEventButton : PartsTransferButton
    {
        /// <summary>
        /// UIを更新する準備が整っているか
        /// </summary>
        /// <returns></returns>
        public static bool IsReadyUiUpdate()
        {
            var masterTransferEventData = WorkDataManager.Instance.TransferEventData.MasterTransferEventData;
            var logoPath = TextUtil.Format(ResourcePath.TRANSFER_EVENT_LOGO_PATH, masterTransferEventData.LogoId);
            if (!ResourceManager.IsExistAsset(logoPath))
            {
                // アップデートと開催が同じタイミングかつ開催時刻にホームにいた場合、ロゴ画像をDL出来ていないので、更新出来ない
                return false;
            }

            return true;
        }
        
        /// <summary>
        /// 特別移籍イベントボタンを表示すべきならリソースを落とす
        /// </summary>
        public static void RegisterDownloadIfNeed(DownloadPathRegister register)
        {
            if (!TransferEventUtil.IsNeedTransferEventButton())
                return;

            // ロゴ画像
            var masterTransferEventData = WorkDataManager.Instance.TransferEventData.MasterTransferEventData;
            var logoPath = TextUtil.Format(ResourcePath.TRANSFER_EVENT_LOGO_PATH, masterTransferEventData.LogoId);
            register.RegisterPathWithoutInfo(logoPath);
        }

        /// <summary>
        /// ホームのカルーセル内において、特別移籍イベントボタンを表示すべきなら生成と初期化を行う
        /// </summary>
        public static PartsTransferEventButton CreateAndSetupIfNeed(Transform parentTrans, FlickHandler flickHandler)
        {
            if (!TransferEventUtil.IsNeedTransferEventButton())
                return null;

            var transferEventButtonObj = GameObject.Instantiate<GameObject>(ResourceManager.LoadOnView<GameObject>(ResourcePath.TRANSFER_EVENT_BUTTON_PREFAB_PATH), parentTrans);
            if (transferEventButtonObj == null) return null;
            var transferEventButton = transferEventButtonObj.GetComponent<PartsTransferEventButton>();

            transferEventButton.Setup(flickHandler);

            return transferEventButton;
        }

        /// <summary>
        /// UI更新
        /// </summary>
        protected override void UpdateUI()
        {
            bool isOpen = WorkDataManager.Instance.TransferEventData.IsOpen();
            if (!isOpen)
            {
                // 開催期間が終了したらボタン全体を消す
                this.gameObject.SetActiveWithCheck(false);
                return;
            }

            // UIを更新する準備が整っているかチェック
            int transferEventId = WorkDataManager.Instance.TransferEventData.TransferId;
            if (!IsReadyUiUpdate())
            {
                this.gameObject.SetActiveWithCheck(false);
                return;
            }

            // ロゴ画像
            var masterTransferEventData = WorkDataManager.Instance.TransferEventData.MasterTransferEventData;
            var logoPath = TextUtil.Format(ResourcePath.TRANSFER_EVENT_LOGO_PATH, masterTransferEventData.LogoId);
            _logoImage.texture = ResourceManager.LoadOnView<Texture2D>(logoPath);

            // 残り期間
            {
                var transferEventData = MasterDataManager.Instance.masterTransferEventData.Get(transferEventId);
                _remainTimeText.gameObject.SetActiveWithCheck(transferEventData != null);
                if (transferEventData != null)
                {
                    string remainTimeText = string.Empty;

                    var workTransferEventData = WorkDataManager.Instance.TransferEventData;
                    var nextEndDetailData = workTransferEventData.NextEndDetailData;
                    if (nextEndDetailData != null)
                    {
                        var notClearNum = workTransferEventData.CountNotClearDetailDataNum();
                        if (notClearNum >= 1)
                        {
                            // 現在の募集の中に未クリアのものが残っている
                            // "現在の募集終了まであと〇〇〇"
                            remainTimeText = GetRestTimeString(TextId.TransferEvent0030.Text(), nextEndDetailData.Master.EndDate);
                        }
                        else
                        {
                            if (nextEndDetailData.Master.EndDate >= transferEventData.EndDate)
                            {
                                // 現在の募集の中に未クリアのものが残っておらず、募集切り替えがない
                                // "特別移籍終了まであと〇〇〇"
                                remainTimeText = GetRestTimeString(TextId.TransferEvent0032.Text(), transferEventData.EndDate);
                            }
                            else
                            {
                                // 現在の募集の中に未クリアのものが残っておらず、募集切り替えがある
                                // "募集切り替えまであと〇〇〇"
                                remainTimeText = GetRestTimeString(TextId.TransferEvent0031.Text(), nextEndDetailData.Master.EndDate);
                            }
                        }
                    }
                    else
                    {
                        // ※ここには来ないはずですが、念のため
                        remainTimeText = GetRestTimeString(TextId.TransferEvent0032.Text(), transferEventData.EndDate);
                    }

                    _remainTimeText.text = remainTimeText;
                }
            }

            this.gameObject.SetActiveWithCheck(true);
        }

        /// <summary>
        /// コールバック：ボタン押下
        /// </summary>
        protected override void OnClick()
        {
            // 特別移籍イベントの開催期間中なら特別移籍イベントTOP画面へ遷移
            if (WorkDataManager.Instance.TransferEventData.IsOpen())
            {
                // 遷移前の画面を憶えておく
                TempData.Instance.TransferEventData.EntranceType = TransferEventDefine.EntranceType.MyPage;

                // 特別移籍TOP画面へ遷移
                TransferEventUtil.GoToTransferRotationView();
            }
            else
            {
                // 期間外エラーダイアログを出す
                TransferEventUtil.OpenErrorDialogNotInTerm();
            }
        }
        
        /// <summary>
        /// 特別移籍（イベント）ボタンにバッジが必要か
        /// </summary>
        /// <returns></returns>
        protected override bool IsNeedBadge()
        {
            return WorkDataManager.Instance.TransferEventData.CheckNeedBadgeAtTransferButton() ||
                   WorkDataManager.Instance.TransferRotationData.CheckNeedBadgeAtTransferButton();
        }

        /// <summary>
        /// 開催日時（開催日時順ソートで用いられる）
        /// </summary>
        /// <returns></returns>
        protected override long GetStartDate()
        {
            var transferEventId = WorkDataManager.Instance.TransferEventData.TransferId;
            var transferEventData = MasterDataManager.Instance.masterTransferEventData.Get(transferEventId);
            if (transferEventData != null)
            {
                return transferEventData.StartDate;
            }

            // マスタから取得できなかった場合は最低値を渡しておく
            return long.MinValue;
        }
    }
}