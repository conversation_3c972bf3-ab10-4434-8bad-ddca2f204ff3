using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using AnimateToUnity.Utility;
using AnimateToUnity;
using UnityEngine.UI;
using DG.Tweening;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSupportCardStrengthenAnimation : MonoBehaviour
    {
        #region FlashLabel

        private const string OBJ_BEFORE_00 = "OBJ_mc_num_lvup_bef00";
        private const string OBJ_AFTER_00 = "OBJ_mc_num_lvup_aft00";
        private const string MOT_LVUP_NUM_00 = "MOT_mc_num_lvup00";
        private const string MOT_LVUP_NUM = "MOT_num_lvup";
        private const string OBJ_LV_1 = "OBJ_num_lvup1";
        private const string OBJ_LV_10 = "OBJ_num_lvup10";

        private const string MOT_NUM_00 = "MOT_mc_num_lv00";
        private const string MOT_MAX = "MOT_mc_ico_max00";
        private const string MOTION_LV_UP = "MOT_mc_strengthen_lvup00";
        private const string MOT_CARD_LV_ROT = "MOT_mc_dum_card_main00";

        private const string IN = "in";
        private const string IN_NUM = "in_num";
        private const string IN_1 = "in1";
        private const string IN_00 = "in00";
        private const string IN_10 = "in10";
        private const string IN_1_1 = "in1_1";
        private const string IN_1_10 = "in1_10";
        private const string IN_10_10 = "in10_10";
        private const string EFF_IN_1_10 = "eff_in1_10";
        private const string EFF_IN_10_10 = "eff_in10_10";
        private const string IN_1_1_END = "in1_1_end";
        private const string IN_1_10_END = "in1_10_end";
        private const string IN_10_10_END = "in10_10_end";
        private const string IN_END = "in_end";
        private const string IN_ROT_END = "in_lvup_end";
        private const string OUT = "out";
        private const string END = "end";

        private const string IN_LVUP = "in_lvup";

        private const string IN_ACTION = "in_action";

        /// <summary>
        /// アニメーション後に待機する秒数
        /// </summary>
        private const float LIST_ANIMATION_END = 0.3f;
        
        #endregion

        /// <summary>
        /// フラッシュアクションプレイヤー
        /// </summary>
        private FlashActionPlayer _flashActionPlayer;

        /// <summary>
        /// レベルアップモーション
        /// </summary>
        private AnMotion _lvUpFlashMotion;

        /// <summary>
        /// レベルアップ時のカードモーション
        /// </summary>
        /// <returns></returns>
        private AnMotion _lvUpCardMotion;

        /// <summary>
        /// MAX表示モーション
        /// </summary>
        private AnMotion _maxMotion;

        /// <summary>
        /// エフェクト効果一覧
        /// </summary>
        private PartsSupportCardEffectTableList _effectTableList;

        /// <summary>
        /// ユニークスキル情報表示プレート
        /// </summary>
        private PartsSupportCardEffectTableItem _uniquePlate;

        /// <summary>
        /// Outのフラッシュがスタートするまでの秒数
        /// </summary>
        private const float FLASH_OUT_PLAY_WAIT = 0.9f;
        
        private const float EFFECT_LIST_ANIMATION_COUNT = 1.0f;
        
        /// <summary>
        /// EffectListのアニメーションが秒数
        /// </summary>
        private const float AUTO_SCROLL_SPEED_TO_TOP = 0.3f;
        private const float AUTO_SCROLL_SPEED = 0.3f;

        /// <summary>
        /// 桁数による出しわけ
        /// </summary>
        private const int DIGIT_LIMIT = 10;

        /// <summary>
        /// モーション群
        /// </summary>
        private AnMotion _beforeDigitMotion;

        private AnMotion _beforeValue1Motion;
        private AnMotion _beforeValue10Motion;
        private AnMotion _afterDigitMotion;
        private AnMotion _afterValue1Motion;
        private AnMotion _afterValue10Motion;

        private bool _isPlayAnimation = false;

        /// <summary>
        /// EDアニメーションは1回だけ呼ぶためのフラグ
        /// </summary>
        private bool _isPlayingEndingAnimation = false;

        public bool IsPlayAnimation
        {
            get { return _isPlayAnimation; }
        }

        /// <summary>
        /// Max表示するかどうか
        /// </summary>
        private bool _isLvMax = false;

        /// <summary>
        /// アニメーション終了時の呼び出し元の処理
        /// </summary>
        private Action _endAction = null;

        /// <summary>
        /// チームサポートボーナス
        /// </summary>
        private PartsTeamSupportBonusInfo _teamSupportBonusInfo = null;

        /// <summary>
        /// 黒フェード
        /// </summary>
        private ImageCommon _overlayBG = null;

        /// <summary>
        /// 元のリストの親を取得
        /// </summary>
        private Transform _transformListParent = null;

        /// <summary>
        /// スクロールの親
        /// </summary>
        private GameObject _scrollParent = null;

        /// <summary>
        /// 元のインデックスを覚えておく
        /// </summary>
        private int _scrollSiblingIndex = 0;
        
        /// <summary>
        /// アニメーションに関する初期化
        /// 変数取得やコントローラ取得など
        /// </summary>
        public void Initialize(SupportCardHaveListViewController controller)
        {
            _flashActionPlayer = controller.FlashPlayerSupportCard;
            _lvUpFlashMotion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOTION_LV_UP);
            _maxMotion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_MAX);
            _lvUpCardMotion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_CARD_LV_ROT);

            var objBefore00 = controller.FlashPlayerSupportCard.FlashPlayer.GetObj(OBJ_BEFORE_00);
            _beforeDigitMotion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_LVUP_NUM_00, true, objBefore00.GameObject);

            var objBefore_num_1 = controller.FlashPlayerSupportCard.FlashPlayer.GetObj(OBJ_LV_1, true, objBefore00.GameObject);
            _beforeValue1Motion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_LVUP_NUM, true, objBefore_num_1.GameObject);

            var objBefore_num_10 = controller.FlashPlayerSupportCard.FlashPlayer.GetObj(OBJ_LV_10, true, objBefore00.GameObject);
            _beforeValue10Motion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_LVUP_NUM, true, objBefore_num_10.GameObject);

            var objAfter00 = controller.FlashPlayerSupportCard.FlashPlayer.GetObj(OBJ_AFTER_00);
            _afterDigitMotion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_LVUP_NUM_00, true, objAfter00.GameObject);

            var objAfter_num_1 = controller.FlashPlayerSupportCard.FlashPlayer.GetObj(OBJ_LV_1, true, objAfter00.GameObject);
            _afterValue1Motion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_LVUP_NUM, true, objAfter_num_1.GameObject);

            var ovbjAfter_num_10 = controller.FlashPlayerSupportCard.FlashPlayer.GetObj(OBJ_LV_10, true, objAfter00.GameObject);
            _afterValue10Motion = controller.FlashPlayerSupportCard.FlashPlayer.GetMotion(MOT_LVUP_NUM, true, ovbjAfter_num_10.GameObject);
        }

        /// <summary>
        /// 強化アニメーション開始
        /// </summary>
        public void StartAnimation(SupportCardHaveListViewController controller, int beforeLv, int afterLv,
            GameObject ScrollParent, PartsSupportCardEffectTableList cardEffectTableList,
            PartsSupportCardEffectTableItem uniquePlate, Action AnimationEndCallBack, bool isMax, PartsTeamSupportBonusInfo teamSupportBonusInfo)
        {
            _isPlayingEndingAnimation = false;
            
            _flashActionPlayer.gameObject.SetActive(true);
            _isPlayAnimation = true;
            _effectTableList = cardEffectTableList;
            _isLvMax = isMax;
            _uniquePlate = uniquePlate;
            _scrollParent = ScrollParent;
            _endAction = AnimationEndCallBack;
            _teamSupportBonusInfo = teamSupportBonusInfo;

            AudioManager.Instance.PlayAudio(AudioId.SFX_SUPPORTCARD_LVUP);

            //黒フェードかける
            Color fadeColor = Color.black;
            fadeColor.a = 0f;
            _overlayBG = controller.GetView().UIOverlayBG;
            _overlayBG.gameObject.SetActive(true);
            _overlayBG.SetMulColor(fadeColor);

            //UniqueSkill取得するかどうか
            var masterUniqueEffect = controller.SupportCardData.GetMasterUniqueEffect();
            int getUniqueEffectLv = masterUniqueEffect == null ? 0 : (int) masterUniqueEffect.Lv;
            bool isGetUniqueSkill = false;
            if (beforeLv < getUniqueEffectLv && getUniqueEffectLv <= afterLv)
            {
                isGetUniqueSkill = true;
            }

            //アニメーションに関係あるオブジェクトのみキャンバスを分ける
            _flashActionPlayer.SetSortLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            _transformListParent = _scrollParent.transform.parent;
            teamSupportBonusInfo.SetSortLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);

            //元のインデックス覚えておく
            _scrollSiblingIndex = _scrollParent.transform.GetSiblingIndex();

            //親子関係整理
            _scrollParent.transform.SetParent(_overlayBG.transform);

            //ボイスを再生
            AudioManager.Instance.PlaySystemVoice_OtherSupportCardLevelUp(controller.SupportCardData.GetCharaId());

            //黒フェード後、アニメーション開始
            _overlayBG.DOFade(GameDefine.BLACK_FADE_ALPHA, GameDefine.UI_OVERRAY_FADE_TIME).OnComplete(() =>
                {
                    //固有スキルのボタンを押せないようにする
                    if (uniquePlate != null)
                    {
                        uniquePlate.ButtonOnOff(false);
                    }
                    StartAnimation(beforeLv, afterLv, controller, isMax, isGetUniqueSkill);
                }
            );
        }

        /// <summary>
        /// レベルアップ演出スタート
        /// </summary>
        private void StartAnimation(int beforeLv, int afterLv, SupportCardHaveListViewController controller, bool isMaxLv, bool isUniqueSkillGet)
        {
            //フラッシュ再生
            _flashActionPlayer.FlashPlayer.GetMotion(MOT_NUM_00).SetMotionPlay(IN_00);
            
            //Inを通った時にレベルアップアニメーション準備
            //一番上が映っていなかったらスクロール
            if (_effectTableList.IsOverTop(0))
            {
                //スクロールアニメーション再生
                _effectTableList.AutoPlayScroll(0,false, AUTO_SCROLL_SPEED_TO_TOP);
            }
            //アニメーション用に情報ソート
            _effectTableList.SortListForAnimation(controller.SupportCardData, null);
            if (isUniqueSkillGet)
            {
                _uniquePlate.SetUpUnlockAnimation();
            }
            
            //Lvアップ演出再生
            _flashActionPlayer.FlashPlayer.Play(IN_LVUP);

            //mc_strengthen_lvup00がin_numになった時に数字のMotion再生
            _lvUpFlashMotion.SetAction(IN_NUM, () =>
            {
                PlayNumberLabelPattern(beforeLv, afterLv,isMaxLv);
            },AnMotionActionTypes.Start);
            
            //mc_strengthen_lvup00がin_endになった時にそれぞれ終了モーションを呼ぶ
            _lvUpFlashMotion.SetAction(IN_END, () =>
            {
                DOTween.Sequence().PrependCallback(() =>
                {
                    _isPlayAnimation = false;
                    _lvUpFlashMotion.SetAction(END, () =>
                    {
                        StartCoroutine(OnAnimationEnd());
                    }, AnMotionActionTypes.Start);
                    _lvUpFlashMotion.SetMotionPlay(OUT);
                }).SetDelay(FLASH_OUT_PLAY_WAIT);
            }, AnMotionActionTypes.Start);
            
            //レベルアップアニメーションを同時に開始
            _flashActionPlayer.FlashPlayer.SetActionCallBack(IN_ACTION, () =>
            {
                //レベルアップアニメーション開始
                _effectTableList.PlayListAnimationOnLvUp(isUniqueSkillGet, _uniquePlate,
                    () =>
                    {
                        StartCoroutine(
                            _teamSupportBonusInfo.UpdateSupportBonusNumComfirm(
                                _effectTableList.AddAnimationCount > 0 || isUniqueSkillGet,
                                    () =>
                                    {
                                        _effectTableList.IsLvUpAnimationPlaying = false;
                                        StartCoroutine(OnAnimationEnd());
                                    }
                                )
                            );
                    });

            }, AnMotionActionTypes.Start);
        }

        /// <summary>
        /// アニメーション終了時に関数実行
        /// </summary>
        /// <returns></returns>
        private IEnumerator OnAnimationEnd()
        {
            if (!_isPlayAnimation && !_effectTableList.IsLvUpAnimationPlaying && !_isPlayingEndingAnimation)
            {
                _isPlayingEndingAnimation = true;
                yield return new WaitForSeconds(LIST_ANIMATION_END);
                //アニメーションが終わったら黒フェード戻す
                _overlayBG.DOFade(GameDefine.BLACK_FADE_ALPHA, GameDefine.UI_OVERRAY_FADE_TIME).OnComplete(() =>
                {
                    //待機に戻す
                    _flashActionPlayer.FlashPlayer.GetMotion(MOT_NUM_00).SetMotionPlay(IN_00);
                    _maxMotion.SetMotionPlay(IN_00);
                    _flashActionPlayer.FlashPlayer.Play(IN_00);

                    //キャンバスを戻す
                    _scrollParent.transform.SetParent(_transformListParent);
                    _scrollParent.transform.SetSiblingIndex(_scrollSiblingIndex);

                    _flashActionPlayer.SetSortLayer(UIManager.UI_SORTING_LAYER_NAME);
                    _teamSupportBonusInfo.SetSortLayer(UIManager.UI_SORTING_LAYER_NAME);
                    _overlayBG.gameObject.SetActive(false);

                    //フラッシュ側のエフェクト消す
                    _flashActionPlayer.CleanupActionInstance();

                    //固有スキルのボタンを押せるようにする
                    if (_uniquePlate != null)
                    {
                        _uniquePlate.ButtonOnOff(true);
                    }

                    //呼び出し元の処理
                    _endAction?.Invoke();
                });
            }
        }

        /// <summary>
        /// レベルごとのラベル再生分岐
        /// </summary>
        /// <param name="beforeValue"></param>
        /// <param name="afterValue"></param>
        private string PlayNumberLabelPattern(int beforeValue, int afterValue,bool isMaxLv)
        {
            string labelName = string.Empty;

            //数字反映

            if (beforeValue < DIGIT_LIMIT && afterValue < DIGIT_LIMIT)
            {
                _beforeValue1Motion.SetMotionPlay(string.Format("num{0}", beforeValue));
                _afterValue1Motion.SetMotionPlay(string.Format("num{0}", afterValue));

                _beforeDigitMotion.SetMotionPlay(IN_1);
                _afterDigitMotion.SetMotionPlay(IN_1);

                _flashActionPlayer.Play(IN_1_1, MOT_NUM_00);
            }
            else if (beforeValue < DIGIT_LIMIT && afterValue >= DIGIT_LIMIT)
            {
                _beforeValue1Motion.SetMotionPlay(string.Format("num{0}", beforeValue % DIGIT_LIMIT));
                _afterValue1Motion.SetMotionPlay(string.Format("num{0}", afterValue % DIGIT_LIMIT));
                _afterValue10Motion.SetMotionPlay(string.Format("num{0}", afterValue / DIGIT_LIMIT));

                _beforeDigitMotion.SetMotionPlay(IN_1);
                _afterDigitMotion.SetMotionPlay(IN_10);

                //Maxなら専用フラッシュも再生
                _flashActionPlayer.FlashPlayer.GetMotion(MOT_NUM_00).SetAction(EFF_IN_1_10, () =>
                {
                    if (isMaxLv)
                    {
                        _maxMotion.SetMotionPlay(IN);
                    }
                },AnMotionActionTypes.Start);
                
                _flashActionPlayer.Play(IN_1_10, MOT_NUM_00);
            }
            else if (beforeValue >= DIGIT_LIMIT && afterValue >= DIGIT_LIMIT)
            {
                _beforeValue1Motion.SetMotionPlay(string.Format("num{0}", beforeValue % DIGIT_LIMIT));
                _beforeValue10Motion.SetMotionPlay(string.Format("num{0}", beforeValue / DIGIT_LIMIT));
                _afterValue1Motion.SetMotionPlay(string.Format("num{0}", afterValue % DIGIT_LIMIT));
                _afterValue10Motion.SetMotionPlay(string.Format("num{0}", afterValue / DIGIT_LIMIT));

                _beforeDigitMotion.SetMotionPlay(IN_10);
                _afterDigitMotion.SetMotionPlay(IN_10);
                
                //Maxなら専用フラッシュも再生
                _flashActionPlayer.FlashPlayer.GetMotion(MOT_NUM_00).SetAction(EFF_IN_10_10, () =>
                {
                    if (isMaxLv)
                    {
                        _maxMotion.SetMotionPlay(IN);
                    }
                },AnMotionActionTypes.Start);
                
                _flashActionPlayer.Play(IN_10_10, MOT_NUM_00);
            }

            return labelName;
        }
    }
}