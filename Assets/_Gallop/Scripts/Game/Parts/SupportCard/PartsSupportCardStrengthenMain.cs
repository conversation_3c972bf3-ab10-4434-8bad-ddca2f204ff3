using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using AnimateToUnity.Utility;
using AnimateToUnity;
using UnityEngine.UI;
using DG.Tweening;
using UnityEngine.Serialization;
using static Gallop.StaticVariableDefine.Parts.PartsSupportCardStrengthenMain;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSupportCardStrengthenMain : MonoBehaviour, IPartsSupportCardManageMain
    {
        #region 定数

        /// <summary>
        /// 下にスクロールしていくリスト数
        /// </summary>
        private const float AUTO_SCROLL_COUNT = 3;
        
        /// <summary>
        /// 自動で↑にスクロールする秒数
        /// </summary>
        private const float AUTO_SCROLL_UP = 0.133f;
        
        /// <summary>
        /// 自動で下にスクロールする秒数の1リスト文
        /// </summary>
        private const float AUTO_SCROLL_DOWN = 0.067f;
        
        /// <summary>
        /// 自動で↑にスクロールする秒数
        /// </summary>
        private const float SCROLL_WAIT_TOP = 0.2f;

        /// <summary>
        /// 次スクロールするまでに必要な秒数
        /// </summary>
        private const float SCROLL_ANIMATION_END_WAIT = 0.5f;

        #endregion

        [field: SerializeField, RenameField]
        public PartsSupportCardEffectTableList EffectTableList;
        [field: SerializeField, RenameField]
        public PartsSupportCardStatusTextGroupRoot StatusTextGroup = null;
        [field: SerializeField, RenameField]
        public PartsTeamSupportBonusInfo TeamSupportBonusInfo = null;

        [SerializeField]
        private TextCommon _textCurrentLevel = null;
        [SerializeField]
        private TextCommon _textNextLevel = null;

        [SerializeField]
        private PartsCardPowerUpArrow _partsPowerUpArrow = null;

        [SerializeField]
        private GameObject _horizontalMoneyLayoutGroup = null;
        [SerializeField]
        private TextCommon _textMoneyHaveCount = null;
        [SerializeField]
        private TextCommon _textMoneyNeedCount = null;

        [SerializeField]
        private GameObject _horizontalExpLayoutGroup = null;
        [SerializeField]
        private TextCommon _textExpHaveCount = null;
        [SerializeField]
        private TextCommon _textExpNeedCount = null;

        [SerializeField]
        private TextCommon _maxLevelText = null;

        [SerializeField]
        private RectTransform _maxDescTextObj = null;

        [SerializeField]
        private TextCommon _goLimitBreakText = null;

        [SerializeField]
        private GameObject _nextMaxObject = null;

        [SerializeField]
        private TextCommon _warningText = null;

        [SerializeField]
        private ButtonCommon _buttonLimitBreak = null;

        /// <summary>
        /// サポートカードのアニメーションクラス
        /// </summary>
        private PartsSupportCardStrengthenAnimation _animationClass;

        /// <summary>
        /// nextLevelアップに使う文字アニメーション
        /// </summary>
        [SerializeField]
        private TweenAnimationTimelineComponent _textNextAnimation = null;

        /// <summary>
        /// nextLevelアップのアニメーション戻す用
        /// </summary>
        [SerializeField]
        private CanvasGroup _textNextLevelCanvas = null;

        private int _beforeCardLv = 0;
        private int _afterCardLv = 0;

        private int _needExperienceNum = 0;
        private int _haveExperienceNum = 0;
        private int _needMoneyNum = 0;
        private int _haveMoneyNum = 0;

        /// <summary> _textWarningに設定されたテキストId </summary>
        private TextId _warningTextId = TextId.None;


        private SupportCardHaveListViewController _controller = null;

        /// <summary>
        /// アニメーション全体のTween
        /// </summary>
        private Sequence _lvUpAnimationSequence;

        private int _maxLevel = 0;
        private int _borderLevel = 0;
        private int _minLevel = 0;

        /// <summary>
        /// 解像度によって配置が分かれるUIがある
        /// </summary>
        private const float BASE_WIDTH = 9f;
        private const float BASE_HEIGHT = 19f;

        private const float MAX_DESC_TEXT_ANCHOREDPOS_Y = -268.5f;

        /// <summary>
        /// 初回の初期化が実行されたか
        /// </summary>
        private bool _alreadySetUpDone = false;

        /// <summary>
        /// 固有ボーナスを持っているかどうか
        /// </summary>
        private bool _haveUniqueEffect = false;

        /// <summary>
        /// スクロールアニメーション中かどうか
        /// </summary>
        private bool _isScrollAnimationPlaying = false;
        
        /// <summary>
        /// スクロールフラグをオンオフするタイマー
        /// </summary>
        private float _scrollTimer = 0f;

        /// <summary>
        /// レベルアップボイスをロードするキャラクターのリスト
        /// </summary>
        private List<int> _loadCharacterIdList = new List<int>();


        /// <summary>
        /// controller設定
        /// </summary>
        /// <param name="controller"></param>
        public void Setup(SupportCardHaveListViewController controller)
        {
            if (_alreadySetUpDone)
            {
                return;
            }

            _controller = controller;
            _buttonLimitBreak.SetOnClick(() =>
            {
                _controller.ChangePage(SupportCardHaveListViewController.PageType.LimitBreak);
            });

            _alreadySetUpDone = true;

            //アニメクラス生成
            _animationClass = gameObject.GetComponent<PartsSupportCardStrengthenAnimation>();
            _animationClass.Initialize(_controller);
            
            //キャッシュ取得
            if (_controller == null || _controller.SupportCardData == null)
            {
                return;
            }
        }

        /// <summary>
        /// リソース破棄
        /// </summary>
        public void Release()
        {
            _controller = null;
            _buttonLimitBreak.SetOnClick(null);
            _animationClass = null;
            _alreadySetUpDone = false;
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize()
        {
            // _partsPowerUpArrow.CurrentValueが古い値の場合があるので先行して数値を設定しなおす.
            bool isLevelMax = _controller.SupportCardData.IsLevelMax();
            if (!isLevelMax)
            {
                UpdatePowerUpArrow();
            }

            //表示更新
            UpdateStrengthenMainUI();
        }

        /// <summary>
        /// スクロールのカウントUpdate
        /// </summary>
        public void UpdateScrollTimer()
        {
            if (_isScrollAnimationPlaying)
            {
                _scrollTimer += Time.deltaTime;
                if (_scrollTimer > SCROLL_ANIMATION_END_WAIT)
                {
                    _scrollTimer = 0f;
                    _isScrollAnimationPlaying = false;
                }
            }
        }

        /// <summary>
        /// サポカ効果スクロールリスト生成
        /// </summary>
        public void CreateEffectTableList(WorkSupportCardData.SupportCardData supportCardData)
        {
            var effectList = supportCardData.GetMasterSupportCardEffectList();
            EffectTableList.SetUpList(supportCardData, effectList);
        }

        /// <summary>
        /// サポカ効果スクロールリスト情報更新
        /// </summary>
        public void UpdateEffectTableListInfo(WorkSupportCardData.SupportCardData supportCardData, bool isShowNextValue, int currentArrowValue)
        {
            PartsSupportCardEffectTableItem uniquePlate = null;
            if (StatusTextGroup.UniqueEffectPlate != null)
            {
                uniquePlate = StatusTextGroup.UniqueEffectPlate;
            }
            EffectTableList.UpdateDisplayListInfo(supportCardData, isShowNextValue, currentArrowValue,uniquePlate);
        }

        /// <summary>
        /// 矢印パーツ関連の情報更新
        /// </summary>
        public void UpdatePowerUpArrow()
        {
            _minLevel = _controller.SupportCardData.Level + 1;
            _maxLevel = _controller.SupportCardData.MaxLevel;
            
            //境界線レベルを設定
            _borderLevel = _controller.SupportCardData.GetMaxStrengthLevelMyItem(_haveExperienceNum, _haveMoneyNum);
            //矢印パーツにレベルなどを設定
            _partsPowerUpArrow.Setup(_minLevel, _borderLevel, _maxLevel, UpdateArrowStatusListUI, PartsCardPowerUpArrow.UIPageType.SupportCardStrengthen, _controller.SupportCardData.IsLimitBreakMax());
        }

        /// <summary>
        /// 表示更新
        /// </summary>
        public void UpdateStrengthenMainUI()
        {
            //サポートカードデータのキャッシュ取得
            WorkSupportCardData.SupportCardData supportCardData = _controller.SupportCardData;

            //EffectTableList情報更新
            UpdateEffectTableListInfo(_controller.SupportCardData, false, _partsPowerUpArrow.CurrentValue);

            //固有スキル情報更新
            if (_controller.SupportCardData.GetMasterSupportCard().UniqueEffectId != 0)
            {
                StatusTextGroup.SetUniqueSkillPlate(supportCardData, _partsPowerUpArrow.CurrentValue);
                StatusTextGroup.OnOffUniqueEffectPlate(true);
            }
            else
            {
                StatusTextGroup.OnOffUniqueEffectPlate(false);
            }

            // テキスト更新をかけるためにリセットしておく
            _warningTextId = TextId.None;

            //強化できるかどうか判定
            bool isLevelMax = _controller.SupportCardData.IsLevelMax();
            bool isLimitBreakMax = _controller.SupportCardData.IsLimitBreakMax();

            //levelMax表示(数字のすぐ下にあるもの)
            _maxLevelText.gameObject.SetActive(isLevelMax);
            _maxLevelText.text = String.Format("{0} {1}", TextId.Common0127.Text(), supportCardData.Level);
            _maxDescTextObj.gameObject.SetActive(isLevelMax);
            
            //>を押して表示されるMaxは一旦表示リセット
            _nextMaxObject.SetActive(false);
            
            if (isLevelMax)
            {
                float screenWidth = Screen.Width;
                float screenHeight = Screen.Height;
                if (screenWidth / screenHeight > BASE_WIDTH / BASE_HEIGHT && !isLimitBreakMax)
                {
                    _maxDescTextObj.transform.SetParent(gameObject.transform);

                    Vector2 tmpAnchoredPos = Math.VECTOR2_ZERO;
                    tmpAnchoredPos.x = _maxDescTextObj.anchoredPosition.x;
                    tmpAnchoredPos.y = MAX_DESC_TEXT_ANCHOREDPOS_Y;
                    _maxDescTextObj.anchoredPosition = tmpAnchoredPos;
                }
                else
                {
                    _maxDescTextObj.transform.SetParent(_goLimitBreakText.gameObject.transform.parent);
                    _maxDescTextObj.transform.SetSiblingIndex(0);
                }
                SetActiveButton(false,false,false);
            }

            //上限解放まだなら促す
            _goLimitBreakText.gameObject.SetActive(isLevelMax && !isLimitBreakMax);
            _buttonLimitBreak.SetActiveWithCheck(isLevelMax && !isLimitBreakMax);

            //強化に使うもの非表示
            _textCurrentLevel.gameObject.SetActive(!isLevelMax);
            _textNextLevel.gameObject.SetActive(!isLevelMax);
            _horizontalMoneyLayoutGroup.SetActive(!isLevelMax);
            _horizontalExpLayoutGroup.SetActive(!isLevelMax);
            _partsPowerUpArrow.gameObject.SetActive(!isLevelMax);

            //レベルに紐づくUI更新
            if (!isLevelMax)
            {
                _partsPowerUpArrow.ParameterListAnimation = false;
                _partsPowerUpArrow.ButtonPushAnimation = false;
                UpdatePowerUpArrow();
                UpdateArrowStatusListUI(_controller.SupportCardData.Level + 1);
            }
            else
            {
                UpdateEffectStatusList(_controller.SupportCardData.Level,false);
            }
            
            //Layout更新
            StatusTextGroup.ForceRebuildLayoutImmediate(true);
        }

        /// <summary>
        /// 最大レベルかどうか
        /// </summary>
        /// <returns></returns>
        public bool IsLimit()
        {
            return _controller.SupportCardData.IsLevelMax();
        }

        /// <summary>
        /// 矢印ボタン更新
        /// </summary>
        /// <param name="level"></param>
        public void UpdateArrowStatusListUI(int level)
        {
            UpdatePowerUpArrowUI(level);
            UpdateEffectStatusList(level,_partsPowerUpArrow.ParameterListAnimation);
        }

        /// <summary>
        /// 矢印ボタンに紐づくUI更新
        /// </summary>
        public void UpdatePowerUpArrowUI(int level)
        {
            _beforeCardLv = _controller.SupportCardData.Level;
            _afterCardLv = level;

            //最大Lvに強化しようとしていたらMax表記(素材の下地の境界線に被るMAX)
            if (_afterCardLv == _controller.SupportCardData.MaxLevel && !_controller.SupportCardData.IsLevelMax())
            {
                _nextMaxObject.SetActive(true);
            }
            else
            {
                _nextMaxObject.SetActive(false);
            }
            
            //レベルテキスト更新
            _textCurrentLevel.text = TextUtil.Format("{0} {1}", TextId.Common0216.Text(), _beforeCardLv);
            _textNextLevel.text = TextUtil.Format("{0} {1}", TextId.Common0216.Text(), _afterCardLv);

            // サポカ応援ボーナス表示更新
            TeamSupportBonusInfo.UpdateSupportBonusNumTemporary(_beforeCardLv, _afterCardLv);

            //Exp更新
            _haveExperienceNum = GallopUtil.GetHaveItemNum(GameDefine.ItemCategory.EXPERIENCE, GameDefine.SUPPORT_CARD_EXP_ITEM_ID);
            _textExpHaveCount.text = _haveExperienceNum.ToString(TextUtil.CommaSeparatedFormat);

            int needExp = _controller.SupportCardData.GetTargetLevelTotalExp(level) - _controller.SupportCardData.Exp;

            //>,<を押したときだけアニメーション
            if (_partsPowerUpArrow.ButtonPushAnimation)
            {
                DOTween.To(() => _needExperienceNum,
                    _needExperienceNum =>
                        _textExpNeedCount.text = _needExperienceNum.ToString(TextUtil.CommaSeparatedFormat), needExp,
                    0.15f).OnComplete(() => { _needExperienceNum = needExp; }
                );
            }
            else
            {
                _needExperienceNum = needExp;
                _textExpNeedCount.text = needExp.ToString(TextUtil.CommaSeparatedFormat);
            }

            bool isEnoughExp = _haveExperienceNum >= needExp;
            _textExpHaveCount.FontColor = !isEnoughExp ? FontColorType.Warning : FontColorType.Brown;

            //マニー更新
            _haveMoneyNum = GallopUtil.GetHaveItemNum(GameDefine.ItemCategory.MONEY, GameDefine.MONEY_ITEM_ID);
            _textMoneyHaveCount.text = _haveMoneyNum.ToString(TextUtil.CommaSeparatedFormat);
            int needMoney = GallopUtil.GetMoneyForSupportCardExp(needExp);

            if (_partsPowerUpArrow.ButtonPushAnimation)
            {
                DOTween.To(() => _needMoneyNum,
                    _needMoneyNum => _textMoneyNeedCount.text = _needMoneyNum.ToString(TextUtil.CommaSeparatedFormat),
                    needMoney, 0.15f).OnComplete(() => { _needMoneyNum = needMoney; }
                );
            }
            else
            {
                _needMoneyNum = needMoney;
                _textMoneyNeedCount.text = needMoney.ToString(TextUtil.CommaSeparatedFormat);
            }

            bool isEnoughMoney = _haveMoneyNum >= needMoney;
            _textMoneyHaveCount.FontColor = !isEnoughMoney ? FontColorType.Warning : FontColorType.Brown;

            //素材最大限より超えた先をユーザーが見ようとしていたら
            if (_partsPowerUpArrow.IsCurrentValueOverBorderLevel)
            {
                _borderLevel = _maxLevel;
            }
            else
            {
                _borderLevel = _controller.SupportCardData.GetMaxStrengthLevelMyItem(_haveExperienceNum, _haveMoneyNum);
            }

            // 所持素材を超えて表示しようとしているか、いないかの設定を反映する
            // 通常は「持っている素材で上げられる上限値」に設定し、既に上限値を超えている場合は「最大値」に設定する
            _partsPowerUpArrow.SetUpBorder(_borderLevel);

            //矢印ボタンが押せるかどうか設定
            _partsPowerUpArrow.CheckButtonInteractable();

            //Centerボタン設定
            bool buttonActive = isEnoughExp && isEnoughMoney && !_controller.SupportCardData.IsLevelMax();
            SetActiveButton(buttonActive,isEnoughExp,isEnoughMoney);
        }

        /// <summary>
        /// 右上にあるエフェクト群の表示を更新
        /// </summary>
        public void UpdateEffectStatusList(int level,bool withScrollAnimation)
        {
            //アニメーション判定に利用する共通変数初期化
            StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.CountNewItem = 0;
            StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.UnlockLvUpIndex = 0;
            StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMinNewIndex = 0;
            StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex = 0;
            
            //ステータスアップ情報更新
            UpdateEffectTableListInfo(_controller.SupportCardData, true, level);
            if (_controller.SupportCardData.IsHaveUniqueEffect())
            {
                //固有スキル情報更新
                StatusTextGroup.SetUniqueSkillPlate(_controller.SupportCardData, level);
            }
            
            _scrollTimer = 0f;
            //スクロールアニメーションさせる条件チェック
            if (IsScrollAnimation() && withScrollAnimation)
            {
                //リストの一番上が映ってなければ↑にスクロール後下にスクロール
                if (EffectTableList.IsOverTop(0))
                {
                    _isScrollAnimationPlaying = true;
                    //自動スクロール
                    EffectTableList.AutoPlayScroll(StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMinNewIndex,false, AUTO_SCROLL_UP, () =>
                    {
                        if (StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex - StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMinNewIndex > AUTO_SCROLL_COUNT)
                        {
                            //最大最小が3つより大きければ下にスクロールしていく(0.2秒待つ)
                            EffectTableList.AutoPlayScroll(StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex,true,
                                AUTO_SCROLL_DOWN * (StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex - StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMinNewIndex), null,SCROLL_WAIT_TOP);
                        }

                        //効果値更新
                        EffectTableList.UpdateDisplayItemList(true, StatusTextGroup.UniqueEffectPlate);
                    });
                }
                else
                {
                    //上にスクロールする必要がなければ下にのみ自動スクロール(下が映ってなければ)
                    if (EffectTableList.IsBottomDown(StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex))
                    {
                        _isScrollAnimationPlaying = true;
                        EffectTableList.AutoPlayScroll(StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex,true,
                            AUTO_SCROLL_DOWN * (StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMaxNewIndex - StaticVariableDefine.Parts.PartsSupportCardEffectTableItem.ValueUpMinNewIndex),null,SCROLL_WAIT_TOP);
                    }

                    //効果値更新
                    EffectTableList.UpdateDisplayItemList(true, StatusTextGroup.UniqueEffectPlate);
                }
            }
            else
            {
                //効果値更新
                EffectTableList.UpdateDisplayItemList(false,StatusTextGroup.UniqueEffectPlate);
            }
            TeamSupportBonusInfo.Setup();
        }

        /// <summary>
        /// スクロールアニメーションするかどうか
        /// </summary>
        /// <returns></returns>
        private bool IsScrollAnimation()
        {
            if (_partsPowerUpArrow.ParameterListAnimation && !_partsPowerUpArrow.IsLongTapRightNow() && !_isScrollAnimationPlaying)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// ボタンオンオフ設定
        /// </summary>
        /// <param name="buttonActive"></param>
        /// <param name="isEnoughExp"></param>
        /// <param name="isEnoughMoney"></param>
        private void SetActiveButton(bool buttonActive, bool isEnoughExp, bool isEnoughMoney)
        {
            string notifyText = string.Empty;
            var currentWarningTextId = TextId.None;
            _warningText.gameObject.SetActive(false);

            if (!buttonActive)
            {
                if (_controller.SupportCardData.IsLevelMax())
                {
                    notifyText = _controller.GetNotificationTextLimit();
                }
                else
                {
                    if (!isEnoughExp && !isEnoughMoney)
                    {
                        currentWarningTextId = TextId.Character0212;
                    }
                    else if (!isEnoughExp)
                    {
                        currentWarningTextId = TextId.Character0209;
                    }
                    else if (!isEnoughMoney)
                    {
                        currentWarningTextId = TextId.Character0203;
                    }

                    notifyText = currentWarningTextId.Text();
                    // アイコンチラつき防止のため同じテキストでない場合のみ更新をかける
                    if (currentWarningTextId != _warningTextId)
                    {
                        TextUtil.SetWarningTextWithIcon(_warningText, notifyText);
                    }

                    _warningText.gameObject.SetActive(true);
                }

                _textNextLevel.VerticalGradientColor = VerticalGradientColorType.NotAvailable;
                _textNextLevel.UpdateColor();
                //点滅アニメーションストップ
                _textNextAnimation.Stop();
                _textNextLevelCanvas.alpha = 1.0f;
            }
            else
            {
                _textNextLevel.VerticalGradientColor = VerticalGradientColorType.SupportCardLevelUp;
                _textNextLevel.UpdateColor();

                if (!_textNextAnimation.IsPlaying())
                {
                    //点滅アニメーションスタート
                    _textNextAnimation.PlayLoop();
                }
            }

            _warningTextId = currentWarningTextId;
            _controller.SetCenterButtonInteractable(buttonActive, notifyText);
        }

        /// <summary>
        /// ボタンを押したときにダイアログ出す
        /// </summary>
        public void OnClickCenterButton()
        {
            //アニメ途中でボタンを押される可能性ある為、再計算しておく
            int needExp = _controller.SupportCardData.GetTargetLevelTotalExp(_partsPowerUpArrow.CurrentValue) - _controller.SupportCardData.Exp;
            _needExperienceNum = needExp;
            _needMoneyNum = GallopUtil.GetMoneyForSupportCardExp(needExp);

            //固有スキルの有無をこのタイミングでチェックしておく
            if (_controller.SupportCardData.GetMasterSupportCard().UniqueEffectId != 0)
            {
                if (_controller.SupportCardData.GetMasterUniqueEffect().Lv <= _controller.SupportCardData.Level)
                {
                    _haveUniqueEffect = true;
                }
                else
                {
                    _haveUniqueEffect = false;
                }
            }

            DialogSupportCardStrengthenConfirm.Open(_controller.SupportCardData, _needExperienceNum, _haveExperienceNum, _needMoneyNum, _haveMoneyNum, _beforeCardLv, _afterCardLv, () =>
            {
                //点滅アニメーションストップ
                _textNextAnimation.Stop();
                _textNextLevelCanvas.alpha = 1.0f;

                //ボタンが二回押されるのを防ぐ(演出側でパネルを置いているが、演出側が変わって二回押背てしまう方が問題なので残しておく)
                _controller.SetCenterButtonInteractable(false, string.Empty);

                //BackKeyOffにする
                _controller.IsBackKeyEnable = false;

                //リソースをダウンロードしてから処理する
                _loadCharacterIdList.Clear();
                _loadCharacterIdList.Add(_controller.SupportCardData.GetCharaId());
                DownloadPathRegister register = DownloadManager.GetNewRegister();
                AudioManager.Instance.RegisterDownloadByTriggerAndCharaIds(register, _loadCharacterIdList, TRIGGER_LIST);
                DownloadManager.Instance.FixDownloadList(ref register);
                DownloadManager.Instance.DownloadFromRegister(() =>
                {
                    //アニメーションスタート
                    _animationClass.StartAnimation(_controller, _beforeCardLv, _afterCardLv, StatusTextGroup.gameObject, EffectTableList, StatusTextGroup.UniqueEffectPlate, UpdateInfoAnimationEnd, _controller.SupportCardData.IsNextLevelMax(_afterCardLv), TeamSupportBonusInfo);
                    //バッジ更新
                    _controller.ApplyBadge();
                });
            });

        }

        /// <summary>
        /// アニメーション終了時の挙動
        /// </summary>
        private void UpdateInfoAnimationEnd()
        {
            //表示更新
            UpdateStrengthenMainUI();

            // refs #69476 サポカ上限解放などと同様にレベルアップ演出後にデータの更新を行う
            _controller.ReSelectSupportCard();

            if (_controller.SupportCardData.GetMasterSupportCard().UniqueEffectId != 0)
            {
                var uniqueEffect = _controller.SupportCardData.GetMasterUniqueEffect();
                //強化によって固有ボーナスを手に入れたかどうか
                if (!_haveUniqueEffect && uniqueEffect.Lv <= _controller.SupportCardData.Level)
                {
                    DialogUniqueEffectDetailSimple.Open(uniqueEffect);
                }
            }
            
            //BackKeyOnにする
            _controller.IsBackKeyEnable = true;
        }
    }
}
