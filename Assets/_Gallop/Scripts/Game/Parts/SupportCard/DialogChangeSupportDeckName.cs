using Cute.Http;
using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// デッキ名変更ダイアログ
    /// </summary>
    public class DialogChangeSupportDeckName : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        #endregion


        #region SerializeField, 変数

        [SerializeField]
        private InputFieldCommon _inputField = null;

        [SerializeField]
        private TextCommon _bodyText = null;

        private DialogCommon _dialog = null;
        private string _preName;

        #endregion


        #region メソッド
        
        public void Setup(string deckName, string bodyText)
        {
            _preName = deckName;
            _bodyText.text = bodyText;
            _inputField.text = deckName;
            _inputField.onEndEdit.AddListener(OnEndEdit);
            OnEndEdit(deckName);
        }

        public void SetDialog(DialogCommon dialog)
        {
            _dialog = dialog;
        }
        
        /// <summary>
        /// 編集終了時に呼ばれる
        /// </summary>
        private void OnEndEdit(string text)
        {
            if (text.Length > GameDefine.USER_NAME_LIMIT)
            {
                _inputField.text = text.Substring(0, GameDefine.USER_NAME_LIMIT);
            }

            bool isValid = CheckText(_inputField.text);
            var btn = _dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
            btn.interactable = isValid;
        }

        /// <summary>
        /// クライアント側での文字列チェック
        /// </summary>
        /// <param name="newText"></param>
        /// <returns></returns>
        private bool CheckText(string newText)
        {
            //空
            if (string.IsNullOrEmpty(newText) || _preName.Equals(newText))
            {
                return false;
            }
            return true;
        }


        /// <summary>
        /// トレーナー名変更APIを送信
        /// </summary>
        /// <param name="name"></param>
        /// <param name="onSuccess"></param>
        public void SendChangeSupportDeckNameAPI(int deckId, string name, Action<SupportCardDeckChangeNameResponse> onSuccess)
        {
            var req = new SupportCardDeckChangeNameRequest
            {
                deck_id = deckId,
                name = name
            };
            req.Send(onSuccess);
        }

        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(string title, string bodyText, string deckName, Action<DialogCommon, string> onDecide)
        {
            var changeSupportDeckName = LoadAndInstantiatePrefab<DialogChangeSupportDeckName>(ResourcePath.DIALOG_CHANGE_SUPPORT_DECK_NAME_PATH);

            DialogCommon.Data dialogData = changeSupportDeckName.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.Title = title;
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = d => d.Close();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = (dialog) =>
            {
                onDecide?.Invoke(dialog, changeSupportDeckName._inputField.text);
            };
            
            var dialogCommon = DialogManager.PushDialog(dialogData);
            changeSupportDeckName.SetDialog(dialogCommon);
            changeSupportDeckName.Setup(deckName, bodyText);
        }

        #endregion
    }
}
