using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 進行状況
    /// </summary>
    [AddComponentMenu("")]
    public class PartsTrainerInfoProgress : MonoBehaviour
    {
        /// <summary> 作成時のパラメータ定義 </summary>
        public class SetupParameter : IPartsSetupParameter
        {
            /// <summary> 育成回数 </summary>
            public int SingleModePlayingNum;
            /// <summary> グッドエンド達成回数 </summary>
            public int SingleModeGoodEndNum;
            /// <summary> 歴代最高評価点 </summary>
            public int SingleModeMaxRankScore;

            /// <summary> GⅠトロフィー数 </summary>
            public int TrophyG1Num;
            /// <summary> GⅡトロフィー数 </summary>
            public int TrophyG2Num;
            /// <summary> GⅢトロフィー数 </summary>
            public int TrophyG3Num;
            /// <summary> イベントトロフィー数 </summary>
            public int TrophyEventNum;

            /// <summary> チームのクラス </summary>
            public TeamStadiumDefine.ClassType TeamClass;
            /// <summary> チームのスコア </summary>
            public int TeamMaxScore;
            /// <summary> チームの勝利数 </summary>
            public int TeamWinNum;

            /// <summary> メインストーリー解放数 </summary>
            public int ReleaceMainStoryNum;
            /// <summary> ウマ娘ストーリー解放数 </summary>
            public int ReleaceCharacterStoryNum;
            /// <summary> 育成ウマ娘解放数 </summary>
            public int ReleaceTrainedCharacterNum;
            /// <summary> サポートカード解放数 </summary>
            public int ReleaceSupportCardNum;
            /// <summary> 楽曲解放数 </summary>
            public int ReleaceMusicNum;
            /// <summary> 演出解放数 </summary>
            public int ReleaceActNum;
            /// <summary> ギャラリー解放数 </summary>
            public int ReleaceGalleryNum;
            /// <summary> ボイス解放数 </summary>
            public int ReleaceVoiceNum;

            /// <summary> ファン総獲得数 </summary>
            public ulong FanSumNum;
            
            /// <summary> 育成ウマ娘イベント解放数 </summary>
            public int ReleaceSingleModeCharacterEventNum;
            /// <summary> 育成サポカイベント解放数 </summary>
            public int ReleaceSingleModeSupportCardEventNum;
            /// <summary> 育成メインシナリオイベント解放数 </summary>
            public int ReleaceSingleModeMainScenarioNum;
            /// <summary> ホーム会話の解放数（既読数） </summary>
            public int ReleaseHomeTalkNum;
        }

        private string TEAM_POINT_ZERO_TEXT = "-";

        #region SerializeField

        [Header("育成")]
        /// <summary> 育成回数 </summary>
        [SerializeField]
        private TextCommon _playingNumText = null;
        /// <summary> グッドエンド達成回数 </summary>
        [SerializeField]
        private TextCommon _goodEndText = null;
        /// <summary> 歴代最高評価点 </summary>
        [SerializeField]
        private TextCommon _maxRankScoreText = null;

        [Header("トロフィー")]
        /// <summary> GⅠ </summary>
        [SerializeField]
        private TextCommon _g1TrophyText = null;
        /// <summary> GⅡ </summary>
        [SerializeField]
        private TextCommon _g2TrophyText = null;
        /// <summary> GⅢ </summary>
        [SerializeField]
        private TextCommon _g3TrophyText = null;
        /// <summary> イベント </summary>
        [SerializeField]
        private TextCommon _eventTrophyText = null;

        [Header("チーム")]
        /// <summary> クラス </summary>
        [SerializeField]
        private TextCommon _classText = null;
        /// <summary> スコア </summary>
        [SerializeField]
        private TextCommon _teamScoreText = null;
        /// <summary> 勝利数 </summary>
        [SerializeField]
        private TextCommon _winNumText = null;

        [Header("解放数")]
        /// <summary> メインストーリー </summary>
        [SerializeField]
        private TextCommon _mainStoryText = null;
        /// <summary> ウマ娘ストーリー </summary>
        [SerializeField]
        private TextCommon _characterStoryText = null;
        /// <summary> 育成ウマ娘 </summary>
        [SerializeField]
        private TextCommon _trainedCharacterText = null;
        /// <summary> サポートカード </summary>
        [SerializeField]
        private TextCommon _supportCardText = null;
        /// <summary> 楽曲 </summary>
        [SerializeField]
        private TextCommon _musicText = null;
        /// <summary> 演出 </summary>
        [SerializeField]
        private TextCommon _actText = null;
        /// <summary> ギャラリー </summary>
        [SerializeField]
        private TextCommon _galleryText = null;
        /// <summary> ボイス </summary>
        [SerializeField]
        private TextCommon _voiceText = null;

        [Header("ファン")]
        /// <summary> 総獲得数 </summary>
        [SerializeField]
        private TextCommon _fanText = null;

        [Header("ギャラリー")]
        /// <summary> ギャラリートップ </summary>
        [SerializeField]
        private GameObject _rootGalleryObject = null;
        /// <summary> 育成ウマ娘 </summary>
        [SerializeField]
        private TextCommon _characterEventText = null;
        /// <summary> サポートカード </summary>
        [SerializeField]
        private TextCommon _supportCardEventText = null;
        /// <summary> メインシナリオ </summary>
        [SerializeField]
        private TextCommon _mainScenarioEventText = null;
        /// <summary> ホーム会話 </summary>
        [SerializeField]
        private TextCommon _homeTalkEventText = null;
        
        #endregion

        #region Method

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="param"></param>
        public void Setup(SetupParameter param)
        {
            // 育成
            _playingNumText.text = TextUtil.ToCommaSeparatedString(param.SingleModePlayingNum);
            _goodEndText.text = TextUtil.ToCommaSeparatedString(param.SingleModeGoodEndNum);
            _maxRankScoreText.text = TextUtil.ToCommaSeparatedString(param.SingleModeMaxRankScore);

            // トロフィー
            _g1TrophyText.text = TextUtil.ToCommaSeparatedString(param.TrophyG1Num);
            _g2TrophyText.text = TextUtil.ToCommaSeparatedString(param.TrophyG2Num);
            _g3TrophyText.text = TextUtil.ToCommaSeparatedString(param.TrophyG3Num);
            _eventTrophyText.text = TextUtil.ToCommaSeparatedString(param.TrophyEventNum);

            // チーム
            _classText.text = string.Format(TextId.Friend0058.Text(), ((int)param.TeamClass).ToString());
            _teamScoreText.text = string.Format(TextId.Friend0059.Text(), TextUtil.ToCommaSeparatedString(param.TeamMaxScore));
            if (param.TeamMaxScore <= 0)
            {
                // 未出走の時はハイフンを表示
                _teamScoreText.text = TextId.TeamStadium0020.Format(TEAM_POINT_ZERO_TEXT);
            }
            _winNumText.text = TextUtil.ToCommaSeparatedString(param.TeamWinNum);

            // 解放数
            _mainStoryText.text = TextUtil.ToCommaSeparatedString(param.ReleaceMainStoryNum);
            _characterStoryText.text = TextUtil.ToCommaSeparatedString(param.ReleaceCharacterStoryNum);
            _trainedCharacterText.text = TextUtil.ToCommaSeparatedString(param.ReleaceTrainedCharacterNum);
            _supportCardText.text = TextUtil.ToCommaSeparatedString(param.ReleaceSupportCardNum);
            _musicText.text = TextUtil.ToCommaSeparatedString(param.ReleaceMusicNum);
            _actText.text = TextUtil.ToCommaSeparatedString(param.ReleaceActNum);

            _galleryText.transform.parent.gameObject.SetActiveWithCheck(true);
            _galleryText.text = TextUtil.ToCommaSeparatedString(param.ReleaceGalleryNum);

            _voiceText.text = TextUtil.ToCommaSeparatedString(param.ReleaceVoiceNum);

            // ファン
            TextUtil.SetFanNum(_fanText, param.FanSumNum, smallSize:TextFormat.FontSize.Size_24, toSmallFanLength:14, overExpectionSetting:true, separateComma:true);

            //ギャラリー
            _rootGalleryObject.SetActive(true);
            _characterEventText.text = TextUtil.ToCommaSeparatedString(param.ReleaceSingleModeCharacterEventNum);
            _supportCardEventText.text = TextUtil.ToCommaSeparatedString(param.ReleaceSingleModeSupportCardEventNum);
            _mainScenarioEventText.text = TextUtil.ToCommaSeparatedString(param.ReleaceSingleModeMainScenarioNum);
            _homeTalkEventText.text = param.ReleaseHomeTalkNum.ToCommaSeparatedString();
        }

        #endregion
    }
}
