using System;
using System.Collections.Generic;
using System.Linq;
using System.Collections;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 【CM見返し機能】「CMムービー削除確認」ダイアログ
    /// </summary>
    public class DialogEpisodeExtraCommercialDeleteConfirm : DialogInnerBase
    {
        private static readonly string DIALOG_PREFAB_PATH = "UI/Parts/Episode/Extra/DialogEpisodeExtraCommercialDeleteConfirm";


        [Header("上部UI")]

        /// <summary>確認文</summary>
        [SerializeField] private TextCommon _confirmText;

        /// <summary>ウマ娘アイコン（のクローン元）</summary>
        [SerializeField] private CharacterButton _characterButtonOrigin;

        /// <summary>ウマ娘アイコンを配置するルートオブジェクト</summary>
        [SerializeField] private Transform _characterButtonRoot1To2;        // アイコンが1個～2個の時用
        [SerializeField] private Transform _characterButtonRoot3To5;        // アイコンが3個～5個の時用
        [SerializeField] private Transform _characterButtonRoot6To10Upper;  // アイコンが6個～10個の時用（上）
        [SerializeField] private Transform _characterButtonRoot6To10Lower;  // アイコンが6個～10個の時用（下）

        [Header("下部UI")]

        /// <summary>チェックボックス</summary>
        [SerializeField] private ToggleCommon _checkBox;

        /// <summary>警告文</summary>
        [SerializeField] private TextCommon _warningText;


        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Story4080018.Text();
            data.RightButtonText = TextId.Common0023.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.AutoClose = false; // 決定ボタンを押した時に通信待ちをするので、自動クローズ機能はOFFにしておく
            return data;
        }


        private List<WorkExtraCommercialData.CmInfo> _workCmInfoList = null;
        private Action _onClickRightButton = null;
        private WorkExtraCommercialData WorkExtraCommercialData => WorkDataManager.Instance.ExtraCommercialData;


        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(List<WorkExtraCommercialData.CmInfo> workCmInfoList, Action onClickRightButton)
        {
            var instance = LoadAndInstantiatePrefab<DialogEpisodeExtraCommercialDeleteConfirm>(DIALOG_PREFAB_PATH);

            var dialogData = instance.CreateDialogData();
            dialogData.AddLeftButtonCallback(instance.OnClickLeftButton);
            dialogData.AddRightButtonCallback(instance.OnClickRightButton);
            DialogManager.PushDialog(dialogData);

            instance.Setup(workCmInfoList, onClickRightButton);
        }

        /// <summary>セットアップ</summary>
        public void Setup(List<WorkExtraCommercialData.CmInfo> workCmInfoList, Action onClickRightButton)
        {
            _workCmInfoList = workCmInfoList;
            _onClickRightButton = onClickRightButton;

            // 確認文
            SetupConfirmText();

            // ウマ娘アイコン
            SetupCharacterButtons();

            // チェックボックス
            SetupCheckBox();

            // 警告文
            SetupWaningText();
        }

        /// <summary>確認文のセットアップ</summary>
        private void SetupConfirmText()
        {
            const float POS_Y_1_TO_5 = 337.5f;
            const float POS_Y_6_TO_10 = 372.5f;

            // ウマ娘アイコンの数によって高さ位置が変化する
            float posY = (_workCmInfoList.Count >= 6) ? POS_Y_6_TO_10 : POS_Y_1_TO_5;
            var pos = UIUtil.GetAnchoredPosition(_confirmText.transform);
            pos = new Vector2(pos.x, posY);
            UIUtil.SetAnchoredPosition(_confirmText.transform, pos);
        }

        /// <summary>ウマ娘アイコン一覧のセットアップ</summary>
        private void SetupCharacterButtons()
        {
            // キャラクターボタン情報一覧を作成
            var characterButtonInfoList = new List<CharacterButtonInfo>();
            foreach (var workCmInfoList in _workCmInfoList)
            {
                var workTrainedCharaData = workCmInfoList.WorkTrainedCharaData;
                if (workTrainedCharaData == null)
                    continue;

                characterButtonInfoList.Add(new CharacterButtonInfo
                {
                    Id = workTrainedCharaData.CardId,
                    IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                    TrainedChara = workTrainedCharaData,
                    RankImage = true,
                    Rarity = workTrainedCharaData.Rarity,
                    EnableRarity = false,
                    IconSizeType = IconBase.SizeType.Chara_72,
                    SortInfoType = CharacterButtonInfo.SortInfo.Memo,
                    EnableButton = false,
                });
            }

            // キャラクターボタンを生成
            _characterButtonOrigin.SetActiveWithCheck(true);
            for (int i = 0; i < characterButtonInfoList.Count; i++) 
            {
                var charaButtonInfo = characterButtonInfoList[i];
                var charaButtonRoot = GetCharacterButtonRoot(i, characterButtonInfoList.Count);
                var charaButton = Instantiate(_characterButtonOrigin, charaButtonRoot);
                charaButton.Setup(charaButtonInfo);
                OnUpdateCharacterButton(charaButton);
            }
            _characterButtonOrigin.SetActiveWithCheck(false);
        }

        /// <summary>ウマ娘アイコン1つの更新処理</summary>
        private void OnUpdateCharacterButton(CharacterButton characterButton)
        {
            var workTrainedChara = characterButton.Info.TrainedChara;
            var workCmInfo = WorkExtraCommercialData.GetCmInfo(workTrainedChara.Id);
            if (workCmInfo == null)
                return;

            // CMの路線名（"クラシック三冠"、"ティアラ"など）を表示
            {
                var routeName = EpisodeExtraCommercialViewController.GetRouteName((SingleModeScenarioLegendDefine.CmRoute)workCmInfo.MasterSingleMode10CmDetail.RouteId);

                // メモ用オブジェクトを使い回して表示する
                characterButton.ForceShowMemoObj();
                characterButton.SetupMemo(routeName);
            }
        }

        /// <summary>キャラクターボタンを配置するルートオブジェクトを決定</summary>
        private Transform GetCharacterButtonRoot(int index, int characterButtonCount)
        {
            // ウマ娘アイコンの数によって配置位置が変化する
            if (characterButtonCount >= 6)
            {
                // アイコンが6個以上なら2行表示にする
                int halfCharacterButtonCountCeil = Mathf.CeilToInt((float)characterButtonCount / 2f); // アイコンの数が奇数の場合、1行目に多く並べる。（例えばアイコンが7個なら1行目に4個、2行目に3個並べる）。そのために小数点以下を切り上げる。
                bool isUpper = (index < halfCharacterButtonCountCeil);
                return isUpper ? _characterButtonRoot6To10Upper : _characterButtonRoot6To10Lower;
            }
            else if (characterButtonCount >= 3)
            {
                // アイコンが3個～5個なら1行で表示し、スペースを詰める
                return _characterButtonRoot3To5;
            }

            // アイコンが1個～2個なら1行で表示し、スペースに余裕を持たせる
            return _characterButtonRoot1To2;
        }

        /// <summary>チェックボックスのセットアップ</summary>
        private void SetupCheckBox()
        {
            _checkBox.SetCallback((isOn) => { OnChangeCheckBox(isOn); });

            // はじめはチェック無し
            OnChangeCheckBox(false);
        }

        /// <summary>警告文のセットアップ</summary>
        private void SetupWaningText()
        {
            TextUtil.SetWarningTextWithIcon(_warningText, TextId.Story4080021.Text(), addSpaceNum: 0);
        }

        /// <summary>チェックボックスを切り替えた時の処理</summary>
        private void OnChangeCheckBox(bool isOn)
        {
            // 決定ボタンを押せるかどうかを切り替え
            RefreshRightButtonInteractable(isOn);
        }

        /// <summary>決定ボタンの押せる/押せないを更新</summary>
        private void RefreshRightButtonInteractable(bool interactable)
        {
            var dialog = (GetDialog() as DialogCommon);
            var rightButton = dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);

            rightButton.SetInteractable(interactable);

            string notificationMessage = string.Empty;
            if (!interactable)
            {
                notificationMessage = TextUtil.Format(TextId.TransferEvent0029.Text(), TextId.Story4080020.Text());
            }
            rightButton.SetNotificationMessage(notificationMessage);
        }

        /// <summary>
        /// 「決定」ボタンを押した時の処理
        /// </summary>
        public void OnClickRightButton(DialogCommon dialog)
        {
            _onClickRightButton?.Invoke();

            // 連打防止
            _onClickRightButton = null;
        }

        /// <summary>
        /// 「トップへ」ボタンを押した時の処理
        /// </summary>
        public void OnClickLeftButton(DialogCommon dialog)
        {
            DialogManager.RemoveAllDialog();
        }
    }
}