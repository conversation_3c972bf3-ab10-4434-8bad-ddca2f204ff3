using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// チーム作りイベント：図鑑：組み合わせ詳細ダイアログ
    /// </summary>
    public class DialogTeamBuildingCollectionSetDetail : DialogInnerBase
    {
        #region SerializeField

        [SerializeField]
        private PartsSingleModeSkillListItem _skillItem;
        [SerializeField]
        private GameObject _registeredLabel;
        [SerializeField]
        private TextCommon _descText;
        [SerializeField]
        private CharacterButton _charaButtonBase;
        [SerializeField]
        private GameObject _organizedLabelBase; // 「編成中」ラベル

        #endregion

        #region Member, Property

        private bool _isRegistedSkill = false;

        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(int collectionSetId)
        {
            var component = LoadAndInstantiatePrefab<DialogTeamBuildingCollectionSetDetail>(ResourcePath.DIALOG_TEAM_BUILDING_COLLECTION_SET_DETAIL);

            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.TeamBuilding400117.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            component.Setup(collectionSetId);
            DialogManager.PushDialog(dialogData);
        }

        private void Setup(int collectionSetId)
        {
            var masterCollectionSetData = MasterDataManager.Instance.masterTeamBuildingCollectionSet.Get(collectionSetId);
            if (masterCollectionSetData == null)
            {
                return;
            }
            
            // スキル
            var skillInfo = new PartsSingleModeSkillListItem.Info(masterCollectionSetData.SkillId);
            _skillItem.UpdateItem(skillInfo, false);

            // 習得済み
            _isRegistedSkill = WorkDataManager.Instance.TeamBuildingData.IsRegisteredCollectionCharaGroup(masterCollectionSetData.CharaGroupId);
            _registeredLabel.SetActiveWithCheck(_isRegistedSkill);
            
            // 説明文
            _descText.text = TextUtil.GetMasterText(MasterString.Category.TeamBuildingCollectionSetDescription, collectionSetId);

            // キャラ一覧
            var groupDataList = MasterDataManager.Instance.masterTeamBuildingCharaGroup.GetListWithCharaGroupIdOrderByCharaIdAsc(masterCollectionSetData.CharaGroupId);
            SetupCharaList(groupDataList.Select(data => (data.CharaId, WorkDataManager.Instance.TeamBuildingData.IsRegisteredCollectionChara(data.CharaId))).ToList());
        }

        private void SetupCharaList(List<(int CharaId, bool IsRegistered)> charaDataList)
        {
            UIUtil.CreateScrollItem(_charaButtonBase, charaDataList.Count, null, (charaButton, i) =>
            {
                var charaData = charaDataList[i];

                // キャラアイコンのセットアップ
                PartsTeamBuildingCollectionSkillGroupListItem.SetupCharaIcon(charaButton, charaData.CharaId, _isRegistedSkill, _organizedLabelBase);
            });
        }
    }

}
