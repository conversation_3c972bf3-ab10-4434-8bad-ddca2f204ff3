using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;

namespace Gallop
{
    using EventButtonDefine;
    using Gallop.Tutorial;

    /// <summary>
    /// チーム作りイベント：レース画面1.5階層の「レースイベント」画面の右下に表示するミニボタン
    /// </summary>
    public class PartsTeamBuildingButtonMiniEntity : AbstractMiniEventButtonEntity
    {
        /// <summary>「出走中」バッジ</summary>
        [SerializeField]
        private GameObject _resumeBadge;

        /// <summary> 「出走中」バッジの文言テキスト</summary>
        [SerializeField]
        private TextCommon _resumeBadgeText;

        /// <summary>「！」バッジの親</summary>
        [SerializeField]
        private Transform _exclamationBadgeParent;

        /// <summary>イベント開始日時</summary>
        public override long StartDate
        {
            get
            {
                var master = WorkDataManager.Instance.TeamBuildingData.MasterTeamBuildingData;
                if (master == null) return -1;

                return master.StartDate;
            }
        }

        /// <summary>開催期間中か（報酬受取期間もtrue）</summary>
        public override bool IsOpen => WorkDataManager.Instance.TeamBuildingData.IsOpen();

        /// <summary>このミニボタンの表示に必要なリソースが揃っているか</summary>
        public override bool IsReadyUiUpdate => PartsTeamBuildingButton.IsReadyUiUpdate();

        /// <summary>「！」バッジが必要か</summary>
        public override bool NeedsBadge => PartsTeamBuildingButton.IsNeedExclamationBadge();

        /// <summary>表示優先度が高いか</summary>
        public static bool IsHighPriority() => false;

        /// <summary>
        /// 期間内であれば自身を生成するクラスを作成する
        /// </summary>
        public static EventMiniButtonCreator CreateCreatorIfNeeded(PartsEventButtonMini.SingleModeParam param)
        {
            if (TutorialManager.IsTutorialExecuting())
                return null; // チュートリアル中は表示しない

            int teamBuildingEventId = WorkDataManager.Instance.TeamBuildingData.TeamBuildingEventId;
            if (teamBuildingEventId <= 0)
                return null; // 開催期間外、または直近での開催予定が無い

            var masterTeamBuildingData = MasterDataManager.Instance.masterTeamBuildingData.Get(teamBuildingEventId);
            if (masterTeamBuildingData == null)
                return null;

            if (!WorkDataManager.Instance.TeamBuildingData.IsOpen())
                return null; // 開催期間外

            if (!PartsTeamBuildingButton.IsReadyUiUpdate())
                return null; // リソースが無い場合は表示しない

            return new EventMiniButtonCreator(ButtonType.TeamBuilding, masterTeamBuildingData.StartDate, masterTeamBuildingData.EndingDate, param, parent => Create(parent));
        }

        /// <summary>
        /// アイコンを生成する
        /// </summary>
        public static PartsTeamBuildingButtonMiniEntity Create(Transform parent) =>
            GameObject.Instantiate<GameObject>(ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_TEAM_BUILDING_BUTTON_MINI), parent).GetComponent<PartsTeamBuildingButtonMiniEntity>();

        /// <summary>
        /// ミニボタン押下時処理
        /// </summary>
        public override void OnClicked()
        {
            // イベント期間外ならエラーダイアログを出す
            if (!WorkDataManager.Instance.TeamBuildingData.IsOpen())
            {
                PartsTeamBuildingButton.OpenErrorDialogNotInTerm();
                return;
            }

            // 遷移先で「戻る」を押したらここへ戻るようにしておく
            {
                SceneManager.Instance.ClearBackStack();

                var backableStateInfo = new BackableStateInfo(SceneDefine.ViewId.HomeHub,
                    new HomeHubViewController.HomeHubViewInfo()
                    {
                        DefaultViewId = SceneDefine.ViewId.Home,
                        HomeViewInfo = new HomeViewInfo(HomeTopState.Race, (int) RaceHomeTopUI.DisplayType.Event)
                    });
                SceneManager.Instance.StackViewForBack(backableStateInfo);
            }

            // イベントへ遷移
            TeamBuildingUtil.GoToTeamBuildingView();
        }

        /// <summary>
        /// 「！」バッジを生成
        /// </summary>
        public override BadgeCommon CreateBadge(Transform parent)
        {
            return BadgeCommon.CreateBadge(_exclamationBadgeParent);
        }

        /// <summary>
        /// 「出走中」バッジと「！」バッジを更新
        /// </summary>
        public override BadgeCommon UpdateBadge(BadgeCommon badge, Transform parent)
        {
            // 「出走中」バッジの更新
            {
                bool needBadge = PartsTeamBuildingButton.IsNeedResumeBadge();

                _resumeBadge.SetActiveWithCheck(needBadge);
                if (needBadge)
                {
                    _resumeBadgeText.text = PartsTeamBuildingButton.GetResumeBadgeType().Label();
                }
            }

            // 「！」バッジの更新
            {
                if (NeedsBadge)
                {
                    if (badge == null)
                    {
                        badge = CreateBadge(parent);
                    }
                }

                if (badge != null)
                {
                    badge.SetActiveWithCheck(NeedsBadge);
                }
            }

            return badge;
        }
    }
}