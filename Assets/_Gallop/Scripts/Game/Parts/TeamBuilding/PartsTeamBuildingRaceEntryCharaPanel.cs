using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// チーム作りイベント：レースにエントリー済みのキャラのパネル
    /// </summary>
    public class PartsTeamBuildingRaceEntryCharaPanel : MonoBehaviour
    {
        /// <summary>キャラアイコン</summary>
        [SerializeField] private CharacterButton _charaButton;

        /// <summary>走法アイコン</summary>
        [SerializeField] private ImageCommon _runStyle;

        /// <summary>二つ名リボン</summary>
        [SerializeField] private PartsNickNameRibbon _nickName;

        /// <summary>キャラ名</summary>
        [SerializeField] private TextCommon _charaName;

        /// <summary>チーム名</summary>
        [SerializeField] private TextCommon _teamName;

        /// <summary>必要スカウトPt欄のルート</summary>
        [SerializeField] private GameObject _needScoutPtRoot;
        /// <summary>必要スカウトPtの値</summary>
        [SerializeField] private TextCommon _needScoutPtValueText;

        /// <summary>バ場適正</summary>
        [SerializeField] private TextCommon _ground;
        [SerializeField] private ImageCommon _groundRank;

        /// <summary>距離適正</summary>
        [SerializeField] private TextCommon _distance;
        [SerializeField] private ImageCommon _distanceRank;

        [SerializeField] private GameObject _raceInfoRoot;

        /// <summary>名簿登録状況ラベル</summary>
        [SerializeField] private GameObject _collectionStatusLabel;


        /// <summary>殿堂入りウマ娘データ（このキャラが殿堂入りウマ娘の場合に値が入り、モブウマ娘の場合は null が入ります）</summary>
        private WorkTrainedCharaData.TrainedCharaData _trainedCharaData = null;
        /// <summary>モブウマ娘データ（このキャラがモブウマ娘の場合に値が入り、殿堂入りウマ娘の場合は null が入ります）</summary>
        private EntryMobChara _entryMobChara = null;

        /// <summary>
        /// 殿堂入りウマ娘用セットアップ
        /// </summary>
        public void Setup(WorkTrainedCharaData.TrainedCharaData trainedCharaData, string teamName, bool isOpponentTeam, bool isShowNeedScoutPt, MasterRaceCourseSet.RaceCourseSet raceCourseSet, ResourceManager.ResourceHash hash)
        {
            _trainedCharaData = trainedCharaData;
            _entryMobChara = null;
            if (trainedCharaData == null)
                return;

            // キャラアイコン
            var charaButtonInfo = CharacterButtonInfo.CreateTrained(trainedCharaData, dispRank: true, dispLongTapDetail: true);
            charaButtonInfo.EnableRankScore = true;
            charaButtonInfo.RankScore = trainedCharaData.RankScore;
            charaButtonInfo.OnLongTap = (_) =>
            {
                TeamBuildingUtil.OpenTrainedCharaDetailDialog(_trainedCharaData, onClose: RefreshTrainedCharaNickName);
            };
            _charaButton.LoadTextureFastFunc = (path) => ResourceManager.LoadOnHash<Texture2D>(path, hash); // VSカットへの遷移中にテクスチャが破棄されないようにしておく
            _charaButton.Setup(charaButtonInfo);

            // 走法アイコン
            _runStyle.sprite = GallopUtil.GetCommonRunningStyleIconSprite((int)trainedCharaData.RunningStyle);

            // キャラ名
            _charaName.text = trainedCharaData.Name;

            // 二つ名リボン
            RefreshTrainedCharaNickName();

            // チーム名
            SetupTeamName(teamName, isOpponentTeam);

            // 「必要スカウトPt」欄
            SetupNeedScoutPt(trainedCharaData.Rank, isShowNeedScoutPt);

            // バ場適正、距離適正
            if (raceCourseSet != null)
            {
                _raceInfoRoot.SetActiveWithCheck(true);

                // バ場適性
                var groundType = (RaceDefine.GroundType)raceCourseSet.Ground;
                _ground.text = groundType.Text();
                _groundRank.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)trainedCharaData.GetProperGround(groundType));

                // 距離適性
                var distanceType = StandaloneSimulator.RaceUtil.CalcDistanceType(raceCourseSet.Distance);
                _distance.text = distanceType.Text();
                _distanceRank.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)trainedCharaData.GetProperDistance(distanceType));
            }
            else
            {
                _raceInfoRoot.SetActiveWithCheck(false);
            }

            // 名簿登録状況
            // 登録済み表記はスカウトするかの指針なので、必要なスカウトPtを表示しない(スカウトする対象ではない)なら表示しない
            _collectionStatusLabel.SetActiveWithCheck(isShowNeedScoutPt && TeamBuildingUtil.IsCollectionRegisterd(trainedCharaData.CharaId));
        }

        /// <summary>
        /// モブウマ娘用セットアップ
        /// </summary>
        public void Setup(EntryMobChara entryMobChara, int charaId, string teamName, bool isOpponentTeam, 
            bool isShowNeedScoutPt, MasterRaceCourseSet.RaceCourseSet raceCourseSet, ResourceManager.ResourceHash hash, TeamBuildingSpecialBuff specialBuff = null)
        {
            _entryMobChara = entryMobChara;
            _trainedCharaData = null;
            if (entryMobChara == null)
                return;

            var masterTeamBuildingRaceNpc = MasterDataManager.Instance.masterTeamBuildingRaceNpc.Get(entryMobChara.npc_id);
            if (masterTeamBuildingRaceNpc == null)
                return;

            // キャラアイコン
            var charaButtonInfo = TeamBuildingUtil.CreateNpcButtonInfo(masterTeamBuildingRaceNpc, (GameDefine.FinalTrainingRank)entryMobChara.rank, specialBuff: specialBuff);
            _charaButton.LoadTextureFastFunc = (path) => ResourceManager.LoadOnHash<Texture2D>(path, hash); // VSカットへの遷移中にテクスチャが破棄されないようにしておく
            _charaButton.Setup(charaButtonInfo);

            // 走法アイコン
            _runStyle.sprite = GallopUtil.GetCommonRunningStyleIconSprite(entryMobChara.running_style);

            // キャラ名
            _charaName.text = masterTeamBuildingRaceNpc.GetName();

            // 二つ名リボン（モブウマ娘は非表示）
            _nickName.SetActiveWithCheck(false);

            // チーム名
            SetupTeamName(teamName, isOpponentTeam);

            // 「必要スカウトPt」欄
            SetupNeedScoutPt((GameDefine.FinalTrainingRank)entryMobChara.rank, isShowNeedScoutPt);

            // バ場適正、距離適正
            if (raceCourseSet != null)
            {
                _raceInfoRoot.SetActiveWithCheck(true);

                // バ場適性
                var groundType = (RaceDefine.GroundType)raceCourseSet.Ground;
                _ground.text = groundType.Text();
                _groundRank.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)masterTeamBuildingRaceNpc.GetProperGround(groundType));

                // 距離適性
                var distanceType = StandaloneSimulator.RaceUtil.CalcDistanceType(raceCourseSet.Distance);
                _distance.text = distanceType.Text();
                _distanceRank.sprite = SingleModeDefine.GetProperGradeSprite((RaceDefine.ProperGrade)masterTeamBuildingRaceNpc.GetProperDistance(distanceType));
            }
            else
            {
                _raceInfoRoot.SetActiveWithCheck(false);
            }

            // 名簿登録状況
            // 登録済み表記はスカウトするかの指針なので、必要なスカウトPtを表示しない(スカウトする対象ではない)なら表示しない
            _collectionStatusLabel.SetActiveWithCheck(isShowNeedScoutPt && TeamBuildingUtil.IsCollectionRegisterd(charaId));
        }

        /// <summary>
        /// チーム名を設定
        /// </summary>
        private void SetupTeamName(string teamName, bool isOpponentTeam)
        {
            _teamName.text = teamName;
            if (isOpponentTeam)
            {
                _teamName.FontColor = FontColorType.SingleModeTeamRaceOpponentTeamName;
            }
            else
            {
                _teamName.FontColor = FontColorType.SingleModeTeamRacePlayerTeamName;
            }
        }

        /// <summary>
        /// 二つ名リボンを殿堂入りウマ娘向けに更新
        /// </summary>
        private void RefreshTrainedCharaNickName()
        {
            if (_trainedCharaData == null)
                return;

            _nickName.SetActiveWithCheck(true);
            _nickName.Initialize(_trainedCharaData.MasterNickname);
        }

        /// <summary>
        /// 「必要スカウトPt」欄のセットアップ
        /// </summary>
        private void SetupNeedScoutPt(GameDefine.FinalTrainingRank charaRank, bool isOpponentTeam)
        {
            bool isActive = isOpponentTeam;
            _needScoutPtRoot.SetActiveWithCheck(isActive);
            if (isActive)
            {
                var needScoutPt = TeamBuildingUtil.GetNeedScoutPt(charaRank);
                _needScoutPtValueText.text = TextId.Friend0059.Format(TextUtil.ToCommaSeparatedString(needScoutPt));
            }
        }
    }
}
