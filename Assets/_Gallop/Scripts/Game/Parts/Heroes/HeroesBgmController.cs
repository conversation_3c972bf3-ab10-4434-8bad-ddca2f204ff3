using System;
using UnityEngine;
using static Gallop.StaticVariableDefine.Heroes.HeroesBgmController;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズ：BGMコントローラー
    /// </summary>
    public class HeroesBgmController
    {
        private WorkHeroesData _workHeroesData => WorkDataManager.Instance.HeroesData;


        /// <summary>
        /// リソースダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            var audioManager = AudioManager.Instance;

            // 定義されているBGM
            foreach (var audioId in BGM_AUDIO_ID_ARRAY)
            {
                var audioIdData = AudioDefine.GetAudioIdData(audioId);
                audioManager.RegisterDownloadByCueSheet(register, audioIdData._cueSheet, AudioManager.SubFolder.Bgm);
            }

            // CSVに登録されているBGM
            var masterHeroesLeagueRankList = MasterHeroesLeagueRank.GetListOrderByLeagueRankAsc();
            foreach (var masterHeroesLeagueRank in masterHeroesLeagueRankList)
            {
                var topBgmCueSheetName = masterHeroesLeagueRank.TopBgmCuesheetName;
                audioManager.RegisterDownloadByCueSheet(register, topBgmCueSheetName, AudioManager.SubFolder.Bgm);

                var racingBaseBgmCueSheetName = masterHeroesLeagueRank.RacingBaseBgmCuesheetName;
                audioManager.RegisterDownloadByCueSheet(register, racingBaseBgmCueSheetName, AudioManager.SubFolder.Bgm);
            }
        }

        /// <summary>
        /// 実体取得
        /// </summary>
        public static HeroesBgmController GetInstance()
        {
            var heroesHubViewController = SceneManager.Instance.GetCurrentViewController() as HeroesHubViewController;
            if (heroesHubViewController == null)
                return null;

            return heroesHubViewController.BgmController;
        }

        /// <summary>
        /// オープニング画面のBGMを取得
        /// </summary>
        public AudioId GetOpeningViewBgmAudioId()
        {
            // 現在のステージとステップによってBGMが変化する
            var activeStage = _workHeroesData.Stage;
            var activeStageStep = _workHeroesData.StageStep;

            // Finalステージウマいね！受付期間以降
            if (activeStage == MasterHeroesStageSchedule.Stage.Final &&
                activeStageStep >= (int)MasterHeroesStageSchedule.FinalStageStep.Umaine)
            {
                return AudioId.BGM_HEROES_TOP_03_OP_VER;
            }
            // チーム編成期間～Finalステージマッチング期間まで
            else
            {
                return AudioId.BGM_HEROES_TOP_01_OP_VER;
            }
        }

        /// <summary>
        /// レース拠点画面のBGMを取得
        /// </summary>
        public AudioId GetStage1RacingBaseViewBgmAudioId()
        {
            // 現在のリーグランクによってBGMが変化する
            var myTeamInfo = _workHeroesData.MyTeamInfo;
            var masterHeroesLeagueRank = myTeamInfo.GetMasterHeroesLeagueRank();
            if (masterHeroesLeagueRank == null)
                return AudioId.INVALID; // ここには来ないはず

            var cueSheetName = masterHeroesLeagueRank.RacingBaseBgmCuesheetName;
            var cueName = masterHeroesLeagueRank.RacingBaseBgmCueName;
            return AudioDefine.GetAudioId(cueSheetName, cueName);
        }

        /// <summary>
        /// 1stステージ/グランドリザルト画面のBGMを取得
        /// </summary>
        public AudioId GetStage1GrandResultViewBgmAudioId()
        {
            return AudioId.BGM_HEROES_STAGE1_GRAND_RESULT;
        }

        /// <summary>
        /// 上記以外の画面のBGMを取得
        /// </summary>
        public AudioId GetDefaultViewBgmAudioId()
        {
            // オープニングのBGMが再生中の場合はそれを流し続ける
            {
                var opBgmAudioId = AudioId.BGM_HEROES_TOP_01_OP_VER;
                if (AudioManager.Instance.IsPlayBgm(opBgmAudioId))
                {
                    return opBgmAudioId;
                }
            }
            {
                var opBgmAudioId = AudioId.BGM_HEROES_TOP_03_OP_VER;
                if (AudioManager.Instance.IsPlayBgm(opBgmAudioId))
                {
                    return opBgmAudioId;
                }
            }

            // 現在のステージとステップによってBGMが変化する
            var activeStage = _workHeroesData.Stage;
            var activeStageStep = _workHeroesData.StageStep;

            // Finalステージウマいね！受付期間以降
            if (activeStage == MasterHeroesStageSchedule.Stage.Final &&
                activeStageStep >= (int)MasterHeroesStageSchedule.FinalStageStep.Umaine)
            {
                return AudioId.BGM_HEROES_TOP_03;
            }
            // チーム編成期間～Finalステージマッチング期間まで
            else
            {
                // 現在のリーグランクによってBGMが変化する
                var myTeamInfo = _workHeroesData.MyTeamInfo;
                var masterHeroesLeagueRank = myTeamInfo.GetMasterHeroesLeagueRank();
                if (masterHeroesLeagueRank == null)
                    return AudioId.INVALID; // ここには来ないはず
                var cueSheetName = masterHeroesLeagueRank.TopBgmCuesheetName;
                var cueName = masterHeroesLeagueRank.TopBgmCueName;
                return AudioDefine.GetAudioId(cueSheetName, cueName);
            }
        }
    }
}