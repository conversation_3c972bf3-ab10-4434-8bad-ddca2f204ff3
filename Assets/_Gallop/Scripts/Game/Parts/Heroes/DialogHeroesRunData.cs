using System;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズ：出走データダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogHeroesRunData : DialogInnerBase
    {
        /// <summary>
        /// タブタイプ
        /// </summary>
        private enum TabType
        {
            RunHistory = 0,        // 出走ウマ娘履歴
            LeagueScore = 1,       // 獲得リーグスコア
        }
        
        /// <summary> 各タブページ </summary>
        [SerializeField] private GameObject[] _tabContent = null;
        [SerializeField] private FlickToggleGroupCommon _tab = null;
        [SerializeField] private FlickableObject _flickableObject = null;

        // 選択したタブ
        private TabType _selectedTab = TabType.RunHistory;
        
        /// <summary>「リーグオブヒーローズ出走データのソート/絞り込み設定」の保存用タグ</summary>
        private const SortFilterSetting.SaveTag SORT_FILTER_SETTING_SAVE_TAG_ENTRY_RECORD = SortFilterSetting.SaveTag.HeroesEntryRecord;
        
        [Header("出走ウマ娘履歴タブ")]
        
        /// <summary> ウマ娘アイコンリスト </summary>
        [SerializeField] private PartsCardListVertical _cardListVertical = null;
        
        // <summary> 出走済みウマ娘がいないときのテキスト </summary>
        [SerializeField] private GameObject _noCharaTextObj;
        
        [Header("獲得リーグスコアタブ")]
        
        // <summary> たずなさんコメント（Stage毎に出し分け） </summary>
        [SerializeField] private TextCommon _tazunaComment;
        
        // <summary> 称号詳細ボタン </summary>
        [SerializeField] private ButtonCommon _honorDetailButton;
        
        // <summary> 称号獲得条件1 </summary>
        [SerializeField] private TextCommon _getHonorCondition1Text;
        
        // <summary> 称号獲得条件2 </summary>
        [SerializeField] private TextCommon _getHonorCondition2Text;
        
        // <summary> リスト </summary>
        [SerializeField] private LoopScroll _loopScroll;
        
        /// <summary>
        /// リストデータ
        /// イベント詳細ダイアログを開いてからの初回遷移時に通信して更新、以降はキャッシュした値を用いる（イベント詳細ダイアログを閉じるとリセット）
        /// </summary>
        private static List<PartsHeroesHonorCharacterListItem.PartsHeroesHonorCharacterListItemModel> _modelList = new List<PartsHeroesHonorCharacterListItem.PartsHeroesHonorCharacterListItemModel>();

        /// <summary> 閉じるボタンコールバック </summary>
        private Action _onCompleteClose = null;
        
        /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;


        /// <summary>
        /// ダイアログの表示
        /// </summary>
        public static void Push(bool isUpdated = false, Action onComplete = null)
        {
            // 一度通信済みなら、通信しない
            if (isUpdated) 
            {
                // ダイアログ表示
                PushInternal(null, onComplete);
                return;
            }
               
            // 称号ウマ娘選択のリスト情報を取得する
            var req = new HeroesStage1HonorSelectCharaListRequest();
            req.Send(res =>
            {
                if (res == null)
                    return;

                // ワークの更新はしない

                // ダイアログ表示
                PushInternal(res.data, onComplete);
            });
        }

        /// <summary>
        /// ダイアログ表示
        /// </summary>
        /// <returns></returns>
        public static DialogHeroesRunData PushInternal(HeroesStage1HonorSelectCharaListResponse.CommonResponse serverData, Action onComplete = null)
        {
            var content = LoadAndInstantiatePrefab<DialogHeroesRunData>(ResourcePath.DIALOG_HEROES_RUN_DATA);
            var data = content.CreateDialogData();
            data.DestroyCallBack = () => onComplete?.Invoke();
            var dialog = DialogManager.PushDialog(data);

            content.Initialize(dialog, serverData);
            return content;
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Heroes408008.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.CenterButtonCallBack = OnCenterClick;

            return dialogData;
        }

        /// <summary>
        /// 閉じるボタン押下時のコールバック
        /// </summary>
        /// <param name="dialog"></param>
        private void OnCenterClick(DialogCommon dialog)
        {
            dialog.Close(() => _onCompleteClose?.Invoke());
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Initialize(DialogCommon dialog, HeroesStage1HonorSelectCharaListResponse.CommonResponse serverData)
        {
            // キャラ一覧を作成（出走ウマ娘履歴）
            SetupRunDataCharaList();
            // キャラ一覧を作成（獲得リーグスコア）
            SetupLeagueScoreCharaList(serverData);
            // タブ初期化
            InitTab();
            // 表示情報の更新
            SetTab(_selectedTab);
        }

        /// <summary>
        /// タブ初期化
        /// </summary>
        private void InitTab()
        {
            _tab.Initialize((int)_selectedTab); // 0で初期化と同義
            _tab.SetOnSelectCallback(i => SetTab((TabType)i));
            _flickableObject.SetFlickCallback(_tab.OnFlick);
        }
        
        /// <summary>
        /// タブを変更
        /// </summary>
        /// <param name="tabType"></param>
        private void SetTab(TabType tabType)
        {
            _selectedTab = tabType;

            // 見た目の変更
            for (int i = 0; i < _tabContent.Length; i++)
            {
                _tabContent[i].SetActiveWithCheck(i == (int)_selectedTab);
            }
        }
        
        /// <summary>
        /// 「出走ウマ娘履歴」タブを作成（1ページ目）
        /// </summary>
        private void SetupRunDataCharaList()
        {
            // 表示するウマ娘の CharacterButtonInfoリストを作成
            var characterButtonInfoList = new List<CharacterButtonInfo>();
            {
                // 所持しているウマ娘の一覧を取得
                var heroesRankCountDataDic = WorkDataManager.Instance.HeroesData.TrainedCharaRankCountData;

                foreach (var dataPair in heroesRankCountDataDic)
                {
                    var trainedId = dataPair.Key;
                    var trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(trainedId);
                    if (trainedChara == null) continue;     // 移籍済みの場合はここで抜ける
                    characterButtonInfoList.Add(new CharacterButtonInfo()
                    {
                        Id = trainedChara.CardId,
                        ChampionsWinRate = dataPair.Value.WinRate,   // Heroes専用にする必要もないので、チャンミを間借り
                        RankImage = true,
                        IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                        TrainedChara = trainedChara,
                        IconSizeType = IconBase.SizeType.Chara_80,
                        ButtonSeType = ButtonCommon.ButtonSeType.DecideM01,
                        OnTap = OnTapCharaButton,
                        OnLongTap = OnLongTapCharaButton,
                        IsLock = trainedChara.IsLock,
                    });
                }
            }

            // 出走済みウマ娘がいないときは、専用表示（一度でも出走した後は、移籍防止処理でこの表示になることはない）
            _noCharaTextObj.SetActiveWithCheck(characterButtonInfoList.Count < 1);
            
            // ソート/絞り込み設定（クライアントに保存されている「リーグオブヒーローズ出走データのソート/絞り込み設定」を読み込む）
            var saveTag = SORT_FILTER_SETTING_SAVE_TAG_ENTRY_RECORD;
            SortFilterSetting sortFilterSetting = SortFilterSetting.Load(saveTag);
            
            // ウマ娘アイコンリストをセットアップ
            _cardListVertical.CreateOrRefresh(
                characterButtonInfoList,
                sortFilterSetting,
                onCreateList: OnExecSortFilter,
                autoSelectOnFilter: true,
                onResetFilter: OnExecSortFilter,
                overrideCheckSelectUpdate: () => false  // 絞り込み時に先頭のウマ娘を自動で選択するか否か
            );
            
            // ソート/絞り込み実行時の処理
            void OnExecSortFilter(PartsListSortButton sortButton)
            {
                // 「ソート/絞り込み設定」をクライアントに保存
                SortFilterSetting.Save(saveTag, sortButton.Setting);
            }
        }
        
        /// <summary>
        /// 「獲得リーグスコア」タブを作成（2ページ目）
        /// </summary>
        private void SetupLeagueScoreCharaList(HeroesStage1HonorSelectCharaListResponse.CommonResponse serverData)
        {
            // たずなさんコメント
            SetupTazunaComment();
            
            // 称号獲得条件
            SetupGetHonorConditionText();
            
            // 称号詳細ボタン
            SetupHonorDetailButton();

            // 更新が必要なら、キャラのリスト作成
            if (serverData != null)
            {
                CreateModelList(serverData.selectable_chara_list, serverData.unselectable_chara_list);
            }

            // 表示の作成
            SetupScoreCharaList();
            
        }
        
        // 称号表示用データを作成
        private void CreateModelList(HeroesHonorSelectCharaData[] selectableArray, HeroesHonorSelectCharaData[] unselectableArray)
        {
            _modelList.Clear();
            
            // ソートしておく
            Array.Sort(selectableArray, (a, b) => a.chara_id - b.chara_id);    // 選択可能：スコア順（同率ならID順）
            Array.Sort(selectableArray, (a, b) => b.score - a.score);
            Array.Sort(unselectableArray, (a, b) => a.chara_id - b.chara_id);  // 選択不可はスコア順（同率ならID順）
            Array.Sort(unselectableArray, (a, b) => b.score - a.score);
            
            // 選択可能
            for (int i = 0; i < selectableArray.Length; i++)
            {
                var chara = new WorkCharaData.CharaData(selectableArray[i].chara_id);
                var score = selectableArray[i].score;
                bool isTopThree = score < HeroesUtil.GetHonorBorderLeagueScore();
                var model = new PartsHeroesHonorCharacterListItem.PartsHeroesHonorCharacterListItemModel(chara, score, isTopThree);
                _modelList.Add(model);
            }
            // 選択不可
            for (int i = 0; i < unselectableArray.Length; i++)
            {
                var chara = new WorkCharaData.CharaData(unselectableArray[i].chara_id);
                var score = unselectableArray[i].score;
                var model = new PartsHeroesHonorCharacterListItem.PartsHeroesHonorCharacterListItemModel(chara, score, false);
                _modelList.Add(model);
            }
        }

        /// <summary>
        /// 獲得リーグスコアのキャラリストを設定
        /// 昇順・降順の入れ替えにも使用
        /// </summary>
        private void SetupScoreCharaList()
        {
            _loopScroll.Setup<PartsHeroesHonorCharacterListItem>(_modelList.Count, 
                onItemUpdate: item =>
                {
                    item.Setup(_modelList[item.ItemIndex]);
                },
                emptyText: TextId.Heroes585003.Text(),
                emptyTextFontSize: TextFormat.FontSize.Size_36);// 表示させるキャラリストがないなら、「リーグスコアを獲得したウマ娘がいません」と表示
        }

        /// <summary>
        /// たずなさんコメントをランダム設定
        /// </summary>
        private void SetupTazunaComment()
        {
            // 定数
            const int VIEW_TYPE = (int)HeroesDefine.MessageViewType.RunDataDialog;
            const int CONDITION_ALWAYS = 0;
            const int ID_BEFORE_STAGE1_FINISH_1 = 300;   // Stage1終了まで
            const int ID_BEFORE_STAGE1_FINISH_2 = 301;
            const int STAGE1 = (int)MasterHeroesStageSchedule.Stage.Stage1;
            const int ID_AFTER_PLATINUM_BEFORE_STAGE1_FINISH = 302;   // PLATINUM到達～Stage1終了まで
            const MasterHeroesLeagueRank.LeagueRank PLATINUM = MasterHeroesLeagueRank.LeagueRank.Platinum1;

            // TextIdのリスト
            var textIdList = HeroesUtil.GetLotteryMessageWithoutCondition(VIEW_TYPE);
            
            // 条件つきのものを追加
            var currentStage = WorkDataManager.Instance.HeroesData.Stage;
            var currentStageStep = WorkDataManager.Instance.HeroesData.StageStep;
            var currentRank = WorkDataManager.Instance.HeroesData.MyTeamInfo.GetLeagueRank();
            var messageList = MasterDataManager.Instance.masterHeroesLotteryMessage.GetListWithViewType(VIEW_TYPE);
            var messageListCount = messageList.Count;
            for (int i = 0; i < messageListCount; i++)
            {
                if (messageList[i].ConditionFlag == CONDITION_ALWAYS)
                {
                    // 既に入れてあるのでスルー
                    continue;
                }

                // 条件付きで抽選候補に入れるもの
                switch (messageList[i].Id)
                {
                    case ID_BEFORE_STAGE1_FINISH_1:
                    case ID_BEFORE_STAGE1_FINISH_2:
                        // メインステージ レース開催期間終了まで
                        if ((int)currentStage <= STAGE1 &&
                            currentStageStep <= (int)MasterHeroesStageSchedule.Stage1StageStep.Battle)
                        {
                            textIdList.Add(messageList[i].Id);
                        }

                        break;
                    case ID_AFTER_PLATINUM_BEFORE_STAGE1_FINISH:
                        // PLATINUM到達 ～ランキング集計期間終了まで
                        if ((int)currentStage <= STAGE1 && currentRank >= PLATINUM)
                        {
                            textIdList.Add(messageList[i].Id);
                        }

                        break;
                }
            }
            
            // 抽選
            var textId = HeroesUtil.GetLotteryMessageId(textIdList);
            if (textId == HeroesDefine.INVALID_MESSAGE_ID) return;
            _tazunaComment.text = TextUtil.GetMasterText(MasterString.Category.HeroesLotteryMessage, textId);
        }


        /// <summary>
        /// 称号獲得条件のテキストを設定する
        /// </summary>
        private void SetupGetHonorConditionText()
        {
            // 報酬リストの取得（現状の中身は1つ）
            var reward = MasterDataManager.Instance.masterHeroesSpecialReward.GetPlatinumHeroesSpecialReward();
            if (reward == null)
            {
                Debug.LogError("Platinum到達時の報酬設定が間違っている可能性があります。");
                return;
            }

            // 必要リーグスコア
            var conditionNeedScore = reward.ConditionValue1;
            // 対象人数
            var conditionMemberNum = reward.ConditionValue2;
            
            // リーグスコア条件
            var needPointStr = TeamStadiumUtil.GetPointText(conditionNeedScore, true);
            _getHonorCondition1Text.text = TextUtil.Format(TextId.Heroes511034.Text(), needPointStr);

            // PLATINUM到達条件
            _getHonorCondition2Text.text = TextUtil.Format(TextId.Heroes511035.Text(), conditionMemberNum);
        }

        /// <summary>
        /// 称号詳細ボタンの設定をする
        /// </summary>
        private void SetupHonorDetailButton()
        {
            _honorDetailButton.SetOnClick(() =>
            {
                DialogHeroesHonorDetail.PushDialog();
            });
        }

        /// <summary>
        /// ウマ娘アイコンをタップした時の処理
        /// </summary>
        private void OnTapCharaButton(CharacterButton button)
        {
            // 専用の戦績付きキャラ詳細を開く
            DialogTrainedCharacterHeroesDetail.Open(button.Info.TrainedChara, onClose:SetupRunDataCharaList, isChangeableNickName:true);
        }
        
        /// <summary>
        /// ウマ娘アイコンをロングタップした時の処理
        /// </summary>
        private void OnLongTapCharaButton(CharacterButton button)
        {
            // 専用の戦績付きキャラ詳細を開く
            DialogTrainedCharacterHeroesDetail.Open(button.Info.TrainedChara, onClose:SetupRunDataCharaList, isChangeableNickName:true);
        }
        
    }
}
