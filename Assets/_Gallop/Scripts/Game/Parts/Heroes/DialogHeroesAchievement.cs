using System;
using UnityEngine;
using System.Collections.Generic;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズ：実績ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogHeroesAchievement : DialogInnerBase
    {
        /// <summary> ループスクロール </summary>
        [SerializeField] private LoopScroll _loopScroll;
        [SerializeField] private ScrollRectCommon _scrollRectCommoncroll;

        /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;
        
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // リーグランクアイコン
            PartsHeroesAchievementItem.RegisterDownload(register);
        }

        /// <summary>
        /// ダイアログ表示
        /// </summary>
        /// <returns></returns>
        public static DialogHeroesAchievement PushDialog()
        {
            var content = LoadAndInstantiatePrefab<DialogHeroesAchievement>(ResourcePath.DIALOG_HEROES_ACHIEVEMENT);
            var data = content.CreateDialogData();
            var dialog = DialogManager.PushDialog(data);

            content.Initialize();
            return content;
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Common0152.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.CenterButtonCallBack = OnCenterClick;

            return dialogData;
        }

        /// <summary>
        /// 閉じるボタン押下時のコールバック
        /// </summary>
        /// <param name="dialog"></param>
        private void OnCenterClick(DialogCommon dialog)
        {
            dialog.Close();
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Initialize()
        {
            // 各開催回の表示を作成
            int listCount = 0;

            // ダイアログを開く前段階で通信して、必要な情報はWorkに格納済み
            var heroesPastResultArray = WorkDataManager.Instance.HeroesData.HeroesPastResultArray;
            
            // ソートしておく
            Array.Sort(heroesPastResultArray, (x, y) => (int)x.heroes_id - y.heroes_id);
            listCount = heroesPastResultArray.Length;
            _loopScroll.Setup<PartsHeroesAchievementItem>(listCount, item =>
            {
                var pastResult = heroesPastResultArray[item.ItemIndex];
                // 更新時処理
                var model = new PartsHeroesAchievementItem.Model(pastResult.heroes_id, pastResult.league_score,
                    pastResult.is_finalist == 1, OnClickRaceOrderButton);
                item.Init(model);
            });

            // 初期スクロール位置
            int tempHeroesId = WorkDataManager.Instance.HeroesData.TempHeroesIdInAchievementReplay;
            if (tempHeroesId != 0)
            {
                _scrollRectCommoncroll.SetScrollPositionEasy(listCount, tempHeroesId - 1);
            }
        }

        /// <summary>
        /// 「着順表」ボタン押下時の処理
        /// </summary>
        public void OnClickRaceOrderButton(int heroesId)
        {
            // 有効なFinalCurrentRaceがなければ取得する
            // /heroes/indexで都度Reset()しているので、実績からリプレイを見ない限りはこっち
            if (!WorkDataManager.Instance.HeroesData.FinalCurrentRace.IsValid())
            {
                // レースリプレイ取得APIの送信
                HeroesUtil.SendHeroesGetPastRaceReplay(heroesId,
                    // 成功したら
                    onSuccess: () =>
                    {
                        // 「着順表」ダイアログを開く
                        OpenDialogHeroesRaceResultList();
                    });
            }
            else
            {
                // 実績からリプレイを見た復帰時に自動で開く際のフロー：すでに通信済みなので通信不要
                // 「着順表」ダイアログを開く
                OpenDialogHeroesRaceResultList();
            }

            void OpenDialogHeroesRaceResultList()
            {
                // 開催回の記録をしておく
                WorkDataManager.Instance.HeroesData.SetTempHeroesIdInAchievementReplay(heroesId);
                
                // 「着順表」ダイアログを開く
                DialogHeroesRaceResultList.OpenWithFinal(
                    // 「リプレイ」が押されたら
                    onClickReplay: (roundNum) =>
                    {
                        // 「リプレイ確認」ダイアログを開く
                        OpenReplayConfirmDialog();
                    },
                    onClickClose: () =>
                    {
                        // 着順表を開いていない状態のイベントTOPでは、常にFinalCurrentRaceがResetされた状態である必要がある
                        WorkDataManager.Instance.HeroesData.FinalCurrentRace.ResetOnTop();
                        // 変数をリセットしておく
                        WorkDataManager.Instance.HeroesData.ResetTempHeroesIdInAchievementReplay();
                    });
            }
        }
        
        /// <summary>
        /// 「リプレイ確認」ダイアログを開く
        /// </summary>
        private void OpenReplayConfirmDialog()
        {
            const SceneDefine.ViewId PREV_VIEW_ID = SceneDefine.ViewId.HeroesTop;

            // 汎用の「リプレイ確認」ダイアログを開く
            RaceUtil.OpenReplayConfirmDialog(
                // 「OK」が押されたら
                onClickDecideButton: () =>
                {
                    // ダイアログを全て閉じる
                    DialogManager.RemoveAllDialog();

                    // フェードを行いつつパドックへ遷移
                    UIManager.Instance.StartCoroutine(
                        HeroesUtil.GoToFinalPaddockCoroutineOnAchievementReplay(prevViewId: PREV_VIEW_ID)
                    );
                });
        }

    }
}
