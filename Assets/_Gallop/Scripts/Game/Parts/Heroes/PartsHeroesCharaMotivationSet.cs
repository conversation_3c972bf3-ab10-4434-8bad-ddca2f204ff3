using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズ：キャラアイコン＋やる気アイコン＋ウマ番のセット
    /// </summary>
    [AddComponentMenu("")]
    public class PartsHeroesCharaMotivationSet : MonoBehaviour
    {
        private const string FLASH_PLN_PLATE = "PLN_dum_plate00";
        private const string FLASH_TXT_NUMBER = "TXT_txt_number00";
        private const string FLASH_MOT_MOTIVATION = "MOT_mc_icon_motivation00";
        private const string FLASH_LABEL_IN = "in";
        private const string FLASH_LABEL_IN_END = "in_end";
        private const string FLASH_LABEL_IN_MOTIVATION = "in_motivation";


        /// <summary>キャラアイコン</summary>
        [SerializeField] private CharacterButton _characterButton = null;

        /// <summary>やる気＋ウマ番A2U(Flash)</summary>
        [SerializeField] private Transform _motivationAndFrameOrderFlashRoot = null;
        [SerializeField] private FlashToUgui _motivationAndFrameOrderFlashToUgui = null;


        private FlashPlayer _motivationAndFrameOrderFlashPlayer;
        private bool _isNeedFlashToUgui = false;

        private WorkHeroesData.MemberInfo _workMemberInfo = null;
        private HorseData _horseData = null;

        private RaceDefine.Motivation _motivation;
        private int _frameOrder;
        private Action<CharacterButton> _onLongTapCharaButton;


        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_MOTIVATION_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.TEAM_STADIUM_BRACKET_PATH);
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_RACE_PT_03);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(WorkHeroesData.MemberInfo workMemberInfo,
                          HorseData horseData,
                          bool isNeedFlashToUgui = false)
        {
            _workMemberInfo = workMemberInfo;
            _horseData = horseData;
            if (_horseData == null)
                return;

            _motivation = _horseData.Motivation;
            _frameOrder = (_horseData.ResponseHorseData != null) ? _horseData.ResponseHorseData.frame_order : 0;

            _isNeedFlashToUgui = isNeedFlashToUgui;

            // キャラアイコンのセットアップ
            _characterButton.Setup(new CharacterButtonInfo()
            {
                Id = _workMemberInfo.CardId,
                IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                RankImage = true,
                TrainedChara = _workMemberInfo.TrainedCharaData,
                OnTap = (charaButton) => { OnTapCharaButton(charaButton); },
                OnLongTap = (charaButton) => { OnLongTapCharaButton(charaButton); },
            });
            _characterButton.SetEnable(true);
            _characterButton.SetSizeType(IconBase.SizeType.Chara_46);

            // やる気＋ウマ番A2U(Flash)のセットアップ
            SetupMotivationAndFrameOrderFlash();
        }

        /// <summary>
        /// やる気＋ウマ番A2U(Flash)のセットアップ
        /// </summary>
        private void SetupMotivationAndFrameOrderFlash()
        {
            Color32 INITIAL_PLATE_COLOR = new Color32(0xB3, 0xB3, 0xB3, 0xFF);

            if (_motivationAndFrameOrderFlashPlayer == null)
            {
                _motivationAndFrameOrderFlashPlayer = FlashLoader.LoadOnView(ResourcePath.TEAM_STADIUM_MOTIVATION_FLASH_PATH, _motivationAndFrameOrderFlashRoot);
            }

            _motivationAndFrameOrderFlashPlayer.SetLayer(_motivationAndFrameOrderFlashRoot.gameObject.layer);
            var canvas = gameObject.GetComponentInParent<Canvas>();
            _motivationAndFrameOrderFlashPlayer.SortLayer = canvas != null ? canvas.sortingLayerName : UIManager.UI_SORTING_LAYER_NAME;
            _motivationAndFrameOrderFlashPlayer.SortOffset = canvas != null ? canvas.sortingOrder + 1 : 1;

            _motivationAndFrameOrderFlashPlayer.SetActiveWithCheck(true);
            _motivationAndFrameOrderFlashPlayer.Init();

            if (_isNeedFlashToUgui)
            {
                _motivationAndFrameOrderFlashToUgui.Target = _motivationAndFrameOrderFlashPlayer.gameObject;
                _motivationAndFrameOrderFlashToUgui.IsVisibleTargetMesh = false;
                _motivationAndFrameOrderFlashToUgui.CreateClone();
            }

            var plate = ResourceManager.LoadOnView<Texture2D>(ResourcePath.TEAM_STADIUM_BRACKET_PATH);
            _motivationAndFrameOrderFlashPlayer.SetTexture(FLASH_PLN_PLATE, plate);
            _motivationAndFrameOrderFlashPlayer.GetPlane(FLASH_PLN_PLATE).SetBaseColor(INITIAL_PLATE_COLOR);
            _motivationAndFrameOrderFlashPlayer.GetText(FLASH_TXT_NUMBER).SetText("?");
            _motivationAndFrameOrderFlashPlayer.Play(FLASH_LABEL_IN);
        }

        /// <summary>
        /// イリアニメを再生
        /// </summary>
        public void PlayIn(int delayCount, bool immediate)
        {
            // やる気＋ウマ番確定演出

            if (immediate)
            {
                PlayFlash(FLASH_LABEL_IN_END);
                return;
            }

            var delay = delayCount * 0.1f; // 3frame / 30f
            this.Timer(delay, () =>
            {
                PlayFlash(FLASH_LABEL_IN_MOTIVATION);
                AudioManager.Instance.PlaySe(AudioId.SFX_PADDOCK_MOTIVATION_DISP);
            }, false);
        }

        /// <summary>
        /// ラベル名を指定してA2U(Flash)を再生
        /// </summary>
        private void PlayFlash(string label)
        {
            var postNumber = StandaloneSimulator.HorsePostNumberCalculator.GetPostNumber(_frameOrder - 1, HeroesDefine.MAX_RACE_ENTRY_NUM);
            var text = _frameOrder.ToString();
            var fontColor = TextFormat.GetFontColor(RaceUtil.GetUIPostNumberTextColor(postNumber));
            var color = RaceUtil.GetUIPostNumberImageColor(postNumber);
            var motivationLabel = GetMotivationLabel(_motivation);

            _motivationAndFrameOrderFlashPlayer.GetMotion(FLASH_MOT_MOTIVATION).SetMotionPlay(motivationLabel);
            _motivationAndFrameOrderFlashPlayer.GetPlane(FLASH_PLN_PLATE).SetBaseColor(color);
            var number = _motivationAndFrameOrderFlashPlayer.GetText(FLASH_TXT_NUMBER);
            number.SetText(text);
            number.SetTextColor(fontColor);
            if (_isNeedFlashToUgui)
            {
                _motivationAndFrameOrderFlashToUgui.CleanupClone();
                _motivationAndFrameOrderFlashToUgui.CreateClone();
            }
            _motivationAndFrameOrderFlashPlayer.Play(label);
        }

        /// <summary>
        /// A2U(Flash)のやる気アイコンの「各やる気に対応したラベル」を取得
        /// </summary>
        private string GetMotivationLabel(RaceDefine.Motivation motivation)
        {
            const string LABEL_FORMAT = "mot{0}";
            return string.Format(LABEL_FORMAT, (int)motivation - 1);
        }

        /// <summary>
        /// ハケアニメを再生
        /// </summary>
        public void PlayOut()
        {
            _motivationAndFrameOrderFlashRoot.gameObject.SetActiveWithCheck(false);
        }

        /// <summary>
        /// ウマ娘アイコンをタップした時の処理
        /// </summary>
        private void OnTapCharaButton(CharacterButton charaButton)
        {
            OnLongTapCharaButton(charaButton);
        }

        /// <summary>
        /// ウマ娘アイコンを長押しした時の処理
        /// </summary>
        private void OnLongTapCharaButton(CharacterButton charaButton)
        {
            if (_horseData == null)
                return;

            // リーグオブヒーローズ向けにウマ娘詳細ダイアログを開く
            HeroesUtil.OpenCharaDetailDialog(_horseData);
        }
    }
}
