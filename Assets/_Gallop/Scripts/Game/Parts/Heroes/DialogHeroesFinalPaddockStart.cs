using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズ：Finalステージ：パドックでの出走前の演出 （コンフルページ名："A2U+FA ファイナルステージ_出走前の演出"）
    /// </summary>
    [AddComponentMenu("")]
    public class DialogHeroesFinalPaddockStart : DialogInnerBase
    {
        /// <summary>表示のパターン</summary>
        public enum Pattern
        {
            None,
            MyRaceNotMain,          // 自分が出走するレース：非メインレース
            MyRaceMain,             // 自分が出走するレース：メインレース
            OtherRaceABlockMain,    // 他人のレースの視聴：Aブロック メインレース
            OtherRaceBBlockMain,    // 他人のレースの視聴：Bブロック メインレース
        }

        /// <summary>全画面ボタン</summary>
        [SerializeField] private ButtonCommon _screenButton;
        /// <summary>「TAP!」UIのオブジェクト</summary>
        [SerializeField] private GameObject _tapUiObj;


        private Pattern _pattern;

        /// <summary>A2U(Flash)</summary>
        private FlashActionPlayer _flashActionPlayer;
        private FlashPlayer _flashPlayer;

        /// <summary>破棄時に呼ばれる外部コールバック</summary>
        private System.Action _onDestroy = null;

        private WorkHeroesData _workHeroesData => WorkDataManager.Instance.HeroesData;

        public override DialogCommonBase.FormType GetFormType() { return DialogCommonBase.FormType.WITHOUT_FRAME; }

        public override DialogCommon.Data.ObjectParentType GetParentType() { return DialogCommon.Data.ObjectParentType.Base; }

        protected override DialogCommon.Data CreateDialogData()
        {
            // 裏地のマスクの透明度
            const float BG_ALPHA_RATE = 0.5f;

            var data = base.CreateDialogData();
            data.EnableOutsideClick = false;
            data.BgAlphaRate = BG_ALPHA_RATE;
            return data;
        }


        /// <summary>
        /// DL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // A2U(Flash)
            register.RegisterPathWithoutInfo(ResourcePath.HEROES_FINAL_PADDOCK_START_FLASH);

            // サウンド
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_HEROES_FINAL_PADDOCK_START);
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(Pattern pattern, System.Action onDestroy)
        {
            var component = LoadAndInstantiatePrefab<DialogHeroesFinalPaddockStart>(ResourcePath.DIALOG_HEROES_FINAL_PADDOCK_START);
            component.Setup(pattern, onDestroy);

            var data = component.CreateDialogData();
            data.OnChangeSortingOrder = component.OnChangeSortOrder;
            data.DestroyCallBack = component.OnDestroy;

            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(Pattern pattern, System.Action onDestroy)
        {
            _pattern = pattern;
            _onDestroy = onDestroy;

            // A2U(Flash)のセットアップ
            SetupFlash();

            // 全画面ボタンのセットアップ
            _screenButton.SetActiveWithCheck(false); // 始めは押せない
            _screenButton.SetOnClick(OnClickScreenButton);

            // 「TAP!」UIのセットアップ
            _tapUiObj.SetActiveWithCheck(false); // 始めは非表示

            // イリアニメを再生
            PlayIn();
        }

        /// <summary>
        /// A2U(Flash)のセットアップ
        /// </summary>
        private void SetupFlash()
        {
            _flashActionPlayer = FlashActionPlayer.Load(ResourcePath.HEROES_FINAL_PADDOCK_START_FLASH, transform);
            _flashPlayer = _flashActionPlayer.LoadFlashPlayer();
            _flashPlayer.transform.SetAsFirstSibling();
        }

        /// <summary>
        /// ダイアログの描画順変更時の処理
        /// </summary>
        private void OnChangeSortOrder(int order, string layer)
        {
            if (_flashActionPlayer == null)
                return;

            _flashActionPlayer.SetSortLayer(layer);
            _flashActionPlayer.SetSortOffset(order + 1);
        }

        /// <summary>
        /// イリアニメを再生
        /// </summary>
        private void PlayIn()
        {
            string inLabel = string.Empty;
            string inEndLabel = string.Empty;

            // 表示パターンによってラベルが異なる
            switch (_pattern)
            {
                case Pattern.MyRaceNotMain:
                    inLabel = "in1";
                    inEndLabel = "in1_end";
                    SetupMyTeamUI();
                    break;
                case Pattern.MyRaceMain:
                    inLabel = "in2";
                    inEndLabel = "in2_end";
                    SetupMyTeamUI();
                    break;
                case Pattern.OtherRaceABlockMain:
                    inLabel = "in3";
                    inEndLabel = "in3_end";
                    break;
                case Pattern.OtherRaceBBlockMain:
                    inLabel = "in4";
                    inEndLabel = "in4_end";
                    break;
            }

            // イリアニメ完了時の処理をセット
            _flashPlayer.SetActionCallBack(inEndLabel, OnCompleteIn, AnMotionActionTypes.End);

            // イリアニメ再生
            _flashPlayer.Play(inLabel);

            // SE
            AudioManager.Instance.PlaySe(AudioId.SFX_HEROES_FINAL_PADDOCK_START);
        }

        /// <summary>
        /// イリアニメの完了時の処理
        /// </summary>
        private void OnCompleteIn()
        {
            // 全画面ボタンを押せるように
            _screenButton.SetActiveWithCheck(true);

            // 「TAP!」UIを表示
            _tapUiObj.SetActive(true);
        }

        /// <summary>
        /// A2U(Flash)のマイチームのUI部分のセットアップ
        /// </summary>
        private void SetupMyTeamUI()
        {
            var teamInfo = _workHeroesData.MyTeamInfo;

            // チーム名
            {
                const string TEAM_NAME_TXT = "TXT_txt_teamname00";
                string teamNameString = teamInfo.TeamName;
                _flashPlayer.SetText(teamNameString, TEAM_NAME_TXT);
            }

            // ウマ娘アイコン（左）
            {
                var rootAnObject = _flashPlayer.GetObj("OBJ_mc_character_set00");
                var memberInfo = teamInfo.GetMemberInfoWithIndex(0);
                SetupCharaIcon(rootAnObject, memberInfo);
            }

            // ウマ娘アイコン（中）
            {
                var rootAnObject = _flashPlayer.GetObj("OBJ_mc_character_set01");
                var memberInfo = teamInfo.GetMemberInfoWithIndex(1);
                SetupCharaIcon(rootAnObject, memberInfo);
            }

            // ウマ娘アイコン（右）
            {
                var rootAnObject = _flashPlayer.GetObj("OBJ_mc_character_set02");
                var memberInfo = teamInfo.GetMemberInfoWithIndex(2);
                SetupCharaIcon(rootAnObject, memberInfo);
            }
        }

        /// <summary>
        /// ウマ娘アイコンのセットアップ
        /// </summary>
        private void SetupCharaIcon(AnObject rootAnObject, WorkHeroesData.MemberInfo memberInfo)
        {
            if (rootAnObject == null)
                return;
            if (memberInfo == null)
                return;
            if (memberInfo.TrainedCharaData == null)
                return;

            // ウマ娘画像
            {
                const string CHARA_ICON_PLN = "PLN_dum_ico_chara00";
                int dressId = (memberInfo.TrainedCharaData != null) ? memberInfo.TrainedCharaData.GetRaceDressId(false) : memberInfo.DressId; // 衣装変更は反映させない
                var texturePath = ResourcePath.GetTrainedCardThumbnailIconPath(memberInfo.CharaId, dressId, memberInfo.TalentLevel);
                var texture = ResourceManager.LoadOnView<Texture>(texturePath);
                _flashPlayer.SetTexture(CHARA_ICON_PLN, texture, rootGameObject: rootAnObject.GameObject);
            }

            // ランク画像
            {
                const string RANK_ICON_PLN = "PLN_dum_ico_rank00";
                var sprite = GallopUtil.GetFinalTrainingRankSprite(memberInfo.TrainedCharaData.Rank);
                _flashPlayer.SetSprite(RANK_ICON_PLN, sprite, rootGameObject: rootAnObject.GameObject);
            }

            // 現在の作戦
            var runningStyle = memberInfo.RunningStyle;

            // 作戦名
            {
                const string TEAM_NAME_TXT = "TXT_txt_status00";
                string runningStyleName = RaceUtil.GetRunnignStyleText(runningStyle);
                _flashPlayer.SetText(runningStyleName, TEAM_NAME_TXT, rootGameObject: rootAnObject.GameObject);
            }

            // 作戦の脚質適正グレードアイコン
            {
                const string PROPER_GRADE_ICON_PLN = "PLN_dum_ico_status00";
                var properGrade = (RaceDefine.ProperGrade)memberInfo.TrainedCharaData.GetProperRunningStyle(runningStyle);
                var sprite = SingleModeDefine.GetProperGradeSprite(properGrade);
                _flashPlayer.SetSprite(PROPER_GRADE_ICON_PLN, sprite, rootGameObject: rootAnObject.GameObject);
            }
        }

        /// <summary>
        /// 全画面ボタン押下時処理
        /// </summary>
        private void OnClickScreenButton()
        {
            // 連打対策
            _screenButton.SetActiveWithCheck(false);

            // ハケアニメを再生
            PlayOut();
        }

        /// <summary>
        /// ハケアニメを再生
        /// </summary>
        private void PlayOut()
        {
            // ブラーをフェードアウトさせる時間（秒）
            const float BLUR_FADE_OUT_SEC = 0.167f;

            var dialog = GetDialog() as DialogCommon;
            if (dialog == null)
                return;

            string outLabel = string.Empty;

            // 表示パターンによってラベルが異なる
            switch (_pattern)
            {
                case Pattern.MyRaceNotMain:
                    outLabel = "out1";
                    break;
                case Pattern.MyRaceMain:
                    outLabel = "out2";
                    break;
                case Pattern.OtherRaceABlockMain:
                    outLabel = "out3";
                    break;
                case Pattern.OtherRaceBBlockMain:
                    outLabel = "out4";
                    break;
            }

            // ハケアニメを再生
            _flashPlayer.Play(outLabel);
            dialog.Close();
            UIManager.Instance.PlayImageEffectBlurFade(0f, BLUR_FADE_OUT_SEC, AnimationCurvePreset.CurveType.CubicEaseInOut);
            _tapUiObj.SetActive(false);
        }

        /// <summary>
        /// 破棄時処理
        /// </summary>
        private void OnDestroy()
        {
            UIManager.Instance.ResetImageEffectBlur();
            _onDestroy?.Invoke();
        }
    }
}