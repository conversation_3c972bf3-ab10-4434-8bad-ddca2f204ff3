using AnimateToUnity;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// AnPlaneを真似するクラス
    /// </summary>
    [DisallowMultipleComponent]
    [RequireComponent((typeof(CanvasRenderer)))]
    [AddComponentMenu("")]
    public sealed class FlashToUguiPlane : MaskableGraphic, IHasBaseAnObject
    {
        private MeshFilter _meshFilter;
        private AnPlane _targetPlane;
        private Renderer _targetRenderer;
        private CanvasRenderer _canvasRenderer;
        private Mask _mask;
        private RectMask2D _mask2D;
        /// <summary> コピー元のオブジェクトに適用されているマテリアルをコピー先オブジェクトのマテリアルに常に同期させるかどうか </summary>
        private bool _needSyncTargetMaterial = false;

        public bool IsMask => _mask != null || _mask2D != null;

            /// <summary>
        /// クローン元のオブジェクト
        /// </summary>
        /// <returns></returns>
        public AnObjectBase GetAnObject()
        {
            return _targetPlane;
        }

        /// <summary>
        /// This function is called when the object becomes enabled and active.
        /// </summary>
        protected override void OnEnable()
        {
            if (_targetRenderer != null && _meshFilter != null)
            {
                _canvasRenderer.SetMaterial(_targetRenderer.sharedMaterial, _targetRenderer.sharedMaterial.mainTexture);
                _canvasRenderer.SetMesh(_meshFilter.sharedMesh);
            }

            base.OnEnable ();
        }

        public override Material material
        {
            get => _targetRenderer != null ? _targetRenderer.sharedMaterial : null;

            set
            {
                if (_targetRenderer == null)
                {
                    return;
                }
                _targetRenderer.sharedMaterial = value;
                SetMaterialDirty();
            }
        }
        
        public override Texture mainTexture
        {
            get
            {
                Texture tex = null;
                if (_targetRenderer != null)
                {
                    var mat = material;
                    if (mat && mat.HasProperty("_MainTex"))
                    {
                        tex = mat.mainTexture;
                    }
                }
                return tex ?? s_WhiteTexture;
            }
        }

        /// <summary>
        /// 使用準備
        /// </summary>
        /// <param name="meshFilter"></param>
        /// <param name="targetRenderer"></param>
        /// <param name="useMask2D"></param>
        /// <param name="needSyncTargetMaterial"> Update時にコピー元オブジェクトに適用されているマテリアルとコピー先オブジェクトのマテリアルが異なるなら同期させるかどうか </param>
        public void Setup( AnPlane targetPlane,
            MeshFilter meshFilter,
            Renderer targetRenderer,
            bool useMask2D = false,
            bool needSyncTargetMaterial = false
        )
        {
            _targetPlane = targetPlane;
            _meshFilter = meshFilter;
            _needSyncTargetMaterial = needSyncTargetMaterial;
            _targetRenderer = targetRenderer;
            _canvasRenderer = GetComponent<CanvasRenderer>();
            if (_targetRenderer != null && _meshFilter != null)
            {
                _canvasRenderer.SetMaterial(targetRenderer.sharedMaterial, targetRenderer.sharedMaterial.mainTexture);
                _canvasRenderer.SetMesh(_meshFilter.sharedMesh);
            }

            if (_targetPlane.ObjectType == AnObjectTypes.StencilAlphaMask)
            {
                if (useMask2D)
                {
                    _mask2D = gameObject.AddComponent<RectMask2D>();
                    gameObject.GetComponent<RectTransform>().sizeDelta = new Vector2(mainTexture.width, mainTexture.height);
                }
                else
                {
                    _mask = gameObject.AddComponent<Mask>();

                    _mask.showMaskGraphic = false;
                }
            }

            raycastTarget = false;

            Update();　//スケールの計算を行うためUpdateを呼ぶ
        }

        /// <summary>
        /// マテリアル設定
        /// </summary>
        /// <param name="baseMaterial"></param>
        /// <returns></returns>
        public override Material GetModifiedMaterial(Material baseMaterial)
        {
            Material mat = null;
            if (_targetRenderer == null)
            {
                mat = baseMaterial;
            }
            else
            {
                mat = _targetRenderer.sharedMaterial;
            }

            return base.GetModifiedMaterial(mat);
        }
        
        /// <summary>
        /// Call to update the geometry of the Graphic onto the CanvasRenderer.
        /// </summary>
        protected override void UpdateGeometry()
        {
            //何もしない（_meshFilterのMeshをそのまま使う）
        }
        
        /// <summary>
        /// 更新
        /// </summary>
        private void Update()
        {
            if (_targetPlane == null || _targetRenderer == null || _meshFilter == null)
            {
                SetMesh(null);
                return;
            }

            //クローン元のPlaneが表示中ならメッシュを流し込む
            if (IsVisibleTarget(_targetPlane))
            {
                SetMesh( _meshFilter.sharedMesh );
                // コピー元オブジェクトとコピー先オブジェクトのマテリアルが異なるなら反映し直す
                if (_needSyncTargetMaterial && _canvasRenderer.GetMaterial() != _targetRenderer.sharedMaterial)
                {
                    SyncCanvasRendererMaterial();
                }
            }
            else
            {
                SetMesh(null);
            }

            var myTransform = transform;
            var targetTransform = _targetRenderer.transform;

            myTransform.position = targetTransform.position;
            myTransform.rotation = targetTransform.rotation;

            var scale = targetTransform.lossyScale;
            var parentScale = myTransform.parent.lossyScale;

            //Canvas上での相対的な拡大率に補正（0割回避も含む）
            if (Math.IsFloatEqualLight(parentScale.x, 0f))
            {
                scale.x = 0f;
            }
            else
            {
                scale.x /= parentScale.x;
            }
            
            if (Math.IsFloatEqualLight(parentScale.y, 0f))
            {
                scale.y = 0f;
            }
            else
            {
                scale.y /= parentScale.y;
            }
            
            if (Math.IsFloatEqualLight(parentScale.x, 0f))
            {
                scale.z = 0f;
            }
            else
            {
                scale.z /= parentScale.z;
            }
            myTransform.localScale = scale;
        }

        /// <summary>
        /// マテリアル情報の同期を取る
        /// </summary>
        public void SyncCanvasRendererMaterial()
        {
            if(_canvasRenderer == null || _targetRenderer == null)
            {
                return;
            }

            _canvasRenderer.SetMaterial(_targetRenderer.sharedMaterial, _targetRenderer.sharedMaterial.mainTexture);
        }

        /// <summary>
        /// クローン元の対象AnPlaneが表示すべき状態かどうか
        /// </summary>
        /// <param name="targetPlane">チェックする対象のAnPlane</param>
        /// <returns>表示するべきならtrue</returns>
        private static bool IsVisibleTarget( AnPlane targetPlane )
        {
            return targetPlane.Visible && targetPlane.VisibleInHierarchy && targetPlane._visibleByAlpha;
        }

        /// <summary>
        /// メッシュを更新する
        /// AnPlaneは常にMeshの頂点を更新し続けているので、Meshの設定も常に行う必要がある（Dirtyで判定できれば軽量化できそう？）
        /// </summary>
        /// <param name="mesh"></param>
        private void SetMesh( Mesh mesh )
        {
            _canvasRenderer.SetMesh(mesh);
            if (_mask != null)
            {
                _mask.enabled = mesh != null;
            }
            else if (_mask2D != null)
            {
                _mask2D.enabled = mesh != null;
            }
        }
    }
}
