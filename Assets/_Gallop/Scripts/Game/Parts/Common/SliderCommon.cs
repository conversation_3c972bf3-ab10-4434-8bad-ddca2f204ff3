using System;
using System.Collections;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// Gallop用Sliderクラス
    /// </summary>
    [AddComponentMenu("")]
    public class SliderCommon : Slider, ILockable
    {
#if UNITY_EDITOR
        /// <summary>
        /// デフォルト設定
        /// </summary>
        protected override void Reset()
        {
            base.Reset();
        }
#endif
        private bool _isSlide = false;
#if UNITY_ANDROID || UNITY_IOS

        ///#114554
        /// 一部のAndroid端末にて、
        /// スライダーを操作した状態で
        /// 通知センターを開いて、ドラッグを行った際に
        /// 通知センターを開いているのにもかかわらず、
        /// スライダーが操作できてしまう不具合の対応
        private bool _isPausingApplication = false;
        
        ///#116683
        /// 一部のAndroid端末にて、
        /// スライダーを操作した状態で通知センターを開き、
        /// ドラッグをし続けた状態で復帰した際に
        /// UIの表示に不具合が発生する対応
        private bool _isReturnedApplicationAndNotCalledPointerDown = false;
#endif
        ///#116683
        /// Undroid端末、DMM版にて
        /// BackキーやEscキーを入力してダイアログを閉じた際に
        /// ボタンを離した際のイベントが呼び出されない不具合の対応
#if UNITY_ANDROID || DMM
        /// <summary>
        /// 操作中にアプリケーションが中断されたときのイベント
        /// </summary>
        public Action OnPausingOperation { private get; set; }
#endif
        
        public Action OnSliderPointerDown { private get; set; }
        public Action OnSliderPointerUp { private get; set; }
        
        public bool IsSlide => _isSlide;

        public override void OnDrag(PointerEventData eventData)
        {
#if UNITY_ANDROID || UNITY_IOS
            if (_isPausingApplication || _isReturnedApplicationAndNotCalledPointerDown)
            {
                //#114554
                //一部のAndroid端末にて、
                //UIの操作をしながら通知センターを開くことができ、
                //復帰時にUIの表示上は離した状態だが、
                //スライダーが動かせる状態にあるため、
                //ポーズ状態でイベントが呼び出された場合は
                //強制的にボタンを離した時のイベントを呼び出すように調整
                OnPointerUp(eventData);
                return;
            }
#endif
            
            if (IsLock())
            {
                return;
            }
            
            _isSlide = true;
            
            base.OnDrag(eventData);
        }

        public override void OnPointerDown(PointerEventData eventData)
        {
#if UNITY_ANDROID || UNITY_IOS
            if (_isPausingApplication)
            {
                //#114554
                //一部のAndroid端末にて、
                //UIの操作をしながら通知センターを開くことができ、
                //復帰時にUIの表示上は離した状態だが、
                //スライダーが動かせる状態にあるため、
                //ポーズ状態でイベントが呼び出された場合は
                //強制的にボタンを離した時のイベントを呼び出すように調整
                OnPointerUp(eventData);
                return;
            }

            _isReturnedApplicationAndNotCalledPointerDown = false;
#endif
            if (IsLock())
            {
                return;
            }
            
            _isSlide = true;

            base.OnPointerDown(eventData);
            
            OnSliderPointerDown.Call();
        }

        public override void OnPointerUp(PointerEventData eventData)
        {
            
#if UNITY_ANDROID || UNITY_IOS
            if (_isPausingApplication || _isReturnedApplicationAndNotCalledPointerDown)
            {
                return;
            }
#endif
            if (IsLock())
            {
                return;
            }
            
            _isSlide = false;

            base.OnPointerUp(eventData);
            
            OnSliderPointerUp.Call();
        }
        
#if DMM || UNITY_ANDROID
        /// <summary>
        /// アプリケーションのアクティブ状態が変更されたとき
        /// </summary>
        /// <param name="hasFocus"></param>
        private void OnApplicationFocus(bool hasFocus)
        {

#if UNITY_ANDROID
            _isPausingApplication = !hasFocus;
#endif

            if (hasFocus)
            {
                OnEnable();
#if UNITY_ANDROID
                _isReturnedApplicationAndNotCalledPointerDown = true;
#endif
            }
            else
            {
                OnDisable();

                //#116299
                //Android版にてInputFieldCommon等を使用して、
                //キーボードを呼び出した際に OnApplicationFocus が呼び出され、
                //このイベント内の ResetCurrentEventSystem が呼ばれることにより、
                //キーボードを開くイベントを中断してしまい、
                //キーボードが開くことができなくなる不具合が発生するためにAndroid版では、呼び出さないように変更した
#if DMM
                UIManager.ResetCurrentEventSystem();
#endif
                OnEnable();
            }
        }
#endif

#if UNITY_IOS || UNITY_ANDROID

        /// <summary>
        /// アプリケーションが中断されたとき
        /// </summary>
        /// <param name="pauseStatus"></param>
        private void OnApplicationPause(bool pauseStatus)
        {
            _isPausingApplication = pauseStatus;

            if (pauseStatus)
            { 
                OnDisable();
            }
            else
            {
                OnEnable();
#if UNITY_ANDROID
                _isReturnedApplicationAndNotCalledPointerDown = true;
#endif
            }
        }
        
#endif
        
#if UNITY_ANDROID || DMM

        // #116683
        // Android端末にて、
        // スライダーを操作しながら、Backボタンを入力して
        // ダイアログを閉じた際にPointerUpのイベントが呼ばれないために
        // ライブ楽曲の音量がダイアログが閉じ切っても
        // なり続けてしまう不具合の対応
        private void OnDisable()
        {
            if (_isSlide)
            {
                OnSliderPointerUp.Call();
                OnPausingOperation.Call();
            }

            _isSlide = false;
            base.OnDisable();
        }
#endif

        /// <summary>
        /// ロックさせているか
        /// </summary>
        public bool IsLock()
        {
            var parentLockableBehaviour = GetComponentInParent<LockableBehaviour>();
            if (parentLockableBehaviour != null)
            {
                return parentLockableBehaviour.IsLock();
            }

            return false;
        }

        public void OnLock(LockableBehaviour lockTarget)
        {
        }

        public void OnUnlock(LockableBehaviour unlockTarget)
        {
        }
    }
}