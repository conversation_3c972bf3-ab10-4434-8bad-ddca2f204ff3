using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System;
using static Gallop.StaticVariableDefine.Parts.OutlineCommon;

namespace Gallop
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(TextCommon))]
    /// <summary>文字アウトラインを作成</summary>
    [AddComponentMenu("gallop/2D/OutlineCommon")]
    public class OutlineCommon : Outline, IParentColorReciever
    {
        #region 定数

        // 重ね描画回数。重ければ小さくするが、4 だとクオリティ的に厳しい。
        public const int QUALITY = 8;

        // 初期化する頂点数
        private const int INITIALIZE_NUM = 32;

        // 頂点数定義
        private const int QUAD_VERTEX_NUM = 4;
        public const int TRINGLE_VERTEX_NUM = 6;

        // フェードイン/アウト中にアウトラインのαを文字色のα - ALPHA_ALIGNMENT_VALUEにする
        private const float ALPHA_ALIGNMENT_VALUE = 0.5f;

        #endregion

        #region プロパティ、SerializeField

        ///<summary> 親オブジェクトからのカラー変更の影響を受けるか </summary>
        public bool IsIgnoreParentColor
        {
            get => TextCommon.IsIgnoreParentColor;
            set => TextCommon.IsIgnoreParentColor = value;
        }

        /// <summary> 受け取りカラー </summary>
        public Color recieveColor { get; set; } = Color.white;

        public Color color
        {
            get => effectColor;
            set => effectColor = value;
        }
        
        [SerializeField]
        private bool _useGraphicRedAsAlpha = false;

        /// <summary>
        /// アウトラインの位置をずらしたい場合に利用: 下に2pxずらすなら(0, -2)を設定
        /// </summary>
        [SerializeField]
        protected Vector2 _outlineOffset = Math.VECTOR2_ZERO;
        public Vector2 OutlineOffset
        {
            get { return _outlineOffset; }
            set { _outlineOffset = value; }
        }

        /// <summary>
        /// 親オブジェクトのαに連動してαを変化させるか
        /// フェードアウトするときアウトラインが残って見えるのを回避するために使用
        /// </summary>
        [SerializeField] 
        private bool _isSyncAlphaWithParent = false;

        ///<summary> デフォルトで設定された色 </summary>
        public Color DefaultColor
        {
            get { return _defaultColor; }
            set
            {
                _defaultColor = value;
                UpdateColor();
            }
        }

        /// <summary>
        /// 掛け合わせる色
        /// </summary>
        public Color MulColor { get; private set; } = Color.white;

        private TextCommon TextCommon
        {
            get
            {
                if (_textCommon == null)
                {
                    _textCommon = GetComponent<TextCommon>();
                }
                return _textCommon;
            }
        }

        #endregion

        #region private変数
        
        private Vector2[] _effectDistanceTableArray;

        private Color _defaultColor = Color.white;

        // Awake済みかどうか
        private bool _isSetupedDefaultColor = false;

        private TextCommon _textCommon = null;

        #endregion

        #region メソッド

        /// <summary>
        /// OutlintQualityを考慮した頂点数を取得
        /// </summary>
        /// <param name="indexNum"></param>
        /// <returns></returns>
        private static int GetOutlineVertexNum(int num) => QUALITY * (QUAD_VERTEX_NUM * (num / TRINGLE_VERTEX_NUM));

        /// <summary>
        /// OutlineQualityを考慮した頂点インデックス数を取得
        /// </summary>
        /// <param name="indexNum"></param>
        /// <returns></returns>
        private static int GetOutlineIndicesNum(int num) => (1 + QUALITY) * num;  //文字本体分が必要なので+1

        /// <summary>
        /// Awake
        /// </summary>
        protected override void Awake()
        {
            //初期バッファ生成
            if(_tempOutlineUIVertexArray == null)
                _tempOutlineUIVertexArray = new UIVertex[GetOutlineVertexNum(INITIALIZE_NUM)];

            if(_tempOutlineIndeciesArray == null)
                _tempOutlineIndeciesArray = new int[GetOutlineIndicesNum(INITIALIZE_NUM)];

            base.Awake();
            SetupDefaultColor();
        }


        protected override void OnDestroy()
        {
            _textCommon = null;
            _effectDistanceTableArray = null;
            base.OnDestroy();
        }

        /// <summary>
        /// DefaultColorを準備する
        /// UpdateColorを呼ぶ前に呼ぶ必要がある
        /// </summary>
        public void SetupDefaultColor()
        {
            SetupDefaultColor(effectColor);
        }
        
        /// <summary>
        /// DefaultColorを準備する
        /// UpdateColorを呼ぶ前に呼ぶ必要がある
        /// </summary>
        public void SetupDefaultColor(Color defaultColor)
        {
            if (_isSetupedDefaultColor)
            {
                return;
            }

            _defaultColor = defaultColor;
            _isSetupedDefaultColor = true;
        }

        /// <summary>
        /// 頂点バッファとっておく
        /// </summary>
        /// <param name="buffer"></param>
        private void ReserveVertexBuffer(int buffer)
        {
            //バッファの最低保証
            if(buffer <= INITIALIZE_NUM)
            {
                buffer = INITIALIZE_NUM;
            }

            //インデックスバッファ生成
            if (_indeciesList == null)
            {
                _indeciesList = new List<int>(GetOutlineIndicesNum(buffer));
            }

            if (_effectDistanceTableArray == null)
            {
                _effectDistanceTableArray = new Vector2[QUALITY];
            }

            int num = _effectDistanceTableArray.Length;
            for (int i = 0; i < num; i++)
            {
                float rad = 2.0f * Mathf.PI * i / QUALITY;
                float x = effectDistance.x * Mathf.Cos(rad);
                float y = effectDistance.y * Mathf.Sin(rad);

                _effectDistanceTableArray[i].x = x + _outlineOffset.x;
                _effectDistanceTableArray[i].y = y + _outlineOffset.y;
            }
        }

        /// <summary>
        /// メッシュ調整
        /// </summary>
        /// <param name="vh"></param>
        public override void ModifyMesh(VertexHelper vh)
        {
            if (!IsActive())
            {
                return;
            }

            ReserveVertexBuffer(vh.currentVertCount);

            var vertList = TextUtil.GetUIVertexStream(vh);

            ModifyVertices(vertList);

            //内部で頂点インデックスリストもクリアされる
            TextUtil.SetUIVertexStream(vh, _indeciesList);

            // OutlineCommonで増やした頂点もRotateTextで回転させる。
            // OutlineCommon->RotateTextの順でModifyMeshが呼び出される必要があるが、
            // 呼び出し順を制御することができないため、手動で呼び出している。
            {
                var rotateText = GetComponent<RotateText>();
                if (rotateText != null && rotateText.enabled)
                {
                    bool isForceModifh = rotateText.IsForceModifyMesh;
                    rotateText.IsForceModifyMesh = true;
                    {   
                        rotateText.ModifyMesh(vh);
                    }
                    rotateText.IsForceModifyMesh = isForceModifh;
                }
            }
        }

        /// <summary>
        /// Work処理をするためのインスタンスチェック
        /// </summary>
        /// <param name="vertexNum"></param>
        /// <param name="indexNum"></param>
        private void CheckWorkInstance(int vertexNum,int indexNum)
        {
#if UNITY_EDITOR
            //編集中ならnullがあり得る
            if (_tempOutlineUIVertexArray == null)
            {
                _tempOutlineUIVertexArray = new UIVertex[vertexNum];
            }

            if (_tempOutlineIndeciesArray == null)
            {
                _tempOutlineIndeciesArray = new int[indexNum];
            }
#endif

            if (_tempOutlineUIVertexArray.Length < vertexNum)
            {
                _tempOutlineUIVertexArray = new UIVertex[vertexNum];
            }

            if (_tempOutlineIndeciesArray.Length < indexNum)
            {
                _tempOutlineIndeciesArray = new int[indexNum];
            }

        }

        protected void ModifyVertices(List<UIVertex> vertList)
        {
            if (_indeciesList == null)
                return;

            int start = 0;
            int num = vertList.Count;

            if (num <= 0)
                return;

            int newVertexNum = QUAD_VERTEX_NUM * (num / TRINGLE_VERTEX_NUM);

            int vertexNum = GetOutlineVertexNum(num);
            int indexNum = GetOutlineIndicesNum(num);

            CheckWorkInstance(vertexNum, indexNum);

            var outlineVertex = _tempOutlineUIVertexArray;
            var outlineIndex = _tempOutlineIndeciesArray;

            Color32 newColor = effectColor;
            if (useGraphicAlpha)
            {
                var alpha = _useGraphicRedAsAlpha ? newColor.r : newColor.a;
                newColor.a = (byte)((alpha * vertList[0].color.a) / 255);
            }

            // 縦横にずらすのではなく、円形に配置したほうが綺麗になる
            //編集中に配列がまだ設定されていないことがある
            if (_effectDistanceTableArray != null)
            {
                int loopNum = _effectDistanceTableArray.Length < QUALITY ? _effectDistanceTableArray.Length : QUALITY;
                for (int n = 0; n < loopNum; n++)
                {
                    ApplyShadow(vertList, _effectDistanceTableArray[n], newColor, outlineVertex, (n * newVertexNum), outlineIndex, start);
                    start += num;
                }
            }

            //本来描画すべきテキストを最後に頂点処理させないといけないので、ここで調整する
            for (int i = 0; i < num; i++)
            {
                outlineIndex[start + i] = i;
            }

            const int CAPACITY_SCALE = 120;
            const int NORMALIZE_NUM = 100;
            if (vertList.Capacity < (vertList.Count + vertexNum))
            {
                //拡張される事が確定しているので拡張サイズを調整する
                vertList.Capacity = ((vertList.Count + vertexNum) * CAPACITY_SCALE) / NORMALIZE_NUM;
            }
            if(_indeciesList.Capacity < (_indeciesList.Count + indexNum))
            {
                //拡張される事が確定しているので拡張サイズを調整する
                _indeciesList.Capacity = ((_indeciesList.Count + vertexNum) * CAPACITY_SCALE) / NORMALIZE_NUM;
            }

            //structなのでメモリコピーとなる
            vertList.AddRange(new ArraySegment<UIVertex>(_tempOutlineUIVertexArray, 0, vertexNum));
            _indeciesList.AddRange(new ArraySegment<int>(outlineIndex, 0, indexNum));   //頂点インデックスだけを指定出来ればこの辺不要
        }

        /// <summary>
        /// 指定のOutlineTypeからカラーと大きさを自動設定する
        /// </summary>
        public void ApplyOutlineColor(Color32 outlineColor)
        {
            if (outlineColor.a > 0)
            {
                effectDistance = OUTLINE_EFFECT_DISTANCE;
                _defaultColor = outlineColor;
                useGraphicAlpha = true;
            }
            UpdateColor();
        }

        public void ApplySize(OutlineSizeType outlineSize)
        {
            effectDistance = ColorPreset.OutlineSizeDictionary[outlineSize];
        }

        protected void ApplyShadow(List<UIVertex> vertList, Vector2 position, Color newColor, UIVertex[] resultBuffer, int arrayStart, int[] indecies, int indexStart)
        {
            UIVertex vt0;
            UIVertex vt1;
            UIVertex vt2;
            UIVertex vt3;

            Vector3 offset;
            offset.x = position.x;
            offset.y = position.y;
            offset.z = 0.0f;

            int num = vertList.Count;
            int loopNum = vertList.Count / TRINGLE_VERTEX_NUM;
            int index = arrayStart;
            for (int i = 0; i < loopNum; ++i)
            {
                int vertIndex = i * TRINGLE_VERTEX_NUM;
                //頂点データが以下のように入っている
                /*
                 * 0-----1
                 * |     |
                 * |     |
                 * 4-----2
                 */
                vt0 = vertList[vertIndex + 0];
                vt1 = vertList[vertIndex + 1];
                vt2 = vertList[vertIndex + 2];
                vt3 = vertList[vertIndex + 4];

                vt0.position = vt0.position + offset;
                vt1.position = vt1.position + offset;
                vt2.position = vt2.position + offset;
                vt3.position = vt3.position + offset;

                vt0.color = newColor;
                vt1.color = newColor;
                vt2.color = newColor;
                vt3.color = newColor;

                resultBuffer[index] = vt0;
                resultBuffer[index + 1] = vt1;
                resultBuffer[index + 2] = vt2;
                resultBuffer[index + 3] = vt3;

                indecies[indexStart + 0] = num + index + 0;
                indecies[indexStart + 1] = num + index + 1;
                indecies[indexStart + 2] = num + index + 2;

                indecies[indexStart + 3] = num + index + 0;
                indecies[indexStart + 4] = num + index + 2;
                indecies[indexStart + 5] = num + index + 3;

                index += QUAD_VERTEX_NUM;
                indexStart += TRINGLE_VERTEX_NUM;
            }
        }

        /// <summary>
        /// 掛け合わせる色セット
        /// </summary>
        /// <param name="color"></param>
        public void SetMulColor(Color color)
        {
            MulColor = color;
            SetupDefaultColor();
            UpdateColor();
        }

        public void UpdateColor()
        {
            var color = _defaultColor * MulColor * recieveColor;
            if (color == effectColor)
            {
                //同一色なら端折る（代入するとModifyMeshが呼ばれてしまうので）
                return;
            }
            effectColor = color;
            
            SyncAlphaWithParent();
        }

        /// <summary>
        /// フェード時にαを親に連動して調整する処理の有効/無効を設定する
        /// </summary>
        /// <param name="isActive"></param>
        public void SetIsSyncAlphaWithParent(bool isActive)
        {
            _isSyncAlphaWithParent = isActive;
        }

        protected override void OnCanvasGroupChanged()
        {
            base.OnCanvasGroupChanged();
            SyncAlphaWithParent();
        }

        /// <summary>
        /// アウトラインのα値をテキストのα値に連動して調節する
        /// </summary>
        private void SyncAlphaWithParent()
        {
            if (!_isSyncAlphaWithParent) return;
            
            var color = effectColor;
            var textAlpha = TextCommon.canvasRenderer.GetInheritedAlpha();
            // テキストをフェードイン/アウトさせるときアウトラインが残って見えるためαを調整する
            color.a = textAlpha < 1f ? Mathf.Clamp01(textAlpha - ALPHA_ALIGNMENT_VALUE) : 1f;
            effectColor = color;
        }

        #endregion
    }
}
