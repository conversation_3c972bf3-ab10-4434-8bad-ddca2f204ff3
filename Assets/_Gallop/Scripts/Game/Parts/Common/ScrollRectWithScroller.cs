using UnityEngine;
using UnityEngine.EventSystems;

using ScrollDirection = Gallop.ScrollAndScrollerButton.ScrollDirection;

namespace Gallop
{
    /// <summary>
    /// ScrollRectとScrollerを併用したいときに使う
    /// 縦か横か判定してどちらのフリックを優先
    /// </summary>
    public class ScrollRectWithScroller : ScrollRectCommon
    {
        public SimpleScroller Scroller { get; set; } = null;

        private ScrollDirection _scrollDirection = ScrollDirection.None;
        private Vector2 _startPos;

        public override void OnBeginDrag(PointerEventData eventData)
        {
            // 開始時の距離との差分からスクローラーとスクロールどちらかを優先するために取っておく
            _startPos = eventData.position;
        }

        public override void OnDrag(PointerEventData eventData)
        {
            if (_scrollDirection == ScrollDirection.None)
            {
                var dist = eventData.position - _startPos;
                if (dist == Math.VECTOR2_ZERO)
                {
                    return;
                }

                if (Scroller != null && Mathf.Abs(dist.x) > Mathf.Abs(dist.y))
                {
                    _scrollDirection = ScrollDirection.Horizontal;
                    Scroller.OnBeginDrag(eventData);
                }
                else
                {
                    _scrollDirection = ScrollDirection.Vertical;
                    base.OnBeginDrag(eventData);
                }
            }
            
            if (Scroller != null && _scrollDirection == ScrollDirection.Horizontal)
            {
                Scroller.OnDrag(eventData);
            }
            else if (_scrollDirection == ScrollDirection.Vertical)
            {
                base.OnDrag(eventData);
            }
        }

        public override void OnEndDrag(PointerEventData eventData)
        {
            if (Scroller != null && _scrollDirection == ScrollDirection.Horizontal)
            {
                Scroller.OnEndDrag(eventData);
            }
            else if (_scrollDirection == ScrollDirection.Vertical)
            {
                base.OnEndDrag(eventData);
            }

            _scrollDirection = ScrollDirection.None;
        }
    }
}
