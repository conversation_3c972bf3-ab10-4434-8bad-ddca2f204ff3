using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// TATバッジ
    /// </summary>
    public class BadgeCommon : MonoBehaviour
    {
        const int MAX_NUM = 99;


        [SerializeField]
        private TweenAnimationTimelineComponent _tat;

        [SerializeField]
        private TextCommon _text;

        [SerializeField]
        private GameObject _objPlusIcon;

        public static BadgeCommon CreateBadge(Transform parent, Vector3 offset, ResourceManager.ResourceHash hash = ResourceManager.ResourceHash.Common)
        {
            var badge = CreateBadge(parent, hash);
            if (badge != null)
            {
                badge.transform.localPosition = offset;
            }
            return badge;
        }
        
        public static BadgeCommon CreateBadge(Transform parent, ResourceManager.ResourceHash hash = ResourceManager.ResourceHash.Common)
        {
            var load = ResourceManager.LoadOnHash<GameObject>(ResourcePath.BADGE_ICON_TAT_PATH, hash);
            if (load == null)
            {
                return null;
            }

            GameObject objBadgeIcon = GameObject.Instantiate(load, parent);
            if (objBadgeIcon == null)
            {
                return null;
            }

            var badgeCommon = objBadgeIcon.GetComponent<BadgeCommon>();

            // 表示
            badgeCommon.PlayTweenAnimationIn();

            return badgeCommon;
        }


        private void PlayTweenAnimationIn()
        {
            if(_tat != null)
            {
                _tat.Play("Tat_in");
            }
        }

        public void SetupBadgeNumText(int num)
        {
            _text.text = num.ToString();

            // 最大値を超えている場合は最大値表示
            if (num > MAX_NUM)
            {
                _text.text = MAX_NUM.ToString();
            }

            SetupPlusIcon(num);
        }

        private void SetupPlusIcon(int num)
        {
            // 表示最大値より大きければ表示
            _objPlusIcon.SetActiveWithCheck(num > MAX_NUM);
        }

    }
}