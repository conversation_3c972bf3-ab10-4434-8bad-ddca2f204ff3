using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// パーツ：絞り込みダイアログの内部パーツ
    /// </summary>
    public class PartsFilterLayoutUI : MonoBehaviour
    {
        [SerializeField]
        private Transform _groupRoot;
        
        [SerializeField]
        private Button<PERSON>ommon _resetButton;

        private SortFilterSetting _sortFilterSetting;
        private Dictionary<FilterMenuGroup, List<FilterMenu>> _menuDict;
        private Dictionary<FilterMenuGroup, IPartsFilterMenuGroup> _groupPartsDict;
        private Action<bool> _onFilterChanged;

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="sortFilterSetting"></param>
        /// <param name="checkCanExecFilter"></param>
        /// <param name="onFilterChanged"></param>
        /// <param name="resourceHash"></param>
        /// <param name="checkCanExecFilterEx"></param>
        public void Setup(SortFilterSetting sortFilterSetting, Func<SortFilterSetting, bool> checkCanExecFilter, Action<bool> onFilterChanged, ResourceManager.ResourceHash resourceHash, Action<SortFilterSetting> checkCanExecFilterEx = null)
        {
            if (sortFilterSetting == null)
            {
                Debug.LogError("不正な入力です");
                return;
            }

            _sortFilterSetting = sortFilterSetting;
            _onFilterChanged = onFilterChanged;

            // グループごとに分ける
            _menuDict = new Dictionary<FilterMenuGroup, List<FilterMenu>>();
            foreach (var menu in _sortFilterSetting.FilterChoices)
            {
                var group = menu.GetGroup();
                if (!_menuDict.ContainsKey(group))
                    _menuDict.Add(group, new List<FilterMenu>());

                _menuDict[group].Add(menu);
            }

            // グループごとにUI生成
            _groupPartsDict = new Dictionary<FilterMenuGroup, IPartsFilterMenuGroup>();
            foreach (var kv in _menuDict)
            {
                var clone = PartsFilterMenuGroupFactory.Create(kv.Key, _groupRoot, resourceHash);
                clone.Setup(kv.Key, kv.Value, _sortFilterSetting, () =>
                {
                    if (onFilterChanged == null || checkCanExecFilter == null) return;
                    
                    // 汎用因子が未設定になった場合、空いた設定を詰める更新
                    RefreshFactorCommon();

                    var newSetting = new SortFilterSetting(
                        _sortFilterSetting.IsAsc, _sortFilterSetting.Sort, _sortFilterSetting.SortChoices,
                        GetActiveMenuList(), _sortFilterSetting.FilterChoices, GetAdvanceFilter()
                    );
                    var canExecFilter = checkCanExecFilter.Invoke(newSetting);
                    onFilterChanged.Invoke(canExecFilter);

                    // 特殊判定があれば
                    if (canExecFilter)
                    {
                        checkCanExecFilterEx?.Invoke(newSetting);
                    }
                    
                    Refresh(newSetting);
                });
                _groupPartsDict.Add(kv.Key, clone);
            }

            // 汎用因子が他の汎用因子の設定情報を取得するためのリスト作成
            SetupFactorCommonLink();
            // 汎用因子のグループ単位でON/OFF更新
            RefreshFactorCommonVisible();

            _resetButton.SetOnClick(OnClickResetButton);
        }

        /// <summary>
        /// アクティブなメニューを取得
        /// </summary>
        /// <returns></returns>
        public List<FilterMenu> GetActiveMenuList(bool isIncludeToggle = true)
        {
            var list = new List<FilterMenu>();
            foreach (var kv in _groupPartsDict)
            {
                list.AddRange(kv.Value.GetActiveMenuList(isIncludeToggle));
            }

            return list;
        }

        /// <summary>
        /// 高度な絞り込み設定を取得
        /// </summary>
        /// <returns></returns>
        public SortFilterSetting.AdvancedFilterSetting GetAdvanceFilter()
        {
            var factorRarityMinStatus = GameDefine.FactorRarity.Rare1;
            var factorRarityMinProper = GameDefine.FactorRarity.Rare1;
            var factorRarityMinUnique = GameDefine.FactorRarity.Rare1;
            var needSuccessionFactorStatus = false;
            var needSuccessionFactorProper = false;
            var needSuccessionFactorUnique = false;
            var isUseFactorRarityFullMatchOption = false;
            var isFullMatchFactorRarityStatus = false;
            var isFullMatchFactorRarityProper = false;
            var isFullMatchFactorRarityUnique = false;
            var factorIdUnique = 0;
            List<int> factorIdCommonList = null;
            List<GameDefine.FactorRarity> factorRarityMinCommonList = null;
            List<bool> needSuccessionFactorCommonList = null;
            List<bool> isFullMatchFactorRarityCommonList = null;

            foreach (var kv in _groupPartsDict)
            {
                var group = kv.Key;
                var parts = kv.Value as PartsFilterMenuGroupAdvancedFactor;
                if(parts == null) continue;
                
                switch (parts.FilterMenuGroup)
                {
                    case FilterMenuGroup.FactorStatus:
                        factorRarityMinStatus = parts.GetFactorRarityMin();
                        needSuccessionFactorStatus = parts.GetNeedSuccessionFactor();
                        isFullMatchFactorRarityStatus = parts.GetIsFullMatchFactorRarity();
                        isUseFactorRarityFullMatchOption = parts.GetIsUseFullMatchOption();
                        break;
                    
                    case FilterMenuGroup.FactorProper:
                        factorRarityMinProper = parts.GetFactorRarityMin();
                        needSuccessionFactorProper = parts.GetNeedSuccessionFactor();
                        isFullMatchFactorRarityProper = parts.GetIsFullMatchFactorRarity();
                        break;
                    
                    case FilterMenuGroup.FactorUnique:
                        factorRarityMinUnique = parts.GetFactorRarityMin();
                        needSuccessionFactorUnique = parts.GetNeedSuccessionFactor();
                        factorIdUnique = parts.GetFactorCommonId();
                        isFullMatchFactorRarityUnique = parts.GetIsFullMatchFactorRarity();
                        break;
                }

                // 汎用因子
                if (parts.IsFactorCommon)
                {
                    if (factorIdCommonList == null) factorIdCommonList = new List<int>();
                    if (factorRarityMinCommonList == null) factorRarityMinCommonList = new List<GameDefine.FactorRarity>();
                    if (needSuccessionFactorCommonList == null) needSuccessionFactorCommonList = new List<bool>();
                    if (isFullMatchFactorRarityCommonList == null) isFullMatchFactorRarityCommonList = new List<bool>();
                    for (int i = 0; i < FilterMenuEx.FACTOR_COMMON_FILTER_MAX; i++)
                    {
                        if (factorIdCommonList.Count <= i) factorIdCommonList.Add(0);
                        if (factorRarityMinCommonList.Count <= i) factorRarityMinCommonList.Add(GameDefine.FactorRarity.Rare1);
                        if (needSuccessionFactorCommonList.Count <= i) needSuccessionFactorCommonList.Add(false);
                        if (isFullMatchFactorRarityCommonList.Count <= i) isFullMatchFactorRarityCommonList.Add(false);
                    }
                    var factorIndex = group.GetFactorCommonIndex();
                    factorIdCommonList[factorIndex] = parts.GetFactorCommonId();
                    factorRarityMinCommonList[factorIndex] = parts.GetFactorCommonRarityMin();
                    needSuccessionFactorCommonList[factorIndex] = parts.GetNeedSuccessionFactorCommon();
                    isFullMatchFactorRarityCommonList[factorIndex] = parts.GetIsFullMatchFactorRarity();
                }

            }
            
            return new SortFilterSetting.AdvancedFilterSetting(
                factorRarityMinStatus, factorRarityMinProper, factorRarityMinUnique,
                needSuccessionFactorStatus, needSuccessionFactorProper, needSuccessionFactorUnique, factorIdUnique,
                factorIdCommonList, factorRarityMinCommonList, needSuccessionFactorCommonList,
                isUseFactorRarityFullMatchOption,
                isFullMatchFactorRarityStatus, isFullMatchFactorRarityProper, isFullMatchFactorRarityUnique, isFullMatchFactorRarityCommonList
            );
        }

        /// <summary>
        /// 更新
        /// </summary>
        private void Refresh(SortFilterSetting sortFilterSetting)
        {
            foreach (var kv in _groupPartsDict)
            {
                kv.Value.UpdateMenu(sortFilterSetting);
            }
        }
        /// <summary>
        /// 汎用因子UI更新
        /// １が未設定の場合、２，３は非表示、２が未設定の
        /// </summary>
        private void RefreshFactorCommon()
        {
            var factorCommonParts = _groupPartsDict
                .Where(keyPair => keyPair.Value is PartsFilterMenuGroupAdvancedFactor { IsFactorCommon:true })
                .Select(keyPair => keyPair.Value as PartsFilterMenuGroupAdvancedFactor).ToList();
            for (int i = 0; i < factorCommonParts.Count-1; i++)
            {
                var now = factorCommonParts[i];
                var next = factorCommonParts[i+1];
                
                // 設定が空になった場合、後ろの要素の内容で詰める
                if (now.GetFactorCommonId() == 0 && next.GetFactorCommonId() != 0)
                {
                    for (int j = i; j < factorCommonParts.Count-1; j++)
                    {
                        var now1 = factorCommonParts[j];
                        var next1 = factorCommonParts[j+1];
                        now1.SetAdvancedFactorCommon(next1.GetFactorCommonId(), next1.GetFactorCommonRarityMin(), next1.GetIsFullMatchFactorRarity(), next1.GetNeedSuccessionFactor());
                    }
                    // 最後の設定を空にする
                    factorCommonParts[factorCommonParts.Count-1].SetAdvancedFactorCommon(0, GameDefine.FactorRarity.Rare1, false, false);
                }
            }

            if (factorCommonParts.Count > 0)
            {
                // グループ表示更新
                RefreshFactorCommonVisible();
            }
        }

        /// <summary>
        /// 汎用因子UIグループ単位のON/OFF更新
        /// １の設定があれば２が表示、２の設定があれば３を表示
        /// </summary>
        private void RefreshFactorCommonVisible()
        {
            var factorCommonParts = _groupPartsDict
                .Where(keyPair => keyPair.Value is PartsFilterMenuGroupAdvancedFactor { IsFactorCommon:true })
                .Select(keyPair => keyPair.Value as PartsFilterMenuGroupAdvancedFactor).ToList();

            for (int i = 0; i < factorCommonParts.Count-1; i++)
            {
                var now = factorCommonParts[i];
                var next = factorCommonParts[i+1];
                next.SetActiveWithCheck(now.GetFactorCommonId() != 0);
            }
        }

        /// <summary>
        /// 汎用因子のグループが、他の汎用因子のグループの情報を取得するためのリストを作成
        /// </summary>
        private void SetupFactorCommonLink()
        {
            var factorCommonParts = _groupPartsDict
                .Where(keyPair => keyPair.Value is PartsFilterMenuGroupAdvancedFactor { IsFactorCommon:true })
                .Select(keyPair => keyPair.Value as PartsFilterMenuGroupAdvancedFactor).ToList();
            
            foreach (var parts in factorCommonParts)
            {
                var list = factorCommonParts.Where(parts2 => parts2.FilterMenuGroup != parts.FilterMenuGroup).ToList();
                parts.SetupFactorCommonLink(list);
            }
        }

        /// <summary>
        /// ボタン押下：絞り込みリセット
        /// </summary>
        private void OnClickResetButton()
        {
            foreach (var kv in _groupPartsDict)
            {
                kv.Value.Reset();
            }
            // 汎用因子グループ表示更新
            RefreshFactorCommonVisible();

            _onFilterChanged.Invoke(true);
            
            var newSetting = new SortFilterSetting(
                _sortFilterSetting.IsAsc, _sortFilterSetting.Sort, _sortFilterSetting.SortChoices,
                GetActiveMenuList(), _sortFilterSetting.FilterChoices, GetAdvanceFilter()
            );
            Refresh(newSetting);
        }

        private void OnDestroy()
        {
            if (!_groupPartsDict.IsNullOrEmpty())
            {
                // Resources.UnloadUnusedAssetsで開放させるため、動的に生成したToggleWithTextのIconの参照を消す
                foreach (var pair in _groupPartsDict)
                {
                    var filterMenuGroup = pair.Value;
                    if (filterMenuGroup != null)
                    {
                        filterMenuGroup.Release();
                    }
                }

                Resources.UnloadUnusedAssets();
            }
        }
    }
}