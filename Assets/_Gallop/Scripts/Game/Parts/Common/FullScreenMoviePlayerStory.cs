using CriWare.CriMana;
using UnityEngine;
using Cute.Cri;

namespace Gallop
{
    /// <summary>
    /// 会話シーンで使う全画面用ムービープレイヤー
    /// </summary>
    public class FullScreenMoviePlayerStory : FullScreenMoviePlayer
    {
        /// <summary>
        /// プロローグの透過型ムービーのアスペクト比（幅 / 縦）
        /// 固定で 1600 x 720 とする
        /// </summary>
        private const float PROLOGUE_MOVIE_ASPECT_RATIO = 1600f / 720f;
        private const float PROLOGUE_MOVIE_ASPECT_RATIO_VERTICAL = 720f / 1600f;

        /// <summary>
        /// 横画面会話で端末を縦持ちした時のViewport
        /// </summary>
        private readonly RectTransform _portraitViewportRect;

        /// <summary>
        /// 高アスペクト比端末の時にスクリーンサイズを補正するか
        /// </summary>
        private readonly bool _isAdjustForHighAspectRatio;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public FullScreenMoviePlayerStory(bool isAdditive, RectTransform portraitViewportRect, bool isAdjustForHighAspectRatio)
        {
            // 加算モードが有効の場合は、ここで設定する。
            // 親クラスのコンストラクタで _handle が設定されるので、この時点で設定して良い。
            if (isAdditive)
            {
                MovieManager.Instance.SetAdditiveMode(_handle, true);
            }

            _portraitViewportRect = portraitViewportRect;
            _isAdjustForHighAspectRatio = isAdjustForHighAspectRatio;
        }

        /// <summary>
        /// 初期パラメーターをセット
        /// </summary>
        protected override void SetDefaultParameter()
        {
            _parameter = new Parameter
            {
                IsLoop = false,
                EndType = Cute.Cri.Movie.PlayEndType.Pause,
            };
        }

        /// <summary>
        /// セーブデータからVolumeを設定する (会話のムービー専用の紐付け)
        /// </summary>
        protected override void ApplyVolume()
        {
            // 主ボリュームにSEを
            // サブボリュームにVoiceを紐付ける
            var movieMgr = MovieManager.Instance;
            movieMgr.SetVolume(_handle, AudioManager.GetVolume(AudioManager.Category.SE));
            movieMgr.SetSubAudioVolume(_handle, AudioManager.GetVolume(AudioManager.Category.VOICE));
        }

        protected override void OnPlayDerived()
        {
            base.OnPlayDerived();

            AdjustScreenSize();
        }

        /// <summary>
        /// スクリーンサイズを画面に合わせて調整
        /// </summary>
        public void AdjustScreenSize()
        {
            var rectTransform = Transform as RectTransform;
            if (rectTransform == null) return;

            switch (StoryTimelineController.CurrentDisplayMode)
            {
                case StoryTimelineController.DisplayMode.Landscape:
                    // 回転処理が終わっていない段階で呼び出された場合は、WidthとHeightが逆になっている可能性があるため、
                    // Landscapeモードの場合は強制的に小さい方をHeight, 大きい方をWidthとして扱う
                    int screenHeight = Mathf.Min(Screen.Height, Screen.Width);
                    int screenWidth = Mathf.Max(Screen.Height, Screen.Width);
                    var mainCanvasRectSize = (UIManager.MainCanvas.transform as RectTransform).rect;

                    if (screenHeight < screenWidth * GameDefine.BASE_ASPECT_RATIO_LANDSCAPE)
                    {
                        // 横幅が広いので調整（縦を拡大し、上下が切れる形にする）
                        // Androidでタイミングよく画面回転するとTransformのwidthが不正に大きな値になってしまうので、MainCanvasのwidthを参照する
                        var width = mainCanvasRectSize.width;
                        var height = width * GameDefine.BASE_ASPECT_RATIO_LANDSCAPE;
                        rectTransform.anchorMin = new Vector2(0, 0.5f);
                        rectTransform.anchorMax = new Vector2(1, 0.5f);
                        rectTransform.pivot = Math.VECTOR2_HALF;
                        rectTransform.sizeDelta = new Vector2(0, height);
                        rectTransform.localPosition = Math.VECTOR3_ZERO;
                    }
                    else
                    {
                        //#88469対応、縦向きに変換されてからここに来る場合（iphone8や4:3画面の場合）,
                        //rectTransformのheightとMainCanvasのheightから大きいサイズを使ってWidthを計算する
                        // 縦幅が広いので調整（横を拡大し、左右が切れる形にする）
                        var width = Mathf.Max(rectTransform.rect.height, mainCanvasRectSize.height) * GameDefine.BASE_ASPECT_RATIO;
                        rectTransform.anchorMin = new Vector2(0.5f, 0);
                        rectTransform.anchorMax = new Vector2(0.5f, 1);
                        rectTransform.pivot = Math.VECTOR2_HALF;
                        rectTransform.sizeDelta = new Vector2(width, 0);
                        rectTransform.localPosition = Math.VECTOR3_ZERO;
                    }
                    break;
                case StoryTimelineController.DisplayMode.Portrait:
                    rectTransform.anchorMin = _portraitViewportRect.anchorMin;
                    rectTransform.anchorMax = _portraitViewportRect.anchorMax;
                    rectTransform.pivot = _portraitViewportRect.pivot;
                    rectTransform.sizeDelta = _portraitViewportRect.sizeDelta;
                    rectTransform.position = _portraitViewportRect.position;
                    break;

                case StoryTimelineController.DisplayMode.SingleModePrologue:
                    {
                        // 縦幅を調整。横幅をベースに縦を拡縮する。上下が切れる or 余白が出来る形に。
                        // #109548追記:
                        // 実装当初は背景を透過しているMovieしかなかったのでこれでよかったのだが
                        // グラマス編で全画面不透明のMovieが登場して問題が出たので21:9端末向けの調整を行う
                        AdjustScreenSize_Prologue(rectTransform);
                    }
                    break;
            }

            // RawImageのuvRectがズレるので、初期化や切り替えの際に補正しておく
            var rawImage = Transform.GetComponent<UnityEngine.UI.RawImage>();
            rawImage.uvRect = new Rect(0, 0, 1, 1);
        }

        /// <summary>
        /// 育成プロローグのロゴ向けにスクリーンサイズを調整
        /// </summary>
        /// <remarks>
        /// <para>#109548対応メモ:
        /// 育成ロゴのMovie素材はサイズが1600x720なので19.5:9端末までなら足りるが21:9端末だと高さが不足して上下が切れてしまう.
        /// 実装当初は背景が透過するMovieしかなかったので特に支障なかったが,
        /// グラマス編で全画面不透明のMovieが登場したことにより問題が表面化した.
        ///-----
        /// #135749対応メモ：
        /// _isAdjustForHighAspectRatioにチェックを入れていてもiPhone15（2556×1179）で左右の見切れが発生するのが発覚した
        /// 画面のアスペクト比は動画のアスペクト比との比較を行い、動画より画面のほうが縦長の比率の場合に、上下はアンカー任せで幅を調整する
        /// </para>
        /// </remarks>
        private void AdjustScreenSize_Prologue(RectTransform rectTransform)
        {
            // フラグ指定があった場合のみ調整を行う
            // 動画サイズの1600:720より縦長の比率の端末かどうか
            float screenAspect = GallopUtil.GetScreenAspect() / PROLOGUE_MOVIE_ASPECT_RATIO_VERTICAL;
            if (_isAdjustForHighAspectRatio && screenAspect < 1f)
            {
                // 動画サイズの1600:720より縦長の端末では通常とは逆で縦幅をベースに横幅を調整する
                var width = rectTransform.rect.height / PROLOGUE_MOVIE_ASPECT_RATIO;
                rectTransform.anchorMin = new Vector2(0.5f, 0f);
                rectTransform.anchorMax = new Vector2(0.5f, 1f);
                rectTransform.sizeDelta = new Vector2(width, 0f);
            }
            else
            {
                // 通常時は横幅をベースに縦幅を調整する
                var height = rectTransform.rect.width * PROLOGUE_MOVIE_ASPECT_RATIO;
                rectTransform.anchorMin = new Vector2(0f, 0.5f);
                rectTransform.anchorMax = new Vector2(1f, 0.5f);
                rectTransform.sizeDelta = new Vector2(0f, height);
            }

            rectTransform.pivot = Math.VECTOR2_HALF;
            rectTransform.localPosition = Math.VECTOR3_ZERO;
        }
    }
}
