namespace Gallop
{
    /// <summary>
    /// トレーナーガイドの定義クラス
    /// </summary>
    public class RaceFitAssistanceDefine
    {
        /// <summary>
        /// トレーナーガイドを対象のイベントタイプ
        /// </summary>
        public enum TargetEventType
        {
            None = 0,
            Champions = 1,
            Heroes = 2,
            UltimateRace = 3
        }

        /// <summary>
        /// ユーザーに表示する更新状況 0:更新なし 1:更新あり 2:新規イベント
        /// </summary>
        public enum ChangeType
        {
            UnChanged = 0,
            Changed = 1,
            Added = 2
        }
        
        /// <summary>
        /// 推奨度合い 0:設定なし 1:おすすめ 2:超おすすめ
        /// </summary>
        public enum RecommendedType
        {
            None = 0,
            Recommended = 1,
            VeryRecommended = 2
        }

        /// <summary>
        /// イベントが開催前か開催中か開催終了か
        /// </summary>
        public enum EventStatus
        {
            NotStarted, // 開催していない
            Ongoing, // 開催中
            Finished // 開催終了
        }

        /// <summary> おすすめ継承ウマ娘の最大表示数 </summary>
        public const int SUCCESSION_CARD_MAX_COUNT = 5;
    }
}