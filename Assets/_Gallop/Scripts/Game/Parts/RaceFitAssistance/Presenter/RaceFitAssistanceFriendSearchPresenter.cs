using System;
using System.Collections.Generic;
using System.Linq;
using Gallop.Tutorial;

namespace Gallop
{
    /// <summary>
    /// トレーナーガイドのフレンド検索関連を制御するクラス
    /// </summary>
    public class RaceFitAssistanceFriendSearchPresenter
    {
        #region フレンド検索確認ダイアログ関連

        /// <summary>
        /// フレンド検索確認ダイアログを開く
        /// </summary>
        public static void OpenFriendSearchConfirm(SingleModeStartViewController.EntryInfo entryInfo,
            TargetRaceFitAssistanceModel targetModel, Action onSearch)
        {
            if (!GameDefine.IS_OPEN_RACE_FIT_ASSISTANCE)
            {
                return;
            }

            if (TutorialManager.IsTutorialExecuting())
            {
                return;
            }

            if (ExistRecommendedSuccessionCard(entryInfo, targetModel))
            {
                return;
            }

            var dialogInfo = new DialogRaceFitAssistanceFriendSearchConfirmInfo(
                entryInfo,
                targetModel.TargetRaceFitAssistanceEventInfo,
                onSearch: onSearch
            );
            DialogRaceFitAssistanceFriendSearchConfirm.Open(dialogInfo);
        }

        /// <summary>
        /// 殿堂入りウマ娘に脚質に合ったおすすめ継承ウマ娘が存在するか判定
        /// </summary>
        private static bool ExistRecommendedSuccessionCard(SingleModeStartViewController.EntryInfo entryInfo,
            TargetRaceFitAssistanceModel targetModel)
        {
            // 自前の殿堂入りウマ娘
            var allTrainedCharaIdList = WorkDataManager.Instance.TrainedCharaData.List.Select(data => data.CardId).ToList();
            // レンタル殿堂入りウマ娘
            if (entryInfo.RentalTrainedCharaArray != null && entryInfo.RentalTrainedCharaArray.Any())
            {
                allTrainedCharaIdList.AddRange(entryInfo.RentalTrainedCharaArray.Select(data => data.CardId));
            }
            // イベント殿堂入りウマ娘
            if (entryInfo.EventRentalTrainedCharaArray != null && entryInfo.EventRentalTrainedCharaArray.Any())
            {
                allTrainedCharaIdList.AddRange(entryInfo.EventRentalTrainedCharaArray.Select(data => data.CardId));
            }

            // 収集した殿堂入りウマ娘リストに、脚質に合ったおすすめ継承ウマ娘が１人でも存在しているか確認
            var successionCardList = targetModel.TargetRaceFitAssistanceEventInfo.RaceFitAssistInfo.RecommendedSuccessionCardIdList
                .Where(x => RaceFitAssistanceUtil.IsTargetAssist(x, targetModel.TargetRaceFitAssistanceEventInfo.RunningStyle))
                .ToList();
            var listCount = successionCardList.Count;
            for (int i = 0; i < listCount; ++i)
            {
                if (allTrainedCharaIdList.Any(id => id == successionCardList[i].CardId))
                {
                    return true;
                }
            }

            return false;
        }

        #endregion フレンド検索確認ダイアログ関連

        #region おすすめフレンド選択ダイアログ関連

        /// <summary>
        /// おすすめフレンド選択ダイアログを開く
        /// </summary>
        public static void OpenFriendSelect(SingleModeStartViewController.EntryInfo entryInfo,
            Action<(UserInfoAtFriend friendUserInfo, WorkTrainedCharaData.TrainedCharaData trainedCharaData, Action onCloseDialog)> onNext)
        {
            if (!GameDefine.IS_OPEN_RACE_FIT_ASSISTANCE)
            {
                return;
            }

            if (TutorialManager.IsTutorialExecuting())
            {
                return;
            }

            RequestRecommendFriend(entryInfo, () =>
            {
                FriendLoad(() =>
                {
                    var dialogInfo = new DialogRaceFitAssistanceFriendSelectInfo(
                        entryInfo,
                        onFollowAndRental: (viewerId, friendUserInfo, trainedCharaData, friendListItemInfo, isFollowerCountExceeded, onCloseOtherDialog) =>
                        {
                            OnFollowAndRental(viewerId, friendUserInfo, trainedCharaData, friendListItemInfo, isFollowerCountExceeded,
                                onCloseOtherDialog, onNext);
                        });
                    DialogRaceFitAssistanceFriendSelect.Open(dialogInfo);
                });
            });
        }

        /// <summary>
        /// フレンド読み込み
        /// </summary>
        public static void FriendLoad(Action onSuccess)
        {
            var req = new FriendLoadRequest();
            req.Send(res =>
                {
                    // 通信成功
                    WorkDataManager.Instance.FriendData.UpdateFriendData(res);
                    onSuccess();
                });
        }

        /// <summary>
        /// おすすめフレンド情報を取得するリクエスト処理
        /// </summary>
        public static void RequestRecommendFriend(SingleModeStartViewController.EntryInfo entryInfo,
            Action onSuccess)
        {
            var req = new FriendSearchWithTrainerGuideRequest
            {
                card_ids_for_exclude = GetExcludeCardIdArray(),
            };
            req.Send(res =>
            {
                // 通信成功
                WorkDataManager.Instance.RaceFitAssistanceData.Apply(res.data);
                onSuccess();
            });

            return;

            // 除外するカードID一覧を取得
            int[] GetExcludeCardIdArray()
            {
                var excludeCardIdList = new List<int>
                {
                    entryInfo.GetMasterCardData().Id,
                };

                var firstSuccessionTrainedCharaData = entryInfo.GetSuccessionTrainedCharaData(true);
                if (firstSuccessionTrainedCharaData != null)
                {
                    excludeCardIdList.Add(firstSuccessionTrainedCharaData.CardId);
                }

                var secondSuccessionTrainedCharaData = entryInfo.GetSuccessionTrainedCharaData(false);
                if (secondSuccessionTrainedCharaData != null)
                {
                    excludeCardIdList.Add(secondSuccessionTrainedCharaData.CardId);
                }

                return excludeCardIdList.ToArray();
            }
        }

        /// <summary>
        /// 「フォローしてレンタル」ボタンをクリックした際のコールバック
        /// </summary>
        /// <param name="viewerId"></param>
        /// <param name="friendUserInfo"></param>
        /// <param name="trainedCharaData"></param>
        /// <param name="friendListItemInfo"></param>
        /// <param name="isFollowerCountExceeded">対象のフォロワー数が上限を越えているか</param>
        /// <param name="onCloseDialog"></param>
        /// <param name="onNext"></param>
        private static void OnFollowAndRental(long viewerId, UserInfoAtFriend friendUserInfo,
            WorkTrainedCharaData.TrainedCharaData trainedCharaData, FriendListItem.FriendListItemInfo friendListItemInfo, bool isFollowerCountExceeded,
            Action onCloseDialog, Action<(UserInfoAtFriend friendUserInfo, WorkTrainedCharaData.TrainedCharaData trainedCharaData, Action onCloseDialog)> onNext)
        {
            // フォローMAXならエラーダイアログ表示
            // (フレンド画面を経由していない場合Workのフレンドリストがない。その場合はサーバー側でエラーコードを返してエラー表示する）
            if (WorkDataManager.Instance.UserData.MaxFollowNum <= WorkDataManager.Instance.FriendData.GetFollowList().Count)
            {
                var code = GallopResultCode.FRIEND_FOLLOW_COUNT_OVER_ERROR;
                var header = MasterDataManager.Instance.masterString.GetText(MasterString.Category.ErrorHeader, code);
                var message = MasterDataManager.Instance.masterString.GetText(MasterString.Category.ErrorMessage, code);
                DialogManager.PushErrorCommon(message, header, null, GallopResultCode.OpenErrorPopupType.ShowOnly);
                return;
            }

            // 対象のフォロワー数が上限を越えていない場合
            if (!isFollowerCountExceeded)
            {
                var req = new FriendFollowRequest
                {
                    friend_viewer_id = viewerId
                };
                req.Send(res =>
                {
                    WorkDataManager.Instance.FriendData.NewFollow(friendUserInfo, res.data.friend_data, trainedCharaData.SuccessionCharaList);
                    WorkDataManager.Instance.PracticeRaceData.UpdateFriendFollow(friendUserInfo.name, res.data.friend_data, trainedCharaData);
                    WorkDataManager.Instance.RoomMatchData.UpdateRoomUserFollowUnfollow(res.data.friend_data);
                    TempData.Instance.PracticeRaceData.IsChangeRentalList = true;
                    TempData.Instance.SingleModeData.IsFollowerChanged = true;
                    
                    // 選択直後に元々リストに表示されていた継承ウマ娘から変更された場合は通知ダイアログを表示
                    OpenSelectedCharaChange(trainedCharaData, friendListItemInfo);
                    
                    onNext.Call((friendUserInfo, trainedCharaData, onCloseDialog));
                });
            }
            else
            {
                // トレーナーフォローダイアログ表示（対象のフォロワー数が上限を越えている）
                OpenFriendFollowNotification(friendListItemInfo);
            }
        }

        /// <summary>
        /// フレンド情報検索
        /// </summary>
        public static void RequestFriendSearch(long viewerId, Action<WorkTrainedCharaData.TrainedCharaData> onSuccess)
        {
            var request = new FriendSearchRequest { friend_viewer_id = viewerId };
            request.Send(res =>
            {
                var trained = new WorkTrainedCharaData.TrainedCharaData(res.data.practice_partner_info);
                trained.SetOwnerViewerId(viewerId); // owner_viewer_idが0で返ってくるのでviewerIdを割り当てる
                onSuccess(trained);
            }, stallOneSecond: true);
        }

        #endregion おすすめフレンド選択ダイアログ関連

        #region フォロー確認ダイアログ関連

        /// <summary>
        /// フォロー確認ダイアログを開く
        /// </summary>
        /// <param name="friendListItemInfo"></param>
        /// <param name="onClose"></param>
        private static void OpenFriendFollowNotification(FriendListItem.FriendListItemInfo friendListItemInfo, Action onClose = null)
        {
            if (!GameDefine.IS_OPEN_RACE_FIT_ASSISTANCE)
            {
                return;
            }

            if (TutorialManager.IsTutorialExecuting())
            {
                return;
            }

            DialogRaceFitAssistanceFriendFollowNotification.Open(friendListItemInfo, onClose);
        }

        #endregion フォロー確認ダイアログ関連

        #region 代表ウマ娘変更通知

        /// <summary>
        /// 選んだ継承ウマ娘が元々表示されていたものから変更された場合にダイアログを表示する
        /// </summary>
        /// <param name="trainedCharaData"></param>
        /// <param name="friendListItemInfo"></param>
        private static void OpenSelectedCharaChange(WorkTrainedCharaData.TrainedCharaData trainedCharaData,
            FriendListItem.FriendListItemInfo friendListItemInfo)
        {
            // カードIDと作成日時どちらかが一致していない場合はダイアログを表示
            var isMatchCardId = trainedCharaData.CardId == friendListItemInfo.FriendData.VirtualTrainedCharaData.CardId;
            var isMatchCreateTime = trainedCharaData.CreateTime ==
                                    friendListItemInfo.FriendData.VirtualTrainedCharaData.CreateTime;
            if (!isMatchCardId || !isMatchCreateTime)
            {
                var data = new DialogCommon.Data();
                data.SetSimpleOneButtonMessage(TextId.RaceFitAssisrance648065.Text(),
                    TextId.RaceFitAssisrance648066.Text(),
                    (dialog) => { });
                DialogManager.PushSystemDialog(data);
            }
        }

        #endregion
    }
}
