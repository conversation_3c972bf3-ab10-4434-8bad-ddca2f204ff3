using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ゲーム内static変数定義：トレーナーガイド
    /// </summary>
    public partial class StaticVariableDefine
    {
        public class RaceFitAssistance
        {
            /// <summary>
            /// トレーナーガイド解除リクエスト用のインスタンス
            /// 必要な情報はAssistanceId,EventSubId,RunningStyleのみだが処理共通化のためいらない部分はすべて0で初期化
            /// </summary>
            public static TargetRaceFitAssistanceEventInfo ResetStatus => new();

            /// <summary>リストアイテムのロゴサイズ</summary>
            public static readonly Vector2 LIST_ITEM_LOGO_SIZE = new(312f, 168f);
            /// <summary>リストアイテムのロゴサイズ（リーグ オブ ヒーローズ）</summary>
            public static readonly Vector2 LIST_ITEM_LOGO_SIZE_HEROES = new(305f, 121f);
            /// <summary>リストアイテムのロゴサイズ（チャンピオンズミーティング）</summary>
            public static readonly Vector2 LIST_ITEM_LOGO_SIZE_CHAMPIONS = new(219f, 56f);
            /// <summary>リストアイテムのロゴ背景サイズ（チャンピオンズミーティング）</summary>
            public static readonly Vector2 LIST_ITEM_LOGO_BASE_SIZE_CHAMPIONS = new(217f, 217f);
            
            /// <summary> リストアイテムの大きさ(RaceFitAssistanceEventListItem) </summary>
            public static readonly Vector2 BUTTON_LIST_ITEM_SIZE = new Vector2(990, 202);
            public static readonly Vector2 LIST_ITEM_SIZE = new Vector2(984, 190);

            /// <summary> リストアイテムのロゴの座標(RaceFitAssistanceEventListItem) </summary>
            public static readonly Vector2 BUTTON_LIST_ITEM_LOGO_POS = new Vector2(-322, 5);
            public static readonly Vector2 LIST_ITEM_LOGO_POS = new Vector2(-324, 0);
        }
    }
}
