using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 目標レース選択ダイアログのリストアイテム
    /// </summary>
    [AddComponentMenu("")]
    public class RaceFitAssistanceEventListItem : LoopScrollItemBase
    {
        [SerializeField] [Header("タップ判定をとるコンポーネント")]
        private ButtonCommon _button = null;
        
        [SerializeField] [Header("ボタンの枠のイメージコンポーネント")]
        private ImageCommon _buttonFrameImage = null;

        [SerializeField] [Header("イベント、レースのロゴを表示するコンポーネント")]
        private RawImageCommon _logo = null;

        [SerializeField] [Header("イベント、レースのロゴを表示するサブコンポーネント")]
        private RawImageCommon _logoChild = null;

        [SerializeField] [Header("イベント名を表示するコンポーネント")]
        private TextCommon _raceEventNameText = null;

        [SerializeField]　[Header("レース情報を表示するコンポーネント")]
        private TextCommon _raceInfoText = null;

        [SerializeField, Header("コース環境情報を表示するコンポーネント")]
        private PartsEnvironmentConditions _environment;
        
        [Serial<PERSON><PERSON><PERSON>, Header("開催期間ラベルコンポーネントの親RectTransform")]
        private RectTransform _labelTransform;
        [SerializeField, Header("開催期間ラベルコンポーネント")]
        private TextCommon _labelText;

        [SerializeField, Header("トレーナーガイドラベル")]
        private RaceFitAssistanceStatusLabel _settingLabel;
        
        private const string BUTTON_IMAGE_ATLAS_NAME = "utx_frm_list_base_00_sl"; 
        private const string FRAME_IMAGE_ATLAS_NAME = "utx_frm_list_base_02_sl";
        
        /// <summary>
        /// スクロールアイテムのセットアップ
        /// </summary>
        /// <param name="targetRaceFitAssistanceEventInfo">表示するイベントレース情報</param>
        /// <param name="hash">ロードしたリソースを保存するhash値</param>
        /// <param name="onClick">スクロールアイテムをクリックしたときのコールバック</param>
        /// <param name="isCurrentSetting">現在選択中か？</param>
        /// <param name="isUpdateGuide">トレーナーガイド更新時かどうか</param>
        public void UpdateItem(TargetRaceFitAssistanceEventInfo targetRaceFitAssistanceEventInfo,
            ResourceManager.ResourceHash hash, System.Action onClick, bool isCurrentSetting = false, bool isUpdateGuide = false)
        {
            _labelTransform.SetActiveWithCheck(false); // ラベルは基本非表示にしておいて、必要に応じて後から設定する
            // トレーナーガイドのレース情報を所得
            var raceFitAssistanceInfo = targetRaceFitAssistanceEventInfo.RaceFitAssistInfo;

            // コールバックがなければボタンを非活性にする
            var isInteractable = onClick != null;
            
            // アイコンを設定
            SetupLogo(targetRaceFitAssistanceEventInfo, isInteractable, hash);
            // イベント名を設定
            _raceEventNameText.text = GetEventName(targetRaceFitAssistanceEventInfo.EventType);
            // レース条件を設定
            // イベントのRaceInstanceIdから参照するのではなく、RaceCourseSetIdから参照しないと管理画面側で設定したいものと乖離してしまう
            _raceInfoText.text =
                RaceUtil.GetCourseInfoTextLong(
                    MasterDataManager.Instance.masterRaceCourseSet.Get(raceFitAssistanceInfo.RaceCourseSetId));
            // コース環境情報を設定
            _environment.Setup(
                raceFitAssistanceInfo.Weather,
                (GameDefine.BgSeason)raceFitAssistanceInfo.Season,
                raceFitAssistanceInfo.GroundCondition,
                raceFitAssistanceInfo.Time
            );

            // ボタンのコールバックを設定
            _button.SetOnClick(onClick.Call);
            
            _button.SetEnable(isInteractable);

            // リストアイテム自体の大きさを変更
            var rectTransform = (this.gameObject.transform as RectTransform);

            // ボタンを押したときの挙動があるかないかで表示を変更する
            if (isInteractable)
            {
                _buttonFrameImage.sprite = UIManager.PreInAtlas.GetSprite(BUTTON_IMAGE_ATLAS_NAME);
                if (rectTransform != null)
                {
                    rectTransform.sizeDelta = StaticVariableDefine.RaceFitAssistance.BUTTON_LIST_ITEM_SIZE;   
                }
            }
            else
            {
                _buttonFrameImage.sprite = UIManager.PreInAtlas.GetSprite(FRAME_IMAGE_ATLAS_NAME);
                if (rectTransform != null)
                {
                    rectTransform.sizeDelta = StaticVariableDefine.RaceFitAssistance.LIST_ITEM_SIZE;
                }
            }
            
            // 設定中かどうかのラベル表示を更新
            UpdateSettingLabel(isCurrentSetting);
        }

        /// <summary>
        /// ロゴのセットアップ
        /// </summary>
        private void SetupLogo(TargetRaceFitAssistanceEventInfo targetInfo, bool isInteractable, ResourceManager.ResourceHash hash)
        {
            // 操作可能なリストアイテムかそうでないかでロゴの座標が変わる
            if (isInteractable)
            {
                _logo.rectTransform.anchoredPosition = StaticVariableDefine.RaceFitAssistance.BUTTON_LIST_ITEM_LOGO_POS;
            }
            else
            {
                _logo.rectTransform.anchoredPosition = StaticVariableDefine.RaceFitAssistance.LIST_ITEM_LOGO_POS;
            }
            
            switch (targetInfo.EventType)
            {
                case RaceFitAssistanceDefine.TargetEventType.Champions:
                    // チャンミのロゴ表示のみY座標が固定される
                    const float CHAMPOINS_LOGO_POS_Y = 0;
                    
                    _logoChild.SetActiveWithCheck(true);

                    _logoChild.texture = RaceFitAssistanceUtil.GetEventLogoChampions(targetInfo.RaceFitAssistInfo, hash);
                    _logoChild.rectTransform.sizeDelta = StaticVariableDefine.RaceFitAssistance.LIST_ITEM_LOGO_SIZE_CHAMPIONS;

                    _logo.texture = RaceFitAssistanceUtil.GetEventLogoBaseChampions(hash);
                    _logo.rectTransform.sizeDelta = StaticVariableDefine.RaceFitAssistance.LIST_ITEM_LOGO_BASE_SIZE_CHAMPIONS;
                    _logo.rectTransform.SetAnchoredPositionY(CHAMPOINS_LOGO_POS_Y);
                    break;
                case RaceFitAssistanceDefine.TargetEventType.Heroes:
                    _logoChild.SetActiveWithCheck(false);

                    _logo.texture = RaceFitAssistanceUtil.GetEventSelectListLogo(targetInfo, hash);
                    _logo.rectTransform.sizeDelta = StaticVariableDefine.RaceFitAssistance.LIST_ITEM_LOGO_SIZE_HEROES;
                    break;
                default:
                    _logoChild.SetActiveWithCheck(false);

                    _logo.texture = RaceFitAssistanceUtil.GetEventSelectListLogo(targetInfo, hash);
                    _logo.rectTransform.sizeDelta = StaticVariableDefine.RaceFitAssistance.LIST_ITEM_LOGO_SIZE;
                    break;
            }
        }

        /// <summary>
        /// リストアイテムの左上につけるラベルの設定
        /// </summary>
        /// <remarks>
        /// UpdateItemの後に呼び出すことが前提の作りになっています。
        /// 前に呼び出すと非表示になる
        /// </remarks>
        /// <param name="message"></param>
        /// <param name="width"></param>
        public void SetLabelMessage(string message,float width)
        {
            // ラベルによってフォントサイズが異なるようになっているため現状はPrefabで直接設定している
            // もっと使用機会が増えるならこの関数で設定できるようにするといいかもしれない
            if(string.IsNullOrEmpty(message))
            {
                _labelTransform.SetActiveWithCheck(false);
                return;
            }
            _labelTransform.SetActiveWithCheck(true);
            _labelText.text = message;
            _labelTransform.sizeDelta = new Vector2(width, _labelTransform.sizeDelta.y);
        }

        /// <summary>
        /// EventTypeに対応するイベント名を返す
        /// </summary>
        /// <param name="targetEventType"></param>
        /// <returns></returns>
        private string GetEventName(RaceFitAssistanceDefine.TargetEventType targetEventType)
        {
            return targetEventType switch
            {
                RaceFitAssistanceDefine.TargetEventType.Champions => TextId.Champions0034.Text(),
                RaceFitAssistanceDefine.TargetEventType.Heroes => TextId.Heroes408000.Text(),
                RaceFitAssistanceDefine.TargetEventType.UltimateRace => TextId.UltimateRace0001.Text(),
                _ => string.Empty
            };
        }

        /// <summary>
        /// 設定中かどうかのラベル表示を更新
        /// </summary>
        private void UpdateSettingLabel(bool isCurrentSetting)
        {
            _settingLabel.Initialize(isCurrentSetting);
        }
    }
}
