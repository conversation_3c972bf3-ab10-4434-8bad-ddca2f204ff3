using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 対象のトレーナーガイドが有効か、そうでないかを示すラベル
    /// </summary>
    [AddComponentMenu("")]
    public class RaceFitAssistanceStatusLabel : MonoBehaviour
    {
        #region SerializeField

        [SerializeField] [Header("ラベル用のイメージコンポーネント")]
        private ImageCommon _labelImage = null;
        
        [SerializeField] [Header("ラベル用のテキストコンポーネント")]
        private TextCommon _labelText = null;

        #endregion

        #region PrivateField

        /// <summary> ラベル表示制御クラス </summary>
        private RaceFitAssistanceStatusLabelDisplay _display;

        /// <summary> 自身のRectTransform </summary>
        private RectTransform _rectTransform = null;

        #endregion

        #region PublicMethod PartsSingleModeCommonHeaderから呼び出される

        /// <summary>
        /// 初期化処理
        /// </summary>
        public void Initialize(bool enable = true)
        {
            if (_display == null)
            {
                _display = new RaceFitAssistanceStatusLabelDisplay(_labelImage, _labelText);
            }
            if (_rectTransform == null)
            {
                _rectTransform = GetComponent<RectTransform>();
            }
            UpdateLabel(enable);
        }

        /// <summary>
        /// 表示更新
        /// </summary>
        public void Setup(bool enable = true)
        {
            if (_display == null || _rectTransform == null)
            {
                Initialize(enable);
            }
            else
            {
                UpdateLabel(enable);
            }
        }
        
        /// <summary>
        /// AnchoredPosition変更
        /// </summary>
        /// <param name="pos"></param>
        public void SetAnchoredPosition(Vector2 pos)
        {
            _rectTransform.anchoredPosition = pos;
        }
        
        /// <summary>
        /// Anchor基準の変更
        /// </summary>
        /// <param name="pos"></param>
        public void SetAnchorVector(Vector2 pos)
        {
            _rectTransform.anchorMax = pos;
            _rectTransform.anchorMin = pos;
        }
        
        // memo
        // #154895: Header側で親CanvasのSortingLayerを変更しているので、ここでのSortingLayer変更処理は実装しなくて良い

        /// <summary>
        /// 表示しているかどうか
        /// </summary>
        /// <returns></returns>
        public bool IsActive()
        {
            return this.gameObject.activeSelf;
        }

        #endregion

        #region PrivateMethod

        /// <summary>
        /// ラベル表示を更新する
        /// </summary>
        /// <remarks>
        /// トレーナーガイドの有効無効に応じてラベルを切り替える
        /// そもそも対象レースを設定していない場合は表示をしない
        /// </remarks>
        private void UpdateLabel(bool enable = true)
        {
            // 機能解放日時前なら何も起こらない
            GameDefine.UpdateOpenRaceFitAssistance();
            
            if (!enable || !GameDefine.IS_OPEN_RACE_FIT_ASSISTANCE)
            {
                this.gameObject.SetActive(false);
                return;
            }

            // 対象レースを設定していないならラベル自体を表示しない
            var targetModel = RaceFitAssistanceModelRepository.GetTargetModel();
            var isSettingRaceFitAssistance = targetModel != null;
            this.gameObject.SetActive(isSettingRaceFitAssistance);
            if (!isSettingRaceFitAssistance) return;

            // トレーナーガイドの有効無効に応じてラベルを切り替える
            var isEndRaceFitAssistance =
                RaceFitAssistanceUtil.IsEndRaceFitAssistance(targetModel.TargetRaceFitAssistanceEventInfo);
            _display.Setup(isEndRaceFitAssistance);
        }

        #endregion
    }
}