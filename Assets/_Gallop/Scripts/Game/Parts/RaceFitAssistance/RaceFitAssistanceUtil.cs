using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cute.UI;

namespace Gallop
{
    /// <summary>
    /// トレーナーガイドで共通して利用するロジックをまとめるクラス
    /// </summary>
    public static class RaceFitAssistanceUtil
    {
        private const int CHAMPIONS_ROUND_START_DATE = 1;

        // 10日以前は上旬で判定する
        private const int MONTH_EARY_BORDER = 10;

        // 10日より後で20以前は中旬で判定する
        private const int MONTH_MIDDLE_BORDER = 20;

        /// <summary>適正アラートの判定基準</summary>
        private const RaceDefine.ProperGrade PROPER_ALERT_THRESHOLD = RaceDefine.ProperGrade.C;

        #region イベントに対応するイベントアイコンを返すロジック

        /// <summary>
        /// トレーナーガイド情報からイベントのロゴを取得する
        /// </summary>
        public static Texture2D GetEventLogo(TargetRaceFitAssistanceEventInfo raceFitAssistanceInfo,
            ResourceManager.ResourceHash hash)
            => raceFitAssistanceInfo.EventType switch
            {
                RaceFitAssistanceDefine.TargetEventType.Champions => GetEventLogoChampions(
                    raceFitAssistanceInfo.RaceFitAssistInfo, hash),
                RaceFitAssistanceDefine.TargetEventType.UltimateRace => GetEventLogoUltimateRace(hash),
                RaceFitAssistanceDefine.TargetEventType.Heroes => GetEventLogoHeroes(hash),
                _ => GetEventLogoDefault()
            };

        /// <summary>
        /// switch式でエラー出しながらnullを返すために用意した関数
        /// </summary>
        /// <returns></returns>
        private static Texture2D GetEventLogoDefault()
        {
            Debug.LogError("想定されていないイベントタイプです");
            return null;
        }

        /// <summary>
        /// チャンミのイベントロゴを取得する
        /// </summary>
        /// <remarks>
        /// 芝、ダート、距離によって一意に決まるリソースIDを元にロゴを取得する
        /// </remarks>
        /// <param name="raceFitAssistanceInfo"></param>
        /// <param name="hash"></param>
        /// <returns></returns>
        public static Texture2D GetEventLogoChampions(RaceFitAssistanceInfo raceFitAssistanceInfo,
            ResourceManager.ResourceHash hash)
        {
            var resourceId = GetChampionsLogoResourceId(raceFitAssistanceInfo);

            return ResourceManager.LoadOnHash<Texture2D>(
                ResourcePath.GetChampionsLogoPath(resourceId, ChampionsDefines.LogoSize.L), hash);
        }

        /// <summary>
        /// チャンミのイベントロゴ背景を取得する
        /// </summary>
        public static Texture2D GetEventLogoBaseChampions(ResourceManager.ResourceHash hash)
        {
            return ResourceManager.LoadOnHash<Texture2D>(ResourcePath.GetChampionsLogoBasePath(), hash);
        }

        public static int GetChampionsLogoResourceId(RaceFitAssistanceInfo raceFitAssistanceInfo)
        {
            var groundType = (RaceDefine.GroundType)MasterDataManager.Instance.masterRaceCourseSet
                .Get(raceFitAssistanceInfo.RaceCourseSetId).Ground;

            const int SPRINT_ID = 13; // 短距離のリソースID
            const int MILE_ID = 14; // マイルのリソースID
            const int CLASSIC_ID = 15;　// 中距離のリソースID
            const int LONG_ID = 16; // 長距離のリソースID
            const int DIRT_ID = 17;　// ダートのリソースID
            int resourceId = SPRINT_ID;

            if (groundType == RaceDefine.GroundType.Turf)
            {
                var distanceType = GetCourseDistanceType(raceFitAssistanceInfo.RaceCourseSetId);
                resourceId = distanceType switch
                {
                    RaceDefine.CourseDistanceType.Short => SPRINT_ID,
                    RaceDefine.CourseDistanceType.Mile => MILE_ID,
                    RaceDefine.CourseDistanceType.Middle => CLASSIC_ID,
                    RaceDefine.CourseDistanceType.Long => LONG_ID,
                    _ => SPRINT_ID
                };
            }
            else if (groundType == RaceDefine.GroundType.Dirt)
            {
                resourceId = DIRT_ID;
            }
            else
            {
                Debug.LogError("想定されていないバ場タイプです");
            }

            return resourceId;
        }

        /// <summary>
        /// マスチャレのイベントロゴを取得する
        /// </summary>
        /// <param name="hash"></param>
        /// <returns></returns>
        private static Texture2D GetEventLogoUltimateRace(ResourceManager.ResourceHash hash)
        {
            return ResourceManager.LoadOnHash<Texture2D>(ResourcePath.ULTIMATE_RACE_LOGO_PATH, hash);
        }

        /// <summary>
        /// リグヒのイベントロゴを取得する
        /// </summary>
        /// <param name="hash"></param>
        /// <returns></returns>
        private static Texture2D GetEventLogoHeroes(ResourceManager.ResourceHash hash)
        {
            return ResourceManager.LoadOnHash<Texture2D>(ResourcePath.HEROES_LOGO_PATH, hash);
        }

        #endregion

        #region イベントに対応するRaceInstanceを返すロジック

        /// <summary>
        /// マスチャレのレースインスタンスを所得して返す
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="eventSubId"></param>
        /// <returns></returns>
        public static MasterRaceInstance.RaceInstance GetRaceInstanceUltimateRace(int eventId, int eventSubId)
        {
            var ultimateRaceCondition =
                MasterDataManager.Instance.masterUltimateRaceContents.GetWithEventIdAndGroupIdAndDifficulty(
                    eventId, eventSubId, 1);
            if(ultimateRaceCondition == null)
            {
                Debug.LogError("指定されたマスチャレのレースが見つかりません");
                return null;
            }
            return MasterDataManager.Instance.masterRaceInstance.Get(ultimateRaceCondition.RaceInstanceId);
        }

        #endregion

        #region アシスト用のデータが指定した脚質に対応しているかを返すロジック

        /// <summary>
        /// 与えられたassistDataが指定した脚質に対応しているかを返す
        /// </summary>
        /// <param name="raceFitAssistanceRunningStyleData"></param>
        /// <param name="runningStyle"></param>
        /// <returns></returns>
        public static RaceFitAssistanceDefine.RecommendedType GetRecommendedType(
            IRaceFitAssistanceRunningStyleRecommendation raceFitAssistanceRunningStyleData,
            RaceDefine.RunningStyle runningStyle)
        {
            var recommendedTypeInt = runningStyle switch
            {
                RaceDefine.RunningStyle.Nige => raceFitAssistanceRunningStyleData.RunningStyleNige,
                RaceDefine.RunningStyle.Senko => raceFitAssistanceRunningStyleData.RunningStyleSenko,
                RaceDefine.RunningStyle.Sashi => raceFitAssistanceRunningStyleData.RunningStyleSashi,
                RaceDefine.RunningStyle.Oikomi => raceFitAssistanceRunningStyleData.RunningStyleOikomi,
                _ => (int)RaceFitAssistanceDefine.RecommendedType.None,
            };

            // 定義チェック
            if (!Enum.IsDefined(typeof(RaceFitAssistanceDefine.RecommendedType), recommendedTypeInt))
            {
                Debug.LogError("想定されていない推奨度合いが設定されています");
                return RaceFitAssistanceDefine.RecommendedType.None;
            }

            return (RaceFitAssistanceDefine.RecommendedType)recommendedTypeInt;
        }

        /// <summary>
        /// 与えられたassistDataが指定した脚質に対応しているかを返す
        /// </summary>
        /// <param name="raceFitAssistanceRunningStyleData"></param>
        /// <param name="runningStyle"></param>
        /// <param name="threshold"> 判定閾値 </param>
        /// <returns></returns>
        public static bool IsTargetAssist(IRaceFitAssistanceRunningStyleRecommendation raceFitAssistanceRunningStyleData,
            RaceDefine.RunningStyle runningStyle,
            RaceFitAssistanceDefine.RecommendedType threshold = RaceFitAssistanceDefine.RecommendedType.None)
            => runningStyle switch
            {
                RaceDefine.RunningStyle.Nige => IsTargetRunningStyleNige(raceFitAssistanceRunningStyleData, (int)threshold),
                RaceDefine.RunningStyle.Senko => IsTargetRunningStyleSenko(raceFitAssistanceRunningStyleData, (int)threshold),
                RaceDefine.RunningStyle.Sashi => IsTargetRunningStyleSashi(raceFitAssistanceRunningStyleData, (int)threshold),
                RaceDefine.RunningStyle.Oikomi => IsTargetRunningStyleOikomi(raceFitAssistanceRunningStyleData, (int)threshold),
                _ => IsTargetRunningStyleDefault(),
            };
        
        /// <summary>
        /// switch式でエラー出しながらfalseを返すために用意した関数
        /// </summary>
        /// <returns></returns>
        private static bool IsTargetRunningStyleDefault()
        {
            Debug.LogError("想定されていない脚質指定です。");
            return false;
        }

        private static bool IsTargetRunningStyleNige(IRaceFitAssistanceRunningStyleRecommendation raceFitAssistanceRunningStyleData, int threshold)
        {
            return raceFitAssistanceRunningStyleData.RunningStyleNige > threshold;
        }

        private static bool IsTargetRunningStyleSenko(IRaceFitAssistanceRunningStyleRecommendation raceFitAssistanceRunningStyleData, int threshold)
        {
            return raceFitAssistanceRunningStyleData.RunningStyleSenko > threshold;
        }

        private static bool IsTargetRunningStyleSashi(IRaceFitAssistanceRunningStyleRecommendation raceFitAssistanceRunningStyleData, int threshold)
        {
            return raceFitAssistanceRunningStyleData.RunningStyleSashi > threshold;
        }

        private static bool IsTargetRunningStyleOikomi(IRaceFitAssistanceRunningStyleRecommendation raceFitAssistanceRunningStyleData, int threshold)
        {
            return raceFitAssistanceRunningStyleData.RunningStyleOikomi > threshold;
        }

        #endregion

        #region イベント選択リスト用のアイコンを取得するロジック

        /// <summary>
        /// イベント選択リスト用のアイコンを取得する
        /// チャンミとリグヒはイベントロゴを表示して、マスチャレはレースのサムネを表示する
        /// </summary>
        /// <param name="raceFitAssistanceInfo"></param>
        /// <param name="hash"></param>
        /// <returns></returns>
        public static Texture2D GetEventSelectListLogo(TargetRaceFitAssistanceEventInfo raceFitAssistanceInfo,
            ResourceManager.ResourceHash hash)
            => raceFitAssistanceInfo.EventType switch
            {
                RaceFitAssistanceDefine.TargetEventType.Champions => GetEventLogo(raceFitAssistanceInfo, hash),
                RaceFitAssistanceDefine.TargetEventType.UltimateRace => GetEventSelectListLogoUltimateRace(raceFitAssistanceInfo.EventId, raceFitAssistanceInfo.RaceFitAssistInfo.EventSubId, hash),
                RaceFitAssistanceDefine.TargetEventType.Heroes => GetEventLogo(raceFitAssistanceInfo, hash),
                _ => GetEventLogoDefault(),
            };

        /// <summary>
        /// マスチャレのレースインスタンスから対応するレースのサムネを所得して返す
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="eventSubId"></param>
        /// <param name="hash"></param>
        /// <returns></returns>
        private static Texture2D GetEventSelectListLogoUltimateRace(int eventId, int eventSubId,
            ResourceManager.ResourceHash hash)
        {
            // 万が一トレーナーガイドのイベント追加に併せてマスチャレのマスターが登録されていないとnullになってしまう
            var masterRaceInstance = GetRaceInstanceUltimateRace(eventId, eventSubId);
            if(masterRaceInstance == null)
            {
                Debug.LogError("指定されたマスチャレのレースが見つかりません");
                return null;
            }
            var raceThumbPath = ResourcePath.GetRaceThumbPath(masterRaceInstance.GetRaceMaster().ThumbnailId);
            var tex = ResourceManager.LoadOnHash<Texture2D>(raceThumbPath, hash);
            if (tex == null)
            {
                Debug.LogError("指定されたマスチャレのレースに対応するレースのサムネが見つかりません");
                return null;
            }

            return tex;
        }

        #endregion
        
        #region 対象のガイドレースが終了している場合trueを返すロジック

        public static bool IsEndRaceFitAssistance(TargetRaceFitAssistanceEventInfo targetRaceFitAssistanceEventInfo)
        {
            var isEnd = targetRaceFitAssistanceEventInfo != null && targetRaceFitAssistanceEventInfo.EndTime < TimeUtil.GetServerTimeStamp();
            return isEnd;
        }
        #endregion

        #region 指定されたテクスチャ名からAtlasのSpriteを所得して返すロジック
        public static Sprite GetSpriteFromAtlas(string spriteName)
        {
            var atlas = ResourceManager.LoadOnView<AtlasReference>(ResourcePath.GetAtlasPath(TargetAtlasType.RaceFitAssistance));
            if (atlas == null)
            {
                return null;
            }
            return atlas.GetSprite(spriteName);
        }

        #endregion
        
        #region おすすめ、超おすすめラベル表示関連ロジック
        /// <summary>
        /// おすすめ、超おすすめの文字列を返す
        /// </summary>
        /// <param name="recommendType"></param>
        /// <returns></returns>
        public static string GetRecommendMessage(RaceFitAssistanceDefine.RecommendedType recommendType)
        {
            return recommendType switch
            {
                RaceFitAssistanceDefine.RecommendedType.Recommended => "おすすめ", // StaticText置き換え
                RaceFitAssistanceDefine.RecommendedType.VeryRecommended => "超おすすめ", // StaticText置き換え
                _ => String.Empty,
            };
        }
        
        /// <summary>
        /// 引数で渡したCardIdのキャラが引数のRaceFitAssistanceEventInfoに対しておすすめか超おすすめかを返す
        /// </summary>
        /// <remarks>
        /// バ場、距離、脚質が全てB以上ならおすすめ、全てA以上なら超おすすめを返す
        /// </remarks>
        public static RaceFitAssistanceDefine.RecommendedType GetRecommendForEntryCard(int cardId, TargetRaceFitAssistanceEventInfo raceFitAssistance)
        {
            var cardData = WorkDataManager.Instance.CardData.GetCardData(cardId);

            var groundTypeProperGrade = GetGroundTypeProperGrade(raceFitAssistance.RaceFitAssistInfo.RaceCourseSetId, cardData);
            var distanceProperGrade = GetDistanceProperGrade(raceFitAssistance.RaceFitAssistInfo.RaceCourseSetId, cardData);
            var runningStyleProperGrade = GetRunningStyleProperGrade(raceFitAssistance.RunningStyle, cardData);

            // 超おすすめ（全てA以上）
            if (groundTypeProperGrade >= RaceDefine.ProperGrade.A &&
                distanceProperGrade >= RaceDefine.ProperGrade.A &&
                runningStyleProperGrade >= RaceDefine.ProperGrade.A)
            {
                return RaceFitAssistanceDefine.RecommendedType.VeryRecommended;
            }

            // おすすめ（全てB以上）
            if (groundTypeProperGrade >= RaceDefine.ProperGrade.B &&
                distanceProperGrade >= RaceDefine.ProperGrade.B &&
                runningStyleProperGrade >= RaceDefine.ProperGrade.B)
            {
                return RaceFitAssistanceDefine.RecommendedType.Recommended;
            }

            // 適性なし（C以下が含まれている）
            return RaceFitAssistanceDefine.RecommendedType.None;
        }

        /// <summary>
        /// 渡されたキャラクターの距離適性に対する適性ランクを返す
        /// </summary>
        private static RaceDefine.ProperGrade GetDistanceProperGrade(int raceCourseSetId, WorkCardData.CardData cardData)
        {
            var raceDistance = GetProperGroup(raceCourseSetId);
            return raceDistance switch
            {
                MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Short => cardData.GetProperDistanceShort(),
                MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Mile => cardData.GetProperDistanceMile(),
                MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Middle => cardData.GetProperDistanceMiddle(),
                MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Long => cardData.GetProperDistanceLong(),
                _ => RaceDefine.ProperGrade.Null
            };
        }

        /// <summary>
        /// 渡されたキャラクターのバ場適性に対する適性ランクを返す
        /// </summary>
        /// <param name="raceCourseSetId"></param>
        /// <param name="cardData"></param>
        /// <returns></returns>
        private static RaceDefine.ProperGrade GetGroundTypeProperGrade(int raceCourseSetId, WorkCardData.CardData cardData)
        {
            var course = MasterDataManager.Instance.masterRaceCourseSet.Get(raceCourseSetId);
            var raceGroundType = (RaceDefine.GroundType)course.Ground;

            return raceGroundType switch
            {
                RaceDefine.GroundType.Turf => cardData.GetProperGroundTurf(),
                RaceDefine.GroundType.Dirt => cardData.GetProperGroundDirt(),
                _ => RaceDefine.ProperGrade.Null
            };
        }

        /// <summary>
        /// 渡されたキャラクターの脚質適性に対する適性ランクを返す
        /// </summary>
        /// <param name="runningStyle"></param>
        /// <param name="cardData"></param>
        /// <returns></returns>
        private static RaceDefine.ProperGrade GetRunningStyleProperGrade(RaceDefine.RunningStyle runningStyle, WorkCardData.CardData cardData)
        {
            return runningStyle switch
            {
                RaceDefine.RunningStyle.Nige => cardData.GetProperRunningStyleNige(),
                RaceDefine.RunningStyle.Senko => cardData.GetProperRunningStyleSenko(),
                RaceDefine.RunningStyle.Sashi => cardData.GetProperRunningStyleSashi(),
                RaceDefine.RunningStyle.Oikomi => cardData.GetProperRunningStyleOikomi(),
                _ => RaceDefine.ProperGrade.Null
            };
        }

        /// <summary>
        /// 指定したカードIDが継承ウマ娘選択で超おすすめか、おすすめか、おすすめされないかを返す
        /// </summary>
        /// <param name="cardId"></param>
        /// <param name="trainedCharaData"></param>
        /// <param name="raceFitAssistance"></param>
        /// <returns></returns>
        public static RaceFitAssistanceDefine.RecommendedType GetRecommendForSuccessionCard(int cardId, WorkTrainedCharaData.TrainedCharaData trainedCharaData, TargetRaceFitAssistanceEventInfo raceFitAssistance)
        {
            var targetRaceFitAssistanceModel = RaceFitAssistanceModelRepository.GetTargetModel();
            if (targetRaceFitAssistanceModel != null)
            {
                var targetRaceFitAssistanceEventInfo = targetRaceFitAssistanceModel.TargetRaceFitAssistanceEventInfo;
                var recommendedSuccessionCardIdList = targetRaceFitAssistanceEventInfo.RaceFitAssistInfo.RecommendedSuccessionCardIdList;
                var isRecommendCard = recommendedSuccessionCardIdList
                    .Where(x =>
                        IsTargetAssist(x, targetRaceFitAssistanceEventInfo.RunningStyle))
                    .Any(x => x.CardId == cardId);
                
                var factorGroupID = (int)GetProperGroup(raceFitAssistance.RaceFitAssistInfo.RaceCourseSetId);
                var trainedChara = trainedCharaData;
                var factorArray = trainedChara.FactorDataArray;
                var factorExists = WorkTrainedCharaData.TrainedCharaData.CreateSortedFactorList(factorArray)
                    .Any(x => x?.FactorGroupId == factorGroupID);

                
                // おすすめ継承ウマ娘である場合は「おすすめ」のステータスを返す
                // かつ、登録した目標トレーナーガイドレースに対応する距離適正の因子がある場合は「超おすすめ」のステータスを返す
                // それ以外は「なし」のステータスを返す
                if (isRecommendCard) // おすすめカードの場合
                {
                    if (factorExists)　// おすすめカードで、かつ登録した目標トレーナーガイドレースに対応する距離適正の因子がある場合
                    {
                        return RaceFitAssistanceDefine.RecommendedType.VeryRecommended;
                    }
                    return RaceFitAssistanceDefine.RecommendedType.Recommended;
                }
            }
            return RaceFitAssistanceDefine.RecommendedType.None;
        }

        /// <summary>
        /// 渡されたraceCourseSetIdを参照し距離タイプに対応するグループIDを返す
        /// </summary>
        /// <param name="raceCourseSetId"></param>
        /// <returns></returns>
        private static MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType GetProperGroup(int raceCourseSetId)
        {
            var distanceType = GetCourseDistanceType(raceCourseSetId);
                
            return distanceType switch
            {
                RaceDefine.CourseDistanceType.Short => MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Short,
                RaceDefine.CourseDistanceType.Mile => MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Mile,
                RaceDefine.CourseDistanceType.Middle => MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Middle,
                RaceDefine.CourseDistanceType.Long => MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Long,
                _ => 0
            };
        }

        /// <summary>
        /// レースコースセットIDから距離タイプを返す(定義はenumの短距離～長距離)
        /// </summary>
        /// <param name="raceCourseSetId"></param>
        /// <returns></returns>
        public static RaceDefine.CourseDistanceType GetCourseDistanceType(int raceCourseSetId)
        {
            var course = MasterDataManager.Instance.masterRaceCourseSet.Get(raceCourseSetId);
            var distance = course.Distance;
            return StandaloneSimulator.RaceUtil.CalcDistanceType(distance);
        }

        /// <summary>
        /// 育成開始画面かどうか
        /// </summary>
        /// <remarks>
        /// ホームの編成画面ではおすすめラベルは出さず、
        /// 育成開始画面の編成画面ではおすすめラベルを出したいため
        /// その画面かどうかを判定する
        /// </remarks>
        /// <returns></returns>
        public static bool IsSingleModeStartView()
        {
            var currentViewController = SceneManager.Instance.GetCurrentViewController();
            if (currentViewController is not HomeHubViewController homeHubViewController) return false;
            var childCurrentViewController = homeHubViewController.ChildCurrentController;
            return childCurrentViewController is SingleModeStartViewController;
        }

        #endregion
        
        #region イベントの時期を処理するロジック

        /// <summary>
        /// 渡したイベント情報に対して、イベントの状態を返す
        /// </summary>
        /// <remarks>
        /// 開催中か、終了済みか、未開催かを返す
        /// </remarks>
        /// <param name="targetRaceFitAssistanceEventInfo"></param>
        /// <returns></returns>
        public static RaceFitAssistanceDefine.EventStatus GetEventStatus(TargetRaceFitAssistanceEventInfo targetRaceFitAssistanceEventInfo)
        {
            var isNotStarted = GetEventStartTime(targetRaceFitAssistanceEventInfo) > TimeUtil.GetServerTimeStamp();
            var isFinished = targetRaceFitAssistanceEventInfo.EndTime <= TimeUtil.GetServerTimeStamp();
            var isOngoing = !isNotStarted && !isFinished;

            if (isOngoing) return RaceFitAssistanceDefine.EventStatus.Ongoing;
            if (isFinished) return RaceFitAssistanceDefine.EventStatus.Finished;
            return RaceFitAssistanceDefine.EventStatus.NotStarted;
        }

        /// <summary>
        /// イベント毎の開催開始時間を返す
        /// </summary>
        /// <param name="targetRaceFitAssistanceEventInfo"></param>
        /// <returns></returns>
        private static long GetEventStartTime(TargetRaceFitAssistanceEventInfo targetRaceFitAssistanceEventInfo)
        {
            // チャンピオンズミーティングはラウンド1開始時間
            // リーグオブヒーローズはメインステージ開始時間
            // マスターズチャレンジはイベントの開始時間を返す
            return targetRaceFitAssistanceEventInfo.EventType switch
            {
                RaceFitAssistanceDefine.TargetEventType.Champions
                    => GetChampionStartTime(targetRaceFitAssistanceEventInfo.EventId),
                RaceFitAssistanceDefine.TargetEventType.Heroes
                    => GetHeroesStartTime(targetRaceFitAssistanceEventInfo.EventId),
                RaceFitAssistanceDefine.TargetEventType.UltimateRace
                    => GetUltimateRaceStartTime(targetRaceFitAssistanceEventInfo.EventId),
                _ => 0
            };

            long GetChampionStartTime(int eventId)
            {
                var championsRoundSchedule =
                    MasterDataManager.Instance.masterChampionsRoundSchedule.GetWithChampionsIdAndRound(eventId, CHAMPIONS_ROUND_START_DATE);
                return championsRoundSchedule.StartDate;
            }

            long GetHeroesStartTime(int eventId)
            {
                return MasterDataManager.Instance.masterHeroesStageSchedule.GetStage1StartDate(eventId);
            }

            long GetUltimateRaceStartTime(int eventId)
            {
                var ultimateRaceData = MasterDataManager.Instance.masterUltimateRaceData.Get(eventId);
                return ultimateRaceData.StartDate;
            }
        }

        /// <summary>
        /// 「〇月中旬開催予定」といった文言を返す
        /// </summary>
        /// <returns></returns>
        public static string GetEventStartTimeText(TargetRaceFitAssistanceEventInfo targetRaceFitAssistanceEventInfo)
        {
            var startTime = GetEventStartTime(targetRaceFitAssistanceEventInfo);
            DateTime dateTime = DateTimeOffset.FromUnixTimeSeconds(startTime).DateTime;
            int day = dateTime.Day;
            int month = dateTime.Month;
            if(day <= MONTH_EARY_BORDER)
            {
                return TextUtil.Format(TextId.RaceFitAssisrance648029.Text(), month);
            }
            if(day <= MONTH_MIDDLE_BORDER)
            {
                return TextUtil.Format(TextId.RaceFitAssisrance648030.Text(), month);
            }
            return TextUtil.Format(TextId.RaceFitAssisrance648031.Text(), month);
        }

        #endregion

        #region Skill
        
        /// <summary>
        /// おすすめ度合いで絞り込みしたスキルリストを返す
        /// </summary>
        /// <param name="baseList"></param>
        /// <param name="recommendedType"></param>
        /// <param name="runningStyle"> 指定なしの場合はRepositoryから設定中のものを参照する </param>
        /// <returns></returns>
        public static IReadOnlyList<RaceFitAssistanceSkillInfo> GetFilteredRecommendedSkillList(
            IReadOnlyList<RaceFitAssistanceSkillInfo> baseList, RaceFitAssistanceDefine.RecommendedType recommendedType,
            RaceDefine.RunningStyle runningStyle = RaceDefine.RunningStyle.None)
        {
            if (runningStyle == RaceDefine.RunningStyle.None)
            {
                var model = RaceFitAssistanceModelRepository.GetTargetModel();
                if (model == null)
                {
                    Debug.LogError("トレーナーガイド対象レースが設定されていません");
                    return null;
                }

                runningStyle = model.TargetRaceFitAssistanceEventInfo.RunningStyle;
            }
            
            switch (runningStyle)
            {
                case RaceDefine.RunningStyle.Nige:
                    return baseList.Where(x => x.RunningStyleNige == (int)recommendedType).ToList();

                case RaceDefine.RunningStyle.Senko:
                    return baseList.Where(x => x.RunningStyleSenko == (int)recommendedType).ToList();

                case RaceDefine.RunningStyle.Sashi:
                    return baseList.Where(x => x.RunningStyleSashi == (int)recommendedType).ToList();

                case RaceDefine.RunningStyle.Oikomi:
                    return baseList.Where(x => x.RunningStyleOikomi == (int)recommendedType).ToList();
            }

            // ここに達することはないはず
            Debug.LogError("おすすめスキルの絞り込みに失敗しました");
            return null;
        }
        
        /// <summary>
        /// 「おすすめスキル」情報をベースに、スキル獲得用のスキル情報リストを作成する
        /// <see cref="SingleModeUtils.CreateSkillInfoBySkillIdList"/>をベースに作成
        /// </summary>
        /// <param name="skillIdList"></param>
        /// <returns></returns>
        public static List<SingleModeSkillLearningSkillInfo> CreateSkillInfoBySkillIdList(List<int> skillIdList)
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var acquiredSkillList = workChara.AcquiredSkillList;
            var mdm = MasterDataManager.Instance;
            var masterSkillData = mdm.masterSkillData;

            var skillInfoList = new List<SingleModeSkillLearningSkillInfo>(skillIdList.Count);

            int ReplaceWhenHasBadSkill(int skillId)
            {
                var data = masterSkillData.Get(skillId);
                if (PartsSingleModeSkillLearningListItem.IsRareSkill(data))
                {
                    return skillId;// レアスキルはツリーが独立して習得可能なので置き換え対象外
                }
                if (data == null) return skillId;
                foreach (var acquiredSkill in acquiredSkillList)
                {
                    if (acquiredSkill.MasterData.GroupId == data.GroupId &&
                        acquiredSkill.MasterData.GroupRate < 0)
                    {
                        return acquiredSkill.MasterId;
                    }
                }
                return skillId;
            }

            void AddSkillInfo(int skillId)
            {
                if (skillInfoList.Exists(info => info.SkillList.Exists(skill => skill.SkillId == skillId)))
                {
                    return;                    // すでにスキルツリー内に存在する場合は追加しない
                }

                var data = masterSkillData.Get(skillId);
                if (skillInfoList.Exists(info => info.SkillList.Exists(skill =>
                    skill.MasterData.GroupId == data.GroupId &&
                    skill.MasterData.Rarity == data.Rarity)))
                {
                    // 82141
                    // すでにスキルツリー内に同じスキルグループのスキルが存在する場合は追加しない
                    // 例）良馬場◎が存在しているなら、良馬場〇の追加をスルーする
                    return;
                }

                var skillInfo = new SingleModeSkillLearningSkillInfo();
                skillInfo.AddInfo(skillId);
                if (skillInfo.SkillList.Count <= 0) return;
                skillInfoList.Add(skillInfo);
            }

            // データ作成
            foreach (var tmpSkillId in skillIdList)
            {
                // Badスキル所持している時は打ち消しできるようにBadスキルIDへ置き換える
                var skillId = ReplaceWhenHasBadSkill(tmpSkillId);
                AddSkillInfo(skillId);
            }

            // レアスキルが習得できる場合に、同グループのノーマルスキルも習得可能にする
            for (int i = 0; i < skillInfoList.Count; i++)
            {
                var lastInfo = skillInfoList[i].SkillList[0];
                // レア以外は処理不要
                if (PartsSingleModeSkillLearningListItem.IsRareSkill(lastInfo.MasterData) == false)
                {
                    continue;
                }

                // レアスキルの同グループのノーマルスキルを抽出
                var skillGroupList = masterSkillData.GetListWithGroupIdOrderByIdAsc(lastInfo.MasterData.GroupId);
                skillGroupList = skillGroupList.FindAll(a => a.GradeValue >= 0 && PartsSingleModeSkillLearningListItem.IsRareSkill(a) == false);//×除外、レア除外
                if (skillGroupList.Count <= 0) continue;
                skillGroupList.Sort((a, b) => a.GradeValue - b.GradeValue);

                // バッドスキル考慮
                var normalSkillId = ReplaceWhenHasBadSkill(skillGroupList[0].Id);
                // ノーマルスキルはツリー内に内包されるので１つ追加するだけでよい
                AddSkillInfo(normalSkillId);
            }

            // レアスキルのスキルポイントを計算する
            SingleModeUtils.CalcSameGroupLowRateSkillNeedPointSum(skillInfoList);

            // 進化先があるスキルに進化ボタンを表示する情報付与
            var upgradeSkillIdList = WorkDataManager.Instance.SingleMode.Character.GetSkillUpgradeSkillIdList().ToList();
            foreach (var skillInfo in skillInfoList)
            {
                foreach (var skillInfoChild in skillInfo.SkillList)
                {
                    if (upgradeSkillIdList.Contains(skillInfoChild.SkillId))
                    {
                        skillInfoChild.IsSkillUpgrade = true;
                    }
                }
            }

            // トレーナーガイドでは獲得できないスキルも表示されるので、獲得可能性の情報を付与しておく
            for (int i = 0; i < skillInfoList.Count; i++)
            {
                var skillInfo = skillInfoList[i];
                skillInfo.UpdateAvailabilityInfo();
            }

            return skillInfoList;
        }
        
        #endregion
        
        #region サポートカード

        /// <summary>
        /// 設定中のトレーナーガイドにおける、指定サポートカードの推奨度を返す
        /// </summary>
        /// <param name="supportCardData"></param>
        /// <param name="scenarioId"></param>
        /// <returns></returns>
        public static RaceFitAssistanceDefine.RecommendedType GetSupportCardRecommendedType(MasterSupportCardData.SupportCardData supportCardData, int scenarioId)
        {
            // 設定中のトレーナーガイド情報を取得する
            var model = RaceFitAssistanceModelRepository.GetTargetModel();
            if (model == null || supportCardData == null)
            {
                return RaceFitAssistanceDefine.RecommendedType.None;
            }
            
            // 超おすすめ判定
            var runningStyle = model.TargetRaceFitAssistanceEventInfo.RunningStyle;
            var veryRecommendedSupportCardArray = model.RecommendedSupportCardArray;
            if (veryRecommendedSupportCardArray.Where(x => x.ScenarioId == scenarioId)
                .Any(x => x.SupportCardId == supportCardData.Id && IsTargetAssist(x, runningStyle)))
            {
                return RaceFitAssistanceDefine.RecommendedType.VeryRecommended;
            }

            // おすすめ判定：おすすめスキル一覧が保持しているサポカ配列から、指定サポカの推奨度を検索する
            var recommendedSkillList = model.TargetRaceFitAssistanceEventInfo.RaceFitAssistInfo.RecommendedSkillList;
            for (int skillIdx = 0, skillCnt = recommendedSkillList.Count; skillIdx < skillCnt; skillIdx++)
            {
                var skill = recommendedSkillList[skillIdx];
                var supportCardIds = skill.SupportCardIds;
                
                // スキルが超おすすめされていたらサポカは「おすすめ」で返す
                if (supportCardIds.Any(x => x == supportCardData.Id && IsTargetAssist(skill, runningStyle, RaceFitAssistanceDefine.RecommendedType.Recommended)))
                {
                    return RaceFitAssistanceDefine.RecommendedType.Recommended;
                }
            }
            
            // 最後まで引っ掛からなかったらおすすめではない
            return RaceFitAssistanceDefine.RecommendedType.None;
        }
        
        #endregion

        #region 育成シナリオ選択画面

        /// <summary>
        /// 指定したシナリオIDがシナリオ選択画面で表示しているかどうかを返す
        /// もしシナリオ選択画面にいない場合はfalseが返る
        /// </summary>
        /// <param name="recommendScenarioId"></param>
        /// <returns></returns>
        public static bool IsRecommendScenarioStart(int recommendScenarioId)
        {
            if (SceneManager.Instance.GetCurrentViewController() is HomeHubViewController
                {
                    ChildCurrentController: SingleModeStartViewController singleModeStartViewController
                })
            {
                return singleModeStartViewController.Entry.ScenarioId == recommendScenarioId;
            }
            return false;
        }

        #endregion

        #region 適正アラート

        /// <summary>
        /// 適正アラート判定
        /// </summary>
        /// <returns>適正基準を満たしていない場合true</returns>
        public static bool IsProperAlertCheck(bool alertCheck, RaceDefine.ProperGrade grade)
        {
            return alertCheck && grade <= PROPER_ALERT_THRESHOLD;
        }

        #endregion 適正アラート
        
        #region チームランク確認
        /// <summary>
        /// トレーナーガイドの使用確認ポップアップを出さないかどうかの判定
        /// </summary>
        /// <remarks>
        /// 最高到達チームランクがEのランク未満の場合はポップアップの表示が有効でもシナリオ選択画面に行ったときに表示しないため
        /// その判定ロジック
        /// </remarks>
        public static bool IsLockUseConfirmPopup
        {
            get
            {
                // 最高到達チームランク
                var userData = WorkDataManager.Instance.UserData;
                var bestTeamEvaluationRank = TeamStadiumUtil.GetTeamRank(userData.BestTeamEvaluationPoint);

                // 現状デイリーレジェンドレースの条件と同様のEランクがボーダーなので定数を共有
                return (int)bestTeamEvaluationRank < ServerDefine.NEED_TEAM_RANK_PLAY_DAILY_LEGEND_RACE;
            }
        }
        #endregion

        #region Setting

        /// <summary>
        /// 適性確認ダイアログの設定をデフォルトに戻す
        /// </summary>
        public static void SetAlertSettingDefault()
        {
            var saveLoader = SaveDataManager.Instance.SaveLoader;
            SetupSetting(saveLoader.IsEnableRaceFitAssistancePopup, StaticVariableDefine.Data.ApplicationSettingSaveLoader
                .DEFAULT_IS_ENABLE_RACE_FIT_ASSISTANCE_CHECK);
        }

        /// <summary>
        /// トレーナーガイドのオプションの設定
        /// </summary>
        /// <param name="isEnableRaceFitAssistancePopup"></param>
        /// <param name="isEnableRaceFitAssistanceCheck"></param>
        public static void SetupSetting(bool isEnableRaceFitAssistancePopup, bool isEnableRaceFitAssistanceCheck)
        {
            var saveLoader = SaveDataManager.Instance.SaveLoader;
            // トレーナーガイド：ポップアップ表示の有無
            saveLoader.IsEnableRaceFitAssistancePopup = isEnableRaceFitAssistancePopup;
            // トレーナーガイド：適性確認の有無
            saveLoader.IsEnableRaceFitAssistanceCheck = isEnableRaceFitAssistanceCheck;
        }
        
        /// <summary>
        /// 指定ボタンの表示更新する
        /// </summary>
        /// <param name="button"></param>
        public static void UpdateRaceFitAssistanceButton(RaceFitAssistanceButton button)
        {
            button.UpdateDisp();
        }

        #endregion
        
        #region APIRequest
        
        /// <summary>
        ///  決定したアシスト情報をサーバーに送信する
        /// </summary>
        /// <param name="determineInfo"></param>
        /// <param name="onSuccessAction"></param>
        public static void RequestDetermineRaceFitAssistance(TargetRaceFitAssistanceEventInfo determineInfo,
            System.Action onSuccessAction, Action<Cute.Http.ErrorType, int> onError)
        {
            var req = new DetermineTargetRaceRequest
            {
                assistance_id = determineInfo.AssistanceId,
                event_sub_id = determineInfo.RaceFitAssistInfo.EventSubId,
                running_style = (int)determineInfo.RunningStyle
            };
            req.Send((response) => OnSuccess(response,determineInfo,onSuccessAction), onError);
        }
        
        /// <summary>
        /// 対象トレーナーガイド決定処理が成功した時の処理
        /// Workにデータを反映し、成功時の処理を実行する
        /// </summary>
        /// <param name="response"></param>
        /// <param name="determineInfo"></param>
        /// <param name="onSuccessAction"></param>
        private static void OnSuccess(DetermineTargetRaceResponse response,TargetRaceFitAssistanceEventInfo determineInfo,
            System.Action onSuccessAction)
        {
            var selectedRaceFitAssistanceInfo = new SelectedRaceFitAssistanceInfo
            {
                assistance_id = determineInfo.AssistanceId,
                event_sub_id = determineInfo.RaceFitAssistInfo.EventSubId,
                running_style = (int)determineInfo.RunningStyle
            };
            WorkDataManager.Instance.RaceFitAssistanceData.Apply(response.data, selectedRaceFitAssistanceInfo);
            onSuccessAction.Call();
            
            // コールバック発火で、UIの更新を行う
            RaceFitAssistancePresenter.Dispatcher?.OnUpdateStatus.Call();
        }
        #endregion
    }
}