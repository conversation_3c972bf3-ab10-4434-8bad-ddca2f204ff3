using static Gallop.StaticVariableDefine.CollectEventMap.CollectEventLoginBonus;
using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 収集イベント 特別ログインボーナスの演出
    /// </summary>
    public class PartsCollectEventSpecialLoginBonus
    {
        /// <summary>
        /// 多段階ログボの段階タイプ
        /// </summary>
        private enum CollectEventSpecialLoginBonusStep
        {
            PrimaryReward = 1,  // １段階目：主な報酬
            ExtraReward = 2,    // ２段階目：１段階目終了後のおまけ報酬
            LastDay = 3,        // ３段階目：最終日の特別報酬
        }

        #region const

        // タイトルロゴにつけるテキスト
        private const string LOGIN_BONUS_DATE_TXT = "TXT_txt_campaign_term00";
        private const string LAST_DAY_REWARD_TERM_TXT = "TXT_txt_present_term00";

        private const string LOGO_MOT = "MOT_mc_logo";
        private const string CAMPAIGN_TERM_MOT = "MOT_mc_campaign_term00";
        private const string PRESENT_TERM_MOT = "MOT_mc_present_term00";

        // アイテムのAnimation
        private const string FIRST_STEP_ITEM_ROOT_OBJ = "OBJ_mc_item{0:D2}";
        private const string SECOND_STEP_ITEM_ROOT_OBJ = "OBJ_mc_item08";
        private const string THIRD_STEP_ITEM_ROOT_OBJ = "OBJ_mc_item_xmas";

        private const string ITEM_MOT = "MOT_mc_item_set";

        private const string ITEM_GET_TXT = "TXT_txt_getitem_info01";

        // モーションなしアイテム
        private const string NEXT_ITEM_ROOT_OBJ = "OBJ_mc_next_text";
        private const string POPUP_ITEM_MOT = "MOT_mc_item_popset00";

        private const string NEXT_ITEM_TXT = "TXT_txt_next_login00";
        private const string NEXT_ITEM_ICON_OBJ = "OBJ_mc_itemicon_set00";

        // アイテムの各パーツ名
        private const string ITEM_ICON_PLN = "PLN_dum_itemicon00";
        private const string GET_STAMP_PLN = "PLN_dum_stamp00";
        private const string ITEM_NUM_TXT = "TXT_txt_num_item00";

        // アイテムの背景窓のAnimation
        private const string FIRST_STEP_WINDOW_ROOT_OBJ = "OBJ_window{0:D2}";
        private const string SECOND_STEP_WINDOW_ROOT_OBJ = "OBJ_window08";
        private const string THIRD_STEP_WINDOW_ROOT_OBJ = "OBJ_window_xmas00";
        private const string WINDOW_NUMBER_MOT = "MOT_img_number_set";

        private const string WINDOW_MOT = "MOT_window";
        private const string CLOSE_WINDOW_MOT = "MOT_img_window_close00";
        private const string INSIDE_WINDOW_MOT = "MOT_mc_tx_room00";
        private const string LEFT_WINDOW_MOT = "OBJ_img_window_open01/offset/MOT_img_window_open01";
        private const string RIGHT_WINDOW_MOT = "OBJ_img_window_open_r01/offset/MOT_img_window_open01";
        private const string LAST_DAY_WINDOW_MOT = "MOT_mc_mini_window";

        private const string CLOSE_DOOR_MOT ="MOT_img_door_close00";
        private const string INSIDE_DOOR_MOT = "MOT_mc_door_room00";
        private const string LEFT_DOOR_MOT = "OBJ_img_door_open01/offset/MOT_img_door_open01";
        private const string RIGHT_DOOR_MOT = "OBJ_img_door_open_r01/offset/MOT_img_door_open01";

        private const string CHARA_PLN_00 = "PLN_dum_chara00";
        private const string CHARA_PLN_01 = "PLN_dum_chara01";
        private const string CHARA_MOT_00 = "MOT_mc_move_dum_chara00";
        private const string CHARA_MOT_01 = "MOT_mc_move_dum_chara01";

        private const string POPUP_MOT = "MOT_mc_popup_item_get00";
        private const string NEXT_DAY_INFO_MOT = "MOT_mc_next_text";

        private const string LOGIN_BONUS_BODY_BG_MOT = "MOT_mc_school_base00";
        private const string ORNAMENT_MOT = "MOT_mc_school_decoset00";
        private const string LAST_DAY_EFFECT_MOT = "MOT_effect_window_set_xmas";

        // flashで表示できる各段階のアイテム数
        private const int FIRST_STEP_ITEM_COUNT = 7;

        private const float ITEM_POP_TIME = 0.667f;

        private const string FLASH_LABEL_IN00 = "in00";
        private const string FLASH_LABEL_IN = "in";
        private const string FLASH_LABEL_IN_END = "in_end";
        private const string FLASH_LABEL_IN_NEXT = "in_next";
        private const string FLASH_LABEL_IN_GET = "in_get";
        private const string FLASH_LABEL_IN_DIALOG = "in_dialog";
        private const string FLASH_LABEL_IN_FADE = "in_fade";
        private const string FLASH_LABEL_STAMP_DONE = "stamp_done";
        private const string FLASH_LABEL_DONE = "done";
        private const string FLASH_LABEL_ITEM = "item";
        private const string FLASH_LABEL_ITEM_PLAN = "item_plan";
        private const string FLASH_LABEL_OPEN = "open";
        private const string FLASH_LABEL_OUT = "out";
        private const string FLASH_LABEL_OUT_END = "out_end";
        private const string FLASH_LABEL_END = "end";
        private const string FLASH_LABEL_SCHOOL_END = "school_end";
        private const string FLASH_LABEL_WINDOW_NUMBER = "n{0}";
        private const string FLASH_LABEL_WINDOW_EXTRA = "qu";

        private const string FLASH_LABEL_POPUP_ITEM_FIRST_STEP = "in{0}";
        private const string FLASH_LABEL_POPUP_ITEM_SECOND_STEP = "in_etc";
        private const string FLASH_LABEL_POPUP_ITEM_THIRD_STEP = "in_xmas";

        private const string FLASH_LABEL_NORMAL_WINDOW = "noon";
        private const string FLASH_LABEL_LAST_DAY_WINDOW = "night";

        private const string FLASH_LABEL_PLAY_IN_ORNAMENT = "in_deco";
        private const string FLASH_LABEL_IN_EFFECT = "in_eff";

        #endregion

        private FlashActionPlayer _titleActionPlayer;
        private FlashActionPlayer _bodyActionPlayer;

        private List<AnimateToUnity.AnMotion> _itemIconAnMotionList = new List<AnimateToUnity.AnMotion>();
        private List<AnimateToUnity.AnMotion> _windowAnMotionList = new List<AnimateToUnity.AnMotion>();

        /// <summary> ログボ２段階目の報酬アイコンインデックス </summary>
        private int _extraRewardAnMotionIndex;
        /// <summary> ログボ３段階目の報酬アイコンインデックス </summary>
        private int _lastDayAnMotionIndex;
        /// <summary> ログボ３段階目（最後の報酬）がもらえる日の前日かどうか </summary>
        private bool _isPreviousDayOfLastStep;
        /// <summary> 複数段階あるログボのなかで今日が何段階目か </summary>
        private CollectEventSpecialLoginBonusStep _step;

        /// <summary> ログボ１～２段階目の期間テキスト </summary>
        private string _loginBonusTerm;
        /// <summary> ログボ３段階目の期間テキスト </summary>
        private string _lastDayRewardTerm;

        private Coroutine _playOutAnimationCoroutine = null;
        private SceneDefine.ViewId _resourceLoadViewId;


        /// <summary>
        /// コンストラクタ
        /// </summary>
        public PartsCollectEventSpecialLoginBonus(SceneDefine.ViewId resourceLoadViewId)
        {
            _resourceLoadViewId = resourceLoadViewId;
        }

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register, MasterLoginBonusData.LoginBonusData loginBonusData)
        {
            // Flash
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_LOGO_FLASH_ACTION);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_BODY_FLASH_ACTION);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_LOGO_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_BODY_EFFECT);

            // 「獲得済」スタンプの画像
            if (loginBonusData.GroupId == 0)
            {
                // グループIdの指定が無ければ、そのログボのスタンプ画像だけDL
                register.RegisterPath(ResourcePath.GetSpecialLoginBonusStampPath(loginBonusData.StampId));
            }
            else
            {
                // グループIdに指定があった場合は、同じグループ全てのログボのスタンプ画像をDL
                var loginBonusDataList = MasterDataManager.Instance.masterLoginBonusData.GetListWithGroupIdOrderByStepAsc(loginBonusData.GroupId);
                foreach (var data in loginBonusDataList)
                {
                    register.RegisterPath(ResourcePath.GetSpecialLoginBonusStampPath(data.StampId));
                }
            }

            // NEXTスタンプの画像
            register.RegisterPath(ResourcePath.LOGIN_BONUS_NEXT_STAMP);

            // 背景画像
            var path = ResourcePath.GetSpecialLoginBonusBgPath(loginBonusData.GetBgId());
            register.RegisterPathWithoutInfo(path);

            // SE
            var cueSheetName = AudioDefine.AUDIO_ID_DATA_DIC[AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_TITLE]._cueSheet;
            AudioManager.Instance.RegisterDownloadByCueSheet(register, cueSheetName, AudioManager.SubFolder.Se);
        }

        #region Flash生成

        public void CreateFlash(Transform titleRoot, Transform bodyRoot)
        {
            CreateTitleFlash(titleRoot);
            CreateBodyFlash(bodyRoot);
        }

        private void CreateTitleFlash(Transform root)
        {
            const int TITLE_SORT_OFFSET = 500;
            const float ANCHORED_POSITION_Y = -250f;

            var titlePrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_LOGO_FLASH_ACTION, _resourceLoadViewId);
            var titeleObj = GameObject.Instantiate(titlePrefab, root);
            _titleActionPlayer = titeleObj.GetComponent<FlashActionPlayer>();
            _titleActionPlayer.LoadFlashPlayer();
            _titleActionPlayer.SetSortOffset(TITLE_SORT_OFFSET);

            var rectTransform = titeleObj.transform as RectTransform;
            rectTransform.anchorMax = LOGO_ANCHOR;
            rectTransform.anchorMin = LOGO_ANCHOR;

            rectTransform.SetAnchoredPositionY(ANCHORED_POSITION_Y);

        }

        private void CreateBodyFlash(Transform root)
        {
            const int BODY_SORT_OFFSET = 10;

            var bodyPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_BODY_FLASH_ACTION, _resourceLoadViewId);
            var bodyObj = GameObject.Instantiate(bodyPrefab, root);

            _bodyActionPlayer = bodyObj.GetComponent<FlashActionPlayer>();
            _bodyActionPlayer.LoadFlashPlayer();
            _bodyActionPlayer.SetSortOffset(BODY_SORT_OFFSET);
        }

        #endregion

        #region Flash再生

        /// <summary>
        /// タイトルのイリ再生
        /// </summary>
        public void PlayInTitle(Action onComplete, bool isSkip = false)
        {
            _titleActionPlayer.FlashPlayer.SetText(_loginBonusTerm, LOGIN_BONUS_DATE_TXT);
            _titleActionPlayer.FlashPlayer.SetText(_lastDayRewardTerm, LAST_DAY_REWARD_TERM_TXT);

            // 再生をスキップしない場合は、SE再生・アニメーション終了コールバック設定
            if (!isSkip)
            {
                AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_TITLE);
                _titleActionPlayer.FlashPlayer.AddActionCallBack(FLASH_LABEL_IN_END, _ => onComplete?.Invoke(), AnimateToUnity.AnMotionActionTypes.Start);
            }

            _titleActionPlayer.FlashPlayer.Play(FLASH_LABEL_IN);

            if (isSkip)
            {
                // ラベルをスキップした影響でパーティクルが生成されなかったら手動で作る
                var titleMot = _titleActionPlayer.FlashPlayer.GetMotion(LOGO_MOT);
                if (titleMot.CurrentLabelName == FLASH_LABEL_IN)
                {
                    var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_LOGO_EFFECT, _resourceLoadViewId);
                    var particleObject = GameObject.Instantiate(prefab, _titleActionPlayer.FlashPlayer.transform);

                    const int SORT_ORDER = 900;
                    var particleList = particleObject.GetComponentsInChildren<ParticleSystemRenderer>(true);
                    foreach (var particle in particleList)
                    {
                        particle.sortingOrder = SORT_ORDER;
                    }
                }

                // タイトル・開催日付のアニメーションスキップ
                titleMot.SetMotionPlay(FLASH_LABEL_IN_END);
                _titleActionPlayer.FlashPlayer.GetMotion(CAMPAIGN_TERM_MOT).SetMotionPlay(FLASH_LABEL_IN_END);
                _titleActionPlayer.FlashPlayer.GetMotion(PRESENT_TERM_MOT).SetMotionPlay(FLASH_LABEL_IN_END);
            }
        }

        /// <summary>
        /// ログボ本体のイリ再生
        /// </summary>
        public void PlayInList(Action onComplete)
        {
            // ログボの段階に応じて、本体背景のラベルを変える
            var label = _step == CollectEventSpecialLoginBonusStep.LastDay ? FLASH_LABEL_LAST_DAY_WINDOW : FLASH_LABEL_NORMAL_WINDOW;
            var mot = _bodyActionPlayer.FlashPlayer.GetMotion(LOGIN_BONUS_BODY_BG_MOT);
            mot.SetMotionPlay(label);

            // ログボが最終段階以外ならエフェクトを出さないようにコールバック削除
            if(_step != CollectEventSpecialLoginBonusStep.LastDay)
            {
                _bodyActionPlayer.FlashPlayer.RemoveAction(FLASH_LABEL_IN, AnimateToUnity.AnMotionActionTypes.Start);
            }

            // イリの途中でオーナメント類を動かすコールバック設定
            _bodyActionPlayer.FlashPlayer.AddActionCallBack(FLASH_LABEL_PLAY_IN_ORNAMENT, _ =>
            {
                _bodyActionPlayer.FlashPlayer.GetMotion(ORNAMENT_MOT).SetMotionPlay(FLASH_LABEL_IN);
            },
            AnimateToUnity.AnMotionActionTypes.Start,
            null);

            // イリ再生
            _bodyActionPlayer.FlashPlayer.AddActionCallBack(FLASH_LABEL_IN, OnComplete, AnimateToUnity.AnMotionActionTypes.End);
            _bodyActionPlayer.Play(FLASH_LABEL_IN);

            void OnComplete(object obj)
            {
                onComplete?.Invoke();
                PlayCharaMotion();
            }
        }

        /// <summary>
        /// 窓が開くアニメーション再生
        /// </summary>
        public void PlayWindowOpenAndPopItem(int num, Action onItemIn)
        {
            var targetIndex = GetTodayRewardAnMotionIndex(num);
            if (targetIndex < 0 || targetIndex > _lastDayAnMotionIndex)
            {
                onItemIn?.Invoke();
                return;
            }

            // ログボが３段階目の時は、２段階目の窓も空いた状態にしておく
            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                var step2Motion = _windowAnMotionList[_extraRewardAnMotionIndex];
                step2Motion.SetMotionPlay(FLASH_LABEL_IN00);
            }

            // ログボの段階に応じてSE再生
            switch (_step)
            {
                case CollectEventSpecialLoginBonusStep.PrimaryReward:
                    AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_WINDOW);
                    break;
                case CollectEventSpecialLoginBonusStep.ExtraReward:
                    AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_DOOR);
                    break;
                case CollectEventSpecialLoginBonusStep.LastDay:
                    AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_LAST_REWARD);
                    break;
            }

            // 対象の窓のアニメーション再生
            var targetAnMotion = _windowAnMotionList[targetIndex];
            targetAnMotion.AddAction(FLASH_LABEL_ITEM, AnimateToUnity.AnMotionActionTypes.Start, _ => onItemIn?.Invoke(), null);
            targetAnMotion.SetMotionPlay(FLASH_LABEL_OPEN);
        }

        /// <summary>
        /// 獲得したアイテムの詳細ポップアップイリ
        /// </summary>
        public void PlayInItemInfo(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList, bool isSkip = false, Action<object> onFinish = null)
        {
            var flash = _bodyActionPlayer.FlashPlayer;
            var popupItemMot = flash.GetMotion(POPUP_ITEM_MOT);
            var popupMot = flash.GetMotion(POPUP_MOT);

            var todayLoginBonusDetail = (_step != CollectEventSpecialLoginBonusStep.LastDay) ?
                detailList[num - 1] :               // 最終日以外なら累積日付に対応したアイテム
                detailList[detailList.Count - 1];   // 最終日なら最後のアイテム

            // ポップアップで出てくるアイコンの設定
            SetItemIcon(todayLoginBonusDetail, popupItemMot.GameObject);

            var anText = flash.GetText(ITEM_GET_TXT, rootGameObject: popupMot.GameObject);
            var itemName = GallopUtil.GetItemName((GameDefine.ItemCategory)todayLoginBonusDetail.ItemCategory, todayLoginBonusDetail.ItemId);
            anText.SetText(TextId.Outgame0305.Format(itemName));

            // アイテムが窓から飛び出すアニメーション再生（日付によってラベルが変わる）
            var popupItemLabel = GetPopupItemMotLabel(num);
            popupItemMot.SetMotionPause(popupItemLabel);

            var time = isSkip ? ITEM_POP_TIME : 0f;
            popupItemMot.SetMotionPlay(popupItemLabel, time);

            // スキップでなければ、アイテムが窓から飛び出すSE再生
            if (!isSkip)
            {
                AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_ITEM_APPEAR);
            }

            // ポップアップのイリ再生
            var popupLabel = isSkip ? FLASH_LABEL_IN_END : FLASH_LABEL_IN;

            // 最終日はもともとのFlashに入っている最終日以外用のエフェクト生成コールバックを実行させない
            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                popupMot.RemoveAction(FLASH_LABEL_IN_EFFECT, AnimateToUnity.AnMotionActionTypes.Start);
            }

            // エフェクトを出すアニメーションのタイミングでSE再生
            popupMot.AddAction(FLASH_LABEL_IN_EFFECT, AnimateToUnity.AnMotionActionTypes.Start,
                _ => AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_ITEM_GET), null);

            popupMot.AddAction(FLASH_LABEL_IN_END, AnimateToUnity.AnMotionActionTypes.Start, onFinish, null);
            popupMot.SetMotionPlay(popupLabel);
        }

        /// <summary>
        /// 獲得したアイテムの詳細ポップアップハケ
        /// </summary>
        public void PlayOutItemInfo(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList, Action onFinish)
        {
            // ポップアップのハケ再生
            var popupMot = _bodyActionPlayer.FlashPlayer.GetMotion(POPUP_MOT);

            // パーティクルを消す
            var particleList = popupMot.GameObject.GetComponentsInChildren<ParticleSystemRenderer>(true);
            foreach (var particle in particleList)
            {
                particle.gameObject.SetActiveWithCheck(false);
            }

            popupMot.SetMotionPlay(FLASH_LABEL_OUT);
            ShowNextDayReward(num, detailList, onFinish);
        }

        /// <summary>
        /// 翌日に獲得できるアイテムの詳細表示イリ
        /// </summary>
        private void ShowNextDayReward(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList, Action onFinish = null, bool isSkip = false)
        {
            switch (_step)
            {
                case CollectEventSpecialLoginBonusStep.PrimaryReward:
                case CollectEventSpecialLoginBonusStep.ExtraReward:

                    // 次の日に獲得できる報酬を画面下に表示してからonFinishを呼ぶ
                    var flash = _bodyActionPlayer.FlashPlayer;

                    // 次の日に獲得できる報酬の文言・アイコン設定
                    var nextLoginBonusDetail = _isPreviousDayOfLastStep ?
                        detailList[detailList.Count - 1] :   // 次が最終日なら最後のアイテム
                        detailList[num];                    // 次が最終日以外なら累積日付に対応したアイテム

                    var nextItemRootObj = flash.GetObj(NEXT_ITEM_ROOT_OBJ).GameObject;
                    var anText = flash.GetText(NEXT_ITEM_TXT, nextItemRootObj);

                    anText.SetText(TextId.CollectEvent424107.Text());
                    SetItemIcon(nextLoginBonusDetail, nextItemRootObj);

                    var label = isSkip ? FLASH_LABEL_IN_END : FLASH_LABEL_IN_NEXT;
                    flash.Play(label);

                    if (!isSkip)
                    {
                        flash.AddActionCallBack(FLASH_LABEL_IN_DIALOG, _ => onFinish?.Invoke(), AnimateToUnity.AnMotionActionTypes.Start);
                        return;
                    }

                    break;

                case CollectEventSpecialLoginBonusStep.LastDay:
                    // 次の日に獲得できる報酬を表示させない
                    var mot = _bodyActionPlayer.FlashPlayer.GetMotion(NEXT_DAY_INFO_MOT);
                    mot.SetMotionPlay(FLASH_LABEL_IN00);

                    break;
            }

            onFinish?.Invoke();
        }

        /// <summary>
        /// 今日獲得したアイテムの「GET」スタンプアニメーションイリ
        /// </summary>
        private void ShowGetStampOnTodayReward(int num, bool isSkip = false, Action<object> onFinish = null)
        {
            switch (_step)
            {
                case CollectEventSpecialLoginBonusStep.PrimaryReward:
                case CollectEventSpecialLoginBonusStep.LastDay:
                    var index = GetTodayRewardAnMotionIndex(num);
                    if (index < 0 || index > _lastDayAnMotionIndex)
                    {
                        return;
                    }

                    // スキップされていなければSE再生
                    if (!isSkip)
                    {
                        AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_STAMP);
                    }

                    var iconAnMotion = _itemIconAnMotionList[index];
                    var label = isSkip ? FLASH_LABEL_STAMP_DONE : FLASH_LABEL_IN_GET;
                    iconAnMotion.AddAction(FLASH_LABEL_STAMP_DONE, AnimateToUnity.AnMotionActionTypes.Start, onFinish, null);
                    iconAnMotion.SetMotionPlay(label);

                    return;
            }
        }

        /// <summary>
        /// 翌日以降に獲得できるアイテムの強調表示イリ
        /// </summary>
        public void PlayNextInList(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList, bool isSkip = false, Action<object> onFinish = null, bool playSE = true)
        {

            // 最終日は次の日以降に獲得できるアイテムが無いので終了
            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                // GETスタンプがで終わったタイミングでonFinish
                ShowGetStampOnTodayReward(num - 1, isSkip, onFinish);
                return;
            }

            ShowGetStampOnTodayReward(num - 1, isSkip);

            // ログボが2段階目の場合は、今日獲得したアイテムと同じ位置に翌日獲得できるアイテムが出るので設定しなおす
            if (_step == CollectEventSpecialLoginBonusStep.ExtraReward)
            {
                SetItemIcon(detailList[num - 1], _itemIconAnMotionList[_extraRewardAnMotionIndex].GameObject);
            }

            var nextItemIconIndex = GetNextDayRewardAnMotionIndex(num);
            if (nextItemIconIndex >= 0 && nextItemIconIndex <= _lastDayAnMotionIndex)
            {
                // 翌日もらえるアイテムにNEXT表示を出す
                var nextItemAnMotion = _itemIconAnMotionList[nextItemIconIndex];

                // 点滅の際に不具合に見えるため、文字のフチを非表示にする
                _bodyActionPlayer.FlashPlayer.GetText(ITEM_NUM_TXT, rootGameObject: nextItemAnMotion.GameObject).SetTextOutline(Color.clear, 0);

                if (nextItemIconIndex != _lastDayAnMotionIndex)
                {
                    // 最終日の窓以外にある数字の表示を消す
                    _bodyActionPlayer.FlashPlayer.GetMotion(WINDOW_NUMBER_MOT, rootGameObject: _windowAnMotionList[nextItemIconIndex].GameObject).GameObject.SetActiveWithCheck(false);
                }

                // アイコン本体のイリが完了しNEXTの表示が出始めるタイミングでSE再生
                nextItemAnMotion.AddAction(FLASH_LABEL_IN_NEXT, AnimateToUnity.AnMotionActionTypes.End, obj =>
                {
                    if (playSE)
                    {
                        AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_EVENT_LOGIN_BONUS_NEXT);
                    }
                },
                null);

                // NEXTマークのアニメーションが終了したタイミングでonFinishコールバック実行
                nextItemAnMotion.AddAction(FLASH_LABEL_IN_END, AnimateToUnity.AnMotionActionTypes.End, onFinish, null);
                nextItemAnMotion.SetMotionPlay(FLASH_LABEL_IN_NEXT);
            }

            // 明後日以降にもらえるアイテムを点滅させる
            var startIndex = nextItemIconIndex + 1;
            for (var i = startIndex; i < _itemIconAnMotionList.Count; i++)
            {
                _itemIconAnMotionList[i].SetMotionPlay(FLASH_LABEL_ITEM_PLAN);

                if (i != _lastDayAnMotionIndex)
                {
                    // 最終日の窓以外にある数字の表示を消す
                    _bodyActionPlayer.FlashPlayer.GetMotion(WINDOW_NUMBER_MOT, rootGameObject: _windowAnMotionList[i].GameObject).GameObject.SetActiveWithCheck(false);
                }

                // 点滅の際に不具合に見えるため、文字のフチを非表示にする
                _bodyActionPlayer.FlashPlayer.GetText(ITEM_NUM_TXT, rootGameObject: _itemIconAnMotionList[i].GameObject).SetTextOutline(Color.clear, 0);
            }
        }

        /// <summary>
        /// ハケ再生
        /// </summary>
        public void PlayOut(Action onFinish)
        {
            _playOutAnimationCoroutine = UIManager.Instance.StartCoroutine(PlayOutImpl(onFinish));
        }

        /// <summary>
        /// ハケ再生内部コルーチン
        /// </summary>
        private IEnumerator PlayOutImpl(Action onFinish)
        {
            var endBodyOut = false;
            _bodyActionPlayer.Play(FLASH_LABEL_OUT);
            _bodyActionPlayer.FlashPlayer.AddActionCallBack(FLASH_LABEL_OUT, _ =>
            {
                endBodyOut = true;
                _bodyActionPlayer.FlashPlayer.Pause();
            }, AnimateToUnity.AnMotionActionTypes.End);

            var endTitleOut = false;
            _titleActionPlayer.Play(FLASH_LABEL_OUT);
            _titleActionPlayer.FlashPlayer.AddActionCallBack(FLASH_LABEL_END, _ => endTitleOut = true, AnimateToUnity.AnMotionActionTypes.Start);

            yield return new WaitUntil(() => endBodyOut && endTitleOut);

            onFinish?.Invoke();
        }

        /// <summary>
        /// キャラのモーション再生
        /// </summary>
        private void PlayCharaMotion(bool isSkip = false)
        {
            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                return;
            }

            var label = isSkip ? FLASH_LABEL_IN_END : FLASH_LABEL_IN;

            var leftCharaAnMotion = _bodyActionPlayer.FlashPlayer.GetMotion(CHARA_MOT_00);
            leftCharaAnMotion.SetMotionPlay(label);

            var rightCharaAnMotion = _bodyActionPlayer.FlashPlayer.GetMotion(CHARA_MOT_01);
            rightCharaAnMotion.SetMotionPlay(label);
        }

        #endregion

        #region スキップ

        /// <summary>
        /// 獲得したアイテム情報ポップアップイリ終了までスキップ
        /// </summary>
        public void SkipUntilItemInfo(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList)
        {
            InitializeWindowState(num - 1);

            if (0 <= num - 1 && num - 1 < detailList.Count)
            {
                InitializeChara(detailList[num - 1]);
            }

            _titleActionPlayer.FlashPlayer.RemoveAction(FLASH_LABEL_IN_END, AnimateToUnity.AnMotionActionTypes.Start);
            _bodyActionPlayer.FlashPlayer.RemoveAction(FLASH_LABEL_IN, AnimateToUnity.AnMotionActionTypes.End);

            PlayInTitle(null, true);
            _bodyActionPlayer.FlashPlayer.Play(FLASH_LABEL_SCHOOL_END);

            // 必要なら今日のぶんの報酬の窓も開ける
            if (_step != CollectEventSpecialLoginBonusStep.LastDay)
            {
                var index = GetTodayRewardAnMotionIndex(num);
                if (index >= 0 && index <= _lastDayAnMotionIndex)
                {
                    _windowAnMotionList[index].SetMotionPlay(FLASH_LABEL_DONE);
                }
            }
            else
            {
                _windowAnMotionList[_extraRewardAnMotionIndex].SetMotionPlay(FLASH_LABEL_IN00);
                _windowAnMotionList[_lastDayAnMotionIndex].SetMotionPlay(FLASH_LABEL_IN_END);
            }

            PlayInItemInfo(num, detailList, true);
            PlayCharaMotion(true);
        }

        /// <summary>
        /// 翌日獲得できるアイテムの強調表示イリまでスキップ
        /// </summary>
        /// <param name="num"></param>
        /// <param name="detailList"></param>
        public void SkipUntilNextShow(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList)
        {
            _bodyActionPlayer.FlashPlayer.RemoveAction(FLASH_LABEL_IN_DIALOG, AnimateToUnity.AnMotionActionTypes.Start);

            var popupMot = _bodyActionPlayer.FlashPlayer.GetMotion(POPUP_MOT);
            popupMot.SetMotionPlay(FLASH_LABEL_OUT_END);

            // パーティクルを消す
            var particleList = popupMot.GameObject.GetComponentsInChildren<ParticleSystemRenderer>(true);
            foreach (var particle in particleList)
            {
                particle.gameObject.SetActiveWithCheck(false);
            }

            PlayNextInList(num + 1, detailList, true);
            ShowNextDayReward(num, detailList, isSkip: true);
        }

        /// <summary>
        /// ログボ確認画面の表示段階までスキップ
        /// </summary>
        public void SkipForGallery(int getRewardCount, List<MasterLoginBonusDetail.LoginBonusDetail> detailList, Transform iconParent)
        {
            var getRewardCountAvoidLastDay = _step == CollectEventSpecialLoginBonusStep.LastDay ? getRewardCount - 1 : getRewardCount;

            PlayInTitle(null, true);
            // rootとループ以外のパーティクルを非表示にする
            var particleList = _titleActionPlayer.gameObject.GetComponentsInChildren<ParticleSystem>(true);
            const string ROOT_PERTICLE_NAME = "root";
            foreach (var particle in particleList)
            {
                if (particle.gameObject.activeSelf && particle.name != ROOT_PERTICLE_NAME && !particle.main.loop)
                {
                    particle.gameObject.SetActiveWithCheck(false);
                }
            }

            // ログボの段階に応じて、本体背景のラベルを変える
            var label = _step == CollectEventSpecialLoginBonusStep.LastDay ? FLASH_LABEL_LAST_DAY_WINDOW : FLASH_LABEL_NORMAL_WINDOW;
            var mot = _bodyActionPlayer.FlashPlayer.GetMotion(LOGIN_BONUS_BODY_BG_MOT);
            mot.SetMotionPlay(label);
            _bodyActionPlayer.FlashPlayer.Play(FLASH_LABEL_IN_END);

            InitializeWindowState(getRewardCountAvoidLastDay);
            if (0 <= getRewardCount - 1 && getRewardCount - 1 < detailList.Count)
            {
                InitializeChara(detailList[getRewardCount - 1]);
            }

            ShowNextDayReward(getRewardCount, detailList, isSkip: true);
            PlayNextInListForGallery(getRewardCount + 1, detailList, iconParent);
            PlayCharaMotion(true);

            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                _windowAnMotionList[_extraRewardAnMotionIndex].SetMotionPlay(FLASH_LABEL_IN00);
                _windowAnMotionList[_lastDayAnMotionIndex].SetMotionPlay(FLASH_LABEL_IN_END);

                // 最終日固有のエフェクトを非表示に
                _bodyActionPlayer.FlashPlayer.GetMotion(LAST_DAY_EFFECT_MOT).GameObject.SetActiveWithCheck(false);

                // 最終日は専用のラベルがないのでItemIconを生成して表示する
                _itemIconAnMotionList[_lastDayAnMotionIndex].SetMotionPlay(FLASH_LABEL_IN_FADE);

                var stampTexture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.COMMON_GET_STAMP_TEXTURE, _resourceLoadViewId);
                CreateItemIconButton(iconParent, _itemIconAnMotionList[_lastDayAnMotionIndex].Transform.position, detailList.Last(), stampTexture);

                var nextItemIconObj = _bodyActionPlayer.FlashPlayer.GetObj(NEXT_ITEM_ICON_OBJ, rootGameObject: _itemIconAnMotionList[_lastDayAnMotionIndex].GameObject);
                nextItemIconObj.GameObject.SetActiveWithCheck(false);

                // ラベルをスキップした影響でパーティクルが生成されないので手動で作る
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.COLLECT_EVENT_LOGIN_BONUS_BODY_EFFECT, _resourceLoadViewId);
                var particleObject = GameObject.Instantiate(prefab, _bodyActionPlayer.FlashPlayer.transform);

                const int SORT_ORDER = 10;
                var bodyParticleList = particleObject.GetComponentsInChildren<ParticleSystemRenderer>(true);
                foreach (var particle in bodyParticleList)
                {
                    particle.sortingOrder = SORT_ORDER;
                }
            }
        }

        /// <summary>
        /// 明日以降に獲得できるアイテムの強調表示イリ（履歴画面用）
        /// </summary>
        public void PlayNextInListForGallery(int num, List<MasterLoginBonusDetail.LoginBonusDetail> detailList, Transform stampParent)
        {
            // 最終日は次の日以降に獲得できるアイテムが無いので何もしない
            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                return;
            }

            var nextItemIconIndex = GetNextDayRewardAnMotionIndex(num);
            if (nextItemIconIndex < 0)
            {
                return;
            }

            // 明日以降にもらえるアイテムを点滅させる
            for (var i = nextItemIconIndex; i < _itemIconAnMotionList.Count; i++)
            {
                // NEXTスタンプは自前で出す
                if (i == nextItemIconIndex)
                {
                    var stampTexture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.LOGIN_BONUS_NEXT_STAMP, _resourceLoadViewId);
                    var stampSize = i == _lastDayAnMotionIndex ? NEXT_STAMP_SIZE_LAST_DAY : NEXT_STAMP_SIZE;
                    var stampPosOffset = i == _lastDayAnMotionIndex ? NEXT_STAMP_POS_OFFSET_LAST_DAY : NEXT_STAMP_POS_OFFSET;
                    CreateStamp(stampParent, stampTexture, stampSize, _itemIconAnMotionList[i].Transform.position, stampPosOffset);
                }

                // 明滅の際に不具合に見えるため、文字のフチを非表示にする
                _bodyActionPlayer.FlashPlayer.GetText(ITEM_NUM_TXT, rootGameObject: _itemIconAnMotionList[i].GameObject).SetTextOutline(Color.clear, 0);

                // アイコンの明滅開始
                _itemIconAnMotionList[i].SetMotionPlay(FLASH_LABEL_IN_FADE);

                // 最終日の窓以外にある数字の表示を消す
                if (i != _lastDayAnMotionIndex)
                {
                    _bodyActionPlayer.FlashPlayer.GetMotion(WINDOW_NUMBER_MOT, rootGameObject: _windowAnMotionList[i].GameObject).GameObject.SetActiveWithCheck(false);
                }
            }
        }


        #endregion

        /// <summary>
        /// アニメーション初期化
        /// </summary>
        public void Initialize(MasterLoginBonusData.LoginBonusData loginBonusData)
        {
            var loginBonusList = MasterDataManager.Instance.masterLoginBonusData.GetListWithGroupIdOrderByStepAsc(loginBonusData.GroupId);
            _itemIconAnMotionList.Clear();
            _windowAnMotionList.Clear();
            _isPreviousDayOfLastStep = IsPrevioutDayOfLastStep(loginBonusList);
            _step = (CollectEventSpecialLoginBonusStep)loginBonusData.Step;

            // 1段階目のアイテム設定
            var step1DetailList = MasterDataManager.Instance.masterLoginBonusDetail.GetListWithLoginBonusIdOrderByCountAsc(loginBonusList[(int)CollectEventSpecialLoginBonusStep.PrimaryReward - 1].Id);
            for (var i = 0; i < FIRST_STEP_ITEM_COUNT; i++)
            {
                // アイテムのモーション
                var anMotion = GetItemIconAnMotion(step1DetailList[i], loginBonusList[(int)CollectEventSpecialLoginBonusStep.PrimaryReward - 1].StampId, TextUtil.Format(FIRST_STEP_ITEM_ROOT_OBJ, i + 1));
                _itemIconAnMotionList.Add(anMotion);

                // アイテムの背景窓のモーション
                var windowAnMotion = GetWindowAnMotion(TextUtil.Format(FIRST_STEP_WINDOW_ROOT_OBJ, i + 1), WINDOW_MOT);
                _windowAnMotionList.Add(windowAnMotion);
            }

            // 2段階目のアイテム設定
            {
                _extraRewardAnMotionIndex = _itemIconAnMotionList.Count;
                var step2Detail = MasterDataManager.Instance.masterLoginBonusDetail.GetWithLoginBonusIdOrderByCountAsc(loginBonusList[(int)CollectEventSpecialLoginBonusStep.ExtraReward - 1].Id);

                // アイテムのモーション
                var anMotion = GetItemIconAnMotion(step2Detail, loginBonusList[(int)CollectEventSpecialLoginBonusStep.ExtraReward - 1].StampId, SECOND_STEP_ITEM_ROOT_OBJ);
                _itemIconAnMotionList.Add(anMotion);

                // アイテムの背景窓のモーション
                var windowAnMotion = GetWindowAnMotion(SECOND_STEP_WINDOW_ROOT_OBJ, WINDOW_MOT);
                _windowAnMotionList.Add(windowAnMotion);
            }

            // 3段階目のアイテム設定
            {
                _lastDayAnMotionIndex = _itemIconAnMotionList.Count;
                var step3Detail = MasterDataManager.Instance.masterLoginBonusDetail.GetWithLoginBonusIdOrderByCountAsc(loginBonusList[(int)CollectEventSpecialLoginBonusStep.LastDay - 1].Id);

                // アイテムのモーション
                var anMotion = GetItemIconAnMotion(step3Detail, loginBonusList[(int)CollectEventSpecialLoginBonusStep.LastDay - 1].StampId, THIRD_STEP_ITEM_ROOT_OBJ);
                _itemIconAnMotionList.Add(anMotion);

                // アイテムの背景窓のモーション
                var windowAnMotion = GetWindowAnMotion(THIRD_STEP_WINDOW_ROOT_OBJ, WINDOW_MOT);
                _windowAnMotionList.Add(windowAnMotion);
            }

            SetDateText(loginBonusList);
        }

        /// <summary>
        /// 開催期間のテキスト設定
        /// </summary>
        private void SetDateText(List<MasterLoginBonusData.LoginBonusData> loginBonusList)
        {
            // １段階目の期日設定
            var firstStepStartDate = TimeUtil.ToLocalDateTimeFromJstString(loginBonusList[(int)CollectEventSpecialLoginBonusStep.PrimaryReward - 1].StartDate);
            var firstStepEndDate = TimeUtil.ToLocalDateTimeFromJstString(loginBonusList[(int)CollectEventSpecialLoginBonusStep.PrimaryReward - 1].EndDate);
            var frstStepStartText = TextId.CollectEvent424108.Format(firstStepStartDate.Month, firstStepStartDate.Day, firstStepStartDate.Hour, firstStepStartDate.Minute);
            var frstStepEndText = TextId.CollectEvent424108.Format(firstStepEndDate.Month, firstStepEndDate.Day, firstStepEndDate.Hour, firstStepEndDate.Minute);
            _loginBonusTerm = TextId.Common0135.Format(frstStepStartText, frstStepEndText);

            // ３段階目の期日設定
            var lastStepStartDate = TimeUtil.ToLocalDateTimeFromJstString(loginBonusList[(int)CollectEventSpecialLoginBonusStep.LastDay - 1].StartDate);
            var lastStepEndDate = TimeUtil.ToLocalDateTimeFromJstString(loginBonusList[(int)CollectEventSpecialLoginBonusStep.LastDay - 1].EndDate);
            var lastStepStartText = TextId.CollectEvent424108.Format(lastStepStartDate.Month, lastStepStartDate.Day, lastStepStartDate.Hour, lastStepStartDate.Minute);
            var lastStepEndText = TextId.CollectEvent424108.Format(lastStepEndDate.Month, lastStepEndDate.Day, lastStepEndDate.Hour, lastStepEndDate.Minute);
            _lastDayRewardTerm = TextId.CollectEvent424106.Format(lastStepStartText, lastStepEndText);
        }

        /// <summary>
        /// アイテム表示窓の初期状態セットアップ
        /// </summary>
        /// <param name="count"></param>
        public void InitializeWindowState(int recievedCount)
        {
            // 昨日までで獲得済のログボにスタンプを付ける
            var stampCount = Mathf.Min(recievedCount, FIRST_STEP_ITEM_COUNT);

            for (var i = 0; i < stampCount; i++)
            {
                _itemIconAnMotionList[i].SetMotionPlay(FLASH_LABEL_STAMP_DONE);
                _windowAnMotionList[i].SetMotionPlay(FLASH_LABEL_DONE);
            }

            // 背景の差分による窓の画像差し替え
            var label = _step == CollectEventSpecialLoginBonusStep.LastDay ? FLASH_LABEL_LAST_DAY_WINDOW : FLASH_LABEL_NORMAL_WINDOW;
            for (var i = 0; i < _extraRewardAnMotionIndex; i++)
            {
                var root = _windowAnMotionList[i];
                _bodyActionPlayer.FlashPlayer.GetMotion(CLOSE_WINDOW_MOT, rootGameObject: root.GameObject).SetMotionPlay(label);
                _bodyActionPlayer.FlashPlayer.GetMotion(INSIDE_WINDOW_MOT, rootGameObject: root.GameObject).SetMotionPlay(label);
                _bodyActionPlayer.FlashPlayer.GetMotion(LEFT_WINDOW_MOT, rootGameObject: root.GameObject).SetMotionPlay(label);
                _bodyActionPlayer.FlashPlayer.GetMotion(RIGHT_WINDOW_MOT, rootGameObject: root.GameObject).SetMotionPlay(label);
            }

            var doorRoot = _windowAnMotionList[_extraRewardAnMotionIndex];
            _bodyActionPlayer.FlashPlayer.GetMotion(CLOSE_DOOR_MOT, rootGameObject: doorRoot.GameObject).SetMotionPlay(label);
            _bodyActionPlayer.FlashPlayer.GetMotion(INSIDE_DOOR_MOT, rootGameObject: doorRoot.GameObject).SetMotionPlay(label);
            _bodyActionPlayer.FlashPlayer.GetMotion(LEFT_DOOR_MOT, rootGameObject: doorRoot.GameObject).SetMotionPlay(label);
            _bodyActionPlayer.FlashPlayer.GetMotion(RIGHT_DOOR_MOT, rootGameObject: doorRoot.GameObject).SetMotionPlay(label);

            var lastWindowRoot = _windowAnMotionList[_lastDayAnMotionIndex];
            _bodyActionPlayer.FlashPlayer.GetMotion(LAST_DAY_WINDOW_MOT, rootGameObject: lastWindowRoot.GameObject).SetMotionPlay(label);


            // 未獲得の窓に数字を出す
            for (var i = stampCount; i <= _extraRewardAnMotionIndex; i++)
            {
                var numberLabel = i < _extraRewardAnMotionIndex ? TextUtil.Format(FLASH_LABEL_WINDOW_NUMBER, (i + 1)) : FLASH_LABEL_WINDOW_EXTRA;
                _bodyActionPlayer.FlashPlayer.GetMotion(WINDOW_NUMBER_MOT, rootGameObject: _itemIconAnMotionList[i].GameObject).SetMotionPlay(numberLabel);
                _bodyActionPlayer.FlashPlayer.GetMotion(WINDOW_NUMBER_MOT, rootGameObject: _windowAnMotionList[i].GameObject).SetMotionPlay(numberLabel);
            }
        }

        /// <summary>
        /// すべてのコールバックを削除
        /// </summary>
        public void RemoveAllCallback()
        {
            _titleActionPlayer.FlashPlayer.RemoveAllAction();
            _bodyActionPlayer.FlashPlayer.RemoveAllAction();

            foreach (var anMotion in _itemIconAnMotionList)
            {
                anMotion.RemoveAllAction();
            }

            foreach (var anMotion in _windowAnMotionList)
            {
                anMotion.RemoveAllAction();
            }

            if (_playOutAnimationCoroutine != null)
            {
                UIManager.Instance.StopCoroutine(_playOutAnimationCoroutine);
                _playOutAnimationCoroutine = null;
            }
        }

        /// <summary>
        /// アニメーションを破棄
        /// </summary>
        public void DestroyAnimation()
        {
            GameObject.Destroy(_titleActionPlayer.gameObject);
            GameObject.Destroy(_bodyActionPlayer.gameObject);
            _itemIconAnMotionList.Clear();
            _windowAnMotionList.Clear();
        }


        /// <summary>
        /// アイテムのアニメーション初期化
        /// </summary>
        private AnimateToUnity.AnMotion GetItemIconAnMotion(MasterLoginBonusDetail.LoginBonusDetail loginBonusDetail, int stampId, string itemRootPath)
        {
            var bodyFlash = _bodyActionPlayer.FlashPlayer;
            var itemRoot = bodyFlash.GetObj(itemRootPath);

            // 報酬アイコン画像
            SetItemIcon(loginBonusDetail, itemRoot.GameObject);

            // スタンプ画像
            var stampTexture = ResourceManager.LoadOnView<Texture>(ResourcePath.GetSpecialLoginBonusStampPath(stampId), _resourceLoadViewId);
            bodyFlash.SetTexture(GET_STAMP_PLN, stampTexture, rootGameObject: itemRoot.GameObject);

            // アイテムアニメーションの初期状態
            var itemIconMotion = bodyFlash.GetMotion(ITEM_MOT, rootGameObject: itemRoot.GameObject);
            itemIconMotion.SetMotionPlay(FLASH_LABEL_IN00);

            return itemIconMotion;
        }

        /// <summary>
        /// アイテムアイコンの初期化
        /// </summary>
        private void SetItemIcon(MasterLoginBonusDetail.LoginBonusDetail loginBonusDetail, GameObject rootObject = null)
        {
            var bodyFlash = _bodyActionPlayer.FlashPlayer;

            // 報酬アイコン画像
            var itemIconPath = ResourcePath.GetItemIconPath((GameDefine.ItemCategory)loginBonusDetail.ItemCategory, loginBonusDetail.ItemId);
            var itemIconTexture = ResourceManager.LoadOnView<Texture>(itemIconPath, _resourceLoadViewId);
            bodyFlash.SetTexture(ITEM_ICON_PLN, itemIconTexture, rootGameObject: rootObject);

            // 報酬をもらえる個数
            var anText = bodyFlash.GetText(ITEM_NUM_TXT, rootGameObject: rootObject);
            anText.SetText(TextId.Common0278.Format(loginBonusDetail.ItemNum));
        }

        /// <summary>
        /// アイテム表示背景の窓の取得
        /// </summary>
        private AnimateToUnity.AnMotion GetWindowAnMotion(string windowRootPath, string windowMotName)
        {
            var bodyFlash = _bodyActionPlayer.FlashPlayer;
            var windowRoot = bodyFlash.GetObj(windowRootPath);
            var windowAnMotion = bodyFlash.GetMotion(windowMotName, rootGameObject: windowRoot.GameObject);

            windowAnMotion.SetMotionPlay(FLASH_LABEL_IN);
            return windowAnMotion;
        }


        /// <summary>
        /// 今日獲得したアイテムアイコンのIndexを取得
        /// </summary>
        private int GetTodayRewardAnMotionIndex(int num)
        {
            switch (_step)
            {
                case CollectEventSpecialLoginBonusStep.PrimaryReward:
                    return num - 1;
                case CollectEventSpecialLoginBonusStep.ExtraReward:
                    return _extraRewardAnMotionIndex;
                case CollectEventSpecialLoginBonusStep.LastDay:
                    return _lastDayAnMotionIndex;
            }

            Debug.LogError($"アニメーション側で想定していないステップ：{_step}");
            return -1;
        }

        /// <summary>
        /// 次の日に獲得できるアイテムアイコンのIndexを取得
        /// </summary>
        private int GetNextDayRewardAnMotionIndex(int num)
        {
            if (_isPreviousDayOfLastStep)
            {
                // 最終日前日なら、次にもらえるのは必ず最終日のアイテム
                return _lastDayAnMotionIndex;
            }

            switch (_step)
            {
                case CollectEventSpecialLoginBonusStep.PrimaryReward:
                    return num - 1;
                case CollectEventSpecialLoginBonusStep.ExtraReward:
                    return _extraRewardAnMotionIndex;
            }

            return -1;
        }

        /// <summary>
        /// 翌日がログインボーナスの最後の段階かどうか判定
        /// </summary>
        private bool IsPrevioutDayOfLastStep(List<MasterLoginBonusData.LoginBonusData> loginBonusDataList)
        {
            var lastStepLoginBonusData = loginBonusDataList[loginBonusDataList.Count - 1];

            var nextDay = DateChange.Instance.LastCheckTime + TimeUtil.DAY_SECOND;
            var lastStepDay = TimeUtil.ToUnixTimeFromJstString(lastStepLoginBonusData.StartDate);

            return TimeUtil.IsSameInGameDay(nextDay, lastStepDay);
        }

        /// <summary>
        /// 獲得したアイテムのポップアップ表示モーション再生用ラベルを取得
        /// </summary>
        /// <param name="num"></param>
        private string GetPopupItemMotLabel(int num)
        {
            switch (_step)
            {
                case CollectEventSpecialLoginBonusStep.PrimaryReward:
                    return TextUtil.Format(FLASH_LABEL_POPUP_ITEM_FIRST_STEP, num);
                case CollectEventSpecialLoginBonusStep.ExtraReward:
                    return FLASH_LABEL_POPUP_ITEM_SECOND_STEP;
                case CollectEventSpecialLoginBonusStep.LastDay:
                    return FLASH_LABEL_POPUP_ITEM_THIRD_STEP;
            }

            Debug.LogError($"アニメーション側で想定していないステップ：{_step}");
            return string.Empty;
        }

        /// <summary>
        /// キャラ画像の設定
        /// </summary>
        public void InitializeChara(MasterLoginBonusDetail.LoginBonusDetail masterLoginBonusDetail)
        {
            if (_step == CollectEventSpecialLoginBonusStep.LastDay)
            {
                // 最終日はFlash側にキャラ画像が入っているので何もしない
                return;
            }

            var masterLoginBonusChara = MasterDataManager.Instance.masterLoginBonusChara.GetWithLoginBonusDetailId(masterLoginBonusDetail.Id);

            if (masterLoginBonusChara == null)
            {
                Debug.LogError($"login_bonus_chara.csvに定義がありません（loginBonusDetailId：{masterLoginBonusDetail.Id}");
                return;
            }

            // 向かって左のキャラ
            if (masterLoginBonusChara.CharaId != 0)
            {
                var imagePath = ResourcePath.GetCharaPetitIconPath(
                    masterLoginBonusChara.CharaId,
                    masterLoginBonusChara.DressId,
                    (ResourcePath.CharaPetitCatetory)masterLoginBonusChara.PoseId);

                var charaImage = ResourceManager.LoadOnView<Texture2D>(imagePath, _resourceLoadViewId);
                _bodyActionPlayer.FlashPlayer.SetTexture(CHARA_PLN_00, charaImage);
            }

            // 向かって右のキャラ
            if (masterLoginBonusChara.CharaId2 != 0)
            {
                var imagePath = ResourcePath.GetCharaPetitIconPath(
                    masterLoginBonusChara.CharaId2,
                    masterLoginBonusChara.DressId2,
                    (ResourcePath.CharaPetitCatetory)masterLoginBonusChara.PoseId2);

                var charaImage = ResourceManager.LoadOnView<Texture2D>(imagePath, _resourceLoadViewId);
                _bodyActionPlayer.FlashPlayer.SetTexture(CHARA_PLN_01, charaImage);
            }
        }

        #region ItemIcon生成

        /// <summary>
        /// アイテムアイコンをUIのItemIconで生成する
        /// </summary>
        private GameObject CreateItemIconButton(Transform parent, Vector3 iconPosition, MasterLoginBonusDetail.LoginBonusDetail detail, Texture stampTexture = null)
        {
            // アイコン本体のセットアップ
            var itemIconPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_ITEM_ICON_PATH, _resourceLoadViewId);
            var iconObject = GameObject.Instantiate<GameObject>(itemIconPrefab, parent);
            var itemIcon = iconObject.GetComponent<ItemIcon>();
            itemIcon.SetData(detail.ItemCategory, detail.ItemId, detail.ItemNum, isInfoPop: true);

            // 位置をFlashでアイコンが出てくる場所にあわせる
            itemIcon.transform.position = iconPosition;

            // 必要ならGETスタンプを生成
            if (stampTexture != null)
            {
                CreateStamp(itemIcon.Button.transform, stampTexture, GET_STAMP_SIZE, iconPosition);
            }

            return iconObject;
        }

        /// <summary>
        /// スタンプ画像を生成する
        /// </summary>
        private void CreateStamp(Transform parent, Texture stampTexture, Vector2 stampSize, Vector3 stampPosition, Vector2? stampPosOffset = null)
        {
            var stamp = new GameObject().AddComponent<RawImageCommon>();
            stamp.texture = stampTexture;
            stamp.raycastTarget = false;

            var stampTransform = stamp.transform as RectTransform;
            stampTransform.SetParent(parent);
            stampTransform.sizeDelta = stampSize;
            stampTransform.localScale = Math.VECTOR3_ONE;

            stamp.transform.position = stampPosition;
            if (stampPosOffset != null)
            {
                stampTransform.anchoredPosition += (Vector2)stampPosOffset;
            }
        }

        #endregion

    }
}