using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using static Gallop.StaticVariableDefine.Live.ChoreographyCyalumeStatic;

namespace Gallop.Cyalume
{
    /// <summary>
    /// 内部用でカラー情報をパースしてる
    /// </summary>
    public struct CyalumeColorData
    {
#if CYG_DEBUG
        public string Name { get; private set; }
#endif
        public Color InColor { get; private set; }
        public Color OutColor { get; private set; }

        public CyalumeColorData(string name, Color inColor, Color outColor)
        {
#if CYG_DEBUG
            Name = name;
#endif
            InColor = inColor;
            OutColor = outColor;
        }
    };

    /// <summary>
    /// サイリウムの振り付けデータ
    /// </summary>
    public class ChoreographyCyalume
    {
        #region Enum Region
        /// <summary>
        /// 振り付けタイプ
        /// </summary>
        public enum ChoreographyType
        {
            // 振り付け
            Oioi,
            <PERSON><PERSON>,
            Bye,
            PPPH,
            <PERSON><PERSON>,
            Wah,
            High,
            <PERSON>a,

            // コマンド
            Stop,  // 停止（前の振付の0フレーム目）
            Pause, // 一時停止（前の振付のフレーム継続）
            None,  // 無効コマンド

            // コマンドのスタート
            CommandStart = Stop,

            // テクスチャの種類数
            TextureMax = Stop,
        };

        /// <summary>
        /// カラーパターン
        /// </summary>
        public enum ColorPattern : int
        {
            All1 = 0,       // １色で全てに塗る
            Random2,        // ランダム２色
            Random3,        // ランダム３色
            Random4,        // ランダム４色
            Random5,        // ランダム５色
            Line2,          // 横に２色
            Line4,          // 横に４色
            Div2,           // 縦に２色
            Div3,           // 縦に３色
            Div4,           // 縦に４色
            Div5,           // 縦に５色
            SetMobColor,    // モブ色変更用コマンド
            Max
        };

        /// <summary>
        /// CSVのデータ
        /// </summary>
        private enum CsvLabel : int
        {
            Time = 0,      // 開始時間
            MoveType,   // 振り付けタイプ
            Bpm,                // 早さ（BPM）
            ColorPattern,       // 配色のパターン
            Color1,             // 色１
            Color2,             // 色２
            Color3,             // 色３
            Color4,             // 色４
            Color5,             // 色５
            Width1,             // 幅情報１
            Width2,             // 幅情報２
            Width3,             // 幅情報３
            Width4,             // 幅情報４
            Width5,             // 幅情報５
        };

        /// <summary>
        /// 配色のパターンタイプ
        /// </summary>
        public enum ColorDataType
        {
            Mesh,
            Texture,
        };
        #endregion Enum Region

        #region Constant Region
        public const int MAX_COLOR_COUNT = 5;            //    最大の色数
        //public const int MAX_CHOREOGRAPHY_PATTERN = 5; // 未使用につきコメントアウト <2018/04/24:miyashita>
        public const int MAX_MESH_PATTERN = 10;
        public const int MAX_TEXTURE_PATTERN = 10;//5;
        public const float BASE_BPM = 180.0f;                //    ベースのBPM（これを基準に再生速度の調整を行う）
        public const float BASE_BPM_ANIMETION_FRAME = 40.0f;
        #endregion Constant Region

        #region Constant Region
        /// <summary>
        /// 振り付けパターン
        /// </summary>
        public class ChoreographyCyalumePattern
        {
            public ChoreographyType _choreographyType;    //    振り付けタイプ
            public ColorPattern _colorPattern;        //    彩色パターン
            public int _colorCount;        //    色の数
            public CyalumeColorData[] _colorData = new CyalumeColorData[MAX_COLOR_COUNT];
            public float[] _colorWidth = new float[MAX_COLOR_COUNT];

            public bool IsTypeChoreography => (_choreographyType < ChoreographyType.CommandStart);

            public ChoreographyCyalumePattern() {}

            public ChoreographyCyalumePattern(ChoreographyCyalumePattern source)
            {
                _choreographyType = source._choreographyType;
                _colorPattern = source._colorPattern;
                _colorCount = source._colorCount;
                _colorData = source._colorData.ToArray();
                _colorWidth = source._colorWidth.ToArray();
            }
        }

        /// <summary>
        /// 振り付けデータ
        /// </summary>
        [AddComponentMenu("")]
        public class ChoreographyCyalumeData : ChoreographyCyalumePattern
        {
            public int DataNo { get; set; }
            public int PatternId { get; set; }              // パターンID
            public float StartTime { get; set; }            // 開始時間
            public float PlaySpeed { get; set; }            // 再生速度（倍速値）
            public Texture2D PreloadedTexture { get; set; } // プリロードされたサイリウムテクスチャ（Cuttのみで使用）

            public float PlayBpm => PlaySpeed * BASE_BPM;

            public ChoreographyCyalumeData() {}

            public ChoreographyCyalumeData(ChoreographyCyalumeData source) : base(source)
            {
                DataNo = source.DataNo;
                PatternId = source.PatternId;
                StartTime = source.StartTime;
                PlaySpeed = source.PlaySpeed;
                PreloadedTexture = source.PreloadedTexture;
            }
        }
        #endregion Constant Region

        #region Data Region
        public ChoreographyCyalumeData[] ChoreographyCyalumeDataArray = null;   // 振り付けデータ

        public ChoreographyCyalumePattern[] ChoreographyPatternArray = null;

        private ColorDataType _colorDataType;
        public ColorDataType colorDataType => _colorDataType;

        #endregion Data Region

        /// <summary>
        /// データ数の取得。
        /// </summary>
        public int GetDataNum()
        {
            return ChoreographyCyalumeDataArray?.Length ?? 0;
        }

        /// <summary>
        /// BPMからプレイスピードの変換する
        /// </summary>
        /// <param name="bpm"></param>
        /// <returns></returns>
        public static float BpmToPlaySpeed(float bpm)
        {
            return bpm / BASE_BPM;
        }

        /// <summary>
        /// プレイスピードからアニメーションフレームに変換する
        /// </summary>
        /// <param name="speed"></param>
        /// <returns></returns>
        public static float PlaySpeedToAnimationTime(float speed)
        {
            return BASE_BPM_ANIMETION_FRAME / (speed * 60.0f);
        }

#if CYG_DEBUG
        public string GetChoreographyPatternString()
        {
            ChoreographyCyalumePattern pattern;
            string strTemp;

            strTemp = "[Cyalume Color Pattern]";
            strTemp += " : Type " + _colorDataType.ToString();
            strTemp += "\n";
            if (ChoreographyPatternArray != null)
            {
                for (int i = 0; i < ChoreographyPatternArray.Length; i++)
                {
                    pattern = ChoreographyPatternArray[i];
                    strTemp += string.Format("[{0:D02}] : {1}, {2}, {3}[", i, pattern._choreographyType.ToString(), pattern._colorPattern.ToString(), pattern._colorCount);
                    for (int j = 0; j < pattern._colorCount; j++)
                    {
                        strTemp += "(" + pattern._colorData[j].Name + ")";
                    }
                    strTemp += "]\n";
                }
            }

            return strTemp;
        }
#endif

        /// <summary>
        /// 振り付けパターンを検索
        /// </summary>
        public void CreateChoreographyPattern()
        {
            if (ChoreographyCyalumeDataArray == null)
            {
                return;
            }
            if (ChoreographyCyalumeDataArray.Length == 0)
            {
                return;
            }

            // カラーデータタイプを設定
            switch (ChoreographyCyalumeDataArray[0]._colorPattern)
            {
                case ColorPattern.Random2:
                case ColorPattern.Random3:
                case ColorPattern.Random4:
                case ColorPattern.Random5:
                    _colorDataType = ColorDataType.Texture;
                    break;
                default:
                    _colorDataType = ColorDataType.Mesh;
                    break;
            }

            List<ChoreographyCyalumePattern> patterns = new List<ChoreographyCyalumePattern>();
            ChoreographyCyalumePattern choreographyCyalumePattern;
            ChoreographyCyalumePattern pattern;
            int i, j, k;
            int count;

            // テクスチャタイプのパターン作成
            if (_colorDataType == ColorDataType.Texture)
            {
                // 違うパターンがあったら登録する
                for (i = 0; i < ChoreographyCyalumeDataArray.Length; i++)
                {
                    count = patterns.Count;
                    choreographyCyalumePattern = ChoreographyCyalumeDataArray[i];
                    if (choreographyCyalumePattern.IsTypeChoreography == false)
                    {
                        continue;
                    }
                    // モブ色設定コマンドは例外なのでパターン作成には考慮しない
                    if (choreographyCyalumePattern._colorPattern == ColorPattern.SetMobColor)
                    {
                        continue;
                    }
                    for (j = 0; j < patterns.Count; j++)
                    {
                        pattern = patterns[j];
                        if (pattern._choreographyType != choreographyCyalumePattern._choreographyType)
                        {
                            continue;
                        }
                        if (pattern._colorPattern != choreographyCyalumePattern._colorPattern)
                        {
                            continue;
                        }
                        if (pattern._colorCount != choreographyCyalumePattern._colorCount)
                        {
                            continue;
                        }

                        // 色関係の探索、全て同じならk==_colorCountになる
                        for (k = 0; k < pattern._colorCount; k++)
                        {
                            if (pattern._colorData[k].InColor != choreographyCyalumePattern._colorData[k].InColor)
                            {
                                break;
                            }
                            if (pattern._colorData[k].OutColor != choreographyCyalumePattern._colorData[k].OutColor)
                            {
                                break;
                            }
                            
                            if (!Math.IsFloatEqual(pattern._colorWidth[k], choreographyCyalumePattern._colorWidth[k]))
                            {
                                break;
                            }
                        }
                        if (k != pattern._colorCount)
                        {
                            continue;
                        }
                        break; // 同じのがあったので抜ける
                    }

                    // パターンIDをつけてアクセスを高速化
                    if (j >= MAX_TEXTURE_PATTERN)
                    {
                        ChoreographyCyalumeDataArray[i].PatternId = MAX_TEXTURE_PATTERN - 1;
                    }
                    else
                    {
                        ChoreographyCyalumeDataArray[i].PatternId = j;
                    }

                    // 同じのが無かったので追加
                    if (j == count)
                    {
                        patterns.Add(ChoreographyCyalumeDataArray[i]);
                    }
                }
            }
            // メッシュタイプのパターンを作成
            else
            {
                // カラーのパターンだけ集計しなおす
                for (i = 0; i < ChoreographyCyalumeDataArray.Length; i++)
                {
                    count = patterns.Count;
                    choreographyCyalumePattern = ChoreographyCyalumeDataArray[i];
                    if (choreographyCyalumePattern.IsTypeChoreography == false)
                    {
                        continue;
                    }
                    // モブ色設定コマンドは例外なのでパターン作成には考慮しない
                    if (choreographyCyalumePattern._colorPattern == ColorPattern.SetMobColor)
                    {
                        continue;
                    }
                    for (j = 0; j < patterns.Count; j++)
                    {
                        pattern = patterns[j];
                        if (pattern._choreographyType != choreographyCyalumePattern._choreographyType)
                        {
                            continue;
                        }
                        if (pattern._colorPattern != choreographyCyalumePattern._colorPattern)
                        {
                            continue;
                        }
                        if (pattern._colorCount != choreographyCyalumePattern._colorCount)
                        {
                            continue;
                        }

                        // 色関係の探索、全て同じならk==_colorCountになる
                        for (k = 0; k < pattern._colorCount; k++)
                        {
                            if (pattern._colorData[k].InColor != choreographyCyalumePattern._colorData[k].InColor)
                            {
                                break;
                            }
                            if (pattern._colorData[k].OutColor != choreographyCyalumePattern._colorData[k].OutColor)
                            {
                                break;
                            }
                            if (!Math.IsFloatEqual(pattern._colorWidth[k], choreographyCyalumePattern._colorWidth[k]))
                            {
                                break;
                            }
                        }
                        if (k != pattern._colorCount)
                        {
                            continue;
                        }
                        break; // 同じのがあったので抜ける
                    }
                    // 同じのが無かったので追加
                    if (j == count)
                    {
                        patterns.Add(ChoreographyCyalumeDataArray[i]);
                    }

                    if (j >= MAX_MESH_PATTERN)
                    {
                        ChoreographyCyalumeDataArray[i].PatternId = MAX_MESH_PATTERN - 1;
                    }
                    else
                    {
                        ChoreographyCyalumeDataArray[i].PatternId = j;
                    }
                }
            }

            ChoreographyPatternArray = patterns.ToArray();

            // ログの出力
#if UNITY_EDITOR && CYG_DEBUG
            if (ChoreographyPatternArray.Length > 0)
            {
                string strTemp = GetChoreographyPatternString();
                if (_colorDataType == ColorDataType.Texture &&
                    ChoreographyPatternArray.Length > MAX_TEXTURE_PATTERN)
                {
                    Debug.LogError(strTemp);
                }
                else if (_colorDataType == ColorDataType.Mesh &&
                    ChoreographyPatternArray.Length > MAX_MESH_PATTERN)
                {
                    Debug.LogError(strTemp);
                }
                else
                {
                    Debug.Log(strTemp);
                }
            }
#endif
        }

        /// <summary>
        /// 配列のリストから読み込む
        /// </summary>
        /// <param name="records"></param>
        /// <returns></returns>
        public bool LoadFromArrayList(ArrayList records)
        {
            if (records == null)
            {
                return false;
            }

            ChoreographyCyalumeData choreographyCyalumeData;
            List<ChoreographyCyalumeData> listChoreographyCyalumeData = new List<ChoreographyCyalumeData>();
            ArrayList columns;
            string[] column;

            for (int i = 0; i < records.Count; i++)
            {
                columns = records[i] as ArrayList;
                column = columns.ToArray(typeof(string)) as string[];

                // パラメータチェック
                if (column[(int)CsvLabel.Time].Length <= 0)
                {
                    continue;
                }
                if (column[(int)CsvLabel.MoveType].Length <= 0)
                {
                    continue;
                }
                if (column[(int)CsvLabel.Bpm].Length <= 0)
                {
                    continue;
                }
                if (column[(int)CsvLabel.ColorPattern].Length <= 0)
                {
                    continue;
                }

                choreographyCyalumeData = new ChoreographyCyalumeData();

                choreographyCyalumeData.DataNo = i;
                choreographyCyalumeData.StartTime = TimeUtil.ConvertMilliSecondsToSeconds(int.Parse(column[(int)CsvLabel.Time]));
                choreographyCyalumeData.PlaySpeed = BpmToPlaySpeed(float.Parse(column[(int)CsvLabel.Bpm]));

                choreographyCyalumeData._choreographyType = (ChoreographyType)Enum.Parse(_typeofChoreographyType, column[(int)CsvLabel.MoveType]);
                // 振付のときは色情報を取得する
                if (choreographyCyalumeData.IsTypeChoreography == true)
                {
                    if (column[(int)CsvLabel.Color1].Length <= 0)
                    {
                        continue;
                    }
                    choreographyCyalumeData._colorPattern = (ColorPattern)Enum.Parse(_typeofColorPattern, column[(int)CsvLabel.ColorPattern]);
                    choreographyCyalumeData._colorCount = _colorCountTable[(int)choreographyCyalumeData._colorPattern];

                    // カラー情報を設定する
                    for (int j = 0; j < MAX_COLOR_COUNT; j++)
                    {
                        // カラー
                        if (column[(int)CsvLabel.Color1 + j].Length > 0)
                        {
                            var colorStr = column[(int)CsvLabel.Color1 + j];
                            if (CYALUME_COLOR_DATA_DICTIONARY.TryGetValue(colorStr, out var value))
                            {
                                choreographyCyalumeData._colorData[j] = value;
                            }
                            else
                            {
                                choreographyCyalumeData._colorData[j] = _defaultColor;
                            }
                        }
                        else
                        {
                            choreographyCyalumeData._colorData[j] = _defaultColor;
                        }

                        // 幅
                        if (column[(int)CsvLabel.Width1 + j].Length > 0)
                        {
                            choreographyCyalumeData._colorWidth[j] = float.Parse(column[(int)CsvLabel.Width1 + j]);
                        }
                        else
                        {
                            choreographyCyalumeData._colorWidth[j] = 0.0f;
                        }
                    }
                }

                listChoreographyCyalumeData.Add(choreographyCyalumeData);
            }

            if (listChoreographyCyalumeData.Count > 0)
            {
                ChoreographyCyalumeDataArray = listChoreographyCyalumeData.ToArray();
            }

            CreateChoreographyPattern();

            return true;
        }

        /// <summary>
        /// ファイルから振り付けデータを読み込む
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        public bool Load(string strPath)
        {
            // テキストの読み込み
            TextAsset textAsset = ResourceManager.LoadOnView<TextAsset>(strPath);
            if (textAsset == null)
            {
                Debug.LogError("textAsset is Null! path=\"" + strPath + "\"");
                return false;
            }
            return Create(textAsset.text);
        }

        /// <summary>
        /// 振り付けデータからパターンデータを生成する
        /// </summary>
        /// <param name="dataText"></param>
        /// <returns></returns>
        public bool Create(string dataText)
        {
            if (string.IsNullOrEmpty(dataText))
            {
                Debug.LogError("dataText is NullOrEmpty!");
                return false;
            }

            // CSVデータに変換
            ArrayList records = GallopUtil.ConvertCSV(dataText);
            return LoadFromArrayList(records);
        }

        /// <summary>
        /// 対象の振り付けデータを取得する
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        public ChoreographyCyalumeData GetChoreographyDataFromTime(float time)
        {
            if (ChoreographyCyalumeDataArray == null)
            {
                return null;
            }

            ChoreographyCyalumeData choreographyCyalumeData = null;

            for (int i = ChoreographyCyalumeDataArray.Length - 1; i >= 0; i--)
            {
                if (ChoreographyCyalumeDataArray[i] == null)
                {
                    continue;
                }
                if (time >= ChoreographyCyalumeDataArray[i].StartTime)
                {
                    choreographyCyalumeData = ChoreographyCyalumeDataArray[i];
                    break;
                }
            }

            return choreographyCyalumeData;
        }

        /// <summary>
        /// 対象番号のデータを取得する
        /// </summary>
        /// <param name="no"></param>
        /// <returns></returns>
        public ChoreographyCyalumeData GetChoreographyDataFromNo(int no)
        {
            if (ChoreographyCyalumeDataArray == null)
            {
                return null;
            }
            if ((no < 0) || (no >= ChoreographyCyalumeDataArray.Length))
            {
                return null;
            }

            return ChoreographyCyalumeDataArray[no];
        }
    }
}
