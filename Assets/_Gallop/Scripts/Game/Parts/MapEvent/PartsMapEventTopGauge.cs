using System;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsMapEventTopGauge : MonoBehaviour
    {
        [SerializeField]
        private GaugeCommon _gauge;

        [SerializeField] 
        public RectTransform _gaugeEffectFlashParent;

        private FlashPlayer _gaugeEffect = null;
        private FlashToUgui _flashToUgui = null;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.FLASH_MAP_EVENT_GAUGE_EFFECT_PATH);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void SetupGaugeEffect()
        {
            if (WorkDataManager.Instance.MapEventData.IsGaugeMax())
            {
                _gaugeEffect = FlashLoader.LoadOnView(ResourcePath.FLASH_MAP_EVENT_GAUGE_EFFECT_PATH, _gaugeEffectFlashParent, active: false);

                // A2U(Flash)生成
                _flashToUgui = _gaugeEffect.gameObject.AddComponent<FlashToUgui>();
                _flashToUgui.gameObject.AddComponent<RectTransform>();
                _flashToUgui.InitializeAndCreateClone(_gaugeEffect.gameObject, includeInactive: true, useMask2D: true, skipSetParentOnSortingOrder: true);
                _gaugeEffect.Init();
                _gaugeEffect.Play(GameDefine.A2U_IN00_LABEL);
            }
        }

        /// <summary>
        /// ゲージ演出再生
        /// </summary>
        public void PlayInGaugeEffect(Action onEndEffect)
        {
            if (_gaugeEffect == null || 
                !WorkDataManager.Instance.MapEventData.IsGaugeMax())
            {
                onEndEffect?.Invoke();
                return;
            }

            if (WorkDataManager.Instance.MapEventData.IsGaugeMax())
            {
                _gaugeEffect.SetActionCallBack(GameDefine.A2U_IN_END_LABEL, () =>
                {
                    onEndEffect?.Invoke();

                }, AnMotionActionTypes.Start);

                AudioManager.Instance.PlaySe(AudioId.SFX_MAPEVENT_UI_FULL);

                _gaugeEffect.SetActiveWithCheck(true);
                _gaugeEffect.Play(GameDefine.A2U_IN_LABEL);
            }
        }

        /// <summary>
        /// ゲージ演出停止
        /// </summary>
        public void StopGaugeEffect()
        {
            if (_gaugeEffect != null)
            {
                _gaugeEffect.Stop();
                _gaugeEffect.SetActiveWithCheck(false);
                _flashToUgui.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// ゲージ更新
        /// </summary>
        public void UpdateGauge()
        {
            var gaugeMaxValue = WorkDataManager.Instance.MapEventData.MapEventGaugeMax;
            var gaugeValue = WorkDataManager.Instance.MapEventData.GaugeValue;

            _gauge.Set(gaugeValue, gaugeMaxValue);
        }

        /// <summary>
        /// ゲージを空にする 
        /// </summary>
        public void GaugeEmpty()
        {
            var gaugeMaxValue = WorkDataManager.Instance.MapEventData.MapEventGaugeMax;
            _gauge.Set(0, gaugeMaxValue);

            WorkDataManager.Instance.MapEventData.ResetLatestGaugeInfo();
        }
    }
}
