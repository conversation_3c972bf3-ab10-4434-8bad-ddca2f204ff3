using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// マップイベント：「ゲージをためる」ダイアログの項目
    /// </summary>
    [AddComponentMenu("")]
    public class PartsMapEventChargeListItem : LoopScrollItemBase
    {
        /// <summary>アイコン</summary>
        [SerializeField] private ImageCommon _icon;

        /// <summary>項目名</summary>
        [SerializeField] private TextCommon _name;

        /// <summary>ボタン</summary>
        [SerializeField] private ButtonCommon _button;


        /// <summary>画面遷移する時に呼ぶ外部コールバック</summary>
        private System.Action _onChangeView = null;

        /// <summary>
        /// 更新（LoopScrollから呼ばれます）
        /// </summary>
        public void UpdateItem(DialogMapEventChargeList.ItemType itemType, System.Action onChangeView)
        {
            _onChangeView = onChangeView;

            switch (itemType)
            {
                // 育成
                case DialogMapEventChargeList.ItemType.SingleMode:
                    SetupSingleMode();
                    break;
                // チーム競技場
                case DialogMapEventChargeList.ItemType.TeamStadium:
                    SetupTeamStadium();
                    break;
            }
        }

        /// <summary>
        /// 育成向けにセットアップ
        /// </summary>
        private void SetupSingleMode()
        {
            // アイコン
            _icon.sprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.SingleMode));

            // 項目名
            _name.text = TextId.Common0273.Text();

            // ボタンを押せるか
            _button.SetInteractable(true);
            _button.SetNotificationMessage(string.Empty);

            // 押した時の処理
            _button.SetOnClick(() =>
            {
                // 遷移先からマップイベントトップに戻す
                var backableStateInfo = new BackableStateInfo(SceneDefine.ViewId.MapEvent);
                SceneManager.Instance.StackViewForBack(backableStateInfo);

                GotoSingleMode(OnChangeView);
            });
        }

        /// <summary>
        /// 育成へ移動
        /// (強化編成の「アイテム入手」ダイアログで「育成」ボタンを押した時と同じ処理を踏襲)
        /// </summary>
        private void GotoSingleMode(Action onChangeView)
        {
            if (WorkDataManager.Instance.SingleMode.IsExistPlayingData)
            {
                // 再開可能な場合
                DialogHomeSingleModeRestartConfirm.Open(() =>
                {
                    SingleModeChangeViewManager.GotoSingleModeView();
                }, () =>
                {
                    HomeTrainingButton.DeleteSingleModeData(() =>
                    {
                        //完了ダイアログを出す
                        var data = new DialogCommon.Data();
                        data.SetSimpleOneButtonMessage(
                            TextId.SingleMode0170,
                            TextId.SingleMode0171.Text(),
                            _ =>
                            {
                                SingleModeChangeViewManager.GotoSingleModeView();
                            });
                        data.AutoClose = false;
                        DialogManager.PushDialog(data);
                    });
                }, null, DialogCommon.DispStackType.DialogOnDialog);
            }
            else
            {
                onChangeView?.Invoke();
                SingleModeChangeViewManager.GotoSingleModeView();
            }
        }

        /// <summary>
        /// チーム競技場向けにセットアップ
        /// </summary>
        private void SetupTeamStadium()
        {
            // アイコン
            _icon.sprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.GetPartsDialogItemPlaceIconPath(PartsDialogItemPlaceListItem.IconImageType.TeamStadium));

            // 項目名
            _name.text = TextId.Race0583.Text();

            // ボタンを押せるか
            _button.SetInteractable(true);
            _button.SetNotificationMessage(string.Empty);

            // 押した時の処理
            _button.SetOnClick(() =>
            {
                // 遷移先からマップイベントトップに戻す
                var backableStateInfo = new BackableStateInfo(SceneDefine.ViewId.MapEvent);
                SceneManager.Instance.StackViewForBack(backableStateInfo);

                // チーム競技場TOPへ遷移
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.TeamStadium);

                OnChangeView();
            });
        }

        /// <summary>
        /// 画面遷移する時の共通処理
        /// </summary>
        private void OnChangeView()
        {
            _onChangeView?.Invoke();
            UIManager.Instance.HideFooter();
        }
    }
}
