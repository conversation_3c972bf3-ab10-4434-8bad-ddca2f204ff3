using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// マンスリーマッチ：ホームからイベントトップへ遷移するボタン
    /// </summary>
    public class PartsRatingRaceButton : MonoBehaviour
    {
        #region private変数
        
        /// <summary> 「！」バッジの位置 </summary>
        private Vector3 BADGE_POS_OFFSET = new Vector3(201f, 154f, 0f);

        [SerializeField]
        private FlickableButton _flickableButton;
        
        [SerializeField]
        private RawImageCommon _logoImage;
        
        [SerializeField]
        private TextCommon _remainTimeText;

        /// <summary> 「！」バッジ </summary>
        private BadgeCommon _exclamationBadge = null;
        
        /// <summary> 「出走中」バッジ </summary>
        [SerializeField]
        private GameObject _resumeBadge = null;

        /// <summary>「チャレンジ発生中」/「フィナーレ開催中」テキスト画像</summary>
        [SerializeField] private GameObject _phaseTextBase = null;
        [SerializeField] private RawImageCommon _phaseTextImgae = null;

        #endregion

        #region public変数、プロパティ

        /// <summary>
        /// イベントの開始日時
        /// </summary>
        public long StartDate { private set; get; }

        private bool _isClicked = false;

        #endregion

        #region staticメソッド

        /// <summary>
        /// 遷移ボタンを表示するか
        /// </summary>
        /// <returns></returns>
        private static bool IsNeedRatingRaceButton()
        {
            // チュートリアル中は生成しない
            if (Tutorial.TutorialManager.IsTutorialExecuting())
            {
                return false;
            }

            // 開催中 or 開催当日ならtrueを返す
            return RatingRaceUtil.IsEventOpenOrFirstDay();
        }

        /// <summary>
        /// ボタンを表示すべきならリソースを落とす
        /// </summary>
        public static void RegisterDownloadIfNeeded(DownloadPathRegister register)
        {
            if (!IsNeedRatingRaceButton())
            {
                return;
            }
            
            register.RegisterPathWithoutInfo(ResourcePath.RATING_RACE_LOGO_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.RATING_RACE_BUTTON_TEXTURE_WEEK_END_TEXT_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.RATING_RACE_BUTTON_TEXTURE_FINALE_TEXT_PATH);
        }

        /// <summary>
        /// ボタンの生成・セットアップ
        /// </summary>
        public static PartsRatingRaceButton CreateAndSetupIfNeeded(Transform parentTrans, FlickHandler flickHandler)
        {
            if (!IsNeedRatingRaceButton())
            {
                return null;
            }

            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_RATING_RACE_BUTTON);
            if (prefab == null)
            {
                return null;
            }

            var ratingRaceButton = GameObject.Instantiate<GameObject>(prefab, parentTrans).GetComponent<PartsRatingRaceButton>();
            ratingRaceButton.Setup(flickHandler);

            return ratingRaceButton;
        }
        
        #endregion


        /// <summary>
        /// 画面イリ処理（MyPageHomeTopUIのplayInのタイミングで呼ばれる）
        /// </summary>
        public void UpdateOnPlayIn()
        {
            UpdateUI();
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(FlickHandler flickHandler)
        {
            this.gameObject.SetActiveWithCheck(false);
            
            var master = WorkDataManager.Instance.RatingRaceData.CurrentMasterRatingRaceData;
            StartDate = master?.StartDate ?? 0;

            _flickableButton.FlickHandler = flickHandler;
            _flickableButton.SetOnClick(OnClick);
            _flickableButton.InitializeCollision(true);
        }

        /// <summary>
        /// フリック機能の有効/無効を設定
        /// </summary>
        public void SetEnableFlick(bool isEnable)
        {
            _flickableButton.IsEnableFlick = isEnable;
        }

        /// <summary>
        /// UI更新
        /// </summary>
        private void UpdateUI()
        {
            this.gameObject.SetActiveWithCheck(false);
            
            var workRatingRaceData = WorkDataManager.Instance.RatingRaceData;
            // イベント期間外なら表示しない
            if (!workRatingRaceData.IsOpenRealTime())
            {
                return;
            }
            
            // UIのDLが済んでいなければ表示しない
            if (!ResourceManager.IsExistAsset(ResourcePath.RATING_RACE_LOGO_PATH))
            {
                return;
            }
            
            // 表示できるならロゴ画像とバッジとテキストの設定
            UpdateLogo(workRatingRaceData.Phase);
            UpdateExclamationBadge();
            UpdateRemainTimeText();
            
            // 出走中バッジ
            UpdateResumeBadge();

            // 「チャレンジ発生中」/「フィナーレ開催中」テキスト画像の更新
            UpdatePhaseTextImage(workRatingRaceData.Phase);

            this.SetActiveWithCheck(true);
        }
        
        /// <summary>
        /// ロゴ画像の更新
        /// </summary>
        private void UpdateLogo(RatingRaceDefine.Phase phase)
        {
            _logoImage.texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.RATING_RACE_LOGO_PATH);

            // ウィークエンドかフィナーレの時は位置を高くする
            var pos = UIUtil.GetAnchoredPosition(_logoImage.transform);
            if (phase == RatingRaceDefine.Phase.WeekEnd ||
                (phase == RatingRaceDefine.Phase.Finale && WorkDataManager.Instance.RatingRaceData.CurrentRace.FinaleRemainingCount > 0))
            {
                const float UP_POS_Y = 64f;
                pos = new Vector2(pos.x, UP_POS_Y);
            }
            else
            {
                const float DEFAULT_POS_Y = 29f;
                pos = new Vector2(pos.x, DEFAULT_POS_Y);
            }
            UIUtil.SetAnchoredPosition(_logoImage.transform, pos);
        }

        /// <summary>
        /// 「！」バッジが必要か
        /// </summary>
        public static bool IsNeedExclamationBadge()
        {
            return false;
        }
        
        /// <summary>
        /// 「！」バッジを生成する
        /// </summary>
        private BadgeCommon CreateExclamationBadge(Transform parent, Vector3 badgePosOffset)
        {
            var badge = BadgeCommon.CreateBadge(parent);
            if (badge != null)
            {
                badge.transform.localPosition = badgePosOffset;
            }
            return badge;
        }

        /// <summary>
        /// 「！」バッジの更新
        /// </summary>
        private void UpdateExclamationBadge()
        {
            bool needBadge = IsNeedExclamationBadge();

            // 必要なら生成
            if (_exclamationBadge == null && needBadge)
            {
                _exclamationBadge = CreateExclamationBadge(parent: _flickableButton.transform, badgePosOffset: BADGE_POS_OFFSET);
            }

            // 表示の更新
            if (_exclamationBadge != null)
            {
                _exclamationBadge.SetActiveWithCheck(needBadge);
            }
        }

        /// <summary>
        /// 残り期間テキスト更新
        /// </summary>
        private void UpdateRemainTimeText()
        {
            var workRatingRaceData = WorkDataManager.Instance.RatingRaceData;
            var isInSession = workRatingRaceData.IsInSessionRealTime();
            
            if (isInSession)
            {
                // 開催期間中の文言
                var masterRatingRaceData = workRatingRaceData.CurrentMasterRatingRaceData;
                if (masterRatingRaceData != null)
                {
                    _remainTimeText.text = TimeUtil.GetRestTimeString(
                        masterRatingRaceData.EndDate, 
                        TextId.StoryEvent0041.Text(), 
                        TextId.StoryEvent0042.Text(), 
                        TextId.StoryEvent0043.Text());
                }
            }
        }

        /// <summary>
        /// コールバック：ボタン押下
        /// </summary>
        private void OnClick()
        {
            if (_isClicked)
                return;
            _isClicked = true;

            // 開催中かチェック（期間外の場合エラーダイアログ ＆ ホーム遷移）
            if (!RatingRaceUtil.CheckEventOpen())
            {
                OpenErrorDialogNotInTerm();
                return;
            }

            // 遷移先で「戻る」を押したらホームへ戻るようにしておく
            StackViewForBack();

            // 中断データがあるなら
            if (RatingRaceUtil.HasResumeState())
            {
                // 再開処理
                RatingRaceUtil.ResumeProcess();
                return;
            }

            // イベントTOPへ遷移
            UIManager.Instance.StartCoroutine(RatingRaceUtil.ChangeTopViewWithSendRatingRaceIndexAPI());
        }

        /// <summary>
        /// 遷移先で「戻る」を押したらホームへ戻るようにしておく
        /// </summary>
        private void StackViewForBack()
        {
            SceneManager.Instance.ClearBackStack();

            var backableStateInfo = new BackableStateInfo(SceneDefine.ViewId.HomeHub,
                new HomeHubViewController.HomeHubViewInfo()
                {
                    DefaultViewId = SceneDefine.ViewId.Home,
                });

            SceneManager.Instance.StackViewForBack(backableStateInfo);
        }

        /// <summary>
        /// 「出走中」バッジが必要か
        /// </summary>
        public static bool IsNeedResumeBadge()
        {
            bool hasResumeState = WorkDataManager.Instance.RatingRaceData.CurrentRace.HasResumeState();
            return hasResumeState;
        }
        
        /// <summary>
        /// 「出走中」バッジの更新
        /// </summary>
        private void UpdateResumeBadge()
        {
            bool needBadge = IsNeedResumeBadge();

            _resumeBadge.SetActiveWithCheck(needBadge);
        }
        
        /// <summary>
        /// 「チャレンジ発生中」/「フィナーレ開催中」テキスト画像の更新
        /// </summary>
        private void UpdatePhaseTextImage(RatingRaceDefine.Phase phase)
        {
            _phaseTextBase.SetActiveWithCheck(false);

            if (phase == RatingRaceDefine.Phase.WeekEnd)
            {
                _phaseTextBase.SetActiveWithCheck(true);
                _phaseTextImgae.texture = ResourceManager.LoadOnScene<Texture2D>(ResourcePath.RATING_RACE_BUTTON_TEXTURE_WEEK_END_TEXT_PATH);
            }
            else if (phase == RatingRaceDefine.Phase.Finale && WorkDataManager.Instance.RatingRaceData.CurrentRace.FinaleRemainingCount > 0)
            {
                _phaseTextBase.SetActiveWithCheck(true);
                _phaseTextImgae.texture = ResourceManager.LoadOnScene<Texture2D>(ResourcePath.RATING_RACE_BUTTON_TEXTURE_FINALE_TEXT_PATH);
            }
        }

        /// <summary>
        /// 期間外エラーダイアログを出す
        /// </summary>
        public static void OpenErrorDialogNotInTerm()
        {
            // 終了時刻を超えていた場合、ダイアログを出して強制遷移
            var resultCode = GallopResultCode.RATING_RACE_NOT_IN_TERM;
            var header = TextUtil.GetMasterText(MasterString.Category.ErrorHeader, resultCode);
            var message = TextUtil.GetMasterText(MasterString.Category.ErrorMessage, resultCode);
            DialogManager.PushErrorCommon(message, header, null, GallopResultCode.OpenErrorPopupType.Home);
        }
    }
}