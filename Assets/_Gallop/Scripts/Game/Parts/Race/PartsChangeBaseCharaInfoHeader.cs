using System;
using UnityEngine;

namespace Gallop
{
    using static TeamStadiumDeckInfo;

    /// <summary>
    /// チーム競技場：デッキ編成：キャラクター選択用のヘッダー
    /// </summary>
    [AddComponentMenu("")]
    public class PartsChangeBaseCharaInfoHeader : MonoBehaviour
    {
        [SerializeField]
        private PartsCharacterNamePlate _namePlate = null;

        [SerializeField]
        private CharacterButton _characterButton = null;

        [SerializeField]
        private PartsButtonAccessory _characterButtonAccessory = null;

        [SerializeField]
        private ImageCommon _properGround = null;

        [SerializeField]
        private TextCommon _properGroundText = null;

        [SerializeField]
        private TextCommon _properGroundNoneText = null;

        [SerializeField]
        private ImageCommon _properDistance = null;

        [SerializeField]
        private TextCommon _properDistanceText = null;

        [SerializeField]
        private TextCommon _properDistanceNoneText = null;

        [SerializeField]
        private ImageCommon _blankRankImage = null;

        [SerializeField]
        private TextCommon _blankName = null;

        private CharacterButtonInfo _buttonInfo;

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(MemberInfo memberInfo, MasterRaceCourseSet.IRaceCourseInfo courseInfo, Action<CharacterButton> onTapCharaButton)
        {
            SetupCharacterButton(memberInfo, onTapCharaButton);
            SetupProperInfo(memberInfo, courseInfo);

            _blankRankImage.SetActiveWithCheck(memberInfo.IsEmpty);
            _blankName.SetActiveWithCheck(memberInfo.IsEmpty);
            _namePlate.SetActiveWithCheck(!memberInfo.IsEmpty);

            if(!memberInfo.IsEmpty)
            {
                _namePlate.Setup(memberInfo.TrainedCharaData);
            }
        }

        /// <summary>
        /// 通常セットアップ
        /// </summary>
        private void SetupCharacterButton(MemberInfo memberInfo, Action<CharacterButton> onTap)
        {
            _characterButton.SetSizeType(IconBase.SizeType.CharaSmall);
            _characterButtonAccessory.SetActiveWithCheck(true);
            _characterButtonAccessory.SetActiveReservedOrangeLabel(true);
            _characterButtonAccessory.SetReservedOrangeLabelText(TeamStadiumUtil.GetRaceName(memberInfo.RaceNumber));
            _characterButtonAccessory.SetActiveReservedOrangePos(new Vector2Int(-28, 46));

            if (memberInfo.IsEmpty)
            {
                SetupEmpty();
            }
            else
            {
                _buttonInfo = new CharacterButtonInfo()
                {
                    Id = memberInfo.TrainedCharaData.CardId,
                    IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                    TrainedChara = memberInfo.TrainedCharaData,
                    RankImage = false,
                    RankScore = memberInfo.TrainedCharaData.RankScore,
                    SortInfoType = CharacterButtonInfo.SortInfo.RankScore,
                    OnTap = button => onTap.Invoke(button),
                };
                _characterButton.Setup(_buttonInfo);
            }
        }

        /// <summary>
        /// 未設定セットアップ
        /// </summary>
        private void SetupEmpty()
        {
            _characterButton.Setup(new CharacterButtonInfo()
            {
                Id = 0,
                IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                SortInfoType = CharacterButtonInfo.SortInfo.Default,
            });
            _characterButton.SetEnable(true);
            _characterButton.MyButton.SeType = ButtonCommon.ButtonSeType.DecideM01;
            _characterButton.SetSortInfoText(CharacterButtonInfo.SortInfo.None);
        }

        /// <summary>
        /// 現在のソート設定を反映
        /// </summary>
        public void SetupCurrnetSortSetting(SortMenu currentSortMenu, bool isSortAsc)
        {
            
            switch (currentSortMenu)
            {
                case SortMenu.CardRarity:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.LimitBreak;
                    break;
                case SortMenu.CardLevel:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.TalentLevel;
                    break;
                case SortMenu.ProperGround:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.GroundType;
                    break;
                case SortMenu.ProperDistance:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.Distance;
                    break;
                case SortMenu.ProperRunningStyle:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.RunningStyle;
                    break;
                case SortMenu.LoveRank:
                case SortMenu.LovePoint:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.LoveRank;
                    break;
                case SortMenu.RankScore:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.RankScore;
                    break;
                case SortMenu.TrainedFactor:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.Factor;
                    break;
                case SortMenu.TrainedSkillNum:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.SkillCount;
                    break;
                case SortMenu.StoryEventCardBonus:
                case SortMenu.FanRaidBonusCard:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.LimitBreak;
                    break;
                default:
                    _buttonInfo.SortInfoType = CharacterButtonInfo.SortInfo.Default;
                    break;
            }

            // ヘッダーのキャラアイコンのお気に入りアイコンは反映しない
            _buttonInfo.IsLock = false;

            // ソートの昇順降順の反映.
            _buttonInfo.IsSortAsc = isSortAsc;

            _characterButton.Setup(_buttonInfo);
        }

        /// <summary>
        /// 育成データから適正グレード表示
        /// </summary>
        private void SetupProperInfo(MemberInfo memberInfo, MasterRaceCourseSet.IRaceCourseInfo courseInfo = null)
        {
            _properGroundNoneText.SetActiveWithCheck(memberInfo.TrainedCharaData == null);
            _properDistanceNoneText.SetActiveWithCheck(memberInfo.TrainedCharaData == null);
            _properGround.SetActiveWithCheck(memberInfo.TrainedCharaData != null);
            _properGroundText.SetActiveWithCheck(memberInfo.TrainedCharaData != null);
            _properDistance.SetActiveWithCheck(memberInfo.TrainedCharaData != null);
            _properDistanceText.SetActiveWithCheck(memberInfo.TrainedCharaData != null);

            if (memberInfo.TrainedCharaData != null)
            {
                _properGround.sprite =  TeamStadiumUtil.GetProperGroundGradeSprite(memberInfo.TrainedCharaData, memberInfo.RaceNumber);
                _properDistance.sprite = TeamStadiumUtil.GetProperDistanceGradeSprite(memberInfo.TrainedCharaData, memberInfo.RaceNumber);

                _properGroundText.text = courseInfo.GroundType.Text();
                _properDistanceText.text = TeamStadiumUtil.GetDistanceType(memberInfo.RaceNumber).Text();
            }
        }
    }
}
