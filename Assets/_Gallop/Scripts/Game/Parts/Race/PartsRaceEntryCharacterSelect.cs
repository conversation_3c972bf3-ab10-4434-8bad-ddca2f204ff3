using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Gallop.Tutorial;
using UnityEngine;
using UnityEngine.Serialization;
using static Gallop.StaticVariableDefine.Race.PartsRaceEntryCharacterSelectStatic;

namespace Gallop
{
    using static TeamStadiumDeckInfo;
    using static WorkTrainedCharaData;

    /// <summary>
    /// 出馬選択画面用複合パーツ
    /// <see href="https://xxxxxxxxxx/app/#/projects/5cff60904942fb496a4cbdc9/screens/5dfae76b86e4655f899a5921">デイリーレース・ウマ娘選択</see>
    /// <see href="https://xxxxxxxxxx/app/#/projects/5cff60904942fb496a4cbdc9/screens/5de8ac7986e4654d3716cad3">模擬レース・ウマ娘選択</see>
    /// などで使用
    /// </summary>
    public class PartsRaceEntryCharacterSelect : MonoBehaviour
    {
        /// <summary>
        /// 画面タイプ
        /// </summary>
        public enum ViewType
        {
            Daily,          // デイリー
            Legend,         // レジェンド
            Story,          // ストーリー
            TeamEdit,       // チーム編成
            Champions,      //チャンピオンズミーティング
            RoomMatch,      // ルームマッチ
            PracticeRace,       // 練習：出走ウマ娘選択
            PracticeDeckEdit,   // 練習：マイ出走リスト
            Challenge,      // チャレンジマッチ
            TeamBuilding,   // チーム作りイベント
            Heroes,         // リーグオブヒーローズ
            Ultimate,       // マスターズチャレンジ
            RatingRace,     // マンスリーマッチ
        }

        // <summary>
        /// 画面タイプ別データ
        /// </summary>
        public class ViewTypeData
        {
            public string BgPath { get; }
            public string EnvPath { get; }

            public ViewTypeData(string bgPath, string envPath)
            {
                BgPath = bgPath;
                EnvPath = envPath;
            }
        }

        /// <summary>
        /// セットアップ用クラス
        /// </summary>
        public class SetupParam
        {
            /// <summary>
            /// 画面タイプ
            /// </summary>
            public ViewType ViewType;

            /// <summary>
            /// 最大選択数
            /// </summary>
            public int MaxSelectCount = 1;

            /// <summary>
            /// 殿堂入りウマ娘
            /// </summary>
            public List<TrainedCharaData> TrainedCharaList;

            /// <summary>
            /// 練習パートナー(フォロー込み)
            /// </summary>
            public List<TrainedCharaData> PartnerTrainedCharaList;

            /// <summary>
            /// 初期選択データリスト
            /// </summary>
            public List<TrainedCharaData> InitialSelectedList;

            /// <summary>
            /// メインストーリーレースデータ
            /// </summary>
            public MasterMainStoryRaceData.MainStoryRaceData MainStoryRaceData;

            /// <summary>
            /// レースインスタンス
            /// </summary>
            public MasterRaceInstance.RaceInstance RaceInstance;

            /// <summary>
            /// レース条件（天候、季節、馬場）
            /// </summary>
            public MasterRaceCondition.RaceCondition RaceCondition;

            /// <summary>
            /// コース情報
            /// </summary>
            public MasterRaceCourseSet.IRaceCourseInfo CourseInfo;

            /// <summary>
            /// 下部のテキスト付きの決定ボタンを使用するか
            /// </summary>
            public bool IsUseSelectButtonWithText = false;

            /// <summary>
            /// 選択時コールバック
            /// (キャラのボタンを押す度呼ばれる)
            /// </summary>
            public Action<TrainedCharaData> OnSelect;

            /// <summary>
            /// 決定コールバック
            /// </summary>
            public Action<List<TrainedCharaData>> OnDecide;

            /// <summary>右上のステータスパネル更新時コールバック</summary>
            public Action<TrainedCharaData> OnUpdateStatusPanel;

            public bool EnableMultiSelect => MaxSelectCount > 1;

            public bool EnableTab => PartnerTrainedCharaList != null;

            /// <summary>
            /// 既にデッキに編成されているキャラ
            /// </summary>
            public Dictionary<TrainedCharaData, string> ReservedCharaDic;

            /// <summary>
            /// 選択不可キャラIdリスト
            /// </summary>
            public List<int> UnselectableCharaIdList;

            /// <summary>
            /// 選択不可育成済みIDリスト
            /// </summary>
            public List<int> UnselectableTrainedCharaIdList;

            /// <summary>
            /// 選択不可だがロングタップで詳細が開けるキャラのリスト
            /// </summary>
            public List<TrainedCharaData> UnselectableCanLongTapTrainedCharaList;

            /// <summary>
            /// 選択できるが訳アリのキャラのリスト（チーム競技場の選択可能「ウマ娘重複キャラ」など）
            /// </summary>
            public List<int> SelectableWithReasonCharaIdList;

            /// <summary>
            /// 選択不可理由取得関数
            /// </summary>
            public Func<TrainedCharaData, string> GetUnselectableReason;

            /// <summary>
            /// ウマ娘重複など選択できない時のアイコンテキスト取得
            /// </summary>
            public Func<TrainedCharaData, string> GetUnselectableNoticeIconText;

            /// <summary>
            /// ウマ娘重複など選択できない時のアイコンテキスト取得(！なし)
            /// </summary>
            public Func<TrainedCharaData, bool> IsUnselectableNoticeIconWithoutMark;

            /// <summary>
            /// 選択できるが補足がある場合のテキスト取得（チーム競技場の選択可能「ウマ娘重複キャラ」など）
            /// </summary>
            public Func<TrainedCharaData, string> GetSelectableNoticeIconText;

            public CharacterButtonNoticeModelFactory CharacterButtonNoticeModelFactory;

            /// <summary>
            /// 選択解除可能か？
            /// </summary>
            public bool Removable = false;

            /// <summary>
            /// ViewTypeが練習のものか
            /// </summary>
            public bool IsViewTypePractice
            {
                get
                {
                    switch (ViewType)
                    {
                        case ViewType.PracticeDeckEdit:
                        case ViewType.PracticeRace:
                            return true;
                    }

                    return false;
                }
            }

            /// <summary>
            /// EXグレード表記
            /// </summary>
            /// <returns></returns>
            public bool IsExGrade()
            {
                switch (ViewType)
                {
                    case ViewType.Daily:
                    case ViewType.Legend:
                    case ViewType.Champions:
                    case ViewType.RoomMatch:
                    case ViewType.Challenge:
                    case ViewType.Heroes:
                    case ViewType.Ultimate:
                    case ViewType.RatingRace:
                        return true;
                }
                return false;
            }

            /// <summary>
            /// 選択不可キャラか？
            /// </summary>
            /// <param name="trainedChara"></param>
            /// <returns></returns>
            public bool IsUnselectableChara(TrainedCharaData trainedChara)
            {
                // タップ不可チェック
                var isNotIntaractive = IsNotInteractiveChara(trainedChara);
                if (isNotIntaractive)
                {
                    return isNotIntaractive;
                }

                // 選択不可キャラ(タップ可能)
                return IsUnselectableCanLongTapTrainedChara(trainedChara);
            }

            /// <summary>
            /// タップ不可のキャラか
            /// </summary>
            /// <param name="trainedChara"></param>
            /// <returns></returns>
            public bool IsNotInteractiveChara(TrainedCharaData trainedChara)
            {
                if (trainedChara == null)
                {
                    // 外す用のキャラボタンだとnullで来る
                    return true;
                }

                var isUnselectableChara = false;
                var isUnselectableTrainedChara = false;
                if (UnselectableCharaIdList != null)
                {
                    // 編成済みキャラとの入れ替えは可能
                    if (ReservedCharaDic != null && ReservedCharaDic.Any(x => x.Key.Id == trainedChara.Id && x.Key.ViewerId == trainedChara.ViewerId))
                    {
                        isUnselectableChara = false;
                    }
                    else
                    {
                        isUnselectableChara = UnselectableCharaIdList.Contains(trainedChara.CharaId);
                    }
                }
                if (UnselectableTrainedCharaIdList != null)
                {
                    //編成不可育成済みID
                    isUnselectableTrainedChara = UnselectableTrainedCharaIdList.Contains(trainedChara.Id);
                }

                return isUnselectableChara || isUnselectableTrainedChara;
            }

            /// <summary>
            /// 選択不可かつタップ可能なキャラか
            /// </summary>
            /// <param name="trainedChara"></param>
            /// <returns></returns>
            public bool IsUnselectableCanLongTapTrainedChara(TrainedCharaData trainedChara)
            {
                if (trainedChara != null && UnselectableCanLongTapTrainedCharaList != null)
                {
                    // 選択不可キャラ(タップ可能)
                    return UnselectableCanLongTapTrainedCharaList.Any(chara => chara.ViewerId == trainedChara.ViewerId && chara.Id == trainedChara.Id);
                }

                return false;
            }

            /// <summary>
            /// 選択できるが補足のあるキャラか
            /// </summary>
            /// <param name="trainedChara"></param>
            /// <returns></returns>
            public bool IsSelectableWithReasonChara(TrainedCharaData trainedChara)
            {
                var isSelectableWithReasonChara = false;
                if (trainedChara != null && SelectableWithReasonCharaIdList != null)
                {
                    isSelectableWithReasonChara = SelectableWithReasonCharaIdList.Contains(trainedChara.CharaId);
                }

                return isSelectableWithReasonChara;
            }

            /// <summary>
            /// 変更元のデッキ編成メンバー情報
            /// </summary>
            public MemberInfo SelectedMemberInfo;

            //他に設定中のキャラ
            public List<TrainedCharaData> SettingCharaList = new List<TrainedCharaData>();

            //チャンピオンズミーティングのレース名にラウンド・グループを表示するために指定
            public MasterChampionsRoundDetail.ChampionsRoundDetail ChampionsRoundDetail = null;

            //ルームマッチ部屋名、状態表示用
            public WorkRoomMatchData.RoomData RoomMatchRoomData = null;

            //練習状態表示用
            public PracticeRaceRaceSettingInfo PracticeSettingInfo = null;

            //適性表示全部表示するか対象レースのみ表示するか
            public PartsSingleModeStartProperStatus.DispType ProperDispType = PartsSingleModeStartProperStatus.DispType.All;
            
            //マスターズチャレンジのレースコンテンツID
            public int UltimateRaceContentsId;

            /// <summary>
            /// デイリーレジェンドレースかどうか(ソート設定でレジェンドレースと区別するために使用)
            /// </summary>
            public bool IsDailyLegendRace = false;

            //ステータスの右上に注意文言出すか
            public string NoticeText = null;
            //注意文言(iボタン付き)
            public string NoticeWithDetailText = null;
            //注意文言のiボタン
            public Action NoticeWithDetailAction = null;
            //注意文言の下地が大きいか
            public bool UseLargeNotice = false;

            /// <summary>
            /// 最大出走人数を表示(MaxSelectCountが1の時用)
            /// </summary>
            public string EntryNumText = null;

            /// <summary>
            /// リストが変動する場合にSetupParamを作り直す
            /// </summary>
            public Func<SetupParam> CreateSetupPraramFunc = null;

            /// <summary>
            /// リストが変動する場合にダウンロード登録を再度行う
            /// </summary>
            public Action<Action> RegisterDownload = null;

            /// <summary>
            /// 空の要素の時に探しにいくボタンをタップしたときのコールバック
            /// </summary>
            public Action OnClickFindButton = null;
        }

        public const int TAB_TRAINED = 0;
        public const int TAB_PERTNER = 1;

        // スキル一覧の高さ調整する時、被る情報のサイズが大きいときは2:1のアスペクト比で調整
        public const float TARGET_ASPECT_LARGE_NOTICE = 0.5f;

        /// <summary>
        /// iPhoneXのセーフエリアよりもヘッダーをずらしたいので、ContentsRoot計算は独自に行う。
        /// </summary>
        [SerializeField]
        private RectTransform _contentsRoot = null;

        [SerializeField]
        private RectTransform _overSafeAreaContentsRoot = null;

        [SerializeField]
        private PartsRaceInfoHeader _raceInfoHeader = null;

        [SerializeField]
        private PartsChangeBaseCharaInfoHeader _partsChangeBaseCharaInfoHeader = null;

        [SerializeField]
        private FlickToggleGroupCommon _toggleGroup = null;

        [SerializeField]
        private PartsCardListVertical _cardList = null;

        [SerializeField]
        private PartsButtonAccessory _buttonAccessoryPrefab = null;

        [SerializeField]
        private PartsCharacterNamePlate _namePlate = null;

        [SerializeField]
        private PartsSingleModeCharacterStatusPanel _statusPanel = null;
        
        [SerializeField]
        private PartsCharacterDetailStatus _detailStatusFrame = null;

        [SerializeField]
        private GameObject _selectTextObject = null;

        [SerializeField]
        private PartsCharacterNamePlateRibbon _ribbon = null;

        [SerializeField]
        private GameObject _entryNumBase = null;

        [SerializeField]
        private TextCommon _entryNum = null;

        [SerializeField]
        private ButtonCommon _selectButton = null;

        [SerializeField] 
        private GameObject _selectButtonNormalLabel = null;

        [SerializeField] 
        private GameObject _selectButtonLabelWithText = null;

        [SerializeField] 
        private TextCommon _selectButtonCountLabel = null;

        [SerializeField]
        private ButtonCommon _charaTapButton = null;

        [SerializeField]
        private GameObject _noticeRoot = null;

        [SerializeField]
        private TextCommon _noticeText = null;
        public TextCommon NoticeText => _noticeText;

        [SerializeField]
        private TextCommon _noticeTextWithDetail = null;

        [SerializeField]
        private ButtonCommon _noticeDetailButton = null;

        [SerializeField]
        private FlickableObject _flickableObject = null;

        // 2つ目のタブが空だった場合の処理
        /// <summary> 空だった時に表示するオブジェクト </summary>
        [SerializeField]
        private GameObject _emptyObjctRoot = null;
        /// <summary> 対象キャラを探しに行くボタン </summary>
        [SerializeField]
        private ButtonCommon _findButtonIfEmpty = null;

        private SetupParam _setupParam;

        private MasterRaceCourseSet.IRaceCourseInfo _courseInfo;

        /// <summary>
        /// 現在選択しているデータ
        /// </summary>
        private TrainedCharaData[] _currentSelectDataArray;

        /// <summary>
        /// 現在選択しているキャラクターボタンの情報
        /// </summary>
        private CharacterButtonInfo _currentButtonInfo;

        /// <summary>
        /// 選択済みのデータ
        /// </summary>
        private List<TrainedCharaData> _selectedDataList;

        /// <summary>
        /// 選択済みデータの練習パートナーの情報
        /// </summary>
        private WorkPracticeRaceData.PracticePartnerData _currentSelectePracticePartnerData = null;

        /// <summary>
        /// 選択中のタブ
        /// </summary>
        private int _tabIndex = 0;

        /// <summary>
        /// 初期化フラグ
        /// </summary>
        private bool _initializing;

        /// <summary>
        /// 殿堂入りウマ娘ダイアログを閉じた際に、リストの更新が必要かどうか（メモ・お気に入りアイコンの変更を反映） 
        /// </summary>
        private bool _needRefreshListOnCloseDialog = false;

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register, List<int> charaIdList, ViewType viewType)
        {
            register.RegisterPathWithoutInfo(ResourcePath.CHARA_RIBBON_FLASH_PATH);

            if (VIEW_DATA_DIC.TryGetValue(viewType, out var viewTypeData))
            {
                register.RegisterPathWithoutInfo(viewTypeData.BgPath);
                register.RegisterPathWithoutInfo(viewTypeData.EnvPath);
            }
            else
            {
                Debug.LogError($"VIEW_DATA_DICにデータがありません. viewType = {viewType}");
            }
            // ボイス登録
            AudioManager.Instance.RegisterDownloadByCharaIds(register, charaIdList, CharacterSystemTextGroupExtension.Scene.Other, true);
        }

        /// <summary>
        /// 背景の上書き設定
        /// チャンピオンズミーティングの出走選択では背景と環境設定を指定したいのでenvPathを併せて指定する
        /// </summary>
        public static void OverrideBgPath(ViewType viewType, string bgPath, string envPath = null)
        {
            // チャンピオンズミーティング以外ではenvPathを指定しないので、こちらを読む
            if (envPath == null)
            {
                envPath = VIEW_DATA_DIC[viewType].EnvPath;
            }
            VIEW_DATA_DIC[viewType] = new ViewTypeData(bgPath, envPath);
        }

        /// <summary>
        /// 生成したタイミングでサイズだけ合わせないといけないのでAwakeで行う
        /// </summary>
        private void Awake()
        {
            var uiManager = UIManager.Instance;
            uiManager.AdjustContentsRootRect(_contentsRoot);
            uiManager.AdjustContentsRootRect(_overSafeAreaContentsRoot, true);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="setupParam"></param>
        public void Setup(SetupParam setupParam)
        {
            if (setupParam == null)
            {
                Debug.LogWarning("不正な入力");
                return;
            }

            _initializing = true;

            _setupParam = setupParam;
            _selectedDataList = _setupParam.InitialSelectedList ?? new List<TrainedCharaData>();

            _courseInfo = _setupParam.CourseInfo;
            if (_setupParam.RaceInstance != null)
            {
                _courseInfo = _setupParam.RaceInstance.GetRaceCourseSetMaster();
            }

            var tabCount = _toggleGroup != null ? _toggleGroup.ToggleArrayLength : 1;
            _currentSelectDataArray = new TrainedCharaData[tabCount];

            // レースヘッダーセットアップ
            SetupRaceHeader();

            // 変更対象のキャラクター情報ヘッダーセットアップ
            //（チーム競技場：デッキ編成のキャラ選択）
            SetupChengeBaseCharaInfoHeader();

            // リボン初期化
            _ribbon.Release();
            _ribbon.InitializePlayer();

            // キャラタップボタンセットアップ
            _charaTapButton.SetOnClick(OnCharaTap);

            // 決定ボタンセットアップ
            _selectButton.SetOnClick(OnClickDecide);

            if (_setupParam.ViewType == ViewType.Daily
            && SaveDataManager.Instance.SaveLoader.DailyIsEnableSkipMultiRace)
            {
                // デイリーレースまとめてスキップの時はSE変更
                _selectButton.SeType = ButtonCommon.ButtonSeType.DecideM01;
            }

            if (_findButtonIfEmpty != null)
            {
                _findButtonIfEmpty.SetOnClick(OnClickFindButtonIfEmpty);
            }
            
            // マスターズチャレンジの場合は挑戦回数付きのラベルを表示する
            _selectButtonNormalLabel.SetActiveWithCheck(!_setupParam.IsUseSelectButtonWithText);
            _selectButtonLabelWithText.SetActiveWithCheck(_setupParam.IsUseSelectButtonWithText);

            Setup3DViewer();

            // トグルセットアップ
            SetupToggle();

            // 出走人数を更新
            UpdateEntryNum();

            //注意文言
            UpdateNotice(_setupParam.NoticeText, _setupParam.NoticeWithDetailText, _setupParam.NoticeWithDetailAction);

            _initializing = false;
        }

        /// <summary>
        /// トグルセットアップ
        /// </summary>
        private void SetupToggle()
        {
            foreach (var selectedData in _selectedDataList)
            {
                var tab = GetInitialTab(selectedData);
                if (tab < _currentSelectDataArray.Length && _currentSelectDataArray[tab] == null)
                {
                    _currentSelectDataArray[tab] = selectedData;
                }
            }

            // 練習パートナー情報があれば代入
            if (_setupParam.IsViewTypePractice && _currentSelectDataArray[TAB_PERTNER] != null && _currentSelectDataArray[TAB_PERTNER].IsGhost)
            {
                _currentSelectePracticePartnerData = WorkDataManager.Instance.PracticeRaceData.PracticePartnerDataDict[_currentSelectDataArray[TAB_PERTNER].Id];
            }

            var initialTab = GetInitialTab(_selectedDataList.FirstOrDefault());
            if (initialTab >= _currentSelectDataArray.Length)
            {
                initialTab = TAB_TRAINED;
            }
            if (_setupParam.EnableTab)
            {
                _toggleGroup.SetActiveWithCheck(true);
                _toggleGroup.SetOnSelectCallback(OnToggleSelect);
                _flickableObject.SetFlickCallback(_toggleGroup.OnFlick);
                _toggleGroup.GetToggle(initialTab).isOn = true;
            }
            else
            {
                _toggleGroup.SetActiveWithCheck(false);
                if (_flickableObject != null)
                {
                    _flickableObject.enabled = false;
                }
            }

            OnToggleSelect(initialTab);
            UpdateSelectChara(true);
        }

        /// <summary>
        /// レースヘッダセットアップ
        /// </summary>
        private void SetupRaceHeader()
        {
            if (_raceInfoHeader == null)
                return;

            _raceInfoHeader.SetActiveWithCheck(true);

            if (_setupParam.RaceInstance != null)
            {
                if (_setupParam.ChampionsRoundDetail != null)
                {
                    //チャンピオンズミーティング用
                    _raceInfoHeader.Setup(_setupParam.ChampionsRoundDetail);
                }
                else if (_setupParam.RoomMatchRoomData != null)
                {
                    // ルームマッチ用
                    _raceInfoHeader.Setup(_setupParam.RoomMatchRoomData);
                }
                else if (_setupParam.IsViewTypePractice)
                {
                    // 練習用
                    if (_setupParam.PracticeSettingInfo != null)
                    {
                        _raceInfoHeader.Setup(_setupParam.PracticeSettingInfo);
                    }
                    else
                    {
                        _raceInfoHeader.SetActiveWithCheck(false);
                    }
                }
                else
                {
                    //その他
                    _raceInfoHeader.Setup(_setupParam.RaceInstance, _setupParam.IsExGrade());
                    if (_setupParam.RaceCondition != null)
                    {
                        _raceInfoHeader.Setup(_setupParam.RaceCondition, _setupParam.RaceInstance);
                    }
                }
            }
            else
            {
                _raceInfoHeader.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// ルームマッチ用ルーム名変更
        /// </summary>
        public void ChangeRoomName(string roomName)
        {
            if (_setupParam.RoomMatchRoomData != null)
            {
                // ルームマッチ用
                _raceInfoHeader.ChangeRoomName(roomName);
            }
        }

        /// <summary>
        /// 変更対象のキャラクター情報ヘッダーセットアップ
        ///（チーム競技場：デッキ編成のキャラ選択）
        /// </summary>
        private void SetupChengeBaseCharaInfoHeader()
        {
            if (_partsChangeBaseCharaInfoHeader == null)
            {
                return;
            }

            _partsChangeBaseCharaInfoHeader.SetActiveWithCheck(true);
            _partsChangeBaseCharaInfoHeader.Setup(_setupParam.SelectedMemberInfo, _courseInfo, (characterButton) =>
            {
                OnLongTapCharacterButton(characterButton);
            });
        }

        /// <summary>
        /// レースインスタンスを設定する
        /// </summary>
        /// <param name="raceInstance"></param>
        public void SetRaceInstance(MasterRaceInstance.RaceInstance raceInstance)
        {
            if (_setupParam != null)
            {
                _setupParam.RaceInstance = raceInstance;
            }
            _courseInfo = raceInstance.GetRaceCourseSetMaster();
            SetupRaceHeader();
            UpdateNameWithStatus();
        }

        /// <summary>
        /// トグル選択コールバック
        /// </summary>
        /// <param name="index"></param>
        private void OnToggleSelect(int index)
        {
            _tabIndex = index;

            SetupCardList(_tabIndex);

            UpdateListEmptyObject();
            UpdateSelectButtonInteractable();

            // タブ変更時はキャラを選択しなおす必要がある
            if (!_initializing)
            {
                UpdateSelectChara();
                PlaySelectedCharaVoice();
            }
        }

        /// <summary>
        /// 初期選択状態のタブインデックスを取得
        /// </summary>
        /// <param name="trainedChara"></param>
        /// <returns></returns>
        private int GetInitialTab(TrainedCharaData trainedChara)
        {
            if (trainedChara == null)
            {
                return TAB_TRAINED;
            }
            else if (trainedChara.IsGhost || trainedChara.IsRental) //レンタルウマ娘は入ってこないはずだが一応入れておく
            {
                return TAB_PERTNER;
            }
            else if (trainedChara.IsPlayer)
            {
                return TAB_TRAINED;
            }
            else
            {
                // 練習でフォロワーの代表ウマ娘の場合はここにくる
                return TAB_PERTNER;
            }
        }

        /// <summary>
        /// リスト作成
        /// </summary>
        /// <param name="index"></param>
        private void SetupCardList(int index)
        {
            switch (index)
            {
                case TAB_TRAINED:
                    SetupCardListImpl(_setupParam.TrainedCharaList);
                    break;
                case TAB_PERTNER:
                    SetupCardListImpl(_setupParam.PartnerTrainedCharaList);
                    break;
            }
        }

        /// <summary>
        /// リストのセットアップ
        /// </summary>
        /// <param name="trainedCharaList"></param>
        /// <returns></returns>
        private void SetupCardListImpl(List<TrainedCharaData> trainedCharaList)
        {
            if (trainedCharaList == null)
            {
                trainedCharaList = new List<TrainedCharaData>();
            }

            // ボタン情報生成
            var buttonInfoList = new List<CharacterButtonInfo>();

            CharacterButtonInfo unselectButtonInfo = null;
            if (_setupParam.Removable)
            {
                unselectButtonInfo = new CharacterButtonInfo
                {
                    IdType = CharacterButtonInfo.IdTypeEnum.Unselect,
                    OnTap = OnTapUnselectButton,
                    OnUpdate = OnUpdateCharacterButton,
                    ChildCloneBase = _buttonAccessoryPrefab.gameObject,
                    IconSizeType = IconBase.SizeType.CharaNormal,
                };
            }

            foreach (var trainedChara in trainedCharaList)
            {
                CharacterButtonInfo buttonInfo = new CharacterButtonInfo(trainedChara.CardId , CharacterButtonInfo.IdTypeEnum.Trained);

                buttonInfo.OnTap = OnTapCharacterButton;
                buttonInfo.OnLongTap = OnLongTapCharacterButton;
                buttonInfo.OnUpdate = OnUpdateCharacterButton;
                buttonInfo.ChildCloneBase = _buttonAccessoryPrefab.gameObject;
                buttonInfo.TrainedChara = trainedChara;
                buttonInfo.RankImage = true;
                buttonInfo.IconSizeType = IconBase.SizeType.CharaNormal;
                buttonInfo.ButtonSeType = ButtonCommon.ButtonSeType.Cursor01;
                buttonInfo.IsLock = trainedChara.IsLock;
                
                // ストーリーアンロックレースの出走ウマ娘選択では、獲得ボーナス情報を設定。
                if (_setupParam.ViewType == ViewType.Story)
                {
                    buttonInfo.GainedBonus = MainStoryConditionRaceBonusCalculator.CalcBonus(trainedChara, _setupParam.MainStoryRaceData);
                }
                // チャンミ出走履歴では勝率を付与
                else if (_setupParam.ViewType == ViewType.Champions)
                {
                    var rankCountData = WorkDataManager.Instance.ChampionsData.GetTrainedCharaRankCountData(trainedChara.Id);
                    if (rankCountData != null)
                    {
                        buttonInfo.ChampionsWinRate = rankCountData.WinRate;
                    }
                }
                // リーグオブヒーローズではリーグオブヒーローズの勝率を付与
                else if (_setupParam.ViewType == ViewType.Heroes)
                {
                    // 出走履歴ダイアログはここを使っていないので注意
                    var rankCountData = WorkDataManager.Instance.HeroesData.GetTrainedCharaRankCountData(trainedChara.Id);
                    if (rankCountData != null)
                    {
                        buttonInfo.ChampionsWinRate = rankCountData.WinRate;
                    }
                    else
                    {
                        buttonInfo.ChampionsWinRate = 0;
                    }
                }
                // チャレンジマッチの場合は獲得Ptボーナスを表示
                else if (_setupParam.ViewType == ViewType.Challenge)
                {
                    int ptBonus = 0;

                    var bonusSkill = ChallengeMatchUtil.GetBonusSkill(trainedChara);
                    if (bonusSkill != null)
                    {
                        var bonusSkillValueData = ChallengeMatchUtil.GetBonusSkillValue(bonusSkill.Level, bonusSkill.MasterData);
                        ptBonus = ChallengeMatchUtil.ConvertBonusSkillValue(bonusSkillValueData.statusValue);
                    }

                    buttonInfo.ChallengeMatchPtBonus = ptBonus;
                }
                // 練習の場合はフォローを表示
                else if (_setupParam.IsViewTypePractice)
                {
                    buttonInfo.EnableFollowText = true;
                }
                // マスターズチャレンジの場合はレースコンテンツIDを設定
                else if (_setupParam.ViewType == ViewType.Ultimate)
                {
                    buttonInfo.UltimateRaceContentsId = _setupParam.UltimateRaceContentsId;
                }
                else if (_setupParam.ViewType == ViewType.TeamBuilding)
                {
                    // 目指せ！最強チームイベントではキャラクターボタンの通知アイコンの表示はNoticeModelを用いて行う
                    var noticeModelFactory = _setupParam.CharacterButtonNoticeModelFactory;
                    buttonInfo.SetNoticeVM(noticeModelFactory?.CreateScoutInfoCharacterButtonNoticeModel(trainedChara.CharaId));
                }

                if (_currentSelectDataArray[_tabIndex] != null)
                {
                    if (_currentSelectDataArray[_tabIndex].Id == trainedChara.Id && _currentSelectDataArray[_tabIndex].ViewerId == trainedChara.ViewerId)
                    {
                        _currentButtonInfo = buttonInfo;
                    }
                }

                buttonInfoList.Add(buttonInfo);
            }
            
            // ソート・絞り込み設定
            var saveTag = SortFilterSetting.SaveTag.None;
            switch (_setupParam.ViewType)
            {
                case ViewType.Daily:
                    saveTag = SortFilterSetting.SaveTag.DailyEntry;
                    break;
                case ViewType.Legend:
                    saveTag = _setupParam.IsDailyLegendRace
                        ? SortFilterSetting.SaveTag.DailyLegendEntry
                        : SortFilterSetting.SaveTag.LegendEntry;
                    break;
                case ViewType.Story:
                    saveTag = SortFilterSetting.SaveTag.EpisodeUnlock;
                    break;
                case ViewType.TeamEdit:
                    saveTag = SortFilterSetting.SaveTag.TeamEditEntry;
                    break;
                case ViewType.Champions:
                    saveTag = SortFilterSetting.SaveTag.ChampionsEntry;
                    break;
                case ViewType.RoomMatch:
                    saveTag = _setupParam.RoomMatchRoomData != null
                        ? SortFilterSetting.SaveTag.RoomMatchEntry// ルーム情報がある=出走キャラ指定の絞り込みあり
                        : SortFilterSetting.SaveTag.RoomMatchPreset;
                    break;
                case ViewType.PracticeRace:
                case ViewType.PracticeDeckEdit:
                    saveTag = _tabIndex == TAB_TRAINED
                        ? SortFilterSetting.SaveTag.PracticeRaceEntryOwn
                        : SortFilterSetting.SaveTag.PracticeRaceEntryPartner;
                    break;
                case ViewType.Challenge:
                    saveTag = SortFilterSetting.SaveTag.ChallengeMatchEntry;
                    break;
                case ViewType.TeamBuilding:
                    saveTag = SortFilterSetting.SaveTag.TeamBuildingCaptainSelect;
                    break;
                case ViewType.Heroes:
                    saveTag = SortFilterSetting.SaveTag.HeroesEntry;
                    break;
                case ViewType.Ultimate:
                    saveTag = SortFilterSetting.SaveTag.UltimateEntry;
                    break;
                case ViewType.RatingRace:
                    switch(WorkDataManager.Instance.RatingRaceData.CurrentRace.RaceRankInfo.RatingRaceCategory)
                    {
                        default:
                        case RatingRaceDefine.RatingRaceCategory.Short:     saveTag = SortFilterSetting.SaveTag.RatingRaceShortEntry;   break;
                        case RatingRaceDefine.RatingRaceCategory.Mile:      saveTag = SortFilterSetting.SaveTag.RatingRaceMileEntry;    break;
                        case RatingRaceDefine.RatingRaceCategory.Middle:    saveTag = SortFilterSetting.SaveTag.RatingRaceMiddleEntry;  break;
                        case RatingRaceDefine.RatingRaceCategory.Long:      saveTag = SortFilterSetting.SaveTag.RatingRaceLongEntry;    break;
                        case RatingRaceDefine.RatingRaceCategory.Dirt:      saveTag = SortFilterSetting.SaveTag.RatingRaceDirtEntry;    break;
                    }
                    break;
            }

            var sortFilterSetting = SortFilterSetting.Load(saveTag);
            
            void SaveSortFilterSetting(PartsListSortButton sortButton)
            {
                SortFilterSetting.Save(saveTag, sortButton.Setting);   
            }
            
            void OnExecSort(PartsListSortButton sortButton)
            {
                SaveSortFilterSetting(sortButton);

                if (_setupParam.ViewType == ViewType.TeamEdit && (_setupParam.SelectedMemberInfo.TrainedCharaData != null))
                {
                    _partsChangeBaseCharaInfoHeader.SetupCurrnetSortSetting(_cardList.CurrentSortMenu, sortButton.Setting.IsAsc);
                }
            }

            bool CheckUpdateSelection()
            {
                // マルチ選択の仕様を確かめられる画面が実装時に存在しなかったのでAssertしておく
                DebugUtils.Assert(_setupParam.EnableMultiSelect == false);
                
                // 選択状態の更新が必要かチェック
                bool needSelectUpdate = true;
                if (_currentButtonInfo != null)
                {
                    needSelectUpdate = _cardList.CurrentButtonInfoList.Exists(x => x == _currentButtonInfo) == false;
                }

                return needSelectUpdate;
            }

            // 絞り込んだ結果が0件なら決定押せなくする
            bool CustomFilterFunc(CharacterButtonInfo info)
            {
                // TODO:@takahashi_reo: 出走ウマ娘選択の絞り込みバグの修正が完了するまでオプション処理として置いておくが、最終的には共通処理として組込みが必要
                return info.Interactable;
            }

            // リスト生成前に一度ボタン情報を更新
            RefreshCharacterButtonInfo(buttonInfoList);

            // リスト生成
            _cardList.CreateOrRefresh(buttonInfoList, sortFilterSetting, OnExecSort, true, onResetFilter: SaveSortFilterSetting, overrideCheckSelectUpdate: CheckUpdateSelection, additionalFilterFunc: CustomFilterFunc,
                headButtonInfo: unselectButtonInfo);
            if (_setupParam.ViewType == ViewType.TeamEdit && (_setupParam.SelectedMemberInfo.TrainedCharaData != null))
            {
                _partsChangeBaseCharaInfoHeader.SetupCurrnetSortSetting(sortFilterSetting.Sort, sortFilterSetting.IsAsc);
            }

            // 絞りこみの結果、選択不可のキャラのみが残るならフィルターをリセット
            if (_cardList.CurrentButtonInfoList.TrueForAll(x => _setupParam.IsUnselectableChara(x.TrainedChara)))
            {
                _cardList.ResetCurrentFilter();
                _cardList.Refresh(true);
            }

            if (!_setupParam.EnableMultiSelect)
            {
                // 複数選択不可の場合
                var needAutoSelect = _currentSelectDataArray[_tabIndex] == null 
                    || _cardList.CurrentButtonInfoList.All(x => x.TrainedChara == null || x.TrainedChara != null && !(x.TrainedChara.Id == _currentSelectDataArray[_tabIndex].Id && x.TrainedChara.ViewerId == _currentSelectDataArray[_tabIndex].ViewerId));
                
                // 練習の場合、練習パートナー⇔代表ウマ娘の入れ替えがありうる
                if (needAutoSelect && _setupParam.IsViewTypePractice && _currentSelectDataArray[_tabIndex] != null && _tabIndex == TAB_PERTNER)
                {
                    var newRentalTrainedChara = FindSamePracticeRentalCharaInfo();
                    if (newRentalTrainedChara != null && !_setupParam.IsUnselectableChara(newRentalTrainedChara))
                    {
                        // 同一キャラがいる
                        var target = _cardList.CurrentButtonInfoList.FirstOrDefault(x => x.TrainedChara != null && x.TrainedChara.Id == newRentalTrainedChara.Id && x.TrainedChara.ViewerId == newRentalTrainedChara.ViewerId);
                        if (target != null)
                        {
                            OnSelectCharacter(target);
                            UpdateNameWithStatus();// 内部的にも変える
                            needAutoSelect = false;
                        }
                    }
                }

                if (needAutoSelect)
                {
                    // 初期選択キャラの指定がない or 絞り込みのせいでリスト上に指定キャラがいない場合は、1番上のキャラを選択
                    var target = _cardList.CurrentButtonInfoList.FirstOrDefault(x => !_setupParam.IsUnselectableChara(x.TrainedChara));
                    if (target != null)
                    {
                        OnChangeCharacterButton(target, _currentSelectDataArray[_tabIndex] != null && !_initializing);
                    }
                    else
                    {
                        // 選択可能なキャラがいない場合
                        _currentButtonInfo = null;
                        _currentSelectDataArray[_tabIndex] = null;
                        if (_setupParam.IsViewTypePractice && _tabIndex == TAB_PERTNER)
                        {
                            _currentSelectePracticePartnerData = null;
                        }
                        UpdateSelectChara();
                        UpdateSelectButtonInteractable();
                    }
                }
            }

            _cardList.Refresh();
        }

        /// <summary>
        /// 出走人数を更新
        /// </summary>
        private void UpdateEntryNum()
        {
            if (_setupParam.EnableMultiSelect)
            {
                _entryNumBase.SetActive(true);
                _entryNum.text = TextUtil.Format(TextId.Common0177.Text(), _selectedDataList.Count, _setupParam.MaxSelectCount);
            }
            else if (!string.IsNullOrEmpty(_setupParam.EntryNumText))
            {
                _entryNumBase.SetActive(true);
                _entryNum.text = _setupParam.EntryNumText;
            }
            else
            {
                _entryNumBase.SetActive(false);
            }
        }

        /// <summary>
        /// ボタンの選択可否を更新
        /// </summary>
        private void UpdateSelectButtonInteractable()
        {
            if (_currentSelectDataArray[_tabIndex] == null)
            {
                // 選択キャラが「０」ならグレーアウト
                _selectButton.SetButtonInteractableWithColor(false);
                _selectButton.SetNotificationMessage(TextId.Champions0633.Text());
            }
            else
            {
                if (_setupParam.IsUnselectableChara(_currentSelectDataArray[_tabIndex]))
                {
                    // 選択されたキャラが選択不可であるはずのキャラならグレーアウト
                    _selectButton.SetButtonInteractableWithColor(false);
                    _selectButton.SetNotificationMessage(_setupParam.GetUnselectableReason?.Invoke(_currentSelectDataArray[_tabIndex]));
                }
                else
                {
                    _selectButton.SetButtonInteractableWithColor(true);
                    _selectButton.SetNotificationMessage(string.Empty);
                }
            }
        }

        /// <summary>
        /// 出走ボタン下部の挑戦回数ラベルのテキストを設定する
        /// </summary>
        /// <param name="text"></param>
        public void SetSelectButtonCountLabelText(string text)
        {
            _selectButtonCountLabel.text = text;
        }

        /// <summary>
        /// 名前とステータスパネルを更新
        /// </summary>
        public void UpdateNameWithStatus()
        {
            if (_currentSelectDataArray[_tabIndex] != null)
            {
                _statusPanel.gameObject.SetActive(true);
                _detailStatusFrame.gameObject.SetActive(true);
                _selectTextObject.gameObject.SetActive(false);
                _namePlate.gameObject.SetActive(true);
                _charaTapButton.SetActiveWithCheck(true);
                UpdateNoticePos(true);

                //ステータス表示
                var dispType = _setupParam == null ? PartsSingleModeStartProperStatus.DispType.All : _setupParam.ProperDispType;
                _statusPanel.Setup(_currentSelectDataArray[_tabIndex], dispType, _courseInfo, _setupParam.ViewType, _setupParam.MainStoryRaceData);
                _setupParam.OnUpdateStatusPanel?.Invoke(_currentSelectDataArray[_tabIndex]);

                var prevLock = _currentSelectDataArray[_tabIndex].IsLock;
                var prevLockType = _currentSelectDataArray[_tabIndex].FavoriteType;
                var prevMemo = _currentSelectDataArray[_tabIndex].Memo;
                var detailType = PartsCharacterNamePlate.DetailType.Normal;
                switch (_setupParam.ViewType)
                {
                    case ViewType.Champions:
                        detailType = PartsCharacterNamePlate.DetailType.ChampionsRaceRecord;
                        break;
                    case ViewType.Heroes:
                        detailType = PartsCharacterNamePlate.DetailType.HeroesRaceRecord;
                        break;
                }
                _namePlate.Setup(
                    _currentSelectDataArray[_tabIndex],
                    detailType: detailType,
                    onOpen: () => TempData.Instance.PracticeRaceData.IsChangeRentalList = false,
                    onClose: () =>
                {
                    // 練習の場合はリスト更新があるかも
                    bool needRefreshPartnerList = _setupParam.IsViewTypePractice && _tabIndex == TAB_PERTNER && TempData.Instance.PracticeRaceData.IsChangeRentalList;
                    TempData.Instance.PracticeRaceData.IsChangeRentalList = false;

                    var afterLock = _currentSelectDataArray[_tabIndex].IsLock;
                    var afterLockType = _currentSelectDataArray[_tabIndex].FavoriteType;
                    var afterMemo = _currentSelectDataArray[_tabIndex].Memo;
                    var isChangedLock = prevLock != afterLock || prevLockType != afterLockType;
                    var isChangedMemo = !string.Equals(prevMemo, afterMemo);
                    // 連続でお気に入りやメモを編集するとUI更新されないことがあるため、編集前情報を更新
                    prevLock = afterLock;
                    prevLockType = afterLockType;
                    prevMemo = afterMemo;
                    bool needRefreshCharacterButton = isChangedLock || isChangedMemo;
                    if (needRefreshCharacterButton == false && !needRefreshPartnerList) return;

                    _currentButtonInfo.IsLock = afterLock;
                    var filters = _cardList.CurrentFilterMenuList;
                    bool needExecFilter = SortFilterSetting.IsSensitiveToDetailDialog(filters);
                    bool needExecSort = SortFilterSetting.IsSensitiveToDetailDialog(_cardList.CurrentSortMenu);
                    _cardList.Refresh(needExecFilter || needExecSort);

                    // リストそのものに変更があった場合は作り直す
                    if (needRefreshPartnerList)
                    {
                        UpdateListTrainedChara();
                        return;
                    }

                    // 要素が0になるのを回避
                    if (_cardList.CurrentButtonInfoList.Count == 0)
                    {
                        _cardList.ResetCurrentFilter();
                        _cardList.Refresh(true);
                    }
                    else
                    {
                        // 絞りこみにより、選択不可のキャラのみが残るならフィルターをリセット
                        if (_cardList.CurrentButtonInfoList.TrueForAll(x => _setupParam.IsUnselectableChara(x.TrainedChara)))
                        {
                            _cardList.ResetCurrentFilter();
                            _cardList.Refresh(true);
                        }
                    }

                    // 選択中のキャラが表示されてなければ、選択可能な先頭のキャラを選択する
                    var selectButtonInfo = _cardList.CurrentButtonInfoList.FirstOrDefault(x => x.TrainedChara != null && x.TrainedChara.Id == _currentButtonInfo.TrainedChara.Id && x.TrainedChara.ViewerId == _currentButtonInfo.TrainedChara.ViewerId);
                    if (selectButtonInfo == null)
                    {
                        var firstButtonInfo = _cardList.CurrentButtonInfoList.FirstOrDefault(x => !_setupParam.IsUnselectableChara(x.TrainedChara));
                        if (firstButtonInfo != null)
                        {
                            OnChangeCharacterButton(firstButtonInfo);
                        }
                    }
                });
            }
            else
            {
                _statusPanel.gameObject.SetActive(false);
                _namePlate.gameObject.SetActive(false);
                _detailStatusFrame.gameObject.SetActive(false);
                _selectTextObject.gameObject.SetActive(true);
                _charaTapButton.SetActiveWithCheck(false);
                UpdateNoticePos(false);
            }
        }

        /// <summary>
        /// (タブ使用時)空のメッセージ用のオブジェクトを更新
        /// </summary>
        private void UpdateListEmptyObject()
        {
            _emptyObjctRoot.SetActiveWithCheck(false);
            _cardList.Scroll.SetActiveWithCheck(true);

            if (_tabIndex == TAB_TRAINED)
            {
                return;
            }
            else if (_tabIndex == TAB_PERTNER && _setupParam.PartnerTrainedCharaList.Count == 0)
            {
                _emptyObjctRoot.SetActiveWithCheck(true);
                _cardList.Scroll.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// タップ時のコールバック（ウマ娘カード）
        /// </summary>
        /// <param name="button"></param>
        private void OnTapCharacterButton(CharacterButton button)
        {
            if (_setupParam.IsUnselectableCanLongTapTrainedChara(button.Info.TrainedChara))
            {
                // ロングタップのみ可能なキャラ
                UIManager.Instance.ShowNotification(_setupParam.GetUnselectableReason?.Invoke(button.Info.TrainedChara));
                return;
            }

            OnChangeCharacterButton(button.Info);
        }

        /// <summary>
        /// 選択キャラ変更時コールバック
        /// </summary>
        /// <param name="buttonInfo"></param>
        private void OnChangeCharacterButton(CharacterButtonInfo buttonInfo, bool isPlayVoice = true)
        {
            var isChanged = buttonInfo.TrainedChara != _currentSelectDataArray[_tabIndex];
            OnSelectCharacter(buttonInfo);
            if (isChanged)
            {
                UpdateSelectChara();
                if (isPlayVoice)
                {
                    // 初回遷移時は遷移中に呼ばれることを防ぐため手動で呼ぶ
                    PlaySelectedCharaVoice();
                }
            }
            UpdateSelectButtonInteractable();

            // 選択状態に変更が加わったのでボタン情報のInteractableを更新
            RefreshCharacterButtonInfo(_cardList.OriginalButtonInfoList);

            _cardList.Refresh();
        }

        /// <summary>
        /// ロングタップ時のコールバック（ウマ娘カード）
        /// </summary>
        /// <param name="button"></param>
        private void OnLongTapCharacterButton(CharacterButton button)
        {
            OpenTrainedCharacterDetailDialog(button.Info.TrainedChara, button.Info);
        }

        /// <summary>
        /// はずすタップ時のコールバック
        /// </summary>
        /// <param name="button"></param>
        private void OnTapUnselectButton(CharacterButton button)
        {
            _setupParam?.OnDecide(null);
        }
        
        /// <summary>
        /// CharacterButtonInfoを再更新
        /// </summary>
        /// <param name="buttonInfoList"></param>
        private void RefreshCharacterButtonInfo(List<CharacterButtonInfo> buttonInfoList)
        {
            // Note: 絞り込みに関するバグ（#65828）を修正する目的で追加した処理なので、必要最低限のプロパティのみ更新する
            foreach (var buttonInfo in buttonInfoList)
            {
                if (buttonInfo == null) continue;
                
                // 一度デフォルト値に戻す
                buttonInfo.Interactable = true;
                buttonInfo.NotificationMessage = string.Empty;
                
                var trainedChara = buttonInfo.TrainedChara;
                if (trainedChara == null)
                {
                    continue;
                }

                // 複数選択可能な場合
                if (_setupParam.EnableMultiSelect)
                {
                    // 選択済みの場合、は常にtrue
                    // 未選択の場合は、選択上限に到達済みかを判定
                    var alreadySelected = _selectedDataList.Exists(x => x.Id == trainedChara.Id);
                    buttonInfo.Interactable = alreadySelected
                        ? buttonInfo.Interactable = true
                        : _selectedDataList.Count < _setupParam.MaxSelectCount;
                }

                // そもそも選択不可能なキャラだった場合
                if (_setupParam.IsNotInteractiveChara(trainedChara))
                {
                    buttonInfo.Interactable = false;
                    buttonInfo.NotificationMessage = _setupParam.GetUnselectableReason?.Invoke(trainedChara);
                }
            }
        }

        /// <summary>
        /// キャラボタンを更新
        /// </summary>
        /// <param name="button"></param>
        private void OnUpdateCharacterButton(CharacterButton button)
        {
            var buttonAccessory = button.PartsButtonAccessory;
            ResetButtonAccessory(buttonAccessory);

            // 選択カーソル表示
            var trainedChara = button.Info.TrainedChara;
            bool isSelected = trainedChara != null && _currentSelectDataArray[_tabIndex] != null && trainedChara.Id == _currentSelectDataArray[_tabIndex].Id && trainedChara.ViewerId == _currentSelectDataArray[_tabIndex].ViewerId;
            buttonAccessory.SetCursor(isSelected, true, button.MyImage.gameObject);

            // CharacterButtonInfoをもとにコンポーネントのInteractableを更新
            button.Interactable = button.Info.Interactable;
            button.MyButton.SetNotificationMessage(button.Info.NotificationMessage);
            if (button.Info.Interactable)
            {
                button.MyButton.SetLookEnableColor(true);
                button.MyButton.SeType = ButtonCommon.ButtonSeType.Cursor01;
            }

            // フレーム周りの更新
            UpdateCharacterButtonFrame(button);
        }

        private void UpdateCharacterButtonFrame(CharacterButton button) 
        {
            var trainedChara = button.Info.TrainedChara;
            if (trainedChara == null)
            {
                return;
            }

            var buttonAccessory = button.PartsButtonAccessory;

            if (_setupParam.EnableMultiSelect)
            {
                // SEを選択用に変更
                button.MyButton.SeType = ButtonCommon.ButtonSeType.DecideS01;

                //選択中かどうかのバッジ表記
                var index = _selectedDataList.IndexOf(trainedChara);
                if (index >= 0)
                {
                    buttonAccessory.gameObject.SetActive(true);
                    buttonAccessory.SetNum(index + 1);
                    button.MyButton.SeType = ButtonCommon.ButtonSeType.CancelS01;
                }
            }

            if (_setupParam.ReservedCharaDic != null
                && _setupParam.ViewType == ViewType.TeamEdit)
            {
                // bugfix#65937:育成前後でTrainedCharaDataの中身が変わる（_cachedCreateTimeTimeStampなど）
                // TrainedCharaDataに少しでも変更があるとTryGetで取得できないので、TrainedCharaIdなどを用いて取得する形に修正
                var label = _setupParam.ReservedCharaDic.FirstOrDefault(x =>
                {
                    var containedTrainedCharaId = x.Key.Id == trainedChara.Id;
                    var containedCharaId = x.Key.CharaId == trainedChara.CharaId;
                    return containedTrainedCharaId && containedCharaId;
                }).Value;

                if (!string.IsNullOrEmpty(label))
                {
                    // 予約ラベルを表示
                    buttonAccessory.SetActiveReservedOrangeLabel(true);
                    buttonAccessory.SetReservedOrangeLabelText(label);
                }
                else
                {
                    // 選択できるけど補足がある場合
                    if (_setupParam.IsSelectableWithReasonChara(trainedChara))
                    {
                        if (_setupParam.GetSelectableNoticeIconText != null)
                        {
                            var text = _setupParam.GetSelectableNoticeIconText(trainedChara);
                            buttonAccessory.SetNoticeIcon(true, text);
                            buttonAccessory.SetNoticeIconPosTop();
                        }
                    }
                }
            }

            if (_setupParam.IsNotInteractiveChara(trainedChara))
            {
                //NoticeIcon用のテキストが指定されてたらそっち使う
                if (_setupParam.GetUnselectableNoticeIconText != null)
                {
                    var text = _setupParam.GetUnselectableNoticeIconText(trainedChara);
                    if (_setupParam.IsUnselectableNoticeIconWithoutMark?.Invoke(trainedChara) == true)
                    {
                        buttonAccessory.SetNoticeIconTextWithoutExclamation(text);
                    }
                    else
                    {
                        buttonAccessory.SetNoticeIcon(true, text);
                    }
                    buttonAccessory.SetNoticeIconPosTop();
                }
                else
                {
                    button.MyButton.TargetText.IsIgnoreParentColor = true;
                    button.MyButton.TargetText.text = TextId.Common0097.Text();
                }
            }
            else if (_setupParam.IsUnselectableCanLongTapTrainedChara(trainedChara))
            {
                // 見かけ上は選択不可だがタップ可能
                button.MyButton.SetLookEnableColor(false);
                button.MyButton.SeType = ButtonCommon.ButtonSeType.CancelM02;
            }

            //設定中表示
            void SetSettingMark()
            {
                buttonAccessory.SetActiveReservedOrangeLabel(false);
                buttonAccessory.SetActiveReservedLabel(true);
                buttonAccessory.SetReservedLabelText(TextId.Character0077.Text());
                buttonAccessory.SetReservedLabelFontSize(TextFormat.FontSize.Size_32);
                buttonAccessory.SetReservedLabelPos(DEFAULT_RESERVED_LABEL_POS);
            }
            if (_setupParam.ViewType == ViewType.TeamEdit && (_setupParam.SelectedMemberInfo.TrainedCharaData != null))
            {
                if (trainedChara.Id == _setupParam.SelectedMemberInfo.TrainedCharaData.Id)
                {
                    SetSettingMark();
                }
            }

            switch (_setupParam.ViewType)
            {
                case ViewType.Champions:
                case ViewType.RoomMatch:
                case ViewType.PracticeRace:
                case ViewType.PracticeDeckEdit:
                case ViewType.Heroes:
                    if(_setupParam.SettingCharaList.Contains(trainedChara))
                    {
                        // 既に編成中のキャラ、一応押せはするが入れ替わる
                        SetSettingMark();
                    }
                    break;
            }
        }

        /// <summary>
        /// ButtonAccessory("選択中"アイコン等)の表示をリセット
        /// </summary>
        private void ResetButtonAccessory(PartsButtonAccessory buttonAccessory)
        {
            buttonAccessory.SetNum(0);
            buttonAccessory.SetActiveReservedLabel(false);
            buttonAccessory.SetActiveReservedOrangeLabel(false);
            buttonAccessory.SetNoticeIcon(false);
            buttonAccessory.transform.SetAsLastSibling();
        }

        /// <summary>
        /// キャラクター選択コールバック
        /// </summary>
        private void OnSelectCharacter(CharacterButtonInfo characterButtonInfo)
        {
            _currentSelectDataArray[_tabIndex] = characterButtonInfo.TrainedChara;
            _currentButtonInfo = characterButtonInfo;
            if (!_setupParam.EnableMultiSelect)
            {
                _selectedDataList.Clear();
            }

            // 練習パートナー情報を保持
            if (_setupParam.IsViewTypePractice && _tabIndex == TAB_PERTNER)
            {
                if (_currentSelectDataArray[TAB_PERTNER] != null && _currentSelectDataArray[TAB_PERTNER].IsGhost)
                {
                    _currentSelectePracticePartnerData = WorkDataManager.Instance.PracticeRaceData.PracticePartnerDataDict[_currentSelectDataArray[TAB_PERTNER].Id];
                }
                else
                {
                    _currentSelectePracticePartnerData = null;
                }
            }

            if (_currentSelectDataArray[_tabIndex] != null)
            {

                if (!_selectedDataList.Contains(_currentSelectDataArray[_tabIndex]))
                {
                    _selectedDataList.Add(_currentSelectDataArray[_tabIndex]);
                }
                else
                {
                    _selectedDataList.Remove(_currentSelectDataArray[_tabIndex]);
                }
            }

            UpdateEntryNum();
        }

        /// <summary>
        /// キャラ表示を更新する
        /// </summary>
        /// <param name="isRibbonAnimFirstCalled">リボンアニメ再生が一度目か (true: そう, false: 違う)</param>
        private void UpdateSelectChara(bool isRibbonAnimFirstCalled = false)
        {
            UpdateNameWithStatus();
            if (_currentSelectDataArray[_tabIndex] != null)
            {
                BGManager.Instance.CharacterBg.Model.SetActiveWithCheck(true);
                Show3DCharacter(_currentSelectDataArray[_tabIndex].CharaId, _currentSelectDataArray[_tabIndex].GetRaceDressId(true));
                PlayRibbonAnimation(_currentSelectDataArray[_tabIndex].CharaId, isRibbonAnimFirstCalled);
            }
            else
            {
                BGManager.Instance.CharacterBg.Model.SetActiveWithCheck(false);
                _ribbon.SetActiveWithCheck(false);
            }
            _setupParam.OnSelect?.Invoke(_currentSelectDataArray[_tabIndex]);
        }

        /// <summary>
        /// IN再生
        /// </summary>
        public void PlayIn()
        {
            if (_currentSelectDataArray == null || _currentSelectDataArray[_tabIndex] == null)
            {
                return;
            }

            _namePlate.PlayFadeIn();
            PlayRibbonAnimation(_currentSelectDataArray[_tabIndex].CharaId, true);
        }

        /// <summary>
        /// OUT再生
        /// </summary>
        public void PlayOut()
        {
            _namePlate.PlayFadeOut();
            _ribbon.PlayOut();
        }

        /// <summary>
        /// リストアニメ再生
        /// </summary>
        /// <param name="isPlayIn"></param>
        public IEnumerator PlayListAnimation(bool isPlayIn)
        {
            // TODO:@takahashi_reo: PlayIn() & PlayOut()と統合する
            var preset = isPlayIn
                ? TweenAnimation.PresetType.PartsInMoveAndFade
                : TweenAnimation.PresetType.PartsOutMoveAndFade;
            _selectButton.transform.localPosition = Math.VECTOR3_ZERO;
            var sequence = TweenAnimationBuilder.CreateSequence(_selectButton.gameObject, preset);
            
            // TODO:@takahashi_reo: ver.1.5.5以降で他機能の出走ウマ娘選択画面と合わせて実装
//            yield return _cardList.CharacterListUI.PlayAnimation(isPlayIn);
            yield return sequence.WaitForCompletion();
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public void Release()
        {
            // リボン破棄
            _ribbon.Release();
            _ribbon.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 帯のアニメーションを再生
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="isPlayInView"></param>
        private void PlayRibbonAnimation(int charaId, bool isPlayInView = false)
        {
            var masterDressData = MasterDataManager.Instance.masterDressData.GetWithCharaIdOrderByIdAsc(charaId);
            if (masterDressData == null)
                return;

            _namePlate.PlayFadeOut();
            _ribbon.SetActiveWithCheck(true);
            _ribbon.PlayNext(masterDressData.MainColor, masterDressData.SubColor, null,
                () => _namePlate.PlayFadeIn(),
                isPlayInView);
        }

        /// <summary>
        /// 背景を表示
        /// </summary>
        public void Setup3DViewer()
        {
            if (VIEW_DATA_DIC.TryGetValue(_setupParam.ViewType, out var viewTypeData))
            {
                BGManager.Instance.CharacterBg.Setup(viewTypeData.EnvPath, viewTypeData.BgPath, 0, 0,isRaceUI:true);
            }
        }

        /// <summary>
        /// 3Dキャラを表示
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="raceDressId"></param>
        public void Show3DCharacter(int charaId, int raceDressId)
        {
            if (VIEW_DATA_DIC.TryGetValue(_setupParam.ViewType, out var viewTypeData))
            {
                // 無効な衣装は体操服にしておく
                if (raceDressId == 0)
                    raceDressId = (int)ModelLoader.DressID.TrackSuit;
                // 背景＋キャラ
                bool isPlayIdleMotion = TutorialManager.IsTutorialExecuting();    // チュートリアルのみCharaBGにおいてデフォルトでアイドルモーションを再生する
                BGManager.Instance.CharacterBg.Setup(viewTypeData.EnvPath, viewTypeData.BgPath, charaId, raceDressId, isPlayIdleMotion, isRaceUI:true);
                //レースキャラ選択用にカメラ位置補正
                BGManager.Instance.CharacterBg.UpdateCameraPosForRaceUI();
                // この後ボイスを再生する場合があるのでモーションを即時反映
                BGManager.Instance.CharacterBg.Model.PlayableAnimator.UpdateMotion(0f);
            }
        }

        /// <summary>
        /// ボイスを再生
        /// </summary>
        public void PlaySelectedCharaVoice()
        {
            if (_currentSelectDataArray == null || _currentSelectDataArray[_tabIndex] == null) return;

            bool isApplyDressChange = _currentSelectDataArray[_tabIndex].IsPlayer;
            var systemText = AudioManager.Instance.PlaySystemVoice_OtherCharaSelectCommon(
            _currentSelectDataArray[_tabIndex].CharaId, _currentSelectDataArray[_tabIndex].CardId,
                _currentSelectDataArray[_tabIndex].GetRaceDressId(false), isApplyDressChange
            );
            BGManager.Instance.CharacterBg.PlayLipSync(systemText);
        }

        public void SelectToScroll(WorkTrainedCharaData.TrainedCharaData trainedCharaData)
        {
            _cardList.SelectButton(trainedCharaData);
            _cardList.ScrollToCursor(true);
        }

        /// <summary>
        /// ビュワーの表示非表示
        /// </summary>
        /// <param name="visible"></param>
        public void SetVisibleViewer(bool visible)
        {
            if (BGManager.Instance.CharacterBg == null)
                return;

            BGManager.Instance.CharacterBg.SetVisible(visible);
        }

        /// <summary>
        /// キャラタップ
        /// </summary>
        private void OnCharaTap()
        {
            PlaySelectedCharaVoice();
        }

        /// <summary>
        /// 決定ボタンクリック
        /// </summary>
        private void OnClickDecide()
        {
            if (_setupParam.OnDecide == null)
            {
                return;
            }

            if (_setupParam.EnableMultiSelect)
            {
                if (_selectedDataList.Any())
                {
                    _setupParam.OnDecide(_selectedDataList);
                }
            }
            else
            {
                var trainedChara = _currentSelectDataArray[_tabIndex];

                if (trainedChara != null &&
                    _setupParam.ViewType == ViewType.TeamEdit &&
                    _setupParam.ReservedCharaDic != null &&
                    _setupParam.SelectedMemberInfo.TrainedCharaData?.CharaId != trainedChara.CharaId)
                {
                    var reservedSameCharaAsSelected = _setupParam.ReservedCharaDic.FirstOrDefault(x =>
                    {
                        var containedTrainedCharaId = x.Key.Id != trainedChara.Id;
                        var containedCharaId = x.Key.CharaId == trainedChara.CharaId;
                        return containedTrainedCharaId && containedCharaId;
                    });

                    if (reservedSameCharaAsSelected.Key != null)
                    {
                        // チーム編成の重複キャラ解除確認ダイアログを表示
                        DialogTeamStadiumConfirmReleaseChara.Open(reservedSameCharaAsSelected.Key.Id, reservedSameCharaAsSelected.Value, () =>
                        {
                            _setupParam.OnDecide(new List<TrainedCharaData>() { trainedChara });

                        }, () =>
                        {
                            OpenCurrentTrainedCharacterDetail(reservedSameCharaAsSelected.Key.Id);
                        });

                        return;
                    }
                }

                _setupParam.OnDecide(new List<TrainedCharaData>() { trainedChara });
            }
        }

        /// <summary>
        /// キャラを探しに行くボタン
        /// </summary>
        private void OnClickFindButtonIfEmpty()
        {
            _setupParam.OnClickFindButton?.Invoke();
        }

        #region 殿堂入りウマ娘詳細ダイアログ

        /// <summary>
        /// ウマ娘詳細ダイアログを開く
        /// </summary>
        public void OpenCurrentTrainedCharacterDetail(int trainedCharaId)
        {
            if (_currentButtonInfo != null && _currentButtonInfo.TrainedChara.Id == trainedCharaId)
            {
                OpenTrainedCharacterDetailDialog(_currentButtonInfo.TrainedChara, _currentButtonInfo);
            }
            else
            {
                // フィルタ設定等で現在選択可能なリストに存在しない場合は初期化時のボタンリストからボタンを取得
                var hideButtonInfo = _cardList.OriginalButtonInfoList.FirstOrDefault(x => x.TrainedChara != null && x.TrainedChara.Id == trainedCharaId);
                if (hideButtonInfo != null)
                {
                    OpenTrainedCharacterDetailDialog(hideButtonInfo.TrainedChara, hideButtonInfo);
                }
            }
        }
        
        /// <summary>
        /// 殿堂入りウマ娘詳細ダイアログを開く
        /// </summary>
        /// <param name="trainedChara"></param>
        /// <param name="info"></param>
        public void OpenTrainedCharacterDetailDialog(TrainedCharaData trainedChara, CharacterButtonInfo info)
        {
            if (TempData.Instance.DailyRaceData.IsPlayingSkipResult)
            {
                // デイリーレーススキップの回数が10回以上だと
                // スキップ結果ダイアログが開くまでの間の連打・長押しで
                // ウマ娘詳細が開いてしまうので防止
                return;
            }

            if (!_setupParam.IsViewTypePractice)
            {
                // 練習では練習パートナーやフォローユーザーの代表ウマ娘が取れないので再代入しない
                trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(trainedChara.Id);
            }

            TempData.Instance.PracticeRaceData.IsChangeRentalList = false;
            var prevLock = trainedChara.IsLock;
            var prevLockType = trainedChara.FavoriteType;
            var prevMemo = trainedChara.Memo;
            void OnCloseDialog()
            {
                // 二つ名が変更されている場合があるので、名前とステータスパネルを更新
                UpdateNameWithStatus();

                // 練習の場合はリスト更新があるかも
                bool needRefreshPartnerList = _setupParam.IsViewTypePractice && _tabIndex == TAB_PERTNER && TempData.Instance.PracticeRaceData.IsChangeRentalList;
                TempData.Instance.PracticeRaceData.IsChangeRentalList = false;

                if (info == null)
                {
                    info = _currentButtonInfo;
                }

                // お気に入りやメモの変更をチェックしてリスト更新
                var afterLock = trainedChara.IsLock;
                info.IsLock = afterLock;
                var afterLockType = trainedChara.FavoriteType;
                var afterMemo = trainedChara.Memo;
                var isChangedLock = prevLock != afterLock || prevLockType != afterLockType;
                var isChangedMemo = !string.Equals(prevMemo, afterMemo);
                bool needRefreshCharacterButton = isChangedLock || isChangedMemo;
                if (!_needRefreshListOnCloseDialog && needRefreshCharacterButton == false && !needRefreshPartnerList) return;

                var selectButtonInfo = _cardList.CurrentButtonInfoList.FirstOrDefault(x => x.TrainedChara != null && x.TrainedChara.Id == info.TrainedChara.Id);
                if (selectButtonInfo != null)
                {
                    selectButtonInfo.IsLock = afterLock;
                }
                else
                {
                    // 現在表示しているリストにヘッダーのキャラアイコンが含まれてない状態で
                    // ヘッダーアイコンから詳細ダイアログを開いてお気に入り設定をした場合は
                    // 初期化時のボタンリストからボタンを取得してお気に入り設定を変更する
                    var hideButtonInfo = _cardList.OriginalButtonInfoList.FirstOrDefault(x => x.TrainedChara != null && x.TrainedChara.Id == info.TrainedChara.Id && x.TrainedChara.ViewerId == info.TrainedChara.ViewerId);
                    if (hideButtonInfo != null)
                    {
                        hideButtonInfo.IsLock = afterLock;
                        hideButtonInfo.SortInfoType = info.SortInfoType;
                        hideButtonInfo.IsSortAsc = _cardList.IsCurrentAsc;
                    }
                }
                
                // リストそのものに変更があった場合は作り直す
                if (needRefreshPartnerList)
                {
                    UpdateListTrainedChara();
                    return;
                }

                var filters = _cardList.CurrentFilterMenuList;
                bool needSortFilter = SortFilterSetting.IsSensitiveToDetailDialog(filters);
                bool isSensitiveToDetail = SortFilterSetting.IsSensitiveToDetailDialog(_cardList.CurrentSortMenu);
                _cardList.Refresh(needSortFilter || isSensitiveToDetail);
 
                if (_cardList.CurrentButtonInfoList.Count == 0)
                {
                    _cardList.ResetCurrentFilter();
                    _cardList.Refresh(true);
                }
                else
                {
                    // 絞りこみにより、選択不可のキャラのみが残るならフィルターをリセット
                    if (_cardList.CurrentButtonInfoList.TrueForAll(x => _setupParam.IsUnselectableChara(x.TrainedChara)))
                    {
                        _cardList.ResetCurrentFilter();
                        _cardList.Refresh(true);
                    }
                }

                // 絞り込み条件に設定中の要素が更新された場合は上のRefreshで
                // CurrentButtonInfoListの中身が更新されるので取得し直す
                // 例：絞り込み「お気に入り設定」中にウマ娘詳細からお気に入り設定をON⇒OFF
                selectButtonInfo = _cardList.CurrentButtonInfoList.FirstOrDefault(x => x.TrainedChara != null && x.TrainedChara.Id == info.TrainedChara.Id && x.TrainedChara.ViewerId == info.TrainedChara.ViewerId);

                // 選択中のキャラが表示されてなければ、選択可能な先頭のキャラを選択する
                if (selectButtonInfo == null)
                {
                    var firstButtonInfo = _cardList.CurrentButtonInfoList.FirstOrDefault(x => !_setupParam.IsUnselectableChara(x.TrainedChara));
                    if (firstButtonInfo != null)
                    {
                        OnChangeCharacterButton(firstButtonInfo);
                    }
                }

                _needRefreshListOnCloseDialog = false;
            }

            switch (_setupParam.ViewType)
            {
                case ViewType.Champions:
                    // チャンミの場合は戦績付きキャラ詳細を開く
                    var championsRankCountData = WorkDataManager.Instance.ChampionsData.GetTrainedCharaRankCountData(trainedChara.Id);
                    if (championsRankCountData != null)
                    {
                        DialogTrainedCharacterChampionsDetail.Open(trainedChara, championsRankCountData, OnCloseDialog);
                    }
                    return;
                case ViewType.Heroes:
                    // リーグオブヒーローズの場合は専用の戦績付きキャラ詳細を開く
                    DialogTrainedCharacterHeroesDetail.Open(trainedChara, onClose:OnCloseDialog, isChangeableNickName:true);
                    return;
                case ViewType.Daily:
                case ViewType.Legend:
                case ViewType.RoomMatch:
                case ViewType.PracticeRace:
                case ViewType.PracticeDeckEdit:
                case ViewType.TeamEdit:
                case ViewType.Ultimate:
                    // 矢印ボタンで表示が切替可能な殿堂入りウマ娘詳細ダイアログを出す
                    var buttonInfoList = _cardList.CurrentButtonInfoList
                        .Where(info => info.TrainedChara != null && info.Interactable)
                        .ToList();
                    var index = buttonInfoList.FindIndex(info => info.TrainedChara.Id == trainedChara.Id);
                    if (index < 0)
                    {
                        Debug.LogError("一覧にいない殿堂入りウマ娘の詳細ダイアログを開こうとしている");
                        return;
                    }

                    // 非同期にSetupParameterを取得して、取得できたら詳細ダイアログを開く
                    CreateDetailDialogSetupParameter(index, buttonInfoList,
                        setupParam => DialogTrainedCharacterDetail.OpenWithArrowButton(setupParam),
                        OnCloseDialog);
                    return;
            }

            // 通常の殿堂入りウマ娘詳細ダイアログを出す
            var parameter = DialogTrainedCharacterDetail.CreateSetupParameter(trainedChara);
            var viewerId =
                trainedChara.IsGhost || trainedChara.IsRental ? trainedChara.OwnerViewerId.GetDecrypted() :
                !trainedChara.IsPlayer ? trainedChara.ViewerId : 0;

            DialogTrainedCharacterDetail.OpenWithGetViewerName(parameter, viewerId, OnCloseDialog);
        }

        /// <summary>
        /// 殿堂入りウマ娘の詳細ダイアログのSetupParameterを非同期に取得して、取得完了時にコールバックをよぶ
        /// </summary>
        private void CreateDetailDialogSetupParameter(int index, List<CharacterButtonInfo> buttonInfoList, Action<DialogCharacterDetailBase.SetupParameter> onCreate, Action onClose)
        {
            var buttonInfo = buttonInfoList.ElementAtOrDefault(index);
            var trainedChara = buttonInfo?.TrainedChara;

            if (trainedChara == null)
            {
                Debug.LogError("一覧にいない殿堂入りウマ娘の詳細ダイアログを開こうとしている");
                return;
            }

            var viewerId =
                (trainedChara.IsGhost || trainedChara.IsRental) ? trainedChara.OwnerViewerId.GetDecrypted() :
                !trainedChara.IsPlayer ? trainedChara.ViewerId : 0;

            // 他人の殿堂入りウマ娘
            if (viewerId > 0)
            {
                // 練習の場合は、トレーナー名をWorkから取得する
                var friendTrainerName = string.Empty;
                if (_setupParam.IsViewTypePractice)
                {
                    friendTrainerName = WorkDataManager.Instance.PracticeRaceData.GetPartnerOwnerName(trainedChara);
                    if (!string.IsNullOrEmpty(friendTrainerName))
                    {
                        var partnerSetupParameter = DialogTrainedCharacterDetail.CreateSetupParameterWithCharaSwitchButtonAsync(
                            trainedChara, index, buttonInfoList.Count,
                            (nextIndex, onGet) => CreateDetailDialogSetupParameter(nextIndex, buttonInfoList, onGet, onClose),
                            OnCloseDetailDialog,
                            updateCharaButtonFrame: UpdateCharacterButtonFrameOnDialog,
                            trainerName: friendTrainerName);

                        // ウマ娘が練習パートナーなら、練習パートナー・登録ボタンを専用のセットアップ
                        if (trainedChara.IsGhost)
                        {
                            partnerSetupParameter.GetPartsParameter<PartsPracticeRacePartner.SetupParameter>((param) =>
                            {
                                param.TrainedChara = trainedChara;
                                param.OwnerTrainedCharaId = trainedChara.OwnerTrainedCharaId;
                                param.OwnerViewerId = trainedChara.OwnerViewerId;
                            });
                        }

                        onCreate?.Invoke(partnerSetupParameter);
                        return;
                    }
                }

                // それ以外はAPIでトレーナー名を取得する
                var req = new FriendSearchRequest { friend_viewer_id = viewerId };
                req.Send(res =>
                {
                    // 取得したトレーナー名をSetupParameterに反映
                    friendTrainerName = res.data.user_info_summary?.name ?? string.Empty;
                    var otherSetupParameter = DialogTrainedCharacterDetail.CreateSetupParameterWithCharaSwitchButtonAsync(
                        trainedChara, index, buttonInfoList.Count,
                        (nextIndex, onGet) => CreateDetailDialogSetupParameter(nextIndex, buttonInfoList, onGet, onClose),
                        OnCloseDetailDialog,
                        updateCharaButtonFrame: UpdateCharacterButtonFrameOnDialog,
                        trainerName: friendTrainerName);

                    // ウマ娘が練習パートナーなら、練習パートナー・登録ボタンを専用のセットアップ
                    if (trainedChara.IsGhost)
                    {
                        otherSetupParameter.GetPartsParameter<PartsPracticeRacePartner.SetupParameter>((param) =>
                        {
                            param.TrainedChara = trainedChara;
                            param.OwnerTrainedCharaId = trainedChara.OwnerTrainedCharaId;
                            param.OwnerViewerId = trainedChara.OwnerViewerId;
                        });
                    }

                    onCreate?.Invoke(otherSetupParameter);
                },
                stallOneSecond: true);
                return;
            }

            // 自分の殿堂入りウマ娘
            // Note: 矢印ボタンで表示を切り替えた際・ダイアログを閉じる際に、UIの更新が必要かの判定処理を追加
            var onChangeCharaAction = DialogTrainedCharacterDetail.CreateOnCloseEditableCharaDataAction(
                trainedChara, isEdit => CheckNeedRefreshCharaList(buttonInfo, isEdit));
            var setupParameter = DialogTrainedCharacterDetail.CreateSetupParameterWithCharaSwitchButtonAsync(
                trainedChara, index, buttonInfoList.Count,
                (nextIndex, onGet) => CreateDetailDialogSetupParameter(nextIndex, buttonInfoList, onGet, onClose),
                OnCloseDetailDialog,
                onChangeCharaAction,
                UpdateCharacterButtonFrameOnDialog);

            onCreate?.Invoke(setupParameter);

            void OnCloseDetailDialog(bool isDispCharaChanged)
            {
                onClose?.Invoke();

                if (isDispCharaChanged)
                {
                    ScrollAndSelectButton(buttonInfo);
                }
            }

            void UpdateCharacterButtonFrameOnDialog(ref CharacterButton button)
            {
                // ButtonAccesoryがなかったら生成
                if (button.PartsButtonAccessory == null)
                {
                    button.SetupChildPrefab(_buttonAccessoryPrefab.gameObject);
                    button.SetSizeType(button.CurrentSizeType);
                }

                // 練習パートナー選択の画面は、表示内容が詳細ダイアログ側の操作で変わるので専用の処理
                if (_setupParam.IsViewTypePractice && _tabIndex == TAB_PERTNER)
                {
                    (var viewerId, var trainedCharaId) = PracticeRaceUtil.GetOwnerTrainedCharaInfo(trainedChara);
                    var isPartner = WorkDataManager.Instance.PracticeRaceData.PracticePartnerDataDict.Values.Any(data => data.OwnerViewerId == viewerId && data.OwnerTrainedCharaId == trainedCharaId);
                    var selectType = (_setupParam.ViewType == ViewType.PracticeRace) ?
                        PracticeRaceCharacterSelectViewController.SelectType.Race :
                        PracticeRaceCharacterSelectViewController.SelectType.DeckEdit;

                    // 「フォロー」アイコン・ボタンをグレーアウトするかの表示設定 (出走キャラ一括選択と共通の処理)
                    PracticeRaceUtil.UpdateCharaIconFrameOnDialog(ref button, trainedChara, selectType, isPartner);

                    if (isPartner)
                    {
                        // 練習パートナー登録済なら、必要なら"設定中"ラベルの表示
                        var isSet = _setupParam.SettingCharaList.Contains(trainedChara);
                        button.PartsButtonAccessory.SetActiveReservedLabel(isSet);
                        button.PartsButtonAccessory.SetReservedLabelText(TextId.Character0077.Text());
                        button.PartsButtonAccessory.SetReservedLabelPos(DEFAULT_RESERVED_LABEL_POS);
                        button.PartsButtonAccessory.SetReservedLabelFontSize(TextFormat.FontSize.Size_32);
                    }
                    else
                    {
                        // 練習パートナー未登録 or 登録が解除されている場合は"設定中"ラベルを非表示に
                        button.PartsButtonAccessory.SetActiveReservedLabel(false);
                    }

                    return;
                }

                // 練習パートナー選択以外は、リスト上でのボタンの見た目と同じになるようダイアログ上のアイコンの見た目を更新
                ResetButtonAccessory(button.PartsButtonAccessory);
                button.Info.TrainedChara = trainedChara;
                button.MyButton.SetLookEnableColor(buttonInfo.Interactable);
                UpdateCharacterButtonFrame(button);
            }
        }

        /// <summary>
        /// 殿堂入りウマ娘詳細ダイアログを閉じたときのコールバック
        /// </summary>
        private void ScrollAndSelectButton(CharacterButtonInfo buttonInfo)
        {
            var trainedCharaData = buttonInfo.TrainedChara;
            if (trainedCharaData == null)
            {
                return;
            }

            // 練習パートナー・フォローしているユーザーの代表ウマ娘が入れ替わっている可能性があるので再取得
            CalcSelectPracticeTrainedChara(out var viewerId, out var trainedCharaId, trainedCharaData);

            // ViewerIdとTrainedCharaIdでbuttonInfoを取得しなおす
            var newButtonInfo = _cardList.CharacterListUI.CurrentButtonInfoList.FirstOrDefault(info => info.TrainedChara?.ViewerId == viewerId && info.TrainedChara?.Id == trainedCharaId);
            if (newButtonInfo == null)
            {
                return;
            }

            // リストをスクロールさせる
            _cardList.ScrollButtonPositionToTop(newButtonInfo);

            // 選択可能なキャラは選択する
            if (!_setupParam.IsUnselectableCanLongTapTrainedChara(newButtonInfo.TrainedChara))
            {
                OnChangeCharacterButton(newButtonInfo);
            }
        }

        private void CalcSelectPracticeTrainedChara(out long viewerId, out int trainedCharaId, WorkTrainedCharaData.TrainedCharaData trainedChara)
        {
            if (!_setupParam.IsViewTypePractice || _tabIndex != TAB_PERTNER)
            {
                // 練習パートナー選択タブを開いていなければそのままの値を返す
                viewerId = trainedChara.ViewerId;
                trainedCharaId = trainedChara.Id;
                return;
            }

            PracticeRaceUtil.CalcPracticePartnerDataByRegisterState(out viewerId, out trainedCharaId, trainedChara);
        }

        /// <summary>
        /// 詳細ダイアログを閉じるときに、リストの表示更新をする必要があるかどうか
        /// </summary>
        private void CheckNeedRefreshCharaList(CharacterButtonInfo buttonInfo, bool isEdited)
        {
            if (!isEdited || buttonInfo.TrainedChara == null)
            {
                return;
            }

            // ボタンにお気に入りアイコンを出すかの設定変更
            buttonInfo.IsLock = buttonInfo.TrainedChara.IsLock;

            // 殿堂入りウマ娘のWorkが編集されているので、ダイアログを閉じた際にリストを更新させる
            _needRefreshListOnCloseDialog = true;
        }

        #endregion // 殿堂入りウマ娘詳細ダイアログ


        /// <summary>
        /// trainedCharaDataからButtonInfoが存在するかを返す
        /// </summary>
        /// <param name="trainedCharaData"> 調べたいTrainedCharaData </param>
        /// <returns></returns>
        public bool IsExistCharacterButtonInfo(TrainedCharaData trainedCharaData)
        {
            if (_cardList == null)
                return false;

            return _cardList.CurrentButtonInfoList.Any(info => info.TrainedChara == trainedCharaData);
        }
        /// <summary>
        /// 注意文言
        /// </summary>
        /// <param name="notice"></param>
        /// <param name="noticeWithDetail"></param>
        /// <param name="detailButtonCallback"></param>
        private void UpdateNotice(string notice, string noticeWithDetail, Action detailButtonCallback)
        {
            if (_noticeRoot == null)
                return;

            _noticeText.SetActiveWithCheck(false);
            _noticeTextWithDetail.SetActiveWithCheck(false);
            if(string.IsNullOrEmpty(notice) && string.IsNullOrEmpty(noticeWithDetail))
            {
                _noticeRoot.SetActive(false);
                return;
            }
            _noticeRoot.SetActiveWithCheck(true);

            if (!string.IsNullOrEmpty(notice) && _noticeText != null)
            {
                _noticeText.SetActiveWithCheck(true);
                _noticeText.text = notice;
            }
            if (!string.IsNullOrEmpty(noticeWithDetail) && _noticeTextWithDetail != null)
            {
                _noticeTextWithDetail.SetActiveWithCheck(true);
                _noticeTextWithDetail.text = noticeWithDetail;
                if (_noticeDetailButton != null)
                {
                    _noticeDetailButton.SetOnClick(() => detailButtonCallback?.Invoke());
                }
            }

            // 画面サイズが「16:9」以下の時、出走条件とスキルの一覧表示が重なるので高さ調整用
            var safeArea = UIManager.Instance.GetSafeArea();
            float screenAspect = Mathf.Floor(safeArea.width / safeArea.height * 100f) / 100f;
            float targetAspect = Mathf.Floor(((float)GameDefine.BASE_SCREEN_WIDTH / (float)GameDefine.BASE_SCREEN_HEIGHT)*100f) / 100f;
            if (_setupParam.UseLargeNotice)
            {
                // 範囲が大きいときは2:1で調整
                targetAspect = TARGET_ASPECT_LARGE_NOTICE;
            }

            if (targetAspect <= screenAspect)
            {
                AdjustSkillListHeight();
            }
        }

        /// <summary>
        /// 注意文言位置更新
        /// </summary>
        private void UpdateNoticePos(bool visibleStatus)
        {
            if (_noticeRoot == null)
                return;

            var noticeRect = _noticeRoot.transform as RectTransform;

            if (visibleStatus)
            {
                //ステータス表示中はステータスの右上になるよう位置調整
                noticeRect.anchoredPosition = _setupParam.UseLargeNotice ? NOTICE_POS_VISIBLE_STATUS_LARGE : NOTICE_POS_VISIBLE_STATUS;
            }
            else
            {
                //ステータス出てない（「選択してください」のテキストが出てる）なら真ん中
                noticeRect.anchoredPosition = NOTICE_POS_NO_VISIBLE_STATUS;
            }
        }

        /// <summary>
        /// リストの要素に変更があった場合にタブを更新
        /// </summary>
        public void UpdateListTrainedChara()
        {
            var newSetupPraram = _setupParam.CreateSetupPraramFunc?.Invoke();

            if (newSetupPraram == null)
            {
                return;
            }

            var isEmpty = _currentSelectDataArray[_tabIndex] == null;
            _setupParam = newSetupPraram;

            void Impl()
            {
                SetupCardList(_tabIndex);

                UpdateListEmptyObject();
                UpdateSelectButtonInteractable();

                if (_currentSelectDataArray[_tabIndex] == null)
                {
                    // ステータスパネルを非表示にする処理
                    UpdateNameWithStatus();
                }

                if (isEmpty)
                {
                    // 以前が空の状態だった場合はボイス周りの初期化が走らない
                    PlaySelectedCharaVoice();
                }
            }

            // ダウンロード登録をしてからリストを更新
            if (_setupParam.RegisterDownload == null)
            {
                Impl();
            }
            else
            {
                _setupParam.RegisterDownload?.Invoke(Impl);
            }
        }

        /// <summary>
        /// スキル一覧の高さ調整
        /// </summary>
        public void AdjustSkillListHeight()
        {
            _statusPanel.AdjustSkillListHeight(_setupParam.UseLargeNotice ? SKILL_LIST_SIZE_OFFSET_LARGE : SKILL_LIST_SIZE_OFFSET);
        }

        /// <summary>
        /// スキル一覧の高さ調整（画面サイズ「16:9」対応）
        /// 最強チーム：キャプテン選択画面向け
        /// </summary>
        public void AdjustSkillListHeightForTeamBuilding()
        {
            var safeArea = UIManager.Instance.GetSafeArea();
            float screenAspect = Mathf.Floor(safeArea.width / safeArea.height * 100f) / 100f;
            float targetAspect = TARGET_ASPECT_LARGE_NOTICE;

            if (targetAspect <= screenAspect)
            {
                _statusPanel.AdjustSkillListHeight(TEAM_BUILDING_CHANGE_CAPTAIN_SKILL_LIST_SIZE_OFFSET_LARGE);
            }
        }

        #region チュートリアル専用処理

        /// <summary>
        /// 編成中のキャラを切り替えるため、対象のキャラボタンにフォーカス
        /// </summary>
        /// <param name="toFocus"></param>
        public void TutorialFocusChangeCharaButton(System.Action<CharacterButton> toFocus)
        {
            // チュートリアル育成で付与されたダイワスカーレットにフォーカスする
            CharacterButtonInfo targetButtonInfo = null;
            foreach (var cardInfo in _cardList.CurrentButtonInfoList)
            {
                if (cardInfo.Id == TutorialSingleMode.TRAINING_CARD_ID)
                {
                    // 初期付与キャラはファン数が0人なのでファン数が0かどうかで育成キャラとして判定
                    if (cardInfo.TrainedChara.Fans > 0)
                    {
                        targetButtonInfo = cardInfo;
                    }
                }
            }

            // すぐに掴まないのでTutorialMaskでタッチ不可にしておく.
            TutorialManager.TutorialGuideUI.Show();

            // すぐに掴むと変な位置でフォーカスされてしまうので遅らせてからフォーカス.
            DOVirtual.DelayedCall(
                TutorialOutGame.TEAM_RACE_DECK_CARD_SELECT_WAIT_TIME,
                () =>
                {
                    toFocus(_cardList.GetButton(targetButtonInfo));
                });
        }
        
        /// <summary>
        /// 決定ボタンをフォーカス
        /// </summary>
        /// <param name="toFocusDecideButton"></param>
        public void TutorialFocusDecideButton(System.Action<ButtonCommon> toFocusDecideButton)
        {
            toFocusDecideButton(_selectButton);
        }

        #endregion

        #region 練習専用処理

        /// <summary>
        /// _currentSelectDataと同じウマ娘がいたら返す
        /// </summary>
        /// <returns></returns>
        private TrainedCharaData FindSamePracticeRentalCharaInfo()
        {
            if (_currentSelectDataArray[_tabIndex] == null)
            {
                return null;
            }

            return WorkDataManager.Instance.PracticeRaceData.FindSameRentalTrainedChara(_currentSelectDataArray[_tabIndex], _currentSelectePracticePartnerData);
        }

        #endregion
    }
}
