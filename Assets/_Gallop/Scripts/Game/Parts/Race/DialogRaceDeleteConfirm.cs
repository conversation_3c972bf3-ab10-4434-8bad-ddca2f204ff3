using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// シングル再開確認用ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogRaceDeleteConfirm : DialogInnerBase
    {
        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField]
        private RectTransform _warningRoot = null;

        [SerializeField]
        private TextCommon _warningText = null;

        private void Setup(string warningText)
        {
            if (warningText != null)
            {
                _warningText.text = warningText;
            }

            LayoutRebuilder.ForceRebuildLayoutImmediate(_warningRoot);
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="onDecide"></param>
        /// <param name="onDelete"></param>
        public static void Open( System.Action onDecide, string warningText = null )
        {
            var dialogObject = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_RACE_DELETE_CONFIRM));
            var dialogContent = dialogObject.GetComponent<DialogRaceDeleteConfirm>();
            dialogContent.Setup(warningText);

            var data = dialogContent.CreateDialogData();
            data.Title = TextId.Race0652.Text();
            data.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            data.AutoClose = false;
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = (dialog) =>
            {
                if (dialog != null)
                {
                    dialog.Close();
                }
            };
            data.RightButtonText = TextId.Common0003.Text();
            //データ削除確定。通信が終わってからダイアログを全て削除しているのでそこで閉じられる
            data.RightButtonCallBack = _ => { onDecide?.Invoke(); };

            DialogManager.PushDialog(data);
        }
    }
}
