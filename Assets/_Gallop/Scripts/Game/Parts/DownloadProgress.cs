using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ダウンロード進捗表示（分割DL用）
    /// </summary>
    public class DownloadProgress : MonoBehaviour
    {
        [SerializeField] private UnityEngine.UI.Slider _slider;
        [SerializeField] private TextCommon _progressText;
        
        
        private const TextId PROGRESS_TEXT_FORMAT = TextId.Outgame0443;
        private const float DELAY_HIDE_TIME = 0.1f;
        private const float INVALID_HIDE_DELAY = -1.0f;

        private float _delayHideTimer = INVALID_HIDE_DELAY;

        public void Initialize()
        {
            Hide(false);
        }

        /// <summary>
        /// 表示状態にする
        /// </summary>
        public void Show()
        {
            _delayHideTimer = INVALID_HIDE_DELAY;        // 非表示タイマーを無効化しておく
            gameObject.SetActiveWithCheck(true);
        }
        
        /// <summary>
        /// 非表示状態にする
        /// </summary>
        /// <param name="isDelayied">少しの時間残してから非表示にする</param>
        public void Hide(bool isDelayied = true)
        {
            if (!isDelayied)
            {
                HideInternal();
                return;
            }

            _delayHideTimer = DELAY_HIDE_TIME;
        }

        /// <summary>
        /// 実際の非表示処理
        /// </summary>
        protected virtual void HideInternal()
        {
            _delayHideTimer = INVALID_HIDE_DELAY;
            
            SetProgressText(0.0f);
            gameObject.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 進行率設定（0.0～1.0)
        /// </summary>
        /// <param name="progress"></param>
        public void SetProgressText(float progress)
        {
            _slider.value = progress;
            
            float floatProgress = Mathf.Floor(progress * 10000.0f) / 100.0f;
            _progressText.text = PROGRESS_TEXT_FORMAT.Format(floatProgress);
        }

        private void Update()
        {
            UpdateInternal();
            
            if (_delayHideTimer > 0.0f)
            {
                _delayHideTimer -= Time.deltaTime;
                if (_delayHideTimer <= 0.0f)
                {
                    HideInternal();
                }
            }
        }

        protected virtual void UpdateInternal() {}
    }
}