using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Gallop.AnimSequenceHelper;

namespace Gallop
{
    /// <summary>
    /// 【全国興行】興行完了画面：ストーリーイベント開催中のみ表示されるページ
    /// </summary>
    [AddComponentMenu("")]
    public class PartsJobsResultStoryEventPage : MonoBehaviour
    {
        [SerializeField] private CanvasGroup _root = null;

        [Header("イベントロゴ")]
        [SerializeField] private RawImageCommon _eventLogoImage;

        [Header("獲得イベントPt")]
        [SerializeField] private CanvasGroup _addEp = null;
        [SerializeField] private CanvasGroup _addEpValue = null;
        [SerializeField] private TextCommon _addEpValueText = null;
        [SerializeField] private CountupModifier _addEpValueCountUpModifier = null;
        [SerializeField] private CanvasGroup _jobsEpValue = null;
        [SerializeField] private TextCommon _jobsEpValueText = null;

        [Header("累計イベントPt")]
        [SerializeField] private CanvasGroup _totalEpHeader = null;
        [SerializeField] private CanvasGroup _totalEpTitle = null;
        [SerializeField] private CanvasGroup _totalEpValue = null;
        [SerializeField] private BitmapTextCommon _totalEpValueText = null;
        [SerializeField] private float _totalEpValueTextPosXAdjustRate = 0.0745f;
        [SerializeField] private CountupModifier _totalEpValueCountUpModifier = null;
        [SerializeField] private ContentSizeFitter _totalEpValueContentSizeFitter = null;

        [Header("イベントPt報酬パネル")]
        [SerializeField] private PartsSingleModeResultEventRewardItem _partsRewardItem = null;

        [Header("ストーリーパネル")]
        [SerializeField] private PartsSingleModeResultStoryEventStoryPanel _partsStoryPanel = null;

        [Header("イリアニメ調整パラメータ")]
        [SerializeField] private float _inWaitAllIn = 0.3f;
        [SerializeField] private float _inWaitAddEpCount = 0.966f;
        [SerializeField] private float _inWaitTotalEpCount = 1.2f;

        [SerializeField] private float _inWaitNextItemRewadButtonIn = 0.2f;

        [SerializeField] private float _inWaitItemDrop = 0.05f;
        [SerializeField] private float _inWaitNextButton = 0.2f;

        [Header("ルーレットコインの獲得上限UI")]
        [SerializeField] private PartsJobsResultStoryEventRouletteCoinLimitUI _rouletteCoinLimitUiPrefab;

        [Header("アニメ")]
        public CanvasGroup[] _inAnimationMoveObjects;


        private int _storyEventId = 0;

        /// <summary>獲得イベントPt量</summary>
        private int _addEventPt = 0;

        /// <summary>累計イベントPt量（加算前）</summary>
        private int _totalEventPtBefore = 0;
        /// <summary>累計イベントPt量（加算後）</summary>
        private int _totalEventPtAfter = 0;
        /// <summary>累計イベントPtタイトルのデフォルト位置</summary>
        private Vector2 _totalEpTitleDefaultPos = Vector2.zero;
        /// <summary>累計イベントPt値のデフォルト位置</summary>
        private Vector2 _totalEpValueDefaultPos = Vector2.zero;

        /// <summary>イベントPt報酬パネル、ストーリーパネルのアニメが終わる度にカウントする</summary>4
        private int _panelAnimeCompleteCount = 0;

        /// <summary>画面下部のボタン</summary>
        private ButtonCommon _footerNextButton = null;

        /// <summary>スキップボタン</summary>
        protected ButtonCommon _skipButton = null;
        /// <summary>スキップ対象Sequenceのリスト（スキップ時に停めたいSequenceをここに貯めておく）</summary>
        private List<Sequence> _skipableSeqList = new List<Sequence>();
        private List<Vector3> _inAnimeDefaultPositionList = new List<Vector3>();

        /// <summary>ルーレットコインを1つでも獲得したか</summary>
        private bool _haveDropItem = false;
        /// <summary>獲得ルーレットコイン情報</summary>
        private List<WorkDataUtil.RewardItemInfo> _mergedDropItemInfoList = null;
        /// <summary>ルーレットコイン獲得演出ダイアログ</summary>
        private DialogStoryEventItemDrop _dialogStoryEventItemDrop = null;

        /// <summary>到達報酬制覇（イベントPtカンスト）ダイアログを表示するか</summary>
        private bool _isOpenDialogCompleteStoryEventPt = false;

        /// <summary>終わり際の各種ダイアログ連続表示 コールバック</summary>
        private System.Action _nearEndContinuousDialogsAction = null;

        /// <summary>全国興行のワークデータ</summary>
        private WorkJobsData _workJobsData => WorkDataManager.Instance.JobsData;
        private SingleModeResultDataContainer.StoryEventInfoData _workResultStoryEventInfoData => _workJobsData.ResultStoryEventInfoData;


        /// <summary>
        /// ストーリーイベントの種類毎に固有なアセットをダウンロード
        /// </summary>
        public static IEnumerator DownloadAssets()
        {
            int storyEventId = WorkDataManager.Instance.JobsData.ResultStoryEventInfoData.StoryEventId;

            var register = DownloadManager.GetNewRegister();

            // イベントロゴ
            var eventLogoPath = ResourcePath.GetStoryEventLogoPath(storyEventId);
            register.RegisterPath(eventLogoPath);

            // イベントPtのビットマップフォント
            BitmapTextCommon.RegistDownload(register, TextFormat.BitmapFont.TexStoryEventPt);

            // ストーリーサムネ
            DialogStoryReleaseNotice.RegisterDownloadInStoryEvent(register, storyEventId);

            // 到達報酬制覇ダイアログ
            DialogSingleModeResultCompleteEventReward.RegisterDownload(register, DialogSingleModeResultCompleteEventReward.EventType.StoryEvent, storyEventId);

            // ルーレットコイン獲得演出
            DialogStoryEventItemDrop.RegisterDownload(register);

            // サウンド
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_UI_COUNT);
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_RACE_STAMP);
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_TRA_RESULT_POINT_WINDOW);
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_TRA_RESULT_POINT_REACH);

            DownloadManager.Instance.FixDownloadList(ref register);
            bool isComplete = false;
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                isComplete = true;
            });
            yield return new WaitWhile(() => isComplete == false);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(ButtonCommon footerNextButton, ButtonCommon skipButton, System.Action nearEndContinuousDialogsAction)
        {
            _footerNextButton = footerNextButton;
            _skipButton = skipButton;
            _nearEndContinuousDialogsAction = nearEndContinuousDialogsAction;

            // 各オブジェクトのアクティブ状態を初期化
            InitializeObjectActiveSetting();

            // ルーレットコイン獲得演出の初期化
            InitializeDropItem(_workResultStoryEventInfoData.DropItemInfoList);

            // 獲得イベントPtパネル
            SetupAddEpPanel();

            // 累計イベントPtをセット
            {
                _totalEventPtAfter = _workResultStoryEventInfoData.TotalEventPoint; // 後でアニメで表示さえるので値だけ保持しておく
                _totalEventPtBefore = Mathf.Max(_totalEventPtAfter - _addEventPt, 0);

                // タイトルと値のデフォルト位置を後のアニメのために保持しておく
                _totalEpTitleDefaultPos = UIUtil.GetAnchoredPosition(_totalEpTitle.transform);
                _totalEpValueDefaultPos = UIUtil.GetAnchoredPosition(_totalEpValue.transform);
            }

            // イベントPt報酬パネル
            {
                _partsRewardItem.Setup(CreatePartsRewardItemSetupParam(
                    _workResultStoryEventInfoData.StoryEventPointRewardItemInfoList,
                    _workResultStoryEventInfoData.StoryEventPointRewardIdList
                    ));
            }

            // ストーリーパネル
            {
                _partsStoryPanel.Setup(_storyEventId, _totalEventPtBefore, _totalEventPtAfter, _workResultStoryEventInfoData.NewStoryIdList);
            }

            // 到達報酬制覇ダイアログを表示するか
            _isOpenDialogCompleteStoryEventPt = _workResultStoryEventInfoData.IsOpenDialogCompleteStoryEventPt;
        }

        /// <summary>各オブジェクトのアクティブ状態や透明度を初期化</summary>
        private void InitializeObjectActiveSetting()
        {
            _root.alpha = 0f;
            _addEp.alpha = 0f;
            _totalEpHeader.alpha = 0f;
            _totalEpTitle.gameObject.SetActiveWithCheck(false);
            _totalEpValue.gameObject.SetActiveWithCheck(true);
            _totalEpValue.alpha = 0f;
            _partsRewardItem.gameObject.SetActiveWithCheck(false);
            _partsStoryPanel.gameObject.SetActiveWithCheck(false);
            _footerNextButton.SetActiveWithCheck(false);
            _skipButton.gameObject.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 獲得イベントPtパネルのセットアップ
        /// </summary>
        private void SetupAddEpPanel()
        {
            // イベントロゴをセット
            {
                _storyEventId = _workResultStoryEventInfoData.StoryEventId;
                var eventLogoPath = ResourcePath.GetStoryEventLogoPath(_storyEventId);
                _eventLogoImage.texture = ResourceManager.LoadOnView<Texture>(eventLogoPath);
            }

            // 獲得イベントPtの数値
            {
                _addEventPt = _workResultStoryEventInfoData.AddEventPoint; // 後でアニメで表示させるので値だけ保持しておく
            }

            // 獲得上限
            {
                int jobsEpAfter = _workJobsData.ResultLimitInfo.after_jobs_story_event_point;
                int jobsEpLimit = _workJobsData.ResultLimitInfo.jobs_story_event_point_limit;
                int jobsEpRemain = Mathf.Max(jobsEpLimit - jobsEpAfter, 0);

                // "99,999/99,999pt"のようなテキスト
                if (jobsEpRemain == 0) // 残り0になった時だけなら分子の色を変える
                {
                    _jobsEpValueText.text = TextId.Jobs408042.Format(0.ToString(), jobsEpLimit.ToString(TextUtil.CommaSeparatedFormat)); // 分子がオレンジ
                }
                else
                {
                    _jobsEpValueText.text = TextId.Jobs408041.Format(jobsEpRemain.ToString(TextUtil.CommaSeparatedFormat), jobsEpLimit.ToString(TextUtil.CommaSeparatedFormat)); // 分子が茶色
                }
            }
        }

        /// <summary>イベントPt報酬パネルのSetupParamを作る</summary>
        private PartsSingleModeResultEventRewardItem.SetupParam CreatePartsRewardItemSetupParam(
            List<WorkDataUtil.RewardItemInfo> rewardItemInfoList,
            List<int> rewardItemIdList
            )
        {
            var setupParam = new PartsSingleModeResultEventRewardItem.SetupParam
            {
                TitleText = TextId.StoryEvent0011.Text(),
                PointSuffix = TextId.StoryEvent0002.Text(),
                BeforePoint = (ulong)_totalEventPtBefore,
                AfterPoint = (ulong)_totalEventPtAfter,
                RewardItemInfoList = rewardItemInfoList,
                RewardItemIdList = rewardItemIdList
            };

            // 「次」に獲得できる「イベントPt達成報酬アイテム」の情報。後でアニメ表示するために保持しておく（全て獲得済みなら無効な値が入る）
            var nextEventPtRewardItem = StoryEventUtil.GetNextRewardItem(_storyEventId, _totalEventPtAfter);
            setupParam.NextRewardId = nextEventPtRewardItem?.Id ?? PartsSingleModeResultEventRewardItem.SetupParam.INVALID_REWARD_ITEM_ID;

            // 「最後」に獲得できる「イベントPt達成報酬アイテム」の情報。後でアニメ表示するために保持しておく
            var lastEventPtRewardItem = StoryEventUtil.GetLastRewardItem(_storyEventId);
            setupParam.LastRewardId = lastEventPtRewardItem?.Id ?? PartsSingleModeResultEventRewardItem.SetupParam.INVALID_REWARD_ITEM_ID;

            // 報酬データ取得
            setupParam.GetRewardItem = id =>
            {
                var rewardData = MasterDataManager.Instance.masterStoryEventPointReward.Get(id);
                if (rewardData == null)
                {
                    return null;
                }

                return new PartsSingleModeResultEventRewardItem.EventRewardItemInfo()
                {
                    Id = id,
                    ItemId = rewardData.ItemId,
                    ItemCategory = rewardData.ItemCategory,
                    ItemNum = rewardData.ItemNum,
                    Point = (ulong)rewardData.Point,
                };
            };
            setupParam.GetPrevRewardItem = id =>
            {
                var rewardData = MasterDataManager.Instance.masterStoryEventPointReward.Get(id);
                if (rewardData == null)
                {
                    return null;
                }

                var prevReward = StoryEventUtil.GetPrevRewardItem(rewardData);
                return prevReward == null ? null : setupParam.GetRewardItem(prevReward.Id);
            };

            setupParam.OnClickRewardListButton = OnClickNextItemRewardButton;
            return setupParam;
        }

        /// <summary>「報酬一覧」ボタン押下時処理</summary>
        private void OnClickNextItemRewardButton()
        {
            DialogStoryEventReward.Open();
        }

        /// <summary>ルーレットコイン獲得演出の初期化</summary>
        private void InitializeDropItem(List<WorkDataUtil.RewardItemInfo> dropItemInfoList)
        {
            // 関連変数をクリア
            _haveDropItem = false;
            _mergedDropItemInfoList = null;

            // ルーレットコインを1つでも獲得しているか判定しておく
            var dropItemNum = (dropItemInfoList != null) ? dropItemInfoList.Count : 0;
            if (dropItemNum == 0)
            {
                return;
            }
            _haveDropItem = true;

            // 同じアイテムは1つのアイコンでまとめて表示する
            // （例えば、ルーレットコイン10個セットと20個セットが両方当選したら、ルーレットコインのアイコンは1つで、中に「x30」と表示する）
            {
                // ドロップアイテム
                if (dropItemNum > 0)
                {
                    var mergedDropItemInfoDict = new Dictionary<int, WorkDataUtil.RewardItemInfo>(); // int==itemId
                    {
                        foreach (var itemInfo in dropItemInfoList)
                        {
                            if (mergedDropItemInfoDict.ContainsKey(itemInfo.ItemId))
                            {
                                mergedDropItemInfoDict[itemInfo.ItemId].AddItemNum(itemInfo.ItemNum);
                            }
                            else
                            {
                                mergedDropItemInfoDict.Add(itemInfo.ItemId, itemInfo);
                            }
                        }
                    }
                    _mergedDropItemInfoList = mergedDropItemInfoDict.Values.ToList();
                }
            }
        }

        /// <summary>
        /// イリアニメ 
        /// </summary>
        public void PlayIn()
        {
            // スキップ機能の準備
            SetupSkip();

            var delay = 0f;

            // 【画面全体】
            {
                var sequence = DOTween.Sequence();

                AudioManager.Instance.PlaySe(AudioId.SFX_TRA_RESULT_POINT_WINDOW); // イリの始まる瞬間にSEを鳴らす

                // 3箇所を順番に0.033秒おきに表示
                {
                    const float DELAY_INTERVAL = 0.033f;

                    sequence.Insert(_root.gameObject, TweenAnimation.PresetType.PartsInFadeFromRight, delay);
                    delay += DELAY_INTERVAL;

                    sequence.Insert(_addEp.gameObject, TweenAnimation.PresetType.PartsInFadeFromRight, delay);
                    delay += DELAY_INTERVAL;

                    sequence.Insert(_totalEpHeader.gameObject, TweenAnimation.PresetType.PartsInFadeFromRight, delay);
                }

                _skipableSeqList.Add(sequence); // スキップ対象アニメとする

                // 少し待機
                delay += _inWaitAllIn;
            }

            // 【獲得イベントPt】
            {
                // 「上限の数値」を消しておく
                _jobsEpValue.alpha = 0f;

                // 獲得イベントPtの数値にイリを再生し、終わったらカウントアップさせる
                _addEpValueText.text = TextUtil.Format("+0{0}", TextId.StoryEvent0002.Text()); // 初期値は0
                _addEpValueCountUpModifier.SetValue(0, true);
                PartsSingleModeResultStoryEventEp.InCanvasGroup(_skipableSeqList, _addEpValue, delay, () =>
                {
                    _addEpValueCountUpModifier.SetValue(_addEventPt);
                    var countupPlayback = AudioManager.Instance.PlaySe(AudioId.SFX_UI_COUNT, loop: true);
                    _addEpValueCountUpModifier.Play(() =>
                    {
                        AudioManager.Instance.StopSe(countupPlayback);

                        // 「上限の数値」にイリ
                        {
                            const float DURATION = 0.133f;

                            var subSeq = DOTween.Sequence();
                            _skipableSeqList.Add(subSeq);

                            subSeq.Append(_jobsEpValue.DOFade(1f, DURATION).SetEase(Ease.InOutQuad));
                        }
                    });
                });

                // 少し待機
                delay += _inWaitAddEpCount;
            }

            // 【累計イベントPt】
            {
                // 累計イベントPtのアイコンとタイトル
                {
                    PartsSingleModeResultStoryEventEp.FadeInCanvasGroupFromRight(_skipableSeqList, _totalEpTitle, _totalEpTitleDefaultPos, delay);
                    delay += 0.033f;
                }

                // 累計イベントPt数テキストの初期化
                {
                    _totalEpValueText.LoadFont();
                    _totalEpValueText.text = StoryEventUtil.GetEventPtText(_totalEventPtAfter); // 桁数を見るため、まずは最終値を入れる

                    // 桁数が上がると表示がパカるので事前にスケールを調整する
                    _totalEpValueContentSizeFitter.SetLayoutHorizontal();
                    _totalEpValueContentSizeFitter.enabled = false;

                    // 綺麗なカウントアップアニメの為にテキストは右詰設定になっているが、それによって文字が右に寄っているのでオブジェクト位置を左へ補正し中央揃えに見えるようにする
                    var width = (_totalEpValueText.transform as RectTransform).sizeDelta.x;
                    if (width > 0)
                    {
                        var adjustPosX = -(width * _totalEpValueTextPosXAdjustRate);
                        var newPos = new Vector2(adjustPosX, UIUtil.GetAnchoredPosition(_totalEpValueText.transform).y);
                        UIUtil.SetAnchoredPosition(_totalEpValueText.transform, newPos);
                        _totalEpValueDefaultPos = newPos;
                    }
                }

                // イリアニメ
                {
                    _totalEpValueCountUpModifier.SetValue(_totalEventPtBefore, true); // イリの時点では変化前の値
                    PartsSingleModeResultStoryEventEp.FadeInCanvasGroupFromRight(_skipableSeqList, _totalEpValue, _totalEpValueDefaultPos, delay,
                    onComplete:() =>
                    {
                        // イリ後、変化後の値をセットしてカウントアップさせる
                        var seqTotalEpValueCountDelay = DOTween.Sequence();
                        _skipableSeqList.Add(seqTotalEpValueCountDelay); // スキップ対象アニメとする
                        seqTotalEpValueCountDelay.AppendInterval(0.1f);
                        seqTotalEpValueCountDelay.AppendCallback(() =>
                        {
                            _totalEpValueCountUpModifier.SetValue(_totalEventPtAfter);
                            var countupPlayback = AudioManager.Instance.PlaySe(AudioId.SFX_UI_COUNT, loop: true);
                            _totalEpValueCountUpModifier.Play(() =>
                            {
                                AudioManager.Instance.StopSe(countupPlayback);
                            });
                        });
                    });
                }

                // 少し待機
                delay += _inWaitTotalEpCount;
            }

            const float PANEL_DELAY_INTERVAL = 0.033f;
            _panelAnimeCompleteCount = 0;

            // 【イベントPt報酬パネル】
            {
                _partsRewardItem.PlayAnimation(delay, _skipableSeqList, OnCompletePanelAnime);
            }

            delay += PANEL_DELAY_INTERVAL;

            // 【ストーリーパネル】
            {
                _partsStoryPanel.PlayAnimation(delay, _skipableSeqList, OnCompletePanelAnime);
            }
        }

        /// <summary>
        /// 「イベントPt報酬パネルのアニメ終了時」と「ストーリーパネルのアニメ終了時」の共通処理
        /// </summary>
        private void OnCompletePanelAnime()
        {
            _panelAnimeCompleteCount++;
            if (_panelAnimeCompleteCount < 2)
                return; // どちらかが終わっていない場合は何もしない

            // どちらも終わった

            // アイテムパネルの「報酬一覧」ボタンのイリ
            {
                var seq = DOTween.Sequence();
                _skipableSeqList.Add(seq); // スキップ対象アニメとする
                seq.AppendCallback(() =>
                {
                    _partsRewardItem.PlayInRewardItemListButton(_skipableSeqList);
                });
                seq.AppendInterval(_inWaitNextItemRewadButtonIn);

                // 全てのイリアニメが終わった
                seq.AppendCallback(() =>
                {
                    // スキップを無効化
                    _skipButton.gameObject.SetActiveWithCheck(false);

                    // 各種ダイアログを表示する
                    PlayDialogSequence();
                });
            }
        }

        #region スキップ機能

        /// <summary>
        /// スキップ機能の準備
        /// </summary>
        private void SetupSkip()
        {
            _skipableSeqList.Clear();
            _skipButton.gameObject.SetActiveWithCheck(true);
            _skipButton.SetOnClick(OnClickSkipButton);

            // イリアニメするCanvasGroupの内、移動するものは初期位置を保持しておく
            _inAnimeDefaultPositionList.Clear();
            foreach (var inAnimationMoveObject in _inAnimationMoveObjects)
            {
                _inAnimeDefaultPositionList.Add(inAnimationMoveObject.transform.localPosition);
            }
        }

        /// <summary>
        /// スキップボタン押下時の処理
        /// </summary>
        private void OnClickSkipButton()
        {
            // 連打防止
            _skipButton.gameObject.SetActiveWithCheck(false);

            // スキップ対象のアニメが再生中だったなら停める
            foreach (var skipableSeq in _skipableSeqList)
            {
                if (skipableSeq == null)
                    continue;
                if (!skipableSeq.IsPlaying())
                    continue;
                skipableSeq.Kill();
            }

            // アニメ停止後の後処理
            // ・アクティブ/非アクティブを適切なものにする
            // ・アニメで透明度が変化するオブジェクトは最終的な透明度にする
            // ・アニメで位置が変化するオブジェクトはデフォルトの位置に戻す
            {
                // イリアニメするCanvasGroupの内、移動するものをデフォルト位置に戻す
                for (int i = 0; i < _inAnimeDefaultPositionList.Count; i++)
                {
                    Vector3 defaultPos = _inAnimeDefaultPositionList[i];
                    _inAnimationMoveObjects[i].transform.localPosition = defaultPos;
                }

                _root.alpha = 1f;

                // 獲得イベントPtの値
                {
                    _addEp.alpha = 1f;

                    PartsSingleModeResultStoryEventEp.ShowInstant(_addEpValue.gameObject);
                    _addEpValueCountUpModifier.ForceComplete();
                    _addEpValueCountUpModifier.SetValue(_addEventPt, true);
                }
                // 獲得イベントPtの「上限の数値」
                {
                    PartsSingleModeResultStoryEventEp.ShowInstant(_jobsEpValue.gameObject);
                }

                // 累計イベントPtのタイトル
                {
                    _totalEpHeader.alpha = 1f;

                    PartsSingleModeResultStoryEventEp.ShowInstant(_totalEpTitle.gameObject);
                    UIUtil.SetAnchoredPosition(_totalEpTitle.transform, _totalEpTitleDefaultPos);
                }
                // 累計イベントPtの値
                {
                    PartsSingleModeResultStoryEventEp.ShowInstant(_totalEpValue.gameObject);
                    UIUtil.SetAnchoredPosition(_totalEpValue.transform, _totalEpValueDefaultPos);
                    _totalEpValueCountUpModifier.ForceComplete();
                    _totalEpValueCountUpModifier.SetValue(_totalEventPtAfter, true);
                }
                // イベントPt報酬パネル
                {
                    _partsRewardItem.gameObject.SetActiveWithCheck(true);
                    _partsRewardItem.OnSkipAnimEnd();
                }
                // ストーリーパネル
                {
                    _partsStoryPanel.gameObject.SetActiveWithCheck(true);
                    _partsStoryPanel.OnSkipAnimEnd();
                }
            }

            // 各種ダイアログを表示する
            PlayDialogSequence();
        }

        #endregion

        /// <summary>
        /// 各種ダイアログの表示シーケンス
        /// </summary>
        void PlayDialogSequence()
        {
            var seq = DOTween.Sequence();
            seq.AppendCallback(() =>
            {
                // 必要なら、イベントPt報酬の「報酬受取」ダイアログを開く
                _partsRewardItem.OpenDialogRewardReceiveIfNeed(() =>
                {
                    // 必要なら、到達報酬制覇ダイアログを開く
                    OpenDialogStoryEventCompleteEventPtIfNeed(() =>
                    {
                        // 「次へ」ボタンのイリアニメを再生
                        PlayInNextButton();
                    });
                });
            });
        }

        /// <summary>
        /// 必要なら、到達報酬制覇ダイアログを開く
        /// </summary>
        private void OpenDialogStoryEventCompleteEventPtIfNeed(System.Action onFinish)
        {
            // サーバーからのフラグが立たない限り開かない
            if (!_isOpenDialogCompleteStoryEventPt)
            {
                onFinish?.Invoke();
                return;
            }

            // 開く
            DialogSingleModeResultCompleteEventReward.Open(DialogSingleModeResultCompleteEventReward.EventType.StoryEvent, _storyEventId, onFinish);
        }

        #region 画面下部のボタン

        /// <summary>「次へ」ボタンのイリアニメを再生</summary>
        private void PlayInNextButton()
        {
            var sequence = DOTween.Sequence();

            UIManager.Instance.LockGameCanvas(); // アニメ中は入力禁止

            _footerNextButton.SetActiveWithCheck(true);
            sequence.Insert(_footerNextButton.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade, 0f);
            sequence.AppendCallback(() =>
            {
                sequence.Complete();
                sequence = null;

                UIManager.Instance.UnlockGameCanvas(); // 入力禁止を解除

                _footerNextButton.SetOnClick(OnClickNextButton);
            });
        }

        /// <summary>「次へ」ボタンのハケを再生</summary>
        private void PlayOutNextButton()
        {
            var sequence = DOTween.Sequence();

            UIManager.Instance.LockGameCanvas(); // アニメ中は入力禁止

            _footerNextButton.SetActiveWithCheck(true);
            sequence.Insert(_footerNextButton.gameObject, TweenAnimation.PresetType.PartsOutFade, 0f);
            sequence.AppendCallback(() =>
            {
                sequence.Complete();
                sequence = null;

                UIManager.Instance.UnlockGameCanvas(); // 入力禁止を解除
            });
        }

        /// <summary>「次へ」ボタン押下時処理</summary>
        private void OnClickNextButton()
        {
            // 「次へ」ボタンのハケアニメを再生
            PlayOutNextButton();

            this.StartCoroutine(NextButtonCoroutine());
        }

        /// <summary>
        /// 「次へ」ボタン押下時コルーチン
        /// </summary>
        private IEnumerator NextButtonCoroutine()
        {
            // 必要ならルーレットコイン獲得演出を表示
            {
                bool isFinishDialog = false;

                OpenDialogStoryEventItemDropIfNeed(() =>
                {
                    isFinishDialog = true;
                });

                yield return new WaitUntil(() => isFinishDialog);
            }

            // 必要ならイベントストーリー解放ダイアログを開く
            {
                bool isFinishDialog = false;
                bool isChangeView = false;

                StoryEventUtil.OpenNewStoryDialogIfNeed(
                    // 普通に閉じた場合
                    onFinish: () =>
                    {
                        isFinishDialog = true;
                    },
                    // ストーリーボタンをタップして画面遷移した場合
                    onGotoEpisodeDialog: () =>
                    {
                        isFinishDialog = true;
                        isChangeView = true;
                    });

                yield return new WaitUntil(() => isFinishDialog);

                if (isChangeView)
                {
                    yield break;
                }
            }

            UIManager.Instance.LockGameCanvas();
            yield return new WaitForSeconds(_inWaitNextButton); // 少し待つ
            UIManager.Instance.UnlockGameCanvas();

            // 興行完了画面の終わり際の各種ダイアログ連続表示へ
            _nearEndContinuousDialogsAction?.Invoke();
        }


        /// <summary>必要ならルーレットコイン獲得演出を表示する</summary>
        private bool OpenDialogStoryEventItemDropIfNeed(System.Action onDropItem)
        {
            // ルーレットコインを獲得していないなら表示しない
            if (!_haveDropItem)
            {
                return false;
            }

            var delaySequence = DOTween.Sequence();
            delaySequence.AppendInterval(_inWaitItemDrop); // 少し待つ
            delaySequence.AppendCallback(() =>
            {
                // ルーレットコイン獲得演出開始
                _dialogStoryEventItemDrop = DialogStoryEventItemDrop.Open(_mergedDropItemInfoList, null, false, onDropItem,
                                                                          lowerAdditionalContentIn: PlayRouletteCoinLimitUiIn,
                                                                          lowerAdditionalContentDuration: 0.2f);
            });

            return true;
        }

        /// <summary>ルーレットコインの獲得上限UIのイリアニメを再生する</summary>
        private void PlayRouletteCoinLimitUiIn()
        {
            if (_dialogStoryEventItemDrop == null)
                return;

            // 獲得上限UIを生成
            var rouletteCoinLimitUI = Object.Instantiate(_rouletteCoinLimitUiPrefab, _dialogStoryEventItemDrop.LowerAdditionalContentRoot.transform);
            rouletteCoinLimitUI.transform.localScale = Vector3.one;
            rouletteCoinLimitUI.transform.localPosition = Vector3.zero;

            // セットアップ
            int coinAfter = _workJobsData.ResultLimitInfo.after_jobs_story_event_roulette_coin;
            int coinLimit = _workJobsData.ResultLimitInfo.jobs_story_event_roulette_coin_limit;
            rouletteCoinLimitUI.Setup(coinAfter, coinLimit);

            // イリ再生
            rouletteCoinLimitUI.PlayIn();
        }

#endregion
    }
}