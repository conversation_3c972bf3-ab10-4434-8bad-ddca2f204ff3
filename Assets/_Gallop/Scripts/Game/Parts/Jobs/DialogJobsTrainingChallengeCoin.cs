using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using Gallop.AnimSequenceHelper;

namespace Gallop
{
    /// <summary>
    /// 全国興行：「トライアルコイン獲得」ダイアログ
    /// </summary>
    public class DialogJobsTrainingChallengeCoin : DialogInnerBase
    {
        [Header("技能試験ロゴ")]

        [SerializeField] private RawImageCommon _eventLogo;

        [Header("コインアイコン")]

        [SerializeField] private ItemIcon _coinItemIcon;

        [Header("999, 999(+99, 999)のような上の数字")]

        // "99,999"のような茶色のテキスト
        [SerializeField] private TextCommon _upperCoinTotalText = null;
        [SerializeField] private CountupModifier _upperCoinTotalCountUpModifier = null;

        // "+9,999"のようなオレンジのテキスト
        [SerializeField] private TextCommon _upperCoinAddText = null;

        [SerializeField] private HorizontalLayoutGroup _upperCoinHorizontalLayout = null;

        [Header("残り 999,999 / 999,999ptのような下の数字")]

        [SerializeField] private TextCommon _lowerCoinText;
        [SerializeField] private CanvasGroup _lowerCoinCanvasGroup = null;


        private int _upperCoinTotalBefore = 0;
        private int _upperCoinTotalAfter = 0;
        private int _upperCoinAdd = 0;

        private int _lowerCoinBefore = 0;
        private int _lowerCoinAfter = 0;
        private int _lowerCoinLimit = 0;
        private int _lowerCoinRemain = 0;

        private Cute.Cri.AudioPlayback _countUpPlayback; // カウントアップSE再生用

        private WorkJobsData _workJobsData => WorkDataManager.Instance.JobsData;
        private TrainingChallengeResult _workResultTrainingChallengeResult => _workJobsData.ResultTrainingChallengeResult;
        private JobsResultLimitInfo _workJobsResultLimitInfo => _workJobsData.ResultLimitInfo;



        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Jobs408050.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            data.OpenSe = AudioId.SFX_UI_ITEM_GET; // 開く時のSEを獲得SEに変更
            return data;
        }


        /// <summary>
        /// アセットのDLを行う
        /// </summary>
        public static IEnumerator DownloadAssets()
        {
            var register = DownloadManager.GetNewRegister();

            register.RegisterPathWithoutInfo(ResourcePath.TRAINING_CHALLENGE_LOGO);

            bool isComplete = false;
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() => isComplete = true);

            yield return new WaitUntil(() => isComplete);
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(System.Action onDestroy)
        {
            var component = LoadAndInstantiatePrefab<DialogJobsTrainingChallengeCoin>(ResourcePath.DIALOG_JOBS_TRAINING_CHALLENGE_COIN_PATH);
            var data = component.CreateDialogData();
            data.DestroyCallBack = () => { onDestroy?.Invoke(); };
            DialogManager.PushDialog(data);
            component.Setup();
            component.PlayIn();
        }

        public void Setup()
        {
            _upperCoinTotalBefore = _workJobsData.ResultJobInfoList[0].TrainingChallengeCoinBefore;
            _upperCoinTotalAfter = WorkDataManager.Instance.TrainingChallengeData.Coin;
            _upperCoinAdd = _workResultTrainingChallengeResult.score_summary.add_event_coin;

            _lowerCoinBefore = _workJobsResultLimitInfo.before_jobs_training_challenge_coin;
            _lowerCoinAfter = _workJobsResultLimitInfo.after_jobs_training_challenge_coin;
            _lowerCoinLimit = _workJobsResultLimitInfo.jobs_training_challenge_coin_limit;
            _lowerCoinRemain = Mathf.Max(_lowerCoinLimit - _lowerCoinAfter, 0);

            // ロゴの設定
            _eventLogo.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.TRAINING_CHALLENGE_LOGO, DialogHash);

            // トライアルコインアイコン
            _coinItemIcon.SetData(GameDefine.ItemCategory.TRAINING_CHALLENGE_COIN, GameDefine.TRAINING_CHALLENGE_EVENT_COIN_ID, number: 0, numDisp: false, isInfoPop: true);

            // "999,999(+99,999)"のような上の数字
            SetupUpperCoin();

            // "残り 999,999 / 999,999pt"のような下の数字
            SetupLowerCoin();
        }

        /// <summary>
        /// "999,999(+99,999)"のような上の数字のセットアップ
        /// </summary>
        private void SetupUpperCoin()
        {
            // "999,999,999人"のような茶色のテキスト
            _upperCoinTotalText.text = _upperCoinTotalBefore.ToString(TextUtil.CommaSeparatedFormat);
            _upperCoinTotalCountUpModifier.SetValue(_upperCoinTotalBefore, true);

            // "+9,999"のようなオレンジのテキスト
            _upperCoinAddText.text = TextId.Outgame0379.Format(_upperCoinAdd.ToString(TextUtil.CommaSeparatedFormat));

            _upperCoinHorizontalLayout.SetLayoutHorizontal();
        }

        /// <summary>
        /// "残り 999,999 / 999,999pt"のような下の数字のセットアップ
        /// </summary>
        private void SetupLowerCoin()
        {
            if (_lowerCoinRemain == 0) // 残り0になった時だけなら分子の色を変える
            {
                _lowerCoinText.text = TextId.Jobs408056.Format(0.ToString(), _lowerCoinLimit.ToCommaSeparatedString()); // 分子がオレンジ
            }
            else
            {
                _lowerCoinText.text = TextId.Jobs408055.Format(_lowerCoinRemain.ToCommaSeparatedString(), _lowerCoinLimit.ToCommaSeparatedString()); // 分子が茶色
            }
        }

        /// <summary>イリアニメを再生</summary>
        public void PlayIn()
        {
            // イリアニメ終了時フローへ
            this.StartCoroutine(InAnimationCoroutine());
        }

        /// <summary>
        /// イリアニメを再生
        /// </summary>
        private IEnumerator InAnimationCoroutine()
        {
            // 入力を禁止
            UIManager.Instance.LockGameCanvas();

            // ダイアログのボタンをグレーアウト
            var dialog = GetDialog() as DialogCommon;
            dialog.GetButtonObj(DialogCommon.ButtonIndex.Center).interactable = false;

            // 一部のオブジェクトは開始時点では透明にしておく
            _lowerCoinCanvasGroup.alpha = 0f;

            // 【999, 999(+99, 999)のような上の数字の茶色のテキストをカウントアップ前の数値にする
            _upperCoinTotalText.text = _upperCoinTotalBefore.ToString(TextUtil.CommaSeparatedFormat);
            _upperCoinTotalCountUpModifier.SetValue(_upperCoinTotalBefore, true);

            // ダイアログのイリを待つ
            yield return new WaitForSeconds(0.234f);

            // 【999, 999(+99, 999)のような上の数字のカウントアップ】
            {
                bool isCompleteCountUp = false;

                var sequence = DOTween.Sequence();
                sequence.AppendCallback(() =>
                {
                    // カウントアップさせる
                    _upperCoinTotalCountUpModifier.SetValue(_upperCoinTotalAfter);
                    _countUpPlayback = AudioManager.Instance.PlaySe(AudioId.SFX_UI_COUNT, loop: true);
                    _upperCoinTotalCountUpModifier.Play(() =>
                    {
                        AudioManager.Instance.StopSe(_countUpPlayback);
                        isCompleteCountUp = true;
                    });
                });

                // カウントアップアニメの終了を待つ
                yield return new WaitUntil(() => isCompleteCountUp);
            }

            // 【残り 999,999 / 999,999ptのような下の数字のイリ】
            {
                const float DURATION = 0.133f;
                _lowerCoinCanvasGroup.DOFade(1f, DURATION).SetEase(Ease.InOutQuad);
                yield return new WaitForSeconds(DURATION);
            }

            // ダイアログのボタンをグレーアウトを解除
            dialog.GetButtonObj(DialogCommon.ButtonIndex.Center).interactable = true;

            // 入力禁止を解除
            UIManager.Instance.UnlockGameCanvas();
        }

    }
}
