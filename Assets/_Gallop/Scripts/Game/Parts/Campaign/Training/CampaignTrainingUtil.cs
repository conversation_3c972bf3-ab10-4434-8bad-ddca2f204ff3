using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.Cri;
using UnityEngine;

namespace Gallop
{
    using static SingleModeScenarioCookDefine;
    /// <summary>
    /// お食事カット用ユーティリティークラス
    /// </summary>
    public class CampaignTrainingUtil : CampaignUtilCommon
    {
        public static void RegisterDownloadTrainingAudioData(DownloadPathRegister register, int charaId)
        {
            if (charaId == GameDefine.INVALID_CHARA_ID)
            {
                return;
            }

            string cuttStartVoice = ResourcePath.GetCampaignTrainingCuttStartVoiceName(charaId);

            string cuttEndVoice = ResourcePath.GetCampaignTrainingCuttEndVoiceName(charaId);

            string acbFilePath = string.Empty;
            string awbFilePath = string.Empty;

            AudioManager.GetDownloadFilePath(AudioManager.SubFolder.Story, cuttStartVoice, out acbFilePath, out awbFilePath);

            register.RegisterPathWithoutInfo(acbFilePath);
            register.RegisterPathWithoutInfo(awbFilePath);

            AudioManager.GetDownloadFilePath(AudioManager.SubFolder.Story, cuttEndVoice, out acbFilePath, out awbFilePath);
            register.RegisterPathWithoutInfo(acbFilePath);
            register.RegisterPathWithoutInfo(awbFilePath);

            //効果音を一括ダウンロード登録
            MasterSingleModeCookDishCuttSeUtils.RegisterDownloadAllCharaTypeSe(register);

            //食べる演出の環境音ダウンロート
            AudioManager.Instance.RegisterDownloadByAudioIds(register, new List<AudioId>()
            {
                AudioId.SFX_CAMPAIGN_TRAINING_CAFETERIA_1,
                AudioId.SFX_CAMPAIGN_TRAINING_CAFETERIA_2,
            });
        }

        /// <summary>
        /// お食事演出に使うキャラ性格を取得
        /// </summary>
        /// <param name="charaId">対象キャラ</param>
        /// <param name="targetCutt">頭部分の共通カットかそれ以外の部分か</param>
        /// <param name="uniqueCuttId">再生しようとするカットId</param>
        /// <returns></returns>
        public static SingleModeCookCuttPersonalityType GetCampaignTrainingCuttCharaPersonality(int charaId, int targetCutt, int materialGroupId = -1)
        {
            var charaTypeData = MasterDataManager.Instance.masterCharaType.Get(charaId, COOK_CUTT_TARGET_SCENE, targetCutt, COOK_CUTT_TARGET_TYPE_COMMON);
            var personlaity = (SingleModeCookCuttPersonalityType)(charaTypeData?.Value ?? COOK_CUTT_CHARACTER_NORMAL_PERSONALITY);
            //料理が汎用でないカットの場合上書きがあり得る
            if (targetCutt == COOK_CUTT_TARGET_CUTT_OTHER_CUTT)
            {
                //お料理ごとに指定する性格がある場合それを利用
                var isSpicyType = IsSpicyType(materialGroupId);
                var targetType = isSpicyType ? COOK_CUTT_TARGET_CUTT_OTHER_CUTT_SPICY_TYPE : COOK_CUTT_TARGET_TYPE_COMMON;
                var overrideCharaType = MasterDataManager.Instance.masterCharaType.Get(charaId, COOK_CUTT_TARGET_SCENE, targetCutt, targetType);
                if (overrideCharaType != null && overrideCharaType.Value > 0)
                {
                    var personalityCache = (SingleModeCookCuttPersonalityType)overrideCharaType.Value;
                    personlaity = personalityCache;
                    //辛い料理の場合返す一部のタイプを直す
                    if (isSpicyType)
                    {
                        personlaity = personalityCache switch
                        {
                            SingleModeCookCuttPersonalityType.Common => SingleModeCookCuttPersonalityType.CommonLikeSpicy,
                            SingleModeCookCuttPersonalityType.Mature => SingleModeCookCuttPersonalityType.MatureLikeSpicy,
                            SingleModeCookCuttPersonalityType.Cool => SingleModeCookCuttPersonalityType.CoolLikeSpicy,
                            _ => personalityCache
                        };
                    }
                }
            }

            return personlaity;
        }

        /// <summary>
        /// お食事演出で性格差分モーションを適用する際に性格参照先を直接指定してモーションを再生させる
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="targetScene"></param>
        /// <param name="targetCut"></param>
        /// <param name="targetType"></param>
        /// <returns></returns>
        public static SingleModeCookCuttPersonalityType GetCampaignTrainingCuttCharaPersonality(int charaId, int targetScene, int targetCut, int targetType)
        {
            var charaTypeData = MasterDataManager.Instance.masterCharaType.Get(charaId, targetScene, targetCut, targetType);

            if (charaTypeData == null)
            {
                return (SingleModeCookCuttPersonalityType)COOK_CUTT_CHARACTER_NORMAL_PERSONALITY;
            }
            else
            {
                return (SingleModeCookCuttPersonalityType)charaTypeData.Value;
            }
        }

        /// <summary>
        /// 料理が辛いものかどうか
        /// </summary>
        /// <param name="materialGroupId"></param>
        /// <returns></returns>
        public static bool IsSpicyType(int materialGroupId)
        {
            return materialGroupId == COOK_CUTT_SPICY_MATERIAL_GROUP_ID_401 || materialGroupId == COOK_CUTT_SPICY_MATERIAL_GROUP_ID_402;
        }

        /// <summary>
        /// 演出に必要なすべてのモデル作成
        /// </summary>
        /// <param name="charaIdArray"></param>
        /// <param name="isAsync"></param>
        /// <param name="resultModelList"></param>
        /// <returns></returns>
        public static IEnumerator CreateCuttModelList(int[] charaIdArray, bool isAsync, List<CutInModelController> resultModelList, Action onCreateComplete = null, int customDressId = GameDefine.INVALID_DRESS_ID)
        {
            //作成結果を受けるリストがnullの場合作成しない
            if (resultModelList == null)
            {
                Debug.LogWarning("モデルを格納するためのリストがnullなため、作成がスキップされました！");
                onCreateComplete?.Invoke();
                yield break;
            }

            if (charaIdArray.IsNullOrEmpty())
            {
                Debug.LogWarning("作成しようとするキャラIdが指定されていないので、作成がスキップされました！");
                onCreateComplete?.Invoke();
                yield break;
            }

            var dressId = (int)ModelLoader.DressID.JerseySummer;

            if (customDressId == GameDefine.INVALID_DRESS_ID)
            {
                //通常再生はここに入る想定
                if (WorkDataManager.HasInstance() && WorkDataManager.Instance.SingleMode.IsPlaying)
                {
                    //139185 お料理編の場合季節によって食べる演出の衣装を変更
                    if (WorkDataManager.Instance.SingleMode.GetScenarioId() == SingleModeDefine.ScenarioId.Cook)
                    {
                        dressId = WorkDataManager.Instance.SingleMode.GetSeasonDressId(dressId);
                    }
                }
            }
            else
            {
                dressId = customDressId;
            }

            foreach (var charaId in charaIdArray)
            {
                //モデル作成
                var buildInfo = new CharacterBuildInfo(charaId, dressId, ModelLoader.ControllerType.CutIn);
                if (isAsync)
                {
                    bool isLoadingModel = true;
                    ModelLoader.CreateModelAsync(buildInfo, (model) =>
                    {
                        resultModelList.Add(model.GetComponent<CutInModelController>());
                        isLoadingModel = false;
                    });

                    //処理負荷を抑えるため非同期でモデルを作成する、終了まで待つ
                    while (isLoadingModel)
                    {
                        yield return null;
                    }
                }
                else
                {
                    //長いフリーズと端末の熱抑えるためにも現状非同期を利用している状態ですが、
                    //一応同期ロードも残しておきます
                    //同期ロード
                    var model = ModelLoader.CreateModel(buildInfo);
                    resultModelList.Add(model.GetComponent<CutInModelController>());
                }
            }

            onCreateComplete?.Invoke();
        }

        /// <summary>
        /// お食事カットを再生
        /// 注意:ここでモデル作成はしないので、呼ぶ前に必ずモデル作成を済んでから実行してください！
        /// </summary>
        /// <param name="onInitFinish">一連の演出のカットの初期化完了のコールバック</param>
        /// <param name="onStartFirstCutt">一連の演出開始時のコールバック</param>
        /// <param name="onEndLastCutt">一連の演出終了時の時のコールバック、これが呼ばれた時カットは最終フレームで止まっている状態となる</param>
        /// <param name="onWaitStart">初期化と再生を別々のタイミングにしたいための演出開始を待たせるコールバック、再生開始可能の場合戻り値をTrueにするべき</param>
        /// <param name="onSkipCutt">外部で演出をスキップしたい時戻り値をTrueにする</param>
        /// <param name="shownResultUI">結果表示UIが表示終了した際に戻り値をTrueにしてカット演出も終了させる（リソースの開放を行う）</param>
        /// <param name="updateCuttSpeed">再生速度設定が更新された時利用</param>
        /// <param name="getCharaModel">外部に持つモデルリストをカットで取得するためのコールバック</param>
        /// <param name="onStartCutt">一連の演出のカットごとにスタートの時呼ばれる</param>
        /// <param name="onEndCutt">一連の演出のカットごとに終了時呼ばれる</param>
        /// <param name="onPlayFailed">カット再生で何等かの問題で失敗した時で呼ばれる</param>
        /// <returns></returns>
        public static IEnumerator CoroutinePlayCampaignTrainingCutIn(int commandGroupId, int materialGroupId, int singleModeCharaId, int[] memberCharaIdArray, bool isSummerCampTrun, bool isSkipInitializeAlter,
            Action<CampaignTrainingCutInHelper[]> onInitFinish,
            Action onStartFirstCutt, Action onEndLastCutt, Func<bool> onWaitStart,
            Func<bool> onSkipCutt, Func<bool> shownResultUI, Action<CampaignTrainingCutInHelper> updateCuttSpeed,
            Func<int, CampaignTrainingCutInHelper, CutInModelController> getCharaModel, Action<CampaignTrainingCutInHelper> onStartCutt = null,
            Action<CampaignTrainingCutInHelper> onEndCutt = null,
            Action onPlayFailed = null)
        {
            //汎用カットも含めてデータを取得
            var cuttMasterData = MasterSingleModeCookDishCutt.GetTargetDishCuttData(commandGroupId, materialGroupId);

            if (cuttMasterData.IsNullOrEmpty())
            {
                Debug.LogError($"command_group_id == {commandGroupId}; material_group_id == {materialGroupId}; CSVに該当するカットデータが設定されていません!");
                onPlayFailed?.Invoke();
                yield break;
            }

            //料理ごとに性格タイプの設定はしない
            //育成キャラとメンバーキャラのIDをまとめて持つ配列が欲しいので
            //配列生成している
            var charaIdArray = memberCharaIdArray;

            //最後のカットから育成キャラの性格を決める(激辛が最後のカットで分岐されるのが原因)
            var singleModelCharaPersonality = GetCampaignTrainingCuttCharaPersonality(singleModeCharaId, SingleModeScenarioCookDefine.COOK_CUTT_TARGET_CUTT_OTHER_CUTT, cuttMasterData.Last().MaterialGroupId);

            //カット数分のカットヘルパーを用意
            var cutInHelperArray = new CampaignTrainingCutInHelper[cuttMasterData.Count];

            //ヘルパークラスのセットアップ
            for (int i = 0; i < cutInHelperArray.Length; i++)
            {
                var helper = new CampaignTrainingCutInHelper();
                cutInHelperArray[i] = helper;
                var masterData = cuttMasterData[i];
                var fullCutId = masterData.CuttId;
                var cutIdSplitArray = fullCutId.Split('_');
                if (cutIdSplitArray.Length < 3)
                {
                    Debug.LogError($"マスターに設定されているCutId: {fullCutId}は不正！データの形式は(Xは数字)[XXX_XX_XXXX]となります！");
                    onPlayFailed?.Invoke();
                    yield break;
                }
                var cutId = cutIdSplitArray[0];
                var subId = cutIdSplitArray[1];
                var optionId = cutIdSplitArray[2];
                var context = new CampaignCutInHelper.Context(int.Parse(cutId), int.Parse(subId), int.Parse(optionId), charaIdArray);
                //実行時表示するモデルを取得するコールバックをセット
                helper.GetTargetCharaModel = getCharaModel;
                var isFirstCutt = i == 0;
                //一気に再生すべきタイムラインインスタンスを作成、順番に再生させる
                var path = masterData.GetDishCutAssetPath();
                helper.PreSetUpPlayParameter(ref context, path, isFirstCutt, masterData, isSummerCampTrun);
                helper.Init();
            }

            //初期化出来たらカットインヘルパークラスインスタンスを渡す
            onInitFinish?.Invoke(cutInHelperArray);

            //再生開始までの待ち、作るシーンと同じタイミングで初期化を済ませて、
            //特定のタイミングで再生スタート時で利用
            while (onWaitStart?.Invoke() ?? false)
            {
                yield return null;
            }

            bool isSkipCutt = false;

            _environmentSePlayback = default;

            //145317 演出の効果音の再生経過時間を記録
            var envSePlaybackPrevTime = 0f;

            //カットの初期化で背景カメラのDepthを弄るため、再生後元に戻すようにここでキープしておく
            UIManager.Instance.BackupAndResetBgCameraDepth();

            //再生速度によりSe再生を止められるかをチェック
            void CheckStopSE(float speed)
            {
                //カットSE再生中で倍速になったらすぐにSEの再生を止める
                if (_environmentSePlayback.Status == CriWare.CriAtomExPlayback.Status.Playing
                    && speed > SingleModeDefine.CUT_PLAY_SPEED_1X)
                {
                    AudioManager.Instance.StopSe(_environmentSePlayback);
                }
            }

            //140797 139693 139321対応でエフェクトはカット再生止めた時一緒にとめて、スキップ時Propや揺れ物の見た目を整える
            void OnEndPlayCutt(CampaignTrainingCutInHelper cuttHelper, bool isSkip)
            {
                if (isSkip)
                {
                    cuttHelper.SkipRunTime();
                    cuttHelper.Pause(true);
                }

                //140797 139693 139321スキップなしの場合最後に止まった場面でエフェクトを止めるためにUpdateをかける
                cuttHelper.AlterUpdate();
            }

            for (int i = 0, len = cutInHelperArray.Length; i < len; i++)
            {
                var currentCutIn = cutInHelperArray[i];

                //再生すべき一連のカットにどれかが初期化失敗してしまった場合、再生中止
                //再生直前にタイムラインを初期化させる
                if (currentCutIn.InstantiateTimeline(currentCutIn.CuttPath, null, SingleModeScenarioCookDefine.COOK_CUTT_DEFAULT_MOTION_TYPE) == false)
                {
                    Debug.LogError($"path:[{currentCutIn.CuttPath}];のカット初期化に失敗しました！再生が中止されました！");
                    onPlayFailed?.Invoke();

                    //再生前に保持していた背景カメラのDepthを戻す
                    UIManager.Instance.RestoreBgCameraDepth();
                    yield break;
                }

                //カットが再生したら外部に通知
                currentCutIn.OnStartAction = (timelineController) =>
                {
                    if (i == 0)
                    {
                        //演出全体がスタートした時の処理
                        onStartFirstCutt?.Invoke();

                        //seは倍速以外の時のみ再生
                        if (timelineController.Speed == SingleModeDefine.CUT_PLAY_SPEED_1X)
                        {
                            //最初のカットのスタートに合わせてSeを流す
                            _environmentSePlayback = MasterSingleModeCookDishCuttSeUtils.PlaySe((int)singleModelCharaPersonality, commandGroupId);
                            //145317 最初に効果音再生する際に経過時間を念のためにリセット
                            envSePlaybackPrevTime = 0f;
                        }

                        //環境音を再生する
                        _cafeteriaSePlayback = AudioManager.Instance.PlaySe(isSummerCampTrun ? AudioId.SFX_CAMPAIGN_TRAINING_CAFETERIA_2 : AudioId.SFX_CAMPAIGN_TRAINING_CAFETERIA_1);
                    }
                    //カットごとに再生開始時の処理
                    onStartCutt?.Invoke(currentCutIn);
                };

                bool isEnd = false;
                currentCutIn.OnEndAction = (timelineController) =>
                {
                    onEndCutt?.Invoke(currentCutIn);
                    isEnd = true;
                };

                //最初の1F目でもSEを止めるチェックを行う
                updateCuttSpeed?.Invoke(currentCutIn);

                currentCutIn.Play(currentCutIn.CuttPath, null, currentCutIn.CuttContext);
                if (isSkipCutt == false)
                {
                    //最初のカットにもキャラ尻尾やライティングの反映遅延と
                    //カット→カットのつなぎ目に遅延問題両方をなくすために0Fの見た目にする
                    currentCutIn.TimelineController.SkipRuntime(0, true);
                    //UpdateとLateUpdateでProp関連の更新を即座反映させるが、
                    //時間を進めたくないためポーズをかけて0Fに固定しておく
                    currentCutIn.Pause(true);

                    currentCutIn.AlterUpdate();
                    //143694 フォトスタジオでは4:3などの画面比率の端末でカメラの位置補正が描画されてしまうため、後更新を通常のフレームに任せる
                    if (!isSkipInitializeAlter)
                    {
                        currentCutIn.AlterLateUpdate();
                    }

                    yield return UIManager.WaitForEndOfFrame;
                    //見た目反映ができたため、ポーズを解除
                    currentCutIn.Pause(false);
                }

                //現在のカット再生の終了まで待つ
                while (isEnd == false && isSkipCutt == false)
                {
                    var timelineController = currentCutIn.TimelineController;
                    //カットSE再生中で倍速になったらすぐにSEの再生を止める
                    CheckStopSE(timelineController == null ? 0 : timelineController.Speed);

                    updateCuttSpeed?.Invoke(currentCutIn);

                    //スキップ状態を監視
                    if (isSkipCutt == false && onSkipCutt != null)
                    {
                        isSkipCutt = onSkipCutt.Invoke();
                    }

                    //145317 カットの再生具合と効果音とずれが発生する場合、そのずれた分をカットに適用
                    if (_environmentSePlayback.Status == CriWare.CriAtomExPlayback.Status.Playing && currentCutIn.IsPause() == false && isSkipCutt == false)
                    {
                        //SEの再生時間を取得
                        var sePlaybackTime = _environmentSePlayback.Time;
                        if (sePlaybackTime >= 0f && currentCutIn.TimelineController.CurrentFrame > 0)
                        {
                            const float MSecToSec = 1000.0f;
                            var deltaTime = (sePlaybackTime - envSePlaybackPrevTime) / MSecToSec;
                            envSePlaybackPrevTime = sePlaybackTime;
                            //deltaTimeに差分が発生する時のみカット側の時間更新をかける
                            var diffDeltaTime = deltaTime - currentCutIn.TimelineController.DeltaTime;
                            if (diffDeltaTime != 0f)
                            {
                                //147518 SEの再生時間の差分を適用してもカット自身の終端時間を超えさせないようにする
                                var newCurrentTime = currentCutIn.TimelineController.CurrentTime + diffDeltaTime;

                                //148497　カットの更新は「CutInUpdateHelperController」で行っていて、
                                //ここでの時間更新によりカットの終端時間に直接に飛ばしてしまう可能性があり、
                                //「CutInUpdateHelperController」の更新とこちらの更新は並行実行しているので、
                                //実行順の前後で「CutInUpdateHelperController」に1F分のUpdateとLateupdateを呼んでしまう。
                                //なので、最終カットの終端時間に到達させるのは元通り「CutInUpdateHelperController」に任せる
                                if (i < len - 1 && newCurrentTime < currentCutIn.TimelineController.GetTotalTime())
                                {
                                    currentCutIn.TimelineController.SetCurrentTime(newCurrentTime);
                                    currentCutIn.TimelineController.ExternalUpdate();
                                }
                            }
                        }
                    }

                    yield return null;
                }

                // 最後のカット以外での一時停止中は演出を進めない
                while (currentCutIn.IsPause() && i < len - 1)
                {
                    yield return null;
                }

                if (isSkipCutt)
                {
                    //最後のカットは再生中
                    if (i == len - 1)
                    {
                        OnEndPlayCutt(currentCutIn, true);
                    }
                    else
                    {
                        //最後のカットは再生していないならすぐに再生中のカットを止める
                        currentCutIn.SkipRunTime();
                        currentCutIn.CleanupPlaying();
                        //次の再生は最後のカットを対象にする
                        i = len - 2;
                    }
                }
                else
                {
                    if (i == len - 1)
                    {
                        OnEndPlayCutt(currentCutIn, false);
                    }
                    else
                    {
                        //通常の再生済み後クリアをかける
                        currentCutIn.CleanupPlaying();
                    }
                }
            }

            if (cutInHelperArray.IsNullOrEmpty() == false)
            {
                //SEも止める
                AudioManager.Instance.StopSe(_environmentSePlayback);
                //環境音も止める
                AudioManager.Instance.StopSe(isSummerCampTrun ? AudioId.SFX_CAMPAIGN_TRAINING_CAFETERIA_2 : AudioId.SFX_CAMPAIGN_TRAINING_CAFETERIA_1);

                //演出再生終了後
                onEndLastCutt?.Invoke();

                //#141586 お料理編にてフリーズ頻度が上がったので待機時間を追加
                yield return null;

                //すべての演出が終わったらUIが表示中
                while (shownResultUI?.Invoke() ?? false)
                {
                    yield return null;
                }

                //後始末
                foreach (var cutInHelper in cutInHelperArray)
                {
                    //ラスト以外のカットここでクリア処理をやらない想定
                    if (cutInHelper.Status != CutInHelper.CutInStatus.End)
                    {
                        cutInHelper.CleanupPlaying();
                    }
                    cutInHelper.Release();
                }

                //再生前に保持していた背景カメラのDepthを戻す
                UIManager.Instance.RestoreBgCameraDepth();
            }
        }

        /// <summary>
        /// 演出スキップの場合直接に最後のカットの最終フレームに飛ばす
        /// </summary>
        /// <returns></returns>
        public static IEnumerator CoroutinePlaySkipCampaignTrainingCutt(int commandGroupId, int materialGroupId, int singleModeCharaId, bool isSummerCampTurn,
            Action<CampaignTrainingCutInHelper> onInitFinish,
            Action onStartCutt, Action onEndCutt, Func<bool> onWaitStart,
            Func<bool> shownResultUI,
            Func<int, CampaignTrainingCutInHelper, CutInModelController> getCharaModel)
        {
            //汎用カットも含めてデータを取得
            var cuttMasterData = MasterSingleModeCookDishCutt.GetTargetDishCuttData(commandGroupId, materialGroupId);

            if (cuttMasterData.IsNullOrEmpty())
            {
                Debug.LogError($"command_group_id == {commandGroupId}; material_group_id == {materialGroupId}; CSVに該当するカットデータが設定されていません!");
                yield break;
            }

            //最後のカットは必ず育成キャラが登場
            var charaIdArray = new int[] { singleModeCharaId };

            //一番最後のカットのデータだけを取得
            var dishCuttData = cuttMasterData.OrderByDescending(x => x.Id).First();
            var cutInHelper = new CampaignTrainingCutInHelper();

            var fullCutId = dishCuttData.CuttId;
            var cutIdSplitArray = fullCutId.Split('_');
            if (cutIdSplitArray.Length < 3)
            {
                Debug.LogError($"マスターに設定されているCutId: {fullCutId}は不正！データの形式は(Xは数字)[XXX_XX_XXXX]となります！");
                yield break;
            }

            var cutId = cutIdSplitArray[0];
            var subId = cutIdSplitArray[1];
            var optionId = cutIdSplitArray[2];
            var context = new CampaignCutInHelper.Context(int.Parse(cutId), int.Parse(subId), int.Parse(optionId), charaIdArray);

            //カットの初期化で背景カメラのDepthを弄るため、再生後元に戻すようにここでキープしておく
            UIManager.Instance.BackupAndResetBgCameraDepth();

            //実行時表示するモデルを取得するコールバックをセット
            cutInHelper.GetTargetCharaModel = getCharaModel;
            var path = dishCuttData.GetDishCutAssetPath();
            cutInHelper.PreSetUpPlayParameter(ref context, path, false, dishCuttData, isSummerCampTurn);
            cutInHelper.Init();
            cutInHelper.InstantiateTimeline(cutInHelper.CuttPath, null, COOK_CUTT_DEFAULT_MOTION_TYPE);

            //演出開始前にフレーム安定させたいため、1Fの遅延を入れる
            yield return null;

            onInitFinish?.Invoke(cutInHelper);

            //通常再生と違って演出スキップ際にすぐに見た目を整えておく
            cutInHelper.Play(cutInHelper.CuttPath, null, cutInHelper.CuttContext);

            //140797 139693 139321対応で通常再生時のスキップと一緒にする
            cutInHelper.TimelineController.SkipRuntime();
            cutInHelper.Pause(true);
            cutInHelper.TimelineController.SuspendAutoUpdate();
            cutInHelper.AlterUpdate();
            cutInHelper.AlterLateUpdate();

            //再生許可されるまで待つ
            while (onWaitStart?.Invoke() ?? false)
            {
                yield return null;
            }

            onStartCutt?.Invoke();

            //違うフレームでスタートと終了のコールバックを呼ぶ
            yield return null;

            onEndCutt?.Invoke();

            while (shownResultUI?.Invoke() ?? false)
            {
                yield return null;
            }

            //140797 139693 139321演出再生後ちゃんとカットの解放を呼ぶ
            cutInHelper.CleanupPlaying();
            cutInHelper.Release();

            //再生前に保持していた背景カメラのDepthを戻す
            UIManager.Instance.RestoreBgCameraDepth();
        }

        /// <summary>
        /// 大豊食祭の理事長カットを再生
        /// </summary>
        /// <returns></returns>
        public static IEnumerator CoroutinePlayRijichouCutIn(Action onEnd, Func<bool> onWaitStart, Func<bool> onSkip, RenderTexture rt, CampaignTrainingCutInHelper cutInHelper)
        {
            //カットは理事長のみ登場
            var charaIdArray = new int[] { GameDefine.RIJICHO_CHARA_ID };

            //ヘルパークラスのセットアップ
            const string CUTT_ID_FORMAT = "{0:D3}_{1:D2}_{2:D4}";
            var context = new CampaignCutInHelper.Context(SPECIAL_EVENT_RIJICHOU_CUTT_MAIN_ID, SPECIAL_EVENT_RIJICHOU_CUTT_SUB_ID, SPECIAL_EVENT_RIJICHOU_CUTT_OPTION_ID, charaIdArray);
            var cuttId = TextUtil.Format(CUTT_ID_FORMAT, context.Id, context.SubId, context.OptionId);
            var path = ResourcePath.GetCampaignTrainingCutInPath(cuttId, COOK_CUTT_DEFAULT_MOTION_TYPE);
            cutInHelper.TargetRT = rt;

            //カットの初期化で背景カメラのDepthを弄るため、再生後元に戻すようにここでキープしておく
            UIManager.Instance.BackupAndResetBgCameraDepth();

            //初期化
            cutInHelper.PreSetUpPlayParameter(ref context, path, false, null, false);
            cutInHelper.Init();
            if (cutInHelper.InstantiateTimeline(cutInHelper.CuttPath, null, COOK_CUTT_DEFAULT_MOTION_TYPE) == false)
            {
                Debug.LogError($"path:[{cutInHelper.CuttPath}];のカット初期化に失敗しました！再生が中止されました！");
                //再生前に保持していた背景カメラのDepthを戻す
                UIManager.Instance.RestoreBgCameraDepth();
                yield break;
            }

            //カットが再生したら外部に通知
            cutInHelper.OnStartAction = (timelineController) =>
            {
                //倍速対応しない
                timelineController.SetSpeed(SingleModeDefine.CUT_PLAY_SPEED_1X);
            };

            bool isEnd = false;
            cutInHelper.OnEndAction = (timelineController) =>
            {
                onEnd?.Invoke();
                isEnd = true;

                //カットの再生終了後にエフェクトを止めたいので、終了時にポーズを呼ぶ
                //cutInHelper.AlterUpdateでポーズされたことがEffectに伝わる
                cutInHelper.Pause(true);
                cutInHelper.AlterUpdate();
            };

            //再生
            cutInHelper.Play(cutInHelper.CuttPath, null, cutInHelper.CuttContext);
            cutInHelper.TimelineController.SkipRuntime(0, true);

            //UpdateとLateUpdateでProp関連の更新を即座反映させるが、
            //時間を進めたくないためポーズをかけて0Fに固定しておく
            cutInHelper.Pause(true);
            cutInHelper.TimelineController.SuspendAutoUpdate();
            cutInHelper.TimelineController.SetActiveWithCheck(false);

            /*
             * 特殊な実装をしているので説明
             * お料理編：目標イベントについて
             * 3DTop＞＞料理中＞＞煽り演出＞＞理事長＞＞結果画面の順に遷移するものとして
             * ・「3DTop画面」側でBgCameraを操作しているが、「3DTop」中に他画面の必要なものの先行ロード、生成を始める、この際、「理事長」演出の初期化側でもBgCameraの操作をするので処理がバッティングする
             * ・「理事長」演出の準備が終わったら、「3DTop」側に影響が出ないように設定を元に戻す
             * ・「理事長」演出再生のタイミングでUIManager.Instance.SetBgCameraRenderTexture(rt);を呼び出して、必要なBgCameraの設定を再度行う
             * ・「理事長」演出再生を終了したら、その後始末は「結果画面」の終了のタイミングで行う、「理事長」演出の最後の画の上に結果画面を載せるため全てが終わるタイミングでないとBgCameraの設定を戻せないため
             */

            //理事長演出の準備が終わったら、3DTop側に影響が出ないように設定を元に戻す
            UIManager.Instance.RestoreBgCameraDepth();

            //再生可能になるまで待つ
            while (onWaitStart?.Invoke() ?? false)
            {
                yield return null;
            }

            bool isSkip = onSkip?.Invoke() ?? false; // スキップしているかどうか
            cutInHelper.TimelineController.SetActiveWithCheck(true);
            cutInHelper.Pause(false);
            cutInHelper.TimelineController.ResumeAutoUpdate();

            if (isSkip == false)
            {
                //スキップしてないときは1FあけてからSetBgCameraRenderTextureする
                //コルーチンであるこの関数内において、上のResumeAutoUpdateしたタイミングではカットの更新が行われず、次のFで更新、カメラが動き出すのでまだ描画結果を表示するべきではないから
                //初っ端からスキップしたとき（この画面に来る前にスキップボタンが押されていたら）は1F待つ必要がないのでyield return nullしない
                yield return null;
            }

            //再生が始まったタイミングで再度描画先のセット（やりたいことはBgCameraのDepthをいじりたい画直接触れないので）
            //ここで加わった変更は「結果画面」の終了処理で元に戻される
            UIManager.Instance.SetBgCameraRenderTexture(rt);


            //終了まで待つ
            while (isEnd == false)
            {
                //スキップ可能になったらそのまま抜ける
                isSkip = onSkip?.Invoke() ?? false; // スキップしているかどうか
                if (isSkip)
                {
                    cutInHelper.SkipRunTime();

                    //SkipRunTimeだけだと、イメージエフェクトまでやってくれないのでAlterUpdateで適用する
                    cutInHelper.TimelineController.AlterUpdate(cutInHelper.TimelineController.CurrentTime);
                    //揺れもののケア
                    cutInHelper.CareCyspring();

                    //スキップ時ドリブンキーなどの更新を即時反映したいため、再度更新をかける
                    var modelList = cutInHelper.TimelineController.GetCharacterModelControllerList();
                    if (modelList.IsNullOrEmpty() == false)
                    {
                        foreach (var model in modelList)
                        {
                            ModelController.FrameUpdate(model);
                        }
                    }

                    yield break;
                }

                yield return null;

            }
        }

        #region 環境音
        private static AudioPlayback _environmentSePlayback;
        private static AudioPlayback _cafeteriaSePlayback;
        /// <summary>
        /// 環境音一時停止
        /// </summary>
        public static void PauseEnvironmentSe()
        {
            AudioManager.Pause(_environmentSePlayback);
            AudioManager.Pause(_cafeteriaSePlayback);
        }

        /// <summary>
        /// 環境音一時停止
        /// </summary>
        public static void ResumeEnvironmentSe()
        {
            AudioManager.Resume(_environmentSePlayback);
            AudioManager.Resume(_cafeteriaSePlayback);
        }

        /// <summary>
        /// 環境音のクリーンアップ
        /// </summary>
        public static void CleanUpEnvironmentSe()
        {
            AudioManager.Stop(_environmentSePlayback);
            AudioManager.Stop(_cafeteriaSePlayback);
        }
        #endregion
    }
}
