using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{
    using Ease = DG.Tweening.Ease;

    /// <summary>
    /// バレンタイン特殊演出ユーティリティークラス
    /// </summary>
    public class CampaignValentineUtil : CampaignUtilCommon
    {
        /// <summary>
        /// カット演出用のジングルのダウンロード登録
        /// </summary>
        public static void RegisterDownloadValentineCutInBGM(DownloadPathRegister register)
        {
            var audioIdData = AudioDefine.GetAudioIdData(AudioId.BGM_CAMPAIGN_VALENTINE_SP_CUTT);
            AudioManager.Instance.RegisterDownloadByCueSheet(register, audioIdData._cueSheet, AudioManager.SubFolder.Bgm);
        }

        /// <summary>
        /// バレンタイン特殊カット演出再生コルーチン
        /// </summary>
        public static IEnumerator CoroutinePlayCampaignValentineCutIn(int cardId,
                                                            ValentineCutinHelper helper,
                                                            int motionType,
                                                            System.Action onComplete,
                                                            System.Action onEndFadeOut = null,
                                                            System.Action onStartFadeIn = null,
                                                            float firstDelay = 0f,
                                                            bool isNeedFadeIn = true,
                                                            PartsCampaignWalkingCutTapScreen cutTapScreen = null,
                                                            bool shouldDestroyCutTapScreen = false)
        {
            // 必要なら少し待つ
            if (firstDelay > 0f)
            {
                yield return new WaitForSeconds(firstDelay);
            }

            // 全画面白フェードアウト
            {
                yield return PlayWhiteFadeOut();

                // 1フレ待つ
                yield return null; // 端末によってはこの後のカット読み込みで処理落ちする。処理落ちした場合、白に染まり切っていない画面で固まってしまい恰好悪いので、1フレーム待って染まり切らせてから処理落ちさせる。

                // 白フェードアウトが完了時の処理
                onEndFadeOut?.Invoke();
            }

            // カット演出再生
            {
                helper.SetupCampaignSpPresentBonusData(cardId);
                var cardData = MasterDataManager.Instance.masterCardData.Get(cardId);
                //バレンタインカットはうまさんぽと違ってまだ「campaign_cutt_data」から衣装を指定する必要がないので、
                //もし開催したキャンペーンごとに同じカットに違う衣装を指定たい場合「MasterCampaignData.INVALID_CAMPAIGN_ID」にきちんと利用中のキャンペーンIdを渡してあげます
                // 演出設定
                var context = new CampaignCutInHelper.Context(helper.PresentBonusData.GetLocationData(), new int[] { cardData.CharaId }, MasterCampaignData.INVALID_CAMPAIGN_ID, cardId: cardId);

                // #98949 キャンペーンカットのモーション入れ替え対応
                helper.OnGetMotionReplaceIndex = CampaignSwapMotionHelper.GetReplaceIndex;

                // 演出のPath
                var path = ResourcePath.GetCampaignCutInPath(context.Id, context.SubId, motionType);

                // カット読み込み
                helper.Init();
                var result = helper.InstantiateTimeline(path, null, motionType);
                if (result)
                {
                    // カット演出用のジングルを再生
                    PlayValentineCutInBgm();

                    // ロードに成功したら白フェードの裏でキャラなどのセットアップを済ませる
                    helper.Play(path, null, context);
                    helper.TimelineController.InitialUpdate();
                    
                    var backupBgCameraEnable = false;
                    if (UIManager.BGCamera != null)
                    {   // 134990 廉価なMali端末でかつ特別チョコ演出のときに画面が真っ白になる。
                        // BGCameraが有効だとUICameraのCanvas描画時に画面が真っ白になる問題が発生するのでその一時回避用。
                        // レジェンドレースVsカットでも同様の問題が起こっており、同じ回避方法をとっている。
                        // 根本解決は 129311 で行う予定。
                        var bgCameraObj = UIManager.BGCamera.gameObject;
                        backupBgCameraEnable = bgCameraObj.activeInHierarchy;
                        bgCameraObj.SetActive(false);
                    }
                    
                    yield return UIManager.WaitForEndOfFrame;

                    // 撮影ボタンを表示
                    cutTapScreen?.SetActivePhotoShotButton(true);

                    // フェードイン (カットはフェードの裏で動いた状態)
                    yield return PlayWhiteFadeIn();

                    // 再生が終わるまで待機させる
                    // ボイス再生終了も待つ
                    while (helper.IsPlaying() || helper.IsWaitingVoiceEnd())
                    {
                        yield return UIManager.WaitForEndOfFrame;
                    }
                    
                    // WaitForTapScreenでUnlockGameCanvasが呼ばれるためロックをかける
                    UIManager.Instance.LockGameCanvas();

                    // カットを末尾まで再生したらタップ待ちをする
                    yield return WaitForTapScreen(cutTapScreen, shouldDestroyCutTapScreen: shouldDestroyCutTapScreen);
                    cutTapScreen?.SetActivePhotoShotButton(false);
                    if (SceneManager.Instance.IsRunChangeView)
                    {
                        yield break;
                    }

                    // タップされてからフェードアウト (カットはフェードの裏で止まった状態)
                    yield return PlayWhiteFadeOut();

                    // カットの後片付け
                    helper.CleanupPlaying();
                    
                    if (UIManager.BGCamera != null)
                    {   // カットが始まる前の状態に戻す
                        UIManager.BGCamera.gameObject.SetActive(backupBgCameraEnable);
                    }
                }
                else
                {
                    // ロード失敗したらダイアログだして処理を抜ける
                    OnCutInLoadError(helper.ErrorInfo, () =>
                    {
                        onStartFadeIn?.Invoke();
                        onComplete?.Invoke();
                    });
                    yield break;
                }
            }

            //必要ならフェードイン
            if (isNeedFadeIn)
            {
                // フェードイン開始前の処理
                onStartFadeIn?.Invoke();

                // 全画面白フェードイン
                yield return PlayWhiteFadeIn();
            }
            
            UIManager.Instance.UnlockGameCanvas();

            // 完了時処理
            onComplete?.Invoke();
        }

        /// <summary>
        /// カット演出用のジングルを再生
        /// </summary>
        private static void PlayValentineCutInBgm()
        {
            const float FADE_IN_TIME = 0f;
            const float FADE_OUT_TIME = 1f;
            var audioIdData = AudioDefine.GetAudioIdData(AudioId.BGM_CAMPAIGN_VALENTINE_SP_CUTT);

            AudioManager.Instance.PlayBgmFromName(audioIdData._cueSheet,
                                                    audioIdData._cueName,
                                                    fadeInTime: FADE_IN_TIME,
                                                    fadeOutTime: FADE_OUT_TIME);
        }

        /// <summary>
        /// 撮影などでカット再生を一時止める
        /// </summary>
        /// <param name="helper"></param>
        public static void PauseCutt(ValentineCutinHelper helper)
        {
            if (helper is null)
            {
                return;
            }

            helper.TimelineController.Pause();
            helper.PauseEnvSound();
        }

        /// <summary>
        /// カット再生を再開する
        /// </summary>
        /// <param name="helper"></param>
        public static void ResumeCutt(ValentineCutinHelper helper)
        {
            if (helper is null)
            {
                return;
            }

            helper.TimelineController.Resume();
            helper.ResumeEnvSound();

            //127003 うま散歩と同じく撮影の中断から戻ってくる時揺れ物の揺れ具合にケアをかける
            UIManager.Instance.StartCoroutine(OnResumeDelayCareCySpring(helper));
        }

#if UNITY_EDITOR && CYG_DEBUG
        /// <summary>
        /// ダイレクトシーンで録画する際にBGMありの動画を取る際に利用している
        /// </summary>
        public static void DebugPlayValentineCutInBgm()
        {
            PlayValentineCutInBgm();
        }
#endif
    }   
}
