using System.Linq;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ウマさんぽキャンペーン：おさんぽ演出ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCampaignWalkingReleasedCutList : DialogInnerBase
    {
        public class SetupParam
        {
            public WorkCharaData.CharaData CharaData { get; private set; }
            public CampaignCutInHelper Helper { get; private set; }

            public SetupParam(WorkCharaData.CharaData charaData, CampaignCutInHelper helper)
            {
                CharaData = charaData;
                Helper = helper;
            }
        }

        #region dialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Outgame424102.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            return data;
        }

        #endregion

        #region SerializeField

        /// <summary> キャラ情報のヘッダー </summary>
        [SerializeField]
        private PartsCharacterProfielHeader _partsProfielHeader;

        /// <summary> 閲覧可能なおでかけ演出一覧リスト </summary>
        [SerializeField]
        private LoopScroll _walkingCutLoopScroll;

        #endregion


        private SetupParam _setupParam;

        // キャンペーンカットを再生するためのヘルパークラス
        private CampaignCutInHelper _helper;
        
        private WorkCampaignWalkingData _workCampaignWalkingData => WorkDataManager.Instance.CampaignWalkingData;


        /// <summary>
        /// 開く
        /// </summary>
        public static void Open(SetupParam setupParam, bool isPlayOpenSe = true)
        {
            if (setupParam.CharaData == null)
            {
                Debug.LogError("charaDataがnullのためダイアログが開けません");
                return;
            }

            var component = LoadAndInstantiatePrefab<DialogCampaignWalkingReleasedCutList>(ResourcePath.DIALOG_CAMPAIGN_WALKING_RELEASED_CUT_LIST);
            var dialogData = component.CreateDialogData();
            dialogData.IsPlayOpenSe = isPlayOpenSe;

            // PushDialogより前にSetupを行う必要があるため、Hashは引数で渡す
            component.Setup(setupParam, dialogData.DialogHash);

            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// 表示内容のセットアップ
        /// </summary>
        private void Setup(SetupParam setupParam, ResourceManager.ResourceHash dialogHash)
        {
            _setupParam = setupParam;

            // ヘッダー部分のセットアップ
            _partsProfielHeader.Setup(_setupParam.CharaData);

            // 演出リストのセットアップ
            SetupLocationList(_setupParam.CharaData, dialogHash);

            // 参照渡し
            _helper = _setupParam.Helper;
        }

        /// <summary>
        /// お出かけ先ごとの演出リストセットアップ
        /// </summary>
        private void SetupLocationList(WorkCharaData.CharaData charaData, ResourceManager.ResourceHash hash)
        {
            var charaId = charaData.Id;
            var masterLocationList = GetMasterLocationList(charaId);
            if (masterLocationList == null)
            {
                return;
            }

            _walkingCutLoopScroll.Setup(masterLocationList.Count, itemBase =>
            {
                var item = itemBase as PartsCampaignWalkingCutListItem;
                var index = item.ItemIndex;
                var master = masterLocationList[index];
                var isViewed = WorkDataManager.Instance.CampaignWalkingData.IsViewed(charaId, master.GetGenre());

                item.UpdateItem(isViewed, master, hash, () => OnClickLocationButton(charaId, master));
            });
        }

        /// <summary>
        /// 特定のキャラのおさんぽ先のマスターデータリストを取得
        /// </summary>
        private List<MasterCampaignWalkingLocation.CampaignWalkingLocation> GetMasterLocationList(int charaId)
        {
            var masterCampaignWalkingChara = WorkDataManager.Instance.CampaignWalkingData.GetCampignWalkingChara(charaId);
            if (masterCampaignWalkingChara == null)
            {
                Debug.LogError($"当該キャラのお出かけデータがありません（キャラID:{charaId}）(キャンペーンID: {WorkDataManager.Instance.CampaignWalkingData.CurrentMasterCampaignWalkingData?.CampaignId ?? 0})");
                return null;
            }

            var masterLocationList = new List<MasterCampaignWalkingLocation.CampaignWalkingLocation>();
            var locationIdList = new List<int>
                {
                    masterCampaignWalkingChara.Location3,
                    masterCampaignWalkingChara.Location1,
                    masterCampaignWalkingChara.Location2,
                }; // @note:MAP上での並びと同じ順番にする事

            // locationId一覧からおさんぽ先のマスターデータを取得してリストに追加
            foreach (var locationId in locationIdList)
            {
                var masterLocation = MasterDataManager.Instance.masterCampaignWalkingLocation.Get(locationId);
                if (masterLocation == null)
                {
                    continue;
                }

                masterLocationList.Add(masterLocation);
            }

            return masterLocationList;
        }

        /// <summary>
        /// お出かけ先のボタンを押下したときの処理
        /// </summary>
        private void OnClickLocationButton(int charaId, MasterCampaignWalkingLocation.CampaignWalkingLocation masterLocation)
        {
            var isViewed = WorkDataManager.Instance.CampaignWalkingData.IsViewed(charaId, masterLocation.GetGenre());
            if (!isViewed)
            {
                return;
            }

            // 各ダイアログの復帰用パラメータ
            {
                // まず復帰用パラメータを全てクリア
                _workCampaignWalkingData.StashedCharaSelectDialogSetupParam = null;
                _workCampaignWalkingData.StashedReleasedCharaDialogSetupParam = null;
                _workCampaignWalkingData.StashedReleasedCutListDialogSetupParam = null;

                // 開いているダイアログの復帰用パラメータをスタッシュしていく
                var charaSelectDialog = DialogManager.Instance.GetDialogInnerComponent<DialogCampaignWalkingCharaSelect>();
                if (charaSelectDialog != null)
                {
                    charaSelectDialog.StashedDialog();
                }
                var releasedCharaDialog = DialogManager.Instance.GetDialogInnerComponent<DialogCampaignWalkingReleasedChara>();
                if (releasedCharaDialog != null)
                {
                    releasedCharaDialog.StashedDialog();
                }
                this.StashedDialog();
            }

            //ギャル散歩に一緒に出掛けるおともウマ娘を抽選、通常のウマさんぽには影響がない
            //リプレイ時抽選したおともウマ娘は保存しない
            _workCampaignWalkingData.LotteryGalWalkingFriendChara(charaId, masterLocation.FriendListId, false);

            var campaignId = _workCampaignWalkingData.CurrentCampaignId;

            // おさんぽ演出カット再生コールチンをキック
            UIManager.Instance.StartCoroutine(CampaignWalkingUtil.CoroutinePlayWalkingCutIn(campaignId, new int[] { charaId, _workCampaignWalkingData.FriendCharaId } ,
                WorkDataManager.Instance.UserData.Gender,
                masterLocation, 
                _helper,
                onComplete: () => {},
                // カットの頭
                onEndFadeOut: () =>
                {
                    // #103661 カット再生される直前ダイアログ閉じるアニメが見れてしまうフレームがあったため、アニメなしで閉じる
                    // ダイアログを全て閉じる
                    DialogManager.RemoveAllDialog(isAnimation: false);
                },
                // カットの終わり際
                onStartFadeIn:() =>
                {
                    // スタッシュしていたダイアログを復元する
                    RestoreStashedDialog();
                },
                // 撮影ボタンを表示する
                isNeedPhotoShotButton:true));
        }

        /// <summary>
        /// このダイアログの復帰用パラメータをスタッシュしておく
        /// </summary>
        public void StashedDialog()
        {
            _workCampaignWalkingData.StashedReleasedCutListDialogSetupParam = _setupParam;
        }

        /// <summary>
        /// スタッシュしていた各ダイアログを復元する
        /// </summary>
        private static void RestoreStashedDialog()
        {
            // 「ウマさんぽキャンペーン」ダイアログの復帰用パラメータがあるなら、それを開く
            {
                var stashedSetupParam = WorkDataManager.Instance.CampaignWalkingData.StashedCharaSelectDialogSetupParam;
                if (stashedSetupParam != null)
                {
                    DialogCampaignWalkingCharaSelect.Open(stashedSetupParam, isPlayOpenSe: false);
                }
            }

            // 「解放済み演出確認」ダイアログの復帰用パラメータがあるなら、それを開く
            {
                var stashedSetupParam = WorkDataManager.Instance.CampaignWalkingData.StashedReleasedCharaDialogSetupParam;
                if (stashedSetupParam != null)
                {
                    DialogCampaignWalkingReleasedChara.Open(stashedSetupParam, isPlayOpenSe: false);
                }
            }

            // 「おさんぽ演出」ダイアログの復帰用パラメータがあるなら、それを開く
            {
                var stashedSetupParam = WorkDataManager.Instance.CampaignWalkingData.StashedReleasedCutListDialogSetupParam;
                if (stashedSetupParam != null)
                {
                    DialogCampaignWalkingReleasedCutList.Open(stashedSetupParam, isPlayOpenSe: false);
                }
            }
        }
    }
}