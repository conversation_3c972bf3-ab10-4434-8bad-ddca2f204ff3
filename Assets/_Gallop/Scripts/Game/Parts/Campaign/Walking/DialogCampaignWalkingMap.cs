using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cute.Http;

namespace Gallop
{
    /// <summary>
    /// ウマさんぽキャンペーン：MAP（おさんぽ先選択）ダイアログ
    /// </summary>
    public class DialogCampaignWalkingMap : DialogInnerBase
    {
        [Header("ヘッダー")]

        /// <summary>ロゴ画像</summary>
        [SerializeField] private RawImageCommon _logoImage = null;

        [Header("MAP部分")]

        /// <summary>MAP背景画像</summary>
        [SerializeField] private RawImageCommon _bgImage;

        /// <summary>おさんぽ先項目一覧</summary>
        [SerializeField] private PartsCampaignWalkingMapItem[] _campaignWalkingMapItemArray = null;

        /// <summary>ウマ娘アイコン</summary>
        [SerializeField] private RawImageCommon _charaIcon;
        /// <summary>ウマ娘名</summary>
        [SerializeField] private TextCommon _charaName;

        /// <summary> マップ上のアニメーションの親 </summary>
        [SerializeField] private Transform _mapTatRoot;

        /// <summary>おさんぽ先マスターとおさんぽ先アイコンのId一覧</summary>
        private List<(int IconId, MasterCampaignWalkingLocation.CampaignWalkingLocation Master)> _masterCampaignWalkingLocationList = new List<(int, MasterCampaignWalkingLocation.CampaignWalkingLocation)>();
        private WorkCampaignWalkingData _workCampaignWalkingData => WorkDataManager.Instance.CampaignWalkingData;

        private MasterCampaignWalkingChara.CampaignWalkingChara _masterCampaignWalkingChara;

        private MasterCampaignWalkingData.CampaignWalkingData _masterCampaignWalkingData;

        /// <summary>マップのループアニメ</summary>
        private TweenAnimationTimelineComponent _mapLoopTat;

        ///<summary>カット演出再生完了時のコールバック</summary>
        private Action _onCompleteCutIn = null;

        // キャンペーンカットを再生するためのヘルパークラス
        private CampaignCutInHelper _helper;

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Outgame408002.Text();
            data.RightButtonText = TextId.Common0003.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.FooterText = TextId.Outgame408001.Text();
            data.DispStackType = DialogCommon.DispStackType.DialogOnDialog; // このダイアログの後に開くダイアログは、このダイアログの上に重ねて開くようにしておく
            data.AutoClose = false; // このダイアログの後に開くダイアログを閉じた時、このダイアログに戻ってきたいので、自動クローズ機能はOFFにしておく
            data.OnChangeSortingOrder = OnChangeDialogSortingOrder;
            return data;
        }


        /// <summary>
        /// DL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register, MasterCampaignWalkingData.CampaignWalkingData master)
        {
            // ロゴ画像
            register.RegisterPathWithoutInfo(ResourcePath.GetCampaignWalkingLogo(master.LogoId));
            // MAP背景画像
            register.RegisterPathWithoutInfo(ResourcePath.GetCampaignWalkingMap(master.MapId));
            // MAP背景アニメーション
            register.RegisterPathWithoutInfo(ResourcePath.GetCampaignWalkingMapTatPath(master.MapId));
            // MAP上の項目
            PartsCampaignWalkingMapItem.RegisterDownload(register, master);
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(CampaignCutInHelper helper, Action onCompleteCutIn = null)
        {
            var instance = LoadAndInstantiatePrefab<DialogCampaignWalkingMap>(ResourcePath.DIALOG_CAMPAIGN_WALKING_MAP);

            var dialogData = instance.CreateDialogData();
            dialogData.AddLeftButtonCallback(instance.OnClickLeftButton);
            dialogData.AddRightButtonCallback(instance.OnClickRightButton);
            DialogManager.PushDialog(dialogData);

            instance.Setup(helper, onCompleteCutIn);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(CampaignCutInHelper helper, Action onCompleteCutIn = null)
        {
            var charaId = _workCampaignWalkingData.CurrentCharaId;
            if (charaId == GameDefine.INVALID_CHARA_ID)
            {
                Debug.LogError("ウマ娘が選択されていません");
                return;
            }

            _masterCampaignWalkingData = WorkDataManager.Instance.CampaignWalkingData.CurrentMasterCampaignWalkingData;
            if (_masterCampaignWalkingData == null)
            {
                Debug.LogError("開催中のウマさんぽCPのマスターデータがありません");
                return;
            }

            _masterCampaignWalkingChara = WorkDataManager.Instance.CampaignWalkingData.GetCampignWalkingChara(charaId);
            if (_masterCampaignWalkingChara == null)
            {
                Debug.LogError("master_campaign_walking_chara.csv に charaId[" + charaId + "]、campaign_Id["+ WorkDataManager.Instance.CampaignWalkingData.CurrentMasterCampaignWalkingData?.CampaignId ?? 0 +"] が登録されていません");
                return;
            }

            var masterCharaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (masterCharaData == null)
            {
                Debug.LogError("master_chara_data.csv に charaId[" + charaId + "] が登録されていません");
                return;
            }

            _onCompleteCutIn = onCompleteCutIn;

            // 参照渡し
            _helper = helper;

            // ダイアログを開いた時点ではおさんぽ先は「未選択」にしておく
            _workCampaignWalkingData.OnSelectLocation(null, isChangedSelectLocation: true);

            // ヘッダー部分
            {
                // ロゴ画像
                _logoImage.texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetCampaignWalkingLogo(_masterCampaignWalkingData.LogoId));
            }

            // MAP部分
            {
                // 背景画像
                _bgImage.texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetCampaignWalkingMap(_masterCampaignWalkingData.MapId));

                // おさんぽ先項目
                {
                    // おさんぽ先１のマスターを取得
                    {
                        var masterCampaignWalkingLocation1 = MasterDataManager.Instance.masterCampaignWalkingLocation.Get(_masterCampaignWalkingChara.Location1);
                        if (masterCampaignWalkingLocation1 != null)
                        {
                            var iconId = _masterCampaignWalkingData.IconIdLocation1;
                            _masterCampaignWalkingLocationList.Add((iconId, masterCampaignWalkingLocation1));
                        }
                    }
                    // おさんぽ先２のマスターを取得
                    {
                        var masterCampaignWalkingLocation2 = MasterDataManager.Instance.masterCampaignWalkingLocation.Get(_masterCampaignWalkingChara.Location2);
                        if (masterCampaignWalkingLocation2 != null)
                        {
                            var iconId = _masterCampaignWalkingData.IconIdLocation2;
                            _masterCampaignWalkingLocationList.Add((iconId, masterCampaignWalkingLocation2));
                        }
                    }
                    // おさんぽ先３のマスターを取得
                    {
                        var masterCampaignWalkingLocation3 = MasterDataManager.Instance.masterCampaignWalkingLocation.Get(_masterCampaignWalkingChara.Location3);
                        if (masterCampaignWalkingLocation3 != null)
                        {
                            var iconId = _masterCampaignWalkingData.IconIdLocation3;
                            _masterCampaignWalkingLocationList.Add((iconId, masterCampaignWalkingLocation3));
                        }
                    }

                    // 各「おさんぽ先」項目のUI更新
                    int masterIndex = 0;
                    foreach(var campaignWalkingMapItem in _campaignWalkingMapItemArray)
                    {
                        if (campaignWalkingMapItem == null)
                            continue;
                        campaignWalkingMapItem.SetActiveWithCheck(false);

                        if (masterIndex >= _masterCampaignWalkingLocationList.Count)
                            break;

                        var masterCampaignWalkingLocation = _masterCampaignWalkingLocationList[masterIndex].Master;
                        var iconId = _masterCampaignWalkingLocationList[masterIndex].IconId;

                        campaignWalkingMapItem.SetActiveWithCheck(true);
                        campaignWalkingMapItem.Setup(iconId, masterCampaignWalkingLocation, OnClickMapItem, DialogHash); // 項目のUI更新！

                        masterIndex++;
                    }

                    SetAlphaReceiver();

                    // マップのループアニメを再生
                    InitializeMapAnimation(_masterCampaignWalkingData.MapId);
                    PlayMapLoopAnimation();
                }

                // ウマ娘アイコン
                {
                    var charaIconPath = ResourcePath.GetCharaThumbnailIconPath(charaId);
                    _charaIcon.texture = ResourceManager.LoadOnView<Texture2D>(charaIconPath);
                }
                // ウマ娘名
                {
                    string charaName = masterCharaData.Name;
                    _charaName.text = charaName;
                }
            }

            // 下部UI
            RefreshFooterUi();
        }

        /// <summary>
        /// 下部UIのセットアップ
        /// </summary>
        private void RefreshFooterUi()
        {
            var dialogCommon = (GetDialog() as DialogCommon);
            if (dialogCommon == null)
                return;

            // 「OK」ボタンを押せかどうかの設定
            var notificationMessage = string.Empty; // この変数がEmptyなら『明るく押せる状態』になり、文字入りなら『グレーアウトされ押せない状態』になります
            {
                // おさんぽ先を選択していないなら押せない
                if (_workCampaignWalkingData.CurrentMasterCampaignWalkingLocation == null)
                {
                    notificationMessage = TextId.Outgame408034.Text();
                }
            }
            dialogCommon.ChangeRightButtonInteractable(notificationMessage);
        }
        
        private void SetAlphaReceiver()
        {
            var canvasRenderer = GetComponent<CanvasRenderer>();
            if (canvasRenderer == null)
            {
                return;
            }

            foreach (var mapItem in _campaignWalkingMapItemArray)
            {
                if (mapItem == null)
                {
                    continue;
                }

                mapItem.SetAlphaReceiver(canvasRenderer);
            }
        }

        /// <summary>
        /// マップ上のオブジェクト表示制御
        /// </summary>
        private void InitializeMapAnimation(int mapId)
        {
            var animationPath = ResourcePath.GetCampaignWalkingMapTatPath(mapId);
            if (string.IsNullOrEmpty(animationPath))
            {
                return;
            }

            // Note:キャラを切り替えてマップを開き直すたびに同じアニメーションがロードされるのを防ぐため、Viewにひもづけてロードしておく
            var mapTatPrefab = ResourceManager.LoadOnView<GameObject>(animationPath);
            if (mapTatPrefab == null)
            {
                Debug.LogError("ウマさんぽMapアニメーションが見つかりません\nパス：" + animationPath);
                return;
            }

            _mapLoopTat = GameObject.Instantiate(mapTatPrefab, _mapTatRoot).GetComponent<TweenAnimationTimelineComponent>();
            if (_mapLoopTat == null)
            {
                Debug.LogError("ウマさんぽMapアニメーションにTweenAnimationTimelineComponentがアタッチされていません\nパス：" + animationPath);
                return;
            }
        }

        /// <summary>
        /// 「おさんぽ先」項目を押した時の処理
        /// </summary>
        private void OnClickMapItem(bool okClick = false)
        {
            // カーソル更新のため、各「おさんぽ先」項目のUI更新
            foreach (var campaignWalkingMapItem in _campaignWalkingMapItemArray)
            {
                if (campaignWalkingMapItem == null)
                    continue;
                campaignWalkingMapItem.RefreshUi();
            }

            // 下部UIの更新
            RefreshFooterUi();

            // 同じ「おさんぽ先」を連射した時
            if (okClick)
            {
                // OKボタンの処理を実行
                OnClickRightButton(GetDialog() as DialogCommon);
            }
        }

        /// <summary>
        /// キャンセルボタンを押した時の処理
        /// </summary>
        public void OnClickLeftButton(DialogCommon dialog)
        {
            dialog.Close();
        }

        /// <summary>
        /// OKボタンを押した時の処理
        /// </summary>
        public void OnClickRightButton(DialogCommon dialog)
        {
            // 「現在選択しているおさんぽ先」を取得
            var currentMasterCampaignWalkingLocation = _workCampaignWalkingData.CurrentMasterCampaignWalkingLocation;
            if (currentMasterCampaignWalkingLocation == null)
                return;

            // 「報酬選択」ダイアログを開く
            var iconId = _masterCampaignWalkingLocationList.FirstOrDefault(data => data.Master.Id == currentMasterCampaignWalkingLocation.Id).IconId ;
            DialogCampaignWalkingRewardSelect.Open(iconId, currentMasterCampaignWalkingLocation, _helper, _onCompleteCutIn);
        }

        private void OnChangeDialogSortingOrder(int orderInLayer, string sortingLayerName)
        {
            // 上に重なったダイアログが閉じるとき吹き出しが前面に表示されるのを防ぐ
            foreach (var campaignWalkingMapItem in _campaignWalkingMapItemArray)
            {
                if (campaignWalkingMapItem == null) 
                    continue;
                
                campaignWalkingMapItem.OnChangeDialogSortingOrder(orderInLayer, sortingLayerName);
            }
        }

        private void OnDestroy()
        {
            // MapItemで使用する低解像度おさんぽ先画像の破棄
            foreach (var campaignWalkingMapItem in _campaignWalkingMapItemArray)
            {
                if (campaignWalkingMapItem == null)
                {
                    continue;
                }

                campaignWalkingMapItem.ReleaseThumbnailTexture();
            }
            Resources.UnloadUnusedAssets();
        }

        #region アニメーション

        /// <summary>
        /// マップのループアニメを再生
        /// </summary>
        public void PlayMapLoopAnimation()
        {
            if (_mapLoopTat == null)
                return;

            _mapLoopTat.PlayLoop("map_loop");
        }

        #endregion
    }
}