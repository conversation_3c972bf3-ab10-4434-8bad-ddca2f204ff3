using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// レイドイベント：イベントボーナス合計パーツ
    /// </summary>
    public class PartsFanRaidTotalBonus : MonoBehaviour, ISingleModeStartEventTotalBonusParts
    {
        /// <summary>
        /// イベントタイトル
        /// </summary>
        [SerializeField]
        private TextCommon _eventTitle = null;
        
        /// <summary>
        /// 左端テキストにイベントタイトル名を自動設定したいかどうか（falseの場合はprefab側で設定）
        /// </summary>
        [SerializeField]
        private bool _autoSetEventTitle = true;
        
        /// <summary>
        /// +1万人などの数値
        /// </summary>
        [SerializeField]
        private TextCommon _valueText = null;

        /// <summary>
        /// Activeかどうか
        /// </summary>
        public bool IsActive => gameObject.activeSelf;

        /// <summary>
        /// 表示準備
        /// </summary>
        public void Setup(int totalBonus, bool isOpen, bool forceActive)
        {
            var isActive = isOpen && totalBonus > 0;
            gameObject.SetActiveWithCheck(isActive || forceActive);
            
            if (_autoSetEventTitle)
            {
                _eventTitle.text = FanRaidUtil.GetEventTitle(WorkDataManager.Instance.FanRaidData.EventId);
            }
            
            _valueText.text = FanRaidUtil.GetBonusText(totalBonus);
        }
    }
}