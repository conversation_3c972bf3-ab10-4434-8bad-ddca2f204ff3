using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// レイドイベント：イベントTOPの全体ファン数
    /// </summary>
    public class PartsFanRaidFanNumCounter : MonoBehaviour
    {
        #region 定数

        /// <summary>達成エフェクトアニメーションの親 </summary>
        private const string FAN_NUM_ACHIEVE_EFFECT_ROOT = "mot_count_number_add";

        /// <summary>達成エフェクトアニメーションの親 </summary>
        private const string FAN_NUM_ACHIEVE_EFFECT_START = "fanraid_eventtop_target_ani";

        #endregion

        #region private変数

        [SerializeField]
        private CountupModifier _countupModifier = null;

        [SerializeField]
        private BitmapTextCommon _suffix = null;

        /// <summary> 全体獲得ファン数が報酬受取条件を達成したときに出るエフェクト </summary>
        private Animator _achieveEffectAnimator = null;
        private BitmapTextCommon _achieveEffectText;
        private bool _waitAchieveEffect = false;

        #endregion

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.FAN_RAID_EVENT_TOP_ACHEVE_EFFECT);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize(long value)
        {
            _countupModifier.SetValue(value, true);
            _suffix.text = TextId.FanRaid400101.Text();

            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.FAN_RAID_EVENT_TOP_ACHEVE_EFFECT);
            _achieveEffectAnimator = GameObject.Instantiate<GameObject>(prefab, transform).GetComponent<Animator>();
            _achieveEffectAnimator.GetComponent<AnimationEventSender>().Initialize(this);
            _achieveEffectAnimator.gameObject.SetActiveWithCheck(false);

            var numParent = GameObjectUtil.FindInDeepChildren(_achieveEffectAnimator.gameObject, FAN_NUM_ACHIEVE_EFFECT_ROOT).transform;
            _achieveEffectText = numParent.GetChild(0).GetComponent<BitmapTextCommon>();
            _achieveEffectText.LoadFont();
        }

        /// <summary>
        /// カウントアップ
        /// </summary>
        /// <param name="needEffect"> 報酬獲得条件達成エフェクトが必要か </param>
        public IEnumerator CountUp(long value, bool needEffect)
        {
            // 数字が変わっていなければ何もしない
            if (_countupModifier.Value == value)
            {
                yield break;
            }

            // 数字のカウントアップ再生
            _countupModifier.SetValue(value);
            _countupModifier.Play();

            yield return new WaitWhile(() => _countupModifier.IsPlaying);

            if (!needEffect)
            {
                yield break;
            }

            // 必要だったら達成エフェクトの再生
            _achieveEffectText.text = FanRaidUtil.GetFanNumText(value);
            _achieveEffectAnimator.gameObject.SetActiveWithCheck(true);

            _waitAchieveEffect = true;
            _achieveEffectAnimator.Play(FAN_NUM_ACHIEVE_EFFECT_START, 0, 0f);

            // エフェクトの待機時間が終わるまで待つ
            yield return new WaitWhile(() => _waitAchieveEffect);
        }

        /// <summary>
        /// 達成エフェクトのAnimation側から呼ばれるコールバック
        /// </summary>
        private void DialogIn()
        {
            _waitAchieveEffect = false;
        }
    }

}
