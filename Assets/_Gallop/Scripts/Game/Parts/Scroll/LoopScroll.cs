using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using System.Linq;
using Cute.Core;
using DG.Tweening;
using UnityEngine.EventSystems;
#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// ループスクロール用のマージンクラス
    /// ** LoopScroll内のマージンを変更する場合はSetMargin関数を呼ぶ
    /// </summary>
    [System.Serializable]
    public class LoopScrollMargin
    {
        public float Left;
        public float Right;
        public float Top;
        public float Bottom;

        public void CopyTo(LoopScrollMargin dest)
        {
            dest.Left = Left;
            dest.Right = Right;
            dest.Top = Top;
            dest.Bottom = Bottom;
        }
    }

    /// <summary>
    /// ループスクロール
    /// </summary>
    public class LoopScroll : UIBehaviour
    {
        #region 定数

        public class ItemRectData
        {
            public Vector2 Size;
            public Vector2 Pos;
        }

        public enum Direction
        {
            None,
            Vertical,
            Horizontal,
        }

        private const float INITIAL_ITEM_SCALE = -1f;
        private const int CREATE_LINE_NUM_ONE_FRAME = 3;
        
        #endregion

        #region SerializeField

        /// <summary>
        /// コピー元オブジェクト
        /// </summary>
        [SerializeField] private LoopScrollItemBase _itemBase = null;
        public LoopScrollItemBase ItemBase
        {
            get => _itemBase;
            set => _itemBase = value;
        }

        /// <summary>
        /// 親のスクロール
        /// </summary>
        [SerializeField] private ScrollRect _scrollRect = null;
        public ScrollRect ScrollRect => _scrollRect;

        /// <summary>
        /// マージン設定、変更する場合はSetMargin関数
        /// </summary>
        [SerializeField] private LoopScrollMargin _margin = new LoopScrollMargin();
        public LoopScrollMargin Margin => _margin;

        /// <summary>
        /// Item同士の間隔
        /// </summary>
        [SerializeField] protected float _spacing = 0;
        public float Spacing
        {
            get { return _spacing; }
            set { _spacing = value; }
        }

        #endregion

        #region プロパティ、変数

        /// <summary>
        /// 向き、ScrollRectと同じ
        /// </summary>
        public Direction ScrollDirection
        {
            get
            {
                if (_scrollRect)
                {
                    if (_scrollRect.vertical || _scrollRect.verticalScrollbar || _isVerticalType)
                        return Direction.Vertical;
                    else if (_scrollRect.horizontal || _scrollRect.horizontalScrollbar)
                        return Direction.Horizontal;
                }
                return Direction.None;
            }
        }

        /// <summary>
        /// RectTranformのキャッシュ
        /// </summary>
        public RectTransform RectTransform
        {
            get
            {
                if (_rectTransform == null)
                {
                    _rectTransform = GetComponent<RectTransform>();
                }

                return _rectTransform;
            }
        }
        private RectTransform _rectTransform;

        public float AnchoredPosition
        {
            get => (ScrollDirection == Direction.Vertical) ? -RectTransform.anchoredPosition.y : RectTransform.anchoredPosition.x;
            set
            {
                if (ScrollDirection == Direction.Vertical)
                {
                    RectTransform.anchoredPosition = new Vector2(RectTransform.anchoredPosition.x, -value);
                }
                else
                {
                    RectTransform.anchoredPosition = new Vector2(value, RectTransform.anchoredPosition.y);
                }
            }
        }

        /// <summary>
        /// spacingを考慮したアイテムのサイズ
        /// </summary>
        public float ItemSize
        {
            get
            {
                if (_itemBase.CacheRectTransform != null && Mathf.Approximately(_itemSize, INITIAL_ITEM_SCALE))
                {
                    _itemSize = (ScrollDirection == Direction.Vertical) ? _itemBase.CacheRectTransform.sizeDelta.y : _itemBase.CacheRectTransform.sizeDelta.x;

                    if (Mathf.Approximately(_itemSize, 0f))
                    {
                        _itemSize = INITIAL_ITEM_SCALE;
                    }

                    if (_itemSize > 0f)
                    {
                        _itemSize = _itemSize + _spacing;
                    }
                }

                return _itemSize;
            }
        }

        protected int MaxSize => _maxSize;

        private float _itemSize = INITIAL_ITEM_SCALE;

        private Coroutine _setupCoroutine;
        public List<LoopScrollItemBase> ItemList { get; private set; } = new List<LoopScrollItemBase>();
        protected float _diffPreFramePosition = 0f;
        protected int _currentItemIndex = 0;
        private bool _isCompleteSetup;
        protected bool IsCompleteSetup => _isCompleteSetup;
        private int _maxSize;
        private int _instantiateItemCount = 5;
        protected int InstantiateItemCount => _instantiateItemCount;
        private LoopScrollMargin _marginOld = new LoopScrollMargin();
        private bool _isUnlimited = false;  //無限スクロールならtrue
        private string _emptyText = null;
        private float _emptyTextPosY = 0;
        private TextFormat.FontSize _emptyTextFontSize = TextFormat.FontSize.Size_32;
        private TextCommon _emptyTextObj = null;
        private bool _isVerticalType = false;  
        private Action<LoopScrollItemBase> _onItemUpdate = null;   // リストアイテム更新時コールバック

        protected List<ItemRectData> _itemRectDataList = new List<ItemRectData>();
#if CYG_DEBUG && UNITY_EDITOR
        public List<ItemRectData> ItemRectDataList => _itemRectDataList;
#endif

        #endregion

        #region 生成関数

        /// <summary>
        /// 同期生成(Generic)
        /// </summary>
        /// </remarks>
        public void Setup<T>(int listCount, Action<T> onItemUpdate, string emptyText = null, float emptyTextPosY = 0, TextFormat.FontSize emptyTextFontSize = TextFormat.FontSize.Size_32) where T : LoopScrollItemBase
        {
            Setup(listCount, item => onItemUpdate(item as T), emptyText, emptyTextPosY, emptyTextFontSize: emptyTextFontSize);
        }

        /// <summary>
        /// 同期生成
        /// </summary>
        /// <param name="listCount"></param>
        /// <param name="onItemUpdate">リストアイテム更新時コールバック</param>
        /// <param name="emptyText">空だった時の表示文言</param>
        /// <remarks>
        /// 要素数によるスクロールバーの表示/非表示やスクロールの有効/無効の制御はScrollRectCommonの処理にまかせる
        /// </remarks>
        public void Setup(int listCount, Action<LoopScrollItemBase> onItemUpdate, string emptyText = null, float emptyTextPosY = 0, float initialAnchoredPosition = 0f, TextFormat.FontSize emptyTextFontSize = TextFormat.FontSize.Size_32)
        {
            StopCurrentSetup();
            //無限スクロール無効化
            _isUnlimited = false;
            _emptyText = emptyText;
            _emptyTextPosY = emptyTextPosY;
            _emptyTextFontSize = emptyTextFontSize;
            //同期で実行する場合はコルーチン化しない
            SetupScrollRect(listCount, onItemUpdate, false, null, 0, initialAnchoredPosition).MoveNext();
        }

        /// <summary>
        /// Grid形式のLoopScrollを作成(LoopScrollGridItemContainerを使用)
        /// </summary>
        /// <param name="allItemNum"></param>
        /// <param name="onItemUpdate"></param>
        /// <param name="emptyText"></param>
        /// <param name="emptyTextPosY"></param>
        /// <param name="initialAnchoredPosition"></param>
        /// <typeparam name="TItem"></typeparam>
        /// <typeparam name="TData"></typeparam>
        public void SetupGrid<TItem>(
            int allItemNum,
            Action<TItem, int> onItemUpdate = null,
            string emptyText = null,
            float emptyTextPosY = 0,
            float initialAnchoredPosition = 0f)
        {
            var colNum = (ItemBase as LoopScrollGridItemContainer)?.GetColumnNum() ?? 1;
            var rowNum = allItemNum / colNum + (allItemNum % colNum == 0 ? 0 : 1);// 割り切れない時は1行足す
            Setup(rowNum, itemBase =>
            {
                if (itemBase is LoopScrollGridItemContainer gridItemContainer)
                {
                    gridItemContainer.UpdateItem<TItem>(allItemNum, onItemUpdate);
                }
            }, emptyText, emptyTextPosY, initialAnchoredPosition);
        }

        /// <summary>
        /// 無限スクロール付き同期生成
        /// </summary>
        /// <param name="listCount"></param>
        /// <param name="onItemUpdate">リストアイテム更新時コールバック</param>
        /// <remarks>
        /// 要素数によるスクロールバーの表示/非表示やスクロールの有効/無効の制御はScrollRectCommonの処理にまかせる
        /// </remarks>
        public void SetupUnlimited(int listCount, Action<LoopScrollItemBase> onItemUpdate)
        {
            StopCurrentSetup();
            //無限スクロール有効化
            _isUnlimited = true;
            //同期で実行する場合はコルーチン化しない
            SetupScrollRect(listCount, onItemUpdate, false, null, 0, 0).MoveNext();
        }

        /// <summary>
        /// 非同期生成
        /// </summary>
        /// <param name="listCount">リストの数</param>
        /// <param name="onItemUpdate">リストアイテム更新時コールバック</param>
        /// <param name="onComplete">初期化が終わった後のコールバック</param>
        /// <param name="createLineNumOneFrame">1フレームで作る数</param>
        /// <param name="itemSetupCallback"></param>
        /// <remarks>
        /// 要素数によるスクロールバーの表示/非表示やスクロールの有効/無効の制御はScrollRectCommonの処理にまかせる
        /// </remarks>
        public void SetupAsync(int listCount,
            Action<LoopScrollItemBase> onItemUpdate,
            Action onComplete = null,
            int createLineNumOneFrame = CREATE_LINE_NUM_ONE_FRAME)
        {
            StopCurrentSetup();
            _isUnlimited = false;
            _setupCoroutine = UpdateDispatcher.StartCoroutine(SetupScrollRect(listCount, onItemUpdate, true, onComplete, createLineNumOneFrame, 0));
        }

        private void StopCurrentSetup()
        {
            _isCompleteSetup = false;
            if (_setupCoroutine != null)
            {
                UpdateDispatcher.StopCoroutine(_setupCoroutine);
                _setupCoroutine = null;
            }
        }

        /// <summary>
        /// スクロール対象となるRectの形成
        /// </summary>
        /// <param name="listCount"></param>
        /// <param name="onItemUpdate"></param>
        /// <param name="isAsync"></param>
        /// <param name="onComplete"></param>
        /// <param name="createLineNumOneFrame"></param>
        /// <returns></returns>
        private IEnumerator SetupScrollRect(int listCount,
            Action<LoopScrollItemBase> onItemUpdate,
            bool isAsync,
            Action onComplete,
            int createLineNumOneFrame,
            float initialAnchoredPosition)
        {
            //最大サイズ取得
            _maxSize = listCount;

            //リストアイテム更新時コールバックを保持
            _onItemUpdate = onItemUpdate;

            //インスタンス数計算
            _instantiateItemCount = CalcInstantiateCount();

            //リストにアイテムが残ってれば削除
            ClearList();
            
            //空文言設定されてたら削除
            if (_emptyTextObj != null)
            {
                Destroy(_emptyTextObj.gameObject);
                _emptyTextObj = null;
            }

            _currentItemIndex = 0;
            _diffPreFramePosition = 0f;
            AnchoredPosition = initialAnchoredPosition;

            _itemSize = INITIAL_ITEM_SCALE;

            float checkMargin = ScrollDirection == Direction.Vertical ? _margin.Top : _margin.Left;
            if (ItemSize != 0f) //一応無限ループ対策
            {
                //while使わずに計算式一発で算出できる気はするけど、とりあえずUpdateと同じ算出方法
                while (AnchoredPosition < _diffPreFramePosition - ItemSize * 2 - checkMargin)
                {
                    _diffPreFramePosition -= ItemSize;
                    _currentItemIndex++;
                }
                while (AnchoredPosition > _diffPreFramePosition - checkMargin)
                {
                    _diffPreFramePosition += ItemSize;
                    _currentItemIndex--;
                }
            }

            // create items
            ScrollRect.content = RectTransform;

            // 生成元のオブジェクトのactiveは切っておく
            // また生成されたオブジェクトはUpdateItem()内でactiveの切り替えが行われる
            _itemBase.gameObject.SetActive(false);

            for (int i = 0; i < _instantiateItemCount; i++)
            {
                var itemObj = Instantiate(_itemBase.gameObject, transform);
                var item = itemObj.GetComponent<LoopScrollItemBase>();
                SetItemPosition(item, i + _currentItemIndex);
                ItemList.Add(item);
                item.ScrollViewport = ScrollRect.viewport;

                //非同期の場合指定数ごとにフレームを進める（フリーズ予防のため）
                if (isAsync && i % createLineNumOneFrame == 0 && i != 0)
                {
                    yield return null;
                }
            }

            //初回の更新、インデックスの先頭から更新する
            for (int i = 0; i < _instantiateItemCount; i++)
            {
                var item = ItemList[i];
                SetItemIndex(item, i + _currentItemIndex);
            }

            SetRectSize();

            UpdateElementSpacing();

            _isCompleteSetup = true;
            onComplete?.Invoke();

            //空文言表示
            SetupEmptyText();

            _setupCoroutine = null;
        }

        /// <summary>
        /// リストを空にする
        /// </summary>
        public void ClearList()
        {
            if (ItemList.Count > 0)
            {
                for (int i = ItemList.Count - 1; i >= 0; i--)
                {
                    GameObject go = ItemList[i].gameObject;
                    ItemList.RemoveAt(i);
                    Destroy(go);
                }
            }
        }

        //空だった時の文言表示
        private void SetupEmptyText()
        {
            const FontColorType FONT_COLOR = FontColorType.Brown;

            //指定がないなら抜ける
            if (_emptyText == null)
                return;

            //要素があるなら抜ける
            if (_maxSize > 0)
                return;

            //SetupScrollRectで毎回破棄されてる
            if(_emptyTextObj == null)
            {
                var res = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_UI_TEXT);
                _emptyTextObj = Instantiate(res, _scrollRect.transform).GetComponent<TextCommon>();
            }

            //一応汎用パーツ使ってるので色だけ上書きしておく
            _emptyTextObj.FontColor = FONT_COLOR;
            
            _emptyTextObj.FontSizeFormat = _emptyTextFontSize;

            //文言指定
            _emptyTextObj.text = _emptyText;

            //位置指定
            _emptyTextObj.rectTransform.anchoredPosition = new Vector2(0, _emptyTextPosY);
        }

        private int CalcInstantiateCount()
        {
            //親のサイズを子のサイズで割り、両端の使いまわし分で2を足せば足りると思われたが、
            //スクロール時の使い回し処理と乖離してるせいか足らなくなることがあったので、更に倍
            const int INSTANTIATE_OFFSET = 4;
            var itemSize = _itemBase.CacheRectTransform.rect.size;
            var scrollRect = _scrollRect.GetComponent<RectTransform>();
            var parentSize = scrollRect.rect.size;
            if (ScrollDirection == Direction.Vertical)
            {
                var hight = parentSize.y;
                if (hight < 1)
                {
                    Debug.LogWarning("ScrollRectのサイズが小さすぎる hight = "+hight);
                    hight = UIManager.DefaultResolution.y;
                }
                return (int)(hight / (itemSize.y + _spacing)) + INSTANTIATE_OFFSET;
            }
            else
            {
                var width = parentSize.x;
                if (width < 1)
                {
                    Debug.LogWarning("ScrollRectのサイズが小さすぎる width = " + width);
                    width = UIManager.DefaultResolution.y;
                }
                return (int)(width / (itemSize.x + _spacing)) + INSTANTIATE_OFFSET;
            }
        }

        /// <summary>
        /// 幅を調整する
        /// </summary>
        protected virtual void SetRectSize()
        {
            int count = _itemRectDataList.Count;
            if (count > 0)
            {
                var delta = RectTransform.sizeDelta;
                if (ScrollDirection == Direction.Vertical)
                {
                    delta.y = Margin.Top;
                    for (int i = 0; i < count; i++)
                    {
                        delta.y += (_itemRectDataList[i].Size.y + Spacing);
                    }
                    delta.y += Margin.Bottom;
                }
                else
                {
                    delta.x = Margin.Left;
                    for (int i = 0; i < count; i++)
                    {
                        delta.x += (_itemRectDataList[i].Size.x + Spacing);
                    }
                    delta.x += Margin.Right;
                }
                RectTransform.sizeDelta = delta;
            }
            else
            {
                var delta = RectTransform.sizeDelta;
                if (ScrollDirection == Direction.Vertical)
                {
                    delta.y = ItemSize * (_maxSize) + Margin.Top + Margin.Bottom;
                }
                else
                {
                    delta.x = ItemSize * (_maxSize) + Margin.Left + Margin.Right;
                }

                RectTransform.sizeDelta = delta;
            }
        }

        #endregion

        #region 更新処理

        /// <summary>
        /// リスト内アイテムのインデックス更新
        /// </summary>
        /// <param name="item"></param>
        /// <param name="index"></param>
        protected void SetItemIndex(LoopScrollItemBase item, int index)
        {
            //リストの範囲外のインデックス値も参照したい場合はこっちを使う
            item.ItemIndexMayOver = index;

            //無限スクロールさせるならインデックス値をラップする
            if (_isUnlimited)
            {
                var loopIndex = GetUnlimitedWrapIndex(index);
                item.gameObject.SetActive(true);
                item.ItemIndex = loopIndex;
                _onItemUpdate?.Invoke(item);
                return;
            }

            if (index < 0 || index >= _maxSize)
            {
                item.gameObject.SetActive(false);
            }
            else
            {
                item.gameObject.SetActive(true);
                item.ItemIndex = index;
                _onItemUpdate?.Invoke(item);
            }
        }

        /// <summary>
        /// 無限スクロール用に引数インデックスをリストの長さでラップさせた値にして返す
        /// </summary>
        /// <returns></returns>
        private int GetUnlimitedWrapIndex(int index)
        {
            if (_maxSize == 0)
                return 0;

            //負の場合
            if (index < 0)
            {
                //ex) 長さが3の場合,
                //-1 → 2, -2 → 1, -3 = 0 …のような変換を行う
                var hash = Mathf.Abs(index) % _maxSize;
                return hash == 0 ? 0 : _maxSize - hash;
            }
            else
            {
                return index % _maxSize;
            }
        }

        public virtual void Update()
        {
            //準備完了前は抜ける
            if (!_isCompleteSetup)
            {
                return;
            }

            if (ItemList == null || ItemList.Count == 0 || ItemSize <= 0)
            {
                return;
            }

            if (ItemList.Count < _instantiateItemCount)
            {
                // 領域外参照をしないようにitemList作成途中なら抜ける
                return;
            }

            float checkMargin = ScrollDirection == Direction.Vertical ? _margin.Top : _margin.Left;

            while (AnchoredPosition < _diffPreFramePosition - ItemSize * 2 - checkMargin)
            {
                _diffPreFramePosition -= ItemSize;
                //末尾に移動させる

                var item = ItemList[0];
                ItemList.RemoveAt(0);
                ItemList.Add(item);

                //DOTweenで移動中なら完了させる
                DOTween.Complete(item, true);

                int targetIndex = _currentItemIndex + _instantiateItemCount;

                SetItemPosition(item, targetIndex);
                SetItemIndex(item, targetIndex);
                UpdateElementSpacing();
                _currentItemIndex++;
            }

            checkMargin = ScrollDirection == Direction.Vertical ? _margin.Top : _margin.Left;

            while (AnchoredPosition > _diffPreFramePosition - checkMargin)
            {
                //頭に移動させる
                _diffPreFramePosition += ItemSize;
                var itemListLastCount = _instantiateItemCount - 1;
                var item = ItemList[itemListLastCount];
                ItemList.RemoveAt(itemListLastCount);
                ItemList.Insert(0, item);

                _currentItemIndex--;

                //DOTweenで移動中なら完了させる
                DOTween.Complete(item, true);

                //位置更新
                SetItemPosition(item, _currentItemIndex);

                //インデックス更新
                SetItemIndex(item, _currentItemIndex);
                UpdateElementSpacing();
            }

#if UNITY_EDITOR
            //マージン変更による位置更新
            ApplyCurrentMargin();
#endif
            UpdateElementSpacing();
        }

        /// <summary>
        /// アイテム位置取得
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public Vector2 GetItemPosition(int index)
        {
            Vector2 pos;
            if ((index >= 0) && (index < _itemRectDataList.Count))
            {
                pos = _itemRectDataList[index].Pos;
            }
            else
            {
                var posOffset = ItemSize * index;
                if (ScrollDirection == Direction.Vertical)
                {
                    pos.x = _margin.Left;
                    pos.y = -posOffset - _margin.Top;
                }
                else
                {
                    pos.x = posOffset + _margin.Left;
                    pos.y = -_margin.Top;
                }
            }
            return pos;
        }

        /// <summary>
        /// アイテム位置更新
        /// </summary>
        /// <param name="item"></param>
        /// <param name="index"></param>
        protected void SetItemPosition(LoopScrollItemBase item, int index)
        {
            var pos = GetItemPosition(index);
            item.CacheRectTransform.anchoredPosition = pos;
        }

        /// <summary>
        /// Activeなアイテムを現在のインデックス値で更新する
        /// </summary>
        public void UpdateActiveItem()
        {
            for(int i = 0; i< ItemList.Count; i++)
            {
                if (ItemList[i].gameObject.activeSelf)
                {
                    _onItemUpdate?.Invoke(ItemList[i]);
                }
            }
        }
        
        /// <summary>
        /// アイテムのアクティブ状態更新(IndexMayOver版）
        /// UpdateMaxSize使用時など表示個数に変動がある場合に使用
        /// </summary>
        public void UpdateItemActiveByIndexMayOver(int dataCount)
        {
            for(int i = 0; i< ItemList.Count; i++)
            {
                var index = ItemList[i].ItemIndexMayOver;
                ItemList[i].gameObject.SetActive(index >= 0 && index < dataCount);
            }
        }
        
        /// <summary>
        /// アクティブアイテムリスト取得
        /// </summary>
        public List<GameObject> GetActiveItemList()
        {
            return ItemList.Where(a => a.gameObject.activeSelf).Select(a => a.gameObject).ToList();
        }

        /// <summary>
        /// LoopScrollに含まれる全てのアイテムのリストを取得する
        /// </summary>
        /// <returns></returns>
        public List<GameObject> GetAllItemList()
        {
            return ItemList.Select(a => a.gameObject).ToList();
        }
        
        /// <summary>
        /// 最大サイズ更新
        /// </summary>
        public void UpdateMaxSize(int maxSize)
        {
            _maxSize = maxSize;
            SetRectSize();
        }

        /// <summary>
        /// マージン更新
        /// </summary>
        /// <param name="top"></param>
        /// <param name="bottom"></param>
        /// <param name="left"></param>
        /// <param name="right"></param>
        public void SetMargin(float top = 0, float bottom = 0, float left = 0, float right = 0)
        {
            var isChange = false;

            //元々０かもしれないので１つ１つ見る
            if(!Math.IsFloatEqualLight(_marginOld.Top, top))
            {
                _margin.Top = top; isChange = true;
            }
            if (!Math.IsFloatEqualLight(_marginOld.Bottom, bottom))
            {
                _margin.Bottom = bottom; isChange = true;
            }
            if (!Math.IsFloatEqualLight(_marginOld.Left, left))
            {
                _margin.Left = left; isChange = true;
            }
            if (!Math.IsFloatEqualLight(_marginOld.Right, right))
            {
                _margin.Right = right; isChange = true;
            }

            //変わってないなら何もしない
            if (isChange == false)
                return;

            for (int i = 0, count = ItemList.Count; i < count; ++i)
            {
                var item = ItemList[i];
                SetItemPosition(item, i + _currentItemIndex);
            }
            SetRectSize();

            _margin.CopyTo(_marginOld);
        }

        /// <summary>
        /// マージン変更による位置更新
        /// </summary>
        public void ApplyCurrentMargin() => SetMargin(_margin.Top, _margin.Bottom, _margin.Left, _margin.Right);
        

        /// <summary>
        /// アイテムの位置をリセットする
        /// </summary>
        public void ResetItemPosition()
        {
            for (int i = 0, count = ItemList.Count; i < count; ++i)
            {
                var item = ItemList[i];
                item.CacheRectTransform.anchoredPosition = Vector2.zero;
                SetItemPosition(item, i + _currentItemIndex);
            }
        }

        /// <summary>
        /// スクロール位置を先頭に戻す
        /// </summary>
        public void ResetScrollTop()
        {
            AnchoredPosition = 0f;
            _diffPreFramePosition = 0f;
            _currentItemIndex = 0;

            for (int i = 0; i < _instantiateItemCount; i++)
            {
                var item = ItemList[i];
                SetItemIndex(item, i);
            }
        }

        public void RecalcInstantiateCount()
        {
            var count = CalcInstantiateCount();

            if (count > _instantiateItemCount)
            {
                var addCount = count - _instantiateItemCount;
                for (int i = 0; i < addCount; i++)
                {
                    var itemObj = Instantiate(_itemBase.gameObject, transform);
                    var item = itemObj.GetComponent<LoopScrollItemBase>();
                    ItemList.Add(item);
                }
            }
            else if (count < _instantiateItemCount)
            {
                var delCount = _instantiateItemCount - count;
                for (int i = 0; i < delCount; i++)
                {
                    var index = ItemList.Count - 1;
                    var item = ItemList[index];
                    GameObject.Destroy(item.gameObject);
                    ItemList.RemoveAt(index);
                }
            }

            _instantiateItemCount = count;

            ResetScrollTop();
        }

        /// <summary>
        /// 指定したIndexのセルをScrollViewの指定位置(0f ~ 1f)に持ってくるように座標を設定する（Verticalスクロール限定）
        /// </summary>
        public void SetCellPositionToTop(int index, bool useTopMargin = false) =>
            SetCellPositionCore(index, 0f, useTopMargin);

        public void SetCellPositionToCenter(int index, bool useTopMargin = false) =>
            SetCellPositionCore(index, 0.5f, useTopMargin);

        public void SetCellPositionToBottom(int index, bool useTopMargin = false) =>
            SetCellPositionCore(index, 1f, useTopMargin);



        private void SetCellPositionCore(int index, float normalizePosition, bool useTopMargin = false)
        {
            if (ScrollDirection != Direction.Vertical)
            {
                Debug.LogWarning("縦スクロールではないので移動できません");
                return;
            }

            // 最大値を超えている場合は何もしない
            if (index >= _maxSize) return;

            var parentSize = _scrollRect.GetComponent<RectTransform>().rect.size;
            var scrollSize = RectTransform.rect.size;

            // セルが全てスクロール範囲内であれば何もしない
            if (scrollSize.y <= parentSize.y) return;

            var pos = GetItemPosition(index);
            if (useTopMargin)
            {
                pos.y += _margin.Top;
            }

            // セルの座標とセルの大きさ足し合わせた数値をContentの一番下に持ってくる
            var cellSize = ItemSize * normalizePosition;
            var cellRefPos = -pos.y + cellSize;

            // 指定箇所に合わせたいセルの位置 - (スクロール範囲 * 配置指定）
            var anchoredPosition = cellRefPos - (parentSize.y * normalizePosition);

            // 範囲を超えていたら戻す
            anchoredPosition = Mathf.Min(anchoredPosition, (scrollSize - parentSize).y);
            anchoredPosition = Mathf.Max(anchoredPosition, 0f);

            AnchoredPosition = -anchoredPosition;

            // 一度全体の更新をかけて位置を確定させる
            Update();
        }

        /// <summary>
        /// 指定アイテムがスクロールの内に来るような位置を計算
        /// </summary>
        /// <param name="index"></param>
        /// <param name="itemPos">指定のアイテムがリストのどこに来るか(0～1)</param>
        /// <returns></returns>
        public float CalcVerticalNormalizedScrollPosition(int index, float itemPos = 0)
        {
            var scrollRectHeight = ScrollRect.GetComponent<RectTransform>().rect.height;
            var verticalMargin = Margin.Top + Margin.Bottom;

            // スクロール可能領域
            var scrollableHeight = RectTransform.rect.height - verticalMargin - scrollRectHeight;
            if (scrollableHeight <= 0) return 1.0f;// 念のため

            // 1アイテム当たりの比率
            var itemRate = ItemSize / scrollableHeight;
            var scrollRectRate = scrollRectHeight / scrollableHeight;

            // 比率から位置を求める
            var scrollPos = 1f - (index + (1 - itemPos)) * itemRate + itemPos * scrollRectRate;

            return Mathf.Clamp01(scrollPos);
        }

        #endregion

        #region アニメーション

        /// <summary>
        /// IN再生
        /// リストより下部のUIがあれば最後に再生されるようにする
        /// </summary>
        public static void PlayInAnimation(List<GameObject> itemList, Action onComplete = null, GameObject bottomTarget = null)
        {
            var animDataList = new List<ViewBase.InOutAnimationData>(itemList.Count+1);
            for (int i = 0; i < itemList.Count; i++)
            {
                AddAnimationData(animDataList, TweenAnimation.PresetType.PartsInMoveAndFade, itemList[i]);
            }

            if (bottomTarget != null)
            {
                AddAnimationData(animDataList, TweenAnimation.PresetType.PartsInMoveAndFade, bottomTarget);
            }

            UIUtil.PlayInViewParts(animDataList, null, onComplete);
        }

        /// <summary>
        /// IN再生
        /// リストより下部のUIがあれば最後に再生されるようにする
        /// </summary>
        /// <param name="resetItemPoisition">アイテムポジションを初期化する</param>
        /// <param name="onComplete"></param>
        /// <param name="bottomTarget"></param>
        public void PlayInAnimation(bool resetItemPoisition, Action onComplete = null, GameObject bottomTarget = null)
        {
            if (resetItemPoisition)
            {
                ResetItemPosition();
            }
            PlayInAnimation(GetActiveItemList(), onComplete, bottomTarget);
        }

        /// <summary>
        /// Out再生
        /// 下部UIはすぐに単独再生
        /// </summary>
        public static void PlayOutAnimation(List<GameObject> itemList, Action onComplete = null, GameObject bottomTarget = null)
        {
            var animDataList = new List<ViewBase.InOutAnimationData>(itemList.Count);
            for (int i = 0; i < itemList.Count; i++)
            {
                AddAnimationData(animDataList, TweenAnimation.PresetType.PartsOutMoveAndFade, itemList[i]);
            }
            UIUtil.PlayInViewParts(animDataList, null, onComplete);

            // 下部UIはすぐに単独再生
            if (bottomTarget != null)
            {
                animDataList.Clear();
                AddAnimationData(animDataList, TweenAnimation.PresetType.PartsOutMoveAndFade, bottomTarget);
                UIUtil.PlayInViewParts(animDataList, null, null);
            }
        }
        
        /// <summary>
        /// アニメーションデータ追加
        /// </summary>
        public static void AddAnimationData(List<ViewBase.InOutAnimationData> list, TweenAnimation.PresetType presetType, GameObject target)
        {
            list.Add(new ViewBase.InOutAnimationData(presetType, target));
        }

        #endregion

        #region サイズ可変

        public virtual void AddItemRectData(Vector2 size)
        {
            // 既存のアイテムの位置をずらす
            var count = _itemRectDataList.Count;

            for (int i = 0; i < count; i++)
            {
                var pos = _itemRectDataList[i].Pos;
                if (ScrollDirection == Direction.Vertical)
                {
                    pos.y -= (size.y + Spacing);
                }
                else
                {
                    pos.x += (size.x + Spacing);
                }
                _itemRectDataList[i].Pos = pos;
            }

            // 先頭に追加する
            var itemRectData = new ItemRectData();
            itemRectData.Size = size;
            if (ScrollDirection == Direction.Vertical)
            {
                itemRectData.Pos = new Vector2(Margin.Left, -Margin.Top);
            }
            else
            {
                itemRectData.Pos = new Vector2(Margin.Left, Margin.Top);
            }
            _itemRectDataList.Insert(0, itemRectData);

            SetRectSize();  
        }

        public bool GetItemRectData(int index, out ItemRectData itemRectData)
        {
            itemRectData = null;

            if ((index >= 0) && (index < _itemRectDataList.Count))
            {
                itemRectData = _itemRectDataList[index];
                return true;
            }
            return false;
        }

        /// <summary>
        /// スクロールアイテム表示Rectデータを取得せず、存在しているかどうかをチェック
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public bool ItemRectDataIsExist(int index)
        {
            return (index >= 0) && (index < _itemRectDataList.Count);
        }

        //要素ループによりサイズ不定の要素同士の間の間隔が正常になっていない問題を対応
        public virtual void UpdateElementSpacing() {}

        //スクロールできない場合verticalがfalseになり、スクロール要素の座標計算が横タイプになってしまうので
        //縦タイプの場合、強制に指定しておく
        public void SetVerticalType(bool isVertical)
        {
            _isVerticalType = isVertical;
        }

        #endregion
    }

#if CYG_DEBUG && UNITY_EDITOR
    [CustomEditor(typeof(LoopScroll))]
    public class LoopScrollEditor : Editor
    {
        private bool _foldout = false;

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            GUIUtil.Separator();

            _foldout = EditorGUILayout.Foldout(_foldout, "ItemRectData");
            if (_foldout)
            {
                var loopScroll = target as LoopScroll;
                var count = loopScroll.ItemRectDataList.Count;
                for (int i = 0; i < count; i++)
                {
                    var itemRectData = loopScroll.ItemRectDataList[i];
                    using (new EditorGUILayout.HorizontalScope())
                    {
                        EditorGUILayout.Vector2Field("", itemRectData.Size);
                        EditorGUILayout.Vector2Field("", itemRectData.Pos);
                    }
                }
            }
        }
    }
#endif
}
