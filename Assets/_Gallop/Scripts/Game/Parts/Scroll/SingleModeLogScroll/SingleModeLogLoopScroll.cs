using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// 育成ログスクロールビュー
    /// </summary>
    public class SingleModeLogLoopScroll : LoopScroll
    {
        //Content最大サイズを保持、要素サイズ不定の場合に利用
        public bool KeepMaxContentSize { get; set; }

        //要素サイズ不定の場合、要素間隔を更新する場合に利用
        public bool UseUpdateElementSpacing { get; set; }

        private Dictionary<int, float> _ignoreElementIndexAndSizeDict = new Dictionary<int, float>();

        //動的に変動する要素サイズに合わせてスクロールContentのサイズを拡大する
        public void UpdateContentSizeByElementSize((Vector2 elementSize, int index) updateData)
        {
            var updateElementHeight = updateData.elementSize.y;

            var itemHeight = ItemSize - Spacing;

            var diff = updateElementHeight - itemHeight;

            if (diff > 0f && !_ignoreElementIndexAndSizeDict.ContainsKey(updateData.index))
            {
                var scrollRectSize = RectTransform.sizeDelta;
                scrollRectSize.y += diff;
                RectTransform.sizeDelta = scrollRectSize;
                _ignoreElementIndexAndSizeDict.Add(updateData.index, updateElementHeight);
            }
        }

        //要素ループによりサイズ不定の要素同士の間の間隔が正常になっていない問題を対応
        public override void UpdateElementSpacing()
        {
            if (ScrollDirection != Direction.Vertical)
            {
                Debug.LogWarning("この関数は現状縦スクロールだけに対応");
                return;
            }

            if (!UseUpdateElementSpacing)
            {
                return;
            }

            var itemList = ItemList.Where(x => x.isActiveAndEnabled).OrderBy(x => x.ItemIndex);

            var count = itemList.Count();

            for (int i = 1; i < count; i++)
            {
                var currentElement = itemList.ElementAt(i);
                var currentElementPos = currentElement.CacheRectTransform.anchoredPosition;
                var prevElement = itemList.ElementAt(i - 1);
                var prevElementPos = prevElement.CacheRectTransform.anchoredPosition;
                var prevElementSize = prevElement.CacheRectTransform.sizeDelta;
                ItemRectData rectdata = null;

                if (GetItemRectData(currentElement.ItemIndex, out rectdata))
                {
                    currentElementPos = rectdata.Pos;
                }
                else
                {
                    var newPos = prevElementPos.y - prevElementSize.y - Spacing;

                    currentElementPos.y = newPos;
                }

                //diffと前の要素の高さが一致するなら間隔スペースに問題ない
                var diff = Mathf.Abs(currentElementPos.y) - Mathf.Abs(prevElementPos.y) - Spacing;

                if (diff != prevElementSize.y)
                {
                    currentElementPos.y += (diff - prevElementSize.y);
                }

                currentElement.CacheRectTransform.anchoredPosition = currentElementPos;
            }

            var newRectSize = RectTransform.sizeDelta;

            newRectSize.y = 0f;

            foreach (var item in itemList)
            {
                var height = item.CacheRectTransform.sizeDelta.y;
                newRectSize.y += height;
            }

            if (newRectSize.y > 0f)
            {
                newRectSize.y += Spacing * count + Margin.Top;

                if (RectTransform.sizeDelta.y < newRectSize.y)
                {
                    RectTransform.sizeDelta = newRectSize;
                }
            }
        }

        public override void Update()
        {
            //準備完了前は抜ける
            if (!IsCompleteSetup)
            {
                return;
            }

            if (ItemList == null || ItemList.Count == 0 || ItemSize <= 0)
            {
                return;
            }

            if (ItemList.Count < InstantiateItemCount)
            {
                // 領域外参照をしないようにitemList作成途中なら抜ける
                return;
            }

            float checkMargin = ScrollDirection == Direction.Vertical ? Margin.Top : Margin.Left;

            //下にスクロール
            while (AnchoredPosition < _diffPreFramePosition - (GetFixedItemSize(0) + GetFixedItemSize(1) + checkMargin))
            {
                int targetIndex = _currentItemIndex + InstantiateItemCount;

                //スクロールアイテムのサイズ不定の場合、
                //ドラッグ操作でスクロール上限超えてスクロール行った場合要素サイズ比べる用の要素取得が正常に動作しないケースがため
                //Index上限下限より更新させない（正常ループ差し替え表示に影響しない）
                if(targetIndex < 0 || targetIndex >= MaxSize)
                {
                    break;
                }

                // 先頭から移動させる要素のサイズぶん_diffPreFramePositionを動かす
                _diffPreFramePosition -= GetFixedItemSize(0);

                //末尾に移動させる
                var item = ItemList[0];
                ItemList.RemoveAt(0);
                ItemList.Add(item);

                SetItemPosition(item, targetIndex);
                SetItemIndex(item, targetIndex);
                //DOTweenで移動中なら完了させる
                DOTween.Complete(item, true);

                _currentItemIndex++;
            }

            //上にスクロール
            while (AnchoredPosition > _diffPreFramePosition - checkMargin)
            {
                var nextIndex = _currentItemIndex - 1;

                //スクロールアイテムのサイズ不定の場合、
                //ドラッグ操作でスクロール上限超えてスクロール行った場合要素サイズ比べる用の要素取得が正常に動作しないケースがため
                //Index上限下限より更新させない（正常ループ差し替え表示に影響しない）
                if (nextIndex < 0 || nextIndex >= MaxSize)
                {
                    break;
                }

                _currentItemIndex = nextIndex;

                //頭に移動させる
                var itemListLastCount = InstantiateItemCount - 1;
                var item = ItemList[itemListLastCount];
                ItemList.RemoveAt(itemListLastCount);
                ItemList.Insert(0, item);

                //DOTweenで移動中なら完了させる
                DOTween.Complete(item, true);

                //位置更新
                SetItemPosition(item, _currentItemIndex);
                //インデックス更新
                SetItemIndex(item, _currentItemIndex);
                
                // 移動によって先頭になった要素のサイズぶん_diffPreFramePositionを動かす
                _diffPreFramePosition += GetFixedItemSize(0);
            }

#if UNITY_EDITOR
            //マージン変更による位置更新
            SetMargin(Margin.Top, Margin.Bottom, Margin.Left, Margin.Right);
#endif

            UpdateElementSpacing();
        }

        //親と違って新規追加されたデータを最後尾に入れる
        /// <summary>
        /// スクロールアイテム表示用設定データを追加
        /// </summary>
        /// <param name="size"></param>
        public override void AddItemRectData(Vector2 size)
        {
            var count = _itemRectDataList.Count;

            var itemRectData = new ItemRectData();
            itemRectData.Size = size;
            if (ScrollDirection == Direction.Vertical)
            {
                itemRectData.Pos = new Vector2(Margin.Left, -Margin.Top);
            }
            else
            {
                itemRectData.Pos = new Vector2(Margin.Left, Margin.Top);
            }

            //ログスクロールは現状縦だけなので、横スクロール未対応
            if(count > 0)
            {
                var prevElement = _itemRectDataList[count - 1];
                if (ScrollDirection == Direction.Vertical)
                {
                    var pos = itemRectData.Pos;
                    pos.y = prevElement.Pos.y - (prevElement.Size.y + Spacing);
                    itemRectData.Pos = pos;
                }
            }
            _itemRectDataList.Add(itemRectData);

            SetRectSize();
        }

        /// <summary>
        /// 幅を調整する
        /// </summary>
        protected override void SetRectSize()
        {
            var delta = RectTransform.sizeDelta;
            delta.y = ItemSize * (MaxSize) + Margin.Top + Margin.Bottom;

            if (KeepMaxContentSize)
            {
                if (RectTransform.sizeDelta.y < delta.y)
                    RectTransform.sizeDelta = delta;
            }
        }

        /// <summary>
        /// spacingを考慮したアイテムのサイズを取得する
        /// サイズ可変の場合は変動後のサイズが取得される
        /// </summary>
        /// <param name="indexOnItemList">LoopScroll.ItemList上のIndex</param>
        /// <returns></returns>
        private float GetFixedItemSize(int indexOnItemList)
        {
            if (ItemList.IsNullOrEmpty()) return 0f;
            if (indexOnItemList < 0 || indexOnItemList >= ItemList.Count) return 0f;

            var targetItemSize = ItemList[indexOnItemList].CacheRectTransform.sizeDelta;
            return ScrollDirection == Direction.Vertical ? targetItemSize.y + Spacing : targetItemSize.x + Spacing;
        }
    }
}
