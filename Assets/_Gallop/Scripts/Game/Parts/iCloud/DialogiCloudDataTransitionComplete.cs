using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// iCloudからのデータ連携が完了したことを通知するダイアログ
    /// </summary>
    public class DialogiCloudDataTransitionComplete
    {
        #region Method

        /// <summary>
        /// 開く
        /// </summary>
        public static void Open()
        {
            var data = new DialogCommon.Data();

            data.SetSimpleOneButtonMessage(
                TextId.AccoutDataLink0169.Text(),
                TextId.AccoutDataLink0167.Text(),
                (d) =>
                {
                    GameSystem.Instance.SoftwareReset();
                }
            );
            data.AutoClose = false;
            data.EnableOutsideClick = false;
            data.DialogType = DialogCommon.DialogType.System;
            DialogManager.PushDialog(data);
        }

        #endregion
    }
}