using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// ゲーム内定数定義
    /// 規模が大きい箇所からこちらに定義していくように
    /// </summary>
    public static partial class GameDefine
    {
        #region マスターデータ関連
        public const string TutorialMasterDBName = "tutorial_master";
        public static string MasterDbName = "master";
        public const string MasterDbExtension = ".mdb";
        public const string lz4Extension = ".lz4";
        public static string UnpackedMasterDBPath
        {
            get
            {
                var path = "/master";
#if UNITY_EDITOR && CYG_DEBUG
                // 環境固有アセバン設定している場合は、それで上書きする
                var extensionPath = UseUniqueAsseBundleSetting.GetUniqueAsseBundlePath();
                if (!string.IsNullOrEmpty(extensionPath))
                { 
                        path += extensionPath;
                }
#endif
                path += "/" + MasterDbName + MasterDbExtension;

                return path;
            }
        }

        public static string MasterDBPath = MasterDbName + MasterDbExtension + lz4Extension;
        
#if UNITY_EDITOR
        public const string LOCAL_MDB_DIRECTRY = "CodeGenerator/Generated";
#endif
        #endregion

        #region APIサーバー定義

#if SERVER_LOCAL
        // Local設定の際の例外設定 
        public const string API_PROTOCOL = "http://";
        public const string APPLICATION_SERVER_URL = API_PROTOCOL + "localhost/gallop";
#else
        // インフラ提供のサーバー(本番・開発）にアクセスする場合
#if UNITY_ANDROID
        public const string API_PROTOCOL = "https://";
#elif UNITY_IOS
        public const string API_PROTOCOL = "https://";
#else
        public const string API_PROTOCOL = "https://";
#endif

        // サーバURL定義 ビルド時にplayer settingsの内容を見て選択されます.
#if SERVER_PRODUCTION || SERVER_PRODUCTION_TEST // 本番
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/umamusume";
#elif SERVER_STAGING_1  // ステージング_1.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/gallop";
#elif SERVER_STAGING_2  // ステージング_2.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/gallop";
#elif SERVER_STAGING_11 // ステージング_11.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/gallop";
#elif SERVER_STAGING_21 // ステージング_21.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/gallop";
#elif SERVER_STAGING_31 // ステージング_31.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/gallop";
#elif SERVER_STAGING_41 // ステージング_41.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+"xxxxxxxxxx/gallop";
#elif SERVER_DEV11      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL + "xxxxxxxxxx/gallop";
#elif SERVER_DEV101      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV102      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV103      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV104      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV105      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV106      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV107      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV108      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV109      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV110      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV111      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV112      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV113      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV114      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV115      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV116      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV117      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV118      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV119      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV120      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV121      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV122      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV123      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV124      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV125      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV126      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV127      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV128      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV129      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV130      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV131      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV132      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV133      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV134      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV135      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV136      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV137      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV138      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV139      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV140      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV141      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV142      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV143      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV144      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV145      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV146      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV147      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV148      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV149      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV150      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV151      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV152      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV153      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV154      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV155      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV156      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV157      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV158      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV159      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV160      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV161      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV162      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV163      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV164      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV165      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV166      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV167      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV168      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV169      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV170      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV171      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV172      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV173      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV174      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV175      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV176      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV177      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV178      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV179      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif SERVER_DEV180      // 開発環境↓.
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#elif ST_SANDBOX_PURCHASE   // サーバータスクの課金作業環境
        public const string APPLICATION_SERVER_URL = API_PROTOCOL+ "xxxxxxxxxx/gallop";
#else
        //何も指定が無い場合。
        public const string APPLICATION_SERVER_URL = API_PROTOCOL + "xxxxxxxxxx/gallop";

#endif  // #if SERVER_XXX
#endif  // #if USE_SERVER_LOCAL

        /// <summary>
        /// アプリケーションサーバー用
        /// </summary>
        public static string ApplicationServerUrl
        {
            get
            {
                if (applicationServerUrl == null)
                {
                    applicationServerUrl = APPLICATION_SERVER_URL;
                }
                return applicationServerUrl;
            }
        }
        //キャッシュ用、デバッグ時はこいつを書き換える
        private static string applicationServerUrl = null;

        /// <summary>
        /// 中間者攻撃対策、証明書のフィンガープリントを取得する
        /// </summary>
        public static string FingerPrint
        {
            get
            {
                //開発サーバーでの証明書チェック
                if (ApplicationServerUrl.StartsWith(GameDefine.API_PROTOCOL + "dev"))
                {
                    return "xxxxxxxxxx";
                }
                Debug.LogError("開発サーバー以外の証明書には対応されていないため、リーダーへ相談し手配を行ってください。");
                return "";
            }
        }

        /// <summary>
        /// サーバー名取得 dev11, dev12, stg01など
        /// </summary>
        /// <returns></returns>
        public static string GetServerName()
        {
            var tokens = ApplicationServerUrl.Replace("//", "/").Split(new char[] { '/', '.' });
            return tokens[1];
        }

        /// <summary>
        /// 接続先サーバーのホスト名(dev11, dev12等)、エイリアス番号(001～030)を取得する
        /// </summary>
        /// <remarks>
        /// 将来的な仕様( dev100～dev150 )には未対応
        /// </remarks>
        /// <returns></returns>
        public static void GetServerAliasInfo(ref string hostNo, ref string aliasNo)
        {
            var tokens = ApplicationServerUrl.Replace("//", "/").Split(new char[] { '/' });

            string hostName = tokens[1];
            string aliasName = tokens[2];

            hostName = hostName.Split(new char[] { '-' })[0];
            // hostNameは dev101 のような値が取れるので数字のみに変換
            hostNo = hostName.Replace("dev", "").Replace("stg", "");    // devXXX, stgXXXの両対応できるように

            aliasNo = aliasName.Replace("gallop", "");
        }

#if CYG_DEBUG
        public enum DebugServer
        {
            local,
            stg01, 
            stg02, 
            stg11,
            stg21,
            stg31,
            stg41,
            dev11, 
            dev101, dev102, dev103, dev104, dev105, dev106, dev107, dev108, dev109, dev110,
            dev111, dev112, dev113, dev114, dev115, dev116, dev117, dev118, dev119, dev120,
            dev121, dev122, dev123, dev124, dev125, dev126, dev127, dev128, dev129, dev130,
            dev131, dev132, dev133, dev134, dev135, dev136, dev137, dev138, dev139, dev140,
            dev141, dev142, dev143, dev144, dev145, dev146, dev147, dev148, dev149, dev150,
            dev151, dev152, dev153, dev154, dev155, dev156, dev157, dev158, dev159, dev160,
            dev161, dev162, dev163, dev164, dev165, dev166, dev167, dev168, dev169, dev170,
            dev171, dev172, dev173, dev174, dev175, dev176, dev177, dev178, dev179, dev180,
            Max,
        }

        public static readonly Dictionary<DebugServer, string> _connectDict = new Dictionary<DebugServer, string>((int)DebugServer.Max)
        {
            {DebugServer.local, "http://localhost/gallop"},
            {DebugServer.stg01, API_PROTOCOL+"xxxxxxxxxx/gallop"},
            {DebugServer.stg02, API_PROTOCOL+"xxxxxxxxxx/gallop"},
            {DebugServer.stg11, API_PROTOCOL+"xxxxxxxxxx/gallop"},
            {DebugServer.stg21, API_PROTOCOL+"xxxxxxxxxx/gallop"},
            {DebugServer.stg31, API_PROTOCOL+"xxxxxxxxxx/gallop"},
            {DebugServer.stg41, API_PROTOCOL+"xxxxxxxxxx/gallop"},
            {DebugServer.dev11, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev101, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev102, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev103, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev104, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev105, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev106, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev107, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev108, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev109, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev110, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev111, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev112, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev113, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev114, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev115, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev116, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev117, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev118, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev119, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev120, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev121, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev122, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev123, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev124, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev125, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev126, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev127, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev128, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev129, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev130, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev131, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev132, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev133, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev134, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev135, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev136, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev137, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev138, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev139, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev140, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev141, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev142, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev143, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev144, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev145, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev146, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev147, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev148, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev149, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev150, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev151, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev152, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev153, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev154, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev155, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev156, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev157, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev158, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev159, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev160, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev161, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev162, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev163, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev164, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev165, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev166, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev167, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev168, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev169, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev170, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev171, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev172, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev173, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev174, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev175, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev176, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev177, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev178, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev179, API_PROTOCOL + "xxxxxxxxxx/gallop"},
            {DebugServer.dev180, API_PROTOCOL + "xxxxxxxxxx/gallop"},
        };

        /// <summary>
        /// デバッグ用にサーバー切り替える
        /// </summary>
        public static void DebugApplicationServerUrl(DebugServer changeServer, bool fromBootSequence=false, bool doSave = true, bool refreshCurrentConnectURLIndex = true)
        {
            if (_connectDict.ContainsKey(changeServer))
            {
                applicationServerUrl = _connectDict[changeServer];
            }
            else
            {
                var _split = changeServer.ToString().ToLower().Split('_');
                applicationServerUrl = API_PROTOCOL + _split[0] + ".gallop.cygames.jp/gallop";
                if (_split.Length == 2) applicationServerUrl += "_" + _split[1];
            }

            if (doSave)
            {
                SaveDataManager.Instance.GetDebugSettingSaveData().ConnectURLIndex = (int)changeServer;
                SaveDataManager.Instance.GetDebugSettingSaveData().Save();
#if UNITY_EDITOR && CYG_DEBUG
                if (refreshCurrentConnectURLIndex)
                {
                    // デバッグ接続先が変更されたため更新
                    ConnectURLUtil.RefreshCurrentConnectURLIndexFromSaveData();
                }

                // UnityEditorは固有設定を保存
                EditorUniqueDevServerSetting.SetDevServer((int)changeServer);
#endif
            }

            if( fromBootSequence ) { return; }   // 起動初期化時はこの後のソフトウェアリセットやセーブデータ削除は走らせたくない

            OnChangeDebugApplicationURL();
        }
        
        /// <summary>
        /// デフォルトサーバーに切り替える
        /// </summary>
        public static void ResetApplicationServerURL(bool fromBootSequence = false, bool refreshCurrentConnectURLIndex = true)
        {
            applicationServerUrl = null;
            SaveDataManager.Instance.GetDebugSettingSaveData().ConnectURLIndex = -1;
            SaveDataManager.Instance.GetDebugSettingSaveData().Save();
#if UNITY_EDITOR && CYG_DEBUG
            if (refreshCurrentConnectURLIndex)
            {
                // デバッグ接続先が変更されたため更新
                ConnectURLUtil.RefreshCurrentConnectURLIndexFromSaveData();
            }

            // UnityEditorは固有設定を保存
            EditorUniqueDevServerSetting.SetDevServer(-1);
#endif
            if( fromBootSequence ) { return; }   // 起動初期化時はこの後のソフトウェアリセットやセーブデータ削除は走らせたくない

            OnChangeDebugApplicationURL();
        }

        public static void OnChangeDebugApplicationURL()
        {
            UIManager.Instance.LockAllCanvas();
            
            // Debug以外だとセーブデータが共有なので、接続先を切り替える時にデータを削除
#if !CYG_DEBUG
            // 行き来する必要があるようなときは2台使うべし
            SaveDataManager.RemoveSavedata();
#endif
                
            // ここから先はどうしようもないのでタイトルに戻す    
            GameSystem.Instance.SoftwareReset();
        }
#endif
            
#if !CYG_PRODUCT
        public static string GetDevServerHostName()
        {
            // デバッグ機能で接続先を切り替えていることがある
            // 接続先を切り替えている場合はURLからホスト名を切り取る
            string targetURL = APPLICATION_SERVER_URL;

#if CYG_DEBUG            
            var debugSave = SaveDataManager.Instance.GetDebugSettingSaveData();
            if (debugSave != null && debugSave.ConnectURLIndex >= 0)
            {
                var connectURLIndex = debugSave.ConnectURLIndex;
    #if UNITY_EDITOR && CYG_DEBUG
                // UnityEditorは固有設定を保存
                EditorUniqueDevServerSetting.OverWriterEditorDevServer(ref connectURLIndex);
    #endif
                targetURL = _connectDict[(GameDefine.DebugServer) connectURLIndex];
            }
#endif
            // いくつかの例外
            if (targetURL.Contains("localhost"))
            {
                return "localhost";
            } 
            else if (targetURL.Contains("gal-dev11-copy"))
            {
                return "dev11";
            }
            
            // 例外以外はSERVER_URLから取り出すことができる
            targetURL = targetURL.Replace(API_PROTOCOL, "");
            var hyphenIndex = targetURL.IndexOf("-");
            return targetURL.Substring(0, hyphenIndex);
            
        }
#endif        

#endregion

        #region リソースサーバー定義

#if TEST_RUNNER_IN_JENKINS
        // stg向けチェック時Macのアセットが存在しないのでWindowsのアセバンを使う
        public const string PLATFORM = "Windows";
#elif UNITY_EDITOR_WIN
        public const string PLATFORM = "Windows";
#elif UNITY_EDITOR_OSX
        // Macのアセバンをビルドしなくなったので代わりにiOSのアセバンを使う
        public const string PLATFORM = "iOS";
#elif UNITY_ANDROID
        public const string PLATFORM = "Android";
#elif UNITY_IOS
        public const string PLATFORM = "iOS";
#elif UNITY_STANDALONE_WIN
        public const string PLATFORM = "Windows";
#elif UNITY_STANDALONE_OSX
        public const string PLATFORM = "Mac";
#elif UNITY_STANDALONE_LINUX
        public const string PLATFORM = "Linux";
#endif

        public const string RESOURCE_PROTOCOL = "https://";

        // ------------       
        // サーバURL定義
        // ビルド時にplayer settingsの内容を見て選択されます.
        // ------------       
        // アプリ設定（シミュレータ以外）
#if SERVER_PRODUCTION || SERVER_PRODUCTION_TEST  // 本番環境は本番用アカマイ
        private const string RESOURCE_SERVER_URL = RESOURCE_PROTOCOL + "xxxxxxxxxx/dl/";

        public static string ResourceServerUrlForBundle { get { return RESOURCE_SERVER_URL; } }
        public static string ResourceServerURLForManifest { get { return RESOURCE_SERVER_URL; } }
        public static string ResourceServerURLForMaster { get { return RESOURCE_SERVER_URL; } }
#elif SERVER_STAGING_1 || SERVER_STAGING_2 || SERVER_STAGING_11 || SERVER_STAGING_21 || SERVER_STAGING_31 || SERVER_STAGING_41 // ステージングは開発用アカマイ
        private const string RESOURCE_SERVER_URL = RESOURCE_PROTOCOL + "xxxxxxxxxx/dl/";

        public static string ResourceServerUrlForBundle { get { return RESOURCE_SERVER_URL; } }
        public static string ResourceServerURLForManifest { get { return RESOURCE_SERVER_URL; } }
        public static string ResourceServerURLForMaster { get { return RESOURCE_SERVER_URL; } }            
#else
        // とりあえずdev11

        // 上書きされない各アセット群はキャッシュが有効なcloudfront経由、マニフェストやマスターはdev11に直アクセスしたい
        private const string RESOURCE_SERVER_URL_CACHED = RESOURCE_PROTOCOL + "xxxxxxxxxx/dl/vertical/";        
        
        private const string RESOURCE_SERVER_URL = RESOURCE_PROTOCOL + "xxxxxxxxxx/dl/vertical/";

        public static string ResourceServerUrlForBundle { get { return RESOURCE_SERVER_URL_CACHED; } }
        public static string ResourceServerURLForManifest { get { return RESOURCE_SERVER_URL; } }
        public static string ResourceServerURLForMaster { get { return RESOURCE_SERVER_URL; } }
#endif

        // ダウンロード場面定義    
        public enum RESOURCE_DOWNLOAD_SITUATION
        {
            TITLE,
            INGAME
        };
        
        // 通信レスポンスでのリソースサーバーURL設定
        // アセット, マスター
        private static CodeStage.AntiCheat.ObscuredTypes.ObscuredString _receivedResourceServerForInTitle = "";
        private static CodeStage.AntiCheat.ObscuredTypes.ObscuredString _receivedResourceServerForInGame = "";
        // マニフェスト
        private static CodeStage.AntiCheat.ObscuredTypes.ObscuredString _receivedResourceServerForManifest = "";

        public static void SetReceivedResourceServerURL(ServerList responseServer)
        {
            
            if (string.IsNullOrEmpty(responseServer.resource_server_ingame) ||
                string.IsNullOrEmpty(responseServer.resource_server_login)) 
            {
                Debug.LogWarning("SeverListが設定されていません。クライアント規定のサーバーに接続します。");
                return;
            }

            // タイトル～ログインDLまでのドメイン
            if (!string.IsNullOrEmpty(responseServer.resource_server_login))
            {
                _receivedResourceServerForInTitle = responseServer.resource_server_login;
                _receivedResourceServerForManifest = responseServer.resource_server_login;
            }

            // ゲーム本編以降のDLドメイン
            if (!string.IsNullOrEmpty(responseServer.resource_server_ingame))
            {
                _receivedResourceServerForInGame = responseServer.resource_server_ingame;
            }
            
#if CYG_DEBUG
            // 開発用サーバーでキャッシュ付きURLが指定されている場合は、そちらのURLを優先使用する                
            if (!string.IsNullOrEmpty(responseServer.resource_server_cf))
            {
                _receivedResourceServerForInTitle = responseServer.resource_server_cf;
                _receivedResourceServerForInGame = responseServer.resource_server_cf;
            }
#endif            
        }
        
        public static void ClearReceivedResourceServerURL()
        {
            _receivedResourceServerForInTitle = "";
            _receivedResourceServerForInGame = "";
            _receivedResourceServerForManifest = "";
        }
        
        /// <summary>
        /// アセバンURL
        /// </summary>
        /// <param name="resourceVersion"></param>
        public static string GetBundleUrl(RESOURCE_DOWNLOAD_SITUATION situation = RESOURCE_DOWNLOAD_SITUATION.INGAME)
        {
            //ローカルテストの場合はリソースバージョン見ないようにする
#if ASSETBUNDLE_LOCAL
            // Local時はサーバーからダウンロードしないため、urlは使用しない
            return "";
#else
            string situationURL = (situation == RESOURCE_DOWNLOAD_SITUATION.TITLE)
                    ? _receivedResourceServerForInTitle
                    : _receivedResourceServerForInGame;
                
            if (!string.IsNullOrEmpty(situationURL))
            {
                return RESOURCE_PROTOCOL + situationURL + "resources/" + PLATFORM + "/assetbundles/";
            }
            else
            {
                return ResourceServerUrlForBundle + "resources/" + PLATFORM + "/assetbundles/";
            }
#endif
        }

        public static string GetGenericUrl(RESOURCE_DOWNLOAD_SITUATION situation = RESOURCE_DOWNLOAD_SITUATION.INGAME)
        {
            string situationURL = (situation == RESOURCE_DOWNLOAD_SITUATION.TITLE)
                    ? _receivedResourceServerForInTitle
                    : _receivedResourceServerForInGame;
                
            if (!string.IsNullOrEmpty(situationURL))
            {
                return RESOURCE_PROTOCOL + situationURL + "resources/Generic/";
            }
            else
            {
                return ResourceServerUrlForBundle + "resources/Generic/";
            }
        }

        public static string GetManifestUrl()
        {
            if (!string.IsNullOrEmpty(_receivedResourceServerForManifest))
            {
                return RESOURCE_PROTOCOL + _receivedResourceServerForManifest + "resources/Manifest/";
            }
            else
            {
                return ResourceServerUrlForBundle + "resources/Manifest/";
            }
        }

        public static string GetDirectRootManifestUrl(string resourceVersion)
        {
#if ASSETBUNDLE_LOCAL
            // Local時はサーバーからダウンロードしないため、urlは使用しない
            return "";
#else
            if (!string.IsNullOrEmpty(_receivedResourceServerForManifest))
            {
                return RESOURCE_PROTOCOL + _receivedResourceServerForManifest + resourceVersion + "/manifests/manifestdat/";
            }
            else
            {
                return ResourceServerURLForManifest + resourceVersion + "/manifests/manifestdat/";
            }
#endif
        }

        #endregion

        #region リソースバージョン
        // release環境に関してはアップデートに応じて上げていく //
        // develop環境に関しては1.0.0と00000000で統一（developでの動作を担保するため変更はNG） //

        //================================================================================================//
        // こので定義するリソースバージョンはチュートリアルに必要なリソースバージョン。                   //
        // それ以外はサーバー管理です。                                                                   //
        // 申請出す時の最新バージョンにバージョンを書き換える                                             //
        //================================================================================================//

        private const string PRODUCT_DEFAULT_RESOURCE_VERSION = "10015700";

#if SERVER_PRODUCTION || SERVER_PRODUCTION_TEST || SERVER_STAGING_1 || SERVER_STAGING_2 || SERVER_STAGING_11 || SERVER_STAGING_21 || SERVER_STAGING_31 || SERVER_STAGING_41// 本番とstgは同一 //
        public const string _defaultResourceVersionName = PRODUCT_DEFAULT_RESOURCE_VERSION;
#elif SERVER_DEV101         // develop開発環境バージョン
        public const string _defaultResourceVersionName = "00000000";
#else // 上記以外は開発環境用共通設定 //
        public const string _defaultResourceVersionName = "00000000";
#endif

        public const string MinimumResourceVersion = "00000000";

#if UNITY_EDITOR
        // returnする値を変更することで、使用するリソースバージョンを強制的に切り替えます。
        public static string GetForceUseResourceVersion()
        {
            //return "********";
            // ※※※ 変更した状態でコミットしてしまうと、全体に影響がでるので注意してください。 ※※※
            return string.Empty;
        }
#endif

        #endregion

        #region 外部URL
        public const string DMM_POINT_CHARGE_URL = "https://point.dmm.com/choice/pay";
        public const string GOOGLE_PLAY_SUBSCRIPTIONS_URL = "https://play.google.com/store/account/subscriptions";
        #endregion 外部URL
    }
}
