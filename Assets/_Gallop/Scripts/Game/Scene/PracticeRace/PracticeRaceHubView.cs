using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using static Gallop.StaticVariableDefine.PracticeRace.PracticeRaceHubViewController;
using static Gallop.StaticVariableDefine.PracticeRace.PartsPracticeRaceCharaMessage;

namespace Gallop
{
    /// <summary>
    /// 練習HubView
    /// </summary>
    public class PracticeRaceHubView : ViewBase
    {
    }

    /// <summary>
    /// 練習HubViewController
    /// </summary>
    public class PracticeRaceHubViewController : HubViewControllerBase
    {
        #region 定数

        /// <summary> 記者の手帳の小物ID </summary>
        private const int REPORTER_PROP_ID_MEMO = 104201;
        /// <summary> 記者のペンの小物ID </summary>
        private const int REPORTER_PROP_ID_PEN = 109100;

        #endregion

        #region ViewInfo

        /// <summary>
        /// ViewInfo
        /// </summary>
        public class ViewInfo : HubViewInfo
        {
            /// <summary> トップViewInfo </summary>
            public PracticeRaceTopViewController.ViewInfo TopViewInfo { get; set; } = null;

            /// <summary> 待機ルームViewInfo </summary>
            public PracticeRaceCharacterEntryViewController.ViewInfo CharacterEntryViewInfo { get; set; } = null;
        }

        #endregion

        #region Member

        private List<CharacterBgPropController> _reporterPropList = new List<CharacterBgPropController>();

        #endregion

        #region HubViewControllerBase

        /// <summary>
        /// 子ビュー配列取得
        /// </summary>
        public override SceneDefine.ViewId[] GetChildViewIdArray()
        {
            return VIEW_ID_ARRAY;
        }

        /// <summary>
        /// 子ビューに渡すViewInfo選定
        /// </summary>
        public override IViewInfo GetChildViewInfo(SceneDefine.ViewId viewId, IViewInfo viewInfo)
        {
            var hubViewInfo = viewInfo as ViewInfo;

            if (hubViewInfo != null)
            {
                switch (viewId)
                {
                    case SceneDefine.ViewId.PracticeRaceTop:
                        return hubViewInfo.TopViewInfo;
                    case SceneDefine.ViewId.PracticeRaceCharacterEntry:
                        return hubViewInfo.CharacterEntryViewInfo;
                }
            }

            return viewInfo;
        }

        #endregion

        #region ViewControllerBase

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            ExhibitionRaceUtil.RegisterDownloadRaceSelect(register, RaceDefine.RaceGroup.Practice);
            PartsPracticeRaceCharaMessage.RegisterDownload(register);

            register.RegisterPath(ResourcePath.GetCharaPropPath(REPORTER_PROP_ID_MEMO));
            register.RegisterPath(ResourcePath.GetCharaPropPath(REPORTER_PROP_ID_PEN));

            const int CHARA_SUB_ID = 0;
            var systemTextList = MasterCharacterSystemText.GetByTriggerList(GameDefine.REPORTER_CHARA_ID, TRIGGER_LIST);
            var motionList = CharacterBgPropController.GetMotionPathList(GameDefine.REPORTER_CHARA_ID, CHARA_SUB_ID, systemTextList);
            // デフォルトでidleを再生
            motionList.Add(ResourcePath.GetEventCharaPropCharaAnimationPath(GameDefine.REPORTER_CHARA_ID, CHARA_SUB_ID, SimpleModelController.Define.IDLE_BODY_MOTION, ResourcePath.MOTION_START_STATE_SUFFIX, false));
            motionList.Add(ResourcePath.GetEventCharaPropCharaAnimationPath(GameDefine.REPORTER_CHARA_ID, CHARA_SUB_ID, SimpleModelController.Define.IDLE_BODY_MOTION, ResourcePath.MOTION_LOOP_STATE_SUFFIX, false));
            register.RegisterPath(motionList);

            register.RegisterPath(ResourcePath.HOME_PROP_ANIMATOR_PATH);

            base.RegisterDownload(register);
        }
        
        public override IEnumerator InitializeView()
        {
            // 機能解放判定(練習外部から来たとき用)
            GameDefine.UpdateEnableExhibitionRaceAddCondition();
            yield return base.InitializeView();
        }

        /// <summary>
        /// 終了処理(NowLoading表示完了後)
        /// </summary>
        /// <returns></returns>
        public override IEnumerator FinalizeView()
        {
            RaceManager.DestroyRaceInfo();
            _reporterPropList.Clear();// 小物の破棄はモデルがやる
            return base.FinalizeView();
        }

        #endregion

        #region Method

        /// <summary>
        /// 記者に小物を持たせる
        /// </summary>
        /// <param name="modelController"></param>
        public void SetupReporterProp(SimpleModelController modelController)
        {
            _reporterPropList.Clear();
            _reporterPropList.Add(CreatePropController(modelController, REPORTER_PROP_ID_MEMO, CharaNodeName.Hand_Attach_L));
            _reporterPropList.Add(CreatePropController(modelController, REPORTER_PROP_ID_PEN, CharaNodeName.Hand_Attach_R));

            modelController.SetCallbackOnPlayMotion(motionSet =>
            {
                foreach (var prop in _reporterPropList)
                {
                    if (modelController == null || prop == null)
                    {
                        continue;
                    }

                    //今喋っているものと同じモーションだったらPropのアニメーションを行う(Idleも再生)
                    if (modelController.CurrentSystemText != null && modelController.CurrentSystemText.MotionSet == motionSet.Id
                        || motionSet.BodyMotion == SimpleModelController.Define.IDLE_BODY_MOTION)
                    {
                        prop.PlayMotion(motionSet);
                    }
                }
            });
        }

        /// <summary>
        /// 小物IDから小物を追加
        /// </summary>
        /// <param name="modelController"></param>
        /// <param name="propId"></param>
        /// <param name="boneName"></param>
        private CharacterBgPropController CreatePropController(SimpleModelController modelController, int propId, string boneName)
        {
            CharaPropController.CreateContext context;
            context._propId = propId;
            context._charaId = modelController.GetCharaID();
            context._charaSubId = modelController.GetCharaSubID();
            context._personalityType = (int)modelController.GetDefaultPersonalityType();
            context._useSceneType = CharaPropController.UseSceneType.Home;
            context.LoadHash = modelController.GetBuildInfo().LoadHashKey;
            context.OtherCharaNodeTransformFindAction = modelController.TryFindTransform;
            context._controller = null;

            var propPath = ResourcePath.GetCharaPropPath(propId);
            var resource = ResourceManager.LoadOnView<GameObject>(propPath, SceneDefine.ViewId.PracticeRaceHub);
            var propController = GameObject.Instantiate(resource).AddComponent<CharacterBgPropController>();

            //アニメーターはホームと同じものを使っておく
            var baseAnimator = ResourceManager.LoadOnView<RuntimeAnimatorController>(ResourcePath.HOME_PROP_ANIMATOR_PATH, SceneDefine.ViewId.PracticeRaceHub);
            context._controller = baseAnimator;
            context.IsDollType = false;

            //小物初期化
            propController.SetLoadResourceHash((ResourceManager.ResourceHash)ResourceManager.GetViewLoadHash(SceneDefine.ViewId.PracticeRaceHub));
            propController.AttachModelController(modelController, boneName);
            propController.Create(ref context);
            propController.SetDefaultMotion(SimpleModelController.Define.IDLE_BODY_MOTION, ResourcePath.MOTION_LOOP_STATE_SUFFIX);

            return propController;
        }

        #endregion
    }
}