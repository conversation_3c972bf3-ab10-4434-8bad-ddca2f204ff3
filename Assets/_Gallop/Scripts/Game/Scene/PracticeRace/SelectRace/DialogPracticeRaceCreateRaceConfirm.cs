using System;
using UnityEngine;

namespace Gallop
{
    public class DialogPracticeRaceCreateRaceConfirm : DialogInnerBase
    {
        #region SerializeField

        /// <summary> 設定UI </summary>
        [SerializeField]
        private PartsPracticeRaceRaceInfoAndSetting _infoAndSettingUI = null;

        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを表示
        /// </summary>
        /// <param name="raceSetting"></param>
        /// <param name="onDecide"></param>
        public static void Open(ExhibitionRaceRaceSettingInfo raceSetting, Action onDecide)
        {
            var component = LoadAndInstantiatePrefab<DialogPracticeRaceCreateRaceConfirm>(ResourcePath.DIALOG_PRACTICE_RACE_CREATE_RACE_CONFIRM);
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.PracticeRace400109.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = _ => onDecide?.Invoke();
            component.Setup(raceSetting);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="raceSetting"></param>
        private void Setup(ExhibitionRaceRaceSettingInfo raceSetting)
        {
            _infoAndSettingUI.SetupNoButton(raceSetting);
        }

        #endregion
    }
}
