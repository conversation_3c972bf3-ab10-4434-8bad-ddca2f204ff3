using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 練習：一括走法選択ダイアログ
    /// </summary>
    public class DialogPracticeRaceEntryRunningStyleSelect : DialogInnerBase
    {
        #region SerializeField

        [SerializeField]
        private PartsPracticeRaceEntryCharaRunningStyleListItem _itemBase = null;

        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Member

        /// <summary> 編成情報 </summary>
        private List<ExhibitionRaceEntryCharaInfo> _entryCharaInfoList;

        /// <summary> スクロールアイテムのリスト </summary>
        List<PartsPracticeRaceEntryCharaRunningStyleListItem> _itemList = new List<PartsPracticeRaceEntryCharaRunningStyleListItem>();

        /// <summary> 練習パートナーやフォロワーの変更があったか </summary>
        private bool _isCharaListChanged = false;

        /// <summary> キャラ選択の種類 </summary>
        private PracticeRaceCharacterSelectViewController.SelectType _selectType;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを表示
        /// </summary>
        /// <param name="entryCharaInfoList"></param>
        /// <param name="onDecide"></param>
        /// <param name="selectType">練習パートナー削除用のレースの種類</param>
        /// <param name="onCancel"></param>
        public static void Open(
            List<ExhibitionRaceEntryCharaInfo> entryCharaInfoList,
            Action<List<ExhibitionRaceEntryCharaInfo>> onDecide,
            PracticeRaceCharacterSelectViewController.SelectType selectType = PracticeRaceCharacterSelectViewController.SelectType.DeckEdit,
            Action<bool> onCancel = null
            )
        {
            var component = LoadAndInstantiatePrefab<DialogPracticeRaceEntryRunningStyleSelect>(ResourcePath.DIALOG_PRACTICE_RACE_ENTRY_RUNNING_STYLE_SELECT);
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.Race0122.Text();
            dialogData.AutoClose = false;
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = d =>
            {
                onCancel?.Invoke(component._isCharaListChanged);
                d.Close();
            };
            dialogData.RightButtonText = TextId.Common0023.Text();
            dialogData.RightButtonCallBack = _ => component.OnTapDecideButton(onDecide);
            dialogData.FooterText = TextId.PracticeRace400132.Text();

            DialogManager.PushDialog(dialogData);
            component.Setup(selectType, entryCharaInfoList);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="selectType"></param>
        /// <param name="entryCharaInfoList"></param>
        private void Setup(PracticeRaceCharacterSelectViewController.SelectType selectType, List<ExhibitionRaceEntryCharaInfo> entryCharaInfoList)
        {
            _selectType = selectType;
            _entryCharaInfoList = entryCharaInfoList;

            CreateScrollList();
        }

        /// <summary>
        /// リスト作成
        /// </summary>
        private void CreateScrollList()
        {
            var characterList = _entryCharaInfoList.Where(i => !i.IsNone).ToList();
            var useMainLabel = _entryCharaInfoList.Count > ServerDefine.RoomMatchDeckEntryNumMax;
            UIUtil.CreateScrollItem(_itemBase, _itemList, characterList, (item, info) =>
            {
                item.Setup(
                    info,
                    useMainLabel,
                    onOpenCharaDetail: () => TempData.Instance.PracticeRaceData.IsChangeRentalList = false,
                    onCloseCharaDetail: () =>
                    {
                        if (TempData.Instance.PracticeRaceData.IsChangeRentalList)
                        {
                            _isCharaListChanged = true;
                            ExhibitionRaceEntryCharaInfo.RemoveEntryCharaIfNone(_entryCharaInfoList, _selectType == PracticeRaceCharacterSelectViewController.SelectType.Race);

                            // 今のスクロールを消す
                            var listCount = _itemList.Count;
                            for (int i = 0; i < listCount; i++)
                            {
                                Destroy(_itemList[i].gameObject);
                            }
                            _itemList.Clear();

                            CreateScrollList();
                        }
                        TempData.Instance.PracticeRaceData.IsChangeRentalList = false;
                    }
                    );
            });
        }

        /// <summary>
        /// 決定ボタン押下
        /// </summary>
        /// <param name="onDecide"></param>
        private void OnTapDecideButton(Action<List<ExhibitionRaceEntryCharaInfo>> onDecide)
        {
            onDecide?.Invoke(_entryCharaInfoList);
        }

        #endregion
    }
}

