using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Gallop.CutIn;
using Gallop.CutIn.Cutt;
using Gallop.RenderPipeline;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using CameraData = Gallop.RenderPipeline.CameraData;

namespace Gallop
{
    [AddComponentMenu("")]
    public class GachaMainView : ViewBase
    {
        public const int FLASH_CENTER = 0;
        public const int FLASH_CHARA_NAME = 1;
        public const int SUPPORT_NEW_ICON = 2;

        #region SerializeField, Property

        [field: SerializeField, RenameField]
        public ButtonCommon ButtonTap { get; private set; } = null;

        [field: SerializeField, RenameField]
        public ButtonCommon ButtonSkip { get; private set; } = null;

        [field: SerializeField, RenameField]
        public Transform[] FlashRootArray { get; private set; } = null;

        /// <summary>
        /// ガチャ排出リザルト
        /// </summary>
        [field: SerializeField, RenameField]
        public PartsGachaResult PartsGachaResult { get; private set; } = null;

        /// <summary>
        /// 写真のズーム位置
        /// </summary>
        [field: SerializeField, RenameField]
        public Vector3 Support2ZoomOutPos = new Vector3(-0.1281875f, 0.6359044f, -0.0380231f);

        /// <summary>
        /// 演出用アニメーションカーブ
        /// </summary>
        [field: SerializeField, RenameField]
        public AnimationCurve AlbumSlide = null;
        [field: SerializeField, RenameField]
        public AnimationCurve AlbumOpen = null;
        [field: SerializeField, RenameField]
        public AnimationCurve AlbumClose = null;
        [field: SerializeField, RenameField]
        public AnimationCurve SupportViewCamera = null;
        [field: SerializeField, RenameField]
        public AnimationCurve SupportReturnPage = null;
        [field: SerializeField, RenameField]
        public AnimationCurve SupportZoomOut = null;
        [field: SerializeField, RenameField]
        public AnimationCurve SupportSetupCamera = null;

        #endregion
    }

    /// <summary>
    /// ガチャ演出View
    /// </summary>
    public class GachaMainViewController : ViewControllerBase<GachaMainView>
    {
        #region 定数
        private const int CUTIN_INFO_LIST_CAPACITY = 16;
        private const string ALBUM_START_SLIDE = "AlbumStartSlide";
        private const string ALBUM_START_PAGE_OPEN = "AlbumStartPageOpen";
        private const string ALBUM_START_CLOSE = "AlbumStartClose";
        private const string ALBUM_PAGE_UPDATE_RARITY= "AlbumPageUpdateRarity";
        private const string ALBUM_PAGE_UPDATE_EFFECT = "AlbumPageUpdateEffect";
        private const string SUPPORT_START_VIEW_CAMERA = "PhotoStartViewCamera";
        private const string SUPPORT_START_RETURN_PAGE = "PhotoStartReturenPage";
        private const string SUPPORT_SET_LAYER_BG = "PhotoSetLayerBG";
        private const string SUPPORT_SET_LAYER_3DUI = "PhotoSetLayer3DUI";
        private const string SUPPORT_CARD_FLASH_IN = "SupportCardFlashIn";
        private const string SUPPORT_CARD_FLASH_OUT = "SupportCardFlashOut";
        public const string CHARA_NAME_FLASH_IN = "CharaNameFlashIn";
        private const string CHARA_NAME_FLASH_OUT = "CharaNameFlashOut";
        private const string RESULT_START = "ResultStart";
        private const string EFFECT_PLAY = "GachaEffectPlay";
        private const string EFFECT_PLAY_LOOP = "GachaEffectPlayLoop";
        private const string EFFECT_CLEAR = "GachaEffectClear";
        private const string SUPPORT_CARD_FRAME_ON = "PhotoFrameOn";
        private const string SUPPORT_CARD_FRAME_OFF = "PhotoFrameOff";
        //URP:置き換え対応
        //private const float FINALIZE_CAMERA_DEPTH = 15f;
        public const float FINALIZE_CAMERA_DEPTH = 15f;
        private const float CARD_BG_CAMERA_DEPTH = 16f;
        private const float CARD_CAMERA_DEPTH = 17f;
        private const float TAP_IGNORE_TIME = 0.2f;
        private const float CARD_CAMERA_RESOLUTION_3D_RATE = 1559f / 720f; // この時の見た目をベースにしたいらしい。
        private const float CARD_CAMERA_DEFAULT_FOV = 26.99147f;
        private const int SETUP_SUBKEY0 = 0;
        private const int SETUP_SUBKEY1 = 1;
        #endregion 定数

        #region 変数

        /// <summary>
        /// 戻り先情報。
        /// </summary>
        private GachaMainViewInfo _viewInfo = null;
        /// <summary>
        /// 演出スキップしたか？
        /// </summary>
        private bool _isSkipAct = false;
        /// <summary>
        /// 次の演出へ、
        /// </summary>
        private bool _isNextCut = false;
        /// <summary>
        /// ChangeViewを呼んだか。
        /// </summary>
        private bool _isRequestedChangeView = false;
        /// <summary>
        /// GameObjectの親
        /// </summary>
        private GameObject _rootObj = null;
        /// <summary>
        /// ガチャ演出の設定
        /// </summary>
        private GachaCutContext _context = new GachaCutContext();
        /// <summary>
        /// 再生する演出のリスト。
        /// </summary>
        private List<GachaCutBase> _cutList = new List<GachaCutBase>(CUTIN_INFO_LIST_CAPACITY);
        private int _playingIndex = 0;
        /// <summary>
        /// サウンドをロードしたキャラIdリスト
        /// </summary>
        private List<int> _soundLoadCharaIdsGacha = new List<int>();
        private List<string> _soundLoadSeCueSheetList = new List<string>();
        /// <summary>
        /// サポカ用カメラ
        /// </summary>
        private Camera _cardBGCamera = null;
        private Camera _cardCamera = null;
        private Transform _cardCameraTrs = null;
        /// <summary>
        /// GameCanvasをLockしたかどうか
        /// </summary>
        private bool _isLockGameCanvas = false;

        /// <summary>
        /// サポカ時UITextureを描画/書き戻しするカスタムレンダーパス管理用のID
        /// Androidのみ使用
        /// </summary>
        private int _copyAndRestoreCrpId = CameraData.INVALID_CRP_RESERVE_ID;

        #endregion 変数

        #region ViewBase
        /// <summary>
        /// ダウンロード対象リソースの登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            CreateCutInInfoList();

            // 背景関係の登録。
            GachaBGController.RegisterDownload(register, _context);
            // 各カット
            for (int index = 0, listCount = _cutList.Count; index < listCount; ++index)
            {
                _cutList[index].RegisterDownload(register);
            }
            // Flash
            if (_context.IsContainNew)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetNewIconFlashPath());
            }
            if (_context.IsChara)
            {
                RegisterDownloadForChara(register);
            }
            else
            {
                RegisterDownloadForSupport(register);
            }
            AudioManager.Instance.RegisterDownloadByAudioId(register, _context.GetBGMAudioId());
            PartsGachaResult.RegisterDownload(register);

            var groupIdList = TempData.Instance.GachaData.AvailableGachaGroupInfo.GetAvailableGroupIdList();
            GachaResourceRegister.RegisterPathForTop(register, TempData.Instance.GachaData.GachaList, groupIdList);
        }
        /// <summary>
        /// ダウンロード対象リソースの登録(キャラ)
        /// </summary>
        private void RegisterDownloadForChara(DownloadPathRegister register)
        {
            // サウンド
            _soundLoadCharaIdsGacha.Clear();
            _soundLoadSeCueSheetList.Clear();
            if (_context.IsContainR3)
            {
                _soundLoadSeCueSheetList.Add(ResourcePath.RACE_SE_CUESHEET_NAME);
                for (int index = 0, count = _context.CardList.Count; index < count; ++index)
                {
                    if (_context.CardList[index].IsR3Rarity())
                    {
                        var charaId = _context.CardList[index].CardData.CharaId;
                        if (!_soundLoadCharaIdsGacha.Contains(charaId))
                        {
                            _soundLoadCharaIdsGacha.Add(charaId);
                        }
                    }
                }
                AudioManager.Instance.RegisterDownloadByTriggerAndCharaIds(register , _soundLoadCharaIdsGacha ,new List<CharacterSystemLotteryTrigger>()
                {
                    CharacterSystemLotteryTrigger.OtherGachaGet,
                } , true);
            }
            _soundLoadSeCueSheetList.Add(ResourcePath.GACHA_SE_CUE_SHEET_000);
            AudioManager.Instance.RegisterDownloadByCueSheets(register, _soundLoadSeCueSheetList, AudioManager.SubFolder.Se);
            // エフェクト
            if (_context.IsContainR3)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetGachaCharaCommentUIAnimPath());
            }
            // Flash
            register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaFlashActionSetter.CHARA_PIECE_FILE_NAME);
            if (_context.IsContainR1)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInR1.FLASH_NAME);
            }
            if (_context.IsContainR2)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInR2.FLASH_NAME);
            }
            if (_context.IsContainR3)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_ROOT + GachaCutInR3_1.FLASH_NAME);
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInR3SkillCutIn.FLASH_NAME);
            }
            register.RegisterPathWithoutInfo(ResourcePath.GACHA_TAT_ROOT + GachaFlashCharaNamePlate.TAT_NAME);
            Model.Component.ShadowController.RegisterDownload(register);
        }
        /// <summary>
        /// ダウンロード対象リソースの登録(サポート)
        /// </summary>
        private void RegisterDownloadForSupport(DownloadPathRegister register)
        {
            // FlashCombine
            if (_context.IsContainR1)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInSupport1R.FLASH_NAME);
            }
            if (_context.IsContainR2)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInSupport1SR.FLASH_NAME);
            }
            if (_context.IsContainR3)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInSupport1SSR.FLASH_NAME);
            }
            
        }

        /// <summary>
        /// ビューの初期化
        /// </summary>
        public override IEnumerator InitializeView()
        {
            DirectionalLightManager.Instance.SetEnable(true);
            _isLockGameCanvas = false;
            _context.TapIgnoreTime = 0f;

            _viewInfo = GetViewInfo() as GachaMainViewInfo;
            CreateRootNode();
            SetupMenu();
            _context.BGCtrl.AlbumSlide = _view.AlbumSlide;
            _context.BGCtrl.AlbumOpen = _view.AlbumOpen;
            _context.BGCtrl.AlbumClose = _view.AlbumClose;
            _context.BGCtrl.SupportViewCamera = _view.SupportViewCamera;
            _context.BGCtrl.SupportReturnPage = _view.SupportReturnPage;
            _context.BGCtrl.SupportZoomOut = _view.SupportZoomOut;
            _context.BGCtrl.SupportSetupCamera = _view.SupportSetupCamera;
            _context.BGCtrl.Support2ZoomOutPos = _view.Support2ZoomOutPos;
            _context.BGCtrl.Initialize(_context);

            _context.CutInOwner = new CutInOwnerGacha(_context);
            InitializeImageEffect();

            _context.CutIn = new GachaCutInHelper();
            _context.CutIn.InitForGacha(_context.CutInOwner);

            if (_context.IsChara)
            {
                yield return InitializeViewForChara();
            }
            else
            {
                yield return InitializeViewForSupport();
            }

            if (_context.IsContainNew)
            {// Newアイコン
                _context.FlashHolder.CreateAdd(ResourceManager.LoadOnView<GameObject>(ResourcePath.GetNewIconFlashPath()));
            }

            _context.SetActiveGachaTapButton = (isActive) =>
            {
                _view.ButtonTap.SetActiveWithCheck(isActive);
            };
            _context.SetActiveSkipButton = (isActive) =>
            {
                _view.ButtonSkip.SetActiveWithCheck(isActive);
            };
            _context.StartResult = () =>
            {
                _view.PartsGachaResult.SetActiveWithCheck(true);
                _view.StartCoroutine(_view.PartsGachaResult.PlayInView());
            };
            _view.PartsGachaResult.OnOkAction = OnResultOk;

            _playingIndex = 0;
            _cutList[_playingIndex].Play();
            yield return _view.PartsGachaResult.InitializeView();
            _view.PartsGachaResult.SetActiveWithCheck(false);
        }

        private void MakeMob(int id,int zekkenNo)
        {
            const int MOB_ID = 8019;
            var data = new GachaCharaHolder.CreateData();
            data.HorseIndex = id;
            data.CardId = GameDefine.INVALID_CARD_ID;
            data.CharaId = ModelLoader.MOB_CHARA_ID;
            data.DressId = (int)ModelLoader.DressID.TrackSuit;

            var charaInfo = new EditableCharacterBuildInfo(data.CardId, data.CharaId, data.DressId, ModelLoader.ControllerType.CutIn, zekkenNo, MOB_ID);
            charaInfo.ZekkenColor = ModelLoader.ZekkenColor.DeepBlue;
            charaInfo.ZekkenFontColor = ModelLoader.ZekkenFontColor.Black;
            charaInfo.FrameColor = ModelLoader.TrackSuitColor.White;
            charaInfo.OverrideClothCategory = CySpringDataContainer.Category.Story;
            charaInfo.Rebuild();
            var charaObject = ModelLoader.CreateModel(charaInfo);
            var modelController = charaObject.GetComponent<ModelController>();
            modelController.transform.SetParent(_context.RootTrs);
            //最初は非表示化
            modelController.OwnerObject.SetActive(false);

            data.Model = modelController as CutInModelController;
            _context.CharaHolder.CharaList.Add(data);
        }

        /// <summary>
        /// ビューの初期化(キャラ)
        /// </summary>
        private IEnumerator InitializeViewForChara()
        {
            ModelLoader.LoadZekkenCompositeResource();

            int cardCount = _context.CardList.Count;
            if (cardCount > 1)
            {
                // この後10体キャラが作られるのでここで一旦次のフレームへ。
                yield return null;
            }
            // キャラ生成
            for (int index = 0; index < cardCount; ++index)
            {
                var info = _context.CardList[index];
                if (info.CardData == null)
                {
                    continue;
                }
                if (_context.CharaHolder.GetFromHorseIndex(info.Order) == null)
                {
                    _context.CharaHolder.CreateAdd(info.Order, info.CardData.Id, info.CardData.CharaId, info.RarityData.RaceDressId, ModelLoader.DEFAULT_HEAD_SUB_ID, _context.RootTrs, false, true);
                }
            }

            // モブ追加
            if (cardCount > 1)
            {
                yield return null;
            }
            for (int index = 0; index < cardCount; ++index)
            {
                MakeMob(GachaDefine.MOB_BEGIN_HORSEINDEX + index, index + 1);
            }

            //単発時に未使用箇所に割り当てられるモブ
            if(cardCount == 1)
            {
                MakeMob(GachaDefine.MOB_EXTRA_HORSEINDEX,0);
            }

            // カットのプレハブなどの用意
            int cutCount = _cutList.Count;
            for (int index = 0; index < cutCount; ++index)
            {
                yield return _cutList[index].InitializeView();
            }
            _context.CutIn.PreInstantiateForGacha();

            // サウンド
            if (_soundLoadCharaIdsGacha.Count > 0)
            {
                AudioManager.Instance.AddCueSheetByCharaIds(_soundLoadCharaIdsGacha, CharacterSystemTextGroupExtension.Scene.Gacha);
            }
            for (int index = 0, count = _soundLoadSeCueSheetList.Count; index < count; ++index)
            {
                AudioManager.Instance.AddCueSheet(_soundLoadSeCueSheetList[index], AudioManager.SubFolder.Se);
            }

            // 星３演出エフェクト
            if (_context.IsContainR3)
            {
                _context.CharaCommentEffectObj = GameObject.Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.GetGachaCharaCommentUIAnimPath()), _context.FlashHolder.RootArray[GachaMainView.FLASH_CENTER]);
                _context.CharaCommentEffectAnimator = _context.CharaCommentEffectObj.GetComponentInChildren<Animator>(true);
                _context.CharaCommentEffectObj.SetActive(false);
            }
            if (_context.IsContainR1)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInR1.FLASH_NAME);
                _context.FlashActionHolder.CreateAdd(prefab);
            }
            if (_context.IsContainR2)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInR2.FLASH_NAME);
                _context.FlashActionHolder.CreateAdd(prefab);
            }
            if (_context.IsContainR3)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInR3SkillCutIn.FLASH_NAME);
                _context.FlashActionHolder.CreateAdd(prefab);
            }
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaFlashActionSetter.CHARA_PIECE_FILE_NAME);
                _context.FlashActionHolder.CreateAdd(prefab, GachaFlashActionSetter.CHARA_PIECE_INSTANCE_0);
                _context.FlashActionHolder.CreateAdd(prefab, GachaFlashActionSetter.CHARA_PIECE_INSTANCE_1);
            }
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_TAT_ROOT + GachaFlashCharaNamePlate.TAT_NAME);
                _context.TatHolder.CreateAdd(prefab);
            }
            yield break;
        }

        //URP:置き換え対応
        private void OnFrameBufferToUI(out CopySurfacePass.CopyParamter parameter)
        {
            //UICameraへの転送はここで停止する
            _context.FrameBuffer.Enable = false;

            parameter = _context.FrameBuffer.FrameBufferToUI;
            parameter.SourceTexture = _context.FrameBuffer.ColorBuffer;
            parameter.IsEnable = (_context.FrameBuffer.ColorBuffer != null);
        }

        /// <summary>
        /// ビューの初期化(サポート)
        /// </summary>
        private IEnumerator InitializeViewForSupport()
        {
#if UNITY_ANDROID
            // 137314 低スぺMaliのサポカ背景絵崩れ問題回避用の対応
            SetupUiCameraCrp();
#endif
            // 専用のカメラを用意。
            var cardBgCameraObj = new GameObject();
#if UNITY_EDITOR || CYG_DEBUG
            cardBgCameraObj.name = "CardBGCamera";
#endif
            cardBgCameraObj.transform.SetParent(_context.RootTrs);
            _cardBGCamera = cardBgCameraObj.AddComponent<Camera>();
            _cardBGCamera.CopyFrom(UIManager.UICamera);
            _cardBGCamera.cullingMask = GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.LayerCardShader);
            _cardBGCamera.depth = CARD_BG_CAMERA_DEPTH;
            var cardCameraObj = new GameObject();
#if UNITY_EDITOR || CYG_DEBUG
            cardCameraObj.name = "CardCamera";
#endif
            _cardCamera = cardCameraObj.AddComponent<Camera>();
            _cardCameraTrs = cardCameraObj.transform;
            _cardCameraTrs.SetParent(_context.RootTrs);
            //URP:置き換え対応
            _cardCamera.allowMSAA = false;  //MSAA不要
            {
                //UITextureへの転送はCardBGCameraが行い、UICameraでは行わない
                var cameraData = _cardBGCamera.GetCameraData();
                cameraData.BeforeOpaqueParameter.FrameBufferToUI.ExecuteActionList = new List<CopySurfacePass.Parameter.ExecuteAction>(1);
                cameraData.BeforeOpaqueParameter.FrameBufferToUI.AddAction(OnFrameBufferToUI);
            }
            /*
            _context.FrameBuffer.FinalizeCamera.depth = FINALIZE_CAMERA_DEPTH;
            */
            _context.CardCamera = _cardCamera;

            // カットのプレハブなどの用意
            int cutCount = _cutList.Count;
            for (int index = 0; index < cutCount; ++index)
            {
                yield return _cutList[index].InitializeView();
            }
            _context.CutIn.PreInstantiateForGacha();

            if (_context.IsContainR1)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInSupport1R.FLASH_NAME);
                _context.FlashActionHolder.CreateAdd(prefab);
            }
            if (_context.IsContainR2)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInSupport1SR.FLASH_NAME);
                _context.FlashActionHolder.CreateAdd(prefab);
            }
            if (_context.IsContainR3)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.GACHA_FLASH_COMBINE_ROOT + GachaCutInSupport1SSR.FLASH_NAME);
                _context.FlashActionHolder.CreateAdd(prefab);
            }
            yield break;
        }
        
        /// <summary>
        /// UICamera用のカスタムレンダーパスを設定
        /// (GetCustomRenderParameterの実行はIEnumerator内では行えないため関数を挟む)
        /// </summary>
        private void SetupUiCameraCrp()
        {
            var uiCameraData = UIManager.UICamera.GetCameraData();
            
            // 137314 このCRP処理はAndroid向けの低スぺMali端末向けの不具合回避用の対応
            // なのでios/winでは実行しても特に意味はない。
            ref var crp = ref uiCameraData.GetCustomRenderParameter(out _copyAndRestoreCrpId);
            crp.IsEnable = true;
            crp.RenderPassEvent = RenderPassEvent.BeforeRenderingOpaques;
            crp.OnExecute = (CustomRenderPass pass, ScriptableRenderContext context, ref RenderingData data) =>
            {
                var uiToFrameBufferTex = UIManager.Instance.UITexture;
                var desc = uiToFrameBufferTex.descriptor;
                var tempTex = RenderTexture.GetTemporary(desc);
                
                var cmd = CommandBufferPool.Get("CopyAndRestore");
                
                // 全く持って無意味だが、一度バッファにコピーして書き戻すと不具合が改善する
                // バッファへのコピーだけや他テクスチャで同じことをやってSetRenderTargetでUITextureに戻すだけだと不具合が改善しない
                // またSetRenderTargetでBufferActionを明示的にLoad/Storeにしても問題は改善しない
                cmd.Blit(uiToFrameBufferTex, tempTex);
                cmd.Blit(tempTex, uiToFrameBufferTex);
                
                context.ExecuteCommandBuffer(cmd);
                RenderTexture.ReleaseTemporary(tempTex);
                CommandBufferPool.Release(cmd);
            };
            crp.SetPassName("CopyAndRestoreForUIToFrameBuffer");
        }
        
        /// <summary>
        /// Rootノードの生成
        /// </summary>
        private void CreateRootNode()
        {
            _rootObj = new GameObject("GachaMainRot");
            _context.RootTrs = _rootObj.transform;
            var rootArray = GetView().FlashRootArray;
            _context.FlashHolder.RootArray = rootArray;
            _context.FlashActionHolder.RootArray = rootArray;
            _context.TatHolder.RootArray = rootArray;
        }
        /// <summary>
        /// メニュー部分設定
        /// </summary>
        private void SetupMenu()
        {
            _view.ButtonTap.onClick.RemoveAllListeners();
            _view.ButtonTap.onClick.AddListener(OnTap);
            _view.ButtonSkip.onClick.RemoveAllListeners();
            _view.ButtonSkip.onClick.AddListener(OnPushSkipButton);
        }

        /// <summary>
        /// レースのカメラがやるはずのイメージエフェクト処理を一部やる
        /// レースのカメラマネージャを持ってこられるなら話は変わる。
        /// </summary>
        private void InitializeImageEffect()
        {
            ReleaseFrameBuffer();

            _context.FrameBuffer = new GallopFrameBuffer();
            _context.FrameBuffer.Initialize();

            _context.CutInOwner.FrameBuffer = _context.FrameBuffer;
        }

        private void ReleaseFrameBuffer()
        {
            if (_context.FrameBuffer != null)
            {
                _context.FrameBuffer.Release();
                _context.FrameBuffer = null;
            }
        }

        /// <summary>
        /// 開始(NowLoadinが捌けた後PlayInViewの終了を待ってから実行される)
        /// </summary>
        public override void BeginView()
        {
            _context.PlayBGM();
        }

        /// <summary>
        /// Viewの後始末処理
        /// </summary>
        public override IEnumerator FinalizeView()
        {
            AudioManager.Instance.StopBgm();
            yield return _view.PartsGachaResult.FinalizeView();

#if UNITY_ANDROID
            if (!_context.IsChara)
            {
                FinalizeViewForSupport();
            }
#endif

            if (_context.CharaCommentEffectObj != null)
            {
                GameObject.Destroy(_context.CharaCommentEffectObj);
                _context.CharaCommentEffectObj = null;
            }
            _context.FlashHolder.FinalizeView();
            _context.FlashActionHolder.FinalizeView();
            _context.TatHolder.FinalizeView();
            for (int index = 0, count = _cutList.Count; index < count; ++index)
            {
                _cutList[index].FinalizeView();
            }
            _context.CutIn.CleanupPlaying();
            //クラッシュ回避のために1フレーム待つ
            yield return null;
            _context.CutIn.Cleanup();
            _context.BGCtrl.Delete();
            ReleaseFrameBuffer();
            ModelLoader.UnloadZekkenCompositeResource();
            if (_rootObj)
            {
                GameObject.Destroy(_rootObj);
                _rootObj = null;
            }
            if (_isLockGameCanvas)
            {
                UIManager.Instance.UnlockGameCanvas();
            }
            yield return base.FinalizeView();
        }

        /// <summary>
        /// サポートカードガチャでのみ実行する後処理
        /// </summary>
        private void FinalizeViewForSupport()
        {
            var uiCameraData = UIManager.UICamera.GetCameraData();
            uiCameraData.ReleaseCustomRenderParameter(ref _copyAndRestoreCrpId);
        }

        // ------
        //  Loading明け前用のUpdate関数
        // ------
        public override void UpdateViewBeforeLoadingOut()
        {
            base.UpdateViewBeforeLoadingOut();
            _cutList[_playingIndex].Update();
        }
        public override void LateUpdateViewBeforeLoadingOut()
        {
            base.LateUpdateViewBeforeLoadingOut();
            _cutList[_playingIndex].LateUpdate();
            LateUpdateCamera();
        }

        /// <summary>
        /// View更新。
        /// </summary>
        public override void UpdateView()
        {
            base.UpdateView();
            if (_context.TapIgnoreTime > 0f)
            {
                _context.TapIgnoreTime = Mathf.Max(0f, _context.TapIgnoreTime - Time.deltaTime);
            }
            if (_isRequestedChangeView && _playingIndex >= _cutList.Count)
            {
                // 最終カットがEndになっているため以降の処理不要
                return;
            }

            bool isEnd = false;
            if (_isSkipAct)
            {
                int count = _cutList.Count;
                _cutList[_playingIndex].OnEnd();
                ++_playingIndex;
                for (; _playingIndex < count; ++_playingIndex)
                {
                    if (!_cutList[_playingIndex].IsEnableSkip())
                    {
                        _context.CutIn.CleanupPlaying();
                        _cutList[_playingIndex].Play();
                        break;
                    }
                    else
                    {
                        _cutList[_playingIndex].OnEndFromSkip();
                    }
                }
                if (_playingIndex >= count)
                {
                    isEnd = true;
                }
            }
            else
            {
                _cutList[_playingIndex].Update();
                if (_cutList[_playingIndex].IsEnd() || _isNextCut)
                {
                    _cutList[_playingIndex].OnEnd();
                    ++_playingIndex;
                    if (_playingIndex < _cutList.Count)
                    {
                        _context.CutIn.CleanupPlaying();
                        _cutList[_playingIndex].IsTapSkipStart = _isNextCut;
                        _cutList[_playingIndex].Play();
                    }
                    else
                    {
                        isEnd = true;
                    }
                }
            }

#if CYG_DEBUG
            //強制スキップフラグがたってたら抜ける
            if ((_forceSkip || isEnd) && !_isRequestedChangeView)
#else
            if (isEnd && !_isRequestedChangeView)
#endif
            {
                //おしまい
                if (_context.IsChara && TempData.Instance.GachaResult.ExecutionResult.ExecutionType == GachaExecutionResult.Execution.Exchange)
                {
                    // カットの更新が終わるけどMonoBehaviourの更新は切れないので
                    // Animatorが更新されるとオフセットが効かなくなってしまうので更新を切っておく。
                    // 問題が起きたのはキャラ交換の★１，２のキャラなので一応交換に限定しておく。
                    _context.CutIn.TimelineController.MotionCamera.MyAnimator.IsEnable = false;
                }

#if !CYG_PRODUCT
                // デバコマ負荷計測
                // FPS計測終了（リリースビルドでも実行するためCYG_DEBUGで括らないこと）
                {
                    var perf = DebugPerfSystem.Instance;
                    if (perf.IsMeasurementDetailFPS)
                    {
                        perf.FinishMeasurePerformanceDetail();
                    }
                }
#endif // !CYG_PRODUCT

#if CYG_DEBUG
                if (GachaDirectSceneController.RequireReturn)
                {
                    SceneManager.GoToDirectScene(SceneDefine.SceneId.GachaDirect);
                }
                else
#endif
                {
                    // 報酬としてカードが存在する場合、戻る前にダイアログで報酬表示をする
                    if (MainStoryUtil.IsDisplayCardRewardDialog())
                    {
                        _view.ButtonSkip.SetActiveWithCheck(false);
                        MainStoryUtil.DirectFinishEpisodeWithReward();
                    }
                    else if (_viewInfo != null)
                    {
                        SceneManager.Instance.ChangeView(_viewInfo.NextViewId, _viewInfo.NextViewInfo);
                    }
                    else
                    {
                        SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub, new HomeViewInfo(HomeTopState.Gacha));
                    }
                }
                _isRequestedChangeView = true;
            }
            _isSkipAct = false;
            _isNextCut = false;
            _context.CutInOwner.IsJumpedFrame = false;
        }
        /// <summary>
        /// Viewの更新処理
        /// </summary>
        public override void LateUpdateView()
        {
            base.LateUpdateView();

            if (_playingIndex < _cutList.Count)
            {
                _cutList[_playingIndex].LateUpdate();
            }

            LateUpdateCamera();
        }
        /// <summary>
        /// カメラの更新。
        /// </summary>
        private void LateUpdateCamera()
        {
            if (_cardCamera != null && _context.CutIn.TimelineController != null)
            {
                Camera cutInCamera = _context.CutIn.TimelineController.GetCuttControlCamera();
                Transform cameraTrs = cutInCamera.transform;
                _cardCamera.CopyFrom(cutInCamera);
                _cardCamera.depth = CARD_CAMERA_DEPTH;
                _cardCamera.cullingMask = GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3DUI);
                _cardCamera.targetTexture = UIManager.Instance.UITexture;
                var resolution = GraphicSettings.Instance.GetVirtualResolution3D();
                float resolutionRate = (float) resolution.y / (float) resolution.x;
                _cardCamera.fieldOfView = CARD_CAMERA_DEFAULT_FOV * (resolutionRate / CARD_CAMERA_RESOLUTION_3D_RATE);
                _cardCameraTrs.transform.SetPositionAndRotation(cameraTrs.position, cameraTrs.rotation);
            }
        }
        #endregion ViewBase

        #region CutIn再生リスト作成
        /// <summary>
        /// CutIn再生リストの作成。
        /// </summary>
        private void CreateCutInInfoList()
        {
            _context = GachaCutContext.Create();
            if (_context.IsChara)
            {
                CreateCutInInfoListForChara();
            }
            else
            {
                CreateCutInInfoListForSupport();
            }
            _cutList[_cutList.Count - 1].IsLastCut = true;
        }
        /// <summary>
        /// CutIn再生リストの作成(キャラ)。
        /// </summary>
        private void CreateCutInInfoListForChara()
        {
            _cutList.Clear();
            var executionType = TempData.Instance.GachaResult.ExecutionResult.ExecutionType;
            bool isNormal = executionType == GachaExecutionResult.Execution.Normal;
            bool isExecution = executionType == GachaExecutionResult.Execution.Exchange;
            if (!isExecution)
            {
                _cutList.Add(new GachaCutInOpening(_context));

                if (executionType == GachaExecutionResult.Execution.TutorialSelect)
                {
                    // チュートリアル用5連ガチャ
                    _cutList.Add(new GachaCutInFiveCmn(_context));
                }
                else if (TempData.Instance.GachaResult.ResultSet.DrawCount == 1)
                {
                    _cutList.Add(new GachaCutInSingleCmn(_context));
                }
                else
                {
                    _cutList.Add(new GachaCutInCmn(_context));
                }
            }

            // キャラ別演出。
            var cardList = _context.CardList;
            for (int index = 0, count = cardList.Count; index < count; ++index)
            {
                var cardData = cardList[index].CardData;
                var cardRarityData = cardList[index].RarityData;
                if (cardList[index].IsR3Rarity())
                {// R3
                    if (!isExecution)
                    {
                        bool isStepUp = false;
                        // 昇格演出
                        if ((int)cardList[index].RarityUpType != (int)cardList[index].RarityType)
                        {
                            if (cardList[index].RarityUpType == GachaDefine.GachaRarityUpType_Door.StepUp)
                            {
                                //銀->金->虹の2段階演出
                                _cutList.Add(new GachaCutInStepUp(cardList[index].Order, GachaDefine.GachaRarityUpType_Door.Gold, false, _context, SETUP_SUBKEY0));
                                _cutList.Add(new GachaCutInStepUp(cardList[index].Order, GachaDefine.GachaRarityUpType_Door.Rainbow, true, _context, SETUP_SUBKEY1));  //２段階目はスキップ可能
                            }
                            else
                            {
                                _cutList.Add(new GachaCutInStepUp(cardList[index].Order, cardList[index].RarityUpType, false, _context, SETUP_SUBKEY0));
                            }
                            isStepUp = true;
                        }
                        // 最初に演出が入ってそのあとスキルカットイン、その後会話
                        if ((index == 0 || isStepUp))
                        {
                            _cutList.Add(new GachaCutInR3GateStart(cardList[index].Order, _context, isStepUp));
                        }
                    }
                    // セリフ表示 + スキルカットイン
                    _cutList.Add(new GachaCutInR3_1(cardList[index].Order, _context));
                    _cutList.Add(new GachaCutInR3SkillCutIn(cardList[index].Order, _context));
                }
                else if (cardList[index].IsR2Rarity())
                {// R2
                    if (!isExecution)
                    {
                        bool isStepUp = false;
                        if ((int)cardList[index].RarityUpType != (int)cardList[index].RarityType)
                        {
                            //昇格演出
                            _cutList.Add(new GachaCutInStepUp(cardList[index].Order, cardList[index].RarityUpType, true, _context, SETUP_SUBKEY0));
                            isStepUp = true;
                        }
                        if (index == 0 || isStepUp)
                        {
                            _cutList.Add(new GachaCutInR1R2GateStart(cardList[index].Order, _context, isStepUp));
                        }
                    }
                    _cutList.Add(new GachaCutInR2(cardList[index].Order, _context));
                }
                else
                {// R1
                    if (!isExecution)
                    {
                        if (index == 0)
                        {
                            _cutList.Add(new GachaCutInR1R2GateStart(cardList[index].Order, _context, false));
                        }
                    }
                    _cutList.Add(new GachaCutInR1(cardList[index].Order, _context));
                }
            }
            if (isNormal)
            {
                // 最後にリザルト追加
                _cutList.Add(new GachaCutCharaResult(_context));
            }
        }
        /// <summary>
        /// CutIn再生リストの作成(サポート)。
        /// </summary>
        private void CreateCutInInfoListForSupport()
        {
            _cutList.Clear();
            var executionType = TempData.Instance.GachaResult.ExecutionResult.ExecutionType;
            bool isNormal = executionType == GachaExecutionResult.Execution.Normal;
            bool isExecution = executionType == GachaExecutionResult.Execution.Exchange;
            if (!isExecution)
            {
                // 初期の演出を最初に追加。
                _cutList.Add(new GachaCutInSupportCmn(_context));
            }
            // カード別演出。
            var cardList = _context.CardList;
            for (int index = 0, count = cardList.Count; index < count; ++index)
            {
                var supportCardData = cardList[index].SupportCardData;
                if (cardList[index].IsR3Rarity())
                {// SSR
                    if (!isExecution)
                    {
                        //昇格
                        if (cardList[index].SupportCardRarityUpType == GachaDefine.GachaRarityUpType_Album.StepUp)
                        {
                            _cutList.Add(new GachaCutInSupportStepUp(cardList[index].Order, GachaDefine.GachaRarityUpType_Album.Gold, _context, false, SETUP_SUBKEY0));
                            _cutList.Add(new GachaCutInSupportStepUp(cardList[index].Order, GachaDefine.GachaRarityUpType_Album.Rainbow, _context, true, SETUP_SUBKEY1));
                        }
                        else if ((int)cardList[index].SupportCardRarityUpType != (int)cardList[index].SupportCardRarityType)
                        {
                            _cutList.Add(new GachaCutInSupportStepUp(cardList[index].Order, cardList[index].SupportCardRarityUpType, _context, false, SETUP_SUBKEY0));
                        }
                    }
                    if (index == 0)
                    {
                        _cutList.Add(new GachaCutInSupport1SSR(cardList[index].Order, _context));
                    }
                    else
                    {
                        _cutList.Add(new GachaCutInSupport2SSR(cardList[index].Order, _context));
                    }
                }
                else if (cardList[index].IsR2Rarity())
                {// SR
                    if (!isExecution)
                    {
                        //昇格
                        if ((int)cardList[index].SupportCardRarityUpType != (int)cardList[index].SupportCardRarityType)
                        {
                            _cutList.Add(new GachaCutInSupportStepUp(cardList[index].Order, cardList[index].SupportCardRarityUpType, _context, true, SETUP_SUBKEY0));
                        }
                    }
                    if (index == 0)
                    {
                        _cutList.Add(new GachaCutInSupport1SR(cardList[index].Order, _context));
                    }
                    else
                    {
                        _cutList.Add(new GachaCutInSupport2SR(cardList[index].Order, _context));
                    }
                }
                else
                {// R
                    if (index == 0)
                    {
                        _cutList.Add(new GachaCutInSupport1R(cardList[index].Order, _context));
                    }
                    else
                    {
                        _cutList.Add(new GachaCutInSupport2R(cardList[index].Order, _context));
                    }
                }
            }
            if (!isExecution)
            {
                _cutList.Add(new GachaCutSupportResultCloseStart(_context));
            }
            if (isNormal)
            {
                // 最後にリザルト追加
                _cutList.Add(new GachaCutSupportResult(_context));
            }
        }
        #endregion CutIn再生リスト作成

        /// <summary>
        /// スキップボタンが押された。
        /// </summary>
        public void OnPushSkipButton()
        {
            _isSkipAct = true;
            if (_playingIndex >= 0 && _playingIndex < _cutList.Count)
            {
                _cutList[_playingIndex].OnSkip();
            }
        }
        /// <summary>
        /// タップされた。
        /// </summary>
        public void OnTap()
        {
            if (_isRequestedChangeView && _playingIndex >= _cutList.Count)
            {
                return;
            }
            if (_context.TapIgnoreTime > 0f)
            {
                return;
            }
            _context.TapIgnoreTime = TAP_IGNORE_TIME;
            bool isEnableCutList = false;
            if (_playingIndex >= 0 && _playingIndex < _cutList.Count)
            {
                isEnableCutList = true;
            }
            bool isSkipedFrame = false;
            if (_context.CutInOwner.IsReadyJumpFrame)
            {
                bool isEnableTapSkip = true;
                if(isEnableCutList)
                {
                    isEnableTapSkip = _cutList[_playingIndex].IsEnableTapSkip();
                }
                if (isEnableTapSkip)
                {
                    int jumpFrame = _cutList[_playingIndex].ExchangeTapJumpFrame(_context.CutInOwner.JumpFrame);
                    if (_context.CutIn.TimelineController.CurrentFrame < jumpFrame)
                    {// 戻ることが無いように。
                        var setTime = (float)jumpFrame / (float)CutInTimelineController.DEFAULT_TARGET_FPS;
                        _context.CutInOwner.IsJumpedFrame = true;

                        //144091
                        //時々ゲートをなめる演出でタップ画効かなくなる問題の対応
                        //下で行われているSkipRuntime処理でカットがスキップした際に、「次のJumpFrameが決まるイベント」までスキップするのだが
                        //「次のJumpFrameが決まるイベント」がSkipRuntimeと同時に実行される時と、次のFにズレるときがあった（多分floatの誤差）
                        //前者の方が問題でSkipRuntime関数の後に_context.CutInOwner.JumpFrame = 0;していたため
                        //せっかく「次のJumpFrameが決まるイベント」で決まったJumpFrameが0になってしまい以後スキップ出来なくなってしまっていた
                        //ここのif文、else ifを見てもらえればわかるがjumpFrameが0だとスキップ出来ない
                        //SkipRuntimeの前に0にすることで、JumpFrameが初期化されないように対応した
                        _context.CutInOwner.JumpFrame = 0;

                        _context.CutIn.TimelineController.SkipRuntime(setTime);
                        _context.CutIn.TimelineController.SetCurrentTime(setTime); // 時間が進みすぎないように時間を固定
                        if (isEnableCutList)
                        {
                            _cutList[_playingIndex].OnSkip();
                        }
                        // SkipRuntime()時にSuspendされても今回のタップですぐにResumeされてSuspendされない場合があったのを修正
                        isSkipedFrame = true;
                    }
                    else if (jumpFrame < 0)
                    {// 次のCutへ。
                        _isNextCut = true;
                        if (isEnableCutList)
                        {
                            _cutList[_playingIndex].OnSkip();
                        }
                    }
                }
            }
            if (isEnableCutList)
            {
                _cutList[_playingIndex].OnTap();
            }
            if (_context.CutIn.TimelineController.IsSuspendAutoPlay && !_context.IsDisableWaitTapResume && !isSkipedFrame)
            {
                _context.CutInOwner.WaitTapFrame = -1;
                if (!_context.IsChara && TempData.Instance.GachaResult.ExecutionResult.ExecutionType == GachaExecutionResult.Execution.Exchange)
                {
                    _isNextCut = true;
                }
                else
                {
                    //111661 対応 タップされたら中断した処理を再開しますが、
                    //Cyspringの中止を再開しないようにする、停止時暖気入れがなくなったため、再開すると動き出してしまうのを避けます
                    //同じカットの使いまわしは「CutInCharacter.EntryModel」の関数でシミュレーション再開処理を行っているので、
                    //問題ないはずです
                    _context.CutIn.TimelineController.ResumeAutoUpdate();
                }
            }
        }
        /// <summary>
        /// リザルト画面でOKボタンが押された
        /// </summary>
        private void OnResultOk()
        {
            if (_context.CutIn.TimelineController.IsSuspendAutoPlay)
            {
                _context.CutIn.TimelineController.ResumeAutoUpdate(true);
                UIManager.Instance.LockGameCanvas();
                _isLockGameCanvas = true;
            }
        }

        // バックキーが押された時のコールバック
        public override void OnClickBackButton()
        {
            if (_view.PartsGachaResult.ButtonEnable)
            {
                OnResultOk();
            }
            else
            {
                //有効じゃない場合は注意文言を出す
                UIUtil.ShowNotificationBackKey();
            }
        }

        // OSバックキーが押された時のコールバック
        public override void OnClickOsBackKey()
        {
            OnClickBackButton();
        }

        #region デバッグ処理
#if CYG_DEBUG
        /// <summary>
        /// 強制スキップ
        /// </summary>
        public void ForceSkip()
        {
            _forceSkip = true;
        }

        private bool _forceSkip = false;
#endif // CYG_DEBUG
        #endregion デバッグ処理
    }
    #region GachaMainViewInfo
    /// <summary>
    /// ガチャ演出後に戻り先を指定したい場合に設定すること。
    /// 指定しない場合はガチャ結果画面に遷移します。
    /// </summary>
    public class GachaMainViewInfo : IViewInfo
    {
        private SceneDefine.ViewId _nextViewId = SceneDefine.ViewId.GachaResult;
        public SceneDefine.ViewId NextViewId { get { return _nextViewId; } }
        private IViewInfo _nextViewInfo = null;
        public IViewInfo NextViewInfo { get { return _nextViewInfo; } }

        public GachaMainViewInfo(SceneDefine.ViewId nextViewId, IViewInfo nextViewInfo = null)
        {
            _nextViewId = nextViewId;
            _nextViewInfo = nextViewInfo;
        }
    }
    #endregion GachaMainViewInfo   
}
