using System;
using UnityEngine;

namespace Gallop
{
    public class JobsSelectViewWorldMap : MonoBehaviour
    {
        [Serializable]
        public class CameraParameter
        {
            [Header("移動開始")]
            public float StartAngleX;
            public float StartAngleZ;
            public float StartDistance;
            public float StartFovY;
            
            [Header("中間点")]
            public float HalfAngleX;
            public float HalfAngleZ;
            public float HalfDistance;
            public float HalfFovY;
            
            [Header("移動完了")]
            public float EndAngleX;
            public float EndAngleZ;
            public float EndDistance;
            public float EndFovY;

            [Header("カーブと時間（前半）")]
            public float TweenTimeFirst = 0.2f;
            public AnimationCurve TweenCurveFirst = AnimationCurve.Linear(0f,0f,1f,1f);
            
            [Header("カーブと時間（後半）")]
            public float TweenTimeSecond = 0.3f;
            public AnimationCurve TweenCurveSecond = AnimationCurve.Linear(0f,0f,1f,1f);
        }
        
        [Header("ランダム→コース注目")]
        [SerializeField]
        private CameraParameter _idleToFocus = new CameraParameter
        {
            StartAngleX = 0f,
            StartAngleZ = 0f,
            StartDistance = 8f,
            StartFovY = 60f,
            
            HalfAngleX = 0f,
            HalfAngleZ = 0f,
            HalfDistance = 6f,
            HalfFovY = 60f,
            
            EndAngleX = -45f,
            EndAngleZ = 0f,
            EndDistance = 3f,
            EndFovY = 60f
        };

        public CameraParameter IdleToFocus => _idleToFocus;
        
        [Header("コース注目→コース注目")]
        [SerializeField]
        private CameraParameter _focusToFocus = new CameraParameter
        {
            StartAngleX = 0f,
            StartAngleZ = 0f,
            StartDistance = 8f,
            StartFovY = 60f,
            
            HalfAngleX = 0f,
            HalfAngleZ = 0f,
            HalfDistance = 6f,
            HalfFovY = 60f,
            
            EndAngleX = -45f,
            EndAngleZ = 0f,
            EndDistance = 3f,
            EndFovY = 60f
        };
        
        public CameraParameter FocusToFocus => _focusToFocus;
        
        [Header("コース注目→ランダム")]
        [SerializeField]
        private CameraParameter _focusToIdle = new CameraParameter
        {
            StartAngleX = 0f,
            StartAngleZ = 0f,
            StartDistance = 8f,
            StartFovY = 60f,
            
            HalfAngleX = 0f,
            HalfAngleZ = 0f,
            HalfDistance = 6f,
            HalfFovY = 60f,
            
            EndAngleX = -45f,
            EndAngleZ = 0f,
            EndDistance = 3f,
            EndFovY = 60f
        };
        
        public CameraParameter FocusToIdle => _focusToIdle;
    }
}