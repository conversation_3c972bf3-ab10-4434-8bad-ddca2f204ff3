using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DG.Tweening;
using Gallop.AnimSequenceHelper;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 全国興行：興行開始確認画面
    /// </summary>
    [AddComponentMenu("")]
    public sealed class JobsConfirmView : ViewBase
    {
        [Header("Center")]

        /// <summary>興行名（"【東京レース場】ファンミーティング"のようなテキスト）</summary>
        [SerializeField] private PartsJobsJobName _jobName;
        public PartsJobsJobName JobName => _jobName;

        /// <summary>「主な報酬」欄</summary>
        [SerializeField] private PartsJobsMainRewards _mainRewards;
        public PartsJobsMainRewards MainRewards => _mainRewards;

        [Header("List")]

        /// <summary>リストのScrollViewのRectTransform</summary>
        [SerializeField] private RectTransform _scrollViewRectTrans;
        public RectTransform ScrollViewRectTrans => _scrollViewRectTrans;

        /// <summary>リスト</summary>
        [SerializeField] private ScrollRectCommon _scrollRect = null;
        public ScrollRectCommon ScrollRect => _scrollRect;

        /// <summary>アイテムアイコン(主な報酬)</summary>
        [SerializeField] private RectTransform _itemIconsRoot;
        public RectTransform ItemIconsRoot => _itemIconsRoot;
        
        /// <summary>アイテムアイコンオブジェクトのルート</summary>
        [SerializeField] private RectTransform _itemIconsObjRoot;
        public RectTransform ItemIconsObjRoot => _itemIconsObjRoot;
        
        /// <summary>アイテムアイコン(その他)</summary>
        [SerializeField] private RectTransform _otherItemIconsRoot;
        public RectTransform OtherItemIconsRoot => _otherItemIconsRoot;
        
        [SerializeField] private ItemIconContainer _otherItemContainer = null;
        public ItemIconContainer OtherItemContainer => _otherItemContainer;
        
        /// <summary>アイテムアイコンオブジェクトのルート</summary>
        [SerializeField] private RectTransform _otherItemIconsObjRoot;
        public RectTransform OtherItemIconsObjRoot => _otherItemIconsObjRoot;

        [Header("LimitedJobInfoUI")]

        /// <summary>限定興行情報のルート</summary>
        [SerializeField] private GameObject _limitedJobInfoUIRoot;
        public GameObject LimitedJobInfoUIRoot => _limitedJobInfoUIRoot;

        /// <summary>限定興行の「開催期間」欄の数字</summary>
        [SerializeField] private TextCommon _limitedRemainTimeNumText;
        public TextCommon LimitedRemainTimeNumText => _limitedRemainTimeNumText;

        /// <summary>限定興行の「残り回数」欄のテキスト</summary>
        [SerializeField] private TextCommon _limitedRemainCountBeforeText;
        public TextCommon LimitedRemainCountBeforeText => _limitedRemainCountBeforeText;
        [SerializeField] private TextCommon _limitedRemainCountAfterText;
        public TextCommon LimitedRemainCountAfterText => _limitedRemainCountAfterText;

        [Header("Animation")]
        public CanvasGroup JobsNameCanvasGroup = null;
        public CanvasGroup MainRewardsHeaderCanvasGroup = null;
        public CanvasGroup MainRewardsMoneyCanvasGroup = null;
        public CanvasGroup MainRewardsSupportPtCanvasGroup = null;
        public CanvasGroup MainRewardsFanNumCanvasGroup = null;
        public CanvasGroup MainRewardsLovePointCanvasGroup = null;
        public CanvasGroup MainRewardsTrainerMedalCanvasGroup = null;
        public CanvasGroup MainRewardsOtherItemHeaderCanvasGroup = null;
        public CanvasGroup LowerUiCanvasGroup = null;

        [Header("Footer")]

        /// <summary>「開始！」ボタン</summary>
        [SerializeField] private ButtonCommon _nextButton;
        public ButtonCommon NextButton => _nextButton;
    }

    public sealed class JobsConfirmViewController : ViewControllerBase<JobsConfirmView>
    {
        /// <summary>「興行開始！」ボタン押下時の演出待ち時間（秒）</summary>
        private const float NEXT_BUTTON_EFFECT_WAIT = 0.6f;

        /// <summary>表示するjobs_reward.csvのマスター</summary>
        private MasterJobsReward.JobsReward _masterJobsReward = null;

        /// <summary>アイテムアイコン一覧 </summary>
        private List<RectTransform> _itemIconList = new List<RectTransform>();
        /// <summary>アイテムアイコン一覧(その他) </summary>
        private List<RectTransform> _otherItemIconContainerList = new List<RectTransform>();

        private bool _isClickedNextButton = false;

        /// <summary>全国興行のワークデータ</summary>
        private WorkJobsData _workJobsData => WorkDataManager.Instance.JobsData;
        private WorkJobsData.JobPreStartInfo _workJobPreStartInfo => _workJobsData.PreStartInfo;
        
        /// <summary>イリアニメ関連</summary>
        private Coroutine _inAnimationCoroutine = null;

        /// <summary>イリハケ時用</summary>
        private List<Vector3> _mainRewardsPos = new List<Vector3>();
        
        /// <summary>
        /// HubViewに遷移した時の初期化処理
        /// </summary>
        public override IEnumerator InitializeView()
        {
            // 主な報酬の位置を保存しておく
            _mainRewardsPos.Clear();
            _mainRewardsPos.Add(_view.MainRewardsHeaderCanvasGroup.transform.localPosition);
            _mainRewardsPos.Add(_view.MainRewardsMoneyCanvasGroup.transform.localPosition);
            _mainRewardsPos.Add(_view.MainRewardsSupportPtCanvasGroup.transform.localPosition);
            _mainRewardsPos.Add(_view.MainRewardsFanNumCanvasGroup.transform.localPosition);
            _mainRewardsPos.Add(_view.MainRewardsLovePointCanvasGroup.transform.localPosition);
            _mainRewardsPos.Add(_view.MainRewardsTrainerMedalCanvasGroup.transform.localPosition);

            yield return base.InitializeView();
        }

        /// <summary>
        /// このViewに遷移した時の初期化処理
        /// </summary>
        public override IEnumerator InitializeEachPlayIn()
        {
            _masterJobsReward = _workJobPreStartInfo.MasterJobsReward;

            // 興行名のセットアップ
            SetupJobName();

            // 「主な報酬」欄のセットアップ
            SetupMainRewards();

            // 「獲得可能な報酬」欄のセットアップ
            SetupGettableRewards();

            // 【限定興行専用】限定興行の情報UIのセットアップ
            SetupLimitedJobInfoUI();

            // 「開始！」ボタンのセットアップ
            SetupNextButton();

            // 非表示にしておく
            SetActivePlayIn(false);

            yield return base.InitializeEachPlayIn();
        }
        
        /// <summary>
        /// 特定の要素の表示on/off
        /// </summary>
        private void SetActivePlayIn(bool isActive)
        {
            _view.ScrollRect.SetActiveWithCheck(isActive);
        }

        /// <summary>
        /// 画面イリ処理
        /// </summary>
        public override IEnumerator PlayInView()
        {
            // 表示にしておく
            SetActivePlayIn(true);
            
            // イリアニメを再生
            _inAnimationCoroutine = _view.StartCoroutine(InAnimationCoroutine());
            
            yield return base.PlayInView();
        }
        
        /// <summary>
        /// イリアニメを再生
        /// </summary>
        private IEnumerator InAnimationCoroutine()
        {
            // イリアニメする要素を全て透明に
            {
                _view.JobsNameCanvasGroup.alpha = 0f;
                _view.MainRewardsHeaderCanvasGroup.alpha = 0f;
                _view.MainRewardsMoneyCanvasGroup.alpha = 0f;
                _view.MainRewardsSupportPtCanvasGroup.alpha = 0f;
                _view.MainRewardsFanNumCanvasGroup.alpha = 0f;
                _view.MainRewardsLovePointCanvasGroup.alpha = 0f;
                _view.MainRewardsTrainerMedalCanvasGroup.alpha = 0f;
                foreach (var itemIcon in _itemIconList)
                {
                    var newItemIcon = itemIcon.GetComponentInChildren<ItemIcon>();
                    var itemIconCanvasGroup = newItemIcon.transform.GetComponent<CanvasGroup>();
                    itemIconCanvasGroup.alpha = 0f;
                }
                
                _view.MainRewardsOtherItemHeaderCanvasGroup.alpha = 0f;
                foreach (var otherItemIconContainer in _otherItemIconContainerList)
                {
                    var itemIconContainer = otherItemIconContainer.GetComponentInChildren<ItemIconContainer>();
                    var otherItemIconContainerCanvasGroup =
                        itemIconContainer.transform.GetComponent<CanvasGroup>();
                    otherItemIconContainerCanvasGroup.alpha = 0f;
                }

                _view.LowerUiCanvasGroup.alpha = 0f;
            }

            // 全画面ワイプインを少し待つ
            {
                yield return null;
            }

            // 【リスト全体のイリ】
            yield return InAnimation();
        }

        /// <summary>
        /// イリアニメを再生
        /// </summary>
        private IEnumerator InAnimation()
        {
            var presetJobsName = TweenAnimation.PresetType.PartsInFade;
            var preset = TweenAnimation.PresetType.PartsInMoveAndFade;
            
            // ・上から順に一行ずつ0.033秒おきにイリアニメを再生していく
            {
                const float DELAY_INTERVAL = 0.033f;

                var sequence = DOTween.Sequence();

                var delay = 0f;

                _view.JobName.ResizeLayout();
                sequence.Insert(_view.JobsNameCanvasGroup.gameObject, presetJobsName, delay);
                delay += DELAY_INTERVAL;

                // 獲得報酬
                sequence.Insert(_view.MainRewardsHeaderCanvasGroup.gameObject, preset, delay);
                delay += DELAY_INTERVAL;

                sequence.Insert(_view.MainRewardsMoneyCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsSupportPtCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsTrainerMedalCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsFanNumCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsLovePointCanvasGroup.gameObject, preset, delay);
                delay += DELAY_INTERVAL;

                var grid = _view.ItemIconsRoot.GetComponent<GridLayoutGroup>();
                var itemLineNum = grid == null ? 0 : grid.constraintCount;
                int index = 0;
                foreach (var itemIcon in _itemIconList)
                {
                    var newItemIcon = itemIcon.GetComponentInChildren<ItemIcon>();
                    var itemIconCanvasGroup = newItemIcon.transform.GetComponent<CanvasGroup>();
                    sequence.Insert(itemIconCanvasGroup.gameObject, preset, delay);
                    index++;
                    if (index % itemLineNum == 0 && itemIcon != _itemIconList.Last())
                    {
                        delay += DELAY_INTERVAL;
                    }
                }
                delay += DELAY_INTERVAL;
                
                sequence.Insert(_view.MainRewardsOtherItemHeaderCanvasGroup.gameObject, preset, delay);
                delay += DELAY_INTERVAL;
                
                foreach (var otherItemIconContainer in _otherItemIconContainerList)
                {
                    var itemIconContainer = otherItemIconContainer.GetComponentInChildren<ItemIconContainer>();
                    var otherItemIconContainerCanvasGroup = itemIconContainer.transform.GetComponent<CanvasGroup>();
                    sequence.Insert(otherItemIconContainerCanvasGroup.gameObject, preset, delay);
                    delay += DELAY_INTERVAL;
                }

                // 下部UI
                sequence.Insert(_view.LowerUiCanvasGroup.gameObject, TweenAnimation.PresetType.PartsInFade, delay);
                delay += DELAY_INTERVAL;

                yield return new WaitForSeconds(delay);
            }
        }
        
        /// <summary>
        /// ハケアニメを再生
        /// </summary>
        private IEnumerator OutAnimation()
        {
            var presetJobsName = TweenAnimation.PresetType.PartsOutFade;
            var preset = TweenAnimation.PresetType.PartsOutMoveAndFade;
            
            // ・下から順に一行ずつ0.033秒おきにイリアニメを再生していく
            {
                const float DELAY_INTERVAL = 0.033f;

                var sequence = DOTween.Sequence();

                var delay = 0f;

                // 下部UI
                sequence.Insert(_view.LowerUiCanvasGroup.gameObject, TweenAnimation.PresetType.PartsOutFade, delay);
                delay += DELAY_INTERVAL;

                // 獲得報酬
                _otherItemIconContainerList.Reverse();
                foreach (var otherItemIconContainer in _otherItemIconContainerList)
                {
                    var itemIconContainer = otherItemIconContainer.GetComponentInChildren<ItemIconContainer>();
                    var otherItemIconContainerCanvasGroup = itemIconContainer.transform.GetComponent<CanvasGroup>();
                    sequence.Insert(otherItemIconContainerCanvasGroup.gameObject, preset, delay);
                    delay += DELAY_INTERVAL;
                }
                
                sequence.Insert(_view.MainRewardsOtherItemHeaderCanvasGroup.gameObject, preset, delay);
                delay += DELAY_INTERVAL;
                
                var grid = _view.ItemIconsRoot.GetComponent<GridLayoutGroup>();
                var itemLineNum = grid == null ? 0 : grid.constraintCount;
                int index = _itemIconList.Count;
                _itemIconList.Reverse();
                foreach (var itemIcon in _itemIconList)
                {
                    var newItemIcon = itemIcon.GetComponentInChildren<ItemIcon>();
                    var itemIconCanvasGroup = newItemIcon.transform.GetComponent<CanvasGroup>();
                    sequence.Insert(itemIconCanvasGroup.gameObject, preset, delay);
                    index--;
                    if (index % itemLineNum == 0 && itemIcon != _itemIconList.Last())
                    {
                        delay += DELAY_INTERVAL;
                    }
                }
                delay += DELAY_INTERVAL;
                
                sequence.Insert(_view.MainRewardsLovePointCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsFanNumCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsTrainerMedalCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsSupportPtCanvasGroup.gameObject, preset, delay);
                sequence.Insert(_view.MainRewardsMoneyCanvasGroup.gameObject, preset, delay);
                delay += DELAY_INTERVAL;
                
                sequence.Insert(_view.MainRewardsHeaderCanvasGroup.gameObject, preset, delay);
                delay += DELAY_INTERVAL;
                
                // 興行名
                sequence.Insert(_view.JobsNameCanvasGroup.gameObject, presetJobsName, delay);
                delay += DELAY_INTERVAL;

                yield return new WaitForSeconds(delay);
            }
        }

        /// <summary>
        /// 興行名のセットアップ
        /// </summary>
        private void SetupJobName()
        {
            if (_masterJobsReward == null)
                return;

            // "【東京レース場】ファンミーティング"のようなテキスト
            _view.JobName.Setup(_masterJobsReward);

            // 位置初期化
            _view.JobsNameCanvasGroup.transform.localPosition = Vector3.zero;
        }

        /// <summary>
        /// 「主な報酬」欄のセットアップ
        /// </summary>
        private void SetupMainRewards()
        {
            _view.MainRewards.Setup(_masterJobsReward);
            
            // 位置を戻しておく
            _view.MainRewardsHeaderCanvasGroup.transform.localPosition = _mainRewardsPos[0];
            _view.MainRewardsMoneyCanvasGroup.transform.localPosition = _mainRewardsPos[1];
            _view.MainRewardsSupportPtCanvasGroup.transform.localPosition = _mainRewardsPos[2];
            _view.MainRewardsFanNumCanvasGroup.transform.localPosition = _mainRewardsPos[3];
            _view.MainRewardsLovePointCanvasGroup.transform.localPosition = _mainRewardsPos[4];
            _view.MainRewardsTrainerMedalCanvasGroup.transform.localPosition = _mainRewardsPos[5];
        }

        /// <summary>
        /// 「獲得可能な報酬」欄のセットアップ
        /// </summary>
        private void SetupGettableRewards()
        {
            _itemIconList.Clear();
            _otherItemIconContainerList.Clear();

            if (_masterJobsReward == null)
                return;
            
            // 位置初期化
            _view.MainRewardsOtherItemHeaderCanvasGroup.transform.localPosition = Vector3.zero;

            // 表示するアイテムの情報一覧を取得
            var mainRewardItemList = _masterJobsReward.MainRewardItemList;
            if (mainRewardItemList.IsNullOrEmpty())
                return;
            
            // 主な報酬
            var mainItemList = mainRewardItemList
                .Where(x => x.ItemDispType == MasterJobsReward.JobsReward.MainRewardItem.JobsRewardItemDispType.Main).ToList();
            
            // アイテムアイコンのインスタンスを生成してセットアップ
            _view.ItemIconsObjRoot.SetActiveWithCheck(true);
            foreach (var mainRewardItem in mainItemList)
            {
                var newItemRoot = Object.Instantiate(_view.ItemIconsObjRoot, _view.ItemIconsRoot);
                var newItemIcon = newItemRoot.GetComponentInChildren<ItemIcon>();
                newItemIcon.SetData(mainRewardItem.ItemCategory, mainRewardItem.ItemId, number: mainRewardItem.ItemNum, numDisp: true, isInfoPop: true, onClick: newItemIcon.OnTapBlanket);
                _itemIconList.Add(newItemRoot);
            }
            _view.ItemIconsObjRoot.SetActiveWithCheck(false);
            
            // その他の報酬
            var otherItemList = mainRewardItemList
                .Where(x => x.ItemDispType == MasterJobsReward.JobsReward.MainRewardItem.JobsRewardItemDispType.Other).ToList();
            
            // 入れ物作成
            var listCount = otherItemList.Count;
            var otherItemIconInfoList = new List<ItemIconListInfo>(listCount);
            foreach (var otherItem in otherItemList)
            {
                var itemIconInfo =
                    new ItemIconListInfo(otherItem.ItemCategory, otherItem.ItemId, otherItem.ItemNum);
                itemIconInfo.IsDispNum = true;
                itemIconInfo.IsInfoPop = true;
                otherItemIconInfoList.Add(itemIconInfo);
            }

            var containerCount = _view.OtherItemContainer.GetInLineCount();
            var listNum = listCount / containerCount;
            if (listCount % containerCount > 0)
            {
                // 余りの行
                listNum++;
            }
            
            // １行分毎に分解
            var explodeList = otherItemIconInfoList.Select((info, i) => new {info, i})
                .GroupBy(x => x.i / containerCount)
                .Select(group => group.Select(x => x.info)).ToList();
            
            _view.OtherItemIconsObjRoot.SetActiveWithCheck(true);
            
            for (int i = 0; i < listNum; i++)
            {
                var clone = Object.Instantiate(_view.OtherItemIconsObjRoot, _view.OtherItemIconsRoot);
                var itemIconContainer = clone.GetComponentInChildren<ItemIconContainer>();
                if (itemIconContainer != null)
                {
                    // LoopScrollだと全部入った配列を送っているためにItemIndexがループごとに変わるが、今回は行毎に送っているのでItemIndexは0スタート
                    itemIconContainer.ItemIndex = 0;
                    itemIconContainer.UpdateItem(explodeList[i].ToList());
                    _otherItemIconContainerList.Add(clone);
                }
            }
            
            _view.OtherItemIconsObjRoot.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 限定興行情報のセットアップ
        /// </summary>
        private void SetupLimitedJobInfoUI()
        {
            // この興行が限定興行なら表示
            bool isActive = _workJobPreStartInfo.IsLimitedTab;
            _view.LimitedJobInfoUIRoot.SetActiveWithCheck(isActive);

            // 表示するかどうかで ScrollView の Bottom 位置を変更する
            {
                const float BOTTOM_NORMAL = 438;
                const float BOTTOM_LIMITED = 567;

                var left = _view.ScrollViewRectTrans.offsetMin.x;
                var bottom = isActive ? BOTTOM_LIMITED : BOTTOM_NORMAL;

                _view.ScrollViewRectTrans.offsetMin = new Vector2(left, bottom);
            }

            // リストのスクロール位置を一番上にリセット
            _view.ScrollRect.verticalNormalizedPosition = 1;

            if (!isActive)
                return; // 表示しないならここまで

            // 「開催期間」欄
            {
                long remainTime = (_workJobsData.MasterJobsLimitedSchedule.EndingDate - TimeUtil.GetServerTimeStamp());
                _view.LimitedRemainTimeNumText.text = TextUtil.ToStringRemainingTime(remainTime);
            }

            // 「残り回数」欄
            {
                var workLimitedJobInfo = _workJobsData.GetLimitedJobInfo(_masterJobsReward.Id);

                int remainCountBefore = (workLimitedJobInfo != null) ? workLimitedJobInfo.RemainCount : _masterJobsReward.LimitedMaxCount;
                int remainCountAfter = Mathf.Max(remainCountBefore - 1, 0);

                _view.LimitedRemainCountBeforeText.text = TextId.TeamBuilding408043.Format(remainCountBefore);
                _view.LimitedRemainCountAfterText.text = TextId.TeamBuilding408043.Format(remainCountAfter);
            }
        }

        /// <summary>
        /// 「戻る」ボタン押下時処理
        /// </summary>
        public override void OnClickBackButton()
        {
            // 参加ウマ娘選択画面へ
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsCharaSelect);
        }

        /// <summary>
        /// OSのバックキー押下時処理
        /// </summary>
        public override void OnClickOsBackKey()
        {
            // 戻るボタンと同じ挙動
            OnClickBackButton();
        }

        /// <summary>
        /// 「開始！」ボタンのセットアップ
        /// </summary>
        private void SetupNextButton()
        {
            _view.NextButton.SetOnClick(OnClickNextButton);
            
            _isClickedNextButton = false;
        }

        /// <summary>
        /// 「開始！」ボタン押下時処理
        /// </summary>
        private void OnClickNextButton()
        {
            if (_masterJobsReward == null)
                return;

            // 連打防止
            if (_isClickedNextButton)
                return;
            _isClickedNextButton = true;

            _view.StartCoroutine(NextButtonCoroutine());
        }

        /// <summary>
        /// 「開始！」ボタン押下時コルーチン
        /// </summary>
        private IEnumerator NextButtonCoroutine()
        {
            // 興行開始API通信
            {
                bool isSuccess = false;

                var attendCardInfoList = new List<JobsAttendCardInfo>();
                for (int i = 0, len = _workJobPreStartInfo.SelectedCardIdList.Count; i < len; i++)
                {
                    //148421 全国興行でキャラのカードIdと衣装Idがずれることがあるため、
                    //Apiで渡す前にチェックをかける
                    var card_id = _workJobPreStartInfo.SelectedCardIdList[i];
                    var dress_id = _workJobPreStartInfo.SelectedDressIdList[i];
                    var masterCardData = MasterDataManager.Instance.masterCardData.Get(card_id);
                    var masterDressData = MasterDataManager.Instance.masterDressData.Get(dress_id);
                    if (masterDressData == null || masterDressData.CharaId != masterCardData.CharaId)
                    {
                        //衣装データに何等かの問題が発生していれば、再度衣装Id取り出す
                        dress_id = JobsUtil.GetDressId(card_id);
                    }

                    attendCardInfoList.Add(new JobsAttendCardInfo()
                    {
                        card_id = card_id,
                        dress_id = dress_id
                    });
                }
                
                var jobsStartJobInfo = new JobsStartJobInfo()
                {
                    jobs_reward_id = _workJobPreStartInfo.MasterJobsReward.Id,
                    attend_card_info_array = attendCardInfoList.ToArray(),
                    local_push_type_index = _workJobPreStartInfo.ButtonIndex
                };

                JobsUtil.SendJobsStartAPI(jobsStartJobInfo,
                    onSuccess: () =>
                    {
                        isSuccess = true;
                    });

                yield return new WaitUntil(() => isSuccess);
            }

            // 入力を禁止
            UIManager.Instance.LockGameCanvas();

            // ミニキャラの興行開始モーション（腕を上げるモーション）を再生
            PlayMiniCharaJobStartMotion();

            //少し待つ
            yield return new WaitForSeconds(NEXT_BUTTON_EFFECT_WAIT);

            // 全画面ワイプアウト（ミニキャラを含めて全画面を一度隠す）
            {
                int charaId = _workJobPreStartInfo.GetFirstCharaId();

                bool isCompleted = false;

                JobsUtil.ShowNowLoadingNormalWipeOut(
                    onComplete: () =>
                    {
                        isCompleted = true;
                    },
                    charaId);

                yield return new WaitUntil(() => isCompleted);
            }

            // 演出ステート変更
            yield return ChangeDirectorState();

            // 入力禁止を解除
            UIManager.Instance.UnlockGameCanvas();

            // 興行TOPへ
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsTop);
        }




        #region 3Dミニキャラ関連

        /// <summary>
        /// 3Dミニキャラ：キャラ数取得
        /// </summary>
        public int GetMiniCharaNum()
        {
            return _workJobPreStartInfo.SelectedCardIdList.Count;
        }

        /// <summary>
        /// 3Dミニキャラ：MiniCharaData取得
        /// </summary>
        public IMiniCharaData GetMiniCharaData(int index)
        {
            if (index >= _workJobPreStartInfo.MiniCharaDataList.Count)
                return null;

            return _workJobPreStartInfo.MiniCharaDataList[index];
        }

        /// <summary>
        /// 3Dミニキャラ：興行開始モーション（腕を上げるモーション）
        /// </summary>
        private void PlayMiniCharaJobStartMotion()
        {
            WorkDataManager.Instance.JobsData.IsExecStartMotion = true;
        }

        /// <summary>
        /// 3Dミニキャラ：ステート変更
        /// </summary>
        private IEnumerator ChangeDirectorState()
        {
            var viewController = SceneManager.Instance.GetCurrentViewController<JobsHubViewController>();

            if (viewController != null)
            {
                yield return viewController.ChangeStateCoroutine(MiniDirectorDefines.DirectorState.JobRunning);
            }

            WorkDataManager.Instance.JobsData.IsExecStartMotion = false;
            yield break;
        }

        #endregion

        /// <summary>
        /// 画面ハケ処理
        /// </summary>
        public override IEnumerator PlayOutView()
        {
            yield return OutAnimation();

            yield return base.PlayOutView();
        }

        /// <summary>
        /// このViewを抜けた時の処理
        /// </summary>
        public override IEnumerator EndView()
        {
            foreach (var itemIcon in _itemIconList)
            {
                Object.Destroy(itemIcon.gameObject);
            }
            _itemIconList.Clear();
            
            foreach (var itemIconContainer in _otherItemIconContainerList)
            {
                Object.Destroy(itemIconContainer.gameObject);
            }
            _otherItemIconContainerList.Clear();
            
            if (_inAnimationCoroutine != null)
            {
                _view.StopCoroutine(_inAnimationCoroutine);
                _inAnimationCoroutine = null;
            }

            yield return base.EndView();
        }
    }
}
