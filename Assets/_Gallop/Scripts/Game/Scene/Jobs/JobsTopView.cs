using System.Collections;
using System.Linq;
using DG.Tweening;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 全国興行：TOP画面
    /// </summary>
    [AddComponentMenu("")]
    public sealed class JobsTopView : ViewBase
    {
        [Header("UpperUI")]

        /// <summary>（リストが空の時に表示する）ロゴ画像</summary>
        [SerializeField] private RawImageCommon _logoImage = null;
        public RawImageCommon LogoImage => _logoImage;

        /// <summary>TP</summary>
        [SerializeField] private CanvasGroup _tpHeaderCanvasGroup = null;
        public CanvasGroup TpHeaderCanvasGroup => _tpHeaderCanvasGroup;

        [Header("CenterUI")]

        /// <summary>中央部UIのCanvasGroup</summary>
        [SerializeField] private CanvasGroup _centerUiCanvasGroup = null;
        public CanvasGroup CenterUiCanvasGroup => _centerUiCanvasGroup;

        /// <summary>中央部UIの下地ロゴ画像</summary>
        [SerializeField] private RawImageCommon _centerUiBaseImage = null;
        public RawImageCommon CenterUiBaseImage => _centerUiBaseImage;

        /// <summary>（リストが空の時に表示する）情報欄</summary>
        [SerializeField] private GameObject _centerEmptyInfoRoot = null;
        public GameObject CenterEmptyInfoRoot => _centerEmptyInfoRoot;

        /// <summary>（リストが空の時に表示する）白縁付き鞄アイコン</summary>
        [SerializeField] private RawImageCommon _centerUiBagIcon = null;
        public RawImageCommon CenterUiBagIcon => _centerUiBagIcon;


        /// <summary>（興行中に表示する）情報欄</summary>
        [SerializeField] private GameObject _centerGoingInfoRoot = null;
        public GameObject CenterGoingInfoRoot => _centerGoingInfoRoot;

        /// <summary>（興行中に表示する）"ファンミーティング（サイン会）"のようなテキスト</summary>
        [SerializeField] private GameObject _centerJobGenreNameFrame = null;
        public GameObject CenterJobGenreNameFrame => _centerJobGenreNameFrame;
        [SerializeField] private TextCommon _centerJobGenreName = null;
        public TextCommon CenterJobGenreName => _centerJobGenreName;

        /// <summary>（複数興行中の場合に表示する）「〇●〇」のようなUI</summary>
        [SerializeField] private GameObject _pageDotsRoot = null;
        public GameObject PageDotsRoot => _pageDotsRoot;
        [SerializeField] private PartsPageDots _pageDots;
        public PartsPageDots PageDots => _pageDots;

        /// <summary>（複数興行中の場合に表示する）←ボタン</summary>
        [SerializeField] private ButtonCommon _miniCharaLeftButton = null;
        public ButtonCommon MiniCharaLeftButton => _miniCharaLeftButton;

        /// <summary>（複数興行中の場合に表示する）→ボタン</summary>
        [SerializeField] private ButtonCommon _miniCharaRightButton = null;
        public ButtonCommon MiniCharaRightButton => _miniCharaRightButton;

        /// <summary>興行無しの時の背景</summary>
        [SerializeField] private PartsJobsTopEmptyBg _emptyBgPrefab;
        public PartsJobsTopEmptyBg EmptyBgPrefab => _emptyBgPrefab;

        /// <summary>ミニキャラの左右フリック機能</summary>
        [SerializeField] private FlickableObject _miniCharaflickableObject = null;
        public FlickableObject MiniCharaFlickableObject => _miniCharaflickableObject;

        /// <summary>キャンペーンアイコン一覧</summary>
        [SerializeField] private PartsCampaignIconList _campaignIconList;
        public PartsCampaignIconList CampaignIconList => _campaignIconList;

        [Header("Tab")]

        /// <summary>タブ</summary>
        [SerializeField] private FlickToggleGroupCommon _tabFlickToggleGroup = null;
        public FlickToggleGroupCommon TabFlickToggleGroup => _tabFlickToggleGroup;

        /// <summary>タブの左右フリック機能</summary>
        [SerializeField] private FlickableObject _tabFlickableObject = null;
        public FlickableObject TabFlickableObject => _tabFlickableObject;

        /// <summary>「限定」タブのNEWアイコン</summary>
        [SerializeField] private Transform _limitedTabNewIconRoot = null;
        public Transform LimitedTabNewIconRoot => _limitedTabNewIconRoot;

        [Header("NormalTabList")]

        /// <summary>「通常」タブのリストのルート（※タブが無い場合も表示されます）</summary>
        [SerializeField] private GameObject _normalTabListRoot;
        public GameObject NormalTabListRoot => _normalTabListRoot;

        /// <summary>「通常」タブのリストのScrollViewのRectTransform</summary>
        [SerializeField] private RectTransform _normalTabListScrollViewRectTrans;
        public RectTransform NormalTabListScrollViewRectTrans => _normalTabListScrollViewRectTrans;

        /// <summary>「通常」タブのリストのLoopScroll</summary>
        [SerializeField] private LoopScroll _normalTabListLoopScroll = null;
        public LoopScroll NormalTabListLoopScroll => _normalTabListLoopScroll;

        [Header("LimitedTabList")]

        /// <summary>「限定」タブのリスト</summary>
        [SerializeField] private PartsJobsTopLimitedTabList _limitedTabList;
        public PartsJobsTopLimitedTabList LimitedTabList => _limitedTabList;

        [Header("LowerUI")]

        /// <summary>「まとめて短縮」ボタン</summary>
        [SerializeField] private ButtonCommon _reduceButton = null;
        public ButtonCommon ReduceButton => _reduceButton;

        /// <summary>「まとめて確認」ボタン</summary>
        [SerializeField] private ButtonCommon _checkCompleteButton = null;
        public ButtonCommon CheckCompleteButton => _checkCompleteButton;
    }

    public sealed class JobsTopViewController : ViewControllerBase<JobsTopView>
    {
        /// <summary>タブの種類</summary>
        public enum TabType
        {
            Normal,  // 通常
            Limited, // 限定
        }

        /// <summary>3Dミニキャラ関連</summary>
        private WorkJobsData.GoingJobInfo _miniCharaWorkGoingJobInfo = null;
        private bool IsMiniCharaEmpty => (_miniCharaWorkGoingJobInfo == null);

        /// <summary>中央部に表示されるドット（「〇●〇」のようなUI）の情報</summary>
        private int _pageDotCount = 0;
        private int _pageDotIndex = 0;
        private bool IsPageDotEmpty => (_pageDotCount <= 0);
        private bool IsPageDotEmptyOrSingle => (_pageDotCount <= 1);

        /// <summary>タブがあるか</summary>
        private bool _hasTab = false;

        /// <summary>現在どのタブを選んでいるか</summary>
        private int _currentTabIndex = 0;

        /// <summary>限定タブのNEWアイコン</summary>
        private GameObject _limitedTabNewIcon = null;

        /// <summary>画面下部のボタン関連</summary>
        private bool _isClickedCheckCompleteButton = false;

        /// <summary>興行無しの時の背景</summary>
        private PartsJobsTopEmptyBg _emptyBg = null;

        private WorkJobsData _workJobsData => WorkDataManager.Instance.JobsData;
        private ApplicationSettingSaveLoader _saveLoader => SaveDataManager.Instance.SaveLoader;


        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.JOBS_LOGO_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.JOBS_TOP_CENTER_UI_BASE_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.JOBS_TOP_BAG_ICON_PATH);
            PartsJobsTopListItem.RegisterDownload(register);
            PartsJobsTopEmptyBg.RegisterDownload(register);
        }

        /// <summary>
        /// HubViewに遷移した時の初期化処理
        /// </summary>
        public override IEnumerator InitializeView()
        {
            // 必要なら端末セーブデータをリセットする
            ResetApplicationSaveDataIfNeeded();

            yield return base.InitializeView();
        }

        /// <summary>
        /// このViewに遷移した時の初期化処理
        /// </summary>
        public override IEnumerator InitializeEachPlayIn()
        {
            // 2Dロゴのセットアップ
            SetupLogo();

            // 中央部UIのセットアップ
            SetupCenterUI();

            // タブのセットアップ
            SetupTab();

            // リストのセットアップ
            SetupList();

            // 画面下部のボタンのセットアップ
            SetupFooterButtons();
            
            if (_miniCharaWorkGoingJobInfo == null)
            {
                // 初期タブに合わせて画面内を更新
                OnSelectTab(_currentTabIndex);

                yield return ChangeDirectorState();
            }
            else
            {
                yield return ChangeDirectorState();

                // 初期タブに合わせて画面内を更新
                OnSelectTab(_currentTabIndex);
            }

            yield return base.InitializeEachPlayIn();
        }

        /// <summary>
        /// 画面イリ処理
        /// </summary>
        public override IEnumerator PlayInView()
        {
            // 興行開始確認画面から来た場合、全画面ワイプがかかっているので解除する
            if (NowLoading.Instance.IsShown())
                NowLoading.Instance.Hide();

            _view.ReduceButton.transform.localPosition = Math.VECTOR3_ZERO;
            _view.CheckCompleteButton.transform.localPosition = Math.VECTOR3_ZERO;
            var isFooterClose = UIManager.Instance.IsOutPlayingFooterBackButton();
            if (!isFooterClose)
            {
                PlayInOutButton(true);
            }
            else
            {
                // ボタン
                UIManager.Instance.SetFooterBackButtonInAction(() =>
                {
                    PlayInOutButton(true);
                });
            }

            // 中央部UIのイリアニメを再生
            PlayInOutCenterUI(true);

            // キャンペーンアイコンリストのイリアニメを再生
            _view.CampaignIconList.PlayIn();

            // リストの「興行中」「興行完了」文字演出のイリアニメを再生
            PlayInOutListFlash(true);

            // 「通常」タブのリストの汎用イリアニメを再生
            var isCompleteNormalTabListIn = false;
            _view.NormalTabListLoopScroll.PlayInAnimation(true, () => isCompleteNormalTabListIn = true);

            // 「限定」タブのリストの汎用イリアニメを再生
            var isCompleteLimitedTabListIn = !_hasTab;
            if (_hasTab)
            {
                _view.LimitedTabList.PlayInAnimation(() => isCompleteLimitedTabListIn = true);
            }

            yield return new WaitUntil(() => (isCompleteNormalTabListIn && isCompleteLimitedTabListIn));

            // リストの「興行中」「興行完了」文字演出の待機アニメを再生
            PlayIdleListFlash();
            
            // Tips
            if (!DialogTutorialGuide.IsAlreadyRead(DialogTutorialGuide.TutorialGuideId.Jobs))
            {
                DialogTutorialGuide.PushDialog(DialogTutorialGuide.TutorialGuideId.Jobs);
            }

            yield return base.PlayInView();
        }

        /// <summary>
        /// 2Dのロゴ画像のセットアップ
        /// </summary>
        private void SetupLogo()
        {
            var texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.JOBS_LOGO_PATH, JobsDefine.LOAD_ON_VIEW_ID);
            _view.LogoImage.texture = texture;
        }

        /// <summary>ロゴの更新</summary>
        private void RefreshLogo(bool isLogoActive)
        {
            _view.LogoImage.SetActiveWithCheck(isLogoActive);

            SetVisibleDirector(!isLogoActive);
        }

        /// <summary>
        /// 中央部UIのセットアップ
        /// </summary>
        private void SetupCenterUI()
        {
            // 下地画像のセットアップ
            var texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.JOBS_TOP_CENTER_UI_BASE_PATH, JobsDefine.LOAD_ON_VIEW_ID);
            _view.CenterUiBaseImage.texture = texture;

            // 鞄アイコンのセットアップ
            var bagTexture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.JOBS_TOP_BAG_ICON_PATH, JobsDefine.LOAD_ON_VIEW_ID);
            _view.CenterUiBagIcon.texture = bagTexture;
        }

        /// <summary>中央部UIの更新</summary>
        private void RefreshCenterUI()
        {
            // 左右ボタン
            {
                bool isActive = (!IsPageDotEmptyOrSingle);
                _view.MiniCharaLeftButton.SetActiveWithCheck(isActive);
                _view.MiniCharaRightButton.SetActiveWithCheck(isActive);
                _view.MiniCharaFlickableObject.SetActiveWithCheck(isActive);
                if (isActive)
                {
                    _view.MiniCharaLeftButton.SetOnClick(OnClickMiniCharaLeftButton);
                    _view.MiniCharaRightButton.SetOnClick(OnClickMiniCharaRightButton);
                    _view.MiniCharaFlickableObject.SetFlickCallback(OnFlick);
                }
            }

            // 複数の興行中の場合に表示するドット（「〇●〇」のようなUI）
            {
                bool isActive = (!IsPageDotEmptyOrSingle);
                _view.PageDotsRoot.SetActiveWithCheck(isActive);
                if (isActive)
                {
                    _view.PageDots.Setup(_pageDotCount);
                    _view.PageDots.UpdateDots(_workJobsData.GoingJobInfoList.IndexOf(_miniCharaWorkGoingJobInfo));
                }
            }

            bool isEmpty = IsPageDotEmpty;
            _view.CenterEmptyInfoRoot.SetActiveWithCheck(isEmpty);
            _view.CenterGoingInfoRoot.SetActiveWithCheck(!isEmpty);

            // キャンペーンアイコンリストの更新
            RefreshCampaignIconList();

            // ドットが無い場合
            if (IsPageDotEmpty)
            {
                return;
            }

            // ある場合

            // "【東京レース場】ファンミーティング（サイン会）"のようなテキストを更新
            var jobName = _miniCharaWorkGoingJobInfo.MasterJobsReward.Name;
            string miniBgName = string.Empty;
            int miniBgId = _miniCharaWorkGoingJobInfo.BgId;
            var masterMiniBg = MasterDataManager.Instance.masterMiniBg.Get(miniBgId);
            if (masterMiniBg != null)
            {
                miniBgName = masterMiniBg.Name;
            }
            _view.CenterJobGenreName.text = TextId.Jobs408046.Format(jobName, miniBgName);

            // 実行中の興行の数によってテキストの高さ位置が変化
            {
                const float Y_SINGLE_JOB = 36f;
                const float Y_MULTI_JOB  = 75f;

                var namePos = UIUtil.GetAnchoredPosition(_view.CenterJobGenreNameFrame.transform);
                if (IsPageDotEmptyOrSingle)
                {
                    namePos = new Vector2(namePos.x, Y_SINGLE_JOB);
                }
                else
                {
                    namePos = new Vector2(namePos.x, Y_MULTI_JOB);
                }
                UIUtil.SetAnchoredPosition(_view.CenterJobGenreNameFrame.transform, namePos);
            }
        }

        /// <summary>キャンペーンアイコンリストの更新</summary>
        private void RefreshCampaignIconList()
        {
            _view.CampaignIconList.Setup(MasterCampaignData.TargetCategory.Jobs, isLoadToScene: true);

            // 実行中の興行の数によってテキストの高さ位置が変化
            {
                const float Y_SINGLE_JOB = 102f;
                const float Y_MULTI_JOB = 143f;

                var pos = UIUtil.GetAnchoredPosition(_view.CampaignIconList.transform);
                if (!IsPageDotEmptyOrSingle)
                {
                    pos = new Vector2(pos.x, Y_MULTI_JOB);
                }
                else
                {
                    pos = new Vector2(pos.x, Y_SINGLE_JOB);
                }
                UIUtil.SetAnchoredPosition(_view.CampaignIconList.transform, pos);
            }
        }

        /// <summary>タブのセットアップ</summary>
        private void SetupTab()
        {
            int tabIndex = (int)TabType.Normal; // デフォルトは「通常」タブ

            // タブがあるか
            _hasTab = _workJobsData.IsInLimitedStartTerm || (_workJobsData.GetLimitedGoingJobInfo() != null);

            _view.TabFlickToggleGroup.gameObject.SetActiveWithCheck(_hasTab);
            _view.TabFlickableObject.gameObject.SetActiveWithCheck(_hasTab);
            // タブがある場合
            if (_hasTab)
            {
                // 以下のいずれかの条件を満たしていたら「限定」タブを選択した状態で始める
                // ・興行開始確認画面で【限定】興行を開始してこの画面へ遷移した
                // ・興行完了画面で【限定】興行にフォーカスした状態で再興行した
                if (WorkDataManager.Instance.JobsData.LastStartLocalPushTypeIndex >= JobsDefine.LAST_START_LOCAL_PUSH_TYPE_INDEX_LIMITED)
                {
                    tabIndex = (int)TabType.Limited;
                }
                // 以下のいずれかの条件を満たしていたら「通常」タブを選択した状態で始める
                // ・興行開始確認画面で【通常】興行を開始してこの画面へ遷移した
                // ・興行完了画面で【通常】興行にフォーカスした状態で再興行した
                else if (WorkDataManager.Instance.JobsData.LastStartLocalPushTypeIndex > JobsDefine.LAST_START_LOCAL_PUSH_TYPE_INDEX_DEFAULT)
                {
                    tabIndex = (int)TabType.Normal;
                }
                // 端末の「興行TOPで最後に選択したタブ」セーブデータを参照し、「限定タブ」になっていたなら「限定」タブを選択した状態で始める
                else if (_saveLoader.JobsTopLastTabIndex == (int)TabType.Limited)
                {
                    tabIndex = (int)TabType.Limited;
                }

                _view.TabFlickToggleGroup.SelectWithoutNotify(tabIndex);
                _view.TabFlickToggleGroup.SetOnSelectCallback(OnSelectTab);

                // フリック機能のセットアップ
                _view.TabFlickableObject.SetFlickCallback(_view.TabFlickToggleGroup.OnFlick);
                _view.TabFlickableObject.transform.SetAsFirstSibling(); // リストのスクロールやボタン押下を阻害しないようオブジェクトの並び順を入れ替える
            }

            _currentTabIndex = tabIndex;

            SetupLimitedTabNewIcon();
        }

        /// <summary>タブが選択された時の処理</summary>
        private void OnSelectTab(int tabIndex)
        {
            _currentTabIndex = tabIndex;

            // 選択されているタブに対応したリストをアクティブにする
            bool isSelectedLimitedTab = (_currentTabIndex == (int)TabType.Limited);
            _view.NormalTabListRoot.SetActiveWithCheck(!isSelectedLimitedTab);
            _view.LimitedTabList.SetActiveWithCheck(isSelectedLimitedTab);

            // 端末の「興行TOPで最後に選択したタブ」セーブデータにセーブ
            _saveLoader.JobsTopLastTabIndex = tabIndex;
            _saveLoader.Save();

            // ミニキャラや中央部UIをタブに合わせて切り替える

            // 限定タブの場合
            if (_currentTabIndex == (int)TabType.Limited)
            {
                _miniCharaWorkGoingJobInfo = _workJobsData.GetLimitedGoingJobInfo();
                _pageDotCount = (_miniCharaWorkGoingJobInfo != null) ? 1 : 0;
                _pageDotIndex = 0;
                _view.LimitedTabList.JobsTopListItem.OnUpdate();
            }
            // 通常タブの場合
            else
            {
                var workNormalTabGoingJobInfoList = _workJobsData.GetNormalGoingJobInfoList();
                _pageDotCount = (!workNormalTabGoingJobInfoList.IsNullOrEmpty()) ? workNormalTabGoingJobInfoList.Count : 0;
                _pageDotIndex = 0;

                // 以下のいずれかの条件を満たしていたら指定された興行を表示する
                // ・興行開始確認画面で【通常】興行を開始してこの画面へ遷移した
                // ・興行完了画面で【通常】興行にフォーカスした状態で再興行した
                if (!workNormalTabGoingJobInfoList.IsNullOrEmpty() &&
                    WorkDataManager.Instance.JobsData.LastStartLocalPushTypeIndex > JobsDefine.LAST_START_LOCAL_PUSH_TYPE_INDEX_DEFAULT &&
                    WorkDataManager.Instance.JobsData.LastStartLocalPushTypeIndex < JobsDefine.LAST_START_LOCAL_PUSH_TYPE_INDEX_LIMITED)
                {
                    for (int i = 0; i < workNormalTabGoingJobInfoList.Count; i++)
                    {
                        var goingJobInfo = workNormalTabGoingJobInfoList[i];
                        if (goingJobInfo.LocalPushTypeIndex == WorkDataManager.Instance.JobsData.LastStartLocalPushTypeIndex)
                        {
                            _miniCharaWorkGoingJobInfo = goingJobInfo;
                            _pageDotIndex = i;
                            break;
                        }
                    }
                }
                else
                {
                    _miniCharaWorkGoingJobInfo = (!workNormalTabGoingJobInfoList.IsNullOrEmpty()) ? workNormalTabGoingJobInfoList.FirstOrDefault() : null;
                }
                
                foreach (var normalTabItem in _view.NormalTabListLoopScroll.ItemList)
                {
                    if (normalTabItem is PartsJobsTopListItem item)
                    {
                        item.OnUpdate();
                    }
                }
            }

            WorkDataManager.Instance.JobsData.LastStartLocalPushTypeIndex = JobsDefine.LAST_START_LOCAL_PUSH_TYPE_INDEX_DEFAULT;

            RefreshMiniCharaOnSelectTab();
            RefreshCenterUI();
        }

        /// <summary>「限定」タブのNEWアイコンのセットアップ</summary>
        private void SetupLimitedTabNewIcon()
        {
            bool isActive = _workJobsData.IsNeedTopLimitedTabNew;

            // 必要なら生成する
            if (isActive && _limitedTabNewIcon == null)
            {
                _limitedTabNewIcon = UIUtil.CreateNewIcon(_view.LimitedTabNewIconRoot);
            }

            // アクティブ/非アクティブ制御
            if (_limitedTabNewIcon != null)
            {
                _limitedTabNewIcon.SetActiveWithCheck(isActive);
            }
        }

        /// <summary>
        /// リストのセットアップ
        /// </summary>
        private void SetupList()
        {
            // 通常タブのリストをセットアップ
            SetupNormalTabList();

            // 限定タブがあるなら限定タブ内もセットアップ
            if (_hasTab)
            {
                _view.LimitedTabList.Setup(this);
            }
        }

        /// <summary>「通常」タブのリストのセットアップ（※限定興行が開催されていない時も呼ばれます）</summary>
        private void SetupNormalTabList()
        {
            // タブの有無によって ScrollView の Top 位置を変更する
            {
                const float TOP_NO_TAB = -103;
                const float TOP_HAS_TAB = -175;

                var right = _view.NormalTabListScrollViewRectTrans.offsetMax.x;
                var top = _hasTab ? TOP_HAS_TAB : TOP_NO_TAB;

                _view.NormalTabListScrollViewRectTrans.offsetMax = new Vector2(right, top);
            }

            // リスト内の各興行枠のセットアップ
            {
                _view.NormalTabListLoopScroll.Setup<PartsJobsTopListItem>(
                    listCount: JobsDefine.NORMAL_GOING_JOB_MAX,
                    onItemUpdate: item =>
                    {
                        WorkJobsData.GoingJobInfo workGoingJobInfo = _workJobsData.GetNormalGoingJobInfoFromLocalPushTypeIndex(item.ItemIndex);
                        SetupJobsTopListItem(item, workGoingJobInfo);
                    });
            }

            // リストのスクロール位置を一番上にリセット
            _view.NormalTabListLoopScroll.ScrollRect.verticalNormalizedPosition = 1;
        }

        /// <summary>「限定」タブのリストのセットアップ</summary>
        /// <summary>リストの興行枠の更新</summary>
        public void SetupJobsTopListItem(PartsJobsTopListItem listItem, WorkJobsData.GoingJobInfo workGoingJobInfo)
        {
            listItem.OnItemUpdate(listItem.ItemIndex,
                        workGoingJobInfo,
                        OnClickListEmptyButton,
                        OnClickListAbortButton,
                        OnClickDialogCheckCompleteButton,
                        (isGoToResult) =>
                        {
                            _view.StartCoroutine(ReduceTimeJobsComplete(isGoToResult));
                        });
        }

        /// <summary>
        /// リストの空ボタン押下時処理
        /// </summary>
        private void OnClickListEmptyButton(int index)
        {
            bool isLimitedTab = (_currentTabIndex == (int)TabType.Limited); // 限定興行タブか

            // 限定興行タブなら
            if (isLimitedTab)
            {
                // 報酬受取期間の開始時刻を過ぎていたらエラーダイアログを表示する
                var masterJobsLimitedSchedule = _workJobsData.MasterJobsLimitedSchedule;
                if (masterJobsLimitedSchedule != null && !masterJobsLimitedSchedule.IsStartTerm())
                {
                    var resultCode = GallopResultCode.JOBS_LIMITED_JOB_NOT_IN_TERM;
                    var header = TextUtil.GetMasterText(MasterString.Category.ErrorHeader, resultCode);
                    var message = TextUtil.GetMasterText(MasterString.Category.ErrorMessage, resultCode);
                    DialogManager.PushErrorCommon(message, header, null, GallopResultCode.OpenErrorPopupType.GoJobsTop);
                    return;
                }
            }

            // レース場選択画面へ
            JobsUtil.GoToJobsPlaceSelectView(isLimitedTab, index);
        }

        /// <summary>
        /// 「戻る」ボタン押下時処理
        /// </summary>
        public override void OnClickBackButton()
        {
            // 戻り先が指定されているならそこへ遷移
            if (SceneManager.Instance.BackUsingStack())
                return;

#if CYG_DEBUG
            if (_workJobsData.IsDirect)
            {
                SceneManager.GoToDirectScene(SceneDefine.SceneId.JobDirect);
            }
            else
#endif
            {
                // ホームへ
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub);
            }
        }

        /// <summary>
        /// OSのバックキー押下時処理
        /// </summary>
        public override void OnClickOsBackKey()
        {
            // 戻るボタンと同じ挙動
            OnClickBackButton();
        }

        /// <summary>
        /// 画面下部のボタンのセットアップ
        /// </summary>
        private void SetupFooterButtons()
        {
            // 「まとめて短縮」ボタン
            _view.ReduceButton.SetOnClick(OnClickReduceButton);
            UpdateReduceButton();

            // 「まとめて確認」ボタン
            _view.CheckCompleteButton.SetOnClick(OnClickCheckCompleteButton);
            UpdateCheckCompleteButton();
            _isClickedCheckCompleteButton = false;
        }

        /// <summary>
        /// 毎フレーム処理
        /// </summary>
        public override void UpdateView()
        {
            base.UpdateView();

            // ダイアログが開いていたり、通信や画面遷移をしている間は何もしない
            if (DialogManager.IsShowDialog)
                return;
            if (Cute.Http.HttpManager.Instance.IsConnecting)
                return;
            if (SceneManager.Instance.IsRunChangeView)
                return;
            if (UIManager.Instance.IsLockGameCanvas())
                return;
            if (NowLoading.Instance.IsShown())
                return;
            if (DialogManager.IsShowLongTapInfoPop)
                return;

            // 画面下部のボタンの押せる/押せないを更新
            UpdateReduceButton();
            UpdateCheckCompleteButton();
        }
        
        /// <summary>
        /// 画面ハケ処理
        /// </summary>
        public override IEnumerator PlayOutView()
        {
            PlayInOutButton(false);

            PlayInOutCenterUI(false);
            _view.CampaignIconList.PlayOut();

            PlayInOutListFlash(false);

            // 「通常」タブのリストのハケ
            var isCompleteNormalTabOut = false;
            LoopScroll.PlayOutAnimation(_view.NormalTabListLoopScroll.GetActiveItemList(), () => isCompleteNormalTabOut = true);

            // 「限定」タブのリストのハケ
            var isCompleteLimitedTabOut = !_hasTab;
            if (_hasTab)
            {
                _view.LimitedTabList.PlayOutAnimation(() => isCompleteLimitedTabOut = true);
            }

            yield return new WaitUntil(() => (isCompleteNormalTabOut && isCompleteLimitedTabOut));

            yield return base.PlayOutView();
        }

        /// <summary>
        /// 中央部UIのイリハケ処理
        /// </summary>
        private void PlayInOutCenterUI(bool isIn)
        {
            var sequence = DOTween.Sequence();

            var preset = isIn ? TweenAnimation.PresetType.PartsInFade : TweenAnimation.PresetType.PartsOutFade;
            sequence.Join(TweenAnimationBuilder.CreateSequence(_view.CenterUiCanvasGroup.gameObject, preset));
        }

        /// <summary>
        /// リストの項目のA2Uのイリハケ処理
        /// </summary>
        private void PlayInOutListFlash(bool isIn)
        {
            // 「限定」タブのリストのイリハケ
            _view.LimitedTabList.PlayInOutListFlash(isIn);

            // 「通常」タブのリストのイリハケ
            var activeItemList = _view.NormalTabListLoopScroll.GetActiveItemList();
            if (activeItemList.Count <= 0)
                return;

            var sequence = DOTween.Sequence();

            for (int i = 0; i < activeItemList.Count; i++)
            {
                var activeItem = activeItemList[i];
                var listItem = activeItem.transform.GetComponent<PartsJobsTopListItem>();
                if (listItem == null)
                    continue;

                float delay = (i * UIUtil.VIEW_IN_PARTS_INTERVAL);

                sequence.Join(listItem.CreateInOutFlashAnimation(isIn, delay));
            }
        }

        /// <summary>
        /// リストの項目のA2Uの待機モーション再生
        /// </summary>
        private void PlayIdleListFlash()
        {
            // 限定タブ
            _view.LimitedTabList.PlayIdleListFlash();

            // 通常タブ
            var activeItemList = _view.NormalTabListLoopScroll.GetActiveItemList();
            if (activeItemList.Count <= 0)
                return;

            for (int i = 0; i < activeItemList.Count; i++)
            {
                var activeItem = activeItemList[i];
                var listItem = activeItem.transform.GetComponent<PartsJobsTopListItem>();
                if (listItem == null)
                    continue;

                listItem.PlayIdleFlashAnimation();
            }
        }

        /// <summary>
        /// ボタンのイリハケ処理
        /// </summary>
        private void PlayInOutButton(bool isIn)
        {
            var sequence = DOTween.Sequence();

            // TP表示
            {
                _view.TpHeaderCanvasGroup.transform.localPosition = Vector3.zero;
                var preset = isIn ? TweenAnimation.PresetType.PartsInFadeFromRight : TweenAnimation.PresetType.PartsOutFadeFromRight;
                sequence.Join(TweenAnimationBuilder.CreateSequence(_view.TpHeaderCanvasGroup.gameObject, preset));
            }

            // 下部のボタン
            {
                var preset = isIn ? TweenAnimation.PresetType.BottomRightPartsIn : TweenAnimation.PresetType.BottomRightPartsOut;
                sequence.Join(TweenAnimationBuilder.CreateSequence(_view.ReduceButton.gameObject, preset));
                sequence.Join(TweenAnimationBuilder.CreateSequence(_view.CheckCompleteButton.gameObject, preset));
            }

            sequence.Play();
        }

#region まとめて短縮関連

        /// <summary>
        /// 「まとめて短縮」ボタンの押せる/押せないを更新（毎フレーム処理）
        /// </summary>
        private void UpdateReduceButton()
        {
            // 興行中データの中に「まとめて短縮」できるものがあるなら押せる
            bool isInteractable = _workJobsData.AnyCanReduceGoingJobInfo();
            string notificationMessage = isInteractable ? string.Empty : TextId.Jobs408031.Text();
            _view.ReduceButton.SetInteractable(isInteractable);
            _view.ReduceButton.SetNotificationMessage(notificationMessage);
        }

        /// <summary>
        /// 「まとめて短縮」ボタン押下時処理
        /// </summary>
        private void OnClickReduceButton()
        {
            DialogReduceTimeJobs.Open(
            (isGoToResult) =>
            {
                _view.StartCoroutine(ReduceTimeJobsComplete(isGoToResult));
            });
        }

        /// <summary>
        /// 「まとめて短縮」処理後、終了した興行がある場合は興行完了画面へ
        /// </summary>
        private IEnumerator ReduceTimeJobsComplete(bool isGoToResult)
        {
            // 終了した興行がある場合は興行完了画面へ
            if (isGoToResult)
            {
                // 興行完了画面へ
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsResult);
            }
            // 無いなら興行TOPに留まる
            else
            {
                if (!WorkDataManager.Instance.JobsData.GoingJobInfoList.IsNullOrEmpty())
                {
                    // 興行中のデータがあればリスト更新
                    _view.NormalTabListLoopScroll.UpdateActiveItem(); // 通常タブ
                    _view.LimitedTabList.Setup(this);                 // 限定タブ
                }

                // 入力を禁止
                UIManager.Instance.LockGameCanvas();

                // ワイプイン
                bool isCompleted = false;
                NowLoading.Instance.Hide(
                    onComplete: () =>
                    {
                        isCompleted = true;
                    });
                yield return new WaitUntil(() => isCompleted);

                // 入力禁止を解除
                UIManager.Instance.UnlockGameCanvas();
            }
        }

#endregion

#region まとめて確認関連

        /// <summary>
        /// 「まとめて確認」ボタンの押せる/押せないを更新（毎フレーム処理）
        /// </summary>
        private void UpdateCheckCompleteButton()
        {
            // 興行中データの中に「まとめて確認」できるものがあるなら押せる
            bool isInteractable = _workJobsData.AnyCanCheckCompleteGoingJobInfo();
            string notificationMessage = isInteractable ? string.Empty : TextId.Jobs408032.Text();
            _view.CheckCompleteButton.SetInteractable(isInteractable);
            _view.CheckCompleteButton.SetNotificationMessage(notificationMessage);
        }

        /// <summary>
        /// 「まとめて確認」ボタン押下時処理
        /// </summary>
        private void OnClickCheckCompleteButton()
        {
            // 連打防止
            if (_isClickedCheckCompleteButton)
                return;
            _isClickedCheckCompleteButton = true;

            _view.StartCoroutine(CheckCompleteExecCoroutine());
        }

        /// <summary>
        /// 「まとめて確認」実行コルーチン
        /// </summary>
        private IEnumerator CheckCompleteExecCoroutine()
        {
            // まとめて確認API通信
            {
                bool isSuccess = false;

                JobsUtil.SendJobsCheckCompleteApiForCheck(
                    onSuccess: () =>
                    {
                        isSuccess = true;
                    });

                yield return new WaitUntil(() => isSuccess);
            }

            if (_workJobsData.ResultJobInfoList.IsNullOrEmpty())
            {
                // 完了した興行が無いならここで終わり（※ここには来ないはず）
                yield break;
            }

            // 入力を禁止
            UIManager.Instance.LockGameCanvas();

            // 全画面ワイプアウト（ミニキャラを含めて全画面を一度隠す）
            {
                bool isCompleted = false;

                JobsUtil.ShowNowLoadingNormalWipeOut(
                    onComplete: () =>
                    {
                        isCompleted = true;
                    });

                yield return new WaitUntil(() => isCompleted);
            }

            // 入力禁止を解除
            UIManager.Instance.UnlockGameCanvas();

            // 興行完了画面へ
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsResult);
        }

#endregion

#region 興行中止関連

        /// <summary>
        /// リストの「中止」ボタン押下時処理
        /// </summary>
        private void OnClickListAbortButton(WorkJobsData.GoingJobInfo workGoingJobInfo)
        {
            if (workGoingJobInfo == null)
                return;
            if (!workGoingJobInfo.IsValid())
                return;

            // 「興行を中止しますか？」確認ダイアログを開く
            var headerText = TextId.Jobs408027.Text();
            var bodyText = TextId.Jobs408028.Text();
            var footerText = TextId.Jobs408035.Text();
            var dialogData = new DialogCommon.Data();
            dialogData.SetSimpleTwoButtonMessage(headerText, bodyText);
            dialogData.FooterText = footerText;
            dialogData.IsFooterNotificationText = true;
            dialogData.RightButtonCallBack = (_) => { OnClickAbortDialogRightButton(workGoingJobInfo); };
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// 「興行を中止しますか？」確認ダイアログでOKを押した時の処理
        /// </summary>
        private void OnClickAbortDialogRightButton(WorkJobsData.GoingJobInfo workGoingJobInfo)
        {
            _view.StartCoroutine(AbortExecCoroutine(workGoingJobInfo));
        }

        /// <summary>
        /// 興行中止実行コルーチン
        /// </summary>
        private IEnumerator AbortExecCoroutine(WorkJobsData.GoingJobInfo workGoingJobInfo)
        {
            // 興行中止API通信
            {
                bool isSuccess = false;

                int jobsRewardId = workGoingJobInfo.JobsRewardId;
                int cardId = workGoingJobInfo.CardIdList[0];

                var req = new JobsAbortRequest();
                req.jobs_reward_id = jobsRewardId;
                req.card_id = cardId;
                req.Send((response) =>
                {
                    // 全国興行のワークデータへ反映
                    WorkDataManager.Instance.JobsData.Apply(response.data, jobsRewardId, cardId);

                    isSuccess = true;
                });

                yield return new WaitUntil(() => isSuccess);
            }

            // 「興行を中止しました」ダイアログを開く
            {
                bool isDestroyedDialog = false;

                var headerText = TextId.Jobs408027.Text();
                var bodyText = TextId.Jobs408030.Text();
                var dialogData = new DialogCommon.Data();
                dialogData.SetSimpleOneButtonMessage(headerText, bodyText);
                dialogData.DestroyCallBack = () =>
                {
                    // 全てのダイアログを閉じる
                    DialogManager.RemoveAllDialog();
                    isDestroyedDialog = true;
                };
                DialogManager.PushDialog(dialogData);

                yield return new WaitUntil(() => isDestroyedDialog);
            }

            // 入力を禁止
            UIManager.Instance.LockGameCanvas();

            // 全画面フェードアウト（ミニキャラを含めて全画面を一度隠す）
            {
                bool isCompleted = false;

                NowLoading.Instance.Show(NowLoading.Type.NowLoadingPlain,
                    onComplete: () =>
                    {
                        isCompleted = true;
                    });

                yield return new WaitUntil(() => isCompleted);
            }

            // 入力禁止を解除
            UIManager.Instance.UnlockGameCanvas();

            // 興行TOPへ再遷移
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsTop, forceChange: true); // 今いるViewに遷移するにはforceChangeを立てる必要がある
        }
        
        /// <summary>
        /// 詳細ダイアログでの「確認」ボタン押下時処理
        /// </summary>
        private void OnClickDialogCheckCompleteButton(WorkJobsData.GoingJobInfo workGoingJobInfo)
        {
            // 連打防止
            if (_isClickedCheckCompleteButton)
                return;
            _isClickedCheckCompleteButton = true;

            _view.StartCoroutine(CheckCompleteExecDialogCoroutine(workGoingJobInfo));
        }
        
        /// <summary>
        /// 詳細ダイアログでの単体確認実行コルーチン
        /// </summary>
        private IEnumerator CheckCompleteExecDialogCoroutine(WorkJobsData.GoingJobInfo workGoingJobInfo)
        {
            // 消費TP0で該当のIDのみ単体で送る
            {
                bool isSuccess = false;
                var reduceTpArray = new JobsReduceTp[1];
                reduceTpArray[0] = new JobsReduceTp();
                reduceTpArray[0].jobs_reward_id = workGoingJobInfo.JobsRewardId;
                reduceTpArray[0].card_id = workGoingJobInfo.CardIdList[0];
                reduceTpArray[0].use_tp_value = 0;
                JobsUtil.SendJobsCheckCompleteApiForReduce(reduceTpArray, null, () =>
                {
                    isSuccess = true;
                });
                yield return new WaitUntil(() => isSuccess);
            }
            
            var workJobsData = WorkDataManager.Instance.JobsData;
            if (workJobsData.ResultJobInfoList.IsNullOrEmpty())
            {
                // 完了した興行が無いならここで終わり（※ここには来ないはず）
                yield break;
            }

            // 入力を禁止
            UIManager.Instance.LockGameCanvas();

            // 全画面ワイプアウト（ミニキャラを含めて全画面を一度隠す）
            {
                bool isCompleted = false;

                JobsUtil.ShowNowLoadingNormalWipeOut(
                    onComplete: () =>
                    {
                        isCompleted = true;
                    });

                yield return new WaitUntil(() => isCompleted);
            }

            // 入力禁止を解除
            UIManager.Instance.UnlockGameCanvas();

            // 興行完了画面へ
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsResult);
        }

#endregion

#region 3Dミニキャラ関連

        /// <summary>
        /// 3Dミニキャラ：更新（タブ選択時）
        /// </summary>
        private void RefreshMiniCharaOnSelectTab()
        {
            bool isLogoActive = (_miniCharaWorkGoingJobInfo == null);
            RefreshLogo(isLogoActive);
            if (isLogoActive)
            {
                return;
            }

            JobsUtil.SetDirectorChannel(_miniCharaWorkGoingJobInfo.LocalPushTypeIndex);
            RefreshMiniChara();
        }

        /// <summary>
        /// 3Dミニキャラ：更新
        /// </summary>
        private void RefreshMiniChara()
        {
            var viewController = SceneManager.Instance.GetCurrentViewController<JobsHubViewController>();

            if (viewController == null || _miniCharaWorkGoingJobInfo == null)
            {
                return;
            }

            // 背景切り替え
            viewController.Director.ChangeBg(_miniCharaWorkGoingJobInfo.BgId, true);

            // 背景の天球のテクスチャ切り替え
            {
                var masterMiniBg = MasterDataManager.Instance.masterMiniBg.Get(_miniCharaWorkGoingJobInfo.BgId);
                int envId = (masterMiniBg != null && masterMiniBg.EnvId > -1) ? masterMiniBg.EnvId : JobsDefine.MINI_CHARA_ENV_ID_DEFAULT;
                viewController.Director.ChangeBgSkyTexture(envId);
            }
        }

        /// <summary>
        /// 3Dミニキャラ：キャラ数取得
        /// </summary>
        public int GetMiniCharaNum()
        {
            int num = 0;

            if (IsMiniCharaEmpty)
                return num;

            foreach (var goingJobInfo in _workJobsData.GoingJobInfoList)
            {
                num += goingJobInfo.CardIdList.Count;
            }

            return num;
        }

        /// <summary>
        /// 3Dミニキャラ：MiniCharaData取得
        /// </summary>
        public IMiniCharaData GetMiniCharaData(int index)
        {
            if (index >= GetMiniCharaNum())
                return null;

            var groupIndex = index / JobsDefine.CHARA_NUM_LIMIT_TO_1_JOB;
            var charaIndex = index % JobsDefine.CHARA_NUM_LIMIT_TO_1_JOB;
            return _workJobsData.GoingJobInfoList[groupIndex].MiniCharaDataList[charaIndex];
        }

        /// <summary>
        /// 3Dミニキャラの左矢印ボタン押下時処理
        /// </summary>
        private void OnClickMiniCharaLeftButton()
        {
            _pageDotIndex--;
            if (_pageDotIndex < 0)
                _pageDotIndex = _pageDotCount - 1;

            _miniCharaWorkGoingJobInfo = _workJobsData.GetNormalGoingJobInfo(_pageDotIndex);

            JobsUtil.SetDirectorChannel(_miniCharaWorkGoingJobInfo.LocalPushTypeIndex);
            RefreshMiniChara();
            RefreshCenterUI();

            _view.PageDots.UpdateDots(_pageDotIndex);
        }

        /// <summary>
        /// 3Dミニキャラの右矢印ボタン押下時処理
        /// </summary>
        private void OnClickMiniCharaRightButton()
        {
            _pageDotIndex++;
            if (_pageDotIndex >= _pageDotCount)
                _pageDotIndex = 0;

            _miniCharaWorkGoingJobInfo = _workJobsData.GetNormalGoingJobInfo(_pageDotIndex);

            JobsUtil.SetDirectorChannel(_miniCharaWorkGoingJobInfo.LocalPushTypeIndex);
            RefreshMiniChara();
            RefreshCenterUI();

            _view.PageDots.UpdateDots(_pageDotIndex);
        }

        /// <summary>
        /// 3Dミニキャラ：ステート変更
        /// </summary>
        private IEnumerator ChangeDirectorState()
        {
            var viewController = SceneManager.Instance.GetCurrentViewController<JobsHubViewController>();

            if (viewController != null)
            {
                bool isWorkGoingJobInfoListEmpty = _workJobsData.GoingJobInfoList.IsNullOrEmpty(); // 実行中の興行が無い状態か、ひいてはミニキャラの演出を表示する可能性が無い状態か
                var directorState = isWorkGoingJobInfoListEmpty
                                  ? MiniDirectorDefines.DirectorState.JobSelect
                                  : MiniDirectorDefines.DirectorState.JobRunning;

                yield return viewController.ChangeStateCoroutine(directorState);
            }

            yield break;
        }

        /// <summary>
        /// 3Dミニキャラ：非表示
        /// </summary>
        private void SetVisibleDirector(bool isVisible)
        {
            var viewController = SceneManager.Instance.GetCurrentViewController<JobsHubViewController>();

            if (viewController != null)
            {
                viewController.Director.SetVisible(isVisible);
            }
        }

#endregion

        /// <summary>
        /// 左右フリックした時の処理
        /// </summary>
        public void OnFlick(FlickHandler.FlickStatus status)
        {
            if (status.IsRight)
            {
                OnClickMiniCharaLeftButton();
            }
            else if (status.IsLeft)
            {
                OnClickMiniCharaRightButton();
            }
        }

        /// <summary>
        /// 必要なら端末セーブデータをリセットする
        /// </summary>
        private void ResetApplicationSaveDataIfNeeded()
        {
            // 「興行TOPで最後に選択したタブ」セーブデータについて、新しい限定興行が開催されるたびにリセットさせる

            // 限定興行が開催中なら
            if (_workJobsData.IsInLimitedTerm)
            {
                var currentLimitedScheduleId = _workJobsData.JobsLimitedScheduleId; // 現在開催している限定興行のID
                var lastLimitedScheduleId = _saveLoader.JobsTopLastLimitedScheduleId; // 「過去最後に興行TOPで表示した限定興行」のID

                // 新しい限定興行が開催されたら
                if (currentLimitedScheduleId != lastLimitedScheduleId)
                {
                    // 「興行TOPで最後に選択したタブ」セーブデータをリセット
                    _saveLoader.JobsTopLastTabIndex = 0;
                }

                _saveLoader.JobsTopLastLimitedScheduleId = currentLimitedScheduleId;
            }

#if CYG_DEBUG
            if (_workJobsData.IsDirect) // 全国興行ダイレクトから来た場合は表示する興行が限定興行かどうかでタブを自動切り替えさせる
            {
                var workLimitedGoingJobInfo = _workJobsData.GetLimitedGoingJobInfo();
                _saveLoader.JobsTopLastTabIndex = (workLimitedGoingJobInfo != null) ? (int)TabType.Limited : (int)TabType.Normal;
                _saveLoader.JobsTopLastLimitedScheduleId = 0;
            }
#endif
        }

        /// <summary>
        /// このViewを抜けた時の処理
        /// </summary>
        public override IEnumerator EndView()
        {
            yield return base.EndView();

            // 「興行無しの時の背景」について、全国興行内の画面遷移なら破棄する
            var nextSceneId = SceneManager.Instance.GetNextSceneId();
            if (_emptyBg != null && nextSceneId == SceneDefine.SceneId.Jobs)
            {
                _emptyBg.Release();
                _emptyBg = null;
            }
        }

        /// <summary>
        /// HubViewを抜けた時の処理
        /// </summary>
        public override IEnumerator FinalizeView()
        {
            yield return base.FinalizeView();

            // 「興行無しの時の背景」を破棄
            if (_emptyBg != null)
            {
                _emptyBg.Release();
                _emptyBg = null;
            }
        }

#region Debug
#if CYG_DEBUG

        /// <summary>
        /// デバッグ用：ダミーデータの興行完了画面へ遷移する
        /// ・興行完了画面内の表示の調整など、頻繁に遷移したい場合に使ってください。
        /// ・OnClickListEmptyButton()あたりに仕込むと使いやすいと思います。
        /// </summary>
        private void Debug_GoToDummyResultView()
        {
            _workJobsData.Debug_CreateDummyResultJobInfo();
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.JobsResult);
        }

#endif
#endregion

    }
}
