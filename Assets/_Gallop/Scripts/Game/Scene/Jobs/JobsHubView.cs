using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cute.Http;
using static Gallop.StaticVariableDefine.Jobs.JobsHubViewController;

namespace Gallop
{
    /// <summary>
    /// 全国興行：HubView
    /// </summary>
    [AddComponentMenu("")]
    public class JobsHubView : ViewBase
    {
        [SerializeField] private MiniDirectorUI _directorUI;
        public MiniDirectorUI DirectorUI => _directorUI;
    }

    public class JobsHubViewController : HubViewControllerBase
    {
        public MiniDirector Director { get; private set; }
        private MasterMiniBg.MiniBg[] _miniBgArray = null;    // 事前に演出に必要なデータを落とす

        WorkJobsData _workJobsData => WorkDataManager.Instance.JobsData;


        /// <summary>
        /// 子ビュー配列取得
        /// </summary>
        public override SceneDefine.ViewId[] GetChildViewIdArray()
        {
            return VIEW_ID_ARRAY;
        }

        /// <summary>
        /// 子ビューに渡すViewInfo選定
        /// </summary>
        public override IViewInfo GetChildViewInfo(SceneDefine.ViewId viewId, IViewInfo viewInfo)
        {
            return viewInfo;
        }

        /// <summary>
        /// HubViewに入った時の初期化処理
        /// </summary>
        public override IEnumerator InitializeView()
        {
            yield return base.InitializeView();

            InitializeDirector();
        }

        /// <summary>
        ///  Loading明け前用のUpdate関数
        /// </summary>
        public override void UpdateViewBeforeLoadingOut()
        {
            UpdateView();
            base.UpdateViewBeforeLoadingOut();
        }

        /// <summary>
        ///  Loading明け前用のLateUpdate関数
        /// </summary>
        public override void LateUpdateViewBeforeLoadingOut()
        {
            //145997 レース場選択から興行TOPに戻る時のロード明けの前にLateupdateが呼ばないとキャラの立ち位置の反映ができないため、
            //ここで呼ぶように調整
            LateUpdateView();
            base.LateUpdateViewBeforeLoadingOut();
        }

        /// <summary>
        ///Loading入り後用のUpdate関数
        ///147618 Update->Lateupdateの処理順を保証したい
        /// </summary>
        public override void UpdateViewBeforeLoadingIn()
        {
            UpdateView();
            base.UpdateViewBeforeLoadingIn();
        }

        /// <summary>
        ///  Loading入り後用のLateUpdate関数
        /// </summary>
        public override void LateUpdateViewBeforeLoadingIn()
        {
            LateUpdateView();
            base.LateUpdateViewBeforeLoadingIn();
        }

        /// <summary>
        /// 更新処理
        /// </summary>
        public override void UpdateView()
        {
            base.UpdateView();

            Director.OnUpdate();
        }

        /// <summary>
        /// 後更新処理
        /// </summary>
        public override void LateUpdateView()
        {
            base.LateUpdateView();

            Director.OnLateUpdate();
        }

        /// <summary>
        /// 終了
        /// </summary>
        public override IEnumerator FinalizeView()
        {
            yield return base.FinalizeView();

            var scene = GetSceneController<JobsSceneController>();
            if (scene != null)
            {
                scene.DestroyDirector();
            }
        }

        /// <summary>
        /// DL事前登録
        /// </summary>
        public override IEnumerator PreRegisterDownload()
        {
            // DLが必要な背景を取得
            List<int> miniBgIdList = new List<int>();
            var masterJobsRewardList = MasterDataManager.Instance.masterJobsReward.GetNormalJobList(); // 通常興行の背景
            int jobsLimitedScheduleId = _workJobsData.JobsLimitedScheduleId;
            if (jobsLimitedScheduleId != 0)
            {
                masterJobsRewardList.AddRange(MasterDataManager.Instance.masterJobsReward.GetListWithLimitedScheduleIdOrderByIdAsc(jobsLimitedScheduleId)); // 限定興行の背景
            }
#if CYG_DEBUG
            if (_workJobsData.IsDirect) // 全国興行ダイレクトから来た場合は全ての限定興行の背景をDL
            {
                masterJobsRewardList.AddRange(MasterDataManager.Instance.masterJobsReward.Debug_GetLimitedJobList());
            }
#endif
            foreach (var masterJobsReward in masterJobsRewardList)
            {
                var masterJobsGenre = masterJobsReward.MasterJobsGenre;
                if (masterJobsGenre == null)
                    continue;

                AddMiniBgId(masterJobsGenre.MiniBgId1);
                AddMiniBgId(masterJobsGenre.MiniBgId2);
                AddMiniBgId(masterJobsGenre.MiniBgId3);
                AddMiniBgId(masterJobsGenre.MiniBgId4);
                AddMiniBgId(masterJobsGenre.MiniBgId5);
            }
            var num = miniBgIdList.Count;
            _miniBgArray = new MasterMiniBg.MiniBg[num];
            for (int i = 0; i < num; i++)
            {
                _miniBgArray[i] = MasterDataManager.Instance.masterMiniBg.Get(miniBgIdList[i]);
            }

            yield return base.PreRegisterDownload();

            // miniBgIdListにminiBgIdを追加
            void AddMiniBgId(int miniBgId)
            {
                if (miniBgId > 0 && !miniBgIdList.Contains(miniBgId))
                {
                    miniBgIdList.Add(miniBgId);
                }
            }
        }

        /// <summary>
        /// DL登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // ミニキャラ演出
            RegistDowloadDirector(register);

            // レース場サムネイル画像
            var masterRaceTrackCollection = MasterDataManager.Instance.masterRaceTrack.dictionary.Values;
            foreach (var masterRaceTrack in masterRaceTrackCollection)
            {
                var raceTrackId = masterRaceTrack.Id;
                var path = ResourcePath.GetJobsRaceTrackThumbnailImagePath(raceTrackId);
                register.RegisterPathWithoutInfo(path);
            }
            // レース場サムネイル画像のマスク画像
            register.RegisterPathWithoutInfo(ResourcePath.JOBS_RACE_TRACK_MASK_TOP_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.JOBS_RACE_TRACK_MASK_PLACE_SELECT_PATH);

            // 画面遷移時の汎用ワイプアウトのSE
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_UI_JOBS_CUTIN);

            // 時計ワイプ
            NowLoadingWipeClock.RegisterDownload(register);

            base.RegisterDownload(register);
        }

        /// <summary>
        /// 演出用リソースDL
        /// </summary>
        private void RegistDowloadDirector(DownloadPathRegister register)
        {
            if (_miniBgArray == null)
            {
                Debug.LogError("DL未登録");
                base.RegisterDownload(register);
                return;
            }

            // 演出用リソースをダウンロード
            MiniDirector.RegisterDownload(register, bgs: _miniBgArray, envIds: new int[] { 0 });
            {
                // 背景の天球のテクスチャ（演出の種類によって差し替えるので表示する可能性のある物を落としておく）
                {
                    // デフォルト
                    var textureName = ResourcePath.GetMiniSkyTextureName(JobsDefine.MINI_CHARA_ENV_ID_DEFAULT);
                    var texturePath = ResourcePath.GetMiniSkyTexturePath(textureName);
                    register.RegisterPathWithoutInfo(texturePath);
                }
                {
                    // 差し替え用
                    foreach (var masterMiniBg in _miniBgArray)
                    {
                        if (masterMiniBg.EnvId <= -1)
                            continue;

                        var textureName = ResourcePath.GetMiniSkyTextureName(masterMiniBg.EnvId);
                        var texturePath = ResourcePath.GetMiniSkyTexturePath(textureName);
                        register.RegisterPathWithoutInfo(texturePath);
                    }
                }
            }
        }

        /// <summary>
        /// 演出初期化
        /// </summary>
        private void InitializeDirector()
        {
            if (!(_view is JobsHubView hubView))
            {
                return;
            }

            var scene = GetSceneController<JobsSceneController>();
            if (scene == null)
                return;

            Director = scene.CreateDirector();

            if (Director == null || Director.State != MiniDirectorDefines.DirectorState.NonInitialized)
                return;

            var defaultBgId = JobsDefine.DEFAULT_BG_ID;
            var directorState = MiniDirectorDefines.DirectorState.JobSelect;
            for (int i = 0; i < JobsDefine.GOING_JOB_MAX; i++)
            {
                var jobInfo = WorkDataManager.Instance.JobsData.GetGoingJobInfo(i);
                if (jobInfo != null)
                {
                    directorState = MiniDirectorDefines.DirectorState.JobRunning;
                    break;
                }
            }

            var bgParam = new MiniDirectorBgParam(_miniBgArray, 0, 0, defaultBgId);
            var charaParam = new MiniDirectorCharaParam(GetMiniCharaNum, GetMiniCharaData, bgPriority: MiniDirectorDefines.CharaAddBgPriority.FixedBg, lotteryType: MiniDirectorDefines.CommandLotteryType.Job);
            var cameraParam = new MiniDirectorCameraParam();
            cameraParam.EnableScroll = false;
            cameraParam.IsOverrideResolution = true;
            // 全国興行のレンダーテクスチャイメージ初期値、
            // UI側はJobVeiw/DirectorUI/RootのAnchorsで調整している
            cameraParam.ResolutionSize = new Vector2(JobsDefine.RENDER_IMAGE_RESOLUTION_SIZE_X, JobsDefine.RENDER_IMAGE_RESOLUTION_SIZE_Y);
            Director.OnInitialize(hubView.DirectorUI, bgParam, charaParam, cameraParam, directorState, viewId: JobsDefine.LOAD_ON_VIEW_ID);
        }

        private int GetMiniCharaNum()
        {
            return ChildCurrentController switch
            {
                JobsTopViewController topViewCtrl => topViewCtrl.GetMiniCharaNum(),
                JobsGenreSelectViewController genreSelectViewCtrl => genreSelectViewCtrl.GetMiniCharaNum(),
                JobsCharaSelectViewController charaSelectViewCtrl => charaSelectViewCtrl.GetMiniCharaNum(),
                JobsConfirmViewController confirmViewCtrl => confirmViewCtrl.GetMiniCharaNum(),
                _ => 0,
            };
        }

        private IMiniCharaData GetMiniCharaData(int index)
        {
            return ChildCurrentController switch
            {
                JobsTopViewController topViewCtrl => topViewCtrl.GetMiniCharaData(index),
                JobsGenreSelectViewController genreSelectViewCtrl => genreSelectViewCtrl.GetMiniCharaData(index),
                JobsCharaSelectViewController charaSelectViewCtrl => charaSelectViewCtrl.GetMiniCharaData(index),
                JobsConfirmViewController confirmViewCtrl => confirmViewCtrl.GetMiniCharaData(index),
                _ => null,
            };
        }

        /// <summary>
        /// ステート変更
        /// </summary>
        public IEnumerator ChangeStateCoroutine(MiniDirectorDefines.DirectorState state)
        {
            // 切り替わり中の入力を禁止
            UIManager.Instance.LockGameCanvas();

            // 演出ステート遷移
            Director.ChangeState(new MiniDirectorCommand(state), false);

            yield return null;  // 演出更新を待つ

            // 入力禁止解除
            UIManager.Instance.UnlockGameCanvas();
        }
    }
}
