using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 全国興行：まとめて短縮ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogReduceTimeJobs : DialogReduceTimeJobsBase
    {
        [field: Header("一覧")]
        
        /// <summary>興行一覧のリスト部分</summary>
        [SerializeField] private LoopScroll _loopScroll;
        
        [field: Header("下部")]
        
        /// <summary>リセットボタン</summary>
        [SerializeField] protected ButtonCommon _resetButton;
        
        /// <summary>自動配分ボタン</summary>
        [SerializeField] private ButtonCommon _distributionButton;

        private WorkJobsData _workJobsData => WorkDataManager.Instance.JobsData;
        /// <summary>興行中データ一覧のworkデータ</summary>
        private List<WorkJobsData.GoingJobInfo> _workGoingJobInfoList;

        /// <summary>ダイアログ関係</summary>
        public override DialogCommon.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(System.Action<bool> onComplete)
        {
            var component = LoadAndInstantiatePrefab<DialogReduceTimeJobs>(ResourcePath.DIALOG_JOB_REDUCE_TIME_JOBS_PATH);
            var data = component.CreateDialogData();
            data.LeftButtonCallBack = (dialog) => dialog.Close();
            data.RightButtonCallBack = (dialog) => { component.OnClickRightButton(dialog, onComplete); };
            data.AutoClose = false;
            component._dialog = DialogManager.PushDialog(data);
            component.SetupWorkTp();
            component.Setup();
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup()
        {
            // 「興行中データ一覧のworkデータ」を作成
            _workGoingJobInfoList = CreateWorkGoingJobInfoList();

            // TP
            SetupTp();
            
            // アイテム
            SetupItem();
            
            // 興行一覧
            SetupJobsList();
            
            // 下部
            SetupLower();
            
            // リセットボタン
            _resetButton.SetOnClick(OnClickResetButton);
            _resetButton.SetInteractable(false);
            
            // 自動配分ボタン
            _distributionButton.SetOnClick(OnClickDistributionButton);

            // ボタン座標送信
            SetupSendBottomButtonPos(TempData.ClickButtonTempData.ButtonDataLogType.JobsCompleteMulti);
        }

        /// <summary>
        /// 「興行中データ一覧のworkデータ」を作成
        /// </summary>
        private List<WorkJobsData.GoingJobInfo> CreateWorkGoingJobInfoList()
        {
            var workGoingJobInfoList = new List<WorkJobsData.GoingJobInfo>();

            var workLimitedGoingJobInfo = _workJobsData.GetLimitedGoingJobInfo();
            if (workLimitedGoingJobInfo != null)
            {
                workGoingJobInfoList.Add(workLimitedGoingJobInfo);
            }

            var workNormalGoingJobInfoList = _workJobsData.GetNormalGoingJobInfoList();
            if (!workNormalGoingJobInfoList.IsNullOrEmpty())
            {
                workGoingJobInfoList.AddRange(workNormalGoingJobInfoList);
            }

            return workGoingJobInfoList;
        }

        /// <summary>
        /// 時間経過したらリストのポイントの計算し直す
        /// </summary>
        protected override void UpdatePartsUI()
        {
            bool isUpdateTp = false;
            UpdateScrollList((listItem) =>
            {
                var listItemParam = _workGoingJobInfoList[listItem.ItemIndex];
                var needTp = listItemParam.GetNeedTp();
                var useTp = listItem.GetUseTp();
                if (needTp < useTp)
                {
                    listItem.UpdateValue(needTp);
                    UpdateAfterTp();
                    isUpdateTp = true;
                }
            });

            if (isUpdateTp)
            {
                OnClickButton();
            }
        }
        
        /// <summary>
        /// 興行一覧のセットアップ
        /// </summary>
        private void SetupJobsList()
        {
            // LoopScrollのセットアップ
            var itemData = GetUseRecoveryItem();
            var itemAddTp = _itemCheckBox.Toggle.isOn ? itemData.ItemNum * itemData.GetRecoveryValue() : 0;
            _loopScroll.Setup(
                _workGoingJobInfoList.Count,
                onItemUpdate: (item) =>
                {
                    if (item is PartsReduceTimeJobsListItem listItem)
                    {
                        var listItemParam = _workGoingJobInfoList[item.ItemIndex];
                        listItem.OnItemUpdate(listItemParam, OnUpdateListExchangeValue, OnClickButton, _loopScroll, itemAddTp);
                    }
                });
        }
        
        /// <summary>
        /// 使用TPを取得する
        /// </summary>
        protected override int GetTotalUseTp()
        {
            var count = 0;
            foreach (var item in _loopScroll.ItemList)
            {
                if (item is PartsReduceTimeJobsListItem listItem)
                {
                    count += listItem.GetUseTp();
                }
            }

            return count;
        }

        /// <summary>
        /// 「+」「-」ボタンを押した時のコールバック
        /// </summary>
        public override void OnClickButton()
        {
            base.OnClickButton();
            
            _resetButton.SetInteractable(GetTotalUseTp() > 0);
        }
        
        /// <summary>
        /// 入力フィールドの最大値更新
        /// </summary>
        protected override void UpdateMaxValue(int afterTp)
        {
            UpdateScrollList((listItem) =>
            {
                listItem.UpdateMaxValue(afterTp);
            });
        }
        
        /// <summary>
        /// リセットボタン押下時
        /// </summary>
        private void OnClickResetButton()
        {
            // 全部0にする
            UpdateScrollList((listItem) =>
            {
                listItem.UpdateValue(0);
            });
            
            OnClickButton();
        }
        
        /// <summary>
        /// リスト全部に処理を実行したい
        /// </summary>
        private void UpdateScrollList(System.Action<PartsReduceTimeJobsListItem> action)
        {
            foreach (var item in _loopScroll.ItemList)
            {
                if (item is PartsReduceTimeJobsListItem listItem)
                {
                    if (listItem.ItemIndex != listItem.GetInvalidIndex())
                    {
                        action?.Invoke(listItem);
                    }
                }
            }
        }
        
        /// <summary>
        /// 通信処理
        /// </summary>
        protected override IEnumerator RightButtonCoroutine(DialogCommon dialog, System.Action<bool> onComplete)
        {
            var workGoingJobInfoList = _workGoingJobInfoList;

            // 通信準備
            var reduceTpList = new List<JobsReduceTp>();
            var tempLocalPushTypeIndexList = new List<int>();
            UpdateScrollList((listItem) =>
            {
                var jobsReduceTp = new JobsReduceTp();
                jobsReduceTp.jobs_reward_id = workGoingJobInfoList[listItem.ItemIndex].JobsRewardId;
                // 興行参加ウマ娘は重複しない為、1人分かれば興行は特定できる　ということでリストの先頭のIDを入れる
                jobsReduceTp.card_id = workGoingJobInfoList[listItem.ItemIndex].CardIdList[0];
                jobsReduceTp.use_tp_value = listItem.GetUseTp();
                reduceTpList.Add(jobsReduceTp);
                tempLocalPushTypeIndexList.Add(workGoingJobInfoList[listItem.ItemIndex].LocalPushTypeIndex);
            });

            // 入力を禁止
            UIManager.Instance.LockGameCanvas();
            
            // このダイアログを閉じる -> 全画面ワイプアウト（ミニキャラを含めて全画面を一度隠す）
            bool isCompleted = false;
            dialog.Close(() =>
            {
                NowLoadingWipeClock.Show(
                    onComplete: () =>
                    {
                        isCompleted = true;
                    });
            });
            yield return new WaitUntil(() => isCompleted);

            // 入力禁止を解除
            UIManager.Instance.UnlockGameCanvas();

            // アイテム
            var useItemInfo = GetUseItemInfo();
            
            // 通信
            bool isSuccess = false;
            JobsUtil.SendJobsCheckCompleteApiForReduce(reduceTpList.ToArray(), useItemInfo,
                onSuccess: () =>
                {
                    isSuccess = true;
                });
            yield return new WaitUntil(() => isSuccess);

            workGoingJobInfoList = CreateWorkGoingJobInfoList(); // 通信で変化している可能性があるので再取得
            {
                // 時間短縮した興行のlocal push時間を変更する
                if (!workGoingJobInfoList.IsNullOrEmpty())
                {
                    for (int i = 0, count = workGoingJobInfoList.Count; i < count; i++)
                    {
                        if (tempLocalPushTypeIndexList.Contains(workGoingJobInfoList[i].LocalPushTypeIndex))
                        {
                            PushNotificationManager.Instance.ReqJobsMax(i);
                        }
                    }
                }
                
                var resultList = WorkDataManager.Instance.JobsData.ResultJobInfoList;
                if (resultList.IsNullOrEmpty())
                {
                    // 完了した興行が無いならここで終わり
                    onComplete?.Invoke(false);
                    yield break;
                }

                // 時間短縮して完了した興行のlocal pushをキャンセルする
                for (int i = 0, count = resultList.Count; i < count; i++)
                {
                    if (tempLocalPushTypeIndexList.Contains(resultList[i].LocalPushTypeIndex))
                    {
                        var deleteIndex = LocalPushDefine.LocalPushType.Job1 + resultList[i].LocalPushTypeIndex;
                        PushNotificationManager.Instance.DeleteLocalPushes(deleteIndex);
                    }
                }

                // 完了した興行がある場合は次へ
                onComplete?.Invoke(true);
            }
        }
        
        /// <summary>
        /// 自動配分
        /// </summary>
        private void OnClickDistributionButton()
        {
            var itemData = GetUseRecoveryItem();
            var itemAddTp = _itemCheckBox.Toggle.isOn ? itemData.ItemNum * itemData.GetRecoveryValue() : 0;
            var currentTp = _workTrainerPoint.CurrentTp + itemAddTp - GetTotalUseTp();
            var listCount = _workGoingJobInfoList.Count;

            var needTps = new int[listCount];
            // 必要値を計算しておく
            UpdateScrollList((listItem) =>
            {
                needTps[listItem.ItemIndex] = _workGoingJobInfoList[listItem.ItemIndex].GetNeedTp() - listItem.GetUseTp();
            });
            
            // TPを均等に配分+TPに余裕があるなら必要なところに分配
            var splitTps = new int[listCount];
            int index = 0;
            int breakCount = 0;
            while(currentTp > 0)
            {
                if (splitTps[index] < needTps[index])
                {
                    splitTps[index]++;
                    breakCount = 0;
                    currentTp--;
                }
                else
                {
                    breakCount++;
                }
                index = (index + 1) % listCount;
                // TPを使い切る、または全種類で値が必要値以上になったら抜ける
                if (currentTp <= 0 || breakCount >= listCount)
                {
                    break;
                }
            }
            
            UpdateScrollList((listItem) =>
            {
                listItem.UpdateValue(listItem.GetUseTp() + splitTps[listItem.ItemIndex]);
            });

            // ボタン系のUI更新
            OnClickButton();
        }
    }
}
