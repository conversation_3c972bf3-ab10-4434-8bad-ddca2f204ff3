using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 全国興行：興行詳細ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogJobsInfo : DialogInnerBase
    {
        /// <summary>残り時間用(この値なら非表示にする)</summary>
        private const long INVALID_END_TIME = -1; 
        /// <summary>残り時間表示時にscrollViewをずらす値</summary>
        private const float OFFSET_TOP = -200f; 
            
        /// <summary>興行名（"【東京レース場】ファンミーティング"のようなテキスト）</summary>
        [SerializeField] private PartsJobsJobName _jobName;
        
        /// <summary>残り時間ルート</summary>
        [SerializeField] private GameObject _remainingTimeRoot;
        /// <summary>ゲージ</summary>
        [SerializeField] private GaugeCommon _gauge;
        /// <summary>"あと 12:34:56""のようなテキスト</summary>
        [SerializeField] private TextCommon _remainingTimeText;
        /// <summary>「中止」ボタン</summary>
        [SerializeField] private ButtonCommon _abortButton;
        /// <summary>「確認」ボタン</summary>
        [SerializeField] private ButtonCommon _checkCompleteButton;
        
        /// <summary>「主な報酬」欄</summary>
        [SerializeField] private PartsJobsMainRewards _mainRewards;
        
        /// <summary>リスト</summary>
        [SerializeField] private ScrollRectCommon _scrollRect = null;
        
        /// <summary>アイテムアイコン(主な報酬)</summary>
        [SerializeField] private RectTransform _itemIconsRoot;
        [SerializeField] private MenuItemViewItem _menuItem;

        /// <summary>アイテムアイコン(その他)</summary>
        [SerializeField] private RectTransform _otherItemIconsRoot;
        [SerializeField] private ItemIconContainer _otherItemContainer = null;
        
        /// <summary>アイテムアイコン一覧</summary>
        private List<ItemIcon> _itemIconList = new List<ItemIcon>();

        /// <summary>報酬データ</summary>
        private MasterJobsReward.JobsReward _masterJobsReward;
        
        /// <summary>残り時間を表示するかどうかのフラグ</summary>
        private long _endJobTime = INVALID_END_TIME;
        
        /// <summary>実行中の興行1つの情報（※興行を実行していない時はnullになります。その場合はこの項目は空表示になります。</summary>
        private WorkJobsData.GoingJobInfo _workGoingJobInfo = null;
        
        /// <summary>「中止」ボタンが押された時に呼ばれる外部コールバック</summary>
        private System.Action<WorkJobsData.GoingJobInfo> _onClickAbortButton = null;
        /// <summary>「確認」ボタンが押された時に呼ばれる外部コールバック</summary>
        private System.Action<WorkJobsData.GoingJobInfo> _onClickCheckCompleteButton = null;

        /// <summary>ダイアログのボタンの数が1つか</summary>
        private bool _isOneButton = true;

        /// <summary>ダイアログ関係</summary>
        public override DialogCommon.FormType GetFormType()
        {
            return _isOneButton ? DialogCommonBase.FormType.BIG_ONE_BUTTON : DialogCommonBase.FormType.BIG_TWO_BUTTON;
        }
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        /// <summary>
        /// ダイアログの設定
        /// </summary>
        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Job497050.Text();
            if (_isOneButton)
            {
                data.CenterButtonText = TextId.Common0007.Text();
            }
            else
            {
                data.LeftButtonText = TextId.Common0007.Text();
                data.RightButtonText = TextId.Jobs600015.Text();
            }

            data.AutoClose = false;
            
            return data;
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(MasterJobsReward.JobsReward masterJobsReward, 
            WorkJobsData.GoingJobInfo workGoingJobInfo = null, 
            System.Action<WorkJobsData.GoingJobInfo> onClickAbortButton = null,
            System.Action<WorkJobsData.GoingJobInfo> onClickCheckCompleteButton = null,
            System.Action<bool> onClickReduceCompleteButton = null)
        {
            var component = LoadAndInstantiatePrefab<DialogJobsInfo>(ResourcePath.DIALOG_JOBS_INFO_PATH);

            component._isOneButton = workGoingJobInfo == null || (workGoingJobInfo.EndTimeStamp - TimeUtil.GetServerTimeStamp()) <= 0;
            
            var data = component.CreateDialogData();

            if (!component._isOneButton)
            {
                data.RightButtonCallBack = (_) =>
                {
                    component.OnClickRightButton(onClickReduceCompleteButton);
                };
                data.LeftButtonCallBack = (dialog) =>
                {
                    dialog.Close();
                };
            }
            else
            {
                data.CenterButtonCallBack = (dialog) =>
                {
                    dialog.Close();
                };
            }
            
            DialogManager.PushDialog(data);
            component.Setup(masterJobsReward, workGoingJobInfo, onClickAbortButton, onClickCheckCompleteButton);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(MasterJobsReward.JobsReward masterJobsReward, 
            WorkJobsData.GoingJobInfo workGoingJobInfo = null, 
            System.Action<WorkJobsData.GoingJobInfo> onClickAbortButton = null,
            System.Action<WorkJobsData.GoingJobInfo> onClickCheckCompleteButton = null)
        {
            _masterJobsReward = masterJobsReward;
            _workGoingJobInfo = workGoingJobInfo;
            if (!_workGoingJobInfo.IsNull())
            {
                _endJobTime = workGoingJobInfo.EndTimeStamp;
                _onClickAbortButton = onClickAbortButton;
                _onClickCheckCompleteButton = onClickCheckCompleteButton;
            }
            
            // 興行名のセットアップ
            SetupJobName();

            // 残り時間表示時のセットアップ
            SetupRemainingTimeRoot();
            
            // 「主な報酬」欄のセットアップ
            SetupMainRewards();

            // 「獲得可能な報酬」欄のセットアップ
            SetupGettableRewards();
        }
        
        /// <summary>
        /// 興行名のセットアップ
        /// </summary>
        private void SetupJobName()
        {
            // "【東京レース場】ファンミーティング"のようなテキスト
            _jobName.Setup(_masterJobsReward);
        }
        
        /// <summary>
        /// 残り時間表示時のセットアップ
        /// </summary>
        private void SetupRemainingTimeRoot()
        {
            bool isVisibleRemainingTime = _endJobTime > INVALID_END_TIME;
                
            // 残り時間表示・非表示
            _remainingTimeRoot.SetActiveWithCheck(isVisibleRemainingTime);

            // 非表示ならこの先何もしない
            if (!isVisibleRemainingTime)
            {
                return;
            }
            
            // スクロールビューの位置をずらす
            var rect = _scrollRect.GetComponent<RectTransform>().offsetMax;
            rect.y = OFFSET_TOP;
            _scrollRect.GetComponent<RectTransform>().offsetMax = rect;
            
            // 中止ボタン
            _abortButton.SetOnClick(OnClickAbortButton);
            // 確認ボタン
            _checkCompleteButton.SetOnClick(OnClickCheckCompleteButton);
        }
        
        /// <summary>
        /// 「主な報酬」欄のセットアップ
        /// </summary>
        private void SetupMainRewards()
        {
            _mainRewards.Setup(_masterJobsReward);
        }
        
        /// <summary>
        /// 「獲得可能な報酬」欄のセットアップ
        /// </summary>
        private void SetupGettableRewards()
        {
            _itemIconList.Clear();

            // 表示するアイテムの情報一覧を取得
            var mainRewardItemList = _masterJobsReward.MainRewardItemList;
            if (mainRewardItemList.IsNullOrEmpty())
            {
                return;
            }
            
            // 主な報酬
            var mainItemList = mainRewardItemList
                .Where(x => x.ItemDispType == MasterJobsReward.JobsReward.MainRewardItem.JobsRewardItemDispType.Main);

            // アイテムのインスタンスを生成してセットアップ
            _menuItem.SetActiveWithCheck(true);
            foreach (var mainRewardItem in mainItemList)
            {
                var clone = Instantiate(_menuItem.gameObject, _itemIconsRoot);
                var menuItem = clone.GetComponent<MenuItemViewItem>();
                if (menuItem != null)
                {
                    menuItem.SetupJobs(mainRewardItem.ItemCategory, mainRewardItem.ItemId, mainRewardItem.ItemNum);
                }
            }
            _menuItem.SetActiveWithCheck(false);
            
            // その他の報酬
            var otherItemList = mainRewardItemList
                .Where(x => x.ItemDispType == MasterJobsReward.JobsReward.MainRewardItem.JobsRewardItemDispType.Other).ToList();
            
            // 入れ物作成
            var listCount = otherItemList.Count;
            var otherItemIconInfoList = new List<ItemIconListInfo>(listCount);
            foreach (var otherItem in otherItemList)
            {
                var itemIconInfo = new ItemIconListInfo(otherItem.ItemCategory, otherItem.ItemId, 0);
                itemIconInfo.IsDispNum = false;
                itemIconInfo.IsInfoPop = true;
                otherItemIconInfoList.Add(itemIconInfo);
            }

            var containerCount = _otherItemContainer.GetInLineCount();
            var listNum = listCount / containerCount;
            if (listCount % containerCount > 0)
            {
                // 余りの行
                listNum++;
            }
            
            // １行分毎に分解
            var explodeList = otherItemIconInfoList.Select((info, i) => new {info, i})
                .GroupBy(x => x.i / containerCount)
                .Select(group => group.Select(x => x.info)).ToList();
            
            for (int i = 0; i < listNum; i++)
            {
                var clone = Instantiate(_otherItemContainer.gameObject, _otherItemIconsRoot);
                var itemIconContainer = clone.GetComponent<ItemIconContainer>();
                if (itemIconContainer != null)
                {
                    // LoopScrollだと全部入った配列を送っているためにItemIndexがループごとに変わるが、今回は行毎に送っているのでItemIndexは0スタート
                    itemIconContainer.ItemIndex = 0;
                    itemIconContainer.UpdateItem(explodeList[i].ToList());
                }
            }
            
            _otherItemContainer.SetActiveWithCheck(false);

            // リストのスクロール位置を一番上にリセット
            _scrollRect.verticalNormalizedPosition = 1;
        }
        
        /// <summary>
        /// 【毎フレーム処理】
        /// </summary>
        private void Update()
        {
            UpdateRemainingTime();
        }
        
        /// <summary>
        /// 【毎フレーム処理】「ゲージ」と「残り時間」と「中止ボタン」と文字画像の更新
        /// </summary>
        private void UpdateRemainingTime()
        {
            // 非表示ならこの先何もしない
            if (_endJobTime == INVALID_END_TIME)
            {
                return;
            }
            
            var remainingTimeSeconds = _endJobTime - TimeUtil.GetServerTimeStamp();

            // 帰還日時を過ぎている場合
            if (remainingTimeSeconds < 0)
            {
                if (_abortButton.IsActive())
                {
                    _remainingTimeText.text = TextUtil.Format(TextId.Jobs408023.Text(), 0, 0, 0);
                    // 中止ボタン非表示
                    _abortButton.SetActiveWithCheck(false);
                    // 確認ボタン表示
                    _checkCompleteButton.SetActiveWithCheck(true);
                    // ゲージの色を変える
                    var gaugeBarSpriteMax = JobsUtil.GetJobGaugeBarMaxSprite();
                    _gauge.SetGaugeImage(gaugeBarSpriteMax);
                }
            }
            // 帰還前の場合
            else
            {
                _remainingTimeText.text = TextUtil.Format(
                    TextId.Jobs408023.Text(),
                    remainingTimeSeconds / TimeUtil.HOUR_SECOND,
                    (remainingTimeSeconds % TimeUtil.HOUR_SECOND) / TimeUtil.MINUTE_SECOND,
                    remainingTimeSeconds % TimeUtil.MINUTE_SECOND
                    );

                // 中止ボタン表示
                _abortButton.SetActiveWithCheck(true);
                // 確認ボタン非表示
                _checkCompleteButton.SetActiveWithCheck(false);
            }

            UpdateGauge(remainingTimeSeconds);
        }

        /// <summary>
        /// 【毎フレーム処理】ゲージの更新
        /// </summary>
        private void UpdateGauge(long remainingTime)
        {
            var masterJobsReward = _masterJobsReward;
            if (masterJobsReward == null)
                return;

            long minutesToJobComplete = masterJobsReward.MinutesToJobComplete;
            long secondsToJobComplete = minutesToJobComplete * TimeUtil.MINUTE_SECOND;

            int now = (int)(secondsToJobComplete - remainingTime);
            int max = (int)(secondsToJobComplete);
            _gauge.Set(now, max);
        }
        
        /// <summary>
        /// 「中止」ボタン押下時処理
        /// </summary>
        private void OnClickAbortButton()
        {
            _onClickAbortButton?.Invoke(_workGoingJobInfo);
        }
        
        /// <summary>
        /// 「確認」ボタン押下時処理
        /// </summary>
        private void OnClickCheckCompleteButton()
        {
            GetDialog().Close();
            
            _onClickCheckCompleteButton?.Invoke(_workGoingJobInfo);
        }
        
        /// <summary>
        /// 「時間短縮」ボタン押下時処理
        /// </summary>
        private void OnClickRightButton(System.Action<bool> onClickReduceCompleteButton)
        {
            DialogReduceTimeJobOne.Open(_workGoingJobInfo, (isGoToResult) =>
            {
                onClickReduceCompleteButton(isGoToResult);
            });
        }
    }
}
