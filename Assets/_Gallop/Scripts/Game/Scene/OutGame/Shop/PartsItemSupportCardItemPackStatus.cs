using UnityEngine;
using static Gallop.StaticVariableDefine.OutGame.PartsItemExchangeListItem;

namespace Gallop
{
    /// <summary>
    /// サポカ所持状況ダイアログのリストアイテム
    /// </summary>
    [AddComponentMenu("")]
    public class PartsItemSupportCardItemPackStatus : LoopScrollItemBase
    {
        #region SerializeField

        /// <summary>
        /// サポートカードボタン
        /// </summary>
        [SerializeField]
        private CharacterButton _supportCardButton;

        /// <summary>
        /// 獲得済みアイコン
        /// </summary>
        [SerializeField]
        private GameObject _havingIcon;

        /// <summary>
        /// タイトル名
        /// </summary>
        [SerializeField]
        private TextCommon _titleText;

        /// <summary>
        /// アイテム名
        /// </summary>
        [SerializeField]
        private TextCommon _nameText;

        /// <summary>
        /// 上限解放アイコン
        /// </summary>
        [SerializeField]
        private PartsSupportCardLimitBreak _limitBreak;

        /// <summary>
        /// 未獲得テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _notGottenText;

        /// <summary>
        /// サポカストック所持数
        /// </summary>
        [SerializeField]
        private TextCommon _stockNumText;

        /// <summary>
        /// 購入時の入手予定数
        /// </summary>
        [SerializeField]
        private TextCommon _addNumText;

        /// <summary>
        /// 上限を超える注意テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _warningText;

        #endregion

        #region const

        // 警告表示あリの場合の全体の高さ
        private const float CONTENT_HEIGHT_WITH_WARNING = 282f;

        #endregion

        #region Method

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="supportCardId">サポートカードID</param>
        /// <param name="addCount">購入時の入手予定枚数</param>
        public void Setup(int supportCardId, int addCount)
        {
            SetupSupportCardIcon(supportCardId);
            var workSupport = WorkDataManager.Instance.SupportCardData.GetSupportCardData(supportCardId);
            SetupLimitBreakCount(workSupport);
            SetupNumber(workSupport, addCount);
        }

        /// <summary>
        /// サポカアイコンのセットアップ
        /// </summary>
        /// <param name="supportCardId">サポートカードID</param>
        private void SetupSupportCardIcon(int supportCardId)
        {
            var masterSupport = MasterDataManager.Instance.masterSupportCardData.Get(supportCardId);

            var info = new CharacterButtonInfo
            {
                IdType = CharacterButtonInfo.IdTypeEnum.Support,
                Id = supportCardId,
                Rarity = masterSupport.Rarity,
                EnableRarity = true,
                EnableObtain = true,
                OnLongTap = button =>
                {
                    var supportCardData = new WorkSupportCardData.SupportCardData(button.Info.Id);
                    DialogSupportCardDetail.Open(supportCardData, enableMaxButton: true);
                }
            };
            _supportCardButton.Setup(info);
            _supportCardButton.SetSizeType(IconBase.SizeType.SupportCard_63);

            // サポカ名表示
            _titleText.text = masterSupport.Titlename;
            _nameText.text = masterSupport.Charaname;

            // 獲得済みアイコンを表示（表示する際はサポカ表示位置をその分ずらす
            var isHaving = WorkDataManager.Instance.SupportCardData.HasSupportCard(supportCardId);
            _havingIcon.SetActiveWithCheck(isHaving);
            _supportCardButton.transform.localPosition = isHaving ? SUPPORT_CARD_HAVING_ITEM_POSITION : SUPPORT_CARD_DEFAULT_ITEM_POSITION;
        }

        /// <summary>
        /// 上限解放数の表示
        /// </summary>
        /// <param name="workSupport">サポートカードワーク情報</param>
        private void SetupLimitBreakCount(WorkSupportCardData.SupportCardData workSupport)
        {
            var isHaving = workSupport != null;
            _notGottenText.SetActiveWithCheck(!isHaving);
            _limitBreak.SetActiveWithCheck(isHaving);

            if (isHaving)
            {
                int limitBreakCount = workSupport.LimitBreakCount;
                _limitBreak.Setup(limitBreakCount);
            }
        }

        /// <summary>
        /// サポカストック数と入手予定数のセットアップ
        /// </summary>
        /// <param name="workSupport">サポートカードワーク情報</param>
        /// <param name="addCount">購入時の入手予定枚数</param>
        private void SetupNumber(WorkSupportCardData.SupportCardData workSupport, int addCount)
        {
            var stockCount = workSupport?.Stock ?? 0;

            _stockNumText.text = TextUtil.ToCommaSeparatedString(stockCount);
            _addNumText.text = addCount.ToString();

            // 入手予定枚数と現在の所持数の総数を合わせて四凸に必要な計5枚の上限を超える場合は警告表示
            var totalCount = stockCount + (workSupport?.LimitBreakCount + 1 ?? 0) + addCount;
            var isOverLimit = totalCount > GameDefine.MAX_SUPPORTCARD_LIMITBREAK + 1;
            SetupWarning(isOverLimit);
        }

        /// <summary>
        /// 警告表示のセットアップ
        /// </summary>
        /// <param name="isOverLimit">サポカ完凸に必要な枚数を超えるかどうか</param>
        private void SetupWarning(bool isOverLimit)
        {
            _warningText.SetActiveWithCheck(isOverLimit);
            if (isOverLimit)
            {
                // 警告表示時は入手予定枚数も警告色に変更
                _addNumText.FontColor = FontColorType.Warning;
                // 警告表示分高さを大きくする
                if (TryGetComponent<RectTransform>(out var rectTransform))
                {
                    rectTransform.SetSizeDeltaY(CONTENT_HEIGHT_WITH_WARNING);
                }
            }
        }

        #endregion
    }
}
