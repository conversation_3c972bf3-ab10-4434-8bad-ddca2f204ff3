using Cute.UI;
using UnityEngine;
using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;

namespace Gallop
{
    /// <summary>
    /// ウマ娘名鑑報酬確認ダイアログ。WorkDataをベースに開く
    /// </summary>
    [AddComponentMenu("")]
    public class DialogConfirmDirectoryReward : DialogInnerBase
    {
        #region Const

        /// <summary>
        /// フェード時間
        /// </summary>
        private const float CONTENT_FADE_DURATION = 0.2f;

        private const float ANIMATION_WAIT_TIME1 = 0.266f;
        private const float ANIMATION_WAIT_TIME2 = 0.166f;
        private const float ANIMATION_WAIT_TIME3 = 0.166f;

        /// <summary>
        /// レベルアップ演出関連オブジェクト
        /// </summary>
        private const string LEVEL_UP_ALL_OBJECT = "MOT_mc_txt_levelup_all";
        private const string LEVEL_UP_BEFORE_NUM_OBJECT = "MOT_mc_num_before_all";
        private const string LEVEL_UP_AFTER_NUM_OBJECT = "MOT_mc_num_after_all";
        private const string MOT_ROOT_OBJECT = "MOT_root";
        private const string EFFECT_OBJECT = "pfb_uieff_characternote_levelup01(Clone)";
        private const string TXT_NUM_AFTER = "TXT_txt_num_after00";
        private const string TXT_NUM_BEFORE = "TXT_txt_num_before00";
        
        private const string LEVEL_UP_IN = "in";
        private const string LEVEL_UP_IN_TEXT = "in_text";
        private const string LEVEL_UP_BEFORE_TEXT_IN = "num_before_in";
        private const string LEVEL_UP_AFTER_TEXT_IN = "num_after_in";
        private const string LEVEL_UP_IN_END_TEXT = "in_text_end";
        //１桁、２桁、３桁でラベルを変更する
        private const string LEVEL_UP_IN_1 = "num01";
        private const string LEVEL_UP_IN_2 = "num02";
        private const string LEVEL_UP_IN_3 = "num03";

        private const string LEVEL_UP_EXPAND = "expand";
        private const string LEVEL_UP_EXPAND_ANIME = "Expand";
        private const string LEVEL_UP_CLOSE = "Close";
        private const string LEVEL_UP_OUT = "out";
        private const int LEVEL_UP_UNDER = 10;
        private const int LEVEL_UP_UPPER = 99;
        private const float LEVEL_UP_MOVEY = 175f;

        #endregion

        #region SerializeField

        /// <summary>
        /// アイテムアイコンのCanvasGroup
        /// </summary>
        [SerializeField]
        private CanvasGroup _canvasGroup = null;

        /// <summary>
        /// アイテムアイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _itemIcon = null;

        /// <summary>
        /// アイテムアイコン
        /// </summary>
        [SerializeField]
        private GameObject _itemIconRoot;

        /// <summary>
        ///  報酬表示用キャンバス
        /// </summary>
        [SerializeField]
        private Canvas _rewardCanvas = null;

        /// <summary>
        /// 閉じるボタン
        /// </summary>
        [SerializeField]
        private ButtonCommon _buttonClose = null;
        [SerializeField]
        private CanvasGroup _buttonCloseCanvasGroup = null;

        #endregion

        #region Variables
        private DialogCommon _dialogCommon;
        private Animator _frameAnimatorInstance = null;
        private FlashPlayer _levelupPlayer = null;
        private Transform _effectObj = null;
        private Coroutine _inAnimeCoroutine = null;
        #endregion

        #region Override

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.WITHOUT_FRAME;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.GetAtlasPath(TargetAtlasType.Directory));
            register.RegisterPathWithoutInfo(ResourcePath.CONFETTI_PARTICLE_PATH);
            //レベルアップ演出とフレームをダウンロード
            register.RegisterPathWithoutInfo(ResourcePath.CHARACTER_NOTE_LEVEL_UP);
            register.RegisterPathWithoutInfo(ResourcePath.CHARACTER_NOTE_LEVEL_UP_UNITYANIMATION_FRAME_04);
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static DialogConfirmDirectoryReward Open(System.Action destroyCallback = null)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CONFIRM_DIRECTORY_REWARD);
            var dialogObj = Instantiate(prefab);
            var dialogContent = dialogObj.GetComponent<DialogConfirmDirectoryReward>();
            var dialogData = dialogContent.CreateDialogData();
            dialogData.CancelSe = AudioId.INVALID;
            dialogData.EnableOutsideClick = false;

            var dialog = DialogManager.PushDialog(dialogData);
            dialogContent._dialogCommon = dialog;
            dialogContent.Setup();
            dialogContent.SetCloseButton();
            dialog.SetDestroyCallBack(destroyCallback);

            // SE再生。
            AudioManager.Instance.PlaySe(AudioId.SFX_DIRECTORY_LV_UP);

            return dialogContent;
        }

        /// <summary>
        /// 表示設定
        /// </summary>
        private void Setup()
        {
            // 報酬
            CreateItemData();
            //アニメーション開始
            OpenAnimation();
        }
        
        /// <summary>
        /// 閉じるボタン挙動実装
        /// </summary>
        /// <param name="dialog"></param>
        private void SetCloseButton()
        {
            _buttonClose.SetActiveWithCheck(false);
            _buttonClose.SetOnClick(() => PlayClose());
        }
        /// <summary>
        /// 開くアニメーション
        /// </summary>
        private void OpenAnimation()
        {
            _inAnimeCoroutine = _dialogCommon.StartCoroutine(RunOpenAnimation());
        }

        private IEnumerator RunOpenAnimation()
        {
            yield return new WaitForSeconds(ANIMATION_WAIT_TIME3); // アニメーション開始前ディレイ

            //フレーム初期化
            var frame = ResourceManager.LoadOnView<GameObject>(ResourcePath.CHARACTER_NOTE_LEVEL_UP_UNITYANIMATION_FRAME_04);
            _frameAnimatorInstance = GameObject.Instantiate(frame, transform).GetComponent<Animator>();
            _frameAnimatorInstance.GetComponent<AnimationEventSender>().Initialize(this);
            _frameAnimatorInstance.transform.SetSiblingIndex(0);

            //レベルアップ初期化
            var flashActionPlayer = FlashActionPlayer.Load(ResourcePath.CHARACTER_NOTE_LEVEL_UP, transform);
            _levelupPlayer = flashActionPlayer.LoadFlashPlayer();
            flashActionPlayer.SetSortLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            var uguiToFlash = _levelupPlayer.gameObject.AddComponent<FlashToUgui>();
            uguiToFlash.InitializeAndCreateClone(_levelupPlayer.gameObject);
            
            //全体のレイヤーをダイアログに合わせる
            gameObject.SetLayerRecursively(UIManager.Instance.NoImageEffectGameCanvasRoot.layer);

            var workDirectory = WorkDataManager.Instance.DirectoryData;

            _levelupPlayer.SetText(workDirectory.BeforeRank.ToString(), TXT_NUM_BEFORE);
            _levelupPlayer.SetText(workDirectory.Rank.ToString(), TXT_NUM_AFTER);
            
            yield return new WaitForSeconds(ANIMATION_WAIT_TIME1);

            _levelupPlayer.Play(LEVEL_UP_IN);
            
            bool isInTextEnd = false;
            _levelupPlayer.SetMotion(LEVEL_UP_ALL_OBJECT);
            
            //それぞれのラベルにコールバックを設定
            _levelupPlayer.SetActionCallBack(LEVEL_UP_BEFORE_TEXT_IN, () => {
                PlayTextNumLabel(workDirectory.BeforeRank , LEVEL_UP_BEFORE_NUM_OBJECT);
            }, AnimateToUnity.AnMotionActionTypes.Start);
            _levelupPlayer.SetActionCallBack(LEVEL_UP_AFTER_TEXT_IN, () => {
                PlayTextNumLabel(workDirectory.Rank , LEVEL_UP_AFTER_NUM_OBJECT);
            }, AnimateToUnity.AnMotionActionTypes.Start);
            _levelupPlayer.SetActionCallBack(LEVEL_UP_IN_END_TEXT, () => {
                isInTextEnd = true;
            }, AnimateToUnity.AnMotionActionTypes.Start);

            _levelupPlayer.SetActionCallBack(LEVEL_UP_IN_END_TEXT, () => {
                isInTextEnd = true;
            }, AnimateToUnity.AnMotionActionTypes.Start);
            //テキストイリが終了まで待つ
            _levelupPlayer.Play(LEVEL_UP_IN_TEXT);
            
            yield return new WaitUntil(() => isInTextEnd);

            //コールバックが誤作動しないように消しておく
            _levelupPlayer.RemoveAction(LEVEL_UP_IN_END_TEXT, AnMotionActionTypes.Start);

            _frameAnimatorInstance.SetTrigger(LEVEL_UP_EXPAND_ANIME);
            _levelupPlayer.SetMotion(MOT_ROOT_OBJECT);
            _levelupPlayer.Play(LEVEL_UP_EXPAND);
            yield return new WaitForSeconds(ANIMATION_WAIT_TIME2);

            var effectMotion = _levelupPlayer.GetMotion(MOT_ROOT_OBJECT);
            _effectObj = null;
            if (effectMotion != null)
            {
                _effectObj = effectMotion.Transform.Find(EFFECT_OBJECT);
                // Y軸+175移動
                if (_effectObj != null)
                {
                    _effectObj.DOLocalMoveY(LEVEL_UP_MOVEY, ANIMATION_WAIT_TIME1).SetEase(Ease.InOutCubic);
                }
            }

            yield return new WaitForSeconds(ANIMATION_WAIT_TIME3);
            _itemIconRoot.SetActiveWithCheck(true);
            // アイテムアイコンの生成
            var sequence = DOTween.Sequence();
            //非表示から始める
            _itemIcon.gameObject.SetActive(false);
            _itemIcon.SetSizeForPlayAnim(IconBase.SizeType.Common_M);
            sequence.AppendCallback(() =>
            {
                _itemIcon.gameObject.SetActive(true);
                _itemIcon.Play(_rewardCanvas.sortingOrder, 0);
            });
            sequence.AppendInterval(_itemIcon.GetAnimationIntervalTime());
            
            //閉じるボタン表示
            ShowCloseButton();
        }

        /// <summary>
        /// テキストラベルを修正
        /// </summary>
        private void PlayTextNumLabel(int rank , string motionName)
        {
            _levelupPlayer.SetMotion(motionName);
            if (rank < LEVEL_UP_UNDER)
            {
                _levelupPlayer.Play(LEVEL_UP_IN_1);
            }
            else if (rank > LEVEL_UP_UPPER)
            {
                _levelupPlayer.Play(LEVEL_UP_IN_3);
            }
            else
            {
                _levelupPlayer.Play(LEVEL_UP_IN_2);
            }
            _levelupPlayer.SetMotion(LEVEL_UP_ALL_OBJECT);
        }

        /// <summary>
        /// アイテムリストをグリッド表示
        /// </summary>
        public void CreateItemData()
        {
            _itemIconRoot.SetActiveWithCheck(false);
            // 名鑑の報酬生成
            var rewardInfo = WorkDataManager.Instance.DirectoryData.GetRewardInfo;
            _itemIcon.SetData(rewardInfo.RewardItem.Category, rewardInfo.RewardItem.ItemId, rewardInfo.RewardItem.ItemNum, isInfoPop: true);
            // #136173 アイテムの星エフェクトのレイヤーとSortingLayerをItemIconに合わせる
            _itemIcon.IsMatchParticleLayerWithParent = true;
            _itemIcon.ParticleSortingLayer = _rewardCanvas.sortingLayerName;
        }

        private void PlayClose()
        {
            if (_inAnimeCoroutine != null)
            {
                _dialogCommon.StopCoroutine(_inAnimeCoroutine);
            }
            _itemIconRoot.SetActiveWithCheck(false);
            if (_effectObj != null)
            {
                _effectObj.gameObject.SetActive(false);
            }
            _levelupPlayer.Play(LEVEL_UP_OUT);
            _frameAnimatorInstance.SetTrigger(LEVEL_UP_CLOSE);
            _canvasGroup
                .DOFade(0, CONTENT_FADE_DURATION)
                .OnComplete(() =>
                {
                    _dialogCommon.Close();
                });
            
            _buttonClose.SetEnable(false);
            _buttonCloseCanvasGroup.DOFade(0f,CONTENT_FADE_DURATION);

        }
        
        /// 閉じるボタン表示
        /// </summary>
        private void ShowCloseButton()
        {
            const float DURATION = 0.2f;
            _buttonClose.SetActiveWithCheck(true);
            var rectTrans = _buttonClose.transform as RectTransform;
            _buttonCloseCanvasGroup.DOFade(0f, DURATION).From();
            rectTrans.DOAnchorPosY(-20f, DURATION).From(true);
        }
        #endregion
    }
}
