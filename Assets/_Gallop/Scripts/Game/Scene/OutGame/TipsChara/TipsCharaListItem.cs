using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ウワサリストアイテム
    /// </summary>
    [AddComponentMenu("")]
    public class TipsCharaListItem : LoopScrollItemBase
    {
        [SerializeField]
        private CharacterButton _charaButton = null;
        [SerializeField]
        private TextCommon _textTitle = null;
        [SerializeField]
        private TextCommon _textUwasa = null;

        /// <summary>
        /// 初期化
        /// </summary>
        public void Init(TopicsInfo info)
        {
            var buttonInfo = new CharacterButtonInfo()
            {
                IdType = CharacterButtonInfo.IdTypeEnum.Chara,
                IconSizeType = IconBase.SizeType.Chara_63,
                Id = info.TopicsData.Value,
                DressId = CharacterButtonInfo.INVALID_VALUE,
                EnableButton = false
            };
            _charaButton.Setup(buttonInfo);
            _textTitle.text = info.TopicsData.title;
            _textUwasa.text = info.TopicsData.comment;
        }
    }
}
