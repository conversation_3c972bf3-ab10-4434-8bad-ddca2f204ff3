using System.Collections.Generic;
using UnityEngine;


namespace Gallop
{
    /// <summary>
    /// ギャラリー：ギャラリーキー使用確認ダイアログのアイテム
    /// </summary>
    public class PartsUseGalleryKeyConfirmListItem : MonoBehaviour
    {
        [SerializeField]
        private TextCommon _title = null;

        [SerializeField]
        private GameObject _charaListUIRoot = null;

        [SerializeField]
        private ButtonCommon _infoButton = null;
        
        [SerializeField]
        private TextCommon _charaNumText = null;
        
        /// <summary>
        /// 要素の更新
        /// </summary>
        /// <param name="title"></param>
        /// <param name="charaIdList">null以外ならリストを大きくしてウマ娘一覧ボタンを表示</param>
        public void UpdateItem(string title, List<int> charaIdList = null)
        {
            _title.text = title;

            var hasCharacterList = !charaIdList.IsNullOrEmpty();
            SwitchCharacterListUI(hasCharacterList);
            if (hasCharacterList)
            {
                _infoButton.SetOnClick(() => OnClickInfoButton(charaIdList));
                _charaNumText.text = TextId.Common0270.Format(charaIdList.Count.ToString());
            }
        }

        /// <summary>
        /// UI切り替え
        /// </summary>
        /// <param name="hasCharacterList"></param>
        private void SwitchCharacterListUI(bool hasCharacterList)
        {
            _charaListUIRoot.SetActiveWithCheck(hasCharacterList);

            const float NORMAL_TITLE_POS_Y = -64f;
            const float CHARA_LIST_TITLE_POS_Y = -54f;
            _title.rectTransform.anchoredPosition = new Vector2(_title.rectTransform.anchoredPosition.x,
                hasCharacterList ? CHARA_LIST_TITLE_POS_Y : NORMAL_TITLE_POS_Y);

            const float NORMAL_HEIGHT = 129f;
            const float CHARA_LIST_HEIGHT = 172f;
            if (transform is RectTransform rectTransform)
            {
                rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, hasCharacterList ? CHARA_LIST_HEIGHT : NORMAL_HEIGHT);
            }
        }

        /// <summary>
        /// iボタン押下
        /// </summary>
        /// <param name="charaIdList"></param>
        private void OnClickInfoButton(List<int> charaIdList)
        {
            GalleryDialogSelectCharacter.OpenForView(charaIdList);
        }
    }
}
