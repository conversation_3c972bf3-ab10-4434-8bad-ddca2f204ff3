using UnityEngine;
using System.Collections.Generic;
using static Gallop.StaticVariableDefine.OutGame.PhotoListInfo;

namespace Gallop
{
    /// <summary>
    /// フォトスクロールビューの設定(CharacterListInfoとほぼ同様)
    /// ** 表示幅が他と違ったり表示物が違う場合などに使うこと
    /// </summary>
    public class PhotoListInfo
    {
        //デフォルトの一行あたりのアイコン数
        public const int DEFAULT_IN_LINE_PHOTO_NUM = 4;
        public int InLinePhotoNum; //一行あたりのアイコン数
        public Vector2? GridSize;    //グリッドサイズ
        public Vector2? GridSpace;   //アイコン同士のスペーシング
        // リソース管理をViewでなくCharacterListUIで行うフラグ
        public bool _enableResourceManageByList;

        public PhotoListInfo(
            int inLineCharaNum = DEFAULT_IN_LINE_PHOTO_NUM,
            Vector2? gridSize = null, Vector2? gridSpace = null,
            bool enableResourceManageByList = false
            )
        {
            InLinePhotoNum = inLineCharaNum;
            GridSize = gridSize == null ? DEFAULT_GRID_SIZE : gridSize;
            GridSpace = gridSpace == null ? DEFAULT_GRID_SPACE : gridSpace;
            _enableResourceManageByList = enableResourceManageByList;
        }
    }
}
