using UnityEngine;

namespace Gallop
{
    public class PartsPhotoStudioPlayCutSettingsScenarioCategorySelectItem : LoopScrollItemBase
    {
        [SerializeField]
        public ButtonCommon _button;

        [SerializeField]
        public RawImageCommon _rawImage;

        [SerializeField]
        public TextCommon _textCommon;
        
        [SerializeField]
        public CursorCommon _cursorCommon;

        public void Setup(IPartsPhotoStudioPlayCutSettingsScenarioCategorySelectItemVM vm,
            IPartsPhotoStudioPlayCutSettingsScenarioCategorySelectItemEventDispatch eventDispatch)
        {
            _textCommon.text = vm.TitleText;
            _rawImage.texture = eventDispatch.LoadTexture(vm.TexturePath);
            _button.SetOnClick(() => eventDispatch.OnSelect(vm.Category));
            _cursorCommon.SetActiveWithCheck(vm.ShouldShowCursor);
        }
    }

    public interface IPartsPhotoStudioPlayCutSettingsScenarioCategorySelectItemEventDispatch
    {
        /// <summary> 選択時処理 </summary>
        /// <param name="scenarioCutCategory"></param>
        void OnSelect(ScenarioCutCategory scenarioCutCategory);

        /// <summary> 画像読み込み </summary>
        /// <param name="texturePath"></param>
        Texture2D LoadTexture(string texturePath);
    }
}