using System.Linq;

namespace Gallop
{
    public class CharaDressSettingInfoWithChangeScenarioCutChara : IPhotoStudioPlayCutSettingsChangeAllDressSettingsInfo
    {
        int IPhotoStudioPlayCutSettingsChangeAllDressSettingsInfo.DressId => _dressId;

        bool IPhotoStudioPlayCutSettingsChangeAllDressSettingsInfo.IsAllChangeDress => false;

        PhotoStudioPlayCutSettingsChangeAllDressSettingsType IPhotoStudioPlayCutSettingsChangeAllDressSettingsInfo.ChangeAllDressSettingsType => PhotoStudioPlayCutSettingsChangeAllDressSettingsType.None;

        private readonly int _dressId;

        public CharaDressSettingInfoWithChangeScenarioCutChara(int charaId)
        {
            // 汎用衣装を除く、勝負服を取得
            var masterDressDataList = WorkDataManager.Instance.DressData
                .GetDressListByCharaId(charaId, GameDefine.CharacterClothMode.Race)
                .Where(x => x.GetCondition == MasterDressData.GetCondition.CardGet)
                .Select(x => MasterDataManager.Instance.masterDressData.Get(x.Id))
                .ToList();

            // 勝負服が存在しない場合は汎用衣装(SF衣装)のみ追加
            if (masterDressDataList.Count == 0)
            {
                masterDressDataList.Add(MasterDataManager.Instance.masterDressData.Get((int)ModelLoader.DressID.SRCommon));
            }

            _dressId = masterDressDataList.FirstOrDefault().Id;
        }
    }
}