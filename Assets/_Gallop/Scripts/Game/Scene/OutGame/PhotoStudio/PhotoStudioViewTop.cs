using System;
using System.Linq;
using System.Collections;
using UnityEngine;
using System.Collections.Generic;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// フォトスタジオTOP
    /// </summary>
    public class PhotoStudioViewTop : MonoBehaviour
    {

        /// <summary>
        /// たづなさんの吹き出しとボイス
        /// </summary>
        [SerializeField]
        public PartsPhotoTopCharaMessage _charaMessage = null;

        // フォトスタジオボタン 
        [SerializeField]
        public ButtonCommon _photoStudioButton = null;

        // フォトライブラリボタン 
        [SerializeField]
        public ButtonCommon _photoLibraryButton = null;


        private System.Action _onClickPhotoStudioButton;
        private System.Action _onClickPhotoLibraryButton;


        #region InitializeView

        /// <summary>
        /// 初期化
        /// </summary>
        /// <returns></returns>
        public void InitializeView(System.Action onClickPhotoStudioButton, System.Action onClickPhotoLibraryButton)
        {
            _onClickPhotoStudioButton = onClickPhotoStudioButton;
            _onClickPhotoLibraryButton = onClickPhotoLibraryButton;

            InitializeButton();
        }

        private void InitializeButton()
        {
            _photoStudioButton.SetOnClick(OnClickPhotoStudioButton);
            _photoLibraryButton.SetOnClick(OnClickPhotoLibraryButton);
        }

        #endregion InitializeView


        #region PlayInView

        public void PlayInView(string bgPath)
        {
            this.gameObject.SetActiveWithCheck(true);

            // ヘッダータイトル更新
            UIManager.Instance.SetHeaderTitleText(TextId.Outgame213067.Text());
            UIManager.Instance.PlayHeaderTitleInAnim();
            UIManager.Instance.SetVisibleHeaderTitle(true);

            PlayInCharacterBg(bgPath);
        }

        private void PlayInCharacterBg(string bgPath)
        {
            BGManager.Instance.SetBg(SceneDefine.BgId.Character);
            BGManager.Instance.CharacterBg.Setup(ResourcePath.PHOTO_TOP_ENV_PARAM, bgPath, GameDefine.TAZUNA_CHARA_ID, GameDefine.TAZUNA_DRESS_ID, true, false);
            _charaMessage.SetModel(BGManager.Instance.CharacterBg.Model, false);

            BGManager.Instance.CharacterBg.CameraController.SetCameraPosWithFixCharacterScale(SceneDefine.ViewId.PhotoStudioViewTop, BGManager.Instance.CharacterBg.Model);
            BGManager.Instance.CharacterBg.SetCharaVisible(true);

            // たづなさんの吹き出しとボイス再生（汎用）
            _charaMessage.Setup(false);
            _charaMessage.SetEnable(true);
            _charaMessage.PlaySet();
        }

        #endregion PlayInView


        public void PlayOutView()
        {
            this.gameObject.SetActiveWithCheck(false);

            _charaMessage.Stop();
            _charaMessage.SetEnable(false);
        }


        /// <summary>
        /// 画面終了時処理
        /// </summary>
        public void EndView()
        {
            _charaMessage.Stop();
            _charaMessage.SetEnable(false);
        }

        private void OnClickPhotoStudioButton()
        {
            _onClickPhotoStudioButton?.Invoke();
        }

        private void OnClickPhotoLibraryButton()
        {
            _onClickPhotoLibraryButton?.Invoke();
        }

    }
}
