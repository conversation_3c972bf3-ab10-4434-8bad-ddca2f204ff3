using UnityEngine;
using UnityEngine.Rendering;

namespace Gallop
{
    /// <summary>
    /// フォトスタジオトップのキャラとカメラを管理するクラス
    /// 
    /// </summary>
    public sealed class PhotoStudioTopCharaViewer : MonoBehaviour
    {
        #region 定数

        //育成画面の見せ方だけ特殊(おそらくCharacterBgよりも前に作られてる)な上、統一ViewでUIがコロコロ変わるので直接対応
        private readonly Vector3 CAMERA_POS_DEFAULT = new Vector3(0, 0.93f, -4.13f);
        private readonly Vector3 CAMERA_POS_OFFSET_CARD_SELECT = new Vector3(0f, 0.112f,0);

        private readonly Quaternion CAMERA_ROTATION_DEFAULT = Math.QUATERNION_IDENTITY;

        //キャラの最大数
        //継承選択で2人表示するので、最大3人分必要になる
        private const int MODEL_MAX = 1;

        public enum ModelIndex
        {
            Center,
            Left,
            Right,
        }

        //レイヤー定数
        public static int NoVisibleLayer { get { return GraphicSettings.GetLayer(GraphicSettings.LayerIndex.LayerNO_VISIBLE); } }
        public static int CharaLayer { get { return GraphicSettings.GetLayer(GraphicSettings.LayerIndex.LayerCHAR); } }

        #endregion

        #region クラス

        /// <summary>
        /// 背景用ブラー
        /// UICameraImageEffectとほぼ同じ
        /// </summary>
        public class BGBlur
        {
            /// <summary>
            /// シェーダーのパス
            /// </summary>
            private const string SHADER_PATH = "Gallop/UI/ImageEffect";

            //ブラー強さの倍率(0->1だと調整しずらいので)
            private const float STRENTG_SCALE = 1.0f / 100.0f;

            /// <summary>
            /// イメージエフェクトをかけるマテリアル
            /// </summary>
            private Material _imageEffectMaterial = null;

            public float BlurStrength { get; set; } = 0.0f;

            public void Initialize()
            {
                _imageEffectMaterial = new Material(Shader.Find(SHADER_PATH));
            }

            public void Release()
            {
                if(_imageEffectMaterial != null)
                {
                    GameObject.Destroy(_imageEffectMaterial);
                    _imageEffectMaterial = null;
                }
            }

            private void UpdateBlurParameter()
            {
                _imageEffectMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._BlurStrength), BlurStrength * STRENTG_SCALE);
            }

            public void OnRender(RenderTexture source)
            {
                if (_imageEffectMaterial == null)
                {
                    return;
                }

                if (BlurStrength <= 0.0f)
                    return;

                UpdateBlurParameter();

                int width = source.width / 2;
                int height = source.height / 2;

                var destination = RenderTexture.GetTemporary(width, height, 0);
                var destination2 = RenderTexture.GetTemporary(width, height, 0);
                //縮小する
                Graphics.Blit(source, destination);
                //ブラーを描ける
                Graphics.Blit(destination, destination2, _imageEffectMaterial, 0);
                Graphics.Blit(destination2, destination, _imageEffectMaterial, 1);
                //戻す
                Graphics.Blit(destination, source);

                RenderTexture.ReleaseTemporary(destination);
                RenderTexture.ReleaseTemporary(destination2);
            }
        }

        /// <summary>
        /// RenderTextureで描画したものをCommandBuffer経由で描画する
        /// </summary>
        public class RenderTextureTo3DCommandBuffer
        {
            private const string BACKGROUND_SHADER = "Gallop/2D/Background3D";

            private CommandBuffer _commandBuffer;
            private Material _backgroundMaterial;
            private Camera _camera;

            public void Initialize()
            {
                _commandBuffer = new CommandBuffer();
                _backgroundMaterial = new Material(Shader.Find(BACKGROUND_SHADER));
                _backgroundMaterial.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ScreenScale), Math.VECTOR4_ONE);
            }

            public void Release()
            {
                if(_commandBuffer != null)
                {
                    _commandBuffer.Release();
                    _commandBuffer = null;
                }
                if(_backgroundMaterial != null)
                {
                    GameObject.Destroy(_backgroundMaterial);
                    _backgroundMaterial = null;
                }
            }

            public void AttachCommand(Camera camera)
            {
                if (_commandBuffer == null)
                    return;
                _camera = camera;
                camera.AddCommandBuffer(CameraEvent.BeforeForwardOpaque, _commandBuffer);
            }

            public void RemoveCommand()
            {
                if(_camera != null && _commandBuffer != null)
                {
                    _camera.RemoveCommandBuffer(CameraEvent.BeforeForwardOpaque, _commandBuffer);
                }
            }

            public void MakeCommand(RenderTexture blitTexture)
            {
                _commandBuffer.Clear();
                _commandBuffer.Blit(blitTexture, BuiltinRenderTextureType.CurrentActive, _backgroundMaterial);
            }
        }

        #endregion

        //これ外部アセットにする
        private SingleModeStartCharaViewerSettingData _settingData;

        [SerializeField]
        private Transform _singleCharaLocator = null;

        [SerializeField]
        private Camera _camera = null;

        [SerializeField]
        private CameraController _cameraController = null;

        private SingleRaceModelController[] _modelArray = new SingleRaceModelController[MODEL_MAX];
        private SingleModeSceneController _sceneController;
        private GallopCharacterImageEffect _imageEffect;
        private LowResolutionCamera _lowResolutionCamera;

        #region カメラ移動

        private Vector3 _initialCameraPosition;
        private Vector3 _beginCameraPosition;
        private Vector3 _endCameraPosition;
        private float _moveCameraTime;
        private float _moveCameraDeltaTime;
        private ModelIndex _moveTargetIndex;    //今回注視するキャラ
        private Transform _cameraTransform;

        private Vector3 _beginCenterCharacterPosition;
        //左右どちらかのカメラに移動した時のオフセット
        private Vector3 _beginLeftCharacterOffset;
        private Vector3 _beginRightCharacterOffset;

        private RenderTexture _backgroundRenderTexture;

        private float _beginBlurStrength;
        private float _endBlurStrength;
        private BGBlur _bgBlur;
        private RenderTextureTo3DCommandBuffer _to3DCommandBuffer;

        #endregion

        public SingleModeStartCharaViewerSettingData SettingData => _settingData;

        public LowResolutionCamera LowResolutionCamera => _lowResolutionCamera;

        /// <summary>
        /// DLアセットパス登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterPath(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.PHOTO_STUDIO_TOP_ENV_PARAM);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        public void InitializeViewer()
        {
            //このオブジェクトのカメラを使用して映すためシーンのカメラは不要
            _sceneController = SceneManager.Instance.GetCurrentSceneController() as SingleModeSceneController;
            _sceneController?.SetCameraEnable(false);
            _cameraTransform = _camera.transform;
            _settingData = ResourceManager.LoadOnScene<SingleModeStartCharaViewerSettingData>(ResourcePath.SINGLE_MODE_START_CHARAVIEWR_SETTING_DATA_PATH);
            //初期カメラ位置を覚えておく(継承でカメラが移動するので)
            _initialCameraPosition = _cameraTransform.localPosition;

            SetupImageEffect();
        }

        /// <summary>
        /// 破棄
        /// </summary>
        public void FinalizeViewer()
        {
            for (int i = 0; i < _modelArray.Length; i++)
            {
                DestroyModel(i);
            }
            _sceneController?.SetCameraEnable(true);
            OnReleaseTexture(null);
        }

        /// <summary>
        /// カメラ姿勢の更新
        /// </summary>
        private void UpdateCameraPosture()
        {
            if (_moveCameraTime > 0.0f)
            {
                float normalizeTime;
                Vector3 cameraPosition;
                _moveCameraDeltaTime += Time.deltaTime;
                if (_moveCameraDeltaTime >= _moveCameraTime)
                {
                    cameraPosition = _endCameraPosition;
                    _moveCameraDeltaTime = 0.0f;
                    _moveCameraTime = 0.0f;
                    normalizeTime = 1.0f;
                    _bgBlur.BlurStrength = _endBlurStrength;
                }
                else
                {
                    normalizeTime = _moveCameraDeltaTime / _moveCameraTime;
                    normalizeTime = SettingData.EvaluateCameraMove(normalizeTime);
                    cameraPosition = Vector3.Lerp(_beginCameraPosition, _endCameraPosition, normalizeTime);
                    _bgBlur.BlurStrength = Mathf.Lerp(_beginBlurStrength,_endBlurStrength, normalizeTime);
                }
                _cameraTransform.localPosition = cameraPosition;
                //カメラが中心へ -> 左右の人は徐々に高さオフセットがかかる
                //カメラが左右へ -> 左右の人の高さはY = 0に向かう
                if (_moveTargetIndex == ModelIndex.Center)
                {
                    SetHeightScalePositionOffsetY(ModelIndex.Left, Math.VECTOR3_ZERO, normalizeTime);
                    SetHeightScalePositionOffsetY(ModelIndex.Right, Math.VECTOR3_ZERO, normalizeTime);
                }
                else
                {
                    SetHeightScalePositionOffsetY(ModelIndex.Left, GetModelOffset(ModelIndex.Left,normalizeTime), 1.0f - normalizeTime);
                    SetHeightScalePositionOffsetY(ModelIndex.Right, GetModelOffset(ModelIndex.Right, normalizeTime),1.0f - normalizeTime);
                }
                SetCenterCharacterPosition(_moveTargetIndex, normalizeTime);
            }
        }

        public void UpdateViewer()
        {
            UpdateCameraPosture();

            if (_modelArray == null)
                return;

            for (int i = 0; i < _modelArray.Length; i++)
            {
                if (_modelArray[i] == null)
                    continue;

                //リーダー(0)が手前になるように
                //引数のorderがでかいほど手前に来る
                _modelArray[i].UpdateGraphicSettings(_modelArray.Length - i);
            }
        }

        /// <summary>
        /// 開始画面のモードに応じてカメラ位置を変更
        /// </summary>
        public void ChangeCameraPosition(SingleModeStartView.Step step)
        {
            Vector3 cameraPosition;
            Quaternion charaRotation;
            var model = GetModel();
            if (model == null)
            {
                return;
            }

            _initialCameraPosition = _cameraTransform.localPosition;
            //画面遷移中に呼ぶとずれるので明示的にViewを指定
            _cameraController.SetCameraPosWithFixCharacterScale(SceneDefine.ViewId.PhotoStudioCharaViewer, model);
            cameraPosition = _cameraController.transform.localPosition + CAMERA_POS_OFFSET_CARD_SELECT;
            charaRotation = CAMERA_ROTATION_DEFAULT;
            switch (step)
            {
                case SingleModeStartView.Step.CardSelect:
                    //画面遷移中に呼ぶとずれるので明示的にViewを指定
                    _cameraController.SetCameraPosWithFixCharacterScale(SceneDefine.ViewId.PhotoStudioCharaViewer, model);
                    cameraPosition = _cameraController.transform.localPosition + CAMERA_POS_OFFSET_CARD_SELECT;
                    charaRotation = CAMERA_ROTATION_DEFAULT;
                    break;
                case SingleModeStartView.Step.SuccessionSelect:
                case SingleModeStartView.Step.EquipSelect: //最終確認画面でキャラ詳細に遷移、そこから戻ってくる可能性があるためカメラ位置を初期化できるようにする
                    _cameraController.SetCameraPosWithFixCharacterScale(SceneDefine.ViewId.PhotoStudioCharaViewer, model);
                    cameraPosition = _cameraController.transform.localPosition + CAMERA_POS_OFFSET_CARD_SELECT;
                    cameraPosition.x = 0;
                    charaRotation = CAMERA_ROTATION_DEFAULT;
                    break;
                default:
                    //リストUI用のEtoEフラグ
                    _cameraController.ResetFOV();
                    cameraPosition = CAMERA_POS_DEFAULT;
                    charaRotation = CAMERA_ROTATION_DEFAULT;
                    break;
            }
            _cameraController.transform.localPosition = cameraPosition;
            model.transform.localRotation = charaRotation;
        }

        #region モデル操作

        /// <summary>
        /// モデルの表示切替
        /// </summary>
        /// <param name="visible"></param>
        /// <param name="index"></param>
        public void SetModelVisible(bool visible, int index = 0)
        {
            var model = GetModel(index);
            if (model == null) return;

            var layer = visible ? CharaLayer : NoVisibleLayer;
            model.gameObject.SetLayerRecursively(layer);

            if (visible)
            {
                ApplyCharacterColor(model);
            }
        }

        /// <summary>
        /// 全モデルの表示切替
        /// </summary>
        /// <param name="visible"></param>
        public void SetModelVisibleAll(bool visible)
        {
            SetModelVisible(visible);
            SetModelVisible(visible, 1);
            SetModelVisible(visible, 2);
        }

        /// <summary>
        /// モデル削除
        /// </summary>
        /// <param name="index"></param>
        private void DestroyModel(int index)
        {
            if (index < 0 || index >= _modelArray.Length)
                return;

            if (_modelArray[index] != null)
            {
                Destroy(_modelArray[index].gameObject);
                _modelArray[index] = null;
            }
        }

        /// <summary>
        /// インデックスからモデル取得
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public SingleRaceModelController GetModel(int index = 0)
        {
            if (index < 0 || index >= _modelArray.Length)
            {
                return null;
            }

            return _modelArray[index];
        }

        /// <summary>
        /// 先頭インデックスでキャラを作成
        /// </summary>
        /// <param name="idSet"></param>
        /// <param name="onCreated"></param>
        public void CreateModel(CardDressIdSet idSet, System.Action<SingleRaceModelController> onCreated = null)
        {
            CreateModel(0, idSet, onCreated);
        }

        /// <summary>
        /// 1体読み込んで表示
        /// </summary>
        public void CreateModel(int index, CardDressIdSet idSet, System.Action<SingleRaceModelController> onCreated = null)
        {
            if (index < 0 || index >= _modelArray.Length)
            {
                return;
            }

            CreateInternal(index, idSet._cardId, idSet._dressId, _singleCharaLocator, m =>
            {
                onCreated?.Invoke(m);
            });
        }

        /// <summary>
        /// 同期キャラロード
        /// ** 重いので非同期にしたい
        /// </summary>
        /// <param name="index"></param>
        /// <param name="cardId"></param>
        /// <param name="dressId"></param>
        /// <param name="onCreated"></param>
        private void CreateInternal(int index, int cardId, int dressId, Transform root, System.Action<SingleRaceModelController> onCreated)
        {
            //マスター存在チェック
            var masterData = MasterDataManager.Instance.masterCardData.Get(cardId);
            if (masterData == null)
            {
                onCreated(null);
                return;
            }

            //既にいる場合返す
            var model = GetModel(index);
            if (model != null)
            {
                var currentInfo = model.GetBuildInfo();
                if (currentInfo.CardId == cardId && currentInfo.DressId == dressId)
                {
                    //親が違ってれば付けなおす
                    if (model.transform.parent != root)
                    {
                        model.transform.SetParent(root);
                        model.transform.localPosition = Math.VECTOR3_ZERO;
                        model.transform.localRotation = Math.QUATERNION_IDENTITY;
                    }
                    SetModelVisible(true, index);
                    onCreated(model);
                    return;
                }
            }

            //違うキャラがいるかもしれんので消す
            DestroyModel(index);

            var buildInfo = new CharacterBuildInfo(cardId, masterData.CharaId, dressId, ModelLoader.ControllerType.SingleRace);
            var modelObject = ModelLoader.CreateModel(buildInfo);
#if UNITY_EDITOR
            modelObject.name = buildInfo.CharaId.ToString();
#endif
            var modelController = modelObject.GetComponent<SingleRaceModelController>();
            modelController.transform.SetParent(root);
            modelController.transform.localPosition = Math.VECTOR3_ZERO;
            modelController.transform.localRotation = Math.QUATERNION_IDENTITY;

            //表示をスイッチするときにキャラが非表示レイヤになるので対策としてAlwaysAnimateを指定している。
            modelController.SetCullingMode(AnimatorCullingMode.AlwaysAnimate);
            modelController.ApplyCySpring();
            modelController.ResetCyspring();
            modelController.ReserveWarmingUpCySpring();
            ApplyCharacterColor(modelController);
            _modelArray[index] = modelController;

            //コールバック発行
            onCreated(modelController);
        }

        /// <summary>
        /// カメラの移動状態によって立ち位置にオフセットがかかる
        /// </summary>
        /// <param name="index"></param>
        /// <param name="rate"></param>
        /// <returns></returns>
        private Vector3 GetModelOffset(ModelIndex index,float rate = 1.0f)
        {
            Vector3 offsetPosition = Math.VECTOR3_ZERO;
            //カメラ移動状態によって移動量に補正がかかる
            switch(_moveTargetIndex)
            {
                case ModelIndex.Center:
                    switch(index)
                    {
                        case ModelIndex.Left:
                            offsetPosition = Vector3.Lerp(Math.VECTOR3_ZERO, _beginLeftCharacterOffset, rate);
                            break;
                        case ModelIndex.Right:
                            offsetPosition = Vector3.Lerp(Math.VECTOR3_ZERO, _beginRightCharacterOffset, rate);
                            break;
                    }
                    break;
                case ModelIndex.Left:
                    if(index == ModelIndex.Right)
                    {
                        offsetPosition = Vector3.Lerp(Math.VECTOR3_ZERO, _beginRightCharacterOffset, rate);
                    }
                    break;
                case ModelIndex.Right:
                    if (index == ModelIndex.Left)
                    {
                        offsetPosition = Vector3.Lerp(Math.VECTOR3_ZERO, _beginLeftCharacterOffset, rate);
                    }
                    break;
            }
            return offsetPosition;
        }

        /// <summary>
        /// キャラの位置指定
        /// </summary>
        /// <param name="index"></param>
        public void SetModelPosition(ModelIndex index,bool isHeightDiff = false)
        {
            var model = GetModel((int)index);
            if (model == null)
                return;
            switch (index)
            {
                case ModelIndex.Center:
                    model.CacheTransform.localPosition = _settingData.CenterCharacterPosition;
                    break;
                case ModelIndex.Left:
                    if (isHeightDiff)
                    {
                        //身長差を考慮して設定する
                        SetHeightScalePositionOffsetY(ModelIndex.Left, Math.VECTOR3_ZERO, 1.0f);
                    }
                    else
                    {
                        model.CacheTransform.localPosition = _settingData.LeftCharacterPosition + GetModelOffset(index);
                    }
                    break;
                case ModelIndex.Right:
                    if (isHeightDiff)
                    {
                        SetHeightScalePositionOffsetY(ModelIndex.Right, Math.VECTOR3_ZERO, 1.0f);
                    }
                    else
                    {
                        model.CacheTransform.localPosition = _settingData.RightCharacterPosition + GetModelOffset(index);
                    }
                    break;
            }
        }

        #endregion

        #region カメラ移動

        private void SetCenterCharacterPosition(ModelIndex moveToCameraIndex, float rate)
        {
            var centerModel = GetModel();
            if (centerModel == null)
                return;

            Vector3 offset = Math.VECTOR3_ZERO;
            switch (moveToCameraIndex)
            {
                case ModelIndex.Left:
                    offset = _settingData.MoveToLeftCenterCharacterOffset;
                    break;

                case ModelIndex.Right:
                    offset = _settingData.MoveToRightCenterCharacterOffset;
                    break;
            }
            centerModel.CacheTransform.localPosition = Vector3.Lerp(_beginCenterCharacterPosition, _settingData.CenterCharacterPosition + offset, rate);
        }

        private float GetHeightScalePositionOffsetY(ModelIndex index,SingleRaceModelController targetModel, SingleRaceModelController centerModel)
        {
            float targetHeight = targetModel.GetHeightRate();
            float centerHeight = centerModel.GetHeightRate();
            float diff = targetHeight - centerHeight;   //中心キャラより背が高いか

            float y;
            if (index == ModelIndex.Left)
            {
                if (diff >= 0.0f)
                {
                    y = _settingData.LeftMaxHeightOffsetY;
                }
                else
                {
                    y = _settingData.LeftMinHeightOffsetY;
                }
            }
            else
            {
                if (diff >= 0.0f)
                {
                    y = _settingData.RightMaxHeightOffsetY;
                }
                else
                {
                    y = _settingData.RightMinHeightOffsetY;
                }
            }

            return y * Mathf.Abs(diff);
        }

        private void SetHeightScalePositionOffsetY(ModelIndex index, Vector3 offsetPosition,float rate)
        {
            //中心の人はいじらない
            if (index == ModelIndex.Center)
                return;

            var centerModel = GetModel();
            if (centerModel == null)
                return;

            var targetModel = GetModel((int)index);
            if (targetModel == null)
                return;

            float y = GetHeightScalePositionOffsetY(index, targetModel, centerModel);
            float baseY;
            float angleY;
            Vector3 position;
            if (index == ModelIndex.Left)
            {
                position = _settingData.LeftCharacterPosition;
                baseY = _settingData.LeftHeightOffsetBaseY;
                angleY = _settingData.LeftAngleY;
            }
            else
            {
                position = _settingData.RightCharacterPosition;
                baseY = _settingData.RightHeightOffsetBaseY;
                angleY = _settingData.RightAngleY;
            }

            y = (baseY + y) * rate;
            position += offsetPosition;
            position.y += y;
            targetModel.CacheTransform.localPosition = position;
            targetModel.CacheTransform.localRotation = Quaternion.Euler(0, angleY * rate, 0);
        }

        /// <summary>
        /// カメラを移動する(継承専用)
        /// </summary>
        /// <param name="index"></param>
        /// <param name="moveTime"></param>
        public void MoveToCamera(ModelIndex index,float moveTime)
        {
            //_initialCameraPositionはスケール考慮する前の位置
            var model = GetModel((int)index);
            if(model == null)
            {
                //いない場合はセンターを中心にする
                model = GetModel();
                //それでもいない場合は想定していない呼び出し
                if (model == null)
                    return;
            }
            var centerModel = GetModel();
            _beginCameraPosition = _cameraTransform.localPosition;
            //SetCameraPosWithFixCharacterScale内でカメラ位置使用されるので初期位置に戻す必要がある
            _cameraTransform.localPosition = _initialCameraPosition;
            _cameraController.SetCameraPosWithFixCharacterScale(model, movePosX:false);
            _endCameraPosition = _cameraController.transform.localPosition + CAMERA_POS_OFFSET_CARD_SELECT;
            _beginBlurStrength = _bgBlur.BlurStrength;
            _endBlurStrength = 0.0f;
            _beginCenterCharacterPosition = centerModel.CacheTransform.localPosition;
            _beginLeftCharacterOffset = Math.VECTOR3_ZERO;
            _beginRightCharacterOffset = Math.VECTOR3_ZERO;
            switch (index)
            {
                case ModelIndex.Center:
                    _endCameraPosition = _endCameraPosition - _settingData.CenterCharacterPosition;
                    //前の状態に依存する
                    switch(_moveTargetIndex)
                    {
                        case ModelIndex.Left:
                            _beginRightCharacterOffset = _settingData.MoveToLeftRightCharacterOffset;   //対象の右のキャラ
                            break;
                        case ModelIndex.Right:
                            _beginLeftCharacterOffset = _settingData.MoveToRightLeftCharacterOffset;    //対象は左のキャラ
                            break;
                    }
                    break;
                case ModelIndex.Left:
                    _endCameraPosition = _endCameraPosition - _settingData.LeftCharacterPosition;
                    _endBlurStrength = _settingData.BgBlurStrength;
                    _beginRightCharacterOffset = _settingData.MoveToLeftRightCharacterOffset;   //対象の右のキャラ
                    break;
                case ModelIndex.Right:
                    _endCameraPosition = _endCameraPosition - _settingData.RightCharacterPosition;
                    _endBlurStrength = _settingData.BgBlurStrength;
                    _beginLeftCharacterOffset = _settingData.MoveToRightLeftCharacterOffset;    //対象は左のキャラ
                    break;
            }

            _moveCameraTime = moveTime;
            _moveCameraDeltaTime = 0.0f;
            _moveTargetIndex = index;
            if (moveTime <= 0.0f)
            {
                //即座に移動する
                _cameraTransform.localPosition = _endCameraPosition;
                _bgBlur.BlurStrength = _endBlurStrength;
                //中心へ移動する場合には左右キャラの足元にオフセットをかける
                if (index == ModelIndex.Center)
                {
                    //最大の影響力を受ける
                    SetHeightScalePositionOffsetY(ModelIndex.Left, Math.VECTOR3_ZERO, 1.0f);
                    SetHeightScalePositionOffsetY(ModelIndex.Right, Math.VECTOR3_ZERO, 1.0f);
                }
                else
                {
                    //それ以外の場合は原点
                    SetModelPosition(ModelIndex.Left);
                    SetModelPosition(ModelIndex.Right);
                }
                //中央キャラを目標位置へ
                SetCenterCharacterPosition(index, 1.0f);
            }
            else
            {
                _cameraTransform.localPosition = _beginCameraPosition;
                //ここの時は補間かけながら下がる
            }
        }

        #endregion

        /// <summary>
        /// イメージエフェクト準備
        /// </summary>
        private void SetupImageEffect()
        {
            _cameraController.Init(_camera);

            _bgBlur = new BGBlur();
            _to3DCommandBuffer = new RenderTextureTo3DCommandBuffer();

            var lowResolutionCamera = _camera.gameObject.AddComponent<LowResolutionCamera>();
            lowResolutionCamera.OnCreateTextureCallback += OnCreateTexture;
            lowResolutionCamera.OnReleaseTextureCallback += OnReleaseTexture;
            lowResolutionCamera.InitializeVerticalWithBackGround();
            _lowResolutionCamera = lowResolutionCamera;
            
            _imageEffect = _camera.gameObject.AddComponent<GallopCharacterImageEffect>();
            _imageEffect.Initialize();
            //URP:置き換え対応
            //imageEffect.ColorCorrectionParam.Initialize();
            _imageEffect.LoadShader();
            _imageEffect.CheckResources();

            var param = ResourceManager.LoadOnView<GallopCharacterImageEffectParameter>(ResourcePath.PHOTO_STUDIO_TOP_ENV_PARAM);
            param.CopyTo(_imageEffect);
        }

        /// <summary>
        /// キャラカラーを適用
        /// </summary>
        private void ApplyCharacterColor(ModelController model)
        {
            _imageEffect.ModelController = model;
            _imageEffect.UpdateCharacterColor();
        }

        private void ReleaseBackgroundRenderTexture()
        {
            if (_backgroundRenderTexture != null)
            {
                _backgroundRenderTexture.Release();
                GameObject.Destroy(_backgroundRenderTexture);
                _backgroundRenderTexture = null;
            }

            if(_bgBlur != null)
            {
                _bgBlur.Release();
            }
            if(_to3DCommandBuffer != null)
            {
                _to3DCommandBuffer.Release();
            }
        }

        /// <summary>
        /// 低解像度カメラのRenderTexture生成時
        /// </summary>
        /// <param name="lowResolutionCamera"></param>
        private void OnCreateTexture(LowResolutionCamera lowResolutionCamera)
        {
            if (UIManager.BGCamera == null)
            {
                return;
            }

            ReleaseBackgroundRenderTexture();

            //BGCameraの内容をこっちに描画してもらう
            _backgroundRenderTexture = new RenderTexture(lowResolutionCamera.Texture.width, lowResolutionCamera.Texture.height, 0);
#if CYG_DEBUG
            _backgroundRenderTexture.name = "SingleModeStartResultCharaViewer.OnCreateTexture._backgroundRenderTexture";
#endif
            _backgroundRenderTexture.antiAliasing = GraphicSettings.Instance.Get3DAntiAliasingLevel(false);
            _backgroundRenderTexture.Create();

            _bgBlur.Initialize();
            _to3DCommandBuffer.Initialize();
            _to3DCommandBuffer.MakeCommand(_backgroundRenderTexture);
            _to3DCommandBuffer.AttachCommand(lowResolutionCamera.Camera);

            UIManager.Instance.SetBgCameraRenderTexture(_backgroundRenderTexture);
            //URP:不要
            /*
            //BGCamera描画終了時にブラーをかける
            if (!UIManager.BGCamera.gameObject.TryGetComponent<CameraDrawEventCallback>(out var drawCallback))
            {
                drawCallback = UIManager.BGCamera.gameObject.AddComponent<CameraDrawEventCallback>();
            }
            drawCallback.OnPostRenderCallback = (camera) =>
            {
                _bgBlur.OnRender(_backgroundRenderTexture);
            };
            */

            //lowResolutionCamera.Camera.clearFlags = CameraClearFlags.Nothing; //背景カメラがクリアするので、ここではクリアしない
            //BgCanvasの設定がOnCreateTextureで変わっているはずなので、背景のサイズを設定しなおす
            BGManager.Instance.RecalcBgSize();
        }

        /// <summary>
        /// 低解像度カメラのRenderTexture破棄時
        /// </summary>
        /// <param name="lowResoCamera"></param>
        private void OnReleaseTexture(LowResolutionCamera lowResoCamera)
        {
            if (UIManager.BGCamera == null)
            {
                return;
            }
            if(_to3DCommandBuffer != null)
                _to3DCommandBuffer.RemoveCommand();

            UIManager.Instance.SetBgCameraRenderTexture(null);
            //URP:不要
            /*
            //Callbackを取り除く
            if (UIManager.BGCamera.gameObject.TryGetComponent<CameraDrawEventCallback>(out var drawCallback))
            {
                Destroy(drawCallback);
            }
            */
            ReleaseBackgroundRenderTexture();
        }
    }
}
