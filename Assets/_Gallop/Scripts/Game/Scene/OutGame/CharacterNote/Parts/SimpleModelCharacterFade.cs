using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// キャラ（SimpleModelController）のフェードを行う
    /// </summary>
    /// <remarks>
    /// 利用事例 : トレーナーノート(CharacterNote3DViewer)
    /// </remarks>
    [AddComponentMenu("")]
    public class SimpleModelCharacterFade : MonoBehaviour
    {
        #region class, enum, const

        //URP:置き換え対応
        //内部でDepthバッファは作られるので、不要
        //private const int CHARA_RENDER_TEXTURE_DEPTH = GallopFrameBuffer.DEPTH_BUFFER;
        private const int CHARA_RENDER_TEXTURE_DEPTH = 0;
        private const float WARMUP_TIME_FOR_FADE_IN = 0.5f;

        public enum FadeType
        {
            None,
            FadeIn,
            FadeOut,
        }

        #endregion

        #region SerializeField, Variable

        private FadeType _fadeType;
        private RenderTexture _charaRenderTexture = null;
        private RawImageCommon _charaFadeImage = null;
        private Camera _mainCamera;
        private Camera _charaFadeCamera;
        private CameraController _cameraController;

        #endregion

        #region Method

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="mainCamera"></param>
        /// <param name="fadeCamera"></param>
        /// <param name="charaFadeImage"></param>
        public void Init(Camera mainCamera, Camera fadeCamera, RawImageCommon charaFadeImage)
        {
            _mainCamera = mainCamera;
            _charaFadeCamera = fadeCamera;

            // レンダリング用テクスチャを初期化
            SetupCaptureRenderTexture(charaFadeImage);

            // フェード用カメラを初期化
            SetupCharaFadeCamera();
        }

        /// <summary>
        /// 解放処理
        /// </summary>
        public void Release()
        {
            if (_charaRenderTexture != null)
            {
                _charaRenderTexture.Release();
                _charaRenderTexture = null;

                _charaFadeImage.texture = null;
                _charaFadeCamera.targetTexture = null;
            }
        }

        /// <summary>
        /// 指定されたキャラのフェードを開始する
        /// </summary>
        /// <param name="isFadeIn"></param>
        /// <param name="model"></param>
        public void StartFadeChara(bool isFadeIn, SimpleModelController model)
        {
            var type = isFadeIn ? FadeType.FadeIn : FadeType.FadeOut;
            if (isFadeIn)
            {
                // フェード前にCySpringの揺れを抑制する
                model.ReserveWarmingUpCySpring(WARMUP_TIME_FOR_FADE_IN);
            }

            // キャラのセットアップ
            SetupCharacter(type, model);

            // フェード開始
            StartFade(type);
        }

        /// <summary>
        /// 指定されたキャラのフェードを開始する
        /// </summary>
        /// <param name="isFadeIn"></param>
        /// <param name="model"></param>
        public void StartFadeChara(bool isFadeIn, MiniModelController model)
        {
            var type = isFadeIn ? FadeType.FadeIn : FadeType.FadeOut;
            if (isFadeIn)
            {
                // フェード前にCySpringの揺れを抑制する
                model.ReserveWarmingUpCySpring(WARMUP_TIME_FOR_FADE_IN);
            }

            // キャラのセットアップ
            SetupCharacter(type, model);

            // フェード開始
            StartFade(type);
        }

        /// <summary>
        /// フェード開始 
        /// </summary>
        /// <param name="type"></param>
        public void StartFade(FadeType type)
        {
            _fadeType = type;

            // RenderTextureとCameraを有効化
            _charaFadeImage.SetActiveWithCheck(true);
            _charaFadeCamera.enabled = true;

            // カメラワークによってキャラカメラが移動する可能性があるので、毎回カメラ初期化
            SetupCharaFadeCamera();
        }

        /// <summary>
        /// キャラのフェード更新
        /// </summary>
        /// <param name="alpha"></param>
        public void AlterUpdate(float alpha)
        {
            _charaFadeImage.canvasRenderer.SetAlpha(alpha);
        }

        /// <summary>
        /// 指定されたキャラのフェードを終了
        /// </summary>
        public void FinishFade(SimpleModelController model)
        {
            ResetChara(model);
            ResetFade();
        }

        /// <summary>
        /// 指定されたキャラのフェードを終了
        /// </summary>
        public void FinishFade(MiniModelController model)
        {
            ResetChara(model);
            ResetFade();
        }

        /// <summary>
        /// フェード設定を解除
        /// </summary>
        public void ResetFade()
        {
            _charaFadeImage.SetActiveWithCheck(false);
            _charaFadeCamera.enabled = false;
            _fadeType = FadeType.None;

            if (_cameraController != null)
            {
                _cameraController.EnableBackgroundTextureBlitFromUI(false);
                _cameraController.enabled = false;
            }
        }

        /// <summary>
        /// レンダリング用テクスチャを初期化
        /// </summary>
        /// <param name="charaFadeImage"></param>
        private void SetupCaptureRenderTexture(RawImageCommon charaFadeImage)
        {
#if ANDROID_PC
            //AndroidPC番はカメラとゲーム画面のサイズがあっていないので固定値
            int targetHeight = Screen.RenderTextureHeight;
            int targetWidth = Screen.RenderTextureWidth;
#else
            // ViewportRectを加味した、必要なテクスチャサイズを求める
            int targetHeight = _mainCamera.pixelHeight;
            int targetWidth = _mainCamera.pixelWidth;
#endif
            
            if (_charaRenderTexture != null)
            {
                // 縦か横の幅が一致しない場合は作り直す
                if (_charaRenderTexture.width != targetWidth || _charaRenderTexture.height != targetHeight)
                {
                    _charaRenderTexture.Release();
                    _charaRenderTexture = null;
                }
            }

            //未作成 or 再作成の場合
            if (_charaRenderTexture == null)
            {
                // FadeImageと同じサイズでテクスチャを作る
                _charaRenderTexture = new RenderTexture(targetWidth, targetHeight, CHARA_RENDER_TEXTURE_DEPTH, RenderTextureFormat.ARGB32);
#if CYG_DEBUG
                _charaRenderTexture.name = "SimpleModelCharacterFade.Init._charaRenderTexture";
#endif
                if (!_charaRenderTexture.Create())
                {
#if CYG_DEBUG
                    Debug.LogError("CharaRenderTextureの生成に失敗しました");
#endif
                }
            }

            // テクスチャへの参照をセット
            _charaFadeImage = charaFadeImage;
            _charaFadeImage.texture = _charaRenderTexture;
            _charaFadeImage.gameObject.SetActive(false); //有効になるまで無効にしておく
        }

        /// <summary>
        /// フェード用カメラを初期化
        /// </summary>
        private void SetupCharaFadeCamera()
        {
            _charaFadeCamera.CopyFrom(_mainCamera);
            //ViewportRectについてだけは、初期の値で固定しておく
            //縦持ち時のViewPortRectにひきずられるとキャプチャ画像がズレてしまうため
            _charaFadeCamera.rect = new Rect(0f, 0f, 1f, 1f);

            // BackgroundColorについて (#60489)
            // 通常のクリアカラー(0, 0, 0, 0)にすると半透明衣装を使うキャラにて
            // 半透明部分に黒い色がのってしまう
            // それを回避するために(1, 1, 1, 0)を使う事にした

            _charaFadeCamera.clearFlags = CameraClearFlags.SolidColor;
            _charaFadeCamera.backgroundColor = GameDefine.COLOR_CLEAR_WHITE;
            _charaFadeCamera.cullingMask = GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3D);
            _charaFadeCamera.targetTexture = _charaRenderTexture;

            // FadeCameraにCameraControllerをアタッチして背景を描画してもらう
            _cameraController = _charaFadeCamera.gameObject.GetOrAddComponent<CameraController>();
            if (_cameraController != null)
            {
                _cameraController.enabled = true;

                _cameraController.Init(_charaFadeCamera);
                _cameraController.EnableBackgroundTextureBlitFromUI(true);
            }
        }

        /// <summary>
        /// キャラのフェード初期化
        /// </summary>
        /// <param name="type"></param>
        /// <param name="model"></param>
        private void SetupCharacter(FadeType type, SimpleModelController model)
        {
            // キャラを表示状態に
            model.SetVisible(true);

            // キャラのLayerIndexを更新
            model.FadeType = type;
            model.SetLayerIndex(GraphicSettings.LayerIndex.Layer3D);
        }

        /// <summary>
        /// キャラのフェード状態を解除
        /// </summary>
        private void ResetChara(SimpleModelController model)
        {
            if (model.FadeType == FadeType.FadeOut)
            {
                // フェードアウトしたときはVisible無効に
                model.SetVisible(false);
            }

            // キャラのLayerIndexを更新
            // レイヤー設定時にFadeTypeを参照するのでFadeTypeを先にセットすること
            model.FadeType = FadeType.None;
            model.SetLayerIndex(GraphicSettings.LayerIndex.LayerCHAR);
        }

        /// <summary>
        /// ミニキャラのフェード初期化
        /// </summary>
        private void SetupCharacter(FadeType type, MiniModelController model)
        {
            // キャラを表示状態に
            model.SetVisible(true);

            // キャラのLayerIndexを更新
            model.FadeType = type;
            model.SetLayerIndex(GraphicSettings.LayerIndex.Layer3D);
        }

        /// <summary>
        /// ミニキャラのフェード状態を解除
        /// </summary>
        private void ResetChara(MiniModelController model)
        {
            if (model.FadeType == FadeType.FadeOut)
            {
                // フェードアウトしたときはVisible無効に
                model.SetVisible(false);
            }

            // キャラのLayerIndexを更新
            // レイヤー設定時にFadeTypeを参照するのでFadeTypeを先にセットすること
            model.FadeType = FadeType.None;
            model.SetLayerIndex(GraphicSettings.LayerIndex.LayerCHAR);
        }

        #endregion
    }
}