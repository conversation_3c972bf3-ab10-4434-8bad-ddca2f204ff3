using System.Collections;
using System.Linq;
using UnityEngine;
using static Gallop.StaticVariableDefine.OutGame.CharacterNoteHubViewController;

namespace Gallop
{
    /// <summary>
    /// ホーム複合ビュー
    /// </summary>
    public class CharacterNoteHubView : ViewBase
    {
    }

    public class CharacterNoteHubViewController : Hu<PERSON>ViewControllerBase
    {
        public class ViewInfo : HubViewControllerBase.HubViewInfo
        {
            public CharacterNoteTopViewController.ViewInfo TopViewInfo { get; set; }
            public GalleryViewController.ViewInfo GalleryInfo { get; set; }
            public TalkGalleryViewController.ViewInfo TalkGalleryInfo { get; set; }
        }
        
        private CharacterNoteHubView _hubView;

        /// <summary>
        /// 子ビュー配列取得
        /// </summary>
        public override SceneDefine.ViewId[] GetChildViewIdArray()
        {
            return VIEW_ID_ARRAY;
        }


        /// <summary>
        /// 子ビューに渡すViewInfo選定
        /// </summary>
        public override IViewInfo GetChildViewInfo(SceneDefine.ViewId viewId, IViewInfo viewInfo)
        {
            var hubViewInfo = viewInfo as ViewInfo;

            if (hubViewInfo != null)
            {
                switch (viewId)
                {
                    case SceneDefine.ViewId.CharacterNoteTop: return hubViewInfo.TopViewInfo;
                    case SceneDefine.ViewId.Gallery: return hubViewInfo.GalleryInfo;
                    case SceneDefine.ViewId.TalkGallery: return hubViewInfo.TalkGalleryInfo;
                }
            }

            return viewInfo;
        }

        public override IEnumerator PreRegisterDownload()
        {
            yield return base.PreRegisterDownload();
        }
        
        public override IEnumerator InitializeEachPlayIn()
        {
            yield return base.InitializeEachPlayIn();
        }

        public override IEnumerator PlayInView()
        {
            yield return base.PlayInView();
        }

        public override IEnumerator InitializeView()
        {
            _hubView = _view as CharacterNoteHubView;
            yield return base.InitializeView();
        }

        public override IEnumerator PlayOutView()
        {
            yield return base.PlayOutView();
        }
    }
}