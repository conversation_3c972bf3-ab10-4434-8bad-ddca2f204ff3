using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{

    /// <summary>
    /// おみくじキャンペーン開始ダイアログ
    /// </summary>
    public class DialogCampaignRaffleStart : DialogInnerBase
    {

        #region SerializeField

        [SerializeField]
        private RawImageCommon _image;

        [SerializeField]
        private TextCommon _campaignRaffleStartText;

        #endregion

        #region Member


        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        System.Action _onCenterButtonCallBack;

        #region Method

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.IMG_CAMPAIGN_OMIKUJI_00);
        }

        /// <summary>
        /// 開く
        /// </summary>
        public static void Open(System.Action onCenterButtonCallBack)
        {
            var content = LoadAndInstantiatePrefab<DialogCampaignRaffleStart>(ResourcePath.DIALOG_CAMPAIGN_RAFFLE_START);
            var dialogData = content.CreateDialogData();

            var dialog = DialogManager.PushDialog(dialogData);
            content.Setup(dialog, onCenterButtonCallBack);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Outgame419102.Text();

            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.CenterButtonColor = DialogCommon.ButtonColor.White;
            dialogData.CenterButtonCallBack = OnCenterButtonCallBack;

            dialogData.FooterText = TextId.Outgame419108.Text();
            dialogData.IsFooterNotificationText = true;

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="exchangeResult"></param>
        private void Setup(DialogCommon dialog, System.Action onCenterButtonCallBack)
        {
            _onCenterButtonCallBack = onCenterButtonCallBack;

            SetupImage(dialog);

            SetupCampaignRaffleStartText();
        }

        private void SetupImage(DialogCommon dialog)
        {
            var texture = ResourceManager.LoadOnHash<Texture>(ResourcePath.IMG_CAMPAIGN_OMIKUJI_00, dialog.DialogData.DialogHash);
            _image.texture = texture;
        }

        private void SetupCampaignRaffleStartText()
        {
            var masterCampaignData = MasterDataManager.Instance.masterCampaignData.GetRaffleActiveCampaignData();
            if (masterCampaignData == null)
            {
                return;
            }

            var openCampaignStartText = TextId.Outgame419106.Format(MasterDataManager.Instance.masterString.GetText(MasterString.Category.CampaignTitle, masterCampaignData.CampaignId));

            int hour = (int)(masterCampaignData.EffectValue1 / 3600);
            var openCampaignDetailText = TextId.Outgame419107.Format(hour, MasterDataManager.Instance.masterString.GetText(MasterString.Category.CampaignExplain, masterCampaignData.CampaignId));

            _campaignRaffleStartText.text = TextId.Common0126.Format(openCampaignStartText, openCampaignDetailText);
        }

        private void OnCenterButtonCallBack(DialogCommon dialog)
        {
            _onCenterButtonCallBack?.Invoke();
        }

        #endregion
    }
}
