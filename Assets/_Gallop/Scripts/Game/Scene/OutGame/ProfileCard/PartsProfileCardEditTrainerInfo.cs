using Gallop;
using System;
using Cute.Http;
using UnityEngine;
using System.Linq;
using System.Collections.Generic;
using CodeStage.AntiCheat.ObscuredTypes;
using UnityEngine.Events;

namespace Gallop
{
    public class PartsProfileCardEditTrainerInfo : MonoBehaviour
    {
        //////////////////////////////////////////////////////////////////////////////////
        #region SerializeField
        //////////////////////////////////////////////////////////////////////////////////

        /// <summary> アニメーション制御用 </summary>
        [SerializeField]
        public ProfileCardViewAnimation ViewAnimation = null;
        
        [SerializeField]
        private ScrollRectCommon _scrollRectCommon = null;
        
        [Header("トレーナー情報")]
        //称号
        [SerializeField]
        private ButtonCommon _honorSelectButton;
        [SerializeField]
        private PartsHonorIcon _honorIcon = null;
        [SerializeField]
        private RawImageCommon _honorImageShadow = null;
        
        //代表ウマ娘
        [SerializeField]
        private CharacterButton _leaderCharacter = null;
        [SerializeField]
        private TextCommon _leaderCharacterName = null; 
        
        //サポカ
        [SerializeField]
        private CharacterButton _settingSupportCard = null;
        [SerializeField]
        private TextCommon _settingSupportCardTitle = null;
        [SerializeField]
        private TextCommon _settingSupportCardCharacterName = null;

        [Header("コメント")]
        //コメント
        [SerializeField]
        private InputFieldCommon _commentEditField = null;

        [Header("ID表示")]
        [SerializeField] 
        private PartsOnOffToggleSwitch _showIdSwitch = null;

        #endregion //SerializeField


        //////////////////////////////////////////////////////////////////////////////////
#region 変数
        //////////////////////////////////////////////////////////////////////////////////
        private int _honorId = 0;
        int _currentLeaderId; 
        int _currentSupportId;
        
        private Action<int> _onChangeLeader; 
        Action<int> _onChangeSupport;

        private Action _preChangeHonorView;

#endregion // 変数・定義

        //////////////////////////////////////////////////////////////////////////////////

#region Method
        //////////////////////////////////////////////////////////////////////////////////

        /// <summary>
        /// トレーナー情報コンテンツ設定
        /// </summary>
        public void SetUpTrainerContent(
            int honorId,
            int partnerTrainedCharaId,
            int supportCardId,
            string comment,
            bool isShowId,

            Action<int> onChangeLeader, 
            Action<int> onChangeSupport, 
            Action<string> onEditComment,
            Action<bool> onEditShowId,
            
            Action preChangeHonorView
            )
        {
            _honorId = honorId;
            _currentLeaderId = partnerTrainedCharaId;
            _currentSupportId = supportCardId;
            
            _onChangeLeader = onChangeLeader;
            _onChangeSupport = onChangeSupport;

            _preChangeHonorView = preChangeHonorView;

            //各ボタン設定
            _honorSelectButton.SetOnClick(OnClickHonorButton);
            _commentEditField.SetOnEndEdit(()=>
            {
                OnEndEdit();
                //名刺データ更新(コメント)
                onEditComment?.Invoke(_commentEditField.text);
            });
            _showIdSwitch.Setup(isShowId, onEditShowId);

            //称号設定
            SetHonorImage(honorId);

            //代表ウマ娘キャラ設定
            var trainedCharaInfo = WorkDataManager.Instance.TrainedCharaData.Get(_currentLeaderId);
            if (trainedCharaInfo != null)
            {
                SetLeaderCharacter(trainedCharaInfo);
            }

            //サポカ設定
            var supportCardInfo = WorkDataManager.Instance.SupportCardData.GetSupportCardData(_currentSupportId);
            if (supportCardInfo != null)
            {
                SetSupportCard(supportCardInfo);
            }

            SetUpComment(comment);
        }
        
        /// <summary>
        /// スクロール位置リセット
        /// </summary>
        public void ResetScrollPos()
        {
            _scrollRectCommon.verticalNormalizedPosition = 1.0f;
        }

#region UI

        /// <summary>
        /// 称号設定
        /// </summary>
        private void SetHonorImage(int honorId)
        {
            var workHonor = WorkDataManager.Instance.HonorData.GetHonor(honorId);
            if (workHonor == null)
                return;

            // 称号テクスチャー.
            _honorIcon.Setup(workHonor);

            // 影
            _honorImageShadow.texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetHonorLayer1ImagePath(honorId, workHonor.Step));
        }

        /// <summary>
        /// リーダー設定
        /// </summary>
        private void SetLeaderCharacter(WorkTrainedCharaData.TrainedCharaData trainedCharaInfo)
        {
            CharacterButtonInfo characterButtonInfo = new CharacterButtonInfo
            {
                Id = trainedCharaInfo.CardId,
                IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                IconSizeType = IconBase.SizeType.CharaNormal,
                TrainedChara = trainedCharaInfo,
                EnableRarity = false,
                RankImage = true,
                FinalTrainingRank = trainedCharaInfo.Rank,
                EnableButton = true,
                EnableBadge = false,
                EnableEventOpenNum = false
            };
            characterButtonInfo.SortInfoType = CharacterButtonInfo.SortInfo.Factor;

            _leaderCharacterName.text = trainedCharaInfo.Name;
            _leaderCharacter.Setup(characterButtonInfo);
            
            //ボタン設定
            _leaderCharacter.MyButton.SetOnClick(() =>
            {
                OnChangeLeader((changeLeaderId) =>
                {
                    //名刺データ変更(代表ウマ娘)
                    _onChangeLeader?.Invoke(changeLeaderId);
                });
            });
        }

        /// <summary>
        /// サポカ設定
        /// </summary>
        /// <param name="supportCardInfo"></param>
        private void SetSupportCard(WorkSupportCardData.SupportCardData supportCardInfo)
        {
            CharacterButtonInfo supportCardButtonInfo = new CharacterButtonInfo
            {
                IconSizeType = IconBase.SizeType.SupportCardNormal,
                Id = supportCardInfo.SupportCardId,
                IdType = CharacterButtonInfo.IdTypeEnum.Support,
                EnableButton = true,
                EnableRarity = true,
                Rarity = supportCardInfo.GetRarity(),
                LimitBreakCount = supportCardInfo.LimitBreakCount,
                Level = supportCardInfo.Level,
                EnableObtain = true,
            };
            _settingSupportCard.Setup(supportCardButtonInfo);
            _settingSupportCardTitle.text = supportCardInfo.GetTitleName();
            _settingSupportCardCharacterName.text = supportCardInfo.GetCharaName();
            
            //ボタン設定
            _settingSupportCard.MyButton.SetOnClick(() =>
            {
                OnChangeSupportCard((changeRentalSupportCardId) =>
                {
                    //名刺データ変更(サポートカード)
                    _onChangeSupport?.Invoke(changeRentalSupportCardId);
                });
            });
        }
        
        /// <summary>
        /// コメント文章設定
        /// </summary>
        /// <param name="commentText"></param>
        private void SetUpComment(string commentText)
        {
            _commentEditField.text = commentText;
            OnEndEdit();
        }
#endregion

#region Callback

        // 称号ボタンが押された時のコールバック
        private void OnClickHonorButton()
        {
            _preChangeHonorView?.Invoke();
            
            // ホーム以外の場所からの指定や複合viewからの細かい遷移をしたい場合は修正.
            SceneManager.Instance.StackViewForBack(new BackableStateInfo(SceneDefine.ViewId.ProfileCard, new ProfileCardViewInfo(SceneDefine.ViewId.ProfileCard, ProfileCardViewController.ViewState.Edit, ProfileCardEdit.CurrentStep.EditTrainerInfo)));
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.Honor);
        }

        /// <summary>
        /// 代表ウマ娘変更
        /// </summary>
        private void OnChangeLeader(Action<int> changeLeaderCharacter)
        {
            DialogTrainedCharaList.OpenForProfileCardPartnerSelect(_currentLeaderId, (selectTrainedCharaId) =>
            {
                _currentLeaderId = selectTrainedCharaId;
                var trainedCharaInfo = WorkDataManager.Instance.TrainedCharaData.Get(_currentLeaderId);
                SetLeaderCharacter(trainedCharaInfo);
                changeLeaderCharacter?.Invoke(_currentLeaderId);
            });
        }

        /// <summary>
        /// サポートカード変更
        /// </summary>
        private void OnChangeSupportCard(Action<int> changeRentalSupportCard)
        {
            DialogRentalSupportCardSelect.PushDialogForProfileCard(_currentSupportId, (selectSupportCardId) =>
            {
                _currentSupportId = selectSupportCardId;
                var supportCardInfo = WorkDataManager.Instance.SupportCardData.GetSupportCardData(_currentSupportId); 
                SetSupportCard(supportCardInfo);
                changeRentalSupportCard?.Invoke(_currentSupportId);
            });
        }

        /// <summary>
        /// 編集終了時に呼ばれる
        /// </summary>
        //private void OnEndEdit(string text)
        public void OnEndEdit()
        {
            // 改行除く
            _commentEditField.text = TextUtil.RemoveNewLine(_commentEditField.text);
            if (_commentEditField.text.Length > GameDefine.PROFILE_CARD_COMMENT_LIMIT)
            {
                _commentEditField.text = _commentEditField.text.Substring(0, GameDefine.PROFILE_CARD_COMMENT_LIMIT);
            }

            if (_commentEditField.text.Length <= 0)
            {
                _commentEditField.text = TextId.Common0188.Text();
            }
        }
        
#endregion

#endregion Method
    }
}