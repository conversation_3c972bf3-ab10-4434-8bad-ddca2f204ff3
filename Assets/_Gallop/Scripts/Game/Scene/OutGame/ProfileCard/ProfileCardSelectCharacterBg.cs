using Gallop;
using System;
using System.Collections;
using System.Collections.Generic;
using Cute.UI;
using UnityEngine;
using System.Linq;

namespace Gallop
{
    public class ProfileCardSelectCharacterBg : MonoBehaviour
    {
        private enum BgType
        {
            CharacterBg,
            CardBg
        }

        public enum BGSelectLevel
        {
            CATEGORY_CHOOSE,
            SELECT_IMAGE
        }
        
        /// <summary>
        /// 画像のカテゴリデータ
        /// </summary>
        public struct NameBgCategoryData
        {
            //カテゴリ主体
            public MasterNameCardBg.NameCardBg NameCardBg;
            //時間差分
            public List<int> TimeGroup;
            //その他差分
            public bool IsEvent;
        }

        #region MyRegion

        /// <summary>
        /// カード背景かキャラ背景か
        /// </summary>
        [SerializeField]
        private BgType _bgType = BgType.CharacterBg;

        /// <summary>
        /// 追加するボタン元
        /// </summary>
        [SerializeField]
        private ProfileCardBgSet _bgButtonSetOrigin = null;

        /// <summary>
        /// ボタンの親
        /// </summary>
        [SerializeField]
        private Transform _bgElementParent = null;

        // フィルター選択のボタン
        [SerializeField]
        private ButtonCommon _buttonFilter = null;
        
        // スクロール
        [SerializeField]
        private ScrollRectCommon _scrollCommon = null;

        // フィルター選択の文字
        [SerializeField]
        private TextCommon _textFilter = null;

        //ソートボタン
        [SerializeField]
        private ButtonCommon _buttonSort = null;

        //ソートボタン
        [SerializeField]
        private TextCommon _textnSort = null;
        
        // ソートのアイコン
        [SerializeField]
        private ImageCommon _iconSortStatus = null;

        /// <summary>
        /// カーソル
        /// </summary>
        [SerializeField]
        private CursorCommon _cursorCommon = null;

        private int _currentSelectBgId = 0;
        public int CurrentSelectId => _currentSelectBgId;

        private BGSelectLevel _currentLevel = BGSelectLevel.CATEGORY_CHOOSE;

        private int _currentFilter = 0;

        private List<GameObject> _BgPoolList = new List<GameObject>();

        private const float ONE_HEIGHT = 345.0f;

        private bool _sortOrderAsend = true;

        private ButtonCommon _rightButton = null;

        private Action<int> _onDecide = null;

        /// <summary>
        /// カード情報設定
        /// </summary>
        public void Init(int currentBgId, Action<int> onDecide = null)
        {
            _currentSelectBgId = currentBgId;
            if (_bgType == BgType.CharacterBg)
            {
                _currentFilter = SaveDataManager.Instance.SaveLoader.CharacterBgFilter;
                _sortOrderAsend = SaveDataManager.Instance.SaveLoader.CharacterBgSortAsc;
                //MasterDataの分だけ増やす
                RefreshList();
            }
            //名刺背景の時
            else if (_bgType == BgType.CardBg)
            {
                //全ファイル分作成
                for (int i = 0; i < ProfileCardView.CARD_BG_COUNT; i++)
                {
                    //ファイルの存在チェック
                    if (ResourceManager.IsExistAsset(ResourcePath.GetProfileCardBgPath(i)))
                    {
                        int cardIndex = i;
                        ProfileCardBgSet bgSet = GameObject.Instantiate(_bgButtonSetOrigin.gameObject).GetComponent<ProfileCardBgSet>();
                        bgSet.transform.SetParent(_bgElementParent);
                        bgSet.GetComponent<RectTransform>().localScale = Gallop.Math.VECTOR3_ONE;
                        bgSet.Init(cardIndex, (selectIndex) => { OnClickButtonForCardBg(bgSet, selectIndex); });

                        //現在選択中のIdに選択中カーソルを付ける
                        if (_currentSelectBgId == i && _cursorCommon != null)
                        {
                            _cursorCommon.SetParent(bgSet.gameObject);
                            //設定中ラベルオン
                            bgSet.UpdateLabel(true);
                        }
                    }
                }
            }

            _bgButtonSetOrigin.gameObject.SetActive(false);

            if (_cursorCommon != null)
            {
                _cursorCommon.SetActiveWithCheck(true);
            }
            _buttonFilter.SetActiveWithCheck(true);
            _buttonSort.SetActiveWithCheck(true);

            if (onDecide != null)
            {
                _onDecide = onDecide;
            }
        }

        /// <summary>
        /// トグル選択時
        /// </summary>
        /// <param name="index"></param>
        private void OnClickButtonForCardBg(ProfileCardBgSet bgSet, int selectId)
        {
            _currentSelectBgId = selectId;
             //カーソル移動
            _cursorCommon.SetParent(bgSet.gameObject);
            if (TempData.Instance.TrainerProfileCardData.CardBg == selectId)
            {
                _rightButton?.SetNotificationMessage(TextId.ProfileCard0036.Text());
                _rightButton.interactable = false;
            }
            else
            {
                _rightButton?.SetNotificationMessage("");
                _rightButton.interactable = true;
            }
        }

        /// <summary>
        /// トグル選択時
        /// </summary>
        /// <param name="index"></param>
        private void OnClickButtonForCharacterBg( ProfileCardBgSet bgSet, int selectId)
        {
            _buttonFilter.SetActiveWithCheck(true);
            _buttonSort.SetActiveWithCheck(true);
 

            _cursorCommon?.SetParent(bgSet.gameObject);

            DialogProfileCardChangeCharacterBgDetail.Open(_currentSelectBgId, selectId, ( id )=>
            {
                // BGが選択されたため選択中のBGのIdを更新する.(この値で辞書からBGIDとSubIDを引っ張ってくる)
                _currentSelectBgId = id;
                RefreshList();
                
                _onDecide?.Invoke(_currentSelectBgId);
            });

        }

        private void RefreshList()
        {
            //絞り込みボタン文字入れ替え
            if (_currentFilter != 0)
            {
                _textFilter.text = TextId.Outgame0440.Text();
            }
            else
            {
                //TODO:nishi ボタン
                _textFilter.text = TextId.Outgame0441.Text();
            }
            _buttonFilter.image.sprite = UIManager.Instance.LoadAtlas(TargetAtlasType.PreIn).GetSprite(AtlasSpritePath.PreIn.GetFilterButtonSprite(_currentFilter != 0));
            _textFilter.FontColor = _currentFilter == 0 ? FontColorType.Brown : FontColorType.White;
            _textFilter.OnUpdate();
            
            //ソートアイコン設定
            if(_sortOrderAsend)
            {
                _textnSort.text = TextId.Common0100.Text();
                _iconSortStatus.transform.localScale = new Vector3(_iconSortStatus.transform.localScale.x, 1.0f, _iconSortStatus.transform.localScale.z);
            }
            else
            {
                _textnSort.text = TextId.Common0101.Text();
                _iconSortStatus.transform.localScale = new Vector3(_iconSortStatus.transform.localScale.x, -1.0f, _iconSortStatus.transform.localScale.z);
            }
            
            Dictionary<int, List<MasterNameCardBg.NameCardBg>> showDic = GetFilteredShowImageCategory();
            int SelecteBgId = MasterDataManager.Instance.masterNameCardBg.Get(_currentSelectBgId).BgId;

            // まず保持しているすべてのリストを削除する.
            if (_BgPoolList.Count() > 0)
            {
                for (int i = _BgPoolList.Count() - 1; i >= 0; --i)
                {
                    var item = _BgPoolList[i];
                    _BgPoolList.Remove(item);
                    item.transform.SetParent(null);
                    DestroyImmediate(item);
                }
            }

            // ソート.
            IOrderedEnumerable<KeyValuePair<int, List<MasterNameCardBg.NameCardBg>>> orderedDic;
            if (_sortOrderAsend == true)
            {
                orderedDic = showDic.OrderBy(selector => { return selector.Key; });
            }
            else
            {
                orderedDic = showDic.OrderByDescending(selector => { return selector.Key; });
            }

            foreach (var showItem in orderedDic)
            {
                int bgId = showItem.Value[0].BgId;
                int bgSubId = showItem.Value[0].BgSub;

                int CountNum = GetFilteredShowImage(bgId).Count;
                if (CountNum > 0)
                {
                    GameObject BGObj = GameObject.Instantiate(_bgButtonSetOrigin.gameObject);
                    ProfileCardBgSet bgSet = BGObj.GetComponent<ProfileCardBgSet>();
                    bgSet.transform.SetParent(_bgElementParent);
                    bgSet.GetComponent<RectTransform>().localScale = Gallop.Math.VECTOR3_ONE;
                    //背景初期化
                    bgSet.Init(showItem.Value[0].Id, bgId, bgSubId, (selectIndex) => { OnClickButtonForCharacterBg(bgSet, selectIndex); });
                    if (_currentLevel == BGSelectLevel.CATEGORY_CHOOSE)
                    {
                        bgSet.SetImageVariation(ProfileCardBgSet.ImageVariation.WITH_FRAME_NUMBER, CountNum);
                    }
                    else
                    {
                        bgSet.SetImageVariation(ProfileCardBgSet.ImageVariation.ONLY_IMAGE, 0);
                    }

                    //現在選択中のIndexに選択中カーソルを付ける
                    int showItemBgId = MasterDataManager.Instance.masterNameCardBg.Get(showItem.Value[0].Id).BgId;
                    if (SelecteBgId == showItemBgId)
                    {
                        //_cursorCommon.SetParent(bgSet.gameObject);
                        //設定中ラベルオン
                        bgSet.UpdateLabel(true);
                    }

                    bgSet.SetActiveWithCheck(true);
                    _BgPoolList.Add(BGObj);
                }
            }

            // contentsizeの拡張.
            RectTransform rect = _bgElementParent.GetComponent<RectTransform>();
            if(rect!= null)
            {
                float h = ONE_HEIGHT * (float)((showDic.Count / 2) + 1);
                rect.sizeDelta = new Vector2(rect.sizeDelta.x, h);
            }
            //ONE_HEIGHT * (float)((showDic.Count / 2) + 1);
        }
        
        /// <summary>
        /// 画像のカテゴリを選定
        /// </summary>
        /// <param name="currentLevel"></param>
        /// <param name="selectBgId"></param>
        /// <returns></returns>
        private Dictionary<int, List<MasterNameCardBg.NameCardBg>> GetFilteredShowImageCategory()
        {
            //BgIDをキーにした差分辞書を作成
            Dictionary<int, List<MasterNameCardBg.NameCardBg>> showDic = new Dictionary<int, List<MasterNameCardBg.NameCardBg>>();
            Dictionary<int, List<MasterNameCardBg.NameCardBg>> resultList = new Dictionary<int, List<MasterNameCardBg.NameCardBg>>();
            
            var masterProdileNameCarBg = MasterDataManager.Instance.masterNameCardBg;   // 背景用に用意しているデータ群

            // 第一階層は場所で検索をかける(BgIdでは見ないため時間などの重複はしない).
            foreach (var profileCardBg in masterProdileNameCarBg.dictionary)
            {
                if (!showDic.ContainsKey(profileCardBg.Value.BgId))
                {
                    showDic.Add(profileCardBg.Value.BgId, new List<MasterNameCardBg.NameCardBg>{ profileCardBg.Value });
                }
                else
                {
                    showDic[profileCardBg.Value.BgId].Add(profileCardBg.Value);
                }
            }

            //場所
            var resultListPlace = DialogFilterProfileBG.GetFilteredShowDicFromPlace(_currentFilter, showDic);
            //時間
            var resultListTime = DialogFilterProfileBG.GetFilteredShowDicFromTime(_currentFilter, resultListPlace);
            //その他
            resultList = DialogFilterProfileBG.GetFilteredShowDicFromOther(_currentFilter, resultListTime);

            return resultList;
        }

        /// <summary>
        /// 実際に表示する画像を選定
        /// </summary>
        /// <param name="currentFilter"></param>
        /// <param name="selectBgId"></param>
        /// <returns></returns>
        private Dictionary<int, List<MasterNameCardBg.NameCardBg>> GetFilteredShowImage(int selectBgId)
        {
            Dictionary<int, List<MasterNameCardBg.NameCardBg>> resultList = new Dictionary<int, List<MasterNameCardBg.NameCardBg>>();
            //指定された場所IDのみを拾ってきて、subIDで抽出をかける.
            var masterProdileNameCarBg = MasterDataManager.Instance.masterNameCardBg; ; // 指定したBGID（場所）の物だけを抽出

            // BGIDが同一のものを選定する.
            foreach (var profileCardBg in masterProdileNameCarBg.dictionary)
            {
                if (profileCardBg.Value.BgId == selectBgId)
                {
                    resultList.Add(profileCardBg.Value.Id, new List<MasterNameCardBg.NameCardBg>{ profileCardBg.Value });
                }
            }

            //差分を時間とイベントで検出
            //時間
            var resultListTime = DialogFilterProfileBG.GetFilteredShowDicFromTime(_currentFilter, resultList);
            //その他
            return DialogFilterProfileBG.GetFilteredShowDicFromOther(_currentFilter, resultListTime);
        }

        // FIlter設定をクリックされた場合.
        public void OnClickFilter()
        {
            DialogFilterProfileBG.Open(_currentFilter, (int filter) =>
            {
                _currentFilter = filter;
                SaveDataManager.Instance.SaveLoader.CharacterBgFilter = _currentFilter;
                SaveDataManager.Instance.Save();
                RefreshList();

                _buttonFilter.image.sprite = UIManager.Instance.LoadAtlas(TargetAtlasType.PreIn).GetSprite(AtlasSpritePath.PreIn.GetFilterButtonSprite(_currentFilter != 0));
                _textFilter.text = _currentFilter == 0 ? TextId.Outgame0316.Text() : TextId.Outgame0315.Text();
                _textFilter.FontColor = _currentFilter == 0 ? FontColorType.Brown : FontColorType.White;
                _textFilter.OnUpdate();

                if (_scrollCommon != null)
                {
                    //スクロールを一番上に
                    _scrollCommon.verticalNormalizedPosition = 1.0f;
                }
            });

        }

        // ソート設定をクリックされた場合.
        public void OnClickSort()
        {
            _sortOrderAsend = _sortOrderAsend == true ? false : true;
            SaveDataManager.Instance.SaveLoader.CharacterBgSortAsc = _sortOrderAsend;
            SaveDataManager.Instance.Save();
            RefreshList();
        }

        // Notificationを外部から設定.
        public void SetRightButtonNotification( ButtonCommon rightButton)
        {
            _rightButton = rightButton;
            _rightButton?.SetNotificationMessage(TextId.ProfileCard0036.Text());
            _rightButton.interactable = false;
        }

        #endregion
    }
}