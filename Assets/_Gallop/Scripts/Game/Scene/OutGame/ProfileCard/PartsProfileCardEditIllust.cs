using Gallop;
using System;
using Cute.Http;
using UnityEngine;
using System.Linq;
using System.Collections.Generic;
using CodeStage.AntiCheat.ObscuredTypes;
using static Gallop.StaticVariableDefine.BusinessCardScreenShot;
using static Gallop.StaticVariableDefine.Parts.BusinessCard;

namespace Gallop
{
    public class PartsProfileCardEditIllust : MonoBehaviour
    {
        /// <summary>
        /// プロフィールカードが右だった場合にProfileCard自体を左右移動させる差分値
        /// </summary>
        private const float PROFILE_RIGHT_OFFSET = -975.0f;

        /// <summary>
        /// 戻るボタンのdelay秒数
        /// </summary>
        public readonly float BACK_ANIMATION_DELAY = 0.0666f;

        //////////////////////////////////////////////////////////////////////////////////
        #region SerializeField
        //////////////////////////////////////////////////////////////////////////////////

        [SerializeField]
        private ProfileCardFront _profileCardIllust = null;

        // 左右切り替えたときの親
        [SerializeField]
        private Transform _rParent = null;
        [SerializeField]
        private Transform _lParent = null;

        [SerializeField]
        private ButtonCommon _changeCharacterButton = null;
        [SerializeField]
        private ButtonCommon _changeSupportCardButton = null;
        [SerializeField]
        private ButtonCommon _changePhotoButton = null;

        [SerializeField]
        private GameObject _charaSetting = null;
        [SerializeField]
        private GameObject _supportCardSetting = null;
        [SerializeField]
        private GameObject _photoSetting = null;

        [Header("育成ウマ娘画像設定画面")]
        [SerializeField]
        private PartsProfileCardEditCharacter _partsEditCharacter = null;

        [Header("サポカ画像設定画面")]
        [SerializeField]
        private PartsProfileCardEditSupportCard _partsEditSupportCard = null;

        [Header("フォト画像設定画面")]
        [SerializeField]
        private PartsProfileCardEditPhoto _partsEditPhoto = null;

        /// <summary> アニメーション制御用 </summary>
        [SerializeField]
        public ProfileCardViewAnimation ViewAnimation = null;

        #endregion //SerializeField

        #region 変数

        //コールバックのキャッシュ
        private Action<int, int, int> _decideCharacterCallback = null;
        private Action<int, int, int, int, int> _decideSupportCardCallback = null;
        private Action<string, int, int, int, int> _decidePhotoCallback = null;
        private bool _illustEdit = false;
        private Action<bool> _decideButtonSetting = null;
        #endregion

        //////////////////////////////////////////////////////////////////////////////////
#region Method
        //////////////////////////////////////////////////////////////////////////////////

        /// <summary>
        /// イラスト変更コンテンツ設定
        /// </summary>
        public void SetUpIllustContent(
            int currentCharacterId,
            int currentDressId,
            int currentBgId,

            int currentSelectSupportId,
            int currentPosX,
            int currentPosY,
            int currentRotate,
            int currentScale,

            string photo,

            Action<ProfileCardEdit.CurrentStep> nextStepCallback,

            Action<int, int, int> selectCharacterIllustCallback,
            Action<int, int, int, int, int> selectSupportIllustCallback,
            Action<string, int, int, int, int> selectPhotoIllustCallback,

            TempData.EditIllustEnumType editIllustEnumType,

            Action<bool> decideButtonSetting
        )
        {
            _illustEdit = false;
            //左右どっちか
            if (TempData.Instance.TrainerProfileCardData.LeftAlighned == false)
            {
                _profileCardIllust.gameObject.transform.SetParent(_lParent);
                RectTransform rec = _profileCardIllust.gameObject.GetComponent<RectTransform>();
                rec.anchoredPosition = new Vector2(PROFILE_RIGHT_OFFSET, rec.anchoredPosition.y);
            }
            else
            {
                _profileCardIllust.gameObject.transform.SetParent(_rParent);
                RectTransform rec = _profileCardIllust.gameObject.GetComponent<RectTransform>();
                rec.anchoredPosition = new Vector2(0f, rec.anchoredPosition.y);
            }

            //左側のカード更新
            _profileCardIllust.SetUpUIAll();
            //カラーのテクスチャ削除
            _profileCardIllust.SetUpCardBgForIllustEdit();

            _charaSetting.SetActive(false);
            _supportCardSetting.SetActive(false);
            _photoSetting.SetActive(false);

            _decideButtonSetting = decideButtonSetting;

            //設定中更新
            switch (editIllustEnumType)
            {
                case TempData.EditIllustEnumType.Character:
                    _charaSetting.SetActive(true);
                    break;
                case TempData.EditIllustEnumType.Support:
                    _supportCardSetting.SetActive(true);
                    break;
                case TempData.EditIllustEnumType.Photo:
                    _photoSetting.SetActive(true);
                    break;
            }

            //---------------- 各ボタンコールバック ----------------
            // 名刺イラスト各種変更ボタン
            _decideCharacterCallback = selectCharacterIllustCallback;
            _changeCharacterButton.SetOnClick(()=>
            {
                // シーンコンポーネントの初期化.
                _partsEditCharacter.InitChangeCharacterUI(
                    currentCharacterId,
                    currentDressId,
                    currentBgId,
                    DecideButtonForIllustEditFlag
                );
                nextStepCallback?.Invoke(ProfileCardEdit.CurrentStep.CharacterChange);
            });

            _decideSupportCardCallback = selectSupportIllustCallback;
            _changeSupportCardButton.SetOnClick(()=>
            {
                //他の編集値を引き継いでしまうのを防止
                if (editIllustEnumType != TempData.EditIllustEnumType.Support)
                {
                    currentPosX = TempData.ProfileCardTempData.DEFAULT_POS;
                    currentPosY = TempData.ProfileCardTempData.DEFAULT_POS;
                    currentRotate = TempData.ProfileCardTempData.DEFAULT_ROTATE;
                    currentScale = TempData.ProfileCardTempData.DEFAULT_SCALE;
                }

                // サポカEdit時の初期化
                _partsEditSupportCard.SupportCardSettingInit(
                    currentSelectSupportId,
                    currentPosX,
                    currentPosY,
                    currentRotate,
                    currentScale,
                    DecideButtonForIllustEditFlag
                );

                nextStepCallback?.Invoke(ProfileCardEdit.CurrentStep.SupportCardChange);
            });

            int photoNum = BuisinessCardScreenShot.Instance.GetAllThumbnailList().Length;
            if (photoNum > 0)
            {
                _decidePhotoCallback = selectPhotoIllustCallback;
                _changePhotoButton.SetButtonInteractableWithColor(true);
                _changePhotoButton.SetNotificationMessage(String.Empty);
                _changePhotoButton.SetOnClick(() =>
                {
                    if (editIllustEnumType != TempData.EditIllustEnumType.Photo)
                    {
                        currentPosX = TempData.ProfileCardTempData.DEFAULT_POS;
                        currentPosY = TempData.ProfileCardTempData.DEFAULT_POS;
                        currentRotate = TempData.ProfileCardTempData.DEFAULT_ROTATE;
                        currentScale = TempData.ProfileCardTempData.DEFAULT_SCALE;
                    }

                    // フォトEdit時の初期化
                    _partsEditPhoto.PhotoSettingInit(
                        photo,
                        currentPosX,
                        currentPosY,
                        currentRotate,
                        currentScale,
                        DecideButtonForIllustEditFlag
                    );

                    nextStepCallback?.Invoke(ProfileCardEdit.CurrentStep.PhotoChange);
                });
            }
            else
            {
                _changePhotoButton.SetButtonInteractableWithColor(false);
                _changePhotoButton.SetNotificationMessage(TextId.ProfileCard0053.Text());
            }

            // イラスト設定コンテンツまで戻った場合には絶対にマルチタップは必要ないので保証しておく.
            Input.multiTouchEnabled = false;
        }

        /// <summary>
        /// Editページ非表示
        /// </summary>
        public void HideEditPage()
        {
            _partsEditCharacter.gameObject.SetActiveWithCheck(false);
            _partsEditSupportCard.gameObject.SetActiveWithCheck(false);
            _partsEditPhoto.gameObject.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 名刺イラスト変更を押したとき
        /// </summary>
        public void OnClickChangeIllust(ProfileCardEdit.CurrentStep currentStep)
        {
            switch(currentStep)
            {
                case ProfileCardEdit.CurrentStep.CharacterChange:
                    _partsEditCharacter.gameObject.SetActiveWithCheck(true);
                    _partsEditSupportCard.gameObject.SetActiveWithCheck(false);
                    _partsEditPhoto.gameObject.SetActiveWithCheck(false);
                    break;
                case ProfileCardEdit.CurrentStep.SupportCardChange:
                    _partsEditCharacter.gameObject.SetActiveWithCheck(false);
                    _partsEditSupportCard.gameObject.SetActiveWithCheck(true);
                    _partsEditPhoto.gameObject.SetActiveWithCheck(false);
                    break;
                case ProfileCardEdit.CurrentStep.PhotoChange:
                    _partsEditCharacter.gameObject.SetActiveWithCheck(false);
                    _partsEditSupportCard.gameObject.SetActiveWithCheck(false);
                    _partsEditPhoto.gameObject.SetActiveWithCheck(true);
                    break;
            }
        }

        /// <summary>
        /// イラスト編集のフラグ確認
        /// </summary>
        /// <returns></returns>
        public bool CheckIllustEditFlagCheck(ProfileCardEdit.CurrentStep currentStep)
        {
            switch (currentStep)
            {
                case ProfileCardEdit.CurrentStep.CharacterChange:
                    return _partsEditCharacter.CheckEditFlag();
                case ProfileCardEdit.CurrentStep.SupportCardChange:
                    return _partsEditSupportCard.CheckEditFlag();
                case ProfileCardEdit.CurrentStep.PhotoChange:
                    return _partsEditPhoto.CheckEditFlag();
            }
            return false;
        }

        /// <summary>
        /// イラスト編集の文言フラグ確認
        /// </summary>
        /// <returns></returns>
        public bool CheckIsSelectOthersTextFlagCheck(ProfileCardEdit.CurrentStep currentStep)
        {
            switch (currentStep)
            {
                case ProfileCardEdit.CurrentStep.SupportCardChange:
                    return _partsEditSupportCard.CheckIsOtherSelectFlag();
                case ProfileCardEdit.CurrentStep.PhotoChange:
                    return _partsEditPhoto.CheckIsOtherSelectFlag();
            }
            return true;
        }

        /// <summary>
        /// 編集記録
        /// </summary>
        private void DecideButtonForIllustEditFlag(ProfileCardEdit.CurrentStep currentStep)
        {
            _illustEdit = CheckIllustEditFlagCheck(currentStep);

            //ボタン設定
            if (_illustEdit)
            {
                _decideButtonSetting?.Invoke(true);
            }
            else
            {
                _decideButtonSetting?.Invoke(false);
            }
        }

        /// <summary>
        /// 各種INアニメーション分岐
        /// </summary>
        /// <param name="currentStep"></param>
        /// <param name="onCallback"></param>
        public void PlayInSkinAnimation(ProfileCardEdit.CurrentStep currentStep)
        {
            switch (currentStep)
            {
                case ProfileCardEdit.CurrentStep.CharacterChange:
                    _partsEditCharacter.ViewAnimation.PlaySkinEditInAnimation();
                    break;
                case ProfileCardEdit.CurrentStep.SupportCardChange:
                    _partsEditSupportCard.ViewAnimation.PlaySkinEditInAnimation();
                    break;
                case ProfileCardEdit.CurrentStep.PhotoChange:
                    _partsEditPhoto.ViewAnimation.PlaySkinEditInAnimation(() =>
                    {
                        _partsEditPhoto.SetCanvasGroupAlpha();
                    });
                    break;
            }
        }

        /// <summary>
        /// 各種アウトアニメーション分岐
        /// </summary>
        /// <param name="currentStep"></param>
        /// <param name="onCallback"></param>
        public void PlayOutSkinAnimation(ProfileCardEdit.CurrentStep currentStep, Action onCallback)
        {
            switch (currentStep)
            {
                case ProfileCardEdit.CurrentStep.CharacterChange:
                    _partsEditCharacter.ViewAnimation.PlaySkinEditOutAnimation(false, onCallback);
                    break;
                case ProfileCardEdit.CurrentStep.SupportCardChange:
                    _partsEditSupportCard.ViewAnimation.PlaySkinEditOutAnimation(false, onCallback, _partsEditSupportCard.DestroyList);
                    break;
                case ProfileCardEdit.CurrentStep.PhotoChange:
                    _partsEditPhoto.ViewAnimation.PlaySkinEditOutAnimation(true, ()=>
                    {
                        onCallback?.Invoke();
                    });
                    break;
            }
        }

        /// <summary>
        /// キャラクターの決定ボタンが押された時
        /// </summary>
        public void OnClickDecideCharacterButton()
        {
            _illustEdit = CheckIllustEditFlagCheck(ProfileCardEdit.CurrentStep.CharacterChange);
            if (!_illustEdit)
            {
                return;
            }
            //名刺更新
            _profileCardIllust.SetUpCharacter( _partsEditCharacter.CurrentCharacterId, _partsEditCharacter.CurrentDressId, false);
            _profileCardIllust.SetUpCharacterBg(_partsEditCharacter.CurrentBgId);

            //TempData更新
            _decideCharacterCallback?.Invoke(_partsEditCharacter.CurrentCharacterId, _partsEditCharacter.CurrentDressId, _partsEditCharacter.CurrentBgId);

            HideEditPage();
        }

        /// <summary>
        /// サポカの決定ボタンが押された時
        /// </summary>
        public void OnClickDecideSupportCardButton()
        {
            _illustEdit = CheckIllustEditFlagCheck(ProfileCardEdit.CurrentStep.SupportCardChange);
            if (!_illustEdit)
            {
                return;
            }

            //名刺更新
            _profileCardIllust.SetUpSupportCard(_partsEditSupportCard.SelectCurrentSupportCardId,
                _partsEditSupportCard.CurrentPosX / MULTIPLY_FLOAT,
                _partsEditSupportCard.CurrentPosY / MULTIPLY_FLOAT,
                _partsEditSupportCard.CurrentRotate / MULTIPLY_FLOAT,
                _partsEditSupportCard.CurrentScale / MULTIPLY_FLOAT,
                false);

            //TempData更新
            _decideSupportCardCallback?.Invoke(_partsEditSupportCard.SelectCurrentSupportCardId, _partsEditSupportCard.CurrentPosX, _partsEditSupportCard.CurrentPosY, _partsEditSupportCard.CurrentRotate, _partsEditSupportCard.CurrentScale);

            HideEditPage();
        }

        /// <summary>
        /// フォトの決定ボタンが押された時
        /// </summary>
        public void OnClickDecidePhotoButton()
        {
            _illustEdit = CheckIllustEditFlagCheck(ProfileCardEdit.CurrentStep.PhotoChange);
            if (!_illustEdit)
            {
                return;
            }
#if DMM || UNITY_EDITOR
            // 読み込み済みのファイルのハッシュを持てばいいかと思ったが、このケースだと
            // ロードと確認のタイミングが大きくずれるため、その間に改変を加えられるとアウトになるため
            // この瞬間にロードして確認するしかないことに気が付いた.
            string Hash = "";
            string imagepath = BuisinessCardScreenShot.GetOrCreateInstance().GetScreenShotPath() + "/" + System.IO.Path.GetFileName(_partsEditPhoto.CurrentPhoto);
            string truePath = imagepath.Replace(THUMBNAIL_SURFIX, "");
            TextureSaveLoaderUtil.SyncLoadImageFile(truePath, out Hash);
            if (!string.IsNullOrEmpty(Hash))
            {
                // ここで読み込まれているフォトの正当性を確認しておく
                if (!BuisinessCardScreenShot.IsAvalableScreenshot(Hash))
                {
                    ProfileCardUtil.OpenPhotoBreakDialog();
                    return;
                }
            }
#endif

            //名刺更新
            _profileCardIllust.SetUpPhoto(_partsEditPhoto.CurrentPhoto,
                _partsEditPhoto.CurrentPosX / MULTIPLY_FLOAT,
                _partsEditPhoto.CurrentPosY / MULTIPLY_FLOAT,
                _partsEditPhoto.CurrentRotate / MULTIPLY_FLOAT,
                _partsEditPhoto.CurrentScale / MULTIPLY_FLOAT);

            _decidePhotoCallback?.Invoke(_partsEditPhoto.CurrentPhoto, _partsEditPhoto.CurrentPosX, _partsEditPhoto.CurrentPosY, _partsEditPhoto.CurrentRotate, _partsEditPhoto.CurrentScale);

            HideEditPage();
        }

#endregion
    }
}