using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using static Gallop.StaticVariableDefine.OutGame.PartsVerticalGauge;

namespace Gallop
{
    /// <summary>
    /// トレーニングレポートのスクロールにのせるprefab
    /// </summary>
    public class PartsTrainingReportPickupReward : LoopScrollItemBase
    {
        #region SerializeField

        /// <summary> 無償報酬情報 </summary>
        [SerializeField]
        private TrainingReportRewardItem _trainingReportRewardItemNormal;

        /// <summary> 有償報酬情報 </summary>
        [SerializeField]
        private TrainingReportRewardItem _trainingReportRewardItemPaid;

        /// <summary>
        /// 獲得条件となるポイントテキスト
        /// </summary>
        [SerializeField]
        private TextCommon _conditionPointText = null;

        /// <summary>
        /// 通常のBG背景ルート
        /// </summary>
        [SerializeField]
        private GameObject _defaultBgRoot = null;

        /// <summary>
        /// 最底辺のBG背景ルート
        /// </summary>
        [SerializeField]
        private GameObject _bottomBgRoot = null;

        /// <summary>
        /// 罫線画像
        /// </summary>
        [SerializeField]
        private GameObject _borderImage = null;

        #endregion

        #region Method

        /// <summary>
        /// ピースのセットアップ
        /// </summary>
        /// <param name="rewardDataDic">表示用の報酬データ</param>
        /// <param name="isBottom">スクロールビューの最後の要素かどうか</param>
        public void Setup(Dictionary<WorkTrainingReportData.RewardType, WorkTrainingReportData.RewardData> rewardDataDic, bool isBottom)
        {
            if (rewardDataDic.TryGetValue(WorkTrainingReportData.RewardType.Normal, out var rewardDataNormal) && rewardDataNormal.IsPickup)
            {
                _trainingReportRewardItemNormal.SetActiveWithCheck(true);
                _trainingReportRewardItemNormal.Setup(rewardDataNormal);
            }
            else
            {
                _trainingReportRewardItemNormal.SetActiveWithCheck(false);
            }

            if (rewardDataDic.TryGetValue(WorkTrainingReportData.RewardType.Paid, out var rewardDataPaid) && rewardDataPaid.IsPickup)
            {
                _trainingReportRewardItemPaid.SetActiveWithCheck(true);
                _trainingReportRewardItemPaid.Setup(rewardDataPaid);
            }
            else
            {
                _trainingReportRewardItemPaid.SetActiveWithCheck(false);
            }

            // ポイント
            var conditionPoint = WorkDataManager.Instance.TrainingReportData.GetConditionPoint(rewardDataDic);
            _conditionPointText.text = TextUtil.Format(
                TextId.Friend0059.Text(),
                conditionPoint.ToString(TextUtil.CommaSeparatedFormat));

            // スクロールビューの最後の要素（最底辺の要素）であれば背景画像を差し替えて罫線を削除
            _bottomBgRoot.SetActiveWithCheck(isBottom);
            _defaultBgRoot.SetActiveWithCheck(!isBottom);
            _borderImage.SetActiveWithCheck(!isBottom);
        }

        #endregion
    }
}
