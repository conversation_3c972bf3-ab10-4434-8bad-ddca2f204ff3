using System.Collections;
using System.Linq;
using UnityEngine;
using DG.Tweening;

namespace Gallop
{
    using AnimSequenceHelper;

    [AddComponentMenu("")]
    public sealed class TrainingReportView : ViewBase
    {
        [SerializeField]
        private LoopScroll _scroll = null;
        public LoopScroll Scroll => _scroll;
        [SerializeField] 
        private ScrollRectCommon _scrollRect;
        public ScrollRectCommon ScrollRect => _scrollRect;

        /// <summary> ノート部分背景 </summary>
        [SerializeField]
        private RawImageCommon _noteBg = null;
        public RawImageCommon NoteBg => _noteBg;

        /// <summary> 有償パス購入ボタン </summary>
        [SerializeField]
        private ButtonCommon _buyPaidPassButton = null;
        public ButtonCommon BuyPaidPassButton => _buyPaidPassButton;

        /// <summary> トレーニングレポート詳細ボタン </summary>
        [SerializeField]
        private ButtonCommon _trainingReportInfoButton = null;
        public ButtonCommon TrainingReportInfoButton => _trainingReportInfoButton;

        /// <summary> 総獲得ポイント表示ルート </summary>
        [SerializeField]
        private GameObject _trainingPointRoot = null;
        public GameObject TrainingPointRoot => _trainingPointRoot;

        /// <summary> シーズン期間 </summary>
        [SerializeField]
        private TextCommon _termText = null;
        public TextCommon TermText => _termText;

        /// <summary> ピックアップ表示ルート </summary>
        [SerializeField]
        private GameObject _pickupRoot = null;
        public GameObject PickupRoot => _pickupRoot;
        /// <summary> ピックアップ表示ルート(単体) </summary>
        [SerializeField]
        private GameObject _pickupSingleRoot = null;
        public GameObject PickupSingleRoot => _pickupSingleRoot;
        /// <summary> ピックアップ表示ルート(複数) </summary>
        [SerializeField]
        private GameObject _pickupMultiRoot = null;
        public GameObject PickupMultiRoot => _pickupMultiRoot;

        /// <summary> 無償ピックアップ報酬アイコン </summary>
        [SerializeField]
        private TrainingReportRewardItem _normalPickupReward  = null;
        public TrainingReportRewardItem NormalPickupReward => _normalPickupReward;

        /// <summary> 有償ピックアップ報酬アイコン </summary>
        [SerializeField]
        private TrainingReportRewardItem _paidPickupReward  = null;
        public TrainingReportRewardItem PaidPickupReward => _paidPickupReward;

        /// <summary> 有償ピックアップ報酬の必要ポイント </summary>
        [SerializeField]
        private TextCommon _pickupPointText = null;
        public TextCommon PickupPointText => _pickupPointText;

        /// <summary> 無償ピックアップ報酬アイコン(複数) </summary>
        [SerializeField]
        private TrainingReportRewardItem _normalPickupRewardMulti  = null;
        public TrainingReportRewardItem NormalPickupRewardMulti => _normalPickupRewardMulti;

        /// <summary> 有償ピックアップ報酬アイコン(複数) </summary>
        [SerializeField]
        private TrainingReportRewardItem _paidPickupRewardMulti  = null;
        public TrainingReportRewardItem PaidPickupRewardMulti => _paidPickupRewardMulti;

        /// <summary> 有償ピックアップ報酬の必要ポイント(通常/複数) </summary>
        [SerializeField]
        private TextCommon _pickupPointTextMultiNormal = null;
        public TextCommon PickupPointTextMultiNormal => _pickupPointTextMultiNormal;

        /// <summary> 有償ピックアップ報酬の必要ポイント(有償/複数) </summary>
        [SerializeField]
        private TextCommon _pickupPointTextMultiPaid = null;
        public TextCommon PickupPointTextMultiPaid => _pickupPointTextMultiPaid;

        /// <summary> ピックアップ報酬確認ボタン </summary>
        [SerializeField]
        private ButtonCommon _pickupRewardButtonSingle = null;
        public ButtonCommon PickupRewardButtonSingle => _pickupRewardButtonSingle;

        /// <summary> ピックアップ報酬確認ボタン(複数) </summary>
        [SerializeField]
        private ButtonCommon _pickupRewardButtonMulti = null;
        public ButtonCommon PickupRewardButtonMulti => _pickupRewardButtonMulti;

        /// <summary> 総獲得ポイントのアンダーバー画像（桁数に応じた出し分け用） </summary>
        [SerializeField]
        private ImageCommon[] _trainingPointUnderBarArray = null;
        public ImageCommon[] TrainingPointUnderBarArray => _trainingPointUnderBarArray;

        /// <summary> トレーニングレポート説明テキスト画像 </summary>
        [SerializeField]
        private ImageCommon _explainTextImage= null;
        public ImageCommon ExplainTextImage => _explainTextImage;
        /// <summary> トレーニングレポート完走テキスト画像 </summary>
        [SerializeField]
        private ImageCommon _completedTextImage= null;
        public ImageCommon CompletedTextImage => _completedTextImage;
    }

    public sealed class TrainingReportViewController : ViewControllerBase<TrainingReportView>
    {
        private int _seasonId = 0;
        private bool _preHasPaidPass = false;

        /// <summary> トップ画面に表示する最大のトレーニングレポート総獲得ポイント </summary>
        private const int MAX_DISPLAY_TRAINING_REPORT_POINT = 99999999;

        /// <summary>
        /// ダウンロード対象リソースの登録前処理
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PreRegisterDownload()
        {
            // トレーニングレポート情報取得
            bool isCompleteReqApi = false;
            var trainingReportReq = new TrainingReportIndexRequest();
            trainingReportReq.Send((res) =>
            {
                isCompleteReqApi = true;
                WorkDataManager.Instance.TrainingReportData.Update(res);
                _seasonId = WorkDataManager.Instance.TrainingReportData.SeasonId;
            });

            yield return new WaitUntil(() => isCompleteReqApi);

            yield return base.PreRegisterDownload();
        }

        /// <inheritdoc />
        /// <summary>
        /// ダウンロード対象リソースの登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            DialogTrainingReportReward.RegisterDownload(register);
            BitmapTextCommon.RegistDownload(register, TextFormat.BitmapFont.UtxTextTeamscore);
            register.RegisterPathWithoutInfo(ResourcePath.TRAINING_REPORT_POINT_TAT_PATH);

            // 背景のノート素材
            var masterTrainingReportPass = MasterDataManager.Instance.masterTrainingReportPass.GetWithSeasonId(_seasonId);
            register.RegisterPathWithoutInfo(ResourcePath.TRAINING_REPORT_NOTE_BG_ROOT_PATH + masterTrainingReportPass.BgImage);
        }

        /// <summary>
        /// InitializeView
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            _view.BuyPaidPassButton.SetOnClick(OnClickBuyPaidButton);
            _view.TrainingReportInfoButton.SetOnClick(() =>
            {
                DialogTrainingReportInformation.PushDialog();
            });

            // プレミアムパス購入ボタンは常時表示
            _view.BuyPaidPassButton.SetActiveWithCheck(true);

            yield return base.InitializeView();
        }

        public override IEnumerator InitializeEachPlayIn()
        {
            // 今シーズンの総獲得ポイント
            SetupTrainingPoint(WorkDataManager.Instance.TrainingReportData.Point);

            // 開催期間
            var masterTrainingReportPass = MasterDataManager.Instance.masterTrainingReportPass.GetWithSeasonId(_seasonId);
            var startDateTime = TimeUtil.ToLocalDateTimeFromJstString(masterTrainingReportPass.StartDate);
            var endDateTime = TimeUtil.ToLocalDateTimeFromJstString(masterTrainingReportPass.EndDate);

            _view.TermText.text = TextUtil.Format(
                TextId.Outgame608011.Text(),
                TimeUtil.ToDispStringFromMonth(startDateTime),
                TimeUtil.ToDispStringFromMonth(endDateTime));

            // 完走しているかどうかで説明テキストと完走テキストを出し分ける
            var isCompleted = WorkDataManager.Instance.TrainingReportData.IsCompleted;
            _view.ExplainTextImage.SetActiveWithCheck(!isCompleted);
            _view.CompletedTextImage.SetActiveWithCheck(isCompleted);

            // ノート部分の背景設定（サイズは統一するので画像に合わせたサイズ変更は不要）
            _view.NoteBg.texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.TRAINING_REPORT_NOTE_BG_ROOT_PATH + masterTrainingReportPass.BgImage);

            // ピックアップ報酬
            SetupPicupReward();

            // スクロールビュー設定
            SetupScroll();

            yield return base.InitializeEachPlayIn();
        }

        /// <summary>
        /// 画面In再生
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PlayInView()
        {
            yield return PlayInAnimation();

            // Tipsを表示する
            DialogTutorialGuide.PushDialogWithReadCheck(DialogTutorialGuide.TutorialGuideId.TrainingReport, () =>
            {
                // シーズンが切り替わった場合はダイアログで通知を行う
                if (WorkDataManager.Instance.TrainingReportData.SeasonChangeFlag)
                {
                    var data = new DialogCommon.Data();
                    data.SetSimpleOneButtonMessage(
                        headerTextArg: TextId.Outgame608029.Text(),
                        message: TextId.Outgame608030.Text(),
                        onClickCenterButton: (dialog) => dialog.Close(ShowRewardReceiveDialog)
                    );
                    DialogManager.PushDialog(data);
                }
                else
                {
                    // 獲得した報酬がある場合は演出を流す
                    ShowRewardReceiveDialog();
                }
            } );

            yield return base.PlayInView();
        }

        /// <summary>
        /// 画面Out再生
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PlayOutView()
        {
            yield return PlayOutAnimation();

            yield return base.PlayOutView();
        }

        /// <summary>
        /// Viewの終了処理
        /// </summary>
        /// <returns></returns>
        public override IEnumerator FinalizeView()
        {
            // 別シーン遷移時はBGMを停止する
            if (SceneManager.Instance.GetNextViewId() != SceneDefine.ViewId.TrainingReport)
            {
                AudioManager.Instance.StopBgm(0.0f);
            }

            yield return base.FinalizeView();
        }

        /// <summary>
        /// 画面In各要素のアニメーション再生
        /// </summary>
        /// <returns></returns>
        private IEnumerator PlayInAnimation()
        {
            // ピックアップ表示領域は透明にしておく
            var canvasGroup = _view.PickupRoot.GetComponent<CanvasGroup>();
            if (!canvasGroup.IsNull()) canvasGroup.alpha = 0.0f;

            var sequence = DOTween.Sequence();
            var delay = UIUtil.VIEW_IN_PARTS_INTERVAL;

            // 課金パス購入ボタン
            sequence.Join(_view.BuyPaidPassButton, TweenAnimation.PresetType.PartsInMoveAndFade, delay);
            // ピックアップ表示領域
            sequence.Join(_view.PickupRoot, TweenAnimation.PresetType.PartsInMoveAndFade, delay);

            delay += UIUtil.VIEW_IN_PARTS_INTERVAL;

            // 戻るボタン
            sequence.InsertCallback(0.0f, () => UIManager.Instance.OpenFooterBackButton(delay));

            yield return sequence.WaitForCompletion();
        }

        /// <summary>
        /// 画面Out各要素のアニメーション再生
        /// </summary>
        /// <returns></returns>
        private IEnumerator PlayOutAnimation()
        {
            var sequence = DOTween.Sequence();

            // 課金パス購入ボタン
            sequence.Join(_view.BuyPaidPassButton, TweenAnimation.PresetType.PartsOutMoveAndFade);
            // ピックアップ表示領域
            sequence.Join(_view.PickupRoot, TweenAnimation.PresetType.PartsOutMoveAndFade);

            yield return sequence.WaitForCompletion();
        }

        /// <summary>
        /// トレーニングレポート報酬獲得があれば演出を表示する
        /// </summary>
        private void ShowRewardReceiveDialog()
        {
            // 獲得した報酬がある場合は演出を流す
            if (WorkDataManager.Instance.TrainingReportData.ReceivedRewardInfoList.Any())
            {
                DialogTrainingReportReward.Open();
            }
        }

        /// <summary>
        /// トレーニングレポートの総獲得ポイントの表示セットアップ
        /// </summary>
        private void SetupTrainingPoint(int point)
        {
            // 表示上は8桁までで切る
            point = System.Math.Min(point, MAX_DISPLAY_TRAINING_REPORT_POINT);
            // 総獲得ポイント表示用のTATをロード
            var trainingReportPoint = TATTrainingReportPoint.Create(_view.TrainingPointRoot.transform);
            // 総獲得ポイント表示
            if (trainingReportPoint)
            {
                trainingReportPoint.SetTrainingPoint(point);
            }

            // 総獲得ポイントの桁数に応じて数値の下に表示するアンダーバーの長さを変えるため画像を出し分け
            var digit = Digit(point);
            var underBarArrayCount = _view.TrainingPointUnderBarArray.Length;
            // 2桁増える度に表示する画像を切り替える
            var visibleIndex =  System.Math.Min((digit - 1) / 2, underBarArrayCount - 1);
            for (var i = 0; i < underBarArrayCount; i++)
            {
                _view.TrainingPointUnderBarArray[i].SetActiveWithCheck(i == visibleIndex);
            }
        }

        /// <summary>
        /// ピックアップ報酬表示セットアップ
        /// </summary>
        private void SetupPicupReward()
        {
            // ピックアップとして表示する報酬情報を取得
            var ( normalRewardData, paidRewardData) =
                WorkDataManager.Instance.TrainingReportData.GetPickupRewardData();

            var normalConditionPoint = normalRewardData.IsNull() ? 0 : normalRewardData.ConditionPoint;
            var paidConditionPoint = paidRewardData.IsNull() ? 0 : paidRewardData.ConditionPoint;

            // ピックアップ報酬の表示を複数版にする必要があるかどうか(獲得条件となるポイントが異なる場合は複数版で表示)
            bool isShowPickupMulti = normalConditionPoint != paidConditionPoint;

            var normalRewardIcon = isShowPickupMulti ? _view.NormalPickupRewardMulti : _view.NormalPickupReward;
            var paidRewardIcon =  isShowPickupMulti ? _view.PaidPickupRewardMulti :_view.PaidPickupReward;
            var pickupRewardButton =  isShowPickupMulti ? _view.PickupRewardButtonMulti :_view.PickupRewardButtonSingle;
            if (normalRewardData.IsNull())
            {
                normalRewardIcon.SetActiveWithCheck(false);
            }
            else
            {
                // 無償ピックアップ報酬アイコン設定
                normalRewardIcon.SetActiveWithCheck(true);
                normalRewardIcon.Setup(normalRewardData);
            }

            if (paidRewardData.IsNull())
            {
                paidRewardIcon.SetActiveWithCheck(false);
            }
            else
            {
                // 有償ピックアップ報酬アイコン設定
                paidRewardIcon.SetActiveWithCheck(true);
                paidRewardIcon.Setup(paidRewardData);
            }

            pickupRewardButton.SetOnClick(() =>
            {
                DialogTrainingReportPickupReward.PushDialog();
            });

            // 複数版表示によって出し分け
            _view.PickupSingleRoot.SetActiveWithCheck(!isShowPickupMulti);
            _view.PickupMultiRoot.SetActiveWithCheck(isShowPickupMulti);

            if (isShowPickupMulti)
            {
                _view.PickupSingleRoot.SetActiveWithCheck(false);
                // ポイント設定
                _view.PickupPointTextMultiNormal.text = TextUtil.Format(
                    TextId.Friend0059.Text(),
                    normalConditionPoint.ToString(TextUtil.CommaSeparatedFormat));
                _view.PickupPointTextMultiPaid.text = TextUtil.Format(
                    TextId.Friend0059.Text(),
                    paidConditionPoint.ToString(TextUtil.CommaSeparatedFormat));
            }
            else
            {
                _view.PickupMultiRoot.SetActiveWithCheck(false);
                // ポイント設定
                _view.PickupPointText.text = TextUtil.Format(
                    TextId.Friend0059.Text(),
                    normalConditionPoint.ToString(TextUtil.CommaSeparatedFormat));
            }
        }

        /// <summary>
        /// スクロールビューのセットアップ
        /// </summary>
        private void SetupScroll()
        {
            var workItemNum = WorkDataManager.Instance.TrainingReportData.DisplayRewardList.Count;
            _view.Scroll.Setup(workItemNum, OnItemUpdate);

            // スクロール位置調整
            SetupScrollPosition(WorkDataManager.Instance.TrainingReportData.NextRewardIndex, workItemNum);
        }

        /// <summary>
        /// スクロール位置のセットアップ
        /// </summary>
        public void SetupScrollPosition(int targetIndex, int maxIndex)
        {
            // 全報酬を獲得できるポイントに到達していたら無条件に一番下までスクロールさせる
            if (WorkDataManager.Instance.TrainingReportData.IsCompleted)
            {
                targetIndex = maxIndex - 1;
            }

            // スクロール位置調整
            _view.ScrollRect.SetScrollPositionEasy(maxIndex, targetIndex);
        }

        /// <summary>
        /// スクロールビューに載せる要素の表示更新
        /// </summary>
        private void OnItemUpdate(LoopScrollItemBase itemBase)
        {
            var item = itemBase as PartsTrainingReportRewardInfo;
            var index = item.ItemIndex;
            var itemInfoList = WorkDataManager.Instance.TrainingReportData.DisplayRewardList;

            if (index < itemInfoList.Count)
            {
                item.gameObject.SetActive(true);
                var itemInfo = itemInfoList[index];
                item.Setup(rewardDataDic: itemInfo, index);
            }
            else
            {
                item.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// 戻るボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {
            if (!SceneManager.Instance.BackUsingStack())
            {
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub);
            }
        }

        /// <summary>
        /// OSのバックキー押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            // 戻るボタンと同じ挙動
            OnClickBackButton();
        }

        /// <summary>
        /// 画面右下の有償パス購入ボタン押下
        /// </summary>
        private void OnClickBuyPaidButton()
        {
            // 購入前に現状の状態を覚えておく
            _preHasPaidPass = WorkDataManager.Instance.TrainingReportData.HasPaidPass;
            // 購入ダイアログを表示（トレーニングパス画面から開いた際は初期スクロール位置を有償パスの位置にする）
            PaymentUtility.Instance.OpenBuyJewelDialog(
                finish: null,
                onClose: ClosePaymentDialog,
                autoScrollIdList: MasterDataManager.Instance.masterTrainingReportPass.ProductMasterIdList);
        }

        /// <summary>
        /// 購入ダイアログを閉じた際のコールバック
        /// </summary>
        private void ClosePaymentDialog()
        {
            // 有償パスの所持状況が変わったら表示内容を更新するため画面を再度開く
            if (_preHasPaidPass != WorkDataManager.Instance.TrainingReportData.HasPaidPass)
            {
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.TrainingReport, forceChange: true);
            }
        }

        /// <summary>
        /// 指定した数値の桁数取得
        /// </summary>
        private static int Digit(int num)
        {
            return (num == 0) ? 1 : ((int)Mathf.Log10(num) + 1);
        }
    }
}
