using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 育成画面のオプションダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogOptionSingleMode : DialogInnerBase
    {
        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        /// <summary>
        /// ページ
        /// </summary>
        [SerializeField] private PartsOptionPageSingleMode _optionPageSingleMode;

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open()
        {
            var DialogOptionSingleMode = LoadAndInstantiatePrefab<DialogOptionSingleMode>(ResourcePath.DIALOG_OPTION_SINGLE_MODE);
            DialogOptionSingleMode.Setup();

            ContentOptionSound.AddCueSheet();

            var data = DialogOptionSingleMode.CreateDialogData();
            data.AutoClose = false;
            data.Title = TextId.Menu0009.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = DialogOptionSingleMode.OnClickCancel;
            data.RightButtonText = TextId.Common0261.Text();
            data.RightButtonCallBack = DialogOptionSingleMode.OnClickSave;
            data.DispStackType =  DialogCommon.DispStackType.DialogOnDialog;
            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        private void Setup()
        {
            _optionPageSingleMode.Setup(GameDefine.OptionDialogType.SingleMode);
        }

        /// <summary>
        /// 音量プレビューで使用するCueSheetのDLを登録する
        /// </summary>
        public static void RegisterDownloadPreviewSoundCueSheets(DownloadPathRegister register)
        {
            ContentOptionSound.RegisterDownloadPreviewSoundCueSheets(register);
        }

        /// <summary>
        /// 保存ボタンが押された時の処理
        /// </summary>
        /// <param name="dialog"></param>
        private void OnClickSave(DialogCommon dialog)
        {
            _optionPageSingleMode.OnClickSave();

            SaveDataManager.Instance.SaveLoader.Save();
            
            // 各オプションから保存する項目を登録
            var optionInfo = new List<OptionInfo>();
            _optionPageSingleMode.RegisterOptionInfo(ref optionInfo);

            WorkOptionData.SendChangeOptionRequestApi(optionInfo, OpenSavedDialog);

            void OpenSavedDialog()
            {
                // 「設定を変更しました」ダイアログを開く
                DialogOptionHome.OpenSavedMessageDialog(
                    onDestroy: () =>
                    {
                        // 閉じたらこのオプションダイアログも閉じる
                        dialog.Close();
                    }
                );
            }
        }

        /// <summary>
        /// キャンセルボタンが押された時の処理
        /// </summary>
        /// <param name="dialog"></param>
        private void OnClickCancel(DialogCommon dialog)
        {
            _optionPageSingleMode.OnClickCancel();

            dialog.Close();
        }
    }
}
