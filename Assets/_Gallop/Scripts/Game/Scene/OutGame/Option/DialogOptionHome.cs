using System.Collections;
using Cute.Core;
using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// ホーム画面のオプションダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogOptionHome : DialogInnerBase
    {
        /// <summary>
        /// タブの種類
        /// </summary>
        public enum TabStatus
        {
            BasicSetting = 0,   // 基本設定
            Sound,              // サウンド
            SingleMode,         // 育成
            Race,               // レース
            Live,               // ライブ
            Max
        }

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField]
        private FlickableObject _flickableObject = null;

        [SerializeField]
        private FlickToggleGroupCommon _flickToggleGroupCommon = null;

        /// <summary>
        /// ページ
        /// </summary>
        [SerializeField] private PartsOptionPageBasicSetting    _optionPageBasicSetting;
        [SerializeField] private PartsOptionPageSound           _optionPageSound;
        [SerializeField] private PartsOptionPageSingleMode      _optionPageSingleMode;
        [SerializeField] private PartsOptionPageRace            _optionPageRace;
        [SerializeField] private PartsOptionPageLive            _optionPageLive;

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static DialogCommon Open()
        {
            var DialogOptionHome = LoadAndInstantiatePrefab<DialogOptionHome>(ResourcePath.DIALOG_OPTION_HOME);
            DialogOptionHome.Setup();

            ContentOptionSound.AddCueSheet();

            var data = DialogOptionHome.CreateDialogData();
            data.AutoClose = false;
            data.Title = TextId.Menu0009.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = DialogOptionHome.OnClickCancel;
            data.RightButtonText = TextId.Common0261.Text();
            data.RightButtonCallBack = DialogOptionHome.OnClickSave;
            return DialogManager.PushDialog(data);
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        private void Setup()
        {
            // タブ切り替えの準備
            _flickToggleGroupCommon.SetOnSelectCallback(ChangeTab);
            _flickableObject.SetFlickCallback(_flickToggleGroupCommon.OnFlick);

            // 各ページの準備
            _optionPageBasicSetting.Setup();
            _optionPageSound.Setup();
            _optionPageSingleMode.Setup(GameDefine.OptionDialogType.Home);
            _optionPageRace.Setup(GameDefine.OptionDialogType.Home);
            _optionPageLive.Setup(GameDefine.OptionDialogType.Home);

            // 最初のタブは「基本設定」
            ChangeTab((int)TabStatus.BasicSetting);
        }

        /// <summary>
        /// タブを切り替える
        /// </summary>
        /// <param name="tabIndex"></param>
        private void ChangeTab(int tabIndex)
        {
            _optionPageBasicSetting.gameObject.SetActive(false);
            _optionPageSound.gameObject.SetActive(false);
            _optionPageSingleMode.gameObject.SetActive(false);
            _optionPageRace.gameObject.SetActive(false);
            _optionPageLive.gameObject.SetActive(false);

            // タブに合ったページをアクティブに
            switch ((TabStatus)tabIndex)
            {
                case TabStatus.BasicSetting:
                    _optionPageBasicSetting.gameObject.SetActive(true);
                    break;
                case TabStatus.Sound:
                    _optionPageSound.gameObject.SetActive(true);
                    break;
                case TabStatus.SingleMode:
                    _optionPageSingleMode.gameObject.SetActive(true);
                    break;
                case TabStatus.Race:
                    _optionPageRace.gameObject.SetActive(true);
                    break;
                case TabStatus.Live:
                    _optionPageLive.gameObject.SetActive(true);
                    break;
            }
        }

        /// <summary>
        /// 音量プレビューで使用するCueSheetのDLを登録する
        /// </summary>
        public static void RegisterDownloadPreviewSoundCueSheets(DownloadPathRegister register)
        {
            ContentOptionSound.RegisterDownloadPreviewSoundCueSheets(register);
        }

        /// <summary>
        /// 保存ボタンが押された時の処理
        /// </summary>
        /// <param name="dialog"></param>
        private void OnClickSave(DialogCommon dialog)
        {
            _optionPageBasicSetting.OnClickSave();
            _optionPageSound.OnClickSave();
            _optionPageSingleMode.OnClickSave();
            _optionPageRace.OnClickSave();
            _optionPageLive.OnClickSave();

            SaveDataManager.Instance.SaveLoader.Save();
            
            // 時間制限をかけられた場合、何時に設定されたかはわからないためすでに予約しているものを一度キャンセルするしかない.
            PushNotificationManager.Instance.DeleteLocalPushes(LocalPushDefine.LocalPushType.Tp);
            PushNotificationManager.Instance.DeleteLocalPushes(LocalPushDefine.LocalPushType.Rp);
            if (GameDefine.IS_ENABLE_JOBS)
            {
                PushNotificationManager.Instance.DeleteJobsLocalPushes();
            }

            // 各通知が有効なのであれば再度設定しなおす(時間フラグ、ONOFFフラグはは内部で見ている).
            PushNotificationManager.Instance.ReqTpMax(true);
            PushNotificationManager.Instance.ReqRpMax(true);
            if (GameDefine.IS_ENABLE_JOBS)
            {
                PushNotificationManager.Instance.ReqJobsMaxAll();
            }

            // 各オプションから保存する項目を登録（現状は「育成」のみ）
            var optionInfo = new List<OptionInfo>();
            _optionPageSingleMode.RegisterOptionInfo(ref optionInfo);

            WorkOptionData.SendChangeOptionRequestApi(optionInfo, OpenSavedDialog);

            void OpenSavedDialog()
            {
                // 保存完了ダイアログの表示タイミングでリソースがない場合
                if (RaceUtil.IsNeedDownloadRealFanfare())
                {
                    // リソースが無い状態でリアルファンファーレ設定がONになるとまずいのでキャンセルされた場合は設定OFFに変更する
                    DialogOptionRace.OpenCompleteFirstSettingRealFanfareDialog(onCancel: () =>
                    {
                        // 上でサウンド設定が更新されているはずなのでそれを反映させる
                        // これをやらないとオプションダイアログを閉じた際にOnCancelが走ってオプションダイアログを開いたときのサウンド設定が反映されてしまう
                        // https://xxxxxxxxxx/archives/C03CK20DCNS/p1670838178001359?thread_ts=1670568832.369509&cid=C03CK20DCNS
                        _optionPageSound.UpdateSetting();
                        _optionPageRace.SetFanfareGameOriginalSoundSetting(true);
                    });
                }
                else
                {
                    // 「設定を変更しました」ダイアログを開く
                    DialogOptionHome.OpenSavedMessageDialog(
                        // 1フレーム待機させて、
                        // フレームレートの設定時に破棄する予定のダイアログの
                        // DialogCloseAnimationへの上書きを回避させるため
                        onDestroy: () => this.WaitForNextFrame(() =>
                        {
                            // 閉じたらこのオプションダイアログも閉じる
                            dialog.Close(() => UIManager.Instance.UnlockGameCanvas());
                        }),
                        onBeginClose: _ => { UIManager.Instance.LockGameCanvas(); });
                }
            }
        }

        /// <summary>
        /// キャンセルボタンが押された時の処理
        /// </summary>
        /// <param name="dialog"></param>
        private void OnClickCancel(DialogCommon dialog)
        {
            _optionPageBasicSetting.OnClickCancel();
            _optionPageSound.OnClickCancel();
            _optionPageSingleMode.OnClickCancel();
            _optionPageRace.OnClickCancel();
            _optionPageLive.OnClickCancel();

            UIManager.Instance.LockGameCanvas();
            dialog.Close(() => UIManager.Instance.UnlockGameCanvas());
        }

        /// <summary>
        /// 「設定を変更しました」ダイアログを開く
        /// </summary>
        public static void OpenSavedMessageDialog(System.Action onDestroy = null,System.Action<bool> onBeginClose=null)
        {
            var data = new DialogCommon.Data();
            data.Title = TextId.AccoutDataLink0061.Text();
            data.FormType = DialogCommon.FormType.SMALL_ONE_BUTTON;
            data.Text = TextId.Outgame0309.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            if (onDestroy != null)
                data.AddDestroyCallback(onDestroy);
            if(onBeginClose != null)
                data.AddBeginCloseCallback(onBeginClose);
            DialogManager.PushDialog(data);
        }
    }
}
