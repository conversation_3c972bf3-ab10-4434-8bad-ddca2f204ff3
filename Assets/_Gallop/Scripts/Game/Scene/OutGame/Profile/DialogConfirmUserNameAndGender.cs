using Cute.Http;
using System;
using UnityEngine;
using UnityEngine.Serialization;

namespace Gallop
{
    /// <summary>
    /// トレーナー名変更ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogConfirmUserNameAndGender : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        #endregion DialogInnerBase

        #region SerializeField
        
        [SerializeField]
        private TextCommon _displayName = null;
        
        [SerializeField]
        private TextCommon _displayGender = null;

        #endregion
        
        /// <summary>
        /// 初期化
        /// </summary>
        private void SetNameAndGender(string name, GameDefine.Gender gender)
        {
            _displayName.text = name;
            _displayGender.text = gender == GameDefine.Gender.Male 
                ? TextId.Menu0031.Text() 
                : TextId.Menu0032.Text() ;
        }
        
        /// <summary>
        /// 指定されたクラスで開く
        /// </summary>
        public static void PushDialog(string name, GameDefine.Gender gender, Action onClickOK)
        {
            var component = LoadAndInstantiatePrefab<DialogConfirmUserNameAndGender>(ResourcePath.DIALOG_CONFIRM_USER_NAME_AND_GENDER_PATH);
            component.SetNameAndGender(name, gender);

            DialogCommon.Data dialogData = component.CreateDialogData();
            dialogData.Title = TextId.Outgame0429.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.AutoClose = false;

            dialogData.RightButtonCallBack = (dialog) =>
            {
                // 性別情報送信
                var req = new UserChangeSexRequest();
                req.sex = (int)gender;
                req.Send(res =>
                {
                    // Work更新
                    WorkDataManager.Instance.UserData.SetUserData(res.data.user_info);
                    // 名前変更ダイアログもまとめて閉じたいのRemoveAll
                    DialogManager.RemoveAllDialog(() =>
                    {
                        onClickOK();
                    });
                }, stallOneSecond: true);
            };
            dialogData.LeftButtonCallBack = (dialog) => { dialog.Close(); };

            DialogManager.PushDialog(dialogData);
        }              
    }
}
