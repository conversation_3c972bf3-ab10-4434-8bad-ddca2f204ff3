using Cute.Http;
using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// トレーナー名変更ダイアログ
    /// </summary>
    public class DialogChangeUserName : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        #endregion


        #region SerializeField, 変数

        [field : SerializeField, RenameField]
        protected InputFieldCommon InputField { get; set; } = null;

        protected ButtonCommon _button = null;
        protected Action<string> _onSuccess = null;

        protected string _preName;

        #endregion

        #region メソッド

        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(string userName, Action<string> onSuccessChangeName)
        {
            var component = LoadAndInstantiatePrefab<DialogChangeUserName>(ResourcePath.DIALOG_CHANGE_USER_NAME_PATH);

            DialogCommon.Data dialogData = component.CreateDialogData();
            dialogData.Title = TextId.Outgame0005.Text();

            // ダイアログの設定 (継承先でoverrideされる)
            component.SetDialogData(dialogData);

            var dialog = DialogManager.PushDialog(dialogData);
            component.Initialize(userName, onSuccessChangeName);
            component.SetButton(dialog);
            component.OnEndEdit(userName);
        }

        #region 初期化
        /// <summary>
        /// ダイアログの設定
        /// </summary>
        protected virtual void SetDialogData(DialogCommon.Data dialogData)
        {
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = OnClickDecide;
            dialogData.LeftButtonCallBack = d => d.Close();
            dialogData.AutoClose = false;
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Initialize(string name, Action<string> onSuccessChangeName)
        {
            _preName = name;
            InputField.text = name;
            InputField.onEndEdit.AddListener(OnEndEdit);
            _onSuccess = onSuccessChangeName;
        }

        /// <summary>
        /// ボタンへの参照を取得
        /// </summary>
        protected virtual void SetButton(DialogCommon dialog)
        {
            _button = dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
        }

        /// <summary>
        /// 編集終了時に呼ばれる
        /// </summary>
        protected void OnEndEdit(string text)
        {
            if (text.Length > GameDefine.USER_NAME_LIMIT)
            {
                InputField.text = text.Substring(0, GameDefine.USER_NAME_LIMIT);
            }

            bool isValid = CheckText(InputField.text);

            // ボタンへの参照はSetButton関数で取得している
            _button.interactable = isValid;
            SetDecideButtonNotification(isValid);
        }

        protected virtual void SetDecideButtonNotification(bool isValid)
        {
            _button.SetNotificationMessage("");
        }

        /// <summary>
        /// クライアント側での文字列チェック
        /// </summary>
        /// <param name="newText"></param>
        /// <returns></returns>
        private bool CheckText(string newText)
        {
            //空
            if (string.IsNullOrEmpty(newText) || _preName.Equals(newText))
            {
                return false;
            }
            return true;
        }
        #endregion 初期化

        #region 名前変更処理
        /// <summary>
        /// 決定ボタンを押した時の処理
        /// </summary>
        protected virtual void OnClickDecide(DialogCommon dialog)
        {
            // 成功時コールバック
            void OnSuccess(ChangeNameResponse res)
            {
                // 成功後、名前を更新する
                WorkDataManager.Instance.UserData.SetUserData(res.data.user_info);
                _onSuccess?.Invoke(res.data.user_info.name);
                _onSuccess = null;
                dialog.Close();
            }

            // API送信
            SendChangeUserNameAPI(InputField.text, OnSuccess);
        }

        /// <summary>
        /// トレーナー名変更APIを送信
        /// </summary>
        /// <param name="name"></param>
        /// <param name="onSuccess"></param>
        /// <param name="onError"></param>
        protected static void SendChangeUserNameAPI(string name, Action<ChangeNameResponse> onSuccess)
        {
            var req = new ChangeNameRequest { name = name };
            req.Send(onSuccess);
        }
        #endregion 名前変更処理

        #endregion メソッド
    }
}
