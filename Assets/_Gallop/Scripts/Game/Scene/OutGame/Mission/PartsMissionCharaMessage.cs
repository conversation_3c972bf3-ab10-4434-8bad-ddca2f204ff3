using DG.Tweening;
using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// ミッション：キャラの吹き出し＆音声
    /// </summary>
    public sealed class PartsMissionCharaMessage : PartsCharaMessageBase
    {
        #region Const

        /// <summary>
        /// Tweenのイリラベル
        /// </summary>
        private const string IN_NAME = "in";

        /// <summary>
        /// Tweenのハケラベル
        /// </summary>
        private const string OUT_NAME = "out";

        /// <summary>
        /// 吹き出しの一行に含められる最大文字数
        /// </summary>
        private const int CHARA_MESSAGE_INLINE_CHARACTER_NUM = 15;

        /// <summary>
        /// 待機ボイス再生間隔（秒）
        /// </summary>
        private const float IDLE_VOICE_INTERVAL = 25f;

        #endregion

        #region Nested Types

        /// <summary>
        /// トリガー
        /// </summary>
        public enum Trigger
        {
            None,
            OtherMissionTop,
            OtherMissionTopLimitedMission,
            OtherMissionTopClearedMission,
        }

        #endregion

        #region SerializeField

        /// <summary>
        /// TweenTimeline
        /// </summary>
        [SerializeField]
        private TweenAnimationTimelineComponent _tweenTimeline;

        #endregion

        #region Member

        /// <summary>
        /// ボイスのトリガー
        /// </summary>
        private Trigger _voiceTrigger = Trigger.None;

        private MasterCharacterSystemText.CharacterSystemText _systemText = null;

        #endregion

        #region Override

        /// <summary>
        /// モデルセット時ボイス再生
        /// </summary>
        protected override MasterCharacterSystemText.CharacterSystemText PlaySetVoice()
        {
            switch (_voiceTrigger)
            {
                case Trigger.None:
                    return _systemText;
                case Trigger.OtherMissionTop:
                    return AudioManager.Instance.PlaySystemVoice_OtherMissionTop();
                case Trigger.OtherMissionTopLimitedMission:
                    return AudioManager.Instance.PlaySystemVoice_OtherMissionTopLimitedMission();
                case Trigger.OtherMissionTopClearedMission:
                    return AudioManager.Instance.PlaySystemVoice_OtherMissionTopCleardMission();
                default:
                    return null;
            }
        }

        /// <summary>
        /// 吹き出しを開く
        /// </summary>
        protected override void Open()
        {
            gameObject.SetActive(true);
            Init(BalloonTailType.UpperLeft);
            _tweenTimeline.Stop(OUT_NAME);
            var seq = _tweenTimeline.Play(IN_NAME);
            seq.Goto(0);
            seq.Play();
        }

        /// <summary>
        /// 吹き出し閉じる
        /// </summary>
        protected override void Close()
        {
        }

        /// <summary>キャラモデルの当たり判定を使用するか？</summary>
        public override bool IsUseCharaTouchColider() { return false; }

        #endregion

        #region Method

        /// <summary>
        /// アセットのDL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // ボイス登録
            AudioManager.Instance.RegisterDownloadByCharaIds(register, new List<int> { GameDefine.RIJICHO_CHARA_ID }, CharacterSystemTextGroupExtension.Scene.Other , true);
        }

        /// <summary>
        /// 表示準備（先にSetModelを呼んでおくこと）
        /// </summary>
        public void Setup()
        {
            // 一行に含められる最大文字数をセット
            SetInlineCharacterNum(CHARA_MESSAGE_INLINE_CHARACTER_NUM);
            // 待機ボイス再生間隔（秒）をセット
            SetIdleVoiceInterval(IDLE_VOICE_INTERVAL);

            gameObject.SetActive(false);
        }

        /// <summary>
        /// 再生
        /// </summary>
        /// <param name="trigger"></param>
        public void Play(Trigger trigger)
        {
            //非アクティブにしているとAwakeが走らなかった(ActiveをTrueにしたときにAwakeが呼ばれる、なぜ呼ばれないのかは不明)ので明示的にInitを呼んでおく
            Init();
            _voiceTrigger = trigger;
            PlaySet();
        }

        /// <summary>
        /// 再生
        /// </summary>
        /// <param name="trigger"></param>
        public void Play(MasterCharacterSystemText.CharacterSystemText systemText)
        {
            //非アクティブにしているとAwakeが走らなかった(ActiveをTrueにしたときにAwakeが呼ばれる、なぜ呼ばれないのかは不明)ので明示的にInitを呼んでおく
            Init();
            _systemText = systemText;
            _voiceTrigger = Trigger.None;
            PlaySet();
        }


        #endregion
    }
}
