using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 才能開花画面（ダイアログ）
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCharacterTalentUpgrade : DialogInnerBase
    {
        private const int SKILL_LIST_ITEM_WIDTH = 984;

        #region DialogInnerBase
        /// <summary>
        /// ダイアログの種類
        /// </summary>
        /// <returns></returns>
        public override DialogCommon.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_TWO_BUTTON;
        }
        /// <summary>
        /// どこにダイアログぶら下げるか
        /// </summary>
        /// <returns></returns>
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Root;
        }

        public override string GetFooterText() => TextId.Outgame0239.Text();

        #endregion

        #region SerializeField
        [SerializeField]
        private PartsSimplePlayListItem _eventListItem = null;
        [SerializeField]
        private PartsSingleModeSkillListItem _skillListItem = null;
        [SerializeField]
        private Transform _skillListRoot = null;

        [SerializeField]
        private ItemIconWithNeedNum _itemPrefab = null;
        [SerializeField]
        private Transform _itemListRoot = null;
        
        [SerializeField]
        private TextCommon _textOwnMoney = null;
        [SerializeField]
        private TextCommon _textNeedMoney = null;

        [SerializeField]
        private TextCommon _talentCurrentLevel = null;

        [SerializeField]
        private TextCommon _nextTalentLevel = null;

        [SerializeField]
        private GameObject _subTitleObject = null;
        [SerializeField]
        private TextCommon _subTitleText = null;
        [SerializeField]
        private TextCommon _getItemTitleText = null;

        #endregion

        #region 変数
        private WorkCardData.CardData _cardWorkData;
        private int _startTalentLevel;
        private int _endTalentLevel;
        private Action _onSuccessAPI;

        /// <summary>
        /// 追加イベントが存在するか
        /// </summary>
        private bool IsExistEvent => _endTalentLevel >= GameDefine.TALENT_LEVEL_RELEASE_SKILL_POINT_DISCOUNT;

        /// <summary>
        /// 獲得スキルが存在するか
        /// 全てのレベルに存在する仕様になっているが念のためチェック
        /// </summary>
        private bool IsExistSkill
        {
            get
            {
                var masterCard = MasterDataManager.Instance.masterCardData.Get(_cardWorkData.CardId);
                if (masterCard == null)
                {
                    Debug.LogError("masterCardがnullです");
                    return false;
                }

                var skillCount = 0;
                for (int level = _startTalentLevel + 1; level <= _endTalentLevel; ++level)
                {
                    var skillSet = MasterDataManager.Instance.masterAvailableSkillSet.GetFromTalentLevel(masterCard, level);
                    if (skillSet != null)
                    {
                        skillCount++;
                    }
                }

                return skillCount > 0;
            }
        }

        #endregion

        #region メソッド
        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="cardData"></param>
        /// <param name="updateViewCallback"></param>
        public static void Open(WorkCardData.CardData cardData, int targetTalentLv, Action updateViewCallback)
        {
            GameObject instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CHARA_TALENT_UPGRADE_PATH));
            var component = instance.GetComponent<DialogCharacterTalentUpgrade>();
            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = instance;
            dialogData.Title = Localize.Get(TextId.Outgame0195);
            dialogData.LeftButtonText = Localize.Get(TextId.Common0004);
            dialogData.RightButtonText = Localize.Get(TextId.Common0288);
            dialogData.AutoClose = false;
            dialogData.LeftButtonCallBack = (clickedDialog) => { clickedDialog.Close();  };
            dialogData.RightButtonCallBack = (clickedDialog) => { component.OnClickTalentUpgrade(); };

            component.Setup(dialogData, cardData, cardData.TalentLevel, targetTalentLv, updateViewCallback);

            var dialog = DialogManager.PushDialog(dialogData);

            // SEを変更
            dialog.SetButtonSeType(DialogCommon.ButtonIndex.Right, ButtonCommon.ButtonSeType.DecideL02);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="dialogData"></param>
        /// <param name="cardData"></param>
        /// <param name="startTalentLv"></param>
        /// <param name="endTalentLv"></param>
        /// <param name="onSuccessAPI"></param>
        private void Setup(DialogCommon.Data dialogData, WorkCardData.CardData cardData, int startTalentLv, int endTalentLv, Action onSuccessAPI = null)
        {
            _cardWorkData = cardData;
            _onSuccessAPI = onSuccessAPI;
            _startTalentLevel = startTalentLv;
            _endTalentLevel = endTalentLv;

            ApplySingleModeEvent();
            ApplySkills();
            ApplyMaterials();
            ApplyTexts();
        }


        /// <summary>
        /// 育成イベント反映
        /// </summary>
        private void ApplySingleModeEvent()
        {
            // 覚醒Lv7に到達する場合は切れ者イベントを解放
            if (!IsExistEvent) return;

            // 小見出し追加
            _subTitleText.text = TextId.Outgame626005.Text();
            var subTitle = Instantiate(_subTitleObject, _skillListRoot);
            subTitle.SetActiveWithCheck(true);

            // イベント追加
            var partsEvent = Instantiate<PartsSimplePlayListItem>(_eventListItem, _skillListRoot);
            var storyData = MasterDataManager.Instance.masterSingleModeStoryData.GetStoryData(MasterSingleModeStoryData.SKILL_POINT_DISCOUNT_STORY_ID);
            var param = new PartsSimplePlayListItem.SetupParameter
            {
                Title = storyData.Title,
            };
            partsEvent.UpdateItem(param);
        }

        /// <summary>
        /// スキル反映
        /// </summary>
        private void ApplySkills()
        {
            var masterCard = MasterDataManager.Instance.masterCardData.Get(_cardWorkData.CardId);
            if (masterCard == null)
            {
                Debug.LogError("masterCardがnullです");
                return;
            }

            // 小見出し追加
            _subTitleText.text = TextId.Character0295.Text();
            var subTitle = Instantiate(_subTitleObject, _skillListRoot);

            for (int level = _startTalentLevel + 1; level <= _endTalentLevel; ++level)
            {
                var skillSet = MasterDataManager.Instance.masterAvailableSkillSet.GetFromTalentLevel(masterCard, level);
                if (skillSet == null)
                {
                    // そのレベルで獲得できるスキルがない場合はcontinue
                    // 2020年3月時点での仕様ではそのケースはないようだが、念のため。
                    continue;
                }

                var partsSkill = Instantiate<PartsSingleModeSkillListItem>(_skillListItem, _skillListRoot);

                // 横幅修正
                var rectTransform = partsSkill.transform as RectTransform;
                if (rectTransform != null)
                {
                    rectTransform.sizeDelta = new Vector2(SKILL_LIST_ITEM_WIDTH, rectTransform.sizeDelta.y);
                }

                var skillInfo = new PartsSingleModeSkillListItem.Info(skillSet.SkillId)
                {
                    IsNew = true
                };
                partsSkill.UpdateItem(skillInfo);
                partsSkill.ChangeTextColorType(FontColorType.Pink);
            }
            subTitle.SetActiveWithCheck(IsExistSkill);
        }

        /// <summary>
        /// 必要素材反映
        /// </summary>
        private void ApplyMaterials()
        {
            var itemList = GallopUtil.GetCharacterCardTalentUpgradeItemList(_cardWorkData.GetTalentGroupId(), _startTalentLevel, _endTalentLevel);
            foreach (var itemData in itemList)
            {
                var partsItem = Instantiate<ItemIconWithNeedNum>(_itemPrefab, _itemListRoot);
                partsItem.Setup(itemData.Category, itemData.ItemId, itemData.ItemNum, IconBase.SizeType.Common_L);
            }
        }

        /// <summary>
        /// テキスト反映
        /// </summary>
        private void ApplyTexts()
        {
            _textOwnMoney.text = GallopUtil.GetHaveItemNum(GameDefine.ItemCategory.MONEY, GameDefine.MONEY_ITEM_ID).ToString(TextUtil.CommaSeparatedFormat);
            //必要マネーを調べる。
            var needNum = GallopUtil.GetMoneyForTalentUpgrade(_endTalentLevel , _startTalentLevel);
            _textNeedMoney.text = needNum.ToString(TextUtil.CommaSeparatedFormat);

            // 覚醒Lv
            _talentCurrentLevel.text = TextUtil.Format("{0} {1}", TextId.Outgame0084.Text(), _startTalentLevel);
            _talentCurrentLevel.VerticalGradientColor = CardUpgradeUtil.GetTalentLevelTextGradientColor(_startTalentLevel);
            _talentCurrentLevel.UpdateColor();
            _nextTalentLevel.text = TextUtil.Format("{0} {1}", TextId.Outgame0084.Text(), _endTalentLevel);
            _nextTalentLevel.VerticalGradientColor = CardUpgradeUtil.GetTalentLevelTextGradientColor(_endTalentLevel);
            _nextTalentLevel.UpdateColor();

            // 獲得できるものタイトル
            var titleText = IsExistEvent switch
            {
                true when IsExistSkill => TextId.Outgame626004.Text(),
                true => TextId.Outgame626005.Text(),
                _ => TextId.Character0295.Text(),
            };
            _getItemTitleText.text = titleText;
        }
        
        /// <summary>
        /// 覚醒Lvアップ開始ボタン押下
        /// </summary>
        private void OnClickTalentUpgrade()
        {
            // 通信
            SendTalentUpgradeAPI((res) =>
            {
                DialogManager.RemoveAllDialog(() =>
                {
                    WorkDataManager.Instance.ItemData.Update(res.data.item_data_array);
                    var workCardData = WorkDataManager.Instance.CardData.UpdateCardData(res.data.card_data);
                    if (res.data.memory_data != null)
                    {
                        WorkDataManager.Instance.MemoryData.Apply(new[] { res.data.memory_data });
                    }

                    _onSuccessAPI?.Invoke();
                });
            });
        }

        /// <summary>
        /// API送信：才能開花
        /// </summary>
        private void SendTalentUpgradeAPI(Action<CardTalentStrengthenResponse> onSuccess)
        {
            var req = new CardTalentStrengthenRequest();
            req.card_id = _cardWorkData.CardId;
            req.talent_level = _cardWorkData.TalentLevel;
            req.new_talent_level = _endTalentLevel;
            Cute.Http.HttpManager.Instance.Send(req, onSuccess);
        }

        #endregion
    }

}
