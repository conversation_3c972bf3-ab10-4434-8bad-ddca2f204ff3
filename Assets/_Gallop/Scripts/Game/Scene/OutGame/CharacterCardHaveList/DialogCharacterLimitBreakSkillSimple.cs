using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// スキル詳細ダイアログ
    /// </summary>
    public class DialogCharacterLimitBreakSkillSimple : DialogInnerBase
    {
        #region DialogInnerBase
        /// <summary>
        /// ダイアログの種類
        /// </summary>
        /// <returns></returns>
        public override DialogCommon.FormType GetFormType()
        {
            return DialogCommon.FormType.SMALL_ONE_BUTTON;
        }

        /// <summary>
        /// どこにダイアログぶら下げるか
        /// </summary>
        /// <returns></returns>
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }


        #endregion

        #region SerializeField
        
        [SerializeField]
        private TextCommon _uniqSkillInfoText = null;
        [SerializeField]
        private TextCommon _warnText = null;
        [SerializeField]
        private PartsSingleModeSkillListItem _partsSingleModeSkillListItem;
        [SerializeField]
        private ButtonCommon _succesionDetailButton;
        
        #endregion

        #region メソッド
        /// <summary>
        /// ダイアログを開く（スキルセットID指定版）
        /// </summary>
        public static void Open(int skillSetId, bool isDrawUniqSkillInfo)
        {
            var skillSet = MasterDataManager.Instance.masterSkillSet.Get(skillSetId);
            if (skillSet == null)
            {
                Debug.LogErrorFormat("MasterSkillSetがnullです。(SkillSetID={0})", skillSetId);
                return;
            }
            OpenWithSkillId(skillSet.SkillId1, skillSet.SkillLevel1, isDrawUniqSkillInfo);
        }

        /// <summary>
        /// ダイアログを開く（スキルID指定版）
        /// </summary>
        public static void OpenWithSkillId(
            int skillId, int skillLevel, bool isDrawUniqSkillInfo,
            SkillDefine.SkillLimitedType skillLimitedType = SkillDefine.SkillLimitedType.Null, bool enableSuccesionDetailButton = true)
        {
            GameObject instace = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CHARA_LIMITBREAK_SKILL_SIMPLE_PATH));
            var component = instace.GetComponent<DialogCharacterLimitBreakSkillSimple>();
            component.Setup(skillId, skillLevel, isDrawUniqSkillInfo, enableSuccesionDetailButton);
            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = instace;
            dialogData.Title = TextId.Character0076.Text();

            switch (skillLimitedType)
            {
                // ストーリーアンロックレース限定で獲得しているスキルには注意書き。
                case SkillDefine.SkillLimitedType.StoryUnlockSkill:
                    dialogData.FooterText = TextId.Story0082.Text();
                    break;
                case SkillDefine.SkillLimitedType.LimitedEventSkill:
                    dialogData.FooterText = TextId.TeamBuilding467001.Text();
                    break;
            }

            dialogData.CenterButtonText = Localize.Get(TextId.Common0007);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(int skillId, int skillLevel, bool isDrawUniqSkillInfo, bool enableSuccesionDetailButton = true)
        {
            var skillData = MasterDataManager.Instance.masterSkillData.Get(skillId);
            if (skillData == null)
            {
                Debug.LogErrorFormat("MasterSkillDataがnullです。(SkillID={0})", skillId);
                return;
            }
            var skillInfo = new PartsSingleModeSkillListItem.Info(skillId);
            skillInfo.Level = skillLevel;
            skillInfo.IsIconLongTapEnable = false;
            // スキルの情報を表示する
            _partsSingleModeSkillListItem.UpdateItem(skillInfo, false);

            _uniqSkillInfoText.SetActiveWithCheck(isDrawUniqSkillInfo);
            // 育成ハードモードの注意文言
            if (SingleModeUtils.IsDifficultyModeAdditionSkill(skillId))
            {
                _warnText.gameObject.SetActive(true);
            }

            // 継承スキルボタン設定
            // ※育成中や殿堂入りウマ娘ダイアログは除外。表示させない。
            {
                // 殿堂入りウマ娘詳細ダイアログか
                var existTrainedCharaDialog = DialogManager.Instance.GetDialogInnerComponent<DialogTrainedCharacterDetail>();
                // 育成中のウマ娘詳細ダイアログか
                var existSingleModeCharaDialog = DialogManager.Instance.GetDialogInnerComponent<DialogSingleModeMainCharacterDetail>();
                // 育成モード中か
                var inSingleMode = SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.SingleMode;

                if (!existTrainedCharaDialog && !inSingleMode && !existSingleModeCharaDialog
                    && enableSuccesionDetailButton)
                {
                    var skillDataValues = MasterDataManager.Instance.masterSkillData.dictionary.Values;
                    var successionSkillData = skillDataValues.FirstOrDefault(x => x.UniqueSkillId1 == skillId);

                    if (successionSkillData != null)
                    {
                        _succesionDetailButton.SetOnClick(() =>
                        {
                            DialogSuccessionSkillDetail.Open(skillId);
                        });
                        _succesionDetailButton.SetActiveWithCheck(true);
                    }
                    // ★1, ★2ウマ娘はボタングレーアウト
                    // 「このスキルは継承スキルが存在しません」Notification表示
                    else
                    {
                        _succesionDetailButton.SetInteractable(false);
                        _succesionDetailButton.SetNotificationMessage(TextId.Outgame352036.Text());
                    }
                }
                else
                {
                    _succesionDetailButton.SetActiveWithCheck(false);
                }
            }
        }
        #endregion
    }
}
