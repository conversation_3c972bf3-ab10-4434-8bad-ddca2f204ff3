using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class DialogCharacterPieceExchangeConfirm : DialogInnerBase
    {
        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override  DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        /// <summary> ピースリストを作成するクラス </summary>
        [SerializeField]
        private PartsItemIconListVertical _itemIconList;
        [SerializeField]
        private TextCommon _cloverNum;
        
        private DialogCommon _dialog = null;

        /// <summary>
        /// ダイアログの準備
        /// </summary>
        /// <param name="selectingPieceList"></param>
        private void Setup(IReadOnlyCollection<PieceData> selectingPieceList)
        {
            int selectingPieceCount = selectingPieceList.Count;
            var pieceIconInfoList = new List<ItemIconListInfo>(selectingPieceCount);
            foreach (var selectingPiece in selectingPieceList)
            {
                var pieceIconInfo = new ItemIconListInfo(
                    GameDefine.ItemCategory.CARD_PIECE, selectingPiece.piece_id, selectingPiece.piece_num
                );
                // pieceIconInfo.IsDispNumはデフォルトtrueなので設定しなくても個数表示される
                pieceIconInfoList.Add(pieceIconInfo);
            }
            _itemIconList.CreateList(pieceIconInfoList);

            //クローバー数表記の更新
            CharacterPieceExchangeViewController.UpdateCloverNum(selectingPieceList, _cloverNum);
        }

        #region Static Method
        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="selectingCardIdList"></param>
        /// <param name="onDecide"></param>
        public static void Open( IReadOnlyCollection<PieceData> pieceDataList, System.Action<IReadOnlyCollection<PieceData>> onDecide)
        {
            var dialogContent = LoadAndInstantiatePrefab<DialogCharacterPieceExchangeConfirm>(ResourcePath.DIALOG_CHARACTER_PIECE_EXCHANGE_CONFIRM);
            dialogContent.Setup(pieceDataList);

            var data = dialogContent.CreateDialogData();
            data.Title = TextId.Outgame0160.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.RightButtonText = TextId.Outgame0243.Text();
            data.RightButtonCallBack = _ => onDecide?.Invoke(pieceDataList);

            var dialog = DialogManager.PushDialog(data);
            
            dialogContent._dialog = dialog;

            // SEを変更
            dialog.SetButtonSeType(DialogCommon.ButtonIndex.Right, ButtonCommon.ButtonSeType.DecideL02);
        }
        #endregion
    }
}
