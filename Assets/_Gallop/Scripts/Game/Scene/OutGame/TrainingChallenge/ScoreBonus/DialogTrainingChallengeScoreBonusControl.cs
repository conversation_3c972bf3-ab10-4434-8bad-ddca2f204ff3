
using UnityEngine;

namespace Gallop
{
    namespace TrainingChallenge
    {
        /// <summary>
        /// 育成チャレンジ : スコアボーナスダイアログ
        /// </summary>
        [RequireComponent(typeof(DialogTrainingChallengeScoreBonusView))]
        public sealed class DialogTrainingChallengeScoreBonusControl : MonoBehaviour
        {
            #region class, 定数

            #endregion

            #region private変数

            [SerializeField] private TrainingChallengeScoreBonusCoreControl _core;

            private DialogTrainingChallengeScoreBonusView _view;
            private DialogTrainingChallengeScoreBonusModel _model = new DialogTrainingChallengeScoreBonusModel();

            #endregion

            #region publicメソッド

            /// <summary>
            /// ダイアログを開く
            /// </summary>
            public static void Open(int examId)
            {
                var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(DialogTrainingChallengeScoreBonusModel.PrefabPath)).GetComponent<DialogTrainingChallengeScoreBonusControl>();
                instance.Setup(examId);
            }

            public void Setup(int examId)
            {
                _view = GetComponent<DialogTrainingChallengeScoreBonusView>();
                _view.Setup(_model.Title, _model.CloseButtonText);

                //_core.Setup(examId);
            }

            #endregion

            #region privateメソッド

            #endregion
        }
    }
}
