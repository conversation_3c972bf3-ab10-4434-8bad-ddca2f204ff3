using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using DG.Tweening;

namespace Gallop
{
    using AnimSequenceHelper;
    using UnityEngine.UI;

    namespace TrainingChallenge
    {
        using BonusType = TrainingChallengeDefine.ScoreBonusType;

        /// <summary>
        /// 育成チャレンジ : スコアボーナスダイアログ
        /// </summary>
        public sealed class TrainingChallengeScoreBonusCoreView : MonoBehaviour
        {
            #region class, 定数

            #endregion

            #region private変数

            [SerializeField] private CanvasGroup _rootCanvasGroup;
            [SerializeField] private Transform _contentRoot;
            [SerializeField] private TrainingChallengeScoreBonusCoreContentView _contentView;
            [SerializeField] private ScrollRectCommon _scroll;

            private DialogCommon _dialog;
            private List<TrainingChallengeScoreBonusCoreContentView> _bonusContents = new List<TrainingChallengeScoreBonusCoreContentView>();
            private bool _isDetailView;
            private ResourceManager.ResourceHash _targetHash = ResourceManager.ResourceHash.InvalidHash;

            #endregion

            #region publicメソッド

            public void Setup(bool isDetailView, ResourceManager.ResourceHash hash)
            {
                _isDetailView = isDetailView;
                _targetHash = hash;

                _contentView.SetActiveWithCheck(false);
            }

            /// <summary>
            /// 評価点
            /// </summary>
            public void ApplyRankScore(TrainingChallengeScore totalScore, List<ScoreBonusData> bonues)
            {
                // 評価点が無ければ生成もしない = 表示されない
                if (totalScore == null) return;

                CreateContentView()
                    .SetupRankScore(totalScore, bonues);
            }

            /// <summary>
            /// スキルボーナス情報のセット
            /// </summary>
            public void ApplySkill(TrainingChallengeScore totalScore, List<ScoreBonusSkillData> bonuses)
            {
                if (bonuses.IsNullOrEmpty()) return;

                CreateContentView()
                    .SetupSkill(totalScore, bonuses);
            }

            /// <summary>
            /// レースボーナス情報のセット
            /// </summary>
            public void ApplyRace(TrainingChallengeScore totalScore, List<ScoreBonusRaceData> races)
            {
                if (races.IsNullOrEmpty()) return;

                CreateContentView()
                    .SetupRace(totalScore, races);
            }

            /// <summary>
            /// パラメータ系
            /// </summary>
            public void ApplyParameter(TrainingChallengeScore totalScore, List<ScoreBonusParameterData> parameters)
            {
                if (parameters.IsNullOrEmpty()) return;

                CreateContentView()
                    .SetupParameter(totalScore, parameters);
            }

            /// <summary>
            /// シナリオボーナス情報のセット
            /// </summary>
            public void ApplyScenario(TrainingChallengeScore totalScore, List<ScoreBonusData> bonuses)
            {
                if (bonuses.IsNullOrEmpty()) return;

                CreateContentView()
                    .SetupScore(BonusType.Scenario, totalScore, bonuses);
            }

            /// <summary>
            /// イリアニメーション
            /// </summary>
            public float PlayInBaseView(Sequence sequence, float delay)
            {
                const float DURATION = 0.2f;

                // フェード
                sequence.InsertFade(_rootCanvasGroup, 0f, 1f, delay, DURATION);

                // 左へ移動
                sequence.InsertLocalMoveX(_rootCanvasGroup.transform, -50f, delay, DURATION);

                // アニメーションが始まった時にリストのタッチを無効化して、終わった時に有効化する
                SetBlockRaycasts(false);

                sequence.onComplete += () => SetBlockRaycasts(true);

                return delay + sequence.GetDefaultDelay();
            }

            public float PlayInListView(Sequence sequence, float delay, System.Action<float> onPlayListInSe)
            {
                // 最大数を決めてアニメーション再生する(画面によってサイズが変わらないため現状固定）
                const int MaxNum = 3;

                // スクロールを整列させるために一度Canvasの強制更新を行う
                Canvas.ForceUpdateCanvases();

                var scrollBounds = (_scroll.transform as RectTransform).GetBounds();

                bool isFirst = true;

                for (int i = 0, size = Mathf.Min(_bonusContents.Count, MaxNum); i < size; ++i)
                {
                    // 範囲外になったら終了
                    var result = _bonusContents[i].TryPlayInView(scrollBounds, sequence, delay, 
                        (seDelay) =>
                        {
                            // 最初の要素であれば呼び出す
                            if (isFirst)
                            {
                                onPlayListInSe?.Invoke(seDelay);
                                isFirst = false;
                            }
                        });

                    if (!result.inRange) return result.delay;

                    delay = result.Item2;
                }

                return delay;
            }

            public void SetBlockRaycasts(bool isEnable)
            {
                _rootCanvasGroup.blocksRaycasts = isEnable;
            }

            #endregion

            #region privateメソッド

            /// <summary>
            /// 項目一つのViewを生成して返す
            /// </summary>
            private TrainingChallengeScoreBonusCoreContentView CreateContentView()
            {
                var instance = Instantiate(_contentView, _contentRoot);
                instance.gameObject.SetActive(true);

                if (_isDetailView)
                {
                    instance.SetupAtDetail(_targetHash);
                }
                else
                {
                    instance.SetupAtResult();
                }

                _bonusContents.Add(instance);

                return instance;
            }

            #endregion
        }
    }
}
