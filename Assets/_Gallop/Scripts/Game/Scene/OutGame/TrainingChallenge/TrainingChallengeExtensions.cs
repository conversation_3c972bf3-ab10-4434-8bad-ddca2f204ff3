using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成チャレンジ用の拡張群
    /// </summary>
    namespace TrainingChallenge
    {
        public static class RaceDefineExtensions
        {
        }

        /// <summary>
        /// <see cref="TrainingChallengeExamInfo"/>
        /// </summary>
        public static class TrainingChallengeExamInfoExtensions
        {
            public static TrainingChallengeDefine.ResultType GetResultType(this TrainingChallengeExamInfo self) =>
                (TrainingChallengeDefine.ResultType)self.result_type;
        }

        /// <summary>
        /// <see cref="WorkTrainedCharaData.TrainedCharaData"/>
        /// </summary>
        public static class TrainedCharaExtensions
        {
            /// <summary>
            /// 編集不可能状態で詳細を開く
            /// </summary>
            public static void OpenDetailAtUneditable(this WorkTrainedCharaData.TrainedCharaData self)
            {
                if (self == null) return;

                // 他人の場合そのまま開く
                if (self.IsOthers)
                {
                    var parameter = DialogTrainedCharacterDetail.CreateSetupParameter(self);
                    DialogTrainedCharacterDetail.OpenWithGetViewerName(parameter, self.ViewerId);
                }
                else
                {
                    DialogTrainedCharacterDetail.Open(self.GetDetailUneditableParameter());
                }
            }

            /// <summary>
            /// 特定の編集を禁止するパラメータを取得
            /// </summary>
            public static DialogTrainedCharacterDetail.SetupParameter GetDetailUneditableParameter(this WorkTrainedCharaData.TrainedCharaData self)
            {
                var parameter = DialogTrainedCharacterDetail.CreateSetupParameter(self);

                parameter.GetPartsParameter<PartsTrainedCharacterDetailHeaderTitle.SetupParameter>((param) =>
                {
                    // お気に入り設定の変更を禁止
                    param.Own.EnableLockChange = false;

                    // 二つ名変更ボタンは非表示
                    param.Own.NickNameChangeType = PartsTrainedCharacterDetailHeaderTitle.SetupParameter.ForMe.NickNameChange.Hide;

                    // メモ機能は防がなくて良い
                });

                return parameter;
            }
        }
    }
}
