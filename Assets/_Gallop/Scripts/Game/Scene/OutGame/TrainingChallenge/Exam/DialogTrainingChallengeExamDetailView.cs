using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    namespace TrainingChallenge
    {
        /// <summary>
        /// 育成チャレンジ : 試験詳細ダイアログ
        /// </summary>
        public sealed class DialogTrainingChallengeExamDetailView : DialogInnerBase
        {
            #region class, 定数

            #endregion

            #region private変数

            [SerializeField] private ImageCommon _titleBaseImage;
            [SerializeField] private TextCommon _examNameText;
            [SerializeField] private TextCommon _examIndexText;
            [SerializeField] private BitmapTextCommon _highScoreText;
            [SerializeField] private CharacterButton _highScoreCharaButton;
            [SerializeField] private TextCommon _unchallengedText;
            [SerializeField] private RewardItemIcon[] _itemIconArray;
            [SerializeField] private TrainingChallengeExamClearRateItemView[] _clearRateItemArray;
            [SerializeField] private TextCommon _confirmationText;

            private DialogCommon _dialog;
            private bool _isSingleModeScene;

            #endregion

            #region プロパティ

            /// <summary>
            /// 試験が選択されたときに呼び出される
            /// </summary>
            public System.Action OnSelected { get; set; }

            public System.Action OnClosed { get; set; }

            /// <summary>
            /// ハイスコアキャラを長押ししたとき
            /// </summary>
            public System.Action<CharacterButton> OnCharaLongTapped { get; set; }

            #endregion

            #region publicメソッド

            /// <summary>
            /// ダイアログの形態（大きさなど）タイプ取得
            /// </summary>
            public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

            /// <summary>
            /// ダイアログの親オブジェクト位置タイプ取得
            /// </summary>
            public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

            public void Setup(string title, string closeButtonText, string notificationMessage, bool selectButtonEnable)
            {
                SetupInternal(true, title, closeButtonText, notificationMessage, selectButtonEnable);
            }

            public void SetupAtSingleMode(string title, string closeButtonText)
            {
                _isSingleModeScene = true;

                SetupInternal(false, title, closeButtonText, null, false);
            }


            public void SetupContents(
                ExamDataEntity data,
                MasterTrainingChallengeExam.TrainingChallengeExam master,
                int index,
                WorkTrainedCharaData.TrainedCharaData charaButtonInfo, 
                List<(int score, bool isCleard)> examClearScoreList,
                string confirmationMessage)
            {
                // Note: 育成内で次Viewに遷移する直前にダイアログが開かれた場合画像がはがれる。
                // 剥がれないようにアトラスもダイアログHash紐づけで読み込ませる(暫定処理なのでusingで囲む処理は今は入れない)
                var uiManager = UIManager.Instance;
                if (_isSingleModeScene)
                {
                    // ハッシュロードに切り替え
                    uiManager.OverrideLoadAtlasHash(DialogHash);

                    // タイトル画像をアトラスから読み込み
                    var atlas = uiManager.LoadAtlas(TargetAtlasType.RaceCommon);
                    if (atlas != null)
                    {
                        _titleBaseImage.sprite = atlas.GetSprite("utx_frm_racetitle_00_sl");
                    }
                }

                // 試験名
                _examNameText.text = master.FeatureDefine.GetText();
                _examIndexText.text = index.ToString();
                
                if (data != null)
                {
                    // 最高スコア
                    SetupHighScore(data.HasHighScore, data.HighScore, charaButtonInfo);

                    // 獲得報酬
                    SetupRewardItems(data.RewardItems, data.IsClear);
                }
                else
                {
                    // 最高スコア
                    SetupHighScore(false, null, charaButtonInfo);

                    // 獲得報酬
                    SetupRewardItems(master.RewardItems, false);
                }

                // 試験評価
                SetupExamClearRate(examClearScoreList);

                _confirmationText.text = confirmationMessage;

                if (_isSingleModeScene)
                {
                    uiManager.ResetOverrideLoadAtlasHash();
                }
            }

            public void Close()
            {
                _dialog.Close();
            }

            #endregion

            #region privateメソッド

            private void SetupInternal(bool isTwoButton, string title, string closeButtonText, string notificationMessage, bool selectButtonEnable)
            {
                var data = CreateDialogData();
                if (isTwoButton)
                {
                    data.FormType = DialogCommonBase.FormType.BIG_TWO_BUTTON;
                    data.LeftButtonText = closeButtonText;
                    data.RightButtonText = TextId.Common0023.Text();
                    if (!selectButtonEnable)
                    {
                        data.RightButtonNoInteractableNotiffication = notificationMessage;
                    };

                    data.LeftButtonCallBack = _ => OnClosed?.Invoke();
                    data.RightButtonCallBack = _ => OnSelected?.Invoke();
                }
                else
                {
                    data.FormType = DialogCommonBase.FormType.BIG_ONE_BUTTON;

                    data.CenterButtonText = closeButtonText;
                    data.CenterButtonCallBack = _ => OnClosed?.Invoke();
                }

                data.Title = title;
                data.AutoClose = false;

                _dialog = DialogManager.PushDialog(data);
            }

            /// <summary>
            /// 獲得報酬一覧のセットアップ
            /// </summary>
            private void SetupRewardItems(List<(GameDefine.ItemCategory category, int id, int num)> RewardItems, bool isCleard)
            {
                var rewardNum = RewardItems.Count;
                for (int i = 0; i < _itemIconArray.Length; i++)
                {
                    if (i > rewardNum)
                    {
                        _itemIconArray[i].SetActiveWithCheck(false);
                        continue;
                    }

                    _itemIconArray[i].SetActiveWithCheck(true);
                    var rewardItem = RewardItems[i];

                    // 長押しで詳細表示するときのアイコンサイズが適切になるようSizeTypeを設定しなおす
                    _itemIconArray[i].SetSize(IconBase.SizeType.Common_L);

                    // アイテムアイコンをダイアログ紐づきでロードする
                    _itemIconArray[i].LoadTextureFunc = (path) => ResourceManager.LoadOnHash<Texture2D>(path, DialogHash); 
                    _itemIconArray[i].SetData(rewardItem.category, rewardItem.id, rewardItem.num, isInfoPop: true);
                    _itemIconArray[i].SetClear(isCleard);
                }
            }

            private void SetupHighScore(bool hasHighScore, TrainingChallengeScore highScore, WorkTrainedCharaData.TrainedCharaData trainedCharaData)
            {
                _highScoreText.SetActiveWithCheck(hasHighScore);
                _highScoreCharaButton.SetActiveWithCheck(hasHighScore);
                _unchallengedText.SetActiveWithCheck(!hasHighScore);

                if (hasHighScore)
                {
                    var buttonData = CharacterButtonInfo.CreateTrained(trainedCharaData, dispRank: true, dispLongTapDetail: true);
                    buttonData.OnLongTap = button => 
                    {
                        OnCharaLongTapped?.Invoke(button);
                    };

                    // 明示的なフォント読み込み
                    _highScoreText.LoadFontAtHash(DialogHash);

                    _highScoreCharaButton.LoadTextureFastFunc = (path) => ResourceManager.LoadOnHash<Texture2D>(path, DialogHash);
                    _highScoreCharaButton.Setup(buttonData);
                    _highScoreText.text = highScore.ToString();
                }
            }

            private void SetupExamClearRate(List<(int score, bool isCread)> examClearScoreList)
            {
                if (examClearScoreList.Count != _clearRateItemArray.Length)
                {
                    Debug.LogWarning($"表示したい評価の数({examClearScoreList.Count})とクリアスコアが設定されている評価の数({_clearRateItemArray.Length})が違います");
                    return;
                }

                for (int i = 0; i < _clearRateItemArray.Length; i++)
                {
                    _clearRateItemArray[i].Setup(examClearScoreList[i].isCread, examClearScoreList[i].score);
                }
            }

            #endregion
        }
    }
}
