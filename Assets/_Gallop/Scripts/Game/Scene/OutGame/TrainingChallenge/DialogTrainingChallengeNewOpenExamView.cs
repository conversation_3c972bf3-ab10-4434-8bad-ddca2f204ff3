using UnityEngine;

namespace Gallop
{
    namespace TrainingChallenge
    {
        public class DialogTrainingChallengeNewOpenExamView : DialogInnerBase
        {
            #region class, 定数

            public const string PrefabPath = ResourcePath.OUTGAME_UI_PATH + "/TrainingChallenge/DialogTrainingChallengeNewOpenExamView";

            #endregion

            #region public変数

            /// <summary>
            /// ダイアログの形態（大きさなど）タイプ取得
            /// </summary>
            public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

            /// <summary>
            /// ダイアログの親オブジェクト位置タイプ取得
            /// </summary>
            public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

            #endregion

            #region private, protected変数

            [SerializeField] private TrainingChallengeExamListItemView _itemView;
            [SerializeField] private TextCommon _confirmText;

            #endregion

            #region publicメソッド

            public static DialogCommon.Data Create(ExamDataEntity entity, string message)
            {
                var component = LoadAndInstantiatePrefab<DialogTrainingChallengeNewOpenExamView>(PrefabPath);
                component.SetupContents(entity, message);
                var dialogData = component.CreateDialogData();
                return dialogData;
            }

            #endregion

            #region privateメソッド

            private void SetupContents(ExamDataEntity entity, string message)
            {
                // 解放された試験の設定
                _itemView.UpdateItem(entity);
                // 文言の設定
                _confirmText.text = message;
            }

            #endregion
        }
    }
}
