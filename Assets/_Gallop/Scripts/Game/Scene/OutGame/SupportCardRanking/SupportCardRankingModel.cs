
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    using TrainingChallenge;

    /// <summary>
    /// サポートランキング
    /// </summary>
    public class SupportCardRankingModel
    {
        #region class, 定数
        
        /// <summary>
        /// 初期化情報
        /// </summary>
        public class Param
        {
            public bool FirstAccessFlag = false;
        }

        /// <summary>
        /// ランキングの１サポートカード情報
        /// </summary>
        public class CardData
        {
            public WorkSupportCardData.SupportCardData WorkData;
            public string TexturePath;
            public string CardTitle;
            public string CardName;
            public int Percent; // 使用率(%)
        }

        /// <summary>
        /// 1クラス（試験）のデータを纏めたもの
        /// </summary>
        public class ClassData
        {
            public int ExamIndex;

            public string ExamName = string.Empty;

            public List<CardData> Cards = new List<CardData>();
        }

        #endregion

        #region public変数
        #endregion

        #region private, protected変数

        private int _defaultClassIndex;

        #endregion

        #region プロパティ


        /// <summary>
        /// メインロゴパス
        /// </summary>
        /// <remarks>
        /// 現在は一種類しかなく区別もしないので固定文言
        /// </remarks>
        public string LogoPath => ResourcePath.TRAINING_CHALLENGE_SUPPORT_CARD_RANKING_LOGO;

        /// <summary>
        /// シナリオロゴパス
        /// </summary>
        public string ScenarioLogoPath { get; private set; } = string.Empty;

        public List<ClassData> Classes { get; } = new List<ClassData>();

        /// <summary>
        /// 現在のクラスを返す
        /// </summary>
        public ClassData CurrentClass => Classes[CurrentClassIndex];

        /// <summary>
        /// 現在表示しているクラス
        /// </summary>
        public int CurrentClassIndex { get; private set; }

        public bool IsShowFirstClass => CurrentClassIndex == 0;

        public bool IsShowLastClass => CurrentClassIndex == (Classes.Count - 1);

        public bool IsAlone => Classes.Count == 1;

        public string ArrowFlyTextMessage => IsAlone ? TextId.TrainingChallenge4182017.Text() : string.Empty;

        /// <summary>
        /// 開催期間のテキスト
        /// </summary>
        public string IndicateTermText { get; private set; }

        /// <summary>
        /// 集計中の場合true
        /// </summary>
        public bool IsCounting { get; private set; }

        /// <summary>
        /// 結果表示中の場合true
        /// </summary>
        public bool IsResultTerm { get; private set; }
        
        /// <summary>
        /// (イベント開催ごとに)最初のアクセスかどうか
        /// </summary>
        public bool IsFirstAccess { get; private set; }

        #endregion

        #region publicメソッド

        public void SetParam(Param param)
        {
            var work = WorkDataManager.Instance.TrainingChallengeData;
            var masterManager = MasterDataManager.Instance;
            var supportCardMaster = masterManager.masterSupportCardData;

            IsFirstAccess = param.FirstAccessFlag;

            // 試験ごとにグループを作る
            var trainingChallengeMaster = MasterDataManager.Instance.masterTrainingChallengeMaster.CurrentEntity;
            var inResultTerm = trainingChallengeMaster.InResultTerm();

            var examIdList = new List<int>(trainingChallengeMaster.ActiveIds);
            if (inResultTerm)
            {
                // エクストラ・フリー課程は無条件で閲覧可能
                examIdList.Add(trainingChallengeMaster.ExExamId);
                examIdList.Add(trainingChallengeMaster.FreeExamId);
            }
            else
            {
                // エクストラ・フリー課程を開放状況に応じて追加
                var examInfoRepo = work.ExamInfoRepository;
                if (examInfoRepo.IsExamOpened(trainingChallengeMaster.ExExamId))
                {
                    examIdList.Add(trainingChallengeMaster.ExExamId);
                }
                if (examInfoRepo.IsExamOpened(trainingChallengeMaster.FreeExamId))
                {
                    examIdList.Add(trainingChallengeMaster.FreeExamId);
                }
            }

            IndicateTermText = trainingChallengeMaster.GetTermText();
            ScenarioLogoPath = ResourcePath.GetSingleModeScenarioTitleImagePath(trainingChallengeMaster.TargetMainScenario);

            Classes.Clear();

            // 試験が未選択か結果期間中なら１番目の試験、それ以外なら選択中の試験をデフォルト表示
            var defalutExamId =
                (inResultTerm || work.SelectedExamEntity == null) ? trainingChallengeMaster.ExamId1 : work.SelectedExamId;

            foreach (var examId in examIdList)
            {
                var tempExamMaster = trainingChallengeMaster.FindExamMaster(examId);
                if(tempExamMaster == null)
                {
                    continue;
                }

                var classData = new ClassData()
                {
                    ExamIndex = trainingChallengeMaster.GetIndex(examId),
                    ExamName = tempExamMaster.FeatureDefine.GetText(),
                };

                // 通信で貰えてる場合のみデータを詰める
                if (work.TryGetSupportRankingItems(work.FindSuportRankingExamIndex(examId), out var items))
                {
                    // データを詰める
                    foreach (var item in items)
                    {
                        var supportCardId = item.support_card_id;

                        var masterCard = supportCardMaster.Get(supportCardId);
                        var name = masterCard.Charaname;
                        classData.Cards.Add(new CardData
                        {
                            WorkData = new WorkSupportCardData.SupportCardData(supportCardId),
                            TexturePath = ResourcePath.GetSupportCardSTexturePath(supportCardId),
                            CardTitle = masterCard.Titlename,
                            CardName = name,
                            Percent = item.percentage
                        });
                    }
                }

                // デフォルト表示するランキングを設定する
                if (examId == defalutExamId)
                {
                    CurrentClassIndex = _defaultClassIndex = Classes.Count;
                }

                Classes.Add(classData);
            }
        }

        public void ResetClassIndex()
        {
            SetCalssIndex(_defaultClassIndex);
        }

        /// <summary>
        /// 表示クラスを切り替える
        /// </summary>
        public void SetCalssIndex(int index)
        {
            CurrentClassIndex = index;
        }

        /// <summary>
        /// 期間表示部分に表示するテキストを取得する
        /// </summary>
        public string GetTermText()
        {
            var naster = MasterDataManager.Instance.masterTrainingChallengeMaster;
            IsResultTerm = naster.Status == TrainingChallengeDefine.EventStatus.Result;

            // 集計中判定
            var work = WorkDataManager.Instance.TrainingChallengeData;
            IsCounting = work.IsSupportRankingCounting;

            // 結果表示中の場合は文言を変える
            if (IsResultTerm)
            {
                return TextId.TrainingChallenge4182012.Text();
            }

            // 集計中の場合は集計中文言にする
            if (IsCounting)
            {
                // 集計中文言にする
                return TextId.TrainingChallenge4180103.Text();
            }

            return IndicateTermText;
        }

        #endregion

        #region privateメソッド

        #endregion
    }
}
