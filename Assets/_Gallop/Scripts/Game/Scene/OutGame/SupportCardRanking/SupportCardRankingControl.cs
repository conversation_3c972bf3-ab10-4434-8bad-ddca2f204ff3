
using UnityEngine;
using System.Collections;
using System.Diagnostics;
using DG.Tweening;

namespace Gallop
{
    public class SupportCardRankingViewInfo : IViewInfo
    {
        public bool IsTransFromTop; // TOPからの遷移の場合true
    }

    /// <summary>
    /// サポートランキング
    /// </summary>
    public class SupportCardRankingControl : ViewControllerBase<SupportCardRankingView>
    {
        #region class, 定数
        #endregion

        #region public変数
        #endregion

        #region private, protected変数

        private SupportCardRankingModel _model = new SupportCardRankingModel();
        private bool _isTransFromTop;
        private Sequence _scrollSequence = null;

        #endregion

        #region プロパティ

        public SceneManager SceneManager => SceneManager.Instance;

        #endregion

        #region publicメソッド

        /// <summary>
        /// View構成情報の作成
        /// </summary>
        public static SceneDefine.ViewData<SupportCardRankingControl> CreateViewData() =>
            new SceneDefine.ViewData<SupportCardRankingControl>(
                    SceneDefine.SceneId.TrainingChallenge,
                     useBackButton: false,
                     bgId: SceneDefine.BgId.List,
                     bgmId: AudioId.BGM_TRAINING_CHALLENGE,
                     footerType: Footer.ButtonType.None,
                     headerTitle: TextId.TrainingChallenge4182001,
                     useFooter: true,
                     guideId: DialogTutorialGuide.TutorialGuideId.TrainingChallengeSupportCardRanking);

        public override IEnumerator PreRegisterDownload()
        {
            yield break;
        }

        public override void RegisterDownload(DownloadPathRegister register)
        {
            // ロゴ画像
            register.RegisterPath(_model.LogoPath);
            register.RegisterPath(_model.ScenarioLogoPath);

            // Font
            register.RegisterPath(ResourcePath.OUTGAME_RANKING_SCORE_FONT_PATH);

            base.RegisterDownload(register);
        }

        public override IEnumerator InitializeView()
        {
            yield break;
        }

        /// <summary>
        /// PlayInViewの前にViewに入るたびに呼び出される
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeEachPlayIn()
        {
            var info = GetViewInfo() as SupportCardRankingViewInfo;
            if (info != null)
            {
                _isTransFromTop = info.IsTransFromTop;
            }

            var work = WorkDataManager.Instance.TrainingChallengeData;
            var masterManager = MasterDataManager.Instance;
            var master = masterManager.masterTrainingChallengeMaster;

            var param = new SupportCardRankingModel.Param();
            
            // 必要に応じて通信を行う。
            // マスタ期間内じゃなくても通信を行う
            if (work.NeedsSupportRankingRequest || master.Status == TrainingChallenge.TrainingChallengeDefine.EventStatus.None)
            {
                yield return SupportCardRankingConnect.RequestGetRanking(res =>
                {
                    Debug_ApplyGetSupportCardRankingResponse(res);

                    work.UpdateSupportRankGroups(res.groups, res.is_counting);
                    
                    // 少なくとも起動後の初回遷移時に「初回アクセスフラグ」の更新をおこなえれば問題ない (起動後2回目以降の遷移は絶対に「初回アクセス」ではない)
                    param.FirstAccessFlag = res.first_access_flag;
                });
            }

            _model.SetParam(param);
            _model.ResetClassIndex();

            // サポカランキングに入りなおすたびに初期設定を行う
            var termText = _model.GetTermText();

            _view.Setup(_model.Classes, termText);
            _view.SetupScenario(_model.ScenarioLogoPath);
            _view.SetupLogo(_model.LogoPath);

            // デフォルトで表示されているランキングの設定
            _view.SelectWithoutNotify(_model.CurrentClassIndex);

            // ランキングが一つしかないときは矢印のアクティブを切る
            _view.SetInteractableAllArrowButton(!_model.IsAlone, _model.ArrowFlyTextMessage);

            yield break;
        }

        public override IEnumerator PlayInView()
        {
            var sequence = DOTween.Sequence();
            _view.BuildInAnimationSequence(sequence);

            sequence.Play();
            yield return sequence.WaitForCompletion();
        }

        public override IEnumerator PlayOutView()
        {
            // スクロール時のアニメーションが再生中だったら止める
            _scrollSequence?.Complete();

            var sequence = DOTween.Sequence();
            _view.BuildOutAnimationSequence(sequence);

            sequence.Play();
            yield return sequence.WaitForCompletion();
        }

        public override void BeginView()
        {
            _view.OnCardClicked = card => 
                {
                    // タップSE再生
                    AudioManager.Instance.PlaySe(AudioId.SFX_UI_DECIDE_M_01);

                    var data = new WorkSupportCardData.SupportCardData(card.WorkData.GetSupportCardId());
                    DialogSupportCardDetail.Open(data, enableMaxButton: true);
                };

            _view.OnFlickRight = () =>
                {
                    if (_model.IsAlone) return;

                    var classCount = _model.Classes.Count;
                    if (classCount > 0)
                    {
                        var nextIndex = (_model.CurrentClassIndex + classCount - 1) % classCount;
                        _model.SetCalssIndex(nextIndex);
                        SwitchAndFadeClass(_view.BuildScrollNextAnimation, _view.NextClassItem);
                    }
                };

            _view.OnFlickLeft = () =>
                {
                    if (_model.IsAlone) return;

                    var classCount = _model.Classes.Count;
                    if (classCount > 0)
                    {
                        var nextIndex = (_model.CurrentClassIndex + 1) % classCount;
                        _model.SetCalssIndex(nextIndex);
                        SwitchAndFadeClass(_view.BuildScrollPrevAnimation, _view.PrevClassItem);
                    }
                };

            // 開催ごとに、初回遷移時はチュートリアルガイドを表示
            if (_model.IsFirstAccess)
            {
                DialogTutorialGuide.PushDialog(DialogTutorialGuide.TutorialGuideId.TrainingChallengeSupportCardRanking, () =>
                    {
                    });
            }
            
        }

        /// <summary>
        /// 戻るボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {
            if (_isTransFromTop)
            {
                // TOPからの遷移の場合はそのまま戻す
                SceneManager.ChangeView(SceneDefine.ViewId.TrainingChallengeTop);
                return;
            }

            if (!SceneManager.Instance.BackUsingStack())
            {
                // スタックに何も積まれていない場合はTOPに戻る
                SceneManager.ChangeView(SceneDefine.ViewId.TrainingChallengeTop);
                return;
            }
        }

        /// <summary>
        /// OSのバックキー押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            // 戻るボタンと同じ挙動
            OnClickBackButton();
        }

        #endregion

        #region privateメソッド

        /// <summary>
        /// イリハケアニメ付きでクラスを切り替える
        /// </summary>
        private void SwitchAndFadeClass(System.Action<Sequence> buildAnimation, SupportCardRankingClassItemView nextClassItem)
        {
            // 再生中のクラス切り替えアニメがあったら強制終了
            _scrollSequence?.Complete();

            // イリハケアニメのビルド・再生
            _scrollSequence = DOTween.Sequence();
            buildAnimation?.Invoke(_scrollSequence);
            _scrollSequence.AppendCallback(() => _scrollSequence = null);
            _scrollSequence.Play();

            SwitchClass(nextClassItem);
        }

        /// <summary>
        /// クラスを切り替える
        /// </summary>
        private void SwitchClass(SupportCardRankingClassItemView nextClassItem)
        {
            nextClassItem.LoadScrollPosition();
            _view.CurrentClassItem.SaveScrollPosition();
            _view.SwitchClassItem(_model.CurrentClassIndex);
        }

        /// <summary>
        /// デバッグ
        /// </summary>
        [Conditional("CYG_DEBUG")]
        private void Debug_ApplyGetSupportCardRankingResponse(SupportCardRankingGetRankingResponse.CommonResponse res)
        {
#if CYG_DEBUG
            DebugPageTrainingChallenge.ApplyDebugSupportCardRankingResponse(res);
#endif
        }

        #endregion
    }
}
