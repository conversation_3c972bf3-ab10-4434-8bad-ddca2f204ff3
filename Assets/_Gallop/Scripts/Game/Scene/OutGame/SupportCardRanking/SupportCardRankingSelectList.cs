using System.Linq;
using UnityEngine;
namespace Gallop
{
    public class SupportCardRankingSelectList : CarouselScroll<SupportCardRankingClassItemView.ItemData>
    {
        #region class, 定数

        private const float SCROLL_CENTER_OFFSET = 0.5f;

        #endregion

        #region public変数
        #endregion

        #region private, protected変数
        #endregion

        #region プロパティ
        #endregion

        #region publicメソッド

        /// <summary>
        /// リストの中央に表示されているClassItemViewを取得する
        /// </summary>
        public SupportCardRankingClassItemView GetCurrentCenterItem()
        {
            // ObjectPoolから中心に表示されているオブジェクトを取得
            var obj = ObjectPool
                .OrderBy(item => Mathf.Abs(item.Position - SCROLL_CENTER_OFFSET))
                .First();

            return obj as SupportCardRankingClassItemView;
        }

        /// <summary>
        /// リストの中央から一つ右に表示されているClassItemViewを取得する
        /// </summary>
        public SupportCardRankingClassItemView GetNextItem()
        {
            var obj = ObjectPool
                .Where(item => item.Position < SCROLL_CENTER_OFFSET)
                .OrderBy(item => Mathf.Abs(item.Position - SCROLL_CENTER_OFFSET))
                .First();

            return obj as SupportCardRankingClassItemView;
        }

        /// <summary>
        /// リストの中央から一つ左に表示されているClassItemViewを取得する
        /// </summary>
        public SupportCardRankingClassItemView GetPrevItem()
        {
            var obj = ObjectPool
                .Where(item => item.Position > SCROLL_CENTER_OFFSET)
                .OrderBy(item => Mathf.Abs(item.Position - SCROLL_CENTER_OFFSET))
                .First();

            return obj as SupportCardRankingClassItemView;
        }

        public void ScrollToWithLock(int index)
        {
            UIManager.Instance.LockGameCanvas();
            ScrollTo(index, _tabScrollDuration, () =>
                {
                    UIManager.Instance.UnlockGameCanvas();
                });
        }
        #endregion

        #region privateメソッド
        #endregion
    }
}
