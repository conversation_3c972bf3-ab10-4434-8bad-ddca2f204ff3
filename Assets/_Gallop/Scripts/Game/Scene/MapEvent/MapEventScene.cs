using UnityEngine;

namespace Gallop
{
    public class MapEventScene : SceneBase
    {
        [SerializeField]
        private MapEventDirector _directorPrefab; // 3Dビュー用
        public MapEventDirector DirectorPrefab => _directorPrefab;
    }

    public class MapEventSceneController : SceneControllerBase<MapEventScene>
    {
        private MapEventDirector _directorInstance;

        /// <summary>
        /// Director生成
        /// </summary>
        public MapEventDirector CreateDirector()
        {
            if(_directorInstance != null)
            {
                return _directorInstance;
            }

            _directorInstance = UnityEngine.Object.Instantiate(_scene.DirectorPrefab);
            return _directorInstance;
        }

        public void DestroyDirector()
        {
            if (_directorInstance == null)
                return;

            Object.Destroy(_directorInstance.gameObject);
        }
    }
}
