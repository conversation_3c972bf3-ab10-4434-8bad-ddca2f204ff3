using Gallop.Model.Component;
namespace Gallop
{
    /// <summary>
    /// ミニキャラアクション
    /// </summary>
    public class CharacterActionPlayMotionSet : CharacterActionBase
    {
        /// <summary>
        /// このアクションを動作するのに必要な設定
        /// </summary>
        public class Context : ContextBase
        {
            public MasterMiniMotionSet.MiniMotionSet NowMotionSet { get; set; }
            public MasterMiniMotionSet.MiniMotionSet NextMotionSet { get; set; }
            public MasterCharacterSystemText.CharacterSystemText VoiceText { get; set; } = null;
            public MiniMotionSetController.MotionStartType StartType { get; set; }
            public System.Action OnEndAction{ get; set; }
            public float LipSyncDuration { get; set; }
            public bool IsCheckAnimEnd { get; set; }
            public bool IsForce { get; set; }
        }

        private Context _context;
        private bool _isPlayedFirstMotion = false;

        public CharacterActionPlayMotionSet(Context context) : base(context)
        {
            _actionType = CharacterActionDefine.CharaActionType.PlayMotion;
            _context = context;
        }

        /// <summary>
        /// 開始処理
        /// </summary>
        protected override bool EnterInternal(float deltaTime)
        {
            _context.ModelCtrl.RegisterEndMotionDelegate(OnMotionEndCallBack);

            if (_context.VoiceText != null)
            {
                AudioManager.Instance.PlaySystemVoice_MapEvent(_context.VoiceText);
                _context.NowMotionSet = MasterDataManager.Instance.masterMiniMotionSet.GetWithId(_context.VoiceText.MotionSet);
                _context.ModelCtrl.PlaySystemText(
                    _context.VoiceText,
                    _context.NextMotionSet,
                    _context.StartType,
                    0.0f // ブレンド設定があると前モーションの尺で差異がでるようなので、ボイス合わせ優先でブレンド値0にする
                );
                _context.ModelCtrl.FacialCtrl.IsOverrideMouthByLipSync = true;
            }
            else
            {
                _context.ModelCtrl.PlayMotion(
                    _context.NowMotionSet,
                    _context.NextMotionSet,
                    _context.StartType,
                    _context.IsForce
                );
            }

            return true;
        }

        protected override bool ExecuteInternal(float deltaTime)
        {
            if (!_context.IsCheckAnimEnd)
            {
                return true;
            }

            _elapsedSec += deltaTime;
            if (_context.ModelCtrl.FacialCtrl.IsOverrideMouthByLipSync)
            {
                if (_elapsedSec >= _context.LipSyncDuration)
                {
                    _context.ModelCtrl.FacialCtrl.IsOverrideMouthByLipSync = false;
                }
            }

            // コールバックで再生完了を受ける
            return _isPlayedFirstMotion;
        }

        protected override bool ExitInternal(float deltaTime)
        {
            if (_context.OnEndAction != null)
            {
                _context.OnEndAction();
            }
            _context.ModelCtrl.UnRegistEndMotionDelegate(OnMotionEndCallBack);
            _context.ModelCtrl.FacialCtrl.IsOverrideMouthByLipSync = false;
            return true;
        }

        public void OnMotionEndCallBack(MasterMiniMotionSet.MiniMotionSet charaMotionSet)
        {
            if (charaMotionSet == _context.NowMotionSet)
            {
                _isPlayedFirstMotion = true;
            }
        }
    }
}
