using System.Linq;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    // 関連オブジェクトのシリアライズ用
    public class MapEventBGPropHolder : MonoBehaviour
    {
        public enum PropType
        {
            LockNormalPoint = 0,
            LockNormalPointKey ,
            ClearNormalPoint,
            ClearNormalPointEffMesh,
            ClearNormalPointEffGlow,

            LockCheckPoint,
            LockCheckPointKey,
            ClearCheckPoint,
            ClearCheckPointEffMesh,
            ClearCheckPointEffGlow,

            LockNormalPointEffMesh,
            LockNormalPointEffGlow,
            LockCheckPointEffMesh,
            LockCheckPointEffGlow,

            Max
        }

        [SerializeField]
        private Transform[] _propArray;
        public Transform[] PropArray => _propArray;

        [SerializeField]
        private Transform _rootTrans = null;
        public Transform RootTrans => _rootTrans;

#if UNITY_EDITOR
            // NOTE :
            // 現状のインポートツール経由で背景を更新するとGameObjectは全て作り変えられ
            // シリアライズはMissingになるため
            // 再設定用のデータを仕込んでおく
            public Transform[] PropArrayEditor
            {
                get { return _propArray; }
                set { _propArray = value; }
            }

            public Transform RootTransEditor
            {
                get { return _rootTrans; }
                set { _rootTrans = value; }
            }

            [SerializeField]
            private string[] _propNameArray;
            public string[] PropNameArray => _propNameArray;
#endif


#if UNITY_EDITOR
        [CustomEditor(typeof(MapEventBGPropHolder), true)]
        [CanEditMultipleObjects]
        public class MapEventBGPropHolderInspector : Editor
        {
            public override void OnInspectorGUI()
            {
                base.OnInspectorGUI();
                var instance = target as MapEventBGPropHolder;

                if (instance)
                {
                    if (GUILayout.Button("シリアライズの再設定"))
                    {
                        instance.RootTransEditor = instance.gameObject.GetChildren().First()?.transform;

                        if (instance.RootTransEditor != null)
                        {
                            instance.PropArrayEditor = new Transform[instance.PropNameArray.Length];
                            for (int i = 0; i < instance.PropNameArray.Length; ++i)
                            {
                                instance.PropArrayEditor[i] = instance.RootTransEditor.Find(instance.PropNameArray[i])?.transform;
                            }
                        }
                    }
                }
            }
        }
#endif
    }
}
