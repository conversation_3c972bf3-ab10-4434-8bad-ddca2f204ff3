using System;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズ：ヒーロースキル獲得演出：結果表示
    /// アイテム有と無で切り替えできる
    /// </summary>
    [AddComponentMenu("")]
    public class PartsHeroesGetSkillCutResult : MonoBehaviour
    {
        // 最初のタップ待ち時間
        private const float TAP_WAIT = 0.2f;
        
        [Header("アイテム無し")]
        /// <summary> 表示オブジェクト全体（アイテム無し） </summary>
        [SerializeField] 
        private GameObject _rootWithoutItem = null;
        
        /// <summary> スキルアイコン（アイテム無し） </summary>
        [SerializeField] 
        private SkillIcon _skillIconWithoutItem = null;
        
        /// <summary> ヒーロースキル説明文（アイテム無し） </summary>
        [SerializeField]
        private TextCommon _skillDescWithoutItem = null;
        
        [Header("アイテム有り")]
        /// <summary> 表示オブジェクト全体（アイテム有り） </summary>
        [SerializeField] 
        private GameObject _rootWithItem = null;

        /// <summary> スキルアイコン（アイテム有り） </summary>
        [SerializeField] 
        private SkillIcon _skillIconWithItem = null;
        
        /// <summary> ヒーロースキル説明文（アイテム有り） </summary>
        [SerializeField]
        private TextCommon _skillDescWithItem = null;
        
        /// <summary> アイテムアイコン（アイテム有りのみ） </summary>
        [SerializeField] 
        private ItemIcon _itemIcon = null;
        
        [Header("共通")]
        /// <summary> 汎用TextFrame </summary>
        [SerializeField]
        private TextFrame _textFrame = null;
        
        private GameObject _paramChangeUI;
        
        /// <summary>
        /// このクラスを設定するのに必要な情報をまとめたモデル
        /// </summary>
        public class HeroSkillModel
        {
            /// <summary> スキルID </summary>
            public int SkillId { get; }

            /// <summary> スキル説明文 </summary>
            public string SkillDesc { get; }
            
            /// <summary> アイテムCategory </summary>
            public int ItemCategory { get; }
            
            /// <summary> アイテムID </summary>
            public int ItemId { get; }

            /// <summary> コンストラクタ </summary>
            public HeroSkillModel(int skillId, MasterHeroesSkillItem.HeroesItem item)
            {
                var skillData = MasterDataManager.Instance.masterSkillData.Get(skillId);
                SkillId = skillData.Id;
                SkillDesc = skillData.Remarks;
                ItemCategory = item.ItemCategory;
                ItemId = item.ItemId;
            }
        }

        public static void RegisterDownload(DownloadPathRegister register)
        {
            // ヒーロースキル獲得演出
            register.RegisterPathWithoutInfo(ResourcePath.HEROES_GET_HERO_SKILL_FLASH);
            // 会話ウインドウの矢印キャラカラー
            TextFrame.RegisterDownload(register);
        }

        /// <summary>
        /// 初期化
        /// 対応アイテム有り/無しで出し分ける
        /// </summary>
        public void SetupSkillInfo(HeroSkillModel model)
        {
            // 対応アイテム無し
            if (model.ItemId == HeroesDefine.INVALID_ITEM_ID || model.ItemCategory == HeroesDefine.INVALID_ITEM_CATEGORY)
            {
                // アイコンの設定
                _skillIconWithoutItem.Setup(model.SkillId, false);

                // スキル説明文
                _skillDescWithoutItem.text = model.SkillDesc;

                // 全体表示
                _rootWithoutItem.SetActiveWithCheck(true);
                _rootWithItem.SetActiveWithCheck(false);
            }
            // 対応アイテム有り
            else
            {
                // アイコンの設定
                _skillIconWithItem.Setup(model.SkillId,false);

                // スキル説明文
                _skillDescWithItem.text = model.SkillDesc;
                
                // アイテムの設定
                _itemIcon.SetData(model.ItemCategory, model.ItemId, 0);

                // 全体表示
                _rootWithoutItem.SetActiveWithCheck(false);
                _rootWithItem.SetActiveWithCheck(true);
            }
        }

        /// <summary>
        /// 継承の仕組みを使ったテキストとFlash表示
        /// </summary>
        public void PlayTextAndFlash(Action onComplete)
        {
            // 専用のパラメータを作成
            var parameterList = new List<TrainingParamChangeUI.ChangeParameterInfo>();
            var parameter = new TrainingParamChangeUI.ChangeParameterInfo();
            parameter.Type = TrainingParamChangeUI.ParameterType.HeroesGetSkill;
            parameterList.Add(parameter);

            _textFrame.TextLabel.enabled = false;
            _textFrame.SetNameLabelVisible(false);
            _textFrame.SetCursorActive(false);

            _paramChangeUI = TrainingParamChangeUI.Create(
                transform,
                _textFrame.TextLabel.GetComponent<RectTransform>(),
                _textFrame.GetComponent<Canvas>(),
                parameterList,
                () => onComplete?.Invoke(),
                0,
                TAP_WAIT,
                false,
                isSuccession: true,
                skipAddSingleModeLog: true,
                // #116102 ストーリーのオート設定を参照しないように強制タップ待ち設定
                forceTapWait:true);
        }

        /// <summary>
        /// 破棄・初期化周り
        /// </summary>
        public void OnExit()
        { 
            Destroy(_paramChangeUI);
        }
    }
}
