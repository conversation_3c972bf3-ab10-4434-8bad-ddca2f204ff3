using System;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// リーグオブヒーローズグランドリザルト画面3D用パラメータ
    /// </summary>
    [Serializable]
    [CreateAssetMenu(menuName = "ScriptableObject/Heroes/3DGrandResultParameterGroup")]
    public class Heroes3DGrandResultParameterGroup : ScriptableObject
    {
        [field: Header("ポジション")]
        public Heroes3DGrandResultCharacterParameter Character1;
        public Heroes3DGrandResultCharacterParameter Character2;
        public Heroes3DGrandResultCharacterParameter Character3;

        [field: Header("カメラ")]
        public Vector3 CameraPosition1;
        public Vector3 CameraRotation1;
        [Range(20f,150f)]
        public float CameraFov1;
        public Vector3 CameraPosition2;
        public Vector3 CameraRotation2;
        [Range(20f,150f)]
        public float CameraFov2;
        public Vector3 CameraPosition3;
        public Vector3 CameraRotation3;
        [Range(20f,150f)]
        public float CameraFov3;

        /// <summary>
        /// 味方3体分のキャラ情報をList形式で取得
        /// </summary>
        public List<Heroes3DGrandResultCharacterParameter> GetMyTeamParameterAsList()
        {
            return new List<Heroes3DGrandResultCharacterParameter>()
            {
                Character1,Character2,Character3,
            };
        }
        /// <summary>
        /// 味方3体分のカメラ情報をList形式で取得
        /// </summary>
        public List<Vector3> GetMyTeamCameraPositionAsList()
        {
            return new List<Vector3>()
            {
                CameraPosition1,CameraPosition2,CameraPosition3
            };
        }
        /// <summary>
        /// 味方3体分のカメラ情報をList形式で取得
        /// </summary>
        public List<Vector3> GetMyTeamCameraRotationAsList()
        {
            return new List<Vector3>()
            {
                CameraRotation1,CameraRotation2,CameraRotation3
            };
        }
        /// <summary>
        /// 味方3体分のカメラのFOVをList形式で取得
        /// </summary>
        public List<float> GetMyTeamCameraFovAsList()
        {
            return new List<float>()
            {
                CameraFov1,CameraFov2,CameraFov3
            };
        }

    }
}
