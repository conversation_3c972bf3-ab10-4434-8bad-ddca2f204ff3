using System;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using System.Linq;
#endif

namespace Gallop
{
    using static ModelLoader;
    /// <summary>
    /// リーグオブヒーローズのトップと拠点画面で利用するアセットのベースクラス
    /// </summary>
    [Serializable]
    public partial class Heroes3DCharacterAnimationParameterBase : ScriptableObject
    {
        #region 性格・走法別でアニメーションのフレーム指定
        [HideInInspector]
        public FrameParameter[] FrameParameterArray = new FrameParameter[] { };

        [Serializable]
        public class FrameParameter
        {
#if UNITY_EDITOR
            //_raceRunningTypeの変更により、表示するパラメータが変更するので、
            //設定中のデータを一時残すためのキャッシュ
            private int _debugInClipStartFrameBase;
            public int DebugInClipStartFrameBase => _debugInClipStartFrameBase;
            private int _debugInClipStartFramePitch;
            public int DebugInClipStartFramePitch => _debugInClipStartFramePitch;
            private int _debugInClipStartFrameStride;
            public int DebugInClipStartFrameStride => _debugInClipStartFrameStride;
#endif

            [SerializeField]
            private int _inClipStartFrame;
            public int InClipStartFrame => _inClipStartFrame;
            [SerializeField]
            private RacePersonalityType _racePersonalityType = RacePersonalityType.Bright;
            public RacePersonalityType RacePersonalityType => _racePersonalityType;
            [SerializeField]
            private RaceRunningType _raceRunningType = RaceRunningType.Base;
            public RaceRunningType RaceRunningType => _raceRunningType;

#if UNITY_EDITOR
            private bool _editorIsOpen = false;

            private bool _isAutoChangeStartFrameType = false;

            private bool _isAutoChangeStartFrameTypeInited = false;

            public void OnGUI(bool IsAutoChangeStartFrameType)
            {
                _isAutoChangeStartFrameType = IsAutoChangeStartFrameType;

                if(_isAutoChangeStartFrameType && _isAutoChangeStartFrameTypeInited == false)
                {
                    switch(_raceRunningType)
                    {
                        case RaceRunningType.Base:
                            _debugInClipStartFrameBase = _inClipStartFrame;
                            break;
                        case RaceRunningType.Pitch:
                            _debugInClipStartFramePitch = _inClipStartFrame;
                            break;
                        case RaceRunningType.Stride:
                            _debugInClipStartFrameStride = _inClipStartFrame;
                            break;
                    }
                    _isAutoChangeStartFrameTypeInited = true;
                }

                var isOpen = EditorGUILayout.Foldout(_editorIsOpen, $"anm_rac_type{((int)_racePersonalityType).ToString("D2")}_run{((int)_raceRunningType).ToString("D2")}_base  性格:{_racePersonalityType}・走法:{_raceRunningType}");

                if (isOpen != _editorIsOpen)
                {
                    _editorIsOpen = isOpen;
                }

                if (_editorIsOpen)
                {
                    EditorGUI.BeginChangeCheck();

                    EditorGUI.indentLevel++;
                    _racePersonalityType = (RacePersonalityType)EditorGUILayout.EnumPopup("Type", _racePersonalityType);
                    _raceRunningType = (RaceRunningType)EditorGUILayout.EnumPopup("走法", _raceRunningType);
                    if(IsAutoChangeStartFrameType)
                    {
                        switch (_raceRunningType)
                        {
                            case RaceRunningType.Base:
                                _debugInClipStartFrameBase = EditorGUILayout.IntField("InClipStartFrameBase", _debugInClipStartFrameBase);
                                break;
                            case RaceRunningType.Pitch:
                                _debugInClipStartFramePitch = EditorGUILayout.IntField("InClipStartFramePitch", _debugInClipStartFramePitch);
                                break;
                            case RaceRunningType.Stride:
                                _debugInClipStartFrameStride = EditorGUILayout.IntField("InClipStartFrameStride", _debugInClipStartFrameStride);
                                break;
                        }
                    }
                    else 
                    {
                        _inClipStartFrame = EditorGUILayout.IntField("InClipStartFrame", _inClipStartFrame);
                    }
                    EditorGUI.indentLevel--;
                    if (EditorGUI.EndChangeCheck())
                    {
                        if(IsAutoChangeStartFrameType)
                        {
                            OnEditorUpdateStartFrame();
                        }
                    }
                }
            }

            public void OnEditorUpdateStartFrame()
            {
                if(_isAutoChangeStartFrameType)
                {
                    _inClipStartFrame = _raceRunningType switch
                    {
                        RaceRunningType.Base => _debugInClipStartFrameBase,
                        RaceRunningType.Pitch => _debugInClipStartFramePitch,
                        RaceRunningType.Stride => _debugInClipStartFrameStride,
                        _ => 0,
                    };
                }
            }
#endif
        }
        #endregion

        #region 表情指定
        [HideInInspector, SerializeField]
        private FacePartsStringSet FacePartsInfo = FacePartsStringSet.Base();
        public FacePartsSet FacePartsSet => FacePartsSetUtil.ToFacePartsSet(FacePartsInfo);
        #endregion

        [HideInInspector, SerializeField]
        private bool IsEnableEyeTarget = false;
        public bool IsEnableEyeTargetGetter => IsEnableEyeTarget;

        [HideInInspector, SerializeField]
        private Vector2 EyeMoveRatio = Math.VECTOR2_ZERO;
        public Vector2 EyeMoveRationGetter => EyeMoveRatio;
    }

#if UNITY_EDITOR
    //Editor上でのみ実行するデバッグ処理をまとめる
    public partial class Heroes3DCharacterAnimationParameterBase
    {
        public FacePartsStringSet DebugFacePartsInfo => FacePartsInfo;

        public void DebugUpdateIsEnableEyeTarget(bool isEnableEyeTarget)
        {
            IsEnableEyeTarget = isEnableEyeTarget;
        }

        public void DebugUpdateEyeMoveRatio(Vector2 updateEyeMoveRatio)
        {
            EyeMoveRatio = updateEyeMoveRatio;
        }
    }

    [CustomEditor(typeof(Heroes3DCharacterAnimationParameterBase))]
    public class Heroes3DCharacterAnimationParameterBaseCustomEditor : Editor
    {
        protected bool _editorFacePartsSetIsOpen = false;

        protected Dictionary<FaceGroupType, bool> _faceGroupFoldOutState = new Dictionary<FaceGroupType, bool>()
        {
            { FaceGroupType.EyeL, false },
            { FaceGroupType.EyeR, false },
            { FaceGroupType.EyebrowL, false },
            { FaceGroupType.EyebrowR, false },
            { FaceGroupType.Mouth, false},
        };

        protected void CreateFaceParameter(FacePartsStringSet data)
        {
            var facePartsSet = FacePartsSetUtil.ToFacePartsSet(data);

            #region ローカル関数
            void CreateParameter(FaceGroupType faceGroupType)
            {
                var label = faceGroupType switch
                {
                    FaceGroupType.EyeL => "左目:",
                    FaceGroupType.EyeR => "右目:",
                    FaceGroupType.EyebrowL => "左眉:",
                    FaceGroupType.EyebrowR => "右眉:",
                    FaceGroupType.Mouth => "口:",
                    _ => ""
                };

                if (string.IsNullOrEmpty(label))
                {
                    return;
                }

                var isOpen = EditorGUILayout.Foldout(_faceGroupFoldOutState[faceGroupType], label);
                if (isOpen != _faceGroupFoldOutState[faceGroupType])
                {
                    _faceGroupFoldOutState[faceGroupType] = isOpen;
                }

                if (isOpen == false)
                {
                    return;
                }

                EditorGUI.indentLevel++;

                var facePartsList = new List<FaceParts>();

                FaceParts[] GetFacePartsArray()
                {
                    return faceGroupType switch
                    {
                        FaceGroupType.EyeL => facePartsSet._eyeL,
                        FaceGroupType.EyeR => facePartsSet._eyeR,
                        FaceGroupType.EyebrowL => facePartsSet._eyebrowL,
                        FaceGroupType.EyebrowR => facePartsSet._eyebrowR,
                        FaceGroupType.Mouth => facePartsSet._mouth,
                        _ => null,
                    };
                }

                var srcFacePartsArray = GetFacePartsArray();

                facePartsList.AddRange(srcFacePartsArray);

                int count = facePartsList.Count;
                if (count > 0)
                {
                    for (int i = 0; i < count; i++)
                    {
                        var index = i;
                        var faceParts = facePartsList[i];
                        GetFacePartsTypeGroupIndex(faceGroupType, faceParts._faceParts, out var groupIndex, out var faceIndex);

                        using (new EditorGUILayout.HorizontalScope())
                        {
                            var value = groupIndex;

                            groupIndex = EditorGUILayout.Popup("グループ: ", value, GetFacePartsTypeGroupName(faceGroupType));

                            if (groupIndex != value)
                            {
                                faceIndex = 0;
                                faceParts._faceParts = GetFaceParts(faceGroupType, groupIndex, faceIndex);
                                facePartsList[index] = faceParts;

                                UpdateFacePartsStringSet(faceGroupType, data, facePartsSet, facePartsList.ToArray());
                            }

                            //追加ボタン
                            if (index <= 0)
                            {
                                if (GUILayout.Button("+", GUILayout.Width(32f)))
                                {
                                    facePartsList.Add(new FaceParts(true));
                                    UpdateFacePartsStringSet(faceGroupType, data, facePartsSet, facePartsList.ToArray());
                                    return;
                                }
                            }
                            //削除ボタン
                            else
                            {
                                if (GUILayout.Button("-", GUILayout.Width(32f)))
                                {
                                    facePartsList.RemoveAt(index);
                                    UpdateFacePartsStringSet(faceGroupType, data, facePartsSet, facePartsList.ToArray());
                                    return;
                                }
                            }
                        }

                        {
                            var value = faceIndex;
                            faceIndex = EditorGUILayout.Popup("種類:", value, GetFacePartsTypeGroupPartsName(faceGroupType, groupIndex));
                            if (faceIndex != value)
                            {
                                faceParts._faceParts = GetFaceParts(faceGroupType, groupIndex, faceIndex);
                                facePartsList[index] = faceParts;
                                UpdateFacePartsStringSet(faceGroupType, data, facePartsSet, facePartsList.ToArray());
                            }
                        }

                        {
                            var result = faceParts._weight;
                            result = EditorGUILayout.FloatField("Weight", result);

                            if (faceParts._weight != result)
                            {
                                faceParts._weight = result;
                                facePartsList[index] = faceParts;
                                UpdateFacePartsStringSet(faceGroupType, data, facePartsSet, facePartsList.ToArray());
                            }
                        }

                    }
                }

                EditorGUI.indentLevel--;
            }

            void GetFacePartsTypeGroupIndex(FaceGroupType faceGroupType, int facePartsType, out int groupIndex, out int faceIndex)
            {
                groupIndex = 0;
                faceIndex = 0;

                switch (faceGroupType)
                {
                    case FaceGroupType.EyeL:
                    case FaceGroupType.EyeR:
                        DrivenKeyComponent.GetFaceEyeTypeGroupIndex(facePartsType, out groupIndex, out faceIndex);
                        break;
                    case FaceGroupType.EyebrowL:
                    case FaceGroupType.EyebrowR:
                        DrivenKeyComponent.GetFaceEyebrowTypeGroupIndex(facePartsType, out groupIndex, out faceIndex);
                        break;
                    case FaceGroupType.Mouth:
                        DrivenKeyComponent.GetFaceMouthTypeGroupIndex(facePartsType, out groupIndex, out faceIndex);
                        break;
                    default:
                        break;
                }
            }

            int GetFaceParts(FaceGroupType faceGroupType, int groupIndex, int faceIndex)
            {
                switch (faceGroupType)
                {
                    case FaceGroupType.EyeL:
                    case FaceGroupType.EyeR:
                        return DrivenKeyComponent.GetFaceEyeType(groupIndex, faceIndex);
                    case FaceGroupType.EyebrowL:
                    case FaceGroupType.EyebrowR:
                        return DrivenKeyComponent.GetFaceEyebrowType(groupIndex, faceIndex);
                    case FaceGroupType.Mouth:
                        return DrivenKeyComponent.GetFaceMouthType(groupIndex, faceIndex);
                    default:
                        break;
                }
                return 0;
            }

            string[] GetFacePartsTypeGroupName(FaceGroupType faceGroupType)
            {
                switch (faceGroupType)
                {
                    case FaceGroupType.EyeL:
                    case FaceGroupType.EyeR:
                        return DrivenKeyComponent.GetFaceEyeTypeGroupName();
                    case FaceGroupType.EyebrowL:
                    case FaceGroupType.EyebrowR:
                        return DrivenKeyComponent.GetFaceEyebrowTypeGroupName();
                    case FaceGroupType.Mouth:
                        return DrivenKeyComponent.GetFaceMouthTypeGroupName();
                    default:
                        break;
                }
                return null;
            }

            string[] GetFacePartsTypeGroupPartsName(FaceGroupType faceGroupType, int groupIndex)
            {
                switch (faceGroupType)
                {
                    case FaceGroupType.EyeL:
                    case FaceGroupType.EyeR:
                        return DrivenKeyComponent.GetFaceEyeTypeGroupPartsName(groupIndex);
                    case FaceGroupType.EyebrowL:
                    case FaceGroupType.EyebrowR:
                        return DrivenKeyComponent.GetFaceEyebrowTypeGroupPartsName(groupIndex);
                    case FaceGroupType.Mouth:
                        return DrivenKeyComponent.GetFaceMouthTypeGroupPartsName(groupIndex);
                    default:
                        break;
                }
                return null;
            }

            void UpdateFacePartsStringSet(FaceGroupType faceGroupType, FacePartsStringSet data, FacePartsSet facePartsSet, FaceParts[] updateFacePartsArray)
            {
                switch (faceGroupType)
                {
                    case FaceGroupType.EyeL:
                        facePartsSet._eyeL = updateFacePartsArray;
                        data._eyeL = FacePartsSetUtil.ToPartsString(facePartsSet._eyeL, FaceGroupSet.Eye);
                        break;
                    case FaceGroupType.EyeR:
                        facePartsSet._eyeR = updateFacePartsArray;
                        data._eyeR = FacePartsSetUtil.ToPartsString(facePartsSet._eyeR, FaceGroupSet.Eye);
                        break;
                    case FaceGroupType.EyebrowL:
                        facePartsSet._eyebrowL = updateFacePartsArray;
                        data._eyebrowL = FacePartsSetUtil.ToPartsString(facePartsSet._eyebrowL, FaceGroupSet.Eyebrow);
                        break;
                    case FaceGroupType.EyebrowR:
                        facePartsSet._eyebrowR = updateFacePartsArray;
                        data._eyebrowR = FacePartsSetUtil.ToPartsString(facePartsSet._eyebrowR, FaceGroupSet.Eyebrow);
                        break;
                    case FaceGroupType.Mouth:
                        facePartsSet._mouth = updateFacePartsArray;
                        data._mouth = FacePartsSetUtil.ToPartsString(facePartsSet._mouth, FaceGroupSet.Mouth);
                        break;
                }
            }

            #endregion


            var isOpen = EditorGUILayout.Foldout(_editorFacePartsSetIsOpen, "表情設定");

            if (isOpen != _editorFacePartsSetIsOpen)
            {
                _editorFacePartsSetIsOpen = isOpen;
            }

            if(isOpen == false)
            {
                return;
            }

            EditorGUI.indentLevel++;

            CreateParameter(FaceGroupType.EyeL);
            EditorGUILayout.Space(5);
            CreateParameter(FaceGroupType.EyeR);
            EditorGUILayout.Space(5);
            CreateParameter(FaceGroupType.EyebrowL);
            EditorGUILayout.Space(5);
            CreateParameter(FaceGroupType.EyebrowR);
            EditorGUILayout.Space(5);
            CreateParameter(FaceGroupType.Mouth);

            EditorGUI.indentLevel--;
        }

        protected bool _editorEyeMoveParamaterIsOpen = false;

        protected void CreateEyeMoveParamater(Heroes3DCharacterAnimationParameterBase data)
        {
            var isOpen = EditorGUILayout.Foldout(_editorEyeMoveParamaterIsOpen, "目線設定");
            if(isOpen != _editorEyeMoveParamaterIsOpen)
            {
                _editorEyeMoveParamaterIsOpen = isOpen;
            }

            if(isOpen == false)
            {
                return;
            }

            EditorGUI.indentLevel++;

            var isEnableEyeTarget = EditorGUILayout.Toggle("目線調整オン", data.IsEnableEyeTargetGetter);
            if(isEnableEyeTarget != data.IsEnableEyeTargetGetter)
            {
                data.DebugUpdateIsEnableEyeTarget(isEnableEyeTarget);
            }

            if(data.IsEnableEyeTargetGetter)
            {
                EditorGUI.BeginChangeCheck();

                const float SLIDER_LEFT_VALUE = -1f;
                const float SLIDER_RIGHT_VALUE = 1f;

                var x = data.EyeMoveRationGetter.x;
                var y = data.EyeMoveRationGetter.y;

                x = EditorGUILayout.Slider("X", x, SLIDER_LEFT_VALUE, SLIDER_RIGHT_VALUE);
                y = EditorGUILayout.Slider("Y", y, SLIDER_LEFT_VALUE, SLIDER_RIGHT_VALUE);

                if(EditorGUI.EndChangeCheck())
                {
                    data.DebugUpdateEyeMoveRatio(new Vector2(x, y));
                }
            }

            EditorGUI.indentLevel--;
        }

        protected void CreateSaveButton(Heroes3DCharacterAnimationParameterBase data, Action onSaved = null)
        {
            if (GUILayout.Button("保存"))
            {
                var errorMsg = OnSaveCheckError(data);
                if (string.IsNullOrEmpty(errorMsg) == false)
                {
                    EditorUtility.DisplayDialog("エラー", errorMsg, "OK");
                    return;
                }

                //編集時StartFrameの上書きをやっているが、念のため保存直前にも更新をかけておく
                foreach (var frameParam in data.FrameParameterArray)
                {
                    frameParam.OnEditorUpdateStartFrame();
                }

                EditorUtility.SetDirty(data);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                onSaved?.Invoke();
            }
        }

        protected virtual string OnSaveCheckError(Heroes3DCharacterAnimationParameterBase data)
        {
            var errorStringBuilder = new System.Text.StringBuilder();

            #region 走法別クリップオフセット配列チェック
            var ignoreList = new List<(RacePersonalityType personality, RaceRunningType runningType)>();

            foreach (var checkData in data.FrameParameterArray)
            {
                if (ignoreList.Any(x => x.personality == checkData.RacePersonalityType && x.runningType == checkData.RaceRunningType))
                {
                    continue;
                }

                if (data.FrameParameterArray.Count(x => x.RacePersonalityType == checkData.RacePersonalityType && x.RaceRunningType == checkData.RaceRunningType) > 1)
                {
                    errorStringBuilder.AppendLine($"重複設定されたデータがあります、チェックしてください！　該当性格: {checkData.RacePersonalityType}; 該当走法: {checkData.RaceRunningType}");
                    ignoreList.Add((checkData.RacePersonalityType, checkData.RaceRunningType));
                }
            }
            #endregion

            if (errorStringBuilder.Length > 0)
            {
                errorStringBuilder.AppendLine();
                errorStringBuilder.AppendLine("保存ができませんでした！");
            }

            return errorStringBuilder.ToString();
        }
    }
#endif
}
