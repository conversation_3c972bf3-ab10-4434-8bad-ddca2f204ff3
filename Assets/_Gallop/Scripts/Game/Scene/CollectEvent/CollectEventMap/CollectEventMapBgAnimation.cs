using UnityEngine;

namespace Gallop
{
    /// <summary>
    ///収集イベント：イベントマップの背景アニメーション
    /// </summary>
    public class CollectEventMapBgAnimation
    {
        private const int CHARA_COUNT = 3;

        private enum SortOrder
        {
            InsideTrack = 10,    //馬場内のイルミネーション
            OutsideTrack = 11,   //コース外のイルミネーション
            Fountain = 12,       //噴水
            Chara = 150,          //キャラ
            Laser = 160,          //レーザーライト
        }

        private enum AnimationType
        {
            Fountain = 0,           //噴水
            LaserLight = 1,         // レーザーライト
            HorseShoeEffect = 2,    //蹄鉄エフェクト
            TreeEffect = 3,         //ツリーのエフェクト
            PotLight = 4,           //ポットのライト
            Stream = 5,             //清流イルミネーション
            RiceField = 6,          //田園イルミネーション
            Tunnel = 7,             //桜トンネルイルミネーション
            Flower = 8,             //藤棚
            RoofLight = 9,          //屋根のイルミネーション
            ShopRoof = 10,          //券売所の屋根
            Chara1 = 11,
            Chara2 = 12,
            Chara3 = 13,

            Max,
        }

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_BG_FOUNTAIN_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_BG_LIGHT_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_HORSESHOE_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_TREE_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_POT_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_STREAM_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_RICEFELD_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_TUNNEL_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_FLOWER_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_ROOF_EFFECT);
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_EVENT_MAP_BG_SHOP_ROOF_FLASH);

            for (int i = 0; i < CHARA_COUNT; i++)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetCollectEventMapBgCharaAnimation(i));
            }
        }

        public void Initialize(ResourceManager.ResourceHash hash, Transform[] animationParent)
        {
            if (animationParent.Length < (int)AnimationType.Max)
            {
                Debug.LogError("アニメーションの親Transformの数が不足");
                return;
            }

            CreateFountainAnimation(hash, animationParent[(int)AnimationType.Fountain]);
            CreateLaserLightAnimation(hash, animationParent[(int)AnimationType.LaserLight]);
            CreateHorseShoeEffect(hash, animationParent[(int)AnimationType.HorseShoeEffect]);
            CreateTreeEffect(hash, animationParent[(int)AnimationType.TreeEffect]);
            CreatePotLightEffect(hash, animationParent[(int)AnimationType.PotLight]);
            CreateStreamLightEffect(hash, animationParent[(int)AnimationType.Stream]);
            CreateRiceFieldLightEffect(hash, animationParent[(int)AnimationType.RiceField]);
            CreateTunnelLightEffect(hash, animationParent[(int)AnimationType.Tunnel]);
            CreateFlowerLightEffect(hash, animationParent[(int)AnimationType.Flower]);
            CreateRoofLightEffect(hash, animationParent[(int)AnimationType.RoofLight]);
            CreateShopRoofAnimation(hash, animationParent[(int)AnimationType.ShopRoof]);

            for (var i = 0; i < CHARA_COUNT; i++)
            {
                CreateCharaAnimation(i, hash, animationParent[(int)AnimationType.Chara1 + i]);
            }
        }

        private void CreateFountainAnimation(ResourceManager.ResourceHash hash, Transform parent)
        {
            // Flash生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_BG_FOUNTAIN_FLASH_PATH, hash);
            var flashActionPlayer = GameObject.Instantiate(prefab, parent).GetComponent<FlashActionPlayer>();
            flashActionPlayer.LoadFlashPlayer();

            // ソート順設定
            flashActionPlayer.SetSortOffset((int)SortOrder.Fountain);

            flashActionPlayer.Play(GameDefine.A2U_IN_LABEL);
        }

        private void CreateLaserLightAnimation(ResourceManager.ResourceHash hash, Transform parent)
        {
            // Flash生成
            var flash = FlashLoader.LoadOnHash(ResourcePath.COLLECT_EVENT_MAP_BG_LIGHT_FLASH_PATH, parent, hash: hash);

            // ソート順設定
            flash.SetLayer(parent.gameObject.layer);
            flash.SortLayer = UIManager.CANVAS_SORTING_LAYER_UI;
            flash.SortOffset = (int)SortOrder.Laser;

            flash.Play(GameDefine.A2U_IN_LABEL);
        }

        private void CreateHorseShoeEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_HORSESHOE_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.OutsideTrack);
        }

        private void CreateTreeEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_TREE_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.OutsideTrack);
        }

        private void CreatePotLightEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_POT_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.OutsideTrack);
        }

        private void CreateStreamLightEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_STREAM_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.InsideTrack);
        }

        private void CreateRiceFieldLightEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_RICEFELD_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.InsideTrack);
        }

        private void CreateTunnelLightEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_TUNNEL_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.InsideTrack);
        }

        private void CreateFlowerLightEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_FLOWER_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.InsideTrack);
        }

        private void CreateRoofLightEffect(ResourceManager.ResourceHash hash, Transform parent)
        {
            // パーティクル生成
            var prefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_EVENT_MAP_ROOF_EFFECT, hash);
            var perticle = GameObject.Instantiate(prefab, parent);
            UIUtil.SetParticleSortOrder(perticle, UIManager.CANVAS_SORTING_LAYER_UI, (int)SortOrder.InsideTrack);
        }

        private void CreateShopRoofAnimation(ResourceManager.ResourceHash hash, Transform parent)
        {
            // Flash生成
            var flash = FlashLoader.LoadOnHash(ResourcePath.COLLECT_EVENT_MAP_BG_SHOP_ROOF_FLASH, parent, hash: hash);

            // ソート順設定
            flash.SetLayer(parent.gameObject.layer);
            flash.SortLayer = UIManager.CANVAS_SORTING_LAYER_UI;
            flash.SortOffset = (int)SortOrder.OutsideTrack;

            flash.Play(GameDefine.A2U_IN_LABEL);
        }

        private void CreateCharaAnimation(int index, ResourceManager.ResourceHash hash, Transform parent)
        {
            // Flash生成
            var flash = FlashLoader.LoadOnHash(ResourcePath.GetCollectEventMapBgCharaAnimation(index), parent, hash: hash);

            // ソート順設定
            flash.SetLayer(parent.gameObject.layer);
            flash.SortLayer = UIManager.CANVAS_SORTING_LAYER_UI;
            flash.SortOffset = (int)SortOrder.Chara;

            flash.Play(GameDefine.A2U_IN_LABEL);
        }
    }

}