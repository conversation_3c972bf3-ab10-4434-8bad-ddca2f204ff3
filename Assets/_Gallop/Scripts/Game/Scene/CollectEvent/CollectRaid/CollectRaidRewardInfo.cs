using CodeStage.AntiCheat.ObscuredTypes;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 収集イベント : 報酬情報
    /// </summary>
    public class CollectRaidRewardInfo
    {
        /// <summary>
        /// 個人・全体報酬　クリア状況
        /// </summary>
        public enum RewardStateType
        {
            NotClear = 0,   // 未クリア
            CanReceive,     // 個人報酬：報酬受け取り可能
            NeedNotice,     // 全体報酬：達成演出再生可能
            GotReward       // 個人報酬：報酬受け取り済み 全体報酬：演出再生済み
        }
        
        /// <summary>
        /// 報酬タイプ
        /// </summary>
        public enum RewardType
        {
            Individual,
            General
        }

        /// <summary> 報酬タイプ </summary>
        public RewardType Type { get; private set; }

        /// <summary> 報酬Id </summary>
        public int RewardId => _rewardId;
        private readonly ObscuredInt _rewardId;

        /// <summary> 報酬アイテムカテゴリ </summary>
        public GameDefine.ItemCategory ItemCategory { get; }

        /// <summary> 報酬アイテムId </summary>
        public int ItemId => _itemId;
        private readonly ObscuredInt _itemId;

        /// <summary> 報酬アイテムの個数 </summary>
        public int ItemNum => _itemNum;
        private readonly ObscuredInt _itemNum;

        /// <summary> 獲得条件(収集数) </summary>
        public long CollectItemNum => _collectItemNum;
        private readonly ObscuredLong _collectItemNum;

        /// <summary> 達成状況 </summary>
        public RewardStateType RewardState { get; private set; }

        /// <summary>
        /// コンストラクタ:個人報酬
        /// </summary>
        public CollectRaidRewardInfo(MasterCollectRaidIndividualReward.CollectRaidIndividualReward reward, RewardStateType state)
        {
            Type = RewardType.Individual;
            _rewardId = reward.Id;
            ItemCategory = (GameDefine.ItemCategory)reward.ItemCategory;
            _itemId = reward.ItemId;
            _itemNum = reward.ItemNum;
            _collectItemNum = reward.IndividualCollectItemNum;
            RewardState = state;
        }
        
        /// <summary>
        /// コンストラクタ:全体報酬
        /// </summary>
        public CollectRaidRewardInfo(MasterCollectRaidAllReward.CollectRaidAllReward reward, RewardStateType state)
        {
            Type = RewardType.General;
            _rewardId = reward.Id;
            ItemCategory = (GameDefine.ItemCategory)reward.ItemCategory;
            _itemId = reward.ItemId;
            _itemNum = reward.ItemNum;
            _collectItemNum = reward.AllCollectItemNum;
            RewardState = state;
        }

        /// <summary>
        /// 状態更新
        /// </summary>
        public void UpdateRewardState(RewardStateType state)
        {
            RewardState = state;
        }
    }
}
