using System.Collections.Generic;
using AnimateToUnity;
using UnityEngine;
using System;
using System.Linq;
using DG.Tweening;

namespace Gallop
{
    using AnimSequenceHelper;
    
    /// <summary>
    /// 収集イベント：結果発表・全体報酬獲得条件達成ダイアログ
    /// </summary>
    public class DialogCollectRaidReceiveGeneralReward : DialogInnerBase
    {
        #region Const
        
        // ラベル
        private const string LABEL_IN_RESULT_TITLE = "in_result";
        private const string LABEL_OUT_RESULT_TITLE = "out_result";
        
        private const string LABEL_IN = "in";
        private const string LABEL_IN_RESULT = "in2";
        private const string LABEL_IN_CHARA = "in_chara";
        private const string LABEL_IN_CHARA_RESULT = "in_chara2";
        private const string LABEL_IN_COLLECT_ITEM = "in_ornament";
        private const string LABEL_IN_COLLECT_ITEM_RESULT = "in_ornament2";
        private const string LABEL_IN_EVENT = "in_event";
        private const string LABEL_IN_REWARD = "in_reward";
        private const string LABEL_IN_COMP = "in_comp2";
        private const string LABEL_OUT = "out";
        private const string LABEL_OUT_RESULT = "out2";
        private const string LABEL_OUT_COMP = "out_comp2";

        private const string NUM_EFFECT_NUM_OBJECT = "utx_count_number_00";
        
        /// <summary> 報酬の数でフレームを変更 </summary>
        private const int REWARD_NUM_USE_SMALL_FRAME = 4;
        public int RewardNumUseSmallFrame => REWARD_NUM_USE_SMALL_FRAME;

        /// <summary>
        /// 描画順制御
        /// </summary>
        /// <remarks>値は10000以内で設定すること</remarks>
        private enum SortOrder
        {
            BaseFlash = 0,
            MiniCharaBgEffect = 100,
            MiniChara = 150,
            TitleFlash = 200,
            CollectItemNum = 250,
            Effect = 300,
            UI = 400,
            ItemIcon = 401,
        }
        
        /// <summary>
        /// 演出の段階
        /// </summary>
        public enum State
        {
            CollectResult = 0,  // 最終集計結果発表
            RewardList = 1,     // 全体報酬達成演出
            Complete = 2,       // 全体報酬コンプリート演出
        }

        private const int MINI_DIRECTOR_BG_ID = 60001;

        #endregion
        
        #region SerializeField
        
        [SerializeField] private GameObject _frameRoot;
        
        [SerializeField] private GameObject _flashRoot;
        
        [SerializeField] private GameObject _titleRoot;
        
        /// <summary> UIをFlashより上に置く用Canvas </summary>
        [SerializeField] private Canvas _flashOverlayCanvas;
        
        /// <summary> 文字エフェクト用Canvas </summary>
        [SerializeField] private Canvas _itemNumCanvas;
        
        /// <summary> キャラエフェクト用Canvas </summary>
        [SerializeField] private Canvas _charaEffectCanvas;
        
        /// <summary> キャラをエフェクトより下にする用Canvas </summary>
        [SerializeField] private Canvas _miniCharaCanvas;
        
        /// <summary> イベント報酬ボタン </summary>
        [SerializeField] private ButtonCommon _eventRewardButton;
        
        /// <summary> 次のイベント報酬までのアイテム数 </summary>
        [SerializeField] private GameObject _nextRewardTargetRoot;
        
        /// <summary> 次のイベント報酬までのアイテム数 </summary>
        [SerializeField] private TextCommon _nextRewardTargetText;
        
        /// <summary> 報酬一覧 </summary>
        [SerializeField] private PartsFanRaidGetRewardItemList _partsRewardItemList;

        /// <summary> ダイアログ閉じるボタン </summary>
        [SerializeField] private ButtonCommon _closeButton;
        
        /// <summary> 報酬情報のルート </summary>
        [SerializeField] private RectTransform _rewardInfoRoot;
        
        /// <summary> テキストのルート </summary>
        [SerializeField] private RectTransform _footerTextRoot;
        
        /// <summary>
        /// StateごとのUIのルート
        /// <remarks>  enumの順番に対応するように参照を貼ること </remarks>
        /// </summary>
        [SerializeField] private GameObject[] _stateUIObjectArray;

        /// <summary>
        /// ミニキャラ表示用
        /// </summary>
        [SerializeField] private RawImageCommon _miniCharaImage;


        #endregion

        #region Member

        private DialogCommon _dialog;
        private List<int> _rewardIdList;
        private bool _isShowCollectResult = false;
        private bool _isGotAllReward = false;
        private Action _onClose = null;
        private List<GameObject> _numEffectObjList = new List<GameObject>();

        private FlashPlayer _baseFlash = null;
        private FlashPlayer _titleFlash = null;
        private FlashActionPlayer _titleFlashAction = null;
        private Animator _numEffectAnimator = null;
        private ParticleSystem _miniCharaEffectParticle = null;
        private ParticleSystem _completeEffectParticle = null;
        private AnimationFrameCross _animationFrameCross = null;

        private AnObject _breakTextObject = null;
        
        private State _state;

        /// <summary>
        /// 演出再生Director
        /// </summary>
        private MiniDirector _miniDirector;
        private bool _isPlayingDirector = false;

        /// <summary>
        /// 描画先のテクスチャ（UIに張り付けるTexture）
        /// </summary>
        private RenderTexture _texture = null;

        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.WITHOUT_FRAME;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion
        
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            var eventId = WorkDataManager.Instance.CollectRaidData.EventId;
            
            register.RegisterPathWithoutInfo(ResourcePath.COLLECT_RAID_COLLECT_RESULT_TITLE_FLASH_ACTION);
            register.RegisterPathWithoutInfo(ResourcePath.FAN_RAID_GET_ALL_FAN_REWARD_TITLE_FLASH);
            register.RegisterPathWithoutInfo(TextUtil.Format(ResourcePath.COLLECT_RAID_RECEIVE_GENERAL_REWARD_FLASH, eventId));
            AnimationFrameCross.RegisterDownload(register);
            register.RegisterPathWithoutInfo(ResourcePath.GetCollectRaidDialogReceiveGeneralRewardMiniCharaBgEffect(eventId));
            register.RegisterPathWithoutInfo(ResourcePath.GetCollectRaidDialogReceiveGeneralRewardNumEffect(eventId));
            register.RegisterPathWithoutInfo(ResourcePath.GetCollectRaidDialogReceiveGeneralRewardCompEffect(eventId));
            ItemIcon.RegisterPath(register);

            MiniDirector.RegisterDownload(register);

            // SE
            AudioManager.Instance.RegisterDownloadByAudioIds(register, new List<AudioId>
            {
                AudioId.SFX_FAN_RAID_GET_ALL_REWARD,
                AudioId.SFX_FAN_RAID_GET_ALL_REWARD_COMP,
                AudioId.SFX_COLLECT_RAID_TOTAL_RESULT,
            });
        }

        /// <summary>
        /// 結果発表・全体報酬獲得条件達成ダイアログを開く
        /// </summary>
        /// <param name="rewardIdList"></param>
        /// <param name="isShowCollectResult">結果発表演出を入れるかどうか</param>
        /// <param name="isGotAllReward">コンプリート演出が必要かどうか</param>
        /// <param name="onClose"></param>
        public static void Open(List<int> rewardIdList, bool isShowCollectResult, bool isGotAllReward, Action onClose = null)
        {
            var component = LoadAndInstantiatePrefab<DialogCollectRaidReceiveGeneralReward>(ResourcePath.COLLECT_RAID_RECEIVE_GENERAL_REWARD_DIALOG);
            var dialogData = component.CreateDialogData();

            dialogData.AutoClose = false;
            dialogData.CancelSe = AudioId.INVALID;
            dialogData.DestroyCallBack = component.OnDestroyDialog;
            dialogData.OnPushBackKey = component.OnPushBackKey;
            dialogData.OnChangeSortingOrder = component.OnChangeSortingOrder;

            component._dialog = DialogManager.PushDialog(dialogData);
            component.Setup(rewardIdList, isShowCollectResult, isGotAllReward, onClose);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(List<int> rewardIdList, bool isShowCollectResult, bool isGotAllReward, Action onClose = null)
        {
            _rewardIdList = rewardIdList;
            _isShowCollectResult = isShowCollectResult;
            _isGotAllReward = isGotAllReward;
            _onClose = onClose;
            _state = isShowCollectResult ? State.CollectResult : State.RewardList; // どこから演出を始めるか

            var eventId = WorkDataManager.Instance.CollectRaidData.EventId;

            // Flash
            InitTitleFlash();
            InitBaseFlash();
            
            // アイテム数
            InitItemNum();
            
            // ミニキャラ裏エフェクト
            var miniEffect = ResourceManager.LoadOnHash<GameObject>(ResourcePath.GetCollectRaidDialogReceiveGeneralRewardMiniCharaBgEffect(eventId), DialogHash);
            _miniCharaEffectParticle = Instantiate(miniEffect, _charaEffectCanvas.transform).GetComponentInChildren<ParticleSystem>();
            _miniCharaEffectParticle.gameObject.SetLayerRecursively(_dialog.gameObject.layer);
            
            if (_isGotAllReward)
            {
                // コンプリート演出エフェクト
                var compEffect = ResourceManager.LoadOnHash<GameObject>(ResourcePath.GetCollectRaidDialogReceiveGeneralRewardCompEffect(eventId), DialogHash);
                _completeEffectParticle = Instantiate(compEffect, _charaEffectCanvas.transform).GetComponentInChildren<ParticleSystem>();
                _completeEffectParticle.gameObject.SetLayerRecursively(_dialog.gameObject.layer);
            }
            
            // ソートの設定(Flashなどを初期化してから呼ぶ)
            UpdateSortOrderAll(_dialog.GetSortingOrder(), _dialog.ParentCanvasSortingLayerName);

            // ボタン
            _closeButton.SetOnClick(Next);
            _eventRewardButton.SetOnClick(OnClickRewardButton);

            // ミニキャラ
            InitMiniChara();
            
            // 報酬アイテム
            InitRewardItemInfo();

            // ダイアログを開く
            PlayInDialog();
        }
        
                
        /// <summary>
        /// タイトル部分に表示するFlashの初期化
        /// </summary>
        private void InitTitleFlash()
        {
            // 集計結果発表はFlashActionPlayer, 報酬達成演出はFlashPlayer
            switch (_state)
            {
                case State.CollectResult:
                    var resultTitlePrefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COLLECT_RAID_COLLECT_RESULT_TITLE_FLASH_ACTION, DialogHash);
                    var obj = Instantiate(resultTitlePrefab, _titleRoot.transform);
                    _titleFlashAction = obj.GetComponent<FlashActionPlayer>();
                    _titleFlashAction.LoadFlashPlayer();
                    _titleFlashAction.FlashPlayer.SetLayer(_flashRoot.gameObject.layer);
                    break;
                case State.RewardList:
                    _titleFlash = FlashLoader.LoadOnHash(ResourcePath.FAN_RAID_GET_ALL_FAN_REWARD_TITLE_FLASH, _titleRoot.transform, hash: DialogHash);
                    _titleFlash.SetLayer(_flashRoot.gameObject.layer);
                    _titleFlash.Init();
                    break;
            }
        }

        /// <summary>
        /// Flashを初期化
        /// </summary>
        private void InitBaseFlash()
        {
            int eventId = WorkDataManager.Instance.CollectRaidData.EventId;
            var flashName = TextUtil.Format(ResourcePath.COLLECT_RAID_RECEIVE_GENERAL_REWARD_FLASH, eventId);
            _baseFlash = FlashLoader.LoadOnHash(flashName, _flashRoot.transform, hash: DialogHash);
            _baseFlash.SetLayer(_flashRoot.gameObject.layer);
            _baseFlash.Init();
            
            // 「突破！」文字
            const string BREAK_TEXT_OBJ_NAME = "OBJ_mc_txt_break00";
            _breakTextObject = _baseFlash.GetObj(BREAK_TEXT_OBJ_NAME);

            // みんなで集めた○○の画像を置き換える
            const string COLLECT_EVERYONE_OBJ_NAME = "OBJ_mc_collect00";
            const string COLLECT_EVERYONE_PLN_NAME = "PLN_dum_collect00";
            var path = ResourcePath.GetCollectRaidGeneralRewardDialogTextTexturePath(eventId);
            var texture = ResourceManager.LoadOnHash<Texture>(path, DialogHash);
            var collectEveryoneObject = _baseFlash.GetObj(COLLECT_EVERYONE_OBJ_NAME);
            _baseFlash.SetTexture(COLLECT_EVERYONE_PLN_NAME, texture, rootGameObject: collectEveryoneObject.GameObject);
        }

        /// <summary>
        /// 収集アイテム数を初期化
        /// </summary>
        private void InitItemNum()
        {
            // ベースのFlash内のロケーターの下にアイテム数のcanvasを移動
            const string LOCATOR_ITEM_NUM_NAME = "OBJ_loc_number00";
            var locator = _baseFlash.GetObj(LOCATOR_ITEM_NUM_NAME);
            var itemNumCanvasTrans = _itemNumCanvas.transform;
            itemNumCanvasTrans.SetParent(locator.Transform);
            itemNumCanvasTrans.localPosition = Math.VECTOR3_ZERO;
            
            // 総アイテム数エフェクト
            var work = WorkDataManager.Instance.CollectRaidData;
            var numEffect = ResourceManager.LoadOnHash<GameObject>(ResourcePath.GetCollectRaidDialogReceiveGeneralRewardNumEffect(work.EventId), DialogHash);
            _numEffectAnimator = Instantiate(numEffect, itemNumCanvasTrans).GetComponent<Animator>();
            _numEffectAnimator.gameObject.SetLayerRecursively(_dialog.gameObject.layer);
            _numEffectAnimator.gameObject.SetActiveWithCheck(false);

            UpdateItemNum();
        }

        /// <summary>
        /// 収集アイテム数を状態に合わせて更新
        /// </summary>
        private void UpdateItemNum()
        {
            if (_numEffectAnimator == null)
            {
                return;
            }
            
            // 結果発表なら総計、全体報酬達成演出なら最も大きな必要アイテム数
            var work = WorkDataManager.Instance.CollectRaidData;
            var rewardItemNum = _state == State.CollectResult
                ? work.AllCollectItemNum // 全体収集数
                : _rewardIdList.Select(id => MasterDataManager.Instance.masterCollectRaidAllReward.Get(id).AllCollectItemNum).Max(); // 達成した報酬の必要数のなかで一番大きいもの
            
            const string NUM_PARENT_OBJ_NAME = "mot_count_number_00"; // 通常マテリアル持ちの親
            const string ADD_NUM_PARENT_OBJ_NAME = "mot_count_number_add"; // 加算マテリアル持ちの親
            _numEffectObjList.Clear();
            SetItemNum(NUM_PARENT_OBJ_NAME, rewardItemNum);
            SetItemNum(ADD_NUM_PARENT_OBJ_NAME, rewardItemNum);

            // 指定された名前のオブジェクト下にあるBitMapTextCommonに数字をセットするメソッド
            void SetItemNum(string parentObjName, long itemNum)
            {
                var parentObj = GameObjectUtil.FindInDeepChildren(_numEffectAnimator.gameObject, parentObjName).gameObject;
                var numberText = GameObjectUtil.FindInDeepChildren(parentObj.gameObject, NUM_EFFECT_NUM_OBJECT).GetComponent<BitmapTextCommon>();
                numberText.LoadFont();
                numberText.text = CollectRaidUtil.GetItemNumText(itemNum);
                numberText.gameObject.AddComponent<CanvasGroup>();
                _numEffectObjList.Add(numberText.gameObject);
            }
        }

        /// <summary>
        /// ミニキャラを初期化
        /// </summary>
        private void InitMiniChara()
        {
            // 使用するデータを抽選しておく
            var miniCharaGroupId = WorkDataManager.Instance.CollectRaidData.MasterCollectRaidMaster.StagingMiniCharaGroupId;
            var master = MasterDataManager.Instance.masterCollectRaidStagingMiniChara;
            var miniCharaData = master.GetRandomMiniCharaData(miniCharaGroupId, (int)CollectRaidUtil.StagingType.CollectResult);

            InitializeDirector(miniCharaData);
        }

        private void InitializeDirector(MasterCollectRaidStagingMiniChara.CollectRaidStagingMiniChara miniCharaData)
        {
            var scene = SceneManager.Instance.GetCurrentSceneController<CollectRaidSceneController>();
            if (scene == null)
                return;

            _miniDirector = scene.CreateDirector();

            if (_miniDirector == null || _miniDirector.State != MiniDirectorDefines.DirectorState.NonInitialized)
                return;

            // 背景
            var miniBgData = MasterDataManager.Instance.masterMiniBg.Get(MINI_DIRECTOR_BG_ID);
            var bgParam = new MiniDirectorBgParam(new MasterMiniBg.MiniBg[] { miniBgData }, 0, 0);

            // キャラ
            var charaNum = 0;
            var charaDataList = new List<CollectRaidMiniCharaData>();

            void AddCharaData(int charaId, int dressId)
            {
                if (charaId <= 0)
                {
                    return;
                }
                var eventId = WorkDataManager.Instance.CollectRaidData.EventId;
                var dressColorDataList = MasterDataManager.Instance.masterCollectRaidDressColor.GetListWithCollectRaidIdOrderByIdAsc(eventId).Where(x => x.CharaId == charaId && x.DressId == dressId).ToList();
                var dressColorId = !dressColorDataList.IsNullOrEmpty() ? dressColorDataList.FirstOrDefault().ColorId : CharacterBuildInfo.BACKDANCER_COLOR_ID_NULL;
                charaDataList.Add(new CollectRaidMiniCharaData("", charaId, dressId, dressColorId));
                charaNum++;
            }
            AddCharaData(miniCharaData.CharaId, miniCharaData.DressId);
            AddCharaData(miniCharaData.CharaId2, miniCharaData.DressId2);
            AddCharaData(miniCharaData.CharaId3, miniCharaData.DressId3);
            AddCharaData(miniCharaData.CharaId4, miniCharaData.DressId4);
            var charaParam = new MiniDirectorCharaParam(() => charaNum, (index) => charaDataList[index], lotteryType: MiniDirectorDefines.CommandLotteryType.CollectRaid, miniModelLayer: GraphicSettings.LayerIndex.LayerCharaMini);

            /// <summary> 使用する画像サイズ </summary>
            const int TEXTURE_SIZE_X = 1080;
            const int TEXTURE_SIZE_Y = 640;

            //描画先
            _texture = RuntimeObjectManager.NewRenderTextureOnScene(TEXTURE_SIZE_X, TEXTURE_SIZE_Y, 0);

            // カメラ
            var cameraParam = new CollectRaidMiniDirectorCameraParam();
            cameraParam.DefaultCameraPosition = Math.VECTOR3_ZERO;
            cameraParam.DefaultCameraRotation = Math.VECTOR3_ZERO;
            cameraParam.EnableScroll = false;

            // 使用するカメラのモーションデータを渡す（無ければ何も動かない）
            cameraParam.CameraMotionPathArray = new string[] { ResourcePath.COLLECT_EVENT_3D_RESULT_CAMERA_PATH_01, ResourcePath.COLLECT_EVENT_3D_RESULT_CAMERA_PATH_02 };

            // 使用するレイヤーマスク設定
            cameraParam.CullingLayerArray = new GraphicSettings.LayerIndex[] { GraphicSettings.LayerIndex.LayerCharaMini };

            // 描画先
            cameraParam.RenderTexture = _texture;
            _miniCharaImage.texture = _texture;

            _miniDirector.OnInitialize(bgParam, charaParam, cameraParam, isSkipSetDefaultBg: true);
        }

        /// <summary>
        /// 報酬情報を初期化
        /// </summary>
        private void InitRewardItemInfo()
        {
            // 次の報酬までの差分
            var workData = WorkDataManager.Instance.CollectRaidData;
            var nextReward = CollectRaidUtil.GetNextOrLastReward(CollectRaidRewardInfo.RewardType.General);
            // 次orラストの報酬が達成されていないなら、次の報酬までの差分を表示する
            var needShowTarget = nextReward?.RewardState == CollectRaidRewardInfo.RewardStateType.NotClear;
            _nextRewardTargetRoot.SetActiveWithCheck(needShowTarget);
            if (needShowTarget)
            {
                _nextRewardTargetText.text = CollectRaidUtil.GetItemNumText(nextReward.CollectItemNum - workData.AllCollectItemNum);
            }
            
            // 報酬リスト
            _partsRewardItemList.Setup(_rewardIdList, PartsFanRaidGetRewardItemList.EventType.CollectRaid, _dialog.ParentCanvasSortingLayerName, GetSortOrderValue(_dialog.GetSortingOrder(), SortOrder.ItemIcon));

        }
        
        /// <summary>
        /// 座標調整
        /// </summary>
        private void AdjustPosition()
        {
            // 最終集計結果発表のレイアウト -> 達成演出のレイアウト(報酬1列)
            const float ADD_Y_TITLE_RESULT = 178;
            const float ADD_Y_FLASH_RESULT = 193;

            _titleRoot.GetComponent<RectTransform>().anchoredPosition += ADD_Y_TITLE_RESULT * Math.VECTOR2_UP;
            _flashRoot.GetComponent<RectTransform>().anchoredPosition += ADD_Y_FLASH_RESULT * Math.VECTOR2_UP;

            if (IsSmallFrame())
            {
                return;
            }

            // 達成演出のレイアウト(報酬1列) -> 達成演出のレイアウト(報酬2列)
            const float ADD_Y_TITLE = 110;
            const float ADD_Y_FLASH = 125;
            const float ADD_Y_REWARD_INFO = 130;
            const float ADD_Y_ITEM_LIST = 130;
            const float ADD_Y_FOOTER = -86;

            _titleRoot.GetComponent<RectTransform>().anchoredPosition += ADD_Y_TITLE * Math.VECTOR2_UP;
            _flashRoot.GetComponent<RectTransform>().anchoredPosition += ADD_Y_FLASH * Math.VECTOR2_UP;
            _rewardInfoRoot.anchoredPosition += ADD_Y_REWARD_INFO * Math.VECTOR2_UP;
            _partsRewardItemList.GetComponent<RectTransform>().anchoredPosition += ADD_Y_ITEM_LIST * Math.VECTOR2_UP;
            _footerTextRoot.anchoredPosition += ADD_Y_FOOTER * Math.VECTOR2_UP;
        }

        /// <summary>
        /// ミニキャラの座標調整
        /// </summary>
        /// <param name="needAnimation">アニメーションをさせるか</param>
        private void AdjustMiniCharaPosition(bool needAnimation = false)
        {
            const float ADD_Y_MINI_CHARA_RESULT = 193;  // 集計結果発表演出 -> 達成演出(報酬1列) 間の差分
            const float ADD_Y_MINI_CHARA = 110;         // 達成演出(報酬1列) -> 達成演出(報酬2列) 間の差分

            var diff = IsSmallFrame() ? ADD_Y_MINI_CHARA_RESULT : ADD_Y_MINI_CHARA_RESULT + ADD_Y_MINI_CHARA; // 移動距離

            var miniRect = _miniCharaImage.GetComponent<RectTransform>();

            if (!needAnimation)
            {
                miniRect.anchoredPosition += diff * Math.VECTOR2_UP;
            }
            else
            {
                const float DURATION = GameDefine.BASE_FPS_TIME * 6f;

                var sequence = DOTween.Sequence();
                var endPosition = miniRect.anchoredPosition + diff * Math.VECTOR2_UP;
                sequence.Append(DOTween.To(() => miniRect.anchoredPosition, v => miniRect.anchoredPosition = v, endPosition, DURATION).SetEase(Ease.OutCubic));
            }
        }

        #region SortOrder制御

        /// <summary>
        /// 全UIのソートオーダーを設定
        /// </summary>
        private void UpdateSortOrderAll(int baseSortOrder, string sortingLayerName)
        {
            // MiniChara < 数字 < Effect < UIの順にする
            _miniCharaCanvas.sortingLayerName = sortingLayerName;
            _miniCharaCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.MiniChara);
            _itemNumCanvas.sortingLayerName = sortingLayerName;
            _itemNumCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.CollectItemNum);
            _charaEffectCanvas.sortingLayerName = sortingLayerName;
            _charaEffectCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.Effect);
            _flashOverlayCanvas.sortingLayerName = sortingLayerName;
            _flashOverlayCanvas.sortingOrder = GetSortOrderValue(baseSortOrder, SortOrder.UI);
            
            _partsRewardItemList.UpdateSortOrder(GetSortOrderValue(baseSortOrder, SortOrder.ItemIcon), sortingLayerName);
            
            // エフェクト系
            if (_baseFlash != null)
            {
                _baseFlash.SortLayer = sortingLayerName;
                _baseFlash.SortOffset = GetSortOrderValue(baseSortOrder, SortOrder.BaseFlash);
            }
            if (_titleFlash != null)
            {
                _titleFlash.SortLayer = sortingLayerName;
                _titleFlash.SortOffset = GetSortOrderValue(baseSortOrder, SortOrder.TitleFlash);
            }
            if (_titleFlashAction != null)
            {
                _titleFlashAction.SetSortLayer(sortingLayerName);
                _titleFlashAction.SetSortOffset(GetSortOrderValue(baseSortOrder, SortOrder.TitleFlash));
            }
            if (_numEffectAnimator != null)
            {
                var numEffectParticles = _numEffectAnimator.GetComponentsInChildren<ParticleSystem>(true);
                foreach (var particle in numEffectParticles)
                {
                    UIUtil.SetParticleSortOrder(particle.gameObject, sortingLayerName, GetSortOrderValue(baseSortOrder, SortOrder.Effect));
                }
            }
            if (_miniCharaEffectParticle != null)
            {
                UIUtil.SetParticleSortOrder(_miniCharaEffectParticle.gameObject, sortingLayerName, GetSortOrderValue(baseSortOrder, SortOrder.MiniCharaBgEffect));
            }
            if (_completeEffectParticle != null)
            {
                UIUtil.SetParticleSortOrder(_completeEffectParticle.gameObject, sortingLayerName, GetSortOrderValue(baseSortOrder, SortOrder.Effect));
            }
        }
        
        /// <summary>
        /// SortOrderを取得
        /// </summary>
        private int GetSortOrderValue(int baseSortOrder, SortOrder sortOrder)
        {
            return baseSortOrder + (int)sortOrder;
        }
        
        private void OnChangeSortingOrder(int order, string layer)
        {
            UpdateSortOrderAll(order, layer);
        }
        
        #endregion
        
        /// <summary>
        /// 次の処理へ移行
        /// </summary>
        private void Next()
        {// 結果発表 -> 全体報酬達成演出
            if (_state == State.CollectResult && _rewardIdList.Any())
            {
                _state = State.RewardList;
                PlayOutCollectResult(PlayReceiveGeneralRewardFromCollectResult);
                return;
            }
            // 全体報酬達成演出 -> 全体報酬コンプリート演出
            else if (_state == State.RewardList && _isGotAllReward)
            {
                _state = State.Complete;
                PlayOutReceiveReward(PlayComplete);
                return;
            }

            CloseDialog();
        }
        
        /// <summary>
        /// ダイアログを閉じる
        /// </summary>
        private void CloseDialog()
        {
            FrameClose();
            _isPlayingDirector = false;

            // 中身と一緒にFlashのハケを再生
            switch (_state)
            {
                case State.CollectResult:
                    _baseFlash.Play(LABEL_OUT_RESULT);
                    _titleFlashAction.Play(LABEL_OUT_RESULT_TITLE);
                    break;
                case State.RewardList:
                    _baseFlash.Play(LABEL_OUT);
                    _titleFlash.Play(LABEL_OUT);
                    break;
                case State.Complete:
                    _baseFlash.Play(LABEL_OUT);
                    _titleFlash.Play(LABEL_OUT_COMP);
                    break;
            }
            _numEffectAnimator.Play(GetNumEffectOutLabel());
            
            _miniCharaEffectParticle.gameObject.SetActiveWithCheck(false);
            
            // コンプリート演出の場合パーティクルを消す
            if (_state == State.Complete && _completeEffectParticle != null)
            {
                _completeEffectParticle.gameObject.SetActiveWithCheck(false);
            }
            
            // 手動でUIハケアニメを再生 (DialogFanRaidGetAllFanRewardを参考)
            const TweenAnimation.PresetType PRESET = TweenAnimation.PresetType.PartsOutMoveAndFade;
            var seq = DOTween.Sequence();
            seq.Join(_stateUIObjectArray[(int)_state], PRESET)
                .Join(_closeButton.gameObject, PRESET)
                .Join(_miniCharaImage, PRESET);
            foreach (var obj in _numEffectObjList)
            {
                seq.Join(obj, PRESET);
            }

            _dialog.Close(_onClose);
        }
        
        /// <summary>
        /// 報酬詳細(i)押下
        /// </summary>
        private void OnClickRewardButton()
        {
            var workData = WorkDataManager.Instance.CollectRaidData;
            DialogCollectRaidGeneralReward.Open(workData.MasterCollectRaidMaster);
        }

        private void OnDestroyDialog()
        {
            if (_texture != null)
            {
                RuntimeObjectManager.Destroy(_texture);
                _texture = null;
            }

            var scene = SceneManager.Instance.GetCurrentSceneController<CollectRaidSceneController>();
            if (scene != null)
            {
                scene.DestroyDirector();
            }
        }
        
        /// <summary>
        /// 戻るボタン押下
        /// </summary>
        private bool OnPushBackKey()
        {
            if (UIManager.Instance.IsLockGameCanvas())
            {
                // ロック中は無視
                return true;
            }

            Next();
            return true;
        }

        #region Frame

        /// <summary>
        /// フレームを開く
        /// </summary>
        private void FrameOpen()
        {
            // 背景フレームの生成
            _animationFrameCross = UIUtil.CreateAnimationFrameCross(_frameRoot.transform, DialogHash);
            _animationFrameCross.transform.SetAsFirstSibling();

            var sequence = DOTween.Sequence();
            sequence.AppendCallback(() =>
            {
                _animationFrameCross.PlayIn(GetFrameOpenAnimType());
            });

            const float PLAY_IN_TITLE_INTERVAL = 0.36f;
            sequence.AppendInterval(PLAY_IN_TITLE_INTERVAL);
            sequence.AppendCallback(PlayInTitle);
        }

        /// <summary>
        /// フレームを閉じる
        /// </summary>
        private void FrameClose()
        {
            _animationFrameCross.PlayOut();
        }

        /// <summary>
        /// フレームを広げる
        /// </summary>
        private void FrameExpand()
        {
            _animationFrameCross.PlayExpand(IsSmallFrame()
                ? AnimationFrameCross.DefineFrameType.FrameType04
                : AnimationFrameCross.DefineFrameType.FrameType05);
        }

        /// <summary>
        /// フレームの開くアニメーションのタイプを取得
        /// </summary>
        private AnimationFrameCross.DefineFrameType GetFrameOpenAnimType()
        {
            if (_state == State.CollectResult)
            {
                return AnimationFrameCross.DefineFrameType.FrameType06;
            }
            else if (IsSmallFrame())
            {
                return AnimationFrameCross.DefineFrameType.FrameType04;
            }
            else
            {
                return AnimationFrameCross.DefineFrameType.FrameType10;
            }
        }

        /// <summary>
        /// フレームの大きさを取得
        /// </summary>
        /// <returns></returns>
        private bool IsSmallFrame()
        {
            return _rewardIdList.Count <= REWARD_NUM_USE_SMALL_FRAME;
        }
        
        #endregion

        #region 演出

        private void PlayInDialog()
        {
            switch (_state)
            {
                case State.CollectResult:
                    PlayCollectResult();
                    break;
                case State.RewardList:
                    PlayReceiveGeneralReward();
                    break;
            }
        }

        /// <summary>
        /// UIを非表示にする
        /// </summary>
        private void SetUIActiveFalse()
        {
            _miniCharaImage.SetActiveWithCheck(false);
            _rewardInfoRoot.gameObject.SetActiveWithCheck(false);
            _partsRewardItemList.SetActiveWithCheck(false);
            _footerTextRoot.gameObject.SetActiveWithCheck(false);
            _closeButton.SetActiveWithCheck(false);
            UpdateUIState();
            _miniCharaEffectParticle.gameObject.SetActiveWithCheck(false);

            if (_completeEffectParticle != null)
            {
                _completeEffectParticle.gameObject.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// stateに応じてStateUIObjectArrayの表示を更新する
        /// </summary>
        private void UpdateUIState()
        {
            for (var i = 0; i < _stateUIObjectArray.Length; i++)
            {
                _stateUIObjectArray[i].SetActiveWithCheck((int)_state == i);
            }
        }

        /// <summary>
        /// 数字エフェクトのイリに使用するラベルを取得
        /// </summary>
        private string GetNumEffectInLabel()
        {
            const string LABEL_IN_NUM_EFFECT = "collectraid_reward_target_in";
            const string LABEL_IN_NUM_EFFECT_RESULT = "collectraid_reward_target_in2";
            return _state == State.CollectResult ? LABEL_IN_NUM_EFFECT_RESULT : LABEL_IN_NUM_EFFECT;
        }
        
        /// <summary>
        /// 数字エフェクトのハケに使用するラベルを取得
        /// </summary>
        private string GetNumEffectOutLabel()
        {
            const string LABEL_OUT_NUM_EFFECT = "collectraid_reward_target_out";
            const string LABEL_OUT_NUM_EFFECT_RESULT = "collectraid_reward_target_out2";
            return _state == State.CollectResult ? LABEL_OUT_NUM_EFFECT_RESULT : LABEL_OUT_NUM_EFFECT;
        }
        
        /// <summary>
        /// タイトル入り演出の再生
        /// </summary>
        private void PlayInTitle()
        {
            _baseFlash.Play(_state == State.CollectResult ? LABEL_IN_RESULT : LABEL_IN);
            switch (_state)
            {
                case State.CollectResult:
                    const float RESULT_TITLE_DELAY = GameDefine.BASE_FPS_TIME * 5f;
                    var seq = DOTween.Sequence();
                    seq.AppendInterval(RESULT_TITLE_DELAY).AppendCallback(() => _titleFlashAction.Play(LABEL_IN_RESULT_TITLE));
                    AudioManager.Instance.PlaySe(AudioId.SFX_COLLECT_RAID_TOTAL_RESULT);
                    break;
                case State.RewardList:
                    _titleFlash.Play(LABEL_IN);
                    AudioManager.Instance.PlaySe(AudioId.SFX_FAN_RAID_GET_ALL_REWARD);
                    break;
            }

            // 放射状エフェクトのモーション再生
            const string LIGHT_EFFECT_OBJ_NAME = "MOT_mc_impact_light1";
            const string LABEL_IN_LIGHT_EFFECT = "in00";
            var motion = _baseFlash.GetMotion(LIGHT_EFFECT_OBJ_NAME);
            motion.SetMotionPlay(LABEL_IN_LIGHT_EFFECT);
        }
        
        /// <summary>
        /// 集計結果発表演出の再生
        /// </summary>
        private void PlayCollectResult()
        {
            // 初めはUIを非表示に
            SetUIActiveFalse();
            _breakTextObject.GameObject.SetActiveWithCheck(false);
            UIManager.Instance.LockGameCanvas();
            
            // コールバック登録
            _baseFlash.SetActionCallBack(LABEL_IN_CHARA_RESULT, () => OnInChara(), AnMotionActionTypes.Start);
            _baseFlash.SetActionCallBack(LABEL_IN_COLLECT_ITEM_RESULT, () =>
            {
                OnInCollectItem();
                // 閉じるボタンイリ
                const float BUTTON_IN_DELAY = GameDefine.BASE_FPS_TIME * 8f;
                _closeButton.SetActiveWithCheck(true);
                var seq = DOTween.Sequence();
                seq.AppendInterval(BUTTON_IN_DELAY)
                    .Append(_closeButton.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade)
                    .OnComplete(() => { UIManager.Instance.UnlockGameCanvas(); });
            }, AnMotionActionTypes.Start);

            // 入り
            FrameOpen();
        }

        /// <summary>
        /// 集計結果発表演出のハケ再生
        /// </summary>
        private void PlayOutCollectResult(Action onEnd)
        {
            // UIハケ
            var outSeq = DOTween.Sequence();
            outSeq.Join(_closeButton.gameObject, TweenAnimation.PresetType.PartsOutFade);

            _numEffectAnimator.Play(GetNumEffectOutLabel());
            _baseFlash.Play(LABEL_OUT_RESULT);
            _titleFlashAction.Play(LABEL_OUT_RESULT_TITLE);
            onEnd?.Invoke();
        }

        /// <summary>
        /// 全体報酬達成演出の再生：結果発表演出からの遷移
        /// </summary>
        private void PlayReceiveGeneralRewardFromCollectResult()
        {
            UpdateUIState();
            UIManager.Instance.LockGameCanvas();

            // コールバック登録
            _baseFlash.RemoveAllAction();
            _baseFlash.SetActionCallBack(LABEL_IN_COLLECT_ITEM, OnInCollectItem, AnMotionActionTypes.Start);
            _baseFlash.SetActionCallBack(LABEL_IN_EVENT, OnInEvent, AnMotionActionTypes.Start);
            _baseFlash.SetActionCallBack(LABEL_IN_REWARD, () =>
            {
                OnInRewardItem(() =>
                {
                    UIManager.Instance.UnlockGameCanvas();
                });
            }, AnMotionActionTypes.Start);

            // 入り
            FrameExpand();
            OnInChara();

            const float MINI_CUBIC_OUT_DELAY = GameDefine.BASE_FPS_TIME * 4f;
            var seq = DOTween.Sequence();
            seq.AppendInterval(MINI_CUBIC_OUT_DELAY)
                .AppendCallback(() =>
                {
                    // ダイアログの大きさに応じた場所までアニメーションで移動させる
                    AdjustMiniCharaPosition(true);
                });

            const float IN_DELAY = GameDefine.BASE_FPS_TIME * 6f;
            seq.AppendInterval(IN_DELAY)
                .AppendCallback(() =>
                {
                    // ミニキャラ以外の要素はタイトルinのタイミングで座標調整する
                    AdjustPosition();
                    InitTitleFlash();
                    UpdateSortOrderAll(_dialog.GetSortingOrder(), _dialog.ParentCanvasSortingLayerName);
                    UpdateItemNum();

                    _baseFlash.Play(LABEL_IN);
                    _titleFlash.Play(LABEL_IN);
                    _breakTextObject.GameObject.SetActiveWithCheck(true);
                    _miniCharaEffectParticle.gameObject.SetActiveWithCheck(true);
                    AudioManager.Instance.PlaySe(AudioId.SFX_FAN_RAID_GET_ALL_REWARD);
                });
        }
        
        /// <summary>
        /// 全体報酬達成演出の再生：単体
        /// </summary>
        private void PlayReceiveGeneralReward()
        {
            // 初めはUIを非表示に
            SetUIActiveFalse();
            UIManager.Instance.LockGameCanvas();
            
            // ダイアログの大きさによっては座標調整が必要
            AdjustPosition();
            AdjustMiniCharaPosition();
            
            // コールバック登録
            _baseFlash.SetActionCallBack(LABEL_IN_CHARA, () => OnInChara(), AnMotionActionTypes.Start);
            _baseFlash.SetActionCallBack(LABEL_IN_COLLECT_ITEM, OnInCollectItem, AnMotionActionTypes.Start);
            _baseFlash.SetActionCallBack(LABEL_IN_EVENT, OnInEvent, AnMotionActionTypes.Start);
            _baseFlash.SetActionCallBack(LABEL_IN_REWARD, () =>
            {
                OnInRewardItem(() =>
                {
                    UIManager.Instance.UnlockGameCanvas();
                });
            }, AnMotionActionTypes.Start); 
            
            // 入り
            FrameOpen();
        }

        /// <summary>
        /// 全体報酬達成演出のハケ再生
        /// </summary>
        private void PlayOutReceiveReward(Action onEnd)
        {
            // UIハケ
            var outSeq = DOTween.Sequence();
            outSeq.Join(_stateUIObjectArray[(int)State.RewardList], TweenAnimation.PresetType.PartsOutMoveAndFade);
            outSeq.Join(_closeButton.gameObject, TweenAnimation.PresetType.PartsOutFade);
            outSeq.OnComplete(() => _stateUIObjectArray[(int) State.RewardList].SetActiveWithCheck(false));

            // タイトルのOutを再生
            _titleFlash.Play(LABEL_OUT);
            
            // ハケ終わりを待たずに次の状態へ切り替える
            onEnd?.Invoke();
        }

        private void Update()
        {
            if (_isPlayingDirector)
            {
                _miniDirector.OnUpdate();
            }
        }

        private void LateUpdate()
        {
            if (_isPlayingDirector)
            {
                _miniDirector.OnLateUpdate();
            }
        }

        /// <summary>
        /// 演出再生
        /// </summary>
        private void OnPlayDirector(int bgId, MiniDirectorDefines.DirectorState state)
        {
            // 背景の切替
            _miniDirector.ChangeBg(bgId, true); // 強制切り替えにしないと、非表示のたづなさんが居座っているためキャラが表示されない…

            ChangeState(state);

            _isPlayingDirector = true;
        }

        /// <summary>
        /// ステート変更
        /// </summary>
        public void ChangeState(MiniDirectorDefines.DirectorState state)
        {
            // 演出ステート遷移
            _miniDirector.ChangeState(new MiniDirectorCommand(state), false);

            // 演出に変更を即座に伝える
            _miniDirector.OnUpdate();
            _miniDirector.OnLateUpdate();
        }

        /// <summary>
        /// ミニキャラのイリ
        /// </summary>
        private void OnInChara()
        {
            _miniCharaImage.SetActiveWithCheck(true);

            switch (_state)
            {
                case State.CollectResult:
                    OnPlayDirector(MINI_DIRECTOR_BG_ID, MiniDirectorDefines.DirectorState.CollectRaidResult);
                    break;
                case State.RewardList:
                    _miniCharaEffectParticle.gameObject.SetActiveWithCheck(true);
                    OnPlayDirector(MINI_DIRECTOR_BG_ID, MiniDirectorDefines.DirectorState.CollectRaidReward);
                    break;
                default:
                    return;
            }            
        }

        /// <summary>
        /// 達成個数のイリ
        /// </summary>
        private void OnInCollectItem()
        {
            _numEffectAnimator.gameObject.SetActiveWithCheck(true);
            _numEffectAnimator.Play(GetNumEffectInLabel());
        }

        /// <summary>
        /// イベント報酬と後〇〇個のイリのタイミング
        /// </summary>
        private void OnInEvent()
        {
            _rewardInfoRoot.gameObject.SetActiveWithCheck(true);
            
            var seq = DOTween.Sequence();
            seq.Join(_rewardInfoRoot.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade);
        }

        /// <summary>
        /// アイテムのアイコンのイリのタイミング
        /// </summary>
        /// <param name="onComplete"></param>
        private void OnInRewardItem(Action onComplete = null)
        {
            var seq = DOTween.Sequence();
            var totalDelay = 0f;
            
            // アイテムイリ
            seq.InsertCallback(totalDelay, () => _partsRewardItemList.SetActiveWithCheck(true))
                .Join(_partsRewardItemList.CreatePlayInAnim());
            
            // 文言イリ
            totalDelay += GameDefine.BASE_FPS_TIME * 8f;
            seq.InsertCallback(totalDelay, () => _footerTextRoot.gameObject.SetActiveWithCheck(true))
                .Join(_footerTextRoot.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade);
            
            // 閉じるボタンイリ
            totalDelay += GameDefine.BASE_FPS_TIME * 2f;
            seq.InsertCallback(totalDelay, () => _closeButton.SetActiveWithCheck(true))
                .Join(_closeButton.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade);

            seq.OnComplete(() => onComplete?.Invoke());
        }

        /// <summary>
        /// 全体報酬コンプリート演出の再生
        /// </summary>
        private void PlayComplete()
        {
            UIManager.Instance.LockGameCanvas();
            var inSeq = DOTween.Sequence();

            var totalDelay = 0f;
            
            totalDelay += GameDefine.BASE_FPS_TIME * 2f;
            inSeq.InsertCallback(totalDelay, () =>
                {
                    // コンプリート演出イリ
                    _titleFlash.Play(LABEL_IN_COMP);
                    AudioManager.Instance.PlaySe(AudioId.SFX_FAN_RAID_GET_ALL_REWARD_COMP);

                    // ミニキャラモーション
                    ChangeState(MiniDirectorDefines.DirectorState.CollectRaidComplete);
                });

            // UI
            totalDelay += GameDefine.BASE_FPS_TIME * 6f;
            inSeq.InsertCallback(totalDelay,() => _stateUIObjectArray[(int)State.Complete].SetActiveWithCheck(true))
                .Insert(_stateUIObjectArray[(int)State.Complete], TweenAnimation.PresetType.PartsInMoveAndFade, totalDelay);

            // ボタン入り
            totalDelay += GameDefine.BASE_FPS_TIME * 5f;
            inSeq.Insert(_closeButton.gameObject, TweenAnimation.PresetType.PartsInMoveAndFade, totalDelay);

            // パーティクル入り (ミニキャラのジャンプモーションに合うように発火させる)
            totalDelay += GameDefine.BASE_FPS_TIME * 5f;
            inSeq.InsertCallback(totalDelay, () =>
                {
                    _completeEffectParticle.gameObject.SetActiveWithCheck(true);
                    UIManager.Instance.UnlockGameCanvas();
                });
        }

        #endregion
    }
}
