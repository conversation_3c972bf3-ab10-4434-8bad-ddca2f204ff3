using System;
using UnityEngine;
namespace Gallop
{
    /// <summary>
    /// ストーリー解放
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCollectRaidStoryReleaseNotice : DialogInnerBase
    {
        /// <summary> ストーリーのプレート </summary>
        [SerializeField]
        private PartsEpisodeListItem _partsEpisodeItem = null;

        ///<summary> 上側のテキスト </summary>
        [SerializeField]
        private TextCommon _headerText = null;

        ///<summary> 下側のテキスト </summary>
        [SerializeField]
        public TextCommon _bottomText = null;

        private EpisodeStoryData _episodeStoryData;
        private bool _canRead; // 読める状態のストーリーかどうか
        private EpisodeStartSetings _settings;

        /// <summary>
        /// ストーリー解放ダイアログを開く
        /// </summary>
        public static void Open(int storyId, Action onCancel, float downloadSize = 0, EpisodeStartSetings settings = null)
        {
            var component = LoadAndInstantiatePrefab<DialogCollectRaidStoryReleaseNotice>(ResourcePath.COLLECT_RAID_STORY_RELEASE_NOTICE_DIALOG);
            var dialogData = component.CreateDialogData();
            component.Initialize(storyId, settings);
            component.SetupDialogData(dialogData, onCancel, downloadSize);

            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Initialize(int storyId, EpisodeStartSetings settings)
        {
            _settings = settings;

            // ストーリー情報セット
            var masterStoryData = MasterDataManager.Instance.masterCollectRaidStory.Get(storyId);
            _episodeStoryData = new EpisodeStoryData(masterStoryData);
            _partsEpisodeItem.UpdateItemSimple(_episodeStoryData);

            // 既読設定追加しておく
            WorkDataManager.Instance.AlreadyReadData.AddReadStoryRelease(masterStoryData.ReadStoryReleaseStoryId);
        }

        /// <summary>
        /// ダイアログの設定
        /// </summary>
        /// <param name="data"></param>
        /// <param name="onCancel"></param>
        /// <param name="downloadSize"></param>
        public void SetupDialogData(DialogCommon.Data data, Action onCancel, float downloadSize)
        {
            _canRead = !_episodeStoryData.IsLock; // 再生可能なストーリーかどうか
            bool showDialogContinuously = TempData.Instance.CollectRaidData.ShowStoryReleaseDialogContinuously; // ダイアログを連続で表示中か

            string bottomText;
            if (_canRead)
            {
                if (showDialogContinuously)
                {
                    // 連続で視聴できるストーリーがあります
                    bottomText = TextId.CollectEvent626007.Text();
                }
                else
                {
                    // 解放されました
                    bottomText = TextId.CollectEvent626005.Text();
                }
            }
            else
            {
                // 解放可能になりました
                bottomText = TextId.CollectEvent626004.Text();
            }

            // ダイアログのタイトル
            string storyText = TextUtil.RemoveNewLine(WorkDataManager.Instance.CollectRaidData.MasterCollectRaidMaster.CollectRaidStoryButtonText);
            data.Title = (showDialogContinuously && _canRead) ? TextId.CollectEvent626006.Text() : TextId.CollectEvent626003.Format(storyText);

            // テキスト設定
            int eventId = WorkDataManager.Instance.CollectRaidData.MasterCollectRaidMaster.Id;
            _headerText.text = TextUtil.GetMasterText(MasterString.Category.CollectRaidTitle, eventId);
            _bottomText.text = TextUtil.Format(bottomText, storyText);
            if (_canRead)
            {
                // 再生可能なストーリーの場合は右が再生ボタンになる
                data.LeftButtonText = TextId.Common0004.Text();
                data.RightButtonText = TextId.Story0033.Format(downloadSize);
                data.RightButtonColor = DialogCommon.ButtonColor.Green;
                data.RightButtonCallBack = _ => StartStory();
                data.LeftButtonCallBack = dialog => dialog.Close(() =>
                {
                    TempData.Instance.CollectRaidData.ShowStoryReleaseDialogContinuously = false; // 連続再生用のフラグを切っておく
                    onCancel?.Invoke();
                });
            }
            else
            {
                data.CenterButtonText = TextId.Common0007.Text();
                data.CenterButtonCallBack = dialog => dialog.Close(() =>
                {
                    TempData.Instance.CollectRaidData.ShowStoryReleaseDialogContinuously = false; // 連続再生用のフラグを切っておく
                    onCancel?.Invoke();
                });
            }

            //初期化後にどのダイアログを開けばいいかわかるので再セット
            data.FormType = GetFormType();
        }

        /// <summary>
        /// 解放されたストーリーを再生する
        /// </summary>
        private void StartStory()
        {
            if (!_canRead)
            {
                Debug.LogError("解放されていないストーリーを再生しようとしています");
                return;
            }

            // ダイアログを閉じてストーリーを再生する
            DialogManager.RemoveAllDialog(() => CollectRaidUtil.StartStory(_episodeStoryData.MasterCollectRaidStoryData, () =>
            {
                // 期間跨ぎでTOP遷移時にストーリーの強制再生が必要になった場合、収集イベントTOPには戻さずホームに遷移する旨のダイアログを表示する
                if (CollectRaidUtil.CreateCalculateTermEndDialogIfNeeded())
                {
                    return;
                }

                // 連続再生用のフラグを立てておく
                TempData.Instance.CollectRaidData.ShowStoryReleaseDialogContinuously = true;

                // 強制再生が必要なストーリーがないなら収集イベントTOPに戻る
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.CollectRaidTop);
            }, setting: _settings));
        }

#region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType()
        {
            return _canRead ? DialogCommonBase.FormType.SMALL_TWO_BUTTON : DialogCommonBase.FormType.SMALL_ONE_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

#endregion
    }
}