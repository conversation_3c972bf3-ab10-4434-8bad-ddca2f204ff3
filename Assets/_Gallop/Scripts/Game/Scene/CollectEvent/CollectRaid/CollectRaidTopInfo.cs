using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 収集イベントTOP画面のModel
    /// </summary>
    public class CollectRaidTopInfo
    {
        #region 定数、enum

        /// <summary> ミッションボタンテクスチャで参照するストイベのID </summary>
        private const int EVENT_MISSION_BUTTON_TARGET_STORY_EVENT_ID = 1001;
        
        /// <summary>
        /// イベントTOPを更新しなおす必要があるか
        /// </summary>
        public enum EventTopRefreshState
        {
            None, // 更新の必要なし
            
            EndMainSession, // 更新の必要あり（本戦終了）
            EndCalculating, // 更新の必要あり（集計終了）
        }

        #endregion

        #region private, protected変数

        private int _eventId;

        #endregion
        
        #region プロパティ

        private WorkCollectRaidData _collectRaidData => WorkDataManager.Instance.CollectRaidData;
        
        /// <summary> 全体収集数 </summary>
        public long AllCollectNum => _collectRaidData.AllCollectItemNum;

        /// <summary> 全体報酬の受け取り資格があるか </summary>
        public bool CanGetGeneralReward => _collectRaidData.CanGetGeneralReward;
        
        /// <summary> イベントタイトル </summary>
        public string EventTitle => CollectRaidMaster.EventTitle;

        public string LogoPath => ResourcePath.GetCollectRaidLogoTexturePath(_eventId);
        
        public string EventLogoFlash => ResourcePath.STORY_EVENT_LOGO_FLASH_PATH;
        
        public string TotalItemCountHeaderPath => ResourcePath.GetCollectRaidGeneralRewardDialogTextTexturePath(_eventId);

        public string BgEffectPath => ResourcePath.GetCollectRaidTopBgEffectPath(_eventId);

        /// <summary> 全体報酬ボタン</summary>
        public string GeneralRewardButtonTexturePath =>
            ResourcePath.GetCollectRaidGeneralRewardButtonTexturePath(_eventId);
        
        /// <summary> 個人報酬ボタン</summary>
        public string IndividualRewardButtonTexturePath =>
            ResourcePath.GetCollectRaidIndividualRewardButtonTexturePath(_eventId);

        /// <summary> ストーリーボタン</summary>
        public string StoryButtonTexturePath => ResourcePath.GetCollectRaidEventStoryButtonTexturePath(_eventId);

        /// <summary>
        /// イベントミッションボタン
        /// 現状では初回ストイベのミッションボタン(たづなさん)を使用
        /// </summary>
        public string MissionButtonTexturePath =>
            ResourcePath.GetStoryEventTopMissionButtonImagePath(EVENT_MISSION_BUTTON_TARGET_STORY_EVENT_ID);
        
        /// <summary> 表示中のマスタ </summary>
        public MasterCollectRaidMaster.CollectRaidMaster CollectRaidMaster { get; private set; }

        /// <summary> PlayInViewでTIPSを表示する必要があるか </summary>
        public bool IsNeedShowTips { private set; get; }

        #endregion

        public void Setup()
        {
            _eventId = _collectRaidData.EventId;
            CollectRaidMaster = MasterDataManager.Instance.masterCollectRaidMaster.Get(_eventId);
            if (CollectRaidMaster == null)
            {
                Debug.LogError("開催中の収集イベントがない");
            }

            // 報酬受取期間以外に初回遷移したならTIPS表示
            SetNeedShowTips(!_collectRaidData.IsRewardReceiving() && _collectRaidData.IsFirstTransition);
        }

        /// <summary>
        /// PlayInViewでTIPSを出す必要があるかどうかの更新
        /// </summary>
        public void SetNeedShowTips(bool isNeedShow)
        {
            IsNeedShowTips = isNeedShow;
        }
        
        /// <summary>
        /// イベントTOPを更新しなおす必要があるか
        /// </summary>
        /// <returns></returns>
        public EventTopRefreshState GetEventTopRefreshState()
        {

            switch (_collectRaidData.EventStatus)
            {
                // サーバーから最後に受け取ったイベントの開催状況と、端末の最新時刻のイベント開催状況が一致しない場合は更新の必要あり
                case WorkCollectRaidData.EventStatusType.InSession:
                    if (!_collectRaidData.IsInSession())
                    {
                        return EventTopRefreshState.EndMainSession;
                    }
                    break;

                case WorkCollectRaidData.EventStatusType.Calculating:
                    if (!_collectRaidData.IsCalculating())
                    {
                        return EventTopRefreshState.EndCalculating;
                    }
                    break;
            }

            return EventTopRefreshState.None;
        }

        #region 文言関連
        
        /// <summary>
        /// 開催期間の文言取得
        /// </summary>
        public string GetEventTermText()
        {
            string termText;

            if (_collectRaidData.IsInSession())
            {
                // 終了時刻の分表記が59になるように1分引く
                termText = TextUtil.Format(TextId.CollectEvent508013.Text(),
                    TimeUtil.ToDispStringFromMonth(CollectRaidMaster.StartDateTime),
                    TimeUtil.ToDispStringFromMonth(CollectRaidMaster.CalcStartDateTime - new TimeSpan(0, 1, 0)));
            }
            else if(_collectRaidData.IsCalculating())
            {
                // 終了時刻の分表記が59になるように1分引く
                termText = TextUtil.Format(TextId.CollectEvent508014.Text(),
                    TimeUtil.ToDispStringFromMonth(CollectRaidMaster.CalcStartDateTime),
                    TimeUtil.ToDispStringFromMonth(CollectRaidMaster.CalcEndDateTime - new TimeSpan(0, 1, 0)));
            }
            else
            {
                termText = CollectRaidMaster.CollectRaidButtonRewardReceivingTermText;
            }
            
            return termText;
        }

        /// <summary>
        /// イベント収集期間が終了していることを示すテキスト取得
        /// </summary>
        public string GetEventSessionEndText()
        {
            var sessionEndText = string.Empty;
            
            if (_collectRaidData.IsCalculating())
            {
                // 集計中なら「〇〇は集計中です」
                sessionEndText = TextUtil.Format(TextId.CollectEvent508009.Text(), CollectRaidMaster.CollectItemName);
            }
            else if (_collectRaidData.IsRewardReceiving() || _collectRaidData.IsClose())
            {
                // 発表後なら「イベント開催期間は終了しました」
                sessionEndText = TextId.StoryEvent0039.Text();
            }

            return sessionEndText;
        }

        /// <summary>
        /// イベント報酬の受け取り条件を示すテキスト取得
        /// </summary>
        public string GetRewardGetConditionText()
        {
            if (WorkDataManager.Instance.CollectRaidData.MasterCollectRaidMaster.UseMission)
            {
                return TextUtil.Format(TextId.CollectEvent508003.Text(), CollectRaidMaster.CollectItemName);
            }
            else
            {
                return TextId.CollectEvent626008.Text();
            }
        }
        
        /// <summary>
        /// イベント報酬の受け取り条件を示すテキスト取得
        /// </summary>
        public string GetNeedItemNumText(CollectRaidRewardInfo.RewardType type)
        {
            var text = string.Empty;
            CollectRaidRewardInfo info;
            switch (type)
            {
                case CollectRaidRewardInfo.RewardType.General:
                    info = CollectRaidUtil.GetNextOrLastReward(CollectRaidRewardInfo.RewardType.General);
                    text = CollectRaidUtil.GetRoundItemNumText(info.CollectItemNum - _collectRaidData.AllCollectItemNum);
                    break;
                case CollectRaidRewardInfo.RewardType.Individual:
                    info = CollectRaidUtil.GetNextOrLastReward(CollectRaidRewardInfo.RewardType.Individual);
                    text = CollectRaidUtil.GetRoundItemNumText(info.CollectItemNum - _collectRaidData.IndividualCollectItemNum);
                    break;
            }

            return text;
        }
        
        #endregion
        
        #region UI
        
        /// <summary>
        /// 個人報酬ボタンにバッジを出すかどうか
        /// </summary>
        public bool IsShowBadgeOnIndividualRewardButton()
        {
            // 受け取り可能なものがあれば出す
            return _collectRaidData.IndividualRewardList.Any(reward => reward.RewardState == CollectRaidRewardInfo.RewardStateType.CanReceive);
        }

        /// <summary>
        /// ストーリーボタンにnewアイコンを出すかどうか
        /// </summary>
        public bool IsShowNewIconOnStoryButton()
        {
            // 未読のものがあれば出す
            return _collectRaidData.UnlockStoryList.Any(story => !story.IsRead);
        }
        
        /// <summary>
        /// ミッションボタンにバッジを出すかどうか
        /// </summary>
        public bool IsShowBadgeOnMissionButton()
        {
            // 受け取り可能なものがあれば出す
            return _collectRaidData.CanReceiveEventMission;
        }
        
        /// <summary>
        /// キャンペーンページボタンにバッジを出すかどうか
        /// </summary>
        public bool IsShowBadgeOnCampaignPageButton()
        {
            if (!CollectRaidMaster.IsCampaignPageEnable())
            {
                return false;
            }
            
            // タップ報酬があるかどうか
            // Note: タップ報酬の有無をまだサーバーから受け取っていない(null)場合はイベント開始時刻跨ぎが行われた直後なのでtrue
            var isExistTapReward = TempData.Instance.CollectEventMapData.IsExistEventMapTapReward ?? true;
            
            return isExistTapReward;
        }
        
        #endregion
        
        #region 通信関連
        
        /// <summary>
        /// イベントTOP遷移時の通信
        /// </summary>
        public static IEnumerator ExecuteEventTopApi()
        {
            var complete = false;
            new CollectRaidIndexRequest().Send(res =>
                {
                    WorkDataManager.Instance.CollectRaidData.UpdateAtEventTop(res);

                    complete = true;
                },
                (error, value) =>
                {
                    complete = true; // 通信エラー時もWaitUntilは抜けたい
                });

            yield return new WaitUntil(() => complete);
        }

        /// <summary>
        /// ポーリング実行
        /// </summary>
        public void ExecutePollingApi(System.Action onComplete)
        {
            new CollectRaidPollingRequest().Send(res =>
            {
                _collectRaidData.UpdateAtPolling(res);
                onComplete?.Invoke();
            }, controlConnectingUI: false);
        }

        /// <summary>
        /// ポーリングの間隔(s)
        /// </summary>
        public float GetPollingTime()
        {
            return _collectRaidData.EventTopPollingTime;
        }

        #endregion

        #region 報酬関連

        /// <summary>
        /// 再生可能な全体報酬達成演出の情報を更新する (バッジ用)
        /// </summary>
        public void CompleteGeneralRewardNotice()
        {
            _collectRaidData.CompleteGeneralRewardNotice();
        }
        
        /// <summary>
        /// 条件達成ダイアログを出した全体報酬情報を更新する
        /// </summary>
        public void UpdateCheckedRewardId(List<int> idList)
        {
            _collectRaidData.RegisterGeneralRewardAsChecked(idList.ToArray());
        }
        
        /// <summary>
        /// 演出再生が必要な報酬IDリストを返す
        /// </summary>
        public List<int> GetUnnoticedRewardIdList()
        {
            var idList = _collectRaidData.GeneralRewardList
                .Where(reward => reward.RewardState == CollectRaidRewardInfo.RewardStateType.NeedNotice)
                .Select(reward => reward.RewardId)
                .ToList();

            return idList;
        }
        
        /// <summary>
        /// 最後の報酬が含まれているか
        /// </summary>
        public bool ContainLastGeneralReward(List<int> rewardIdList)
        {
            var master = MasterDataManager.Instance.masterCollectRaidAllReward;
            return rewardIdList.Any(rewardId => master.IsLastRewardId(_collectRaidData.MasterCollectRaidMaster.AllRewardSetId, rewardId));
        }

        #endregion

        #region イベントTOPで強制再生するストーリー

        /// <summary>
        /// イベントTOP遷移時に再生する必要のあるストーリーのMasterDataを取得
        /// </summary>
        public MasterCollectRaidStory.CollectRaidStory GetMasterEventStoryDataNeedPlay()
        {
            var collectRaidId = _collectRaidData.MasterCollectRaidMaster.Id;
            var storyList = MasterDataManager.Instance.masterCollectRaidStory.GetListWithCollectRaidIdOrderByIdAsc(collectRaidId);

            switch (_collectRaidData.EventStatus)
            {
                case WorkCollectRaidData.EventStatusType.InSession:
                    // イベント開催期間中でプロローグが未読なら強制再生
                    var prologue = storyList.FirstOrDefault(data => data.StoryConditionType == (int)CollectRaidUtil.EventStoryType.Prologue);
                    if (prologue != null && !prologue.IsRead())
                    {
                        return prologue;
                    }
                    break;

                case WorkCollectRaidData.EventStatusType.RewardReceiving:
                    // イベント報酬受取期間中でエンディングが未読なら強制再生
                    var ending = storyList.FirstOrDefault(data => data.StoryConditionType == (int)CollectRaidUtil.EventStoryType.Ending);
                    if (ending != null && !ending.IsRead())
                    {
                        return ending;
                    }
                    break;
            }

            return null;
        }

        #endregion

        #region イベントTOPの3d演出・BGM

        /// <summary>
        /// 現在のイベント進捗状況に応じて表示を切り替える3D・BGMのマスターデータを取得
        /// </summary>
        public MasterCollectRaidTopData.CollectRaidTopData GetMasterTopAnimationData() => CollectRaidUtil.GetMasterTopAnimationData();

        /// <summary>
        /// セグメント演出を再生する必要があるかどうか
        /// </summary>
        public bool IsNeedPlaySegmentAnimation()
        {

            #if CYG_DEBUG

            if (DebugPageCollectRaid.FORCE_PLAY_SEGMENT_ANIMATION)
            {
                return true;
            }

            #endif

            var masterTopData = GetMasterTopAnimationData();
            if (masterTopData == null)
            {
                return false;
            }

            switch ((CollectRaidUtil.TopAnimationTrigger)masterTopData.ConditionType)
            {
                case CollectRaidUtil.TopAnimationTrigger.ArchiveGeneralReward:
                    // 対象の報酬獲得演出をこの後すぐ出す必要があればtrue
                    return GetUnnoticedRewardIdList().Any(rewardId => rewardId == masterTopData.ConditionValue);
                case CollectRaidUtil.TopAnimationTrigger.ReadStory:
                    // 対象のストーリーをこの後すぐ再生する必要があればtrue
                    var storyDataNeedPlay = GetMasterEventStoryDataNeedPlay();
                    return masterTopData.ConditionValue == storyDataNeedPlay?.Id;
            }

            return false;
        }

        #endregion
    }
}
