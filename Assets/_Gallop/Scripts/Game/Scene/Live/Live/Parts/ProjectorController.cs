using UnityEngine;
using Gallop.Live.Cutt;
using Gallop.RenderPipeline;

namespace Gallop.Live
{
    [AddComponentMenu("")]
    public class ProjectorController : MonoBehaviour
    {
        private string _projectorName = string.Empty;
        private int _projectorNameHash = 0;

        private Texture[] _projectorTextureArray = null;
        public Texture[] ProjectorTextureArray
        {
            set => _projectorTextureArray = value;
        }

        private Transform _transform = null;
        private Renderer[] _rendererArray = null;

        /// <summary>
        /// 使用されるマテリアル
        /// 初期化のタイミングはSetMaterial関数
        /// </summary>
        private Material _material = null;

        private int _renderQueue = (int)UnityEngine.Rendering.RenderQueue.Transparent;
        private Animation _animation = null;
        private string[] _animationClipNameArray = new string[0];

        //URP:置き換え対応
        //private MaterialPropertyBlock _materialPropertyBlock = null;

        private bool _isEnabledRendererArray = false;
        private bool _isEnabledMaterial = false;
        private bool _isInitialized = false;

        //URP:置き換え対応
        private void OnDestroy()
        {
            RenderUtils.Destroy(ref _material);

            if (_isEnabledRendererArray)
            {
                for (int i = 0; i < _rendererArray.Length; ++i)
                {
                    if (_rendererArray[i].material != null)
                    {
                        Material.Destroy(_rendererArray[i].material);
                        _rendererArray[i].material = null;
                    }
                }
                _isEnabledRendererArray = false;
            }

            _isInitialized = false;
        }

        public void Initialize(ref string projectorName)
        {
            _projectorName = projectorName;
            _projectorNameHash = FNVHash.Generate(_projectorName);
            Initialize();
        }

        public void Initialize()
        {
            _transform = this.transform;

            _rendererArray = GetComponentsInChildren<Renderer>();
            /*
            if (_rendererArray != null)
            {
                //ProjectorControllerはMaterialが設定されていないので、初期化時に設定できない
                //またStageControllerでマテリアル収集が行われるが、ProjectorControllerは除外しないといけないので設定してはいけない
            }
            */
            _isEnabledRendererArray = ((_rendererArray != null) && (_rendererArray.Length > 0));
            if (_isEnabledRendererArray)
            {
                for (int i = 0; i < _rendererArray.Length; ++i)
                {
                    // この段階でプレハブのMeshRendererのマテリアルは状態問わずインスタンス化されている。
                    // Materialが空の場合は"Default-Material (Instance)"
                    // （※コメントとして残しておく）
                    //if (_rendererArray[i].material != null)
                    //{
                    //    _rendererArray[i].material = Material.Instantiate(_rendererArray[i].material);
                    //}
                    _rendererArray[i].enabled = false;
                }
            }

            _animation = GetComponent<Animation>();
            if (_animation != null)
            {
                int clipCount = _animation.GetClipCount();
                if (clipCount > 0)
                {
                    _animationClipNameArray = new string[clipCount];
                    int i = 0;
                    foreach (AnimationState anim in _animation)
                    {
                        _animationClipNameArray[i] = anim.clip.name;
                        i++;
                    }
                }
            }
#if CYG_DEBUG
            if (_animationClipNameArray.Length <= 0)
            {
                Debug.LogWarning("アニメーションが見つかりませんでした。");
            }
#endif
            //URP:置き換え対応
            /*
            _materialPropertyBlock = new MaterialPropertyBlock();
            _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MulColor0), GameDefine.COLOR_WHITE);
            _materialPropertyBlock.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ColorPower), 1.0f);
            */
            _isInitialized = true;
        }

        /// <summary>
        /// マテリアル周りの初期化
        /// </summary>
        /// <param name="material"></param>
        /// <param name="renderQueue"></param>
        public void SetMaterial(Material material, int renderQueue)
        {
            if ((!_isInitialized) || (!_isEnabledRendererArray))
            {
                return;
            }
            if (material == null)
            {
                return;
            }


            _material = Material.Instantiate(material);
            // 描画順を整理してDynamicBatchを効きやすくする。
            _renderQueue = renderQueue;
            _material.renderQueue = _renderQueue;

            for (int i = 0; i < _rendererArray.Length; ++i)
            {
                // 既存のインスタンス化されたマテリアルを明示的に破棄する。
                if (_rendererArray[i].material != null)
                {
                    Material.Destroy(_rendererArray[i].material);
                    _rendererArray[i].material = null;
                }

                //URP:置き換え対応
                _rendererArray[i].material = _material;
                //_rendererArray[i].sharedMaterial = _material;
            }

            _isEnabledMaterial = true;
        }

        /// <summary>
        /// 状態を更新する
        /// </summary>
        /// <param name="updateInfo"></param>
        public void UpdateStatus(ref ProjectorUpdateInfo updateInfo)
        {
            if (!_isInitialized)
            {
                Debug.LogWarning("初期化が行われていません。");
                return;
            }
            if (updateInfo.nameHash != _projectorNameHash)
            {
                return;
            }

            UpdateTransform(ref updateInfo);

            // マテリアルの更新
            bool isValidMaterial = false;
            if (_projectorTextureArray != null)
            {
                int textureIndex = updateInfo.materialId;
                if ((textureIndex >= 0) && (textureIndex < _projectorTextureArray.Length))
                {
                    isValidMaterial = UpdateMaterial(ref updateInfo, _projectorTextureArray[textureIndex]);
                }
#if CYG_DEBUG
                else
                {
                    Debug.LogWarningFormat("無効なマテリアル番号が指定されています。({0})", textureIndex);
                }
#endif
            }
#if CYG_DEBUG
            else
            {
                Debug.LogWarning("テクスチャが設定されていません。");
            }
#endif

            // アニメーションの更新
            bool isValidAnimation = UpdateAnimation(ref updateInfo);

            // 有効なパラメータが入力されていた場合のみ表示する
            UpdateEnable(isValidMaterial && isValidAnimation);
        }

        public void UpdateTransform(ref ProjectorUpdateInfo updateInfo)
        {
            _transform.localPosition = updateInfo.position;

            Vector3 _workVector;

            _workVector.x = 0.0f;
            _workVector.y = updateInfo.rotate % 360.0f;
            _workVector.z = 0.0f;
            _transform.eulerAngles = _workVector;

            _workVector.x = updateInfo.size.x;
            _workVector.y = 1.0f;
            _workVector.z = updateInfo.size.y;
            _transform.localScale = _workVector;
        }

        public bool UpdateMaterial(ref ProjectorUpdateInfo updateInfo, Material material)
        {
            if ((!_isInitialized) || (!_isEnabledRendererArray))
            {
                return false;
            }
            if (material == null)
            {
                return false;
            }

            //URP:置き換え対応
            //materialの内容をコピー(シェーダー含め)
            RenderUtils.CopyMaterial(material, _material);
            RenderUtils.SetColor(_material, ShaderManager.PropertyId._MulColor0, updateInfo.color1);
            RenderUtils.SetFloat(_material, ShaderManager.PropertyId._ColorPower, updateInfo.power);
            //初期化でmaterial設定が行えないのでここで行う
            //Rendererで描画する内容は同じなので共有マテリアルで良いはず
            for (int i = 0; i < _rendererArray.Length; ++i)
            {
                _rendererArray[i].material = _material;
            }

            /*
            _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MulColor0), updateInfo.color1);
            _materialPropertyBlock.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ColorPower), updateInfo.power);

            for (int i = 0; i < _rendererArray.Length; ++i)
            {
                _rendererArray[i].sharedMaterial = material;
                _rendererArray[i].SetPropertyBlock(_materialPropertyBlock);
            }
            */

            return true;
        }

        public bool UpdateMaterial(ref ProjectorUpdateInfo updateInfo, Texture texture)
        {
            if ((!_isInitialized) || (!_isEnabledRendererArray))
            {
                return false;
            }
            if (texture == null)
            {
                return false;
            }

            if (_isEnabledMaterial)
            {
                LiveDefine.TrySetLightBlendModeMaterialProperty(updateInfo.LightBlendMode, _material);
            }
            //URP:置き換え対応
            _material.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex), texture);
            RenderUtils.SetColor(_material, ShaderManager.PropertyId._MulColor0, updateInfo.color1);
            RenderUtils.SetFloat(_material, ShaderManager.PropertyId._ColorPower, updateInfo.power);
            for (int i = 0; i < _rendererArray.Length; ++i)
            {
                _rendererArray[i].material = _material;
            }

            /*
            _materialPropertyBlock.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex), texture);
            _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MulColor0), updateInfo.color1);
            _materialPropertyBlock.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ColorPower), updateInfo.power);

            for (int i = 0; i < _rendererArray.Length; ++i)
            {
                _rendererArray[i].SetPropertyBlock(_materialPropertyBlock);
            }
            */

            return true;
        }

        public bool UpdateAnimation(ref ProjectorUpdateInfo updateInfo)
        {
            if (!_isInitialized)
            {
                return false;
            }

            int motionIndex = updateInfo.motionId;
            if ((motionIndex >= 0) && (motionIndex < _animationClipNameArray.Length))
            {
                string animationClipName = _animationClipNameArray[motionIndex];

                if (!_animation.IsPlaying(animationClipName))
                {
                    _animation.Stop();
                    _animation.Play(animationClipName);
                }

                AnimationState animationState = _animation[animationClipName];
                animationState.time = updateInfo.progressTime * updateInfo.speed;
                animationState.enabled = true;
                _animation.Sample();
                animationState.enabled = false;
                return true;
            }
#if CYG_DEBUG
            else
            {
                Debug.LogWarningFormat("無効なモーション番号が指定されています。({0})", motionIndex);
            }
#endif
            return false;
        }

        public void UpdateEnable(bool enable)
        {
            if ((!_isInitialized) || (!_isEnabledRendererArray))
            {
                return;
            }

            for (int i = 0; i < _rendererArray.Length; ++i)
            {
                _rendererArray[i].enabled = enable;
            }
        }
    }
}
