using UnityEngine;
using Gallop.Live.Cutt;
using Gallop.RenderPipeline;

namespace Gallop.Live
{
    [AddComponentMenu("")]
    public class Spotlight3dController : MonoBehaviour
    {
        private Transform _transform = null;
        private Renderer _renderer = null;
        private Material[] _materialArray;
        //URP:置き換え対応
        //private MaterialPropertyBlock _materialPropertyBlock = null;
        private BillboardController _billboardController = null;
        private Vector3 _offset = Math.VECTOR3_ZERO;

        private int _timelineIndex = -1;
        public int TimelineIndex => _timelineIndex;
        private Vector3 _position = Math.VECTOR3_ZERO;
        private float _localHeight = 0f;
        private Quaternion _localRotation = Math.QUATERNION_IDENTITY;
        private Vector3 _localScale = Math.VECTOR3_ONE;
        private float _colorPower = 0f;

        private bool _isInitialized = false;

        public void Initialize(int timelineIndex, Transform targetCameraTransform)
        {
            _isInitialized = false;

            _transform = this.transform;

            _timelineIndex = timelineIndex;

            _position = Math.VECTOR3_ZERO;
            _localHeight = 0f;
            _localRotation = Math.QUATERNION_IDENTITY;
            _localScale = Math.VECTOR3_ONE;

            _renderer = GetComponentInChildren<Renderer>();
            if (_renderer == null)
            {
                Debug.LogWarning("初期化失敗：Rendererコンポーネントが見つかりませんでした。");
                return;
            }
            _renderer.enabled = false;

            _billboardController = GetComponentInChildren<BillboardController>();
            if (_billboardController != null)
            {
                _billboardController.Initialize(targetCameraTransform);
            }

            //URP:置き換え対応
            _colorPower = 0f;
            _materialArray = RenderUtils.GetMaterialArray(_renderer);
            RenderUtils.SetColor(_materialArray, ShaderManager.PropertyId._MulColor0, GameDefine.COLOR_WHITE);
            RenderUtils.SetFloat(_materialArray, ShaderManager.PropertyId._ColorPower, _colorPower);
            /*
            _materialPropertyBlock = new MaterialPropertyBlock();
            _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MulColor0), GameDefine.COLOR_WHITE);
            _colorPower = 0f;
            _materialPropertyBlock.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ColorPower), _colorPower);
            */


            _isInitialized = true;
        }

        public void AlterLateUpdate()
        {
            if (!_isInitialized)
            {
                return;
            }

            if (_colorPower > 0.0f)
            {
                if (!_renderer.enabled)
                {
                    _renderer.enabled = true;
                }

                //ビルボードコントローラーは内部で_transform.positionを見ているので先に位置を決定しないとずれる
                _offset.y = _localHeight;
                _transform.position = _position + _localRotation * _offset;

                if (_billboardController != null)
                {
                    _transform.localRotation = Math.QUATERNION_IDENTITY;
                    _billboardController.AlterLateUpdate();
                }

                _transform.localRotation = _transform.localRotation * _localRotation;
                _transform.localScale = _localScale;

                //URP:置き換え対応
                //_renderer.SetPropertyBlock(_materialPropertyBlock);
            }
            else
            {
                if (_renderer.enabled)
                {
                    _renderer.enabled = false;
                }
            }
        }

        public void SetUpdateInfo(ref Spotlight3dUpdateInfo updateInfo)
        {
            if (!_isInitialized)
            {
                return;
            }
            _colorPower = (updateInfo.isActive && (updateInfo.colorPower > 0f)) ? updateInfo.colorPower : 0f;

            _position = updateInfo.position;
            _localHeight = updateInfo.localHeight;
            _localRotation = Quaternion.Euler(updateInfo.rotation);
            _localScale = updateInfo.scale;

            //URP:置き換え対応
            /*
            _materialPropertyBlock.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MulColor0), updateInfo.color);
            _materialPropertyBlock.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ColorPower), _colorPower);
            */
            RenderUtils.SetColor(_materialArray, ShaderManager.PropertyId._MulColor0, updateInfo.color);
            RenderUtils.SetFloat(_materialArray, ShaderManager.PropertyId._ColorPower, _colorPower);
            if (_billboardController != null)
            {
                _billboardController.enabled = updateInfo.IsEnabledBillboard;
                _billboardController.TargetCameraTransform = updateInfo.TargetCameraTransform;
            }
        }

        private void OnDestroy()
        {
            RenderUtils.Destroy(ref _materialArray);
        }
    }
}
