using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// View：ルーレットダービー
    /// </summary>
    [AddComponentMenu("")]
    public class RouletteDerbyView : ViewBase
    {
        [SerializeField, RenameField]
        public RouletteDirector2D Director2D;

        [SerializeField, RenameField]
        public ButtonCommon CoinInfoButton;

        [SerializeField, RenameField]
        public TextCommon HeaderCoinNumText;

        [SerializeField, RenameField]
        public TextCommon SheetNumText;

        [SerializeField, RenameField]
        public ButtonCommon ExchangeSheetButton;

        [SerializeField, RenameField]
        public ButtonCommon RouletteRewardDetailButton;

        [SerializeField, RenameField]
        public ButtonCommon BingoRewardDetailButton;

        [SerializeField, RenameField]
        public RawImageCommon BgImage;

        [SerializeField, RenameField]
        public UITouchArea BingoSheetArea;

        [Serial<PERSON><PERSON><PERSON>, <PERSON>ameField]
        public ButtonCommon OptionButton;
    }

    /// <summary>
    /// ViewController：ルーレットダービー
    /// </summary>
    public class RouletteDerbyViewController : ViewControllerBase<RouletteDerbyView>
    {
        #region Const

        private const string CUE_SHEET_NAME_BGM = "snd_bgm_GM068";
        private const string CUE_NAME_BGM = "snd_bgm_GM068_roulette";

        #endregion

        #region Property

        /// <summary>
        /// SceneController
        /// </summary>
        private StoryEventSceneController _sceneController;

        /// <summary>
        /// 購読イベント
        /// </summary>
        private readonly CompositeDisposable _subscription = new CompositeDisposable();

        /// <summary>
        /// 現在のステート
        /// </summary>
        private RouletteDefine.State _currentState;

        /// <summary>
        /// もともとのCharacterBg表示状態
        /// </summary>
        private bool _prevCharacterBgVisible;

        /// <summary>
        /// もともとのBgControllerのEnable
        /// </summary>
        private bool _prevBgCameraEnable;

        /// <summary>
        /// もともとのBgCanvasのアクティブ
        /// </summary>
        private bool _prevBgCanvasActive;

        /// <summary>
        /// 3D演出
        /// </summary>
        private GameObject _rouletteDirector3dObj = null;
        private RouletteDirector3D _rouletteDirector3d = null;

        /// <summary>
        /// 3D演出に設定する情報
        /// </summary>
        private RouletteDirector3D.Context _context3d;

        /// <summary>
        /// 3D演出の描画先
        /// </summary>
        private RenderTexture _renderTexture = null;
        
        #endregion

        #region Method

        #region Override

        public override void RegisterDownload(DownloadPathRegister register)
        {
            RouletteFlashAnimation.RegisterDownload(register);

            base.RegisterDownload(register);

            // ビンゴシート毎にキャラを変える場合は修正が必要
            {
                int storyEventId = WorkDataManager.Instance.StoryEventData.StoryEventId;
                var masterData = MasterDataManager.Instance.masterStoryEventRouletteBingo.GetWithStoryEventIdOrderByIdAsc(storyEventId);
                if (masterData != null)
                {
                    _context3d.CharaId = masterData.CharacterId;
                    _context3d.DressId = masterData.DressId;
                    RouletteDirector3D.RegisterDownload(register, _context3d);
                }

#if CYG_DEBUG
                if (_context3d.CharaId == 0)
                {
                    Debug.LogWarning("RouletteDirector3D: ミニキャラのCharaIDが指定されていません。(0)");
                    _context3d.CharaId = 1001;
                }
                if (_context3d.DressId == 0)
                {
                    Debug.LogWarning("RouletteDirector3D: ミニキャラのDressIDが指定されていません。(0)");
                    _context3d.DressId = 2;
                }
#endif
            }
            RouletteDirector2D.RegisterDownload(register);
            
            AudioManager.Instance.RegisterDownloadByCueSheets(register, new List<string>
            {
                AudioDefine.GetAudioIdData(AudioId.SFX_UI_DECIDE_M_01)._cueSheet,
            }, AudioManager.SubFolder.Se);

            register.RegisterPath(ResourcePath.ROULETTE_BG_PATH);

            AudioManager.Instance.RegisterDownloadByCueSheets(register, new List<string>() {CUE_SHEET_NAME_BGM}, AudioManager.SubFolder.Bgm);
        }

        public override IEnumerator InitializeView()
        {
            _sceneController = GetSceneController() as StoryEventSceneController;

            _subscription.Clear();
            if (_sceneController != null)
            {
                _subscription.Add(_sceneController.RequestNextStateSubject.Subscribe(OnRequestNextStateEvent));
                _subscription.Add(_sceneController.ManualSheetChangeSubject.Subscribe(OnManualSheetChangeEvent));
            }

            Initialize2D();
            Initialize3D();

            yield return base.InitializeView();
        }

        public override IEnumerator InitializeEachPlayIn()
        {
            // インデックスAPI
            var waitApi = true;
            SendIndexApi(() => { waitApi = false; });
            while (waitApi)
            {
                yield return null;
            }

            UpdateInfoUI();
            yield return base.InitializeEachPlayIn();
        }

        public override IEnumerator PlayInView()
        {
            AudioManager.Instance.PlayBgmFromName(CUE_SHEET_NAME_BGM, CUE_NAME_BGM);

            UIManager.Instance.LockGameCanvas();

            // 3D表示。State.PlayInになる前に設定。
            _rouletteDirector3d.PlayInView();
            SetBgOnPlayIn();
            UpdateInfoUI();
            ChangeState(RouletteDefine.State.PlayIn);
            yield return base.PlayInView();

            // 3D制御の都合でフッターが押せてはまずいタイミングなので、入力禁止のまま一瞬待つ。（演出を見せる意図もある）
            const float WAIT_TIME = 0.3f;
            yield return new WaitForSeconds(WAIT_TIME);

            UIManager.Instance.UnlockGameCanvas();

            // 初回遷移時のガイド表示
            if (WorkDataManager.Instance.RouletteDerbyData.IsViewAccessFirst)
            {
                DialogTutorialGuide.PushDialogWithReadCheck(DialogTutorialGuide.TutorialGuideId.RouletteDerby, ChangeToReadyPre);
            }
            else
            {
                ChangeToReadyPre();
            }

            void ChangeToReadyPre()
            {
                ChangeState(RouletteDefine.State.ReadyPre);
            }
        }

        public override IEnumerator PlayOutView()
        {
            // 3D非表示
            _rouletteDirector3d.PlayOutView();

            ChangeState(RouletteDefine.State.PlayOut);
            
            // ミッション達成通知が残っているならここで表示
            UIManager.Instance.NoticeMissionClearUI.Show();
            
            yield return base.PlayOutView();

            ResetBgOnPlayOut();
        }

        public override IEnumerator FinalizeView()
        {
            // リソース破棄
            if (_rouletteDirector3d != null)
            {
                _rouletteDirector3d.FinalizeView();
                _rouletteDirector3d = null;
            }
            if (_rouletteDirector3dObj != null)
            {
                GameObject.Destroy(_rouletteDirector3dObj);
                _rouletteDirector3dObj = null;
            }

            if (_renderTexture != null)
            {
                _renderTexture.Release();
                RenderTexture.Destroy(_renderTexture);
                _renderTexture = null;
            }

            yield return base.FinalizeView();
        }

        public override void OnClickBackButton()
        {
            OnClickOsBackKey();
        }

        public override void OnClickOsBackKey()
        {
            switch (_currentState)
            {
                case RouletteDefine.State.PlayIn:
                case RouletteDefine.State.ReadyPre:
                case RouletteDefine.State.Ready:
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.StoryEventTop);
                    break;
                case RouletteDefine.State.Start:
                case RouletteDefine.State.CanSKip:
                case RouletteDefine.State.LastSpurt:
                case RouletteDefine.State.Exhausted:
                case RouletteDefine.State.Goal:
                case RouletteDefine.State.GoalMotion:
                case RouletteDefine.State.ShowRouletteReward:
                case RouletteDefine.State.HideRouletteReward:
                case RouletteDefine.State.ShowBingoSheet:
                case RouletteDefine.State.Bingo:
                case RouletteDefine.State.ShowBingoReward:
                case RouletteDefine.State.WaitForCloseBingoSheet:
                case RouletteDefine.State.CloseBingoSheet:
                case RouletteDefine.State.ExecChangeSheet:
                case RouletteDefine.State.Finish:
                case RouletteDefine.State.ReliefDialog:
                case RouletteDefine.State.ContinuousRouletteStop:
                case RouletteDefine.State.FinishToReady:
                case RouletteDefine.State.PlayOut:
                    break;
            }
        }

        #endregion

        #region 初期化

        /// <summary>
        /// 初期化：2D
        /// </summary>
        private void Initialize2D()
        {
            InitializeUI();
            _view.Director2D.Initialize(SkipToGoalMotion, OpenOptionDialogByGuide);

            var rect = _view.Director2D.Rendered3dImage.rectTransform.rect;
            //URP:置き換え対応
            //内部でDepthバッファは作られるので、不要
            //_renderTexture = new RenderTexture((int) rect.width, (int) rect.height, GallopFrameBuffer.DEPTH_BUFFER, RenderTextureFormat.ARGB32);
            _renderTexture = new RenderTexture((int)rect.width, (int)rect.height, 0, RenderTextureFormat.ARGB32);
#if CYG_DEBUG
            _renderTexture.name = "Roulette3d_Texture";
#endif // CYG_DEBUG
            _renderTexture.useMipMap = false;
            _renderTexture.autoGenerateMips = false;
            _renderTexture.filterMode = FilterMode.Bilinear;
            //URP:置き換え対応
            /*
            _renderTexture.antiAliasing = GraphicSettings.Instance.Get3DAntiAliasingLevel(true);
            */
            _renderTexture.Create();
            _view.Director2D.Rendered3dImage.texture = _renderTexture;
        }

        /// <summary>
        /// 初期化：3D
        /// </summary>
        private void Initialize3D()
        {
            var pfb = ResourceManager.LoadOnView<GameObject>(ResourcePath.STORY_EVENT_ROULETTE_DIRECTOR_3D);
            _rouletteDirector3dObj = GameObject.Instantiate(pfb, _sceneController.GetSceneTransform());
            _rouletteDirector3d = _rouletteDirector3dObj.GetComponent<RouletteDirector3D>();
            DebugUtils.Assert(_rouletteDirector3d != null, "RouletteDirector3D.prefabからRouletteDirector3Dを取得できないのは想定外。データの確認をお願いします。");
            _rouletteDirector3d.InitializeView(_context3d, _renderTexture);
        }

        #endregion

        #region イベント

        /// <summary>
        /// コールバック：ステート更新要求
        /// </summary>
        /// <param name="info"></param>
        private void OnRequestNextStateEvent(RouletteEventInfoForRequestNextState info)
        {
            var workData = WorkDataManager.Instance.RouletteDerbyData;
            switch (_currentState)
            {
                case RouletteDefine.State.ReadyPre:
                    ChangeState(RouletteDefine.State.Ready);
                    break;
                case RouletteDefine.State.Ready:
                    if (workData.CanExecRoulette == false)
                    {
                        Debug.LogWarning("ルーレットコインの数が足りません");
                        return;
                    }

                    SendExecApi(() =>
                    {
                        UpdateInfoUI(false);
                        ChangeState(RouletteDefine.State.Start);
                    });
                    break;
                case RouletteDefine.State.Start:
                    ChangeState(RouletteDefine.State.CanSKip);
                    break;
                case RouletteDefine.State.CanSKip:
                    ChangeState(RouletteDefine.State.LastSpurt);
                    break;
                case RouletteDefine.State.LastSpurt:
                    // 6～8着の時はExhaustedに遷移
                    int order = workData.DirectorOrder;
                    if (RouletteDefine.CHARA_EXHAUSTED_ORDER_MIN <= order && order <= RouletteDefine.CHARA_EXHAUSTED_ORDER_MAX)
                    {
                        ChangeState(RouletteDefine.State.Exhausted);
                    }
                    else
                    {
                        ChangeState(RouletteDefine.State.Goal);
                    }

                    break;
                case RouletteDefine.State.Exhausted:
                    ChangeState(RouletteDefine.State.Goal);
                    break;
                case RouletteDefine.State.Goal:
                    ChangeState(RouletteDefine.State.GoalMotion);
                    break;
                case RouletteDefine.State.GoalMotion:
                    bool haveRouletteReward = workData.CurrentRouletteReward != null;
                    ChangeState(haveRouletteReward ? RouletteDefine.State.ShowRouletteReward : RouletteDefine.State.Finish);
                    break;
                case RouletteDefine.State.ShowRouletteReward:
                    ChangeState(RouletteDefine.State.HideRouletteReward);
                    break;
                case RouletteDefine.State.HideRouletteReward:
                    ChangeState(workData.IsAlreadyGotOrder ? RouletteDefine.State.Finish : RouletteDefine.State.ShowBingoSheet);
                    break;
                case RouletteDefine.State.ShowBingoSheet:
                    var bingoRewardArray = workData.CurrentBingoReward;
                    bool isBingo = bingoRewardArray != null && bingoRewardArray.Length > 0;
                    ChangeState(isBingo ? RouletteDefine.State.Bingo : RouletteDefine.State.WaitForCloseBingoSheet);
                    break;
                case RouletteDefine.State.Bingo:
                    ChangeState(RouletteDefine.State.BingoLineTextIn);
                    break;
                case RouletteDefine.State.BingoLineTextIn:
                    ChangeState(RouletteDefine.State.BingoLineTextInEnd);
                    break;
                case RouletteDefine.State.BingoLineTextInEnd:
                    ChangeState(RouletteDefine.State.ShowBingoReward);
                    break;
                case RouletteDefine.State.ShowBingoReward:
                    ChangeState(RouletteDefine.State.WaitForCloseBingoSheet);
                    break;
                case RouletteDefine.State.WaitForCloseBingoSheet:
                    ChangeState(RouletteDefine.State.CloseBingoSheet);
                    break;
                case RouletteDefine.State.CloseBingoSheet:
                    ChangeState(workData.NeedChangeSheet ? RouletteDefine.State.ExecChangeSheet : RouletteDefine.State.Finish);
                    break;
                case RouletteDefine.State.ExecChangeSheet:
                    ChangeState(RouletteDefine.State.Finish);
                    break;
                case RouletteDefine.State.Finish:
                    // 天井モードになったかチェック
                    var rouletteData = workData.CurrentMasterRouletteBingoData;
                    bool justChangeToRelief = rouletteData != null 
                                              && workData.ExecCount == rouletteData.RouletteMaxNum 
                                              && workData.NeedChangeSheet == false;

                    // 「連続でルーレットを回す」がONかつ設定が「次の1ラインBINGOまで」か「8ラインBINGO達成まで」の状態でコイン不足で停止したかチェック
                    bool isContinuousRouletteStop = false;
                    {
                        var saveLoader = SaveDataManager.Instance.SaveLoader;
                        var isEnableContinuousRoulette = saveLoader.StoryEventIsEnableContinuousRoulette;
                        var continuousSetting = saveLoader.StoryEventContinuousRouletteSetting;

                        if (isEnableContinuousRoulette)
                        {
                            if (continuousSetting == RouletteDefine.ContinuousSetting.Until1LineBingo ||
                                continuousSetting == RouletteDefine.ContinuousSetting.Until8LineBingo)
                            {
                                if (workData.RouletteStopType == RouletteDefine.RouletteStopType.CoinEmpty)
                                {
                                    isContinuousRouletteStop = true;
                                }
                            }
                        }
                    }

                    var nextState = RouletteDefine.State.FinishToReady;
                    if (justChangeToRelief)
                    {
                        nextState = RouletteDefine.State.ReliefDialog;
                    }
                    else if (isContinuousRouletteStop)
                    {
                        nextState = RouletteDefine.State.ContinuousRouletteStop;
                    }

                    WorkDataManager.Instance.RouletteDerbyData.AddAlreadyGotOrderList();
                    WorkDataManager.Instance.RouletteDerbyData.ClearTempInfo();
                    ChangeState(nextState);
                    break;
                case RouletteDefine.State.ReliefDialog:
                    ChangeState(RouletteDefine.State.FinishToReady);
                    break;
                case RouletteDefine.State.ContinuousRouletteStop:
                    ChangeState(RouletteDefine.State.FinishToReady);
                    break;
                case RouletteDefine.State.FinishToReady:
                    ChangeState(RouletteDefine.State.ReadyPre);
                    UpdateInfoUI();
                    break;
            }
        }

        /// <summary>
        /// ステート設定
        /// </summary>
        /// <param name="nextState"></param>
        /// <param name="isSkip"></param>
        private void ChangeState(RouletteDefine.State nextState, bool isSkip = false)
        {
            _currentState = nextState;
            _sceneController?.TriggerStateUpdateEventForStateAdministrator(nextState, isSkip);
        }

        /// <summary>
        /// GoalMotionへスキップ
        /// </summary>
        private void SkipToGoalMotion()
        {
            ChangeState(RouletteDefine.State.GoalMotion, isSkip: true);
        }

        /// <summary>
        /// イベント：手動シート交換
        /// </summary>
        /// <param name="info"></param>
        private void OnManualSheetChangeEvent(RouletteEventInfoForManualSheetChange info)
        {
            UpdateInfoUI();
        }

        /// <summary>
        /// （誘導された流れで）オプションダイアログを開く
        /// </summary>
        private void OpenOptionDialogByGuide()
        {
            DialogRouletteOption.Open(
                forceEnableContinuousRoulette: true, // 「連続でルーレットを回す」を強制的にONにする
                onBeginClose: (withDestroy) =>
                {
                    // 閉じたら中央ボタンのUIを更新
                    _view.Director2D.RefreshCenterButtonByOption();
                });
        }


        #endregion

        #region UI更新

        /// <summary>
        /// 背景セット
        /// </summary>
        private void SetBgOnPlayIn()
        {
            // 汎用BG処理は使わない
            _prevCharacterBgVisible = BGManager.Instance.CharacterBg.IsVisible;
            _prevBgCameraEnable = BGManager.GetBgCameraEnable();
            _prevBgCanvasActive = BGManager.GetBgCanvasEnable();
            BGManager.Instance.CharacterBg.SetCharaVisible(false);
            BGManager.SetBgCameraEnable(false);
            BGManager.SetBgCanvasEnable(false);
        }

        /// <summary>
        /// 背景リセット
        /// </summary>
        private void ResetBgOnPlayOut()
        {
            // 汎用BG設定をもとに戻す
            BGManager.Instance.CharacterBg.SetCharaVisible(_prevCharacterBgVisible);
            BGManager.SetBgCameraEnable(_prevBgCameraEnable);
            BGManager.SetBgCanvasEnable(_prevBgCanvasActive);
        }

        /// <summary>
        /// ルーレット情報UIを初期化
        /// </summary>
        private void InitializeUI()
        {
            _view.CoinInfoButton.SetOnClick(OnClickCoinInfoButton);
            _view.ExchangeSheetButton.SetOnClick(OnClickExchangeSheetButton);
            _view.RouletteRewardDetailButton.SetOnClick(OnClickRouletteRewardDetailButton);
            _view.BingoRewardDetailButton.SetOnClick(OnClickBingoRewardDetailButton);
            _view.BingoSheetArea.SetClickCallback(_ => OnClickTopBingoSheetButton());
            _view.BgImage.texture = ResourceManager.LoadOnScene<Texture2D>(ResourcePath.ROULETTE_BG_PATH);
            _view.OptionButton.SetOnClick(OnClickOptionButton);
        }

        /// <summary>
        /// ルーレット情報を更新
        /// </summary>
        /// <param name="isUIInteractable"></param>
        private void UpdateInfoUI(bool isUIInteractable = true)
        {
            _view.HeaderCoinNumText.text = WorkDataManager.Instance.RouletteDerbyData.CoinNum.ToCommaSeparatedString();
            _view.SheetNumText.text = TextUtil.Format(TextId.StoryEvent0031.Text(), WorkDataManager.Instance.RouletteDerbyData.BingoSheetNum);

            UpdateUIInteractive(isUIInteractable);
        }

        /// <summary>
        /// ボタンの使用可否を更新
        /// </summary>
        /// <param name="isInteractable"></param>
        private void UpdateUIInteractive(bool isInteractable)
        {
            bool canChangeSheet = WorkDataManager.Instance.RouletteDerbyData.CanChangeSheet;

            _view.CoinInfoButton.SetInteractable(isInteractable);
            _view.ExchangeSheetButton.SetInteractable(isInteractable && canChangeSheet);
            _view.RouletteRewardDetailButton.SetInteractable(isInteractable);
            _view.BingoRewardDetailButton.SetInteractable(isInteractable);
            _view.OptionButton.SetInteractable(isInteractable);
            _view.BingoSheetArea.enabled = isInteractable;
            UIManager.Footer.SetInteractiveAllFooterButtons(isInteractable);
            UIManager.CommonHeaderTitle.SetCurrentInfoButtonInteractable(isInteractable);
            UIManager.Instance.FooterBackButton.SetInteractable(isInteractable);

            _view.ExchangeSheetButton.SetNotificationMessage(canChangeSheet ? string.Empty : TextId.StoryEvent0072.Text());
        }

        /// <summary>
        /// 押下：コイン情報ボタン
        /// </summary>
        private void OnClickCoinInfoButton()
        {
            DialogItemInformation.Open(GameDefine.ItemCategory.EVENT_RESOURCE, GameDefine.STORY_EVENT_ROULETTE_COIN_ITEM_ID);
        }

        /// <summary>
        /// 押下：シート交換ボタン
        /// </summary>
        private void OnClickExchangeSheetButton()
        {
            DialogRouletteBingoSheetChange.Open(DialogRouletteBingoSheetChange.Type.ManualChange,
                onComplete: () =>
                {
                    // 中央ボタンのUIを更新（「10PLAYまとめて」設定の場合、回せる回数が変化する可能性があるため）
                    _view.Director2D.RefreshCenterButtonByOption();

                    _sceneController.TriggerManualSheetChangeEvent();
                });
        }

        /// <summary>
        /// 押下：ルーレット報酬一覧ボタン
        /// </summary>
        private void OnClickRouletteRewardDetailButton()
        {
            DialogRouletteReward.Open();
        }

        /// <summary>
        /// 押下：ビンゴ報酬一覧ボタン
        /// </summary>
        private void OnClickBingoRewardDetailButton()
        {
            DialogRouletteBingoReward.Open();
        }

        /// <summary>
        /// 押下：トップ画面ビンゴシート
        /// </summary>
        private void OnClickTopBingoSheetButton()
        {
            if (_view.BingoSheetArea.IsLock())
            {
                return;
            }

            AudioManager.Instance.PlaySe(AudioId.SFX_UI_DECIDE_M_01);
            _view.Director2D.OpenBingSheetDialog();
        }

        /// <summary>
        /// 押下：オプションボタン
        /// </summary>
        private void OnClickOptionButton()
        {
            // 「オプション」ダイアログを開く
            DialogRouletteOption.Open(
                onBeginClose: (withDestroy) =>
                {
                    // 閉じたら中央ボタンのUIを更新
                    _view.Director2D.RefreshCenterButtonByOption();
                });
        }

        #endregion

        #region API

        /// <summary>
        /// インデックスAPI送信（/story_event/roulette）
        /// </summary>
        /// <param name="onFinish"></param>
        private static void SendIndexApi(Action onFinish)
        {
            new StoryEventRouletteRequest().Send((res) =>
            {
                WorkDataManager.Instance.StoryEventData.UpdateUserInfo(res.data);
                WorkDataManager.Instance.RouletteDerbyData.Update(res.data);
                onFinish?.Invoke();
            });
        }

        /// <summary>
        /// ルーレット実行API送信（/story_event/roulette_exec）
        /// </summary>
        /// <param name="onFinish"></param>
        private static void SendExecApi(Action onFinish)
        {
            var saveLoader = SaveDataManager.Instance.SaveLoader;
            RouletteDefine.ContinuousSetting continuousSetting = (saveLoader.StoryEventIsEnableContinuousRoulette) ? saveLoader.StoryEventContinuousRouletteSetting : RouletteDefine.ContinuousSetting.None;

            new StoryEventRouletteExecRequest()
            {
                roulette_coin_num = WorkDataManager.Instance.RouletteDerbyData.CoinNum,
                continuous_setting = (int)continuousSetting, // ルーレットを1回だけ回すか連続で回すか、連続で回すならどこまで回すか、の設定
            }.Send((res) =>
            {
                // 抽選した回数分、コインを消費
                if (res.data.order_list != null)
                {
                    for (int i = 0; i < res.data.order_list.Length; i++)
                    {
                        WorkDataManager.Instance.RouletteDerbyData.ReduceCoinNumForExec();
                    }
                }

                // 報酬を反映
                WorkDataUtil.SetRewardSummaryInfo(res.data?.reward_summary_info);

                // ルーレットのワークを更新
                WorkDataManager.Instance.RouletteDerbyData.Update(res.data);

                onFinish?.Invoke();
            });
        }

        /// <summary>
        /// ビンゴシート交換API送信（/story_event/roulette_change_sheet）
        /// </summary>
        /// <param name="onFinish"></param>
        public static void SendChangeSheetApi(Action onFinish)
        {
            new StoryEventRouletteChangeSheetRequest().Send((res) =>
            {
                WorkDataManager.Instance.RouletteDerbyData.Update(res.data);
                onFinish?.Invoke();
            });
        }

        #endregion

        #endregion
    }
}