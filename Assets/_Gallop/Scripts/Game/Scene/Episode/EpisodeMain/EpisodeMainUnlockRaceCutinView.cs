using System.Collections;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class EpisodeMainUnlockRaceCutinView : ViewBase
    {
        [field: SerializeField, RenameField]
        public PartsSingleModeSuccessionCutResult ResultParts { get; private set; }

        [field: SerializeField, RenameField]
        public ButtonCom<PERSON> { get; private set; }
    }

    public class EpisodeMainUnlockRaceCutinViewController : ViewControllerBase<EpisodeMainUnlockRaceCutinView>
    {
        public class ViewInfo : IViewInfo
        {
            public MasterMainStoryRaceData.MainStoryRaceData MainStoryRaceData { get; }

            public MasterMainStoryData.MainStoryData MainStoryData { get; }

            public ViewInfo(MasterMainStoryRaceData.MainStoryRaceData mainStoryRaceData, MasterMainStoryData.MainStoryData mainStoryData)
            {
                MainStoryRaceData = mainStoryRaceData;
                MainStoryData = mainStoryData;
            }
        }

        /// <summary>
        ///　アンロックレース継承演出の対象キャラ
        ///　現状はまだスペシャルウィークだけが対象
        /// </summary>
        private const int UNLOCK_RACE_MAIN_CHARA_ID = 1001;

        private CutInHelper _cutinHelper = null;

        private bool _isStopReserve = false;

        private bool _playUnlockCutin = false;

        private ViewInfo _viewInfo = null;

        /// <summary>
        /// 継承結果画面
        /// </summary>
        private SingleModeSuccessionResult _result = new SingleModeSuccessionResult();

        #region ベース呼び出し

        /// <summary>
        /// ダウンロード対象リソースの登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // 影
            Model.Component.ShadowController.RegisterDownload(register);

            // カット
            register.RegisterPath(ResourcePath.STORYRACE_UNLOCKRACE_CUT);
            
            // SE
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_UNLOCK_RACE_EVENT_01);

            // 結果画面のDL
            SingleModeSuccessionResult.RegisterDownload(register, UNLOCK_RACE_MAIN_CHARA_ID);
        }

        private void PlayUnlockCutin()
        {
            if(_cutinHelper == null || !_cutinHelper.IsInitialized)
            {
                Debug.LogWarning("カット再生処理が初期化出来ていない!");
                return;
            }

            _cutinHelper.OnEndAction += timelineController =>
            {
                _isStopReserve = true;

                PlaySuccessionResult(false);
            };

            _cutinHelper.OnStartAction += timelineController =>
            {
                AudioManager.Instance.PlaySe(AudioId.SFX_UNLOCK_RACE_EVENT_01);

                _view.SkipButton.SetActiveWithCheck(true);
            };

            _cutinHelper.Play(ResourcePath.STORYRACE_UNLOCKRACE_CUT, SceneManager.Instance.GetCurrentSceneController().GetSceneTransform());
        }

        /// <summary>
        /// 結果演出を表示する
        /// </summary>
        private void PlaySuccessionResult(bool normalTypeResult)
        {
            //結果画面でスキップボタンを非表示に
            _view.SkipButton.SetActiveWithCheck(false);

            int charaId = UNLOCK_RACE_MAIN_CHARA_ID;
            //ユーザーのカード所持状態に関係なくスペシャルウィーク固定になるので、
            //勝負服を取得する
            var defaultDressData = MasterDataManager.Instance.masterDressData.GetWithCharaIdAndCostumeType(charaId, MasterDressData.DEFAULT_COSTUME_TYPE);
            int dressId = defaultDressData?.Id ?? (int)ModelLoader.DressID.SRCommon;

            // 継承結果表示
            CharaDressIdSet charaDressIdSet = new CharaDressIdSet(charaId, dressId);
            var chara_data = MasterDataManager.Instance.masterCharaData.Get(charaId);

            _result.CreateCharacterModel(charaDressIdSet);

            //継承演出カット再生されない場合、ライトがホームビューから出る際に閉じられたため、オンにする必要がある
            if(normalTypeResult)
            {
                DirectionalLightManager.Instance.SetEnable(true);
                StaticVariableDefine.Episode.MainStoryDefine.UNLOCK_RACE_NORMAL_INHERIT_CUTIN_SYSTEM_TEXT_AND_MOTION.TryGetValue(UNLOCK_RACE_MAIN_CHARA_ID, out var data);

                var charaSysLottery = MasterDataManager.Instance.masterCharacterSystemLottery.GetListWithCharaId(0).FirstOrDefault(x => x.SysTextId == data.sysLotteryId);

                var motionSet = MasterDataManager.Instance.masterCharaMotionSet.Get(data.motionSetId);

                //キャラ表示位置補間データは育成と同じものを参照するため、育成のビューIdを渡している
                _result.PlayResult(charaSysLottery, motionSet, SceneDefine.ViewId.SingleModeSuccessionCut, true);
            }
            else
            {
                //キャラ表示位置補間データは育成と同じものを参照するため、育成のビューIdを渡している
                _result.PlayResult(true, SceneDefine.ViewId.SingleModeSuccessionCut, true); // 成功のみ
            }

            _view.ResultParts.SetActiveWithCheck(true);

            var charaName = chara_data?.Name ?? "";

            _view.ResultParts.PlayUnlock(true, ref charaName, () =>
            {
                // 継承結果終了
                void ChangeViewNext()
                {
                    _result.DestroyResult();
                    _result = null;

                    //選択した殿堂入りウマ娘のIdを取得
                    var trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(TempData.Instance.EpisodeData.InheritTrainedCharaId);
                    TempData.Instance.EpisodeData.TrainedCharaIdInNpcRace = trainedChara.Id;

                    //パドックへ
                    MainStoryUtil.GoToNpcRace(_viewInfo.MainStoryData);
                }

                //育成と同じ感じでフェードをかける
                FadeManager.Instance.FadeOut(Color.white, SingleModeSuccessionEventViewController.FADE_TIME, onFinishCallback: () => ChangeViewNext());
            });
        }

        public override void UpdateView()
        {
            base.UpdateView();

            if (_cutinHelper != null)
            {

                _cutinHelper.AlterUpdate();
            }
        }

        public override void LateUpdateView()
        {
            base.LateUpdateView();

            if (_cutinHelper != null)
            {
                _cutinHelper.AlterLateUpdate();

                if (_isStopReserve)
                {
                    _isStopReserve = false;
                    StopCutIn();
                }
            }
        }

        public override IEnumerator InitializeView()
        {
            _viewInfo = GetViewInfo() as ViewInfo;

            _view.ResultParts.SetActiveWithCheck(false);

            //このケースはないけど、一応念のため入れておく
            if (_viewInfo != null)
            {
                var trainedCharaData = WorkDataManager.Instance.TrainedCharaData;
                if (trainedCharaData != null)
                {
                    var gainedBouns = MainStoryConditionRaceBonusCalculator.CalcBonus(trainedCharaData.Get(TempData.Instance.EpisodeData.InheritTrainedCharaId), _viewInfo.MainStoryRaceData);
                    _playUnlockCutin = gainedBouns.HasBonus;
                }
            }

            if(_playUnlockCutin)
            {
                _cutinHelper = new CutInHelper();
                _cutinHelper.Init();
                _cutinHelper.InstantiateTimeline(ResourcePath.STORYRACE_UNLOCKRACE_CUT, SceneManager.Instance.GetCurrentSceneController().GetSceneTransform());
            }
            else
            {
                _view.SkipButton.SetActiveWithCheck(false);
            }

            yield return null;
        }

        public override void BeginView()
        {
            base.BeginView();

            //特定のスキル継承できたので、継承カット再生
            if (_playUnlockCutin)
            {
                PlayUnlockCutin();

                _view.SkipButton.SetOnClick(() =>
                {
                    if (_cutinHelper.TimelineController != null)
                    {
                        _cutinHelper.TimelineController.SkipRuntime();   
                    }

                    //演出スキップされたため、SEを止めておく
                    AudioManager.Instance.StopSe(AudioId.SFX_UNLOCK_RACE_EVENT_01);

                    _view.SkipButton.onClick.RemoveAllListeners();
                });
            }
            else
            {
                //カット表示なしで直接に結果画面へ
                _isStopReserve = true;
                PlaySuccessionResult(true);
            }
        }

        //バックキーの挙動は育成と同じものにする
        public override void OnClickOsBackKey()
        {
            UIUtil.ShowNotificationBackKey();
        }

        #endregion ベース呼び出し

        public void StopCutIn()
        {
            _cutinHelper.DestroyTimelineController();
            _cutinHelper.CleanupPlaying();
            _cutinHelper = null;
        }
    }

}