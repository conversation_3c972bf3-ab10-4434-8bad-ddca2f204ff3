using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.Core;
using UnityEngine;

namespace Gallop
{
    using static DialogTutorialGuide;
    using static Gallop.EpisodeDefine;

    /// <summary>
    /// エクストラストーリー画面
    /// Viewコントローラー
    /// </summary>
    [AddComponentMenu("")]
    public class EpisodeExtraViewController : ViewControllerBase<EpisodeExtraView>
    {
        #region ViewInfo

        /// <summary>
        /// エクストラストーリーViewInfo
        /// </summary>
        public class ViewInfo : IViewInfo
        {
            public const int INVALID_PARTDATA_ID = -1;

            /// <summary> 遷移先画面 </summary>
            public ExtraSubCategory TargetCategory = ExtraSubCategory.None;
            public ScreenType TargetScreenType = EpisodeExtraViewController.ScreenType.Top;

            /// <summary> 表示したい章データId </summary>
            public int TargetPartDataId = INVALID_PARTDATA_ID;
        }

        #endregion

        #region Enum

        /// <summary>
        /// 画面状態
        /// </summary>
        public enum ScreenType
        {
            Top,        // Top
            Part,       // 章
            Story,      // 話
        }

        #endregion

        #region Property, Variable

        private EpisodeExtraModel _model = new EpisodeExtraModel();

        #endregion

        #region Method

        #region Override

        public override void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPath(_model.GetRegisterDownloadPaths());

            base.RegisterDownload(register);
        }

        public override IEnumerator InitializeEachPlayIn()
        {
            _view.EpisodeList.InitializeEachPlayIn();
            yield return base.InitializeEachPlayIn();
        }

        public override IEnumerator PlayInView()
        {
            var targetCategory = ExtraSubCategory.None;
            var targetScreenType = ScreenType.Top;
            var targetPartDataId = ViewInfo.INVALID_PARTDATA_ID;

            // ViewInfoによる遷移先の指定があれば取得
            if (GetViewInfo() is ViewInfo viewInfo)
            {
                targetCategory = viewInfo.TargetCategory;
                targetScreenType = viewInfo.TargetScreenType;
                targetPartDataId = viewInfo.TargetPartDataId;
            }

            // フェードがかかっているなら解除
            if (NowLoading.Instance.IsShown())
                NowLoading.Instance.Hide();

            _model.Setup(targetCategory, targetScreenType, targetPartDataId);
            _view.Setup(BGManager.Instance.CharacterBg.BGRenderer);

            ShowScreen(_model.CurrentScreenType, skipPlayOut: true);

            bool isAlreadyReadGuide = DialogTutorialGuide.IsAlreadyRead(TutorialGuideId.ExtraStory);

            DialogTutorialGuide.PushDialogWithReadCheck(TutorialGuideId.ExtraStory, () =>
            {
                // たづなさんがいる画面の場合はボイス再生
                switch (_model.CurrentScreenType)
                {
                    case ScreenType.Top:
                        if (!isAlreadyReadGuide) // 今回のイリでTIPSを表示した場合のみ再生
                        {
                            _view.PlayCharacterVoice();
                        }
                        break;

                    case ScreenType.Part:
                        _view.PlayCharacterVoice(_model.CurrentCategory);
                        break;

                    // その他(話)はたづなさんはいないので何もしない
                }
            });

            yield return base.PlayInView();
        }

        public override IEnumerator PlayOutView()
        {
            yield return _view.EpisodeList.OnPlayOutView(isSceneChange: true);
        }

        /// <summary>
        /// View終了時処理（※HubView終了時ではない点に注意）
        /// </summary>
        public override IEnumerator EndView()
        {
            yield return base.EndView();
        }

        /// <summary>
        /// 戻るボタンアニメの開始までの遅延時間取得（画面Inオブジェクト数で可変する） 
        /// </summary>
        public override float GetBackButtonAnimationDelayTime()
        {
            return _view.EpisodeList.GetBackButtonAnimationDelayTime();
        }

        public override void OnClickBackButton()
        {
            if (_view.EpisodeList.OnClickBackButton())
            {
                // リストの方で独自の操作があれば優先
                return;
            }

            switch (_model.CurrentScreenType)
            {
                case ScreenType.Top:
                    // 元の画面にもどす
                    if (SceneManager.Instance.BackUsingStack())
                    {
                        return;
                    }

                    UIManager.Footer.Transition(Footer.ButtonType.Story);
                    break;

                // Part → Top
                case ScreenType.Part:
                    ShowScreen(ScreenType.Top);
                    break;
                
                // Story → Part
                case ScreenType.Story:
                    ShowScreen(ScreenType.Part);
                    break;
            }
        }

        public override void OnClickOsBackKey()
        {
            OnClickBackButton();
        }


        // 終了処理(NowLoading表示完了後)
        public override IEnumerator FinalizeView() 
        {
            // 他の画面の背景に影響を与えてしまうので、背景クロスフェード機能を破棄
            _view.CrossFade.Destroy();
            _view.Destroy();

            yield break; 
        }

#endregion

        /// <summary>
        /// 画面表示切り替え
        /// </summary>
        /// <param name="screenType"></param>
        /// <param name="skipPlayOut"></param>
        private void ShowScreen(ScreenType screenType, bool skipPlayOut = false)
        {
            // 画面をロック
            UIManager.Instance.LockGameCanvas();

            IEnumerator PlaySequence()
            {
                bool isSkipExtraStoryEventShioriButtonAnime = false;

                _model.SwitchScreen(screenType);

                _view.EpisodeList.SetOnClickExtraStoryEventShioriCompleteDialogButton(null, null);

                switch (_model.CurrentScreenType)
                {
                    // エクストラTOP
                    case ScreenType.Top:
                        yield return ShowTopView(skipPlayOut);
                        break;

                    // 章リストを表示する
                    case ScreenType.Part:
                        yield return ShowPartView(skipPlayOut);

                        // ストーリーイベントの話画面からの遷移の場合、「回想のしおり使用」ボタンのアニメは再生させない
                        if (_model.CurrentCategory == ExtraSubCategory.StoryEvent &&
                            _model.PrevScreenType == ScreenType.Story)
                        {
                            isSkipExtraStoryEventShioriButtonAnime = true;
                        }
                        break;

                    // 話リストを表示する
                    // Note: Categoryは上の階層で指定済みなので共通処理を呼び出すだけでOK
                    case ScreenType.Story:
                        yield return ShowEpisodeView(skipPlayOut);

                        // ストーリーイベントの章画面からの遷移の場合、「回想のしおり使用」ボタンのアニメは再生させない
                        if (_model.CurrentCategory == ExtraSubCategory.StoryEvent &&
                            _model.PrevScreenType == ScreenType.Part)
                        {
                            isSkipExtraStoryEventShioriButtonAnime = true;
                        }
                        break;
                }

                // INアニメーション
                yield return _view.EpisodeList.OnPlayInView(isSkipExtraStoryEventShioriButtonAnime: isSkipExtraStoryEventShioriButtonAnime);

                // 画面をアンロック
                UIManager.Instance.UnlockGameCanvas();
            }

            // 遷移開始
            UpdateDispatcher.StartCoroutine(PlaySequence());
        }


        /// <summary>
        /// トップを表示する
        /// </summary>
        private IEnumerator ShowTopView(bool isSkilPlayOut)
        {
            // "エクストラストーリー"
            UIManager.Instance.SetHeaderTitleText(TextId.StoryEvent4180002.Text(), TutorialGuideId.ExtraStory);
            UIManager.Instance.PlayHeaderTitleInAnim();

            // 背景の切り替え
            // 章 → トップの場合背景を変化させる
            if (_model.PrevScreenType == ScreenType.Part)
            {
                _view.CrossFade.FadeOut();
            }

            if (isSkilPlayOut == false)
            {
                yield return _view.EpisodeList.OnPlayOutView();
            }

            _view.ShowTopList(_model.TopDataList, OnClickTopListItem);
        }

        /// <summary>
        /// 章のデータリストを表示する
        /// </summary>
        private IEnumerator ShowPartView(bool isSkilPlayOut)
        {
            var titleText = string.Empty;
            var category = _model.CurrentCategory;
            bool isSkipExtraStoryEventShioriButtonAnime = false;

            _model.SetupEpisodeData(category);

            UIManager.Instance.SetHeaderTitleText(_model.GetCategoryTitle(category), TutorialGuideId.ExtraStory);
            UIManager.Instance.PlayHeaderTitleInAnim();

            // 背景の切り替え
            _view.CrossFade.SetTexture(_model.GetBgTextureAtCurrent());
            
            switch (_model.PrevScreenType)
            {
                // トップ → 章の場合背景を変化させる
                case ScreenType.Top:
                    _view.CrossFade.FadeIn();
                    break;
                
                // ムービー(章) → 章の場合は背景のαを1にする(しないとTopの背景が映る)
                case ScreenType.Part:
                    _view.CrossFade.SetAlpha(1f);
                    break;

                // 話 → 章の場合は背景のαを1にする(ホームから直接話に飛べるので背景を必ず映す処理を入れる)
                case ScreenType.Story:
                    _view.CrossFade.SetAlpha(1f);

                    // ストーリーイベントの場合、「回想のしおり使用」ボタンのアニメを再生させない
                    if (_model.CurrentCategory == ExtraSubCategory.StoryEvent)
                        isSkipExtraStoryEventShioriButtonAnime = true;
                    break;
            }

            switch (_model.CurrentCategory)
            {
                // ストーリーイベントの章リストを表示するなら
                case ExtraSubCategory.StoryEvent:
                    // 回想のしおりの「解放完了」ダイアログのストーリーイベントボタンが押された時の処理と閉じるボタンが押された時の処理を設定する
                    _view.EpisodeList.SetOnClickExtraStoryEventShioriCompleteDialogButton(OnClickExtraStoryEventShioriCompleteDialogStoryEventButtonInPart,
                                                                                          OnClickExtraStoryEventShioriCompleteDialogCloseButtonInPart);
                    break;
            }

            if (isSkilPlayOut == false)
            {
                yield return _view.EpisodeList.OnPlayOutView(isSkipExtraStoryEventShioriButtonAnime: isSkipExtraStoryEventShioriButtonAnime);
            }

            // 通常導線（Movie系以外）
            if (_model.CurrentCategory != ExtraSubCategory.Movie)
            {
                _view.ShowPartList(category, _model.GetEpisodePartDataList(category), OnClickPartListItem);
            }
            // Movie系の場合
            else
            {
                _view.ShowPartList(category, _model.GetEpisodePartDataList(category), OnClickMoviePartListItem);
            }
            
        }

        /// <summary>
        /// 話のデータリストを表示する
        /// </summary>
        private IEnumerator ShowEpisodeView(bool isSkilPlayOut)
        {
            bool isSkipExtraStoryEventShioriButtonAnime = false;

            // エピソードデータがないので出せない
            if (_model.CurrentPartData == null) yield break;

            // エピソード一覧のためではなく、ストーリー再生後の次話再生確認ダイアログのためのDLをここで行う #57073
            // ・ストーリー内部でのDL: 本質的だが、対応コストが高い上にエンバグリスクも高い
            // ・エクストラトップ画面でDL: 運用を続けるたびにDL量が積み上がっていく
            // の2点を考慮して、ここでDLすることにした
            bool isDlComplete = false;

            switch (_model.CurrentCategory)
            {
                case ExtraSubCategory.StoryEvent:
                    DownloadStoryEventAsset(_model.CurrentPartData.Id, () => { isDlComplete = true; });

                    // 章→話 の遷移の場合「回想のしおり使用」ボタンのアニメを再生させない
                    if (_model.PrevScreenType == ScreenType.Part)
                        isSkipExtraStoryEventShioriButtonAnime = true;
                    break;

                case ExtraSubCategory.Anniversary:
                    DownloadStoryAnniversaryThumbnail(_model.CurrentPartData.Id, () => { isDlComplete = true; });
                    break;
                
                case ExtraSubCategory.Movie:
                    // Movie系は話の概念がないので、ここに到達することはないはず
                    Debug.LogError("意図しないケースが生じています");
                    yield break;
            }

            while (isDlComplete == false)
            {
                yield return null;
            }

            UIManager.Instance.SetHeaderTitleText(_model.CurrentPartData.Title, TutorialGuideId.ExtraStory);
            UIManager.Instance.PlayHeaderTitleInAnim();
            if (isSkilPlayOut == false)
            {
                yield return _view.EpisodeList.OnPlayOutView(isSkipExtraStoryEventShioriButtonAnime: isSkipExtraStoryEventShioriButtonAnime);
            }

            // エピソードを表示できるか確認
            if (!_model.CanShowEpisodeListVew()) yield break;

            _view.ShowEpisodeList(_model.CurrentCategory, _model.GetEpisodePartDataList(), _model.CurrentPartData, OnClickStoryListItem);
        }


        /// <summary>
        /// Topリスト押下
        /// </summary>
        private void OnClickTopListItem(EpisodePartData partData)
        {
            switch (_model.GetCategoryByTopEpisodePartData(partData))
            {
                case ExtraSubCategory.StoryEvent:
                    _model.SwitchCategory(ExtraSubCategory.StoryEvent);
                    break;

                case ExtraSubCategory.Anniversary:
                    _model.SwitchCategory(ExtraSubCategory.Anniversary);
                    break;
                
                case ExtraSubCategory.Movie:
                    _model.SwitchCategory(ExtraSubCategory.Movie);
                    break;
            }

            ShowScreen(ScreenType.Part);
        }

        /// <summary>
        /// コールバック：ストーリイベント一覧リスト押下
        /// </summary>
        private void OnClickPartListItem(EpisodePartData partData)
        {
            _model.CurrentPartData = partData;

            ShowScreen(ScreenType.Story);
        }
        
        /// <summary>
        /// コールバック：Movieイベント一覧リスト押下
        /// </summary>
        private void OnClickMoviePartListItem(EpisodePartData partData)
        {
            // 「CMムービー」項目押下時
            if (partData.LayoutMode == EpisodePartData.Mode.CmMovie)
            {
                // CMムービー画面へ
                WorkDataManager.Instance.ExtraCommercialData.LastPlayCmTrainedCharaId = 0;
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.EpisodeExtraCommercial);
                return;
            }

            // 通常のムービーの項目押下時

            // ボイスのダウンロードを確認して、Movie視聴画面に遷移させる
            var storyExtraMovieData = MasterDataManager.Instance.masterStoryExtraMovieData.Get(partData.Id);
            var storyData = new EpisodeStoryData(storyExtraMovieData);
            DialogExtraMovieStartConfirm.OpenWithDelay(storyData, settings =>
            {
                _view.StartCoroutine(StartMovieWithDownload(settings, partData));
            });
        }

        /// <summary>
        /// ダウンロードしてからムービー再生を開始
        /// </summary>
        /// <returns></returns>
        private IEnumerator StartMovieWithDownload(EpisodeStartSetings settings, EpisodePartData partData)
        {
            // ボタン入力防止
            UIManager.Instance.LockGameCanvas();
            {
                bool closeDialog = false;

                DialogManager.RemoveAllDialog(() => closeDialog = true);
                while (!closeDialog)
                {
                    yield return null;
                }

                // ダイアログ閉じたらダウンロードを実行する
                bool downloadFinish = false;
                DownloadManager.Instance.Download(settings.GetDownloadList(), () => downloadFinish = true);
                while (!downloadFinish)
                {
                    yield return null;
                }
            
                var moviePath = MasterDataManager.Instance.masterStoryExtraMovieData.Get(partData.Id).MoviePath;
                _view.StartCoroutine(ChangeMovieScene(moviePath));
            }
            UIManager.Instance.UnlockGameCanvas();
        }

        /// <summary>
        /// Movie視聴画面に遷移
        /// </summary>
        private IEnumerator ChangeMovieScene(string moviePath)
        {
            const int INVALID_PARAMETER = -1;
            
            // 原状復帰するための関数
            void ChangeHomeHubScene()
            {             
                // エクストラへ遷移
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.EpisodeExtra, 
                    new ViewInfo()
                    {
                        TargetCategory = ExtraSubCategory.Movie,
                        TargetScreenType = ScreenType.Part,
                        TargetPartDataId = (int)GameDefine.OpVersion.Latest
                    });
            };
            
            // 画面遷移
            var viewInfo = new StoryMovieViewController.ViewInfo
            {
                MovieNumber = INVALID_PARAMETER,
                MarkEpisodeId = INVALID_PARAMETER,
                MarkEpisodeType = StoryMarkEpisodeType.Main,
                IsForceLandscape = true,                // 横向き強制
                IsAllowPhotoCaption = true,             // 撮影を許可する
                IsShowMenuButton = false,               // メニューボタンは出さない（スキップボタンと撮影ボタンは出す）
                IsMovieSizeHD = true,                   // アスペクト比調整をせずに再生する
                RegisteredMoviePath = moviePath,
                OnComplete = ChangeHomeHubScene,
            };
            StoryUtil.GoToStoryMovieCommon(viewInfo, isNeedFade: false);
            yield break;
        }

        /// <summary>
        /// コールバック：ストーリイベント話リスト押下
        /// </summary>
        private void OnClickStoryListItem(EpisodePartData partData, EpisodeStoryData data)
        {
            // ロックされているときは解放条件を表示する
            if (data.IsLock)
            {
                switch (_model.CurrentCategory)
                {
                    case ExtraSubCategory.StoryEvent:
                        DialogEpisodeUnlockInfoBase.PushDialog(data.MasterEventStoryData, StoryEventUtil.StoryPlayType.StoryEventInExtra);
                        return;

                    case ExtraSubCategory.Anniversary:
                        DialogEpisodeUnlockInfoBase.PushDialog(data.MasterExtraStoryData);
                        return;
                    
                    case ExtraSubCategory.Movie:
                        // Movie系は話の概念がないので、ここに到達することはないはず
                        Debug.LogError("意図しないケースが生じています");
                        return;
                }
            }

            // ボイスのダウンロードを確認してストーリーへの遷移
            DialogEpisodeStartSettingConfirm.OpenWithDelay(data, settings =>
            {
                var targetCategory = _model.CurrentCategory;

                void ChangeHomeHubScene()
                {             
                    // エクストラへ遷移
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.EpisodeExtra, 
                        new ViewInfo()
                        {
                            TargetCategory = targetCategory,
                            TargetScreenType = ScreenType.Story,
                            TargetPartDataId = partData.Id,
                        });
                };


                switch (_model.CurrentCategory)
                {
                    case ExtraSubCategory.StoryEvent:
                        StoryEventUtil.StartEpisodeWithDownload(settings, StoryEventUtil.StoryPlayType.StoryEventInExtra, () =>
                        {
                            ChangeHomeHubScene();
                        });
                        break;

                    case ExtraSubCategory.Anniversary:
                        StoryExtraUtil.StartEpisodeWithDownload(settings, () =>
                        {
                            ChangeHomeHubScene();
                        });
                        break;
                    
                    case ExtraSubCategory.Movie:
                        // Movie系は話の概念がないので、ここに到達することはないはず
                        Debug.LogError("意図しないケースが生じています");
                        break;
                }
                
            });
        }

        /// <summary>
        /// ストーリーイベントアセットDL
        /// </summary>
        /// <returns></returns>
        private static void DownloadStoryEventAsset(int storyEventId, Action onFinish)
        {
            // DL登録したいアセットを登録
            var assetPathList = new List<string>();

            // サムネイル
            var storyDataList = MasterDataManager.Instance.masterStoryEventStoryData.GetListWithStoryEventIdOrderByIdAsc(storyEventId);
            foreach (var storyData in storyDataList)
            {
                assetPathList.Add(ResourcePath.GetStoryEventExtraStoryThumbnailPath(storyData.StoryEventId, storyData.EpisodeIndexId));
            }

            // 予告ボイス
            var announceData = PartsEpisodeList.GetAnnounceDataByStoryEventId(storyEventId);
            if (announceData != null)
            {
                var cueSheetPath = AudioManager.GetCueSheetPathList(new List<string>() { announceData.CueSheetName }, AudioManager.SubFolder.Voice);
                assetPathList.AddRange(cueSheetPath);
            }

            // DL開始
            DownloadManager.Instance.Download(assetPathList, onFinish);
        }

        private static void DownloadStoryAnniversaryThumbnail(int storyExtraId, Action onFinish)
        {
            // DL登録したいアセットを登録
            var assetPathList = new List<string>();
            var storyDataList = MasterDataManager.Instance.masterStoryExtraStoryData.GetListWithStoryExtraIdOrderByIdAsc(storyExtraId);
            foreach (var storyData in storyDataList)
            {
                assetPathList.Add(ResourcePath.GetExtraStoryExtraThumbnailPath(storyData.StoryExtraId, storyData.EpisodeIndexId));
            }

            // DL開始
            DownloadManager.Instance.Download(assetPathList, onFinish);
        }

        /// <summary>
        /// ストーリーイベントの章選択で回想のしおりの「解放完了」ダイアログのストーリーイベントボタンが押された時の処理
        /// </summary>
        private void OnClickExtraStoryEventShioriCompleteDialogStoryEventButtonInPart(int storyEventId)
        {
            if (storyEventId <= 0)
                return;

            // ストーリーイベントが解放された事で、そのストーリーイベントの話リストの表示内容を変化させる必要があるため、EpisodeDataを作り直す
            _model.SetupEpisodeData(ExtraSubCategory.StoryEvent);

            var episodePartDataList = _model.GetEpisodePartDataList();
            if (episodePartDataList == null || episodePartDataList.Count <= 0)
                return;

            var targetPartData = episodePartDataList.FirstOrDefault(e => e.Id == storyEventId);
            if (targetPartData == null)
                return;

            // 解放したストーリーイベントの話選択へ遷移
            _model.CurrentPartData = targetPartData;
            ShowScreen(ScreenType.Story);
        }

        /// <summary>
        /// ストーリーイベントの章選択で回想のしおりの「解放完了」ダイアログの閉じるボタンが押された時の処理
        /// </summary>
        private void OnClickExtraStoryEventShioriCompleteDialogCloseButtonInPart()
        {
            // ストーリーイベントが解放された事で、そのストーリーイベントの話リストの表示内容を変化させる必要があるため、EpisodeDataを作り直す
            _model.SetupEpisodeData(ExtraSubCategory.StoryEvent);
        }

#endregion
    }
}
