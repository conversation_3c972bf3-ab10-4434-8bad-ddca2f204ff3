using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// マスターズチャレンジ：VS演出3D用パラメータ
    /// </summary>
    [Serializable]
    [CreateAssetMenu(menuName = "ScriptableObject/UltimateRace/VsCutinParameter")]
    public class PartsUltimateRaceVsCutinParameter : ScriptableObject
    {
        [field: Header("Bgエフェクトカメラ設定")] 
        public Vector3 BgCameraPos;
        public float BgCameraFov = 22;
        
        [field: Header("キャラカメラ共通設定")]
        public float CameraFov = 60;

        [field: Header("1人用")] 
        [Range( 0.0f, 1.0f)]
        public float CharaTextureScale1;
        [field: Header("身長差サイズ補正設定")] 
        public Vector2 CharaMinMaxScale1 = Math.VECTOR2_ONE;
        public CameraSetParam CameraSet1;
        
        [field: Header("2人用")]
        [Range( 0.0f, 1.0f)]
        public float CharaTextureScale2;
        [field: Header("身長差サイズ補正設定")] 
        public Vector2 CharaMinMaxScale2 = Math.VECTOR2_ONE;
        public CameraSetParam[] CameraSet2;
        
        [field: Header("3人用")]
        [Range( 0.0f, 1.0f)]
        public float CharaTextureScale3;
        [field: Header("身長差サイズ補正設定")] 
        public Vector2 CharaMinMaxScale3 = Math.VECTOR2_ONE;
        public CameraSetParam[] CameraSet3;
        
        [field: Header("4人用")]
        [Range( 0.0f, 1.0f)]
        public float CharaTextureScale4;
        [field: Header("身長差サイズ補正設定")] 
        public Vector2 CharaMinMaxScale4 = Math.VECTOR2_ONE;
        public CameraSetParam[] CameraSet4;
        
        [Serializable]
        public struct CameraSetParam
        {
            public Vector2 CameraObliqueness;
            public Vector3 CharaRot;
            public Vector2 CharaOffset;
        }
        
#if UNITY_EDITOR && CYG_DEBUG
        /// <summary>
        /// パラメータ設定の為にカットをループさせる用のフラグ。
        /// </summary>
        [NonSerialized]
        public bool IsLoopCutIn = false;
#endif
    }
    
    // カスタムエディタクラス
#if UNITY_EDITOR && CYG_DEBUG
    [UnityEditor.CustomEditor(typeof(PartsUltimateRaceVsCutinParameter))]
    public class DemoScriptableObjectEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var param = target as PartsUltimateRaceVsCutinParameter;
            base.OnInspectorGUI();
            
            param.IsLoopCutIn = UnityEditor.EditorGUILayout.Toggle("【デバッグ機能】カットインループ", param.IsLoopCutIn);
        }
    }
#endif
}
