using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.AssetBundle;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class TutorialOpMovieView : ViewBase
    {
        [field: SerializeField, RenameField] 
        public ButtonCommon Skip<PERSON>utton { get; private set; } = null;
    }

    public class TutorialOpMovieViewController : ViewControllerBase<TutorialOpMovieView>
    {
        private enum MoviePlayingState
        {
            WaitPlay,
            BeginPlay,
            Pause,
            WaitDownload,
            WaitFinish,
        }
        
#if CYG_DEBUG
        public class DebugViewInfo : IViewInfo
        {
            public bool IsMovieCheck { get; set; } = false;
        }
#endif

        // DL確認を出す時間        
        private const int PAUSE_MOVIE_FRAME = 4;

        private MoviePlayingState _playingState = MoviePlayingState.WaitPlay;

        // ムービープレイヤー
        private FullScreenMoviePlayer _moviePlayer = null;
        private int _prevPlayFrame = 0;
        
        // 視聴するOpのバージョン
        private int _viewedOpVersion = (int)GameDefine.DEFAULT_PLAY_TUTORIAL_OP_VERSION;

#if CYG_DEBUG
        private bool _isMovieCheckMode = false;
#endif
        
        public override IEnumerator GetChangeViewOrientation(Screen.ScreenOrientationClassWrapper retOrientation)
        {
            retOrientation.Orientation = ScreenOrientation.LandscapeLeft;
            yield break;
        }

        public override void RegisterDownload(DownloadPathRegister register)
        {
            // 最新リソースをDL対象とする
            int version = ResourcePath.GetLatestTutorialMovieVersion();
            register.RegisterPathWithoutInfo($"{ResourcePath.MovieFolderPath}{ResourcePath.GetTutorialMoviePath(version)}{Cute.Cri.MovieManager.USM_EXTENSION}");
        }

        /// <summary>
        /// 初期化
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            // ダウンロード中は自動スリープ無効
            GallopUtil.SetSleepEnable(false);
            
#if CYG_DEBUG
            IViewInfo viewInfo = GetViewInfo();
            if (viewInfo != null && viewInfo is DebugViewInfo)
            {
                _isMovieCheckMode = (viewInfo as DebugViewInfo).IsMovieCheck;
            }
#endif
            _moviePlayer = new FullScreenMoviePlayer();

            // 視聴するOPを決定
            _viewedOpVersion = ResourcePath.GetLatestTutorialMovieVersion();
            var moviePath = ResourcePath.GetPlayableTutorialMoviePath();
            yield return _moviePlayer.Load(moviePath);
            
            // アス比設定
            _moviePlayer.Init(true);
            
            _view.SkipButton.gameObject.SetActive(false);
            _view.SkipButton.SetOnClick(OnClickSkip);
        }

        public override void BeginView()
        {
            _moviePlayer.Play(() => { _playingState = MoviePlayingState.BeginPlay; });
        }

        public override void UpdateView()
        {
            if (_moviePlayer == null)
            {
                return;
            }
            
            int currentPlayFrame = _moviePlayer.GetPlayFrameInLoop();
            int prevPlayFrame = _prevPlayFrame;
            _prevPlayFrame = currentPlayFrame;

            switch (_playingState)
            {
                case MoviePlayingState.BeginPlay:
                    if (!DownloadManager.IsDownloading())
                    {
                        // ダウンロードしていない場合
                        // ムービーをそのまま再生して、スキップボタンも即時解放する
                        _playingState = MoviePlayingState.WaitFinish;
                        _view.SkipButton.gameObject.SetActive(true);
                    }
                    break;
                
                case MoviePlayingState.Pause:
                    // ダイアログで処理されるのを待つ
                    // ステートはダイアログ操作側で更新される
                    break;
                
                case MoviePlayingState.WaitDownload:
                    if (!DownloadManager.IsDownloading())
                    {
                        _playingState = MoviePlayingState.WaitFinish;
                        _view.SkipButton.gameObject.SetActive(true);
                    }
                    break;
                
                case MoviePlayingState.WaitFinish:
#if CYG_DEBUG
                    // ムービー確認モードの場合、勝手に終了しない
                    if (_isMovieCheckMode)
                    {
                        return;
                    }
#endif
                    // 時間が巻き戻った = 再生終了してループ開始したので、終了していい
                    if (prevPlayFrame > currentPlayFrame)
                    {
                        FinalizeMovie();
                        
                        OnFinal();
                    }
                    break;
            }
        }

        private void OnClickSkip()
        {
#if CYG_DEBUG
            // ムービー確認モードの場合、Skipしたらタイトルに戻る
            if (_isMovieCheckMode)
            {
                GameSystem.Instance.SoftwareReset();
                return;
            }
#endif
            OnFinal();
        }
        
        /// <summary>
        /// 視聴が終わったときの処理
        /// </summary>
        private void OnFinal()
        {
            // 視聴したOPバージョンによって分岐させる
            if (_viewedOpVersion == (int)GameDefine.LATEST_TUTORIAL_OP_VERSION)
            {
                // 最新のOP（ThirdAniv以降）を視聴した場合はAPI通信を挟む
                var req = new TitleOpVersionUpdateRequest();
                req.Send(res =>
                {
                    if (res == null || res.data == null)
                    {
                        Debug.LogError("不正なresponseです: TitleOpVersionUpdate API");
                        return;
                    }

                    var version = res.data.viewed_latest_op_version;
                    WorkDataManager.Instance.UserData.UpdateViewedLatestOpVersion(version);
                
                    // シーン移動
                    ChangeView();
                });
            }
            else
            {
                // 旧OPを視聴した際は通信しない
                ChangeView();
            }

            void ChangeView()
            {
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.Home);
                _view.SkipButton.SetEnable(false);
            }
        }

        /// <summary>
        /// 後始末
        /// </summary>
        /// <returns></returns>
        public override IEnumerator FinalizeView()
        {
            // 自動スリープ無効にしていたのを解除
            GallopUtil.SetSleepEnable(true);

            FinalizeMovie();
            yield break;
        }

        private void FinalizeMovie()
        {
            if (_moviePlayer != null)
            {
                _moviePlayer.Pause();
                _moviePlayer.SetVolume(0.0f);
                
                _moviePlayer.Stop();
                _moviePlayer.DestroyPlayer();
            }
            // #137187 再生開始時にカメラの背景色が黒になるので白に戻す
            UIManager.BGCamera.backgroundColor = GameDefine.COLOR_WHITE;

            _moviePlayer = null;
        }
    }
}