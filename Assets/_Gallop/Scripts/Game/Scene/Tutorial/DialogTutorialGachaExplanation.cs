using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    namespace Tutorial
    {
        [AddComponentMenu("")]
        public class DialogTutorialGachaExplanation : DialogInnerBase
        {
            #region DialogCommonBase

            /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
            public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

            /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
            public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

            #endregion
            
            #region SerializeField
            
            [SerializeField]
            private TextCommon _gachaDetailMessageTextCommon = null;

            [SerializeField] 
            private TextCommon _warningMessageTextCommon = null;        
            
            #endregion
            
            #region 定数・定義

            public enum GachaType
            {
                PrettyDerby,
                SupportCard
            }
            
            #endregion

            private void SetupContent(GachaType gachaType)
            {
                switch (gachaType)
                {
                    case GachaType.PrettyDerby:
                        _gachaDetailMessageTextCommon.text = TextId.Tutorial0017.Text();
                        _warningMessageTextCommon.SetTextWithCustomTag(TextId.Tutorial0019.Text());
                        break;
                    
                    case GachaType.SupportCard:
                        _gachaDetailMessageTextCommon.text = TextId.Tutorial0018.Text();
                        _warningMessageTextCommon.SetTextWithCustomTag(TextId.Tutorial0020.Text());
                        break;
                }
            }

            /// <summary>
            /// ダイアログ開く
            /// </summary>
            /// <param name="type"></param>
            /// <param name="onClickAccept"></param>
            /// <param name="OnClickReject"></param>
            public static void OpenDialog(GachaType gachaType, System.Action onClickButton)
            {
                // Dialog内オブジェクト生成
                var dialogObj = LoadAndInstantiatePrefab<DialogTutorialGachaExplanation>(ResourcePath.TUTORIAL_GACHA_EXPLANATION_DIALOG_PATH);
                dialogObj.SetupContent(gachaType);
            
                // DialogData取得
                var dialogData = dialogObj.CreateDialogData();
                dialogData.Title = TextId.Tutorial0015.Text();
                dialogData.AutoClose = false;
                dialogData.EnableOutsideClick = false; 

                // ダイアログ中央1ボタン
                dialogData.CenterButtonColor = DialogCommon.ButtonColor.Green;
                dialogData.CenterButtonCallBack = (dialog) =>
                {
                    dialog.Close(() =>
                    {
                        onClickButton();
                    });
                };
                dialogData.CenterButtonText = TextId.Gacha0021.Text();

                DialogManager.PushDialog(dialogData);
            }
        }
    }
}