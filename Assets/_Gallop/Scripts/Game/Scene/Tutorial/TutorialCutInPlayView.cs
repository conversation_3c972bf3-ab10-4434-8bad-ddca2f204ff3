using System.Collections;
using System.Collections.Generic;
using Cute.Cri;
using Gallop.CutIn.Cutt;
using UnityEngine;
using static Gallop.StaticVariableDefine.Tutorial;

namespace Gallop
{
    [AddComponentMenu("")]
    public class TutorialCutInPlayView : ViewBase
    {
        [field: SerializeField, RenameField]
        public ButtonCommon AllScreenButton = null;

        [field: SerializeField, RenameField]
        public ButtonCommon Skip<PERSON>utton = null;
        
        [field: SerializeField, RenameField]
        public RawImageCommon TextImage = null;
    }

    public class TutorialCutInPlayViewController : ViewControllerBase<TutorialCutInPlayView>
    {
        public class TextImageData
        {
            public readonly int InFrame;
            public readonly int OutFrame;
            public readonly string TexturePath;
            
            //表示中かどうか。スキップした場合はtrueのまま残っている可能性があるので注意
            public bool IsDisplay;

            public TextImageData(int inFrame ,int outFrame , string path)
            {
                InFrame = inFrame;
                OutFrame = outFrame;
                TexturePath = path;
                IsDisplay = false;
            }

            public void SetIsDisplay(bool isDisplay)
            {
                IsDisplay = isDisplay;
            }
        }
        

        
        //キューシート
        private const string VOICE_CUE_SHEET = "snd_voi_story_010000001";

        private const float FADE_TIME = 0.3f;
        private const float TAP_WAIT_TIME = 0.3f;
        private const float END_FRAME = 10f; //最後のフレームから何フレーム以内だとブラックアウトと判定しておく
        private const int WAIT_SKIP_FRAME = 30; //開始して0.3秒間はタップスキップを無効化しておく
        
        private TutorialCutInHelper _cutInHelper;
        
        //インクリメントで0からスタートさせたいので初期値は-1
        private int _currentTextIndex = -1;

        private bool _isEnd = false;
        private bool _isTapEnable = false;
        private Coroutine _startWaitCoroutine = null;
        private Coroutine _waitTapCoroutine = null;
        private Coroutine _fadeCoroutine = null;
        
        private AudioPlayback _currentVoicePlayback;

        public override void RegisterDownload(DownloadPathRegister register)
        {
            //カット
            register.RegisterPathWithoutInfo(ResourcePath.TUTORIAL_CUTIN_PATH);
            
            //ボイス
            var voiceResourceList = AudioManager.GetCueSheetPathList(new List<string>(){VOICE_CUE_SHEET},AudioManager.SubFolder.Story);
            int count = voiceResourceList.Count;
            for (int i = 0; i < count; ++i)
            {
                register.RegisterPathWithoutInfo(voiceResourceList[i]);
            }

            count = TEXT_IMAGE_DATA_LIST.Count;
            for(int i = 0; i < count; ++i)
            {
                var path = TEXT_IMAGE_DATA_LIST[i].TexturePath;
                register.RegisterPathWithoutInfo(path);
            }
            
            //その他必要なら追加する
            base.RegisterDownload(register);
        }
        public override IEnumerator InitializeView()
        {
            //ボタンのコールバックの設定や画像の初期設定
            _view.SkipButton.SetOnClick(OnClickSkipButton);
            _view.AllScreenButton.SetOnClick(OnClickAllScreenButton);
            _view.SkipButton.SetActiveWithCheck(false);
            _view.AllScreenButton.SetActiveWithCheck(false);
            _view.TextImage.SetActiveWithCheck(false);

            //初回は条件なしでタップＯＫ
            _isTapEnable = true;
            
            //DLされたCueSheetを読み込む
            AudioManager.Instance.AddCueSheet(VOICE_CUE_SHEET,AudioManager.SubFolder.Story);

            //カットの初期化
            _cutInHelper = new TutorialCutInHelper();
            _cutInHelper.Init();
            _cutInHelper.OnEndAction += (controller) => { OnEnd(); };
            yield return base.InitializeView();
        }
        
        public override IEnumerator PlayInView()
        {
            _cutInHelper.Play(ResourcePath.TUTORIAL_CUTIN_PATH, SceneManager.Instance.GetCurrentSceneController().GetSceneTransform());
            _startWaitCoroutine = _view.StartCoroutine(WaitCutPlayStart());
            yield return base.PlayInView();
        }

        /// <summary>
        /// カットがスタートするのを待って処理をする
        /// </summary>
        /// <returns></returns>
        private IEnumerator WaitCutPlayStart()
        {
            //開始してから一定秒数はタップを無効化
            var timelineController = _cutInHelper.TimelineController;
            while (timelineController != null && timelineController.CurrentFrame < WAIT_SKIP_FRAME)
            {
                yield return null;
            }
            
            _view.SkipButton.SetActiveWithCheck(true);
            _view.AllScreenButton.SetActiveWithCheck(true);
            _startWaitCoroutine = null;
        }

        public override void UpdateView()
        {
            base.UpdateView();
            if (!_isEnd)
            {
                _cutInHelper?.AlterUpdate();
            }

            var currentFrame = _cutInHelper.TimelineController.CurrentFrame;
            int count = TEXT_IMAGE_DATA_LIST.Count;
            //テキスト表示。現在表示中のテキストの次のテキスト表示タイミングになっていたらテキスト表示
            for (int i = _currentTextIndex + 1; i < count; ++i)
            {
                var data = TEXT_IMAGE_DATA_LIST[i];
                if (currentFrame >= data.InFrame)
                {
                    _currentTextIndex = i;
                    SetTexture(_currentTextIndex);
                }
            }

            //消す必要のある画像チェック。処理落ちで飛んだ時のことも考えて、FadeIn処理が終わった後に走らせる
            if (_currentTextIndex >= 0 && _currentTextIndex < count)
            {
                //表示中で、非表示タイミングが来ていたら消す
                var data = TEXT_IMAGE_DATA_LIST[_currentTextIndex];
                if (data.IsDisplay && currentFrame >= data.OutFrame)
                {
                    HideTexture(_currentTextIndex);
                }
            }
        }

        public override void LateUpdateView()
        {
            base.LateUpdateView();
            if (!_isEnd)
            {
                _cutInHelper?.AlterLateUpdate();
            }
        }
        
        /// <summary>
        /// スキップボタンを押した時の処理
        /// </summary>
        private void OnClickSkipButton()
        {
            OnEnd();
        }
        
        /// <summary>
        /// 画面全体を覆うようなボタン
        /// </summary>
        private void OnClickAllScreenButton()
        {
            if (!_isTapEnable)
            {
                return;
            }

            _isTapEnable = false;
            //画像が入るまでは何もしない
            _currentTextIndex++;
            if (_currentTextIndex >= TEXT_IMAGE_DATA_LIST.Count)
            {
                //全てのテキストを表示し終えてたら終了しているのと同じ
                OnClickSkipButton();
                return;
            }

            var data = TEXT_IMAGE_DATA_LIST[_currentTextIndex];
            _cutInHelper.TimelineController.SkipRuntime(data.InFrame);
            SetTexture(_currentTextIndex);
            
            if (_waitTapCoroutine != null)
            {
                _view.StopCoroutine(_waitTapCoroutine);
            }
            _view.StartCoroutine(WaitTap());
        }

        /// <summary>
        /// 画面連打防止対策で待つ時間
        /// </summary>
        private IEnumerator WaitTap()
        {
            yield return new WaitForSeconds(TAP_WAIT_TIME);
            _isTapEnable = true;
        }

        private void SetTexture(int index)
        {
            var data = TEXT_IMAGE_DATA_LIST[index];
            data.SetIsDisplay(true);
            _view.TextImage.texture = ResourceManager.LoadOnView<Texture2D>(TEXT_IMAGE_DATA_LIST[index].TexturePath);
            _view.TextImage.SetActiveWithCheck(true);
            _view.TextImage.SetAlpha(0f);

            if(AudioManager.IsPlay(_currentVoicePlayback))
            {
                AudioManager.Stop(_currentVoicePlayback);
            }
            var cueName = VOICE_CUE_NAME_LIST[index];
            _currentVoicePlayback = AudioManager.Instance.PlayVoice(VOICE_CUE_SHEET, cueName);
            
            if (_fadeCoroutine != null)
            {
                _view.StopCoroutine(_fadeCoroutine);
            }

            _view.StartCoroutine(FadeInOutTexture(true));
        }

        private void HideTexture(int index)
        {
            if (_currentTextIndex > index)
            {
                //現在より小さいものが来たらなにもしない
                return;
            }
            
            var data = TEXT_IMAGE_DATA_LIST[index];
            data.SetIsDisplay(false);

            if (_fadeCoroutine != null)
            {
                _view.StopCoroutine(_fadeCoroutine);
            }

            _view.StartCoroutine(FadeInOutTexture(false));
        }

        private IEnumerator FadeInOutTexture(bool isFadeIn)
        {
            var timer = 0.0f;
            var finishFade = isFadeIn ? 1f : 0f;
            var animationCurve = UIManager.Instance.GetAnimationCurve(AnimationCurvePreset.CurveType.Linear);
            while (timer < FADE_TIME)
            {
                timer += Time.deltaTime;
                var alpha = animationCurve.Evaluate(timer / FADE_TIME);
                //FadeOutの時は1fから逆に減っていく
                var setAlpha = isFadeIn ? alpha : 1f - alpha;
                _view.TextImage.SetAlpha(setAlpha);
                yield return null;
            }
            
            _view.TextImage.SetAlpha(finishFade);
            _fadeCoroutine = null;
        }
        
        /// <summary>
        /// 終了時コールバック
        /// </summary>
        private void OnEnd()
        {
            _isEnd = true;
            
            //一応カットは時を止めておく
            _cutInHelper.TimelineController.SetSpeed(0);

            //サウンドもここで強制終了
            if(AudioManager.IsPlay(_currentVoicePlayback))
            {
                AudioManager.Stop(_currentVoicePlayback);
            }

            //どこに遷移しても上書きしてくれるはずなので、ストーリーを仮で入れておく
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.Story);
        }

        public override IEnumerator FinalizeView()
        {
            //コルーチンも止めておく
            if (_startWaitCoroutine != null)
            {
                _view.StopCoroutine(_startWaitCoroutine);
            }
            if (_waitTapCoroutine != null)
            {
                _view.StopCoroutine(_waitTapCoroutine);
            }
            if (_fadeCoroutine != null)
            {
                _view.StopCoroutine(_fadeCoroutine);
            }
            
            _cutInHelper.CleanupPlaying();
            _cutInHelper = null;
            yield return base.FinalizeView();
        }
    }
}