using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// レース開始までのカウンター
    /// </summary>
    [AddComponentMenu("")]
    public class ChampionsRaceStartCountUI : MonoBehaviour
    {
        [SerializeField] private TextCommon _lastTimeText;

        //残り時間計測
        private bool _initLastTime = false;
        private bool _end = false;
        private TimeSpan _lastTimeSpan;

        private MasterChampionsRoundDetail.ChampionsRoundDetail _roundDetail;
        private MasterChampionsRoundSchedule.ChampionsRoundSchedule _raceStartSchedule;
        private Action _onRaceStart;

        public void Set(Action onRaceStart)
        {
            _onRaceStart = onRaceStart;
            _roundDetail = WorkDataManager.Instance.ChampionsData.GetCurrentRoundDetail();
            if (_roundDetail == null)
                return;

            _raceStartSchedule = MasterDataManager.Instance.masterChampionsRoundSchedule.GetRaceStart(_roundDetail.ChampionsId);
            _lastTimeSpan = _raceStartSchedule.GetStartDate() - TimeUtil.GetDateTime();
            UpdateLastTime(true);
        }

        private void Update()
        {
            if (_initLastTime == false)
                return;
            UpdateLastTime();
        }


        //残り時間更新
        private void UpdateLastTime(bool force = false)
        {
            if (!_initLastTime || _raceStartSchedule == null)
                return;

            var currentTimeSpan = _raceStartSchedule.GetStartDate() - TimeUtil.GetDateTime();
            if (!_end && currentTimeSpan.TotalSeconds < 0)
            {
                //終了した
                _end = true;
                _onRaceStart?.Invoke();
                gameObject.SetActive(false);    //非表示にする
                return;
            }

            //終了してるので不要
            if (_end)
                return;

            //毎秒更新
            if (force || currentTimeSpan.Seconds != _lastTimeSpan.Seconds)
            {
                var totalHour = currentTimeSpan.Days * 24 + currentTimeSpan.Hours;
                _lastTimeText.text = TextId.Champions0064.Format(totalHour, currentTimeSpan.Minutes, currentTimeSpan.Seconds);
                _lastTimeSpan = currentTimeSpan;
            }
        }

    }
}
