using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Random = UnityEngine.Random;
using static Gallop.StaticVariableDefine.Champions.StampRandomPositionArea;

namespace Gallop
{
    [AddComponentMenu("")]
    public class StampRandomPositionArea : MonoBehaviour
    {
        [SerializeField]
        private RectTransform stampAreaRectTransform; 
        
        // スタンプのエリア判別用
        public int StampAreaId { get; private set; }
        
        private void Awake()
        {
            StampAreaId = transform.GetSiblingIndex();
        }

        /// <summary>
        /// 表示するスタンプに特定範囲内のランダムな位置を設定（他のスタンプとの重なり許容）
        /// </summary>
        public void SetRandomOverWrapPosition(GameObject targetStamp, RectTransform stampsParent, List<GameObject> stampObjectList)
        {
            if (targetStamp == null || stampObjectList.IsNullOrEmpty())
            {
                return;
            }
            
            var rectTransform = stampAreaRectTransform;
            var halfItem = new Vector2(STAMP_SIZE.x / 2f, STAMP_SIZE.y / 2f);
            var half = new Vector2(rectTransform.rect.width / 2f, rectTransform.rect.height / 2f);
            var pos = transform.position;
            
            var min = new Vector2(pos.x - half.x + halfItem.x, pos.y - half.y + halfItem.y);
            var max = new Vector2(pos.x + half.x - halfItem.x, pos.y + half.y - halfItem.y);

            pos.x = Random.Range(min.x, max.x);
            pos.y = Random.Range(min.y, max.y);

            targetStamp.transform.SetParent(transform);
            targetStamp.gameObject.SetActiveWithCheck(false);
            targetStamp.gameObject.transform.localPosition = pos;
            
            targetStamp.transform.SetParent(stampsParent, true);
            targetStamp.transform.SetAsLastSibling();
        }
    }
}