using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// チャンピオンズミーティング：レース結果要素
    /// </summary>
    public class ChampionsRaceResultInfoListItem : MonoBehaviour
    {
        private const int NEXT_FLASH_SORT_ORDER_OFFSET = 1;

        [SerializeField] private ButtonCommon _button;
        [SerializeField] private TextCommon _raceNum;
        [SerializeField] private TextCommon _raceName;
        [SerializeField] private PartsEnvironmentConditions _environments;
        [SerializeField] private ImageCommon _result;
        [SerializeField] private GameObject _noResult;
        [SerializeField] private GameObject _raceNow;
        [SerializeField] private ChampionsRaceResultInfoListItemChara[] _myArray;
        [SerializeField] private ChampionsRaceResultInfoListItemChara[] _oponentArray1;
        [SerializeField] private ChampionsRaceResultInfoListItemChara[] _oponentArray2;
        [SerializeField] private GameObject _arrow;
        [SerializeField] private Transform _nextRoot;

        private int _index;
        private ChampionsRoomInfo _roomInfo;
        private int _championsId;
        private ChampionsDefines.Round _round;
        private MasterRaceInstance.RaceInstance _raceInstance;
        private MasterRaceCondition.RaceCondition _raceCondition;
        private FlashPlayer _nextFlashPlayer = null;

        public void Set(
            int index, int championsId, int roundId, ChampionsDefines.League league,
            ChampionsDefines.RaceResult result,
            List<CharacterButtonInfo> myTeamList,
            List<CharacterButtonInfo> opponentList1,
            List<CharacterButtonInfo> opponentList2,
            int userOrder = -1,
            int opp1Order = -1,
            int opp2Order = -1,
            ChampionsRoomInfo roomInfo = null
        )
        {
            var roundDetail = MasterDataManager.Instance.masterChampionsRoundDetail.Get(championsId, roundId, league);
            var champRaceCondition = MasterDataManager.Instance.masterChampionsRaceCondition.Get(championsId, roundId);
            if (champRaceCondition == null || roundDetail == null)
                return;

            _raceInstance = MasterDataManager.Instance.masterRaceInstance.Get(champRaceCondition.RaceInstanceId);
            _raceCondition = MasterDataManager.Instance.masterRaceCondition.Get(champRaceCondition.RaceConditionId);
            if (_raceInstance == null || _raceCondition == null)
                return;

            _index = index;
            _roomInfo = roomInfo;
            _championsId = championsId;
            _round = roundDetail.GetRound();

            _environments.Setup(_raceCondition, _raceInstance);

            //第〇レース
            _raceNum.text = TeamStadiumUtil.GetRoundNumText(index + 1);
            //レース名
            _raceName.text = RaceUtil.GetCourseInfoTextLong(_raceInstance);
            var work = WorkDataManager.Instance.ChampionsData;
            //現在レースか
            var isCurrentRace = work.CurrentRoundData.RaceNum == index;
            //結果、チーム競技場のアトラスから取得するため定義値を変換
            if (result == ChampionsDefines.RaceResult.None)
            {
                //出走中か
                if (work.IsRacing && isCurrentRace)
                {
                    _noResult.SetActive(false);
                    _raceNow.SetActive(true);
                    _result.SetActiveWithCheck(false);
                    _button.SetInteractable(false);
                    _button.SetOnClick(null);
                    _button.SetNotificationMessage(TextId.Champions0110.Text());
                }
                else
                {
                    //未出走
                    _noResult.SetActive(true);
                    _raceNow.SetActive(false);
                    _result.SetActiveWithCheck(false);
                    _button.SetInteractable(false);
                    _button.SetOnClick(null);
                    _button.SetNotificationMessage(TextId.Champions0109.Text());
                }
            }
            else
            {
                _button.SetOnClick(() => OnClick());
                _noResult.SetActive(false);
                _raceNow.SetActive(false);
                _result.SetActiveWithCheck(true);
                var teamStdResult = TeamStadiumDefine.RoundResultType.None;
                switch (result)
                {
                    case ChampionsDefines.RaceResult.Lose: teamStdResult = TeamStadiumDefine.RoundResultType.Lose; break;
                    case ChampionsDefines.RaceResult.Win: teamStdResult = TeamStadiumDefine.RoundResultType.Win; break;
                }
                _result.sprite = TeamStadiumUtil.GetRoundResultSprite(teamStdResult);
            }

            //キャラ一覧表示
            SetCharaButtons(userOrder, myTeamList, _myArray);
            SetCharaButtons(opp1Order, opponentList1, _oponentArray1);
            SetCharaButtons(opp2Order, opponentList2, _oponentArray2);

            //末尾なら矢印不要
            if (index == roundDetail.RoundNumber - 1)
            {
                _arrow.SetActive(false);
            }

            //現在レースのマーク
            if (isCurrentRace)
            {
                _nextFlashPlayer = FlashLoader.LoadOnView(ResourcePath.FLASH_COMMON_NEXT_TARGET, _nextRoot, layer: GraphicSettings.LayerIndex.LayerSyetemUI);
                _nextFlashPlayer.Play(ChampionsRoundStateItem.NEXT_IN);
                _nextFlashPlayer.SortLayer = UIManager.SYSTEM_UI_SORTING_LAYER_NAME;
            }
        }

        public void OnClick(System.Action<DialogChampionsRaceResultDetail> onOpen = null)
        {
            if (_roomInfo == null)
                return; //未出走データは出走できない

            DialogChampionsRaceResultDetail.PushDialog(_roomInfo, _championsId, _round, _index, _raceInstance, _raceCondition, onOpen);
        }

        private void SetCharaButtons(int order, List<CharacterButtonInfo> infoList, ChampionsRaceResultInfoListItemChara[] itemArray)
        {
            if (infoList.Count != itemArray.Length)
                return;

            for (int i = 0; i < infoList.Count; i++)
            {
                var info = infoList[i];
                var item = itemArray[i];
                item.Set(
                    info,
                    i == 0 ? info.FinalTrainingRank : GameDefine.FinalTrainingRank.None,
                    i == 0 ? order : -1
                );
            }
        }

        /// <summary>
        /// 終了する
        /// </summary>
        public void Close()
        {
            if (_nextFlashPlayer != null)
            {
                _nextFlashPlayer.Play(ChampionsRoundStateItem.NEXT_OUT);
            }
        }

        /// <summary>
        /// 描画順を更新する
        /// </summary>
        public void UpdateSortingOrder(int baseOrder, string sortingLayerName)
        {
            if (_nextFlashPlayer != null)
            {
                _nextFlashPlayer.SortOffset = baseOrder + NEXT_FLASH_SORT_ORDER_OFFSET;
                _nextFlashPlayer.SortLayer = sortingLayerName;
            }
        }
    }
}
