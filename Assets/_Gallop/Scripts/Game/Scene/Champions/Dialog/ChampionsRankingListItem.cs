using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// チャンピオンズミーティング：スクロール要素
    /// </summary>
    public class ChampionsRankingListItem : LoopScrollItemBase
    {
        public class Info
        {
            public int Rank;
            public int RealRank;
            public ChampionsRankingInfo RankInfo;
            public ChampionsRankingUserInfo UserInfo;
            public List<ChampionsRankingEntryCharaInfo> EntryCharaList;

            public Info(int rank, int realRank, ChampionsRankingInfo rankInfo, ChampionsRankingUserInfo userInfo, List<ChampionsRankingEntryCharaInfo> entryCharaList)
            {
                Rank = rank;
                RealRank = realRank;
                RankInfo = rankInfo;
                UserInfo = userInfo;
                EntryCharaList = entryCharaList; 
            }

            public CharacterButtonInfo GetCharaButtonInfo(int index)
            {
                if (EntryCharaList == null || index < 0 || EntryCharaList.Count <= index)
                    return null;

                var data = EntryCharaList[index];
                var charaButtonInfo = CharacterButtonInfo.CreateTrained(data.card_id, data.talent_level, data.rarity, data.race_cloth_id);
                charaButtonInfo.OnTap = c => OnTapButton(index);
                charaButtonInfo.OnLongTap = c => OnTapButton(index);
                return charaButtonInfo;
            }

            private void OnTapButton(int index)
            {
                var data = EntryCharaList[index];
                //育成済みデータの詳細を取得し育成済みダイアログを出す
                var req = new ChampionsGetRankingCharaInfoRequest();
                req.target_viewer_id = data.viewer_id;
                req.team_member_id = data.team_member_id;
                req.Send(res =>
                {
                    if (res.data == null || res.data.trained_chara == null)
                        return;

                    var trainedData = new WorkTrainedCharaData.TrainedCharaData(res.data.trained_chara);
                    var setUpParam = DialogTrainedCharacterDetail.CreateSetupParameter(trainedData, UserInfo.name);
                    // 二つ名変更ボタンを使用不可にする
                    if (trainedData.ViewerId == Certification.ViewerId)
                    {
                        setUpParam.GetPartsParameter<PartsTrainedCharacterDetailHeaderTitle.SetupParameter>((param) => {
                            param.Own.NickNameChangeType = PartsTrainedCharacterDetailHeaderTitle.SetupParameter.ForMe.NickNameChange.Disable;
                            param.Own.NickNameChangeDisableText = TextId.Race0670.Text();
                        });
                    }
                    DialogTrainedCharacterDetail.Open(setUpParam);
                });
            }
        }

        [SerializeField] private ImageCommon _base;
        [SerializeField] private TextCommon _nameText; 
        [SerializeField] private CharacterButton _charaIcon;
        [SerializeField] private ButtonCommon _trainerInfoButton;
        [SerializeField] private ImageCommon _rankingBaseImage;   //順位下地
        [SerializeField] private TextCommon _rankingText; //順位
        [SerializeField] private PartsHonorIcon _honorIcon;
        [SerializeField] private ImageCommon[] _finalTrainingRankArray;
        [SerializeField] private ImageCommon[] _runStyleArray;
        [SerializeField] private CharacterButton[] _memberArray;

        private long _viewerId = 0;

        private void Start()
        {
            //SetOnClickはインスタンス分だけ実行すればいいのでStartでやってしまう
            _trainerInfoButton.SetOnClick(OnInfoClick);
        }

        public void OnUpdate(Info info)
        {
            _viewerId = info.UserInfo.viewer_id;

            //デバッグ用に所属ラウンド、グループを出す
#if CYG_DEBUG
            if (DialogChampionsRanking.DebugDisp)
            {
                var work = WorkDataManager.Instance.ChampionsData;
                var roundDetail = MasterDataManager.Instance.masterChampionsRoundDetail.Get(work.ChampionsId, info.RankInfo.last_round_id, work.GetLeague());
                _nameText.text = info.UserInfo.viewer_id + " " + roundDetail.GetRound().ToString() + " " + roundDetail.GetTier().GetText() + " " + info.RealRank + "位";
            }
            else
#endif
            {
                _nameText.text = info.UserInfo.name;
            }

            //トレーナー情報
            var frameName = info.UserInfo.viewer_id == Certification.ViewerId ? AtlasSpritePath.PreIn.LIST_BASE03 : AtlasSpritePath.PreIn.LIST_BASE02;
            _base.sprite = UIManager.PreInAtlas.GetSprite(frameName);

            var trainerCharaInfo = CharacterButtonInfo.CreateChara(info.UserInfo.leader_chara_id, info.UserInfo.leader_chara_dress_id);
            trainerCharaInfo.OnLongTap = b => OnInfoClick();
            trainerCharaInfo.OnTap = b => OnInfoClick();
            _charaIcon.Setup(trainerCharaInfo);

            //称号画像を最大１００種類(ランキングが最大１００位件)読み込むことになる(リストは毎回同じ)
            //DXT1でだいたい32KBなので100件なら32MB程度なのでとりあえず許容しとく・・・
            _honorIcon.Setup(info.UserInfo.honor_data);

            //メンバー
            for(int i = 0; i< info.EntryCharaList.Count; i++)
            {
                var entryInfo = info.EntryCharaList[i];
                _finalTrainingRankArray[i].sprite = GallopUtil.GetFinalTrainingRankSprite((GameDefine.FinalTrainingRank)entryInfo.final_grade);
                _runStyleArray[i].sprite = GallopUtil.GetCommonRunningStyleIconSprite(entryInfo.running_style);
                _memberArray[i].Setup(info.GetCharaButtonInfo(i));
            }

            //Circleから移植したランキング表示
            var rank = info.Rank;
            CircleUtil.SetRanking(rank, _rankingText, _rankingBaseImage);
        }

        private void OnInfoClick()
        {
            if (_viewerId != 0)
            {
                DialogTrainerInfo.PushDialog(_viewerId);
            }
        }

        private void Update()
        {
            _nameText.UpdatePreferedWidth();
        }
    }
}