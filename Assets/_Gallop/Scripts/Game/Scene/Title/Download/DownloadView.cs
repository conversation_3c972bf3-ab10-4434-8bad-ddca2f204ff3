using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    [AddComponentMenu("")]
    public class DownloadViewInfo : IViewInfo
    {
        public SceneDefine.ViewId _nextViewID = SceneDefine.ViewId.None;       // DL完了後に遷移するシーンID。指定しない場合は本編への遷移として扱う
        public System.Action OnDownloadFinish = null;
        public Cyan.Downloader.Request _downloadRequest = null;
        public DownloadManager.DownloadOption _downloadOption = null;
    }

    /// <summary>
    /// DL画面
    /// </summary>
    public class DownloadView : ViewBase
    {
        private const float DECIMAL_POINT = 10.0f;
        private const int DOWNLOAD_PERCENT_DECIMAL_DIGIT = 2;

        [SerializeField]
        private TextCommon _downloadSizeText = null;
        [SerializeField]
        private TextCommon _downloadWarningText = null;
        public TextCommon DownloadWarningText => _downloadWarningText;
        
        [SerializeField]
        private UnityEngine.UI.Slider _progressSlider = null;

        /// <summary>
        /// 見た目更新
        /// </summary>
        public void UpdateView(float downloadSize, float allDownloadSize)
        {
            if(Math.IsFloatEqual(allDownloadSize, 0))
            {
                _progressSlider.value = 0;
                _downloadSizeText.text = string.Format("{0:0.00}%", 0);
                return;
            }
            
            float downloadRatio = downloadSize / allDownloadSize;
            float downloadPercent = downloadRatio * 100.0f;

            int coefficent = (int)(System.Math.Pow(10, DOWNLOAD_PERCENT_DECIMAL_DIGIT));
            downloadPercent = Mathf.Ceil(downloadPercent * coefficent) / coefficent;

            _progressSlider.value = downloadRatio;
            _downloadSizeText.text = string.Format("{0:0.00}%", downloadPercent);
        }

        private static float GetMegaBytes(float bytes)
        {
            bytes /= 1024 * 1024;
            return (float)System.Math.Truncate(bytes * DECIMAL_POINT) / DECIMAL_POINT;
        }
    }

    public class DownloadViewController : ViewControllerBase<DownloadView>
    {
        private enum DownloadViewState
        {
            Downloading,
            WaitChangeView,
            RequestedChangeView
        }
        private DownloadViewState _state = DownloadViewState.Downloading;


        private DownloadViewInfo _dlViewInfo = null;

        // 画面遷移が可能になる条件制御
        // ・BeginViewが呼ばれていること ( SceneManagerのChangeViewが完了している )
        // ・BeginViewの後、一定時間が経過（ ダウンロードゲージ 100% を少しだけ見せる )
        private const float ALLOW_CHANGE_VIEW_TIME = 0.1f;
        private bool  _isBeginView = false;
        private float _allowChangeViewTime = ALLOW_CHANGE_VIEW_TIME;

        /// <summary>
        /// <para>画面間でのデータ渡し</para>
        /// <para>
        /// Awakeの直後に呼ばれます
        /// 設定されていない場合はnullが入りますので各実装で処理して下さい
        /// </para>
        /// </summary>
        /// <param name="viewInfo"></param>
        public override IEnumerator InitializeView()
        {
            _dlViewInfo = GetViewInfo() as DownloadViewInfo;

            if (_dlViewInfo == null)
            {
                // 製品版ではあり得ない想定
                DebugUtils.Assert(false, "DownloadViewに遷移する場合は適切なViewInfoが必要です"
                                         + "DonwloadViewInfoを生成・パラメータを設定し、ChangeViewに渡してください。");
                // エラー表示してソフトウェアリセットかける
                GameSystem.Instance.SoftwareReset();
                yield break;
            }
            
            //DMM版とAndroidPCは注意文なし
#if !DMM && !ANDROID_PC
            _view.DownloadWarningText.text = TextId.Title0052.Text();
#else
            _view.DownloadWarningText.text = TextId.Title0112.Text();
#endif
            
            _view.UpdateView(0, 0);

            // ダウンロード中は自動スリープしないでほしい
            GallopUtil.SetSleepEnable(false);

            // タイトル以外から来た時用にムービー再生をリクエスト
            TitleSceneController sceneController = GetSceneController<TitleSceneController>();
            if (!sceneController.IsPlayingMovie)
            {
                yield return sceneController.PlayMovieAsync();
            }
        }

        DownloadManager.IDownloadInfo _downloadInfo;

        public override void BeginView()
        {
            base.BeginView();
            _isBeginView = true;

            // ダウンロードを開始する
            _downloadInfo = DownloadManager.Instance.Download(_dlViewInfo._downloadRequest, _dlViewInfo.OnDownloadFinish,
    option: _dlViewInfo._downloadOption);
            _downloadInfo.Resume();
        }

        /// <summary>
        /// <para>Viewの更新処理</para>
        /// <para>
        /// 更新処理はこの関数で行い、MonoのUpdateは呼ばないでください
        /// </para>
        /// </summary>
        public override void UpdateView()
        {
            base.UpdateView();
            // 進捗率更新
            var current = _downloadInfo.GetDownloadedByteSize();
            var total = _downloadInfo.GetTotalByteSize();
            _view.UpdateView(current, total); 

            switch (_state)
            {
                case DownloadViewState.Downloading:                    // ダウンロード中

                    // ダウンロード終了
                    if (_downloadInfo.GetDownloadState() == DownloadManager.DownloadState.Finish)
                    {
                        _state = DownloadViewState.WaitChangeView;
                    }
                    break;

                case DownloadViewState.WaitChangeView:              // ダウンロード後のChangeView発行待ち

                    _allowChangeViewTime -= Time.deltaTime;
                    if (_allowChangeViewTime > 0.0f)
                    {
                        // ゲージ100%を見せるため、一定時間の間は画面遷移しない
                        return;
                    }

                    if (!_isBeginView)
                    {
                        // まだ画面遷移が終わってないので遷移できない
                        return;
                    }

                    _dlViewInfo.OnDownloadFinish?.Invoke();

                    // 画面遷移チェック
                    if (_dlViewInfo._nextViewID == SceneDefine.ViewId.None)
                    {
                        // 遷移先未指定の場合は本編への遷移として扱う
                        MainGameInitializer.InitializeMainGameAfterTitleDL();
                    }
                    else
                    {
                        // 指定がある場合は指定先のViewへ
                        SceneManager.Instance.ChangeView(_dlViewInfo._nextViewID);
                    }

                    _state = DownloadViewState.RequestedChangeView;
                    break;

                case DownloadViewState.RequestedChangeView:
                default:
                    return;
            }
        }

        /// <summary>
        /// View後始末
        /// </summary>
        public override IEnumerator FinalizeView()
        {
            // ダウンロードが完了したら自動スリープ設定を戻す
            GallopUtil.SetSleepEnable(true);
            // 60FPS化を解除
            FrameRateController.Instance.ResetOverride(FrameRateController.FrameRateOverrideLayer.TitleDownloading);

            yield return base.FinalizeView();
        }
    }
}
