using UnityEngine;
using System;
using System.Collections.Generic;

namespace Gallop
{
    using ParamaterType = GameDefine.ParameterType;

    /// <summary>
    /// レーダーチャート付きのステータス表示（明滅版）
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsStatusRadarChartBlink : MonoBehaviour
    {
        /// <summary>
        /// ステータスパラメータ
        /// </summary>
        public struct StatusParams
        {
            public int Speed;
            public int Stamina;
            public int Guts;
            public int Power;
            public int Wiz;

            public bool isBlinkSpeed;
            public bool isBlinkStamina;
            public bool isBlinkGuts;
            public bool isBlinkPower;
            public bool isBlinkWiz;

            public int GetParam(ParamaterType paramType)
            {
                switch (paramType)
                {
                    case ParamaterType.Speed: return Speed;
                    case ParamaterType.Stamina: return Stamina;
                    case ParamaterType.Guts: return Guts;
                    case ParamaterType.Power: return Power;
                    case ParamaterType.Wiz: return Wiz;
                }
                return 0;
            }

            public bool IsBlink(ParamaterType paramType)
            {
                switch (paramType)
                {
                    case ParamaterType.Speed: return isBlinkSpeed;
                    case ParamaterType.Stamina: return isBlinkStamina;
                    case ParamaterType.Guts: return isBlinkGuts;
                    case ParamaterType.Power: return isBlinkPower;
                    case ParamaterType.Wiz: return isBlinkWiz;
                }
                return false;
            }

            public void SetParam(ParamaterType paramType, int param, bool isBlink = false)
            {
                switch (paramType)
                {
                    case ParamaterType.Speed:
                        Speed = param;
                        isBlinkSpeed = isBlink;
                        break;
                    case ParamaterType.Stamina:
                        Stamina = param;
                        isBlinkStamina = isBlink;
                        break;
                    case ParamaterType.Guts:
                        Guts = param;
                        isBlinkGuts = isBlink;
                        break;
                    case ParamaterType.Power:
                        Power = param;
                        isBlinkPower = isBlink;
                        break;
                    case ParamaterType.Wiz:
                        Wiz = param;
                        isBlinkWiz = isBlink;
                        break;
                }
            }
        }

        private const int DEFAULT_MAX_PARAM = SingleModeDefine.PARAMETER_RADAR_CHART_MAX;    //デフォルトのチャートの最大値
        
        #region パラメータごとのUI定義

        [Serializable]
        public sealed class ParamUI
        {
            [SerializeField]
            private ImageCommon _rankIcon = null;
            public ImageCommon RankIcon { get { return _rankIcon; } }

            [SerializeField]
            private PartsOutsideBlinkImage _blinkEffect1 = null;
            public PartsOutsideBlinkImage BlinkEffect1 => _blinkEffect1;

            [SerializeField]
            private PartsOutsideBlinkImage _blinkEffect2 = null;
            public PartsOutsideBlinkImage BlinkEffect2 => _blinkEffect2;
        }

        [System.Serializable]
        public sealed class ParamUIPair : KeyAndValue<ParamaterType, ParamUI>
        {
            public ParamUIPair(ParamaterType key, ParamUI value) : base(key, value)
            {
            }
            public override KeyAndValue<ParamaterType, ParamUI> CreateCopyInstance() { return new ParamUIPair(Key, Value); }
        }

        [System.Serializable]
        public sealed class ParamUITable : TableBase<ParamaterType, ParamUI, ParamUIPair>
        {
        }

        #endregion

        [SerializeField]
        private RadarChartSprite _nowRadarChart = null;   //レーダーチャートスプライト
        [SerializeField]
        private ParamUITable _paramUITable = null;

        /// <summary>
        /// レーダーチャートの値に対する割合を設定
        /// NOTE: 下地画像はパラメータが1200(1200/2000=0.6)のときに75%に位置になるよう調整
        /// </summary>
        [SerializeField]
        private AnimationCurve _curve;

        //最大値テーブル、可変
        private Dictionary<ParamaterType, float> _maxParamDict = null;
        private StatusParams _insideStatus;   //内側のグラフ

        /// <summary>
        /// デフォルト値で初期化する
        /// </summary>
        private void InitializeMaxParamDict()
        {
            if (_maxParamDict != null)
            {
                return;
            }

            _maxParamDict = new Dictionary<ParamaterType, float>();
            var typeArray = StaticVariableDefine.SingleMode.PARAMETER_TYPE_ARRAY;
            for (int i = 0; i < typeArray.Length; i++)
            {
                _maxParamDict.Add(typeArray[i], DEFAULT_MAX_PARAM);
            }
        }

        /// <summary>
        /// 内側のグラフのパラメータ設定
        /// </summary>
        /// <param name="baseStatus"></param>
        public void SetInsideParam(StatusParams insideStatus)
        {
            _insideStatus = insideStatus;
            InitializeMaxParamDict();
            UpdateNowRadarChart();
        }

        /// <summary>
        /// 現在値レーダーチャート更新
        /// </summary>
        private void UpdateNowRadarChart()
        {
            for (int i = 0, count = EnumUtil.GetEnumElementCount<ParamaterType>(); i < count; i++)
            {
                var eParam = (ParamaterType)i;
                var value = _insideStatus.GetParam(eParam);
                var rate = GetMaxParameterRate(eParam, value);
                _nowRadarChart.SetVolume(i, _curve.Evaluate(rate));
            }

            SetStatusIcon(_insideStatus);
            _nowRadarChart.SetAllDirty();
        }

        /// <summary>
        /// ステータス値が最大ステータス値に対する比率
        /// </summary>
        private float GetMaxParameterRate(ParamaterType paramType, int param)
        {
            if (_maxParamDict == null)
            {
                Debug.LogError("レーダーチャートの最大値が未定義です。");
                return Mathf.Min(param / DEFAULT_MAX_PARAM, 1);
            }

            float rate = param / _maxParamDict[paramType];
            return Mathf.Min(rate, 1);
        }

        /// <summary>
        /// ランク表示更新
        /// </summary>
        public void SetStatusIcon(StatusParams statusParam)
        {
            for (int i = 0, count = EnumUtil.GetEnumElementCount<ParamaterType>(); i < count; i++)
            {
                var eParam = (ParamaterType)i;
                var ui = GetParamUI(eParam);
                if (ui == null)
                    continue;

                // ランクアイコン
                {
                    var baseVal = statusParam.GetParam(eParam);
                    ui.RankIcon.sprite = AtlasSpritePath.StatusRank.GetParameterRankSprite(baseVal);
                }

                // アイテムなどによるバフの明滅エフェクトのON/OFF
                var effectActive = statusParam.IsBlink(eParam);
                if (ui.BlinkEffect1 != null)
                {
                    ui.BlinkEffect1.SetEffectActive(effectActive);
                    if (effectActive)
                    {
                        ui.BlinkEffect1.ChangeBlinkAnimType(PartsOutsideBlinkImage.BlinkAnimType.Text);
                    }
                }
                if (ui.BlinkEffect2 != null)
                {
                    ui.BlinkEffect2.SetEffectActive(effectActive);
                    if (effectActive)
                    {
                        ui.BlinkEffect2.ChangeBlinkAnimType(PartsOutsideBlinkImage.BlinkAnimType.Text);
                    }
                }
            }
        }

        /// <summary>
        /// アイテムなどによるバフの明滅エフェクトのシーク
        /// </summary>
        public void BlinkEffectGoTo(float to)
        {
            for (int i = 0, count = EnumUtil.GetEnumElementCount<ParamaterType>(); i < count; i++)
            {
                var eParam = (ParamaterType)i;
                var ui = GetParamUI(eParam);
                if (ui == null)
                    continue;

                if (ui.BlinkEffect1 != null)
                {
                    ui.BlinkEffect1.GoTo(to);
                }
                if (ui.BlinkEffect2 != null)
                {
                    ui.BlinkEffect2.GoTo(to);
                }
            }
        }

        private ParamUI GetParamUI(ParamaterType paramType)
        {
            var table = _paramUITable.DataDic;
            ParamUI ui = null;
            table.TryGetValue(paramType, out ui);
            return ui;
        }
    }
}
