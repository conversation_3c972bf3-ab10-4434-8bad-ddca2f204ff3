using AnimateToUnity;
using UnityEngine;
using static Gallop.StaticVariableDefine.Race.RaceOverRunResultBase;
using static Gallop.StaticVariableDefine.Race.RaceOverRunResultLongChamp;

namespace Gallop
{
    /// <summary>
    /// オーバーラン時に表示する着順表再生クラス(ロンシャンレース場用)
    /// </summary>
    public class RaceOverRunResultLongChamp : RaceOverRunResultBase
    {
        #region 定数
        #region アニメーション再生ラベル(メインと着順を表示する赤い帯)
        /// <summary> 縦画面用Flash再生ラベル </summary>
        protected override string IN_LABEL_PORTRAIT => GameDefine.A2U_IN01_LABEL;
        /// <summary> 横画面用Flash再生ラベル </summary>
        protected override string IN_LABEL_LANDSCAPE => GameDefine.A2U_IN02_LABEL;
        #endregion アニメーション再生ラベル(メインと着順を表示する赤い帯)

        #endregion 定数

        /// <summary> ロゴアニメーション再生用ラベル </summary>
        private string _logoAnimationLabel;

        //-------------------------------------------------------------------
        /// <summary>
        /// 着順表アニメーションを配置するRootオブジェクトのAnchoredPosition
        /// </summary>
        /// <returns></returns>
        public override Vector2 GetRootAnchoredPosition => ROOT_ANCHORED_POS;

        //-------------------------------------------------------------------
        /// <summary> ContentsRootの子オブジェクトにするかどうか </summary>
        /// <returns></returns>
        public override bool IsContentsRootChild()
        {
            // ロンシャンレース場用着順アニメーションは縦画面ならContentsRootの子オブジェクトにして
            // 横画面なら帯を端まで表示したい関係上ContentsRootの子オブジェクトにしない
            return !TempData.Instance.RaceData.IsRaceLandscape;
        }

        //-------------------------------------------------------------------
        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="flashPrefab"> FlashのPrefab </param>
        /// <param name="parent"> 親にしたいTransform </param>
        /// <param name="raceInfo"></param>
        public override void Setup(GameObject flashPrefab, Transform parent, RaceInfo raceInfo)
        {
            // トライアルレース用ロゴ再生ラベル
            const string IN_LABEL_TRIAL_LOGO = GameDefine.A2U_IN01_LABEL;
            // G1レース用ロゴ再生ラベル
            const string IN_LABEL_G1_LOGO = GameDefine.A2U_IN02_LABEL;
            // チャンミレース用ロゴ再生ラベル
            const string IN_LABEL_CHAMPIONS_LOGO = GameDefine.A2U_IN03_LABEL;

            base.Setup(flashPrefab, parent, raceInfo);

            // raceInfoはここで捨てたいのでロゴ再生用のアニメーションラベルを保持しておく
            _logoAnimationLabel = IN_LABEL_TRIAL_LOGO;
            if (raceInfo.Grade == RaceDefine.Grade.G1) _logoAnimationLabel = IN_LABEL_G1_LOGO;                       // G1なら差し替え
            if (raceInfo.RaceType == RaceDefine.RaceType.Champions) _logoAnimationLabel = IN_LABEL_CHAMPIONS_LOGO;   // チャンミなら差し替え
        }

        protected override void SetupAnimationObj(RaceInfo raceInfo)
        {
            // 1着の着タイム設定
            SetupFinishTime(raceInfo);
            // 着順テキスト設定
            SetupOrderText(raceInfo);
            // 着差テキスト設定
            SetupFinishDiffText(raceInfo);
            // 各着差に応じた下地の色を設定
            SetupOrderObjColor();

        }

        protected override void SetupCallbacks()
        {
            SetupCallbackOnPlayRootAnimation();
            SetupCallbackInLogoAnimation();
            SetupCallbackInOfficial();
            SetupCallbackResultEnd();
        }

        #region 再生/停止

        /// <summary>
        /// 着順アニメーションを再生
        /// </summary>
        public override void PlayArrivalOrderAnimation()
        {
            // 縦画面用着順アニメーション再生用ラベル
            const string IN_LABEL_PORTRAIT_ARRIVAL_ORDER_ANIMATION = "in_arrival01";
            // 横画面用着順アニメーション再生用ラベル
            const string IN_LABEL_LANDSCAPE_ARRIVAL_ORDER_ANIMATION = "in_arrival02";

            _flashPlayer.gameObject.SetActive(true);
            var inLabel = (TempData.Instance.RaceData.IsRaceLandscape)
                ? IN_LABEL_LANDSCAPE_ARRIVAL_ORDER_ANIMATION
                : IN_LABEL_PORTRAIT_ARRIVAL_ORDER_ANIMATION;
            _flashPlayer.Root.RootMotion.SetMotionPlay(inLabel);
        }

        /// <summary>
        /// ロゴアニメーション再生
        /// </summary>
        private void PlayLogoAnimation()
        {
            // ロゴアニメーション再生用オブジェクト
            const string LOGO_MOT_OBJ_NAME = "MOT_img_obj_logo00";

            _flashPlayer.GetMotion(LOGO_MOT_OBJ_NAME).SetMotionPlay(_logoAnimationLabel);
        }

        /// <summary>
        /// 着差アニメーション再生
        /// </summary>
        private void PlayFinishDiffAnimation(int index)
        {
            _flashPlayer.GetMotion(FINISH_DIFF_OBJ_NAME_ARRAY[index]).SetMotionPlay(FINISH_DIFF_OBJ_PLAY_ANIMATION_LABEL_NAME);
        }

        /// <summary>
        /// 僅差だった順位のオブジェクトモーションを再生
        /// </summary>
        private void PlayNearGoalOrderAnimation()
        {
            // 各着差が僅差の時のみ馬番表示オブジェクトの下地の色を黄色から白色に変更する
            for (int i = 0; i < _nearGoalOrderObjArray.Length; i++)
            {
                if (_nearGoalOrderObjArray != null)
                {
                    _nearGoalOrderObjArray[i].SetMotionPlay(ORDER_OBJ_CHANGE_COLOR_YELLOW_TO_WHITE);
                }
            }
        }
        #endregion 再生/停止

        #region コールバック設定

        /// <summary>
        /// ロゴアニメーション開始のコールバックを設定する
        /// </summary>
        private void SetupCallbackInLogoAnimation()
        {
            // 縦画面用ロゴアニメーション再生トリガー判定用ラベル
            const string IN_LOGO_ANIMATION_TRIGGER_LABEL_PORTRAIT = "in_logo01";
            // 横画面用ロゴアニメーション再生トリガー判定用ラベル
            const string IN_LOGO_ANIMATION_TRIGGER_LABEL_LANDSCAPE= "in_logo02";

            var inLogoAnimationTriggerLabel = (TempData.Instance.RaceData.IsRaceLandscape)
                ? IN_LOGO_ANIMATION_TRIGGER_LABEL_LANDSCAPE
                : IN_LOGO_ANIMATION_TRIGGER_LABEL_PORTRAIT;
            _flashPlayer.Root.RootMotion.SetAction(inLogoAnimationTriggerLabel, PlayLogoAnimation, AnMotionActionTypes.Start);
        }

        /// <summary>
        /// Rootのアニメーションが実行された時に実行するコールバックを設定
        /// </summary>
        private void SetupCallbackOnPlayRootAnimation()
        {
            var orderObjAnimationLabelArray = (TempData.Instance.RaceData.IsRaceLandscape)
                ? ORDER_OBJ_ANIMATION_LABEL_NAME_LANDSCAPE_ARRAY
                : ORDER_OBJ_ANIMATION_LABEL_NAME_PORTRAIT_ARRAY;

            // 着差アニメーションコールバック仕込み
            for (int i = 0; i < FINISH_DIFF_OBJ_NAME_ARRAY.Length; i++)
            {
                // 僅差の場合は確定演出後に表示したいのでここでは登録しない
                if (_diffOrderInfoArray[i].IsNearGoal)
                {
                    continue;
                }
                int finishDiffAnimationIndex = i;
                // orderObjAnimationLabelArrayは1着と2着の間の着だったら2着のラベルアニメーションのタイミングで実行したいので2つ目の順位を基準とする
                // またインデックスなので -1もする
                int orderObjAnimationLabelArrayIndex = _diffOrderInfoArray[i].Order2 - 1;
                _flashPlayer.Root.RootMotion.AddAction(
                    orderObjAnimationLabelArray[orderObjAnimationLabelArrayIndex],
                    AnMotionActionTypes.Start,
                    (_) => PlayFinishDiffAnimation(finishDiffAnimationIndex),
                    null
                );
            }
        }

        /// <summary>
        /// 「result_end」のラベルアニメーションが呼ばれた時のコールバックを設定する
        /// </summary>
        private void SetupCallbackResultEnd()
        {
            // 縦画面用確定文字表示用画像モーションオブジェクト名
            const string RESULT_END_MOT_OBJ_NAME_PORTRAIT = "MOT_img_txt_proviroire_offciel00";
            // 横画面用確定文字表示用画像モーションオブジェクト名
            const string RESULT_END_MOT_OBJ_NAME_LANDSCAPE = "MOT_img_txt_proviroire_offciel01";

            var resultEndObjName = (TempData.Instance.RaceData.IsRaceLandscape)
                ? RESULT_END_MOT_OBJ_NAME_LANDSCAPE
                : RESULT_END_MOT_OBJ_NAME_PORTRAIT;
            var resultEndMotion = _flashPlayer.GetMotion(resultEndObjName);
            // 確定が表示されたタイミングで実況の電光掲示板表示フラグを上げておく
            resultEndMotion.AddAction(
                RESULT_END_LABEL, AnMotionActionTypes.Start, (_) => RaceManager.Instance.Jikkyo.IsFixedResultBoard = true, null
            );
        }

        /// <summary>
        /// 「in_official」のラベルアニメーションが呼ばれた時のコールバックを設定する
        /// </summary>
        /// <remarks>
        /// 「in_official」は着順確定演出
        /// </remarks>
        private void SetupCallbackInOfficial()
        {
            // 確定演出が開始する時のラベル名
            const string PROVISOIRE_OFFICIAL_ANIMATION_LABEL = "in_officiel";
            // 確定文字アニメーション再生用モーションオブジェクト名
            const string PROVISOIRE_OFFICIAL_MOT_OBJ_NAME_PORTRAIT = "MOT_img_txt_proviroire_offciel00";
            const string PROVISOIRE_OFFICIAL_MOT_OBJ_NAME_LANDSCAPE = "MOT_img_txt_proviroire_offciel01";

            var provisoireObjName = (TempData.Instance.RaceData.IsRaceLandscape)
                ? PROVISOIRE_OFFICIAL_MOT_OBJ_NAME_LANDSCAPE
                : PROVISOIRE_OFFICIAL_MOT_OBJ_NAME_PORTRAIT;
            // 確定文字表示後に僅差だった馬番を黄色から白色に変化させる
            var provisoireMotion = _flashPlayer.GetMotion(provisoireObjName);
            provisoireMotion.AddAction(
                PROVISOIRE_OFFICIAL_ANIMATION_LABEL, AnMotionActionTypes.Start, (_) => PlayNearGoalOrderAnimation(), null
            );

            // 僅差で非表示だった着差を表示させる
            for (int i = 0; i < FINISH_DIFF_OBJ_NAME_ARRAY.Length;i++)
            {
                // 僅差出ない場合はRootアニメーション再生時に表示するようにしてあるのでここでは登録しない
                if (!_diffOrderInfoArray[i].IsNearGoal)
                {
                    continue;
                }

                var index = i;
                provisoireMotion.AddAction(
                    PROVISOIRE_OFFICIAL_ANIMATION_LABEL, AnMotionActionTypes.Start, (_) => PlayFinishDiffAnimation(index), null
                );
            }
        }

        #endregion コールバック設定

        #region テキスト、色設定等

        /// <summary>
        /// 各着毎の着差が僅差だったら馬番の下地色を黄色に変更する
        /// </summary>
        /// <summary>
        /// 僅差だったときにモーション再生を行うオブジェクトのリストを作成
        /// </summary>
        private void SetupOrderObjColor()
        {
            for (int i = 0; i < _nearGoalOrderObjArray.Length; i++)
            {
                _nearGoalOrderObjArray[i].SetMotionPlay(ORDER_OBJ_CHANGE_COLOR_TO_YELLOW);
            }
        }

        /// <summary>
        /// 僅差だったときにアニメーション再生を行うモーションオブジェクトを順位から取得する
        /// </summary>
        protected override AnMotion GetNearGoalMotionObj(int order)
        {
            // 親オブジェクトの名前が各着毎に異なっていて取得したいオブジェクト名は共通なため一旦親オブジェクトを取得している
            // indexなので順位 - 1
            var rootObj = _flashPlayer.GetMotion(NEAR_GOAL_MOTION_OBJ_NAME_ARRAY[order - 1]);
            var motion = _flashPlayer.GetMotion(ORDER_OBJ_NAME, rootGameObject: rootObj.GameObject);
            return motion;
        }

        #endregion テキスト、色設定等

        #region ロジック

        /// <summary>
        /// ロンシャンレース場用着差取得
        /// </summary>
        /// <param name="horseLengthType"></param>
        /// <returns></returns>
        protected override string GetHorseLengthStr(RaceDefine.HorseLength horseLengthType)
        {
            return RaceUtil.GetHorseLengthFrenchString(horseLengthType);
        }

        #endregion ロジック

    }
}
