using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class EffectTransForm : MonoBehaviour
    {
        private ModelController _owner = null;
        private EffectParam.TransFormParam _param;
        private Transform _refTransFormPosition;
        private Transform _refTransFormRotation;

        private Transform _cacheTransform;

#if CYG_DEBUG
        private Vector3 _originalPosition;
        private Quaternion _originalRotation;
#endif

        public void Init(ModelController owner , EffectParam.TransFormParam param )
        {
            _owner = owner;
            _param = param;

            // ポジション参照用ノード取得
            _refTransFormPosition = _owner?.FindTransform(_param.GetPositionNodeName());
            // ローテーション参照用ノード取得
            _refTransFormRotation = _owner?.FindTransform(_param.GetRotaionNodeName());

            _cacheTransform = gameObject.transform;

#if CYG_DEBUG
            // 削除時に戻すために元のPos/Rotを保存
            _originalPosition = _cacheTransform.localPosition;
            _originalRotation = _cacheTransform.localRotation;
#endif
            // Updateコールバックを待つと1フレ原点に出るので、ここで一度姿勢初期化。
            UpdateTransform();
        }

        // 更新処理
        public void Update()
        {
            if (_cacheTransform == null)
                return;

#if CYG_DEBUG && UNITY_EDITOR
            // エフェクト作成時用：ノード更新
            NodeUpdate();
#endif

            // Transform更新
            UpdateTransform();
        }

        // Transform更新
        private void UpdateTransform()
        {
            if (_refTransFormRotation != null)
            {
                // 回転更新
                // TODO@goto_takafumi: 要修正（優先度低）：意図としてはEulerのx|y|zに0を入れたいのだと思われるが、Quaternionのx|y|zに0を入れてしまっている。
                // https://xxxxxxxxxx/redmine/issues/471837
                Quaternion rot = _refTransFormRotation.rotation;
                if (!_param.EnableRot_X) rot.x = 0.0f;
                if (!_param.EnableRot_Y) rot.y = 0.0f;
                if (!_param.EnableRot_Z) rot.z = 0.0f;
                _cacheTransform.localRotation = rot * _param.GetRotationOffset();
            }

            if (_refTransFormPosition != null)
            {
                // 座標更新
                _cacheTransform.localPosition = _refTransFormPosition.position + _cacheTransform.localRotation * _param.GetPositionOffset();
            }
        }

#if CYG_DEBUG && UNITY_EDITOR
        public void NodeUpdate()
        {
            // ポジション参照用ノード取得
            var trans = _owner?.FindTransform(_param.GetPositionNodeName());
            if ( trans != null )
            {
                _refTransFormPosition = trans;
            }
            // ローテーション参照用ノード取得
            trans = _owner?.FindTransform(_param.GetRotaionNodeName());
            if ( trans != null )
            {
                _refTransFormRotation = trans;
            }
        }
#endif

#if CYG_DEBUG
        public void OnDestroy()
        {
            if (_cacheTransform != null)
            {
                // Pos/Rotを元に戻す
                _cacheTransform.localPosition = _originalPosition;
                _cacheTransform.localRotation = _originalRotation;
            }
        }
#endif

    }
}
