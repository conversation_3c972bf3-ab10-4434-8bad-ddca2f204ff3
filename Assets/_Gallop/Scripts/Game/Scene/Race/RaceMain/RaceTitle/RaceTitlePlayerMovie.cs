using System.Collections;
using Cute.Cri;
using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    //---------------------------------------------------------------
    /// <summary>
    /// レースタイトル再生：CRIムービー。
    /// </summary>
    /// <remarks>G1レースのタイトル再生に使用する。</remarks>
    //---------------------------------------------------------------
    [AddComponentMenu("")]
    public class RaceTitlePlayerMovie : IRaceTitlePlayer
    {
        private readonly MasterRace.Race _raceMaster;
        private readonly GameObject _parent;
        private MoviePlayerHandle _moviePlayer;
        private bool _isLoaded;
        private bool _isPlaying;
        private bool _isPlaySetup;   //再生準備が終わっているか

        /// <summary> SEAudioPlayBack </summary>
        /// <remarks> 再生されないこともあるのでその時はNullのまま </remarks>
        private AudioPlayback? _sePlayback = null;
        /// <summary> SEのキュー名 </summary>
        private readonly string _seCueName;
        /// <summary> SEのキューシート名 </summary>
        private readonly string _seCueSheetName;

        //---------------------------------------------------------------
        /// <summary>
        /// コンストラクタ。
        /// </summary>
        /// <param name="parent">このオブジェクトの下に動画プレイヤーを生成する。</param>
        /// <param name="raceMaster">RaceElement.</param>
        //---------------------------------------------------------------
        public RaceTitlePlayerMovie( GameObject parent, MasterRace.Race raceMaster )
        {
            _parent = parent;
            _raceMaster = raceMaster;
            DebugUtils.Assert(_raceMaster != null);
            (_seCueName, _seCueSheetName) =
                ResourcePath.GetRaceTitleSeData((RaceDefine.RaceGroup)raceMaster.Group, (RaceDefine.Grade)raceMaster.Grade, raceMaster.Id);
        }

        //---------------------------------------------------------------
        public IEnumerator Load()
        {
            int orientation = (TempData.HasInstance() && TempData.Instance.RaceData.IsRaceLandscape) 
                ? (int)RaceDefine.RaceScreenMode.Landscape
                : (int)RaceDefine.RaceScreenMode.Portrait;
            var movieFormat = GetRaceTitleMovieFormat();
            var moviePath = ResourcePath.GetRaceTitleMoviePathWithoutExt( _raceMaster.FfAnim, _raceMaster.FfSub, string.Empty, movieFormat, orientation );
            var movieMng = Cute.Cri.MovieManager.Instance;
            _moviePlayer = movieMng.CreateMoviePlayer(MoviePlayerType.ForUI, _parent);

            movieMng.SetAdditiveMode(_moviePlayer, true);
            movieMng.Load(_moviePlayer, moviePath, () => _isLoaded = true);

            // チュートリアルレース以外の時は、以下の挙動になるように設定
            // （チュートリアルレース以外の時は、アプリ再開時のタイアログを閉じるまでは、ムービーを一時停止しておく必要があるが、
            // デフォルトでは、CRIの内部で自動でムービーの一時停止を解除する挙動になっている為）
            //
            // ・アプリのポーズ時 -> ムービーの一時停止
            // ・アプリのレジューム時 -> ムービーの一時停止したまま
            if (RaceManager.RaceInfo != null && RaceManager.RaceInfo.RaceType != RaceDefine.RaceType.Tutorial)
            {
                movieMng.SetOnApplicationPauseCustomBehavior(_moviePlayer, true, false);
            }

            while(!_isLoaded)
            {
                yield return null;
            }

            // ムービーのSEにボリューム反映。
            movieMng.SetVolume(_moviePlayer, AudioManager.GetVolume(AudioManager.Category.SE));
            
            // #17448 HUD系のUIの奥に描画するため、兄弟オブジェクト内の位置調整。
            var movieObjeTrans = movieMng.GetPlayerTransform( _moviePlayer );
            if( null != movieObjeTrans )
            {
                movieObjeTrans.SetAsFirstSibling();
            }
            
        #if false // #58840 maxFrameDrop設定すると58840が発生するためCRIに問い合わせ中（#7358）。CRI側から何らかの解決策もらうまでは#54112の発生は許容する。
            // #54112 低スぺ端末でのフレームスキップを可能にする。
            movieMng.SetMaxFrameDrop(_moviePlayer, CriManaMovieMaterial.MaxFrameDrop.Infinite);
        #endif
        }

        //---------------------------------------------------------------
        public void Unload()
        {
            // リソースの破棄はMovieManagerが行っているので未定義。

            Stop();
        }

        //---------------------------------------------------------------
        public void Play()
        {
            if (_moviePlayer.PlayerId <= 0)
            {
                DebugUtils.Assert(false, "タイトルムービーが空 raceid=" + _raceMaster.Id);
                return;
            }

            _isPlaying = true;
            _isPlaySetup = false;

            var movidManager = MovieManager.Instance;
            movidManager.Play(
                _moviePlayer,
                () =>
                {
                    _isPlaySetup = true;
                    PlaySE();
                },
                () =>
                {
                    _isPlaying = false;
                });
            MovieScreenSizeHelper.AdjustSizeFullScreen(_moviePlayer);
            movidManager.SetIsTargetForReycast(_moviePlayer, false);
            movidManager.SetPlayEndType(_moviePlayer, Cute.Cri.Movie.PlayEndType.Pause);
        }

        //---------------------------------------------------------------
        private void PlaySE()
        {
            if (!string.IsNullOrEmpty(_seCueSheetName) && !string.IsNullOrEmpty(_seCueName))
            {
                _sePlayback = AudioManager.Instance.PlaySe(_seCueSheetName, _seCueName);
            }
        }

        //---------------------------------------------------------------
        public void Stop()
        {
            _isPlaying = false;
            _isPlaySetup = false;

            MovieManager.Instance.Stop(_moviePlayer);

            if (_sePlayback.HasValue)
            {
                AudioManager.Instance.StopSe(_sePlayback.Value);
            }

            // SetPlayEndTypeでPauseを指定した場合、MoviePlayerHandleの管理するGameObjectが破棄されない。
            // その結果ムービーを一定回数再生すると、その後の再生に失敗するためClear()が必要。
            MovieManager.Instance.Clear(_moviePlayer);
        }

        //-----------------------------------------------------------
        public void Pause()
        {
            MovieManager.Instance.Pause( _moviePlayer, true );
        }
        
        //-----------------------------------------------------------
        public void Resume()
        {
            MovieManager.Instance.Pause( _moviePlayer, false );
        }

        //-----------------------------------------------------------
        public bool IsPlayingMovie()
        {
            if(!MovieManager.Instance.HasDestroyWaitPlayer && !_isPlaying)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 再生開始準備が完了しているか
        /// </summary>
        /// <returns></returns>
        public bool IsPlaySetup() => _isPlaySetup;

        /// <summary>
        /// レースタイトルで使用するムービーフォーマット取得。
        /// </summary>
        public static MovieHelper.MovieFormat GetRaceTitleMovieFormat()
        {
            return MovieHelper.GetMovieFormat();
        }

        /// <summary>
        /// Movieの場合はオブジェクト取得できなくても名刺機能的にOK
        /// (作成時に参照がローカル内で完結してるので取得できず)
        /// →何らかの状況で取得が必要になった時に取得する
        /// </summary>
        /// <returns></returns>
        public List<GameObject> GetTitleObjectList()
        {
            return null;
        }

        /// <summary>
        /// GetTitleObjectに付随してTAT内で生成されたBG用FLATOUTを一時的に
        /// 非表示にするために取得
        /// </summary>
        /// <returns></returns>
        public GameObject GetTitleBGObject()
        {
            return null;
        }
    }
}
