#if CYG_DEBUG
using System.IO;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース初期化データエクスポーター：json出力。
    /// </summary>
    //-------------------------------------------------------------------
    public class LoadRaceInfoExporterJson
    {
        public const string LoadRaceInfoFileName = "sim_loadraceinfo_data.json";
        public static string LoadRaceInfoPath 
        {
            get { return RaceDebuggerIO.RACE_RACEINFO_JSON_PATH + "/"; }
        }

        private readonly string _filePathName = "";

        //---------------------------------------------------------------
        public LoadRaceInfoExporter<PERSON>son( string filePathName )
        {
            if( string.IsNullOrEmpty( filePathName ) )
            {
                _filePathName = LoadRaceInfoPath + LoadRaceInfoFileName;
            }
            else
            {
                _filePathName = filePathName;
            }
        }

        //---------------------------------------------------------------
        /// <summary>
        /// json出力
        /// </summary>
        /// <param name="loadRaceInfo"></param>
        /// <param name="scenarioBase64">シナリオ。空文字列ならシナリオ無しでjson出力する。</param>
        /// <param name="simulateHorseDataArray">【デバッグ用】nullならデバッグ用パラメーター無しでjson出力する。</param>
        public void Export( RaceInitializer.LoadRaceInfo loadRaceInfo, string scenarioBase64, SimulateHorseData[] simulateHorseDataArray = null )
        {
            var raceParam = loadRaceInfo.ToSerializedParam( scenarioBase64, simulateHorseDataArray );
            
            var jsonStr = JsonUtility.ToJson( raceParam );

            // フォルダが無ければ作る。
            var path = Path.GetDirectoryName( _filePathName );
            if( !Directory.Exists( path ) )
            {
                Directory.CreateDirectory( path );
            }

            using( var sw = new StreamWriter( _filePathName ) )
            {
                sw.Write( jsonStr );
            }

            Cute.Core.RemoteLogger.Instance.Log( Cute.Core.RemoteLogger.Level.INFO, jsonStr );
            Cute.Core.RemoteLogger.Instance.SendAll();
        }
    }
}
#endif