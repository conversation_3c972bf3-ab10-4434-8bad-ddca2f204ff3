using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース関連ローダー：スキルカットイン。
    /// </summary>
    //-------------------------------------------------------------------
    [AddComponentMenu("")]
    public class RaceLoaderSkillCutIn : IRaceLoaderView
    {
        #region スキルカットインローダー

        //---------------------------------------------------------------
        public void RegisterPath(ref RaceLoadParam loadParam, DownloadPathRegister register, RaceMainView.Mode bootMode)
        {
            if (RaceManager.RaceInfo == null)
            {
                return;
            }

            // ハイライト版、ゴールキャプチャ撮るだけレース、リザルト直遷移不要
            // CMレースも必要なし
            if (bootMode == RaceMainView.Mode.Highlight ||
                bootMode == RaceMainView.Mode.SkipToResult ||
                bootMode == RaceMainView.Mode.OnlyGoalCapture ||
                bootMode == RaceMainView.Mode.Commercial
            )
            {
                return;
            }

            // ダイナミックカメラでは不要なのでロードしない
            // ※このタイミングではTempDataにIsRaceDynamicCameraが退避されていないので、直接SaveDataManagerの値を参照する。
            bool isDynamicCamera = RaceUtil.GetRaceDynamicCameraSettingData( RaceMainView.Mode.Normal);
#if CYG_DEBUG
            isDynamicCamera |= RaceDebugger.IsForceDynamicCamera;
#endif
            if (isDynamicCamera)
            {
                return;
            }

            // カットインシートとボイス登録。
            // カットインを再生しない設定になっている場合やストーリーレースのため再生しない場合はCutInReserve.ReservedCutInListに予約が入っていない。
            // ここではCutInReserve.ReservedCutInListにあるものを全 てDL登録してOK。
            foreach (var reserveInfo in loadParam.CutInReserve.ReservedCutInList)
            {
                var horseData = loadParam._raceInfo.RaceHorse[reserveInfo.HorseIndex];

                // シート登録。
                register.RegisterPathWithoutInfo(reserveInfo.CutInName);

                // ボイスのDL登録
                var masterCard = MasterDataManager.Instance.masterCardData.Get(horseData.CardId);
                var cueSheetName = ResourcePath.GetSkillCutInCueSheetName(masterCard.CharaId, (GameDefine.CardRarity)masterCard.DefaultRarity);
                AudioManager.Instance.RegisterDownloadByCueSheets(register, new List<string> { cueSheetName }, AudioManager.SubFolder.Voice);

                // ★３ or ★１カットインで必要なもの。
                if (reserveInfo.Category == RaceManager.CutInCategory.UniqueRare ||
                    reserveInfo.Category == RaceManager.CutInCategory.Unique)
                {
                    // スキル名Flashに刺すスキル名テクスチャ。
                    register.RegisterPathWithoutInfo(ResourcePath.GetSSRSkillNameTexturePath(reserveInfo.SkillId));
                }
                
                // ★３カットインでスキルごとに必要なもの。
                if(reserveInfo.Category == RaceManager.CutInCategory.UniqueRare)
                {
                    // カットインで再生するSE。
                    var cueSheetNameUniqueRareShort = ResourcePath.GetUniqueRareSkillCutInCueName(horseData.CardId, true);
                    var cueSheetNameUniqueRareLong = ResourcePath.GetUniqueRareSkillCutInCueName(horseData.CardId, false);
                    AudioManager.Instance.RegisterDownloadByCueSheet(register, cueSheetNameUniqueRareShort, AudioManager.SubFolder.Se);
                    AudioManager.Instance.RegisterDownloadByCueSheet(register, cueSheetNameUniqueRareLong, AudioManager.SubFolder.Se);
                }
            }
#if CYG_DEBUG
            if (RaceManager.RaceInfo.IsRaceDirectRace)
            {
                // スキルカットイン強制上書き用
                foreach (var data in RaceDebugger.OverwriteSkillCutInDict.Values)
                {
                    // ボイスのDL登録
                    var masterCard = MasterDataManager.Instance.masterCardData.Get(data.CardId);
                    if (masterCard == null) continue;
                    var cueSheetName = ResourcePath.GetSkillCutInCueSheetName(masterCard.CharaId, (GameDefine.CardRarity)masterCard.DefaultRarity);
                    AudioManager.Instance.RegisterDownloadByCueSheets(register, new List<string> { cueSheetName }, AudioManager.SubFolder.Voice);
                    
                    // カットインで再生するSE。
                    var cueSheetNameUniqueRareShort = ResourcePath.GetUniqueRareSkillCutInCueName(data.CardId, true);
                    var cueSheetNameUniqueRareLong = ResourcePath.GetUniqueRareSkillCutInCueName(data.CardId, false);
                    AudioManager.Instance.RegisterDownloadByCueSheet(register, cueSheetNameUniqueRareShort, AudioManager.SubFolder.Se);
                    AudioManager.Instance.RegisterDownloadByCueSheet(register, cueSheetNameUniqueRareLong, AudioManager.SubFolder.Se);
                }
            }
#endif
            // RaceLoaderAudioに登録されているけどスキルカットインでも使うんだよという意思表示。
            AudioManager.Instance.RegisterDownloadByCueSheet(register, ResourcePath.RACE_SE_CUESHEET_NAME, AudioManager.SubFolder.Se);

            // カットインで共通で必要なもの。
            if (loadParam.CutInReserve.HasSSRSkillCutIn || loadParam.CutInReserve.HasRareSkillCutIn)
            {
                // スキル発動Flash。
                register.RegisterPathWithoutInfo(ResourcePath.SSR_SKILL_ACTIVATE_FLASH_PATH);
                // スキル名Flash。
                register.RegisterPathWithoutInfo(ResourcePath.SSR_SKILL_NAME_FLASH_PATH);
                // スキル名Flashに刺すスキルレベルLvテクスチャ。
                register.RegisterPathWithoutInfo(ResourcePath.SSR_SKILL_LEVEL_LV_TEXTURE_PATH);
                // スキル名Flashに刺すスキルレベル数値テクスチャ。
                const int LEVEL_NUM = 10; // 0~9の10種。
                for (int i = 0; i < LEVEL_NUM; ++i)
                {
                    register.RegisterPathWithoutInfo(ResourcePath.GetSSRSkillLevelNumTexturePath(i));
                }
                
                // スキル名Flashと同時に再生するSE。
                AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_SKILLCUTIN_UNIQUE_SKILL_NAME);
            }
        }

        //---------------------------------------------------------------
        public IEnumerator OnLoadEnd(RaceMainView.Mode bootMode)
        {
            yield break;
        }
        //---------------------------------------------------------------
        public void Release()
        {
            // RegisterDownloadしかしていないので破棄無し
        }

        #endregion
    }
}
