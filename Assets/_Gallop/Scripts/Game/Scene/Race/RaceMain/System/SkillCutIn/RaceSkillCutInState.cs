using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中のスキルカットイン再生状態。
    /// </summary>
    /// <remarks>
    /// カットイン別で処理が分かれてきたのでRaceManagerReplayBaseから分離してクラス化した。
    /// レース側（RaceManagerReplayBase）とカットイン側（RaceSkillCutInHelper）との橋渡し役となる。
    ///
    /// このクラスの目的は
    /// ・カットインの再生キック
    /// ・カットイン前後の入退場処理（UIや3Dオブジェクトやサウンド）
    /// である。但し具体的な処理はIRaceSkillCutInStateImplを実装したクラス内で行う。
    /// </remarks>
    //-------------------------------------------------------------------
    public class RaceSkillCutInState
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public class CutInEnterInfo
        {
            public RaceManagerReplayBase.CutInInfo CutInInfo;
        }

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        private readonly Transform _cutInParent;
        private readonly RaceManagerReplayBase _raceManager;
        private readonly RaceSkillCutInHelper _cutInHelper;
        private readonly RaceMainViewController _raceMainView;
        private readonly IJikkyoAccessor _jikkyoAccessor;
        private readonly ICourseManager _courseManager;
        private readonly IRaceView _raceView;

        private IRaceSkillCutInStateImpl _impl;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public RaceSkillCutInState(
            Transform cutInParent,
            RaceManagerReplayBase raceManager,
            RaceSkillCutInHelper cutInHelper,
            RaceMainViewController raceMainView,
            IJikkyoAccessor jikkyoAccessor,
            ICourseManager courseManager,
            IRaceView raceView) 
        {
            _raceManager = raceManager;
            _raceMainView = raceMainView;
            _jikkyoAccessor = jikkyoAccessor;
            _cutInHelper = cutInHelper;
            _cutInParent = cutInParent;
            _courseManager = courseManager;
            _raceView = raceView;
        }

        private IRaceSkillCutInStateImpl CreateImpl(RaceManager.CutInCategory category)
        {
            switch (category)
            {
                case RaceManager.CutInCategory.Unique:
                    return new RaceSkillCutInStateImplRare(
                        _cutInParent,
                        _raceManager,
                        _cutInHelper,
                        _raceMainView,
                        _jikkyoAccessor,
                        _courseManager,
                        _raceView);

                case RaceManager.CutInCategory.UniqueRare:
                    return new RaceSkillCutInStateImplSSR(
                        _cutInParent,
                        _raceManager,
                        _cutInHelper,
                        _raceMainView,
                        _jikkyoAccessor,
                        _courseManager,
                        _raceView);

                default:
                    Debug.LogWarning($"不正なCutInCategoryです。category={category}");
                    return null;
            }
        }

        /// <summary>
        /// カットイン状態入場処理。
        /// </summary>
        public void Enter(CutInEnterInfo enterInfo)
        {
            _impl = CreateImpl(enterInfo.CutInInfo.Category);
            if (_impl != null)
            {
                _impl.Enter(enterInfo);
            }
        }

        /// <summary>
        /// カットイン状態退場処理。
        /// </summary>
        public void Exit()
        {
            if (_impl != null)
            {
                _impl.Exit();
                _impl = null;
            }
        }

        /// <summary>
        /// カットイン状態更新。
        /// </summary>
        /// <returns>カットイン再生終了したらtrue。</returns>
        public void FixedUpdate()
        {
            _impl.FixedUpdate();
        }

        /// <summary>
        /// カットイン状態更新。
        /// </summary>
        /// <returns>カットイン再生終了したらtrue。</returns>
        public bool Update(float deltaTime)
        {
            if (_impl != null)
            {
                return _impl.Update(deltaTime);
            }
            return true;
        }

        /// <summary>
        /// カットインのLateUpdate。
        /// </summary>
        public void LateUpdate()
        {
            if (_impl != null)
            {
                _impl.LateUpdate();
            }
        }
    }
}

