using System;
using System.Collections;
using Gallop.CutIn;
using Gallop.CutIn.Cutt;
using UnityEngine;
namespace Gallop
{
    /// <summary>
    /// オーバーランとレースリザルトの間に再生するカットのコントローラー
    /// </summary>
    public class RaceOverRunCutInController
    {
        /// <summary>
        /// カットイン再生で使用するCutInHelper
        /// </summary>
        private SinglemodeScenarioLegendSpGoalCutInHelper _setCutInHelper = null;
        private RaceOverRunCutInVariantApplier _variantApplier;
        private Camera _cutInCamera = null;
        private CutInModelController _setCutInModelController;

        /// <summary> 再生するBGMのAudioId</summary>
        private AudioId _bgmAudioId;
        /// <summary> 再生するSEのAudioId</summary>
        private AudioId _seAudioId;

        /// <summary> ポーズしているかどうか </summary>
        private bool _isPause = false;
        public bool IsPause => _isPause;

        public bool IsLoadCompleted => _variantApplier != null && _variantApplier.IsLoadCompleted;

        /// <summary>
        /// CMレース：カットインの生成
        /// SetupSetCutInとInitializeSetCutInに分かれているのは、カットツールから生成した場合はSetupSetCutInではなく、カットツール側でカットが用意されるため
        /// </summary>
        public IEnumerator Initialize(System.Action onPlayAction, System.Action onEndAction)
        {
            // 現状は伝説編でしか使われないので固定
            _bgmAudioId = AudioId.BGM_SINGLE_MODE_SCENARIO_LEGEND_OVER_RUN_CUT_IN;
            // モデル生成
            var playerData = RaceManager.Instance.GetPlayerHorseData();
            var courseManagerReplay = RaceManager.Instance.CourseManager as CourseManagerReplay;
            var courseBg = courseManagerReplay == null ? null : courseManagerReplay.CourseBg as CourseBg;
            yield return InitializeController(
                onPlayAction: onPlayAction,
                onEndAction: onEndAction,
                onInitFinish: null,     // 正規導線では不要
                onStartCut: null,       // 正規導線では不要
                charaId: playerData.charaId,
                dressId: playerData.RaceDressIdWithOption,
                raceResultVariantPrefab: RaceManager.Instance.RaceLoader.RaceResultVariantPrefab,
                groundType: RaceManager.RaceInfo.GroundType,
                courseBg: courseBg,
                courseManager: RaceManager.Instance.CourseManager
            );

            // カットインカメラの描画をFrameBufferに書き込むようにする
            RaceCameraManager.Instance.FrameBuffer.AddCamera(_cutInCamera);
            //152277　レース側のイメージエフェクトをなくし、カット側の設定をそのまま適用したいので、
            //レース側が残るEnvCameraをオフにする
            RaceCameraManager.Instance.SetEnvCameraEnable(false);
        }

        /// <summary>
        /// カットインの生成：フォトスタジオ専用
        /// </summary>
        /// <remarks>
        /// フォトスタジオ側ではCourseだけを用意してレース再生はしないようにしている
        /// </remarks>
        public IEnumerator InitializeForPhoto(
            System.Action onPlayAction, 
            System.Action onEndAction,
            System.Action<CutInHelper[]> onInitFinish,
            System.Action<CutInHelper> onStartCut,
            int charaId,
            int dressId,
            GameObject raceResultVariantPrefab,
            RaceDefine.GroundType groundType, 
            CourseBg courseBg)
        {
            yield return InitializeController(
                onPlayAction: onPlayAction, 
                onEndAction: onEndAction, 
                onInitFinish: onInitFinish, 
                onStartCut: onStartCut,
                charaId: charaId, 
                dressId: dressId,
                raceResultVariantPrefab: raceResultVariantPrefab, 
                groundType: groundType,
                courseBg: courseBg,
                courseManager: null    // フォトスタジオでは不要
                );
        }

        /// <summary>
        /// 初期化
        /// </summary>
        /// <remarks>
        /// 正規導線とフォトスタジオで兼用
        /// </remarks>>
        /// <param name="onPlayAction"></param>
        /// <param name="onEndAction"></param>
        /// <param name="onInitFinish"> 初期化完了時にHelper配列を渡す（フォトスタジオ） </param>
        /// <param name="onStartCut"> Cut再生時にHelperをセットする（フォトスタジオ） </param>
        /// <param name="charaId"></param>
        /// <param name="dressId"></param>
        /// <param name="raceResultVariantPrefab"></param>
        /// <param name="groundType"></param>
        /// <param name="courseBg"></param>
        /// <param name="courseManager"> 正規導線では必要、フォトスタジオでは不要 </param>
        /// <returns></returns>
        private IEnumerator InitializeController(
            System.Action onPlayAction,
            System.Action onEndAction,
            System.Action<CutInHelper[]> onInitFinish,
            System.Action<CutInHelper> onStartCut,
            int charaId,
            int dressId,
            GameObject raceResultVariantPrefab,
            RaceDefine.GroundType groundType,
            CourseBg courseBg,
            CourseManager courseManager = null
            )
        {
            //カットの生成
            _setCutInHelper = new SinglemodeScenarioLegendSpGoalCutInHelper();
            //モブの色をランダムにしておくためのフラグを呼んでおく
            _setCutInHelper.SetRandomMobColor();

            // モデル生成
            if (_setCutInModelController == null)
            {
                CharacterBuildInfo characterBuildInfo = new CharacterBuildInfo(charaId, dressId, ModelLoader.ControllerType.CutIn);
                _setCutInModelController = ModelLoader.CreateModel(characterBuildInfo).GetComponent<CutInModelController>();
            }

#if CYG_DEBUG
            // 開発終盤になってこのカットで使用するキャラモデルに汚れなどが適用されていないことが明らかになった
            // リリースタイミングではこのカットは晴れ良馬場のレースでしか使用されないので汚れや濡れは反映されなくても問題無いことになったため
            // 修正入れていない。ただし今後別条件のレースでこのカットが使用される可能性は0ではないので気づけるようエラーログだけ出すようにしている
            // また修正の際は、リザルトカットインで使用しているキャラモデルをここでも使いまわすようにすれば統一性が取れて一番良いと思われる。
            // しかしリザルト直遷移の時にどうなるかだったり気にするところが多いので注意して実装する必要がある。
            // 一番楽なのはEditableCharacterBuildInfoで定義するようにしてLoadWetTexture等を実行すること
            RaceManager.GetUseCharacterEnvironmentTexture(out var isUseWetTexture, out var isUseDirtTexture);
            if (isUseWetTexture || isUseDirtTexture)
            {
                UnityEngine.Debug.LogError("このカットで使用されているキャラモデルには汚れ、濡れが適用されていません。" +
                                           "このレースでカットを再生する場合は追加対応が必要になります");
            }
#endif
            var sceneTransform = SceneManager.Instance.GetCurrentSceneController().GetSceneTransform();
            _setCutInHelper.Init(onPlayAction, onEndAction, _setCutInModelController, sceneTransform, courseBg);
            
            // #156878: Init後からカメラは有効だが、セットアップが終わっていない状態で描画を開始すると見えてはいけないものが見えるので、
            //          セットアップが終わるまでカメラは切っておく
            _cutInCamera = _setCutInHelper.TimelineController.GetCuttControlCamera();
            _cutInCamera.enabled = false;

            // 差分Prefabの方で芝を置いてるのでリッチ芝を無効にする
            if (RaceQualitySettings.IsUseGrassFur && courseManager != null)
            {
                courseManager.SetActiveGrassFur(false);
            }

            _variantApplier = new RaceOverRunCutInVariantApplier(_setCutInHelper.TimelineController, new CutInRootPositionApplier(_setCutInHelper.TimelineController.TemporaryObjectParent));
            var course = courseBg == null ? null : courseBg.gameObject;
            if (course == null && courseManager != null)
            {
                course = courseManager.gameObject;
            }
            _variantApplier.LoadChildren(raceResultVariantPrefab, groundType, course);
            _setCutInHelper.SetVariantApplier(_variantApplier, groundType);


            // 差分Prefabの準備が終わるのを待つ
            while (!_variantApplier.IsLoadCompleted)
            {
                yield return null;
            }
            
            // 初期化できたら、必要ならカットインヘルパークラスインスタンスを渡す
            onInitFinish?.Invoke(new CutInHelper[] { _setCutInHelper });
            // SEを読み込んで再生する
            // 芝とダートで出し分ける
            _seAudioId = groundType == RaceDefine.GroundType.Turf
                ? AudioId.SFX_ADDON10_LEGEND_OVERRUN_CUTIN_TURF
                : AudioId.SFX_ADDON10_LEGEND_OVERRUN_CUTIN_DIRT;
            AudioManager.Instance.AddCueSheetByAudioId(_seAudioId);
            _setCutInHelper.AddAudioPlayback(AudioManager.Instance.PlaySe(_seAudioId)); // SE再生

            // BGM再生
            AudioManager.Instance.AddCueSheetByAudioId(_bgmAudioId);
            AudioManager.Instance.PlayBgm(_bgmAudioId);
            
            // 必要なら、カットごとに再生開始時の処理
            onStartCut?.Invoke(_setCutInHelper);

            _setCutInHelper.Play(ResourcePath.RACE_OVERRUN_CUTT_SINGLEMODE_LEGEND, sceneTransform);
            // 切っておいたカメラを戻す
            _cutInCamera.enabled = true;
            // SetCutInはRESET_TIME(-1F)で初期化されるので明示的に0Fスタートに設定する
            _setCutInHelper.TimelineController.SkipRuntime(0f);
            
            // カットインカメラの描画をFrameBufferに書き込むようにする
            if (RaceCameraManager.HasInstance())
            {
                RaceCameraManager.Instance.FrameBuffer.AddCamera(_cutInCamera);
                //152277　レース側のイメージエフェクトをなくし、カット側の設定をそのまま適用したいので、
                //レース側が残るEnvCameraをオフにする
                RaceCameraManager.Instance.SetEnvCameraEnable(false);
            }
        }
        
        /// <summary>
        /// Update処理
        /// </summary>
        public void AlterUpdate()
        {
#if CYG_DEBUG
            if (Input.GetKeyDown(KeyCode.Q))
            {
                if (_isPause)
                {
                    Resume();
                }
                else
                {
                    Pause();
                }
            }
#endif
            //カットの更新
            _setCutInHelper?.AlterUpdate();
        }

        /// <summary>
        /// LateUpdate処理
        /// </summary>
        public void AlterLateUpdate()
        {
            _setCutInHelper?.AlterLateUpdate();
        }

        /// <summary>
        /// カットの破棄
        /// </summary>
        public void Release()
        {
            //カットがなければ何もしない
            if (_setCutInHelper == null)
                return;

            if (RaceQualitySettings.IsUseGrassFur && RaceManager.HasInstance())
            {
                RaceManager.Instance.CourseManager.SetActiveGrassFur(true);
            }
            _variantApplier.Dispose();

            // BGM停止して破棄
            AudioManager.Instance.StopBgm(stopedCallback:() =>
            {
                var audioIdData = AudioDefine.GetAudioIdData(_bgmAudioId);
                AudioManager.RemoveCueSheet(audioIdData._cueSheet);
            });

            // SE破棄
            AudioManager.Instance.StopSe(_seAudioId, stopedCallback: () =>
            {
                var audioIdData = AudioDefine.GetAudioIdData(_seAudioId);
                AudioManager.RemoveCueSheet(audioIdData._cueSheet);
            });

            _setCutInHelper.CleanupPlaying();
            _setCutInHelper.CleanUp();
            _setCutInHelper.DestroyTimelineController();
            // FrameBufferから削除する
            if (RaceCameraManager.HasInstance())
            {
                RaceCameraManager.Instance.FrameBuffer.RemoveCamera(_cutInCamera);
                //152277 カット再生時オフにしていたため、オンに戻す
                RaceCameraManager.Instance.SetEnvCameraEnable(true);
            }
            if (_setCutInModelController != null)
            {
                GameObject.Destroy(_setCutInModelController.gameObject);
                _setCutInModelController = null;
            }
            _cutInCamera = null;
            _setCutInHelper = null;
        }

        /// <summary>
        /// カットインのポーズ
        /// </summary>
        public void Pause()
        {
            if (_setCutInHelper == null)
            {
                return;
            }

            _isPause = true;
#if CYG_DEBUG
            //ダイレクトシーン経由の再生でnullになる可能性がある
            if (RaceManager.HasInstance() && RaceManager.Instance.RaceView != null)
#endif
            {
                RaceManager.Instance.RaceView.PauseForResult();
            }
            _setCutInHelper.Pause(true);
            AudioManager.Instance.PauseBgm();
            AudioManager.Instance.PauseSe(_seAudioId);
        }

        public void Resume()
        {
            if (_setCutInHelper == null)
            {
                return;
            }

            _isPause = false;

#if CYG_DEBUG
            //ダイレクトシーン経由の再生でnullになる可能性がある
            if (RaceManager.HasInstance() && RaceManager.Instance.RaceView != null)
#endif
            {
                RaceManager.Instance.RaceView.ResumeForResult();
            }
            _setCutInHelper.Pause(false);
            AudioManager.Instance.ResumeBgm();
            AudioManager.Instance.ResumeSe(_seAudioId);
        }

#if CYG_DEBUG
        /// <summary>
        /// ダイレクトシーンから指定されたキャラを登場させる
        /// </summary>
        /// <param name="onPlayAction"></param>
        /// <param name="onEndAction"></param>
        /// <param name="model"></param>
        /// <param name="charaParam"></param>
        /// <param name="raceResultVariantPrefab"></param>
        /// <param name="courseBg"></param>
        /// <param name="groundType"></param>
        /// <returns></returns>
        public IEnumerator InitializeFromDirect(System.Action<CutInHelper> onPlayAction,
            System.Action<CutInHelper> onEndAction,
            CutInModelController model,
            CoursePostFilmSetGroup.CharacterParam charaParam,
            GameObject raceResultVariantPrefab,
            CourseBg courseBg,
            RaceDefine.GroundType groundType)
        {

            _setCutInModelController = model;

            yield return InitializeController(
                onPlayAction: () =>
                {
                    UIManager.Instance.SetCameraTargetFromUITexture(_setCutInHelper.TimelineController.MotionCamera
                        .targetCamera);
                    onPlayAction?.Invoke(_setCutInHelper);
                },
                onEndAction: () =>
                {
                    onEndAction?.Invoke(_setCutInHelper);
                },
                onInitFinish: null,
                onStartCut: null,
                charaId: GameDefine.INVALID_CHARA_ID,
                dressId: GameDefine.INVALID_DRESS_ID,
                raceResultVariantPrefab: raceResultVariantPrefab,
                groundType: groundType,
                courseBg: courseBg,
                courseManager: null
            );
        }

#if UNITY_EDITOR

        public float GetCutinLength_Debug()
        {
            return _setCutInHelper?.GetTotalTime() ?? 0f;
        }

        public Camera GetCutinMotionCamera_Debug()
        {
            return _setCutInHelper?.TimelineController.MotionCamera.targetCamera ?? null;
        }

#endif



#endif
    }
}
