namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース馬管理オブジェクトインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IRaceHorseManager : IRaceHorseAccessor
    {
        //---------------------------------------------------------------
        /// <summary>
        /// 初期化。
        /// </summary>
        //---------------------------------------------------------------
        void Init( RaceInfo raceInfo );
        void Release();

        /// <summary>
        /// スキルエフェクト初期化。
        /// </summary>
        void InitHorseSkillEffect(ISkillEffectPool effectPool);

        //---------------------------------------------------------------
        /// <summary>
        /// 馬をレース初期座標に配置。
        /// </summary>
        //---------------------------------------------------------------
        void InitStartPosition();

        //---------------------------------------------------------------
        /// <summary>
        /// 全馬の基本５パラメータ(Speed/Stamina/Pow/Guts/Wiz)を元にステータス計算。
        /// </summary>
        //---------------------------------------------------------------
        void RecalcParams();

        //---------------------------------------------------------------
        /// <summary>
        /// 全馬のスタート遅延時間を計算。
        /// </summary>
        //---------------------------------------------------------------
        void CalcDelayTime();

        //---------------------------------------------------------------
        /// <summary>
        /// 馬更新。
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        bool UpdateHorse( float deltaTime );

        //---------------------------------------------------------------
        /// <summary>
        /// 馬更新(強制ゴール版)。
        /// </summary>
        /// <returns></returns>
        //---------------------------------------------------------------
        void UpdateHorseForceGoal(float deltaTime);

        //---------------------------------------------------------------
        /// <summary>
        /// 全馬のスキルをクリアする。
        /// </summary>
        //---------------------------------------------------------------
        void AllHorseClearSkills();

        //---------------------------------------------------------------
        /// <summary>
        /// 全馬のスキルエフェクトを一時停止する。
        /// </summary>
        //---------------------------------------------------------------
        void AllHorsePauseSkillEffect();

        //---------------------------------------------------------------
        /// <summary>
        /// 全馬のスキルエフェクトを再開する。
        /// </summary>
        //---------------------------------------------------------------
        void AllHorseResumeSkillEffect();

        //---------------------------------------------------------------
        /// <summary>
        /// 全馬オーバーランモードON/OFF。
        /// </summary>
        //---------------------------------------------------------------
        void SetOverRunMode(bool bOn);

        /// <summary>
        /// レース中にスキップが行われたときに呼び出される。
        /// </summary>
        /// <remarks>
        /// レース終盤前→終盤、終盤以降→リザルト演出までの両方のスキップで呼び出される。
        /// </remarks>
        void OnSkip();
    }
}
