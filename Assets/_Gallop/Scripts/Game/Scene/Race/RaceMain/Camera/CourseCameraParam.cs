using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{

    /// <summary>
    /// コースカメラ制御
    /// </summary>
    public enum CourseCameraControlType
    {
        None = 0,
        Pan,            // 固定位置でターゲットを注視
        Truck_Standard, // オフセット固定でターゲット追従
        Truck_Move,     // オフセット変化でターゲット追従
        Fix,            // 位置・注視点ともに固定
        Drone,          // 指定位置間を移動
    }

    /// <summary>
    /// コースカメラターゲット
    /// </summary>
    public enum CourseCameraTargetType
    {
        None = 0,
        Shift,          // 順位順になめる
        ShiftContinue,  // 順位順になめる（順位引き継ぎ）
        InvShift,       // 順位逆順になめる
        First,          // 先頭
        Last,           // 最後尾
        Center,         // 中順位
        Player,         // プレイヤのウマ娘
        OrderDirect,    // 順位指定
        IndexDirect,    // 枠番指定
        Popularity,     // 人気順位
        PositionCenter, // 中央位置
        JikkyoOrder,   // 実況側の順位実況対象
        World,          // ワールド座標
    }

    /// <summary>
    /// コースカメラ補間
    /// </summary>
    public enum CourseInterpolationType
    {
        None = 0,
        Linear,     // 線形
        EaseIn,     // ゆっくり入る
        EaseOut,    // ゆっくり出る
        EaseInOut,  // ゆっくり入ってゆっくり出る
        Curve,      // カーブ指定
    }

    [System.Serializable]
    public class CourseBaseParam
    {
        /// <summary>
        /// 補間パラメタ
        /// </summary>
        [System.Serializable]
        public class InterpolationParamSimple
        {
            public CourseInterpolationType _type; // 補間タイプ
            public float _startTime; // 補間開始時間
            public float _time; // 補間にかける時間

            public InterpolationParamSimple()
            {
            }

            public InterpolationParamSimple(InterpolationParamSimple src)
            {
                _type = src._type;
                _startTime = src._startTime;
                _time = src._time;
            }

            public virtual float Evaluate(float time)
            {
                if (time < _startTime || Mathf.Approximately(_time, 0.0f)) { return 0.0f; }
                if (time > _startTime + _time) { return 1.0f; }

                const float HalfPI = Mathf.PI * 0.5f;
                float key = (time - _startTime) / _time;
                switch (_type)
                {
                    case CourseInterpolationType.Linear:
                        return key;
                    case CourseInterpolationType.EaseIn:
                        return 1.0f - Mathf.Cos(key * HalfPI);
                    case CourseInterpolationType.EaseOut:
                        return Mathf.Sin(key * HalfPI);
                    case CourseInterpolationType.EaseInOut:
                        key *= 2.0f;
                        return (key < 1.0f) ? 0.5f * (1.0f - Mathf.Cos(key * HalfPI)) : 0.5f * Mathf.Sin((key - 1.0f) * HalfPI) + 0.5f;
                    default:
                        return key;
                }
            }

            public bool IsInterpolate()
            {
                return NeedsParam() && (_type != CourseInterpolationType.None);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam(float param1, float param2)
            {
                return (param1 != param2) && !Math.IsFloatEqualLight(_time, 0.0f);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam()
            {
                return !Math.IsFloatEqualLight(_time, 0.0f);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam(Vector2 param1, Vector2 param2)
            {
                return (param1 != param2) && !Math.IsFloatEqualLight(_time, 0.0f);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam(Vector4 param1, Vector4 param2)
            {
                return (param1 != param2) && !Math.IsFloatEqualLight(_time, 0.0f);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam(Vector3 param1, Vector3 param2)
            {
                return (param1 != param2) && !Math.IsFloatEqualLight(_time, 0.0f);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam(Quaternion param1, Quaternion param2)
            {
                return (param1 != param2) && !Math.IsFloatEqualLight(_time, 0.0f);
            }

            /// <summary>
            /// パラメータが有効か調べる
            /// </summary>
            /// <param name="param1"></param>
            /// <param name="param2"></param>
            /// <returns></returns>
            public bool NeedsParam(Color param1, Color param2)
            {
                return (param1 != param2) && !Math.IsFloatEqualLight(_time, 0.0f);
            }

#if UNITY_EDITOR

            private static readonly string[] ControlTypeStrs =
            {
                "Pan", "Truck_Standard", "Truck_Move", "Fix", "Drone",
            };

            private static readonly string[] TargetTypeStrs =
            {
                "Shift", "ShiftContinue", "InvShift", "First", "Last", "Center",
                "Player", "OrderDirect", "IndexDirect", "Popularity", "PositionCenter",
                "JikkyoOrder", "World",
            };

            private static readonly string[] InterpolationTypeStrs =
            {
                "Linear", "EaseIn", "EaseOut", "EaseInOut",
            };

            public static string[] GetInterpolationTypeStr()
            {
                return InterpolationTypeStrs;
            }

            public static CourseCameraControlType GetControlTypeFromIndex(int index)
            {
                if (index < 0 || index >= ControlTypeStrs.Length)
                {
                    return CourseCameraControlType.None;
                }

                return (CourseCameraControlType)System.Enum.Parse(typeof(CourseCameraControlType), ControlTypeStrs[index]);
            }

            public static CourseCameraTargetType GetTargetTypeFromIndex(int index)
            {
                if (index < 0 || index >= TargetTypeStrs.Length)
                {
                    return CourseCameraTargetType.None;
                }

                return (CourseCameraTargetType)System.Enum.Parse(typeof(CourseCameraTargetType), TargetTypeStrs[index]);
            }

            public static CourseInterpolationType GetInterpolationTypeFromIndex(int index)
            {
                if (index < 0 || index >= InterpolationTypeStrs.Length)
                {
                    return CourseInterpolationType.None;
                }

                return (CourseInterpolationType)System.Enum.Parse(typeof(CourseInterpolationType), InterpolationTypeStrs[index]);
            }

            public static int GetIndexFromControlType(CourseCameraControlType type)
            {
                return Mathf.Max(System.Array.IndexOf(ControlTypeStrs, type.ToString()), 0);
            }

            public static int GetIndexFromTargetType(CourseCameraTargetType type)
            {
                return Mathf.Max(System.Array.IndexOf(TargetTypeStrs, type.ToString()), 0);
            }

            public static int GetIndexFromInterpolationType(CourseInterpolationType type)
            {
                return Mathf.Max(System.Array.IndexOf(InterpolationTypeStrs, type.ToString()), 0);
            }

            public static CourseInterpolationType GUI_InterpolationTypePopup(string labelName, CourseInterpolationType value)
            {
                return GetInterpolationTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromInterpolationType(value), InterpolationTypeStrs));
            }

            public static CourseInterpolationType GUI_InterpolationTypePopup(string labelName, CourseInterpolationType value, float width)
            {
                return GetInterpolationTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromInterpolationType(value), InterpolationTypeStrs, GUILayout.Width(width)));
            }

            public static CourseCameraControlType GUI_ControlTypePopup(string labelName, CourseCameraControlType value)
            {
                return GetControlTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromControlType(value), ControlTypeStrs));
            }

            public static CourseCameraControlType GUI_ControlTypePopup(string labelName, CourseCameraControlType value, float width)
            {
                return GetControlTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromControlType(value), ControlTypeStrs, GUILayout.Width(width)));
            }

            public static CourseCameraTargetType GUI_TargetTypePopup(string labelName, CourseCameraTargetType value)
            {
                return GetTargetTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromTargetType(value), TargetTypeStrs));
            }
#endif
        }

        /// <summary>
        /// 補間パラメタ
        /// </summary>
        [System.Serializable]
        [AddComponentMenu("")]
        public class InterpolationParam : InterpolationParamSimple
        {
            public AnimationCurve _curve; // 補間カーブ

            public InterpolationParam()
            {
                _curve = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);
            }

            public InterpolationParam(InterpolationParam src) : base(src)
            {
                _curve = new AnimationCurve(src._curve.keys);
            }

            public override float Evaluate(float time)
            {
                switch (_type)
                {
                    default:
                        return base.Evaluate(time);

                    case CourseInterpolationType.Curve:
                        {
                            if (time < _startTime || Mathf.Approximately(_time, 0.0f)) { return 0.0f; }
                            if (time > _startTime + _time) { return 1.0f; }

                            float key = (time - _startTime) / _time;
                            return _curve.Evaluate(key);
                        }
                }
            }

#if UNITY_EDITOR
            private static readonly string[] InterpolationTypeStrs =
            {
                "Linear", "EaseIn", "EaseOut", "EaseInOut", "Curve",
            };

            public new static string[] GetInterpolationTypeStr()
            {
                return InterpolationTypeStrs;
            }

            public new static CourseInterpolationType GetInterpolationTypeFromIndex(int index)
            {
                if (index < 0 || index >= InterpolationTypeStrs.Length)
                {
                    return CourseInterpolationType.None;
                }

                return (CourseInterpolationType)System.Enum.Parse(typeof(CourseInterpolationType), InterpolationTypeStrs[index]);
            }

            public new static int GetIndexFromInterpolationType(CourseInterpolationType type)
            {
                return Mathf.Max(System.Array.IndexOf(InterpolationTypeStrs, type.ToString()), 0);
            }

            public new static CourseInterpolationType GUI_InterpolationTypePopup(string labelName, CourseInterpolationType value)
            {
                return GetInterpolationTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromInterpolationType(value), InterpolationTypeStrs));
            }
#endif
        }

#if UNITY_EDITOR
        //キー操作のロック
        [System.NonSerialized]
        public bool IsLock = false;
#endif

        public float _startDistance; // カメラ開始距離

        public virtual void Set(CourseBaseParam src)
        {
            _startDistance = src._startDistance;
        }

        public CourseBaseParam(CourseBaseParam src)
        {
            Set(src);
        }

        public CourseBaseParam()
        {
        }

        public virtual CourseBaseParam Clone()
        {
            return new CourseBaseParam(this);
        }
    }

    [System.Serializable]
    public class CourseBaseParamInterp : CourseBaseParam
    {
        public CourseInterpolationType InterpolationType; // 補間タイプ
        public AnimationCurve InterpolationCurve; // 補間カーブ

        public bool IsEnabledInterpolation => (InterpolationType != CourseInterpolationType.None);

        public CourseBaseParamInterp()
        {
            _startDistance = 0.0f;
            InterpolationType = CourseInterpolationType.None;
            InterpolationCurve = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);
        }

        public CourseBaseParamInterp(CourseBaseParamInterp src)
        {
            Set(src);
        }

        public override void Set(CourseBaseParam src)
        {
            base.Set(src);

            if (src is CourseBaseParamInterp interpParam)
            {
                InterpolationType = interpParam.InterpolationType;
                InterpolationCurve = new AnimationCurve(interpParam.InterpolationCurve.keys);
            }
        }

        public void SetInterp(CourseBaseParamInterp prev, CourseBaseParamInterp next)
        {
            float t = prev.Evaluate(_startDistance, next);
            SetInterp(prev, next, t);
        }

        public virtual void SetInterp(CourseBaseParamInterp prev, CourseBaseParamInterp next, float t)
        {
        }

        public override CourseBaseParam Clone()
        {
            return new CourseBaseParamInterp(this);
        }

        public float Evaluate(float currentTime, CourseBaseParamInterp nextParam)
        {
            if ((nextParam == null) || (!nextParam.IsEnabledInterpolation))
                return 0.0f;

            float length = nextParam._startDistance - _startDistance;
            if (length < Math.EPSILON)
                return 1.0f;

            float rate = Mathf.Clamp01((currentTime - _startDistance) / length);

            switch (nextParam.InterpolationType)
            {
                case CourseInterpolationType.None:
                default:
                    return 0.0f;
                case CourseInterpolationType.Linear:
                    return rate;
                case CourseInterpolationType.EaseIn:
                    return 1.0f - Mathf.Cos(rate * Math.PI_HALF);
                case CourseInterpolationType.EaseOut:
                    return Mathf.Sin(rate * Math.PI_HALF);
                case CourseInterpolationType.EaseInOut:
                    rate *= 2.0f;
                    return (rate < 1.0f) ? 0.5f * (1.0f - Mathf.Cos(rate * Math.PI_HALF)) : 0.5f * Mathf.Sin((rate - 1.0f) * Math.PI_HALF) + 0.5f;
                case CourseInterpolationType.Curve:
                    return nextParam.InterpolationCurve.Evaluate(rate);
            }
        }

#if UNITY_EDITOR
        private static readonly string[] INTERPOLATION_TYPE_NAME_ARRAY =
        {
            "None", "Linear", "EaseIn", "EaseOut", "EaseInOut", "Curve",
        };

        public static string[] GetInterpolationTypeNameArray()
        {
            return INTERPOLATION_TYPE_NAME_ARRAY;
        }

        public static CourseInterpolationType GetInterpolationTypeFromIndex(int index)
        {
            if ((index < 0) || (index >= INTERPOLATION_TYPE_NAME_ARRAY.Length))
            {
                return CourseInterpolationType.None;
            }

            return (CourseInterpolationType)System.Enum.Parse(typeof(CourseInterpolationType), INTERPOLATION_TYPE_NAME_ARRAY[index]);
        }

        public static int GetIndexFromInterpolationType(CourseInterpolationType type)
        {
            return Mathf.Max(System.Array.IndexOf(INTERPOLATION_TYPE_NAME_ARRAY, type.ToString()), 0);
        }

        public static CourseInterpolationType GUI_InterpolationTypePopup(string labelName, CourseInterpolationType value)
        {
            return GetInterpolationTypeFromIndex(UnityEditor.EditorGUILayout.Popup(labelName, GetIndexFromInterpolationType(value), INTERPOLATION_TYPE_NAME_ARRAY));
        }
#endif
    }

    /// <summary>
    /// コースカメラパラメタ
    /// </summary>
    [System.Serializable]
    public class CourseCameraParam : CourseBaseParam
    {
        public const float DefaultTargetShiftMoveTime = 1.8f; // Shift系制御でターゲット切り替えにかかる時間

        // イベントカメラ切り替えのための情報
        public RaceCameraManager.RunningDirection _runningDirection; // 左→右・右→左のカメラ切り替え情報

        // 制御
        public CourseCameraControlType _controlType; // 制御タイプ
        public Vector3 _vec0; // 位置orオフセット
        public Vector3 _vec1; // 位置orオフセット（Truck_Move, Fix, Droneで使用）
        public InterpolationParam _vecInterpolation; // 位置orオフセット補間パラメタ

        // fov
        public float _fovStart = 10.0f; // fov開始値
        public float _fovEnd = 10.0f; // fov終了値
        public InterpolationParam _fovInterpolation; // fov補間パラメタ

        // Near / Far
        public bool isNearFar = false;
        public float nearClip = RaceCameraManager.DEFAULT_NEARCLIP;
        public float farClip = RaceCameraManager.DEFAULT_FARCLIP;

        // ターゲット
        public CourseCameraTargetType _targetType; // ターゲットタイプ
        public int _targetIndexDirect; // 直接指定
        public float _targetShiftTime; // ターゲット切り替えを開始するまでの時間（Shift系制御用）
        public float _targetShiftMoveTime = DefaultTargetShiftMoveTime; // ターゲット切り替えにかかる時間（Shift系制御用）
        public float _targetFirstShiftTimeAdd; // 最初のターゲットのみの加算時間（Shift系制御用）
        public int _targetCenterPositionFirstWeight = 2; // CenterPosition算出で先頭に追加する重み
        public bool _useTargetInterpolation; // 前のカメラのターゲットを引き継ぐ
        public bool _useLaneOriginPosition; // ラチ固定
        public float _laneOriginOffset; // ラチ固定時のラチからのオフセット距離
        public bool IsClipTargetTransOnGoal; // ターゲットがゴール後はゴール地点を注視する
        public Vector3 _targetOffset; // ターゲット位置のオフセット
        public Vector3 _targetOffsetEnd; // ターゲット位置のオフセット終了値
        public InterpolationParam _targetOffsetInterpolation; // ターゲット位置のオフセット補間パラメタ

        // カメラ補間
        public InterpolationParam _cameraInterpolation; // 位置・オフセット・注視点の補間パラメタ

        public float _startRoll; //カメラロール
        public float _endRoll; //カメラロール
        public InterpolationParamSimple _cameraRollInterpolation;

        // その他
        public bool _useJikkyouOrder; // 順位実況を行う

        public bool _startGateHide;     //スタートゲートを消す
        public bool _rachiHide;         //埒を消す
        public int[] _rachiHideNo;      //消す埒の番号
        public bool _audienceHide;      // 観客を消す

        // シェイク
        public bool _isShake;
        public Vector3 _positionShake;
        public InterpolationParamSimple _positionShakeInterpolation;
        public Vector3 _targetShake;
        public InterpolationParamSimple _targetShakeInterpolation;

        public bool _isShakePerlinNoise; // パーリンノイズを使用した振動
        // 視点用パラメータ
        public Vector3 _shakeSelfScaleStart;
        public float _shakeSelfFrequencyStart;
        public Vector3 _shakeSelfScaleEnd;
        public float _shakeSelfFrequencyEnd;
        public float ShakeSelfRotZScaleStart;
        public float ShakeSelfRotZFrequencyStart;
        public float ShakeSelfRotZScaleEnd;
        public float ShakeSelfRotZFrequencyEnd;
        // 注視点用パラメータ
        public Vector3 _shakeTargetScaleStart;
        public float _shakeTargetFrequencyStart;
        public Vector3 _shakeTargetScaleEnd;
        public float _shakeTargetFrequencyEnd;

        // ブラー
        public float _groundBlurRateStart;
        public float _groundBlurRateEnd;
        public InterpolationParamSimple _targetGroundBlurRateInterpolate;

        // Fur芝制御
        public bool _isOverrideGrassFurSetting; // 通常レースではデフォルトでFur芝制御が入っているのでそれを上書きするかどうか
        public bool _isEnableGrassFur;          // 上記で上書きする場合のOnOff
        public float _grassFadeBeginDist;       // CourseEnvParamの同じパラメータを上書きする用
        public float _grassFadeEndDist;         // CourseEnvParamの同じパラメータを上書きする用
        public float _grassFadeOffsetPower;     // CourseEnvParamの同じパラメータを上書きする用
        public float _grassShellStepScale;      // CourseEnvParamの同じパラメータを上書きする用
        public float _grassAlphaCutout;         // CourseEnvParamの同じパラメータを上書きする用
        public float _grassOcclusion;           // CourseEnvParamの同じパラメータを上書きする用
        public int _grassShellAmount;           // CourseEnvParamの同じパラメータを上書きする用
        public Vector2 _grassNoiseTexSize;      // CourseEnvParamの同じパラメータを上書きする用

        // ShadowDistance制御
        public bool _isOverrideShadowDistanceSettings;      // 通常レースではデフォルトでFur芝制御が入っているのでそれを上書きするかどうか
        public float _charaDistanceRange;                   // CourseCameraShadowDistanceParamの同じパラメータを上書きする用
        public float _shadowDistanceCorrect;                // CourseCameraShadowDistanceParamの同じパラメータを上書きする用
        public bool _isIncludeGateForCalcShadowDistance;    // ShadowDistance距離計算にゲートを含めるかどうか、キャラ位置によるDistance制御だとゲートの影が消えてしまうことがあるのでその回避用。

        public CourseCameraParam()
        {
            _vecInterpolation = new InterpolationParam();
            _fovInterpolation = new InterpolationParam();
            _targetOffsetInterpolation = new InterpolationParam();
            _cameraInterpolation = new InterpolationParam();

            _rachiHideNo = new int[CourseBaseObject.RachiNum];

            _positionShakeInterpolation = new InterpolationParamSimple();
            _targetShakeInterpolation = new InterpolationParamSimple();

            _cameraRollInterpolation = new InterpolationParamSimple();
            _targetGroundBlurRateInterpolate = new InterpolationParamSimple();
        }

        public CourseCameraParam(CourseCameraParam src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraParam(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            CourseCameraParam src = param as CourseCameraParam;
            if (src == null)
                return;

            _runningDirection = src._runningDirection;

            // カメラ制御
            _controlType = src._controlType;
            _vec0 = src._vec0;
            _vec1 = src._vec1;
            _vecInterpolation = new InterpolationParam(src._vecInterpolation);

            _fovStart = src._fovStart;
            _fovEnd = src._fovEnd;
            _fovInterpolation = new InterpolationParam(src._fovInterpolation);

            _targetType = src._targetType;
            _targetShiftTime = src._targetShiftTime;
            _targetShiftMoveTime = src._targetShiftMoveTime;
            _targetFirstShiftTimeAdd = src._targetFirstShiftTimeAdd;
            _targetCenterPositionFirstWeight = src._targetCenterPositionFirstWeight;
            _useTargetInterpolation = src._useTargetInterpolation;
            _useLaneOriginPosition = src._useLaneOriginPosition;
            _laneOriginOffset = src._laneOriginOffset;
            IsClipTargetTransOnGoal = src.IsClipTargetTransOnGoal;
            _targetOffset = src._targetOffset;
            _targetOffsetEnd = src._targetOffsetEnd;
            _targetIndexDirect = src._targetIndexDirect;
            _targetOffsetInterpolation = new InterpolationParam(src._targetOffsetInterpolation);
            _cameraInterpolation = new InterpolationParam(src._cameraInterpolation);

            _useJikkyouOrder = src._useJikkyouOrder;
            _startGateHide = src._startGateHide;
            _rachiHide = src._rachiHide;
            _rachiHideNo = new int[CourseBaseObject.RachiNum];
            if (src._rachiHideNo != null)
            {
                System.Array.Copy(src._rachiHideNo, _rachiHideNo, CourseBaseObject.RachiNum);
            }
            _audienceHide = src._audienceHide;

            _isShake = src._isShake;
            _positionShake = src._positionShake;
            _positionShakeInterpolation = new InterpolationParamSimple(src._positionShakeInterpolation);

            _targetShake = src._targetShake;
            _targetShakeInterpolation = new InterpolationParamSimple(src._targetShakeInterpolation);

            _isShakePerlinNoise = src._isShakePerlinNoise;
            _shakeSelfScaleStart = src._shakeSelfScaleStart;
            _shakeSelfFrequencyStart = src._shakeSelfFrequencyStart;
            _shakeSelfScaleEnd = src._shakeSelfScaleEnd;
            _shakeSelfFrequencyEnd = src._shakeSelfFrequencyEnd;
            ShakeSelfRotZScaleStart = src.ShakeSelfRotZScaleStart;
            ShakeSelfRotZFrequencyStart = src.ShakeSelfRotZFrequencyStart;
            ShakeSelfRotZScaleEnd = src.ShakeSelfRotZScaleEnd;
            ShakeSelfRotZFrequencyEnd = src.ShakeSelfRotZFrequencyEnd;
            _shakeTargetScaleStart = src._shakeTargetScaleStart;
            _shakeTargetFrequencyStart = src._shakeTargetFrequencyStart;
            _shakeTargetScaleEnd = src._shakeTargetScaleEnd;
            _shakeTargetFrequencyEnd = src._shakeTargetFrequencyEnd;

            _startRoll = src._startRoll;
            _endRoll = src._endRoll;
            _cameraRollInterpolation = new InterpolationParamSimple(src._cameraRollInterpolation);

            _groundBlurRateStart = src._groundBlurRateStart;
            _groundBlurRateEnd = src._groundBlurRateStart;
            _targetGroundBlurRateInterpolate = new InterpolationParamSimple(src._targetGroundBlurRateInterpolate);

            _isOverrideGrassFurSetting = src._isOverrideGrassFurSetting;
            _isEnableGrassFur = src._isEnableGrassFur;
            _grassFadeBeginDist = src._grassFadeBeginDist;
            _grassFadeEndDist = src._grassFadeEndDist;
            _grassFadeOffsetPower = src._grassFadeOffsetPower;
            _grassShellStepScale = src._grassShellStepScale;
            _grassAlphaCutout = src._grassAlphaCutout;
            _grassOcclusion = src._grassOcclusion;
            _grassNoiseTexSize = src._grassNoiseTexSize;
            _grassShellAmount = src._grassShellAmount;

            _isOverrideShadowDistanceSettings = src._isOverrideShadowDistanceSettings;
            _charaDistanceRange = src._charaDistanceRange;
            _shadowDistanceCorrect = src._shadowDistanceCorrect;
            _isIncludeGateForCalcShadowDistance = src._isIncludeGateForCalcShadowDistance;

            isNearFar = src.isNearFar;
            nearClip = src.nearClip;
            farClip = src.farClip;
        }

        public bool NeedsPositionShakeInterpolation()
        {
            if (_isShakePerlinNoise)
            {
                if ((_shakeSelfScaleStart != _shakeSelfScaleEnd) || !Mathf.Approximately(_shakeSelfFrequencyStart, _shakeSelfFrequencyEnd))
                {
                    return true;
                }
                // RotZも共用する。
                if (!Mathf.Approximately(ShakeSelfRotZScaleStart, ShakeSelfRotZScaleEnd) ||
                    !Mathf.Approximately(ShakeSelfRotZFrequencyStart, ShakeSelfRotZFrequencyEnd))
                {
                    return true;
                }
                return false;
            }
            else
            {
                return _positionShakeInterpolation.NeedsParam(_positionShake, Math.VECTOR3_ZERO);
            }
        }

        public bool NeedsTargetShakeInterpolation()
        {
            if (_isShakePerlinNoise)
            {
                return (_shakeTargetScaleStart != _shakeTargetScaleEnd) || !Mathf.Approximately(_shakeTargetFrequencyStart, _shakeTargetFrequencyEnd);
            }
            else
            {
                return _targetShakeInterpolation.NeedsParam(_targetShake, Math.VECTOR3_ZERO);
            }
        }

        public bool NeedsFovInterpolation()
        {
            return (_fovStart != _fovEnd) && !Mathf.Approximately(_fovInterpolation._time, 0.0f);
        }

        public bool NeedsVecInterpolation()
        {
            return (_vec0 != _vec1) && !Mathf.Approximately(_vecInterpolation._time, 0.0f);
        }
        public bool NeedsTargetOffsetInterpolation()
        {
            return (_targetOffset != _targetOffsetEnd) && !Mathf.Approximately(_targetOffsetInterpolation._time, 0.0f);
        }

        public bool NeedsCameraInterpolation()
        {
            return !Mathf.Approximately(_cameraInterpolation._time, 0.0f);
        }

        public bool NeedsCameraRollInterpolation()
        {
            return _cameraRollInterpolation.NeedsParam(_startRoll, _endRoll);
        }

        public bool NeedsGroundBlurRate()
        {
            return _targetGroundBlurRateInterpolate.NeedsParam(_groundBlurRateStart, _groundBlurRateEnd);
        }

        public void SetupDummyParam(RaceDefine.Rotation rotation, float dtsiance)
        {
            _startDistance = dtsiance;
            _controlType = CourseCameraControlType.Truck_Standard;
            _targetType = CourseCameraTargetType.Player;
            _vec0 = new Vector3(-83.0f, 61.0f, 150.0f);
            if (rotation == RaceDefine.Rotation.Right)
            {
                _vec0.z *= -1.0f;
            }
            _fovStart = 5.0f;
            _fovEnd = 20.0f;
            _targetOffset = new Vector3(0.0f, 0.6f, 0.0f);
            _targetShiftTime = 1;

            _isOverrideGrassFurSetting = false;
            _isEnableGrassFur = false;
            _isOverrideShadowDistanceSettings = false;
        }

#if UNITY_EDITOR

        /// <summary>
        /// CameraParamからのコンバート
        /// 設定形式が違うため全く同一の設定はできない箇所もある
        /// </summary>
        public CourseCameraParam(CameraParam src)
        {
            _startDistance = src.activeDist;

            _runningDirection = RaceCameraManager.RunningDirection.LtoR;

            // カメラ制御
            _vec0 = src.position;
            _vecInterpolation = new InterpolationParam();
            _vecInterpolation._type = CourseInterpolationType.Linear;

            _fovStart = src.fov;
            _fovEnd = src.fov;
            _fovInterpolation = new InterpolationParam();
            _fovInterpolation._type = CourseInterpolationType.Linear;

            _targetShiftTime = src.targetShiftTime;
            _targetShiftMoveTime = DefaultTargetShiftMoveTime;
            _targetFirstShiftTimeAdd = src._firstShiftTimeAdd;
            _useTargetInterpolation = src.useTargetInterpolation;
            _useLaneOriginPosition = src.laneOriginPosition;
            _laneOriginOffset = src.laneOffset;
            _targetOffset = src.offset;
            _targetOffsetEnd = src.offset;
            _targetOffsetInterpolation = new InterpolationParam();
            _targetOffsetInterpolation._type = CourseInterpolationType.Linear;

            _cameraInterpolation = new InterpolationParam();
            _cameraInterpolation._type = CourseInterpolationType.Linear;
            _cameraInterpolation._time = src.interpolationTime;

            _useJikkyouOrder = src._isUseJikkyouOrder;
            _startGateHide = src._isStartGateHide;
            _rachiHide = src._isRachiHide;
            _rachiHideNo = new int[CourseBaseObject.RachiNum];
            if (src._rachiNo != null)
            {
                System.Array.Copy(src._rachiNo, _rachiHideNo, CourseBaseObject.RachiNum);
            }
            _audienceHide = src._isAudienceHide;

            if (!TargetTypeTable.TryGetValue(src.targetType, out _targetType))
            {
                _targetType = CourseCameraTargetType.Player;
            }

            switch (src.ctrlType)
            {
                case CameraCtrlType.Pan:
                    _controlType = CourseCameraControlType.Pan;
                    _vec1 = Math.VECTOR3_ZERO;
                    break;
                case CameraCtrlType.PanZoom:
                    _controlType = CourseCameraControlType.Pan;
                    _vec1 = Math.VECTOR3_ZERO;
                    _fovEnd = src.param1;
                    _fovInterpolation._time = src.param2;
                    _fovInterpolation._type = (CourseInterpolationType)src.param3;
                    break;
                case CameraCtrlType.Truck:
                case CameraCtrlType.Truck2:
                    _controlType = CourseCameraControlType.Truck_Standard;
                    _vec1 = Math.VECTOR3_ZERO;
                    if (src.param1 > 0.0f)
                    {
                        _fovEnd = src.param1;
                        _fovInterpolation._time = 3.0f;
                    }
                    break;
                case CameraCtrlType.Truck3:
                    _controlType = CourseCameraControlType.Truck_Move;
                    _vec1 = new Vector3(src.param1, src.param2, src.param3);
                    _vecInterpolation._time = src.param4;
                    break;
                case CameraCtrlType.Fix:
                    _controlType = CourseCameraControlType.Fix;
                    _vec1 = new Vector3(src.param1, src.param2, src.param3);
                    break;
                case CameraCtrlType.Crane:
                    _controlType = CourseCameraControlType.Drone;
                    _vec1 = new Vector3(src.param1, src.param2, src.param3);
                    _vecInterpolation._time = src.param4;
                    break;
                default:
                    // 未使用・未実装・不正なものは適当なTruckカメラを付けておく
                    _controlType = CourseCameraControlType.Truck_Standard;
                    _vec0 = new Vector3(20.0f, 5.0f, 25.0f);
                    _vec1 = Math.VECTOR3_ZERO;
                    _fovStart = _fovEnd = 6.0f;
                    _targetType = CourseCameraTargetType.Player;
                    _targetOffset = new Vector3(0.0f, -0.5f, 0.0f);
                    break;
            }

        }
        static Dictionary<CameraTargetType, CourseCameraTargetType> TargetTypeTable = new Dictionary<CameraTargetType, CourseCameraTargetType>()
        {
            { CameraTargetType.None, CourseCameraTargetType.Player },
            { CameraTargetType.Shift, CourseCameraTargetType.Shift },
            { CameraTargetType.ShiftContinue, CourseCameraTargetType.ShiftContinue },
            { CameraTargetType.InvShift, CourseCameraTargetType.InvShift },
            { CameraTargetType.First, CourseCameraTargetType.First },
            { CameraTargetType.Last, CourseCameraTargetType.Last },
            { CameraTargetType.Center, CourseCameraTargetType.Center },
            { CameraTargetType.PositionCenter, CourseCameraTargetType.PositionCenter },
            { CameraTargetType.Player, CourseCameraTargetType.Player },
            { CameraTargetType.PlayerHead, CourseCameraTargetType.Player },
            { CameraTargetType.PlayerPelvis, CourseCameraTargetType.Player },
            { CameraTargetType.PlayerFoot, CourseCameraTargetType.Player },
            { CameraTargetType.OrderDirect, CourseCameraTargetType.OrderDirect },
            { CameraTargetType.IndexDirect, CourseCameraTargetType.IndexDirect },
        };

#endif

    }

    /// <summary>
    /// コースカメライメージエフェクトパラメータ
    /// </summary>
    [System.Serializable]
    public class CourseFlipParam : CourseBaseParam
    {
        public bool _isRunningDirectionFlip;

        public CourseFlipParam(CourseFlipParam src)
        {
            Set(src);
        }

        public CourseFlipParam()
        {
        }

        public override CourseBaseParam Clone()
        {
            return new CourseFlipParam(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);

            CourseFlipParam src = param as CourseFlipParam;
            if (src == null)
                return;

            _isRunningDirectionFlip = src._isRunningDirectionFlip;
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }

#if UNITY_EDITOR

        /// <summary>
        /// CameraParamからのコンバート
        /// 設定形式が違うため全く同一の設定はできない箇所もある
        /// </summary>
        public CourseFlipParam(CameraParam src)
        {
            _startDistance = src.activeDist;
        }

#endif
    }

    #region DOF

    /// <summary>
    /// DOF
    /// CourseCamera用、StoryRaceはCourseDOFParamを使用
    /// </summary>
    [System.Serializable]
    public class CourseCameraDOF : CourseBaseParam
    {
        /// <summary>
        /// CourseCameraは1キーで開始と終了値を持つのでデータの2重持ちが発生するので内部でラップしておく
        /// </summary>
        [System.Serializable]
        public class Param
        {
            public float FocalDistance;
            public float FocalSize;

            public Vector3 FocalPosition;
            public float MaxBlurSpread;
            public float Smoothness;
            public float ForegroundSize;

            public void Set(Param src)
            {
                FocalDistance = src.FocalDistance;
                FocalSize = src.FocalSize;
                FocalPosition = src.FocalPosition;
                MaxBlurSpread = src.MaxBlurSpread;
                Smoothness = src.Smoothness;
                ForegroundSize = src.ForegroundSize;
            }
        }

        public bool IsEnable;

        public DepthBlurAndBloom.DofQuality DofQuality = DepthBlurAndBloom.DofQuality.OnlyBackground;
        public DepthBlurAndBloom.DofFocalType DofFocalType;
        public DepthBlurAndBloom.DofBlur DofBlurType;

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraDOF()
        {
        }

        public CourseCameraDOF(CourseCameraDOF src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraDOF(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraDOF;
            if (src == null)
                return;

            IsEnable = src.IsEnable;

            DofQuality = src.DofQuality;
            DofFocalType = src.DofFocalType;
            DofBlurType = src.DofBlurType;

            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region ColorCorrection

    [System.Serializable]
    public class CourseCameraColorCorrection : CourseBaseParam
    {
        [System.Serializable]
        public class Param
        {
            public float ColorCorrectionSaturation;

            public void Set(Param src)
            {
                ColorCorrectionSaturation = src.ColorCorrectionSaturation;
            }
        }

        public bool IsEnable;

        public Param Start = new Param();
        public Param End = new Param();

        public AnimationCurve ColorCorrectionRedChannel = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);
        public AnimationCurve ColorCorrectionGreenChannel = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);
        public AnimationCurve ColorCorrectionBlueChannel = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);

        [System.NonSerialized]
        public bool IsUpdateColorCorrectionParam = true;

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraColorCorrection()
        {
        }

        public CourseCameraColorCorrection(CourseCameraColorCorrection src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraColorCorrection(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraColorCorrection;
            if (src == null)
                return;

            IsEnable = src.IsEnable;
            Start.Set(src.Start);
            End.Set(src.End);

            ColorCorrectionRedChannel = new AnimationCurve(src.ColorCorrectionRedChannel.keys);
            ColorCorrectionGreenChannel = new AnimationCurve(src.ColorCorrectionGreenChannel.keys);
            ColorCorrectionBlueChannel = new AnimationCurve(src.ColorCorrectionBlueChannel.keys);

            Interpolation = new InterpolationParamSimple(src.Interpolation);

            IsUpdateColorCorrectionParam = true;
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region SunShaft

    [System.Serializable]
    public class CourseCameraSunShaft : CourseBaseParam
    {
        [System.Serializable]
        public class Param
        {
            public Vector3 SunPosition = Gallop.Math.VECTOR3_ZERO;
            public Color SunColor = Color.white;
            public float Power = 0f;
            public float BlurRadius = 0.0f;
            public float CenterBrightness;
            public float CenterMultiplex;

            public void Set(Param src)
            {
                SunPosition = src.SunPosition;
                SunColor = src.SunColor;
                Power = src.Power;
                BlurRadius = src.BlurRadius;
                CenterBrightness = src.CenterBrightness;
                CenterMultiplex = src.CenterMultiplex;
            }
        }
        public bool IsEnable = true;
        public bool IsEnabledBorderClear = false;
        public float Komorebi = 0.0f;

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraSunShaft()
        {
        }

        public CourseCameraSunShaft(CourseCameraSunShaft src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraSunShaft(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraSunShaft;
            if (src == null)
                return;

            IsEnable = src.IsEnable;
            IsEnabledBorderClear = src.IsEnabledBorderClear;
            Komorebi = src.Komorebi;
            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region PostFilm

    [System.Serializable]
    public class CourseCameraPostFilm : CourseBaseParam
    {
        [System.Serializable]
        public class OverlayParam
        {
            public void Set(OverlayParam src)
            {
                PostFilmPower = src.PostFilmPower;
                PostFilmOffsetParam = src.PostFilmOffsetParam;
                PostFilmOptionParam = src.PostFilmOptionParam;
                DepthPower = src.DepthPower;
                FilmRoll = src.FilmRoll;
                FilmScale = src.FilmScale;
                InverseVignette = src.InverseVignette;
            }

            public float PostFilmPower;
            public Vector2 PostFilmOffsetParam;
            public Vector4 PostFilmOptionParam;
            public float DepthPower;
            public float FilmRoll;
            public Vector2 FilmScale = Gallop.Math.VECTOR2_ONE;
            public bool InverseVignette;
        }

        [System.Serializable]
        public class Param
        {
            public OverlayParam[] Overlay;

            public Param()
            {
                Overlay = new OverlayParam[CourseImageEffectParam.SCREEN_OVERLAY_MAX];
                for (int i = 0; i < Overlay.Length; i++)
                {
                    Overlay[i] = new OverlayParam();
                }
            }

            public void Set(Param src)
            {
                for (int i = 0; i < Overlay.Length; i++)
                {
                    Overlay[i].Set(src.Overlay[i]);
                }
            }
        }

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraPostFilm()
        {
        }

        public CourseCameraPostFilm(CourseCameraPostFilm src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraPostFilm(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraPostFilm;
            if (src == null)
                return;

            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region RadialBlur

    [System.Serializable]
    public class CourseCameraRadialBlur : CourseBaseParam
    {
        /// <summary>
        /// CourseCameraは1キーで開始と終了値を持つのでデータの2重持ちが発生するので内部でラップしておく
        /// </summary>
        [System.Serializable]
        public class Param
        {
            public Vector2 CenterOffset = Math.VECTOR2_HALF;
            public Vector2 EllipseDirScale = Math.VECTOR2_ONE;   //楕円時の移動向きスケール量
            public float RollEulerAngles;
            public float BlurPower = 1.0f;
            public float BlurStartArea = 0.0f;
            public float BlurEndArea = 1.0f;

            public float DepthPowerFront;
            public float DepthPowerBack;

            public Vector4 DepthCancelRect;
            public float DepthCancelBlendLength;

            public void Set(Param src)
            {
                CenterOffset = src.CenterOffset;
                EllipseDirScale = src.EllipseDirScale;
                RollEulerAngles = src.RollEulerAngles;
                BlurPower = src.BlurPower;
                BlurStartArea = src.BlurStartArea;
                BlurEndArea = src.BlurEndArea;
                DepthPowerFront = src.DepthPowerFront;
                DepthPowerBack = src.DepthPowerBack;
                DepthCancelRect = src.DepthCancelRect;
                DepthCancelBlendLength = src.DepthCancelBlendLength;
            }
        }

        public ImageEffect.RadialBlurParam.MoveBlurType BlurType = ImageEffect.RadialBlurParam.MoveBlurType.None;
        public int Downsample = 3;
        public int BlurIteration = 1;

        public bool IsEnableDepth;
        public bool IsEnableDepthCancelRect;
        public bool IsExpandCancelRect;

#if UNITY_EDITOR
        [System.NonSerialized]
        public bool IsDebugCancelRect;
#endif

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraRadialBlur()
        {
        }

        public CourseCameraRadialBlur(CourseCameraRadialBlur src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraRadialBlur(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraRadialBlur;
            if (src == null)
                return;

            BlurType = src.BlurType;
            Downsample = src.Downsample;
            BlurIteration = src.BlurIteration;
            IsEnableDepth = src.IsEnableDepth;
            IsEnableDepthCancelRect = src.IsEnableDepthCancelRect;
            IsExpandCancelRect = src.IsExpandCancelRect;
#if UNITY_EDITOR
            IsDebugCancelRect = src.IsDebugCancelRect;
#endif

            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region LensDistortion

    [System.Serializable]
    public class CourseCameraLensDistortion : CourseBaseParam
    {
        /// <summary>
        /// CourseCameraは1キーで開始と終了値を持つのでデータの2重持ちが発生するので内部でラップしておく
        /// </summary>
        [System.Serializable]
        public class Param
        {
            public float Intensity = 0f;
            public float IntensityX = 1f;
            public float IntensityY = 1f;
            public float CenterX = 0f;
            public float CenterY = 0f;
            public float Scale = 1f;

            public void Set(Param src)
            {
                Intensity = src.Intensity;
                IntensityX = src.IntensityX;
                IntensityY = src.IntensityY;
                CenterX = src.CenterX;
                CenterY = src.CenterY;
                Scale = src.Scale;
            }
        }

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraLensDistortion()
        {
        }

        public CourseCameraLensDistortion(CourseCameraLensDistortion src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraLensDistortion(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraLensDistortion;
            if (src == null)
                return;

            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region Fluctuation

    [System.Serializable]
    public class CourseCameraFluctuation : CourseBaseParam
    {
        /// <summary>
        /// CourseCameraは1キーで開始と終了値を持つのでデータの2重持ちが発生するので内部でラップしておく
        /// </summary>
        [System.Serializable]
        public class Param
        {
            public Vector2 MoveDirection = Gallop.Math.VECTOR2_UP;
            public float MovePower = 0.05f;
            public float Power = 0.01f;
            public float DepthClip = 0.3f;

            public void Set(Param src)
            {
                MoveDirection = src.MoveDirection;
                MovePower = src.MovePower;
                Power = src.Power;
                DepthClip = src.DepthClip;
            }
        }

        /// <summary>
        /// 有効/無効フラグ
        /// </summary>
        public bool IsEnable = false;

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraFluctuation()
        {
        }

        public CourseCameraFluctuation(CourseCameraFluctuation src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraFluctuation(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraFluctuation;
            if (src == null)
                return;

            IsEnable = src.IsEnable;
            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region ChromaticAberration

    [System.Serializable]
    public class CourseCameraChromaticAberration : CourseBaseParam
    {
        /// <summary>
        /// CourseCameraは1キーで開始と終了値を持つのでデータの2重持ちが発生するので内部でラップしておく
        /// </summary>
        [System.Serializable]
        public class Param
        {
            public Vector2 RedOffset;
            public Vector2 GreenOffset;
            public Vector2 BlueOffset;
            public float Power = 1.0f;
            public float Clip = 0.0f;

            public void Set(Param src)
            {
                RedOffset = src.RedOffset;
                GreenOffset = src.GreenOffset;
                BlueOffset = src.BlueOffset;
                Power = src.Power;
                Clip = src.Clip;
            }
        }

        public bool IsEnable = false;
        public ImageEffect.ChromaticAberration.ChromaticAberrationType EffectType;

        public Param Start = new Param();
        public Param End = new Param();

        [System.NonSerialized]
        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraChromaticAberration()
        {
        }

        public CourseCameraChromaticAberration(CourseCameraChromaticAberration src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraChromaticAberration(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraChromaticAberration;
            if (src == null)
                return;

            IsEnable = src.IsEnable;
            EffectType = src.EffectType;
            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    #region MultiCamera

    [System.Serializable]
    public class CourseCameraMultiCamera : CourseCameraParam
    {
        [System.Serializable]
        public class Param
        {
            public Vector3 MaskPosition;
            public Quaternion MaskRotation;
            public Vector3 MaskScale;
            public float MainCameraOffset;

            public Param()
            {
                MaskPosition = new Vector3(0f, 0f, 0.31f);  //NearClipが0.3なので底より少し離す
                MaskRotation = Math.QUATERNION_IDENTITY;
                MaskScale = Math.VECTOR3_ONE;
                MainCameraOffset = 0;
            }
            public void Set(Param src)
            {
                MaskPosition = src.MaskPosition;
                MaskRotation = src.MaskRotation;
                MaskScale = src.MaskScale;
                MainCameraOffset = src.MainCameraOffset;
            }
        }
        public bool IsEnable = false;

        public Param Start = new Param();
        public Param End = new Param();

        public InterpolationParamSimple Interpolation = new InterpolationParamSimple();

        public CourseCameraMultiCamera()
        {
        }

        public CourseCameraMultiCamera(CourseCameraParam src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraMultiCamera(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            var src = param as CourseCameraMultiCamera;
            if (src == null)
                return;

            IsEnable = src.IsEnable;

            Start.Set(src.Start);
            End.Set(src.End);

            Interpolation = new InterpolationParamSimple(src.Interpolation);
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }
    }

    #endregion

    /// <summary>
    /// コースカメライメージエフェクトパラメータ
    /// </summary>
    [System.Serializable]
    public class CourseImageEffectParam : CourseBaseParam
    {
        [System.Serializable]
        public class ScreenOverlayParam
        {
            public float postFilmPower = 0.0f;

            public Vector2 postFilmOffsetParam = Vector4.zero;
            public Vector4 postFilmOptionParam = Vector4.zero;

            public bool inverseVignette = false;

            public ScreenOverlayParam()
            {

            }

            public ScreenOverlayParam(ScreenOverlayParam src)
            {
                postFilmPower = src.postFilmPower;

                postFilmOffsetParam = src.postFilmOffsetParam;
                postFilmOptionParam = src.postFilmOptionParam;

                inverseVignette = src.inverseVignette;
            }
        }

        // 以下、ポストエフェクト

        // DOF
        public bool _useDOF;
        public bool _dofFocalTarget;
        public float _dofFocalDistance;
        public float _dofFocalSize;
        public float _dofAperture;
        public float _dofMaxBlurDistance;
        public Vector3 _dofFocalPosition;
        public float _dofMaxBlurSpread;
        public float _dofSmoothness;

        // ColorCorrection
        public bool _useColorCorrection;
        public float _colorCorrectionSaturation = 1.0f;
        public AnimationCurve _colorCorrectionRedChannel;
        public AnimationCurve _colorCorrectionGreenChannel;
        public AnimationCurve _colorCorrectionBlueChannel;
        public bool _isUpdateColorCorrectionParam;

        //SunShafts
        public bool _useSunShafts;
        public float _sunShaftsBlurRadius;
        public Color _sunShaftsColor = Color.white;
        public float _sunShaftsPower = -1.0f;
        public float _sunShaftsCenterBrightness = 1.5f;
        public float _sunShaftsCenterMultiplex = 1.0f;
        public bool _sunShaftsKomorebi = false;
        public bool _sunShaftsBoarderClear = false;
        public Vector3 _sunShaftsPosition = Vector3.zero;

        //PostFilm
        public enum ScreenOverlayIndex
        {
            First = 0,
            Second,
            Max
        }

        public const int SCREEN_OVERLAY_MAX = (int)ScreenOverlayIndex.Max;

        public ScreenOverlayParam[] _screenOverlayParam;


        public CourseImageEffectParam()
        {
            _colorCorrectionRedChannel = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);
            _colorCorrectionGreenChannel = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);
            _colorCorrectionBlueChannel = AnimationCurve.Linear(0.0f, 0.0f, 1.0f, 1.0f);

            _screenOverlayParam = new ScreenOverlayParam[(int)ScreenOverlayIndex.Max];
            for (int i = 0; i < (int)ScreenOverlayIndex.Max; i++)
            {
                _screenOverlayParam[i] = new ScreenOverlayParam();
            }
        }

        public CourseImageEffectParam(CourseImageEffectParam src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseImageEffectParam(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);
            CourseImageEffectParam src = param as CourseImageEffectParam;
            if (src == null)
                return;

            // ポストエフェクト
            _useDOF = src._useDOF;
            _dofFocalTarget = src._dofFocalTarget;
            _dofFocalDistance = src._dofFocalDistance;
            _dofFocalSize = src._dofFocalSize;
            _dofAperture = src._dofAperture;
            _dofMaxBlurDistance = src._dofMaxBlurDistance;

            _useColorCorrection = src._useColorCorrection;
            _colorCorrectionRedChannel = new AnimationCurve(src._colorCorrectionRedChannel.keys);
            _colorCorrectionGreenChannel = new AnimationCurve(src._colorCorrectionGreenChannel.keys);
            _colorCorrectionBlueChannel = new AnimationCurve(src._colorCorrectionBlueChannel.keys);
            _isUpdateColorCorrectionParam = src._isUpdateColorCorrectionParam;

            _useSunShafts = src._useSunShafts;
            _sunShaftsBlurRadius = src._sunShaftsBlurRadius;
            _sunShaftsColor = src._sunShaftsColor;
            _sunShaftsPower = src._sunShaftsPower;
            _sunShaftsCenterBrightness = src._sunShaftsCenterBrightness;
            _sunShaftsCenterMultiplex = src._sunShaftsCenterMultiplex;
            _sunShaftsKomorebi = src._sunShaftsKomorebi;
            _sunShaftsBoarderClear = src._sunShaftsBoarderClear;
            _sunShaftsPosition = src._sunShaftsPosition;

            _screenOverlayParam = new ScreenOverlayParam[(int)ScreenOverlayIndex.Max];
            for (int i = 0; i < (int)ScreenOverlayIndex.Max; i++)
            {
                _screenOverlayParam[i] = new ScreenOverlayParam(src._screenOverlayParam[i]);
            }
        }

        public void SetupDummyParam(float distance)
        {
            _startDistance = distance;
        }

#if UNITY_EDITOR

        public void CopyToDOF(CourseCameraDOF dst)
        {
            dst._startDistance = _startDistance;
            dst.IsEnable = _useDOF;
            dst.DofFocalType = DepthBlurAndBloom.DofFocalType.Position;

            CourseCameraDOF.Param param = new CourseCameraDOF.Param();

            param.FocalDistance = _dofFocalDistance;
            param.FocalSize = _dofFocalSize;
            param.FocalPosition = _dofFocalPosition;
            param.MaxBlurSpread = _dofMaxBlurSpread;
            param.Smoothness = _dofSmoothness;

            dst.Start.Set(param);
            dst.End.Set(param);
        }

        public void CopyToColorCorrection(CourseCameraColorCorrection dst)
        {
            dst._startDistance = _startDistance;
            dst.IsEnable = _useColorCorrection;
            dst.ColorCorrectionBlueChannel = new AnimationCurve(_colorCorrectionRedChannel.keys);
            dst.ColorCorrectionGreenChannel = new AnimationCurve(_colorCorrectionGreenChannel.keys);
            dst.ColorCorrectionBlueChannel = new AnimationCurve(_colorCorrectionBlueChannel.keys);

            CourseCameraColorCorrection.Param param = new CourseCameraColorCorrection.Param();
            param.ColorCorrectionSaturation = _colorCorrectionSaturation;

            dst.Start.Set(param);
            dst.End.Set(param);
        }

        public void CopyToSunShaft(CourseCameraSunShaft dst)
        {
            dst._startDistance = _startDistance;
            dst.IsEnable = _useSunShafts;
            dst.IsEnabledBorderClear = _sunShaftsBoarderClear;

            CourseCameraSunShaft.Param param = new CourseCameraSunShaft.Param();
            param.BlurRadius = _sunShaftsBlurRadius;
            param.SunColor = _sunShaftsColor;
            param.Power = _sunShaftsPower;
            param.CenterBrightness = _sunShaftsCenterBrightness;
            param.CenterMultiplex = _sunShaftsCenterMultiplex;
            param.SunPosition = _sunShaftsPosition;

            dst.Komorebi = _sunShaftsKomorebi ? 1.0f : 0.0f;
            dst.Start.Set(param);
            dst.End.Set(param);
        }

        public void CopyToPostFilm(CourseCameraPostFilm dst)
        {
            dst._startDistance = _startDistance;

            CourseCameraPostFilm.Param param = new CourseCameraPostFilm.Param();
            for (int i = 0; i < param.Overlay.Length; i++)
            {
                param.Overlay[i].InverseVignette = _screenOverlayParam[i].inverseVignette;
                param.Overlay[i].PostFilmPower = _screenOverlayParam[i].postFilmPower;
                param.Overlay[i].PostFilmOffsetParam = _screenOverlayParam[i].postFilmOffsetParam;
                param.Overlay[i].PostFilmOptionParam = _screenOverlayParam[i].postFilmOptionParam;
            }

            dst.Start.Set(param);
            dst.End.Set(param);
        }

        /// <summary>
        /// CameraParamからのコンバート
        /// 設定形式が違うため全く同一の設定はできない箇所もある
        /// </summary>
        public CourseImageEffectParam(CameraParam src)
        {
            _startDistance = src.activeDist;

            // ポストエフェクト
            _useDOF = src.useDOF;
            _dofFocalTarget = src.dofFocalTarget;
            _dofFocalDistance = src.dofFocalDistance;
            _dofFocalSize = src.dofFocalSize;
            _dofAperture = src.dofAperture;
            _dofMaxBlurDistance = src.dofMaxBlurDistance;

            _useColorCorrection = src.useColorCorrection;
            _colorCorrectionRedChannel = new AnimationCurve(src.colorCorrectionRedChannel.keys);
            _colorCorrectionGreenChannel = new AnimationCurve(src.colorCorrectionGreenChannel.keys);
            _colorCorrectionBlueChannel = new AnimationCurve(src.colorCorrectionBlueChannel.keys);
            _isUpdateColorCorrectionParam = src.isUpdateColorCorrectionParameter;

            _useSunShafts = src.useSunShafts;
            _sunShaftsBlurRadius = src.sunShaftsBlurRadius;
            _sunShaftsColor = src.sunShaftsColor;
            _sunShaftsPower = src.sunShaftsPower;
            _sunShaftsCenterBrightness = src.sunShaftsCenterBrightness;
            _sunShaftsCenterMultiplex = src.sunShaftsCenterMultiplex;
            _sunShaftsKomorebi = src.sunShaftsKomorebi;
            _sunShaftsBoarderClear = src.sunShaftsBoarderClear;
            _sunShaftsPosition = src.sunShaftsPosition;

            _screenOverlayParam = new ScreenOverlayParam[(int)ScreenOverlayIndex.Max];
            for (int i = 0; i < (int)ScreenOverlayIndex.Max; i++)
            {
                _screenOverlayParam[i] = new ScreenOverlayParam(src.screenOverlayParam[i]);
            }
        }
#endif

    }
}
