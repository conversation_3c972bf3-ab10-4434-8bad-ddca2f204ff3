using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{

    [System.Serializable]
    [AddComponentMenu("")]
    public abstract class CourseCharacterFacialParamBase : CourseBaseParam
    {
        public FacePartsStringSet FacePartsStringSet;

        // ------------------------------------------

        #region Serialize対象外パラメータ
        public FacePartsSet FacePartsSet { get; private set; }
#if UNITY_EDITOR && CYG_DEBUG
        public List<FaceParts> FacePartsEyeLList { get; private set; }
        public List<FaceParts> FacePartsEyeRList { get; private set; }
        public List<FaceParts> FacePartsEyebrowLList { get; private set; }
        public List<FaceParts> FacePartsEyebrowRList { get; private set; }
        public List<FaceParts> FacePartsMouthList { get; private set; }
#endif
        #endregion

        public CourseCharacterFacialParamBase(CourseCharacterFacialParamBase src)
        {
            Set(src);
        }

        public CourseCharacterFacialParamBase()
        {
            FacePartsStringSet = FacePartsStringSet.Base();
            InitializeFaceParts();
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);

            var src = param as CourseCharacterFacialParamBase;
            if (src == null)
            {
                return;
            }

            FacePartsStringSet = src.FacePartsStringSet.DeepCopy();

            // Set後に行う処理
            InitializeFaceParts();
        }

        public void InitializeFaceParts()
        {
            FacePartsSet = FacePartsSetUtil.ToFacePartsSet(FacePartsStringSet);

#if UNITY_EDITOR && CYG_DEBUG
            FacePartsEyeLList = new List<FaceParts>(FacePartsSet._eyeL);
            FacePartsEyeRList = new List<FaceParts>(FacePartsSet._eyeR);
            FacePartsEyebrowLList = new List<FaceParts>(FacePartsSet._eyebrowL);
            FacePartsEyebrowRList = new List<FaceParts>(FacePartsSet._eyebrowR);
            FacePartsMouthList = new List<FaceParts>(FacePartsSet._mouth);
#endif
        }

#if CYG_DEBUG && UNITY_EDITOR
        public void UpdateFacePartsStringSetEyeL()
        {
            FacePartsSet._eyeL = FacePartsEyeLList.ToArray();
            FacePartsStringSet._eyeL = FacePartsSetUtil.ToPartsString(FacePartsSet._eyeL, FaceGroupSet.Eye);
        }

        public void UpdateFacePartsStringSetEyeR()
        {
            FacePartsSet._eyeR = FacePartsEyeRList.ToArray();
            FacePartsStringSet._eyeR = FacePartsSetUtil.ToPartsString(FacePartsSet._eyeR, FaceGroupSet.Eye);
        }

        public void UpdateFacePartsStringSetEyebrowL()
        {
            FacePartsSet._eyebrowL = FacePartsEyebrowLList.ToArray();
            FacePartsStringSet._eyebrowL = FacePartsSetUtil.ToPartsString(FacePartsSet._eyebrowL, FaceGroupSet.Eyebrow);
        }

        public void UpdateFacePartsStringSetEyebrowR()
        {
            FacePartsSet._eyebrowR = FacePartsEyebrowRList.ToArray();
            FacePartsStringSet._eyebrowR = FacePartsSetUtil.ToPartsString(FacePartsSet._eyebrowR, FaceGroupSet.Eyebrow);
        }

        public void UpdateFacePartsStringSetMouth()
        {
            FacePartsSet._mouth = FacePartsMouthList.ToArray();
            FacePartsStringSet._mouth = FacePartsSetUtil.ToPartsString(FacePartsSet._mouth, FaceGroupSet.Mouth);
        }

        public List<FaceParts> GetFacePartsList(FaceGroupType faceGroupType)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                    return FacePartsEyeLList;
                case FaceGroupType.EyeR:
                    return FacePartsEyeRList;
                case FaceGroupType.EyebrowL:
                    return FacePartsEyebrowLList;
                case FaceGroupType.EyebrowR:
                    return FacePartsEyebrowRList;
                case FaceGroupType.Mouth:
                    return FacePartsMouthList;
                default:
                    break;
            }
            return null;
        }

        public void UpdateFacePartsStringSet(FaceGroupType faceGroupType)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                    UpdateFacePartsStringSetEyeL();
                    break;
                case FaceGroupType.EyeR:
                    UpdateFacePartsStringSetEyeR();
                    break;
                case FaceGroupType.EyebrowL:
                    UpdateFacePartsStringSetEyebrowL();
                    break;
                case FaceGroupType.EyebrowR:
                    UpdateFacePartsStringSetEyebrowR();
                    break;
                case FaceGroupType.Mouth:
                    UpdateFacePartsStringSetMouth();
                    break;
                default:
                    break;
            }
        }

        public void CopyFaceParts(FaceGroupType srcType, FaceGroupType dstType)
        {
            var srcList = GetFacePartsList(srcType);
            switch (dstType)
            {
                case FaceGroupType.EyeL:
                    FacePartsEyeLList = new List<FaceParts>(srcList);
                    UpdateFacePartsStringSetEyeL();
                    break;
                case FaceGroupType.EyeR:
                    FacePartsEyeRList = new List<FaceParts>(srcList);
                    UpdateFacePartsStringSetEyeR();
                    break;
                case FaceGroupType.EyebrowL:
                    FacePartsEyebrowLList = new List<FaceParts>(srcList);
                    UpdateFacePartsStringSetEyebrowL();
                    break;
                case FaceGroupType.EyebrowR:
                    FacePartsEyebrowRList = new List<FaceParts>(srcList);
                    UpdateFacePartsStringSetEyebrowR();
                    break;
                case FaceGroupType.Mouth:
                    FacePartsMouthList = new List<FaceParts>(srcList);
                    UpdateFacePartsStringSetMouth();
                    break;
                default:
                    break;
            }
        }
#endif
    }

}
