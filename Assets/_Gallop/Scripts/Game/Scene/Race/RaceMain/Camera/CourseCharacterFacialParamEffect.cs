using UnityEngine;
using Gallop.Model.Component;

namespace Gallop
{
    [System.Serializable]
    [AddComponentMenu("")]
    public class CourseCharacterFacialParamEffect : CourseBaseParamInterp
    {
        public const int SWEAT_NUM = GallopSweatLocator.LocatorNum;

        [System.Serializable]
        public class SweatParam
        {
            public Color Color = GameDefine.COLOR_WHITE;
            public float ColorPower = 1.0f;
            public float Strength = 1.0f;
            public float Threshold = 0.0f;
            public Vector3 Offset = Math.VECTOR3_ZERO;

            public SweatParam()
            {
                Reset();
            }

            public void Reset()
            {
                Color = GameDefine.COLOR_WHITE;
                ColorPower = 1.0f;
                Strength = 1.0f;
                Threshold = 0.0f;
                Offset = Math.VECTOR3_ZERO;
            }

            public void CopyFrom(SweatParam src)
            {
                if (src == null)
                {
                    return;
                }

                Color = src.Color;
                ColorPower = src.ColorPower;
                Strength = src.Strength;
                Threshold = src.Threshold;
                Offset = src.Offset;
            }

            public void SetInterp(SweatParam prev, SweatParam next, float t)
            {
                if ((prev == null) || (next == null))
                {
                    return;
                }

                Color = Color.Lerp(prev.Color, next.Color, t);
                ColorPower = Mathf.Lerp(prev.ColorPower, next.ColorPower, t);
                Strength = Mathf.Lerp(prev.Strength, next.Strength, t);
                Threshold = Mathf.Lerp(prev.Threshold, next.Threshold, t);
                Offset = Vector3.Lerp(prev.Offset, next.Offset, t);
            }
        }

        public bool EnableSweat;
        public bool[] IsShowSweat = new bool[SWEAT_NUM];
        public SweatParam[] SweatParamArray = null;

        public int CheekVisibleIndex;
        public InterpolationParamSimple CheekInterpolation;

        public bool TearVisible;
        public InterpolationParamSimple TearInterpolation;

        public EyeHighlightController.HighlightAnimationId EyeHighlightType = EyeHighlightController.HighlightAnimationId.NoAnim;


        public bool FaceShadowVisible;
        public Color FaceShadowColor = StaticVariableDefine.CG3D.FaceShadowController.SHADOW_DEFAULT_COLOR;
        public InterpolationParamSimple FaceShadowInterpolation;

        public CourseCharacterFacialParamEffect(CourseCharacterFacialParamEffect src)
        {
            Set(src);
        }

        public CourseCharacterFacialParamEffect()
        {
            CheekInterpolation = new InterpolationParamSimple();
            TearInterpolation = new InterpolationParamSimple();
            FaceShadowInterpolation = new InterpolationParamSimple();
            IsShowSweat = new bool[SWEAT_NUM];
            SweatParamArray = new SweatParam[SWEAT_NUM];
            for (int i = 0; i < SweatParamArray.Length; i++)
            {
                SweatParamArray[i] = new SweatParam();
            }
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCharacterFacialParamEffect(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);

            var src = param as CourseCharacterFacialParamEffect;
            if (src == null)
            {
                return;
            }

            EnableSweat = src.EnableSweat;
            System.Array.Copy(src.IsShowSweat, IsShowSweat, SWEAT_NUM);
            SweatParamArray = new SweatParam[SWEAT_NUM];
            for (int i = 0; i < SweatParamArray.Length; i++)
            {
                SweatParamArray[i] = new SweatParam();
            }
            {
                int length = Mathf.Min(SweatParamArray.Length, src.SweatParamArray.Length);
                for (int i = 0; i < length; i++)
                {
                    SweatParamArray[i].CopyFrom(src.SweatParamArray[i]);
                }
            }

            CheekVisibleIndex = src.CheekVisibleIndex;
            CheekInterpolation = new InterpolationParamSimple(src.CheekInterpolation);

            TearVisible = src.TearVisible;
            TearInterpolation = new InterpolationParamSimple(src.TearInterpolation);

            EyeHighlightType = src.EyeHighlightType;

            FaceShadowVisible = src.FaceShadowVisible;
            FaceShadowColor = src.FaceShadowColor;
            FaceShadowInterpolation = new InterpolationParamSimple(src.FaceShadowInterpolation);
        }

        public override void SetInterp(CourseBaseParamInterp prevParam, CourseBaseParamInterp nextParam, float t)
        {
            base.SetInterp(prevParam, nextParam, t);

            var prev = prevParam as CourseCharacterFacialParamEffect;
            var next = nextParam as CourseCharacterFacialParamEffect;

            // Sweat
            if ((SweatParamArray != null) && (prev.SweatParamArray != null) && (next.SweatParamArray != null))
            {
                int length = Mathf.Min(prev.SweatParamArray.Length, next.SweatParamArray.Length);
                length = Mathf.Min(length, SweatParamArray.Length);
                for (int i = 0; i < length; i++)
                {
                    if (SweatParamArray[i] != null)
                    {
                        SweatParamArray[i].SetInterp(prev.SweatParamArray[i], next.SweatParamArray[i], t);
                    }
                }
            }
        }
    }
}
