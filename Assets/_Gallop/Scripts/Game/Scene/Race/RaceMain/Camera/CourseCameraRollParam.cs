using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// カメラRoll制御
    /// </summary>
    [System.Serializable]
    public class CourseCameraRollParam : CourseBaseParamInterp
    {
        public float Roll; //カメラロール

        public CourseCameraRollParam()
        {
        }

        public CourseCameraRollParam(CourseCameraRollParam src)
        {
            Set(src);
        }

        public override CourseBaseParam Clone()
        {
            return new CourseCameraRollParam(this);
        }

        public override void Set(CourseBaseParam param)
        {
            base.Set(param);

            var src = param as CourseCameraRollParam;
            if (src == null)
            {
                return;
            }

            Roll = src.Roll;
        }

        public override void SetInterp(CourseBaseParamInterp prevParam, CourseBaseParamInterp nextParam, float t)
        {
            base.SetInterp(prevParam, nextParam, t);

            var prev = prevParam as CourseCameraRollParam;
            var next = nextParam as CourseCameraRollParam;

            Roll = Mathf.Lerp(prev.Roll, next.Roll, t);
        }
    }
}
