using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Gallop.RenderPipeline;
using UnityEngineRendering = UnityEngine.Rendering;
using RenderingUniversal = UnityEngine.Rendering.Universal;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース見た目（キャラ・競馬場・カメラ・ライトなど）制御：基底。
    /// </summary>
    //-------------------------------------------------------------------
    [AddComponentMenu("")]
    public abstract class RaceViewBase : MonoBehaviour, IRaceView
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public enum Phase
        {
            Loading,
            MemberList,
            RaceTitleStart,
            RaceTitleMovieWait,
            RaceTitleWait,
            FootSlide,
            FaceSlide,
            EventCamera_First,
            EventCamera_SecondCutin,
            EventCamera_Player,
            EventCamera_ZoomOutFast,
            EventCamera_ZoomOutSlow,
            Intro,
            WaitGateOpen,
            WaitStartComment,
            WaitStartRace,
            InitRace,
            Race,
            Finish,
            WaitEndRaceTitle,
            GateInDemo,
            TutorialShortStory,
            WaitStartGateIn,    // ゲートイン演出開始待ち(空撮等)
            WaitStartGateOpen,  // ゲートオープン演出開始待ち
        }

        /// <summary>
        /// カリングのフィルタ
        /// </summary>
        public enum CullingType
        {
            None = 0,
            Default,
        }

        public struct HorseModelDepthInfo
        {
            public int HorseIndex;
            public float DepthOrder;
        }

        protected class MarkerInfo
        {
            public RaceHorseMarkerController MarkerController;
            public GameObject MarkerObject;
            public Renderer MarkerObjRenderer;
            public Material Material;
        }

        /// <summary>
        /// キャラモデルに関してキャッシュしておきたい情報をまとめる(イベントカメラ再生時などで使用)
        /// </summary>
        protected class ModelCacheInfo
        {
            public AnimatorCullingMode AnimatorCullingModeCache = AnimatorCullingMode.CullUpdateTransforms;
        }

        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public bool IsReady => IsModelInitialized && null != _courseManager && _courseManager.IsReady;
        protected bool IsModelInitialized { get; set; }
        protected RaceMainViewController _raceMainView = null;
        protected GameObject _horseRoot;
        private GameObject _effectRoot;
        protected Phase _curPhase = Phase.Loading;
        
        protected bool _isSetDirectionalLightParam = false;

        //------------------------------------
        // キャラ。
        //------------------------------------
        protected RaceModelController[] _horseModels;
        /// <summary> キャラモデルに対するキャッシュ情報 </summary>
        protected Dictionary<int, ModelCacheInfo> _horseModelCacheInfoDict;
        protected HorseModelDepthInfo[] _horseModelRenderOrder;

        /// <summary> 各HorseIndexをキー、プレイヤーのウマいねマーカー情報をValueとしたDictionary </summary>
        protected abstract Dictionary<int, MarkerInfo> UmaineMarkerInfoDict { get; }

        protected static int PlayerHorseIndex => RaceManager.Instance.GetPlayerHorseIndex();
        private float _modelSpeedScale = 1.0f;

        // アウトラインをOFFにする距離
        public float OutlineOffLength { get; protected set; }
        [Tooltip("アウトライン最小のFOV")]
        [SerializeField]
        private float _outlineFovMin = 10.0f;

        [Tooltip("最小FOVのアウトライン距離")]
        [SerializeField]
        private float _outlineFovMinOffLength = 30.0f;

        [Tooltip("最小FOVより小さい時のアウトライン距離")]
        [SerializeField]
        private float _outlineFovLessOffLength = 150.0f;

        [Tooltip("アウトライン最大のFOV")]
        [SerializeField]
        private float _outlineFovMax = 60.0f;

        [Tooltip("最大FOVのアウトライン距離")]
        [SerializeField]
        private float _outlineFovMaxOffLength = 4f;

        [Tooltip("最大FOVより大きい時のアウトライン距離")]
        [SerializeField]
        private float _outlineFovGreaterOffLength = 2.5f;

#if CYG_DEBUG
#pragma warning disable 0414
        [SerializeField]
        protected bool _isDither = false; //現在のDither状態確認用
#pragma warning restore 0414
#endif
        private bool _lastSpurtProcessed = false;

        //------------------------------------
        // 競馬場。
        //------------------------------------
        protected CourseManager _courseManager;
        public CourseManager CourseManager => _courseManager;
        protected GameObject _raceEnvEffectObj = null;
        protected GameObject[] _courseEffect; //コース固有のエフェクト
        protected ParticleSystem[][] _courseEffectParticleArray;
        protected Animator[][] _courseEffectAnimatorArray;    //コース固有エフェクトにアニメーターが設定されている
        protected MaterialPropertyBlock _courseEffectMaterialPropertyBlock;
        protected CourseEnvParam _courseEnvParam; //コース環境
        protected CourseWeatherEffectController _courseWeatherEffectController = null;
        public CourseEnvParam CourseEnvParam => _courseEnvParam;
        public CourseWeatherEffectController CourseWeatherEffectController => _courseWeatherEffectController;

        //------------------------------------
        // カメラ。
        //------------------------------------
        protected RaceCameraManager _cameraManager;
        public RaceCameraManager RaceCameraManager => _cameraManager;
        protected GateCamera GateCamera { get { return _cameraManager.GateCamera; } }
        protected abstract float EnvCameraDepth { get; }
        protected RaceIntroCameraData cameraData;
        protected List<CourseCameraController> _courseCameraController = null;
        protected CourseCameraShadowDistanceParam _courseCameraShadowDistanceParam;

        public CourseCameraShadowDistanceParam CourseCameraShadowDistanceParam => _courseCameraShadowDistanceParam;
#if CYG_DEBUG && UNITY_EDITOR
        private Bounds _overrunDitherBounds;
#endif

        //URP:置き換え対応
        //Initialize時のBgカメラ状態(終了時に戻す)
        private bool _uiBgCameraEnable = false;

        //------------------------------------
        // ライト。
        //------------------------------------
        private GameObject _directionalLight = null;
        protected Transform _directionalLightTransform;

        //------------------------------------
        // ゴールキャプチャ用。
        //------------------------------------
        private RenderTexture _goalCaptureTexture;
        public RenderTexture GoalCaptureTexture => _goalCaptureTexture;

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------

        #region 初期化・更新など

        /// <summary>
        /// 自身にぶら下げるルート用GameObject生成。
        /// </summary>
        protected GameObject CreateRootObject(string name)
        {
            GameObject go = new GameObject(name);
            go.transform.SetParent(transform);
            return go;
        }


        //---------------------------------------------------------------
        public virtual void Init()
        {
            //リプレイした時に削除されたカメラが残るのでここでクリアする
            CameraManager.Instance.ClearCameras();

            _raceMainView = RaceManager.Instance.RaceMainView;

            //URP:置き換え対応
            //QualitySettings.antiAliasing = GetAntialiasingLevel();
            //レースは全体設定でアンチエイリアスレベルを切り替えているのでオーバーライドする
            //システム全体に関わる変更になるので、使用後は必ず戻す事
            GraphicSettings.Instance.RenderingManager.IsOverrideAntiAliasingLevel = true;
            GraphicSettings.Instance.RenderingManager.OverrideAntiAliasingLevel = (CameraData.AntiAliasLevel)GetAntialiasingLevel();

            //ディレクショナルライトを管理マネージャーから取得する。
            if (DirectionalLightManager.HasInstance())
            {
                var directionalLightManager = DirectionalLightManager.Instance;
                directionalLightManager.SetEnable(true);
                _directionalLight = directionalLightManager.DirectionalLightObject;
                _directionalLightTransform = directionalLightManager.DirectionalLightTransform;
            }

            _raceEnvEffectObj = new GameObject("RaceEnvEffect");
            _raceEnvEffectObj.transform.SetParent(gameObject.transform);

            //イメージエフェクトのカメラを付ける
            var envCamera = _raceEnvEffectObj.AddComponent<Camera>();
            envCamera.allowHDR = false;
            envCamera.allowMSAA = false;
            envCamera.allowDynamicResolution = false;
            envCamera.useOcclusionCulling = false;
            envCamera.cullingMask = GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.LayerTransparentFX);
            envCamera.clearFlags = CameraClearFlags.Nothing;
            envCamera.backgroundColor = Color.black;
            envCamera.depth = EnvCameraDepth;

            // カメラ　※競馬場のビルボードオブジェクトがカメラを参照するので、CourseManagerの初期化前に作っておく必要がある。
            _CreateCourseCamera(envCamera);

            // 競馬場
            GameObject courseObject = CreateRootObject("Course");
            _courseManager = _CreateCourseManager(courseObject);

            // キャラモデル
            _horseRoot = CreateRootObject("Horse");

            // エフェクト
            _effectRoot = CreateRootObject("Effect");
            var effManager = _effectRoot.AddComponent<RaceEffectManager>();
            effManager.Init();

            var manager = RaceManager.Instance;
            var raceInfo = RaceManager.RaceInfo;

            EnableDirectionalLight();

            //URP:置き換え対応
            //最適化、BgCameraは使用していないので無効にする
            if (UIManager.BGCamera != null)
            {
                _uiBgCameraEnable = UIManager.BGCamera.enabled;
                UIManager.BGCamera.enabled = false;
            }
        }

        //---------------------------------------------------------------
        public virtual void InitModels(HorseData[] horseData, System.Action onFinish)
        {
            StartCoroutine(_InitModelsCoroutine(horseData, onFinish));
        }

        public void ReleaseModel()
        {
            if (_horseModels == null)
            {
                return;
            }

            foreach (var model in _horseModels)
            {
                GameObject.Destroy(model.gameObject);
            }

            //不要なnullチェックをしないように0で初期化しておく
            _horseModels = new RaceModelController[0];
            _horseModelRenderOrder = new HorseModelDepthInfo[0];
        }

        /// <summary>
        /// モデル関連初期化。
        /// </summary>
        private IEnumerator _InitModelsCoroutine(HorseData[] horseData, System.Action onFinish)
        {
            IsModelInitialized = false;

            _courseEnvParam = RaceManager.Instance.RaceLoader.CourseEnvParam;

            yield return _InitCharaModels(horseData);

            _InitCamera();

            _InitCourseEffect();

            yield return _InitBillboards();

            IsModelInitialized = true;

            onFinish?.Invoke();
        }

        public virtual void Release()
        {
            if (_raceEnvEffectObj != null)
            {
                Destroy(_raceEnvEffectObj);
                _raceEnvEffectObj = null;
                if (_cameraManager != null)
                {
                    _cameraManager.ClearCameraChangeCallback();
                    _cameraManager.ClearCameraChangedParamCallback();
                    _cameraManager.SetRaceEnvCamera(null);
                }
            }

            if (_courseEffect != null)
            {
                int num = _courseEffect.Length;
                _courseEffectAnimatorArray = null;
                _courseEffectParticleArray = null;
                for (int i = 0; i < num; i++)
                {
                    if (_courseEffect[i] != null)
                    {
                        Destroy(_courseEffect[i]);
                    }
                }
                _courseEffect = null;
            }
            ReleaseGoalCaptureTexture();
            _raceMainView = null;
            _courseEnvParam = null;
            _courseWeatherEffectController?.Release();
            _courseWeatherEffectController = null;

            //URP:置き換え対応
            /*
        #if !CYG_PRODUCT
            if (MeasurementSettingSceneController.IsNoAntialiasing)
            {
                QualitySettings.antiAliasing = 1;
            }
            else
        #endif
            {
                if (GraphicSettings.HasInstance())
                {
                    //同一品質の時に設定が戻らないので毎回ここで設定する
                    QualitySettings.antiAliasing = GraphicSettings.Instance.GetAntialiasingValue();
                }
            }
            */
            //上書きしたMSAAレベルを戻す
            //URP:置き換え対応
            //instanceチェックの追加
            if (GraphicSettings.HasInstance())
            {
                GraphicSettings.Instance.RenderingManager.IsOverrideAntiAliasingLevel = false;
                GraphicSettings.Instance.RenderingManager.OverrideAntiAliasingLevel = CameraData.AntiAliasLevel.Auto;
            }
            DisableDirectionalLight();

            //URP:置き換え対応
#if CYG_DEBUG || CYG_RELEASE
            //FPS計測設定を戻す
            DebugPageFPSMeasurement.IsRaceFPSMeasurement = false;
#endif

            //URP:置き換え対応
            //無効にしていたBGCameraを戻す
            if (UIManager.BGCamera != null)
            {
                UIManager.BGCamera.enabled = _uiBgCameraEnable;
            }
        }

        /// <summary>
        /// ゴール用キャプチャーテクスチャを破棄する。
        /// </summary>
        public void ReleaseGoalCaptureTexture()
        {
            if (_goalCaptureTexture != null)
            {
                _goalCaptureTexture.Release();
                _goalCaptureTexture = null;
            }
        }

        public abstract void UpdateView(float deltaTime);

        public void LateUpdateView()
        {
            _courseWeatherEffectController?.PoolGC();
        }

        public abstract void SetPhase(Phase phase);
        public abstract Phase GetPhase();

        public abstract void SkipGateIn();
        public abstract void SkipRaceTitle();

        protected virtual void PauseCourseEffect()
        {
            if (_courseEffectAnimatorArray != null)
            {
                foreach (var animatorArray in _courseEffectAnimatorArray)
                {
                    if (animatorArray == null)
                        continue;
                    foreach (var animator in animatorArray)
                    {
                        animator.enabled = false;
                    }
                }
            }

            if (_courseEffectParticleArray != null)
            {
                foreach (var particleArray in _courseEffectParticleArray)
                {
                    if (particleArray == null)
                        continue;
                    foreach (var particle in particleArray)
                    {
                        particle.Pause();
                    }
                }
            }

            _courseWeatherEffectController?.Pause();
        }

        protected virtual void ResumeCourseEffect()
        {
            if (_courseEffectAnimatorArray != null)
            {
                foreach (var animatorArray in _courseEffectAnimatorArray)
                {
                    if (animatorArray == null)
                        continue;

                    foreach (var animator in animatorArray)
                    {
                        animator.enabled = true;
                    }
                }
            }

            if (_courseEffectParticleArray != null)
            {
                foreach (var particleArray in _courseEffectParticleArray)
                {
                    if (particleArray == null)
                        continue;
                    foreach (var particle in particleArray)
                    {
                        particle.Play();
                    }
                }
            }

            _courseWeatherEffectController?.Resume();
        }

        /// <summary>
        /// 背景エフェクトの再生速度変更
        /// </summary>
        /// <param name="speed"></param>
        public void SetCourseEffectSpeed(float speed)
        {
            if (_courseEffectAnimatorArray != null)
            {
                foreach (var animatorArray in _courseEffectAnimatorArray)
                {
                    if (animatorArray == null)
                        continue;

                    foreach (var animator in animatorArray)
                    {
                        animator.speed = speed;
                    }
                }
            }

            if (_courseEffectParticleArray != null)
            {
                foreach (var particleArray in _courseEffectParticleArray)
                {
                    if (particleArray == null)
                        continue;
                    foreach (var particle in particleArray)
                    {
                        var main = particle.main;
                        main.simulationSpeed = speed;
                    }
                }
            }
        }

        public virtual void Pause()
        {
            PauseCamera();
            PauseModelControllerAll();
            if (_courseManager != null)
                _courseManager.Pause();

            _raceMainView.PauseRaceTitle();

            PauseCourseEffect();
        }

        public virtual void Resume()
        {
            ResumeCamera();
            ResumeModelControllerAll();
            if (_courseManager != null)
                _courseManager.Resume();

            _raceMainView.ResumeRaceTitle();
            ResumeCourseEffect();
        }

        /// <summary>
        /// ポーズ処理：リザルト画面用
        /// </summary>
        public virtual void PauseForResult()
        {
            // 競馬場のアニメーションのポーズ処理
            PauseCourseEffect();
        }

        /// <summary>
        /// レジューム処理：リザルト画面用
        /// </summary>
        public virtual void ResumeForResult()
        {
            // 競馬場のアニメーションのレジューム処理
            ResumeCourseEffect();
        }
        #endregion

        #region キャラ

        /// <summary>
        /// モデルへ環境情報を反映する
        /// </summary>
        /// <param name="modelController"></param>
        /// <param name="raceLoader"></param>
        protected void ReflectEnvParamToModel(RaceModelController modelController, RaceLoaderManager raceLoader, string name, Transform parent, bool isExtraChara = false)
        {
            if (modelController == null)
            {
                return;
            }
            modelController.name = name;
            modelController.transform.SetParent(parent);

            var paramSet = _cameraManager.PostFilmParamSet;

            //この辺コンポーネントにして一括で渡したい
            modelController.TurfShadowColor = _courseEnvParam.characterTurfShadow;
            modelController.DirtShadowColor = _courseEnvParam.characterDirtShadow;
            modelController.LightProbeColor = _courseEnvParam.DirectionalLightParam.LightProbeColor;
            modelController.LightProbeShadowColor = _courseEnvParam.DirectionalLightParam.LightProbeShadowColor;
            modelController.BackgroundLightColor = _courseEnvParam.GlobalLightMapParam.LightmapColor;
            modelController.DitherTexture = raceLoader.RaceDitherTexture;  //ディザテクスチャの設定
            modelController.SoundParamData = raceLoader.RaceSoundParamData; //サウンド情報

            //PostFilmParamからキャラのRimやトゥーンのパラメータを取得する。
            if ((paramSet != null) && (paramSet.CharacterParam != null))
            {
                modelController.UpdateMaterialPropertyBlockFromCharacterParam(paramSet.CharacterParam);
            }

            // フェイシャルの設定
            RaceModelController.FacialMotionType type = RaceModelController.FacialMotionType.DrivenKeyFaceType;
            modelController.SetPlayFacialMotionType(type);
            if (!isExtraChara)
            {
                modelController.LoadFacialDrivenkey();
            }

            //足元エフェクト生成
            modelController.CreateFootSmokeEffect(raceLoader.RaceFootEffects, RaceManager.RaceInfo.GroundType);
            //足影の初期化
            modelController.ReflectEnvColor(RaceManager.RaceInfo.GroundType);
            //吐息エフェクトの生成(リッチ化でかつ冬のみ)
            modelController.CreateBreathSmokeEffect(raceLoader.RaceBreathEffectArray);
        }

        /// <summary>
        /// CharaColorTypeに応じたエフェクトの生成・初期化。
        /// </summary>
        private static void SetupCharaColorEffect(RaceModelController model, RaceDefine.CharaColorType color)
        {
            var charaColorEffectDic = RaceManager.Instance.RaceLoader.CharaColorEffectDic;

            if (!charaColorEffectDic.ContainsKey(color))
            {
                return;
            }

            var effectPrefab = charaColorEffectDic[color];
            if (effectPrefab == null)
            {
                return;
            }

            // エフェクト生成。
            var obj = Instantiate(effectPrefab, RaceEffectManager.Instance.transform);

            // EffectParamが付いている場合はEffectTransFormの生成・初期化も行う。
            var effectParam = obj.GetComponent<EffectParam>();
            if (effectParam != null)
            {
                for (int i = 0; i < effectParam._transFormParam.Length; i++)
                {
                    var transParam = effectParam._transFormParam[i];
                    var effTrans = transParam._obj.AddComponent<EffectTransForm>();
                    effTrans.Init(model, transParam);
                }
            }

            model.SetCharaColorEffect(obj);
            model.InitCharaColorEffectPauseController();
        }

        protected virtual IEnumerator _InitCharaModels(HorseData[] horseData)
        {
#if CYG_DEBUG
            if (RaceDebugger.IsLoadDisable(RaceDebugger.LoadCategory.Chara))
            {
                // キャラモデルを生成しない場合は空配列を作っておく。
                _horseModels = new RaceModelController[0];
                _horseModelRenderOrder = new HorseModelDepthInfo[0];
                _horseModelCacheInfoDict = new Dictionary<int, ModelCacheInfo>(0);

                yield break;
            }
#endif
            var raceLoader = RaceManager.Instance.RaceLoader;

            var horseRootTransform = _horseRoot.transform;

            if (RaceManager.Instance.RaceBootMode == RaceMainView.Mode.SkipToResult)
            {
                // リザルトへ直遷移する場合、モデルは読み込まれておらずRaceModelControllerも作られていない。
                // 配列でループ回す時のnullチェックをせずに済むため空配列で初期化しておく。
                _horseModels = new RaceModelController[0];
                _horseModelRenderOrder = new HorseModelDepthInfo[0];
                yield break;
            }

            _horseModels = new RaceModelController[horseData.Length];
            _horseModelRenderOrder = new HorseModelDepthInfo[horseData.Length];
            // イベントカメラ対象のキャラでしか使われないが対象となるキャラは変わるのでとりあえず全員分確保しておく
            _horseModelCacheInfoDict = new Dictionary<int, ModelCacheInfo>(horseData.Length);

            _courseEnvParam = raceLoader.CourseEnvParam;
            var marker = new List<int>(horseData.Length);
            float elapsedTime = Time.realtimeSinceStartup;

            // ウマいねマーカー表示に必要な情報
            var umaineMarker = new List<int>(horseData.Length);
            var heroesWorkData = WorkDataManager.Instance.HeroesData;
            // ウマいねマーカーが必要なのはリーグオブヒーローズのレース且つファイナルレース且つ実績からのレース再生ではない場合
            bool isNeedShowUmaineMarker = RaceManager.RaceInfo.RaceType == RaceDefine.RaceType.Heroes &&
                                          heroesWorkData.FinalCurrentRace.IsValid() &&
                                          !heroesWorkData.IsAchievementReplay();

            for (int i = 0; i < horseData.Length; i++)
            {
                GameObject charaObject = raceLoader.RaceCharaModels[i];
                if (charaObject == null)
                {
                    continue;
                }
                _horseModels[i] = raceLoader.ModelControllerArray[i] as RaceModelController;
                _horseModelCacheInfoDict[i] = new ModelCacheInfo();
                ReflectEnvParamToModel(_horseModels[i], raceLoader, horseData[i].charaName, horseRootTransform);

                // #68593 キャラカラー指定。
                if (horseData[i].CharaColorType != RaceDefine.CharaColorType.Null)
                {
                    _horseModels[i].SetCharacterColor(StaticVariableDefine.Race.RaceDefineStatic.CHARA_COLOR_ARRAY[(int)horseData[i].CharaColorType]);
                    SetupCharaColorEffect(_horseModels[i], horseData[i].CharaColorType);
                }

                charaObject.SetActive(false);

                if (!IsNeedMarker())
                {
                    //マーカーが不要なら戻す
                    continue;
                }

                //以下、マーカー作成処理
                if (RaceManager.RaceInfo.RaceType == RaceDefine.RaceType.Practice)
                {
                    // 練習の場合はメイン指定のキャラのみマーカーを付ける
                    if (TempData.Instance.PracticeRaceData.RaceMainHorseIndexList.Contains(i))
                    {
                        marker.Add(i);
                    }
                }
                else if (horseData[i].IsUser)
                {
                    marker.Add(i);
                }

                // ウマいねマーカー
                if (isNeedShowUmaineMarker)
                {
                    if (RaceUtil.IsUmaine(RaceManager.RaceInfo.RaceType, horseData[i]))
                    {
                        umaineMarker.Add(i);
                    }
                }

                //3FPS経過していなければ、待たない
                var diffTime = Time.realtimeSinceStartup - elapsedTime;
                if (diffTime >= (GameDefine.MAX_FPS_TIME * 3.0f))
                {
                    // 負荷が1フレームに集中しないようにする。
                    yield return null;
                    elapsedTime = Time.realtimeSinceStartup;
                }
            }

            // #56413
            // モブキャプチャ時の髪暴れ対策。モデル初期化後、↓でCySpringのResetが行われる前にキャラをスタートゲートに配置しておく。
            // ここで行わないとCySpringのReset後にキャラが原点からスタートゲートへ瞬間移動するため、そのタイミングで髪が暴れることがある。
            SetInitialPosition();

            InitializeMarker(marker, horseData);

            InitializeUmaineMarker(umaineMarker, horseData);

            CreateMarkerControllerTable();

            // 上層の処理だと非表示するタイミングが遅くてローディング画面に
            // 映りこんでしまっていたので、早めにマーカーを非表示にする
            VisibleMarkers(false);

            // ボーンのバインドポーズ初期化のためOnOff
            for (int i = 0; i < _horseModels.Length; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                _horseModels[i].OwnerObject.SetActive(true);

                // 髪物理設定
                _horseModels[i].ApplyCySpring();
                _horseModels[i].PauseCySpring();

                // #56413 モブキャプチャ時の髪暴れ対策。
                _horseModels[i].ResetCyspring();
                _horseModels[i].ReserveWarmingUpCySpring();
            }
        }

        //---------------------------------------------------------------
        protected abstract bool IsNeedMarker();

        //---------------------------------------------------------------
        protected abstract void InitializeMarker(List<int> markerList, HorseData[] horseData);
        //---------------------------------------------------------------
        protected abstract void InitializeUmaineMarker(List<int> markerList, HorseData[] horseData);
        //---------------------------------------------------------------
        protected abstract void CreateMarkerControllerTable();

        public abstract void UpdateHorseModel(float deltaTime);

        public virtual void UpdateHorseModelMotion()
        {
#if CYG_DEBUG
            if (RaceDebugger.IsMemoryCheckAutoPlay && !IsModelInitialized)
            {
                return;
            }
#endif

            float time = RaceManager.Instance.CalculateElapsedTime;
            //モーションを更新する
            foreach (var model in _horseModels)
            {
                if (model == null) continue; //歯抜けなキャラは除外
                model.UpdateMotion(time);
            }
        }

        /// <summary>
        /// アウトライン距離を更新
        /// </summary>
        protected void UpdateOutlineLength()
        {
            if (RaceCameraManager.HasInstance() == false)
                return;
            var camera = RaceCameraManager.Instance.CurrentCamera;
            if (camera == null)
                return;

            if (camera.fieldOfView < _outlineFovMin)
            {
                OutlineOffLength = _outlineFovLessOffLength;
            }
            else if (camera.fieldOfView > _outlineFovMax)
            {
                OutlineOffLength = _outlineFovGreaterOffLength;
            }
            else
            {
                float range = _outlineFovMax - _outlineFovMin;
                if (range > 0.0f)
                {
                    OutlineOffLength = Mathf.Lerp(_outlineFovMinOffLength, _outlineFovMaxOffLength, camera.fieldOfView / range);
                }
                else
                {
                    OutlineOffLength = _outlineFovMinOffLength;
                }
            }
        }

        /// <summary>
        /// 全キャラクターのMaterialPropertyBlockを更新する。
        /// </summary>
        protected virtual void UpdateCharacterMaterialPropertyBlock()
        {
            UpdateCharacterMaterialPropertyBlockForModels(_horseModels);
        }

        protected void UpdateCharacterMaterialPropertyBlockForModels(RaceModelController[] modelArray)
        {
            var paramSet = _cameraManager.PostFilmParamSet;
            if ((paramSet != null) && (paramSet.CharacterParam != null))
            {
                for (int i = 0; i < modelArray.Length; i++)
                {
                    var model = modelArray[i];
                    if (model == null)
                    {
                        continue;
                    }
                    if (model.IsEnabledPostFilmSetRim)
                    {
                        model.UpdateMaterialPropertyBlockFromCharacterParam(paramSet.CharacterParam);
                    }
                }
            }
        }

        public void UpdateRenderSettings()
        {
            UpdateRenderSettings(_cameraManager.CurrentCamera);
        }
        public virtual void UpdateRenderSettings(Camera camera)
        {
            if (!IsModelInitialized)
                return;

            int num = _horseModels.Length;
            for (int i = 0; i < num; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                _horseModelRenderOrder[i].HorseIndex = i;
                _horseModelRenderOrder[i].DepthOrder = _horseModels[i].GetDepthOrder(camera);
            }
            System.Array.Sort(_horseModelRenderOrder, (a, b) => (int)((a.DepthOrder - b.DepthOrder) * 10000));

            UpdateShaderSetting();
        }

        /// <summary>
        /// ShaderとRenderQueueの更新
        /// </summary>
        private void UpdateShaderSetting()
        {
            //カメラからの距離によってShaderを入れ替える
            switch (_curPhase)
            {
                case Phase.Race:
                case Phase.WaitGateOpen:
                    SetRaceShaderSettings();
                    break;

                default:
                    {
                        int num = _horseModels.Length;

                        //通常のシェーダで一括設定して終了
                        for (int i = 0; i < num; i++)
                        {
                            if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                            
                            var model = _horseModels[_horseModelRenderOrder[i].HorseIndex];
                            //明示的にアウトラインを描画しない場合はNolineToon系のシェーダーにする
                            if (model.OutlineWidth < Math.EPSILON)
                            {
                                model.LodType = LODType.Low;
                            }
                            else if (model.IsBodyWillRender)
                            {
                                model.LodType = LODType.High;
                            }
                            model.UpdateGraphicSettings(i);
                        }
                    }
                    break;
            }
        }

        public virtual void SetModelSpeedScale(float scale)
        {
            _modelSpeedScale = scale;

            SetModelSpeedScaleForModels(_horseModels, scale);

            //エフェクトの再生速度
            var manager = RaceManager.Instance;
            int num = RaceManager.RaceInfo.NumRaceHorses;
            for (int i = 0; i < num; i++)
            {
                var info = manager.GetHorseInfo(i);
                info.SkillManager.SetEffectSpeed(scale);
            }

            //エフェクトのタイムスケール変更
            GraphicSettings.SetEffectTimeScale(scale);

            //コースエフェクトのタイムスケール変更
            SetCourseEffectSpeed(scale);
        }

        public void SetModelSpeedScaleForModels(RaceModelController[] modelArray, float scale)
        {
            float windTimeScale = Mathf.Pow(scale, 0.5f);
            for (int i = 0; i < modelArray.Length; i++)
            {
                var model = modelArray[i];
                if (model == null)
                {
                    continue;
                }

                //アニメーション再生速度
                model.SetAnimationSpeedScale(scale);

                // 揺れ物の速度（風なびきの周期のみスケールさせる）
                model.SetAdditionalWindTimeScale(windTimeScale);

                // 足煙エフェクトの再生速度
                model.SetFootSmokeSpeed(scale);

                // キャラカラーエフェクトの再生速度。
                model.SetCharaColorEffectSpeed(scale);

                // 吐息エフェクトの再生速度
                model.SetBreathSmokeSpeed(scale);
            }
        }

        public virtual void PauseModelControllerAll()
        {
            PauseModelControllerArray(_horseModels);
        }

        protected void PauseModelControllerArray(RaceModelController[] modelArray)
        {
            for (int i = 0; i < modelArray.Length; ++i)
            {
                var model = modelArray[i];
                if (model == null)
                {
                    continue;
                }
                // ポーズの時は、Pause→SetMotionSpeedScale(0.0f)で処理を行う必要がある。
                // 先にSetMotionSpeedScale(0.0f)を行うと、_animator.speedが0になり、Pauseでその時の再生速度(_animator.speed)がキャッシュされる。
                // その後Resume時にキャッシュしたスピードを復元するときに0で復帰した結果、モーションが停止してしまうため。
                model.Pause();
                model.SetAnimationSpeedScale(0.0f);
            }
        }

        public virtual void ResumeModelControllerAll()
        {
            ResumeModelControllerArray(_horseModels);
        }

        protected void ResumeModelControllerArray(RaceModelController[] modelArray)
        {
            for (int i = 0; i < modelArray.Length; ++i)
            {
                var model = modelArray[i];
                if (model == null)
                {
                    continue;
                }
                model.Resume();
                model.SetAnimationSpeedScale(_modelSpeedScale);
            }
        }

        public virtual RaceModelController GetModelController(int horseIndex)
        {
            if (null == _horseModels)
            {
                return null;
            }
            if (horseIndex < 0 || horseIndex >= _horseModels.Length)
            {
                DebugUtils.Assert(false, "horseIndexが不正です。horseIndex=" + horseIndex);
                return null;
            }
            if (null == _horseModels[horseIndex])
            {
                //ストーリーレースは生成スキップが許可される場合がある(メモリ軽減)
                if (!RaceManager.RaceInfo.IsStoryRace)
                {
                    DebugUtils.Assert(false, "_horseModels[horseIndex]がnullです。horseIndex=" + horseIndex);
                }
                return null;
            }

            return _horseModels[horseIndex];
        }

        public Transform GetModelTransform(int horseIndex)
        {
            var model = GetModelController(horseIndex);
            if (model == null)
            {
                return null;
            }

            return model.CacheTransform;
        }

        public GameObject GetModelObject(int horseIndex)
        {
            if (horseIndex < 0 || horseIndex >= _horseModels.Length)
            {
                return null;
            }
            return _horseModels[horseIndex].OwnerObject;
        }

        public virtual void SetVisibleHorseAll(bool isVisible, bool isForce = false, int ignoreIndex = -1)
        {
            SetVisibleHorseForModels(_horseModels, isVisible, isForce, ignoreIndex);
        }

        protected void SetVisibleHorseForModels(RaceModelController[] modelArray, bool isVisible, bool isForce, int ignoreIndex = -1)
        {
            if (!IsModelInitialized)
            {
                return;
            }

            for (int i = 0; i < modelArray.Length; i++)
            {
                var model = modelArray[i];
                if (model == null)
                {
                    continue;
                }

                if (i == ignoreIndex)
                {
                    continue;
                }

                model.SetVisible(isVisible, isForce);
            }
        }

        public virtual void SetInitialPosition()
        {
            for (int i = 0; i < _horseModels.Length; i++)
            {
                var info = RaceManager.Instance.GetHorseInfo(i);
                if (_horseModels[i] != null)
                {
                    _horseModels[i].UpdateTransform(info);
                }
            }
        }

        public void StartRace()
        {
            for (int i = 0; i < _horseModels.Length; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                _horseModels[i].IsUpdateInvisible = false;
                _horseModels[i].SetJawOutlineVisible(false);
            }
        }

        protected static Bounds GetDitherBounds(Vector3 targetPosition, Vector3 cameraPosition)
        {
            return RaceCameraManager.GetDitherBounds(targetPosition, cameraPosition);
        }

        private Bounds GetOverrunDitherBounds(Vector3 cameraPosition)
        {
            return RaceCameraManager.GetOverrunDitherBounds(cameraPosition);
        }

        /// <summary>
        /// レース中のシェーダー設定
        /// </summary>
        private void SetRaceShaderSettings()
        {
            if (_cameraManager == null)
            {
                return;
            }

            //カメラからの距離によってShaderを入れ替える
            var cameraManager = _cameraManager;
            // 127896 スキルカット中はレースキャラのシェーダやティザ判定を行わない。
            // スキルカット中も本処理が行われており、カット背景用のSS撮影後に全レースキャラが非表示になるがここでキャラが表示状態になることがあり
            // その場合にスキルカット中に映り込むことがあったのでカット中は処理を避ける。
            if (_cameraManager.IsEnableCutIn) return;
            int num = _horseModels.Length;
            float outlineOffLength = OutlineOffLength * OutlineOffLength;

            //URP:置き換え対応
            var cameraPosition = cameraManager.CurrentCameraTransform.position;
            //var cameraPosition = camera.transform.position;

            // #53178 オーバーラン中はカメラにめり込みそうなキャラをディザシェーダーに切り替える。
            if (RaceCameraManager.Instance.IsOverrunCamera())
            {
                var isDither = cameraManager.IsDither;
                var cameraBounds = GetOverrunDitherBounds(cameraPosition);
#if CYG_DEBUG && UNITY_EDITOR
                _overrunDitherBounds = cameraBounds;
#endif
#if CYG_DEBUG
                _isDither = isDither;
#endif

                for (int i = 0; i < num; i++)
                {
                    if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                    
                    int orderIndex = num - (i + 1);
                    int horseIndex = _horseModelRenderOrder[orderIndex].HorseIndex;
                    var model = _horseModels[horseIndex];
                    //体が写っていなければ判定は不要
                    if (!model.IsBodyWillRender)
                    {
                        if (model.LodType == LODType.Hide)
                        {
                            //マルチカメラでHide状態なり映らないまま、再度Eventカメラ判定に入ると
                            //Hide(表示されている)->Hideとなる非表示にならないので、ここで何かの状態にはする
                            UpdateLODTypeOnCourseCamera(model, cameraPosition, outlineOffLength);
                        }
                        model.UpdateGraphicSettings(orderIndex);
                        continue;
                    }

                    if (isDither)
                    {
                        var modelPos = model.GetPosition();
                        var modelForward = model.CacheTransform.forward;

                        if (RaceCameraManager.IsInDitherBounds(modelPos, modelForward, ref cameraBounds))
                        {
                            model.LodType = LODType.DitherHigh;
                        }
                        else
                        {
                            model.LodType = LODType.High;
                        }
                    }
                    
                    // ダイナミックカメラの自キャラのみアウトラインを変更するので、そのチェック
                    CheckAndSetRaceShaderSettingsForDynamicCameraOverRun(horseIndex);
                    
                    model.UpdateGraphicSettings(orderIndex);
                }
            }
            else
            {
                var cameraMode = cameraManager.GetCameraMode();
                switch (cameraMode)
                {
                    case RaceCameraManager.CameraMode.Event:
                        {
                            SetRaceShaderSettingsForEventCamera();
                        }
                        break;
                    case RaceCameraManager.CameraMode.Dynamic:
                        {
                            SetRaceShaderSettingsForDynamicCamera();
                        }
                        break;

                    default:
                        {
                            SetRaceShaderSettingsForDefault();
                        }
                        break;
                }
            }
        }
        
        /// <summary>
        /// デフォルトのレース中のシェーダー設定
        /// </summary>
        protected void SetRaceShaderSettingsForDefault()
        {
            int num = _horseModels.Length;
            float outlineOffLength = OutlineOffLength * OutlineOffLength;
            var cameraManager = _cameraManager;
            var cameraPosition = cameraManager.CurrentCameraTransform.position;
            
            for (int i = 0; i < num; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                int orderIndex = num - (i + 1);
                var horseIndex = _horseModelRenderOrder[orderIndex].HorseIndex;
                var model = _horseModels[horseIndex];
                
                //体写っていなければ判定は不要
                if (!model.IsBodyWillRender)
                {
                    if (model.LodType == LODType.Hide)
                    {
                        //マルチカメラでHide状態なり映らないまま、再度Eventカメラ判定に入ると
                        //Hide(表示されている)->Hideとなる非表示にならないので、ここで何かの状態にはする
                        UpdateLODTypeOnCourseCamera(model, cameraPosition, outlineOffLength);
                    }
                    model.UpdateGraphicSettings(orderIndex);
                    continue;
                }

                UpdateLODTypeOnCourseCamera(model, cameraPosition, outlineOffLength);

                model.UpdateGraphicSettings(orderIndex);
            }
        }

        /// <summary>
        /// イベントカメラ使用時のレース中のシェーダー設定
        /// </summary>
        protected virtual void SetRaceShaderSettingsForEventCamera()
        {
            // RaceViewReplayのみで使用したいのでここでは何も処理しない
        }
        
        /// <summary>
        /// ダイナミックカメラ使用時のレース中のシェーダー設定
        /// </summary>
        protected virtual void SetRaceShaderSettingsForDynamicCamera()
        {
            // RaceViewReplayのみで使用したいのでここでは何も処理しない
        }
        
        /// <summary>
        /// ダイナミックカメラ使用時オーバーランのレース中のシェーダー設定
        /// </summary>
        protected virtual void CheckAndSetRaceShaderSettingsForDynamicCameraOverRun(int horseIndex)
        {
            // RaceViewReplayのみで使用したいのでここでは何も処理しない
        }
        
        /// <summary>
        /// ダイナミックカメラ開始時処理
        /// </summary>
        public virtual void OnStartDynamicCamera()
        {
            // RaceViewReplayのみで使用したいのでここでは何も処理しない
        }
        
        /// <summary>
        /// ダイナミックカメラ終了時処理
        /// </summary>
        public virtual void OnEndDynamicCamera()
        {
            // RaceViewReplayのみで使用したいのでここでは何も処理しない
        }

        protected abstract void UpdateLODTypeOnCourseCamera(RaceModelController model, Vector3 cameraPos, float outlineOffLength);

        public void Culling(Camera curCamera, int targetHorseIndex, RaceViewBase.CullingType culling = RaceViewBase.CullingType.Default, float cullingSqrMagnitude = 0)
        {
            if (culling == CullingType.None) return;

            // カメラから一定距離内にいるキャラを非表示にする
            var numHorse = _horseModels.Length;
            Vector3 v0 = curCamera.transform.position;
            for (int i = 0; i < numHorse; i++)
            {
                if (i == targetHorseIndex) continue;    //見ているキャラは対象外
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                var model = _horseModels[i];
                Vector3 v1 = model.GetBounds().center;
                float sqrDistance = (v0 - v1).sqrMagnitude;

                if (sqrDistance < cullingSqrMagnitude)
                {
                    model.SetVisible(false);
                }
            }
        }

        /// <summary>
        /// 出走キャラ以外のキャラモデル取得。
        /// </summary>
        public abstract RaceModelController GetOtherModelController(int otherIndex);

        //---------------------------------------------------------------
        public void SetVisibleHorseAndStartGate(bool isVisible)
        {
            SetVisibleHorseAll(isVisible);

            var courseMng = CourseManager;
            if (null == courseMng)
            {
                return;
            }
            courseMng.SetStartGateVisible(isVisible);
        }

        public virtual void VisibleMarkers(bool visible) { }
        public virtual void ReleaseMarker() { }

        public void StartSetupCySpring(RaceModelController modelController)
        {
            modelController.SetForceDisableHipMoveParam(true);
            modelController.SetEnableCySpringWind(true);
            modelController.SetEnableTailCySpring(false);
            modelController.SetSkirtExpressionSpesialBone(false);
            modelController.UseCySpringAutoEnable = true;
            modelController.Resume();
            modelController.ResetCyspring();
        }

        /// <summary>
        /// 走った時用のCySpring設定を行う
        /// </summary>
        public void RaceStartSetupCySpring()
        {
            for (int i = 0; i < _horseModels.Length; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                var model = _horseModels[i];
                StartSetupCySpring(model);
            }
        }

        /// <summary>
        /// 揺れものをポーズ
        /// </summary>
        public void PauseCySpring()
        {
            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.PauseCySpring();
                }
            }
        }
        #endregion キャラ

        #region 競馬場
        /// <summary>
        /// ビルボード初期化。
        /// </summary>
        private IEnumerator _InitBillboards()
        {
            //CameraManagerに登録されたカメラにビルボード登録を行う
            var cameras = CameraManager.Instance.GetCameras();
            var billboardObjectList = _courseManager.GetBillboardObject().ToList();
            // ウマいねマーカーもビルボードで表示したいので追加する
            // 数が不定なので一旦Listにして追加後配列に戻してる
            if (UmaineMarkerInfoDict != null)
            {
                foreach (var markerInfo in UmaineMarkerInfoDict.Values)
                {
                    if (markerInfo.MarkerObject.TryGetComponent<CourseBillboardBounds>(out var billboard))
                    {
                        billboardObjectList.Add(billboard);
                    }
                }
            }

            var billboardObjects = billboardObjectList.ToArray();

            var cameraControllerContext = CreateContext();

            _courseCameraController = new List<CourseCameraController>();
            var count = cameras.Count;
            for (int i = 0; i < count; i++)
            {
                var cameraController = cameras[i];
                var camera = cameraController.GetCamera();
                if (camera == null)
                {
                    Debug.LogError($"Camera is Null! i:{i}");
                    continue;
                }
                var billboard = camera.gameObject.GetComponent<CourseHierarchyBillboardController>();
                if (billboard == null)
                {
                    billboard = camera.gameObject.AddComponent<CourseHierarchyBillboardController>();
                }
                billboard.Initialize(billboardObjects);

                var controller = camera.gameObject.AddComponent<CourseCameraController>();
                controller.Initialize(ref cameraControllerContext);
                _courseCameraController.Add(controller);
            }

            if (RaceCameraManager.HasInstance() && CameraManager.HasInstance())
            {
                // 129828 キャラ周りは映らないので影補正やShadowDistanceを制御するCourseCameraControllerは不要
                // 元々は「CameraManager.Instance.GetCameras」の対象で↑でまとめて制御していたが、
                // バグ対応でCourseCameraControllerを除去する必要があり、処理を分離しここで他パラメータのセットアップをするように対応。
                var raceCameraManager = RaceCameraManager.Instance;
                var raceTitleCamera = raceCameraManager.RaceTitleCamera.Camera;

                if (raceTitleCamera != null)
                {   // 129828 の対応によりレースタイトル向けの処理が追加されたが、
                    // ストーリーレースではTitleCameraはnullなのでチェックが必要
                    raceCameraManager.SetupRaceCameraParam(raceTitleCamera);
                    CameraManager.Instance.Attach(raceTitleCamera);
                
                    var billboard = raceTitleCamera.gameObject.GetComponent<CourseHierarchyBillboardController>();
                    if (billboard == null)
                    {
                        billboard = raceTitleCamera.gameObject.AddComponent<CourseHierarchyBillboardController>();
                    }
                    billboard.Initialize(billboardObjects);
                }
            }

            {
                // 129828 キャラ周りは映らないので影補正やShadowDistanceを制御するCourseCameraControllerは不要
                //コースカメラはCameraManagerの管理外なので、別途登録する
                var courseCamera = RaceCameraManager.CourseCamera.CameraComponent.gameObject;  //コースカメラとカメラコンポーネントが付いているオブジェクトは違う
                var billboard = courseCamera.AddComponent<CourseHierarchyBillboardController>();
                billboard.Initialize(billboardObjects);
            }

            {
                //ゲートカメラにビルボードを追加する
                var controller = GateCamera.gameObject.AddComponent<CourseCameraController>();
                controller.Initialize(ref cameraControllerContext);
                _courseCameraController.Add(controller);

                var billboard = GateCamera.gameObject.AddComponent<CourseHierarchyBillboardController>();
                billboard.Initialize(billboardObjects);
            }

            // ゴールカメラにビルボード追加
            if (RaceCameraManager.GoalCamera != null)
            {
                var billboard = RaceCameraManager.GoalCamera.gameObject.AddComponent<CourseHierarchyBillboardController>();
                billboard.Initialize(billboardObjects);
            }

            //StoryRace専用処理
            var raceInfo = RaceManager.RaceInfo;
            if (raceInfo != null && raceInfo.IsStoryRace)
            {
                //モニターカメラを登録する
                var monitorCamera = RaceCameraManager.Instance.MonitorCamera;
                if (monitorCamera != null)
                {
                    var billboard = monitorCamera.gameObject.AddComponent<CourseHierarchyBillboardController>();
                    billboard.Initialize(billboardObjects);
                }
            }

            // 画面分割マルチカメラ対応
            var firstCameraObject = RaceCameraManager.MainMultiCamera?.FirstCameraObejct;
            if (firstCameraObject != null)
            {
                var billboard = firstCameraObject.AddComponent<CourseHierarchyBillboardController>();
                billboard.Initialize(billboardObjects);
                var controller = firstCameraObject.AddComponent<CourseCameraController>();
                cameraControllerContext.IsMultiCamera = true;
                controller.Initialize(ref cameraControllerContext);
                _courseCameraController.Add(controller);
            }

            yield break;
        }

        /// <summary>
        /// Context作成メソッド
        /// </summary>
        /// <returns></returns>
        private CourseCameraController.Context CreateContext()
        {
            var isLandscape = false;
            if (SaveDataManager.HasInstance())
            {
                isLandscape = RaceUtil.GetRaceLandScapeSettingData();
            }

            var directionalLightRotation = _directionalLightTransform.localRotation;

            var cameraControllerContext = new CourseCameraController.Context
            {
                Directional = _directionalLight,
                LightDir = directionalLightRotation,
                CameraType = isLandscape ? CourseCameraController.CameraType.Landscape : CourseCameraController.CameraType.Portrait,
                DistanceParam = _courseCameraShadowDistanceParam,
                IsShadowOffsetAutoCorrect = true,
                IsMultiCamera = false,
            };

            return cameraControllerContext;
        }

        /// <summary>
        /// 競馬場エフェクト初期化。
        /// </summary>
        protected abstract void _InitCourseEffect();

#if CYG_DEBUG
        public abstract void DbgInitCourseEffect();
        public abstract IEnumerator DbgInitCharaModel();
#endif

        /// <summary>
        /// 競馬場固有のエフェクト再生
        /// </summary>
        protected void InitCourseEffectRace()
        {
#if CYG_DEBUG
            if (RaceDebugger.IsLoadDisable(RaceDebugger.LoadCategory.Course))
            {
                _courseEffect = new GameObject[(int)RaceLoaderEffect.CourseEffectType.Max];
                _courseEffectAnimatorArray = new Animator[(int)RaceLoaderEffect.CourseEffectType.Max][];
                _courseEffectParticleArray = new ParticleSystem[(int)RaceLoaderEffect.CourseEffectType.Max][];
                _courseWeatherEffectController = new CourseWeatherEffectController();
                return;
            }
#endif
            var raceManager = RaceManager.Instance;
            var courseEffect = raceManager.RaceLoader.CourseEffectPrefab;
            if (courseEffect != null)
            {
                // こっちは雨・雪などのエフェクト実体 (CourseEffect)
                _courseEffect = new GameObject[(int)RaceLoaderEffect.CourseEffectType.Max];
                _courseEffectAnimatorArray = new Animator[(int)RaceLoaderEffect.CourseEffectType.Max][];
                _courseEffectParticleArray = new ParticleSystem[(int)RaceLoaderEffect.CourseEffectType.Max][];

                _courseWeatherEffectController = new CourseWeatherEffectController();
                var context = new CourseWeatherEffectController.Context()
                {
                    View = this,
                    CourseEffect = _courseEffect,
                    RandomSeed = RaceManager.RaceInfo?.RandomSeed ?? 0,
                    StartGateTransform = raceManager.CourseManager.GetStartGateTransform(),

                    CourseEffectPrefab = raceManager.RaceLoader.CourseEffectPrefab,
                    GateinWeatherEffectPrefab = raceManager.RaceLoader.GateinWeatherEffectPrefab,
                    CourseWeatherData = raceManager.RaceLoader.CourseWeatherData,
                };
                _courseWeatherEffectController.Init(ref context);

                //こっちはEFFECTレイヤーで表示される (CourseAttachEffect)
                {
                    var courseEnvEffectRenderer = new List<Renderer>(64);

                    int[] envIndexArray = new int[]
                    {
                        (int) RaceLoaderEffect.CourseEffectType.EnvWorldSpace,
                        (int) RaceLoaderEffect.CourseEffectType.EnvWorldSpaceOnlyRace,
                    };
                    foreach (var envIndex in envIndexArray)
                    {
                        if (courseEffect[envIndex] != null)
                        {
                            // エフェクトのインスタンス生成。
                            _courseEffect[envIndex] = Instantiate<GameObject>(courseEffect[envIndex]);
                            _courseEffectAnimatorArray[envIndex] = _courseEffect[envIndex].GetComponentsInChildren<Animator>();
                            _courseEffectParticleArray[envIndex] = _courseEffect[envIndex].GetComponentsInChildren<ParticleSystem>();
                            if (_courseManager != null)
                            {
                                _courseEffect[envIndex].transform.SetParent(_courseManager.transform, true);
                            }

                            // エフェクトを描画するRenderer退避。
                            courseEnvEffectRenderer.AddRange(_courseEffect[envIndex].GetComponentsInChildren<Renderer>());
                        }
                    }

                    //Probeの色を適用する
                    _courseEffectMaterialPropertyBlock = new MaterialPropertyBlock();
                    if (_courseEnvParam != null)
                    {
                        _courseEffectMaterialPropertyBlock.SetColor(
                            ShaderManager.GetPropertyId(ShaderManager.PropertyId._LightProbeColor),
                            _courseEnvParam.DirectionalLightParam.LightProbeColor);
                        for (int i = 0; i < courseEnvEffectRenderer.Count; i++)
                        {
                            courseEnvEffectRenderer[i].SetPropertyBlock(_courseEffectMaterialPropertyBlock);
                        }
                    }
                }
            }
        }

        protected CourseManager _CreateCourseManager(GameObject attachTarget)
        {
            return attachTarget.AddComponent<CourseManagerReplay>();
        }

        /// <summary>
        /// 環境エフェクトを全て非アクティブ状態にする
        /// </summary>
        public void DeactivateEnvEffect()
        {
            if (_raceEnvEffectObj != null)
            {
                _raceEnvEffectObj.SetActive(false);
            }
        }

        /// <summary>
        /// 競馬場エフェクトのON/OFF。
        /// </summary>
        public void SetActiveCourseEffect(bool isActive, RaceLoaderEffect.CourseEffectType effectType)
        {
            if (_courseEffect == null)
            {
                return;
            }
            if (_courseEffect[(int)effectType] == null)
            {
                return;
            }
            _courseEffect[(int)effectType].SetActive(isActive);
        }

        /// <summary>
        /// レース以外の画面などに飛んだ後、レースに戻ってきた際に行う処理
        /// 変更された環境設定などを戻す
        /// </summary>
        public void ResetEnvSettings() 
        {
            if (_courseEnvParam != null)
            {
                //Lightmapの設定を戻す
                _courseEnvParam.GlobalLightMapParam.UpdateGraphicSetting();
            }

            // リセットしたDirectionalLightを再設定
            EnableDirectionalLight();
        }

        #endregion

        #region カメラ
        /// <summary>
        /// レースカメラ生成/初期化。
        /// </summary>
        private void _CreateCourseCamera(Camera envCamera)
        {
            GameObject cameraObject = Instantiate<GameObject>(RaceManager.Instance.RaceLoader.CameraTablesPrefab);
            cameraObject.transform.SetParent(transform);
            _cameraManager = cameraObject.GetComponent<RaceCameraManager>();
            _cameraManager.ClearCameraChangeCallback();
            _cameraManager.SetCameraChangeCallback(OnChangeCamera);
            _cameraManager.ClearCameraChangedParamCallback();
            _cameraManager.AddCameraChangedParamCallback(OnChangeCameraParam);

            cameraData = RaceManager.Instance.RaceLoader.RaceIntroCameraData;

            // ポストエフェクト用設定
            var manager = RaceManager.Instance;
            var raceInfo = RaceManager.RaceInfo;
            _cameraManager.Initialize(manager.RaceLoader.CoursePostFilmGroup, raceInfo, envCamera, ref _goalCaptureTexture);
        }

        /// <summary>
        /// カメラ初期化。
        /// </summary>
        private void _InitCamera()
        {
            if (_raceMainView.BootMode == RaceMainView.Mode.Highlight ||
                _raceMainView.BootMode == RaceMainView.Mode.SkipToResult ||
                _raceMainView.BootMode == RaceMainView.Mode.OnlyGoalCapture ||
                _raceMainView.BootMode == RaceMainView.Mode.Commercial
               )
            {
                return;
            }

            InitGateCamera();
            InitCourseCamera();
            InitRaceTitleCamera();
        }

        /// <summary>
        /// ゲートイン演出カメラ初期化。
        /// </summary>
        protected abstract void InitGateCamera();

        /// <summary>
        /// 出走表カメラ初期化。
        /// </summary>
        protected abstract void InitCourseCamera();

        /// <summary>
        /// レースタイトルカメラ初期化。
        /// </summary>
        protected abstract void InitRaceTitleCamera();

        private void OnChangeCamera(Camera changedCamera)
        {
            // レースリザルトカットイン以降は回さない
            if (RaceManager.Instance.IsAfterRaceEndCutIn)
            {
                return;
            }
            if (changedCamera == null)
            {
                return;
            }

            var cameraMode = RaceCameraManager.Instance.GetCameraMode();
            for (int i = 0; i < _horseModels.Length; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                var horseModel = _horseModels[i];
                horseModel.UpdateCameraDistanceBlendRate(
                    changedCamera,
                    cameraMode,
                    RaceManager.Instance.GetHorseInfo(i).IsFinished());
            }
        }

        protected abstract void OnChangeCameraParam(Camera changedCamera, CourseCameraParam param);

        public void SetEnableCamera(bool enable)
        {
            _cameraManager.gameObject.SetActive(enable);
            if (enable)
            {
                //黒フェードを解除するために、ここでClearを無効にする
                _cameraManager.RaceEnvCamera.clearFlags = CameraClearFlags.Nothing;
            }
        }

        public void SetRaceCourseCameraViewport(Rect rect)
        {
            _cameraManager.MainCamera.rect = rect;
        }
        public Rect GetRaceCourseCameraViewport()
        {
            return _cameraManager.MainCamera.rect;
        }

        protected void UpdateByDistance()
        {
            if (_lastSpurtProcessed == false)
            {
                float leftDistance = RaceManager.RaceInfo.CourseDistance - RaceManager.Instance.GetFirstHorseInfo().GetDistance();
                if (leftDistance <= RaceManager.Instance.ParamDefine.EventCameraUnPlayableDistanceFromGoal)
                {
                    RaceCameraManager.Instance.SetEventCameraUnPlayable();
                    _lastSpurtProcessed = true;
                }
            }
        }

        /// <summary>
        /// ウイニングサークル時の立ち位置
        /// </summary>
        /// <returns></returns>
        public Transform GetResultStandTransform()
        {
            return _courseManager.GetResultStandTransform();
        }

        /// <summary>
        /// リザルトで表示する表彰台の位置
        /// </summary>
        /// <returns></returns>
        public Transform GetResultPanelTransform()
        {
            return _courseManager.GetResultPanelTransform();
        }

        private void PauseCamera()
        {
            _cameraManager.PauseRace();
        }

        private void ResumeCamera()
        {
            _cameraManager.ResumeRace();
        }

        #endregion

        #region ライト
        /// <summary>
        /// ライトの色をまとめて設定する
        /// </summary>
        public virtual void SetLightColor(Color light, Color shadow)
        {
            SetLightColorForModels(_horseModels, light, shadow);
        }

        protected void SetLightColorForModels(RaceModelController[] modelArray, Color light, Color shadow)
        {
            for (int i = 0; i < modelArray.Length; i++)
            {
                var model = modelArray[i];
                if (model == null)
                {
                    continue;
                }
                model.LightProbeColor = light;
                model.LightProbeShadowColor = shadow;
            }
        }

        /// <summary>
        /// ライト関連のパラメータをコース環境データを元にリセットする
        /// </summary>
        public virtual void ResetLightParameter()
        {
            ResetLightParameterForModels(_horseModels);
        }

        protected void ResetLightParameterForModels(RaceModelController[] modelArray)
        {
            for (int i = 0; i < modelArray.Length; i++)
            {
                var model = modelArray[i];
                if (model == null)
                {
                    continue;
                }
                model.TurfShadowColor = _courseEnvParam.characterTurfShadow;
                model.DirtShadowColor = _courseEnvParam.characterDirtShadow;
                model.LightProbeColor = _courseEnvParam.DirectionalLightParam.LightProbeColor;
                model.LightProbeShadowColor = _courseEnvParam.DirectionalLightParam.LightProbeShadowColor;
            }
        }

        /// <summary>
        /// DirectionalLightに対する共通設定
        /// </summary>
        /// <param name="directionalLight"></param>
        protected void EnableDirectionalLightCommon(Light directionalLight)
        {
            if(directionalLight == null) return;
            if (!RaceQualitySettings.IsUseRealtimeShadowCommon) return;

            // 背景のBakeに使用しているLightRotation
            _directionalLightTransform.localRotation = StaticVariableDefine.Race.RaceDefineStatic.DIRECTIONAL_LIGHT_ROTATION;

#if CYG_DEBUG
            // #120267: ここでCourseCameraControllerにキャッシュされているDirectionalLightのRotation情報を再設定する必要がある
            if (_courseCameraController != null)
            {
                foreach (var controller in _courseCameraController)
                {
                    if (controller == null) continue;
                    
                    // controller.Initializeが実行される度にCameraData::GetCustomRenderParameterが呼ばれて
                    // CustomRenderParameterの上限を超えてしまう。それを防ぐために都度解放するようにする
                    controller.ShadowMapController?.Release();
                    controller.SetupShadowMapController();
                }
            }
#endif

            // リアルタイムライトの設定
            directionalLight.shadows = LightShadows.Hard;

            // 元々はリアルタイムシャドウ追加時にデフォルト(CullingMask全部あり状態)だと
            // ポスト前にCameraDepthTextureがクリアされてしまっていたためそれを避けるためにCullingMaskをMainCameraと合わせる対応を入れていた。
            // URP以降後はこの問題は発生しないのと、リアルタイムシャドウを制御する個所が増え正常にバックアップが取れなくなり
            // 131972の問題が発生してしまっていたのでLightのCullingMask自体の設定を行わないように対応。
            // 細かく制御する場合はOn->Off/Off->Onのケースがあり必要に応じてPush/Popするような仕組みにする必要があると思われるが
            // 現時点(2024/01/10)では特に必要なさそうなので特に対応していない。
            // このフラグはCullingMask以外のパラメータにも影響しているのでそのままおいておく。
            _isSetDirectionalLightParam = true;
        }

        /// <summary>
        /// リアルタイムシャドウ用のライティングを有効にする設定
        /// </summary>
        public virtual void EnableDirectionalLight()
        {
            if (!RaceQualitySettings.IsUseRealtimeShadowCommon) return;

            var dirLightInst = DirectionalLightManager.Instance;
            // 背景のBakeに使用しているLightRotation
            dirLightInst.SetRotation(StaticVariableDefine.Race.RaceDefineStatic.DIRECTIONAL_LIGHT_ROTATION);

            dirLightInst.DirectionalLight.shadows = LightShadows.Hard;
            EnableDirectionalLightCommon(dirLightInst.DirectionalLight);
        }

        public virtual void DisableDirectionalLight()
        {
            if (!_isSetDirectionalLightParam)
            {
                return;
            }

            if (DirectionalLightManager.HasInstance())
            {
                DirectionalLightManager.Instance.Reset();
            }
            _isSetDirectionalLightParam = false;
        }
        
        /// <summary> リアルタイムシャドウを有効にする </summary>
        /// <remarks> EnableDirectionalLightに加えて各レースオブジェクト等のシャドウも調整します </remarks>
        public virtual void EnableRealTimeShadow()
        {
            if (RaceManager.RaceInfo != null && RaceManager.RaceInfo.IsStoryRace)
            {
                //StoryRaceなら専用のTempフラグ立てる
                TempData.Instance.RaceData.IsStoryRaceRealtimeShadow = true;
            }
            // まずはライティング
            EnableDirectionalLight();
            // レース場
            _courseManager.EnableRealTimeShadow();
            // キャラ
            for (int i = 0; i < _horseModels.Length; i++)
            {
                var model = _horseModels[i];
                if (model == null)
                {
                    continue;
                }
                model.ChangeShadowController(isUseRealtimeShadow: true);
            }
        }

        /// <summary> リアルタイムシャドウを有効にする </summary>
        /// <remarks> EnableDirectionalLightに加えて各レースオブジェクト等のシャドウも調整します </remarks>
        public virtual void DisableRealTimeShadow()
        {
            if (RaceManager.RaceInfo != null && RaceManager.RaceInfo.IsStoryRace)
            {
                TempData.Instance.RaceData.IsStoryRaceRealtimeShadow = false;
            }
            // まずはライティング
            DisableDirectionalLight();
            // レース場
            _courseManager.DisableRealTimeShadow();
            // キャラ
            for (int i = 0; i < _horseModels.Length; i++)
            {
                if (_horseModels[i] == null) continue; //歯抜けなキャラは除外
                
                var model = _horseModels[i];
                if (model == null)
                {
                    continue;
                }
                model.ChangeShadowController(isUseRealtimeShadow: false);
            }
        }
        
#if UNITY_ANDROID
        /// <summary>
        /// 影補正用のCustomRenderPassの実行を制御する用の関数
        ///
        /// 135692 2部8話のストーリーレ‐スのマルチカメラ時のAndroidの機種依存バグ回避対応
        /// 下記の対応を入れた際にAndroidの一部端末のみUnityの不具合と思われる症状が出てくる為それを制御する用の処理
        /// ・Androidのみ軽量化対応として軽量ステンシルに連動してMSAAを切る
        /// ・CustomRenderPassの根本対応後のUnityバグによる絵崩れ回避対応
        /// 　通常描画より前のCustomRenderPassに起因して発生しており現時点では影の処理のみ
        /// TODO: [#135845] Unity2022にアプデされたら不要になるはずなのでこちらの対応を削除する。
        /// (この件はまだ2022環境での修正が確認できておらず断定はできてない)
        /// </summary>
        /// <param name="isEnable"></param>
        public void SetEnableShadowCustomRenderPass(bool isEnable)
        {
            if (_courseCameraController == null) return;

            if (isEnable)
            {
                foreach (var controller in _courseCameraController)
                {
                    if (controller == null) continue;
                    controller.SetupShadowMapController();
                }
            }
            else
            {
                foreach (var controller in _courseCameraController)
                {
                    if (controller == null) continue;
                    controller.ShadowMapController?.Release();
                }
            }
        }
#endif
        #endregion

        #region ポストエフェクト
        /// <summary>
        /// アンチエイリアスのレベルを取得する
        /// </summary>
        /// <returns></returns>
        private static int GetAntialiasingLevel()
        {
            if (GraphicSettings.Instance.Get3DQuality() == GraphicSettings.Graphics3DQuality.None)
            {
#if !CYG_PRODUCT
                if (MeasurementSettingSceneController.IsNoAntialiasing)
                {
                    return 1;
                }
#endif
                // レース品質設定でMSAAが無効化されている。
                if (!RaceQualitySettings.IsUseMSAA)
                {
                    return GraphicSettings.UNITY_MSAA_QUALITY_NONE;
                }
                // デバイス情報に応じてAAレベルを決めるのでDeviceDependencySupportからAAレベルを取得する
                return DeviceDependencySupport.GetRaceAntialasingLevel();
            }
            else
            {
                return GraphicSettings.Instance.Get3DAntiAliasingLevel(true);
            }
        }
        #endregion


        #region デバッグ
#if CYG_DEBUG && UNITY_EDITOR
        private void OnGUI()
        {
            //一時停止中でもアウトライン更新を反映する
            if (UnityEditor.EditorApplication.isPaused)
            {
                UpdateOutlineLength();
                UpdateCharacterMaterialPropertyBlock();
                UpdateRenderSettings();
            }
        }

        private void OnDrawGizmos()
        {
            // オーバーラン中のディザシェーダー切り替え用Bounds描画。
            if (RaceCameraManager.HasInstance())
            {
                if (RaceCameraManager.Instance.IsOverrunCamera())
                {
                    Gizmos.DrawWireCube(_overrunDitherBounds.center, _overrunDitherBounds.size);
                }
            }
            _courseWeatherEffectController?.OnDrawGizmos();

            // サンシャフトの光源位置を表示
            if (_cameraManager.ImageEffect.SunShaftsParam.SunTransform != null && _cameraManager.ImageEffect.SunShaftsParam.IsEnable)
            {
                Gizmos.color = _cameraManager.ImageEffect.SunShaftsParam.SunColor;
                // オブジェクトの位置からオフセットされた位置に球体を描画
                Gizmos.DrawSphere(_cameraManager.ImageEffect.SunShaftsParam.SunTransform.position, 0.5f);
            }
        }
#endif
        #endregion

        #region RenderingTool用
#if CYG_DEBUG && UNITY_EDITOR
        public virtual void StartRendering()
        {
            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.StartRendering();
                }
            }
        }

        public virtual void EndRendering()
        {
            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.EndRendering();
                }
            }
        }

        public virtual void StartSimulation()
        {
            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.StartRenderingSimulation();
                }
            }
        }

        public virtual void EndSimulation()
        {
            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.EndRenderingSimulation();
                }
            }
        }

        public virtual void ResetHorseCySpring()
        {
#if CYG_DEBUG
            if (RaceDebugger.IsMemoryCheckAutoPlay && !IsModelInitialized)
            {
                return;
            }
#endif

            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.ResetCySpring();
                    model.ReserveWarmingUpCySpring(3f);
                    model.ResetWindTime();
                }
            }
        }

        public virtual void AlterFixedLateUpdateHorse()
        {
#if CYG_DEBUG
            if (RaceDebugger.IsMemoryCheckAutoPlay && !IsModelInitialized)
            {
                return;
            }
#endif

            // 揺れもののシミュレーションのために呼んでる
            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.Resume();
                    model.AlterFixedLateUpdate();
                    model.AlterFixedLateUpdatePost();
                    model.Pause();
                }
            }
        }

        public virtual void SetDeltaTime(float deltaTime)
        {
#if CYG_DEBUG
            if (RaceDebugger.IsMemoryCheckAutoPlay && !IsModelInitialized)
            {
                return;
            }
#endif

            foreach (var model in _horseModels)
            {
                if (model != null)
                {
                    model.OriginalDeltaTime = deltaTime;
                }
            }
        }
#endif
        #endregion
    }
}
