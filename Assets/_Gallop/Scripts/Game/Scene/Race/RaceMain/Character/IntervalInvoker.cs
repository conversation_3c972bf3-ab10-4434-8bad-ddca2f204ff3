using System;

namespace Gallop
{
    /// <summary>
    /// 任意の間隔で処理実行。
    /// </summary>
    public class IntervalInvoker
    {
        private readonly int _intervalMax;
        private int _interval;
        private readonly Action _funcUpdate;
        private bool _isForceFirstUpdate;

        /// <summary>
        /// コンストラクタ。
        /// </summary>
        /// <param name="funcUpdate">呼び出す処理。</param>
        /// <param name="intervalMax">更新間隔。</param>
        /// <param name="intervalStart">更新間隔初期値。</param>
        /// <param name="isForceFirstUpdate">最初のUpdateで必ずfuncUpdateを呼び出すならtrue。</param>
        public IntervalInvoker( Action funcUpdate, int intervalMax, int intervalStart, bool isForceFirstUpdate )
        {
            _funcUpdate = funcUpdate;
            _intervalMax = intervalMax;
            _interval = intervalStart;
            _isForceFirstUpdate = isForceFirstUpdate;
        }

        /// <summary>
        /// 更新。
        /// </summary>
        public void Update()
        {
            // 最初の更新は必ず行う。
            if( _isForceFirstUpdate )
            {
                _isForceFirstUpdate = false;
            }
            // 一定間隔で更新へ進む。
            else
            {
                if( _interval > 0 )
                {
                    --_interval;
                    return;
                }
                _interval = _intervalMax;
            }

            _funcUpdate?.Invoke();
        }
    }
}
