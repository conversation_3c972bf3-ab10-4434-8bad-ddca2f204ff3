using UnityEngine;
using UnityEngine.Rendering;

namespace Gallop
{
    namespace Model.Component
    {
        // こちらはストーリーレースで使用される用のリアルタイムシャドウと丸影の併用クラス。
        public class StoryRaceShadowController : RaceShadowController
        {
            /// <summary>
            /// 丸影と落ち影のどちらの利用か
            /// </summary>
            public enum UseShadow
            {
                Circle,
                Realtime
            }

            private UseShadow _currentUseShadow = UseShadow.Circle;
            CharaPartsHolder _partsData;

            public StoryRaceShadowController(ref Context context) : base(ref context)
            {
                _partsData = context.PartsHolder;
                //リアルタイムシャドウの制御設定
                SetupRealtimeShadow();
                SetUpShadow(ref context);
            }

            public override void SetShadowType(ShadowType type)
            {
                //丸影の時だけ基底を呼び出す
                if (_currentUseShadow == UseShadow.Circle)
                {
                    base.SetShadowType(type);
                }
            }

            public override void AfterEndCySpringSimulation()
            {
                //丸影の時だけ基底を呼び出す
                if (_currentUseShadow == UseShadow.Circle)
                {
                    base.AfterEndCySpringSimulation();
                }
            }

            public override void OnInitialize()
            {
                //丸影の基底を呼び出す
                base.OnInitialize();
                _CircleShadownitialize = true;

                //開始地点のフラグを見て呼び分ける
                if (RaceQualitySettings.IsUseRealtimeShadowCommon)
                {
                    ChangeUseShadow(UseShadow.Realtime);
                }
                else
                {
                    ChangeUseShadow(UseShadow.Circle);
                }
            }

            protected override void OnUpdate()
            {
                //丸影の時だけ基底を呼び出す
                if (_currentUseShadow == UseShadow.Circle)
                {
                    base.OnUpdate();
                }
            }

            public void SetupRealtimeShadow()
            {
                // リアルタイムシャドウ用のCastの対象を有効にする
                _partsData.SetShadowCasting(ShadowCastingMode.On);
            }

            private void DisableRealtimeShadow()
            {
                // リアルタイムシャドウ用のCastの対象を無効にする
                _partsData.SetShadowCasting(ShadowCastingMode.Off);
            }

            /// <summary>
            /// 影を変更する必要判定
            /// </summary>
            /// <param name="UseCircleShadow"></param>
            /// <returns></returns>
            public bool IsNeedChangeShadow(bool UseCircleShadow)
            {
                //既に丸影を利用
                if (_currentUseShadow == UseShadow.Circle && UseCircleShadow)
                {
                    return false;
                }
                //既にリアルタイムシャドウを利用
                else if (_currentUseShadow == UseShadow.Realtime && !UseCircleShadow)
                {
                    return false;
                }

                return true;
            }

            /// <summary>
            /// 使用する影の切り替え
            /// </summary>
            public void ChangeUseShadow(UseShadow useShadow)
            {
                if (useShadow == UseShadow.Realtime)
                {
                    _shadowObject.SetActive(false);
                    SetupRealtimeShadow();
                    _currentUseShadow = UseShadow.Realtime;
                }
                else
                {
                    _shadowObject.SetActive(true);
                    DisableRealtimeShadow();
                    _currentUseShadow = UseShadow.Circle;
                }
            }

            /// <summary>
            /// CastShadowをShadowOnlyにする
            /// </summary>
            public void ChangeShadowOnly()
            {
                // リアルタイムシャドウ用のCastの対象を有効にする
                _partsData.SetShadowCasting(ShadowCastingMode.ShadowsOnly);
            }
        }
    }
}
