#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    /// <summary>
    /// Singletonサポート
    /// </summary>
    public abstract class Singleton<T> where T : Singleton<T>, new()
    {
#if STANDALONE_SIMULATOR
        [ThreadStatic]
#endif
        private static T _instance = null;
        public static T Instance
        {
            get
            {
                Debug.Assert(_instance != null);
                return _instance;
            }
        }

        /// <summary>
        /// 明示的な生成リクエスト
        /// </summary>
        public static void CreateInstance()
        {
            if (HasInstance()) { return; }
            _instance = new T();
            _instance.OnInitialize();
        }

        /// <summary>
        /// Singleton存在チェック
        /// </summary>
        /// <returns></returns>
        public static bool HasInstance()
        {
            return _instance != null;
        }

        /// <summary>
        /// インスタンス破棄　インターフェース
        /// </summary>
        public static void DestroyInstance()
        {
            if (HasInstance())
            {
                _instance.DestroyInstanceInternal();
            }
        }

        public void DestroyInstanceInternal()
        {
            OnFinalize();

            _instance = null;
        }


        // --------------------------------------------
        //  派生先定義用
        // --------------------------------------------

        /// <summary>
        /// 初期化
        /// Singletonとして使用されるオブジェクトのみ実行される(Awake相当)
        /// </summary>
        protected virtual void OnInitialize()
        {
        }

        /// <summary>
        /// 後始末
        /// Singletonとして使用されるオブジェクトのみ実行される(OnDestroy相当)
        /// </summary>
        protected virtual void OnFinalize()
        {
        }
    }
}
#endif
