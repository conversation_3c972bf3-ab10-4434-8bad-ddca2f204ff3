#if STANDALONE_SIMULATOR || UNITY_EDITOR
using System;

namespace StandaloneSimulator
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ポジション維持機能実処理：ペースアップ。
    /// </summary>
    //-------------------------------------------------------------------
    public class PositionKeepStrategyPaseUp : PositionKeepStrategyPaseBase
    {
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        public override Gallop.RaceDefine.PositionKeepMode PositionKeepMode { get { return Gallop.RaceDefine.PositionKeepMode.PaseUp; } }

        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        public PositionKeepStrategyPaseUp(
            IHorseRaceInfoSimulate ownerHorse,
            Gallop.RaceParamDefine.PositionKeepParam keepParam,
            IRaceRandomGenerator randomGenerator,
            IRaceHorseAccessor horseAccessor,
            int courseDistance,
            int sectionNum,
            float baseTargetSpeedMultiply,
            IHorsePaseMakerCalculator paseMakerCalculator)
            : base(ownerHorse, keepParam, randomGenerator, horseAccessor, courseDistance, sectionNum,  paseMakerCalculator)
        {
            BaseTargetSpeedMultiply = baseTargetSpeedMultiply;
        }

        //---------------------------------------------------------------
        public override void Update(float deltaTime)
        {
        }

        //---------------------------------------------------------------
        public override bool TryStart(bool isRot)
        {
            // 確率抽選。
            if(isRot)
            {
                float per = CalcStartPer(_ownerHorse.Wiz, _keepParam);
                if (_randomGenerator.GetRandom(100.0f) >= per)
                {
                    return false;
                }
            }

            // ここまで来たら発動。
            _endDistance = CalcEndDistance();
            // ペース調整によって目指す距離差を計算。
            InitPaseAdjustTargetDistance(false);
            return true;
        }

        //---------------------------------------------------------------
        private static float CalcStartPer(float wiz, Gallop.RaceParamDefine.PositionKeepParam keepParam)
        {
            float per = keepParam.PositionKeepPaseUpStartPerVal1 * (float)Math.Log10(wiz * 0.1f);
            return per;
        }

        //---------------------------------------------------------------
        public override bool CheckEnd(float deltaTime)
        {
            // 一定距離走行したら終了。
            if (CheckEndDistance())
            {
                return true;
            }

            // 本来の位置取りまで追いついたら終了。
            if (CheckPaseAdjustEnd())
            {
                return true;
            }

            return false;
        }

        //---------------------------------------------------------------
        private bool CheckPaseAdjustEnd()
        {
            // ペースメーカーとの距離差が、目指す距離差以内に近付いたらペースダウン終了可能。
            float distanceDiff = CalcPaseMakerDistanceDiff();
#if CYG_DEBUG
            _dbgCurDistanceDiffFromPaseMaker = distanceDiff;
#endif
            return distanceDiff <= _paseAdjustTargetDistanceDiff;
        }


        //---------------------------------------------------------------
        public override void Clear()
        {
        }
    }
}
#endif
