// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/face_type_data
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - []
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterFaceTypeData : AbstractMasterData
    {
        public const string TABLE_NAME = "face_type_data";

        MasterCardDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<string> _notFounds = null;

        // cache dictionary
        private Dictionary<string, FaceTypeData> _lazyPrimaryKeyDictionary = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<string, FaceTypeData> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterFaceTypeData");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterFaceTypeData(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<string, FaceTypeData>();
            _db = db;
        }


        public FaceTypeData Get(string label)
        {
            string key = label;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterFaceTypeData");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(label);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<string>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterFaceTypeData", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private FaceTypeData _SelectOne(string label)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_FaceTypeData();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for FaceTypeData");
                return null;
            }

            // SELECT `eyebrow_l`,`eyebrow_r`,`eye_l`,`eye_r`,`mouth`,`mouth_shape_type`,`inverce_face_type`,`set_face_group` FROM `face_type_data` WHERE `label`=?;
            if (!query.BindText(1, label)) { return null; }

            FaceTypeData orm = null;

            if (query.Step())
            {
                string eyebrowL        = query.GetText(0);
                string eyebrowR        = query.GetText(1);
                string eyeL            = query.GetText(2);
                string eyeR            = query.GetText(3);
                string mouth           = query.GetText(4);
                int mouthShapeType     = (int)query.GetInt(5);
                string inverceFaceType = query.GetText(6);
                int setFaceGroup       = (int)query.GetInt(7);

                orm = new FaceTypeData(label, eyebrowL, eyebrowR, eyeL, eyeR, mouth, mouthShapeType, inverceFaceType, setFaceGroup);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", label));
            }

            query.Reset();

            return orm;
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_FaceTypeData()) {
                while (query.Step()) {
                    string label           = query.GetText(0);
                    string eyebrowL        = query.GetText(1);
                    string eyebrowR        = query.GetText(2);
                    string eyeL            = query.GetText(3);
                    string eyeR            = query.GetText(4);
                    string mouth           = query.GetText(5);
                    int mouthShapeType     = (int)query.GetInt(6);
                    string inverceFaceType = query.GetText(7);
                    int setFaceGroup       = (int)query.GetInt(8);

                    string key = label;
                    FaceTypeData orm = new FaceTypeData(label, eyebrowL, eyebrowR, eyeL, eyeR, mouth, mouthShapeType, inverceFaceType, setFaceGroup);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class FaceTypeData
        {
            /// <summary> (CSV column: label) </summary>
            public readonly string Label;
            /// <summary> (CSV column: eyebrow_l) </summary>
            public readonly string EyebrowL;
            /// <summary> (CSV column: eyebrow_r) </summary>
            public readonly string EyebrowR;
            /// <summary> (CSV column: eye_l) </summary>
            public readonly string EyeL;
            /// <summary> (CSV column: eye_r) </summary>
            public readonly string EyeR;
            /// <summary> (CSV column: mouth) </summary>
            public readonly string Mouth;
            /// <summary> (CSV column: mouth_shape_type) </summary>
            public readonly int MouthShapeType;
            /// <summary> (CSV column: inverce_face_type) </summary>
            public readonly string InverceFaceType;
            /// <summary> (CSV column: set_face_group) </summary>
            public readonly int SetFaceGroup;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public FaceTypeData(string label = "", string eyebrowL = "", string eyebrowR = "", string eyeL = "", string eyeR = "", string mouth = "", int mouthShapeType = 0, string inverceFaceType = "", int setFaceGroup = 0)
            {
                this.Label           = label;
                this.EyebrowL        = eyebrowL;
                this.EyebrowR        = eyebrowR;
                this.EyeL            = eyeL;
                this.EyeR            = eyeR;
                this.Mouth           = mouth;
                this.MouthShapeType  = mouthShapeType;
                this.InverceFaceType = inverceFaceType;
                this.SetFaceGroup    = setFaceGroup;
            }
        }
    }
}
#endif
