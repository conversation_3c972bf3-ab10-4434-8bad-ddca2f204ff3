using System.Numerics;
using System.Collections.Generic;
using System.Linq;
using System;

namespace StandaloneSimulator
{
    /// <summary>
    /// RaceUtil：GALLOP/.NET兼用
    /// </summary>
    public static partial class RaceUtil
    {
        private const float BASHIN_INT2FLOAT_ACCURACY = 10000.0f;
        
        public static float Distance2BashinFloat(float distance)
        {
            if (distance < 0)
            {
                Debug.LogError("距離が負の値です。distance=" + distance);
                return 0;
            }
            return distance / Gallop.RaceDefine.HORSE_LENGTH_DISTANCE_ONE;
        }
        
        /// <summary>
        /// 馬身差をfloat->int変換。※clientではfloat扱い、serverではintで扱うための変換関数。
        /// </summary>
        public static int BashinFloat2Int(float bashin)
        {
            return (int)(bashin * BASHIN_INT2FLOAT_ACCURACY);
        }
        
        public const int DISTANCE_TYPE_LONG_THRESHOLD = 2401;
        public const int DISTANCE_TYPE_MIDDLE_THRESHOLD = 1801;
        public const int DISTANCE_TYPE_MILE_THRESHOLD = 1401;

        /// <summary>
        /// レースの距離区分取得。
        /// </summary>
        /// <seealso cref="Test.TestRaceUtil.TestCalcDistanceType(RaceDefine.CourseDistanceType, int)"/>
        public static Gallop.RaceDefine.CourseDistanceType CalcDistanceType(int distance)
        {
            if (distance >= DISTANCE_TYPE_LONG_THRESHOLD)
            {
                return Gallop.RaceDefine.CourseDistanceType.Long;
            }
            else if (distance >= DISTANCE_TYPE_MIDDLE_THRESHOLD)
            {
                return Gallop.RaceDefine.CourseDistanceType.Middle;
            }
            else if (distance >= DISTANCE_TYPE_MILE_THRESHOLD)
            {
                return Gallop.RaceDefine.CourseDistanceType.Mile;
            }
            else
            {
                return Gallop.RaceDefine.CourseDistanceType.Short;
            }
        }
        
        // 着差取得
        // #121540: float誤差回避のため、サーバー実装合わせで整数値バ身への変換を経由するように変更
        public static Gallop.RaceDefine.HorseLength GetHorseLength(float diffDistance)
        {
            float diffBashin = Distance2BashinFloat(diffDistance);
            int bashinDiffFromBehind = BashinFloat2Int(diffBashin);

            for (int i = 0; i < Gallop.RaceDefine.HORSE_LENGTH_BASHIN.Length; i++)
            {
                if (bashinDiffFromBehind <= Gallop.RaceDefine.HORSE_LENGTH_BASHIN[i])
                {
                    return (Gallop.RaceDefine.HorseLength)i;
                }
            }
            return Gallop.RaceDefine.HorseLength.OverTen;  // 10馬身以上
        }
        
        // 着差判定で1秒差をどの程度の距離差と見なすか、の係数。
        public const float DIFFTIME_TO_LENGTH = /*20.0f*/15.0f;

        public static float DiffTime2Distance(float diffTime)
        {
            return diffTime * DIFFTIME_TO_LENGTH;
        }

        public static Gallop.RaceDefine.HorseLength GetHorseLengthByDiffTime(float diffTime)
        {
            float diffDistance = DiffTime2Distance(diffTime);
            return GetHorseLength(diffDistance);
        }
        
    #if CYG_DEBUG && GALLOP
        /// <summary>
        /// HorseLengthから着タイム差を算出する
        /// </summary>
        /// <param name="horseLength"></param>
        /// <returns></returns>
        public static float GetDiffTimeByHorseLength(Gallop.RaceDefine.HorseLength horseLength)
        {
            // GetHorseLengthByDiffTimeでやっていることと逆の計算をしてhorseLengthから着タイム差を算出する
            int bashinDiff = 0;
            int horseLengthInt = (int)horseLength;

            if (horseLengthInt >= Gallop.RaceDefine.HORSE_LENGTH_BASHIN.Length)
            {
                // 大差以上は定義されていないので個別に処理する必要がある
                horseLengthInt = (int)Gallop.RaceDefine.HorseLength.Ten;
                bashinDiff = Gallop.RaceDefine.HORSE_LENGTH_BASHIN[horseLengthInt];
                // このままだと10馬身差になるので少し大きくして大差にする
                bashinDiff += 1;
            }
            else
            {
                bashinDiff = Gallop.RaceDefine.HORSE_LENGTH_BASHIN[horseLengthInt];
            }
            float bashinDiffFloat = bashinDiff / BASHIN_INT2FLOAT_ACCURACY;
            float diffDistance = bashinDiffFloat * Gallop.RaceDefine.HORSE_LENGTH_DISTANCE_ONE;
            float diffTime = diffDistance / DIFFTIME_TO_LENGTH;
            return diffTime;
        }
    #endif

        public const float FINISHTIME_INT2FLOAT_ACCURACY = 1 / 10000.0f;

        /// <summary>
        /// 着タイムをint->float変換。※clientではfloat扱い、serverではintで扱うための変換関数。
        /// </summary>
        public static float FinishTimeInt2Float(int value)
        {
            return (float)value * FINISHTIME_INT2FLOAT_ACCURACY;
        }

        /// <summary>
        /// 着タイムをfloat->int変換。※clientではfloat扱い、serverではintで扱うための変換関数。
        /// </summary>
        public static int FinishTimeFloat2Int(float value)
        {
            return (int)(value / FINISHTIME_INT2FLOAT_ACCURACY);
        }

        private const int FINISHTIME_ACCURACY = 10;
        
        /// <summary>
        /// HorseIndexをRaceHorseData.frame_orderに変換。
        /// </summary>
        public static int HorseIndex2FrameOrder(int horseIndex)
        { 
            return horseIndex + 1;
        }
        public static int FrameOrder2HorseIndex(int frameOrder)
        { 
            return frameOrder - 1;
        }
        
        /// <summary>
        /// 最終コーナーか判定する
        /// </summary>
        /// <param name="remainDistance"></param>
        /// <param name="cornerNo"></param>
        /// <returns></returns>
        public static bool IsFinalCorner(float remainDistance, int cornerNo)
        {
            if (remainDistance <= Gallop.RaceDefine.LAST_CORNER_THRESHOLD && cornerNo >= Gallop.RaceDefine.LAST_CORNER_NUMBER)
            {
                return true;
            }

            return false;
        }
        
        public static bool GetCornerDistance(CourseParam courseParam, out float startDistance, out float endDistance)
        {
            startDistance = 0;
            endDistance = 0;

            if (null == courseParam)
            {
                return false;
            }
            if (courseParam.Values.Length <= 0)
            {
                return false;
            }

            // コーナー継続距離を取得。第二引数で継続距離を指定されていない場合は規定値を使用する。
            int cornerDistance = Gallop.RaceDefine.CORNER_DISTANCE_DEFAULT;
            if (courseParam.Values.Length > CourseParam.CORNER_VALUE_INDEX_DISTANCE)
            {
                cornerDistance = courseParam.Values[CourseParam.CORNER_VALUE_INDEX_DISTANCE];
            }
            startDistance = courseParam._distance;
            endDistance = courseParam._distance + cornerDistance;
            return true;
        }
        
        public static bool GetSlopeDistance(CourseParam courseParam, out float startDistance, out float endDistance)
        {
            startDistance = 0;
            endDistance = 0;

            if (null == courseParam)
            {
                return false;
            }
            if (!CourseParam.GetSlopeValue(courseParam.Values, out _, out _, out float slopeLength))
            {
                return false;
            }

            startDistance = courseParam._distance;
            endDistance = courseParam._distance + slopeLength;
            return true;
        }
        
        /// <summary>
        /// LaneDistanceでコース外側にずらす値を取得。
        /// </summary>
        /// <returns>LaneDistance=0でコースパス上を走る。LaneDistanceが大きくなるほど、コース外側になる。</returns>
        public static float CalcWorldPosXFromLane(float laneDistance)
        {
            return Gallop.RaceDefine.COURSE_WIDTH * laneDistance;
        }

        /// <summary>
        /// 左向き直線を左回り、右向き直線を右回りと扱うための変換処理。
        /// </summary>
        public static Gallop.RaceDefine.Rotation GetCourseRotationCategory(Gallop.RaceDefine.Rotation rotation)
        {
            switch (rotation)
            {
                case Gallop.RaceDefine.Rotation.Left:
                case Gallop.RaceDefine.Rotation.StraightLeft: // 左向き直線も左回りの数値を使う。
                    return Gallop.RaceDefine.Rotation.Left;

                case Gallop.RaceDefine.Rotation.Right:
                case Gallop.RaceDefine.Rotation.StraightRight: // 右向き直線も右回りの数値を使う。
                    return Gallop.RaceDefine.Rotation.Right;
                    
                default:
                    return Gallop.RaceDefine.Rotation.Left;
            }
        }
        
        public const float SUPPORT_CARD_BONUS_INT2FLOAT_ACCURACY = 1 / 10000.0f;
        
        /// <summary>
        /// サポートカードによるスコアボーナスをint->float変換。※clientではfloat扱い、serverではintで扱うための変換関数。
        /// </summary>
        public static float SupportCardBonusInt2Float(int value)
        {
            return (float)value * SUPPORT_CARD_BONUS_INT2FLOAT_ACCURACY;
        }
    }
}

#if STANDALONE_SIMULATOR || UNITY_EDITOR || CYG_DEBUG
namespace StandaloneSimulator
{
    /// <summary>
    /// RaceUtil：.NET/デバッグ用
    /// </summary>
    public static partial class RaceUtil
    {
        /// <summary>
        /// 距離(m)を馬身に変換。
        /// </summary>
        /// <returns>端数切捨てで整数で返却する。</returns>
        /// <seealso cref="Test.TestRaceUtil.TestDistance2Bashin(int, float)"/>
        public static int Distance2Bashin(float distance)
        {
            return RaceUtilMath.FloorToInt(Distance2BashinFloat(distance));
        }
        
        public static float BashinInt2Float(int bashin)
        {
            return bashin / BASHIN_INT2FLOAT_ACCURACY;
        }

        /// <summary>
        /// 人気◎マークの数をカウント。
        /// </summary>
        /// <seealso cref="Test.TestHorsePopularityCalculator.TestCountPopularityTopIcon(int, int, int, int)"/>
        public static int CountPopularityTopIcon(int rankLeft, int rankCenter, int rankRight)
        {
            int retCount = 0;

            // 人気計算の中間パラメータ順位がトップの場合に◎が付く。最大３個まで。
            if (rankLeft == 0)
            {
                ++retCount;
            }
            if (rankCenter == 0)
            {
                ++retCount;
            }
            if (rankRight == 0)
            {
                ++retCount;
            }

            return retCount;
        }
        
        /// <summary>
        /// 人気マークの数をカウント。
        /// </summary>
        /// <seealso cref="Test.TestHorsePopularityCalculator.TestCountPopularityIcon(int, int, int, int)"/>
        public static int CountPopularityIcon(int rankLeft, int rankCenter, int rankRight)
        {
            int retCount = 0;

            // 人気計算の中間パラメータ順位が5位以内の時にマークがつく。
            if (rankLeft < Gallop.RaceDefine.POPULARITY_ICON_MAX)
            {
                ++retCount;
            }
            if (rankCenter < Gallop.RaceDefine.POPULARITY_ICON_MAX)
            {
                ++retCount;
            }
            if (rankRight < Gallop.RaceDefine.POPULARITY_ICON_MAX)
            {
                ++retCount;
            }

            return retCount;
        }
    }
}
#endif

#if STANDALONE_SIMULATOR || UNITY_EDITOR
namespace StandaloneSimulator
{
    /// <summary>
    /// RaceUtil：.NET専用
    /// </summary>
    public static partial class RaceUtil
    {
        /// <summary>
        /// ラストスパート結果から評価値への変換
        /// </summary>
        /// <param name="result"> ラストスパート結果 </param>
        /// <returns></returns>
        public static Gallop.RaceDefine.LastSpurtEvaluateValue LastSpurtCalcResult2EvaluateValue(LastSpurtCalcResult result)
        {
            switch (result)
            {
                case LastSpurtCalcResult.False:
                case LastSpurtCalcResult.FalseBelowNeedMinHp:
                    return Gallop.RaceDefine.LastSpurtEvaluateValue.False;

                case LastSpurtCalcResult.True:
                    return Gallop.RaceDefine.LastSpurtEvaluateValue.True;

                case LastSpurtCalcResult.TrueExceedNeedMaxHp:
                    return Gallop.RaceDefine.LastSpurtEvaluateValue.TrueExceedNeedMaxHp;
            }

            return Gallop.RaceDefine.LastSpurtEvaluateValue.False;
        }

        /// <summary>
        /// 着タイムの丸め。実数をFINISHTIME_ACCURACY倍した整数を返す。
        /// </summary>
        /// <remarks>
        /// 着順表での表示とスコア計算で使う着タイムの精度を合わせるために使う。
        /// </remarks>
        public static int ScaleAndRoundFinishTime(float finishTime)
        {
            // 小数第２位以下を切り捨て。
            int finishTimeInt = RaceUtilMath.FloorToInt(finishTime * FINISHTIME_ACCURACY);
            return finishTimeInt;
        }
        
        /// <summary>
        /// 指定コーナーの開始距離、終了距離を計算。
        /// </summary>
        /// <param name="corner">コーナー番号。</param>
        /// <param name="startDistance">コーナー開始距離返却。</param>
        /// <param name="endDistance">コーナー終了距離返却。</param>
        /// <returns>距離の取得に成功したらtrue。</returns>
        public static bool CalcCornerDistance(int corner, out float startDistance, out float endDistance)
        {
            startDistance = 0;
            endDistance = 0;

            if (corner <= 0)
            {
                return false;
            }
            var corners = RaceManagerSimulate.Instance.GetCourseEventCorner();
            if (null == corners)
            {
                return false;
            }

            // コース後半のコーナーからチェックしていく。
            for (int i = corners.Length - 1; i >= 0; --i)
            {
                var cornerParam = corners[i];

                if (cornerParam.Values.Length < CourseParam.CORNER_VALUE_NUM)
                {
                    continue;
                }
                if (corner != cornerParam.Values[CourseParam.CORNER_VALUE_INDEX_NO])
                {
                    continue;
                }

                return GetCornerDistance(cornerParam, out startDistance, out endDistance);
            }

            // 指定コーナーが見つからなかった。
            return false;
        }

        /// <summary>
        /// 最終コーナーの開始距離、終了距離を計算。
        /// </summary>
        /// <param name="startDistance">コーナー開始距離返却。</param>
        /// <param name="endDistance">コーナー終了距離返却。</param>
        /// <returns>距離の取得に成功したらtrue。</returns>
        public static bool CalcFinalCornerDistance(int courseDistance, out float startDistance, out float endDistance)
        {
            startDistance = 0;
            endDistance = 0;

            var corners = RaceManagerSimulate.Instance.GetCourseEventCorner();
            if (null == corners)
            {
                return false;
            }

            // コース後半のコーナーからチェックしていく。
            for (int i = corners.Length - 1; i >= 0; --i)
            {
                var cornerEvent = corners[i];

                if (cornerEvent.Values.Length < CourseParam.CORNER_VALUE_NUM)
                {
                    continue;
                }

                var remainDistance = courseDistance - cornerEvent._distance;
                var cornerNumber = cornerEvent.Values[CourseParam.CORNER_VALUE_INDEX_NO];
                if (!IsFinalCorner(remainDistance, cornerNumber))
                {
                    continue;
                }

                return GetCornerDistance(cornerEvent, out startDistance, out endDistance);
            }

            // 指定コーナーが見つからなかった。
            return false;
        }

        public static bool GetStraightParam(
            int[] courseParamValues,
            out bool isStart,
            out Gallop.RaceDefine.StraightFrontType frontType,
            out bool isLast
        ){
            isStart = false;
            frontType = Gallop.RaceDefine.StraightFrontType.Null;
            isLast = false;

            if (courseParamValues.Length <= CourseParam.STRAIGHT_VALUE_INDEX_START_END)
            {
                Debug.LogError("GetStraightParam courseParamValues.Length <= CourseParam.STRAIGHT_VALUE_INDEX_START_END. len=" + courseParamValues.Length);
                return false;
            }

            isStart = courseParamValues[CourseParam.STRAIGHT_VALUE_INDEX_START_END] == 1;
            if (isStart)
            {
                // 直線開始。
                if (courseParamValues.Length <= CourseParam.STRAIGHT_VALUE_INDEX_TYPE)
                {
                    Debug.LogError("EventStraightChange values.Length <= CourseParam.STRAIGHT_VALUE_INDEX_TYPE. len=" + courseParamValues.Length);
                    return false;
                }
                frontType = (Gallop.RaceDefine.StraightFrontType)courseParamValues[CourseParam.STRAIGHT_VALUE_INDEX_TYPE];
                isStart = true;
                
                // 最後の直線かどうか
                if (RaceManagerSimulate.Instance.IsExistLastStraightEvent)
                {
                    // 最終直線以外は設定されていなかったりするので要素数が足りてなければisLast = falseのままreturnする
                    if (courseParamValues.Length <= CourseParam.STRAIGHT_VALUE_INDEX_IS_LAST)
                    {
                        // 動作的には問題なしとして扱うのでtrueで返す
                        return true;
                    }
                    isLast = courseParamValues[CourseParam.STRAIGHT_VALUE_INDEX_IS_LAST] == 1;
                }
            }
            else
            {
                // 直線終了。
                frontType = Gallop.RaceDefine.StraightFrontType.Null;
                isStart = false;
            }

            return true;
        }
        
        /// <summary>
        /// 着タイムから着順順のHorseIndex配列取得。
        /// </summary>
        /// <param name="finishTimes">HorseIndex順の着タイム。</param>
        /// <param name="horseIndexByFinishOrder">着順順のHorseIndex配列返却。</param>
        public static void InitHorseIndexByFinishOrder(double[] finishTimes, out int[] horseIndexByFinishOrder)
        {
            int numHorse = finishTimes.Length;

            // 着タイムでソート用の作業用バッファ構築。
            var finishSortBuff = new FinishOrderParam[numHorse];
            for (int i = 0; i < numHorse; i++)
            {
                finishSortBuff[i].horseIndex = i;
                finishSortBuff[i].time = finishTimes[i];
            }

            // 着タイムで昇順にソート。
            SortFinishOrderAscend(finishSortBuff);

            // HorseIndexのみ取り出し。
            horseIndexByFinishOrder = finishSortBuff.Select(f => f.horseIndex).ToArray();
        }

        public struct FinishOrderParam
        {
            public int horseIndex;
            public double time;
        }

        private static void SortFinishOrderAscend(FinishOrderParam[] pairArray)
        {
            System.Array.Sort(pairArray,
                delegate (FinishOrderParam x, FinishOrderParam y)
                {
                    int compResult = x.time.CompareTo(y.time);
                    if (0 != compResult)
                    {
                        return compResult;
                    }
                    // 着タイムが同一であった場合、第２ソートキーをHorseIndex(馬番)にする。
                    return x.horseIndex.CompareTo(y.horseIndex);
                }
            );
        }

        /// <summary>
        /// Distance/LaneDistanceから3D空間上の座標/回転を取得。
        /// </summary>
        /// <param name="distance">距離。コース距離に該当する。</param>
        /// <param name="laneDistance">横位置。</param>
        /// <param name="courseLane">レーンアニメーション管理オブジェクト。</param>
        /// <param name="posLaneOrigin">横位置を考慮しない3D座標返却。</param>
        /// <param name="posWorld">3D空間上の座標返却。</param>
        /// <param name="rot">3D空間上の回転返却。</param>
        public static void CalcWorldTransform(float distance, float laneDistance, out Vector3 posLaneOrigin, out Vector3 posWorld, out Quaternion rot)
        {
            // 指定distanceでの座標/回転をレーンアニメーションから取得する。※この値は横位置は考慮されていない。
            RaceManagerSimulate.Instance.GetLaneTransform(distance, out posLaneOrigin, out rot);
            // 指定laneDistance分、キャラをコース外側にずらした座標を取得。
            var lanePos = new Vector3(CalcWorldPosXFromLane(laneDistance), 0, 0);
            posWorld = posLaneOrigin + Vector3.Transform(lanePos, rot);
        }
        
        /// <summary>
        /// レース開始後一定距離からトップでゴールしたか。
        /// </summary>
        public static bool CheckRunningAloneSimulate(IHorseRaceInfoSimulate horse)
        {
            // 一定距離からの順位変動がなく、着順１位であれば、条件を満たしていることになる。
            
            if (horse.CurOrderDownCountDistance1 > 0 || horse.CurOrderUpCountDistance1 > 0)
            {
                return false;
            }

            if (horse.FinishOrder > 0)
            {
                return false;
            }

            return true;
        }
        
        /// <summary>
        /// ２つのチームが同じかどうか。
        /// </summary>
        public static bool IsSameTeam(int teamIdLh, int teamidRh)
        {
            // 無所属同士は同じチームとみなさない。
            if (teamIdLh == Gallop.RaceDefine.TEAM_ID_NULL || teamidRh == Gallop.RaceDefine.TEAM_ID_NULL)
            {
                return false;
            }

            return teamIdLh == teamidRh;
        }

        /// <summary>
        /// チームランクからステータスボーナスのMaster取得。
        /// </summary>
        /// <returns>teamRankに該当するデータがcsvに登録されていないとnullが返る。</returns>
        public static IRaceSingleModeTeamStatusAccessor GetTeamStatusBonus(int teamRank)
        {
            // 0は無効値。
            if (teamRank == 0 || !MasterManager.HasInstance())
            {
                return null;
            }
            
            var masterTeamStatusArray = MasterManager.Instance.MasterRaceSingleModeTeamStatus.GetAllOrderByTeamRankThreshold();
            foreach (var masterTeamStatus in masterTeamStatusArray)
            {
                if (teamRank < masterTeamStatus.TeamRankThreshold)
                {
                    return masterTeamStatus;
                }
            }

            Debug.LogWarning($"チームランクによるステータス補正がrace_single_mode_team_status.csvに登録されていないためステータス加算できていない可能性があります teamRank={teamRank}");
            return null;
        }
        
        public static bool CalcClampedFinishTime(ref float finishTimeScaled, int raceInstanceId, int randomSeed)
        {
            var masterRaceInstance = MasterManager.Instance.MasterRaceInstance.Get(raceInstanceId);
            if (masterRaceInstance == null)
            {
                Debug.LogWarning($"raceInstanceId={raceInstanceId}がcsvに登録されていない");
                return false;
            }

            var masterCourseSet = masterRaceInstance.GetRaceCourseSetMaster();
            if (masterCourseSet == null)
            {
                Debug.LogWarning($"raceInstanceId={raceInstanceId}のcourse_setがcsvに登録されていない");
                return false;
            }

            // 着タイム最小値～最大値の間に収まっているなら改竄必要無し。
            float finishTimeMin = RaceUtilMath.MasterInt2Float(masterCourseSet.FinishTimeMin);
            float finishTimeMax = RaceUtilMath.MasterInt2Float(masterCourseSet.FinishTimeMax);
            if (finishTimeScaled >= finishTimeMin &&
                finishTimeScaled <= finishTimeMax)
            {
                return false;
            }

            // 改竄後着タイムに加えるランダム値。着順表を開きなおす度にランダム値が異なるということが無いように、シードを外部から指定できるようにしてある。
            var random = new RaceRandom(randomSeed);
            
            if (finishTimeScaled < finishTimeMin)
            {
                float randomRange = RaceUtilMath.MasterInt2Float(masterCourseSet.FinishTimeMinRandomRange);
                float randomTime = random.GetRandom(-randomRange, randomRange);
                finishTimeScaled = finishTimeMin + randomTime;
            }
            else
            {
                float randomRange = RaceUtilMath.MasterInt2Float(masterCourseSet.FinishTimeMaxRandomRange);
                float randomTime = random.GetRandom(-randomRange, randomRange);
                finishTimeScaled = finishTimeMax + randomTime;
            }

            return true;
        }
    }
}
#endif