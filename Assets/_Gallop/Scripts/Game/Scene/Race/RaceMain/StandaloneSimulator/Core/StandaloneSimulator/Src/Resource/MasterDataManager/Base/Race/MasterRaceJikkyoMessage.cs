// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: race/race_jikkyo_message
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - true
        rename_map - {}
        master_alias - (none)
        group_cache - [[:group_id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterRaceJikkyoMessage : AbstractMasterData
    {
        public const string TABLE_NAME = "race_jikkyo_message";

        MasterRaceDatabase _db = null;
        private bool _preloaded = false;

        // not found cache
        private HashSet<int> _notFounds = null;

        // cache dictionary
        private Dictionary<int, RaceJikkyoMessage> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<RaceJikkyoMessage>> _dictionaryWithGroupId = null;

        // (deprecated) direct dictionary interface with implicit preload just for backward compatibility
        public Dictionary<int, RaceJikkyoMessage> dictionary {
            get
            {
                if (!_preloaded)
                {
                    Debug.Log("implicit preload with use of cache dictionary: MasterRaceJikkyoMessage");
                    _ForcePreloadAllEntries();
                }

                return _lazyPrimaryKeyDictionary;
            }
        }

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterRaceJikkyoMessage(MasterRaceDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<int, RaceJikkyoMessage>();
            _dictionaryWithGroupId = new Dictionary<int, List<RaceJikkyoMessage>>();
            _db = db;
        }


        public RaceJikkyoMessage Get(int id)
        {
            int key = (int)id;

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterRaceJikkyoMessage");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<int>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterRaceJikkyoMessage", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoMessage _SelectOne(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_RaceJikkyoMessage();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoMessage");
                return null;
            }

            // SELECT `group_id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `id`=?;
            if (!query.BindInt(1, id)) { return null; }

            RaceJikkyoMessage orm = null;

            if (query.Step())
            {
                int groupId      = (int)query.GetInt(0);
                string message   = query.GetText(1);
                string voice     = query.GetText(2);
                int per          = (int)query.GetInt(3);
                int commentGroup = (int)query.GetInt(4);
                int reuse        = (int)query.GetInt(5);
                int omitTag      = (int)query.GetInt(6);

                orm = new RaceJikkyoMessage(id, groupId, message, voice, per, commentGroup, reuse, omitTag);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0}", id));
            }

            query.Reset();

            return orm;
        }

        public RaceJikkyoMessage GetWithGroupId(int groupId)
        {
            RaceJikkyoMessage orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithGroupId(groupId);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", groupId));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoMessage _SelectWithGroupId(int groupId)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoMessage_GroupId();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoMessage");
                return null;
            }

            // SELECT `id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            RaceJikkyoMessage orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
            }

            query.Reset();

            return orm;
        }

        public List<RaceJikkyoMessage> GetListWithGroupId(int groupId)
        {
            int key = (int)groupId;

            if (!_dictionaryWithGroupId.ContainsKey(key)) {
                _dictionaryWithGroupId.Add(key, _ListSelectWithGroupId(groupId));
            }

            return _dictionaryWithGroupId[key];
        }

        public List<RaceJikkyoMessage> MaybeListWithGroupId(int groupId)
        {
            List<RaceJikkyoMessage> list = GetListWithGroupId(groupId);
            return list.Count > 0 ? list : null;
        }

        private List<RaceJikkyoMessage> _ListSelectWithGroupId(int groupId)
        {
            List<RaceJikkyoMessage> _list = new List<RaceJikkyoMessage>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoMessage_GroupId();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoMessage");
                return null;
            }

            // SELECT `id`,`message`,`voice`,`per`,`comment_group`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `group_id`=?;
            if (!query.BindInt(1, groupId)) { return null; }

            while (query.Step()) {
                RaceJikkyoMessage orm = _CreateOrmByQueryResultWithGroupId(query, groupId);
                int key = (int)orm.Id;

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private RaceJikkyoMessage _CreateOrmByQueryResultWithGroupId(LibNative.Sqlite3.PreparedQuery query, int groupId)
        {
            int id           = (int)query.GetInt(0);
            string message   = query.GetText(1);
            string voice     = query.GetText(2);
            int per          = (int)query.GetInt(3);
            int commentGroup = (int)query.GetInt(4);
            int reuse        = (int)query.GetInt(5);
            int omitTag      = (int)query.GetInt(6);

            return new RaceJikkyoMessage(id, groupId, message, voice, per, commentGroup, reuse, omitTag);
        }

        public RaceJikkyoMessage GetWithCommentGroup(int commentGroup)
        {
            RaceJikkyoMessage orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithCommentGroup(commentGroup);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", commentGroup));
                } else {
                    int key = (int)orm.Id;

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private RaceJikkyoMessage _SelectWithCommentGroup(int commentGroup)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_RaceJikkyoMessage_CommentGroup();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for RaceJikkyoMessage");
                return null;
            }

            // SELECT `id`,`group_id`,`message`,`voice`,`per`,`reuse`,`omit_tag` FROM `race_jikkyo_message` WHERE `comment_group`=?;
            if (!query.BindInt(1, commentGroup)) { return null; }

            RaceJikkyoMessage orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithCommentGroup(query, commentGroup);
            }

            query.Reset();

            return orm;
        }

        private RaceJikkyoMessage _CreateOrmByQueryResultWithCommentGroup(LibNative.Sqlite3.PreparedQuery query, int commentGroup)
        {
            int id         = (int)query.GetInt(0);
            int groupId    = (int)query.GetInt(1);
            string message = query.GetText(2);
            string voice   = query.GetText(3);
            int per        = (int)query.GetInt(4);
            int reuse      = (int)query.GetInt(5);
            int omitTag    = (int)query.GetInt(6);

            return new RaceJikkyoMessage(id, groupId, message, voice, per, commentGroup, reuse, omitTag);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithGroupId.Clear();
            _preloaded = false;
        }

        private void _ForcePreloadAllEntries()
        {
            using (LibNative.Sqlite3.Query query = _db.GetSelectAllQuery_RaceJikkyoMessage()) {
                while (query.Step()) {
                    int id           = (int)query.GetInt(0);
                    int groupId      = (int)query.GetInt(1);
                    string message   = query.GetText(2);
                    string voice     = query.GetText(3);
                    int per          = (int)query.GetInt(4);
                    int commentGroup = (int)query.GetInt(5);
                    int reuse        = (int)query.GetInt(6);
                    int omitTag      = (int)query.GetInt(7);

                    int key = (int)id;
                    RaceJikkyoMessage orm = new RaceJikkyoMessage(id, groupId, message, voice, per, commentGroup, reuse, omitTag);

                    if (!_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            _preloaded = true;
        }

        public sealed partial class RaceJikkyoMessage
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: group_id) </summary>
            public readonly int GroupId;
            /// <summary> (CSV column: message) </summary>
            public readonly string Message;
            /// <summary> (CSV column: voice) </summary>
            public readonly string Voice;
            /// <summary> (CSV column: per) </summary>
            public readonly int Per;
            /// <summary> (CSV column: comment_group) </summary>
            public readonly int CommentGroup;
            /// <summary> (CSV column: reuse) </summary>
            public readonly int Reuse;
            /// <summary> (CSV column: omit_tag) </summary>
            public readonly int OmitTag;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public RaceJikkyoMessage(int id = 0, int groupId = 0, string message = "", string voice = "", int per = 0, int commentGroup = 0, int reuse = 0, int omitTag = 0)
            {
                this.Id           = id;
                this.GroupId      = groupId;
                this.Message      = message;
                this.Voice        = voice;
                this.Per          = per;
                this.CommentGroup = commentGroup;
                this.Reuse        = reuse;
                this.OmitTag      = omitTag;
            }
        }
    }
}
#endif
