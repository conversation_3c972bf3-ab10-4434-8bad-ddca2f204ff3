// ***********************************
//             !!WARNING!!
// ***********************************
// TOOL GENERATED SCRIPT. DO NOT EDIT!
//
// Master: card/support_card_effect_table
// Author: ykst <<EMAIL>>

#if STANDALONE_SIMULATOR
using System.Collections;
using System.Collections.Generic;

namespace StandaloneSimulator
{
    /* enabled pragmas:
        naming - naming_lower_camel_case
        preload_allowed - false
        rename_map - {}
        master_alias - (none)
        group_cache - [[:id]]
        unused - []
        parser_hook_targets - []
        limited_platform - []
     */
    public sealed partial class MasterSupportCardEffectTable : AbstractMasterData
    {
        public const string TABLE_NAME = "support_card_effect_table";

        MasterCardDatabase _db = null;

        // not found cache
        private HashSet<ulong> _notFounds = null;

        // cache dictionary
        private Dictionary<ulong, SupportCardEffectTable> _lazyPrimaryKeyDictionary = null;
        private Dictionary<int, List<SupportCardEffectTable>> _dictionaryWithId = null;

        /// <summary>
        /// Constructor with database wrapper
        /// </summary>
        public MasterSupportCardEffectTable(MasterCardDatabase db) : base(db)
        {
            _lazyPrimaryKeyDictionary = new Dictionary<ulong, SupportCardEffectTable>();
            _dictionaryWithId = new Dictionary<int, List<SupportCardEffectTable>>();
            _db = db;
        }


        public ulong GetKey(int id, int type)
        {
            return ((uint)unchecked((ulong)((int)id))) | ((((ulong)unchecked((ulong)((int)type)))) << 32);
        }

        public SupportCardEffectTable Get(ulong key)
        {
            int id   = (int)unchecked((int)((key >> 0) & 0xFFFFFFFF));
            int type = (int)unchecked((int)((key >> 32) & 0xFFFFFFFF));

            return Get(id, type);
        }

        public SupportCardEffectTable Get(int id, int type)
        {
            ulong key = ((uint)unchecked((ulong)((int)id))) | ((((ulong)unchecked((ulong)((int)type)))) << 32);

            if (!_lazyPrimaryKeyDictionary.TryGetValue(key, out var orm))
            {
                if (_db == null)
                {
                    orm = null;
                    Debug.LogError("database not initialized for MasterSupportCardEffectTable");
                }
                else
                {
                    if (_notFounds == null || !_notFounds.Contains(key))
                    {
                        orm = _SelectOne(id, type);

                        if (orm == null)
                        {
                            if (_notFounds == null)
                            {
                                _notFounds = new HashSet<ulong>();
                            }

                            _notFounds.Add(key);
                            Debug.Log(string.Format("key {0} not found in MasterSupportCardEffectTable", key));
                        }
                        else
                        {
                            _lazyPrimaryKeyDictionary.Add(key, orm);
                        }
                    }
                }
            }

            return orm;
        }

        private SupportCardEffectTable _SelectOne(int id, int type)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQuery_SupportCardEffectTable();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectTable");
                return null;
            }

            // SELECT `init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? AND `type`=?;
            if (!query.BindInt(1, id))   { return null; }
            if (!query.BindInt(2, type)) { return null; }

            SupportCardEffectTable orm = null;

            if (query.Step())
            {
                int init      = (int)query.GetInt(0);
                int limitLv5  = (int)query.GetInt(1);
                int limitLv10 = (int)query.GetInt(2);
                int limitLv15 = (int)query.GetInt(3);
                int limitLv20 = (int)query.GetInt(4);
                int limitLv25 = (int)query.GetInt(5);
                int limitLv30 = (int)query.GetInt(6);
                int limitLv35 = (int)query.GetInt(7);
                int limitLv40 = (int)query.GetInt(8);
                int limitLv45 = (int)query.GetInt(9);
                int limitLv50 = (int)query.GetInt(10);

                orm = new SupportCardEffectTable(id, type, init, limitLv5, limitLv10, limitLv15, limitLv20, limitLv25, limitLv30, limitLv35, limitLv40, limitLv45, limitLv50);
            }
            else
            {
                Debug.Log(string.Format("Item not found: {0} {1}", id, type));
            }

            query.Reset();

            return orm;
        }

        public SupportCardEffectTable GetWithIdOrderByTypeAsc(int id)
        {
            SupportCardEffectTable orm = null;

            if (_db == null)
            {
                Debug.LogError("indexed query failed with malformed setup");
            }
            else
            {
                orm = _SelectWithIdOrderByTypeAsc(id);

                if (orm == null) {
                    Debug.Log(string.Format("Item not found: {0}", id));
                } else {
                    ulong key = ((uint)unchecked((ulong)((int)orm.Id))) | ((((ulong)unchecked((ulong)((int)orm.Type)))) << 32);

                    if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                        orm = _lazyPrimaryKeyDictionary[key];
                    } else {
                        _lazyPrimaryKeyDictionary.Add(key, orm);
                    }
                }
            }

            return orm;
        }

        private SupportCardEffectTable _SelectWithIdOrderByTypeAsc(int id)
        {
            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardEffectTable_Id();
            if (query == null)
            {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectTable");
                return null;
            }

            // SELECT `type`,`init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? ORDER BY `type` ASC;
            if (!query.BindInt(1, id)) { return null; }

            SupportCardEffectTable orm = null;

            if (query.Step())
            {
                orm = _CreateOrmByQueryResultWithIdOrderByTypeAsc(query, id);
            }

            query.Reset();

            return orm;
        }

        public List<SupportCardEffectTable> GetListWithIdOrderByTypeAsc(int id)
        {
            int key = (int)id;

            if (!_dictionaryWithId.ContainsKey(key)) {
                _dictionaryWithId.Add(key, _ListSelectWithIdOrderByTypeAsc(id));
            }

            return _dictionaryWithId[key];
        }

        public List<SupportCardEffectTable> MaybeListWithIdOrderByTypeAsc(int id)
        {
            List<SupportCardEffectTable> list = GetListWithIdOrderByTypeAsc(id);
            return list.Count > 0 ? list : null;
        }

        private List<SupportCardEffectTable> _ListSelectWithIdOrderByTypeAsc(int id)
        {
            List<SupportCardEffectTable> _list = new List<SupportCardEffectTable>();

            LibNative.Sqlite3.PreparedQuery query = _db.GetSelectQueryWithIndex_SupportCardEffectTable_Id();
            if (query == null) {
                Debug.LogError("Fatal: failed to allocate prepared query for SupportCardEffectTable");
                return null;
            }

            // SELECT `type`,`init`,`limit_lv5`,`limit_lv10`,`limit_lv15`,`limit_lv20`,`limit_lv25`,`limit_lv30`,`limit_lv35`,`limit_lv40`,`limit_lv45`,`limit_lv50` FROM `support_card_effect_table` WHERE `id`=? ORDER BY `type` ASC;
            if (!query.BindInt(1, id)) { return null; }

            while (query.Step()) {
                SupportCardEffectTable orm = _CreateOrmByQueryResultWithIdOrderByTypeAsc(query, id);
                ulong key = ((uint)unchecked((ulong)((int)orm.Id))) | ((((ulong)unchecked((ulong)((int)orm.Type)))) << 32);

                if (_lazyPrimaryKeyDictionary.ContainsKey(key)) {
                    orm = _lazyPrimaryKeyDictionary[key];
                } else {
                    _lazyPrimaryKeyDictionary.Add(key, orm);
                }

                _list.Add(orm);
            }

            query.Reset();

            return _list;
        }

        private SupportCardEffectTable _CreateOrmByQueryResultWithIdOrderByTypeAsc(LibNative.Sqlite3.PreparedQuery query, int id)
        {
            int type      = (int)query.GetInt(0);
            int init      = (int)query.GetInt(1);
            int limitLv5  = (int)query.GetInt(2);
            int limitLv10 = (int)query.GetInt(3);
            int limitLv15 = (int)query.GetInt(4);
            int limitLv20 = (int)query.GetInt(5);
            int limitLv25 = (int)query.GetInt(6);
            int limitLv30 = (int)query.GetInt(7);
            int limitLv35 = (int)query.GetInt(8);
            int limitLv40 = (int)query.GetInt(9);
            int limitLv45 = (int)query.GetInt(10);
            int limitLv50 = (int)query.GetInt(11);

            return new SupportCardEffectTable(id, type, init, limitLv5, limitLv10, limitLv15, limitLv20, limitLv25, limitLv30, limitLv35, limitLv40, limitLv45, limitLv50);
        }

        public override void Unload()
        {
            _lazyPrimaryKeyDictionary.Clear();
            _dictionaryWithId.Clear();
        }

        public sealed partial class SupportCardEffectTable
        {
            /// <summary> (CSV column: id) </summary>
            public readonly int Id;
            /// <summary> (CSV column: type) </summary>
            public readonly int Type;
            /// <summary> (CSV column: init) </summary>
            public readonly int Init;
            /// <summary> (CSV column: limit_lv5) </summary>
            public readonly int LimitLv5;
            /// <summary> (CSV column: limit_lv10) </summary>
            public readonly int LimitLv10;
            /// <summary> (CSV column: limit_lv15) </summary>
            public readonly int LimitLv15;
            /// <summary> (CSV column: limit_lv20) </summary>
            public readonly int LimitLv20;
            /// <summary> (CSV column: limit_lv25) </summary>
            public readonly int LimitLv25;
            /// <summary> (CSV column: limit_lv30) </summary>
            public readonly int LimitLv30;
            /// <summary> (CSV column: limit_lv35) </summary>
            public readonly int LimitLv35;
            /// <summary> (CSV column: limit_lv40) </summary>
            public readonly int LimitLv40;
            /// <summary> (CSV column: limit_lv45) </summary>
            public readonly int LimitLv45;
            /// <summary> (CSV column: limit_lv50) </summary>
            public readonly int LimitLv50;
            /// <summary>
            /// Explicit initializer
            /// </summary>
            public SupportCardEffectTable(int id = 0, int type = 0, int init = 0, int limitLv5 = 0, int limitLv10 = 0, int limitLv15 = 0, int limitLv20 = 0, int limitLv25 = 0, int limitLv30 = 0, int limitLv35 = 0, int limitLv40 = 0, int limitLv45 = 0, int limitLv50 = 0)
            {
                this.Id        = id;
                this.Type      = type;
                this.Init      = init;
                this.LimitLv5  = limitLv5;
                this.LimitLv10 = limitLv10;
                this.LimitLv15 = limitLv15;
                this.LimitLv20 = limitLv20;
                this.LimitLv25 = limitLv25;
                this.LimitLv30 = limitLv30;
                this.LimitLv35 = limitLv35;
                this.LimitLv40 = limitLv40;
                this.LimitLv45 = limitLv45;
                this.LimitLv50 = limitLv50;
            }
        }
    }
}
#endif
