using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// レース中情報プレートUIインターフェース。
    /// </summary>
    //-------------------------------------------------------------------
    public interface IRaceUIMessagePlate
    {
        /// <summary>
        /// 初期化。
        /// </summary>
        void Init(RaceLoaderManager raceLoader, RaceDefine.RaceType raceType);
        /// <summary>
        /// 更新。予約状態のメッセージがあったら空きが出来次第出す
        /// </summary>
        void Update(float deltaTime);
        /// <summary>
        /// 表示リクエスト。
        /// </summary>
        void RequestShowMessagePlate(ref RaceUIMessagePlate.PlayDesc desc);
        /// <summary>
        /// 表示中プレートを非表示。表示待ち行列もクリア。
        /// </summary>
        void HideMessagePlateImmediately();
        /// <summary>
        /// 全プレートの描画ON/OFF。
        /// </summary>
        void SetVisible(bool isVisible);
        /// <summary>
        /// 描画ON/OFF取得。
        /// </summary>
        bool IsVisible();
        /// <summary>
        /// ポーズ。
        /// </summary>
        void Pause();
        /// <summary>
        /// 再開。
        /// </summary>
        void Resume();
        /// <summary>
        /// 再生速度設定。
        /// </summary>
        void SetSpeed(float speed);
    }

}

