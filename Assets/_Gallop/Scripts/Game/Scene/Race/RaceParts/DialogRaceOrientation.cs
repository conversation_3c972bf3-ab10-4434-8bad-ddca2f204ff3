using System.Linq;
using UnityEngine;

namespace Gallop
{
    public class DialogRaceOrientation : DialogInnerBase
    {
        #region Const
        /// <summary> フッターテキストのy軸上のオフセット </summary>
        private const int FOOTER_TEXT_OFFSET_Y = 6;
        
        /// <summary> ダイアログサイズごとのオフセット </summary>
        private const int HEADER_TEXT_OFFSET_Y_FOR_SMALL = -36;
        private const int HEADER_TEXT_OFFSET_Y_FOR_MIDDLE = -24;
        private const int TOGGLE_OFFSET_Y_FOR_SMALL = -100;
        private const int TOGGLE_OFFSET_Y_FOR_MIDDLE = -84;
        
        /// <summary> ToggleGroup内のToggle Index </summary>
        private const int TOGGLE_INDEX_LANDSCAPE = 0;           // 通常横持ち
        private const int TOGGLE_INDEX_PORTRAIT = 1;            // 通常縦持ち
        private const int TOGGLE_INDEX_PORTRAIT_DYNAMIC = 2;     // ダイナミックカメラ縦持ち
        private const int TOGGLE_INDEX_NUM = TOGGLE_INDEX_PORTRAIT_DYNAMIC + 1;  // numなのでindex+1
        #endregion Const

        #region SerializeField
        
        // UIオフセット調整用Root
        [SerializeField]
        private RectTransform _headerTextRoot = null;
        [SerializeField]
        private RectTransform _toggleRoot = null;

        // 全カメラを含むToggleGroup
        [SerializeField]
        protected ToggleGroupCommon _toggleGroupAll = null;

        [SerializeField] 
        private ToggleCommon _toggleEnableRaceOrientationPopup = null;
        /// <summary> 注釈文 </summary>
        [SerializeField]
        private TextCommon _warningText = null;
        
        #endregion SerializeField

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;     // コードによって変更されることがある
        }

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        #endregion DialogInnerBase

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// このダイアログは開くまでに遅延が発生するので呼び出し側は注意すること
        /// </summary>
        public static void Open(System.Action onSelected, System.Action<DialogCommon> onCancel, bool isSpecialUnLockRace, RaceInfo raceInfo)
        {
            // 時限解放チェック
            GameDefine.UpdateEnableDynamicCamera();
            
            const string PATH = ResourcePath.DIALOG_CHANGE_RACE_ORIENTATION;
            var dialogObj = Instantiate(ResourceManager.LoadOnView<GameObject>(PATH));
            var dialogContent = dialogObj.GetComponent<DialogRaceOrientation>();
            var dialogData = dialogContent.CreateDialogData();
            // 時限解放前後でダイアログの大きさを変更する
            dialogData.FormType = GameDefine.IS_ENABLE_DYNAMIC_CAMERA
                ? DialogCommonBase.FormType.MIDDLE_TWO_BUTTON
                : DialogCommonBase.FormType.SMALL_TWO_BUTTON;
            
            dialogContent.SetupAndOpen(dialogData, onSelected, onCancel, isSpecialUnLockRace, raceInfo);
        }

        /// <summary>
        /// ダイアログの中身を設定して開く
        /// </summary>
        private void SetupAndOpen(DialogCommon.Data dialogData, System.Action onSelected, System.Action<DialogCommon> onCancel, bool isSpecialUnLockRace, RaceInfo raceInfo)
        {
            // UIオフセット調整
            var headerTextOffsetY = GameDefine.IS_ENABLE_DYNAMIC_CAMERA
                ? HEADER_TEXT_OFFSET_Y_FOR_MIDDLE
                : HEADER_TEXT_OFFSET_Y_FOR_SMALL;
            _headerTextRoot.anchoredPosition = new Vector2(_headerTextRoot.anchoredPosition.x, headerTextOffsetY);
            var toggleOffsetY = GameDefine.IS_ENABLE_DYNAMIC_CAMERA
                ? TOGGLE_OFFSET_Y_FOR_MIDDLE
                : TOGGLE_OFFSET_Y_FOR_SMALL;
            _toggleRoot.anchoredPosition = new Vector2(_toggleRoot.anchoredPosition.x, toggleOffsetY);
            
            var saveLoader = SaveDataManager.Instance.SaveLoader;
            bool isLandscape = saveLoader.IsRaceLandscape;
            bool isDynamicCamera = saveLoader.IsTryRaceDynamicCamera && GameDefine.IS_ENABLE_DYNAMIC_CAMERA;   // 機能解放チェック

            _toggleEnableRaceOrientationPopup.SetActiveWithCheck(!isSpecialUnLockRace);
            _warningText.SetActiveWithCheck(!isSpecialUnLockRace);
            if (isSpecialUnLockRace)
            {
                dialogData.IsFooterNotificationText = true;
                dialogData.FooterText = TextId.Story467001.Text();
                // 特殊なアンロックレースに関しては個別で縦横設定を保持している
                isLandscape = saveLoader.IsSpecialUnlockRaceLandscape;
            }
            
            // ToggleGroup出し分け
            bool enableDynamic = RaceUtil.IsEnableDynamicRaceType(raceInfo.RaceType);
            if (_toggleGroupAll.ToggleArray.Length != TOGGLE_INDEX_NUM)
            {
                Debug.LogError("Toggleの数が一致していません　設定数を確認してください");
                return;
            }
            ToggleCommon toggleOrientationLandscape = _toggleGroupAll.ToggleArray[TOGGLE_INDEX_LANDSCAPE];
            ToggleCommon toggleOrientationPortrait = _toggleGroupAll.ToggleArray[TOGGLE_INDEX_PORTRAIT];
            ToggleCommon toggleOrientationPortraitDynamicCamera = _toggleGroupAll.ToggleArray[TOGGLE_INDEX_PORTRAIT_DYNAMIC];
            
            // 機能解放チェック
            if (!GameDefine.IS_ENABLE_DYNAMIC_CAMERA)
            {
                toggleOrientationPortraitDynamicCamera.SetActiveWithCheck(false);
            }
            
            // 現在の状態に合わせてトグルを更新
            toggleOrientationLandscape.isOn = isLandscape && (!enableDynamic || !isDynamicCamera);
            toggleOrientationPortrait.isOn = !isLandscape && (!enableDynamic || !isDynamicCamera);
            if (enableDynamic) { toggleOrientationPortraitDynamicCamera.isOn = isDynamicCamera; }
            // ダイナミックカメラ用のトグルを操作できるか？
            toggleOrientationPortraitDynamicCamera.SetInteractable(enableDynamic);
            var notificationMessage = enableDynamic ? string.Empty : TextId.Race9511009.Text();
            toggleOrientationPortraitDynamicCamera.SetNotificationMessage(notificationMessage);
            toggleOrientationPortraitDynamicCamera.IsBeep = !enableDynamic;

            dialogData.Title = TextId.Race0678.Text();
            dialogData.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = (_) =>
            {
                // 選択された向きに変更
                // 場合によってはトグルを非表示にすることもあるので非表示の時に裏で勝手にセーブされないよう調整
                if (toggleOrientationLandscape.isActiveAndEnabled)
                {
                    // 特殊なアンロックレースに関しては個別で縦横設定を保持している
                    if (isSpecialUnLockRace)
                    {
                        saveLoader.IsSpecialUnlockRaceLandscape = toggleOrientationLandscape.isOn;
                    }
                    else
                    {
                        saveLoader.IsRaceLandscape = toggleOrientationLandscape.isOn;
                    }
                }
                
                // ダイナミックカメラ再生設定
                if (enableDynamic && toggleOrientationPortraitDynamicCamera.isActiveAndEnabled)
                {
                    saveLoader.IsTryRaceDynamicCamera = toggleOrientationPortraitDynamicCamera.isOn;
                }

                if (_toggleEnableRaceOrientationPopup.isActiveAndEnabled)
                {
                    // ダイナミックカメラ機能リリースのタイミングでセーブデータを新規にする
                    if (GameDefine.IS_ENABLE_DYNAMIC_CAMERA)
                    {
                        saveLoader.IsEnableRaceOrientationPopupVer2 = !_toggleEnableRaceOrientationPopup.isOn;
                    }
                    else
                    {
                        saveLoader.IsEnableRaceOrientationPopup = !_toggleEnableRaceOrientationPopup.isOn;
                    }
                }

                SaveDataManager.Instance.Save();
                
                // ダイナミックカメラの場合は視点選択ダイアログを挟む
                if (enableDynamic && toggleOrientationPortraitDynamicCamera.isOn)
                {
                    // 他人視点を許可しないことがあるので、isNeedRestrictChara=true
                    DialogRaceDynamicCameraSetting.Open(raceInfo.RaceHorse.ToList(), raceInfo, true, onSelected,
                        () => onCancel.Invoke(null));
                }
                else
                {
                    // ダイナミックカメラではないので、LoHエクストラ以外では視点設定を切っておく必要がある
                    if (!raceInfo.IsForceSoloModeRace)
                    {
                        RaceManager.RaceInfo.ResetOverridePlayerHorseIndex();
                    }
                    onSelected?.Invoke();
                }
            };
            dialogData.LeftButtonCallBack = onCancel;


            var dialog = DialogManager.PushDialog(dialogData);
            if (isSpecialUnLockRace)
            {
                dialog.AddFooterTextPosY(FOOTER_TEXT_OFFSET_Y);
            }
            
            // 追加のコールバック設定
            SetToggleAdditionalCallback(dialog);

            ChangeDialogSe(dialog, enableDynamic && toggleOrientationPortraitDynamicCamera.isOn);
        }

        /// <summary>
        /// Toggleに追加のコールバックを設定する（SE変更など）
        /// </summary>
        private void SetToggleAdditionalCallback(DialogCommon dialog)
        {
            // _toggleGroupAllにはダイナミックカメラ設定が含まれているので、SE変更の処理を入れる
            // _toggleGroupWithoutDynamicは一旦不要
            for (int i = 0; i < _toggleGroupAll.ToggleArray.Length; i++)
            {
                // ダイナミックカメラ設定なら、さらにダイアログを開くのでSEを変える必要がある
                bool isOpenAnotherDialog = i == TOGGLE_INDEX_PORTRAIT_DYNAMIC;
                _toggleGroupAll.ToggleArray[i].AddOnValueChangeIsOn(() => ChangeDialogSe(dialog, isOpenAnotherDialog));
            }
        }

        /// <summary>
        /// トグルの状態によってSEを変える
        /// </summary>
        private void ChangeDialogSe(DialogCommon dialog, bool isOpenAnotherDialog)
        {
            ButtonCommon.ButtonSeType seType = ButtonCommon.ButtonSeType.DecideL02;
            if (isOpenAnotherDialog)
            {
                seType = ButtonCommon.ButtonSeType.DecideM02;
            }
            
            // SEの変更
            dialog.SetButtonSeType(DialogCommon.ButtonIndex.Right, seType);
        }

        #endregion Method
    }
}
