using System.Collections;
using Cute.Core;
using Cute.UI;
using UnityEngine;
using DG.Tweening;
using static Gallop.StaticVariableDefine.Race.RaceEntryTableButton;

namespace Gallop
{
    /// <summary>
    /// 出走表UIでキャラ１体の短冊制御。
    /// </summary>
    [AddComponentMenu("")]
    public class RaceEntryTableButton : MonoBehaviour
    {
        /// <summary>
        /// 枠番の位置（出走表）
        /// </summary>
        private const float GATE_NIMBER_POS_X_ENTRY = -114.0f;

        /// <summary>
        /// 枠番の位置（やる気）
        /// </summary>
        private const float GATE_NIMBER_POS_X_MOTIVATION = -270.0f;

        /// <summary>
        /// キャラ名などの位置（出走表）
        /// </summary>
        private const float CHARAINFOROOT_POS_X_ENTRY = 41.0f;

        /// <summary>
        /// キャラ名などの位置（やる気）
        /// </summary>
        private const float CHARAINFOROOT_POS_X_MOTIVATION = -164.0f;

        [Header("Header")]
        [SerializeField]
        private ImageCommon _postNumberImage = null;

        [Header("Character")]
        [SerializeField]
        private ImageCommon _bg = null;
        [SerializeField]
        private RectTransform _charaInfoRoot = null;
        [SerializeField]
        private TextCommon _charaName = null;
        [SerializeField]
        private PartsNickNameRibbon _nickNameRibbon = null;
        [SerializeField]
        private TextCommon _userName = null;
        [SerializeField]
        private RawImageCommon _charaImage = null;
        [SerializeField]
        private CharacterButton _characterButton = null;
        [SerializeField]
        private RawImageCommon _mobCharaImage = null;

        [Header("Popularity")]
        [SerializeField]
        private ImageCommon[] _popularityIcons = null;

        [SerializeField]
        private TextCommon _popularityText = null;

        [Header("RunStyle")]
        [SerializeField]
        private TextCommon _runStyle = null;

        [Header("Other")]
        /// <summary> ウマいねアイコン </summary>
        [SerializeField]
        private ImageCommon _umaineIcon = null;

        [SerializeField]
        private RaceNumberIcon _gateNumber = null;


        [SerializeField]
        private FadeScalePanel _fadeScaler = null;

        private RaceEntryTablePanel.RaceTableData _data = null;

        public RaceEntryTablePanel.RaceTableData Data => _data;

        public System.Action<RaceEntryTablePanel.RaceTableData> onClick = null;

        /// <summary>
        /// 参照
        /// </summary>
        RaceEntryTablePanel _panel = null;

        #region やる気

        /// <summary>
        /// アイコンの置く位置
        /// </summary>
        private const string MOTIVATION_ICON_LOCATOR = "OBJ_loc_ico_motivation00";

        private const string TARGET_PLANE_NAME = "PLN_dum_icon_motivation00";
        private const string TARGET_PLANE_PARENT_OBJECT_NAME = "OBJ_mc_dum_icon_motivation00";

        /// <summary>
        /// やる気Flashのルートオブジェ
        /// </summary>
        private GameObject _flashRoot = null;

        /// <summary>
        /// やる気演出フラッシュ
        /// </summary>
        private FlashPlayer _motivationPlayer = null;

        /// <summary>
        /// やる気アイコンプレハブ
        /// </summary>
        private FlashPlayer _motivationIconPlayer = null;

        /// <summary>
        /// やる気アイコンのモーション
        /// </summary>
        private AnimateToUnity.AnMotion _motivationIconMotion = null;

        /// <summary>
        /// アニメーションのシーケンス
        /// </summary>
        private Sequence _seq = null;


        #endregion やる気

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="isVisibleUserName"></param>
        /// <param name="data"></param>
        /// <param name="panel"></param>
        private void SetupCommon(bool isVisibleUserName,
            RaceEntryTablePanel.RaceTableData data,
            RaceEntryTablePanel panel)
        {
            _data = data;
            _panel = panel;

            var raceCommonAtlas = ResourceManager.LoadOnView<AtlasReference>(ResourcePath.GetAtlasPath(TargetAtlasType.RaceCommon));

            // 枠番カラースプライトの設定
            _postNumberImage.sprite = GetPostNumberSprite(_data.Data.postNumber);
            
            _charaName.text = _data.CharaName;

            bool isNickNameLandscape = RaceUtil.IsRaceSceneLandscape();
            _nickNameRibbon.Initialize(_data.Data.GetCharaNickNameMaster(), isNickNameLandscape);

            // #101552 縦書き二つ名の英数字の位置・サイズ調整。
            if (isNickNameLandscape)
            {
                var rotateTextSync = _nickNameRibbon.Label.gameObject.GetComponent<RotateTextSizeSync>();
                if (rotateTextSync == null)
                {
                    rotateTextSync = _nickNameRibbon.Label.gameObject.AddComponent<RotateTextSizeSync>();
                }
                // テキストは二つ名下地より少し小さめの領域に収める。※下部の < の部分にテキストを描きたくないため。
                rotateTextSync.TextWidthMax = _nickNameRibbon.GetComponent<RectTransform>().sizeDelta.y - 40;
                rotateTextSync.TextDefaultY = _nickNameRibbon.Label.transform.localPosition.y;
            }
            
            _userName.gameObject.SetActive(isVisibleUserName && _data.UserName != null);
            _userName.text = _data.UserName;
            if(_data.UserNameColor != FontColorType.None)
            {
                _userName.FontColor = _data.UserNameColor;
            }

            if(_data.UserNameOutlineColor != OutlineColorType.None)
            {
                _userName.OutlineColor = _data.UserNameOutlineColor;
            }

            _userName.OutlineSize = _data.UserNameOutLineSize;

            if (_data.UserNameFontSize != TextFormat.FontSize.None)
            {
                _userName.FontSizeFormat = _data.UserNameFontSize;
            }
            //念のためユーザー名やチーム名表示のテキストに更新をかける
            _userName.UpdateColor();
            _userName.OnUpdate();

            SetupCharaNameAnchorPos();

            _gateNumber.SetNumberData(_data.Index - 1, RaceManager.RaceInfo.NumRaceHorses);

            for (int i = 0; i < 3; ++i)
            {
                _popularityIcons[i].sprite = RaceUtil.GetPopularityIcon(raceCommonAtlas, _data.PopularityIconArray[i]);
            }
            _popularityText.text = string.Format(TextId.Race0140.Text(), data.Popularity);
            _runStyle.text = RaceUtil.GetRunnignStyleText((RaceDefine.RunningStyle)data.RunningStyle);

            if (data.IsPlayer)
            {
                _bg.sprite = UIManager.PreInAtlas.GetSprite("utx_frm_list_base_03_sl");
            }

            ChangeRunStyle((RaceDefine.RunningStyle)data.RunningStyle);

            // ウマいねアイコン表示
            _umaineIcon.SetActiveWithCheck(data.IsUmaine);
        }

        private void SetupCharaNameAnchorPos()
        {
            if (!TempData.Instance.RaceData.IsRaceLandscape)
            {
                return;
            }
            
            if (_nickNameRibbon.gameObject.activeSelf && !_userName.gameObject.activeSelf)
            {
                _charaName.rectTransform.anchoredPosition = CHARA_NAME_POS_1;
                (_nickNameRibbon.transform as RectTransform).anchoredPosition = NICKNAME_POS_1;
            }
            else if (!_nickNameRibbon.gameObject.activeSelf && _userName.gameObject.activeSelf)
            {
                _userName.rectTransform.anchoredPosition = TRAINER_NAME_POS_2;
                _charaName.rectTransform.anchoredPosition = CHARA_NAME_POS_2;
            }
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="isVisibleUserName"></param>
        /// <param name="data"></param>
        /// <param name="panel"></param>
        public void Setup(bool isVisibleUserName,
            RaceEntryTablePanel.RaceTableData data,
            RaceEntryTablePanel panel)
        {
            SetupCommon(isVisibleUserName, data, panel);

            if (ModelLoader.IsMob(_data.CharaId))
            {
                _charaImage.gameObject.SetActive(false);
                if (panel.MobCaptureRenderTextureDic.TryGetValue(_data.Index, out var texture))
                {
                    _mobCharaImage.gameObject.SetActive(true);
                    _mobCharaImage.texture = texture;
                }
                else
                {
                    _mobCharaImage.gameObject.SetActive(false);
                }
            }
            else
            {
                _mobCharaImage.gameObject.SetActive(false);
                _charaImage.gameObject.SetActive(true);
                string path = ResourcePath.GetCharaStandMediumImagePath(_data.CharaId, _data.Data.RaceDressId);
                _charaImage.texture = ResourceManager.LoadOnView<Texture2D>(path);
            }
            
            if (_characterButton != null)
            {
                _characterButton.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// 表示準備(やる気)
        /// </summary>
        /// <param name="isVisibleUserName"></param>
        /// <param name="data"></param>
        /// <param name="panel"></param>
        /// <param name="motivationAnimeObj"></param>
        /// <param name="motivationIconObj"></param>
        public void SetupMotivation(bool isVisibleUserName,
            RaceEntryTablePanel.RaceTableData data,
            RaceEntryTablePanel panel,
            GameObject motivationAnimeObj,
            GameObject motivationIconObj)
        {
            SetupCommon(isVisibleUserName, data, panel);

            _flashRoot = new GameObject("FlashRoot", typeof(RectTransform));
            FlashToUgui ftoUObj = _flashRoot.AddComponent<FlashToUgui>();
            ftoUObj.transform.SetParent(transform);
            ftoUObj.transform.localPosition = Math.VECTOR3_ZERO;
            ftoUObj.transform.localScale = Math.VECTOR3_ONE;
            ftoUObj.transform.localRotation = Math.QUATERNION_IDENTITY;

            //育成中は殿堂入りではないのでアイコンの種類を変える
            var raceType = RaceManager.RaceInfo.RaceType;
            CharacterButtonInfo buttonInfo = new CharacterButtonInfo();
            if (data.CharaId == ModelLoader.MOB_CHARA_ID)
            {
                buttonInfo.Id = data.MobId;
                buttonInfo.IdType = CharacterButtonInfo.IdTypeEnum.MobChara;
                buttonInfo.ColorType = data.ColorType;
                if (raceType == RaceDefine.RaceType.Single)
                {
                    buttonInfo.IconImageType = CharacterButtonInfo.IconImageTypeEnum.Card;
                }
                else
                {
                    buttonInfo.IconImageType = CharacterButtonInfo.IconImageTypeEnum.Trained;
                }
            }
            else
            {
                if (raceType == RaceDefine.RaceType.Single)
                {
                    buttonInfo.IdType = CharacterButtonInfo.IdTypeEnum.Card;
                    buttonInfo.Id = data.Data.CardId;
                }
                //自分のウマ娘でないなら殿堂入りではないのでアイコンの種類を変える
                else if (!data.IsPlayer)
                {
                    buttonInfo.IdType = CharacterButtonInfo.IdTypeEnum.Chara;
                    buttonInfo.Id = data.Data.charaId;
                }
                // #86602 アンロックレースかつ特殊レースの自キャラのアイコンはchara_idで表示する。MainStoryRaceGetRaceTableResponseでcard_idを受け取っておらず、プレイアブルではないため殿堂入りウマ娘のデータも無いから。
                else if (data.IsPlayer && raceType == RaceDefine.RaceType.StoryCondition && RaceManager.RaceInfo.MainStoryRaceGimmickType == MainStoryDefine.RaceGimmickType.Special_00)
                {
                    buttonInfo.IdType = CharacterButtonInfo.IdTypeEnum.Chara;
                    buttonInfo.Id = data.Data.charaId;
                }
                //自分の殿堂入りウマ娘
                else
                {
                    buttonInfo.IdType = CharacterButtonInfo.IdTypeEnum.Trained;
                    buttonInfo.Id = data.Data.CardId;
                }
                buttonInfo.Level = data.Data.TalentLevel;
            }
            buttonInfo.DressId = data.DressId;
            buttonInfo.Rarity = data.Rarity;
            buttonInfo.RankImage = true;
            buttonInfo.FinalTrainingRank = (GameDefine.FinalTrainingRank)data.Data.FinalGrade;
            buttonInfo.EnableButton = false;

            if (_characterButton != null)
            {
                _characterButton.Setup(buttonInfo);
            }
            _charaImage.SetActiveWithCheck(false);
            _mobCharaImage.SetActiveWithCheck(false);

            Vector3 pos = _charaInfoRoot.localPosition;
            pos.x = CHARAINFOROOT_POS_X_MOTIVATION;
            _charaInfoRoot.localPosition = pos;

            _gateNumber.SetPos(GATE_NIMBER_POS_X_MOTIVATION);

            _motivationPlayer = Instantiate<GameObject>(motivationAnimeObj, ftoUObj.transform).AddComponent<FlashPlayer>();
            _motivationPlayer.Init();
            _motivationPlayer.SortOffset = 10;

            // アイコン置き場
            AnimateToUnity.AnObject locator = _motivationPlayer.GetObj(MOTIVATION_ICON_LOCATOR);
            
            _motivationIconPlayer = Instantiate<GameObject>(motivationIconObj, locator.OffsetObject.transform).AddComponent<FlashPlayer>();
            _motivationIconPlayer.Init();
            _motivationIconPlayer.SortOffset = 20;
            _motivationIconMotion = _motivationIconPlayer.GetMotion("MOT_mc_icon_motivation");
            _motivationIconPlayer.transform.localScale = Math.VECTOR3_ONE * 0.85f; // 85%縮小

            ftoUObj.Target = _motivationPlayer.gameObject;
            ftoUObj.IsVisibleTargetMesh = false;
            ftoUObj.CreateClone();

        }

        /// <summary>
        /// 枠番から枠番カラースプライト取得。
        /// </summary>
        /// <param name="postNumber">枠番。1~。</param>
        private Sprite GetPostNumberSprite(int postNumber)
        {
            var raceAtlas = UIManager.Instance.LoadAtlas(TargetAtlasType.Race);
            var spriteName = $"utx_frm_racelist_bracket_{postNumber-1:00}";
            return raceAtlas.GetSprite(spriteName);
        }

        public void PlayEnter()
        {
            if( _motivationIconPlayer != null )
            {
                // やる気演出の時は一部演出を省く
                _fadeScaler.SetDisableMainContents("PostNumberImage");
            }

            _fadeScaler.PlayEnter();

            if(_panel.IsSkip )
            {
                Skip();
            }
        }

        /// <summary>
        /// ハケアニメ再生
        /// </summary>
        public void PlayExit()
        {
            if (_flashRoot != null)
            {
                // やる気Flashにもプレートのハケアニメを適用するため親を変更
                _flashRoot.transform.SetParent(_bg.transform);
            }

            // プレートのハケアニメ再生
            _fadeScaler.PlayExit();
        }

        public void Skip()
        {
            if (_fadeScaler != null)
            {
                _fadeScaler.Skip();
            }
        }

        public void ChangeRunStyle(RaceDefine.RunningStyle runStyle)
        {
            _data.RunningStyle = (int)runStyle;
            _runStyle.text = RaceUtil.GetRunnignStyleText(runStyle);
        }


        /// <summary>
        /// やる気アイコン登場演出
        /// </summary>
        public void PlayMotivation(bool isSkip = false, bool isCallSe = true)
        {
            RectTransform rt = GetComponent<RectTransform>();
            switch (_data.Data.Motivation)
            {
                case RaceDefine.Motivation.Max:
                    _seq = DOTween.Sequence().SetDelay(0.1f);
                    _seq.Append(rt.DOScale(0.97f, 0.0f));
                    _seq.Join(rt.DOScale(1.0f, 0.1f).SetEase(Ease.InSine));
                    if (isSkip)
                    {
                        _motivationPlayer.Play("skip_best_motivation");
                        _seq.Complete(true);
                    }
                    else
                    {
                        _motivationPlayer.Play("in_best_motivation");
                    }
                    _motivationIconPlayer.Play("in_end");
                    break;

                default:
                    _seq = DOTween.Sequence().SetDelay(0.1f);
                    _seq.Append(rt.DOScale(0.98f, 0.0f));
                    _seq.Join(rt.DOScale(1.0f, 0.1f).SetEase(Ease.InSine));
                    if (isSkip)
                    {
                        _motivationPlayer.Play("skip_motivation");
                        _seq.Complete(true);
                    }
                    else
                    {
                        _motivationPlayer.Play("in_motivation");
                    }
                    _motivationIconPlayer.Play("in_end");
                    
                    break;
            }

            var motionName = $"stop_motivation_{(int)_data.Data.Motivation - 1:00}";
            _motivationIconMotion.SetMotionPlay(motionName);
            
            // SE再生。
            if (isCallSe)
            {
                AudioManager.Instance.PlaySe(AudioId.SFX_PADDOCK_MOTIVATION_DISP);
            }
        }

        /// <summary>
        /// 自分のやる気強調表示
        /// </summary>
        public void PlayMyMotivation()
        {
            AudioManager.Instance.PlaySe(AudioId.SFX_RACE_MY_CONDITION);
            switch (_data.Data.Motivation)
            {
                case RaceDefine.Motivation.Max:
                    _motivationPlayer.Play("in_best_player");
                    break;

                default:
                    _motivationPlayer.Play("in_player");
                    break;
            }
        }

        /// <summary>
        /// レーススタート（=リスト表示終了）時に呼ぶ
        /// </summary>
        public void OnRemove(float time)
        {
            if (_motivationIconPlayer != null)
            {
                // やる気演出もついている時はそっちも消す
                StartCoroutine(FadeFlashCoroutine(time));
            }
        }

        /// <summary>
        /// キャラのテクスチャを解放する
        /// </summary>
        public void ReleaseTexture()
        {
            if (_mobCharaImage != null)
            {
                _mobCharaImage.texture = null;
            }
        }

        /// <summary>
        /// やる気アイコンのαフェード
        /// フラッシュなので自前で消してみた。
        /// </summary>
        /// <param name="time"></param>
        /// <returns></returns>
        private IEnumerator FadeFlashCoroutine( float time )
        {
            Color color = Color.white;
            float max = time;

            while (time > 0.0f)
            {
                color.a = time / max;

                _motivationIconPlayer.SetColor(color);
                _motivationPlayer.SetColor(color);

                time -= Time.deltaTime;

                yield return null;
            }

            color.a = 0.0f;

            _motivationIconPlayer.SetColor(color);
            _motivationPlayer.SetColor(color);
        }

        private void OnDestroy()
        {
            if (_seq != null)
            {
                if (_seq.IsPlaying())
                {
                    _seq.Kill();
                }

                _seq = null;
            }
        }

#if CYG_DEBUG
        public void DbgSetVisible(bool isVisible)
        {
            _bg.gameObject.SetActive(isVisible);
        }
#endif

#if UNITY_EDITOR
        public void ReloadMobCapture()
        {
            if (ModelLoader.IsMob(_data.CharaId))
            {
                _charaImage.gameObject.SetActive(false);
                if (_panel.MobCaptureRenderTextureDic.TryGetValue(_data.Index, out var texture))
                {
                    _mobCharaImage.gameObject.SetActive(true);
                    _mobCharaImage.texture = texture;
                }
                else
                {
                    _mobCharaImage.gameObject.SetActive(false);
                }
            }
        }
#endif

    }
}
