using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using UnityEngine.Rendering;

namespace Gallop
{
    public class RaceEntryTablePanelLandscape : RaceEntryTablePanel
    {
        private const float START_BUTTON_LANDSCAPE_Y = 77.7f;
        private const int PAGE_MAX_NUM_LANDSCAPE = 8;
        
        protected override int PageMaxNum => PAGE_MAX_NUM_LANDSCAPE; 

        protected override bool IsLandscape => true;
        
        protected override void SetScrollNormalizedPosition(float pos)
        {
            _scroll.horizontalNormalizedPosition = pos;
        }
        protected override float GetScrollNormalizedPosition()
        {
            return _scroll.horizontalNormalizedPosition;
        }
        protected override void SetScrollEnable(bool isEnable)
        {
            _scroll.horizontal = isEnable;
        }
        
        protected override Scrollbar GetScrollBar()
        {
            return _scroll.horizontalScrollbar;
        }
        
        protected override float StartButtonPosY => START_BUTTON_LANDSCAPE_Y;

        protected override void InstantiateButton(RaceTableData[] dataArray, BuildInfo build)
        {
            var isVisibleUserName = build.NameType == NameType.Multi;
            var count = dataArray.Length;
            for (int i = 0; i < count; ++i)
            {
                var horse = dataArray[i];
                var newButton = Instantiate(_button.gameObject, _buttonRoot)
                    .GetComponent<RaceEntryTableButton>();

                //対抗戦チームレースの場合チーム名の色を指定する
                if(_raceType == RaceDefine.RaceType.SingleModeScenarioTeamRace)
                {
                    SetupTeamRaceTeamNameColor(horse);
                }
                else if(_raceType == RaceDefine.RaceType.TeamBuilding)
                {
                    SetupTeamBuildingTeamNameText(horse);
                }
                
                newButton.Setup(isVisibleUserName, horse, this);
                newButton.onClick = build.OnClick;
                
                // 馬番１のキャラが一番右に来るようにしたいので。
                newButton.transform.SetAsFirstSibling();
                
                _buttons.Add(newButton);
            }
            _button.gameObject.SetActive(false);
        }
     
        protected override Tween CreateEnterLargeScrollTween(DOTweenAnimation animation)
        {
            return _scroll
                .DOHorizontalNormalizedPos(0, _buttons.Count * animation.duration)
                .SetDOTweenAnimation(animation)
                .OnComplete(() => _scroll.movementType = ScrollRect.MovementType.Elastic);
        }
        protected override Tween CreateEnterLargePlayerScrollTween(float rate, DOTweenAnimation animation)
        {
            return _scroll
                .DOHorizontalNormalizedPos(rate, animation.duration)
                .SetDOTweenAnimation(animation);
        }
        
        protected override void SetupEnterSmallLayout()
        {
            _buttonRootContentSizeFitter.enabled = false;
            _buttonRootVerticalLayout.enabled = true;
            _buttonRoot.offsetMax = Math.VECTOR2_ZERO;
        }

        protected override void InitScrollViewFade()
        {
        }
        
        protected override int CalcVisibleStartIndex()
        {
            if (_buttons.Count <= 0)
            {
                return 0;
            }
            
            var layout = _buttonRootVerticalLayout;
            
            float itemWidth = (_buttons[0].transform as RectTransform).sizeDelta.x;
            float space = layout.spacing;
            var padding = layout.padding;

            // リストの右部からスクロールした量を計算。
            float contentWidth = _scroll.content.rect.width;
            float viewportWidth = _scroll.viewport.rect.width;
            float scrollNoramlizedFromTop = (1 - GetScrollNormalizedPosition());
            float scrolledWidth = (contentWidth - viewportWidth) * scrollNoramlizedFromTop;

            // 描画範囲内に最初に入る要素インデックス。
            int startIndex = (int)System.Math.Truncate((scrolledWidth - padding.right) / (itemWidth + space));

            return startIndex;
        }
    }
}
