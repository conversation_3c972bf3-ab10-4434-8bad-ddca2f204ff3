using UnityEngine;
using System;

namespace Gallop
{
    /// <summary>
    /// エキシビション：レース一覧リストアイテム
    /// </summary>
    [AddComponentMenu("")]
    public class PartsExhibitionRaceSelectRaceListItem : MonoBehaviour
    {
        #region SerializeField

        /// <summary> ボタン </summary>
        [SerializeField]
        private ButtonCommon _button;

        /// <summary> サムネ </summary>
        [SerializeField]
        private RawImageCommon _raceImage;

        /// <summary> カーソル </summary>
        [SerializeField]
        private CursorCommon _cursor;

        /// <summary> キャンバスグループ </summary>
        [SerializeField]
        private CanvasGroup _canvasGroup;

        #endregion

        #region Member

        public int RaceId { get; private set; }

        /// <summary>
        /// アニメーションさせるオブジェクト（ルートがLayoutGroupで位置制御されているため）
        /// </summary>
        public GameObject AnimationRoot => _button.gameObject;

        #endregion

        #region Method

        /// <summary>
        /// 更新処理
        /// </summary>
        /// <param name="raceData"></param>
        /// <param name="onClick"></param>
        /// <param name="viewId"></param>
        /// <param name="resourceHash"></param>
        public void UpdateItem(MasterRace.Race raceData, Action<int> onClick, SceneDefine.ViewId viewId, ResourceManager.ResourceHash resourceHash = ResourceManager.ResourceHash.InvalidHash)
        {
            RaceId = raceData.Id;

            // UI
            var path = ResourcePath.GetRaceThumbPath(raceData.ThumbnailId);
            if (resourceHash != ResourceManager.ResourceHash.InvalidHash)
            {
                _raceImage.texture = ResourceManager.LoadOnHash<Texture2D>(path, resourceHash);
            }
            else
            {
                _raceImage.texture = ResourceManager.LoadOnView<Texture2D>(path, viewId);
            }

            _button.SetOnClick(() => onClick.Invoke(RaceId));
        }

        /// <summary>
        /// カーソルの可視不可視
        /// </summary>
        /// <param name="isActive"></param>
        public void SetCursorActive(bool isActive)
        {
            _cursor.SetActiveWithCheck(isActive);
        }

        /// <summary>
        /// 透明度をリセット
        /// </summary>
        public void ResetAlpha()
        {
            _canvasGroup.alpha = 1.0f;
        }

        #endregion
    }
}
