using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 左下0、右上1で正規化した座標で位置指定可能な平面
    /// </summary>
    public class NormalizeFitPlane : MonoBehaviour
    {
        /// <summary>
        /// 正規化空間に配置された子要素情報
        /// </summary>
        public class ChildInfo
        {

            //トランスフォームへのキャッシュ
            public Transform ChildTrans
            {
                get
                {
                    if (_cachedTrans == null)
                        _cachedTrans = ChildObject.transform;
                    return _cachedTrans;
                }
            }
            private Transform _cachedTrans;

            public Vector2 NormalizedPos;
            public GameObject ChildObject;

            //このフラグが立ってないとビルドは実行しない
            public bool BuildFlag = true;

            //以下モデル管理用フィールド
            //ビルドは後から行えるように各モデルのプロパティはモデルから切り離してある
            public SimpleModelController Model;
            public CharacterBuildInfo ModelInfo;

            //モデルのルートオブジェクト
            //モデル自身に姿勢を入れるとCySpringが暴れてしまうため、１つオブジェクトを噛ます
            private Transform _modelRoot;

            public bool ModelVisible
            {
                get => _modelVisible;
                set { if (Model != null) Model.SetVisible(value); _modelVisible = value; }
            }
            private bool _modelVisible;

            /// <summary>
            /// モデルのLightProbeColorとEmissiveColorを設定する
            /// </summary>
            public void SetModelColor(in Color lightProbeColor, in Color emissiveColor)
            {
                // おまかせ編成や編成呼び出しをするとModelが作成されるよりも前にこの関数が実行される
                // 指定されたColorをキャッシュしてInitializeModelのタイミングで反映できるようにする
                _lightProbeColor = lightProbeColor;
                _emissiveColor = emissiveColor;

                if (Model == null) return;

                // #124001対応: EmissiveColorも更新しておかないとメインキャラを暗くした時にEmissiveが光って見えてしまう
                Model.SetEmissiveColor(emissiveColor);

                // この内部でApplyMaterialPropertyBlock関数も実行されるのでEmissiveColor設定後に呼び出す
                Model.SetLightProbeColor(lightProbeColor);
            }

            /// <summary>
            /// デフォルトカラーを設定する
            /// </summary>
            public void SetDefaultColor() => SetModelColor(GameDefine.COLOR_WHITE, GameDefine.COLOR_WHITE);

            /// <summary>
            /// キャッシュされたLightProbeColorとEmissiveColorを設定する
            /// </summary>
            private void SetModelColor() => SetModelColor(_lightProbeColor, _emissiveColor);

            private Color _lightProbeColor = GameDefine.COLOR_WHITE;
            private Color _emissiveColor = GameDefine.COLOR_WHITE;

            public Vector3 ModelPosition
            {
                get => _modelPosition;
                set { if (_modelRoot != null) { _modelRoot.localPosition = value; } _modelPosition = value; }
            }
            private Vector3 _modelPosition;

            public Quaternion ModelRotation
            {
                get => _modelRotation;
                set { if (_modelRoot != null) _modelRoot.localRotation = value; _modelRotation = value; }
            }
            private Quaternion _modelRotation;

            public float ModelScale
            {
                get => _modelScale;
                set { if (_modelRoot != null) _modelRoot.localScale = Math.VECTOR3_ONE * value; _modelScale = value; }
            }
            private float _modelScale;

            /// <summary>
            /// IKコリジョン用のスケール値 (モデルのTotalScaleに加えて親TransformのScale変化も考慮する点に注意)
            /// </summary>
            public float ModelIKCollisionScale { get; set; } = 0f;

            public int ModelRenderOrder
            {
                get => _modelRenderOrder;
                set { if (Model != null) Model.UpdateRenderQueue(_modelRenderOrder); _modelRenderOrder = value; }
            }
            private int _modelRenderOrder;

            public void BuildModel(CharaDressIdSet idSet)
            {
                ClearModel();
                CreateModelRoot();
                ModelInfo = idSet.IsMob ?
                    new CharacterBuildInfo(0, ModelLoader.MOB_CHARA_ID, idSet.DressId, ModelLoader.ControllerType.Simple, mobId: idSet.GetMobCharaId(), backDancerColorId: idSet.DressColorId) :
                    new CharacterBuildInfo(0, idSet.GetCharaIdRoundMob(), idSet.DressId, ModelLoader.ControllerType.LiveTheater, backDancerColorId: idSet.DressColorId);
                if (BuildFlag)
                {
                    var modelObj = ModelLoader.CreateModel(ModelInfo);
                    Model = modelObj.GetComponent<SimpleModelController>();
                    InitializeModel();
                }
            }

            private void CreateModelRoot()
            {
                if (_modelRoot != null)
                    return;

                var rootObj = new GameObject("ModelRoot");
                _modelRoot = rootObj.transform;
                _modelRoot.SetParent(ChildTrans);
                _modelRoot.localPosition = Math.VECTOR3_ZERO;
                _modelRoot.localRotation = Math.QUATERNION_IDENTITY;
                _modelRoot.localScale = Math.VECTOR3_ONE;
            }

            private void InitializeModel()
            {
                if (Model == null || _modelRoot == null)
                    return;
                Model.CacheTransform.SetParent(_modelRoot);
                Model.SetVisible(ModelVisible);
                Model.CacheTransform.localPosition = Math.VECTOR3_ZERO;
                Model.CacheTransform.localRotation = Math.QUATERNION_IDENTITY;
                Model.CacheTransform.localScale = Math.VECTOR3_ONE;
                Model.UpdateRenderQueue(ModelRenderOrder);
                Model.PlayMotion(Model.IdleMotionSetMaster, startType: SimpleModelController.MotionStartType.LoopWithoutSubMotion);
                Model.SetCySpringAffect(position: false, rotation: false, scale: false);
                Model.ResetCyspring();
                Model.ReserveWarmingUpCySpring();
                //常時アニメーションさせる
                Model.SetCullingMode(AnimatorCullingMode.AlwaysAnimate);
                // IKコリジョン用のスケールをキャッシュ
                ModelIKCollisionScale = Model.GetTotalScale();
                // RandomTailMotionControllerをTheater用に入れ替え
                Model.SwapRandomTailMotionController(new TheaterRandomTailMotionController());

                // #124001対応: キャッシュしておいたLightProbeColorとEmissiveColorを設定する
                SetModelColor();
            }

            public void ClearModel()
            {
                if (Model != null)
                {
                    Destroy(Model.gameObject);
                }
            }
        }

        private List<ChildInfo> _childList = null;
        private bool _init = false;

        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize()
        {
            _childList = new List<ChildInfo>();
            _init = true;
        }

        /// <summary>
        /// リストの全削除、ゲームオブジェクトも削除します
        /// </summary>
        public void Clear()
        {
            if (_childList == null)
                return;

            foreach (var info in _childList)
            {
                info.ClearModel();
                Destroy(info.ChildObject);
            }
            _childList.Clear();
        }

        /// <summary>
        /// 追加
        /// </summary>
        /// <param name="normalizedPos"></param>
        /// <param name="childObj"></param>
        /// <returns></returns>
        public ChildInfo Add(Vector2 normalizedPos, GameObject childObj, bool defaultBuildFlag = true)
        {
            var newInfo = new ChildInfo()
            {
                NormalizedPos = normalizedPos,
                ChildObject = childObj,
                BuildFlag = defaultBuildFlag
            };
            _childList.Add(newInfo);
            return newInfo;
        }

        /// <summary>
        /// 削除、ゲームオブジェクトの破棄まで行います
        /// </summary>
        /// <param name="info"></param>
        public void Remove(ChildInfo info)
        {
            var index = _childList.IndexOf(info);
            if (index < 0) return;

            _childList.RemoveAt(index);
        }

        /// <summary>
        /// ワールドコーナーの更新
        /// </summary>
        /// <param name="worldCorner"></param>
        public void OnUpdate(Vector3[] worldCorner)
        {
            if (_init == false)
                return;

            foreach (var info in _childList)
            {
                SetPosition(info, worldCorner);
            }
        }

        //worldCorner = 左下、左上、右上、右下
        //コーナー配列で渡す必要は無いが値コピーが防がれることを期待して配列で。
        private void SetPosition(ChildInfo info, Vector3[] worldCorner)
        {
            if (worldCorner.Length != 4)
                return;

            var pos = info.ChildTrans.position;
            //横方向
            pos.x = Mathf.Lerp(worldCorner[0].x, worldCorner[2].x, info.NormalizedPos.x);
            //奥行き
            pos.z = Mathf.Lerp(worldCorner[0].z, worldCorner[2].z, info.NormalizedPos.y);
            //高さ、奥行きと同じ率使ったらとりあえずいい感じになった・・・
            pos.y = Mathf.Lerp(worldCorner[0].y, worldCorner[2].y, info.NormalizedPos.y);

            info.ChildTrans.position = pos;
        }
    }
}
