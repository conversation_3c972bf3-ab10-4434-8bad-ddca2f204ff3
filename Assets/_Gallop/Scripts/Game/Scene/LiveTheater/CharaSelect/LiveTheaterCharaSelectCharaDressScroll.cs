using System.Linq;
using System;
using System.Collections.Generic;
using UnityEngine;
using static Gallop.StaticVariableDefine.LiveTheater.LiveTheaterCharaSelectCharaDressScroll;

namespace Gallop
{
    /// <summary>
    /// ライブシアター:キャラ、衣装選択スクロール
    /// </summary>
    [AddComponentMenu("")]
    public class LiveTheaterCharaSelectCharaDressScroll : MonoBehaviour
    {

        private const int SCROLL_LIMIT_NUM = 5; //この数を超えたらスクロール有効化
        private const int SCROLL_LIMIT_NUM_FOR_FORMATION_SAVE = 6; //編成保存ダイアログの場合（幅広め）

        private static readonly Vector2 Center = new Vector2(0.5f, 0.5f);
        private static readonly Vector2 Left = new Vector2(0, 0.5f);
        private const float CONST_WIDTH = 960;
        private const float CONST_WIDTH_FOR_FORMATION_SAVE = 1050; //編成保存ダイアログの場合（幅広め）

        public enum MemberType {
            Main = 0,
            Back = 1,
        }

        [SerializeField] private ScrollRectCommon _scroll = null;
        [SerializeField] private UnityEngine.UI.ContentSizeFitter _sizeFitter;
        [SerializeField] private RectTransform _content = null;
        [SerializeField] private LiveTheaterCharaSelectCharaDressSetUI _charaDressSetBase = null;
        [SerializeField] private GameObject _emptyObj = null;

        private List<LiveTheaterCharaSelectCharaDressSetUI> _childCharaDressUIList = new List<LiveTheaterCharaSelectCharaDressSetUI>();

        private LiveTheaterInfo _theaterInfo;
        private MemberType _memberType;
        private int _offset;
        private int _max;
        private int _num;

        /// <summary>
        /// 初期化、スクロールの生成
        /// </summary>
        public void Initialize(
            MemberType memberType,
            List<CharacterButtonInfo> charaButtonInfoList,
            LiveTheaterInfo theaterInfo,
            LiveTheaterFormation formation,
            Action<int, int, int, int, int, int, int> onChangeChara,
            Action<LiveTheaterInfo.AllDressChangeType, int, int, LiveTheaterInfo.AllDressChangePattern> onAllDressChange
            )
        {
            _theaterInfo = theaterInfo;
            _memberType = memberType;

            //既存の子要素を削除
            for (var i = 0; i< _childCharaDressUIList.Count; i++)
            {
                Destroy(_childCharaDressUIList[i].gameObject);
            }
            _childCharaDressUIList.Clear();

            _offset = memberType == MemberType.Main ? 0 : _theaterInfo.MainNum;
            _max = memberType == MemberType.Main ? _theaterInfo.MainNum : _theaterInfo.CharaDressIdArray.Length;
            _num = _max - _offset;

            _charaDressSetBase.SetActiveWithCheck(true);

            if (_memberType == MemberType.Main)
            {
                //メインの場合フォーメーションのX座標を元に並び替える
                var posIndexList = new List<KeyValuePair<int, float>>();
                for (int i = 0; i < formation.PositionArray.Length; i++)
                {
                    posIndexList.Add(new KeyValuePair<int, float>(i, formation.PositionArray[i].transform.localPosition.x));
                }
                var sortedList = posIndexList.OrderBy(p => p.Value).ToList();
                for (int i = 0; i < sortedList.Count; i++)
                {
                    var clone = Instantiate(_charaDressSetBase, _content);
                    clone.Initialize(sortedList[i].Key, memberType, charaButtonInfoList, _theaterInfo, onChangeChara, null, onAllDressChange);
                    _childCharaDressUIList.Add(clone);
                }
            }
            else
            {
                for (int i = _offset; i < _max; i++)
                {
                    var clone = Instantiate(_charaDressSetBase, _content);
                    clone.Initialize(i, memberType, charaButtonInfoList, _theaterInfo, onChangeChara, null, onAllDressChange);
                    _childCharaDressUIList.Add(clone);
                }
            }

            _charaDressSetBase.SetActiveWithCheck(false);

            //０人
            _emptyObj.SetActive(_num == 0);

            if (_num < SCROLL_LIMIT_NUM)
            {
                //スクロールできない
                _scroll.enabled = false;

                //中心に寄せる
                _content.anchorMax = Center;
                _content.anchorMin = Center;
                _content.pivot = Center;
                _content.anchoredPosition = Math.VECTOR2_ZERO;
                _scroll.horizontalScrollbar.gameObject.SetActive(false);

                //サイズを固定してExpandさせる
                _sizeFitter.enabled = false;
                _content.sizeDelta = new Vector2(CONST_WIDTH, _content.sizeDelta.y);
            }
            else
            {
                //左に寄せてスクロールさせる
                _content.anchorMax = Left;
                _content.anchorMin = Left;
                _content.pivot = Left;

                //スクロール有効化
                _sizeFitter.enabled = true;
                _scroll.enabled = true;
                _scroll.horizontal = true;
                _scroll.horizontalScrollbar.gameObject.SetActive(true);
            }

            //強制リビルド
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(_content);
        }

        /// <summary>
        /// 初期化（編成保存画面用。キャラ衣装変更操作不可）
        /// </summary>
        public void InitializeForFormationSave(LiveTheaterInfo theaterInfo)
        {
            _theaterInfo = theaterInfo;

            //既存の子要素を削除
            var charaListCount = _childCharaDressUIList.Count;
            for (var i = 0; i < charaListCount; i++)
            {
                Destroy(_childCharaDressUIList[i].gameObject);
            }
            _childCharaDressUIList.Clear();

            _offset = 0;
            if (_theaterInfo != null && _theaterInfo.CharaDressIdArray != null)
            {
                _max = _theaterInfo.CharaDressIdArray.Count();
            }
            else
            {
                _max = 0;
            }
            _num = _max - _offset;

            _charaDressSetBase.SetActiveWithCheck(true);

            // メインダンサー＆バックダンサー全員ループ
            for (var i = 0; i < _max; i++)
            {
                var clone = Instantiate(_charaDressSetBase, _content);

                MemberType memberType = MemberType.Main;
                if (_theaterInfo != null && _theaterInfo.MainNum <= i)
                {
                    memberType = MemberType.Back;
                }
                clone.Initialize(i, memberType, null, _theaterInfo, null, null, null, true);
                clone.AllButtonEnable(false); // ボタン操作無効
                _childCharaDressUIList.Add(clone);
            }

            _charaDressSetBase.SetActiveWithCheck(false);

            if (_num < SCROLL_LIMIT_NUM_FOR_FORMATION_SAVE)
            {
                //スクロールできない
                _scroll.enabled = false;

                //中心に寄せる
                _content.anchorMax = Center;
                _content.anchorMin = Center;
                _content.pivot = Center;
                _content.anchoredPosition = Math.VECTOR2_ZERO;
                _scroll.horizontalScrollbar.gameObject.SetActive(false);

                //サイズを固定してExpandさせる
                _sizeFitter.enabled = false;
                _content.sizeDelta = new Vector2(CONST_WIDTH_FOR_FORMATION_SAVE, _content.sizeDelta.y);
            }
            else
            {
                //左に寄せてスクロールさせる
                _content.anchorMax = Left;
                _content.anchorMin = Left;
                _content.pivot = Left;

                //スクロール有効化
                _sizeFitter.enabled = true;
                _scroll.enabled = true;
                _scroll.horizontal = true;
                _scroll.horizontalScrollbar.gameObject.SetActive(true);
            }

            //強制リビルド
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(_content);
        }


        /// <summary>
        /// ライブinfoから表示更新
        /// </summary>
        public void Refresh()
        {
            foreach(var ui in _childCharaDressUIList)
            {
                ui.UpdateCharaTex();
                ui.UpdateDressTex();
            }
        }

        /// <summary>
        /// キャラの入れ替え
        /// </summary>
        public void SetCharaId(int listIndex, int charaId, int dressId, int dressColor, int dressId2, int dressColor2)
        {
            var uiIndex = listIndex - _offset;
            if (uiIndex < 0 || uiIndex >= _childCharaDressUIList.Count)
                return;

            if (_memberType == MemberType.Main)
            {
                //メインは配列の並びと表示されてる並びが異なるため内部で保持してるインデックス値と比較する
                foreach (var ui in _childCharaDressUIList)
                {
                    if (ui.Index == uiIndex)
                    {
                        ui.SetCharaId(charaId, dressId, dressColor, dressId2, dressColor2);
                        break;
                    }
                }
            }
            else
            {
                //バックは配列の並び＝UIの並び
                _childCharaDressUIList[uiIndex].SetCharaId(charaId, dressId, dressColor, dressId2, dressColor2);
            }
        }


        //バックダンサー衣装は個別に設定できるように、ただ負荷次第で戻すかもとのことなので一旦残し
#if false

        /// <summary>
        /// バックダンサーの衣装アイコンを引数指定の衣装IDのものに変更する
        /// </summary>
        /// <param name="dressId"></param>
        private void SetBackDancerAllDressIdIcon(int selectedIndex, int oldId, int dressId, int colorId)
        {
            if (_memberType != MemberType.Back)
                return;

            for(int i = 0; i< _childCharaDressUIList.Count; i++)
            {
                _childCharaDressUIList[i].SetDressId(dressId, colorId);
            }
        }

#endif
    }
}