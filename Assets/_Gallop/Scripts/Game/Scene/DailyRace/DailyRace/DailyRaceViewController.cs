using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Cute.Core;

namespace Gallop
{
    public class DailyRaceViewModel
    {
        /// <summary>
        /// 選択中のデイリーレース情報
        /// </summary>
        private DailyRaceInfo _currentDailyRaceInfo = null;
        public DailyRaceInfo CurrentDailyRaceInfo 
        {
            get
            {
                return _currentDailyRaceInfo;
            }

            set
            {
                // 異なるレースが選択された
                if(_currentDailyRaceInfo != null &&
                   _currentDailyRaceInfo.RaceId != value.RaceId)
                {
                    // 選択中キャラをリセット
                    _trainedCharaId = 0;
                }
                _currentDailyRaceInfo = value;
            }
        }

        //選択したグレード情報
        public DailyRaceInfo.GradeInfo GradeInfo { get; set; }


        //選択した育成済みキャラID
        private int _trainedCharaId = 0;
        public int TrainedCharaDataId
        {
            get
            {
                // 設定がない場合
                if (_trainedCharaId <= 0)
                {
                    // 前回プレイしたウマ娘を設定
                    var trainedList = WorkDataManager.Instance.TrainedCharaData.List;
                    var initialSelectTrainedCharaId = GetInitialSelectTrainedCharaId(GradeInfo, trainedList);
                    _trainedCharaId = initialSelectTrainedCharaId;
                }
                return _trainedCharaId;
            }

            set
            {
                _trainedCharaId = value;
            }
        }


        /// <summary>
        /// 過去にこのデイリーレースに出走した事があるなら、その時出走したウマ娘を返す
        /// </summary>
        /// <returns></returns>
        private int GetInitialSelectTrainedCharaId(DailyRaceInfo.GradeInfo gradeInfo, List<WorkTrainedCharaData.TrainedCharaData> trainedCharaList)
        {
            if (gradeInfo == null)
                return 0;
            if (trainedCharaList == null || !trainedCharaList.Any())
                return 0;

            if (!SaveDataManager.HasInstance())
                return 0;

            var logGroupIdArray = SaveDataManager.Instance.SaveLoader.DailyRaceLogGroupIdArray;
            if (logGroupIdArray == null || logGroupIdArray.Length <= 0)
                return 0; // 過去の出走記録が無い

            var logTrainedCharaIdArray = SaveDataManager.Instance.SaveLoader.DailyRaceLogTrainedCharaIdArray;
            if (logTrainedCharaIdArray == null || logTrainedCharaIdArray.Length <= 0)
                return 0;

            if (logGroupIdArray.Length != logTrainedCharaIdArray.Length)
                return 0;

            var currentGroupId = gradeInfo.DailyRace.GroupId;

            for (int i = 0; i < logGroupIdArray.Length; ++i)
            {
                var logGroupId = logGroupIdArray[i];
                if (logGroupId <= 0)
                    continue;
                if (currentGroupId != logGroupId)
                    continue;

                var logTrainedCharaId = logTrainedCharaIdArray[i];
                if (logTrainedCharaId <= 0)
                    continue;

                var anyLogTrainedChara = trainedCharaList.Any(trainedChara => trainedChara.Id == logTrainedCharaId);
                if (!anyLogTrainedChara)
                    continue; // 移籍などで当時のウマ娘がいなくなった

                return logTrainedCharaId;
            }

            return 0;
        }

        public bool HasTicket
        {
            get
            {
                var ticketCurrentNum = WorkDataManager.Instance.ItemData.GetHaveItemNum(GameDefine.ITEM_ID_DAILY_TICKET);
                return ticketCurrentNum > 0;
            }
        }


        public void ClearDailyRaceSelectInfo()
        {
            _currentDailyRaceInfo = null;
            GradeInfo = null;
            _trainedCharaId = 0;
        }
    }



    /// <summary>
    /// デイリーレース
    /// Viewコントローラー
    /// </summary>
    [AddComponentMenu("")]
    public class DailyRaceViewController : ViewControllerBase<DailyRaceView>
    {
        /// <summary>
        /// 画面の表示状態
        /// </summary>
        public enum State
        {
            Top = 0,
            CharaSelect,
            Max
        }

        private DailyRaceViewModel _dailyRaceViewModel = null;
        private DailyRaceViewModel DailyRaceModel
        {
            get
            {
                if(_dailyRaceViewModel == null)
                {
                    _dailyRaceViewModel = new DailyRaceViewModel();
                }
                return _dailyRaceViewModel;
            }
        }



        //背景パス
        public string TopBgEnvPath { get; private set; }
        public string TopBgPath { get; private set; }

        //ステートに対応した画面辞書
        private Dictionary<State, RaceTopUIBase<DailyRaceViewController>> _stateUIDic;

        //現在ステート
        private State _state;

        //インデックス通信レスポンス
        private DailyRaceIndexResponse.CommonResponse _indexRes = null;

        #region override

        /// <summary>
        /// View初期化
        /// </summary>
        public override IEnumerator InitializeEachPlayIn()
        {
            //辞書にステートごとの画面を登録
            if (_stateUIDic == null)
            {
                _stateUIDic = new Dictionary<State, RaceTopUIBase<DailyRaceViewController>>();
                _stateUIDic.Add(State.Top, _view.Top);
                _stateUIDic.Add(State.CharaSelect, _view.CharaSelect);
                foreach(var ui in _stateUIDic.Values)
                {
                    ui.SetViewController(this);
                    yield return ui.InitializeEachPlayIn();
                }
            }

            //EndViewで次に進む場合以外は全て破棄するのでそれ以外は通信してデータを更新
            if (_indexRes == null)
            {
                // インデックス通信、クリア状況など受け取る
                var isComplete = false;
                var req = new DailyRaceIndexRequest();
                req.Send(res =>
                {
                    _indexRes = res.data;
                    isComplete = true;
                });
                yield return new WaitWhile(() => isComplete == false);

                // インデックスレスポンスからレース一覧を作成
                TempData.Instance.DailyRaceData.UpdateRaceInfoList(_indexRes.daily_race_record_array);

                // AccountHold状態を保持
                TempData.Instance.ShopSubscriptionData.IsValidOnGame = _indexRes.subscription_is_valid;

                // レース一覧に合わせたアセットをDL
                yield return DownloadRaceInfoAssets(TempData.Instance.DailyRaceData.RaceInfoList);

                // ワークデータの「出走中の自キャラID」をリセット
                WorkDataManager.Instance.RaceStateData.SetDailyRaceEntryTrainedCharaId(0);

                //インデックスレスポンスから情報を更新
                UpdateIndexDailyInfo();
            }
            
            yield return base.InitializeEachPlayIn();
        }

        /// <summary>
        /// 画面In再生
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PlayInView()
        {
            foreach (var kv in _stateUIDic)
            {
                kv.Value.Hide(true);

                yield return kv.Value.PlayInView();
            }

            var setDailyRaceTopMode = DailyRaceTop.Mode.RaceList;

            bool isFromResult = TempData.Instance.DailyRaceData.CurrentGroupId != TempData.DailyRaceTempData.INVAID_GROUP_ID;
            if (isFromResult)
            {
                // API通信が完了していて、レースリザルトから返ってきた場合
                int targetGroupId = TempData.Instance.DailyRaceData.CurrentGroupId;
                var raceInfo = TempData.Instance.DailyRaceData.RaceInfoList.FirstOrDefault(x => x.GroupId == targetGroupId);
                if (raceInfo != null)
                {
                    DailyRaceModel.CurrentDailyRaceInfo = raceInfo;
                    setDailyRaceTopMode = DailyRaceTop.Mode.GradeList;
                }
            }

            TempData.Instance.DailyRaceData.CurrentGroupId = TempData.DailyRaceTempData.INVAID_GROUP_ID;
            TempData.Instance.DailyRaceData.CurrentRaceInstanceId = TempData.DailyRaceTempData.INVAID_RACE_INSTANCE_ID;

            if(setDailyRaceTopMode == DailyRaceTop.Mode.RaceList)
            {
                var viewInfo = GetViewInfo() as DailyRaceViewInfo;
                if (viewInfo != null)
                {
                    var raceInfo = TempData.Instance.DailyRaceData.RaceInfoList.FirstOrDefault(x => x.GroupId == viewInfo.GroupId);
                    if (raceInfo != null)
                    {
                        DailyRaceModel.CurrentDailyRaceInfo = raceInfo;
                        setDailyRaceTopMode = DailyRaceTop.Mode.GradeList;
                    }
                }
            }

            SetState(State.Top, setDailyRaceTopMode);

            yield return base.PlayInView();
        }

        /// <summary>
        /// 画面Out再生
        /// </summary>
        /// <returns></returns>
        public override IEnumerator PlayOutView()
        {
            if (_stateUIDic.TryGetValue(_state, out var ui))
            {
                yield return ui.PlayOutView();
            }
            yield return base.PlayOutView();
        }

        /// <summary>
        /// 終了処理
        /// </summary>
        /// <returns></returns>
        public override IEnumerator EndView()
        {
            //表示終了
            foreach (var kv in _stateUIDic)
            {
                yield return kv.Value.EndView();
            }

            //レスポンスは消しておく
            _indexRes = null;

            // 選択情報を破棄
            DailyRaceModel.ClearDailyRaceSelectInfo();

            yield return base.EndView();
        }

        public override void RegisterDownload(DownloadPathRegister register)
        {
            //背景
            TopBgPath = ResourcePath.GetBackgroundPath(DailyRaceTop.TOP_BG_ID, DailyRaceTop.TOP_BG_SUB_ID);
            TopBgEnvPath = ResourcePath.DAILY_TOP_ENV_PATH;

            register.RegisterPathWithoutInfo(TopBgPath);
            register.RegisterPathWithoutInfo(TopBgEnvPath);

            //走法選択
            var charaIdList = new List<int>();
            var trainedList = WorkDataManager.Instance.TrainedCharaData.List;
            foreach (var t in trainedList)
            {
                if (t == null)
                {
                    Debug.LogError("殿堂入りウマ娘のリストの中にnullがあります");
                    continue;
                }

                if(!charaIdList.Contains(t.CharaId))
                {
                    charaIdList.Add(t.CharaId);
                }
            }
            DialogSetRunStyle.RegisterDownloadAssets(register);
            
            //レース選択画面
            DailyRaceTop.RegisterDL(register);

            //キャラ選択画面
            PartsRaceEntryCharacterSelect.RegisterDownload(
                register,
                charaIdList,
                PartsRaceEntryCharacterSelect.ViewType.Daily);

            // デイリーレースTOP：キャラの吹き出し＆音声
            PartsDailyRaceTopCharaMessage.RegisterDownload(register);

            // デイリーレースまとめてスキップ結果演出ダイアログ
            DialogDailyRaceSkipMultiRaceResult.RegisterDownload(register);
            // 限定セールスダイアログ
            DialogItemExchangeLimitedSalesOpen.RegisterDownload(register);

            // ウマさんぽキャンペーンの期間内or初日ならダウンロード（View遷移を跨がずレースリザルトが出る場合があるので、初日は開催時刻前もダウンロードを許可する）
            var isCampaignWalkingFirstDay = MasterDataManager.Instance.masterCampaignData.IsCampaignWalkingFirstDay();
            if (WorkDataManager.Instance.CampaignWalkingData.IsInTerm() || isCampaignWalkingFirstDay)
            {
                DialogCampaignWalkingGaugeUpCutin.RegisterDownload(register);
            }

            // 因子研究の期間内or初日ならダウンロード
            if (FactorResearchUtil.IsEventOpenOrFirstDay())
            {
                DialogFactorResearchGaugeUp.RegisterDownload(register);
            }

            base.RegisterDownload(register);
        }

        /// <summary>
        /// バックボタン押下
        /// </summary>
        public override void OnClickBackButton()
        {
            Back();
            base.OnClickBackButton();
        }

        /// <summary>
        /// OSバックボタン押下
        /// </summary>
        public override void OnClickOsBackKey()
        {
            Back();
            base.OnClickOsBackKey();
        }

        #endregion

        #region method

        /// <summary>
        /// 次の状態に遷移
        /// </summary>
        public void Next()
        {
            var next = _state + 1;
            if(next == State.Max)
            {
                if (DailyRaceModel.GradeInfo == null)
                {
                    Debug.LogError("GradeInfoがnullなのにmaxになった");
                    next--;
                }
                else if (DailyRaceModel.GradeInfo.IsClear && SaveDataManager.Instance.SaveLoader.DailyIsEnableSkipMultiRace)
                {
                    // まとめてレーススキップ設定ダイアログ表示
                    OpenDialogSkipNumSetting(DailyRaceModel.TrainedCharaDataId, DailyRaceModel.CurrentDailyRaceInfo, DailyRaceModel.GradeInfo);
                    return;
                }
                else
                {
                    //パドックへ
                    GoPaddock(DailyRaceModel.TrainedCharaDataId, DailyRaceModel.CurrentDailyRaceInfo, DailyRaceModel.GradeInfo);
                    return;
                }
            }

            //次画面へ
            SetState(next);
        }

        /// <summary>
        /// バックキー/戻るボタンの共通処理
        /// </summary>
        private void Back()
        {
            // カード強化から来た場合は強化へ戻る(階層戻りなし)
            if (WorkDataManager.Instance.CardData.BackCardHaveViewUsingStack())
            {
                return;
            }

            BackDailyRaceTop();

            BackDailyRaceCharaSelect();
        }

        private void BackDailyRaceTop()
        {
            if (_state != State.Top)
            {
                return;
            }
            
            var stateUI = _stateUIDic[State.Top] as DailyRaceTop;
            switch(stateUI.Current)
            {
                case DailyRaceTop.Mode.RaceList:
                    BackOtherView();
                    return;

                case DailyRaceTop.Mode.GradeList:
                    _view.Top.ChangeGradeListToRaceList();
                    return;
            }
        }

        private void BackOtherView()
        {
            // キャンペーン、ミッション画面から来た場合は該当画面へ戻す
            if (!SceneManager.Instance.BackUsingStack())
            {
                // デイリーレジェンドレースリリース後ならレース画面第1.5階層へ
                UIManager.Footer.Transition(Footer.ButtonType.Race, (int)RaceHomeTopUI.DisplayType.DailyProgram);
            }
        }

        private void BackDailyRaceCharaSelect()
        {
            if (_state != State.CharaSelect)
            {
                return;
            }
            
            SetState(State.Top, DailyRaceTop.Mode.GradeList);
        }
        
        /// <summary>
        /// キャラ選択ステートからトップステートへアニメーションしながら遷移させるコルーチン
        /// </summary>
        IEnumerator CoroutineChangeStateCharaSelectToTop()
        {
            // 切り替わり中の入力を禁止
            UIManager.Instance.LockGameCanvas();

            // トップステートへ
            SetState(State.Top);

            var topStateUI = _stateUIDic[State.Top] as DailyRaceTop;

            // INアニメーション
            yield return topStateUI.PlayInGradeListMode(DailyRaceModel.CurrentDailyRaceInfo);

            // 入力禁止解除
            UIManager.Instance.UnlockGameCanvas();
        }

        /// <summary>
        /// 引数ステートの画面に設定
        /// </summary>
        /// <param name="state"></param>
        private void SetState(State state, DailyRaceTop.Mode dailyRaceTopMode = DailyRaceTop.Mode.RaceList)
        {
            foreach(var kv  in _stateUIDic)
            {
                kv.Value.Hide(false);
            }

            SetupHeaderTitle(state);

            switch (state)
            {
                case State.Top:
                    ShowDailyRaceTop(dailyRaceTopMode);
                    break;
                case State.CharaSelect:
                    ShowDailyRaceCharaSelect();
                    break;
            }
        }
        
        /// <summary>
        /// ヘッダー名の反映
        /// </summary>
        private void SetupHeaderTitle(State state)
        {
            switch (state)
            {
                case State.Top:
                    UIManager.Instance.SetHeaderTitleText(TextId.Race0043.Text(), DialogTutorialGuide.TutorialGuideId.DailyRaceTop);
                    break;
                case State.CharaSelect:
                    UIManager.Instance.SetHeaderTitleText(TextId.CustomRace0048.Text(), DialogTutorialGuide.TutorialGuideId.DailyRaceTop);
                    break;
            }
            // ヘッダータイトルを元に戻す変更（綺麗にやるならViewのHeaderの変更を自分でやるのではなく、サポカ強化画面のように全て自分で管理する形に変更する）
            UIManager.Instance.PlayHeaderTitleInAnim();
        }

        //indexのレスポンスからinfo更新
        private void UpdateIndexDailyInfo()
        {
            if (_indexRes == null || _indexRes.daily_race_record_array == null)
                return;

            //チケット交換回数更新
            WorkDataManager.Instance.ItemData.SetDailyRaceTicketPurchaseNum(_indexRes.purchase_num);
        }


        #region DailyRaceTop

        private void ShowDailyRaceTop(DailyRaceTop.Mode dailyRaceTopMode = DailyRaceTop.Mode.RaceList)
        {
            _view.Top.Show(dailyRaceTopMode, OnSelectDailyRace, OnDecideDailyRaceGrade, DailyRaceModel.CurrentDailyRaceInfo);

            _state = State.Top;
        }

        /// <summary>
        /// レース選択時
        /// </summary>
        /// <param name="dailyRaceInfo"></param>
        private void OnSelectDailyRace(DailyRaceInfo dailyRaceInfo)
        {
            // 選択レースを保存
            DailyRaceModel.CurrentDailyRaceInfo = dailyRaceInfo;

            // レースリストからグレードリストへ遷移
            _view.Top.ChangeRaceListToGradeList(DailyRaceModel.CurrentDailyRaceInfo);
        }

        /// <summary>
        /// グレード決定時
        /// </summary>
        /// <param name="gradeInfo"></param>
        private void OnDecideDailyRaceGrade(DailyRaceInfo.GradeInfo gradeInfo)
        {
            DailyRaceModel.GradeInfo = gradeInfo;

            if (!DailyRaceModel.HasTicket)
            {
                OpenDialogNotHaveTicket();
                return;
            }

            // レース詳細（出走確認）ダイアログを開く
            DialogDailyRaceGradeSelectConfirm.PushDialog(gradeInfo, 0, null, // チケット購入機能OFF
            () =>
            {
                // キャラ選択画面へ
                SetState(State.CharaSelect);
            });
        }

        private void OpenDialogNotHaveTicket()
        {
            // 日付跨ぎチェック（チケットは日付を跨ぐと回復して購入の必要が無くなるので、無駄な課金を防ぐためにここでチェックする）
            if (DateChange.CheckDateChange())
            {
                DateChange.ExecuteDateChangeProcess();
                return;
            }

            // チケットが足りないなら、それを伝えて購入するか確認するダイアログを出す
            RaceUtil.OpenDialogNotHaveTicket(RaceDefine.RaceType.Daily, () => 
            {
                _view.Top.UpdateTicketNum();
            });
        }

        #endregion

        #region DailyRaceCharaSelect

        private void ShowDailyRaceCharaSelect()
        {
            var selectCharaDataId = DailyRaceModel.TrainedCharaDataId;
            var gradeInfo = DailyRaceModel.GradeInfo;

            _view.CharaSelect.Show(DailyRaceCharaSelectOnDecide, DailyRaceCharaSelectOnSelect, selectCharaDataId, gradeInfo);

            _state = State.CharaSelect;
        }

        /// <summary>
        /// キャラ決定時
        /// </summary>
        private void DailyRaceCharaSelectOnDecide()
        {
            Next();
        }

        /// <summary>
        /// キャラ選択時
        /// </summary>
        /// <param name="trainedCharaData"></param>
        private void DailyRaceCharaSelectOnSelect(WorkTrainedCharaData.TrainedCharaData trainedCharaData)
        {
            DailyRaceModel.TrainedCharaDataId = trainedCharaData.Id;
        }

        #endregion

        /// <summary>
        /// レースまとめてスキップ設定
        /// </summary>
        /// <param name="trainedCharaId"></param>
        /// <param name="currentDailyRaceInfo"></param>
        /// <param name="gradeInfo"></param>
        private void OpenDialogSkipNumSetting(int trainedCharaId, DailyRaceInfo currentDailyRaceInfo, DailyRaceInfo.GradeInfo gradeInfo)
        {
            // チケットの種類
            var ticketItemId = GameDefine.ITEM_ID_DAILY_TICKET;
            
            DialogRaceSkipNumSetting.Open(ticketItemId, trainedCharaId, onDecide:(skipCount, runningStyle) =>
            {
                var ticketNum = WorkDataManager.Instance.ItemData.GetHaveItemNum(ticketItemId);
                
                var req = new DailyRaceSkipRequest();
                req.daily_race_id = gradeInfo.DailyRace.Id;
                req.trained_chara_id = trainedCharaId;
                req.race_skip_count = skipCount;
                req.client_own_num = ticketNum;
                req.running_style = (int)runningStyle;
                req.Send((res) =>
                {
                    for (int i = 0; i < res.data.race_reward_array.Length; i++)
                    {
                        //報酬付与
                        WorkDataUtil.SetRewardSummaryInfo(res.data.race_reward_array[i].reward_summary_info);
                    }

                    // 所持チケット数更新
                    WorkDataManager.Instance.ItemData.Update(res.data.item_info_array);

                    // 限定セールス反映
                    WorkDataManager.Instance.LimitedSalesData.Update(res.data.limited_shop_info);

                    // サーバー保存した走法作戦を反映
                    WorkDataManager.Instance.TrainedCharaData.UpdateRunningStyle(trainedCharaId, (int)runningStyle);

                    // ウマさんぽキャンペーンのデータ反映
                    WorkDataManager.Instance.CampaignWalkingData.Apply(res.data);

                    // 因子研究のデータ反映
                    WorkDataManager.Instance.FactorResearchData.Apply(res.data);

                    // 選択した殿堂入りウマ娘を保存しておく
                    SaveSelectedTrainedCharaId(currentDailyRaceInfo, trainedCharaId);

                    // 演出ダイアログを開く
                    _view.StartCoroutine(OpenDialogSkipRaceResult(skipCount, trainedCharaId, res.data.race_reward_array));
                });

            }, () =>
            {
                // ウマ娘詳細を開く
                _view.CharaSelect.OpenDialogTrainedCharaDetail(trainedCharaId);
            });
        }

        /// <summary>
        /// レースまとめてスキップ結果ダイアログを開く
        /// </summary>
        /// <param name="skipCount"></param>
        /// <param name="trainedCharaId"></param>
        /// <param name="raceSkipRewardArray"></param>
        private IEnumerator OpenDialogSkipRaceResult(int skipCount, int trainedCharaId, RaceSkipReward[] raceSkipRewardArray)
        {
            TempData.Instance.DailyRaceData.IsPlayingSkipResult = true;
            var complete = false;
            var waitComplete = new WaitUntil(() => complete);

            // 演出ダイアログを開く
            DialogDailyRaceSkipMultiRaceResult.Open(skipCount, trainedCharaId, raceSkipRewardArray, onComplete: () =>
            {
                complete = true;
            });
            yield return new WaitUntil(() => complete);

            // 必要ならウマさんぽキャンペーンのおさんぽゲージ上昇演出を再生
            {
                complete = false;
                CampaignWalkingUtil.PlayGaugeUpCutinIfNeed(() => complete = true);
                yield return waitComplete;
            }

            // 必要なら因子研究のゲージ上昇演出を再生
            var factorResearchGaugeInfo = WorkDataManager.Instance.FactorResearchData.GaugeInfo;
            if (factorResearchGaugeInfo != null && factorResearchGaugeInfo.NeedPlayGaugeUpAnimation())
            {
                complete = false;
                DialogFactorResearchGaugeUp.Play(factorResearchGaugeInfo, () => complete = true);
                yield return waitComplete;
            }

            // ショップの限定セールスが開いていて開催通知が必要なら出す
            if (WorkDataManager.Instance.LimitedSalesData.NeedToInform)
            {
                // 限定セールスオープンダイアログを開く
                DialogItemExchangeLimitedSalesOpen.Open(
                    WorkDataManager.Instance.LimitedSalesData,
                    // ショップへ遷移する時の処理
                    onChangeView: () =>
                    {
                        // デイリーレースへ出走中の(殿堂入り)自キャラIDをクリア
                        WorkDataManager.Instance.RaceStateData.SetDailyRaceEntryTrainedCharaId(0);

                        // ショップで戻るボタンを押した時の戻り先を指定
                        WorkDataManager.Instance.LimitedSalesData.StackViewForBack(SceneDefine.ViewId.DailyRace);
                    },
                    onClose: () =>
                    {
                        // ショップに行く場合以外
                        if (SceneManager.Instance.GetNextViewId() != SceneDefine.ViewId.MenuShop)
                        {
                            // デイリーレーストップ、難易度選択に戻る
                            SetState(State.Top, DailyRaceTop.Mode.GradeList);
                        }
                    });
            }
            else
            {
                // トップ、難易度選択に戻る
                SetState(State.Top, DailyRaceTop.Mode.GradeList);
            }
        }

        /// <summary>
        /// パドックへ
        /// </summary>
        private void GoPaddock(int trainedCharaId, DailyRaceInfo dailyInfo, DailyRaceInfo.GradeInfo gradeInfo)
        {
            var masterDaily = MasterDataManager.Instance.masterDailyRace.Get(gradeInfo.DailyRace.Id);
            if (masterDaily == null)
                return;

            var req = new DailyRaceRaceEntryRequest();
            req.daily_race_id = gradeInfo.DailyRace.Id;
            req.trained_chara_id = trainedCharaId;

            req.Send(res =>
            {
                // パドックを含めたレースの進行状況をワークデータに記録
                WorkDataManager.Instance.RaceStateData.SetDailyRaceState(res.data.state);
                WorkDataManager.Instance.RaceStateData.SetDailyRaceEntryTrainedCharaId(res.data.trained_chara_id);

                // 選択した殿堂入りウマ娘を保存しておく
                SaveSelectedTrainedCharaId(dailyInfo, trainedCharaId);

                var horseDataArray = res.data.race_horse_data_array;
                var season = (GameDefine.BgSeason)res.data.season;
                var weather = (RaceDefine.Weather)res.data.weather;
                var groundCondition = (RaceDefine.GroundCondition)res.data.ground_condition;
                int randomSeed = res.data.random_seed;

                for (int i = 0; i < horseDataArray.Length; i++)
                {
                    var horseData = horseDataArray[i];
                    // 無効な衣装は体操服にしておく
                    if (horseData.race_dress_id == 0)
                        horseData.race_dress_id = (int)ModelLoader.DressID.TrackSuit;
                }

                // LoadRaceInfo/RaceInfo生成
                var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                    masterDaily.RaceInstanceId,
                    horseDataArray,
                    randomSeed,
                    RaceDefine.RaceType.Daily,
                    season,
                    weather,
                    groundCondition,
                    string.Empty
                );
                var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
                RaceInitializer.CreateRaceInfo(loadRaceInfo);

                // パドックへ遷移
                TempData.Instance.DailyRaceData.CurrentGroupId = dailyInfo.GroupId;
                TempData.Instance.DailyRaceData.CurrentRaceInstanceId = masterDaily.RaceInstanceId;
                var viewInfo = new DailyPaddockViewInfo(loadRaceInfo, SceneDefine.ViewId.DailyRace, dailyInfo, gradeInfo);
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.DailyRacePaddock, viewInfo);
            });
        }


        /// <summary>
        /// レース一覧に合わせたアセットをDL
        /// </summary>
        /// <param name="raceInfoList"></param>
        /// <returns></returns>
        private IEnumerator DownloadRaceInfoAssets(List<DailyRaceInfo> raceInfoList)
        {
            var register = DownloadManager.GetNewRegister();
            foreach (var info in raceInfoList)
            {
                info.RegisterDownload(register);
            }

            DownloadManager.Instance.FixDownloadList(ref register);

            bool isComplete = false;
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                isComplete = true;
            });
            yield return new WaitWhile(() => isComplete == false);
        }


        /// <summary>
        /// 選択した殿堂入りウマ娘を（レースのGroupId別に）保存しておく
        /// </summary>
        private void SaveSelectedTrainedCharaId(DailyRaceInfo dailyRaceInfo, int selectedTrainedCharaId)
        {
            if (!SaveDataManager.HasInstance())
                return;

            var logGroupIdArray = SaveDataManager.Instance.SaveLoader.DailyRaceLogGroupIdArray;
            var logTrainedCharaIdArray = SaveDataManager.Instance.SaveLoader.DailyRaceLogTrainedCharaIdArray;
            DebugUtils.Assert(logGroupIdArray.Length == logTrainedCharaIdArray.Length);

            // 上書き保存
            if (logGroupIdArray.Contains(dailyRaceInfo.GroupId))
            {
                for (int i = 0; i < logGroupIdArray.Length; ++i)
                {
                    int logGroupId = logGroupIdArray[i];
                    if (logGroupId != dailyRaceInfo.GroupId)
                        continue;

                    logTrainedCharaIdArray[i] = selectedTrainedCharaId;
                }
            }
            // 新規保存
            else
            {
                System.Array.Resize(ref logGroupIdArray, logGroupIdArray.Length + 1);
                System.Array.Resize(ref logTrainedCharaIdArray, logGroupIdArray.Length); // 常にlogGroupArrayと同じサイズ

                logGroupIdArray[logGroupIdArray.Length - 1] = dailyRaceInfo.GroupId;
                logTrainedCharaIdArray[logTrainedCharaIdArray.Length - 1] = selectedTrainedCharaId;
            }

            SaveDataManager.Instance.SaveLoader.DailyRaceLogGroupIdArray = logGroupIdArray;
            SaveDataManager.Instance.SaveLoader.DailyRaceLogTrainedCharaIdArray = logTrainedCharaIdArray;
            SaveDataManager.Instance.Save();
        }

        #endregion
    }


    /// <summary>
    /// デイリーレースのViewInfo
    /// </summary>
    public class DailyRaceViewInfo : IViewInfo
    {
        /// <summary>
        /// デフォルト表示したいレースのグループID
        /// </summary>
        public int GroupId { get; }

        public DailyRaceViewInfo(int groupId)
        {
            GroupId = groupId;
        }
    }
}