using System;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// デイリーレース：まとめてスキップ結果演出ダイアログのリストアイテム
    /// </summary>
    public class PartsDailyRaceSkipMultiRaceResultListItem : MonoBehaviour
    {
        private const string RESULT_RANK_TEXT_OBJ_NAME = "txt_race_result_rank03";
        private const string RESULT_RANK_IN_ANIMATION_FORMAT = "in{0:D2}";
        private const string RESULT_RANK_LOOP_ANIMATION_FORMAT = "loop{0:D2}";
        private const int RESULT_RANK_UNDER_FOURTH_ANIMATION_NUM = 3;

        private const float PLAY_IN_INTERVAL_SHORT = 0.033f;
        private const float PLAY_IN_INTERVAL_LONG = 0.066f;
        private const float DELAY_AUTO_LAYOUT = 0.26f;
        private const float DELAY = 0.033f;
        private const float WAIT_ICON_PLAY = 0.13f;

        private const string SE_CUENAME_DAILY_RACE_SKIP_RANK_1 = "snd_sfx_daylyrace_skip_rank_1";
        private const string SE_CUENAME_DAILY_RACE_SKIP_RANK_2 = "snd_sfx_daylyrace_skip_rank_2";
        private const string SE_CUENAME_DAILY_RACE_SKIP_RANK_3 = "snd_sfx_daylyrace_skip_rank_3";

        [SerializeField] 
        private ContentSizeFitter _contentSizeFitter = null;
        
        [SerializeField] 
        private VerticalLayoutGroup _verticalLayoutGroup = null;

        [SerializeField]
        private GameObject _skipCountLabelObject = null;

        [SerializeField]
        private TextCommon _skipCountLabel = null;

        [SerializeField]
        private ImageCommon _skipCountLabelBackground = null;

        [SerializeField]
        private RectTransform _resultRankFlashParentTransform = null;

        [SerializeField]
        private GameObject _mainRewardLabelObject = null;

        [SerializeField]
        private ImageCommon _mainRewardLabelBackground = null;

        [SerializeField]
        private VerticalLayoutGroup _mainRewardVerticalLayoutGroup = null;

        [SerializeField]
        private ContentSizeFitter _mainRewardParentContentSizeFitter = null;

        [SerializeField]
        private ContentSizeFitter _mainRewardGridContentSizeFitter = null;

        [SerializeField]
        private GameObject _raceOrderRewardLabelObject = null;

        [SerializeField]
        private ImageCommon _raceOrderRewardLabelBackground = null;

        [SerializeField]
        private VerticalLayoutGroup _raceOrderRewardVerticalLayoutGroup = null;

        [SerializeField]
        private ContentSizeFitter _raceOrderRewardParentContentSizeFitter = null;

        [SerializeField]
        private ContentSizeFitter _raceOrderRewardGridContentSizeFitter = null;
        
        [SerializeField]
        private RaceResultItemList _mainRewardItemList = null;
        
        [SerializeField]
        private RaceResultItemList _raceOrderRewardItemList = null;

        [SerializeField] 
        private Transform[] _rebuildStartArray = null;

        private TweenAnimationTimelineComponent _inAnimation = null;
        private TweenAnimationTimelineComponent _loopAnimation = null;
        private GameObject _resultRankAnimationObject = null;
        
        private Sequence _sequence = null;
        private Action _onComplete = null;
        private int _rank;
        private string _loopAnimationName;
        private string _inAnimationName;
        private Canvas _resultRankCanvas;
        private bool _isSummary;
        private Sequence _playInRaceOrderRewardSequence = null;
        private Sequence _playInMainRewardSequence = null;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.COMMON_RACE_RESULT_RANK_ANIMATION);
            ItemIcon.RegisterPath(register);
        }
        
        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="skipNum"></param>
        /// <param name="raceSkipReward"></param>
        /// <param name="dialog"></param>
        /// <param name="isSummary"></param>
        public void Setup(RaceSkipReward raceSkipReward, DialogCommon dialog, int skipNum = 0, bool isSummary = false)
        {
            _isSummary = isSummary;
            if (_isSummary)
            {
                // 合計報酬
                _skipCountLabel.text = TextId.Race352007.Text();
                _resultRankFlashParentTransform.gameObject.SetActiveWithCheck(false);
                _skipCountLabelBackground.sprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.TXT_BASE_07);
                _mainRewardLabelBackground.sprite = UIManager.PreInAtlas.GetSprite(AtlasSpritePath.PreIn.TXT_BASE_11);
                _raceOrderRewardLabelBackground.sprite = UIManager.PreInAtlas.GetSprite(AtlasSpritePath.PreIn.TXT_BASE_11);
            }
            else
            {
                // 順位
                _rank = raceSkipReward.rank;

                // スキップ回数
                _skipCountLabel.text = TextId.Race352006.Format(skipNum);
                // 結果順位フラッシュのセットアップ
                SetupResultRank(dialog);
            }

            // 報酬リストのセットアップ
            SetupList(raceSkipReward);
        }

        /// <summary>
        /// 結果順位アニメーションのセットアップ
        /// </summary>
        /// <param name="dialogCommon"></param>
        private void SetupResultRank(DialogCommon dialogCommon)
        {
            _resultRankFlashParentTransform.gameObject.SetActiveWithCheck(true);
            
            var animationPrefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COMMON_RACE_RESULT_RANK_ANIMATION, dialogCommon.DialogData.DialogHash);
            _resultRankAnimationObject = GameObject.Instantiate(animationPrefab, _resultRankFlashParentTransform.transform);

            SetupParticle(_resultRankAnimationObject.transform);

            _inAnimationName = GetResultRankInAnimationName();
            _loopAnimationName = GetResultRankLoopAnimationName();
            var animationArray = _resultRankAnimationObject.GetComponentsInChildren<TweenAnimationTimelineComponent>();

            for (int i = 0; i < animationArray.Length; i++)
            {
                // 自動再生・開始は切る
                animationArray[i].IsAutoPlay = false;
                animationArray[i].IsAutoPlayOnEnable = false;
                animationArray[i].IsAutoPlayLoop = false;

                if (animationArray[i].TimelineName == _inAnimationName)
                {
                    // inアニメーション取得
                    _inAnimation = animationArray[i];
                }
                else if (animationArray[i].TimelineName == _loopAnimationName)
                {
                    // loopアニメーション取得
                    _loopAnimation = animationArray[i];
                }
                else
                {
                    // 他の順位、不要なアニメーションは無効化
                    animationArray[i].enabled = false;
                }
            }

            // アニメーション再生時にアクティブだったオブジェクトの画像が
            // 一瞬表示されるので全て非アクティブにしておく
            for (int i = 0, n = _resultRankAnimationObject.transform.childCount; i < n; ++i)
            {
                var childTransform = _resultRankAnimationObject.transform.GetChild(i);
                childTransform.gameObject.SetActiveWithCheck(false);
            }

            _resultRankAnimationObject.SetActiveWithCheck(false);

            // ４着以下の画像を設定しておく
            if (RaceDefine.RANK_4TH <= _rank)
            {
               var rankTextObj = GameObjectUtil.FindInDeepChildren(_resultRankAnimationObject, RESULT_RANK_TEXT_OBJ_NAME);
               var imageCommon = rankTextObj.GetComponent<ImageCommon>();
               imageCommon.sprite = RaceUtil.GetRankSprite(_rank - 1);
            }

            // パーティクルシステムの設定
            void SetupParticle(Transform targetTransform)
            {
                var parentCanvas = transform.GetComponentInParent<Canvas>();

                ParticleSystem[] particleSystemArray = targetTransform.GetComponentsInChildren<ParticleSystem>(true);
                if (!particleSystemArray.IsNullOrEmpty())
                {
                    for (int i = 0; i < particleSystemArray.Length; i++)
                    {
                        var particleSystem = particleSystemArray[i];
                        var particleSystemRenderer = particleSystem.GetComponent<ParticleSystemRenderer>();
                        particleSystemRenderer.sortingOrder = parentCanvas.sortingOrder;
                        particleSystemRenderer.sortingLayerName = parentCanvas.sortingLayerName;
                    }
                }
            }
        }

        /// <summary>
        /// 報酬リスト要素のセットアップ
        /// </summary>
        /// <param name="raceSkipReward"></param>
        private void SetupList(RaceSkipReward raceSkipReward)
        {
            var infoList = new List<RaceResultItemList.RewardItemInfo>();

            // RewardItemInfo情報に変換してリストに追加する
            void AddRewardItemInfo(RaceRewardData[] rewardData, System.Func<RaceRewardData, RaceResultItemList.RewardItemInfo> createFunc = null)
            {
                if (rewardData.IsNullOrEmpty()) return;

                if (createFunc == null)
                {
                    // Default
                    createFunc = data => RaceResultItemList.RewardItemInfo.Create(data);
                }

                infoList.AddRange(rewardData.Select(data => createFunc(data)));
            }
            
            // 通常（出走）報酬
            AddRewardItemInfo(raceSkipReward.normal_reward_array);
            
            _mainRewardItemList.Initialize(infoList, parentCanvas:UIManager.DialogCanvas, isEvacuationInfoPop: true);
            if (infoList.Count > 0)
            {
                _mainRewardItemList.Initialize(infoList, parentCanvas:UIManager.DialogCanvas, isEvacuationInfoPop:true);
            }
            else
            {
                _mainRewardItemList.SetActiveWithCheck(false);
                _mainRewardLabelObject.SetActiveWithCheck(false);
            }

            infoList.Clear();

            // 着順報酬
            AddRewardItemInfo(raceSkipReward.rare_reward_array);

            // プラスボーナス報酬
            AddRewardItemInfo(raceSkipReward.bonus_reward_array, data => RaceResultItemList.RewardItemInfo.CreateAtPlusBonus(data));
            
            if (infoList.Count > 0)
            {
                _raceOrderRewardItemList.Initialize(infoList, parentCanvas:UIManager.DialogCanvas, isEvacuationInfoPop:true);
            }
            else
            {
                _raceOrderRewardItemList.SetActiveWithCheck(false);
                _raceOrderRewardLabelObject.SetActiveWithCheck(false);
            }
            
            // レイアウトを調整
            Transform next = null;
            foreach (var start in _rebuildStartArray)
            {
                next = start;
                do
                {
                    LayoutRebuilder.ForceRebuildLayoutImmediate(next.GetComponent<RectTransform>());
                    next = next.parent;
                } while (transform.parent != next.transform);
            }

            // 自動レイアウトを戻す
            SwitchEnableAutoLayout(false);
            SwitchEnableAutoLayout(true);
        }

        /// <summary>
        /// 順位INアニメーション名取得
        /// </summary>
        /// <returns></returns>
        private string GetResultRankInAnimationName()
        {
            if (_rank < RaceDefine.RANK_4TH)
            {
                return TextUtil.Format(RESULT_RANK_IN_ANIMATION_FORMAT, (_rank - 1));
            }

            return TextUtil.Format(RESULT_RANK_IN_ANIMATION_FORMAT, RESULT_RANK_UNDER_FOURTH_ANIMATION_NUM);
        }

        /// <summary>
        /// 順位LOOPアニメーション名取得
        /// </summary>
        /// <returns></returns>
        private string GetResultRankLoopAnimationName()
        {
            if (_rank < RaceDefine.RANK_4TH)
            {
                return TextUtil.Format(RESULT_RANK_LOOP_ANIMATION_FORMAT, (_rank - 1));
            }

            return TextUtil.Format(RESULT_RANK_LOOP_ANIMATION_FORMAT, RESULT_RANK_UNDER_FOURTH_ANIMATION_NUM);
        }

        /// <summary>
        /// 結果順位の演出再生
        /// </summary>
        private void PlayResultRank()
        {
            _resultRankAnimationObject.SetActiveWithCheck(true);

            if (_inAnimation != null)
            {
                // ヒエラルキーの順序から「着順報酬」のラベルが被るので再生時は描画順を無理やり変える
                var parentCanvas = transform.GetComponentInParent<Canvas>();
                _resultRankCanvas = _resultRankAnimationObject.AddComponent<Canvas>();
                _resultRankCanvas.overrideSorting = true;
                _resultRankCanvas.sortingLayerName = parentCanvas.sortingLayerName;
                // refs #138294 同値のSortingOrderの描画順は不定なので確実に前面で描画するため親Canvas + 1のSortingOrderを設定する
                _resultRankCanvas.sortingOrder = parentCanvas.sortingOrder + 1;

                // 順位アニメーション再生
                _inAnimation.Stop(true);
                _inAnimation.Play(_inAnimationName, () =>
                {
                    // アニメーション終わったらSortの上書きを戻す
                    _resultRankCanvas.overrideSorting = false;

                    if (_loopAnimation != null && !_loopAnimation.IsPlaying())
                    {
                        _loopAnimation.PlayLoop(_loopAnimationName);
                    }
                });

                // SE再生
                PlayResultRankSe();
            }
        }

        /// <summary>
        ///  結果順位の演出を強制終了させる
        /// </summary>
        public void ForceCompleteResultRank()
        {
            if (_isSummary)
            {
                // 合計報酬の箇所は結果順位なし
                return;
            }

            _resultRankAnimationObject.SetActiveWithCheck(true);

            if (_inAnimation != null && _inAnimation.IsPlaying(_inAnimationName))
            {
                _inAnimation.Complete(_inAnimationName, true);
            }
            else if (_inAnimation != null && !_inAnimation.IsPlaying(_inAnimationName)
            && _loopAnimation != null && !_loopAnimation.IsPlaying(_loopAnimationName))
            {
                _inAnimation.Play(_inAnimationName);
                _inAnimation.Complete(_inAnimationName, true);
            }

            if (_loopAnimation != null && !_loopAnimation.IsPlaying())
            {
                _loopAnimation.PlayLoop(_loopAnimationName);
            }

            if (_resultRankCanvas != null)
            {
                _resultRankCanvas.overrideSorting = false;
            }
        }

        /// <summary>
        /// SE再生
        /// </summary>
        private void PlayResultRankSe()
        {
            var seCuename = string.Empty;
            if (_rank == RaceDefine.RANK_1ST)
            {
                seCuename = SE_CUENAME_DAILY_RACE_SKIP_RANK_1;
            }
            else if (RaceDefine.RANK_2ND <= _rank && _rank <= RaceDefine.RANK_5TH)
            {
                seCuename = SE_CUENAME_DAILY_RACE_SKIP_RANK_2;
            }
            else if (RaceDefine.RANK_6TH <= _rank)
            {
                seCuename = SE_CUENAME_DAILY_RACE_SKIP_RANK_3;
            }

            AudioManager.Instance.PlaySe(ResourcePath.RACE_SE_CUESHEET_NAME, seCuename);
        }

        /// <summary>
        /// スキップ１回目のみの演出
        /// </summary>
        /// <param name="onComplete"></param>
        public void PlayInFirst(Action onComplete = null)
        {
            _onComplete = onComplete;
            
            _sequence = DOTween.Sequence();
            {
                // スキップ何回目か
                _sequence.Join(TweenAnimationBuilder.CreateSequence(_skipCountLabelObject, TweenAnimation.PresetType.PartsInMoveAndFade));
                _sequence.AppendInterval(PLAY_IN_INTERVAL_SHORT);
 
                // 順位
                _sequence.AppendCallback(PlayResultRank);
                _sequence.AppendInterval(PLAY_IN_INTERVAL_SHORT);

                // 出走報酬(表題)
                _sequence.Append(TweenAnimationBuilder.CreateSequence(_mainRewardLabelObject, TweenAnimation.PresetType.PartsInMoveAndFade));
                _sequence.AppendInterval(PLAY_IN_INTERVAL_SHORT);

                // 出走報酬アイテム、着順報酬(表題)
                _sequence.AppendCallback(() =>
                {
                    PlayInMainRewardList(() => {});
                });

                _sequence.Join(TweenAnimationBuilder.CreateSequence(_raceOrderRewardLabelObject, TweenAnimation.PresetType.PartsInMoveAndFade));
                _sequence.AppendInterval(PLAY_IN_INTERVAL_SHORT);

                // 着順報酬アイテム
                _sequence.AppendCallback(() =>
                {
                    PlayInRaceOrderRewardList(() =>
                    {
                        DOVirtual.DelayedCall(DELAY_AUTO_LAYOUT, (() =>
                        {
                            // 自動レイアウト戻す
                            SwitchEnableAutoLayout(true);
                            
                            _onComplete?.Invoke();
                        }));

                        ForceCompleteResultRank();
                    });
                });
            }
            // 演出の為に自動レイアウト切る
            SwitchEnableAutoLayout(false);
            _sequence.Play();
        }

        /// <summary>
        /// スキップ２回目以降の演出
        /// </summary>
        /// <param name="onComplete"></param>
        public void PlayIn(Action onComplete)
        {
            _onComplete = onComplete;
            
            _sequence = DOTween.Sequence();
            _sequence.OnStart(PlayResultRank);
            
            _sequence.AppendInterval(PLAY_IN_INTERVAL_LONG);
            
            _sequence.AppendCallback(() =>
            {
                PlayInMainRewardList(() =>
                {
                    DOVirtual.DelayedCall(DELAY, (() =>
                    {
                        PlayInRaceOrderRewardList(() =>
                        {
                            _raceOrderRewardGridContentSizeFitter.enabled = true;
                            _mainRewardGridContentSizeFitter.enabled = true;

                            ForceCompleteResultRank();

                            _onComplete?.Invoke();
                        });
                    }));
                });
            });
            _raceOrderRewardGridContentSizeFitter.enabled = false;
            _mainRewardGridContentSizeFitter.enabled = false;
            _sequence.Play();
        }

        /// <summary>
        /// 最後合計報酬の演出
        /// </summary>
        /// <param name="onComplete"></param>
        public void PlayInTotalReward(Action onComplete, ScrollRectCommon targetScroll = null)
        {
            _onComplete = onComplete;

            _sequence = DOTween.Sequence();
            _sequence.OnStart(() =>
            {
                PlayInMainRewardList(() =>
                {
                    PlayInRaceOrderRewardList(() =>
                    {
                        _raceOrderRewardGridContentSizeFitter.enabled = true;
                        _mainRewardGridContentSizeFitter.enabled = true;

                        ForceCompleteResultRank();

                        _onComplete?.Invoke();
                    });
                }, targetScroll);
            });
            _raceOrderRewardGridContentSizeFitter.enabled = false;
            _mainRewardGridContentSizeFitter.enabled = false;
            _sequence.Play();
        }

        /// <summary>
        /// 自動レイアウト切り替え
        /// </summary>
        /// <param name="isEnable"></param>
        private void SwitchEnableAutoLayout(bool isEnable)
        {
            if (isEnable)
            {
                _mainRewardVerticalLayoutGroup.enabled = true;
                _mainRewardGridContentSizeFitter.enabled = true;
                _mainRewardParentContentSizeFitter.enabled = true;
                _raceOrderRewardVerticalLayoutGroup.enabled = true;
                _raceOrderRewardGridContentSizeFitter.enabled = true;
                _raceOrderRewardParentContentSizeFitter.enabled = true;
                _verticalLayoutGroup.enabled = true;
                _contentSizeFitter.enabled = true;
            }
            else
            {
                _contentSizeFitter.enabled = false;
                _verticalLayoutGroup.enabled = false;
                _raceOrderRewardParentContentSizeFitter.enabled = false;
                _raceOrderRewardGridContentSizeFitter.enabled = false;
                _raceOrderRewardVerticalLayoutGroup.enabled = false;
                _mainRewardParentContentSizeFitter.enabled = false;
                _mainRewardGridContentSizeFitter.enabled = false;
                _mainRewardVerticalLayoutGroup.enabled = false;
            }
        }

        /// <summary>
        /// 出走報酬アイテム
        /// </summary>
        /// <param name="onComplete"></param>
        public void PlayInMainRewardList(TweenCallback onComplete, ScrollRectCommon targetScroll = null)
        {
            _playInMainRewardSequence = DOTween.Sequence();
            if (_mainRewardItemList.gameObject.activeSelf)
            {
                _playInMainRewardSequence.Append(_mainRewardItemList.Play(targetScroll: targetScroll, eachItemPlayInterval: PLAY_IN_INTERVAL_SHORT));
            }
            _playInMainRewardSequence.AppendCallback(onComplete);
            _playInMainRewardSequence.Play();
        }
        
        /// <summary>
        /// 着順報酬アイテム
        /// </summary>
        /// <param name="onComplete"></param>
        public void PlayInRaceOrderRewardList(TweenCallback onComplete) 
        {
            _playInRaceOrderRewardSequence = DOTween.Sequence();
            if (_raceOrderRewardItemList.gameObject.activeSelf)
            {
                _playInRaceOrderRewardSequence.Append(_raceOrderRewardItemList.Play(eachItemPlayInterval: PLAY_IN_INTERVAL_SHORT));

                if (!_isSummary)
                {
                    _playInRaceOrderRewardSequence.AppendInterval(WAIT_ICON_PLAY);
                    _playInRaceOrderRewardSequence.AppendCallback(SkipPlayInRewardList);
                }
            }
            _playInRaceOrderRewardSequence.AppendCallback(onComplete);
            _playInRaceOrderRewardSequence.Play();
        }

        /// <summary>
        /// 再生中の演出を終了、スキップ表示する
        /// </summary>
        public void SkipPlayInReward()
        {
            _sequence?.Complete();
            _playInMainRewardSequence?.Complete();
            _playInRaceOrderRewardSequence?.Complete();
            ForceCompleteResultRank();
            SkipPlayInRewardList();
            SwitchEnableAutoLayout(true);
        } 
        
        /// <summary>
        /// 報酬演出スキップ、報酬アイコンを即座に表示する
        /// </summary>
        public void SkipPlayInRewardList()
        {
            _mainRewardItemList.SkipFlashItemList();
            _raceOrderRewardItemList.SkipFlashItemList();
        }

        /// <summary>
        /// 演出停止
        /// </summary>
        public void StopEffect()
        {
            if (_sequence != null && _sequence.IsPlaying())
            {
                _sequence.Kill();
                _sequence = null;
            }

            if (_inAnimation != null && _inAnimation.IsPlaying())
            {
                _inAnimation.Stop();
                _inAnimation = null;
            }
            
            if (_loopAnimation != null && _loopAnimation.IsPlaying())
            {
                _loopAnimation.Stop();
                _loopAnimation = null;
            }
        }

        private void OnDestroy()
        {
            StopEffect();
        }
    }
}