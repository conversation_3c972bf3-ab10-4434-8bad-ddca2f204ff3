using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    namespace Model.Component
    {
        /// <summary>
        /// 育成TOPやトレーニング画面の背景キャラに使える丸影
        /// キャラの動きに合わせた丸影アニメーションをさせる用途
        /// </summary>
        [AddComponentMenu("")]
        public class SingleModeModelCircleShadowController : ShadowController
        {
            private GameObject _locator;
            private Animator _animator;
            private SimplePlayableAnimator _playableAnimator;
            private const string SHADOW_LOCATOR_NAME = "ShadowLocator";

            #region ModelComponent
            public override void OnInitialize()
            {
                base.OnInitialize();
                SetMaterial(false);
                _color = GameDefine.COLOR_BLACK;
                _shadowTransform.localScale = new Vector3(BASESCALE, BASESCALE, BASESCALE);
                _shadowTransform.localPosition = new Vector3(BASEOFFSET_X, BASEOFFSET_Y, RaceShadowController.CIRCLE_SHADOW_OFFSET_Z);
                
                var owner = GetOwner();

                _locator = new GameObject(SHADOW_LOCATOR_NAME);
                _locator.gameObject.layer = owner.transform.gameObject.layer;
                _locator.transform.SetParent(owner.transform);
                _locator.transform.Initialize();

                _shadowTransform.SetParent(_locator.transform, false);

                _animator = _locator.AddComponent<Animator>();
                _playableAnimator = new SimplePlayableAnimator(_animator);
            }

            protected override void OnUpdate()
            {
                if (!IsEnable) return;
                UpdateMotion();
            }
            
            public override void OnDestroy()
            {
                base.OnDestroy();
                
                _playableAnimator?.Release();
                _playableAnimator = null;
                
                if (_locator != null)
                {
                    GameObject.Destroy(_locator);
                    _locator = null;
                }
            }
            
            #endregion

            private void UpdateMotion()
            {
                if (_playableAnimator == null || _animator.enabled == false) return;
                var owner = GetOwner();
                _playableAnimator.UpdateMotion(owner.DeltaTime);
                _playableAnimator.Evaluate();
            }


            /// <summary>
            /// 見た目含めてON・OFF切り替え
            /// </summary>
            /// <param name="enable"></param>
            public void SetEnable(bool enable)
            {
                IsEnable = enable;
                IsVisible = enable;
            }

            /// <summary>
            /// アニメーション丸影か、動かない丸影か切り替える
            /// </summary>
            public void SetEnableAnimation(bool enable)
            {
                _animator.enabled = enable;
                if (enable)
                {
                    _playableAnimator.PlayGraph();
                }
                else
                {
                    _playableAnimator.StopGraph();
                    _locator.transform.Initialize();//位置リセット（動かない丸影に戻した時の位置ずれ修正）
                }
            }
            
            /// <summary>
            /// モーションセットに対応したPositionアニメーションをロードして再生設定する
            /// </summary>
            public void SetAnimation(MasterCharaMotionSet.CharaMotionSet motionSet, ResourceManager.ResourceHash resourceHash)
            {
                var motionNameList = new List<string>();
                if (!SimpleModelController.SetCommonPositionMotionNameList(motionSet, motionNameList))
                {
                    return;
                }
                switch (motionSet.BodyMotionPlayTypeEnum)
                {
                    // 単発
                    case SimpleModelController.BodyMotionPlayType.Single:
                        PlayAnimationClip(
                            SimplePlayableAnimator.State.Motion_Single, 
                            ResourceManager.LoadOnHash<AnimationClip>(motionNameList[0], resourceHash));
                        break;
                    
                    // Start,Loop,End
                    case SimpleModelController.BodyMotionPlayType.Triple:
                        PlayAnimationClip(
                            SimplePlayableAnimator.State.Motion_Start, 
                            ResourceManager.LoadOnHash<AnimationClip>(motionNameList[0], resourceHash));
                        _playableAnimator.SetClip(SimplePlayableAnimator.State.Motion_Middle, 
                            ResourceManager.LoadOnHash<AnimationClip>(motionNameList[1], resourceHash));
                        _playableAnimator.SetClip(SimplePlayableAnimator.State.Motion_End, 
                            ResourceManager.LoadOnHash<AnimationClip>(motionNameList[2], resourceHash));
                        break;
                        
                    // Loop
                    case SimpleModelController.BodyMotionPlayType.Loop:
                        PlayAnimationClip(
                            SimplePlayableAnimator.State.Motion_Middle, 
                            ResourceManager.LoadOnHash<AnimationClip>(motionNameList[0], resourceHash));
                        break;
                }
            }
            
            /// <summary>
            /// AnimationClip再生
            /// </summary>
            private void PlayAnimationClip(SimplePlayableAnimator.State state, AnimationClip clip)
            {
                _playableAnimator.SetClip(state, clip);
                _playableAnimator.Play(SimplePlayableAnimator.Layer.Base, state, 0);
            }
        }
    }
}
