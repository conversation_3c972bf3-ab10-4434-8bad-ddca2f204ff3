using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 指定因子確認のUIパーツ
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeConfirmCompleteFactorOrderConfirm : MonoBehaviour
    {
        #region SerializeField
        
        [SerializeField] private GameObject _emptyRoot;
        [SerializeField] private GameObject _normalRoot;

        [SerializeField] private PartsFactorListContent _factorContent;
        [SerializeField] private ButtonCommon _factorChangeButton;
        [SerializeField] private ButtonCommon _factorChangeEmptyButton;
        
        private SingleModeFactorOrderInfoModel _factorOrderInfo;
        private Action _onSelectFactor;

        #endregion

        public void Setup(SingleModeFactorOrderInfoModel factorOrderInfo, Action onSelectFactor)
        {
            _factorOrderInfo = factorOrderInfo;
            _onSelectFactor = onSelectFactor;
            SetupRoot();
            SetupFactorContent();
            // 指定因子変更
            _factorChangeButton.SetOnClick(OnClickFactorChange);
            _factorChangeEmptyButton.SetOnClick(OnClickFactorChange);
        }

        private void OnClickFactorChange()
        {
            DialogSingleModeConfirmCompleteFactorOrderChange.PushDialog(_factorOrderInfo.CurrentFactorGroupId, selectedFactorGroupId =>
            {
                _factorOrderInfo.CurrentFactorGroupId = selectedFactorGroupId;
                _onSelectFactor?.Invoke();
                SetupRoot();
                SetupFactorContent();
            });
        }
        

        private void SetupRoot()
        {
            var isEmpty = _factorOrderInfo.IsEmptyCurrentFactorGroupId;
            _emptyRoot.SetActive(isEmpty);
            _normalRoot.SetActive(!isEmpty);
        }

        private void SetupFactorContent()
        {
            var masterFactor = MasterDataManager.Instance.masterSuccessionFactor.GetWithFactorGroupIdAndRarity(_factorOrderInfo.CurrentFactorGroupId, (int)GameDefine.FactorRarity.Rare1);
            _factorContent.Setup(masterFactor,0, SingleModeDefine.SuccessionEffectedPosition.None, false);
            _factorContent.SetFactorOrderBadge(_factorOrderInfo.CurrentFactorGroupId);// 指定因子バッジ
            _factorContent.SetFrameWidth((_factorContent.transform as RectTransform).sizeDelta.x);
            _factorContent.SetVisibleRarityStar(false);
            _factorContent.SetOnClickInfoButton(() => DialogSingleModeFactorDetail.Open(masterFactor, 0, showRarity:false));
        }
    }
}