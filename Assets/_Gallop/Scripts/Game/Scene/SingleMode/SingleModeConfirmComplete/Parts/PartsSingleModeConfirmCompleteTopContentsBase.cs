using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成完了確認ダイアログの上部コンテンツ
    /// ライブ編時は派生する
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeConfirmCompleteTopContentsBase : MonoBehaviour
    {
        [SerializeField] private TextCommon _remainSkillPtLabel;

        public virtual void Setup()
        {
            _remainSkillPtLabel.text = TextId.Friend0059.Format(WorkDataManager.Instance.SingleMode.Character.SkillPoint);
        }
    }
}