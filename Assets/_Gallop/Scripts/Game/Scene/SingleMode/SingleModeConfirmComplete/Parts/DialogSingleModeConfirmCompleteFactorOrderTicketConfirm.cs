using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// チケットによる指定因子確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeConfirmCompleteFactorOrderTicketConfirm : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON; 
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        [SerializeField] private PartsSingleModeConfirmCompleteFactorOrderConfirm _factorContent;
        [SerializeField] private TextCommon _mainText;
        [SerializeField] private TextCommon _subText;
        [SerializeField] private TextCommon _currentValueText = null;
        [SerializeField] private TextCommon _afterValueText = null;
        [SerializeField] private ImageCommon _itemIcon;

        private SingleModeFactorOrderInfoModel _factorOrderInfo;

        
        public static void PushDialog(SingleModeFactorOrderInfoModel factorOrderInfo, System.Action onDecide)
        {
            const string PATH = ResourcePath.SINGLE_MODE_CONFIRM_COMPLETE_UI_ROOT + "DialogSingleModeConfirmCompleteFactorOrderTicketConfirm";
            var content = LoadAndInstantiatePrefab<DialogSingleModeConfirmCompleteFactorOrderTicketConfirm>(PATH);
            var dialogData = content.CreateDialogData();
            dialogData.Title = TextId.SingleMode194105.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0023.Text();
            dialogData.RightButtonCallBack = _ => onDecide?.Invoke();
            content._factorOrderInfo = factorOrderInfo;
            DialogManager.PushDialog(dialogData);
            content.Setup();
        }

        private void Setup()
        {
            SetupDialogButton();
            _factorContent.Setup(_factorOrderInfo, SetupDialogButton);
            
            var itemNum = WorkDataManager.Instance.ItemData.GetHaveItemNum(GameDefine.FACTOR_ORDER_TICKET_ID);
            var itemName = TextUtil.GetMasterText(MasterString.Category.MasterItemName, GameDefine.FACTOR_ORDER_TICKET_ID);
            var itemCostNum = _factorOrderInfo.RequestCost;
            var itemSpriteName = AtlasSpritePath.Common.GetItemIconByItemCategory(GameDefine.ItemCategory.FACTOR_ORDER_TICKET);
            
            _mainText.text = TextId.SingleMode194114.Format(itemName, itemCostNum);
            _subText.text = TextId.SingleMode539049.Format(itemName);
            
            // アイテム数表示
            _currentValueText.text = itemNum.ToString();
            _afterValueText.text = (itemNum - itemCostNum).ToString();
            
            // アイテムアイコン
            _itemIcon.sprite = UIManager.CommonAtlas.GetSprite(itemSpriteName);
        }
        
        
        /// <summary>
        /// ダイアログボタンの更新
        /// </summary>
        private void SetupDialogButton()
        {
            var dialog = GetDialog() as DialogCommon;
            var isEnable = !_factorOrderInfo.IsEmptyCurrentFactorGroupId;
            dialog.ChangeRightButtonInteractable(isEnable);
            dialog.SetRightButtonNotificationMessage(isEnable ? string.Empty : TextId.SingleMode194117.Text());
        }

    }
}
