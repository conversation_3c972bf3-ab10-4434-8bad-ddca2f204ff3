using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 指定因子パスによる指定因子確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeConfirmCompleteFactorOrderPassConfirm : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON; 
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region SerializeField

        [SerializeField] private PartsSingleModeConfirmCompleteFactorOrderConfirm _factorContent;
        [SerializeField] private TextCommon _lastTime;
        [SerializeField] private TextCommon _currentValueText;
        [SerializeField] private TextCommon _afterValueText;
        
        #endregion

        #region Member

        private SingleModeResultFactorOrderPassRemainTimeModel _factorPassRemainTimeModel;
        private SingleModeFactorOrderInfoModel _factorOrderInfo;

        #endregion
        
        public static DialogCommon PushDialog(SingleModeFactorOrderInfoModel factorOrderInfo, System.Action onDecide)
        {
            const string PATH = ResourcePath.SINGLE_MODE_CONFIRM_COMPLETE_UI_ROOT + "DialogSingleModeConfirmCompleteFactorOrderPassConfirm";
            var content = LoadAndInstantiatePrefab<DialogSingleModeConfirmCompleteFactorOrderPassConfirm>(PATH);
            var dialogData = content.CreateDialogData();
            dialogData.Title = TextId.SingleMode194105.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0023.Text();
            dialogData.RightButtonCallBack = _ => onDecide?.Invoke();
            content._factorOrderInfo = factorOrderInfo;
            var dialog =  DialogManager.PushDialog(dialogData);
            content.Setup();
            return dialog;
        }

        private void Setup()
        {
            SetupDialogButton();
            _factorContent.Setup(_factorOrderInfo, SetupDialogButton);

            _factorPassRemainTimeModel = new SingleModeResultFactorOrderPassRemainTimeModel();
            _lastTime.text = _factorPassRemainTimeModel.RemainTimeText;

            int currentJewel = WorkDataManager.Instance.UserData.CarrotStone.TotalCoin;
            // ジュエル数は変化しない
            _currentValueText.text = _afterValueText.text = currentJewel.ToCommaSeparatedString();
        }

        private void Update()
        {
            if (_factorPassRemainTimeModel.IsExpired == false)
            {
                // 因子再獲得パスの残り時間更新
                _lastTime.text = _factorPassRemainTimeModel.RemainTimeText;
            }
        }
        
        /// <summary>
        /// ダイアログボタンの更新
        /// </summary>
        private void SetupDialogButton()
        {
            var dialog = GetDialog() as DialogCommon;
            var isEnable = !_factorOrderInfo.IsEmptyCurrentFactorGroupId;
            dialog.ChangeRightButtonInteractable(isEnable);
            dialog.SetRightButtonNotificationMessage(isEnable ? string.Empty : TextId.SingleMode194117.Text());
        }
    }
}
