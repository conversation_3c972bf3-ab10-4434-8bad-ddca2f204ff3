using UnityEngine;
using System;

namespace Gallop
{
    /// <summary>
    /// 指定因子確認ダイアログ（ジュエル）
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeConfirmCompleteFactorOrderConfirm : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region SerializeField
        
        [SerializeField] private PartsSingleModeConfirmCompleteFactorOrderConfirm _factorContent;

        [Header("通常の石消費UI")]
        [SerializeField] private GameObject _defaultRoot;

        [SerializeField] private TextCommon _mainText;
        [SerializeField] private TextCommon _currentStoneSumText;
        [SerializeField] private TextCommon _currentStoneDetailText;
        [SerializeField] private TextCommon _afterStoneSumText;
        [SerializeField] private TextCommon _afterStoneDetailText;
        [SerializeField] private TextCommon _warningText;
        [SerializeField] private ButtonCommon _factorPassbutton;
        
        [Header("石が足りないUI")]
        [SerializeField] private GameObject _shortageRoot;
        [SerializeField] private RectTransform _shortageFactorTargetRoot;
        [SerializeField] private TextCommon _shortageMainText;
        [SerializeField] private TextCommon _shortageCurrentStoneSumText;
        [SerializeField] private TextCommon _shortageCurrentStoneDetailText;
        [SerializeField] private TextCommon _shortageWarningText;
        
        [Header("共通UI")]
        [SerializeField] private ButtonCommon _tokuteishotorihikiButton;
        
        private Action _onPassPurchaseCompleted;
        private bool _isPassPurchased;
        private int CarrotStoneCost => _factorOrderInfo.RequestCost;
        private SingleModeFactorOrderInfoModel _factorOrderInfo;
        private bool _isShortage;

        #endregion

        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(SingleModeFactorOrderInfoModel factorOrderInfo, bool isShortage, Action onDecide, Action onPremiumPassPurchaseCompleted)
        {
            const string PATH = ResourcePath.SINGLE_MODE_CONFIRM_COMPLETE_UI_ROOT + "DialogSingleModeConfirmCompleteFactorOrderConfirm";
            var content = LoadAndInstantiatePrefab<DialogSingleModeConfirmCompleteFactorOrderConfirm>(PATH);
            var dialogData = content.CreateDialogData();
            dialogData.Title = TextId.SingleMode194105.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = isShortage ? TextId.SingleMode539040.Text() : TextId.Common0023.Text();
            dialogData.RightButtonCallBack = _ => { onDecide?.Invoke();};
            content._factorOrderInfo = factorOrderInfo;
            content._onPassPurchaseCompleted = onPremiumPassPurchaseCompleted;
            DialogManager.PushDialog(dialogData);
            content.Setup(isShortage);
        }

        private void Setup(bool isShortage)
        {
            _isShortage = isShortage;
            SetupDialogButton();
            _factorContent.Setup(_factorOrderInfo, SetupDialogButton);
            
            _defaultRoot.SetActiveWithCheck(!isShortage);
            _shortageRoot.SetActiveWithCheck(isShortage);

            if (isShortage)
            {
                // ジュエルが足りないセットアップ
                SetupShortage();
            }
            else
            {
                // ジュエルあるセットアップ
                SetupDefault();
            }
            // 特定商取引法ダイアログ
            _tokuteishotorihikiButton.SetOnClick(PaymentUtility.OpenTokushoWebviewDialog);
        }

        /// <summary>
        /// ダイアログボタンの更新
        /// </summary>
        private void SetupDialogButton()
        {
            if (_isShortage) return;// ジュエル不足時は購入ボタンは常に有効なのでこのあとの処理はスキップ
            var dialog = GetDialog() as DialogCommon;
            var isEnable = !_factorOrderInfo.IsEmptyCurrentFactorGroupId;
            dialog.ChangeRightButtonInteractable(isEnable);
            dialog.SetRightButtonNotificationMessage(isEnable ? string.Empty : TextId.SingleMode194117.Text());
        }

        /// <summary>
        /// 通常の有償・無償石消費セットアップ
        /// </summary>
        private void SetupDefault()
        {
            _mainText.text = TextId.SingleMode194112.Format(TextId.Outgame0150.Text(), CarrotStoneCost);
            
            TextUtil.SetWarningTextWithIcon(_warningText, TextId.Gacha0107.Text());
            
            // 現在の石情報
            var stoneData = WorkDataManager.Instance.UserData.CarrotStone;
            SetupStoneText(_currentStoneSumText, _currentStoneDetailText,
                stoneData.TotalCoin, stoneData.ChargeCoin, stoneData.FreeCoin);

            // 消費後の石情報
            var afterTotalCoin = stoneData.TotalCoin - CarrotStoneCost;
            var afterFreeCoin = stoneData.FreeCoin;
            var afterChargeCoin = stoneData.ChargeCoin;

            if (CarrotStoneCost > stoneData.FreeCoin)
            {
                // 無償石だけでは足りない場合
                afterFreeCoin = 0;
                afterChargeCoin -= CarrotStoneCost - stoneData.FreeCoin;
            }
            else
            {
                // 無償石だけで足りる場合
                afterFreeCoin -= CarrotStoneCost;
            }
            
            SetupStoneText(_afterStoneSumText, _afterStoneDetailText,
                afterTotalCoin, afterChargeCoin, afterFreeCoin);
            
            // パス購入
            _factorPassbutton.SetOnClick(OnClickBuyPass);
        }
        
        
        /// <summary>
        /// 有償・無償石が足りない表示セットアップ
        /// </summary>
        private void SetupShortage()
        {
            (_factorContent.transform as RectTransform).anchoredPosition = _shortageFactorTargetRoot.anchoredPosition;//指定因子UIの配置調整
            _shortageMainText.text = TextId.SingleMode194111.Format(TextId.Outgame0150.Text(), CarrotStoneCost);
            
            // 現在の石情報
            var stoneData = WorkDataManager.Instance.UserData.CarrotStone;
            SetupStoneText(_shortageCurrentStoneSumText, _shortageCurrentStoneDetailText,
                stoneData.TotalCoin, stoneData.ChargeCoin, stoneData.FreeCoin);
            
            _shortageWarningText.SetTextWithCustomTag(
                TextId.Gacha0083.Format(TextId.Outgame0150.Text()), addSpaceNum: TextUtil.WARNING_SPACE_NUM);
        }
        
        private void SetupStoneText(TextCommon sumText, TextCommon detailText, int sum, int chargeNum, int freeNum)
        {
            sumText.text = sum.ToCommaSeparatedString();
            detailText.text = TextId.Gacha0105.Format(
                chargeNum.ToCommaSeparatedString(),
                freeNum.ToCommaSeparatedString()
            );
        }

        
        private void OnClickBuyPass()
        {
            // ジュエル等の購入ダイアログ
            PaymentUtility.Instance.OpenBuyJewelDialog(
                finish:OnPurchaseFinish,
                removeAllOnFinish:false,
                onClose:OnClosedBuyJewelDialog,
                autoScrollIdList:new []{StaticVariableDefine.Payment.FACTOR_ORDER_PASS_ID});
        }
        /// <summary>
        /// 何かしらの商品が購入されたときのイベント
        /// </summary>
        private void OnPurchaseFinish()
        {
            // 何かしらが購入されたので、最新の因子再獲得パス情報を取得してチェック
            var factorOrderPass = PaymentUtility.Instance.GetFactorOrderPass();
            if (factorOrderPass.IsPurchased)
            {
                _isPassPurchased = true;
            }
        }
        /// <summary>
        /// ジュエル購入ダイアログをとじたときのイベント
        /// </summary>
        private void OnClosedBuyJewelDialog()
        {
            // パスが購入されている場合のみ、イベントを叩いてダイアログを閉じる
            if (_isPassPurchased)
            {
                _onPassPurchaseCompleted?.Invoke();
                GetDialog().Close();
            }
        }
    }
}