using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 指定因子選択トグルUIパーツ
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeConfirmCompleteFactorOrderToggle : MonoBehaviour
    {
        [SerializeField] private ToggleCommon _toggle;
        public ToggleCommon Toggle => _toggle;
        [SerializeField] private ImageCommon _frame;
        [SerializeField] private ImageCommon _icon;
        [SerializeField] private TextCommon _factorName;

        public void Setup(MasterSuccessionFactor.SuccessionFactor factor, bool isOn, bool isEnable)
        {
            _toggle.isOn = isOn;
            _toggle.SetInteractable(isEnable);
            _toggle.SetNotificationMessage(isEnable ? string.Empty : TextId.SingleMode194119.Text());
            var singleAtlas = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleCommon);
            _frame.sprite = singleAtlas.GetSprite(AtlasSpritePath.SingleCommon.GetFactorFrameSmall(factor));
            _icon.sprite = singleAtlas.GetSprite(AtlasSpritePath.SingleCommon.GetFactorGradeIconPath(factor));
            _factorName.text = factor.Name(0);
            _factorName.FontColor = factor.GetFactorNameFontColor();
            _factorName.OnUpdate();
        }
    }
}