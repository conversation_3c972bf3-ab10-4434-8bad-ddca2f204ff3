using System;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 育成完了確認ダイアログ：指定因子機能がついている
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeConfirmCompleteWithFactorOrder : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [Header("上部UI")]

        [SerializeField] private Transform _topContentsParent;

        [Header("「完了する」ボタン")]

        [SerializeField] private RawImageCommon _charaImage;
        [SerializeField] private RawImageCommon _charaImageOutline;
        [SerializeField] private ButtonCommon _completeButton;

        [Header("「指定して完了する」ボタン")]

        [SerializeField] private ButtonCommon _factorOrderButton;
        [SerializeField] private GameObject _factorOrderButtonJewelRoot;
        [SerializeField] private TextCommon _factorOrderButtonJewelText;
        [SerializeField] private GameObject _factorOrderButtonTicketRoot;
        [SerializeField] private TextCommon _factorOrderButtonTicketText;
        [SerializeField] private ImageCommon _factorOrderButtonTicketIcon;
        [SerializeField] private TextCommon _factorOrderTicketNoticeText;
        [SerializeField] private GameObject _factorOrderButtonPassRoot;
        [SerializeField] private TextCommon _factorOrderPassRemainText;
        [SerializeField] private TextCommon _factorOrderButtonPassText;


        private Action _onDecide;
        private SingleModeFactorOrderInfoModel _factorOrderInfo;
        private bool _ignoreUpdateFactorOrderPass; // これがfalseの時、リアルタイム（毎フレーム）のパス期限チェックを行い、期限が切れていたら「有効期限切れ」ダイアログを開く
        private DialogCommon _factorOrderConfirmDialog;

        // ダイアログパス
        private const string DIALOG_PATH = ResourcePath.SINGLE_MODE_CONFIRM_COMPLETE_UI_ROOT + "DialogSingleModeConfirmCompleteWithFactorOrder";
        // TOPコンテンツパス
        private const string TOP_CONTENTS_PATH = ResourcePath.SINGLE_MODE_CONFIRM_COMPLETE_UI_ROOT + "PartsSingleModeConfirmCompleteTopContentsBase";
        // ライブ編：TOPコンテンツパス
        private const string TOP_CONTENTS_SCENARIO_LIVE_PATH_LIVE = ResourcePath.SINGLE_MODE_SCENARIO_LIVE_UI_ROOT + "PartsSingleModeScenarioLiveConfirmCompleteTopContents";


        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(Action onDecide)
        {
            SingleModeAPI.SendFactorOrderLoad(res =>
            {
                PushDialogImpl(onDecide, new SingleModeFactorOrderInfoModel(res.factor_order_info));
            });
        }
        private static void PushDialogImpl(Action onDecide, SingleModeFactorOrderInfoModel factorOrderInfo)
        {
            var content = LoadAndInstantiatePrefab<DialogSingleModeConfirmCompleteWithFactorOrder>(DIALOG_PATH);
            var dialogData = content.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.CenterButtonCallBack = d => d.Close();
            dialogData.Title = TextId.SingleMode0245.Text();
            dialogData.CenterButtonText = TextId.Common0004.Text();
            DialogManager.PushDialog(dialogData);
            content._factorOrderInfo = factorOrderInfo;
            content._onDecide = onDecide;
            content.Setup();
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup()
        {
            SetupTopContentsScenario();
            SetupCompleteButton();
            SetupFactorOrderButton();
        }

        /// <summary>
        ///　シナリオ別セットアップ
        /// </summary>
        private void SetupTopContentsScenario()
        {
            var prefab = ResourceManager.LoadOnHash<GameObject>(GetScenarioPartsPath(), DialogHash);
            var topContents = Instantiate(prefab, _topContentsParent).GetComponent<PartsSingleModeConfirmCompleteTopContentsBase>();
            topContents.Setup();
        }
        /// <summary>
        /// シナリオ別UIパーツパス
        /// </summary>
        private string GetScenarioPartsPath()
        {
            return WorkDataManager.Instance.SingleMode.GetScenarioId() switch
            {
                SingleModeDefine.ScenarioId.Live => TOP_CONTENTS_SCENARIO_LIVE_PATH_LIVE,
                _ => TOP_CONTENTS_PATH,
            };
        }
        
        /// <summary>
        /// 完了するボタンのセットアップ
        /// </summary>
        private void SetupCompleteButton()
        {
            _completeButton.SetOnClick(OnClickComplete);
            
            // 育成キャラのプチキャラ
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var charaImagePath = ResourcePath.GetCharaPetitIconPath(workChara.CharaId, workChara.GetRaceDressId(false), ResourcePath.CharaPetitCatetory.SingleStartOn);
            _charaImageOutline.texture = _charaImage.texture = ResourceManager.LoadOnHash<Texture2D>(charaImagePath, DialogHash);
        }
        
        /// <summary>
        /// 完了ボタン押下
        /// </summary>
        private void OnClickComplete()
        {
            // 「指定因子なし」である事を一時領域に保持
            TempData.Instance.SingleModeData.FactorOrderRequest = null;

            var factorOrderPass = PaymentUtility.Instance.GetFactorOrderPass();
            if (factorOrderPass.IsExpired == false)
            {
                // パスが有効なら、指定因子しないのか確認
                PushDialogNoneFactorOrderConfirm();
                return;
            }

            if (TempData.Instance.SingleModeData.IsDecidedFactorOrder)
            {
                // ユーザーが因子を指定した後なら、指定因子しないのか確認 （例：因子を指定後に、進化スキル選択からキャンセルで戻ってきた場合やエラーで戻された場合）
                PushDialogNoneFactorOrderConfirm();
                return;
            }

            CallOnDecide();
        }

        /// <summary>
        /// 育成完了で外部関数コール（ダイアログ呼び出し元の制御へ移る）
        /// </summary>
        private void CallOnDecide()
        {
            _ignoreUpdateFactorOrderPass = true;//パス期限チェックなし
            _onDecide?.Invoke();
        }
        
        private void SetupFactorOrderButton()
        {
            _factorOrderButton.SetOnClick(OnClickFactorOrder);
            
            // いったんOFF
            _factorOrderButtonJewelRoot.SetActiveWithCheck(false);
            _factorOrderButtonTicketRoot.SetActiveWithCheck(false);
            _factorOrderTicketNoticeText.SetActiveWithCheck(false);
            _factorOrderButtonPassRoot.SetActiveWithCheck(false);
            _factorOrderPassRemainText.SetActiveWithCheck(false);

            // リクエストタイプ別
            switch (_factorOrderInfo.RequestType)
            {
                case SingleModeFactorOrderInfoModel.RequestTypeEnum.Jewel: SetupJewel(); break;
                case SingleModeFactorOrderInfoModel.RequestTypeEnum.Ticket: SetupTicket(); break;
                case SingleModeFactorOrderInfoModel.RequestTypeEnum.Pass: SetupPass(); break;
            }

            // ジュエルやチケットの消費コスト
            int RequestCost()
            {
                return _factorOrderInfo.RequestCost;
            }

            // ジュエル
            void SetupJewel()
            {
                _factorOrderButtonJewelRoot.SetActiveWithCheck(true);
                _factorOrderButtonJewelText.text = RequestCost().ToString();
            }
            
            // チケット
            void SetupTicket()
            {
                _factorOrderButtonTicketRoot.SetActiveWithCheck(true);
                
                var spritePath = AtlasSpritePath.Common.GetItemIconByItemCategory(GameDefine.ItemCategory.FACTOR_ORDER_TICKET);
                _factorOrderButtonTicketIcon.sprite = UIManager.CommonAtlas.GetSprite(spritePath);
                _factorOrderButtonTicketText.text = RequestCost().ToString();
                
                var itemName = TextUtil.GetMasterText(MasterString.Category.MasterItemName, GameDefine.FACTOR_ORDER_TICKET_ID);
                _factorOrderTicketNoticeText.text = TextId.SingleMode539049.Format(itemName);
                _factorOrderTicketNoticeText.SetActiveWithCheck(true);
            }
            
            // パス
            void SetupPass()
            {
                _factorOrderButtonPassRoot.SetActiveWithCheck(true);
                var remainTimeModel = new SingleModeResultFactorOrderPassRemainTimeModel();
                _factorOrderPassRemainText.SetActiveWithCheck(true);
                _factorOrderPassRemainText.text = remainTimeModel.RemainTimeText;
            }
        }
        
        /// <summary>
        /// 指定因子ボタン押下
        /// </summary>
        private void OnClickFactorOrder()
        {
            switch (_factorOrderInfo.RequestType)
            {
                case SingleModeFactorOrderInfoModel.RequestTypeEnum.Jewel:
                    // ジュエルで指定因子確認ダイアログ
                    OpenFactorOrderConfirmForJewel();
                    break;
                case SingleModeFactorOrderInfoModel.RequestTypeEnum.Pass:
                    // パスで指定因子確認ダイアログ
                    _factorOrderConfirmDialog = DialogSingleModeConfirmCompleteFactorOrderPassConfirm.PushDialog(_factorOrderInfo, OnDecideFactorOrder);
                    break;
                case SingleModeFactorOrderInfoModel.RequestTypeEnum.Ticket:
                    // チケットで指定因子確認ダイアログ
                    DialogSingleModeConfirmCompleteFactorOrderTicketConfirm.PushDialog(_factorOrderInfo, OnDecideFactorOrder);
                    break;
            }
        }
        
        /// <summary>
        /// ジュエルによる指定因子確認ダイアログ
        /// </summary>
        private void OpenFactorOrderConfirmForJewel()
        {
            var isShortage = !FactorOrderEnoughUseJewel;

            Action onDecide = null;
            if (isShortage)
            {
                // ジュエル購入
                onDecide = OnDecideOpenBuyJewelDialog;
            }
            else
            {
                // 指定因子実行
                onDecide = OnDecideFactorOrder;
            }
            DialogSingleModeConfirmCompleteFactorOrderConfirm.PushDialog(_factorOrderInfo, isShortage, onDecide, OnPurchasedCompleted);
        }

        /// <summary>
        /// 指定因子確定
        /// </summary>
        private void OnDecideFactorOrder()
        {
            _ignoreUpdateFactorOrderPass = true;

            // ユーザーが指定した内容を一時領域に保持しておく
            var factorOrderRequest = new FactorOrderRequest();
            factorOrderRequest.factor_group_id = _factorOrderInfo.CurrentFactorGroupId; // 指定する因子グループID
            factorOrderRequest.request_type = (int)_factorOrderInfo.RequestType;        // 指定因子実行時の消費コストタイプ 0:無効値 1:ジュエル 2:課金パス 3:補填アイテム
            factorOrderRequest.use_coin = _factorOrderInfo.RequestCoin;                 // 使用ジュエル
            TempData.Instance.SingleModeData.FactorOrderRequest = factorOrderRequest;
            TempData.Instance.SingleModeData.IsDecidedFactorOrder = true;

            CallOnDecide();
        }

        /// <summary>
        /// 指定因子確認ダイアログ上から商品を購入した際の処理
        /// </summary>
        private void OnPurchasedCompleted()
        {
            // 最新のパス情報を取得
            var factorOrderPass = PaymentUtility.Instance.GetFactorOrderPass();
            if (factorOrderPass.IsPurchased)
            {
                // パスに変更
                _factorOrderInfo.ApplyPassPurchased();
                _ignoreUpdateFactorOrderPass = false;
                // UI更新
                SetupFactorOrderButton();
            }
        }
        
        /// <summary>
        /// ジュエル購入画面を開く
        /// </summary>
        private void OnDecideOpenBuyJewelDialog()
        {
            PaymentUtility.Instance.OpenBuyJewelDialog(
                finish:null,
                removeAllOnFinish:false,
                onClose:OnPurchasedCompleted,
                autoScrollIdList:new []{StaticVariableDefine.Payment.FACTOR_ORDER_PASS_ID});
        }
        
        /// <summary>
        /// 更新
        /// </summary>
        public void Update()
        {
            // 指定因子パスの残り時間更新用
            if (_factorOrderPassRemainText.gameObject.activeInHierarchy && 
                _ignoreUpdateFactorOrderPass == false &&
                _factorOrderInfo.RequestType == SingleModeFactorOrderInfoModel.RequestTypeEnum.Pass)
            {
                var dialog = GetDialog() as DialogCommon;
                if (dialog != null && (dialog.IsOpen == false || dialog.IsCloseAnimation))
                {
                    //ダイアログが閉じた状態、閉じてる最中はチェックしない
                    return;
                }
                var remainTimeModel = new SingleModeResultFactorOrderPassRemainTimeModel();

                if (remainTimeModel.IsExpired)
                {
                    // 指定因子パス期限切れダイアログ
                    _ignoreUpdateFactorOrderPass = true;
                    var dialogData = new DialogCommon.Data();
                    dialogData.SetSimpleOneButtonMessage(TextId.SingleMode539038.Text(), TextId.SingleMode194122.Text(), _ => OnExpired());
                    DialogManager.PushDialog(dialogData);
                }
                else
                {
                    _factorOrderPassRemainText.text = remainTimeModel.RemainTimeText;
                }
            }
        }
        
        /// <summary>
        /// 指定因子パス有効期限切れのダイアログを閉じたとき
        /// </summary>
        private void OnExpired()
        {
            // 2階層上の因子変更ダイアログを開いていた場合は閉じる
            var factorChangeDialog = DialogManager.Instance.GetDialogInnerComponent<DialogSingleModeConfirmCompleteFactorOrderChange>();
            if (factorChangeDialog != null)
            {
                var dialog = factorChangeDialog.GetDialog();
                if (dialog != null && dialog.IsOpen)
                {
                    dialog.Close();
                }
            }
            var factorDetailDialog = DialogManager.Instance.GetDialogInnerComponent<DialogSingleModeFactorDetail>();
            if (factorDetailDialog != null)
            {
                var dialog = factorDetailDialog.GetDialog();
                if (dialog != null && dialog.IsOpen)
                {
                    dialog.Close();
                }
            }
            // 確認ダイアログを開いていた場合は閉じる
            if (_factorOrderConfirmDialog != null && _factorOrderConfirmDialog.IsOpen)
            {
                _factorOrderConfirmDialog.Close();
            }

            // パス期限切れでリクエストタイプ変更
            ChangeFactorOrderRequestTypeWhenPassExpired();

            // 指定因子ボタン更新
            SetupFactorOrderButton();
        }

        /// <summary>
        /// パス期限切れでリクエストタイプ変更
        /// </summary>
        private void ChangeFactorOrderRequestTypeWhenPassExpired()
        {
            if (HasFactorOrderTicket())
            {
                // チケットに変更
                _factorOrderInfo.ApplyRequestTicketWhenPassExpired();
            }
            else
            {
                // ジュエルに変更
                _factorOrderInfo.ApplyRequestJewelWhenPassExpired();
            }
        }
        
        /// <summary>
        /// 指定因子をせずに次へ行くか確認ダイアログ
        /// </summary>
        private void PushDialogNoneFactorOrderConfirm()
        {
            var dialogData = new DialogCommon.Data();
            dialogData.SetSimpleTwoButtonMessage(TextId.SingleMode194120.Text(), TextId.SingleMode194121.Text(), _ => CallOnDecide(), TextId.Common0004, TextId.Common0003);
            _factorOrderConfirmDialog = DialogManager.PushDialog(dialogData);
        }
        
        private bool HasFactorOrderTicket()
        {
            return WorkDataManager.Instance.ItemData.GetHaveItemNum(GameDefine.FACTOR_ORDER_TICKET_ID) > 0;
        }
        /// <summary> 指定因子に必要な石が足りているかどうか </summary>
        public bool FactorOrderEnoughUseJewel
            => FactorOrderCarrotJewelCost <= WorkDataManager.Instance.UserData.CarrotStone.TotalCoin;
        private int FactorOrderCarrotJewelCost => _factorOrderInfo.RequestCost;
    }
}