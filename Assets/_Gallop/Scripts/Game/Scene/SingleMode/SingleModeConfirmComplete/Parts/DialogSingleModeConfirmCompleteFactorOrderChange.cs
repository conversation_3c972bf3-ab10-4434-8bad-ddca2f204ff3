using UnityEngine;
using System;
using System.Linq;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 指定因子変更ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeConfirmCompleteFactorOrderChange : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion


        [SerializeField] private ToggleGroupCommon _toggleGroup;
        [SerializeField] private PartsSingleModeConfirmCompleteFactorOrderToggle _blueToggleItem;
        [SerializeField] private PartsSingleModeConfirmCompleteFactorOrderToggle _redToggleItem;
        
        
        private int _selectFactorGroupId;


        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(int factorGroupId, Action<int/*factor_id*/> onSelect)
        {
            const string PATH = ResourcePath.SINGLE_MODE_CONFIRM_COMPLETE_UI_ROOT + "DialogSingleModeConfirmCompleteFactorOrderChange";
            var content = LoadAndInstantiatePrefab<DialogSingleModeConfirmCompleteFactorOrderChange>(PATH);
            var dialogData = content.CreateDialogData();
            dialogData.Title = TextId.SingleMode419115.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0023.Text();
            dialogData.RightButtonCallBack = _ => { onSelect.Invoke(content._selectFactorGroupId);};
            dialogData.FooterText = TextId.SingleMode194116.Text();
            dialogData.IsFooterNotificationText = true;
            content.Setup(factorGroupId);
            DialogManager.PushDialog(dialogData);
        }

        private void Setup(int factorGroupId)
        {
            _selectFactorGroupId = factorGroupId;
            
            var factorList = GetFactorList();
            
            // 選択中因子がリスト内になければリストの最初を選択にする
            if (factorList.Count > 0 &&
                factorList.Exists(x => x.FactorGroupId == _selectFactorGroupId) == false)
            {
                _selectFactorGroupId = factorList[0].FactorGroupId;
            }

            var itemList = new List<PartsSingleModeConfirmCompleteFactorOrderToggle>();
            
            // 青因子トグル生成
            CreateFactorToggle(MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARAM, _blueToggleItem);
            
            // 赤因子トグル生成
            CreateFactorToggle(MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PROPER, _redToggleItem);

            // local func : 因子リスト作成
            void CreateFactorToggle(int factorType, PartsSingleModeConfirmCompleteFactorOrderToggle listItem)
            {
                var targetFactorList = factorList.Where(x => x.FactorType == factorType).ToList();
                UIUtil.CreateScrollItem(listItem, itemList, targetFactorList, (obj, factor) =>
                {
                    obj.Setup(factor, _selectFactorGroupId == factor.FactorGroupId, SingleModeFactorOrderInfoModel.IsEnableFactor(factor));
                });
            }
            
            _toggleGroup.ToggleArray = itemList.Select(x => x.Toggle).ToArray();
            _toggleGroup.SetToggleArrayCallBack();
            _toggleGroup.SetOnSelectCallback(index => _selectFactorGroupId = factorList[index].FactorGroupId);
        }
        
        /// <summary>
        /// 因子リスト
        /// </summary>
        private List<MasterSuccessionFactor.SuccessionFactor> GetFactorList()
        {
            return MasterDataManager.Instance.masterSuccessionFactor.dictionary.Values
                .Where(IsEnableShowOnFactorList)
                .GroupBy(x => x.FactorGroupId).Select(group => group.First())
                .OrderBy(GetFactorSortOrder)
                .ThenBy(x => x.FactorGroupId)
                .ToList();
        }
        
        /// <summary>
        /// 因子リストに表示可能か
        /// </summary>
        private bool IsEnableShowOnFactorList(MasterSuccessionFactor.SuccessionFactor factor)
        {
            return IsMatchFactorType(factor) && factor.IsInTerm();
        }
        
        /// <summary>
        /// 表示可能な因子タイプ
        /// 青と赤
        /// </summary>
        private bool IsMatchFactorType(MasterSuccessionFactor.SuccessionFactor factor)
        {
            switch (factor.FactorType)
            {
                case MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PARAM:
                case MasterSuccessionFactor.SuccessionFactor.FACTOR_TYPE_PROPER:
                    return true;
            }
            return false;
        }
        
        /// <summary>
        /// 因子タイプ別ソート値
        /// 基礎、バ場、距離、脚質の順でUI上に並べたい
        /// </summary>
        private int GetFactorSortOrder(MasterSuccessionFactor.SuccessionFactor factor)
        {
            switch ((MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType)factor.FactorGroupId)
            {
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Speed:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Stamina:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Power:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Guts:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Wiz:
                    return 1;
            
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Turf:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Dirt:
                    return 2;

                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Nige:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Senko:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Sashi:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Oikomi:
                    return 4;
            
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Short:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Mile:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Middle:
                case MasterSuccessionFactorEffect.SuccessionFactorEffect.FactorTargetType.Long:
                    return 3;
            }
            return 0;
        }
    }
}