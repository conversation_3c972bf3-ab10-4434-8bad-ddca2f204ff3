using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成シナリオ固有レースパドック(代表交流戦、アップグレードレースなど）
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeScenarioRacePaddockView : SingleModePaddockView
    {
    }

    public sealed class SingleModeScenarioRacePaddockViewController : SingleModePaddockViewController
    {
        protected override IEnumerator InitializeViewRaceType()
        {
            //通常レースを出走直後にシナリオレースに参加すると同一ターンでのRaceEnd送信判定に引っかかるためここでクリアしておく
            //シナリオレース後に同じターンでレースは発生しないので問題ないはず
            TempData.Instance.SingleModeData.PrevRaceEndTurn = 0;
            
            yield return base.InitializeViewRaceType();
        }

        //継続してPaddockBGMを再生するため何もしない
        //キュー自体は GetDynamicBgmCueInfo で指定しているため中断復帰でも問題ない
        protected override void PlayBGM(){}

        public override AudioManager.CueSheetCueNameInfo GetDynamicBgmCueInfo()
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            return SingleModeUtils.GetPaddockBgmInfoByProgramId(workSingle.RaceStartResultInfoData.StartInfo.program_id);
        }
    }
}