using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Gallop.Tutorial;
using UnityEngine;
using Object = UnityEngine.Object;
using DG.Tweening;
using Gallop.TrainingChallenge;

namespace Gallop
{
    using static StaticVariableDefine.SingleMode.SingleModeStartView;
    using Step = SingleModeStartView.Step;
    
    /// <summary>
    /// シングル開始画面
    /// </summary>
    [AddComponentMenu("")]
    public class SingleModeStartView : ViewBase
    {
        /// <summary>
        /// 開始ステップ
        /// </summary>
        public enum Step
        {
            None = -1,
            RouteSelect,
            CardSelect,
            SuccessionSelect,
            EquipSelect,
            Max,
        }

        #region ステップに対するUIテーブル

        [System.Serializable]
        public class StepUIPair : KeyAndValue<Step, SingleModeStartStepBase>
        {
            public StepUIPair(Step key, SingleModeStartStepBase value) : base(key, value)
            {
            }
            public override KeyAndValue<Step, SingleModeStartStepBase> CreateCopyInstance() { return new StepUIPair(Key, Value); }
        }

        [System.Serializable]
        public class StepUITable : TableBase<Step, SingleModeStartStepBase, StepUIPair>
        {
        }

        #endregion

        [SerializeField]
        private StepUITable _stepUIPrefabTable = null;
        public StepUITable StepUIPrefabTable => _stepUIPrefabTable;

        [SerializeField]
        private RectTransform _stepUIRoot = null;
        public RectTransform StepUIRoot => _stepUIRoot;
        

        [Header("ハードモード")]
        [SerializeField]
        public RectTransform _difficultyTitleRoot = null;

        [Header("サポカランキングボタン")]
        [SerializeField] private SupportCardRankingButtonView _supportCardRankingButton;

        [field: Header("トレーナーガイドラベル")]
        [field: SerializeField]
        /// <summary>　トレーナーガイドラベル </summary>
        public RaceFitAssistanceStatusLabel RaceFitAssistanceStatusLabel { get; private set; }

        public SupportCardRankingButtonView SupportCardRankingButton => _supportCardRankingButton;
    }

    public class SingleModeStartViewModel
    {
        public int GetSingleModeStartUseTp(int scenarioId)
        {
            // 育成TPキャンペーンデータを取得
            var singleModeTpCampaignData = GetSingleModeTpCampaign(scenarioId);
            if (singleModeTpCampaignData != null)
            {
                // キャンペーンの値を適応
                return singleModeTpCampaignData.EffectValue1;
            }

            return ServerDefine.SingleModeStartUseTp;
        }


        private MasterCampaignData.CampaignData GetSingleModeTpCampaign(int scenarioId)
        {
            // キャンペーン情報を取得
            var campaignDataList = MasterDataManager.Instance.masterCampaignData.GetSingleUseTpCampaignData();
            if (campaignDataList == null || !campaignDataList.Any())
            {
                // 有効なキャンペーン情報がないので終了
                return null;
            }

            // 育成TPキャンペーンデータを検索
            // シナリオIDが一致するデータを検索
            var singleModeTpCampaignData = campaignDataList.FirstOrDefault(campaign => campaign.TargetId == scenarioId || campaign.TargetId == MasterCampaignData.SINGLE_MODE_ALL_SCENARIO_TARGET_ID);
            if (singleModeTpCampaignData == null)
            {
                // 有効なキャンペーン情報がないので終了
                return null;
            }

            return singleModeTpCampaignData;
        }

        public bool IsSingleModeTpCampaign(int scenarioId)
        {
            var singleModeTpCampaignData = GetSingleModeTpCampaign(scenarioId);
            return singleModeTpCampaignData != null;
        }


        /// <summary>
        /// レンタル回数の取得
        /// </summary>
        /// <param name="scenarioId"></param>
        /// <returns></returns>
        public int GetMaxRentalCount(int scenarioId)
        {
            var maxRentalCount = GetDefaultMaxRentalCount();

            // 育成レンタル増加キャンペーン情報を取得
            var campaignDataList = MasterDataManager.Instance.masterCampaignData.GetSingleRentalCountCampaignData();
            if (campaignDataList == null || !campaignDataList.Any())
            {
                // 有効なキャンペーン情報がないので終了
                return maxRentalCount;
            }

            // シナリオIDが一致するデータを検索
            var singleRentalCountCampaignData = campaignDataList.FirstOrDefault(campaign => campaign.TargetId == scenarioId || campaign.TargetId == MasterCampaignData.SINGLE_MODE_ALL_SCENARIO_TARGET_ID);
            if (singleRentalCountCampaignData == null)
            {
                // 有効なキャンペーン情報がないので終了
                return maxRentalCount;
            }

            return singleRentalCountCampaignData.EffectValue1;
        }

        /// <summary>
        /// 残りレンタル可能回数の取得
        /// </summary>
        /// <param name="scenarioId"></param>
        /// <returns></returns>
        public int GetRemainRentalCount(int scenarioId)
        {
            var maxRentalCount = GetMaxRentalCount(scenarioId);
            var rentalCount = WorkDataManager.Instance.SingleMode.RentalCount;
            var remainingRentalCount = maxRentalCount - rentalCount;
            if(remainingRentalCount < 0)
            {
                remainingRentalCount = 0;
            }
            return remainingRentalCount;
        }

        /// <summary>
        /// 最大レンタル回数の取得（キャンペーンは含まない）
        /// </summary>
        public int GetDefaultMaxRentalCount()
        {
            return ServerDefine.SingleModeRentalMaxNum;
        }
        
        /// <summary>
        /// 1日1回無料継承レンタルできるか
        /// </summary>
        public bool IsEnableFreeRental()
        {
            return WorkDataManager.Instance.SingleMode.IsEnableFreeRental;
        }
        
        /// <summary>
        /// レンタル可能かどうか
        /// </summary>
        /// <param name="scenarioId"></param>
        /// <param name="successionChara"></param>
        /// <returns></returns>
        public bool CanRentalSuccessionChara(int scenarioId, WorkTrainedCharaData.TrainedCharaData successionChara, bool isEventRental)
        {
            // レンタル回数確認
            // イベントレンタルは通常レンタル回数を見ない
            if (!isEventRental)
            {
                var maxRentalCount = GetMaxRentalCount(scenarioId);
                if (WorkDataManager.Instance.SingleMode.RentalCount >= maxRentalCount)
                {
                    return false;
                }
            }

            // レンタルに必要なコストの所持確認
            var isCircleMember = false;
            if (WorkDataManager.Instance.CircleData.IsJoin())
            {
                var userData = WorkDataManager.Instance.FriendData.GetFollowList().FirstOrDefault(follow => follow.ViewerId == successionChara.ViewerId);
                if (userData != null)
                {
                    isCircleMember = userData.CircleId == WorkDataManager.Instance.CircleData.CircleId;
                }
            }

            // 所持金チェック
            if (SingleModeUtils.GetRentalCost(successionChara.Rank, isCircleMember, isEventRental: isEventRental, out var isDiscountCampaign) > WorkDataManager.Instance.ItemData.GetMoneyNum())
            {
                return false;
            }

            return true;
        }

    }


    /// <summary>
    /// シングル開始画面
    /// </summary>
    public class SingleModeStartViewController : ViewControllerBase<SingleModeStartView>
    {

        public class ResumeInfo
        {
            public SingleModeStartView.Step Step { get; }
            public bool IsFirst { get; } = false;

            //サポカ選択
            public bool ShowSupportCardSelectDialog;
            public int SupportCardIndex;
            // サポートカード編成ダイアログにて遷移前に詳細ダイアログを開いていたサポートカードID
            public int SelectedSupportCardId { get; set; } = 0;
            // サポートカード一括編成ダイアログにて遷移前に開いていた詳細ダイアログがデッキ内のものかどうか
            public bool IsDeckCard { get; set; }
            // サポートカード一括編成の編成中一時データ
            public DialogSupportDeckCardSelectMulti.WorkDataMultiSelect WorkData { get; set; }
            // サポートカード一括編成から編成途中で遷移したかどうか
            public bool IsFromSupportCardEditing { get; set; }

            public void SetupForEquipSelect(bool showSupportCardSelectDialog, int supportCardIndex)
            {
                ShowSupportCardSelectDialog = showSupportCardSelectDialog;
                SupportCardIndex = supportCardIndex;
            }
        }

        public class ViewInfo : IViewInfo
        {
            public SingleModeStartView.Step Step { get; set; }
            public ResumeInfo ResumeInfo { get; } = new ResumeInfo();
            public EntryInfo EntryInfo { get; }
            public bool IsFirst { get; } = false;
            public int SupportCardId { get; }

            /// <summary> 継承の詳細を表示するか（レンタルのおすすめトレーナーを探すから戻ってきた場合） </summary>
            public bool ShowSuccessionDetail { get; } = false;

            public bool OpenRaceFitAssistanceProperAlert { get; set; }

            /// <summary> 遷移時にダイアログを開くためのコールバック </summary>
            public Action<SingleModeStartStepBase> OpenDialogAction { get; set; } = null;

            public ViewInfo(SingleModeStartView.Step step, EntryInfo entryInfo, bool showSuccessionDetail = false, bool isFirst = false, int supportCardId = 0, bool openRaceFitAssistanceProperAlert = false)
            {
                Step = step;
                EntryInfo = entryInfo;
                ShowSuccessionDetail = showSuccessionDetail;
                IsFirst = isFirst;
                SupportCardId = supportCardId;
                OpenRaceFitAssistanceProperAlert = openRaceFitAssistanceProperAlert;
            }
        }

        private SingleModeStartViewModel _singleModeStartModel = null;
        public SingleModeStartViewModel SingleModeStartModel
        {
            get
            {
                if (_singleModeStartModel == null)
                {
                    _singleModeStartModel = new SingleModeStartViewModel();
                }
                return _singleModeStartModel;
            }
        }

#if CYG_DEBUG
public static bool UseDebug { get; set; }
#endif

        //BeginView以降でtrue
        public bool IsBegin { get; private set; }

        public const int SELECT_NONE = 0;
        public const int INVALID_ROUTE_ID = -1;

        private PartsSingleModeDifficultyTitle _singleModeDifficultyTitle = null;
        
        // 技能試験ラベル　X座標
        private const float DIFFICULTY_TITLE_POS_X_DEFAULT = -195;
        // 技能試験ラベル　Y座標
        private const float DIFFICULTY_TITLE_TOP_POS_Y_DEFAULT = -270;
        private const float DIFFICULTY_TITLE_TOP_POS_Y_SUCCESSION_SELECT = -340;
        private const float DIFFICULTY_TITLE_MID_POS_Y_EQUIP_SELECT = 700;
        
        // サポカランキングボタン　X座標
        private const float SUPPORT_CARD_RANKING_BUTTON_POS_X_DEFAULT = 289.5f;
        // サポカランキングボタン　Y座標
        private const float SUPPORT_CARD_RANKING_BUTTON_POS_Y_DEFAULT = 692.5f;
        
        // トレーナーガイド　X座標
        private const float RACE_FIT_ASSISTANCE_LABEL_TOP_POS_X = -85;
        private const float RACE_FIT_ASSISTANCE_LABEL_MID_POS_X = -105;
        
        // トレーナーガイド　Y座標　デフォルト
        private const float RACE_FIT_ASSISTANCE_LABEL_TOP_POS_Y_DEFAULT = -275;
        // トレーナーガイド　Y座標　サポカ編成時
        private const float RACE_FIT_ASSISTANCE_LABEL_TOP_POS_Y_SUCCESSION_SELECT = -340;
        // トレーナーガイド　Y座標　継承選択
        private const float RACE_FIT_ASSISTANCE_LABEL_MID_POS_Y_EQUIP_SELECT = 692.5f;
        
        // トレーナーガイドのラベルによってX座標を調整するためのオフセット値
        private const float DIFFICULTY_TITLE_OFFSET_POS_X_WITH_RACE_FIT_ASSISTANCE_LABEL = -155;
        private const float SUPPORT_CARD_RANKING_BUTTON_OFFSET_POS_X_WITH_RACE_FIT_ASSISTANCE_LABEL = -130;
        
        // Anchors基準位置　設定用
        private readonly Vector2 ANCHORS_VECTOR_CENTER_RIGHT = new Vector2(1.0f, 0.5f);
        private readonly Vector2 ANCHORS_VECTOR_TOP_RIGHT = Vector2.one;

        #region 背景拡大用

        private float _bgMoveTime;
        private float _bgMoveDeltaTime;
        private Rect _initialBgRect;
        private Rect _beginRect;
        private Rect _endRect;

#if UNITY_EDITOR
        //MoveToCameraで注目しているキャラ
        private SingleModeStartResultCharaViewer.ModelIndex _targetModelIndex = SingleModeStartResultCharaViewer.ModelIndex.Center;
#endif

        #endregion

        /// <summary>
        /// 登録情報
        /// </summary>
        public class EntryInfo
        {
            public int CardId { get; set; }

            /// <summary>ユーザーが所持しているサポカの中からデッキに編成されているサポカ一覧（support_card_data.csvのID）</summary>
            public int[] SupportSerialIdArray { get; set; }
            /// <summary>【サポートカードレンタルキャンペーン】で貸し出されているサポカの中からデッキに編成されているサポカの一覧（support_card_data.csvのID）</summary>
            public int[] RentalSupportCardIdArray { get; set; }
            /// <summary>フレンドサポートカード情報</summary>
            public FriendCardInfo FriendSupportCardInfo { get; set; }
            /// <summary>フレンドサポートカードの編成位置</summary>
            public int FriendSupportCardPosition { get; set; }
            /// <summary>選択したレンタルデッキID（レンタルデッキID以外を選択した場合は0）（</summary>
            public int RentalDeckId { get; set; } = 0;

            public WorkTrainedCharaData.TrainedCharaData SuccessionTrainedChara_First;
            public WorkTrainedCharaData.TrainedCharaData SuccessionTrainedChara_Second;
            public int ScenarioId { get; set; } = INVALID_ROUTE_ID;

            /// <summary> イベントレンタルを使用している場合true </summary>
            public (bool first, bool second) UseEventRental { get; set; }

            /// <summary> 片方でもイベントレンタルを使用している場合true </summary>
            public bool HasUseEventRental => UseEventRental.first || UseEventRental.second;

            /// <summary> 消費TP（消費半減キャンペーン適用） </summary>
            public int UseTp { get; set; }
            /// <summary> TPブースト時の消費TP（最終確認時とAPI送信時で値を合せるために保存） </summary>
            public int UseTpWhenBoostMode { get; set; }
            /// <summary> TPブーストするかどうか </summary>
            public bool EnableTpBoost { get; set; }
            public int DifficultyId { get; set; }
            public int Difficulty { get; set; }
            private Dictionary<GameDefine.ParameterType, PartsSingleModeMemberParamaters.ParamSet> _paramDict;
            private CardDressIdSet _cardDressIdSet;

            /// <summary>
            /// 有効な育成イベント
            /// </summary>
            public SingleModeDefine.TrainingEventType EnableEventType { get; set; }

            /// <summary>
            /// フレンドサポートカード関連データ
            /// </summary>
            public class FriendCardInfo
            {
                /// <summary>
                /// ビューワーID
                /// </summary>
                public long ViewerId;

                /// <summary>
                /// ユーザー名
                /// </summary>
                public string UserName;

                /// <summary>
                /// サポートカードID
                /// </summary>
                public int SupportCardId;

                /// <summary>
                /// サポートカードのマスタデータ
                /// </summary>
                public MasterSupportCardData.SupportCardData MasterSupportCardData
                {
                    get
                    {
                        if (_masterSupportCardData == null)
                        {
                            _masterSupportCardData = MasterDataManager.Instance.masterSupportCardData.Get(SupportCardId);
                        }
                        return _masterSupportCardData;
                    }
                }

                private MasterSupportCardData.SupportCardData _masterSupportCardData = null;

                /// <summary>
                /// サポートカードのレアリティ
                /// </summary>
                public int SupportCardRarity
                {
                    get
                    {
                        if (MasterSupportCardData == null)
                            return 0;

                        return MasterSupportCardData.Rarity;
                    }
                }

                /// <summary>
                /// サポートカードのレベル
                /// </summary>
                public int SupportCardLevel;

                /// <summary>
                /// サポートカードの経験値
                /// </summary>
                public int SupportCardExp;

                /// <summary>
                /// 上限突破の回数
                /// </summary>
                public int LimitBreakCount;

                /// <summary>
                /// キャラID
                /// </summary>
                public int CharaId
                {
                    get
                    {
                        if (MasterSupportCardData == null)
                            return 0;

                        return MasterSupportCardData.CharaId;
                    }
                }

                /// <summary>
                /// 得意練習
                /// </summary>
                public MasterSupportCardData.TrainingType BestTraining
                {
                    get
                    {
                        if (MasterSupportCardData == null)
                            return 0;

                        return MasterSupportCardData.GetBestTraining();
                    }
                }

                /// <summary>
                /// スキル発動条件種類
                /// </summary>
                public SkillDefine.SkillTriggerTag[] SkillTriggerTagArray
                {
                    get
                    {
                        if (_skillTriggerTagArray != null)
                            return _skillTriggerTagArray;

                        var allTagList = new List<SkillDefine.SkillTriggerTag>();
                        var hintEventList =
                            MasterDataManager.Instance.masterSingleModeHintGain.GetListWithHintIdOrderByIdAsc(MasterSupportCardData.SkillSetId);
                        if (hintEventList != null)
                            foreach (var hint in hintEventList)
                                if (hint.HintGainType == decimal.Zero && hint.SupportCardId == SupportCardId)
                                {
                                    var tagList = StandaloneSimulator.SkillTriggerCreatorSimulate
                                        .CreateTriggerTagList(hint.HintValue1)
                                        .ToArray();
                                    if (tagList.Length == 0)
                                        continue;

                                    allTagList.AddRange(tagList);
                                }

                        _skillTriggerTagArray = allTagList.ToArray();
                        return _skillTriggerTagArray;
                    }
                }

                private SkillDefine.SkillTriggerTag[] _skillTriggerTagArray = null;

                public List<MasterSupportCardEffectTable.SupportCardEffectTable> MasterSupportCardEffectList =>
                    MasterDataManager.Instance.masterSupportCardEffectTable.GetListWithIdOrderByTypeAsc(MasterSupportCardData.EffectTableId);

                /// <summary>
                /// フレンドかどうか
                /// </summary>
                public WorkFriendData.FriendState FriendState;

                /// <summary>
                /// 最終ログイン時間
                /// </summary>
                public long LastLoginTime;
            }

            //サークルからレンタルできるキャラ一覧
            public UserInfoAtFriend[] RentalUserInfoArray = null;
            public WorkTrainedCharaData.TrainedCharaData[] RentalTrainedCharaArray = null;
            // イベント用レンタル情報
            public UserInfoAtFriend[] EventRentalUserInfoArray;
            public WorkTrainedCharaData.TrainedCharaData[] EventRentalTrainedCharaArray;
            
            /// <summary> 適用したいキャンペーンID一覧 </summary> 
            private readonly HashSet<int> _applyCampaignIdHashSet = new HashSet<int>();
            
            /// <summary>
            /// 適用したいキャンペーンIDのリストに該当のIDが含まれているか確認
            /// </summary>
            /// <param name="campaignId">含まれているかどうか確認したいキャンペーンID</param>
            /// <returns>引数のキャンペーンIDが含まれているときはtrue</returns>
            public bool ContainsApplyCampaignId(int campaignId) => _applyCampaignIdHashSet.Contains(campaignId);
            
            /// <summary>
            /// 適用したいキャンペーンIDのリストに引数のIDを追加、すでに存在する場合は追加しない
            /// </summary>
            /// <param name="campaignId">追加したいキャンペーンID</param>
            /// <returns>引数のキャンペーンIDを追加したときはtrue</returns>
            public bool AddApplyCampaignId(int campaignId) => _applyCampaignIdHashSet.Add(campaignId);
            
            /// <summary>
            /// 適用したいキャンペーンIDのリストから引数のIDを削除、存在しない場合は何もしない
            /// </summary>
            /// <param name="campaignId">削除したいキャンペーンID</param>
            /// <returns>引数のキャンペーンIDを削除したときはtrue</returns>
            public bool RemoveApplyCampaignId(int campaignId) => _applyCampaignIdHashSet.Remove(campaignId);

            /// <summary>
            /// コンストラクタ
            /// </summary>
            public EntryInfo()
            {
                CardId = SELECT_NONE;
                _paramDict = new Dictionary<GameDefine.ParameterType, PartsSingleModeMemberParamaters.ParamSet>();
                var typeArray = StaticVariableDefine.SingleMode.PARAMETER_TYPE_ARRAY;
                for (int i = 0; i < typeArray.Length; i++)
                {
                    _paramDict[typeArray[i]] = new PartsSingleModeMemberParamaters.ParamSet(0, 0);
                }

                SupportSerialIdArray = new int[GameDefine.MAX_SUPPORT_DECK_MEMBER_NUM];
                for (int i = 0; i < SupportSerialIdArray.Length; i++)
                {
                    SupportSerialIdArray[i] = SELECT_NONE;
                }
                RentalSupportCardIdArray = new int[GameDefine.MAX_SUPPORT_DECK_MEMBER_NUM];
                for (int i = 0; i < RentalSupportCardIdArray.Length; i++)
                {
                    RentalSupportCardIdArray[i] = SELECT_NONE;
                }

                _cardDressIdSet = new CardDressIdSet(0, 0);

                FriendSupportCardInfo = null;
                FriendSupportCardPosition = GameDefine.INVALID_FRIEND_SUPPORT_CARD_POSITION;

                //作り直し（初期化されたら）フレンド情報も初期化されるので削除。生成だけして使わないケースでも初期化されてしまうが、それは使い方的に起こり得ないのと、情報を引き継ぐケースなどもあるのでここで初期化。
                TempData.Instance.SupportDeckData.ClearDeckSelectIndex();

                // レンタルデッキ解放前は育成画面遷移時に全デッキのフレンドサポカを外す
                if (!GameDefine.IS_ENABLE_RENTAL_DECK)
                {
                    TempData.Instance.SupportDeckData.RemoveAllDeckFriendSupportCardInfo();
                }
            }

            public WorkTrainedCharaData.TrainedCharaData GetSuccessionTrainedCharaData(bool first)
            {
                return first ? SuccessionTrainedChara_First : SuccessionTrainedChara_Second;
            }

            public WorkTrainedCharaData.TrainedCharaData GetRentalTrainedChara()
            {
                
                var userViewerId = WorkDataManager.Instance.UserData.ViewerId;
                if(SuccessionTrainedChara_First != null && SuccessionTrainedChara_First.ViewerId != userViewerId)
                {
                    return SuccessionTrainedChara_First;
                }

                if(SuccessionTrainedChara_Second != null && SuccessionTrainedChara_Second.ViewerId != userViewerId)
                {
                    return SuccessionTrainedChara_Second;
                }
                return null;
            }


            //指定のキャラはサークルメンバーのものか
            public bool TargetCharaIsCircleMember(WorkTrainedCharaData.TrainedCharaData rentalChara)
            {
                if(!WorkDataManager.Instance.CircleData.IsJoin())
                {
                    return false;
                }
                if(rentalChara != null)
                {
                    var rentalUserInfo = RentalUserInfoArray.FirstOrDefault(info => info.viewer_id == rentalChara.ViewerId);
                    if(rentalUserInfo?.circle_info?.circle_id == WorkDataManager.Instance.CircleData.CircleId)
                    {
                        return true;
                    }
                }
                return false;
            }
            
            /// <summary>
            /// レンタルしたキャラはサークルメンバーのものか
            /// </summary>
            /// <returns></returns>
            public bool RentalCharaIsCircleMember()
            {
                return TargetCharaIsCircleMember(GetRentalTrainedChara());
            }

            public MasterCardData.CardData GetMasterCardData()
            {
                return MasterDataManager.Instance.masterCardData.Get(CardId);
            }

            /// <summary>
            /// カードIdと衣装Idのペアを返す
            /// </summary>
            /// <param name="isApplyDressChange"> 衣装変更機能を適用するかどうか </param>
            /// <returns></returns>
            public CardDressIdSet GetCardDressIdSet(bool isApplyDressChange)
            {
                // 育成開始時ではCardDataの方を参照
                var workCardData = WorkDataManager.Instance.CardData.GetCardData(CardId);
                if (workCardData == null) return _cardDressIdSet;
                _cardDressIdSet._cardId = CardId;
                _cardDressIdSet._dressId = workCardData.GetRaceDressId(isApplyDressChange);

                return _cardDressIdSet;
            }

            public int GetCharaId()
            {
                var master = GetMasterCardData();
                if (master == null)
                {
                    return 0;
                }

                return master.CharaId;
            }

            //開始API送信用のデータ取得
            public SingleModeStartChara GetSingleModeStartChara()
            {
                var selectFriendSupportCardInfo = FriendSupportCardInfo;
                SingleModeFriendSupportCardInfo friendSupportCardInfo = new SingleModeFriendSupportCardInfo()
                {
                    viewer_id = selectFriendSupportCardInfo != null ? selectFriendSupportCardInfo.ViewerId : 0,
                    support_card_id = selectFriendSupportCardInfo != null ? selectFriendSupportCardInfo.SupportCardId : 0,
                    position = selectFriendSupportCardInfo != null ? FriendSupportCardPosition : 0,
                };
                int trainedCharaId1 = 0; //0だとレンタル
                int trainedCharaId2 = 0;
                var userViewerId = WorkDataManager.Instance.UserData.ViewerId;
                var rentalTrainedChara = new SingleModeRentalSuccessionChara();
                if(SuccessionTrainedChara_First.ViewerId != userViewerId) //1がレンタル
                {
                    rentalTrainedChara.trained_chara_id = SuccessionTrainedChara_First.Id;
                    rentalTrainedChara.viewer_id = SuccessionTrainedChara_First.ViewerId;
                    rentalTrainedChara.is_circle_member = TargetCharaIsCircleMember(SuccessionTrainedChara_First);
                    rentalTrainedChara.is_event_rental = UseEventRental.first;
                    trainedCharaId2 = SuccessionTrainedChara_Second.Id;
                }
                else if(SuccessionTrainedChara_Second.ViewerId != userViewerId) //2がレンタル
                {
                    rentalTrainedChara.trained_chara_id = SuccessionTrainedChara_Second.Id;
                    rentalTrainedChara.viewer_id = SuccessionTrainedChara_Second.ViewerId;
                    rentalTrainedChara.is_circle_member = TargetCharaIsCircleMember(SuccessionTrainedChara_Second);
                    rentalTrainedChara.is_event_rental = UseEventRental.second;
                    trainedCharaId1 = SuccessionTrainedChara_First.Id;
                }
                else
                {
                    trainedCharaId1 = SuccessionTrainedChara_First.Id;
                    trainedCharaId2 = SuccessionTrainedChara_Second.Id;
                }

                // TPブーストするときストーリーイベントIDを入れる
                var workStoryEvent = WorkDataManager.Instance.StoryEventData;
                var boostStoryEventId = 0;

                // 開催期間(準備期間、報酬受け取り期間を除く)の時のみブーストフラグを立てる
                if (EnableTpBoost && workStoryEvent.GetEventPeriodState() == WorkStoryEventData.PeriodState.InSession)
                {
                    boostStoryEventId = workStoryEvent.StoryEventId;
                }

                // 高難易度モード
                var isDifficultyBoost = 0;
                var difficultyMaster = DifficultyId != 0 ? MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(DifficultyId) : null;
                if (difficultyMaster?.EnableTpBoost ?? false)
                {
                    isDifficultyBoost = EnableTpBoost ? 1 : 0;
                }

                var difficultyInfo = new SingleModeSelectedDifficultyInfo
                {
                    difficulty_id = DifficultyId,
                    difficulty = Difficulty,
                    is_boost = isDifficultyBoost,
                };
                
                // 技能試験の挑戦情報
                var trainingChallengeMode = TrainingChallengeDefine.EventConditionOnSingleMode.None;

                if (EnableEventType == SingleModeDefine.TrainingEventType.TrainingChallenge)
                {
                    if (EnableTpBoost)
                    {
                        trainingChallengeMode = TrainingChallengeDefine.EventConditionOnSingleMode.ExamWithBoost;
                    }
                    else
                    {
                        trainingChallengeMode = TrainingChallengeDefine.EventConditionOnSingleMode.ExamWithoutBoost;
                    }
                }
                
                // 因子研究
                var workFactorResearch = WorkDataManager.Instance.FactorResearchData;
                var boostFactorResearchEventId = 0;
                if (EnableTpBoost && workFactorResearch.EnableTpBoost)
                {
                    boostFactorResearchEventId = workFactorResearch.FactorResearchEventId;
                }

                return new SingleModeStartChara
                {
                    card_id = CardId,
                    succession_trained_chara_id_1 = trainedCharaId1,
                    succession_trained_chara_id_2 = trainedCharaId2,
                    rental_succession_trained_chara = rentalTrainedChara,
                    scenario_id = ScenarioId,
                    support_card_ids = GetMergedSupportCardIdArray(),
                    friend_support_card_info = friendSupportCardInfo,
                    selected_difficulty_info = difficultyInfo,
                    select_deck_id = TempData.Instance.SupportDeckData.SelectedDeckId,
                    boost_story_event_id = boostStoryEventId,
                    boost_factor_research_event_id = boostFactorResearchEventId,
                    training_challenge_mode = (int)trainingChallengeMode,
                    campaign_rental_support_card_id_array = GetCampaignRentalSupportCardIdArray(),
                    request_campaign_id_array = _applyCampaignIdHashSet.ToArray(),
                    rental_deck_id = RentalDeckId,
                };
            }
            
            /// <summary>
            /// TPブースト機能を適用した場合のTP消費量
            /// </summary>
            public int GetUseTpWhenBoostMode()
            {
                var masterManager = MasterDataManager.Instance;
                var boostTp = 0;

                // 高難易度モード
                if (TryGetUseTpBoost(masterManager.masterSingleModeDifficultyMode.Get(DifficultyId), out boostTp)) return boostTp;

                // ストーリーイベントTPブースト
                if (TryGetUseTpBoost(WorkDataManager.Instance.StoryEventData, out boostTp)) return boostTp;
                
                // トレーナー技能試験TPブースト
                if (TryGetUseTpBoost(WorkDataManager.Instance.TrainingChallengeData, out boostTp)) return boostTp;
                
                // 因子研究TPブースト
                if (TryGetUseTpBoost(WorkDataManager.Instance.FactorResearchData, out boostTp)) return boostTp;

                return UseTp;


                bool TryGetUseTpBoost(ISingleModeTpBoostProvider provider, out int boostTp)
                {
                    boostTp = 0;
                    if (provider?.TpBoostData == null) return false;
                    if (!provider.EnableTpBoost) return false;

                    boostTp = (int)System.Math.Ceiling((UseTp * provider.TpBoostData.ConsumeTpRatioFloat));
                    return true;
                }
            }

            /// <summary>
            /// フォロワーが変更されレンタルしたウマ娘が使えなくなったら削除
            /// </summary>
            public void DeleteRentalCharaIfNeed()
            {
                var rental = GetRentalTrainedChara();
                if (rental == null || RentalUserInfoArray == null)
                {
                    return;
                }

                // 存在チェック
                if (RentalUserInfoArray.Any(user => user.viewer_id == rental.ViewerId))
                {
                    return;
                }

                // レンタルしたウマ娘がいないので削除
                if (SuccessionTrainedChara_First != null && SuccessionTrainedChara_First.IsOthers)
                {
                    SuccessionTrainedChara_First = null;
                }
                else if (SuccessionTrainedChara_Second != null && SuccessionTrainedChara_Second.IsOthers)
                {
                    SuccessionTrainedChara_Second = null;
                }
            }

            /// <summary>
            /// イベントレンタルが使用できないときに削除する
            /// </summary>
            public void DeleteEventRentalCharaIfNeeded()
            {
                if (!HasUseEventRental) return; // イベントを手持ちに持ってなければ何もしない
                if (EnableEventType != SingleModeDefine.TrainingEventType.None) return; // イベントシナリオであれば良い

                if (UseEventRental.first)
                {
                    SuccessionTrainedChara_First = null;
                }

                if (UseEventRental.second)
                {
                    SuccessionTrainedChara_Second = null;
                }

                UseEventRental = (false, false);
            }

            /// <summary>
            /// 1～6スロットの「自分のサポカ」と「【サポートカードレンタルキャンペーン】のサポカ」を合わせた配列を返す。
            /// （要素数は必ず5個。値はsupport_card_data.csvのIDで、セットされていない場合は0。）
            /// </summary>
            private int[] GetMergedSupportCardIdArray()
            {
                return WorkSupportDeckData.SupportDeckData.GetMergedSupportCardIdArrayImpl(
                    SupportSerialIdArray, RentalSupportCardIdArray);
            }

            /// <summary>
            /// 1～6スロットの「【サポートカードレンタルキャンペーン】のサポカ」の配列を返す。
            /// （要素数は必ず6個。値はcampaign_rental_support_card.csvのIDで、セットされていない場合は0。）
            /// </summary>
            public int[] GetCampaignRentalSupportCardIdArray()
            {
                var campaignRentalSupportCardIdArray = new int[GameDefine.MAX_SUPPORT_DECK_MEMBER_NUM];

                var masterCampaignRentalSupportCardList = WorkDataManager.Instance.CampaignRentalSupportCardData.CurrentMasterCampaignRentalSupportCardList;
                if (!masterCampaignRentalSupportCardList.IsNullOrEmpty())
                {
                    for (int i = 0; i < GameDefine.MAX_SUPPORT_DECK_MEMBER_NUM; ++i)
                    {
                        int supportCardId = RentalSupportCardIdArray[i];
                        if (supportCardId == 0)
                            continue;
                        var masterCampaignRentalSupportCard = masterCampaignRentalSupportCardList.FirstOrDefault(m => m.SupportCardId == supportCardId);
                        if (masterCampaignRentalSupportCard == null)
                            continue;

                        campaignRentalSupportCardIdArray[i] = masterCampaignRentalSupportCard.Id;
                    }
                }

                return campaignRentalSupportCardIdArray;
            }

            /// <summary>
            /// レンタルサポカをデッキにセットしているか
            /// </summary>
            public bool IsUseRentalSupportCard() => (RentalSupportCardIdArray.Count(id => id != 0) >= 1);

        }//end public class EntryInfo

        /// <summary>
        /// シングル開始キャラデータ
        /// </summary>
        public EntryInfo Entry { get; private set; }

        private Step _currentStep = Step.RouteSelect;
        public Step CurrentStep => _currentStep;
        public Step PrevStep { get; private set; } = Step.RouteSelect;
        private SingleModeStartStepBase _currentStepUI;
        private Dictionary<Step, SingleModeStartStepBase> _stepUIDic;
        private Dictionary<Step, SingleModeStartStepBase> StepUIDic
        {
            get
            {
                if(_stepUIDic == null)
                {
                    _stepUIDic = new Dictionary<Step, SingleModeStartStepBase>();
                }
                return _stepUIDic;
            }
        }
        public SingleModeStartResultCharaViewer CharaViewer { get; private set; }

        private Cute.Cri.AudioPlayback _lastPlayAudio;
        private bool _isTapWait;

        /// <summary> ノートから戻ったときにダイアログを再表示するAction </summary>
        public Action OpenDialogAction { get; set; }

        /// <summary> SingleModeをやめてSingleMode以外のStoryViewに移るときはこのフラグを入れる </summary>
        public bool ExitSingleMode { get; set; } = false;


        private List<ButtonCommon> _campaignButtonList = null;
        private List<ButtonCommon> CampaignButtonList
        {
            get
            {
                if(_campaignButtonList == null)
                {
                    _campaignButtonList = new List<ButtonCommon>();
                }
                return _campaignButtonList;
            }
        }


        #region ViewControllerBase

        public override void RegisterDownload(DownloadPathRegister register)
        {
            base.RegisterDownload(register);

            var scenarioIdList = WorkDataManager.Instance.SingleMode.ScenarioIdList;
            foreach (var scenarioId in scenarioIdList)
            {
                var masterScenario = MasterDataManager.Instance.masterSingleModeScenario.Get(scenarioId);
                if (masterScenario == null) continue;
                PartsSingleModeRouteImage.RegisterPath(register, masterScenario);
            }

            SingleModeStartStepRouteSelect.RegisterPath(register);
            SingleModeStartStepSuccessionSelect.RegisterPath(register);
            DialogSingleModeRaceConfirm.RegisterDownload(register);
            SingleModeStartResultCharaViewer.RegisterPath(register);
            DialogSingleModeStartScenarioDetail.RegisterDownload(register);
            DialogSupportDeckCardSelect.RegisterDownload(register);
            DialogSupportDeckCardSelectMulti.RegisterDownload(register);
            DialogRaceFitAssistanceFriendSelect.RegisterDownload(register);

            // Flash
            register.RegisterPathWithoutInfo(ResourcePath.HEADER_TITLE_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.CHARA_RIBBON_FLASH_PATH);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            _bgMoveTime = 0.0f;
            _bgMoveDeltaTime = 0.0f;

            var viewInfo = GetViewInfo() as SingleModeStartViewController.ViewInfo;
            if(viewInfo != null)
            {
                Entry = viewInfo.EntryInfo;
                _currentStep = viewInfo.Step;
                OpenDialogAction = null;
                if (viewInfo.OpenDialogAction != null)
                {
                    OpenDialogAction = () => viewInfo.OpenDialogAction.Invoke(_currentStepUI);
                }

                //キャラビュワー作成
                CreateCharaViewer();
                
                CharaViewer.CreateModel(Entry.GetCardDressIdSet(true));
                var isComplete = false;
                RequestIndex(() => isComplete = true);
                yield return new WaitUntil(() => isComplete);
            }
            else
            {
                //メンバー情報初期化
                Entry = new EntryInfo();
            }
            
            // トレーナーガイドの有効ラベルの初期化処理
            _view.RaceFitAssistanceStatusLabel.Initialize(false);

            yield return base.InitializeView();
        }

        public override IEnumerator InitializeEachPlayIn()
        {
            //キャラビュワー作成
            CreateCharaViewer();

            // 各ステップのUIを作成
            var currentTimeStamp = TimeUtil.GetServerTimeStamp();
            for (int i = 0; i < (int)Step.Max; i++)
            {
                var eStep = (Step)i;
                CreateStepUI(eStep);

                var stepUI = StepUIDic[eStep];
                stepUI.InitializeEachPlayIn(currentTimeStamp);
            }

            yield return base.InitializeEachPlayIn();
        }

        private void CreateCharaViewer()
        {
            if (CharaViewer == null)
            {
                var charaViewerObject = ResourceManager.LoadOnView<GameObject>(ResourcePath.SingleModeStartResultCharaViewerPath);
                var charaViewerInstance = GameObject.Instantiate(charaViewerObject);
                CharaViewer = charaViewerInstance.GetComponent<SingleModeStartResultCharaViewer>(); //シーン直下に生成する
                CharaViewer.InitializeViewer();
                CharaViewer.transform.position = new Vector3(0, 100, 0);//ホームと共有空間になったのでホームに影響しない位置へ移動する
                CharaViewer.gameObject.SetActive(false);
            }
            else
            {
                var viewInfo = GetViewInfo() as SingleModeStartViewController.ViewInfo;
                //フッターから戻られた場合直前の状態が残っているので、先頭から始める場合にはリセットする
                if (viewInfo != null && viewInfo.Step == Step.RouteSelect)
                {
                    MoveToCamera(SingleModeStartResultCharaViewer.ModelIndex.Center, 0f);
                }
            }
        }

        public override IEnumerator PlayInView()
        {
            //背景ブラーを消す
            var homeHubView = SceneManager.Instance.GetCurrentViewController() as HomeHubViewController;
            if(homeHubView != null)
            {
                homeHubView.HideBlurFade();
            }

            SetBg();
            var viewInfo = GetViewInfo() as SingleModeStartViewController.ViewInfo;
            var initStep = _currentStep;
            if (viewInfo != null)
            {
                Entry = viewInfo.EntryInfo;
                initStep = viewInfo.Step;
            }

            CharaViewer.LowResolutionCamera.Camera.enabled = true;
            CharaViewer.gameObject.SetActive(true);
            //初期背景サイズを取得する
            GetMainBgRect(ref _initialBgRect);

            // シナリオレコードが存在しない = 起動直後の遷移時など のみ事前に通信する
            var isComplete = true;
            if(!WorkDataManager.Instance.ScenarioRecordData.HasMaxRecordData)
            {
                isComplete = false;
                RequestIndex(() => isComplete = true);
            }
            yield return new WaitUntil(() => isComplete);

            SetStep(initStep);
            if (initStep == Step.RouteSelect)
            {
                // #64392 Tweenアニメの速度調整（負荷が高い場合に使用）
                // ホーム⇒シナリオ選択　の場合に発生する症状で、育成キャラ選択⇒シナリオ選択の場合には発生しない、そのためシナリオ選択のShow()でなくここで処理
                UIManager.Instance.StartCoroutine(CoroutineDoTweenTimeScale());
            }

            // フッターの表示設定
            UIManager.Instance.OpenFooterBackButton(0);

            // BGMの切り替えまでCanvasをロックする
            UIManager.Instance.LockGameCanvas();

            isComplete = true;
            if( Entry.RentalTrainedCharaArray == null )
            {
                //既に通信済みの場合は通信しない(フレンドフォローから戻ってきた時、初回遷移時)
                isComplete = false;
                RequestIndex(() => isComplete = true);
            }

            yield return new WaitUntil(() => isComplete);

            // 完了後にアンロック
            UIManager.Instance.UnlockGameCanvas();
        }
        
        /// <summary>
        /// #64392
        /// 画面の切り替え負荷でアニメーションの1フレーム目が飛んでしまう症状がある
        /// アニメーションをスローに1フレームだけして、負荷で飛ばないようにする
        /// 見せたい絵は各パーツがフェードインしてくるところ(timeScale=0だと完全に透明なのである程度透過した状態を見せたいいい感じの値0.2にした)
        /// </summary>
        private IEnumerator CoroutineDoTweenTimeScale()
        {
            DOTween.timeScale = 0.2f;
            yield return null;
            DOTween.timeScale = 1;
        }


        public override void BeginView()
        {
            IsBegin = true;
            _currentStepUI.BeginView();
            base.BeginView();
        }

        public override void UpdateView()
        {
            UpdateBg();
            if (CharaViewer != null)
            {
                CharaViewer.UpdateViewer();
            }
#if UNITY_EDITOR
            //設定データの影響を常に受けるようにする
            ApplyCharaViewSettingData();
#endif
            base.UpdateView();
        }

        /// <summary>
        /// 画面Out時
        /// </summary>
        public override IEnumerator PlayOutView()
        {
            // 「戻る」ボタンのハケアニメを開始
            if (UIManager.Instance.IsVisibleBackButton())
            {
                const float DELAY = 0.033f;
                var seq = DOTween.Sequence();
                seq.AppendInterval(DELAY);
                seq.AppendCallback(() =>
                {
                    UIManager.Instance.CloseFooterBackButton();
                });
            }

            // サポカランキングボタン
            _view.SupportCardRankingButton.Hide();

            // 現在のステップの画面のハケアニメを開始
            if (_stepUIDic != null)
            {
                var currentStepUI = GetStepUI(_currentStep);
                yield return currentStepUI.PlayOutView();
            }

            //難易度タイトルがあればハケアニメーション
            if(_singleModeDifficultyTitle != null)
            {
                _singleModeDifficultyTitle.PlayOut();
            }

            yield return base.PlayOutView();
        }

        public override IEnumerator EndView()
        {
            if (_stepUIDic != null)
            {
                var currentStepUI = GetStepUI(_currentStep);
                currentStepUI.Hide(); // ハケアニメが終わったら現在のステップの画面を消す
            }

            if (WorkDataManager.Instance.SingleMode.IsPlaying)
            {
                var nextScene = SceneManager.Instance.GetNextSceneId();
                if ((nextScene != SceneDefine.SceneId.SingleMode && nextScene != SceneDefine.SceneId.Story)
                     || ExitSingleMode)
                {
                    ExitSingleMode = false;
                    // シングルモードを抜けるときシングルモードフラグクリア　※シングルモードの会話から始まるケースがあるのでStoryも除外する
                    WorkDataManager.Instance.SingleMode.IsPlaying = false;
                    if (SingleModeChangeViewManager.HasInstance())
                    {
                        SingleModeChangeViewManager.Instance.ClearData();
                    }
                }
                else
                {
                    // 次に進む時にスタックはクリアする
                    SceneManager.Instance.ClearBackStack();
                }
            }

            //HubView以外での遷移の場合はキャプチャー破棄のタイミングを遅らせる。このタイミングでテクスチャを破棄すると画面遷移時のキャプチャーが正しく行えない
            if (!SceneManager.Instance.IsRunChangeInHubView)
            {
                CharaViewer.LowResolutionCamera.IsOnDisableReleaseTexture = false;
            }

            // #136344 : 継承キャラ選択時の背景ブラーの値が残り続けるため、遷移時にリセットをかける
            CharaViewer.ResetBlurStrength();

            CharaViewer.gameObject.SetActive(false);

            //初期化
            if (_stepUIDic != null)
            {
                foreach (var stepUI in _stepUIDic.Values)
                {
                    stepUI.EndView();
                }
            }
            
            // トレーナーガイドの有効ラベルはViewを出た時点で無効化する
            _view.RaceFitAssistanceStatusLabel.Setup(false);
            
            WorkDataManager.Instance.CardData.ClearCardHaveViewBackStack(); // 強化アイテム入手場所から来た場合
            
            yield break;
        }

        public override IEnumerator FinalizeView()
        {
            //FinalizeViewerで破棄されるように促す
            if (CharaViewer != null)
            {
                CharaViewer.LowResolutionCamera.IsOnDisableReleaseTexture = true;
                CharaViewer.FinalizeViewer();
                Object.Destroy(CharaViewer.gameObject);
            }

            //各UIの強制退出処理
            if (_stepUIDic != null)
            {
                foreach (var step in _stepUIDic)
                {
                    step.Value.Hide(true);
                }
            }
            

            yield return base.FinalizeView();
        }

        #endregion ViewControllerBase

        #region 背景
        
        /// <summary>
        /// 背景設定
        /// </summary>
        public void SetBg()
        {
            BGManager.Instance.SetMainBg(GetCurrentScenarioBgPath());
            BGManager.Instance.SetMainBgLocalPos(GetCurrentScenarioBgOffset());
            GetMainBgRect(ref _initialBgRect); // BGの位置変更があった場合はRect取得しなおす
        }

        /// <summary>
        /// ホームからシナリオ選択に遷移する時のシナリオに合わせた背景座標オフセット
        /// </summary>
        public static Vector3 GetCurrentScenarioBgOffsetFromHome()
        {
            return GetScenarioBgOffset(GetCurrentScenarioIdFromHome());
        }
        
        /// <summary>
        /// 現在選択中のシナリオに合わせた背景座標オフセット(ホームからの遷移ブラーにも使用する）
        /// </summary>
        public static Vector3 GetCurrentScenarioBgOffset()
        {
            return GetScenarioBgOffset(GetCurrentScenarioId());
        }
        private static Vector3 GetScenarioBgOffset(int scenarioId)
        {
            var masterScenario = MasterDataManager.Instance.masterSingleModeScenario.Get(scenarioId);
            return masterScenario != null ? new Vector3(masterScenario.BgOffsetX, 0, 0) : BG_POS;
        }

        /// <summary>
        /// 現在選択中のシナリオに合わせた背景(ホームからの遷移ブラーにも使用する）
        /// </summary>
        public static string GetCurrentScenarioBgPath()
        {
            return GetScenarioBgPath(GetCurrentScenarioId());
        }
        
        /// <summary>
        /// ホームからシナリオ選択に遷移する時のシナリオに合わせた背景
        /// </summary>
        public static string GetScenarioBgPathFromHome()
        {
            return GetScenarioBgPath(GetCurrentScenarioIdFromHome());
        }
        
        public static string GetScenarioBgPath(int scenarioId)
        {
            var masterScenario = MasterDataManager.Instance.masterSingleModeScenario.Get(scenarioId);
            if (masterScenario == null)
            {
                return SceneDefine.GetBgData(SceneDefine.BgId.SingleModeSeasonSpring).TexturePath;
            }
            return ResourcePath.GetBackgroundPath(masterScenario.BgId, masterScenario.BgSubId);
        }
        
        /// <summary>
        /// 現在選択中のシナリオに合わせた背景ブラー素材パス(ホームからの遷移ブラーに使用する）
        /// </summary>
        public static string GetCurrentScenarioBgFadePath()
        {
            return ResourcePath.GetSingleModeScenarioBgFadePath(GetCurrentScenarioId());
        }
        /// <summary>
        /// ホームからシナリオ選択に遷移する時のシナリオに合わせた背景ブラー素材パス
        /// </summary>
        public static string GetScenarioBgFadePathFromHome()
        {
            return ResourcePath.GetSingleModeScenarioBgFadePath(GetCurrentScenarioIdFromHome());
        }
        
        /// <summary>
        /// 現在選択中のシナリオID(ホームからの遷移ブラーにも使用する）
        /// </summary>
        public static int GetCurrentScenarioId()
        {
            var scenarioIdList = WorkDataManager.Instance.SingleMode.ScenarioIdList;
            var lastScenarioId = SaveDataManager.Instance.SaveLoader.LastSingleModeScenarioId;
            var lastIndex = scenarioIdList.FindIndex(id => id == lastScenarioId);
            if (lastIndex < 0) lastIndex = 0;　// 未選択や選択シナリオがなくなったときは0番目
            return scenarioIdList[lastIndex];
        }

        /// <summary>
        /// ホームから遷移してきた時に選択されているシナリオID
        /// </summary>
        private static int GetCurrentScenarioIdFromHome()
        {
            if (IsJustAfterTutorial())
            {
                // 今後のシナリオの追加状況に関わらず、必ず「新設！URAファイナルズ」が選択されます
                return (int)SingleModeDefine.ScenarioId.URA;
            }
            return GetCurrentScenarioId();
        }

        /// <summary>
        /// チュートリアル直後か。チュートリアル以外で一度も育成完了してない＝歴代評価０件
        /// </summary>
        public static bool IsJustAfterTutorial()
        {
            var directoryCnt = WorkDataManager.Instance.DirectoryData.GetCardDataListCount();
            return directoryCnt == 0;
        }

        #endregion
        
#if UNITY_EDITOR
        private void ApplyCharaViewSettingData()
        {
            if (!IsBegin)
                return;

            //移動中は反映させない
            if (_bgMoveTime > 0.0f)
                return;

            //左右キャラの高さ設定を反映させる
            CharaViewer.ApplyHeightScalePositionOffsetY();
            //背景スケールとオフセットを反映する
            Rect rect = _initialBgRect;
            var settingData = CharaViewer.SettingData;
            switch (_targetModelIndex)
            {
                case SingleModeStartResultCharaViewer.ModelIndex.Left:
                    rect.x += settingData.LeftBgOffset.x;
                    rect.y += settingData.LeftBgOffset.y;
                    rect.width *= settingData.LeftBgSizeScale;
                    rect.height *= settingData.LeftBgSizeScale;
                    break;
                case SingleModeStartResultCharaViewer.ModelIndex.Right:
                    rect.x += settingData.RightBgOffset.x;
                    rect.y += settingData.RightBgOffset.y;
                    rect.width *= settingData.RightBgSizeScale;
                    rect.height *= settingData.RightBgSizeScale;
                    break;
            }
            SetMainBgRect(ref rect);
        }
#endif

        /// <summary>
        /// 背景更新(位置移動など)
        /// </summary>
        private void UpdateBg()
        {
            if (_bgMoveTime <= 0.0f)
                return;

            _bgMoveDeltaTime += Time.deltaTime;
            Rect rect = _endRect;
            if (_bgMoveDeltaTime >= _bgMoveTime)
            {
                _bgMoveDeltaTime = 0.0f;
                _bgMoveTime = 0.0f;
            }
            else
            {
                float normalTime = _bgMoveDeltaTime / _bgMoveTime;

                var value = CharaViewer.SettingData.EvaluateCameraMove(normalTime);
                rect.x = Mathf.Lerp(_beginRect.x, _endRect.x, value);
                rect.y = Mathf.Lerp(_beginRect.y, _endRect.y, value);
                rect.width = Mathf.Lerp(_beginRect.width, _endRect.width, value);
                rect.height = Mathf.Lerp(_beginRect.height, _endRect.height, value);
            }
            SetMainBgRect(ref rect);
        }

        /// <summary>
        /// UIの生成
        /// </summary>
        private void CreateStepUI(Step step)
        {
            if(StepUIDic.ContainsKey(step))
            {
                return;
            }

            SingleModeStartStepBase prefab = null;
            var stepPrefabDic = _view.StepUIPrefabTable.DataDic;
            if (!stepPrefabDic.TryGetValue(step, out prefab))
            {
                return;
            }

            var uiInstance = Object.Instantiate(prefab.gameObject, _view.StepUIRoot).GetComponent<SingleModeStartStepBase>();
            uiInstance.Initialize(this, step);
            uiInstance.Hide(true);
            StepUIDic.Add(step, uiInstance);
        }

        /// <summary>
        /// ステップ設定
        /// </summary>
        /// <param name="step"></param>
        public void SetStep(Step step)
        {
            SetupRaceFitAssistanceLabel(step);
            SetupDifficultyCampaign(step);
            SetupSupportCardRankingButton(step);

            //ステップごとのUI表示切替とステップの更新(Show前にやらないとNextButtonの判定がずれる)
            var prevUI = GetStepUI(_currentStep);
            _currentStepUI = GetStepUI(step);
            PrevStep = _currentStep;
            _currentStep = step;

            var vi = GetViewInfo() as SingleModeStartViewController.ViewInfo;
            if(vi != null)
            {
                vi.Step = step;
            }

            prevUI.Hide();

            UIManager.Instance.SetHeaderTitleText(_currentStepUI.HeaderTextId, _currentStepUI.GuideId);
            UIManager.Instance.PlayHeaderTitleInAnim(true);

            _currentStepUI.Show();
        }

        public SingleModeStartStepBase GetStepUI(Step step)
        {
            SingleModeStartStepBase ui = null;
            if (_stepUIDic.TryGetValue(step, out ui))
            {
                return ui;
            }

            return null;
        }

        /// <summary>
        /// 難易度表示設定
        /// </summary>
        private void SetupDifficultyCampaign(Step step)
        {
            // サポート選択画面以外では非表示にする
            // Note: issue-87241 今後非表示ではなく位置調整による変更が入るかもしれない為、画面の比較処理条件の変更だけにする
            if (step != Step.EquipSelect)
            {
                _singleModeDifficultyTitle?.SetActiveWithCheck(false);
                return;
            }

            // 選択中のハードモードマスタを取得
            var difficultyId = Entry.DifficultyId;
            var difficulty = Entry.Difficulty;
            var masterDifficultyData = MasterDataManager.Instance.masterSingleModeDifficultyData.Get(difficultyId, difficulty);
            if (masterDifficultyData == null)
            {
                // マスタが取れなければ表示しない
                _singleModeDifficultyTitle?.SetActiveWithCheck(false);
                return;
            }

            var resourceHash = (ResourceManager.ResourceHash)ResourceManager.GetViewLoadHash(SceneManager.Instance.GetCurrentViewId());
            var mode = MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(difficultyId);
            var showInfoButton = false;
            if(mode != null && mode.IsContinueBonusRewardType) //ゴルシモード2022ではモード詳細を表示する
            {
                showInfoButton = true;
            }
            // ハードモードタイトル表示を生成
            if (_singleModeDifficultyTitle == null)
            {
                _singleModeDifficultyTitle = PartsSingleModeDifficultyTitle.Create(_view._difficultyTitleRoot, masterDifficultyData, resourceHash, showInfoButton);
            }
            else
            {
                //生成済みで別モードになっている場合があるので更新
                _singleModeDifficultyTitle.Setup(masterDifficultyData, resourceHash, showInfoButton);
            }
            _singleModeDifficultyTitle?.SetActiveWithCheck(true);

            // 表示位置調整
            var xPos = DIFFICULTY_TITLE_POS_X_DEFAULT;
            if (_view.RaceFitAssistanceStatusLabel.IsActive())
            {
                xPos += DIFFICULTY_TITLE_OFFSET_POS_X_WITH_RACE_FIT_ASSISTANCE_LABEL;
            }
            switch (step)
            {
                case Step.SuccessionSelect:
                    // 右上基準
                    _view._difficultyTitleRoot.anchorMax = ANCHORS_VECTOR_TOP_RIGHT;
                    _view._difficultyTitleRoot.anchorMin = ANCHORS_VECTOR_TOP_RIGHT;
                    _view._difficultyTitleRoot.anchoredPosition = new Vector2(xPos, DIFFICULTY_TITLE_TOP_POS_Y_SUCCESSION_SELECT);
                    break;
                case Step.CardSelect:
                    // 右上基準
                    _view._difficultyTitleRoot.anchorMax = ANCHORS_VECTOR_TOP_RIGHT;
                    _view._difficultyTitleRoot.anchorMin = ANCHORS_VECTOR_TOP_RIGHT;
                    _view._difficultyTitleRoot.anchoredPosition = new Vector2(xPos, DIFFICULTY_TITLE_TOP_POS_Y_DEFAULT);
                    break;
                case Step.EquipSelect:
                    // 中央右基準
                    _view._difficultyTitleRoot.anchorMax = ANCHORS_VECTOR_CENTER_RIGHT;
                    _view._difficultyTitleRoot.anchorMin = ANCHORS_VECTOR_CENTER_RIGHT;
                    _view._difficultyTitleRoot.anchoredPosition = new Vector2(xPos, DIFFICULTY_TITLE_MID_POS_Y_EQUIP_SELECT);
                    break;
            }
        }

        /// <summary>
        /// サポカランキングボタンの表示/非表示
        /// </summary>
        private void SetupSupportCardRankingButton(Step step)
        {
            if (step == Step.EquipSelect)
            {
                _view.SupportCardRankingButton.SetupAtSupportCardDeck(Entry?.ScenarioId ?? 0);
                _view.SupportCardRankingButton.OnClick = () => 
                {
                    var viewInfo = new SingleModeStartViewController.ViewInfo(SingleModeStartView.Step.EquipSelect, Entry);
                    var backableStateInfo = new BackableStateInfo(SceneDefine.ViewId.HomeHub, new HomeHubViewController.HomeHubViewInfo()
                    {
                        DefaultViewId = SceneDefine.ViewId.SingleModeStart,
                        StartViewInfo = viewInfo,
                    });

                    _view.SupportCardRankingButton.ChangeScene(backableStateInfo);
                };

                _view.SupportCardRankingButton.Show();
                
                // 座標更新（トレーナーガイドの有無で可変）
                var xPos = SUPPORT_CARD_RANKING_BUTTON_POS_X_DEFAULT;
                if (_view.RaceFitAssistanceStatusLabel.IsActive())
                {
                    xPos += SUPPORT_CARD_RANKING_BUTTON_OFFSET_POS_X_WITH_RACE_FIT_ASSISTANCE_LABEL;
                }
                _view.SupportCardRankingButton.RectTransform.anchoredPosition = new Vector2(xPos , SUPPORT_CARD_RANKING_BUTTON_POS_Y_DEFAULT);
            }
            else
            {
                _view.SupportCardRankingButton.Hide();
            }
        }

        /// <summary>
        /// トレーナーガイドのラベルの表示/非表示
        /// </summary>
        /// <param name="step"></param>
        /// <remarks>
        /// 育成ウマ娘選択、サポカ選択、継承ウマ娘選択の画面で表示する
        /// 継承ウマ娘選択の画面では、ラベルの位置を調整する
        /// </remarks>
        private void SetupRaceFitAssistanceLabel(Step step)
        {
            var anchoredPos = Vector2.zero;
            switch (step)
            {
                default:
                    _view.RaceFitAssistanceStatusLabel.Setup(false);
                    break;
                case Step.CardSelect:
                    // 右上基準、初期座標で表示
                    _view.RaceFitAssistanceStatusLabel.SetAnchorVector(ANCHORS_VECTOR_TOP_RIGHT);
                    anchoredPos.x = RACE_FIT_ASSISTANCE_LABEL_TOP_POS_X;
                    anchoredPos.y = RACE_FIT_ASSISTANCE_LABEL_TOP_POS_Y_DEFAULT;
                    _view.RaceFitAssistanceStatusLabel.SetAnchoredPosition(anchoredPos);
                    _view.RaceFitAssistanceStatusLabel.Setup();
                    break;
                case Step.SuccessionSelect:
                    // 右上基準、y座標のみ調整して表示
                    _view.RaceFitAssistanceStatusLabel.SetAnchorVector(ANCHORS_VECTOR_TOP_RIGHT);
                    anchoredPos.x = RACE_FIT_ASSISTANCE_LABEL_TOP_POS_X;
                    anchoredPos.y = RACE_FIT_ASSISTANCE_LABEL_TOP_POS_Y_SUCCESSION_SELECT;
                    _view.RaceFitAssistanceStatusLabel.SetAnchoredPosition(anchoredPos);
                    _view.RaceFitAssistanceStatusLabel.Setup();
                    break;
                case Step.EquipSelect:
                    // 中央右基準、初期座標で表示
                    _view.RaceFitAssistanceStatusLabel.SetAnchorVector(ANCHORS_VECTOR_CENTER_RIGHT);
                    anchoredPos.x = RACE_FIT_ASSISTANCE_LABEL_MID_POS_X;
                    anchoredPos.y = RACE_FIT_ASSISTANCE_LABEL_MID_POS_Y_EQUIP_SELECT;
                    _view.RaceFitAssistanceStatusLabel.SetAnchoredPosition(anchoredPos);
                    _view.RaceFitAssistanceStatusLabel.Setup();
                    break;
                
            }
        }

        /// <summary>
        /// キャラ選択時ボイス、モーション再生
        /// </summary>
        /// <param name="model"></param>
        public void PlaySelectedMotion(SimpleModelController model)
        {
            if (model == null) return;
            var systemText = AudioManager.Instance.PlaySystemVoice_OtherCharaSelectCommon(
                model.GetCharaID(), model.GetCardId(), model.GetDressId(), true
            );
            if (systemText == null) return;
            _lastPlayAudio = AudioManager.Instance.GetAudioPlayback(systemText.CueSheet, systemText.CueId);
            model.PlayLipSyncAndMotion(systemText, nextMotion: model.IdleMotionSetMaster);
        }

        /// <summary>
        /// 現在のキャラのタッチリアクションボイス、モーションを再生
        /// </summary>
        public void PlayTapMotion()
        {
            // タップ待ち時間を統一
            if (_isTapWait) return;
            _isTapWait = true;
            DG.Tweening.DOVirtual.DelayedCall(GameDefine.CHARACTER_TAP_REACTION_WAIT, () => _isTapWait = false);

            StopVoice();
            var model = CharaViewer.GetModel();
            if (model == null)
            {
                return;
            }

            var systemText = AudioManager.Instance.PlaySystemVoice_OtherCharaSelectCommon(
                model.GetCharaID(), model.GetCardId(), model.GetDressId(), true
            );
            if (systemText == null)
            {
                return;
            }

            _lastPlayAudio = AudioManager.Instance.GetAudioPlayback(systemText.CueSheet, systemText.CueId);
            model.PlayLipSyncAndMotion(systemText, nextMotion: model.IdleMotionSetMaster);
        }

        /// <summary>
        /// ボイス停止
        /// </summary>
        public void StopVoice()
        {
            if (CharaViewer == null) return;
            AudioManager.Instance.StopVoice(_lastPlayAudio);
            var model = CharaViewer.GetModel();
            if (model == null) return;
            model.StopLipSync();
        }

        /// <summary>
        /// MainBgの位置とサイズ情報を取得する
        /// </summary>
        /// <returns></returns>
        private void GetMainBgRect(ref Rect rect)
        {
            var rectTransform = BGManager.MainBg.rectTransform;
            rect.x = rectTransform.anchoredPosition.x;
            rect.y = rectTransform.anchoredPosition.y;
            rect.width = rectTransform.sizeDelta.x;
            rect.height = rectTransform.sizeDelta.y;
        }

        /// <summary>
        /// UIのMainBgにサイズと位置を設定する
        /// </summary>
        /// <param name="rect"></param>
        private void SetMainBgRect(ref Rect rect)
        {
            var rectTransform = BGManager.MainBg.rectTransform;
            Vector2 resultPosition;
            Vector2 resultSize;

            resultPosition.x = rect.x;
            resultPosition.y = rect.y;
            resultSize.x = rect.width;
            resultSize.y = rect.height;
            rectTransform.anchoredPosition = resultPosition;
            rectTransform.sizeDelta = resultSize;
        }

        /// <summary>
        /// 時間指定つきカメラ移動
        /// </summary>
        /// <param name="targetIndex"></param>
        /// <param name="time"></param>
        private void MoveToCamera(SingleModeStartResultCharaViewer.ModelIndex targetIndex,float time)
        {
            var settingData = CharaViewer.SettingData;

            CharaViewer.MoveToCamera(targetIndex, time);

            _bgMoveTime = time;
            _bgMoveDeltaTime = 0.0f;
            GetMainBgRect(ref _beginRect);
            _endRect = _initialBgRect;
            switch (targetIndex)
            {
                case SingleModeStartResultCharaViewer.ModelIndex.Center:
                    break;
                case SingleModeStartResultCharaViewer.ModelIndex.Left:
                    _endRect.x += settingData.LeftBgOffset.x;
                    _endRect.y += settingData.LeftBgOffset.y;
                    _endRect.width *= settingData.LeftBgSizeScale;
                    _endRect.height *= settingData.LeftBgSizeScale;
                    break;
                case SingleModeStartResultCharaViewer.ModelIndex.Right:
                    _endRect.x += settingData.RightBgOffset.x;
                    _endRect.y += settingData.RightBgOffset.y;
                    _endRect.width *= settingData.RightBgSizeScale;
                    _endRect.height *= settingData.RightBgSizeScale;
                    break;
            }

            if (time <= 0.0f)
            {
                SetMainBgRect(ref _endRect);
            }

#if UNITY_EDITOR
            _targetModelIndex = targetIndex;
#endif
        }

        /// <summary>
        /// カメラを指定位置に移動する
        /// </summary>
        /// <param name="targetIndex"></param>
        /// <param name="moveTime"></param>
        public void MoveToCamera(SingleModeStartResultCharaViewer.ModelIndex targetIndex)
        {
            var settingData = CharaViewer.SettingData;
            float time = settingData.CameraMoveTime;
            //BeginView来る前に呼び出された時は、初期化時なので即座に移動する
            if (!IsBegin)
                time = 0.0f;

            MoveToCamera(targetIndex,time);
        }

        /// <summary>
        /// TrainedDataからキャラをセットアップする
        /// </summary>
        /// <param name="index"></param>
        /// <param name="data"></param>
        public void SetupCharacterFromTrainedData(SingleModeStartResultCharaViewer.ModelIndex index, WorkTrainedCharaData.TrainedCharaData data,bool isHeightDiff = false)
        {
            CardDressIdSet idSet = null;
            if(data != null)
            {
                idSet = new CardDressIdSet(data.CardId, data.GetRaceDressId(true));
            }

            //nullの場合は外すを選択されたので消しておく
            if (idSet != null)
            {
                CharaViewer.CreateModel((int)index, idSet, (model) =>
                {
                    CharaViewer.SetModelPosition(index, isHeightDiff);
                    model.PlayMotion(model.IdleMotionSetMaster, startType: SimpleModelController.MotionStartType.Loop);
                    model.PlayableAnimator.UpdateMotion(0); //Updateモーション後に呼び出される可能性があるので、ここで初期状態を設定しておく
                });
            }
            else
            {
                CharaViewer.SetModelVisible(false, (int)index);
            }
        }

        public void RequestIndex(System.Action onComplete)
        {
            // チュートリアル中はフレンドキャラクターはダミーを利用する
            if (TutorialManager.IsTutorialExecuting())
            {
                onComplete?.Invoke();
                return;
            }
            
            var request = new PreSingleModeIndexRequest();
            request.Send((res) =>
            {
#if CYG_DEBUG
                // ダミー通信処理(内部でデバッグONの場合置き換え処理
                DebugPageTrainingChallenge.ApplyDebugPreModelIndexResponse(res);
#endif

                // ワークデータへ反映
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                WorkDataManager.Instance.ScenarioRecordData.UpdateMaxRecordData(res.data.scenario_record_highest_score_array);

                // レンタル情報を構築する
                (UserInfoAtFriend[] userInfos, WorkTrainedCharaData.TrainedCharaData[] traineds) BuildRentalInfo(SingleModeSuccessionTrainedChara charaData)
                {
                    if (charaData == null) return (null, null);

                    // 自分のViewerIdは弾く(イベント系で入っている可能性がある)
                    var userViewerId = WorkDataManager.Instance.UserData.ViewerId;

                    for (int i = 0; i < charaData.succession_trained_chara_array.Length; ++i)
                    {
                        charaData.succession_trained_chara_array[i].owner_viewer_id = charaData.summary_user_info_array[i].viewer_id;
                    }
                    var trainedCharaArray = charaData.succession_trained_chara_array
                        .Where(chara => chara.viewer_id != userViewerId)
                        .Select(chara => new WorkTrainedCharaData.TrainedCharaData(chara))
                        .ToArray();
                    
                    var userInfoArray = charaData.summary_user_info_array
                        .Where(userInfo => userInfo.viewer_id != userViewerId)
                        .ToArray();

                    return (userInfoArray, trainedCharaArray);
                }

                // レンタル
                var rentalInfo = BuildRentalInfo(res.data.succession_trained_chara_data);
                Entry.RentalTrainedCharaArray = rentalInfo.traineds;
                Entry.RentalUserInfoArray = rentalInfo.userInfos;

                // イベントレンタル
                var eventRentalInfo = BuildRentalInfo(res.data.event_succession_trained_chara_data);
                Entry.EventRentalTrainedCharaArray = eventRentalInfo.traineds;
                Entry.EventRentalUserInfoArray = eventRentalInfo.userInfos;

                // サポカランキング情報
                var workrainingChallenge = WorkDataManager.Instance.TrainingChallengeData;
                workrainingChallenge?.UpdateSingleModeStartSupportCardRankingButton((TrainingChallenge.TrainingChallengeDefine.SupportCardRankingState)res.data.support_card_ranking_state);

                // フレンドサポートカード情報
                var friendSupportCardData = res.data.friend_support_card_data;

                // 更新することが確定していれば、リストの初期化をする（情報が残っている場合があるため）
                if((friendSupportCardData.summary_user_info_array != null && friendSupportCardData.summary_user_info_array.Any())
                   && (friendSupportCardData.support_card_data_array != null && friendSupportCardData.support_card_data_array.Any()))
                {
                    var workSupportDeckData = WorkDataManager.Instance.SupportDeckData;
                    workSupportDeckData.ClearFriendSupportCardInfoList();
                }
                OnCompleteRequestSupportCard(friendSupportCardData.summary_user_info_array, friendSupportCardData.support_card_data_array);

                // サポートカードレンタルキャンペーン関連
                OnCompleteRequestCampaignRentalSupportCard(res.data);

                // AccountHold状態を保持
                TempData.Instance.ShopSubscriptionData.IsValidOnGame = res.data.subscription_is_valid;

                onComplete?.Invoke();
            } , stallOneSecond:true);
        }

        /// <summary>
        /// リロードリクエストを投げる
        /// </summary>
        /// <param name="entry"></param>
        /// <param name="onComplete"></param>
        public static void ReloadSupportCard(Action onComplete)
        {
            var workSupportDeckData = WorkDataManager.Instance.SupportDeckData;
            var request = new PreSingleModeFriendSupportCardReloadRequest()
            {
                exclude_viewer_id_array = workSupportDeckData.GetFriendSupportCardInfoList()
                    .Select(info => info.ViewerId).ToArray()
            };
            request.Send((res) =>
            {
                OnCompleteRequestSupportCard(res.data.summary_user_info_array, res.data.support_card_data_array);
                onComplete?.Invoke();
            },stallOneSecond:true);
        }

        /// <summary>
        /// サーバーから取得したユーザー情報をEntryInfoに格納する
        /// </summary>
        private static void OnCompleteRequestSupportCard(UserInfoAtFriend[] userInfoArray, UserSupportCard[] supportInfoArray)
        {
            WorkDataManager.Instance.SupportDeckData.ApplyFriendSupportCardInfoList(userInfoArray, supportInfoArray);
        }

        /// <summary>
        /// サーバーから取得したサポートカードレンタルキャンペーン関連のデータをワークに反映する
        /// </summary>
        private static void OnCompleteRequestCampaignRentalSupportCard(PreSingleModeIndexResponse.CommonResponse serverData)
        {
            // まだ時限解放されていないなら何もしない
            WorkDataManager.Instance.CampaignRentalSupportCardData.UpdateUnlockCampaignRentalSupportCard(); // 時限解放チェック
            if (!WorkDataManager.Instance.CampaignRentalSupportCardData.IsUnlockCampaignRentalSupportCard)
                return;

            // サポートカードレンタルキャンペーンのワークデータへ反映
            WorkDataManager.Instance.CampaignRentalSupportCardData.Apply(serverData);
        }

        public override void OnClickBackButton()
        {
            OnClickOsBackKey();
        }

        public override void OnClickOsBackKey()
        {
            _currentStepUI.OnClickBackButton();
        }
    }
}
