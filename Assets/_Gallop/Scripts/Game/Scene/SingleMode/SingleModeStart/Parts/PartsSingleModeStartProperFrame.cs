using UnityEngine;
using System.Collections.Generic;
using System.Linq;
namespace Gallop
{
    using static MasterSuccessionFactorEffect.SuccessionFactorEffect;
    [AddComponentMenu("")]
    public sealed class PartsSingleModeStartProperFrame : PartsProperFrame
    {
        [System.Serializable]
        public class HighlightObject
        {
            public FactorTargetType type;
            public GameObject _highlight;
        }

        [Header("ハイライト")]
        public HighlightObject[] _highlightArray = null;

        public sealed class InfoWithBonus : Info
        {
            public PartsCharacterProperList.ProperParameter _paramWithBonus;
            public List<FactorTargetType> _highlightArrayTargetList = new List<FactorTargetType>();
            public InfoWithBonus(WorkCardData.CardData data , 
                                WorkTrainedCharaData.TrainedCharaData successionFirst, 
                                WorkTrainedCharaData.TrainedCharaData successionSecond) : base(data)
            {
                var bonus = WorkDataUtil.CalcSuccessionBonusParameter(successionFirst);
                WorkDataUtil.CalcSuccessionBonusParameter(successionSecond,ref bonus);
                var _paramWithBonus = WorkDataUtil.CreateProperParameterWithSuccessionBonus(data,bonus);
                if(_turf < _paramWithBonus.Turf.Grade)
                {
                    _turf = _paramWithBonus.Turf.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Turf);
                }
                if(_dirt < _paramWithBonus.Dirt.Grade)
                {
                    _dirt = _paramWithBonus.Dirt.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Dirt);
                }
                if(_short < _paramWithBonus.Short.Grade)
                {
                    _short = _paramWithBonus.Short.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Short);
                }
                if(_mile < _paramWithBonus.Mile.Grade)
                {
                    _mile = _paramWithBonus.Mile.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Mile);
                }
                if(_middle < _paramWithBonus.Middle.Grade)
                {
                    _middle = _paramWithBonus.Middle.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Middle);
                }
                if(_long < _paramWithBonus.Long.Grade)
                {
                    _long = _paramWithBonus.Long.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Long);
                }
                if(_nige < _paramWithBonus.Nige.Grade)
                {
                    _nige = _paramWithBonus.Nige.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Nige);
                }
                if(_senko < _paramWithBonus.Senko.Grade)
                {
                    _senko = _paramWithBonus.Senko.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Senko);
                }
                if(_sashi < _paramWithBonus.Sashi.Grade)
                {
                    _sashi = _paramWithBonus.Sashi.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Sashi);
                }
                if(_oikomi < _paramWithBonus.Oikomi.Grade)
                {
                    _oikomi = _paramWithBonus.Oikomi.Grade;
                    _highlightArrayTargetList.Add(FactorTargetType.Oikomi);
                }
            }
        }

        public void Setup(WorkCardData.CardData data , 
                        WorkTrainedCharaData.TrainedCharaData successionFirst, 
                        WorkTrainedCharaData.TrainedCharaData successionSecond)
        {
            var info = new InfoWithBonus(data,successionFirst,successionSecond);
            SetupContents(info);
        }

        private void SetupContents(InfoWithBonus info)
        {
            base.SetupContents(info);
            foreach(var highlight in _highlightArray.Select(h => h._highlight))
            {
                highlight.SetActive(false);
            }
            foreach(var type in info._highlightArrayTargetList)
            {
                _highlightArray.First(obj => obj.type == type)._highlight.SetActive(true);
            }
        }
    }
}