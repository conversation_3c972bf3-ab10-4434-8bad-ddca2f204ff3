using UnityEngine;

namespace Gallop
{
    using static StaticVariableDefine.SingleMode.SingleModeRoutePageRace;

    #region Model
    
    public interface ISingleModeRoutePageRaceModel
    {
        public MasterSingleModeRouteRace.SingleModeRouteRace RouteRace { get; }
        public SingleModeDefine.ScenarioId ScenarioId { get; }
        public int TurnSetId { get; }
        public MasterSingleModeProgram.SingleModeProgram SingleModeProgram { get; }
        public bool IsNextRouteTarget { get; }
    }

    /// <summary>
    /// 育成プレー中用
    /// </summary>
    public class SingleModeRoutePageRaceModelForPlaying : ISingleModeRoutePageRaceModel
    {
        public MasterSingleModeRouteRace.SingleModeRouteRace RouteRace { get; }
        public SingleModeDefine.ScenarioId ScenarioId => WorkDataManager.Instance.SingleMode.GetScenarioId();
        public int TurnSetId => WorkDataManager.Instance.SingleMode.GetTurnSetId();
        public MasterSingleModeProgram.SingleModeProgram SingleModeProgram => RouteRace.GetProgram(ScenarioId);
        public bool IsNextRouteTarget => RouteRace.Id == WorkDataManager.Instance.SingleMode.GetNextRouteTarget()?.Id;

        public SingleModeRoutePageRaceModelForPlaying(MasterSingleModeRouteRace.SingleModeRouteRace routeRace)
        {
            RouteRace = routeRace;
        }
    }

    /// <summary>
    /// 育成開始前に表示する用
    /// </summary>
    public class SingleModeRoutePageRaceModelForStart : ISingleModeRoutePageRaceModel
    {
        public MasterSingleModeRouteRace.SingleModeRouteRace RouteRace { get; }
        public SingleModeDefine.ScenarioId ScenarioId => (SingleModeDefine.ScenarioId)(int)SaveDataManager.Instance.SaveLoader.LastSingleModeScenarioId;
        public int TurnSetId => MasterDataManager.Instance.masterSingleModeScenario.Get((int)ScenarioId)?.TurnSetId ?? 1;
        public MasterSingleModeProgram.SingleModeProgram SingleModeProgram => RouteRace.GetProgram(ScenarioId);
        
        /// <summary> 育成開始前は目標進捗の概念がない </summary>
        public bool IsNextRouteTarget => false;

        public SingleModeRoutePageRaceModelForStart(MasterSingleModeRouteRace.SingleModeRouteRace routeRace)
        {
            RouteRace = routeRace;
        }
    }
    
    #endregion
    
    [AddComponentMenu("")]
    public sealed class SingleModeRoutePageRace : MonoBehaviour
    {
        public enum BgType
        {
            White,
            Yellow
        }

        [SerializeField]
        private ImageCommon _bg = null;
        [SerializeField]
        private GameObject _titleRoot = null;
        [SerializeField]
        private GameObject _contentsRoot = null;
        [Header("Header")]
        [SerializeField]
        private ImageCommon _headerImage = null;
        /// <summary>
        /// クリアマーク
        /// </summary>
        [SerializeField]
        private ImageCommon _clearMark = null;

        /// <summary>
        /// 詳細ボタン
        /// </summary>
        [SerializeField]
        private ButtonCommon _infoButton = null;

        /// <summary>
        /// レース名
        /// </summary>
        [SerializeField]
        private TextCommon _raceName = null;
        
        /// <summary>
        /// 挑戦中表示
        /// </summary>
        [SerializeField]
        private GameObject _challengeBadge = null;

        /// <summary>
        /// 目標決定
        /// </summary>
        [SerializeField]
        private GameObject _targetChangeBadge = null;
        [SerializeField]
        private TextCommon _targetChangeText = null;

        /// <summary>
        /// 選択式目標が未決定の通知
        /// </summary>
        [SerializeField]
        private GameObject _selectableTargetBadge = null;

        /// <summary>
        /// 開催時期
        /// </summary>
        [Header("Turn")]
        [SerializeField]
        private TextCommon _turnText = null;
        
        [SerializeField]
        private TextCommon _turnTitleText = null;
        
        /// <summary>
        /// 残り〇ターン
        /// </summary>
        [SerializeField]
        private TextCommon _remainTurnText = null;
        
        [Header("Separator")]
        [SerializeField]
        private ImageCommon _line = null;
        /// <summary>
        /// 条件
        /// </summary>
        [Header("Condition")]
        [SerializeField]
        private RectTransform _conditionLabelRoot = null;
        
        [SerializeField]
        private TextCommon _targetRaceWinText = null;
        [SerializeField]
        private TextCommon _separator = null;
        [SerializeField]
        private TextCommon _conditionText = null;
        
        /// <summary>
        /// 条件に対して残り何人か
        /// </summary>
        [SerializeField]
        private TextCommon _conditionRemain = null;

        private ISingleModeRoutePageRaceModel _model;

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="data"></param>
        public void Setup(MasterSingleModeRouteRace.SingleModeRouteRace data, bool isVisibleBg)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            _model = new SingleModeRoutePageRaceModelForPlaying(data);

            //まずは全て非表示
            SetInvisible();

            var turn = workSingle.GetCurrentTurn();
            var isClear = data.Turn < turn;// 通過した目標はクリア表記

            var type = (SingleModeDefine.ConditionType)_model.RouteRace.ConditionType;
            switch (type)
            {
                case SingleModeDefine.ConditionType.Race:
                case SingleModeDefine.ConditionType.ReplaceRace:
                case SingleModeDefine.ConditionType.AdditiveRace:
                    SetupToRace();
                    break;
                case SingleModeDefine.ConditionType.Fun:
                    SetupToFan();
                    break;
                case SingleModeDefine.ConditionType.Grade:
                    SetupToGrade();
                    break;
                case SingleModeDefine.ConditionType.WinPoint:
                    SetupToWinPoint(isClear);
                    break;
                case SingleModeDefine.ConditionType.ApprovalRate:
                    SetupToApprovalRate(isClear);
                    break;
                case SingleModeDefine.ConditionType.ApprovalPoint:
                    SetupToApprovalPoint(isClear);
                    break;
                case SingleModeDefine.ConditionType.Pending:
                    SetupToPending();
                    break;
            }

            // Clear
            SetupClearMark(isClear);
            SetBgColor(!isClear);
            SetEnableInfoButton(!isClear);
            // 挑戦中
            if (_model.IsNextRouteTarget)
            {
                var remainTurn = workSingle.GetDisplayRemainTurnNum();
                SetChallenge(remainTurn);
            }
            
            var isActiveTargetRace = workSingle.IsActiveTargetRace(data);
            var determineRace = data.GetDetermineRace(_model.ScenarioId);
            
            // タイトルプレート
            SetupHeaderImage(data);
            
            if (isClear == false)
            {
                // 目標変化後「目標決定」
                if (determineRace == SingleModeDefine.TargetRaceDetermine.ChangeAfter)
                {
                    // 目標変化
                    _targetChangeBadge.SetActiveWithCheck(true);
                    _targetChangeText.text = TextId.SingleMode0447.Text();
                }
                
                // 目標追加後「目標追加」
                if (determineRace == SingleModeDefine.TargetRaceDetermine.Add)
                {
                    // 目標追加
                    _targetChangeBadge.SetActiveWithCheck(true);
                    _targetChangeText.text = TextId.SingleMode508021.Text();
                }

                // 選択式目標
                bool isLockSelectableRace = false;
                if (determineRace == SingleModeDefine.TargetRaceDetermine.Selectable)
                {
                    // 未決定状態
                    if (isActiveTargetRace == false)
                    {
                        isLockSelectableRace = true;
                        _selectableTargetBadge.SetActiveWithCheck(isLockSelectableRace);
                    }
                    else
                    {
                        // 目標決定
                        _targetChangeBadge.SetActiveWithCheck(true);
                        _targetChangeText.text = TextId.SingleMode0446.Text();
                    }
                }
                _titleRoot.SetActiveWithCheck(!isLockSelectableRace); // ロック中は中身なし
                _contentsRoot.SetActiveWithCheck(!isLockSelectableRace);
            }

            // 目標削除タイプ（Vanishの場合はリストに含まれないため何もしない）
            if (determineRace == SingleModeDefine.TargetRaceDetermine.Delete)
            {
                if (isActiveTargetRace == false)
                {
                    // 目標が削除されている
                    SetupClearMark(false);
                    SetBgColor(false);
                    SetEnableInfoButton(false);
                    _headerImage.sprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.FRAME_LIST_TARGET_02);
                }
            }

            //用途によっては背景を非表示にする
            _bg.enabled = isVisibleBg;
        }

        /// <summary>
        /// タイトルプレート
        /// </summary>
        /// <param name="data"></param>
        private void SetupHeaderImage(MasterSingleModeRouteRace.SingleModeRouteRace data)
        {
            Sprite headerImageSprite;
            switch (data.GetDetermineRace(_model.ScenarioId))
            {
                case SingleModeDefine.TargetRaceDetermine.ChangeAfter:
                    // 強調表示
                    headerImageSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.FRAME_LIST_TARGET_02);
                    break;
                
                case SingleModeDefine.TargetRaceDetermine.Selectable:
                case SingleModeDefine.TargetRaceDetermine.VanishAfterAppear:
                case SingleModeDefine.TargetRaceDetermine.Add:
                    // 目標が出現している場合は強調表示
                    var isActiveTargetRace = WorkDataManager.Instance.SingleMode.IsActiveTargetRace(data);
                    var spriteName = isActiveTargetRace ? AtlasSpritePath.Common.FRAME_LIST_TARGET_02 : AtlasSpritePath.Common.FRAME_LIST_TARGET_00;
                    headerImageSprite = UIManager.CommonAtlas.GetSprite(spriteName);
                    break;
                
                default:
                    headerImageSprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.FRAME_LIST_TARGET_00);
                    break;
            }

            _headerImage.sprite = headerImageSprite;
        }
        
        /// <summary>
        /// 表示準備（育成開始前用）
        /// </summary>
        public void SetupForStartInfo(MasterSingleModeRouteRace.SingleModeRouteRace data)
        {
            _model = new SingleModeRoutePageRaceModelForStart(data);

            //まずは全て非表示
            SetInvisible();

            var type = (SingleModeDefine.ConditionType)_model.RouteRace.ConditionType;
            switch (type)
            {
                case SingleModeDefine.ConditionType.Race:
                case SingleModeDefine.ConditionType.ReplaceRace:
                case SingleModeDefine.ConditionType.AdditiveRace:
                    SetupToRace(true);
                    break;
                case SingleModeDefine.ConditionType.Fun:
                    SetupToOther();
                    break;
                case SingleModeDefine.ConditionType.Grade:
                    SetupToOther();
                    break;
                case SingleModeDefine.ConditionType.WinPoint:
                    SetupToWinPoint(isClear: false);
                    break;
                case SingleModeDefine.ConditionType.ApprovalRate:
                    SetupToApprovalRate(isClear: false);
                    break;
                case SingleModeDefine.ConditionType.ApprovalPoint:
                    SetupToApprovalPoint(isClear: false);
                    break;
                case SingleModeDefine.ConditionType.Pending:
                    SetupToPending();
                    break;
            }

            var determineRace = data.GetDetermineRace(_model.ScenarioId);
            if (determineRace == SingleModeDefine.TargetRaceDetermine.Selectable)
            {
                // 選択式レースは未判明状態
                _titleRoot.SetActiveWithCheck(false);
                _contentsRoot.SetActiveWithCheck(false);
                _selectableTargetBadge.SetActiveWithCheck(true);
            }
        }

        //全て非表示
        private void SetInvisible()
        {
            _clearMark.gameObject.SetActive(false);
            _infoButton.gameObject.SetActive(false);
            _challengeBadge.SetActiveWithCheck(false);
            _targetChangeBadge.SetActiveWithCheck(false);
            _selectableTargetBadge.SetActiveWithCheck(false);
            _turnText.text = string.Empty;
            _conditionRemain.text = string.Empty;
            _remainTurnText.text = string.Empty;
            _targetRaceWinText.SetActiveWithCheck(false);
            _separator.SetActiveWithCheck(false);
        }

        /// <summary>
        /// レース用にセットアップ
        /// </summary>
        private void SetupToRace(bool isBeforeSingleStart = false)
        {
            _turnTitleText.text = TextId.SingleMode0130.Text(); // 本番
            _raceName.text = SingleModeUtils.GetRouteRaceConditionText(_model.RouteRace);

            _turnText.text = SingleModeUtils.GetRouteRaceLimitTurnText(_model.ScenarioId, _model.TurnSetId, _model.RouteRace);

            var program = _model.SingleModeProgram;
            if(program.NeedFanCount == 0)
            {
                SetupRaceWithoutFan(program);
            }
            else
            {
                SetupRaceWithFan(program);
            }

            _infoButton.gameObject.SetActive(true);
            _infoButton.SetOnClick(() => OnClickInfo(isBeforeSingleStart));
        }

        /// <summary>
        /// ファン数取得
        /// </summary>
        private int GetFanCount()
        {
            var character = WorkDataManager.Instance.SingleMode.Character;
            if (character == null) return 0;
            return character.FanCount;
        }

        /// <summary>
        /// レース目標/ファンあり
        /// </summary>
        private void SetupRaceWithFan(MasterSingleModeProgram.SingleModeProgram program)
        {
            var needFanCount = program.NeedFanCount;
            var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
            var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
            var condition = TextUtil.Format(TextId.Common0270.Text(),
                TextUtil.ToCommaSeparatedString(program.NeedFanCount));
            condition = TextUtil.Format("<color=#{1}>{0}</color>", condition, orangeColorCode);
            var conditionText = string.Empty;
            
            var workSingle = WorkDataManager.Instance.SingleMode;
            var fanCount = GetFanCount();
            var isNoWinGrade = workSingle.Character?.CharaGrade == SingleModeDefine.CharaGradeType.NoWin;
            
            // 開始前 
            if(WorkDataManager.Instance.SingleMode.IsExistPlayingData == false)
            {
                conditionText = TextUtil.Format(TextId.SingleMode0127.Text(), condition);
                _conditionText.SetTextWithCustomTag(conditionText);
                return;
            }
            
            //次の目標ならば
            if(_model.IsNextRouteTarget)
            {
                if(!isNoWinGrade && needFanCount <= fanCount) //出走条件達成していたら何も表示しない
                {
                    _conditionText.text = string.Empty;
                    SetupRemainCount(needFanCount);
                    return;
                }
                else
                {
                    if(isNoWinGrade) //未勝利時
                    {
                        _targetRaceWinText.SetActiveWithCheck(true);
                        if (needFanCount > fanCount) //未勝利かつファン数も不足
                        {
                            _separator.SetActiveWithCheck(true);
                            conditionText = TextUtil.Format(TextId.SingleMode0127.Text(), condition);
                        }
                    }
                    else //勝利はしているがファン数が足りない
                    {
                        conditionText = TextUtil.Format(TextId.SingleMode0127.Text(), condition);
                    }
                }

                //ファン数もしくは出走条件達成表示
                SetupRemainCount(needFanCount);
            }
            else //未来の目標は必要ファン数のみ表示する
            {
                conditionText = TextUtil.Format(TextId.SingleMode0127.Text(), condition);
            }
            _conditionText.SetTextWithCustomTag(conditionText);
            
        }

        /// <summary>
        /// レース目標/ファンなし
        /// </summary>
        private void SetupRaceWithoutFan(MasterSingleModeProgram.SingleModeProgram program)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            // 開始前 
            if(WorkDataManager.Instance.SingleMode.IsExistPlayingData == false)
            {
                _conditionText.text = TextId.SingleMode0255.Text(); //特になし
                return;
            }
            
            var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
            var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
            var condition = TextUtil.Format(TextId.Common0270.Text(),
                TextUtil.ToCommaSeparatedString(program.NeedFanCount));
            condition = TextUtil.Format("<color=#{1}>{0}</color>", condition, orangeColorCode);
            var isNoWinGrade = workSingle.Character?.CharaGrade == SingleModeDefine.CharaGradeType.NoWin;
            //次の目標かつデビュー戦でないならば勝利済みか判定して達成表示する
            if(_model.IsNextRouteTarget && program.RaceGrade != RaceDefine.Grade.Beginner)
            {
                _targetRaceWinText.SetActiveWithCheck(isNoWinGrade); //未勝利戦で一着表示
                SetupRemainCount(program.NeedFanCount);
            }
            else
            {
                _conditionText.text = TextId.SingleMode0255.Text(); //特になし
            }
        }

        /// <summary>
        /// 残り必要ファン数表示部分を更新
        /// </summary>
        /// <param name="needFanCount"></param>
        private void SetupRemainCount(int needFanCount)
        {
            var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
            var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
            var isNoWinGrade = WorkDataManager.Instance.SingleMode.Character?.CharaGrade == SingleModeDefine.CharaGradeType.NoWin;
            var leastRemainNum = needFanCount - GetFanCount();
            if (leastRemainNum <= 0 && !isNoWinGrade) //条件達成
            {
                _conditionRemain.FontColor = FontColorType.Plus;
                _conditionRemain.text = TextId.SingleMode0341.Text();
            }
            else //未達成
            {
                if(leastRemainNum > 0) //ファン数不足していたら表示
                {
                    _conditionRemain.FontColor = FontColorType.Brown;
                    _conditionRemain.text = TextUtil.Format(TextId.SingleMode0342.Text(),leastRemainNum.ToCommaSeparatedString(),orangeColorCode);
                }
                else //そうでなければ空欄
                {
                    _conditionRemain.text = string.Empty;
                }
            }
        }

        /// <summary>
        /// ファン数目標用にセットアップ
        /// </summary>
        private void SetupToFan()
        {
            SetupToOther();

            var fanCount = GetFanCount();
            var remainNum = _model.RouteRace.ConditionValue1 - fanCount;
            if (remainNum > 0)
            {
                _conditionRemain.FontColor = FontColorType.Brown;
                var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
                var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
                var textWithCustomTag = TextUtil.Format(TextId.SingleMode0343.Text(), remainNum.ToCommaSeparatedString(), orangeColorCode);
                _conditionRemain.SetTextWithCustomTag( textWithCustomTag );
            }
            else
            {
                _conditionRemain.FontColor = FontColorType.Plus;
                _conditionRemain.text = TextId.SingleMode0340.Text();
            }
            _conditionRemain.OnUpdate();
        }
        
        /// <summary>
        /// グレード勝利目標用にセットアップ
        /// </summary>
        private void SetupToGrade()
        {
            SetupToOther();

            var workData = WorkDataManager.Instance.SingleMode;
            // 前の目標ターン
            var preTargetRace = workData.GetPreRequiredTargetRace(_model.RouteRace.Turn);
            var preTargetRaceTurn =  preTargetRace != null ? preTargetRace.Turn : 0;
                    
            var grade = (RaceDefine.Grade)_model.RouteRace.ConditionId; // レースグレード
            var needWinCount = _model.RouteRace.ConditionValue2; // 必要な勝利数
            // 現在の勝利数
            var winCount = workData.GetNextTargetRaceGradeWinNum(preTargetRaceTurn, grade, _model.RouteRace.ConditionValue1, _model.RouteRace.Turn);
            // 残り勝利数
            var remainNum = needWinCount - winCount;
            
            if (remainNum > 0)
            {
                _conditionRemain.FontColor = FontColorType.Brown;
                var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
                var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
                var textWithCustomTag = TextUtil.Format(TextId.SingleMode0379.Text(), remainNum.ToCommaSeparatedString(), orangeColorCode);
                _conditionRemain.SetTextWithCustomTag( textWithCustomTag );
            }
            else 
            {
                _conditionRemain.FontColor = FontColorType.Plus;
                _conditionRemain.text = TextId.SingleMode0340.Text();
            }
            _conditionRemain.OnUpdate();
        }
        
        /// <summary>
        /// レース系でない場合の設定
        /// </summary>
        private void SetupToOther()
        {
            _turnTitleText.text = TextId.SingleMode0333.Text(); // 期限
            _raceName.text = SingleModeUtils.GetRouteRaceConditionText(_model.RouteRace);
            _turnText.text = SingleModeUtils.GetRouteRaceLimitTurnText(_model.ScenarioId, _model.TurnSetId, _model.RouteRace);
            
            _conditionLabelRoot.gameObject.SetActive(false);
            _conditionText.text = string.Empty;
        }

        /// <summary>
        /// 勝利ポイント目標用にセットアップ
        /// </summary>
        private void SetupToWinPoint(bool isClear)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;

            SetupToOther();

            var remainNum = 0;
            // クリア済みでは無いときのみ残りポイントの計算処理を行う
            if (!isClear)
            {
                remainNum = _model.RouteRace.ConditionValue1;
                if (_model.IsNextRouteTarget)
                {
                    // いまの目標の場合のみ、表示勝利ポイントを計算
                    var winPoint = workSingleMode.Character.WorkScenarioFree.WinPoints;
                    remainNum = _model.RouteRace.ConditionValue1 - winPoint;
                }
            }

            if (remainNum > 0)
            {
                _conditionRemain.FontColor = FontColorType.Brown;
                var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
                var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
                var textWithCustomTag = TextUtil.Format(TextId.SingleModeScenarioFree419002.Text(), remainNum.ToCommaSeparatedString(), orangeColorCode);
                _conditionRemain.SetTextWithCustomTag(textWithCustomTag);
            }
            else
            {
                // 達成
                _conditionRemain.FontColor = FontColorType.Plus;
                _conditionRemain.text = TextId.SingleMode0340.Text();
            }
            // 育成開始後のみ表示
            _conditionRemain.SetActiveWithCheck(workSingleMode.IsExistPlayingData);

            _conditionRemain.OnUpdate();
        }
        
        /// <summary>
        /// 支持率目標用にセットアップ
        /// </summary>
        private void SetupToApprovalRate(bool isClear)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;

            SetupToOther();

            var remainRate = 0f;
            // クリア済みでは無いときのみ残り支持率の計算処理を行う
            if (!isClear)
            {
                // いまの目標を取得
                var currentRouteRace = workSingleMode.GetNextRouteTarget();

                remainRate = _model.RouteRace.ConditionValue1;
                if (currentRouteRace != null)
                {
                    var needRateRaw = _model.RouteRace.ConditionValue1 * SingleModeScenarioArcDefine.APPROVAL_RATE_SERVER;
                    var remainRateRaw = needRateRaw - workSingleMode.ScenarioArc.ApprovalRate;// 残り支持率(生データなので10倍のint型)

                    remainRate = (float)(needRateRaw - remainRateRaw) / SingleModeScenarioArcDefine.APPROVAL_RATE_SERVER;
                }
            }

            if (remainRate > 0)
            {
                _conditionRemain.FontColor = FontColorType.Brown;
                var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
                var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
                var textWithCustomTag = TextUtil.Format(TextId.SingleModeScenarioArc508021.Text(), remainRate, orangeColorCode);
                _conditionRemain.SetTextWithCustomTag(textWithCustomTag);
            }
            else
            {
                // 達成
                _conditionRemain.FontColor = FontColorType.Plus;
                _conditionRemain.text = TextId.SingleMode0340.Text();
            }
            // 育成開始後のみ表示
            _conditionRemain.SetActiveWithCheck(workSingleMode.IsExistPlayingData);

            _conditionRemain.OnUpdate();
        }
        
        /// <summary>
        /// 支持ポイント目標用にセットアップ
        /// </summary>
        private void SetupToApprovalPoint(bool isClear)
        {
            var workSingleMode = WorkDataManager.Instance.SingleMode;

            SetupToOther();

            var remainNum = 0;
            // クリア済みでは無いときのみ残りポイントの計算処理を行う
            if (!isClear)
            {
                // いまの目標を取得
                var currentRouteRace = workSingleMode.GetNextRouteTarget();

                remainNum = _model.RouteRace.ConditionValue1;
                if (currentRouteRace != null)
                {
                    var approvalPoint = workSingleMode.ScenarioArc.GetApprovalPoint();
                    remainNum = _model.RouteRace.ConditionValue1 - approvalPoint;
                }
            }

            if (remainNum > 0)
            {
                _conditionRemain.FontColor = FontColorType.Brown;
                var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
                var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
                var textWithCustomTag = TextUtil.Format(TextId.SingleModeScenarioArc508020.Text(), remainNum.ToCommaSeparatedString(), orangeColorCode);
                _conditionRemain.SetTextWithCustomTag(textWithCustomTag);
            }
            else
            {
                // 達成
                _conditionRemain.FontColor = FontColorType.Plus;
                _conditionRemain.text = TextId.SingleMode0340.Text();
            }
            // 育成開始後のみ表示
            _conditionRemain.SetActiveWithCheck(workSingleMode.IsExistPlayingData);

            _conditionRemain.OnUpdate();
        }

        /// <summary>
        /// 「未定」目標用にセットアップ
        /// </summary>
        private void SetupToPending()
        {
            // ヘッダーに「未定」表記
            _raceName.text = SingleModeUtils.GetRouteRaceConditionText(_model.RouteRace);

            // 目標ターン・出走条件・詳細ボタンを非表示に
            _turnTitleText.transform.parent.gameObject.SetActive(false);
            _conditionLabelRoot.gameObject.SetActive(false);
            _infoButton.gameObject.SetActive(false);
        }

        /// <summary>
        /// 現在挑戦中の表示に設定
        /// </summary>
        private void SetChallenge(int remainTurn)
        {
            if (WorkDataManager.Instance.SingleMode.IsPendingTurn(_model.RouteRace))
            {
                // 次の目標が「未定」の場合は、「挑戦中」でも何も表示しない
                return;
            }
            
            if (remainTurn == 0 && 
                SingleModeUtils.IsConditionTypeRaceRoute(_model.RouteRace))
            {
                // レース系目標の当日なら「本番」
                _remainTurnText.text = TextId.SingleMode0130.Text();
            }
            else
            {
                var orangeColor = ColorPreset.GetFontColor(FontColorType.Plus);
                var orangeColorCode = ColorUtility.ToHtmlStringRGB(orangeColor);
                _remainTurnText.text = TextUtil.Format(TextId.SingleMode0128.Text(), remainTurn, orangeColorCode);
            }
            
            _challengeBadge.SetActiveWithCheck(true);
            _bg.sprite = UIManager.PreInAtlas.GetSprite(BG_SPRITE_NAME_DIC[BgType.Yellow]);
            _line.sprite = UIManager.PreInAtlas.GetSprite(LINE_SPRITE_NAME_DIC[BgType.Yellow]);
        }

        /// <summary>
        /// クリア済みマークを付ける
        /// </summary>
        /// <param name="isVisible"></param>
        private void SetupClearMark(bool isVisible)
        {
            _clearMark.gameObject.SetActive(isVisible);
        }

        private void SetEnableInfoButton(bool enable)
        {
            _infoButton.SetEnable(enable);
        }
        private void SetBgColor(bool enable)
        {
            _bg.color = enable ? StaticVariableDefine.Parts.ButtonCommonStatic.DEFAULT_COLOR_WHITE : StaticVariableDefine.Parts.ButtonCommonStatic.NO_INTERACTERABLE_COLOR;
        }

        private void OnClickInfo(bool isBeforeSingleStart)
        {
            var program = _model.SingleModeProgram;
            bool hideCondition = true;
            if (WorkDataManager.Instance.SingleMode.IsExistPlayingData)
            {
                hideCondition = !_model.IsNextRouteTarget; //挑戦中じゃなかったら隠す
            }
       
            DialogSingleModeRaceConfirm.OpenInformation(_model.RouteRace.Turn, program, TextId.CustomRace0068.Text(), hideCondition, isBeforeSingleStart);
        }
    }
}
