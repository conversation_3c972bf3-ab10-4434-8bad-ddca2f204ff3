using System.Linq;

namespace Gallop
{
    /// <summary>
    /// アオハル杯編：SingleModeMainCharaController拡張
    /// </summary>
    public class SingleModeMainCharaScenarioTeamRaceController : SingleModeMainCharaController
    {
        /// <summary> 育成TOPにチームメンバーを配置する場合カメラを引いた感じに調整したものを使用する </summary>
        protected override bool UseMainViewFocusCameraScenarioPreset => GetBgModelList(BgDisplayType.Top).Count > 0;
        
        /// <summary>
        /// 育成TOPで表示する背景キャラクターのセットアップ
        /// </summary>
        protected override void SetupBGCharaModel()
        {
            //簡易版だった場合は背景キャラを表示しない
            //とりあえずチーム対抗戦のデータが存在していれば、対抗戦のデータを利用
            var gameQuality = WorkDataManager.Instance.SingleMode.TeamRace?.GameQuality ?? SaveDataManager.Instance.SaveLoader.GameQuality;

            if (gameQuality == GraphicSettings.GameQuality.Light)
            {
                // 前回配置したキャラを破棄
                ClearBgModel();
                return;
            }
            
            var isNormalOrRich = WorkDataManager.Instance.SingleMode.TeamRace.GameQuality == GraphicSettings.GameQuality.Normal || WorkDataManager.Instance.SingleMode.TeamRace.GameQuality == GraphicSettings.GameQuality.Rich;
            if (isNormalOrRich)
            {
                SetupTeamRaceBGCharacter();
            }
        }
        
        /// <summary>
        /// チーム対抗戦用背景キャラクターのセットアップ
        /// </summary>
        private void SetupTeamRaceBGCharacter()
        {
            var workData = WorkDataManager.Instance.SingleMode;

            //配置マスターを抽選する
            var placeMasterList = MasterDataManager.Instance.masterSingleModeTopBgChara.RotSingleModeTopBgCharaList(workData.TeamRace.TeamMemberList.Count);

            //何も取得できなければ終了
            if (placeMasterList == null || !placeMasterList.Any())
            {
                // 前回配置したキャラを破棄
                ClearBgModel();
                return;
            }

            var turnSetId = workData.GetTurnSetId();
            var turn = workData.GetCurrentTurn();
            if (turn > 1) //0ターン目判定を回避する
            {
                //夏/冬が切り替わっているか判定する
                if (SingleModeUtils.GetBgSeasonSubByTurn(turnSetId, turn) != SingleModeUtils.GetBgSeasonSubByTurn(turnSetId, turn - 1))
                {
                    //切り替わっていたら体操服が変わるので配置したキャラを一旦クリアする
                    ClearBgModel();
                }
            }

            // 配置したいランダムなメンバーを抽出
            var randomMember = workData.TeamRace.TeamMemberList.OrderBy(m => System.Guid.NewGuid()).Take(placeMasterList.Count).ToList();

            // 生成済みモデルのうち、配置メンバーに含まれないモデル破棄
            var modelList = GetBgModelList(BgDisplayType.Top);
            for (int i = modelList.Count - 1; i >= 0; i--)
            {
                var bgModel = modelList[i];
                bool exist = randomMember.Exists(a => a.SupportCardId == bgModel.SupportCardId);
                if (exist) continue; //配置したいモデルは削除しない

                _trainingController.RemoveModel(bgModel.Controller);

                _scene.DestroyModel(bgModel);// 破棄
                modelList.Remove(bgModel);
            }

            //ランダムなメンバーを配置
            for (int i = 0; i < randomMember.Count; i++)
            {
                var member = randomMember[i];
                var placeMaster = placeMasterList[i];
                var dress = WorkDataManager.Instance.SingleMode.GetSeasonDressId(SingleModeDefine.DefaultDressId);
                var model = _scene.CreateBgCharaModelBySupportCardId(member.SupportCardId, dress, true);
                if (modelList.Contains(model) == false)
                {
                    modelList.Add(model);
                }
                var modelController = model.Controller;
                modelController.PlayMotion(placeMaster.CharaMotionSet, startType: SimpleModelController.MotionStartType.Loop, bodyBlendTime: 0, isForce: true);
                modelController.transform.localPosition = placeMaster.GetPosition();
                modelController.transform.localRotation = placeMaster.GetRotation();

                _trainingController.AddModel(modelController);
            }

            // イメージエフェクト適用
            _trainingController.UpdateImageEffectCharacterColor();
            
            foreach (var model in modelList)
            {
                Gallop.ModelController.FrameUpdate(model.Controller);
            }

            // 描画順
            UpdateBGCharacterRenderQueue(_scene.FocusCamera.GetCamera());
        }
    }
}

