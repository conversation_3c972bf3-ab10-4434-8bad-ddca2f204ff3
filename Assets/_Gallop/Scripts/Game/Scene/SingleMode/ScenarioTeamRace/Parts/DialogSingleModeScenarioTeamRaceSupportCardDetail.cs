using System;
using UnityEngine;
using System.Linq;
using System.Collections.Generic;

namespace Gallop
{
    using static StaticVariableDefine.SingleMode.DialogSingleModeScenarioTeamRaceSupportCardDetail;
    
    /// <summary>
    /// チーム対抗戦：サポートカード詳細ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeScenarioTeamRaceSupportCardDetail : MonoBehaviour
    {
        private const int INVALID_NPC_ID = -1;


        #region SerializeField, 変数

        [SerializeField]
        private PartsCharacterDetailStatus _statusParts = null;
        [SerializeField]
        private PartsCharacterDetailProper _properParts = null;

        [SerializeField]
        private GameObject[] _guestHideObjectArray = null;
        [SerializeField]
        private GameObject _guestRoot = null;
        [SerializeField]
        private TextCommon _guestTitleName = null;
        [SerializeField]
        private TextCommon _guestCharaName = null;
        [SerializeField]
        private GameObject _guestLabel = null;
        [SerializeField]
        private ImageCommon _aoharuSoul = null;
        [SerializeField]
        private GameObject _guestSkillScrollViewHeader = null;
        [SerializeField]
        private GameObject _guestSkillScrollView = null;
        [SerializeField]
        private LoopScroll _guestSkillListLoopScroll = null;
        [SerializeField]
        private GameObject _guestNoSkillLabel = null;
        [SerializeField]
        private GameObject _guestSkillScrollViewPort = null;
        [SerializeField]
        private ImageCommon _statusRankImage = null;
        #endregion

        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(WorkSingleModeScenarioTeamRace.TeamMember member, bool isGuest)
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var equipSupportCard = workChara.GetEquipSupportCardBySupportCardId(member.SupportCardId);
            WorkSupportCardData.SupportCardData workSupportCard = null; 
            // work化
            if(equipSupportCard != null)
            {
                workSupportCard = equipSupportCard.ConvertWorkSupportCardData(); // 装備サポカは経験値や限突設定
            }
            else
            {
                workSupportCard = new WorkSupportCardData.SupportCardData(member.SupportCardId);// ゲスト用
            }
            
            //ゲストは得意トレーニングアイコンを表示しない
            // ベースのサポカ詳細を派生プレファブで生成
            var baseComponent = DialogSupportCardDetail.Open(workSupportCard,
                prefabPath: ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_TEAM_RACE_SUPPORT_CARD_DETAIL,
                enableObtain: !isGuest, hideToggle: isGuest,
                isEnableRaceFitAssistanceLabel: true);      // 育成中はトレーナーガイドのおすすめラベルが有効
            // ステータス、適性を設定
            var component = baseComponent.GetComponent<DialogSingleModeScenarioTeamRaceSupportCardDetail>();
            
            component.SetupMemberStatus(baseComponent, member, isGuest, showAoharuSoul: SingleModeScenarioTeamRaceUtils.IsSingleModeCharaByCharaId(member.CharaId) == false);

            if(isGuest)
            {
                component.SetupGuestSkillScrollView(workSupportCard);
            }
        }

        /// <summary>
        /// 開く
        /// 出走の自チームと相手チームのサポカ詳細画面
        /// </summary>
        /// <param name="raceMember"></param>
        public static void PushDialog(WorkSingleModeScenarioTeamRace.RunRaceDeckData raceMember)
        {
            if(raceMember.IsPlayerTeam)
            {
                //育成以外のキャラ
                if(raceMember.IsSingleModeChara == false)
                {
                    var teamMember = raceMember.PlayerTeamMember;

                    //自チームのサポカの場合既存処理を利用
                    PushDialog(teamMember, teamMember.IsEquipSupportCard() == false);
                }
            }
            else
            {
                //モブではない場合、ユニークキャラはサポカRになる
                if(raceMember.IsMobChara == false)
                {
                    //該当カードの状態はプレイヤー所持カードと同じスキルセットを出す
                    var workSupCardDataList = WorkDataManager.Instance.SupportCardData.GetSupportCardListByCharaId(raceMember.MobCharaId);

                    WorkSupportCardData.SupportCardData workSupRCard;

                    //未所持のカード
                    if (workSupCardDataList == null || workSupCardDataList.Count == 0)
                    {
                        //work化
                        workSupRCard = new WorkSupportCardData.SupportCardData(raceMember.NpcUniqueCharaSupCardId);
                    }
                    else
                    {
                        workSupRCard = workSupCardDataList.FirstOrDefault(data => data.GetRarity() == (int)GameDefine.SupportCardRarity.RareR);
                    }

                    // ベースのサポカ詳細を派生プレファブで生成
                    var baseComponent = DialogSupportCardDetail.Open(workSupRCard, prefabPath: ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_TEAM_RACE_SUPPORT_CARD_DETAIL, enableObtain: false, hideToggle: true);
                    // ステータス、適性を設定
                    var component = baseComponent.GetComponent<DialogSingleModeScenarioTeamRaceSupportCardDetail>();

                    var teamMember = WorkSingleModeScenarioTeamRace.TeamMember.CreateDefaultMember(raceMember.MobCharaId, raceMember.OpponentInfo);

                    component.SetupMemberStatus(baseComponent, teamMember, true, raceMember.NpcId);

                    //相手チームの場合、ゲストラベルだけ隠す必要がある
                    component._guestLabel.SetActive(false);

                    component.SetupGuestSkillScrollView(workSupRCard);
                }
            }
        }
        
        private void SetupMemberStatus(DialogSupportCardDetail baseComponent, WorkSingleModeScenarioTeamRace.TeamMember member, bool isGuest, int npcId = INVALID_NPC_ID, bool showAoharuSoul = false)
        {
            // ステータス
            var statusParameter = new PartsCharacterDetailStatus.SetupParameter(member);
            // 適性
            var properParameter = new PartsCharacterDetailProper.SetupParameter
            {
                ProperParameter = npcId == INVALID_NPC_ID ? new PartsCharacterProperList.ProperParameter(member.MasterScoutChara) : new PartsCharacterProperList.ProperParameter(MasterDataManager.Instance.masterSingleModeNpc.Get(npcId)),
                DisplayType = PartsCharacterProperList.DisplayType.IconOnly
            };

            _statusParts.Setup(statusParameter);
            _properParts.Setup(properParameter);
            
            var statusRank = SingleModeScenarioTeamRaceUtils.GetTeamMemberStatusRank(statusParameter);
            _statusRankImage.sprite = AtlasSpritePath.SingleModeScenarioTeamRace.GetStatusRankSprite(statusRank);
            _aoharuSoul.SetActiveWithCheck(showAoharuSoul);
            if(showAoharuSoul)
            {
                _aoharuSoul.sprite = SingleModeScenarioTeamRaceUtils.GetAoharuSoulIconSprite(member);
            }

            // ゲスト
            _guestRoot.SetActive(isGuest);
            if (isGuest)
            {
                foreach (var obj in _guestHideObjectArray)
                {
                    obj.SetActive(false);
                }
                // ↑で不要なイベントタブとエピソードタブ非表示
                // フリックできるのはスキルタブまでに設定する
                baseComponent.SetMaxFlickIndex(DialogSupportCardDetail.ToggleIndex.Skill);
                
                var masterSupportCard = MasterDataManager.Instance.masterSupportCardData.Get(member.SupportCardId);
                _guestTitleName.text = masterSupportCard.Titlename;
                _guestCharaName.text = masterSupportCard.Charaname;
            }
        }

        //ゲストのスキルを表示するスクロールビュー関連の設定
        private void SetupGuestSkillScrollView(WorkSupportCardData.SupportCardData workSupportCard)
        {
            _guestSkillScrollViewHeader.SetActiveWithCheck(true);

            _guestSkillScrollView.SetActiveWithCheck(true);

            // スキル
            var masterSkillData = workSupportCard.GetMasterSupportCard();
            var hintGain = MasterDataManager.Instance.masterSingleModeHintGain.GetListWithHintIdOrderByIdAsc(masterSkillData.SkillSetId);
            List<PartsSingleModeSkillListItem.Info> infoList = new List<PartsSingleModeSkillListItem.Info>();
            foreach (var hint in hintGain)
            {
                if (hint.HintGainType == Decimal.Zero && hint.SupportCardId == workSupportCard.SupportCardId)
                {
                    var info = new PartsSingleModeSkillListItem.Info(hint.HintValue1);
                    info.UseSkillListButton = true;
                    info.OverrideSkillIconSize = true;
                    info.SkillIconSize = SKILLICON_SIZE_S;
                    info.IsDrawDesc = false;
                    info.IsDrawNeedSkillPoint = true;
                    infoList.Add(info);
                }
            }

            //ループスクロールにセット
            int count = Mathf.CeilToInt((float)infoList.Count / 2);
            _guestSkillListLoopScroll.Setup(count, (item) =>
            {
                if (item is PartsSingleModeSkillListItemContainer skillListItemContainer)
                {
                    skillListItemContainer.UpdateItem(infoList);
                }
            });

            _guestNoSkillLabel.SetActiveWithCheck(infoList.Count == 0);
            _guestSkillScrollViewPort.SetActiveWithCheck(infoList.Count > 0);
        }
    }
}
