using DG.Tweening;
using UnityEngine;

namespace Gallop
{
    using static StaticVariableDefine.SingleMode.SingleModeScenarioTeamRaceDefine;
    
    /// <summary>
    /// チーム情報を表示するヘッター部分
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioTeamRaceInfoHeader : MonoBehaviour
    {
        /// <summary> 下地 </summary>
        [field: SerializeField, RenameField]
        public ImageCommon RootImage { get; private set; } = null;
        
        /// <summary> 内容物アニメーション元 </summary>
        [field: SerializeField, RenameField]
        public CanvasGroup InnerAnimationRoot { get; private set; } = null;
        
        /// <summary>チーム名 </summary>
        [field: SerializeField, RenameField]
        public TextCommon TeamNameText { get; private set; } = null;

        /// <summary>名誉アイコン </summary>
        [field: SerializeField, RenameField]
        public ImageCommon HonorIcon { get; private set; } = null;

        /// <summary>名誉テキスト </summary>
        [field: SerializeField, RenameField]
        public TextCommon HonorText { get; private set; } = null;

        [field: SerializeField, RenameField]
        public OutlineCommon HonorTextOutline { get; private set; } = null;

        /// <summary>ランクアイコン </summary>
        [field: SerializeField, RenameField]
        public ImageCommon RankIcon { get; private set; } = null;

        /// <summary>infoボタン </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon TitleHeaderInfoButton { get; private set; } = null;

        /// <summary>順位</summary>
        [field: SerializeField, RenameField]
        public BitmapTextCommon TeamRankText { get; private set; } = null;

        /// <summary>位 </summary>
        [field: SerializeField, RenameField]
        public GameObject RankText { get; private set; } = null;

        [SerializeField]
        private ImageCommon _aoharuResultImage = null;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            DialogSingleModeScenarioTeamRaceTeamInfo.RegisterDownload(register);
        }
        
        public void SetUpHeader(bool lockGoToDeckButton = false, bool hideGoToDeckButton = false, bool hideTeamRankingBonus = false)
        {
            var workData = WorkDataManager.Instance.SingleMode.TeamRace;
            var teamName = workData.TeamName;
            var teamHonorName = workData.TeamHonorName;
            var teamRanking = workData.TeamRanking;
            var teamTotalPower = workData.TeamTotalPower;

            TeamNameText.text = teamName;

            HonorText.text = teamHonorName;

            var honorOutlineIndex = Mathf.Clamp(workData.TeamHonorId - 1, 0, HONOR_OUTLINE_COLOR_ALLAY.Length);
            HonorTextOutline.SetMulColor(HONOR_OUTLINE_COLOR_ALLAY[honorOutlineIndex]);

            HonorIcon.sprite = AtlasSpritePath.SingleModeScenarioTeamRace.GetTeamHonorBaseSprite(workData.TeamHonorId);

            TeamRankText.text = teamRanking.ToString();

            RankIcon.sprite = AtlasSpritePath.SingleModeScenarioTeamRace.GetTeamTotalPowerIconSprite(teamTotalPower);

            TitleHeaderInfoButton.SetOnClick(() => {
                DialogSingleModeScenarioTeamRaceTeamInfo.PushDialog(lockGoToDeckButton, hideGoToDeckButton, hideTeamRankingBonus);
            });

            //チーム情報ダイアログと同様に未参加/優勝/準優勝の出しわけ
            _aoharuResultImage.SetActiveWithCheck(false);
            var resultSprite = WorkDataManager.Instance.SingleMode.TeamRace.GetAoharuResultImage();
            if (resultSprite != null)
            {
                TeamRankText.SetActiveWithCheck(false);
                RankText.SetActiveWithCheck(false);
                _aoharuResultImage.SetActiveWithCheck(true);
                _aoharuResultImage.sprite = resultSprite;
                _aoharuResultImage.SetNativeSize();
                //優勝・準優勝などの画像のサイズを調整
                const float IMAGE_OFFSET_RATE = 0.75f;
                var spriteSize = new Vector2(_aoharuResultImage.sprite.rect.width, _aoharuResultImage.sprite.rect.height);
                spriteSize.x *= IMAGE_OFFSET_RATE;
                spriteSize.y *= IMAGE_OFFSET_RATE;
                _aoharuResultImage.rectTransform.sizeDelta = spriteSize;
            }
        }

        /// <summary>
        /// IN再生
        /// </summary>
        public void PlayIn(float baseDelay = GameDefine.BASE_FPS_TIME)
        {
            // https://xxxxxxxxxx/pages/viewpage.action?pageId=223757627

            RootImage.color = GameDefine.COLOR_CLEAR_WHITE;
            InnerAnimationRoot.alpha = 0;
            // 0.0333秒 : 吹き出しIN
            TweenAnimationBuilder.CreateSequence(RootImage.gameObject, TweenAnimation.PresetType.BalloonInScaleUpFade).SetDelay(baseDelay);
            // 0.1秒 : 内容物IN
            TweenAnimationBuilder.CreateSequence(InnerAnimationRoot.gameObject, TweenAnimation.PresetType.PartsInFade).SetDelay(baseDelay * 3);
        }
        
        /// <summary>
        /// IN再生：理事長代理戦用
        /// </summary>
        public void PlayInBossRaceOpponentSelect(float delay)
        {
            TweenAnimationBuilder.CreateSequence(RootImage.gameObject, TweenAnimation.PresetType.PartsInFade).SetDelay(delay);
            TweenAnimationBuilder.CreateSequence(InnerAnimationRoot.gameObject, TweenAnimation.PresetType.PartsInFade).SetDelay(delay);
        }

        /// <summary>
        /// INの前段階（アルファ０）
        /// </summary>
        public void PlayInPrepare()
        {
            RootImage.color = GameDefine.COLOR_CLEAR_WHITE;
            InnerAnimationRoot.alpha = 0;
        }

        
        /// <summary>
        /// OUT再生
        /// </summary>
        public void PlayOut()
        {
            TweenAnimationBuilder.CreateSequence(RootImage.gameObject, TweenAnimation.PresetType.PartsOutMoveAndFade);
            TweenAnimationBuilder.CreateSequence(InnerAnimationRoot.gameObject, TweenAnimation.PresetType.PartsOutMoveAndFade);
        }
        
        /// <summary>
        /// OUT再生：理事長代理戦用
        /// </summary>
        public void PlayOutBossRaceOpponentSelect()
        {
            TweenAnimationBuilder.CreateSequence(RootImage.gameObject, TweenAnimation.PresetType.PartsOutFade);
            TweenAnimationBuilder.CreateSequence(InnerAnimationRoot.gameObject, TweenAnimation.PresetType.PartsOutFade);
        }
    }
}
