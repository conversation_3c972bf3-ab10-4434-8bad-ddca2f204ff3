using System.Linq;
using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// チーム対抗戦:チーム情報表示 チーム名/称号/順位/総合力
    /// </summary>
    public sealed class PartsSingleModeScenarioTeamRaceTeamInfo : MonoBehaviour
    {
        [SerializeField]
        private TextCommon _teamName = null;

        [SerializeField]
        private ImageCommon _teampowerImage;

        [SerializeField]
        private ImageCommon _honorBaseImage = null;
        
        [SerializeField]
        private TextCommon _honorText = null;

        [SerializeField]
        private OutlineCommon _honorOutline = null;

        [SerializeField]
        private BitmapTextCommon _rankText = null;

        [SerializeField]
        private GameObject _rankLabel = null; //「位」

        [SerializeField]
        private ImageCommon _aoharuResultImage = null;


        public void Setup()
        {
            var workTeamRace = WorkDataManager.Instance.SingleMode.TeamRace;

            //チーム名
            _teamName.text = workTeamRace.TeamName;
            //称号
            PartsSingleModeScenarioTeamRaceMainView.SetHonorId(workTeamRace.TeamHonorId, _honorText, _honorOutline, _honorBaseImage);
            //順位
            _rankText.text = workTeamRace.TeamRanking.ToString();
            //総合力
            _teampowerImage.sprite = AtlasSpritePath.SingleModeScenarioTeamRace.GetTeamTotalPowerIconSprite(workTeamRace.TeamTotalPower);


            //チーム情報ダイアログと同様に未参加/優勝/準優勝の出しわけ
            _aoharuResultImage.SetActiveWithCheck(false);
            var resultSprite = workTeamRace.GetAoharuResultImage();
            if(resultSprite != null)
            {
                _rankText.SetActiveWithCheck(false);
                _rankLabel.SetActiveWithCheck(false);
                _aoharuResultImage.SetActiveWithCheck(true);
                _aoharuResultImage.sprite = resultSprite;
                _aoharuResultImage.SetNativeSize();
                //優勝・準優勝などの画像のサイズを調整
                const float IMAGE_OFFSET_RATE = 0.75f;
                var spriteSize = new Vector2(_aoharuResultImage.sprite.rect.width, _aoharuResultImage.sprite.rect.height);
                spriteSize.x *= IMAGE_OFFSET_RATE;
                spriteSize.y *= IMAGE_OFFSET_RATE;
                _aoharuResultImage.rectTransform.sizeDelta = spriteSize;
            }
        }
    }
}