using CodeStage.AntiCheat.ObscuredTypes;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    using static WorkSingleModeScenarioTeamRace;

    /// <summary>
    /// チーム対抗戦: デッキ情報
    /// </summary>
    public class SingleModeScenarioTeamRaceDeckInfo
    {
        // レース１距離種別あたりのMAXチーム人数
        private const int MAX_MEMBER_NUM_PER_DISTANCE = 3;

        private readonly List<MemberInfo> _memberList = new List<MemberInfo>();

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public SingleModeScenarioTeamRaceDeckInfo()
        {
            Initialize();
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Initialize()
        {
            _memberList.Clear();
            for (int race = 0; race < TeamStadiumDefine.MAX_RACE_NUMBER; race++)
            {
                for (int member = 0; member < MAX_MEMBER_NUM_PER_DISTANCE; member++)
                {
                    _memberList.Add(new MemberInfo(race + 1, member + 1));
                }
            }
        }

        /// <summary>
        /// 複製する
        /// </summary>
        /// <returns></returns>
        public SingleModeScenarioTeamRaceDeckInfo Clone()
        {
            var newInfo = new SingleModeScenarioTeamRaceDeckInfo();
            newInfo._memberList.Clear();
            newInfo._memberList.AddRange(_memberList.Select(x => x.Clone()));
            return newInfo;
        }

        /// <summary>
        /// データをコピーする
        /// </summary>
        /// <param name="src"></param>
        public void Copy(SingleModeScenarioTeamRaceDeckInfo src)
        {
            for (int i = 0, n = _memberList.Count; i < n; i++)
            {
                _memberList[i].Copy(src._memberList[i]);
            }
        }

        /// <summary>
        /// 編成メンバーリストを取得
        /// </summary>
        /// <returns></returns>
        public List<MemberInfo> GetMemberList()
        {
            return _memberList;
        }

        /// <summary>
        /// 解放済みメンバーリストを取得
        /// </summary>
        /// <returns></returns>
        public List<MemberInfo> GetUnlockMemberList()
        {
            return _memberList.Where(x => !x.IsLock).ToList();
        }

        /// <summary>
        /// 指定したレースタイプの編成メンバーリストを取得
        /// </summary>
        /// <param name="raceNum"></param>
        /// <returns></returns>
        public List<MemberInfo> GetMemberList(int raceNum)
        {
            return _memberList.Where(member => member.DistanceType == raceNum).ToList();
        }

        /// <summary>
        ///  編成メンバーを取得
        /// </summary>
        /// <param name="raceNum"></param>
        /// <param name="memberId"></param>
        /// <returns></returns>
        public MemberInfo GetMember(int raceNum, int memberId)
        {
            return _memberList.Find(x => x.DistanceType.GetDecrypted() == raceNum && x.MemberId == memberId);
        }

        public void UpdateMemberData(MemberInfo updateData)
        {
            var baseData = _memberList.Find(x => x.DistanceType == updateData.DistanceType && x.MemberId == updateData.MemberId);
            
            if(baseData.IsEmpty == false)
            {
                if (baseData.CharaId == updateData.CharaId)
                {
                    //配置枠変更していない
                    baseData.SetData(updateData.TeamMember, updateData.RunningStyle);
                }
                else if (baseData.CharaId != updateData.CharaId)
                {
                    //既存の配置キャラと差し替え
                    var changeTmpData = _memberList.Find(x => x.CharaId == updateData.CharaId);
                    if(changeTmpData != null)
                    {
                        changeTmpData.SetData(baseData.TeamMember, baseData.RunningStyle);
                    }
                    baseData.SetData(updateData.TeamMember, updateData.RunningStyle);
                }
            }
            else
            {
                //配置先が空枠の場合、一旦現在のキャラが任意の枠に配置済みの場合外しておく
                var clearData = _memberList.Find(x => x.CharaId == updateData.CharaId);
                if (clearData != null)
                {
                    clearData.Clear();
                }

                baseData.SetData(updateData.TeamMember, updateData.RunningStyle);
            }
        }

        /// <summary>
        /// チーム編成デッキデータ
        /// </summary>
        public class MemberInfo
        {
            private TeamMember _teamMember;

            public TeamMember TeamMember 
            {
                get { return _teamMember; }
                set
                {
                    _teamMember = value;
                }
            }

            /// <summary> 距離区分（1:短距離/2:マイル/3:中距離/4:長距離/5:ダート）</summary>
            public ObscuredInt DistanceType { get; }
            /// <summary> 距離区分に出走するメンバーのインデックス</summary>
            public ObscuredInt MemberId { get; }
            /// <summary> 対象距離区分に出走する自キャラID</summary>
            public int CharaId 
            {
                get
                {
                    return (_teamMember != null) ? _teamMember.CharaId.GetDecrypted() : 0;
                }
            }

            /// <summary> 出走時のウマ娘の走法</summary>
            private RaceDefine.RunningStyle _runningStyle = RaceDefine.RunningStyle.None;

            /// <summary>
            /// 走法
            /// </summary>
            public RaceDefine.RunningStyle RunningStyle
            {
                get { return _runningStyle; }
                set
                {
                    _runningStyle = value;
                }
            }

            /// <summary>
            /// エースか？
            /// </summary>
            public bool IsAce => !IsEmpty && MemberId == 1;

            /// <summary>
            /// メンバー未設定か？
            /// </summary>
            public bool IsEmpty => TeamMember == null;

            /// <summary>
            /// 未開放か？
            /// </summary>
            public bool IsLock => TeamStadiumUtil.GetRaceMemberNum() < MemberId;

            public MemberInfo(int distanceType, int memberId)
            {
                DistanceType = distanceType;
                MemberId = memberId;
            }

            /// <summary>
            /// 評価点を取得
            /// </summary>
            /// <returns></returns>
            public int GetScore()
            {
                if (!IsEmpty)
                {
                    return SingleModeScenarioTeamRaceUtils.GetRankScore(this);
                }
                return 0;
            }

            /// <summary>
            /// 未設定状態にする
            /// </summary>
            /// <param name="notifyChanged"></param>
            public void Clear()
            {
                _teamMember = null;
            }

            public void SetData(TeamMember teamMember, RaceDefine.RunningStyle runningStyle)
            {
                _teamMember = teamMember;
                _runningStyle = runningStyle;
            }

            /// <summary>
            /// 複製する
            /// </summary>
            /// <returns></returns>
            public MemberInfo Clone()
            {
                var newInfo = new MemberInfo(DistanceType, MemberId);
                newInfo._teamMember = TeamMember;
                newInfo._runningStyle = RunningStyle;
                return newInfo;
            }

            /// <summary>
            /// データをコピーする
            /// </summary>
            /// <param name="src"></param>
            public void Copy(MemberInfo src)
            {
                _teamMember = src._teamMember;
                _runningStyle = src._runningStyle;
            }
        }
    }
}
