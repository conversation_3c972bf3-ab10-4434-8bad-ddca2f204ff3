using AnimateToUnity.Utility;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 購買部 アイテム効果予測パーツ タイトル
    /// </summary>
    public class PartsSingleModeScenarioFreeShopUseItemConfirmTitle : MonoBehaviour
    {
        [SerializeField]
        TextCommon _titleText = null;

        public static PartsSingleModeScenarioFreeShopUseItemConfirmTitle Create(string title, Transform parent, ResourceManager.ResourceHash resourceHash = ResourceManager.ResourceHash.Common)
        {
            var load = ResourceManager.LoadOnHash<GameObject>(ResourcePath.PARTS_SINGLE_MODE_SCENARIO_FREE_SHOP_USE_ITEM_CONFIRM_TITLE, resourceHash);
            GameObject obj = GameObject.Instantiate(load, parent);
            var component = obj.GetComponent<PartsSingleModeScenarioFreeShopUseItemConfirmTitle>();

            component.Setup(title);

            return component;
        }

        private void Setup(string title)
        {
            _titleText.text = title;
        }

    }
}