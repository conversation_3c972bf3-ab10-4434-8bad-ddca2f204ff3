using AnimateToUnity.Utility;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{

    /// <summary>
    /// 購買部 アイテム効果予測パーツ
    /// </summary>
    public class PartsSingleModeScenarioFreeShopUseItemConfirmText : PartsSingleModeScenarioFreeShopUseItemConfirmBase
    {
        [SerializeField]
        private TextCommon _text = null;


        public static PartsSingleModeScenarioFreeShopUseItemConfirmText Create(string text, Transform parent, ResourceManager.ResourceHash resourceHash = ResourceManager.ResourceHash.Common)
        {
            var load = ResourceManager.LoadOnHash<GameObject>(ResourcePath.PARTS_SINGLE_MODE_SCENARIO_FREE_SHOP_USE_ITEM_CONFIRM_TEXT, resourceHash);
            GameObject obj = GameObject.Instantiate(load, parent);
            var component = obj.GetComponent<PartsSingleModeScenarioFreeShopUseItemConfirmText>();

            component.Setup(text);

            return component;
        }

        private void Setup(string text)
        {
            _text.text = text;
        }

    }
}