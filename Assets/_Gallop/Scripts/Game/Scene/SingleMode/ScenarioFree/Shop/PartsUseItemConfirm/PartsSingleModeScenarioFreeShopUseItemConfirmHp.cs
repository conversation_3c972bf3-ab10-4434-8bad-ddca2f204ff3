using AnimateToUnity.Utility;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    public class PartsSingleModeScenarioFreeShopUseItemConfirmHpModel
    {
        private List<MasterSingleModeFreeShopEffect.SingleModeFreeShopEffect> _masterSingleModeFreeShopEffectHpList = null;
        private List<MasterSingleModeFreeShopEffect.SingleModeFreeShopEffect> _masterSingleModeFreeShopEffectMaxHpList = null;

        public PartsSingleModeScenarioFreeShopUseItemConfirmHpModel(List<MasterSingleModeFreeShopEffect.SingleModeFreeShopEffect> masterSingleModeFreeShopEffectHpList, List<MasterSingleModeFreeShopEffect.SingleModeFreeShopEffect> masterSingleModeFreeShopEffectMaxHpList)
        {
            _masterSingleModeFreeShopEffectHpList = masterSingleModeFreeShopEffectHpList;
            _masterSingleModeFreeShopEffectMaxHpList = masterSingleModeFreeShopEffectMaxHpList;
        }

        public int CalcPreviewHp(int previewMaxHp, int useNum = 1)
        {
            var workSingleModeCharacter = WorkDataManager.Instance.SingleMode.Character;
            var previewHp = workSingleModeCharacter.Hp;

            if (_masterSingleModeFreeShopEffectHpList.Any())
            {
                _masterSingleModeFreeShopEffectHpList.ForEach(masterSingleModeFreeShopEffect =>
                {
                    previewHp += masterSingleModeFreeShopEffect.EffectValue2 * useNum;
                });
            }

            // 最大値補正
            if (previewHp > previewMaxHp)
            {
                previewHp = previewMaxHp;
            }

            // 最小値補正
            if (previewHp < 0)
            {
                previewHp = 0;
            }

            return previewHp;
        }

        public int CalcPreviewMaxHp(int useNum = 1)
        {
            var workSingleModeCharacter = WorkDataManager.Instance.SingleMode.Character;
            var previewMaxHp = workSingleModeCharacter.MaxHp;

            if (_masterSingleModeFreeShopEffectMaxHpList.Any())
            {
                _masterSingleModeFreeShopEffectMaxHpList.ForEach(masterSingleModeFreeShopEffect =>
                {
                    previewMaxHp += masterSingleModeFreeShopEffect.EffectValue2 * useNum;
                });
            }

            // 最大値補正
            if (previewMaxHp > SingleModeDefine.VITAL_VALUE_MAX)
            {
                previewMaxHp = SingleModeDefine.VITAL_VALUE_MAX;
            }

            return previewMaxHp;
        }

    }


    /// <summary>
    /// 購買部 アイテム効果予測パーツ HP
    /// </summary>
    public class PartsSingleModeScenarioFreeShopUseItemConfirmHp : PartsSingleModeScenarioFreeShopUseItemConfirmBase
    {
        private const int HP_GAUGE_ADD_RANGE = 95;

        [SerializeField]
        private RectTransform _hpGaugeFrame = null;

        [SerializeField]
        private GaugeCommon _hpGauge = null;

        [SerializeField]
        private GaugeCommon _hpGaugePlus = null;

        [SerializeField]
        private TextCommon _hpText = null;

        [SerializeField]
        private TextCommon _maxHpText = null;


        private PartsSingleModeScenarioFreeShopUseItemConfirmHpModel _model = null;


        public static PartsSingleModeScenarioFreeShopUseItemConfirmHp Create(PartsSingleModeScenarioFreeShopUseItemConfirmHpModel model, Transform parent, ResourceManager.ResourceHash resourceHash = ResourceManager.ResourceHash.Common)
        {
            var load = ResourceManager.LoadOnHash<GameObject>(ResourcePath.PARTS_SINGLE_MODE_SCENARIO_FREE_SHOP_USE_ITEM_CONFIRM_HP, resourceHash);
            GameObject obj = GameObject.Instantiate(load, parent);
            var component = obj.GetComponent<PartsSingleModeScenarioFreeShopUseItemConfirmHp>();

            component.Setup(model, resourceHash);

            return component;
        }

        private void Setup(PartsSingleModeScenarioFreeShopUseItemConfirmHpModel model, ResourceManager.ResourceHash resourceHash = ResourceManager.ResourceHash.Common)
        {
            _model = model;

            // ゲージ設定
            SetupHpGauge();
        }

        private void SetupHpGauge(int useNum = 1)
        {
            var previewMaxHp = _model.CalcPreviewMaxHp(useNum);
            var previewHp = _model.CalcPreviewHp(previewMaxHp, useNum);
            var currentHp = WorkDataManager.Instance.SingleMode.Character.Hp;

            SetupHpGaugeSize(previewMaxHp);

            _hpGauge.Set(currentHp, previewMaxHp);
            _hpGaugePlus.Set(previewHp, previewMaxHp);

            SetupHpText(previewHp, previewMaxHp);
        }

        private void SetupHpGaugeSize(int maxHp)
        {
            // HPゲージを最大値に合わせて伸ばす
            var addMaxHp = (float)(maxHp - SingleModeDefine.VITAL_DEFAULT_MAX);
            var hpGaugeAdd = HP_GAUGE_ADD_RANGE * (addMaxHp / (SingleModeDefine.VITAL_VALUE_MAX - SingleModeDefine.VITAL_DEFAULT_MAX));
            var width = _hpGaugeFrame.sizeDelta.x + hpGaugeAdd;
            _hpGaugeFrame.sizeDelta = new Vector2(width, _hpGaugeFrame.sizeDelta.y);
        }

        private void SetupHpText(int previewHp, int previewMaxHp)
        {
            var workSingleModeCharacter = WorkDataManager.Instance.SingleMode.Character;

            var previewHpText = previewHp.ToString();
            if (previewHp != workSingleModeCharacter.Hp)
            {
                previewHpText = TextUtil.AddColorFormatCode(FontColorType.Plus, previewHp.ToString());
            }
            _hpText.text = previewHpText;


            var previewMaxHpText = previewMaxHp.ToString();
            if (previewMaxHp != workSingleModeCharacter.MaxHp)
            {
                previewMaxHpText = TextUtil.AddColorFormatCode(FontColorType.Plus, previewMaxHp.ToString());
            }
            _maxHpText.text = previewMaxHpText;
        }

    }
}