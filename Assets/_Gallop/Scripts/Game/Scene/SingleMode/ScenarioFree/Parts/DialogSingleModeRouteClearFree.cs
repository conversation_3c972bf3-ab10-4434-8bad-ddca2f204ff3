using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using DG.Tweening;
using Gallop.Model.Component;

namespace Gallop
{
    public sealed class DialogSingleModeRouteClearFree : DialogSingleModeRouteClear
    {
        public static void OpenFree(int sortId ,System.Action onClose)
        {
            // 目標リスト
            var masterRouteRace = WorkDataManager.Instance.SingleMode.GetTargetRaceEnumerable(true)
                .FirstOrDefault(routeRace => routeRace.SortId == sortId);

            if(masterRouteRace == null)
            {
                Debug.LogError("sortIndex不正, sortIndex =" + sortId);
                onClose();
                return;
            }

            // 必須目標でなければ表示不要
            if ((SingleModeDefine.RouteRaceTargetType) masterRouteRace.TargetType != 
                SingleModeDefine.RouteRaceTargetType.Required)
            {
                onClose();
                return;
            }

            //ダイアログの表示
            var copy = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_SINGLE_MODE_ROUTE_CLEAR_FREE));
            var component = copy.GetComponent<DialogSingleModeRouteClearFree>();
            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = copy;
            dialogData.AutoClose = false;
            dialogData.HideBGImage = true;
            dialogData.DisableAnimation = true;
            dialogData.OpenSe = AudioId.SFX_UI_WINDOW_02;
            dialogData.CancelSe = AudioId.INVALID;
            dialogData.OnPushBackKey += () =>
            {
                if (component._button.IsActive())
                {
                    component._button.onClick.Invoke();
                }
                return true;
            };
            
            var dialog = DialogManager.PushDialog(dialogData);
            component.Initialize(dialog,dialogData, sortId, masterRouteRace);
            component._closeDialog = onClose;
        }

        public override void TriggerExpand()
        {
            _animatorInstance.Play("expand05");
        }
    }
}