using AnimateToUnity;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// トレーニングコマンドボタンA2U：フリー編
    /// </summary>
    public class SingleModeMainViewTrainingFooterItemA2UScenarioFree : SingleModeMainViewTrainingFooterItemA2UBase
    {
        private FlashPlayer _guideBadgeFlashPlayer;
        private AnMotion _guideBadgeAnMotionItemBonus;
        
                
        /// <summary> DL登録：シナリオ拡張用 </summary>
        protected override void RegisterDownloadScenario(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_FREE_BADGE_ITEM_BONUS_00);
        }
        
        /// <summary>
        /// バッジ表示
        /// </summary>
        protected override void SetupTrainingButtonBadgeScenario(WorkSingleModeData.TurnInfo turnInfo)
        {
            if (_guideBadgeFlashPlayer == null)
            {
                CreateBadgeFlash(out _guideBadgeFlashPlayer, ResourcePath.SINGLE_MODE_FREE_BADGE_ITEM_BONUS_00);
                _guideBadgeAnMotionItemBonus = _guideBadgeFlashPlayer.GetMotion("MOT_mc_badge_itembonus00");
            }

            var isTips = turnInfo.IsExistTips();
            var isExistItemEffect = turnInfo.IsExistItemEffect();

            // ヒントor指導でON
            PlayMotionBadgeKnackRoot(isTips || isExistItemEffect);

            // ヒントバッジ
            bool isTipsBadgeActive = (isTips && isExistItemEffect == false); // 指導＞ヒント
            _tipsBadgeFlashPlayer.Play(isTipsBadgeActive ? GameDefine.A2U_IN_LABEL : GameDefine.A2U_IN00_LABEL);
            // アイテムボーナスバッジ
            if (_guideBadgeFlashPlayer != null)
            {
                _guideBadgeFlashPlayer.Play(isExistItemEffect ? GameDefine.A2U_IN_LABEL : GameDefine.A2U_IN00_LABEL);
                var bonusRate = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree.GetTrainingEffectUpBonusRate((int)turnInfo.CommandId);
                if (bonusRate != 0)
                {
                    var effectValue = bonusRate + 100;
                    //表示分けのIDを取得 1~3 をラベル用に 0~2 に変換する
                    var id = MasterDataManager.Instance.masterSingleModeFreeTrainingPlate.GetIdFromValue(effectValue) - 1;
                    _guideBadgeAnMotionItemBonus.SetMotionPlay(TextUtil.Format("base{0:D2}", id));
                }
            }
        }
        
        /// <summary>
        /// 失敗率の表示：シナリオ拡張用
        /// </summary>
        protected override void SetupTrainingFailureRateTextScenario(int failureRate)
        {
            var workScenarioFree = WorkDataManager.Instance.SingleMode.Character.WorkScenarioFree;

            var failureRateText = TextId.SingleMode0037.Format(failureRate);
            if (workScenarioFree.SingleModeFreeItemEffectArray != null &&
                workScenarioFree.SingleModeFreeItemEffectArray.Any(singleModeFreeItemEffect => (SingleModeScenarioFreeDefine.SingleModeScenarioFreeShopEffectType)singleModeFreeItemEffect.effect_type == SingleModeScenarioFreeDefine.SingleModeScenarioFreeShopEffectType.TrainingFailureRate))
            {
                // 失敗率0％アイテムが使われていればフォントカラーを変更
                failureRateText = TextUtil.AddColorFormatCode(FontColorType.SingleModeScenarioFreeShopItemTrainingFailureRate, failureRateText);
            }

            // 失敗率（％）
            _anTextNumRateFail?.SetTextIfChanged(failureRateText);
        }
    }
}
