using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// メカ編 : シナリオレース3D挙動Model
    /// </summary>
    public class SingleModeScenarioMechaRaceTop3DModel : SingleModeScenarioContentsTop3DModel
    {
        /// <summary> 最終UGEのメカウマ娘モーションセット </summary>
        private const int LAST_MECHA_MOTION_SET_ID = 2008801;
        /// <summary> 最終UGE版か </summary>
        private readonly bool _isLast;

        public SingleModeScenarioMechaRaceTop3DModel(bool isLast)
        {
            _isLast = isLast;
        }
        public override string ImageEffectPath => _isLast ?
            ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RACE_TOP_LAST_IMAGE_EFFECT_DATA :
            ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RACE_TOP_NORMAL_IMAGE_EFFECT_DATA;
        private string CharaParamPath => _isLast ? 
            ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RACE_LAST_CHARA_PARAM : 
            ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RACE_NORMAL_CHARA_PARAM;

        /// <summary> 画質設定簡易版でキャラ数制限を行うか </summary>
        protected override bool UseGraphicSettingsGameQuality => false;
        
        public void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(CharaParamPath);
            register.RegisterPathWithoutInfo(ImageEffectPath);
        }

        public void Setup()
        {
            var charaBuildInfos = new List<SingleModeScenarioContentsCharaInfo>();
            if (!_isLast)
            {
                // 最終以外は育成キャラを出す
                charaBuildInfos.Add(SingleModeScenarioContentsCharaInfo.CreateAtMain());
            }
            var mechaCharDressSet = SingleModeScenarioMechaUtils.GetMechaCharaDressIdSet();
            var mechaCharaBuildInfo = SingleModeScenarioContentsCharaInfo.Create(mechaCharDressSet.CharaId, true);
            mechaCharaBuildInfo.DressId = mechaCharDressSet.DressId;
            charaBuildInfos.Add(mechaCharaBuildInfo);
            base.Setup(LoadCharaParam(), charaBuildInfos);

            var index = charaBuildInfos.FindIndex(x => x.CharaId == mechaCharDressSet.CharaId);
            SetupMechaMotionSet(index);
        }

        /// <summary>
        /// メカウマ娘のモーションセット設定
        /// </summary>
        private void SetupMechaMotionSet(int index)
        {
            if (!_isLast) return;//通常UGEではデフォルト設定
            
            // 最終UGEでは固有モーション
            CharaMotionSetMasters[index] = MasterDataManager.Instance.masterCharaMotionSet.Get(LAST_MECHA_MOTION_SET_ID);
        }

        public override SingleModeScenarioContentsTop3DCharaParam LoadCharaParam() =>
            SingleModeScenarioMechaRaceTop3DCharaParam.Load(CharaParamPath);
        
        private class SingleModeScenarioMechaRaceTop3DCharaParam : SingleModeScenarioContentsTop3DCharaParam
        {
            public static SingleModeScenarioContentsTop3DCharaParam Load(string path) =>
                Load<SingleModeScenarioContentsTop3DCharaParam>(path);
        }
    }

    /// <summary>
    /// メカ編 : シナリオレース3D挙動Control
    /// </summary>
    public class SingleModeScenarioMechaRaceTop3DController : SingleModeScenarioContentsTop3DController
    {
        /// <summary> 最終UGE版か </summary>
        private readonly bool _isLast;

        public SingleModeScenarioMechaRaceTop3DController(bool isLast)
        {
            _isLast = isLast;
        }
        
        private const int BG_ID = 76;
        private const int BG_SUB_ID = 110;
        private const int LAST_BG_ID = 284;
        private const int LAST_BG_SUB_ID = 0;
        private string BgPath => ResourcePath.GetVerticalBackgroundPath(BgId, BgSubId);

        public int BgId => _isLast ? LAST_BG_ID : BG_ID;
        public int BgSubId => _isLast ? LAST_BG_SUB_ID : BG_SUB_ID;
        
        public void Setup(SingleModeScenarioContentsTop3DView view, SingleModeScenarioMechaRaceTop3DModel model)
        {
            base.Setup(view, model);
            SetupBG(BgPath, true);
        }

        protected override void RegisterDownloadCore(DownloadPathRegister register)
        {
            var model = new SingleModeScenarioMechaRaceTop3DModel(_isLast);
            model.RegisterDownload(register);
            register.RegisterPathWithoutInfo(BgPath);
        }

        protected override void DestroyCore() { }
    }
}
