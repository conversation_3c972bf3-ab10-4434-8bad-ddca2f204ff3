using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    public class PartsSingleModeScenarioMechaImageNumberModel : PartsSingleModeImageNumberModel
    {
        /// <summary> シナリオギミックによる追加の加算表示を行うか </summary>
        public override bool EnableBonus => true;
        /// <summary> シナリオギミックによる追加の加算表示A2Uパス </summary>
        public override string BonusA2UPath => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_NUM_EXTRA_PARAM_00;
        /// <summary> シナリオギミックによる追加の加算表示A2UのプレートID </summary>
        public override int BonusPlateId(int value, int commandId) => SingleModeUtils.GetTrainingBonusPlateId(value);
        /// <summary> シナリオギミックによる追加の加算表示A2Uプレートモーションオブジェクト名 </summary>
        public override string BonusPlateMotionObjectName => PartsSingleModeImageNumber.MOT_NUM_EXTRA00;
        
        private GameObject _evaluateODEffect = null;
        
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            base.RegisterDownload(register);
            
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_EXTRAPARAM_OVERDRIVE_EFFECT_PATH);
        }
        
        /// <summary>
        /// <see cref="PartsSingleModeImageNumber.SetBonusValue"/>の追加処理
        /// </summary>
        public override void OnSetBonusValueExtend(FlashPlayer flashPlayer)
        {
            base.OnSetBonusValueExtend(flashPlayer);
            
            // オーバードライブ効果表示のエフェクト処理
            SetOverdriveEffect(flashPlayer);
        }

        /// <summary>
        /// オーバードライブ効果表示のエフェクト処理
        /// </summary>
        private void SetOverdriveEffect(FlashPlayer flashPlayer)
        {
            var overdriveEffect = flashPlayer.GetMotion(TextUtil.Format("MOT_effectiveness_{0:D2}", BonusPlateId(BonusValue, (int)SelectTrainingCommandId)));
            // オーバードライブ発動中かどうか
            var isUsingOverdrive = WorkDataManager.Instance.SingleMode.Character.ScenarioMecha.IsUsingOverdrive();
            // 併せウマ娘1人につきトレーニング効果上昇または合計研究Lvごとにトレーニング時の各パラメータ上昇のオーバードライブ効果を持っているか
            var hasParamUpOverDriveEffect = SingleModeScenarioMechaBoardModel.IsContainsPartnerParameterUpOverdriveEffect()
                || SingleModeScenarioMechaBoardModel.IsContainsParamUpOverdriveEffect(ParameterType);
            var isActiveStanceEffectForTips = HasBonusValue && isUsingOverdrive && hasParamUpOverDriveEffect;
            // 条件を満たしていればエフェクト表示
            overdriveEffect?.SetResetModeType(AnMotion.ResetModeTypes.None);
            overdriveEffect?.SetMotionPlay(isActiveStanceEffectForTips ? GameDefine.A2U_LOOP_LABEL : GameDefine.A2U_IN00_LABEL);
            
            // 上に載せるエフェクトの生成
            if (_evaluateODEffect == null)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_EXTRAPARAM_OVERDRIVE_EFFECT_PATH);
                _evaluateODEffect = GameObject.Instantiate(prefab, flashPlayer.transform);
                // アルファの伝搬
                var canvasRenderers = _evaluateODEffect.GetComponentsInChildren<CanvasRenderer>();
                foreach (var canvasRenderer in canvasRenderers)
                {
                    var receiver = canvasRenderer.gameObject.GetComponent<A2UAlphaReceiver>();
                    receiver?.Set(flashPlayer.Motion, canvasRenderer);
                }
            }
            _evaluateODEffect.SetActiveWithCheck(isActiveStanceEffectForTips);
        }
    }
}