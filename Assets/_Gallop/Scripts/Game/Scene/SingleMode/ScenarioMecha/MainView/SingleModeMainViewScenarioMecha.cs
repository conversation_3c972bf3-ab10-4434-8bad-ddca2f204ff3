using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// SingleModeMain メカ編用処理群
    /// </summary>
    public sealed partial class SingleModeMainViewScenarioMechaController : AbstractSingleModeMainScenarioController<PartsSingleModeScenarioMechaMainView>
    {
        /// <summary>
        /// ターン開始時の通知ダイアログタイプ
        /// </summary>
        private enum AlertDialogType
        {
            Progress, // 研究進捗度
            TargetLvup, // 目標研究レベルアップ
        }
        private PartsSingleModeScenarioMechaMainView _scenarioMechaParts;

        
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // 共通
            SingleModeScenarioMechaUtils.RegisterDownloadCommon(register);
            // A2U
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_REMIND_TURN_00);
            PartsSingleModeScenarioMechaTuningNowLoadingWipe.RegisterDownloadCustom(register);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RESEARCH_LV_UP_PATH);
            // ダイアログ
            PartsSingleModeScenarioMechaMainView.RegisterDownload(register);
            DialogSingleModeScenarioMechaDataList.RegisterDownload(register);
            PartsSingleModeScenarioMechaOverdriveCutIn.RegisterDownload(register);
            PartsSingleModeScenarioMechaOverdriveSequence.RegisterDownload(register);
            DialogSingleModeScenarioMechaOverdriveWarning.RegisterDownload(register);
            DialogSingleModeScenarioMechaOverdrivePowerUp.RegisterDownload(register);
            DialogSingleModeScenarioMechaOverdriveContinue.RegisterDownload(register);
            DialogSingleModeScenarioMechaRivalDetail.RegisterDownload(register);
            PartsSingleModeScenarioMechaOverdriveRecoveryAndProgressUp.RegisterDownload(register);
            // チップリソース
            foreach (var singleMode09Chip in MasterDataManager.Instance.masterSingleMode09Chip.dictionary.Values)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaChipIconPath(singleMode09Chip.Id));
            }

            //トレーニング選択画面の背景カット
            SingleModeScenarioMechaTrainingCutInHelper.RegisterDownloadCuttResources(register);
        }

        /// <summary>
        /// 画面初期設定
        /// </summary>
        protected override void SetupCore()
        {
            //吹き出し差し替え
            _baseView.CharaMessage.SetBalloonSprite(AtlasSpritePath.SingleMode.GetBalloonSprite(false));

             if (!WorkDataManager.Instance.SingleMode.IsEnableUniqueCommand())
            {
                return;
            }

            // UI
            if (ScenarioParts == null)
            {
                _scenarioMechaParts = PartsSingleModeScenarioMechaMainView.Create(_baseView.ContentsRoot);
                ScenarioParts = _scenarioMechaParts;
            }
            ScenarioParts.Setup();
            
            _baseView.StablesPanel.OnClickAddonScenario = OnClickTuning;
        }

        /// <summary>
        /// 共通UIの位置調整を行う
        /// </summary>
        public override void UpdateCommonUIPosition()
        {
            UpdateCommonUIPositionCommon();
        }
        
        /// <summary>
        /// チューニングボタン
        /// </summary>
        private void OnClickTuning()
        {
            SceneManager.Instance.AdditiveView(SceneDefine.ViewId.SingleModeScenarioMechaTuning);
        }
        
        public override void UpdateView()
        {
            _cutIn?.UpdateView();
        }

        public override void LateUpdateView()
        {
            _cutIn?.LateUpdateView();
        }

        #region ターン開始時の通知関連
        
        /// <summary>
        /// ターン開始時に通知を表示する必要がある場合データを返す
        /// </summary>
        public override void GetShowTurnStartNotice(List<SingleModeMainTurnStartNoticeImpl> list)
        {
            // オーバードライブ強化通知
            if (IsNeedShowOverdrivePowerUp())
            {
                list.Add(new SingleModeMainTurnStartNoticeImpl(SingleModeMainTurnStartChecker.Priority.ScenarioHighest, ShowOverdrivePowerUp));
            }
            // スーパーオーバードライブ自動発動
            if (IsNeedShowOverdriveBurstContinue())
            {
                list.Add(new SingleModeMainTurnStartNoticeImpl(SingleModeMainTurnStartChecker.Priority.ScenarioHighest, ShowOverdriveBurstContinue));
            }
            // 次の目標研究レベル表示
            if (IsShowNextTargetLevel())
            {
                // 最初目標レベルまでの数値は隠しておく
                _scenarioMechaParts.SetActiveTargetLevel(false);
                list.Add(new SingleModeMainTurnStartNoticeImpl(SingleModeMainTurnStartChecker.Priority.ScenarioLowerst, ShowNextTargetLevelNotice));
                list.Add(new SingleModeMainTurnStartNoticeImpl(SingleModeMainTurnStartChecker.Priority.ScenarioLowerst, ShowNextTargetLevelSetup));
            }
            else
            {
                // 演出が必要なければ目標レベルまでの数値は常に出しておく
                _scenarioMechaParts.PlayNormalTargetLevel();
            }
            // 研究進捗度変化アニメ
            if (_scenarioMechaParts != null)
            {
                if (IsNeedPlayProgressGaugeSuperSuccess())
                {
                    list.Add(new SingleModeMainTurnStartNoticeImpl(SingleModeMainTurnStartChecker.Priority.ScenarioLowerst, PlayProgressGaugeSuperSuccess, false));
                }
                else
                {
                    SaveMainViewProgressResultType();
                }
            }
            
            // 残りターン通知
            if (ScenarioParts?.IsNeedShowScenarioNotice() ?? false)
            {
                list.Add(CreateScenarioPartsNotice(SingleModeMainTurnStartChecker.Priority.ScenarioLowerst, ScenarioParts.ShowScenarioNotice));
            }
        }
        
        /// <summary>
        /// 研究進捗度アニメーションを再生するか
        /// </summary>
        private bool IsNeedPlayProgressGaugeSuperSuccess()
        {
            if (SingleModeChangeViewManager.Instance.HasCheckedScenarioAlertCurrentTurn(AlertDialogType.Progress, _baseModel))
            {
                return false;// このターンチェック済み
            }
            var workMecha = WorkDataManager.Instance.SingleMode.ScenarioMecha;
            var tempMecha = TempData.Instance.SingleModeData.Mecha;
            // 超成功になった
            return tempMecha.MainViewProgressResultType == SingleModeScenarioMechaDefine.ProgressResultType.GreatSuccess && 
                   workMecha.RivalData.ProgressResultType == SingleModeScenarioMechaDefine.ProgressResultType.SuperSuccess;
        }

        /// <summary>
        /// 研究進捗度アニメーションチェック状況を保存
        /// </summary>
        private void SaveMainViewProgressResultType()
        {
            SingleModeChangeViewManager.Instance.MarkAsCheckedScenarioAlert(AlertDialogType.Progress, _baseModel);// チェックターン保存
            TempData.Instance.SingleModeData.Mecha.MainViewProgressResultType = WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData.ProgressResultType;
        }
        
        /// <summary>
        /// 研究進捗度アニメーションを再生
        /// </summary>
        private IEnumerator PlayProgressGaugeSuperSuccess(SingleModeMainTurnStartChecker.StreamData streamData)
        {
            SaveMainViewProgressResultType();
            var complete = false;
            _scenarioMechaParts.PlayProgressGaugeSuperSuccess(() => complete = true);
            if (streamData.IsInLastNotice)
            {
                // これ以降の通知がないならば、即座にタップ可能にする
                yield break;
            }
            else
            {
                UIManager.Instance.LockGameCanvas(); //後続の通知がある場合に、操作されないようにロック
                yield return new WaitUntil(()=> complete); //完了待ち
                UIManager.Instance.UnlockGameCanvas();
            }
        }

        /// <summary>
        /// オーバードライブ強化通知を表示するか
        /// 常時ODフラグがON、かつ、OD未発動、かつ、EXターンの最初の１ターン目のみ
        /// </summary>
        public static bool IsNeedShowOverdrivePowerUp()
        {
            var workMecha = WorkDataManager.Instance.SingleMode.ScenarioMecha;
            if (!workMecha.OverdriveInfo.IsOverdriveBurst)
            {
                // スーパーOD状態ではない
                return false;
            }
            if (workMecha.IsUsingOverdrive())
            {
                // すでに発動済み（育成再開時など）
                return false;
            }
            if (!SingleModeScenarioMechaUtils.IsFinalFirstTurn())
            {
                // EX期間最初の1ターン以外
                return false;
            }
            return true;
        }
        
        /// <summary>
        /// オーバードライブ強化通知
        /// </summary>
        private IEnumerator ShowOverdrivePowerUp(SingleModeMainTurnStartChecker.StreamData streamData)
        {
            // オーバードライブ強化通知
            var isCompletePowerUp = false;
            DialogSingleModeScenarioMechaOverdrivePowerUp.PushDialog(()=> isCompletePowerUp = true);
            yield return new WaitUntil(() => isCompletePowerUp);
            
            // オーバードライブ発動演出
            yield return _scenarioMechaParts.CoroutineOverdrive();
        }
        
        /// <summary>
        /// スーパーオーバードライブ継続発動するか
        /// 常時ODフラグがON、かつ、OD未発動、かつ、EXターンの２ターン目以降
        /// </summary>
        private bool IsNeedShowOverdriveBurstContinue()
        {
            var workMecha = WorkDataManager.Instance.SingleMode.ScenarioMecha;
            if (!workMecha.OverdriveInfo.IsOverdriveBurst)
            {
                // スーパーOD状態ではない
                return false;
            }
            if (workMecha.IsUsingOverdrive())
            {
                // すでに発動済み（育成再開時など）
                return false;
            }
            if (!SingleModeScenarioMechaUtils.IsFinalSecondTurn())
            {
                // EX期間の2ターン以降ではない
                return false;
            }
            return true;
        }
        
        /// <summary>
        /// 次の必要研究レベル演出を表示するかどうか
        /// </summary>
        private bool IsShowNextTargetLevel()
        {
            var prevUGETurn = SingleModeScenarioMechaUtils.GetPrevMechaSchedule()?.TurnNum ?? 0;
            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            if (SingleModeChangeViewManager.Instance.HasCheckedScenarioAlertCurrentTurn(AlertDialogType.TargetLvup, _baseModel))
            {
                return false;// このターンチェック済み
            }
            // 前の目標の直後のターンのときに1回だけ目標変更演出を出す
            return currentTurn - prevUGETurn == 1 && !SingleModeScenarioMechaUtils.IsCompleteResearch();
        }
        
        /// <summary>
        /// スーパーオーバードライブ継続発動
        /// </summary>
        private IEnumerator ShowOverdriveBurstContinue(SingleModeMainTurnStartChecker.StreamData streamData)
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var prevHp = workChara.Hp;
            
            // API
            var isCompleteAPI = false;
            SingleModeMechaAPI.SendMechaOverdrive(() =>
            {
                _scenarioMechaParts.SetupOverdriveContinue();//UI更新
                isCompleteAPI = true;
            });

            // オーバードライブ継続演出
            var isCompleteA2U = false;
            DialogSingleModeScenarioMechaOverdriveContinue.PushDialog(()=> isCompleteA2U = true);
            // 終了待ち
            yield return new WaitUntil(() => isCompleteA2U && isCompleteAPI);
            
            //HPゲージ変動
            if (SingleModeScenarioMechaBoardModel.IsContainsHpOverdriveEffect())
            {
                var hp = workChara.Hp;
                UIManager.SingleModeHeader.HpGauge.PlayValueOnTrainingExec(hp, prevHp);
                var hpGauge = SingleModeMainServiceLocator.Instance.Resolve<SingleModeMainHeaderAndFooterController>().HpGauge.HpGaugeFlashPlayer;
                var mechaHpGauge = hpGauge as SingleModeMainViewHpGaugeA2UScenarioMecha;
                mechaHpGauge?.PlayHpRecoverAnimation();
                AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_PARAM_TEXT_CHANGE_UP);
                if (prevHp < hp)
                {
                    // 回復音
                    AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_RECOVERY);
                }
            }
            // やる気OD効果（変動しなくてもUIやエフェクトは再生する）
            if (SingleModeScenarioMechaBoardModel.IsContainsMotivationOverdriveEffect())
            {
                var mechaMotivationButton = UIManager.SingleModeHeader.MotivationButton.PlayerBase as SingleModeMainViewMotivationButtonA2UScenarioMecha;
                mechaMotivationButton.SetOneShotOverdrive();
                UIManager.SingleModeHeader.MotivationButton.PlayUp(workChara.Motivation);
                AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_PARAM_TEXT_CHANGE_UP);
            }
        }

        /// <summary>
        /// チュートリアル表示
        /// </summary>
        public override void TutorialCommandSelectStart(Action callback)
        {
            if (WorkDataManager.Instance.SingleMode.IsEnableUniqueCommand() == false)
            {
                // シナリオギミック前はスルー
                callback?.Invoke();
                return;
            }

            var tutorialGuideId = GetTutorialGuideId();
            if (!DialogTutorialGuide.IsAlreadyRead(tutorialGuideId))
            {
                _charaController.IsRestrictCharaMessage = true;
            }

            DialogTutorialGuide.PushDialogWithReadCheck(tutorialGuideId, callback);
        }

        #endregion
        
        private PartsSingleModeScenarioMechaOverdriveCutIn _cutIn;

        public void PlayCutIn(Action onFinish, bool autoFinalize)
        {
            _cutIn = PartsSingleModeScenarioMechaOverdriveCutIn.CreateAndPlay(() =>
            {
                if(autoFinalize)FinalizeCutIn();
                onFinish?.Invoke();
            });
        }

        public void FinalizeCutIn()
        {
            if (_cutIn == null) return;
            _cutIn.DestroyCutIn();
            GameObject.Destroy(_cutIn.gameObject);
            _cutIn = null;
        }
        
        /// <summary>
        /// 次回の目標研究レベル演出
        /// </summary>
        private IEnumerator ShowNextTargetLevelNotice(SingleModeMainTurnStartChecker.StreamData streamData)
        {
            const float DIALOG_CLOSE_WAIT_DURATION = 0.3f;

            // 1つ前にダイアログを表示していればハケを待つ、 IsExistDialogで判定するとハケの最中に演出が再生されてしまうため
            if (streamData.IsDialogInPrevNotice)
            {
                UIManager.Instance.LockGameCanvas();
                yield return new WaitForSeconds(DIALOG_CLOSE_WAIT_DURATION);
                UIManager.Instance.UnlockGameCanvas();
            }

            var showNotice = true;
            PartsSingleModeMainViewScenarioMechaLvupNotice.Play(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RESEARCH_LV_UP_PATH, SingleModeScenarioMechaUtils.GetNextMechaScheduleTargetLevel(), () => showNotice = false);
            SingleModeChangeViewManager.Instance.MarkAsCheckedScenarioAlert(AlertDialogType.TargetLvup, _baseModel); // チェックターン保存
            yield return new WaitWhile(() => showNotice);
        }
        
        /// <summary>
        /// 次回の目標研究レベル演出
        /// </summary>
        private IEnumerator ShowNextTargetLevelSetup(SingleModeMainTurnStartChecker.StreamData streamData)
        {
            UIManager.Instance.LockGameCanvas();
            var complete = false;
            _scenarioMechaParts.PlayUpdateTargetLevelAnimation(() => complete = true);
            if (streamData.IsInLastNotice)
            {
                // これ以降の通知がないならば、即座にタップ可能にする
                UIManager.Instance.UnlockGameCanvas();
                yield break;
            }
            else
            {
                yield return new WaitUntil(() => complete);
                UIManager.Instance.UnlockGameCanvas();
            }
        }

    }
}
