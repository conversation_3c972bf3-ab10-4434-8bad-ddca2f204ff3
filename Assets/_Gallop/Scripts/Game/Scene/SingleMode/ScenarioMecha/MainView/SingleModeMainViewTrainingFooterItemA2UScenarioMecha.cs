using System.Linq;
using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// トレーニングコマンドボタンA2U：メカ編
    /// </summary>
    public class SingleModeMainViewTrainingFooterItemA2UScenarioMecha : SingleModeMainViewTrainingFooterItemA2UBase
    {
        private FlashPlayer _guideBadgeFlashPlayer;
        private FlashPlayer _guideBadgeFlashPlayer2;
        private AnMotion _overdriveMotion;
        private GameObject _overdriveInstance;
        
        private GameObject _hintODEffect = null;
        
        private const int BADGE_EFFECT_SORT_ORDER = 101; // 基本的にヒントバッヂのOrderより１大きい数字を動的に入れるが、万一取得できなかったときに備えて個別で定義

        /// <summary> PlayerA2UPath </summary>
        protected override string PlayerA2UPath => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BTN_TRAINING_MENU_PATH;
        
        protected override string TipsBadgeFlashPlayerPath => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_SKILL_HINT_BADGE;

        /// <summary> DL登録：シナリオ拡張用 </summary>
        protected override void RegisterDownloadScenario(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BADGE_PARTS_ICON_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BTN_TRAINING_MENU00);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_SKILL_HINT_BADGE);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BADGE_KNACK_PATH);
        }
        
        public override void PlayIn(float duration, bool isSelect)
        {
            base.PlayIn(duration, isSelect);
            // オーバードライブ効果表示のために、再度呼び出し
            SetupTrainingButtonBadgeScenario(_turnInfo);
        }
        
        /// <summary>
        /// ヒントバッジ、オススメバッジの表示設定
        /// </summary>
        protected override void SetupTrainingButtonBadgeScenario(WorkSingleModeData.TurnInfo turnInfo)
        {
            SetupOverdrive();
            if (_guideBadgeFlashPlayer == null)
            {
                CreateBadgeFlash(out _guideBadgeFlashPlayer, ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BADGE_PARTS_ICON_PATH, "loc_badge_left00");
            }

            // バッジルート（ヒントなど）
            var isTips = turnInfo.IsExistTips();
            
            // シナリオ専用のバッヂがない場合は元のバッヂを使用
            if (_tipsBadgeFlashPlayer == null)
            {
                CreateBadgeFlash(out _tipsBadgeFlashPlayer, base.TipsBadgeFlashPlayerPath, sortOffset:TipsBadgeSortOffset);
            }
            
            // ヒントバッジ
            _tipsBadgeFlashPlayer.Play(isTips ? GameDefine.A2U_IN_LABEL : GameDefine.A2U_IN00_LABEL);
            PlayMotionBadgeKnackRoot(isTips);
            
            // スキルヒント持ち、かつオーバードライブ中、かつオーバードライブ効果にスキル関連のものがあればエフェクト表示
            var hintBadgeBaseMotion = _tipsBadgeFlashPlayer.GetMotion("MOT_mc_badge_number00");
            var isUsingOverdrive = WorkDataManager.Instance.SingleMode.Character.ScenarioMecha.IsUsingOverdrive();
            var hasSkillHintOverDriveEffect = SingleModeScenarioMechaBoardModel.IsContainsOverdriveEffect(MasterSingleMode09ChipEffect.EffectTypeEnum.OverdriveConfirmHint)
                || SingleModeScenarioMechaBoardModel.IsContainsOverdriveEffect(MasterSingleMode09ChipEffect.EffectTypeEnum.GetAllHint);
            var isActiveEffect = isTips && isUsingOverdrive && hasSkillHintOverDriveEffect;
            hintBadgeBaseMotion?.SetResetModeType(AnMotion.ResetModeTypes.None);
            hintBadgeBaseMotion?.SetMotionPlay(isActiveEffect ? GameDefine.A2U_IN01_LABEL : GameDefine.A2U_IN00_LABEL);
            
            // 上に載せるエフェクトの生成
            if (_hintODEffect == null)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BADGE_KNACK_PATH);
                var badgeObj = _tipsBadgeFlashPlayer?.GetObj("OBJ_mc_badge_number00");
                _hintODEffect = GameObject.Instantiate(prefab, badgeObj?.Transform);
                // エフェクトがやや左下に生成されてしまうためその対策
                _hintODEffect.transform.localPosition = StaticVariableDefine.SingleMode.SingleModeScenarioMechaDefine.BADGE_EFFECT_OFFSET;
                
                // ヒントバッヂよりも上にエフェクトを生成
                const int BADGE_OFFSET = 5;
                var canvas = _hintODEffect.GetComponent<Canvas>();
                if (canvas != null)
                {
                    canvas.sortingOrder = _tipsBadgeFlashPlayer?.SortOffset + BADGE_OFFSET ?? BADGE_EFFECT_SORT_ORDER;
                }
                // アルファの伝搬
                var canvasRenderers = _hintODEffect.GetComponentsInChildren<CanvasRenderer>();
                foreach (var canvasRenderer in canvasRenderers)
                {
                    var receiver = canvasRenderer.gameObject.GetComponent<A2UAlphaReceiver>();
                    receiver?.Set(_tipsBadgeFlashPlayer?.Motion, canvasRenderer);
                }
            }
            _hintODEffect.SetActiveWithCheck(isActiveEffect);
            

            if (_guideBadgeFlashPlayer == null) return;

            // オススメバッジ
            var mechaCommandInfo = WorkDataManager.Instance.SingleMode.ScenarioMecha.GetCommandInfo(turnInfo);
            if (mechaCommandInfo == null) return;
            var isRecommend = mechaCommandInfo.IsRecommend;
            _flatoutPlayer.GetMotion("MOT_mc_ico_left00").SetMotionPlay(isRecommend ? GameDefine.A2U_IN_LABEL : GameDefine.A2U_IN00_LABEL);
            _guideBadgeFlashPlayer.Play(isRecommend ? GameDefine.A2U_IN_LABEL : GameDefine.A2U_IN00_LABEL);
        }
        
        /// <summary>
        /// オーバードライブ状態設定
        /// </summary>
        private void SetupOverdrive()
        {
            var isOverdrive = WorkDataManager.Instance.SingleMode.ScenarioMecha.IsUsingOverdrive();//オーバードライブが発動しているか

            if(isOverdrive)
            {
                if (_overdriveInstance == null)
                {
                    CreateOverdriveEffect();
                }
            }
            _overdriveInstance?.SetActive(isOverdrive);

            if (_overdriveMotion == null)
            {
                _overdriveMotion = _flatoutPlayer.GetMotion("MOT_training_eff");
                _overdriveMotion.SetResetModeType(AnMotion.ResetModeTypes.None);
            }
            _overdriveMotion.SetMotionPlay(isOverdrive ? GameDefine.A2U_LOOP_LABEL : GameDefine.A2U_IN00_LABEL);
        }
    
        /// <summary>
        /// オーバードライブエフェクト作成
        /// </summary>
        private void CreateOverdriveEffect()
        {
            var obj = _flatoutPlayer.GetObj("OBJ_loc_overdrive_eff00");
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_BTN_TRAINING_MENU00);
            _overdriveInstance = GameObject.Instantiate(prefab, UIManager.MainCanvas.transform);//CanvasのoverrideSortingを設定するにはアクティブな階層で生成する
                    
            //エフェクトをuGUIで扱う（A2Uのαアニメに連動させたい）
            ParticleForUgui.AddParticleForUgui(_overdriveInstance, raycastTarget:false);
            foreach (var p4u in _overdriveInstance.GetComponentsInChildren<ParticleForUgui>())
            {
                p4u.transform.localScale = Math.VECTOR3_ONE;//エフェクトにZ0が入っているとスプライトが表示されないので1
                p4u.SetupScaleForGameCanvas();
            }
            // アルファ連動
            var canvasRendererArray = _overdriveInstance.GetComponentsInChildren<CanvasRenderer>();
            foreach (var canvasRenderer in canvasRendererArray)
            {
                var receiver = canvasRenderer.gameObject.AddComponent<A2UAlphaReceiver>();
                receiver.Set(obj, canvasRenderer);
                receiver.SetEnableColorReceive(true);//カラー連動ON（無効時のグレーアウトも連動させる）
            }
            
            // 描画順
            var canvas = _overdriveInstance.AddComponent<Canvas>();
            canvas.overrideSorting = true;
            canvas.sortingLayerName = UIManager.UI_SORTING_LAYER_NAME;
            var particleSystemRendererArray = _overdriveInstance.GetComponentsInChildren<ParticleSystemRenderer>();
            canvas.sortingOrder = particleSystemRendererArray.Max(x =>x.sortingOrder);
            
            // ロケーター位置に配置
            _overdriveInstance.transform.SetParent(obj.Transform);
            _overdriveInstance.InitTransform();
        }
        
        protected override void SetupTrainingFailureRateScenario(WorkSingleModeData.TurnInfo turnInfo)
        {
            SetupTrainingFailureRateScenarioMecha(turnInfo);
        }
        
        private void SetupTrainingFailureRateScenarioMecha(WorkSingleModeData.TurnInfo turnInfo)
        {
            base.SetupTrainingFailureRateScenario(turnInfo);
            // オーバードライブ中、かつオーバードライブ効果に失敗率0があれば専用表示
            var isUsingOverdrive = WorkDataManager.Instance.SingleMode.Character.ScenarioMecha.IsUsingOverdrive();
            var hasSkillHintOverDriveEffect = SingleModeScenarioMechaBoardModel.IsContainsOverdriveEffect(MasterSingleMode09ChipEffect.EffectTypeEnum.DontFailure);
            if (isUsingOverdrive && hasSkillHintOverDriveEffect)
            {
                _anMotionFailureRate?.SetMotionPlay("in4");
            }
        }
    }
}
