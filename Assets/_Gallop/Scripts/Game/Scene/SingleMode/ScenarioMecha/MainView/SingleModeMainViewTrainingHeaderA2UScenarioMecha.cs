namespace Gallop
{
    /// <summary>
    /// 育成追加シナリオ：メカ編：トレーニングヘッダー
    /// </summary>
    public sealed class SingleModeMainViewTrainingHeaderA2UScenarioMecha : SingleModeMainViewTrainingHeaderA2UBase
    {
        protected override string FlashPlayerPath => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TRAINING_MENU_BASE00_PATH;
        
        /// <summary>
        /// 選択トレーニングの情報をA2Uに適用する
        /// </summary>
        public override void ApplyTrainingInfo(TrainingDefine.TrainingCommandId selectedCommandId, int commandLevel)
        {
            if (_flashPlayer == null) return;
            base.ApplyTrainingInfo(selectedCommandId, commandLevel);
            SetupStatusLevelBonusIcon();
        }
        
        /// <summary>
        /// 研究Lvボーナス表示のセットアップ
        /// </summary>
        private void SetupStatusLevelBonusIcon()
        {
            var label = SingleModeScenarioMechaUtils.IsStatusLevelBonusTurn() ? "ex" : GameDefine.A2U_IN00_LABEL;
            _flashPlayer.GetMotion("MOT_mc_ex_base00")?.SetMotionPlay(label);
        }
        
    }
}
