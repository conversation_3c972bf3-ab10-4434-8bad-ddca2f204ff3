using System;
using AnimateToUnity;
using UnityEngine;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;

namespace Gallop
{
    /// <summary>
    /// メカ編:研究メンバー加入演出
    /// </summary>
    public sealed class SingleModeScenarioMechaJoinMemberParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        public const int JOIN_MEMBER_MAX = 6;
        public bool IsGroupPlay => false;
        public string MessageText { get; set; }
        public TrainingParamChangeA2UContext CreateA2UContext() => new SingleModeScenarioMechaJoinMemberA2U();
    }
    
    /// <summary>
    /// メカ編:研究メンバー加入演出A2U
    /// </summary>
    public class SingleModeScenarioMechaJoinMemberA2U : TrainingParamChangeA2UContext
    {
        public override string FlashPath => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_EVENT_JOIN_PATH;
        public override float PlayTime => ANIMATION_LONG_TIME;
        public override string OutMotionName => TextUtil.Format(GameDefine.A2U_OUT_NUM_LABEL, _changeParameter.ParamValue);
        public override void PlaySE() { }
        
        private const string ICON_OBJ_NAME_FORMAT = "OBJ_mc_dum_ico_join{0:D2}/PLN_dum_ico_join00";

        public override void LoadPlayer(Transform contentsRoot, Transform parent, float speed, int layer = -1, string sortLayer = "", int sortOffset = -1)
        {
            var fa = FlashActionPlayer.Load(FlashPath, parent);
            _flashPlayer = fa.LoadFlashPlayer();
        }

        public override void Play(ChangeParameterInfo changeParameter, bool enableGroupPlay, Action unsetIsPlaying, Action setIsEnd)
        {
            base.Play(changeParameter, enableGroupPlay, unsetIsPlaying, setIsEnd);
            foreach (var value in changeParameter.ValueList.Indexed())
            {
                var charaId = value.item;
                var iconPath = ResourcePath.GetCharaThumbnailIconPath(charaId);
                var planePath = TextUtil.Format(ICON_OBJ_NAME_FORMAT, value.index + 1);
                _flashPlayer.SetTexture(planePath, ResourceManager.LoadOnView<Texture2D>(iconPath));
            }
        }

        /// <summary>
        /// SE再生用ラベル名取得
        /// </summary>
        private string GetSeLabel()
        {
            var num = _changeParameter.ParamValue;
            if (num <= 2)
            {
                // 2名以下はinに合わせる
                return TextUtil.Format(GameDefine.A2U_IN_NUM1_LABEL, num);
            }
            return TextUtil.Format(GameDefine.A2U_SE_NUM_LABEL, num);
        }
        
        public override void PlayInMotion(bool enableGroupPlay)
        {
            //人数に応じて再生するラベルを変更する
            _flashPlayer.Play(TextUtil.Format(GameDefine.A2U_IN_NUM1_LABEL, _changeParameter.ParamValue));
            
            //人数に応じてSEを再生するタイミングを変更する
            var seLabel = GetSeLabel();

            _flashPlayer.AddActionCallBack(seLabel, (obj) => AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_LOGPOSI_04), AnMotionActionTypes.Start);

            for (int i = 0; i < _changeParameter.ParamValue; ++i)
            {
                var planePath = TextUtil.Format(ICON_OBJ_NAME_FORMAT, i + 1);
                _flashPlayer.GetPlane(planePath).SetVisible(true);
            }
        }
    }
}