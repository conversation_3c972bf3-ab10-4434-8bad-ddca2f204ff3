using UnityEngine;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;

namespace Gallop
{
    /// <summary>
    /// メカ編:オーバードライブ回数回復
    /// </summary>
    public sealed class SingleModeScenarioMechaGainOverdriveRecoveryParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        public bool IsGroupPlay => false;
        public string MessageText => TextId.SingleModeScenarioMecha194003.Text();

        public TrainingParamChangeA2UContext CreateA2UContext()
        {
            return new SingleModeScenarioMechaGainOverdriveRecoveryA2U(Value);
        }
    }

    public class SingleModeScenarioMechaGainOverdriveRecoveryA2U : TrainingParamChangeA2UContext
    {
        public SingleModeScenarioMechaGainOverdriveRecoveryA2U(int recoveryNum)
        {
            _recoveryNum = recoveryNum;
        }
        private int _recoveryNum;
        public override string FlashPath => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_RECOVERY_PATH;
        public override float PlayTime => ANIMATION_30_FRAME_TIME;
        public override void PlaySE() => AudioManager.Instance.PlaySe(AudioId.SFX_ADDON09_MECHA_GET);
        public override void LoadPlayer(Transform contentsRoot, Transform parent, float speed, int layer = -1, string sortLayer = "", int sortOffset = -1)
        {
            var flashAction = FlashActionPlayer.Load(FlashPath, contentsRoot);
            _flashPlayer = flashAction.LoadFlashPlayer();
            _flashPlayer.SetActiveWithCheck(false);
            if (!string.IsNullOrEmpty(sortLayer))
            {
                flashAction.SetSortLayer(sortLayer);
            }
            if (layer >= 0)
            {
                flashAction.gameObject.SetLayerRecursively(layer);
            }
        }
        public override void PlayInMotion(bool enableGroupPlay)
        {
            _flashPlayer.Play(TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, _recoveryNum));
        }
        public override string OutMotionName => TextUtil.Format(GameDefine.A2U_OUT_NUM2_LABEL, _recoveryNum);
    }
}