using System;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;

namespace Gallop
{
    /// <summary>
    /// メカ編:チューニングPt獲得
    /// </summary>
    public sealed class SingleModeScenarioMechaGainTuningPointParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        public bool IsGroupPlay => false;
        public string MessageText => TextId.SingleModeScenarioMecha194084.Format(TextId.SingleModeScenarioMecha194033.Text(), Value);

        public TrainingParamChangeA2UContext CreateA2UContext()
        {
            return new SingleModeScenarioMechaGainTuningPointA2U();
        }
    }

    public class SingleModeScenarioMechaGainTuningPointA2U : ParamUpTypeA2UContext
    {
        public override string FlashPath => ResourcePath.SINGLE_MODE_EVENTBONUS_UP_BONUS_PATH;
        public override float PlayTime => ANIMATION_UP_TIME;

        public override void Play(ChangeParameterInfo changeParameter, bool enableGroupPlay, Action unsetIsPlaying, Action setIsEnd)
        {
            base.Play(changeParameter, enableGroupPlay, unsetIsPlaying, setIsEnd);
            var sprite = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioMecha).GetSprite(AtlasSpritePath.SingleModeScenarioMecha.ICO_TUNING_PT);
            TrainingParamChangeA2UBonusUp.Setup(_flashPlayer, TextId.SingleModeScenarioMecha194033.Text(), sprite);
        }
    }
}