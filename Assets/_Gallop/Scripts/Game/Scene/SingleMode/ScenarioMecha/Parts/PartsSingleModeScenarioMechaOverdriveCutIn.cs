using System;
using DG.Tweening;
using Gallop.Model.Component;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// メカ編:オーバードライブカットイン
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioMechaOverdriveCutIn : MonoBehaviour
    {
        [SerializeField] private Transform _a2uBgRoot;
        [SerializeField] private Transform _a2uTextRoot;
        [SerializeField] private RawImageCommon _image3D;
        [SerializeField] private ButtonCommon _skipButton;

        private SingleModeScenarioMechaOverdriveCutInHelper _cutInHelper;
        private Action _onFinish;

        /// <summary>
        /// 149380 A2U背景オブジェクトのインスタンス
        /// </summary>
        private GameObject _a2uBgInstance = null;
        /// <summary>
        /// 149308 A2U背景プレハブに含まれているキャンバス
        /// </summary>
        private Canvas _a2uBgCanvas = null;

        private static string GetBgEffectPath(int boardId) => ResourcePath.GetSingleModeScenarioMechaOverdriveCutBgPath(boardId);
        private static string GetBurstBgEffectPath() => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_CUT_IN_BURST_BG_PATH;

        /// <summary>
        /// DL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // 影の登録
            ShadowController.RegisterDownload(register);

            // カットリソース登録
            var phaseList = EnumUtil.GetEnumArray<SingleModeScenarioMechaDefine.MechaDressPhase>();
            foreach (var mechaDressPhase in phaseList)
            {
                // カット
                foreach (var masterBoard in MasterDataManager.Instance.masterSingleMode09Board.dictionary.Values)
                {
                    register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaOverdriveCutPath(mechaDressPhase, masterBoard.Id));
                }
            }
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_BURST_CUT_PATH);


            // 背景エフェクト
            foreach (var masterBoard in MasterDataManager.Instance.masterSingleMode09Board.dictionary.Values)
            {
                register.RegisterPathWithoutInfo(GetBgEffectPath(masterBoard.Id));
            }
            register.RegisterPathWithoutInfo(GetBurstBgEffectPath());

            // A2U
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_TXT_PATH);
        }

        /// <summary>
        /// 生成してカットインを再生する
        /// </summary>
        public static PartsSingleModeScenarioMechaOverdriveCutIn CreateAndPlay(Action onFinish)
        {
            var workMecha = WorkDataManager.Instance.SingleMode.ScenarioMecha;
            var trendBoardId = workMecha.GetTrendBoardId();
            var context = new SingleModeScenarioMechaOverdriveCutInHelper.Context(SingleModeScenarioMechaUtils.GetMechaCharaDressPhase(), trendBoardId, workMecha.OverdriveInfo.IsOverdriveBurst);
            return CreateAndPlay(context, onFinish);
        }

        public static PartsSingleModeScenarioMechaOverdriveCutIn CreateAndPlay(SingleModeScenarioMechaOverdriveCutInHelper.Context context, Action onFinish)
        {
            // Scene/Viewのリソースロードを固有のHashに変更して解放準備する
            SingleModeMainViewController.PushSceneResourceHash();

            var parent = TargetCanvas.transform;
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_CUTIN);
            var obj = Instantiate(prefab, parent);
            obj.SetLayerRecursively(parent.gameObject.layer);
            var parts = obj.GetComponent<PartsSingleModeScenarioMechaOverdriveCutIn>();
            parts._onFinish = onFinish;
            parts.Setup(context);
            parts.Play(context);
            return parts;
        }

        private void Setup(SingleModeScenarioMechaOverdriveCutInHelper.Context context)
        {
            _image3D.SetActiveWithCheck(false);

            _cutInHelper = new SingleModeScenarioMechaOverdriveCutInHelper();
            _cutInHelper.Init();
            _cutInHelper.UseOutputRenderTexture = true;
            CreateA2UText(context);
            CreateCutInBgEffect(context);
        }

        private void Play(SingleModeScenarioMechaOverdriveCutInHelper.Context context)
        {
            UIManager.Instance.ResetImageEffectBlur();//#150560 HideImageEffectBlurFadeによりブラーが終わってないとEnableUIImageEffectが書き換えられる危険があるのでTweenを完了させる
            UIManager.Instance.EnableUIImageEffect(true);
            _cutInHelper.Play(context, OnEndCutIn, _a2uBgCanvas.gameObject);
            _image3D.SetActiveWithCheck(true);
            _image3D.texture = _cutInHelper.OutputRenderTexture;

            // 再生0.2秒後からスキップ可能
            DOVirtual.DelayedCall(GameDefine.COMMON_TAP_WAIT_400MS, () =>
            {
                _skipButton.SetOnClick(OnEndCutIn);
            });
        }

        /// <summary>
        /// 3Dカットイン背景エフェクト生成
        /// </summary>
        private void CreateCutInBgEffect(SingleModeScenarioMechaOverdriveCutInHelper.Context context)
        {
            var path = context.IsBurst ? GetBurstBgEffectPath() : GetBgEffectPath(context.BoardId);
            var prefab = ResourceManager.LoadOnView<GameObject>(path);
            _a2uBgInstance = Instantiate(prefab, UIManager.GameCanvas.transform);
            _a2uBgInstance.SetLayerRecursively(UIManager.GameCanvas.gameObject.layer);
            _a2uBgCanvas = _a2uBgInstance.GetComponentInChildren<Canvas>();
            var sortingOrder = 0;
            if (_a2uBgCanvas != null)
            {
                //エフェクト内の背景2Dの描画順調整
                _a2uBgCanvas.sortingOrder = sortingOrder;
                //UIより優先したいので、「AfterUI」に指定
                _a2uBgCanvas.sortingLayerName = GameDefine.SORTINGLAYER_NAME_AFTERUI;
            }
            UIUtil.SetParticleSortOrder(_a2uBgInstance, SortingLayerName, sortingOrder);//エフェクト内パーティクル描画順調整
        }


        /// <summary>
        /// A2U生成
        /// </summary>
        private void CreateA2UText(SingleModeScenarioMechaOverdriveCutInHelper.Context context)
        {
            var actionPlayer = FlashActionPlayer.Load(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_TXT_PATH, _a2uTextRoot);
            actionPlayer.LoadFlashPlayer();
            actionPlayer.gameObject.SetLayerRecursively(_a2uTextRoot.gameObject.layer);
            actionPlayer.SetSortLayer(SortingLayerName);
            actionPlayer.SetSortOffset(SortingOrder + 1);//カット演出より上に文字A2U
            const int OVERDRIVE_BURST_LABEL_ID = 4; // スーパーオーバードライブのラベルは4
            var labelId = context.IsBurst ? OVERDRIVE_BURST_LABEL_ID : context.BoardId;
            actionPlayer.Play(TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, labelId));
        }

        public static Canvas TargetCanvas => UIManager.NoImageEffectCanvas;
        private string SortingLayerName => TargetCanvas.sortingLayerName;
        private int SortingOrder => (int)UIManager.CanvasSoringOrder.System;

        /// <summary>
        /// カットインの終了
        /// </summary>
        private void OnEndCutIn()
        {
            _onFinish?.Invoke();
            _onFinish = null;
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void UpdateView()
        {
            _cutInHelper?.AlterUpdate();
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void LateUpdateView()
        {
            _cutInHelper?.AlterLateUpdate();
        }

        /// <summary>
        /// 破棄
        /// </summary>
        public void DestroyCutIn()
        {
            UIManager.Instance.EnableUIImageEffect(false);
            if (_cutInHelper != null)
            {
                _cutInHelper.CleanUp();
                _cutInHelper.CleanupPlaying();
                _cutInHelper = null;
            }

            //149380 背景用オブジェクトは別キャンバスに配置するので、ここで明示的に削除
            if (_a2uBgInstance != null)
            {
                GameObject.Destroy(_a2uBgInstance);
                _a2uBgInstance = null;
                _a2uBgCanvas = null;
            }

            // リソース解放
            SingleModeMainViewController.PopSceneResourceHash(false);
        }
    }
}