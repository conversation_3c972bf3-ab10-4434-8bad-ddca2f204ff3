using UnityEngine;
using UnityEngine.UI;

namespace Gallop 
{
    /// <summary>
    /// メカ編:メカウマ娘詳細ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioMechaRivalDetail : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] private RawImageCommon _bgImage;
        [SerializeField] private RawImageCommon _rivalImage;
        [SerializeField] private PartsSingleModeScenarioMechaProgressGauge _progressGauge;
        [SerializeField] private HorizontalLayoutGroup _totalLevelLayout;//合計/目標研究Lv表記のレイアウト整列
        [SerializeField] private BitmapTextCommon _totalLevel;//合計研究Lv
        [SerializeField] private BitmapTextCommon _targetLevel;//目標研究Lv
        [SerializeField] private BitmapTextCommon _remainLevel;//残り研究Lv
        [SerializeField] private GameObject _targetLevelCompleteRoot;//残り研究Lvがない時「--」表示
        [SerializeField] private GameObject _targetLevelRemainRoot;//残り研究Lvがあるとき「あと」表示
        [SerializeField] private GameObject _remainRoot;// 全ての研究スケジュールが終わってないときは、残り研究Lv関連の表示
        [SerializeField] private GameObject _titleRemainRoot;// 目標研究Lvラベルの表示
        [SerializeField] private GameObject _titleRemainRoot2;// /ラベルの表示
        [SerializeField] private FlickToggleGroupCommon _toggleGroupCommon;
        [SerializeField] private FlickableObject _flickableObject;
        [SerializeField] private GameObject[] _tabContentsObjectArray;
        [SerializeField] private TextCommon[] _statusLvBonusTextArray;
        [SerializeField] private PartsSingleModeScenarioMechaRivalStatusItem[] _rivalStatusArray;
        [SerializeField] private PartsSingleModeScenarioMechaTuningRadarChart _radarChart;
        [SerializeField] private PartsSingleModeScenarioMechaChipEffectList _chipEffectList;
        [SerializeField] private ButtonCommon _charaDetailButton;

        private bool _setupChipEffectList;
        private bool _enableCharaDetail;
        
        /// <summary> タブ種類 </summary>
        private enum TabIndex
        {
            Status,  //研究
            ChipEffect,//チップ効果
        }

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPath(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_MUSUME_INFO_DECO);
            var mechaDressPhaseArray = EnumUtil.GetEnumArray<SingleModeScenarioMechaDefine.MechaDressPhase>();
            foreach (var mechaDressPhase in mechaDressPhaseArray)
            {
                register.RegisterPath(ResourcePath.GetSingleModeScenarioMechaInfoDecoRivalIcon(mechaDressPhase));
            }
            BitmapTextCommon.RegistDownload(register, TextFormat.BitmapFont.SingleModeMechaResearchLv);
            // チップリソース
            foreach (var singleMode09Chip in MasterDataManager.Instance.masterSingleMode09Chip.dictionary.Values)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaChipIconPath(singleMode09Chip.Id));
            }
        }
        
        public static void PushDialog(bool enableCharaDetail)
        {
            var dialogComponent = LoadAndInstantiatePrefab<DialogSingleModeScenarioMechaRivalDetail>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_MECHA_RIVAL_DETAIL);
            var dialogData = dialogComponent.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioMecha194045.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogComponent.Setup(enableCharaDetail);
            DialogManager.PushDialog(dialogData);
        }
        
        public void Setup(bool enableCharaDetail)
        {
            _enableCharaDetail = enableCharaDetail;
            SetHeaderInfo();
            SetupTotalLevelTargetLevel();
            SetupTab();
            SetupStatus();
            _radarChart.Setup(SingleModeScenarioMechaBoardModel.GetEffectedChipModelListByBoardModelList(SingleModeScenarioMechaBoardModel.GetBoardModelList()), false);
            SetupTrainingBonus();
            SetupCharaDetailButton(enableCharaDetail);
        }

        /// <summary>
        /// メカウマ娘情報
        /// </summary>
        private void SetHeaderInfo()
        {
            _bgImage.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_MUSUME_INFO_DECO, DialogHash);
            _rivalImage.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.GetSingleModeScenarioMechaInfoDecoRivalIcon(SingleModeScenarioMechaUtils.GetMechaCharaDressPhase()), DialogHash);
            _progressGauge.Setup();
        }

        /// <summary>
        /// 合計研究Lv/目標研究Lv関連のフレームUI配下の情報設定
        /// </summary>
        private void SetupTotalLevelTargetLevel()
        {
            var totalLevel = WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData.TotalLevel;
            _totalLevel.text = TextId.Race0612.Format(totalLevel);//合計研究Lv

            var isCompleteResearch = SingleModeScenarioMechaUtils.IsCompleteResearch();
            _remainRoot.SetActiveWithCheck(!isCompleteResearch);// 全ての研究スケジュールが終わってないときは、残り研究Lv関連の表示
            _titleRemainRoot.SetActiveWithCheck(!isCompleteResearch); // フレームタイトルの「目標研究Lv」は研究完了時不要
            _titleRemainRoot2.SetActiveWithCheck(!isCompleteResearch);
            _targetLevel.SetActiveWithCheck(!isCompleteResearch);//完了時は目標Lv不要
            if (!isCompleteResearch)
            {
                _targetLevel.text = TextId.Common0268.Format(SingleModeScenarioMechaUtils.GetNextMechaScheduleTargetLevel());//目標研究Lv
                
                var remainLevel = SingleModeScenarioMechaUtils.GetNextMechaScheduleRemainLevel();
                _remainLevel.text = remainLevel.ToString(); // あとLv〇
                
                //Bitmapフォントを小さいフォントサイズで使用するとき、自動レイアウトではサイズ計算が合わないためUpdateWidthWithTextを実行してサイズを決める
                const float BITMAP_CHAR_WIDTH_RATE = 82f / 92f;//素材の元サイズのW比率
                _remainLevel.UpdateWidthWithText(_remainLevel.FontSize, _remainLevel.FontSize * BITMAP_CHAR_WIDTH_RATE);
                
                var isDrawRemainLevel = remainLevel > 0;
                _targetLevelRemainRoot.SetActiveWithCheck(isDrawRemainLevel);//残り研究Lvがあるとき「あと」表示
                _targetLevelCompleteRoot.SetActiveWithCheck(!isDrawRemainLevel);//残り研究Lvがない時「--」表示
            }
            else
            {
                // 合計表記のみの場合はオフセット変更
                const int PADDING_LEFT = -46;
                _totalLevelLayout.padding.left = PADDING_LEFT;
            }
        }
        
        /// <summary>
        /// タブ設定
        /// </summary>
        private void SetupTab()
        {
            _toggleGroupCommon.SetOnSelectCallback(OnChangeTabToggle);
            _toggleGroupCommon.Initialize(0);
            _flickableObject.SetFlickCallback(_toggleGroupCommon.OnFlick);
        }
        
        /// <summary>
        /// タブ切り替えコールバック
        /// </summary>
        private void OnChangeTabToggle(int index)
        {
            for (var i = 0; i < _tabContentsObjectArray.Length; i++)
            {
                _tabContentsObjectArray[i].SetActive(i==index);
            }

            if (index == (int)TabIndex.ChipEffect)
            {
                // 負荷の最適化のため、タブが選択されてから生成する
                SetupEffectChipList();
            }
        }
        
        /// <summary>
        /// チップ効果一覧
        /// </summary>
        private void SetupEffectChipList()
        {
            if (_setupChipEffectList) return;
            _setupChipEffectList = true;
            _chipEffectList.Setup();
            if (_enableCharaDetail)
            {
                var rectTrans = (_chipEffectList.transform as RectTransform);
                const int HEIGHT = 1020; // ウマ娘詳細ボタンがあるときは、リストサイズを狭める 
                rectTrans.sizeDelta = new Vector2(rectTrans.sizeDelta.x, HEIGHT);
            }
        }

        /// <summary>
        /// メカウマ娘研究Lv設定
        /// </summary>
        private void SetupStatus()
        {
            var rivalData = WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData;
            _rivalStatusArray[(int)GameDefine.ParameterType.Speed].Setup(rivalData.Speed, rivalData.SpeedLimit);
            _rivalStatusArray[(int)GameDefine.ParameterType.Stamina].Setup(rivalData.Stamina, rivalData.StaminaLimit);
            _rivalStatusArray[(int)GameDefine.ParameterType.Power].Setup(rivalData.Pow, rivalData.PowLimit);
            _rivalStatusArray[(int)GameDefine.ParameterType.Guts].Setup(rivalData.Guts, rivalData.GutsLimit);
            _rivalStatusArray[(int)GameDefine.ParameterType.Wiz].Setup(rivalData.Wiz, rivalData.WizLimit);
        }
        
        /// <summary>
        /// 研究Lvによるトレーニングボーナス設定
        /// </summary>
        public void SetupTrainingBonus()
        {
            var rival = WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData;
            var masterLearningBonus = MasterDataManager.Instance.masterSingleMode09LearningBonus;
            var bonusValueArray = new[]
            {
                masterLearningBonus.GetTrainingBonusValue(rival.Speed),
                masterLearningBonus.GetTrainingBonusValue(rival.Stamina),
                masterLearningBonus.GetTrainingBonusValue(rival.Pow),
                masterLearningBonus.GetTrainingBonusValue(rival.Guts),
                masterLearningBonus.GetTrainingBonusValue(rival.Wiz),
            };
            for (var i = 0; i < _statusLvBonusTextArray.Length; i++)
            {
                var bonusValue = Math.MasterInt2FloatPercent(bonusValueArray[i]);
                var textCommon = _statusLvBonusTextArray[i];
                textCommon.text = TextId.SingleModeScenarioMecha465001.Format(bonusValue);
                textCommon.FontColor = bonusValue > 0 ? FontColorType.Emphasis : FontColorType.Brown;
                textCommon.UpdateColor();
            }
        }

        /// <summary>
        /// 育成ウマ娘詳細ボタン設定
        /// </summary>
        private void SetupCharaDetailButton(bool enableCharaDetail)
        {
            _charaDetailButton.SetActiveWithCheck(enableCharaDetail);
            _charaDetailButton.SetOnClick(()=>DialogSingleModeMainCharacterDetail.Open(WorkDataManager.Instance.SingleMode.Character));
        }
    }
}
