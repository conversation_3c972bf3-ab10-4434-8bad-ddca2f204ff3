using System;
using Gallop.Model.Component;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// メカ編:チューニングカットイン
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioMechaTuningCutIn : MonoBehaviour
    {
        [SerializeField] private Transform _a2uBgRoot;
        [SerializeField] private Transform _a2uTextRoot;
        [SerializeField] private RawImageCommon _image3D;

        private SingleModeScenarioMechaTuningCutInHelper _cutInHelper;
        private FlashActionPlayer _a2uTextPlayer;
        private Action _onFinish;
        private static string GetBgEffectPath(int boardId) => ResourcePath.GetSingleModeScenarioMechaTuningCutBgPath(boardId);
        private static string GetBurstBgEffectPath() => ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TUNING_CUT_IN_BURST_BG_PATH;

        private const int OVERDRIVE_BURST_LABEL_ID = 4; // スーパーオーバードライブのラベルは4

        /// <summary>
        /// DL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // 影の登録
            ShadowController.RegisterDownload(register);

            foreach (var masterBoard in MasterDataManager.Instance.masterSingleMode09Board.dictionary.Values)
            {
                // 背景エフェクト
                register.RegisterPathWithoutInfo(GetBgEffectPath(masterBoard.Id));
                //149681 チューニング完了と通常時両方のカットをダウンロード
                // カットリソース登録
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaTuningCutPath(masterBoard.Id, true));
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaTuningCutPath(masterBoard.Id, false));
            }
            register.RegisterPathWithoutInfo(GetBurstBgEffectPath());

            // A2U
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TUNING_TXT_PATH);

            PartsSingleModeScenarioMechaTuningSuperODCutIn.RegisterDownload(register);
        }

        /// <summary>
        /// 生成してカットインを再生する
        /// </summary>
        public static PartsSingleModeScenarioMechaTuningCutIn CreateAndPlay(Action onFinish)
        {
            var workMecha = WorkDataManager.Instance.SingleMode.ScenarioMecha;
            var trendBoardId = workMecha.GetTrendBoardId();
            var context = new SingleModeScenarioMechaTuningCutInHelper.Context(SingleModeScenarioMechaUtils.GetMechaCharaDressPhase(), trendBoardId, workMecha.OverdriveInfo.IsOverdriveBurst);
            return CreateAndPlay(context, onFinish);
        }

        public static PartsSingleModeScenarioMechaTuningCutIn CreateAndPlay(SingleModeScenarioMechaTuningCutInHelper.Context context, Action onFinish)
        {
            // Scene/Viewのリソースロードを固有のHashに変更して解放準備する
            SingleModeMainViewController.PushSceneResourceHash();

            var parent = UIManager.NoImageEffectCanvas.transform;
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TUNING_CUTIN);
            var obj = Instantiate(prefab, parent);
            obj.SetLayerRecursively(parent.gameObject.layer);
            var parts = obj.GetComponent<PartsSingleModeScenarioMechaTuningCutIn>();
            parts._onFinish = onFinish;
            parts.Setup(context);
            parts.Play(context);
            return parts;
        }

        private void Setup(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            _image3D.SetActiveWithCheck(false);

            _cutInHelper = new SingleModeScenarioMechaTuningCutInHelper();
            _cutInHelper.Init();
            _cutInHelper.UseOutputRenderTexture = true;
            CreateA2UText(context);
            CreateCutInBgEffect(context);
        }

        private void Play(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            UIManager.Instance.EnableUIImageEffect(true);
            _cutInHelper.Play(context, OnEndCutIn);
            _image3D.SetActiveWithCheck(true);
            _image3D.texture = _cutInHelper.OutputRenderTexture;
        }

        /// <summary>
        /// 3Dカットイン背景エフェクト生成
        /// </summary>
        private void CreateCutInBgEffect(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            var path = context.IsBurst ? GetBurstBgEffectPath() : GetBgEffectPath(context.BoardId);
            var prefab = ResourceManager.LoadOnView<GameObject>(path);
            var instance = Instantiate(prefab, _a2uBgRoot);
            instance.SetLayerRecursively(UIManager.NoImageEffectCanvas.gameObject.layer);
            var canvas = instance.GetComponentInChildren<Canvas>();
            var sortingOrder = SortingOrder - 1;//A2U系より下げる
            if (canvas != null)
            {
                //エフェクト内の背景2Dの描画順調整
                canvas.sortingOrder = sortingOrder;
                canvas.sortingLayerName = SortingLayerName;
            }
            UIUtil.SetParticleSortOrder(instance, SortingLayerName, sortingOrder);//エフェクト内パーティクル描画順調整
        }

        /// <summary>
        /// A2U生成
        /// </summary>
        private void CreateA2UText(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            var actionPlayer = FlashActionPlayer.Load(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TUNING_TXT_PATH, _a2uTextRoot);
            actionPlayer.LoadFlashPlayer();
            actionPlayer.gameObject.SetLayerRecursively(_a2uTextRoot.gameObject.layer);
            actionPlayer.SetSortLayer(SortingLayerName);
            actionPlayer.SetSortOffset(SortingOrder + 1);//カット演出より上に文字A2U
            var labelId = context.IsBurst ? OVERDRIVE_BURST_LABEL_ID : context.BoardId;
            actionPlayer.Play(TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, labelId));
            _a2uTextPlayer = actionPlayer;
        }

        private string SortingLayerName => UIManager.NoImageEffectCanvas.sortingLayerName;
        private int SortingOrder => (int)UIManager.CanvasSoringOrder.System;

        /// <summary>
        /// カットインの終了時にスーパーオーバードライブに変化するか判定
        /// </summary>
        private void OnEndCutIn(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            if (context.IsBurst)
            {
                BurstOverDrive(context);
                return;
            }
            OnFinishCutIn(context);
        }

        /// <summary>
        /// スーパーオーバードライブが発動可能になったカットインを再生
        /// </summary>
        private void BurstOverDrive(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            SetParentMainCanvas();
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TUNING_SUPER_OD_CUTIN);
            var obj = Instantiate(prefab, UIManager.Instance.NoImageEffectGameCanvasRoot.transform);
            obj.GetComponent<PartsSingleModeScenarioMechaTuningSuperODCutIn>().BurstOverDrive(() => OnFinishCutIn(context));
        }

        private void SetParentMainCanvas()
        {
            // ブラーを掛けるためにMainCanvasに移動
            gameObject.transform.SetParent(UIManager.MainCanvas.transform);
            gameObject.SetLayerRecursively(UIManager.UI_SORTING_LAYER_NAME);
            UIManager.Instance.ShowImageEffectBlurFade(color: GameDefine.COLOR_BLACK_HALF_ALPHA);
        }

        /// <summary>
        /// カットインの終了
        /// </summary>
        private void OnFinishCutIn(SingleModeScenarioMechaTuningCutInHelper.Context context)
        {
            var labelId = context.IsBurst ? OVERDRIVE_BURST_LABEL_ID : context.BoardId;
            _a2uTextPlayer.Play(TextUtil.Format(GameDefine.A2U_OUT_NUM2_LABEL, labelId));
            _onFinish?.Invoke();
            _onFinish = null;
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void UpdateView()
        {
            _cutInHelper?.AlterUpdate();
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void LateUpdateView()
        {
            _cutInHelper?.AlterLateUpdate();
        }

        /// <summary>
        /// 破棄
        /// </summary>
        public void DestroyCutIn()
        {
            UIManager.Instance.EnableUIImageEffect(false);
            if (_cutInHelper != null)
            {
                _cutInHelper.CleanUp();
                _cutInHelper.CleanupPlaying();
                _cutInHelper = null;
            }
            // リソース解放
            SingleModeMainViewController.PopSceneResourceHash(false);
        }
    }
}