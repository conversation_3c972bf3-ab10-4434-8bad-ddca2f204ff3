using System;
using UnityEngine;

namespace Gallop 
{
    /// <summary>
    /// メカ編:オーバードライブ警告ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioMechaOverdriveWarning : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;
        
        [SerializeField] private RawImageCommon _image;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_WARNING_PATH);
        }
        
        /// <summary>
        /// ダイアログ開く
        /// </summary>
        public static void PushDialog(Action onDecide)
        {
            var dialogComponent = LoadAndInstantiatePrefab<DialogSingleModeScenarioMechaOverdriveWarning>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_WARNING);
            var dialogData = dialogComponent.CreateDialogData();
            dialogData.Title = TextId.SingleMode0002.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = _ => onDecide();
            dialogData.EnableOutsideClick = false;
            dialogData.IsFooterNotificationText = true;
            dialogData.FooterText = TextId.SingleModeScenarioMecha465004.Text();
            dialogComponent.Setup();
            DialogManager.PushDialog(dialogData);
        }

        private void Setup()
        {
            _image.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_WARNING_PATH, DialogHash);
            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_NOTICE_01);
        }
    }
}
