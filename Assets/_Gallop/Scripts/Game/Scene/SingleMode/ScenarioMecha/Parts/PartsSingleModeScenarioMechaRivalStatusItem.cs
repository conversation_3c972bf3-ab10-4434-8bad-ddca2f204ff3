using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// メカ編：メカウマ娘の研究Lvの1要素
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioMechaRivalStatusItem : MonoBehaviour
    {
        [SerializeField] private TextMeshProUguiCommon _lvText;
        [SerializeField] private TextMeshProUguiCommon _lvMaxText;
        [SerializeField] private GameObject _lvMaxIcon;
        
        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(int point, int pointMax)
        {
            _lvText.text = point.ToString();
            _lvMaxText.text = TextId.Common0268.Format(pointMax);
            
            var isLevelMax = point >= pointMax;
            _lvMaxIcon.SetActive(isLevelMax);
        }
    }
}
