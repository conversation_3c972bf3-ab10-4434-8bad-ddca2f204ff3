using Cute.UI;
using UnityEngine;

namespace Gallop 
{
    /// <summary>
    /// メカ編：TextCommonのタグアイコン表示の改修版
    /// メカ編専用処理のためタグでアトラスの指定を省略する（メカ編アトラス使用）
    /// アイコン名の前半prefix固定部分は省略する
    ///　タグの入力例　：　<mecha=obtain_00/>
    /// </summary>
    public class PartsSingleModeScenarioMechaTagIconPerser : TextCommon.IIconTagParser
    {
        public string Tag => "<mecha=";
        private const string TAG_END = "/>";
        private const string SPRITE_FORMAT = "utx_ico_mecha_tag_{0}";
        public Sprite GetSprite(string tag, int posIndex)
        {
            var spriteName = GetSpriteName(tag);

            AtlasReference atlas = null;
            if (Application.isPlaying == false || UIManager.HasInstance() == false)
            {
                atlas = Resources.Load<AtlasReference>(ResourcePath.GetAtlasPath(TargetAtlasType.SingleModeScenarioMecha));
            }
            else if (UIManager.HasInstance())
            {
                atlas = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioMecha);
            }
            if (atlas == null)
            {
                Debug.LogWarning($"メカ編アトラスが取得できませんでした");
                return null;
            }

            // スプライトロード
            var sprite = atlas.GetSprite(spriteName);
            if (sprite == null)
            {
                Debug.LogWarning($"テキスト内アイコンタグに指定されたスプライト名が不正。spriteName　= {spriteName}");
                return null;
            }
            return sprite;
        }            
        
        /// <summary>
        /// タグの内容からスプライト名を抜き出す
        /// 【<mecha=study_01>】　⇒　utx_ico_mecha_tag_study_01　で返す
        /// </summary>
        private string GetSpriteName(string tag)
        {
            var spriteName = tag.Remove(0, Tag.Length);//"<mecha="削除
            spriteName = spriteName.Remove(spriteName.Length - TAG_END.Length, TAG_END.Length);//"/>"削除
            return TextUtil.Format(SPRITE_FORMAT, spriteName); // 省略した前半の名称をつなげる
        }
    }
}
