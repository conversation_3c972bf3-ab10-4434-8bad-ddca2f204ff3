using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop 
{
    /// <summary>
    /// メカ編:チップ効果リスト
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioMechaChipEffectList : MonoBehaviour
    {
        [SerializeField] private ScrollRectCommon _contentScrollRectCommon;
        [SerializeField] private RectTransform _content;
        [SerializeField] private RectTransform _contentTop;
        [SerializeField] private RectTransform _contentBottom;
        [SerializeField] private PartsSingleModeScenarioMechaTuningRadarChart _radarChart;
        [SerializeField] private PartsSingleModeScenarioMechaEffectedChipListItem _overdriveListItem;
        [SerializeField] private PartsSingleModeScenarioMechaEffectedChipListItem _mainListItem;
        [SerializeField] private GameObject _mainEmptyRoot;
        [SerializeField] private TextCommon[] _rivalStatusBonusTextArray;

        private List<PartsSingleModeScenarioMechaEffectedChipListItem> _overdriveItemList = new List<PartsSingleModeScenarioMechaEffectedChipListItem>();
        private List<PartsSingleModeScenarioMechaEffectedChipListItem> _overdriveDestroyReserveItemList = new List<PartsSingleModeScenarioMechaEffectedChipListItem>();
        private List<PartsSingleModeScenarioMechaEffectedChipListItem> _mainItemList = new List<PartsSingleModeScenarioMechaEffectedChipListItem>();
        private List<PartsSingleModeScenarioMechaEffectedChipListItem> _mainDestroyReserveItemList = new List<PartsSingleModeScenarioMechaEffectedChipListItem>();
        private bool _checkNewEffectScroll;
        private float _newEffectScrollBottomY;
        private float _newEffectScrollTopY;
        private Tweener _tweenerScroll;
        private Tween _tweenDelayCall;
        private const float SCROLL_FOCUS_ANIM_DURATION = GameDefine.BASE_FPS_TIME * 6;

        public void Setup()
        {
            Setup(SingleModeScenarioMechaBoardModel.GetEffectedChipModelListByBoardModelList(SingleModeScenarioMechaBoardModel.GetBoardModelList()), false);
        }

        /// <summary>
        /// チューニング画面など、任意でPt割り振りした状態を渡したい場合のセットアップ
        /// </summary>
        public void Setup(List<SingleModeScenarioMechaBoardModel> boardModelList, bool playAnimation)
        {
            Setup(SingleModeScenarioMechaBoardModel.GetEffectedChipModelListByBoardModelList(boardModelList), playAnimation);
        }

        private void Setup(List<SingleModeScenarioMechaChipModel> chipModelList, bool playAnimation)
        {
            // 研究Lvレーダーチャート
            _radarChart?.Setup(chipModelList, playAnimation);
            // 研究Lv表
            SetupRivalStatusBonus(chipModelList);
            // オーバードライブ効果
            SetupOverdrive(chipModelList, playAnimation);
            // 育成ウマ娘効果
            SetupMain(chipModelList, playAnimation);
            
            if (playAnimation)
            {
                // アニメーション設定
                SetupPlayAnimation();
            }
        }

        /// <summary>
        /// オーバードライブ効果の設定
        /// </summary>
        private void SetupOverdrive(List<SingleModeScenarioMechaChipModel> chipModelList, bool playAnimation)
        {
            var overdriveChipModelList = chipModelList.Where(x => x.IsOverdrive).OrderBy(x=>x.ChipId).ToList();
            SetupChipListItem(overdriveChipModelList, playAnimation, _overdriveItemList, _overdriveDestroyReserveItemList, _overdriveListItem);
        }
        
        private void SetupChipListItem(List<SingleModeScenarioMechaChipModel> chipModelList, bool playAnimation, 
            List<PartsSingleModeScenarioMechaEffectedChipListItem> itemList,
            List<PartsSingleModeScenarioMechaEffectedChipListItem> destroyReserveItemList,
            PartsSingleModeScenarioMechaEffectedChipListItem prefabItem)
        {
            // 要素が減少した場合削除する
            for (var i = itemList.Count-1; i >= 0; i--)
            {
                if (!chipModelList.Exists(x => x.ChipId == itemList[i].ChipId))
                {
                    itemList[i].PlayDestroyAnimation();
                    destroyReserveItemList.Add(itemList[i]);//破棄用リストに入れる
                    itemList.RemoveAt(i);
                }
            }

            // 更新と新規チップ生成
            bool isNew = false;
            prefabItem.SetActiveWithCheck(false);//1件もない時、元プレファブが表示されないように
            for (var i = 0; i < chipModelList.Count; i++)
            {
                var chipModel = chipModelList[i];
                var listItem = itemList.Find(x => x.ChipId == chipModel.ChipId);
                if (listItem == null)
                {
                    // 新規作成
                    UIUtil.CreateScrollItem(prefabItem, itemList, new[]{chipModel}, (item, chipModel) =>
                    {
                        item.Setup(chipModel, playAnimation);
                        isNew = true;
                    });
                }
                else
                {
                    // 更新
                    listItem.Setup(chipModel, playAnimation);
                }
            }

            //　要素が増えた場表、表示順ソート
            if (isNew)
            {
                itemList = itemList.OrderBy(x => x.ChipId).ToList();
                for (var i = 0; i < itemList.Count; i++)
                {
                    itemList[i].transform.SetSiblingIndex(i);
                }
            }
        }

        #region アニメーション関連
        
        /// <summary>
        /// 新規効果が発生した場合、効果の挿入アニメーションとスクロールのフォーカスを再生する
        /// </summary>
        private void SetupPlayAnimation()
        {
            // レイアウト確定前に挿入アニメーション中のもの完了させておく
            _overdriveItemList.ForEach(x => x.CompleteAnimation());
            _mainItemList.ForEach(x => x.CompleteAnimation());

            // レイアウト自動整列
            ForceRebuildLayout();

            // スクロールフォーカス
            CheckScrollFocus();
            PlayNewEffectScroll();

            // 挿入アニメーション再生
            _overdriveItemList.ForEach(x => x.PlayInsertAnimationIfNew());
            _mainItemList.ForEach(x => x.PlayInsertAnimationIfNew());
        }
        
        /// <summary>
        /// アニメーション前に自動レイアウトを確定させる
        /// </summary>
        private void ForceRebuildLayout()
        {
            LayoutRebuilder.ForceRebuildLayoutImmediate(_content);  // アニメーション前に自動レイアウトのコンテンツ幅を全て確定させておく
            _contentScrollRectCommon.movementType = ScrollRect.MovementType.Clamped;//スクロール外にいると引き戻されるので完了までOFF
            _contentScrollRectCommon.StopMovement();                // 移動中であれば停止
            if (_tweenDelayCall != null)
            {
                _tweenDelayCall.Kill();
                _tweenDelayCall = null;
            }
            _tweenDelayCall = DOVirtual.DelayedCall(SCROLL_FOCUS_ANIM_DURATION, () =>
            {
                _contentScrollRectCommon.movementType = ScrollRect.MovementType.Elastic;
            });
        }

        /// <summary>
        /// スクロールフォーカス対象をチェック
        /// </summary>
        private void CheckScrollFocus()
        {
            _checkNewEffectScroll = false;

            // 破棄されたものは除外
            RemoveDestroyItemList(_overdriveDestroyReserveItemList);
            RemoveDestroyItemList(_mainDestroyReserveItemList);
            
            // オーバードライブを検索(チップやレーダーチャートにはフォーカス不要）
            if (CheckItemList(_overdriveItemList)) return;
            CheckItemList(_overdriveDestroyReserveItemList);
        }

        /// <summary>
        /// 対象リストに変動があればスクロールフォーカス設定
        /// </summary>
        private bool CheckItemList(List<PartsSingleModeScenarioMechaEffectedChipListItem> itemList)
        {
            foreach (var item in itemList)
            {
                if (item.IsChangeEffected())
                {
                    SetScrollTargetPosition(item.ContentBottom.position.y, item.ContentTop.position.y);
                    return true;
                }
            }
            return false;
        }
        
        /// <summary>
        /// 中身に破棄されたオブジェクトがあればリストから削除
        /// </summary>
        private void RemoveDestroyItemList(List<PartsSingleModeScenarioMechaEffectedChipListItem> itemList)
        {
            for (var i = itemList.Count-1; i >= 0; i--)
            {
                if (itemList[i] == null)
                {
                    itemList.RemoveAt(i);
                }
            }
        }
        
        /// <summary>
        /// スクロールフォーカス設定
        /// </summary>
        private void SetScrollTargetPosition(float bottomY, float topY)
        {
            _newEffectScrollBottomY = bottomY;
            _newEffectScrollTopY = topY;
            _checkNewEffectScroll = true;
        }

        /// <summary>
        /// 新規効果にスクロールフォーカスする
        /// </summary>
        public void PlayNewEffectScroll()
        {
            if (_checkNewEffectScroll == false) return;

            var viewportHeight = _contentScrollRectCommon.viewport.rect.height;
            var contentHeight = _content.rect.height;
            var contentPosY = _content.anchoredPosition.y;
            if (contentHeight < viewportHeight)
            {
                // スクロールエリア内にコンテンツが収まるときはフォーカス不要
                return;
            }

            const float MARGIN = 1;//誤差許容マージン
            if (contentPosY < -MARGIN || contentHeight + MARGIN < contentPosY)
            {
                // スクロールエリア外にコンテンツがいる状況は、ユーザー操作によりスクロールを引っ張った状態や引っ張り戻り中なのでフォーカス不要
                return;
            }
            
            var newItemBottomY = _newEffectScrollBottomY;
            var scrollBottomY = _contentBottom.position.y;
            
            if (scrollBottomY > newItemBottomY)
            {
                // 新規アイテムの下部がスクロール下部より下なら、フォーカス移動する
                var deltaY = scrollBottomY - newItemBottomY;
                PlayFocusScroll(deltaY);
                return;
            }
            
            var newItemTopY = _newEffectScrollTopY;
            var scrollTopY = _contentTop.position.y;
            
            if (scrollTopY < newItemTopY)
            {
                var deltaY = scrollTopY - newItemTopY;
                PlayFocusScroll(deltaY);
            }
        }

        /// <summary>
        /// スクロールフォーカス
        /// </summary>
        private void PlayFocusScroll(float deltaY)
        {
            ClearFocusScroll();// スクロール途中のものがあれば中断する

            SetEnableScrollMovement(false);//フォーカススクロール中は、ユーザー操作のスクロールを禁止
            _tweenerScroll = _content.DOMoveY(_content.position.y + deltaY, SCROLL_FOCUS_ANIM_DURATION).SetEase(Ease.OutCubic).OnComplete(() =>
            {
                _tweenerScroll = null;
                SetEnableScrollMovement(true);
                if (!_contentScrollRectCommon.vertical)
                {
                    // フォーカス中にスクロール領域内に収まった場合に位置をデフォルトに戻す
                    _content.anchoredPosition = Vector2.zero;
                }
            }).OnUpdate(() =>
            {
                // #146205 スクロール最下部で、縮小＋フォーカスが同時に発生した場合、フォーカス移動がスクロール領域外に行き過ぎるのを抑制する
                var contentHeight = _content.sizeDelta.y;
                if (_content.anchoredPosition.y > contentHeight)
                {
                    _content.SetAnchoredPositionY(contentHeight);
                }
            });
        }
        
        /// <summary>
        /// スクロールフォーカス中止
        /// </summary>
        private void ClearFocusScroll()
        {
            if (_tweenerScroll == null) return;
            _tweenerScroll.Kill();
            _tweenerScroll = null;
        }
        
        /// <summary>
        /// ユーザー操作によるスクロールビュー移動のON/OFF
        /// </summary>
        private void SetEnableScrollMovement(bool enable)
        {
            if(!enable)_contentScrollRectCommon.StopMovement();
            _contentScrollRectCommon.inertia = enable;
        }
        
        #endregion 

        /// <summary>
        /// 育成ウマ娘への効果設定
        /// </summary>
        private void SetupMain(List<SingleModeScenarioMechaChipModel> chipModelList, bool playAnimation)
        {
            var mainChipModelList = chipModelList.FindAll(x => x.IsMainEffectType);
            SetupChipListItem(mainChipModelList, playAnimation, _mainItemList, _mainDestroyReserveItemList, _mainListItem);
            _mainEmptyRoot.SetActiveWithCheck(mainChipModelList.Count <= 0);
        }

        /// <summary>
        /// 研究Lvボーナス設定
        /// </summary>
        private void SetupRivalStatusBonus(List<SingleModeScenarioMechaChipModel> chipModelList)
        {
            var masterEffectList = SingleModeScenarioMechaChipModel.GetMasterChipEffectListByPointRivalStatusType(chipModelList);
            int GetBonusValue(TrainingDefine.TrainingCommandId cmd) => masterEffectList.Find(x => x.EffectValue1 == (int)cmd)?.EffectValue2 ?? 0;
            var bonusValueArray = new[]
            {
                GetBonusValue(TrainingDefine.TrainingCommandId.Turf),
                GetBonusValue(TrainingDefine.TrainingCommandId.Pool),
                GetBonusValue(TrainingDefine.TrainingCommandId.Dirt),
                GetBonusValue(TrainingDefine.TrainingCommandId.Slope),
                GetBonusValue(TrainingDefine.TrainingCommandId.Study),
            };
            for (var i = 0; i < _rivalStatusBonusTextArray.Length; i++)
            {
                var bonusValue = bonusValueArray[i];
                var textCommon = _rivalStatusBonusTextArray[i];
                textCommon.text = TextId.SingleMode0037.Format(TextUtil.ToStringChange(bonusValue));
                textCommon.FontColor = bonusValue > 0 ? FontColorType.Emphasis : FontColorType.Brown;
                textCommon.UpdateColor();
            }
        }
    }
}
