using UnityEngine;

namespace Gallop 
{
    /// <summary>
    /// メカ編:オーバードライブ効果確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioMechaOverdriveDetail : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] private PartsSingleModeScenarioMechaEffectedChipListItem _effectedChipListItem;

        public void Setup()
        {
            var overdriveChipModelList = SingleModeScenarioMechaBoardModel.GetEffectedOverdriveChipModelList();
            UIUtil.CreateScrollItem(_effectedChipListItem, null, overdriveChipModelList, (item, chipModel) =>
            {
                item.Setup(chipModel);
            });
        }
        public static void PushDialog()
        {
            var dialogComponent = LoadAndInstantiatePrefab<DialogSingleModeScenarioMechaOverdriveDetail>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_DETAIL);
            var dialogData = dialogComponent.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioMecha194096.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogComponent.Setup();
            DialogManager.PushDialog(dialogData);
        }
    }
}
