using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// メカ編：チップに関するデータクラス
    /// </summary>
    public class SingleModeScenarioMechaChipModel
    {
        public int ChipId { get; }
        /// <summary> 割り振りPt </summary>
        public int Point 
        {
            get
            {
                if (IsOverdrive)
                {
                    if (_overdriveLinkPointChipList == null) return 1;
                    // オーバードライブチップはボード所属チップの合計Pt
                    return _overdriveLinkPointChipList.Sum(x => x.Point);
                }
                return _point;
            }
        }
        private int _point;

        /// <summary> 割り振り最大か </summary>
        public bool IsMaxPoint => Point == MaxPoint;
        /// <summary> 割り振り最大ポイント </summary>
        public int MaxPoint => GetMasterChipEffectList().Max(x => x.Point);

        /// <summary> アイコン </summary>
        public Texture2D IconTexture => ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetSingleModeScenarioMechaChipIconPath(ChipId));

        /// <summary> マスター </summary>
        private MasterSingleMode09Chip.SingleMode09Chip MasterChip;
        /// <summary> ボードID </summary>
        public int BoardId => MasterChip.BoardId;
        /// <summary> 名前（1行）</summary>
        public string Name => MasterChip.Name;
        /// <summary> 名前 （改行付き。チューニング画面など狭いUIで使用）</summary>
        public string RawName => MasterChip.RawName;
        /// <summary> オーバードライブタイプか </summary>
        public bool IsOverdrive => MasterChip.IsOverdrive;

        /// <summary> 育成ウマ娘への効果タイプか </summary>
        public bool IsMainEffectType => GetMasterChipEffectList().Exists(x => x.IsMainEffectType);

        /// <summary> ロック中か </summary>
        public bool IsLock 
        {
            get
            {
                var masterSchedule = MasterDataManager.Instance.masterSingleMode09Schedule.Get(UnLockScheduleId);
                if (masterSchedule == null) return false;//ロック条件なし
                
                var turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
                if (masterSchedule.TurnNum < turn) return false;//ターン経過済みなら解放

                if (masterSchedule.TurnNum <= turn && SingleModeScenarioMechaUtils.IsTuningEnable()) return false;//同じターンでもUGE終了でチューニング状態に入っていたら解放
                
                //ロック
                return true;
            }
        }
        /// <summary> 現在アンロックされた新規チップかどうか </summary>
        public bool IsNewUnlock 
        {
            get
            {
                if (!SingleModeScenarioMechaUtils.IsTuningEnable()) return false;//チューニング期間外
                
                var masterSchedule = MasterDataManager.Instance.masterSingleMode09Schedule.Get(UnLockScheduleId);
                if (masterSchedule == null) return false;//ロック条件ないので新規でない
                
                var turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
                return masterSchedule.TurnNum == turn;
            }
        }
        /// <summary> ロック解除のUGE番号 </summary>
        public int UnLockScheduleId => MasterChip.ScheduleId;

        /// <summary>
        /// オーバードライブチップの場合の割り振りポイントは、ボード所属の他チップの合計であるためチップリストを渡して計算する
        /// </summary>
        private List<SingleModeScenarioMechaChipModel> _overdriveLinkPointChipList;
        public void SetOverdriveLinkPointChipList(List<SingleModeScenarioMechaChipModel> chipList)
        {
            _overdriveLinkPointChipList = chipList;
        }

        /// <summary> コンストラクタ </summary>
        public SingleModeScenarioMechaChipModel(int chipId)
        {
            ChipId = chipId;
            MasterChip = MasterDataManager.Instance.masterSingleMode09Chip.Get(chipId);
            _point = WorkDataManager.Instance.SingleMode.ScenarioMecha.GetChipInfoPoint(chipId);
        }
        
        /// <summary> チューニング画面ポイント割り振り </summary>
        public void PlusPoint() => _point++;
        public void MinusPoint() => _point--;
        public void ResetPoint() => _point = 0;
        
        /// <summary> 効果マスターリスト </summary>
        private List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectList() => 
            MasterDataManager.Instance.masterSingleMode09ChipEffect.GetListWithChipId(ChipId);

        /// <summary> 効果の変わり目となるptリストを取得。(2pt,4pt,6ptなど効果の変わり目ptを羅列) </summary>
        public List<int> GetChipEffectThresholdPointList()
        {
            return GetMasterChipEffectList().Select(x => x.Point).Distinct().OrderBy(x=>x).ToList();
        }
        
        /// <summary>
        /// ポイント別効果の段階数を取得
        /// </summary>
        public int GetCountChipEffectThresholdPointList() => GetChipEffectThresholdPointList().Count;

        /// <summary>
        /// 発動中の効果ポイント取得
        /// 2pt 4ptで効果が切り替わる設定の場合で、3pt割り振られている場合、2pt効果が有効であるため2が返ってくる
        /// </summary>
        public int GetEffectedPoint()
        {
            var pointList = GetChipEffectThresholdPointList();//効果の閾値となるpointリスト
            return pointList.Where(x => x <= Point).OrderByDescending(x=>x).FirstOrDefault();
        }

        /// <summary>
        /// 発動中のポイント効果がリストの何番目かIndex取得
        /// </summary>
        public int GetEffectedPointIndex()
        {
            var point = GetEffectedPoint();
            return GetChipEffectThresholdPointList().FindIndex(x => x == point);
        }
        
        /// <summary>
        /// 最大のPt効果まで発動できているか
        /// </summary>
        public bool IsMaxEffectedPoint()
        {
            return GetCountChipEffectThresholdPointList() == GetEffectedPointIndex()+1;
        }

        /// <summary> ポイント達成している効果マスターリスト </summary>
        public List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPoint()
        {
            return GetMasterChipEffectListByPoint(Point);
        }
        public List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPoint(int targetPoint)
        {
            var result = new List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect>();
            var group = GetMasterChipEffectList().GroupBy(x => x.GroupId);
            foreach (var singleMode09ChipEffects in group)
            {
                // グループ別リスト内でポイント達成したものを抽出し、その最大達成項目を取り出す
                var lastEffect = singleMode09ChipEffects.Where(eff => eff.Point <= targetPoint)
                    .OrderByDescending(eff=>eff.Point).FirstOrDefault();
                if (lastEffect == null) continue;
                result.Add(lastEffect);
            }
            return result;
        }

        /// <summary> ポイント達成している育成ウマ娘への効果マスターリスト </summary>
        private List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPointMainType()
        {
            return GetMasterChipEffectListByPoint().FindAll(x => x.IsMainEffectType);
        }
        public static List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPointMainType(List<SingleModeScenarioMechaChipModel> chipModelList)
        {
            return chipModelList.SelectMany(x => x.GetMasterChipEffectListByPointMainType()).ToList();
        }

        /// <summary> ポイント達成しているオーバードライブ効果マスターリスト </summary>
        public List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPointOverdriveType()
        {
            return GetMasterChipEffectListByPoint().FindAll(x => x.IsOverdriveEffectType);
        }
        public static List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPointOverdriveType(List<SingleModeScenarioMechaChipModel> chipModelList)
        {
            return chipModelList.SelectMany(x => x.GetMasterChipEffectListByPointOverdriveType()).ToList();
        }
        
        /// <summary> ポイント達成している研究Lv効果マスターリスト </summary>
        private List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPointRivalStatusType()
        {
            return GetMasterChipEffectListByPoint().FindAll(x => x.IsRivalStatusPointEffectType);
        }
        public static List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> GetMasterChipEffectListByPointRivalStatusType(List<SingleModeScenarioMechaChipModel> chipModelList)
        {
            return chipModelList.SelectMany(x => x.GetMasterChipEffectListByPointRivalStatusType()).ToList();
        }
    }
}
