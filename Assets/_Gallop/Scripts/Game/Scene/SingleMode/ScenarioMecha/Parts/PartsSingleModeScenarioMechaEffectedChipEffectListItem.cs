using DG.Tweening;
using UnityEngine;

namespace Gallop 
{
    /// <summary>
    /// メカ編:チップが発動中のチップ効果リスト要素
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioMechaEffectedChipEffectListItem : MonoBehaviour
    {
        [SerializeField] private ImageCommon _bg;
        [SerializeField] private bool _isNarrowText;
        [SerializeField] private TextCommon _text;
        [SerializeField] private GameObject _badgeRoot;
        [SerializeField] private TextCommon _badgeText;
        [SerializeField] private GameObject _newAnimationRoot;
        [SerializeField] private TweenAnimationTimelineComponent _newAnimation;
        [SerializeField] private TweenAnimationTimelineComponent _bonusAnimation;

        public int Index { get; private set; }
        private string _originalText;
        private MasterSingleMode09ChipEffect.SingleMode09ChipEffect _masterEffect;
        private MasterSingleMode09ChipEffect.SingleMode09ChipEffect _prevMasterEffect;
        private Transform _defaultUpDownAnimationParent;
        
        public void Setup(int index, bool isLastIndex, MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterEffect)
        {
            _prevMasterEffect = _masterEffect;
            _masterEffect = masterEffect;
            if (_defaultUpDownAnimationParent == null && _bonusAnimation != null)
            {
                _defaultUpDownAnimationParent = _bonusAnimation.transform.parent;//デフォルト保存
            }
            var text = _isNarrowText ? SingleModeScenarioMechaUtils.GetChipEffectedDetailNarrow(masterEffect) : SingleModeScenarioMechaUtils.GetChipEffectedDetail(masterEffect);
            
            Index = index;
            var enableBg = index % 2 != 0;
            _bg.enabled = enableBg;//リスト交互に下地有効
            if (enableBg)
            {
                var spriteName = isLastIndex ? AtlasSpritePath.SingleModeScenarioMecha.CHIP_DETAIL_LABEL00 : AtlasSpritePath.SingleModeScenarioMecha.CHIP_DETAIL_LABEL01;
                _bg.sprite = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioMecha).GetSprite(spriteName);
            }

            // 内容に変更があった場合テキスト変化アニメ（チューニング画面でポイント割り振り時に変化）
            PlayChangeTextIfChenged(text);
            
            _text.SetTextWithCustomTagMultiLineIfChanged(text, new PartsSingleModeScenarioMechaTagIconPerser());
            
            if (_newAnimation?.IsPlaying() == false)
            {
                _newAnimationRoot.SetActiveWithCheck(false);
            }
            _badgeRoot.SetActiveWithCheck(false);
        }

        /// <summary>
        /// バッジ表示
        /// </summary>
        private void SetupBadge()
        {
            if (_badgeRoot == null || _badgeText == null) return;//バッジ不要な画面では省略
            
            if (IsNew)
            {
                // 新規
                _badgeRoot.SetActive(true);
                _badgeText.text = TextId.SingleModeScenarioMecha194093.Text();
                return;
            }

            if (_prevMasterEffect.Id != _masterEffect.Id &&
                _prevMasterEffect.GroupId == _masterEffect.GroupId)
            {
                // 上昇、減少
                _badgeRoot.SetActive(true);
                var isUp = _prevMasterEffect.Point < _masterEffect.Point;
                _badgeText.text = isUp ? TextId.SingleModeScenarioMecha194094.Text() : TextId.SingleModeScenarioMecha194095.Text();
                return;
            }
            
            // バッジなし
            _badgeRoot.SetActive(false);
        }

        /// <summary>
        /// テキストに変更があった場合、テキスト変更アニメを再生する
        /// </summary>
        public void PlayChangeTextIfChenged(string text)
        {
            if (!string.IsNullOrEmpty(_text.text) && _originalText != text)
            {
                // テキスト変化のアニメーション
                const float DURATION = 0.3f;
                _text.SetMulColor(GameDefine.COLOR_CLEAR_WHITE);
                DOTween.To(
                    () => _text.GetMulColor(),
                    x => _text.SetMulColor(x),
                    Color.white, DURATION);
            }
            _originalText = text;
        }
        
        /// <summary>
        /// 効果変動があれば増減エフェクト再生
        /// </summary>
        public void PlayInsertAnimation()
        {
            SetupBadge();
            
            if (IsNew) return;//新規効果はここでは再生しない（挿入伸縮アニメ後にやる）
            
            if (_prevMasterEffect.Id == _masterEffect.Id) return;//変化なし
            
            var isUpDown = _prevMasterEffect.GroupId == _masterEffect.GroupId;
            if (isUpDown)
            {
                //同じ効果グループで増減した場合、必要Ptが多いなら上昇
                var isUp = _prevMasterEffect.Point < _masterEffect.Point;
                PlayUpDownAnimation(isUp);
            }
        }

        /// <summary>
        /// 挿入アニメーション親の処理の後に呼ばれる
        /// </summary>
        public void PlayInsertAnimationAfter(bool isNewParent)
        {
            if (isNewParent || !IsNew) return;//新規でない場合なし

            // 効果項目が追加される場合は伸縮が終わってから自身を表示する
            _bg.SetActiveWithCheck(false);
        }
        
        /// <summary>
        /// 親の挿入伸縮アニメーション完了後に呼ばれる
        /// </summary>
        public void OnCompletePlayInsertAnimation()
        {
            _bg.SetActiveWithCheck(true); //完了時はBG必ず表示（連打で強制完了時に対応）
        }
        
        /// <summary>
        /// 新規効果のフレーム光彩エフェクト
        /// </summary>
        public void PlayInsertAnimationCompleteEffect()
        {
            _newAnimationRoot.SetActiveWithCheck(true);
            _newAnimation.Play(()=>_newAnimationRoot.SetActiveWithCheck(false));
            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_LOGPOSI_07);
        }

        /// <summary>
        /// 新規か
        /// </summary>
        public bool IsNew => _prevMasterEffect == null;

        /// <summary>
        /// 上昇、下降アニメーション再生
        /// </summary>
        private void PlayUpDownAnimation(bool isUp)
        {
            // 複数効果上昇した場合に前後の項目でエフェクトの表示が被るので、親階層で再生する
            _bonusAnimation.transform.SetParent(transform.parent);
            _bonusAnimation.transform.SetAsLastSibling();//他の効果より最前面に描画したい
            
            const string UP_ANIMATION_NAME = "up";
            const string DOWN_ANIMATION_NAME = "down";
            
            // 再生中のものがあれば最後まで再生した状態にして完了させる
            if (_bonusAnimation.IsPlaying(UP_ANIMATION_NAME))
            {
                _bonusAnimation.Goto(UP_ANIMATION_NAME, _bonusAnimation.Duration(UP_ANIMATION_NAME));
            }
            if (_bonusAnimation.IsPlaying(DOWN_ANIMATION_NAME))
            {
                _bonusAnimation.Goto(DOWN_ANIMATION_NAME, _bonusAnimation.Duration(DOWN_ANIMATION_NAME));
            }

            // 再生
            if (isUp)
            {
                _bonusAnimation.Play(UP_ANIMATION_NAME).OnComplete(OnComplete);
                AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_LOGPOSI_07);
            }
            else
            {
                _bonusAnimation.Play(DOWN_ANIMATION_NAME).OnComplete(OnComplete);
                AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_PARAM_TEXT_CHANGE_DOWN);
            }
            
            void OnComplete()
            {
                // 元の階層に戻す  
                if (_defaultUpDownAnimationParent != null)
                {
                    _bonusAnimation.transform.SetParent(_defaultUpDownAnimationParent);
                }
            }
        }
    }
}
