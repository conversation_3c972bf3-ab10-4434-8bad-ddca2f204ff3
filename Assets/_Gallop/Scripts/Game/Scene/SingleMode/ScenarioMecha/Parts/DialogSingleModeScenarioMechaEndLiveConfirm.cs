using System;
using UnityEngine;

namespace Gallop 
{
    /// <summary>
    /// メカ編: エンディングライブ再生確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioMechaEndLiveConfirm : DialogInnerBase
    {
        /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;

        /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;
        
        [SerializeField] private RawImageCommon _logoImage;
        [SerializeField] private RawImageCommon _textImage;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RACE_LOGO_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TXT_ENDING_LIVE_PATH);
        }

        public static void PushDialog(Action onDecide, Action onCansel)
        {
            var dialogComponent = LoadAndInstantiatePrefab<DialogSingleModeScenarioMechaEndLiveConfirm>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_MECHA_ENDING_LIVE_CONFIRM);
            var dialogData = dialogComponent.CreateDialogData();
            dialogData.LeftButtonCallBack = _ =>
            {
                TempData.Instance.SingleModeData.Mecha.IsSelectedPlayLive = false;
                onCansel();
            };
            dialogData.RightButtonCallBack = _ =>
            {
                TempData.Instance.SingleModeData.Mecha.IsSelectedPlayLive = true;
                onDecide();
            };
            DialogManager.PushDialog(dialogData);
            AudioManager.Instance.PlaySe(AudioId.SFX_ADDON09_MECHA_LIVE);
            dialogComponent.Setup();
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.EnableOutsideClick = false;
            dialogData.Title = TextId.SingleModeScenarioLive0036.Text();
            dialogData.LeftButtonText = TextId.Common0248.Text();
            dialogData.RightButtonText = TextId.SingleMode0102.Text();
            dialogData.OnPushBackKey = UIUtil.ShowNotificationBackKey;
            return dialogData;
        }

        private void Setup()
        {
            _logoImage.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_RACE_LOGO_PATH, DialogHash);
            _textImage.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_TXT_ENDING_LIVE_PATH, DialogHash);
        }
    }
}
