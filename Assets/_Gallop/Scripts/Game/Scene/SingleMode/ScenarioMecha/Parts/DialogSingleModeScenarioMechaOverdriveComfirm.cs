using System;
using UnityEngine;

namespace Gallop 
{
    /// <summary>
    /// メカ編:オーバードライブ発動確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioMechaOverdriveComfirm : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] private PartsSingleModeScenarioMechaEffectedChipListItem _effectedChipListItem;
        [SerializeField] private TextCommon _nowNum;
        [SerializeField] private TextCommon _nextNum;
        [SerializeField] private GameObject _canRoot;
        [SerializeField] private GameObject _notRoot;
        [SerializeField] private GameObject _numRoot;

        
        public void Setup()
        {
            var workMecha = WorkDataManager.Instance.SingleMode.ScenarioMecha;
            // 発動条件を達成しているオーバードライブ効果リストを取得
            var overdriveChipModelList = SingleModeScenarioMechaBoardModel.GetEffectedOverdriveChipModelList();
            UIUtil.CreateScrollItem(_effectedChipListItem, null, overdriveChipModelList, (item, chipModel) =>
            {
                item.Setup(chipModel);
            });

            // オーバードライブ回数表記（常時発動可能時は非表示）
            _numRoot.SetActiveWithCheck(!workMecha.OverdriveInfo.IsOverdriveBurst);
            
            var canOverdrive = SingleModeScenarioMechaUtils.CanUseOverdrive(WorkDataManager.Instance.SingleMode.ScenarioMecha.OverdriveInfo);
            _canRoot.SetActive(canOverdrive);
            _notRoot.SetActive(!canOverdrive);
            // オーバードライブ回数
            var nowNnm = workMecha.OverdriveInfo.RemainNnum;
            _nowNum.text = TextId.Directory0015.Format(nowNnm);
            _nextNum.text = TextId.Directory0015.Format(nowNnm-1);
        }
        public static void PushDialog(Action onClickRightButton)
        {
            var dialogComponent = LoadAndInstantiatePrefab<DialogSingleModeScenarioMechaOverdriveComfirm>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_CONFIRM);
            var dialogData = dialogComponent.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioMecha194038.Text();
            dialogData.LeftButtonText = TextId.Common0007.Text();
            dialogData.LeftButtonCallBack = d => d.Close();
            dialogData.RightButtonText = TextId.SingleModeScenarioMecha194040.Text();
            dialogData.RightButtonCallBack = _ => onClickRightButton.Invoke();
            dialogData.RightButtonNoInteractableNotiffication = SingleModeScenarioMechaUtils.GetOverdriveDisableNotification(WorkDataManager.Instance.SingleMode.ScenarioMecha.OverdriveInfo);
            dialogData.FooterText = TextId.SingleModeScenarioMecha194039.Text();
            dialogData.AutoClose = false;
            dialogComponent.Setup();
            DialogManager.PushDialog(dialogData);
        }
    }
}
