using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop 
{
    /// <summary>
    /// メカ編:チップが発動中のチップ効果リスト要素
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioMechaEffectedChipListItem : MonoBehaviour
    {
        [SerializeField] private ImageCommon _bg;
        [SerializeField] private ImageCommon _header;
        [SerializeField] private RawImageCommon _icon;
        [SerializeField] private TextCommon _text;
        [SerializeField] private PartsSingleModeScenarioMechaEffectedChipEffectListItem _effectItem;
        [SerializeField] private GameObject _overdrivePointDotRoot;
        [SerializeField] private ImageCommon _overdrivePointDotItem;
        [SerializeField] private RectTransform _contentTop;
        public RectTransform ContentTop => _contentTop;
        [SerializeField] private RectTransform _contentBottom;
        public RectTransform ContentBottom => _contentBottom;
        [SerializeField] private LayoutElement _animationInsertLayoutElement;
        [SerializeField] private LayoutElement _bgLayoutElement;
        [SerializeField] private VerticalLayoutGroup _bgLayoutGroup;
        [SerializeField] private CanvasGroup _animationInsertBgCanvasGroup;
        [SerializeField] private TweenAnimationTimelineComponent _newAnimation;


        private List<PartsSingleModeScenarioMechaEffectedChipEffectListItem> _effectItemList = new List<PartsSingleModeScenarioMechaEffectedChipEffectListItem>();
        private List<ImageCommon> _overdrivePointDotItemList = new List<ImageCommon>();
        public List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> MasterChipEffectList { get; private set; }
        private List<MasterSingleMode09ChipEffect.SingleMode09ChipEffect> _prevMasterChipEffectList;
        public SingleModeScenarioMechaChipModel ChipModel { get; private set; }
        public int ChipId => ChipModel.ChipId;
        private bool _playAnimation;
        private Tweener _tweenerInsertAnimation;
        private Tweener _tweenerDestroyAnimation;
        private List<PartsSingleModeScenarioMechaEffectedChipEffectListItem> _insertAnimationNewEffectItemList;
        private float _insertAnimationTargetHeight;
        /// <summary> 伸縮アニメ時間 </summary>
        private const float HEIGHT_ANIMATION_DURATION = GameDefine.BASE_FPS_TIME * 4;

        public void Setup(SingleModeScenarioMechaChipModel chipModel, bool playAnimation = false)
        {
            ChipModel = chipModel;
            _prevMasterChipEffectList = MasterChipEffectList;
            _playAnimation = playAnimation;
            MasterChipEffectList = chipModel.GetMasterChipEffectListByPoint();
            
            _bg.sprite = AtlasSpritePath.SingleModeScenarioMecha.GetBoardChipDetailBgSprite(chipModel.BoardId);
            _header.sprite = AtlasSpritePath.SingleModeScenarioMecha.GetBoardChipDetailHeaderSprite(chipModel.BoardId);
            _icon.texture = chipModel.IconTexture;
            _text.text = chipModel.Name;
            SetupEffectItemList();
            SetupPointDot();
            _animationInsertLayoutElement.SetActiveWithCheck(false);
        }

        private void Update()
        {
            if (!_playAnimation)
            {
                // アニメーションなしからアニメーションありに変化した時用に、高さを追従しておく
                _animationInsertLayoutElement.minHeight = _bg.rectTransform.rect.height;
            }
        }

        /// <summary>
        /// オーバードライブのPt解放段階UI
        /// </summary>
        private void SetupPointDot()
        {
            _overdrivePointDotRoot.SetActive(false);
            if (!ChipModel.IsOverdrive) return;//通常チップはスキップ

            var count = ChipModel.GetCountChipEffectThresholdPointList();//効果段階の数
            if (count <= 1) return; //1段階しかないなら表示の必要なし（基礎コア）

            _overdrivePointDotRoot.SetActive(true);
            
            var activeIndex = ChipModel.GetEffectedPointIndex();
            var atlas = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioMecha);
            for (int i = 0; i < count; i++)
            {
                var isActive = i <= activeIndex;
                var spriteName = isActive ? AtlasSpritePath.SingleModeScenarioMecha.MECHA_OD_LEVEL_ON : AtlasSpritePath.SingleModeScenarioMecha.MECHA_OD_LEVEL_OFF;
                var sprite = atlas.GetSprite(spriteName);

                if (_overdrivePointDotItemList.Count <= i)
                {
                    // インスタンス足りない場合は生成
                    UIUtil.CreateScrollItem(_overdrivePointDotItem, _overdrivePointDotItemList, new []{0}, (item, _) =>
                    {
                        item.sprite = sprite;
                    });
                }
                else
                {
                    // インスタンスが足りてるならDot状態更新
                    _overdrivePointDotItemList[i].sprite = sprite;
                }
            }
        }

        private void SetupEffectItemList()
        {
            // 要素が減少した場合削除する
            for (var i = _effectItemList.Count-1; i >= 0; i--)
            {
                if (MasterChipEffectList.Count <= i)
                {
                    Destroy(_effectItemList[i].gameObject);
                    _effectItemList.RemoveAt(i);
                }
            }
            
            int index = 0;
            foreach (var masterChipEffect in MasterChipEffectList)
            {
                var chipEffectListItem = _effectItemList.Find(x => x.Index == index);
                if (chipEffectListItem != null)
                {
                    chipEffectListItem.Setup(index, MasterChipEffectList.Count-1 == index, MasterChipEffectList[index]);
                }
                else
                {
                    // 新規効果生成
                    UIUtil.CreateScrollItem(_effectItem, _effectItemList, new[]{masterChipEffect}, (item, masterEffect) =>
                    {
                        item.Setup(index, MasterChipEffectList.Count-1 == index, masterEffect);
                    });
                }
                index++;
            }
        }

        /// <summary>
        /// 効果に変化があるか
        /// </summary>
        public bool IsChangeEffected()
        {
            if (_prevMasterChipEffectList == null ||//新規
                _prevMasterChipEffectList.Count != MasterChipEffectList.Count) return true;//変動あり
            if (_tweenerDestroyAnimation != null) return true;//消滅中も変動あり
            return MasterChipEffectList.Exists(x => !_prevMasterChipEffectList.Exists(y => y.Id == x.Id));//新しいものがある
        }

        /// <summary>
        /// 効果数が減少したか
        /// </summary>
        private bool IsChangeRemoveEffected()
        {
            if (_prevMasterChipEffectList == null) return false;
            return _prevMasterChipEffectList.Count > MasterChipEffectList.Count;
        }

        /// <summary>
        /// 効果に変動あれば挿入アニメーションを再生する
        /// </summary>
        public void PlayInsertAnimationIfNew()
        {
            PlayInsertAnimation();
            _effectItemList.ForEach(x => x.PlayInsertAnimation());
        }

        /// <summary>
        /// コンテンツ高さが広がってフェードで出現するアニメ再生
        /// </summary>
        private void PlayInsertAnimation()
        {
            if (Math.IsFloatEqual(_bg.rectTransform.rect.height, _animationInsertLayoutElement.minHeight)) return;//目標サイズに変更ないなら不要

            bool isNew = _prevMasterChipEffectList == null;//新規か
            
            _animationInsertLayoutElement.gameObject.SetActiveWithCheck(true);//アニメーション対象ON
            _bgLayoutElement.ignoreLayout = true; //本体の自動レイアウトOFF
            _animationInsertBgCanvasGroup.gameObject.SetActiveWithCheck(!isNew);//初回は本体OFF

            CoroutinePlayInsertAnimation(isNew);
        }

        private void CoroutinePlayInsertAnimation(bool isNew)
        {
            const float INSERT_FADE_DURATION = GameDefine.BASE_FPS_TIME * 4;
            var nextTargetHeight = _bg.rectTransform.rect.height;
            if (nextTargetHeight < _insertAnimationTargetHeight)
            {
                // #148646 枠が小さく縮小するとき、光彩エフェクト中なら停止させる
                StopInsertAnimationCompleteEffect();
            }
            _insertAnimationTargetHeight = nextTargetHeight;
            // 新規効果リスト(枠の拡大中にPt割り振り変わった場合でも、新規効果のエフェクト再生はするために事前にリスト）
            _insertAnimationNewEffectItemList = _effectItemList.FindAll(x => x.IsNew);
            _tweenerInsertAnimation = DOTween.To(
                    () => _animationInsertLayoutElement.minHeight,
                    x =>
                    {
                        _animationInsertLayoutElement.minHeight = x;
                    },
                    _insertAnimationTargetHeight, HEIGHT_ANIMATION_DURATION)
                .SetEase(Ease.OutCirc)
                .OnComplete(() =>
                {
                    // 伸縮完了
                    OnCompletePlayInsertAnimation(_insertAnimationTargetHeight, _insertAnimationNewEffectItemList);
                    // 新規時は全体フェードイン
                    if (isNew)
                    {
                        _animationInsertBgCanvasGroup.alpha = 0;
                        _animationInsertBgCanvasGroup.DOFade(1, INSERT_FADE_DURATION);
                    }

                    _tweenerInsertAnimation = null;
                })
                .OnUpdate(() =>
                {
                    // NOTE:効果の要素数が減少した時Bgのサイズが確定するのが1フレーム遅れる症状があり、ForceRebuildLayoutImmediate()では解決できない
                    if (IsChangeRemoveEffected())
                    {
                        // 現在再生中の目標値と差異が出た場合、再度再生しなおす
                        if (!Math.IsFloatEqual(_bg.rectTransform.rect.height, _insertAnimationTargetHeight))
                        {
                            _tweenerInsertAnimation.Kill();
                            _tweenerInsertAnimation = null;
                            CoroutinePlayInsertAnimation(isNew);
                        }
                    }

                });
            _effectItemList.ForEach(x => x.PlayInsertAnimationAfter(isNew));
        }

        private void OnCompletePlayInsertAnimation(float targetHeight, List<PartsSingleModeScenarioMechaEffectedChipEffectListItem> newItemList)
        {
            _animationInsertLayoutElement.minHeight = targetHeight;
            _animationInsertLayoutElement.gameObject.SetActiveWithCheck(false);
            _animationInsertBgCanvasGroup.gameObject.SetActiveWithCheck(true);
            _bgLayoutElement.ignoreLayout = false;
            // 完了コールバック
            _effectItemList.ForEach(x => x.OnCompletePlayInsertAnimation());
            
            // 新規効果リスト
            if (newItemList.Count >= 2)
            {
                // 複数効果のグループを１つの光彩フレームで再生
                PlayInsertAnimationCompleteEffect(newItemList);
            }
            else
            {
                // 単体の光彩フレームエフェクト再生
                newItemList.ForEach(x => x.PlayInsertAnimationCompleteEffect());
            }
        }

        /// <summary>
        /// 光彩フレームエフェクトを停止
        /// </summary>
        private void StopInsertAnimationCompleteEffect()
        {
            if (_newAnimation.IsPlaying())
            {
                //再生中なら最後まで行った状態で停止
                _newAnimation.Goto(_newAnimation.Duration());
                _newAnimation.Stop();
            }
        }

        /// <summary>
        /// 複数効果を対象に新規効果のフレーム光彩エフェクト再生
        /// </summary>
        private void PlayInsertAnimationCompleteEffect(List<PartsSingleModeScenarioMechaEffectedChipEffectListItem> itemList)
        {
            // 複数効果の中央座標と複数の合計サイズを求める
            Vector2 position = Math.VECTOR2_ZERO;
            Vector2 sizeDelta = Math.VECTOR2_ZERO;
            itemList.ForEach(x =>
            {
                var rectTrans = x.transform as RectTransform;
                sizeDelta.x = rectTrans.sizeDelta.x;
                sizeDelta.y += rectTrans.sizeDelta.y;
            });
            sizeDelta.y += _bgLayoutGroup.spacing * (itemList.Count - 1);//整列スペースを加味
            if (itemList.Count > 0)
            {
                // 先頭アイテムのTOP座標
                var firstRectTrans = itemList[0].transform as RectTransform;
                position = firstRectTrans.anchoredPosition + new Vector2(0, firstRectTrans.sizeDelta.y * 0.5f);
                // 光彩フレームの中心
                position.y -= sizeDelta.y * 0.5f;
            }

            // 座標とサイズ設定
            var rectTrans = _newAnimation.transform as RectTransform;
            rectTrans.anchoredPosition = position;
            rectTrans.sizeDelta = sizeDelta;
            
            //再生
            _newAnimation.Play();
            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_LOGPOSI_07);
        }
        
        /// <summary>
        /// 破棄アニメーション
        /// 縮小してから破棄
        /// </summary>
        public void PlayDestroyAnimation()
        {
            _animationInsertLayoutElement.gameObject.SetActiveWithCheck(true);//アニメーション対象ON
            _animationInsertBgCanvasGroup.gameObject.SetActiveWithCheck(false);//本体OFF
            
            _tweenerDestroyAnimation = DOTween.To(
                    () => _animationInsertLayoutElement.minHeight,
                    x =>
                    {
                        _animationInsertLayoutElement.minHeight = x;
                    },
                    0, HEIGHT_ANIMATION_DURATION)
                .SetEase(Ease.OutCirc)
                .OnComplete(() =>
                {
                    _tweenerDestroyAnimation = null;
                    if(gameObject != null)Destroy(gameObject);
                });
        }
        
        /// <summary>
        /// アニメーションがあるなら完了させる
        /// </summary>
        public void CompleteAnimation()
        {
            CompleteInsertAnimation();
            CompleteDestroyAnimation();
        }
        
        /// <summary>
        /// 挿入アニメーションがあるなら完了させる
        /// </summary>
        private void CompleteInsertAnimation()
        {
            if (_tweenerInsertAnimation == null) return;
            // #148080 _tweenerInsertAnimation.Complete()でTweenManager内で配列外アクセスが発生するので、Completeを使わず自前で完了後の状態にする 
            OnCompletePlayInsertAnimation(_insertAnimationTargetHeight, _insertAnimationNewEffectItemList);
            _tweenerInsertAnimation.Kill();
            _tweenerInsertAnimation = null;
        }
        
        /// <summary>
        /// 破棄アニメーションがあるなら完了させる
        /// </summary>
        private void CompleteDestroyAnimation()
        {
            if (_tweenerDestroyAnimation == null) return;
            _tweenerDestroyAnimation.Complete();
            _tweenerDestroyAnimation = null;
        }
    }
}
