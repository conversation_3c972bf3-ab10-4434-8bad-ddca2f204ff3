using System;
using System.Collections.Generic;
using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// メカ編 ：オーバードライブ発動演出
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioMechaOverdriveSequence : PartsSingleModeParamUpMiniCharaModelBase
    {
        // メッセージ速度
        private const float MESSAGE_ANIMATION_TIME = GameDefine.BASE_FPS_TIME * 3;
        private FlashActionPlayer _flashActionPlayer;
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            RegisterDownloadBase(register);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_EVENT_UP_TRAINING_EFFECT_PATH);
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_TRAINING_LOGPOSI_02);
        }

        /// <summary>
        /// 生成してカットインを再生する
        /// </summary>
        public static void CreateAndPlay(Transform parent, Action onFinish)
        {
            // Scene/Viewのリソースロードを固有のHashに変更して解放準備する
            SingleModeMainViewController.PushSceneResourceHash();

            SetHeaderAndFooterSortingOrder(parent);// ヘッダー・フッターを見えるレイヤーに設定

            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_SEQUENCE);
            var obj = Instantiate(prefab, parent);
            obj.SetLayerRecursively(parent.gameObject.layer);
            var parts = obj.GetComponent<PartsSingleModeScenarioMechaOverdriveSequence>();
            parts.Play(
                UIManager.SingleModeHeader.HpGauge,
                UIManager.SingleModeHeader.MotivationButton,
                onCompleteInitialize: SetPrevHp,//HPゲージを変化前の状態にしておく
                callback: () =>
                {
                    ResetHeaderAndFooterSortingOrder(); // ヘッダー・フッター戻し
                    // リソース解放
                    SingleModeMainViewController.PopSceneResourceHash(false);
                    onFinish?.Invoke();
                });
        }
        
        /// <summary>
        /// ヘッダー・フッターを見えるレイヤーに設定
        /// </summary>
        private static void SetHeaderAndFooterSortingOrder(Transform parent)
        {
            const int HEADER_FOOTER_SORTING_ORDER = 510;
            UIManager.SingleModeHeader.SetButtonEnable(false, false);
            UIManager.SingleModeHeader.SetSortingLayerName(UIManager.SYSTEM_UI_SORTING_LAYER_NAME, UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            UIManager.SingleModeHeader.SetSortingOrder(HEADER_FOOTER_SORTING_ORDER);
            UIManager.SingleModeHeader.gameObject.SetLayerRecursively(LayerMask.NameToLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME));
            UIManager.SingleModeHeader.transform.SetParent(parent);
            
            UIManager.SingleModeFooter.SetButtonEnableFromCutt();
            UIManager.SingleModeFooter.SetSortingLayerName(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            UIManager.SingleModeFooter.SetSortingOrder(HEADER_FOOTER_SORTING_ORDER);
            UIManager.SingleModeFooter.gameObject.SetLayerRecursively(LayerMask.NameToLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME));
            UIManager.SingleModeFooter.transform.SetParent(parent);
        }

        private static void ResetHeaderAndFooterSortingOrder()
        {
            UIManager.SingleModeHeader.SetButtonEnable(true, true);
            UIManager.SingleModeHeader.ResetAllSortSetting();
            
            UIManager.SingleModeFooter.SetButtonInteractable(true);
            UIManager.SingleModeFooter.ResetAllSortSetting();
        }

        /// <summary>
        /// メッセージ速度。SKIP時はメカ編用の調整値。AUTOなど既存速度
        /// </summary>
        private float GetMessagePlayTime(string message = null)
        {
            if (StoryManager.IsHighSpeedMode)
            {
                // SKIP時高速
                return MESSAGE_ANIMATION_TIME;
            }

            if (string.IsNullOrEmpty(message))
            {
                return StaticVariableDefine.SingleMode.TrainingParamChangeA2U.ANIMATION_TIME_TEXT_ONLY;
            }

            var length = TextUtil.RemoveRitchTextTag(message).Length;
            if (length <= TrainingParamChangePlate.TYPEWRITE_TEXT_LENGTH)
            {
                // 1行以内
                return StaticVariableDefine.SingleMode.TrainingParamChangeA2U.ANIMATION_TIME_TEXT_ONLY;
            }
            // #148139
            // SKIPでない場合、テキスト表示はタイプライト処理になるので、タイプライトより短いとタイプライト途中に終了してしまう問題がある
            // そのため2行(21文字)以上の場合は追加の待機時間を加算する
            var overLength = length - TrainingParamChangePlate.TYPEWRITE_TEXT_LENGTH;//20文字以上の数
            const float CHAR_TIME = StaticVariableDefine.SingleMode.TrainingParamChangeA2U.ANIMATION_TIME_TEXT_ONLY / TrainingParamChangePlate.TYPEWRITE_TEXT_LENGTH;//1文字あたりの時間
            var addTime = overLength * CHAR_TIME;
            
            return StaticVariableDefine.SingleMode.TrainingParamChangeA2U.ANIMATION_TIME_TEXT_ONLY + addTime;
        }

        /// <summary>
        /// パラメータリスト生成
        /// </summary>
        protected override List<TrainingParamChangeUI.ChangeParameterInfo> CreateChangeParameterInfoList()
        {
            var chipModelList = SingleModeScenarioMechaBoardModel.GetEffectedOverdriveChipModelList();
            var paramList = new List<TrainingParamChangeUI.ChangeParameterInfo>();
            
            // 基礎効果「オーバードライブ効果が発動した！」
            paramList.Add(new TrainingParamChangeUI.ChangeParameterInfo()
            {
                Type = TrainingParamChangeUI.ParameterType.TextOnly,
                ParamStr = TextId.SingleModeScenarioMecha194072.Text(),
                ParamValue = (int)AudioId.SFX_TRAINING_PARAM_TEXT_CHANGE_UP,
                OnParamChangeStart = OnParamChangeStart,
                ForcedPlayTime = GetMessagePlayTime(),
            });

            // 発動中OD効果
            foreach (var chipModel in chipModelList)
            {
                paramList.AddRange(CreateChangeParameterInfoListByChip(chipModel));
            }
            return paramList;
        }

        /// <summary>
        /// チップのOD効果演出データ生成
        /// </summary>
        private List<TrainingParamChangeUI.ChangeParameterInfo> CreateChangeParameterInfoListByChip(SingleModeScenarioMechaChipModel chipModel)
        {
            var paramList = new List<TrainingParamChangeUI.ChangeParameterInfo>();
            var chipEffectList = chipModel.GetMasterChipEffectListByPointOverdriveType();
            foreach (var chipEffect in chipEffectList)
            {
                var param = CreateChangeParameterInfoByChipEffect(chipEffect);
                if (param == null) continue;
                paramList.Add(param);
            }
            return paramList;
        }
        
        /// <summary>
        /// OD効果別のテキスト演出データ生成
        /// </summary>
        private TrainingParamChangeUI.ChangeParameterInfo CreateChangeParameterInfoByChipEffect(MasterSingleMode09ChipEffect.SingleMode09ChipEffect chipEffect)
        {
            if (chipEffect.IsHpEffectType)
            {
                //体力回復効果
                return new TrainingParamChangeUI.ChangeParameterInfo
                {
                    Type = TrainingParamChangeUI.ParameterType.Hp,
                    Value = GetDeltaHp(),
                    ForcedFlashNone = true,//A2Uなし
                    ForcedPlayTime = GetMessagePlayTime(),
                    OnParamChangeStart = () =>
                    {
                        var hpGauge = SingleModeMainServiceLocator.Instance.Resolve<SingleModeMainHeaderAndFooterController>().HpGauge.HpGaugeFlashPlayer;
                        var mechaHpGauge = hpGauge as SingleModeMainViewHpGaugeA2UScenarioMecha;
                        // 体力回復の演出を呼び出す
                        mechaHpGauge?.PlayHpRecoverAnimation();
                    }
                };
            }
            if (chipEffect.IsMotivationEffectType)
            {
                //やる気効果
                return new TrainingParamChangeUI.ChangeParameterInfo
                {
                    Type = TrainingParamChangeUI.ParameterType.Motivation,
                    Value = GetDeltaMotivation(),
                    ForcedFlashNone = true,//A2Uなし
                    ForcedPlayTime = GetMessagePlayTime(),
                    ParamStr = IsMigraine() ? SingleModeUtils.GetTextWindowMessageLimitMotivationByMigraine() : SingleModeUtils.GetTextWindowMessageMotivationDefault(GetDeltaMotivation()),
                    ParamValue = IsMigraine() ? (int)SingleModeDefine.CharaEffectId.Migraine : 0,
                    OnParamChangeStart = () =>
                    {
                        // ヘッダーのやる気A2Uにオーバードライブエフェクト再生フラグ（実際の再生はA2U内の特定ラベル通過時）
                        var mechaMotivationButton = UIManager.SingleModeHeader.MotivationButton.PlayerBase as SingleModeMainViewMotivationButtonA2UScenarioMecha;
                        mechaMotivationButton.SetOneShotOverdrive();
                    }
                };
            }
            if (chipEffect.EffectTypeEnum == MasterSingleMode09ChipEffect.EffectTypeEnum.TrainingMultiPartner)
            {
                // 分身タイプ効果
                if (WorkDataManager.Instance.SingleMode.ScenarioMecha.HasSubCommandCharaInfo == false)
                {
                    // 分身しなかった場合はテキストを省略する（友人/グルサポ6枚編成時など）
                    return null;
                }
            }
            // テキストのみ
            return new TrainingParamChangeUI.ChangeParameterInfo
            {
                Type = TrainingParamChangeUI.ParameterType.TextOnly,
                ParamStr = SingleModeScenarioMechaUtils.GetChipEffectedMessage(chipEffect),
                ParamValue = (int)AudioId.SFX_TRAINING_PARAM_TEXT_CHANGE_UP,
                ForcedPlayTime = GetMessagePlayTime(SingleModeScenarioMechaUtils.GetChipEffectedMessage(chipEffect)),
            };
        }

        /// <summary>
        /// キャラ効果の片頭痛が発動している
        /// </summary>
        private bool IsMigraine()
        {
            return WorkDataManager.Instance.SingleMode.ChangeParameterInfo.LimitCharaEffectIdList.Exists(x => x == (int)SingleModeDefine.CharaEffectId.Migraine);
        }

        
        /// <summary>
        /// 最初の「オーバードライブ効果が発動した！」の時に追加で行いたい演出
        /// </summary>
        private void OnParamChangeStart()
        {
            PlayA2UText();
        }

        /// <summary>
        /// 「オーバードライブ効果発動！」
        /// </summary>
        private void PlayA2UText()
        {
            _flashActionPlayer = FlashActionPlayer.Load(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_EVENT_UP_TRAINING_EFFECT_PATH, transform);
            const float POSITION_Y = -236;
            _flashActionPlayer.transform.SetLocalPositionY(POSITION_Y);
            _flashActionPlayer.LoadFlashPlayer();
            _flashActionPlayer.gameObject.SetLayerRecursively(gameObject.layer);
            _flashActionPlayer.SetSortLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            _flashActionPlayer.SetSortOffset(OVER_MINI_MODEL_UI_SORTING_ORDER);
            _flashActionPlayer.Play(GetA2UInLabel());
            SetupA2UBoard();
            SetupA2UBurst();
            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_LOGPOSI_02);
        }

        /// <summary>
        /// A2Uラベル番号。in0X, out0Xなどに使用
        /// </summary>
        private int GetA2ULabelNum() => SingleModeScenarioMechaBoardModel.GetEffectedOverdriveChipModelList().Count;
        private string GetA2UInLabel() => TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, GetA2ULabelNum());
        private string GetA2UOutLabel() => TextUtil.Format(GameDefine.A2U_OUT_NUM2_LABEL, GetA2ULabelNum());

        /// <summary>
        /// A2Uの発動コア表示
        /// </summary>
        private void SetupA2UBoard()
        {
            var player = _flashActionPlayer.FlashPlayer;
            var chipModelList = SingleModeScenarioMechaBoardModel.GetEffectedOverdriveChipModelList();
            for (var i = 0; i < chipModelList.Count; i++)
            {
                var chipModel = chipModelList[i];
                var num = i + 1;
                var iconObjName = TextUtil.Format("OBJ_mc_icon{0:D2}/OBJ_mc_dummy_scale/PLN_mc_dummy", num);
                var motObjName = TextUtil.Format("OBJ_mc_icon{0:D2}/MOT_mc_kousai_select00", num);
                var label = TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, chipModel.ChipId);
                player.SetTexture(iconObjName, chipModel.IconTexture);//アイコン
                player.GetMotion(motObjName).SetMotionPlay(label);
            }
        }

        /// <summary>
        /// スーパーオーバードライブ時は「継続発動！」表記を追加
        /// </summary>
        private void SetupA2UBurst()
        {
            var isBurst = WorkDataManager.Instance.SingleMode.ScenarioMecha.OverdriveInfo.IsOverdriveBurst;
            var motion = _flashActionPlayer.FlashPlayer.GetMotion("MOT_mc_turn_count00");
            var player = _flashActionPlayer.FlashPlayer;
            // イリはじめ
            player.SetActionCallBack(GetA2UInLabel(), () =>
            {
                motion.SetMotionPlay(isBurst ? "in00_continue" : GameDefine.A2U_IN00_LABEL);
            }, AnMotionActionTypes.Start);
            
            // 表示変化
            var timingLabel = TextUtil.Format("in_badge{0:D2}", GetA2ULabelNum());
            //motion.SetResetModeType(AnMotion.ResetModeTypes.None);
            player.SetActionCallBack(timingLabel, () =>
            {
                motion.SetMotionPlay(isBurst ? "in_continue" : GameDefine.A2U_IN_LABEL);
            }, AnMotionActionTypes.Start);
        }

        /// <summary> Outアニメ開始時のコールバック。派生先で追加で処理したいことがあればoverride </summary>
        protected override void OnPlayOutFrameAnimation()
        {
            _flashActionPlayer.Play(GetA2UOutLabel());
        }


        private static int GetHp() => WorkDataManager.Instance.SingleMode.Character.Hp;
        private static int GetPrevHp() => GetHp() - GetDeltaHp();

        private static int GetDeltaHp() => WorkDataManager.Instance.SingleMode.ChangeParameterInfo.Hp;
        private static void SetPrevHp() => UIManager.SingleModeHeader.HpGauge.SetValue(GetPrevHp());
        private static int GetDeltaMotivation() => WorkDataManager.Instance.SingleMode.ChangeParameterInfo.Motivation;

        /// <summary> ロググループタイプ </summary>
        protected override SingleModeLogInfoDefine.LogGroupType LogGroupType => SingleModeLogInfoDefine.LogGroupType.MechaOverdrive;
        
        /// <summary> ロググループタイトル </summary>
        protected override string LogGroupTitle => TextId.SingleModeScenarioMecha194032.Text();
    }
}