using System;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Gallop.Live;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Gallop
{
    /// <summary>
    /// 育成追加シナリオ：メカ編
    /// </summary>
    public static class SingleModeScenarioMechaUtils
    {
        #region ダウンロード登録
        
        /// <summary>
        /// メカ編共通ダウンロード登録
        /// </summary>
        public static void RegisterDownloadCommon(DownloadPathRegister register)
        {
            // サウンド
            var soundList = new List<string>
            {
                ResourcePath.SINGLE_MODE_SCENARIO_MECHA_SE_CUE_SHEET_NAME,
            };

#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                // バッチモード対応
                AudioManager.RegisterDownloadByCueSheetsForEditor(register, soundList, AudioManager.SubFolder.Se);
            }
            else
#endif
            {
                AudioManager.Instance.RegisterDownloadByCueSheets(register, soundList, AudioManager.SubFolder.Se);
            }
        }
        
        #endregion
        
        #region メカウマ娘
        
        /// <summary>
        /// メカウマ娘のキャラID＆衣装ID取得
        /// </summary>
        public static CharaDressIdSet GetMechaCharaDressIdSet() => GetMechaCharaDressIdSet(GetMechaScheduleDressId());
        public static CharaDressIdSet GetMechaCharaDressIdSet(SingleModeScenarioMechaDefine.MechaDressPhase dressPhase) => GetMechaCharaDressIdSet(dressPhase.DressId());
        public static CharaDressIdSet GetMechaCharaDressIdSet(int dressId)
        {
            return new CharaDressIdSet(SingleModeScenarioMechaDefine.MECHA_CHARA_ID, dressId);
        }
                
        /// <summary>
        /// メカウマ娘形態別衣装ID取得
        /// </summary>
        public static int DressId(this SingleModeScenarioMechaDefine.MechaDressPhase dressPhase)
        {
            return dressPhase switch
            {
                SingleModeScenarioMechaDefine.MechaDressPhase.First => SingleModeScenarioMechaDefine.MECHA_DRESS_ID1,
                SingleModeScenarioMechaDefine.MechaDressPhase.Second => SingleModeScenarioMechaDefine.MECHA_DRESS_ID2,
                SingleModeScenarioMechaDefine.MechaDressPhase.Third => SingleModeScenarioMechaDefine.MECHA_DRESS_ID3,
                _ => 0,
            };
        }

        /// <summary>
        /// メカウマ娘形態取得
        /// </summary>
        public static SingleModeScenarioMechaDefine.MechaDressPhase GetMechaCharaDressPhase()
        {
            var dressId = GetMechaScheduleDressId();
            return dressId switch
            {
                SingleModeScenarioMechaDefine.MECHA_DRESS_ID1 => SingleModeScenarioMechaDefine.MechaDressPhase.First,
                SingleModeScenarioMechaDefine.MECHA_DRESS_ID2 => SingleModeScenarioMechaDefine.MechaDressPhase.Second,
                SingleModeScenarioMechaDefine.MECHA_DRESS_ID3 => SingleModeScenarioMechaDefine.MechaDressPhase.Third,
                _ => SingleModeScenarioMechaDefine.MechaDressPhase.First,
            };
        }
        
        /// <summary>
        /// メカウマ娘の形態によるアイコンリソースIndexを取得。0～2
        /// </summary>
        public static int GetMechaDressPhaseIconIndex(SingleModeScenarioMechaDefine.MechaDressPhase dressPhase)
        {
            return (int)dressPhase - 1;
        }
        
        /// <summary>
        /// メカウマ娘の衣装ID。スケジュールCSVから取得
        /// CSV設定としてはその回が終了したら形態変化する、なので直近通過したスケジュールを取得
        /// 1回目を経過してないときはnullになるので、デフォルト衣装を返す
        /// UGE開始イベントでメカが形態変化するシナリオの流れのため、同じターンでもシナリオ前後で形態を分ける必要がある
        /// 36ターン目開始時点では第一形態、36ターン目のUGE準備画面～チューニング画面は第２形態へ変化する
        /// </summary>
        private static int GetMechaScheduleDressId()
        {
            var isForceNext = false;
            var playingState = WorkDataManager.Instance.SingleMode.PlayingState;
            if (playingState == SingleModeDefine.PlayingState.MechaRaceTop ||
                playingState == SingleModeDefine.PlayingState.MechaTuning)
            {
                isForceNext = true; // 次のスケジュール基準で形態を考慮
            }
            var schedule = isForceNext ? GetNextMechaSchedule() : GetPrevMechaSchedule();
            return schedule?.DressId ?? SingleModeScenarioMechaDefine.MECHA_DRESS_ID1;
        }

        #endregion
        
        #region アップグレードレース
        
        /// <summary>
        /// 次回開催のアップグレードレースを取得
        /// </summary>
        public static MasterSingleMode09Schedule.SingleMode09Schedule GetNextMechaSchedule()
        {
            return GetNextMechaSchedule(WorkDataManager.Instance.SingleMode.GetCurrentTurn());
        }

        /// <summary>
        /// 次回開催のアップグレードレースを取得
        /// </summary>
        public static MasterSingleMode09Schedule.SingleMode09Schedule GetNextMechaSchedule(int turn)
        {
            return MasterDataManager.Instance.masterSingleMode09Schedule.GetList().Find(a => a.TurnNum >= turn);
        }

        /// <summary>
        /// 前回開催のアップグレードレースを取得
        /// </summary>
        public static MasterSingleMode09Schedule.SingleMode09Schedule GetPrevMechaSchedule()
        {
            var turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            return MasterDataManager.Instance.masterSingleMode09Schedule.GetList().OrderByDescending(x=>x.TurnNum).FirstOrDefault(a => a.TurnNum < turn);
        }

        /// <summary>
        /// 研究完了か。(最後のUGRが終了した)
        /// </summary>
        public static bool IsCompleteResearch() => GetNextMechaSchedule() == null;

        /// <summary>
        /// 研究Lvボーナスターンか。(夏合宿ターン)
        /// </summary>
        public static bool IsStatusLevelBonusTurn() => WorkDataManager.Instance.SingleMode.IsSummerCampTurn();

        /// <summary>
        /// 次のUGEの目標研究Lvに必要な残り研究Lv取得
        /// </summary>
        public static int GetNextMechaScheduleRemainLevel()
        {
            return GetNextMechaScheduleTargetLevel() - WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData.TotalLevel;
        }

        /// <summary>
        /// 次のUGEの目標研究Lv取得
        /// </summary>
        public static int GetNextMechaScheduleTargetLevel()
        {
            return GetNextMechaSchedule()?.TargetPt ?? 0;
        }
        
        /// <summary>
        /// チューニング可能か。（サーバー制御：アップグレードレース後true、チューニング確定でfalse）
        /// </summary>
        public static bool IsTuningEnable()
        {
            return WorkDataManager.Instance.SingleMode.PlayingState == SingleModeDefine.PlayingState.MechaTuning;
        }
        
        #endregion

        #region 研究Lv
        
        /// <summary>
        /// 研究LvのUIインデックス対応するコマンド取得
        /// </summary>
        public static TrainingDefine.TrainingCommandId IndexToStatusType(int index)
        {
            return index switch
            {
                0=> TrainingDefine.TrainingCommandId.Turf,
                1=> TrainingDefine.TrainingCommandId.Pool,
                2=> TrainingDefine.TrainingCommandId.Dirt,
                3=> TrainingDefine.TrainingCommandId.Slope,
                4=> TrainingDefine.TrainingCommandId.Study,
                _=> TrainingDefine.TrainingCommandId.Turf
            };
        }
        
        /// <summary>
        /// 研究Lvのコマンドに対応するUIインデックス取得
        /// </summary>
        public static int StatusTypeToIndex(TrainingDefine.TrainingCommandId type)
        {
            return type switch
            {
                TrainingDefine.TrainingCommandId.Turf => 0 ,
                TrainingDefine.TrainingCommandId.Pool => 1,
                TrainingDefine.TrainingCommandId.Dirt => 2,
                TrainingDefine.TrainingCommandId.Slope => 3,
                TrainingDefine.TrainingCommandId.Study => 4,
                _=> 0
            };
        }
        public static int StatusTypeToIndex(SingleModeScenarioMechaDefine.EventGainStatusType statusType)
        {
            return statusType switch
            {
                SingleModeScenarioMechaDefine.EventGainStatusType.SpeedLv => 0,
                SingleModeScenarioMechaDefine.EventGainStatusType.StaminaLv => 1,
                SingleModeScenarioMechaDefine.EventGainStatusType.PowLv => 2,
                SingleModeScenarioMechaDefine.EventGainStatusType.GutsLv => 3,
                SingleModeScenarioMechaDefine.EventGainStatusType.WizLv => 4,
                _ => 0,
            };
        }
        
        /// <summary>
        /// 研究Lv名
        /// </summary>
        public static string Text(this SingleModeScenarioMechaDefine.EventGainStatusType statusType)
        {
            return statusType switch
            {
                SingleModeScenarioMechaDefine.EventGainStatusType.SpeedLv => TextId.SingleModeScenarioMecha194012.Text(),
                SingleModeScenarioMechaDefine.EventGainStatusType.StaminaLv => TextId.SingleModeScenarioMecha194013.Text(),
                SingleModeScenarioMechaDefine.EventGainStatusType.PowLv => TextId.SingleModeScenarioMecha194014.Text(),
                SingleModeScenarioMechaDefine.EventGainStatusType.GutsLv => TextId.SingleModeScenarioMecha194015.Text(),
                SingleModeScenarioMechaDefine.EventGainStatusType.WizLv => TextId.SingleModeScenarioMecha194016.Text(),
                _ => string.Empty,
            };
        }
        
        /// <summary> 〇〇の研究Lv </summary>
        public static string TextLv(this SingleModeScenarioMechaDefine.EventGainStatusType statusType) 
            => TextId.SingleModeScenarioMecha194018.Format(statusType.Text());
        
        /// <summary> 〇〇の研究Lv上限 </summary>
        public static string TextLvLimit(this SingleModeScenarioMechaDefine.EventGainStatusType statusType) 
            => TextId.SingleModeScenarioMecha194019.Format(statusType.Text());
        
        /// <summary>
        /// 研究進捗度名
        /// </summary>
        public static string Text(this SingleModeScenarioMechaDefine.ProgressResultType progressResultType)
        {
            return progressResultType switch
            {
                SingleModeScenarioMechaDefine.ProgressResultType.GreatSuccess => TextId.SingleModeScenarioMecha194008.Text(),
                SingleModeScenarioMechaDefine.ProgressResultType.SuperSuccess => TextId.SingleModeScenarioMecha194009.Text(),
                _ => string.Empty,
            };
        }
        #endregion
        
        #region オーバードライブ
        
        /// <summary>
        /// オーバードライブ発動不可の通知テキスト取得
        /// 発動可能の場合null
        /// </summary>
        public static string GetOverdriveDisableNotification(WorkSingleModeScenarioMecha.WorkOverdriveInfo info)
        {
            // NOTE:OD効果に体力回復＆やる気があることから、目標レース時などトレーニングできない状況でもOD発動できてよい

            if (WorkDataManager.Instance.SingleMode.HomeInfo.IsShowTazunaAtHomeTop())
            {
                // 育成キャラ不在のとき（育成TOPにタズナしかいない）OD発動不可
                return TextId.SingleModeScenarioMecha194088.Text();
            }
            
            if (info.CanOverDriveState() == false)
            {
                return TextId.SingleModeScenarioMecha194042.Text();
            }
            else if (info.IsOverdriveBurst)
            {
                // URAF期間で常時オーバードライブ使用可能
                return null;
            }
            else if (info.CanOverDriveRemainNnum() == false)
            {
                return TextId.SingleModeScenarioMecha194043.Text();
            }
            return null;
        }

        /// <summary>
        /// オーバードライブ発動可能か
        /// </summary>
        public static bool CanUseOverdrive(WorkSingleModeScenarioMecha.WorkOverdriveInfo info)
        {
            return string.IsNullOrEmpty(GetOverdriveDisableNotification(info));
        }
        
        #endregion
        
        #region チップ効果
        
        /// <summary> チップ効果詳細 </summary>
        public static string GetChipEffectedDetail(MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterChipEffect)
        {
            return GetChipEffectedDetail(masterChipEffect, TextUtil.GetMasterText(MasterString.Category.SingleModeScenarioMechaChipEffectDetail, masterChipEffect.Id));
        }
            
        /// <summary> チップ効果詳細（狭いUI用） </summary>
        public static string GetChipEffectedDetailNarrow(MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterChipEffect)
        {
            return GetChipEffectedDetail(masterChipEffect, TextUtil.GetMasterText(MasterString.Category.SingleModeScenarioMechaChipEffectDetailNarrow, masterChipEffect.Id));
        }

        /// <summary> チップ効果発動メッセージ </summary>
        public static string GetChipEffectedMessage(MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterChipEffect)
        {
            return GetChipEffectedMessage(masterChipEffect, TextUtil.GetMasterText(MasterString.Category.SingleModeScenarioMechaChipEffectMessage, masterChipEffect.Id));
        }

        /// <summary>
        /// 現在効果量を考慮した詳細に変換
        /// </summary>
        private static string GetChipEffectedDetail(MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterChipEffect, string detail)
        {
            switch (masterChipEffect.EffectTypeEnum)
            {
                case MasterSingleMode09ChipEffect.EffectTypeEnum.OverdriveNum:// オーバードライブの発動回数に応じてトレーニング効果アップ（{1}回発動 現在の効果量+{0}%）
                case MasterSingleMode09ChipEffect.EffectTypeEnum.LearningLevel:// 〇〇トレーニング効果が研究Lvに応じて上昇する (現在Lv{1} 現在の上昇量+{0}）
                    return TextUtil.Format(detail, GetChipEffectedValue(masterChipEffect), GetChipEffectedBaseValue(masterChipEffect.EffectTypeEnum));
            }
            return detail;
        }
            
        /// <summary>
        /// 現在効果量を考慮したメッセージに変換
        /// 発動メッセージには効果量のみ必要で。要因値は不要。
        /// </summary>
        private static string GetChipEffectedMessage(MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterChipEffect, string detail)
        {
            switch (masterChipEffect.EffectTypeEnum)
            {
                case MasterSingleMode09ChipEffect.EffectTypeEnum.OverdriveNum:// オーバードライブの発動回数に応じてトレーニング効果アップ（{1}回発動 現在の効果量+{0}%）
                case MasterSingleMode09ChipEffect.EffectTypeEnum.LearningLevel:// 〇〇トレーニング効果が研究Lvに応じて上昇する (現在Lv{1} 現在の上昇量+{0}）
                    return TextUtil.Format(detail, GetChipEffectedValue(masterChipEffect));
            }
            return detail;
        }
        
        /// <summary>
        /// 現在のプレイ状況に合わせた効果量を取得
        /// </summary>
        private static float GetChipEffectedValue(MasterSingleMode09ChipEffect.SingleMode09ChipEffect masterChipEffect)
        {
            switch (masterChipEffect.EffectTypeEnum)
            {
                case MasterSingleMode09ChipEffect.EffectTypeEnum.OverdriveNum:
                    // オーバードライブの発動回数に応じてトレーニング効果アップ（{1}回発動 現在の効果量+{0}%）
                    return Math.MasterInt2FloatPercent(masterChipEffect.EffectValue2) * GetChipEffectedBaseValue(masterChipEffect.EffectTypeEnum);
                case MasterSingleMode09ChipEffect.EffectTypeEnum.LearningLevel:
                    // 〇〇トレーニング効果が研究Lvに応じて上昇する (現在Lv{1} 現在の上昇量+{0}）
                    return MasterDataManager.Instance.masterSingleMode09TrainingBoost.GetBoostValue(masterChipEffect.EffectValue4, GetChipEffectedBaseValue(masterChipEffect.EffectTypeEnum));
            }
            return 0;
        }
        
        /// <summary>
        /// 効果量の元になる要因値を取得
        /// </summary>
        private static int GetChipEffectedBaseValue(MasterSingleMode09ChipEffect.EffectTypeEnum effectType)
        {
            switch (effectType)
            {
                case MasterSingleMode09ChipEffect.EffectTypeEnum.OverdriveNum:
                    // オーバードライブの発動回数に応じてトレーニング効果アップ（{1}回発動 現在の効果量+{0}%）
                    return WorkDataManager.Instance.SingleMode.ScenarioMecha.OverdriveInfo.OverdriveCount;
                case MasterSingleMode09ChipEffect.EffectTypeEnum.LearningLevel:
                    // 〇〇トレーニング効果が研究Lvに応じて上昇する (現在Lv{1} 現在の上昇量+{0}）
                    return WorkDataManager.Instance.SingleMode.ScenarioMecha.RivalData.TotalLevel;
            }
            return 0;
        }
        
        #endregion

        #region シナリオリンク効果

        /// <summary>
        /// 発動中のシナリオリンク効果値を取得
        /// </summary>
        public static int GetLinkEffectValue(MasterSingleMode09ChipEffect.EffectTypeEnum effectType)
        {
            var effectValue = 0;
            foreach (var linkEffect in GetLinkEffectArray())
            {
                if (linkEffect.EffectType == (int)effectType)
                {
                    effectValue += linkEffect.EffectValue2;
                }
            }

            return effectValue;
        }

        /// <summary>
        /// 発動するシナリオリンク効果一覧を取得
        /// </summary>
        private static List<MasterSingleMode09LinkEffect.SingleMode09LinkEffect> GetLinkEffectArray()
        {
            var masterLinkEffect = MasterDataManager.Instance.masterSingleMode09LinkEffect;
            var charaData = WorkDataManager.Instance.SingleMode.Character;
            var equipSupportCardArray = WorkDataManager.Instance.SingleMode.Character.EquipSupportCardArray;
            var linkBonusArray = new List<MasterSingleMode09LinkEffect.SingleMode09LinkEffect>();

            linkBonusArray.AddRange(masterLinkEffect.GetListWithCharaId(charaData.CharaId));
            foreach (var equipSupportCard in equipSupportCardArray)
            {
                // グルサポ以外はサポカのキャラIDからリンク効果を取得
                if (equipSupportCard.MasterSupportCardData.SupportCardType == (int)MasterSupportCardData.CardType.Group)
                {
                    foreach (var charaId in equipSupportCard.MasterSupportCardData.GetCharaIdList())
                    {
                        var groupLinkEffectArray = masterLinkEffect.GetListWithCharaId(charaId);
                        linkBonusArray.AddRange(groupLinkEffectArray);
                    }
                    continue;
                }

                var supportLinkEffectArray = masterLinkEffect.GetListWithCharaId(equipSupportCard.CharaId);
                linkBonusArray.AddRange(supportLinkEffectArray);
            }

            return linkBonusArray;
        }

        #endregion
        
        #region ターン
        
        /// <summary>
        /// EX期間の最初のターンか
        /// </summary>
        public static bool IsFinalFirstTurn()
        {
            var masterTurn = WorkDataManager.Instance.SingleMode.GetCurrentMasterTurn();
            if( SingleModeUtils.IsDegreeTypeFinalTurn() &&
                masterTurn.Month == SingleModeDefine.JANUARY && 
                masterTurn.Half == (int)SingleModeDefine.HalfOfMonth.First)
            {
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// EX期間の2ターン以降か
        /// </summary>
        public static bool IsFinalSecondTurn()
        {
            return SingleModeUtils.IsDegreeTypeFinalTurn() && !IsFinalFirstTurn();
        }
        
        #endregion
        
        #region 演出関連

        /// <summary>
        /// フェードアウト
        /// </summary>
        public static void FadeOut(Action onFinish)
        {
            const float FADE_TIME_OUT = GameDefine.BASE_FPS_TIME * 6f;
            FadeManager.Instance.FadeOut(GameDefine.COLOR_WHITE, FADE_TIME_OUT, onFinish, fadeCanvasType: FadeManager.FadeCanvasType.SystemCanvas, easeType: Ease.OutCubic);
        }
        
        /// <summary>
        /// フェードイン
        /// </summary>
        public static void FadeIn()
        {
            const float FADE_TIME_IN = GameDefine.BASE_FPS_TIME * 5f;
            FadeManager.Instance.FadeIn(FADE_TIME_IN, fadeCanvasType: FadeManager.FadeCanvasType.SystemCanvas, easeType: Ease.InOutCubic);
        }
        
        /// <summary>
        /// UGEカット終了時のフェード明け
        /// </summary>
        public static void FadeInOnEndUgeCutt()
        {
            const float FADE_TIME_IN = GameDefine.BASE_FPS_TIME * 5f;
            FadeManager.Instance.FadeIn(FADE_TIME_IN, fadeCanvasType: FadeManager.FadeCanvasType.SystemCanvas);
        }
        
        #endregion
        
        #region ライブ

        /// <summary>
        /// 育成イベントからのエンディングライブ遷移にはライブ再生確認ダイアログを出す
        /// ※初回は確認ダイアログなしでライブへ
        /// </summary>
        public static void ChangeEndingLiveViewFromStoryView()
        {
            var isNewMusic = !HasEndingMusic();
            if (isNewMusic)
            {
                // 新規楽曲なら再生確認せずにライブ再生へ
                ChangeEndingLiveView();
                return;
            }

            // ライブ再生確認ダイアログ
            DialogSingleModeScenarioMechaEndLiveConfirm.PushDialog(
                () =>
                {
                    // 縦持ち横持ちの確認ダイアログを出す
                    DialogLiveStartConfirm.PushDialog(SingleModeScenarioMechaDefine.LIVE_MUSIC_ID, false,
                        () =>
                        {
                            DialogManager.RemoveAllDialog(ChangeEndingLiveView);
                        });
                },
                () =>
                {
                    DialogManager.RemoveAllDialog(ChangeEndingLiveView);
                });
        }
        
        /// <summary>
        /// エンディングライブ遷移（シナリオ専用API、視聴API、ライブ画面、楽曲獲得、育成へ戻る）
        /// ※初回でない場合は再生確認ダイアログで再生するフラグがない場合はplaying_state変更後に育成TOPへ遷移する
        /// </summary>
        public static void ChangeEndingLiveView()
        {
            // 新規楽曲か
            var isNewMusic = !HasEndingMusic();

            // ライブ再生が必要か（新規楽曲 or 確認ダイアログで再生するを選んだ）
            var isNeedForcePlayLive = isNewMusic || TempData.Instance.SingleModeData.Mecha.IsSelectedPlayLive;
            TempData.Instance.SingleModeData.Mecha.IsSelectedPlayLive = false;//選択フラグ折り

            // メカ編API、planing_state変更
            SingleModeMechaAPI.SendMechaEndingLive(OnCompleteMechaApi);
            
            // メカ編API完了
            void OnCompleteMechaApi()
            {
                if (isNeedForcePlayLive)
                {
                    // ライブ再生前にライブ視聴API
                    SendLiveStartApi();
                }
                else
                {
                    // 育成へ
                    ChangeMainView();
                }
            }
            
            // ライブ視聴API(ミッション用)
            void SendLiveStartApi()
            {
                SetupLiveSettings();
                LiveUtil.SendLiveStartApi(SingleModeScenarioMechaDefine.LIVE_MUSIC_ID, OnCompleteLiveStartApi, true);
            }
            
            // ライブ視聴API完了
            void OnCompleteLiveStartApi()
            {
                // ライブ画面へ
                ChangeLiveView(OnCompleteLiveView);
            }

            // ライブ画面終了後
            void OnCompleteLiveView()
            {
                var isGetMusic = HasEndingMusic();
                // 新規楽曲を獲得したら
                if (isNewMusic && isGetMusic)
                {
                    // 楽曲獲得ダイアログ ⇒ 育成へ
                    DialogGetLiveMusic.Open(SingleModeScenarioMechaDefine.LIVE_MUSIC_ID, ChangeMainView);
                }
                else
                {
                    // 育成へ
                    ChangeMainView();
                }
            }
        }

        /// <summary>
        /// エンディング楽曲を所持しているか
        /// </summary>
        private static bool HasEndingMusic()
        {
            return WorkDataManager.Instance.MusicData.Get(SingleModeScenarioMechaDefine.LIVE_MUSIC_ID) != null;
        }

        /// <summary>
        /// ライブ画面へ
        /// </summary>
        private static void ChangeLiveView(Action onEndLiveCallback = null)
        {
            var liveViewInfo = new LiveViewController.ViewInfo { OnComplete = onEndLiveCallback };
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.Live, liveViewInfo, onEndLiveCallback);
        }
        
        /// <summary>
        /// 育成TOPへ
        /// </summary>
        private static void ChangeMainView()
        {
            SingleModeChangeViewManager.Instance.ChangeMainView();
        }

        /// <summary>
        /// ライブ設定：メンバー選出、衣装選定
        /// </summary>
        public static List<Director.LiveLoadSettings.CharacterInfo> SetupLiveSettings()
        {
            var loadSettings = Director.LoadSettings;
            loadSettings.Initialize();
            loadSettings.MusicId = SingleModeScenarioMechaDefine.LIVE_MUSIC_ID;
            
            // 育成ウマ娘（衣装変更有効）
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            loadSettings.CharacterInfoList.Add(
                Director.LiveLoadSettings.CharacterInfo.Create(
                    workChara.CharaId,
                    ModelLoader.MOB_ID_NULL,
                    workChara.GetRaceDressId(true),
                    ModelLoader.DRESS_COLOR_ID_DEFAULT));
            
            // メカウマ娘
            var mechaCharaDressIdSet = GetMechaCharaDressIdSet();
            loadSettings.CharacterInfoList.Add(
                Director.LiveLoadSettings.CharacterInfo.Create(
                    mechaCharaDressIdSet.CharaId,
                    ModelLoader.MOB_ID_NULL,
                    mechaCharaDressIdSet.DressId,
                    ModelLoader.DRESS_COLOR_ID_DEFAULT));
            
            // シナリオリンク
            var specialCharaList = MasterDataManager.Instance.masterSingleModeSpecialChara.GetListWithScenarioIdOrderByIdAsc((int)SingleModeDefine.ScenarioId.Mecha).
                OrderBy(x => StaticVariableDefine.SingleMode.SingleModeScenarioMechaDefine.ENDING_LIVE_CHARA_SORT.FindIndex(charaId => charaId == x.CharaId) );
            foreach (var specialChara in specialCharaList)
            {
                var charaId = specialChara.CharaId;
                var specialCharaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
                // 友人など除外（メカ編ではいないが、保険）
                if (specialCharaData.CharaCategory != 0) continue;

                if (workChara.CharaId == charaId)
                {
                    // シナリオリンクを育成している場合、協力者の中からランダムで1人追加
                    AddRandomResearchMember();
                }
                else
                {
                    loadSettings.CharacterInfoList.Add(CreateInfo(charaId));
                }
            }

            return loadSettings.CharacterInfoList;
            
            // 協力者の中からランダムで1人追加
            void AddRandomResearchMember()
            {
                var charaIds = workChara.EvaluationList
                    .Where(evaluation => evaluation.IsMechaResearchMember)//研究メンバーのみ
                    .Select(evaluation => SingleModeUtils.GetCharaIdByAllPosition(evaluation.TargetId))//キャラIDに変換
                    .Where(charaId => 
                        !loadSettings.CharacterInfoList.Exists(info => info.CharaId == charaId) && //まだメンバーに選定されていない
                        !specialCharaList.Any(specialChara => specialChara.CharaId == charaId) && //シナリオリンクメンバー以外から
                        MasterDataManager.Instance.masterCharaData.Get(charaId).CharaCategory == 0).ToList();//ウマ娘
                if (charaIds.Any())
                {
                    var charaId = charaIds[Random.Range(0, charaIds.Count)];//1人抽選
                    loadSettings.CharacterInfoList.Add(CreateInfo(charaId));
                }
                else
                {
                    Debug.LogError("ライブメンバー抽選に失敗しました。シナリオリンクキャラの代わりのメンバーが抽選できませんでした。エンジニアにご連絡お願いします");
                }
            }

            // local func:Info生成
            Director.LiveLoadSettings.CharacterInfo CreateInfo(int charaId)
            {
                int order = loadSettings.CharacterInfoList.Count + 1;//着順配置番号
                var (dressId, dressColor) = SingleModeUtils.GetLiveDressIdAndDressColorId(loadSettings.MusicId, charaId, order);
                return Director.LiveLoadSettings.CharacterInfo.Create(
                    charaId,
                    ModelLoader.MOB_ID_NULL,
                    dressId,
                    dressColor);
            }
        }
        
        #endregion
        
        #region 背景
       
        /// <summary>
        /// 背景エフェクトを画面比率スケール値取得
        /// </summary>
        public static float GetBgEffectScale()
        {
            // アニデザ指定の画面比率別エフェクトスケール
            const float SCALE_3_4 = 1f;
            const float SCALE_9_16 = 0.82f;
            const float SCALE_9_19 = 1f;
            const float SCALE_9_21 = 1.075f;
            
            float screenAspect = GallopUtil.GetScreenAspect();// w / h

            // 3:4よりワイド
            if (screenAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_4_3)
            {
                return SCALE_3_4;
            }
            // 3:4　～　9:16
            if (screenAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_16_9)
            {
                return Math.MapClamp(screenAspect, GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_4_3, GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_16_9, SCALE_3_4, SCALE_9_16);
            }
            // 9:16　～　9:19.5
            if (screenAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL)
            {
                return Math.MapClamp(screenAspect, GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_16_9, GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL, SCALE_9_16, SCALE_9_19);
            }
            // 9:19.5　～　9:21　
            if (screenAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_21_9)
            {
                return Math.MapClamp(screenAspect, GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL, GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_21_9, SCALE_9_19, SCALE_9_21);
            }
            // 9:21　～
            return SCALE_9_21;
        }
        
        #endregion
    }
}
