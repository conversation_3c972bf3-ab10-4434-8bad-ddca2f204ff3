using System.Collections;
using Gallop.SingleModeAutoPlay;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成追加シナリオ：Cook編
    /// </summary>
    public static class SingleModeScenarioCookUtils
    {

        /// <summary>
        /// 畑画面へ遷移
        /// </summary>
        public static void GotoGardenView()
        {
            var currentViewId = SceneManager.Instance.GetCurrentViewId();
            switch (currentViewId)
            {
                // 育成TOPから遷移する場合はAdditiveView
                case SceneDefine.ViewId.SingleModeMain:
                case SceneDefine.ViewId.SingleModeScenarioCookTastingTop:
                    {
                        // トレーニング画面がアクティブな状態で畑に遷移する場合は、戻り先をトレーニング画面に設定する
                        if (SingleModeMainServiceLocator.HasInstance())
                        {
                            // トレーニングフッターが有効 & StablePanelが無効 であればトレーニング画面から畑へ遷移したと判定する
                            var singleModeMainBaseView = SingleModeMainServiceLocator.Instance.Resolve<ISingleModeMainBaseView>();
                            var trainingView = SingleModeMainServiceLocator.Instance.Resolve<ISingleModeMainViewTraining>();
                            if ((trainingView != null && trainingView.TrainingFooter.gameObject.activeSelf) &&
                                (singleModeMainBaseView != null && !singleModeMainBaseView.StablesPanel.gameObject.activeSelf))
                            {
                                var singleModeMainViewController = SceneManager.Instance.GetCurrentViewController<SingleModeMainViewController>();
                                if (singleModeMainViewController != null)
                                {
                                    singleModeMainViewController.IsBackTrainingFromAdditiveView = true;
                                }
                            }
                        }

                        // 畑画面へ遷移
                        AdditiveGardenView();
                    }
                    break;

                default:
                    ChangeGardenView();
                    break;
            }
        }

        private static void AdditiveGardenView()
        {
            // ダイアログを閉じる
            DialogManager.RemoveAllDialog();

            SceneManager.Instance.AdditiveView(SceneDefine.ViewId.SingleModeScenarioCookGarden);
        }

        private static void ChangeGardenView()
        {
            // ChangeViewの場合は蹄鉄ロードを挟む
            UIManager.LoadingCanvas.NowLoading.Show(NowLoading.Type.NowLoadingPlain, () =>
            {
                // ダイアログを閉じる
                DialogManager.RemoveAllDialog();

                SceneManager.Instance.ChangeView(SceneDefine.ViewId.SingleModeScenarioCookGarden);
            });
        }


    }

    /// <summary>
    /// 料理を実行するService
    /// </summary>
    public static class SingleModeScenarioCookExecCookService
    {
        class PrevCharaData
        {
            public int PrevHP { get; private set; }
            public int PrevMaxHp { get; private set; }
            public RaceDefine.Motivation PrevMotivation { get; private set; }

            public PrevCharaData(WorkSingleModeCharaData workSingleModeCharaData)
            {
                PrevHP = workSingleModeCharaData.Hp;
                PrevMaxHp = workSingleModeCharaData.MaxHp;
                PrevMotivation = workSingleModeCharaData.Motivation;
            }
        }
        /// <summary>
        /// 料理を実行
        /// </summary>
        public static void ExecCook(SingleModeScenarioCookDishModel dishModel, bool isReSelectableDish, System.Action onCompleteCookDish = null, System.Action onCancel = null)
        {
            // 確認ダイアログを表示
            PushDialogSingleModeScenarioCookConfirmCookDish(dishModel, isReSelectableDish, (confirmDishModel, isSkip) => 
            {
                // 演出へ
                UIManager.Instance.StartCoroutine(PlayCookingCut(confirmDishModel, isSkip, onCompleteCookDish));
            }, onCancel);
        }

        /// <summary>
        /// 確認ダイアログを表示
        /// </summary>
        private static void PushDialogSingleModeScenarioCookConfirmCookDish(SingleModeScenarioCookDishModel dishModel, bool isReSelectableDish, System.Action<SingleModeScenarioCookDishModel, bool> onDecide, System.Action onCancel)
        {
            DialogSingleModeScenarioCookConfirmCookDish.PushDialog(
                dishModel,
                isShowSelectButton: isReSelectableDish, // 確認ダイアログで実行する料理を選びなおせる
                onDecide,
                onCancel);
        }

        /// <summary>
        /// 料理実行演出を開始
        /// </summary>
        private static IEnumerator PlayCookingCut(SingleModeScenarioCookDishModel dishModel, bool isSkip, System.Action onCompleteCookDish)
        {
            // API実行前のステータスを保持
            var prevCharaData = new PrevCharaData(WorkDataManager.Instance.SingleMode.Character);
            
            DialogManager.RemoveAllDialog();

            var completeGetResponse = false;
            var completeWipeIn = false;

            // APIを実行
            SingleModeCookAPI.SendCookRequest(dishModel.Master.Id, SingleModeAutoPlayAgent.Instance.ExecAutoPlayPlanId, () => { completeGetResponse = true; });
            
            NowloadingSingleModeScenarioCookWipe.Show(onCompleteShowWipe: () => completeWipeIn = true, isSetSystemUILayer: false);

            // ワイプで画面が隠れるかつ通信が完了するのを待ってから作る演出を流す
            yield return new WaitUntil(() => completeGetResponse && completeWipeIn);

            OnCompleteShowWipe(dishModel, isSkip, onCompleteCookDish, prevCharaData);
        }

        private static void OnCompleteShowWipe(SingleModeScenarioCookDishModel dishModel, bool isSkip, System.Action onCompleteCookDish, PrevCharaData prevCharaData)
        {
            if (SingleModeMainServiceLocator.HasInstance())
            {
                // HPゲージの予想値リセット
                var headerAndFooterController = SingleModeMainServiceLocator.Instance.Resolve<SingleModeMainHeaderAndFooterController>();
                headerAndFooterController.HpGauge.Setup();
            }

            // HPゲージ/やる気表示更新
            UIManager.SingleModeHeader?.HpGauge.Setup(prevCharaData.PrevHP, prevCharaData.PrevMaxHp);
            UIManager.SingleModeHeader?.MotivationButton.Setup(prevCharaData.PrevMotivation);

                
            // カットを再生
            var cuttPlayer = new PartsSingleModeScenarioCookCookingCutRunner();
            cuttPlayer.Play(dishModel.Master, prevCharaData.PrevHP, prevCharaData.PrevMaxHp, prevCharaData.PrevMotivation, isSkip: isSkip);

            onCompleteCookDish?.Invoke();
        }

    }


}