using UnityEngine;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioCookDishDirectoryListItem : MonoBehaviour
    {
        [SerializeField] private ImageCommon _icon;
        [SerializeField] private ButtonCommon _button;
        [SerializeField] private GameObject _newIcon;

        private SingleModeScenarioCookDishDirectoryItemModel _directoryItemModel;
        private System.Action<PartsSingleModeScenarioCookDishDirectoryListItem, SingleModeScenarioCookDishDirectoryItemModel> _onClick;

        public void Setup(SingleModeScenarioCookDishDirectoryItemModel directoryItemModel, System.Action<PartsSingleModeScenarioCookDishDirectoryListItem, SingleModeScenarioCookDishDirectoryItemModel> onClick)
        {
            _directoryItemModel = directoryItemModel;
            _onClick = onClick;

            _icon.sprite = directoryItemModel.IconSprite;
            _icon.color = directoryItemModel.IconColor;
            _newIcon.SetActive(directoryItemModel.IsNew);
            _button.SetOnClick(() => OnClickButton());
            gameObject.SetActive(directoryItemModel.IsActive);
        }

        private void OnClickButton()
        {
            _onClick?.Invoke(this, _directoryItemModel);
        }

    }
}