using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class PartsSingleModeGainPointItem : MonoBehaviour
    {
        [SerializeField] private CountupModifier _pointCountUpModifier;
        [SerializeField] private BitmapTextCommon _pointCountUpModifierText;
        [SerializeField] private TextCommon _friendPointText;
        [SerializeField] private BitmapTextCommon _pointSuffix;
        [SerializeField] private TextCommon _gainValueLabel;
        [SerializeField] private TextCommon _gainValueLabelInURAF;
        [SerializeField] private HorizontalLayoutGroup _horizontalLayoutGroup;

        [SerializeField] private BitmapTextCommon _denominatorPoint;
        [SerializeField] private TextCommon _targetPointText;
        [SerializeField] private GameObject _slash;

        public float Duration => _pointCountUpModifier.Duration;

        private int _toValue;
        private System.Action _onCompleteCountUp;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_FONT_ROOT + "ApprovalPt/ApprovalPt");
        }

        public void Setup(int prevValue, int toValue, bool showDenominator, int denominatorValue = 0)
        {
            _toValue = toValue;

            var gainValue = toValue - prevValue;
            _friendPointText.text = toValue.ToCommaSeparatedString();　// レイアウト調整用に透明なテキストに現在のお料理Ptを代入する
            _gainValueLabel.text = _gainValueLabelInURAF.text = TextId.SingleModeScenarioArc508017.Format(TextUtil.ToStringChange(gainValue)); // (+XXpt)
            // 分母がない時（URAF期間）は中央に加算値を持ってくる
            _gainValueLabel.SetActiveWithCheck(showDenominator);
            _gainValueLabelInURAF.SetActiveWithCheck(!showDenominator);

            SetupDenominator(showDenominator, denominatorValue);

            // カウントアップ後の値でLayoutを確定させる
            _pointCountUpModifier.SetValue(toValue, true);
            LayoutRebuilder.ForceRebuildLayoutImmediate((RectTransform)_horizontalLayoutGroup.transform);
            _horizontalLayoutGroup.enabled = false;

            // カウントアップ前の値で初期化
            _pointCountUpModifier.SetValue(prevValue, true);

            // カウントアップ後の値でTimeScaleで再生開始することで、桁数による表示ズレを抑制する
            _pointCountUpModifier.TimeScale = 0f;
            _pointCountUpModifier.SetValue(toValue);
            _pointCountUpModifier.Play();

            _pointCountUpModifierText.LoadFont();
            _pointSuffix.LoadFont();
        }

        /// <summary>
        /// 分母表示
        /// </summary>
        private void SetupDenominator(bool showNextRouteTarget, int denominatorValue)
        {
            _slash.SetActive(showNextRouteTarget);
            _denominatorPoint.SetActiveWithCheck(showNextRouteTarget);
            _targetPointText.SetActiveWithCheck(showNextRouteTarget);
            if (showNextRouteTarget)
            {
                _denominatorPoint.text = denominatorValue.ToCommaSeparatedString();
                _targetPointText.text = denominatorValue.ToCommaSeparatedString();// レイアウト調整用に透明なテキストに目標のお料理Ptを代入する
            }
        }

        public void PlayPointCountUp(System.Action onComplete = null)
        {
            _onCompleteCountUp = onComplete;

            _pointCountUpModifier.TimeScale = 1f;
            _pointCountUpModifier.SetValue(_toValue);
            _pointCountUpModifier.Play(_onCompleteCountUp);
        }

        public void SkipPointCountUp()
        {
            _pointCountUpModifier.SetValue(_toValue, true);
        }

        public void SetActiveGainValueLabel(bool isActive)
            => _gainValueLabel.SetActiveWithCheck(isActive);

    }
}