using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static ResourcePath;

    /// <summary>
    /// Cook編料理作成確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioCookConfirmCookDish : DialogInnerBase
    {
        //おすすめ料理作成確認ダイアログ
        private const string DIALOG_PATH = SINGLE_MODE_SCENARIO_COOK_PARTS_UI_ROOT + "DialogSingleModeScenarioCookConfirmCookDish";

        /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] private RawImageCommon _bgImage;

        [Header("大成功情報")]
        [SerializeField] private PartsSingleModeScenarioCookCookPartnerInfo _partnerInfo;
        [SerializeField] private ButtonCommon _greatSuccessInfoButton;

        [Header("料理情報")]
        [SerializeField] private TextCommon _dishHeaderText;
        [SerializeField] private PartsSingleModeScenarioCookDishInfo _dishInfo;

        [SerializeField] private ButtonCommon _dishChangeButton;
        [SerializeField] private GameObject _dishChangeButtonRoot;

        [Header("お料理ポイント情報")]
        [SerializeField] private TextCommon _beforeBaseFriendPoint;
        [SerializeField] private ImageCommon _beforeGreatSuccessImage;
        [SerializeField] private TextCommon _afterBaseFriendPoint;
        [SerializeField] private ImageCommon _afterGreatSuccessImage;

        [Header("料理実行後の野菜所持情報")]
        [SerializeField] private TextCommon[] _afterMaterialCountTextArray;

        [Header("畑ボタン")]
        [SerializeField] private GameObject _gardenButtonRoot;
        [SerializeField] private ButtonCommon _gardenButton;
        [SerializeField] private ImageCommon _growableBadge;

        [Header("警告テキスト")]
        [SerializeField] private GameObject _alertTextNotExsitMaterialRoot;
        [SerializeField] private GameObject _alertTextGardenUpdateRoot;
        [SerializeField] private GameObject _alertTextOtherTrainingEffectRoot;

        [Header("スキップボタン")]
        [SerializeField] private ButtonCommon _skipButton;
        [SerializeField] private ImageCommon _skipButtonImage;
        [SerializeField] private TextCommon _skipButtonText;



        private bool IsSkip => SaveDataManager.Instance.SaveLoader.IsSkipSingleCookDishPerformance;

        private System.Action<SingleModeScenarioCookDishModel, bool> _onDecide;
        private System.Action _onCancel;
        private SingleModeScenarioCookDishModel _dishModel;
        
        private bool _prevIsRecommendLowCostDish;

        public static void PushDialog(SingleModeScenarioCookDishModel dishModel, bool isShowSelectButton, System.Action<SingleModeScenarioCookDishModel, bool> onCookDish, System.Action onCancel)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeScenarioCookConfirmCookDish>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            dialogData.RightButtonNoInteractableNotiffication = dishModel.Cookable ? null : TextId.SingleModeScenarioCook585013.Text();

            DialogManager.PushDialog(dialogData);
            dialog.Setup(dishModel, isShowSelectButton, onCookDish, onCancel);
            TempData.Instance.SingleModeData.Cook.ConfirmCookDialogToGardenViewDishModel = null;
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioCook359002.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = dialog => dialog.Close( () =>
            {
                _onCancel?.Invoke();
                UpdateTrainingFooter();
            });
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = (dialogCommon) => OnRightButtonCallBack(dialogCommon);
            dialogData.AutoClose = false;
            return dialogData;
        }


        private void Setup(SingleModeScenarioCookDishModel dishModel, bool isShowSelectButton, System.Action<SingleModeScenarioCookDishModel, bool> onDecide, System.Action onCancel)
        {
            _dishModel = dishModel;
            _onDecide = onDecide;
            _onCancel = onCancel;
            
            _prevIsRecommendLowCostDish = SaveDataManager.Instance.SaveLoader.IsRecommendLowCostDish;
            
            _bgImage.texture = ResourceManager.LoadOnHash<Texture2D>(SINGLE_MODE_SCENARIO_COOK_DIALOG_DECO_00, DialogHash);

            SetupCookPartnerInfo();
            SetupDishInfo(isShowSelectButton);
            SetupSuccessFriendPoint();
            SetupMaterialList();
            SetupGardenButton();
            SetupAlertText();
            SetupSkipButton();
        }

        private void SetupCookPartnerInfo()
        {
            _partnerInfo.Setup();
            _greatSuccessInfoButton.SetOnClick(DialogSingleModeScenarioCookGreatSuccessDetail.PushDialog);
        }

        private void SetupDishInfo(bool isShowSelectButton)
        {
            _dishHeaderText.text = TextId.SingleModeScenarioCook585018.Text();
            _dishInfo.Setup(_dishModel);
            _dishInfo.SetEnableButton(false);

            _dishChangeButton.SetOnClick(OnClickDishChangeButton);
            _dishChangeButtonRoot.SetActiveWithCheck(isShowSelectButton);
        }

        private void SetupSuccessFriendPoint()
        {
            var beforeSuccessFriendPoint = SingleModeScenarioCookFriendsPowerModel.CookingFriendsPower;
            var afterSuccessFriendPoint = beforeSuccessFriendPoint + _dishInfo.DishModel.GainFriendsPower;
            if (SingleModeScenarioCookFriendsPowerModel.ExistNextFriendsPowerMissionGoal)
            {
                _beforeBaseFriendPoint.text = TextId.Common0089.Format(beforeSuccessFriendPoint.ToCommaSeparatedString(),
                    SingleModeScenarioCookFriendsPowerModel.NextFriendsPowerMissionPoint.ToCommaSeparatedString());
                _beforeGreatSuccessImage.SetActiveWithCheck(beforeSuccessFriendPoint >= SingleModeScenarioCookFriendsPowerModel.NextFriendsPowerMissionPoint);
                _afterBaseFriendPoint.text = TextId.SingleModeScenarioCook585014.Format(afterSuccessFriendPoint.ToCommaSeparatedString(),
                    SingleModeScenarioCookFriendsPowerModel.NextFriendsPowerMissionPoint.ToCommaSeparatedString());
                _afterGreatSuccessImage.SetActiveWithCheck(afterSuccessFriendPoint >= SingleModeScenarioCookFriendsPowerModel.NextFriendsPowerMissionPoint);
            }
            else
            {
                // 大豊食祭後は獲得料理ptのみ表示し、大満足の予感等の表示は出さない
                _beforeBaseFriendPoint.text = beforeSuccessFriendPoint.ToCommaSeparatedString();
                _beforeGreatSuccessImage.SetActiveWithCheck(false);
                _afterBaseFriendPoint.text = afterSuccessFriendPoint.ToCommaSeparatedString();
                _afterBaseFriendPoint.FontColor = FontColorType.Plus;
                _afterGreatSuccessImage.SetActiveWithCheck(false);
            }
        }
        
        /// <summary>
        /// 野菜一覧
        /// </summary>
        private void SetupMaterialList()
        {
            // 所持野菜情報
            var materialModelList = SingleModeScenarioCookMaterialModel.GetMaterialModels();

            var count = materialModelList.Count;
            for (int i = 0; i < count; i++)
            {
                var materialModel = materialModelList[i];

                var useDishMaterial = _dishInfo.DishModel.MaterialList.FirstOrDefault(x => x.MaterialId == materialModel.MaterialId);
                if (useDishMaterial == null)
                {
                    // 使用されない野菜の場合現在の所持数をそのまま表示
                    _afterMaterialCountTextArray[i].text = materialModel.Num.ToString();
                    _afterMaterialCountTextArray[i].FontColor = FontColorType.Brown;
                    continue;
                }

                // 使用する数を引いた値を表示
                var afterMaterialNum = materialModel.Num - useDishMaterial.MaterialNum;
                _afterMaterialCountTextArray[i].text = afterMaterialNum.ToString();

                // 足りていればMinus、不足していればWarning
                _afterMaterialCountTextArray[i].FontColor = useDishMaterial.MaterialNum > materialModel.Num ? FontColorType.Warning : FontColorType.Minus; 
            }

        }


        private void SetupGardenButton()
        {
            // 畑へ遷移
            _gardenButton.SetOnClick(OnClickGardenButton);

            // 夏合宿中は遷移できない
            _gardenButton.SetButtonInteractableWithColor(!WorkDataManager.Instance.SingleMode.IsSummerCampTurn());
            _gardenButton.SetNotificationMessage(WorkDataManager.Instance.SingleMode.IsSummerCampTurn() ? TextId.SingleModeScenarioCook425061.Text() : string.Empty);
            
            // 畑ボタンのバッヂ制御
            _growableBadge.SetActiveWithCheck(IsNeedGardenUpdateCaution);
        }

        /// <summary>
        /// スキップボタンの背景画像、文言、フォントカラーをスキップ状態に合わせて設定
        /// </summary>
        private void SetupSkipButton()
        {
            if (IsSkip)
            {
                _skipButtonImage.sprite = UIManager.PreInAtlas.GetSprite(AtlasSpritePath.PreIn.BUTTON_S_01);
                _skipButtonText.text = TextId.SingleModeScenarioCook585006.Format(TextId.Common0036.Text());
                _skipButtonText.FontColor = FontColorType.White;
            }
            else
            {
                _skipButtonImage.sprite = UIManager.PreInAtlas.GetSprite(AtlasSpritePath.PreIn.BUTTON_S_00);
                _skipButtonText.text = TextId.SingleModeScenarioCook585006.Format(TextId.Common0037.Text());
                _skipButtonText.FontColor = FontColorType.Brown;
            }

            _skipButton.SetOnClick(OnClickSkipButton);
        }


        private void SetupAlertText()
        {
            _alertTextNotExsitMaterialRoot.SetActiveWithCheck(!_dishModel.Cookable);

            // 料理が作れない時は、他のアラート文を表示しない
            if (!_dishModel.Cookable)
            {
                _alertTextGardenUpdateRoot.SetActiveWithCheck(false);
                _alertTextOtherTrainingEffectRoot.SetActiveWithCheck(false);
                return;
            }

            _alertTextGardenUpdateRoot.SetActiveWithCheck(IsNeedGardenUpdateCaution);
            _alertTextOtherTrainingEffectRoot.SetActiveWithCheck(SingleModeScenarioCookDishModel.IsMultipleRecommendDishes());
        }


        private void OnRightButtonCallBack(DialogCommon dialogCommon)
        {
            if (IsNeedGardenUpdateCaution)
            {
                DialogCommon.Data dialogData = new DialogCommon.Data();
                dialogData.DialogType = DialogCommon.DialogType.System;
                dialogData.FormType = DialogCommonBase.FormType.SMALL_TWO_BUTTON;
                dialogData.Title = TextId.Common0009.Text();
                dialogData.Text = TextId.SingleModeScenarioCook419004.Text();
                dialogData.LeftButtonText = TextId.Common0004.Text();
                dialogData.RightButtonText = TextId.Common0003.Text();
                dialogData.RightButtonCallBack = (_) => OnCookDish(dialogCommon);
                DialogManager.PushDialog(dialogData);
            }
            else
            {
                OnCookDish(dialogCommon);
            }
        }

        private void OnCookDish(DialogCommon dialogCommon)
        {
            dialogCommon.Close();
            _onDecide?.Invoke(_dishModel, IsSkip);
        }

        private void OnClickDishChangeButton()
        {
            DialogSingleModeScenarioCookSelectDish.PushDialog(true, (dishModel) =>
            {
                // 選択した料理情報で表示を更新
                _dishModel = dishModel;

                var dialogCommon = GetDialog() as DialogCommon;
                if (dialogCommon != null)
                {
                    var buttonRight = dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right);
                    buttonRight.SetButtonInteractableWithColor(dishModel.Cookable);
                    buttonRight.SetNotificationMessage(dishModel.Cookable ? string.Empty : TextId.SingleModeScenarioCook585013.Text());
                }

                SetupCookPartnerInfo();
                _dishInfo.Setup(dishModel);
                SetupSuccessFriendPoint();
                SetupMaterialList();
                SetupGardenButton();
                SetupAlertText();
                SetupSkipButton();
            });
        }

        private void OnClickGardenButton()
        {
            // ダイアログを閉じて畑へ遷移
            GetDialog().Close();

            SingleModeScenarioCookUtils.GotoGardenView();

            // 確認ダイアログから畑に遷移したら、再表示用の情報を保存する
            TempData.Instance.SingleModeData.Cook.ConfirmCookDialogToGardenViewDishModel = _dishModel;
        }

        private void OnClickSkipButton()
        {
            SaveDataManager.Instance.SaveLoader.IsSkipSingleCookDishPerformance = !IsSkip;
            SaveDataManager.Instance.SaveLoader.Save();
            SetupSkipButton();
        }


        private bool IsNeedGardenUpdateCaution
        {
            get
            {
                // 夏合宿中は畑に遷移できない
                if (WorkDataManager.Instance.SingleMode.IsSummerCampTurn()) return false;

                var dishGroupId = _dishModel.DishGroupId;
                var isTargetRaceTurn = WorkDataManager.Instance.SingleMode.IsTargetRaceTurn();

                // 強化可能な畑で得られる効果リストを取得
                var masterSingleModeCookGardenEffectList = GardenFacilityData.GetCanLevelUpGardenEffectList();

                // 選択中の料理効果を強化する畑効果がある
                foreach (var masterSingleModeCookGardenEffect in masterSingleModeCookGardenEffectList)
                {
                    // 目標レースターンではレースに関する効果のみをチェックする
                    if (isTargetRaceTurn)
                    {
                        if (masterSingleModeCookGardenEffect.EffectType == GardenFacilityData.EFFECT_TYPE_RACE_STATUS_UP)
                        {
                            if (masterSingleModeCookGardenEffect.EffectValue1 == dishGroupId ||
                                masterSingleModeCookGardenEffect.EffectValue2 == dishGroupId ||
                                masterSingleModeCookGardenEffect.EffectValue3 == dishGroupId)
                            {
                                return true;
                            }
                        }

                        continue;
                    }

                    if (masterSingleModeCookGardenEffect.EffectType == GardenFacilityData.EFFECT_TYPE_GAIN_HP ||
                        masterSingleModeCookGardenEffect.EffectType == GardenFacilityData.EFFECT_TYPE_DISH_EFFECT ||
                        masterSingleModeCookGardenEffect.EffectType == GardenFacilityData.EFFECT_TYPE_RACE_STATUS_UP)
                    {
                        if (masterSingleModeCookGardenEffect.EffectValue1 == dishGroupId || 
                            masterSingleModeCookGardenEffect.EffectValue2 == dishGroupId ||
                            masterSingleModeCookGardenEffect.EffectValue3 == dishGroupId)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
        }
        
        private void UpdateTrainingFooter()
        {
            // おすすめ料理の表示オプションが変更された状態でトレーニング画面に戻ってきたら
            if (_prevIsRecommendLowCostDish != SaveDataManager.Instance.SaveLoader.IsRecommendLowCostDish && !DialogManager.IsExistDialog)
            {
                var trainingFooter = SingleModeMainServiceLocator.Instance.Resolve<SingleModeMainView>()?.TrainingFooter;
                // 料理可能バッヂの有無やおすすめ料理に表示される料理の種類が変わるためフッターを更新して開きなおす
                if (trainingFooter != null)
                {
                    // SetupでトレーニングメニューのActiveが切られてしまうので、先にトレーニング画面かどうかを判定しておく
                    var isInTrainingView = trainingFooter.gameObject.activeSelf;
                    trainingFooter.Setup((int)trainingFooter.SelectItem.TrainingMenu.CommandId);
                    if (isInTrainingView)
                    {
                        UIManager.Instance.StartCoroutine(trainingFooter.Open());
                    }
                }
            }
        }

    }
}