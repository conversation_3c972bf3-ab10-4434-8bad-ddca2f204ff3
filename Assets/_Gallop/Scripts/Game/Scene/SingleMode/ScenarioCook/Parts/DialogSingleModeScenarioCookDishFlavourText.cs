using UnityEngine;
using UnityEngine.Serialization;

namespace Gallop
{
    using static ResourcePath;
    using static StaticVariableDefine.Parts.ButtonCommonStatic;
    [AddComponentMenu("")]
    public class DialogSingleModeScenarioCookDishFlavourText : DialogInnerBase
    {
        [System.Serializable]
        public class PartsFlavourText
        {
            [SerializeField] private ImageCommon _rootFrame;
            [SerializeField] private TextCommon _conditionText;
            [SerializeField] private TextCommon _flavourText;
            [SerializeField] private GameObject _openRoot;
            [SerializeField] private GameObject _lockRoot;
            [SerializeField] private TextCommon _lockText;
            [SerializeField] private GameObject _questionMarkText; //ロック中の ??? テキスト表示
            [SerializeField] private GameObject _newIcon;

            public void Setup(SingleModeScenarioCookDishDirectoryItemModel itemModel, bool isSpecial)
            {
                if (isSpecial && itemModel.SpecialFlavourTextModel == null) //キャラ固有枠設定だがモデルが存在しなければ表示しない
                {
                    _rootFrame.SetActiveWithCheck(false);
                    return;
                }
                _rootFrame.SetActiveWithCheck(true);
                var flavourTextModel = isSpecial ? itemModel.SpecialFlavourTextModel : itemModel.DefaultFlavourTextModel;
                _flavourText.text = flavourTextModel.FlavourText;
                _lockText.text = _conditionText.text = flavourTextModel.UnlockConditionText;
                _openRoot.SetActive(!flavourTextModel.IsShowUnlockCondition);
                _lockRoot.SetActive(flavourTextModel.IsShowUnlockCondition);
                _questionMarkText.SetActive(flavourTextModel.IsShowUnlockCondition);
                _rootFrame.color = flavourTextModel.IsShowUnlockCondition ? NO_INTERACTERABLE_COLOR : DEFAULT_COLOR_WHITE;
                _newIcon.SetActive(flavourTextModel.IsNew);
            }
        }

        private const string DIALOG_PATH = SINGLE_MODE_SCENARIO_COOK_PARTS_UI_ROOT + "DialogSingleModeScenarioCookDishFlavourText";
        
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;


        [SerializeField] private ImageCommon _dishIcon;
        [SerializeField] private TextCommon _dishName;

        [SerializeField] private PartsFlavourText _flavourTextParts;
        [SerializeField] private PartsFlavourText _specialFlavourTextParts;


        private System.Action _destroyCallBack;

        public static void PushDialog(SingleModeScenarioCookDishDirectoryItemModel itemModel)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeScenarioCookDishFlavourText>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            DialogManager.PushDialog(dialogData);
            dialog.Setup(itemModel);
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioCook425116.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.DestroyCallBack = DestoryCallback;
            return dialogData;
        }
        
        private void Setup(SingleModeScenarioCookDishDirectoryItemModel itemModel)
        {
            _dishIcon.sprite = itemModel.IconSprite;
            _dishName.text = itemModel.Name;
            _flavourTextParts.Setup(itemModel, false);
            _specialFlavourTextParts.Setup(itemModel, true);
        }

        private void DestoryCallback()
        {
            _destroyCallBack?.Invoke();
        }
    }
}