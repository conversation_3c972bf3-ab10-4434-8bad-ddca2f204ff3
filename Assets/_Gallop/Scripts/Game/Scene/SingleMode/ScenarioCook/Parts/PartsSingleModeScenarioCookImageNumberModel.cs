namespace Gallop
{
    public class PartsSingleModeScenarioCookImageNumberModel : PartsSingleModeImageNumberModel
    {
        /// <summary> シナリオギミックによる追加の加算表示を行うか </summary>
        public override bool EnableBonus => true;

        /// <summary> シナリオギミックによる追加の加算表示A2Uパス </summary>
        public override string BonusA2UPath => ResourcePath.SINGLE_MODE_SCENARIO_COOK_NUM_EXTRA_PARAM_00;

        /// <summary> シナリオギミックによる追加の加算表示A2UのプレートID </summary>
        public override int BonusPlateId(int value, int commandId) => SingleModeUtils.GetTrainingBonusPlateId(value);

        /// <summary> シナリオギミックによる追加の加算表示A2Uプレートモーションオブジェクト名 </summary>
        public override string BonusPlateMotionObjectName => PartsSingleModeImageNumber.MOT_NUM_EXTRA00;

        /// <summary>
        /// 獲得パラメータを0で表示する
        /// </summary>
        public override bool IsShowZeroGainParamText()
        {
            // ボーナスが合ってもベース値が0なら表示しない
            return IsMaxTrainingGainParam ||
                   IsBadConditionOverWeight ||
                   IsParameterTypeSkillPoint;
        }
    }
}