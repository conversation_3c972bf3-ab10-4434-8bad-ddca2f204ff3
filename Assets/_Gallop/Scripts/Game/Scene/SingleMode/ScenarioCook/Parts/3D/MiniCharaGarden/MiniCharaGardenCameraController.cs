using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// お料理編ミニキャラお畑用カメラ
    /// </summary>
    public class MiniCharaGardenCameraController : MiniCameraControllerBase
    {
        private MiniDirector _director;

        private MiniDirectorUI _ui = null;

        private SingleModeCookGardenCameraParam _cameraParam;

        public override void OnDirectorInitialize(MiniDirector director)
        {
            _director = director;
            _camera.enabled = true;

            //カメラの設定データを取得
            if (_cameraParam == null)
            {
                _cameraParam = SingleModeCookGardenCameraParam.LoadCameraParam();
            }

            //描画先のUIを取得
            if (director.DirectorUI == null)
            {
                SetCameraDefault(_director); //これはViewer上でも実行
                return;
            }

            //描画先のUIを取得
            if (director.DirectorUI == null)
            {
                SetCameraDefault(_director); //これはViewer上でも実行
                return;
            }

            //テクスチャの生成
            _ui = director.DirectorUI;
            if (_ui.TextureResolution.x <= 0 || _ui.TextureResolution.y <= 0)
            {
                Debug.LogError("解像度が変 " + _ui.TextureResolution);
                return;
            }
            if (_renderTexture == null)
            {
                _renderTexture = MiniDirectorUtil.CreateRenderTex(_camera, _ui.TextureResolution);
            }

            _camera.targetTexture = _renderTexture;

            //カリングの設定
            _camera.cullingMask = 0;
            for (int i = 0; i < _director.CameraParam.CullingLayerArray.Length; i++)
            {
                _camera.cullingMask |= (1 << GraphicSettings.GetLayer(_director.CameraParam.CullingLayerArray[i]));
            }

            //UIテクスチャに映す
            _ui.SetRenderTexture(_renderTexture);

            //GameViewサイズ変更
#if UNITY_EDITOR
            GameViewSizeChangeListner.TryAddComponent(gameObject, this, () =>
            {
                var defaultVisibleArrow = _director.DirectorUI.IsVisbleArrow;
                _director.DirectorUI.ResetTextureSize();
                ReleaseTexture();
                OnDirectorInitialize(_director);    //ここで画面両端の矢印が消えてしまうので↓で戻してる
                OnBgChange(_director.CurrentBg.Id);
                _director.DirectorUI.SetVisibleArrow(defaultVisibleArrow);
            });
#endif
            ApplyParamToCamera();
        }

        public override void OnDirectorUpdate()
        {
            AudioManager.Update3dListener(GetCamera());
            base.OnDirectorUpdate();
        }

        public override void OnDirectorFinalize()
        {
            base.OnDirectorFinalize();
            if (_cameraParam != null)
            {
                _cameraParam.Dispose();
                _cameraParam = null;
            }
        }

        protected override void SetCameraDefault(MiniDirector director)
        {
            base.SetCameraDefault(director);

            ApplyParamToCamera();
        }

        /// <summary>
        /// カメラのパラメーターデータを適用
        /// </summary>
        private void ApplyParamToCamera()
        {
            if (_cameraParam == null)
            {
                return;
            }

            var cameraData = _cameraParam.GetCameraData();

            _camera.orthographic = cameraData.IsOrthoGraphic;
            _camera.orthographicSize = cameraData.OrthoGraphicSize;
            _camera.fieldOfView = cameraData.FieldOfView;
            _camera.nearClipPlane = cameraData.NearClipPlane;
            _camera.farClipPlane = cameraData.FarClipPlane;
            _camera.transform.localPosition = cameraData.CameraPosition;
            _camera.transform.localRotation = Quaternion.Euler(cameraData.CameraRotation);

            AdjustCameraPositionByAspectRatio(cameraData);
        }

        /// <summary>
        /// アスペクト比に応じてカメラの位置を調整
        /// </summary>
        private void AdjustCameraPositionByAspectRatio(SingleModeCookGardenCameraParam.CameraData cameraData)
        {
            // NOTE:9:16合わせて設定されてるカメラを9:19.5くらいで限界まで座標が変動するように調整する
            const float BASE_ASPECT_RATIO = GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL_16_9;
            const float MAX_TAEGET_ASPECT_RATIO = GallopUtil.BASE_SCREEN_ASPECT_RATIO_VERTICAL;

            var currentAspectRatio = GraphicSettings.Instance.GetAspect();

            var subAspectRatio1 = MAX_TAEGET_ASPECT_RATIO - BASE_ASPECT_RATIO;
            var subAspectRatio2 = currentAspectRatio - BASE_ASPECT_RATIO;
            var t = Math.DivideSafe(subAspectRatio2, subAspectRatio1);
            var resultPositionY = Mathf.Lerp(cameraData.CameraPosition.y, cameraData.MaxCameraPositionY, t);

            _camera.transform.SetLocalPositionY(resultPositionY);
        }

#if UNITY_EDITOR

        /// <summary>
        /// 設定ファイルのデータを直接に読み込む
        /// </summary>
        public void DebugApplyParamToCamera()
        {
            if (_camera == null)
            {
                return;
            }

            ApplyParamToCamera();
        }

#endif
    }
}
