using System.Collections.Generic;
using System.Linq;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;

namespace Gallop
{
    /// <summary>
    /// Cook編:料理でのパラメータボーナスアップ
    /// </summary>
    public sealed class CookFixedParamBonusUpParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        private List<SingleModeScenarioCookDishModel.SingleModeCookDishEffect> ModelList { get; }
        public bool IsGroupPlay => false;
        public string MessageText => GetMessageText();

        public CookFixedParamBonusUpParameterInfo(List<SingleModeScenarioCookDishModel.SingleModeCookDishEffect> modelList)
        {
            ModelList = modelList;
        }
        
        public TrainingParamChangeA2UContext CreateA2UContext()
        {
            return new SingleModeScenarioCookFixedParamBonusUpA2U();
        }

        private string GetMessageText()
        {
            const int MAX_PARAM_NUM = 5;
            
            // 先頭効果から効果量を取得
            int effectValue = ModelList.Select(m => m.EffectValue1).FirstOrDefault();
            if (ModelList.Count == MAX_PARAM_NUM)
            {
                return TextId.SingleModeScenarioCook359020.Format(effectValue);
            }
            else
            {
                string paramStr = string.Join(
                    TextId.SingleModeScenarioCook425078.Text(),
                    ModelList.OrderBy(model => model.EffectValue2).Select(m => TextId.SingleModeScenarioCook359021.Format(m.FixedParamTypeText))
                );
                return TextId.SingleModeScenarioCook359019.Format(paramStr, effectValue); 
            }
        }
    }
    
    public class SingleModeScenarioCookFixedParamBonusUpA2U : TrainingParamChangeA2UContext
    {
        public override string FlashPath =>
            ResourcePath.SINGLE_MODE_SCENARIO_COOK_FLASH_ROOT + "pf_fl_singlemode_cook_event_parabonus00";

        public override float PlayTime => ANIMATION_UP_TIME;

        public override void PlaySE()
        {
            AudioManager.Instance.PlaySe(AudioId.SFX_SINGLEMODE_POSITIVE_LOG);
        }
    }
}