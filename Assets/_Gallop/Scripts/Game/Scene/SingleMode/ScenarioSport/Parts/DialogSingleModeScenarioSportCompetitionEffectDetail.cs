using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// パッシブ効果詳細ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioSportCompetitionEffectDetail : DialogInnerBase
    {
        #region SerializeField
        
        [SerializeField] private FlickToggleGroupCommon _toggleGroup;
        [SerializeField] private FlickableObject _flickableObject;

        [SerializeField] private ImageCommon _sportTypeBaseImage;
        [SerializeField] private TextCommon _sportTypeWinNumTitleText;
        [SerializeField] private TextCommon _sportTypeWinNumText;

        [SerializeField] private PartsSingleModeScenarioSportCompetitionEffectDetailList _competitionEffectDetailList;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            
        }

        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog()
        {
            const string PATH = ResourcePath.SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_EFFECT_DETAIL;
            var content = LoadAndInstantiatePrefab<DialogSingleModeScenarioSportCompetitionEffectDetail>(PATH);
            var dialogData = content.CreateDialogData();

            DialogManager.PushDialog(dialogData);
            content.Setup();
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioSport508018.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();

            return dialogData;
        }
        
        public void Setup()
        {
            const SingleModeScenarioSportDefine.DefineSportType DEFAULT_SPORT_TYPE = SingleModeScenarioSportDefine.DefineSportType.BallGame;
            
            SetupToggleGroup();
            
            SetupSportTypeInfo(DEFAULT_SPORT_TYPE);
            SetupCompetitionEffectDetailList(DEFAULT_SPORT_TYPE);
        }

        private void SetupToggleGroup()
        {
            _toggleGroup.SetOnSelectCallback(OnSelectToggle);
            _flickableObject.SetFlickCallback(_toggleGroup.OnFlick);
            
            // トグルのテキスト設定
            for (var toggleIndex = 0; toggleIndex < _toggleGroup.ToggleArrayLength; toggleIndex++)
            {
                var toggle = _toggleGroup.GetToggle(toggleIndex);
                if (toggle != null)
                {
                    var sportType = toggleIndex switch
                    {
                        0 => SingleModeScenarioSportDefine.DefineSportType.BallGame,
                        1 => SingleModeScenarioSportDefine.DefineSportType.MartialArts,
                        2 => SingleModeScenarioSportDefine.DefineSportType.Variety,
                        _ => SingleModeScenarioSportDefine.DefineSportType.BallGame,
                    };
                    toggle.TargetText.text = sportType.Name();
                }
            }
        }

        private void SetupSportTypeInfo(SingleModeScenarioSportDefine.DefineSportType sportType)
        {
            _sportTypeBaseImage.sprite = AtlasSpritePath.SingleModeScenarioSport.GetSingleModeScenarioSportCompetitionEffectDetailBaseImage(sportType);
            
            // 「xxの優勝数」
            _sportTypeWinNumTitleText.text = TextId.SingleModeScenarioSport508019.Format(sportType.Name());
            
            var competitionModel = new SingleModeScenarioSportCompetitionModel();
            var totalWinNum = competitionModel.GetCompetitionWinCommandIdCountWithSportType(sportType);
            _sportTypeWinNumText.text = totalWinNum.ToString();
        }

        private void SetupCompetitionEffectDetailList(SingleModeScenarioSportDefine.DefineSportType sportType)
        {
            _competitionEffectDetailList.Setup(sportType, DialogHash);
        }

        private void OnSelectToggle(int index)
        {
            var sportType = index switch
            {
                0 => SingleModeScenarioSportDefine.DefineSportType.BallGame,
                1 => SingleModeScenarioSportDefine.DefineSportType.MartialArts,
                2 => SingleModeScenarioSportDefine.DefineSportType.Variety,
                _ => SingleModeScenarioSportDefine.DefineSportType.BallGame,
            };

            SetupSportTypeInfo(sportType);
            SetupCompetitionEffectDetailList(sportType);
        }
    }
}
