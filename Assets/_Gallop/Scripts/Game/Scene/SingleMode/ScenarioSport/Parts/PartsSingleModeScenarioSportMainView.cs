using DG.Tweening;
using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{

    public class PartsSingleModeScenarioSportMainViewModel
    {
        /// <summary>
        /// 全ての大会が終了したかどうか (次の大会マスタが取れなければtrue)
        /// </summary>
        public bool IsAllSportCompetitionEnd
            => MasterDataManager.Instance.masterSingleModeSportCompetition.GetSingleModeSportCompetition(WorkDataManager.Instance.SingleMode.GetCurrentTurn()) == null;

        public SingleModeScenarioSportCompetitionModel CompetitionModel { get; private set; }

        /// <summary>
        /// 大会モデルのインスタンスを更新する (ターンを跨いでインスタンスを保持した場合に、きちんと更新されるようにする)
        /// </summary>
        public void UpdateCompetitionModelInstance()
        {
            CompetitionModel = new SingleModeScenarioSportCompetitionModel();
        }
    }


    /// <summary>
    /// 育成追加シナリオ：Sport編
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioSportMainView : MonoBehaviour, ISingleModeScenarioMainViewAdditivePartsBase
    {
        private const int COMPETITION_INFO_BUTTON_SORT_OFFSET = 100;
        
        #region SerializeField
        
        [SerializeField] private PartsSingleModeMainViewInOutAnimationRoot _animationRoot;

        [Header("大会結果予測")]
        [SerializeField] private PartsSingleModeScenarioSportCompetitionExpectation _competitionExpectation;
        [SerializeField] private ButtonCommon _competitionInfoButton;
        [SerializeField] private Canvas _competitionInfoButtonCanvas;
        [SerializeField] private PartsSingleModeMainViewInOutAnimationRoot _competitionExpectationAnimationRoot;

        /// <summary> トレーニング画面でのみ表示する「所持グッズ」ボタン 育成TOPではStablePanelButtonで出す </summary>
        [SerializeField] private PartsSingleModeScenarioSportUserItemButton _userItemButtonForTraining;
        [SerializeField] private PartsSingleModeMainViewInOutAnimationRoot _userItemButtonForTrainingAnimationRoot;
        
        
        [Header("スタンス情報")]
        [SerializeField] private PartsSingleModeScenarioSportStanceInfoList _stanceInfo;
        [SerializeField] private PartsSingleModeMainViewInOutAnimationRoot _stanceInfoAnimationRoot;

        #endregion

        #region Member

        private PartsSingleModeScenarioSportMainViewModel _model = null;
        private PartsSingleModeScenarioSportMainViewModel Model
        {
            get
            {
                if(_model == null)
                {
                    _model = new PartsSingleModeScenarioSportMainViewModel();
                }
                return _model;
            }
        }

        private Canvas _parentCanvas;
        private Canvas ParentCanvas => _parentCanvas ??= transform.parent.GetComponentInParent<Canvas>();


        #endregion

        /// <summary>
        /// 生成
        /// </summary>
        public static PartsSingleModeScenarioSportMainView Create(Transform parent)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_MAIN_VIEW);
            var obj = Instantiate(prefab, parent);
            var parts = obj.GetComponent<PartsSingleModeScenarioSportMainView>();
            
            return parts;
        }

        public static void RegisterDownlaod(DownloadPathRegister register)
        {
            DialogSingleModeScenarioSportStanceInfo.RegisterDownload(register);

            DialogSingleModeScenarioSportItemUpdateNotice.RegisterDownload(register);
            
            DialogSingleModeScenarioSportCompetitionEffectDetail.RegisterDownload(register);
            
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_EVENT_RANKUP00);
            PartsSingleModeScenarioSportStanceActivation.RegisterDownload(register);
            
            // 大会TOPに遷移する専用ワイプ
            NowLoadingWipeSingleModeScenarioSportCompetitionTop.RegisterDownload(register);
            
            // 期待度予測・ゲージ
            PartsSingleModeScenarioSportCompetitionExpectation.RegisterDownload(register);

            // 優勝本命時のアイコンエフェクト
            PartsSingleModeScenarioSportRankInfo.RegisterDownload(register);
        }

        #region ISingleModeScenarioMainViewAdditivePartsBase

        /// <summary>
        /// 表示更新
        /// </summary>
        public void Setup()
        {
            SetupCompetitionInfo();
            SetupCompetitionInfoButton();

            _stanceInfo.Setup();
            _stanceInfo.SetActiveGainSportRankText(false);
            
            _userItemButtonForTraining.Setup();
        }


        /// <summary>
        /// IN
        /// </summary>
        public void PlayIn()
        {
            if (this.gameObject.activeSelf)
            {
                _animationRoot.CreatePlayInSequence(TweenAnimation.PresetType.PartsInFadeFromLeft);

                // 育成トップでは非表示
                _userItemButtonForTraining.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// OUT
        /// </summary>
        public void PlayOut()
        {
            if (this.gameObject.activeSelf)
            {
                _animationRoot.CreatePlayOutSequence(TweenAnimation.PresetType.PartsOutFadeFromLeft);
            }
        }

        /// <summary>
        /// トレーニング選択へ
        /// </summary>
        public void PlayGoTrainingSelect()
        {
            _userItemButtonForTrainingAnimationRoot.CreatePlayInSequence(TweenAnimation.PresetType.PartsInFadeFromLeft);
            _competitionExpectationAnimationRoot.CreatePlayOutSequence(TweenAnimation.PresetType.PartsOutFadeFromLeft);
            
            // トレーニングボタンのヒントバッヂとアニメーションを同期するためにPlay関数を再度呼ぶ
            _userItemButtonForTraining.Setup();
        }

        /// <summary>
        /// トレーニング選択から戻る
        /// </summary>
        public void PlayReturnTrainingSelect()
        {
            _userItemButtonForTrainingAnimationRoot.CreatePlayOutSequence(TweenAnimation.PresetType.PartsOutFadeFromLeft);            
            _competitionExpectationAnimationRoot.CreatePlayInSequence(TweenAnimation.PresetType.PartsInFadeFromLeft);

            // 競技ランク上昇予測OFF
            _stanceInfo.SetActiveGainSportRankText(false);
        }

        /// <summary>
        /// トレーニングコマンド選択時
        /// </summary>
        public void PlayExecTrainingCut()
        {
            _stanceInfo.SetInfoButtonInteractable(false);

            _userItemButtonForTraining.SetButtonEnable(false);
            _userItemButtonForTrainingAnimationRoot.CreatePlayOutSequence(TweenAnimation.PresetType.PartsOutFadeFromLeft);
        }

        /// <summary>
        /// AdditiveViewからトレーニング画面へ帰ってきた時のIN再生
        /// </summary>
        public void PlayInBackTrainingFromAdditiveView() { }


        public void OnTrainingItemSelected(TrainingDefine.TrainingCommandId commandId)
        {
            _userItemButtonForTraining.SetActiveWithCheck(true);
            
            // トレーニングによる競技ランク上昇予測
            _stanceInfo.SetupGainSportRankText(commandId);
        }

        public void SetActive(bool enable)
        {
            this.gameObject.SetActive(enable);
            _animationRoot.gameObject.SetActive(enable);
            if(enable)
            {
                _animationRoot.SetActiveImmediate(true);
                
                _competitionExpectationAnimationRoot.SetActiveImmediate(true);

                _stanceInfoAnimationRoot.SetActiveImmediate(true);
                _stanceInfo.SetInfoButtonInteractable(true);

                _userItemButtonForTrainingAnimationRoot.SetActiveImmediate(true);
                _userItemButtonForTraining.SetButtonEnable(true);
            }
        }

        public bool IsNeedShowScenarioNotice()
        {
            // 大会開催告知は10ターン前と5ターン前
            const int NOTICE_TURN_10 = 10;
            const int NOTICE_TURN_5 = 5;

            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var masterSingleModeSportCompetition = MasterDataManager.Instance.masterSingleModeSportCompetition.GetSingleModeSportCompetition(currentTurn);
            if (masterSingleModeSportCompetition == null)
            {
                return false;
            }

            // 開催表示をチェック済みでない
            if (SingleModeChangeViewManager.Instance.LastCheckScenarioNotice == null ||
                SingleModeChangeViewManager.Instance.LastCheckScenarioNotice != WorkDataManager.Instance.SingleMode.GetCurrentYearMonthAndHalf()) 
            {
                return currentTurn == masterSingleModeSportCompetition.Turn - NOTICE_TURN_10 ||
                       currentTurn == masterSingleModeSportCompetition.Turn - NOTICE_TURN_5;
            }
            return false;
        }

        /// <summary>
        /// 開催予定表示
        /// </summary>
        public void ShowScenarioNotice(System.Action onComplete)
        {
            var masterSingleModeSportCompetition = MasterDataManager.Instance.masterSingleModeSportCompetition.GetSingleModeSportCompetition(WorkDataManager.Instance.SingleMode.GetCurrentTurn());
            PartsSingleModeMainViewScenarioNotice.Play(transform, ResourcePath.SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_REMIND_TURN00, masterSingleModeSportCompetition.Turn,
                onComplete: () =>
                {
                    if (!IsNeedPlayCompetitionExpectationGaugeAnimation())
                    {
                        onComplete?.Invoke();
                    }
                },
                onPlayOut: () =>
                {
                    // 期待度上昇アニメーションがある場合は、リマインド演出のoutのタイミングから再生開始できるよう早めに抜けさせる
                    if (IsNeedPlayCompetitionExpectationGaugeAnimation())
                    {
                        onComplete?.Invoke();
                    }
                });
        }

        #endregion

        private void SetupCompetitionInfo()
        {
            // 大会モデル更新
            Model.UpdateCompetitionModelInstance();
            
            var isAllCompetitionEnd = Model.IsAllSportCompetitionEnd;
            if (isAllCompetitionEnd == false)
            {
                // アニメーションが必要な場合はTempに積んである値でセットアップし、別途MainView側からPlayCompetitionExpectationGaugeAnimation()でアニメーションを流す
                int gaugeValue;
                PartsSingleModeScenarioSportCompetitionExpectation.MaxDisplayState maxDisplayState;
                if (IsNeedPlayCompetitionExpectationGaugeAnimation())
                {
                    if (TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedCompeType == Model.CompetitionModel.NextCompetitionExpectation.CompeType)
                    {
                        gaugeValue = TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedGaugeValue;
                        maxDisplayState = TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedMaxDisplayState;
                    }
                    else
                    {
                        gaugeValue = 0; // 目標大会が変わった時は、アニメーションを0から再生するためにゲージの元の数をリセット
                        maxDisplayState = PartsSingleModeScenarioSportCompetitionExpectation.MaxDisplayState.Normal;
                    }
                }
                else
                {
                    gaugeValue = SingleModeScenarioSportUtils.GetCompetitionExpectationGaugeValue(Model.CompetitionModel.NextCompetitionExpectation);
                    maxDisplayState = SingleModeScenarioSportUtils.GetCompetitionExpectationMaxDisplayState(Model.CompetitionModel.NextCompetitionExpectation);
                }
                _competitionExpectation.Setup(gaugeValue, maxDisplayState);
            }
            else
            {
                _competitionExpectation.SetupCompetitionEnd();
            }
        }

        private void SetupCompetitionInfoButton()
        {
            _competitionInfoButton.SetOnClick(DialogSingleModeScenarioSportCompetitionInfo.PushDialog);

            if (ParentCanvas != null)
            {
                _competitionInfoButtonCanvas.sortingLayerName = ParentCanvas.sortingLayerName;
                _competitionInfoButtonCanvas.sortingOrder = ParentCanvas.sortingOrder + COMPETITION_INFO_BUTTON_SORT_OFFSET;
            }
        }

        #region 期待度上昇アニメーション
        
        /// <summary>
        /// 支持率が前ターンから上昇するアニメを再生する必要があるか
        /// チェック後、チェック済みを保存
        /// </summary>
        public bool CheckPlayCompetitionExpectationGaugeAnimation()
        {
            var isNeed = IsNeedPlayCompetitionExpectationGaugeAnimation();
            if (isNeed == false)
            {
                // 再生しない場合はここで保存し翌ターンチェックできるように。再生する場合はPlayCompetitionExpectationGaugeAnimation()で再生後に保存する
                UpdatePlayCompetitionExpectationAsPlayed();
            }
            return isNeed;
        }
        
        /// <summary>
        /// 期待度が前ターンから上昇するアニメを再生する必要があるかどうか
        /// </summary>
        private bool IsNeedPlayCompetitionExpectationGaugeAnimation()
        {
            // 未更新(初回)なら不要
            var tempSport = TempData.Instance.SingleModeData.Sport;
            if (tempSport.MainViewCompetitionExpectationRatePlayedTurn == 0) return false;
            
            // そのターン既に表示済みなら不要
            var turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            if (tempSport.MainViewCompetitionExpectationRatePlayedTurn == turn) return false;
            
            // 大会終了済みなら不要
            var nextCompetitionExpectation = Model.CompetitionModel.NextCompetitionExpectation;
            if (nextCompetitionExpectation == null) return false;

            // ゲージが空なら不要
            var currentGauge = SingleModeScenarioSportUtils.GetCompetitionExpectationGaugeValue(nextCompetitionExpectation);
            if(currentGauge == 0) return false;

            var currentMaxDisplayType = SingleModeScenarioSportUtils.GetCompetitionExpectationMaxDisplayState(nextCompetitionExpectation);
            
            // 対象の大会が同じ(大会の次のターンではない)場合、ゲージが増えていないなら不要
            // 大会の次のターンの場合、既にゲージに値があるなら必ず再生する
            if (tempSport.MainViewCompetitionExpectationPlayedCompeType == nextCompetitionExpectation.CompeType)
            {
                // DisplayTypeが変わっているならゲージ増減がなくてもアニメーション必要
                if (currentMaxDisplayType != tempSport.MainViewCompetitionExpectationPlayedMaxDisplayState)
                {
                    return true;
                }
                
                var prevGauge = tempSport.MainViewCompetitionExpectationPlayedGaugeValue;
                if (currentGauge <= prevGauge) return false;
            }

            return true;
        }
        
        /// <summary>
        /// 期待度上昇アニメーション確認済みとしてマークする
        /// </summary>
        private void UpdatePlayCompetitionExpectationAsPlayed()
        {
            var nextCompetitionExpectation = Model.CompetitionModel.NextCompetitionExpectation;
            if (nextCompetitionExpectation == null) return;
            
            TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationRatePlayedTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedCompeType = nextCompetitionExpectation.CompeType;
            TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedGaugeValue = SingleModeScenarioSportUtils.GetCompetitionExpectationGaugeValue(nextCompetitionExpectation);
            TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedMaxDisplayState = SingleModeScenarioSportUtils.GetCompetitionExpectationMaxDisplayState(nextCompetitionExpectation);
        }

        /// <summary>
        /// 期待度が前ターンから上昇するアニメ再生
        /// </summary>
        public IEnumerator PlayCompetitionExpectationGaugeAnimation(SingleModeMainTurnStartChecker.StreamData streamData)
        {
            UIManager.Instance.LockGameCanvas();
            var nextCompetitionExpectation = Model.CompetitionModel.NextCompetitionExpectation;
            if (nextCompetitionExpectation == null) yield break;

            // この通知が一番初めの場合、TweenPresetによるPlayInとタイミングが被ってしまうため、そちらの完了を待ってからアニメーションを再生させる
            if (streamData.ShowCount == 0)
            {
                var dummyPlayInSequence = TweenAnimationBuilder.CreateSequence(_animationRoot.gameObject, TweenAnimation.PresetType.PartsInFadeFromLeft);
                var playInDuration = dummyPlayInSequence.Duration();
                dummyPlayInSequence.Kill(true); // duration取得用のsequenceなのですぐに切る

                yield return new WaitForSeconds(playInDuration);
            }

            var complete = false;
            var prevGaugeValue = TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedGaugeValue;
            if(TempData.Instance.SingleModeData.Sport.MainViewCompetitionExpectationPlayedCompeType != nextCompetitionExpectation.CompeType)
            {
                // 目標大会が変わった時は、アニメーションを0から再生するためにゲージの元の数をリセット
                prevGaugeValue = 0;
            }
            var dstGaugeValue =  SingleModeScenarioSportUtils.GetCompetitionExpectationGaugeValue(nextCompetitionExpectation);
            var maxDisplayState = SingleModeScenarioSportUtils.GetCompetitionExpectationMaxDisplayState(nextCompetitionExpectation);

            if (prevGaugeValue != dstGaugeValue)
            {
                _competitionExpectation.PlayGaugeAnimation(prevGaugeValue, dstGaugeValue, maxDisplayState, () => complete = true);
            }
            else
            {
                // ゲージ変化なし、文字のみが切り替わるアニメーション
                _competitionExpectation.PlayGaugeMaxStateAnimation(maxDisplayState, () => complete = true);
            }
            
            // 確認済みとして保存
            UpdatePlayCompetitionExpectationAsPlayed();
            
            if (streamData.IsInLastNotice)
            {
                // これ以降の通知がないならば、即座にタップ可能にする
                UIManager.Instance.UnlockGameCanvas();
                yield break;
            }
            else
            {
                yield return new WaitUntil(() => complete);
                UIManager.Instance.UnlockGameCanvas();
            }
        }
        
        #endregion


        #region SingleModeMainTrainingCuttControllerイベント

        /// <summary>
        /// ヒートアップUIの競技ランク獲得演出・ヒートアップ発動演出
        /// </summary>
        public void PlayInStanceSportRankUp(Action onComplete)
        {
            _stanceInfo.PlayInStanceSportRankUp(onComplete);
        }

        /// <summary>
        /// 競技ランク加算演出：エフェクトがヒートアップUIに吸い込まれていく演出の再生
        /// </summary>
        public void PlaySportRankGainEffectMoveAnimation(Transform target, SingleModeScenarioSportDefine.DefineSportType sportType, System.Action onComplete)
        {
            _stanceInfo.PlaySportRankGainEffectMoveAnimation(target, sportType, onComplete);
        }
        
        /// <summary>
        /// ヒートアップUIのハケ再生
        /// </summary>
        public void PlayOutStanceInfoList()
        {
            _stanceInfoAnimationRoot.CreatePlayOutSequence(TweenAnimation.PresetType.PartsOutFadeFromLeft);
        }


        #endregion SingleModeMainTrainingCuttControllerイベント

    }
}
