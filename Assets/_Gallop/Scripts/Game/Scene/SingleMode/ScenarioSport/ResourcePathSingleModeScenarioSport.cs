using static Gallop.SingleModeScenarioSportDefine;

namespace Gallop
{
    /// <summary>
    /// 育成追加シナリオ：Sport編 リソースパス
    /// </summary>
    public static partial class ResourcePath
    {
        #region SINGLE_MODE_SCENARIO_SPORT_UI

        public const string SINGLE_MODE_SCENARIO_SPORT_UI_ROOT = SINGLE_MODE_UI_ROOT + "ScenarioSport/";

        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_MAIN_VIEW = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportMainView";
        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_MAIN_STABLES_PANEL_BUTTON = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportMainStablesPanelButton";

        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_PARAM_UP_MINI_CHARA_MODEL = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportParamUpMiniCharaModel";

        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_STANCE_INFO_LIST = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportStanceInfoList";
        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_STANCE_ACTIVATION = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportStanceActivation";

        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_ITEM_UPDATE_NOTICE = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportItemUpdateNotice";

        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_USE_ITEM_CHANGE_SPORT_TYPE_ALL = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportUseItemChangeSportTypeAll";
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_USE_ITEM_CHANGE_SPORT_TYPE_ALL_CONFIRM = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportUseItemChangeSportTypeAllConfirm";
        
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_DATA_LIST = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportDataList";
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_NEXT_SCHEDULE_LIST = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportNextScheduleList";
        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_DATA_LIST_ITEM = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportDataListItem";

        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_RESULT_DETAIL = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportCompetitionResultDetail";
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_SHOW_DOWN_END_LIVE_CONFIRM = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportCompetitionShowDownEndLiveConfirm";
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_RESULT = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportCompetitionResult";
        
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_INFO = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportCompetitionInfo";
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_EFFECT_DETAIL = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportCompetitionEffectDetail";

        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_EFFECT_SUMMARY_LIST_ITEM = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportCompetitionEffectSummaryListItem";
        public const string SINGLE_MODE_SCENARIO_SPORT_PARTS_SINGLE_MODE_SCENARIO_SPORT_COMPETITION_EFFECT_DETAIL_LIST_ITEM = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "PartsSingleModeScenarioSportCompetitionEffectDetailListItem";

        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_STANCE_INFO = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportStanceInfo";
        public const string SINGLE_MODE_SCENARIO_SPORT_DIALOG_SINGLE_MODE_SCENARIO_SPORT_SPORT_RANK_DETAIL = SINGLE_MODE_SCENARIO_SPORT_UI_ROOT + "DialogSingleModeScenarioSportSportRankDetail";


        #endregion SINGLE_MODE_SCENARIO_SPORT_UI

        #region SINGLE_MODE_SCENARIO_SPORT_FLASH

        public const string SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT = SINGLE_MODE_FLASH_ROOT + "Sport/";
        public const string SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT = SINGLE_MODE_FLASH_COMBINE_ROOT + "Sport/";
        public const string SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT = SINGLE_MODE_UI_EFFECT_ROOT + "Sport/";

        /// <summary>
        /// ヘッダー
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_HEADER_TURNCOUNTER00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_header_turncounter00";
        
        /// <summary>
        /// トレーニングタイトルヘッダー
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_TRAININGMENU_BASE00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_trainingmenu_base00";

        /// <summary>
        /// トレーニングボタン
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_BTN_TRAININGMENU00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_btn_trainingmenu00";

        /// <summary>
        /// アスリート編ヒントバッジ
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_BADGE_KNACK00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_badge_knack00";
        
        /// <summary>
        /// トレーニング別枠加算表示
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_NUM_EXTRAPARAM00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_num_extraparam00";
        
        /// <summary>
        /// トレーニング別枠加算表示
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_TRAINING_STATUSBOX00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_training_statusbox00";
        
        /// <summary>
        /// リンクトレーニング成功演出
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_TXT_TRAININGRESULT00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_txt_trainingresult00";
        
        /// <summary>
        /// 友情リンクトレーニング成功演出
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_TXT_TAGTRAININGRESULT00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_txt_tagtrainingresult00";

        /// <summary> 育成TOP：大会期待度・ゲージ表示 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_COMPETITION_GAUGE00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_competition_gauge00";

        /// <summary> 大会開催リマインド演出 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_REMIND_TURN00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_remind_turn00";

        /// <summary> 大会TOPに遷移する際の専用ワイプ </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_COMPETITION_TRANSITION00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_competition_transition00";
        
        /// <summary> 大会TOP: 大会ロゴ </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_COMPETITION_LOGO00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_competition_logo00";
        
        /// <summary> 大会TOP: ShowDownの大会ボタン </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_BTN_COMPETITION_DECIDE00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_btn_competition_decide00";

        /// <summary> 大会結果ダイアログ：「大会終了」タイトル演出 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_TXT_COMPETITIONEND00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_txt_competitionend00";

        /// <summary> 大会リザルト画面：大会総合結果演出 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_COMPETITIONRESULT00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_competitionresult00";

        /// <summary> 大会優勝演出(SHOWDOWN) </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_SHOWDOWN_RESULT00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_showdown_result00";
        
        /// <summary> ソノンの相談効果使用演出(ジャンル変更！) </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PF_FL_SINGLEMODE_SPORT_EVENT_ADVICE00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_ROOT + "pf_fl_singlemode_sport_event_advice00";
        
        /// <summary> ランダムイベント：競技ランクアップ演出 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_EVENT_STANCE_RANKUP00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_event_stance_rankup00";

        /// <summary> トレーニング演出：競技ランクアップ演出 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_EVENT_RANKUP00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_event_rankup00";
        
        /// <summary> トレーニング演出：スタンス発動演出(各競技アイコン) </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_HEATUP_GENRE00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_heatup_genre00";
        
        /// <summary> トレーニング演出：ヒートアップ発動演出テキスト部分(画面中央) </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_TXT_HEATUP00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_txt_heatup00";

        /// <summary> 獲得パラメータ表示パーティクル：スフィア </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_SINGLE_SPORT_EXTRAPARAM_FIRE01 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_extraparam_fire01";
        
        /// <summary> 獲得パラメータ表示パーティクル：ファイト </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_SINGLE_SPORT_EXTRAPARAM_FIRE02 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_extraparam_fire02";
        
        /// <summary> 獲得パラメータ表示パーティクル：スフィア・ファイト </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_SINGLE_SPORT_EXTRAPARAM_FIREMIX = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_extraparam_firemix_01_02";

        /// <summary>
        /// トレーニング結果文字演出：友情リンクトレーニング　アイコンまわりのエフェクト
        /// </summary>
        /// <param name="linkTrainingNum"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioSportTagLinkTrainingEffect(int linkTrainingNum)
        {
            var index = linkTrainingNum - 2; // リンク数が2なら00、3なら01... 1以下のリンクは仕様上存在しない
            
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_tagtrainig_glitter{0:D2}", index).ToString();
        }
        
        /// <summary>
        /// トレーニング演出：競技レベルアップ演出の炎エフェクト
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetSportRankGainStanceFireEffect(DefineSportType sportType)
        {
            const string BASE_PATH = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_training_stance_fire{0:D2}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BASE_PATH, (int)sportType).ToString();
        }
        
        /// <summary>
        /// トレーニング演出：スタンス値加算エフェクト 通常時
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetSportRankGainUIEffect(DefineSportType sportType)
        {
            const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_STANCE = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_stance_{0:D2}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_STANCE, (int)sportType).ToString();
        }

        /// <summary>
        /// トレーニング演出：スタンス値加算エフェクト スタンス発動時
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetSportRankGainActiveStanceUIEffect(DefineSportType sportType)
        {
            const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_STANCE_TRIGGER = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_stance_trigger_{0:D2}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_STANCE_TRIGGER, (int)sportType).ToString();
        }

        /// <summary>
        /// スタンス発動中エフェクト
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetActiveStanceUIEffect(DefineSportType sportType)
        {
            const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_STANCE_TRIGGER_LP = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_stance_trigger_{0:D2}_lp";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_STANCE_TRIGGER_LP, (int)sportType).ToString();
        }
        
        /// <summary>
        /// スタンス発動中エフェクト (アイコン裏の炎エフェクト)
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetActiveStanceIconBgEffect(DefineSportType sportType)
        {
            const string BASE_PATH = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_stance_fire{0:D2}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BASE_PATH, (int)sportType).ToString();
        }
        
        /// <summary>
        /// トレーニング演出：スタンス発動演出 アイコン周りのパーティクル
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetStanceActivationIconEffect(DefineSportType sportType)
        {
            const string BASE_PATH = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_sport_stance_ico_{0:D2}_lp";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BASE_PATH, (int)sportType).ToString();
        }
        
        /// <summary>
        /// トレーニング演出：スタンス発動演出 アイコン周りのワンショットパーティクル
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetStanceActivationIconOneShotEffect(DefineSportType sportType)
        {
            const string BASE_PATH = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_sport_stance_activation{0:D2}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BASE_PATH, (int)sportType).ToString();
        }
        
        /// <summary>
        /// トレーニング演出：スタンス発動演出 アイコン周りの炎エフェクト
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetStanceActivationIconFireEffect(DefineSportType sportType)
        {
            const string BASE_PATH = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_stance_heatup_fire{0:D2}";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BASE_PATH, (int)sportType).ToString();
        }
        
        /// <summary>
        /// パラメータ上昇演出：競技レベルアップ演出のジャンルアイコンエフェクト
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetSportRankGainSportTypeIconEffect(DefineSportType sportType)
        {
            const string BASE_PATH = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_sport_stance_ico_rankup{0:D2}_lp";

            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(BASE_PATH, (int)sportType).ToString();
        }
        
        /// <summary>
        /// ヒートアップ効果発動時のヒントバッジに載せるエフェクト
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_EXTRAPARAM_FIRE03 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_extraparam_fire03";

        /// <summary>
        /// 大会終了ダイアログの「優勝」に載せるエフェクト
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_COMPETITIONRESULT_WIN00 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_competitionresult_win00";

        /// <summary>
        /// 優勝本命の競技に載せるエフェクト
        /// </summary>
        public const string SINGLE_SPORT_ICON_ACTIVE_EFF_00 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_icon_active_eff00";
        
        /// <summary>
        /// 優勝本命の競技に載せるエフェクト（大会情報ダイアログ用）
        /// </summary>
        public const string SINGLE_SPORT_ICON_ACTIVE_EFF_01 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_icon_active_eff01";
        
        /// <summary>
        /// 大会TOPの背景エフェクト (SHOWDOWN以外用)
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_SCENARIORACE_TOP00 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_scenariorace_top00";
        
        /// <summary>
        /// 大会結果画面の背景エフェクト
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_COMPETITION_PAPER00 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_competition_paper00";

        /// <summary>
        /// 相談可能ダイアログのアイコン画像まわりに載せるエフェクト
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_PFB_UIEFF_SINGLE_SPORT_NEGOTIABLE_GLITTER01 = SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_negotiable_glitter01";
        
        /// <summary> 相談ダイアログに載せる用タッグトレーニングエフェクト(手前のキラキラ) </summary>
        public const string SINGLE_MODE_PFB_UIEFF_SINGLE_DIALOG_TAGTRAINING_GLITTER_EFF01 = SINGLE_MODE_UI_EFFECT_ROOT + "pfb_uieff_single_dialog_tagtraining_glitter_eff01";

        /// <summary>
        /// ヒートアップ効果詳細ダイアログ：競技ジャンルアイコン周りに出すエフェクト
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioSportStanceActiveEffectPath(DefineSportType sportType)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_icon_active_{0:D2}", (int)sportType).ToString();
        }
        
        /// <summary>
        /// ヒートアップ効果詳細ダイアログ：競技ジャンルアイコン周りに出す炎のエフェクト
        /// </summary>
        /// <param name="sportType"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioSportStanceActiveFireEffectPath(DefineSportType sportType)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_UI_EFFECT_ROOT + "pfb_uieff_single_sport_icon_active_fire{0:D2}", (int)sportType).ToString();
        }

        /// <summary>
        /// 大会演出カットイン用マスク演出
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_COMPETITION_CUTIN_MASK = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_competition_highlight00";

        /// <summary>
        /// 大会ハイライト演出中_テロップ
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_SINGLEMODE_SPORT_COMPETITION_TELOP00 = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_competition_telop00";

        /// <summary>
        /// 大会演出カットイン用ロゴ演出
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_FA_COMPETITION_CUTIN_LOGO = SINGLE_MODE_SCENARIO_SPORT_FLASH_COMBINE_ROOT + "fa_singlemode_sport_highlight_logoshift00";

        #endregion SINGLE_MODE_SCENARIO_SPORT_FLASH
        
        #region TAT

        public const string TAT_SINGLE_EFF_TAG_TRAININGMENU01 = FLASH_COMIBINE_TIMELINE_ROOT + "SingleMode/tat_single_eff_tag_trainingmenu01";
        
        /// <summary> アスリート編：TATルート </summary>
        private const string SINGLE_MODE_SCENARIO_SPORT_TAT_ROOT = FLASH_COMIBINE_TIMELINE_ROOT + "SingleMode/Sport/";
        /// <summary> アスリート編：スタンス効果発動中文字エフェクト </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_STANCE_ACTIVE_TEXT_TAT = SINGLE_MODE_SCENARIO_SPORT_TAT_ROOT + "tat_single_sport_activeeff_text00";

        #endregion

        private const string SINGLE_MODE_SCENARIO_SPORT_ROOT = "Single/ScenarioSport/";

        /// <summary> 相談ダイアログ：キャラクター画像 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_ITEM_CHARACTER_IMAGE = SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_frm_advice_info_00";
        public const string SINGLE_MODE_SCENARIO_SPORT_ITEM_CHARACTER_IMAGE_OLD = SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_frm_advice_info_deco_00";
        public const string SINGLE_MODE_SCENARIO_SPORT_ITEM_UPDATE_NOTICE_TITLE = SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_txt_advice_update_00";
        public const string SINGLE_MODE_SCENARIO_SPORT_ITEM_UPDATE_NOTICE_ICON = SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_frm_recoveryadvice_dialog_00";
        
        /// <summary> 大会汎用ロゴ </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_COMPETITION_COMMON_LOGO = SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_txt_competition_ll_00";
        /// <summary> 「セレモニーライブ」テキスト画像 </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_CEREMONY_LIVE_TITLE = SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_txt_ceremonylive_l_00";
        
        /// <summary>
        /// 相談ダイアログ 競技ジャンルごとの下地取得
        /// </summary>
        public static string GetSingleModeScenarioSportUseItemBaseImage(DefineSportType sportType)
        {
            const string BASE_NAME = "utx_frame_base_training_replace_{0:D2}";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_ROOT + BASE_NAME, (int)sportType).ToString();
        }

        /// <summary>
        /// 大会タイトル画像パス取得
        /// </summary>
        /// <param name="competitionType"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioSportCompetitionTitlePath(int competitionType)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_ROOT + "utx_txt_competition_l_{0:D2}", competitionType).ToString();
        }
        
        /// <summary>
        /// ヒートアップ効果詳細ダイアログ 競技ジャンルごとの下地取得
        /// </summary>
        public static string GetSingleModeScenarioSportStanceInfoWithEffectDetailBaseImage(DefineSportType sportType)
        {
            const string BASE_NAME = "utx_frame_base_stance_{0:D2}_detail_00_sl";
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(SINGLE_MODE_SCENARIO_SPORT_ROOT + BASE_NAME, (int)sportType).ToString();
        }
        
        #region ScriptableObject

        /// <summary>大会リザルト画面のキャラ配置データ </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_COMPETITION_RESULT_ENV_PATH = EnvParamRoot + "SingleMode/ast_prm_singlemode_sport_competition_result";


        #endregion ScriptableObject

        #region Cutt

        /// <summary>
        /// アスリート編の大会演出用カットインのパスを取得
        /// </summary>
        public static string GetSingleModeSportCompetitionCutInPath(int pattern)
        {
            const string SINGLE_MODE_SCENARIO_SPORT_COMPETITION_CUTIN_PATH_FORMAT = "Cutt/CutIn/Set/set_004_{0:D2}_athleteResult/set_004_{0:D2}_athleteResult";

            return TextUtil.Format(SINGLE_MODE_SCENARIO_SPORT_COMPETITION_CUTIN_PATH_FORMAT, pattern);
        }

        /// <summary>
        /// 大会決勝TOP演出用カットイン
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_SPORT_COMPETITION_TOP_SHOW_DOWN_CUTIN_PATH = "Cutt/CutIn/Set/set_005_01_athletefinal/set_005_01_athletefinal";


        #endregion
    }
}