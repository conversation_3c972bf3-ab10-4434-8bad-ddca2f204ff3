namespace Gallop
{
    /// <summary>
    /// 育成追加シナリオBGMセレクター
    /// </summary>
    public class SingleModeBgmSelectorBase
    {
        /// <summary> シナリオ別セレクター名 </summary>
        protected virtual string SelectorName => string.Empty;
        
        /// <summary> 育成TOPのラベル </summary>
        private const string MAIN_VIEW_LABEL_NAME = "0_Top";

        /// <summary>
        /// 育成TOPのセレクターラベル設定
        /// </summary>
        public void SetBgmSelectorLabelFromMainView()
        {
            SetBgmSelectorLabelFromName(MAIN_VIEW_LABEL_NAME);
        }
        
        /// <summary>
        /// セレクターラベル設定
        /// </summary>
        protected void SetBgmSelectorLabelFromName(string labelName)
        {
            AudioManager.Instance.SetBgmSelectorLabelFromName(SelectorName, labelName);
        }
   }
}
