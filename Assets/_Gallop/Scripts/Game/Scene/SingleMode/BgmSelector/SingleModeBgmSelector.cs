namespace Gallop
{
    /// <summary>
    /// 育成追加シナリオBGMセレクター
    /// </summary>
    public sealed class SingleModeBgmSelector
    {
        public static SingleModeBgmSelectorBase CreateInstance()
        {
            switch(WorkDataManager.Instance.SingleMode.GetScenarioId())
            {
                case SingleModeDefine.ScenarioId.Cook: return new SingleModeScenarioCookBgmSelector();
                default: return null;
            }
        }
    }
}
