using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using Gallop.TrainingChallenge;
using System.Linq;

namespace Gallop
{
    public class PartsSingleModeResultTrainingChallengeModel
    {
        public TrainingChallengeResult ResultData { get; private set; }

        /// <summary>
        /// 開催情報
        /// </summary>
        public TrainingChallenge.TrainingChallengeDefine.State State { get; private set; }

        /// <summary>
        /// スコアViewのパラメータ
        /// </summary>
        public PartsSingleModeResultTrainingChallengeScoreView.Param ViewParam { get; private set; }
        /// <summary>
        /// 獲得したスコア情報が入っている
        /// </summary>
        public TrainingChallengeScoreSummary ScoreSummary { get; private set; }

        /// <summary>
        /// 合計スコア
        /// </summary>
        public int TotalScore { get; private set; }

        /// <summary>
        /// 解放された次試験マスタ
        /// </summary>
        public MasterTrainingChallengeExam.TrainingChallengeExam NextExamMaster { get; private set; }

        /// <summary>
        /// 試験名
        /// </summary>
        public string ExamFeatureName { get; private set; }

        /// <summary>
        /// メインロゴパス
        /// </summary>
        public string LogoPath => ResourcePath.TRAINING_CHALLENGE_LOGO;

        /// <summary>
        /// 今回ハイスコアを更新した場合true
        /// </summary>
        public bool IsNew { get; private set; }

        /// <summary>
        /// 今回のランク
        /// </summary>
        public TrainingChallengeDefine.ResultType ResultRank { get; private set; }

        /// <summary>
        /// 勝利演出が必要な場合true
        /// </summary>
        public bool NeedsWinAnimation { get; private set; }

        /// <summary>
        /// 報酬を表示する必要があるか
        /// </summary>
        public bool NeedsShowReward => !ResultData.reward_list.IsNullOrEmpty() && !IsPlayedReawrd;

        /// <summary>
        /// プレゼントに送られるものがあれば
        /// </summary>
        public bool CanShowRewardNotice => ResultData.reward_summary_info.add_present_num > 0;

        /// <summary>
        /// 表示報酬リスト
        /// </summary>
        public List<WorkDataUtil.RewardItemInfo> RewardItemInfos =>
            WorkDataUtil.CreateRewardSummaryInfoByDisplayInfo(ResultData.reward_list);

        /// <summary>
        /// 報酬演出が再生済みの場合true
        /// </summary>
        public bool IsPlayedReawrd { get; set; }


        public void Setup(TrainingChallengeResult resultData, int trainedCharaId)
        {
            ResultData = resultData;

            var masterManager = MasterDataManager.Instance;
            var master = masterManager.masterTrainingChallengeMaster;

            if (ResultData == null)
            {
                switch (master.Status)
                {
                    // ResultがNullで本戦期間外の場合は"本戦終了ダイアログ"
                    case TrainingChallengeDefine.EventStatus.Result:
                        State = TrainingChallenge.TrainingChallengeDefine.State.MainEnded;
                        break;

                    // ResultがNullでイベント自体が期間外の場合は"イベント終了ダイアログ"
                    case TrainingChallengeDefine.EventStatus.None:
                        State = TrainingChallenge.TrainingChallengeDefine.State.EventEnded;
                        break;

                    default:
                        State = TrainingChallenge.TrainingChallengeDefine.State.EventEnded;
                        break;
                }
            }
            else
            {
                // リザルトデータが存在する場合は必ず開催中ステイタスで進める
                State = TrainingChallenge.TrainingChallengeDefine.State.Open;
            }

            Debug_StateChange();

            // イベントが終了しているときは何もしない
            if (State != TrainingChallenge.TrainingChallengeDefine.State.Open)
            {
                return;
            }

            ScoreSummary = resultData.score_summary;

            var userInfo = resultData.event_user_info;

            // 更新
            var work = WorkDataManager.Instance.TrainingChallengeData;
            work.UpdateUserInfo(userInfo);
            work.UpdateExamInfos(resultData.exam_infos);

            // 選択されている試験情報
            var selectedExamEntity = work.ExamInfoRepository.FindEntity(ResultData.exam_id);

            // 今回ハイスコアを更新したか
            if (selectedExamEntity.HasHighScore)
            {
                // 今回の育成キャラとハイスコアキャラが一致すれば
                IsNew = trainedCharaId == selectedExamEntity.HighScoreTrainedChara.trained_chara_id;
            }

            // 距離適正名
            ExamFeatureName = selectedExamEntity.Master.FeatureDefine.GetText();

            // 勝利数を求める
            var raceHistoryInfos = WorkDataManager.Instance.SingleMode.RaceHistoryInfoList;
            var raceHistoryPrograms = raceHistoryInfos
                .Where(info => info.ResultRank == SingleModeDefine.RACE_RANK_FIRST)
                .Select(info => masterManager.masterSingleModeProgram.Get(info.ProgramId));

            // 各グレードの出走数を求める
            var raceNumDict = raceHistoryPrograms
                .GroupBy(program => program.RaceGrade)
                .ToDictionary(group => group.Key, group => group.Count());

            // 獲得スキル (Key:レアリティ, Value:スキルマスタリスト)
            var trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(trainedCharaId);
            var skillsDict = trainedChara.AcquiredSkillArray
                .Select(skill => skill.MasterData)
                .GroupBy(skill => (SkillDefine.SkillRarity)skill.Rarity)
                .ToDictionary(group => group.Key, group => group.ToList());

            // 今回のランク
            ResultRank = selectedExamEntity.Master.GetResultType(resultData.score_summary.total);

            // 優を取得していたら演出を再生する
            // しかし前回も「優」の場合は再生しない
            if (!selectedExamEntity.PrevResult.IsExcellent())
            {
                NeedsWinAnimation = ResultRank.IsExcellent();
            }

            // Tpブーストを使用したかどうか
            var useTpBoost = resultData.training_challenge_mode == (int)TrainingChallengeDefine.EventConditionOnSingleMode.ExamWithBoost;


#if CYG_DEBUG // 強制的に演出関連を変更するデバッグ

            if (DebugPageTrainingChallenge.EnableForceResultWinAnimation)
            {
                ResultRank = TrainingChallengeDefine.ResultType.Excellent;
                NeedsWinAnimation = true;

                // 報酬が無いときはダミーを作る
                if (ResultData.reward_list.IsNullOrEmpty())
                {
                    ResultData.reward_list = new DisplayRewardInfo[]
                        {
                            new DisplayRewardInfo{ item_type = (int)GameDefine.ItemCategory.TRAINING, item_id = 43, item_num = 9},
                            new DisplayRewardInfo{ item_type = (int)GameDefine.ItemCategory.TRAINING, item_id = 1, item_num = 99},
                            new DisplayRewardInfo{ item_type = (int)GameDefine.ItemCategory.TRAINING, item_id = 43, item_num = 999},
                            new DisplayRewardInfo{ item_type = (int)GameDefine.ItemCategory.TRAINING, item_id = 1, item_num = 9999},
                            new DisplayRewardInfo{ item_type = (int)GameDefine.ItemCategory.TRAINING, item_id = 43, item_num = 1234},
                        };
                }
            }

            if (DebugPageTrainingChallenge.EnableForceResultForceNextExamNotice)
            {
                resultData.next_exam_id = 2;
            }

            if (DebugPageTrainingChallenge.ResultForceSetResultType != null)
            {
                ResultRank = DebugPageTrainingChallenge.ResultForceSetResultType.Value;
            }

            if (DebugPageTrainingChallenge.EnableForceResultTpBoost)
            {
                useTpBoost = true;
            }

            if (DebugPageTrainingChallengeModelViewer.Enable)
            {
                ResultRank = TrainingChallengeDefine.ResultType.Excellent;
                NeedsWinAnimation = true;
            }

#endif


            ViewParam = new PartsSingleModeResultTrainingChallengeScoreView.Param
                { 
                    ScoreSummary = resultData.score_summary,
                    RankScore = new TrainingChallengeScore(ScoreSummary.rank_score),
                    BaseRankScore = new TrainingChallengeScore(ScoreSummary.base_rank_score),
                    TotalScore = new TrainingChallengeScore(ScoreSummary.total),
                    TotalCoin = new TrainingChallengeScore(userInfo.event_coin),
                    AddCoin = new TrainingChallengeScore(ScoreSummary.add_event_coin),
                    HighScore = selectedExamEntity.HighScore,
                    RaceNumDict = raceNumDict,
                    SkillsDict = skillsDict,
                    IsNew = IsNew,
                    ResultType = ResultRank,
                    NeedsWinAnimation = NeedsWinAnimation,
                    NeedTpBoostBadge = useTpBoost,
                };

            if (selectedExamEntity.HighScoreTrainedChara != null)
            {
                var highScoreCharaWorkData = new WorkTrainedCharaData.TrainedCharaData(selectedExamEntity.HighScoreTrainedChara);
                ViewParam.CharaButtonInfo = CharacterButtonInfo.CreateTrained(highScoreCharaWorkData, dispLongTapDetail: true);
            }

            ScoreSummary = resultData.score_summary;
            TotalScore = resultData.score_summary.total;

            // 新しい試験のマスタが解放されたときは解放通知を出すためにマスタを取得する
            if (resultData.next_exam_id > 0)
            {
                NextExamMaster = masterManager.masterTrainingChallengeExam.Get(resultData.next_exam_id);
            }
        }


        /// <summary>
        /// デバッグ
        /// 強制的に開催情報を変更する
        /// </summary>
        [Conditional("CYG_DEBUG")]
        private void Debug_StateChange()
        {
#if CYG_DEBUG
            State = DebugPageTrainingChallenge.TrainingEndState;
#endif
        }
    }

}