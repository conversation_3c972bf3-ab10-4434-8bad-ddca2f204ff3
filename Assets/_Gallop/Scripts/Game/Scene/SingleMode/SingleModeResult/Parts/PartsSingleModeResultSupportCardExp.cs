using UnityEngine;
using System.Collections;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeResultSupportCardExp : MonoBehaviour
    {
        private const float COUNTUP_DURATION = 0.8f;

        private static readonly Vector2 LEVEL_LABEL_POSITION = new Vector2(-35f, -147.5f);
        private static readonly Vector2 LEVEL_LABEL_POSITION_S = new Vector2(-22f, -125f);
        private static readonly Vector3 LEVEL_LABEL_SCALE = Vector3.one;
        private static readonly Vector3 LEVEL_LABEL_SCALE_S = Vector3.one;

        private static readonly Vector2 EXP_GAUGE_POSITION = new Vector2(0f, -180f);
        private static readonly Vector2 EXP_GAUGE_POSITION_S = new Vector2(0f, -157f);
        private static readonly Vector2 EXP_GAUGE_SIZE = new Vector2(184f, 16f);
        private static readonly Vector2 EXP_GAUGE_FILL_SIZE = new Vector2(180f, 16f);
        private static readonly Vector2 EXP_GAUGE_SIZE_S = new Vector2(142f, 16f);
        private static readonly Vector2 EXP_GAUGE_FILL_SIZE_S = new Vector2(138f, 16f);

        private static readonly Vector2 MAX_SPRITE_POSITION = new Vector2(56f, -147.5f);
        private static readonly Vector2 MAX_SPRITE_POSITION_S = new Vector2(45f, -128f);
        private static readonly Vector3 MAX_SPRITE_SCALE = Vector3.one;
        private static readonly Vector3 MAX_SPRITE_SCALE_S = new Vector3(0.8f, 0.8f, 1f);

        #region SerializeField

        [SerializeField]
        private CharacterButton _characterButton = null;

        [SerializeField]
        private RectTransform _levelLabelRoot = null;
        [SerializeField]
        private TextCommon _levelLabel = null;
        [SerializeField]
        private TweenAnimationTimelineComponent _levelLabelTimeline = null;

        [SerializeField]
        private RectTransform _expGaugeRoot = null;
        [SerializeField]
        private RectTransform _expGaugeMaskRoot = null;
        [SerializeField]
        private RectTransform _expGaugeFillRoot = null;
        [SerializeField]
        private GaugeCommon _expGauge = null;
        public GaugeCommon ExpGauge { get { return _expGauge; } }

        [SerializeField]
        private RectTransform _maxSpriteRoot = null;
        [SerializeField]
        private ImageCommon _maxSprite = null;

        #endregion

        #region publicプロパティ

        public bool IsMax { get; private set; }
        public bool IsCountUp { get; private set; }

        #endregion

        #region private変数,プロパティ

        private WorkTrainedCharaData.SupportCardData _supportCard;

        // EXP開始値
        private int _beforeExp;
        // EXP終了値
        private int _afterExp;

        private FlashPlayer _maxIconFlash = null;
        private FlashToUgui _maxIconFlashtoUgui = null;
        private int _labelLevel = 0;

        private IconBase.SizeType _iconSizeType;

        #endregion

        private Coroutine _countUpRoutine;

        public void Initialize(WorkTrainedCharaData.SupportCardData supportCard,int afterExp,int beforeExp, IconBase.SizeType iconSizeType)
        {
            _supportCard = supportCard;
            _afterExp = afterExp;
            _beforeExp = beforeExp;
            _iconSizeType = iconSizeType;

            InitializeCharacterButton(supportCard);

            InitializeFlashIconMax();

            InitializeExp(supportCard, _beforeExp);
        }

        private void InitializeCharacterButton(WorkTrainedCharaData.SupportCardData supportCard)
        {
            var buttonInfo = new CharacterButtonInfo(supportCard.SupportCardId, CharacterButtonInfo.IdTypeEnum.Support);
            buttonInfo.IconSizeType = _iconSizeType;
            _characterButton.Setup(buttonInfo);
        }

        private void InitializeFlashIconMax()
        {
            _maxIconFlash = FlashLoader.LoadOnView(ResourcePath.COMMON_ICON_MAX, _maxSprite.transform.parent);
            _maxIconFlash.GetMotion("MOT_root").SetSortOffset(1);
        }

        private void InitializeExp(WorkTrainedCharaData.SupportCardData supportCard, int exp)
        {
            // レベル表示
            SetTextLevel(supportCard, exp);

            // ゲージ表示設定
            SetExpGauge(supportCard, exp);

            // 既にレベル最大
            var level = GetSupportCardLevel(supportCard, exp);
            if (IsLevelMax(level))
            {
                IsMax = true;
                _maxIconFlash?.Play("in_end");
                _levelLabel.FontColor = FontColorType.Plus;
            }
        }

        private void SetTextLevel(WorkTrainedCharaData.SupportCardData supportCard, int exp)
        {
            // SizeTypeに合わせてUIを調整
            var isIconSizeS = _iconSizeType == IconBase.SizeType.SupportCard_DeckSelect;
            _levelLabelRoot.localPosition = isIconSizeS ? LEVEL_LABEL_POSITION_S : LEVEL_LABEL_POSITION;
            _levelLabelRoot.localScale = isIconSizeS ? LEVEL_LABEL_SCALE_S : LEVEL_LABEL_SCALE;
            _maxSpriteRoot.localPosition = isIconSizeS ? MAX_SPRITE_POSITION_S : MAX_SPRITE_POSITION;
            _maxSpriteRoot.localScale = isIconSizeS ? MAX_SPRITE_SCALE_S : MAX_SPRITE_SCALE;

            var level = GetSupportCardLevel(supportCard, exp);
            _levelLabel.text = TextId.Common0127.Text() + level.ToString();

            // 表示中のレベルを保持
            _labelLevel = level;
        }

        public void CountUpExp()
        {
            //すでにMaxレベルならカウントアップ演出は行わない
            if(IsMax)
            {
                //Maxアイコンの描画順だけ調整
                InitFlashToUgui();
            }
            else
            {
                _countUpRoutine = StartCoroutine(CountUpRoutine());
            }
        }

        private IEnumerator CountUpRoutine()
        {
            IsCountUp = true;

            var currentExp = _beforeExp;
            var time = 0f;
            while (time < COUNTUP_DURATION)
            {
                currentExp = GetCountUpExp(_beforeExp, _afterExp, time);

                UpdateExp(_supportCard, currentExp);

                yield return UIManager.WaitForEndOfFrame;

                time += Time.deltaTime;
            }
            currentExp = _afterExp;
            UpdateExp(_supportCard, currentExp);

            InitFlashToUgui();

            IsCountUp = false;
        }

        public void SkipCountUp()
        {
            InitFlashToUgui();//73009　Skip時に生成されてないので終了時のフェードが効いていないケースに対応
            if(_countUpRoutine != null) //演出中なら停止して設定
            {
                StopCoroutine(_countUpRoutine);
                UpdateExp(_supportCard, _afterExp);
                IsCountUp = false;
            }
            else //そうでなければそのまま表示更新
            {
                UpdateExp(_supportCard, _afterExp);
            }
        }

        private int GetCountUpExp(int beforeExp, int afterExp, float time)
        {
            return Mathf.FloorToInt(Mathf.SmoothStep(beforeExp, afterExp, time / COUNTUP_DURATION));
        }

        private void UpdateExp(WorkTrainedCharaData.SupportCardData supportCard, int exp)
        {
            // レベルアップ演出計算
            CalcLevelUp(supportCard, exp);

            SetExpGauge(supportCard, exp);
        }

        private void SetExpGauge(WorkTrainedCharaData.SupportCardData supportCard, int exp)
        {
            // SizeTypeに合わせてUIを調整
            var isIconSizeS = _iconSizeType == IconBase.SizeType.SupportCard_DeckSelect;
            _expGaugeRoot.localPosition = isIconSizeS ? EXP_GAUGE_POSITION_S : EXP_GAUGE_POSITION;
            _expGaugeRoot.sizeDelta = isIconSizeS ? EXP_GAUGE_SIZE_S : EXP_GAUGE_SIZE;
            _expGaugeMaskRoot.sizeDelta = isIconSizeS ? EXP_GAUGE_FILL_SIZE_S : EXP_GAUGE_FILL_SIZE;
            _expGaugeFillRoot.sizeDelta = isIconSizeS ? EXP_GAUGE_FILL_SIZE_S : EXP_GAUGE_FILL_SIZE;

            // 表示レベルを取得
            var level = GetSupportCardLevel(supportCard, exp);

            // 最大レベルならばゲージを最大にして終了
            if (IsLevelMax(level))
            {
                SetMaxExpGauge(supportCard, exp);
                return;
            }


            // 表示レベルの経験値を取得
            var currentLevelTotalExp = GetLevelTotalExp(supportCard, level);

            // 次のレベルの経験値を取得
            var nextLevel = level + 1;
            var nextLevelTotalExp = GetLevelTotalExp(supportCard, nextLevel);

            //現在の経験値と次への必要経験値から現在Lvに必要な経験値をそれぞれ引くことで進捗を計算する
            var gaugeNow = exp - currentLevelTotalExp;
            var gaugeMax = nextLevelTotalExp - currentLevelTotalExp;
            _expGauge.Set(gaugeNow, gaugeMax);

            //経験値が0だった場合はGaugeCommon側でImageを非表示にしてしまうためMaskに使用しているここでは問題がある (Maskが切れて全部見えてしまう)
            //なのでゲージごと非アクティブにすることで対応する
            _expGauge.GaugeImage.SetActiveWithCheck(!Mathf.Approximately(gaugeNow, 0f));
        }

        private void SetMaxExpGauge(WorkTrainedCharaData.SupportCardData supportCard, int exp)
        {
            _expGauge.Set(1f, 1f);
            SetTextLevel(supportCard, exp);
        }

        private void CalcLevelUp(WorkTrainedCharaData.SupportCardData supportCard, int exp)
        {
            // 表示レベルを取得
            var level = GetSupportCardLevel(supportCard, exp);

            // 表示中のレベルと異なればレベルアップ
            if(_labelLevel != level)
            {
                // レベル表示設定
                SetTextLevel(supportCard, exp);

                // レベルアップ演出
                OnUpdateLevel(level);
            }
        }

        /// <summary>
        /// 獲得ルーチン中のレベルアップ演出
        /// </summary>
        private void OnUpdateLevel(int level)
        {
            _levelLabelTimeline.Play();
            _levelLabel.FontColor = FontColorType.Plus;
            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_RESULT_7);

            // 最大レベル
            var limitLevel = GetSupportCardLimitLevel(_supportCard);
            if (level >= limitLevel)
            {
                IsMax = true;
                _maxIconFlash?.Play("in");
            }
        }


        public void InitFlashToUgui()
        {
            if (_maxIconFlash != null && _maxIconFlashtoUgui == null)
            {
                _maxIconFlashtoUgui = _maxIconFlash.transform.parent.gameObject.AddComponent<FlashToUgui>();
                _maxIconFlashtoUgui.InitializeAndCreateClone(_maxIconFlash.gameObject);
            }
        }


        /// <summary>
        /// 最大レベルを取得
        /// </summary>
        /// <param name="supportCardData"></param>
        /// <returns></returns>
        private int GetSupportCardLimitLevel(WorkTrainedCharaData.SupportCardData supportCardData)
        {
            if(supportCardData == null)
            {
                return 0;
            }

            var masterSupportCardLimit = MasterDataManager.Instance.masterSupportCardLimit.Get(supportCardData.MasterSupportCardData.Rarity);
            if(masterSupportCardLimit == null)
            {
                return 0;
            }
            var limitLevel = masterSupportCardLimit.GetMaxLevelByLimitBreakCount(supportCardData.LimitBreakCount);
            return limitLevel;
        }

        /// <summary>
        /// 経験値からレベルを取得
        /// </summary>
        /// <param name="supportCardData"></param>
        /// <param name="exp"></param>
        /// <returns></returns>
        private int GetSupportCardLevel(WorkTrainedCharaData.SupportCardData supportCardData, int exp)
        {
            if (supportCardData == null)
            {
                return 1;
            }

            var masterSupportCardData = supportCardData.MasterSupportCardData;
            if (masterSupportCardData == null)
            {
                return 1;
            }

            var masterSupportCardLevel = MasterDataManager.Instance.masterSupportCardLevel.GetSupportCardLevelData(exp, masterSupportCardData.Rarity);
            if(masterSupportCardLevel == null)
            {
                return 1;
            }
            return masterSupportCardLevel.Level;
        }

        /// <summary>
        /// 指定レベルに必要な経験値を取得
        /// </summary>
        /// <param name="supportCardData"></param>
        /// <param name="level"></param>
        /// <returns></returns>
        private int GetLevelTotalExp(WorkTrainedCharaData.SupportCardData supportCardData, int level)
        {
            if(supportCardData == null)
            {
                return 1;
            }

            var masterSupportCardData = supportCardData.MasterSupportCardData;
            if (masterSupportCardData == null)
            {
                return 1;
            }

            var masterSupportCardLevel = MasterDataManager.Instance.masterSupportCardLevel.GetSupportCardLevelDataByLevel(level, masterSupportCardData.Rarity);
            if(masterSupportCardLevel == null)
            {
                return 1;
            }
            return masterSupportCardLevel.TotalExp;
        }


        /// <summary>
        /// レベル最大判定
        /// </summary>
        /// <param name="level"></param>
        /// <returns></returns>
        private bool IsLevelMax(int level)
        {
            var limitLevel = GetSupportCardLimitLevel(_supportCard);
            return level >= limitLevel;
        }
    }
}