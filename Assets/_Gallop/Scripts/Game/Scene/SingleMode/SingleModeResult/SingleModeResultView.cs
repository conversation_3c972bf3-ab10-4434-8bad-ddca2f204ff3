using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Gallop.Tutorial;

namespace Gallop
{
    /// <summary>
    /// シングルモード結果画面
    /// 最終結果の表示、殿堂入りの登録はSingleModeSaveViewで行う
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeResultView : ViewBase
    {
        [field: SerializeField]
        public SingleModeResultSequence.SingleModeResultSequenceParts _sequenceParts = null;

        [field: SerializeField]
        public SingleModeResultSequence.SingleModeResultCommonUIParts CommonUIParts { private set; get; }
    }

    public sealed partial class SingleModeResultViewController : ViewControllerBase<SingleModeResultView>
    {

        #region SingleModeResultAPI

        /// <summary>
        /// 育成リザルトAPI
        /// </summary>
        private static class SingleModeResultAPI
        {
            public static void SendSingleModeFinishRequest(int factorLotteryId, System.Action<SingleModeResultDataContainer> callback)
            {
                var workManager = WorkDataManager.Instance;
                var resultDataContainer = new SingleModeResultDataContainer();

#if CYG_DEBUG
                //リザルト強制再生
                if (SingleModeResultDirectSceneController.ExecuteDirectPlayResult)
                {
                    resultDataContainer = SingleModeResultDirectSceneController.DataContainer;
                    callback?.Invoke(resultDataContainer);
                    return;
                }
                else if (DebugPageSingleModeResult.IsExecuteForcePlayResult())
                {
                    resultDataContainer.InitializeCharacterData();
                    resultDataContainer.InitializeExpData(true);
                    resultDataContainer.DebugGenerateResultData(
                        WorkDataManager.Instance.SingleMode.Character.CharaId,
                        WorkDataManager.Instance.SingleMode.Character.CardId);
                    resultDataContainer.OverrideResultData();
                    callback?.Invoke(resultDataContainer);
                    return;
                }
#endif
                resultDataContainer.InitializeCharacterData();
                resultDataContainer.InitializeExpData(true);

                var prevUnlockMainStoryIdList = MainStoryUtil.GetUnlockMainStoryIdList();
                var prevCharacterStoryIdList = WorkDataManager.Instance.CharacterStoryData.GetUnlockLoveRankStoryIdList();

                // 育成終了API
                SingleModeAPI.SendFinish(false, factorLotteryId, OnSendSuccess);

                // local func : 通信成功時
                void OnSendSuccess(SingleModeAPI.OnSendFinishSuccessDataContainer dataContainer)
                {
                    //サポートカードのオーナーIDが0でも自分自身でもないものはフレンド
                    var supportCardOwnerViewerIdList = dataContainer.charaInfo.support_card_array.Select(card => card.owner_viewer_id).Where(id => id != 0);
                    resultDataContainer.FriendViewId = supportCardOwnerViewerIdList.FirstOrDefault(viewerId => viewerId != WorkDataManager.Instance.UserData.ViewerId);

                    // Work反映
                    var singleModeCharaInfo = dataContainer.charaInfo;
                    workManager.SingleMode.ApplyCharacter(singleModeCharaInfo);

                    // 親愛度
                    if (dataContainer.lovePointInfo != null)
                    {
                        // Work更新
                        workManager.CharaData.UpdateLovePoint(dataContainer.lovePointInfo);
                    }

                    //獲得経験値
                    resultDataContainer.RewardItemInfoList = WorkDataUtil.SetRewardSummaryInfo(dataContainer.rewardItemInfo);
                    foreach (var data in dataContainer.supportCardDataArray)
                    {
                        workManager.SupportCardData.UpdateSupportCardData(data);
                    }

                    // ファン数
                    workManager.CharaData.AddFan(WorkDataManager.Instance.SingleMode.Character.CharaId, dataContainer.charaInfo.fans);
                    WorkDataManager.Instance.UserData.AddFanNum((ulong)dataContainer.charaInfo.fans);
                    resultDataContainer.InitializeExpData(false);

                    //月間総獲得サークルファン数
                    resultDataContainer.CirclePoint = dataContainer.circlePoint;

                    //獲得ファン数
                    resultDataContainer.AddFanNum = (ulong)dataContainer.charaInfo.fans;

                    // 保存された殿堂入りデータをワークデータに反映
                    WorkDataManager.Instance.TrainedCharaData.UpdateAll(dataContainer.trainedChara);

                    // 名鑑情報を更新
                    WorkDataManager.Instance.DirectoryData.UpdateCardNum(dataContainer.directoryCardNum);
                    WorkDataManager.Instance.DirectoryData.UpdateCardData(dataContainer.directoryCardArray);
                    
                    // シナリオレコード情報を更新
                    WorkDataManager.Instance.ScenarioRecordData.UpdateData(dataContainer.scenarioRecordArray);
                    // シナリオレコード報酬
                    resultDataContainer.ScenarioRecordRewardItemInfoList = WorkDataUtil.CreateRewardSummaryInfoByDisplayInfo(dataContainer.scenarioRecordRewardArray);
                    if (!dataContainer.scenarioRecordArray.IsNullOrEmpty())
                    {
                        var highestScenarioRecordData = WorkDataManager.Instance.ScenarioRecordData.GetHighestScenarioRecordData((int) workManager.SingleMode.GetScenarioId());
                        if (highestScenarioRecordData != null && highestScenarioRecordData.TrainedCharaData.Id ==
                            dataContainer.trainedCharaId)
                        {
                            // シナリオレコード新記録更新
                            var afterScenarioRecord = highestScenarioRecordData.TrainedCharaData.RankScore;
                            resultDataContainer.ScenarioRecord = afterScenarioRecord;
                            WorkDataManager.Instance.ScenarioRecordData.UpdateMaxRecordData(
                                (int) workManager.SingleMode.GetScenarioId(), afterScenarioRecord);
                        }
                    }

                    // 限定セールス更新
                    WorkDataManager.Instance.LimitedSalesData.Update(dataContainer.limitedShopInfo);

                    // キャラデータ更新
                    WorkDataManager.Instance.CharaData.UpdateCharaData(dataContainer.updateUserCharaInfo);

                    // ホームのショップボタンのバッジ更新
                    WorkDataManager.Instance.LimitedSalesData.UpdateReleaseItemFlag(dataContainer.releaseItemFlag);

                    // トレーナーノートプロフィール更新
                    TempData.Instance.CharacterNoteData.AddProfileNewData(dataContainer.newCharaProfileArray);

                    resultDataContainer.InitializeTrainedCharacterData(dataContainer.trainedCharaId);

                    //差集合で新しく読めるようになったStoryIdを保持しておく
                    resultDataContainer.UnlockMainStoryIdList = MainStoryUtil.GetUnlockMainStoryIdList().Except(prevUnlockMainStoryIdList).ToList();
                    resultDataContainer.UnlockCharacterStoryIdList = WorkDataManager.Instance.CharacterStoryData.GetUnlockLoveRankStoryIdList().Except(prevCharacterStoryIdList).ToList();

                    resultDataContainer.CampaignIdArray = dataContainer.campaignIdArray;
                    resultDataContainer.SubscriptionEffectIdArray = dataContainer.subscriptionEffectIdArray;

                    // ストーリーイベントが開催中なら
                    if (dataContainer.storyEventInfo != null)
                    {
                        // ストーリーイベントの報酬を付与
                        WorkDataUtil.SetRewardSummaryInfo(dataContainer.storyEventInfo.reward_summary_info);
                        WorkDataManager.Instance.StoryEventData.UpdateSingleModeFinish(dataContainer.storyEventInfo);
                        WorkDataManager.Instance.RouletteDerbyData.Update(dataContainer.storyEventInfo);
                    }

                    // ストーリーイベントが開催中ならこの後ストーリーイベント用の表示をするための情報を保持しておく
                    resultDataContainer.InitializeStoryEventInfo(dataContainer.storyEventInfo);

                    // ハードモード
                    if (dataContainer.difficultyDataSet != null)
                    {
                        // ハードモード報酬を付与
                        var difficultyRewardItemInfoList = WorkDataUtil.SetRewardSummaryInfo(dataContainer.difficultyDataSet.type1_reward_info);
                        WorkDataUtil.SetRewardSummaryInfo(dataContainer.difficultyDataSet.type2_reward_info);

                        // Work反映前の情報をコピー
                        var workDifficultyInfoBefore = WorkDataManager.Instance.SingleMode.GetCopyDifficultyInfo(WorkDataManager.Instance.SingleMode.Character.DifficultyId);
                        // Workにハードモード情報更新
                        WorkDataManager.Instance.SingleMode.ApplyDifficultyInfo(dataContainer.difficultyDataSet.single_mode_difficulty_info_array);

                        resultDataContainer.InitializeDifficultyInfo(dataContainer.difficultyDataSet, difficultyRewardItemInfoList, workDifficultyInfoBefore);
                    }

                    // ファルコモード
                    resultDataContainer.InitializeFanRaidData(dataContainer.fanRaidEventInfo);
                    if (dataContainer.fanRaidEventInfo != null)
                    {
                        // 報酬を付与
                        WorkDataUtil.SetRewardSummaryInfo(dataContainer.fanRaidEventInfo.individual_reward_summary_info);
                        WorkDataManager.Instance.FanRaidData.UpdateAtSingleModeResult(dataContainer.fanRaidEventInfo);
                    }

                    // 特別移籍:殿堂入りウマ娘の数が増減したので特別移籍のWorkDataを更新（イベント期間外なら軽い処理のみ）
                    workManager.TransferRotationData.Update(dataContainer.transferRotationInfo);
                    workManager.TransferEventData.Update(dataContainer.transferEventInfo);

                    // ウマさんぽキャンペーン
                    resultDataContainer.InitializeCampaignWalking(dataContainer.CampaignWalkingGaugeInfo);
                    WorkDataManager.Instance.CampaignWalkingData.Apply(dataContainer);

                    // エイプリルフールイベント: マップイベント
                    resultDataContainer.InitializeMapEvent(dataContainer.MapEventGaugeInfo);
                    WorkDataManager.Instance.MapEventData.UpdateGaugeInfo(dataContainer);

                    // 因子研究
                    resultDataContainer.InitializeFactorResearch(dataContainer.FactorResearchDataSet);
                    WorkDataManager.Instance.FactorResearchData.Apply(dataContainer);

                    // 収集イベント
                    resultDataContainer.InitializeCollectRaid(dataContainer.CollectRaidInfo);
                    WorkDataManager.Instance.CollectRaidData.UpdateAtSingleModeResult(dataContainer.CollectRaidInfo);

                    // 育成で親愛度大獲得キャンペーン
                    WorkDataManager.Instance.CampaignData.SingleModeLovePointUpCampaignData.UpdateData(dataContainer.SingleModeLovePointUpCampaignResponseData);
                    
                    // 育成チャレンジ
                    var trainingChallenge = dataContainer.TrainingChallengeResult;

#if CYG_DEBUG       // 育成チャレンジデバッグ機能
                    trainingChallenge = DebugPageTrainingChallenge.CreateDebugTrainingChallengeResult(trainingChallenge);
#endif
                    if (trainingChallenge != null)
                    {
                        WorkDataUtil.SetRewardSummaryInfo(trainingChallenge.reward_summary_info);
                    }

                    resultDataContainer.TrainingChallengeResult = trainingChallenge;

                    // レンタルデッキ用ショップ導線表示のパラメータ
                    resultDataContainer.RentalDeckProductInfo = dataContainer.RentalDeckProductInfo;

                    resultDataContainer.SetupForHide();

                    callback?.Invoke(resultDataContainer);
                }
            }


            public static void SendSingleModeFinishRequest_Tutorial(System.Action<SingleModeResultDataContainer> callback)
            {
                var workManager = WorkDataManager.Instance;

                var resultDataContainer = new SingleModeResultDataContainer();
                resultDataContainer.InitializeCharacterData();
                resultDataContainer.InitializeExpData(true);
#if CYG_DEBUG
                // 強制チュートリアルデバッグ
                if (DebugPageSingleModeTutorial.IsForcedTutorial)
                {
                    var charaId = WorkDataManager.Instance.SingleMode.Character.CharaId;
                    var trainedCharaData = WorkDataManager.Instance.TrainedCharaData.List.Find(a => a.CharaId == charaId); //殿堂入りキャラはカード選択時に付与されている
                    if (trainedCharaData == null) trainedCharaData = WorkDataManager.Instance.TrainedCharaData.List.OrderByDescending(_ => _.RankScore).First(); // 見つからないときは保険でなにか入れる（デバッグ機能からの確認など）
                    resultDataContainer.RewardItemInfoList = new List<WorkDataUtil.RewardItemInfo>();
                    resultDataContainer.InitializeExpData(false);
                    resultDataContainer.InitializeTrainedCharacterData(trainedCharaData.Id);
                    callback?.Invoke(resultDataContainer);
                    return;
                }
#endif
                // 育成チュートリアルは専用API(殿堂入りは初回に付与済みで報酬のみ付与）
                var req = new TutorialSingleModeFinishRequest();
                req.Send(res =>
                {
                    // 親愛度
                    if (res.data.love_point_info != null)
                    {
                        workManager.CharaData.UpdateLovePoint(res.data.love_point_info);
                    }

                    //獲得経験値
                    resultDataContainer.RewardItemInfoList = WorkDataUtil.SetRewardSummaryInfo(res.data.reward_item_info);
                    foreach (var data in res.data.support_card_data_array)
                    {
                        workManager.SupportCardData.UpdateSupportCardData(data);
                    }

                    var charaId = workManager.SingleMode.Character.CharaId;
                    // ファン数
                    var addFan = res.data.update_user_chara_info.fan - workManager.CharaData.Get(charaId).FanNum;
                    workManager.CharaData.AddFan(charaId, (int)addFan);
                    workManager.UserData.AddFanNum(addFan);
                    resultDataContainer.InitializeExpData(false);
                    //獲得ファン数
                    resultDataContainer.AddFanNum = res.data.update_user_chara_info.fan;

                    // 保存された殿堂入りデータをワークデータに反映
                    workManager.TrainedCharaData.UpdateAll(res.data.trained_chara);

                    // 名鑑情報を更新
                    workManager.DirectoryData.UpdateCardData(res.data.directory_card_array);

                    // 限定セールス更新
                    workManager.LimitedSalesData.Update(res.data.limited_shop_info);

                    // キャラデータ更新
                    workManager.CharaData.UpdateCharaData(res.data.update_user_chara_info);

                    // トレーナーノートプロフィール更新
                    TempData.Instance.CharacterNoteData.AddProfileNewData(res.data.new_chara_profile_array);

                    resultDataContainer.InitializeTrainedCharacterData(res.data.trained_chara_id);

                    resultDataContainer.SetupForHide();

                    callback?.Invoke(resultDataContainer);
                });
            }


            public static void SendFactorSelectRequest(System.Action<SingleModeFactorSelectCommon, SingleModeFactorOrderCommon> callback)
            {
                // すでにWorkDataに因子情報を持っている
                if (WorkDataManager.Instance.SingleMode.ResumeFactorSelect != null)
                {
                    // 通信後の処理へ
                    callback(WorkDataManager.Instance.SingleMode.ResumeFactorSelect, null);
                    return;
                }
                // スキル進化によりすでに因子情報があるなら通信後の処理へ
                var skillUpgradeFactorSelect = WorkDataManager.Instance.SingleMode.SkillUpgradeFactorSelect;
                if (skillUpgradeFactorSelect != null)
                {
                    callback(skillUpgradeFactorSelect, null);
                    return;
                }

                SingleModeAPI.SendFactorSelect(null, (singleModeFactorSelectCommon, singleModeFactorOrderCommon) =>
                {
                    callback(singleModeFactorSelectCommon, singleModeFactorOrderCommon);
                });
            }
        }

        #endregion


        //終了通信の送信が完了しているかどうか。この通信が正常に返ってきたら育成データがサーバー上から削除される
        private bool _isSendFinishAPI = false;
    
        private SingleModeResultCharaViewer _charaViewer;
        private SingleModeResultSequence _sequence = new SingleModeResultSequence();

#if CYG_DEBUG

        public SingleModeResultSequence Sequence => _sequence;
        public override IEnumerator PreRegisterDownload()
        {
            //テストで遷移したときにデータが作られてないのでテスト用に作成する
            if (!SingleModeResultDirectSceneController.ExecuteDirectPlayResult &&  WorkDataManager.Instance.SingleMode.Character == null)
            {
                yield return Cute.Core.UpdateDispatcher.StartCoroutine(CreateTestData());
            }
        }
#endif

        public override void RegisterDownload(DownloadPathRegister register)
        {
#if CYG_DEBUG
            //直接飛んだ時にはダウンロードしない
            if (SingleModeResultDirectSceneController.ExecuteDirectPlayResult)
            {
                return;
            }
#endif

            var singleCharacter = WorkDataManager.Instance.SingleMode.Character;
            // キャラ・競馬場
            int dressId = singleCharacter.GetRaceDressId(true);
            var racePersonalityType = ModelLoader.GetRaceOverRunMotionPersonality(singleCharacter.CharaId);
            var buildInfo = new CharacterBuildInfo(singleCharacter.CharaId, dressId, ModelLoader.ControllerType.Race);
            SingleModeResultCharaViewer.RegisterDownload(buildInfo, racePersonalityType, register);

            //各コンテンツごとにダウンロード登録
            PartsSingleModeResultRankScore.RegisterDownload(register);
            PartsSingleModeResultRaceHistory.RegisterDownload(register);
            PartsSingleModeResultFactor.RegisterDownload(register);
            PartsSingleModeResultFactorLottery.RegisterDownload(register);
            PartsSingleModeResultFactorSelect.RegisterDownload(register);
            PartsSingleModeResultTrainerStatus.RegisterDownload(register);
            PartsSingleModeResultSupportCardExpAndItem.RegisterDownload(register);
            DialogSingleModeResultUpdateRankScoreRanking.RegisterDownload(register);
            DialogSingleModeResultUpdateScenarioRecord.RegisterDownload(register);
            if (!TutorialManager.IsTutorialExecuting())
            {
                DialogStoryReleaseNotice.RegisterDownload(register);
            }

            // Effect
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_RESULT_EFFECT_PATH);
            // Flash
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_LOVE_RANKUP_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TRAINER_LEVEL_UP_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_RESULT_RANK_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_RESULT_TITLE_FLASH_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_RESULT_HONOR_FLASH_PATH);

            // 最終育成ランク画像
            for(var rank = GameDefine.FinalTrainingRank.Min ; rank <= GameDefine.FinalTrainingRank.Max ; ++rank)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeFinalTrainingRankIconPath(rank));
            }

            //アイテムアイコン関連
            ItemIcon.RegisterPath(register);

            // ボイス
            var charaIdList = new List<int>() { singleCharacter.CharaId };
            AudioManager.Instance.RegisterDownloadByCharaIds(register, charaIdList, CharacterSystemTextGroupExtension.Scene.SingleMode);
            // 親愛度ランクアップボイス
            AudioManager.Instance.RegisterDownloadByCharaIds(register, charaIdList, CharacterSystemTextGroupExtension.Scene.Other);

            // 限定ショップオープン
            DialogItemExchangeLimitedSalesOpen.RegisterDownload(register);
            RegisterSe(register);

            // ハードモード 報酬上限達成
            DialogDifficultyCampaignRewardComplete.RegisterDownload(register, singleCharacter);

            // フリー編
            RegisterDownloadScenarioFree(register);
        }

        public static void RegisterSe(DownloadPathRegister register)
        {
            AudioManager.Instance.RegisterDownloadByAudioIds(register, new[]
            {
                AudioId.SFX_TRAINING_RESULT_1,
                AudioId.SFX_TRAINING_RESULT_2,
                AudioId.SFX_TRAINING_RESULT_3,
                AudioId.SFX_TRAINING_RESULT_4,
                AudioId.SFX_TRAINING_RESULT_5,
                AudioId.SFX_TRAINING_RESULT_6,
                AudioId.SFX_TRAINING_RESULT_7,
                AudioId.SFX_TRAINING_RESULT_RANK_APPEAR_01,
                AudioId.SFX_TRAINING_RESULT_RANK_APPEAR_02,
                AudioId.SFX_TRAINING_RESULT_RANK_MOVE,                      //育成ランクシンボルが真ん中から右上へ移動
                AudioId.SFX_TRAINING_RESULT_TRANSISION,                     //因子獲得・獲得報酬へ遷移
                AudioId.SFX_TRAINING_RESULT_EXPERIENCE,                     //主な勝鞍
                AudioId.SFX_TRAINING_RESULT_FACTOR,                         //獲得因子表示
                AudioId.SFX_TRAINING_RESULT_COUNT,                          //カウント(評価点、ファン数、サポートポイント)
                AudioId.SFX_TRAINING_RESULT_EXCHANGE_SUPO,                  //サポートカードの経験値をサポートポイントに変換
                AudioId.SFX_UI_SHINRAIUP,
                AudioId.SFX_UI_COUNT,
                AudioId.SFX_UI_ITEM_GET,
                AudioId.SFX_UI_RANK,
                AudioId.SFX_TRAINING_DONE
            });
        }

        private void RegisterDownloadScenarioFree(DownloadPathRegister register)
        {
            if (WorkDataManager.Instance.SingleMode.GetScenarioId() != SingleModeDefine.ScenarioId.Free)
            {
                // フリー編ではないので終了
                return;
            }

            PartsSingleModeScenarioFreeWinPointGauge.RegisterDownload(register);
            DialogSingleModeRouteTscContinue.RegisterDownload(register);
        }

        public override AudioManager.CueSheetCueNameInfo GetDynamicBgmCueInfo()
        {
            //一応初期値は旧データのシナリオIDの1にしておく
            int scenarioId = SingleModeDefine.OLD_SCENARIO_ID;
#if CYG_DEBUG
            if (SingleModeResultDirectSceneController.ExecuteDirectPlayResult)
            {
                //ダイレクトで来た場合に仮の値を入れておく
                scenarioId = SingleModeDefine.OLD_SCENARIO_ID;
            }
            else
#endif
            {
                scenarioId = WorkDataManager.Instance.SingleMode.Character.ScenarioId;
            }

            // 育成ハードモードBGM
            var difficultyCampaignData = SingleModeUtils.GetActiveDifficultyCampaignDataByScenarioId(scenarioId);
            if (difficultyCampaignData != null)
            {
                var masterSingleModeDifficultyMode = MasterDataManager.Instance.masterSingleModeDifficultyMode.Get(WorkDataManager.Instance.SingleMode.Character.DifficultyId);
                if (masterSingleModeDifficultyMode != null &&
                    masterSingleModeDifficultyMode.DifficultyId == difficultyCampaignData.EffectValue1 &&
                    string.IsNullOrEmpty(masterSingleModeDifficultyMode.BgmCuesheetName) == false &&
                    string.IsNullOrEmpty(masterSingleModeDifficultyMode.BgmCueName) == false)
                {
                    return new AudioManager.CueSheetCueNameInfo() { CueSheet = masterSingleModeDifficultyMode.BgmCuesheetName, CueName = masterSingleModeDifficultyMode.BgmCueName };
                }
            }


            // 通常BGM
            var audioData = AudioDefine.GetAudioIdData(AudioId.BGM_SINGLE_MODE_RESULT);
            return new AudioManager.CueSheetCueNameInfo() {CueSheet = audioData._cueSheet, CueName = audioData._cueName};
        }


        /// <summary>
        /// 初期化
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeView()
        {
            InitializeCharaViewer();

            AutoPlayProxy = new SingleModeResultViewControllerProxy(this);
            
            yield return SendSingleModeResultAPI();
        }


        private void InitializeCharaViewer()
        {
            TryGetCharaDressId(out var charaId, out var dressId);

            //独自カメラで表示するので無効にする
            var sceneController = GetSceneController<SingleModeSceneController>();
            if (sceneController != null)
            {
                sceneController.SetCameraEnable(false);
            }

            //キャラビュワー作成
            var charaViewerObj = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_RESULT_CHARA_VIEWER_PATH);
            var charaViewerInstance = Object.Instantiate(charaViewerObj);
            _charaViewer = charaViewerInstance.GetComponent<SingleModeResultCharaViewer>(); //シーン直下に生成する
            _charaViewer.CreateCourseAndCharacter(charaId, dressId);
            _charaViewer.SetCharacterVisible(false);
            _charaViewer.Update();  //初期位置で更新
            _charaViewer.EnableCameraUpdate = false;// キャラINまでは更新OFF
        }

        private void TryGetCharaDressId(out int charaId, out int dressId)
        {
            charaId = 0;
            dressId = 0;

#if CYG_DEBUG
            if (SingleModeResultDirectSceneController.ExecuteDirectPlayResult)
            {
                charaId = SingleModeResultDirectSceneController.CharaId;
                dressId = SingleModeResultDirectSceneController.DressId;
            }
            else
#endif
            {
                //キャラ取得
                var workCharaData = WorkDataManager.Instance.SingleMode.Character;
                if (workCharaData == null)
                {
                    Debug.LogError("シングルモードのキャラデータが存在しない");
                    return;
                }

                //モデル、Infoの生成
                var cardId = workCharaData.CardId;
                var cardData = MasterDataManager.Instance.masterCardData.Get(cardId);
                if (cardData == null)
                {
                    Debug.LogError("存在しないカードID cardId = " + cardId);
                    return;
                }

                charaId = cardData.CharaId;
                dressId = workCharaData.GetRaceDressId(true);
            }
        }


        private IEnumerator SendSingleModeResultAPI()
        {
#if CYG_DEBUG
            if (SingleModeResultDirectSceneController.ExecuteDirectPlayResult || 
                DebugPageSingleModeResult.IsExecuteForcePlayResult())
            {
                SendSingleModeResultAPI_ExecuteDirectPlayResult();
                yield break;
            }
#endif

            var requestComplete = false;

            if (TutorialSingleMode.IsTutorial)
            {
                SendSingleModeFinish(() =>
                {
                    _isSendFinishAPI = true;
                    requestComplete = true;
                });
                yield return new WaitUntil(() => requestComplete);

                yield break;
            }

            SendFactorSelectRequest(() =>
            {
                requestComplete = true;
            });
            yield return new WaitUntil(() => requestComplete);
        }


#if CYG_DEBUG
        [System.Diagnostics.Conditional("CYG_DEBUG")]
        private void SendSingleModeResultAPI_ExecuteDirectPlayResult()
        {
            SendSingleModeFinish(() => { });
        }
#endif


        private void SendFactorSelectRequest(System.Action onComplete)
        {
            SingleModeResultAPI.SendFactorSelectRequest((singleModeFactorSelectCommon, singleModeFactorOrderCommon) => CallbackSendFactorSelectRequest(singleModeFactorSelectCommon, singleModeFactorOrderCommon, onComplete));
        }

        private void CallbackSendFactorSelectRequest(SingleModeFactorSelectCommon singleModeFactorSelectCommon, SingleModeFactorOrderCommon singleModeFactorOrderCommon, System.Action onComplete)
        {
            _sequence.Setup(_charaViewer, _view._sequenceParts, _view.CommonUIParts, (GameDefine.FinalTrainingRank)singleModeFactorSelectCommon.rank, singleModeFactorSelectCommon.rank_score, 
                (factorLotteryId, callback) =>
                {
                    SingleModeResultAPI.SendSingleModeFinishRequest(factorLotteryId,
                        singleModeResultDataContainer =>
                        {
                            _isSendFinishAPI = true;
                            callback.Invoke(singleModeResultDataContainer);
                        });
                });

            // ランクスコア表示設定
            var characterId = WorkDataManager.Instance.SingleMode.Character.CharaId;
            _sequence.SetupRankScore(characterId, singleModeFactorSelectCommon.rank, singleModeFactorSelectCommon.rank_score);

            // 獲得因子表示設定
            _sequence.SetupFactor(
                singleModeFactorSelectCommon.factor_select_info_array,
                singleModeFactorSelectCommon.next_lottery_info,
                singleModeFactorSelectCommon.factor_order_info);

            // 指定因子機能を使っていたならそのレスポンスをワークデータに反映
            if (singleModeFactorOrderCommon != null)
            {
                WorkDataManager.Instance.UserData.UpdateCoin(singleModeFactorOrderCommon.coin_info); // ジュエル消費
                WorkDataManager.Instance.ItemData.Update(singleModeFactorOrderCommon.user_item);     // 指定因子チケット消費
            }

            onComplete?.Invoke();
        }


        private void SendSingleModeFinish(System.Action onComplete)
        {
            if (TutorialSingleMode.IsTutorial)
            {
                // チュートリアル専用API
                SingleModeResultAPI.SendSingleModeFinishRequest_Tutorial(singleModeResultDataContainer => SendSingleModeFinish(singleModeResultDataContainer, onComplete));
                return;
            }

            SingleModeResultAPI.SendSingleModeFinishRequest(0, singleModeResultDataContainer => SendSingleModeFinish(singleModeResultDataContainer, onComplete));
        }

        private void SendSingleModeFinish(SingleModeResultDataContainer singleModeResultDataContainer, System.Action onComplete)
        {
            _sequence.Setup(_charaViewer, _view._sequenceParts, _view.CommonUIParts, singleModeResultDataContainer);

            var characterId = GameDefine.INVALID_CHARA_ID;
#if CYG_DEBUG
            if (SingleModeResultDirectSceneController.ExecuteDirectPlayResult)
            {
                //ダイレクトから起動の場合はダイレクトシーンの設定されたキャラIDを使用する
                characterId = SingleModeResultDirectSceneController.CharaId;
            }
            else
#endif
            {
                characterId = WorkDataManager.Instance.SingleMode.Character.CharaId;
            }

            // ランクスコア表示設定
            _sequence.SetupRankScore(characterId, (int)singleModeResultDataContainer.TrainingRank, singleModeResultDataContainer.RankScore);


            // 獲得因子表示設定
            _sequence.SetupFactor(singleModeResultDataContainer);

            onComplete?.Invoke();
        }



        public override void BeginView()
        {
            _sequence.StartStep();
        }


        public override IEnumerator FinalizeView()
        {
            _sequence.StopSequence();

            if (_charaViewer != null)
            {
                Object.Destroy(_charaViewer.gameObject);
                _charaViewer = null;
            }

            // DirectionalLightをリセットする
            var lightManager = DirectionalLightManager.Instance;
            lightManager.Reset();

#if CYG_DEBUG
            //連続再生ならワークはそのままにしておく
            if (DebugPageSingleModeResult.ContinueResult)
            {
                yield return base.FinalizeView();
                yield break;
            }
#endif

            //サーバーへの終了通信が終わっていたらフラグを落とす。そうでない場合（メンテなどでホームに戻る時）はデータは残したままにしないといけない
            if (_isSendFinishAPI)
            {
                // フラグ解除
                WorkDataManager.Instance.SingleMode.IsPlaying = false;

                //戦績をクリア
                WorkDataManager.Instance.SingleMode.ClearAllPlayingData();

                //TempDataも消す
                TempData.Instance.SingleModeData.Clear();
            }

            yield return base.FinalizeView();
        }

        public override void OnClickOsBackKey()
        {
            _sequence.OnClickOsBackKey();
        }

#if CYG_DEBUG
        /// <summary>
        /// 表示テスト用コード
        /// </summary>
        private static IEnumerator CreateTestData()
        {
            //Index通信
            SingleModeLoadResponse response = null;
            yield return SingleModeSceneController.SendAsync<SingleModeLoadRequest, SingleModeLoadResponse>(
                new SingleModeLoadRequest(),
                res =>
                {
                    response = res;
                });

            if (response == null)
            {
                yield break;
            }

            WorkDataManager.Instance.SingleMode.Apply(response.data);
        }
#endif
    }
}
