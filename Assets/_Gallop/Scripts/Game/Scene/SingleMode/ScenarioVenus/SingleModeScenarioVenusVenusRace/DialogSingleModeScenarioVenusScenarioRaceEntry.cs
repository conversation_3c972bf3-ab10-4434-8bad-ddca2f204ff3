using UnityEngine;
namespace Gallop
{
    using static  Gallop.DialogSingleModeRaceAnalyzeResult; 
    /// <summary>
    /// Venus編:シナリオレース出走確認ダイアログ
    /// </summary>
    public sealed class DialogSingleModeScenarioVenusScenarioRaceEntry : DialogInnerBase
    {
        /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] 
        private RawImageCommon _courseImage = null;
        [SerializeField]
        private RawImageCommon _raceLogoImage = null;
        [SerializeField]
        private TextCommon _courseText = null;
        [SerializeField]
        private TextCommon _entryNumText = null;
        [SerializeField]
        private TextCommon _openDateText = null;
        [SerializeField]
        private PartsEnvironmentConditions _conditions = null;
        [SerializeField]
        private ImageCommon _gradeIcon = null;


        [Header("レース分析")]
        [SerializeField]
        private RawImageCommon _cardIcon = null;
        [SerializeField]
        private TextCommon _charaName = null;

        [SerializeField]
        private ImageCommon _baseSpeedResultIcon = null;
        [SerializeField]
        private ImageCommon _baseStaminaResultIcon = null;
        [SerializeField]
        private ImageCommon _basePowResultIcon = null;
        [SerializeField]
        private ImageCommon _baseGutsResultIcon = null;
        [SerializeField]
        private ImageCommon _baseWizResultIcon = null;

        [SerializeField]
        private TazunaMessageBalloon _tazunaBalloon = null;

        private MasterSingleModeProgram.SingleModeProgram _program = null;
        private MasterRaceInstance.RaceInstance _raceInstance = null;

        public static void PushDialog()
        {
            var workVenus = WorkDataManager.Instance.SingleMode.Character.ScenarioVenus;
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_VENUS_SCENARIO_RACE_ENTRY);

            var dialogContentObj = Instantiate(prefab);
            var dialogContent = dialogContentObj.GetComponent<DialogSingleModeScenarioVenusScenarioRaceEntry>();
            var dialogData = dialogContent.CreateDialogData();
            
            DialogManager.PushDialog(dialogData);
            dialogContent.Setup();
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Outgame0069.Text();
            data.RightButtonCallBack = OnClickRightButton;
            data.LeftButtonCallBack = (dialog) => dialog.Close();

            data.RightButtonText = TextId.Race0148.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            return data;
        }

        public static void RegisterDownload(DownloadPathRegister register)
        {
            var programId = WorkDataManager.Instance.SingleMode.Character.ScenarioVenus.VenusRaceCondition.program_id;
            var program = MasterDataManager.Instance.masterSingleModeProgram.Get(programId);
            var raceInstance = program?.GetMasterRaceInstance();
            if(raceInstance != null)
            {
                var courseSet = raceInstance.GetRaceCourseSetMaster();
                register.RegisterPath(ResourcePath.GetCourseImagePath(courseSet.RaceTrackId));
            }
            
            register.RegisterPath(ResourcePath.GetVenusScenarioRaceLogoPath(SingleModeScenarioVenusUtils.GetVenusScenarioRaceLogoCourseTypeId()));
        }

        private void OnClickRightButton(DialogCommon dialog)
        {
            SingleModeVenusAPI.SendVenusRaceEntry(() => 
            {
                SingleModeChangeViewManager.Instance.ChangeViewVenusScenarioRacePaddock();
            });
        }

        public void Setup()
        {
            var programId = WorkDataManager.Instance.SingleMode.Character.ScenarioVenus.VenusRaceCondition.program_id;
            _program = MasterDataManager.Instance.masterSingleModeProgram.Get(programId);
            _raceInstance = _program?.GetMasterRaceInstance();
            if (_program == null || _raceInstance == null) return;

            SetupLogo();
            SetupCourseInfo();
            SetupAnalyze();
        }

        /// <summary>
        /// ダイアログ上部のロゴ画像周りを設定
        /// </summary>
        /// <param name="dialogHash"></param>
        /// <param name="raceTrackId"></param>
        private void SetupLogo()
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var courseSet = _raceInstance.GetRaceCourseSetMaster();
            //コース画像
            _courseImage.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.GetCourseImagePath(courseSet.RaceTrackId), base.DialogHash);
            //レースロゴ
            var logoPath = ResourcePath.GetVenusScenarioRaceLogoPath(SingleModeScenarioVenusUtils.GetVenusScenarioRaceLogoCourseTypeId());
            _raceLogoImage.texture = ResourceManager.LoadOnHash<Texture>(logoPath, base.DialogHash);
            
            var isGUR = workSingle.GetDegreeType() == SingleModeDefine.DegreeType.Junior;
            _gradeIcon.SetActiveWithCheck(!isGUR);
        }

        /// <summary>
        /// コース情報を設定
        /// </summary>
        private void SetupCourseInfo()
        {
            var condition = WorkDataManager.Instance.SingleMode.Character.ScenarioVenus.VenusRaceCondition;
            var workSingle = WorkDataManager.Instance.SingleMode;
            _courseText.text = RaceUtil.GetCourseInfoTextLong(_raceInstance);
            _entryNumText.text = TextId.Common0270.Format(_raceInstance.GetRaceMaster().EntryNum);
            _conditions.Setup((RaceDefine.Weather)condition.weather, workSingle.GetSeason() , (RaceDefine.GroundCondition)condition.ground_condition,(RaceDefine.Time)_raceInstance.Time);

            if (SingleModeScenarioLegendUtils.IsEnable21300UpdateWithResource() == false)
            {
                // アプデ前の旧実装. 現在ターンを表示
                var degree = SingleModeUtils.GetDegreeText(workSingle.GetDegreeType());
                var month = SingleModeUtils.GetMonthTextByTurn(workSingle.GetCurrentTurn());
                _openDateText.text = TextUtil.Format(TextId.Common0252.Text(),degree,month);
                return;
            }
            _openDateText.text = SingleModeUtils.GetOpenTermText(_program);
        }

        /// <summary>
        /// レース分析周りを設定
        /// </summary>
        private void SetupAnalyze()
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var raceInfo = workSingle.Character.ScenarioVenus.RaceStartInfo;
            var year = SingleModeUtils.GetYear(workSingle.GetTurnSetId(), workSingle.GetCurrentTurn());

            if(raceInfo.race_horse_data.IsNullOrEmpty()) return;
            var setupDesc = RaceUtil.Analyze(raceInfo.race_horse_data,_raceInstance.Id,year);
            SetUpCharaInfo();
            SetUpBaseStatusResult(setupDesc);
            SetUpTazuna(setupDesc);
        }

        /// <summary>
        /// キャラ情報
        /// </summary>
        private void SetUpCharaInfo()
        {
            var singleModeChara = WorkDataManager.Instance.SingleMode.Character;
            var masterChara = MasterDataManager.Instance.masterCharaData.Get(singleModeChara.CharaId);
            if(masterChara == null)
            {
                return;
            }

            _cardIcon.texture = ResourceManager.LoadOnHash<Texture>(ResourcePath.GetCardThumbnailIconPath(singleModeChara.CardRarityData, singleModeChara.TalentLevel), DialogHash);
            _charaName.text = masterChara.Name;
        }

        /// <summary>
        /// ステータス分析表示
        /// </summary>
        private void SetUpBaseStatusResult(SetUpDesc desc)
        {
            _baseSpeedResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseSpeedResult);
            _baseStaminaResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseStaminaResult);
            _basePowResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BasePowResult);
            _baseGutsResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseGutsResult);
            _baseWizResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseWizResult);
        }

        /// <summary>
        /// たづなさん表示
        /// </summary>
        private void SetUpTazuna(SetUpDesc desc)
        {
            var msg = GetTazunaMessage(
                desc.Popularity, 
                desc.Year, 
                desc.RaceInstanceId,
                desc.ProperDistance,
                desc.ProperGround);
            if(msg != null)
            {
                var msgStr = TextUtil.GetMasterText(MasterString.Category.MasterSingleModeAnalyzeMessage, msg.Id);
                _tazunaBalloon.PlayMessage(
                    TazunaMessageBalloon.BalloonType.WhiteTop, 
                    1f,
                    msgStr);
                _tazunaBalloon.gameObject.SetActive(true);
            }
            else
            {
                _tazunaBalloon.gameObject.SetActive(false);
            }
        }
    }
}