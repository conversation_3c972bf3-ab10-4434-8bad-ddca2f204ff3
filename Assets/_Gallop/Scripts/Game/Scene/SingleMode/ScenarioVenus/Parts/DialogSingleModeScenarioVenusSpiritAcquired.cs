using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 女神の叡智習得可能ダイアログ
    /// </summary>
    public class DialogSingleModeScenarioVenusSpiritAcquired : DialogInnerBase
    {

        #region SerializeField

        [SerializeField]
        private RawImageCommon _titleImage;

        [SerializeField]
        private RawImageCommon _image;

        [SerializeField]
        private PartsSingleModeScenarioVenusIconVenusSpirit _iconVenusSpirit;

        #endregion


        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion


        private System.Action _onClickDecide = null;


        #region Method




        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.IMG_VENUSSPIRIT_GET_00);
            register.RegisterPathWithoutInfo(ResourcePath.IMG_VENUSSPIRIT_GET_01);
            register.RegisterPathWithoutInfo(ResourcePath.IMG_VENUSSPIRIT_GET_02);
            register.RegisterPathWithoutInfo(ResourcePath.UTX_TXT_VENUSSPIRIT_GET_00);
            register.RegisterPathWithoutInfo(ResourcePath.COMMON_DIALOG_TITLE_GITTER_01_PATH);
            PartsSingleModeScenarioVenusIconVenusSpirit.RegisterDownload(register);
            AudioManager.Instance.RegisterDownloadByAudioId(register,AudioId.SFX_ADDON05_WISDOM_GET);
        }

        /// <summary>
        /// 開く
        /// </summary>
        public static DialogSingleModeScenarioVenusSpiritAcquired Open(Action onClickDecide)
        {
            var content = LoadAndInstantiatePrefab<DialogSingleModeScenarioVenusSpiritAcquired>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_VENUS_SPIRIT_ACQUIRED);
            var dialogData = content.CreateDialogData();

            DialogManager.PushDialog(dialogData);
            content.Setup(onClickDecide);

            //専用のSEを鳴らす
            AudioManager.Instance.PlaySe(AudioId.SFX_ADDON05_WISDOM_GET);

            return content;
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();

            dialogData.RightButtonText = TextId.Common0009.Text();
            dialogData.RightButtonColor = DialogCommon.ButtonColor.Green;
            dialogData.RightButtonCallBack = OnRightButtonCallBack;

            dialogData.LeftButtonText = TextId.Common0007.Text();
            dialogData.LeftButtonColor = DialogCommon.ButtonColor.White;

            dialogData.IsFooterNotificationText = true;
            dialogData.FooterText = TextId.SingleModeScenarioVenus419020.Text();

            dialogData.IsPlayOpenSe = false;

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="onClickDecide"></param>
        private void Setup(Action onClickDecide)
        {
            _onClickDecide = onClickDecide;

            SetupImage();

            SetupTitleImage();

            SetupTitleFlash();
        }

        private void SetupImage()
        {
            var infoArray = WorkDataManager.Instance.SingleMode.Character.ScenarioVenus.SpiritInfoArray;
            if(!infoArray.IsNullOrEmpty())
            {
                var venusSpiritIndex = infoArray.FirstOrDefault(info => info.spirit_num == SingleModeScenarioVenusDefine.VENUS_SPIRIT_INDEX);
                if (venusSpiritIndex != null)
                {
                    switch (venusSpiritIndex.spirit_id)
                    {
                        case GameDefine.VENUS_CHARA_ID_0:
                            _image.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.IMG_VENUSSPIRIT_GET_00, DialogHash);
                            break;
                        case GameDefine.VENUS_CHARA_ID_1:
                            _image.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.IMG_VENUSSPIRIT_GET_01, DialogHash);
                            break;
                        case GameDefine.VENUS_CHARA_ID_2:
                            _image.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.IMG_VENUSSPIRIT_GET_02, DialogHash);
                            break;
                    }

                    var dialogCanvas = GetDialog().GetComponent<Canvas>();
                    _iconVenusSpirit.Setup(venusSpiritIndex.spirit_id, false, UIManager.CANVAS_SORTING_LAYER_NO_IMAGE_EFFECT_UI, dialogCanvas.sortingOrder, DialogHash);
                }
            }
        }
        

        private void SetupTitleImage()
        {
            _titleImage.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.UTX_TXT_VENUSSPIRIT_GET_00, DialogHash);
        }

        private void SetupTitleFlash()
        {
            // タイトルの上にのせるエフェクト
            var effectPrefab = ResourceManager.LoadOnHash<GameObject>(ResourcePath.COMMON_DIALOG_TITLE_GITTER_01_PATH, DialogHash);
            var effectObj = Instantiate(effectPrefab, _titleImage.transform);
            effectObj.SetLayerRecursively(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            UIUtil.SetParticleSortOrder(effectObj, UIManager.SYSTEM_UI_SORTING_LAYER_NAME, (int)UIManager.CanvasSoringOrder.DialogOverlay);
        }


        private void OnRightButtonCallBack(DialogCommon dialog)
        {
            _onClickDecide?.Invoke();
        }


        #endregion
    }
}
