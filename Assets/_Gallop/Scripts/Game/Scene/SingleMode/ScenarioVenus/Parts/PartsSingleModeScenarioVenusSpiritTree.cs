using System.Collections.Generic;
using System.Linq;
using System;
using AnimateToUnity;
using DG.Tweening;
using UnityEngine;
namespace Gallop
{

    /// <summary>
    /// ヴィーナス編:血統表ツリー
    /// </summary>
    public sealed class PartsSingleModeScenarioVenusSpiritTree : MonoBehaviour
    {
        [SerializeField]
        private PartsSingleModeScenarioVenusSpiritTreeSlot[] _spiritSlotArray;

        private SingleModeVenusSpiritInfo[] _spiritInfoArray;
        private System.Action<SingleModeVenusSpiritInfo> _onClickSpiritSlot;


        public void Setup(SingleModeVenusSpiritInfo[] spiritInfoArray, System.Action<SingleModeVenusSpiritInfo> onClickSpiritSlot)
        {
            _spiritInfoArray = spiritInfoArray;
            _onClickSpiritSlot = onClickSpiritSlot;

            foreach (var spiritTreeSlot in _spiritSlotArray)
            {
                spiritTreeSlot.Setup(spiritInfoArray.ToList(), OnClickSpiritSlot);
            }
        }

        public void SetupTatSpiritIcon(string sortingLayerName, int sortingOrder, ResourceManager.ResourceHash resourceHash)
        {
            foreach (var spiritTreeSlot in _spiritSlotArray)
            {
                spiritTreeSlot.SetTatSpiritIcon(sortingLayerName, sortingOrder, resourceHash);
            }
        }

        private void OnClickSpiritSlot(SingleModeVenusSpiritInfo spiritInfo)
        {
            _onClickSpiritSlot?.Invoke(spiritInfo);
        }

    }
}