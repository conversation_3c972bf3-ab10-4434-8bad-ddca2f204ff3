using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// Venus編：SingleModeMainCharaController拡張
    /// </summary>
    public class SingleModeMainCharaScenarioVenusController : SingleModeMainCharaController
    {
        /// <summary>
        /// 育成TOPで表示する背景キャラクターのセットアップ
        /// </summary>
        protected override void SetupBGCharaModel()
        {
            //簡易版だった場合は背景キャラを表示しない
            //とりあえずチーム対抗戦のデータが存在していれば、対抗戦のデータを利用
            var gameQuality = WorkDataManager.Instance.SingleMode.TeamRace?.GameQuality ?? SaveDataManager.Instance.SaveLoader.GameQuality;

            if (gameQuality == GraphicSettings.GameQuality.Light)
            {
                // 前回配置したキャラを破棄
                ClearBgModel();
                return;
            }
            
            if (SaveDataManager.Instance.SaveLoader.GameQuality != GraphicSettings.GameQuality.Light)
            {
                SetupVenusGMCharacter(WorkDataManager.Instance.SingleMode.Character.ScenarioVenus.GetHighLevelVenusId());
            }
        }

        /// <summary>
        /// シナリオ別メインキャラ配置調整が必要ならば対応する
        /// </summary>
        public override void UpdateMainCharaPositionByScenario()
        {
            SetupVenusGMMainCharacterPosition();
        }
        
        /// <summary>
        /// Venus編背景キャラクター配置
        /// </summary>
        public void SetupVenusGMCharacter(int rootVenusId)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var charaParam = SingleModeScenarioVenusMainView3DCharaParam.Load();
            var venusIdArray = StaticVariableDefine.SingleMode.SingleModeScenarioVenusDefine.VENUS_GM_ORDER_DICTIONARY[rootVenusId];

            //ファイナルズ期間で目標レースターン(グランドマスターズ開催ターン) でなければなにもしない
            if(!(workSingle.GetDegreeType() == SingleModeDefine.DegreeType.Final && workSingle.GetNextRouteTarget().Turn == workSingle.GetCurrentTurn()))
            {
                return;
            }

            //パラメータがないもしくは配置情報が女神 + 育成キャラの分用意されていない
            if(charaParam == null || charaParam.CharaSettings.Count < SingleModeScenarioVenusDefine.VENUS_CHARA_LENGTH + 1)
            {
                return;
            }


            var modelList = GetBgModelList(BgDisplayType.Top);
            var index = 1; //0は育成キャラで使用する
            foreach(var id in venusIdArray)
            {
                var dress = MasterDataManager.Instance.masterDressData.GetWithCharaIdOrderByIdAsc(id).Id;
                var model = _scene.CreateBgCharaModelByCharaId(id, dress, false);
                if (modelList.Contains(model) == false)
                {
                    modelList.Add(model);
                }
                var modelController = model.Controller;
                modelController.PlayMotion(modelController.IdleMotionSetMaster, startType: SimpleModelController.MotionStartType.Loop, bodyBlendTime: 0, isForce: true);
                modelController.PlayableAnimator.UpdateMotion(0f); //モーションを即時反映させる
                modelController.PlayableAnimator.Evaluate();
                modelController.transform.localPosition = charaParam.CharaSettings[index].Position;
                modelController.transform.localEulerAngles = charaParam.CharaSettings[index].Rotation;
                modelController.transform.localScale = charaParam.CharaSettings[index].Scale;
                modelController.SetShadow(ModelLoader.ShadowType.Normal); //リアルタイムシャドウ

                GallopCharacterImageEffectParameter.ApplayCharacterColor(charaParam.GrobalCharacterColorData,ModelController);
                if(charaParam.IsRescaleModel)
                {
                    modelController.RescaleModel(ref charaParam.CharaMinMaxScale);
                }

                _trainingController.AddModel(modelController);
                index++;
            }

            // イメージエフェクト適用
            _trainingController.UpdateImageEffectCharacterColor();
            
            foreach (var model in modelList)
            {
                Gallop.ModelController.FrameUpdate(model.Controller);
            }

            // 描画順
            UpdateBGCharacterRenderQueue(_scene.FocusCamera.GetCamera());
        }
        
        public void SetupVenusGMMainCharacterPosition()
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var charaParam = SingleModeScenarioVenusMainView3DCharaParam.Load();

            //ファイナルズ期間で目標レースターン(グランドマスターズ開催ターン) でなければなにもしない
            if(!(workSingle.GetDegreeType() == SingleModeDefine.DegreeType.Final && workSingle.GetNextRouteTarget().Turn == workSingle.GetCurrentTurn()))
            {
                return;
            }

            //パラメータがないもしくは配置情報が女神 + 育成キャラの分用意されていない
            if(charaParam == null || charaParam.CharaSettings.Count < SingleModeScenarioVenusDefine.VENUS_CHARA_LENGTH + 1)
            {
                return;
            }

            if(_lightController == null)
            {
                _lightController = new SingleModeScenarioContentsTopLightController();
                _lightController.CreateCharaLight();
                _scene.FocusCamera.GetCamera().cullingMask |= GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3D);
            }

            var camera = _scene.FocusCamera.GetCamera();
            if(_defaultCameraData == null)
            {
                _defaultCameraData = new CameraData()
                {
                    FocusCameraFOV = camera.fieldOfView,
                    FocusCameraPos = camera.transform.localPosition
                };
            }

            camera.fieldOfView = charaParam.Camera.Fov;
            camera.transform.localPosition = charaParam.Camera.Position;


            var mainCharaParam = charaParam.CharaSettings[0];
            ModelController.transform.localPosition = mainCharaParam.Position;
            ModelController.transform.localEulerAngles = mainCharaParam.Rotation;
            ModelController.transform.localScale = mainCharaParam.Scale;
            ModelController.SetShadow(ModelLoader.ShadowType.Normal);
            GallopCharacterImageEffectParameter.ApplayCharacterColor(charaParam.GrobalCharacterColorData,ModelController);
            
            if(charaParam.IsRescaleModel)
            {
                ModelController.RescaleModel(ref charaParam.CharaMinMaxScale);
            }

            _lightController.SetupDirectionalLight(charaParam);
            QualitySettings.shadowDistance = charaParam.ShadowDistance;
        }
    }
}

