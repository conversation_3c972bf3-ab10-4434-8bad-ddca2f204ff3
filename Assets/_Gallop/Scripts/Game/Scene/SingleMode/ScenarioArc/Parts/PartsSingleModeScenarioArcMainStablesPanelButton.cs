using System.Linq;
using UnityEngine;

namespace Gallop
{
    public sealed class PartsSingleModeScenarioArcMainStablesPanelButtonModel : PartsSingleModeMainStablesPanelButtonModel
    {
        public PartsSingleModeScenarioArcMainStablesPanelButtonModel(DefineButtonSizeType buttonSizeType = DefineButtonSizeType.Normal)
            : base(buttonSizeType)
        {}

        public override Sprite ButtonSprite
        {
            get
            {
                if (!IsEnableUniqCommand) return LockButtonSprite;
                switch (ButtonSizeType)
                {
                    case DefineButtonSizeType.Small:
                        return GetSprite(AtlasSpritePath.SingleModeScenarioArc.BTN_POTENTIAL_S_00);
                    case DefineButtonSizeType.Normal:
                    default:
                        return GetSprite(AtlasSpritePath.SingleModeScenarioArc.BTN_POTENTIAL_M_00);
                };
            }
        }

        private Sprite LockButtonSprite => GetSprite(AtlasSpritePath.SingleModeScenarioArc.BTN_LOCK_MAIN_ARC_S);
        public override Sprite TextImageSprite
        {
            get
            {
                switch (ButtonSizeType)
                {
                    case DefineButtonSizeType.Small:
                        return GetSprite(AtlasSpritePath.SingleModeScenarioArc.TXT_POTENTIAL_S_00);
                    case DefineButtonSizeType.Normal:
                    default:
                        return GetSprite(AtlasSpritePath.SingleModeScenarioArc.TXT_POTENTIAL_M_00);
                };
            }
        }

        public override Sprite ButtonIconIdleSprite => GetSprite(AtlasSpritePath.SingleModeScenarioArc.ICO_POTENTIAL_00);
        public override Sprite ButtonIconEnterSprite => GetSprite(AtlasSpritePath.SingleModeScenarioArc.ICO_POTENTIAL_01);
        public bool IsEnableUniqCommand => WorkDataManager.Instance.SingleMode.IsEnableUniqueCommand();
        public bool Interactable => IsEnableUniqCommand;
        public string NotificationMessage => Interactable ? string.Empty : TextId.Common425001.Text();
        private Sprite GetSprite(string spriteName) => AtlasUtil.GetSpriteByName(TargetAtlasType.SingleModeScenarioArc, spriteName);
    }
    
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioArcMainStablesPanelButton : PartsSingleModeMainStablesPanelButton
    {
        [SerializeField]private GameObject _lockIcon;
        [SerializeField]private ImageCommon _lockMarkImage;
        [SerializeField]private TextCommon _globalExp;
        [SerializeField]private GameObject _globalExpRoot;
        [SerializeField]private GameObject _canLeveluppRoot;

        private PartsSingleModeScenarioArcMainStablesPanelButtonModel Model => _model as PartsSingleModeScenarioArcMainStablesPanelButtonModel;

        #region Method

        public static PartsSingleModeScenarioArcMainStablesPanelButton Create(Transform parent)
        {
            var obj = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_ARC_MAIN_STABLE_PANEL_BUTTON), parent);
            var component = obj.GetComponent<PartsSingleModeScenarioArcMainStablesPanelButton>();
            return component;
        }

        public override void Setup(PartsSingleModeMainStablesPanelButtonModel model, System.Action onClick = null)
        {
            _model = model;

            if (onClick != null)
            {
                _onClick = onClick;
            }
            _button.SetOnClick(OnClick);
            gameObject.SetActive(model.IsActive);
            
            SetButtonSprite(_model);

            SetButtonIconSprite(_model);

            SetTextImageSprite(_model);


            SetInteractable(Model.Interactable);
            // ロック表示
            SetPanelObjectActive(Model.IsEnableUniqCommand);
            var isLock = !Model.IsEnableUniqCommand;
            _lockIcon.SetActive(isLock);
            _lockMarkImage.SetActiveWithCheck(isLock);//?マーク

            //ノーティフィケーション
            SetNotificationMessage(Model.NotificationMessage);

            // 適性Pt
            _globalExpRoot.SetActive(!isLock);//ロック中は非表示
            _globalExp.text = WorkDataManager.Instance.SingleMode.ScenarioArc.GlobalExp.ToString(TextUtil.CommaSeparatedFormat);
            
            // レベルアップ可能
            _canLeveluppRoot.SetActiveWithCheck(SingleModeScenarioArcUtils.CanLevelUpPotential());
        }

        #endregion
    }
}