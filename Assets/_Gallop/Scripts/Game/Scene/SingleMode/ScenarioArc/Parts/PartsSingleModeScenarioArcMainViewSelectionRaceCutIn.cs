using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using AnimateToUnity;
using DG.Tweening;
using UnityEngine;
using Gallop.Model.Component;
using Gallop.AnimSequenceHelper;
using Gallop.CutIn.Cutt;

namespace Gallop
{
    /// <summary>
    /// 凱旋門賞編:SSマッチ：スキルカットイン＋リザルトカットイン
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioArcMainViewSelectionRaceCutIn : MonoBehaviour
    {
        [SerializeField] private Transform _bgEffectRoot;
        [SerializeField] private RawImageCommon _image3D;
        [SerializeField] private RectTransform _contentsRoot;
        [SerializeField] private Transform _resultFlashRoot;

        /// <summary> UIをFlashより上に置く用Canvas </summary>
        [SerializeField] private Canvas _flashOverlayCanvas;
        
        [SerializeField] private PartsSingleModeScenarioArcMainViewSelectionRaceGainApprovalPoint _approvalPointParts;
        [SerializeField] private PartsSingleModeScenarioArcMainViewSelectionRaceRivalResult _rivalResultParts;
        [SerializeField] private SingleModeMainViewTrainingCutStatus _cutStatus;

        [SerializeField] private ButtonCommon _tapButton;

        private Sequence _sequence;
        private FlashActionPlayer _cutInResultPlayer;
        private FlashActionPlayer _sssCutInPlayer;
        private AnMotion _sssCutInCharaRootMotion;
        private int _winNum;
        private float _cutLength;
        private bool _canStopResultCutIn;
        private Action _onFinish;
        private Action _onStart;
        private Action _onPlayInUI;
        private SingleModeScenarioArcSelectionRaceTopCharaParam _paramData;

        private AnimationSetupParameter _setupParam;
        
        private const string MOTION_WIN_COUNT_00 = "OBJ_mc_wincount00/MOT_mc_wincount00";
        private const string MOTION_WIN_COUNT_01 = "OBJ_mc_wincount01/MOT_mc_wincount00";
        private const string LABEL_IN_WIN = "in_win";
        private const string LABEL_IN_LOSE = "in_lose";
        private const string LABEL_OUT_WIN = "out_win";
        private const string LABEL_OUT_LOSE = "out_lose";
        private const string WIN_NUM_LABEL_NUM = "w{0}";
        // 結果A2Uの表示順
        private const int RESULT_FLASH_SORT_OFFSET = 10;
        // 結果A2U連動エフェクトの表示順
        private const int RESULT_EFFECT_SORT_OFFSET = 10;
        // 結果A2Uの上に乗せるUIの表示順
        private const int FLASH_OVERLAY_UI_SORT_OFFSET = 100;

        // リザルト表示開始までの遅延
        private const float DELAY_TIME_RESULT_IN = GameDefine.BASE_FPS_TIME;
        
        private string TargetLayerName => UIManager.UI_SORTING_LAYER_NAME;

        /// <summary>
        /// リザルト演出のセットアップパラメタ
        /// </summary>
        public class AnimationSetupParameter
        {
            public float CutLength;

            public int GainPoint;
            public int ApprovalPoint;

            /// <summary> ステータスパネルに加算表示で載せるパラメータ増減 </summary>
            public Dictionary<SingleModeDefine.ParameterType, WorkSingleModeData.ParamsIncDecInfo> BonusParamIncDecInfoDic;
            
            public List<SetupParamRival> RivalCharaList = new List<SetupParamRival>();
            
            public class SetupParamRival
            {
                public TeamStadiumDefine.RoundResultType Result;
                
                public int CharaId;
                public int DressId;
            }
            
            public void AddRivalInfo(TeamStadiumDefine.RoundResultType result, int charaId, int dressId)
            {
                var info = new SetupParamRival
                {
                    Result = result,
                    CharaId = charaId,
                    DressId = dressId,
                };
                RivalCharaList.Add(info);
            }
        }

        /// <summary>
        /// DL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            BitmapTextCommon.RegistDownload(register, TextFormat.BitmapFont.SingleModeArcApprovalPt);
            
            PartsSingleModeScenarioArcMainViewSelectionRaceGainApprovalPoint.RegisterDownload(register);
            PartsSingleModeScenarioArcMainViewSelectionRaceRivalResult.RegisterDownload(register);
            
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_MATCH_RESULT_00_PATH);
            AudioManager.Instance.RegisterDownloadByAudioIds(register, new List<AudioId>()
            {
                AudioId.SFX_RACE_RESULT_02,
                AudioId.SFX_RACE_RESULT_03,
                AudioId.SFX_RACE_RESULT_04,
                AudioId.SFX_RACE_SKIP_WHOOSH,
            });
            RegisterDownloadCutIn(register);
        }
        
        /// <summary>
        /// 生成してカットインを再生する
        /// </summary>
        public static PartsSingleModeScenarioArcMainViewSelectionRaceCutIn CreateAndPlay(Transform parent, AnimationSetupParameter setupParam, Action onStart, Action onFinish, Action onPlayInUI)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_ARC_MAIN_VIEW_SELECTION_RACE_CUTIN);
            var obj = Instantiate(prefab, parent);
            var parts = obj.GetComponent<PartsSingleModeScenarioArcMainViewSelectionRaceCutIn>();
            parts._winNum = setupParam.RivalCharaList.Count(r => r.Result == TeamStadiumDefine.RoundResultType.Win);
            parts._cutLength = setupParam.CutLength;
            parts._onFinish = onFinish;
            parts._onStart = onStart;
            parts._onPlayInUI = onPlayInUI;
            parts._setupParam = setupParam;
            parts.SetupUI(setupParam);
            
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            parts.SetupCutIn(workChara.CardId, workChara.CharaId, workChara.GetRaceDressId(true));

            parts.Play();
            return parts;
        }

        private void SetupUI(AnimationSetupParameter setupParam)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_MATCH_RESULT_00_PATH);
            _cutInResultPlayer = Instantiate(prefab, _resultFlashRoot).GetComponent<FlashActionPlayer>();
            _cutInResultPlayer.LoadFlashPlayer();
            _cutInResultPlayer.SetSortLayer(TargetLayerName);
            _cutInResultPlayer.SetSortOffset(RESULT_FLASH_SORT_OFFSET);

            // パーティクルの描画を前面にもってくる
            _cutInResultPlayer.SetOnInstantiatedParticle(particles =>
            {
                foreach (var particle in particles)
                {
                    var particleSystemRenderer = particle.GetComponent<ParticleSystemRenderer>();
                    particleSystemRenderer.sortingOrder = _cutInResultPlayer.SortOffset + RESULT_EFFECT_SORT_OFFSET;
                }
            });
            _cutInResultPlayer.gameObject.SetActive(false);

            // セーフエリア
            UIManager.Instance.AdjustContentsRootRect(_contentsRoot);

            // 一瞬表示されてしまうので透明にしておく
            _image3D.color = Color.clear;
            
            _tapButton.SetOnClick(OnTap);
            _tapButton.SetActiveWithCheck(false);

            _flashOverlayCanvas.sortingLayerName = TargetLayerName;
            _flashOverlayCanvas.sortingOrder = _cutInResultPlayer.SortOffset + FLASH_OVERLAY_UI_SORT_OFFSET;
            
            // リザルト一覧
            SetupRivalResultParts(setupParam);
            _rivalResultParts.SetActiveWithCheck(false);
            
            // 獲得支持Pt
            SetupApprovalInfoParts(setupParam);
            _approvalPointParts.SetActiveWithCheck(false);

            // ステータスフレーム
            _cutStatus.Setup((ResourceManager.ResourceHash)ResourceManager.GetViewLoadHash(SceneManager.Instance.GetCurrentViewId()), TargetLayerName);
            _cutStatus.ClearBonusValue();
            _cutStatus.SetBonusValue(setupParam.BonusParamIncDecInfoDic);
            _cutStatus.PlayPreIn();
        }

        /// <summary>
        /// 各ライバルとの対戦結果を表示するパーツのセットアップ
        /// </summary>
        private void SetupRivalResultParts(AnimationSetupParameter animSetupParam)
        {
            var param = new PartsSingleModeScenarioArcMainViewSelectionRaceRivalResult.SetupParam();
            
            foreach (var setupParamRival in animSetupParam.RivalCharaList)
            {
                param.AddRivalInfo(setupParamRival.CharaId, setupParamRival.DressId, setupParamRival.Result);
            }
            
            _rivalResultParts.Setup(param);
        }


        /// <summary>
        /// 獲得した支持ptを表示するパーツのセットアップ
        /// </summary>
        private void SetupApprovalInfoParts(AnimationSetupParameter animSetupParam)
        {
            var param = new PartsSingleModeScenarioArcMainViewSelectionRaceGainApprovalPoint.SetupParam();

            param.PlayerChara = new PartsSingleModeScenarioArcMainViewSelectionRaceGainApprovalPoint.SetupParam.SetupParamChara();
            param.PlayerChara.GainPoint = animSetupParam.GainPoint;
            param.PlayerChara.ApprovalPoint = animSetupParam.ApprovalPoint;
            
            _approvalPointParts.Setup(param);
        }

        /// <summary>
        /// 頭から再生
        /// </summary>
        private void Play()
        {
            // カットイン開始を外部に通知する
            _onStart?.Invoke();

            Play3DCutIn();
        }
        
        /// <summary>
        /// UIアニメーションを再生する
        /// </summary>
        private void PlayInAnimation()
        {
            _onPlayInUI?.Invoke();
            float skipTime1 = 0;
            float skipTime2 = 0;
                
            float delay = DELAY_TIME_RESULT_IN;//3D演出の1フレーム後から結果A2U再生
            _sequence = DOTween.Sequence();
            _sequence.InsertCallback(delay, () =>
            {
                _cutInResultPlayer.gameObject.SetActive(true);

                var player = _cutInResultPlayer.FlashPlayer;
                // 1つでもwinがあれば全体としてwin
                var label = _winNum > 0 ? LABEL_IN_WIN : LABEL_IN_LOSE;
                player.Play(label);
                if (_winNum > 0)
                {
                    var winCountLabel = TextUtil.Format(WIN_NUM_LABEL_NUM, _winNum);
                    player.GetMotion(MOTION_WIN_COUNT_00)?.SetMotionPlay(winCountLabel);
                    player.GetMotion(MOTION_WIN_COUNT_01)?.SetMotionPlay(winCountLabel);
                }
                PlayResultSe();

                // タップスキップ可能
                _onTapCutIn = OnTapCutIn;
                void OnTapCutIn()
                {
                    _onTapCutIn = null;
                    _rivalResultParts.SetActiveWithCheck(true);
                    _rivalResultParts.PlayInResultFrame(false);
                    _rivalResultParts.Skip();
                    _sequence.Goto(skipTime1,true);// Win/Lose表示の終わりまでジャンプ
                }
            });
            
            // WinLose一覧
            delay += GameDefine.BASE_FPS_TIME * 9;
            _sequence.InsertCallback(delay, () =>
            {
                _rivalResultParts.SetActiveWithCheck(true);
                _rivalResultParts.PlayInResultFrame();
            });
            delay += GameDefine.BASE_FPS_TIME * 7;
            _sequence.InsertCallback(delay, () => { _rivalResultParts.PlayInResult(); });

            // WinLose一覧ハケ
            var winLoseAnimDuration = GameDefine.BASE_FPS_TIME * 2 * (_setupParam.RivalCharaList.Count - 1);
            skipTime1 = delay + winLoseAnimDuration;// この時間までスキップ
            delay += GameDefine.BASE_FPS_TIME * 14 + winLoseAnimDuration; // 最後のキャラのwinLoseがイリしてから14f後
            _sequence.InsertCallback(delay, () => { _onTapCutIn = null; });//OUTまできたら、それより前のスキップ処理無効
            _sequence.Insert(_rivalResultParts.gameObject, TweenAnimation.PresetType.PartsOutMoveAndFade, delay);

            // 獲得Pt
            delay += GameDefine.BASE_FPS_TIME * 2;
            skipTime2 = delay + PartsSingleModeScenarioArcMainViewSelectionRaceGainApprovalPoint.APPROVAL_POINT_COUNT_UP_DELAY + _approvalPointParts.GetCountUpDuration();// Ptカウントアップ完了時刻
            _sequence.InsertCallback(delay, () =>
            {
                _approvalPointParts.SetActiveWithCheck(true);
                _approvalPointParts.PlayIn();
                // タップスキップ可能
                DOVirtual.DelayedCall(GameDefine.COMMON_TAP_WAIT_200MS, () =>
                {
                    _onTapCutIn = OnTapCutIn;
                    void OnTapCutIn()
                    {
                        _onTapCutIn = null;
                        _sequence.Goto(skipTime2, true);// Ptカウントアップ完了時刻までスキップ
                        _approvalPointParts.SkipApprovalPointCountUp();
                    }
                });
            });

            // 獲得ptハケ
            delay += GameDefine.BASE_FPS_TIME * 36;
            _sequence.InsertCallback(delay, () => { _onTapCutIn = null; });//OUTまできたら、それより前のスキップ処理無効
            _sequence.InsertCallback(delay, () =>
            {
                _approvalPointParts.PlayOut();
                PlayOutResult(); // Win/Loseハケ
            });
            
            // ステータスフレーム
            delay += GameDefine.BASE_FPS_TIME * 3;
            _sequence.InsertCallback(delay, () => { _cutStatus.PlayInAll(_cutLength, CallbackOnFinish); });
            
            var statusAnimTimeScale = SingleModeUtils.GetTrainingCutTimeScale(_cutLength);
            delay += GameDefine.BASE_FPS_TIME * 7 / statusAnimTimeScale;
            _sequence.InsertCallback(delay, () => { AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_PARAM_TEXT_CHANGE_UP); });

            _sequence.InsertCallback(delay, () =>
            {
                // 当たり判定が残っていたため消しておく
                _approvalPointParts.SetActiveWithCheck(false);
                _rivalResultParts.SetActiveWithCheck(false);
            });

            _sequence.Play();
        }

        private void PlayOutResult()
        {
            var label = _winNum > 0 ? LABEL_OUT_WIN : LABEL_OUT_LOSE;
            _cutInResultPlayer.Play(label); //結果ハケ
        }

        #region 3Dカットイン関連

        /// <summary>
        /// カット再生コントローラークラス
        /// 基本は競技場と同じだが、リザルトカット開始時に追加の設定ができるようにする
        /// </summary>
        private class SelectionRaceCutIn3DController : TeamStadium3DController
        {
            public Action OnPlayResultCutIn;
            protected override void OnPlayResultCutInStartAction(CutInTimelineController timelineController, CutInTimelineData.ResultCutSettings settingData)
            {
                base.OnPlayResultCutInStartAction(timelineController, settingData);
                OnPlayResultCutIn?.Invoke();
            }
        }
        private SelectionRaceCutIn3DController _selectionRaceCutIn3DController;
        private int _cardId, _charaId, _dressId, _gateNo;
        private Action _onTapCutIn;    // カットイン中に画面タップされたら呼び出される

        private const float FADE_IN_TIME_CUTIN = 0.1f; 
       
        /// <summary>
        /// カットイン関連のDL登録
        /// </summary>
        private static void RegisterDownloadCutIn(DownloadPathRegister register)
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var charaId = workChara.CharaId;
            var dressId = workChara.GetRaceDressId(true);
            // 影の登録
            ShadowController.RegisterDownload(register);

            // スキルカットインのリソース登録
            register.RegisterPathWithoutInfo(ResourcePath.GetSkillCutInTimelineControlPath(ResourcePath.GetRareSkillCutInName(charaId)));

            // リザルトカットインのリソース登録
            ResourcePath.RegistDownloadResultCutin(register, charaId, dressId, TeamStadiumDefine.RoundResultType.Win, RaceDefine.RaceType.TeamStadium);
            ResourcePath.RegistDownloadResultCutin(register, charaId, dressId, TeamStadiumDefine.RoundResultType.Draw, RaceDefine.RaceType.TeamStadium);
            ResourcePath.RegistDownloadResultCutin(register, charaId, dressId, TeamStadiumDefine.RoundResultType.Lose, RaceDefine.RaceType.TeamStadium);

            // ボイス登録
            AudioManager.Instance.RegisterDownloadByCharaIds(register, new List<int> { charaId }, CharacterSystemTextGroupExtension.Scene.Team);
            
            // 背景エフェクト
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_ARC_SSMATCH_BG_EFFECT_PATH);
        }
        
        private void SetupCutIn(int cardId, int charaId, int dressId)
        {
            _cardId = cardId;
            _charaId = charaId;
            _dressId = dressId;
            _gateNo = 1;//使用しないので固定
            _selectionRaceCutIn3DController = new SelectionRaceCutIn3DController();
            _selectionRaceCutIn3DController.Initialize(null);
            _selectionRaceCutIn3DController.InitializeCutin();

            CreateCutInBgEffect();
            
            // カメラ背景色用にSSマッチ選択画面の設定を使用。
            // ※背景が青基調で、選択画面とカットイン背景が同じであるから流用するが、選択画面とカットインで背景が全然違うとかになると別のパラメータにする必要がでてくる
            _paramData = SingleModeScenarioArcSelectionRaceTopCharaParam.Load();
        }

        /// <summary>
        /// 3Dカットイン背景エフェクト生成
        /// </summary>
        private void CreateCutInBgEffect()
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SCENARIO_ARC_SSMATCH_BG_EFFECT_PATH);
            Instantiate(prefab, _bgEffectRoot);
        }

        /// <summary>
        /// 3Dカットインを再生する (スキルカットイン→リザルトカットイン)
        /// </summary>
        private void Play3DCutIn()
        {
            // カットイン再生（まずスキルカットイン、その後リザルトカットイン）
            UIManager.Instance.StartCoroutine(_selectionRaceCutIn3DController.PlaySkillCutinCoroutine(_cardId,
                _charaId,
                _dressId,
                _gateNo,
                null,
                false,
                null,
                OnStartSkillCutIn,
                OnEndSkillCutIn
            ));

            SetBGCulling();
        }
        
        /// <summary>
        /// 3Dカットに内包された背景は今回使用しないのでカメラに映らないようにする、背景はクリア色にして、専用背景エフェクトが映るようにする
        /// </summary>
        private void SetBGCulling()
        {
            SetCamera(_selectionRaceCutIn3DController.SkillCutInBackgroundCamera, GraphicSettings.LayerIndex.LayerCircleProfile);
            SetCamera(_selectionRaceCutIn3DController.CutInCamera, GraphicSettings.LayerIndex.LayerBG);

            void SetCamera(Camera targetCamera, GraphicSettings.LayerIndex layerIndex)
            {
                if (targetCamera == null) return;
                targetCamera.cullingMask &= ~GraphicSettings.GetCullingLayer(layerIndex);
                targetCamera.backgroundColor = _paramData.GetCameraSolidColorCharaSettingData();
            }
        }

        /// <summary>
        /// スキルカットインの開始
        /// </summary>
        private void OnStartSkillCutIn()
        {
            _image3D.color = GameDefine.COLOR_WHITE;
            _image3D.texture = _selectionRaceCutIn3DController.CutinRenderTexture;

            // カットイン開始のSE再生
            var audioPlayBack = AudioManager.Instance.PlaySe(AudioId.SFX_RACE_SKIP_WHOOSH);

            // タップ受付開始
            _tapButton.SetActiveWithCheck(true);
            
            // タップされたらスキルカットインを停止する
            _onTapCutIn = OnTapCutIn;
            void OnTapCutIn()
            {
                if (_selectionRaceCutIn3DController.CanStopSkillCutIn)
                {
                    // SEも止める
                    AudioManager.Instance.StopSe(audioPlayBack);

                    _selectionRaceCutIn3DController.StopRequestSkillCutIn();
                }
            }
        }
        
        /// <summary>
        /// スキルカットインの終了
        /// </summary>
        private void OnEndSkillCutIn()
        {
            // カットを破棄
            _selectionRaceCutIn3DController.DestroySkillCutin();

            // UIアニメーション再生
            PlayInAnimation();

            // リザルトカットの再生
            var result = _winNum > 0 ? TeamStadiumDefine.RoundResultType.Win : TeamStadiumDefine.RoundResultType.Lose;
            _selectionRaceCutIn3DController.OnPlayResultCutIn = OnPlayResultCutIn;//カット再生開始時のコールバック登録
            UIManager.Instance.StartCoroutine(_selectionRaceCutIn3DController.PlayResultCutinCoroutine(_cardId,
                _charaId,
                _dressId,
                _gateNo,
                null,
                false,
                result,
                null,
                PlayVoice,
                null));
        }

        /// <summary>
        /// リザルトカットイン再生開始時、背景色の設定とフェード開け
        /// </summary>
        private void OnPlayResultCutIn()
        {
            SetBGCulling();

            // フェード開け
            FadeManager.Instance.FadeIn(FADE_IN_TIME_CUTIN, null, false, Ease.InOutCubic, FadeManager.FadeCanvasType.SystemCanvas);
        }
        
        /// <summary>
        /// WIN/DRAW/LOSEの結果別SE再生
        /// </summary>
        private void PlayResultSe()
        {
            var resultSe = _winNum > 0 ? AudioId.SFX_RACE_RESULT_02 : AudioId.SFX_RACE_RESULT_04;
            AudioManager.Instance.PlaySe(resultSe);
        }
        
        /// <summary>
        /// WIN/LOSEの結果別ボイス再生
        /// </summary>
        private void PlayVoice()
        {
            var trigger = _winNum > 0 
                ? CharacterSystemLotteryTrigger.TeamStadiumRaceListRaceWin
                : CharacterSystemLotteryTrigger.TeamStadiumRaceListRaceLose;
            AudioManager.Instance.PlaySystemVoice_TeamStadium(_charaId, 0, 0, trigger);
        }
        
        /// <summary>
        /// リザルトカットインのタップ入力受付開始
        /// </summary>
        private void OnTapResultCutIn()
        {
            // タップされたらカットインを停止する
            _onTapCutIn = OnTapCutIn;
            void OnTapCutIn()
            {
                // UI側で止めていた場合も何もしない
                if (!_canStopResultCutIn) return;
                _canStopResultCutIn = false;
                UIManager.Instance.StartCoroutine(OnEndResultCutIn());
            }
        }
        
        /// <summary>
        /// リザルトカットイン終了時
        /// </summary>
        private IEnumerator OnEndResultCutIn()
        {
            // すでに終わっている可能性あるのでチェック
            if (!enabled) yield break;
            CallbackOnFinish();
        }

        private void CallbackOnFinish()
        {
            // タップ受付終了
            _tapButton.SetActiveWithCheck(false);
            
            _onFinish?.Invoke();
            _onFinish = null;
        }
        
        /// <summary>
        /// 更新
        /// </summary>
        public void UpdateView()
        {
            if (_selectionRaceCutIn3DController == null) return;
            _selectionRaceCutIn3DController?.Update3DController();
        }

        /// <summary>
        /// タップ時
        /// </summary>
        private void OnTap()
        {
            if (_selectionRaceCutIn3DController == null) return;
            _onTapCutIn?.Invoke();
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void LateUpdateView()
        {
            _selectionRaceCutIn3DController?.LateUpdate3DController();
        }
        
        /// <summary>
        /// 破棄
        /// </summary>
        public void DestroyCutIn()
        {
            _selectionRaceCutIn3DController.DestroyResultCutin();
            _selectionRaceCutIn3DController.Destroy();
            _selectionRaceCutIn3DController = null;
        }
        
        #endregion

    }
}