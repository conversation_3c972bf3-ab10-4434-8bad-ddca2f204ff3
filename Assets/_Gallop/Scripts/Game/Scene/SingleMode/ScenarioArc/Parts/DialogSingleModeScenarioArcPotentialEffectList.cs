using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 凱旋門賞編:海外適性効果一覧
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioArcPotentialEffectList : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion DialogInnerBase
        
        #region SerializeField、変数

        [SerializeField] private PartsSingleModeScenarioArcPotentialEffectList _potentialEffectListParts;

        #endregion

        #region 関数

        /// <summary>
        /// ダイアログ開く
        /// </summary>
        public static void PushDialog(
            int charaId,
            List<SingleModeScenarioArcPotentialViewController.PotentialViewData> potentialViewDataList,
            TextId title = TextId.SingleModeScenarioArc0029,
            bool isShowTrainingBuff = true)
        {
            var component = LoadAndInstantiatePrefab<DialogSingleModeScenarioArcPotentialEffectList>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_ARC_POTENTIAL_EFFECT_LIST);
            component.Setup(charaId, potentialViewDataList, isShowTrainingBuff);
            var dialogData = component.CreateDialogData();
            dialogData.Title = title.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            DialogManager.PushDialog(dialogData);
        }
        
        /// <summary>
        /// ダイアログ開く：ライバルキャラの発動中海外適性一覧
        /// </summary>
        public static void PushDialogRivalCharacter(int charaId)
        {
            var workInfo = WorkDataManager.Instance.SingleMode.ScenarioArc.GetApprovalInfo(charaId);
            if (workInfo == null)
            {
                Debug.LogError($"指定のキャラはライバルでないためライバルの海外適性一覧は開けません。charaId = {charaId}");
                return;
            }
            // ライバルにトレーニング効果の表示は不要
            PushDialogPotentialList(charaId, workInfo.PotentialDataList, TextId.SingleModeScenarioArc0075, false);
        }
        
        /// <summary>
        /// ダイアログ開く：海外適性一覧(Work)
        /// </summary>
        private static void PushDialogPotentialList(int charaId, List<WorkSingleModeScenarioArc.IPotentialData> workPotentialDataList, TextId title, bool isShowTrainingBuff)
        {
            var potentialViewDataList = new List<SingleModeScenarioArcPotentialViewController.PotentialViewData>();
            var masterList = MasterDataManager.Instance.masterSingleModeArcPotential.GetListOrderByIdAsc();
            foreach (var masterPotential in masterList)
            {
                var workPotential = workPotentialDataList.Find(a=>a.Id == masterPotential.Id);
                if (workPotential == null || workPotential.Level == 0) continue;//未開放
                potentialViewDataList.Add(new SingleModeScenarioArcPotentialViewController.PotentialViewData(masterPotential, workPotential.Level, 1, workPotential.Level));
            }
            PushDialog(charaId, potentialViewDataList, title, isShowTrainingBuff);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(int charaId, List<SingleModeScenarioArcPotentialViewController.PotentialViewData> potentialViewDataList, bool isShowTrainingBuff)
        {
            _potentialEffectListParts.Setup(charaId, potentialViewDataList, isShowTrainingBuff);
        }

        #endregion
    }
}
