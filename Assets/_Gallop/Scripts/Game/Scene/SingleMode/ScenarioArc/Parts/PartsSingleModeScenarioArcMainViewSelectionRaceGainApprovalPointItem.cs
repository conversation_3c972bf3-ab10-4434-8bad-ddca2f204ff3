using UnityEngine;

namespace  Gallop
{
    /// <summary>
    /// 凱旋門賞編:SSマッチ獲得支持pt ポイント表示部分
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioArcMainViewSelectionRaceGainApprovalPointItem : MonoBehaviour
    {
        [SerializeField] private CountupModifier _pointCountUpModifier;
        [SerializeField] private BitmapTextCommon _pointCountUpModifierText;
        [SerializeField] private BitmapTextCommon _pointSuffix;
        [SerializeField] private TextCommon _gainPointLabel;
        
        [SerializeField] private BitmapTextCommon _nextTargetPoint;
        [SerializeField] private GameObject _nextTargetPointSplit;

        private System.Action _onCompleteCountUp;

        /// <summary>
        /// 表示
        /// </summary>
        public void Setup(int prevApprovalPoint, int currentApprovalPoint)
        {
            // カウントアップ前後で桁数が変わる場合、Play()を実行したタイミングでテキスト幅が変わる
            // このオブジェクトでは中央寄せのレイアウトを入れているため、瞬時に桁の位置がずれたような見え方になり違和感がある
            // これを見えないようにするため、フェードイン前にあらかじめPlay()しておき 実際にカウントアップを始めたいタイミングでTimeScaleを戻す実装にする
            _pointCountUpModifier.SetValue(prevApprovalPoint, true);
            _pointCountUpModifier.SetValue(currentApprovalPoint);
            _pointCountUpModifier.TimeScale = 0f;
            _pointCountUpModifier.Play(_onCompleteCountUp);
            
            _pointCountUpModifierText.LoadFont();
            _pointSuffix.LoadFont();

            var gainPoint = currentApprovalPoint - prevApprovalPoint;
            _gainPointLabel.text = TextId.SingleModeScenarioArc508017.Format(TextUtil.ToStringChange(gainPoint)); // (+XXpt)
            
            SetupRoutePointInfo();
        }

        private void SetupRoutePointInfo()
        {
            // 目標がサポーターPtなら分母を表記
            var workSingle = WorkDataManager.Instance.SingleMode;
            var nextRouteTarget = workSingle.GetNextRouteTarget();
            var isNextRouteTargetApprovalPoint = nextRouteTarget is { ConditionType: (int)SingleModeDefine.ConditionType.ApprovalPoint };
            
            _nextTargetPointSplit.SetActive(isNextRouteTargetApprovalPoint);
            _nextTargetPoint.SetActiveWithCheck(isNextRouteTargetApprovalPoint);
            
            if (isNextRouteTargetApprovalPoint)
            {
                _nextTargetPoint.text = nextRouteTarget.ConditionValue1.ToCommaSeparatedString();// 目標値
            }
        }

        /// <summary>
        /// 指定の値までカウントアップ再生
        /// </summary>
        public void PlayApprovalPointCountUp(int approvalPoint, System.Action onComplete = null)
        {
            _pointCountUpModifier.TimeScale = 1f;
            _onCompleteCountUp = onComplete;
        }
        public void SkipApprovalPointCountUp(int approvalPoint)
        {
            _pointCountUpModifier.SetValue(approvalPoint, true);
            _pointCountUpModifier.TimeScale = 1f;
        }

        public float GetCountUpDuration()
        {
            return _pointCountUpModifier.Duration;
        }
    }
}
