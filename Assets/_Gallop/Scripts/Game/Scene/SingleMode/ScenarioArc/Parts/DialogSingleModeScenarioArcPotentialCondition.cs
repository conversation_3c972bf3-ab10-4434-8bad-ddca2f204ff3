using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 凱旋門賞編:海外適性解放条件
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeScenarioArcPotentialCondition : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion DialogInnerBase

        #region SerializeField、変数

        [SerializeField] private PartsSingleModeScenarioArcPotentialConditionItem _conditionItem; // 進化条件要素

        #endregion

        #region 関数

        /// <summary>
        /// ダイアログ開く
        /// </summary>
        public static void PushDialog(SingleModeScenarioArcPotentialViewController.PotentialViewData potentialViewData)
        {
            var component = LoadAndInstantiatePrefab<DialogSingleModeScenarioArcPotentialCondition>(ResourcePath.DIALOG_SINGLE_MODE_SCENARIO_ARC_POTENTIAL_CONDITION);
            component.Setup(potentialViewData);
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.SingleModeScenarioArc0027.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(SingleModeScenarioArcPotentialViewController.PotentialViewData potentialViewData)
        {
            UIUtil.CreateScrollItem(_conditionItem, null, potentialViewData.PotentialConditionList, (item, data) =>
            {
                item.Setup(data);
            });
        }

        #endregion
    }
}
