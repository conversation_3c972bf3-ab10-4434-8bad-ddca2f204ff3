namespace Gallop
{
    /// <summary>
    /// 育成TOP：レースコマンドボタン：凱旋門賞編拡張
    /// </summary>
    public partial class PartsSingleModeRaceStablesPanelButton
    {
        // ライバル VSアイコン
        private SingleModeRaceStablePanelOptionScenarioArcRivalIcon _arcRivalBadge;

        private void SetButtonEffectCoreScenarioArc()
        {
            var workArc = WorkDataManager.Instance.SingleMode.ScenarioArc;
            if (workArc.RivalRaceInfoArray == null) return;

            var isEnable = workArc.RivalRaceInfoArray.Length > 0;
            if (_arcRivalBadge == null)
            {
                _arcRivalBadge = _options.GetOptionComponent<SingleModeRaceStablePanelOptionScenarioArcRivalIcon>();
            }
            _arcRivalBadge.Setup(isEnable);
            _arcRivalBadge.ApplyPosition(Model.IsReservedRaceOpen, _model.ButtonSizeType == PartsSingleModeMainStablesPanelButtonModel.DefineButtonSizeType.Small);
        }
    }
}