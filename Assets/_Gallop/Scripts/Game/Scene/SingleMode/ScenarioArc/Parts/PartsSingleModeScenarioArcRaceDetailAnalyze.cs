using UnityEngine;
using static  Gallop.DialogSingleModeRaceAnalyzeResult; 

namespace Gallop
{
    /// <summary>
    /// 凱旋門賞編:レース詳細ダイアログ：予想結果
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioArcRaceDetailAnalyze : MonoBehaviour
    {
        [SerializeField] private ImageCommon _baseSpeedResultIcon;
        [SerializeField] private ImageCommon _baseStaminaResultIcon;
        [SerializeField] private ImageCommon _basePowResultIcon;
        [SerializeField] private ImageCommon _baseGutsResultIcon;
        [SerializeField] private ImageCommon _baseWizResultIcon;
        [SerializeField] private TazunaMessageBalloon _tazunaBalloon;
 
        
        public void Setup(int raceInstanceId, RaceHorseData[] raceHorseDataArray)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var year = SingleModeUtils.GetYear(workSingle.GetTurnSetId(), workSingle.GetCurrentTurn());

            if(raceHorseDataArray.IsNullOrEmpty()) return;
            var setupDesc = RaceUtil.Analyze(raceHorseDataArray, raceInstanceId, year);
            SetupBaseStatusResult(setupDesc);
            SetupTazuna(setupDesc);
        }
        
        /// <summary>
        /// 予想マーク
        /// </summary>
        private void SetupBaseStatusResult(SetUpDesc desc)
        {
            _baseSpeedResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseSpeedResult);
            _baseStaminaResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseStaminaResult);
            _basePowResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BasePowResult);
            _baseGutsResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseGutsResult);
            _baseWizResultIcon.sprite = AtlasSpritePath.SingleMode.GetAnalyzeResultSprite(desc.BaseWizResult);
        }
        
        /// <summary>
        /// たづなさんメッセージ
        /// </summary>
        private void SetupTazuna(SetUpDesc desc)
        {
            var msg = GetTazunaMessage(desc.Popularity, desc.Year, desc.RaceInstanceId, desc.ProperDistance, desc.ProperGround);
            if(msg != null)
            {
                var msgStr = TextUtil.GetMasterText(MasterString.Category.MasterSingleModeAnalyzeMessage, msg.Id);
                _tazunaBalloon.PlayMessage(TazunaMessageBalloon.BalloonType.WhiteTop, SingleModeDefine.TAZUNA_SCALE, msgStr);
                _tazunaBalloon.gameObject.SetActive(true);
            }
            else
            {
                _tazunaBalloon.gameObject.SetActive(false);
            }
        }
   }
}