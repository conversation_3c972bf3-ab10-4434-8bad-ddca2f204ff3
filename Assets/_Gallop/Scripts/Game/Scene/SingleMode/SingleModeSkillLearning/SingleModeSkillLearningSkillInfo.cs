using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成スキル獲得：スキル情報クラス
    /// </summary>
    public class SingleModeSkillLearningSkillInfo
    {
        public List<PartsSingleModeSkillLearningListItem.Info> SkillList { get; } =
            new List<PartsSingleModeSkillLearningListItem.Info>();

        /// <summary>
        /// データ追加関数（再帰）
        /// </summary>
        /// <param name="skillId"></param>
        /// <param name="recursive"> 再帰チェックするか </param>
        public void AddInfo(int skillId, bool recursive = true)
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var mdm = MasterDataManager.Instance;
            var masterSkillData = mdm.masterSkillData;
            var masterSingleModeSkillNeedPoint = mdm.masterSingleModeSkillNeedPoint;

            var skillData = masterSkillData.Get(skillId);
            if (skillData == null)
            {
                Debug.LogError("Not Found Skill! SkillId:" + skillId);
                return;
            }

            var masterNeedPoint = masterSingleModeSkillNeedPoint.Get(skillId);

            // 習得状況
            var isGet = workChara.AcquiredSkillList.Exists(a =>
            {
                if (a.MasterId == skillId) return true; //習得済み
                if (skillData.GroupId == a.MasterData.GroupId)
                {
                    if (skillData.GroupRate < a.MasterData.GroupRate) return true; // 上位スキル習得済みなら習得済み
                }

                return false;
            });
            // Badスキルを所持している場合は打ち消しを行えるようにする
            if (skillData.GroupRate < 0)
            {
                isGet = false;
            }

            var skillInfo = new PartsSingleModeSkillLearningListItem.Info(skillData, masterNeedPoint, isGet, workChara);

            SkillList.Add(skillInfo);

            // 再帰確認しない場合はここで終了
            if (recursive == false) return;

            // ノーマルスキル
            if (PartsSingleModeSkillLearningListItem.IsRareSkill(skillData) == false)
            {
                // グループの上位互換がノーマルなら派生習得可能
                var skillGroupList = mdm.masterSkillData.GetListWithGroupIdOrderByIdAsc(skillData.GroupId);
                skillGroupList.Sort((a, b) => a.GradeValue - b.GradeValue);
                foreach (var groupSkill in skillGroupList)
                {
                    if (skillData.GroupRate >= groupSkill.GroupRate) continue; // 下位互換は除外
                    if (PartsSingleModeSkillLearningListItem.IsRareSkill(groupSkill)) continue; // レアは除外
                    AddInfo(groupSkill.Id, false);
                }
            }
        }

        /// <summary>
        /// SkillInfo内に、習得可能性の情報を付与する
        /// トレーナーガイドでは、習得できないスキルも一覧に表示されるので、この情報が必要になる
        /// </summary>
        public void UpdateAvailabilityInfo()
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;

            // もし習得できないものであれば、フラグを立てておく
            var availableSkillSet =
                MasterDataManager.Instance.masterAvailableSkillSet.GetListWithAvailableSkillSetIdOrderByIdAsc(
                    workChara.CardData.AvailableSkillSetId);
            var availableSkillIdList = availableSkillSet.Where(a => a.NeedRank <= workChara.TalentLevel)
                .Select(a => a.SkillId).ToList();
            // TIPSや下位スキルなどを考慮したリストに変換する
            var availableSkillInfoList = SingleModeUtils.CreateSkillInfoBySkillIdList(availableSkillIdList);

            for (int i = 0, cnt = SkillList.Count; i < cnt; i++)
            {
                // どれも含んでいなければ取得できない
                var skillInfo = SkillList[i];
                if (availableSkillInfoList.All(x => x.SkillList.All(y => y.SkillId != skillInfo.SkillId)))
                {
                    skillInfo.SetAvailability(false);
                }
            }
        }
    }
}