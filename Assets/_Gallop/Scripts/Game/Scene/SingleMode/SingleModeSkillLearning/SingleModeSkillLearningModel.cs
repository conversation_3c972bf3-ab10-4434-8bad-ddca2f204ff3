
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    using static SingleModeSkillLearningViewController;
    
    /// <summary>
    /// スキル習得Model
    /// </summary>
    public class SingleModeSkillLearningModel
    {
        public SingleModeSkillLearningModel()
        {
            SkillInfoList = CreateSkillInfoList();
            SortFilteredSkillInfoList = SkillInfoList;
        }

        /// <summary> 仮選択中のSkillId一覧 </summary>
        private HashSet<int> _selectedSkillIdHashSet = new HashSet<int>();
        
        public List<SingleModeSkillLearningSkillInfo> SkillInfoList { get; }

        public List<SingleModeSkillLearningSkillInfo> SortFilteredSkillInfoList { get; private set; }

        /// <summary>
        /// 仮選択中のスキルがあるか
        /// </summary>
        public bool HasSelectedSkill => _selectedSkillIdHashSet.Any();

        /// <summary>
        /// 仮選択ぶんを除いた残りのスキルPt
        /// </summary>
        public int RemainingPoint
        {
            get
            {
                var currentSkillPoint = WorkDataManager.Instance.SingleMode.Character.SkillPoint;
                
                // 選択中のスキル習得に必要なスキルポイント合計
                var totalSkillPointSelectedSkillNeed = SkillInfoList
                    .Sum(skillInfo => skillInfo.SkillList
                        .Where(info => IsSelect(info) && !info.IsAcquired)
                        .Sum(info => info.CurrentNeedPoint)
                    );

                return currentSkillPoint - totalSkillPointSelectedSkillNeed;
            }
        }

        private static List<SingleModeSkillLearningSkillInfo> CreateSkillInfoList()
        {
            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var availableSkillSet = MasterDataManager.Instance.masterAvailableSkillSet.GetListWithAvailableSkillSetIdOrderByIdAsc(workChara.CardData.AvailableSkillSetId);
            // 素質レベル達成しているスキルIDをリストアップ
            var availableSkillIdList = availableSkillSet.Where(a => a.NeedRank <= workChara.TalentLevel).Select(a => a.SkillId).ToList();

            // 育成チュートリアルで習得させたいスキルが含まれてなければ挿入
            if (TutorialSingleMode.IsTutorial)
            {
                var tutorialSkillId = TutorialSingleMode.GetSkillLeaningSkillId();
                if (availableSkillIdList.Contains(tutorialSkillId) == false)
                {
                    availableSkillIdList.Add(tutorialSkillId);
                }
            }

            var skillInfoList = SingleModeUtils.CreateSkillInfoBySkillIdList(availableSkillIdList);

            // 進化先があるスキルに進化ボタンを表示する情報付与
            var upgradeSkillIdList = WorkDataManager.Instance.SingleMode.Character.GetSkillUpgradeSkillIdList().ToList();
            foreach (var skillInfo in skillInfoList)
            {
                foreach (var skillInfoChild in skillInfo.SkillList)
                {
                    if (upgradeSkillIdList.Contains(skillInfoChild.SkillId))
                    {
                        skillInfoChild.IsSkillUpgrade = true;
                    }
                }
            }

            return skillInfoList;
        }

        public void ApplySortFilter(SortFilterSetting sortSetting, SingleModeSkillLearningListSortFilterService.SkillFilterSetting skillFilterSetting)
        {
            SortFilteredSkillInfoList = SingleModeSkillLearningListSortFilterService.ExecSortFilter(SkillInfoList, sortSetting, skillFilterSetting);
        }
        
        #region スキル選択操作

        public bool IsSelect(PartsSingleModeSkillLearningListItem.Info info) => _selectedSkillIdHashSet.Contains(info.SkillId);

        /// <summary>
        /// 指定のスキルを仮選択状態にする
        /// </summary>
        public void Select(PartsSingleModeSkillLearningListItem.Info selectedInfo)
        {
            _selectedSkillIdHashSet.Add(selectedInfo.SkillId);
            
            // 選択されたスキルがレアスキルだった場合、未取得の下位互換スキルもまとめて選択対象
            if (PartsSingleModeSkillLearningListItem.IsRareSkill(selectedInfo.MasterData))
            {
                EachSkill(SkillInfoList, info =>
                {
                    if (info.MasterData.IsSameGroupLowerRankSkillThan(selectedInfo.MasterData) && !info.IsAcquired)
                    {
                        _selectedSkillIdHashSet.Add(info.SkillId);
                    }
                });
            }
            
            // レアスキルの習得必要ポイント合算値を再計算
            CalcDiscountAmountBySelectedSameGroupLowRateSkill();
        }
        
        /// <summary>
        /// 指定のスキルの仮選択を解除する
        /// </summary>
        public void Deselect(PartsSingleModeSkillLearningListItem.Info deselectedInfo)
        {
            _selectedSkillIdHashSet.Remove(deselectedInfo.SkillId);
            
            // 解除されたスキルがレアスキル以外だった場合、選択中の上位互換スキルもまとめて解除対象
            if (!PartsSingleModeSkillLearningListItem.IsRareSkill(deselectedInfo.MasterData))
            {
                EachSkill(SkillInfoList, info =>
                {
                    if (info.MasterData.IsSameGroupHigherRankSkillThan(deselectedInfo.MasterData) && !info.IsAcquired)
                    {
                        _selectedSkillIdHashSet.Remove(info.SkillId);
                    }
                });
            }
            
            // 解除されたスキルがレアスキルだった場合、フィルタから弾かれている未取得の下位互換スキルもまとめて解除対象 (下位スキルが見えないところで選択に残り続けることを防ぐ)
            if (PartsSingleModeSkillLearningListItem.IsRareSkill(deselectedInfo.MasterData))
            {
                var deselectedSkillInfo = SkillInfoList.FirstOrDefault(skillInfo => skillInfo.SkillList.Any(info => info.SkillId == deselectedInfo.SkillId));
                // レアスキルのSKillInfoは現状1スキルしか入らない想定だが、万が一複数レアスキルがまとめられる場合はもっとも下位のレアスキルを解除した時のみ下位スキル自動解除をおこなう
                var deSelectedSkillIndex = deselectedSkillInfo?.SkillList.IndexOf(deselectedInfo);
                if (deSelectedSkillIndex == 0)
                {
                    EachSkill(SkillInfoList, info =>
                    {
                        if (info.MasterData.IsSameGroupLowerRankSkillThan(deselectedInfo.MasterData) && !info.IsAcquired)
                        {
                            // 未取得で、かつSortFilteredSkillInfoListに含まれない下位スキルをすべて解除
                            if (SortFilteredSkillInfoList.Any(skillInfo => skillInfo.SkillList.Any(x => x.SkillId == info.SkillId)) == false)
                            {
                                _selectedSkillIdHashSet.Remove(info.SkillId);
                            }
                        }
                    });
                }
            }
            
            // レアスキルの習得必要ポイント合算値を再計算
            CalcDiscountAmountBySelectedSameGroupLowRateSkill();
        }

        /// <summary>
        /// 仮選択をすべて解除
        /// </summary>
        public void ResetSelected()
        {
            _selectedSkillIdHashSet.Clear();
            
            // レアスキルの習得必要ポイント合算値を再計算
            CalcDiscountAmountBySelectedSameGroupLowRateSkill();
        }
        
        #endregion

        /// <summary>
        /// 選択中スキル情報をスキル取得通信用のクラスに変換して取得する
        /// </summary>
        public IReadOnlyCollection<GainSkillInfo> GetSelectedGainSkillInfos()
            => _selectedSkillIdHashSet.Select(skillId => new GainSkillInfo { skill_id = skillId, level = SkillDefine.SKILL_LEVEL_MIN }).ToArray();
        
        /// <summary>
        /// 取得選択状態に応じて、レアスキルの習得必要ポイントから減算して見せる量を再計算する
        /// </summary>
        private void CalcDiscountAmountBySelectedSameGroupLowRateSkill()
        {
            foreach (var skillInfo in SkillInfoList)
            {
                if (skillInfo.SkillList.IsNullOrEmpty()) continue;

                var lastInfo = skillInfo.SkillList[0];
                // レア以外は計算の必要なし
                if (PartsSingleModeSkillLearningListItem.IsRareSkill(lastInfo.MasterData) == false)
                {
                    continue;
                }

                // 選択中下位互換スキルの必要Ptを合算してセットする
                var discountAmount = SumDiscountAmount(SkillInfoList, lastInfo);
                lastInfo.SetDiscountAmountBySelectedSameGroupLowRateSkill(discountAmount);
            }

            // 下位互換スキルのうち、未取得かつ選択中なものの必要スキルポイントを合算する
            int SumDiscountAmount(List<SingleModeSkillLearningSkillInfo> calcSkillInfoList, PartsSingleModeSkillLearningListItem.Info targetInfo)
            {
                return calcSkillInfoList
                    .Sum(info => info.SkillList
                        .Where(itemInfo => !itemInfo.IsAcquired && IsSelect(itemInfo) && itemInfo.MasterData.IsSameGroupLowerRankSkillThan(targetInfo.MasterData))
                        .Sum(itemInfo => itemInfo.CurrentNeedPoint));
            }
        }

        /// <summary>
        /// 各スキルに対する処理
        /// </summary>
        private void EachSkill(List<SingleModeSkillLearningSkillInfo> skillInfos, System.Action<PartsSingleModeSkillLearningListItem.Info> action)
        {
            foreach (var skillInfo in skillInfos)
            {
                foreach (var info in skillInfo.SkillList)
                {
                    action?.Invoke(info);
                }
            }
        }
        
        /// <summary>
        /// 引数SkillListリストから、それと同じスキルを持つクラス内SkillListを持つSingleModeSkillLearningSkillInfoを取得する
        /// </summary>
        /// <remarks>
        /// 育成で取得できるスキルはクラス内SkillListで管理されるが、トレーナーガイドではそれとは別に表示用にSkillListを使用するので、<br/>
        /// それらの対応付けのためにこの関数を使用する
        /// </remarks>>
        /// <param name="skillList">  </param>
        /// <returns> 合致するものがない場合はnullを返す </returns>
        public SingleModeSkillLearningSkillInfo GetSkillInfoByItemInfoList(
            List<PartsSingleModeSkillLearningListItem.Info> skillList)
        {
            if (skillList.IsNullOrEmpty())
            {
                return null;
            }
            // SkillList間でSkillIDが重複することはないので、SkillIDでの比較で問題ないはず
            return GetEnableLearningSkill(skillList[0].SkillId);
        }
        
        /// <summary>
        /// 渡されたskill_idのスキルヒントを所得済みかどうか返す
        /// </summary>
        public bool IsEnableLearningSkill(int skillId)
        {
            return GetEnableLearningSkill(skillId) != null;
        }

        /// <summary>
        /// 渡されたskill_idのスキルヒントを所得済みのものを返す
        /// </summary>
        /// <param name="skillId"></param>
        /// <returns></returns>
        private SingleModeSkillLearningSkillInfo GetEnableLearningSkill(int skillId)
        {
            var enableLearningSkill = SkillInfoList.FirstOrDefault(skillInfo =>
                skillInfo.SkillList.Any(skill => skill.SkillId == skillId));
            return enableLearningSkill;
        }
    }
}
