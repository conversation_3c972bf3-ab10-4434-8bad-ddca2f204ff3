using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static SingleModeSkillLearningListSortFilterService;
    
    /// <summary>
    /// シングルモードスキル習得：スキルフィルターボタン
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeSkillLearningSkillFilterButton : MonoBehaviour
    {
        [Serializable]
        private class ActiveFilterItem
        {
            [field:SerializeField] public GameObject GameObject { get; private set; }
            [field:SerializeField] public TextCommon Text { get; private set; }
        }
            
        [SerializeField] private ButtonCommon _button;

        /// <summary> 有効になっているフィルター項目のRoot </summary>
        [SerializeField] private RectTransform _activeFilterItemRoot;
        /// <summary> 有効になっているフィルター項目 </summary>
        [SerializeField] private ActiveFilterItem[] _activeFilterItemArray;

        /// <summary> 未設定状態の際に表示するオブジェクト </summary>
        [SerializeField] private GameObject _notSetItem;
        
        private Action<SkillFilterSetting> _skillFilterSettingSetter;
        private Func<SkillFilterSetting> _skillFilterSettingGetter;
        
        public void Setup(Func<SkillFilterSetting> getter, Action<SkillFilterSetting> setter)
        {
            _skillFilterSettingGetter = getter;
            _skillFilterSettingSetter = setter;
            
            _button.SetOnClick(OnClickFilterButton);

            SetupActiveFilters();
        }

        private void SetupActiveFilters()
        {
            var currentSetting = _skillFilterSettingGetter?.Invoke();
            var displayableFilters = currentSetting?.EnableFilters?.Where(IsNeedDisplayFilterMenu).ToArray() ?? Array.Empty<SkillFilterSetting.FilterMenu>();

            if (!displayableFilters.Any())
            {
                // 表示可能なフィルターが設定されていない場合は「未設定」
                _activeFilterItemRoot.SetActiveWithCheck(false);
                _notSetItem.SetActiveWithCheck(true);
                return;
            }
            
            _activeFilterItemRoot.SetActiveWithCheck(true);
            _notSetItem.SetActiveWithCheck(false);

            for(var i = 0; i < _activeFilterItemArray.Length; i++)
            {
                var activeFilterItem = _activeFilterItemArray[i];
                var isValid = i < displayableFilters.Length;
                
                activeFilterItem.GameObject.SetActiveWithCheck(isValid);
                if (isValid)
                {
                    activeFilterItem.Text.text = displayableFilters[i].GetEnumDisplayName();
                }
            }
        }

        private void OnClickFilterButton()
        {
            var currentSetting = _skillFilterSettingGetter?.Invoke();
            DialogSingleModeSkillLearningSkillFilter.PushDialog(currentSetting, newSetting =>
            {
                _skillFilterSettingSetter?.Invoke(newSetting);
                
                SetupActiveFilters();
            });
        }

        /// <summary>
        /// フィルタ設定が有効になっている場合に 「変更」ボタン横に表示するか
        /// </summary>
        private bool IsNeedDisplayFilterMenu(SkillFilterSetting.FilterMenu filterMenu)
        {
            switch (filterMenu)
            {
                case SkillFilterSetting.FilterMenu.Turf:
                case SkillFilterSetting.FilterMenu.Dirt:
                case SkillFilterSetting.FilterMenu.Short:
                case SkillFilterSetting.FilterMenu.Mile:
                case SkillFilterSetting.FilterMenu.Middle:
                case SkillFilterSetting.FilterMenu.Long:
                case SkillFilterSetting.FilterMenu.Nige:
                case SkillFilterSetting.FilterMenu.Senko:
                case SkillFilterSetting.FilterMenu.Sashi:
                case SkillFilterSetting.FilterMenu.Oikomi:
                    return true;
                case SkillFilterSetting.FilterMenu.IsAlwaysContainUpgradeSkill:
                default:
                    return false;
            }
        }
    }
}
