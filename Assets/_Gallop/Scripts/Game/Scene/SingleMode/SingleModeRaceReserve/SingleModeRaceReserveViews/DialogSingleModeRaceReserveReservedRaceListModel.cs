using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 予約レース一覧ダイアログ
    /// </summary>
    public class DialogSingleModeRaceReserveReservedRaceListModel
    {
        #region 定数, class

        public const string PrefabPath = ResourcePath.MULTI_RESERVE_PARTS_PATH + "DialogSingleModeRaceReserveReservedRaceList";


        public enum Type
        {
            Normal, // 通常
            Preset, // プリセットから押されたとき
        }


        public class Entity
        {
            public SingleModeDefine.DegreeType Degree;
            public int Turn;
            public int ProgramId;

            public bool IsDisable;          // 無効(予約できない)
            public string ImageFilaname;    // ファイル名
            public bool IsTarget;           // 目標
            public bool IsReserve;         // 予約中
            public bool IsSelect;           // 選択中
            public bool IsGrayout;          // グレイアウト状態
            public string Title;

            public static Entity CreateAtDisable(string title) =>
                new Entity { IsDisable = true, Title = title };

            public bool IsTransitionable => !IsDisable && !IsGrayout;
        }


        /// <summary>
        /// 年代ごとのデータ
        /// </summary>
        public class DegreeData
        {
            public List<Entity> Entites { get; } = new List<Entity>();


            public void Add(Entity entity) =>
                Entites.Add(entity);
        }

        #endregion

        #region private変数

        private Dictionary<SingleModeDefine.DegreeType, DegreeData> _degreeDict = new Dictionary<SingleModeDefine.DegreeType, DegreeData>();
        private IEnumerable<MasterSingleModeRouteRace.SingleModeRouteRace> _routeRaces;
        private bool _isTargetLabelEnabled;

        #endregion

        #region プロパティ

        public string Title => TextId.SingleMode418024.Text();
        public string CanterButtonText => TextId.Common0007.Text();

        public DegreeData CurrentDegreeData => _degreeDict[CurrentDegree];

        /// <summary>
        /// 現在の年数
        /// </summary>
        public SingleModeDefine.DegreeType CurrentDegree { get; private set; }

        /// <summary>
        /// 現在のトグルインデックス
        /// </summary>
        public int CurrentToggleIndex => (int)CurrentDegree - 1;

        public List<Entity> CurrentEntities => _degreeDict[CurrentDegree].Entites;

        public SingleModeRaceReserve.Context Context { get; private set; }

        public int DeckIndex { get; private set; }

        public int CurrentTurn { get; private set; }

        /// <summary>
        /// 更新された場合true
        /// </summary>
        public bool IsUpdated { get; set; }

        /// <summary>
        /// プリセットボタンの有無
        /// </summary>
        public bool IsPresetButtonActive { get; private set; }

        /// <summary>
        /// 予約リセットボタンの有無
        /// </summary>
        public bool IsReserveResetButtonActive { get; private set; }
        
        /// <summary>
        /// 前ターンの遷移の有効化
        /// </summary>
        public bool IsPrevTurnEnabled { get; private set; }

        #endregion

        #region public, protectedメソッド

        public void Setup(Type type, SingleModeRaceReserve.Context context, int deckIndex, SingleModeDefine.DegreeType degree, int turn)
        {
            Context = context;
            DeckIndex = deckIndex;
            CurrentDegree = degree;
            CurrentTurn = turn;

            switch (type)
            {
                // 通常ダイアログではプリセットボタン有効
                case Type.Normal:
                    IsPresetButtonActive = true;
                    IsReserveResetButtonActive = true;
                    _isTargetLabelEnabled = true;
                    break;

                // プリセットからの編集では前ターンの遷移も有効
                case Type.Preset:
                    IsPrevTurnEnabled = true;
                    break;
            }

            _routeRaces = WorkDataManager.Instance.SingleMode.GetTargetRaceEnumerable();
            UpdateDegreeEntityAll();
        }

        public void SetToggleIndex(int index)
        {
            switch (index)
            {
                case 0:
                    CurrentDegree = SingleModeDefine.DegreeType.Junior;
                    break;

                case 1:
                    CurrentDegree = SingleModeDefine.DegreeType.Classic;
                    break;

                case 2:
                    CurrentDegree = SingleModeDefine.DegreeType.Senior;
                    break;
            }
        }

        public void SetTurn(int turn)
        {
            CurrentTurn = turn;
            CurrentDegree = Context.GetDegreeByTurn(turn);
        }

        public void UpdateDegreeEntityAll()
        {
            _degreeDict.Clear();

            // 年代ごとのデータ構築
            SetupAtDegree(SingleModeDefine.DegreeType.Junior);
            SetupAtDegree(SingleModeDefine.DegreeType.Classic);
            SetupAtDegree(SingleModeDefine.DegreeType.Senior);
        }
        
        /// <summary>
        /// 予約全リセットを実行する
        /// </summary>
        /// <param name="onSuccess"></param>
        /// <returns>true = 予約リセット通信を実行（成否問わず）、 false = 通信非実行</returns>
        public bool TryAllReserveReset(System.Action onSuccess)
        {
            const int ACTIVE_DECK_INDEX = 0;
            var deckEntity = Context.ReserveRepository.GetDeckEntity(ACTIVE_DECK_INDEX);
            if (deckEntity.ReservedRaceCount == 0)
            {
                // 予約レースが存在しないため何もしない
                return false;
            }

            // 現在予約しているレースを全てキャンセル対象に登録
            var builder = Context.RequestBuilder;
            builder.Clear();
            foreach (var turnEntity in deckEntity.GetAllValues())
            {
                builder.PushCancel(turnEntity.ServerData.ToDegreeType(), ACTIVE_DECK_INDEX, turnEntity.Turn);
            }
            builder.Build(out _, out var cancels);

            // 通信
            Context.Connect.RequestMultiRaceReserve(ACTIVE_DECK_INDEX, cancels, 
                () => onSuccess?.Invoke()
                );
            return true;
        }

        #endregion

        #region privateメソッド

        private void SetupAtDegree(SingleModeDefine.DegreeType degree)
        {
            var turnRepository = DeckIndex == 0 ? Context.TurnRepository : Context.DefaultUraTurnRepository;
            var turnEntity = turnRepository.FindEntity(degree);
            if (turnEntity == null) return;

            var degreeData = new DegreeData();

            foreach (var master in turnEntity.TurnMasters)
            {
                var text = GetMonthTextByTurn(master);

                if (turnEntity.CanReserve(master))
                {
                    var entity = new Entity();

                    SetupEntityData(master, degree, entity);

                    degreeData.Add(entity);
                }
                else
                {
                    degreeData.Add(Entity.CreateAtDisable(text));
                }
            }

            _degreeDict.Add(degree, degreeData);
        }


        /// <summary>
        /// ターンが何月〇半かテキスト取得
        /// </summary>
        public string GetMonthTextByTurn(MasterSingleModeTurn.SingleModeTurn master)
        {
            var monthText = SingleModeUtils.GetMonthText(master.Month);
            var half = master.HalfOfMonthType.Text();

            return $"{monthText} {half}";
        }

        /// <summary>
        /// エンティティ構築に必要なデータを設定する
        /// </summary>
        public void SetupEntityData(MasterSingleModeTurn.SingleModeTurn turnMaster, SingleModeDefine.DegreeType degree, Entity entity)
        {
            var turn = turnMaster.Turn;
            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var currentTurn = workSingleMode.GetCurrentTurn();
            var programs = Context.GetPrograms(turn, DeckIndex);
            var entryViewModel = Context.EntryViewModel;

            // 目標
            if (IsTargetLabelEnabled(turnMaster.Turn) && _routeRaces != null)
            {
                var targetProgram = programs.FirstOrDefault(p => 
                {
                    var routeRaceData = entryViewModel.GetMasterTargetRouteRaceData(p, degree, turn);
                    if (routeRaceData == null) return false;
                    if (!workSingleMode.IsActiveTargetRace(routeRaceData)) return false;    // 目標がアクティブか

                    return true;
                });

                entity.IsTarget = targetProgram != null;
                entity.ImageFilaname = Context.GetRaceFilename(targetProgram);
                entity.ProgramId = targetProgram?.Id ?? 0;
            }

            // 以前のレースの予約が出来ないときはグレイアウト判定
            if (!IsPrevTurnEnabled)
            {
                entity.IsGrayout = turnMaster.Turn < currentTurn;
            }

            // 予約中
            if (!entity.IsGrayout)
            {
                if (Context.TryGetReservedProgramId(degree, turn, DeckIndex, out var reservedProgramId))
                {
                    entity.IsReserve = true;

                    if (string.IsNullOrEmpty(entity.ImageFilaname))
                    {
                        var programMaster = MasterDataManager.Instance.masterSingleModeProgram.Get(reservedProgramId);
                        entity.ImageFilaname = Context.GetRaceFilename(programMaster);
                        entity.ProgramId = reservedProgramId;
                    }
                }
            }

            // ターンテキスト
            entity.Title = GetMonthTextByTurn(turnMaster);

            entity.Degree = degree;
            entity.Turn = turnMaster.Turn;
            entity.IsSelect = turnMaster.Turn == CurrentTurn;
        }

        /// <summary>
        /// 目標レースの表示が有効か
        /// </summary>
        public bool IsTargetLabelEnabled(int turn)
        {
            // 全体フラグが立っていないときは強制無効
            if (!_isTargetLabelEnabled) return false;

            var workSingleMode = WorkDataManager.Instance.SingleMode;
            var currentTurn = workSingleMode.GetCurrentTurn();

            return turn >= currentTurn;
        }

        #endregion
    }
}
