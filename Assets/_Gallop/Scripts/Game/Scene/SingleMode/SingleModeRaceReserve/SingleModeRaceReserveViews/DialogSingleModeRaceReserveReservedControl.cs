using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 予約レース一覧ダイアログ
    /// </summary>
    [RequireComponent(typeof(DialogSingleModeRaceReserveReservedView))]
    public class DialogSingleModeRaceReserveReservedControl : MonoBehaviour
    {
        #region 定数, class
        #endregion

        #region private変数

        [SerializeField] private SingleModeRaceReserveListCoreControl _listCoreControl;

        private DialogSingleModeRaceReserveReservedView _view;
        private DialogSingleModeRaceReserveReservedModel _model = new DialogSingleModeRaceReserveReservedModel();
        private System.Action _onUpdate;
        private System.Action<bool /* isUpdated*/> _onClosed;

        #endregion

        #region プロパティ
        #endregion

        #region public, protectedメソッド

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(SingleModeRaceReserve.Context context, System.Action onUpdate = null, System.Action<bool /* isUpdated*/> onClosed = null) =>
            Open(context, 0, onUpdate, onClosed);

        public static void Open(SingleModeRaceReserve.Context context, int deckIndex, System.Action onUpdate = null, System.Action<bool /* isUpdated*/> onClosed = null)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(DialogSingleModeRaceReserveReservedModel.PrefabPath))
                .GetComponent<DialogSingleModeRaceReserveReservedControl>();

            instance.Setup(deckIndex, context, onUpdate, onClosed);
        }

        #endregion

        #region privateメソッド

        private void Setup(int deckIndex, SingleModeRaceReserve.Context context, System.Action onUpdate = null, System.Action<bool /* isUpdated*/> onClosed = null)
        {
            _onUpdate = onUpdate;
            _onClosed = onClosed;

            // ビルダーをクリアしておく
            context.RequestBuilder.Clear();

            _model.SetParam(deckIndex, context);

            _view = GetComponent<DialogSingleModeRaceReserveReservedView>();
            _view.Setup(_model.Title, _model.CancelButtonText, _model.EditButtonText);

            UpdateList();
        }

        private void UpdateList()
        {
            var context = WorkDataManager.Instance.SingleMode.RaceReserveContext;

            var param = new SingleModeRaceReserveListCoreView.Param
            {
                Context = context,
                UseLoopScroll = true,
                IsShowableTitle = true,
                IsCursorEnabled = false,
                ListItemParam = new DialogSingleModeRaceListItem.Param { DisableReserve = true }
            };

            _listCoreControl.Setup(param);

            // デッキの中身を表示する
            // 無ければ空
            var deckEntity = context.ReserveRepository.GetDeckEntity(_model.DeckIndex);
            if (deckEntity.IsNullOrEmpty())
            {
                _view.ShowEmptyText(true);
                _view.SetSelectCancelButtonEnabled(false, TextId.SingleMode418008.Text());

                _listCoreControl.RemoveItemViewAll();
            }
            else
            {
                _view.ShowEmptyText(false);
                _view.SetSelectCancelButtonEnabled(true, string.Empty);

                var reserveData = deckEntity.BuildReserveData();

                // リストアイテムの更新
                _listCoreControl.OnItemUpdate = (index, itemView) =>
                {
                    var elm = reserveData[index];

                    UpdateTitleContents(itemView, elm.degree, elm.turn);

                    // タイトルを表示する(文言取得は動作重いためキャッシュする)
                    itemView.SetTitleContents(elm.degree, _model.GetTitle(elm.degree, elm.turn));
                };

                // アイテムがクリックされたときは詳細あログを開く
                _listCoreControl.OnItemClicked = (index, program, itemView) =>
                {
                    var elm = reserveData[index];

                    DialogSingleModeRaceReserveCancelConfirm.Open(context, elm.degree, elm.turn, elm.program.Id,
                        onOk: () =>
                        {
                            OnUpdateList();
                        });
                };

                _listCoreControl.UpdateList(reserveData);
                _view.UpdateReservedNum(reserveData.Length);

                // 選択キャンセルモードダイアログを開く
                _view.OnSelectCancelButton = () =>
                {
                    DialogSingleModeRaceReserveCancelSelectControl.Open(context, _model.DeckIndex, _model.TitleCache,
                        reserveData,
                        () =>
                        {
                            OnUpdateList();
                        });
                };

            }

            // プリセットダイアログを開く
            _view.OnPresetButton = () =>
            {
                DialogSingleModeRaceReservePresetControl.OpenPresetList(context,
                    isChanged =>
                    {
                        if (isChanged)
                        {
                            OnUpdateList();
                        }
                    });
            };

            // 予約編集を開く
            _view.OnRightClicked = () =>
            {
                DialogSingleModeRaceReserveReserveSetControl.Open(context,
                    turn =>
                    {
                        OnUpdateList();
                    });
            };

            _view.OnLeftClicked = () =>
            {
                // 「更新」が発生したかどうかを指定
                _onClosed?.Invoke(_model.IsUpdated);

                _view.Close();
            };
        }

        private void UpdateTitleContents(SingleModeRaceReserveListCoreTurnItemView itemView, SingleModeDefine.DegreeType degree, int turn)
        {
            // タイトルを表示する(文言取得は動作重いためキャッシュする)
            itemView.SetTitleContents(degree, _model.GetTitle(degree, turn));
        }

        private void OnUpdateList()
        {
            _model.IsUpdated = true;

            _onUpdate?.Invoke();

            // 更新する
            UpdateList();
        }

        #endregion
    }
}
