using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// マイローテ呼び出し完了ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeRaceReserveLoadComplete : DialogInnerBase
    {
        private const string PREFAB_PATH = ResourcePath.MULTI_RESERVE_PARTS_PATH + "DialogSingleModeRaceReserveLoadComplete";
        
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;
        
        [SerializeField] private RectTransform _reserveErrorReasonRoot;
        [SerializeField] private GameObject _reserveErrorReasonTextBase;

        private Action _onClose;

        public static void Open(DialogSingleModeRaceReservePresetModel.BuildResult result, Action onClose)
        {
            if (!result.HasAnyError)
            {
                // 予約できなかったレースがない場合は通常のテキストのみダイアログを用いる
                var simpleDialogData = new DialogCommon.Data();
                simpleDialogData.SetSimpleOneButtonMessage(TextId.SingleMode0277.Text(), TextId.SingleMode508027.Text());
                simpleDialogData.CenterButtonCallBack = _ => onClose?.Invoke();
                DialogManager.PushDialog(simpleDialogData);
                return;
            }
            
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeRaceReserveLoadComplete>(PREFAB_PATH);
            dialog.Setup(result, onClose);
            
            var dialogData = dialog.CreateDialogData();
            DialogManager.PushDialog(dialogData);
        }
        
        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.SingleMode0277.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            data.CenterButtonCallBack = _ => _onClose?.Invoke();;
            return data;
        }

        private void Setup(DialogSingleModeRaceReservePresetModel.BuildResult result, Action onClose)
        {
            _onClose = onClose;
            
            if(result.IsTargetRaceTurn) CreateErrorReasonItem(TextId.SingleMode508029.Text());
            if(result.IsOverTurn || result.IsNotFount) CreateErrorReasonItem(TextId.SingleMode508030.Text());
            if(result.IsPrevTurn) CreateErrorReasonItem(TextId.SingleMode508031.Text());
        }

        private void CreateErrorReasonItem(string reason)
        {
            _reserveErrorReasonTextBase.SetActiveWithCheck(true);

            var obj = Instantiate(_reserveErrorReasonTextBase, _reserveErrorReasonRoot);
            var text = obj.GetComponent<TextCommon>();

            text.text = reason;
            
            _reserveErrorReasonTextBase.SetActiveWithCheck(false);
        }
    }
}
