using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 因子一覧
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeFactorList : DialogInnerBase
    {
        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField]
        private PartsFactorTree _factorTree1 = null;
        [SerializeField]
        private PartsFactorTree _factorTree2 = null;

        public static void Open(WorkTrainedCharaData.TrainedCharaData trainedChara1,WorkTrainedCharaData.TrainedCharaData trainedChara2)
        {
            var dialogContentobj = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_SINGLE_MODE_FACTOR_LIST));
            var dialogContent = dialogContentobj.GetComponent<DialogSingleModeFactorList>();
            var dialogData = dialogContent.CreateDialogData();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.Title = TextId.SingleMode0398.Text();
            dialogData.AutoClose = true;
            dialogData.DispStackType =  DialogCommon.DispStackType.DialogOnDialog;
            dialogContent.Setup(trainedChara1,trainedChara2);
            DialogManager.PushDialog(dialogData);
        }

        public void Setup(WorkTrainedCharaData.TrainedCharaData trainedChara1,WorkTrainedCharaData.TrainedCharaData trainedChara2)
        {
            _factorTree1.Setup(trainedChara1, SingleModeDefine.SuccessionEffectedPosition.First, DialogHash, false);
            _factorTree2.Setup(trainedChara2, SingleModeDefine.SuccessionEffectedPosition.Second, DialogHash, false);
        }
    }
}