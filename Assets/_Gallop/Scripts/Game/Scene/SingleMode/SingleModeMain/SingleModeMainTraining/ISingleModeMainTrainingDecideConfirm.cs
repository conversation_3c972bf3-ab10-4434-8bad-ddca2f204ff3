using System;

namespace Gallop
{
    /// <summary>
    /// 育成：トレーニング実行確認インターフェース
    /// </summary>
    public interface ISingleModeMainTrainingDecideConfirm
    {
        public void Confirm(Action onDecide);
    }
    
    /// <summary>
    /// 育成：トレーニング実行確認基本動作クラス。追加の確認なし
    /// </summary>
    public class SingleModeMainTrainingDecideConfirm : ISingleModeMainTrainingDecideConfirm
    {
        public void Confirm(Action onDecide)
        {
            onDecide.Invoke();
        }
    }
    

    /// <summary>
    /// 育成：トレーニング実行確認シナリオ別クラス生成
    /// </summary>
    public class SingleModeMainTrainingDecideConfirmFactory
    {
        public static ISingleModeMainTrainingDecideConfirm Create()
        {
            switch (WorkDataManager.Instance.SingleMode.GetScenarioId())
            {
                case SingleModeDefine.ScenarioId.Mecha:
                    return new SingleModeMainTrainingDecideConfirmScenarioMecha();

                default:
                    return new SingleModeMainTrainingDecideConfirm();
            }
        }
    }
}
