using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// タズナさんのメッセージ選択に係る処理をまとめたクラス
    /// </summary>
    public partial class SingleModeTazunaMessageSelector
    {
        private List<MasterSingleModeMessage.SingleModeMessage> _cashMessageList;
        
        public MasterSingleModeMessage.SingleModeMessage GetTazunaMessageTop()
        {
            _cashMessageList ??= MasterDataManager.Instance.masterSingleModeMessage.dictionary.Values.ToList();
            
            //Topにふさわしい文言が見つからなかったら、成長具合にあわせた文言を探す

            var growthProspectInfo = new SingleModeCharacterGrowthProspectInfo();

            //有効なメッセージが有るかどうかをチェックする
            var validMessageList = _cashMessageList
                .Where(message =>
                {
                    var conditions = MessageConditionFactory.Create(message, growthProspectInfo);
                    return conditions != null && conditions.All(condition => condition.IsMatch);
                }).ToList();

            //一つも見つからなかった場合（ここには来ない想定だけれども一応）
            if (validMessageList.IsNullOrEmpty()) return null;
            
            //抽選した結果を返す
            //一番優先度の高いMessageを取得する
            var maxPriority = validMessageList.Max(msg => msg.Priority);
            var maxPriorityMessageList = validMessageList.Where(msg => msg.Priority == maxPriority).ToList();

            // 抽選する
            return maxPriorityMessageList[ UnityEngine.Random.Range( 0, maxPriorityMessageList.Count ) ];
        }
    }
}
