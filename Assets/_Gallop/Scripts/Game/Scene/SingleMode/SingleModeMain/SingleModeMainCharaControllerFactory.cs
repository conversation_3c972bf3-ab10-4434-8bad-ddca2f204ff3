using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// シナリオ別SingleModeMainCharaController生成
    /// </summary>
    public static class SingleModeMainCharaControllerFactory
    {
        public static SingleModeMainCharaController Create()
        {
            return WorkDataManager.Instance.SingleMode.GetScenarioId() switch
            {
                SingleModeDefine.ScenarioId.TeamRace => new SingleModeMainCharaScenarioTeamRaceController(),
                SingleModeDefine.ScenarioId.Venus => new SingleModeMainCharaScenarioVenusController(),
                SingleModeDefine.ScenarioId.Mecha => new SingleModeMainCharaScenarioMechaController(),
                _ => new SingleModeMainCharaController(),
            };
        }
    }
}

