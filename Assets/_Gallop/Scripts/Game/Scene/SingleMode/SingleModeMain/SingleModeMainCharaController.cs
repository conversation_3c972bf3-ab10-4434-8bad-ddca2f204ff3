using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 衣装関連
    /// </summary>
    public static class SingleModeMainDressUtil
    {
        /// <summary>
        /// 適切な衣装IDを取得する
        /// </summary>
        public static int GetPreferredDressId(WorkSingleModeData workData, int dressId)
        {
            var season = workData.GetSeason();
            var seasonSub = GallopUtil.GetBgSeasonSub(season);
            var rarity = workData.Character.CardRarityData;

            return GetPreferredDressId(seasonSub, rarity, dressId);
        }

        /// <summary>
        /// 適切な衣装IDを取得する
        /// </summary>
        public static int GetPreferredDressId(GameDefine.BgSeasonSub seasonSub, MasterCardRarityData.CardRarityData rarityData, int dressId)
        {
            // 季節に合わせて衣装を変える
            dressId = GetSeasonDressId(seasonSub, dressId);

            // 水着キャラの場合は学校指定水着を固有衣装に変える
            dressId = GetSwimsuitDressId(rarityData, dressId);

            return dressId;
        }

        /// <summary>
        /// 季節に合わせて衣装を変える
        /// </summary>
        public static int GetSeasonDressId(GameDefine.BgSeasonSub seasonSub, int dressId)
        {
            return MasterDataManager.Instance.masterDressData.ConvertClothIdBySeason((int)seasonSub, dressId);
        }

        /// <summary>
        /// 水着キャラの場合は通常水着を水着勝負服に変える
        /// </summary>
        private static int GetSwimsuitDressId(MasterCardRarityData.CardRarityData rarityData, int dressId)
        {
            switch (WorkDataManager.Instance.SingleMode.GetScenarioId())
            {
                case SingleModeDefine.ScenarioId.Sport:
                    // #131584 アスリート編の競技トレーニングでは水着勝負服による上書きをおこなわない
                    return dressId;
            }

            return TryGetSwimsuitDressIdAndHeadId(rarityData, dressId, out int swimsuitId, out int _)
                ? swimsuitId    // 水着キャラなら水着勝負服のIDを返す
                : dressId;      // 対象外なら元の衣装IDを返す
        }

        /// <summary>
        /// 水着キャラの衣装IDと頭IDを取得する
        /// </summary>
        public static bool TryGetSwimsuitDressIdAndHeadId(MasterCardRarityData.CardRarityData rarityData, int dressId, out int swimsuitId, out int headSubId)
        {
            swimsuitId = dressId;
            headSubId = ModelLoader.DEFAULT_HEAD_SUB_ID;

            // まずは今の衣装が通常水着（スク水やバックダンサー水着）かどうか判定する
            if (!MasterDressData.DressData.IsNormalSwimsuit((ModelLoader.DressID)dressId))
            {
                // ジャージなどを着用している場合は対象外
                return false;
            }

            // 通常水着を着用していた場合はそのキャラのレース衣装をチェックする
            // 育成時のレアリティから衣装を取得しつつ衣装変更が適用されていたらそちらを参照するようにする
            int raceDressId = WorkDataManager.Instance.SingleMode.Character.GetRaceDressId(true);
            var dress = MasterDataManager.Instance.masterDressData.Get(raceDressId);
            if (dress?.IsSpecialSwimsuit() != true)
            {
                // 水着勝負服じゃないので対象外
                return false;
            }

            // 水着勝負服(RaceDressId=100130等)だった場合, そのDressIdを返す
            // またその衣装に対応するHeadIdを返す
            swimsuitId = dress.Id;
            headSubId = dress.HeadSubId;
            return true;
        }
    }

    /// <summary>
    /// キャラクタ周りの管理
    /// <see cref="SingleModeMainView"/>
    /// </summary>
    public class SingleModeMainCharaController : ISingleModeMainController
    {
        #region シナリオ拡張

        /// <summary> 育成TOPでシナリオ別カメラプリセットを使用するか。アオハル杯編など </summary>
        protected virtual bool UseMainViewFocusCameraScenarioPreset => false;

        /// <summary> トレーニングTOPでシナリオ別カメラプリセットを使用するか。メカ編など </summary>
        protected virtual bool UseTrainingViewFocusCameraScenarioPreset => false;

        /// <summary> リアルタイムシャドウを利用するかを指定するフラグ（現状メカ編のみ利用） </summary>
        public virtual bool UseRealTimeShadow => false;

        /// <summary> 育成TOPで表示する背景キャラクターのセットアップ。アオハル杯編など </summary>
        protected virtual void SetupBGCharaModel() { }

        /// <summary> トレーニング選択画面で表示する背景キャラクターのセットアップ。メカ編など </summary>
        public virtual void SetupTrainingBGCharaModel(MasterSingleModeTraining.SingleModeTraining trainingData, bool playMotion) { }

        /// <summary> イメージエフェクトの設定が終わってから行いたい処理 </summary>
        public virtual void AfterSetupImageEffect() { }

        /// <summary> シナリオ別メインキャラ配置調整が必要ならば対応する。Venus編GMターンなど </summary>
        public virtual void UpdateMainCharaPositionByScenario() { }

        /// <summary> リソース解放する際に子クラスで実装したい処理 </summary>
        protected virtual void OnClearBgModel() { }

        /// <summary> トレーニング画面のモデル表示切替 </summary>
        protected virtual void OnSetVisibleTrainingModel(bool visible) { }

        #endregion

        protected SingleModeSceneController _scene;
        protected SingleModeMainTrainingController _trainingController;
        private ISingleModeMainBaseView _baseView;

        private readonly Dictionary<int, SingleRaceModelController> _trainingModelDic = new Dictionary<int, SingleRaceModelController>();

        /// <summary>
        /// 背景用キャラが育成TOP用かトレーニング選択用かの表示タイプ
        /// </summary>
        public enum BgDisplayType
        {
            Top, // 育成TOP
            Training,// トレーニング選択
        }
        protected readonly Dictionary<BgDisplayType, List<SingleModeModelManager.Model>> _bgModelListDic = new Dictionary<BgDisplayType, List<SingleModeModelManager.Model>>();

        protected SingleModeScenarioContentsTopLightController _lightController;
        protected class CameraData
        {
            public Vector3 FocusCameraPos;
            public float FocusCameraFOV;
        }
        protected CameraData _defaultCameraData;


        public System.Action<SingleRaceModelController> OnModelControllerUpdated; // モデルが更新されたときに呼び出される


        /// <summary>
        /// Top画面で表示しているキャラモデル
        /// </summary>
        public SingleRaceModelController ModelController { get; private set; }

        /// <summary>
        /// キャラ吹き出しを制限するフラグ
        /// </summary>
        public bool IsRestrictCharaMessage { get; set; }

        // 賑やかしの背景キャラより、育成キャラは手前に描画する
        private const int NEAR_RENDER_QUEUE = 17; // 0~17　orderが若いほど奥に描画される　育成キャラは常に手前

        public void Initialize()
        {
            var serviceLocator = SingleModeMainServiceLocator.Instance;

            _scene = serviceLocator.Resolve<SingleModeSceneController>();
            _trainingController = serviceLocator.Resolve<SingleModeMainTrainingController>();
            _baseView = serviceLocator.Resolve<ISingleModeMainBaseView>();
        }

        public void SetupModel()
        {
            //3Dモデルの準備
            SetupTopCharaModel();

            //育成TOP背景に表示するキャラモデル
            SetupBGCharaModel();
        }

        /// <summary>
        /// 育成TOPのウマ娘モデル生成
        /// </summary>
        public void SetupTopCharaModel()
        {
            if (WorkDataManager.Instance.SingleMode.HomeInfo.IsShowTazunaAtHomeTop())
            {
                // たづなを表示させるための特殊設定
                SetupTopTazunaCharaModel();
            }
            else
            {
                // 通常の育成キャラ設定
                SetupTopAndTrainingCharaModel();
            }
        }

        /// <summary>
        /// 育成TOP＆トレーニングのウマ娘モデル生成
        /// 特殊な状況を除いて、通常はこちらを利用する
        /// </summary>
        private void SetupTopAndTrainingCharaModel()
        {
            var cardId = WorkDataManager.Instance.SingleMode.Character.CardId;

            var topDressIdList = GetTopCharaModelDressIdList();             // TOP必要な衣装リスト取得
            var trainingDressIdList = GetTrainingCharaModelDressIdList();// トレーニングに必要な衣装リスト取得
            var newDressIdList = topDressIdList.Concat(trainingDressIdList).Distinct().ToList();　// 結合重複削除
            // 必要なモデルが未生成なら生成する
            for (int i = 0; i < newDressIdList.Count; i++)
            {
                var dressId = newDressIdList[i];
                // ターンで固定したランダム値から衣装カラーバリエーションを取得
                var dressColorId = WorkDataManager.Instance.SingleMode.GetDressColorIdFixedTurn(dressId);

                if (_trainingModelDic.TryGetValue(dressId, out var model))
                {
                    // すでに生成済み、カラーバリエーションまで同じなら生成しなくてよい
                    if (model.GetBuildInfo().BackDancerColorId == dressColorId)
                    {
                        continue;
                    }
                    else
                    {
                        // カラーバリエーション違いを生成したいので、既存モデルは破棄
                        _scene.DestroyModel(cardId, dressId);
                        _trainingModelDic.Remove(dressId);
                    }
                }

                // モデル生成
                model = _scene.GetModel(cardId, dressId);
                if (model == null)
                {
                    model = _scene.CreateModel(cardId, dressId);
                    // トレーニングモデル
                    if (trainingDressIdList.Contains(dressId))
                    {
                        model.transform.rotation = Quaternion.LookRotation(Vector3.back);
                        model.ResetCyspring();
                        model.ReserveWarmingUpCySpring();
                    }
                    // TOPモデル
                    if (topDressIdList.Contains(dressId))
                    {
                        // 賑やかしの背景キャラより、育成キャラは手前に描画する
                        model.UpdateGraphicSettings(NEAR_RENDER_QUEUE);
                        ModelController = model;
                    }
                }
                _trainingModelDic.Add(dressId, model);
            }

            // TOP表示用のたづなモデルが登録されている場合があるので破棄
            DestroyTazunaCharaModel();

            // 生成済みモデルの中から、不要になったモデルを破棄する
            var nowDressIdList = _trainingModelDic.Keys.ToList(); // モデル生成済みの衣装IDリスト
            for (int i = nowDressIdList.Count - 1; i >= 0; i--)
            {
                var dressId = nowDressIdList[i];
                if (newDressIdList.Contains(dressId))
                {
                    _trainingModelDic[dressId].SetVisibleWithCySpring(false); //まずは非表示
                    continue; //必要なモデル
                }

                _scene.DestroyModel(cardId, dressId);// 破棄
                _trainingModelDic.Remove(dressId);
            }
        }

        /// <summary>
        /// TOPにたづなを表示するためのセットアップ
        /// </summary>
        private void SetupTopTazunaCharaModel()
        {
            // たづなさん生成
            var model = _scene.CreateBgCharaModelByCharaId(
                GameDefine.TAZUNA_CHARA_ID, GameDefine.TAZUNA_DRESS_ID).Controller;

            // 賑やかしの背景キャラより、育成キャラは手前に描画する
            model.UpdateGraphicSettings(NEAR_RENDER_QUEUE);
            ModelController = model;

            if (_trainingModelDic.ContainsKey(GameDefine.TAZUNA_DRESS_ID) == false)
            {
                _trainingModelDic.Add(GameDefine.TAZUNA_DRESS_ID, model);
            }
        }

        /// <summary>
        /// TOP表示用たづなモデル破棄処理
        /// </summary>
        private void DestroyTazunaCharaModel()
        {
            // 登録されている場合があるので、削除する
            _scene.DestroyModelByCharaId(GameDefine.TAZUNA_CHARA_ID, GameDefine.TAZUNA_DRESS_ID);

            if (_trainingModelDic.ContainsKey(GameDefine.TAZUNA_DRESS_ID))
            {
                _trainingModelDic.Remove(GameDefine.TAZUNA_DRESS_ID);
            }
        }

        /// <summary>
        /// トレーニングモデルの表示/非表示切り替え
        /// </summary>
        public void SetTrainingModelVisible(bool visible)
        {
            foreach (var model in _trainingModelDic.Values)
            {
                model.SetVisibleWithCySpring(visible);
            }

            OnSetVisibleTrainingModel(visible);
        }

        public void SetMainModelVisible(bool visible)
        {
            ModelController?.SetVisibleWithCySpring(visible);
        }

        public void SetAllModelVisible(bool visible)
        {
            SetMainModelVisible(visible);
            SetTrainingModelVisible(visible);
        }

        /// <summary>
        /// TOPに表示しているモデルを更新する
        /// </summary>
        public void SetMainModelController(SingleRaceModelController modelController)
        {
            ModelController = modelController;

            OnModelControllerUpdated?.Invoke(modelController);
        }

        /// <summary>
        /// 育成TOPのモデル表示にする
        /// （トレーニングなどで別衣装が表示されている状況から使用する）
        /// </summary>
        public void SetMainViewDress()
        {
            //モデルは一旦全て非表示
            SetTrainingModelVisible(false);

            //モデル表示
            var dressId = WorkSingleModeData.GetCurrentTopClothId();
            //季節に合わせて衣装を変える
            dressId = GetPreferredDressId(WorkDataManager.Instance.SingleMode, dressId);

            SetMainModelController(_trainingModelDic[dressId]);

            ModelController.SetVisibleWithCySpring(true);
        }

        /// <summary>
        /// 育成TOPのモデルコントローラーを取得する
        /// </summary>
        public SingleRaceModelController GetMainViewModelController()
        {
            var dressId = WorkSingleModeData.GetCurrentTopClothId();
            dressId = GetPreferredDressId(WorkDataManager.Instance.SingleMode, dressId);
            return _trainingModelDic.TryGetValue(dressId, out var model) ? model : null;
        }

        /// <summary>
        /// Topモデルの更新を行う
        /// 季節に応じて服装を変える
        /// </summary>
        public void UpdateCurrentTopCloth(bool toTop)
        {
            SetMainViewDress();//育成TOPの衣装設定
            ToggleBgModelByTopAndTraining(BgDisplayType.Top); //背景キャラ

            if (toTop)
            {
                //トップ画面へ遷移する
                ModelController.PlayMotion(ModelController.IdleMotionSetMaster, startType: SimpleModelController.MotionStartType.Loop, bodyBlendTime: 0, isForce: true);
                ModelController.PlayableAnimator.UpdateMotion(0f); //モーションを即時反映させる
                ModelController.PlayableAnimator.Evaluate();
            }
            else
            {
                ModelController.PlayMotion(ModelController.IdleMotionSetMaster);
            }
            ModelController.transform.rotation = Quaternion.LookRotation(Vector3.back);
            ModelController.ResetCyspring();
            ModelController.ReserveWarmingUpCySpring();
            ModelController.ShoesOffsetOff();

            // カメラ位置に身長差補正をかける
            _scene.UpdateFocusCameraPosition(SingleModeScenarioCameraCharacterCorrectionPreset.DisplayType.Top, ModelController, UseMainViewFocusCameraScenarioPreset);
        }

        /// <summary>
        /// 背景キャラのオンオフ
        /// </summary>
        /// <param name="active"></param>
        public void ToggleBgModelList(bool active)
        {
            foreach (var keyValuePair in _bgModelListDic)
            {
                foreach (var model in keyValuePair.Value)
                {
                    model.SetVisible(active);
                }
            }
        }

        /// <summary>
        /// 指定DisplayTypeの背景キャラON/OFF
        /// </summary>
        private void ToggleBgModelList(bool active, BgDisplayType bgDisplayType)
        {
            var modelList = GetBgModelList(bgDisplayType);
            foreach (var model in modelList)
            {
                model.SetVisible(active);
            }
        }

        /// <summary>
        /// 背景キャラはTOP用とトレーニング用のどちらを表示したいか指定してON/OFF切り替え
        /// ONにしたいほうのタイプを指定すると、指定外のタイプはOFFになります
        /// </summary>
        public void ToggleBgModelByTopAndTraining(BgDisplayType bgDisplayType)
        {
            var activeTop = bgDisplayType == BgDisplayType.Top;
            // TOP背景キャラ
            ToggleBgModelList(activeTop, BgDisplayType.Top);
            // トレーニング背景キャラ(TOPをONにするとき、トレーニングはOFF)
            ToggleBgModelList(!activeTop, BgDisplayType.Training);
        }

        /// <summary>
        /// 育成TOPのキャラの適切な衣装IDを取得する
        /// </summary>
        private int GetPreferredDressId(WorkSingleModeData workData, int dressId)
        {
            // たづなさん表示時は固定値
            if (workData.HomeInfo.IsShowTazunaAtHomeTop())
            {
                return GameDefine.TAZUNA_DRESS_ID;
            }

            return SingleModeMainDressUtil.GetPreferredDressId(workData, dressId);
        }

        /// <summary>
        /// 育成TOPで必要な育成ウマ娘の衣装IDリスト
        /// </summary>
        public List<int> GetTopCharaModelDressIdList()
        {
            var result = new List<int>(8);
            var workData = WorkDataManager.Instance.SingleMode;
            var workCharaData = workData.Character;
            //通常服モデルは必ず追加する
            var raceDressId = GetPreferredDressId(workData, workCharaData.GetRaceDressId(true));
            result.Add(raceDressId);
            //ターンCSV指定の特殊服モデルのロード
            var topDressId = WorkSingleModeData.GetCurrentTopClothId();
            if (topDressId > 0)
            {
                //季節に合わせて衣装を変える
                topDressId = GetPreferredDressId(workData, topDressId);
                if (result.Contains(topDressId) == false) result.Add(topDressId);
            }
            return result;
        }

        /// <summary>
        /// トレーニングで必要な育成ウマ娘の衣装IDリスト
        /// </summary>
        public List<int> GetTrainingCharaModelDressIdList()
        {
            var result = new List<int>(8);
            var workData = WorkDataManager.Instance.SingleMode;
            var workCharaData = workData.Character;
            var rarityData = workCharaData.CardRarityData;
            // 各トレーニング用の衣装
            var masterSingleModeTraining = MasterDataManager.Instance.masterSingleModeTraining;

            var homeInfo = workData.HomeInfo;
            if (!homeInfo.TurnInfoListDic.TryGetValue(SingleModeDefine.CommandType.Training,
                out var trainingTurnInfoList))
            {
                return result;
            }

            // たづなが表示されるときはトレーニングは実行できないので、空のまま返す
            if (homeInfo.IsShowTazunaAtHomeTop())
            {
                return result;
            }

            var season = workData.GetSeason();
            var seasonSub = GallopUtil.GetBgSeasonSub(season);

            foreach (var trainingInfo in trainingTurnInfoList)
            {
                var commandId = trainingInfo.CommandId;
                int trainingLevel = workCharaData.GetTrainingLevel(commandId);
                var masterTraining = masterSingleModeTraining.GetWithCommandIdAndCommandLevel((int)commandId, trainingLevel);
                if (masterTraining == null)
                {
                    Debug.LogError("Not Found masterTraining! CommandId:" + commandId + " TrainingLevel:" + trainingLevel);
                    continue;
                }
                var dressId = SingleModeMainDressUtil.GetPreferredDressId(seasonSub, rarityData, masterTraining.DressId);
                if (result.Contains(dressId) == false) result.Add(dressId);
            }
            return result;
        }

        /// <summary>
        /// 指定したタイプの背景キャラリストを取得する
        /// </summary>
        protected List<SingleModeModelManager.Model> GetBgModelList(BgDisplayType bgDisplayType)
        {
            if (_bgModelListDic.TryGetValue(bgDisplayType, out var modelList))
            {
                return modelList;
            }
            // 無ければ空リスト登録して返す
            _bgModelListDic[bgDisplayType] = new List<SingleModeModelManager.Model>();
            return _bgModelListDic[bgDisplayType];
        }

        /// <summary>
        /// 背景キャラをタイプ別モデルリストに登録する
        /// </summary>
        protected void AddBgModelListDic(SingleModeModelManager.Model model, BgDisplayType bgDisplayType)
        {
            if (_bgModelListDic.TryGetValue(bgDisplayType, out var modelList))
            {
                // 既存キーのリストにまだなければ登録
                if (modelList.Contains(model) == false)
                {
                    modelList.Add(model);
                }
            }
            else
            {
                // 新規
                _bgModelListDic[bgDisplayType] = new List<SingleModeModelManager.Model>() { model };
            }
        }

        /// <summary>
        /// 背景キャラモデルの登録
        /// イメージエフェクト適用。描画順更新
        /// </summary>
        protected void EntryBgCharaModel(SingleModeModelManager.Model model, BgDisplayType bgDisplayType)
        {
            // 登録
            AddBgModelListDic(model, bgDisplayType);
            // イメージエフェクト適用
            _trainingController.AddModel(model.Controller);
            _trainingController.UpdateImageEffectCharacterColor();
            Gallop.ModelController.FrameUpdate(model.Controller);
            // 描画順
            UpdateBGCharacterRenderQueue(_scene.FocusCamera.GetCamera());
        }

        /// <summary>
        /// 育成キャラと一緒に表示されるキャラにもリアルタイムシャドウを利用する時
        /// 影を落とすためのメッシュを作成
        /// </summary>
        /// <returns></returns>
        protected GameObject CreateRealTimeShadowRecevier()
        {
            //リアルタイムシャドウを落とすメッシュを用意
            var isUseRealTimeShadow = _scene.IsUseRealtimeShadow && UseRealTimeShadow;

            GameObject realtimeShadowRecevier = null;
            if (isUseRealTimeShadow)
            {
                realtimeShadowRecevier = _scene.CreateRealtimeShadowRecevier();
            }

            return realtimeShadowRecevier;
        }

        /// <summary>
        /// 背景キャラモデルの描画順設定
        /// </summary>
        public void UpdateBGCharacterRenderQueue(Camera camera)
        {
            foreach (var keyValuePair in _bgModelListDic)
            {
                var depthOrderList = keyValuePair.Value.OrderBy(a => a.Controller.GetDepthOrder(camera)).ToList();
                for (var i = 0; i < depthOrderList.Count; i++)
                {
                    // orderが若いほど奥に描画される（育成キャラは17で手前に描画設定済み）
                    depthOrderList[i].Controller.UpdateGraphicSettings(i);
                }
            }
        }

        /// <summary>
        /// 衣装IDからモデルの衣装を変更する
        /// </summary>
        public void UpdateModelByDressId(int dressId, MasterCharaMotionSet.CharaMotionSet motionSetMaster)
        {
            var workData = WorkDataManager.Instance.SingleMode;

            //季節に合わせて衣装を変える
            dressId = GetPreferredDressId(workData, dressId);

            //トレーニング用モデルを探す
            if (!_trainingModelDic.TryGetValue(dressId, out var model))
            {
                // #135755 シナリオギミックでトレーニング配置が変わるケースなどで、モデルの更新が漏れるケースが考えられる
                // フェイルセーフとして、万が一目的のモデルが見つからなかった場合は再度SetupModel()を実行し、必要なモデルが生成されるか試みる
                SetupModel();

                if (!_trainingModelDic.TryGetValue(dressId, out model))
                {
                    // それでも見つからない場合は SetupModel() に不備がある
                    Debug.LogError("必要な3Dモデルが生成されていませんでした。SetupModel()のロジックを確認し、必要な3Dモデルが揃うようにしてください");
                    return;
                }
#if CYG_DEBUG
                else
                {
                    // 開発中はエラーとして検知して最適化を促す
                    Debug.LogError("必要な3Dモデルが生成されていませんでした。ターン中に必要な3Dモデルが切り替わる場合は、都度SetupModel()を明示的に呼ぶ必要があります");
                }
#endif
            }

            //モデルのアクティブを切り替えるとアニメーションが初期化されるので見た目だけ。どうしてもActiveを切りたい場合は、Animator.keepAnimatorControllerStateOnDisableをONにする
            foreach (var trainingModel in _trainingModelDic.Values)
            {
                trainingModel.SetVisibleWithCySpring(false);
            }

            model.SetVisibleWithCySpring(true);
            model.ShoesOffsetOff();
            _scene.UpdateFocusCameraPosition(SingleModeScenarioCameraCharacterCorrectionPreset.DisplayType.Training, model, UseTrainingViewFocusCameraScenarioPreset);

            if (motionSetMaster.BodyMotionPlayType == (int)SimpleModelController.BodyMotionPlayType.Single)
            {
                // 単発モーションの時は待機モーションへつなぐ
                // #64456 パワーから根性につなぐとブレンド中に腕が貫通するのでブレンド無しにする
                // #96746 スピードからパワーにつなぐと前モーションのEndが挟まってしまうのでSkipEndフラグを指定する
                model.PlayMotion(motionSetMaster, model.IdleMotionSetMaster, bodyBlendTime: 0, isForce: true, isSkipEnd: true);
            }
            else
            {
                // それ以外であればループ
                model.PlayMotion(motionSetMaster, startType: SimpleModelController.MotionStartType.Loop, bodyBlendTime: 0, isForce: true);// ループ部分から補間なしで開始
            }

            // Reset+ReserveWarmingUpによって揺れものが暴れるのを抑制
            // 賢さトレーニングの場合Loopモーションが斜めに向いた状態になるので
            // 回転が終わってからWarmingUpがかかるようにする
            // #65015　即座にモーション反映する。CySpring暖気は遅延なし
            model.PlayableAnimator.UpdateMotion(0);
            model.PlayableAnimator.Evaluate();
            model.ResetCyspring();
            model.ReserveWarmingUpCySpring();

            SetMainModelController(model);
        }

        /// <summary>
        /// モーションや揺れ物をリセット
        /// </summary>
        public void ResetMotionAndCySpring()
        {
            ModelController.PlayMotion(ModelController.IdleMotionSetMaster, startType: SimpleModelController.MotionStartType.Loop, bodyBlendTime: 0f, isForce: true);
            ModelController.ResetCyspring();
            ModelController.ReserveWarmingUpCySpring();
        }

        /// <summary>
        /// キャラの吹き出し表示
        /// </summary>
        /// <param name="character"></param>
        public void ShowCharaMessage()
        {
            // チュートリアル中はキャラがしゃべってしまうとたづなさんのメッセージボイスと被ってしまうのでボイスは絶対に再生しない
            if (TutorialSingleMode.IsTutorial)
            {
                return;
            }

            // 制限がかかっている時は出さない
            if (IsRestrictCharaMessage)
            {
                return;
            }

            _baseView.CharaMessage.SetEnable(true);
            if (!_baseView.CharaMessage.IsPlaying)
            {
                _baseView.CharaMessage.PlayIdle(useSmoothFaceBlend: true);
            }

            _baseView.CharaMessage.SetActiveWithCheck(_baseView.CharaMessage.IsPlaying);
        }

        /// <summary>
        /// キャラの吹き出し非表示
        /// </summary>
        public void HideCharaMessage()
        {
            _baseView.CharaMessage.Stop();
            _baseView.CharaMessage.SetEnable(false);
        }


        /// <summary>
        /// 背景に配置しているキャラクターを破棄
        /// </summary>
        public void ClearBgModel()
        {
            foreach (var keyValuePair in _bgModelListDic)
            {
                var modelList = keyValuePair.Value;
                foreach (var model in modelList)
                {
                    _trainingController?.RemoveModel(model.Controller);

                    _scene.DestroyModel(model);
                }
                modelList.Clear();
            }
            _bgModelListDic.Clear();

            OnClearBgModel();
        }

        public void EndView()
        {
            SetMainModelVisible(false);
            ToggleBgModelList(false);
            if (ModelController != null)
            {
                ModelController.SetDefaultScale();
            }
        }

        public void FinalizeView()
        {
            if (_lightController != null)
            {
                _lightController.Destroy();
                _scene.FocusCamera.GetCamera().cullingMask ^= GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3D);
            }

            if (_defaultCameraData != null)
            {
                var camera = _scene.FocusCamera.GetCamera();
                camera.fieldOfView = _defaultCameraData.FocusCameraFOV;
                camera.transform.localPosition = _defaultCameraData.FocusCameraPos;
            }
            // 背景キャラを破棄
            ClearBgModel();

            //148058 148079 リアルタイムシャドウを利用している場合、ビュー切り替え時一緒に削除
            if (UseRealTimeShadow && _scene != null && _scene.IsUseRealtimeShadow)
            {
                _scene.DestroyRealtimeShadowParts();
            }
        }


#if CYG_DEBUG && UNITY_EDITOR

        public void ClearTrainingModel() =>
            _trainingModelDic.Clear();

        private ModelController _mainCharaModel;
        public void DebugShowGMMainChara(int charaId, int dressId)
        {
            if (_mainCharaModel == null)
            {
                _mainCharaModel = ModelController;
                _mainCharaModel.SetActiveWithCheck(false);
            }
            else
            {
                _scene.DestroyModel(ModelController.GetCardId(), ModelController.GetDressId());
            }
            ModelController = _scene.CreateModelByCharaId(charaId, dressId);
            SetMainModelController(ModelController);
            _trainingController.UpdateImageEffectCharacterColor();
        }

#endif

    }
}

