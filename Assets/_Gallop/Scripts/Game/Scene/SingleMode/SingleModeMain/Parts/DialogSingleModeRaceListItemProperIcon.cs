using Cute.UI;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// シングル用　レース一覧の項目
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeRaceListItemProperIcon : MonoBehaviour
    {
        [SerializeField]
        private ImageCommon _image = null;

        [SerializeField]
        private TextCommon _text = null;

        [SerializeField]
        private ImageCommon _star = null;
        [SerializeField]
        private ImageCommon _starEffect = null;

        [SerializeField]
        private ImageCommon _lightEffect = null;

        /// <summary>
        /// 準備
        /// </summary>
        /// <param name="caption"></param>
        /// <param name="isEnable"></param>
        /// <param name="grade"></param>
        public void Setup( string caption, bool isEnable, RaceDefine.ProperGrade grade , RaceDefine.ProperGrade scenarioBuffGrade)
        {
            _text.text = caption;
            
            // 非適正字の文字色変更
            if (!isEnable)
            {
                _text.FontColor = FontColorType.ProperDisable;
                _text.UpdateColor();
            }

            // B以上で強調
            bool isStar = (grade >= RaceDefine.ProperGrade.B || scenarioBuffGrade >= RaceDefine.ProperGrade.B) && isEnable;
            _star.SetActiveWithCheck(isStar);
            _lightEffect.SetActiveWithCheck(isStar);
            
            // シナリオギミックによるバフで基礎能力より上昇している場合
            if (grade < scenarioBuffGrade)
            {
                SetStartSprite(AtlasUtil.GetSpriteByName(TargetAtlasType.SingleModeScenarioArc, AtlasSpritePath.SingleModeScenarioArc.ICO_DISTANCEFITMATCH_ARC_00));
                _image.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.SingleModeScenarioArc, AtlasSpritePath.SingleModeScenarioArc.FRM_RACELIST_INFOLABEL_ARC_00); 
            }
            else
            {
                SetStartSprite(AtlasUtil.GetSpriteByName(TargetAtlasType.Single, AtlasSpritePath.SingleMode.ICO_DISTANCEFITMATCH_00));
                _image.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Single, isStar ? AtlasSpritePath.SingleMode.FRAME_RACE_LIST_STRONG : AtlasSpritePath.SingleMode.FRAME_RACE_LIST_NORMAL);
            }

            // 星スプライト設定
            void SetStartSprite(Sprite sprite)
            {
                _star.sprite = sprite;
                _starEffect.sprite = _star.sprite;
                _star.SetNativeSize();
                _starEffect.SetNativeSize();
            }
        }

    }
}
