using UnityEngine;
using DG.Tweening;
using AnimateToUnity;

namespace Gallop
{
    /// <summary>
    /// 育成TOPの追加シナリオ告知A2U
    /// (アオハル杯まであとNターン）
    /// </summary>
    public sealed class PartsSingleModeMainViewScenarioNotice : MonoBehaviour
    {
        [SerializeField]private ImageCommon _noticeBg;
        [SerializeField]private Canvas _noticeBgCanvas;

        /// <summary>
        /// 開催予定表示
        /// </summary>
        public static void Play(Transform parent, string path, int turn, System.Action onComplete, System.Action<FlashPlayer> onPlayExtend = null, System.Action onPlayOut = null)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_SINGLE_MODE_MAIN_SCENARIO_NOTICE_PATH);
            var obj = Instantiate(prefab, parent);
            var component = obj.GetComponent<PartsSingleModeMainViewScenarioNotice>();
            component.PlayImpl(path, turn, onComplete, onPlayExtend, onPlayOut);
        }
        private void PlayImpl(string path, int turn, System.Action onComplete, System.Action<FlashPlayer> onPlayExtend, System.Action onPlayOut = null)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var currentTurn = workSingle.GetCurrentTurn();
            var player = FlashLoader.LoadOnView(path,_noticeBg.transform);

            player.SortOffset = _noticeBgCanvas.sortingOrder+1;
            player.Init();
            player.Play(GameDefine.A2U_IN_LABEL);
            if (turn > 0)
            {
                // あとNターン（フリー編など一部のシナリオでは不要）
                player.GetImageNumber("OBJ_mc_num_turn00").SetValue(turn - currentTurn);
            }
            onPlayExtend?.Invoke(player);
            AudioManager.Instance.PlaySe(AudioId.SFX_ADDON01_NOTICE);
            DOTween.ToAlpha(()=> _noticeBg.color,color => _noticeBg.color = color, 0.5f,0.13f).SetEase(Ease.OutCubic);
            player.SetActionCallBack(GameDefine.A2U_OUT_LABEL,()=>
            {
                DOTween.ToAlpha(()=> _noticeBg.color,color => _noticeBg.color = color, 0f,0.13f).SetEase(Ease.OutCubic);
                onPlayOut?.Invoke();
            },AnMotionActionTypes.Start);
            player.SetActionCallBack(GameDefine.A2U_END_LABEL,() => 
            {
                
                Destroy(this.gameObject);// 自分破棄
                ResourceManager.UnloadFromView(path);//リソース開放
                onComplete?.Invoke();
            },AnMotionActionTypes.End);
            // チェック済み
            SingleModeChangeViewManager.Instance.LastCheckScenarioNotice = workSingle.GetCurrentYearMonthAndHalf();        
        }        
    }
}

