using DG.Tweening;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ISingleModeScenarioMainViewAdditivePartsBase内の要素のイリハケ制御を楽におこなうためのコンポーネント
    /// </summary>
    [DisallowMultipleComponent]
    [RequireComponent(typeof(CanvasGroup))]
    public class PartsSingleModeMainViewInOutAnimationRoot : MonoBehaviour
    {
        [SerializeField] private CanvasGroup _canvasGroup;

        private bool _hasInitialized;
        
        private Vector3 _defaultPosition;

        private Sequence _sequence;

        private void Awake()
        {
            Initialize();
        }

        /// <summary>
        /// 初期化処理
        /// </summary>
        /// <remarks>生成時に元のGameObjectが非アクティブだとAwakeが呼ばれないので、各メソッドの頭で初期化できるようなつくりにしておく</remarks>
        private void Initialize()
        {
            if (_hasInitialized) return;
            _hasInitialized = true;
            
            _defaultPosition = transform.localPosition;

            if (_canvasGroup == null)
            {
                _canvasGroup = GetComponent<CanvasGroup>();
#if CYG_DEBUG
                Debug.LogWarning("PartsSingleModeMainViewInOutAnimationRoot: GetComponentコスト削減のため、SerializeFieldに参照をアタッチしてください");
#endif
            }
        }

        /// <summary>
        /// 即座に表示切替
        /// </summary>
        /// <remarks><see cref="ISingleModeScenarioMainViewAdditivePartsBase.SetActive"/>から呼ばれることを想定</remarks>
        public void SetActiveImmediate(bool isActive)
        {
            if (!_hasInitialized) Initialize();
            
            if (isActive)
            {
                gameObject.SetActiveWithCheck(true);
                gameObject.transform.localPosition = _defaultPosition;
                _canvasGroup.alpha = 1;
            }
            else
            {
                gameObject.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// イリアニメのシーケンスを作成する
        /// </summary>
        public Sequence CreatePlayInSequence(TweenAnimation.PresetType presetType)
        {
            if (!_hasInitialized) Initialize();

            _sequence?.Kill(true);

            gameObject.SetActiveWithCheck(true);
            gameObject.transform.localPosition = _defaultPosition;

            _sequence = TweenAnimationBuilder.CreateSequence(gameObject, presetType);

            return _sequence;
        }

        /// <summary>
        /// ハケアニメのシーケンスを作成する
        /// </summary>
        public Sequence CreatePlayOutSequence(TweenAnimation.PresetType presetType)
        {
            if (!_hasInitialized) Initialize();

            _sequence?.Kill(true);
            _sequence = TweenAnimationBuilder.CreateSequence(gameObject, presetType);
            _sequence.OnComplete(() => gameObject.SetActiveWithCheck(false));

            return _sequence;
        }
    }
}
