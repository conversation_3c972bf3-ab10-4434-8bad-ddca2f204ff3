using DG.Tweening;
using UnityEngine;
using System.Collections.Generic;
using AnimateToUnity;

namespace Gallop
{
    /// <summary>
    /// 育成TOP:タズナヒント
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeMainViewTazunaHint : MonoBehaviour
    {
        [SerializeField]
        private ButtonCommon _button;
        [SerializeField]
        private Transform _flashParent;
        [SerializeField]
        private Transform _badgeParent;
        
        private FlashPlayer _flashPlayer;
        private FlashPlayer _badge;
        private bool _isIn;
        private MasterSingleModeMessage.SingleModeMessage _tazunaMessage;
        
        // 1行の最大文字数
        private const int MESSAGE_LINE_MAX = 18;

        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_HINT_FLASH_PATH);
        }
        
        /// <summary>
        /// 表示
        /// </summary>
        public void Setup(MasterSingleModeMessage.SingleModeMessage tazunaMessage)
        {
            _tazunaMessage = tazunaMessage;
            if (_flashPlayer == null)
            {
                _flashPlayer = FlashLoader.LoadOnView(ResourcePath.SINGLE_MODE_HINT_FLASH_PATH, _flashParent);
                _flashPlayer.Init();
            }
            
            var isShow = SaveDataManager.Instance.SaveLoader.SingleModeHintShow;
            if (isShow) PlayIn();
            else Show();
            
            SetWarning(tazunaMessage.Emergent == 1);
            
            _button.SetOnClick(OnClick);
        }

        private void SetWarning(bool isWarning)
        {
            var motionName = isWarning ? "warning" : "normal";
            _flashPlayer.GetMotion("MOT_mc_question00")?.SetMotionPlay(motionName);
            _flashPlayer.GetMotion("MOT_mc_chara00")?.SetMotionPlay(motionName);
            _flashPlayer.GetMotion("MOT_mc_frame00")?.SetMotionPlay(motionName);

            if (isWarning)
            {
                if (_badge == null)
                {
                    _badge = UIUtil.CreateNotifyBadgeIconFlash(_badgeParent, UIManager.CanvasSoringOrder.Main + 10);// タズナFlashより上
                }
            }
            _badge.SetActiveWithCheck(isWarning);
        }

        private void SetMessage(string message)
        {
            bool isDoubleLine = message.Length > MESSAGE_LINE_MAX; // 2行にするか
            var motionName = isDoubleLine ? "double" : "single";
            var motionObj = _flashPlayer.GetMotion("MOT_mc_txt_hint00");
            motionObj.SetResetModeType(AnMotion.ResetModeTypes.None);
            motionObj.SetMotionPlay(motionName);

            var textName = isDoubleLine ? "TXT_text_hint_double00" : "TXT_txt_hint_single00";
            var textObj = _flashPlayer.GetText(textName);
            textObj.SetText(message);
            textObj.SetTextAnchor(TextAnchor.MiddleLeft);
            textObj.SetTextAlignment(TextAlignment.Left);
        }

        private void OnClick()
        {
            var saveData = SaveDataManager.Instance.SaveLoader;
            saveData.SingleModeHintShow = !saveData.SingleModeHintShow;
            saveData.Save();
            if (saveData.SingleModeHintShow)
            {
                PlayIn();
            }
            else
            {
                PlayOut();
            }
        }

        private void Show()
        {
            _flashPlayer.Play("in");
            _flashPlayer.Motion.SetResetModeType(AnMotion.ResetModeTypes.None);
        }
        
        private void PlayIn()
        {
            var message = TextUtil.GetMasterText(MasterString.Category.SingleModeTazunaComment, _tazunaMessage.Id);
            SetMessage(message);
            
            if (_isIn) return;
            _flashPlayer.Play("in_hint");
            _flashPlayer.Motion.SetResetModeType(AnMotion.ResetModeTypes.None);
            _isIn = true;
        }

        private void PlayOut()
        {
            if (!_isIn) return;
            _flashPlayer.Play("out_hint");
            _isIn = false;
        }
    }
}
