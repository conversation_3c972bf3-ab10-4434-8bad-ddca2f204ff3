using Gallop.Tutorial;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成用タイトルヘッダー
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeHeaderTitle : PartsCommonHeaderTitle
    {
        public class SingleModeTitlePlayer : TitlePlayer
        {
            protected override string A2UPath => ResourcePath.SINGLE_MODE_MAIN_VIEW_HEADER_TITLE_PATH;

            // フラッシュのソートオーダー
            protected override int FLASH_SORT_OFFSET => 500;

            protected override string TITLE_TEXT => "TXT_txt_singlemode_title00";
            protected override string HEADER_BTN_MOT_INFO => "MOT_mc_header_btn_info00";
            protected override string HEADER_BTN_INFO_OBJ => "OBJ_loc_btn_info00";


            public SingleModeTitlePlayer(ResourceManager.ResourceHash resourceHash, Transform parent, Transform infoButtonParent, ButtonCommon infoButton)
            {
                _flashPlayer = FlashLoader.LoadOnHash(A2UPath, parent, hash: resourceHash);
                _flashPlayer.SortOffset = FLASH_SORT_OFFSET;
                _flashPlayer.Init();
                _text = _flashPlayer.GetText(TITLE_TEXT);
                _text.SetTextAnchor(TextAnchor.LowerLeft);

                var buttonObj = _flashPlayer.GetObj(HEADER_BTN_INFO_OBJ);
                _infoButton = infoButton;
                infoButtonParent.SetParent(buttonObj.Transform);
                infoButtonParent.localPosition = Math.VECTOR3_ZERO;

                _buttonMotion = _flashPlayer.GetMotion(HEADER_BTN_MOT_INFO);

                var color = _infoButton.GetInteractableMulColor(true);
                color.a = 0f;
                _infoButton.SetButtonColors(color);
                _infoButton.SetEnable(false);

                if (TutorialManager.IsTutorialExecuting())
                {
                    // infoボタン : チュートリアル中は表示ごと消し、ボタンとしても動作しないようにする
                    _infoButton.targetGraphic.enabled = false;
                    _infoButton.SetInteractable(false);
                }
            }

            protected override void SetText(string text)
            {
                const string MOT_UTX_CALENDAR_COLOR00 = "MOT_utx_calendar_color00";

                _text?.SetText(text);
                _text?.SetTextAnchor(TextAnchor.MiddleLeft);

                var inLabel = GetInLabel(text);
                var motionCalendarColor = _flashPlayer.GetMotion(MOT_UTX_CALENDAR_COLOR00);
                if (motionCalendarColor != null)
                {
                    motionCalendarColor.SetMotionPlay(inLabel);
                }
            }

            protected override void PlayInfoButton(string text)
            {
                const string MOT_MC_HEADER_BTN_INFO_POS00 = "MOT_mc_header_btn_info_pos00";

                var inLabel = GetInLabel(text);
                var motionHeaderBtnInfoPos = _flashPlayer.GetMotion(MOT_MC_HEADER_BTN_INFO_POS00);
                if (motionHeaderBtnInfoPos != null)
                {
                    motionHeaderBtnInfoPos.SetMotionPlay(inLabel);
                }
            }

            private string GetInLabel(string text)
            {
                const int TEXT_LENGTH_LABEL_SIZE01 = 8;

                switch (text.Length)
                {
                    case var length when length >= TEXT_LENGTH_LABEL_SIZE01:
                        return TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, 2);
                    default:
                        return TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, 1);
                }
            }


        }

        // iボタンのソートオーダー
        protected override int BUTTON_SORT_OFFSET => 510;

        public override void CreateFlashPlayer(ResourceManager.ResourceHash resourceHash)
        {
            _currentPlayer = new SingleModeTitlePlayer(resourceHash, _titleRoot.gameObject.transform, _infoButtonCanvas.transform, _infoButton);
            _nextPlayer = new SingleModeTitlePlayer(resourceHash, _titleRoot.gameObject.transform, _infoButtonCanvas2.transform, _infoButton2);
        }



    }
}
