using System.Linq;
using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成トレーニング併せウマアイコン：分身アイコン表現付き汎用クラス
    /// お料理編から、分身表記が必用なら使用する
    /// </summary>
    public class SingleModeMainViewTrainingHorseIcon01A2U : SingleModeMainViewTrainingHorseIconA2UBase
    {
        protected override string PartnerA2UPath => ResourcePath.SINGLE_MODE_PARTNER_FLASH_PATH_01;
        
        private AnMotion _subCommandCharaMotion;
        private const string MOT_ICO_ADDITION = "MOT_mc_dum_ico_addition00";
        private const string PLN_ICO_ADDITION = "PLN_dum_ico_addition00";
        
        /// <summary>
        /// バッジ表示：シナリオ拡張
        /// </summary>
        protected override void SetBadgeBalloon(WorkSingleModeData.TrainingHorse trainingHorse)
        {
            base.SetBadgeBalloon(trainingHorse);
            SetSubCommandChara(trainingHorse);
        }

        /// <summary>
        /// 分身アイコン表示設定
        /// </summary>
        protected virtual void SetSubCommandChara(WorkSingleModeData.TrainingHorse trainingHorse)
        {
            SetupSubCommandCharaMotion();
            var turnInfo = WorkDataManager.Instance.SingleMode.HomeInfo.GetTrainingTurnInfo(trainingHorse.CommandId);
            if (turnInfo == null || turnInfo.SubCommandPartnerArray.IsNullOrEmpty())
            {
                // OFF
                PlaySubCommandCharaMotion(false);
                return;
            };
            
            var isSubChara = turnInfo.SubCommandPartnerArray.Any(positionId => positionId == trainingHorse.PositionId);//分身か
            PlaySubCommandCharaMotion(isSubChara);
            SetSubCommandCharaSprite(UIManager.Instance.LoadAtlas(TargetAtlasType.Single2).GetSprite(AtlasSpritePath.Single2.ICO_SUPPORT_DOUBLE_00));
        }

        protected void SetupSubCommandCharaMotion()
        {
            if (_subCommandCharaMotion != null) return;
            _subCommandCharaMotion = _flashPlayer.GetMotion(MOT_ICO_ADDITION);
        }
        protected void SetSubCommandCharaSprite(Sprite sprite)
        {
            _flashPlayer.SetSprite(PLN_ICO_ADDITION, sprite);
        }
        protected void PlaySubCommandCharaMotion(bool isActive)
        {
            var label = isActive ? GameDefine.A2U_IN_LABEL : GameDefine.A2U_IN00_LABEL;//ON・OFFラベル
            _subCommandCharaMotion.SetMotionPlay(label);
        }
    }
}
