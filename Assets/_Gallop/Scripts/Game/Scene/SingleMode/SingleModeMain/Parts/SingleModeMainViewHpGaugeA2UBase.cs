using AnimateToUnity;
using AnimateToUnity.Utility;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 体力ゲージのA2U基底クラス
    /// </summary>
    public class SingleModeMainViewHpGaugeA2UBase
    {
        /// <summary>
        /// シナリオ別生成
        /// </summary>
        public static SingleModeMainViewHpGaugeA2UBase Create()
        {
            // シナリオ別モデル生成
            return WorkDataManager.Instance.SingleMode.GetScenarioId() switch
            {
                SingleModeDefine.ScenarioId.Mecha => new SingleModeMainViewHpGaugeA2UScenarioMecha(),
                _ => new SingleModeMainViewHpGaugeA2UBase()
            };
        }

        protected FlashPlayer _hpGauge;
        public FlashPlayer HPGauge => _hpGauge;

        protected virtual string A2UPath => ResourcePath.SINGLE_MODE_HP_GAUGE_FLASH_PATH;

        protected ResourceManager.ResourceHash Hash { get; private set; }
        
        #region 基本処理
        
        /// <summary>
        /// DL登録
        /// </summary>
        public virtual void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(A2UPath);
        }
        
        /// <summary>
        /// A2U生成準備
        /// </summary>
        public void CreateA2U(Transform parent, ResourceManager.ResourceHash resourceHash)
        {
            _hpGauge = FlashLoader.LoadOnHash(A2UPath, parent, hash: resourceHash);
            Hash = resourceHash;
        }
        
        /// <summary>
        /// トレーニング選択毎にシナリオギミックの演出を呼び出す
        /// </summary>
        public virtual void PlayScenarioGimmick(bool isPlayGimmick) { }
        
        #endregion
    }
}
