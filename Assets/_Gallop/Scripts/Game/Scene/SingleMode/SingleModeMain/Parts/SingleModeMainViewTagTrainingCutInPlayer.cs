using System.Collections;
using System.Collections.Generic;
using System.Linq;
using AnimateToUnity;
using DG.Tweening;
using UnityEngine;

namespace Gallop
{
    using static StaticVariableDefine.SingleMode.SingleModeMainViewTagTrainingCutInPlayer;

    /// <summary>
    /// 友情タッグトレーニングのカットイン再生処理をコード分離
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeMainViewTagTrainingCutInPlayer : MonoBehaviour
    {
        #region 定数、変数
        
        private const float GRADATION_DEFAULT_ALPHA = 80f / 255f;
        private const int CUT_IN_MIN = 1;

        private const float SUPER_HIGH_SPEED_MOTION_SPEED = 2f;
        private const float DEFAULT_MOTION_SPEED = 1f;

        public const int CUT_IN_MAX = 5;
        /// <summary>
        /// タッグトレーニングカットインFlashのSortOffset
        /// </summary>
        public const int TAG_TRAINING_CUT_IN_FLASH_SORT_OFFSET = 6000;

        [SerializeField]
        private RectTransform _root;
        
        [Header("Top")]
        [SerializeField]
        private ImageCommon _topGradation;
        private Animator _topLineAnimator;
        
        [Header("Bottom")]
        [SerializeField]
        private ImageCommon _bottomGradation;
        private Animator _bottomLineAnimator;

        private FlashPlayer _tagCutInFlashPlayer;
        private FlashPlayer _tagCutInTextFlashPlayer;
        private GameObject _tagCutInEffect;
        private GameObject _tagCutInRootObject;

        private FlashPlayer _buttonEffectPlayer;

        private bool UseSuperHighSpeed => SaveDataManager.Instance.SaveLoader.IsEnableSuperHighSpeedSkip && StoryManager.IsHighSpeedMode;
        private float MotionSpeed => UseSuperHighSpeed ? SUPER_HIGH_SPEED_MOTION_SPEED : DEFAULT_MOTION_SPEED;
        
        #endregion
        
        /// <summary>
        /// ダウンロード登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload( DownloadPathRegister register )
        {
            //カットイン本体
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_FLASH_PATH);
            //開始文字
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_START_TEXT_FLASH_PATH);
            //成功文字
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_SUCCESS_TEXT_FLASH_PATH);
            //ボタン押下
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_BUTTON_FLASH_PATH);
            //流線エフェクト
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_LINE_EFFECT_PATH);
            
            //開始エフェクト
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_START_EFFECT_PATH);
            //成功エフェクト
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_TAG_TRAINING_SUCCESS_EFFECT_PATH);
        }
        
#if CYG_DEBUG
        /// <summary>
        /// Debug用のDownload処理
        /// </summary>
        /// <returns></returns>
        public static IEnumerator DownloadForDebugAsync()
        {
            //Flash素材のDL カットインに必要なFlashのみDL
            yield return DownloadForDebugAsync(ResourcePath.SINGLE_MODE_TAG_TRAINING_FLASH_PATH);
            yield return DownloadForDebugAsync(ResourcePath.SINGLE_MODE_TAG_TRAINING_START_TEXT_FLASH_PATH);
            yield return DownloadForDebugAsync(ResourcePath.SINGLE_MODE_TAG_TRAINING_START_EFFECT_PATH);
        }
        
        private static IEnumerator DownloadForDebugAsync( string path )
        {
            //タッグトレーニング演出
            var isComplete = false;
            DownloadManager.Instance.Download(path, () => isComplete = true);
            yield return new WaitUntil(() => isComplete );
        }
#endif

        public static bool IsValidTag(List<MasterSupportCardData.SupportCardData> partnerList)
        {
            var partnerCount = partnerList.Count;
            //想定外の値ならすぐ抜ける
            if (partnerCount < CUT_IN_MIN || CUT_IN_MAX < partnerCount)
            {
                return false;
            }
            
            return true;
        }

        #region カットイン
        
        /// <summary>
        /// タッグトレーニングカットインIN
        /// </summary>
        public void PlayCutIn(List<MasterSupportCardData.SupportCardData> partnerList, System.Action onInEnd )
        {
            //想定外の値ならすぐ抜ける
            if (!IsValidTag(partnerList))
            {
                onInEnd?.Invoke();
            }

            // ボイス再生
            var voiceSupportCard = partnerList[0];
            var voiceCharaId = voiceSupportCard.CharaId;
            // グループサポカの場合所属キャラ全員から抽選する(ただしウマ娘でないキャラ除く）
            var charaIdList = voiceSupportCard.GetCharaIdList().Where(charaId => MasterDataManager.Instance.masterCharaData.Get(charaId).CharaCategory == 0).ToList();
            if (charaIdList.Count > 0)
            {
                voiceCharaId = charaIdList[Random.Range(0, charaIdList.Count)];//ランダム抽選
            }

            
            AudioManager.Instance.PlaySystemVoice_SingleModeTrainingFriendly(voiceCharaId);
            
            _tagCutInRootObject = new GameObject("TagCutInContentsRoot", typeof(RectTransform));
            _tagCutInRootObject.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.LayerUI);
            var rootRectTransform = _tagCutInRootObject.GetComponent<RectTransform>();
            rootRectTransform.SetParent(UIManager.MainOverlayCanvas.transform);
            UIManager.Instance.AdjustContentsRootRect(rootRectTransform);
            
            var partnerCount = partnerList.Count;
            
            // SE再生
            var audioId = AudioId.SFX_TRAINING_TAG_CUTIN_01;
            switch (partnerCount)
            {
                case 2: audioId = AudioId.SFX_TRAINING_TAG_CUTIN_02; break;
                case 3: audioId = AudioId.SFX_TRAINING_TAG_CUTIN_03; break;
                case 4: audioId = AudioId.SFX_TRAINING_TAG_CUTIN_04; break;
                case 5: audioId = AudioId.SFX_TRAINING_TAG_CUTIN_05; break;
            }
            AudioManager.Instance.PlaySe(audioId);
            
            _tagCutInFlashPlayer = FlashLoader.LoadOnView(
                ResourcePath.SINGLE_MODE_TAG_TRAINING_FLASH_PATH,
                UIManager.MainOverlayCanvas.transform,
                $"MOT_mc_tagtraining_cut{partnerCount}");
            _tagCutInFlashPlayer.SortOffset = TAG_TRAINING_CUT_IN_FLASH_SORT_OFFSET;
            
            //画像の貼り付け
            
            //画像貼り付け先のPlane名
            const string planeName = "PLN_dum_image_plane";
            
            for ( int i = 0 ; i < partnerCount ; ++i )
            {
                var partnerId = partnerList[i].Id;
                var texture = ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetSupportCardTexTexturePath(partnerId));

                var objName = $"OBJ_dum_tag{partnerCount}_{i + 1}";
                var parentObj = _tagCutInFlashPlayer.GetObj(objName);
                _tagCutInFlashPlayer.SetTexture(planeName, texture, rootGameObject:parentObj.GameObject);
                var plane = _tagCutInFlashPlayer.GetPlane(planeName,rootGameObject:parentObj.GameObject);

                var posList = MasterDataManager.Instance.masterSingleModeTagCardPos.GetListWithSupportCardIdOrderByIdAsc(partnerId);
                var findPosition = posList.Find( p => p.Pattern == partnerCount && p.PosIndex == i + 1 );
                if (findPosition != null)
                {
                    var localScale = plane.Transform.localScale;
                    localScale.x = Math.MasterInt2Float(findPosition.ScaleX);
                    localScale.y = Math.MasterInt2Float(findPosition.ScaleY);
                    plane.Transform.localScale = localScale;
                    
                    var localPos = plane.Transform.localPosition;
                    localPos.x = Math.MasterInt2Float(findPosition.PosX);
                    localPos.y = Math.MasterInt2Float(findPosition.PosY);
                    plane.Transform.localPosition = localPos;

                    plane.Transform.localRotation = Quaternion.Euler(0f, 0f, Math.MasterInt2Float(findPosition.RotZ));
                }
            }
            
            //「タッグトレーニング発生！」文字
            _tagCutInTextFlashPlayer = FlashLoader.LoadOnView(
                ResourcePath.SINGLE_MODE_TAG_TRAINING_START_TEXT_FLASH_PATH,
                rootRectTransform);
            _tagCutInTextFlashPlayer.SortOffset = TAG_TRAINING_CUT_IN_FLASH_SORT_OFFSET + 100;
            var textRectTransform = _tagCutInTextFlashPlayer.gameObject.AddComponent<RectTransform>();
            textRectTransform.anchorMin = new Vector2(0.5f,0f);
            textRectTransform.anchorMax = new Vector2(0.5f,0f);
            textRectTransform.anchoredPosition = new Vector2(0f,150f);

            //文字に付随するエフェクト
            var loadEffectObj = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_TAG_TRAINING_START_EFFECT_PATH);
            _tagCutInEffect = Instantiate(loadEffectObj,textRectTransform);
            foreach (var particleRenderer in _tagCutInEffect.GetComponentsInChildren<ParticleSystemRenderer>())
            {
                particleRenderer.sortingOrder += TAG_TRAINING_CUT_IN_FLASH_SORT_OFFSET;
            }
            //まずは消しておく
            _tagCutInEffect.gameObject.SetActive(false);
            //eff_inでエフェクト表示
            var textMotion = _tagCutInTextFlashPlayer.GetMotion("MOT_mc_start_friend_training_text");
            textMotion.SetAction("eff_in",
                () =>
                {
                    _tagCutInEffect.gameObject.SetActive(true);
                },
                AnMotionActionTypes.Start);
            
            _tagCutInFlashPlayer.Motion.SetMotionSpeed(MotionSpeed,true);
            _tagCutInTextFlashPlayer.Motion.SetMotionSpeed(MotionSpeed,true);
            _tagCutInFlashPlayer.Play("in", () =>
            {
                onInEnd?.Invoke();
            });

            var delayTime = START_TEXT_DELAY_TIME_ARRAY[partnerCount-1] / MotionSpeed;
            //Text演出を少し遅れて再生させる（DelayTimeは人数によって異なる）
            DOVirtual.DelayedCall( delayTime, () => _tagCutInTextFlashPlayer.Play("in") );
        }

        /// <summary>
        /// カットインOUT
        /// </summary>
        public void PlayCutInOut(System.Action onEnd = null)
        {
            _tagCutInFlashPlayer.Play("out", () =>
            {
                Destroy(_tagCutInFlashPlayer.gameObject);
                Destroy(_tagCutInTextFlashPlayer.gameObject);
                Destroy(_tagCutInRootObject);
                Destroy(_tagCutInEffect);
                _tagCutInFlashPlayer = null;
                _tagCutInTextFlashPlayer = null;
                _tagCutInRootObject = null;
                _tagCutInEffect = null;
                onEnd?.Invoke();
            });
            _tagCutInTextFlashPlayer.Play("out");
            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_TAG_TRANSISSION);
        }
        
        #endregion

        #region トレーニング決定時のボタン押しエフェクト
        
        /// <summary>
        /// ボタン押下時のエフェクトFlashを再生する
        /// </summary>
        /// <param name="parent"></param>
        /// <param name="onComplete"></param>
        public void PlayButtonEffect( Transform parent,
            Vector3 position,
            System.Action onComplete )
        {
            var path = ResourcePath.SINGLE_MODE_TAG_TRAINING_BUTTON_FLASH_PATH;
            _buttonEffectPlayer = FlashLoader.LoadOnHash(path, parent, hash: ResourceManager.ResourceHash.SingleModeTrainingResource);
            _buttonEffectPlayer.SetActionCallBack("in_end", () =>
            {
                onComplete?.Invoke();
            }, AnMotionActionTypes.End);
            _buttonEffectPlayer.transform.position = position;
            _buttonEffectPlayer.Init();
            _buttonEffectPlayer.SortOffset = (int) UIManager.CanvasSoringOrder.MainOverlay; // UIより上
            _buttonEffectPlayer.Play("in");

            _buttonEffectPlayer.Motion.SetMotionSpeed(MotionSpeed,true);
        }

        /// <summary>
        /// ボタンエフェクトFlash破棄
        /// </summary>
        public void DestroyButtonEffect()
        {
            if (_buttonEffectPlayer != null)
            {
                Destroy(_buttonEffectPlayer.gameObject);
                _buttonEffectPlayer = null;
            }
        }
        
        #endregion

        #region トレーニング中流線エフェクト

        /// <summary>
        /// 流線エフェクトの生成
        /// </summary>
        private Animator CreateLineEffect(Transform parent)
        {
            var lineEffectObj =
                ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_TAG_TRAINING_LINE_EFFECT_PATH);
            var animator = Instantiate(lineEffectObj, parent).GetComponent<Animator>();
            var rectTransform = animator.GetComponent<RectTransform>();
            rectTransform.anchorMin = new Vector2(0.5f,0.5f);
            rectTransform.anchorMax = new Vector2(0.5f,0.5f);
            rectTransform.anchoredPosition = Math.VECTOR2_ZERO;
            var particleRendererArray = animator.GetComponentsInChildren<ParticleSystemRenderer>(true);
            foreach (var particleRenderer in particleRendererArray)
            {
                particleRenderer.sortingOrder = -100; // UIより奥に描画
            }
            return animator;
        }

        /// <summary>
        /// 流線エフェクトの再生
        /// </summary>
        public void PlayLineEffect()
        {
            const float DURATION = 0.2f;
            gameObject.SetActive(true);

            // #57416 Awake時にロードされたスプライトはカット終了時にカット中のリソース破棄に巻き込まれるので随時設定する
            var atlas = UIManager.Instance.LoadAtlas(TargetAtlasType.Race);
            var gradationSprite = atlas.GetSprite(AtlasSpritePath.Race.BG_RACE_RESULT_BASE_00);
            _topGradation.sprite = gradationSprite;
            _bottomGradation.sprite = gradationSprite;

            //古いのが残っていたらここで破棄して作り直す
            DestroyLineEffect();
            if (_topLineAnimator == null)
            {
                _topLineAnimator = CreateLineEffect(_topGradation.rectTransform);
            }
            
            if (_bottomLineAnimator == null)
            {
                _bottomLineAnimator = CreateLineEffect(_bottomGradation.rectTransform);
                _bottomLineAnimator.transform.localScale = new Vector3(-1f,1f,1f);
            }

            _topLineAnimator.Play("in");
            _bottomLineAnimator.Play("in");
            
            var color = _topGradation.color;
            color.a = 0f;
            _topGradation.color = color;
            _topGradation.DOFade(GRADATION_DEFAULT_ALPHA,DURATION);
            
            color = _bottomGradation.color;
            color.a = 0f;
            _bottomGradation.color = color;
            _bottomGradation.DOFade(GRADATION_DEFAULT_ALPHA,DURATION);
        }
        
        /// <summary>
        /// 流線エフェクトの停止
        /// </summary>
        public void StopLineEffect()
        {
            _topLineAnimator.Play("out");
            _bottomLineAnimator.Play("out");
            _topGradation.DOFade(0f,0.2f);
            _bottomGradation.DOFade(0f,0.2f);
        }
        
        /// <summary>
        /// 流線エフェクトの破棄
        /// </summary>
        public void DestroyLineEffect()
        {
            if(_topLineAnimator != null)
            {
                GameObject.Destroy(_topLineAnimator.gameObject);
                _topLineAnimator = null;
            }
            if(_bottomLineAnimator != null)
            {
                GameObject.Destroy(_bottomLineAnimator.gameObject);
                _bottomLineAnimator = null;
            }
        }

        #endregion

        #region トレーニングリザルト

        private void OnDisable()
        {
            //Disableタイミングで破棄を行い次回流線エフェクト再生時に作り直す
            DestroyLineEffect();
        }

        #endregion
    }
}