using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.Http;
using Gallop.RenderPipeline;

namespace Gallop
{
    /// <inheritdoc />
    /// <summary>
    /// シングルレースシーン
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeScene : SceneBase
    {
        [SerializeField]
        private Transform _charaPosition = null;

        public Transform CharacterPosition => _charaPosition;

        [SerializeField]
        private CameraController _focusCamera = null;

#if UNITY_EDITOR
        [SerializeField]
        public Color ShadowColor = GameDefine.COLOR_BLACK;
#endif
        public CameraController FocusCamera => _focusCamera;
    }

    /// <inheritdoc />
    /// <summary>
    /// シングルモードシーンコントローラー
    /// </summary>
    public sealed class SingleModeSceneController : SceneControllerBase<SingleModeScene>
    {
        public Transform CharacterPosition
        {
            get
            {
                if (_scene == null)
                {
                    return null;
                }

                return _scene.CharacterPosition;
            }
        }

        public CameraController FocusCamera
        {
            get
            {
                if (_scene == null)
                {
                    return null;
                }

                return _scene.FocusCamera;
            }
        }

        private SingleModeModelManager _modelManager = null;

        private LowResolutionCamera _lowResolutionCamera;

        #region #144182 リアルタイムシャドウを落とすためのパーツ
        /// <summary>
        /// リアルタイムシャドウを落とすためのパーツ（ライトとレシーバーメッシュの親）
        /// </summary>
        private GameObject _realtimeShadowParts;

        /// <summary>
        /// リアルタイムシャドウを落とすためのライト、育成の既存ライトと別に用意
        /// </summary>
        private Light _realtimeShadowLight;

        /// <summary>
        /// リアルタイムシャドウを落とすための地面メッシュ
        /// </summary>
        private GameObject _realtimeShadowRecevier;

        /// <summary>
        /// リアルタイムシャドウ利用中
        /// </summary>
        public bool IsUseRealtimeShadow => _realtimeShadowParts != null;

        #endregion

        /// <summary> 育成シナリオ別カメラプリセット </summary>
        private SingleModeScenarioCameraCharacterCorrectionPreset _correctionPreset;

        /// <summary>
        /// LowResolutionCameraのCreateRenderTextureから呼ばれる
        /// </summary>
        private static void OnCreateTexture(LowResolutionCamera lowResolutionCamera)
        {
            if (!UIManager.HasInstance())
            {
                return;
            }

            //URP:置き換え対応
            var cameraData = lowResolutionCamera.Camera.GetCameraData();
            UIManager.Instance.SetBgCameraRenderTexture(lowResolutionCamera.Texture);
            /*
            UIManager.Instance.SetBgCameraRenderTexture(lowResoCamera.Texture);
            */
            // #87154 背景カメラを使っていない時にClearFlags.Nothingにすると描画が崩れるのでEnableをチェックする
            if (BGManager.GetBgCameraEnable())
            {
                // 背景カメラがクリアするのでLowResolutionCameraはクリアしない
                lowResolutionCamera.Camera.clearFlags = CameraClearFlags.Nothing;
            }
        }

        private void OnReleaseTexture(LowResolutionCamera lowResoCamera)
        {
            if (!UIManager.HasInstance())
            {
                return;
            }

            UIManager.Instance.SetBgCameraRenderTexture(null);
        }

        /// <summary>
        /// シーンの初期化
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeScene()
        {
            var lowResolutionCamera = _scene.FocusCamera.gameObject.AddComponent<LowResolutionCamera>();
            lowResolutionCamera.OnCreateTextureCallback += OnCreateTexture;
            lowResolutionCamera.OnReleaseTextureCallback += OnReleaseTexture;
            lowResolutionCamera.InitializeVerticalWithBackGround();

            _lowResolutionCamera = lowResolutionCamera;

            DirectionalLightManager.Instance.SetEnable(true);
            _modelManager = new SingleModeModelManager();

            if (!SingleModeFlashInstanceManager.HasInstance())
            {
                SingleModeFlashInstanceManager.CreateInstance();
            }
            //各Scene、Viewのリソースを解放させないようにHash紐付けを行う
            AddHashToResourcesAssociatedOtherHash();
            yield break;
        }

        /// <summary>
        /// 各Scene、Viewのリソースを解放させないようにHash紐付けを行う
        /// </summary>
        private void AddHashToResourcesAssociatedOtherHash()
        {
            var keepHash = ResourceManager.ResourceHash.SceneTraining;
            var sceneHash = (ResourceManager.ResourceHash)ResourceManager.GetSceneLoadHash(SceneDefine.SceneId.SingleMode);
            ResourceManager.AddHashToResourcesAssociatedOtherHash(sceneHash, keepHash);

            var viewIdArray = new[]
            {
                SceneDefine.ViewId.SingleModeMain,
                SceneDefine.ViewId.SingleModeRaceEntry,
                SceneDefine.ViewId.SingleModeSkillLearning,
                SceneDefine.ViewId.SingleModeConfirmComplete,
                SceneDefine.ViewId.SingleModeResult,
                SceneDefine.ViewId.SingleModeMonthStart,
            };

            foreach (var viewId in viewIdArray)
            {
                var viewHash = (ResourceManager.ResourceHash)ResourceManager.GetViewLoadHash(viewId);
                ResourceManager.AddHashToResourcesAssociatedOtherHash(viewHash, keepHash);
            }
        }


        /// <summary>
        /// シーンの終了
        /// </summary>
        public override void FinalizeScene()
        {
            if (SingleModeFlashInstanceManager.HasInstance())
            {
                SingleModeFlashInstanceManager.DestroyInstance();
            }
            // BGM再生時の追加処理解除
            AudioManager.Instance.OnPlayBgm = null;

            if (_lowResolutionCamera != null)
            {
                _lowResolutionCamera.OnCreateTextureCallback -= OnCreateTexture;
                _lowResolutionCamera.OnReleaseTextureCallback -= OnReleaseTexture;
            }

            if (UIManager.HasInstance())
            {
                UIManager.Instance.SetBgCameraRenderTexture(null);
            }

            ClearModelCache();

            bool isDestroyInstance = true;
            bool isReleaseResource = true;
            var nextSceneId = SceneManager.Instance.GetNextSceneId();
            var nextViewId = SceneManager.Instance.GetNextViewId();
            //レースとストーリーに進む時以外はマネージャのインスタンスを削除する
            //もし今後OutGameに直接遷移する流れが追加されたら危険な予感
            if (nextSceneId == SceneDefine.SceneId.Race ||
                nextSceneId == SceneDefine.SceneId.Story ||
                nextSceneId == SceneDefine.SceneId.CraneGame ||
                nextSceneId == SceneDefine.SceneId.Live ||
                nextViewId == SceneDefine.ViewId.SingleModeStart)    //育成編成はホームと結合してホームシーンに属している
            {
                isDestroyInstance = false;
            }

            //会話の遷移は多々行うのでリソースは解放しないでおく
            if (nextSceneId == SceneDefine.SceneId.Story)
            {
                isReleaseResource = false;
            }

            //後処理
            _correctionPreset = null;

            //リソース開放する？
            if (isReleaseResource)
            {
                ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.SceneTraining);
            }

            //Instance消しても良い？
            if (isDestroyInstance)
            {
                SingleModeUtils.ClearPlayData();
            }

            base.FinalizeScene();
        }

        /// <summary>
        /// カメラ有効無効切替
        /// </summary>
        /// <param name="enable"></param>
        public void SetCameraEnable(bool enable)
        {
            if (_scene != null)
            {
                _scene.FocusCamera.GetCamera().enabled = enable;
                if (_lowResolutionCamera != null)
                {
                    _lowResolutionCamera.enabled = enable;
                }
            }
        }

        /// <summary>
        /// カメラの位置を更新。キャラクターの身長補正を行う
        /// </summary>
        public void UpdateFocusCameraPosition(
            SingleModeScenarioCameraCharacterCorrectionPreset.DisplayType displayType,
            ModelController modelController,
            bool useScenarioPreset = true)
        {
            var scenarioId = WorkDataManager.Instance.SingleMode.GetScenarioId();
            if (_correctionPreset == null)
            {
                _correctionPreset = ResourceManager.LoadOnScene<SingleModeScenarioCameraCharacterCorrectionPreset>(ResourcePath.SINGLE_MODE_CAMERA_CHARACTER_CORRECTION_PRESET_PATH);
            }

            var preset = useScenarioPreset ? _correctionPreset.PresetList.Find(a => a.ScenarioId == scenarioId && a.Display == displayType) : null;
            if (preset == null)
            {
                // 標準機能：画面ごとのカメラ位置
                FocusCamera.SetCameraPosWithFixCharacterScale(modelController);
            }
            else
            {
                // シナリオ別：育成TOPやトレーニング選択画面のカメラ位置
                var charaHeight = modelController.GetHeight();
                var cameraPos = preset.CameraInitPosValue;
#if UNITY_EDITOR
                // Inspectorでの調整反映
                if (CameraController.ForceUseDebugCameraPos)
                {
                    cameraPos = CameraController.CameraDebugPos;
                }
#endif

                // 身長差補正
                var heightScaleVec = Math.VECTOR2_ZERO;
                if (CameraOffsetFromCharaScale.IsHighCharacter(charaHeight))
                {
                    heightScaleVec = preset.HeightCorrectionValue;
                }
                else if (CameraOffsetFromCharaScale.IsLowCharacter(charaHeight))
                {
                    heightScaleVec = preset.LowCorrectionValue;
                }
                else
                {
                    heightScaleVec = preset.NormalCorrectionValue;
                }
#if UNITY_EDITOR
                // Inspectorでの調整反映
                if (CameraOffsetFromCharaScale.ForceUseMinMaxScaleNormal)
                {
                    if (CameraOffsetFromCharaScale.IsHighCharacter(charaHeight))
                    {
                        CameraOffsetFromCharaScale.Get3DCharaHeightScale(charaHeight, ref heightScaleVec);
                    }
                    if (CameraOffsetFromCharaScale.IsLowCharacter(charaHeight))
                    {
                        CameraOffsetFromCharaScale.Get3DCharaHeightScale(charaHeight, ref heightScaleVec);
                    }
                    else
                    {
                        CameraOffsetFromCharaScale.Get3DCharaHeightScale(charaHeight, ref heightScaleVec);
                    }
                }
#endif
                var heightScale = CameraOffsetFromCharaScale.Get3DCharaHeightScale(charaHeight, ref heightScaleVec);

                cameraPos.y *= heightScale;
                if (CameraOffsetFromCharaScale.IsHighCharacter(modelController.GetHeight()))
                {
                    cameraPos.z *= heightScale;
                }
                // カメラ座標
                FocusCamera.transform.localPosition = cameraPos;
            }
        }

        /// <summary>
        /// WorkSingleModeに対応するモデルを返却する。
        /// </summary>
        public SingleRaceModelController GetModel(int cardId, int dressId)
        {
            if (_modelManager == null)
            {
                return null;
            }

            var model = _modelManager.Find(cardId, dressId);
            if (model == null)
            {
                return null;
            }

            return model.Controller;
        }

        /// <summary>
        /// WorkSingleModeに対応するモデルを作成して登録する。
        /// </summary>
        public SingleRaceModelController CreateModel(int cardId, int dressId, bool addVoiceCue = true)
        {
            if (_modelManager == null)
            {
                return null;
            }

            SingleModeModelManager.Model model = _modelManager.Find(cardId, dressId);
            if (model == null)
            {
                model = new SingleModeModelManager.Model();
                model.Setup(cardId, dressId, _scene.CharacterPosition, addVoiceCue: addVoiceCue);
                _modelManager.RegisterModel(model);
            }
            return model.Controller;
        }

#if CYG_DEBUG
        /// <summary>
        /// CharaIDを利用しセンターキャラを生成する(Debug専用)
        /// </summary>
        public SingleRaceModelController CreateModelByCharaId(int charaId, int dressId)
        {
            if (_modelManager == null)
            {
                return null;
            }

            SingleModeModelManager.Model model = _modelManager.FindByCharaId(charaId, dressId);
            if (model == null)
            {
                model = new SingleModeModelManager.Model();
                model.SetupByCharaId(charaId, dressId, _scene.CharacterPosition, circleShadow: false);
                _modelManager.RegisterModel(model);
            }
            return model.Controller;
        }
#endif

        /// <summary>
        /// モデル破棄登録解除
        /// </summary>
        public void DestroyModel(int cardId, int dressId)
        {
            if (_modelManager == null) return;
            var model = _modelManager.Find(cardId, dressId);
            DestroyModel(model);
        }

        /// <summary>
        /// モデル破棄登録解除
        /// </summary>
        public void DestroyModel(SingleModeModelManager.Model model)
        {
            if (model == null) return;
            model.Cleanup();
            _modelManager.RemoveModel(model);
        }

        /// <summary>
        /// キャラIDによるモデル破棄登録解除
        /// </summary>
        public void DestroyModelByCharaId(int charaId, int dressId)
        {
            if (_modelManager == null) return;
            var model = _modelManager.FindByCharaId(charaId, dressId);
            DestroyModel(model);
        }

        public SingleModeModelManager.Model CreateBgCharaModelByCharaId(int charaId, int dressId, bool circleShadow = false)
        {
            if (_modelManager == null)
            {
                return null;
            }

            var model = _modelManager.FindByCharaId(charaId, dressId);
            if (model == null)
            {
                model = new SingleModeModelManager.Model();
                model.SetupByCharaId(charaId, dressId, _scene.CharacterPosition, circleShadow, false);
                _modelManager.RegisterModel(model);
            }
            return model;
        }

        public SingleModeModelManager.Model CreateBgCharaModelBySupportCardId(int supportCardId, int dressId, bool circleShadow = false)
        {
            if (_modelManager == null)
            {
                return null;
            }

            var model = _modelManager.FindBySupportCardId(supportCardId, dressId);
            if (model == null)
            {
                model = new SingleModeModelManager.Model();
                model.SetupBySupportCard(supportCardId, dressId, _scene.CharacterPosition, circleShadow, false);
                _modelManager.RegisterModel(model);
            }
            return model;
        }

        public SingleModeModelManager.Model CreateBgCharaModelByMobId(int mobId, int dressId, bool circleShadow = false)
        {
            if (_modelManager == null)
            {
                return null;
            }

            var model = _modelManager.FindByMobId(mobId, dressId);
            if (model == null)
            {
                model = new SingleModeModelManager.Model();
                model.SetupByMobId(mobId, dressId, _scene.CharacterPosition, circleShadow, false);
                _modelManager.RegisterModel(model);
            }
            return model;
        }

        /// <summary>
        /// モデルを削除する
        /// </summary>
        public void ClearModelCache()
        {
            if (_modelManager != null)
            {
                _modelManager.Cleanup();
            }
        }

        /// <summary>
        /// シーンのアクティブ切替
        /// </summary>
        /// <param name="isActive"></param>
        public void SetActiveScene(bool isActive)
        {
            _scene.SetActiveWithCheck(isActive);
        }

        /// <summary>
        /// 足元影色の更新
        /// </summary>
        /// <param name="color"></param>
        public void UpdateShadowColor(Color color)
        {
            if (_modelManager == null)
                return;
            _modelManager.UpdateShadowColor(color);
        }

        /// <summary>
        /// リアルタイムシャドウ関連のパラメーター
        /// </summary>
        /// <param name="realtimeShadowParam"></param>
        public void UpdateRealtimeShadowParam(RealtimeShadowParam realtimeShadowParam, bool useRealTimeShadow)
        {
            if (_modelManager == null) return;

            //所持しているモデルにリアルタイムシャドウの設定を適用
            _modelManager.SetShadowType(realtimeShadowParam.IsEnable && useRealTimeShadow ? ModelLoader.ShadowType.Normal
                : ModelLoader.ShadowType.None);

            if (realtimeShadowParam.IsEnable && useRealTimeShadow)
            {
                CreateRealtimeShadowParts();
                SetupRealtimeShadow(realtimeShadowParam);
            }
            else
            {
                DestroyRealtimeShadowParts();
            }
        }

        /// <summary>
        /// リアルタイムシャドウを落とすためメッシュを新規に用意
        /// この関数が呼ばれた際に新規オブジェクトが作成される
        /// </summary>
        /// <returns></returns>
        public GameObject CreateRealtimeShadowRecevier()
        {
            if (_realtimeShadowRecevier == null)
            {
                return null;
            }

            var newRecevier = GameObject.Instantiate(_realtimeShadowRecevier, Math.VECTOR3_ZERO, Quaternion.Euler(Math.VECTOR3_ZERO));
            return newRecevier;
        }

        /// <summary>
        /// リアルタイムシャドウを落とすためのパーツを削除
        /// View遷移する際に削除する必要があるため、外部から呼べるように変更
        /// </summary>
        public void DestroyRealtimeShadowParts()
        {
            if (_realtimeShadowParts == null)
            {
                return;
            }

            _realtimeShadowLight = null;

            _realtimeShadowRecevier = null;

            GameObject.Destroy(_realtimeShadowParts);
            _realtimeShadowParts = null;
        }

        /// <summary>
        /// リアルタイムシャドウ関連のパーツの有効無効切り替え
        /// </summary>
        /// <param name="toActive"></param>
        public void SetRealtimeShadowPartsActive(bool toActive)
        {
            if (_realtimeShadowParts == null)
            {
                return;
            }

            _realtimeShadowParts.SetActive(toActive);
        }

        /// <summary>
        /// リアルタイムシャドウを落とすためのライトに設定を適用
        /// </summary>
        /// <param name="realtimeShadowParam"></param>
        private void SetupRealtimeShadow(RealtimeShadowParam realtimeShadowParam)
        {
            if (_realtimeShadowParts == null || realtimeShadowParam == null)
            {
                return;
            }

            _realtimeShadowLight.enabled = true;
            _realtimeShadowLight.type = LightType.Directional;
            _realtimeShadowLight.shadowStrength = realtimeShadowParam.ShadowStrength;
            _realtimeShadowLight.shadows = realtimeShadowParam.ShadowType;
            _realtimeShadowLight.transform.localRotation = Quaternion.Euler(realtimeShadowParam.Rotation);
            _realtimeShadowLight.cullingMask = GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.Layer3D)
                | GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.LayerCHAR);
        }

        /// <summary>
        /// リアルタイムシャドウを落とすためのパーツを作成
        /// </summary>
        private void CreateRealtimeShadowParts()
        {
            if (_realtimeShadowParts == null)
            {
                var shadowPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.CHARACTER_SHADOW_RECEIVER_PREFAB_PATH);
                _realtimeShadowParts = GameObject.Instantiate<GameObject>(shadowPrefab);

                _realtimeShadowLight = _realtimeShadowParts.GetComponentInChildren<Light>();

                var receiverRenderer = _realtimeShadowParts.GetComponentInChildren<Renderer>();
                _realtimeShadowRecevier = receiverRenderer.gameObject;
            }
        }


#if UNITY_EDITOR
        public override void UpdateScene()
        {
            base.UpdateScene();
            var scene = GetScene();
            if (scene != null)
            {
                UpdateShadowColor(scene.ShadowColor);
            }
        }
#endif

        #region static関数
        /// <summary>
        /// 通信処理のラップ
        /// </summary>
        /// <typeparam name="TRequestType">APIリクエストの型</typeparam>
        /// <typeparam name="TResponseType">APIレスポンスの型</typeparam>
        /// <param name="requestApi">APIリクエスト</param>
        /// <param name="onSuccess">成功時のコールバック</param>
        /// <returns></returns>
        public static IEnumerator SendAsync<TRequestType, TResponseType>(TRequestType requestApi,
            System.Action<TResponseType> onSuccess,
                System.Action<ErrorType, int> onError = null)
            where TRequestType : RequestBase<TResponseType>
            where TResponseType : ResponseCommon
        {
            TResponseType response = null;

            bool isComplete = false;
            bool isSuccess = false;
            requestApi.Send(res =>
            {
                response = res;
                isComplete = true;
                isSuccess = true;
            }, (type, i) =>
            {
                isComplete = true;
                onError?.Invoke(type, i);
            });

            yield return new WaitWhile(() => !isComplete);

            if (isSuccess)
            {
                onSuccess?.Invoke(response);
            }
        }

        /// <summary>
        /// レース終了通信
        /// </summary>
        public static IEnumerator SendRaceEndAsync(RaceInfo raceInfo, System.Action onComplete = null)
        {
            if (TutorialSingleMode.IsTutorial)
            {
                // 育成チュートリアルでは通信しない
                // 代わりにファン数等必要なパラメータを増やしておく
                TutorialSingleMode.OnRaceEnd(raceInfo);

                onComplete?.Invoke();
                yield break;
            }

            var tempSingle = TempData.Instance.SingleModeData;
            if (tempSingle.PrevRaceEndTurn != WorkDataManager.Instance.SingleMode.GetCurrentTurn())
            {
                WorkDataManager.Instance.RaceStateData.ResetRaceResultAddTrophyInfo();
                WorkDataManager.Instance.RaceStateData.ResetRaceResultCampaignInfo();

                //RaceEndAPIを叩いて、レース結果を確定させる
                yield return SingleModeAPI.SendRaceEndAsync(onComplete, raceInfo);
            }
            else
            {
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// SingleRaceStartInfoからRaceInfo生成。
        /// </summary>
        public static RaceInitializer.LoadRaceInfo CreateRaceInfoByRaceStartInfo(SingleRaceStartInfo raceStartInfo, string raceInfo, int randoSeedAdd = 0)
        {
            var program = MasterDataManager.Instance.masterSingleModeProgram.Get(raceStartInfo.program_id);
            if (null == program)
            {
                return null;
            }

            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                program.RaceInstanceId,
                raceStartInfo.race_horse_data,
                raceStartInfo.random_seed + randoSeedAdd,
                RaceDefine.RaceType.Single,
                RaceUtil.GetSeason(program.RaceInstanceId),
                (RaceDefine.Weather)raceStartInfo.weather,
                (RaceDefine.GroundCondition)raceStartInfo.ground_condition,
                raceInfo,
                raceStartInfo.program_id);
            var info = new RaceInitializer.LoadRaceInfo(ref buildParam);
            RaceInitializer.CreateRaceInfo(info);
            return info;
        }

        /// <summary>
        /// チーム対抗戦リザルトからRaceInfoを生成する
        /// </summary>
        /// <param name="raceResult"></param>
        /// <param name="randomSeedAdd"></param>
        public static RaceInitializer.LoadRaceInfo CreateRaceInfoByTeamRaceResult(WorkSingleModeScenarioTeamRace.SingleTeamRaceResult raceResult, int randomSeedAdd = 0)
        {
            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                (int)raceResult.RaceInstanceId,
                raceResult.RaceHorseData,
                raceResult.RandomSeed + randomSeedAdd,
                RaceDefine.RaceType.SingleModeScenarioTeamRace,
                (GameDefine.BgSeason)(int)raceResult.Season,
                (RaceDefine.Weather)(int)raceResult.Weather,
                (RaceDefine.GroundCondition)(int)raceResult.GroundCondition,
                raceResult.RaceScenario);
            var info = new RaceInitializer.LoadRaceInfo(ref buildParam);
            RaceInitializer.CreateRaceInfo(info);
            return info;
        }

        /// <summary>
        /// グラマス編シナリオレースのRaceInfoを生成する
        /// </summary>
        /// <param name="randoSeedAdd"></param>
        public static void CreateVenusRaceInfo(int randoSeedAdd = 0)
        {
            var scenarioVenus = WorkDataManager.Instance.SingleMode.Character.ScenarioVenus;
            var program = MasterDataManager.Instance.masterSingleModeProgram.Get(scenarioVenus.RaceStartInfo.program_id);
            if (null == program)
            {
                return;
            }

            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                program.RaceInstanceId,
                scenarioVenus.RaceStartInfo.race_horse_data,
                scenarioVenus.RaceStartInfo.random_seed + randoSeedAdd,
                RaceDefine.RaceType.Single,
                RaceUtil.GetSeason(program.RaceInstanceId),
                (RaceDefine.Weather)scenarioVenus.RaceStartInfo.weather,
                (RaceDefine.GroundCondition)scenarioVenus.RaceStartInfo.ground_condition,
                scenarioVenus.RaceScenario,
                scenarioVenus.RaceStartInfo.program_id);
            var info = new RaceInitializer.LoadRaceInfo(ref buildParam);
            RaceInitializer.CreateRaceInfo(info);
        }
        #endregion
    }
}
