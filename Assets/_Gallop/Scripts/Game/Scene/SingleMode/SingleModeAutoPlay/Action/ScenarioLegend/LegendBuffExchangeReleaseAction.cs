namespace Gallop.SingleModeAutoPlay
{
    public class LegendBuffExchangeReleaseAction : ISingleModeAutoPlayAgentAction
    {
        private readonly IAutoPlayLegendBuffSelector _buffSelector;

        public LegendBuffExchangeReleaseAction(IAutoPlayLegendBuffSelector buffSelector)
        {
            _buffSelector = buffSelector;
        }

        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.GetForeFrontDialog();

            if (dialog is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeScenarioLegendBuffExchangeRelease>() is { } exchangeRelease)
            {
                var buffId = _buffSelector.GetRecord().RemoveBuffId;

                var proxy = exchangeRelease.AutoPlayProxy;
                proxy.SelectReleaseBuff(buffId);
                dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right).onClick.Invoke();

                return true;
            }
            return false;
        }
    }
}