namespace Gallop.SingleModeAutoPlay
{
    public class LegendBuffSelectConfirmAction : ISingleModeAutoPlayAgentAction
    {
        private bool _isCalled = false;

        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.GetForeFrontDialog();

            if (dialog is DialogCommon dialogCommon)
            {
                if (dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeScenarioLegendBuffSelectConfirm>() is { })
                {
                    if (!_isCalled)
                    {
                        dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right).onClick.Invoke();
                        _isCalled = true;
                    }
                    return true;
                }

                if (dialogCommon.DialogData.ContentsObject == null)
                {
                    if (!_isCalled)
                    {
                        dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right).onClick.Invoke();
                        _isCalled = true;
                    }
                    return true;
                }
            }
            return false;
        }
    }
}