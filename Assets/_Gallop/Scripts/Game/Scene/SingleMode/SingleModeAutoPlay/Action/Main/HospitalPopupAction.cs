namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 保健室確認ダイアログから進行する
    /// </summary>
    public class HospitalPopupAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.Instance.GetForefrontDialog();
            
            if (dialog is DialogCommon dialogCommon)
            {
                var dialogSimpleCheckWithImage = dialogCommon.DialogData.ContentsObject.GetComponent<DialogSimpleCheckWithImage>();
                if (dialogSimpleCheckWithImage != null)
                {
                    dialogCommon.DialogData.RightButtonCallBack.Invoke(dialogCommon);
                    return true;
                }
            }
            return true;
        }
    }
}