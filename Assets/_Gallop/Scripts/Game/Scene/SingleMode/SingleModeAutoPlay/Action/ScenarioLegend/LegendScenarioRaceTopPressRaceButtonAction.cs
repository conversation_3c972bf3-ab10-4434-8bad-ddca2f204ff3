namespace Gallop.SingleModeAutoPlay
{
    public class LegendScenarioRaceTopAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (SceneManager.Instance.GetCurrentViewController() is SingleModeScenarioLegendScenarioRaceTopViewController viewController)
            {
                viewController.GetView().RaceButton.onClick?.Invoke();
                stateMachine.ChangeState<LegendScenarioRaceRaceEntryDialogState>();
                return true;
            }

            return false;
        }
    }
}