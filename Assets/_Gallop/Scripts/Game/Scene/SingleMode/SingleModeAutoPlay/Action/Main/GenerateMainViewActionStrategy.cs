using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public static class GenerateMainViewActionStrategyFactory
    {
        public static IGenerateActionStrategy CreateMainViewActionStrategy(SingleModeDefine.ScenarioId scenarioId)
        {
            return scenarioId switch
            {
                SingleModeDefine.ScenarioId.URA => new URAMainViewActionStrategy(),
                SingleModeDefine.ScenarioId.Arc => new ArcMainViewActionStrategy(),
                SingleModeDefine.ScenarioId.Legend => new LegendMainViewActionStrategy(),
                _ => new URAMainViewActionStrategy()
            };
        }
    }

    public class URAMainViewActionStrategy : SingleModeMainViewGenerateActionStrategyBase
    {
        protected override ICommandSelector CommandSelector { get; } = new URACommandSelector();
    }

    public class ArcMainViewActionStrategy : SingleModeMainViewGenerateActionStrategyBase
    {
        protected override ICommandSelector CommandSelector { get; } = new ArcCommandSelector();
    }

    public class LegendMainViewActionStrategy : SingleModeMainViewGenerateActionStrategyBase
    {
        protected override ICommandSelector CommandSelector { get; } = new LegendCommandSelector();
    }
}