namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// お休み確認ダイアログから進行する
    /// </summary>
    public class HolidayPopupAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dc && 
                dc.GetDialogInnerBaseComponent<DialogSimpleCheckWithImage>() is {})
            {
                dc.DialogData.RightButtonCallBack.Invoke(dc);
                return true;
            }
            return false;
        }
    }
}