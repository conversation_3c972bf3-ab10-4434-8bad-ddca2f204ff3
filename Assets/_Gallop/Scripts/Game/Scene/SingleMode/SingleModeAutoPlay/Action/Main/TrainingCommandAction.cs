namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// トレーニングコマンドを選択
    /// </summary>
    public class TrainingCommandAction : ISingleModeAutoPlayAgentAction
    {
        private readonly AutoPlayTrainingActionParameter _parameter;
        public TrainingCommandAction(AutoPlayTrainingActionParameter parameter)
        {
            _parameter = parameter;
        }

        public bool ExecuteAction(StateMachine stateMachine)
        {
            if(SceneManager.Instance.GetCurrentViewBase() is SingleModeMainView mainView && mainView.TrainingFooter != null)
            {
                var footerItem = mainView.TrainingFooter.GetFooterItemWithCommandId(_parameter.TrainingCommandId);
                if (footerItem != null)
                {
                    // 操作前に現在選択されているコマンドが選択しようとしているコマンドと一致するか見ておく
                    var isSelected = mainView.TrainingFooter.AutoPlayProxy.IsSelectedTargetItem(footerItem);

                    // コマンドに触れている状態にする
                    mainView.TrainingFooter.AutoPlayProxy.SelectDown(footerItem, footerItem.TrainingMenu);

                    // 操作前に選択済みのコマンドではなかったならコマンド選択操作であって、コマンド決定操作ではなかったと解釈
                    if (!isSelected)
                    {
                        // ステートを変更しなければ再度TrainingCommandActionが呼ばれる
                        return true;
                    }

                    // コマンド決定操作を行う
                    mainView.TrainingFooter.AutoPlayProxy.ClickSelectedFooterItem();
                    stateMachine.ChangeSubState<SingleModeMainViewTrainingCommandSelectedState>();
                    return true;
                }
            }
            return false;
        }
    }

    public class AutoPlayTrainingActionParameter : IAutoPlayActionParameter
    {
        public TrainingDefine.TrainingCommandId TrainingCommandId { get; }

        public AutoPlayTrainingActionParameter(TrainingDefine.TrainingCommandId trainingCommandId)
        {
            TrainingCommandId = trainingCommandId;
        }
    }
}