namespace Gallop.SingleModeAutoPlay
{
    public class WaitTurnStartCheckerAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if( SceneManager.Instance.GetCurrentViewController() is SingleModeMainViewController vc && 
                vc.TurnStartChecker.BlockAutoPlay)
            {
                //演出再生中なら何もしないでおく
                return true;
            }
            return false;
        }
    }
}