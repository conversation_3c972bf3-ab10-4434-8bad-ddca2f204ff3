namespace Gallop.SingleModeAutoPlay
{
    public class StopAutoPlayAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var singleModeStateMachine = stateMachine.GetPlaySingleModeStateMachine();
            singleModeStateMachine.ChangeSubState<NotImplState>();
            SingleModeAutoPlayAgent.Instance.StopAutoPlay();
            return true; 
        }
    }
}