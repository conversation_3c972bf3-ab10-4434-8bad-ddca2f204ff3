using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class LegendBuffSelectAction : ISingleModeAutoPlayAgentAction
    {
        private readonly IAutoPlayLegendBuffSelector _buffSelector;

        /// <summary>
        /// バフ選択画面で停止する設定をしていたらおまかせ育成を一時停止
        /// </summary>
        private bool ShouldPause
        {
            get
            {
                var stopperConditionIdList = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord.ModifyStopperConditionIdList;
                return stopperConditionIdList?
                    .Any(x =>
                    {
                        var masterData = MasterDataManager.Instance.masterOmakaseStopCondition.Get(x);
                        return masterData?.ConditionType == (int)AutoPlayPauseInfo.AutoPlayPauseConditionType.LegendBuffSelect;
                    }) ?? false;
            }
        }

        public LegendBuffSelectAction(IAutoPlayLegendBuffSelector buffSelector)
        {
            _buffSelector = buffSelector;
        }

        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.GetForeFrontDialog();

            if (dialog is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeScenarioLegendBuffSelectCore>() is { } buffSelectCore)
            {
                if (ShouldPause)
                {
                    stateMachine.ChangeSubState<LegendBuffSelectPauseState>();
                    return true;
                }

                var buffId = _buffSelector.GetRecord().SelectBuffId;

                var proxy = buffSelectCore.AutoPlayProxy;
                proxy.SelectBuff(buffId);
                proxy.Decide();

                return true;
            }
            return false;
        }
    }
}