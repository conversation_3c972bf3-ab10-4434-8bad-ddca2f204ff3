using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class LegendStoryChoiceAction : StoryChoiceAction, ISingleModeAutoPlayAgentAction
    {
        private const int LEGEND_SUPPORT_CARD_STORY_ID = 830241003;
        private const int LEGEND_OUTING_STORY_ID = 830241020;

        bool ISingleModeAutoPlayAgentAction.ExecuteAction(StateMachine stateMachine)
        {
            var vc = StoryManager.Instance.ViewController;
            if (vc == null) return false;

            var storyId = StoryManager.Instance.StoryId;
            if (storyId != LEGEND_SUPPORT_CARD_STORY_ID && storyId != LEGEND_OUTING_STORY_ID)
            {
                return base.ExecuteAction(stateMachine);
            }

            var choiceSelector = CreateSelector();
            var choiceIndex = choiceSelector.GetChoiceIndex();
            var proxy = vc.AutoPlayProxy;
            return proxy.TryChoice(choiceIndex);
        }

        private LegendEventChoiceSelector CreateSelector()
        {
            var planRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
            return new LegendEventChoiceSelector(planRecord);
        }

        /// <summary>
        /// 伝説編のイベント選択を心得獲得重視度によって変更する
        /// </summary>
        private class LegendEventChoiceSelector
        {
            private readonly IAutoPlayPlanRecord _planRecord;

            public LegendEventChoiceSelector(IAutoPlayPlanRecord planRecord)
            {
                _planRecord = planRecord;
            }

            public int GetChoiceIndex()
            {
                return GetTargetChoiceIndexList().GetByRandom();
            }

            private IReadOnlyList<int> GetTargetChoiceIndexList()
            {
                var modifyPtList = GetModifyPtList();
                return modifyPtList
                    .Select((value, index) => (value, index))
                    .GroupBy(valueAndIndex => valueAndIndex.value)
                    .OrderByDescending(group => group.Key)
                    .FirstOrDefault()
                    .Select(valueAndIndex => valueAndIndex.index)
                    .ToList();
            }

            private List<int> GetModifyPtList()
            {
                // 伝説編用のバフ選択の重み付けIDは1004~1006
                const int MODIFY_ID_9046 = 1004;
                const int MODIFY_ID_9047 = 1005;
                const int MODIFY_ID_9048 = 1006;

                return new()
                {
                    GetModifyPt(MODIFY_ID_9046),
                    GetModifyPt(MODIFY_ID_9047),
                    GetModifyPt(MODIFY_ID_9048),
                };
            }

            private int GetModifyPt(int modifyId)
            {
                var settingType = _planRecord.ModifyPtRecordList.FirstOrDefault(x => x.ModifyId == modifyId)?.CurrentSettingType ?? 0;
                return (int)settingType;
            }
        }
    }
}