namespace Gallop.SingleModeAutoPlay
{
    public class DialogSingleModeReserveAlertDialogAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dc &&
                dc.GetDialogInnerBaseComponent<DialogSingleModeReserveAlertDialog>() is { } dialogSingleModeReserveAlertDialog)
            {
                // コマンド選択で一時停止が必要なら停止ステートに切り替えるだけにする
                if (NeedAutoPlayPauseBySelectCommand())
                {
                    stateMachine.GetPlaySingleModeStateMachine().ChangeSubState<PauseAutoPlayState>();
                    return true;
                }
                var program = MasterDataManager.Instance.masterSingleModeProgram.Get(dialogSingleModeReserveAlertDialog.ProgramId);
                SingleModeAutoPlayAgent.Instance.Proxy.SetParameter(new AutoPlayRaceActionParameter(program));
                dc.DialogData.RightButtonCallBack.Call(dc);
                dc.Close();
                return true;
            }

            return false;
        }

        /// <summary>
        /// コマンド選択時に一時停止する必要があるかチェックする
        /// </summary>
        private bool NeedAutoPlayPauseBySelectCommand()
        {
            var weightRepository = SingleModeAutoPlayAgent.Instance.Proxy.GetWeightRepository();
            var pauseInfo = weightRepository?.AutoPlayPauseInfo;
            return pauseInfo?.NeedAutoPlayPauseBySelectCommand() ?? false;
        }
    }
}