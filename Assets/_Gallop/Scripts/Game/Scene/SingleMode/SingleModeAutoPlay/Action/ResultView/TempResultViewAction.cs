namespace Gallop.SingleModeAutoPlay
{
    public class TempResultViewAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (stateMachine is SingleModeResultViewStateBase resultViewState)
            {
                if (resultViewState.Step > SingleModeResultSequence.Step.ShowFactorSelect)
                {
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub);
                    return true;
                }
            }

            return false;
        }
    }
}