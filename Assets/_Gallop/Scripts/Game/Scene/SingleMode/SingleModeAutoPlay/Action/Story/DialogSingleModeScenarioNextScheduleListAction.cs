namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// スケジュール表示を進めるアクション
    /// </summary>
    public class DialogSingleModeScenarioNextScheduleListAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.Instance.GetForefrontDialog();
            if (dialog is DialogCommon dialogCommon && 
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeScenarioNextScheduleList>() is { } scheduleList)
            {
                // 閉じるボタンが押せるようになったら押す処理を実行する
                if (scheduleList.AutoPlayProxy.IsEnableCloseButton)
                {
                    scheduleList.AutoPlayProxy.OnClickCloseButton();
                }
                return true;
            }

            return false;
        }
    }
}