namespace Gallop.SingleModeAutoPlay
{
    public class DialogSingleModeReserveCancelAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeReserveCancel>() is { })
            {
                dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Center).onClick.Invoke();
                return true;
            }

            return false;
        }
    }
}