namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// シナリオレースのレースエントリーダイアログのアクション
    /// </summary>
    public class LegendScenarioRaceDialogRaceEntryAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {   
            var dialog = DialogManager.Instance.GetForefrontDialog();
            if (dialog is DialogCommon dialogCommon)
            {
                dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right).onClick?.Invoke();
                
                if( SceneManager.Instance.GetCurrentViewController() is SingleModeScenarioLegendScenarioRaceTopViewController vc && 
                    vc.AutoPlayProxy.IsNeedPlayRivalEntryAnimation)
                {
                    //ライバル演出がある場合はライバル演出へ
                    stateMachine.ChangeState<LegendScenarioRaceWaitRivalCuttState>();
                }
                else
                {
                    stateMachine.ChangeState<LegendScenarioRaceWaitEntryPaddockState>();
                }
                return true;
            }

            return false;
        }
    }
}