using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class RaceResultListPauseContinueAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            // コンティニュー不可の場合は何もしない
            if (!CanContinue()) return false;

            var planRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
            var stopperConditionIdList = planRecord.ModifyStopperConditionIdList;

            if (MasterDataManager.Instance.masterOmakaseStopCondition
                    .GetListWithScenarioIdOrderByStopConditionIdAsc(planRecord.ScenarioId)
                    .FirstOrDefault(x => x.ConditionType == (int)AutoPlayPauseInfo.AutoPlayPauseConditionType.ContinuableRace) is not { } stopConditionMaster || //対象マスターが存在しない
                !(stopperConditionIdList?.Contains(stopConditionMaster.StopConditionId) ?? false)) return false; //もしくは停止コンディション一覧に含まれていない場合は停止しない

            //停止ステートに遷移
            stateMachine.ChangeSubState<SingleModePaddockResultContinueDialogState>();
            return true;
        }

        /// <summary>
        ///  パドック画面かレース画面でコンティニュー可能か
        /// </summary>
        private bool CanContinue()
        {
            var viewController = SceneManager.Instance.GetCurrentViewController();
            if (viewController is PaddockViewControllerBase paddockViewController)
            {
                return paddockViewController.AutoPlayProxy.CanContinue();
            }

            if (viewController is RaceMainViewController raceMainViewController)
            {
                return raceMainViewController.AutoPlayProxy.CanContinue();
            }
            
            return false;
        }
    }
}