using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public abstract class SingleModeMainViewGenerateActionStrategyBase : IGenerateActionStrategy
    {
        /// <summary> コマンド選択を行うもの </summary>
        protected abstract ICommandSelector CommandSelector { get; }

#if CYG_DEBUG
        ICommandSelector IGenerateActionStrategy.CommandSelector => CommandSelector;
#endif

        /// <summary> 実行する行動を返す </summary>
        List<ISingleModeAutoPlayAgentAction> IGenerateActionStrategy.GenerateActionList()
        {
            return new List<ISingleModeAutoPlayAgentAction>() { CommandSelector.SelectCommand() };
        }

        /// <summary> 実行するトレーニングを返す </summary>
        List<ISingleModeAutoPlayAgentAction> IGenerateActionStrategy.GenerateTrainingSelectActionList(AutoPlayTrainingActionParameter parameter)
        {
            return new List<ISingleModeAutoPlayAgentAction>() { CommandSelector.SelectTraining(parameter) };
        }
    }
}