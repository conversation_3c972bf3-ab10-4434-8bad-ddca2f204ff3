namespace Gallop.SingleModeAutoPlay
{
    public class LegendBuffExchangeConfirmAction : ISingleModeAutoPlayAgentAction
    {
        private bool _isCalled = false;

        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.GetForeFrontDialog();

            if (dialog is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeScenarioLegendBuffExchangeConfirm>() is { })
            {
                if (!_isCalled)
                {
                    _isCalled = true;
                    dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right).onClick.Invoke();
                }
                return true;
            }
            return false;
        }
    }
}