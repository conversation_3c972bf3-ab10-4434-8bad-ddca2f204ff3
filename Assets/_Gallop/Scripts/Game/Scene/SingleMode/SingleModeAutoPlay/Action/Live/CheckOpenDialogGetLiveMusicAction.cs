namespace Gallop.SingleModeAutoPlay
{
    public class CheckOpenDialogGetLiveMusicAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogGetLiveMusic>() is { })
            {
                // 楽曲獲得ダイアログが開いたらステート変更
                stateMachine.ChangeSubState<DialogGetLiveMusicState>();
                return true;
            }

            return false;
        }
    }
}