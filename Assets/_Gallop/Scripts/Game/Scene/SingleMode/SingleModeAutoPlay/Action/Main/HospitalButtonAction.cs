namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 育成TOPで保健室ボタンを押す
    /// </summary>
    public class HospitalButtonAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var currentView = SceneManager.Instance.GetCurrentViewBase();
            if (currentView is SingleModeMainView mainView && mainView.StablesPanel.CheckAlive()?.AutoPlayProxy != null)
            {
                mainView.StablesPanel.AutoPlayProxy.OnClickHospitalButton();
                // レース予約など警告がある可能性があるのでステート遷移
                stateMachine.ChangeSubState<SingleModeMainViewClickedHolidayButtonState>();
                return true;
            }

            return false;
        }
    }
}