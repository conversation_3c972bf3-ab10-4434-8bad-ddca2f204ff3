namespace Gallop.SingleModeAutoPlay
{
    public class SummerOutingPopupAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.Instance.GetForefrontDialog();

            if (dialog is DialogCommon dialogCommon)
            {
                var dialogSummerOutingConfirm = dialogCommon.DialogData.ContentsObject.GetComponent<DialogSingleModeSummerOutingConfirm>();
                if (dialogSummerOutingConfirm != null)
                {
                    dialogCommon.DialogData.RightButtonCallBack.Invoke(dialogCommon);
                    return true;
                }
            }

            return false;
        }
    }
}