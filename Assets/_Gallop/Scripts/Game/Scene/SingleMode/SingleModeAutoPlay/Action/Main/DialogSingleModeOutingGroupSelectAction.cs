using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class DialogSingleModeOutingGroupSelectAction : ISingleModeAutoPlayAgentAction
    {
        private readonly AutoPlayOutingParameter _outingParameter;

        public DialogSingleModeOutingGroupSelectAction(AutoPlayOutingParameter outingParameter)
        {
            _outingParameter = outingParameter;
        }

        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.Instance.GetForefrontDialog();
            if (dialog is DialogCommon dialogCommon)
            {
                var outingPartnerSelectDialog = dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeOutingPartnerSelect>();
                if (outingPartnerSelectDialog != null)
                {
                    var item = outingPartnerSelectDialog.ItemList
                        .FirstOrDefault(x => x.GroupCharaId == _outingParameter.GroupCharaId ||
                                             x.IsGroupAll && _outingParameter.IsGroupAll);
                    item.DecideButton.onClick.Invoke();

                    // レース予約など警告がある可能性があるのでステート遷移
                    stateMachine.ChangeSubState<SingleModeDialogOutingPartnerSelectedState>();
                    return true;
                }
            }
            return false;
        }
    }
}