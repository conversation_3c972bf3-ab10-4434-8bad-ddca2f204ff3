using UnityEngine;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModeResultShowRankScoreAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = GameObject.FindObjectOfType<DialogSingleModeResultRankScoreAndFactorSelect>();
            var button = dialog.RankScore.transform.Find("NextButton");

            if (button is { gameObject: { activeInHierarchy: true } })
            {
                button.GetComponent<ButtonCommon>().onClick?.Invoke();
                return true;
            }

            return false;
        }
    }
}