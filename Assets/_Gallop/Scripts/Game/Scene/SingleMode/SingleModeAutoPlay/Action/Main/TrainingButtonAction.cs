using UnityEngine;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 育成TOPでトレーニングボタンを押す
    /// </summary>
    public class TrainingButtonAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var currentView = SceneManager.Instance.GetCurrentViewBase();
            if (currentView is SingleModeMainView mainView && mainView.StablesPanel.CheckAlive()?.AutoPlayProxy != null)
            {
                mainView.StablesPanel.AutoPlayProxy.OnClickTrainingButton();
                var parameter = SingleModeAutoPlayAgent.Instance.Proxy.GetParameter<AutoPlayTrainingActionParameter>();
                stateMachine.ChangeState<SingleModeMainViewTrainingSelectState>(parameter);
                return true;
            }

            return false;
        }
    }
}