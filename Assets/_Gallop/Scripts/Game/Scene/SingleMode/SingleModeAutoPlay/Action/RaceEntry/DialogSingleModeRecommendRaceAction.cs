namespace Gallop.SingleModeAutoPlay
{
    public class DialogSingleModeRecommendRaceAction  : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dc && 
                dc.GetDialogInnerBaseComponent<DialogSingleModeRecommendRace>() is {} dialogSingleModeRecommendRace)
            {
                dialogSingleModeRecommendRace.AutoPlayProxy.SelectCenterPosition();
                dc.Close();
                return true;
            }

            return false;
        }
    }
}