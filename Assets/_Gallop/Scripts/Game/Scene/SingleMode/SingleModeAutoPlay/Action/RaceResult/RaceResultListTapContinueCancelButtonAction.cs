namespace Gallop.SingleModeAutoPlay
{
    public class RaceResultListTapContinueCancelButtonAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            // 自動コンティニュー設定済み かつ 目覚まし時計を持っている場合はキャンセルしない
            if (EnableAutoContinue && EnoughContinueItem) return false;
            if (CanContinue())
            {
                if(DialogManager.Instance.GetDialogInnerComponent<DialogSingleModeRouteContinue>() is { } continueDialog)
                {
                    continueDialog.AutoPlayProxy.OnClickCancel();
                    continueDialog.GetDialog().Close();
                    return true;
                }
            }

            return false;
        }

        private bool CanContinue()
        {
            var viewController = SceneManager.Instance.GetCurrentViewController();
            if (viewController is PaddockViewControllerBase paddockViewController)
            {
                return paddockViewController.AutoPlayProxy.CanContinue();
            }

            if (viewController is RaceMainViewController raceMainViewController)
            {
                return raceMainViewController.AutoPlayProxy.CanContinue();
            }
            
            return false;
        }

        private bool EnableAutoContinue
        {
            get
            {
                var planRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
                return planRecord.IsExecuteRaceContinue;
            }
        }

        private bool EnoughContinueItem => WorkDataManager.Instance.SingleMode.HomeInfo.IsEnableFreeContinue ||
                                           GetContinueItemNum() > 0;

        private int GetContinueItemNum()
        {
            var item = WorkDataManager.Instance.ItemData.GetItemData(GameDefine.NEED_ITEM_ID_FOR_CONTINUE);
            return item?.ItemNum ?? 0;
        }
    }
}