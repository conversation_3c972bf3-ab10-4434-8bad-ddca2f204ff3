namespace Gallop.SingleModeAutoPlay
{
    public class SkipSettingDialogAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.GetForeFrontDialog();

            if (dialog is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeShortCutSetting>() is { })
            {
                dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Center).onClick.Invoke();
                return true;
            }
            return false;
        }
    }
}