namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// お出かけ確認ダイアログから進行する
    /// </summary>
    public class OutingPopupAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.Instance.GetForefrontDialog();
            
            if (dialog is DialogCommon dialogCommon)
            {
                var dialogSimpleCheckWithImage = dialogCommon.GetDialogInnerBaseComponent<DialogSimpleCheckWithImage>();
                if (dialogSimpleCheckWithImage != null)
                {
                    dialogCommon.DialogData.RightButtonCallBack.Invoke(dialogCommon);
                    return true;
                }
            }
            return false;
        }
    }
}