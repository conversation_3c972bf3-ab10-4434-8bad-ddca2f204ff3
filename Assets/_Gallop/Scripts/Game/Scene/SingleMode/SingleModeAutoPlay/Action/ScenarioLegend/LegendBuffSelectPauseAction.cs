namespace Gallop.SingleModeAutoPlay
{
    public class LegendBuffSelectPauseAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.GetForeFrontDialog();

            // 何かしらのダイアログが表示されていれば停止中として扱う
            if (dialog is DialogCommon) return true;

            stateMachine.GetPlaySingleModeStateMachine().ChangeSubState<NotImplState>();
            return false;
        }
    }
}