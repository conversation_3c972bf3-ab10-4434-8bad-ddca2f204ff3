namespace Gallop.SingleModeAutoPlay
{
    public class DialogSingleModeTazunaMessageAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dc &&
                dc.GetDialogInnerBaseComponent<DialogSingleModeTazunaMessage>() is not null)
            {
                dc.DialogData.LeftButtonCallBack.Call(dc);
                dc.Close();
                return true;
            }

            return false;
        }
    }
}