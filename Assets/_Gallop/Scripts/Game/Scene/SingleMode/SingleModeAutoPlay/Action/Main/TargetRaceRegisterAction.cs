using UnityEngine;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 育成TOPで目標レースボタンを押す
    /// </summary>
    public class TargetRaceRegisterAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var currentView = SceneManager.Instance.GetCurrentViewBase();
            if (currentView is SingleModeMainView mainView && mainView.StablesPanel.CheckAlive()?.AutoPlayProxy != null)
            {
                mainView.StablesPanel.AutoPlayProxy.OnClickTargetRaceButton();
                stateMachine.ChangeSubState<SingleModeMainViewClickedRaceButtonState>();
                return true;
            }

            return false;
        }
    }
}