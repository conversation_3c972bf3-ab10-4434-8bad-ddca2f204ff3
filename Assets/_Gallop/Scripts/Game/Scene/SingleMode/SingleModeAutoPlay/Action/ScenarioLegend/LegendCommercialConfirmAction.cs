namespace Gallop.SingleModeAutoPlay
{
    public class LegendCommercialConfirmAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (DialogManager.Instance.GetForefrontDialog() is DialogCommon dialogCommon &&
                dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeScenarioLegendCommercialConfirm>() is { })
            {
                dialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right).onClick?.Invoke();
                return true;
            }

            return false;
        }
    }
}