namespace Gallop.SingleModeAutoPlay
{
    public class OutingButtonAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var currentView = SceneManager.Instance.GetCurrentViewBase();
            if (currentView is SingleModeMainView mainView && mainView.StablesPanel.CheckAlive()?.AutoPlayProxy != null)
            {
                mainView.StablesPanel.AutoPlayProxy.OnClickOutingButton();
                var parameter = SingleModeAutoPlayAgent.Instance.Proxy.GetParameter<AutoPlayOutingParameter>();
                stateMachine.ChangeSubState<SingleModeMainViewClickedOutingButtonState>(parameter);
                return true;
            }

            return false;
        }
    }
}