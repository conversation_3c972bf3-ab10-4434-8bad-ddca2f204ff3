using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class DialogSingleModeOutingPartnerSelectAction : ISingleModeAutoPlayAgentAction
    {
        private readonly AutoPlayOutingParameter _outingParameter;

        public DialogSingleModeOutingPartnerSelectAction(AutoPlayOutingParameter outingParameter)
        {
            _outingParameter = outingParameter;
        }

        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = DialogManager.Instance.GetForefrontDialog();
            if (dialog is DialogCommon dialogCommon)
            {
                var outingPartnerSelectDialog = dialogCommon.GetDialogInnerBaseComponent<DialogSingleModeOutingPartnerSelect>();
                if (outingPartnerSelectDialog != null)
                {
                    var item = outingPartnerSelectDialog.ItemList.FirstOrDefault(x => x.SupportCardId == _outingParameter.SupportCardId);
                    item.DecideButton.onClick.Invoke();
                    if (_outingParameter.IsGroup)
                    {
                        // グループサポカを選択した場合は、誰とのお出かけかを選択するステートに遷移
                        stateMachine.ChangeSubState<SingleModeDialogOutingGroupSelectedState>(_outingParameter);
                    }
                    else
                    {
                        // レース予約など警告がある可能性があるのでステート遷移
                        stateMachine.ChangeSubState<SingleModeDialogOutingPartnerSelectedState>();
                    }
                    return true;
                }
            }
            return false;
        }
    }

    /// <summary>
    /// お出かけ情報
    /// </summary>
    public class AutoPlayOutingParameter : IAutoPlayActionParameter
    {
        /// <summary> 対象のサポカID </summary>
        public int SupportCardId { get; }
        /// <summary> グループサポカのキャラID（グループサポカの場合のみ） </summary>
        public int GroupCharaId { get; }
        /// <summary> グループサポカのキャラ全員とのお出かけが有効か（グループサポカの場合のみ） </summary>
        public bool IsGroupAll { get; }
        /// <summary> 育成キャラのお出かけか </summary>
        public bool IsTrainingChara { get; }

        /// <summary> グループサポカのお出かけ情報か </summary>
        public bool IsGroup => GroupCharaId != 0 || IsGroupAll;

        public AutoPlayOutingParameter(int supportCardId, int groupCharaId, bool isGroupAll, bool isTrainingChara)
        {
            SupportCardId = supportCardId;
            GroupCharaId = groupCharaId;
            IsGroupAll = isGroupAll;
            IsTrainingChara = isTrainingChara;
        }
    }
}