using UnityEngine;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModeResultFactorSelectAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            var dialog = GameObject.FindObjectOfType<DialogSingleModeResultRankScoreAndFactorSelect>();
            var button = dialog.Factor.transform.Find("FooterButtonRoot/NextButton");

            if (button is { gameObject: { activeInHierarchy: true } })
            {
                button.GetComponent<ButtonCommon>().onClick?.Invoke();
                return true;
            }

            return false;
        }
    }
}