namespace Gallop.SingleModeAutoPlay
{
    public class LegendScenarioRaceTapRivalCuttAction : ISingleModeAutoPlayAgentAction
    {
        public bool ExecuteAction(StateMachine stateMachine)
        {
            if (SceneManager.Instance.GetCurrentViewController() is SingleModeScenarioLegendScenarioRaceTopViewController vc)
            {
                //ライバルカット表示を待ってタップする
                if (vc.AutoPlayProxy.TryTapRivalEntryButton())
                {
                    stateMachine.ChangeState<LegendScenarioRaceWaitEntryPaddockState>(); //遷移待ちステートへ遷移
                    return true;
                }
            }
            return false;
        }
    }
}