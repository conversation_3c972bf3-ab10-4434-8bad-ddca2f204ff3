using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public partial class AutoPlayPauseInfo
    {
        /// <summary>
        /// レースコマンドに関する一時停止Model
        /// </summary>
        protected class AutoPlayPauseRaceModel
        {
            private SingleModeRaceReserve.Context RaceReserveContext { get; }
            private IReadOnlyList<int> PauseTriggerTypeList { get; }

            /// <summary> 予約レースの一時停止トリガーが設定されているか </summary>
            protected virtual bool HasRaceReserveTrigger => PauseTriggerTypeList?.Any(x => x == (int)AutoPlayPauseTriggerType.RaceReserve) ?? false;

            /// <summary> 予約レースの一時停止トリガーが有効か </summary>
            protected virtual bool IsRaceReserve => HasRaceReserveTrigger &&
                                                    RaceReserveContext.TurnRepository.IsUpdated &&
                                                    RaceReserveContext.IsCurrentTurnReserved();

            /// <summary> レースコマンドに関する有効な一時停止トリガーが存在するか </summary>
            public virtual bool HasApplicablePauseTrigger => IsRaceReserve;

            public AutoPlayPauseRaceModel(SingleModeRaceReserve.Context raceReserveContext, IReadOnlyList<int> pauseTriggerTypeList)
            {
                RaceReserveContext = raceReserveContext;
                PauseTriggerTypeList = pauseTriggerTypeList;
            }
        }
    }
}