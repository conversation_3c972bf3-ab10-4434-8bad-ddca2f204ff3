using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class AutoPlayRaceActionInfo : IAutoPlayRaceCommand
    {
        #region IAutoPlayRaceCommand
        int IAutoPlayActionInfo.Weight => SingleModeProgramArray?.FirstOrDefault().Weight ?? 0;
        int IAutoPlayActionInfo.TargetCount => 1;

        bool IAutoPlayModifiableFilterActionInfo.IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            return IsMatchCondition(filterRecordList);
        }

        IReadOnlyList<AutoPlayFilterRecord> IAutoPlayModifiableFilterActionInfo.ApplicableFilterRecordList => ApplicableFilterRecordList;

        IReadOnlyList<int> IAutoPlayModifiableFilterActionInfo.UsableCommandIdList
        {
            get
            {
                if (LockCommandIdList == null) return DefaultUsableCommandIdList;
                return DefaultUsableCommandIdList.Except(LockCommandIdList).ToList();
            }
        }

        void IAutoPlayModifiableFilterActionInfo.SetLockCommandIdList(IReadOnlyList<int> lockCommandIdList)
        {
            LockCommandIdList = lockCommandIdList;
        }
        #endregion IAutoPlayRaceCommand

        protected virtual IReadOnlyList<int> DefaultUsableCommandIdList
        {
            get
            {
                var list = new List<int>();
                if (_repository == null) return list;
                list.Add(_repository.GetRaceAutoPlayCommandId());
                return list;
            }
        }

        protected IReadOnlyList<int> LockCommandIdList { get; private set; }
        protected IReadOnlyList<AutoPlayFilterRecord> ApplicableFilterRecordList { get; private set; }

        public MasterSingleModeProgram.SingleModeProgram RaceProgram => SingleModeProgramArray?.FirstOrDefault().RaceProgram;

        /// <summary>
        /// レースに出走できるか
        /// </summary>
        protected bool CanPlayRace(MasterSingleModeProgram.SingleModeProgram race)
        {
            if (race == null || _workCharaData == null) return false;

            // ファン数が足りているならレースに出走できる
            return race.NeedFanCount <= _workCharaData.FanCount;
        }

        private (MasterSingleModeProgram.SingleModeProgram RaceProgram, int Weight)[] SingleModeProgramArray => _cacheSingleModeProgramArray ??= Calculate();
        private (MasterSingleModeProgram.SingleModeProgram RaceProgram, int Weight)[] _cacheSingleModeProgramArray;

        private readonly ICommonSingleModeAutoPlayWeightRepository _repository;

        private readonly WorkSingleModeCharaData _workCharaData;

        protected bool IsLock
        {
            get
            {
                if (LockCommandIdList.IsNullOrEmptyReadOnlyList()) return false;
                return LockCommandIdList.Contains(_repository.GetRaceAutoPlayCommandId());
            }
        }

        public AutoPlayRaceActionInfo(WorkSingleModeCharaData workCharaData, ICommonSingleModeAutoPlayWeightRepository repository)
        {
            _workCharaData = workCharaData;
            _repository = repository;
        }

        protected virtual (MasterSingleModeProgram.SingleModeProgram, int)[] Calculate()
        {
            if (IsLock) return null;

            var turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var raceList = SingleModeRaceEntryViewController.GetProgramList(turn);
            var baseWeight = _repository.GetRaceBaseWeight();
            var preferenceWeight = ApplicableFilterRecordList?.Sum(filterRecord =>
                filterRecord.GetPreferenceWeight(_repository.GetRaceAutoPlayCommandId())) ?? 0;

            return raceList
                .Where(CanPlayRace)
                .Select(race =>
                {
                    var totalAddWeight = TotalAddWeight(race, turn);
                    var totalAddWeightValue = totalAddWeight
                        .GroupBy(x => x.ConditionSetId)
                        .Sum(recordGroup => recordGroup?.Min(record => record.Weight) ?? 0);
                    var totalWeight = baseWeight + totalAddWeightValue + preferenceWeight;
                    return (race, totalWeight);
                })
                .OrderByDescending(x => x.totalWeight)
                .ThenByDescending(x => IsHighProperGrade(x.race))
                .ThenBy(x => x.race.RaceGrade)
                .ToArray();
        }

        /// <summary>
        /// 各重み付けの合算値
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> TotalAddWeight(
            MasterSingleModeProgram.SingleModeProgram race,
            int currentTurn)
        {
            var list = new List<IWeightRecord>();
            list.AddRangeSafely(RaceCommonWeight(race));
            list.AddRangeSafely(ReservedRaceWeight(currentTurn, race.Id));
            list.AddRangeSafely(TargetRaceTurnWeight(race));
            return list;
        }

        /// <summary>
        /// レースの適正での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceCommonWeight(MasterSingleModeProgram.SingleModeProgram race)
        {
            // 目標レースターンの場合はレース適正による重み付けを無効
            if (WorkDataManager.Instance.SingleMode.IsTargetRaceTurn()) return new List<IWeightRecord>();

            // 指定レースの距離・バ場情報を取得
            var distanceAndGround = race.GetRaceDistanceAndGround().Value;
            var distanceType = distanceAndGround.DistanceType;
            var groundType = distanceAndGround.GroundType;
            var raceGrade = race.RaceGrade;

            var list = new List<IWeightRecord>();
            list.AddRangeSafely(RaceGradeWeight(raceGrade));
            list.AddRangeSafely(RaceGradeModifyWeight(raceGrade));
            list.AddRangeSafely(RaceDistanceWeight(distanceType));
            list.AddRangeSafely(RaceDistanceModifyWeight(distanceType));
            list.AddRangeSafely(RaceGroundWeight(groundType));
            list.AddRangeSafely(RaceGroundModifyWeight(groundType));
            return list;
        }

        /// <summary>
        /// レースのグレードでの重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceGradeWeight(RaceDefine.Grade raceGrade)
        {
            var weightRecordList = _repository.GetRaceGradeWeightRecordList();
            return RaceGradeWeight(raceGrade, weightRecordList);
        }

        /// <summary>
        /// ユーザー設定によるレースのグレードでの重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceGradeModifyWeight(RaceDefine.Grade raceGrade)
        {
            var weightRecordList = _repository.GetRaceGradeModifyWeightRecordList();
            return RaceGradeWeight(raceGrade, weightRecordList);
        }

        /// <summary>
        /// レースのグレードでの重みの共通処理
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceGradeWeight(
            RaceDefine.Grade raceGrade,
            IReadOnlyList<IConditionValueRangeRecord> weightRecordList)
        {
            return weightRecordList?
                .Select(x => new ConditionValueRangeWeightModel(x, (int)raceGrade))
                .ToList();
        }
        

        /// <summary>
        /// 指定のProperGrade間の適正である距離種別を返す
        /// </summary>
        protected IEnumerable<RaceDefine.CourseDistanceType> GetTargetProperDistanceTypeList(
            RaceDefine.ProperGrade minGrade,
            RaceDefine.ProperGrade maxOverGrade)
        {
            if (_workCharaData == null) return Enumerable.Empty<RaceDefine.CourseDistanceType>();
            var minProperDistanceTypeList = _workCharaData.GetProperDistanceTypeListByGrade(minGrade);
            var maxOverProperDistanceTypeList = _workCharaData.GetProperDistanceTypeListByGrade(maxOverGrade);
            return minProperDistanceTypeList.Except(maxOverProperDistanceTypeList);
        }

        /// <summary>
        /// 指定のProperGrade間の適正であるバ場種別を返す
        /// </summary>
        protected IEnumerable<RaceDefine.GroundType> GetTargetProperGroundTypeList(
            RaceDefine.ProperGrade minGrade,
            RaceDefine.ProperGrade maxOverGrade)
        {
            if (_workCharaData == null) return Enumerable.Empty<RaceDefine.GroundType>();
            var minProperGroundTypeList = _workCharaData.GetProperGroundTypeListByGrade(minGrade);
            var maxOverProperGroundTypeList = _workCharaData.GetProperGroundTypeListByGrade(maxOverGrade);
            return minProperGroundTypeList.Except(maxOverProperGroundTypeList);
        }

        /// <summary>
        /// 距離適正による重み付け
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceDistanceWeight(RaceDefine.CourseDistanceType distanceType)
        {
            // 距離適性の重み取得
            var distanceWeightRecordList = _repository.GetRaceDistanceWeightRecordList();
            return RaceDistanceWeight(distanceType, distanceWeightRecordList);
        }

        /// <summary>
        /// 距離適正によるユーザ設定での重み付け
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceDistanceModifyWeight(RaceDefine.CourseDistanceType distanceType)
        {
            // 距離適性の重み取得
            var distanceWeightRecordList = _repository.GetRaceDistanceModifyWeightRecordList();
            return RaceDistanceWeight(distanceType, distanceWeightRecordList);
        }

        /// <summary>
        /// 距離適性による重み付け共通処理
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceDistanceWeight(
            RaceDefine.CourseDistanceType distanceType,
            IReadOnlyList<IConditionValueRangeRecord> distanceWeightRecordList)
        {
            return distanceWeightRecordList
                .Select(record =>
                {
                    // NOTE: 例えば適正Bのみを指定する条件の場合、適正A以上は除外したいため maxOver としている
                    var minProperGrade = (RaceDefine.ProperGrade)record.Min;
                    var maxOverProperGrade = (RaceDefine.ProperGrade)(record.Max + 1);

                    var targetProperDistanceTypeList =
                        GetTargetProperDistanceTypeList(minProperGrade, maxOverProperGrade);

                    var conditionSetId = record.ConditionSetId;
                    var weight = targetProperDistanceTypeList.Contains(distanceType) ? record.Weight : 0;
                    return new RaceWeightRecord(conditionSetId, weight);
                })
                .ToList();
        }

        /// <summary>
        /// バ場適正による重み付け
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceGroundWeight(RaceDefine.GroundType groundType)
        {
            // バ場適性の重み取得
            var groundWeightRecordList = _repository.GetRaceGroundWeightRecordList();
            return RaceGroundWeight(groundType, groundWeightRecordList);
        }
        
        /// <summary>
        /// バ場適正によるユーザ設定での重み付け
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceGroundModifyWeight(RaceDefine.GroundType groundType)
        {
            // バ場適性の重み取得
            var groundWeightRecordList = _repository.GetRaceGroundModifyWeightRecordList();
            return RaceGroundWeight(groundType, groundWeightRecordList);
        }

        /// <summary>
        /// バ場適性による重み付け共通処理
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> RaceGroundWeight(
            RaceDefine.GroundType groundType,
            IReadOnlyList<IConditionValueRangeRecord> groundWeightRecordList)
        {
            return groundWeightRecordList
                .Select(record =>
                {
                    // NOTE: 適正Bのみを指定する条件の場合、適正A以上は除外したいため maxOver としている
                    var minProperGrade = (RaceDefine.ProperGrade)record.Min;
                    var maxOverProperGrade = (RaceDefine.ProperGrade)(record.Max + 1);

                    var targetProperGroundTypeList =
                        GetTargetProperGroundTypeList(minProperGrade, maxOverProperGrade);
                    var conditionSetId = record.ConditionSetId;
                    var weight = targetProperGroundTypeList.Contains(groundType) ? record.Weight : 0;
                    return new RaceWeightRecord(conditionSetId, weight);
                })
                .ToList();
        }

        /// <summary>
        /// 予約レースの場合の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ReservedRaceWeight(int currentTurn, int programId)
        {
            if (!IsReservedRace(currentTurn, programId)) return new List<IWeightRecord>();

            var result = new List<IWeightRecord>();
            IReadOnlyList<IWeightRecord> raceReserveWeightRecordList = _repository.GetRaceReserveWeightRecordList();
            result.AddRangeSafely(raceReserveWeightRecordList);
            result.AddRangeSafely(ReservedRaceModifyWeight());
            return result;
        }

        /// <summary>
        /// ユーザー設定による予約レースの場合の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ReservedRaceModifyWeight()
        {
            return _repository.GetRaceReserveModifyWeightRecordList();
        }

        /// <summary>
        /// 指定のレースがこのターンの予約レースか
        /// </summary>
        protected bool IsReservedRace(int currentTurn, int programId)
        {
            var isUpdatedRepository = WorkDataManager.Instance.SingleMode.RaceReserveContext.TurnRepository.IsUpdated;
            if (!isUpdatedRepository) return false;

            var isReservedRace = WorkDataManager.Instance.SingleMode.RaceReserveContext.HasReserved(currentTurn, programId);
            return isReservedRace;
        }

        /// <summary>
        /// 目標レースの場合の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> TargetRaceTurnWeight(MasterSingleModeProgram.SingleModeProgram race)
        {
            var isTargetRaceTurn = WorkDataManager.Instance.SingleMode.IsTargetRaceTurn();
            if (!isTargetRaceTurn) return new List<IWeightRecord>();

            var targetRouteRace = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            if (targetRouteRace == null) return new List<IWeightRecord>();

            if (GetProgramByRoute(targetRouteRace) != race) return new List<IWeightRecord>();

            // 非ターン消費行動との兼ね合いで決め打ちでデータを用意しておく
            const int CONDITION_SET_ID = 0;
            const int WEIGHT = 1;

            return new List<IWeightRecord>() { new RaceWeightRecord(CONDITION_SET_ID, WEIGHT) };
        }

        /// <summary>
        /// 目標レースからレースプログラムを取得
        /// </summary>
        protected virtual MasterSingleModeProgram.SingleModeProgram GetProgramByRoute(
            MasterSingleModeRouteRace.SingleModeRouteRace routeRace)
        {
            var program = routeRace.GetProgram(WorkDataManager.Instance.SingleMode.GetScenarioId());
            return program;
        }

        /// <summary>
        /// 指定のレースへの適正が高いか
        /// </summary>
        protected virtual bool IsHighProperGrade(MasterSingleModeProgram.SingleModeProgram race)
        {
            // 最低限ほしい適正
            const RaceDefine.ProperGrade NEED_PROPER_GRADE = RaceDefine.ProperGrade.B;

            // 指定レースの距離・バ場情報を取得
            var distanceAndGround = race.GetRaceDistanceAndGround().Value;
            var distanceType = distanceAndGround.DistanceType;
            var groundType = distanceAndGround.GroundType;

            var highProperDistanceTypeList = _workCharaData?.GetProperDistanceTypeListByGrade(NEED_PROPER_GRADE);
            var highProperGroundTypeList = _workCharaData?.GetProperGroundTypeListByGrade(NEED_PROPER_GRADE);

            return (highProperDistanceTypeList?.Contains(distanceType) ?? false) && 
                   (highProperGroundTypeList?.Contains(groundType) ?? false);
        }

        /// <summary>
        /// フィルタ情報を適用した場合に適用条件を満たすか
        /// </summary>
        protected virtual bool IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            // 受け取ったフィルタ情報のうち、適用種別と条件を満たすものを格納する
            ApplicableFilterRecordList = filterRecordList?
                .Select(ApplicableFilterRecord)
                .Where(record => record != null)
                .ToList();

            // 適用種別と条件を満たすフィルタ情報があるか返す
            return ApplicableFilterRecordList != null && ApplicableFilterRecordList.Count > 0;
        }

        /// <summary>
        /// フィルタ情報をまとめたModelを作成
        /// </summary>
        protected virtual AutoPlayRaceConditionModel CreateRaceConditionModel(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            return new AutoPlayRaceConditionModel()
            {
                RaceGradeConditionList = GetRaceGradeConditionList(filterConditionList),
                RaceDistanceConditionList = GetRaceDistanceGradeConditionList(filterConditionList),
                RaceGroundConditionList = GetRaceGroundGradeConditionList(filterConditionList),
                RaceReserveConditionList = GetRaceReserveConditionList(filterConditionList),
                NotEnoughFanCountToTargetRaceConditionList = GetNotEnoughFanCountToTargetRaceConditionList(filterConditionList),
                NearNextTargetTurnConditionList = GetNearNextTargetTurnConditionList(filterConditionList),
                NotEnoughFanCountConditionList = GetNotEnoughFanCountConditionList(filterConditionList),
                NotEnoughRaceReserveRaceGradeCountConditionList = GetNotEnoughRaceReserveRaceGradeCountConditionList(filterConditionList),
                MeetConditionRaceGradeTurnConditionList = GetMeetConditionRaceGradeTurnConditionList(filterConditionList),
                NotWinRaceConditionList = GetNotWinRaceConditionList(filterConditionList),
            };
        }

        protected virtual IReadOnlyList<bool?> CreateIsApplicableRaceConditionList(
            int currentTurn,
            MasterSingleModeProgram.SingleModeProgram race,
            AutoPlayRaceConditionModel autoPlayRaceConditionModel)
        {
            var conditionList = new List<bool?>();

            // 指定レースの距離・バ場情報を取得
            var distanceAndGround = race.GetRaceDistanceAndGround().Value;
            var distanceType = distanceAndGround.DistanceType;
            var groundType = distanceAndGround.GroundType;

            conditionList.TryAddValue(IsApplicableRaceGradeCondition(autoPlayRaceConditionModel, race));
            conditionList.TryAddValue(IsApplicableRaceDistanceCondition(autoPlayRaceConditionModel, distanceType));
            conditionList.TryAddValue(IsApplicableRaceGroundCondition(autoPlayRaceConditionModel, groundType));
            conditionList.TryAddValue(IsApplicableRaceReserveCondition(autoPlayRaceConditionModel, currentTurn, race));
            conditionList.TryAddValue(IsApplicableNearNextTargetTurn(autoPlayRaceConditionModel, currentTurn));
            conditionList.TryAddValue(IsApplicableNotEnoughFanCount(autoPlayRaceConditionModel));
            conditionList.TryAddValue(IsApplicableNotEnoughFanCountToTargetRace(autoPlayRaceConditionModel));
            conditionList.TryAddValue(IsApplicableMeetConditionRaceGradeTurn(autoPlayRaceConditionModel, race));
            conditionList.TryAddValue(IsApplicableNotWinRace(autoPlayRaceConditionModel));
            conditionList.TryAddValue(IsApplicableNotEnoughRaceReserveRaceGradeCount(autoPlayRaceConditionModel, currentTurn));

            return conditionList;
        }

        protected virtual AutoPlayFilterRecord ApplicableFilterRecord(AutoPlayFilterRecord autoPlayFilterRecord)
        {
            var filterConditionList = autoPlayFilterRecord.FilterConditionList;
            var raceConditionModel = CreateRaceConditionModel(filterConditionList);

            // 適用種別がそもそも合わない場合は null
            if (raceConditionModel.IsAllNullCondition) return null;

            var turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var raceList = SingleModeRaceEntryViewController.GetProgramList(turn);
            var canPlayRaceList = raceList.Where(CanPlayRace).ToList();

            foreach (var race in canPlayRaceList)
            {
                var isApplicableConditionList = CreateIsApplicableRaceConditionList(turn, race, raceConditionModel);

                if (isApplicableConditionList.IsNullOrEmptyReadOnlyList()) continue;

                // 適用種別が合うものがあるなら、それらがアンド条件として満たされるかを確認
                var isApplicableCondition = isApplicableConditionList.All(isApplicableCondition => isApplicableCondition ?? true);

                // アンド条件として満たされるなら適用種別と条件を満たすフィルタ情報を返す
                if (isApplicableCondition) return autoPlayFilterRecord;
            }

            // どのレースも条件を全て満たせないなら null を返す
            return null;
        }

        /// <summary>
        /// レースのグレードを条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetRaceGradeConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var raceGradeConditionList = _repository?.GetRaceGradeConditionList(filterConditionList);
            if (raceGradeConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return raceGradeConditionList;
        }

        /// <summary>
        /// レースのグレードを条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableRaceGradeCondition(
            AutoPlayRaceConditionModel conditionModel,
            MasterSingleModeProgram.SingleModeProgram race)
        {
            if (conditionModel?.IsNullRaceGradeConditionList ?? true) return null;
            var filterConditionList = conditionModel.RaceGradeConditionList;

            var raceGrade = (int)race.RaceGrade;

            return filterConditionList.All(condition => condition.ConditionValue1 <= raceGrade &&
                                                        condition.ConditionValue2 >= raceGrade);
        }

        /// <summary>
        /// レースの距離適性を条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetRaceDistanceGradeConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var raceDistanceList = _repository?.GetRaceDistanceGradeConditionList(filterConditionList);
            if (raceDistanceList.IsNullOrEmptyReadOnlyList()) return null;
            return raceDistanceList;
        }

        /// <summary>
        /// レースの距離適性を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableRaceDistanceCondition(
            AutoPlayRaceConditionModel autoPlayRaceConditionModel,
            RaceDefine.CourseDistanceType distanceType)
        {
            if (autoPlayRaceConditionModel?.IsNullRaceDistanceConditionList ?? true) return null;

            var filterConditionList = autoPlayRaceConditionModel.RaceDistanceConditionList;

            return filterConditionList.All(condition =>
            {
                // NOTE: 適正Bのみを指定する条件の場合、適正A以上は除外したいため maxOver としている
                var minProperGrade = (RaceDefine.ProperGrade)condition.ConditionValue1;
                var maxOverProperGrade = (RaceDefine.ProperGrade)(condition.ConditionValue2 + 1);

                var targetProperDistanceTypeList = GetTargetProperDistanceTypeList(minProperGrade, maxOverProperGrade);
                return targetProperDistanceTypeList.Contains(distanceType);
            });
        }

        /// <summary>
        /// レースのバ場適性を条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetRaceGroundGradeConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var raceGroundList = _repository?.GetRaceGroundGradeConditionList(filterConditionList);
            if (raceGroundList.IsNullOrEmptyReadOnlyList()) return null;
            return raceGroundList;
        }

        /// <summary>
        /// レースのバ場適性を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableRaceGroundCondition(
            AutoPlayRaceConditionModel autoPlayRaceConditionModel,
            RaceDefine.GroundType groundType)
        {
            if (autoPlayRaceConditionModel?.IsNullRaceGroundConditionList ?? true) return null;
            var filterConditionList = autoPlayRaceConditionModel.RaceGroundConditionList;

            return filterConditionList.All(condition => 
            {
                // NOTE: 適正Bのみを指定する条件の場合、適正A以上は除外したいため maxOver としている
                var minProperGrade = (RaceDefine.ProperGrade)condition.ConditionValue1;
                var maxOverProperGrade = (RaceDefine.ProperGrade)(condition.ConditionValue2 + 1);

                var targetProperGroundTypeList = GetTargetProperGroundTypeList(minProperGrade, maxOverProperGrade);
                return targetProperGroundTypeList.Contains(groundType);
            });
        }

        /// <summary>
        /// 予約レースターンかを条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetRaceReserveConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var raceReserveConditionList = _repository?.GetRaceReserveConditionList(filterConditionList);
            if (raceReserveConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return raceReserveConditionList;
        }

        /// <summary>
        /// 予約レースターンかを条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableRaceReserveCondition(
            AutoPlayRaceConditionModel autoPlayRaceConditionModel,
            int currentTurn,
            MasterSingleModeProgram.SingleModeProgram race)
        {
            if (autoPlayRaceConditionModel?.IsNullRaceReserveConditionList ?? true) return null;

            return IsReservedRace(currentTurn, race.Id);
        }

        /// <summary>
        /// 次の目標レース出走に必要なファン数が足りていないことを条件とするフィルタデータを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetNotEnoughFanCountToTargetRaceConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var notEnoughFanCountToTargetRaceConditionList = _repository?.GetNotEnoughFanCountToTargetRaceConditionList(filterConditionList);
            if (notEnoughFanCountToTargetRaceConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return notEnoughFanCountToTargetRaceConditionList;
        }

        /// <summary>
        /// 次の目標レース出走に必要なファン数が足りていないことを条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableNotEnoughFanCountToTargetRace(AutoPlayRaceConditionModel autoPlayRaceConditionModel)
        {
            if (autoPlayRaceConditionModel?.IsNullNotEnoughFanCountToTargetRaceConditionList ?? true) return null;

            var nextRouteTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            if (nextRouteTarget == null) return false;

            var race = GetProgramByRoute(nextRouteTarget);
            if (race == null) return false;
            if (_workCharaData == null) return false;

            return race.NeedFanCount > _workCharaData.FanCount;
        }

        /// <summary>
        /// 次の目標まで残りNターン以内かを条件とするフィルタデータを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetNearNextTargetTurnConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var nearNextTargetTurnConditionList = _repository?.GetNearNextTargetTurnList(filterConditionList);
            if (nearNextTargetTurnConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return nearNextTargetTurnConditionList;
        }

        /// <summary>
        /// 次の目標まで残りNターン以内かを条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableNearNextTargetTurn(AutoPlayRaceConditionModel autoPlayRaceConditionModel, int currentTurn)
        {
            if (autoPlayRaceConditionModel?.IsNullNearNextTargetTurnConditionList ?? true) return null;

            var filterConditionList = autoPlayRaceConditionModel.NearNextTargetTurnConditionList;
            var nextRouteTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            // 次の目標が無ければ適用条件を満たさない
            if (nextRouteTarget == null) return false;

            var routeRemainTurn = nextRouteTarget.Turn - currentTurn + 1;
            return filterConditionList.All(condition => condition.ConditionValue1 >= routeRemainTurn);
        }

        /// <summary>
        /// 次の目標の必要ファン数に達していない場合を条件とするフィルタデータを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetNotEnoughFanCountConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var notEnoughFanCountConditionList = _repository?.GetNotEnoughFanCountConditionList(filterConditionList);
            if (notEnoughFanCountConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return notEnoughFanCountConditionList;
        }

        /// <summary>
        /// 次の目標の必要ファン数に達していない場合を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableNotEnoughFanCount(AutoPlayRaceConditionModel autoPlayRaceConditionModel)
        {
            if (autoPlayRaceConditionModel?.IsNullNotEnoughFanCountConditionList ?? true) return null;

            var nextTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            if (nextTarget == null) return false;

            if (nextTarget.ConditionType != (int)SingleModeDefine.ConditionType.Fun) return false;
            if (_workCharaData == null) return false;

            return nextTarget.ConditionValue1 > _workCharaData.FanCount;
        }

        /// <summary>
        /// 指定のレースグレードで〇勝する目標を達成できるだけの予約レースが設定されていない場合を条件とするフィルタデータを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetNotEnoughRaceReserveRaceGradeCountConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var notEnoughRaceReserveRaceGradeCountConditionList = _repository?.GetNotEnoughRaceReserveRaceGradeCountConditionList(filterConditionList);
            if (notEnoughRaceReserveRaceGradeCountConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return notEnoughRaceReserveRaceGradeCountConditionList;
        }

        /// <summary>
        /// 指定のレースグレードで〇勝する目標を達成できるだけの予約レースが設定されていない場合を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableNotEnoughRaceReserveRaceGradeCount(AutoPlayRaceConditionModel autoPlayRaceConditionModel, int currentTurn)
        {
            if (autoPlayRaceConditionModel?.IsNullNotEnoughRaceReserveRaceGradeCountConditionList ?? true) return null;

            var nextRouteTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            if (nextRouteTarget == null) return false;

            if (nextRouteTarget.ConditionType != (int)SingleModeDefine.ConditionType.Grade) return false;

            var remainingTargetRaceGradeWinNum = WorkDataManager.Instance.SingleMode.GetRemainingTargetRaceGradeWinNum();

            // 既に目標を達成している場合は適用条件を満たさない
            if (remainingTargetRaceGradeWinNum <= 0) return false;

            var repository = WorkDataManager.Instance.SingleMode.RaceReserveContext.ReserveRepository;
            var activeDeckEntity = repository.GetActiveDeckEntity();

            // 現在のターンから目標レースまでの間に条件を満たすだけの予約レース数が設定されているか確認
            for (int i = currentTurn; i < nextRouteTarget.Turn; i++)
            {
                var entity = activeDeckEntity.FindEntityByTurn(i);

                // 予約レースが無いなら次のターンに進む
                if (entity == null) continue;

                // レースグレードが目標のグレード以上なら、残り回数を減らす
                var raceGrade = entity.Program.RaceGrade;
                if (nextRouteTarget.ConditionId >= (int)raceGrade)
                {
                    remainingTargetRaceGradeWinNum--;
                }

                // 目標数を満たすだけの予約レースが設定されているなら適用条件を満たさない
                if (remainingTargetRaceGradeWinNum <= 0) return false;
            }

            return true;
        }

        /// <summary>
        /// 現在のターンが指定のレースグレードで〇勝する目標条件を満たす場合を条件とするフィルタデータを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetMeetConditionRaceGradeTurnConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var meetConditionRaceGradeTurnConditionList = _repository?.GetMeetConditionRaceGradeTurnConditionList(filterConditionList);
            if (meetConditionRaceGradeTurnConditionList == null || meetConditionRaceGradeTurnConditionList.Count <= 0) return null;
            return meetConditionRaceGradeTurnConditionList;
        }

        /// <summary>
        /// 現在のターンが指定のレースグレードで〇勝する目標条件を満たす場合を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableMeetConditionRaceGradeTurn(AutoPlayRaceConditionModel autoPlayRaceConditionModel,
            MasterSingleModeProgram.SingleModeProgram race)
        {
            if (autoPlayRaceConditionModel?.IsNullMeetConditionRaceGradeTurnConditionList ?? true) return null;

            var nextRouteTarget = WorkDataManager.Instance.SingleMode.GetNextRouteTarget();

            if (nextRouteTarget == null) return false;

            if (nextRouteTarget.ConditionType != (int)SingleModeDefine.ConditionType.Grade) return false;

            return nextRouteTarget.ConditionId >= (int)race.RaceGrade;
        }

        /// <summary>
        /// 未勝利状態を条件とするフィルタデータを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetNotWinRaceConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var notWinRaceConditionList = _repository?.GetNotWinRaceConditionList(filterConditionList);
            if (notWinRaceConditionList == null || notWinRaceConditionList.Count <= 0) return null;
            return notWinRaceConditionList;
        }

        /// <summary>
        /// 未勝利状態を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableNotWinRace(AutoPlayRaceConditionModel autoPlayRaceConditionModel)
        {
            if (autoPlayRaceConditionModel?.IsNullNotWinRaceConditionList ?? true) return null;

            if (_workCharaData == null) return false;

            return _workCharaData.CharaGrade == SingleModeDefine.CharaGradeType.NoWin;
        }

        /// <summary>
        /// レース用の重みデータ
        /// </summary>
        protected class RaceWeightRecord : IWeightRecord
        {
            public int ConditionSetId { get; }
            public int Weight { get; }

            public RaceWeightRecord(int conditionSetId, int weight)
            {
                ConditionSetId = conditionSetId;
                Weight = weight;
            }
        }

        /// <summary>
        /// レース用の行動制限データ
        /// </summary>
        protected class AutoPlayRaceConditionModel
        {
            /// <summary> レースのグレードを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> RaceGradeConditionList;
            public bool IsNullRaceGradeConditionList => RaceGradeConditionList == null;

            /// <summary> レースの距離適性を条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> RaceDistanceConditionList;
            public bool IsNullRaceDistanceConditionList => RaceDistanceConditionList == null;

            /// <summary> レースのバ場適性を条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> RaceGroundConditionList;
            public bool IsNullRaceGroundConditionList => RaceGroundConditionList == null;

            /// <summary> 予約レースを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> RaceReserveConditionList;
            public bool IsNullRaceReserveConditionList => RaceReserveConditionList == null;

            /// <summary> 目標レースの出走に必要なファン数が足りないことを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> NotEnoughFanCountToTargetRaceConditionList;
            public bool IsNullNotEnoughFanCountToTargetRaceConditionList => NotEnoughFanCountToTargetRaceConditionList == null;

            /// <summary> 目標までの残りNターンを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> NearNextTargetTurnConditionList;
            public bool IsNullNearNextTargetTurnConditionList => NearNextTargetTurnConditionList == null;

            /// <summary> 目標のファン数が足りないことを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> NotEnoughFanCountConditionList;
            public bool IsNullNotEnoughFanCountConditionList => NotEnoughFanCountConditionList == null;

            /// <summary> 指定のレースグレードで〇勝する目標を達成できるだけの予約レースが設定されていないことを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> NotEnoughRaceReserveRaceGradeCountConditionList;
            public bool IsNullNotEnoughRaceReserveRaceGradeCountConditionList => NotEnoughRaceReserveRaceGradeCountConditionList == null;

            /// <summary> 現在のターンが指定のレースグレードで〇勝する目標条件を満たすことを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> MeetConditionRaceGradeTurnConditionList;
            public bool IsNullMeetConditionRaceGradeTurnConditionList => MeetConditionRaceGradeTurnConditionList == null;

            /// <summary> レースに未勝利なことを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> NotWinRaceConditionList;
            public bool IsNullNotWinRaceConditionList => NotWinRaceConditionList == null;

            /// <summary> レースに関連するフィルタ情報が存在しないか </summary>
            public virtual bool IsAllNullCondition => IsNullRaceGradeConditionList &&
                                                      IsNullRaceDistanceConditionList &&
                                                      IsNullRaceGroundConditionList &&
                                                      IsNullRaceReserveConditionList &&
                                                      IsNullNearNextTargetTurnConditionList &&
                                                      IsNullNotEnoughFanCountConditionList &&
                                                      IsNullNotEnoughFanCountToTargetRaceConditionList &&
                                                      IsNullNotEnoughRaceReserveRaceGradeCountConditionList &&
                                                      IsNullMeetConditionRaceGradeTurnConditionList &&
                                                      IsNullNotWinRaceConditionList;
        }
    }
}