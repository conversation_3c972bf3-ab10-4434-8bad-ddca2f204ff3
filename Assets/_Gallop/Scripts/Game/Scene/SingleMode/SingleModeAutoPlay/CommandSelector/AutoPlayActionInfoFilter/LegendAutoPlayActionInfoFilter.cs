using Gallop.SingleMode.ScenarioLegend;

namespace Gallop.SingleModeAutoPlay
{
    public class LegendAutoPlayActionInfoFilter : AutoPlayActionInfoFilter
    {
        protected override bool IsUsableTraining(AutoPlayTrainingActionInfo.AutoPlayTrainingRecord trainingRecord)
        {
            // NOTE: スピードシンボリのスポ根ゾーン適用中はトレーニングを失敗しない
            if (MasterlyBonusModelRepository.TryGet<MasterlyBonus9047Model>(out var masterlyBonus))
            {
                if (masterlyBonus.IsZoneCondition) return true;
            }

            return base.IsUsableTraining(trainingRecord);
        }

        protected override bool CustomIsUsableConsumptionCommand(IAutoPlayActionInfo actionInfo)
        {
            // NOTE: スピードシンボリのスポ根ゾーン適用中はお出かけ・お休みを行わない
            if (MasterlyBonusModelRepository.TryGet<MasterlyBonus9047Model>(out var masterlyBonus))
            {
                // スポ根ゾーン適用中ではないなら制限しない
                if (!masterlyBonus.IsZoneCondition) return true;
                return actionInfo is not IAutoPlayOutingCommand && actionInfo is not IAutoPlayHolidayCommand;
            }

            return true;
        }
    }
}