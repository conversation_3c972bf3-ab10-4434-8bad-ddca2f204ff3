using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class AutoPlayTrainingActionInfoSelectedSkillContext
    {
        /// <summary>
        /// 選択している優先スキルのヒントを獲得する可能性があるトレーニングコマンドの重み付け
        /// </summary>
        public virtual IReadOnlyList<IWeightRecord> CalculateSelectedSkillGettableTipsTrainingWeight(
            IReadOnlyList<IConditionValueRangeRecord> weightRecordList,
            IReadOnlyList<int> selectedSkillIdList,
            IReadOnlyList<int> hasTipsSupportCardIdList,
            WorkSingleModeCharaData workCharaData)
        {
            // サポカIDのリストから、ヒントを取得できるスキルIDのリストを取得
            var hintGainSkillIdList = HintGainForSkillIdList(hasTipsSupportCardIdList);

            // 取得済みのスキル一覧
            var acquiredSkillList = workCharaData.AcquiredSkillList;

            // ヒントが取得できる未取得スキルのIDリストを取得
            var targetSkillIdList = GettableSkillIdList(acquiredSkillList, selectedSkillIdList, hintGainSkillIdList);

            return CalculateSelectedSkillGettableTipsTrainingWeight(weightRecordList, targetSkillIdList, workCharaData);
        }

        protected virtual IReadOnlyList<int> HintGainForSkillIdList(IReadOnlyList<int> hasTipsSupportCardIdList)
        {
            return hasTipsSupportCardIdList
                .SelectMany(supportCardId =>
                {
                    var masterSupportCardData = MasterDataManager.Instance.masterSupportCardData.Get(supportCardId);
                    var masterHintGainList = MasterDataManager.Instance.masterSingleModeHintGain
                        .GetListWithHintIdOrderByIdAsc(masterSupportCardData.SkillSetId);

                    // スキルのヒントイベントが存在しないサポカなら空のリストを返す
                    if (masterHintGainList.IsNullOrEmpty()) return Enumerable.Empty<int>();

                    // サポカが持っているスキルのIDリストを返す
                    return masterHintGainList
                        .Where(masterHintGain => masterHintGain.HintGainType == 0 &&
                                                 masterHintGain.SupportCardId == supportCardId)
                        .Select(masterHintGain => masterHintGain.HintValue1)
                        .ToList();
                })
                .Distinct()
                .ToList();
        }

        protected virtual IReadOnlyList<int> GettableSkillIdList(
            IReadOnlyList<WorkSkillData.AcquiredSkill> acquiredSkillList,
            IReadOnlyList<int> selectedSkillIdList,
            IReadOnlyList<int> hintGainSkillIdList)
        {
            return selectedSkillIdList
                .Where(skillId =>
                {
                    var isAcquired = IsAcquiredSkill(skillId, acquiredSkillList);
                    if (isAcquired) return false;

                    return hintGainSkillIdList.Contains(skillId);
                })
                .ToList();
        }

        protected virtual IReadOnlyList<IWeightRecord> CalculateSelectedSkillGettableTipsTrainingWeight(
            IReadOnlyList<IConditionValueRangeRecord> weightRecordList,
            IReadOnlyList<int> targetSkillIdList, 
            WorkSingleModeCharaData workCharaData)
        {
            // 対象のスキルのヒントレベルが条件を満たすものが存在すれば重みデータを返す
            return weightRecordList
                .Select(weightRecord =>
                {
                    return targetSkillIdList
                        .Select(targetSkillId =>
                        {
                            // NOTE: Weightの値を参照したいため、明示的にインターフェースをreturnする
                            IWeightRecord weightModel = new ConditionValueRangeWeightModel(weightRecord, workCharaData.GetTipsSkillLevel(targetSkillId));
                            return weightModel;
                        })
                        .FirstOrDefault(weightModel => weightModel.Weight > 0);
                })
                .RemoveNull()
                .ToList();
        }

        /// <summary>
        /// 対象のスキルを獲得済みか
        /// </summary>
        protected bool IsAcquiredSkill(int skillId, IReadOnlyList<WorkSkillData.AcquiredSkill> acquiredSkillList)
        {
            var targetSkillData = MasterDataManager.Instance.masterSkillData.Get(skillId);
            // 同じレアリティのスキルの最上位スキルを検索
            var sameGroupHighestRankSkill = MasterDataManager.Instance.masterSkillData
                .GetListWithGroupIdOrderByIdAsc(targetSkillData.GroupId)
                .Where(x => x.Rarity == targetSkillData.Rarity)
                .OrderByDescending(x => x.GroupRate)
                .FirstOrDefault();

            // 対象スキルが同じレアリティのスキルの中で最上位のスキルか
            var isTargetSkillHighest = sameGroupHighestRankSkill.Id == skillId;

            return acquiredSkillList.Any(acquiredSkill =>
            {
                // レアリティの異なる上位スキルを持っている（例: 「右回りの鬼」を所持していたら「右回り○」も持っている）
                var hasSameGroupHigherRankRareSkillThan = acquiredSkill.MasterData.IsSameGroupHigherRankSkillThan(sameGroupHighestRankSkill);
                if (hasSameGroupHigherRankRareSkillThan) return true;

                // 同じレアリティの最上位スキルを持っている（例: 「右回り◎」を所持していたら「右回り○」も持っている）
                var hasSameGroupHighestRankSkillThen = acquiredSkill.MasterId == sameGroupHighestRankSkill.Id;
                if (hasSameGroupHighestRankSkillThen) return true;

                // 同じレアリティのスキルの中で自身が最上位で、そのスキルを持っている
                // NOTE: 対象スキルが最上位スキルではない場合、ヒントイベントを獲得したいスキルとして判定できるようにしている
                //       例: 「右回り○」を持っていても「右回り◎」が同レアリティでの最上位スキルのため、「右回り○」のヒントは獲得したいものとする
                var isAcquiredTargetSkill = acquiredSkill.MasterId == skillId;
                return isAcquiredTargetSkill && isTargetSkillHighest;
            });
        }

        /// <summary>
        /// 選択している優先スキルのヒントを獲得する可能性があるトレーニングコマンドの重み付け
        /// </summary>
        public virtual IReadOnlyList<IWeightRecord> CalculateSelectedSkillGettableTipsAndNotHaveWeight(
            IReadOnlyList<IConditionValueEmptyRecord> weightRecordList,
            IReadOnlyList<int> selectedSkillIdList,
            IReadOnlyList<int> hasTipsSupportCardIdList,
            WorkSingleModeCharaData workCharaData)
        {
            // サポカIDのリストから、ヒントを取得できるスキルIDのリストを取得
            var hintGainSkillIdList = HintGainForSkillIdList(hasTipsSupportCardIdList);

            // ヒントが取得できる未取得スキルのIDリストを取得
            var targetSkillIdList = GettableAndNotHaveHintSkillIdList(workCharaData, selectedSkillIdList, hintGainSkillIdList);

            if (targetSkillIdList.IsNullOrEmptyReadOnlyList()) return null;

            return weightRecordList;
        }

        protected virtual IReadOnlyList<int> GettableAndNotHaveHintSkillIdList(
            WorkSingleModeCharaData workCharaData,
            IReadOnlyList<int> selectedSkillIdList,
            IReadOnlyList<int> hintGainSkillIdList)
        {
            // 取得済みのスキル一覧
            var acquiredSkillList = workCharaData.AcquiredSkillList;

            return selectedSkillIdList
                .Where(skillId =>
                {
                    var targetSkillData = MasterDataManager.Instance.masterSkillData.Get(skillId);

                    if (workCharaData.GetSkillTips(targetSkillData) != null) return false;

                    var isAcquired = IsAcquiredSkill(skillId, acquiredSkillList);
                    if (isAcquired) return false;

                    return hintGainSkillIdList.Contains(skillId);
                })
                .ToList();
        }
    }
}