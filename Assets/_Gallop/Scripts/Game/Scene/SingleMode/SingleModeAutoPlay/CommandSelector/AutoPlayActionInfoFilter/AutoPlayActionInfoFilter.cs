using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class AutoPlayActionInfoFilter
    {
        /// <summary> 失敗率を増加させる効果のID種別 </summary>
        private enum PoorTrainingCharaEffectIdType
        {
            Common = 6, // 練習下手
            Creek = 12, // 固有効果
        }

        /// <summary> 効果によって増加する失敗率 </summary>
        private const int POOR_TRAINING_FAILURE_RATE = 2;

        /// <summary>
        /// 指定のトレーニングコマンドを実行できるか
        /// </summary>
        protected virtual bool IsUsableTraining(AutoPlayTrainingActionInfo.AutoPlayTrainingRecord autoPlayTrainingRecord)
        {
            if (autoPlayTrainingRecord == null) return false;

            var trainingFailureRate = autoPlayTrainingRecord.TrainingFailureRate;
            // 失敗率が 0% 以下なら実行できる
            if (trainingFailureRate <= 0) return true;

            // キャラが持っているトレーニング失敗率増加効果分を無視して、失敗率が 0% 以下なら実行できることにする
            return TrainingFailureRateIgnoreCharaEffect(trainingFailureRate, autoPlayTrainingRecord.WorkCharaData) <= 0;
        }

        /// <summary>
        /// キャラが持っている失敗率増加効果分を無視した失敗率を返す
        /// </summary>
        protected virtual int TrainingFailureRateIgnoreCharaEffect(int trainingFailureRate, WorkSingleModeCharaData workCharaData)
        {
            var characterEffectIdArray = workCharaData.CharaEffectIdArray;
            var poorTrainingCharaEffectIdTypeArray = EnumUtil.GetEnumArray<PoorTrainingCharaEffectIdType>();
            var poorTrainingCharaEffectCount = poorTrainingCharaEffectIdTypeArray
                .Count(effectIdType => characterEffectIdArray.Contains((int)effectIdType));
            return trainingFailureRate - POOR_TRAINING_FAILURE_RATE * poorTrainingCharaEffectCount;
        }

        /// <summary>
        /// シナリオごとの特有の行動使用可否判定
        /// </summary>
        protected virtual bool CustomIsUsableConsumptionCommand(IAutoPlayActionInfo actionInfo)
        {
            return true;
        }

        /// <summary>
        /// 使用可能なトレーニングコマンドのみに絞り込む
        /// </summary>
        public virtual IReadOnlyList<AutoPlayTrainingActionInfo.AutoPlayTrainingRecord> TrainingCommandFilter(
            IReadOnlyList<AutoPlayTrainingActionInfo.AutoPlayTrainingRecord> trainingRecordList)
        {
            return trainingRecordList?.Where(IsUsableTraining).ToList();
        }

        /// <summary>
        /// 行動に紐づくCommandTypeを取得
        /// </summary>
        protected virtual SingleModeDefine.CommandType GetConsumptionCommand(IAutoPlayActionInfo actionInfo)
        {
            var commandType = SingleModeAutoPlayUtils.GetConsumptionCommand(actionInfo);
            return commandType != SingleModeDefine.CommandType.None ? commandType : GetCustomConsumptionCommandType(actionInfo);
        }

        /// <summary>
        /// 行動に紐づくシナリオ固有のCommandTypeを取得（デフォルトはNone）
        /// </summary>
        protected virtual SingleModeDefine.CommandType GetCustomConsumptionCommandType(IAutoPlayActionInfo actionInfo)
        {
            return SingleModeDefine.CommandType.None;
        }

        /// <summary>
        /// 指定の行動に紐づくターン消費コマンドが実行できるか
        /// </summary>
        protected virtual bool IsEnableConsumptionCommand(IAutoPlayActionInfo actionInfo)
        {
            var consumptionCommandType = GetConsumptionCommand(actionInfo);
            return WorkDataManager.Instance.SingleMode.HomeInfo.IsEnable(consumptionCommandType);
        }

        /// <summary>
        /// 使用可能なターン消費行動のみに絞り込む
        /// </summary>
        public IReadOnlyList<(IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Generate)> ConsumptionCommandFilter(
            IEnumerable<(IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Generate)> actionInfoAndGenerateEnumerable)
        {
            return actionInfoAndGenerateEnumerable?.Where(actionInfoAndGenerate =>
            {
                var actionInfo = actionInfoAndGenerate.ActionInfo;

                // ターン消費行動でこのターンに実行できるか
                var isUsableCommand = IsEnableConsumptionCommand(actionInfo);
                // シナリオごとの条件で実行できるか
                var customIsUsable = CustomIsUsableConsumptionCommand(actionInfo);

                // 全ての条件を満たしていたら使用可能
                return isUsableCommand &&
                       customIsUsable;
            }).ToList();
        }
    }
}