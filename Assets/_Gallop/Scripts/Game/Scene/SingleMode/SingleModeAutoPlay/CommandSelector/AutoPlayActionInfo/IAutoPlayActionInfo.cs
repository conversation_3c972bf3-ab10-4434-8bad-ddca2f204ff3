using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public interface IAutoPlayActionInfo
    {
        /// <summary> 行動が選ばれるかどうかの重み </summary>
        public int Weight { get; }
        public int TargetCount { get; }
    }

    public interface IAutoPlayModifiableFilterActionInfo
    {
        /// <summary> 行動制限データの適用条件を満たすか </summary>
        public bool IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList);

        /// <summary> 適用条件を満たした行動制限データリスト </summary>
        public IReadOnlyList<AutoPlayFilterRecord> ApplicableFilterRecordList { get; }

        /// <summary> 使用可能なCommandIdのリスト </summary>
        public IReadOnlyList<int> UsableCommandIdList { get; }

        /// <summary> 指定のCommandIdを塞ぐ </summary>
        public void SetLockCommandIdList(IReadOnlyList<int> lockCommandIdList);
    }

    public interface INonConsumptionAutoPlayActionInfo : IAutoPlayActionInfo { }

    /// <summary> レースコマンドに関するAutoPlayActionInfo </summary>
    public interface IAutoPlayRaceCommand : IAutoPlayActionInfo, IAutoPlayModifiableFilterActionInfo { }

    /// <summary> トレーニングコマンドに関するAutoPlayActionInfo </summary>
    public interface IAutoPlayTrainingCommand : IAutoPlayActionInfo, IAutoPlayModifiableFilterActionInfo
    {
#if CYG_DEBUG
        IReadOnlyList<(TrainingDefine.TrainingCommandId TrainingCommandId, int Weight)> DebugWeightList { get; }
        TrainingDefine.TrainingCommandId DebugHighestTrainingCommandId { get; }
#endif
    }

    /// <summary> お休みコマンドに関するAutoPlayActionInfo </summary>
    public interface IAutoPlayHolidayCommand : IAutoPlayActionInfo, IAutoPlayModifiableFilterActionInfo { }

    /// <summary> お出かけコマンドに関するAutoPlayActionInfo </summary>
    public interface IAutoPlayOutingCommand : IAutoPlayActionInfo, IAutoPlayModifiableFilterActionInfo { }

    /// <summary> 保健室コマンドに関するAutoPlayActionInfo </summary>
    public interface IAutoPlayHospitalCommand : IAutoPlayActionInfo, IAutoPlayModifiableFilterActionInfo { }
}