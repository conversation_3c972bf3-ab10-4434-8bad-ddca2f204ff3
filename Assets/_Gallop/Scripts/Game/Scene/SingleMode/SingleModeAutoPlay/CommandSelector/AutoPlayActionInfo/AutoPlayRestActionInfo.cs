using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// お休みコマンドを実行するかの重み付け行う
    /// </summary>
    public abstract class AutoPlayRestActionInfoBase : IAutoPlayActionInfo, IAutoPlayModifiableFilterActionInfo
    {
        private const int DEFAULT_WEIGHT = -1;
        protected readonly ICommonSingleModeAutoPlayWeightRepository _repository;

        #region IAutoPlayActionInfo
        int IAutoPlayActionInfo.Weight
        {
            get
            {
                if (_cacheWeight == DEFAULT_WEIGHT) _cacheWeight = CalculateWeight();
                return _cacheWeight;
            }
        }
        int IAutoPlayActionInfo.TargetCount => 1;
        #endregion IAutoPlayActionInfo

        #region IAutoPlayModifiableFilterActionInfo

        bool IAutoPlayModifiableFilterActionInfo.IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            return IsMatchCondition(filterRecordList);
        }

        IReadOnlyList<AutoPlayFilterRecord> IAutoPlayModifiableFilterActionInfo.ApplicableFilterRecordList => ApplicableFilterRecordList;

        IReadOnlyList<int> IAutoPlayModifiableFilterActionInfo.UsableCommandIdList
        {
            get
            {
                if (LockCommandIdList == null) return DefaultUsableCommandIdList;
                return DefaultUsableCommandIdList.Except(LockCommandIdList).ToList();
            }
        }

        void IAutoPlayModifiableFilterActionInfo.SetLockCommandIdList(IReadOnlyList<int> lockCommandIdList)
        {
            LockCommandIdList = lockCommandIdList;
        }
        #endregion IAutoPlayModifiableFilterActionInfo

        protected virtual IReadOnlyList<int> DefaultUsableCommandIdList
        {
            get
            {
                var list = new List<int>();
                if (_repository == null) return list;
                list.Add(_repository.GetRestAutoPlayCommandId());
                return list;
            }
        }

        protected IReadOnlyList<int> LockCommandIdList { get; private set; }
        protected IReadOnlyList<AutoPlayFilterRecord> ApplicableFilterRecordList { get; private set; }

        private int _cacheWeight = DEFAULT_WEIGHT;

        protected readonly WorkSingleModeCharaData _workCharaData;

        protected virtual int BaseWeight => _repository?.GetHolidayBaseWeight() ?? 0;
        protected virtual IReadOnlyList<IConditionValueRangeRecord> WeightRecordList => _repository?.GetHolidayWeightRecordList();
        protected virtual IReadOnlyList<IConditionValueRangeRecord> ModifyWeightRecordList => _repository?.GetHolidayModifyWeightRecordList();

        protected virtual IReadOnlyList<IConditionFilterRecord> GetRestConditionList(IReadOnlyList<IConditionFilterRecord> filterRecordList)
        {
            return _repository?.GetHpConditionList(filterRecordList);
        }

        protected bool IsLock
        {
            get
            {
                if (LockCommandIdList.IsNullOrEmptyReadOnlyList()) return false;
                return LockCommandIdList.Contains(_repository.GetRestAutoPlayCommandId());
            }
        }

        public AutoPlayRestActionInfoBase(WorkSingleModeCharaData workCharaData, ICommonSingleModeAutoPlayWeightRepository repository)
        {
            _workCharaData = workCharaData;
            _repository = repository;
        }

        protected virtual int CalculateWeight()
        {
            if (IsLock) return 0;

            // 残りの体力から重みを計算する
            var hp = _workCharaData?.Hp ?? 0;

            var totalAddWeight = TotalAddWeight(hp);
            var totalAddWeightValue = totalAddWeight?
                .GroupBy(x => x.ConditionSetId)
                .Sum(recordGroup => recordGroup?.Min(record => record.Weight) ?? 0)
                ?? 0;

            var preferenceWeight = ApplicableFilterRecordList?.Sum(filterRecord =>
                filterRecord.GetPreferenceWeight(_repository.GetRestAutoPlayCommandId())) ?? 0;

            var totalWeight = BaseWeight + totalAddWeightValue + preferenceWeight;
            return totalWeight;
        }

        protected virtual IReadOnlyList<IWeightRecord> TotalAddWeight(int hp)
        {
            var result = new List<IWeightRecord>();
            result.AddRangeSafely(RestWeight(hp));
            result.AddRangeSafely(RestModifyWeight(hp));
            return result;
        }

        protected virtual IReadOnlyList<IWeightRecord> RestWeight(int hp)
        {
            return RestRecordWeight(hp, WeightRecordList);
        }

        protected virtual IReadOnlyList<IWeightRecord> RestModifyWeight(int hp)
        {
            return RestRecordWeight(hp, ModifyWeightRecordList);
        }

        protected virtual IReadOnlyList<IWeightRecord> RestRecordWeight(int hp,
            IReadOnlyList<IConditionValueRangeRecord> recordList)
        {
            return recordList.Select(x => new ConditionValueRangeWeightModel(x, hp)).ToList();
        }

        /// <summary>
        /// フィルタ情報を適用した場合に適用条件を満たすか
        /// </summary>
        protected virtual bool IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            // 受け取ったフィルタ情報のうち、適用種別と条件を満たすものを格納する
            ApplicableFilterRecordList = filterRecordList?
                .Where(record =>
                {
                    if (record == null) return false;
                    var filterConditionList = record.FilterConditionList;

                    var restConditionList = GetRestConditionList(filterConditionList);
                    if (restConditionList.IsNullOrEmptyReadOnlyList()) return false;

                    var isApplicableCondition = restConditionList
                        .All(condition => condition.ConditionValue1 <= _workCharaData.Hp &&
                                          condition.ConditionValue2 >= _workCharaData.Hp);
                    return isApplicableCondition;
                })
                .ToList();

            // 適用種別と条件を満たすフィルタ情報があるか返す
            return ApplicableFilterRecordList?.Any() ?? false;
        }
    }

    /// <summary>
    /// お休みコマンドを実行するかの重み付け行う
    /// </summary>
    public class AutoPlayHolidayActionInfo : AutoPlayRestActionInfoBase, IAutoPlayHolidayCommand
    {
        public AutoPlayHolidayActionInfo(WorkSingleModeCharaData workCharaData, ICommonSingleModeAutoPlayWeightRepository repository)
            : base(workCharaData, repository)
        {
        }
    }

    /// <summary>
    /// 夏合宿のお休みコマンドを実行するかの重み付け行う
    /// </summary>
    public class AutoPlayCampOutingActionInfo : AutoPlayRestActionInfoBase, IAutoPlayOutingCommand
    {
        protected override int BaseWeight => _repository?.GetSummerCampBaseWeight() ?? 0;
        protected override IReadOnlyList<IConditionValueRangeRecord> WeightRecordList => _repository?.GetSummerCampOutingWeightRecordList();
        protected override IReadOnlyList<IConditionValueRangeRecord> ModifyWeightRecordList => _repository?.GetSummerCampOutingModifyWeightRecordList();

        public AutoPlayCampOutingActionInfo(WorkSingleModeCharaData workCharaData, ICommonSingleModeAutoPlayWeightRepository repository)
            : base(workCharaData, repository)
        {
        }
    }
}