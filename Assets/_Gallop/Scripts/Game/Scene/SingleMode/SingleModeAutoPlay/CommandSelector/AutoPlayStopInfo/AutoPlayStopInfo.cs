using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class AutoPlayStopInfo
    {
        /// <summary>
        /// ストーリーイベントでおまかせ育成を終了する必要があるか
        /// </summary>
        public bool NeedAutoPlayStopByStopperEvent(int storyId)
        {
            var master = MasterDataManager.Instance.masterOmakaseStopperEvent.GetWithStoryId(storyId);
            var currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
            if (currentPlanRecord == null || master == null) return false;

            var isStop = master.StopType == 0;
            if (!isStop) return false;

            // 終了をユーザーが切り替えられるイベントでなければ、強制的に終了させる
            var isModifiable = master.IsModifiable == 1;
            if (!isModifiable) return true;

            // 現在のプランで終了をするイベントのIDリストに含まれるなら終了する必要がある
            var storyStopperIdList = currentPlanRecord.ModifyStoryStopperIdList;
            return storyStopperIdList?.Contains(master.StopperId) ?? false;
        }
    }
}