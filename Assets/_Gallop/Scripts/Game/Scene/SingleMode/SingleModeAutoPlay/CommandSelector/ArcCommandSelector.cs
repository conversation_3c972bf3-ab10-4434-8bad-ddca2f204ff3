using System;
using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 凱旋門賞編の育成コマンド・トレーニングコマンドの選択ロジック
    /// </summary>
    public class ArcCommandSelector : CommandSelectorBase
    {
        protected override (IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Func) SelectAction()
        {
            throw new NotImplementedException();
        }

        protected override ISingleModeAutoPlayAgentAction SelectNonConsumptionAction(IAutoPlayActionInfo selectedActionInfo)
        {
            throw new NotImplementedException();
        }

        protected override ISingleModeAutoPlayAgentAction SelectTraining(AutoPlayTrainingActionParameter parameter)
        {
            throw new System.NotImplementedException();
        }
#if CYG_DEBUG
        protected override IReadOnlyList<IAutoPlayActionInfo> DebugGetActionInfoList()
        {
            throw new NotImplementedException();
        }
#endif
    }
}