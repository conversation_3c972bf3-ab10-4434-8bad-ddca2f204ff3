using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class AutoPlayOutingTrainingCharaActionInfo : AutoPlayOutingActionInfo
    {
        public AutoPlayOutingTrainingCharaActionInfo(int characterHp, ICommonSingleModeAutoPlayWeightRepository weightRepository)
            : base(characterHp, weightRepository, null, null, null)
        {
        }

        /// <summary> 対象のサポカID </summary>
        public override int SupportCardId => 0;
        /// <summary> グループサポカのキャラID（グループサポカの場合のみ） </summary>
        public override int GroupCharaId => 0;
        /// <summary> グループサポカのキャラ全員とのお出かけが有効か（グループサポカの場合のみ） </summary>
        public override bool IsGroupAll => false;
        /// <summary> 育成キャラのお出かけか </summary>
        public override bool IsTrainingChara => true;

        /// <summary> おまかせ育成用CommandId </summary>
        protected override int AutoPlayCommandId => _weightRepository?.GetOutingTrainingCharaAutoPlayCommandId() ?? 0;
        /// <summary> 基礎重み </summary>
        protected override int BaseWeight => _weightRepository?.GetOutingTrainingCharaBaseWeight() ?? 0;

        protected override OutingInfo TargetInfo { get; } = new OutingInfo();

        protected override IReadOnlyList<IWeightRecord> OutingWeight()
        {
            var weightRecordList = _weightRepository.GetOutingTrainingCharaWeightRecordList();
            return OutingWeight(weightRecordList);
        }

        protected override IReadOnlyList<IWeightRecord> OutingModifyWeight()
        {
            var weightRecordList = _weightRepository.GetOutingTrainingCharaModifyWeightRecordList();
            return OutingWeight(weightRecordList);
        }
    }
}