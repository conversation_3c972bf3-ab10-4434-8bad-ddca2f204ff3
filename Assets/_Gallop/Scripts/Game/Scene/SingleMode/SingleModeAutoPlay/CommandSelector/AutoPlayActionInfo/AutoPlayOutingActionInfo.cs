using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// お出かけコマンドを実行するかの重み付けを行う
    /// </summary>
    public class AutoPlayOutingActionInfo : IAutoPlayOutingCommand
    {
        #region IAutoPlayOutingCommand

        private const int DEFAULT_WEIGHT_VALUE = -1;

        int IAutoPlayActionInfo.Weight
        {
            get
            {
                if (TargetInfo == null) return DEFAULT_WEIGHT_VALUE;

                if (_cacheWeight != DEFAULT_WEIGHT_VALUE) return _cacheWeight;
                _cacheWeight = CalculateWeight();
                return _cacheWeight;
            }
        }
        int IAutoPlayActionInfo.TargetCount => 1;

        bool IAutoPlayModifiableFilterActionInfo.IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            return IsMatchCondition(filterRecordList);
        }

        IReadOnlyList<AutoPlayFilterRecord> IAutoPlayModifiableFilterActionInfo.ApplicableFilterRecordList => ApplicableFilterRecordList;

        IReadOnlyList<int> IAutoPlayModifiableFilterActionInfo.UsableCommandIdList
        {
            get
            {
                if (LockCommandIdList == null) return DefaultUsableCommandIdList;
                return DefaultUsableCommandIdList.Except(LockCommandIdList).ToList();
            }
        }

        void IAutoPlayModifiableFilterActionInfo.SetLockCommandIdList(IReadOnlyList<int> lockCommandIdList)
        {
            LockCommandIdList = lockCommandIdList;
        }
        #endregion IAutoPlayOutingCommand
        protected virtual IReadOnlyList<int> DefaultUsableCommandIdList
        {
            get
            {
                var list = new List<int>();
                if (_weightRepository == null) return list;
                list.Add(AutoPlayCommandId);
                return list;
            }
        }

        protected IReadOnlyList<int> LockCommandIdList { get; private set; }
        protected IReadOnlyList<AutoPlayFilterRecord> ApplicableFilterRecordList { get; private set; }

        /// <summary> 対象のサポカID </summary>
        public virtual int SupportCardId => TargetInfo?.SupportCardId ?? 0;
        /// <summary> グループサポカのキャラID（グループサポカの場合のみ） </summary>
        public virtual int GroupCharaId => TargetInfo?.GroupCharaId ?? 0;
        /// <summary> グループサポカのキャラ全員とのお出かけが有効か（グループサポカの場合のみ） </summary>
        public virtual bool IsGroupAll => TargetInfo?.IsGroupAll ?? false;
        /// <summary> 育成キャラのお出かけか </summary>
        public virtual bool IsTrainingChara => false;

        /// <summary> 実行するお出かけ情報 </summary>
        protected virtual OutingInfo TargetInfo => InfoList?.FirstOrDefault();

        /// <summary> お出かけ情報一覧 </summary>
        private IReadOnlyList<OutingInfo> InfoList => _cacheOutingInfoList ??= SelectOuting();
        private IReadOnlyList<OutingInfo> _cacheOutingInfoList;

        private int _cacheWeight = DEFAULT_WEIGHT_VALUE;

        /// <summary> 体力 </summary>
        private readonly int _characterHp;
        /// <summary> 汎用的なコマンドの重みデータの取得ができるRepository </summary>
        protected readonly ICommonSingleModeAutoPlayWeightRepository _weightRepository;
        ///<summary> 編成しているサポカ情報 </summary>
        private readonly WorkSingleModeCharaData.EquipSupportCard[] _equipSupportCardArray;
        ///<summary> 編成しているサポカの親愛度情報 </summary>
        private readonly IReadOnlyList<WorkSingleModeCharaData.Evaluation> _evaluationList;
        ///<summary> 野良サポカのお出かけ情報 </summary>
        private readonly IReadOnlyList<WorkSingleModeCharaData.GuestOutingInfo> _guestOutingInfoList;

        /// <summary> おまかせ育成用CommandId </summary>
        protected virtual int AutoPlayCommandId => _weightRepository?.GetOutingAutoPlayCommandId() ?? 0;
        /// <summary> 基礎重み </summary>
        protected virtual int BaseWeight => _weightRepository?.GetOutingBaseWeight() ?? 0;
        
        protected bool IsLock
        {
            get
            {
                if (LockCommandIdList.IsNullOrEmptyReadOnlyList()) return false;
                return LockCommandIdList.Contains(AutoPlayCommandId);
            }
        }

        public AutoPlayOutingActionInfo(
            int characterHp,
            ICommonSingleModeAutoPlayWeightRepository weightRepository,
            WorkSingleModeCharaData.EquipSupportCard[] equipSupportCardArray,
            IReadOnlyList<WorkSingleModeCharaData.Evaluation> evaluationList,
            IReadOnlyList<WorkSingleModeCharaData.GuestOutingInfo> guestOutingInfoList)
        {
            _characterHp = characterHp;
            _weightRepository = weightRepository;
            _equipSupportCardArray = equipSupportCardArray;
            _evaluationList = evaluationList;
            _guestOutingInfoList = guestOutingInfoList;
        }

        private int CalculateWeight()
        {
            if (IsLock) return 0;

            var totalAddWeight = TotalAddWeight();
            var totalAddWeightValue = totalAddWeight
                .GroupBy(x => x.ConditionSetId)
                .Sum(recordGroup => recordGroup?.Min(record => record.Weight) ?? 0);

            var preferenceWeight = ApplicableFilterRecordList?.Sum(filterRecord =>
                filterRecord.GetPreferenceWeight(AutoPlayCommandId)) ?? 0;
            return BaseWeight + totalAddWeightValue + preferenceWeight;
        }

        protected virtual IReadOnlyList<IWeightRecord> TotalAddWeight()
        {
            var result = new List<IWeightRecord>();
            result.AddRangeSafely(OutingWeight());
            result.AddRangeSafely(OutingModifyWeight());
            return result;
        }

        protected virtual IReadOnlyList<IWeightRecord> OutingWeight()
        {
            var weightRecordList = _weightRepository.GetOutingWeightRecordList();
            return OutingWeight(weightRecordList);
        }

        protected virtual IReadOnlyList<IWeightRecord> OutingModifyWeight()
        {
            var weightRecordList = _weightRepository.GetOutingModifyWeightRecordList();
            return OutingWeight(weightRecordList);
        }

        protected virtual IReadOnlyList<IWeightRecord> OutingWeight(IReadOnlyList<IConditionValueRangeRecord> recordList)
        {
            return recordList.Select(x => new ConditionValueRangeWeightModel(x, _characterHp)).ToList();
        }

        /// <summary>
        /// 対象になるお出かけ情報の生成
        /// </summary>
        private IReadOnlyList<OutingInfo> SelectOuting()
        {
            var list = new List<OutingInfo>();

            // 編成しているサポカのお出かけ情報を追加
            list.AddRangeSafely(GetOutingInfoEnumerableByEquip());

            // 野良サポカのお出かけ情報を追加
            list.AddRangeSafely(GetOutingInfoEnumerableByGuest());

            // お出かけ情報をランダムに並び替え
            GallopUtil.ListShuffleNonAlloc(list);
            return list;
        }

        /// <summary>
        /// 編成しているサポカのお出かけ情報を取得
        /// </summary>
        private IEnumerable<OutingInfo> GetOutingInfoEnumerableByEquip()
        {
            if (_equipSupportCardArray.IsNullOrEmpty()) return null;

            // お出かけ可能なサポカIDと親愛度のペアのリスト
            var outingSupportCardIdAndEvaluationList = _equipSupportCardArray
                .Select(x =>
                {
                    // サポカIDと親愛度のペアに変換
                    var supportCardId = x.SupportCardId;
                    var evaluation = _evaluationList.FirstOrDefault(evaluation => evaluation.TargetId == x.Position);
                    return (supportCardId, evaluation);
                })
                .Where(pair => pair.evaluation.IsOuting) // お出かけ可能なデータのみに絞り込む
                .ToList();

            var list = new List<OutingInfo>();
            foreach (var pair in outingSupportCardIdAndEvaluationList)
            {
                var supportCardId = pair.supportCardId;
                var evaluation = pair.evaluation;

                var masterSupportCardData = MasterDataManager.Instance.masterSupportCardData.Get(supportCardId);

                // 既にお出かけ回数が最大値に到達していたらスキップ
                if (evaluation.StoryStep >= masterSupportCardData.OutingMax) continue;

                var outingInfoList = masterSupportCardData.IsGroupSupportCard ? 
                    GetOutingInfoEnumerableByGroup(masterSupportCardData, evaluation.GroupOutingInfoList) : // グループサポカ
                    GetOutingInfoEnumerableByFriend(masterSupportCardData);                                 // 友人サポカ

                list.AddRange(outingInfoList);
            }

            return list;
        }

        /// <summary>
        /// 野良サポカのお出かけ情報を取得
        /// </summary>
        private IEnumerable<OutingInfo> GetOutingInfoEnumerableByGuest()
        {
            if (_guestOutingInfoList.IsNullOrEmptyReadOnlyList()) return null;

            var list = new List<OutingInfo>();
            foreach (var outingInfo in _guestOutingInfoList)
            {
                var masterSupportCard = MasterDataManager.Instance.masterSupportCardData.Get(outingInfo.SupportCardId);

                // 既にお出かけ回数が最大値に到達していたらスキップ
                if (outingInfo.StoryStep >= masterSupportCard.OutingMax) continue;

                var outingInfoList = masterSupportCard.IsGroupSupportCard ? 
                    GetOutingInfoEnumerableByGroup(masterSupportCard, outingInfo.GroupOutingInfoList) : // グループサポカ
                    GetOutingInfoEnumerableByFriend(masterSupportCard);                                 // 友人サポカ

                list.AddRange(outingInfoList);
            }

            return list;
        }

        /// <summary>
        /// グループサポカのお出かけ情報を取得
        /// </summary>
        private IEnumerable<OutingInfo> GetOutingInfoEnumerableByGroup(
            MasterSupportCardData.SupportCardData masterSupportCardData,
            IReadOnlyList<WorkSingleModeCharaData.Evaluation.GroupOutingInfo> groupOutingInfoList)
        {
            var groupMasterList = masterSupportCardData.GetMasterSupportCardGroupList();
            var groupCardCharaIdList = masterSupportCardData.GetCharaIdList();

            // グループに所属している各キャラのお出かけ情報に変換
            var outingInfoList = groupCardCharaIdList
                .Where(x =>
                {
                    // お出かけ可能なキャラIDのみに絞り込み
                    var groupData = groupMasterList.Find(groupMaster => groupMaster.CharaId == x);
                    return groupOutingInfoList
                        .Any(groupOutingInfo => groupOutingInfo.IsOuting && 
                                                groupOutingInfo.StoryStep < groupData.OutingMax && 
                                                groupOutingInfo.CharaId == x);
                })
                .Select(x => new OutingInfo(masterSupportCardData.Id, x, false))
                .ToList();
            // グループに所属しているキャラが全員お出かけ不可 = グループの全員とのお出かけが可能
            if (outingInfoList.IsNullOrEmpty())
            {
                outingInfoList.Add(new OutingInfo(masterSupportCardData.Id, 0, true));
            }

            return outingInfoList;
        }

        /// <summary>
        /// 友人サポカのお出かけ情報を取得
        /// </summary>
        private IEnumerable<OutingInfo> GetOutingInfoEnumerableByFriend(MasterSupportCardData.SupportCardData masterSupportCardData)
        {
            return new List<OutingInfo>() { new OutingInfo(masterSupportCardData.Id) };
        }

        /// <summary>
        /// フィルタ情報を適用した場合に適用条件を満たすか
        /// </summary>
        protected virtual bool IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            // 受け取ったフィルタ情報のうち、適用種別と条件を満たすものを格納する
            ApplicableFilterRecordList = filterRecordList?
                .Where(record =>
                {
                    if (record == null) return false;

                    var filterConditionList = record.FilterConditionList;
                    var hpConditionList = _weightRepository?.GetHpConditionList(filterConditionList);

                    if (hpConditionList.IsNullOrEmptyReadOnlyList()) return false;

                    var isApplicableCondition = hpConditionList
                        .All(condition => condition.ConditionValue1 <= _characterHp && 
                                          condition.ConditionValue2 >= _characterHp);
                    return isApplicableCondition;
                })
                .ToList();

            // 適用種別と条件を満たすフィルタ情報があるか返す
            return ApplicableFilterRecordList?.Any() ?? false;
        }

        /// <summary>
        /// お出かけ情報
        /// </summary>
        protected class OutingInfo
        {
            /// <summary> 対象のサポカID </summary>
            public int SupportCardId { get; }
            /// <summary> グループサポカのキャラID（グループサポカの場合のみ） </summary>
            public int GroupCharaId { get; }
            /// <summary> グループサポカのキャラ全員とのお出かけが有効か（グループサポカの場合のみ） </summary>
            public bool IsGroupAll { get; }

            public OutingInfo() : this(0, 0, false) { }

            public OutingInfo(int supportCardId) : this(supportCardId, 0, false) { }

            public OutingInfo(int supportCardId, int groupCharaId, bool isGroupAll)
            {
                SupportCardId = supportCardId;
                GroupCharaId = groupCharaId;
                IsGroupAll = isGroupAll;
            }
        }
    }
}