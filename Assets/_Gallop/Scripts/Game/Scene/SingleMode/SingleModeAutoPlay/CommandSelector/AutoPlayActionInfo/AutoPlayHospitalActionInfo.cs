using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public class AutoPlayHospitalActionInfo : IAutoPlayHospitalCommand
    {
        private const int DEFAULT_WEIGHT = -1;

        #region IAutoPlayHospitalCommand
        int IAutoPlayActionInfo.Weight
        {
            get
            {
                if (_cacheWeight == DEFAULT_WEIGHT) _cacheWeight = Calculate();
                return _cacheWeight;
            }
        }
        int IAutoPlayActionInfo.TargetCount => 1;

        bool IAutoPlayModifiableFilterActionInfo.IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            return IsMatchCondition(filterRecordList);
        }

        IReadOnlyList<AutoPlayFilterRecord> IAutoPlayModifiableFilterActionInfo.ApplicableFilterRecordList => ApplicableFilterRecordList;

        IReadOnlyList<int> IAutoPlayModifiableFilterActionInfo.UsableCommandIdList
        {
            get
            {
                if (LockCommandIdList == null) return DefaultUsableCommandIdList;
                return DefaultUsableCommandIdList.Except(LockCommandIdList).ToList();
            }
        }

        void IAutoPlayModifiableFilterActionInfo.SetLockCommandIdList(IReadOnlyList<int> lockCommandIdList)
        {
            LockCommandIdList = lockCommandIdList;
        }
        #endregion IAutoPlayHospitalCommand

        protected virtual IReadOnlyList<int> DefaultUsableCommandIdList
        {
            get
            {
                var list = new List<int>();
                if (_weightRepository == null) return list;
                list.Add(_weightRepository.GetHospitalAutoPlayCommandId());
                return list;
            }
        }

        protected IReadOnlyList<int> LockCommandIdList { get; private set; }
        protected IReadOnlyList<AutoPlayFilterRecord> ApplicableFilterRecordList { get; private set; }

        private int _cacheWeight = DEFAULT_WEIGHT;

        private readonly WorkSingleModeCharaData _workCharaData;
        private readonly ICommonSingleModeAutoPlayWeightRepository _weightRepository;

        protected bool IsLock
        {
            get
            {
                if (LockCommandIdList.IsNullOrEmptyReadOnlyList()) return false;
                return LockCommandIdList.Contains(_weightRepository.GetHospitalAutoPlayCommandId());
            }
        }

        public AutoPlayHospitalActionInfo(WorkSingleModeCharaData workCharaData, 
            ICommonSingleModeAutoPlayWeightRepository weightRepository)
        {
            _workCharaData = workCharaData;
            _weightRepository = weightRepository;
        }

        protected virtual int Calculate()
        {
            if (IsLock) return 0;

            // 基礎重み
            var baseWeight = _weightRepository.GetHospitalBaseWeight();

            var totalAddWeight = TotalAddWeight();
            var totalAddWeightValue = totalAddWeight
                .GroupBy(x => x.ConditionSetId)
                .Sum(recordGroup => recordGroup?.Min(record => record.Weight) ?? 0);

            var preferenceWeight = ApplicableFilterRecordList?.Sum(filterRecord =>
                filterRecord.GetPreferenceWeight(_weightRepository.GetHospitalAutoPlayCommandId())) ?? 0;

            var totalWeight = baseWeight + totalAddWeightValue + preferenceWeight;
            return totalWeight;
        }

        protected virtual IReadOnlyList<IWeightRecord> TotalAddWeight()
        {
            var charaEffectIdArray = _workCharaData?.CharaEffectIdArray;
            var weightRecordList = _weightRepository?.GetHospitalWeightRecordList();
            var modifyRecordList = _weightRepository?.GetHospitalModifyWeightRecordList();

            var result = new List<IConditionValueOneUseRecord>();
            result.AddRangeSafely(weightRecordList);
            result.AddRangeSafely(modifyRecordList);

            if (result.IsNullOrEmpty()) return new List<IWeightRecord>();

            return result.Select(x =>
                {
                    return new ConditionValueOneUseWeightModel(x, HasEffect);

                    bool HasEffect(int conditionValue) => charaEffectIdArray?.Contains(conditionValue) ?? false;
                }).ToList();
        }

        /// <summary>
        /// フィルタ情報を適用した場合に適用条件を満たすか
        /// </summary>
        protected virtual bool IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            var charaEffectIdArray = _workCharaData?.CharaEffectIdArray;

            // 受け取ったフィルタ情報のうち、適用種別と条件を満たすものを格納する
            ApplicableFilterRecordList = filterRecordList?
                .Where(record =>
                {
                    if (record == null) return false;

                    var filterConditionList = record.FilterConditionList;
                    var hasBadEffectConditionList = _weightRepository?.GetBadEffectConditionList(filterConditionList);

                    if (hasBadEffectConditionList.IsNullOrEmptyReadOnlyList()) return false;

                    var isApplicableCondition = hasBadEffectConditionList
                        .All(condition => charaEffectIdArray?.Contains(condition.ConditionValue1) ?? false);
                    return isApplicableCondition;
                })
                .ToList();

            // 適用種別と条件を満たすフィルタ情報があるか返す
            return ApplicableFilterRecordList?.Any() ?? false;
        }
    }
}