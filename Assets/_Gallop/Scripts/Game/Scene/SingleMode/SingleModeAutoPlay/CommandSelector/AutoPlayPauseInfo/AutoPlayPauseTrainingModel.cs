using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public partial class AutoPlayPauseInfo
    {
        /// <summary>
        /// トレーニングコマンドに関する一時停止Model
        /// </summary>
        protected class AutoPlayPauseTrainingModel
        {
            private IReadOnlyList<AutoPlayPauseTrainingHorseRecord> TrainingHorseRecordList { get; }
            private IReadOnlyList<int> PauseTriggerTypeList { get; }

            /// <summary> 友情トレーニングの一時停止トリガーが設定されているか </summary>
            protected virtual bool HasTagTrainingTrigger => PauseTriggerTypeList?.Any(x => x == (int)AutoPlayPauseTriggerType.TagTraining) ?? false;

            /// <summary> 友情トレーニングの一時停止トリガーが有効か </summary>
            protected virtual bool IsTagTraining => HasTagTrainingTrigger && (TrainingHorseRecordList?.Any(x => x.IsTagTraining) ?? false);

            /// <summary> ヒントイベントの一時停止トリガーが設定されているか </summary>
            protected virtual bool HasTipsTrigger => PauseTriggerTypeList?.Any(x => x == (int)AutoPlayPauseTriggerType.HasTips) ?? false;

            /// <summary> ヒントイベントの一時停止トリガーが有効か </summary>
            protected virtual bool HasTips => HasTipsTrigger && (TrainingHorseRecordList?.Any(x => x.HasTips) ?? false);

            public AutoPlayPauseTrainingModel(IReadOnlyList<AutoPlayPauseTrainingHorseRecord> trainingHorseRecordList,
                IReadOnlyList<int> pauseTriggerTypeList)
            {
                TrainingHorseRecordList = trainingHorseRecordList;
                PauseTriggerTypeList = pauseTriggerTypeList;
            }

            /// <summary> トレーニングコマンドに関する有効な一時停止トリガーが存在するか </summary>
            public virtual bool HasApplicablePauseTrigger => IsTagTraining || HasTips;
        }

        /// <summary>
        /// トレーニングウマの一時停止用レコード
        /// </summary>
        protected class AutoPlayPauseTrainingHorseRecord
        {
            /// <summary> 友情トレーニングが発生するか </summary>
            public bool IsTagTraining { get; }

            /// <summary> ヒントイベントが発生するか </summary>
            public bool HasTips { get; }

            public AutoPlayPauseTrainingHorseRecord(bool isTagTraining, bool hasTips)
            {
                IsTagTraining = isTagTraining;
                HasTips = hasTips;
            }
        }
    }
}