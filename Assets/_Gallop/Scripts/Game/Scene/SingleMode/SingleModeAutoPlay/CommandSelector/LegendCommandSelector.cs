using System;
using System.Collections.Generic;
#if CYG_DEBUG
using System.Linq;
#endif

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 伝説編のコマンド選択ロジック
    /// </summary>
    public class LegendCommandSelector : CommandSelectorBase
    {
        protected override AutoPlayActionInfoFilter ActionInfoFilter { get; } = new LegendAutoPlayActionInfoFilter();

        protected override (IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Func) SelectAction()
        {
            // トレーニングコマンドデータを作成
            var trainingActionInfo = GenerateAutoPlayTrainingActionInfo();

            // お休みコマンドデータを作成
            var restActionInfo = GenerateAutoPlayRestActionInfo();

            // レースコマンドデータを作成
            var raceActionInfo = GenerateAutoPlayRaceActionInfo();

            // 保健室コマンドデータを作成
            var hospitalActionInfo = GenerateAutoPlayHospitalActionInfo();

            // お出かけコマンドデータを作成（夏合宿中のお出かけはお休みコマンド側で吸収）
            var outingActionInfo = GenerateAutoPlayOutingActionInfo();

            // 育成キャラのお出かけコマンドデータを作成（夏合宿中のお出かけはお休みコマンド側で吸収）
            var outingTrainingCharaActionInfo = GenerateAutoPlayOutingTrainingCharaActionInfo();

            // どのコマンドがどの行動を指すのかを紐づける
            var actionInfoAndGenerateList = new List<(IAutoPlayActionInfo, Func<ISingleModeAutoPlayAgentAction>)>();
            actionInfoAndGenerateList.Add((trainingActionInfo, () => GenerateCommandAndTrainingAction(trainingActionInfo)));
            actionInfoAndGenerateList.Add((restActionInfo, GenerateRestCommandAction));
            actionInfoAndGenerateList.Add((raceActionInfo, () => GenerateRaceCommandAction(raceActionInfo)));
            actionInfoAndGenerateList.Add((hospitalActionInfo, GenerateHospitalCommandAction));
            actionInfoAndGenerateList.Add((outingActionInfo, () => GenerateOutingCommandAction(outingActionInfo)));
            actionInfoAndGenerateList.Add((outingTrainingCharaActionInfo, () => GenerateOutingCommandAction(outingTrainingCharaActionInfo)));

            var filteredActionInfoAndGenerateList = ActionInfoFilter.ConsumptionCommandFilter(actionInfoAndGenerateList);
            
            return SelectConsumptionAction(filteredActionInfoAndGenerateList);
        }

        protected override ISingleModeAutoPlayAgentAction SelectNonConsumptionAction(IAutoPlayActionInfo selectedActionInfo)
        {
            return null;
        }

        protected override ISingleModeAutoPlayAgentAction SelectTraining(AutoPlayTrainingActionParameter parameter)
        {
            return new TrainingCommandAction(parameter);
        }

#if CYG_DEBUG
        protected override IReadOnlyList<IAutoPlayActionInfo> DebugGetActionInfoList()
        {
            var targetActionInfoList = new List<(IAutoPlayActionInfo, Func<ISingleModeAutoPlayAgentAction>)>()
            {
                (GenerateAutoPlayTrainingActionInfo(), null),
                (GenerateAutoPlayRestActionInfo(), null),
                (GenerateAutoPlayRaceActionInfo(), null),
                (GenerateAutoPlayHospitalActionInfo(), null),
                (GenerateAutoPlayOutingActionInfo(), null),
                (GenerateAutoPlayOutingTrainingCharaActionInfo(), null),
            };
            return ActionInfoFilter.ConsumptionCommandFilter(targetActionInfoList)
                .Select(x => x.ActionInfo)
                .ToList();
        }
#endif
    }
}