using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public abstract class CommandSelectorBase : ICommandSelector
    {
        #region プロパティ
        protected virtual AutoPlayActionInfoFilter ActionInfoFilter { get; } = new AutoPlayActionInfoFilter();
        #endregion プロパティ

        #region abstract
        protected abstract (IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Func) SelectAction();
        protected abstract ISingleModeAutoPlayAgentAction SelectNonConsumptionAction(IAutoPlayActionInfo selectedActionInfo);
        protected abstract ISingleModeAutoPlayAgentAction SelectTraining(AutoPlayTrainingActionParameter parameter);
        #endregion abstract

        #region Repository
        /// <summary> 重みデータを管理するRepository </summary>
        protected SingleModeAutoPlayWeightRepositoryBase WeightRepository
        {
            get
            {
                if (!SingleModeAutoPlayAgent.HasInstance()) return null;
                return SingleModeAutoPlayAgent.Instance.Proxy.GetWeightRepository();
            }
        }

        /// <summary> 汎用的なコマンドの重みデータを管理するRepository </summary>
        protected ICommonSingleModeAutoPlayWeightRepository CommonWeightRepository =>
            WeightRepository.GetAsRepository<ICommonSingleModeAutoPlayWeightRepository>();
        #endregion Repository

        #region ICommandSelector
        ISingleModeAutoPlayAgentAction ICommandSelector.SelectCommand()
        {
            var autoPlayPauseInfo = WeightRepository?.AutoPlayPauseInfo;
            if (autoPlayPauseInfo?.NeedAutoPlayPauseBySelectCommand() ?? false) return new PauseAutoPlayAction();
            var selectedAction = SelectAction();

            // 選択されたターン消費行動を基に、先行して行いたいターン非消費行動があるか探す
            var selectedNonConsumptionAction = SelectNonConsumptionAction(selectedAction.ActionInfo);
            if (selectedNonConsumptionAction != null) return selectedNonConsumptionAction;

            return selectedAction.Func?.Invoke();
        }

        ISingleModeAutoPlayAgentAction ICommandSelector.SelectTraining(AutoPlayTrainingActionParameter parameter)
        {
            return SelectTraining(parameter);
        }
        #endregion ICommandSelector

        #region ターン消費行動
        /// <summary>
        /// トレーニングボタンを押す行動を作成
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction GenerateCommandAndTrainingAction(AutoPlayTrainingActionInfo trainingActionInfo)
        {
            SingleModeAutoPlayAgent.Instance.Proxy.SetParameter(new AutoPlayTrainingActionParameter(trainingActionInfo.CommandId));
            return new TrainingButtonAction();
        }

        /// <summary>
        /// お休みボタンを押す行動を作成
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction GenerateRestCommandAction()
        {
            var isSummerCampTurn = WorkDataManager.Instance.SingleMode.IsSummerCampTurn();
            if (isSummerCampTurn) return new SummerOutingButtonAction();
            return new HolidayButtonAction();
        }

        /// <summary>
        /// レースボタンを押す行動を作成
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction GenerateRaceCommandAction(AutoPlayRaceActionInfo raceActionInfo)
        {
            var parameter = new AutoPlayRaceActionParameter(raceActionInfo.RaceProgram);
            SingleModeAutoPlayAgent.Instance.Proxy.SetParameter(parameter);
            return new TargetRaceRegisterAction();
        }

        /// <summary>
        /// 保健室ボタンを押す行動を作成
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction GenerateHospitalCommandAction()
        {
            return new HospitalButtonAction();
        }

        /// <summary>
        /// お出かけボタンを押す行動を作成
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction GenerateOutingCommandAction(AutoPlayOutingActionInfo outingActionInfo)
        {
            // お出かけ対象をAgentProxyに登録
            var outingParameter = new AutoPlayOutingParameter(outingActionInfo.SupportCardId, outingActionInfo.GroupCharaId, outingActionInfo.IsGroupAll, outingActionInfo.IsTrainingChara);
            SingleModeAutoPlayAgent.Instance.Proxy.SetParameter(outingParameter);
            return new OutingButtonAction();
        }

        /// <summary>
        /// トレーニングコマンドの重み付け用Recordを作成
        /// </summary>
        protected virtual IReadOnlyList<AutoPlayTrainingActionInfo.AutoPlayTrainingRecord> GenerateAutoPlayTrainingActionRecord()
        {
            var turnInfoListDic = WorkDataManager.Instance.SingleMode.HomeInfo.TurnInfoListDic;
            if (!turnInfoListDic.TryGetValue(SingleModeDefine.CommandType.Training, out var turnInfoList)) return null;
            return turnInfoList
                .Where(turnInfo => turnInfo.IsEnable)
                .Select(turnInfo => new AutoPlayTrainingActionInfo.AutoPlayTrainingRecord() 
                {
                    TrainingFailureRate = turnInfo.TrainingFailureRate,
                    WorkCharaData = WorkDataManager.Instance.SingleMode.Character,
                    CommandId = turnInfo.CommandId,
                    HorseRecordList = GenerateAutoPlayTrainingHorseRecordList(turnInfo),
                })
                .ToList();
        }

        /// <summary>
        /// トレーニングコマンドの重み付けで使用する併せウマ用RecordをTurnInfoから作成
        /// </summary>
        protected virtual IReadOnlyList<AutoPlayTrainingActionInfo.AutoPlayTrainingHorseRecord> GenerateAutoPlayTrainingHorseRecordList(
            WorkSingleModeData.TurnInfo turnInfo)
        {
            var commandType = turnInfo?.CommandType ?? SingleModeDefine.CommandType.None;
            var commandId = turnInfo?.CommandId ?? TrainingDefine.TrainingCommandId.None;
            var trainingHorseList = turnInfo?.TrainingHorseList;

            return trainingHorseList?.Select(trainingHorse =>
            {
                return new AutoPlayTrainingActionInfo.AutoPlayTrainingHorseRecord()
                {
                    SupportCardId = trainingHorse.GetSupportCardData()?.Id ?? 0,
                    IsGuest = trainingHorse.IsGuest,
                    HasTips = trainingHorse.IsTips,
                    IsTagTraining = trainingHorse.IsTagTraining(commandType, commandId)
                };
            }).ToList();
        }

        /// <summary>
        /// トレーニングコマンドの重み付けを行うModelクラスを生成
        /// </summary>
        protected virtual AutoPlayTrainingActionInfo GenerateAutoPlayTrainingActionInfo()
        {
            var trainingActionInfoRecordList = GenerateAutoPlayTrainingActionRecord();

            // 指定できないトレーニングは踏まないように省く
            var filteredTrainingActionInfoRecordList = ActionInfoFilter.TrainingCommandFilter(trainingActionInfoRecordList);

            return new AutoPlayTrainingActionInfo(filteredTrainingActionInfoRecordList, CommonWeightRepository);
        }

        /// <summary>
        /// お休みコマンドの重み付けを行うModelクラスのうち、使用できるものを生成
        /// </summary>
        protected virtual AutoPlayRestActionInfoBase GenerateAutoPlayRestActionInfo()
        {
            var isSummerCampTurn = WorkDataManager.Instance.SingleMode.IsSummerCampTurn();
            return isSummerCampTurn ? GenerateAutoPlaySummerOutingActionInfo() : GenerateAutoPlayHolidayActionInfo();
        }

        /// <summary>
        /// お休みコマンドの重み付けを行うModelクラスを生成
        /// </summary>
        protected virtual AutoPlayRestActionInfoBase GenerateAutoPlayHolidayActionInfo()
        {
            return new AutoPlayHolidayActionInfo(WorkDataManager.Instance.SingleMode.Character, WeightRepository);
        }
        
        /// <summary>
        /// 夏合宿中のお休みコマンドの重み付けを行うModelクラスを生成
        /// </summary>
        protected virtual AutoPlayRestActionInfoBase GenerateAutoPlaySummerOutingActionInfo()
        {
            return new AutoPlayCampOutingActionInfo(WorkDataManager.Instance.SingleMode.Character, WeightRepository);
        }

        protected virtual AutoPlayRaceActionInfo GenerateAutoPlayRaceActionInfo()
        {
            return new AutoPlayRaceActionInfo(WorkDataManager.Instance.SingleMode.Character, WeightRepository);
        }

        /// <summary>
        /// 保健室コマンドの重み付けを行うModelクラスを生成
        /// </summary>
        protected virtual AutoPlayHospitalActionInfo GenerateAutoPlayHospitalActionInfo()
        {
            var workCharaData = WorkDataManager.Instance.SingleMode.Character;
            if (workCharaData == null) return null;
            return new AutoPlayHospitalActionInfo(workCharaData, WeightRepository);
        }

        /// <summary>
        /// お出かけコマンドの重み付けを行うModelクラスを生成
        /// </summary>
        protected virtual AutoPlayOutingActionInfo GenerateAutoPlayOutingActionInfo()
        {
            var workCharaData = WorkDataManager.Instance.SingleMode.Character;
            if (workCharaData == null) return null;

            // 夏合宿中はお出かけできない
            var isSummerCampTurn = WorkDataManager.Instance.SingleMode.IsSummerCampTurn();
            if (isSummerCampTurn) return null;

            return new AutoPlayOutingActionInfo(
                workCharaData.Hp,
                WeightRepository,
                workCharaData.EquipSupportCardArray,
                workCharaData.EvaluationList,
                workCharaData.GuestOutingInfoList);
        }

        /// <summary>
        /// 育成キャラのお出かけコマンドの重み付けを行うModelクラスを生成
        /// </summary>
        protected virtual AutoPlayOutingTrainingCharaActionInfo GenerateAutoPlayOutingTrainingCharaActionInfo()
        {
            var workCharaData = WorkDataManager.Instance.SingleMode.Character;
            if (workCharaData == null) return null;

            // 夏合宿中はお出かけできない
            var isSummerCampTurn = WorkDataManager.Instance.SingleMode.IsSummerCampTurn();
            if (isSummerCampTurn) return null;

            return new AutoPlayOutingTrainingCharaActionInfo(workCharaData.Hp, WeightRepository);
        }

        /// <summary>
        /// ターン消費行動を選択
        /// </summary>
        protected (IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Func) SelectConsumptionAction(
            IReadOnlyList<(IAutoPlayActionInfo ActionInfo, Func<ISingleModeAutoPlayAgentAction> Func)> actionList)
        {
            if (actionList.IsNullOrEmptyReadOnlyList()) return (null, null);

            var usableActionInfoList = UsableActionInfoList(actionList.Select(x => x.ActionInfo).ToList());
            // 最もWeightが高い行動を実行
            // NOTE: 各トレーニングコマンドを他のコマンドと並列に扱わないと、各トレーニングのランダム選出確率に影響が出る
            var highestWeightActionInfoList = actionList
                .Where(pair => usableActionInfoList.Contains(pair.ActionInfo))
                .GroupBy(pair => pair.ActionInfo?.Weight ?? -1)
                .OrderByDescending(pair => pair.Key)
                .FirstOrDefault()
                ?.SelectMany(x => Enumerable.Repeat(x, x.ActionInfo.TargetCount)) // トレーニングコマンドは種別分だけリストに追加
                .ToList();
            return highestWeightActionInfoList.GetByRandom();
        }

         /// <summary>
        /// 使用可能なAutoPlayActionInfoのリストを返す
        /// </summary>
        protected virtual IReadOnlyList<IAutoPlayActionInfo> UsableActionInfoList(IReadOnlyList<IAutoPlayActionInfo> actionInfoList)
        {
            if (actionInfoList == null) return null;

            var currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;

            var userSettingActiveFilterIdList = currentPlanRecord?.ModifyFilterIdList;
            var usableFilterRecordList = GetUsableFilterRecordList(userSettingActiveFilterIdList);
            if (usableFilterRecordList.IsNullOrEmptyReadOnlyList()) return actionInfoList;

            var usableMaxPriority = usableFilterRecordList.Max(record => record.FilterRecord.Priority);
            var usableCommandModelList = actionInfoList.SelectMany(actionInfo =>
            {
                if (!(actionInfo is IAutoPlayModifiableFilterActionInfo modifiableFilterActionInfo)) return null;
                var usableCommandIdList = modifiableFilterActionInfo.UsableCommandIdList;
                return usableCommandIdList.Select(commandId => new UsableCommandModel(commandId));
            }).ToList();
            return UsableActionInfoList(actionInfoList, usableCommandModelList, usableFilterRecordList, usableMaxPriority);
        }

        /// <summary>
        /// 使用可能なAutoPlayActionInfoのリストを返す（再帰）
        /// </summary>
        protected IReadOnlyList<IAutoPlayActionInfo> UsableActionInfoList(
            IReadOnlyList<IAutoPlayActionInfo> actionInfoList,
            IReadOnlyList<UsableCommandModel> usableCommandModelList,
            IReadOnlyList<AutoPlayFilterRecord> usableFilterRecordList,
            int usableMaxPriority)
        {
            // 適用するフィルターが無ければ終了
            if (usableFilterRecordList.IsNullOrEmptyReadOnlyList()) return actionInfoList;

            var matchingConditionActionInfoList = actionInfoList
                .Where(actionInfo => IsMatchConditionActionInfo(actionInfo, usableFilterRecordList, usableMaxPriority))
                .Select(actionInfo => actionInfo as IAutoPlayModifiableFilterActionInfo)
                .ToList();

            var isEmptyMatchingConditionActionInfoList = matchingConditionActionInfoList.IsNullOrEmpty();

            var appliedFilterRecordUsableCommandIdList = GetAppliedFilterRecordUsableCommandIdList(matchingConditionActionInfoList, usableCommandModelList);
            var isEmptyAppliedFilterRecordUsableCommandIdList = (appliedFilterRecordUsableCommandIdList?.Count ?? 0) <= 0;

            if (isEmptyMatchingConditionActionInfoList || isEmptyAppliedFilterRecordUsableCommandIdList)
            {
                foreach (var usableCommandModel in usableCommandModelList)
                {
                    // フィルタ適用によって使用不可になったコマンド情報を使用可能に戻す
                    usableCommandModel.Unlock();
                }

                // フィルタを適用した場合に行動可能なActionInfoが存在しなくなった場合、適用するPriorityの値を変更する
                var nextUsableFilterRecordList = usableFilterRecordList
                    .Where(record => record.FilterRecord.Priority < usableMaxPriority)
                    .ToList();
                var nextUsableMaxPriority = nextUsableFilterRecordList.Count == 0 ? 0 :
                                            nextUsableFilterRecordList.Max(record => record?.FilterRecord?.Priority ?? 0);

                return UsableActionInfoList(actionInfoList, usableCommandModelList, nextUsableFilterRecordList, nextUsableMaxPriority);
            }

            // フィルタ適用後でも実行できる行動があるので再帰を抜ける
            return actionInfoList.Where(actionInfo =>
            {
                // フィルタそのものが適用されないなら行動できるものとして返す
                if (!(actionInfo is IAutoPlayModifiableFilterActionInfo modifiableFilterActionInfo)) return true;

                var usableCommandIdList = modifiableFilterActionInfo.UsableCommandIdList;
                var lockCommandIdList = usableCommandIdList
                    .Where(commandId => !appliedFilterRecordUsableCommandIdList.Contains(commandId))
                    .ToList();
                modifiableFilterActionInfo.SetLockCommandIdList(lockCommandIdList);
                return modifiableFilterActionInfo.UsableCommandIdList.Count > 0;
            }).ToList();
        }

        #endregion ターン消費行動

        #region ターン非消費行動
        /// <summary>
        /// どのコマンドの実行前にもチェックするターン非消費行動データを作成
        /// </summary>
        protected virtual IReadOnlyList<INonConsumptionAutoPlayActionInfo> GenerateBeforeCommandActionInfoList()
        {
            var nonConsumptionAutoPlayActionInfoList = new List<INonConsumptionAutoPlayActionInfo>();
            // ターン非消費行動をリストに追加する
            return nonConsumptionAutoPlayActionInfoList;
        }

        /// <summary>
        /// トレーニングコマンド前に実行したいターン非消費行動を作成
        /// </summary>
        protected virtual IReadOnlyList<INonConsumptionAutoPlayActionInfo> GenerateBeforeTrainingActionInfoList(IAutoPlayActionInfo selectedActionInfo)
        {
            var nonConsumptionAutoPlayActionInfoList = new List<INonConsumptionAutoPlayActionInfo>();
            if (selectedActionInfo is IAutoPlayTrainingCommand)
            {
                // ターン非消費行動をリストに追加する
            }
            return nonConsumptionAutoPlayActionInfoList;
        }

        /// <summary>
        /// お休みコマンド（夏合宿のお休み＆お出かけコマンドを含む）前に実行したいターン非消費行動を作成
        /// </summary>
        protected virtual IReadOnlyList<INonConsumptionAutoPlayActionInfo> GenerateBeforeRestActionInfoList(IAutoPlayActionInfo selectedActionInfo)
        {
            var nonConsumptionAutoPlayActionInfoList = new List<INonConsumptionAutoPlayActionInfo>();
            if (selectedActionInfo is AutoPlayRestActionInfoBase)
            {
                // ターン非消費行動をリストに追加する
            }
            return nonConsumptionAutoPlayActionInfoList;
        }

        /// <summary>
        /// レースコマンド前に実行したいターン非消費行動を作成
        /// </summary>
        protected virtual IReadOnlyList<INonConsumptionAutoPlayActionInfo> GenerateBeforeRaceActionInfoList(IAutoPlayActionInfo selectedActionInfo)
        {
            var nonConsumptionAutoPlayActionInfoList = new List<INonConsumptionAutoPlayActionInfo>();
            if (selectedActionInfo is IAutoPlayRaceCommand)
            {
                // ターン非消費行動をリストに追加する
            }
            return nonConsumptionAutoPlayActionInfoList;
        }

        /// <summary>
        /// 保健室コマンド前に実行したいターン非消費行動を作成
        /// </summary>
        protected virtual IReadOnlyList<INonConsumptionAutoPlayActionInfo> GenerateBeforeHospitalActionInfoList(IAutoPlayActionInfo selectedActionInfo)
        {
            var nonConsumptionAutoPlayActionInfoList = new List<INonConsumptionAutoPlayActionInfo>();
            if (selectedActionInfo is IAutoPlayHospitalCommand)
            {
                // ターン非消費行動をリストに追加する
            }
            return nonConsumptionAutoPlayActionInfoList;
        }

        /// <summary>
        /// お出かけコマンド前に実行したいターン非消費行動を作成
        /// </summary>
        protected virtual IReadOnlyList<INonConsumptionAutoPlayActionInfo> GenerateBeforeOutingActionInfoList(IAutoPlayActionInfo selectedActionInfo)
        {
            var nonConsumptionAutoPlayActionInfoList = new List<INonConsumptionAutoPlayActionInfo>();
            if (selectedActionInfo is IAutoPlayOutingCommand)
            {
                // ターン非消費行動をリストに追加する
            }
            return nonConsumptionAutoPlayActionInfoList;
        }

        /// <summary>
        /// ターン非消費行動データから実際に行う行動を作成する
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction GenerateNonConsumptionAction(INonConsumptionAutoPlayActionInfo actionInfo)
        {
            return actionInfo switch
            {
                _ => CustomGenerateNonConsumptionAction(actionInfo),
            };
        }

        /// <summary>
        /// シナリオ固有のターン非消費行動データから実際に行う行動を作成する
        /// </summary>
        protected virtual ISingleModeAutoPlayAgentAction CustomGenerateNonConsumptionAction(INonConsumptionAutoPlayActionInfo actionInfo)
        {
            return null;
        }

        /// <summary>
        /// ターン非消費行動を選択
        /// </summary>
        protected INonConsumptionAutoPlayActionInfo SelectNonConsumptionAction(IReadOnlyList<INonConsumptionAutoPlayActionInfo> actionInfoList)
        {
            // 最もWeightが高い行動を実行
            return actionInfoList?
                .OrderByDescending(actionInfo => actionInfo?.Weight ?? -1)
                .FirstOrDefault();
        }
        #endregion ターン非消費行動

        /// <summary>
        /// フィルタデータの適用条件を満たすか
        /// </summary>
        protected virtual bool IsMatchConditionActionInfo(
            IAutoPlayActionInfo actionInfo,
            IReadOnlyList<AutoPlayFilterRecord> usableFilterRecordList,
            int usableMaxPriority)
        {
            var usablePriorityFilterRecordList = GetUsablePriorityFilterRecordList(usableFilterRecordList, usableMaxPriority);

            if (!(actionInfo is IAutoPlayModifiableFilterActionInfo modifiableFilterActionInfo)) return false;

            return modifiableFilterActionInfo.IsMatchCondition(usablePriorityFilterRecordList);
        }

        /// <summary>
        /// 有効なフィルタデータを取得
        /// </summary>
        protected virtual IReadOnlyList<AutoPlayFilterRecord> GetUsableFilterRecordList(IReadOnlyList<int> userSettingActiveFilterIdList)
        {
            var filterDataList = WeightRepository.GetFilterDataList();

            // 常に適用される or ユーザー設定によって適用されるフィルタデータのみ返す
            return filterDataList
                ?.Where(x => !x.FilterRecord.IsModifiable ||
                            (userSettingActiveFilterIdList?.Contains(x.FilterRecord.FilterId) ?? false))
                .ToList();
        }

        /// <summary>
        /// 適用可能な優先度を満たすフィルタデータのみを取得
        /// </summary>
        protected virtual IReadOnlyList<AutoPlayFilterRecord> GetUsablePriorityFilterRecordList(
            IReadOnlyList<AutoPlayFilterRecord> usableFilterRecordList,
            int usableMaxPriority)
        {
            return usableFilterRecordList
                ?.Where(filterData => filterData.FilterRecord.Priority <= usableMaxPriority)
                .ToList();
        }

        /// <summary>
        /// フィルタを適用後に実行できるCommandIdのリストを取得
        /// </summary>
        protected virtual IReadOnlyList<int> GetAppliedFilterRecordUsableCommandIdList(
            IReadOnlyList<IAutoPlayModifiableFilterActionInfo> targetActionInfoList,
            IReadOnlyList<UsableCommandModel> usableCommandModelList)
        {
            if (targetActionInfoList.IsNullOrEmptyReadOnlyList()) return null;

            // フィルタ適用後に実行できるActionInfoから適用可能なフィルタ情報を取得
            var applicableFilterRecordList = targetActionInfoList
                .SelectMany(actionInfo => actionInfo.ApplicableFilterRecordList ?? Enumerable.Empty<AutoPlayFilterRecord>())
                .ToList();

            foreach (var record in applicableFilterRecordList)
            {
                foreach (var detailRecord in record.FilterDetailList)
                {
                    var commandId = detailRecord.CommandId;
                    var isCommandLock = detailRecord.IsCommandLock;
                    if (isCommandLock)
                    {
                        // 指定のコマンドを塞ぐ設定なら、指定のコマンドを探して塞ぐ
                        usableCommandModelList.FirstOrDefault(x => x.CommandId == commandId)?.Lock();
                    }
                }
            }

            // フィルタ適用後に実行可能なCommandIdのみを返す
            return usableCommandModelList.Where(x => x.IsUsable).Select(x => x.CommandId).ToList();
        }

        /// <summary>
        /// CommandId と 使用可能か をまとめたModel
        /// </summary>
        protected class UsableCommandModel
        {
            public int CommandId { get; }
            public bool IsUsable { get; private set; }

            public UsableCommandModel(int commandId)
            {
                CommandId = commandId;
                IsUsable = true;
            }

            public void Lock()
            {
                IsUsable = false;
            }

            public void Unlock()
            {
                IsUsable = true;
            }
        }
#if CYG_DEBUG
        protected abstract IReadOnlyList<IAutoPlayActionInfo> DebugGetActionInfoList();
        IReadOnlyList<IAutoPlayActionInfo> ICommandSelector.DebugGetActionInfoList()
        {
            return UsableActionInfoList(DebugGetActionInfoList());
        }

        IAutoPlayActionInfo ICommandSelector.DebugGetUseActionInfo()
        {
            return SelectAction().ActionInfo;
        }
#endif
    }
}