#if CYG_DEBUG
using System.Collections.Generic;
#endif

namespace Gallop.SingleModeAutoPlay
{
    public interface ICommandSelector
    {
        /// <summary> 育成コマンドを選択 </summary>
        ISingleModeAutoPlayAgentAction SelectCommand();

        /// <summary> トレーニングコマンドを選択 </summary>
        ISingleModeAutoPlayAgentAction SelectTraining(AutoPlayTrainingActionParameter parameter);

#if CYG_DEBUG
        IReadOnlyList<IAutoPlayActionInfo> DebugGetActionInfoList();
        IAutoPlayActionInfo DebugGetUseActionInfo();
#endif
    }
}