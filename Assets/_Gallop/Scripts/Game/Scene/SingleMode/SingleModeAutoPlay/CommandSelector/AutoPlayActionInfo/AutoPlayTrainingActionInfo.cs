using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// トレーニングコマンドを実行するかの重み付け行う
    /// </summary>
    public partial class AutoPlayTrainingActionInfo : IAutoPlayTrainingCommand
    {
        /// <summary>
        /// 重み付けに必要な情報をまとめたクラス
        /// </summary>
        public class AutoPlayTrainingCommandRecord
        {
            public WorkSingleModeCharaData WorkCharaData;
            public IReadOnlyList<AutoPlayTrainingHorseRecord> HorseRecordList;
            public TrainingDefine.TrainingCommandId CommandId;
        }

        /// <summary>
        /// トレーニングコマンドごとの併せウマ情報
        /// </summary>
        public class AutoPlayTrainingHorseRecord
        {
            public int SupportCardId;
            public bool IsGuest;
            public bool HasTips;
            public bool IsTagTraining;
        }

        /// <summary>
        /// トレーニングの情報を追加しているクラス
        /// </summary>
        public class AutoPlayTrainingRecord : AutoPlayTrainingCommandRecord
        {
            public int TrainingFailureRate;
        }

        #region IAutoPlayTrainingCommand
        int IAutoPlayActionInfo.Weight => HighestWeightCommandData.Weight;
        int IAutoPlayActionInfo.TargetCount => HighestWeightCommandDataList?.Count ?? 0;

        bool IAutoPlayModifiableFilterActionInfo.IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            return IsMatchCondition(filterRecordList);
        }

        IReadOnlyList<AutoPlayFilterRecord> IAutoPlayModifiableFilterActionInfo.ApplicableFilterRecordList => ApplicableFilterRecordList;

        IReadOnlyList<int> IAutoPlayModifiableFilterActionInfo.UsableCommandIdList
        {
            get
            {
                if (LockCommandIdList == null) return DefaultUsableCommandIdList;
                return DefaultUsableCommandIdList.Except(LockCommandIdList).ToList();
            }
        }

        void IAutoPlayModifiableFilterActionInfo.SetLockCommandIdList(IReadOnlyList<int> lockCommandIdList)
        {
            LockCommandIdList = lockCommandIdList;
        }
        #endregion IAutoPlayTrainingCommand

        protected virtual IReadOnlyList<int> DefaultUsableCommandIdList
        {
            get
            {
                var list = new List<int>();
                if (_weightRepository == null) return list;
                list.AddRangeSafely(_weightRepository.GetTrainingAutoPlayCommandIdList(_recordList.Select(record => record.CommandId).ToList()));
                return list;
            }
        }

        protected IReadOnlyList<int> LockCommandIdList { get; private set; }
        protected IReadOnlyList<AutoPlayFilterRecord> ApplicableFilterRecordList { get; private set; }

        protected AutoPlayTrainingActionInfoSelectedSkillContext SelectedSkillContext { get; } =
            new AutoPlayTrainingActionInfoSelectedSkillContext();

        public TrainingDefine.TrainingCommandId CommandId => HighestWeightCommandData.CommandId;

        private (TrainingDefine.TrainingCommandId CommandId, int Weight) HighestWeightCommandData
        {
            get
            {
                _cacheHighestWeightTrainingCommandData = HighestWeightCommandDataList?.GetByRandom() ?? (TrainingDefine.TrainingCommandId.None, 0);
                return _cacheHighestWeightTrainingCommandData;
            }
        }

        private IReadOnlyList<(TrainingDefine.TrainingCommandId CommandId, int Weight)> HighestWeightCommandDataList
        {
            get
            {
                if (_cacheHighestWeightTrainingCommandDataList == null)
                {
                    var trainingCommandDataArray = CalculateTrainingCommandWeight();
                    _cacheHighestWeightTrainingCommandDataList = trainingCommandDataArray
                        ?.GroupBy(x => x.Weight)
                        .OrderByDescending(pair => pair.Key) // 重みが大きい順にソート
                        .FirstOrDefault()
                        ?.ToList();
                }

                return _cacheHighestWeightTrainingCommandDataList;
            }
        }

        private IReadOnlyList<AutoPlayTrainingCommandRecord> _recordList;
        private readonly ICommonSingleModeAutoPlayWeightRepository _weightRepository;
        private (TrainingDefine.TrainingCommandId CommandId, int Weight) _cacheHighestWeightTrainingCommandData;
        private IReadOnlyList<(TrainingDefine.TrainingCommandId CommandId, int Weight)> _cacheHighestWeightTrainingCommandDataList;

        public AutoPlayTrainingActionInfo(IReadOnlyList<AutoPlayTrainingCommandRecord> recordList, ICommonSingleModeAutoPlayWeightRepository weightRepository)
        {
            _recordList = recordList;
            _weightRepository = weightRepository;
        }

        protected virtual int GetTagTrainingHorseCount(IReadOnlyList<AutoPlayTrainingHorseRecord> horseList)
        {
            return horseList.Count(horse => !horse.IsGuest && horse.IsTagTraining);
        }

        protected virtual int GetTipsCount(IReadOnlyList<AutoPlayTrainingHorseRecord> horseList)
        {
            return horseList.Count(horse => !horse.IsGuest && horse.HasTips);
        }

        protected virtual int GetTrainingHorseCount(IReadOnlyList<AutoPlayTrainingHorseRecord> horseList)
        {
            return horseList.Count(horse => !horse.IsGuest);
        }

        /// <summary>
        /// 重み付けの計算をして、最大の重みとコマンドを返す
        /// </summary>
        private (TrainingDefine.TrainingCommandId TrainingCommandId, int Weight)[] CalculateTrainingCommandWeight()
        {
            if (_recordList == null) return null;

            return _recordList
                .Where(x =>
                {
                    if (LockCommandIdList.IsNullOrEmptyReadOnlyList()) return true;
                    var autoPlayCommandId = _weightRepository.GetTrainingAutoPlayCommandId(x.CommandId);
                    return LockCommandIdList.All(lockCommandId => lockCommandId != autoPlayCommandId);
                })
                .Select(x =>
                {
                    // 基礎重み
                    var baseWeight = _weightRepository.GetTrainingBaseWeight(x.CommandId);

                    var totalAddWeight = TotalAddWeight(x);

                    // NOTE: ConditionSetId が同じ重みデータはアンド条件。
                    //       条件を満たしていないものは重みが 0 で返る想定。
                    var totalAddWeightValue = totalAddWeight
                        .GroupBy(record => record.ConditionSetId)
                        .Sum(recordGroup => recordGroup?.Min(x => x.Weight) ?? 0);
                    var autoPlayCommandId = _weightRepository.GetTrainingAutoPlayCommandId(x.CommandId);
                    var preferenceWeightValue = ApplicableFilterRecordList?.Sum(filterRecord => filterRecord.GetPreferenceWeight(autoPlayCommandId)) ?? 0;
                    var totalWeightValue = baseWeight + totalAddWeightValue + preferenceWeightValue;

                    return (x.CommandId, totalWeightValue);
                })
                .ToArray();
        }

        /// <summary>
        /// 各トレーニングの重み付けを合算する
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> TotalAddWeight(AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            var horseList = autoPlayTrainingCommandRecord.HorseRecordList;
            var commandId = autoPlayTrainingCommandRecord.CommandId;

            var totalWeightList = new List<IWeightRecord>();

            // 野良ウマ娘以外がいる人数で算出される重み
            totalWeightList.AddRangeSafely(NotGuestHorseWeight(GetTrainingHorseCount(horseList), commandId));

            // 友情トレーニングの人数で算出される重み
            totalWeightList.AddRangeSafely(TagTrainingWeight(GetTagTrainingHorseCount(horseList), commandId));

            // スキルヒントの有無で算出される重み
            totalWeightList.AddRangeSafely(SkillHintWeight(GetTipsCount(horseList), commandId));

            // 優先スキルで算出される重み
            var workCharaData = autoPlayTrainingCommandRecord.WorkCharaData;
            var hasTipsSupportCardIdList = horseList.Where(x => x.HasTips).Select(x => x.SupportCardId).ToList();
            totalWeightList.AddRangeSafely(SelectedSkillGettableTipsTrainingWeight(workCharaData, hasTipsSupportCardIdList, commandId));
            totalWeightList.AddRangeSafely(GetSelectedSkillGettableTipsAndNotHaveWeightRecordList(workCharaData, hasTipsSupportCardIdList, commandId));

            // 絆が指定範囲内の併せウマ数で算出される重み
            totalWeightList.AddRangeSafely(EvaluationHorseWeight(workCharaData, horseList, commandId));

            return totalWeightList;
        }

        /// <summary>
        /// 野良ウマ娘以外がいる人数での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> NotGuestHorseWeight(
            int trainingHorseCount,
            TrainingDefine.TrainingCommandId commandId)
        {
            var weightRecordList = _weightRepository.GetTrainingHorseCountWeightRecordList(commandId);
            var result = new List<IWeightRecord>();

            IReadOnlyList<IWeightRecord> convertWeightModelList = weightRecordList?
                .Select(x => new ConditionValueRangeWeightModel(x, trainingHorseCount))
                .ToList();

            result.AddRangeSafely(convertWeightModelList);
            result.AddRangeSafely(ModifyNotGuestHorseWeight(trainingHorseCount, commandId));

            return result;
        }

        /// <summary>
        /// ユーザ設定による野良ウマ娘以外がいる人数での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ModifyNotGuestHorseWeight(
            int trainingHorseCount,
            TrainingDefine.TrainingCommandId commandId)
        {
            var modifyWeightRecordList = _weightRepository.GetTrainingHorseCountModifyWeightRecordList(commandId);
            return modifyWeightRecordList
                .Select(x => new ConditionValueRangeWeightModel(x, trainingHorseCount))
                .ToList();
        }

        /// <summary>
        /// 友情トレーニングの人数での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> TagTrainingWeight(
            int tagTrainingCount,
            TrainingDefine.TrainingCommandId commandId)
        {
            var weightRecordList = _weightRepository.GetTagTrainingWeightRecordList(commandId);

            var result = new List<IWeightRecord>();

            IReadOnlyList<IWeightRecord> convertWeightModelList = weightRecordList?
                .Select(x => new ConditionValueRangeWeightModel(x, tagTrainingCount))
                .ToList();

            result.AddRangeSafely(convertWeightModelList);
            result.AddRangeSafely(ModifyTagTrainingWeight(tagTrainingCount, commandId));
            return result;
        }

        /// <summary>
        /// ユーザ設定による友情トレーニング人数での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ModifyTagTrainingWeight(int tagTrainingCount, TrainingDefine.TrainingCommandId commandId)
        {
            var modifyWeightRecordList = _weightRepository.GetTagTrainingModifyWeightRecordList(commandId);
            return modifyWeightRecordList
                .Select(x => new ConditionValueRangeWeightModel(x, tagTrainingCount))
                .ToList();
        }

        /// <summary>
        /// スキルヒントの有無での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> SkillHintWeight(
            int skillHintCount,
            TrainingDefine.TrainingCommandId commandId)
        {
            var weightRecordList = _weightRepository.GetTipsTrainingWeightRecordList(commandId);

            var result = new List<IWeightRecord>();

            IReadOnlyList<IWeightRecord> convertWeightModelList = weightRecordList?
                .Select(x => new ConditionValueRangeWeightModel(x, skillHintCount))
                .ToList();

            result.AddRangeSafely(convertWeightModelList);
            result.AddRangeSafely(ModifyTipsTrainingWeight(skillHintCount, commandId));
            return result;
        }

        /// <summary>
        /// ユーザ設定によるスキルヒントの有無での重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ModifyTipsTrainingWeight(
            int hasTipsCount,
            TrainingDefine.TrainingCommandId commandId)
        {
            var modifyWeightRecordList = _weightRepository.GetTipsTrainingModifyWeightRecordList(commandId);
            return modifyWeightRecordList
                ?.Select(x => new ConditionValueRangeWeightModel(x, hasTipsCount))
                .ToList();
        }

        /// <summary>
        /// 設定しているスキルのヒントを取得できる可能性がある場合の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> SelectedSkillGettableTipsTrainingWeight(
            WorkSingleModeCharaData workCharaData,
            IReadOnlyList<int> hasTipsSupportCardIdList,
            TrainingDefine.TrainingCommandId commandId)
        {
            // ヒントイベントが発生しないなら無視
            if (hasTipsSupportCardIdList.IsNullOrEmptyReadOnlyList()) return null;

            var selectedSkillIdList = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord?.ModifyPreferenceSkillIdList;

            // 優先スキルの設定をしていないなら無視
            if (selectedSkillIdList.IsNullOrEmptyReadOnlyList()) return null;

            var weightRecordList = _weightRepository.GetSelectedSkillGettableTipsTrainingWeightRecordList(commandId);

            var result = new List<IWeightRecord>();
            result.AddRangeSafely(SelectedSkillContext.CalculateSelectedSkillGettableTipsTrainingWeight(weightRecordList, selectedSkillIdList, hasTipsSupportCardIdList, workCharaData));
            result.AddRangeSafely(ModifySelectedSkillGettableTipsTrainingWeight(workCharaData, selectedSkillIdList, hasTipsSupportCardIdList, commandId));
            return result;
        }

        /// <summary>
        /// ユーザー設定による「設定しているスキルのヒントを取得できる可能性がある場合」の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ModifySelectedSkillGettableTipsTrainingWeight(
            WorkSingleModeCharaData workCharaData,
            IReadOnlyList<int> selectedSkillIdList,
            IReadOnlyList<int> hasTipsSupportCardIdList,
            TrainingDefine.TrainingCommandId commandId)
        {
            var modifyWeightRecordList = _weightRepository.GetSelectedSkillGettableTipsTrainingModifyWeightRecordList(commandId);
            return SelectedSkillContext.CalculateSelectedSkillGettableTipsTrainingWeight(modifyWeightRecordList, selectedSkillIdList, hasTipsSupportCardIdList, workCharaData);
        }

        /// <summary>
        /// 絆80未満の併せウマ数の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> EvaluationHorseWeight(
            WorkSingleModeCharaData workCharaData,
            IReadOnlyList<AutoPlayTrainingHorseRecord> horseRecordList,
            TrainingDefine.TrainingCommandId commandId)
        {
            var evaluationList = horseRecordList
                ?.Select(horseRecord =>
                {
                    if (horseRecord.IsGuest) return null;

                    // サポカを編成していない場合は無視する
                    var supportCardId = horseRecord.SupportCardId;
                    var equipSupportCard = workCharaData.GetEquipSupportCardBySupportCardId(supportCardId);
                    if (equipSupportCard == null) return null;

                    var position = equipSupportCard.Position;
                    return workCharaData.GetEvaluation(position);
                })
                .RemoveNull()
                .ToList();
            if (evaluationList.IsNullOrEmpty()) return null;

            var lowEvaluationCount = evaluationList.Count(evaluation =>
                evaluation.Value < SingleModeDefine.EVALUATION_TAG_ENABLE_VALUE);

            var weightRecordList = _weightRepository.GetEvaluationHorseWeightRecordList(commandId);
            var convertWeightModelList = weightRecordList
                .Select(weightRecord => new ConditionValueRangeWeightModel(weightRecord, lowEvaluationCount))
                .ToList();

            var result = new List<IWeightRecord>();
            result.AddRangeSafely(convertWeightModelList);
            result.AddRangeSafely(ModifyEvaluationHorseWeight(lowEvaluationCount, commandId));
            return result;
        }

        /// <summary>
        /// ユーザー設定による絆80未満の併せウマ数の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> ModifyEvaluationHorseWeight(
            int lowEvaluationCount,
            TrainingDefine.TrainingCommandId commandId)
        {
            var modifyWeightRecordList = _weightRepository.GetEvaluationHorseModifyWeightRecordList(commandId);
            return modifyWeightRecordList
                .Select(weightRecord => new ConditionValueRangeWeightModel(weightRecord, lowEvaluationCount))
                .ToList();
        }

        /// <summary>
        /// 設定しているスキルのヒントを持っておらず、そのスキルのヒントを取得できる可能性がある場合の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> GetSelectedSkillGettableTipsAndNotHaveWeightRecordList(
            WorkSingleModeCharaData workCharaData,
            IReadOnlyList<int> hasTipsSupportCardIdList,
            TrainingDefine.TrainingCommandId commandId)
        {
            // ヒントイベントが発生しないなら無視
            if (hasTipsSupportCardIdList.IsNullOrEmptyReadOnlyList()) return null;

            var selectedSkillIdList = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord?.ModifyPreferenceSkillIdList;

            // 優先スキルの設定をしていないなら無視
            if (selectedSkillIdList.IsNullOrEmptyReadOnlyList()) return null;

            var weightRecordList = _weightRepository.GetSelectedSkillGettableTipsAndNotHaveWeightRecordList(commandId);

            var result = new List<IWeightRecord>();
            result.AddRangeSafely(SelectedSkillContext.CalculateSelectedSkillGettableTipsAndNotHaveWeight(weightRecordList, selectedSkillIdList, hasTipsSupportCardIdList, workCharaData));
            result.AddRangeSafely(GetSelectedSkillGettableTipsAndNotHaveModifyWeightRecordList(workCharaData, selectedSkillIdList, hasTipsSupportCardIdList, commandId));
            return result;
        }


        /// ユーザー設定による「設定しているスキルのヒントを持っておらず、そのスキルのヒントを取得できる可能性がある場合」の重み
        /// </summary>
        protected virtual IReadOnlyList<IWeightRecord> GetSelectedSkillGettableTipsAndNotHaveModifyWeightRecordList(
            WorkSingleModeCharaData workCharaData,
            IReadOnlyList<int> selectedSkillIdList,
            IReadOnlyList<int> hasTipsSupportCardIdList,
            TrainingDefine.TrainingCommandId commandId)
        {
            var modifyWeightRecordList = _weightRepository.GetSelectedSkillGettableTipsAndNotHaveModifyWeightRecordList(commandId);
            return SelectedSkillContext.CalculateSelectedSkillGettableTipsAndNotHaveWeight(modifyWeightRecordList, selectedSkillIdList, hasTipsSupportCardIdList, workCharaData);
        }

        /// <summary>
        /// フィルタ情報を適用した場合に適用条件を満たすか
        /// </summary>
        protected virtual bool IsMatchCondition(IReadOnlyList<AutoPlayFilterRecord> filterRecordList)
        {
            // 受け取ったフィルタ情報のうち、適用種別と条件を満たすものを格納する
            ApplicableFilterRecordList = filterRecordList?
                .Select(ApplicableFilterRecord)
                .Where(record => record != null)
                .ToList();

            // 適用種別と条件を満たすフィルタ情報があるか返す
            return ApplicableFilterRecordList != null && ApplicableFilterRecordList.Count > 0;
        }

        protected virtual AutoPlayFilterRecord ApplicableFilterRecord(AutoPlayFilterRecord autoPlayFilterRecord)
        {
            var targetAutoPlayCommandId = autoPlayFilterRecord.FilterRecord.CommandId;
            if (!DefaultUsableCommandIdList.Contains(targetAutoPlayCommandId)) return null;

            var filterConditionList = autoPlayFilterRecord.FilterConditionList;
            var trainingConditionModel = CreateTrainingConditionModel(filterConditionList);

            // 適用種別がそもそも合わない場合は null
            if (trainingConditionModel.IsAllNullCondition) return null;

            var record = _recordList.FirstOrDefault(x => _weightRepository.GetTrainingAutoPlayCommandId(x.CommandId) == targetAutoPlayCommandId);
            if (record == null) return null;

            var isApplicableConditionList = CreateIsApplicableTrainingConditionList(trainingConditionModel, record);

            if (isApplicableConditionList.IsNullOrEmptyReadOnlyList()) return null;

            // 適用種別が合うものがあるなら、それらがアンド条件として満たされるかを確認
            var isApplicableCondition = isApplicableConditionList.All(isApplicableCondition => isApplicableCondition ?? true);

            // アンド条件として満たされるなら適用種別と条件を満たすフィルタ情報を返す
            return isApplicableCondition ? autoPlayFilterRecord : null;
        }

        protected AutoPlayTrainingConditionModel CreateTrainingConditionModel(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            return new AutoPlayTrainingConditionModel()
            {
                TagTrainingConditionList = GetTagTrainingConditionList(filterConditionList),
                TipsTrainingConditionList = GetTipsTrainingConditionList(filterConditionList),
                TrainingHorseCountConditionList = GetTrainingHorseCountConditionList(filterConditionList),
                MaxStatusConditionList = GetMaxStatusConditionList(filterConditionList),
                EvaluationHorseConditionList = GetEvaluationHorseConditionList(filterConditionList),
            };
        }

        protected virtual IReadOnlyList<bool?> CreateIsApplicableTrainingConditionList(
            AutoPlayTrainingConditionModel autoPlayTrainingConditionModel,
            AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            var conditionList = new List<bool?>();

            conditionList.TryAddValue(IsApplicableTagTrainingCondition(autoPlayTrainingConditionModel, autoPlayTrainingCommandRecord));
            conditionList.TryAddValue(IsApplicableTipsTrainingCondition(autoPlayTrainingConditionModel, autoPlayTrainingCommandRecord));
            conditionList.TryAddValue(IsApplicableTrainingHorseCountCondition(autoPlayTrainingConditionModel, autoPlayTrainingCommandRecord));
            conditionList.TryAddValue(IsApplicableMaxStatusCondition(autoPlayTrainingConditionModel, autoPlayTrainingCommandRecord));
            conditionList.TryAddValue(IsApplicableEvaluationHorseCondition(autoPlayTrainingConditionModel, autoPlayTrainingCommandRecord));

            return conditionList;
        }

        /// <summary>
        /// 友情状態の併せウマ数を条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetTagTrainingConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var tagTrainingConditionList = _weightRepository?.GetTagTrainingConditionList(filterConditionList);
            if (tagTrainingConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return tagTrainingConditionList;
        }

        /// <summary>
        /// 友情状態の併せウマ数を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableTagTrainingCondition(AutoPlayTrainingConditionModel autoPlayTrainingConditionModel,
            AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            if (autoPlayTrainingConditionModel?.IsNullTagTrainingConditionList ?? true) return null;

            if (autoPlayTrainingCommandRecord == null) return false;

            var tagTrainingCount = autoPlayTrainingCommandRecord
                .HorseRecordList
                .Count(horse => !horse.IsGuest && horse.IsTagTraining);

            return autoPlayTrainingConditionModel
                .TagTrainingConditionList
                .All(condition => condition.ConditionValue1 <= tagTrainingCount &&
                                  condition.ConditionValue2 >= tagTrainingCount);
        }

        /// <summary>
        /// ヒントイベントを条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetTipsTrainingConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var tipsTrainingConditionList = _weightRepository?.GetTipsTrainingConditionList(filterConditionList);
            if (tipsTrainingConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return tipsTrainingConditionList;
        }

        /// <summary>
        /// ヒントイベントを条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableTipsTrainingCondition(AutoPlayTrainingConditionModel autoPlayTrainingConditionModel, AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            if (autoPlayTrainingConditionModel?.IsNullTipsTrainingConditionList ?? true) return null;

            if (autoPlayTrainingCommandRecord == null) return false;
            
            var skillHintCount = autoPlayTrainingCommandRecord
                .HorseRecordList
                .Count(horse => !horse.IsGuest && horse.HasTips);

            return autoPlayTrainingConditionModel
                .TipsTrainingConditionList
                .All(condition => condition.ConditionValue1 <= skillHintCount &&
                                  condition.ConditionValue2 >= skillHintCount);
        }

        /// <summary>
        /// 併せウマ数（サポカのみ）を条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetTrainingHorseCountConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var trainingHorseCountConditionList = _weightRepository?.GetTrainingHorseCountConditionList(filterConditionList);
            if (trainingHorseCountConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return trainingHorseCountConditionList;
        }

        /// <summary>
        /// 併せウマ数（サポカのみ）を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableTrainingHorseCountCondition(AutoPlayTrainingConditionModel autoPlayTrainingConditionModel, AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            if (autoPlayTrainingConditionModel?.IsNullTrainingHorseCountConditionList ?? true) return null;

            if (autoPlayTrainingCommandRecord == null) return false;

            var trainingHorseCount = autoPlayTrainingCommandRecord.HorseRecordList.Count(horse => !horse.IsGuest);

            return autoPlayTrainingConditionModel
                .TrainingHorseCountConditionList
                .All(condition => condition.ConditionValue1 <= trainingHorseCount &&
                                  condition.ConditionValue2 >= trainingHorseCount);
        }

        /// <summary>
        /// ステータスが最大かを条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetMaxStatusConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var maxStatusConditionList = _weightRepository?.GetMaxStatusConditionList(filterConditionList);
            if (maxStatusConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return maxStatusConditionList;
        }

        /// <summary>
        /// ステータスが最大かを条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableMaxStatusCondition(AutoPlayTrainingConditionModel autoPlayTrainingConditionModel, AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            const int SPEED = 1, STAMINA = 2, POWER = 3, GUTS = 4, WIZ = 5;

            if (autoPlayTrainingConditionModel?.IsNullMaxStatusConditionList ?? true) return null;

            if (autoPlayTrainingCommandRecord == null) return false;

            var workCharaData = autoPlayTrainingCommandRecord.WorkCharaData;
            return autoPlayTrainingConditionModel
                .MaxStatusConditionList
                .All(condition =>
                {
                    return condition.ConditionValue1 switch
                    {
                        SPEED => workCharaData.Speed >= workCharaData.MaxSpeed,
                        STAMINA => workCharaData.Stamina >= workCharaData.MaxStamina,
                        POWER => workCharaData.Power >= workCharaData.MaxPower,
                        GUTS => workCharaData.Guts >= workCharaData.MaxGuts,
                        WIZ => workCharaData.Wiz >= workCharaData.MaxWiz,
                        _ => false,
                    };
                });
        }

        /// <summary>
        /// 絆80未満の併せウマ数を条件とするフィルタリストを取得
        /// </summary>
        protected IReadOnlyList<IConditionFilterRecord> GetEvaluationHorseConditionList(IReadOnlyList<IConditionFilterRecord> filterConditionList)
        {
            var evaluationHorseConditionList = _weightRepository?.GetEvaluationHorseConditionList(filterConditionList);
            if (evaluationHorseConditionList.IsNullOrEmptyReadOnlyList()) return null;
            return evaluationHorseConditionList;
        }

        /// <summary>
        /// 絆80未満の併せウマ数を条件とするフィルタの適用条件を満たすか
        /// </summary>
        protected bool? IsApplicableEvaluationHorseCondition(AutoPlayTrainingConditionModel autoPlayTrainingConditionModel, AutoPlayTrainingCommandRecord autoPlayTrainingCommandRecord)
        {
            if (autoPlayTrainingConditionModel?.IsNullEvaluationHorseConditionList ?? true) return null;

            if (autoPlayTrainingCommandRecord == null) return false;

            var workCharaData = autoPlayTrainingCommandRecord.WorkCharaData;

            var evaluationList = autoPlayTrainingCommandRecord.HorseRecordList
                ?.Select(horseRecord =>
                {
                    if (horseRecord.IsGuest) return null;

                    // サポカを編成していない場合は無視する
                    var supportCardId = horseRecord.SupportCardId;
                    var equipSupportCard = workCharaData.GetEquipSupportCardBySupportCardId(supportCardId);
                    if (equipSupportCard == null) return null;

                    var position = equipSupportCard.Position;
                    return workCharaData.GetEvaluation(position);
                })
                .RemoveNull()
                .ToList();
            if (evaluationList.IsNullOrEmpty()) return false;

            var lowEvaluationCount = evaluationList.Count(evaluation => evaluation.Value < SingleModeDefine.EVALUATION_TAG_ENABLE_VALUE);

            return autoPlayTrainingConditionModel
                .EvaluationHorseConditionList
                .All(condition => condition.ConditionValue1 <= lowEvaluationCount &&
                                  condition.ConditionValue2 >= lowEvaluationCount);
        }

        protected class AutoPlayTrainingConditionModel
        {
            /// <summary> 友情状態の併せウマ数を条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> TagTrainingConditionList;

            public bool IsNullTagTrainingConditionList => TagTrainingConditionList == null;

            /// <summary> ヒントイベントを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> TipsTrainingConditionList;

            public bool IsNullTipsTrainingConditionList => TipsTrainingConditionList == null;

            /// <summary> 併せウマ数（サポカのみ）を条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> TrainingHorseCountConditionList;

            public bool IsNullTrainingHorseCountConditionList => TrainingHorseCountConditionList == null;

            /// <summary> ステータスがMAXかを条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> MaxStatusConditionList;
            public bool IsNullMaxStatusConditionList => MaxStatusConditionList == null;

            /// <summary> 絆80未満の併せウマ数を条件とするフィルタリスト </summary>
            public IReadOnlyList<IConditionFilterRecord> EvaluationHorseConditionList;
            public bool IsNullEvaluationHorseConditionList => EvaluationHorseConditionList == null;

            /// <summary> トレーニングに関連するフィルタ情報が存在しないか </summary>
            public virtual bool IsAllNullCondition => IsNullTagTrainingConditionList &&
                                                      IsNullTipsTrainingConditionList &&
                                                      IsNullTrainingHorseCountConditionList &&
                                                      IsNullMaxStatusConditionList &&
                                                      IsNullEvaluationHorseConditionList;
        }
    }
}