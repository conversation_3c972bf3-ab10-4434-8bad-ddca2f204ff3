using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleModeAutoPlay
{
    public partial class AutoPlayPauseInfo
    {
        /// <summary>
        /// コマンド選択時に一時停止する必要があるか
        /// </summary>
        public bool NeedAutoPlayPauseBySelectCommand()
        {
            var currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;

            // 有効なプランが存在しなければ終了
            if (currentPlanRecord == null) return false;

            // 現在有効なコマンド選択時の一時停止条件種別をリストアップ
            var stopperConditionIdList = currentPlanRecord.ModifyStopperConditionIdList;

            if (stopperConditionIdList.IsNullOrEmptyReadOnlyList())
            {
                return false;
            }

            var selectCommandPauseConditionTypeList = stopperConditionIdList
                .Select(x => MasterDataManager.Instance.masterOmakaseStopCondition.Get(x).ConditionType)
                .Distinct()
                .Where(IsSelectCommandPauseConditionType)
                .ToList();

            // 一時停止が必要な条件を満たす場合は、一時停止する必要があると判定する
            return selectCommandPauseConditionTypeList.Any(NeedAutoPlayPauseCondition);
        }

        /// <summary>
        /// ストーリーイベントで一時停止する必要があるか
        /// </summary>
        public bool NeedAutoPlayPauseByStopperEvent(int storyId)
        {
            var master = MasterDataManager.Instance.masterOmakaseStopperEvent.GetWithStoryId(storyId);
            var currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
            if (currentPlanRecord == null || master == null) return false;

            var isPause = master.StopType == 1;
            if (!isPause) return false;

            // 一時停止をユーザーが切り替えられるイベントでなければ、強制的に一時停止させる
            var isModifiable = master.IsModifiable == 1;
            if (!isModifiable) return true;

            var masterStopConditionList = MasterDataManager.Instance.masterOmakaseStopCondition
                .GetListWithScenarioIdOrderByStopConditionIdAsc(currentPlanRecord.ScenarioId);

            var masterScenarioGimmickCondition = masterStopConditionList
                .FirstOrDefault(x => x.ConditionType == (int)AutoPlayPauseConditionType.ScenarioGimmick);

            if (masterScenarioGimmickCondition != null &&
                (currentPlanRecord.ModifyStopperConditionIdList?.Contains(masterScenarioGimmickCondition.StopConditionId) ?? false))
            {
                var masterScenarioStopEventCondition = masterStopConditionList
                    .FirstOrDefault(x => x.ConditionType == (int)AutoPlayPauseConditionType.StoryEvent);

                return masterScenarioStopEventCondition != null && 
                       (currentPlanRecord.ModifyStopperConditionIdList?.Contains(masterScenarioStopEventCondition.StopConditionId) ?? false);
            }

            // 現在のプランで一時停止をするイベントのIDリストに含まれるなら一時停止が必要
            var storyStopperIdList = currentPlanRecord.ModifyStoryStopperIdList;
            return storyStopperIdList?.Contains(master.StopperId) ?? false;
        }

        private bool NeedAutoPlayPauseCondition(int pauseConditionType)
        {
            var currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;

            // 有効なプランが存在しなければ終了
            if (currentPlanRecord == null) return false;

            // 現在有効な一時停止トリガー種別をリストアップ
            var stopperTriggerIdList = currentPlanRecord.ModifyStopperTriggerIdList;

            // 有効な一時停止トリガー種別が無ければ終了
            if (stopperTriggerIdList.IsNullOrEmptyReadOnlyList()) return false;

            var triggerTypeList = stopperTriggerIdList
                .Select(x => MasterDataManager.Instance.masterOmakaseStopTrigger.Get(x).TriggerType)
                .ToList();

            // 一時停止条件と一時停止トリガーの組み合わせで一時停止が必要かをチェックする
            return pauseConditionType switch
            {
                (int)AutoPlayPauseConditionType.MultiTargetCommand => IsMultiTargetCommand(triggerTypeList),
                _ => CustomNeedAutoPlayPauseCondition(pauseConditionType, triggerTypeList),
            };
        }

        /// <summary>
        /// シナリオ別で定義される一時停止条件と、有効な一時停止トリガーから一時停止する必要があるかチェックする
        /// </summary>
        protected virtual bool CustomNeedAutoPlayPauseCondition(int pauseConditionType, IReadOnlyList<int> pauseTriggerTypeList)
        {
            return false;
        }

        /// <summary>
        /// トレーニングコマンドごとに一時停止チェック用のModelへ変換する
        /// </summary>
        protected virtual IReadOnlyList<AutoPlayPauseTrainingModel> CreateTrainingModelList(IReadOnlyList<int> pauseTriggerTypeList)
        {
            WorkDataManager.Instance.SingleMode.HomeInfo.TurnInfoListDic.TryGetValue(SingleModeDefine.CommandType.Training, out var turnInfoList);
            return turnInfoList
                .Select(x =>
                {
                    var trainingHorseList = x.TrainingHorseList
                        .Where(trainingHorse => !trainingHorse.IsGuest)
                        .Select(trainingHorse =>
                        {
                            var isTagTraining = trainingHorse.IsTagTraining(SingleModeDefine.CommandType.Training, x.CommandId);
                            var hasTips = trainingHorse.IsTips;
                            return new AutoPlayPauseTrainingHorseRecord(isTagTraining, hasTips);
                        })
                        .ToList();
                    return new AutoPlayPauseTrainingModel(trainingHorseList, pauseTriggerTypeList);
                })
                .ToList();
        }

        /// <summary>
        /// レースコマンドの一時停止チェック用のModelを作成
        /// </summary>
        protected virtual AutoPlayPauseRaceModel CreateRaceModel(IReadOnlyList<int> pauseTriggerTypeList)
        {
            var raceReserveContext = WorkDataManager.Instance.SingleMode.RaceReserveContext;
            return new AutoPlayPauseRaceModel(raceReserveContext, pauseTriggerTypeList);
        }

        /// <summary>
        /// 一時停止トリガーを満たすコマンドが複数存在するか
        /// </summary>
        private bool IsMultiTargetCommand(IReadOnlyList<int> pauseTriggerTypeList)
        {
            // トレーニングコマンドで一時停止トリガーを満たすコマンド数を算出
            var trainingModelList = CreateTrainingModelList(pauseTriggerTypeList);
            var hasPauseTriggerCountByTraining = trainingModelList.Count(x => x.HasApplicablePauseTrigger);

            // レースコマンドで一時停止トリガーを満たすコマンド数を算出
            var raceModel = CreateRaceModel(pauseTriggerTypeList);
            var hasPauseTriggerCountByRace = raceModel.HasApplicablePauseTrigger ? 1 : 0;

            // シナリオ別の一時停止トリガーを満たすコマンド数を算出
            var customHasPauseTriggerCount = CustomHasPauseTriggerCountByIsMultiTargetCommand(pauseTriggerTypeList);

            // 一時停止トリガーを満たすコマンド数を合算
            var totalCount = hasPauseTriggerCountByTraining +
                             hasPauseTriggerCountByRace +
                             customHasPauseTriggerCount;

            return totalCount > 1;
        }

        /// <summary>
        /// シナリオ別の一時停止トリガーを満たすコマンドが複数存在するか
        /// </summary>
        protected virtual int CustomHasPauseTriggerCountByIsMultiTargetCommand(IReadOnlyList<int> pauseTriggerTypeList)
        {
            return 0;
        }

        /// <summary>
        /// コマンド選択時にチェックが必要な一時停止条件かを判定
        /// </summary>
        private bool IsSelectCommandPauseConditionType(int pauseConditionType)
        {
            return pauseConditionType switch
            {
                (int)AutoPlayPauseConditionType.MultiTargetCommand => true,
                _ => CustomIsSelectCommandPauseConditionType(pauseConditionType),
            };
        }

        /// <summary>
        /// シナリオ別のコマンド選択時にチェックが必要な一時停止条件かを判定
        /// </summary>
        protected virtual bool CustomIsSelectCommandPauseConditionType(int pauseConditionType)
        {
            return false;
        }
    }
}