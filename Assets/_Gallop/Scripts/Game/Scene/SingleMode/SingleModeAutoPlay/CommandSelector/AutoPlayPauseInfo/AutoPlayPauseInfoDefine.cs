namespace Gallop.SingleModeAutoPlay
{
    public partial class AutoPlayPauseInfo
    {
        /// <summary>
        /// 一時停止のトリガー種別
        /// </summary>
        public enum AutoPlayPauseTriggerType
        {
            RaceReserve = 1, // 予約レースのターンの場合
            TagTraining = 2, // トレーニングで友情トレーニングが発生する場合
            HasTips = 3,     // ヒントイベントがある場合
        }

        /// <summary>
        /// 一時停止の条件種別
        /// </summary>
        public enum AutoPlayPauseConditionType
        {
            MultiTargetCommand = 1, // 停止トリガーを満たすコマンドが複数存在する場合
            StoryEvent = 2,   // 停止トリガーを満たすイベントがある場合
            ScenarioGimmick = 3, // 停止トリガーを満たすギミックがある場合
            ContinuableRace = 4, //コンテ可能なレース
            

            // 伝説編
            LegendBuffSelect = 1001, // バフ選択画面
        }
    }
}