using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// クラスパネル表示中のステート
    /// </summary>
    public class SingleModePaddockResultClassState : SingleModeAutoPlayAgentStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>
            {
                new RaceResultListTapSingleModeNextButtonAtClass()
            };
        }
    }
}