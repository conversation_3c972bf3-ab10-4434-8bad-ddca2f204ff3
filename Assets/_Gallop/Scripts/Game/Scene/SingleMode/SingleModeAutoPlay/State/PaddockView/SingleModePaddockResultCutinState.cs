using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModePaddockResultCutinState : SingleModeAutoPlayAgentStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>
            {
                new ResultCutinTapAction()
            };
        }
        
        void Update()
        {
            var raceResultList = FindObjectOfType<RaceResultList>();
            if (raceResultList != null)
            {
                ChangeState<SingleModePaddockResultRankState>();
            }
            
        }
    }
}