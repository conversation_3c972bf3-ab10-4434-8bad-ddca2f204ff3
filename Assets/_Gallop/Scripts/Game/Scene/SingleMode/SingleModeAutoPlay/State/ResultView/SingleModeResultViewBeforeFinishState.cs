using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModeResultViewBeforeFinishState : SingleModeResultViewStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>()
            {
                new TempResultViewAction(),
                new SingleModeResultFactorDecideConfirmAction(),
                new SingleModeResultFactorSelectAction(),
                new SingleModeResultShowRankScoreAction()
            };
        }
    }
}