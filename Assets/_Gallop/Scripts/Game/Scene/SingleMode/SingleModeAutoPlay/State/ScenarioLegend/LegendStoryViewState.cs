namespace Gallop.SingleModeAutoPlay
{
    public class LegendStoryViewState : StoryViewState
    {
        protected override ISingleModeAutoPlayAgentAction ChoiceAction { get; } = new LegendStoryChoiceAction();

        protected override void OnStateEnter()
        {
            base.OnStateEnter();

            var buffSelector = new LegendEventAutoPlayBuffSelector();

            ActionList.Add(new LegendBuffSelectAction(buffSelector));
            ActionList.Add(new LegendBuffSelectConfirmAction());
            ActionList.Add(new LegendBuffExchangeReleaseAction(buffSelector));
            ActionList.Add(new LegendBuffExchangeConfirmAction());
            ActionList.Add(new LegendCommercialConfirmAction());
        }
    }
}