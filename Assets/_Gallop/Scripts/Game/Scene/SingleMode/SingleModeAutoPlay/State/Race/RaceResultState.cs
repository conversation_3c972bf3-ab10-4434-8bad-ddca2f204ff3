using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class RaceResultState : SingleModeAutoPlayAgentStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>()
            {
                new RaceResultSkipCharacterMotionAction(),
                new RaceResultListPauseContinueAction(),
                new RaceResultListContinueAction(),
                new RaceResultListTapRivalRaceResultUIAction(),
                new RaceResultListTapContinueCancelButtonAction(),
                new RaceResultListTapSingleModeNextButton(),
                new RaceResultAction(),
                new DialogAddTrophyCloseAction()
            };
        }
    }
}