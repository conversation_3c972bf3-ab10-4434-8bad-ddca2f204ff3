using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModePaddockTopState : SingleModeAutoPlayAgentStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>
            {
                new WaitChangeViewAction(),
                new PaddcokWaitUniqueNPCAnimationAction(),
                new PaddockStartRaceAction(),
                new PaddockSkipRaceAction()
            };
        }
    }
}