using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// コンティニュー待機のステート
    /// </summary>
    public class SingleModePaddockResultContinueDialogState : PauseAutoPlayState
    {
        void Update()
        {
            //おまかせ育成は一時停止するが状態は監視しておきたい
            if (SceneManager.Instance.GetCurrentViewController() is PaddockViewControllerBase paddockView &&
                !paddockView.AutoPlayProxy.IsRaceStarted)
            {
                //パドックトップのステートに遷移
                GetParentStateMachine().ChangeState<SingleModePaddockTopState>();
            }
        }
    }
}