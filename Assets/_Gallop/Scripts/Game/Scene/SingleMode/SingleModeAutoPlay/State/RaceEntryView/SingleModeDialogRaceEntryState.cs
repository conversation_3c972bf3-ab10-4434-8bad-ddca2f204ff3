using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// 出走確認ダイアログ
    /// </summary>
    public class SingleModeDialogRaceEntryState : SingleModeAutoPlayAgentStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>
            {
                new DialogRaceEntryAction()
            };
        }
    }
}