using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModeResultShowRankScoreState : SingleModeResultViewStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>()
            {
                new SingleModeResultShowRankScoreAction()
            };
        }

        void Update()
        {
            if(Step == SingleModeResultSequence.Step.ShowFactor)
            {
                ChangeState<SingleModeResultShowFactorState>();
            }
        }
    }
}