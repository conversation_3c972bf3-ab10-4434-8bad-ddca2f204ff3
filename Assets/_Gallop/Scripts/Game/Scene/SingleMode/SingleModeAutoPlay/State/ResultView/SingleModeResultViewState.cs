using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModeResultViewStateBase : SingleModeAutoPlayAgentStateBase
    {
        public SingleModeResultViewController ViewController => SceneManager.Instance.GetCurrentViewController<SingleModeResultViewController>();
        public SingleModeResultSequence.Step Step => ViewController.AutoPlayProxy.Step;
    }

    public class SingleModeResultViewState : SingleModeResultViewStateBase
    {
        protected override void OnStateEnter()
        {
            ChangeSubState<SingleModeResultViewBeforeFinishState>();
        }
    }
}