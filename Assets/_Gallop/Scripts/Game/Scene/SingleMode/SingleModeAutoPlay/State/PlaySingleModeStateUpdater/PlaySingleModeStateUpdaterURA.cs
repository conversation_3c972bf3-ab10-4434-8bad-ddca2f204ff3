namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// URAシナリオでの育成中ステート更新処理
    /// </summary>
    public class PlaySingleModeStateUpdaterURA : PlaySingleModeStateUpdaterBase
    {
        public PlaySingleModeStateUpdaterURA(PlaySingleModeState state) : base(state)
        {
        }

        public override void UpdateState()
        {
            if (!WorkDataManager.HasInstance() ||
                !WorkDataManager.Instance.SingleMode.IsPlaying)
            {
                _state.ChangeState<NotPlaySingleModeState>();
                return;
            }

            if (CheckIsChangeTurn()) return;
            if (CheckIsPauseAutoPlay()) return;
            if (CheckIsSingleModeStart()) return;
            if (CheckIsSingleModeResult()) return;
            if (CheckIsSingleModeConfirmComplete()) return;
            if (CheckIsSuccessionState()) return;
            if (CheckIsStoryState()) return;
            if (CheckIsRaceEntryState()) return;
            if (CheckPaddockViewState()) return;
            if (CheckIsSingleModeMainState()) return;
            if (CheckIsCraneGameViewState()) return;
            if (CheckIsRaceViewState()) return;
            if (CheckIsLiveViewState()) return;
            _state.ChangeSubState<NotImplState>();
        }
    }
}