using System.Collections.Generic;

namespace Gallop.SingleModeAutoPlay
{
    public class RaceTopState : SingleModeAutoPlayAgentStateBase
    {
        protected override void OnStateEnter()
        {
            ActionList = new List<ISingleModeAutoPlayAgentAction>()
            {
                new PlayRaceAction(),
                new DialogRacePauseCloseAction(),
                new StartedRaceSkipAction(),
                new FinishedRaceSkipAction(),
                new CheckRaceResultAction(),
            };
        }
    }
}