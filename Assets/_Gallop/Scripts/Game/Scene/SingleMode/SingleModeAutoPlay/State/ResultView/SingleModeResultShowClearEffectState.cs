

namespace Gallop.SingleModeAutoPlay
{
    public class SingleModeResultShowClearState : SingleModeResultViewStateBase
    {
        protected override void OnStateEnter()
        {
            
        }

        void Update()
        {
            if(Step == SingleModeResultSequence.Step.ShowRankScore)
            {
                ChangeState<SingleModeResultShowRankScoreState>();
            }
        }
    }
}