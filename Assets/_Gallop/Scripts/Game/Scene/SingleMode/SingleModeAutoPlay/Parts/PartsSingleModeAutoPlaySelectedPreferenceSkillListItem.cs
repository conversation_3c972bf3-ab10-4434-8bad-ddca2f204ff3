using System;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    /// <summary>
    /// 設定済みの優先スキル表示パーツ
    /// </summary>
    public class PartsSingleModeAutoPlaySelectedPreferenceSkillListItem : MonoBehaviour
    {
        [SerializeField] private ImageCommon _bgImage;
        [SerializeField] private SkillIcon _skillIcon;
        [SerializeField] private TextCommon _skillName;

        [SerializeField] private ButtonCommon _skillDetailButton;
        [SerializeField] private ButtonCommon _selectButton;
        [SerializeField] private ButtonCommon _resetButton;

        public void Setup(MasterSkillData.SkillData skillData, ResourceManager.ResourceHash resourceHash, Action onSelect, Action onReset)
        {
            _bgImage.sprite = SingleModeUtils.GetSkillButtonSprite(skillData);
            _skillIcon.Setup(skillData.Id, false, SkillIconLongTapInfoPop.OpenType.None, resourceHash);
            _skillName.SetText(skillData.Name);

            _skillDetailButton.SetOnClick(() => OnClickSkillDetail(skillData));
            _selectButton.SetOnClick(onSelect.Call);
            _resetButton.SetOnClick(onReset.Call);
        }

        private void OnClickSkillDetail(MasterSkillData.SkillData skillData)
        {
            DialogCharacterSimpleSkillDetail.Open(skillData, true, SkillDefine.SkillLimitedType.Null, null);
        }
    }
}