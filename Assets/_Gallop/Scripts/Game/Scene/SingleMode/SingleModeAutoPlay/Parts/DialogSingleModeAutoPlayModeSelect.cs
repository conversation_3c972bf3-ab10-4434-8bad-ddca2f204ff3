using System.Linq;
using Gallop.SingleModeAutoPlay;
using UnityEngine;

namespace Gallop
{
    using static ResourcePath;

    [AddComponentMenu("")]
    public class DialogSingleModeAutoPlayModeSelect : DialogInnerBase
    {
        private const string DIALOG_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/DialogSingleModeAutoPlayModeSelect";
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] private PartsSingleModeAutoPlayDefaultPlan[] _defaultPlans;
        [SerializeField] private PartsSingleModeAutoPlayCustomPlan[] _customPlans;

        private int _currentSelectedPlanId;
        private bool _willStartAutoPlay = false;

        public static void PushDialog()
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeAutoPlayModeSelect>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            DialogManager.PushDialog(dialogData);
            dialog.Setup();
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();

            dialogData.Title = TextId.SingleModeAutoPlay425001.Text();

            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.SingleModeAutoPlay619006.Text();
            dialogData.RightButtonCallBack = (_) =>
            {
                WorkDataManager.Instance.AutoPlayData.SelectPlan(_currentSelectedPlanId);
                _willStartAutoPlay = true;
            };

            dialogData.DestroyCallBack = () =>
            {
                if (_willStartAutoPlay)
                {
                    var req = new SingleModeAutoPlaySelectPlanRequest { plan_id = _currentSelectedPlanId };
                    req.Send((_) =>
                    {
                        SingleModeAutoPlayAgent.Instance.StartAutoPlay();
                    });
                }
            };
            return dialogData;
        }

        public static void RegisterDownload(DownloadPathRegister register)
        {
            MasterDataManager.Instance.masterOmakaseDefaultPlan.GetListWithScenarioIdOrderByPlanIdAsc((int)WorkDataManager.Instance.SingleMode.GetScenarioId())
                .Select(x => x.PlanCharaId)
                .Distinct()
                .ToList()
                .ForEach(x =>
                {
                    register.RegisterPath(GetCharaThumbnailIconPath(x));
                    register.RegisterPath(GetCharaStandMediumImagePath(x, GameDefine.GetSpecialCharaDefaultDressId(x)));
                });
            DialogSingleModeAutoPlayPlanDetail.RegisterDownload(register);
        }

        private void Setup()
        {
            _willStartAutoPlay = false;
            //ダイアログ表示タイミングで停止
            SingleModeAutoPlayAgent.Instance.StopAutoPlay();
            SetupPlanData();
            SetupDefaultPlans();
            SetupCustomPlans();
            SetupSelectPlanStatus();
        }

        /// <summary>
        /// プランデータのセットアップ
        /// </summary>
        private void SetupPlanData()
        {
            // 現在のシナリオに紐づくプランデータを作成する
            var scenarioId = WorkDataManager.Instance.SingleMode.GetScenarioId();
            SingleModeAutoPlayAgent.Instance.Proxy.LoadPlanSetting(scenarioId);

            var currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
            
            //選択情報が存在しない場合はサーバーから受け取ったものを一旦選択する
            if (currentPlanRecord == null)
            {
                var selectedPlanId = WorkDataManager.Instance.SingleMode.AutoPlaySelectedPlanId;
                WorkDataManager.Instance.AutoPlayData.SelectPlan(selectedPlanId);
                currentPlanRecord = WorkDataManager.Instance.AutoPlayData.CurrentPlanRecord;
            }
            
            // サーバーから受け取った値適用後も選択情報が存在しない or 選択情報が別シナリオのものの場合はデフォルトプランの最初に見つかったデータを選択状態にする
            if (currentPlanRecord == null || currentPlanRecord.ScenarioId != (int)scenarioId)
            {
                var masterDefaultPlanList = MasterDataManager.Instance.masterOmakaseDefaultPlan.GetListWithScenarioIdOrderByPlanIdAsc((int)scenarioId);
                var firstDefaultPlan = masterDefaultPlanList.FirstOrDefault();
                WorkDataManager.Instance.AutoPlayData.SelectPlan(firstDefaultPlan?.PlanId ?? 0);
            }
        }

        /// <summary>
        /// デフォルトプラン情報をセットアップ
        /// </summary>
        private void SetupDefaultPlans()
        {
            var scenarioId = WorkDataManager.Instance.SingleMode.GetScenarioId();
            var masterDefaultPlanList = MasterDataManager.Instance.masterOmakaseDefaultPlan.GetListWithScenarioIdOrderByPlanIdAsc((int)scenarioId);

            for (var i = 0; i < _defaultPlans.Length; i++)
            {
                masterDefaultPlanList.TryGetValue(i, out var masterDefaultPlan);
                var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(masterDefaultPlan.PlanId);
                var planInfo = new PartsSingleModeAutoPlayDefaultPlan.Info(planRecord, masterDefaultPlan, DialogHash);

                var plan = _defaultPlans[i];
                plan.Setup(planInfo, (planId) =>
                {
                    _currentSelectedPlanId = planId;
                    UpdatePlanStatus();
                });
            }
        }

        /// <summary>
        /// デフォルトプラン情報を更新
        /// </summary>
        private void UpdatePlanStatus()
        {
            foreach (var plan in _defaultPlans)
            {
                plan.ToggleRadioButton(_currentSelectedPlanId);
            }

            foreach (var plan in _customPlans)
            {
                plan.ToggleRadioButton(_currentSelectedPlanId);
            }
        }

        /// <summary>
        /// カスタムプランの情報をセットアップ
        /// </summary>
        private void SetupCustomPlans()
        {
            var scenarioId = WorkDataManager.Instance.SingleMode.GetScenarioId();
            foreach (var customPlan in _customPlans.Indexed())
            {
                var planId = SingleModeAutoPlayUtils.GetCustomPlanId((int)scenarioId, customPlan.index);
                var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);

                if (planRecord is not IAutoPlayCustomPlanRecord customPlanRecord) continue;
                var planInfo = new PartsSingleModeAutoPlayCustomPlan.Info(customPlanRecord, planId);
                customPlan.item.Setup(planInfo, (x) =>
                {
                    _currentSelectedPlanId = x;
                    UpdatePlanStatus();
                });
            }
        }

        /// <summary>
        /// プランの選択状況をセットアップ
        /// </summary>
        private void SetupSelectPlanStatus()
        {
            _currentSelectedPlanId = WorkDataManager.Instance.AutoPlayData.CurrentPlanId;
            UpdatePlanStatus();
        }
    }
}