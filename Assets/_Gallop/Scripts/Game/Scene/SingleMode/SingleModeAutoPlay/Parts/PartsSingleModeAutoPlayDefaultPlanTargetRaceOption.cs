using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Gallop.SingleModeAutoPlay;

namespace Gallop
{
    using static SingleModeAutoPlayWeightRepositoryBase;

    public class PartsSingleModeAutoPlayTargetRaceOptionBase : MonoBehaviour
    {
        protected bool GetIsModifiable(IAutoPlayPlanRecord planRecord, ConditionType conditionType)
        {
            return MasterDataManager.Instance.masterOmakaseFilter
                .GetListWithScenarioIdOrderByFilterIdAsc(planRecord.ScenarioId)
                .Where(x => x.IsModifiable.ToBoolean()) //対象シナリオのModifiableなフィルター一覧
                .FirstOrDefault(filter =>
                {
                    return MasterDataManager.Instance.masterOmakaseConditionSet
                        .GetListWithConditionSetIdOrderByIdAsc(filter.ConditionSetId)
                        .FirstOrDefault(x => x.ConditionType == (int)conditionType) != null; //対象フィルターのConditionSetIdで対象のConditionTypeが存在するか
                }) != null;
        }
        
        protected MasterOmakaseFilter.OmakaseFilter GetFilterMasterByConditionType(IAutoPlayPlanRecord planRecord, ConditionType conditionType)
        {
            return MasterDataManager.Instance.masterOmakaseFilter
                .GetListWithScenarioIdOrderByFilterIdAsc(planRecord.ScenarioId)
                .Where(x => x.IsModifiable.ToBoolean())
                .FirstOrDefault(filter =>
                {
                    return MasterDataManager.Instance.masterOmakaseConditionSet
                        .GetListWithConditionSetIdOrderByIdAsc(filter.ConditionSetId)
                        .FirstOrDefault(x => x.ConditionType == (int)conditionType) != null;
                });
        }
    }


    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayDefaultPlanTargetRaceOption : PartsSingleModeAutoPlayTargetRaceOptionBase
    {
        [SerializeField] private TextCommon _targetRaceFanNumText; //目標レース条件のファン数
        [SerializeField] private TextCommon _targetFanNumText; //目標ファン数
        [SerializeField] private TextCommon _notClearRaceConditionText; //目標レースのレース戦績
        [SerializeField] private TextCommon _notWinText; //未勝利

        public void Setup(int planId)
        {
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);

            void SetupContent(TextCommon textCommon , ConditionType conditionType)
            {
                var filter = GetFilterMasterByConditionType(planRecord, conditionType);
                textCommon.transform.parent.gameObject.SetActive(filter != null);
                if (filter != null)
                {
                    textCommon.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayFilterTitle, filter.FilterId);
                }
            }

            SetupContent(_targetRaceFanNumText, ConditionType.NotEnoughFanCountToTargetRace);
            SetupContent(_targetFanNumText, ConditionType.NotEnoughFanCount);
            SetupContent(_notClearRaceConditionText, ConditionType.NotEnoughRaceReserveRaceGradeCount);
            SetupContent(_notWinText, ConditionType.NotWinRace);
        }
    }
}