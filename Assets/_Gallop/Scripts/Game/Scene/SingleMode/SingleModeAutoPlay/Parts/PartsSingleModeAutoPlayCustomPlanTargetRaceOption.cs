using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Gallop.SingleModeAutoPlay;

namespace Gallop
{
    using static SingleModeAutoPlayWeightRepositoryBase;
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayCustomPlanTargetRaceOption : PartsSingleModeAutoPlayTargetRaceOptionBase
    {
        [SerializeField] private ToggleWithText _targetRaceFanNumToggle; //目標レース条件のファン数
        [SerializeField] private ToggleWithText _targetFanNumToggle; //目標ファン数
        [SerializeField] private ToggleWithText _notClearRaceConditionToggle; //目標レースのレース戦績
        [SerializeField] private ToggleWithText _notWinToggle; //未勝利

        private int _planId;

        public void Setup(int planId)
        {
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);
            _planId = planId;
            
            void SetupContent(ToggleWithText toggleWithText, ConditionType conditionType)
            {
                var filter = GetFilterMasterByConditionType(planRecord, conditionType);
                toggleWithText.gameObject.SetActive(filter != null);
                if (filter != null)
                {
                    toggleWithText.Text.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayFilterTitle, filter.FilterId);
                    toggleWithText.Toggle.isOn = planRecord.ModifyFilterIdList.Contains(filter.FilterId);
                    toggleWithText.Toggle.onValueChanged.AddListener(_ => ModifyFilterIdList());
                }
            }
            
            SetupContent(_targetRaceFanNumToggle, ConditionType.NotEnoughFanCountToTargetRace);
            SetupContent(_targetFanNumToggle, ConditionType.NotEnoughFanCount);
            SetupContent(_notClearRaceConditionToggle, ConditionType.NotEnoughRaceReserveRaceGradeCount);
            SetupContent(_notWinToggle, ConditionType.NotWinRace);
            
        }

        private void ModifyFilterIdList()
        {
            var filterIdList = new List<int>();
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(_planId);
            if (_targetRaceFanNumToggle.Toggle.isOn) TryAddFilterIdFromConditionType(filterIdList, planRecord, ConditionType.NotEnoughFanCountToTargetRace);
            if (_targetFanNumToggle.Toggle.isOn) TryAddFilterIdFromConditionType(filterIdList, planRecord, ConditionType.NotEnoughFanCount);
            if (_notClearRaceConditionToggle.Toggle.isOn) TryAddFilterIdFromConditionType(filterIdList, planRecord, ConditionType.NotEnoughRaceReserveRaceGradeCount);
            if (_notWinToggle.Toggle.isOn) TryAddFilterIdFromConditionType(filterIdList, planRecord, ConditionType.NotWinRace);
            WorkDataManager.Instance.AutoPlayData.ApplyFilterIdList(_planId, filterIdList);
        }

        private void TryAddFilterIdFromConditionType(List<int> filterIdList, IAutoPlayPlanRecord planRecord, ConditionType conditionType)
        {
            if (GetFilterMasterByConditionType(planRecord, conditionType) is {} filter)
            {
                filterIdList.Add(filter.FilterId);
            }
        }
    }
}