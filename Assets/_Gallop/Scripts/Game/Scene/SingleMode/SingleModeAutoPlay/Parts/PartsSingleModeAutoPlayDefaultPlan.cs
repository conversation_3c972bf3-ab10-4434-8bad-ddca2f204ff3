using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// おまかせ育成デフォルトプラン選択パーツ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayDefaultPlan : PartsSingleModeAutoPlayPlanSelectorBase
    {
        [SerializeField] private RawImageCommon _charaIcon;
        
        private System.Action<int> _onClickPlan;
        private Info _planInfo;

        public void Setup(Info planInfo, System.Action<int> onClickPlan)
        {
            _toggleButton.SetOnClick(OnClickContents);
            _customizeButton.SetOnClick(OnClickCustomizeButton);
            _planInfo = planInfo;
            _onClickPlan = onClickPlan;
            _planNameText.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayDefaultPlanTitle, planInfo.PlanSetId);
            _planDescriptionText.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayDefaultPlanDescription, planInfo.PlanSetId);
            var iconPath = ResourcePath.GetCharaThumbnailIconPath(planInfo.PlanCharaId);
            _charaIcon.texture = ResourceManager.LoadOnHash<Texture2D>(iconPath, planInfo.ResourceHash);
        }

        public void ToggleRadioButton(int selectedPlanId)
        {
            var isOn = _planInfo.PlanId == selectedPlanId;
            _toggleButton.gameObject.SetActive(!isOn);
            _onImage.gameObject.SetActive(isOn);
        }

        private void OnClickContents()
        {
            _onClickPlan?.Invoke(_planInfo.PlanId);
        }

        private void OnClickCustomizeButton()
        {
            DialogSingleModeAutoPlayPlanDetail.PushDialog(_planInfo, ()=> { /* デフォルトプランでは何もしない */ });
        }

        /// <summary>
        /// デフォルトプラン情報
        /// </summary>
        public class Info : IAutoPlayPlanInfo
        {
            public IAutoPlayPlanRecord PlanRecord { get; }
            public int PlanId { get; }
            public int PlanCharaId { get; } 
            public int PlanSetId { get; }
            public ResourceManager.ResourceHash ResourceHash { get; }

            public Info(IAutoPlayPlanRecord planRecord, MasterOmakaseDefaultPlan.OmakaseDefaultPlan defaultPlan, ResourceManager.ResourceHash resourceHash)
            {
                PlanRecord = planRecord;
                PlanId = defaultPlan?.PlanId ?? 0;
                PlanCharaId = defaultPlan?.PlanCharaId ?? 0;
                PlanSetId = defaultPlan?.PlanSetId ?? 0;
                ResourceHash = resourceHash;
            }
        }

    }
}