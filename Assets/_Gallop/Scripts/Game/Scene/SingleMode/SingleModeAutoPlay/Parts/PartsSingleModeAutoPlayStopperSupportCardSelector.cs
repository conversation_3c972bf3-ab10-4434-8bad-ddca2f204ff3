using System.Linq;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayStopperSupportCardSelector : MonoBehaviour
    {
        [SerializeField] private ToggleWithText[] _toggleArray;
        [SerializeField] private RawImageCommon[] _supportCardImageArray;
        [SerializeField] private ImageCommon _frameImage;

        [SerializeField] private ButtonCommon _blockButton;
        private int _planId;
        private ResourceManager.ResourceHash _dialogHash;

        public void Setup(int planId, ResourceManager.ResourceHash dialogHash)
        {
            _planId = planId;
            _dialogHash = dialogHash;
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);
            for (var i = 0; i < _toggleArray.Length; ++i)
            {
                var equipSupportCard = WorkDataManager.Instance.SingleMode.Character.EquipSupportCardArray[i];
                if (equipSupportCard == null) continue;
                _toggleArray[i].Toggle.isOn = !planRecord.ModifyStopEventSupportCardIdList.IsNullOrEmptyReadOnlyList() &&
                                              planRecord.ModifyStopEventSupportCardIdList.Contains(equipSupportCard.SupportCardId);
                _toggleArray[i].Toggle.onValueChanged.AddListener(_ => { ModifyStopEventSupportCardIdList(); });
            }
            _blockButton.SetOnClick(() =>
            {
                UIManager.Instance.ShowNotification(TextId.SingleModeAutoPlay425040.Text());
                AudioManager.Instance.PlaySe(AudioId.SFX_UI_CANCEL_M_02);
            });
            SetupSupportCardInfo();
        }

        private void SetupSupportCardInfo()
        {
            
            var equipSupportCardArray = WorkDataManager.Instance.SingleMode.Character.EquipSupportCardArray;
            for (var i = 0; i < _supportCardImageArray.Length; ++i)
            {
                var equipSupportCard = equipSupportCardArray[i];
                if (equipSupportCard == null) continue;
                _supportCardImageArray[i].texture = ResourceManager.LoadOnHash<Texture>(ResourcePath.GetSupportCardSTexturePath(equipSupportCard.SupportCardId), _dialogHash);
            }

            foreach (var toggleWithText in _toggleArray.Indexed())
            {
                toggleWithText.item.Text.text = TextId.SingleModeAutoPlay425026.Format(toggleWithText.index+1); //{0}枚目のサポートカード
            }
        }

        /// <summary>
        /// 停止するサポートカードイベントのIndexリストをプランデータに反映する
        /// </summary>
        private void ModifyStopEventSupportCardIdList()
        {
            const int INVALID_INDEX = -1;
            var selectedIndexList = _toggleArray
                .Select((x, index) => x.Toggle.isOn ? index : INVALID_INDEX)
                .Where(x => x != INVALID_INDEX)
                .ToList();
            var equipSupportCardArray = WorkDataManager.Instance.SingleMode.Character.EquipSupportCardArray;
            WorkDataManager.Instance.AutoPlayData.ApplyStopSupportCardEventIndexList(_planId, selectedIndexList, equipSupportCardArray);
        }

        public void ToggleInteractable(bool isInteractable)
        {
            _blockButton.SetActiveWithCheck(!isInteractable);
            _frameImage.color = isInteractable ? StaticVariableDefine.Parts.ButtonCommonStatic.DEFAULT_COLOR_WHITE : StaticVariableDefine.Parts.ButtonCommonStatic.NO_INTERACTERABLE_COLOR;
        }
    }
}