using UnityEngine;
using UnityEngine.EventSystems;

namespace Gallop
{
    public class PartsSingleModeAutoPlayVenusProfileFadeImage : UIBehaviour
    {
        [SerializeField, Range(0.0f, 1.0f)] private float _alphaAlignmentValue;
        [SerializeField] private CanvasGroup _fadeCanvasGroup;
        [SerializeField] private CanvasRenderer _canvasRenderer;

        protected override void OnCanvasGroupChanged()
        {
            base.OnCanvasGroupChanged();
            
            var alpha = _canvasRenderer.GetInheritedAlpha();

            var clampAlpha = alpha < _alphaAlignmentValue ? alpha : 1f;
            _fadeCanvasGroup.alpha = clampAlpha;
        }
    }
}