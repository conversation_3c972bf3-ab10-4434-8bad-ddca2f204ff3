using System;
using System.Globalization;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static ResourcePath;

    [AddComponentMenu("")]
    public class DialogSingleModeAutoPlaySelectPreferenceSkill :
        DialogInnerBase,
        IPartsSingleModeAutoPlaySelectableSkillListEventDispatch,
        IPartsSingleModeAutoPlayFilterSkillNameEventDispatch
    {
        private const string DIALOG_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/DialogSingleModeAutoPlaySelectPreferenceSkill";

        [SerializeField] private PartsSingleModeAutoPlaySelectableSkillList _selectableSkillList;
        [SerializeField] private PartsSingleModeAutoPlayFilterSkillName _filterSkillName;

        private Action<int> _onDecide;
        private SingleModeAutoPlaySkillSelectorModel _preferenceSkillModel;

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_TWO_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();

            dialogData.Title = TextId.SingleModeAutoPlay619001.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0023.Text();
            dialogData.RightButtonCallBack = OnDecide;
            return dialogData;
        }

        public static void PushDialog(int changeTargetSkill, IReadOnlyList<int> selectedSkillIdList, Action<int> onDecide)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeAutoPlaySelectPreferenceSkill>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            DialogManager.PushDialog(dialogData);
            dialog.Setup(changeTargetSkill, selectedSkillIdList, dialogData.DialogHash, onDecide);
        }

        private void Setup(int changeTargetSkill, IReadOnlyList<int> selectedSkillIdList, ResourceManager.ResourceHash resourceHash, Action<int> onDecide)
        {
            _preferenceSkillModel = new SingleModeAutoPlaySkillSelectorModel(changeTargetSkill, selectedSkillIdList);
            _onDecide = onDecide;

            SetupSelectableSkillList(resourceHash);
            SetupFilterSkillName();
        }

        private void SetupSelectableSkillList(ResourceManager.ResourceHash resourceHash)
        {
            var skillListInfo = new PartsSingleModeAutoPlaySelectableSkillList.Info(_preferenceSkillModel, resourceHash);
            _selectableSkillList.Setup(skillListInfo, _preferenceSkillModel, this);
        }

        private void SetupFilterSkillName()
        {
            _filterSkillName.Setup(this);
        }

        private void UpdateFilter()
        {
            _selectableSkillList.SetupSkillList();
        }

        private void OnDecide(DialogCommon dialogCommon)
        {
            _onDecide.Call(_preferenceSkillModel.SelectedSkillId);
        }

        /// <summary>
        /// スキル選択の操作
        /// </summary>
        void IPartsSingleModeAutoPlaySelectableSkillListItemEventDispatch.OnSelectSkill(int skillId)
        {
            _preferenceSkillModel.SelectSkill(skillId);
            _selectableSkillList.UpdateSkillList();
        }

        /// <summary>
        /// スキル詳細の操作
        /// </summary>
        void IPartsSingleModeAutoPlaySelectableSkillListItemEventDispatch.OnClickSkillDetail(int skillId)
        {
            var skillData = MasterDataManager.Instance.masterSkillData.Get(skillId);
            DialogCharacterSimpleSkillDetail.Open(skillData, true, SkillDefine.SkillLimitedType.Null, null);
        }

        void IPartsSingleModeAutoPlayFilterSkillNameEventDispatch.OnChangeFilterSkillName(string inputSkillName)
        {
            _preferenceSkillModel.SetFilterSkillText(inputSkillName);
            UpdateFilter();
        }

        private class SingleModeAutoPlaySkillSelectorModel : 
            ISingleModeAutoPlaySelectedPreferenceSkillAccessor,
            ISingleModeAutoPlaySelectablePreferenceSkillAccessor
        {
            private int _currentSelectSkillId;

            private string _inputFilterSkillText;
            private IReadOnlyList<MasterSkillData.SkillData> _skillDataList;

            public int SelectedSkillId => _currentSelectSkillId;

            public IReadOnlyList<MasterSkillData.SkillData> FilteredSkillDataList
                => _filteredSkillDataList;

            private IReadOnlyList<MasterSkillData.SkillData> _filteredSkillDataList;

            public SingleModeAutoPlaySkillSelectorModel(int changeTargetSkillId, IReadOnlyList<int> selectedSkillIdList)
            {
                // 表示するスキルのレアリティ
                const int NORMAL_SKILL_RARITY = 1, RARE_SKILL_RARITY = 2;
                // バッドスキルを除くGroupRateのしきい値
                const int IGNORE_BAD_SKILL_GROUP_RATE_THRESHOLD = 1;

                const int EMPTY_UNIQUE_SKILL_ID = 0;

                var hideSkillIdList = MasterDataManager.Instance.masterOmakaseHideSkill.dictionary.Values
                    .Select(x => x.SkillId)
                    .ToList();

                // 表示するスキルは
                // 非表示設定がされていない、通常のノーマルスキルorレアスキル、バッドスキルを含まない、継承スキルでない、変更対象以外の選択されていないスキル
                _skillDataList = MasterDataManager.Instance.masterSkillData.dictionary.Values
                    .Where(skillData => !hideSkillIdList.Contains(skillData.Id) &&
                                        (skillData.Rarity == NORMAL_SKILL_RARITY || skillData.Rarity == RARE_SKILL_RARITY) &&
                                        skillData.IsGeneralSkill.ToBoolean() &&
                                        skillData.GroupRate >= IGNORE_BAD_SKILL_GROUP_RATE_THRESHOLD &&
                                        skillData.UniqueSkillId1 == EMPTY_UNIQUE_SKILL_ID &&
                                        (changeTargetSkillId == skillData.Id || selectedSkillIdList == null ||!selectedSkillIdList.Contains(skillData.Id)))
                    .OrderBy(skillData => skillData.DispOrder)
                    .ToList();

                _currentSelectSkillId = changeTargetSkillId != 0 ? changeTargetSkillId : _skillDataList.FirstOrDefault()?.Id ?? 0;
                _filteredSkillDataList = _skillDataList;
            }

            #region スキル選択
            public void SelectSkill(int skillId)
            {
                _currentSelectSkillId = skillId;
            }
            #endregion スキル選択

            #region スキルフィルタ
            public void SetFilterSkillText(string inputText)
            {
                _inputFilterSkillText = inputText;
                _filteredSkillDataList = _skillDataList.Where(skillData => IsMatch(skillData.Name)).ToList();
            }

            private bool IsMatch(string target)
            {
                if (_inputFilterSkillText.IsEmpty()) return true;
                if (target.IndexOf(_inputFilterSkillText, StringComparison.OrdinalIgnoreCase) >= 0)
                {
                    // 大文字小文字どちらでも
                    return true;
                }

                // カタカナでもひらがなでも、全角でも半角でも(=などの記号)
                var option = CompareOptions.IgnoreKanaType | CompareOptions.IgnoreWidth;
                if (CultureInfo.CurrentCulture.CompareInfo.IndexOf(target, _inputFilterSkillText, option) >= 0)
                {
                    return true;
                }
            
                return false;
            }
            #endregion スキルフィルタ

            bool ISingleModeAutoPlaySelectedPreferenceSkillAccessor.GetIsSelected(int skillId)
            {
                return _currentSelectSkillId == skillId;
            }

            IReadOnlyList<MasterSkillData.SkillData> ISingleModeAutoPlaySelectablePreferenceSkillAccessor.SelectablePreferenceSkillList => FilteredSkillDataList;
        }
    }

    public interface ISingleModeAutoPlaySelectedPreferenceSkillAccessor
    {
        public bool GetIsSelected(int skillId);
    }

    public interface ISingleModeAutoPlaySelectablePreferenceSkillAccessor
    {
        public IReadOnlyList<MasterSkillData.SkillData> SelectablePreferenceSkillList { get; }
        public int SelectedSkillId { get; }
    }
}