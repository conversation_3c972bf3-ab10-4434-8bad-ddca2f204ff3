using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeAutoPlaySelectableSkillList : MonoBehaviour
    {
        [SerializeField] private LoopScroll _skillListScroll;

        private Info _info;
        private ISingleModeAutoPlaySelectedPreferenceSkillAccessor _selectedPreferenceSkillAccessor;
        private IPartsSingleModeAutoPlaySelectableSkillListEventDispatch _eventDispatch;

        public void Setup(
            Info info,
            ISingleModeAutoPlaySelectedPreferenceSkillAccessor selectedPreferenceSkillAccessor,
            IPartsSingleModeAutoPlaySelectableSkillListEventDispatch eventDispatch)
        {
            _info = info;
            _selectedPreferenceSkillAccessor = selectedPreferenceSkillAccessor;
            _eventDispatch = eventDispatch;

            SetupSkillList();
            _skillListScroll.SetCellPositionToTop(_info.SelectedSkillIndex, true);
        }

        public void SetupSkillList()
        {
            _skillListScroll.Setup(_info.SelectableSkillList.Count, OnItemUpdate);
        }

        public void UpdateSkillList()
        {
            _skillListScroll.UpdateActiveItem();
        }

        private void OnItemUpdate(LoopScrollItemBase listItem)
        {
            if (listItem is not PartsSingleModeAutoPlaySelectableSkillListItem selectableSkillListItem) return;

            selectableSkillListItem.Setup(_info.SelectableSkillList[listItem.ItemIndex], 
                                          _info.ResourceHash,
                                          _selectedPreferenceSkillAccessor,
                                          _eventDispatch);
        }

        public class Info
        {
            private readonly ISingleModeAutoPlaySelectablePreferenceSkillAccessor _selectableSkillAccessor;
            public IReadOnlyList<MasterSkillData.SkillData> SelectableSkillList => _selectableSkillAccessor.SelectablePreferenceSkillList;

            public int SelectedSkillIndex
            {
                get
                {
                    var index = SelectableSkillList.FindIndex(x => x.Id == _selectableSkillAccessor.SelectedSkillId);
                    return index < 0 ? 0 : index;
                }
            }

            public ResourceManager.ResourceHash ResourceHash { get; }

            public Info(
                ISingleModeAutoPlaySelectablePreferenceSkillAccessor selectableSkillAccessor,
                ResourceManager.ResourceHash resourceHash)
            {
                _selectableSkillAccessor = selectableSkillAccessor;
                ResourceHash = resourceHash;
            }
        }
    }

    public interface IPartsSingleModeAutoPlaySelectableSkillListEventDispatch :
        IPartsSingleModeAutoPlaySelectableSkillListItemEventDispatch
    {
    }
}