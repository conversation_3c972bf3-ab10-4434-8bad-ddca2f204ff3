using UnityEngine;
using System;

namespace Gallop
{
    using static ResourcePath;

    [AddComponentMenu("")]
    public sealed class DialogSingleModeAutoPlayChangePlanName : DialogEditNameBase
    {
        private const string DIALOG_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/DialogSingleModeAutoPlayChangePlanName";
        public const int PLAN_NAME_LIMIT = 12;
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        public static void PushDialog(string planName, Action<DialogCommon, string> onDecide)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeAutoPlayChangePlanName>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            dialogData.RightButtonCallBack = (x) => { onDecide?.Invoke(x, dialog._inputField.text); };
            DialogManager.PushDialog(dialogData);
            dialog.Setup(planName, InputFieldCommon.InputTextType.SingleModeAutoPlayPlanName);
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.SingleModeAutoPlay425017.Text();
            return dialogData;
        }
    }
}