using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeAutoPlayFilterSkillName : MonoBehaviour
    {
        [SerializeField] private InputFieldCommon _inputField;
        [SerializeField] private ButtonCommon _resetButton;

        private IPartsSingleModeAutoPlayFilterSkillNameEventDispatch _eventDispatch;

        public void Setup(IPartsSingleModeAutoPlayFilterSkillNameEventDispatch eventDispatch)
        {
            _eventDispatch = eventDispatch;

            _inputField.SetValueChangedCallback(OnValueChanged);
            _resetButton.SetOnClick(() => _inputField.text = string.Empty);

            UpdateResetButtonInteractable();
        }

        private void OnValueChanged()
        {
            UpdateResetButtonInteractable();
            _eventDispatch.OnChangeFilterSkillName(_inputField.text);
        }

        /// <summary>
        /// リセットボタンは入力がある時に有効
        /// </summary>
        private void UpdateResetButtonInteractable()
        {
            var hasInputText = !_inputField.text.IsEmpty();
            _resetButton.interactable = hasInputText;
            _resetButton.SetNotificationMessage(hasInputText ? string.Empty: TextId.Outgame194019.Text());
        }
    }

    public interface IPartsSingleModeAutoPlayFilterSkillNameEventDispatch
    {
        public void OnChangeFilterSkillName(string inputSkillName);
    }
}