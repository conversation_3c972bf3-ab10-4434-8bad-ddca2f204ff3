using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 伝説編用の優先度設定トグルクラス
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeLegendAutoPlayModifyToggleSelector : MonoBehaviour
    {
        [SerializeField] private TextCommon _labelText;
        [SerializeField] private ToggleGroupCommon _toggleGroup;

        public void Setup(int planId, int modifyId)
        {
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);

            // 伝説編以外では表示しない
            var isLegendScenario = planRecord.ScenarioId == (int)SingleModeDefine.ScenarioId.Legend;
            gameObject.SetActiveWithCheck(isLegendScenario);
            if (!isLegendScenario) return;

            _labelText.SetText(TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayModifyPtTitle, modifyId));

            var modifyRecord = planRecord.ModifyPtRecordList.FirstOrDefault(x => x.ModifyId == modifyId);
            // NOTE: SettingTypeは1～なのでIndexにするために-1をしている
            var selectedIndex = modifyRecord == null ? 0 : (int)modifyRecord.CurrentSettingType - 1;

            _toggleGroup.SetToggleOnFromNumber(selectedIndex);
            _toggleGroup.SetOnSelectCallback(index =>
            {
                WorkDataManager.Instance.AutoPlayData.ApplyModifyPtSetting(planId, modifyId, (WorkAutoPlayModifyPtRecord.SettingType)(index + 1));
            });
        }
    }
}