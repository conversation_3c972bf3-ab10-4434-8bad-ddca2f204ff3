using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static SingleModeAutoPlay.AutoPlayPauseInfo;

    /// <summary>
    /// コマンド条件のトグルを管理するクラス
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayStopperCommandSelector : MonoBehaviour
    {
        [SerializeField] private ToggleWithText _tagTrainingToggle;
        [SerializeField] private ToggleWithText _reservedRaceToggle;
        [SerializeField] private ToggleWithText _hintEventToggle;
        [SerializeField] private ImageCommon _commandFrameImage;
        [SerializeField] private ButtonCommon _blockButton;
        private int _planId;

        private int GetStopperTriggerId(IAutoPlayPlanRecord planRecord, AutoPlayPauseTriggerType triggerType)
        {
            return MasterDataManager.Instance.masterOmakaseStopTrigger
                .GetListWithScenarioIdOrderByStopTriggerIdAsc(planRecord.ScenarioId)
                .FirstOrDefault(x => x.TriggerType == (int)triggerType)
                ?.StopTriggerId ?? 0;
        }

        public void Setup(int planId)
        {
            _planId = planId;
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);

            _tagTrainingToggle.Toggle.isOn = planRecord.ModifyStopperTriggerIdList?.Contains(GetStopperTriggerId(planRecord, AutoPlayPauseTriggerType.TagTraining)) ?? false;
            _reservedRaceToggle.Toggle.isOn = planRecord.ModifyStopperTriggerIdList?.Contains(GetStopperTriggerId(planRecord, AutoPlayPauseTriggerType.RaceReserve)) ?? false;
            _hintEventToggle.Toggle.isOn = planRecord.ModifyStopperTriggerIdList?.Contains(GetStopperTriggerId(planRecord, AutoPlayPauseTriggerType.HasTips)) ?? false;

            _tagTrainingToggle.Toggle.onValueChanged.AddListener(_ => { ModifyStopperTriggerIdList(planRecord); });
            _reservedRaceToggle.Toggle.onValueChanged.AddListener(_ => { ModifyStopperTriggerIdList(planRecord); });
            _hintEventToggle.Toggle.onValueChanged.AddListener(_ => { ModifyStopperTriggerIdList(planRecord); });
            
            _blockButton.SetOnClick(() =>
            {
                UIManager.Instance.ShowNotification(TextId.SingleModeAutoPlay425039.Text());
                AudioManager.Instance.PlaySe(AudioId.SFX_UI_CANCEL_M_02);
            });
        }

        private void ModifyStopperTriggerIdList(IAutoPlayPlanRecord planRecord)
        {
            var stopperTriggerIdList = new List<int>();
            if (_tagTrainingToggle.Toggle.isOn) stopperTriggerIdList.Add(GetStopperTriggerId(planRecord, AutoPlayPauseTriggerType.TagTraining));
            if (_reservedRaceToggle.Toggle.isOn) stopperTriggerIdList.Add(GetStopperTriggerId(planRecord, AutoPlayPauseTriggerType.RaceReserve));
            if (_hintEventToggle.Toggle.isOn) stopperTriggerIdList.Add(GetStopperTriggerId(planRecord, AutoPlayPauseTriggerType.HasTips));
            WorkDataManager.Instance.AutoPlayData.ApplyStopperTriggerIdList(_planId, stopperTriggerIdList);
        }

        public void ToggleInteractable(bool isInteractable)
        {
            _blockButton.SetActiveWithCheck(!isInteractable);
            _commandFrameImage.color = isInteractable ? StaticVariableDefine.Parts.ButtonCommonStatic.DEFAULT_COLOR_WHITE : StaticVariableDefine.Parts.ButtonCommonStatic.NO_INTERACTERABLE_COLOR;
        }
    }
}