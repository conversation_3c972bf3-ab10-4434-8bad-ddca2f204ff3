using UnityEngine;

namespace Gallop
{
    public class PartsSingleModeAutoPlayPlanSelectorBase : MonoBeh<PERSON>our
    {
        [SerializeField] protected Button<PERSON>ommon _toggleButton;
        [SerializeField] protected ImageCommon _onImage;
        [SerializeField] protected TextCommon _planNameText;
        [SerializeField] protected TextCommon _planDescriptionText;
        [SerializeField] protected Button<PERSON><PERSON>mon _customizeButton;
        
        public interface IAutoPlayPlanInfo
        {
            public IAutoPlayPlanRecord PlanRecord { get; }
            public int PlanId { get; }
        }
    }

    
}