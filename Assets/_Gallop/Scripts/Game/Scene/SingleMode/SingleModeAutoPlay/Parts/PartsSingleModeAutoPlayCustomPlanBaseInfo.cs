using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// カスタムプランの基本情報パーツ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayCustomPlanBaseInfo : PartsSingleModeAutoPlayBaseInfo
    {   
        [SerializeField] private ButtonCommon _editNameButton;
        [SerializeField] private ButtonCommon _editDescriptionButton;
        
        private PartsSingleModeAutoPlayCustomPlan.Info _planInfo;
        public void Setup(PartsSingleModeAutoPlayCustomPlan.Info planInfo)
        {
            if (planInfo.PlanRecord is IAutoPlayCustomPlanRecord customPlanRecord)
            {
                _planInfo = planInfo;
                UpdatePlanText();
                _editNameButton.SetOnClick(() => DialogSingleModeAutoPlayChangePlanName.PushDialog(customPlanRecord.PlanName, OnEndEditPlanName));
                _editDescriptionButton.SetOnClick(() => DialogSingleModeAutoPlayChangePlanDescription.PushDialog(customPlanRecord.PlanDescription, OnEndEditPlanDescription));
            }
        }

        private void UpdatePlanText()
        {
            if (_planInfo.PlanRecord is IAutoPlayCustomPlanRecord customPlanRecord)
            {
                _planNameText.text = customPlanRecord.PlanName;
                _planDescriptionText.text = customPlanRecord.FormattedPlanDescription;
            }
        }

        private void OnEndEditPlanName(DialogCommon dialog, string planName)
        {
            var req = new SingleModeAutoPlaySavePlanNameRequest()
            {
                plan_id = _planInfo.PlanId,
                plan_name = planName
            };
            req.Send((res) =>
            {
                dialog.Close();
                if (_planInfo.PlanRecord is WorkAutoPlayCustomPlanRecord customPlanRecord)
                {
                    customPlanRecord.ApplyPlanName(planName);
                    UpdatePlanText();
                }
            });
        }
        
        private void OnEndEditPlanDescription(DialogCommon dialog, string planDescription)
        {
            var req = new SingleModeAutoPlaySavePlanDescriptionRequest()
            {
                plan_id = _planInfo.PlanId,
                plan_description = planDescription
            };
            req.Send((res) =>
            {
                //保存用に改行なしのテキストで来るので表示用に更新
                _planDescriptionText.text = 
                    planDescription.Length > DialogSingleModeAutoPlayChangePlanDescription.LINE_LIMIT ?  //12文字以上か？
                    planDescription.Insert(DialogSingleModeAutoPlayChangePlanDescription.LINE_LIMIT, "\n") : planDescription;
                dialog.Close();
                if(_planInfo.PlanRecord is WorkAutoPlayCustomPlanRecord customPlanRecord)
                {
                    customPlanRecord.ApplyPlanDescription(planDescription);
                    UpdatePlanText();
                }
            });
        }
    }
}