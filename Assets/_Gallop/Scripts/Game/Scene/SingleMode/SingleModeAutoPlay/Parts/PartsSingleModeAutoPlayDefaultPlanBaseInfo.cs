using UnityEngine;

namespace Gallop
{
    
    public class PartsSingleModeAutoPlayBaseInfo : MonoBehaviour
    {
        [SerializeField] protected TextCommon _planNameText;
        [SerializeField] protected TextCommon _planDescriptionText;
    }
    
    /// <summary>
    /// デフォルトプランの基本情報パーツ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayDefaultPlanBaseInfo : PartsSingleModeAutoPlayBaseInfo
    {
        [SerializeField] private RawImageCommon _charaIcon;
        [SerializeField] private ButtonCommon _profileButton;
        
        public void Setup(PartsSingleModeAutoPlayDefaultPlan.Info planInfo, ResourceManager.ResourceHash dialogHash)
        {
            _planNameText.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayDefaultPlanTitle, planInfo.PlanSetId);
            _planDescriptionText.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayDefaultPlanDescription, planInfo.PlanSetId);
            _charaIcon.texture = ResourceManager.LoadOnHash<Texture2D>(ResourcePath.GetCharaThumbnailIconPath(planInfo.PlanCharaId), dialogHash);
            _profileButton.SetOnClick(() => DialogSingleModeAutoPlayVenusProfile.PushDialog(planInfo));
        }
    }
}