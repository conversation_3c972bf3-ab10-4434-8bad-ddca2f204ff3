using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeAutoPlaySelectableSkillListItem : LoopScrollItemBase, IRadioButtonToggleCommonEventDispatch
    {
        [SerializeField] private ImageCommon _bgImage;
        [SerializeField] private SkillIcon _skillIcon;
        [SerializeField] private TextCommon _skillNameText;
        [SerializeField] private ButtonCommon _infoButton;
        [SerializeField] private RadioButtonToggleCommon _selectButtonToggle;

        private int _skillId;
        private IPartsSingleModeAutoPlaySelectableSkillListItemEventDispatch _eventDispatch;

        public void Setup(
            MasterSkillData.SkillData skillData,
            ResourceManager.ResourceHash resourceHash,
            ISingleModeAutoPlaySelectedPreferenceSkillAccessor selectedPreferenceSkillAccessor,
            IPartsSingleModeAutoPlaySelectableSkillListItemEventDispatch eventDispatch)
        {
            _skillId = skillData.Id;
            _eventDispatch = eventDispatch;

            _bgImage.sprite = SingleModeUtils.GetSkillFrameSprite(skillData);
            _infoButton.SetOnClick(() => _eventDispatch.OnClickSkillDetail(_skillId));
            _skillNameText.SetText(skillData.Name);
            _skillIcon.Setup(_skillId, false, SkillIconLongTapInfoPop.OpenType.Explain, resourceHash);
            _selectButtonToggle.Initialize(new ButtonToggleVM(_skillId, selectedPreferenceSkillAccessor), this);
        }

        void IRadioButtonToggleCommonEventDispatch.OnClickButtonToggle()
        {
            _eventDispatch.OnSelectSkill(_skillId);
            _selectButtonToggle.UpdateDisplay();
        }

        private class ButtonToggleVM : IRadioButtonToggleCommonVM
        {
            bool IRadioButtonToggleCommonVM.IsOff => !SelectedPreferenceSkillAccessor.GetIsSelected(SkillId);
            bool IButtonToggleCommonVM.IsOn => SelectedPreferenceSkillAccessor.GetIsSelected(SkillId);
            int IButtonToggleCommonVM.Index => 0;
            FontColorType IButtonToggleCommonVM.FontColorType => FontColorType.None;

            private int SkillId { get; }
            private ISingleModeAutoPlaySelectedPreferenceSkillAccessor SelectedPreferenceSkillAccessor { get; }

            public ButtonToggleVM(int skillId, ISingleModeAutoPlaySelectedPreferenceSkillAccessor selectedPreferenceSkillAccessor)
            {
                SkillId = skillId;
                SelectedPreferenceSkillAccessor = selectedPreferenceSkillAccessor;
            }
        }
    }

    public interface IPartsSingleModeAutoPlaySelectableSkillListItemEventDispatch
    {
        public void OnSelectSkill(int skillId);
        public void OnClickSkillDetail(int skillId);
    }
}