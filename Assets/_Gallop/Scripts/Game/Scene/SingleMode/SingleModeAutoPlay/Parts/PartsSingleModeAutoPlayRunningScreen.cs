using Gallop.SingleModeAutoPlay;
using UnityEngine;

namespace Gallop
{
    using static ResourcePath;
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayRunningScreen : MonoBehaviour
    {
        private const string PREFAB_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/PartsSingleModeAutoPlayRunningScreen";
        private const string A2U_PATH = SINGLE_MODE_FLASH_COMBINE_ROOT + "fa_singlemode_txt_auto_training00";
        private const int FLASH_SORT_OFFSET = 2000;
        [SerializeField] private RectTransform _contentRoot;
        [SerializeField] private Transform _flashPlayerRoot;
        [SerializeField] private ButtonCommon _stopButton;

        [SerializeField] private ImageCommon _fadeImage;
        [SerializeField] private Transform _fade;
        [SerializeField] private ImageCommon _landScapeFadeImage;
        [SerializeField] private Transform _raceLandscapeFade;
        [SerializeField] private Transform _raceLandscapeFlashPlayerRoot;
        [SerializeField] private ButtonCommon _raceLandscapeStopButton;

        private FlashActionPlayer _flashActionPlayer;
        private FlashActionPlayer _flashActionPlayerByRaceLandscape;

        public static PartsSingleModeAutoPlayRunningScreen CreateScreen()
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(PREFAB_PATH);
            var screen = Instantiate(prefab,UIManager.SystemCanvas.transform).GetComponent<PartsSingleModeAutoPlayRunningScreen>();
            screen.Setup();
            screen.HideScreen();
            return screen;
        }

        void Update()
        {
            //おまかせ中にスプライトが剥がれたら再設定する
            if (_fadeImage.sprite == null)
            {
                _fadeImage.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Single2, AtlasSpritePath.Single2.FRM_AUTO_TEXT_BASE_00);
            }
            
            if(_landScapeFadeImage.sprite == null)
            {
                _landScapeFadeImage.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Single2, AtlasSpritePath.Single2.FRM_AUTO_TEXT_BASE_01);
            }
        }
        
        private void Setup()
        {
            UIManager.Instance.AdjustContentsRootRect(_contentRoot);
            _flashActionPlayer = FlashActionPlayer.Load(A2U_PATH, _flashPlayerRoot, scope: FlashActionPlayer.ResourceScope.Hash, resourceHash: ResourceManager.ResourceHash.Common);
            _flashActionPlayer.LoadFlashPlayer();
            _flashActionPlayer.SetSortLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
            _flashActionPlayer.SetSortOffset(FLASH_SORT_OFFSET);

            _flashActionPlayerByRaceLandscape = FlashActionPlayer.Load(A2U_PATH, _raceLandscapeFlashPlayerRoot, scope: FlashActionPlayer.ResourceScope.Hash, resourceHash: ResourceManager.ResourceHash.Common);
            _flashActionPlayerByRaceLandscape.LoadFlashPlayer(ResourceManager.ResourceHash.Common);
            _flashActionPlayerByRaceLandscape.SetSortLayer(UIManager.AFTER_SYSTEM_UI_SORTING_LAYER_NAME);
            _flashActionPlayerByRaceLandscape.SetSortOffset(FLASH_SORT_OFFSET);

            _stopButton.SetOnClick(() =>
            {
                SingleModeAutoPlayAgent.Instance.StopAutoPlay();
            });
            _raceLandscapeStopButton.SetOnClick(() =>
            {
                SingleModeAutoPlayAgent.Instance.StopAutoPlay();
            });
            ChangePortrait();
        }

        public void ChangeLandscapeByRace()
        {
            UIManager.Instance.AdjustContentsRootRect(_contentRoot);
            _flashActionPlayer.Play(GameDefine.A2U_OUT_LABEL);
            _flashActionPlayerByRaceLandscape.Play(GameDefine.A2U_IN_LABEL);
            _fade.SetActiveWithCheck(false);
            _raceLandscapeFade.SetActiveWithCheck(true);
        }

        public void ChangePortrait()
        {
            UIManager.Instance.AdjustContentsRootRect(_contentRoot);
            _flashActionPlayer.Play(GameDefine.A2U_IN_LABEL);
            _flashActionPlayerByRaceLandscape.Play(GameDefine.A2U_OUT_LABEL);
            _fade.SetActiveWithCheck(true);
            _raceLandscapeFade.SetActiveWithCheck(false);
        }

        public void ShowScreen()
        {
            gameObject.SetActive(true);
            _flashActionPlayer.Play(GameDefine.A2U_IN_LABEL);
        }

        public void HideScreen()
        {
            gameObject.SetActive(false);
            _flashActionPlayer.Play(GameDefine.A2U_OUT_LABEL);
        }
    }
}