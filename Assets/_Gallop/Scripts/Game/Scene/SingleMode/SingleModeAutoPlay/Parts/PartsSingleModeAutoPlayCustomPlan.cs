using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayCustomPlan : PartsSingleModeAutoPlayPlanSelectorBase
    {
        private System.Action<int> _onClickPlan;
        private Info _planInfo;

        public void Setup(Info planInfo, System.Action<int> onClickPlan)
        {
            _toggleButton.SetOnClick(OnClickContents);
            _customizeButton.SetOnClick(OnClickCustomizeButton);
            _planInfo = planInfo;
            _onClickPlan = onClickPlan;
            if(planInfo.PlanRecord is IAutoPlayCustomPlanRecord planRecord)
            {
                _planNameText.text = planRecord.PlanName;
                _planDescriptionText.text = planRecord.FormattedPlanDescription;
            }
        }

        public void ToggleRadioButton(int selectedPlanId)
        {
            var isOn = _planInfo.PlanId == selectedPlanId;
            _toggleButton.gameObject.SetActive(!isOn);
            _onImage.gameObject.SetActive(isOn);
        }

        private void OnClickContents()
        {
            _onClickPlan?.Invoke(_planInfo.PlanId);
        }

        private void OnClickCustomizeButton()
        {
            DialogSingleModeAutoPlayPlanDetail.PushDialog(_planInfo, () =>
            {
                //詳細ダイアログから戻ってきたらプラン名と説明を更新
                if(_planInfo.PlanRecord is IAutoPlayCustomPlanRecord planRecord)
                {
                    _planNameText.text = planRecord.PlanName;
                    _planDescriptionText.text = planRecord.FormattedPlanDescription;
                }
            });
        }

        /// <summary>
        /// カスタムプラン情報
        /// </summary>
        public class Info : IAutoPlayPlanInfo
        {
            public IAutoPlayPlanRecord PlanRecord { get; }
            public int PlanId { get; }

            public Info(IAutoPlayCustomPlanRecord planRecord, int planId)
            {
                PlanRecord = planRecord;
                PlanId = planId;
            }
        }
    }
}