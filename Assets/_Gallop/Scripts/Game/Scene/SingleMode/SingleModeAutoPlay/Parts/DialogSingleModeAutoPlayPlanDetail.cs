using System.Collections.Generic;
using System.Linq;
using Gallop.SingleModeAutoPlay;
using UnityEngine;

namespace Gallop
{
    using static ResourcePath;
    using static AutoPlayPauseInfo;

    [AddComponentMenu("")]
    public class DialogSingleModeAutoPlayPlanDetail : DialogInnerBase
    {
        private const string DIALOG_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/DialogSingleModeAutoPlayPlanDetail";
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        [SerializeField] private PartsSingleModeAutoPlayDefaultPlanBaseInfo _planBaseInfo;
        [SerializeField] private PartsSingleModeAutoPlayCustomPlanBaseInfo _customPlanBaseInfo;
        
        //重視項目を選択するトグル
        [SerializeField] private PartsSingleModeAutoPlayModifyPtToggleSelector _modifyPtToggleSelector;
        [SerializeField] private PartsSingleModeLegendAutoPlayModifyToggleSelector[] _legendModifyPtToggleSelectorArray;

        //特定のコマンド条件で停止するトグル
        [SerializeField] private ToggleCommon _stopperCommandToggle;
        [SerializeField] private PartsSingleModeAutoPlayStopperCommandSelector _stopperCommandSelector;

        //特定のイベントで停止するトグル
        [SerializeField] private ToggleCommon _stopperEventToggle;
        [SerializeField] private PartsSingleModeAutoPlayStopperSupportCardSelector _supportCardSelector;
        
        // シナリオイベントによって停止するトグル (選択肢によってもらえるスキルヒントが変わるイベント
        [SerializeField] private ToggleWithText _scenarioEventPauseToggle;
        
        //コンテ可能なレースで停止するトグル
        [SerializeField] private ToggleCommon _continuableRaceToggle;

        // シナリオギミックによって停止するトグル
        [SerializeField] private ToggleWithText _scenarioGimmickPauseToggle;
        [SerializeField] private Transform _scenarioGimmickPauseToggleParent;

        //優先スキルの設定
        [SerializeField] private ButtonCommon _selectPreferenceSkillButton;
        [SerializeField] private Transform _selectedSkillListParent;
        [SerializeField] private PartsSingleModeAutoPlaySelectedPreferenceSkillListItem _selectedPreferenceSkillListItem;
        
        //目標レース周りの設定
        [SerializeField] private PartsSingleModeAutoPlayDefaultPlanTargetRaceOption _defaultPlanTargetRaceOption;
        [SerializeField] private PartsSingleModeAutoPlayCustomPlanTargetRaceOption _customPlanTargetRaceOption;

        [SerializeField] private ToggleCommon _executeRaceContinueToggle;

        private PartsSingleModeAutoPlayPlanSelectorBase.IAutoPlayPlanInfo _planInfo;
        private List<PartsSingleModeAutoPlaySelectedPreferenceSkillListItem> _createdSelectedSkillListItemList = new();
        private List<(int StopConditionId, ToggleWithText Toggle)> _scenarioGimmickPauseToggleList = new();

        public static void PushDialog(PartsSingleModeAutoPlayPlanSelectorBase.IAutoPlayPlanInfo planInfo, System.Action onDestroyDialog)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeAutoPlayPlanDetail>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            dialogData.AddDestroyCallback(onDestroyDialog);
            DialogManager.PushDialog(dialogData);

            if (planInfo is PartsSingleModeAutoPlayDefaultPlan.Info defaultPlanInfo)
            {
                dialog.SetupDefaultPlan(defaultPlanInfo);
            }
            else if (planInfo is PartsSingleModeAutoPlayCustomPlan.Info customPlanInfo)
            {
                dialog.SetupCustomPlan(customPlanInfo);
            }
        }
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            var equipSupportCardArray = WorkDataManager.Instance.SingleMode.Character.EquipSupportCardArray;
            foreach(var equipSupportCard in equipSupportCardArray)
            {
                register.RegisterPath(GetSupportCardSTexturePath(equipSupportCard.SupportCardId));
            }
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();

            dialogData.Title = TextId.SingleModeAutoPlay425004.Text();
            dialogData.RightButtonText = TextId.Common0023.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();

            // 決定時にはAPI通信を行ってからダイアログを閉じるため、自動でダイアログを閉じないようにする
            dialogData.AutoClose = false;
            dialogData.RightButtonCallBack = OnDecide;
            dialogData.LeftButtonCallBack = OnCansel;

            return dialogData;
        }

        private void OnDecide(DialogCommon dialogCommon)
        {
            SingleModeAutoPlayAgent.Instance.Proxy.SavePlanSetting(
                _planInfo.PlanId,
                _planInfo.PlanRecord,
                dialogCommon.Close);
        }

        private void OnCansel(DialogCommon dialogCommon)
        {
            var agentProxy = SingleModeAutoPlayAgent.Instance.Proxy;
            agentProxy.CanselEditPlanSetting(_planInfo.PlanId, _planInfo.PlanRecord.ScenarioId);
            dialogCommon.Close();
        }

        private void SetupDefaultPlan(PartsSingleModeAutoPlayDefaultPlan.Info planInfo)
        {
            _planInfo = planInfo;
            _planBaseInfo.Setup(planInfo, DialogHash);
            _planBaseInfo.gameObject.SetActive(true);
            _customPlanBaseInfo.gameObject.SetActive(false);
            
            _defaultPlanTargetRaceOption.Setup(planInfo.PlanId);
            _defaultPlanTargetRaceOption.gameObject.SetActive(true);
            _customPlanTargetRaceOption.gameObject.SetActive(false);

            _modifyPtToggleSelector.SetupModifyPtInfo(planInfo.PlanId, planInfo.PlanRecord, false);
            SetupScenarioModifyPtToggleSelector();

            SetupStopperCondition(planInfo.PlanRecord, planInfo.PlanId);

            SetupSelectPreferenceSkill(planInfo.PlanId);

            SetupExecuteRaceContinueToggle(planInfo.PlanRecord, planInfo.PlanId);
        }

        private void SetupCustomPlan(PartsSingleModeAutoPlayCustomPlan.Info planInfo)
        {
            _planInfo = planInfo;
            _customPlanBaseInfo.Setup(planInfo);
            _customPlanBaseInfo.gameObject.SetActive(true);
            _planBaseInfo.gameObject.SetActive(false);

            _customPlanTargetRaceOption.Setup(planInfo.PlanId);
            _defaultPlanTargetRaceOption.gameObject.SetActive(false);
            _customPlanTargetRaceOption.gameObject.SetActive(true);

            _modifyPtToggleSelector.SetupModifyPtInfo(planInfo.PlanId, planInfo.PlanRecord);
            SetupScenarioModifyPtToggleSelector();

            SetupStopperCondition(planInfo.PlanRecord, planInfo.PlanId);

            SetupSelectPreferenceSkill(planInfo.PlanId);

            SetupExecuteRaceContinueToggle(planInfo.PlanRecord, planInfo.PlanId);
        }

        private int GetStopperConditionId(IAutoPlayPlanRecord planRecord, int pauseConditionType)
        {
            return MasterDataManager.Instance.masterOmakaseStopCondition
                .GetListWithScenarioIdOrderByStopConditionIdAsc(planRecord.ScenarioId)
                .FirstOrDefault(x => x.ConditionType == pauseConditionType)
                ?.StopConditionId ?? 0;
        }

        /// <summary>
        /// シナリオ固有の重み付け調整トグルのセットアップ
        /// </summary>
        private void SetupScenarioModifyPtToggleSelector()
        {
            SetupLegendModifyPtToggleSelector();
        }

        /// <summary>
        /// 伝説編固有の重み付け調整トグルのセットアップ
        /// </summary>
        private void SetupLegendModifyPtToggleSelector()
        {
            // 伝説編用のIDが1004~1006で定義されている
            const int MODIFY_ID_BASE = 1004;

            for (int i = 0; i < (_legendModifyPtToggleSelectorArray?.Length ?? 0); i++)
            {
                _legendModifyPtToggleSelectorArray[i].Setup(_planInfo.PlanId, MODIFY_ID_BASE + i);
            }
        }

        /// <summary>
        /// 停止条件のセットアップ
        /// </summary>
        /// <param name="planRecord"></param>
        /// <param name="planId"></param>
        private void SetupStopperCondition(IAutoPlayPlanRecord planRecord, int planId)
        {
            //特定のコマンド条件で停止するトグル
            _stopperCommandToggle.isOn = planRecord.ModifyStopperConditionIdList?.Contains(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.MultiTargetCommand)) ?? false;
            _stopperCommandSelector.ToggleInteractable(_stopperCommandToggle.isOn);
            _stopperCommandToggle.onValueChanged.AddListener(isOn =>
            {
                OnChangeStopperCondition(planRecord, planId);
                _stopperCommandSelector.ToggleInteractable(isOn);
            });

            //特定のイベントで停止するトグル
            _stopperEventToggle.isOn = planRecord.ModifyStopperConditionIdList?.Contains(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.StoryEvent)) ?? false;
            _supportCardSelector.ToggleInteractable(_stopperEventToggle.isOn);

            SetupScenarioEventPauseToggle(planRecord, planId);

            _stopperEventToggle.onValueChanged.AddListener(isOn =>
            {
                OnChangeStopperCondition(planRecord, planId);
                _supportCardSelector.ToggleInteractable(isOn);
            });

            _stopperCommandSelector.Setup(planId);
            _supportCardSelector.Setup(planId, DialogHash);

            _continuableRaceToggle.isOn = planRecord.ModifyStopperConditionIdList?.Contains(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.ContinuableRace)) ?? false;
            _continuableRaceToggle.onValueChanged.AddListener(_ =>
            {
                OnChangeStopperCondition(planRecord, planId);
            });

            SetupScenarioGimmickPauseToggle(planRecord, planId);
        }

        private void SetupScenarioEventPauseToggle(IAutoPlayPlanRecord planRecord, int planId)
        {
            var stopperEvents = MasterDataManager.Instance.masterOmakaseStopperEvent.GetListWithScenarioIdOrderByStopperIdAsc(planRecord.ScenarioId);
            if (!stopperEvents.IsNullOrEmpty()) //停止対象のイベントがある場合
            {
                _scenarioEventPauseToggle.gameObject.SetActive(true);

                // シナリオイベントによって停止するトグル
                var storyTitle = TextUtil.GetMasterText(MasterString.Category.SingleModeStoryTitle, stopperEvents.FirstOrDefault().StoryId);
                _scenarioEventPauseToggle.Text.SetText(TextUtil.Format(TextId.SingleModeAutoPlay619009.Text(), storyTitle));

                _scenarioEventPauseToggle.Toggle.isOn = planRecord.ModifyStopperConditionIdList?.Contains(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.ScenarioGimmick)) ?? false;
                _scenarioEventPauseToggle.Toggle.onValueChanged.AddListener(_ =>
                {
                    OnChangeStopperCondition(planRecord, planId);
                });
            }
            else
            {
                //ない場合はトグルを表示しない
                _scenarioEventPauseToggle.gameObject.SetActive(false);
            }
        }

        /// <summary>
        /// トレーニングコマンド停止条件の変更
        /// </summary>
        /// <param name="planId"></param>
        private void OnChangeStopperCondition(IAutoPlayPlanRecord planRecord, int planId)
        {
            var conditionIdList = new List<int>();
            if (_stopperCommandToggle.isOn) conditionIdList.Add(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.MultiTargetCommand));
            if (_stopperEventToggle.isOn) conditionIdList.Add(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.StoryEvent));
            if (_continuableRaceToggle.isOn) conditionIdList.Add(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.ContinuableRace));
            if (_scenarioEventPauseToggle.Toggle.isOn) conditionIdList.Add(GetStopperConditionId(planRecord, (int)AutoPlayPauseConditionType.ScenarioGimmick));

            // シナリオギミックの一時停止条件設定を追加
            conditionIdList.AddRangeSafely(_scenarioGimmickPauseToggleList.Where(x => x.Toggle.Toggle.isOn).Select(x => x.StopConditionId));

            WorkDataManager.Instance.AutoPlayData.ApplyStopperConditionIdList(planId, conditionIdList);
        }

        /// <summary>
        /// 優先スキル表示のセットアップ
        /// </summary>
        private void SetupSelectPreferenceSkill(int planId)
        {
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);
            var shouldShowSelectPreferenceSkillButton = SingleModeAutoPlayPlanDataDefine.MAX_PREFERENCE_SKILL_COUNT > (planRecord.ModifyPreferenceSkillIdList?.Count ?? 0);

            _selectPreferenceSkillButton.SetActiveWithCheck(shouldShowSelectPreferenceSkillButton);
            if (shouldShowSelectPreferenceSkillButton)
            {
                _selectPreferenceSkillButton.SetOnClick(() => OpenDialogSelectPreferenceSkill(0, planId));
            }

            CreateSelectedPreferenceList(planId);
        }

        /// <summary>
        /// 優先スキル選択ダイアログを開く
        /// </summary>
        private void OpenDialogSelectPreferenceSkill(int changeTargetSkillId, int planId)
        {
            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);
            DialogSingleModeAutoPlaySelectPreferenceSkill.PushDialog(changeTargetSkillId, planRecord.ModifyPreferenceSkillIdList, selectedSkillId =>
            {
                WorkDataManager.Instance.AutoPlayData.ApplyPreferenceSkillIdList(planId, selectedSkillId, changeTargetSkillId);
                SetupSelectPreferenceSkill(planId);
            });
        }

        /// <summary>
        /// 設定済みの優先スキル一覧を表示する
        /// </summary>
        private void CreateSelectedPreferenceList(int planId)
        {
            _createdSelectedSkillListItemList.DestroyAndClear();

            var planRecord = WorkDataManager.Instance.AutoPlayData.GetPlanRecord(planId);
            var selectedPreferenceSkillIdList = planRecord.ModifyPreferenceSkillIdList;

            var isEmptySelectedPreferenceSkillIdList = selectedPreferenceSkillIdList.IsNullOrEmptyReadOnlyList();
            _selectedSkillListParent.SetActiveWithCheck(!isEmptySelectedPreferenceSkillIdList);

            if (isEmptySelectedPreferenceSkillIdList) return;

            foreach (var skillId in selectedPreferenceSkillIdList)
            {
                var skillData = MasterDataManager.Instance.masterSkillData.Get(skillId);
                var listItem = Instantiate(_selectedPreferenceSkillListItem, _selectedSkillListParent);
                listItem.SetActiveWithCheck(true);
                listItem.Setup(skillData, DialogHash, () => OnSelect(skillId), () => OnReset(skillId));
                _createdSelectedSkillListItemList.Add(listItem);
            }

            void OnReset(int skillId)
            {
                WorkDataManager.Instance.AutoPlayData.ApplyPreferenceSkillIdList(planId, 0, skillId);
                SetupSelectPreferenceSkill(planId);
            }

            void OnSelect(int skillId)
            {
                OpenDialogSelectPreferenceSkill(skillId, planId);
            }
        }

        /// <summary>
        /// シナリオギミックで停止する場合のトグルをセットアップ
        /// </summary>
        private void SetupScenarioGimmickPauseToggle(IAutoPlayPlanRecord planRecord, int planId)
        {
            const int DIGIT = 100;

            var scenarioId = planRecord.ScenarioId;
            var thresholdScenarioGimmickStopConditionType = scenarioId * DIGIT;

            _scenarioGimmickPauseToggleList = MasterDataManager.Instance.masterOmakaseStopCondition
                .GetListWithScenarioIdOrderByStopConditionIdAsc(planRecord.ScenarioId)
                .Where(x => x.ConditionType > thresholdScenarioGimmickStopConditionType)
                .Select(x =>
                {
                    var toggleWithText = Instantiate(_scenarioGimmickPauseToggle, _scenarioGimmickPauseToggleParent);
                    toggleWithText.Toggle.isOn = planRecord.ModifyStopperConditionIdList?.Contains(x.StopConditionId) ?? false;
                    toggleWithText.Toggle.onValueChanged.AddListener(_ => OnChangeStopperCondition(planRecord, planId));
                    toggleWithText.Text.SetText(TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayStopperCondition, x.StopConditionId));
                    toggleWithText.SetActiveWithCheck(true);
                    return (x.StopConditionId, toggleWithText);
                })
                .ToList();
        }

        private void SetupExecuteRaceContinueToggle(IAutoPlayPlanRecord planRecord, int planId)
        {
            _executeRaceContinueToggle.isOn = planRecord.IsExecuteRaceContinue;
            _executeRaceContinueToggle.SetCallback(isOn => WorkDataManager.Instance.AutoPlayData.ApplyIsExecuteRaceContinue(planId, isOn));
        }
    }
}