using UnityEngine;
using System.Linq;
namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class PartsSingleModeAutoPlayModifyPtToggleSelector : MonoBehaviour
    {
        private enum ModifyIdType
        {
            Tag = 1, //友情トレーニング重視
            Hint = 2, //スキルヒント重視
            Race = 3, //G1レース重視
        }
        
        [SerializeField] private ToggleGroupCommon _tagTrainingToggleGroup;
        [SerializeField] private ToggleGroupCommon _skillHintToggleGroup;
        [SerializeField] private ToggleGroupCommon _raceToggleGroup;

        private int GetModifyId(int scenarioId, ModifyIdType type)
        {
            const int DIGIT = 100;
            return scenarioId * DIGIT + (int)type;
        }

        /// <summary>
        /// 重視項目のセットアップ
        /// </summary>
        /// <param name="planRecord"></param>
        /// <param name="interactable"></param>
        public void SetupModifyPtInfo(int planId, IAutoPlayPlanRecord planRecord, bool interactable = true)
        {
            foreach (var toggle in _tagTrainingToggleGroup.ToggleArray)
            {
                toggle.interactable = interactable;
                toggle.SetNotificationMessage(interactable ? string.Empty : TextId.SingleModeAutoPlay425038.Text());
            }
            
            foreach (var toggle in _skillHintToggleGroup.ToggleArray)
            {
                toggle.interactable = interactable;
                toggle.SetNotificationMessage(interactable ? string.Empty : TextId.SingleModeAutoPlay425038.Text());
            }
            
            foreach (var toggle in _raceToggleGroup.ToggleArray)
            {
                toggle.interactable = interactable;
                toggle.SetNotificationMessage(interactable ? string.Empty : TextId.SingleModeAutoPlay425038.Text());
            }
            
            //友情トレーニング重視
            if (planRecord.ModifyPtRecordList.FirstOrDefault(l => l.ModifyId == GetModifyId(planRecord.ScenarioId, ModifyIdType.Tag)) is { } tagTrainingRecord)
            {
                _tagTrainingToggleGroup.SetToggleOnFromNumber((int)tagTrainingRecord.CurrentSettingType - 1);
                _tagTrainingToggleGroup.SetOnSelectCallback(index => SetOnSettingType(planId, tagTrainingRecord.ModifyId, index));
            }

            //スキルヒント重視
            if (planRecord.ModifyPtRecordList.FirstOrDefault(l => l.ModifyId  == GetModifyId(planRecord.ScenarioId, ModifyIdType.Hint)) is { } hintTrainingRecord)
            {
                _skillHintToggleGroup.SetToggleOnFromNumber((int)hintTrainingRecord.CurrentSettingType - 1);
                _skillHintToggleGroup.SetOnSelectCallback(index => SetOnSettingType(planId, hintTrainingRecord.ModifyId, index));
            }

            //G1レース重視
            if (planRecord.ModifyPtRecordList.FirstOrDefault(l => l.ModifyId == GetModifyId(planRecord.ScenarioId, ModifyIdType.Race)) is { } g1RaceRecord)
            {
                _raceToggleGroup.SetToggleOnFromNumber((int)g1RaceRecord.CurrentSettingType - 1);
                _raceToggleGroup.SetOnSelectCallback(index => SetOnSettingType(planId, g1RaceRecord.ModifyId, index));
            }
        }

        private void SetOnSettingType(int planId, int modifyId, int index)
        {
            WorkDataManager.Instance.AutoPlayData.ApplyModifyPtSetting(planId, modifyId, (WorkAutoPlayModifyPtRecord.SettingType)(index + 1));
        }
    }
}