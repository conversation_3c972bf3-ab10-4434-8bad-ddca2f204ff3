using UnityEngine;
using System;

namespace Gallop
{
    using static ResourcePath;

    [AddComponentMenu("")]
    public sealed class DialogSingleModeAutoPlayChangePlanDescription : DialogEditNameBase
    {
        private const string DIALOG_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/DialogSingleModeAutoPlayChangePlanDescription";
        public const int PLAN_DESCRIPTION_LIMIT = 24; //最大24文字
        public const int LINE_LIMIT = 12; //一行12文字
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        public static void PushDialog(string description, Action<DialogCommon, string> onDecide)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeAutoPlayChangePlanDescription>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            dialogData.RightButtonCallBack = (x) => { onDecide?.Invoke(x, dialog._inputField.text); };
            DialogManager.PushDialog(dialogData);
            dialog.Setup(description, InputFieldCommon.InputTextType.SingleModeAutoPlayPlanDescription);
        }

        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.SingleModeAutoPlay425018.Text();
            return dialogData;
        }
        
        protected override void Setup(string currentName, InputFieldCommon.InputTextType inputTextType)
        {
            base.Setup(currentName, inputTextType);
#if DMM || UNITY_EDITOR || ANDROID_PC
            _inputField.ChangeTextCallEndcallback = true;
#endif
        }

        protected override void OnEndEdit(string text)
        {
            text = TextUtil.RemoveNewLine(text);
            //文字数制限をかける
            if (text.Length > PLAN_DESCRIPTION_LIMIT)
            {
                text = text.Substring(0, PLAN_DESCRIPTION_LIMIT);
            }
            _inputField.text = text;

            if (GetDialog() is DialogCommon dialog)
            {
                var btn = dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
                btn.interactable = CheckText(_inputField.text);
            }
        }
    }
}