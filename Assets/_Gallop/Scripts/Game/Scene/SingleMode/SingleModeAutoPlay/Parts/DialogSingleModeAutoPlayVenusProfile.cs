using UnityEngine;

namespace Gallop
{
    using static ResourcePath;
    [AddComponentMenu("")]
    public class DialogSingleModeAutoPlayVenusProfile : DialogInnerBase
    {
        private const string DIALOG_PATH = SINGLE_MODE_UI_ROOT + "AutoPlay/DialogSingleModeAutoPlayVenusProfile";
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;
        
        [SerializeField] private RawImageCommon _image;
        [SerializeField] private TextCommon _message;

        public static void PushDialog(PartsSingleModeAutoPlayDefaultPlan.Info planInfo)
        {
            var dialog = LoadAndInstantiatePrefab<DialogSingleModeAutoPlayVenusProfile>(DIALOG_PATH);
            var dialogData = dialog.CreateDialogData();
            dialogData.Title = TextId.Character0009.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            DialogManager.PushDialog(dialogData);
            dialog.Setup(planInfo);
        }

        private void Setup(PartsSingleModeAutoPlayDefaultPlan.Info planInfo)
        {
            _image.texture = ResourceManager.LoadOnHash<Texture2D>(
                GetCharaStandMediumImagePath(planInfo.PlanCharaId,
                GameDefine.GetSpecialCharaDefaultDressId(planInfo.PlanCharaId)), 
                DialogHash);
                
            _message.text = TextUtil.GetMasterText(MasterString.Category.SingleModeAutoPlayVenusProfile, planInfo.PlanId);
        }
    }
}