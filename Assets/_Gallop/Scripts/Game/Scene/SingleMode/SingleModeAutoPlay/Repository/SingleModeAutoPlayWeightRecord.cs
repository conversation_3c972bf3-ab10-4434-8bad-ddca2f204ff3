namespace Gallop.SingleModeAutoPlay
{
    /// <summary>
    /// おまかせ育成の重みと適用される範囲をまとめたレコード
    /// </summary>
    public class SingleModeAutoPlayWeightRecord :
        IConditionValueRangeRecord,
        IConditionValueEmptyRecord,
        IConditionValueOneUseRecord,
        IConditionFilterRecord
    {
        /// <summary> 適用種別 </summary>
        private int ConditionType { get; }

        /// <summary> 設定ID </summary>
        private int ConditionSetId { get; }

        /// <summary> 設定値１ </summary>
        private int ConditionValue1 { get; }

        /// <summary> 設定値２ </summary>
        private int ConditionValue2 { get; }

        /// <summary> 重み </summary>
        private int Weight { get; }

        public SingleModeAutoPlayWeightRecord(int conditionType, int conditionSetId, int conditionValue1, int conditionValue2, int weight)
        {
            ConditionType = conditionType;
            ConditionSetId = conditionSetId;
            ConditionValue1 = conditionValue1;
            ConditionValue2 = conditionValue2;
            Weight = weight;
        }

        #region 適用範囲としての用途
        int IConditionValueRangeRecord.Min => ConditionValue1;
        int IConditionValueRangeRecord.Max => ConditionValue2;
        #endregion 適用範囲としての用途

        #region 一種類の設定データとしての用途
        int IConditionValueOneUseRecord.ConditionValue => ConditionValue1;
        #endregion 1つのみ設定される設定データとしての用途
        
        #region 重みデータとしての用途
        int IWeightRecord.ConditionSetId => ConditionSetId;
        int IWeightRecord.Weight => Weight;
        #endregion 重みデータとしての用途

        #region フィルタデータとしての用途
        int IConditionFilterRecord.ConditionType => ConditionType;

        int IConditionFilterRecord.ConditionValue1 => ConditionValue1;

        int IConditionFilterRecord.ConditionValue2 => ConditionValue2;
        #endregion フィルタデータとしての用途
    }

    #region インターフェース
    /// <summary>
    /// 重み
    /// </summary>
    public interface IWeightRecord
    {
        public int ConditionSetId { get; }
        public int Weight { get; }
    }

    /// <summary>
    /// 適用範囲を設定している場合に使用
    /// </summary>
    public interface IConditionValueRangeRecord : IWeightRecord
    {
        public int Min { get; }
        public int Max { get; }
    }

    /// <summary>
    /// 条件が1つのみ設定される場合に使用（バッドコンディションを指定する場合など）
    /// </summary>
    public interface IConditionValueOneUseRecord : IWeightRecord
    {
        public int ConditionValue { get; }
    }

    /// <summary>
    /// 適用条件がConditionTypeのみで判定できて、ConditionValueが設定されない場合に使用（予約レースターンなど）
    /// </summary>
    public interface IConditionValueEmptyRecord : IWeightRecord
    {
        
    }

    public interface IConditionFilterRecord
    {
        public int ConditionType { get; }
        public int ConditionValue1 { get; }
        public int ConditionValue2 { get; }
    }
    #endregion インターフェース
}