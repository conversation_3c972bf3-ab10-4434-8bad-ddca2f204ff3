using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ライブ編：期待度ゲージ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioLiveExpectationGauge : MonoBehaviour
    {
        [SerializeField]private GaugeCommon _gauge;
        [SerializeField]private GameObject _maxIcon;

        /// <summary>
        /// 設定
        /// </summary>
        public void Setup()
        {
            var workLive = WorkDataManager.Instance.SingleMode.ScenarioLive;
            _gauge.Set(workLive.GetExpectationGauge(), 1f);
            _maxIcon.SetActive(workLive.IsMaxExpectationGauge());
        }
    }
}
