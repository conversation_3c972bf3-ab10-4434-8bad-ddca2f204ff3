using UnityEngine;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// 追加シナリオでレースリザルトカットイン中に表示する追加の獲得報酬A2U
    /// クライマックス編:支持Pt
    /// 凱旋門賞編:支持Pt
    /// </summary>
    public sealed class PartsSingleModeScenarioCommonRaceResultPointGainA2U
    {
        private FlashPlayer _player;
        private static string A2UPath => ResourcePath.SINGLE_MODE_SCENARIO_FREE_WIN_PT_00;
        private const float IN_DELAY = 0.2f;
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(A2UPath);
        }

        public static PartsSingleModeScenarioCommonRaceResultPointGainA2U Create(Transform parent)
        {
            var component = new PartsSingleModeScenarioCommonRaceResultPointGainA2U();
            component.Setup(parent);
            return component;
        }

        private void Setup(Transform parent)
        {
            _player = FlashLoader.LoadOnView(A2UPath, parent);
        }
        
        public void Play(int point, int sortOffset, bool useSystemUiSortingLayer, Sprite sprite)
        {
            DOVirtual.DelayedCall(IN_DELAY,()=>
            {
                var digits = Math.Digit(point); //桁数
                _player.Play(GameDefine.A2U_IN_LABEL);
                if (useSystemUiSortingLayer)
                {
                    _player.SortLayer = UIManager.SYSTEM_UI_SORTING_LAYER_NAME;
                }
                _player.SortOffset = sortOffset;
                _player.GetMotion("MOT_mc_winpt00").SetMotionPlay(TextUtil.Format("digit{0:D2}",digits));
                _player.GetImageNumber("OBJ_mc_num_winpt00").SetValue(point, true);
                _player.SetSprite("PLN_dum_ico_winpt00", sprite);
            });
        }
    }
}