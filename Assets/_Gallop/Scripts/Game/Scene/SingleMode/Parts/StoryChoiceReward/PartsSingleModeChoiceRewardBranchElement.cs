using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.UI;
using static Gallop.StaticVariableDefine.SingleMode.PartsSingleModeChoiceRewardBranchElement;

namespace Gallop
{
    /// <summary>
    /// 選択肢報酬のうち、確率分岐1つ分の報酬情報を表示するパーツ
    /// </summary>
    [AddComponentMenu("")] // Warning対策 AddComponentから非表示になる
    public class PartsSingleModeChoiceRewardBranchElement : MonoBehaviour
    {
        // 分岐の表示
        [SerializeField] private TextCommon _branchLabel;

        [SerializeField] private TextCommon _rewardText;

        // スキル表示
        [SerializeField] private GameObject _skillRoot;
        [SerializeField] private RectTransform _skillListRect;
        [SerializeField] private PartsSingleModeChoiceRewardSkillElement _skillElementBase;

        // 連続イベントで獲得できるスキルの表示
        [SerializeField] private PartsSingleModeChoiceRewardAnotherEventSkillElement _anotherEventSkillItem;

        [SerializeField] private RectTransform _bgRectTransform;

        private PartsSingleModeChoiceRewardBranchElementViewModel _viewModel;
        private List<PartsSingleModeChoiceRewardSkillElement> _skillElementList = new List<PartsSingleModeChoiceRewardSkillElement>();

        private const float SKILL_ROOT_HEIGHT_OFFSET = 5f;

        public void Setup(PartsSingleModeChoiceRewardBranchElementViewModel viewModel)
        {
            _viewModel = viewModel;

            gameObject.SetActiveWithCheck(true);

            SetBranchLabel();
            SetRewardText();
            SetSkill();
            SetAnotherEventSkill();
            UpdateSize();
        }

        /// <summary>
        /// 分岐ラベルをセットする
        /// </summary>
        private void SetBranchLabel()
        {
            _branchLabel.SetActiveWithCheck(_viewModel.IsMultipleBranch);

            if (_viewModel.IsMultipleBranch)
            {
                _branchLabel.text = TextId.SingleMode570004.Format(_viewModel.BranchIndex + 1);
            }
        }

        /// <summary>
        /// 報酬文言をセットする
        /// </summary>
        private void SetRewardText()
        {
            _rewardText.SetActiveWithCheck(false);

            if (_viewModel.HasRewardText)
            {
                _rewardText.SetActiveWithCheck(true);
                _rewardText.SetOverrideIconSize(REWARD_TEXT_ICON_SIZE);

                // テキスト中の改行を反映させる
                var rewardText = TextUtil.ReplaceNewLine(_viewModel.RewardText);
                _rewardText.SetTextWithCustomTagMultiLine(rewardText);

                _rewardText.UpdatePreferedHeight();
            }
            else if (!_viewModel.HasAnyRewardInfo)
            {
                // #153521 報酬が無い分岐では「特になし」を表示する
                _rewardText.SetActiveWithCheck(true);
                _rewardText.text = TextId.SingleMode0255.Text();
                _rewardText.UpdatePreferedHeight();
            }
        }

        /// <summary>
        /// スキルの表示
        /// </summary>
        private void SetSkill()
        {
            // 報酬にスキルが存在する場合のみ表示
            _skillRoot.SetActiveWithCheck(_viewModel.HasSkillRewardInfo);
            _skillElementList.Clear();

            if (_viewModel.HasSkillRewardInfo)
            {
                foreach (var skillElementViewModel in _viewModel.SkillRewardInfoArray)
                {
                    var skillElement = Instantiate(_skillElementBase, _skillListRect);
                    skillElement.Setup(skillElementViewModel);
                    _skillElementList.Add(skillElement);
                }
            }
        }

        /// <summary>
        /// 連続イベントで獲得できるスキルの表示
        /// </summary>
        private void SetAnotherEventSkill()
        {
            // 該当する報酬がある場合のみ表示
            _anotherEventSkillItem.SetActiveWithCheck(_viewModel.HasAnotherEventSkillRewardInfo);

            if (_viewModel.HasAnotherEventSkillRewardInfo)
            {
                _anotherEventSkillItem.Setup(_viewModel.AnotherEventSkillRewardInfo, _viewModel.HasRewardText, _viewModel.HasSkillRewardInfo);
            }
        }

        /// <summary>
        /// 表示内容に合わせてサイズを更新する
        /// </summary>
        private void UpdateSize()
        {
            // スキルのオブジェクトの高さを表示要素に合わせる
            LayoutRebuilder.ForceRebuildLayoutImmediate(_skillListRect);
            var skillRootRect = _skillRoot.transform as RectTransform;
            if (skillRootRect != null)
            {
                // スキルプレートのドロップシャドウはマージンの考慮に入れないので高さを差し引く
                skillRootRect.SetSizeDeltaY(_skillListRect.sizeDelta.y - SKILL_ROOT_HEIGHT_OFFSET);
            }

            // 背景のサイズをContentSizeFitterで調整し、自身のサイズを背景に合わせる
            LayoutRebuilder.ForceRebuildLayoutImmediate(_bgRectTransform);
            var bgHeight = _bgRectTransform.sizeDelta.y;

            if (transform is RectTransform rectTransform)
            {
                rectTransform.SetSizeDeltaY(bgHeight);
            }
        }
    }   
}
