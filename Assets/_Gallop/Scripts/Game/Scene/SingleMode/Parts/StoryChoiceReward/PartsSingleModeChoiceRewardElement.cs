using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 選択肢一つ分の報酬情報を表示するパーツ
    /// </summary>
    [AddComponentMenu("")] // Warning対策 AddComponentから非表示になる
    public class PartsSingleModeChoiceRewardElement : LoopScrollItemBase
    {
        [SerializeField] private TextCommon _labeltext;
        [SerializeField] private Transform _branchElementRootTransform;
        [SerializeField] private PartsSingleModeChoiceRewardBranchElement _branchElementBase;
        [SerializeField] private RectTransform _branchRootRect;
        [SerializeField] private GameObject _emptyObject;

        private List<PartsSingleModeChoiceRewardBranchElement> _branchElementList = new List<PartsSingleModeChoiceRewardBranchElement>();
        private PartsSingleModeChoiceRewardElementViewModel _viewModel;

        private const float LABEL_SPACING = 25f;

        public void Setup(PartsSingleModeChoiceRewardElementViewModel viewModel)
        {
            _viewModel = viewModel;
            gameObject.SetActiveWithCheck(true);

            // 報酬が空の場合の表示
            var isRewardEmpty = _viewModel.IsEmptyRewardInfo;
            _emptyObject.SetActiveWithCheck(isRewardEmpty);

            // 選択肢テキスト
            SetLabelText(_viewModel.LabelText);

            // 報酬内容
            if (!isRewardEmpty)
            {
                SetBranchElement(_viewModel.BranchViewModelList);
            }

            UpdateSize();
        }

        /// <summary>
        /// 選択肢テキストをセット
        /// </summary>
        /// <param name="labelText"></param>
        private void SetLabelText(string labelText)
        {
            _labeltext.text = labelText;
        }

        /// <summary>
        /// 分岐ごとの報酬情報をセット
        /// </summary>
        /// <param name="viewModelList"></param>
        private void SetBranchElement(IReadOnlyList<PartsSingleModeChoiceRewardBranchElementViewModel> viewModelList)
        {
            _branchElementList.Clear();

            // ViewModelが分岐1つに対応する
            foreach (var branchViewModel in viewModelList)
            {
                var branchElement = Instantiate(_branchElementBase, _branchElementRootTransform);
                branchElement.Setup(branchViewModel);
                _branchElementList.Add(branchElement);
            }
        }

        /// <summary>
        /// 表示内容に合わせてサイズを更新する
        /// </summary>
        private void UpdateSize()
        {
            var height = 0f;

            LayoutRebuilder.ForceRebuildLayoutImmediate(_branchRootRect);
            var branchRootHeight = _branchRootRect.sizeDelta.y;

            // オブジェクトの高さを求める
            height = _labeltext.preferredHeight + branchRootHeight;
            // 選択肢テキストと報酬要素の間の間隔を足す
            height += LABEL_SPACING;

            var rect = transform as RectTransform;
            if (rect != null)
            {
                rect.SetSizeDeltaY(height);
            }
        }
    }   
}
