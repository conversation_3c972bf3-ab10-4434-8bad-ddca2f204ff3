using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 選択肢報酬表示：スキルの直接獲得、スキルヒント
    /// </summary>
    [AddComponentMenu("")] // Warning対策 AddComponentから非表示になる
    public class PartsSingleModeChoiceRewardSkillElement : MonoBehaviour
    {
        [SerializeField] private PartsSingleModeSkillListItem _skillItem;
        [SerializeField] private TextCommon _skillText;

        public void Setup(ISingleModeChoiceRewardInfoSkillElement viewModel)
        {
            gameObject.SetActiveWithCheck(true);

            // スキルプレートはSサイズで表示する
            var skillInfo = new PartsSingleModeSkillListItem.Info(viewModel.SkillId)
            {
                UseSkillListButton = true,
                OverrideSkillIconSize = true,
                SkillIconSize = StaticVariableDefine.SingleMode.PartsSingleModeSkillList.SKILLICON_SIZE_S,
                IsDrawDesc = false,
                IsDrawNeedSkillPoint = true,
            };
            _skillItem.UpdateItem(skillInfo);

            _skillText.text = viewModel.LabelText;
        }
    }   
}
