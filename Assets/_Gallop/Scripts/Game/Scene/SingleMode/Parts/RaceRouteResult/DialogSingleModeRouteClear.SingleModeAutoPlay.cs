namespace Gallop
{
    public partial class DialogSingleModeRouteClear
    {
        public DialogSingleModeRouteClearProxy AutoPlayProxy { get; private set; }

        public class DialogSingleModeRouteClearProxy
        {
            private DialogSingleModeRouteClear _dialog;

            public DialogSingleModeRouteClearProxy(DialogSingleModeRouteClear dialog)
            {
                _dialog = dialog;
            }

            public bool IsNextButtonAnimationRunning => _dialog.IsNextButtonAnimationRunning;
            public bool NextButtonIsActive => _dialog._button.IsActive();
            
            public void OnClick()
            {
                if (!_dialog._button.enabled) return;

                _dialog._button.onClick?.Invoke();
            }
        }
    }
}