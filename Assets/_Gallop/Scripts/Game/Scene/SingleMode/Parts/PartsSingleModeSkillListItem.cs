using System;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// シングル開始画面：スキルリスト子要素
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeSkillListItem : LoopScrollItemBase
    {
        #region Nested Types

        public sealed class Info
        {
            public int Id { get; private set; }
            public int Level { get; set; } = 1;
            public string Name => MasterData == null ? "" : MasterData.Name;
            public int NeedSkillPoint { get; set; }        //習得に必要なスキルポイント
            public bool IsDrawNeedSkillPoint { get; set; }  //習得に必要なスキルポイントを表示するか
            public string Desc => MasterData == null ? "" : MasterData.Remarks;
            public bool IsDrawDesc { get; set;} = true;
            public string Condition => MasterData == null ? "" : MasterData.Condition;
            public int Probability { get; set; }    //習得確率
            public bool IsDrawExp { get; set; } //EXP表示が必要か(編成時は必要ない)
            public bool IsNew { get; set; }     //New表示フラグ
            public bool IsHintLvUp { get; set; }     //ヒントLvアップするフラグ
            public int HintLv { get; set; }    //スキルのヒントLv
            public bool IsDrawHintLvUpParamOnDetail { get; set; } = true; // スキル詳細ダイアログでスキルのヒントLv+割引表示するか (アウトゲームでは必要、育成内では不要)
            public int NeedTalentLevel { get; set; } // 習得可能になる覚醒Lv
            public bool IsDrawNeedTalentLevel { get; set; } // 習得可能になる覚醒Lvを表示するか
            public bool OverrideSkillIconSize { get; set; } = false; // trueの場合は4行/3行のサイズ変更無効
            public Vector2 SkillIconSize;
            public bool UseSkillListButton { get; set; } = false;

            public bool IsLevelUpColor { get; set; }     //Levelの色を上昇色（オレンジ）にするかどうか
#if CYG_DEBUG
            public int DebugSkillPoint { get; set; }
            public bool DebugIsInherit { get; set; } = false; //継承によって得られたスキルならtrue
#endif
            public bool IsSkillLearningNameMode { get; set; }     //スキル習得画面でバッドスキル名称を変化させるフラグ
            public bool IsDrawUniqSkillInfo { get; set; }  // スキル詳細ダイアログで固有スキルの説明表示するか（育成前のウマ娘詳細は表示してください）
            public bool IsEnableRaceFitAssistanceLabel { get; set; }  // トレーナーガイド；おすすめラベルを表示するか

            public bool IsDisplayUpgradeSkill { get; set; } = true; // 進化ボタンの表示を非表示にするかどうか、デフォルトでは表示する
            
            public SkillDefine.SkillPlateType PlateType { get; set; } = SkillDefine.SkillPlateType.Null;

            /// <summary> 限定スキルのタイプ </summary>
            public SkillDefine.SkillLimitedType LimitedType { get; set; } = SkillDefine.SkillLimitedType.Null;

            public MasterSkillData.SkillData MasterData { get; private set; }
            /// <summary> 条件達成アイコン表示 </summary>
            public bool IsConditionComplete { get; set; }
            /// <summary> 進化条件ボタンを表示するための進化条件マスター </summary>
            public MasterSkillUpgradeDescription.SkillUpgradeDescription MasterSkillUpgradeDescription { get; set; }
            /// <summary> 進化条件ボタンを表示するための進化条件マスター（シナリオ進化） </summary>
            public MasterSkillUpgradeSpeciality.SkillUpgradeSpeciality MasterSkillUpgradeSpeciality { get; set; }
            /// <summary> 育成中の進化条件ボタン表示か（進化条件ダイアログで育成状況から達成判定を行う） </summary>
            public bool IsSingleModeSkillUpgradeDescription { get; set; }
            /// <summary> 習得済みアイコン表示 </summary>
            public bool IsDrawAcquire { get; set; }

            /// <summary>
            /// 進化先があれば進化iボタンを表示したい要求フラグ。
            /// スキル詳細ダイアログなどで、シナリオ進化があれば表示したい。
            /// さらにSkillUpgradeCardIdの指定があれば、育成ウマ娘の進化スキルである場合に進化iボタンを表示する。
            /// </summary>
            public bool IsRequestSkillUpgradeButton;
            /// <summary>
            /// 進化iボタンから表示されるスキル進化ダイアログ内のシナリオ進化スキルを特定シナリオにフィルターする
            /// 育成内では選択中のシナリオのみにフィルターするために使用する
            /// </summary>
            public int ScenarioSkillUpgradeFilterScenarioId;
            /// <summary> 進化ありか(Sリスト用) </summary>
            public bool IsSkillUpgradeBadge;
            /// <summary> 進化条件表示用のカードID </summary>
            public int SkillUpgradeCardId;

            /// <summary> 試験ボーナスアイコン表示 </summary>
            public bool IsEventBonusSkill { get; set; }

            /// <summary> ロングタップを有効にするか</summary>
            public bool IsIconLongTapEnable { get; set; } = true;
            
            /// <summary> トレーナーガイドモードのスキル詳細を開くか</summary>
            public bool IsAssist  { get; set; } = false;

            /// <summary> Skillの有効/無効ラベル表示 </summary>
            /// <remarks> 基本InteractableでIsOverrideInteractableMulColorを変更しない限り無効にはならない </remarks>>
            public enum InteractableMulColorType
            {
                Default, // デフォルト(上書きしない)
                InInteractable, // 無効時の色
                Interactable, // 有効時の色
            }

            public InteractableMulColorType IsOverrideInteractableMulColor { get; set; } = InteractableMulColorType.Interactable;

            public Info(int id)
            {
                Id = id;
                MasterData = MasterDataManager.Instance.masterSkillData.Get(Id);
                var masterNeedPoint = MasterDataManager.Instance.masterSingleModeSkillNeedPoint.Get(Id);

                if (MasterData == null)
                {
                    Debug.LogError("SKillId" + Id + " がマスターに存在しません");
                    return;
                }
                NeedSkillPoint = masterNeedPoint != null ? masterNeedPoint.NeedSkillPoint : 0;
                
                PlateType = (SkillDefine.SkillPlateType)MasterData.PlateType;
            #if CYG_DEBUG
                if (RaceDebugger.ForceSkillPlateType != SkillDefine.SkillPlateType.Null)
                {
                    PlateType = RaceDebugger.ForceSkillPlateType;
                }
            #endif

                // #72659 SkillPlateTypeの指定があれば「アンロックレース限定スキル」文言表示。
                switch (PlateType)
                {
                    case SkillDefine.SkillPlateType.LimitedEventBuff:
                        LimitedType = SkillDefine.SkillLimitedType.LimitedEventSkill;
                        break;
                    case SkillDefine.SkillPlateType.Null:
                        LimitedType = SkillDefine.SkillLimitedType.Null;
                        break;
                    default:
                        LimitedType = SkillDefine.SkillLimitedType.StoryUnlockSkill;
                        break;
                }

            }
        }

        #endregion Nested Types

        #region Const
        //スキル説明の一行当たりの文字数
        private const int DISC_CHAR_NAME = 18;
        
        /// <summary> 試験ボーナスアイコンのX座標 </summary>
        private const int EVENT_BONUS_BADGE_CONDITION_COMPLETE_POS_X = -206;
        private const int EVENT_BONUS_BADGE_DEFAULT_POS_X = -398;
        
        // スキルテキスト表示欄が3行分の場合の高さ
        private const float HEIGHT_THREE_LINES = 204f;
        // スキルテキスト表示欄が4行分の場合の高さ
        private const float HEIGHT_FOUR_LINES = 248f;
        // 進化表示時のスキルアイコンのY座標
        private const float SKILL_ICON_UPGRADE_POS_Y = 26;

        #endregion

        #region SerializeField
        [SerializeField]
        private ImageCommon _bgImage = null;
        [SerializeField]
        private ButtonCommon _bgButton = null;
        [SerializeField]
        private ImageCommon _lineImage = null;
        [SerializeField]
        private Transform _skillIconRoot = null;
        [SerializeField]
        private TextCommon _nameText = null;
        [SerializeField]
        private TextCommon _levelText = null;
        [SerializeField]
        private TextCommon _descText = null;
        [SerializeField]
        private GameObject _newIconObject = null;
        [SerializeField]
        private GameObject _needSkillPointRoot = null;
        [SerializeField]
        private TextCommon _needSkillPointText = null;
        [SerializeField]
        private TextCommon _needTalentLevelText;
        [SerializeField]
        private PartsOutsideBlinkImage _blinkImage = null;
        [SerializeField]
        private ImageCommon _eventLimitedImage = null;
        /// <summary> 5レース限定アイコン </summary>
        [SerializeField]
        private ImageCommon _raceLimitedImage = null;
        [SerializeField]
        private ImageCommon _hintLabelObject = null;
        [SerializeField]
        private TextCommon _hintLvText = null;
        /// <summary> トレーナーガイド：おすすめラベル </summary>
        [SerializeField]
        private PartsRaceFitAssistanceLabel _partsRaceFitAssistanceLabel = null;
        /// <summary> 条件達成アイコン </summary>
        [SerializeField]
        private GameObject _conditionCompleteIcon;
        /// <summary> 進化条件ボタン </summary>
        [SerializeField]
        private ButtonCommon _upgradeConditionButton;
        /// <summary> 進化バッジ(Sサイズリスト用) </summary>
        [SerializeField]
        private GameObject _upgradeBadgeRoot;
        /// <summary> 進化(iボタン) </summary>
        [SerializeField]
        private ButtonCommon _upgradeButton;
        /// <summary> 進化(iボタン)親 </summary>
        [SerializeField]
        private GameObject _upgradeRoot;
        /// <summary> 習得済アイコン </summary>
        [SerializeField]
        private GameObject _acquireIcon;
        /// <summary> 試験ボーナスアイコン </summary>
        [SerializeField]
        private GameObject _eventBonusIcon;

        #endregion SerializeField

        #region Member

        private Info _info = null;

        private SkillIcon _skillIcon = null;

        private SkillIconLongTapInfoPop _skillInfoPop;

        private GameObject _hintLvMaxEffect;

        #endregion Member

        #region Method

        /// <summary>
        /// リストアイテムを更新
        /// </summary>
        /// <param name="skillInfo"> 更新するスキルデータ </param>
        /// <param name="isPlateEffectEnable"> スキルプレートの明滅を行うかどうか </param>
        public void UpdateItem(Info skillInfo, bool isPlateEffectEnable = true)
        {
            _info = skillInfo;
            if (_info == null) return;

            // レア別下地
            if (_bgImage != null)
            {
                if(_info.UseSkillListButton)
                {
                    _bgImage.sprite = SingleModeUtils.GetSkillButtonSprite(_info.MasterData);
                    _bgButton.enabled = true;
                    SetupOnClickSkillButton(skillInfo);
                }
                else
                {
                    _bgImage.sprite = SingleModeUtils.GetSkillFrameSprite(_info.MasterData);
                    _bgButton.enabled = false;
                }
            }
            // レア別ライン
            if (_lineImage != null)
            {
                _lineImage.sprite = SingleModeUtils.GetSkillLineSprite(_info.MasterData);
            }
            
            // スキル名
            SetupNameText();
            
            // スキル説明
            if (_descText && skillInfo.IsDrawDesc)
            {
                _descText.SetActiveWithCheck(true);
                _descText.text = GallopUtil.LineHeadWrap(_info.Desc, DISC_CHAR_NAME);
            }
            
            if (_skillIcon == null)
            {
                // スキルアイコン生成
                var skillIconPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.PARTS_SKILL_ICON_PATH);
                var skillIconObj = Instantiate(skillIconPrefab, _skillIconRoot);
                _skillIcon = skillIconObj.GetComponent<SkillIcon>();
                
                //フレームが押下できる場合長押しを有効化する必要はないのでenabledを切り替える
                _skillIcon.MyButton.enabled = _info.IsIconLongTapEnable;
                
                if (_info.UseSkillListButton)
                {
                    _skillIcon.MyButton.enabled = false;
                }

                if(_info.OverrideSkillIconSize)
                {
                    _skillIcon.SetSize(_info.SkillIconSize);
                }
            }

            // 習得に必要なスキルポイント表示
            _needSkillPointRoot.SetActive(false); // 一度非表示
            var canDrawSkillNeedPoint = !(_info.UseSkillListButton && _info.OverrideSkillIconSize);// 小さいサイズのスキルリストを使用している時は必要ポイント表示できません（スキル詳細ダイアログでは表示したい）
            if (canDrawSkillNeedPoint)
            {
                if (_info.IsDrawNeedSkillPoint && _info.NeedSkillPoint > 0)
                {
                    _needSkillPointRoot.SetActive(true);
                    _needSkillPointText.text = _info.NeedSkillPoint.ToString();
                }
            }

            // 習得可能になる覚醒Lv表示
            _needTalentLevelText.SetActiveWithCheck(false);
            if (_info.IsDrawNeedTalentLevel && _info.NeedTalentLevel > 1)
            {
                _needTalentLevelText.SetActiveWithCheck(true);
                _needTalentLevelText.text = string.Format(TextId.Character0065.Text(), _info.NeedTalentLevel);
                var gradientColor = CardUpgradeUtil.GetTalentLevelTextGradientColor(_info.NeedTalentLevel);
                _needTalentLevelText.VerticalGradientColor = gradientColor;
                _needTalentLevelText.OutlineColor = OutlineColorType.White; 
                _needTalentLevelText.UpdateColor();
            }

            // Newアイコン
            if (_newIconObject)
            {
                _newIconObject.SetActive(_info.IsNew);
            }

            // 習得スキル
            SetGetSkill();

            // 背景点滅エフェクト。
            var plateType = isPlateEffectEnable ? _info.PlateType : SkillDefine.SkillPlateType.Null;
            RaceUtil.SetupSkillPlateEffect(_blinkImage, plateType);
#if CYG_DEBUG
            SetDebug();
#endif
            // イベント限定スキルかどうか
            if (ChallengeMatchUtil.IsMatchBonusSkill(_info.MasterData))
            {
                _eventLimitedImage.gameObject.SetActive(true);
                var skillDesc = _info.Desc;
                var skillPercentData = ChallengeMatchUtil.GetBonusSkillValue(_info.Level, _info.MasterData);
                skillDesc = TextUtil.Format(skillDesc,
                    ChallengeMatchUtil.ConvertBonusSkillValue(skillPercentData.statusValue),
                    ChallengeMatchUtil.ConvertBonusSkillValue(skillPercentData.motiovationValue));
                _descText.text = skillDesc;
            }
            else if(SingleModeUtils.IsDifficultyModeAdditionSkill(_info.MasterData.Id)) //ゴルシモード2022のデバフスキルか
            {
                _eventLimitedImage.gameObject.SetActive(true);
                _eventLimitedImage.sprite = AtlasSpritePath.SingleMode.GetDifficultySkillEventLimitedTextSprite(); //「イベント限定」テキスト画像を差し替える
            }
            else
            {
                _eventLimitedImage.gameObject.SetActive(false);
            }

            if (_hintLabelObject != null && _hintLvText != null)
            {
                SetHintLv();
            }
            
            if (_partsRaceFitAssistanceLabel != null)
            {
                SetRaceFitAssistanceLabel();
            }

            // 条件達成アイコン
            SetConditionCompleteIcon();
            // 進化条件ボタン
            SetUpgradeConditionButton();
            // 進化バッジ
            SetUpgradeBadge();
            // 進化iボタン
            SetupUpgradeButton();
            // 習得済アイコン
            SetAcquire();
            
            // イベントボーナス表示切替
            UpdateEventBonusBadge();

            var isHeroesSkill = HeroesUtil.IsHeroesSkill(_info.PlateType);
            // 5レース限定アイコン
            _raceLimitedImage.SetActiveWithCheck(isHeroesSkill);
            if (!_info.OverrideSkillIconSize)
            {
                // ヒーロースキルの場合はスキルテキストの表示欄を3行分にする
                ResizeObject(isHeroesSkill);
            }
        }

        /// <summary>
        /// スキルアイコンを押下した際の処理を設定
        /// </summary>
        /// <param name="skillInfo">UpdateItemで渡されたそのままのinfo</param>
        private void SetupOnClickSkillButton(Info skillInfo)
        {

            _bgButton.SetOnClick(() =>
            {
                if (_info.IsAssist)
                {
                    // MEMO:_infoの中身が元から書き換えられるなら他のダイアログ同様にskillInfoを渡す必要がでてきそう
                    DialogRaceFitAssistanceSkillDetail.Open(_info);
                } else if (_info.MasterData.IsLevelUp)
                {
                    DialogCharacterLimitBreakSkillSimple.OpenWithSkillId(skillInfo.MasterData.Id, _info.Level,
                        _info.IsDrawUniqSkillInfo, _info.LimitedType);
                }
                else
                {
                    var showHintLvUpParam = _info.IsDrawHintLvUpParamOnDetail
                        ? new DialogCharacterSimpleSkillDetail.ShowHintLvUpParam(_info.IsHintLvUp, _info.HintLv)
                        : null;
                    DialogCharacterSimpleSkillDetail.Open(skillInfo.MasterData, _info.IsDrawNeedSkillPoint,
                        _info.LimitedType, showHintLvUpParam, _info.SkillUpgradeCardId,
                        skillInfo.IsSingleModeSkillUpgradeDescription,
                        skillInfo.IsDisplayUpgradeSkill);
                }
            });
        }

        public void SetupNeedSkillPoint()
        {
            _needSkillPointText.text = _info.NeedSkillPoint.ToString();
        }

        public void SetActiveNeedSkillPoint(bool isActive)
        {
            _needSkillPointRoot.SetActive(isActive);
        }

        /// <summary>
        /// オブジェクトの大きさを設定する
        /// </summary>
        /// <param name="isThreeLines">3行分の高さにするかどうか</param>
        private void ResizeObject(bool isThreeLines)
        {
            var rectTransform = gameObject.transform as RectTransform;
            var itemSize = rectTransform.sizeDelta;
            itemSize.y = isThreeLines ? HEIGHT_THREE_LINES : HEIGHT_FOUR_LINES;
            rectTransform.sizeDelta = itemSize;
        }
        
        /// <summary>
        /// テキスト色変更
        /// </summary>
        /// <param name="fontColorType"></param>
        public void ChangeTextColorType(FontColorType fontColorType)
        {
            _nameText.FontColor = fontColorType;
        }

        /// <summary>
        /// 名前文字列の準備
        /// </summary>
        private void SetupNameText()
        {
            if (_nameText == null)
            {
                return;
            }

            _nameText.SetActiveWithCheck(true);
            
            if (_info.IsSkillLearningNameMode && _info.MasterData.GroupRate < 0)
            {
                // Badスキル打ち消し
                _nameText.text = TextId.SingleMode0394.Format(_info.Name);
            }
            else
            {
                _nameText.text = _info.Name;
            }

            if (!_info.MasterData.IsLevelUp)
            {
                //レベルアップ系でないなら抜ける
                _levelText.gameObject.SetActive(false);
                return;
            }
            
            _levelText.gameObject.SetActive(true);
            _levelText.text = string.Format(TextId.Race0612.Text(), _info.Level);
            _levelText.FontColor = _info.IsLevelUpColor ? FontColorType.Orange : FontColorType.Brown;
        }

        /// <summary>
        /// 習得スキルの表示設定
        /// </summary>
        private void SetGetSkill()
        {
            if (_skillIcon)
            {
                var openType = SkillIconLongTapInfoPop.OpenType.Explain;
                _skillIcon.Setup(_info.Id, false, openType);
            }

            _bgImage.SetMulColor(StaticVariableDefine.Parts.ButtonCommonStatic.DEFAULT_COLOR_WHITE);
            _bgImage.UpdateColor();
        }

        /// <summary>
        /// ヒントレベル設定
        /// </summary>
        private void SetHintLv()
        {
            //文字設定
            if (_info.HintLv > 0)
            {
                var isLvMax = _info.HintLv == SingleModeDefine.TIPS_LEVEL_MAX;
                _hintLabelObject.gameObject.SetActive(true);
                _hintLvText.text = TextUtil.Format(TextId.SingleMode0241.Text(), isLvMax ? TextId.Common0230.Text() : _info.HintLv.ToString() );
                //ラベル設定
                if (_info.IsHintLvUp)
                {
                    _hintLabelObject.sprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.HINT_RECOMMEND_LABEL);
                }
                else
                {
                    _hintLabelObject.sprite = UIManager.CommonAtlas.GetSprite(AtlasSpritePath.Common.HINT_PREPRESENT_LABEL);
                }
                
                // エフェクト
                if (isLvMax && _hintLvMaxEffect == null)
                {
                    var effPrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.SINGLE_MODE_SUPPORT_DETAIL_HINT_MAX_EFF);
                    _hintLvMaxEffect = Instantiate(effPrefab, _hintLabelObject.transform);
                }
                _hintLvMaxEffect.SetActiveWithCheck(isLvMax);
            }
            else
            {
                _hintLabelObject.gameObject.SetActive(false);
                _hintLvText.gameObject.SetActive(false);
                _hintLvText.text = String.Empty;
                
                _hintLvMaxEffect.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// トレーナーガイド；おすすめラベル設定
        /// </summary>
        private void SetRaceFitAssistanceLabel()
        {
            // まず非表示にする
            _partsRaceFitAssistanceLabel.SetActiveWithCheck(false);
            
            if (!_info.IsEnableRaceFitAssistanceLabel ||
                _info.IsConditionComplete)    // 条件達成アイコンと共存することはないはずだが、レイアウトが崩れるので念のため重複しないようにしている
            {
                return;
            }
            
            // (トレーナーガイドダイアログを開いているなら)開いているトレーナーガイドの情報を所得
            var showRaceFitAssistance = RaceFitAssistanceSettingPresenter.CurrentTargetRaceFitAssistanceEventInfo;
            // 設定中のトレーナーガイド情報を取得する
            var model = RaceFitAssistanceModelRepository.GetTargetModel();
            
            // 開いているトレーナーガイドの情報があるならその情報を元に表示
            // 開いていないなら、設定中のトレーナーガイド情報を元に表示
            // どちらも満たさないなら表示しない
            TargetRaceFitAssistanceEventInfo recommendTarget = null;
            recommendTarget = showRaceFitAssistance ?? model?.TargetRaceFitAssistanceEventInfo;
            if (recommendTarget == null)
            {
                return;
            }
                
            // おすすめ判定
            var recommendedSkillList = recommendTarget.RaceFitAssistInfo.RecommendedSkillList;
            var raceFitAssistanceSkillInfo = recommendedSkillList.FirstOrDefault(x => x.SkillId == _info.Id);
            if (raceFitAssistanceSkillInfo == null)
            {
                // 該当なし
                return;
            }
            
            var runningStyle = recommendTarget.RunningStyle;
            var recommendedType = RaceFitAssistanceUtil.GetRecommendedType(raceFitAssistanceSkillInfo, runningStyle);
            _partsRaceFitAssistanceLabel.Setup(recommendedType);
            _partsRaceFitAssistanceLabel.SetActiveWithCheck(true);
        }

        /// <summary>
        /// 条件達成アイコン
        /// </summary>
        private void SetConditionCompleteIcon()
        {
            _conditionCompleteIcon.SetActiveWithCheck(_info.IsConditionComplete);
        }
        
        /// <summary>
        /// 進化条件ボタン
        /// </summary>
        private void SetUpgradeConditionButton()
        {
            var isCardUpgrade = _info.MasterSkillUpgradeDescription != null;
            var isScenarioUpgrade = _info.MasterSkillUpgradeSpeciality != null;
            var isEnable = isScenarioUpgrade || isCardUpgrade;
            _upgradeConditionButton.SetActiveWithCheck(isEnable);
            if (_upgradeConditionButton != null)
            {
                if (isCardUpgrade)
                {
                    // 育成ウマ娘進化スキル条件
                    _upgradeConditionButton.SetOnClick(() => DialogSingleModeSkillUpgradeCondition.PushDialog(_info.MasterSkillUpgradeDescription, _info.IsSingleModeSkillUpgradeDescription));
                }
                else if(isScenarioUpgrade)
                {
                    // シナリオ進化スキル条件
                    _upgradeConditionButton.SetOnClick(() => DialogSingleModeSkillUpgradeCondition.PushDialog(_info.MasterSkillUpgradeSpeciality, _info.IsSingleModeSkillUpgradeDescription));
                }
            }
        }
        
        /// <summary>
        /// 進化バッジ（Sサイズリスト用）
        /// </summary>
        private void SetUpgradeBadge()
        {
            _upgradeBadgeRoot.SetActiveWithCheck(_info.IsSkillUpgradeBadge);
        }

        /// <summary>
        /// 習得済アイコン
        /// </summary>
        private void SetAcquire()
        {
            _acquireIcon.SetActiveWithCheck(_info.IsDrawAcquire);
        }

        /// <summary>
        /// 有効時白/無効時グレーアウト
        /// </summary>
        public void SetInteractableMulColor(bool interactable)
        {
            _bgImage.SetInteractableMulColor(interactable);
        }
        
        /// <summary>
        /// トレーナー技能試験のボーナススキル表示更新
        /// </summary>
        private void UpdateEventBonusBadge()
        {
            _eventBonusIcon.SetActiveWithCheck(_info.IsEventBonusSkill);
            
            // Note: _eventBonusIcon オブジェクトの実体は進化スキル・進化条件ダイアログprefab側に追加している
            if (_info.IsEventBonusSkill && _eventBonusIcon != null)
            {
                // "条件達成" 表記と被らないように位置調整
                var badgePosX = _info.IsConditionComplete ? EVENT_BONUS_BADGE_CONDITION_COMPLETE_POS_X : EVENT_BONUS_BADGE_DEFAULT_POS_X;
                var rect = _eventBonusIcon.transform as RectTransform;
                if (rect != null)
                {
                    rect.anchoredPosition = new Vector2(badgePosX, rect.anchoredPosition.y);
                }
            }
        }

        /// <summary>
        /// スキルレベルテキスト表示切替え
        /// </summary>
        /// <param name="isActive"></param>
        public void SetActiveSkillLevelText(bool isActive)
        {
            _levelText.SetActiveWithCheck(isActive);
        }

        /// <summary>
        /// シナリオ進化スキルがあるか
        /// </summary>
        private bool ExistScenarioUpgradeSkill()
        {
            var isSingleMode = false;
            var workSingleMode = WorkDataManager.Instance.SingleMode;

            // 育成中である場合、プレイ中のシナリオに準拠してマスターの取得をするように
            if (workSingleMode.IsPlaying && workSingleMode.Character != null)
            {
                isSingleMode = true;
            }
            var masterSpecialityList = PartsSingleModeSkillList.GetMasterSkillUpgradeSpecialityList(_info.MasterData.Id, isSingleMode);
            return masterSpecialityList.Count > 0;
        }

        /// <summary>
        /// 育成ウマ娘の指定がある場合、進化スキルがあるか
        /// </summary>
        private bool ExistCardUpgradeSkill()
        {
            if (_info.SkillUpgradeCardId == 0) return false;//未指定
            var cardData = MasterDataManager.Instance.masterCardData.Get(_info.SkillUpgradeCardId);
            return PartsSingleModeSkillList.HasUpgrade(_info.SkillUpgradeCardId, cardData.GetMaxTalentLevel(), _info.MasterData.Id);
        }
        
        /// <summary>
        /// 進化iボタン表示
        /// </summary>
        private void SetupUpgradeButton()
        {
            if (_upgradeRoot == null || _upgradeButton == null) return; //シリアライズされてない場合の保険でnullチェック強化
            
            if (_info.UseSkillListButton || // Sサイズボタンは表示しない
                _info.IsRequestSkillUpgradeButton == false)//リクエストのない場面では表示しない
            {
                _upgradeRoot.SetActiveWithCheck(false);
                return;
            }

            var isExistScenarioUpgradeSkill = ExistScenarioUpgradeSkill();
            var isExistCardUpgradeSkill = ExistCardUpgradeSkill();
            var isExist = isExistScenarioUpgradeSkill || isExistCardUpgradeSkill;
            
            // IsDisplayUpgradeSkillがfalseの場合は条件を満たすスキルでも進化ボタンを表示しない
            _upgradeRoot.SetActiveWithCheck(isExist && _info.IsDisplayUpgradeSkill);
            if (isExist)
            {
                _upgradeButton.SetOnClick(() =>
                {
                    DialogSingleModeSkillUpgrade.PushDialog(_info.SkillUpgradeCardId, _info.MasterData.Id, _info.ScenarioSkillUpgradeFilterScenarioId);
                });
                
                // 進化ボタンがあるときはスキルアイコンを上にズラす
                var iconRectTrans = _skillIcon.transform as RectTransform;
                iconRectTrans.anchoredPosition = new Vector2(iconRectTrans.anchoredPosition.x, SKILL_ICON_UPGRADE_POS_Y);
            }
        }
        

#if CYG_DEBUG
        private void SetDebug()
        {
            var view = SceneManager.Instance.GetCurrentViewId();
            if (view != SceneDefine.ViewId.SingleModeStart)
                return;

            if (!SingleModeStartViewController.UseDebug)
                return;

            if (_descText)
            {
                _descText.SetActiveWithCheck(true);
                _descText.text = _descText.text + " point = " + _info.DebugSkillPoint;
            }
            if (_nameText)
            {
                _nameText.SetActiveWithCheck(true);
                _nameText.text = _info.DebugIsInherit ? _nameText.text + " (継承)" : _nameText.text;
            }
        }
#endif
        #endregion Method
    }
}
