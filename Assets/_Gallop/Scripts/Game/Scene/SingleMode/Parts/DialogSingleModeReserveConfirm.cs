using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// レース予約
    /// </summary>
    [AddComponentMenu("")]
    public class DialogSingleModeReserveConfirm : DialogInnerBase
    {
        #region SerializeField

        [SerializeField]
        private PartsSingleModeReserveRaceListItem _item;

        [SerializeField]
        private TextCommon _message;

        #endregion

        #region DialogCommonBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        private static DialogSingleModeReserveConfirm LoadAndInstantiate()
        {
            const string PATH = ResourcePath.DIALOG_SINGLE_MODE_RESERVE_CONFIRM;
            var dialogObj = Instantiate(ResourceManager.LoadOnView<GameObject>(PATH));
            return dialogObj.GetComponent<DialogSingleModeReserveConfirm>();
        }


        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="program"></param>
        /// <param name="onClose"></param>
        public static void Open(int turn, MasterSingleModeProgram.SingleModeProgram program, Action onClose)
        {
            var dialogContent = LoadAndInstantiate();
            var dialogData = dialogContent.CreateDialogData();
            dialogData.Title = TextId.SingleMode0277.Text();
            dialogData.FormType = DialogCommonBase.FormType.SMALL_ONE_BUTTON;
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.DestroyCallBack = onClose;
            dialogData.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            dialogContent.Setup(turn, program, TextId.SingleMode0278.Text());
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// 複数予約ダイアログへの導線付きでダイアログを開く
        /// </summary>
        public static void OpenWithFlowToMultiReserve(
            SingleModeRaceReserve.Context context,
            int turn,
            MasterSingleModeProgram.SingleModeProgram program,
            Action onClose,
            Action onReserveUpdated)
        {
            var dialogContent = LoadAndInstantiate();
            var dialogData = dialogContent.CreateDialogData();

            // タイトル・ボタン文字設定
            dialogData.Title = TextId.SingleMode0277.Text();
            dialogData.LeftButtonText = TextId.Common0007.Text();
            dialogData.RightButtonText = TextId.SingleMode424001.Text();

            // コールバック設定
            dialogData.AutoClose = false;
            dialogData.LeftButtonCallBack = dialog =>
            {
                onClose?.Invoke();
                dialog.Close();
            };
            dialogData.RightButtonCallBack = dialog =>
            {
                // コールバック先でレース画像がアンロードされる処理があるため、先に呼び出しておく
                onClose?.Invoke();

                DialogSingleModeRaceReserveReservedRaceListControl.Open(context, turn,
                    pair =>
                    {
                        if (pair.IsClosed && pair.IsUpdated)
                        {
                            onReserveUpdated?.Invoke();
                        }
                    });

                dialog.Close();
            };

            // ローテーション機能を利用できない期間
            if (context.TryGetDisableReserveListMessage(context.GetDegreeByTurn(turn), out var disableMessage))
            {
                dialogData.RightButtonNoInteractableNotiffication = disableMessage;
            }

            dialogContent.Setup(turn, program, TextId.SingleMode0278.Text());
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// 予約変更用の開き
        /// </summary>
        /// <param name="program"></param>
        /// <param name="onReserve"></param>
        public static void OpenForChange(
            int turn,
            MasterSingleModeProgram.SingleModeProgram program,
            Action onReserve)
        {
            var dialogContent = LoadAndInstantiate();
            var dialogData = dialogContent.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.Title = TextId.SingleMode0279.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = dialog => dialog.Close();
            dialogData.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = dialog =>
            {
                dialog.Close();
                onReserve?.Invoke();
            };
            dialogContent.Setup(turn, program, TextId.SingleMode0280.Text());
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(
            int turn, MasterSingleModeProgram.SingleModeProgram program,
            string message)
        {
            _item.Setup(turn, program);
            _message.text = message;
        }

        #endregion
    }
}
