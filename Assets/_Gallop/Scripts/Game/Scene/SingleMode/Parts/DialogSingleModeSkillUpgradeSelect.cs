using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// シングルモード：スキル進化選択ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogSingleModeSkillUpgradeSelect : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;
        
        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.RightButtonText = TextId.Common0023.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = dialog => dialog.Close();
            data.AutoClose = false;
            data.IsFooterNotificationText = true;
            return data;
        }
        
        #endregion DialogInnerBase

        #region SerializeField、変数

        [SerializeField]
        private PartsSingleModeSkillUpgradeSelectItem _itemPrefab;
        [SerializeField]
        private Transform _itemParent;
        [SerializeField]
        private TextCommon _footerText;

        private List<PartsSingleModeSkillUpgradeSelectItem> _itemList = new List<PartsSingleModeSkillUpgradeSelectItem>();

        private List<SelectResult> _selectResultList;
        private Action<List<SelectResult>> _onDecide;

        #endregion
        
        #region class

        /// <summary>
        /// スキル進化選択結果
        /// </summary>
        public class SelectResult
        {
            public int OriginSkillId;//進化元
            public int ResultSkillId;//進化先
            public SkillDefine.SkillUpgradeType UpgradeType; // 進化タイプ（キャラ別の覚醒スキル、シナリオ別進化スキル）
        }
        
        #endregion

        #region ダイアログ開く

        /// <summary>
        /// 進化選択ダイアログ開く
        /// </summary>
        public static void PushDialogSelect(Action<List<SelectResult>> onDecide, Action onDestroy)
        {
            var component = LoadAndInstantiatePrefab<DialogSingleModeSkillUpgradeSelect>(ResourcePath.DIALOG_SINGLE_MODE_SKILL_UPGRADE_SELECT_PATH);
            var dialogData = component.CreateDialogData();
            dialogData.Title = component.GetTitleText();
            dialogData.FooterText = TextId.SingleMode194083.Text();
            dialogData.RightButtonCallBack = component.OnDecide;
            dialogData.DestroyCallBack = () => onDestroy?.Invoke();
            var dialog = DialogManager.PushDialog(dialogData);
            component.Setup(dialog, onDecide);
        }
        
        /// <summary>
        /// 進化確認ダイアログ開く
        /// </summary>
        public static void PushDialogConfirm(
            List<SelectResult> selectResultList, 
            Action<List<SelectResult>> onDecide)
        {
            var component = LoadAndInstantiatePrefab<DialogSingleModeSkillUpgradeSelect>(ResourcePath.DIALOG_SINGLE_MODE_SKILL_UPGRADE_SELECT_PATH);
            component.SetupConfirm(selectResultList, onDecide);
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.SingleMode194084.Text();
            dialogData.FooterText = TextId.SingleMode194086.Text();
            dialogData.RightButtonCallBack = component.OnDecideConfirm;
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(DialogCommon dialog, Action<List<SelectResult>> onDecide)
        {
            _onDecide = onDecide;

            var workChara = WorkDataManager.Instance.SingleMode.Character;
            var skillUpgradeSetList = workChara.GetSkillUpgradeSet();
            
            foreach (var skillUpgradeSet in skillUpgradeSetList)
            {
                // シナリオ進化スキルの要件は満たしているが、覚醒スキルの要件は満たしていないケースは弾く
                if (skillUpgradeSet.DescriptionList.IsNullOrEmpty())
                {
                    continue;
                }
                
                // 生成
                var itemObj = Instantiate(_itemPrefab, _itemParent);
                var item = itemObj.GetComponent<PartsSingleModeSkillUpgradeSelectItem>();
                item.Setup(_itemList.Count, skillUpgradeSet, () =>
                {
                    var existUnSelected =_itemList.Exists(a => a.IsSelected() == false);
                    if (existUnSelected == false)
                    {
                        // 全て選択したら決定ボタン有効
                        dialog.SetButtonEnable(DialogCommon.ButtonIndex.Right, true);
                        dialog.GetButtonObj(DialogCommon.ButtonIndex.Right).SetNotificationMessage(string.Empty);
                    }
                });
                
                _itemList.Add(item);
            }
            _itemPrefab.SetActiveWithCheck(false);
            _footerText.text = TextId.SingleMode194082.Text();

            // 選択するまで決定ボタン無効
            dialog.SetButtonEnable(DialogCommon.ButtonIndex.Right, false);
            dialog.GetButtonObj(DialogCommon.ButtonIndex.Right).SetNotificationMessage(TextId.SingleMode194087.Text());
        }

        /// <summary>
        /// 覚醒スキル選択ダイアログのタイトルテキスト取得
        /// </summary>
        /// <returns></returns>
        private string GetTitleText()
        {
            return TextId.SingleMode539008.Text();
        }

        /// <summary>
        /// セットアップ：スキル進化確認用
        /// </summary>
        /// <param name="selectResultList"></param>
        /// <param name="dialogUpgradeSelect"></param>
        /// <param name="dialogSpecialitySelect"></param>
        /// <param name="onDecide"></param>
        private void SetupConfirm(
            List<SelectResult> selectResultList,
            Action<List<SelectResult>> onDecide)
        {
            _selectResultList = selectResultList;
            _onDecide = onDecide;

            var upgradeSkillIndex = 0;
            var upgradeSpecialitySkillIndex = 0;
            foreach (var selectResult in selectResultList)
            {
                // 生成
                var itemObj = Instantiate(_itemPrefab, _itemParent);
                var item = itemObj.GetComponent<PartsSingleModeSkillUpgradeSelectItem>();

                var index = 0;
                if (selectResult.UpgradeType == SkillDefine.SkillUpgradeType.Chara)
                {
                    index = upgradeSkillIndex;
                    upgradeSkillIndex++;
                }
                else
                {
                    index = upgradeSpecialitySkillIndex;
                    upgradeSpecialitySkillIndex++;
                }
                
                item.SetupConfirm(index, selectResult.OriginSkillId, selectResult.ResultSkillId, selectResult.UpgradeType);
                
                _itemList.Add(item);
            }
            _itemPrefab.SetActiveWithCheck(false);
            _footerText.text = TextId.SingleMode194085.Text();
        }

        private void OnDecide(DialogCommon dialogCommon)
        {
            _onDecide?.Invoke(GetSelectedSkillIdList());
        }

        private void OnDecideConfirm(DialogCommon dialogConfirm)
        {
            // サーバー送信用に進化先スキルIDをリストアップする
            // ResultSkillId が「0」は「進化しない」を選択した対象なので取り除く
            var resultSkillIdList = _selectResultList.Where(a => a.ResultSkillId != SingleModeDefine.NOT_SKILL_UPGRADE_VALUE).ToList();
            _onDecide?.Invoke(resultSkillIdList);
        }
        
        /// <summary>
        /// 選択中の進化先スキルIDを取得
        /// </summary>
        private List<SelectResult> GetSelectedSkillIdList()
        {
            return _itemList.
                Select(a => a.GetSelectedResult()).
                OrderBy(x => x.ResultSkillId == SingleModeDefine.NOT_SKILL_UPGRADE_VALUE).ToList();
        }

        #endregion
    }
}
