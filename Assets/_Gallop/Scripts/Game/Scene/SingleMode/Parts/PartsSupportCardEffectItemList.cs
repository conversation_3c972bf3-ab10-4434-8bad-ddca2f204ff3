using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public sealed class PartsSupportCardEffectItemList : LoopScrollItemBase
    {
        #region SerializeField
        [SerializeField]
        private GameObject _bgImage = null;

        [SerializeField]
        private GameObject _bgNewImage = null;

        [SerializeField]
        private GameObject _newObj = null;

        [SerializeField]
        private TextCommon _nameText = null;

        [SerializeField]
        private TextCommon _effectValueText = null;

        [SerializeField]
        private ButtonCommon _button;
        #endregion SerializeField

        #region Method

        /// <summary>
        /// リストアイテムを更新
        /// </summary>
        public void UpdateItem(WorkSupportCardData.SupportCardData supportCardData, MasterSupportCardEffectTable.SupportCardEffectTable item, int currentLv, int nextLv)
        {
            _nameText.text = item.GetName();

            var currentValue = item.GetValueEffect(supportCardData, currentLv);
            var nextValue = item.GetValueEffect(supportCardData, nextLv);
            bool isNew = item.GetEffectiveLevel(supportCardData, supportCardData.Level) > currentLv;

            var effectType = item.GetEffectType();
            _effectValueText.text = effectType.GetValueText(nextValue);

            if (currentValue < nextValue)
            {
                _nameText.FontColor = FontColorType.Pink;
                _effectValueText.FontColor = FontColorType.Pink;
            }
            else
            {
                _nameText.FontColor = FontColorType.Brown;
                _effectValueText.FontColor = FontColorType.Brown;
            }

            _bgImage.SetActiveWithCheck(!isNew);
            _bgNewImage.SetActiveWithCheck(isNew);
            _newObj.SetActiveWithCheck(isNew);
            
            //ボタン設定
            _button.onClick.AddListener(() =>
            {
                DialogSupportCardTrainingEffectDetail.Open(effectType, nextValue);
            });
        }

        #endregion Method
    }
}
