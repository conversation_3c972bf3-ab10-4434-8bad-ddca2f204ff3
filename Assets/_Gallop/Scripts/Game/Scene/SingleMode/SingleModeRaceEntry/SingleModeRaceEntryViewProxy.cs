using System.Linq;

namespace Gallop
{
    public partial class SingleModeRaceEntryViewController
    {
        public sealed class SingleModeRaceEntryViewControllerProxy
        {
            private readonly SingleModeRaceEntryViewController _viewController;
            public SingleModeRaceEntryViewControllerProxy(SingleModeRaceEntryViewController viewController)
            {
                _viewController = viewController;
            }

            public void ClickRaceEntryButton(MasterSingleModeProgram.SingleModeProgram raceProgram)
            {
                var itemList = _viewController._itemList;
                var item = itemList.FirstOrDefault(item => item.Program == raceProgram);
                item.EntryButton.onClick?.Invoke();

                _viewController.OnClickRaceEntryButton();
            }
            
        }
    }
}