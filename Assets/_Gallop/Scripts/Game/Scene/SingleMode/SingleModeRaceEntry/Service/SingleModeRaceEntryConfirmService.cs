namespace Gallop
{

    public interface ISingleModeRaceEntryConfirmService
    {
        public void RaceEntryConfirm(System.Action<MasterSingleModeProgram.SingleModeProgram> onDecideRaceEntry);
    }

    /// <summary>
    /// レース出走確認Service
    /// </summary>
    public class SingleModeRaceEntryConfirmService : ISingleModeRaceEntryConfirmService
    {
        private MasterSingleModeProgram.SingleModeProgram _masterSingleModeProgram;

        public SingleModeRaceEntryConfirmService(MasterSingleModeProgram.SingleModeProgram masterSingleModeProgram)
        {
            _masterSingleModeProgram = masterSingleModeProgram;
        }

        private System.Action<MasterSingleModeProgram.SingleModeProgram> _onDecideRaceEntry;

        public virtual void RaceEntryConfirm(System.Action<MasterSingleModeProgram.SingleModeProgram> onDecideRaceEntry)
        {
            _onDecideRaceEntry = onDecideRaceEntry;

            // 出走確認ダイアログを表示
            DialogSingleModeRaceEntry.OpenEntry(_masterSingleModeProgram, OnDecideRaceEntry);
        }

        private void OnDecideRaceEntry(MasterSingleModeProgram.SingleModeProgram masterSingleModeProgram, DialogCommon dialog)
        {
            dialog.Close(() => _onDecideRaceEntry?.Invoke(masterSingleModeProgram));
        }

    }
}
