namespace Gallop
{
    public static class SingleModeScenarioLegendDefine
    {
        public enum LegendType
        {
            Legend9046,
            Legend9047,
            Legend9048,
        }

        /// <summary>
        /// バフ発動効果でのやる気変化情報
        /// </summary>
        public class MotivationChangedInfoByBuff
        {
            /// <summary>
            /// バフ発動効果でやる気が変化しなかった情報 バフ報酬で下がることはないので上がらないケースのパターン分け
            /// </summary>
            public enum NotChangedReason
            {
                None,
                NotUpByMotivationLimit,     // 絶好調で上がらなかった
                NotUpBySuperMaxMotivation,  // 超絶好調で上がらなかった
                NotUpByMigraine,            // 片頭痛で上がらなかった
            }
            
            public int ChangedValue { get; }
            public NotChangedReason Reason { get; }
            
            /// <summary> バフ発動によってやる気報酬が付与されたか </summary>
            public bool ExistMotivationGain => ChangedValue != 0 || Reason != NotChangedReason.None;
            
            /// <summary> 上昇した時のコンストラクタ </summary>
            public static MotivationChangedInfoByBuff CreateUp(int changedValue) => new (changedValue, NotChangedReason.None);
            /// <summary> 何らかの理由で上昇しなかった時のコンストラクタ </summary>
            public static MotivationChangedInfoByBuff CreateNotUp(NotChangedReason reason) => new (0, reason);
            /// <summary> バフ発動によるやる気変動がそもそも発生していないときのコンストラクタ </summary>
            public static MotivationChangedInfoByBuff CreateNone() => new (0, NotChangedReason.None);
            
            private MotivationChangedInfoByBuff(int changedValue, NotChangedReason reason)
            {
                ChangedValue = changedValue;
                Reason = reason;
            }
        }

        /// <summary>
        /// バフゲージの上限数
        /// </summary>
        public const int BUFF_GAUGE_LIMIT = 8;

        /// <summary>
        /// バフリストの上限数
        /// </summary>
        public const int BUFF_LIST_LIMIT = 10;

        public const int LEGEND_ID_9046 = 9046;
        public const int LEGEND_ID_9047 = 9047;
        public const int LEGEND_ID_9048 = 9048;
        public const int LEGEND_DRESS_ID_9046 = 904601;
        public const int LEGEND_DRESS_ID_9047 = 904701;
        public const int LEGEND_DRESS_ID_9048 = 904801;
        /// <summary> アーモンドアイ出走判定用NPC ID 距離別で出走するnpcデータが異なる </summary>
        public const int RIVAL_NPC_ID_1 = 91129901;
        public const int RIVAL_NPC_ID_2 = 91129902;

        /// <summary> 超絶好調突入までに必要なやる気アップカウント </summary>
        public const int MASTERLY_BONUS_9046_EFFECT_ACTIVATION_REQUIRED_COUNT = 3;

        /// <summary> 超絶好調効果を延長できる上限カウント </summary>
        public const int MASTERLY_BONUS_9046_EFFECT_EXTENSION_LIMIT_COUNT = 2;

        /// <summary> スポ根ゾーン突入までに必要なトレーニングカウント </summary>
        public const int MASTERLY_BONUS_9047_ZONE_ACTIVATION_REQUIRED_COUNT = 4;

        /// <summary> ハイセイコーマスタリー：親友ゲージのMAX値 </summary>
        public const int MASTERLY_BONUS_9048_FRIEND_GAUGE_MAX_VALUE = 20;

        /// <summary> ハイセイコーマスタリー：親友ゲージレベルのMAX値 </summary>
        public const int MASTERLY_BONUS_9048_FRIEND_GAUGE_MAX_LEVEL = 9;

        /// <summary> バフのランク2が獲得可能（抽選に含まれる）にる数 </summary>
        public const int BUFF_RANK_2_ACTIVATION_COUNT = 2;

        /// <summary> バフのランク3が獲得可能（抽選に含まれる）にる数 </summary>
        public const int BUFF_RANK_3_ACTIVATION_COUNT = 4;

        /// <summary> single_mode_10_buff_condition.condition_type やる気の値が指定値以上 </summary>
        public const int BUFF_CONDITION_TYPE_MOTIVATION = 14;
        
        /// <summary> single_mode_10_buff_effect.effect_type 29 において、やる気報酬に対応したeffect_value_1 </summary>
        public const int BUFF_EFFECT_TYPE_GAIN_PARAMETER_IMMEDIATELY_REWARD_TYPE_MOTIVATION = 20;

        /// <summary> バフのエフェクトタイプ </summary>
        /// single_mode_10_buff_effect.effect_type
        /// クライアント参照するものだけ表記
        public enum BuffEffectType
        {
            TrainingUp = 1, // トレーニング効果アップ
            HintUp = 2, // ヒント発生率アップ
            BestTrainingUp = 3, // 得意率アップ
            TrainingFailureRateDown = 5, // 失敗率ダウン
            TrainingHpConsumeDown = 6, // 体力消費ダウン
            AddSupportForTraining = 10,// サポカが追加でトレーニングに現れる
            TrainingUpByTagTrainingCount = 14,// 友情の数がおおいほど、トレーニング効果アップ
            EvaluateGaugeUp = 15, // 絆ゲージ上昇量アップ
            SkillPointBonus = 16, // スキルPtボーナス
            TagTrainingBonus = 17, // 友情ボーナス
            HintUpByTagTrainingCount = 21, // 友情の数がおおいほど、ヒント発生率アップ
            TrainingNotArrangementDown = 24, // トレーニング未配置率ダウン
            HintEventAddHintCount = 25, // ヒントイベントでの追加ヒント獲得
            HintEventFixedOccurrenceCount = 26, // ヒントイベントの確定発生数
            SupportBestTrainingFixedArrangement = 27, // サポカが得意トレーニング確定配置
            MotivationEffectUp = 28, // やる気効果アップ
            GainParameterImmediately = 29, // 即時報酬
        }

        /// <summary>
        /// 伝説編で利用するカット演出タイプ
        /// </summary>
        public enum ScenarioLegendCuttType
        {
            None = 0,
            Poster, //ポスター
            DreamFestGoal,  //特殊レース後演出
            Advertisement,  //広告
            FanContact_1 = 10,　//ファン交流の1~3
            FanContact_2,
            FanContact_3,
            PhotoBookCover = 20, //写真集表紙
            PhotoBookPage1, //写真集1~6ページ目
            PhotoBookPage2,
            PhotoBookPage3,
            PhotoBookPage4,
            PhotoBookPage5,
            PhotoBookPage6,
            PhotoBookObi,
        }

        /// <summary>
        /// 評判カットインタイプ
        /// </summary>
        public enum PopularityCuttType
        {
            FanExchange = 1,// ファン交流
            Poster,         // ポスター
            PhotoPage,      // 写真集
            Advertisement,  // 広告
        }

        public const string GOAL_CAPTURE_FILE_NAME = "race_goal_capture_legend.png";

        /// <summary>
        /// お出かけ時の報酬タイプ
        /// single_mode_10_group_outing_eff.effect_typeと合わせる
        /// </summary>
        public enum OutingEffectType
        {
            All = 1, // 全てのゲージ
            StoryChoice = 2, // ストーリー内の選択肢で取得対象分岐
            Single = 3, // 単体のゲージ（選んだキャラのゲージの項目側の表記）
            ChoiceChara = 4, // 選んだキャラいずれかのゲージがもらえる（文言表記）
        }

        /// <summary> ファン交流演出に使うサウンドCSVの「staging_type」 </summary>
        public const int FAN_EXCHANGE_SOUND_CSV_STAGING_TYPE = (int)PopularityCuttType.FanExchange;

        /// <summary> 特殊レース前演出で利用するSEのCSVの「staging_type」 </summary>
        public const int RACE_GATE_SET_CUT_IN_SOUND_CSV_STAGING_TYPE = 5;

        /// <summary> 伝説編ジュニア期のライブで利用する「歓声少ない」のオケバリエーションID </summary>
        public const int OKE_VARIATION_ID_REDUCED_CALL = 3;

        /// <summary> ポスター演出で利用するキャッチコピーの最大type数 </summary>
        public const int POSTER_CATCH_COPY_TYPE_MAX = 18;

        /// <summary>
        /// CMの路線
        /// </summary>
        public enum CmRoute
        {
            None = 0,
            Classic,            // クラシック（牡馬三冠）
            Tiara,              // ティアラ（牝馬三冠）
            Dirt,               // ダート
            ShortMile,          // 短距離マイル
            SeniorMiddleLong,   // シニア中長距離
        }
        
        /// <summary> CM演出やライブなどで使われる、路線IDの最大数 </summary>
        public const int ROUTE_ID_MAX = 5;

        /// <summary> ポスター演出に登場するキャラのデフォルト衣装Id </summary>
        public const int POSTER_CHARACTER_DEFAULT_DRESS_ID = (int)ModelLoader.DressID.UniformWinter;

    }
}
