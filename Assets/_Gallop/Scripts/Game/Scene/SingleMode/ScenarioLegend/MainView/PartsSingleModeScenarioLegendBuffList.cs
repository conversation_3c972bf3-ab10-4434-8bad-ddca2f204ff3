using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Gallop.SingleMode.ScenarioLegend;

namespace Gallop
{
    /// <summary>
    /// 伝説編：バフリストUI
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioLegendBuffList : MonoBehaviour
    {
        [SerializeField] private PartsSingleModeScenarioLegendUserBuffListItem[] _itemIconArray;
        [SerializeField] private ButtonCommon _buffListInfoButton;
        
        private IDisposable _loopEffectSyncSubscription;
        private (PartsSingleModeScenarioLegendUserBuffListItem Target, EventSubject<string> Subject)? _syncInfo;
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            PartsSingleModeScenarioLegendUserBuffListItem.RegisterDownload(register);
            DialogSingleModeScenarioLegendUserBuffList.RegisterDownload(register);
        }

        public void Setup(IBuffListModel buffListModel, IMasterlyBonusModel masterlyBonusModel)
        {
            // 直前の同期情報が残っていれば破棄
            _loopEffectSyncSubscription?.Dispose();
            _syncInfo?.Subject.Dispose();
            _syncInfo = null;
            
            for (var i = 0; i < _itemIconArray.Length; i++)
            {
                var itemModel = buffListModel.GetItemByIndex(i);
                var itemIcon = _itemIconArray[i];
                
                if (itemModel != null)
                {
                    itemIcon.Setup(itemModel);
                }
                else
                {
                    itemIcon.SetupEmpty();
                }
            }
            
            SetupBuffListInfoButton(buffListModel, masterlyBonusModel);
        }

        public void SetupWithTrainingParamChange(IBuffListModel prevBuffListModel, IBuffListModel currentBuffListModel)
        {
            for (var i = 0; i < _itemIconArray.Length; i++)
            {
                var prevItemModel = prevBuffListModel.GetItemByIndex(i);
                var currentItemModel = currentBuffListModel.GetItemByIndex(i);
                var preferredItemModel = NeedUseCurrentItemModel(prevItemModel, currentItemModel) ? currentItemModel : prevItemModel;
                
                var itemIcon = _itemIconArray[i];
                
                if (preferredItemModel != null)
                {
                    itemIcon.Setup(preferredItemModel);
                }
                else
                {
                    itemIcon.SetupEmpty();
                }
            }
            
            // イベント導線ではinteractableを切っているためInfoButtonのセットアップはおこなわない

            return;
            
            bool NeedUseCurrentItemModel(IBuffItemModel prevBuffItemModel, IBuffItemModel currentBuffItemModel)
            {
                if(prevBuffItemModel == null || currentBuffItemModel == null) return false;
                
                // やる気を条件としているバフがイベント前後で非発動に切り替わった場合、非発動状態でイリさせたいため prevではなくcurrentでセットアップする必要がある
                var deactivated = prevBuffItemModel.IsEffectActive && !currentBuffItemModel.IsEffectActive;
                if (deactivated)
                {
                    return prevBuffItemModel.IsMotivationCondition;
                }

                return false;
            }
        }
        
        private void SetupBuffListInfoButton(IBuffListModel buffListModel, IMasterlyBonusModel masterlyBonusModel)
        {
            _buffListInfoButton.SetOnClick(() =>
            {
                DialogSingleModeScenarioLegendUserBuffList.PushDialog(new DialogSingleModeScenarioLegendUserBuffListModel(buffListModel, masterlyBonusModel));
            });
        }

        /// <summary>
        /// バフリスト内のアイコンのボタン有効/無効切り替え
        /// </summary>
        /// <param name="enable"></param>
        public void SetInteractable(bool enable)
        { 
            foreach (var itemIcon in _itemIconArray)
            {
                itemIcon.SetInteractable(enable);
            }
            _buffListInfoButton.SetInteractable(enable);
        }
        
        public void SetActiveBuffActivateLoopEffect(bool isActive)
        {
            // エフェクトが生成済みなら表示切替
            foreach (var itemIcon in _itemIconArray)
            {
                itemIcon.SetActiveLoopEffect(isActive);
            }
        }

        /// <summary>
        /// バフの発動アニメーションを再生
        /// </summary>
        public void PlayGainAnimation(IBuffListModel prevModel, IBuffListModel currentModel, IReadOnlyList<int> activatedBuffIdList, Action onComplete)
        {
            var animationCompleteCount = 0;
            
            _loopEffectSyncSubscription?.Dispose();
            _syncInfo?.Subject.Dispose();
            _syncInfo = null;

            // 表示され続けるループエフェクトがある場合、後から表示するループエフェクトは同期させる。基準となるエフェクトを取得しておく
            for (var i = 0; i < _itemIconArray.Length; i++)
            {
                var buffId = currentModel.GetItemByIndex(i)?.BuffId ?? 0;
                var isActivated = activatedBuffIdList.Contains(buffId);
                var isLoopEffectActive = _itemIconArray[i].IsActiveLoopEffect();
                // 既にループエフェクトが生成済みで、発動アニメーションの対象外ならループエフェクトが表示され続ける
                if (isActivated == false && isLoopEffectActive)
                {
                    _syncInfo ??= (_itemIconArray[i], new EventSubject<string>());
                    
                    // 同期タイミングに合わせてエフェクトを再表示する
                    var icon = _itemIconArray[i];
                    _syncInfo?.Subject.Subscribe(_ =>
                    {
                        icon.SetActiveLoopEffect(false);
                        icon.SetActiveLoopEffect(true);
                    });
                }
            }

            if (activatedBuffIdList.Any())
            {
                AudioManager.Instance.PlaySe(AudioId.SFX_ADDON10_LEGEND_BUFF_ACTIVATE);
            }
            
            for (var i = 0; i < _itemIconArray.Length; i++)
            {
                var prevItemModel = prevModel.GetItemByIndex(i);
                var currentItemModel = currentModel.GetItemByIndex(i);
                if (prevItemModel == null || currentItemModel == null)
                {
                    OnCompleteEachItemAnimation();
                    continue;
                }
                
                // 効果が発動した (連続発動した場合、Model間のデータ差分が出ないため発動有無はこちらのパラメータを見る)
                var isActivated = activatedBuffIdList.Contains(currentItemModel.BuffId);
                
                var itemIcon = _itemIconArray[i];
                // 発動演出
                if (isActivated)
                {
                    itemIcon.PlayActivatedAnimation(currentItemModel, OnCompleteEachItemAnimation, _syncInfo?.Subject);
                }
                else
                {
                    OnCompleteEachItemAnimation();
                }
            }

            // 全ての演出が終了したらコールバックを呼ぶ
            void OnCompleteEachItemAnimation()
            {
                animationCompleteCount++;
                if (animationCompleteCount >= _itemIconArray.Length) OnCompleteAll();
            }

            void OnCompleteAll()
            {
                // 既に表示されているループエフェクトがある場合、後から表示するループエフェクトは同期させる
                if (_syncInfo != null)
                {
                    _loopEffectSyncSubscription = _syncInfo?.Target.SubscribeLoopEvent(() =>
                    {
                        // 同期対象の各LoopEffectをリスタート、処理は1度で良いので通知したら購読サブジェクトは破棄
                        _syncInfo?.Subject.OnNext(string.Empty);
                        _syncInfo?.Subject.Dispose();
                        _syncInfo = null;
                    });
                }
                
                onComplete?.Invoke();
            }
        }
    }
}
