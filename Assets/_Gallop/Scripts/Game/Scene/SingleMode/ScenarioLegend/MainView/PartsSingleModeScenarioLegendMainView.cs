using System.Collections.Generic;
using System.Linq;
using AnimateToUnity;
using DG.Tweening;
using UnityEngine;
using Gallop.SingleMode.ScenarioLegend;

namespace Gallop
{
    using static SingleModeScenarioLegendDefine;
    /// <summary>
    /// 育成追加シナリオ：伝説編：育成TOPの追加UIパーツ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class PartsSingleModeScenarioLegendMainView : MonoBehaviour, ISingleModeScenarioMainViewAdditivePartsBase
    {
        #region const
        
        private const string REMIND_TURN_A2U_PASS = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_ROOT + "pf_fl_singlemode_legend_remind_turn00";
        private const string PREFAB_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_UI_ROOT + "PartsSingleModeScenarioLegendMainView";
        
        private const int MASTERLY_BONUS_SORT_ORDER = 1;
        private const int MASTERLY_BONUS_TRAINING_SORT_ORDER = 100;
        
        #endregion
        
        #region SerializeField
        
        [SerializeField] private PartsSingleModeMainViewInOutAnimationRoot _animationRoot;

        [Header("バフゲージ")]
        [SerializeField] private PartsSingleModeScenarioLegendBuffGauge _buffGauge;
        
        [Header("バフ獲得までの残りターン")]
        [SerializeField] private PartsSingleModeScenarioLegendBuffLotteryRemainTurnBalloon _buffLotteryRemainTurnBalloon;
        
        [Header("バフリスト")]
        [SerializeField] private PartsSingleModeScenarioLegendBuffList _buffList;

        [Header("マスタリーボーナス")]
        [SerializeField] private PartsSingleModeScenarioLegendMasterlyBonus _masterlyBonus;
        public PartsSingleModeScenarioLegendMasterlyBonus MasterlyBonus => _masterlyBonus;
        
        #endregion
        
        /// <summary>
        /// 生成
        /// </summary>
        public static PartsSingleModeScenarioLegendMainView Create(Transform parent)
            => UIUtil.LoadAndInstantiate<PartsSingleModeScenarioLegendMainView>(parent, PREFAB_PATH);
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // バフゲージ
            PartsSingleModeScenarioLegendBuffGauge.RegisterDownload(register);
            
            // バフ一覧
            PartsSingleModeScenarioLegendBuffList.RegisterDownload(register);
            
            // シナリオレース一覧
            DialogSingleModeScenarioLegendDataList.RegisterDownload(register);
            
            // マスタリーボーナス効果
            DialogSingleModeScenarioLegendMasterlyBonusInfo.RegisterDownload(register);
            
            // マスタリーボーナス
            PartsSingleModeScenarioLegendMasterlyBonus.RegisterDownload(register);
            
            // 評判カットイン遷移用ワイプ
            PartsSingleModeScenarioLegendPopularityNowLoadingWipe.RegisterDownloadCommon(register);
            
            // CM遷移用ワイプ
            PartsSingleModeScenarioLegendCommercialNowLoadingWipe.RegisterDownloadCommon(register);
        }

        #region ISingleModeScenarioMainViewAdditivePartsBase

        /// <summary>
        /// 表示更新
        /// </summary>
        public void Setup()
        {
            var buffGaugeModel = GaugeModelRepository.Get();
            var buffListModel = BuffListModelRepository.Get();
            MasterlyBonusModelRepository.TryGet(out var masterlyBonusModel);
            
            SetupBuffGauge(buffGaugeModel);
            
            SetupBuffLotteryRemainTurnBalloon();

            SetupBuffList(buffListModel, masterlyBonusModel);

            SetupMasterlyBonus(buffListModel, masterlyBonusModel);
            var isTrainingView = SingleModeMainServiceLocator.Instance.Resolve<ISingleModeMainViewTraining>()?.TrainingFooter?.gameObject.activeSelf ?? false;
            MasterlyBonus.SetSortOrder(isTrainingView ? MASTERLY_BONUS_TRAINING_SORT_ORDER : MASTERLY_BONUS_SORT_ORDER);
            
            // ボタン操作有効 
            SetInteractableTopUI(true);
        }
        
        /// <summary>
        /// ストーリーでPartsMainViewを見せる場合のSetup. 通信前のデータを表示する
        /// </summary>
        public void SetupWithTrainingParamChange(bool useBackupData = true)
        {
            // Backupに積まれているエンティティで表示をセットアップする
            var buffGaugeModel = useBackupData ? GaugeModelRepository.GetBackupData() : GaugeModelRepository.Get();
            var buffListModel = useBackupData ? BuffListModelRepository.GetBackupData() : BuffListModelRepository.Get();

            IMasterlyBonusModel masterlyBonusModel;
            if (useBackupData)
                MasterlyBonusModelRepository.TryGetBackup(out masterlyBonusModel);
            else
                MasterlyBonusModelRepository.TryGet(out masterlyBonusModel);
            
            SetupBuffGauge(buffGaugeModel);
            
            // バルーンは非表示
            _buffLotteryRemainTurnBalloon.SetActiveWithCheck(false);

            if (useBackupData)
            {
                var currentBuffListModel = BuffListModelRepository.Get();
                var prevBuffListModel = buffListModel;
                _buffList.SetupWithTrainingParamChange(prevBuffListModel, currentBuffListModel);
            }
            else
            {
                SetupBuffList(buffListModel, masterlyBonusModel);
            }

            SetupMasterlyBonus(buffListModel, masterlyBonusModel);

            // ボタン操作無効
            SetInteractableTopUI(false);
        }

        public void SetupBuffGauge(IBuffGaugeModel buffGaugeModel)
        {
            _buffGauge.SetActiveWithCheck(true);
            _buffGauge.Setup(buffGaugeModel);
            _buffGauge.HideTrainingGainPredict();
        }

        public void SetupBuffList(IBuffListModel buffListModel, IMasterlyBonusModel masterlyBonusModel)
        {
            _buffList.Setup(buffListModel, masterlyBonusModel);
        }
        
        private void SetupBuffLotteryRemainTurnBalloon()
        {
            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var finalTurn = WorkDataManager.Instance.SingleMode.GetFinalTurn();
            _buffLotteryRemainTurnBalloon.Setup(currentTurn, finalTurn);
        }

        public void SetupMasterlyBonus(IBuffListModel buffListModel, IMasterlyBonusModel masterlyBonusModel)
        {
            _masterlyBonus.Setup(buffListModel, masterlyBonusModel);
        }

        public void SetActive(bool enable)
        {
            this.gameObject.SetActive(enable);
            _animationRoot.gameObject.SetActive(enable);

            if (enable)
            {
                _animationRoot.SetActiveImmediate(true);
            }
        }

        /// <summary>
        /// IN
        /// </summary>
        public void PlayIn() => PlayIn(null);
        
        public void PlayIn(System.Action onComplete)
        {
            if (this.gameObject.activeSelf)
            {
                _buffGauge.SetActiveGaugeMaxLoopEffect(false);
                _buffList.SetActiveBuffActivateLoopEffect(false);
                _animationRoot.CreatePlayInSequence(TweenAnimation.PresetType.PartsInFadeFromLeft).OnComplete(() =>
                {
                    _buffGauge.SetActiveGaugeMaxLoopEffect(true);
                    _buffList.SetActiveBuffActivateLoopEffect(true);
                    onComplete?.Invoke();
                });
            }
            else
            {
                onComplete?.Invoke();
            }
        }

        /// <summary>
        /// OUT
        /// </summary>
        public void PlayOut() => PlayOut(null);
        
        public void PlayOut(System.Action onComplete)
        {
            if (this.gameObject.activeSelf)
            {
                // バフゲージのループエフェクトをはける際に消す
                // #152937 アニデザ確認の結果、フェードではなく即時OFFで良い方針になった。そのためループエフェクトにはParticleForUguiをつけていない(フェードもしない). 
                _buffGauge.SetActiveGaugeMaxLoopEffect(false);
                _buffList.SetActiveBuffActivateLoopEffect(false);
                _animationRoot.CreatePlayOutSequence(TweenAnimation.PresetType.PartsOutFadeFromLeft).OnComplete(() => onComplete?.Invoke());
            }
            else
            {
                onComplete?.Invoke();
            }
        }

        public void PlayGoTrainingSelect()
        {
            MasterlyBonus.ShowTrainingGain();
            // #153866 16:9より横長、かつセーフエリアがある一部端末で導きUIと別枠加算表示が干渉する。位置かぶりは許容となったが、SortOrderが入れ違いにならないようシナリオUIを手前に持ってくる
            // 影響範囲を最小限にするためトレーニング画面滞在中かつマスタリーボーナスUIのみ手前に出す形で実装
            MasterlyBonus.SetSortOrder(MASTERLY_BONUS_TRAINING_SORT_ORDER);
        }

        public void PlayReturnTrainingSelect()
        {
            _buffGauge.HideTrainingGainPredict();
            MasterlyBonus.HideTrainingGain();
            MasterlyBonus.SetSortOrder(MASTERLY_BONUS_SORT_ORDER);
        }

        public void PlayExecTrainingCut()
        {
            // ボタン系を押せないようにする
            SetInteractableTopUI(false);
            
            // ゲージ上昇予測OFF
            _buffGauge.HideTrainingGainPredict();
            MasterlyBonus.HideTrainingGain();
            MasterlyBonus.SetSortOrder(MASTERLY_BONUS_SORT_ORDER);
        }
        
        public void PlayInBackTrainingFromAdditiveView(){}

        public void OnTrainingItemSelected(TrainingDefine.TrainingCommandId commandId)
        {
            var trainingCommandModel = TrainingModelRepository.Get(commandId);
            _buffGauge.SetupTrainingGainPredict(trainingCommandModel);
        }

        #endregion
        
        /// <summary>
        /// 開催予定表示
        /// </summary>
        public void ShowScenarioNotice(MasterSingleMode10Sprace.SingleMode10Sprace nextSchedule, System.Action onComplete)
        {
            PartsSingleModeMainViewScenarioNotice.Play(
                transform,
                REMIND_TURN_A2U_PASS,
                nextSchedule.TurnNum,
                onComplete);
        }

        /// <summary>
        /// TOPに表示している専用UIの有効/無効切り替え制御
        /// </summary>
        /// <param name="enable"></param>
        private void SetInteractableTopUI(bool enable)
        {
            _buffList.SetInteractable(enable);
            
            _masterlyBonus.SetInteractable(enable);
        }
        
        public void PlayBuffGaugeUp(IBuffGaugeModel prevModel, IBuffGaugeModel currentModel, System.Action onComplete)
        {
            _buffGauge.PlayGaugeUp(prevModel, currentModel, onComplete);
        }

        public void PlayGaugeFlushAndReset()
        {
            _buffGauge.PlayGaugeFlushAndReset();
        }
        public AnMotion GetGaugeRootMotion(LegendType legendType) => _buffGauge.GetGaugeRootMotion(legendType);
        
        public void PlayBuffListGainAnimation(IBuffListModel prevModel, IBuffListModel currentModel, IReadOnlyList<int> activatedBuffIdList, System.Action onComplete)
        {
            _buffList.PlayGainAnimation(prevModel, currentModel, activatedBuffIdList, onComplete);
        }
        
        public void PlayProgressMasterlyBonus(MasterlyBonus9046Model prevModel, MasterlyBonus9046Model currentModel, System.Action onComplete)
        {
            _masterlyBonus.PlayProgress(prevModel, currentModel, onComplete);
        }
        
        public void PlayActivationMasterlyBonus9048(MasterlyBonus9048Model masterly9048Model, System.Action onComplete)
        {
            _masterlyBonus.PlayActivate(masterly9048Model, onComplete);
        }
    }
}