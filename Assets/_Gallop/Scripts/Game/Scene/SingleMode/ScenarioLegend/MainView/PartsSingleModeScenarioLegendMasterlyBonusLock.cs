using UnityEngine;
using Gallop.SingleMode.ScenarioLegend;

namespace Gallop
{
    /// <summary>
    /// 伝説編：マスタリーボーナス未取得時のUI
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioLegendMasterlyBonusLock : MonoBehaviour
    {
        private const string PREFAB_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_MASTERLY_BONUS_UI_ROOT + "PartsSingleModeScenarioLegendMasterlyBonusLock";
        
        [SerializeField] private ImageCommon _baseImage;
        
        public static PartsSingleModeScenarioLegendMasterlyBonusLock Create(Transform parent)
            => UIUtil.LoadAndInstantiate<PartsSingleModeScenarioLegendMasterlyBonusLock>(parent, PREFAB_PATH);

        public void Setup()
        {
            _baseImage.IsIgnoreParentColor = true;
        }
    }
}