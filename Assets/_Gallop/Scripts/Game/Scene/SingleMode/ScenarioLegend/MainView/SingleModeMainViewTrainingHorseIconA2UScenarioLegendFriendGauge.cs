using System.Collections.Generic;
using UnityEngine;
using AnimateToUnity;
using AnimateToUnity.Utility;
using Gallop.SingleMode.ScenarioLegend;
using DG.Tweening;

namespace Gallop
{
    public class SingleModeMainViewTrainingHorseIconA2UScenarioLegendFriendGauge
    {
        private const string FLASH_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_bestfriend_gauge00";
        
        private const string LABEL_IDLE_FORMAT = "idle{0:D2}";
        private const string LABEL_IDLE_GAUGE_ACTIVE_FORMAT = "idle_gauge_active{0:D2}";
        private const string LABEL_IN_ANNOUNCE_FORMAT = "in_announce{0:D2}";
        private const string LABEL_IN_FULL_ANNOUNCE_FORMAT = "in_full_announce{0:D2}";
        private const string LABEL_IN_UP_FORMAT = "in_up{0:D2}";
        private const string LABEL_IN_MAX_FORMAT = "in_max{0:D2}";
        private const string LABEL_MAX_END_FORMAT = "max_end{0:D2}";
        private const string LABEL_IN_LVUP_FORMAT = "in_lvup{0:D2}";
        private const string LABEL_DIALOG_GAUGE_ACTIVE_FORMAT = "dialog_gauge_active{0:D2}";

        private readonly GameObject _rootObject;
        private readonly FlashActionPlayer _flashActionPlayer;
        private readonly List<AnMotion> _levelTextMotions;
        private readonly AnMotion _gaugeEffectMotion; 
        private readonly AnMotion _predictMotion;
        private readonly AnMotion _predictBlinkMotion; 
        private readonly AnProgressBar _mainProgressBar;
        private readonly AnProgressBar _predictProgressBar;
        private readonly AnProgressBar _animProgressBar;
        private readonly int _gaugeMaxValue;

        private FlashPlayer FlashPlayer => _flashActionPlayer.FlashPlayer;
        
        public bool IsPlayingGaugeUpAnimation { get; private set; }
        
        public SingleModeMainViewTrainingHorseIconA2UScenarioLegendFriendGauge(Transform parent, int gaugeMaxValue)
        {
            const int GAUGE_POS_X = -120;
            
            _gaugeMaxValue = gaugeMaxValue;
            
            _rootObject = new GameObject("FriendGaugeRoot", typeof(RectTransform));
            _rootObject.transform.SetParent(parent);
            _rootObject.transform.localPosition = new Vector3(GAUGE_POS_X, 0, 0);
            _rootObject.transform.localScale = Vector3.one;

            _flashActionPlayer = FlashActionPlayer.Load(FLASH_PATH, _rootObject.transform);
            _flashActionPlayer.LoadFlashPlayer();
            // 通常・加算効果など複数のテキストオブジェクトがあるためそれぞれ取得
            _levelTextMotions = new List<AnMotion>()
            {
                FlashPlayer.GetMotion("OBJ_mc_lv_flash_00/MOT_mc_lv_flash_01/OBJ_txt_lv_value00/MOT_txt_lv_value00"),
                FlashPlayer.GetMotion("OBJ_mc_lv_flash_00/MOT_mc_lv_flash_01/OBJ_txt_lv_value00_4/MOT_txt_lv_value00"),
                FlashPlayer.GetMotion("OBJ_mc_lv_flash_00/MOT_mc_lv_flash_01/OBJ_txt_lv_value00_10/MOT_txt_lv_value00"),
                FlashPlayer.GetMotion("MOT_root/OBJ_txt_lv_value00/MOT_txt_lv_value00"),
            };
            _mainProgressBar = FlashPlayer.GetProgressBar("OBJ_mc_bestfriend_gauge_base00");
            _predictProgressBar = FlashPlayer.GetProgressBar("OBJ_mc_bestfriend_gauge_announce00");
            _animProgressBar = FlashPlayer.GetProgressBar("OBJ_mc_bestfriend_gauge_base01");
            _predictMotion = FlashPlayer.GetMotion("MOT_mc_bestfriend_gauge_announce00");
            _predictBlinkMotion = FlashPlayer.GetMotion("MOT_mc_bestfriend_gauge_announce_blink");
            _gaugeEffectMotion = FlashPlayer.GetMotion("MOT_img_bestfriend_gauge_blink00");
            
            // 親友ゲージは上昇量にかかわらず固定時間でアニメーションさせる
            _animProgressBar.SetBlendTime(GameDefine.BASE_FPS_TIME * 2);
        }
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(FLASH_PATH);
            
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_TRAINING_LOGPOSI_07);
        }
        
        public static string GetRootMotionLabel(string formatString, MasterlyBonus9048Model.FriendDataModel friendDataModel)
            => TextUtil.Format(formatString, friendDataModel.IsMaxLevel ? "01" : "00");
        
        public void SetVisible(bool isVisible)
        {
            _rootObject.SetActiveWithCheck(isVisible);
        }

        public void Setup(MasterlyBonus9048Model.FriendDataModel friendDataModel, int predictGaugeUpValue)
        {
            // 通常・予兆表示・ゲージMAXで再生ラベル切り分け
            if (friendDataModel.IsMaxGauge)
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IDLE_GAUGE_ACTIVE_FORMAT, friendDataModel));
            }
            else
            {
                if (predictGaugeUpValue == 0)
                {
                    FlashPlayer.Play(GetRootMotionLabel(LABEL_IDLE_FORMAT, friendDataModel));
                }
                else
                {
                    // 予測込みでゲージがMAXになる場合は専用のラベルを呼んで差別化する
                    var willBeMax = friendDataModel.GaugeValue + predictGaugeUpValue >= _gaugeMaxValue;
                    if (willBeMax)
                    {
                        FlashPlayer.Play(GetRootMotionLabel(LABEL_IN_FULL_ANNOUNCE_FORMAT, friendDataModel));
                    }
                    else
                    {
                        FlashPlayer.Play(GetRootMotionLabel(LABEL_IN_ANNOUNCE_FORMAT, friendDataModel));
                    }
                }
            }
            
            // 予兆ゲージの同期のため、ラベルを再生しなおす
            _predictMotion.SetMotionPlay(GameDefine.A2U_IN_LABEL);
            _predictBlinkMotion.SetMotionPlay("Loop");
            
            SetupGauge(friendDataModel, predictGaugeUpValue);
            SetupLevel(friendDataModel);
        }

        /// <summary>
        /// ダイアログ上に親友ゲージを載せる場合のセットアップ
        /// </summary>
        public void SetupFromDialog(MasterlyBonus9048Model.FriendDataModel friendDataModel, int sortOffset, CanvasRenderer canvasRenderer)
        {
            if (friendDataModel.IsMaxGauge)
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_DIALOG_GAUGE_ACTIVE_FORMAT, friendDataModel));
            }
            else
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IDLE_FORMAT, friendDataModel));
            }
            
            SetupGauge(friendDataModel, 0);
            SetupLevel(friendDataModel);
            
            SetDialogSortOffsetAndLayer(sortOffset);
            SetAlphaReceiver(canvasRenderer);
        }

        private void SetupGauge(MasterlyBonus9048Model.FriendDataModel model, int predictGaugeUpValue)
        { 
            _mainProgressBar.SetValue(model.GaugeValue);
            // アニメーション用のゲージも現在値に合わせておく
            _animProgressBar.SetValue(model.GaugeValue);
            var predictProgressValue = Mathf.Clamp(model.GaugeValue + predictGaugeUpValue, 0, _gaugeMaxValue);
            _predictProgressBar.SetValue(predictProgressValue);
        }

        private void SetupLevel(MasterlyBonus9048Model.FriendDataModel model)
        {
            if (!model.IsMaxLevel)
            {
                foreach (var levelTextMotion in _levelTextMotions)
                {
                    levelTextMotion.SetMotionPlay(TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, model.Level));
                }
            }
        }

        /// <summary>
        /// 予兆演出を停止する
        /// </summary>
        public void StopAnnounce(MasterlyBonus9048Model.FriendDataModel friendDataModel)
        {
            if (friendDataModel.IsMaxGauge)
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IDLE_GAUGE_ACTIVE_FORMAT, friendDataModel));
            }
            else
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IDLE_FORMAT, friendDataModel));
            }
        }

        /// <summary>
        /// ゲージ上昇アニメーション
        /// </summary>
        /// <param name="currentFriendDataModel"></param>
        /// <param name="prevFriendDataModel"></param>
        public void PlayGaugeUp(
            MasterlyBonus9048Model.FriendDataModel currentFriendDataModel,
            MasterlyBonus9048Model.FriendDataModel prevFriendDataModel)
        {
            IsPlayingGaugeUpAnimation = true;
            
            // コールバック系全削除
            _animProgressBar.ActionValueChangeEnd = null;
            FlashPlayer.RemoveAllAction();
            _gaugeEffectMotion.RemoveAllAction();

            // ゲージMAXで変動がない場合は待機状態のラベルを再生して抜ける
            // 全サポカの得意練習が同じかつ全員ゲージMAXの場合、うち1人は得意トレーニングに配置されずに溢れてしまう。
            // この状態(=ゲージMAXにもかかわらず得意練習に配置されていない)の併せウマとトレーニングした場合はゲージ消費/レベルアップが発生せずここに入る
            if (prevFriendDataModel.IsMaxGauge && currentFriendDataModel.IsMaxGauge)
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_MAX_END_FORMAT, currentFriendDataModel));
                return;
            }

            if (prevFriendDataModel.IsMaxGauge)
            {
                // ゲージMAX状態なのでレベルアップ
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IN_LVUP_FORMAT, currentFriendDataModel), () => IsPlayingGaugeUpAnimation = false);

                SetupGauge(currentFriendDataModel, 0);
                SetupLevel(currentFriendDataModel);
            }
            else if(currentFriendDataModel.IsMaxGauge)
            {
                // 今回の上昇でMAXになる
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IN_UP_FORMAT, currentFriendDataModel));
                _gaugeEffectMotion.SetMotionPlay(GameDefine.A2U_IN00_LABEL);
                _animProgressBar.SetValue(prevFriendDataModel.GaugeValue);
                _animProgressBar.SetValue(currentFriendDataModel.GaugeValue, true);
                _animProgressBar.ActionValueChangeEnd = () =>
                {
                    var delay = GameDefine.BASE_FPS_TIME * 3f;
                    DOVirtual.DelayedCall(delay, () =>
                    {
                        FlashPlayer.Play(GetRootMotionLabel(LABEL_IN_MAX_FORMAT, currentFriendDataModel), () =>
                        {
                            FlashPlayer.Play(GetRootMotionLabel(LABEL_MAX_END_FORMAT, currentFriendDataModel));
                            IsPlayingGaugeUpAnimation = false;
                        });
                    });
                    
                    _gaugeEffectMotion.SetMotionPlay(GameDefine.A2U_IN01_LABEL);
                };
            }
            else
            {
                FlashPlayer.Play(GetRootMotionLabel(LABEL_IN_UP_FORMAT, currentFriendDataModel));
                _gaugeEffectMotion.SetMotionPlay(GameDefine.A2U_IN00_LABEL);
                _animProgressBar.SetValue(prevFriendDataModel.GaugeValue);
                _animProgressBar.SetValue(currentFriendDataModel.GaugeValue, true);
                _animProgressBar.ActionValueChangeEnd = () =>
                {
                    _gaugeEffectMotion.SetMotionPlay(GameDefine.A2U_IN01_LABEL);
                    _gaugeEffectMotion.SetAction(GameDefine.A2U_IN01_LABEL, () => IsPlayingGaugeUpAnimation = false, AnMotionActionTypes.End);
                };
            }

            AudioManager.Instance.PlaySe(AudioId.SFX_TRAINING_LOGPOSI_07);
        }

        /// <summary>
        /// ダイアログ上で表示する際のソート＆レイヤー設定
        /// </summary>
        /// <param name="offset"></param>
        private void SetDialogSortOffsetAndLayer(int offset)
        {
            _flashActionPlayer.gameObject.SetLayerRecursively(UIManager.DialogCanvas.gameObject.layer);
            _flashActionPlayer.SetSortLayer(UIManager.DialogCanvas.sortingLayerName);
            _flashActionPlayer.SetSortOffset(UIManager.DialogCanvas.sortingOrder + offset);
        }
        
        /// <summary>
        /// 該当のcanvasにAlpha追従させる
        /// </summary>
        /// <param name="targetCanvas"></param>
        private void SetAlphaReceiver(CanvasRenderer targetCanvas)
        {
            var receiver = FlashPlayer.gameObject.GetComponent<A2UAlphaReceiver>() ?? FlashPlayer.gameObject.AddComponent<A2UAlphaReceiver>();
            receiver.Set(targetCanvas, FlashPlayer.Motion);
        }
    }
}