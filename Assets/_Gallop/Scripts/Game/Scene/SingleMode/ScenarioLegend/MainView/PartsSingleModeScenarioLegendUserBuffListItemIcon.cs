using UnityEngine;
using Gallop.SingleMode.ScenarioLegend;

namespace Gallop
{
    using static AtlasSpritePath.SingleModeScenarioLegend;
    
    /// <summary>
    /// 伝説編：育成TOPバフリストUI内の1バフのアイコン画像部分
    /// </summary>
    [AddComponentMenu("")]
    public class PartsSingleModeScenarioLegendUserBuffListItemIcon : MonoBehaviour
    {
        [SerializeField] private ImageCommon _itemImage;
        [SerializeField] private ImageCommon _baseImage;
        [SerializeField] private ImageCommon _categoryImage;
        
        public void Setup(IBuffItemModel itemModel)
        {
            _baseImage.SetActiveWithCheck(true);
            _baseImage.sprite = GetLegendBuffIconFrameBgSprite(itemModel.LegendType);

            _categoryImage.SetActiveWithCheck(true);
            _categoryImage.sprite = GetLegendBuffIconFrameSprite(itemModel.LegendType, itemModel.BuffRank);
            
            _itemImage.SetActiveWithCheck(true);
            _itemImage.sprite = GetLegendBuffIconSprite(itemModel.LegendType, itemModel.IconId);
        }

        public void SetupEmpty()
        {
            _baseImage.sprite = GetLegendBuffIconBlankFrameSprite();
            _categoryImage.SetActiveWithCheck(false);
            _itemImage.SetActiveWithCheck(false);
        }
    }
}
