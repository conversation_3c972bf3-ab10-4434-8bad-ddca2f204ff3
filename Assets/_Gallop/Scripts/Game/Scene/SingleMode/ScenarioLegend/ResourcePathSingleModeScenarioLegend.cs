using UnityEngine;

namespace Gallop
{
    using static SingleModeScenarioLegendDefine;

    /// <summary>
    /// 育成追加シナリオ：伝説編
    /// </summary>
    public static partial class ResourcePath
    {
        #region UI Prefab

        /// <summary> 伝説編：UIパーツRoot </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_UI_ROOT = SINGLE_MODE_UI_ROOT + "ScenarioLegend/";

        public const string SINGLE_MODE_SCENARIO_LEGEND_MASTERLY_BONUS_UI_ROOT = SINGLE_MODE_SCENARIO_LEGEND_UI_ROOT + "MasterlyBonus/";

        public const string SINGLE_MODE_SCENARIO_LEGEND_BUFF_UI_ROOT = SINGLE_MODE_SCENARIO_LEGEND_UI_ROOT + "Buff/";

        public const string SINGLE_MODE_SCENARIO_LEGEND_STORY_CHOICE_EFFECT_GAUGE_ICON = SINGLE_MODE_SCENARIO_LEGEND_UI_ROOT + "PartsSingleModeScenarioLegendStoryChoiceEffectGaugeIcon";

        #endregion UI Prefab

        #region A2U

        public const string SINGLE_MODE_SCENARIO_LEGEND_FLASH_ROOT = SINGLE_MODE_FLASH_ROOT + "Legend/";
        public const string SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT = SINGLE_MODE_FLASH_COMBINE_ROOT + "Legend/";

        /// <summary>　伝説編：トレーニングステータスボックス 　</summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_TRAINING_STATUSBOX_00 = SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_training_statusbox00";

        /// <summary>　伝説編：アーモンドアイのカットイン遷移時の演出　</summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_EXHIBITION_CUTIN00 = SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_exhibition_cutin00";

        public const string BUFF_GAUGE_GAIN_A2U_PATH = SINGLE_MODE_SCENARIO_LEGEND_FLASH_ROOT + "pf_fl_singlemode_legend_badge_buffgauge00";
        
        public const string SINGLE_MODE_SCENARIO_LEGEND_BADGE_KNACK = SINGLE_MODE_SCENARIO_LEGEND_FLASH_ROOT + "pf_fl_singlemode_legend_badge_knack00";
        
        // 154681 特殊レースのゲート演出時のフェードの代わりに再生するWipe
        // いずれも必要になる箇所で定数として定義しているのでそれに倣って定義している。
        public const int SINGLE_MODE_SCENARIO_LEGEND_RACE_GATE_WIPE_ID = 0;
        public const int SINGLE_MODE_SCENARIO_LEGEND_RACE_GATE_WIPE_SUB_ID = 0;

        #endregion

        #region Effect

        public const string SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT = SINGLE_MODE_UI_EFFECT_ROOT + "Legend/";
        
        public const string SINGLE_MODE_SCENARIO_LEGEND_HINT_EFFECT = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_extraparam00";

        /// <summary>
        /// 伝説編：バフゲージが最大になった時のワンショットエフェクト
        /// </summary>
        public static string GetSingleModeScenarioLegendBuffGaugeIconMaxEffectPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_BUFF_GAUGE_ICON_MAX_EFFECT_BASE_PATH, legendType.ToIndex());

        private const string SINGLE_MODE_SCENARIO_LEGEND_BUFF_GAUGE_ICON_MAX_EFFECT_BASE_PATH = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_buffgauge_icon_glitter{0:D2}";

        /// <summary>
        /// 伝説編：バフゲージが最大の状態でゲージ部分に表示するループエフェクト
        /// </summary>
        public static string GetSingleModeScenarioLegendBuffGaugeMaxLoopEffectPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_BUFF_GAUGE_MAX_LOOP_EFFECT_BASE_PATH, legendType.ToIndex());

        private const string SINGLE_MODE_SCENARIO_LEGEND_BUFF_GAUGE_MAX_LOOP_EFFECT_BASE_PATH = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_buffgauge_loop{0:D2}";

        /// <summary>
        /// 伝説編：バフ発動演出のワンショットエフェクト
        /// </summary>
        public static string GetSingleModeScenarioLegendBuffActivateOneShotEffectPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_BUFF_ACTIVATE_ONE_SHOT_EFFECT_BASE_PATH, legendType.ToIndex());

        private const string SINGLE_MODE_SCENARIO_LEGEND_BUFF_ACTIVATE_ONE_SHOT_EFFECT_BASE_PATH = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_buff_icon_glitter{0:D2}";

        /// <summary>
        /// 伝説編：バフ発動演出のループエフェクト
        /// </summary>
        public static string GetSingleModeScenarioLegendBuffActivateLoopEffectPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_BUFF_ACTIVATE_LOOP_EFFECT_BASE_PATH, legendType.ToIndex());

        private const string SINGLE_MODE_SCENARIO_LEGEND_BUFF_ACTIVATE_LOOP_EFFECT_BASE_PATH = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_buff_icon_bg{0:D2}";
        
        /// <summary>
        /// 伝説編：バフ発動演出のループエフェクト
        /// 所持バフ一覧ダイアログ用
        /// </summary>
        public static string GetSingleModeScenarioLegendBuffActivateLoopEffectForDialogPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_BUFF_ACTIVATE_LOOP_EFFECT_DIALOG_BASE_PATH, legendType.ToIndex());

        private const string SINGLE_MODE_SCENARIO_LEGEND_BUFF_ACTIVATE_LOOP_EFFECT_DIALOG_BASE_PATH = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_buff_icon_bg{0:D2}_01";

        #endregion

        #region Bundle/Resources

        public const string SINGLE_MODE_SCENARIO_LEGEND_ROOT = "Single/ScenarioLegend/";

        /// <summary>
        /// マスタリーボーナス効果ダイアログ背景
        /// </summary>
        public static string GetSingleModeScenarioLegendMasterlyBonusDialogBgPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_ROOT + "utx_txt_legend_detailbg_{0:D4}_00", legendType.ToLegendId());

        /// <summary>
        /// マスタリーボーナスアイコン
        /// </summary>
        /// <param name="legendType"></param>
        /// <param name="isLock">未解放の鍵付き</param>
        /// <returns></returns>
        public static string GetSingleModeScenarioLegendMasterlyBonusIconPath(LegendType legendType, bool isLock = false)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_ROOT + "utx_ico_legend_masterly_symbol_{0:D4}_{1:D2}", legendType.ToLegendId(), isLock ? 0 : 1);

        private const string SINGLE_MODE_SCENARIO_LEGEND_RACE_LOGO = SINGLE_MODE_SCENARIO_LEGEND_ROOT + "tex_singlelegend_logo_000_{0:D4}_00";

        private static int GetSingleModeScenarioLegendRaceLogoId(SingleModeDefine.DegreeType degree)
        {
            // シナリオ固有レースDreamFestのベースID
            const int LOGO_ID_JUNIOR = 9080;
            const int LOGO_ID_CLASSIC = 9090;
            const int LOGO_ID_SENIOR = 9100;

            return degree switch
            {
                SingleModeDefine.DegreeType.Junior => LOGO_ID_JUNIOR,
                SingleModeDefine.DegreeType.Classic => LOGO_ID_CLASSIC,
                SingleModeDefine.DegreeType.Senior => LOGO_ID_SENIOR,
                _ => 0,
            };
        }

        /// <summary>
        /// シナリオレースの年次別ロゴを取得
        /// </summary>
        public static string GetSingleModeScenarioLegendScenarioRaceLogoTexturePath(SingleModeDefine.DegreeType degree)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_RACE_LOGO, GetSingleModeScenarioLegendRaceLogoId(degree));

        /// <summary>
        /// シナリオレースの年次・条件別ロゴを取得
        /// </summary>
        public static string GetSingleModeScenarioLegendScenarioRaceLogoTexturePathByCondition(SingleModeDefine.DegreeType degree, RaceDefine.GroundType ground, RaceDefine.CourseDistanceType distance)
        {
            var baseId = GetSingleModeScenarioLegendRaceLogoId(degree);

            // 芝レースは距離別(1~4)、ダートレースは共通(5)
            var offset = ground == RaceDefine.GroundType.Turf ? (int)distance : 5;
            const string FORMAT = SINGLE_MODE_SCENARIO_LEGEND_ROOT + "utx_tex_singlelegend_logo_000_{0:0000}_{1:00}";
            return TextUtil.Format(FORMAT, baseId + offset, 0);
        }

        /// <summary>
        /// バフ獲得演出 バフの下地(A2U置換用)
        /// </summary>
        public static string GetSingleModeScenarioLegendAcquiredBuffBasePlatePath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_ROOT + "utx_frm_legend_buff_{0:D4}_00", legendType.ToLegendId());

        /// <summary>
        /// マスタリーボーナス獲得演出：背景画像
        /// </summary>
        public static string GetSingleModeScenarioLegendMasterlyBonusAcquireBgPath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_MASTERLY_BONUS_ACQUIRE_BG, legendType.ToLegendId());

        private const string SINGLE_MODE_SCENARIO_LEGEND_MASTERLY_BONUS_ACQUIRE_BG = "Single/ScenarioLegend/singlelegend_getmasterly_bg_{0:D4}_00";

        /// <summary>
        /// マスタリーボーナス獲得演出：背景パーティクル
        /// </summary>
        public static string GetSingleModeScenarioLegendMasterlyBonusAcquireBgParticlePath(LegendType legendType)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_MASTERLY_BONUS_ACQUIRE_BG_PARTICLE, legendType.ToIndex() + 1);

        private const string SINGLE_MODE_SCENARIO_LEGEND_MASTERLY_BONUS_ACQUIRE_BG_PARTICLE = SINGLE_MODE_SCENARIO_LEGEND_EFFECT_ROOT + "pfb_uieff_single_legend_masterly_get_bg_particle{0:D2}";

        /// <summary>
        /// エキシビションレース開始直前特殊演出用のカットインコントローラ側で使用する調整用アセット
        /// </summary>
        public const string RaceGateSetCutControllerParamPath = "Race/RaceGate/ast_race_gate_cut_control_param";

        /// <summary> エキシビジョンレースTOP：地下バ道モデル </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_RACE_TOP_BG_PREFAB_PATH = Root3d + "Env/Set/set70001/Main/pfb_env_set70001_main002_000";

        /// <summary>
        /// CMの路線別サムネイル
        /// </summary>
        public static string GetSingleModeScenarioLegendCommercialThumbPath(int routeId)
            => TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_ROOT + "utx_img_legend_cm_production_{0:D2}", routeId - 1);

        #endregion

        #region CutIn

        /// <summary>
        /// 評判演出写真集カットイン（本体）
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_BOOK = SET_CUTT_ROOT + "set_008_01_03_legend_reputa_photo/Type01";

        /// <summary>
        /// 評判演出写真集カットイン（ページ）
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_PAGE_FORMAT = SET_CUTT_ROOT + "set_008_01_03_legend_reputa_photo_page{0:D2}/type{1:D2}";
        public static string GetSingleModeScenarioLegendReputationPhotoCutInPagePath(int typeIndex, int pageIndex)
        {
            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_PAGE_FORMAT, pageIndex, typeIndex);
        }

        /// <summary>
        /// 評判演出写真集カットイン（1ページ目専用）
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_PAGE01_FORMAT = SET_CUTT_ROOT + "set_008_01_03_legend_reputa_photo_page01/type{0:D2}_{1:D2}";
        public static string GetSingleModeScenarioLegendReputationPhotoCutInPage01Path(ModelLoader.RacePersonalityType personalityType, ModelLoader.RaceRunningType runningType)
        {
            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_PAGE01_FORMAT, (int)personalityType, (int)runningType);
        }

        /// <summary>
        /// 評判演出ファン交流カットイン
        /// </summary>
        private const string SINGLE_MODE_SCENARIO_LEGEND_FAN_CONTACT_PATH_FORMAT = SET_CUTT_ROOT + "set_008_01_legend_reputa01_fan01_{0:D2}/type{1:D2}";
        public static string GetSingleModeScenarioLegendFanContactPath(int cuttIndex, int type)
        {
            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_FAN_CONTACT_PATH_FORMAT, cuttIndex, type);
        }

        /// <summary> 伝説編で使う評判カットの2D観客テクスチャパス </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_FAN_CONTACT_AUDIENCE_TEXTURE_PATH = "3d/Env/Set/set20034/Main/Textures/tex_env_set20034_main000_000_audience{0:D2}";

        /// <summary>
        /// 評判演出広告カットイン
        /// </summary>
        private const string SINGLE_MODE_SCENARIO_LEGEND_ADVERTISEMENT_PATH = SET_CUTT_ROOT + "set_008_01_legend_reputa01_ad/set_008_01_legend_reputa01_ad_{0:D2}";
        public static string GetSingleModeLegendAdvertisementCuttPath(int legendCharaId)
        {
            var index = legendCharaId switch
            {
                SingleModeScenarioLegendDefine.LEGEND_ID_9046 => 3,
                SingleModeScenarioLegendDefine.LEGEND_ID_9047 => 2,
                SingleModeScenarioLegendDefine.LEGEND_ID_9048 => 1,
                _ => 1,
            };

            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_ADVERTISEMENT_PATH, index);
        }

        /// <summary>
        /// エキシビションレース開始直前特殊演出用のカットイン
        /// </summary>
        public const string SINGLE_MODE_LEGEND_RACE_GATE_CUTIN_FORMAT = SET_CUTT_ROOT + "set_008_05_legend_PreRace{0:00}/set_008_05_legend_PreRace{0:00}_{1:00}";
        public static string GetSingleModeLegendRaceGateCutInPagePath(int cutType, int cutIndex)
        {
            return TextUtil.Format(SINGLE_MODE_LEGEND_RACE_GATE_CUTIN_FORMAT, cutType, cutIndex);
        }

        /// <summary>
        /// 評判演出ポスターカットイン
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_CUTIN_FORMAT = SET_CUTT_ROOT + "set_008_01_legend_reputa01_poster/set_008_01_legend_reputa01_poster_{0:D2}";
        public static string GetSingleModeScenarioLegendReputationPosterCutInPath(int typeIndex)
        {
            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_CUTIN_FORMAT, typeIndex);
        }

        /// <summary> 伝説編で使うカットのモーションベースパス </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_CUTT_CHARA_TYPE_MOTION_PATH_ROOT = MOTION_ROOT + "Set/008_legend/";
        /// <summary> 伝説編で使うキャラタイプによるモーション差し替えパターン </summary>
        private const string SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_NAME_REPLACE_PATTERN = @"anm_set_type\d{2}";
        /// <summary> 伝説編で使うモーション名の差し替え文字列 </summary>
        private const string SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_REPLACE_NAME = "anm_set_type{0:D2}";

        /// <summary>
        /// 伝説編カットで利用するType差分のBodyモーションパスを取得
        /// </summary>
        /// <param name="charaType"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioLegendCuttBodyMotionPath(int charaType, AnimationClip baseClip)
        {
            const string BODY_MOTION_PATH = SINGLE_MODE_SCENARIO_LEGEND_CUTT_CHARA_TYPE_MOTION_PATH_ROOT + "Body/Type{0:D2}/{1}";
            if (charaType < 0)
            {
                return string.Empty;
            }

            if (baseClip == null)
            {
                return string.Empty;
            }

            var regex = new System.Text.RegularExpressions.Regex(SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_NAME_REPLACE_PATTERN);
            var replaceClipName = regex.Replace(baseClip.name, TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_REPLACE_NAME, charaType));

            return TextUtil.Format(BODY_MOTION_PATH, charaType, replaceClipName);
        }

        /// <summary>
        /// 伝説編カットで利用するType差分のEarとFacialモーションパスを取得
        /// (EarとFacialは同じフォルダー内に配置)
        /// </summary>
        /// <param name="charaType"></param>
        /// <param name="clip"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioLegendCuttFacialMotionPath(int charaType, DrivenKeyAnimation baseClip)
        {
            const string FACIAL_MOTION_PATH = SINGLE_MODE_SCENARIO_LEGEND_CUTT_CHARA_TYPE_MOTION_PATH_ROOT + "Facial/Type{0:D2}/{1}";

            if (charaType < 0)
            {
                return string.Empty;
            }

            if (baseClip == null)
            {
                return string.Empty;
            }

            var regex = new System.Text.RegularExpressions.Regex(SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_NAME_REPLACE_PATTERN);
            var replaceClipName = regex.Replace(baseClip.name, TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_REPLACE_NAME, charaType));
            return TextUtil.Format(FACIAL_MOTION_PATH, charaType, replaceClipName);
        }

        /// <summary>
        /// 伝説編カットで利用するType差分のPositionモーションパスを取得
        /// </summary>
        /// <param name="charaType"></param>
        /// <param name="clip"></param>
        /// <returns></returns>
        public static string GetSingleModeScenarioLegendCuttPositionMotionPath(int charaType, AnimationClip baseClip)
        {
            const string POSITION_MOTION_PATH = SINGLE_MODE_SCENARIO_LEGEND_CUTT_CHARA_TYPE_MOTION_PATH_ROOT + "Position/Type{0:D2}/{1}";

            if (charaType < 0)
            {
                return string.Empty;
            }

            if (baseClip == null)
            {
                return string.Empty;
            }

            var regex = new System.Text.RegularExpressions.Regex(SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_NAME_REPLACE_PATTERN);
            var replaceClipName = regex.Replace(baseClip.name, TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_CUTT_MOITON_CLIP_REPLACE_NAME, charaType));
            return TextUtil.Format(POSITION_MOTION_PATH, charaType, replaceClipName);
        }

        /// <summary>
        /// ポスター演出に乗せるテキストのレイアウト設定
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_TEXT_PARAM = "Single/ScenarioLegend/ast_single_mode_scenario_legend_reputation_poster_param";

        // <summary> 伝説編：アーモンドアイ登場演出</summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_SPECIAL_RIVAL_CUTT_PATH = SET_CUTT_ROOT + "set_008_04_RivalCutIn01/set_008_04_RivalCutIn01";

        /// <summary>
        /// キャッチコピーの差し替え画像(_00 ~ _17)
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_CATCHPHRASE_IMAGE_FORMAT = "Single/ScenarioLegend/utx_txt_legend_poster_catchphrase_{0:D2}";
        public static string GetSingleModeScenarioLegendReputationPosterCatchphraseImagePath(int typeIndex)
        {
            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_CATCHPHRASE_IMAGE_FORMAT, typeIndex);
        }

        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_MATERIAL = "Single/ScenarioLegend/mat_single_mode_scenario_legend_reputaion_poster_white_frame";

        public static string GetSingleModeScenarioLegendReputationPosterEventCameraPrefabNameWithoutRotation(int number)
        {
            return GetEventCameraPrefabNameWithoutRotation(RaceDefine.EventCameraCategory.PosterGoalCapture, 0, 0, number);
        }

        /// <summary>
        /// 広告演出カットで利用するロゴA2U演出
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_ADVERTISEMENT_CUTT_LOGO_A2U = FLASH_COMBINE_ACTION_ROOT + "SingleMode/Legend/fa_singlemode_legend_eputa01_ad";

        /// <summary>
        /// 写真集表紙用テキスト背景画像
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_COVER_TEXT_BG_IMAGE = "Single/ScenarioLegend/utx_frm_legend_photobook_00";

        /// <summary>
        /// 写真集表紙用 帯テキスト画像(_00 ~ _05)
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_OBI_TEXT_IMAGE_FORMAT = "Single/ScenarioLegend/utx_txt_legend_photobook_obi_{0:D2}";

        public static string GetSingleModeScenarioLegendReputationPhotoBookObiTextImage(int typeIndex)
        {
            return TextUtil.Format(SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_OBI_TEXT_IMAGE_FORMAT, typeIndex);
        }

        /// <summary>
        /// 写真集表紙用帯エンブレム画像
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_OBI_EMBLEM_IMAGE = "Single/ScenarioLegend/utx_txt_legend_photobook_emblem_00";


        /// <summary>
        /// 写真集演出に乗せるテキストのレイアウト設定
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_TEXT_PARAM = "Single/ScenarioLegend/ast_single_mode_scenario_legend_reputation_photo_param";

        /// <summary>
        /// ポスター演出のキャプチャ用カメラ設定
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_POSTER_CAPTURE_PARAM = "Single/ScenarioLegend/ast_single_mode_scenario_legend_reputation_poster_capture_param";
        #endregion

        #region サウンド

        /// <summary>
        /// 伝説編サウンド：SE
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_SE_CUE_SHEET_NAME = "snd_sfx_training_010";

        /// <summary>
        /// 伝説編サウンド：Voice
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_VOICE_CUE_SHEET_NAME = "snd_voi_leg_cm";
        
        /// <summary>
        /// 伝説編サウンド：CM演出BGM
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_CM_BGM_CUE_SHEET_NAME = "snd_bgm_GM408";

        /// <summary>
        /// 伝説編サウンド：Voice：特殊ゴール後演出ガヤ　男性・汎用：キューシート・キュー名
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_OVERRUN_CUTIN_AUDIENCE_VOICE_M_COMMON_CUE_SHEET_NAME = "snd_voi_leg_gaya_m";

        /// <summary>
        /// 伝説編サウンド：Voice：特殊ゴール後演出ガヤ　男性・愛称：キューシート
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_OVERRUN_CUTIN_AUDIENCE_VOICE_M_UNIQUE_CUE_SHEET_NAME = "snd_voi_leg_aftergoal_m";

        /// <summary>
        /// 伝説編サウンド：Voice：特殊ゴール後演出ガヤ　男性・愛称：キュー形式
        /// </summary>
        /// <remarks>
        /// CharaIdを指定して使用する
        /// </remarks>
        public const string SINGLE_MODE_SCENARIO_LEGEND_OVERRUN_CUTIN_AUDIENCE_VOICE_M_UNIQUE_CUE_NAME_FORMAT = "snd_voi_leg_aftergoal_m_{0:d4}";

        /// <summary>
        /// 伝説編サウンド：Voice：特殊ゴール後演出ガヤ　女性・汎用：キューシート・キュー名
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_OVERRUN_CUTIN_AUDIENCE_VOICE_F_COMMON_CUE_SHEET_NAME = "snd_voi_leg_gaya_f";

        /// <summary>
        /// 伝説編サウンド：Voice：特殊ゴール後演出ガヤ　女性・愛称：キューシート
        /// </summary>
        public const string SINGLE_MODE_SCENARIO_LEGEND_OVERRUN_CUTIN_AUDIENCE_VOICE_F_UNIQUE_CUE_SHEET_NAME = "snd_voi_leg_aftergoal_f";

        /// <summary>
        /// 伝説編サウンド：Voice：特殊ゴール後演出ガヤ 女性・愛称：キュー形式
        /// </summary>
        /// <remarks>
        /// CharaIdを指定して使用する
        /// </remarks>
        public const string SINGLE_MODE_SCENARIO_LEGEND_OVERRUN_CUTIN_AUDIENCE_VOICE_F_UNIQUE_CUE_NAME_FORMAT = "snd_voi_leg_aftergoal_f_{0:d4}";

        #endregion サウンド
    }
}
