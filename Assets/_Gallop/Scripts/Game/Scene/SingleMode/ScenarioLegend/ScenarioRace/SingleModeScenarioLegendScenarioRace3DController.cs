using System.Collections.Generic;
using System.Linq;
using Gallop.SingleMode.ScenarioLegend;
using UnityEngine;
using Gallop.RenderPipeline;
using UnityEngine.Rendering.Universal;

namespace Gallop
{
    using static SingleModeScenarioLegendDefine;
    
    public class SingleModeScenarioLegendScenarioRace3DModel : SingleModeScenarioContentsTop3DModel
    {
        public const string IMAGE_EFFECT_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_ROOT + "ast_param_imageffect_single_mode_legend_race_top";
        public const string CHARA_PARAMS_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_ROOT + "ast_single_mode_scenario_legend_race_top_chara_param";
        
        #region Implement
        
        public override string ImageEffectPath => IMAGE_EFFECT_PATH;

        public override SingleModeScenarioContentsTop3DCharaParam LoadCharaParam()
            => SingleModeScenarioLegendScenarioRace3DCharaParam.LoadCharaParam();
        
        private class SingleModeScenarioLegendScenarioRace3DCharaParam : SingleModeScenarioContentsTop3DCharaParam
        {
            public static SingleModeScenarioContentsTop3DCharaParam LoadCharaParam()
                => Load<SingleModeScenarioContentsTop3DCharaParam>(CHARA_PARAMS_PATH);
        }
        
        #endregion

        public List<SingleModeScenarioContentsCharaInfo> CreateCharaBuildInfos()
        {
            var charaBuildInfos = new List<SingleModeScenarioContentsCharaInfo>();
            
            // 育成キャラ
            charaBuildInfos.Add(SingleModeScenarioContentsCharaInfo.CreateAtMain());
            
            var random = new System.Random(WorkDataManager.Instance.SingleMode.FixedTurnCharaSeed);
            
            // 3レジェンド
            var legendIds = new [] { LEGEND_ID_9046, LEGEND_ID_9047, LEGEND_ID_9048};
            
            MasterlyBonusModelRepository.TryGet(out var masterlyBonusModel);
            if (masterlyBonusModel != null)
            {
                // 導きレジェンドキャラを指定して、他2名ランダム
                CreateLastLegendCharaInfos(masterlyBonusModel.LegendType.ToLegendId());
            }
            else
            {
                var buffListModel = BuffListModelRepository.Get();
                if (buffListModel.BuffItems.Count > 0)
                {
                    // タイプ別グループ作成（そのタイプの数を保存したグループ）
                    var legendTypeGroup = buffListModel.BuffItems.GroupBy(x => x.LegendType)
                        .Select(group => new{legendType = group.Key, count = group.Count()});

                    // タイプ別で最多数
                    var max = legendTypeGroup.Max(x => x.count);
                    // 最多数グループ抽出。同数ならランダムで1人取り出す
                    var legend = legendTypeGroup.Where(x => x.count == max).OrderByDescending(a => random.Next()).FirstOrDefault();

                    // 最多の想いレジェンドキャラを指定して、他2名をランダム
                    CreateLastLegendCharaInfos(legend.legendType.ToLegendId());
                }
                else
                {
                    // 1つも想いがない時は、全ランダム
                    CreateLegendCharaInfos(legendIds.OrderBy(a => random.Next()));
                }
            }

            // 右に立たせる代表のレジェンドキャラを指定して、他2名をランダム
            void CreateLastLegendCharaInfos(int lastLegendCharaId)
            {
                // 他2名はランダム
                CreateLegendCharaInfos(legendIds.Where(x => x != lastLegendCharaId).OrderBy(a => random.Next()));
                
                charaBuildInfos.Add(CreateLegendCharaInfo(lastLegendCharaId));
            }
            

            void CreateLegendCharaInfos(IEnumerable<int> charaIds)
            {
                foreach (var charaId in charaIds)
                {
                    charaBuildInfos.Add(CreateLegendCharaInfo(charaId));
                }
            }
            
            SingleModeScenarioContentsCharaInfo CreateLegendCharaInfo(int legendId)
            {
                var info = SingleModeScenarioContentsCharaInfo.Create(legendId);
                info.CharaId = legendId;
                info.CardId = legendId;
                info.DressId = MasterDataManager.Instance.masterDressData.GetWithCharaIdOrderByIdAsc(legendId).Id;
                return info;
            }

            return charaBuildInfos;
        }
    }
    /// <summary>
    /// 伝説編：シナリオレース3D Control
    /// </summary>
    public class SingleModeScenarioLegendScenarioRace3DController : SingleModeScenarioContentsTop3DController
    {
        /// <summary> カメラのRotation設定使用する </summary>
        protected override bool UseCameraRotation => true;

        /// 156382 シャドウの解像度を弄る前の解像度設定情報を保持
        private bool _cameraDefaultIsOverrideShadowResolution = false;
        private UnityEngine.Rendering.Universal.ShadowResolution _cameraDefaultShadowResolution = UnityEngine.Rendering.Universal.ShadowResolution._1024;
        private RenderPipeline.CameraData _usingCameraData = null;

        #region Implement
        
        protected override void RegisterDownloadCore(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(SingleModeScenarioLegendScenarioRace3DModel.CHARA_PARAMS_PATH);
            register.RegisterPathWithoutInfo(SingleModeScenarioLegendScenarioRace3DModel.IMAGE_EFFECT_PATH);
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_RACE_TOP_BG_PREFAB_PATH);
        }

        protected override void DestroyCore()
        {
            //156382 画面が破棄される際にカメラの設定を元に戻す
            if (_usingCameraData == null)
            {
                return;
            }

            _usingCameraData.IsOverrideShadowResolution = _cameraDefaultIsOverrideShadowResolution;
            _usingCameraData.OverrideShadowResolution = _cameraDefaultShadowResolution;
            _usingCameraData = null;
        }
        
        #endregion
        
        public void Setup(SingleModeScenarioContentsTop3DView view, SingleModeScenarioLegendScenarioRace3DModel model)
        {
            //156382 親クラスでカメラの設定後、リアルタイムシャドウの解像度を調整
            OnSetupCamera = SetupRealtimeShadowResolution;

            base.Setup(view, model);
            SetupBg3d(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_RACE_TOP_BG_PREFAB_PATH);
        }

        /// <summary>
        /// 156382 リアルタイムシャドウの解像度調整
        /// </summary>
        /// <param name="camera"></param>
        private void SetupRealtimeShadowResolution(Camera camera)
        {
            if (camera == null)
            {
                return;
            }

            _usingCameraData = camera.GetCameraData();

            _cameraDefaultIsOverrideShadowResolution = _usingCameraData.IsOverrideShadowResolution;
            _usingCameraData.IsOverrideShadowResolution = true;
            _cameraDefaultShadowResolution = _usingCameraData.OverrideShadowResolution;
            //見た目の要望で2048指定
            _usingCameraData.OverrideShadowResolution = UnityEngine.Rendering.Universal.ShadowResolution._2048;

        }
    }
}
