using System;
using System.Linq;

namespace Gallop
{
    using static SingleModeDefine;
    
    /// <summary>
    /// 伝説編：シナリオレースTOP Model
    /// </summary>
    public class SingleModeScenarioLegendScenarioRaceTopViewModel
    {
        public const float RIVAL_ENTRY_BGM_STOP_FADE_TIME = 1.0f;
        
        /// <summary> Viewで表示対象の single_mode_10_sprace.csv のレコード </summary>
        private readonly MasterSingleMode10Sprace.SingleMode10Sprace _currentSpRaceMaster;

        /// <summary> 開催されているレース条件 </summary>
        private readonly (RaceDefine.CourseDistanceType DistanceType, RaceDefine.GroundType GroundType) _currentSpRaceCourseInfo;

        public string RaceTitlePath
            => ResourcePath.GetSingleModeScenarioLegendScenarioRaceLogoTexturePathByCondition(DegreeType, _currentSpRaceCourseInfo.GroundType, _currentSpRaceCourseInfo.DistanceType);

        public WorkSingleModeCharaData WorkChara
            => WorkDataManager.Instance.SingleMode.Character;

        public ResourcePath.CharaPetitCatetory RaceButtonCategoryOff
            => DegreeType switch
            {
                DegreeType.Junior => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestStellaOff,
                DegreeType.Classic => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestPrideOff,
                DegreeType.Senior => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestLegendOff,
                _ => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestStellaOff
            };
        
        public ResourcePath.CharaPetitCatetory RaceButtonCategoryOn
            => DegreeType switch
            {
                DegreeType.Junior => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestStellaOn,
                DegreeType.Classic => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestPrideOn,
                DegreeType.Senior => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestLegendOn,
                _ => ResourcePath.CharaPetitCatetory.SingleScenarioLegendDreamFestStellaOn
            };

        /// <summary>
        /// 遷移時にライバル参戦演出が必要かどうか
        /// </summary>
        public bool IsNeedPlayRivalEntryAnimation => WorkDataManager.Instance.SingleMode.RaceStartResultInfoData.StartInfo.race_horse_data.Any(IsRivalChara);
        
        private bool IsRivalChara(RaceHorseData raceHorseData)
            => raceHorseData.trained_chara_id is SingleModeScenarioLegendDefine.RIVAL_NPC_ID_1 or SingleModeScenarioLegendDefine.RIVAL_NPC_ID_2;
        
        private DegreeType DegreeType
            => SingleModeUtils.GetDegreeType(WorkDataManager.Instance.SingleMode.GetTurnSetId(), _currentSpRaceMaster.TurnNum);

        /// <summary>
        /// 最後のシナリオレースか
        /// </summary>
        private bool IsLastSchedule
            => MasterDataManager.Instance.masterSingleMode10Sprace.OrderedSchedule.LastOrDefault()?.Id == _currentSpRaceMaster.Id;

        /// <summary> 環境音のキューシート名 </summary>
        public string EnvironmentCueSheet => EnvironmentCueCommon;
        /// <summary> 環境音のキュー名 </summary>
        public string EnvironmentCueName => EnvironmentCueCommon;
        private string EnvironmentCueCommon => TextUtil.Format("snd_sfx_atmos_exhibition_top_{0:D2}", _currentSpRaceMaster.Id);

        public SingleModeScenarioLegendScenarioRaceTopViewModel()
        {
            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            _currentSpRaceMaster = MasterDataManager.Instance.masterSingleMode10Sprace.GetNextSchedule(currentTurn);

            var startInfo = WorkDataManager.Instance.SingleMode.RaceStartResultInfoData.StartInfo;
            var program = MasterDataManager.Instance.masterSingleModeProgram.Get(startInfo.program_id);
            _currentSpRaceCourseInfo = program.GetRaceDistanceAndGround() ?? new ValueTuple<RaceDefine.CourseDistanceType, RaceDefine.GroundType>();
        }

        public SingleModeScenarioLegendScenarioRaceTopViewModel(int spRaceId, RaceDefine.GroundType ground, RaceDefine.CourseDistanceType distance)
        {
            _currentSpRaceMaster = MasterDataManager.Instance.masterSingleMode10Sprace.Get(spRaceId);
            _currentSpRaceCourseInfo = (distance, ground);
        }
    }
    
#if CYG_DEBUG
    public class DebugSingleModeScenarioLegendScenarioRaceTopViewModel : IViewInfo
    {
        public int SpRaceId { get; set; }
        public RaceDefine.GroundType Ground { get; set; }
        public RaceDefine.CourseDistanceType Distance { get; set; }
        
        public SingleModeScenarioLegendScenarioRaceTopViewModel Model
            => new SingleModeScenarioLegendScenarioRaceTopViewModel(SpRaceId, Ground, Distance);
    }
#endif
}
