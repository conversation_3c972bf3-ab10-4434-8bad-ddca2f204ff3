using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 伝説編：シナリオレースTOP
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeScenarioLegendScenarioRaceTopView : ViewBase
    {
        [field: SerializeField, RenameField]
        public RawImageCommon RaceTitle { get; private set; }
        
        [field: SerializeField, RenameField]
        public SingleModeMainViewTrainingStatus TrainingStatus { get; private set; }
        [field: SerializeField, RenameField]
        public SingleModeMainViewSkillPointPanel SKillPointPanel { get; private set; }
        
        [field: SerializeField, RenameField]
        public PartsSingleModeCharaGradePlate CharaGrade { get; private set; }
        
        [field: SerializeField, RenameField]
        public PartsSingleModeScenarioScenarioRaceTopCharaMessage CharaMessage { get; private set; }
        
        [field: SerializeField, RenameField]
        public Button<PERSON>ommon DetailButton { get; private set; }
        
        [field: Serial<PERSON><PERSON><PERSON>, <PERSON>ame<PERSON>ield]
        public ButtonCom<PERSON> SkillButton { get; private set; }
        [field: SerializeField, RenameField]
        public ButtonCommon RaceButton { get; private set; }
        
        [field: SerializeField, RenameField]
        public SingleModeScenarioContentsTop3DView Top3DContentsView { get; private set; } = null;
    }

    /// <summary>
    /// 伝説編：シナリオレースTOP
    /// </summary>
    public sealed class SingleModeScenarioLegendScenarioRaceTopViewController : ViewControllerBase<SingleModeScenarioLegendScenarioRaceTopView>
    {

        public class SingleModeScenarioLegendScenarioRaceTopViewControllerProxy
        {
            private readonly SingleModeScenarioLegendScenarioRaceTopViewController _viewController;
            public SingleModeScenarioLegendScenarioRaceTopViewControllerProxy(SingleModeScenarioLegendScenarioRaceTopViewController viewController)
            {
                _viewController = viewController;
            }
            
            public bool TryTapRivalEntryButton()
            {
                return _viewController._partsRivalEntry.CheckAlive()?.AutoPlayProxy.TryTapButton() ?? false;
            }

            public bool IsNeedPlayRivalEntryAnimation => _viewController.Model.IsNeedPlayRivalEntryAnimation;
        }
        private SingleModeScenarioLegendScenarioRaceTopViewModel Model
        {
            get
            {
#if CYG_DEBUG
                // デバコマから来た場合はそちらのModelを使用する
                if (GetViewInfo() is DebugSingleModeScenarioLegendScenarioRaceTopViewModel debugViewInfo) _model ??= debugViewInfo.Model;
#endif
                return _model ??= new SingleModeScenarioLegendScenarioRaceTopViewModel();
            }
        }
        private SingleModeScenarioLegendScenarioRaceTopViewModel _model;

        private bool _isEnableBackKey = true;
        
        private SingleModeScenarioLegendScenarioRace3DController _modelController;
        private SingleModeScenarioLegendScenarioRaceRivalCutt _partsRivalEntry;

        private SingleModeScenarioLegendScenarioRaceTopViewControllerProxy _autoPlayProxy;
        public SingleModeScenarioLegendScenarioRaceTopViewControllerProxy AutoPlayProxy => 
            _autoPlayProxy ??= new SingleModeScenarioLegendScenarioRaceTopViewControllerProxy(this);
        
        #region ViewControllerBase
        
        /// <summary>
        /// ダウンロード対象リソースの登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(Model.RaceTitlePath);

            _modelController = new SingleModeScenarioLegendScenarioRace3DController();
            _modelController.RegisterDownload(register);
            
            SingleModeScenarioLegendUtils.RegisterDownloadCommon(register);
            PartsSingleModeCharaGradePlate.RegisterDownload(register);
            DialogSingleModeRaceConfirm.RegisterDownload(register);
            PartsSingleModeRaceButtonEffect.RegisterDownload(register);
            
            DialogSingleModeScenarioRaceEntry.RegisterDownload(register);
            // アーモンドアイ登場演出
            SingleModeScenarioLegendScenarioRaceRivalCutt.RegisterDownload(register);
            
            // BGM
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.BGM_SINGLE_MODE_SCENARIO_LEGEND_RACE_TOP);
            
            // 環境音
            AudioManager.Instance.RegisterDownloadByCueSheet(register, Model.EnvironmentCueSheet, AudioManager.SubFolder.Se);
            
            // ボイス
            AudioManager.Instance.RegisterDownloadByCharaIds(register, new List<int> { WorkDataManager.Instance.SingleMode.Character.CharaId }, CharacterSystemTextGroupExtension.Scene.SingleMode, true);
        }

        /// <summary>
        /// ビュー初期化
        /// </summary>
        public override IEnumerator InitializeView()
        {
            SetupSingleModeHeader();
            SetupSingleModeFooter();

            SetupRaceTitle();
            
            SetupCharacterStatus();
            
            SetupRaceButton();
            SetupSKillButton();

            SetupModel();

            yield return UIManager.WaitForEndOfFrame;//#120083 1フレームライティング反映を待つ
        }

        public override IEnumerator InitializeEachPlayIn()
        {
            yield break;
        }
        
        /// <summary>
        /// BGM
        /// </summary>
        public override AudioId GetDynamicBgmId()
        {
            return WorkDataManager.Instance.SingleMode.GetCurrentTurnBgmId();
        }
        
        /// <summary>
        /// BGM
        /// </summary>
        public override AudioManager.CueSheetCueNameInfo GetDynamicBgmCueInfo()
        {
            return WorkDataManager.Instance.SingleMode.GetCurrentTurnBgmInfo();
        }
        
        /// <summary>
        /// ローディングタイプ
        /// </summary>
        public override void OverrideDynamicNowLoadingType(ref NowLoading.Type loadingType, SceneDefine.ViewId prevViewID)
        {
            if (prevViewID == SceneDefine.ViewId.SingleModeSkillLearning)
            {
                loadingType = NowLoading.Type.WhiteOutWithHorseShoe;
                return;
            }
            base.OverrideDynamicNowLoadingType(ref loadingType, prevViewID);
        }
        
        /// <summary>
        /// IN
        /// </summary>
        public override IEnumerator PlayInView()
        {
            //タイトルヘッダー
            UIManager.SingleModeHeader.SetTitleHeader(TextId.SingleModeScenarioLegend539028, GetTutorialGuideId(), true);
            
            _modelController.SetCharacterActive(true);
            
            // 環境音再生
            SingleModeUtils.PlayEnvironmentSe(Model.EnvironmentCueSheet, Model.EnvironmentCueName);
            //ボイスエフェクトON
            SetAudioBusParam(true);
            
            yield break;
        }

        public override void BeginView()
        {
            DialogTutorialGuide.PushDialogWithReadCheck(GetTutorialGuideId());
        }
        
        private DialogTutorialGuide.TutorialGuideId GetTutorialGuideId() => DialogTutorialGuide.TutorialGuideId.SingleModeScenarioLegendRaceTop;
        
        /// <summary>
        /// 更新
        /// </summary>
        public override void UpdateView()
        {
            _modelController.UpdateCharacterModelSetting();
            _partsRivalEntry?.UpdateView();
        }
        
        public override void LateUpdateView()
        {
            _partsRivalEntry?.LateUpdateView();
        }
        
        public override IEnumerator FinalizeView()
        {
            StopEnvironmentSeAndBusParam();//#154776 通信エラーでソフトウェアリセットのパターンもあるのでここでケア
            _modelController.Destroy();
            
            if (_partsRivalEntry != null)
            {
                DestroyRivalEntryAnimation();
            }
            
            yield break;
        }
        
        public override void OnClickOsBackKey()
        {
            if (_isEnableBackKey == false)
            {
                UIUtil.ShowNotificationBackKey();
                return;
            }
            
            DialogSingleModeTopMenu.Open();
        }
        
        #endregion
        
        #region Setup

        private void SetupSingleModeHeader()
        {
            UIManager.Instance.CreateSingleModeHeader();
            UIManager.SingleModeHeader.Setup();
            UIManager.SingleModeHeader.SetVisible(false);
            UIManager.SingleModeHeader.SetButtonEnable(false,true);
        }

        private void SetupSingleModeFooter()
        {
            UIManager.Instance.CreateSingleModeFooter();
        }

        private void SetupRaceTitle()
        {
            _view.RaceTitle.texture = ResourceManager.LoadOnView<Texture2D>(Model.RaceTitlePath);
        }
        
        /// <summary>
        /// キャラクターステータス表示
        /// </summary>
        private void SetupCharacterStatus()
        {
            //ステータスを設定
            _view.TrainingStatus.Setup(Model.WorkChara);
            _view.SKillPointPanel.SetSkillPoint(Model.WorkChara.SkillPoint);

            // キャラグレード
            _view.CharaGrade.Setup();

            //能力詳細ボタン
            _view.DetailButton.SetOnClick(() => DialogSingleModeMainCharacterDetail.Open(Model.WorkChara));
        }

        /// <summary>
        /// レースボタン
        /// </summary>
        private void SetupRaceButton()
        {
            // レースボタンエフェクト
            PartsSingleModeRaceButtonEffect.Create(_view.RaceButton.transform, StaticVariableDefine.SingleMode.PartsSingleModeRaceButtonEffect.TARGET_RACE_BUTTON_EFFECT_SIZE);
            // レースボタン
            SingleModeUtils.SetupButtonCharaPetit(_view.RaceButton, Model.RaceButtonCategoryOff, Model.RaceButtonCategoryOn);
            _view.RaceButton.SetOnClick(OnClickRaceButton);
        }

        /// <summary>
        /// スキルボタン
        /// </summary>
        private void SetupSKillButton()
        {
            _view.SkillButton.SetOnClick(() => 
            {
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.SingleModeSkillLearning);
            });
        }
        
        /// <summary>
        /// キャラモデル表示
        /// </summary>
        private void SetupModel()
        {
            var singleModeScene = SceneManager.Instance.GetCurrentSceneController() as SingleModeSceneController;
            if (singleModeScene == null) return;
            
            //カメラ設定
            singleModeScene.SetActiveScene(true);
            singleModeScene.SetCameraEnable(true);
            singleModeScene.FocusCamera.EnableBackgroundTextureBlitFromUI(false);
            
            var model = new SingleModeScenarioLegendScenarioRace3DModel();
            model.Setup(model.LoadCharaParam(), model.CreateCharaBuildInfos());
            _modelController.Setup(_view.Top3DContentsView, model);
            _modelController.SetCharacterActive(true);

            _view.CharaMessage.SetModel(_modelController.GetModel(0), false);
        }

        /// <summary>
        /// ボイスエフェクトON/OFF（トンネル風）
        /// </summary>
        private void SetAudioBusParam(bool isEnable)
        {
            AudioManager.Instance.SetBusParam(AudioManager.BUS_NAME_STORY_BG_0054_TUNNEL, isEnable);
        }

        /// <summary>
        /// 環境音とボイスエフェクトOFF
        /// </summary>
        private void StopEnvironmentSeAndBusParam()
        {
            SingleModeUtils.StopEnvironmentSe();//環境音停止
            SetAudioBusParam(false);//ボイスエフェクトOFF
        }
        
        #endregion

        /// <summary>
        /// ライバル演出の破棄
        /// </summary>
        private void DestroyRivalEntryAnimation()
        {
            if (_partsRivalEntry == null) return;
            // ライバル演出の破棄
            _partsRivalEntry.DestroyCutIn();
            GameObject.Destroy(_partsRivalEntry.gameObject);
            _partsRivalEntry = null;
        }

        private void OnClickRaceButton()
        {
            DialogSingleModeScenarioRaceEntry.PushDialog(() =>
            {
                // 出走API
                SingleModeLegendAPI.SendLegendScenarioRaceEntry(() =>
                {
                    if (Model.IsNeedPlayRivalEntryAnimation)
                    {
                        // ライバル演出中はバックキー無効
                        _isEnableBackKey = false;
                        _view.CharaMessage.SetEnable(false);//待機セリフ喋らないように
                        
                        // BGMをフェードで止める
                        AudioManager.Instance.StopBgm(SingleModeScenarioLegendScenarioRaceTopViewModel.RIVAL_ENTRY_BGM_STOP_FADE_TIME);
                        
                        // キャラのセリフも止める
                        _view.CharaMessage.Stop();
                    
                        // アーモンドアイ登場演出へ
                        _partsRivalEntry = SingleModeScenarioLegendScenarioRaceRivalCutt.CreateAndPlay(
                            onCompleteFadeIn:() =>
                            {
                                // View側表示物OFF
                                _modelController.SetCharacterActive(false);
                                _modelController.SetBg3dActive(false);
                                GetSceneController<SingleModeSceneController>()?.SetCameraEnable(false);
                                _view.SetActiveWithCheck(false);
                                UIManager.SingleModeHeader.SetActiveWithCheck(false);
                                UIManager.SingleModeHeader.SetInfoButtonActive(false);
                                UIManager.SingleModeFooter.SetActiveWithCheck(false);
                                StopEnvironmentSeAndBusParam();//環境音とボイスエフェクトOFF
                            }, 
                            onComplete:SingleModeChangeViewManager.Instance.ChangeViewScenarioRacePaddock);
                    }
                    else
                    {
                        SingleModeChangeViewManager.Instance.ChangeViewScenarioRacePaddock();
                    }
                });
            });
        }
        
    #if CYG_DEBUG

        public void DebugApplyRaceButton(SingleModeScenarioLegendScenarioRaceTopViewModel model, int charaId, int dressId)
        {
            SingleModeUtils.SetupButtonCharaPetit(charaId, dressId, _view.RaceButton, model.RaceButtonCategoryOff, model.RaceButtonCategoryOn);
        }
    #endif
   }
}