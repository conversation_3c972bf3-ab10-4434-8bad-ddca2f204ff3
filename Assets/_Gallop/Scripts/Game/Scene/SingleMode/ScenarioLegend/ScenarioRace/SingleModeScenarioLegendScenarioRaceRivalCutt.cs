using System;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;
using DG.Tweening;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 伝説編：アーモンドアイ登場演出
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeScenarioLegendScenarioRaceRivalCutt : MonoBehaviour
    {
        public class SingleModeScenarioLegendScenarioRaceRivalCuttProxy
        {
            private readonly SingleModeScenarioLegendScenarioRaceRivalCutt _proxy;
            public SingleModeScenarioLegendScenarioRaceRivalCuttProxy(SingleModeScenarioLegendScenarioRaceRivalCutt proxy)
            {
                _proxy = proxy;
            }

            public bool TryTapButton()
            {
                if (_proxy._tapButton.IsActive())
                {
                    _proxy._tapButton.onClick?.Invoke();
                    return true;
                }
                return false;
            }
        }
        [SerializeField] private RectTransform _contentsRoot;
        [SerializeField] private ButtonCommon _skipButton;
        [SerializeField] private ButtonCommon _tapButton;
        [SerializeField] private GameObject _tapRoot;

        private SingleModeScenarioLegendRivalCutInHelper _cutInHelper;
        
        private SingleModeScenarioLegendScenarioRaceRivalCuttProxy _autoPlayProxy;
        public SingleModeScenarioLegendScenarioRaceRivalCuttProxy AutoPlayProxy => 
            _autoPlayProxy ??= new SingleModeScenarioLegendScenarioRaceRivalCuttProxy(this);
#if CYG_DEBUG
        public SingleModeScenarioLegendRivalCutInHelper DebugCuttInHelper => _cutInHelper;
#endif
        private Action _onComplete;
        private Cute.Cri.AudioPlayback _audioPlayback;
        private FlashActionPlayer _wipePlayer;
        private static string CUTT_PATH => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_SPECIAL_RIVAL_CUTT_PATH;
        private static string WIPE_PATH => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_EXHIBITION_CUTIN00;
        
        /// <summary>
        /// DL登録
        /// </summary>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            register.RegisterPathWithoutInfo(CUTT_PATH);
            register.RegisterPathWithoutInfo(WIPE_PATH);
            // SE
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.SFX_ADDON10_LEGEND_RIVAL_CUT_01);
            // BGM
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.BGM_SINGLE_MODE_SCENARIO_LEGEND_RIVAL_CUT_IN);
        }

        /// <summary>
        /// 生成してカットインを再生する
        /// </summary>
        public static SingleModeScenarioLegendScenarioRaceRivalCutt CreateAndPlay(Action onCompleteFadeIn, Action onComplete)
        {
            const string PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_UI_ROOT + "SingleModeScenarioLegendScenarioRaceRivalCutt";
            var obj = Instantiate(ResourceManager.LoadOnView<GameObject>(PATH), UIManager.MainCanvas.transform);
            var parts = obj.GetComponent<SingleModeScenarioLegendScenarioRaceRivalCutt>();
            parts.Setup();
            
            UIManager.Instance.LockGameCanvas();
            parts.PlayInWipe(() =>
            {
                onCompleteFadeIn?.Invoke();
                parts.Play(onComplete);
            });
            return parts;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup()
        {
            UIManager.Instance.AdjustContentsRootRect(_contentsRoot);//Viewとは別でTAPを出すのでセーフエリア対応
            _tapRoot.SetActiveWithCheck(false);
            _skipButton.SetActiveWithCheck(false);
            _skipButton.SetOnClick(OnClickSkip);
            _tapButton.SetOnClick(OnClickTap);
            SetupWipe();
        }

        private void SetupWipe()
        {
            _wipePlayer = FlashActionPlayer.Load(WIPE_PATH, transform);
            _wipePlayer.LoadFlashPlayer();

            //ヘッダーフッターより上
            if(UIManager.SingleModeFooter == null || UIManager.SingleModeHeader == null) return;
            var sortingOrder = Mathf.Max(UIManager.SingleModeFooter.GetSortingOrder(), UIManager.SingleModeHeader.GetSortingOrder());
            _wipePlayer.SetSortOffset(sortingOrder + 100);//ヘッダー構造もFlashやiボタンなどである程度階層があるので、+100くらいしておく
        }
        private void PlayInWipe(Action onPlayCut)
        {
            _wipePlayer.Play(GameDefine.A2U_IN_LABEL);
            _wipePlayer.FlashPlayer.AddActionCallBack("in_cutin", _ => onPlayCut?.Invoke(), AnMotionActionTypes.Start);
            
            // SE (ワイプに合わせたSEとカットに合わせたSE 両方が一つのキューに入っている)
            _audioPlayback = AudioManager.Instance.PlaySe(AudioId.SFX_ADDON10_LEGEND_RIVAL_CUT_01);
        }
        
        /// <summary>
        /// 終了時の白フェード
        /// </summary>
        private void FadeInLast(Action onFinish)
        {
            const float FADE_TIME = 0.4f;
            FadeManager.Instance.FadeOut(GameDefine.COLOR_WHITE, FADE_TIME, onFinish, easeType: Ease.OutQuad, fadeCanvasType:FadeManager.FadeCanvasType.SystemCanvas);
        }
        
        public void Play(Action onComplete)
        {
            _onComplete = onComplete;
            _cutInHelper = new SingleModeScenarioLegendRivalCutInHelper();
            _cutInHelper.Init();
            _cutInHelper.Setup(SetTapOn);
            _cutInHelper.Play(CUTT_PATH, SceneManager.Instance.GetCurrentSceneController().GetSceneTransform());
            
            AudioManager.Instance.PlayBgm(AudioId.BGM_SINGLE_MODE_SCENARIO_LEGEND_RIVAL_CUT_IN);
            
            // 遷移直後の入力可能までの時間を設定
            DOVirtual.DelayedCall(GameDefine.COMMON_TAP_WAIT_400MS, () =>
            {
                UIManager.Instance.UnlockGameCanvas();
                _skipButton.SetActiveWithCheck(true); //SKIP可能
            });
        }

        /// <summary>
        /// TAP表示を出す
        /// </summary>
        private void SetTapOn()
        {
            _skipButton.SetActiveWithCheck(false);
            _tapRoot.SetActiveWithCheck(true);//TAP可能
        }

        public void DestroyCutIn()
        {
            if (_cutInHelper != null)
            {
                _cutInHelper.CleanUp();
                _cutInHelper.CleanupPlaying();
                _cutInHelper = null;
            }
        }

        /// <summary>
        /// SKIPボタン押下時
        /// </summary>
        private void OnClickSkip()
        {
            _cutInHelper.SkipRuntime();//カットをスキップさせる
            AudioManager.Instance.StopSe(_audioPlayback);//SE停止
            AudioManager.Instance.StopBgm(); //BGM停止
            SetTapOn();//TAP表示
        }

        /// <summary>
        /// TAPボタン押下時
        /// </summary>
        private void OnClickTap()
        {
            _tapRoot.SetActiveWithCheck(false);
            FadeInLast(() => _onComplete?.Invoke());//フェード後に完了
        }
        
        /// <summary>
        /// 更新
        /// NOTE:凱旋門賞のライバルカット同様に、再生終了したらUpdate呼ばない。Update呼ばないことで最終フレームの絵で固定化されずパーティクルなどが動作する
        /// </summary>
        public void UpdateView()
        {
            if (_cutInHelper == null || !_cutInHelper.IsPlaying()) return;
            _cutInHelper.AlterUpdate();
        }

        /// <summary>
        /// 更新
        /// </summary>
        public void LateUpdateView()
        {
            if (_cutInHelper == null || !_cutInHelper.IsPlaying()) return;
            _cutInHelper.AlterLateUpdate();
        }
    }
}