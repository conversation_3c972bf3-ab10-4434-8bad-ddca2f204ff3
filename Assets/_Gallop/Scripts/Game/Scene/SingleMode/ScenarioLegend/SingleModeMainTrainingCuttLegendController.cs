using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using DG.Tweening;
using Gallop.SingleMode.ScenarioLegend;
using UnityEngine;

namespace Gallop
{
    using static SingleModeMainTrainingCuttController;
    using static SingleModeScenarioLegendUtils;

    #region CuttMemberInfoCreator
    
    /// <summary>
    /// 伝説編：トレーニングカット再生に表示するメンバー算出ロジック
    /// </summary>
    public class SingleModeMainTrainingCuttMemberInfoCreatorScenarioLegend : SingleModeMainTrainingCuttMemberInfoCreator
    {
        protected override List<int> GetGroupSupportCardCharaIdList(MasterSupportCardData.SupportCardData supportCardData)
        {
            // 3レジェンドのグルサポのみ専用処理をいれる
            if (IsLegendGroupSupportCard(supportCardData))
            {
                var backupService = SingleModeMainServiceLocator.Instance.Resolve<SingleModeMainTrainingDataBackupService>();
                var backupData = backupService?.GetBackupData<TrainingBackupDataScenarioLegend>();
                
                return backupData != null 
                    // トレーニングに設定されたレジェンドを表示させる
                    ? new List<int> { backupData.LegendId } 
                    // 情報がなければ通常処理へ
                    : base.GetGroupSupportCardCharaIdList(supportCardData);
            }

            return base.GetGroupSupportCardCharaIdList(supportCardData);
        }
    }

    #endregion

    #region backupData
    
    public class TrainingBackupDataScenarioLegend : TrainingBackupDataDefault
    {
        /// <summary> トレーニングに配置されている3レジェンドのId </summary>
        public int LegendId { get; }

        public TrainingBackupDataScenarioLegend(WorkSingleModeData.TurnInfo turnInfo) : base(turnInfo)
        {
            // 該当練習に配置されているレジェンドタイプの取得
            var trainingModel = TrainingModelRepository.Get(turnInfo.CommandId);
            LegendId = trainingModel.LegendType.ToLegendId();
        }
    }

    #endregion
    
    /// <summary>
    /// <see cref="SingleModeMainTrainingCuttController"/>
    /// </summary>
    public partial class SingleModeMainTrainingCuttController
    {
        /// <summary>
        /// 伝説編
        /// 専用トレーニングカット管理
        /// </summary>
        private class TrainingCuttLegendController : AbstractTrainingCuttScenarioController
        {
            private PartsSingleModeScenarioLegendMainView _legendMainView;
            private PartsSingleModeScenarioLegendMainView LegendMainView
            {
                get
                {
                    if (_legendMainView == null)
                    {
                        _legendMainView = SingleModeMainServiceLocator.Instance.ResolveIfNeeded<ISingleModeScenarioMainViewAdditivePartsBase>() as PartsSingleModeScenarioLegendMainView;
                    }
                    return _legendMainView;
                }
            }
            
            /// <summary> シナリオ専用トレーニングカット有効化 </summary>
            public override bool NeedsTrainingAfterEffect() => true;
            
            /// <summary>
            /// 通信前バックアップデータ作成
            /// </summary>
            public override ITrainingBackupData ExecuteBackupBeforeSendAPI(WorkSingleModeData.TurnInfo turnInfo)
                => new TrainingBackupDataScenarioLegend(turnInfo);
            
            #region パラメータ上昇メッセージ
            
            public void AddMotivationMessage(List<SingleModeMainData.MessageWindowText> messageList)
            {
                var changeParameterInfo = WorkDataManager.Instance.SingleMode.ChangeParameterInfo;
                var motivationChangeInfoOnBuff = GetMotivationChangedInfoOnBuffActivation(changeParameterInfo);
                
                // やる気(バフ発動による変動分を差し引いて計算)
                var motivationDelta = changeParameterInfo.Motivation - motivationChangeInfoOnBuff.ChangedValue;
                if (motivationDelta > 0)
                {
                    messageList.Add(SingleModeMainData.MessageWindowText.CreatePositive(TextId.SingleMode0077.Text()));
                }
                else if (motivationDelta < 0)
                {
                    messageList.Add(SingleModeMainData.MessageWindowText.CreateNegative(TextId.SingleMode0076.Text()));
                }
            
                // やる気上限
                if (changeParameterInfo.ExistLimitStatusType(SingleModeDefine.ParameterGainLimitType.Motivation))
                {
                    // 超絶好調発動時は文言を変える。トレーニングで超絶好調状態になったり解除されることはないため、最新の通信データを見ての判定で良い
                    MasterlyBonusModelRepository.TryGet(out var masterlyBonusModel);
                    var message = masterlyBonusModel.IsSuperMaxMotivation()
                        ? TextId.SingleModeScenarioLegend508080.Text()
                        : SingleModeUtils.GetTextWindowMessageLimitMotivation();

                    messageList.Add(SingleModeMainData.MessageWindowText.CreatePositive(message));
                }
            }

            /// <summary>
            /// 親友ゲージ上昇・レベルアップメッセージの追加
            /// </summary>
            public void AddFriendGaugeMessage(List<SingleModeMainData.MessageWindowText> messageList)
            {
                var paramChange = WorkDataManager.Instance.SingleMode.ChangeParameterInfo;
                MasterlyBonusModelRepository.TryGetBackup(out var prevMasterlyBonusModel);
                MasterlyBonusModelRepository.TryGet(out var currentMasterlyBonusModel);
                
                // 親友ゲージ変動
                if (prevMasterlyBonusModel is MasterlyBonus9048Model prev9048Model && currentMasterlyBonusModel is MasterlyBonus9048Model current9048Model)
                {
                    messageList.AddRange(GetFriendGaugeUpMessages(prev9048Model, current9048Model, paramChange));
                }
            }
            
            /// <summary>
            /// バフゲージ上昇メッセージ取得
            /// </summary>
            private SingleModeMainData.MessageWindowText[] GetBuffGaugeUpMessages(IBuffGaugeModel prevGaugeModel, IBuffGaugeModel currentGaugeModel, WorkSingleModeChangeParameterInfo paramChange)
            {
                var gaugeNotUpLegendIds = paramChange.ScenarioLegend.BuffGaugeNotUpLegendIds;
                // TrainingParamChangeA2Uと同じロジックで演出を作成し、ログメッセージを抽出
                return SingleModeScenarioLegendGainedBuffGaugeParameterInfo
                    .CreateList(prevGaugeModel, currentGaugeModel, gaugeNotUpLegendIds)
                    .Select(info => SingleModeMainData.MessageWindowText.CreatePositive(info.MessageText, GetScenarioMessagePlayTime()))
                    .ToArray();
            }
            
            /// <summary>
            /// バフ発動メッセージ取得
            /// </summary>
            private SingleModeMainData.MessageWindowText[] GetBuffListGainMessage(IBuffListModel prevModel, IBuffListModel currentModel, WorkSingleModeChangeParameterInfo paramChange)
            {
                if (paramChange.ScenarioLegend.ActivatedBuffIds.Any() == false) return Array.Empty<SingleModeMainData.MessageWindowText>().ToArray();
                
                // TrainingParamChangeA2Uと同じロジックで演出を作成し、ログメッセージを抽出
                var motivationChangeInfo = GetMotivationChangedInfoOnBuffActivation(paramChange);
                var destMotivation = WorkDataManager.Instance.SingleMode.Character.Motivation;
                
                return SingleModeScenarioLegendProgressedBuffParameterInfo.CreateList(prevModel, currentModel, paramChange.ScenarioLegend.ActivatedBuffIds, motivationChangeInfo, destMotivation)
                    .Select(info => SingleModeMainData.MessageWindowText.CreatePositive(info.MessageText, info.CreateA2UContext().PlayTime))
                    .ToArray();
            }
            
            /// <summary>
            /// マスタリーボーナス関連の進捗
            /// </summary>
            private SingleModeMainData.MessageWindowText GetMasterlyBonusProgressMessage(IMasterlyBonusModel prevModel, IMasterlyBonusModel currentModel, WorkSingleModeChangeParameterInfo paramChange, bool isOuting)
            {
                // 超絶好調変動
                if (prevModel is MasterlyBonus9046Model prev9046Model && currentModel is MasterlyBonus9046Model current9046Model)
                {
                    return GetSuperMaxMotivationProgressMessage(prev9046Model, current9046Model, paramChange);
                }
                
                // スポ根ゾーン変動
                if (prevModel is MasterlyBonus9047Model prev9047Model && currentModel is MasterlyBonus9047Model current9047Model)
                {
                    return GetZoneConditionProgressMessage(prev9047Model, current9047Model, paramChange, isOuting);
                }

                return null;
            }
            
            /// <summary>
            /// 超絶好調　進捗・延長メッセージ取得
            /// </summary>
            private SingleModeMainData.MessageWindowText GetSuperMaxMotivationProgressMessage(MasterlyBonus9046Model prevModel, MasterlyBonus9046Model currentModel, WorkSingleModeChangeParameterInfo paramChange)
            {
                // 超絶好調発動進捗
                if (MasterlyBonus9046Model.IsProgressed9046(prevModel, currentModel))
                {
                    var isAchieveRequiredCount = currentModel.EffectActivationCount >= currentModel.EffectActivationRequiredCount;
                    var messageText = isAchieveRequiredCount
                        ? TextId.SingleModeScenarioLegend508083.Text()
                        : TextId.SingleModeScenarioLegend508082.Text();
                    return SingleModeMainData.MessageWindowText.CreatePositive(messageText, GetScenarioMessagePlayTime());
                }
                
                // 超絶好調延長
                var extendedCount = currentModel.IsSuperMaxMotivation() ? prevModel.ConditionExtensionRemainCount - currentModel.ConditionExtensionRemainCount : 0;
                if (extendedCount > 0)
                {
                    var info = new SingleModeScenarioLegendSuperMaxMotivationExtensionParameterInfo(prevModel, currentModel, extendedCount);
                    return SingleModeMainData.MessageWindowText.CreatePositive(info.MessageText, GetScenarioMessagePlayTime());
                }

                // 延長ターンは最大だ
                if (paramChange.ScenarioLegend.NotExtendSuperMaxMotivationRemainCount)
                {
                    return SingleModeMainData.MessageWindowText.CreatePositive(TextId.SingleModeScenarioLegend508078.Text(), GetScenarioMessagePlayTime());
                }

                return null;
            }
            
            /// <summary>
            /// スポ根ゾーン 進捗・継続メッセージ取得
            /// </summary>
            private SingleModeMainData.MessageWindowText GetZoneConditionProgressMessage(MasterlyBonus9047Model prevModel, MasterlyBonus9047Model currentModel, WorkSingleModeChangeParameterInfo paramChange, bool isOuting)
            {
                // スポ根ゾーン発動進捗
                var isProgress = !currentModel.IsZoneCondition && prevModel.ZoneActivationCount < currentModel.ZoneActivationCount;
                if (isProgress)
                {
                    var isAchieveRequiredCount = currentModel.ZoneActivationCount >= currentModel.ZoneActivationRequiredCount;
                    var messageText = isAchieveRequiredCount
                        ? TextId.SingleModeScenarioLegend508085.Text()
                        : TextId.SingleModeScenarioLegend508084.Text();
                    return SingleModeMainData.MessageWindowText.CreatePositive(messageText, GetScenarioMessagePlayTime());
                }
                
                // スポ根ゾーンが継続したかどうか. 継続中かつカウントが進んでいれば継続
                var isContinue = currentModel.IsZoneCondition && prevModel.ZoneContinuationTurnCount < currentModel.ZoneContinuationTurnCount;
                if (isContinue)
                {
                    return SingleModeMainData.MessageWindowText.CreatePositive(TextId.SingleModeScenarioLegend508086.Text(), GetScenarioMessagePlayTime());
                }
                
                // スポ根ゾーンの終了率に当選したかどうか. 継続中かつカウントが進んでいなければ終了
                var willEndZone = paramChange.ScenarioLegend.IsTransitionedZoneEndWaiting || (currentModel.IsZoneCondition && prevModel.ZoneContinuationTurnCount == currentModel.ZoneContinuationTurnCount);
                if (willEndZone)
                {
                    // トレーニングとお出かけで文言を分ける
                    var charaName = MasterDataUtil.GetCharaNameByCharaId(WorkDataManager.Instance.SingleMode.Character.CharaId);
                    var text = isOuting ? TextId.SingleModeScenarioLegend508089.Text() : TextId.SingleModeScenarioLegend508088.Format(charaName);
                    return SingleModeMainData.MessageWindowText.CreatePositive(text, GetScenarioMessagePlayTime());
                }
                
                return null;
            }
            
            /// <summary>
            /// 親友ゲージ・親友レベル上昇メッセージ取得
            /// </summary>
            private SingleModeMainData.MessageWindowText[] GetFriendGaugeUpMessages(MasterlyBonus9048Model prevModel, MasterlyBonus9048Model currentModel, WorkSingleModeChangeParameterInfo paramChange)
            {
                var gaugeNotUpCharaIds = paramChange.ScenarioLegend.FriendGaugeNotUpPartnerIds.Select(SingleModeUtils.GetCharaIdByAllPosition).ToList();
                var gaugeNotUpAtRandomCharaIds = paramChange.ScenarioLegend.FriendGaugeNotUpAtRandomPartnerIds.Select(SingleModeUtils.GetCharaIdByAllPosition).ToList();
                var gaugeNotDownCharaIds = paramChange.ScenarioLegend.FriendGaugeNotDownPartnerIds.Select(SingleModeUtils.GetCharaIdByAllPosition).ToList();
                var levelNotUpCharaIds = paramChange.ScenarioLegend.FriendLevelNotUpPartnerIds.Select(SingleModeUtils.GetCharaIdByAllPosition).ToList();
                var allFriendGaugeGain = paramChange.ScenarioLegend.AllFriendGaugeGainInfo;
                // SingleModeScenarioLegendGainedFriendGaugeInfo.CreateListと同じロジックで演出を作成し、ログメッセージを抽出
                return SingleModeScenarioLegendGainedFriendGaugeInfo
                    .CreateList(prevModel, currentModel, gaugeNotUpCharaIds, gaugeNotUpAtRandomCharaIds, gaugeNotDownCharaIds, levelNotUpCharaIds, allFriendGaugeGain)
                    .Select(info => SingleModeMainData.MessageWindowText.CreatePositive(info.MessageText, GetScenarioMessagePlayTime()))
                    .ToArray();
            }

            private float GetScenarioMessagePlayTime()
            {
                // 倍速時は即座に表示したい(アニメーション側のテンポ感に合わせる)
                return StoryManager.IsHighSpeedMode ? 0f : SingleModeMainData.MessageWindowText.DEFAULT_TYPEWRITE_INTERVAL;
            }
            
            #endregion
            
            public override IEnumerator PlayTrainingCutt(CuttPlayInfo playInfo)
            {
                var paramChange = WorkDataManager.Instance.SingleMode.ChangeParameterInfo;
                
                var prevGaugeModel = GaugeModelRepository.GetBackupData();
                var currentGaugeModel = GaugeModelRepository.Get();
                var prevBuffListModel = BuffListModelRepository.GetBackupData();
                var currentBuffListModel = BuffListModelRepository.Get();
                MasterlyBonusModelRepository.TryGetBackup(out var prevMasterlyBonusModel);
                MasterlyBonusModelRepository.TryGet(out var currentMasterlyBonusModel);
                
                var motivationChangeInfo = GetMotivationChangedInfoOnBuffActivation(paramChange);
                
                yield return new WaitWhile(() => MessageController.IsTypeWriting); 

                // 伝説編ではお出かけ時にシナリオギミックの進捗を見せるケースがある. その場合はイリ再生
                var isOuting = playInfo.CommandType is SingleModeDefine.CommandType.Outing;
                var existActivatedBuff = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.ActivatedBuffIds.Any();
                var exist9046Anim = MasterlyBonus9046Model.IsProgressed9046(prevMasterlyBonusModel, currentMasterlyBonusModel) || MasterlyBonus9046Model.IsExtended9046(prevMasterlyBonusModel, currentMasterlyBonusModel);
                var existMainViewAnime = existActivatedBuff || exist9046Anim;
                if (isOuting && existMainViewAnime && LegendMainView.gameObject.activeSelf == false)
                {
                    LegendMainView.SetActiveWithCheck(true);
                    var isCompletePlayIn = false;
                    LegendMainView.PlayIn(() => isCompletePlayIn = true);
                    yield return new WaitUntil(() => isCompletePlayIn);
                    
                    // イリさせた場合は演出初めまでウエイトを設ける
                    yield return new WaitForSeconds(GameDefine.BASE_FPS_TIME * 4);
                }
                
                // バフゲージ上昇演出
                var isCompleteBuffGaugeUp = false;
                PlayBuffGaugeUp(prevGaugeModel, currentGaugeModel, () => isCompleteBuffGaugeUp = true);
                yield return _owner.ShowMassageWindowTextCoroutine(GetBuffGaugeUpMessages(prevGaugeModel, currentGaugeModel, paramChange));
                if (isCompleteBuffGaugeUp == false)
                {
                    yield return new WaitUntil(() => isCompleteBuffGaugeUp);
                }

                // バフ進捗・発動演出
                var isCompleteBuffListGain = false;
                var activatedBuffIds = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.ActivatedBuffIds;
                if (activatedBuffIds.Any())
                {
                    var isEnablePlayMasterlyBonus = false; // マスタリーボーナス獲得演出を再生開始してよいか
                    PlayBuffListGain(prevBuffListModel, currentBuffListModel, activatedBuffIds, () => isCompleteBuffListGain = true);

                    // バフ発動→マスタリ進捗と続く場合、バフ発動のアニメーションが終わり切る前にマスタリ進捗演出を始めたい. 6f後 or バフ発動のログ表示後
                    DOVirtual.DelayedCall(GameDefine.BASE_FPS_TIME * 6f, () => isEnablePlayMasterlyBonus = true);
                    
                    if (motivationChangeInfo.ExistMotivationGain)
                    {
                        // バフ発動によるやる気変動がある場合はこのタイミングで処理
                        _owner._headerAndFooterController.MotivationButton.PlayUp(WorkDataManager.Instance.SingleMode.Character.Motivation);
                    }
                    yield return _owner.ShowMassageWindowTextCoroutine(GetBuffListGainMessage(prevBuffListModel, currentBuffListModel, paramChange));
                
                    // マスタリーボーナス獲得演出が再生可能になるまで待つ
                    if (isEnablePlayMasterlyBonus == false)
                    {
                        yield return new WaitUntil(() => isEnablePlayMasterlyBonus);
                    }
                }
                else
                {
                    isCompleteBuffListGain = true;
                }
                
                // マスタリーボーナス進捗演出
                var isCompleteMasterlyBonusProgress = false;
                PlayMasterlyBonusProgress(prevMasterlyBonusModel, currentMasterlyBonusModel, () => isCompleteMasterlyBonusProgress = true);
                yield return _owner.ShowMassageWindowTextCoroutine(GetMasterlyBonusProgressMessage(prevMasterlyBonusModel, currentMasterlyBonusModel, paramChange, isOuting));
                if(isCompleteMasterlyBonusProgress == false)
                {
                    yield return new WaitUntil(() => isCompleteMasterlyBonusProgress);
                }
                
                // バフ発動演出が終わっていなければ待つ
                if (isCompleteBuffListGain == false)
                {
                    yield return new WaitUntil(() => isCompleteBuffListGain);
                }

                // 親友ゲージのアニメーションが終わっていなかった場合は待つ (お出かけ時はListがnullなのでチェック)
                if (playInfo.TrainingHorseList != null)
                {
                    var trainingHorseIconA2UList = playInfo.TrainingHorseList
                        .Select(trainingHorse => TrainingView.TrainingHorse.GetHorseIcon(trainingHorse.PositionId))
                        .Select(trainingHorseIcon => trainingHorseIcon.PlayerBase as SingleModeMainViewTrainingHorseIconA2UScenarioLegend)
                        .Where(trainingHorsePlayer => trainingHorsePlayer != null).ToList();
                    bool IsCompleteFriendGaugeUp() => trainingHorseIconA2UList.All(trainingHorsePlayer => trainingHorsePlayer.IsPlayingScenarioContentGaugeUp() == false);
                    if (IsCompleteFriendGaugeUp() == false)
                    {
                        yield return new WaitUntil(IsCompleteFriendGaugeUp);
                    }
                }

                // ウエイト
                yield return new WaitForSeconds(GameDefine.BASE_FPS_TIME * 3);

                // この後のタップ待ちは不要なので消す
                MessageController.HideMessageWindow();
            }
            
            private void PlayBuffGaugeUp(IBuffGaugeModel prevGaugeModel, IBuffGaugeModel currentGaugeModel, System.Action onComplete)
            {
                LegendMainView.PlayBuffGaugeUp(prevGaugeModel, currentGaugeModel, onComplete);
            }
            
            private void PlayBuffListGain(IBuffListModel prevModel, IBuffListModel currentModel, IReadOnlyList<int> activatedBuffIds, System.Action onComplete)
            {
                LegendMainView.PlayBuffListGainAnimation(prevModel, currentModel, activatedBuffIds, onComplete);
            }
            
            private void PlayMasterlyBonusProgress(IMasterlyBonusModel prevModel, IMasterlyBonusModel currentModel, System.Action onComplete)
            {
                if (prevModel is MasterlyBonus9046Model prev9046Model && currentModel is MasterlyBonus9046Model current9046Model)
                {
                    LegendMainView.MasterlyBonus.PlayProgressOrExtended(prev9046Model, current9046Model, onComplete);
                }
                else if (prevModel is MasterlyBonus9047Model prev9047Model && currentModel is MasterlyBonus9047Model current9047Model)
                {
                    LegendMainView.MasterlyBonus.PlayProgress(prev9047Model, current9047Model, onComplete);
                }
                else
                {
                    onComplete?.Invoke();
                }
            }
            
            /// <summary>
            /// ワイプが再生されるタイミング
            /// </summary>
            public override void OnPlayTrainingCutEndAsync()
            {
                if (LegendMainView == null) return;
                
                if (StoryManager.IsHighSpeedMode)
                {
                    LegendMainView.SetActive(false); //倍速時は即座にOFF
                }
                else
                {
                    LegendMainView.PlayOut();
                }
            }
            
            /// <summary>
            /// サボリトレーニング開始時のワイプが完了したタイミング
            /// シナリオ固有のUIを非表示にしたい場合はこちらに追加処理
            /// </summary>
            public override void OnEndWipeSaboriStart()
            {
                if (LegendMainView == null) return;
                LegendMainView.SetActive(false);
            }
        }
    }
}
