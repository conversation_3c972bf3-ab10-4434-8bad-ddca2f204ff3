using Gallop.SingleMode.ScenarioLegend;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 伝説編: トレーニング併せ馬アイコンのモデル
    /// </summary>
    public class SingleModeMainViewTrainingHorseIconModelScenarioLegend : SingleModeMainViewTrainingHorseIconModel
    {
        private bool TryGetFriendData(out MasterlyBonus9048Model.FriendDataModel friendData)
        {
            friendData = null;
            
            if (MasterlyBonusModelRepository.TryGet<MasterlyBonus9048Model>(out var masterlyBonusModel) == false)
                return false;
                
            masterlyBonusModel.TryGetFriendDataByCharaId(TrainingHorse.GetCharaId(), out friendData); 
            return friendData != null;
        }
        
        /// <summary>
        /// 伝説編のサポートカードId
        /// 伝説編で加入した親友ゲスト以外はDefaultを返す
        /// </summary>
        private int SupportCardIdLegend
            => TryGetFriendData(out var friendData) && friendData.IsGuest ? friendData.SupportCardId : SupportCardIdDefault;
        
        /// <summary>
        /// サポートカードIdを取得
        /// ユニークキャラ(理事長など)はサポートカード詳細を表示しない想定のため、0を返す
        /// </summary>
        protected override int SupportCardId => IsUniqChara ? 0 : SupportCardIdLegend;

        /// <summary>
        /// 派生先でクリックした時のシナリオ固有の挙動を追加
        /// </summary>
        public override bool OnClick(int positionId)
        {
            // 親友ゲージ情報が取得できない場合は通常フローで開く
            if (TryGetFriendData(out var friendData) == false)
                return false;

            if (friendData.IsGuest)
            {
                // 協力者(野良)
                var model = new DialogSingleModeScenarioLegendGuestCharacterCardDetailModel(friendData); 
                DialogSingleModeScenarioLegendGuestCharacterCardDetail.PushDialog(model);
                return true;
            }
            else
            {
                // 協力者兼編成サポートカード
                DialogSingleModeScenarioLegendSupportCardDetail.PushDialog(_equipSupportCard, friendData);
                return true;
            }
        }

        /// <summary>
        /// バフによるヒント発生エフェクトがアクティブかどうか
        /// </summary>
        public bool IsActiveHintEffectByBuff()
        {
            var buffListModel = BuffListModelRepository.Get(); 
            var hasHintEffectBuff = buffListModel.BuffItems.Any(x => x.IsEffectActive && x.IsGainHintCountEffect);

            return TrainingHorse.IsTips && hasHintEffectBuff;
        }
    }
}