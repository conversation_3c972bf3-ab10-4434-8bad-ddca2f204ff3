namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;
    
    /// <summary>
    /// 伝説編：バフゲージの1要素
    /// </summary>
    public interface IBuffGaugeItemModel
    {
        public LegendType LegendType { get; }
        public int Count { get; }
    }
    
    public class BuffGaugeItemModel : IBuffGaugeItemModel
    {
        public BuffGaugeItemModel(WorkSingleModeScenarioLegend.LegendGaugeData gaugeData)
        {
            _gaugeData = gaugeData;
        }

        private readonly WorkSingleModeScenarioLegend.LegendGaugeData _gaugeData;

        public LegendType LegendType => SingleModeScenarioLegendUtils.LegendIdToLegendType(_gaugeData.LegendId);
        public int Count => _gaugeData.Count;
    }
}
