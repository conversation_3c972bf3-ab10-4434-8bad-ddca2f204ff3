using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;
    /// <summary>
    /// 伝説編：バフゲージ
    /// </summary>
    public interface IBuffGaugeModel
    {
        public bool IsGaugeMax(LegendType legendType);
        public int GetGaugeCountByLegendType(LegendType legendType);
    }
    
    public class BuffGaugeModel : IBuffGaugeModel
    {
        public BuffGaugeModel(IReadOnlyCollection<WorkSingleModeScenarioLegend.LegendGaugeData> gaugeDataArray)
        {
            _items = gaugeDataArray.Select(data => new BuffGaugeItemModel(data)).ToArray();
        }

        private readonly BuffGaugeItemModel[] _items;
        
        #region Implement

        public bool IsGaugeMax(LegendType legendType) => GetGaugeCountByLegendType(legendType) >= BUFF_GAUGE_LIMIT;

        public int GetGaugeCountByLegendType(LegendType legendType) => _items.FirstOrDefault(x => x.LegendType == legendType)?.Count ?? 0;

        #endregion
    }
}
