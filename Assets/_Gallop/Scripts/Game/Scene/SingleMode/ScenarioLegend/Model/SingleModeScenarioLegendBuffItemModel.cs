using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;
    
    /// <summary>
    /// 伝説編：バフリストに格納されるバフアイテム
    /// </summary>
    public interface IBuffItemModel
    {
        public int BuffId { get; }
        public int IconId { get; }
        public int BuffRank { get; }
        public string Name { get; }
        
        public LegendType LegendType { get; }
        
        public bool IsEffectActive { get; }
        public int EffectCoolTime { get; }
        
        /// <summary> 効果が常時発動するバフか </summary>
        public bool IsAlwaysEffectActive { get; }
        public int ObtainedTurn { get; }
        
        /// <summary> やる気の値が指定値のときに発動するバフか </summary>
        public bool IsMotivationCondition { get; }
        
        /// <summary> 効果発動時にやる気が上昇するバフか </summary>
        public bool IsGainMotivationOnActivate { get; }
        
        /// <summary> ヒント獲得数を増やすバフか </summary>
        public bool IsGainHintCountEffect { get; }
    }
    
    public class BuffItemModel : IBuffItemModel
    {
        private BuffItemModel(WorkSingleModeScenarioLegend.LegendBuffDataInfo buffDataInfo)
            : this(buffDataInfo.BuffId, buffDataInfo.IsActive != 0, buffDataInfo.CoolTime, buffDataInfo.ObtainedTurn){}
        private BuffItemModel(int buffId, bool isEffectActive, int effectCoolTime, int obtainedTurn)
        {
            BuffId = buffId;
            IsEffectActive = isEffectActive;
            EffectCoolTime = effectCoolTime;

            _buffMaster = MasterDataManager.Instance.masterSingleMode10Buff.GetWithBuffId(buffId);

            ObtainedTurn = obtainedTurn;
        }

        /// <summary> ユーザ所持データ用インスタンス取得 </summary>
        public static BuffItemModel CreateForObtained(WorkSingleModeScenarioLegend.LegendBuffDataInfo buffDataInfo)
            => new BuffItemModel(buffDataInfo);
        
        /// <summary> 未所持用インスタンス取得 </summary>
        public static BuffItemModel CreateForNotObtained(int buffId)
            => new BuffItemModel(buffId, false, 0, 0);

        private readonly MasterSingleMode10Buff.SingleMode10Buff _buffMaster;
        
        public int BuffId { get; }
        public int IconId => _buffMaster.Icon;
        public int BuffRank => _buffMaster.BuffRank;
        public string Name => SingleModeScenarioLegendUtils.GetBuffName(BuffId);

        public LegendType LegendType => SingleModeScenarioLegendUtils.LegendIdToLegendType(_buffMaster.LegendId);
        
        public bool IsEffectActive { get; }
        public int EffectCoolTime { get; }

        /// <summary> 効果が常時発動するバフか </summary>
        public bool IsAlwaysEffectActive => _buffMaster.ConditionGroupId == 0;
        public int ObtainedTurn { get; }
        
        /// <summary> やる気の値が指定値のときに発動するバフか </summary>
        public bool IsMotivationCondition
            => this.GetBuffConditionMasterList().Any(x => x.IsMotivationCondition);

        /// <summary> 効果発動時にやる気が上昇するバフか </summary>
        public bool IsGainMotivationOnActivate
            => this.GetBuffEffectMasterList().Any(x => x.IsGainMotivationOnActivate);
        
        /// <summary> ヒント獲得数を増やすバフか </summary>
        public bool IsGainHintCountEffect
            => this.GetBuffEffectMasterList().Any(x => x.IsGainHintCountOnActivate);
    }

    public static class BuffItemModelExtension
    {
        private static MasterSingleMode10Buff.SingleMode10Buff GetBuffMaster(this IBuffItemModel buffItemModel)
            => MasterDataManager.Instance.masterSingleMode10Buff.GetWithBuffId(buffItemModel.BuffId);

        public static List<MasterSingleMode10BuffCondition.SingleMode10BuffCondition> GetBuffConditionMasterList(this IBuffItemModel buffItemModel)
            => MasterDataManager.Instance.masterSingleMode10BuffCondition.GetListWithConditionGroupIdOrderByIdAsc(buffItemModel.GetBuffMaster().ConditionGroupId);
        
        public static List<MasterSingleMode10BuffEffect.SingleMode10BuffEffect> GetBuffEffectMasterList(this IBuffItemModel buffItemModel)
            => MasterDataManager.Instance.masterSingleMode10BuffEffect.GetListWithEffectGroupIdOrderByIdAsc(buffItemModel.GetBuffMaster().EffectGroupId);
    }
}

