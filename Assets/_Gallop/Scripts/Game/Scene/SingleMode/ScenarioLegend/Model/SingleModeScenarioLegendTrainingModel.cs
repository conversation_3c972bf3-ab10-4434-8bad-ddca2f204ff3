using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;
    
    /// <summary>
    /// 伝説編：トレーニング
    /// </summary>
    public interface ITrainingCommandModel
    {
        /// <summary> CommandId </summary>
        public TrainingDefine.TrainingCommandId CommandId { get; }
        
        /// <summary> 配置されているレジェンドの種類 </summary>
        public LegendType LegendType { get; }
        
        /// <summary> バフゲージ増加量 </summary>
        public int GainGaugeAmount { get; }

        /// <summary> ハイセイコーマスタリーボーナスの親友ゲージ増加予測量 </summary>
        public int GetFriendGaugePredictAmount(int positionId);
    }
    
    public class TrainingCommandModel : ITrainingCommandModel
    {
        public TrainingCommandModel(WorkSingleModeScenarioLegend.LegendTrainingData trainingData, IBuffGaugeModel buffGaugeModel)
        {
            _trainingData = trainingData;
            _buffGaugeModel = buffGaugeModel;
        }

        private readonly WorkSingleModeScenarioLegend.LegendTrainingData _trainingData;
        private readonly IBuffGaugeModel _buffGaugeModel;

        public TrainingDefine.TrainingCommandId CommandId => (TrainingDefine.TrainingCommandId)(int)_trainingData.CommandId;
        public LegendType LegendType  =>SingleModeScenarioLegendUtils.LegendIdToLegendType(_trainingData.LegendId);

        public int GainGaugeAmount => _trainingData.GainGauge;
        
        public int GetFriendGaugePredictAmount(int positionId) 
            => _trainingData.Legend9048GaugeGainList?.FirstOrDefault(
                x => x.TrainingPartnerId == positionId)?.GainValue ?? 0;
    }
}
