using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    /// <summary>
    /// 伝説編：バフリスト
    /// </summary>
    public interface IBuffListModel
    {
        /// <summary>
        /// 所持しているバフリストを取得する
        /// </summary>
        public IReadOnlyList<IBuffItemModel> BuffItems { get; }

        public IBuffItemModel GetItemByIndex(int index);

        /// <summary> 所有しているバフか </summary>
        public bool HasBuff(int buffId);
    }
    
    public class BuffListModel : IBuffListModel
    {
        public BuffListModel(IReadOnlyList<WorkSingleModeScenarioLegend.LegendBuffDataInfo> buffDataInfoList)
        {
            _buffItems = buffDataInfoList?.Select(BuffItemModel.CreateForObtained).OrderByObtainedTurn().ToArray() ?? Array.Empty<BuffItemModel>();
        }

        private readonly BuffItemModel[] _buffItems;
        
        #region Implement

        public IReadOnlyList<IBuffItemModel> BuffItems => _buffItems;

        public IBuffItemModel GetItemByIndex(int index)
        {
            if (index < 0 || index >= _buffItems.Length) return null;
            return _buffItems[index];
        }
        
        public bool HasBuff(int buffId) => _buffItems.Any(x => x.BuffId == buffId);

        #endregion
    }
}
