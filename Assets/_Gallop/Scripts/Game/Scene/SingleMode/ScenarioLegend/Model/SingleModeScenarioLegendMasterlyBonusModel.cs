using System;
using System.Collections.Generic;
using System.Linq;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;
    
    public interface IMasterlyBonusModel
    {
        public LegendType LegendType { get; }
    }

    /// <summary>
    /// 伝説編：セントライトのマスタリーボーナス
    /// </summary>
    public class MasterlyBonus9046Model : IMasterlyBonusModel
    {
        public LegendType LegendType => LegendType.Legend9046;
        
        /// <summary> 超絶好調かどうか </summary>
        public bool IsSuperMaxCondition => _9046BonusInfo.RemainCount > 0;
        
        /// <summary> 超絶好調突入までの現在のカウント </summary>
        public int EffectActivationCount => _9046BonusInfo.CurrentStepCount;

        /// <summary> 超絶好調突入までに必要なカウント </summary>
        public int EffectActivationRequiredCount => MASTERLY_BONUS_9046_EFFECT_ACTIVATION_REQUIRED_COUNT;
        
        /// <summary> 超絶好調効果の残りカウント </summary>
        public int SuperMaxConditionRemainCount => _9046BonusInfo.RemainCount;
        
        /// <summary> 超絶好調効果を延長できる残りカウント </summary>
        public int ConditionExtensionRemainCount => _9046BonusInfo.CanExtendCount;
        
        /// <summary> 超絶好調効果を延長できる上限カウント </summary>
        public int ConditionExtensionLimitCount => MASTERLY_BONUS_9046_EFFECT_EXTENSION_LIMIT_COUNT;

        private readonly WorkSingleModeScenarioLegend.Legend9046BonusInfo _9046BonusInfo;

        public MasterlyBonus9046Model(WorkSingleModeScenarioLegend.Legend9046BonusInfo legend9046BonusInfo)
        {
            _9046BonusInfo = legend9046BonusInfo;
        }
        
        /// <summary>
        /// セントライト：進捗したか
        /// </summary>
        public static bool IsProgressed9046(IMasterlyBonusModel prevModel, IMasterlyBonusModel currentModel)
        {
            if (prevModel is not MasterlyBonus9046Model prev9046Model || currentModel is not MasterlyBonus9046Model current9046Model) return false;
            return prev9046Model.EffectActivationCount < current9046Model.EffectActivationCount;
        }
            
        /// <summary>
        /// セントライト：延長したか
        /// </summary>
        public static bool IsExtended9046(IMasterlyBonusModel prevModel, IMasterlyBonusModel currentModel)
        {
            if (prevModel is not MasterlyBonus9046Model prev9046Model || currentModel is not MasterlyBonus9046Model current9046Model) return false;
            return current9046Model.IsSuperMaxMotivation() && prev9046Model.ConditionExtensionRemainCount > current9046Model.ConditionExtensionRemainCount;
        }
    }
    
    public class MasterlyBonus9047Model : IMasterlyBonusModel
    {
        public LegendType LegendType => LegendType.Legend9047;

        /// <summary> スポ根ゾーンかどうか </summary>
        public bool IsZoneCondition => ZoneContinuationTurnCount > 0;
        
        /// <summary> スポ根ゾーンの継続ターンカウント </summary>
        public int ZoneContinuationTurnCount => _9047BonusInfo.ContinuationZoneCount;

        /// <summary> スポ根ゾーン突入までの現在のカウント </summary>
        public int ZoneActivationCount => _9047BonusInfo.CurrentStepCount;

        /// <summary> スポ根ゾーン突入までに必要なカウント </summary>
        public int ZoneActivationRequiredCount => MASTERLY_BONUS_9047_ZONE_ACTIVATION_REQUIRED_COUNT;
        
        /// <summary> スポ根ゾーン中のゾーン終了率を取得 </summary>
        public int GetZoneEndRate(SingleModeDefine.CommandType commandType, TrainingDefine.TrainingCommandId commandId)
        {
            var targetInfo = _9047BonusInfo.Legend9047EndRateInfoList.FirstOrDefault(
                endRateInfo => endRateInfo.CommandType == (int)commandType && endRateInfo.CommandId == (int)commandId);
            
            return targetInfo != null ? (int)targetInfo.ZoneEndRate : 0;
        }
        
        private readonly WorkSingleModeScenarioLegend.Legend9047BonusInfo _9047BonusInfo;

        public MasterlyBonus9047Model(WorkSingleModeScenarioLegend.Legend9047BonusInfo legend9047BonusInfo)
        {
            _9047BonusInfo = legend9047BonusInfo;
        }
    }
    
    public class MasterlyBonus9048Model : IMasterlyBonusModel
    {
        #region InnerClass

        public delegate int GetCharaIdByPositionIdHandler(int positionId);
        public delegate int GetSupportCardIdByCharaIdHandler(int charaId);
        public delegate bool IsGuestHandler(int positionId);

        public class FriendDataModel
        {
            public int CharaId => _getCharaIdByPositionId(_gaugeData.TrainingPartnerId);
            public int GaugeValue =>_gaugeData.GaugeValue;
            public int Level => _gaugeData.Level;
            public int SupportCardId => _getSupportCardIdByCharaId(CharaId);
            public bool IsMaxLevel => Level == MASTERLY_BONUS_9048_FRIEND_GAUGE_MAX_LEVEL;
            public bool IsMaxGauge => GaugeValue == FRIEND_GAUGE_MAX_VALUE;
            public bool IsGuest { get; }

            private readonly WorkSingleModeScenarioLegend.Legend9048Gauge _gaugeData;
            private readonly GetCharaIdByPositionIdHandler _getCharaIdByPositionId;
            private readonly GetSupportCardIdByCharaIdHandler _getSupportCardIdByCharaId;
            
            public FriendDataModel(
                WorkSingleModeScenarioLegend.Legend9048Gauge legend9048Gauge,
                GetCharaIdByPositionIdHandler getCharaIdByPositionId,
                GetSupportCardIdByCharaIdHandler getSupportCardIdByCharaId,
                bool isGuest)
            {
                _gaugeData = legend9048Gauge;
                _getCharaIdByPositionId = getCharaIdByPositionId;
                _getSupportCardIdByCharaId = getSupportCardIdByCharaId;
                IsGuest = isGuest;
            }
        }

        #endregion
        
        public LegendType LegendType => LegendType.Legend9048;

        /// <summary> 親友関連のデータ </summary>
        private readonly FriendDataModel[] _friendDataArray;
        public IReadOnlyList<FriendDataModel> FriendDataList => _friendDataArray;
        
        /// <summary> 親友データ取得 </summary>
        public bool TryGetFriendDataByCharaId(int charaId, out FriendDataModel friendDataModel)
        {
            friendDataModel = _friendDataArray.FirstOrDefault(friendData => friendData.CharaId == charaId);
            return friendDataModel != null;
        }
        
        /// <summary> 親友ゲージのMAX量 </summary>
        public const int FRIEND_GAUGE_MAX_VALUE = SingleModeScenarioLegendDefine.MASTERLY_BONUS_9048_FRIEND_GAUGE_MAX_VALUE;
        
        /// <summary> ゲージMAXの数 </summary>
        public int MaxFriendGaugeCount
            => _friendDataArray.Count(friendData => friendData.IsMaxGauge);

        /// <summary> コンストラクタ </summary>
        public MasterlyBonus9048Model(
            WorkSingleModeScenarioLegend.Legend9048BonusInfo bonusInfo,
            GetCharaIdByPositionIdHandler getCharaIdByPositionId,
            GetSupportCardIdByCharaIdHandler getSupportCardIdByCharaId,
            IsGuestHandler isGuestHandler)
        {
            _friendDataArray = bonusInfo.Legend9048GaugeList.Select(gaugeInfo =>
                new FriendDataModel(gaugeInfo, getCharaIdByPositionId, getSupportCardIdByCharaId, isGuestHandler(gaugeInfo.TrainingPartnerId))
            ).ToArray();
        }
    }

    /// <summary>
    /// マスタリーボーナス拡張メソッド
    /// </summary>
    public static class MasterlyBonusExtension
    {
        /// <summary> セントライトの超絶好調状態かどうか </summary>
        public static bool IsSuperMaxMotivation(this IMasterlyBonusModel masterlyBonus) 
            => masterlyBonus is MasterlyBonus9046Model {IsSuperMaxCondition: true};

        /// <summary> スピードシンボリのスポ根ゾーン状態かどうか </summary>
        public static bool IsZone(this IMasterlyBonusModel masterlyBonus) 
            => masterlyBonus is MasterlyBonus9047Model {IsZoneCondition: true};
    }
}
