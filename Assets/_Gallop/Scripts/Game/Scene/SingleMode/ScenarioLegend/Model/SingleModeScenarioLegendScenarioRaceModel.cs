using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;
    
    /// <summary>
    /// 伝説編：シナリオレース
    /// </summary>
    public interface IScenarioRaceModel
    {
        /// <summary>
        /// 次のシナリオレースのマスタを取得 全て終了している場合はnull
        /// </summary>
        public MasterSingleMode10Sprace.SingleMode10Sprace NextSpRace { get; }
        
        /// <summary>
        /// 指定ターンのシナリオレース結果を取得
        /// note: ユースケースが限定的なためレース結果情報に対して個別のクラスは切っていない. 情報が複雑化してきたら検討
        /// </summary>
        public WorkSingleModeScenarioLegend.LegendScenarioRaceHistory GetRaceResult(int turn);
        
        public WorkSingleModeScenarioLegend.LegendScenarioRaceHistory GetLatestRaceResult();
        
        /// <summary>
        /// 出走しているDreamFestの年度を取得する。現在DreamFestに出走していない場合はfalse
        /// </summary>
        public bool TryGetPlayingLegendRaceDegree(out SingleModeDefine.DegreeType degree);
    }
    
    public class ScenarioRaceModel : IScenarioRaceModel
    {
        public ScenarioRaceModel(IReadOnlyList<WorkSingleModeScenarioLegend.LegendScenarioRaceHistory> raceHistoryList, Func<int> getCurrentTurn, Func<SingleModeDefine.DegreeType> getCurrentDegree)
        {
            _raceHistoryList = raceHistoryList;
            _getCurrentTurn = getCurrentTurn;
            _getCurrentDegree = getCurrentDegree;
        }

        private readonly IReadOnlyList<WorkSingleModeScenarioLegend.LegendScenarioRaceHistory> _raceHistoryList;
        private readonly Func<int> _getCurrentTurn;
        private readonly Func<SingleModeDefine.DegreeType> _getCurrentDegree;

        public MasterSingleMode10Sprace.SingleMode10Sprace NextSpRace
        {
            get
            {
                var currentTurn = _getCurrentTurn();
                // 現在ターンにシナリオレースがあり、かつレース結果が既にある場合は次ターン以降で探す
                var searchTurn = GetRaceResult(currentTurn) != null ? currentTurn + 1 : currentTurn;
                var nextSpRaceMaster = MasterDataManager.Instance.masterSingleMode10Sprace.GetNextSchedule(searchTurn);

                return nextSpRaceMaster;
            }
        }

        public WorkSingleModeScenarioLegend.LegendScenarioRaceHistory GetRaceResult(int turn)
        {
            return _raceHistoryList.FirstOrDefault(history => history.Turn == turn);
        }
        
        public WorkSingleModeScenarioLegend.LegendScenarioRaceHistory GetLatestRaceResult()
        {
            return _raceHistoryList.OrderBy(history => history.Turn).LastOrDefault();
        }

        public bool TryGetPlayingLegendRaceDegree(out SingleModeDefine.DegreeType degree)
        {
            degree = _getCurrentDegree();
            return SingleModeScenarioLegendUtils.IsPlayingLegendRace();
        }
    }
    
#if CYG_DEBUG
        
    /// <summary>
    /// ダイレクトシーンから任意の年度のシナリオレース演出を確認するためのモデル
    /// </summary>
    public class DebugScenarioRaceModel : IScenarioRaceModel
    {
        private readonly SingleModeDefine.DegreeType _dummyDegree;
            
        public DebugScenarioRaceModel(SingleModeDefine.DegreeType dummyDegree)
        {
            _dummyDegree = dummyDegree;
        }
            
        public MasterSingleMode10Sprace.SingleMode10Sprace NextSpRace => null;
        public WorkSingleModeScenarioLegend.LegendScenarioRaceHistory GetRaceResult(int turn) => null;
        public WorkSingleModeScenarioLegend.LegendScenarioRaceHistory GetLatestRaceResult() => null;

        public bool TryGetPlayingLegendRaceDegree(out SingleModeDefine.DegreeType degree)
        {
            degree = _dummyDegree;
            return true;
        }
    }

#endif
}