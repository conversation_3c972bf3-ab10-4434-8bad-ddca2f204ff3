using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ポスター演出再生プレイヤー
    /// </summary>
    public class SingleModeScenarioLegendPopularityCuttPlayerPoster : SingleModeScenarioLegendPopularityCuttPlayerBase
    {
        public class PosterContext : ContextBase
        {
            public int CatchphraseId;//キャッチコピーID
            public Texture GoalCaptureTexture;
            
            public PosterContext(int charaId, int dressId, int catchphraseId, GameDefine.BgSeason season, Texture goalCaptureTexture, Action onEndAction, Action onComplete) : base(charaId, dressId, season, onEndAction, onComplete)
            {
                CatchphraseId = catchphraseId;
                GoalCaptureTexture = goalCaptureTexture;
            }
        }
        private SingleModeScenarioLegendReputationPosterCutInController _cutInController;
        private ModelController _model;
        
        /// <summary> 再生終了時コールバック（ループ処理など） </summary>
        private Action _onEndAction;

        public override void RegisterDownloadPath(DownloadPathRegister register, ContextBase context)
        {
            SingleModeScenarioLegendReputationPosterCutInController.RegisterDownload(register);
        }

        public override IEnumerator SetupPlayData(ContextBase context)
        {
            if (!(context is PosterContext posterContext))
            {
                Debug.LogError("ポスター用ではない再生情報のため初期化できません");
                yield break;
            }

            //季節を考慮した衣装を育成キャラに適用
            var dressId = MasterDataManager.Instance.masterDressData.ConvertClothIdBySeason(posterContext.Season, SingleModeScenarioLegendDefine.POSTER_CHARACTER_DEFAULT_DRESS_ID);
            var buildInfo = new CharacterBuildInfo(charaId: posterContext.CharaId,
                dressId: dressId, controllerType: ModelLoader.ControllerType.CutIn);
            var charaObj = ModelLoader.CreateModel(buildInfo);
            _model = charaObj.GetComponent<ModelController>();

            var playContext = new SingleModeScenarioLegendReputationPosterCutInController.Context()
            {
                CharaId = posterContext.CharaId,
                DressId = dressId,
                Layout = SingleModeScenarioLegendReputationPosterCutInController.Context.LayoutType.Vertical,
                CatchCopyId = posterContext.CatchphraseId,
                OverrideUserModel = _model,
                Season = posterContext.Season,
                GoalCaptureTexture = posterContext.GoalCaptureTexture,
            };
            _onEndAction = posterContext.OnEndAction;
            _cutInController = new SingleModeScenarioLegendReputationPosterCutInController();
            _cutInController.Initialize(playContext, SceneManager.Instance.GetCurrentSceneController().GetSceneTransform());
            yield return _cutInController.PreparePlayCutIn(null);
        }
        public override void Play() 
        { 
            _cutInController.PlayCutIn(() =>
            {
                _onEndAction.Call();
                IsFinished = true;
            });
        }
        public override void Update()
        {
            if (_cutInController == null)return;
            _cutInController.AlterUpdate();
        }
        public override void LateUpdate()
        {
            if (_cutInController == null)return;
            _cutInController.AlterLateUpdate();
        }

        public override void Release(ContextBase context)
        {
            if (_cutInController != null)
            {
                _cutInController.Destroy();
                _cutInController = null;
            }
            if (_model != null)
            {
                GameObject.Destroy(_model.gameObject);
                _model = null;
            }
            context.OnComplete?.Invoke();
        }
        
        /// <summary>
        /// Helperを取得
        /// フォトスタジオなど、外部でループ管理する場合に使用
        /// </summary>
        /// <returns></returns>
        public override CutInHelper GetCutInHelper()
        {
            return _cutInController.GetCutInHelper();
        }
        
        /// <summary>
        /// Helper配列を取得
        /// フォトスタジオなど、外部でループ管理する場合に使用
        /// </summary>
        /// <returns></returns>
        public override CutInHelper[] GetCutInHelperArray()
        {
            return new CutInHelper[] { _cutInController.GetCutInHelper() };
        }
    }
}