using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ファン交流演出再生プレイヤー
    /// </summary>
    public class SingleModeScenarioLegendPopularityCuttPlayerFanExchange : SingleModeScenarioLegendPopularityCuttPlayerBase
    {
        private bool IsPlaying { get; set; }
        private bool IsRelease { get; set; }
        public override void RegisterDownloadPath(DownloadPathRegister register, ContextBase context)
        {
            SingleModeScenarioLegendCuttUtils.RegisterFanContactCuttPath(register, context.CharaId);
        }
        public override IEnumerator SetupPlayData(ContextBase context)
        {
            var isInitCompleted = false;
            UIManager.Instance.StartCoroutine(SingleModeScenarioLegendCuttUtils.PlayFanContactCutt(context.CharaId, context.DressId, context.Season,
                onInitCompleted: () => isInitCompleted = true,
                onStartFirstCut: null,
                onEnd: () => IsFinished = true,     //終了フラグ
                onSkip: () => IsRelease,　　// 再生途中に解放フラグが立ったらスキップして解放まで行くように 
                onWaitStart: () => !IsPlaying,//Playまで待機
                onWaitRelease: () => !IsRelease,//解放まで待機
                onComplete: context.OnComplete));

            yield return new WaitUntil(() => isInitCompleted);//初期化待ち
        }
        public override void Play()
        {
            IsPlaying = true;
        }
        public override void Release(ContextBase context)
        {
            IsRelease = true;
        }
    }
}