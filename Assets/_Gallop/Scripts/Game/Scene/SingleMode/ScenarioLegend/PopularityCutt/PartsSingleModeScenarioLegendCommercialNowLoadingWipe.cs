using System;

namespace Gallop
{
    /// <summary>
    /// 伝説編：CM画面遷移ワイプ
    /// </summary>
    public class PartsSingleModeScenarioLegendCommercialNowLoadingWipe : PartsSingleModeNowLoadingWipe
    {
        private const string FLASH_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_ROOT + "pf_fl_singlemode_legend_commercial_load00";
        public static void RegisterDownloadCommon(DownloadPathRegister register) => CreatePlayInfo().RegisterDownload(register);
        public static PlayInfo CreatePlayInfo() => new(false, FLASH_PATH, AudioId.SFX_ADDON10_LEGEND_CM_WIPE_IN);
        public static void ShowCommon(Action onCompleteShow) => ((PartsSingleModeNowLoadingWipe)new PartsSingleModeScenarioLegendCommercialNowLoadingWipe(CreatePlayInfo())).Show(onCompleteShow);
        private PartsSingleModeScenarioLegendCommercialNowLoadingWipe(PlayInfo playInfo) : base(playInfo){ }
    }
}
