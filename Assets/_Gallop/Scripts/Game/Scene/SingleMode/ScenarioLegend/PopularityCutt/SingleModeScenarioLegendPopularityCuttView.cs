using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 伝説編：評判カットイン
    /// </summary>
    [AddComponentMenu("")]
    public sealed class SingleModeScenarioLegendPopularityCuttView : ViewBase
    {
        [field: SerializeField, RenameField]
        public ButtonCommon Ski<PERSON>on { get; private set; }
    }

    /// <summary>
    /// 伝説編：評判カットイン
    /// </summary>
    public sealed class SingleModeScenarioLegendPopularityCuttViewController : ViewControllerBase<SingleModeScenarioLegendPopularityCuttView>
    {
        /// <summary>
        /// 画面用情報
        /// </summary>
        public class ViewInfo : IViewInfo
        {
            public SingleModeScenarioLegendDefine.PopularityCuttType PopularityType;
            public int CharaId;
            public int DressId;
            public GameDefine.BgSeason Season;
            public int PosterCatchphraseId;//ポスター用：キャッチコピーID
            public Texture PosterGoalCaptureTexture;//ポスター用：ゴールキャプチャ
            public int AdvertisementLegendId;//広告用：レジェンドID
            public Action<Texture> FinishCallback;//終了時コールバック(フォトスタジオではTextureを渡せるようにしている)
            public bool NeedReplayCurrentTurnBgm; // 直前のBGMを引き継ぐか
        }

        private SingleModeScenarioLegendDefine.PopularityCuttType GetPopularityType() => GetViewInfo<ViewInfo>().PopularityType;
        private SingleModeScenarioLegendPopularityCuttPlayerBase.ContextBase _context;
        private SingleModeScenarioLegendPopularityCuttPlayerBase _player;
        private bool _isFinished;
        private bool _isReleasedPlayer;
        
        /// <summary>
        /// 演出別Player生成
        /// </summary>
        private SingleModeScenarioLegendPopularityCuttPlayerBase CreatePlayer()
        {
            return GetPopularityType() switch
            {
                SingleModeScenarioLegendDefine.PopularityCuttType.FanExchange => new SingleModeScenarioLegendPopularityCuttPlayerFanExchange(),
                SingleModeScenarioLegendDefine.PopularityCuttType.PhotoPage => new SingleModeScenarioLegendPopularityCuttPlayerPhotoPage(),
                SingleModeScenarioLegendDefine.PopularityCuttType.Poster => new SingleModeScenarioLegendPopularityCuttPlayerPoster(),
                SingleModeScenarioLegendDefine.PopularityCuttType.Advertisement => new SingleModeScenarioLegendPopularityCuttPlayerAdvertisement(),
                _ => null,
            };
        }
        
        /// <summary>
        /// 演出別Player再生情報生成
        /// </summary>
        private SingleModeScenarioLegendPopularityCuttPlayerBase.ContextBase CreateContext()
        {
            var info = GetViewInfo<ViewInfo>();
            return GetPopularityType() switch
            {
                SingleModeScenarioLegendDefine.PopularityCuttType.FanExchange => new SingleModeScenarioLegendPopularityCuttPlayerBase.ContextBase(info.CharaId, info.DressId, info.Season, null, OnCompleterPlayer),
                SingleModeScenarioLegendDefine.PopularityCuttType.PhotoPage => new SingleModeScenarioLegendPopularityCuttPlayerPhotoPage.PhotoPageContext(info.CharaId, info.DressId, info.Season, null, true, null, OnCompleterPlayer),
                SingleModeScenarioLegendDefine.PopularityCuttType.Poster => new SingleModeScenarioLegendPopularityCuttPlayerPoster.PosterContext(info.CharaId, info.DressId, info.PosterCatchphraseId, info.Season, info.PosterGoalCaptureTexture, null, OnCompleterPlayer),
                SingleModeScenarioLegendDefine.PopularityCuttType.Advertisement => new SingleModeScenarioLegendPopularityCuttPlayerAdvertisement.AdvertisementContext(info.CharaId, info.DressId, info.AdvertisementLegendId, null, OnCompleterPlayer),
                _ => null,
            };
        }

        /// <summary>
        /// 演出が最後まで再生された時のコールバック（SKIP時も呼ばれる）
        /// </summary>
        private void FinishCuttCallback()
        {
            if (_isFinished) return;
            _isFinished = true;
            _view.StartCoroutine(CoroutineFinishCallback());
        }
        private IEnumerator CoroutineFinishCallback()
        {
            var isComplete = false;
            NowLoading.Instance.Show(NowLoading.Type.WhiteFade, onComplete: () => isComplete = true);
            yield return new WaitUntil(() => isComplete);
            yield return null;   // 完全にフェード後の絵が描画された状態待ち（この後負荷高い遷移だと半フェード状態が目立つので）
            // 外部制御に渡す（ChangeViewで他画面へ）
            GetViewInfo<ViewInfo>().FinishCallback?.Invoke(null);
        }
        
        #region ViewControllerBase
        
        /// <summary>
        /// ダウンロード対象リソースの登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            PartsSingleModeScenarioLegendPopularityNowLoadingWipe.RegisterDownloadCommon(register);
            _context = CreateContext();
            _player = CreatePlayer();
            _player.RegisterDownloadPath(register, _context);
            
            // 目標レース出走ターンに評判カットインが発生し、評判カットインから育成再開する場合は目標レース後のBGMを流す (Story側に準拠する)
            AudioManager.Instance.RegisterDownloadByAudioId(register, AudioId.BGM_SINGLE_MODE_AFTER_TARGET_RACE);
        }

        public override void OverrideDynamicNowLoadingType(ref NowLoading.Type loadingType, SceneDefine.ViewId prevViewID)
        {
            if (prevViewID == SceneDefine.ViewId.SingleModeMain)
            {
                loadingType = NowLoading.Type.CustomWipeFlash;
                PartsSingleModeScenarioLegendPopularityNowLoadingWipe.SetupCommon();
            }
        }
        
        public override bool IgnoreBgm()
        {
            // 育成TOP側で流していたものを引き継ぐ、もしくは自前で流すため無視
            return true;
        }
        
        private void PlayCurrentTurnBgm()
        {
            // ここに来るのは中断再開：
            // 目標レースのターン、かつ高速スキップで評判カットイン後イベントに遷移する場合は目標レース後のBGMを流す(Story準拠)。そうでない場合は育成TOPのBGMを流すことでイベント遷移後もBGMが維持されるようにする
            if (WorkDataManager.Instance.SingleMode.IsAfterTargetRace() && StoryManager.IsHighSpeedMode)
            {
                AudioManager.Instance.PlayBgm(AudioId.BGM_SINGLE_MODE_AFTER_TARGET_RACE);
            }
            else
            {
                SingleModeUtils.PlayTopBgm(isStory:true); //育成BGM
            }
        }

        /// <summary>
        /// ビュー初期化
        /// </summary>
        public override IEnumerator InitializeView()
        {
            var scene = GetSceneController<SingleModeSceneController>();
            scene.SetActiveScene(true);
            scene.SetCameraEnable(false);//育成用シーンカメラ不要
            
            // カットのリソースがシーン紐づきになってしまうのを防ぐため、View紐づきに変更
            ResourceManager.PushLoadSceneResourceHash((ResourceManager.ResourceHash)ResourceManager.GetViewLoadHash(SceneManager.Instance.GetCurrentViewId()));
            
            yield return _player.SetupPlayData(_context);

            // スキップ
            _view.SkipButton.SetOnClick(FinishCuttCallback);
            
            _player.Play();
            
            if (GetViewInfo<ViewInfo>().NeedReplayCurrentTurnBgm)
            {
                // 中断再開時は自前でBGMを流す必要がある
                PlayCurrentTurnBgm();

                // 自前でBGMを流した場合は MainViewに戻るまで停止対象から一時的に除外
                AudioManager.Instance.SetEnableBgmAutoStop(false);
            }
        }

        public override IEnumerator FinalizeView()
        {
            if (SceneManager.Instance.GetNextViewId() != SceneDefine.ViewId.SingleModeMain)
            {
                // 何らかの理由で評判カットイン後イベント以外の画面に行く場合はBGMが画面遷移時に止まるように設定を戻す
                AudioManager.Instance.SetEnableBgmAutoStop(true);
            }
            
            // カット破棄など後処理
            _player.Release(_context);
            // 破棄完了待ち
            yield return new WaitUntil(() => _isReleasedPlayer);
            
            ResourceManager.PopLoadSceneResourceHash();
        }
        
        /// <summary>
        /// 演出側のReleaseが全部終わった時のコールバック
        /// </summary>
        private void OnCompleterPlayer()
        {
            _isReleasedPlayer = true;
        }

        /// <summary>
        /// 更新
        /// </summary>
        public override void UpdateView()
        {
            _player.Update();

            // 演出終わったらコールバック
            if (_player.IsFinished && !_isFinished)
            {
                FinishCuttCallback();
            }
        }
        
        public override void LateUpdateView()
        {
            _player.LateUpdate();
        }
        
        public override void UpdateViewBeforeLoadingOut()
        {
            UpdateView();
        }

        public override void UpdateViewBeforeLoadingIn()
        {
            UpdateView();
        }

        public override void LateUpdateViewBeforeLoadingOut()
        {
            LateUpdateView();
        }

        public override void LateUpdateViewBeforeLoadingIn()
        {
            LateUpdateView();
        }
        
        public override void OnClickOsBackKey()
        {
            UIUtil.ShowNotificationBackKey();
        }
        
        #endregion
   }
}