using Gallop.SingleMode.ScenarioLegend;
using UnityEngine;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;

namespace Gallop
{
    /// <summary>
    /// 伝説編：マスタリーボーナス_ハイセイコー_初回発動演出_③育成TOPUIが左上から表示
    /// </summary>
    public class SingleModeScenarioLegend9048ActivationParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        public bool IsGroupPlay => false;
        public string MessageText => string.Empty;
        
        public MasterlyBonus9048Model CurrentModel { get; }
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            PartsSingleModeScenarioLegendMainView.RegisterDownload(register);
        }
        
        public SingleModeScenarioLegend9048ActivationParameterInfo(MasterlyBonus9048Model currentModel)
        {
            CurrentModel = currentModel;
        }
        
        public TrainingParamChangeA2UContext CreateA2UContext()
        {
            return new SingleModeScenarioLegend9048ActivationA2U(this);
        }
        
        private class SingleModeScenarioLegend9048ActivationA2U : TrainingParamChangeA2UContext<SingleModeScenarioLegend9048ActivationParameterInfo>
        {
            public override string FlashPath => string.Empty;
            public override float PlayTime => ANIMATION_LONG_TIME;
            
            /// <summary> 育成TOP左端に表示するUI </summary>
            private TrainingParamChangeSharedPartsControllerScenarioLegend _partsController;

            public SingleModeScenarioLegend9048ActivationA2U(SingleModeScenarioLegend9048ActivationParameterInfo info)
            {
                SetChangeParameterInfo(info);
            }
            
            public override void LoadPlayer(Transform contentsRoot, Transform parent, float speed, int layer = -1, string sortLayer = "", int sortOffset = -1)
            {
                _partsController = _trainingParamChangeUI.SharedPartsLocator.CreateOrResolve<TrainingParamChangeSharedPartsControllerScenarioLegend>();
                _partsController.SetupSharedParts(contentsRoot);
            }
            
            public override void Play(ChangeParameterInfo changeParameter, bool enableGroupPlay, System.Action unsetIsPlaying, System.Action setIsEnd)
            {
                _unsetIsPlaying = unsetIsPlaying;
                _setIsEnd = setIsEnd;
            
                _trainingParamChangeUI.SetEnableCheckScreenTap(false); //TAP送り禁止
                _trainingParamChangeUI.SetTapButtonLayer(UIManager.SYSTEM_UI_SORTING_LAYER_NAME); // ヘッダーフッターへのタップ伝達を切るため手前に持ってくる
                
                // TOP IN
                _partsController.ExcludePartsFromUIImageEffect();
                _partsController.PlayInBlurFade();
                _partsController.PlayInIfInActive();
                _partsController.Parts.PlayActivationMasterlyBonus9048(_changeParameter.CurrentModel, () =>
                {
                    _trainingParamChangeUI.SetEnableCheckScreenTap(true);
                    _unsetIsPlaying?.Invoke();
                    _trainingParamChangeUI.OnTapScreen();// タップ送りなしで次へ進行
                });
            }
            
            /// <summary>
            /// OUT
            /// </summary>
            public override void PlayOut()
            {
                _partsController.PlayOutBlurFade();
                _partsController.PlayOut(() =>
                {
                    _partsController.IncludePartsInUIImageEffect();
                    _trainingParamChangeUI.SetTapButtonLayer(UIManager.UI_SORTING_LAYER_NAME);
                    _setIsEnd?.Invoke();
                });
            }
        }
    }
}
