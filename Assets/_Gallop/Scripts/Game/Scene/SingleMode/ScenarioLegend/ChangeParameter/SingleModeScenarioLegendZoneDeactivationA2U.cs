using DG.Tweening;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 伝説編：スポ根ゾーン終了
    /// </summary>
    public class SingleModeScenarioLegendZoneDeactivationParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        public bool IsGroupPlay => false;
        public string MessageText => TextId.SingleModeScenarioLegend539033.Text();

        public static void RegisterDownload(DownloadPathRegister register)
        {
            SingleModeScenarioLegendZoneDeactivationA2U.RegisterDownload(register);
        }
        
        public TrainingParamChangeA2UContext CreateA2UContext()
        {
            return new SingleModeScenarioLegendZoneDeactivationA2U();
        }
    }
    
    /// <summary>
    /// 伝説編：スポ根ゾーン終了
    /// </summary>
    public class SingleModeScenarioLegendZoneDeactivationA2U : TrainingParamChangeA2UContext
    {
        public override string FlashPath => string.Empty;
        public override float PlayTime => ANIMATION_LONG_TIME;
        
        /// <summary> 育成TOP左端に表示するUI </summary>
        private TrainingParamChangeSharedPartsControllerScenarioLegend _partsController;
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            PartsSingleModeScenarioLegendMainView.RegisterDownload(register);
        }
        
        public override void LoadPlayer(Transform contentsRoot, Transform parent, float speed, int layer = -1, string sortLayer = "", int sortOffset = -1)
        {
            _partsController = _trainingParamChangeUI.SharedPartsLocator.CreateOrResolve<TrainingParamChangeSharedPartsControllerScenarioLegend>();
            _partsController.SetupSharedParts(contentsRoot);
        }
        
        public override void Play(ChangeParameterInfo changeParameter, bool enableGroupPlay, System.Action unsetIsPlaying, System.Action setIsEnd)
        {
            _unsetIsPlaying = unsetIsPlaying;
            _setIsEnd = setIsEnd;
            
            _partsController.PlayInIfInActive(PlayInternal);
            _trainingParamChangeUI.SetEnableCheckScreenTap(false); //TAP送り禁止
            _trainingParamChangeUI.HideAndPauseCurrentMessage();

            void PlayInternal()
            {
                var sequence = DOTween.Sequence();
                sequence.InsertCallback(GameDefine.BASE_FPS_TIME * 3f, () =>
                {
                    _trainingParamChangeUI.ShowAndResumeCurrentMessage();
                    _partsController.Parts.MasterlyBonus.PlayDeactivateZone();
                });
                sequence.InsertCallback(GameDefine.BASE_FPS_TIME * 15f, ()=>
                {
                    _trainingParamChangeUI.SetEnableCheckScreenTap(true);
                    _unsetIsPlaying?.Invoke();
                });
            }
        }
        
        public override void PlayOut()
        {
            _partsController.PlayOut(() =>
            {
                _setIsEnd?.Invoke();
            });
        }
    }
}