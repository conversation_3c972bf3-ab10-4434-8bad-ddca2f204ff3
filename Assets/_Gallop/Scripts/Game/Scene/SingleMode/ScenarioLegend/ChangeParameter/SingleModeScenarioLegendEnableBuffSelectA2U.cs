using System.Collections.Generic;
using System.Linq;
using AnimateToUnity;
using DG.Tweening;
using Gallop.SingleMode.ScenarioLegend;
using static Gallop.TrainingParamChangeUI;
using static Gallop.StaticVariableDefine.SingleMode.TrainingParamChangeA2U;
using UnityEngine;

namespace Gallop
{
    using static AtlasSpritePath.SingleModeScenarioLegend;

    /// <summary>
    /// 伝説編：バフ獲得可能！演出
    /// </summary>
    public class SingleModeScenarioLegendEnableBuffSelectParameterInfo : ChangeParameterInfo, IChangeParameterUIContext, IChangeParamterA2UFactory
    {
        public bool IsGroupPlay => false;
        public string MessageText => TextId.SingleModeScenarioLegend194001.Text();
        
        public IReadOnlyList<IBuffItemModel> BuffItemList { get; }
        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            SingleModeScenarioLegendEnableBuffSelectA2U.RegisterDownload(register);
        }

        public SingleModeScenarioLegendEnableBuffSelectParameterInfo(IReadOnlyList<IBuffItemModel> buffItemList)
        {
            BuffItemList = buffItemList;
        }
        
        public TrainingParamChangeA2UContext CreateA2UContext()
        {
            return new SingleModeScenarioLegendEnableBuffSelectA2U(this);
        }
    }
    
    /// <summary>
    /// 伝説編：バフ獲得可能！演出
    /// </summary>
    public class SingleModeScenarioLegendEnableBuffSelectA2U : TrainingParamChangeA2UContext<SingleModeScenarioLegendEnableBuffSelectParameterInfo>
    {
        private const int TXT_FLASH_OFFSET_Y = -90;
        private const int LIST_FLASH_OFFSET_Y = 220;
        private const int LIST_FLASH_SORT_OFFSET = 50;
        private const int TXT_FLASH_SORT_OFFSET = 100;
        private const int ORB_FLASH_SORT_OFFSET = 10;
        
        private const string TXT_FLASH_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_txt_buff_available00";
        private const string ORB_FLASH_PATH_FORMAT = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_buff_available{0:D2}";
        private const string LIST_FLASH_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_buff_available_list00";
        private static string GetOrbFlashPath(SingleModeScenarioLegendDefine.LegendType legendType) => TextUtil.Format(ORB_FLASH_PATH_FORMAT, (int)legendType);
        
        public override string FlashPath => TXT_FLASH_PATH;
        public override float PlayTime => ANIMATION_LONG_TIME;
        
        /// <summary> 育成TOP左端に表示するUI </summary>
        private TrainingParamChangeSharedPartsControllerScenarioLegend _partsController;

        private FlashActionPlayer _textFlashActionPlayer;
        private FlashActionPlayer[] _orbFlashActionPlayerArray;
        private FlashActionPlayer _listFlashActionPlayer;

        private Transform _textControllerParent;
        private SingleModeScenarioLegendEnableBuffSelectParameterInfo _info;
        public SingleModeScenarioLegendEnableBuffSelectA2U(SingleModeScenarioLegendEnableBuffSelectParameterInfo info)
        {
            _info = info;
        }

        
        public static void RegisterDownload(DownloadPathRegister register)
        {
            PartsSingleModeScenarioLegendMainView.RegisterDownload(register);
            register.RegisterPathWithoutInfo(TXT_FLASH_PATH);
            var legendTypeArray = EnumUtil.GetEnumArray<SingleModeScenarioLegendDefine.LegendType>();
            foreach (var legendType in legendTypeArray)
            {
                register.RegisterPathWithoutInfo(GetOrbFlashPath(legendType));
            }
            register.RegisterPathWithoutInfo(LIST_FLASH_PATH);
        }
        
        public override void LoadPlayer(Transform contentsRoot, Transform parent, float speed, int layer = -1, string sortLayer = "", int sortOffset = -1)
        {
            // バフ獲得可能文字
            _textFlashActionPlayer = FlashActionPlayer.Load(TXT_FLASH_PATH, parent);
            _textFlashActionPlayer.LoadFlashPlayer();
            _textFlashActionPlayer.transform.SetLocalPositionY(TXT_FLASH_OFFSET_Y);
            _textFlashActionPlayer.SetSortOffset(FlashSortOffset + TXT_FLASH_SORT_OFFSET);
            _textFlashActionPlayer.SetOnInstantiatedParticle(particles =>
            {
                foreach (var particle in particles)
                {
                    var particleSystemRenderer = particle.GetComponent<ParticleSystemRenderer>();
                    particleSystemRenderer.sortingOrder = _textFlashActionPlayer.SortOffset;
                }
            });

            // ３種類の光の玉
            LoadPlayerOrb(parent);
            
            // 一覧表示
            LoadPlayerList(parent);
            
            _partsController = _trainingParamChangeUI.SharedPartsLocator.CreateOrResolve<TrainingParamChangeSharedPartsControllerScenarioLegend>();
            _partsController.SetupSharedParts(contentsRoot, false);//バフ獲得イベントは通信前なのでバックアップを使用しない
        }
        
        public override void Play(ChangeParameterInfo changeParameter, bool enableGroupPlay, System.Action unsetIsPlaying, System.Action setIsEnd)
        {
            _setIsEnd = setIsEnd;
            
            _textFlashActionPlayer.Play(GameDefine.A2U_IN00_LABEL);
            StartPositionOrbs();
            PlayOrb(GameDefine.A2U_IN00_LABEL);
            
            _trainingParamChangeUI.SetEnableCheckScreenTap(false); //TAP送り禁止

            // ブラー上へ移動
            foreach (var flashActionPlayer in _orbFlashActionPlayerArray)
            {
                if (flashActionPlayer == null) continue;
                ExcludePartsFromUIImageEffect(flashActionPlayer.gameObject);
            }
            ExcludePartsFromUIImageEffect(_textFlashActionPlayer.gameObject);
            ExcludePartsFromUIImageEffect(_listFlashActionPlayer.gameObject);
            _trainingParamChangeUI.ExcludeFromUIImageEffect();
            _trainingParamChangeUI.SetActiveWithTextFrame(true);
            _partsController.ExcludePartsFromUIImageEffect();
            // ブラーON
            _partsController.PlayInBlurFade();
            // IN
            _partsController.PlayInIfInActive(() =>
            {
                var sequence = DOTween.Sequence();
                sequence.AppendInterval(GameDefine.BASE_FPS_TIME * 3f); // 8F
                sequence.AppendCallback(() =>
                {
                    AudioManager.Instance.PlaySe(AudioId.SFX_ADDON10_LEGEND_BUFF_GET);
                    PlayOrb(GameDefine.A2U_IN01_LABEL);     // 光発光
                    _partsController.Parts.PlayGaugeFlushAndReset();// バフゲージ発光
                });  
                
                // 8F
                sequence.AppendInterval(GameDefine.BASE_FPS_TIME * 7f); // 15F
                sequence.AppendCallback(() =>
                {
                    _listFlashActionPlayer.Play("in_loc");//一覧のロケーターのみ
                    PlayMoveOrbs();// 光の玉移動
                });                   
                sequence.AppendInterval(GameDefine.BASE_FPS_TIME * 4f); // 19F
                sequence.AppendCallback(PlayInList);// 一覧IN
                sequence.AppendInterval(GameDefine.BASE_FPS_TIME * 3f); // 21F
                sequence.AppendCallback(()=>_textFlashActionPlayer.Play(GameDefine.A2U_IN_LABEL));// 獲得可能！
                sequence.AppendInterval(GameDefine.BASE_FPS_TIME * 24f);
                sequence.AppendCallback(()=>
                {
                    _trainingParamChangeUI.SetEnableCheckScreenTap(true); //TAP送り復帰。等速再生の場合はここでタップ待ち
                    unsetIsPlaying?.Invoke();
                });
            });
        }
        
        /// <summary>
        /// 指定オブジェクトをUIImageEffectの対象から除外する
        /// </summary>
        private void ExcludePartsFromUIImageEffect(GameObject target)
        {
            target.transform.SetParent(UIManager.Instance.NoImageEffectGameCanvasRoot.transform);
            target.SetLayerRecursively(UIManager.SYSTEM_UI_SORTING_LAYER_NAME);
        }

        #region 光の玉

        /// <summary>
        /// 光の玉A2U読み込み
        /// </summary>
        private void LoadPlayerOrb(Transform parent)
        {
            _orbFlashActionPlayerArray = new FlashActionPlayer[3];
            var buffLegendGroup = _info.BuffItemList.GroupBy(x => x.LegendType);
            foreach (var buffLegend in buffLegendGroup)
            {
                var legendType = buffLegend.Key;
                var i = (int)legendType;
                _orbFlashActionPlayerArray[i] = FlashActionPlayer.Load(GetOrbFlashPath(legendType), parent);
                _orbFlashActionPlayerArray[i].LoadFlashPlayer();
                _orbFlashActionPlayerArray[i].SetSortOffset(FlashSortOffset + ORB_FLASH_SORT_OFFSET);
            }
        }
        
        /// <summary>
        /// すべての光の玉の移動
        /// </summary>
        private void PlayMoveOrbs()
        {
            var legendTypeArray = EnumUtil.GetEnumArray<SingleModeScenarioLegendDefine.LegendType>();
            foreach (var legendType in legendTypeArray)
            {
                var i = (int)legendType;
                var player = _orbFlashActionPlayerArray[i];
                if (player == null) continue;
                
                player.Play("in_move");
                var goalLocatorObj = _listFlashActionPlayer.FlashPlayer.GetObj(TextUtil.Format("OBJ_loc_ico_legend_eff{0:D2}", i));
                var goalPosition = goalLocatorObj._transform.position;
                MoveOrb(player.transform, goalPosition);
            }
        }
        
        /// <summary>
        /// 全ての光の玉を開始位置へ
        /// </summary>
        private void StartPositionOrbs()
        {
            var legendTypeArray = EnumUtil.GetEnumArray<SingleModeScenarioLegendDefine.LegendType>();
            foreach (var legendType in legendTypeArray)
            {
                var i = (int)legendType;
                var player = _orbFlashActionPlayerArray[i];
                if (player == null) continue;
                var motion = _partsController.Parts.GetGaugeRootMotion(legendType);//バフゲージの位置
                player.transform.position = motion._transform.position;
            }
        }
        
        /// <summary>
        /// 光の玉の移動
        /// </summary>
        private void MoveOrb(Transform target, Vector3 goalLocalPosition)
        {
            const float DURATION = GameDefine.BASE_FPS_TIME * 6f;
            target.DOMove(goalLocalPosition, DURATION).SetEase(Ease.InQuad);
        }

        /// <summary>
        /// すべての光の玉の再生
        /// </summary>
        private void PlayOrb(string label)
        {
            foreach (var flashActionPlayer in _orbFlashActionPlayerArray)
            {
                if (flashActionPlayer == null) continue;
                flashActionPlayer.Play(label);
            }
        }
        #endregion
        
        #region バフ一覧

        /// <summary>
        /// 一覧A2U読み込み
        /// </summary>
        private void LoadPlayerList(Transform parent)
        {
            _listFlashActionPlayer = FlashActionPlayer.Load(LIST_FLASH_PATH, parent);
            _listFlashActionPlayer.LoadFlashPlayer();
            _listFlashActionPlayer.transform.SetLocalPositionY(LIST_FLASH_OFFSET_Y);
            _listFlashActionPlayer.SetSortOffset(FlashSortOffset + LIST_FLASH_SORT_OFFSET);
        }

        /// <summary>
        /// 一覧IN
        /// </summary>
        private void PlayInList()
        {
            PlayOrb("in_move_end");//光の玉END
            _listFlashActionPlayer.Play(GameDefine.A2U_IN01_LABEL);
            
            // バフの種類別の点灯
            var legendTypeArray = EnumUtil.GetEnumArray<SingleModeScenarioLegendDefine.LegendType>();
            var player = _listFlashActionPlayer.FlashPlayer;
            foreach (var legendType in legendTypeArray)
            {
                // 高ランクのバフが右側に行くようにソート
                var buffList = _info.BuffItemList.Where(x => x.LegendType == legendType).OrderByEnableBuffSelect().ToList();
                var cnt = buffList.Count;
                if(cnt <= 0) continue;

                var motionRootName = GetMotionObjectName(legendType);
                var motion = player.GetMotion(motionRootName);
                motion.SetMotionPlay(TextUtil.Format(GameDefine.A2U_IN_NUM_LABEL, cnt));

                // バフアイコン設定
                for (int i = 0; i < cnt; i++)
                {
                    var buffItem = buffList[i];
                    var itemObjRoot = player.GetObj(TextUtil.Format("OBJ_mc_ico_legend_buff{0:D2}", i), rootGameObject: motion.GameObject);

                    // バフアイコン
                    var buffIconSprite = GetLegendBuffIconSprite(buffItem.LegendType, buffItem.IconId);
                    player.SetSprite("OBJ_mc_dum_ico_legend_buff00/PLN_object0", buffIconSprite, rootGameObject:itemObjRoot.GameObject);

                    // 下地
                    var baseSprite = GetLegendBuffIconFrameBgSprite(buffItem.LegendType);
                    player.SetSprite("PLN_dum_ico_legend_buff_frame_bg00", baseSprite, rootGameObject:itemObjRoot.GameObject);
                    
                    // カテゴリ
                    var categorySprite =  GetLegendBuffIconFrameSprite(buffItem.LegendType, buffItem.BuffRank);
                    player.SetSprite("OBJ_mc_dum_ico_legend_buff_frame00/PLN_dum_ico_trainingmenu00", categorySprite, rootGameObject:itemObjRoot.GameObject);
                }
            }

            // レジェンド別のモーションオブジェクト名
            string GetMotionObjectName(SingleModeScenarioLegendDefine.LegendType legendType)
            {
                return legendType switch
                {
                    SingleModeScenarioLegendDefine.LegendType.Legend9046 => "MOT_mc_ico_legend_buff_upper00",
                    SingleModeScenarioLegendDefine.LegendType.Legend9047 => "MOT_mc_ico_legend_buff_middle00",
                    SingleModeScenarioLegendDefine.LegendType.Legend9048 => "MOT_mc_ico_legend_buff_lower00",
                    _ => throw new System.ArgumentOutOfRangeException()
                };
            }
        }
        
        #endregion

        
        public override void PlayOut()
        {
            _textFlashActionPlayer.Play(GameDefine.A2U_OUT_LABEL);
            _listFlashActionPlayer.Play(GameDefine.A2U_OUT_LABEL);

            _partsController.PlayOutBlurFade(() =>
            {
                // ブラー上にした設定をもとに戻す
                _partsController.IncludePartsInUIImageEffect();
                _trainingParamChangeUI.IncludeInUIImageEffect();
                _trainingParamChangeUI.SetActiveWithTextFrame(false);
                // 終了
                _setIsEnd?.Invoke();
                Finalize();
            });
            _partsController.PlayOut();
        }

        private void Finalize()
        {
            foreach (var flashActionPlayer in _orbFlashActionPlayerArray)
            {
                if (flashActionPlayer == null) continue;
                Object.Destroy(flashActionPlayer.gameObject);
            }
            _orbFlashActionPlayerArray =  null;
            DestroyPlayer(ref _textFlashActionPlayer);
            DestroyPlayer(ref _listFlashActionPlayer);

            void DestroyPlayer(ref FlashActionPlayer player)
            {
                if(player == null) return;
                Object.Destroy(player.gameObject);
                player = null;
            }
        }
    }
}