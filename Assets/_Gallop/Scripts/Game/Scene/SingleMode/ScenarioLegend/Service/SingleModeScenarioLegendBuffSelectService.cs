using System;
using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    using static SingleModeScenarioLegendDefine;

    public class SelectableBuffItemModel
    {
        public IBuffItemModel BuffItemModel { get; }
        public bool IsGaugeMaxBonus { get; }
        
        public SelectableBuffItemModel(IBuffItemModel buffItemModel, bool isGaugeMaxBonus)
        {
            BuffItemModel = buffItemModel;
            IsGaugeMaxBonus = isGaugeMaxBonus;
        }
    }
    
    /// <summary>
    /// 伝説編　バフ選択~獲得
    /// </summary>
    public static class BuffSelectService
    {
        /// <summary>
        /// バフの入れ替えが可能かどうか
        /// </summary>
        public static bool IsEnableExchange
        {
            get
            {
                // 選択可能なバフがない場合は入れ替えも不可
                if (GetSelectableItems().IsNullOrEmpty()) return false;

                // バフリストが上限までセットされているなら入れ替え可能
                var buffListModel = BuffListModelRepository.Get();
                return buffListModel.BuffItems.Count == BUFF_LIST_LIMIT;
            }
        }
        
        /// <summary>
        /// バフ選択イベントが発生するまでの残りコマンド実行回数を取得。もうイベントがない場合はfalse
        /// 育成TOPで表示する用。イベントで表示する場合は<see cref="WorkSingleModeData.IsStepTurnByStory"/>などの考慮が必要
        /// </summary>
        public static bool TryGetBuffLotteryRemainCommandCountForCommandSelect(int currentTurn, out int remainCount)
        {
            var nextLotteryTurn = MasterDataManager.Instance.masterSingleMode10LotteryTurn.GetNextLotteryTurn(currentTurn);
            var remainTurn = nextLotteryTurn?.Turn - currentTurn ?? 0;
            remainCount = remainTurn + 1; // コマンド実行後に獲得できるのでコマンド実行前には+1
            return nextLotteryTurn != null;
        }
        
        /// <summary>
        /// 選択可能なバフ候補を返す
        /// </summary>
        public static SelectableBuffItemModel[] GetSelectableItems()
        {
            var selectableIds = WorkDataManager.Instance.SingleMode.Character.ScenarioLegend.ObtainableBuffList;
            var selectableBuffItems = selectableIds
                .Select(id => BuffItemModel.CreateForNotObtained(id)).ToArray();
            
            return selectableBuffItems
                .Select(buffItem => new SelectableBuffItemModel(buffItem, IsGaugeMaxBonus(buffItem, selectableBuffItems))).ToArray();
        }

        /// <summary>
        /// 指定のバフがゲージMAXボーナス扱いかどうか
        /// クライアントのみで判定する
        /// </summary>
        private static bool IsGaugeMaxBonus(IBuffItemModel buff, IBuffItemModel[] candidates)
        {
            var sameLegendCandidates = candidates.Where(candidate => candidate.LegendType == buff.LegendType).ToArray();
            var buffSelectMaster = MasterDataManager.Instance.masterSingleMode10BuffNum.GetWithLegendIdAndNumOrderByIdAsc(buff.LegendType.ToLegendId(), sameLegendCandidates.Length);
            if (buffSelectMaster == null) return false;
            
            // ゲージMAXボーナスの最大数。ゲージがMAX(=同ジャンルで3個抽選)でない場合はゲージMAXボーナスが発生していない
            var gaugeMaxBonusNum = buffSelectMaster.HighestNum;
            if (gaugeMaxBonusNum == 0) return false;
            
            return sameLegendCandidates
                .OrderByDescending(x => x.BuffRank)
                .ThenBy(x => x.BuffId)
                .Take(gaugeMaxBonusNum)
                .Any(x => x.BuffId == buff.BuffId);
        }

        /// <summary>
        /// 選択を確定する = サーバ通信
        /// </summary>
        public static void SendSelect(int buffId, Action onComplete)
        {
            var req = new SingleModeLegendSelectBuffRequest
            {
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                buff_id = buffId,
            };
            
            req.Send(response =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(response.data);
                    onComplete?.Invoke();
                }
            );
        }

        /// <summary>
        /// バフの入れ替えを実行
        /// </summary>
        /// <param name="releaseBuffId">手放すバフ</param>
        /// <param name="acquireBuffId">新たに獲得するバフ</param>
        /// <param name="onComplete"></param>
        public static void SendExchange(int releaseBuffId, int acquireBuffId, Action onComplete)
            => SendExchangeInternal(releaseBuffId, acquireBuffId, onComplete);

        /// <summary>
        /// バフの入れ替えを却下する
        /// </summary>
        public static void SendExchangeCancel(Action onComplete)
            // バフを入れ替えない場合はBuffIdの指定をせず0で送る
            => SendExchangeInternal(0, 0, onComplete);
        
        #region privateメソッド

        /// <summary>
        /// バフの入れ替えを実行
        /// </summary>
        /// <param name="releaseBuffId">手放すバフ</param>
        /// <param name="acquireBuffId">新たに獲得するバフ</param>
        /// <param name="onComplete"></param>
        private static void SendExchangeInternal(int releaseBuffId, int acquireBuffId, Action onComplete)
        {
            var req = new SingleModeLegendExchangeBuffRequest()
            {
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                before_buff_id = releaseBuffId,
                after_buff_id = acquireBuffId,
            };
            
            req.Send(response =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(response.data);
                    onComplete?.Invoke();
                }
            );
        }

        #endregion
    }
}
