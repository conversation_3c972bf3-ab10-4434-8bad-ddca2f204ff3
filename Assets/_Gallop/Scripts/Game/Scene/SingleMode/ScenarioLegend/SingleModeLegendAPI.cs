using System;
using System.Collections;
using System.Linq;
using Cute.Http;

namespace Gallop
{
    public class SingleModeLegendAPI : ISingleModeAPIBase
    {
        /// <summary>
        /// 育成中でも日付変更チェックを行うAPIかチェックする
        /// </summary>
        public bool IsDateChangeCheckAPI(Type request)
        {
            return IsSingleModeRaceEndRequest(request) ||
                   IsSingleModeFinishRequest(request) ||
                   IsSingleModeContinueRequest(request);
        }

        private bool IsSingleModeRaceEndRequest(Type request)
            => typeof(SingleModeLegendRaceEndRequest) == request;

        private bool IsSingleModeFinishRequest(Type request)
            => typeof(SingleModeLegendFinishRequest) == request;

        private bool IsSingleModeContinueRequest(Type request)
            => typeof(SingleModeLegendContinueRequest) == request;

        /// <summary>
        /// 育成開始API
        /// </summary>
        public void SendStart(
            int scenarioId,
            SingleModeStartChara startChara,
            TpInfo tpInfo,
            int useTp,
            int currentMoney,
            int successionRankPoint,
            Action<SingleModeStartCommon, SingleModeStartChara> onSendSuccess)
        {
            var req = new SingleModeLegendStartRequest
            {
                start_chara = startChara,
                current_money = currentMoney,
                tp_info = tpInfo,
                use_tp = useTp,
                current_succession_rank_point = successionRankPoint,
            };
            req.Send(response =>
            {
                WorkDataManager.Instance.SingleMode.Apply(response.data);
                onSendSuccess?.Invoke(response.data.single_mode_start_common, startChara);
            });
        }

        /// <summary>
        /// 育成再開API
        /// </summary>
        public IEnumerator SendLoadAsync(
            Action<SingleModeLoadCommon> onSendSuccess,
            Action<ErrorType, int> onSendError)
        {
            yield return SingleModeSceneController.SendAsync<SingleModeLegendLoadRequest, SingleModeLegendLoadResponse>(
                new SingleModeLegendLoadRequest(),
                response =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(response.data);
                    onSendSuccess?.Invoke(response.data.single_mode_load_common);

                    // #58607 エラー時にフラグを下しているが、リトライ成功した時に備えてここでフラグON
                    WorkDataManager.Instance.SingleMode.IsPlaying = true;
                }, onSendError);
        }

        /// <summary>
        /// コマンド実行API
        /// </summary>
        public IEnumerator SendExecCommandAsync(
            SingleModeDefine.CommandType commandType,
            TrainingDefine.TrainingCommandId commandId,
            int commandGroupId,
            int selectId,
            int execModeValue,
            Action<SingleModeCommandResult, int> onSendSuccess,
            Action<ErrorType, int> onSendError)
        {
            var req = new SingleModeLegendExecCommandRequest
            {
                command_type = (int)commandType,
                command_id = (int)commandId,
                command_group_id = commandGroupId,
                select_id = selectId,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                current_vital = WorkDataManager.Instance.SingleMode.Character.Hp,
                exec_auto_play_plan_id = execModeValue,
            };
            yield return SingleModeSceneController.SendAsync<SingleModeLegendExecCommandRequest, SingleModeLegendExecCommandResponse>(
                req,
                res =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(res.data);
                    onSendSuccess?.Invoke(res.data.command_result, res.data.chara_info.turn);
                }, onSendError);
        }

        /// <summary>
        /// イベント既読API
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="charaId"></param>
        /// <param name="gainId"></param>
        /// <param name="execModeValue"></param>
        /// <param name="onSendSuccess"></param>
        public void SendCheckEvent(int eventId, int charaId, int gainId, int execModeValue, Action<int> onSendSuccess)
        {
            var req = new SingleModeLegendCheckEventRequest
            {
                event_id = eventId,
                chara_id = charaId,
                choice_number = gainId,
                exec_auto_play_plan_id = execModeValue,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
            };
            req.Send(res =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSendSuccess.Invoke(res.data.select_index);
            });
        }

        /// <summary>
        /// 育成終了API
        /// </summary>
        public void SendFinish(bool isForceDelete, int factorLotteryId, Action<SingleModeAPI.OnSendFinishSuccessDataContainer> onSendSuccess)
        {
            var req = new SingleModeLegendFinishRequest
            {
                is_force_delete = isForceDelete,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                factor_lottery_id = factorLotteryId
            };
            req.Send(response =>
            {
                var dataContainer = new SingleModeAPI.OnSendFinishSuccessDataContainer(response.data.single_mode_finish_common);
                onSendSuccess(dataContainer);
            });
        }

        public void SendFactorSelect(SingleModeGainSkillUpgrade[] singleModeGainSkillUpgradeArray, Action<SingleModeFactorSelectCommon, SingleModeFactorOrderCommon> onSuccess)
        {
            var upgradeSkillIdArray = singleModeGainSkillUpgradeArray?.Select(x => x.skill_id).ToArray();
            var req = new SingleModeLegendFactorSelectRequest
            {
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                skill_id_array = upgradeSkillIdArray,
                gain_skill_upgrade_info_array = singleModeGainSkillUpgradeArray,
                factor_order_request = TempData.Instance.SingleModeData.FactorOrderRequest,
            };

            req.Send(response =>
            {
                onSuccess(response.data.single_mode_factor_select_common, response.data.single_mode_factor_order_common);
            });
        }

        public void SendFactorLottery(int lotteryCount, TpInfo tpInfo, int useTp, int useCoin, int requestLotteryType, Action<SingleModeFactorLotteryCommon> onSuccess)
        {
            var req = new SingleModeLegendFactorLotteryRequest
            {
                lottery_count = lotteryCount,
                tp_info = tpInfo,
                use_tp = useTp,
                use_coin = useCoin,
                request_lottery_type = requestLotteryType
            };

            req.Send(response =>
            {
                onSuccess(response.data.single_mode_factor_lottery_common);
            });
        }
        
        /// <summary>
        /// 因子抽選再読み込みAPI
        /// </summary>
        public void SendFactorReload(int lotteryCount, Action<SingleModeFactorLotteryCommon> onSuccess)
        {
            var req = new SingleModeLegendFactorReloadRequest
            {
                lottery_count = lotteryCount,
            };

            req.Send(response =>
            {
                onSuccess(response.data.single_mode_factor_lottery_common);
            });
        }

        /// <summary>
        /// 因子再抽選終了API
        /// </summary>
        public void SendFactorLotteryEnd(Action onSuccess)
        {
            var req = new SingleModeLegendFactorRelotteryEndRequest();
            req.Send(_ => onSuccess());
        }

        /// <summary>
        /// 指定因子情報取得API
        /// </summary>
        public void SendFactorOrderLoad(Action<SingleModeFactorOrderLoadCommon> onSuccess)
        {
            new SingleModeLegendFactorOrderLoadRequest().Send(res => onSuccess(res.data.single_mode_factor_order_load_common));
        }

        /// <summary>
        /// レース出走登録API
        /// </summary>
        public void SendRaceEntry(int programId, Action onSuccess)
        {
            var req = new SingleModeLegendRaceEntryRequest
            {
                program_id = programId,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
            };
            req.Send(response =>
            {
                WorkDataManager.Instance.SingleMode.Apply(response.data);
                WorkDataManager.Instance.SingleMode.Character.EntryRace(programId);
                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// レース終了（結果確定）
        /// </summary>
        public IEnumerator SendRaceEndAsync(int execModeValue, Action onSuccess, Action<CharaRaceReward, LoginUserTrophyInfo, RaceRewardData, RewardSummaryInfo> onSendSuccess)
        {
            if (WorkDataManager.Instance.SingleMode.PlayingState == SingleModeDefine.PlayingState.LegendRaceInGame)
            {
                // シナリオレース
                yield return SendLegendScenarioRaceEndAsync(onSendSuccess);
                yield break;
            }
            
            yield return SingleModeSceneController.SendAsync<SingleModeLegendRaceEndRequest, SingleModeLegendRaceEndResponse>(
                new SingleModeLegendRaceEndRequest()
                {
                    exec_auto_play_plan_id = execModeValue,
                    current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
                },
                res =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(res.data);
                    onSendSuccess(res.data.race_reward_info, res.data.add_trophy_info, res.data.trophy_reward_info, res.data.reward_summary_info);
                });
        }

        /// <summary>
        /// レース退出API
        /// </summary>
        public void SendRaceOut(Action onSuccess)
        {
            if (WorkDataManager.Instance.SingleMode.PlayingState == SingleModeDefine.PlayingState.LegendRaceResult)
            {
                // シナリオレース
                SendLegendScenarioRaceOut(onSuccess);
                return;
            }
            
            var req = new SingleModeLegendRaceOutRequest();
            req.current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            req.Send(res =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// コンティニューAPI
        /// </summary>
        public IEnumerator SendContinueAsync(int continueType, Action onSuccess)
        {
            var req = new SingleModeLegendContinueRequest()
            {
                continue_type = continueType,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn()
            };
            yield return SingleModeSceneController.SendAsync<SingleModeLegendContinueRequest, SingleModeLegendContinueResponse>(
                req,
                response =>
                {
                    WorkDataManager.Instance.SingleMode.Apply(response.data);
                    WorkDataManager.Instance.ItemData.Update(response.data.user_item);
                    onSuccess?.Invoke();
                });
        }

        /// <summary>
        /// レース予約API
        /// </summary>
        public void SendMultiRaceReserve(int deckNum, string deckName, SingleModeReservedRace[] addRaces, SingleModeReservedRace[] cancelRaces, bool isDefault, System.Action<SingleModeReservedRaceInfo> onSuccess, bool stallOneSecond = false)
        {
            var req = new SingleModeLegendMultiRaceReserveRequest
            {
                deck_num = deckNum,
                deck_name = deckName,
                add_race_array = addRaces,
                cancel_race_array = cancelRaces,
                is_default = isDefault ? 1 : 0,
            };
            req.Send(res => onSuccess?.Invoke(res.data.reserved_race_info), stallOneSecond: stallOneSecond);
        }

        /// <summary>
        /// スキル習得API
        /// </summary>
        public void SendGainSkills(GainSkillInfo[] gainSkillArray, int execModeValue, Action onSuccess)
        {
            var req = new SingleModeLegendGainSkillsRequest
            {
                gain_skill_info_array = gainSkillArray,
                exec_auto_play_plan_id = execModeValue,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
            };
            req.Send(res =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// 短縮設定変更API
        /// </summary>
        public void SendChangeShortCut(SingleModeDefine.EventShortCutType shortcutType, bool isNonShortCutUncheckedEvent, Action onSuccess)
        {
            var req = new SingleModeLegendChangeShortCutRequest()
            {
                short_cut_state = (int)shortcutType,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),
                is_non_short_cut_unchecked_event = isNonShortCutUncheckedEvent ? 1 : 0,
            };
            req.Send((res) =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSuccess?.Invoke();
            }, stallOneSecond: true);
        }

        /// <summary>
        /// クレーンゲーム結果送信API
        /// </summary>
        public void SendFinishClawCrane(Plushie[] plushies, int currentTurn, Action onSuccess)
        {
            var req = new SingleModeLegendFinishClawCraneRequest()
            {
                plushies = plushies,
                current_turn = WorkDataManager.Instance.SingleMode.GetCurrentTurn(),

            };
            req.Send(res =>
            {
                WorkDataManager.Instance.SingleMode.Apply(res.data);
                onSuccess.Invoke();
            });
        }

        /// <summary>
        /// 育成レース開始リクエスト
        /// </summary>
        public void SendRaceStart(bool isShort, System.Action<string> onComplete)
        {
            if (WorkDataManager.Instance.SingleMode.PlayingState == SingleModeDefine.PlayingState.LegendRacePaddock)
            {
                // シナリオレース
                SendLegendScenarioRaceStart(isShort, onComplete);
                return;
            }

            var workSingle = WorkDataManager.Instance.SingleMode;
            var isShortValue = isShort ? 1 : 0;
            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var reqRaceStart = new SingleModeLegendRaceStartRequest();
            reqRaceStart.is_short = isShortValue; // 短縮レースフラグ（タスクキル再開地点がレースリザルトになるように）
            reqRaceStart.current_turn = currentTurn;
            HttpManager.Instance.Send<SingleModeLegendRaceStartRequest, SingleModeLegendRaceStartResponse>(
                reqRaceStart,
                res =>
                {
                    workSingle.Apply(res.data);
                    SingleModeSceneController.CreateRaceInfoByRaceStartInfo(res.data.race_start_info, res.data.race_scenario);
                    onComplete?.Invoke(res.data.race_scenario);
                });
        }

        /// <summary>
        /// レース分析リクエスト
        /// </summary>
        public void SendAnalyzeRequest(int programId, int current_turn, int year, Action<RaceHorseData[]> onAnalyzed = null)
        {
            var req = new SingleModeLegendRaceAnalyzeRequest()
            {
                program_id = programId,
                current_turn = current_turn,
            };
            Cute.Http.HttpManager.Instance.Send<SingleModeLegendRaceAnalyzeRequest, SingleModeLegendRaceAnalyzeResponse>(
                req,
                res =>
                {
                    // 馬番で昇順にソート。
                    var horseDataArray = res.data.race_horse_data_array;
                    RaceUtil.SortRaceHorseDataAsc(horseDataArray);
                    onAnalyzed(horseDataArray);
                });
        }

        /// <summary>
        /// 走法変更リクエスト
        /// </summary>
        public void SendChangeRunningStyleRequest(int raceProgramId, RaceDefine.RunningStyle runningStyle, int current_turn, System.Action onCompleteRequest, System.Action onError)
        {
            var req = new SingleModeLegendChangeRunningStyleRequest()
            {
                program_id = raceProgramId,
                running_style = (int)runningStyle,
                current_turn = current_turn,
            };

            HttpManager.Instance.Send<SingleModeLegendChangeRunningStyleRequest, SingleModeLegendChangeRunningStyleResponse>(
                req,
                (res) =>
                {
                    onCompleteRequest?.Invoke();
                },
                (error, val) =>
                {
                    onError?.Invoke();
                }
            );
        }

        /// <summary>
        /// 報酬情報取得リクエスト
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="onSuccess"></param>
        public void SendGetChoiceRewardRequest(int eventId, Action<EventChoiceReward[]> onSuccess)
        {
            var req = new SingleModeLegendGetChoiceRewardRequest()
            {
                event_id = eventId,
            };

            req.Send(res => onSuccess?.Invoke(res.data.choice_reward_array));
        }

        #region シナリオレース
        
        /// <summary>
        /// シナリオレース：レース出走登録
        /// </summary>
        public static void SendLegendScenarioRaceEntry(Action onComplete)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeLegendLegendRaceEntryRequest();
            req.current_turn = workSingle.GetCurrentTurn();
            req.Send(res =>
            {
                workSingle.Apply(res.data);
                onComplete?.Invoke();
            });
        }
        
        /// <summary>
        /// シナリオレース：レース開始リクエスト
        /// </summary>
        private void SendLegendScenarioRaceStart(bool isShort, Action<string> onComplete)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var isShortValue = isShort ? 1 : 0;
            var currentTurn = workSingle.GetCurrentTurn();
            var req = new SingleModeLegendLegendRaceStartRequest();
            req.is_short = isShortValue;
            req.current_turn = currentTurn;
            req.Send(res =>
            {
                workSingle.Apply(res.data);
                SingleModeSceneController.CreateRaceInfoByRaceStartInfo(res.data.race_start_info, res.data.race_scenario);
                onComplete?.Invoke(res.data.race_scenario);
            });            
        }
        
        /// <summary>
        /// シナリオレース：レース終了（結果確定）
        /// </summary>
        private IEnumerator SendLegendScenarioRaceEndAsync(Action<CharaRaceReward, LoginUserTrophyInfo, RaceRewardData, RewardSummaryInfo> onSendSuccess)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            yield return SingleModeSceneController.SendAsync<SingleModeLegendLegendRaceEndRequest, SingleModeLegendLegendRaceEndResponse>(
                new SingleModeLegendLegendRaceEndRequest()
                {
                    current_turn = workSingle.GetCurrentTurn()
                },
                res =>
                {
                    workSingle.Apply(res.data);
                    onSendSuccess(res.data.race_reward_info, res.data.add_trophy_info, res.data.trophy_reward_info, res.data.reward_summary_info);
                });            
        }

        /// <summary>
        /// シナリオレース：コンティニュー
        /// </summary>
        public static void SendLegendScenarioRaceContinue(int continueType, Action onSuccess)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeLegendLegendRaceContinueRequest();
            req.continue_type = continueType;
            req.current_turn = workSingle.GetCurrentTurn();
            req.Send(res =>
            {
                workSingle.Apply(res.data);
                WorkDataManager.Instance.ItemData.Update(res.data.user_item);
                onSuccess?.Invoke();
            });
        }
 
        /// <summary>
        /// シナリオレース：レース退出API
        /// </summary>
        private void SendLegendScenarioRaceOut(Action onSuccess)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeLegendLegendRaceOutRequest();
            req.current_turn = workSingle.GetCurrentTurn();
            req.Send(res =>
            {
                workSingle.Apply(res.data);
                onSuccess?.Invoke();
            });
        }
        
        #endregion
        
        /// <summary>
        /// CM終了API
        /// </summary>
        public static void SendLegendCmEnd(Action onComplete)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeLegendCmEndRequest();
            req.current_turn = workSingle.GetCurrentTurn();
            req.Send(res =>
            {
                workSingle.Apply(res.data);
                onComplete?.Invoke();
            });
        }
        
        /// <summary>
        /// 評判終了API
        /// </summary>
        public static void SendLegendPopularityEnd(Action onComplete)
        {
            var workSingle = WorkDataManager.Instance.SingleMode;
            var req = new SingleModeLegendPopularityEndRequest();
            req.current_turn = workSingle.GetCurrentTurn();
            req.Send(res =>
            {
                workSingle.Apply(res.data);
                onComplete?.Invoke();
            });
        }
    }

}