using Gallop.SingleMode.ScenarioLegend;

namespace Gallop
{
    using static SingleModeDefine;
    /// <summary>
    /// 伝説編：育成オリジナルレースの演出
    /// </summary>
    public static class SingleModeScenarioLegendStoryOriginalRaceA2U
    {
        public static IStoryOriginalRaceA2U IfNeedCreateStoryOriginalRaceStartA2U(DegreeType degreeType)
        {
            var prefabPath = GetOriginalRaceStartPrefabPath(degreeType);
            if (prefabPath.IsEmpty())
                return null;
            
            return CreateStoryOriginalRaceStartA2U(prefabPath);
        }
        
        public static IStoryOriginalRaceA2U IfNeedCreateStoryOriginalRaceWinA2U(DegreeType degreeType)
        {
            // 勝利演出を再生するかどうか
            if (IsNeedPlayStoryOriginalRaceWin() == false)
                return null;
            
            var prefabPath = GetOriginalRaceWinPrefabPath(degreeType);
            if (prefabPath.IsEmpty())
                return null;

            return CreateStoryOriginalRaceWinA2U(prefabPath);
        }
        
        /// <summary>
        /// SPレース開始
        /// </summary>
        private static IStoryOriginalRaceA2U CreateStoryOriginalRaceStartA2U(string prefabPath)
        {
            var playInfo = new StoryOriginalRaceA2UBase.PlayInfo(
                prefabPath,
                AudioId.SFX_ADDON01_START,
                GameDefine.A2U_IN_LABEL,
                GameDefine.A2U_OUT_LABEL);
            return new StoryOriginalRaceA2UBase(playInfo);
        }
        
        /// <summary>
        /// SPレース優勝
        /// </summary>
        private static IStoryOriginalRaceA2U CreateStoryOriginalRaceWinA2U(string prefabPath)
        {
            var playInfo = new StoryOriginalRaceA2UBase.PlayInfo(
                prefabPath,
                AudioId.SFX_ADDON01_START,
                GameDefine.A2U_IN_LABEL,
                GameDefine.A2U_OUT_LABEL);
            return new StoryOriginalRaceA2UBase(playInfo);
        }
        
        /// <summary>
        /// SPレース優勝演出を流す必要があるか
        /// </summary>
        /// <remarks>
        /// スクリプト工数の関係で、SPレース結果イベント(単一story_data)内に勝利分岐と敗北分岐が入っている
        /// そのため、show_clearにプラスして直近のSPレース結果を考慮して演出を流すか判定する
        /// </remarks>
        private static bool IsNeedPlayStoryOriginalRaceWin()
        {
            // 育成中でない場合は再生しない
            if (WorkDataManager.Instance.SingleMode.IsPlaying == false) return false;
            
            var scenarioRaceModel = ScenarioRaceRepository.Get();
            var lastRaceResult = scenarioRaceModel.GetLatestRaceResult();
            return lastRaceResult?.ResultRank == 1;
        }
        
        private static string GetOriginalRaceStartPrefabPath(DegreeType degreeType) => degreeType switch
        {
            DegreeType.Junior => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_extrarace_result00",
            DegreeType.Classic => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_extrarace_result00_01",
            DegreeType.Senior => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_extrarace_result00_02",
            _ => string.Empty
        };

        private static string GetOriginalRaceWinPrefabPath(DegreeType degreeType) => degreeType switch
        {
            DegreeType.Junior => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_extrarace_result01",
            DegreeType.Classic => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_extrarace_result01_01",
            DegreeType.Senior => ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_FLASH_COMBINE_ROOT + "fa_singlemode_legend_extrarace_result01_02",
            _ => string.Empty
        };
    }
}