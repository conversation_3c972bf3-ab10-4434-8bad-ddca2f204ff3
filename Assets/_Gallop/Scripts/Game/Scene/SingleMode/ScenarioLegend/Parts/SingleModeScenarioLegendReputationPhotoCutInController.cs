using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Gallop.CutIn;
using Gallop.CutIn.Cutt;
using Gallop.RenderPipeline;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering;
using UnityEngine.UI;

namespace Gallop
{
    public interface ISingleModeScenarioLegendReputationPhotoCutInController
    {
        public void Initialize(Transform parentTransform, ModelController modelController = null, GameDefine.BgSeason season = GameDefine.BgSeason.None, List<string> cuttPathList = null, bool isNeedOnAfterCapturePhoto = true);
        public IEnumerator PreparePlayCutIn(Action onComplete);
        public void PlayCutIn(Action onComplete);
        public void AlterUpdate();
        public void AlterLateUpdate();
        public void Destroy();

    }
    public class SingleModeScenarioLegendReputationPhotoCutInController : ISingleModeScenarioLegendReputationPhotoCutInController
    {
        public const int PHOTO_RUNNING_PAGE_INDEX = 1;
        private const int PHOTO_TYPE_MAX = 5;
        private const int PHOTO_PAGE_MAX = 7;
        // 本はA4サイズ（595×842）なのでアスペクト比はおよそ「1:1.414」
        private const int BOOK_WIDTH = 595;
        private const int BOOK_HEIGHT = 842;
        private const float BOOK_ASPECT = BOOK_HEIGHT / (float)BOOK_WIDTH;
        private const int OBI_HEIGHT = 142;
        private const int OBI_TEXT_WIDTH = 430;
        private const int OBI_TEXT_HEIGHT = 78;
        private const int OBI_ICON_WIDTH = 112;
        private const int OBI_ICON_HEIGHT = 120;
        private const int OBI_ICON_OFFSET = 28;

        private CutInTimelinePhotoCollectionController _cutInController;
        public CutInTimelinePhotoCollectionController CutInController => _cutInController;

        private ModelController _overriderCuttModel = null;

        private Camera _uiCamera;
        private Canvas _uiCanvas;

        private Camera _obiCamera;
        private Canvas _obiCanvas;
        private RenderTexture _obiRenderTexture;

        private RawImageCommon _bookCoverRawImage;
        private RectTransform _bookCoverRectTransform;
        private RawImageCommon _bookSpineRawImage;
        private RectTransform _bookSpineRectTransform;
        private TextCommon _bookSpineText;
        private RectTransform _bookSpineTextRectTransform;

        private TextCommon _bookCoverNameText;
        private RectTransform _bookCoverNameTextRectTransform;
        private TextCommon _bookCoverNameTextShadow;
        private RectTransform _bookCoverNameTextShadowRectTransform;

        private TextCommon _bookCoverTitleText;
        private RectTransform _bookCoverTitleTextRectTransform;
        private TextCommon _bookCoverTitleTextShadow;
        private RectTransform _bookCoverTitleTextShadowRectTransform;

        private RawImageCommon _bookCoverTextBgRawImage;
        private RectTransform _bookCoverTextBgRectTransform;

        private RawImageCommon _bookObiRawImage;
        private RectTransform _bookObiRectTransform;
        private RawImageCommon _bookObiTextRawImage;
        private RectTransform _bookObiTextRectTransform;
        private RawImageCommon _bookObiIconRawImage;
        private RectTransform _bookObiIconRectTransform;

        private SingleModeScenarioLegendReputationPhotoTextParam _textParam;

        //156140 カットの差分モーション設定アセット
        private SetCuttSwapMotionData _setCuttSwapMotionData;

        public static void RegisterDownload(DownloadPathRegister register, int charaId)
        {
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_BOOK);
            for (int typeIndex = 0; typeIndex < PHOTO_TYPE_MAX; typeIndex++)
            {
                for (int pageIndex = 0; pageIndex < PHOTO_PAGE_MAX; pageIndex++)
                {
                    // 1ページ目だけ構成が異なる
                    if (pageIndex == PHOTO_RUNNING_PAGE_INDEX)
                        continue;

                    register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioLegendReputationPhotoCutInPagePath(typeIndex, pageIndex));
                }
            }

            register.RegisterPathWithoutInfo(ResourcePath.TSUKUSHI_MINSHO_PRO_FONT_PATH);

            // 表紙用
            RegisterDownloadPage00(register, charaId);
            // 1ページ目用
            RegisterDownloadPage01(register, charaId);
            // カットで使う走りモーション（run02,run03,run05）
            SetCutInHelper.RegisterDownload(register);
        }

        private static void RegisterDownloadPage00(DownloadPathRegister register, int charaId)
        {
            // 表紙
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_COVER_TEXT_BG_IMAGE);

            // 帯
            register.RegisterPathWithoutInfo(GetObiTextImagePath(charaId));
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_OBI_EMBLEM_IMAGE);

            // レイアウト設定
            register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_TEXT_PARAM);
        }

        private static void RegisterDownloadPage01(DownloadPathRegister register, int charaId)
        {
            // 1ページ目用
            var (personalityType, runningType) = SingleModeScenarioLegendCuttUtils.GetCharaRacePersonalityAndRunningType(charaId);
            register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioLegendReputationPhotoCutInPage01Path(personalityType, runningType));
        }

        public void Initialize(Transform parentTransform, ModelController modelController, GameDefine.BgSeason season,
            List<string> cuttPathList = null, bool isNeedOnAfterCapturePhoto = true)
        {
            _cutInController = new CutInTimelinePhotoCollectionController();

            _overriderCuttModel = modelController;
            // 各ページを撮り終えるまで消しておく
            _overriderCuttModel.SetActiveWithCheck(false);

            //156140 一連のキャプチャのためのカットに差分モーションを適用
            if (_setCuttSwapMotionData == null)
            {
                _setCuttSwapMotionData = SetCuttSwapMotionData.LoadSwapData();
            }

            var context = new CutInTimelinePhotoCollectionController.Context
            {
                ParentCutInHelper = GetNewCutInHelper(),
                ParentCutInPath = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_CUTIN_BOOK,
                PhotoCutInHelperList = Enumerable.Range(0, PHOTO_PAGE_MAX)
                    .Select(GetNewPageCutInHelper)
                    .ToList(),
                //カットのパスは外部からの指定があればそれを利用
                PhotoCutInPathList = cuttPathList.IsNullOrEmpty() ? Enumerable.Range(0, PHOTO_PAGE_MAX)
                    .Select(GetPageCutInPath)
                    .ToList() : cuttPathList,
                ParentTransform = parentTransform,
                OnAfterCapturePhoto = isNeedOnAfterCapturePhoto ? OnAfterCapturePhoto : null,
                Season = season,
                RenderTextureSize = GetPhotoRenderTextureSize(),
                //156140 差分モーションを適用に必要なデータと処理を渡す
                SwapMotionData = _setCuttSwapMotionData,
                GetPageDressId = GetPageDressId,
                CharaId = modelController.GetCharaID(),
            };
            _cutInController.Initialize(context);

            LoadTextParameter();

            return;

            CutInHelper GetNewCutInHelper()
            {
                var cutinHelper = new SetCutInHelper();
                //登場するキャラモデルは外部から指定可能にする
                cutinHelper.GetUserModelControllerAction += GetUserModelControllerAction;
                //154239 写真集のRootTypeのPropに表紙と帯のテクスチャを差し替える必要があるので、独自に設定
                cutinHelper.CustomSwapCharaPropExternalTexture = SwapPropExternalTexture;
                return cutinHelper;
            }

            CutInHelper GetNewPageCutInHelper(int pageIndex)
            {
                var cutinHelper = new SetCutInHelper();
                //各ページに登場するキャラモデルは外部から衣装を指定可能にする
                cutinHelper.GetUserModelControllerAction += (info) => GetUserModelControllerActionPage(info, pageIndex);
                return cutinHelper;
            }
        }

        /// <summary>
        /// 写真集のPropに張り付けるためのRTサイズを取得
        /// </summary>
        /// <returns></returns>
        private Vector2Int GetPhotoRenderTextureSize()
        {
            var resolution3D = GraphicSettings.Instance.GetVirtualResolution3D();
            //解像度を写真集の比率に合わせてリサイズする
            resolution3D.y = Mathf.CeilToInt(resolution3D.x * BOOK_ASPECT);
            resolution3D.x = Mathf.CeilToInt(resolution3D.y / BOOK_ASPECT);

            return resolution3D;
        }

        private string GetPageCutInPath(int pageIndex)
        {
            if (pageIndex < 0 || pageIndex > PHOTO_PAGE_MAX)
            {
                Debug.LogError($"ページIndexが範囲外です pageIndex:{pageIndex}");
                return null;
            }

            var charaId = GameDefine.INVALID_CHARA_ID;
            if (_overriderCuttModel != null)
            {
                charaId = _overriderCuttModel.GetCharaID();
            }

            if (charaId == GameDefine.INVALID_CHARA_ID)
            {
                Debug.LogError($"キャラIDが不明なため写真集のPathが取得できません");
                return null;
            }


            // 01ページ目だけ構成が異なる
            if (pageIndex == PHOTO_RUNNING_PAGE_INDEX)
            {
                var (personalityType, runningType) = SingleModeScenarioLegendCuttUtils.GetCharaRacePersonalityAndRunningType(charaId);
                return ResourcePath.GetSingleModeScenarioLegendReputationPhotoCutInPage01Path(personalityType, runningType);
            }
            else
            {
                // PhotoBookCoverから連番
                var targetCutt = SingleModeScenarioLegendDefine.ScenarioLegendCuttType.PhotoBookCover + pageIndex;
                var cuttCharaType = SingleModeScenarioLegendCuttUtils.GetCuttCharaType(charaId, targetCutt);
                return ResourcePath.GetSingleModeScenarioLegendReputationPhotoCutInPagePath(cuttCharaType, pageIndex);
            }
        }

        public IEnumerator PreparePlayCutIn(Action onComplete)
        {
            yield return _cutInController.PreparePlayCutIn();

            onComplete.Call();
        }
        public IEnumerator PreparePlayCutInWithoutCapture(Action onComplete, List<RenderTexture> preCaptureTextureList)
        {
            yield return _cutInController.PreparePlayCutInWithoutCapture(preCaptureTextureList);

            onComplete.Call();
        }
        
        /// <summary>
        /// 事前撮影したテクスチャを外部から取得する
        /// </summary>
        /// <returns></returns>
        public List<RenderTexture> GetRenderTextureList()
        {
            return _cutInController.PhotoRenderTextureList;
        }

        public void PlayCutIn(Action onComplete)
        {
            _cutInController.PlayCutIn(onComplete);
        }

        public void AlterUpdate()
        {
            _cutInController.AlterUpdate();
        }

        public void AlterLateUpdate()
        {
            _cutInController.AlterLateUpdate();
        }

        public void Destroy()
        {
            if (_cutInController != null)
            {
                _cutInController.DestroyCutIn();
                _cutInController = null;
            }

            //156140　差分モーションアセットを解放
            if (_setCuttSwapMotionData != null)
            {
                _setCuttSwapMotionData.UnLoad();
                _setCuttSwapMotionData = null;
            }

            _overriderCuttModel = null;
        }

        private ModelController GetUserModelControllerAction(CutInCharacterCreateInfo info)
        {
            //育成キャラの場合そのまま外部から来たモデルを渡す
            if (info._characterType == TimelineKeyCharacterType.User && info._charaIndex == 0)
            {
                return _overriderCuttModel;
            }
            else
            {
                return null;
            }
        }

        private ModelController GetUserModelControllerActionPage(CutInCharacterCreateInfo info, int pageIndex)
        {
            //育成キャラの場合ページ毎に指定された衣装IDに変える
            if (info._characterType == TimelineKeyCharacterType.User && info._charaIndex == 0)
            {
                var charaId = _overriderCuttModel != null ? _overriderCuttModel.GetCharaID() : GameDefine.INVALID_CHARA_ID;
                info._charaId = charaId;
                info._clothId = GetPageDressId(charaId, pageIndex);
                // Cuttツール上の頭部ID設定が入ってしまうとマスターで設定された衣装IDに対応する頭部IDが適用されないのでデフォルトに戻す
                info.IsUseDressDataHeadModelSubId = true;
                info._headId = ModelLoader.DEFAULT_HEAD_SUB_ID;
            }

            return null;
        }

        private int GetPageDressId(int charaId, int pageIndex)
        {
            // 表紙・・・・・私服
            // ページ01・・・勝負服
            // ページ02・・・夏用ジャージ
            // ページ03・・・スターティングフューチャー
            // ページ04・・・冬用ジャージ
            // ページ05・・・夏用制服
            // ページ06・・・冬用ジャージ
            var dressId = pageIndex switch
            {
                0 => GetPrivateDressId(charaId),
                1 => GetRaceDressId(),
                2 => GetJerseySummerDressId(),
                3 => GetStartingFeatureDressId(),
                4 => GetJerseyWinterDressId(),
                5 => GetUniformSummerDressId(),
                6 => GetJerseyWinterDressId(),
                _ => GameDefine.INVALID_DRESS_ID
            };

            return dressId;

            int GetPrivateDressId(int charaId)
            {
                // 私服IDは「90 + {ID4桁}」の形式 例：901001
                const int PRIVATE_DRESS_ID_OFFSET = 900000;

                return PRIVATE_DRESS_ID_OFFSET + charaId;
            }

            int GetRaceDressId()
            {
                // 渡された育成キャラモデルの衣装IDを使う
                // 衣装変更機能などが反映されている前提
                return _overriderCuttModel != null ? _overriderCuttModel.GetDressId() : (int)ModelLoader.DressID.SRCommon;
            }

            int GetJerseySummerDressId()
            {
                return (int)ModelLoader.DressID.JerseySummer;
            }

            int GetJerseyWinterDressId()
            {
                return (int)ModelLoader.DressID.JerseyWinter;
            }

            int GetStartingFeatureDressId()
            {
                return (int)ModelLoader.DressID.SRCommon;
            }

            int GetUniformSummerDressId()
            {
                return (int)ModelLoader.DressID.UniformSummer;
            }
        }

        /// <summary>
        /// 写真集のRoot Propにテクスチャの差し替え対応
        /// </summary>
        /// <param name="propController"></param>
        /// <param name="swapExternalTexture"></param>
        private void SwapPropExternalTexture(TimelineCharaProp prop)
        {
            if (prop.PropCtrl == null || prop.PropCtrl.AssetHolder == null)
            {
                return;
            }

            var objectList = prop.PropCtrl.AssetHolder.ObjectList;

            if (objectList.IsNullOrEmpty())
            {
                return;
            }

            //表紙
            const string KEYWORD_COVER = "cover";
            //帯
            const string KEYWORD_OBI = "obi";

            //表紙テクスチャのインデックス
            const int BOOK_COVER_TEXTURE_INDEX = 0;

            //表紙と帯用テクスチャを取得しておく
            var coverTexture = _cutInController.CutInHelper?.GetExternalPropTextureAction?.Invoke(BOOK_COVER_TEXTURE_INDEX);
            var obiTexture = _obiRenderTexture;

            foreach (var obj in objectList)
            {
                Texture swapTexture = null;

                if (obj.Key.Contains(KEYWORD_COVER, StringComparison.OrdinalIgnoreCase))
                {
                    //表紙はキャプチャ画像を読み込む
                    swapTexture = coverTexture;
                }
                else if (obj.Key.Contains(KEYWORD_OBI, StringComparison.OrdinalIgnoreCase))
                {
                    swapTexture = obiTexture;
                }
                else
                {
                    continue;
                }

                var rendererIndex = prop.PropCtrl.GetRendererIndex((obj.Value as GameObject));
                prop.PropCtrl.SwapExternalTexture(swapTexture, rendererIndex);
            }
        }

        /// <summary>
        /// 各ページの撮影後の追加処理
        /// </summary>
        private IEnumerator OnAfterCapturePhoto(int pageIndex, RenderTexture renderTexture, Action<RenderTexture> onNewRenderTexture)
        {
            // 表紙(index:0)だけ追加処理
            if (pageIndex == 0)
            {
                // 表紙用カットキャプチャを元に追加加工を行い、出来上がったテクスチャをカットに返す
                // _uiCamera.targetTextureに差した時に、CanvasサイズがRenderTextureのサイズに連動するので表紙+背表紙のサイズで作り、差しておく
                var bookCoverRenderTexture = RuntimeObjectManager.NewRenderTextureOnView((int)(BOOK_WIDTH / 0.9f), BOOK_HEIGHT, depth: 0);
                bookCoverRenderTexture.DebugSetName($"{renderTexture.name}_with_text");
                _obiRenderTexture = RuntimeObjectManager.NewRenderTextureOnView((int)(BOOK_WIDTH / 0.9f), OBI_HEIGHT, depth: 0);
                _obiRenderTexture.DebugSetName("ObiRenderTexture");

                CreateTextCameraAndCanvas();

                _uiCamera.targetTexture = bookCoverRenderTexture;

                CreateObiCameraAndCanvas();
                _obiCamera.targetTexture = _obiRenderTexture;
                // targetTextureに設定してから、Canvasサイズが変わるまでに1フレかかる
                yield return UIManager.WaitForEndOfFrame;

                CreateBookCoverObjects(renderTexture);

                // 表紙用のRenderTextureへ出力待ち
                yield return UIManager.WaitForEndOfFrame;

                _uiCamera.enabled = false;
                _obiCamera.enabled = false;

                // 新しく作ったRenderTextureを返す
                onNewRenderTexture.Call(bookCoverRenderTexture);

                // 帯用のRenderTextureをリストに追加
                _cutInController.AddRenderTexture(_obiRenderTexture);
                yield break;
            }
            else
            {
                //カット側が持つRTはすでに写真集に合ったアスペクト比になっているので、
                //ここで特に何もしない
                yield break;
            }
        }

        private void CreateTextCameraAndCanvas()
        {
            _uiCamera = CutInHelper.CreateMonitorUICamera(_cutInController.CutInParent.transform);
            _uiCamera.gameObject.DebugSetName("BookCoverCamera");
            _uiCanvas = CutInHelper.CreateMonitorUICanvas(_cutInController.CutInParent.transform, _uiCamera);
            _uiCanvas.gameObject.DebugSetName("BookCoverCanvas");
        }

        private void CreateObiCameraAndCanvas()
        {
            // 表紙のカメラと被らないようにずらす
            const float OFFSET_X = 100f;
            _obiCamera = CutInHelper.CreateMonitorUICamera(_cutInController.CutInParent.transform);
            _obiCamera.gameObject.DebugSetName("ObiCamera");
            _obiCamera.transform.localPosition += new Vector3(OFFSET_X, 0, 0);
            _obiCanvas = CutInHelper.CreateMonitorUICanvas(_cutInController.CutInParent.transform, _obiCamera);
            _obiCanvas.gameObject.DebugSetName("ObiCanvas");
        }

        /// <summary>
        /// 背表紙つきの表紙テクスチャを作る用のオブジェクトを生成
        /// </summary>
        /// <param name="srcTexture"></param>
        private void CreateBookCoverObjects(Texture srcTexture)
        {
            // 撮影した表紙部分
            CreateBookCoverImageObject(srcTexture);

            // 帯
            CreateBookObiObjects();

            // 表紙のテキスト
            CreateBookCoverNameTextObject();

            // 設定アセットを反映
            ApplyTextParameter();

            // 縦書き用のRectTransform更新
            UpdateVerticalTextRectTransform();

            // 追加する背表紙部分
            CreateBookSpineImageObject();

            // 背表紙のテキスト
            CreateBookSpineTextObject();

            // 縦書き用のRectTransform更新
            UpdateVerticalTextRectTransform();
        }

        private void CreateBookCoverImageObject(Texture srcTexture)
        {
            var bookCoverObj = new GameObject();
            bookCoverObj.DebugSetName("BookCoverImage");
            bookCoverObj.transform.SetParent(_uiCanvas.transform);
            bookCoverObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookCoverRectTransform = bookCoverObj.AddComponent<RectTransform>();
            // 左寄せで 0.0 ~ 0.9 のエリアに表紙
            _bookCoverRectTransform.anchorMin = Vector2.zero;
            _bookCoverRectTransform.anchorMax = new Vector2(0.9f, 1f);
            _bookCoverRectTransform.anchoredPosition3D = Vector3.zero;
            _bookCoverRectTransform.sizeDelta = Vector2.zero;
            _bookCoverRectTransform.localScale = Vector3.one;
            _bookCoverRawImage = bookCoverObj.AddComponent<RawImageCommon>();
            _bookCoverRawImage.texture = srcTexture;
        }

        private void CreateBookCoverNameTextObject()
        {
            // 背景
            var bookCoverTextBgObj = new GameObject();
            bookCoverTextBgObj.DebugSetName("BookCoverTextBg");
            bookCoverTextBgObj.transform.SetParent(_bookCoverRectTransform.transform);
            bookCoverTextBgObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookCoverTextBgRectTransform = bookCoverTextBgObj.AddComponent<RectTransform>();
            // 画像設定
            _bookCoverTextBgRawImage = bookCoverTextBgObj.AddComponent<RawImageCommon>();
            var imageTexture = ResourceManager.LoadOnView<Texture>(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_COVER_TEXT_BG_IMAGE);
            if (imageTexture != null)
            {
                _bookCoverTextBgRawImage.texture = imageTexture;
            }
            else
            {
                _bookCoverTextBgRawImage.gameObject.SetActiveWithCheck(false);
            }

            // 縦書きで「ウマ娘名 + フォトブック」というテキストを乗せる
            var bookCoverTextObj = new GameObject();
            bookCoverTextObj.DebugSetName("BookCoverNameText");
            bookCoverTextObj.transform.SetParent(_bookCoverRectTransform.transform);
            bookCoverTextObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookCoverNameTextRectTransform = bookCoverTextObj.AddComponent<RectTransform>();
            _bookCoverNameTextRectTransform.anchoredPosition3D = Vector3.zero;
            _bookCoverNameTextRectTransform.localScale = Vector3.one;

            // キャラIDをマスター参照して名前をセット
            _bookCoverNameText = bookCoverTextObj.AddComponent<TextCommon>();
            var charaId = _overriderCuttModel != null ? _overriderCuttModel.GetCharaID() : GameDefine.INVALID_CHARA_ID;
            var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (charaData != null)
            {
                _bookCoverNameText.text = charaData.Name;
            }
            else
            {
                Debug.LogError("ウマ娘名情報が取得できません");
                _bookCoverNameText.text = string.Empty;
            }
            _bookCoverNameText.FontColor = FontColorType.White;
            _bookCoverNameText.alignment = TextAnchor.UpperLeft;
            _bookCoverNameText.font = null; // 一度Nullにしないと反映されない
            _bookCoverNameText.FontType = TextFormat.Font.TsukuMinProE;

            // 縦書き用設定
            // 縦書きテキストはRectTransformを90度回転さてRotateTextを付ける必要がある
            _bookCoverNameTextRectTransform.localRotation = Quaternion.Euler(0, 0, -90);
            bookCoverTextObj.AddComponent<RotateText>();

            // シャドウ文字として複製
            CreateTextShadowObject(_bookCoverNameText, out _bookCoverNameTextShadow, out _bookCoverNameTextShadowRectTransform);

            // レイアウトが違うので「フォトブック」は分けて作る
            var bookCoverTitleObj = new GameObject();
            bookCoverTitleObj.DebugSetName("BookCoverTitleText");
            bookCoverTitleObj.transform.SetParent(_bookCoverRectTransform.transform);
            bookCoverTitleObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookCoverTitleTextRectTransform = bookCoverTitleObj.AddComponent<RectTransform>();
            _bookCoverTitleTextRectTransform.anchoredPosition3D = Vector3.zero;
            _bookCoverTitleTextRectTransform.localScale = Vector3.one;
            _bookCoverTitleText = bookCoverTitleObj.AddComponent<TextCommon>();
            // 背表紙に使っている文字から空白を消して流用
            _bookCoverTitleText.text = TextId.SingleModeScenarioLegend510001.Format(string.Empty).Trim();
            _bookCoverTitleText.FontColor = FontColorType.White;
            _bookCoverTitleText.alignment = TextAnchor.UpperLeft;
            _bookCoverTitleText.font = null; // 一度Nullにしないと反映されない
            _bookCoverTitleText.FontType = TextFormat.Font.TsukuMinProE;

            // 縦書き用設定
            _bookCoverTitleTextRectTransform.localRotation = Quaternion.Euler(0, 0, -90);
            bookCoverTitleObj.AddComponent<RotateText>();

            // シャドウ文字として複製
            CreateTextShadowObject(_bookCoverTitleText, out _bookCoverTitleTextShadow, out _bookCoverTitleTextShadowRectTransform);
        }

        private void CreateTextShadowObject(TextCommon original, out TextCommon shadowText, out RectTransform shadowRectTransform)
        {
            // 元のTextCommonを複製してシャドウ文字にする
            var shadowObj = GameObject.Instantiate(original.gameObject, original.transform.parent);
            shadowObj.DebugSetName($"{original.name}_shadow");
            shadowRectTransform = shadowObj.GetComponent<RectTransform>();

            // 色設定
            shadowText = shadowObj.GetComponent<TextCommon>();
            shadowText.VerticalGradientColor = VerticalGradientColorType.SingleModeLegendPhotoBook;
            shadowText.UpdateColor();
        }

        private void UpdateCanvasForBookCoverUI()
        {
            // CanvasScalerの設定
            var canvasScaler = _uiCanvas.GetComponent<CanvasScaler>();

            canvasScaler.referenceResolution = new Vector2(BOOK_WIDTH, BOOK_HEIGHT);
            canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            canvasScaler.matchWidthOrHeight = 1.0f;
            Canvas.ForceUpdateCanvases();
        }

        private void UpdateCanvasForBookObiUI()
        {
            // CanvasScalerの設定
            var canvasScaler = _obiCanvas.GetComponent<CanvasScaler>();
            canvasScaler.referenceResolution = new Vector2(BOOK_WIDTH, OBI_HEIGHT);
            canvasScaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            canvasScaler.matchWidthOrHeight = 1.0f;
            Canvas.ForceUpdateCanvases();
        }

        private void CreateBookSpineImageObject()
        {
            var bookSpineObj = new GameObject();
            bookSpineObj.DebugSetName("BookSpineImage");
            bookSpineObj.transform.SetParent(_uiCanvas.transform);
            bookSpineObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookSpineRectTransform = bookSpineObj.AddComponent<RectTransform>();
            // 左寄せで 0.9 ~ 1.0 のエリアに背表紙
            _bookSpineRectTransform.anchorMin = new Vector2(0.9f, 0f);
            _bookSpineRectTransform.anchorMax = Vector2.one;
            _bookSpineRectTransform.anchoredPosition3D = Vector3.zero;
            _bookSpineRectTransform.sizeDelta = Vector2.zero;
            _bookSpineRectTransform.localScale = Vector3.one;
            _bookSpineRawImage = bookSpineObj.AddComponent<RawImageCommon>();
            // nullにして真っ白背景にする
            _bookSpineRawImage.texture = null;
        }

        private void CreateBookSpineTextObject()
        {
            // 縦書きで「ウマ娘名 + フォトブック」というテキストを乗せる
            var bookSpineTextObj = new GameObject();
            bookSpineTextObj.DebugSetName("BookSpineText");
            bookSpineTextObj.transform.SetParent(_bookSpineRectTransform);
            bookSpineTextObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            var bookSpineTextRectTransform = bookSpineTextObj.AddComponent<RectTransform>();
            bookSpineTextRectTransform.anchoredPosition3D = Vector3.zero;
            bookSpineTextRectTransform.localScale = Vector3.one;

            // NOTE:親のRectTransformを横に90度回転させたものを基準にしたいが、
            // 端末の画面サイズによってCanvasから設定されるサイズが変わるので、Canvasを強制更新して反映させておく
            const float ADJUST_SIZE_RATE = 0.9f;
            Canvas.ForceUpdateCanvases();
            var parentRect = _bookSpineRectTransform.rect;
            bookSpineTextRectTransform.sizeDelta = new Vector2(parentRect.height * ADJUST_SIZE_RATE, parentRect.width * ADJUST_SIZE_RATE);

            // キャラIDをマスター参照して名前をセット
            var bookSpineTextCommon = bookSpineTextObj.AddComponent<TextCommon>();
            var charaId = _overriderCuttModel != null ? _overriderCuttModel.GetCharaID() : GameDefine.INVALID_CHARA_ID;
            var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (charaData != null)
            {
                // 「{ウマ娘名}  フォトブック」
                bookSpineTextCommon.text = TextId.SingleModeScenarioLegend510001.Format(charaData.Name);
            }
            else
            {
                Debug.LogError("ウマ娘名情報が取得できません");
                bookSpineTextCommon.text = string.Empty;
            }
            bookSpineTextCommon.FontColor = FontColorType.Black;
            bookSpineTextCommon.FontSizeFormat = TextFormat.FontSize.Size_32;
            bookSpineTextCommon.alignment = TextAnchor.MiddleLeft;
            bookSpineTextCommon.font = null; // 一度Nullにしないと反映されない
            bookSpineTextCommon.FontType = TextFormat.Font.TsukuMinProE;

            //155833 fontにfontsizeformat設定後一度Update関数を呼ばないとサイズの反映ができないので、
            //ここで呼んでおく
            bookSpineTextCommon.OnUpdate();

            // 縦書き用設定
            // 縦書きテキストはRectTransformを90度回転さてRotateTextを付ける必要がある
            bookSpineTextRectTransform.localRotation = Quaternion.Euler(0, 0, -90);
            bookSpineTextObj.AddComponent<RotateText>();
        }

        private void CreateBookObiObjects()
        {
            // 帯下地
            var bookObiObj = new GameObject();
            bookObiObj.DebugSetName("BookObiImage");
            bookObiObj.transform.SetParent(_obiCanvas.transform);
            bookObiObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookObiRectTransform = bookObiObj.AddComponent<RectTransform>();
            _bookObiRectTransform.anchorMin = Vector2.zero;
            _bookObiRectTransform.anchorMax = Vector2.one;
            _bookObiRectTransform.sizeDelta = Vector2.zero;
            _bookObiRectTransform.anchoredPosition3D = new Vector3(0f, 0f, 0f);
            _bookObiRectTransform.localScale = Vector3.one;
            _bookObiRawImage = bookObiObj.AddComponent<RawImageCommon>();
            // nullにして単色背景にする
            _bookObiRawImage.texture = null;

            // キャラのCSVにある色を使う
            var charaId = _overriderCuttModel != null ? _overriderCuttModel.GetCharaID() : GameDefine.INVALID_CHARA_ID;
            var charaColor = CutInTimelineController.GetCharacterUiMainColor(charaId);
            _bookObiRawImage.color = charaColor;

            // キャッチコピー
            var bookObiTextObj = new GameObject();
            bookObiTextObj.DebugSetName("BookObiTextImage");
            bookObiTextObj.transform.SetParent(_bookObiRectTransform);
            bookObiTextObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookObiTextRectTransform = bookObiTextObj.AddComponent<RectTransform>();
            _bookObiTextRectTransform.anchorMin = new Vector2(0.9f, 0.5f);
            _bookObiTextRectTransform.anchorMax = new Vector2(0.9f, 0.5f);
            _bookObiTextRectTransform.sizeDelta = new Vector2(OBI_TEXT_WIDTH, OBI_TEXT_HEIGHT);
            _bookObiTextRectTransform.anchoredPosition3D = new Vector3(-OBI_TEXT_WIDTH / 2f, 0f, 0f);
            _bookObiTextRectTransform.localScale = Vector3.one;
            _bookObiTextRawImage = bookObiTextObj.AddComponent<RawImageCommon>();
            // キャラタイプに応じたキャッチコピー画像にする
            var bookObiTextTexture = GetObiTextImage(charaId);
            if (bookObiTextTexture != null)
            {
                _bookObiTextRawImage.texture = bookObiTextTexture;
            }
            else
            {
                _bookObiTextRawImage.SetActiveWithCheck(false);
            }

            // アイコン
            var bookObiIconObj = new GameObject();
            bookObiIconObj.DebugSetName("BookObiIconImage");
            bookObiIconObj.transform.SetParent(_bookObiRectTransform);
            bookObiIconObj.layer = GraphicSettings.GetLayer(GraphicSettings.LayerIndex.Layer3DUI);
            _bookObiIconRectTransform = bookObiIconObj.AddComponent<RectTransform>();
            _bookObiIconRectTransform.anchorMin = new Vector2(0f, 0.5f);
            _bookObiIconRectTransform.anchorMax = new Vector2(0f, 0.5f);
            _bookObiIconRectTransform.sizeDelta = new Vector2(OBI_ICON_WIDTH, OBI_ICON_HEIGHT);
            _bookObiIconRectTransform.anchoredPosition3D = new Vector3(OBI_ICON_WIDTH / 2f + OBI_ICON_OFFSET, 0f, 0f);
            _bookObiIconRectTransform.localScale = Vector3.one;
            _bookObiIconRawImage = bookObiIconObj.AddComponent<RawImageCommon>();
            var bookObiIconTexture = ResourceManager.LoadOnView<Texture>(ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_OBI_EMBLEM_IMAGE);
            if (bookObiIconTexture != null)
            {
                _bookObiIconRawImage.texture = bookObiIconTexture;
            }
            else
            {
                _bookObiIconRawImage.SetActiveWithCheck(false);
            }
        }

        private static string GetObiTextImagePath(int charaId)
        {
            var charaType = SingleModeScenarioLegendCuttUtils.GetCuttCharaType(charaId, SingleModeScenarioLegendDefine.ScenarioLegendCuttType.PhotoBookObi);
            // 画像名は0始まりなので-1
            var index = charaType - 1;
            var path = ResourcePath.GetSingleModeScenarioLegendReputationPhotoBookObiTextImage(index);

            return path;
        }

        /// <summary>
        /// 帯のテキストを画像として取得
        /// </summary>
        private static Texture GetObiTextImage(int charaId)
        {
            var path = GetObiTextImagePath(charaId);
            var texture = ResourceManager.LoadOnView<Texture>(path);

            return texture;
        }

        private void LoadTextParameter()
        {
            const string ASSET_PATH = ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_REPUTATION_PHOTO_BOOK_TEXT_PARAM;
            _textParam = ResourceManager.LoadOnView<SingleModeScenarioLegendReputationPhotoTextParam>(ASSET_PATH);
        }

        private void ApplyTextParameter()
        {
            if (_textParam == null)
                return;

            // キャラ名
            var rectInfo = _textParam.NameRectInfo;
            var textInfo = _textParam.NameTextCommonInfo;
            ApplyRectInfo(_bookCoverNameTextRectTransform, rectInfo);
            ApplyTextInfo(_bookCoverNameText, textInfo);
            ApplyNameTextSize(_bookCoverNameText, _textParam.NameTextLengthThreshold, _textParam.NameTextSizeShort, _textParam.NameTextSizeLong);

            // キャラ名シャドウ
            var shadowInfo = _textParam.TextShadowOffset;
            ApplyRectInfo(_bookCoverNameTextShadowRectTransform, rectInfo);
            ApplyTextInfo(_bookCoverNameTextShadow, textInfo);
            ApplyNameTextSize(_bookCoverNameTextShadow, _textParam.NameTextLengthThreshold, _textParam.NameTextSizeShort, _textParam.NameTextSizeLong);
            ApplyTextShadowInfo(_bookCoverNameTextShadow, _bookCoverNameText, shadowInfo);

            // フォトブック
            rectInfo = _textParam.TitleRectInfo;
            textInfo = _textParam.TitleTextCommonInfo;
            ApplyRectInfo(_bookCoverTitleTextRectTransform, rectInfo);
            ApplyTextInfo(_bookCoverTitleText, textInfo);

            // フォトブックシャドウ
            shadowInfo = _textParam.TitleShadowOffset;
            ApplyRectInfo(_bookCoverTitleTextShadowRectTransform, rectInfo);
            ApplyTextInfo(_bookCoverTitleTextShadow, textInfo);
            ApplyTextShadowInfo(_bookCoverTitleTextShadow, _bookCoverTitleText, shadowInfo);

            // 背景
            rectInfo = _textParam.BgRectInfo;
            ApplyRectInfo(_bookCoverTextBgRectTransform, rectInfo);

            // 帯テキスト画像
            rectInfo = _textParam.ObiTextRectInfo;
            ApplyRectInfo(_bookObiTextRectTransform, rectInfo);

            // 帯アイコン画像
            rectInfo = _textParam.ObiIconRectInfo;
            ApplyRectInfo(_bookObiIconRectTransform, rectInfo);

            return;

            void ApplyRectInfo(RectTransform targetRect, SingleModeScenarioLegendReputationPosterTextParam.RectTransformInfo rectInfo)
            {
                if (targetRect == null)
                    return;

                targetRect.anchorMin = rectInfo.AnchorMin;
                targetRect.anchorMax = rectInfo.AnchorMax;
                var position = targetRect.anchoredPosition3D;
                position.Set(rectInfo.AnchorPos.x, rectInfo.AnchorPos.y, 0);
                targetRect.anchoredPosition3D = position;
                targetRect.sizeDelta = rectInfo.SizeDelta;
                var scale = targetRect.localScale;
                scale.Set(rectInfo.Scale.x, rectInfo.Scale.y, 1);
                targetRect.localScale = scale;
            }

            void ApplyTextInfo(TextCommon targetText, SingleModeScenarioLegendReputationPosterTextParam.TextCommonInfo textInfo)
            {
                if (targetText == null)
                    return;

                targetText.FontType = textInfo.Font;
                targetText.FontSizeFormat = textInfo.TextSize;
                targetText.FontSize = textInfo.TextSize.GetSize();
                targetText.alignment = textInfo.Alignment;
                targetText.FontColor = textInfo.FontColorType;
            }

            void ApplyTextShadowInfo(TextCommon targetText, TextCommon originalText, Vector3 offset)
            {
                if (targetText == null)
                    return;

                // 位置調整
                var position = originalText.transform.localPosition;
                position += offset;
                targetText.transform.localPosition = position;

                // 描画順調整
                // Canvas要素なのでヒエラルキー上の並びを入れ替えて制御
                targetText.transform.SetSiblingIndex(originalText.transform.GetSiblingIndex());
            }

            void ApplyNameTextSize(TextCommon targetText, int threshold, TextFormat.FontSize sizeShort, TextFormat.FontSize sizeLong)
            {
                if (targetText == null)
                    return;

                var sizeFormat = targetText.text.Length > threshold ? sizeLong : sizeShort;
                targetText.FontSizeFormat = sizeFormat;
                targetText.FontSize = sizeFormat.GetSize();
            }
        }

        private void UpdateVerticalTextRectTransform()
        {
            // NOTE:縦書きのために90度回転させた状態で親のRectTransformと同じサイズになるようにする
            // 端末の画面サイズによってCanvasから設定されるサイズが変わるので、Canvasを強制更新して反映させておく
            UpdateCanvasForBookCoverUI();
            UpdateCanvasForBookObiUI();

            var parentRect = _bookCoverRectTransform.rect;
            // 回転してるので縦横入れ替えて設定
            var adjustedSize = new Vector2(parentRect.height, parentRect.width);
            _bookCoverNameTextRectTransform.sizeDelta = adjustedSize;
            _bookCoverNameTextShadowRectTransform.sizeDelta = adjustedSize;
            _bookCoverTitleTextRectTransform.sizeDelta = adjustedSize;
            _bookCoverTitleTextShadowRectTransform.sizeDelta = adjustedSize;
        }

#if CYG_DEBUG
        /// <summary>
        /// 外部からページごとにキャラのタイプを指定可能にする
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="charaType"></param>
        /// <returns></returns>
        public string DebugGetPageCutInPath(int pageIndex, int charaType, int charaId)
        {
            if (pageIndex < 0 || pageIndex > PHOTO_PAGE_MAX)
            {
                Debug.LogError($"ページIndexが範囲外です pageIndex:{pageIndex}");
                return null;
            }

            // 01ページ目だけ構成が異なる
            if (pageIndex == PHOTO_RUNNING_PAGE_INDEX)
            {
                var (personalityType, runningType) = SingleModeScenarioLegendCuttUtils.GetCharaRacePersonalityAndRunningType(charaId);
                return ResourcePath.GetSingleModeScenarioLegendReputationPhotoCutInPage01Path(personalityType, runningType);
            }
            else
            {
                // PhotoBookCoverから連番
                var targetCutt = SingleModeScenarioLegendDefine.ScenarioLegendCuttType.PhotoBookCover + pageIndex;
                //上書き指定用の性格タイプがあればそれを利用、なければ既存のままにする
                var cuttCharaType = charaType > 0 ? charaType : SingleModeScenarioLegendCuttUtils.GetCuttCharaType(charaId, targetCutt);
                return ResourcePath.GetSingleModeScenarioLegendReputationPhotoCutInPagePath(cuttCharaType, pageIndex);
            }
        }
#endif

#if CYG_DEBUG
        /// <summary>
        /// 途中経過の確認用にテクスチャとしてストレージに保存
        /// </summary>
        private void DebugSaveTexture(RenderTexture srcTexture)
        {
            var debugSavePath = $"{ChampionsUtils.GetGoalCaptureSavePath()}Debug";
            var fileName = "debug_save_texture.png";

            var saveTex = TextureSaveLoaderUtil.ConvertRenderTexToTexture2D(srcTexture);
            var (_, texDataHash) = TextureSaveLoaderUtil.SaveTexture(saveTex, debugSavePath, fileName);
        }
#endif

    }
}
