using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using Gallop.SingleMode.ScenarioLegend;

namespace Gallop
{
    using static SingleModeScenarioLegendDefine;

    public static class SingleModeScenarioLegendUtils
    {
        /// <summary>
        /// 2.13.0向けアップデート対応のうち、リソースとセットでのリリースで必要なものが有効かどうか
        /// 10015900がリリースされたら有効になる
        /// TODO:[#157641] @takahashi_nobushige 後のバージョンで削除する
        /// </summary>
        public static bool IsEnable21300UpdateWithResource()
        {
            GameDefine.UpdateEnableSingleModeLegendUpdate();
            return GameDefine.IS_ENABLE_SINGLE_MODE_LEGEND_UPDATE;
        }
        
        public static bool IsLegendCharaId(int legendId)
            => legendId is LEGEND_ID_9046 or LEGEND_ID_9047 or LEGEND_ID_9048;

        public static LegendType LegendIdToLegendType(int legendId)
            => legendId switch
            {
                LEGEND_ID_9046 => LegendType.Legend9046,
                LEGEND_ID_9047 => LegendType.Legend9047,
                LEGEND_ID_9048 => LegendType.Legend9048,
                _ => LegendType.Legend9046,
            };

        /// <summary>
        /// レジェンドのキャラtypeを0始まりインデックスに変換
        /// </summary>
        public static int ToIndex(this LegendType legendType)
            => legendType switch
            {
                LegendType.Legend9046 => 0,
                LegendType.Legend9047 => 1,
                LegendType.Legend9048 => 2,
                _ => 0
            };

        /// <summary>
        /// レジェンドのキャラtypeを0始まりcharaIdに変換
        /// </summary>
        public static int ToLegendId(this LegendType legendType)
            => legendType switch
            {
                LegendType.Legend9046 => LEGEND_ID_9046,
                LegendType.Legend9047 => LEGEND_ID_9047,
                LegendType.Legend9048 => LEGEND_ID_9048,
                _ => 0
            };

        /// <summary>
        /// レジェンドのキャラtypeを勝負服のドレスIdに変換
        /// </summary>
        public static int ToLegendDressId(this LegendType legendType)
            => legendType switch
            {
                LegendType.Legend9046 => LEGEND_DRESS_ID_9046,
                LegendType.Legend9047 => LEGEND_DRESS_ID_9047,
                LegendType.Legend9048 => LEGEND_DRESS_ID_9048,
                _ => 0
            };

        /// <summary>
        /// レジェンドのキャラtypeをキャラ名に変換
        /// </summary>
        public static string ToCharaName(this LegendType legendType)
            => TextUtil.GetMasterText(MasterString.Category.MasterCharaName, legendType.ToLegendId());

        public static OutlineColorType ToImageOutlineColor(this LegendType legendType)
            => legendType switch
            {
                LegendType.Legend9046 => OutlineColorType.SingleModeScenarioLegend9046,
                LegendType.Legend9047 => OutlineColorType.SingleModeScenarioLegend9047Masterly,
                LegendType.Legend9048 => OutlineColorType.SingleModeScenarioLegend9048,
                _ => OutlineColorType.Black,
            };

        /// <summary>
        /// 所持バフは入手順に並び変える
        /// </summary>
        public static IOrderedEnumerable<T> OrderByObtainedTurn<T>(this IEnumerable<T> buffItems)
            where T : IBuffItemModel
            => buffItems
                .OrderBy(x => x.ObtainedTurn)
                .ThenByDescending(x => x.BuffRank)
                .ThenBy(x => x.BuffId);

        /// <summary>
        /// 表示用の共通順番に並び替える
        /// LegendType昇順>Rank降順>BuffId昇順
        /// </summary>
        public static IOrderedEnumerable<T> OrderByDisplayPriority<T>(this IEnumerable<T> buffItems)
            where T : SelectableBuffItemModel
            => buffItems
                .OrderBy(x => x.BuffItemModel.LegendType)
                .ThenByDescending(x => x.BuffItemModel.BuffRank)
                .ThenBy(x => x.BuffItemModel.BuffId);

        /// <summary>
        /// 心得習得可能表示用に並び替える
        /// Rank昇順>BuffId昇順
        /// </summary>
        public static IOrderedEnumerable<T> OrderByEnableBuffSelect<T>(this IEnumerable<T> buffItems)
            where T : IBuffItemModel
            => buffItems
                .OrderBy(x => x.BuffRank)
                .ThenBy(x => x.BuffId);

        /// <summary>
        /// シナリオレース中か(PlayingState)
        /// </summary>
        public static bool IsPlayingLegendRace()
        {
#if CYG_DEBUG
            if (RaceManager.RaceInfo != null && RaceManager.RaceInfo.IsRaceDirectRace)
            {
                //ダイレクトシーンなどのデバッグ機能から指定された時
                return RaceDebugger.IsDebugPlayingSingleModeLegendRace;
            }
#endif
            // 育成プレー中であることが前提
            if (!WorkDataManager.Instance.SingleMode.IsPlaying)
            {
                return false;
            }
            switch (WorkDataManager.Instance.SingleMode.PlayingState)
            {
                case SingleModeDefine.PlayingState.LegendRacePaddock:
                case SingleModeDefine.PlayingState.LegendRaceInGame:
                case SingleModeDefine.PlayingState.LegendRaceResult:
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 伝説編シナリオレースにおいて対象レースがコンティニュー可能か判定する
        /// PlayingStateは呼び出しもとで見ること
        /// </summary>
        public static bool CanContinueTargetRace(RaceInfo raceInfo)
        {
            //1着だった場合はコンティニュー不可
            if (raceInfo.GetPlayerHorseFinishOrder(isValidOverride: false) == 0) // 進行にかかわる部分は育成キャラのものなのでfalse
            {
                return false;
            }

            //コンティニュー回数がもうない
            if (WorkDataManager.Instance.SingleMode.HomeInfo.AvailableContinueNum == 0)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 最終ターンのシナリオレースかどうか
        /// </summary>
        /// <returns></returns>
        public static bool IsPlayingFinalLegendRace()
        {
            // まずシナリオレース中かどうか
            if (!IsPlayingLegendRace())
            {
                return false;
            }

            var currentTurn = WorkDataManager.Instance.SingleMode.GetCurrentTurn();
            var lastScenarioRace = MasterDataManager.Instance.masterSingleMode10Sprace.OrderedSchedule.LastOrDefault();
            return lastScenarioRace?.TurnNum == currentTurn;
        }
        
        /// <summary>
        /// 指定のProgramIdがシナリオレースかどうか
        /// </summary>
        public static bool IsScenarioLegendRaceByProgramId(int programId)
        {
            var groupId = MasterDataManager.Instance.masterSingleModeRaceGroup.GetRaceGroupIdByProgramId(programId);
            return MasterDataManager.Instance.masterSingleMode10Sprace.OrderedSchedule.Any(x => x.RaceGroupId == groupId);
        }

        /// <summary>
        /// バフ名をマスタから取得
        /// </summary>
        public static string GetBuffName(int buffId)
            => TextUtil.GetMasterText(MasterString.Category.SingleModeScenarioLegendBuffItemName, buffId);

        public static bool IsLegendGroupSupportCard(MasterSupportCardData.SupportCardData masterSupportCardData)
        {
            // そもそもグルサポでなければ判定しない
            if (masterSupportCardData == null ||
                masterSupportCardData.IsGroupSupportCard == false)
                return false;

            var supportCardCharaIdList = masterSupportCardData.GetCharaIdList();
            var legendIdList = new[] { LEGEND_ID_9046, LEGEND_ID_9047, LEGEND_ID_9048 };
            return legendIdList.All(supportCardCharaIdList.Contains);
        }

        /// <summary>
        /// シナリオレースのレース名（距離別を含まないもの）を取得
        /// </summary>
        /// <param name="turn"></param>
        /// <returns></returns>
        public static string GetScenarioRaceNameByTurn(int turn)
        {
            var turnSetId = WorkDataManager.Instance.SingleMode.GetTurnSetId();
            var degreeType = SingleModeUtils.GetDegreeType(turnSetId, turn);
            return GetScenarioRaceNameByDegree(degreeType);
        }

        public static string GetScenarioRaceNameByDegree(SingleModeDefine.DegreeType degreeType) => degreeType switch
        {
            SingleModeDefine.DegreeType.Junior => TextId.SingleModeScenarioLegend539030.Text(),
            SingleModeDefine.DegreeType.Classic => TextId.SingleModeScenarioLegend539031.Text(),
            SingleModeDefine.DegreeType.Senior => TextId.SingleModeScenarioLegend539032.Text(),
            _ => string.Empty,
        };

        /// <summary>
        /// 伝説編：バフ発動の効果によるやる気の変化情報を取得する
        /// </summary>
        public static MotivationChangedInfoByBuff GetMotivationChangedInfoOnBuffActivation(WorkSingleModeChangeParameterInfo changeParameterInfo)
        {
            var changeParameter = changeParameterInfo.ScenarioLegend;
            
            // そもそもバフが何も発動していない
            if (changeParameter.ActivatedBuffIds.Count == 0)
            {
                return MotivationChangedInfoByBuff.CreateNone();
            }
            
            // 片頭痛で変動なし情報が来ている
            if (changeParameter.NotUpCharaEffectIdListOnBuffActivationEffect.Contains(SingleModeDefine.CharaEffectId.Migraine))
            {
                return MotivationChangedInfoByBuff.CreateNotUp(MotivationChangedInfoByBuff.NotChangedReason.NotUpByMigraine);
            }
            
            // 変動なし情報が来ている
            if (changeParameter.NotUpStatusTypeListOnBuffActivationEffect.Contains(SingleModeDefine.ParameterGainLimitType.Motivation))
            {
                MasterlyBonusModelRepository.TryGet(out var masterlyBonus);
                var reason = masterlyBonus.IsSuperMaxMotivation()
                    ? MotivationChangedInfoByBuff.NotChangedReason.NotUpBySuperMaxMotivation : MotivationChangedInfoByBuff.NotChangedReason.NotUpByMotivationLimit;
                return MotivationChangedInfoByBuff.CreateNotUp(reason);
            }

            // バフ発動によるやる気上昇量を算出。やる気が即時報酬として設定されているバフをカウント (1バフ1段階まで)
            var gainMotivationBuffCount = changeParameter.ActivatedBuffIds
                .Count(buffId =>
                {
                    var buffMaster = MasterDataManager.Instance.masterSingleMode10Buff.GetWithBuffId(buffId);
                    var buffEffectMasterList = MasterDataManager.Instance.masterSingleMode10BuffEffect.GetListWithEffectGroupIdOrderByIdAsc(buffMaster?.EffectGroupId ?? 0);
                    return buffEffectMasterList?.Any(x => x.IsGainMotivationOnActivate) ?? false;
                });
            
            return gainMotivationBuffCount > 0 ? MotivationChangedInfoByBuff.CreateUp(gainMotivationBuffCount) : MotivationChangedInfoByBuff.CreateNone();
        }

        /// <summary>
        /// SingleModeScenarioLegendアトラスからスプライトを取得
        /// </summary>
        public static Sprite GetAtlasSprite(string spriteName)
        {
            var atlas = UIManager.Instance.LoadAtlas(TargetAtlasType.SingleModeScenarioLegend);
            return atlas.GetSprite(spriteName);
        }
        
        /// <summary>
        /// 伝説編編共通ダウンロード登録
        /// </summary>
        public static void RegisterDownloadCommon(DownloadPathRegister register)
        {
            // サウンド
            var soundList = new List<string>
            {
                ResourcePath.SINGLE_MODE_SCENARIO_LEGEND_SE_CUE_SHEET_NAME,
            };

#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                // バッチモード対応
                AudioManager.RegisterDownloadByCueSheetsForEditor(register, soundList, AudioManager.SubFolder.Se);
            }
            else
#endif
            {
                AudioManager.Instance.RegisterDownloadByCueSheets(register, soundList, AudioManager.SubFolder.Se);
            }
        }
        
        /// <summary>
        /// ハイセイコーマスタリーボーナスの野良併せウマのダウンロード登録
        /// </summary>
        /// <remarks>
        /// 伝説編では野良ウマ娘が友情トレーニングを発生させうる。この野良はイベント内で増える
        /// SingleModeMainView上でイベントを再生した場合、イベント明けに戻ってきたタイミングでは SingleModeMainView.RegisterDownloadは呼ばれないため
        /// DL呼び出し箇所が SingleModeMainView.RegisterDownload のみだとボイスのDL漏れが起こりうる
        /// 
        /// ハイセイコーの協力者加入演出の後にはかならず協力者のゲージがMAXになるイベントが発生するため、
        /// SingleModeMainViewとStoryViewのそれぞれにDLを入れることでカバーする
        /// </remarks>
        public static void RegisterDownloadLegend9048Guest(DownloadPathRegister register)
        {
            if (MasterlyBonusModelRepository.TryGet<MasterlyBonus9048Model>(out var current9048Model) == false)
                return;
            
            var legend9048GuestIdList = new List<int>();
            foreach (var friendData in current9048Model.FriendDataList)
            {
                // ゲスト以外は通常フローでダウンロードするため対象外とする
                if (friendData.IsGuest == false)
                    continue;
                
                // Rサポートカード(グルサポは野良ウマ対象外なので考慮しない
                register.RegisterPathWithoutInfo(ResourcePath.GetSupportCardTexTexturePath(friendData.SupportCardId));

                legend9048GuestIdList.Add(friendData.CharaId);
            }
            
            // 育成タッグの音声のみ対象
            AudioManager.Instance.RegisterDownloadByTriggerAndCharaIds(register, legend9048GuestIdList, 
                new List<CharacterSystemLotteryTrigger>() { CharacterSystemLotteryTrigger.SingleModeTrainingFriendly }
            );
        }
    }
}