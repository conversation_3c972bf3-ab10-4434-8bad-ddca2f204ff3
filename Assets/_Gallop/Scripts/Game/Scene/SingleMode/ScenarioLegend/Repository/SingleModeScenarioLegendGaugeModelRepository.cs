namespace Gallop.SingleMode.ScenarioLegend
{
    /// <summary>
    /// 伝説編：バフゲージのエンティティ取得に用いるリポジトリ
    /// </summary>
    public static class GaugeModelRepository
    {
        public static IBuffGaugeModel Get()
        {
            return CreateBuffGaugeModel(WorkDataManager.Instance.SingleMode.Character.ScenarioLegend);
        }
        
        public static IBuffGaugeModel GetBackupData()
        {
            var backup = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.BackupData;
            return backup != null ? CreateBuffGaugeModel(backup) : null;
        }
        
        private static IBuffGaugeModel CreateBuffGaugeModel(IWorkSingleModeScenarioLegendData workSingleModeScenarioLegend)
        {
            return new BuffGaugeModel(workSingleModeScenarioLegend.LegendGaugeDataList);
        }
    }
}
