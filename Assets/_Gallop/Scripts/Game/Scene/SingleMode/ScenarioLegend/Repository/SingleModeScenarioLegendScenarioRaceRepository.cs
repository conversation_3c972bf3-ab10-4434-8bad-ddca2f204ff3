namespace Gallop.SingleMode.ScenarioLegend
{
    /// <summary>
    /// 伝説編：シナリオレース情報のエンティティ取得に用いるリポジトリ
    /// </summary>
    public static class ScenarioRaceRepository
    {
        public static IScenarioRaceModel Get()
        {
#if CYG_DEBUG
            // デバッグ用のエンティティが設定されている場合はそれを返す
            if (DebugOverrideEntity != null) return DebugOverrideEntity;
#endif
            
            return CreateScenarioRaceModel(WorkDataManager.Instance.SingleMode.Character.ScenarioLegend);
        }

        public static IScenarioRaceModel GetBackupData()
        {
            var backup = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.BackupData;
            return backup != null ? CreateScenarioRaceModel(backup) : null;
        }

        private static IScenarioRaceModel CreateScenarioRaceModel(IWorkSingleModeScenarioLegendData workSingleModeScenarioLegend)
        {
            return new ScenarioRaceModel(
                workSingleModeScenarioLegend.LegendRaceHistoryList,
                WorkDataManager.Instance.SingleMode.GetCurrentTurn,
                WorkDataManager.Instance.SingleMode.GetDegreeType);
        }
        
#if CYG_DEBUG
        public static DebugScenarioRaceModel DebugOverrideEntity { get; set; }
#endif
    }
}
