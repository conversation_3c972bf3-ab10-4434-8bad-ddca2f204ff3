namespace Gallop.SingleMode.ScenarioLegend
{
    /// <summary>
    /// 伝説編：バフリストのエンティティ取得に用いるリポジトリ
    /// </summary>
    public static class BuffListModelRepository
    {
        public static IBuffListModel Get()
        {
            return CreateBuffListModel(WorkDataManager.Instance.SingleMode.Character.ScenarioLegend);
        }
        
        public static IBuffListModel GetBackupData()
        {
            var backup = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.BackupData;
            return backup != null ? CreateBuffListModel(backup) : null;
        }
        
        private static IBuffListModel CreateBuffListModel(IWorkSingleModeScenarioLegendData workSingleModeScenarioLegend)
        {
            return new BuffListModel(workSingleModeScenarioLegend.BuffDataInfoList);
        }
    }
}
