using System.Linq;

namespace Gallop.SingleMode.ScenarioLegend
{
    /// <summary>
    /// 伝説編：トレーニング情報のエンティティ取得に用いるリポジトリ
    /// </summary>
    public static class TrainingModelRepository
    {
        public static ITrainingCommandModel Get(TrainingDefine.TrainingCommandId commandId)
        {
            var gaugeModel = GaugeModelRepository.Get();
            return CreateTrainingCommandModel(WorkDataManager.Instance.SingleMode.Character.ScenarioLegend, commandId, gaugeModel);
        }
        
        public static ITrainingCommandModel Get(SingleModeDefine.CommandType commandType)
        {
            DebugUtils.Assert(commandType != SingleModeDefine.CommandType.Training, "CommandType.Training は Get(TrainingDefine.TrainingCommandId commandId)を利用してください");
            
            var gaugeModel = GaugeModelRepository.Get();
            return CreateTrainingCommandModelByCommandType(WorkDataManager.Instance.SingleMode.Character.ScenarioLegend, commandType, gaugeModel);
        }

        public static ITrainingCommandModel GetBackupData(TrainingDefine.TrainingCommandId commandId)
        {
            var backup = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.BackupData;
            var gaugeModelBackup = GaugeModelRepository.GetBackupData();
            return backup != null ? CreateTrainingCommandModel(backup, commandId, gaugeModelBackup) : null;
        }
        
        private static ITrainingCommandModel CreateTrainingCommandModel(IWorkSingleModeScenarioLegendData workSingleModeScenarioLegend, TrainingDefine.TrainingCommandId commandId, IBuffGaugeModel gaugeModel)
        {
            var trainingData = workSingleModeScenarioLegend.LegendTrainingDataList.FirstOrDefault(data => data.CommandId == (int)commandId);
            if (trainingData == null) return null;
            
            return new TrainingCommandModel(trainingData, gaugeModel);
        }
        
        private static ITrainingCommandModel CreateTrainingCommandModelByCommandType(IWorkSingleModeScenarioLegendData workSingleModeScenarioLegend, SingleModeDefine.CommandType commandType, IBuffGaugeModel gaugeModel)
        {
            var trainingData = workSingleModeScenarioLegend.LegendTrainingDataList.FirstOrDefault(data => data.CommandType == (int)commandType);
            if (trainingData == null) return null;
            
            return new TrainingCommandModel(trainingData, gaugeModel);
        }
    }
}
