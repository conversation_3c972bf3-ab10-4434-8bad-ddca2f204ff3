namespace Gallop.SingleMode.ScenarioLegend
{
    /// <summary>
    /// 伝説編：マスタリーボーナス
    /// </summary>
    public static class MasterlyBonusModelRepository
    {
        public static bool TryGet(out IMasterlyBonusModel masterlyBonus)
        {
#if CYG_DEBUG
            // デバッグ用のモデルが設定されている場合はそれを返す
            if (DebugModel != null)
            {
                masterlyBonus = DebugModel;
                return true;
            }
#endif
            
            masterlyBonus = CreateMasterlyBonusModel(WorkDataManager.Instance.SingleMode.Character.ScenarioLegend);
            return masterlyBonus != null;
        }
        public static bool TryGetBackup(out IMasterlyBonusModel masterlyBonus)
        {
#if CYG_DEBUG
            // デバッグ用のモデルが設定されている場合はそれを返す
            if (DebugBackupModel != null)
            {
                masterlyBonus = DebugBackupModel;
                return true;
            }
#endif
            var backup = WorkDataManager.Instance.SingleMode.ChangeParameterInfo.ScenarioLegend.BackupData;
            if (backup == null)
            {
                masterlyBonus = null;
                return false;
            }
            
            masterlyBonus = CreateMasterlyBonusModel(backup);
            return masterlyBonus != null;
        }

        public static bool TryGet<T>(out T masterlyBonus) where T : class, IMasterlyBonusModel
        {
            if (TryGet(out var model) && model is T)
            {
                masterlyBonus = (T)model;
                return true;
            }

            masterlyBonus = null;
            return false;
        }
        public static bool TryGetBackup<T>(out T masterlyBonus) where T : class, IMasterlyBonusModel
        {
            if (TryGetBackup(out var model) && model is T)
            {
                masterlyBonus = (T)model;
                return true;
            }

            masterlyBonus = null;
            return false;
        }
        
        private static IMasterlyBonusModel CreateMasterlyBonusModel(IWorkSingleModeScenarioLegendData workSingleModeScenarioLegend)
        {
            return CreateMasterlyBonusModel(workSingleModeScenarioLegend.MasterlyInfoData);
        }
        
        /// <summary>
        /// Legendに対応したマスタリーボーナスの情報モデルを作成する
        /// </summary>
        private static IMasterlyBonusModel CreateMasterlyBonusModel(WorkSingleModeScenarioLegend.LegendMasterlyInfoData masterlyInfoData)
        {
            if (masterlyInfoData == null) return null;
            
            if (masterlyInfoData.Legend9046BonusInfo != null)
                return new MasterlyBonus9046Model(masterlyInfoData.Legend9046BonusInfo);
            
            if (masterlyInfoData.Legend9047BonusInfo != null)
                return new MasterlyBonus9047Model(masterlyInfoData.Legend9047BonusInfo);
            
            if(masterlyInfoData.Legend9048BonusInfo != null)
                return new MasterlyBonus9048Model(
                    masterlyInfoData.Legend9048BonusInfo,
                    SingleModeUtils.GetCharaIdByAllPosition,
                    SingleModeUtils.GetTeamMemberSupportCardId,
                    (positionId) => SingleModeUtils.GetCharaIdByPosition(positionId) == 0);
            
            return null;
        }
        
#if CYG_DEBUG
        private static IMasterlyBonusModel DebugModel { get; set; }
        private static IMasterlyBonusModel DebugBackupModel { get; set; }
        public static void DebugRegisterModel(IMasterlyBonusModel model) => DebugModel = model;
        public static void DebugRemoveModel() => DebugModel = null;
        public static void DebugRegisterBackupModel(IMasterlyBonusModel model) => DebugBackupModel = model;
        public static void DebugRemoveBackupModel() => DebugBackupModel = null;
#endif
    }
}

