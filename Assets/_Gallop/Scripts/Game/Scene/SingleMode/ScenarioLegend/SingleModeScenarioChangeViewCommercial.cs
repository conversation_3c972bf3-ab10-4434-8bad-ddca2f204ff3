namespace Gallop
{
    /// <summary>
    /// 伝説編：CM演出の遷移関連処理
    /// </summary>
    public static class SingleModeScenarioChangeViewCommercial
    {
        /// <summary>
        /// CM演出遷移を包括した内容を実行する
        ///
        /// 育成再開時は再生確認ダイアログなしでCMへ
        /// 
        /// 初回は再生確認ダイアログなしでCMへ
        /// 
        /// 2回目からは再生確認ダイアログ
        /// </summary>
        public static void Execute()
        {
            if (!IsStoryAfter() || IsFirstCommercial())
            {
                // ストーリー経由でない or 初回なら CMへ
                ChangeViewCommercial();
                return;
            }

            // CM再生確認ダイアログ
            DialogSingleModeScenarioLegendCommercialConfirm.PushDialog(
                () => DialogManager.RemoveAllDialog(ChangeViewCommercial),
                () => DialogManager.RemoveAllDialog(OnSkipCommercial));
        }

        /// <summary>
        /// 育成イベントから来ているか
        /// 育成からの続きならtrue。再生確認してからCMへ行きたい
        /// ホームやイベント系画面からの中断再開では確認なしでCMへ行きたい
        /// </summary>
        private static bool IsStoryAfter()
        {
            var currentViewId = SceneManager.Instance.GetCurrentViewId();
            if (currentViewId == SceneDefine.ViewId.SingleModeMain ||
                currentViewId == SceneDefine.ViewId.Story)
            {
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 初回判定
        /// </summary>
        private static bool IsFirstCommercial()
        {
            const int STORY_ID1 = 400010024;    // エピローグ(TRUEエンド）
            const int STORY_ID2 = 400010025;    // エピローグ(goodエンド）
            var isRead1 = WorkDataManager.Instance.GalleryData.IsReadEventData(STORY_ID1);
            var isRead2 = WorkDataManager.Instance.GalleryData.IsReadEventData(STORY_ID2);
            return !isRead1 && !isRead2;    // どちらも見てないなら初回
        }

        /// <summary>
        /// CM再生確認でスキップを選択
        /// CM再生せず育成へ
        /// </summary>
        private static void OnSkipCommercial()
        {
            // CM終了API
            SingleModeLegendAPI.SendLegendCmEnd(() =>
            {
                // 育成へ
                SingleModeChangeViewManager.Instance.ChangeMainView();
            });
        }
        
        /// <summary>
        /// CM演出へ
        /// </summary>
        private static void ChangeViewCommercial()
        {
            var workCmInfo = WorkDataManager.Instance.SingleMode.Character.ScenarioLegend.CmInfoData;
            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(workCmInfo.RaceResultInfo, RaceDefine.RaceType.Single);
            RaceInitializer.CreateRaceInfo(new RaceInitializer.LoadRaceInfo(ref buildParam));
            var masterCmDetail = MasterDataManager.Instance.masterSingleMode10CmDetail.Get(workCmInfo.CmDetailId);
            RaceManager.RaceInfo.CommercialType = masterCmDetail?.RouteId ?? 1;
            RaceManager.RaceInfo.CommercialVoiceId = masterCmDetail?.VoiceId ?? 1;
            var isWipe = IsStoryAfter();// 育成イベント経由ならワイプ
            var raceMainViewInfo = new RaceMainViewController.ViewInfo(
                bootMode: RaceMainView.Mode.Commercial,
                onOverrideDynamicNowLoadingType: () =>
                {
                    if (isWipe)
                    {
                        // レース画面のNowLoadingをワイプに変更
                        return NowLoading.Type.CustomWipeFlash;
                    }
                    // 中断再開などは蹄鉄ロード
                    return NowLoading.Type.NowLoadingPlain;                    
                });

            if (isWipe)
            {
                UIManager.Instance.LockGameCanvas();
                PartsSingleModeScenarioLegendCommercialNowLoadingWipe.ShowCommon(() =>
                {
                    UIManager.Instance.UnlockGameCanvas();
                    RaceInitializer.GoToRace(raceMainViewInfo);
                });
            }
            else
            {
                RaceInitializer.GoToRace(raceMainViewInfo);
            }
        }
    }
}