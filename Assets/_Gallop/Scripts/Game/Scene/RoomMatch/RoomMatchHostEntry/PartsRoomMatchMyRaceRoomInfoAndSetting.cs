using UnityEngine;
using System;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsRoomMatchMyRaceRoomInfoAndSetting : PartsRoomMatchHostEntryRoomInfoAndSetting
    {
        #region Const

        #endregion
        
        #region SerializeField, Member
        
        /// <summary> 保存ID </summary>
        public int ConditionId {get; set;}

        #endregion

        #region Method

        /// <summary>
        /// 初期化
        /// </summary>
        public override void Initialize(Action<ExhibitionRaceRaceSettingInfo> onChange)
        {
            base.Initialize(onChange);
            
            _changeRoomSettingButton.SetOnClick(() => DialogRoomMatchHostEntryRoomSetting.Open(_info, info => UpdateRoomInfo(info)));
            _changeRaceSettingButton.SetOnClick(() => DialogExhibitionRaceChangeRaceSetting.OpenForRoomMatch(_info, info => UpdateRoomInfo(info)));
            _restrictCharaSettingButton.SetOnClick(() =>
            {
                DialogRoomMatchRestrictChara.Open(_info.RestrictCharaDataList, true, newDataList =>
                {
                    _info.RestrictCharaDataList = newDataList;
                    UpdateRoomInfo(_info);
                }, 
                _info.MinEntryNum);
            });
        }

        /// <summary>
        /// テキストフィールドを使う時の設定
        /// </summary>
        /// <param name="onChangeRoomName"></param>
        /// <param name="onChangeMessage"></param>
        protected override void SetupTextField(Action<string> onChangeRoomName, Action<string> onChangeMessage)
        {
            // なにもしない
        }

        /// <summary>
        /// テキストフィールドに文字列を設定する
        /// </summary>
        /// <param name="roomName"></param>
        /// <param name="message"></param>
        protected override void UpdateTextField(string roomName, string message)
        {
            // テキスト書き換え
            _roomName.text = roomName;
            _message.text = message;
        }
        
        /// <summary>
        /// 出走人数変更ボタンコールバック
        /// </summary>
        protected override void OnClickEntryNumButton()
        {
            if (!_info.CourseSetting.SelectedRaceId.HasValue)
            {
                Debug.LogWarning("レースIDが決定していません。");// ここには通常来ないはず
                return;
            }
            
            DialogRoomMatchHostEntryNumRoomSetting.Open(_info, info => UpdateRoomInfo(info));
        }
        
        /// <summary>
        /// 情報の更新
        /// </summary>
        /// <param name="info"></param>
        private void UpdateRoomInfo(RoomMatchHostEntrySettingInfo info)
        {
            bool isUpdate = true;
            var detail = RoomMatchUtil.GetRaceConditionDetail(info);
            var work = WorkDataManager.Instance.RoomMatchData.MyRaceConditionData.ConditionPresetInfoList[ConditionId];
            if (!work.race_condition_detail.IsNull())
            {
                if (RoomMatchUtil.IsUpdateMyRaceCondition(work.race_condition_detail, detail))
                {
                    isUpdate = false;
                }
                else
                {
                    // 一旦初期化
                    work.race_condition_detail = null;
                }
            }

            if (isUpdate)
            {
                // 通信
                new RoomMatchSaveRaceConditionRequest()
                {
                    my_race_condition_id = work.race_condition_preset_id,
                    race_condition_detail = detail
                }.Send(res =>
                {
                    work.race_condition_detail = detail;
                    
                    // 通信後の処理
                    UpdateInfo(info);
                });
            }
            else
            {
                UpdateInfo(info);
            }
        }

        #endregion
    }
}
