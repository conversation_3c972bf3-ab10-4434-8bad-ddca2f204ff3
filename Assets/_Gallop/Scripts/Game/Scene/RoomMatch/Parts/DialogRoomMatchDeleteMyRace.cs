using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// マイレース条件削除ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogRoomMatchDeleteMyRace : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        #endregion
        
        #region Const

        #endregion

        #region SerializeField, 変数

        [SerializeField] 
        private PartsRoomMatchListMyRace _myRaceParts;
        
        [SerializeField] 
        private TextCommon _warningText;

        #endregion

        #region メソッド

        /// <summary>
        /// 開く
        /// </summary>
        public static void PushDialog(int conditionId, ExhibitionRaceRaceSettingInfo courseSetting, System.Action deleteAction)
        {
            var dialogDeleteMyRace = LoadAndInstantiatePrefab<DialogRoomMatchDeleteMyRace>(ResourcePath.DIALOG_ROOM_MATCH_DELETE_MY_RACE);

            DialogCommon.Data dialogData = dialogDeleteMyRace.CreateDialogData();
            dialogData.AutoClose = false;
            dialogData.Title = TextId.Common0009.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = d => d.Close();
            dialogData.RightButtonText = TextId.Common0003.Text();
            dialogData.RightButtonCallBack = (dialog) =>
            {
                // 削除
                dialogDeleteMyRace.DeleteMyRace(dialog, conditionId, deleteAction);
            };
            
            DialogManager.PushDialog(dialogData);
            dialogDeleteMyRace.Setup(conditionId, courseSetting);
        }
        
        /// <summary>
        /// 初期化
        /// </summary>
        public void Setup(int conditionId, ExhibitionRaceRaceSettingInfo courseSetting)
        {
            // マイレース条件情報
            _myRaceParts.SetupReadOnly(conditionId);
            // 注意文
            _warningText.SetTextWithCustomTag(TextId.RoomMatch600027.Text());
        }

        /// <summary>
        /// マイレース条件削除
        /// </summary>
        public void DeleteMyRace(DialogCommon dialog, int conditionId, System.Action deleteAction)
        {
            var work = WorkDataManager.Instance.RoomMatchData.MyRaceConditionData.ConditionPresetInfoList[conditionId];
            new RoomMatchDeleteRaceConditionRequest()
            {
                my_race_condition_id = work.race_condition_preset_id,
            }.Send(res =>
            {
                dialog.Close();
                deleteAction?.Invoke();
            });
        }

        #endregion
    }
}
