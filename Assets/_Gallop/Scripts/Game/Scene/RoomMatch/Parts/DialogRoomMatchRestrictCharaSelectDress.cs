using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// ルームマッチ：キャラ制限用衣装選択ダイアログ
    /// </summary>
    public class DialogRoomMatchRestrictCharaSelectDress : DialogInnerBase
    {
        #region SerializeField

        [SerializeField]
        private DressButton _item = null;

        #endregion

        #region Member

        private List<int> _selectedDressIdList;
        private List<int> _defaultDressIdList;// 編集済みかの判定のために取っておく
        private List<DressButton> _itemList = new List<DressButton>();
        private ButtonCommon _rightButton;

        /// <summary>
        /// 同一カードで2着以上の衣装がある場合同じ衣装扱いにする
        /// (★1★2キャラのスターティングフューチャーと勝負服など)
        /// </summary>
        private List<List<int>> _sameDressListList = new List<List<int>>();

        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method
        
        /// <summary>
        /// 開く
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="dressIdList"></param>
        /// <param name="onDecide"></param>
        public static void Open(
            int charaId,
            List<int> dressIdList,
            Action<DialogCommon, List<int>> onDecide)
        {
            var component = LoadAndInstantiatePrefab<DialogRoomMatchRestrictCharaSelectDress>(ResourcePath.DIALOG_ROOM_MATCH_RESTRICT_CHARA_SELECT_DRESS);
            var data = component.CreateDialogData();
            data.Title = TextId.LiveTheater0029.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.RightButtonText = TextId.Common0023.Text();
            data.RightButtonCallBack = d => onDecide?.Invoke(d, component._selectedDressIdList);

            var dialog =  DialogManager.PushDialog(data);

            component._rightButton = dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
            component._rightButton.SetInteractable(false);

            component.Setup(charaId, dressIdList);
        }

        /// <summary>
        /// 表示準備
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="defaultDressIdList"></param>
        public void Setup(int charaId, List<int> defaultDressIdList)
        {
            _defaultDressIdList = defaultDressIdList.OrderBy(id => id).ToList();// 比較用にソート
            _selectedDressIdList = new List<int>(defaultDressIdList);

            var gender = GameDefine.Gender.None;
            var masterChara = MasterDataManager.Instance.masterCharaData.Get(charaId);
            if (masterChara != null)
            {
                gender = (GameDefine.Gender)masterChara.Sex;
            }

            // 着れるドレスを取得
            var masterDressList = new List<MasterDressData.DressData>();
            var cardDataList = MasterDataManager.Instance.masterCardData.GetListWithCharaIdOrderByIdAsc(charaId).OrderBy(card => card.Id);
            var serverTime = TimeUtil.GetServerTimeStamp();
            var sameDressCardIdList = new List<Tuple<int, List<int>>>();
            foreach (var cardData in cardDataList)
            {
                var cardRarityList = MasterDataManager.Instance.masterCardRarityData.GetListWithCardIdOrderByRarityAsc(cardData.Id);
                foreach (var cardRarityData in cardRarityList)
                {
                    var dressId = cardRarityData.RaceDressId;
                    var masterDress = MasterDataManager.Instance.masterDressData.Get(dressId);
                    if (masterDressList.All(data => data.Id != dressId) && masterDress != null && masterDress.IsInTerm(serverTime))
                    {
                        // 重複+有効期間チェックを入れて追加
                        masterDressList.Add(masterDress);

                        // 同一レアリティ確認
                        var sameDressCardData = sameDressCardIdList.FirstOrDefault(data => data.Item1 == cardRarityData.CardId); 
                        if (sameDressCardData != null)
                        {
                            // 追加済み
                            sameDressCardData.Item2.Add(dressId);
                        }
                        else
                        {
                            // 新しく追加
                            sameDressCardIdList.Add(new Tuple<int, List<int>>(cardRarityData.CardId, new List<int>() { dressId }));
                        }
                    }
                }

                if (_selectedDressIdList.IsNullOrEmpty())
                {
                    // 選択済みの衣装が無い場合は一番古い勝負服を選択
                    // 現状勝負服の古さを区別する方法がないのでCardIdが最小=一番古いとしている
                    _selectedDressIdList.AddRange(masterDressList.Select(dress => dress.Id).OrderBy(id => id));
                }
            }

            _sameDressListList = sameDressCardIdList
                .Select(tuple => tuple.Item2)
                .Where(list => list.Count > 1)
                .ToList();

            UIUtil.CreateScrollItem(_item, _itemList, masterDressList, (item, masterDress)=>
            {
                item.Init(masterDress, gender, OnDressButtonClicked);
                UpdateItemButton(item);
            });
            UpdateRightButton();
        }

        /// <summary>
        /// 衣装ボタンが押されたときの処理
        /// </summary>
        private void OnDressButtonClicked(DressButton selectDressButton)
        {
            var dressId = selectDressButton.DressId;
            if (_selectedDressIdList.Contains(dressId))
            {
                _selectedDressIdList.Remove(dressId);

                // 共通衣装用の処理
                foreach (var sameDressList in _sameDressListList)
                {
                    if (sameDressList.Contains(dressId))
                    {
                        foreach (var id in sameDressList)
                        {
                            if (id != dressId)
                            {
                                _selectedDressIdList.Remove(id);
                            }
                        }
                    }
                }
            }
            else
            {
                _selectedDressIdList.Add(dressId);
                // 共通衣装用の処理
                foreach (var sameDressList in _sameDressListList)
                {
                    if (sameDressList.Contains(dressId))
                    {
                        foreach (var id in sameDressList)
                        {
                            if (id != dressId)
                            {
                                _selectedDressIdList.Add(id);
                            }
                        }
                    }
                }
                _selectedDressIdList.Sort();// 比較用にソート
            }

            foreach (var dressButton in _itemList)
            {
                UpdateItemButton(dressButton);
            }

            UpdateRightButton();
        }

        private void UpdateRightButton()
        {
            var notification = string.Empty;

            if (_selectedDressIdList.IsNullOrEmpty())
            {
                // 衣装が選択されていない
                notification = TextId.RoomMatch400043.Text();
            }
            else if (_defaultDressIdList.SequenceEqual(_selectedDressIdList))
            {
                // 元の衣装設定から変更されていない
                notification = TextId.RoomMatch400044.Text();
            }

            _rightButton.SetInteractable(string.IsNullOrEmpty(notification));
            _rightButton.SetNotificationMessage(notification);
        }

        /// <summary>
        /// ドレスボタンを更新
        /// </summary>
        /// <param name="item"></param>
        private void UpdateItemButton(DressButton item)
        {
            var isSelected = _selectedDressIdList.Contains(item.DressId);
            // SEを更新(選択不可時に変わるため先に更新)
            item.SetButtonSeType(isSelected ? ButtonCommon.ButtonSeType.CancelS01 : ButtonCommon.ButtonSeType.DecideS01);

            item.SetActiveAppliedDressLabel(isSelected);
        }

        #endregion
    }
}
