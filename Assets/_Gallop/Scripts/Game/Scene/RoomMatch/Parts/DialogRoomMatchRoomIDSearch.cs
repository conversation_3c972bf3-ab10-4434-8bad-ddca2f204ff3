using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ID検索ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogRoomMatchRoomIDSearch : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// ID入力欄
        /// </summary>
        [SerializeField]
        private InputFieldCommon _inputField = null;

        #endregion

        #region member

        private int _inputNumber = 0;

        #endregion

        /// <summary>ダイアログの種類</summary>
        public override DialogCommon.FormType GetFormType() => DialogCommon.FormType.SMALL_TWO_BUTTON;

        /// <summary>どこにダイアログぶら下げるか</summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Root;

        /// <summary>
        /// ダイアログ作成
        /// </summary>
        public static void PushDialog(Action onCloseWatchRequest, Action onChangeLobbyScene)
        {
            GameObject innerObj = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_ROOM_MATCH_ROOM_ID_SEARCH_PATH));
            var inner = innerObj.GetComponent<DialogRoomMatchRoomIDSearch>();
            DialogCommon searchDialogCommon = null ;

            var data = inner.CreateDialogData();
            data.Title = TextId.Friend0039.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = dialog => dialog.Close();
            data.RightButtonText = TextId.Common0022.Text();
            data.RightButtonCallBack = (_) => { inner.SearchRoom(onCloseWatchRequest, onChangeLobbyScene, searchDialogCommon); };
            data.AutoClose = false;

            ButtonCommon rightButton = null;
            var notifyText = TextId.RoomMatch0114.Text();
            var notifyExistText = TextId.RoomMatch0093.Text();

            inner._inputField.characterLimit = GameDefine.ROOM_MATCH_ROOM_ID_LIMIT;
            inner._inputField.onValueChanged.AddListener((inputParam) =>
            {
                // 数値が入力されたら検索ボタン有効
                bool validation = false;
                bool isExist = false;
                if (string.IsNullOrEmpty(inputParam) == false &&
                    inputParam.Length >= GameDefine.ROOM_MATCH_ROOM_ID_LIMIT &&
                    int.TryParse(inputParam, out var inputNumber))
                {
                    // 8桁であっても既に参加中のレースはグレーアウト.
                    var roomList = WorkDataManager.Instance.RoomMatchData.MyEntryRoomList;
                    isExist = roomList.Exists(x => x.RoomId == inputNumber);
                    if (isExist)
                    {
                        validation = false;
                    }
                    else
                    {
                        validation = true;
                        inner._inputNumber = inputNumber;
                    }
                }

                if (rightButton != null)
                {
                    rightButton.interactable = validation;
                    if (isExist)
                    {
                        rightButton.SetNotificationMessage(notifyExistText);
                    }
                    else
                    {
                        rightButton.SetNotificationMessage(validation ? string.Empty : notifyText);
                    }
                }
            });
            searchDialogCommon = DialogManager.PushDialog(data);
            
            rightButton = searchDialogCommon.GetButtonObj(DialogCommon.ButtonIndex.Right);
            rightButton.interactable = false;
            rightButton.SetNotificationMessage(notifyText);
        }

        /// <summary>
        /// 部屋の検索.
        /// </summary>
        private void SearchRoom(Action onCloseWatchRequest, Action onChangeLobbyScene, DialogCommon searchDialog)
        {
            var searchReq = new RoomMatchRoomSearchRequest();
            searchReq.room_id = _inputNumber;

            // 成功時コールバック
            void OnSuccess(RoomMatchRoomSearchResponse searchRes)
            {   
                // 参加済ルーム数更新
                WorkDataManager.Instance.RoomMatchData.UpdateJoinWatchRoomNum(searchRes.data.join_watch_room_num);
                
                // 検索結果ルームに参加してるユーザー情報適用
                WorkDataManager.Instance.RoomMatchData.ApplyRoomSearchResultUsersInfo(searchRes.data.room_info.room_id, searchRes.data.room_user_array);
                var hostUser = new RoomMatchUser();
                if (searchRes.data.room_user_array != null)
                {
                    hostUser = searchRes.data.room_user_array.FirstOrDefault(x => x.viewer_id == searchRes.data.room_info.host_viewer_id);
                }
                WorkRoomMatchData.RoomData roomData = new WorkRoomMatchData.RoomData(searchRes.data.room_info, hostUser);

                // 出走15分前.
                var watchLimitTime = roomData.StartUnixTime - ServerDefine.RoomMatchEntryLimitMinute * TimeUtil.MINUTE_SECOND;

                if (!roomData.CanWatch)
                {
                    // 観戦不可のレース
                    DialogManager.PushDialog(new DialogCommon.Data().SetSimpleOneButtonMessage(
                                TextId.RoomMatch0084.Text(),
                                TextId.RoomMatch0094.Text()
                                ));
                    return;
                }
                else if (roomData.StartUnixTime < TimeUtil.GetServerTimeStamp())
                {
                    // 出走済み
                    var resultCode = GallopResultCode.ROOM_MATCH_WATCH_RACE_START_TIME_ERROR;
                    var header = TextUtil.GetMasterText(MasterString.Category.ErrorHeader, resultCode);
                    var message = TextUtil.GetMasterText(MasterString.Category.ErrorMessage, resultCode);
                    DialogManager.PushErrorCommon(message, header, null, GallopResultCode.OpenErrorPopupType.ShowOnly);
                    return;
                }
                else if (watchLimitTime < TimeUtil.GetServerTimeStamp())
                {
                    // 出走15分前.
                    var resultCode = GallopResultCode.ROOM_MATCH_ENTRY_OVERTIME_ERROR;
                    var header = TextUtil.GetMasterText(MasterString.Category.ErrorHeader, resultCode);
                    var message = TextUtil.GetMasterText(MasterString.Category.ErrorMessage, resultCode);
                    DialogManager.PushErrorCommon(message, header, null, GallopResultCode.OpenErrorPopupType.ShowOnly);
                    return;
                }

                // 既に登録済
                bool isRegist = false;
                var searchResultUsersInfo = WorkDataManager.Instance.RoomMatchData.RoomSearchResultUsersInfo;
                if (searchResultUsersInfo.UserArray != null)
                {
                    isRegist = searchResultUsersInfo.UserArray.Any(x => x.ViewerId == Certification.ViewerId);
                }

                if (isRegist)
                {
                    DialogManager.PushDialog(new DialogCommon.Data().SetSimpleOneButtonMessage(
                        TextId.Common0071.Text(), 
                        TextId.RoomMatch0093.Text()
                    ));
                    return;
                }

                // 参加確認ダイアログ
                DialogExhibitionRaceRaceConfirm.Open(
                TextId.RoomMatch0087.Text(),
                TextId.RoomMatch0090.Text(),
                roomData,
                dialog =>
                {
                    // 観戦はIDのみでOK.
                    var watchReq = new RoomMatchWatchRoomRequest();
                    watchReq.room_id = roomData.RoomId;
                    watchReq.Send(watchRes =>
                    {
                        roomData.UpdateRoomInfo(watchRes.data.room_info);
                        WorkDataManager.Instance.RoomMatchData.AddMyEntryRoomList(roomData);
                        
                        DialogRoomMatchRoomComplete.Open(
                                   TextId.RoomMatch0088.Text(),
                                   TextId.RoomMatch0091.Text(),
                                   false,
                                   roomData,
                                   () => { onCloseWatchRequest?.Invoke(); },
                                   onChangeLobbyScene : onChangeLobbyScene);
                        dialog.Close();
                        searchDialog.Close();
                    });
                });
            }

            searchReq.Send(OnSuccess);
        }
    }

}
