using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.UI;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ルームマッチ：レースリザルト
    /// </summary>
    [AddComponentMenu("")]
    public class RoomMatchRaceResultView : ViewBase
    {
    }

    /// <summary>
    /// ルームマッチ: レースリザルト
    /// ViewInfo
    /// </summary>
    public class RoomMatchRaceResultViewInfo : IViewInfo
    {
        public RaceDefine.RaceType RaceType;

        public HorseData[] HorseDataArray;

        public int RaceHorsesNum;
    }

    /// <summary>
    /// ルームマッチ: レースリザルト
    /// Viewコントローラー
    /// </summary>
    public class RoomMatchRaceResultViewController : ViewControllerBase<RoomMatchRaceResultView>
    {
        private PartsRoomMatchRaceResultBackground _partsRoomMatchRaceResultBackground;
        private RaceResultFriendFollowPanel _friendFollowPanel;
        private bool _anyPlayer;

        // 保存上書き時にレース詳細ダイアログを開くと上書きされてしまうため持っておく
        private WorkRoomMatchData.RoomData _currentRoomData;
        private List<WorkRoomMatchData.RoomMatchTrainedCharaData> _currentRoomEntryCharaList;

        private bool _isSaved = false; // 保存APIは仕様上1回だけ送るため

        #region Method

        #region ViewControllerBase

        public override TextId GetDynamicHeaderTitleTextId()
        {
            var viewInfo = GetViewInfo<RoomMatchRaceResultViewInfo>();
            if (viewInfo != null)
            {
                // 自分を除き他にプレイヤーが居るか（フォローできるプレイヤーが居るか）
                var anyPlayer = viewInfo.HorseDataArray.Any(x => !x.IsUser && (!x.IsMob));
                if (anyPlayer)
                {
                    return TextId.RoomMatch0173;
                }
            }

            return TextId.None;
        }

        public override void RegisterDownload(DownloadPathRegister register)
        {
            var trainedChara = WorkDataManager.Instance.TrainedCharaData.List.FirstOrDefault();
            // キャラ・競馬場
            int dressId = trainedChara.GetRaceDressId(true);
            var racePersonalityType = ModelLoader.GetRaceOverRunMotionPersonality(trainedChara.CharaId);
            PartsRoomMatchRaceResultBackground.RegisterDownload(dressId, racePersonalityType, register);

            base.RegisterDownload(register);
        }

        public override IEnumerator InitializeView()
        {
            var trainedChara = WorkDataManager.Instance.TrainedCharaData.List.FirstOrDefault();

            //キャラビュワー作成
            var charaViewerObj = ResourceManager.LoadOnView<GameObject>(ResourcePath.ROOM_MATCH_RACE_RESULT_BACKGROUND_PATH);
            var charaViewerInstance = GameObject.Instantiate(charaViewerObj);
            _partsRoomMatchRaceResultBackground = charaViewerInstance.GetComponent<PartsRoomMatchRaceResultBackground>(); //シーン直下に生成する
            _partsRoomMatchRaceResultBackground.CreateCourseAndCharacter(trainedChara.CharaId, trainedChara.GetRaceDressId(true));
            _partsRoomMatchRaceResultBackground.SetCharacterVisible(false);
            _partsRoomMatchRaceResultBackground.Update();  //初期位置で更新
            _partsRoomMatchRaceResultBackground.EnableCameraUpdate = false;// キャラINまでは更新OFF


            var viewInfo = GetViewInfo<RoomMatchRaceResultViewInfo>();
            if (viewInfo != null)
            {
                // 自分を除き他にプレイヤーが居るか（フォローできるプレイヤーが居るか）
                _anyPlayer = viewInfo.HorseDataArray.Any(x => !x.IsUser && (!x.IsMob));
                if (_anyPlayer)
                {
                    var completeGetFriendList = false;

                    // フレンド情報API
                    GetSummaryFriendListInfo(() => 
                    {
                        // フォロー選択パネルのセットアップ
                        var clonePanel = GameObject.Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.RACE_RESULT_FRIEND_FOLLOW_PANEL_PATH), _view.ContentsRoot.transform);
                        _friendFollowPanel = clonePanel.GetComponent<RaceResultFriendFollowPanel>();
                
                        var buildInfo = new RaceResultFriendFollowPanel.BuildInfo
                        {
                            IsEnableButton = true,
                            RaceHorsesNum = viewInfo.RaceHorsesNum,
                            RaceType = viewInfo.RaceType,
                        };

                        var dataArray = RaceResultFriendFollowPanel.CreateRaceTableData(viewInfo.HorseDataArray).OrderBy(x => x.FinishOrder).ToArray();
                        _friendFollowPanel.Setup(dataArray, buildInfo, onClickStart: ExitFollowPanel);

                        completeGetFriendList = true;

                    });
                    yield return new WaitUntil(() => completeGetFriendList);
                }
            }

            _currentRoomData = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;
            _currentRoomEntryCharaList = WorkDataManager.Instance.RoomMatchData.CurrentRoomUserList
                .FirstOrDefault(user => user.IsMe())?
                .TraindCharaList;
            if (_currentRoomEntryCharaList != null)
            {
                _currentRoomEntryCharaList.Sort((l, r) => l.MemberId - r.MemberId);

                //  90492 _currentRoomEntryCharaListは二つ名がルーム登録時になっているため二つ名だけ上書き
                // 106645 対策でキャラをそのまま上書きはしない
                foreach (var roomMatchChara in _currentRoomEntryCharaList)
                {
                    var workChara = WorkDataManager.Instance.TrainedCharaData.Get(roomMatchChara.Id);
                    roomMatchChara.SetNickNameId(workChara.NickNameId);
                }
            }

            yield return base.InitializeView();
        }

        /// <summary>
        /// ビューの開始時
        /// </summary>
        public override IEnumerator PlayInView()
        {
            if (_anyPlayer)
            {
                UIManager.Instance.LockGameCanvas();

                // フォロー選択パネルの表示
                _view.StartCoroutine(_friendFollowPanel.PlayEnterAsync(() =>
                {
                    UIManager.Instance.UnlockGameCanvas();
                }));
            }
            else
            {
                // レース保存確認ダイアログ表示
                OpenDialogRoomMatchSaveRaceConfirm();
            }

            yield return base.PlayInView();
        }

        /// <summary>
        /// フレンド情報取得
        /// </summary>
        private void GetSummaryFriendListInfo(Action onComplete)
        {
            var currentRoom = WorkDataManager.Instance.RoomMatchData.CurrentRoomData;

            var req = new RoomMatchGetFriendListRequest();
            req.room_id = currentRoom.RoomId;
            req.Send(res => 
            {
                WorkDataManager.Instance.RoomMatchData.ApplyRoomUserFriendInfoList(res.data);
                onComplete.Invoke();
            });
        }

        /// <summary>
        /// フォロー選択パネル非表示演出（次へボタン押下時）
        /// </summary>
        private void ExitFollowPanel()
        {
            UIManager.Instance.LockGameCanvas();

            // パネル非表示演出再生
            _view.StartCoroutine(_friendFollowPanel.PlayExitAsync(() =>
            {
                _friendFollowPanel.SetActiveWithCheck(false);

                // タイトル非表示
                UIManager.Instance.SetVisibleHeaderTitleWithAnim(false);
                UIManager.Instance.UnlockGameCanvas();

                // レース保存確認ダイアログ表示
                OpenDialogRoomMatchSaveRaceConfirm();
                
            }));
        }

        /// <summary>
        /// 保存レース確認ダイアログ表示
        /// </summary>
        private void OpenDialogRoomMatchSaveRaceConfirm()
        {
            DialogRoomMatchSaveRoomConfirm.Open(CreateSaveDialogSetupParam());
        }

        /// <summary>
        /// OSバックキーが押された時のコールバック
        /// </summary>
        public override void OnClickOsBackKey()
        {
            // 一方通行なので一律無効化
            UIUtil.ShowNotificationBackKey();
            return;
        }

        public override IEnumerator FinalizeView()
        {
            if (_anyPlayer && _friendFollowPanel != null)
            {
                _friendFollowPanel.Release();
            }

            if (_partsRoomMatchRaceResultBackground != null)
            {
                GameObject.Destroy(_partsRoomMatchRaceResultBackground.gameObject);
                _partsRoomMatchRaceResultBackground = null;
            }
            
            // 保存レースダイアログでRaceInfoが作られる場合がある
            RaceManager.DestroyRaceInfo();

            return base.FinalizeView();
        }

        #endregion

        #region 保存確認ダイアログ

        /// <summary>
        /// 保存確認ダイアログ用セットアップパラメータの生成
        /// </summary>
        /// <returns></returns>
        private DialogRoomMatchSaveRoomConfirm.SetupParam CreateSaveDialogSetupParam()
        {
            var saveRoomDic = WorkDataManager.Instance.RoomMatchData.SaveRoomDictionary;

            var param = new DialogRoomMatchSaveRoomConfirm.SetupParam();

            param.RoomData = _currentRoomData;
            if (GetViewInfo() is RoomMatchRaceResultViewInfo viewInfo)
            {
                param.HorseDataArray = viewInfo.HorseDataArray;
            }

            param.Title = _currentRoomData.IsHost() ? TextId.RoomMatch400045.Text() : TextId.RoomMatch0069.Text();
            param.TazunaText = TextId.RoomMatch400029.Text();
            
            param.CurrentSavedRaceNum = saveRoomDic.Count;
            if (_isSaved && ServerDefine.RoomMatchMaxSavedRaceNum > param.CurrentSavedRaceNum)
            {
                param.CurrentSavedRaceNum++;// 保存直後はWorkに入ってないので手動で足す
            }
            param.MaxSavedRaceNum = ServerDefine.RoomMatchMaxSavedRaceNum;

            param.CanGotoAnotherView = _currentRoomData.IsHost();
            param.GotoFirstViewButtonText = TextId.CustomRace0053.Text();
            param.OnClickGotoFirstViewButton = ChangeRoomMatchTop;
            param.GotoSecondViewButtonText = TextId.RoomMatch400030.Text();
            param.OnClickGotoSecondViewButton = _ =>
            {
                // 開催日時を調整
                var startTime = TimeUtil.GetServerTimeStamp() + StaticVariableDefine.Race.ExhibitionRaceDefine.START_TIME_MINUTE_DICT[(int)WorkDataManager.Instance.RoomMatchData.RaceResultInfo.StartTime] * TimeUtil.MINUTE_SECOND;
                startTime += (TimeUtil.MINUTE_SECOND - 1); // 遷移中に数frm経過して時間が29分表記になるのを避ける.
                _currentRoomData.UpdateStartTime(startTime);

                // 出走ウマ娘を開催用に自分だけにする
                TempData.Instance.RoomMatchData.HostEntryTrainedCharaList.Clear();
                TempData.Instance.RoomMatchData.HostEntryTrainedCharaList.AddRange(_currentRoomEntryCharaList);

                DialogRoomMatchRaceDetail.Open(
                    _currentRoomData,
                    CreateRoomAndGotoLobby,
                    DialogRoomMatchRaceDetail.DetailType.Recreate);
            };

            param.OnClickSave = d =>
            {
                d.Dialog.Close();
                SaveRace(() =>
                {
                    DialogManager.RemoveAllDialog(() =>
                    {
                        var setupParam = CreateSaveDialogSetupParam();
                        setupParam.IsSaveButtonInteractive = false;
                        DialogRoomMatchSaveRoomConfirm.Open(setupParam);
                    });
                });
            };
            param.OnClickOverwrite = d => OpenSavedRaceListDialog(d.Dialog,
                onSuccess: () =>
                {
                    DialogManager.RemoveAllDialog(() =>
                    {
                        var setupParam = CreateSaveDialogSetupParam();
                        setupParam.IsSaveButtonInteractive = false;
                        DialogRoomMatchSaveRoomConfirm.Open(setupParam);
                    });
                },
                onOverTimeSaveRoomError: () =>
                {
                    // 一旦ダイアログを閉じて、もう一度ダイアログを開く
                    DialogManager.RemoveAllDialog(() => DialogRoomMatchSaveRoomConfirm.Open(CreateSaveDialogSetupParam()));
                }
                );

            return param;
        }

        /// <summary>
        /// ルームマッチトップへ
        /// </summary>
        private void ChangeRoomMatchTop(DialogCommon dialog)
        {
            // レース終了API
            SendRaceEndIfNeed(() =>
            {
                dialog.Close();
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.RoomMatchHub,
                    new HubViewControllerBase.HubViewInfo() { DefaultViewId = SceneDefine.ViewId.RoomMatchTop });
            });
        }

        /// <summary>
        /// ルームを作成し待機ルームへ
        /// </summary>
        private void CreateRoomAndGotoLobby()
        {
            const int ADJUST_TIME = 2;// 連続実行防止のためexec_timeをずらす
            var timeStamp = TimeUtil.GetServerTimeStamp();// 終了APIで時間が巻き戻ることがあるのであらかじめ保持
            SendRaceEndIfNeed(() =>
            {
                var req = new RoomMatchCreateRoomRequest();
                req.race_instance_id = _currentRoomData.RaceInstanceId;
                req.room_name = _currentRoomData.RoomName;
                req.message = _currentRoomData.Message;
                req.entry_num = _currentRoomData.EntryNum;
                req.start_time_type = (int)WorkDataManager.Instance.RoomMatchData.RaceResultInfo.StartTime;
                req.is_allow_watching = (_currentRoomData.CanWatch) ? 1 : 2;
                req.private_entry_type = (int)_currentRoomData.PrivateSetting;
                req.private_entry_num = _currentRoomData.PrivateSetting == ExhibitionRaceDefine.PrivateSetting.None ? 0 : _currentRoomData.PrivateEntryNum;
                req.season = (int)_currentRoomData.Season;
                req.weather = (int)_currentRoomData.Weather;
                req.ground_condition = (int)_currentRoomData.GroundCondition;
                req.motivation = (int)_currentRoomData.Motivation;
                req.trained_chara_restriction_upper = (int)_currentRoomData.RankRestriction;
                req.trained_chara_restriction_lower = (int)_currentRoomData.RankRestrictionLower;
                req.restrict_chara_info_array = _currentRoomData.RestrictCharaDataList
                    .Select(data =>
                        new RoomMatchRestrictCharaInfo()
                        {
                            chara_id = data.CharaId,
                            dress_id_array = data.DressIdList.Select(id => id.GetDecrypted()).ToArray(),
                        })
                    .ToArray();
                req.entry_chara_array = _currentRoomEntryCharaList
                    .Select(chara =>
                        new RoomMatchEntryChara()
                        {
                            trained_chara_id = chara.Id,
                            member_id = chara.MemberId,
                            running_style = (int)chara.RunningStyle,
                        })
                    .ToArray();
                req.exec_time = timeStamp + ADJUST_TIME;
                req.is_allow_display_flag = _currentRoomData.AllowDisplay ? WorkRoomMatchData.RoomData.SERVER_ALLOW_DISPLAY : 0;
                req.max_user_num = _currentRoomData.MaxTrainerNum;
                req.specified_entry_num = _currentRoomData.MinEntryNum;
                req.Send(res =>
                {
                    WorkDataManager.Instance.RoomMatchData.ApplyCreateRoom(res.data);
                    DialogManager.RemoveAllDialog();
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.RoomMatchHub,
                        new RoomMatchHubViewController.RoomMatchHubViewInfo()
                        {
                            DefaultViewId = SceneDefine.ViewId.RoomMatchLobby,
                            RoomMatchLobbyViewInfo = new RoomMatchLobbyViewInfo(WorkDataManager.Instance.RoomMatchData.CurrentRoomData.RoomId, true),
                        });
                });
            });
        }

        /// <summary>
        /// 必要に応じてルーム終了APIを送る(保存しない)
        /// </summary>
        /// <param name="onSuccess"></param>
        /// <param name="adjustExecTime">別APIと同時に送るときにエラーを回避するために1秒足す</param>
        private void SendRaceEndIfNeed(Action onSuccess)
        {
            if (_isSaved)
            {
                onSuccess?.Invoke();
                return;
            }

            var req = new RoomMatchRaceEndResultRequest();
            req.room_id = _currentRoomData.RoomId;
            req.is_save_result = 0; // レース保存しない
            req.saved_room_id = 0;
            req.register_id = 0;
            req.exec_time = TimeUtil.GetServerTimeStamp();
            req.Send(res =>
            {
                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// 保存レース一覧ダイアログ表示
        /// </summary>
        private void OpenSavedRaceListDialog(DialogCommon saveDialog, Action onSuccess, Action onOverTimeSaveRoomError)
        {
            saveDialog.DispClose();

            // 上書き保存.
            // 詳細を開いている間にCurrentRoomが情報受け渡しの為に書き換わるので記憶しておいたIdを使う.
            DialogRoomMatchOverwriteSaveList.Open(
                _currentRoomData.RoomId,
                onOverwriteData: (savedRoomId, registerId) =>
                {
                    // レース終了API（レース上書き保存）
                    var req = new RoomMatchRaceEndResultRequest();
                    req.room_id = _currentRoomData.RoomId;
                    req.is_save_result = 1; // レース保存する
                    req.saved_room_id = savedRoomId;
                    req.register_id = registerId;
                    req.exec_time = TimeUtil.GetServerTimeStamp();
                    req.Send(res =>
                        {
                            _isSaved = true;
                            // 削除完了ダイアログ
                            var saveRoomDic = WorkDataManager.Instance.RoomMatchData.SaveRoomDictionary;
                            var maxSavedNum = ServerDefine.RoomMatchMaxSavedRaceNum.GetDecrypted();
                            var savedRoomNum = (saveRoomDic.Count < maxSavedNum) ? (saveRoomDic.Count + 1) : maxSavedNum;
                            DialogExhibitionRaceMessageWithSaveRoomNum.Open(
                                title: TextId.RoomMatch0123.Text(),
                                message: TextId.RoomMatch0160.Text(),
                                buttonText: TextId.Common0007.Text(),
                                savedRaceNum: savedRoomNum,
                                savedRaceNumMax: maxSavedNum,
                                onClick: () =>
                                {
                                    onSuccess?.Invoke();
                                });
                        },
                        (_, resultCode) =>
                        {
                            RoomMatchUtil.OnRoomOverTimeServerError(savedRoomId, registerId, resultCode, onOverTimeSaveRoomError);
                        });
                },
                onOverTimeSaveRoomError: onOverTimeSaveRoomError,
                onClose: () => saveDialog.ReOpen(null, false)
                );
        }

        /// <summary>
        /// レースを保存する
        /// </summary>
        private void SaveRace(Action onSuccess)
        {
            // レース終了API（レース保存）
            var req = new RoomMatchRaceEndResultRequest();
            req.room_id = _currentRoomData.RoomId;
            req.is_save_result = 1; // レース保存する
            req.saved_room_id = 0;
            req.register_id = 0;
            req.exec_time = TimeUtil.GetServerTimeStamp();
            req.Send(res =>
            {
                // 保存完了
                _isSaved = true;
                var saveRoomDic = WorkDataManager.Instance.RoomMatchData.SaveRoomDictionary;
                var maxSavedNum = ServerDefine.RoomMatchMaxSavedRaceNum.GetDecrypted();
                var savedRoomNum = (saveRoomDic.Count < maxSavedNum) ? (saveRoomDic.Count + 1) : maxSavedNum;
                DialogExhibitionRaceMessageWithSaveRoomNum.Open(
                    title: TextId.RoomMatch0070.Text(),
                    message: TextId.RoomMatch0073.Text(),
                    buttonText: TextId.Common0007.Text(),
                    savedRaceNum: savedRoomNum,
                    savedRaceNumMax: maxSavedNum,
                    autoClose: false,
                    onClick: () =>
                    {
                        onSuccess?.Invoke();
                    });
            });
        }

        #endregion

        #endregion
    }
}
