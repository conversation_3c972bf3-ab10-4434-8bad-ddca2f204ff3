using DG.Tweening;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    using static WorkTrainedCharaData;
    using static TeamStadiumDeckInfo;

    /// <summary>
    /// チーム競技場: デッキ編成アイテム
    /// </summary>
    [AddComponentMenu("")]
    public class PartsTeamStadiumDeckEntryItem : MonoBehaviour, MasterRaceCourseSet.IRaceCourseInfo
    {
        private readonly Color32 KIMBERLY_TRANSLUCENT = new Color32(98, 93, 125, 127);

        private const string PLAY_IN_UNLOCK_FLASH_LABEL = "in";

        private const string SET_ACTION_UNLOCK_IN_FLASH_LABEL = "unlock_in";

        private const float DELAY_BACK_TO_DECK = 0.25f;

        [SerializeField]
        private GameObject _aceFrameHeader = null;

        [SerializeField]
        private CharacterButton _characterButton = null;

        [SerializeField]
        private ImageCommon _runningStyleIcon = null;

        [SerializeField]
        private TextCommon _unlockText = null;

        [SerializeField]
        private ImageCommon _lockIcon = null;

        [SerializeField]
        private RectTransform _unlockFlashRoot = null;

        private TeamStadiumDeckInfo _deckInfo;

        private MemberInfo _memberInfo;

        private FlashActionPlayer _unlockFlashActionPlayer = null;

        RaceDefine.GroundType MasterRaceCourseSet.IRaceCourseInfo.GroundType => TeamStadiumUtil.GetGroundType(_memberInfo.RaceNumber);

        RaceDefine.CourseDistanceType MasterRaceCourseSet.IRaceCourseInfo.DistanceType => TeamStadiumUtil.GetDistanceType(_memberInfo.RaceNumber);

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="deckInfo"></param>
        /// <param name="memberInfo"></param>
        /// <param name="parentCanvas"></param>
        public void Setup(TeamStadiumDeckInfo deckInfo, MemberInfo memberInfo, Canvas parentCanvas)
        {
            _deckInfo = deckInfo;
            _memberInfo = memberInfo;

            if (_memberInfo.IsLock)
            {
                SetupLock();
            }
            else if (_memberInfo.IsAce)
            {
                SetupAce();
            }
            else
            {
                SetupDefault();
                SetupFlash(parentCanvas);
            }

            _characterButton.SetSizeType(IconBase.SizeType.Chara_75);

            UpdateRunningStyle();
        }

        /// <summary>
        /// エースセットアップ
        /// </summary>
        private void SetupAce()
        {
            SetupDefault();
            _aceFrameHeader.SetActive(true);
        }

        /// <summary>
        /// 通常セットアップ
        /// </summary>
        private void SetupDefault()
        {
            if (_memberInfo.IsEmpty)
            {
                SetupEmpty();
            }
            else
            {
                _aceFrameHeader.SetActive(false);
                _lockIcon.SetActiveWithCheck(false);
                _characterButton.Setup(new CharacterButtonInfo()
                {
                    Id = _memberInfo.TrainedCharaData.CardId,
                    IdType = CharacterButtonInfo.IdTypeEnum.Trained,
                    TrainedChara = _memberInfo.TrainedCharaData,
                    RankImage = true,
                    OnTap = OnTapIcon,
                    OnLongTap = OnLongTapIcon,
                });
            }
        }

        /// <summary>
        /// 未設定セットアップ
        /// </summary>
        private void SetupEmpty()
        {
            _aceFrameHeader.SetActive(false);
            _lockIcon.SetActiveWithCheck(false);
            _characterButton.Setup(new CharacterButtonInfo()
            {
                Id = 0,
                IdType = CharacterButtonInfo.IdTypeEnum.Plus,
                OnTap = OnTapIcon,
            });
            _characterButton.SetEnable(true);
            _characterButton.MyButton.SeType = ButtonCommon.ButtonSeType.DecideM01;
        }

        /// <summary>
        /// 未開放セットアップ
        /// </summary>
        private void SetupLock()
        {
            _aceFrameHeader.SetActive(false);
            _lockIcon.SetActiveWithCheck(true);
            _unlockText.SetActiveWithCheck(true);
            _unlockText.text = TextId.TeamStadium0050.Format(TeamStadiumUtil.GetUnlockClass(_memberInfo.MemberId));
            _characterButton.Setup(new CharacterButtonInfo()
            {
                Id = 0,
                IdType = CharacterButtonInfo.IdTypeEnum.Plus,
            });
            _characterButton.MyButton.SetDisableButtonColor(KIMBERLY_TRANSLUCENT);
            _characterButton.MyButton.SetInteractable(false);
        }

        /// <summary>
        /// アイコンタップコールバック
        /// </summary>
        /// <param name="characterButton"></param>
        private void OnTapIcon(CharacterButton characterButton)
        {
            GoCharacterSelect();
        }

        /// <summary>
        /// アイコンロングタップコールバック
        /// </summary>
        /// <param name="characterButton"></param>
        private void OnLongTapIcon(CharacterButton characterButton)
        {
            DialogTrainedCharacterDetail.Open(characterButton.Info.TrainedChara);
        }

        /// <summary>
        /// 作戦表示更新
        /// </summary>
        private void UpdateRunningStyle()
        {
            if (!_memberInfo.IsEmpty && !_memberInfo.IsLock)
            {
                _runningStyleIcon.SetActiveWithCheck(true);
                _runningStyleIcon.sprite = RaceUtil.GetRunningStyleIconSprite(_memberInfo.RunningStyle);
            }
            else
            {
                _runningStyleIcon.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// キャラ選択Viewへ遷移
        /// </summary>
        private void GoCharacterSelect()
        {
            var viewInfo = new TeamStadiumCharacterSelectViewInfo();
            var charaId = _memberInfo.IsEmpty ? 0 : _memberInfo.TrainedCharaData.CharaId.GetDecrypted();
            var members = _deckInfo.GetMemberList().Where(x => !x.IsEmpty);
            var trainedData = _memberInfo.IsEmpty ? null : WorkDataManager.Instance.TrainedCharaData.Get(_memberInfo.TrainedCharaData.Id);
            viewInfo.SetupParam = new PartsRaceEntryCharacterSelect.SetupParam()
            {
                ViewType = PartsRaceEntryCharacterSelect.ViewType.TeamEdit,
                TrainedCharaList = WorkDataManager.Instance.TrainedCharaData.List,
                InitialSelectedList = _memberInfo.IsEmpty ? null : new List<TrainedCharaData> { trainedData },
                ReservedCharaDic = members.ToDictionary(x => x.TrainedCharaData, y => TeamStadiumUtil.GetRaceName(y.RaceNumber)),
                GetUnselectableReason = data => TextId.TeamStadium0040.Text(),

                SelectableWithReasonCharaIdList = members.Select(x => x.TrainedCharaData.CharaId.GetDecrypted()).Where(x => x != charaId).Distinct().ToList(),
                GetSelectableNoticeIconText = data => TextId.Outgame0406.Text(),
                CourseInfo = this,
                OnDecide = OnDecide
            };
            viewInfo.SelectedMemberInfo = _memberInfo;
            viewInfo.CurrentDeckInfo = _deckInfo;

            // ハケアニメ開始
            UIManager.Instance.LockGameCanvas(); // ハケアニメ中は入力禁止
            UIManager.Footer.Hide();
            NowLoading.Instance.Show(NowLoading.Type.WhiteOutWithHorseShoe, () => 
            { 
                UIManager.Instance.UnlockGameCanvas(); // ハケアニメが終わったので入力禁止解除

                SceneManager.Instance.ChangeView(SceneDefine.ViewId.TeamStadiumCharacterSelect, viewInfo);
            });
        }

        /// <summary>
        /// キャラ選択コールバック
        /// </summary>
        /// <param name="selectedList"></param>
        private void OnDecide(List<TrainedCharaData> selectedList)
        {
            if (selectedList != null && selectedList.Any())
            {
                OpenRunStyleDialog(selectedList[0], true);
            }
            else
            {
                UIManager.Footer.Hide();
                NowLoading.Instance.Show(NowLoading.Type.WhiteOutWithHorseShoe, () =>
                {
                    SceneManager.Instance.ChangeView(SceneDefine.ViewId.TeamStadiumDeck);
                });
            }
        }

        /// <summary>
        /// 走法選択ダイアログを開く
        /// </summary>
        /// <param name="trainedCharaData"></param>
        /// <param name="backPrevView"></param>
        private void OpenRunStyleDialog(TrainedCharaData trainedCharaData, bool backPrevView = false)
        {
            var selected = trainedCharaData.RunningStyle;
            if (trainedCharaData.Id == _memberInfo.TrainedCharaData?.Id && _memberInfo.RunningStyle != RaceDefine.RunningStyle.None)
            {
                selected = _memberInfo.RunningStyle;
            }

            // 走法選択ダイアログ
            DialogSetRunStyle.ShowDialog(
                trainedCharaData.CharaId,
                DialogSetRunStyle.GetRunStyleRankDic(trainedCharaData),
                selectedRunningStyle: selected,
                recommendRunningStyle: RaceDefine.RunningStyle.None,
                null, true, OnConfirmed);

            /// <summary>
            /// 走法設定コールバック
            /// </summary>
            void OnConfirmed(RaceDefine.RunningStyle runningStyle)
            {
                var releaseMemberInfo = _deckInfo.GetMemberList().Where(x => !x.IsEmpty)
                    .FirstOrDefault(x => x.TrainedCharaData.Id != trainedCharaData.Id && 
                        x.TrainedCharaData.CharaId == trainedCharaData.CharaId);

                if (releaseMemberInfo != null)
                {
                    // 現在の編成から重複キャラを外す
                    releaseMemberInfo.Clear();
                }

                _memberInfo.TrainedCharaData = trainedCharaData;
                _memberInfo.RunningStyle = runningStyle;
                UpdateRunningStyle();
                if (backPrevView)
                {
                    DOVirtual.DelayedCall(DELAY_BACK_TO_DECK, () =>
                    {
                        UIManager.Footer.Hide();
                        NowLoading.Instance.Show(NowLoading.Type.WhiteOutWithHorseShoe, () =>
                        {
                            SceneManager.Instance.ChangeView(SceneDefine.ViewId.TeamStadiumDeck);
                        });
                    });
                }
            }
        }

        /// <summary>
        /// Flashセットアップ
        /// </summary>
        /// <param name="parentCanvas"></param>
        private void SetupFlash(Canvas parentCanvas) 
        {
            if (_unlockFlashActionPlayer == null && _memberInfo.TrainedCharaData == null)
            {
                var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.TEAM_STADIUM_UNLOCK_DECK_EDIT_FLASH_ACTION_PATH, SceneDefine.ViewId.TeamStadiumDeck);
                var obj = GameObject.Instantiate(prefab, _unlockFlashRoot);
                _unlockFlashActionPlayer = obj.GetComponent<FlashActionPlayer>();
                _unlockFlashActionPlayer.LoadFlashPlayer();
                _unlockFlashActionPlayer.gameObject.SetLayerRecursively(_unlockFlashRoot.gameObject.layer);
                _unlockFlashActionPlayer.SetSortOffset(parentCanvas.sortingOrder + 1);
                _unlockFlashActionPlayer.SetSortLayer(parentCanvas.sortingLayerName);
                _unlockFlashActionPlayer.FlashPlayer.Init();
            }
        }

        /// <summary>
        ///  編成枠解放演出
        /// </summary>
        public void UnlockIn()
        {
            if (_unlockFlashActionPlayer != null && _memberInfo.TrainedCharaData == null)
            {
                _unlockText.SetActiveWithCheck(true);
                _unlockText.text = TextId.TeamStadium0050.Format(TeamStadiumUtil.GetUnlockClass(_memberInfo.MemberId));
                _characterButton.MyButton.SetInteractable(false);

                _unlockFlashActionPlayer.FlashPlayer.SetActionCallBack(SET_ACTION_UNLOCK_IN_FLASH_LABEL, () => 
                {
                    _unlockText.SetActiveWithCheck(false);
                    _characterButton.MyButton.SetInteractable(true);

                }, AnimateToUnity.AnMotionActionTypes.Start);

                _unlockFlashActionPlayer.Play(PLAY_IN_UNLOCK_FLASH_LABEL);
            }
        }
        
        #region チュートリアル専用処理

        /// <summary>
        /// 
        /// </summary>
        public void TutorialFocusCharaButton(System.Action<CharacterButton> toFocus)
        {
            toFocus(_characterButton);
        }
        
        #endregion        
    }
}
