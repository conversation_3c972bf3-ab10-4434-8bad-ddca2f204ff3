using System.Collections;
using UnityEngine;

namespace Gallop
{
    using static StaticVariableDefine.TeamStadium.TeamStadiumDeckHubViewController;
    /// <summary>
    /// チーム競技場デッキHubView
    /// </summary>
    [AddComponentMenu("")]
    public class TeamStadiumDeckHubView : ViewBase
    {
    }

    /// <summary>
    /// チーム競技場デッキHub
    /// Viewコントローラー
    /// </summary>
    public class TeamStadiumDeckHubViewController : HubViewControllerBase
    {
        /// <summary>
        /// 子ビュー配列取得
        /// </summary>
        public override SceneDefine.ViewId[] GetChildViewIdArray()
        {
            return VIEW_ID_ARRAY;
        }

        /// <summary>
        /// 子ビューに渡すViewInfo選定
        /// </summary>
        public override IViewInfo GetChildViewInfo(SceneDefine.ViewId viewId, IViewInfo viewInfo)
        {
            return viewInfo;
        }

        /// <summary>
        /// 終了処理
        /// </summary>
        public override IEnumerator FinalizeView()
        {
            yield return base.FinalizeView();
        }
    }
}
