using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// スコア詳細ダイアログのスコア名＋スコア数値表示用プレート。
    /// </summary>
    /// <remarks>
    /// </remarks>
    //-------------------------------------------------------------------
    [AddComponentMenu("")]
    public class PartsTeamStadiumHistoryScoreDetailPlate : IconBase
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        /// <summary>スコアが正の値のフォントカラー。</summary>
        private const FontColorType VALUE_COLOR_POSITIVE = FontColorType.Plus;
        /// <summary>スコアが負の値のフォントカラー。</summary>
        private const FontColorType VALUE_COLOR_NEGATIVE = FontColorType.Minus;

        /// <summary>スコア内訳で表示する素点の表示優先度。</summary>
        private const int RAW_SCORE_PRIORITY = 0;
        
        /// <summary>
        /// スコア内訳に表示する内容。
        /// </summary>
        struct BreakDownItem
        {
            public readonly int Priority; // 表示優先度。小さい値ほどリストの上に表示される。
            public readonly string Name; // 内訳名。
            public readonly int Score; // スコア。

            public BreakDownItem(int priority, string name, int score)
            {
                Priority = priority;
                Name = name;
                Score = score;
            }
        }
        
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>内訳の有り/無しで下部のスペースを調整するため。</summary>
        [SerializeField] 
        private VerticalLayoutGroup _rootVerticalLayout = null;
        [SerializeField]
        private RectTransform _rootRectTransform = null;
        /// <summary>スコア名テキスト。</summary>
        [SerializeField] 
        private TextCommon _scoreName = null;
        /// <summary>スコア数値テキスト。</summary>
        [SerializeField] 
        private TextCommon _scoreValue = null;
        /// <summary>スコア内訳のルート。</summary>
        [SerializeField] 
        private GameObject _scoreBreakDownRoot = null;
        /// <summary>長押しで詳細表示するために。</summary>
        [SerializeField]
        private ButtonCommon _button = null;

        /// <summary>表示するScoreData。</summary>
        public TeamStadiumRaceHistoryUtil.TeamStadiumResultScoreData ScoreData { get; private set; }
        /// <summary>team_stadium_raw_score.csvのid</summary>
        public int RawScoreId => ScoreData.RawScoreId;
        /// <summary>RawScoreIdを獲得した数。</summary>
        public int Num => ScoreData.Num;
        /// <summary>RawScoreIdで獲得したスコア合計値。</summary>
        public int ScoreValue => ScoreData.Score;
        
        /// <summary>長押しによる説明文を表示中かどうか。</summary>
        private bool _isDisplayLongTapInfo;

        public bool HasBonus => ScoreData.BonusArray.Length > 0;

        public float NowHeight => this.CachedRectTransform.rect.height;

        /// <summary>
        /// 初期化。
        /// </summary>
        /// <param name="score">表示用データ</param>
        /// <param name="fromLongTapPopup">ポップアップから呼び出されているかどうか</param>
        /// 
        public void Setup(TeamStadiumRaceHistoryUtil.TeamStadiumResultScoreData score, bool fromLongTapPopup = false)
        {
            ScoreData = score; 
            SetupScoreName(RawScoreId, Num);
            SetupScoreValue(ScoreValue);
            //ポップアップ表示中、長押しのイベントを設定する必要がない
            if (!fromLongTapPopup)
            {
                SetupOpenDetailButton();
            }
            SetupBreakDown(score);
            //ポップアップからの呼び出しの場合ポップアップ表示中フラグをTureに
            _isDisplayLongTapInfo = fromLongTapPopup;

            if (_button != null)
            {
                _button.SetEnable(!fromLongTapPopup);
            }

            // #93262: ContentSizeFitterでのサイズ確定は1フレーム待つ必要がある
            // 待ってからの確定だと配下のButtonCollisionのサイズが適切な値にならないので
            // 強制再構築で1フレーム待たずにサイズを確定させる
            LayoutRebuilder.ForceRebuildLayoutImmediate(_rootRectTransform);
        }

        /// <summary>
        /// 内訳表示が必要かどうか。
        /// </summary>
        private static bool IsNeedBreakDown(TeamStadiumRaceHistoryUtil.TeamStadiumResultScoreData score)
        {
            return score.Score > 0;
        }

        /// <summary>
        /// 素点を算出する。
        /// </summary>
        /// <param name="score"></param>
        /// <returns></returns>
        private static int CalcRawScore(TeamStadiumRaceHistoryUtil.TeamStadiumResultScoreData score)
        {
            int rawScore = score.Score - SumAllBonus(score);
            return rawScore;
        }

        /// <summary>
        /// スコアボーナス値の合計を取得する。
        /// </summary>
        /// <param name="score"></param>
        /// <returns></returns>
        private static int SumAllBonus(TeamStadiumRaceHistoryUtil.TeamStadiumResultScoreData score)
        {
            if (score?.BonusArray == null)
            {
                return 0;
            }

            int total = 0;
            for (int i = 0; i < score.BonusArray.Length; i++)
            {
                total += score.BonusArray[i].BonusScore;
            }
            return total;
        }

        /// <summary>
        /// スコアボーナス名を取得する。
        /// </summary>
        /// </summary>
        /// <param name="bonus"></param>
        /// <returns></returns>
        private static string GetBonusName(TeamStadiumRaceHistoryUtil.TeamStadiumResultBonusData bonus)
        {
            if (bonus == null)
            {
                return string.Empty;
            }

            return TextUtil.GetMasterText(MasterString.Category.TeamStadiumScoreBonusName, bonus.ScoreBonusId);
        }

        /// <summary>
        /// 内訳初期化。
        /// </summary>
        private void SetupBreakDown(TeamStadiumRaceHistoryUtil.TeamStadiumResultScoreData score)
        {
            if (!IsNeedBreakDown(score))
            {
                // 内訳表示しない時は下のパディングを無効にしてレイアウト整える。
                _rootVerticalLayout.padding.bottom = 0;
                
                _scoreBreakDownRoot.gameObject.SetActive(false);
                return;
            }
            
            var platePrefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.TEAM_STADIUM_SCORE_BREAKDOWN_PLATE_PATH);

            var breakDownList = new List<BreakDownItem>();
            breakDownList.Add(new BreakDownItem(RAW_SCORE_PRIORITY, TextUtil.GetStaticText(TextId.TeamStadium0088), CalcRawScore(score)));
            foreach (var bonus in score.BonusArray)
            {
                var masterBonus = MasterDataManager.Instance.masterTeamStadiumScoreBonus.Get(bonus.ScoreBonusId);
                if (masterBonus == null)
                {
                    continue;
                }
                
                // priority=0は基礎点として予約。csvに0が入っていたら警告する。
                DebugUtils.Assert(masterBonus.Priority != RAW_SCORE_PRIORITY, $"team_stadium_bonus.csvにpriority=0が入力されています。id={masterBonus.Id}");
                
                breakDownList.Add(new BreakDownItem(masterBonus.Priority, GetBonusName(bonus), bonus.BonusScore));
            }
            // priorityが小さいものから順に表示する。
            breakDownList.Sort((a, b) => a.Priority - b.Priority);

            PartsTeamStadiumScoreBreakDownPlate plateComponent = null;
            for (int i = 0; i < breakDownList.Count; ++i)
            {
                if (breakDownList[i].Score <= 0)
                {
                    continue;
                }
                
                // プレートインスタンス生成。
                var plateObj = GameObject.Instantiate(platePrefab, _scoreBreakDownRoot.transform);
                plateComponent = plateObj.GetComponent<PartsTeamStadiumScoreBreakDownPlate>();
                
                // プレートに表示する情報を設定。
                plateComponent.Setup(breakDownList[i].Name, breakDownList[i].Score);
                plateComponent.SetSeparatorActive(true);
            }
            // 一番下の内訳には区切り線は不要。
            if (plateComponent != null)
            {
                plateComponent.SetSeparatorActive(false);
            }
        }

        /// <summary>
        /// スコア素点名称初期化。
        /// </summary>
        private void SetupScoreName(int rawScoreId, int num)
        {
            var masterRawScore = MasterDataManager.Instance.masterTeamStadiumRawScore.Get(rawScoreId);
            if (masterRawScore == null)
            {
                return;
            }

            _scoreName.text = masterRawScore.ScoreName;

            // 例「レアスキル発動 x 3」のようなスコア名称 + 回数を表示。「x１」は表示しない
            if (num > 1)
            {
                _scoreName.text += " " + TextId.Common0255.Format(num);
            }
        }

        /// <summary>
        /// スコア値初期化。
        /// </summary>
        /// <param name="value"></param>
        private void SetupScoreValue(int value)
        {
            _scoreValue.text = TextId.TeamStadium0063.Format(value);
            _scoreValue.FontColor = value >= 0 ? VALUE_COLOR_POSITIVE : VALUE_COLOR_NEGATIVE;
        }

        /// <summary>
        /// プレート長押しで説明文表示するボタン初期化。
        /// </summary>
        private void SetupOpenDetailButton()
        {
            _button.SetOnLongTap(OnLongTap);
        }

        /// <summary>
        /// プレート長押しコールバック。
        /// </summary>
        private void OnLongTap()
        {
            if (_isDisplayLongTapInfo)
            {
                return;
            }
            
            _isDisplayLongTapInfo = true;
            var longTapInfo = OnLongTap<PartsTeamStadiumHistoryScoreDetailLongTapInfoPop, PartsTeamStadiumHistoryScoreDetailPlate>(
                this,
                ResourcePath.TEAM_STADIUM_HISTORY_SCORE_DETAIL_POPUP,
                () => _isDisplayLongTapInfo = false);
            longTapInfo.Setup(this);
        }
    }
}
