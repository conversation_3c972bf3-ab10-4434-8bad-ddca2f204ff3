using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using static Gallop.WorkTrainedCharaData;

namespace Gallop
{
    /// <summary>
    /// チーム競技場: ランキング情報ダイアログ
    /// https://xxxxxxxxxx/app/#/projects/5e44a4f586e4655b009a56d3/screens/5e44a53086e4655b009a5701
    /// </summary>
    [AddComponentMenu("")]
    public class DialogTeamStadiumRankingInfoTeamList : DialogInnerBase
    {
        #region SerializeFields

        [SerializeField] private RawImageCommon _imageSticker = null;
        [SerializeField] private ButtonCommon _userDetailButton = null;
        [SerializeField] private ImageCommon _classTextImage = null;
        [SerializeField] private BitmapTextCommon _rankNumBitmapText = null;
        [SerializeField] private BitmapTextCommon _pointNumBitmapText = null;
        [SerializeField] private TextCommon _textTrainerName = null;
        [SerializeField] private ScrollRectCommon _scroll = null;
        [SerializeField] private DialogTeamStadiumRankingInfoTeamListItem[] _dialogTeamStadiumRankingInfoTeamListItemArray = null;

        #endregion

        #region Overrides

        /// <summary>
        /// DialogCommonDataを作成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.TeamStadium0065.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            return data;
        }

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        /// <returns></returns>
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        /// <returns></returns>
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        #endregion

        #region Methods

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(TeamStadiumRanking ranking)
        {
            var ins = LoadAndInstantiatePrefab<DialogTeamStadiumRankingInfoTeamList>(ResourcePath.DIALOG_TEAM_STADIUM_RANKING_INFO_TEAM_LIST);

            var dialogData = ins.CreateDialogData();
            dialogData.ContentsObject = ins.gameObject;
            var dialog = DialogManager.PushDialog(dialogData);

            ins.Initialize(ranking, dialog);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="ranking"></param>
        /// <param name="dialog"></param>
        private void Initialize(TeamStadiumRanking ranking, DialogCommon dialog)
        {
            var userDetailsInfo = WorkDataManager.Instance.TeamStadiumData.TeamStadiumRankingUserDetailsInfo;
            var userInfo = userDetailsInfo.GetUserInfo(ranking.viewer_id);

            string path = ResourcePath.GetCharaStandMediumImagePath(userInfo.leader_chara_id, userInfo.leader_chara_dress_id);

            _imageSticker.texture = ResourceManager.LoadOnView<Texture2D>(path);
            _userDetailButton.SetOnClick(() => { OpenDialogTrainerInfo(userInfo); });

            _classTextImage.sprite = TeamStadiumUtil.GetClassTextSprite(ranking.team_class);
            _rankNumBitmapText.text = TeamStadiumUtil.GetRankText(ranking.rank);
            _pointNumBitmapText.text = TeamStadiumUtil.GetPointText(ranking.best_point, true);
            _textTrainerName.text = userInfo.name;

            var teamDataList = userDetailsInfo.GetTeamDataList(ranking.viewer_id);

            UpdateView(teamDataList, userInfo.viewer_id);
        }

        /// <summary>
        /// スクロールビュー更新
        /// </summary>
        /// <param name="teamDataList"></param>
        /// <param name="viewerId"></param>
        private void UpdateView(List<TeamStadiumTeamData> teamDataList, long viewerId)
        {
            var raceNumRange = Enumerable.Range(TeamStadiumDefine.RACE_NUM1, TeamStadiumDefine.MAX_RACE_NUMBER).ToList();
            var teamsByDistanceTypeGroup = teamDataList.GroupBy(x => x.distance_type);

            for (int i = 0, n = raceNumRange.Count(); i < n; i++)
            {
                var team = teamsByDistanceTypeGroup.First(x => x.Key == raceNumRange[i]).ToList();

                _dialogTeamStadiumRankingInfoTeamListItemArray[i].Setup(viewerId, team, raceNumRange[i], (trainedCharaId) =>
                {
                    // キャラクターをクリックしたとき
                    OpenDialogTrainedCharacterDetail(viewerId, trainedCharaId);
                });
            }

            _scroll.verticalNormalizedPosition = 1f;
        }

        /// <summary>
        /// トレーナー情報ダイアログを開く
        /// </summary>
        /// <param name="userInfo"></param>
        private void OpenDialogTrainerInfo(UserInfoAtFriend userInfo)
        {
            DialogTrainerInfo.PushDialog(userInfo.viewer_id);
        }

        /// <summary>
        /// ウマ娘詳細ダイアログを開く
        /// </summary>
        /// <param name="targetViewerId"></param>
        /// <param name="trainedCharaId"></param>
        private void OpenDialogTrainedCharacterDetail(long targetViewerId, int trainedCharaId)
        {

            if (targetViewerId == WorkDataManager.Instance.UserData.ViewerId)
            {
                var trainedChara = WorkDataManager.Instance.TrainedCharaData.Get(trainedCharaId);
                DialogTrainedCharacterDetail.Open(trainedChara);
            }
            else
            {
                var charaDetails = WorkDataManager.Instance.TeamStadiumData.TeamStadiumRankingUserDetailsInfo.GetTrainedCharaDetails(targetViewerId, trainedCharaId);
                var trainedChara = new TrainedCharaData(charaDetails);

                var parameter = DialogTrainedCharacterDetail.CreateSetupParameter(trainedChara);
                DialogTrainedCharacterDetail.OpenWithGetViewerName(parameter, trainedChara.ViewerId);
            }
        }
        #endregion
    }
}