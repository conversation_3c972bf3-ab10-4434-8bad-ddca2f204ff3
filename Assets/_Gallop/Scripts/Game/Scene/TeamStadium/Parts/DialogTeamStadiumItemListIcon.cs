using System;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;

namespace Gallop
{
    using static StaticVariableDefine.TeamStadium.DialogTeamStadiumItemListIcon;
    /// <summary>
    /// チーム競技場: アイテム選択画面リストアイコン
    /// </summary>
    public class DialogTeamStadiumItemListIcon : MonoBehaviour
    {
        #region SerializeFields, Variables

        [SerializeField] private ItemIcon _icon = null;
        [SerializeField] private PartsButtonAccessory _partsButtonAccessory = null;
        [SerializeField] private ButtonCommon _buttonCommon = null;
        [SerializeField] private PartsUseItemList _useItemList = null;

        /// <summary>使用上限アイコン（「あと〇回」アイコン）</summary>
        [SerializeField] private PartsItemUseNumLimitIcon _limitIcon = null;

        /// <summary>装飾の強調アニメ</summary>
        [SerializeField] private ImageCommon _outsideBlinkImage = null;


        /// <summary>アイテムを1個以上所持しているか</summary>
        private bool _hasItem = true;
        public bool HasItem => _hasItem;

        public ItemIcon ItemIcon => _icon;
        public bool IsSelected { get; private set; } = false;
        public bool HasSelectedOppositeItem { get; private set; } = false;

        /// <summary>装飾情報</summary>
        private PartsUseItemList.ItemDecorationInfo _itemDecorationInfo = null;
        private Tweener _outsideBlinkTweener;

        #endregion

        #region Method

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="itemData"></param>
        /// <param name="onClick"></param>
        /// <param name="itemDecorationInfo">装飾情報（省略可）</param>
        public void Setup(WorkItemData.ItemData itemData, Action<DialogTeamStadiumItemListIcon> onClick,
                          PartsUseItemList.ItemDecorationInfo itemDecorationInfo = null)
        {
            if (itemData == null)
                return;

            var itemNum = itemData.ItemNum;
            _hasItem = (0 < itemNum);

            _itemDecorationInfo = itemDecorationInfo;

            _icon.SetData(
                itemData.ItemMaster.ItemCategory,
                itemData.ItemId,
                number: itemNum,
                numDisp: true,
                showZero: true,
                opt: 0,
                isInfoPop: true,
                parentCanvas: UIManager.DialogCanvas,
                // アイテムアイコンタップ時
                onClick: () =>
            {
                // 上限回数が設定されており、「あと0回」なら
                if (_itemDecorationInfo != null && _itemDecorationInfo.IsLimitReached())
                {
                    // 回数制限により使用できない旨をユーザーに伝えるためのNotification
                    UIManager.Instance.ShowNotification(_itemDecorationInfo.LimitNotificationMessage);
                }
                // アイテムを未所持なら
                else if (!_hasItem)
                {
                    // アイテムの所持数が０の場合はリスト・アイコンの状態に関わらず
                    // 同じ警告文を表示するのでここで設定
                    UIManager.Instance.ShowNotification(TextId.Race0672.Text());
                }

                onClick.Invoke(this);
            });

            _icon.SetSize(IconBase.SizeType.Large);
            _buttonCommon.SeType = ButtonCommon.ButtonSeType.DecideS01;
            _icon.SetIsSyncNumOutlineAlphaWithParent(true);

            // 装飾のセットアップ
            SetupDecoration();

            // アイテムの所持数が０の場合
            if (!_hasItem)
            {
                _buttonCommon.SetLookEnableColor(false);
                _buttonCommon.SeType = ButtonCommon.ButtonSeType.CancelM02;
            }

            // アイテムアイコンの上にさらに乗せるアイコンの初期化
            _partsButtonAccessory.SetActiveWithCheck(true);
            _partsButtonAccessory.SetCheckMarkPos(CHECK_MARK_POS);
            _partsButtonAccessory.SetNoticeIconPos(OPPOSITE_MARK_POS);
        }

        /// <summary>
        /// 選択済にする
        /// </summary>
        /// <param name="count"></param>
        public void SetSelected(int count)
        {
            IsSelected = (0 < count);
            _partsButtonAccessory.SetAnimCheckMark(IsSelected);
            _buttonCommon.SeType = ButtonCommon.ButtonSeType.CancelS01;
            _buttonCommon.SetLookEnableColor(!IsSelected);
        }

        /// <summary>
        /// 逆の効果アイテムのフラグ設定
        /// </summary>
        /// <param name="hasSelectedOppositeItem"></param>
        public void SetSelectedOppositeItemFlag(bool hasSelectedOppositeItem)
        {
            if (!_hasItem) return;

            HasSelectedOppositeItem = hasSelectedOppositeItem;
            _partsButtonAccessory.SetNoticeIcon(hasSelectedOppositeItem, TextId.Race0666.Text());
            _buttonCommon.SetLookEnableColor((hasSelectedOppositeItem) ? false : true);
            _buttonCommon.SeType = hasSelectedOppositeItem ? ButtonCommon.ButtonSeType.CancelM02 : ButtonCommon.ButtonSeType.DecideS01;
        }

        /// <summary>
        /// アイコン（ボタン）の状態を変える
        /// </summary>
        /// <param name="isSelectable"></param>
        public void SetSelectable(bool isSelectable)
        {
            _buttonCommon.SetNotificationMessage(null);
            _buttonCommon.SetLookEnableColor(true);

            // アイテム個数がないならグレーアウト
            if (!_hasItem)
            {
                _buttonCommon.SetLookEnableColor(false);
                _buttonCommon.SeType = ButtonCommon.ButtonSeType.CancelM02;
                return;
            }

            // 逆の効果アイテムを選択しているならグレーアウトしてNotificationをセット
            if (HasSelectedOppositeItem)
            {
                _buttonCommon.SetLookEnableColor(false);
                _buttonCommon.SetNotificationMessage(TextId.Race408008.Text());
                _buttonCommon.SeType = ButtonCommon.ButtonSeType.CancelM02;
                return;
            }

            // アイテム選択済のものを選択不可能にしようとしてる
            if (!isSelectable && IsSelected) return;

            // SEの変更(選択不可、選択可能、選択済み)
            _buttonCommon.SeType = IsSelected ? ButtonCommon.ButtonSeType.CancelS01 : ButtonCommon.ButtonSeType.DecideS01;
            if (HasSelectedOppositeItem || !_hasItem)
            {
                _buttonCommon.SeType = ButtonCommon.ButtonSeType.CancelM02;
            }

            // 限界数まで選択済みなため選択できないなら Notification をセット
            if (_useItemList.IsLimitSelected() && _hasItem && !HasSelectedOppositeItem && !isSelectable)
            {
                _buttonCommon.SetNotificationMessage(TextId.Race0667.Text());
                _buttonCommon.SetLookEnableColor(false);
                _buttonCommon.SeType = ButtonCommon.ButtonSeType.CancelM02;
            }
        }

        /// <summary>
        /// 装飾のセットアップ
        /// </summary>
        private void SetupDecoration()
        {
            // 装飾のオブジェクトはデフォルト非表示
            _limitIcon.SetActiveWithCheck(false);
            _outsideBlinkImage.SetActiveWithCheck(false);

            // 装飾情報が設定されていないなら早期リターン
            if (_itemDecorationInfo == null)
                return;

            // 必要なら上限回数アイコンを表示する
            {
                bool isNeedLimitIcon = _itemDecorationInfo.IsNeedLimitIcon;
                _limitIcon.SetActiveWithCheck(isNeedLimitIcon);
                if (isNeedLimitIcon)
                {
                    _limitIcon.Setup(_itemDecorationInfo.LimitNumLeft);

                    // 上限回数に達しているなら、アイテム未所持と同じ動作になるようにする
                    bool isLimitReached = (_itemDecorationInfo != null && _itemDecorationInfo.IsLimitReached());
                    if (isLimitReached)
                    {
                        _hasItem = false;
                    }
                }
            }

            // 必要なら強調アニメを再生する
            {
                bool isNeedOutsideBlink = _itemDecorationInfo.IsNeedOutsideBlink;
                _outsideBlinkImage.SetActiveWithCheck(isNeedOutsideBlink);
                if (isNeedOutsideBlink)
                {
                    PlayOutsideBlinkAnimation();
                }
            }
        }

        /// <summary>
        /// 装飾の強調アニメを再生
        /// </summary>
        private void PlayOutsideBlinkAnimation()
        {
            if (_outsideBlinkTweener != null)
            {
                _outsideBlinkTweener.Kill();
            }
            _outsideBlinkImage.color = GameDefine.COLOR_CLEAR_WHITE;
            _outsideBlinkTweener = _outsideBlinkImage.DOFade(1, 1f).SetEase(Ease.OutCubic).SetLoops(-1, LoopType.Yoyo);
        }

        #endregion
    }
}