using System.Collections.Generic;
using System.Linq;
using Cute.Cri;
using UnityEngine;
using FadeCurve = Cute.Cri.FadeCurve;
using static Gallop.StaticVariableDefine.Story.StoryTimelineController;

namespace Gallop
{
    /// <summary>
    /// ストーリのタイムラインコントローラー
    /// </summary>
    public partial class StoryTimelineController : MonoBehaviour
    {
        #region 定数

        /// <summary>
        /// 設定されていないなどで使用
        /// </summary>
        public const int INVALID_VALUE = -1;

        /// <summary>
        /// 育成イベント系のcsvで未指定の場合に割り当てされる値
        /// </summary>
        public const int INVALID_EVENT_SETTING = 0;

        /// <summary>
        /// OffsetIdで指定していない場合の値(この場合background_dataの位置データを参照する)
        /// </summary>
        public const int INVALID_OFFSET_ID = 0;

        /// <summary>
        /// サウンド停止用のキュー名
        /// </summary>
        private const string MUTE_CUE_NAME = "mute";

        /// <summary>
        /// 次の画面タッチが解禁されるまでの間隔
        /// </summary>
        private const int TOUCH_BLOCK_INTERVAL = 4;

        /// <summary>
        /// 連打していると判定されるまでの間隔
        /// </summary>
        private const int CONTINUOUS_TOUCH_INTERVAL = 10;

        private const int WORK_STRINGBUILDER_BUFFER_SIZE = 128;

        // テキスト差分関連
        private const int PARAMETER_BRANCH = (int)StoryTimelineTextClipData.TextDifferenceType.ParameterBranch;
        private const int FIRST_SELECT_INDEX = 1;

        // 倍速用フレームのIndex
        private const int FRAME_COUNT_INDEX_1ST = 0;
        private const int FRAME_COUNT_INDEX_END = 1;

        /// <summary>
        /// 倍速用のカウント基準
        /// </summary>
        public const int SKIP_COUNT_ALIGNMENT = 3; // 3回ごとに処理が戻る
        public const int FULL_MESSAGE_COUNT = 0; // 3 % 0のときセリフ1f表示
        public const int HIDE_MESSAGE_COUNT = 1; // 3 % 1のときセリフ非表示
        public const int SKIP_MESSAGE_COUNT = 2; // 3 % 2のときセリフスキップ

        //常駐させるキューシートの属性ID
        const int CUESHEET_ATTRIBUTE_ALWAYS = 1;

        #endregion 定数

        #region Enum

        /// <summary>
        /// 表示モード
        /// </summary>
        public enum DisplayMode
        {
            /// <summary>シングルモードやパドックなどの縦持ち全画面レイアウト</summary>
            SingleMode,

            /// <summary>メインストーリーやLiveなどの縦持ちレイアウト</summary>
            Portrait,

            /// <summary>メインストーリーやLiveなどの横持ち全画面レイアウト</summary>
            Landscape,

            /// <summary>縦持ち全画面だがテキストフレームではなく字幕表示になる</summary>
            SingleModePrologue,
        }

        /// <summary>
        /// ワープ処理の段階
        /// </summary>
        public enum WarpStep
        {
            None = 0,

            Reserve = 1,

            Exec = 2,
        }

        /// <summary>倍速再生タイプ</summary>
        public enum HighSpeedType
        {
            None, //PlayMode == GrandLiveの場合処理上「None == SpeedX1」になるが、UIは既存挙動と同じ
            SpeedX1,
            HighSpeedX4,
            Max,
        }

        /// <summary>再生モード</summary>
        public enum PlayMode
        {
            /// <summary>通常ストーリー</summary>
            Default,
            /// <summary>ライブ編グランドライブ特別演出</summary>
            GrandLive
        }

        #endregion Enum

        #region 変数

        #region 基本データ

        /// <summary>
        /// タイムラインのデータ
        /// </summary>
        [SerializeField]
        private StoryTimelineData _timelineData = null;

        public StoryTimelineData TimelineData
        {
            set { _timelineData = value; }
            get { return _timelineData; }
        }

        /// <summary>
        /// プレイ済みBlockのIndexリスト
        /// 119801 要素を追加する際に「UpdatePlayedBlockIndexList」関数を利用してください
        /// 上記関数利用しない場合リスト内のIndexは必ず連番になるようにしてください
        /// </summary>
        public List<int> PlayedBlockIndexList { get; } = new List<int>();

        /// <summary>
        /// 別タイムラインとのブレンドを行う
        /// </summary>
        private StoryTimelineBlockData _prevBlockData;

        public StoryTimelineBlockData PrevBlockData
        {
            get { return _prevBlockData; }
            set { _prevBlockData = value; }
        }

        /// <summary>
        /// 116290 外部にある、選択肢を持っているかどうかを判定する関数を実行する
        /// </summary>
        public delegate StoryTimelineTextClipData CheckChoiseBlockDelegate(List<StoryTimelineTextClipData> skippedClipList, int frameCount);
        public CheckChoiseBlockDelegate CheckChoiseBlock = null;

        /// <summary>
        /// 138583 外部にある、強制オート用のデリゲート
        /// </summary>
        public delegate void ForceAutoDelegate();
        public ForceAutoDelegate OnForceAuto = null;

        /// <summary>
        /// 138583 外部にある、強制オート解除用のデリゲート
        /// </summary>
        public delegate void ResetForceAutoDelegate();
        public ResetForceAutoDelegate OnResetForceAuto = null;


        /// <summary>
        /// 現在のストーリーで読み込んだVoiceCueSheet名のリスト
        /// </summary>
        private List<string> _loadedVoiceCueSheetNameList = new List<string>();

        #region 録画関連

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// 連続録画中のStoryId
        /// </summary>
        private static int _recordingStoryId = INVALID_VALUE;

        private static int _recordingBlockIndex = INVALID_VALUE;
        private static int _recordingLastBlockIndex = INVALID_VALUE;
        private static int _recordingLastChoiceIndex = INVALID_VALUE;

        public static bool IsRecordingStoryId => (_recordingStoryId != INVALID_VALUE);

        /// <summary>録画でパラメータ分岐があった場合、抽選結果のブロックが分岐元ブロックから何番目に見つかった分岐先にするか</summary>
        public static int RecordingParameterChoiceIndex = INVALID_VALUE;
        public static bool IsOverrideParameterChoiceForRecording => RecordingParameterChoiceIndex != INVALID_VALUE;

        /// <summary>録画でループ選択肢を選ぶか</summary>
        public static bool RecordingSelectLoopChoice = false;

        public class ChoiceTreeNode
        {
            public bool IsChildSelected; // この選択肢の先のBlockが完全に再生されているか
            public int PrevBlockIndex; // IsChildSelectedを更新する時に使う
            public int PrevChoiceIndex; // IsChildSelectedを更新する時に使う
            public int ChoiceCount = INVALID_VALUE; // 全ての選択肢を選んだかを調べるのに使う
            public Dictionary<int, ChoiceTreeNode> ChoiceDict; // 分岐先

            private bool IsSelectedIncludingChildren => IsChildSelected && HasNoChildOrSelected;
            private bool HasNoChildOrSelected => (ChoiceDict == null) || ChoiceDict.Values.All(n => n.IsSelectedIncludingChildren);

            /// <summary>
            /// 選択肢の先も含めたブロックが再生済みか
            /// </summary>
            /// <param name="choiceIndex"></param>
            /// <returns></returns>
            public bool IsChoiseSelected(int choiceIndex)
            {
                return IsChildSelected &&
                       ChoiceDict != null && ChoiceDict.TryGetValue(choiceIndex, out var childNode) &&
                       childNode.IsSelectedIncludingChildren;
            }

            /// <summary>
            /// このブロックの選択肢が全て再生済みか
            /// 連続録画の終了判定用
            /// </summary>
            /// <returns></returns>
            public bool IsAllChoiceSelected()
            {
                return IsChildSelected && ChoiceCount == (ChoiceDict?.Count ?? 0);
            }
        }

        /// <summary>
        /// StoryIdで全ての選択肢を選択済みか
        /// 最後に再生した時に未読の選択肢がなければTrue
        /// </summary>
        public static bool IsAllChoiceSelectedForRecording =>
            _choiceDict?.Values.All(node => node.IsAllChoiceSelected()) == true;

        /// <summary>
        /// 録画するStoryIdが変われば選択済み選択肢テーブルをクリア
        /// </summary>
        /// <param name="storyId"></param>
        /// <param name="forceChange"></param>
        public static void OnStartStoryRecording(int storyId, bool forceChange = false)
        {
            _recordingBlockIndex = INVALID_VALUE;
            _recordingLastBlockIndex = INVALID_VALUE;

            if (!forceChange && (storyId == _recordingStoryId))
            {
                return;
            }

            _recordingStoryId = storyId;
            _choiceDict = _choiceDict ?? new Dictionary<int, ChoiceTreeNode>();
            ClearChoiceDictForRecording();
        }

        /// <summary>
        /// 前回と同じストーリーを別の抽選結果・差分で再生する時に選択済みリストをクリアする
        /// </summary>
        public static void ClearChoiceDictForRecording()
        {
            _choiceDict?.Clear();
            ClearSelectedChoiceList();
        }

        /// <summary>
        /// 前回と同じストーリーを録画する時に選択履歴をクリアする
        /// </summary>
        public static void ClearSelectedChoiceList()
        {
            RecordingSelectedChoiceList.Clear();
        }

        public static void SetRecordingBlockIndex(int blockIndex)
        {
            _recordingBlockIndex = blockIndex;
        }

        /// <summary>
        /// 選択肢をすでに選んだことがあるか
        /// 録画用
        /// </summary>
        /// <param name="choiceIndex"></param>
        /// <returns></returns>
        public static bool IsRecordingChoiceSelected(int choiceIndex)
        {
            return _choiceDict.TryGetValue(_recordingBlockIndex, out var choiceSet) &&
                   choiceSet.IsChoiseSelected(choiceIndex);
        }

        /// <summary>
        /// 選択肢を選択済みとしてマーク
        /// </summary>
        /// <param name="choiceIndex"></param>
        /// <param name="choiceCount"></param>
        public static void SetRecordingChoiceSelected(int choiceIndex, int choiceCount)
        {
            if (!_choiceDict.TryGetValue(_recordingBlockIndex, out var choiceTreeNode))
            {
                choiceTreeNode = new ChoiceTreeNode
                {
                    ChoiceCount = choiceCount,
                    PrevBlockIndex = _recordingLastBlockIndex,
                    PrevChoiceIndex = _recordingLastChoiceIndex
                };
                _choiceDict.Add(_recordingBlockIndex, choiceTreeNode);
            }

            choiceTreeNode.IsChildSelected = true;
            choiceTreeNode.ChoiceDict = choiceTreeNode.ChoiceDict ?? new Dictionary<int, ChoiceTreeNode>();
            if (!choiceTreeNode.ChoiceDict.ContainsKey(choiceIndex))
            {
                choiceTreeNode.ChoiceDict.Add(choiceIndex, new ChoiceTreeNode { IsChildSelected = true });
            }

            // 前の選択肢のフラグを下ろす（全選択肢を選んでいれば下ろさない）
            if (choiceTreeNode.PrevBlockIndex != INVALID_VALUE &&
                _choiceDict.TryGetValue(choiceTreeNode.PrevBlockIndex, out var prevTreeNode))
            {
                prevTreeNode.ChoiceDict[choiceTreeNode.PrevChoiceIndex].IsChildSelected =
                    (((int)choiceTreeNode.ChoiceDict?.Count) >= choiceCount);
            }

            _recordingLastBlockIndex = _recordingBlockIndex;
            _recordingLastChoiceIndex = choiceIndex;
            RecordingSelectedChoiceList.Add(choiceIndex);
        }

        /// <summary>
        /// まだ選択していない選択肢番号を取得
        /// </summary>
        /// <returns></returns>
        public static int GetChoiceIndexForRecording(int choiceCount)
        {
            if (!IsRecordingStoryId)
            {
                return INVALID_VALUE;
            }

            var choiceIndex = 0;
            for (; (choiceIndex < choiceCount) && IsRecordingChoiceSelected(choiceIndex); choiceIndex++) { }
            return choiceIndex;
        }

#endif

        #endregion 録画関連

        #region 環境設定

        /// <summary>
        /// 最後に設定された背景の環境設定データ
        /// キャラ固有の設定にしたあとで、元に戻したくなったときに元の値がわからないと戻せないので記憶する
        /// </summary>
        private StoryEnvParam _envParamBg = null;

        public StoryEnvParam EnvParamBg => _envParamBg;

        /// <summary>
        /// 2枚目の背景の環境設定データ (クロスフェード中に使用する)
        /// </summary>
        private StoryEnvParam _nextEnvParamBg;

        /// <summary>
        /// 現在適用中の環境設定
        /// </summary>
        private StoryEnvParam _envParam = null;

        private bool _isBeforeReverseLightStatus = false;

        /// <summary>
        /// キャラクターTrack別の現在の環境設定
        /// </summary>
        private StoryEnvParam[] _envParamArray = new StoryEnvParam[StoryTimelineBlockData.CHARACTER_MAX];
        public StoryEnvParam[] EnvParamArray => _envParamArray;

        #endregion 環境設定

        #region 時間

        /// <summary>
        /// 経過フレーム数
        /// </summary>
        [SerializeField]
        private int _frameCount = 0;

        public int FrameCount
        {
            set
            {
                _frameCount = value;
                _currentTime = _frameCount * GameDefine.BASE_FPS_TIME;
            }
            get { return _frameCount; }
        }

        /// <summary>
        /// 待ち中の経過フレーム数
        /// </summary>
        [SerializeField]
        private int _waitingFrameCount = 0;

        public int WaitingFrameCount => _waitingFrameCount;

        /// <summary>
        /// 実際の時間
        /// </summary>
        [SerializeField]
        private float _currentTime = 0.0f;

        public float CurrentTime
        {
            get { return _currentTime; }
            set { _currentTime = value; }
        }

        /// <summary>
        /// 待ち中の時間（待ちの間加算されて、待ち状態で現在のFrameが動かなくても時間だけ進めたいときに使用）
        /// </summary>
        [SerializeField]
        private float _waitingCurrentTime = 0.0f;

        public float WaitingCurrentTime
        {
            get => _waitingCurrentTime;
            set
            {
                _waitingCurrentTime = value;
                _waitingFrameCount = Math.Round(_waitingCurrentTime * GameDefine.NORMAL_FRAME_RATE);
            }
        }

        /// <summary>
        /// 最後に経過したFrame数
        /// </summary>
        private int _lastFrameCount = INVALID_VALUE;

        public int LastFrameCount
        {
            set { _lastFrameCount = value; }
            get { return _lastFrameCount; }
        }

        /// <summary>
        /// 倍速中にUpdateするフレームの配列 (TextClipの終端と終了の2フレームだけUpdateする)
        /// </summary>
        private readonly int[] _highSpeedFrameCountArray = new int[2];

        /// <summary>
        /// 倍速中にUpdateするフレームのindex
        /// </summary>
        private int _highSpeedFrameCountIndex = 0;

        /// <summary>
        /// deltaTimeと1/30フレーム時間により増加する経過フレーム数、主にNeedsCheckForWaiting関数内で自動進行のチェック処理に使う
        /// </summary>
        private float _elapsedFrameCount = 0f;

        #endregion 時間

        #region タッチ制御

        /// <summary>
        /// ユーザのタッチ待ちをしているか (タッチ待ちに加えて選択肢決定待ちなどあらゆる待機状態を含む)
        /// </summary>
        public bool IsWaiting { get; private set; } = false;

        /// <summary>
        /// ユーザの入力待ちをしているか (タッチ以外の待機状態: 選択肢決定や名前入力など)
        /// </summary>
        private bool _isWaitingUserInput = false;

        /// <summary>
        /// ユーザの入力待ちを開始するフレームからBlock末尾までに隙間があるか
        /// </summary>
        private bool _hasClipEndSpace;

        /// <summary>
        /// ２択以上の選択肢が表示されるフレーム数: 連打で選択肢が飛ぶ問題の暫定対応
        /// </summary>
        public int ChoiceFrameCount { get; set; } = int.MaxValue;

        /// <summary>
        /// タッチ待ちによる一時停止が発生する位置 (次のテキストClipの１フレーム手前)
        /// </summary>
        private int _frameCountForWaiting = int.MaxValue;

        /// <summary>
        /// この時間まではタッチ禁止になるフレーム数
        /// </summary>
        private int _touchBlockFrameCount = INVALID_VALUE;

        /// <summary>
        /// CySpringの更新が必要かどうか
        /// </summary>
        private bool _isDirtyCySpring = false;

        /// <summary>
        /// 連打中かどうか
        /// </summary>
        private bool _isContinuousTouch = false;

        /// <summary>
        /// 連打中判定
        /// </summary>
        public bool IsContinuousTouch
        {
            get => _isContinuousTouch;
            set => _isContinuousTouch = value;
        }

        /// <summary>
        /// 連打中と判定されるフレーム数: この時間までにタッチが来たら連打状態
        /// </summary>
        private int _continuousTouchFrameCount = INVALID_VALUE;

        /// <summary>
        /// タッチ操作でBlock移動したかどうか
        /// </summary>
        /// <remarks>
        /// Blockの途中でタッチしてBlock移動 -> true
        /// Blockの末尾で待機中にタッチしてBlock移動 -> true
        /// </remarks>
        public bool IsMovedBlockByTouch { get; private set; } = false;

        /// <summary>
        /// 148952 タッチ操作でフレームスキップが発生するかどうか
        /// </summary>
        /// <remarks>
        /// Blockの途中でタッチしてBlock移動 -> true
        /// Blockの末尾で待機中にタッチしてBlock移動 -> false
        /// </remarks>
        public bool IsSkipFrameByTouch { get; private set; } = false;

        #endregion タッチ制御

        /// <summary>
        /// Timelineを再生中かどうか
        /// </summary>
        public bool IsPlaying { get; set; } = false;

        /// <summary>
        /// Timelineが終了したか
        /// </summary>
        public bool IsFinished { get; private set; } = false;

#if CYG_DEBUG && UNITY_EDITOR
        /// <summary>
        /// Timelineエディタで再生中かどうか
        /// </summary>
        public bool IsPlayingEditor { get; set; } = false;

        /// <summary>
        /// Timelineエディタで編集中か (通常プレイでもエディタプレイでもない, かつ, StoryTimelineEditorシーンにいる)
        /// </summary>
        public bool IsEditing => !IsPlaying && !IsPlayingEditor
                                            && SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.StoryTimelineEditor;
        /// <summary>
        /// TimeLineエディタ経由で編集を行う
        /// </summary>
        public float ElapsedFrameCountEditor
        {
            get
            {
                return _elapsedFrameCount;
            }
            set
            {
                _elapsedFrameCount = value;
            }
        }
#endif

        /// <summary>
        /// モーションを再生の直前にロードするか (デフォルトでは無効: つまり事前にロードする)
        /// </summary>
        public bool IsLoadMotionBeforePlaying { get; set; } = false;

        /// <summary>
        /// 体のモーション読み込み済みかチェック
        /// </summary>
        private bool _isLoadedBodyMotion = false;

        /// <summary>
        /// DataUpdateの処理を必ずやるか.
        /// </summary>
        private bool _isDisableDataUpdateCulling = false;

        /// <summary>
        /// 最初の1フレームは必ずupdateするフラグ.
        /// </summary>
        private bool _isFirstFrame = true;

        /// <summary>
        /// 文字列処理用
        /// 毎フレーム処理で文字列結合などを行いたい場合に使用する
        /// 何処で使われるか分からないので、次フレームで使いまわすような事をしてはいけない
        /// </summary>
        private System.Text.StringBuilder _stringBuilder = new System.Text.StringBuilder(WORK_STRINGBUILDER_BUFFER_SIZE);

        public System.Text.StringBuilder WorkStringBuilder => _stringBuilder;

        /// <summary>
        /// 128727 Propモーションと連動して、キャラモーションもFrameを使って更新をする指定ランタイムデータ
        /// </summary>
        private RunTimeStoryFrameUpdateCharaMotionData _runTimeStoryFrameUpdateCharaMotionData;
        public RunTimeStoryFrameUpdateCharaMotionData RunTimeStoryFrameUpdateCharaMotionData
        {
            get
            {
                // 128727 ランタイム用フレームで更新するためのCharaMotion指定データ
                // パドックで利用する際に「Initialize」が呼ばれないケースがある、
                // Runtimeデータに参照が発生する際に初期化を行う
                if (_runTimeStoryFrameUpdateCharaMotionData == null)
                {
                    _runTimeStoryFrameUpdateCharaMotionData = new RunTimeStoryFrameUpdateCharaMotionData();
                }
                return _runTimeStoryFrameUpdateCharaMotionData;
            }
        }
        
        /// <summary>
        /// 複数選択肢の時に選択肢をスキップするかどうか、trueならスキップする
        /// </summary>
        public bool IsSkipMultiChoice { get; set; } = false;
        
        #endregion 基本データ

        #region UI管理

        /// <summary>
        /// 現在の表示モード
        /// </summary>
        public static DisplayMode CurrentDisplayMode = DisplayMode.Portrait;

        /// <summary>
        /// 現在のPlayMode
        /// </summary>
        public static PlayMode CurrentPlayMode = PlayMode.Default;

        /// <summary>
        /// オート再生かどうか (デフォルトではtrueなので使うシーン側で制御する)
        /// </summary>
        public static bool IsAutoPlay = true;
        #endregion UI管理

        #region 背景

        /// <summary>
        /// 最後に再生した環境設定で使用する背景ID
        /// </summary>
        public int LastEnvBgId { get; set; } = INVALID_VALUE;

        /// <summary>
        /// 最後に再生した環境設定で使用する背景サブID
        /// </summary>
        public int LastEnvBgSubId { get; set; } = INVALID_VALUE;

        /// <summary>
        /// ３D背景を表示しているか
        /// </summary>
        public bool IsShow3dBg { get; set; } = false;

        /// <summary>
        /// ３D背景に紐づく影のタイプ
        /// </summary>
        public StoryTimelineBg3DClipData.ShadowType3d ShadowType3d { get; set; } = StoryTimelineBg3DClipData.ShadowType3d.None;

        /// <summary>
        /// 3D背景に紐づく丸影を表示しているか
        /// </summary>
        public bool IsShowCircleShadow => IsShow3dBg && ShadowType3d == StoryTimelineBg3DClipData.ShadowType3d.CircleShadow;

        #endregion 背景

        #region サウンド

        private readonly bool[] _isBgmStoppingArray = new bool[StoryTimelineBlockData.DSP_BGM_MAX];
        private bool _isSingleModeBgmPlaying = false;
        private float _bgmVolume = 1f;
        private float _envVolume = 1f;

        /// <summary>
        /// 「BGM_Timeline1」、「BGM_Timeline2」用のBusVolume値を保持する配列
        /// </summary>
        private readonly float[] _bgmDspBusVolumeArray = new float[StoryTimelineBlockData.DSP_BGM_MAX];

        /// <summary>
        /// 「BGM_Timeline1」、「BGM_Timeline2」用のBusVolume値の変更が2回目以降かのフラグを保持する変数
        /// </summary>
        private readonly bool[] _bgmDspBusVolumeChange2ndTimeArray = new bool[StoryTimelineBlockData.DSP_BGM_MAX];

        private AudioPlayback[] _bgmPlaybackArray = new AudioPlayback[StoryTimelineBlockData.DSP_BGM_MAX];
#if UNITY_EDITOR
        private readonly List<AudioPlayback> _bgmPlaybackList = new List<AudioPlayback>(32);
#endif // UNITY_EDITOR
        private AudioPlayback _envPlayback;
        private AudioPlayback _lastSePlayback;
        private readonly List<AudioPlayback> _sePlaybackList = new List<AudioPlayback>(32);
        /// <summary>Object制御EventStoryEffectの特殊SE用（雨エフェクト）</summary>
        private readonly List<AudioPlayback> _nonStopSePlaybackList = new List<AudioPlayback>(10);
        /// <summary>LoopSE用の最後に再生したSEのPlayback。リストなのはトラック数分確保するから。</summary>
        private readonly AudioPlayback[] _loopSePlaybackArray = new AudioPlayback[StoryTimelineBlockData.LOOP_SE_MAX];

        /// <summary>
        /// 背景環境音の再生を許可するか
        /// このフラグがfalseの時はReserveBgEnvSound関数で再生を予約する
        /// </summary>
        public bool IsEnvSeEnabled { get; private set; } = false;

        /// <summary>
        /// 背景環境音の再生を予約する際のCue名
        /// </summary>
        public string ReservedEnvCueName { get; set; } = string.Empty;

        /// <summary>
        /// 背景環境音の再生を予約する際のCueシート名
        /// </summary>
        public string ReservedEnvCueSheetName { get; set; } = string.Empty;

        /// <summary>
        /// BgmClipDataからBGM再生ができるか (倍速で育成会話を開始する場合は設定不可になる)
        /// </summary>
        public bool IsBgmClipEnabled { get; private set; } = true;

        /// <summary>
        /// 会話シーンが始まる時に育成BGMを停止するか
        /// </summary>
        public bool ShouldStopSingleModeBgm { get; private set; }

        /// <summary>
        /// 育成BGMを再生する場合の処理
        /// </summary>
        public System.Action PlaySingleModeBgm { get; set; } = null;

        /// <summary>
        /// BgmClipDataからの再生が可能になった時に本来再生するはずだったBGMを再生する
        /// </summary>
        public System.Action OnBgmEnabled { get; private set; } = null;

        /// <summary>
        /// 背景別のBusParamSet名
        /// </summary>
        public string LastBgBusParamSetName { get; set; } = "";

        /// <summary>
        /// 設定されているBusParamSetのリスト。最後にまとめて解除するため。
        /// </summary>
        private readonly List<string> _setBusParamSetNameList = new List<string>();

        /// <summary>
        /// 倍速中のBGMフェード時間 (2フレーム)
        /// </summary>
        public const float FADE_TIME_FOR_HIGH_SPEED = GameDefine.BASE_FPS_TIME * 2f;

        /// <summary>
        /// Bgmの再生開始位置を補正するか (倍速を解除した時にtrue)
        /// </summary>
        private bool _correctBgmStartTime = false;

        /// <summary>
        /// 最後にBGMを再生したフレーム
        /// </summary>
        private int _lastBgmFrameCount = 0;
        #endregion サウンド

        #region 各種コントローラーへの参照

        /// <summary>
        /// ワイプ用Controller
        /// </summary>
        public TimelineWipeController WipeController = null;

        /// <summary>
        /// 画面効果用Controller
        /// </summary>
        public TimelineScreenEffectController ScreenEffectController = null;

        /// <summary>
        /// イメージエフェクト
        /// </summary>
        public GallopImageEffect ImageEffectController = null;

        /// <summary>
        /// キャラクター用Controller
        /// </summary>
        public TimelineCharacterController CharacterController = null;

        /// <summary>
        /// 小物用Controller
        /// </summary>
        public TimelinePropController PropController = null;

        /// <summary>
        /// カメラ用Controller
        /// </summary>
        public TimelineCameraController CameraController = null;

        /// <summary>
        /// テキスト用Controller
        /// </summary>
        public TimelineTextController TextController = null;

        /// <summary>
        /// 背景用Controller
        /// </summary>
        public TimelineBackgroundController BackgroundController = null;

        /// <summary>
        /// 3D背景用Controller
        /// </summary>
        public TimelineBackground3DController Background3DController = null;

        /// <summary>
        /// スチル絵用Controller
        /// </summary>
        public TimelineStillController StillController = null;

        /// <summary>
        /// 育成カット再生用Controller
        /// </summary>
        public TimelineTrainingCuttController TrainingCuttController = null;

        public TimelineSingleImageController SingleImageController = null;

        public TimelineUIAnimationController UIAnimationController = null;

        public TimelineSoundController SoundController = null;
        
        /// <summary>
        /// 配信風フィルター用Controller
        /// </summary>
        public TimelineLiveStreamingController LiveStreamingController = null;
        
        #endregion 各種コントローラーへの参照

        // 画面タッチ時や再生終了時にシーン側で実行したい処理を設定する用
        #region 各種処理に対応するコールバック

        /// <summary>
        /// 再生終了時の処理
        /// </summary>
        public System.Action OnFinish = null;

        /// <summary>
        /// ブロックが切り替わったタイミングのコールバック
        /// </summary>
        public System.Action<StoryTimelineController, StoryTimelineBlockData> OnBlockChange = null;

        /// <summary>
        /// 揺れものアクシデント防止用コールバック
        /// </summary>
        public System.Action PreventCySpringAccident = null;

        /// <summary>
        /// 待ちタイミングで自動瞬きをONにする
        /// キー入力待ちなどのタイミングではnullが渡される
        /// </summary>
        public System.Action<EventTimelineModelController> OnAutoBlinkOn = null;

        /// <summary>
        /// 画面の向きが回転中か (使わない場合はnullのままで良い)
        /// </summary>
        public System.Func<bool> IsChangingOrientation = null;

        /// <summary>
        /// フッターボタンの状態更新
        /// 主に高速化スキップ中で利用、
        /// 選択肢表示中にショートカットボタン以外のものは操作可能にする
        /// それ以外の時全フッターボタンは操作不可
        /// </summary>
        public System.Action<bool> OnUpdateFooterStatus = null;

        /// <summary>
        /// 121469 育成イベント高速化スキップでプレイヤー応答の選択肢表示せず直接にログを追加
        /// </summary>
        public System.Action<StoryTimelineTextClipData> OnForceAddReplyChoiceLog = null;

#if CYG_DEBUG

        /// <summary>
        /// タイムライン更新後に呼び出されるコールバック
        /// </summary>
        public System.Action OnEndUpdateForDebug = null;

#if UNITY_EDITOR
        /// <summary>
        /// タイトル文言を表示する (使わない場合はnullのままで良い)
        /// </summary>
        public System.Action ShowTitleCaption = null;

        /// <summary>
        /// 持ち方変更時の処理 (使わない場合はnullのままで良い)
        /// </summary>
        public System.Action<DisplayMode> RequestChangeOrientation = null;

        /// <summary>
        /// タイムライン更新後に呼び出されるコールバック
        /// </summary>
        public System.Action OnEndUpdateForEditor = null;

        /// <summary>
        /// EditorWindowsでプレイボタンが押された時の処理
        /// </summary>
        public System.Action<int[], int[]> OnClickPlayButton = null;

        /// <summary>
        /// TimelineDataのリソースロードが終わった後の処理
        /// </summary>
        public System.Action<StoryTimelineData> OnLoadResources = null;

        public static bool IsProrogue()
        {
            return IsProrogue(CurrentDisplayMode);
        }

        public static bool IsProrogue(DisplayMode mode)
        {
            Debug.Log($"[Story]Test if prorogue or not. mode={mode}. Result is {mode == DisplayMode.SingleModePrologue}.");
            return mode == DisplayMode.SingleModePrologue;
        }
#endif //UNITY_EDITOR
#endif //CYG_DEBUG

        #endregion 各種処理に対応するコールバック

        #region 口パク用のデータの管理

        /// <summary>
        /// EventTimelineModelControllerが使うLipSyncDataのリスト
        /// </summary>
        public readonly Dictionary<string, List<LipSyncDataFile>> LipSyncDataFileDict = new Dictionary<string, List<LipSyncDataFile>>();

        /// <summary>
        /// LipSyncDataをロードする処理 (シーン毎に違うので実行したい処理を割り当ててから使う)
        /// </summary>
        public System.Action<string, StoryTimelineController> LoadLipSyncData = null;

        /// <summary>
        /// CueIDを指定してLipSyncDataを取り出す
        /// </summary>
        public LipSyncDataFile GetLipSyncDataFile(string sheetId, int cueId, int gender)
        {
            if (LipSyncDataFileDict.TryGetValue(sheetId, out var list))
            {
                var dataList = new List<LipSyncDataFile>();
                int count = list.Count;
                for (int i = 0; i < count; i++)
                {
                    var lipSyncDataFile = list[i];
                    if (lipSyncDataFile.dataId == cueId)
                    {
                        if (lipSyncDataFile.gender == gender)
                        {
                            return lipSyncDataFile;
                        }
                        dataList.Add(lipSyncDataFile);
                    }
                }
                if (dataList.Count <= 0)
                {
                    return null;
                }
                // データが1つしかない場合は性別に関わらず返す
                if (dataList.Count == 1)
                {
                    return dataList[0];
                }
                // 同じCueIDで複数ヒットしたが性別タイプが一致しない場合はGenderType.Noneのものを返す
                return dataList.Find(data => data.gender == (int)LipSyncDataFile.GenderType.None);
            }

            return null;
        }

        #endregion 口パク用のデータの管理

        #region テキスト差分

        /// <summary>
        /// テキスト差分を指定する用
        /// </summary>
        public int TextDifferenceFlag = 0;

        /// <summary>育成対象のキャラID</summary>
        public int TrainingCharaId = INVALID_VALUE;

        /// <summary>育成対象の衣装ID</summary>
        public int TrainingDressId = INVALID_VALUE;

        /// <summary>サポートキャラのID</summary>
        public int SupportCharaId = INVALID_VALUE;

        /// <summary>パラメータ差分の抽選結果ID</summary>
        public int ParameterSelectIndex = INVALID_VALUE;

#if CYG_DEBUG && UNITY_EDITOR

        /// <summary>
        /// 現在のテキストに設定された差分カテゴリ（実機では使わない）
        /// </summary>
        public int TextDifferenceCategoryFlag;

#endif

        #endregion テキスト差分

        #region AABBアップデートカリング
        /// <summary>
        /// AABBのサイズ、位置から生成されるAABB.
        /// </summary>
        private Bounds _updateAABB;

        /// <summary>
        /// フラスタム用プレーン領域、フラスタム専用なので6面固定.
        /// </summary>
        private Plane[] _frustumPlanes = new Plane[6];

        #endregion AABBアップデートカリング

        #region ループ選択肢の管理
        /// <summary>
        /// ループ選択肢を選んだ回数 (ループ選択肢のExit先ごとに回数を集計する)
        /// </summary>
        public Dictionary<int, int> LoopCountDict { get; } = new Dictionary<int, int>();

        #endregion ループ選択肢の管理

        #region ワープ

        /// <summary>
        /// 時間ワープしたフラグ
        /// 時間ワープした場合、TrackのUpdateで「最後に再生したClipの順番を覚えておいてそこから探すロジック」にて過去にさかのぼって探さないため見つからない不具合が出るので
        /// ワープした場合は全走査するためのフラグ
        /// </summary>
        private WarpStep _warpStepValue = 0;

        public WarpStep WarpStepValue
        {
            get { return _warpStepValue; }
        }

        /// <summary>
        /// ワープ先のFrame
        /// </summary>
        private int _reserveWarpFrameCpount = 0;

        #endregion ワープ

        #region 倍速設定

        //強制オート
        private bool _isForceAuto;
        public bool IsForceAuto => _isForceAuto;

        /// <summary> 倍速再生速度タイプ </summary>
        private static HighSpeedType _highSpeedType = HighSpeedType.None;
        public static System.Action<HighSpeedType, HighSpeedType> OnHighSpeedTypeChanged { get; set; }
        public static void SetHighSpeedType(HighSpeedType type)
        {
            var prevSpeedType = _highSpeedType;
            _highSpeedType = type;
            OnHighSpeedTypeChanged?.Invoke(prevSpeedType, _highSpeedType);
            if (_highSpeedType != HighSpeedType.HighSpeedX4)
            {
                _skipBlockCount = 0;
            }
        }

        /// <summary> 倍速再生をしているか </summary>
        public static bool IsHighSpeedMode(HighSpeedType type) => type > HighSpeedType.SpeedX1;

        public static bool IsHighSpeedMode() => _highSpeedType > HighSpeedType.SpeedX1;

        /// <summary>
        /// Timelineの再生倍率
        /// </summary>
        public static float TimeScale { get; set; } = StoryDefine.NORMAL_TIME_SCALE;

        /// <summary>
        /// 倍速設定時に利用する処理ブロックのカウント
        /// </summary>
        private static int _skipBlockCount;

        /// <summary>
        /// 強制オート切り替え
        /// </summary>
        /// <param name="enable"></param>
        public void EnableForceAuto(bool enable)
        {
            _isForceAuto = enable;
        }

#if CYG_DEBUG
        /// <summary>
        /// デバコマから編集するグランドライブ特別演出で利用するタイムスケール
        /// </summary>
        public static float DebugGrandLiveHighSpeedTimeScale = StoryDefine.HIGH_SPEED_TIME_SCALE_GRAND_LIVE;

        /// <summary>
        /// ダイレクトシーンなどのデバッグ機能で強制的に高速スキップを利用
        /// </summary>
        public static bool DebugIsForceUseSuperHighSpeedSkip = false;

        /// <summary>
        /// 140132 147255 強制的に無音で再生するか（リソースロード自体を無視する、現状はstory directで利用）
        /// </summary>
        public static bool DebugIsForcedMute = false;
        /// <summary>
        /// 147255 TimelineControllerへ参照が持っているものはこのプロパティを探すことで結合度合を少し下げる
        /// </summary>
        public bool IsForcedMute => DebugIsForcedMute;
#endif

        /// <summary>
        /// 特別イベント用ワイプ再生中のタイムスケール
        /// </summary>
        public static float TimeScaleEventWipe => StoryDefine.EVENT_WIPE_TIME_SCALE;

        /// <summary>
        /// 会話再生が終わってパラメータアップなどの演出を行っている時のタイムスケール
        /// </summary>
        /// <remarks>
        /// OnEndStory()が呼ばれた時にTimeScaleをこちらの値に書き換える.
        /// その関数が呼ばれた後はWaitingCurrentTimeがその倍率でもって更新される.
        /// 影響を受けるのは終了演出が終わるまでのモーション再生.
        /// </remarks>
        public static float TimeScaleAfterEndStory => StoryDefine.STORY_END_TIME_SCALE;

        /// <summary>
        /// タップ送りモード
        /// グランドライブ特別演出中必ずfalseになる
        /// </summary>
        /// <returns></returns>
        public static bool IsTapMode() => _highSpeedType == HighSpeedType.None && CurrentPlayMode == PlayMode.Default;

        /// <summary>
        /// 高速スキップを適用するためのフラグ、
        /// ストーリー再生中スキップボタンが「>>」以外になるとfalseになり、次のストーリー開始時Trueになります
        /// </summary>
        private bool _isApplySuperHighSpeedSkip = false;
        /// <summary>
        /// ストーリー再生中に高速化を適用させるかのフラグ設定
        /// </summary>
        /// <param name="isApply"></param>
        public void SetIsApplySuperHighSpeedSkip(bool isApply)
        {
            if (isApply)
            {
                //適用の場合、再生スタート時の高速化設定を見て、オフの場合適用できない
                isApply = IsSettingSuperHighSpeedSkip();
            }

            _isApplySuperHighSpeedSkip = isApply;

            SetFooterStatus();
        }

        /// <summary>
        /// ストーリー再生は高速スキップ中の状態である
        /// </summary>
        /// <returns></returns>
        public bool IsSuperHighSpeedSkipMode() => _isApplySuperHighSpeedSkip && IsSettingSuperHighSpeedSkip() && IsHighSpeedMode();

        /// <summary>
        /// オプションダイアログに高速スキップを適用するためのフラグ状態
        /// 育成イベント再生中に高速スキップ適応中カの状態を取得したい場合「IsSuperHighSpeedSkipMode」を利用してください
        /// </summary>
        private static bool IsSettingSuperHighSpeedSkip()
        {
#if CYG_DEBUG
            //デバッグ機能から強制的にハイスピードを適用になっている
            if (DebugIsForceUseSuperHighSpeedSkip)
            {
                return true;
            }
#endif
            if (SaveDataManager.HasInstance() == false)
            {
                Debug.LogError("SaveDataManagerが作成出来ていないため、高速化フラグの参照が正しくできていない");
                return false;
            }
            var saveLoader = SaveDataManager.Instance.SaveLoader;
            return saveLoader.IsEnableSuperHighSpeedSkip;
        }

        #endregion

        #endregion 変数

        #region 保存してプレビュー
#if CYG_DEBUG && UNITY_EDITOR
        public class PreviewData
        {
            public int DifferenceFlag;
            public int TrainingCharaId;
            public int TrainingDressId;
            public int SupportCharaId;
            public int[] SelectIndexArray;
            public int[] ReceiveItemIdArray;
            public int[] TargetRaceIdArray;
            public bool DisplayStoryBlockFlag;
        }

        public delegate void StartPreviewDelegate(PreviewData data);

        /// <summary>
        /// プレビューを開始する時の処理
        /// </summary>
        public StartPreviewDelegate StartPreview = null;

#endif
        #endregion 保存してプレビュー

        #region メソッド

        #region 初期化
        /// <summary>
        /// 各種変数を初期化
        /// </summary>
        public void Initialize()
        {
            if (_envParam == null)
            {
                _envParam = ScriptableObject.CreateInstance<StoryEnvParam>();
                _nextEnvParamBg = ScriptableObject.CreateInstance<StoryEnvParam>();
            }

            PlayedBlockIndexList.Clear();

            // フレームカウントを初期化
            FrameCount = 0;
            _lastFrameCount = INVALID_VALUE;

            // テキスト差分関連の変数を初期化
            TextDifferenceFlag = 0;
            TrainingCharaId = INVALID_VALUE;
            TrainingDressId = INVALID_VALUE;
            SupportCharaId = INVALID_VALUE;
            ParameterSelectIndex = INVALID_VALUE;

            // イベント関連の変数を初期化
            EventDressId = INVALID_EVENT_SETTING;
            EventBgId = INVALID_EVENT_SETTING;
            EventBgSubId = INVALID_EVENT_SETTING;
            EventWeatherBgSubId = INVALID_EVENT_SETTING;

            // 倍速再生・オート再生の変数を初期化
            TimeScale = StoryDefine.NORMAL_TIME_SCALE;
            IsAutoPlay = true;
            CurrentPlayMode = PlayMode.Default;
            _highSpeedType = HighSpeedType.None;
            _skipBlockCount = 0;

            // ループSE用の変数初期化
            InitializeAudioLoopSe();

            // エディタ向けのサウンドプロファイラーを初期化
            InitializeSoundProfiler();
        }

        public void Release()
        {
            _timelineData = null;

            PlaySingleModeBgm = null;
            OnBgmEnabled = null;
            OnFinish = null;
            OnBlockChange = null;
            PreventCySpringAccident = null;
            OnAutoBlinkOn = null;
            LoadLipSyncData = null;
            _envParam = null;
            OnHighSpeedTypeChanged = null;
            OnUpdateFooterStatus = null;
            OnForceAddReplyChoiceLog = null;

            // エディタ向けのサウンドプロファイラーの後始末
            ReleaseSoundProfiler();

            // 128727 フレームで更新するためのCharaMotionの指定データを解放
            _runTimeStoryFrameUpdateCharaMotionData?.Dispose();
            _runTimeStoryFrameUpdateCharaMotionData = null;

#if CYG_DEBUG && UNITY_EDITOR
            ShowTitleCaption = null;
            RequestChangeOrientation = null;
            IsChangingOrientation = null;
            OnEndUpdateForEditor = null;
            OnClickPlayButton = null;
            OnLoadResources = null;
            StartPreview = null;
#endif
        }
        #endregion 初期化

        #region ロード

        /// <summary>
        /// 必要なリソースのロード(全部込み)
        /// キャラの差し替えなどを行う場合は外でLoadCuttDataしてStoryTimelineDataを読み込んで、中身入れ替えてからこのロジックを呼ぶ
        /// </summary>
        public void LoadResources(string cuttDataPath = "")
        {
            // カットの読み込み
            _timelineData = LoadCuttData(cuttDataPath);

#if UNITY_EDITOR && CYG_DEBUG
            LoadResourcesForEditor(_timelineData);
#else
            var resourceList = LoadResourceList(_timelineData.StoryId);
            LoadResources(_timelineData, resourceList);
#endif
        }

        /// <summary>
        /// 必要なリソースのロード(StoryTimelineDataを入れる)
        /// </summary>
        public void LoadResources(StoryTimelineData timelineData, StoryTimelineResourceList resourceList)
        {
            // データ渡し
            _timelineData = timelineData;
            _timelineData.Initialize(this);

            // キャラの読み込み
            LoadAllCharacter();

            // 背景の読み込み
            LoadBg3D();

            // サウンドの読み込み
            LoadSound(resourceList);

            // ボディモーションの読み込み
            LoadBodyMotion();

            // カメラ制御タイプの確認
            CheckCameraType();

            // AABB形状を構築する.
            if (_timelineData != null && _timelineData.IsEnableAABBCull)
            {
                _updateAABB = new Bounds(_timelineData.AABBCenterPos, _timelineData.AABBSize);
            }
        }

#if UNITY_EDITOR && CYG_DEBUG
        public void LoadResourcesForEditor(StoryTimelineData timelineData)
        {
            if (timelineData == null)
            {
                return;
            }

            var tmpResourceList = GallopUtil.ObjectPool.Get(ScriptableObject.CreateInstance<StoryTimelineResourceList>);

            timelineData.SaveResources(tmpResourceList);
            LoadResources(timelineData, tmpResourceList);

            //#97610対応、一部リソースパスはCSV依存するので、CSV参照して用意した（モーションなど）リソースパスが保存済みのリソースアセットに登録されていない場合
            //警告を出す、相違がある場合実機上でダウンロード漏れが発生してしまう可能性があります
            if (int.TryParse(timelineData.StoryId, out var storyId))
            {
                var resourceListPath = ResourcePath.GetStoryTimelineResourceListPath(storyId);
                var resourceList = ResourceManager.LoadOnView<StoryTimelineResourceList>(resourceListPath);
                if (resourceList != null)
                {
                    var sb = new System.Text.StringBuilder();

                    foreach (var data in tmpResourceList.ResourcePathList)
                    {
                        //キャッシュに入れたリソースパスが保存されているリソースアセットに存在しない場合警告を表示
                        if (resourceList.ResourcePathIsExist(data.Path, data.TrackType) == false)
                        {
                            if (sb.Length == 0)
                            {
                                sb.AppendLine("本番リソースアセットと相違のあるパス:");
                            }
                            sb.AppendLine($"パス: {data.Path}; TrackType: {data.TrackType}");
                        }
                    }

                    //エラーが発生する場合、ダイアログを表示
                    if (sb.Length > 0)
                    {
                        UnityEditor.EditorUtility.DisplayDialog("警告", "パスに問題がありました\nConsoleのWarningログをご確認ください\n一括更新でパス更新を行ってください\n更新後CSVにも問題ない場合エンジニアに問題調査を依頼してください。", "OK");

                        Debug.LogWarning($"警告 {sb}");
                    }
                }
            }

            tmpResourceList.Clear();
            GallopUtil.ObjectPool.Release(tmpResourceList);
        }
#else
        private static StoryTimelineResourceList LoadResourceList(string storyIdString)
        {
            return int.TryParse(storyIdString, out var storyId) ? LoadResourceList(storyId) : null;
        }

        private static StoryTimelineResourceList LoadResourceList(int storyId)
        {
            return ResourceManager.LoadOnView<StoryTimelineResourceList>(
                ResourcePath.GetStoryTimelineResourceListPath(storyId));
        }
#endif

        /// <summary>
        /// 必要なリソースのロード(StoryTimelineDataを入れる)
        /// </summary>
        public void LoadResourcesBgOnly(StoryTimelineData timelineData)
        {
            // データ渡し
            _timelineData = timelineData;
            _timelineData.Initialize(this);

            // 背景の読み込み
            LoadBg3D();
        }

        #region カットのロード
        /// <summary>
        /// カットのロード
        /// </summary>
        private StoryTimelineData LoadCuttData(string cuttDataPath)
        {
            // パスがないのでスルー
            if (string.IsNullOrEmpty(cuttDataPath))
            {
                // ちなみにエラーではない可能性がある（ツールから起動した場合等）
                // すでにあるデーターを返す
                return _timelineData;
            }

            // ロード
            // View紐づけでロードした場合, Unloadはシステム側に任せます
            return ResourceManager.LoadOnView<StoryTimelineData>(cuttDataPath);
        }

        /// <summary>
        /// 外部からタイムラインデータを読み込む際のロジック
        /// ハッシュ設定で読み込んでいるので、いらなくなったタイミングでUnloadしてあげること
        /// </summary>
        /// <param name="cuttDataPath"></param>
        /// <param name="hash"></param>
        /// <returns></returns>
        public static StoryTimelineData LoadCuttDataOnHash(string cuttDataPath, ResourceManager.ResourceHash hash)
        {
            // StoryTimelineDataはインスタンス生成せずに使用するのがルールになります
            // 以下の問題が出ます
            // - 複数のControllerとTimelineDataを使うシチュエーションで同じデータを読み込んだ場合
            //   TimelineDataのインスタンスが違うがその先のBlockData、TrackDataのメモリが共有されているため
            //   再生されているものとは違うTimelineControllerを返す
            //   TimelineDataが参照するControllerと再生中Controllerが一致ないと正しくUpdateできない

            // ロード
            var data = ResourceManager.LoadOnHash<StoryTimelineData>(cuttDataPath, hash);

            // RegisterDownload中にSaveResourcesが呼ばれ, さらに内部でLoad処理が呼ばれるケースがある (3D背景Trackなど)
            // そのLoad時に同じHash値が使えるように覚えておく
            data.LoadedHash = hash;

            return data;
        }

        /// <summary>
        /// タイムラインデータを破棄する
        /// </summary>
        public static void UnloadCuttDataFromHash(string cuttDataPath, ResourceManager.ResourceHash hash)
        {
            // PaddockのようにUnloadAllTargetHashResourcesを使っている場合は呼び出し不要

            ResourceManager.UnloadFromHash(cuttDataPath, hash);
        }

        #endregion カットのロード

        #region キャラクターのロード

        /// <summary>
        /// カットに登場する全キャラクターのロード
        /// （カットロード時に行うことで、あらかじめ登場するモデルをキャッシュしておける）
        /// </summary>
        private void LoadAllCharacter()
        {
            // キャラのIDは準備ブロック(Index=0のブロック)内のTrackに仕込んである前提
            var trackList = _timelineData.GetCharacterHeadTrackList();
            int length = trackList.Count;
            for (int i = 0; i < length; i++)
            {
                var trackData = trackList[i];

                //キャラロードする前にゼッケンのデータを設定する
                trackData.ResetZekkenParameter();

                //#89117対応 ロード前にキャラクリップから頭ID上書きが必要かどうかを判断してIdを設定
                //キャラロード前に頭Idを設定
                trackData.SetupUsingCharaId();

                //トラック開始前にここが実行されてしまう
                LoadChara(trackData, isReload: false);

                // Trackごとのキャラの環境設定入れ物を用意
                if (_envParamArray[i] == null)
                {
                    _envParamArray[i] = ScriptableObject.CreateInstance<StoryEnvParam>();
                }
            }

            // シーン側で行う処理
            CharacterController.LoadAllCharacter?.Invoke();
        }

        /// <summary>
        /// キャラをロードする
        /// </summary>
        public void LoadChara(StoryTimelineCharaTrackData trackData, bool isReload, int overrideDressId = -1)
        {
            // この関数は必ずHeadTrackに対して処理する
            DebugUtils.Assert(trackData.BlockData.IsSettingBlock, "HeadTrackが指定されていない");

            if (isReload == false)
            {
                // リロードじゃないときは、すでにあるかチェックしてあるならここで終了
                if (CharacterController.GetModelControllerFromCache?.Invoke(trackData.GroupIndex, CharacterController, new TimelineCharacterController.LoadCondition(true)) != null)
                {
                    // すでに存在する場合はここで抜ける
                    return;
                }
            }

            // ModelCacheManagerにわたす情報を集める（cardIdは固定値で問題ない）
            const int cardId = 0;

            // 現在フレームから最後に影響するクリップの取得
            var existClip = trackData.TryGetClip<StoryTimelineCharaClipData>(_frameCount, out var curClip);

            int charaId = trackData.CharaId;
            int dressId = overrideDressId > 0 ? overrideDressId : trackData.DressId;
            int dressColorId = trackData.DressColorId;
            int headId = trackData.UsingHeadId;
            int zekkenNumber = trackData.UsingZekkenNumber;
            bool isWet = existClip && curClip.IsWet;
            bool isDirt = existClip && curClip.IsDirt;
            string zekkenName = trackData.UsingZekkenName;
            var fontStyle = trackData.UsingBoldFont ? FontStyle.Bold : FontStyle.Normal;
            var color = trackData.UsingZekkenColor;
            var fontColor = trackData.UsingZekkenFontColor;
            var suitColor = trackData.UsingTrackSuitColor;
            bool isMob = trackData.CharaType == StoryTimelineCharaTrackData.CharacterType.Mob;
            int mobId = trackData.MobId;
            bool isUseDressDataHeadModelSubId = trackData.IsUseDressDataHeadModelSubId;
            //92091 3D背景利用中なら円形シャドウ有効にする
            bool useCircleShadow = _timelineData != null && _timelineData.Use3DBg;
            var charaDressColorSetId = trackData.CharaDressColorSetId;

            var isOverrideDressRaceHistory = existClip && curClip.UsePastRace;
            var pastRaceId = existClip ? curClip.PastRaceId : 0;

            if (isMob == false)
            {
                // キャラIDチェック
                var charaElement = MasterDataManager.Instance.masterCharaData.Get(charaId);
                if (charaElement == null)
                {
                    return;
                }
            }
            else
            {
                // キャラIDチェック
                var mobElement = MasterDataManager.Instance.masterMobData.Get(mobId);
                if (mobElement == null)
                {
                    return;
                }
            }

            // 衣装情報チェック
            var dressElement = MasterDataManager.Instance.masterDressData.Get(dressId);
            if (dressElement == null)
            {
                return;
            }

            var info = new TimelineCharacterController.TimelineCharaBuildInfo
            {
                CharaId = charaId,
                CardId = cardId,
                DressId = dressId,
                DressColorId = dressColorId,
                HeadId = headId,
                IsWet = isWet,
                IsDirt = isDirt,
                MobId = mobId,
                ZekkenNumber = zekkenNumber,
                ZekkenName = zekkenName,
                FontStyle = fontStyle,
                Color = color,
                FontColor = fontColor,
                TrackIndex = trackData.GroupIndex,
                SuitColor = suitColor,
                DynamicCharaZekken = trackData.DynamicCharaZekken,
                IsUseDressDataHeadModelSubId = isUseDressDataHeadModelSubId,
                UseCircleShadow = useCircleShadow,
                CharaDressColorSetId = trackData.CharaDressColorSetId,
                IsOverrideDressRaceHistory = isOverrideDressRaceHistory,
                PastRaceId = pastRaceId
            };

            // #94410 ビルド情報をキャッシュしておく
            // レース前後の育成会話ではRaceInfoを使ってゼッケン情報が変更される場合がある
            // CharaTrackがそれを把握しておかないとReloadZekken処理によって再設定されてしまいRaceInfoによる変更が失われる
            trackData.CharaBuildInfo = info;

            // ロード処理
            CharacterController.LoadCharacter?.Invoke(info, isReload);

            //#89554 現在の背景のキャラのライティング、キャラの環境ファイルのライティング適用
            //#95740対応　エディタ上でストーリーを開く際にキャラの環境データが正しく設定されない問題の対応
            //charaTrackIndex == -1のケースは本番処理順でも起こり得るため、とりあえずこちらの対応はエディタ専用にしない
            if (trackData.CharacterTrackIndex == INVALID_VALUE)
            {
                trackData.UpdateCharacterTrackIndex();
            }

            UpdateEnvParamForCharacter(trackData.CharacterTrackIndex);
        }
        #endregion キャラクターのロード

        #region 育成キャラや背景の上書き処理
        /// <summary>
        /// キャラIDを育成中のキャラIDで上書きする
        /// </summary>
        public void OverrideClipData(bool isPlayingSingleMode)
        {
            var blockList = TimelineData?.BlockList;
            if (blockList == null)
            {
                return;
            }

            var charaTrackList = TimelineData?.GetCharacterHeadTrackList();
            if (charaTrackList == null)
            {
                return;
            }

            // Charaトラックを上書き
            int storyId = int.Parse(TimelineData.StoryId);
            OverrideCharaTrackData(charaTrackList, storyId, isPlayingSingleMode);

            // 全Wipeクリップを上書き
            OverrideWipeClipData(blockList);

            // 全Bgクリップを上書き
            OverrideBgClipData(blockList);
            OverrideBgClipDataByWeather(blockList);
        }

        /// <summary>
        /// CharaTrackDataのキャラIDを育成中のキャラIDで上書きする
        /// </summary>
        private void OverrideCharaTrackData(List<StoryTimelineCharaTrackData> charaTrackList, int storyId, bool isPlayingSingleMode)
        {
            int length = charaTrackList.Count;
            for (int i = 0; i < length; i++)
            {
                bool isOverride = false;
                var charaTrackData = charaTrackList[i];

                // フラグが立っていたら差し換え
                // 衣装替えで複数のTrackを使う場合があるので全て差し換える
                if (charaTrackData.IsTrainingOverride && MasterCharaData.IsCharaId(TrainingCharaId))
                {
                    isOverride = true;
                    charaTrackData.CharaId = TrainingCharaId;
                }

                if (IsTrainingDressOverride(charaTrackData))
                {
                    isOverride = true;

                    charaTrackData.DressId = TrainingDressId;

                    //121884 強制的に勝負服かSF着用のフラグを取得、trueの場合体操服変更オプションを無視
                    var isForceUseRaceDress = SingleModeUtils.IsForceUseRunDressStory(storyId);

                    // 体操服変更オプション処理
                    // 現在のレースをチェック
                    if (SingleModeUtils.IsOverrideTrackSuitStory(TempData.Instance.SingleModeData, storyId, isPlayingSingleMode) && isForceUseRaceDress == false)
                    {
                        // 体操服IDで上書き
                        charaTrackData.DressId = (int)ModelLoader.DressID.TrackSuit;
                    }

                    // ストーリーに過去の指定レースIDが設定されている時、体操服に変更するかどうかチェック
                    var raceHistoryInfo = SingleModeUtils.GetRaceHistoryInfo(StoryManager.Instance.PastRaceId);
                    if (raceHistoryInfo != null)
                    {
                        if (SingleModeUtils.IsOverrideTrackSuit(raceHistoryInfo))
                        {
                            // 体操服IDで上書き
                            charaTrackData.DressId = (int)ModelLoader.DressID.TrackSuit;
                        }
                    }

                    //121884 勝負服かSF着用、着せ替えの衣装があればそれを優先利用
                    //この設定は「IsEventDressOverride」より優先度が低い
                    if (isForceUseRaceDress)
                    {
                        var overrideDressId = (int)ModelLoader.DressID.SRCommon;
                        //育成中なら育成キャラ情報を参照
                        if (isPlayingSingleMode)
                        {
                            var workChara = WorkDataManager.Instance.SingleMode.Character;
                            overrideDressId = workChara.GetRaceDressId(true);
                        }
                        else
                        {
                            //なければそのまま「TrainingDressId」を利用、ギャラリーからの再生の場合勝負服かSFの考慮があって代入済み
                            //参照関数「GalleryData.GetTrainingCharaIdAndRaceDressId」
                            overrideDressId = TrainingDressId;
                        }
                        charaTrackData.DressId = overrideDressId;
                    }
                }

                if (charaTrackData.IsEventDressOverride && EventDressId > INVALID_EVENT_SETTING)
                {
                    isOverride = true;
                    charaTrackData.DressId = EventDressId;
                }

                if (charaTrackData.IsSupportCharaOverride && MasterCharaData.IsCharaId(SupportCharaId))
                {
                    isOverride = true;
                    charaTrackData.CharaId = SupportCharaId;
                }

                if (isOverride)
                {
                    // 固有衣装によっては特定のHeadIdを指定する必要があるため
                    // なんらか上書きされた時は頭部モデルを衣装に合わせて変えておく
                    OverrideCharaHeadId(charaTrackData);
                }

                // キャラIDの上書きが終わったらさらに小物IDの上書きチェックを行う
                if (charaTrackData.IsPropIdOverride)
                {
                    OverrideCharaPropId(charaTrackData);
                }
            }
        }

        /// <summary>
        /// 育成キャラの衣装上書きを実行するか判定する
        /// </summary>
        private bool IsTrainingDressOverride(StoryTimelineCharaTrackData charaTrackData)
        {
            if (!charaTrackData.IsTrainingDressOverride || TrainingDressId == INVALID_VALUE)
            {
                // TrackDataにフラグが設定されていない or 上書きする衣装が未指定 の場合は実行しない
                return false;
            }

            if (TrainingCharaId != INVALID_VALUE)
            {
                // #74357 育成キャラIDが指定されている場合はTrackDataのキャラIDと一致するかチェックする
                return charaTrackData.CharaId == TrainingCharaId;
            }

            return true;
        }

        /// <summary>
        /// DressIdに応じてHeadIdを上書きする
        /// </summary>
        /// <param name="charaTrackData"></param>
        private static void OverrideCharaHeadId(StoryTimelineCharaTrackData charaTrackData)
        {
            var dressData = MasterDataManager.Instance.masterDressData.Get(charaTrackData.DressId);
            if (dressData != null)
            {
                if (MasterCharaData.IsCharaId(dressData.CharaId) && dressData.CharaId != charaTrackData.CharaId)
                {
                    // 勝負服や私服などキャラ固有衣装の場合
                    // 同じBodyTypeのものに置き換えが必要
                    OverrideCharaDressId(charaTrackData, ref dressData);
                }

                charaTrackData.HeadId = dressData.HeadSubId;
                //#87706対応　ドレスによる頭Idの書き換えはキャラリロードをかけないで、既存の処理に任せる
                charaTrackData.UpdateUsingCharaHeadId(dressData.HeadSubId, true);
            }
        }

        /// <summary>
        /// 必要に応じて小物の上書き処理を行う
        /// </summary>
        private static void OverrideCharaPropId(StoryTimelineCharaTrackData charaTrackData)
        {
            var overrideData = PropIdOverrideData.Load();
            if (overrideData == null) return;

            PropIdOverrideController.Apply(overrideData, charaTrackData);
        }

        /// <summary>
        /// 固有衣装の場合に必要なDressIdの上書き処理
        /// </summary>
        private static void OverrideCharaDressId(StoryTimelineCharaTrackData charaTrackData, ref MasterDressData.DressData dressData)
        {
            int bodyType = dressData.BodyType;
            var list = MasterDataManager.Instance.masterDressData.GetListWithCharaIdOrderByIdAsc(charaTrackData.CharaId);
            int count = list.Count;
            for (int i = 0; i < count; i++)
            {
                if (list[i].BodyType == bodyType)
                {
                    // 上書き前のキャラに設定された固有衣装と同じタイプを取ってくる
                    dressData = list[i];
                    charaTrackData.DressId = dressData.Id;

                    return;
                }
            }
        }

        /// <summary>
        /// WipeClipDataのキャラIDを育成中のキャラIDで上書きする
        /// </summary>
        private void OverrideWipeClipData(List<StoryTimelineBlockData> list)
        {
            if (TrainingCharaId == INVALID_VALUE)
            {
                // キャラ未指定なら上書き処理はしない
                return;
            }

            int blockCount = list.Count;
            for (int i = 0; i < blockCount; i++)
            {
                var clipList = list[i].WipeTrack.ClipList;

                int clipCount = clipList.Count;
                for (int j = 0; j < clipCount; j++)
                {
                    var clip = clipList[j];
                    if (clip is StoryTimelineWipeClipData wipeClipData && wipeClipData.IsTrainingOverride)
                    {
                        // フラグが立っていたら差し換え
                        wipeClipData.SpineCharaId = TrainingCharaId;
                    }
                }
            }
        }

        /// <summary>
        /// BgClipDataのIdを育成イベント中のものに置き換える
        /// </summary>
        private static void OverrideBgClipData(List<StoryTimelineBlockData> list)
        {
            if (EventBgId <= INVALID_EVENT_SETTING)
            {
                return;
            }

            var bgData = MasterDataManager.Instance.masterBackgroundData.GetWithBgIdAndBgSub(EventBgId, EventBgSubId);
            if (bgData == null)
            {
                return;
            }

            int blockCount = list.Count;
            for (int i = 0; i < blockCount; i++)
            {
                var clipList = list[i].BgTrack.ClipList;

                int clipCount = clipList.Count;
                for (int j = 0; j < clipCount; j++)
                {
                    var clip = clipList[j];
                    if (clip is StoryTimelineBgClipData bgClipData && bgClipData.IsEventBgOverride)
                    {
                        // フラグが立っていたら差し換え
                        var infoList = bgClipData.BgInfoList;
                        if (infoList.Count > 0)
                        {
                            infoList[0].IsVerticalBg = false;
                            infoList[0].MainBgId = bgData.BgId;
                            infoList[0].SubBgId = bgData.BgSub;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// BgClipDataのサブIdを天候差分に応じて置き換える
        /// </summary>
        private void OverrideBgClipDataByWeather(List<StoryTimelineBlockData> list)
        {
            if (EventWeatherBgSubId <= INVALID_EVENT_SETTING)
            {
                // この会話イベントは天候差分の適用対象ではない
                return;
            }

            int blockCount = list.Count;
            for (int i = 0; i < blockCount; i++)
            {
                var clipList = list[i].BgTrack.ClipList;

                int clipCount = clipList.Count;
                for (int j = 0; j < clipCount; j++)
                {
                    if (!(clipList[j] is StoryTimelineBgClipData bgClipData))
                    {
                        continue;
                    }

                    if (!bgClipData.IsRaceWeatherOverride)
                    {
                        // #69309
                        // レース前後のシナリオでは回想シーンにも天候差分が適用されてしまうので
                        // フラグを導入してレースの天候を反映するかどうか選べるようにした
                        continue;
                    }

                    var infoList = bgClipData.BgInfoList;
                    if (infoList.Count == 0)
                    {
                        continue;
                    }

                    int bgId = infoList[0].MainBgId;
                    int subId = infoList[0].SubBgId;

                    var eventWeatherBgSubId = EventWeatherBgSubId;
                    //このブロックで明示的に過去の参照データが指定されていた時、
                    //過去のレースデータがある会話かどうかチェックしておく
                    //#137600
                    if (bgClipData.UsePastRace && bgClipData.WeatherPastRaceId > 0)
                    {
                        // 過去のRace情報があればそちらで置き換え
                        var raceHistoryInfo = SingleModeUtils.GetRaceHistoryInfo(bgClipData.WeatherPastRaceId);
                        if (raceHistoryInfo != null)
                        {
                            var overrideWeatherId = (int)GallopUtil.ToBgWeatherIdFromRaceWeather((RaceDefine.Weather)raceHistoryInfo.Weather);
                            // 有効な天候IDが指定されたらIDをそちらに差分IDを置き換え。
                            // 指定がない、見つからないであれば開始直後に決まった差分IDまま。
                            if (overrideWeatherId != (int)MasterBackgroundData.WeatherId.None)
                            {
                                eventWeatherBgSubId = overrideWeatherId;
                            }
                        }
                    }

                    // サブIdを分解しつつ天候差分を反映
                    int seasonId = subId / StoryTimelineBgClipData.SEASON_ID_DIGIT;
                    int weatherId = eventWeatherBgSubId;

                    subId %= StoryTimelineBgClipData.WEATHER_ID_DIGIT;
                    int timeId = subId / StoryTimelineBgClipData.TIME_ID_DIGIT;
                    int mobId = subId % StoryTimelineBgClipData.TIME_ID_DIGIT;

                    // サブIdに戻す
                    subId = (seasonId * StoryTimelineBgClipData.SEASON_ID_DIGIT)
                        + (weatherId * StoryTimelineBgClipData.WEATHER_ID_DIGIT)
                        + (timeId * StoryTimelineBgClipData.TIME_ID_DIGIT)
                        + mobId;

                    // マスターに存在すれば上書き
                    var bgData = MasterDataManager.Instance.masterBackgroundData.GetWithBgIdAndBgSub(bgId, subId);
                    if (bgData == null)
                    {
                        continue;
                    }
                    infoList[0].SubBgId = subId;
                }
            }
        }
        #endregion 育成キャラや背景の上書き処理

        #region 季節による衣装の上書き処理
        /// <summary>
        /// 季節に合わせて衣装を変更する (この関数はOverrideClipDataの後に呼び出すこと)
        /// </summary>
        public void OverrideDressIdBySeason(int bgSeasonSub)
        {
            var charaTrackList = TimelineData?.GetCharacterHeadTrackList();
            if (charaTrackList == null)
            {
                return;
            }

            int length = charaTrackList.Count;
            for (int i = 0; i < length; i++)
            {
                var track = charaTrackList[i];

                // 衣装変更フラグが立っていて有効な衣装IDだったら変更を行う
                // 回想シーンなどで衣装IDを固定したい場合はフラグが折れた状態になっている
                if (track is StoryTimelineCharaTrackData charaTrackData &&
                    charaTrackData.IsSeasonDressOverride &&
                    charaTrackData.DressId > INVALID_VALUE)
                {
                    int newDressId = MasterDataManager.Instance.masterDressData.ConvertClothIdBySeason(bgSeasonSub, charaTrackData.DressId);
                    if (newDressId != charaTrackData.DressId)
                    {
                        // 固有衣装によっては特定のHeadIdを指定する必要があるため
                        // なんらか上書きされた時は頭部モデルを衣装に合わせて変えておく
                        charaTrackData.DressId = newDressId;
                        OverrideCharaHeadId(charaTrackData);
                    }
                }
            }
        }
        #endregion 季節による衣装の上書き処理

        #region 水着キャラによる衣装の上書き処理
        /// <summary>
        /// 水着キャラの場合は衣装IDを固有衣装に上書きする
        /// </summary>
        public void OverrideDressIdBySwimsuit(int charaId, int dressId)
        {
            var charaTrackList = TimelineData?.GetCharacterHeadTrackList();
            if (charaTrackList == null)
            {
                return;
            }

            int length = charaTrackList.Count;
            for (int i = 0; i < length; i++)
            {
                var charaTrackData = charaTrackList[i];

                // 水着キャラが学校指定水着を着用していたら固有衣装に差し替える
                if (charaTrackData.CharaId == charaId &&
                    charaTrackData.IsSwimsuitTargetDress &&  // 衣装が換え対象か(=スクール水着か)
                    charaTrackData.IsSwimsuitOverride)       // #121379対応: 置き換えフラグが立っているか
                {
                    // 固有衣装によっては特定のHeadIdを指定する必要があるため
                    // なんらか上書きされた時は頭部モデルを衣装に合わせて変えておく
                    charaTrackData.DressId = dressId;
                    OverrideCharaHeadId(charaTrackData);
                }
            }
        }
        #endregion 水着キャラによる衣装の上書き処理

        #region 背景3Dのロード

        private void LoadBg3D()
        {
            // 先頭の背景Trackから背景を読み込む
            StoryTimelineBg3DTrackData trackData = GetBg3DTrackData();

            LoadBg3D(trackData, false);
        }

        public void LoadBg3D(StoryTimelineBg3DTrackData trackData, bool isReload)
        {
            if (Background3DController == null)
            {
                return;
            }

            Background3DController.ShowBackground?.Invoke(trackData);
        }

        /// <summary>
        /// 背景TrackDataを取得する
        /// 今のところ1タイムライン一個しかない想定
        /// </summary>
        /// <returns></returns>
        public StoryTimelineBg3DTrackData GetBg3DTrackData()
        {
            return _timelineData.BlockList[0].Bg3DTrack;
        }

        #endregion 背景3Dのロード

        #region サウンドのロード

#if UNITY_EDITOR
        /// <summary>
        /// SEのロード
        /// </summary>
        public void LoadSE(StoryTimelineSeClipDataBase clipData)
        {
            if (clipData.SheetName != null)
            {
                AddCueSheet(clipData.SheetName, AudioManager.SubFolder.Se);
            }
        }

        /// <summary>
        /// BGMのロード
        /// </summary>
        public void LoadBGM(StoryTimelineBgmClipDataBase clipData)
        {
            if (clipData.CueSheetName != null)
            {
                AddCueSheet(clipData.CueSheetName, AudioManager.SubFolder.Bgm);
            }
        }
#endif

        /// <summary>
        /// サウンドのロード
        /// </summary>
        public void LoadSound(StoryTimelineResourceList resourceList)
        {
#if CYG_DEBUG
            //ミュートになるため、ストーリー音声ファイルロードを強制的にスキップする
            if (IsForcedMute == false)
#endif
            {
                if (resourceList.CueSheetSeNameList != null)
                {
                    int length = resourceList.CueSheetSeNameList.Count;
                    for (int i = 0; i < length; i++)
                    {
                        AddCueSheet(resourceList.CueSheetSeNameList[i].Path, AudioManager.SubFolder.Se);
                        _loadedVoiceCueSheetNameList.Add(resourceList.CueSheetSeNameList[i].Path);
                    }
                }

                if (resourceList.CueSheetBgmNameList != null)
                {
                    int length = resourceList.CueSheetBgmNameList.Count;
                    for (int i = 0; i < length; i++)
                    {
                        AddCueSheet(resourceList.CueSheetBgmNameList[i].Path, AudioManager.SubFolder.Bgm);
                    }
                }
            }

            LoadVoice(resourceList);
        }

        /// <summary>
        /// キューシートをロード
        /// </summary>
        private static void AddCueSheet(string cueSheetName, AudioManager.SubFolder subFolder)
        {
            // 103733対応: スクリプターがTimelineエディタで作業している時はシート不足によってエラーが出るのを抑制する
            // 通常プレイ時や実機ではシートがなければエラーになる
            if (ExistsCueSheetForEditor(cueSheetName, subFolder))
            {
                AudioManager.Instance.AddCueSheet(cueSheetName, subFolder);
            }
        }

        /// <summary>
        /// キューシートが存在するかチェックする
        /// </summary>
        public static bool ExistsCueSheetForEditor(string cueSheetName, AudioManager.SubFolder subFolder)
        {
#if CYG_DEBUG && UNITY_EDITOR
            // Timelineエディタで作業しているかを判定する
            // エディタで編集中, エディタでプレイ中, 保存してプレビューとカバーしたい範囲が広いのでエディタを開いたかどうかで判定
            if (!UnityEditor.EditorWindow.HasOpenInstances<StoryTimelineEditorWindow>())
            {
                // エディタを開いていないならチェックしない
                return true;
            }

            // キューシートの存在チェックをする
            var acbPath = AudioManager.GetAcbFilePath(cueSheetName, subFolder);
            return AudioManager.IsExistAsset(acbPath);
#else
            // 実機ではチェックしない
            return true;
#endif
        }

        /// <summary>
        /// ボイスCueSheetとLipSyncデータのロード
        /// </summary>
        public void LoadVoice(StoryTimelineResourceList resourceList)
        {
#if CYG_DEBUG
            //140132 音声を強制的に再生しない時、ボイスファイルのリソースロードをスキップ
            //ただ口パクは残したいので、リップシンクデータのロードを保留
            if (IsForcedMute == false)
#endif
            {
                if (resourceList.CueSheetVoiceNameList != null)
                {
                    if (_timelineData.SceneType != StoryTimelineData.SupportedSceneType.Home)
                    {
                        // 前にロードしていたものは消しておく（ホームの場合、会話を同時再生するので破棄しない）
                        AudioManager.Instance.RemoveStoryVoiceCueSheet();
                    }

                    if (_timelineData.SceneType == StoryTimelineData.SupportedSceneType.Story ||
                        _timelineData.SceneType == StoryTimelineData.SupportedSceneType.Tutorial)
                    {
                        // ボイスがあるのが前提だが, 開発途中では１つもボイスが設定されていない場合があり得る
                        int count = resourceList.CueSheetVoiceNameList.Count;
                        if (count == 0)
                        {
                            // ボイスがないので抜ける (開発中はここに来る可能性がある)
                            return;
                        }
                    }

                    //130313 高速化スキップ中に音声ファイルのバインド数を確認
                    if (IsSuperHighSpeedSkipMode())
                    {
                        //現在バインド中の数
                        int curCount = 0;
                        //過去に最大同時にバインドした数
                        int maxCount = 0;
                        //バインド可能回数の上限値
                        int limitCount = 0;
                        CriWare.CriFs.GetNumBinds(out curCount, out maxCount, out limitCount);

                        //高速再生中にボイスの再生自体がなくなるはずで、上限を超えようとする場合、
                        //ロードをスキップさせる
                        var diffCount = limitCount - curCount;
                        if (resourceList.CueSheetVoiceNameList.Count > diffCount)
                        {
                            Debug.LogError("高速スキップ中に音声バインド上限に達したため、ボイスファイルのロードがスキップされました!このメッセージが表示されても進行に影響がない。");
                            return;
                        }
                    }

                    // CueSheetをロードする
                    // Listで指定されたCueSheetが全てロードできないとボイスが再生されない点に注意
                    AudioManager.Instance.AddVoiceStoryCueSheet(
                        resourceList.CueSheetVoiceNameList.Select(item => item.Path));
                    _loadedVoiceCueSheetNameList.AddRange(resourceList.CueSheetVoiceNameList.Select(item => item.Path));
                }
            }

            if (resourceList.VoiceCueSheetIdList != null)
            {
                int length = resourceList.VoiceCueSheetIdList.Count;
                for (int i = 0; i < length; i++)
                {
                    // ボイスに対応する口パクデータのロード
                    LoadLipSyncData?.Invoke(resourceList.VoiceCueSheetIdList[i].Path, this);
                }
            }
        }

        #endregion サウンドのロード

        #region モーションのロード

        /// <summary>
        /// ボディモーションで使うAnimationファイルのロード
        /// </summary>
        private void LoadBodyMotion()
        {
            if (_timelineData.SceneType != StoryTimelineData.SupportedSceneType.Story)
            {
                // ボディモーションの先行ロードは今のところ会話のみ
                return;
            }

            if (IsLoadMotionBeforePlaying)
            {
                // 再生の直前にロードする方式なら処理を抜ける
                // シングルモードの場合はここにくる
                return;
            }

            if (_isLoadedBodyMotion == true)
            {
                // 読み込み済みの場合は抜ける
                return;
            }

            // ゲーム本編で使うコードなので煩雑だがLinqは避ける

            int blockCount = _timelineData.BlockList.Count;
            for (int i = 0; i < blockCount; i++)
            {
                var block = _timelineData.BlockList[i];

                int charaCount = block.CharacterTrackList.Count;
                for (int j = 0; j < charaCount; j++)
                {
                    var charaTrack = block.CharacterTrackList[j];
                    int charaId = Mathf.Max(charaTrack.CharaId, charaTrack.MobId);
                    if (!MasterCharaData.IsCharaId(charaId))
                    {
                        continue;
                    }

                    // 全身モーション, 上半身モーション, 下半身モーションが対象
                    LoadBodyMotion(charaId, charaTrack.StoryTimelineCharaMotionTrackData);
                    LoadBodyMotion(charaId, charaTrack.StoryTimelineCharaUpperMotionTrackData);
                    LoadBodyMotion(charaId, charaTrack.StoryTimelineCharaLowerMotionTrackData);
                }
            }

            // 読み込み済み
            _isLoadedBodyMotion = true;
        }

        /// <summary>
        /// ボディモーションで使うAnimationファイルのロード
        /// </summary>
        private static void LoadBodyMotion(int charaId, StoryTimelineTrackData track)
        {
            int clipCount = track.ClipList.Count;
            for (int i = 0; i < clipCount; i++)
            {
                if (track.ClipList[i] is StoryTimelineMotionClipDataBase clip)
                {
                    LoadBodyMotion(charaId, clip);
                }
            }
        }

        /// <summary>
        /// ボディモーションで使うAnimationファイルのロード
        /// </summary>
        private static void LoadBodyMotion(int charaId, StoryTimelineMotionClipDataBase clip)
        {
            string motionPath = StoryMotionInfo.GetMotionPathStoryTimeline(charaId, 0, clip);
            if (string.IsNullOrEmpty(motionPath))
            {
                return;
            }

            ResourceManager.LoadOnScene<AnimationClip>(motionPath);
        }

        #endregion モーションのロード

        /// <summary>
        /// 簡易カメラを使っているかチェック
        /// </summary>
        public void CheckCameraType()
        {
            // SelectCamera(簡易カメラ)を使っているか
            bool isSelectCameraUsed = _timelineData.BlockList
                .SelectMany(b => b.CameraTrack.ClipList)
                .Cast<StoryTimelineCameraClipData>()
                .Any(c => c.PresetType == StoryTimelineCameraClipData.CameraPresetType.Select);

            var headDofTrack = _timelineData.BlockList[0].ImageEffectTrack.StoryTimelineImageEffectDofTrackData;
            // 簡易カメラと併用しない、かつ、
            headDofTrack.Enable = headDofTrack.UseWithSimpleCamera
                ? true                     // 簡易カメラと併用する時は常にDof有効
                : !isSelectCameraUsed;     // そうじゃない場合は簡易カメラが有効などDof無効
        }

        /// <summary>
        /// 現在の差分を考慮してTextClipに選択肢抽選結果IDを設定する
        /// </summary>
        public void SetTextDifferenceSelectIndex()
        {
#if CYG_DEBUG && UNITY_EDITOR
            // 一度フラグを全て落とす
            TextDifferenceCategoryFlag = 0;
#endif

            // どちらの分岐に進むのかはSelectIndexを使って決める
            //  1. 差分を考慮した上で各TextClipのSelectIndexを設定する
            //  2. 選択肢毎にSelectIndexを設定する (サーバ側のcsvで指定)
            //  3. ユーザが選択肢を選ぶとSelectIndexが確定する
            //  4. ParameterSelectionに到達するとそのSelectIndexを持つParameterBranchに進む
            // この関数は上記の1.を担う

            // TextDiffereceFlagは事前に設定しておくこと
            uint controllerFlag = (uint)TextDifferenceFlag;

            // 育成のcsvには1始まりのindexが設定されているので初期値は1
            int selectIndex = FIRST_SELECT_INDEX;

            var blockList = _timelineData.BlockList;
            int length = blockList.Count;
            for (int i = 0; i < length; i++)
            {
                var clipList = blockList[i].TextTrack.ClipList;
                if (clipList.FirstOrDefault() is StoryTimelineTextClipData clip)
                {
#if CYG_DEBUG && UNITY_EDITOR
                    if (clip.HasTextDifference())
                    {
                        // clipに設定されたカテゴリについてフラグを立てる
                        TextDifferenceCategoryFlag |= clip.DifferenceCategoryFlag;
                    }
#endif

                    // ParameterBranchが設定されており, それ以外のフラグも一致するTextClipを探す
                    if (FlagUtil.HasFlag(clip.DifferenceFlag, PARAMETER_BRANCH) &&
                        FlagUtil.HasAllFlag(controllerFlag, (uint)clip.DifferenceFlag))
                    {
                        // 1始まりの連番になるようにする
                        clip.SelectIndex = selectIndex;
                        selectIndex++;
                    }
                    else
                    {
                        // 必ずリセットする (TimelineエディタでTextDifferenceFlagを変えた時に前回の設定が残るため)
                        clip.SelectIndex = INVALID_VALUE;
                    }
                }
            }
        }

        #endregion ロード

        #region 再生/停止

        /// <summary>
        /// 再生
        /// </summary>
        public void Play()
        {
            IsWaiting = false;
            _isWaitingUserInput = false;
            _hasClipEndSpace = false;
            ChoiceFrameCount = int.MaxValue;
            _frameCountForWaiting = int.MaxValue;
            _touchBlockFrameCount = INVALID_VALUE;
            _isDirtyCySpring = false;
            _isContinuousTouch = false;
            _continuousTouchFrameCount = INVALID_VALUE;
            _prevBlockData = null;

            // サウンド関連
            for (var i = 0; i < StoryTimelineBlockData.DSP_BGM_MAX; i++)
            {
                _isBgmStoppingArray[i] = false;
            }
            _isSingleModeBgmPlaying = false;
            StartSoundProfiler();

            // ループ選択肢の管理
            LoopCountDict.Clear();

            // ライト有効化
            DirectionalLightManager.Instance.SetEnable(true);

            IsFinished = false;
            _timelineData.Play();

            // プレイ済みBlockのIndex更新
            // エディタ上でプレイした時は必ずしもBlock0とは限らないので今のBlockを取りだすようにする
            StoryTimelineBlockData blockData = _timelineData.GetCurrentBlockData(FrameCount);
            if (blockData != null)
            {
                int blockIndex = blockData.BlockIndex;
#if CYG_DEBUG && UNITY_EDITOR
                if (IsPlayingEditor)
                {
                    // 前のBlockに戻ってPlayしている場合があるので不要な要素を消しておく
                    // 開始地点より先のBlockを消さないと過去Clipの取得でプレイ済みListを遡る時におかしくなる
                    PlayedBlockIndexList.RemoveAll(i => i > blockIndex);
                }
#endif

                if (!PlayedBlockIndexList.Contains(blockIndex))
                {
                    PlayedBlockIndexList.Add(blockIndex);
                }

                // 倍速中にUpdateするフレームを設定
                SetHighSpeedFrameCount(blockData.TextTrack, FrameCount);
            }

            #region 録画用処理

#if CYG_DEBUG && UNITY_EDITOR
            // 録画用のStoryIdをセット
            if (_timelineData.SceneType == StoryTimelineData.SupportedSceneType.Story &&
                int.TryParse(_timelineData.StoryId, out var storyId))
            {
                OnStartStoryRecording(storyId);
            }
#endif

            #endregion
        }

        /// <summary>
        /// ブレンド再生
        /// </summary>
        /// <param name="prevBlockData"></param>
        public void Play(StoryTimelineBlockData prevBlockData)
        {
            Play();
            _prevBlockData = prevBlockData;
        }

        /// <summary>
        /// 停止
        /// </summary>
        public void Stop()
        {
            StopWaiting();
            StopSoundProfiler();
        }

        /// <summary>
        /// ワープ
        /// </summary>
        /// <param name="warpPointName"></param>
        public void Warp(string warpPointName)
        {
            if (_timelineData == null)
            {
                return;
            }

            StoryTimelineWarpClipData warpClipData = null;
            int blockCount = _timelineData.BlockList.Count;
            for (int i = 0; i < blockCount; i++)
            {
                int clipCount = _timelineData.BlockList[i].WarpTrack.ClipList.Count;
                for (int j = 0; j < clipCount; j++)
                {
                    StoryTimelineWarpClipData clipData = _timelineData.BlockList[i].WarpTrack.ClipList[j] as StoryTimelineWarpClipData;
                    if (clipData.PointName.Equals(warpPointName))
                    {
                        warpClipData = clipData;
                        break;
                    }
                }
            }

            if (warpClipData == null)
            {
                // 存在しないときも無視
                return;
            }

            Warp(warpClipData.FixedStartFrame);
        }

        /// <summary>
        /// ワープ
        /// </summary>
        /// <param name="warpFrameCount">ワープ先フレーム番号</param>
        public void Warp(int warpFrameCount)
        {
            _reserveWarpFrameCpount = warpFrameCount;

            // ワープした場合はフレームがさかのぼっている可能性があるのでそのチェックのためのフラグを立てる
            _warpStepValue = WarpStep.Reserve;
        }

        #endregion

        #region アップデート処理

        /// <summary>
        /// タイムラインのUpdate
        /// </summary>
        public void AlterUpdate(float deltaTime, Camera currentCamera = null)
        {
            if (TimelineData == null)
            {
                return;
            }

            //コントローラーが違うがデータを共有して扱えるようにするために毎回設定する
            TimelineData.TimelineController = this;

            // ワープ処理
            CheckWarp();

            // 再生倍率を反映
            deltaTime *= TimeScale;

            // 時間の更新
            UpdateTime(deltaTime);

            // 連打フラグの更新
            UpdateIsContinuousTouch();

            // タイムラインの中身の更新
            UpdateTimelineData(currentCamera);

            // コントローラーの更新
            UpdateController(_frameCount);

#if CYG_DEBUG
            OnEndUpdateForDebug?.Invoke();
#if UNITY_EDITOR
            OnEndUpdateForEditor?.Invoke();
#endif
#endif

            // タッチ操作によるBlock移動のフラグを落とす
            IsMovedBlockByTouch = false;

            // 148952 タップ操作によるフレームスキップフラグを落とす
            IsSkipFrameByTouch = false;

            _isFirstFrame = false;
        }
        /// <summary>
        /// タイムラインのUpdate(カメラ複数時の拡張対応)
        /// </summary>
        public void AlterUpdate(float deltaTime, Camera[] currentCamera)
        {
            //コントローラーが違うがデータを共有して扱えるようにするために毎回設定する
            TimelineData.TimelineController = this;

            // ワープ処理
            CheckWarp();

            // 再生倍率を反映
            deltaTime *= TimeScale;

            // 時間の更新
            UpdateTime(deltaTime);

            // 連打フラグの更新
            UpdateIsContinuousTouch();

            // タイムラインの中身の更新
            UpdateTimelineData(currentCamera);

            // コントローラーの更新
            UpdateController(_frameCount);

#if CYG_DEBUG
            OnEndUpdateForDebug?.Invoke();
#if UNITY_EDITOR
            OnEndUpdateForEditor?.Invoke();
#endif
#endif

            // タッチ操作によるBlock移動のフラグを落とす
            IsMovedBlockByTouch = false;

            _isFirstFrame = false;
        }


        /// <summary>
        /// ワープ処理のチェック
        /// </summary>
        private void CheckWarp()
        {
            switch (_warpStepValue)
            {
                case WarpStep.Reserve:
                    // 予約していたので処理する
                    FrameCount = _reserveWarpFrameCpount;
                    _warpStepValue = WarpStep.Exec;
                    break;

                case WarpStep.Exec:
                    // 処理が終わったので設定を戻す
                    _warpStepValue = WarpStep.None;
                    break;
            }
        }

        /// <summary>
        /// 会話シーンの初期化中に準備ブロックをUpdateする用
        /// </summary>
        public void AlterUpdateForSetup(int frameCount)
        {
            FrameCount = frameCount;

            // 初期化時なのでUpdateはちゃんとかかるようにカメラはNULL.
            UpdateTimelineData();
        }

        /// <summary>
        /// 会話終了からLoading画面に入るまでのUpdate
        /// </summary>
        public void AlterUpdateViewBeforeLoadingIn(float deltaTime)
        {
            // 再生倍率を反映
            deltaTime *= TimeScale;

            // パラメータアップ演出が終わるのを待っている間に
            // 待機モーションが固まらないようにする
            WaitingCurrentTime += deltaTime;

            UpdateTimelineData();
        }

        /// <summary>
        /// 時間の経過
        /// </summary>
        /// <remarks>
        /// この関数に指定されるdeltaTimeはTimeScale適用済み
        /// </remarks>
        private void UpdateTime(float deltaTime)
        {
            // 倍速再生するとフレーム数が飛び飛びになるので
            // タッチ待ちが必要かどうかは次のフレーム数を使って判定する
            GetNextFrameCount(deltaTime, out float nextTime, out int nextFrame);

            DebugLogFlush();
            DebugLogAppendLine("<color=lime>[StoryTimelineController]</color>:");

            // IsWaitingフラグが立ってからAuto再生に切り替える場合に対応できるようにこのifブロックの条件にはIsWaitingを含めない
            // 関数内のコメントも参照のこと
            if (NeedsCheckForWaiting(nextFrame))
            {
                // 次のブロックに進むのか入力待ちをするのか判定
                // 必要な場合は対応するフラグが立つ
                CheckGotoNextAndWaitInput(out bool gotoNext, out bool waitInput, out var textClipData);
                DebugLogAppendLine($"GotoNext = {gotoNext}, WaitInput = {waitInput}");

                if (gotoNext && textClipData != null)
                {
                    GoNextBlock(textClipData, nextTime, nextFrame);
                    return;
                }

                // 入力待ちを始める
                WaitInput(nextFrame, waitInput, textClipData);
            }
            else
            {
                // ブロックの末端フレーム
                var blockData = _timelineData.GetCurrentBlockData(_frameCount);
                int endFrame = blockData?.EndFrame ?? _timelineData.Length;

                // fix #53465
                // 処理落ちで、nextFrame より大きな値に飛んだ場合
                // 次フレームがストーリーの終端だった場合、正しく終了処理が行われないので補正しなければならない
                if (IsStoryEndFrameOrGrandLiveWaitFrameSkipped(this, blockData, nextFrame, endFrame))
                {
                    nextFrame = endFrame;
                    nextTime = nextFrame * GameDefine.BASE_FPS_TIME;
                }

                // 以下の条件を全て満たす場合、モーションのスキップを行う
                //
                // 1. 倍速中
                // 2. 非タッチ待ち状態
                // 3. 現在のフレームが再生中のブロックの末端に到達していない
                // 4. ＃122200　高速化スキップ中でないこと(SkipMotionFrameはスキップ処理で呼ばれ済み、2重の呼び出しでCySpringが暴れてしまうケースが発生)
                bool needsToSkipMotionFrame = IsHighSpeedMode() && !IsWaiting && _frameCount < endFrame && IsSuperHighSpeedSkipMode() == false;
                // #94404対応
                // 4. グランドライブ演出の場合、ライブ開始ボタン表示され、クリック待ち中でなければモーションスキップを行う
                if (needsToSkipMotionFrame && CurrentPlayMode == PlayMode.GrandLive)
                {
                    needsToSkipMotionFrame = _isWaitingUserInput == false;
                }

                // 以下の条件をすべて満たす場合、タッチ待ち状態に遷移する
                //
                // 1. StoryTimeline が再生完了している
                // 2. 非タッチ待ち状態
                // 3. 現在のフレームがブロックの末端に到達している
                bool needsToStartWaitingForTouch = IsFinished && !IsWaiting && _frameCount >= endFrame;

                // #92076 育成会話Live編対応
                // 次の条件を満たした場合も待ち状態に遷移する
                //
                // 1. 待ち状態ではない
                // 2. ユーザ入力待ちになっている
                // 3. 意図的にTextClipとBlock末尾との間に隙間が設定されている
                // 4. 現在のフレームがブロックの末端に到達している
                needsToStartWaitingForTouch = needsToStartWaitingForTouch
                                              || (!IsWaiting && _isWaitingUserInput && _hasClipEndSpace && _frameCount >= endFrame);

                // 以下の条件をすべて満たす場合、タッチ待ち状態を解除し、Autoによって次ブロックに進ませる
                //
                // 1. IsWaiting状態である
                // 2. Auto状態である
                // 3. _frameCountが_frameCountForWaiting待ちであること
                //    (_frameCountForWaitingはブロック末尾の1フレーム前が指定されます。)
                // 4. 62860 選択肢等の入力待ち(_isWaitingUserInput)でないとき
                bool isAutoEnable = _highSpeedType == HighSpeedType.SpeedX1 || CurrentPlayMode == PlayMode.GrandLive;
                bool isWaitForFrameCountForWaiting = _frameCount == _frameCountForWaiting - 1;
                bool needsToEndWaitingForTouch = IsWaiting && isAutoEnable && isWaitForFrameCountForWaiting && (!_isWaitingUserInput);

                DebugLogAppendLine($"_highSpeedType = {_highSpeedType}, IsWaiting = {IsWaiting}, IsFinished = {IsFinished}, _frameCount = {_frameCount}, _frameCountForWaiting = {_frameCountForWaiting}, endFrame={endFrame}");
                DebugLogAppendLine($"needsToSkipMotionFrame = {needsToSkipMotionFrame}, needsToStartWaitingForTouch = {needsToStartWaitingForTouch}, needsToEndWaitingForTouch = {needsToEndWaitingForTouch}");

                if (needsToSkipMotionFrame)
                {
                    DebugLogAppendLine($"SkipMotionFrame({nextFrame});");

                    //倍速だけで連続して待つと選択肢待ちなどで
                    //倍速有効時に揺れものが固まる可能性があるので、IsWaitingで選択肢待ちかチェックする
                    //キー入力待ちなどで設定がスキップされる事がある
                    SkipMotionFrame(nextFrame);
                }
                else if (needsToStartWaitingForTouch)
                {
                    DebugLogAppendLine("StartWaitingForTouch();");

                    // 最後フレーム以降はStoryTimeline以外の演出が重なる場合があるので待機状態にする
                    // 待機状態にしないとモーションの更新が止まってキャラが固まってしまう
                    StartWaitingForTouch();
                }
                else if (needsToEndWaitingForTouch)
                {
                    DebugLogAppendLine("StopWaiting();");

                    // 73004
                    // 高速でSkip切り替えを行う際に稀にここが通ってしまう、GetNextFrameCount関数内に対応
                    // 61656
                    // Auto下の回転によってWaitInputで立てたIsWaitingがframeCountForWaitingによって
                    // IsWaitingが折られない状態になるため、条件が整った場合に強制的に折る
                    // ※ここでおらなくてもdeltaTimeが大き目の値(0.04位)と取ることによって偶にGotoBlockで折られることがある。
                    StopWaiting();
                }
            }
            DebugLogPrint();

            if (IsWaiting)
            {
                WaitingCurrentTime += deltaTime;

                // タッチ待ちなので止める
                return;
            }

            // フレーム数を更新する
            // FrameCountプロパティを使うと_currentTimeも更新されるので直接_frameCountを設定
            _currentTime = nextTime;
            _frameCount = nextFrame;

            // 最大フレーム数チェック
            if (_frameCount > _timelineData.Length)
            {
                FrameCount = _timelineData.Length;

                OnEndStory();
            }
        }

        /// <summary>
        /// フッターボタンの状態を設定
        /// 現状は高速スキップ時の処理が入っている
        /// </summary>
        private void SetFooterStatus()
        {
            var isEnableFromStory = true;

            if (IsSuperHighSpeedSkipMode() && _isWaitingUserInput == false)
            {
                isEnableFromStory = false;
            }

            OnUpdateFooterStatus?.Invoke(isEnableFromStory);
        }

        /// <summary>
        /// 現在のTextClipDataを見て次のブロックに進む処理
        /// </summary>
        private void GoNextBlock(StoryTimelineTextClipData textClipData, float nextTime, int nextFrame)
        {
            // 倍速設定時なら専用処理が走る
            if (IsHighSpeedMode())
            {
                // 以下の条件を満たす場合、明示的にTimelineのUpdateを呼ぶ
                //
                //issue:34220対応
                //テキストのクリップ実行フレームよりも前で倍速モードになった
                //end - 1でwaitFrameするとき、テキストのクリップが実行されないので明示的に実行するフラグを立てる
                if (_frameCount < textClipData.FixedStartFrame && _frameCountForWaiting == _highSpeedFrameCountArray[FRAME_COUNT_INDEX_1ST])
                {
                    _currentTime = nextTime;
                    _frameCount = nextFrame;
                    //次のブロックにGotoする前にend - 1でTimeline更新
                    UpdateTimelineDataSelf();
                }
                GotoBlockForSkip(textClipData.NextBlockIndex, weakenCySpring: false);
            }
            else
            {
                // 次のブロックに進む
                GotoBlock(textClipData.NextBlockIndex, weakenCySpring: false);
            }
        }

        /// <summary>
        /// ブロックのカウントを増やす
        /// </summary>
        private void IncrementBlockCount(StoryTimelineBlockData blockData)
        {
            if (_highSpeedType == HighSpeedType.HighSpeedX4 && IsCheckIncrementBlockCount(blockData))
            {
                _skipBlockCount++;
            }
        }

        /// <summary>
        /// ブロックカウントを+してよいか
        /// </summary>
        /// <returns></returns>
        public static bool IsCheckIncrementBlockCount(StoryTimelineBlockData blockData)
        {
            //ブロックデータが無ければ処理しない
            if (blockData == null)
            {
                return false;
            }

            /// Blockのインデックスが0,Endブロックではなく、選択肢の直後ではない。通常セリフのブロックであればインクリメントしてよい
            return blockData.BlockIndex != 0 && !IsImportantBlock(blockData);
        }

        /// <summary>
        /// ブロックを処理するかどうか
        /// </summary>
        /// <returns></returns>
        public static bool IsProcessBlock(int blockCount, StoryTimelineBlockData blockData)
        {
            //ブロックデータが無ければ処理しない
            if (blockData == null)
            {
                return false;
            }

            //準備ブロック or Endブロック、重要ブロックなら必ず処理
            if (IsEndBlock(blockData) || blockData.IsSettingBlock || IsImportantBlock(blockData))
            {
                return true;
            }

            var textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            if (textClipData != null)
            {
                //終了ブロックなら必ず処理
                if (textClipData.NextBlockIndex == StoryTimelineTextClipData.END_STORY)
                {
                    return true;
                }
                //ブロックカウント3つめじゃなければ処理する
                if (blockCount % 3 != 2)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 重要ブロックかどうか
        /// </summary>
        /// <returns></returns>
        private static bool IsImportantBlock(StoryTimelineBlockData blockData)
        {
            return HasTrainingCuttClipBlock(blockData) ||
                   HasSingleModeTagBlock(blockData) ||
                   HasChoiceBlock(blockData) ||
                   HasCharacterClipBlock(blockData) ||
                   HasBgClipBlock(blockData) ||
                   HasPropClipBlock(blockData) ||
                   HasImageEffectClipBlock(blockData) ||
                   HasScreenEffectClipBlock(blockData) ||
                   HasBGMClipBlock(blockData) ||
                   HasSpecialWipeBlock(blockData);
        }

        /// <summary>
        /// 育成のカット割り込みがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasTrainingCuttClipBlock(StoryTimelineBlockData blockData)
        {
            //カットのクリップがあってブロックのインデックスが一致するなら重要ブロック判定
            var cuttClipList = blockData.TrainingCuttTrack.ClipList;
            //カットのクリップがあるなら重要ブロック判定
            if (cuttClipList.Count > 0)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 重要なタグがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasSingleModeTagBlock(StoryTimelineBlockData blockData)
        {
            var textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            if (textClipData != null)
            {
                return TextUtil.HasSingleModeImportantTag(textClipData.Text);
            }
            return false;
        }

        /// <summary>
        /// キャラの表示非表示があるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasCharacterClipBlock(StoryTimelineBlockData blockData)
        {
            var charaTrackList = blockData.CharacterTrackList;
            for (int i = 0; i < charaTrackList.Count; i++)
            {
                //キャラのクリップがあるなら重要ブロック判定
                if (charaTrackList[i].ClipList.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 背景の表示非表示があるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasBgClipBlock(StoryTimelineBlockData blockData)
        {
            //背景のクリップがあるなら重要ブロック判定
            return blockData.BgTrack.ClipList.Count > 0;
        }

        /// <summary>
        /// ImageEffectのクリップがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasImageEffectClipBlock(StoryTimelineBlockData blockData)
        {
            //ImageEffectのクリップがあるなら重要ブロック判定
            return blockData.ImageEffectTrack.ClipList.Count > 0;
        }

        /// <summary>
        /// 画面効果クリップがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasScreenEffectClipBlock(StoryTimelineBlockData blockData)
        {
            bool hasScreenEffectClip = false;
            //画面効果のクリップがあるなら重要ブロック判定
            for (int i = 0; i < blockData.ScreenEffectTrackList.Count; i++)
            {
                if (blockData.ScreenEffectTrackList[i].ClipList.Count > 0)
                {
                    return true;
                }
            }
            return hasScreenEffectClip;
        }

        /// <summary>
        /// BGMクリップがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasBGMClipBlock(StoryTimelineBlockData blockData)
        {
            //BGMのクリップがあるなら即return
            bool hasBGMClip = blockData.BgmTrack.ClipList.Count > 0;
            if (hasBGMClip)
            {
                return true;
            }
            //DSPチェック
            for (int i = 0; i < blockData.DspBgmTrackList.Count; i++)
            {
                if (blockData.DspBgmTrackList[i].ClipList.Count > 0)
                {
                    return true;
                }
            }
            //ボリューム
            bool hasBGMVolumeClip = blockData.BgmVolumeTrack.ClipList.Count > 0;
            if (hasBGMVolumeClip)
            {
                return true;
            }
            //DSPボリュームチェック
            for (int i = 0; i < blockData.DspBgmVolumeTrackList.Count; i++)
            {
                if (blockData.DspBgmVolumeTrackList[i].ClipList.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 小物表示クリップがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasPropClipBlock(StoryTimelineBlockData blockData)
        {
            //小物1
            var charaTrackList = blockData.CharacterTrackList;
            for (int i = 0; i < charaTrackList.Count; i++)
            {
                //小物1
                var propClipList = charaTrackList[i].StoryTimelineCharaPropTrackData.ClipList;
                //小物1のクリップがあるなら重要ブロック判定
                if (propClipList.Count > 0)
                {
                    return true;
                }
                //小物2
                var propClipList2 = charaTrackList[i].StoryTimelineCharaProp2TrackData.ClipList;
                //小物2のクリップがあるなら重要ブロック判定
                if (propClipList2.Count > 0)
                {
                    return true;
                }
                //小物3
                var propClipList3 = charaTrackList[i].StoryTimelineCharaProp3TrackData.ClipList;
                //小物3のクリップがあるなら重要ブロック判定
                if (propClipList3.Count > 0)
                {
                    return true;
                }
                //小物4
                var propClipList4 = charaTrackList[i].StoryTimelineCharaProp4TrackData.ClipList;
                //小物4のクリップがあるなら重要ブロック判定
                if (propClipList4.Count > 0)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 選択肢ががあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasChoiceBlock(StoryTimelineBlockData blockData)
        {
            var textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            if (textClipData != null)
            {
                return textClipData.HasChoice;
            }
            return false;
        }

        /// <summary>
        /// 特殊ワイプクリップがあるか
        /// </summary>
        /// <param name="blockData"></param>
        /// <returns></returns>
        private static bool HasSpecialWipeBlock(StoryTimelineBlockData blockData)
        {
            var wipeTrack = blockData.WipeTrack;
            //Wipeの存在チェック
            bool hasWipeClip = wipeTrack.ClipList.Count > 0;
            if (hasWipeClip)
            {
                //ワイプの種類チェック
                for (int i = 0; i < wipeTrack.ClipList.Count; i++)
                {
                    var wipeClip = wipeTrack.ClipList[i] as StoryTimelineWipeClipData;
                    if (wipeClip != null)
                    {
                        if (wipeClip.Type == StoryTimelineWipeClipData.WipeType.EventFlash || wipeClip.Type == StoryTimelineWipeClipData.WipeType.TargetRace)
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 終了ブロックかどうか(次があるか)
        /// </summary>
        /// <returns></returns>
        public static bool IsEndBlock(StoryTimelineBlockData blockData)
        {
            //中身チェック
            if (blockData == null || blockData.TextTrack.ClipList.Count <= 0)
            {
                return false;
            }

            var textClip = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            //Endチェック
            if (textClip != null && textClip.NextBlockIndex == StoryTimelineTextClipData.END_STORY)
            {
                return true;
            }

            return false;
        }

        private static bool IsStoryEndFrameOrGrandLiveWaitFrameSkipped(StoryTimelineController timelineController, StoryTimelineBlockData blockData, int nextFrame, int endFrame)
        {
            if (blockData == null) return false;
            if (blockData.TextTrack == null) return false;
            if (blockData.TextTrack.ClipList.Count <= 0) return false;

            var textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            if (textClipData == null) return false;

            //#94660対応　ライブ円陣演出でライブ開始ボタン表示してからプレイヤーがボタン押されるまで待つ必要があるので、
            //フレーム落ちによりフレームが待ちすべきフレームから突破してしまうケースを防ぐ
            if (CurrentPlayMode == PlayMode.GrandLive)
            {
                if (timelineController != null && (timelineController._hasClipEndSpace || timelineController._isWaitingUserInput))
                {
                    return nextFrame > endFrame;
                }
            }

            return textClipData.NextBlockIndex == StoryTimelineTextClipData.END_STORY && nextFrame > endFrame;
        }

        #region 次のフレームを取得する処理
        /// <summary>
        /// 次のフレームを取得
        /// </summary>
        private void GetNextFrameCount(float deltaTime, out float nextTime, out int nextFrame)
        {
            // 倍速中は専用の処理となる
            // ただし, グランドライブ特別演出・イベントワイプ中・育成カット再生中は通常フローに進む
            if (CurrentPlayMode != PlayMode.GrandLive &&
                TimeScale > TimeScaleEventWipe &&
                TrainingCuttController?.IsPlayingTrainingCutt == false)
            {
                GetNextFrameCount_HighSpeed(out nextTime, out nextFrame);
                UpdateElapsedFrameCount(deltaTime, nextFrame, isHighSpeed: true);
                return;
            }

            // 通常は経過した時間を加算して算出
            nextTime = _currentTime + deltaTime;
            nextFrame = Math.Round(nextTime * GameDefine.NORMAL_FRAME_RATE);
            UpdateElapsedFrameCount(deltaTime, nextFrame, isHighSpeed: false);
        }

        /// <summary>
        /// 次のフレームを取得 (倍速用のフロー)
        /// </summary>
        private void GetNextFrameCount_HighSpeed(out float nextTime, out int nextFrame)
        {
            //73004
            //高速でskip設定を切り替えの際に、稀に再生途中UpdateTime関数でStopWaiting関数が呼ばれてしまい、_frameCountForWaitingがint.MaxValueになってしまうため、
            //倍速状態で次のフレームカウンターを取得する際にチェックを入れる、_frameCountForWaitingがMaxValueのままではNeedsCheckForWaitingでfalseになってしまう
            //オートで遷できなくなるのを避けるため、再生中のブロックデータの終了フレームの１フレーム手前のフレーム数を入れ直してあげる
            //次のBlockDataへ遷移し始めるタイミングでまた_frameCountForWaitingがBlockDataのデータを代入する
            if (_frameCountForWaiting == int.MaxValue)
            {
                _frameCountForWaiting = _highSpeedFrameCountArray[FRAME_COUNT_INDEX_1ST];
            }

            // 倍速は決まったフレームを更新
            // TextClipの中央と終了の2フレームだけUpdateする
            nextFrame = _highSpeedFrameCountArray[_highSpeedFrameCountIndex];
            nextTime = nextFrame * GameDefine.BASE_FPS_TIME;

            // 倍速用フレームのindexを更新
            _highSpeedFrameCountIndex = Mathf.Min(++_highSpeedFrameCountIndex, FRAME_COUNT_INDEX_END);
        }

        /// <summary>
        /// 経過フレーム数を更新
        /// </summary>
        private void UpdateElapsedFrameCount(float deltaTime, int nextFrame, bool isHighSpeed)
        {
            if (isHighSpeed)
            {
                // 倍速の場合はnextFrameをそのまま代入
                _elapsedFrameCount = nextFrame;
                return;
            }

            if (IsWaiting)
            {
                // #73004 次のフレームでもしIsWaitingがTrueになり、_elapsedFrameCountが_frameCountForWaitingより小さくなってしまうと
                // 次のBlockへ遷移出来なくなりますので、_elapsedFrameCountに_frameCountForWaitingを代入しておく
                // #87435対応 _elapsedFrameCountの型をInt型に揃える
                if (Mathf.FloorToInt(_elapsedFrameCount) < _frameCountForWaiting && _frameCountForWaiting != int.MaxValue)
                {
                    _elapsedFrameCount = _frameCountForWaiting;
                }
                return;
            }

            if (IsTapMode())
            {
                // #79321 _elapsedFrameCountはオートと倍速時だけ利用するため、オートや倍速解除の場合、nextFrameをそのまま代入
                // オートと倍速が再度Onになる場合のスタート値が同じであることを保証
                _elapsedFrameCount = nextFrame;
            }
            else
            {
                // #73004 Math.Roundの誤差を避けるため、単純にdeltaTimeから経過フレーム数を計算、最低でも1を経過させる、nextFrameに値が小さくなる誤差が発生すると通るべきチェックが通らなくなる
                // 誤差というのはnextFrameの計算で同じnextTimeを使って前後２フレームで得た結果が違い、さらに２フレーム以降は数値が小さくなる現象
                // #87435対応 Mathf.Maxの第２引数は現在適用中のFPSと通常ベースFPSの倍数から設定する、ダイアログ表示で60FPSになる際に、ここが通常より倍以上のフレーム数が進行してしまうため
                // 誤差が大きく生じてしまいます。よって進行フレーム数はゲーム適用FPSによって決める
                _elapsedFrameCount += Mathf.Max(Mathf.Floor(deltaTime / GameDefine.BASE_FPS_TIME), GameDefine.NORMAL_FRAME_RATE / (float)Application.targetFrameRate);
            }
        }
        #endregion 次のフレームを取得する処理

        /// <summary>
        /// タッチ待ちのチェックが必要かどうか
        /// </summary>
        private bool NeedsCheckForWaiting(int nextFrame)
        {
            // IsWaitingフラグが立ってからAuto再生に切り替える場合に対応できるように
            // この関数で行う条件判定にはIsWaitingを含めない
            // 選択肢決定や名前入力待ちには_isWaitingUserInputが用意されている

            if (IsFinished)
            {
                // 終了状態ならチェック不要
                return false;
            }

            if (nextFrame < _frameCountForWaiting)
            {
                if (IsTapMode())
                {
                    // タッチ待ちの時間になっていない
                    return false;
                }

                //73004 自動再生の場合
                //nextFrameが誤差により小さくなってしまい、_frameCountForWaitingのチェックが通ってしまう場合があるので、ここで２回目のチェックを入れる
                //#87435対応 _elapsedFrameCountの型をInt型に揃える
                if (Mathf.FloorToInt(_elapsedFrameCount) < _frameCountForWaiting)
                {
                    // 経過フレームがタッチ待ちの時間になっていない
                    return false;
                }
            }

            // 選択肢決定などの入力待ちはは別フラグでチェック
            if (_isWaitingUserInput)
            {
                // ユーザの入力待ちになっている
                return false;
            }

            // チェック必要
            DebugLogAppendLine($"NeedsCheckForWaiting is true: _frameCount = {_frameCount}");
            return true;
        }

        /// <summary>
        /// 入力待ちが必要な時に実行する処理
        /// </summary>
        private void WaitInput(int nextFrame, bool waitInput, StoryTimelineTextClipData textClipData)
        {
            if (waitInput && textClipData != null)
            {
                if (IsHighSpeedMode())
                {
                    // 倍速再生の時に入力待ちが来たら暴れ抑制を行う
                    SkipMotionFrame(textClipData.ChoiceFrameCount);
                }

                if (!IsTapMode())
                {
                    // グランドライブ演出の場合デフォルト状態はオートになる
                    // オート再生以上ならユーザの入力待ちを開始する
                    // タッチ待ちさせるのでここではreturnしない
                    DebugLogAppendLine("StartWaitInput");
                    textClipData.StartWaitInput();
                }

                // フレーム数を選択肢表示位置に合わせる
                SetFrameCountToChoiceFrame(textClipData);
            }

            // 育成カットクリップはブロックをまたいで配置されるが、その間はタッチ待ちを行わない
            // GotoBlockよりも先に評価すると選択肢分岐を考慮しなくなるのでここで評価する
            var block = _timelineData.GetCurrentBlockData(nextFrame);
            var ignoreWaitingForTrainingCutt = (true == block?.TrainingCuttTrack.IsAutoPlay(nextFrame));

            // タッチ待ちを始めていなければここで開始する
            if (!IsWaiting && !ignoreWaitingForTrainingCutt)
            {
                // IsWaitingフラグが立っている時に画面タップすればタッチ待ちの解除が行われる
                // 待機中はPastタイプのClipは処理されないがAlwaysタイプは処理される点に注意
                StartWaitingForTouch();
            }
        }

        /// <summary>
        /// フレーム数を選択肢表示位置に合わせる
        /// </summary>
        private void SetFrameCountToChoiceFrame(StoryTimelineTextClipData textClipData)
        {
            if (_hasClipEndSpace)
            {
                // #92076 育成会話Live編対応
                // Liveボタンの表示タイミングを早めるためにTextClipとBlock末尾との間に隙間が設定されている
                // この場合は何もしない
                return;
            }

            // フレーム数を選択肢表示位置に合わせる
            // ここでは_currentTimeも同じにして問題ないのでプロパティ使う
            FrameCount = textClipData.ChoiceFrameCount;
            DebugLogAppendLine($"SetFrameCountToChoiceFrame: FrameCount = {FrameCount}");
        }

        /// <summary>
        /// 末尾まで再生した時の処理 (Skipを実行した場合も呼ばれる)
        /// </summary>
        public void OnEndStory()
        {
#if CYG_DEBUG
            //ストーリー再生後デバッグ用フラグをリセット
            DebugIsForceUseSuperHighSpeedSkip = false;

#if UNITY_EDITOR
            // StoryTimelineEditorでは終了処理を行わない
            if (SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.StoryTimelineEditor)
            {
                return;
            }
#endif
#endif

            // 破棄は1回だけ
            // View/SceneとStoryTimelineが同時に破棄されない場合の対策
            if (IsFinished)
            {
                return;
            }

            IsFinished = true;

            // 末尾まで再生した時はボイスを止める
            AudioManager.Instance.StopVoiceAll();
            // refs #96666 途中スキップなどでエフェクトが掛かったままになってしまうことがあるのでストーリータイムライン終了時にエフェクトをリセットする
            AudioManager.Instance.PlaySe(AudioId.SFX_RESET_VOICE_EFFECT);
            // 再生倍率を更新する
            TimeScale = TimeScaleAfterEndStory;


            var alwaysNeedSheetNameList = MasterDataManager.Instance.masterAudioCuesheet
                .GetListWithAttribute(CUESHEET_ATTRIBUTE_ALWAYS)
                .Select(x => x.CueSheet);

            // 常にロードしておくシートは削除しない
            _loadedVoiceCueSheetNameList = _loadedVoiceCueSheetNameList.Except(alwaysNeedSheetNameList).ToList();

            // エフェクトリセット用のシートも除外する
            if (AudioDefine.AUDIO_ID_DATA_DIC.TryGetValue(AudioId.SFX_RESET_VOICE_EFFECT, out var audioData) &&
                _loadedVoiceCueSheetNameList.Contains(audioData._cueSheet))
            {
                _loadedVoiceCueSheetNameList.Remove(audioData._cueSheet);
            }
            // このストーリーのボイスのキューシートをRemoveする
            foreach (var sheet in _loadedVoiceCueSheetNameList)
            {
                AudioManager.RemoveCueSheet(sheet);
            }
            _loadedVoiceCueSheetNameList.Clear();

            // TimeScaleが戻り倍速再生の場合CySpringが暴れるため、WarmUpを行う
            if (IsHighSpeedMode())
            {
                var charaTrackList = _timelineData.GetCharacterHeadTrackList();
                int count = charaTrackList.Count;
                for (int i = 0; i < count; i++)
                {
                    var modelController = charaTrackList[i].GetModelControllerFromCache();
                    if (modelController == null)
                        continue;
                    modelController.ReserveWarmingUpCySpring();
                }
            }

            // シーン側の終了処理を実行
            OnFinish?.Invoke();

            //ストーリー終了時再生モードをリセット
            //タイムラインコントローラーのPlayModeをDefaultに戻す
            CurrentPlayMode = StoryTimelineController.PlayMode.Default;
        }

        /// <summary>
        /// タイムラインの更新処理(確認ステップ)
        /// </summary>
        private void UpdateTimelineData(Camera currentCam = null)
        {
            // タイムラインデータが存在するか確認.
            if (_timelineData == null)
            {
                return;
            }

            // AABBタイムラインスキップありの場合はフラスタム内包を確認しないとアップデートしない
            if (_timelineData.IsEnableAABBCull && currentCam != null && !_isDisableDataUpdateCulling && !_isFirstFrame)
            {
                GeometryUtility.CalculateFrustumPlanes(currentCam, _frustumPlanes);
                if (!GeometryUtility.TestPlanesAABB(_frustumPlanes, _updateAABB))
                {
                    // 入っていなければそのまま抜ける.
                    return;
                }
            }

            UpdateTimelineDataSelf();
        }

        /// <summary>
        /// タイムラインの更新処理(複数カメラ時確認ステップ)
        /// </summary>
        private void UpdateTimelineData(Camera[] currentCam)
        {
            // タイムラインデータが存在するか確認.
            if (_timelineData == null)
            {
                return;
            }

            // AABBタイムラインスキップありの場合はフラスタム内包を確認しないとアップデートしない
            if (_timelineData.IsEnableAABBCull && currentCam != null && !_isDisableDataUpdateCulling && !_isFirstFrame)
            {
                for (int i = 0; i < currentCam.Length; ++i)
                {
                    GeometryUtility.CalculateFrustumPlanes(currentCam[i], _frustumPlanes);
                    if (GeometryUtility.TestPlanesAABB(_frustumPlanes, _updateAABB))
                    {
                        // 複数フラスタムの内一つでも入っている場合、実際のタイムラインデータアップデート処理.
                        UpdateTimelineDataSelf();
                        return;
                    }
                }
            }
            else
            {
                UpdateTimelineDataSelf();
            }
        }

        /// <summary>
        /// タイムラインの更新 (FrameCountの更新が終わった後に呼ぶ)
        /// </summary>
        private void UpdateTimelineDataSelf()
        {
            // タイムラインデータが存在するか確認(単体で何かで使われると怖いためここでも確認).
            if (_timelineData == null)
            {
                return;
            }

            // タイムラインの更新
            if (_timelineData.UpdateTimelineData(_frameCount, _currentTime, IsWaiting))
            {
                // Updateが走った場合のみ（Editorなどで一時的に何もない状態を避ける為）

                // 立ち位置に変化が出たら実行する
                CharacterController.UpdateCharacterPos?.Invoke();
            }

            //109852 キャラの位置変更以外で連打による操作が発生する際に育成プロローグで揺れもののケアが必要なため、
            //呼び出しタイミングを調整
            // 揺れものが危険な場合は防止コールバックを実行
            RunCySpringCare();

            // 最後に再生したフレーム数を記憶
            _lastFrameCount = _frameCount;
        }


        /// <summary>
        /// タイムラインのデータ更新の軽量化の禁止/解除を設定する
        /// </summary>
        public void SetDisableDataUpdateCullingState(bool isDisable)
        {
            _isDisableDataUpdateCulling = isDisable;
        }
        /// <summary>
        /// 最初の1フレームは必ずupdateするフラグを設定。
        /// </summary>
        public void SetFirstFrameUpdateState(bool isFirstFrame)
        {
            _isFirstFrame = isFirstFrame;
        }

        /// <summary>
        /// 119801 再生済みのBlockIndexを追加
        /// 元々リストに再生済みのIndexが入りていました（例えば0番のBlockからX番に飛ぶと、リストに０とXのIndexしか入らない）が、
        /// 高速モード対応で、いきなりストーリーを選択肢選択BlockかラストBlockに飛ぶようになり、
        /// その際に飛び先のBlockより一個前のClipDataを検索が必要で、元々の方式だと0番と飛び先X番目のBlockからしか検索できないので
        /// ここでIndexを更新する時リストに順番通りのデータになるようにする
        /// 注意:高速モード利用中は必ず第2引数にスキップされたBlockデータリストを渡すこと!
        /// </summary>
        /// <param name="addIndex"></param>
        /// <param name=""></param>
        public void UpdatePlayedBlockIndexList(int addIndex, List<StoryTimelineTextClipData> skippedBlockList = null)
        {
            if (IsSuperHighSpeedSkipMode() && skippedBlockList.IsNullOrEmpty() == false)
            {
                foreach (var data in skippedBlockList)
                {
                    var blockIndex = data.BlockData.BlockIndex;
                    if (PlayedBlockIndexList.Contains(blockIndex))
                    {
                        continue;
                    }

                    PlayedBlockIndexList.Add(blockIndex);
                }
            }
            else
            {
                PlayedBlockIndexList.Add(addIndex);
            }
        }


#if UNITY_EDITOR && CYG_DEBUG
        public void OnGameViewSizeChanged()
        {
            // #84212 初期化前に画面サイズ変更する場合があるのでnullチェック追加
            if (_timelineData != null)
            {
                _timelineData.RedrawCurrentBlock(_frameCount, _currentTime, IsWaiting);
            }
        }

        void OnDrawGizmos()
        {
            if (_timelineData != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(_timelineData.AABBCenterPos, _timelineData.AABBSize);
            }
        }
#endif
        /// <summary>
        /// 各種コントローラーのアップデートを行う
        /// </summary>
        private void UpdateController(int frameCount)
        {
            StillController?.AlterUpdate();
            SoundController?.AlterUpdate();
            if (LiveStreamingController != null)
            {
                LiveStreamingController.AlterUpdate(frameCount);
            }
        }

        #endregion アップデート処理

        #region タッチ制御
        /// <summary>
        /// 会話シーンの画面タップされた時の処理
        /// </summary>
        public void OnClickedStoryScene()
        {
            if (!_isContinuousTouch && _frameCount <= _continuousTouchFrameCount)
            {
                // 連打中
                // CONTINUOUS_TOUCH_INTERVALはTOUCH_BLOCK_INTERVALより長い: 多少タップ速度が遅くても連打として扱う
                _isContinuousTouch = true;
            }
            else if (_frameCount > _continuousTouchFrameCount)
            {
                _isContinuousTouch = false;
            }

            if (IsWaiting)
            {
                // Blockの末尾まで再生してタッチ待ちの状態
                // - 選択肢がない: タッチ待ちを解除して次のBlockに進む
                // - 選択肢がある: 選択肢が決定されるまで待機継続
                var blockData = _timelineData.GetCurrentBlockData(_frameCount);
                FinishBlockByTouch(blockData, weakenCySpring: false);

                return;
            }

            if (_frameCount < _touchBlockFrameCount)
            {
                // 連打された場合はここに来る
                Debug.LogFormat("[StoryTimelineController] Touch is blocked : {0} < {1}", _frameCount, _touchBlockFrameCount);
                return;
            }

            // 一定時間タッチできない
            _touchBlockFrameCount = Mathf.Min(_frameCount + TOUCH_BLOCK_INTERVAL, _frameCountForWaiting);

            // 連打判定される時間を設定する
            _continuousTouchFrameCount = _frameCount + CONTINUOUS_TOUCH_INTERVAL;

            // 会話開始直後の連打対策 ... 準備ブロックなら抜ける
            var block = _timelineData.GetCurrentBlockData(_frameCount);
            if (block.IsSettingBlock)
            {
                return;
            }

            //タップスキップが発生した時
            IsSkipFrameByTouch = true;

            // ワイプがある場合は別処理になる
            // 種類や配置によってフレームを飛ばしたりタップをブロックしたりする
            if (IsSkipOrBlockByWipe(block))
            {
                // ここで抜けておかないとタイプライトが完了してしまって見た目がおかしくなる
                return;
            }

            // タイプライトを終了させる
            // 既にタイプライトが終わっていた場合はFinishBlockByTouch()が実行される
            if (block.TextTrack.ClipList[0] is StoryTimelineTextClipData textClip)
            {
                // 会話開始直後の連打対策 ... TextClipより前ならTextClipの先頭まで飛ばす
                if (_frameCount < textClip.FixedStartFrame)
                {
                    _touchBlockFrameCount = textClip.FixedStartFrame + TOUCH_BLOCK_INTERVAL;


                    // フレーム数が飛ぶのでCySpringをリセットする
                    SkipFrameCount(textClip.FixedStartFrame, resetCySpring: true);
                    block.UpdateBlockDataOnFrameSkip();

                    //110369 現状育成プロローグのみに適用
                    if (CurrentDisplayMode == DisplayMode.SingleModePrologue)
                    {
                        RequestCySpringCare();
                    }
                    return;
                }

                if (textClip.IsTypewriteFinished)
                {
                    // フレーム数が飛ぶのでCySpringをリセットする
                    FinishBlockByTouch(block, weakenCySpring: true);
                }
                else
                {
                    textClip.FinishTypewriteByTouch();
                    block.UpdateBlockDataOnFrameSkip();
                }
            }
        }

        private int _careFrameCount = 0;
        /// <summary>
        /// 揺れもののケアに予約
        /// ケア処理はUpdate関数内で実行、現状は育成プロローグのみで利用している
        /// </summary>
        private void RequestCySpringCare(int careCount = -1)
        {
            //CySpringのケアフレーム数
            const int CYSPRING_CARE_FRAME_COUNT = 2;
            _isDirtyCySpring = true;
            //通常はTextClipのFixedStartTimeにスキップ時で利用
            //キャラのモーション反映は2フレームの遅延が生じているため、2フレーム分のケアを入れる
            _careFrameCount = careCount <= 0 ? CYSPRING_CARE_FRAME_COUNT : careCount;
        }

        /// <summary>
        /// 揺れものにケアをかける
        /// </summary>
        private void RunCySpringCare()
        {
            //110369 育成プロローグにタップスキップ時揺れもののパサつきにケアを入れる
            if (_isDirtyCySpring)
            {
                _careFrameCount--;

                PreventCySpringAccident?.Invoke();
                if (_careFrameCount >= 0)
                {
                    return;
                }

                CancelCySpringCare();
            }
        }

        /// <summary>
        /// 予約していた揺れものケアを取り消す
        /// </summary>
        private void CancelCySpringCare()
        {
            _isDirtyCySpring = false;
            _careFrameCount = 0;
        }

        /// <summary>
        /// ワイプによってフレームを飛ばすか
        /// </summary>
        /// <returns>タップ処理を中断させる場合はtrue</returns>
        private bool IsSkipOrBlockByWipe(StoryTimelineBlockData block)
        {
            if (block.WipeTrack.ClipList.Count == 0)
            {
                return false;
            }

            var wipeClip = block.WipeTrack.ClipList[0] as StoryTimelineWipeClipData;

            // イベント用ワイプの場合
            if (wipeClip.Type == StoryTimelineWipeClipData.WipeType.EventFlash)
            {
                var wipeStartFrame = wipeClip.FixedStartFrame;
                var blockFrame = wipeStartFrame + StoryTimelineWipeClipData.EVENT_WIPE_TOUCH_BLOCK_FRAME;
                if (_frameCount < blockFrame)
                {
                    // ワイプの冒頭はタップ飛ばしできなくする
                    _touchBlockFrameCount = Mathf.Max(blockFrame, _touchBlockFrameCount);

                    // ブロックされる区間内にいるのでタップ処理は途中で抜ける
                    return true;
                }
            }

            // 温泉Cuttの終わりに配置された場合
            // このケースでは1つ前のBlockに温泉Cuttが存在する (温泉CuttのClipはBlockをまたいで配置されている)
            var prevBlock = block.BlockIndex > 1 ? TimelineData.BlockList[block.BlockIndex - 1] : null;
            if (prevBlock?.TrainingCuttTrack.ClipList.Count > 0)
            {
                int frameCountInBlock = _frameCount - block.StartFrame;
                var cuttClip = prevBlock.TrainingCuttTrack.ClipList[0];
                if (_frameCount < cuttClip.FixedEndFrame && wipeClip.ContainsBlockFrame(frameCountInBlock))
                {
                    // 温泉Cuttと重ねて配置されたWipeがあるのでタップ飛ばしを禁止する
                    return true;
                }
            }

            // 先頭にワイプがある場合
            if (wipeClip.StartFrame == 0)
            {
                var wipeEndFrame = wipeClip.FixedEndFrame;
                if (_frameCount < wipeEndFrame)
                {
                    // ワイプの終わりまで飛ばす
                    // フレーム数が飛ぶのでCySpringをリセットする
                    SkipFrameCount(wipeEndFrame, resetCySpring: true);

                    // タップ処理は途中で抜ける
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// タッチにより現在のBlockを終了する (選択肢表示 or 次のBlock or 会話終了のいずれかになる)
        /// </summary>
        private void FinishBlockByTouch(StoryTimelineBlockData blockData, bool weakenCySpring)
        {
            // テキストが終了したので、選択肢、もしくは次に進むBlockを探す
            StoryTimelineTextClipData textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;

            if (textClipData.NeedsWaitInput)
            {
                // 入力待ちがあるのでそこまで進む
                int newFrameCount = textClipData.FixedEndFrame - 1;
                SkipFrameCount(newFrameCount, weakenCySpring);
                textClipData.StartWaitInput();
                return;
            }

            // タップで次のブロックに進む場合は専用の処理を実行
            blockData.UpdateBlockDataOnTouch();

            // 会話送りSE
            AudioManager.Instance.PlaySe(AudioId.SFX_UI_DECIDE_S_02);

            // タップで次のブロックに進む場合はストーリーSEを止める
            StopAudioSe(StoryTimelineSeClipData.STORY_SE_FADEOUT_TIME);

            // #142003対応: 表示されているキャラのリップシンクを強制的に即時終了させる
            ApplyVisibleCharacter((modelController) =>
            {
                // #148475 表情アニメーション再生を継続しながらブロックをまたぐと口の動きがおかしくなるのを防ぐ
                if (!modelController.IsPlayingFacialMotion)
                {
                    modelController.StopLipSync(true);
                }
            });

            // 指定されたBlockに進む
            // 連打状態だった場合はTextClipの先頭まで飛ばす
            GotoBlock(textClipData.NextBlockIndex, weakenCySpring);

            // タッチ操作によるBlock移動のフラグを立てる
            // ※フラグはAlterUpdate関数の最後にfalseにされるため、各種Update関数で使用可能
            IsMovedBlockByTouch = true;
        }

        /// <summary>
        /// Liveボタンが押された時の専用処理
        /// </summary>
        public void OnClickLiveButton()
        {
            // #92076 Block末尾に至る前にボタンが押された場合は次のBlockに進む
            var blockData = _timelineData.GetCurrentBlockData(_frameCount);
            if (_frameCount < blockData.EndFrame && blockData.TextTrack.ClipList[0] is StoryTimelineTextClipData textClipData)
            {
                GotoBlock(textClipData.NextBlockIndex, weakenCySpring: true);
                return;
            }

            // タップ待ちを解除して通常のUpdate処理で次のBlockに進む
            StopWaiting();
        }

        /// <summary>
        /// タッチ待ちを解除する
        /// </summary>
        public void StopWaiting()
        {
            // タッチ待ち関連の変数をリセット
            IsWaiting = false;
            _isWaitingUserInput = false;
            _hasClipEndSpace = false;
            _frameCountForWaiting = int.MaxValue;

            //Auto再生時には待ち開始時にIsWaiting = trueにならないので、ここでコールバックを呼び出す必要上がる
            OnAutoBlinkOn?.Invoke(null);
        }

        /// <summary>
        /// タッチ待ちを発生させるフレームを指定
        /// </summary>
        /// <param name="frameCount"></param>
        public void SetFrameCountForWaiting(int frameCount)
        {
            _frameCountForWaiting = frameCount;
        }

        /// <summary>
        /// 次のBlockに進むのか入力待ちをするのかチェック (必要なものには対応するフラグを立てる)
        /// </summary>
        private void CheckGotoNextAndWaitInput(out bool gotoNext, out bool waitInput, out StoryTimelineTextClipData textClipData)
        {
            gotoNext = false;
            waitInput = false;
            textClipData = null;

            // テキストに選択肢があるか確認
            var blockData = _timelineData.GetCurrentBlockData(_frameCountForWaiting);

            if (blockData?.TextTrack?.ClipList == null || blockData.TextTrack.ClipList.Count <= 0)
            {
                return;
            }

            textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            if (textClipData.NeedsWaitInput)
            {
                //グランドライブ特別演出でOn Stageボタンクリックされるまでを待つ
                // 選択肢を表示または名前入力でユーザの入力待ちをする
                waitInput = true;
                return;
            }

            if (blockData.BlockIndex == 0)
            {
                // 最初のブロックは特殊でAutoかどうかによらず必ず次に進む
                gotoNext = true;
                return;
            }

            if (!IsAutoPlay)
            {
                // タッチ待ちが必要
                return;
            }

            if (IsChangingOrientation != null && IsChangingOrientation.Invoke())
            {
                // 画面回転が完了するのを待つ
                return;
            }

            // Autoプレイになったのでタッチ待ちは解除
            // 選択肢もないので次のBlockに進む
            gotoNext = true;
        }

        /// <summary>
        /// 連続タップフラグが立ちつつづけているかどうかの更新を行う
        /// (ここで更新しなければ連続タップフラグが立った直後に次回タップするまでずっと立ち続けている)
        /// </summary>
        private void UpdateIsContinuousTouch()
        {
            if (IsContinuousTouch && _frameCount > _continuousTouchFrameCount)
            {
                IsContinuousTouch = false;
            }
        }

        #endregion タッチ制御

        #region 選択肢対応

        /// <summary>
        /// タッチ待ちを開始する
        /// </summary>
        public void StartWaitingForTouch()
        {
            if (_hasClipEndSpace)
            {
                // TextClipとBlock末尾との間に隙間が設定された場合は特殊
                // 現在のフレームがBlock末尾に到達していないなら何もせずに処理を抜ける
                var blockData = _timelineData.GetCurrentBlockData(_frameCount);
                int endFrame = blockData?.EndFrame ?? _timelineData.Length;
                if (_frameCount < endFrame)
                {
                    return;
                }
            }

            if (IsWaiting == false)
            {
                // コピーしておく
                // ここを基準に待ち中のモーションは進む
                WaitingCurrentTime = _currentTime;
            }

            // タッチ待ちフラグはStartWaitingForUserInput経由でも使うが
            // 選択肢表示中はOnClickedStoryScene()やCheckWaiting()によって解除される事はない
            IsWaiting = true;

            // 自動瞬きを始める
            OnAutoBlinkOn?.Invoke(null);
        }

        /// <summary>
        /// 選択肢決定などユーザの入力待ちを開始する
        /// </summary>
        public void StartWaitingForUserInput(bool hasClipEndSpace)
        {
            _isWaitingUserInput = true;
            _hasClipEndSpace = hasClipEndSpace;

            // さらに待機フラグを立てる共通処理を実行
            StartWaitingForTouch();

            //高速化スキップ中に選択肢表示時フッターボタン状態を更新
            SetFooterStatus();
        }

        /// <summary>
        /// 画面タップまたは選択肢を選んだときに指定のBlockに飛ぶ
        /// </summary>
        public void GotoBlock(int blockId, bool weakenCySpring, bool isUpdate = false, bool isChoice = false)
        {
            DebugLogPrintDirect($"<color=\"blue\">[StoryTimelineController]</color>: blockId = {blockId}, isUpdate = {isUpdate}, isChoice = {isChoice}.");

            if (blockId == StoryTimelineTextClipData.END_STORY)
            {
                // Story終了
                OnEndStory();
                return;
            }

#if CYG_DEBUG && UNITY_EDITOR
            if (blockId < 0 || blockId >= _timelineData.BlockList.Count)
            {
                Debug.LogErrorFormat("指定されたブロックが見つかりません: blockId={0}", blockId);
                return;
            }
#endif

            // 再生済みBlockリストに追加
            UpdatePlayedBlockIndexList(blockId);

            // 次のブロックのテキストClip取得 (必ずある前提なのでノーチェック)
            var blockData = _timelineData.BlockList[blockId];
            var textClip = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;

            // 倍速設定時にブロックのカウントを増やす
            IncrementBlockCount(blockData);

            // ワイプの存在チェック
            GetWipeFlag(blockData, out bool isWipe, out bool isEventWipe, out bool isRaceWipe);

            // 再生速度を更新する
            TimeScale = GetTimeScaleByHighSpeedType(isEventWipe, isRaceWipe);

            // 次のブロックに入る時のフレーム数を決める
            bool isHighSpeed = IsHighSpeedMode();
            int frameCount = GetNextBlockFrameCount(isHighSpeed, isWipe, isEventWipe, isRaceWipe, blockData, textClip);

            // タッチ待ちを解除
            StopWaiting();

            // タッチをブロックするフレーム数を設定
            SetTouchBlockFrameOnGotoBlock(blockData, textClip, frameCount, isChoice, isWipe, isEventWipe);

            // フレームを飛ばすのでSkip時の処理が必要
            SkipFrameCount(frameCount, weakenCySpring, isUpdate);

            // 新しいBlockに入る時はボイスとを止める
            AudioManager.Instance.StopVoiceAll();

            // 倍速再生中はSEを停止する
            if (isHighSpeed)
            {
                StopAudioSe(StoryTimelineSeClipData.STORY_SE_FADEOUT_TIME);
            }

            _elapsedFrameCount = (float)frameCount;

            #region 録画用処理

#if CYG_DEBUG && UNITY_EDITOR
            // 録画用のBlockIndexをセット
            if (IsRecordingStoryId)
            {
                SetRecordingBlockIndex(blockId);
            }
#endif

            #endregion
        }

        /// <summary>
        /// 倍速時にブロックを飛ばす判定を行う
        /// </summary>
        public void GotoBlockForSkip(int blockId, bool weakenCySpring)
        {
            if (blockId == StoryTimelineTextClipData.END_STORY)
            {
                // Story終了
                OnEndStory();
                return;
            }

            if (IsSuperHighSpeedSkipMode())
            {
                HighSpeedSkip(blockId, weakenCySpring);
            }
            else
            {
                NormalSkip(blockId, weakenCySpring);
            }
        }

        /// <summary>
        /// 通常ワイプおよびイベントワイプが存在したらフラグを立てる
        /// </summary>
        private static void GetWipeFlag(StoryTimelineBlockData blockData, out bool isWipe, out bool isEventWipe, out bool isRaceWipe)
        {
            isWipe = false;
            isEventWipe = false;
            isRaceWipe = false;
            if (blockData.WipeTrack.ClipList.Count == 0) return;
            if (!(blockData.WipeTrack.ClipList[0] is StoryTimelineWipeClipData wipeClipData)) return;

            isWipe = true;
            isEventWipe = wipeClipData.Type == StoryTimelineWipeClipData.WipeType.EventFlash;
            isRaceWipe = wipeClipData.Type == StoryTimelineWipeClipData.WipeType.TargetRace;
        }

        /// <summary>
        /// 次のBlockに移動する時にタッチをブロックするフレーム数を設定する
        /// </summary>
        private void SetTouchBlockFrameOnGotoBlock(StoryTimelineBlockData blockData, StoryTimelineTextClipData textClip, int frameCount, bool isChoice, bool isWipe, bool isEventWipe)
        {
            if (isChoice)
            {
                // 選択肢のテキスト追加が終わるまでタッチをふさぐ
                int interval = Mathf.CeilToInt(StoryLogItem.TWEEN_DURATION * GameDefine.NORMAL_FRAME_RATE);
                _touchBlockFrameCount = frameCount + interval;
            }

            if (isWipe && TryGetWipeBlockFrame(blockData, textClip, isEventWipe, out int wipeBlockFrame))
            {
                // ワイプがある場合はそれに応じてタッチをブロックするフレームを設定
                _touchBlockFrameCount = wipeBlockFrame;
            }

            if (_touchBlockFrameCount > blockData.EndFrame)
            {
                // 一部のイベントでは前のブロックに戻ることがある (初出: 安心沢イベント)
                // その場合はタッチ制限フレームを補正する
                _touchBlockFrameCount = frameCount + TOUCH_BLOCK_INTERVAL;
            }

            Debug.LogFormat("[StoryTimelineController] TouchBlockFrame = {0}", _touchBlockFrameCount);
        }

        /// <summary>
        /// ワイプがある場合はタッチをブロックするフレーム数を設定する
        /// </summary>
        private bool TryGetWipeBlockFrame(StoryTimelineBlockData blockData, StoryTimelineTextClipData textClip, bool isEventWipe, out int wipeBlockFrame)
        {
            var wipeClipData = blockData.WipeTrack.ClipList[0] as StoryTimelineWipeClipData;

            wipeBlockFrame = 0;
            if (isEventWipe || wipeClipData.StartFrame == 0)
            {
                // イベント用ワイプ、もしくは先頭にワイプがある場合はワイプによってタッチをブロックするフレームを設定
                var wipeMaster = MasterDataManager.Instance.masterStoryWipeDictionary.FindByWipeData(wipeClipData);

                if (wipeMaster == null)
                {
                    Debug.LogErrorFormat("[StoryTimelineController] StoryWipeDictionaryのレコードが見つかりません WipeId:{0}, SubId:{1}", wipeClipData.WipeId, wipeClipData.SubId);
                    return false;
                }
                wipeBlockFrame = wipeClipData.FixedStartFrame + wipeMaster.IgnoreTapLength;
            }
            else
            {
                // その他のタイプ・配置は何もしなくて良い
                return false;
            }

            // タイプライト終わるフレーム
            int textFrame = textClip.FixedEndFrame - textClip.WaitFrame;

            // 連打対応: ワイプがある場合は冒頭1/4までタッチできない
            // ワイプClipがテキストClipを超えて配置される場合もある点に注意 (その場合はテキスト開始位置とする)
            wipeBlockFrame = Mathf.Min(wipeBlockFrame, textFrame);
            return true;
        }

        /// <summary>
        /// 次のブロックに入る時のフレーム数を決定する
        /// </summary>
        private int GetNextBlockFrameCount(
            bool isHighSpeed,
            bool isWipe,
            bool isEventWipe,
            bool isRaceWipe,
            StoryTimelineBlockData blockData,
            StoryTimelineTextClipData textClip)
        {
            int frameCount = _timelineData.GetStartFrame(blockData);

            // グランドライブ特別演出の場合、倍速を無視してこのまま次のBlockの開始フレームを返せばいい
            // 途中で倍速になることも考慮不要
            if (CurrentPlayMode == PlayMode.GrandLive)
            {
                return frameCount;
            }

            // 倍速中にUpdateするフレームを設定 (途中で倍速になっても平気なように必ず実行)
            SetHighSpeedFrameCount(textClip);

            if (isHighSpeed && !isRaceWipe)
            {
                // 倍速中は専用のフレームに飛ぶ (TextClipの終端)
                // #134942対応: レース演出がある場合は除外する
                frameCount = _highSpeedFrameCountArray[FRAME_COUNT_INDEX_1ST];

                // 次のUpdateのためにIndexを更新
                _highSpeedFrameCountIndex = FRAME_COUNT_INDEX_END;
            }
            else if (IsSkipToTextClip(isWipe, isEventWipe))
            {
                // TextClipの先頭まで飛ばす場合はFrameCountを補正
                frameCount += textClip.StartFrame;
            }

            return frameCount;
        }

        /// <summary>
        /// TextClipの先頭まで飛ばすか
        /// </summary>
        private bool IsSkipToTextClip(bool isWipe, bool isEventWipe)
        {
            if (IsHighSpeedMode())
            {
                // 倍速中は特別イベント用ワイプじゃなければ飛ばす
                return !isEventWipe;
            }

            if (IsAutoPlay)
            {
                // オート再生の場合は飛ばさない
                return false;
            }

            // 連打中かつワイプでなければ飛ばす
            return _isContinuousTouch && !isWipe;
        }
        #endregion 選択肢対応

        #region 倍速対応

        /// <summary>
        /// 倍速設定別TimeScaleを更新する
        /// 倍速時はブロック尺を固定フレーム数にするために、現在ブロックの尺に応じて可変速度になる
        /// </summary>
        public void UpdateTimeScaleByHispeedType()
        {
            if (IsFinished)
            {
                // 会話が終わっていたら専用の再生倍率にする
                TimeScale = TimeScaleAfterEndStory;
                return;
            }

            TimeScale = GetTimeScaleByHighSpeedType(false, false);

            // Autoから倍速になった時にHighSpeedFrameCountIndexを必ず戻しておく(急にENDが走らないように)
            _highSpeedFrameCountIndex = FRAME_COUNT_INDEX_1ST;
        }

        /// <summary>
        /// HighSpeedTypeに応じてTimeScaleを設定する
        /// </summary>
        private static float GetTimeScaleByHighSpeedType(bool isEventWipe, bool isRaceWipe)
        {
            if (IsHighSpeedMode())
            {
                return GetTimeScaleHighSpeed(isEventWipe, isRaceWipe);
            }
            else
            {
                return StoryDefine.NORMAL_TIME_SCALE;
            }
        }

        /// <summary>
        /// 倍速中のTimeScaleを取得する
        /// </summary>
        private static float GetTimeScaleHighSpeed(bool isEventWipe, bool isRaceWipe)
        {
            // 特別イベント用ワイプがある場合は専用のタイムスケールにする
            // #134942対応: レース演出がある場合も専用のタイムスケールにする
            if (isEventWipe || isRaceWipe)
            {
                return TimeScaleEventWipe;
            }

            //グランドライブ演出で倍速のタイムスケールを利用する
            if (CurrentPlayMode == PlayMode.GrandLive)
            {
                switch (_highSpeedType)
                {
                    case HighSpeedType.HighSpeedX4:
                    case HighSpeedType.Max:
#if CYG_DEBUG
                        return DebugGrandLiveHighSpeedTimeScale;
#else
                        //6倍速
                        return StoryDefine.HIGH_SPEED_TIME_SCALE_GRAND_LIVE;
#endif
                    default:
                        return StoryDefine.NORMAL_TIME_SCALE;
                }
            }
            else
            {
                // 倍速の時はどのフレームに飛ぶのかを直接指定するのでTimeScaleを使うことはないが
                // イベントワイプとは別の値を返しておく
                return StoryDefine.HIGH_SPEED_TIME_SCALE_X4;
            }
        }

        /// <summary>
        /// 高速再生時に空テキスト表示かどうか
        /// </summary>
        public bool IsHighSpeedTextEmpty()
        {
            switch (_highSpeedType)
            {
                case HighSpeedType.HighSpeedX4:
                    var blockData = _timelineData.GetCurrentBlockData(_frameCount);
                    var textClipData = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;
                    if (textClipData != null)
                    {
                        // 3回に一回は非表示で良い、ただし最後、重要ブロックであればテキストは表示
                        if (_skipBlockCount % SKIP_COUNT_ALIGNMENT == HIDE_MESSAGE_COUNT && !IsEndBlock(blockData) && !IsImportantBlock(blockData))
                        {
                            return true;
                        }
                    }
                    return false;
            }

            return false;
        }

        /// <summary>
        /// 倍速中にUpdateするフレームを設定 (Play関数から呼ばれる)
        /// </summary>
        private void SetHighSpeedFrameCount(StoryTimelineTextTrackData textTrack, int nextFrameCount)
        {
            if (textTrack.ClipList.Count == 0)
            {
                // パドックの場合はTextClipがないので何もせず抜ける (倍速のサポートも不要)
                return;
            }

            var textClip = textTrack.ClipList[0] as StoryTimelineTextClipData;
            SetHighSpeedFrameCount(textClip);
        }

        /// <summary>
        /// 倍速中にUpdateするフレームを設定 (GotoBlockやSkipした時に呼ばれる)
        /// </summary>
        public void SetHighSpeedFrameCount(StoryTimelineTextClipData textClip)
        {
            // 補足: endの計算について
            // FixedEndFrameを使えば済むのだが内部でBlockData.StartFrameを呼んでしまう
            // BlockのStartFrameはDictionaryアクセスが走るので下記のように計算して少しでもコスト下げる
            int start = textClip.FixedStartFrame;
            int end = start + textClip.ClipLength;
            SetHighSpeedFrameCount(end);
        }

        /// <summary>
        /// 倍速中にUpdateするフレームをEndの1フレーム前に設定
        /// </summary>
        /// <param name="end">次に更新するBlockのTextClip終了フレーム</param>
        /// <remarks>
        /// 倍速再生の時は1つのBlockを1フレームで通り過ぎる
        /// TextClipの1つ前のフレームで全文表示、終了の2フレームだけ処理する
        /// </remarks>
        private void SetHighSpeedFrameCount(int end)
        {
            // 倍速時はTextClip終端 - 1フレームを使う
            // #92118時の改修
            _highSpeedFrameCountArray[FRAME_COUNT_INDEX_1ST] = end - 1;             // endにするとGotoBlockが発動するのでその1フレーム手前
            _highSpeedFrameCountArray[FRAME_COUNT_INDEX_END] = end;                 // GotoBlockに進むフレームを設定

            // 倍速時は必ずTextClip終了フレーム -1 から開始
            _highSpeedFrameCountIndex = FRAME_COUNT_INDEX_1ST;
        }

        #endregion　倍速対応

        #region スキップ対応
        /// <summary>
        /// 表示中のキャラモデルに対して引数で指定された処理を実行
        /// </summary>
        private void ApplyVisibleCharacter(System.Action<EventTimelineModelController> action)
        {
            var charaTrackList = _timelineData.GetCharacterHeadTrackList();
            int count = charaTrackList.Count;
            for (int i = 0; i < count; i++)
            {
                var modelController = charaTrackList[i].GetModelControllerFromCache();
                if (modelController == null)
                    continue;
                if (!modelController.GetVisible())
                    continue;

                // 表示中なら指定された処理を実行
                action.Invoke(modelController);
            }
        }

        /// <summary>
        /// 全キャラに対してデフォルト揺れもの姿勢に変更する (倍速でフレーム飛んだ時に呼ぶ)
        /// </summary>
        /// <remarks>
        /// この関数内でReserveWarmingUpCySpringを呼ぶと負荷がかなり高くなるので注意
        /// </remarks>
        private void ApplyCharacterSpring()
        {
            ApplyVisibleCharacter(ApplySpring);

            void ApplySpring(ModelController modelController)
            {
                //CySpringReset
                modelController.ResetCyspring();
                //CySpring更新
                modelController.AlterFixedLateUpdate();
                modelController.AlterFixedLateUpdatePost();
            }
        }

        /// <summary>
        /// キャラモーション更新系を即座に行う
        /// </summary>
        private void ApplyCharacterMotion()
        {
            ApplyVisibleCharacter(ApplyMotion);

            void ApplyMotion(ModelController modelController)
            {
                //モーション反映
                modelController.AlterFixedUpdate();
                modelController.AlterFixedUpdatePost();
            }
        }

        /// <summary>
        /// キャラにPairMotionOffsetを適用する
        /// </summary>
        private void ApplyCharacterPairMotionOffset()
        {
            ApplyVisibleCharacter(ApplyPairMotionOffset);

            void ApplyPairMotionOffset(EventTimelineModelController modelController)
            {
                // PairMotionOffsetが有効だったらオフセットを適用
                if (modelController.PairMotionOffsetComponent.IsEnable)
                {
                    modelController.PairMotionOffsetComponent.AfterUpdate();
                }
            }
        }

        private void SkipMotionFrame(int frameCount)
        {
            var prevFrameCount = FrameCount;
            var diffFrameCount = frameCount - prevFrameCount;
            var lastPlayBlock = _timelineData.LastPlayBlockData;

            //飛び先の1F前の姿勢にする
            FrameCount = prevFrameCount + (diffFrameCount - 1);

            //モーショントラックだけ更新する
            _timelineData.UpdateMotionTimelineData(_frameCount, _currentTime, lastPlayBlock);

            //キャラモーション反映を行う
            ApplyCharacterMotion();

            //その場でReset+Spring更新を1度だけ行う事で
            //今の姿勢のデフォルト揺れもの姿勢に変更する
            ApplyCharacterSpring();

            // 120907 131032 倍速再生時モーションスキップしてからCyspringのリセット後ウォーミングアップをかける
            //パフォーマンスを考慮して、通常2fのケアをかけますが、倍速中は1fに限定します
            //UpdateTime経由でSkipFrameCount経由しないで呼ばれるケースのあるので、
            //モーションスキップの時にケアをかけるように調整
            if (IsHighSpeedMode())
            {
                const int CYSPRING_CARE_FRAME_COUNT = 1;
                RequestCySpringCare(CYSPRING_CARE_FRAME_COUNT);
            }
        }

        /// <summary>
        /// 指定されたフレーム数まで飛ぶ
        /// </summary>
        public void SkipFrameCount(int frameCount, bool resetCySpring, bool isUpdate = false)
        {
            if (frameCount > ChoiceFrameCount)
            {
                // 選択肢を表示するフレーム数より先にはいけない
                return;
            }

            //倍速再生の時にはモーションだけ分割更新を行う
            if (IsHighSpeedMode())
            {
                SkipMotionFrame(frameCount);
            }

            // フレーム数更新
            FrameCount = frameCount;

            if (resetCySpring)
            {
                // タップやSkipによってフレーム数が飛んだ時に動きの大きいモーションがかさなると
                // 揺れものが暴れてしまうのでタイムライン更新後に揺れもの防止用コールバックを呼ぶ
                _isDirtyCySpring = true;
            }

            if (isUpdate)
            {
                // #51188対応: 指定された場合はデータの更新も行う
                // 倍速再生中に選択肢が決定された直後にFrameCountと見た目がズレる問題の解消
                UpdateTimelineDataSelf();

                // #106546対応: PairMotionOffsetを反映する
                // タイムラインデータの更新によりEffectのUpdateNonClip経由でStopShakeが実行される
                // それによりオフセットがリセットされてしまうので適用し直しておく
                ApplyCharacterPairMotionOffset();
            }

            // 最大フレーム数チェック
            if (_frameCount > _timelineData.Length)
            {
                // 最終Blockにて下記の条件全てを満たすとここに来る
                // 1. WipeClipがBlock冒頭から設定されている
                // 2. そのWipeClipの終端がTimelineDataの長さを超えている

                FrameCount = _timelineData.Length;

                OnEndStory();
            }
        }

        /// <summary>
        /// 通常の倍速スキップ
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="weakenCySpring"></param>
        private void NormalSkip(int blockId, bool weakenCySpring)
        {
            //このブロックが必ず処理するブロックならここを処理する
            var blockData = _timelineData.BlockList[blockId];
            if (IsProcessBlock(_skipBlockCount, blockData))
            {
                GotoBlock(blockId, weakenCySpring);
            }
            //処理しなくていいなら処理を飛ばして次のブロックへ
            else
            {
                // 飛ばしたブロックを再生済みBlockリストに追加
                UpdatePlayedBlockIndexList(blockId);

                //飛ばした際もスキップのカウントは増やす
                IncrementBlockCount(blockData);

                //テキストクリップ取得
                var textClip = blockData.TextTrack.ClipList[0] as StoryTimelineTextClipData;

                AddTextLog(textClip);

                //ジャンプするべき次のブロックIDを調べる
                int newBlockId = textClip.NextBlockIndex;
                GotoBlock(newBlockId, weakenCySpring);
            }
        }

        #region 高速化スキップ
        /// <summary>
        /// 高速化されたスキップ
        /// </summary>
        /// <param name="blockId"></param>
        /// <param name="weakenCySpring"></param>
        private void HighSpeedSkip(int blockId, bool weakenCySpring)
        {
            var blockData = _timelineData.BlockList[blockId];

            // 二択以上の選択肢が残っているか確認
            var skippedClipList = new List<StoryTimelineTextClipData>();
            var nextClip = CheckChoiseBlock?.Invoke(skippedClipList, blockData.StartFrame);

            // 連打で選択肢が飛ぶ問題の対応
            ChoiceFrameCount = nextClip != null ? nextClip.ChoiceFrameCount : int.MaxValue;

            //121768 元々の再生予定のBlockデータのIndexを追加
            UpdatePlayedBlockIndexList(blockData.BlockIndex);
            //122112 対応再生すべき最初のBlockデータを追加、遡って再生するケース以外はログ追加される
            //遡って再生の場合TextClipからログ登録となる
            if (blockData.TextTrack.ClipList[0] is StoryTimelineTextClipData blockDataTextClip)
            {
                skippedClipList.Insert(0, blockDataTextClip);
            }

            if (nextClip == null)
            {
                //#121456 選択肢の選び直しで、選択肢表示のBlockに飛ばすことがあるため、ここで再度チェックをかける
                //現在のFrameCountで選択肢ありのnextClipが取得できなくても、必ずラストBlockに飛ぶとは限らない（安心沢イベントなど）
                var isReturnToPrevBlock = IsHighSpeedSkipReturnToPrevChoiceBlock(skippedClipList, blockData.BlockIndex);
                if (isReturnToPrevBlock == false)
                {
                    //次に選択肢表示のブロックが存在しないため、直接にラストBlockに飛ばす
                    //121525 skippedClipListに再生すべき正しいEndBlockが入っているはずです。
                    //それを直接に利用。EndBlockについて一つのストーリーで複数のEndBlockが存在するものがあるので、
                    //選択肢によって（例えば0~35,36~50で再生するBlock分ける）再生が変わり、skippedClipListはそういったケースを考慮したデータとなります
                    var lastBlock = skippedClipList.FirstOrDefault(x => IsEndBlock(x.BlockData))?.BlockData ?? null;

                    if (lastBlock == null)
                    {
                        //渡されたBlockIdが再生すべきラストBlockである場合ここに入り（選択肢によって飛び先が違う、必ずしもタイムラインのラストBlockではない）、
                        //データをセットしてあげる
                        lastBlock = blockData;
                    }

                    //121336 lastBlockは「SkipFrameCount」のほうで更新をかけて「SetupTypewrite」でログ追加するので、
                    //2重追加を避けたいため、ここでlastBlockのログを追加しない
                    // 飛ばした分のテキストを会話ログに残す
                    AddSkippedTextLog(skippedClipList, lastBlock.BlockIndex);

                    //次に再生するBlockのIndexと飛ばされたBlockデータのIndexを記録
                    UpdatePlayedBlockIndexList(lastBlock.BlockIndex, skippedClipList);

                    // 二択以上の選択肢は残っていないので会話終了の-1フレまで飛ばす
                    HighSpeedSkipToTargetFrame(lastBlock.EndFrame - 1, HighSpeedSkipGetPrevBlockEndFrame(lastBlock, skippedClipList));

                    GotoBlock(lastBlock.BlockIndex, weakenCySpring);

                    //121660　選択肢選んでからラストBlockに遷移する際にフッターボタンを再度封印する
                    SetFooterStatus();
                }
                return;
            }

            //121442 次に表示する選択肢ありのBlockがあっても直接に遷移するとは限らない
            //例えばチュートリアルで説明の選択肢表示のところはなんども説明選択肢の表示があり得る
            if (IsHighSpeedSkipReturnToPrevChoiceBlock(skippedClipList, blockData.BlockIndex))
            {
                return;
            }

            var nextBlockIndex = nextClip.BlockData.BlockIndex;
            //121336 nextClipは「SkipFrameCount」のほうで更新をかけて「SetupTypewrite」でログ追加するので、
            //2重追加を避けたいため、ここでnextClipのログを追加しない
            // 飛ばした分のテキストを会話ログに残す
            AddSkippedTextLog(skippedClipList, nextBlockIndex);

            //次に再生するBlockのIndexと飛ばされたBlockデータのIndexを記録
            UpdatePlayedBlockIndexList(nextBlockIndex, skippedClipList);

            //入力待ちの場合「isUpdateWaitCurrentTime」の更新が必要
            HighSpeedSkipToTargetFrame(nextClip.ChoiceFrameCount, HighSpeedSkipGetPrevBlockEndFrame(nextClip.BlockData, skippedClipList));
            nextClip.StartWaitInput();

            // 倍速中にUpdateするフレームを設定
            SetHighSpeedFrameCount(nextClip.ChoiceFrameCount);
        }

        /// <summary>
        ///  //高速スキップで飛ばされたテキストをログに追加
        /// </summary>
        /// <param name="textClipList"></param>
        /// <param name="ignoreBlockIndex"></param>
        private void AddSkippedTextLog(List<StoryTimelineTextClipData> textClipList, int ignoreBlockIndex = -1)
        {
            if (textClipList.IsNullOrEmpty())
            {
                return;
            }

            foreach (var textClip in textClipList)
            {
                if (textClip.BlockData.BlockIndex == ignoreBlockIndex)
                {
                    continue;
                }

                AddTextLog(textClip);

                //プレイヤーの応答選択肢が存在する可能性があるので、
                //ここでスキップされたテキストクリップに対してログ追記を行う
                var choiceDataCount = textClip.ChoiceDataList?.Count ?? 0;
                if (choiceDataCount > 0)
                {
                    OnForceAddReplyChoiceLog?.Invoke(textClip);
                }
            }
        }

        /// <summary>
        /// 高速化スキップ中に目標フレームに飛ぶ
        /// </summary>
        /// <param name="targetFrameCount"></param>
        /// <param name="prevBlockEndFrame"></param>
        private void HighSpeedSkipToTargetFrame(int targetFrameCount, int prevBlockEndFrame)
        {
            void UpdateFrame(int frame)
            {
                //122121 高速スキップ中でユーザーの入力待ちになった場合、待ち時間に更新をかける、
                //かけないと「StoryTimelineMotionClipDataBase」のUpdateClipDataで参照する時、正しくないモーション時間となってしまい、モーションの見た目が変わってしまうことがありました
                //122383 選択肢選択後ラストBlockへ飛ぶ際にIsWaitingがTrueのままでまだFalseになっていないので、このタイミングでWaitCurrentTimeに更新しないとモーションが正しくなりません。
                //IsWaitingフラグを直接に見て時間更新するように変更しました
                if (IsWaiting)
                {
                    WaitingCurrentTime = (float)frame * GameDefine.BASE_FPS_TIME;
                }

                SkipFrameCount(frame, resetCySpring: true, isUpdate: true);
            }

            //121563 同一フレームで実行したキャラの設定の順序が保証されないので、
            //目線の設定は特定のキャラを注目のような設定ではターゲットキャラが取得できなくなる問題がありました。
            //------------------------------------------------------------
            //例えば「「X目のBlock」でキャラAを表示、「X+1目のBlock」でキャラBがキャラAを注視するといったケースに関して
            //高速スキップで直接に「X+1目のBlock」に飛ばしてしまい、キャラAがキャラBより後でセットアップされるケース」
            //既存のスキップでは必ず「X」→「X+1」のBlock処理順となり、問題が起きないはずです。
            //------------------------------------------------------------
            //同じBlockに対して「targetFrameCount」に前後フレームを使って更新をかけてもうまく更新できないため、
            //次のBlockより一つ手前のBlockで一回更新をかけて必要のキャラをセットアップさせる
            UpdateFrame(Mathf.Max(prevBlockEndFrame, 0));
            // 選択肢があるTextClipの末尾まで飛ばして選択肢を表示する
            // フレーム数が飛ぶのでCySpringをリセットをして飛んだ先のフレームでUpdateを行う
            UpdateFrame(targetFrameCount);
        }

        /// <summary>
        /// 121442 高速化スキップ中再生状態によって過去のBlockに遡って再生
        /// </summary>
        /// <param name="skippedClipList"></param>
        /// <param name="currentBlockIndex"></param>
        /// <returns></returns>
        private bool IsHighSpeedSkipReturnToPrevChoiceBlock(List<StoryTimelineTextClipData> skippedClipList, int currentBlockIndex)
        {
            //チュートリアルなどで選択肢の結果によって一定のストーリー再生後再び表示していた選択肢へ飛ぶ必要があるので、
            //次の選択肢ありのBlockへ直接飛ばさないようにする必要がある。
            //一番手前のBlock遡って再生設定のBlockを取得
            var nextClip = skippedClipList.FirstOrDefault(data => data.BlockData.BlockIndex > currentBlockIndex && data.NextBlockIndex < data.BlockData.BlockIndex);
            if (nextClip != null)
            {
                skippedClipList = skippedClipList.Where(data => data.BlockData.BlockIndex <= nextClip.BlockData.BlockIndex).ToList();

                //再生すべき過去のクリップを取得
                var targetClip = _timelineData.BlockList.FirstOrDefault(data => data.BlockIndex == nextClip.NextBlockIndex);

                if (targetClip != null && targetClip.TextTrack.ClipList[0] is StoryTimelineTextClipData nextTextClip)
                {
                    if (nextTextClip.ChoiceDataList.IsNullOrEmpty() == false)
                    {
                        AddSkippedTextLog(skippedClipList);
                        UpdatePlayedBlockIndexList(currentBlockIndex, skippedClipList);

                        //入力待ちの場合「isUpdateWaitCurrentTime」の更新が必要
                        //選択肢ありの場合そのまま表示させる
                        HighSpeedSkipToTargetFrame(nextTextClip.ChoiceFrameCount, HighSpeedSkipGetPrevBlockEndFrame(nextTextClip.BlockData, skippedClipList));
                        nextTextClip.StartWaitInput();

                        // 倍速中にUpdateするフレームを設定
                        SetHighSpeedFrameCount(nextTextClip.ChoiceFrameCount);
                        return true;
                    }
                }

            }

            return false;
        }

        /// <summary>
        /// 122212 高速化スキップで目標Blockに飛ぶ際に直前のBlockのEndFrameを取得
        /// </summary>
        /// <param name="currentBlockData"></param>
        /// <param name="searchList"></param>
        /// <returns></returns>
        private int HighSpeedSkipGetPrevBlockEndFrame(StoryTimelineBlockData currentBlockData, List<StoryTimelineTextClipData> searchList)
        {
            var prevBlockEndFrame = currentBlockData.StartFrame - 1;

            if (searchList.IsNullOrEmpty())
            {
                return prevBlockEndFrame;
            }

            StoryTimelineBlockData prevBlockData = searchList.OrderByDescending(data => data.BlockData.BlockIndex).FirstOrDefault(data => data.BlockData.BlockIndex < currentBlockData.BlockIndex)?.BlockData;

            if (prevBlockData != null)
            {
                prevBlockEndFrame = prevBlockData.EndFrame;
            }
            else
            {
                // #154759 目標Blockまでの間にBlockが無い場合
                // 例: 安心沢イベントで選択肢を選んだ後、遷移先のBlockが確認の選択肢となっている
                prevBlockEndFrame = _lastFrameCount;
            }

            return prevBlockEndFrame;
        }

        /// <summary>
        /// 表示されたテキストのログ追加
        /// </summary>
        /// <param name="textClipData"></param>
        private void AddTextLog(StoryTimelineTextClipData textClipData)
        {
            if (textClipData == null)
            {
                Debug.LogError("追加しようとするログのテキストクリップデータが見つかりませんでした！");
                return;
            }

            if (!FlagUtil.HasFlag(textClipData.FeaturesFlag, (int)StoryTimelineTextClipData.AdditionalFeatures.IgnoreTypewrite))
            {
                // 飛ばしたブロックのログを追加
                TextController.SkipTextAddLog?.Invoke(textClipData);
            }
        }
        #endregion

#if CYG_DEBUG
        /// <summary>
        /// 指定された数だけテキストを飛ばす
        /// </summary>
        /// <remarks>
        /// numが正ならページ飛ばし
        /// numが負ならページ戻し
        /// numがゼロなら同じページを再表示
        /// </remarks>
        public void JumpText(int num)
        {
            var block = TimelineData.LastPlayBlockData;
            if (block == null)
            {
                return;
            }

            // ジャンプ先を設定
            int blockId = block.BlockIndex;
            const int minBlockId = 1;
            int maxBlockId = TimelineData.BlockList.Count - 1;
            int nextId = Mathf.Clamp(blockId + num, minBlockId, maxBlockId);

            var nextBlock = TimelineData.BlockList[nextId];

            // ジャンプ先はBlockの先頭 (CySpringはそのままにする)
            SkipFrameCount(nextBlock.StartFrame, true);

            // 選択肢を出していたら非表示にする
            StoryChoiceController.Instance.CloseAllButton();

            // タッチ待ちを解除
            StopWaiting();
            _touchBlockFrameCount = INVALID_VALUE;
        }

        /// <summary>
        /// 該当フレームへのスキップを行います。
        ///
        /// 通常のSkipFrameCountだとブロックをまたいだ際にキャラの表示や背景などの不整合が発生してしまうため、
        /// それの対応バージョンとなります。
        /// </summary>
        /// <param name="frameCount"></param>
        /// <param name="resetCySpring"></param>
        /// <param name="isUpdate"></param>
        public void DebugSkipFrameCount(int frameCount, bool resetCySpring, bool isUpdate = false)
        {
            // スキップ先のブロックの取得
            var blockData = _timelineData.GetCurrentBlockData(frameCount);
            if (blockData == null) return;

            // 直前まで再生していたブロックの取得
            var curBlockData = _timelineData.GetCurrentBlockData(FrameCount);

            // 移動先が同一ブロックの時はSkipFrameだけを呼ぶ。
            if (curBlockData == blockData)
            {
                SkipFrameCount(frameCount, false, true);
                return;
            }

            // ブロックをまたぎで先に進むか戻るかで処理分岐
            if (blockData.BlockIndex > curBlockData.BlockIndex)
            {   // 未来に行く場合
                // ブロック差分だけ移動
                int blockNum = blockData.BlockIndex - curBlockData.BlockIndex;
                for (int i = 0; i < blockNum; i++)
                {
                    GotoBlock(curBlockData.BlockIndex + i, false, true);
                }
            }
            else
            {   // 過去に行く場合
                // ブロック再生履歴を一度初期化し、先頭から移動先まで移動
                PlayedBlockIndexList.Clear();
                for (int i = 0; i < blockData.BlockIndex; i++)
                {
                    GotoBlock(i, false, true);
                }
            }

            // ブロック内に移動したので該当フレームにスキップする
            SkipFrameCount(frameCount, false, true);
        }
#endif

        #endregion スキップ対応

        #region 環境設定

        /// <summary>
        /// Envファイルを指定して環境設定を更新
        /// </summary>
        public void ChangeEnvFromImageEffectParam(StoryEnvParam envParam)
        {
            // 現在適用中の環境設定を更新
            _envParam.CopyFrom(envParam);

            // 変更処理
            ChangeEnvProcess();
        }

        /// <summary>
        /// 2Dの背景Clipから環境設定を更新
        /// </summary>
        /// <param name="bgClipData"></param>
        public void ChangeEnvFrom2DBg(StoryTimelineBgClipData bgClipData)
        {
            StoryEnvParam envParam = bgClipData.EnvParam;
            if (envParam == null)
            {
                // Envファイルが無いなどでClipに設定が無ければ処理しない
                return;
            }

            if (_envParam.IsSameBg(envParam))
            {
                // 同じ背景の環境設定が反映済みなら処理しない
                // こうしておかないとDofが一瞬リセットされるという症状がでる
                return;
            }

            // 背景の設定を覚えておく（背景に戻したくなった時用）
            _envParamBg = envParam;

            // 現在適用中の環境設定を更新
            _envParam.CopyFrom(envParam);

            // 変更処理
            ChangeEnvProcess();
        }

        /// <summary>
        /// 環境設定を更新する時の共通処理
        /// </summary>
        private void ChangeEnvProcess()
        {
            // ライトの向き変更
            DirectionalLightManager.Instance.SetDirection(_envParam.DirectionalLightParam.LightRotation);

            for (int i = 0; i < _envParamArray.Length; i++)
            {
                if (_envParamArray[i] != null)
                {
                    _envParamArray[i].CopyFrom(_envParam);
                }
            }

            // 変更されたので反映
            UpdateEnvParam();
        }

        /// <summary>
        /// 3Dの背景Trackから環境設定を更新
        /// </summary>
        public void ChangeEnvFrom3DBg(StoryTimelineBg3DTrackData bgTrackData)
        {
            ChangeEnvFrom3DBg(bgTrackData.EnvParam);
        }

        /// <summary>
        /// 3D背景から環境設定を更新
        /// </summary>
        public void ChangeEnvFrom3DBg(TrainingEnvParam envParam)
        {
            if (envParam == null)
            {
                return;
            }

            if (_envParamBg == null)
            {
                _envParamBg = ScriptableObject.CreateInstance<StoryEnvParam>();
            }

            // めんどくさいけど、育成環境設定からストーリ環境設定ファイルへは別ファイルなので手動コピー
            _envParamBg.CopyFrom(envParam);
            _envParam.CopyFrom(envParam);

            // 変更処理
            ChangeEnvProcess();
        }

        /// <summary>
        /// 3Dの背景Trackから環境設定を更新
        /// </summary>
        public void ChangeEnvFrom3DBg(StoryEnvParam envParam)
        {
            if (envParam == null)
            {
                return;
            }

            if (_envParamBg == null)
            {
                return;
            }

            // 3d背景の環境設定では２D背景のBGIdとBGSubIdを渡す
            // ChangeEnvFrom2DBg関数の_envParam.IsSameBgの条件でブロックまたぎで２D背景の環境設定に戻さないようにするため
            envParam._bgId = _envParam._bgId;
            envParam._bgSubId = _envParam._bgSubId;

            // めんどくさいけど、３D背景環境設定から2d背景環境設定ファイルへは別ファイルなので手動コピー
            _envParamBg.CopyFrom(_envParam);
            _envParam.CopyFrom(envParam);

            // 変更処理
            ChangeEnvProcess();
        }

        /// <summary>
        /// 2D背景の環境設定に戻す
        /// </summary>
        public void RestoreEnvFromBefore2DBg()
        {
            if (_envParamBg == null) return;

            _envParam.CopyFrom(_envParamBg);

            // 変更処理
            ChangeEnvProcess();
        }

        /// <summary>
        /// 環境設定Clipから環境設定を更新
        /// </summary>
        public void ChangeEnvFromCharacter(StoryTimelineCharaEnvClipData envClipData)
        {
            // 自分がキャラの中で何番目か調べてそのIndexを使う (HeadTrackを使って調べる)
            var charaTrackData = envClipData.TrackData.GetParentHeadTrack<StoryTimelineCharaTrackData>();
            var index = charaTrackData.CharacterTrackIndex;
            var targetParam = _envParamArray[index];

            if (envClipData.IsControl)
            {
                // パラメータ渡し
                envClipData.CharacterColorData.CopyParam(targetParam.CharacterColorData);

                if (envClipData.OverwriteDirectionalLight)
                {
                    // #89999 LightProbeColorの更新のみサポート
                    targetParam.DirectionalLightParam.LightProbeColor = envClipData.DirectionalLightParam.LightProbeColor;
                }
            }
            else
            {
                // 背景のを持ってくる
                targetParam.CopyFrom(_envParamBg);
            }

            // 変更されたのでキャラ関連のみを反映
            // こうしておかないとDofが一瞬リセットされるという症状がでる
            UpdateEnvParamForCharacter(index);
        }

        /// <summary>
        /// 環境光の状態を更新する
        /// </summary>
        public void UpdateReverseDirectionalLightStatus(bool isReverseLight, bool notHavePrevClip)
        {
            var isChangeStatus = isReverseLight != _isBeforeReverseLightStatus;
            var currentRotation = DirectionalLightManager
                .Instance
                .GetRotation();

            var nextRotation = isChangeStatus
                ? new Quaternion(currentRotation.x, -currentRotation.y, -currentRotation.z, currentRotation.w)
                : currentRotation;

            //DirectionalLightを回転
            DirectionalLightManager.Instance.SetRotation(nextRotation);

            //OriginalDirectionalLightを回転
            var index = 0;
            foreach (var charaTack in _timelineData.GetCharacterHeadTrackList())
            {
                var envParam = _envParamArray[index];
                var modelController = charaTack.GetModelControllerFromCache();

                if (envParam == null || modelController == null)
                {
                    continue;
                }

                //OriginalDirectionalLightの更新
                var colorData = envParam.CharacterColorData;
                modelController.ApplyCharacterColorData(colorData, isReverseLight);

                index++;
            }

            _isBeforeReverseLightStatus = isReverseLight;
        }

        /// <summary>
        /// 環境設定を設定
        /// </summary>
        public void UpdateEnvParam(bool isUpdateImageEffect = true)
        {
            // キャラ用設定は全員に反映させる
            UpdateEnvParamForCharacter();

            // 136957 一部のケースで処理順によりポスト情報が上書きされることが困るため制御できるように対応。
            // 分岐追加時点では会話のCutt終了時のみfalse
            if (isUpdateImageEffect)
            {
                UpdateEnvParamForImageEffect();
            }
            UpdateEnvParamForTreeSunbeams();
        }

        /// <summary>
        /// 環境設定のうちCharacter関連を設定
        /// </summary>
        public void UpdateEnvParamForCharacter(int index = -1)
        {
            var charaTrackList = _timelineData.GetCharacterHeadTrackList();

            // ModelControllerへの反映
            if (index < 0)
            {
                // 全員
                int count = charaTrackList.Count;
                for (int i = 0; i < count; i++)
                {
                    Apply(i);
                }
            }
            else
            {
                // 個別
                Apply(index);
            }

            // ローカル関数
            void Apply(int i)
            {
                var modelController = charaTrackList[i].GetModelControllerFromCache();
                if (modelController == null) return;

                modelController.ApplyEnvParam(_envParamArray[i]);
            }
        }

        /// <summary>
        /// 環境設定のうちImageEffect関連を設定
        /// </summary>
        private void UpdateEnvParamForImageEffect()
        {
            if (ImageEffectController == null)
            {
                // イメージエフェクトを使わない
                return;
            }

            if (_envParam == null)
            {
                return;
            }

            // 背景の設定を適用
            _envParam.CopyTo(ImageEffectController);
        }

        /// <summary>
        /// 環境設定のうち木漏れ日関連を反映
        /// </summary>
        private void UpdateEnvParamForTreeSunbeams()
        {
            BackgroundController?.UpdateTreeSunbeams(_envParam.IsTreeSunbeams, _envParam.AdditiveBgId, _envParam.TreeSunbeamsColor);
        }

        /// <summary>
        /// 次の背景のEnvParamをキャッシュしておく
        /// </summary>
        public void SetNextEnvParamBg(StoryEnvParam envParam)
        {
            // 一部データを変更したいため, 参照コピーではなく各項目の設定値をコピーする形にする
            _nextEnvParamBg.CopyFrom(envParam);

            // 通常, モデルのApplyEnvParam関数ではLightManagerに設定されたライト角度を反映させる
            // この仕組みだとフェードインしてくるキャラに次の背景のライト角度を設定できない (LightManagerには前の背景のライト角度が入っているので)
            // そこでDirectionalLightParamをColorDataにコピーして直接キャラに反映させる
            var lightDir = _nextEnvParamBg.CharacterColorData.LightDir;
            if (lightDir.IsValid) return; // 元々個別ライトを使う設定だった場合はそっちを優先する
            lightDir.IsValid = true;
            lightDir.Value = _nextEnvParamBg.DirectionalLightParam.LightRotation;
        }

        /// <summary>
        /// 次の背景のEnvParamを指定されたキャラに適用する
        /// </summary>
        public void ApplyNextEnvParamBgToChara(EventTimelineModelController modelController)
        {
            modelController.ApplyEnvParam(_nextEnvParamBg);
        }
        #endregion 環境設定

        #region サウンド

        #region BGM
        /// <summary>
        /// BgmClipDataの有効フラグを初期化
        /// </summary>
        public void InitializeBgmClipEnabled()
        {
            // 倍速ならBgmClipDataからは再生不可
            IsBgmClipEnabled = !IsHighSpeedMode();
        }

        /// <summary>
        /// BgmClipDataを有効にする
        /// </summary>
        public void SetEnableBgmClip(bool enable)
        {
            IsBgmClipEnabled = enable;
        }

        /// <summary>
        /// 育成BGMを維持するかフラグを初期化
        /// </summary>
        public void InitializeKeepSingleModeBgm()
        {
            if (_timelineData == null)
            {
                ShouldStopSingleModeBgm = false;
                return;
            }

            // 会話が始まった時に育成BGMを停止する条件は
            // 1. BgmClipが有効 (倍速再生ではない)
            // 2. 先頭のBgmClipにて育成BGMを維持しない (IsPlaySingleModeBgmがfalse)
            // 3. 先頭のBgmClipにてBGM再生を行う (IsPlayがtrue)

            // まず条件1.を設定
            ShouldStopSingleModeBgm = IsBgmClipEnabled;
            if (!ShouldStopSingleModeBgm)
            {
                // 既にfalse確定しているので追加の判定は不要
                return;
            }

            // 最初に配置されたBGMクリップの設定によってフラグを初期化
            int length = _timelineData.BlockList.Count;
            for (int i = 0; i < length; i++)
            {
                var block = _timelineData.BlockList[i];
                var bgmClip = GetBgmClipFromList(block.DspBgmTrackList) ?? GetBgmClip(block.BgmTrack);
                if (bgmClip != null)
                {
                    // 条件2.と3.を設定
                    ShouldStopSingleModeBgm &= !bgmClip.IsPlaySingleModeBgm;
                    ShouldStopSingleModeBgm &= bgmClip.IsPlay;

                    // クリップが見つかったので処理を抜ける
                    return;
                }
            }

            StoryTimelineBgmClipDataBase GetBgmClipFromList(List<StoryTimelineDspBgmTrackData> trackList)
            {
                foreach (var track in trackList)
                {
                    var bgmClip = GetBgmClip(track);
                    if (bgmClip != null)
                    {
                        return bgmClip;
                    }
                }

                return null;
            }

            StoryTimelineBgmClipDataBase GetBgmClip(StoryTimelineTrackData trackData)
            {
                foreach (var clip in trackData.ClipList)
                {
                    if (clip is StoryTimelineBgmClipDataBase bgmClip)
                    {
                        return bgmClip;
                    }
                }

                return null;
            }
        }

        /// <summary>
        /// 倍速設定変更時にBgmClipを有効化 (この関数は倍速設定の変更後に呼び出す)
        /// </summary>
        public void SetBgmClipEnabledOnSwitchHighSpeedMode()
        {
            bool isHighSpeedMode = IsHighSpeedMode();

            if (!IsBgmClipEnabled && !isHighSpeedMode)
            {
                // 再生中に倍速が解除されたらBGMを設定できるようする
                // 同じ会話の中で再び倍速になった時はそのままBgmClipDataで再生可能とする
                IsBgmClipEnabled = true;

                // 育成BGMが再生されていたら止める
                if (SceneDefine.TryGetViewData(SceneDefine.ViewId.SingleModeMain, out var viewData) &&
                    AudioManager.Instance.IsPlayBgm(viewData.BgmAudioId))
                {
                    AudioManager.Instance.StopBgm(viewData.BgmAudioId);
                }

                // BGMの開始時間を補正させる
                _correctBgmStartTime = true;

                // 本来再生するはずだったBGMを流す
                OnBgmEnabled?.Invoke();
                OnBgmEnabled = null;
            }
        }

        public void PlayBgmFromName(
            string cueSheetName,
            string cueName,
            int dspBusIndex = -1,
            bool isLoop = true,
            bool isPlaySingleModeBgm = false,
            float fadeInTime = AudioManager.BGM_FADEIN_TIME,
            float fadeOutTime = AudioManager.BGM_FADEOUT_TIME,
            float startTime = 0f,
            bool isCrossFade = true,
            int fadeCurveId = 0)
        {
            // BGM再生をするフレームを保存 (倍速を解除した時に使う)
            _lastBgmFrameCount = FrameCount;

            if (IsBgmClipEnabled)
            {
                PlayBgm();
            }
            else
            {
                // BgmClipDataで再生しない場合は育成のBGMを再生する
                RequestSingleModeBgm();

                // 倍速が解除されたらBgmClipDataでの再生が可能になる
                // その時にBgmClipDataが指定したものが再生できるようにしておく
                if (isPlaySingleModeBgm)
                {
                    // 育成BGMを維持する
                    OnBgmEnabled = null;
                }
                else if (isCrossFade)
                {
                    // クロスフェードによって育成BGMが停止する
                    OnBgmEnabled = PlayBgm;
                }
                else
                {
                    // 育成BGMを止めてから再生 (Linearにフェードアウトしてよい)
                    OnBgmEnabled = () =>
                    {
                        var bgmPlaybackIndex = GetBgmPlaybackIndex(dspBusIndex);
                        AudioManager.Stop(_bgmPlaybackArray[bgmPlaybackIndex]);
                        SetDspBgmSendLevel(bgmPlaybackIndex, from: 0f, to: 0f, time: 0f);
                        PlayBgm();
                    };
                }
            }

            // 再生時のローカル関数
            void PlayBgm()
            {
                if (isPlaySingleModeBgm)
                {
                    // フラグが立っていたら育成BGMを再生する
                    RequestSingleModeBgm();
                    return;
                }

                // 倍速中に他のBGMがかかっていたら強制で止める
                ForceStopBgmOnHighSpeed();

                // 必要に応じてBGM開始時間を補正
                startTime = GetBgmStartTime(startTime);

                // 通常時はClipで指定されたBGMを再生する
                var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
                AudioManager.Instance.PlayBgmFromName(cueSheetName, cueName, isLoop, _bgmVolume, fadeInTime, fadeOutTime, startTime, isCrossFade, fadeCurve);
                _bgmPlaybackArray[GetBgmPlaybackIndex(dspBusIndex)] = AudioManager.Instance.BgmPlayback;
#if UNITY_EDITOR
                _bgmPlaybackList.Add(AudioManager.Instance.BgmPlayback);
#endif // UNITY_EDITOR
                if (IsDspBgm(dspBusIndex))
                {
                    // DSP版はAudioPlaybackが別なので別にクロスフェードする
                    if (isCrossFade)
                    {
                        var fadeoutDspBusIndex = (dspBusIndex + 1) % StoryTimelineBlockData.DSP_BGM_MAX;
                        AudioManager.Stop(_bgmPlaybackArray[fadeoutDspBusIndex], fadeOutTime, fadeCurve: fadeCurve);
                    }
                    // DSPバスのセンドレベルを指定する（ADX2 Craftで初期値を0（-∞db）に指定済）
                    SetDspBgmSendLevel(dspBusIndex, 1f, 1f, 0f);
                }

                // エディタ向けのサウンド情報を更新
                UpdateBgmInfo(cueName, _bgmVolume);
            }
        }

        /// <summary>
        /// 育成のBGMを再生する
        /// </summary>
        private void RequestSingleModeBgm()
        {
            if (_isSingleModeBgmPlaying)
            {
                return;
            }

            _isSingleModeBgmPlaying = true;

            // BGM,環境音再生 (クロスフェードになった場合はLinearにフェードアウトしてよい)
            PlaySingleModeBgm?.Invoke();
            _bgmPlaybackArray[0] = AudioManager.Instance.BgmPlayback;

            // エディタ向けのサウンド情報を更新
            UpdateBgmInfo(_bgmPlaybackArray[0].CueName, _bgmVolume);
        }

        /// <summary>
        /// 倍速中に他のBGMがかかっていたら強制で止める
        /// </summary>
        private void ForceStopBgmOnHighSpeed()
        {
            if (!IsHighSpeedMode())
            {
                return;
            }

            for (int i = 0; i < _bgmPlaybackArray.Length; i++)
            {
                AudioManager.Stop(_bgmPlaybackArray[i]);
            }
        }

        /// <summary>
        /// 倍速を解除した時にBGM開始時間を補正する
        /// </summary>
        private float GetBgmStartTime(float startTime)
        {
            if (!_correctBgmStartTime)
            {
                // 補正しない
                return startTime;
            }

            // 補正する時にフラグ落とす
            _correctBgmStartTime = false;

            // 本来再生するはずだった時間から経過した分を加算する
            float correct = (FrameCount - _lastBgmFrameCount) * GameDefine.BASE_FPS_TIME;
            return startTime + correct;
        }

        /// <summary>
        /// BGMを停止する
        /// </summary>
        public void StopBgm(float fadeoutTime = 0f, int fadeCurveId = 0, int dspBusIndex = -1)
        {
            var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
            StopBgm(fadeoutTime, fadeCurve, dspBusIndex);
        }

        public void StopBgm(float fadeoutTime = 0f, FadeCurve fadeCurve = FadeCurve.Linear, int dspBusIndex = -1)
        {
            var bgmPlaybackIndex = GetBgmPlaybackIndex(dspBusIndex);

            if (!IsBgmClipEnabled)
            {
                // BgmClipDataからの停止も受け付けない
                // 倍速が解除された時にBgmを止めるようにする
                OnBgmEnabled = StopBgmInternal;
                return;
            }

            if (_isBgmStoppingArray[bgmPlaybackIndex])
            {
                // 長めのフェード時間が設定されていると完全に停止するより先に次のBlockに入ってUpdateNonClipからStopBgmが呼ばれる事がある
                // フェードアウトの途中でまたフェード付きStopをかけるとおかしくなるので処理を抜ける
                return;
            }

            StopBgmInternal();

            // 停止時のローカル関数
            void StopBgmInternal()
            {
                ref var bgmPlayback = ref _bgmPlaybackArray[bgmPlaybackIndex];
                if (!AudioManager.IsPlay(bgmPlayback))
                {
                    return;
                }
                AudioManager.Stop(bgmPlayback, fadeoutTime,
                    () => _isBgmStoppingArray[bgmPlaybackIndex] = false, fadeCurve);
                if (IsDspBgm(dspBusIndex))
                {
                    SetDspBgmSendLevel(dspBusIndex, from: 1f, to: 0f, time: fadeoutTime);
                }
                _isBgmStoppingArray[bgmPlaybackIndex] = true;

                // エディタ向けのサウンド情報を更新
                ResetBgmInfo();
            }
        }

        /// <summary>
        /// BGMを全て停止する
        /// </summary>
        public void StopBgmAll(float fadeOutTime = 0.0f, FadeCurve fadeCurve = FadeCurve.Linear)
        {
            for (var i = 0; i < StoryTimelineBlockData.DSP_BGM_MAX; i++)
            {
                StopBgm(fadeOutTime, fadeCurve: fadeCurve, dspBusIndex: i);
            }
#if UNITY_EDITOR
            foreach (var playback in _bgmPlaybackList)
            {
                AudioManager.Stop(playback, fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            }
            _bgmPlaybackList.Clear();

            // エディタ向けのサウンド情報を更新
            ResetBgmInfo();
#endif // UNITY_EDITOR
        }

        /// <summary>
        /// 現在のBGMをプレイバック配列に登録する
        /// </summary>
        public void SetBgmPlayback(int dspBusIndex = -1)
        {
            var bgmPlaybackIndex = GetBgmPlaybackIndex(dspBusIndex);
            _bgmPlaybackArray[bgmPlaybackIndex] = AudioManager.Instance.BgmPlayback;
        }

        public bool IsPlayBgm(int dspBusIndex = -1)
        {
            return AudioManager.IsPlay(_bgmPlaybackArray[GetBgmPlaybackIndex(dspBusIndex)]);
        }

        public bool IsPlayBgm(string cueSheetname, string cueName, int dspBusIndex = -1)
        {
            ref var bgmPlayback = ref _bgmPlaybackArray[GetBgmPlaybackIndex(dspBusIndex)];
            return AudioManager.IsPlay(bgmPlayback) &&
                   bgmPlayback.CueSheetName.Equals(cueSheetname) &&
                   bgmPlayback.CueName.Equals(cueName);
        }

        public bool IsAnyPlayBgm()
        {
            bool isAnyPlay = false;
            int length = _bgmPlaybackArray.Length;
            for (int i = 0; i < length; i++)
            {
                isAnyPlay |= AudioManager.IsPlay(_bgmPlaybackArray[i]);
            }
            return isAnyPlay;
        }

        public void SetBgmVolume(float volume, float fadeTime, int fadeCurveId, int dspBusIndex = -1)
        {
            var bgmPlaybackIndex = GetBgmPlaybackIndex(dspBusIndex);
            if (_isBgmStoppingArray[bgmPlaybackIndex])
            {
                // 長めのフェード時間が設定されていると完全に停止するより先に次のBlockに入ってUpdateNonClipからStopBgmが呼ばれる事がある
                // フェードアウトの途中でボリューム変更されるとおかしくなるので処理を抜ける
                return;
            }

            _bgmVolume = volume;
            ref var bgmPlayback = ref _bgmPlaybackArray[GetBgmPlaybackIndex(dspBusIndex)];

            if (!AudioManager.IsPlay(bgmPlayback))
            {
                return;
            }

            var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
            AudioManager.SetVolume(bgmPlayback, volume, fadeTime, fadeCurve);

            // エディタ向けのサウンド情報を更新
            UpdateBgmInfo(bgmPlayback.CueName, _bgmVolume);
        }

        /// <summary>
        /// BGMのDSPバスセンドレベルを指定する
        /// </summary>
        /// <param name="dspBusIndex">DSPBGMトラックが制御するDSPバスのインデックス</param>
        /// <param name="from">フェード開始センドレベル</param>
        /// <param name="to">フェード終了センドレベル</param>
        /// <param name="time">フェード時間</param>
        /// <remarks>
        /// CriAtomSourceのプレイヤーを停止した場合は、センドレベルの変更はリセットされます。
        /// </remarks>
        private void SetDspBgmSendLevel(int dspBusIndex, float from, float to, float time)
        {
            var busName = StoryTimelineDspBgmTrackData.DSP_CHANNEL_NAME_ARRAY[dspBusIndex];
            ref var bgmPlayback = ref _bgmPlaybackArray[GetBgmPlaybackIndex(dspBusIndex)];
            AudioManager.SetBusSendLevel(bgmPlayback, busName, from, to, time);
        }

        /// <summary>
        /// DSPバス音量を設定する
        /// </summary>
        /// <param name="storyDspBusIndex">DSPBGMトラックが制御するDSPバスのインデックス</param>
        /// <param name="toLevel">フェード終了DSPバス音量</param>
        /// <param name="fadeTime">フェード時間</param>
        /// <param name="fadeCurveId">フェードカーブのインデックス</param>
        public void SetDspBgmVolume(int storyDspBusIndex, float toLevel, float fadeTime, int fadeCurveId)
        {
            var fromLevel = _bgmDspBusVolumeArray[storyDspBusIndex];
            _bgmDspBusVolumeArray[storyDspBusIndex] = toLevel;

            // 初回の「DSPバス音量変更」かチェック
            if (_bgmDspBusVolumeChange2ndTimeArray[storyDspBusIndex])
            {
                // 前回と同じ「DSPバス音量」にセットしようとしているかをチェックする
                // 「AtomCraftデータ側で設定されているDSPバス音量」の値は、0.0fでは無い為、
                // 初回の「DSPバス音量変更」の時は、前回の「DSPバス音量変更」を比較しない
                if (Math.IsFloatEqual(fromLevel, toLevel))
                {
                    return;
                }
            }
            else
            {
                _bgmDspBusVolumeChange2ndTimeArray[storyDspBusIndex] = true;
            }

            var busName = StoryTimelineDspBgmTrackData.DSP_CHANNEL_NAME_ARRAY[storyDspBusIndex];
            var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
            AudioManager.SetBusVolume(busName, fromLevel, toLevel, fadeTime, fadeCurve);
        }

        private static int GetBgmPlaybackIndex(int dspBusIndex)
        {
            return Mathf.Clamp(dspBusIndex, 0, StoryTimelineBlockData.DSP_BGM_MAX - 1);
        }

        private static bool IsDspBgm(int dspBusIndex)
        {
            return -1 < dspBusIndex && dspBusIndex < StoryTimelineDspBgmTrackData.DSP_CHANNEL_NAME_ARRAY.Length;
        }

        #endregion BGM

        #region 環境音

        /// <summary>
        /// 背景環境音のCueNameを取得
        /// </summary>
        private static void GetBgEnvSoundCueName(int bgId, int subId, out string cueName, out string sheetName)
        {
            // 背景ID, SubIDからマスターデータを取得
            MasterBackgroundData.BackgroundData backgorundData = MasterDataManager.Instance.masterBackgroundData.GetWithBgIdAndBgSub(bgId, subId);

            // 環境音指定がある場合
            if (backgorundData != null)
            {
                cueName = backgorundData.CueName;
                sheetName = backgorundData.SheetName;
                return;
            }

            // ない場合は空文字にしておく
            cueName = string.Empty;
            sheetName = string.Empty;
        }

        /// <summary>
        /// 背景に対応する環境音がある場合は再生する
        /// </summary>
        /// <param name="bgId"></param>
        /// <param name="subId"></param>
        public void PlayBgEnvSound(int bgId, int subId)
        {
            // 背景環境音のCueName, sheetNameを取得
            GetBgEnvSoundCueName(bgId, subId, out var cueName, out var sheetName);

            PlayBgEnvSound(cueName, sheetName);
        }

        /// <summary>
        /// 背景環境音の再生を予約
        /// </summary>
        public void ReserveBgEnvSound(int bgId, int subId)
        {
            // 背景環境音のCueName, sheetNameを取得
            GetBgEnvSoundCueName(bgId, subId, out var cueName, out var sheetName);

            ReserveBgEnvSound(cueName, sheetName);
        }

        /// <summary>
        /// 背景環境音の再生を予約
        /// </summary>
        public void ReserveBgEnvSound(string cueSheetName, string cueName)
        {
            ReservedEnvCueName = cueName;
            ReservedEnvCueSheetName = cueSheetName;
        }

        /// <summary>
        /// 予約された背景環境音を再生する
        /// </summary>
        public bool PlayReservedBgEnvSound()
        {
            IsEnvSeEnabled = true;
            if (string.IsNullOrEmpty(ReservedEnvCueName))
            {
                return false;
            }
            PlayBgEnvSound(ReservedEnvCueSheetName, ReservedEnvCueName);
            ReservedEnvCueName = string.Empty;
            ReservedEnvCueSheetName = string.Empty;
            return true;
        }

        /// <summary>
        /// CueName指定による背景環境音再生
        /// </summary>
        public void PlayBgEnvSound(string cueSheetName, string cueName)
        {
#if CYG_DEBUG
            //140132 ストーリーダイレクトシーンで無音再生の時環境音を流さない
            if (IsForcedMute)
            {
                StopBgEnvSound();
                return;
            }
#endif

            if (string.IsNullOrEmpty(cueName))
            {
                // 指定が無い場合は現在鳴っている環境音を停止
                StopBgEnvSound();
                return;
            }

            if (cueName.Equals(MUTE_CUE_NAME))
            {
                // mute指定の場合は環境音を停止
                StopBgEnvSound();
                return;
            }

            if (AudioManager.IsPlaySe(_envPlayback))
            {
                if (_envPlayback.CueName == cueName)
                {
                    // 既に再生されているので何もしない
                    return;
                }

                // 別の環境音が再生されているので止める
                AudioManager.Instance.StopSe(_envPlayback, StoryTimelineSeClipData.STORY_ENV_FADEOUT_TIME);
            }

            // 環境音を再生し、Playbackを保持
            _envPlayback = AudioManager.Instance.PlaySe(cueSheetName, cueName);
            // 音量を設定
            SetBgEnvSoundVolume(_envVolume, 0f);

            // エディタ向けのサウンド情報を更新
            UpdateEnvInfo(cueName, _envVolume);
        }

        /// <summary>
        /// 背景に対応する環境音を停止
        /// </summary>
        private void StopBgEnvSound()
        {
            AudioManager.Instance.StopSe(_envPlayback, StoryTimelineSeClipData.STORY_ENV_FADEOUT_TIME);
            _envPlayback = Cute.Cri.AudioPlayback.Error(Cute.Cri.SoundGroup.Se);

            // エディタ向けのサウンド情報を更新
            ResetEnvInfo();
        }

        /// <summary>
        /// 背景に対応する環境音の音量を変更する
        /// </summary>
        public void SetBgEnvSoundVolume(float volume, float fadeTime, int fadeCurveId = 0)
        {
            if (!AudioManager.IsPlaySe(_envPlayback))
            {
                if (_timelineData.GetCurrentBlockData(_frameCount).IsSettingBlock)
                {
                    // #70115 準備ブロックでは目的のボリューム値を覚えておく (理由はチケット参照)
                    _envVolume = volume;
                }
                return;
            }

            if (Math.IsFloatEqual(volume, AudioManager.GetVolume(_envPlayback)))
            {
                return;
            }

            if (Math.IsFloatEqual(volume, _envVolume) && AudioManager.IsFadingSe(_envPlayback))
            {
                Debug.LogFormat("[StoryTimelineController] SetVolume is blocked : cue={0}, vol={1}", _envPlayback.CueName, _envVolume);
                return;
            }

            _envVolume = volume;

            // #67474 倍速だとボリューム変更のフェードが後続のClipに影響するので即時反映する
            if (IsHighSpeedMode()) fadeTime = 0f;

            var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
            AudioManager.SetVolume(_envPlayback, volume, fadeTime, fadeCurve);

            // エディタ向けのサウンド情報を更新
            UpdateEnvInfo(_envPlayback.CueName, volume);
        }

        #endregion 環境音

        #region SE
        /// <summary>
        /// Seを再生する (ClipDataから実行)
        /// </summary>
        public void PlaySe(string sheetName, string cueName, float volume, float pan3dAngle,
            float pitch = AudioManager.PITCH_DEFAULT,
            float timeStretch = AudioManager.TIME_STRETCH_DEFAULT,
            float pitchShift = AudioManager.PITCH_SHIFT_DEFAULT,
            float fadeInTime = StoryTimelineSeClipData.STORY_SE_FADEIN_TIME,
            float startTime = StoryTimelineSeClipData.STORY_SE_START_TIME_OFFSET,
            bool loop = false)
        {
            if (IsFinished)
            {
                // 既に終了している場合は再生しない
                return;
            }

            if (IsHighSpeedMode())
            {
                var ignoredCue = MasterDataManager.Instance.masterAudioIgnoredCueOnHighspeed.GetWithCueNameAndCueSheet(cueName, sheetName);
                if (ignoredCue != null)
                {
                    // 無視する対象になっていたら再生しない
                    return;
                }
            }

            _lastSePlayback = AudioManager.Instance.PlaySe(sheetName, cueName,
                pan3dAngle: pan3dAngle, volume: volume, pitch: pitch, timeStretch: timeStretch, pitchShift: pitchShift,
                fadeInTime: fadeInTime, startTime: startTime, loop: loop);

            _sePlaybackList.RemoveAll(playback => !AudioManager.IsPlay(playback));

            if (!_lastSePlayback.IsError)
            {
                _sePlaybackList.Add(_lastSePlayback);
            }

            // エディタ向けのサウンド情報を更新
            UpdateSeInfo(cueName, volume, pan3dAngle);
        }

        /// <summary>
        /// Object制御EventStoryEffectの特殊Seを再生する (ClipDataから実行)
        /// </summary>
        /// <returns>再生できたか</returns>
        public AudioPlayback PlayNonStopSe(string sheetName, string cueName, float volume, float pan3dAngle,
            float pitch = AudioManager.PITCH_DEFAULT, float timeStretch = AudioManager.TIME_STRETCH_DEFAULT, float pitchShift = AudioManager.PITCH_SHIFT_DEFAULT, bool loop = false,
            float fadeInTime = 0f)
        {
            if (IsFinished)
            {
                // 既にTimelineが終了している場合は再生しない
                return AudioPlayback.Error(SoundGroup.Se);
            }

            if (IsHighSpeedMode())
            {
                var ignoredCue = MasterDataManager.Instance.masterAudioIgnoredCueOnHighspeed.GetWithCueNameAndCueSheet(cueName, sheetName);
                if (ignoredCue != null)
                {
                    // 無視する対象になっていたら再生しない
                    return AudioPlayback.Error(SoundGroup.Se);
                }
            }

            _lastSePlayback = AudioManager.Instance.PlaySe(sheetName, cueName,
                pan3dAngle: pan3dAngle, volume: volume, pitch: pitch, timeStretch: timeStretch, pitchShift: pitchShift, loop: loop, fadeInTime: fadeInTime);

            _nonStopSePlaybackList.RemoveAll(playback => !AudioManager.IsPlay(playback));

            if (!_lastSePlayback.IsError)
            {
                _nonStopSePlaybackList.Add(_lastSePlayback);
            }

            // エディタ向けのサウンド情報を更新
            UpdateSeInfo(cueName, volume, pan3dAngle);

            return _lastSePlayback.IsError ? AudioPlayback.Error(SoundGroup.Se) : _lastSePlayback;
        }

        /// <summary>
        /// ループSEの再生
        /// </summary>
        public AudioPlayback PlayLoopSe(int groupIndex, string sheetName, string cueName, float volume, float pan3dAngle,
            float pitch = AudioManager.PITCH_DEFAULT, float timeStretch = AudioManager.TIME_STRETCH_DEFAULT, float pitchShift = AudioManager.PITCH_SHIFT_DEFAULT, bool loop = false,
            float fadeInTime = 0f, float startTime = 0f)
        {
            if (IsFinished)
            {
                // 既にTimelineが終了している場合は再生しない
                return AudioPlayback.Error(SoundGroup.Se);
            }

            if (IsHighSpeedMode())
            {
                var ignoredCue = MasterDataManager.Instance.masterAudioIgnoredCueOnHighspeed.GetWithCueNameAndCueSheet(cueName, sheetName);
                if (ignoredCue != null)
                {
                    // 無視する対象になっていたら再生しない
                    return AudioPlayback.Error(SoundGroup.Se);
                }
            }

            // 同じトラックですでに他のループSEが再生されていたら停止する。
            var loopSePlayback = _loopSePlaybackArray[groupIndex];
            if (!loopSePlayback.IsError && AudioManager.IsPlay(loopSePlayback))
            {
                StopLoopSe(groupIndex, 0f);
            }

            var lastLoopSePlayback = AudioManager.Instance.PlaySe(sheetName, cueName, pan3dAngle: pan3dAngle, volume: volume,
                pitch: pitch, timeStretch: timeStretch, pitchShift: pitchShift, loop: loop, fadeInTime: fadeInTime, startTime: startTime);

            if (!lastLoopSePlayback.IsError)
            {
                _loopSePlaybackArray[groupIndex] = lastLoopSePlayback;
            }

            // エディタ向けのサウンド情報を更新
            UpdateSeInfo(cueName, volume, pan3dAngle);

            return _loopSePlaybackArray[groupIndex];
        }

        /// <summary>
        /// LoopSEのAudioPlaybackを取得
        /// </summary>
        public AudioPlayback GetLoopSeAudioPlayback(int groupIndex)
        {
            return _loopSePlaybackArray[groupIndex];
        }

        /// <summary>
        /// Seを停止する (ClipDataから実行)
        /// </summary>
        public void StopSe(string sheetName, string cueName, float fadeoutTime, int fadeCurveId = 0)
        {
            var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
            AudioManager.Instance.StopSe(sheetName, cueName, fadeoutTime, fadeCurve: fadeCurve);

            // エディタ向けのサウンド情報を更新
            ResetSeInfo();
        }

        /// <summary>
        /// ループSeを停止する
        /// </summary>
        public void StopLoopSe(int groupIndex, float fadeoutTime, int fadeCurveId = 0)
        {
            // 指定のgroupIndexのSeが再生されていたら止める
            var loopSePlayback = _loopSePlaybackArray[groupIndex];
            if (!loopSePlayback.IsError && AudioManager.IsPlay(loopSePlayback))
            {
                var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
                AudioManager.Instance.StopSe(loopSePlayback, fadeoutTime, fadeCurve: fadeCurve);
            }

            // エディタ向けのサウンド情報を更新
            ResetSeInfo();
        }

        /// <summary>
        /// 環境音、SE停止 (FinalizeViewや育成パラメータ演出後に実行)
        /// </summary>
        public void StopAudio(float seFadeOutTime = 0f, float envFadeOutTime = 0f)
        {
            StopAudioSe(seFadeOutTime);
            StopAudioEnv(envFadeOutTime);
            StopAudioNonStopSe(seFadeOutTime);
            StopAudioLoopSe(seFadeOutTime);
        }

        public void StopAudioSe(float fadeOutTime = 0.0f, FadeCurve fadeCurve = FadeCurve.Linear)
        {
            // SE
            foreach (var playback in _sePlaybackList)
            {
                AudioManager.Stop(playback, fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            }
            _sePlaybackList.Clear();

            // エディタ向けのサウンド情報を更新
            ResetSeInfo();
        }

        public void StopAudioEnv(float fadeOutTime = 0.0f, FadeCurve fadeCurve = FadeCurve.Linear)
        {
            // 環境音
            AudioManager.Stop(_envPlayback, fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);

            // エディタ向けのサウンド情報を更新
            ResetEnvInfo();
        }

        public void StopAudioNonStopSe(float fadeOutTime = 0.0f, FadeCurve fadeCurve = FadeCurve.Linear)
        {
            foreach (var playback in _nonStopSePlaybackList)
            {
                AudioManager.Stop(playback, fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            }
            _nonStopSePlaybackList.Clear();

            // エディタ向けのサウンド情報を更新
            ResetEnvInfo();
        }

        public void StopAudioLoopSe(float fadeOutTime = 0.0f, FadeCurve fadeCurve = FadeCurve.Linear)
        {
            foreach (var playback in _loopSePlaybackArray)
            {
                AudioManager.Stop(playback, fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            }

            // エディタ向けのサウンド情報を更新
            ResetSeInfo();
        }

        /// <summary>
        /// ループSE用変数の初期化
        /// </summary>
        private void InitializeAudioLoopSe()
        {
            for (int i = 0; i < _loopSePlaybackArray.Length; i++)
            {
                _loopSePlaybackArray[i] = AudioPlayback.Error(SoundGroup.Se);
            }
        }
        public void ClearAudioLoopSe()
        {
            // ループSE用変数の初期化
            InitializeAudioLoopSe();

            SoundController?.Cleanup();
        }

        /// <summary>
        /// BGM,SE,環境音を停止
        /// </summary>
        /// <param name="fadeOutTime"></param>
        public void StopAllAudio(float fadeOutTime = 0.0f, FadeCurve fadeCurve = FadeCurve.Linear)
        {
            StopAudioSe(fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            StopAudioEnv(fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            StopAudioNonStopSe(fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            StopAudioLoopSe(fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            StopBgmAll(fadeOutTime: fadeOutTime, fadeCurve: fadeCurve);
            // 音量設定によりフェードアウト。解除はResetBusParamSetに任せる。
            var paramName = (fadeOutTime <= StoryUtil.FADE_DURATION_SKIP_STORY) ? BusParamSet.STORY_WHITE_OUT_SKIP : BusParamSet.STORY_WHITE_OUT_NORMAL;
            SetBusParam(paramName, true);
        }

        /// <summary>
        /// BGM,SE,環境音を一時停止
        /// </summary>
        public void PauseAllAudio()
        {
            AudioManager.Pause(_envPlayback);
            foreach (var playback in _sePlaybackList)
            {
                AudioManager.Pause(playback);
            }
            foreach (var playback in _nonStopSePlaybackList)
            {
                AudioManager.Pause(playback);
            }
            foreach (var playback in _loopSePlaybackArray)
            {
                AudioManager.Pause(playback);
            }
            for (var i = 0; i < StoryTimelineBlockData.DSP_BGM_MAX; i++)
            {
                var bgmPlaybackIndex = GetBgmPlaybackIndex(i);
                ref var bgmPlayback = ref _bgmPlaybackArray[bgmPlaybackIndex];
                AudioManager.Pause(bgmPlayback);
            }
        }

        /// <summary>
        /// BGM,SE,環境音を再開
        /// </summary>
        public void ResumeAllAudio()
        {
            AudioManager.Resume(_envPlayback);
            foreach (var playback in _sePlaybackList)
            {
                AudioManager.Resume(playback);
            }
            foreach (var playback in _nonStopSePlaybackList)
            {
                AudioManager.Resume(playback);
            }
            foreach (var playback in _loopSePlaybackArray)
            {
                AudioManager.Resume(playback);
            }
            for (var i = 0; i < StoryTimelineBlockData.DSP_BGM_MAX; i++)
            {
                var bgmPlaybackIndex = GetBgmPlaybackIndex(i);
                ref var bgmPlayback = ref _bgmPlaybackArray[bgmPlaybackIndex];
                AudioManager.Resume(bgmPlayback);
            }
        }

        /// <summary>
        /// ループSEの音量を設定
        /// </summary>
        public void SetLoopSeVolume(int groupIndex, float volume, float fadeTime, int fadeCurveId)
        {
            AudioPlayback playback = _loopSePlaybackArray[groupIndex];
            if (playback.IsError || !AudioManager.IsPlay(playback))
            {
                return;
            }

            var fadeCurve = FADE_CURVE_DICT[fadeCurveId];
            AudioManager.SetVolume(playback, volume, fadeTime, fadeCurve);
        }

        #region エディタ用
#if CYG_DEBUG && UNITY_EDITOR
        public void SetSeVolume(float volume)
        {
            AudioManager.SetVolume(_lastSePlayback, volume);
        }

        public void SetSePan3dAngle(float angle)
        {
            AudioManager.SetPan3dAngle(_lastSePlayback, angle);
        }
        
        public void SetupLipSyncData()
        {
            _timelineData.SetupLipSyncData();
        }
#endif
        #endregion エディタ用

        #endregion SE

        #region BusParamSet
        /// <summary>
        /// BusParamSetを設定。
        /// </summary>
        public void SetBusParam(string paramName, bool isEnable, int overrideEffectPriority = -1)
        {
            if (_setBusParamSetNameList.Contains(paramName))
            {
                if (!isEnable)
                {
                    _setBusParamSetNameList.Remove(paramName);
                }
            }
            else
            {
                if (isEnable)
                {
                    _setBusParamSetNameList.Add(paramName);
                }
            }
            AudioManager.Instance.SetBusParam(paramName, isEnable, overrideEffectPriority);

            // エディタ向けのサウンド情報を更新
            UpdateBusParamInfo(paramName, isEnable);
        }
        /// <summary>
        /// BusParamSetの設定を解除。
        /// </summary>
        public void ResetBusParamSet()
        {
            for (int index = 0, count = _setBusParamSetNameList.Count; index < count; ++index)
            {
                AudioManager.Instance.SetBusParam(_setBusParamSetNameList[index], false);
            }
            _setBusParamSetNameList.Clear();
            LastBgBusParamSetName = "";
        }
        #endregion BusParamSet

        #endregion サウンド

#if UNITY_EDITOR && CYG_DEBUG

        #region StoryStillで使用される定数

        //StoryStillClipの内容をStoryTimelineControllerが保持する理由
        //StartPositionDropdownFilter,EndPositionDropdownFilterなどをstatic変数として保持する必要があるが
        //ScriptableObject内の変数を静的変数として定義すると、静的変数の参照が残り、メモリが解放されない
        //そのため、ScriptableObjectではなくシーン遷移によって破棄されるStoryTimelineControllerが持っておく

        private bool _isPositionFilterLoaded = false;
        public StoryTimelineCameraClipData.SimpleAnimation<StoryTimelineStillAnimationClipData>.CameraPositionOriginFilter StartPositionDropdownFilter
                    = new StoryTimelineCameraClipData.SimpleAnimation<StoryTimelineStillAnimationClipData>.CameraPositionOriginFilter();
        public StoryTimelineCameraClipData.SimpleAnimation<StoryTimelineStillAnimationClipData>.CameraPositionOriginFilter EndPositionDropdownFilter
                    = new StoryTimelineCameraClipData.SimpleAnimation<StoryTimelineStillAnimationClipData>.CameraPositionOriginFilter();

        /// <summary>
        /// プルダウン入力の選択肢に使うキャラ名を取得するコールバック
        /// </summary>
        private string GetCharaNameByCameraPositionOrigin(StoryTimelineCameraClipData.CameraPositionOrigin origin)
        {
            return origin.Text();
        }

        public void SetupPositionDropdownFilter()
        {
            if (_isPositionFilterLoaded)
            {
                return;
            }

            StartPositionDropdownFilter.GetCharaNameFunc = GetCharaNameByCameraPositionOrigin;
            EndPositionDropdownFilter.GetCharaNameFunc = GetCharaNameByCameraPositionOrigin;

            _isPositionFilterLoaded = true;
        }

        public bool IsPositionFilterLoaded()
        {
            return _isPositionFilterLoaded;
        }

        #endregion

#endif

        #endregion メソッド

        

    }
}
