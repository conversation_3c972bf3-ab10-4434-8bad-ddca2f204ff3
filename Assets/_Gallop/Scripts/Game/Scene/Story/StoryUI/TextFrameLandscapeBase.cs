using UnityEngine;
using static Gallop.StaticVariableDefine.Story.TextFrameLandscapeBase;

namespace Gallop
{
    /// <summary>
    /// メインストーリー横持ちのTextFrameの下地
    /// </summary>
    [AddComponentMenu("")]
    public class TextFrameLandscapeBase : MonoBehaviour
    {
        private const float RECT_HEIGHT_PROLOGUE = 660f;

        [SerializeField]
        protected Canvas _canvas = null;

        [SerializeField]
        protected RectTransform _rectTransform = null;

        private bool _enabled = false;

        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize(bool isVisible, bool isPrologue)
        {
            // 初期設定を覚えておく
            _enabled = isVisible;

            SetVisible(isVisible);

            _canvas.sortingOrder = (int)StoryViewController.SortingOrder.TextFrame - 1;

            if (isPrologue)
            {
                // 画面の縦幅とSafeAreaの縦幅の差を求める
                float diff = UIManager.Instance.GetSafeAreaSourceScreenSize().y - UIManager.Instance.GetSafeArea().height;

                // 基本の縦幅に差の半分を加算して下地の高さを算出
                float height = RECT_HEIGHT_PROLOGUE + (diff / 2f);
                _rectTransform.sizeDelta = new Vector2(0, height);
            }
            else
            {
                _rectTransform.sizeDelta = DEFAULT_SIZE;
            }
        }

        /// <summary>
        /// 表示を切り替える
        /// </summary>
        public void SetVisible(bool isVisible)
        {
            // 初期化で非表示になった場合はずっと非表示となる
            gameObject.SetActive(_enabled && isVisible);
        }
    }
}
