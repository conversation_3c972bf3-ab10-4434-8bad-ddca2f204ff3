using System;
using UnityEngine;

namespace Gallop.StoryStill
{
    [AddComponentMenu("")]
    public class StoryStillCamera : MonoBehaviour
    {
        [SerializeField] private Camera _camera;
        public Camera Camera => _camera; // 撮影用

        public Transform CameraTransform => _camera.transform;

        public RenderTexture TargetTexture
        {
            get => _camera.targetTexture;
            set => _camera.targetTexture = value;
        }

        /// <summary>
        /// 最大カメラサイズ（これを超えるとスチル絵が切れてしまう）
        /// </summary>
        public float MaxOrthographicSize
        {
            get => _maxSize;
            set
            {
                _maxSize = value;
                UpdateSize();
            }
        }
        private float _maxSize = StoryStillCameraExtension.INITIAL_ORTHOGRAPHIC_SIZE_LEGACY;

        /// <summary>
        /// カメラサイズ（StillCameraClipDataのFovアニメーションで設定する用）
        /// </summary>
        public float OrthographicSize
        {
            get => _size;
            set
            {
                _size = value;
                UpdateSize();
            }
        }
        private float _size = StoryStillCameraExtension.INITIAL_ORTHOGRAPHIC_SIZE_LEGACY;

#if CYG_DEBUG && UNITY_EDITOR
        public float OrthographicSizeFactorForCapture
        {
            get => _sizeFactorForCapture;
            set
            {
                _sizeFactorForCapture = value;
                UpdateSize();
            }
        }
        private float _sizeFactorForCapture = 1f;
#endif

        /// <summary>
        /// カメラのサイズを更新する
        /// </summary>
        private void UpdateSize()
        {
            // _sizeはStillCameraClipDataのFovアニメーションにより変動する
            // _maxSizeは端末アスペクト比によって決まる
            // アニメーションでカメラサイズを小さくすることは可能だが最大値を超えることはできない
            _camera.orthographicSize = Mathf.Min(_size, _maxSize);
#if CYG_DEBUG && UNITY_EDITOR
            _camera.orthographicSize *= _sizeFactorForCapture;
#endif
        }
    }

    public static class StoryStillCameraExtension
    {
        #region 定数

        /// <summary>16:9端末でイラストチーム想定の見た目となるカメラサイズ</summary>
        /// <remarks>
        /// 19.5:9端末もこのサイズを使う
        /// </remarks>
        public const float ORTHOGRAPHIC_SIZE = 3.6f;

        /// <summary>4:3端末でイラストチーム想定の見た目となるカメラサイズ</summary>
        /// <remarks>
        /// 4:3端末で見える横幅と16:9端末で見える横幅が同じになるので
        /// HORIZONTAL_SIZE = ORTHOGRAPHIC_SIZE_4_3 * ASPECT_RATIO_4_3
        ///                 = ORTHOGRAPHIC_SIZE_16_9 * ASPECT_RATIO_16_9
        /// により
        /// ORTHOGRAPHIC_SIZE_4_3 = ORTHOGRAPHIC_SIZE_16_9 * ASPECT_RATIO_16_9 / ASPECT_RATIO_4_3
        /// という関係になっている
        /// </remarks>
        private const float ORTHOGRAPHIC_SIZE_4_3 = ORTHOGRAPHIC_SIZE
                                                    * GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_16_9
                                                    / GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_4_3;

        /// <summary>21:9端末でイラストチーム想定の見た目となるカメラサイズ</summary>
        /// <remarks>
        /// 算出方法は4:3と同じで、19.5:9の時と見える横幅が同じという関係を用いる
        /// </remarks>
        private const float ORTHOGRAPHIC_SIZE_21_9 = ORTHOGRAPHIC_SIZE
                                                     * GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL
                                                     / GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_21_9;

        /// <summary>
        /// 初期カメラサイズ（4:3でスチル絵を全画面表示する時のサイズに一致させておく）
        /// </summary>
        private const float INITIAL_ORTHOGRAPHIC_SIZE = ORTHOGRAPHIC_SIZE_4_3;

        /// <summary>
        /// 古い計算方法における初期カメラサイズ（既存会話の見た目が変わらないようにするための定数）
        /// </summary>
        public const float INITIAL_ORTHOGRAPHIC_SIZE_LEGACY = 4f;

        #endregion 定数

        public static void UpdateCameraSizeByDisplayMode(this StoryStillCamera camera, bool isLegacyFittingMode)
        {
            switch (StoryTimelineController.CurrentDisplayMode)
            {
                case StoryTimelineController.DisplayMode.SingleMode:
                    // SingleModeは非対応
                    break;
                case StoryTimelineController.DisplayMode.SingleModePrologue:
                    UpdateCameraSizeSingleModeTutorial(camera);
                    break;
                case StoryTimelineController.DisplayMode.Portrait:
                    UpdateCameraSizePortrait(camera, isLegacyFittingMode);
                    break;
                case StoryTimelineController.DisplayMode.Landscape:
                    UpdateCameraSizeLandscape(camera, isLegacyFittingMode);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        /// <summary>
        /// 端末アスペクト比に応じて最大カメラサイズを設定（スチル絵を全画面で表示する時のサイズを設定する）
        /// </summary>
        public static void UpdateCameraSizeByAspect(this StoryStillCamera camera, float width, float height, bool isLegacyFittingMode)
        {
            // StoryStillプレビュー用の特殊解像度対応
            if (Math.IsFloatEqual(width, 1560f) && Math.IsFloatEqual(height, 960f))
            {
                const float ORIGINAL_SIZE = 4.8f;
                UpdateMaxCameraSize(camera, ORIGINAL_SIZE);
                return;
            }

            // #81404 スチル絵のEtoE対応がイラストチーム想定のものと異なる問題への対応
            // カメラサイズの計算方法を切り替える
            var size = isLegacyFittingMode
                ? GetMaxCameraSizeLegacyFitting(width, height)  // 既存の会話イベントはこちら
                : GetMaxCameraSize(width, height);              // イラストチーム想定の見た目を担保

            UpdateMaxCameraSize(camera, size);
        }

        /// <summary>
        /// リリース当初のカメラサイズ計算（古い計算方法）
        /// </summary>
        private static float GetMaxCameraSizeLegacyFitting(float width, float height)
        {
            // イラストチームの想定とは異なる物だったので新規会話イベントでは使わない
            // リリース済みのスチル絵の見た目が変化しないように既存イベントのみこちらを使う

            const float ASPECT_4_3 = 4f / 3f;
            const float ASPECT_16_9 = 16f / 9f;
            const float ASPECT_SPINE = ASPECT_16_9;
            var deviceAspect = width / height;
            // 16:9 以上は横合わせ
            if (deviceAspect >= ASPECT_SPINE)
            {
                const float TARGET_ORTHOGRAPHIC_WIDTH = 7.8f;
                return TARGET_ORTHOGRAPHIC_WIDTH / deviceAspect;
            }
            // 16:9 未満は縦合わせ
            else
            {
                const float MIN_ASPECT = ASPECT_4_3;
                const float MAX_ASPECT = ASPECT_SPINE;
                const float MIN_SIZE = 4.36f; // 16:9の時にSpineが全画面になるサイズ
                const float MAX_SIZE = 4.76f; // 04:3の時にSpineが全画面になるサイズ
                var t = (deviceAspect - MIN_ASPECT) / (MAX_ASPECT - MIN_ASPECT);
                return Mathf.Lerp(MAX_SIZE, MIN_SIZE, t);
            }
        }

        /// <summary>
        /// カメラサイズ計算（イラストチーム想定の見た目を担保）
        /// </summary>
        private static float GetMaxCameraSize(float width, float height)
        {
            // 16:9の見た目を基準として, 4:3なら見える範囲が縦に伸びて19.5:9なら見える範囲が横に伸びる
            // 縦長の端末も横長の端末も見える範囲が基準より広がる事になる

            var deviceAspect = width / height;
            // 21:9 以上
            if (deviceAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_21_9)
            {
                // 4:3端末、21:9端末でのカメラサイズと同様に計算する。見える横幅が同じになるように調整するので
                // HORIZONTAL_SIZE = ORTHOGRAPHIC_SIZE(19.5:9) * ASPECT_RATIO(19.5:9)
                //                 = ORTHOGRAPHIC_SIZE(deviceAspect) * ASPECT_RATIO(deviceAspect)
                // より
                // ORTHOGRAPHIC_SIZE(deviceAspect) = ORTHOGRAPHIC_SIZE(19.5:9) * ASPECT_RATIO(19.5:9) / ASPECT_RATIO(deviceAspect)
                return (ORTHOGRAPHIC_SIZE * GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL) / deviceAspect;
            }
            // 19.5:9 以上
            if (deviceAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL)
            {
                // 21:9は16:9の状態から横に見える範囲が広がる
                // ただしスチル絵の横幅は19.5:9の分までしか作られていないのでそのままだと左右が切れてしまう
                // カメラサイズを小さくして、横幅が19.5:9相当になるようにする（結果的に縦方向には見える範囲が縮む）
                var t = Mathf.InverseLerp(GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_21_9, GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL, deviceAspect);
                return Mathf.Lerp(ORTHOGRAPHIC_SIZE_21_9, ORTHOGRAPHIC_SIZE, t);

            }
            // 16:9 以上
            else if (deviceAspect > GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_16_9)
            {
                // 19.5:9は16:9の状態から横に見える範囲が広がる
                // 横には広がるが縦には変化しないのでカメラサイズはそのままで良い
                return ORTHOGRAPHIC_SIZE;
            }
            // 16:9 未満
            {
                // そのままだと4:3は16:9の状態から縦に見える範囲が狭くなる
                // カメラサイズを大きくして、横幅が16:9相当になるようにする（結果的に縦方向には見える範囲が伸びる）
                var t = Mathf.InverseLerp(GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_4_3, GallopUtil.BASE_SCREEN_ASPECT_RATIO_HORIZONTAL_16_9, deviceAspect);
                return Mathf.Lerp(ORTHOGRAPHIC_SIZE_4_3, ORTHOGRAPHIC_SIZE, t);
            }
        }

        private static void UpdateCameraSizePortrait(this StoryStillCamera camera, bool isLegacyFittingMode)
        {
            const float PORTRAIT_SIZE = 4.39f;
            var size = isLegacyFittingMode
                ? PORTRAIT_SIZE         // 既存の会話イベントはこちら
                : ORTHOGRAPHIC_SIZE;    // イラストチーム想定の見た目を担保の16：9の場合のサイズ

            UpdateMaxCameraSize(camera, size);
        }

        private static void UpdateCameraSizeSingleModeTutorial(this StoryStillCamera camera)
        {
            const float PORTRAIT_SIZE = 4.39f;
            UpdateMaxCameraSize(camera, PORTRAIT_SIZE);
        }

        private static void UpdateCameraSizeLandscape(this StoryStillCamera camera, bool isLegacyFittingMode)
        {
            UpdateCameraSizeByAspect(camera, Screen.Width, Screen.Height, isLegacyFittingMode);
            AdjustCameraSizeToMax(camera);
        }

        private static void UpdateMaxCameraSize(this StoryStillCamera camera, float size)
        {
            camera.MaxOrthographicSize = size;
        }

        /// <summary>
        /// カメラサイズを最大にする
        /// </summary>
        /// <param name="camera"></param>
        public static void AdjustCameraSizeToMax(this StoryStillCamera camera)
        {
            camera.OrthographicSize = camera.MaxOrthographicSize;
        }

        /// <summary>
        /// カメラの初期サイズを設定（初期化時に一回だけ実行）
        /// </summary>
        public static void InitializeCameraSize(this StoryStillCamera camera, bool isLegacyFittingMode)
        {
            var size = isLegacyFittingMode
                ? INITIAL_ORTHOGRAPHIC_SIZE_LEGACY
                : INITIAL_ORTHOGRAPHIC_SIZE;

            camera.OrthographicSize = size;
            camera.MaxOrthographicSize = size;
        }
    }
}