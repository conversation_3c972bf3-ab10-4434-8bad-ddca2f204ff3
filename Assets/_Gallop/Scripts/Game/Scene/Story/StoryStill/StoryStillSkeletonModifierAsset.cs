using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Spine;
using Spine.Unity;
using static Gallop.StaticVariableDefine.Story.StoryStillSkeletonModifierAsset;

namespace Gallop.StoryStill
{
    [CreateAssetMenu(menuName = "ScriptableObject/Story/Spine/SkeletonModifierAsset")]
    [AddComponentMenu("")]
    public class StoryStillSkeletonModifierAsset : SkeletonDataModifierAsset
    {
        #region GallopModification

        public Material fillMaterialTemplate;

        #endregion

        public Material multiplyMaterialTemplate;
        public Material screenMaterialTemplate;
        public Material additiveMaterialTemplate;

        public bool applyAdditiveMaterial;

        public override void Apply(SkeletonData skeletonData)
        {
            ApplyMaterials(skeletonData, fillMaterialTemplate, multiplyMaterialTemplate, screenMaterialTemplate,
                additiveMaterialTemplate, applyAdditiveMaterial);
        }

        public static void ApplyMaterials(SkeletonData skeletonData, Material fillTemplate, Material multiplyTemplate,
            Material screenTemplate,
            Material additiveTemplate, bool includeAdditiveSlots)
        {
            if (skeletonData == null) throw new ArgumentNullException("skeletonData");

            #region GallopModification

            var useStencil = skeletonData.Slots.Any(slot =>
                slot.Name.Equals("Mask=True", StringComparison.InvariantCultureIgnoreCase));
            var stencilOpValue = useStencil
                ? (int) UnityEngine.Rendering.CompareFunction.Less
                : (int) UnityEngine.Rendering.CompareFunction.Always;

            #endregion

            using (var materialCache = new AtlasMaterialCache())
            {
                var attachmentBuffer = new List<Attachment>();
                var slotsItems = skeletonData.Slots.Items;
                for (int i = 0, slotCount = skeletonData.Slots.Count; i < slotCount; i++)
                {
                    var slot = slotsItems[i];

                    #region GallopModification

//                    if (slot.BlendMode == BlendMode.Normal) continue;

                    #endregion

                    if (!includeAdditiveSlots && slot.BlendMode == BlendMode.Additive) continue;

                    attachmentBuffer.Clear();
                    foreach (var skin in skeletonData.Skins)
                        skin.FindAttachmentsForSlot(i, attachmentBuffer);

                    Material templateMaterial = null;
                    switch (slot.BlendMode)
                    {
                        case BlendMode.Multiply:
                            templateMaterial = multiplyTemplate;
                            break;
                        case BlendMode.Screen:
                            templateMaterial = screenTemplate;
                            break;
                        case BlendMode.Additive:
                            templateMaterial = additiveTemplate;
                            break;

                        #region GallopModification

                        default:
                            templateMaterial = fillTemplate;
                            break;

                        #endregion
                    }

                    if (templateMaterial == null) continue;

                    #region GallopModification

//                    foreach (var attachment in attachmentBuffer) {
//                        var renderableAttachment = attachment as IHasRendererObject;
//                        if (renderableAttachment != null) {
//                            renderableAttachment.RendererObject = materialCache.CloneAtlasRegionWithMaterial((AtlasRegion)renderableAttachment.RendererObject, templateMaterial);
//                        }

                    foreach (var attachment in attachmentBuffer)
                    {
                        if (!(attachment is IHasRendererObject renderableAttachment))
                            continue;

                        var atlasRegion =
                            materialCache.CloneAtlasRegionWithMaterial(
                                (AtlasRegion) renderableAttachment.RendererObject, templateMaterial);
                        if (atlasRegion.page.rendererObject is Material material)
                        {
                            material.SetFloat(STENCIL_COMP, stencilOpValue);
                        }

                        renderableAttachment.RendererObject = atlasRegion;
                    }

                    #endregion
                }
            }
        }

        class AtlasMaterialCache : IDisposable
        {
            readonly Dictionary<KeyValuePair<AtlasPage, Material>, AtlasPage> cache =
                new Dictionary<KeyValuePair<AtlasPage, Material>, AtlasPage>();

            /// <summary>Creates a clone of an AtlasRegion that uses different Material settings, while retaining the original texture.</summary>
            public AtlasRegion CloneAtlasRegionWithMaterial(AtlasRegion originalRegion, Material materialTemplate)
            {
                var newRegion = originalRegion.Clone();
                newRegion.page = GetAtlasPageWithMaterial(originalRegion.page, materialTemplate);
                return newRegion;
            }

            AtlasPage GetAtlasPageWithMaterial(AtlasPage originalPage, Material materialTemplate)
            {
                if (originalPage == null) throw new ArgumentNullException("originalPage");

                AtlasPage newPage = null;
                var key = new KeyValuePair<AtlasPage, Material>(originalPage, materialTemplate);
                cache.TryGetValue(key, out newPage);

                if (newPage == null)
                {
                    newPage = originalPage.Clone();
                    var originalMaterial = originalPage.rendererObject as Material;
                    newPage.rendererObject = new Material(materialTemplate)
                    {
                        name = originalMaterial.name + " " + materialTemplate.name,
                        mainTexture = originalMaterial.mainTexture
                    };
                    cache.Add(key, newPage);
                }

                return newPage;
            }

            public void Dispose()
            {
                cache.Clear();
            }
        }
    }
}