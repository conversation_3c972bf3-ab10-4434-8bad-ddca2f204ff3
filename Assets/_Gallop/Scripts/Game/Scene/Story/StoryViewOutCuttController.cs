using System.Collections.Generic;
using System.Linq;
using Gallop.CutIn.Cutt;
using Gallop.SingleModeTrainingCutHelperExtension;
using UnityEngine;
using static Gallop.StaticVariableDefine.Story.StoryView;

namespace Gallop
{
    /// <summary>
    /// StoryViewのうち、OutCutt用の処理をまとめたもの
    /// </summary>
    public partial class StoryViewController
    {
        #region OutCutt

        /// <summary>
        /// カットの情報
        /// </summary>
        public sealed class OutCutInfo
        {
            public GameObject Parent;
            public RaceSkillCutInHelper CutInHelper;
            public SingleModeTrainingCutInHelper.Context CutInContext;
        }

        /// <summary>
        /// カットの情報を再生開始時のフレーム値をキーとして管理しているDictionary
        /// </summary>
        private readonly Dictionary<string, OutCutInfo> _outCutInfoDictionary = new Dictionary<string, OutCutInfo>();

        /// <summary>
        /// 現在再生中のカットの親オブジェクト
        /// </summary>
        private Transform CurrentOutCutInParent => _outCutInfoDictionary[_currentCutIdName].Parent.transform;

        /// <summary>
        /// 現在再生中のカットのHelper情報
        /// </summary>
        private RaceSkillCutInHelper CurrentOutCutInHelper => _outCutInfoDictionary[_currentCutIdName].CutInHelper;

        /// <summary>
        /// 現在再生中のカットのContext情報
        /// </summary>
        private SingleModeTrainingCutInHelper.Context CurrentOutCutInContext => _outCutInfoDictionary[_currentCutIdName].CutInContext;

        /// <summary>
        /// FrameBuffer
        /// OutカットはFrameBufferが必要になる
        /// 複数Outカットを再生する場合も1つのFrameBufferを使いまわすので、解放はストーリー終わったタイミングになる
        /// </summary>
        private GallopFrameBuffer _frameBuffer;

        /// <summary>
        /// StoryTimelineからカットの割込み再生が呼び出されたときのコールバック
        /// </summary>
        private void OnStartOutCutt(StoryTimelineTrainingCuttClipData clipData)
        {
            //カットのタイプを指定
            IsPrologueCuttType = clipData.IsPrologueCutt;

            // ブロックをまたいで再生しているときにタップ待ちが発生しないように、タップ待ち予約を解除する
            TimelineController.StopWaiting();

            // 会話側のオブジェクトを非表示にする
            _controller.SetStoryObjectActive(false);

            // 会話UIを全て非表示にする
            SetUIVisibleForTrainingCutt(false);

            // 環境音を停止する
            TimelineController.StopAudioEnv(AudioManager.BGM_FADEOUT_TIME);

            // カット再生中フラグを立てる
            _trainingCuttController.IsPlayingTrainingCutt = true;

            // 時刻同期のため、再生開始時刻を記録
            _trainingCuttController.StartTime = clipData.FixedStartFrame * GameDefine.BASE_FPS_TIME;

            // 先頭待機時間をクリップからコピー
            _trainingCuttController.DelayTime = clipData.DelayTime;

            // 再生終了時間をクリップからコピー
            _trainingCuttController.TotalTime = clipData.ClipLength * GameDefine.BASE_FPS_TIME;

            //メソッドが呼び出されるよりも前に再生されているものがあれば、
            //再生状態にかかわらず親オブジェクトを念のために非表示にする(再生を停止させる)
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName))
            {
                CurrentOutCutInParent.SetActiveWithCheck(false);
            }

            //現在再生中のカット名
            _currentCutIdName = clipData.UniqueCutIdName;

            var isExistKey = _outCutInfoDictionary.ContainsKey(_currentCutIdName);
            var cutInHelper = isExistKey ? CurrentOutCutInHelper : null;
            var cutInContext = isExistKey ? CurrentOutCutInContext : null;

            //Preloadで登録された中から見つからなければ終了
            if (cutInContext == null || cutInHelper == null)
            {
                return;
            }

            //カットはここで横画面対応が入っている(StoryViewTrainingCuttControllerのOnStartTrainingCutt)
            //Outカットでは今のところ横画面にならないので対応しない

            //FrameBuffer＞＞UIにコピーするイベントをOnにする
            if (_frameBuffer != null)
            {
                _frameBuffer.AddFrameBufferToUI();
            }

            PlayOutCutt(cutInHelper);

            // #142555対応: カット開始時から時間が経過している場合があるので経過時間を設定しておく。
            _trainingCuttController.OnSetCurrentTime?.Invoke(TimelineController.CurrentTime - _trainingCuttController.StartTime);
        }

        /// <summary>
        /// StoryTimelineからカットの割込み再生終了が呼び出されたときのコールバック
        /// </summary>
        private void OnStopOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName))
            {
                //カットはここで横画面対応が入っている(StoryViewTrainingCuttControllerのOnStopTrainingCutt)
                //Outカットでは今のところ横画面にならないので対応しない
            }
            else
            {
                return;
            }

            //フラグを落とす
            IsPrologueCuttType = false;

            StopOutCutt();

            _isTrainingCuttSkip = false;

            // 会話UIを全て表示する
            SetUIVisibleForTrainingCutt(true);

            // DirectionalLight設定を復帰させる
            var block = _timelineData.GetCurrentBlockData(TimelineController.FrameCount);
            // もともと適用してあった背景を適用
            if (block.BgTrack.TryGetLatestClip(out StoryTimelineBgClipData bgClip))
            {
                bgClip.StartClipData(TimelineController.FrameCount, null, false);
            }
            // もともと適用してあった背景のオフセットを適用
            if (block.BgTrack.StoryTimelineBgOffsetTrackData != null)
            {
                var bgOffsetTrack = block.BgTrack.StoryTimelineBgOffsetTrackData;
                if (bgOffsetTrack.TryGetLatestClip(out StoryTimelineBgOffsetClipData bgOffsetClip))
                {
                    bgOffsetClip.StartClipData(TimelineController.FrameCount, null, false);
                }
            }

            // 136957 ここでパラメータ上書きをすると、タップスキップ時に会話側の反映が初期化されてしまうためここでは行わない。
            // 会話側のクリップ処理 -> Cutt処理(更新->本終了処理)で会話側のクリップ情報が上書きされる。
            TimelineController.UpdateEnvParam(false);

            // 環境音を再開する
            TimelineController.PlayReservedBgEnvSound();

            // 会話側のオブジェクトを表示する
            _controller.SetStoryObjectActive(true);

            // 倍速設定を有効化
            TimelineController.UpdateTimeScaleByHispeedType();

            // カット再生中フラグを折る
            _trainingCuttController.IsPlayingTrainingCutt = false;

            // カット再生中に立って待機フラグを下ろす
            TimelineController.StopWaiting();

            // 倍速中にUpdateするフレームを設定
            var textClip = block.TextTrack.ClipList[0] as StoryTimelineTextClipData;
            TimelineController.SetHighSpeedFrameCount(textClip);

            // StopWaitingでリセットされるのでタッチ待ち時間を再設定
#if CYG_DEBUG && UNITY_EDITOR
            if (TimelineController.IsEditing)
            {
                // Timelineエディタで編集中は何もしない
            }
            else
#endif
            {
                TimelineController.SetFrameCountForWaiting(textClip.FixedEndFrame);
            }

            //FrameBuffer＞＞UIのイベントをOffにする
            if (_frameBuffer != null)
            {
                _frameBuffer.RemoveFrameBufferToUI();
            }
        }

        /// <summary>
        /// カットの再生用意
        /// 0フレーム状態で待機させる
        /// </summary>
        private void OnPreloadOutCutt(StoryTimelineTrainingCuttClipData clipData)
        {
            var contextList = clipData
                .CutInContextList
                .Cast<SingleModeTrainingCutInHelper.Context>()
                .ToList();
            var uniqueCutIdName = clipData.UniqueCutIdName;

            PreloadOutCutt(uniqueCutIdName, contextList[0]);
        }


        /// <summary>
        /// カットの再生用意
        /// 0フレーム状態で待機させる
        /// ※1ストーリー内で複数回カットシーンが再生される場合は、ここに何度も来るので２回初期化されないように注意
        /// </summary>
        private void PreloadOutCutt(string uniqueCutIdName, SingleModeTrainingCutInHelper.Context context)
        {
            OutCutInfo cutInfo = null;
            bool isFindCutInfo = _outCutInfoDictionary.TryGetValue(uniqueCutIdName, out cutInfo);
            var cutInHelper = isFindCutInfo ? cutInfo.CutInHelper : null;

            // Editorなどで複数回Preloadが呼ばれる場合もあるので、オブジェクトを破棄する
            if (cutInHelper != null)
            {
                //148685 カット止める際に、環境音と登録済みのコールバックも外す
                cutInHelper.RequestStopEnvSeOnEndCutIn();
                cutInHelper.CleanupPlaying();
                cutInHelper.Release();
            }

            // まとめて表示制御を行うため、カット用の親を用意
            if (_trainingCuttRoot == null)
            {
                _trainingCuttRoot = new GameObject("TrainingCutRoot").transform;
                _trainingCuttRoot.gameObject.SetActive(true);

                //148685 StorySceneの直下に配置、これをやらないとシーンの削除と一緒にトレーニングカットのルートオブジェクトが消さなくなる
                _trainingCuttRoot.SetParent(_scene.transform);
                _trainingCuttRoot.Initialize();
            }

            //個々のClipオブジェクトに対して表示非表示を一括で管理するための親オブジェクトを生成
            var parentObject = isFindCutInfo ? cutInfo.Parent : new GameObject();
            var parent = parentObject.transform;
            parent.SetParent(_trainingCuttRoot, false);
            parent.SetActiveWithCheck(true);

#if UNITY_EDITOR && CYG_DEBUG
            parentObject.name = $"TrainingCut_{uniqueCutIdName}";
#endif

            if (!(context.EnvParamExists() && ResourceManager.IsExistAsset(ResourcePath.GetPrologueCuttPath(context.CommandId))))
            {
                return;
            }

            //カットの初期化と初回更新を行い諸々のリソースをロードさせる
            var isLoadSameCut = _currentCutIdName == uniqueCutIdName;

            //FrameBufferの作成
            //OutカットはFrameBufferが必要
            if (_frameBuffer == null)
            {
                _frameBuffer = new GallopFrameBuffer();
                _frameBuffer.Initialize();

                //まだカット再生しないので一度FrameBuffer＞＞UIのイベントはOff、再生のタイミングと終了のタイミングでOn/Off切り替える
                _frameBuffer.RemoveFrameBufferToUI();
            }

            cutInHelper = new RaceSkillCutInHelper();
            cutInHelper.InitForStory(_frameBuffer);
            cutInHelper.OnStartAction = (timelineController) =>
            {
                //通常のスキルカットとは違う処理
                //初回Updateの前にIsSkipSetupCoroutineWaitでカットの中で行われるコルーチンのWaitをしないようにしている
                //parent.SetActiveWithCheck(false);が下で呼ばれていると思うので、そのタイミングでコルーチンが止まってしまい途中で処理が終わってしまう
                //無理やりコルーチンで止まらないようにフラグで1F待つのをやめるように
                //ただし調べたのはキャラのSetupCharacter関数だけでそれ以外のコルーチンで問題は出ているかもしれない
                timelineController.IsSkipSetupCoroutineWait = true;
                //InitialUpdateを呼ぶことで再生前に背景やキャラをロードする
                timelineController.InitialUpdate();
            };
            cutInHelper.OnEndAction = (_) => {
                if (isLoadSameCut == false)
                {
                    _trainingCuttController.OnStopCutt.Call();
                }
            };

            cutInHelper.Play(context.UpdateCutInPath(), parent);
            // InitialUpdateの時点では再生位置が-1Fなので1F進める
            cutInHelper.TimelineController.ExternalUpdate();
            cutInHelper.TimelineController.IsEndless = true;

            // カット関連GameObjectを隠す
            parent.SetActiveWithCheck(false);

            _trainingCuttController.IsTrainingCuttLoaded = true;

            if (!_outCutInfoDictionary.ContainsKey(uniqueCutIdName))
            {
                var cutInBuffer = new OutCutInfo()
                {
                    Parent = parentObject,
                    CutInContext = context,
                    CutInHelper = cutInHelper
                };

                _outCutInfoDictionary.Add(uniqueCutIdName, cutInBuffer);
            }
        }

        /// <summary>
        /// カットの再生開始処理を行う
        /// </summary>
        private void PlayOutCutt(RaceSkillCutInHelper cutInHelper)
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            //倍速中なら先にUpdateを呼んでおく
            //#101506対応のため(呼んでおかないと内部のフラグが折れたままになってしまう)
            //既存への影響を考えて倍速時のみUpdate設定
            if (StoryTimelineController.IsHighSpeedMode())
            {
                UpdateOutCutt();
            }
            
            CurrentOutCutInParent.SetActiveWithCheck(true);


#if UNITY_EDITOR && CYG_DEBUG
            //タイムラインエディター操作中は巻き戻ったりして、IsAutoPlayが解除されることがあるので再生のタイミングでtrueにしなおす
            var sceneId = SceneManager.Instance.GetCurrentSceneId();
            if (sceneId == SceneDefine.SceneId.StoryTimelineEditor)
            {
                CurrentOutCutInHelper.TimelineController.IsAutoPlay = true;
            }
#endif
        }

        /// <summary>
        /// カットFixedUpdate更新
        /// </summary>
        private void FixedUpdateOutCutt()
        {
            if (_isPlayedFixedUpdateForHighSpeed)
            {
                return;
            }

            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            // カットのAlterUpdate
            // もしも倍速で再生している場合、1フレームのシミュレート時間を30FPSの時と同じ時間にするため、ここで分割してアップデートを行う
            var speed = GetTargetSpeed();
            if (speed > SingleModeDefine.CUT_PLAY_SPEED_1X)
            {
                CurrentOutCutInHelper.FixedUpdateForHighSpeed(speed);
            }

            _isPlayedFixedUpdateForHighSpeed = true;
        }

        /// <summary>
        /// Cuttの再生速度を取得する
        /// 育成カットの場合はSingleModeTrainingCutInHelperが内部で行っている処理だが、Outはスキルカットインを使用しているのでいったん外部で判定をすることに
        /// </summary>
        private float GetTargetSpeed()
        {
            var speed = SingleModeDefine.CUT_PLAY_SPEED_1X;

            //育成かまたはギャラリーでプレイ中
            bool isPlayingSingleMode = WorkDataManager.Instance.SingleMode.IsPlaying || WorkDataManager.Instance.GalleryData.IsPlayingEventGallery;
#if CYG_DEBUG
            //Directの場合もプレイ中扱いとする
            isPlayingSingleMode = isPlayingSingleMode || SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.TrainingDirect
                                                      || SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.Story;
#endif

            // 再生速度設定（プロローグ以外のシングルモード中のみ）
            if (CurrentOutCutInHelper != null && CurrentOutCutInHelper.TimelineController != null && CurrentOutCutInHelper.TimelineController.Data != null && isPlayingSingleMode && !CurrentOutCutInContext.IsPrologueCutt)
            {
                speed = SingleModeUtils.GetTrainingCutTimeScale(CurrentOutCutInHelper.TimelineController.GetTotalTime());
            }

            return speed;
        }

        /// <summary>
        /// カット更新
        /// </summary>
        private void UpdateOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            // カットのAlterUpdate
            CurrentOutCutInHelper?.AlterUpdate();
            
            // CharaPartsDataの設定を反映する
            _trainingCuttController.ApplyCharaPartsData();
        }

        /// <summary>
        /// カット後更新
        /// </summary>
        private void LateUpdateOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            _isPlayedFixedUpdateForHighSpeed = false;

            CurrentOutCutInHelper?.AlterLateUpdate();

#if UNITY_EDITOR && CYG_DEBUG
            var sceneId = SceneManager.Instance.GetCurrentSceneId();
            if (sceneId == SceneDefine.SceneId.StoryTimelineEditor && IsOutCuttEnd())
            {
                _trainingCuttController.OnStopCutt();
            }
#endif
        }

        /// <summary>
        /// カットの再生停止をリクエストする
        /// </summary>
        private void RequestStopOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (CurrentOutCutInHelper == null)
            {
                return;
            }

#if UNITY_EDITOR && CYG_DEBUG
            //タイムラインエディター操作中はStopRequestで完全停止されても困るのでIsAutoPlayだけ解除
            var sceneId = SceneManager.Instance.GetCurrentSceneId();
            if (sceneId == SceneDefine.SceneId.StoryTimelineEditor)
            {
                CurrentOutCutInHelper.TimelineController.IsAutoPlay = false;
            }
            else
#endif
            {
                CurrentOutCutInHelper.StopRequest();
            }
        }

        /// <summary>
        /// カットの再生後後片付けを行う
        /// </summary>
        private void StopOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (CurrentOutCutInHelper == null)
            {
                return;
            }

            CurrentOutCutInParent.SetActiveWithCheck(false);

#if UNITY_EDITOR && CYG_DEBUG
            //タイムラインエディター操作中はStopRequestで完全停止されても困るのでIsAutoPlayだけ解除
            var sceneId = SceneManager.Instance.GetCurrentSceneId();
            if (sceneId == SceneDefine.SceneId.StoryTimelineEditor)
            {
                CurrentOutCutInHelper.TimelineController.IsAutoPlay = false;
            }
            else
#endif
            {
                CurrentOutCutInHelper.StopRequest();
            }
        }

        /// <summary>
        /// カットをスキップする
        /// </summary>
        public void SkipOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (CurrentOutCutInHelper == null)
            {
                return;
            }

            _isTrainingCuttSkip = true;
            CurrentOutCutInHelper?.TimelineController.SkipRuntime();
            GraphicSettings.Instance.Update(); // GraphicSettingsのUpdateを手動で行う（いつもはUpdateで実行されるが、スキップしてスクショを取る流れだとスクショとるまでにパラメータの更新が行われないため）

            // StoryTimelineControllerの時間がずれてしまうのでカット終了時刻まで飛ばす
            var cuttStartTime = (_trainingCuttController.StartTime + _trainingCuttController.DelayTime);
            var cuttEndTime = (cuttStartTime + CurrentOutCutInHelper?.GetTotalTime() ?? 0f);
            if (TimelineController.CurrentTime < cuttEndTime)
            {
                TimelineController.Warp(Mathf.FloorToInt(cuttEndTime * GameDefine.NORMAL_FRAME_RATE));
            }
        }

        /// <summary>
        /// カットをリスタートさせる
        /// </summary>
        public void RestartOutCutt()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (CurrentOutCutInHelper == null)
            {
                return;
            }

            CurrentOutCutInHelper.TimelineController.SetCurrentTime(0f);

            //148685 カット時間設定後すぐに適用して、エフェクトやProp回りにも更新
            CurrentOutCutInHelper.AlterUpdate();
            CurrentOutCutInHelper.AlterLateUpdate();
            CurrentOutCutInHelper.TimelineController.EffectSimulateToCurrentFrame();
        }

        /// <summary>
        /// カットの現在再生時刻を更新する
        /// 内部でEidtor用の処理が分かれている、本当ならSkipRuntimeやSkipTimeDirectを使う事。
        /// ただしEditorでは何度も呼ばれるとフリーズする。あるいはシークバーを動かしたときの表示の違いが目立つ。
        /// 
        /// CyrrentTimeの更新でもシークを飛ばし飛ばしにするとパーティクル等が残る可能性があるので、厳密に行うならCutInToolでやっているようにCutTimelineController側のUpdateViewを調整する必要があるが、
        /// 変更箇所が多いわりに育成で長いカットを使うのはグラマス編のプロローグでしかまだ存在しないので見送り 2023/02/03
        /// </summary>
        private void OnSetOutCuttCurrentTime(float currentTime)
        {
#if UNITY_EDITOR && CYG_DEBUG
            if (TimelineController.IsPlayingEditor)
            {
                OnSetOutCutttCurrentTimeForEditor(currentTime);
                return;
            }
#endif

            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (CurrentOutCutInHelper == null)
            {
                return;
            }

            var time = Mathf.Max(currentTime - _trainingCuttController.DelayTime, 0);
            //SkipTimeを更新してTimeline側のUpdateに任せる
            CurrentOutCutInHelper.TimelineController.SkipTimeDirect(time);
        }

#if UNITY_EDITOR        
        /// <summary>
        /// カットの現在再生時刻を更新する
        /// </summary>
        private void OnSetOutCutttCurrentTimeForEditor(float currentTime)
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (CurrentOutCutInHelper == null)
            {
                return;
            }

            var time = Mathf.Max(currentTime - _trainingCuttController.DelayTime, 0);
            //EditorではCurrentTimeを直に更新する
            //StoryTimeline経由(連続更新が何度も必要)だとTimeline側のUpdate更新などのタイミングのずれとかで上手くいかないことが多い。
            //時間を戻したときや飛ばしたときにパーティクルが残ってしまうときもあるが、再生とプレビュー時に問題はないので許容するしかない

            //2024/12/26if文追加
            //CurrentTimeは-1Fから始まってUpdateで中で0になるのが正しい
            //Storyから起動した場合ストーリーの時間がセットされるので0.0始まりになってしまうので１度カットのUpdateで内部で-1F＞＞0Fに変わってから時間がセットされるように
            if (time >= 0.0f && CurrentOutCutInHelper.TimelineController.CurrentTime >= 0.0f)
            {
                CurrentOutCutInHelper.TimelineController.CurrentTime = time;
            }
        }
#endif        

        /// <summary>
        /// カットが末尾まで再生されたかを取得する
        /// </summary>
        private bool IsOutCuttEnd()
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return false;
            }

            // カットを再生していた場合はClipの外に出たかどうかを判定
            // - Clipには余白を持たせてあるので実際のCuttデータより長い
            // - そのため, Clipの外に出たかどうかで判定しないと移動ワイプとタイミングがズレてしまう

            if (CurrentOutCutInHelper != null)
            {
                // 処理の都合でカット側が1フレーム遅れる場合があるので、1フレーム範囲を拡大する
                var elapsedTime = TimelineController.CurrentTime - _trainingCuttController.StartTime;
                var totalTime = _trainingCuttController.TotalTime;

                //カット開始してから現在の時間がマイナスの場合
                if (elapsedTime < -GameDefine.BASE_FPS_TIME)
                {
                    //今のところ、カットを使用したストーリーで前に戻る進行はないけれど、一応入れておく
                    //カットは最初-1F時間から始まるので、CurrentTimeを初期値の-1Fに戻しておく
                    CurrentOutCutInHelper.TimelineController.SetCurrentTime( CutInTimelineController.RESET_TIME );
                    return true;
                }

                //カット開始してから現在の時間がカットのトータルの時間を超えている場合（カット再生しきった場合）
                return elapsedTime >= totalTime;
            }

            return true;
        }

        /// <summary>
        /// 現在再生中のCutInから指定されたキャラのモデルコントローラーを取得
        /// </summary>
        private ModelController GetModelControllerFromOutCutIn(int charaId)
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return null;
            }

            if (CurrentOutCutInHelper == null)
            {
                return null;
            }

            for (int j = 0, charaCount = CurrentOutCutInHelper.TimelineController.CharacterList.Count; j < charaCount; j++)
            {
                var chara = CurrentOutCutInHelper.TimelineController.CharacterList[j]._cutInCharacter;
                if (chara == null || chara.Model == null) continue;

                if (chara.Model.GetCharaID() == charaId)
                {
                    return chara.Model;
                }
            }

            return null;
        }

        /// <summary>
        /// トレーニングカットに停止と再開の状態設定を独立にする
        /// </summary>
        /// <param name="isPause"></param>
        private void PauseOutCutt(bool isPause)
        {
            if (_outCutInfoDictionary.ContainsKey(_currentCutIdName) == false)
            {
                return;
            }

            if (IsPlayingTrainingCutt)
            {
                if (isPause)
                {
                    CurrentOutCutInHelper?.Pause(true);
                }
                else
                {
                    CurrentOutCutInHelper?.Pause(false);
                }
            }
        }

        /// <summary>
        /// 解放処理
        /// </summary>
        private void CleanUpOutCuttIn()
        {
            foreach (var cutInfo in _outCutInfoDictionary.Values)
            {
                cutInfo.CutInHelper.RequestStopEnvSeOnEndCutIn();
                cutInfo.CutInHelper.CleanupPlaying();
                cutInfo.CutInHelper.Release();
            }

            _outCutInfoDictionary.Clear();

            if (_frameBuffer != null)
            {
                _frameBuffer.Release();
                _frameBuffer = null;
            }
        }

        #endregion TrainingCutt

    }
}
