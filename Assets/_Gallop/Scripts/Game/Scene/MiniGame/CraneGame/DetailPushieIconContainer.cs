using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

namespace Gallop
{
    /// <summary>
    /// ぬいぐるみ詳細アイコンの１列を管理するクラス
    /// </summary>
    [DisallowMultipleComponent]
    public sealed class DetailPushieIconContainer : LoopScrollItemBase
    {
        [SerializeField]
        private TextCommon _titleName = null;
        
        [SerializeField]
        private PlushieIconButton _bigPlushieIconButton = null;
        
        [SerializeField]
        private PlushieIconButton _smallPlushieIconButton = null;
        
        [SerializeField]
        private PlushieIconButton _smallPlushieWithMicIconButton = null;

        /// <summary>
        /// 値を設定する
        /// </summary>
        public void Setup
            (
                PlushieData plushieData,
                IPlushieDressData dressData,
                IReadOnlyList<IPlushieDressData> possessionList,
                ResourceManager.ResourceHash hash,
                Action<PlushieIconData> onSelect
            )
        {
            var characterId = plushieData.CharacterId;
            var dressId = dressData.DressId;
            _titleName.text = dressData.DressName;
            
            //ぬいぐるみの衣装情報が存在していれば、
            //所持しているかどうかを判断する
            //ぬいぐるみの情報が存在しなければ、所持していないと判断する
            var isPossessionStandard = possessionList?.Any(x => x.IsPossesionStandard) ?? false;
            var isPossessionWithMic = possessionList?.Any(x => x.IsPossessionWithMic) ?? false;
            var isPossessionBig = possessionList?.Any(x => x.IsPossessionBig) ?? false;
            var message = TextId.Outgame562005.Text();
            
            var smallPlushieIconData = new PlushieIconData(characterId, dressId, false, false);
            var smallPlushieWithMicIconData = new PlushieIconData(characterId, dressId, true, false);
            var bigPlushieIconButton = new PlushieIconData(characterId, dressId, false, true);


            //ぬいぐるみの衣装情報が存在していれば、
            //確認済みのフラグを確認して新しいものかどうかを判断する
            //ぬいぐるみの情報が存在しなければ、新しいもではないと判断する
            var isNewStandard = possessionList?.Any(x => !x.IsCheckedStandard) ?? false;
            var isNewWithMic = possessionList?.Any(x => !x.IsCheckedWithMic) ?? false;
            var isNewBig = possessionList?.Any(x => !x.IsCheckedBig) ?? false;

            _smallPlushieIconButton
                .Setup(smallPlushieIconData, isPossessionStandard, isNewStandard, hash, onSelect, message);
            _smallPlushieWithMicIconButton
                .Setup(smallPlushieWithMicIconData, isPossessionWithMic, isNewWithMic, hash, onSelect, message);
            _bigPlushieIconButton
                .Setup(bigPlushieIconButton, isPossessionBig, isNewBig, hash, onSelect, message);
        }
    }
}