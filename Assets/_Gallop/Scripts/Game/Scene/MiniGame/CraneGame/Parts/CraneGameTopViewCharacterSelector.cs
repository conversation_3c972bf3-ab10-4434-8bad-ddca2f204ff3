using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    public sealed class CraneGameTopViewCharacterSelector : MonoBehaviour
    {
        [SerializeField]
        private PartsCardListVertical _cardList = null;
        [SerializeField]
        private ButtonCommon _nextButton = null;
        [SerializeField]
        private PartsCharacterNamePlate _namePlate;
        [SerializeField]
        private PartsCharacterNamePlateRibbon _ribbon = null;

        private List<WorkCharaData.CharaData> _allCharaList;
        private System.Action<int> _onSelectCharacter;
        private System.DateTime _beginViewJst;

        private int _selectedCharaId;

        public void Initialize(System.Action<int> onSelectCharacter, System.DateTime beginViewJst)
        {
            _beginViewJst = beginViewJst;
            _onSelectCharacter = onSelectCharacter;
            _nextButton.SetOnClick(OnClickNextButton);
            _allCharaList = WorkDataManager.Instance.CharaData.GetList();
            CreateCardList();
            InitializeSelectedCharaId();
        }
        
        public void InitializeEachPlayIn()
        {
            _cardList.Refresh();
            _ribbon.InitializePlayer(true);
            _cardList.EnableCursor(_selectedCharaId);
            _cardList.ScrollToCursor(true);
            SetupNamePlate();
        }

        public void OnPlayInView()
        {
            Setup3DModel(_selectedCharaId);
            TweenAnimationBuilder.CreateSequence(_nextButton.gameObject,TweenAnimation.PresetType.PartsInMoveAndFade);
            PlaySelectedMotion();
            PlayRibbonAnimation(true);
        }

        public IEnumerator PlayOutView()
        {
            var complete = false;
            _ribbon.PlayOut();
            _namePlate.PlayFadeOut(()=> complete = true);
            yield return new WaitUntil(()=> complete);
        }
        public void OnEndView()
        {
            _ribbon.Release();
        }

        private void CreateCardList()
        {
            var characterButtonInfo = new List<CharacterButtonInfo>();

            foreach (var charaData in _allCharaList)
            {
                var data = WorkDataManager.Instance.CharaData.Get(charaData.Id);
                characterButtonInfo.Add(new CharacterButtonInfo()
                {
                    Id = charaData.Id,
                    IdType = CharacterButtonInfo.IdTypeEnum.Chara,
                    OverrideSortInfo = true,
                    SortInfoType = CharacterButtonInfo.SortInfo.None,
                    IconSizeType = IconBase.SizeType.Chara_80,
                    ButtonSeType = ButtonCommon.ButtonSeType.Cursor01,
                    EnableButton = data != null,
                    OnTap = (item) =>
                    {
                        OnTapItemIcon(item.Id);
                    }
                });
            }
            _cardList.Create(characterButtonInfo);
        }

        private void InitializeSelectedCharaId()
        {
            //最後にプレイしたキャラを選択する
            _selectedCharaId = SaveDataManager.Instance.SaveLoader.LastPermanentCraneGamePlayCharaId;
            //未プレイなら先頭を選択
            if (_selectedCharaId == GameDefine.INVALID_CHARA_ID)
            {
                _selectedCharaId = _allCharaList.First().Id;
            }
        }

        private void PlayRibbonAnimation(bool isPlayIn = false)
        {
            var masterDressData = MasterDataManager.Instance.masterDressData.GetWithCharaIdOrderByIdAsc(_selectedCharaId);
            UIManager.Instance.LockGameCanvas();
            _ribbon.PlayNext(masterDressData.MainColor, masterDressData.SubColor, null,() => 
            {
                UIManager.Instance.UnlockGameCanvas();
                _namePlate.PlayFadeIn();
            },isPlayIn);
        }

        /// <summary>
        /// キャラアイコンタップ時の処理
        /// </summary>
        /// <param name="charaId"></param>
        private void OnTapItemIcon(int charaId)
        {
            //違うキャラタップなら変更
            if (_selectedCharaId != charaId)
            {
                _selectedCharaId = charaId;
                Setup3DModel(charaId);
                SetupNamePlate();
                PlayRibbonAnimation();
                PlaySelectedMotion();
                _cardList.EnableCursor(charaId);
            }
        }

        private void SetupNamePlate()
        {
            var masterChara = MasterDataManager.Instance.masterCharaData.Get(_selectedCharaId);
            _namePlate.Setup(masterChara);
        }

        private void Setup3DModel(int charaId)
        {
            var bgPath = ResourcePath.GetBackgroundPath(CraneGameHubViewController.BG_ID, CraneGameHubViewController.BG_SUB_ID);
            var dressID = MasterDataManager.Instance.masterDressData.GetUniformDressIdByDateTime(charaId, _beginViewJst);
            
            BGManager.Instance.CharacterBg.Setup(ResourcePath.CRANE_GAME_HUB_ENV_PATH, bgPath, charaId, dressID, true, isRaceUI : false);
            BGManager.Instance.CharacterBg.SetupDefaultPosition();
            var pos = BGManager.Instance.CharacterBg.CameraController.transform.localPosition;
            pos.x = 0;
            BGManager.Instance.CharacterBg.SetCameraPos(pos);
            BGManager.Instance.CharacterBg.Model.PlayableAnimator.UpdateMotion(0f);
            BGManager.Instance.CharacterBg.Model.ResetCyspring();
        }

        private void PlaySelectedMotion()
        {
            var model = BGManager.Instance.CharacterBg.Model;
            if (model == null) return;
            var systemText = AudioManager.Instance.PlaySystemVoice_OtherCharaSelectCommon(
                model.GetCharaID(), model.GetCardId(), model.GetDressId(), true
            );
            if (systemText == null) return;
            AudioManager.Instance.GetAudioPlayback(systemText.CueSheet, systemText.CueId);
            model.PlayLipSyncAndMotion(systemText, nextMotion: model.IdleMotionSetMaster);
        }

        public void OnClickNextButton()
        {
            SaveDataManager.Instance.SaveLoader.LastPermanentCraneGamePlayCharaId = _selectedCharaId;
            _onSelectCharacter(_selectedCharaId);
        }
    }
}