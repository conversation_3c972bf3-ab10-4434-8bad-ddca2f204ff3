using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ぬいぐるみアイコンボタンの情報を管理するクラス
    /// </summary>
    public sealed class PlushieIconData
    {
        private int _characterId = 0;
        private int _dressId = 0;
        private bool _haveMic = false;
        private bool _isBig = false;

        public int CharacterId => _characterId;
        public int DressId => _dressId;
        public bool HaveMic => _haveMic;
        public bool IsBig => _isBig;

        public PlushieIconData(int characterId, int dressId, bool haveMic, bool isBig)
        {
            _characterId = characterId;
            _dressId = dressId;
            _haveMic = haveMic;
            _isBig = isBig;
        }
    }
}