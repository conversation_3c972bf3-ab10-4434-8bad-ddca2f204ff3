using Cute.Http;

namespace Gallop
{
    /// <summary>
    /// レジェンドレースの開催期間が終了した事をユーザーにわかりやすく表示するためのエラーハンドラ
    /// </summary>
    public class LegendRaceErrorHandler
    {
        public static void Handle(ErrorType errorType, int errorCode)
        {
            if (errorCode == GallopResultCode.LEGEND_RACE_NOT_RACE_TIME)
            {
                ShowDialog();
            }
        }

        private static void ShowDialog()
        {
            // エラーダイアログを表示してホームへ戻る
            // （タイトル：エラー　本文：レジェンドレースの開催期間が終了しました）
            // 1.28.5：時限開放後 （タイトル：エラー　本文：イベントの開催期間外です）
            string titleText = TextId.Common0071.Text();
            string explainText = TextId.ChallengeMatch408038.Text();

            if (SceneManager.Instance.GetCurrentSceneId() == SceneDefine.SceneId.Home)
            {
                titleText = TextId.Home497001.Text();
                explainText = TextId.Home497002.Text();
            }

            DialogManager.PushErrorCommon(explainText, titleText, () =>
            {
                // ワークデータに記録していたレジェンドレースの進行状況をリセット
                WorkDataManager.Instance.RaceStateData.ResetLegendRaceData();

                // 画面遷移中や通信中はゲームキャンバスがロックされている可能性があるのでロック解除
                if (UIManager.Instance.IsLockGameCanvas())
                {
                    UIManager.Instance.UnlockGameCanvas();
                }
            }, GallopResultCode.OpenErrorPopupType.Home);
        }
    }
}
