using UnityEngine;
using System;

namespace Gallop
{
    public class StoryMovieView : ViewBase
    {
        /// <summary>メニューで管理するボタンの制御クラス</summary>
        [SerializeField]
        private StoryMovieMenuButton _storyMovieMenuButton = null;
        public StoryMovieMenuButton StoryMovieMenuButton => _storyMovieMenuButton;
        
        /// <summary>ポーズUI制御クラス</summary>
        [SerializeField]
        private PauseIconController _pauseIconController = null;

        public PauseIconController PauseIconController => _pauseIconController;

        /// <summary>ポーズボタン（横持ち）</summary>
        [SerializeField]
        private ButtonCommon _pauseButtonLandscape = null;
        public ButtonCommon PauseButtonLandscape => _pauseButtonLandscape;
        
        /// <summary>ポーズボタン（縦持ち）</summary>
        [SerializeField]
        private ButtonCommon _pauseButtonPortrait = null;
        public ButtonCommon PauseButtonPortrait => _pauseButtonPortrait;
        
        public bool IsEnabledApplicationPause { get; set; } = false;
        public Action<bool> ApplicationPause { get; set; } = null;

        #region サスレジ対応
        private void OnApplicationFocus(bool hasFocus)
        {
#if DMM && !UNITY_EDITOR
            return;
#elif UNITY_EDITOR && CYG_DEBUG
            if (TutorialAutoProgressor.IsAutoProgressEnable)
            {
                return;
            }
#endif
            
            if (IsEnabledApplicationPause)
            {
                ApplicationPause(!hasFocus);
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
#if UNITY_EDITOR && CYG_DEBUG
            if (TutorialAutoProgressor.IsAutoProgressEnable)
            {
                return;
            }
#endif
            
            if (IsEnabledApplicationPause)
            {
                ApplicationPause(pauseStatus);
            }
        }
        #endregion サスレジ対応
    }
}