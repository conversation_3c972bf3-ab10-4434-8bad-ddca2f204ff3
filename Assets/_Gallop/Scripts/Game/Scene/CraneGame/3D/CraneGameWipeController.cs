using UnityEngine;
using System.Collections.Generic;
using static Gallop.CraneGameDefines;

namespace Gallop
{
    /// <summary>
    /// クレーンゲーム：ワイプ、リザルトのキャラ表示も兼用
    /// </summary>
    public class CraneGameWipeController : CraneGameControllerBase, ICraneGameDefineLoader
    {
#if CYG_DEBUG
        public bool DebugFollowEnable
        {
            get => _debugFollowEnable;
            set
            {
                if(value == false)
                {
                    _camera.transform.localPosition = Math.VECTOR3_ZERO;
                }
                _debugFollowEnable = value;
            }
        }
        private bool _debugFollowEnable = true;
#endif

        private const int PROP_ID = 1103;
        private const string LABEL_BIG = "doll03_loop";
        private const string LABEL_SUC = "doll02_loop";
        public const string LABEL_GREAT = "doll01_loop";

        private static Vector2Int RENDER_TEX_RESOLUTION = new Vector2Int(512, 512);
        private static Vector2Int RESULT_RENDER_TEX_RESOLUTION = new Vector2Int(1536, 2048);

        public Camera GetResultCamera() => _resultCamera;
        public List<MiniModelController> GetPropMiniModelList() => _propMiniModelList;

        [SerializeField] private Transform _cameraRoot;
        [SerializeField] private Camera _camera;
        [SerializeField] private Camera _resultCamera;

        private CraneGame _game;
        private SimpleModelController _charaModel;
        private RenderTexture _renderTex;
        private RenderTexture _resultRenderTex;
        private CraneGameDefinesAsset _defines;
        private MasterCharacterSystemText.CharacterSystemText _resultVoiceData;
        private CraneGameCharaPropController _propController;
        private List<MiniModelController> _propMiniModelList = new List<MiniModelController>(10);
        private MasterCharaMotionSet.CharaMotionSet _defaultMotionSet;
        private Transform _cameraFollowTarget;
        private bool _isInitCamera;
        private Vector3 _defaultFollowTargetPos;
        private Vector3 _defaultCameraPos;

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="game"></param>
        /// <param name="onInitialzied"></param>
        public override void Initialize(CraneGame game, System.Action onInitialzied)
        {
            Clear();
            _game = game;

            //すだち
            _defaultMotionSet = MasterDataManager.Instance.masterCharaMotionSet.Get(SimpleModelController.Define.SUDACHI_MOTION_ID);

            //キャラモデル生成
            var buildInfo = new CharacterBuildInfo(game.Info.WipeCharaId, game.Info.WipeDressId, ModelLoader.ControllerType.Simple);
            _charaModel = ModelLoader.CreateModel(buildInfo).GetComponent<SimpleModelController>();
            _charaModel.transform.SetParent(transform);
            _charaModel.PlayMotion(_defaultMotionSet, startType: SimpleModelController.MotionStartType.Loop);
            if(_charaModel.TryGetModelComponent<Model.Component.EyeTraceController>(out var eyeTraceController))
            {
                eyeTraceController.IsEnable = false;    //常に正面を見てしまうので、うつむいたときにちゃんと地面見るようにする
            }
#if CYG_DEBUG
            if(!CraneGame.DebugFPS30)
#endif
            if (_charaModel.TryGetModelComponent<Gallop.Model.Component.CySpringUpdater>(out var cySpringUpdater))
            {
                //60FPS想定で動作させる
                cySpringUpdater.SpringUpdateMode = CySpringController.SpringUpdateMode.SkipFramePostAlways;
            }
            _cameraFollowTarget = _charaModel.FindTransform(CharaNodeName.Head);

            //UIにワイプテクスチャをセット
            var ui = game.GetController<CraneGameCraneUI>();
            _renderTex = MiniDirectorUtil.CreateRenderTex(_camera, RENDER_TEX_RESOLUTION);
            _resultRenderTex = MiniDirectorUtil.CreateRenderTex(_camera, RESULT_RENDER_TEX_RESOLUTION);
            _camera.targetTexture = _renderTex;
            _resultCamera.targetTexture = _resultRenderTex;
            ui.SetWipeTexture(_renderTex);
            ui.SetResultCharaTexture(_resultRenderTex);

            onInitialzied();
        }

        /// <summary>
        /// ステート遷移
        /// </summary>
        /// <param name="state"></param>
        public override void OnStateChange(CraneGameDefines.GameState state)
        {
            switch (state)
            {
                case CraneGameDefines.GameState.GameStart:
                    if(_propController != null)
                    {
                        _charaModel.RemovePropCtrl(_propController);
                        Destroy(_propController.gameObject);
                        _charaModel.PlayMotion(_charaModel.IdleMotionSetMaster, startType: SimpleModelController.MotionStartType.Loop);
                    }
                    _camera.enabled = true;
                    _resultCamera.enabled = false;
                    break;
                case CraneGameDefines.GameState.Result:
                    _camera.enabled = false;
                    _resultCamera.enabled = true;
                    var resultCharaParam = _defines.Get(CraneGameDefinesAsset.Key.ResultChara);
                    resultCharaParam.SetTransformLocal(_charaModel.transform);
                    break;
            }

            base.OnStateChange(state);
        }

        //カメラ初期化
        private void InitCamera()
        {
            _defaultFollowTargetPos = _cameraFollowTarget.position;
            _defaultCameraPos = _camera.transform.position;
        }

        //カメラ位置更新
        private void UpdateCamera()
        {
            if (_game.State == GameState.Result)
                return;

#if CYG_DEBUG
            if (DebugFollowEnable == false)
                return;
#endif

            var diff = _cameraFollowTarget.position - _defaultFollowTargetPos;
            var cameraPos = _defaultCameraPos + diff;
            _camera.transform.position = cameraPos;
        }

        public override void OnLateUpdate()
        {
            switch (_game.State)
            {
                case GameState.GameStart:
                    if (!_isInitCamera)
                    {
                        var charaParam = _defines.Get(CraneGameDefinesAsset.Key.WipeChara);
                        charaParam.SetTransformLocal(_charaModel.transform);
                        InitCamera();
                        _isInitCamera = true;
                    }        
                    break;
            }
            UpdateCamera();
            base.OnLateUpdate();
        }

        /// <summary>
        /// 終了
        /// </summary>
        public override void Final()
        {
            Clear();
        }

        /// <summary>
        /// システムテキスト指定でモーション、ボイス再生
        /// </summary>
        /// <param name="systemText"></param>
        public void PlaySystemText(MasterCharacterSystemText.CharacterSystemText systemText, bool isUseFixedElapsedTime)
        {
            if (_charaModel == null || systemText == null)
                return;

            _charaModel.PlayLipSyncAndMotion(systemText, _defaultMotionSet, isUseFixedElapsedTime : isUseFixedElapsedTime);
        }

        private void Clear()
        {
            if (_charaModel != null)
            {
                _charaModel.SetActiveWithCheck(false);
                Destroy(_charaModel.gameObject);
            }
            if (_renderTex != null)
            {
                _renderTex.Release();
                _renderTex = null;
                _camera.targetTexture = null;
            }
            if(_resultRenderTex != null)
            {
                _resultRenderTex.Release();
                _resultRenderTex = null;
                _resultCamera.targetTexture = null;
            }
        }

        //事前に抽選したリザルトのボイスデータから音声再生だけ行う
        public void PlayResultVoice()
        {
            if (_resultVoiceData == null)
                return;

            AudioManager.Instance.PlaySystemVoiceByElement(_resultVoiceData);
            _charaModel.PlayLipSync(_resultVoiceData);
        }

        //ぬいぐるみ抱えモーション再生
        public void PlayResultMotion(GameResult result, List<CraneGameInfo.PrizeInfo> getList)
        {
            if (_charaModel == null)
                return;

            var isBig = false;
            var propNum = 0;
            foreach (var prize in getList)
            {
                if (prize.IsBig)
                {
                    isBig = true;
                    break;
                }
            }

            var trigger = CharacterSystemLotteryTrigger.None;
            switch (result)
            {
                case GameResult.GreatSuccess:
                    trigger = isBig ? CharacterSystemLotteryTrigger.MinigameCraneResultGreatBig : CharacterSystemLotteryTrigger.MinigameCraneResultGreat;
                    propNum = isBig ? 3 : 6;
                    break;
                case GameResult.Success:
                    trigger = isBig ? CharacterSystemLotteryTrigger.MinigameCraneResultGoodBig : CharacterSystemLotteryTrigger.MinigameCraneResultGood;
                    propNum = 1;
                    break;
                case GameResult.Failed:
                    trigger = CharacterSystemLotteryTrigger.MinigameCraneResultBad;
                    break;
            }

            //ボイスデータの取得
            _resultVoiceData = AudioManager.Instance.PlaySystemVoice_CharaTrigger(_game.Info.WipeCharaId, trigger, lotteryOnly: true);

            //モーションの再生
            if (_resultVoiceData != null)
            {
                var motionSet = MasterDataManager.Instance.masterCharaMotionSet.Get(_resultVoiceData.MotionSet);
                if (motionSet != null)
                {
                    _charaModel.PlayMotion(motionSet, startType: SimpleModelController.MotionStartType.Loop);
                    CreatePrizeProps(motionSet.BodyMotion, isBig, propNum, getList);
                }
            }
        }

        //ぬいぐるみの生成
        private void CreatePrizeProps(string label, bool big, int propNum, List<CraneGameInfo.PrizeInfo> getList)
        {
            //親となる小物の生成
            var resourcePath = ResourcePath.GetPropPath(PROP_ID, 0);
            GameObject originalObj = ResourceManager.LoadOnScene<GameObject>(resourcePath);
            if (originalObj == null)
                return;

            var attachTransform = _charaModel.FindTransform(CharaNodeName.Hand_Attach_R);
            var propObj = Instantiate(originalObj, attachTransform);

            _propController = propObj.AddComponent<CraneGameCharaPropController>();
            CharaPropController.CreateContext context = new CharaPropController.CreateContext
            {
                _propId = PROP_ID,
                _useSceneType = CharaPropController.UseSceneType.Story,
                LoadHash = _charaModel.GetBuildInfo().LoadHashKey,
                OtherCharaNodeTransformFindAction = _charaModel.TryFindTransform,
                _controller = ResourceManager.LoadOnView<RuntimeAnimatorController>(ResourcePath.CRANE_GAME_RESULT_PROP_ANIMATOR),
            };
            // StoryCharaPropControllerの初期化
            _propController.Create(ref context);
            _charaModel.SetPropCtrl(_propController);
            _propController.CacheTransform.localPosition = Math.VECTOR3_ZERO;
            _propController.CacheTransform.localRotation = Math.QUATERNION_IDENTITY;

            //ぬいぐるみのロード
            _propMiniModelList.Clear();
            if (big)
            {
                //巨大有り、優先的に巨大を配置し、残りに通常を設置
                CraneGameInfo.PrizeInfo bigInfo = null;
                foreach(var p in getList)
                {
                    if(p.IsBig)
                    {
                        bigInfo = p;
                        var mini = StoryCharaPropController.CreateMiniModel(_propController, 0, p.CharaId, p.DressId, p.HoldMotion, p.FaceType, CraneGamePrizeObject.BIG_MODEL_SCALE);
                        _propMiniModelList.Add(mini);
                        break;
                    }
                }
                var index = 1;
                for (int i = 0; i < getList.Count && index < propNum; i++)
                {
                    var prize = getList[i];
                    if (prize.IsBig)
                        continue;

                    var mini = StoryCharaPropController.CreateMiniModel(_propController, index, prize.CharaId, prize.DressId, prize.HoldMotion, prize.FaceType, CraneGamePrizeObject.MINI_MODEL_SCALE);
                    _propMiniModelList.Add(mini);
                    index++;
                }
            }
            else
            {
                //全員ちっさいので普通に配置
                for (int i = 0; i < propNum; i++)
                {
                    var prize = getList[i];
                    var mini =StoryCharaPropController.CreateMiniModel(_propController, i, prize.CharaId, prize.DressId, prize.HoldMotion, prize.FaceType, CraneGamePrizeObject.MINI_MODEL_SCALE);
                    _propMiniModelList.Add(mini);
                }
            }

            _propController.PlayLabel(label);
        }

        #region ICraneGameDefineLoader

        public void LoadDefine(CraneGameDefinesAsset asset)
        {
            _defines = asset;

            var cameraParam = _defines.Get(CraneGameDefinesAsset.Key.WipeCamera);
            cameraParam.SetTransformLocal(_cameraRoot);
            _camera.fieldOfView = cameraParam.Fov;
            SetCameraTransformBaseChara(_cameraRoot);

            var resultCameraParam = _defines.Get(CraneGameDefinesAsset.Key.ResultCamera);
            resultCameraParam.SetTransformLocal(_resultCamera.transform);
            _resultCamera.fieldOfView = resultCameraParam.Fov;
            SetCameraTransformBaseChara(_resultCamera.transform);

            switch (_game.State)
            {
                case CraneGameDefines.GameState.GameStart:
                    var charaParam = _defines.Get(CraneGameDefinesAsset.Key.WipeChara);
                    charaParam.SetTransformLocal(_charaModel.transform);
                    break;
                case CraneGameDefines.GameState.GameEnd:
                    var resultCharaParam = _defines.Get(CraneGameDefinesAsset.Key.ResultChara);
                    resultCharaParam.SetTransformLocal(_charaModel.transform);
                    break;
            }
        }

        private void SetCameraTransformBaseChara(Transform cameraTransform)
        {
            if (_charaModel == null)
                return;

            var cameraPos = cameraTransform.transform.localPosition;
            cameraPos *= _charaModel.GetBodyScale();
            //スぺで調整された位置なのでスぺのスケールで割る
            cameraTransform.transform.localPosition = cameraPos;
        }

        public List<CraneGameDefinesAsset.Param> GetSaveParamList()
        {
            if (_charaModel == null)
                return new List<CraneGameDefinesAsset.Param>();

            var cameraParam = CraneGameDefinesAsset.Param.CameraToParam(CraneGameDefinesAsset.Key.WipeCamera, _camera, _cameraRoot);
            cameraParam.Position = cameraParam.Position / _charaModel.GetBodyScale();   //スケール補正が掛かってるので戻す
            var wipeCharaParam = CraneGameDefinesAsset.Param.TransformLocalToParam(CraneGameDefinesAsset.Key.WipeChara, _charaModel.transform);

            var resultCameraParam = CraneGameDefinesAsset.Param.CameraToParam(CraneGameDefinesAsset.Key.ResultCamera, _resultCamera);
            resultCameraParam.Position = resultCameraParam.Position / _charaModel.GetBodyScale();
            var resultCharaParam = CraneGameDefinesAsset.Param.TransformLocalToParam(CraneGameDefinesAsset.Key.ResultChara, _charaModel.transform);

            return new List<CraneGameDefinesAsset.Param>()
            {
                wipeCharaParam,
                cameraParam,
                resultCameraParam,
                resultCharaParam
            };
        }

        #endregion
    }
}
