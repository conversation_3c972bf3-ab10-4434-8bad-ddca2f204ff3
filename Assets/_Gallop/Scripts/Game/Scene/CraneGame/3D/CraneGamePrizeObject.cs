using UnityEngine;
using Gallop.Model.Component;

namespace Gallop
{
    /// <summary>
    /// クレーンゲーム：景品オブジェクト
    /// </summary>
    [AddComponentMenu("")]
    public class CraneGamePrizeObject : MonoBehaviour
    {
        public const string CATCH_MOTION_LABEL = "Catch{0:D2}";
        public const string GET_POS_MOTION_LABEL = "Catch01";
        public const string GET_POS_MOTION_LABEL_BIG = "Catch12";
        public const string LIFT_MOTION_LABEL = "Lift{0:D2}";
        public const string HANG_MOTION_LABEL = "Hang{0:D2}_{1:D2}";
        public const string PUSH_MOTION_LABEL = "Push{0:D2}";
        public const float MINI_MODEL_SCALE = 0.15f;
        public const float BIG_MODEL_SCALE = MINI_MODEL_SCALE * 1.5f;   //通常モデルの1.5倍

        private const float GRAVITY_RATE = 0.75f;    //重力を弱くする
        private const float VELOCITY_RATE_UP = 0.1f;
        private const float VELOCITY_RATE_XZ = 0.3f;

        //ジョイント用プロパティ
        //グループからセットしてもらう
        public HingeJoint Joint { get; set; }
        public Rigidbody JointBody { get; set; }

        public CraneGameInfo.PrizeInfo Info { get; private set; }
        public Rigidbody RigidBody => _rigidBody;
        public Transform HandAttachR { get; private set; }
        public Transform Head { get; private set; }
        public Transform Hip { get; private set; }
        public Transform TailIKJoint => _tailIk.IKJoint;
        public MiniModelController GetModel() => _miniModel;
        
        [SerializeField] private Rigidbody _rigidBody;

        private MiniModelController _miniModel;
        private Transform _headLimitTrans;
        private CraneGamePrizeObject _parent;
        private GameObject _collisionInstance;
        private string _defaultMotion;

        //尻尾
        private MiniTailIK _tailIk;
        private Transform _tailCtrl;

        /// <summary>
        /// 生成
        /// </summary>
        /// <param name="prizeInfo"></param>
        public void Create(CraneGameInfo.PrizeInfo prizeInfo, string defaultMotion)
        {
            Info = prizeInfo;

            var scale = prizeInfo.IsBig ? BIG_MODEL_SCALE : MINI_MODEL_SCALE;

            //小さくする
            transform.localScale = Math.VECTOR3_ONE * scale;
            transform.localPosition = Math.VECTOR3_ZERO;
            transform.localRotation = Math.QUATERNION_IDENTITY;

            //モデルの生成
            //別オブジェクトに差し替えるならこの辺を切り替える
            _miniModel = ModelLoader.CreateModel(new CharacterBuildInfo(prizeInfo.CharaId, prizeInfo.DressId, ModelLoader.ControllerType.Mini)).GetComponent<MiniModelController>();
            _miniModel.SetEnableCySpringScale(false);
            PlayMotion(defaultMotion);
            _defaultMotion = defaultMotion;
#if CYG_DEBUG
            if (!CraneGame.DebugFPS30)
#endif
            if (_miniModel.TryGetModelComponent<CySpringUpdater>(out var cySpringUpdater))
            {
                //60FPS想定で動作させる
                cySpringUpdater.SpringUpdateMode = CySpringController.SpringUpdateMode.SkipFrame;
            }
            _miniModel.ReserveWarmingUpCySpring();
            _miniModel.SetOutlineWidth(0);  //アウトライン表示OFF
            _miniModel.UpdateMaterialPropertyBlock();
            _miniModel.SetCullingMode(AnimatorCullingMode.AlwaysAnimate);   //カメラが移動するので常にアニメさせる
            var baseModel = _miniModel.gameObject;

            //Transform初期化
            baseModel.transform.SetParent(transform);
            baseModel.transform.localPosition = Math.VECTOR3_ZERO;
            baseModel.transform.localRotation = Math.QUATERNION_IDENTITY;
            baseModel.transform.localScale = Math.VECTOR3_ONE;

            //コリジョンの生成、キャラに姿勢が入るのでHipの下に置く
            var collisionObj = ResourceManager.LoadOnView<GameObject>(ResourcePath.CRANE_GAME_MODEL_COLLISION);
            _collisionInstance = Instantiate(collisionObj, transform);
            var hip = _miniModel.FindTransform(CharaNodeName.Hip);
            if (hip)
            {
                _collisionInstance.transform.SetParent(hip);
            }
            _collisionInstance.transform.localPosition = Math.VECTOR3_ZERO;
            _collisionInstance.transform.localRotation = Math.QUATERNION_IDENTITY;
            _collisionInstance.transform.localScale = Math.VECTOR3_ONE;

            //右手
            HandAttachR = _miniModel.FindTransform(CharaNodeName.Hand_Attach_R);

            //頭
            Head = _miniModel.FindTransform(CharaNodeName.Head);

            //尻
            Hip = _miniModel.FindTransform(CharaNodeName.Hip);

            //尻尾IK
            _tailCtrl = _miniModel.FindTransform(CharaNodeName.TAIL_CTRL);
            if (_tailCtrl)
            {
                _tailIk = gameObject.AddComponent<MiniTailIK>();
                _tailIk.Initialize(_tailCtrl);
            }

            SetKinematic(true);
        }

        /// <summary>
        /// 尻尾のIKターゲットを指定
        /// </summary>
        /// <param name="target"></param>
        public void SetTailTarget(Transform target)
        {
            if (_tailIk == null)
                return;

            _miniModel.PauseCySpringTail();
            _tailIk.SetTarget(target);
        }

        /// <summary>
        /// 尻尾のIKを停止
        /// </summary>
        public void UnSetTailTarget()
        {
            if (_tailIk == null)
                return;

            _miniModel.ResumeCySpringTail();
            _tailIk.UnSetTarget();
        }
        
        /// <summary>
        /// ゲーム開始時に姿勢の初期化
        /// </summary>
        public void ResetTransform()
        {
            transform.localPosition = Math.VECTOR3_ZERO;
            transform.localRotation = Math.QUATERNION_IDENTITY;
            SetKinematic(true);

            if(_miniModel != null)
            {
                _miniModel.ReserveWarmingUpCySpring();
            }
        }

        //物理有効化するならfalse(Unity仕様)
        public void SetKinematic(bool kinematic)
        {
            if (kinematic)
                _rigidBody.collisionDetectionMode = CollisionDetectionMode.Discrete;

            _rigidBody.isKinematic = kinematic;

            //警告対策で↑のifのelseではやらない
           if(!kinematic)
               _rigidBody.collisionDetectionMode = CollisionDetectionMode.Continuous;
        }

        //コリジョンの有効化
        public void SetCollisionActive(bool active)
        {
            if (_collisionInstance == null)
                return;

            _collisionInstance.SetActive(active);
        }

        //押し込みモーション
        public void PlayPushMotion(int pushType)
        {
            var label = string.Format(PUSH_MOTION_LABEL, pushType);
            PlayMotion(label);
        }

        //持ちあげモーション
        public void PlayLiftMotion(int liftType)
        {
            var label = string.Format(LIFT_MOTION_LABEL, liftType);
            PlayMotion(label);
        }

        //持ちあげの停止
        public void StopLiftMotion()
        {
            if (_miniModel == null)
                return;
            _miniModel.StopAddMotion(Model.Component.MiniMotionSetController.AnimatorLayer.AddLayer2);
        }

        //獲得配置モーション
        public void PlayGetPosMotion()
        {
            var label = GET_POS_MOTION_LABEL;
            var master = MasterDataManager.Instance.masterMiniMotionSet.Get(_defaultMotion);
            if(master != null && master.PropId > 0)
            {
                label = _defaultMotion; //マイク持ってたりするやつはそのまま
            }
            PlayMotion(label);
        }

        //モーション再生
        //フェイシャル固定のため関数共通化
        private void PlayMotion(string label)
        {
            if (_miniModel == null)
                return;
            _miniModel.PlayMotion(label);
            _miniModel.SetFaceType(Info.FaceType);
        }

        public bool IsKinematic()
        {
            return _rigidBody.isKinematic;
        }

        //表示切替
        public void SetVisible(bool visible)
        {
            if (_miniModel == null)
                return;

            _miniModel.SetVisible(visible);
        }

        private void FixedUpdate()
        {
            if (IsKinematic())
                return;

            //速度制限
            var velocity = _rigidBody.velocity;
            velocity.y *= velocity.y > 0 ? VELOCITY_RATE_UP : GRAVITY_RATE;
            velocity.x *= VELOCITY_RATE_XZ;
            velocity.z *= VELOCITY_RATE_XZ;
            _rigidBody.velocity = velocity;
        }
    }
}
