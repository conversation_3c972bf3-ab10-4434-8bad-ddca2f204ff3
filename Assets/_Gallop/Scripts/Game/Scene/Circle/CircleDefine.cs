using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    using static StaticVariableDefine.Circle.CircleDefine;
    /// <summary>
    /// サークル定義
    /// </summary>
    public static class CircleDefine
    {
        /// <summary>
        /// 役職
        /// </summary>
        public enum Role
        {
            None,
            Member,     // メンバー
            SubLeader,  // サブリーダー
            Leader,     // リーダー
            Leave,      // 自ら脱退した状態(24時間ペナルティ)
            Free,       // 未所属(キックされて未所属になったなど、ペナルティが発生していない状態)
        }

        /// <summary>
        /// 役職名
        /// </summary>
        public static string Text(this Role role)
        {
            switch (role)
            {
                case Role.Member:
                    return TextId.Circle0151.Text();// メンバー
                case Role.SubLeader:
                    return TextId.Circle0152.Text();// サブリーダー
                case Role.Leader:
                    return TextId.Circle0003.Text();// リーダー
            }
            return "";
        }

        /// <summary>
        /// 参加人数
        /// </summary>
        public enum Population
        {
            [EnumDisplayName(TextId.Circle0021)]None,       // 指定なし
            [EnumDisplayName(TextId.Circle0222)]Under10,    // 1～10人
            [EnumDisplayName(TextId.Circle0223)]Under20,    // 11～20人
            [EnumDisplayName(TextId.Circle0224)]Under29     // 21～29人
        }

        public static string Text(this Population population)
        {
            return population.GetEnumDisplayName();
        }

        /// <summary>
        /// 承認タイプ
        /// </summary>
        public enum Approval
        {
            [EnumDisplayName(TextId.Circle0021)] None,          // 指定なし
            [EnumDisplayName(TextId.Circle0008)] Auto,          // 自動
            [EnumDisplayName(TextId.Circle0009)] Manual,        // 承認制
            [EnumDisplayName(TextId.Circle0159)] ScoutOnly,     // 勧誘のみ
        }
        
        /// <summary>
        /// 承認タイプ
        /// </summary>
        public static string Text(this Approval approval)
        {
            return approval.GetEnumDisplayName();
        }

        /// <summary>
        /// 活動方針
        /// </summary>
        public enum Policy
        {
            [EnumDisplayName(TextId.Circle0021)] None,         // 指定なし
            [EnumDisplayName(TextId.Circle0011)] Policy01,     // 自由にプレイ
            [EnumDisplayName(TextId.Circle0010)] Policy02,     // まったり
            [EnumDisplayName(TextId.Circle0012)] Policy03,     // がっつり
            [EnumDisplayName(TextId.Circle0160)] Policy04,     // 初心者OK
            [EnumDisplayName(TextId.Circle0161)] Policy05,     // わいわいプレイ
            [EnumDisplayName(TextId.Circle0162)] Policy06,     // サークルランキング2000位以内    
            [EnumDisplayName(TextId.Circle0163)] Policy07,     // サークルランキング1000位以内
            [EnumDisplayName(TextId.Circle0164)] Policy08,     // サークルランキング500位以内
            [EnumDisplayName(TextId.Circle0165)] Policy09,     // サークルランキング250位以内
            [EnumDisplayName(TextId.Circle0166)] Policy10,     // サークルランキング100位以内
            [EnumDisplayName(TextId.Circle0167)] Policy11,     // サークルランキング20位以内
            [EnumDisplayName(TextId.Circle0168)] Policy12,     // 毎日ログイン
            [EnumDisplayName(TextId.Circle0169)] Policy13,     // ２日以内ログイン
            [EnumDisplayName(TextId.Circle0170)] Policy14,     // 朝プレイメイン
            [EnumDisplayName(TextId.Circle0171)] Policy15,     // 昼プレイメイン
            [EnumDisplayName(TextId.Circle0172)] Policy16,     // 夜プレイメイン
            [EnumDisplayName(TextId.Circle0173)] Policy17,     // 深夜プレイメイン
        }

        /// <summary>
        /// 承認タイプ
        /// </summary>
        public static string Text(this Policy policy)
        {
            return policy.GetEnumDisplayName();
        }

        /// <summary>
        /// クラブランク
        /// </summary>
        public enum Rank
        {
            G = 1,
            F,
            E,
            D,
            C,
            B,
            A,
            S,
            Max
        }

        /// <summary>
        /// ランキング参加ステート
        /// </summary>
        public enum CircleRankingState
        {
            None,   //未参加（条件当てはまらない時用）
            Join,   //参加中
            CreateCircleMonth,  //サークル設立月
            NoFan,  //ファン数ゼロ
            Calculate,  //集計中
        }
        
        /// <summary>
        /// 権限
        /// </summary>
        public enum RoleAuth
        {
            [EnumDisplayName(TextId.Circle0210)] EditCircle = 0,    //サークル編集
            [EnumDisplayName(TextId.Circle0211)] ManageMember = 1,  //メンバー管理
            [EnumDisplayName(TextId.Circle0212)] ManageRequest = 2,  //加入申請・スカウト
        }

        /// <summary>
        /// ユーザーのスカウト状況
        /// ** サーバー合わせなので挿入禁止
        /// </summary>
        public enum ScoutState
        {
            None = 0,   //スカウト不可
            Enable = 1, //スカウト可能
            Scouted = 2,    //スカウト済み
            CircleFull = 3,   //サークル人数上限
            ScoutFull = 4,  //勧誘上限
        }

        // リーダーが持てる権限の数
        public static int AUTH_MAX => ROLE_AUTH_DIC[ Role.Leader ].Length;
        // サブリーダーの最大数
        public const int SUBLEADER_NUM_MAX = 2;
        // 不適合なサークルID
        public const int INVALID_CIRCLE_ID = -1;
        // サークル名最大文字数(〇文字以内)
        public const int MAX_CIRCLE_NAME_LENGTH = 10;
        // サークル名最小文字数(〇文字以上)
        public const int MIN_CIRCLE_NAME_LENGTH = 2;
        // サークルコメント最大文字数(〇文字以内)
        public const int MAX_CIRCLE_COMMENT_LENGTH = 60;
        //ランキング圏外
        public const int RANKING_NONE = 0;
        //１背景当たりのメンバー最大数
        public const int MAX_CIRCLE_MEMBER_NUM_BG = 10;
        // アイテムリクエスト最低寄付可能数
        public const int MIN_ITEM_REQUEST_DONATE_COUNT = 1;
        // サークル名刺コメント最大文字数(〇文字以内)
        public const int MAX_CIRCLE_PROFILE_CARD_COMMENT_LENGTH = 40;
    }
}
