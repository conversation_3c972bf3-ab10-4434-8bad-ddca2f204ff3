using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// アイテムリクエスト：寄付ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCircleItemDonate : DialogInnerBase
    {
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void PushDialog(WorkCircleChatData.ItemRequestInfo itemRequestInfo)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CIRCLE_DONATE_PATH));
            var component = instance.GetComponent<DialogCircleItemDonate>();
            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = instance;
            dialogData.Title = TextId.Circle0248.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Circle0250.Text();
            dialogData.LeftButtonCallBack = d => d.Close();
            dialogData.AddRightButtonCallback(component.OnClickRightButton);
            dialogData.AutoClose = false;

            var dialog = DialogManager.PushDialog(dialogData);
            component.Initialize(dialog, itemRequestInfo);
        }


        [SerializeField]
        private ItemDescription _itemDescription = null;
        
        [SerializeField]
        private TextCommon _textCurrentProgress = null;
        [SerializeField]
        private ImageCommon _imageCurrentProgress = null;

        [SerializeField]
        private ButtonCommon _buttonMinus = null;
        [SerializeField]
        private ButtonCommon _buttonPlus = null;

        [SerializeField]
        private TextCommon _textDonateCount = null;
        [SerializeField]
        private TextCommon _textItemCount = null;

        [SerializeField]
        private TextCommon _textRemainCount = null;
        
        //寄付できる数、最大数に達するまで何回でもできる
        private int DonateNumMax => _itemRequestInfo == null ? (int)ServerDefine.MaxDonateNumForRequest : ServerDefine.MaxDonateNumForRequest - _itemRequestInfo.GetDonateNumFromMe();

        private WorkCircleChatData.ItemRequestInfo _itemRequestInfo;
        private int _haveItemCount = 0;
        private int _currentDonatedNum = 0;
        private int _donateCount = CircleDefine.MIN_ITEM_REQUEST_DONATE_COUNT;
        private bool _isEnd = false;
        private DialogCommon _dialog;

        /// <summary> ダイアログ内で寄付数を書き換えたかどうかのフラグ </summary>
        private bool _isChangeDonateCount = false;

        
        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="dialog"></param>
        /// <param name="itemId"></param>
        public void Initialize(DialogCommon dialog, WorkCircleChatData.ItemRequestInfo itemRequestInfo)
        {
            _itemRequestInfo = itemRequestInfo;
            _haveItemCount = GallopUtil.GetHaveItemNum(GameDefine.ItemCategory.TRAINING, _itemRequestInfo.ItemId);
            _itemDescription.Setup(GameDefine.ItemCategory.TRAINING, _itemRequestInfo.ItemId);
            _donateCount = SaveDataManager.Instance.SaveLoader.CircleItemDonateNum;
            _dialog = dialog;
            _isChangeDonateCount = false;

            _currentDonatedNum = itemRequestInfo.GetDonatedNum();
            ValidateDonateItemCount();

            _textCurrentProgress.text = string.Format(TextId.Common0177.Format(_currentDonatedNum, ServerDefine.MaxDonateNumTotal));
            var progressSize = _imageCurrentProgress.rectTransform.sizeDelta;
            progressSize.x = progressSize.x * _currentDonatedNum / ServerDefine.MaxDonateNumTotal;
            _imageCurrentProgress.rectTransform.sizeDelta = progressSize;

            ApplyDonateItemCountText();
            _textItemCount.text = _haveItemCount.ToString();

            _buttonMinus.onClick.AddListener(OnClickMinusButton);
            _buttonPlus.onClick.AddListener(OnClickPlusButton);

            //今日寄付できる残り個数
            var remainCount = ServerDefine.MaxDonateCountDay - WorkDataManager.Instance.CircleData.DailyDonatedCount;
            _textRemainCount.text = TextId.Circle0247.Format(remainCount);

            // 最低個数を所持数が下回っている場合は押せない
            SetButtonEnable(dialog);
        }

        /// <summary>
        /// 寄付ボタンの有効化
        /// ** CircleItemReuquestInfoに似たような関数群があるので共通化したい
        /// </summary>
        /// <param name="dialog"></param>
        private void SetButtonEnable(DialogCommon dialog)
        {
            var button = dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
            var enable = true;
            var noticeText = "";
            if (_haveItemCount < CircleDefine.MIN_ITEM_REQUEST_DONATE_COUNT)
            {
                //数が足りない
                enable = false;
                noticeText = TextId.Circle0325.Text();
            }
            else if (_itemRequestInfo.GetDonateNumFromMe() >= ServerDefine.MaxDonateNumForRequest)
            {
                //最大寄付数
                enable = false;
                noticeText = TextId.Circle0262.Text();
            }
            else if (WorkDataManager.Instance.CircleData.DailyDonatedCount >= ServerDefine.MaxDonateCountDay)
            {
                //1日の寄付上限
                enable = false;
                noticeText = TextId.Circle0263.Text();
            }
            else if (_itemRequestInfo.IsMax)
            {
                //合計寄付数が最大
                enable = false;
                noticeText = TextId.Circle0269.Text();
            }
            button.SetInteractable(enable);
            button.SetNotificationMessage(noticeText);
        }

        public void OnClickRightButton(DialogCommon dialog)
        {
            CircleUtil.SendItemDonateRequest(_itemRequestInfo, _donateCount, dialog, _isChangeDonateCount);
        }

        private void OnClickPlusButton()
        {
            ++_donateCount;
            ValidateDonateItemCount();
            ApplyDonateItemCountText();
            _isChangeDonateCount = true;
        }

        private void OnClickMinusButton()
        {
            --_donateCount;
            ValidateDonateItemCount();
            ApplyDonateItemCountText();
            _isChangeDonateCount = true;
        }

        /// <summary>
        /// 寄付できるアイテムの最大数を算出する
        /// </summary>
        /// <returns></returns>
        private int CalcDonateItemMax()
        {
            var num = Mathf.Min(
                _haveItemCount,
                DonateNumMax
            );

            if((num + _currentDonatedNum) > ServerDefine.MaxDonateNumTotal)
            {
                return ServerDefine.MaxDonateNumTotal - _currentDonatedNum;
            }

            return num;
        }

        /// <summary>
        /// 寄付アイテムの数のバリデーション
        /// </summary>
        private void ValidateDonateItemCount()
        {
            int min = CircleDefine.MIN_ITEM_REQUEST_DONATE_COUNT;
            int max = CalcDonateItemMax();

            _donateCount = Mathf.Clamp(_donateCount, min, max);

            _buttonPlus.interactable = _donateCount < max;
            _buttonMinus.interactable = _donateCount > min;
        }

        /// <summary>
        /// 寄付予定アイテム数の表示反映
        /// </summary>
        private void ApplyDonateItemCountText()
        {
            _textDonateCount.text = _donateCount.ToString();
        }

        /// <summary>
        /// 期限切れチェック
        /// </summary>
        private void Update()
        {
            CheckEnd();
        }

        private void CheckEnd()
        {
            if (_itemRequestInfo == null || _dialog == null)
                return;

            if (_isEnd)
                return; //既に終了済み

            _isEnd = _itemRequestInfo.IsEnd;
            if (!_isEnd)
                return; //まだ終わってない

            //終わってるので終了済み表示
            var rightButton = _dialog.GetButtonObj(DialogCommon.ButtonIndex.Right);
            rightButton.interactable = false;
            rightButton.SetNotificationMessage(TextId.Circle0354.Text());
        }
    }
}
