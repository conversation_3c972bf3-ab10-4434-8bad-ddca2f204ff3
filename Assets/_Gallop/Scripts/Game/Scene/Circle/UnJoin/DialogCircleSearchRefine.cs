using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using Cute.Http;

namespace Gallop
{
    /// <summary>
    /// クラブ検索ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCircleSearchRefine : DialogInnerBase
    {

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        #region SerializeField、変数
        
        // サークル検索の種類
        // 名前
        [SerializeField]
        private InputFieldCommon _inputNameText = null;
        [SerializeField]
        private ButtonCommon _buttonPopulation = null;
        [SerializeField]
        private TextCommon _textPopulation = null;
        [SerializeField]
        private ButtonCommon _buttonPolicy = null;
        [SerializeField]
        private TextCommon _textPolicy = null;
        [SerializeField]
        private ButtonCommon _buttonApproval = null;
        [SerializeField]
        private TextCommon _textApproval = null;
        
        private DialogCommon _dialog = null;
        private CircleSearchParam _param;
        private System.Action<CircleSearchParam> _onSearch;
        
        private const CircleSearchParam.SearchType DefaultSearchType = CircleSearchParam.SearchType.Condition;

        #endregion

        #region 開く、設定

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void PushDialog(CircleSearchParam param, System.Action<CircleSearchParam> onSearch)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CIRCLE_SEARCH_REFINE_PATH));
            var component = instance.GetComponent<DialogCircleSearchRefine>();
            var dialogData = component.CreateDialogData();
            dialogData.ContentsObject = instance;
            dialogData.Title = TextId.Circle0020.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0022.Text();
            dialogData.AutoClose = false;
            dialogData.LeftButtonCallBack = d => d.Close();
            dialogData.RightButtonCallBack = d => component.OnDecide();

            var dialog = DialogManager.PushDialog(dialogData);
            component.Initialize(dialog, param, onSearch);
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void Initialize(DialogCommon dialog, CircleSearchParam param, System.Action<CircleSearchParam> onSearch)
        {
            _param = param;
            _dialog = dialog;
            _onSearch = onSearch;

            if (_param.searchType == CircleSearchParam.SearchType.Disabled)
            {
                // まだ検索一度もしていなかった場合、デフォルト値を使う
                _param.searchType = DefaultSearchType;
            }
            
            _buttonApproval.SetOnClick(OnClickApprovalButton);
            _buttonPolicy.SetOnClick(OnClickPolicyButton);
            _buttonPopulation.SetOnClick(OnClickPopulationButton);

            ApplyTexts();
        }

        /// <summary>
        /// テキスト反映
        /// </summary>
        private void ApplyTexts()
        {
            _inputNameText.text = _param.name;
            _textApproval.text = _param.approval.Text();
            _textPolicy.text = _param.policy.Text();
            _textPopulation.text = _param.population.Text();
        }

        #endregion

        #region ボタン反応

        /// <summary>
        /// ダイアログボタンコールバック：キャンセル
        /// </summary>
        private void OnCancel()
        {
            _dialog.Close();
        }

        /// <summary>
        /// ダイアログボタンコールバック：検索する
        /// </summary>
        private void OnDecide()
        {
            SendCircleSearchAPI(_param, (param)=> {
                _onSearch?.Invoke(param);   //この中でダイアログ開くとだめ
                DialogManager.RemoveAllDialog();
            });
        }

        /// <summary>
        /// クラブ検索通信
        /// </summary>
        public static void SendCircleSearchAPI(CircleSearchParam param, Action<CircleSearchParam> callback)
        {
            switch(param.searchType)
            {
                case CircleSearchParam.SearchType.Condition:
                {
                    var req = new CircleConditionalSearchRequest()
                    {
                        keyword = param.name,
                        member_num = (int)param.population,
                        join_style = (int)param.approval,
                        policy = (int)param.policy
                    };
                    HttpManager.Instance.Send<CircleConditionalSearchRequest, CircleConditionalSearchResponse>(req, (res) =>
                    {
                        param.circleInfos = res.data.circle_info_array;
                        param.userInfoAtFriends = res.data.leader_info_array;
                        param.rankingArray = res.data.circle_ranking_array;
                        callback(param);
                    });
                    break;
                }
                default:
                {
                    callback(param);
                    break;
                }
            }
            
        }
        

        /// <summary>
        /// サークル名入力完了
        /// </summary>
        public void OnEndEditName()
        {
            if (_inputNameText.text.Length > CircleDefine.MAX_CIRCLE_NAME_LENGTH)
            {
                _inputNameText.text = _inputNameText.text.Substring(0, CircleDefine.MAX_CIRCLE_NAME_LENGTH);
            }
            _param.name = _inputNameText.text;
        }

        /// <summary>
        /// 人数設定
        /// </summary>
        public void OnClickPopulationButton()
        {
            ToggleMenuDialog.OpenEnum(p =>
            {
                _param.population = p;
                ApplyTexts();
            }, _param.population, TextId.Circle0151.Text());
        }

        /// <summary>
        /// 活動方針
        /// </summary>
        public void OnClickPolicyButton()
        {
            ToggleMenuDialog.OpenEnum(p =>
            {
                _param.policy = p;
                ApplyTexts();
            }, _param.policy, TextId.Circle0046.Text());
        }

        /// <summary>
        /// 加入条件
        /// </summary>
        public void OnClickApprovalButton()
        {
            ToggleMenuDialog.OpenEnum(a =>
            {
                _param.approval = a;
                ApplyTexts();
            }, _param.approval, TextId.Circle0044.Text());
        }
        
        #endregion
    }
}
