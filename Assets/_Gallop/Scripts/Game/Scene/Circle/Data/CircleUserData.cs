using System;
using static Gallop.CircleDefine;

namespace Gallop
{
    /// <summary>
    /// サークルのユーザー情報
    /// <seealso cref="CircleUser"/>
    /// <seealso cref="UserInfoAtFriend"/>
    /// </summary>
    [Serializable]
    public class CircleUserData : IMiniCharaData
#if CYG_DEBUG
        , IMiniCharaDebugData
#endif
    {
#if CYG_DEBUG
        public long ViewerId { get; set; }
#else
        public long ViewerId { get; private set; }
#endif

#if CYG_DEBUG
        public int CardId { get; set; }
#else
        public int CardId { get; private set; }
#endif

        //IMiniCharaDebugData
#if CYG_DEBUG
        public int DebugBgCharaMotionId { get; set; }
#endif

        public long UniqueId => ViewerId;
        public string Name { get; set; }
        public int CharaId { get; set; }
        public int DressId { get; set; }
        public int DressColorId => CharacterBuildInfo.BACKDANCER_COLOR_ID_NULL;
        public Role Role { get; private set; }
        public long JoinTimeStamp { get; private set; }
        public long LastLoginTimeStamp { get; private set; }
        public int MonthlyCirclePoint { get; private set; }
        public int TotalCirclePoint { get; private set; }
        public bool IsItemRequest { get; private set; }
        public bool IsRaceRequest { get; private set; }
        public bool IsRequestAny => IsItemRequest || IsRaceRequest;
        public bool IsVisibleMessage => IsItemRequest;
        public int ItemRequestId { get; private set; }
        //毎回文字列をlongに直すのは避ける用
        private string _jointTime = null;
        private string _lastLoginTime = null;

        //サークル未所属用
        public CircleUserData(WorkUserData userData)
        {
            ViewerId = userData.ViewerId;
            Name = userData.UserName;
            CharaId = userData.LeaderCharaId;
            DressId = userData.LeaderDressId;

            Role = Role.None;
            JoinTimeStamp = 0;
            _lastLoginTime = userData.LastLoginTime.GetDecrypted();
            LastLoginTimeStamp = TimeUtil.ToUnixTimeFromJstString(_lastLoginTime);
            MonthlyCirclePoint = 0;
            TotalCirclePoint = 0;
            IsItemRequest = false;
            IsRaceRequest = false;
        }

        public CircleUserData(CircleUser circleUser, UserInfoAtFriend userInfo)
        {
            Update(circleUser, userInfo);
        }

        public void Update(CircleUser circleUser, UserInfoAtFriend userInfo)
        {
            UpdateCircle(circleUser);
            UpdateUser(userInfo);
            UpdateRequestFlag();
        }

    public void ChangeRole(Role newRole)
        {
            Role = newRole;
        }

        public void UpdateCircle(CircleUser server)
        {
            if (server == null)
                return;

            ViewerId = server.viewer_id;
            Role = (Role)server.membership;

            if(_jointTime != server.join_time)
            {
                _jointTime = server.join_time;
                JoinTimeStamp = TimeUtil.ToUnixTimeFromJstString(_jointTime);
            }
        }

        public void UpdateUser(UserInfoAtFriend server)
        {
            if (server == null)
                return;

            ViewerId = server.viewer_id;
            Name = server.name;
            CharaId = server.leader_chara_id;
            DressId = server.leader_chara_dress_id;
            if (_lastLoginTime != server.last_login_time)
            {
                _lastLoginTime = server.last_login_time;
                LastLoginTimeStamp = TimeUtil.ToUnixTimeFromJstString(_lastLoginTime);
            }
        }

        /// <summary>
        /// リクエスト中フラグの更新
        /// </summary>
        public void UpdateRequestFlag()
        {
            var chatItemRequestInfo = WorkDataManager.Instance.CircleChatData.GetItemRequestMessageInfo(ViewerId);
    
            if (chatItemRequestInfo != null && chatItemRequestInfo.IsEnd)
            {
                chatItemRequestInfo = null;
            }

            IsItemRequest = chatItemRequestInfo != null;
            if (chatItemRequestInfo != null)
            {
                ItemRequestId = chatItemRequestInfo.RequestId;
            }
        }
    }
}