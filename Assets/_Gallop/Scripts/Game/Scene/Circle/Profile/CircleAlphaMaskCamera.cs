using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Gallop.RenderPipeline;

namespace Gallop
{
    using GallopCameraData = Gallop.RenderPipeline.CameraData;

    /// <summary>
    /// サークルプロフィール、指定されたテクスチャで画面をマスクする
    /// </summary>
    public class CircleAlphaMaskCamera : MonoBehaviour
    {
        #region 定数

        private static readonly int MASK_PROPERTYID = Shader.PropertyToID("_Mask");
        private static readonly int TEMP_RT0_NAME = Shader.PropertyToID(nameof(CircleAlphaMaskCamera) + "_RT0");

        #endregion

        [SerializeField] private Material _baseMaterial;

        private Material _renderMat = null;

        public void Initialize()
        {
            _renderMat = Instantiate(_baseMaterial);
            var maskTexture = ResourceManager.LoadOnScene<Texture>(ResourcePath.CIRCLE_PROFILE_MASK_TEXTURE);
            //URP:置き換え対応
            _renderMat.SetTexture(MASK_PROPERTYID, maskTexture);
            if(gameObject.TryGetComponent<Camera>(out var camera))
            {
                var cameraData = camera.GetCameraData();
                cameraData.AfterRenderingParameter.AfterAction.Callback += OnEndRender;
            }
            //_renderMat.SetTexture("_Mask", maskTexture);
        }

        public void Release()
        {
            //URP:置き換え対応
            RenderUtils.Destroy(ref _renderMat);
            if (gameObject.TryGetComponent<Camera>(out var camera))
            {
                var cameraData = camera.GetCameraData();
                cameraData.AfterRenderingParameter.AfterAction.Callback -= OnEndRender;
            }

            /*
            if (_renderMat == null)
                return;

            Destroy(_renderMat);
            _renderMat = null;
            */
        }

        //URP:置き換え対応
        private void OnEndRender(ref ScriptableRenderContext context,ref RenderingData renderingData, ref RenderingCallbackPass.Parameter.Argument args)
        {
            if (_renderMat == null)
                return;

            var cmd = args.CommandBuffer;
            ref var descriptor = ref renderingData.cameraData.cameraTargetDescriptor;
            RenderTextureHandle temp = RenderTextureHandle.Make(TEMP_RT0_NAME, descriptor.width, descriptor.height);

            cmd.GetTemporaryRT(temp.NameId, temp.Width, temp.Height);
            cmd.Blit(args.ColorAttachment, temp.RtId, _renderMat);
            cmd.Blit(temp.RtId, args.ColorAttachment);
            cmd.ReleaseTemporaryRT(temp.NameId);
        }

        /*
        private void OnRenderImage(RenderTexture source, RenderTexture destination)
        {
            if (_renderMat == null)
            {
                Graphics.Blit(source, destination);
                return;
            }

            Graphics.Blit(source, destination, _renderMat);
        }
        */
    }
}