using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using static Gallop.StaticVariableDefine.Parts.CircleItemRequest;

namespace Gallop
{
    /// <summary>
    /// サークル：共有リスト
    /// ** 現状アイテムリクエストのみ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCircleShareItemList : DialogInnerBase
    {

        public enum TabStatus
        {
            Item = 0,
            Partner,
            Max
        }

        /// <summary>
        /// 一括寄付設定
        /// </summary>
        private enum MultiDonateSettingType
        {
            All,            // 全て
            OnlyShoes,      // シューズのみ
            OnlyBlanket,    // レイのみ
        }

        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        [SerializeField]
        private List<GameObject> _contentObjectList = null;
        /// <summary> サークルチャット用UI(indexをTabStatusと合わせること) </summary>
        [SerializeField]
        private List<CircleChatScrollUI> _chatScrollList = null;
        /// <summary> アイテムリクエストの一括寄付用のボタン </summary>
        [SerializeField]
        private ButtonCommon _itemRequestMultiDonateButton = null;
        /// <summary> 一括寄付設定 </summary>
        [SerializeField]
        private PartsCircleItemMultiDonateSetting _multiDonateSetting = null;

        /// <summary> アイテムリクエスト情報 </summary>
        private List<WorkCircleChatData.ItemRequestInfo> _itemRequestInfoList = null;

        #region API

        /// <summary>
        /// 指定タブで表示するリストを取得するAPIを実行しonFinishに渡す
        /// </summary>
        /// <param name="targetTab"></param>
        /// <param name="onFinish"></param>
        /// <param name="onError"></param>
        public static void SendGetListApi(TabStatus targetTab, 
            System.Action<(List<WorkCircleChatData.ChatMessageInfo>, List<WorkCircleChatData.PracticePartnerInfo>)> onFinish,
            Action<(Cute.Http.ErrorType Type, int ResultCode)> onError = null)
        {
            switch (targetTab)
            {
                case TabStatus.Item:
                    var itemList = new List<WorkCircleChatData.ChatMessageInfo>();
                    var itemListReq = new CircleItemRequestGetRequestDataRequest();
                    //サークルではポーリング通信で通信をスタックできるので、明示的にロックと通信中表示を出す
                    Cute.Http.HttpManager.Instance.SendAfterLockUI(
                        itemListReq,
                        (CircleItemRequestGetRequestDataResponse res) => 
                        {
                            itemList = ToChatMessageList(res.data.circle_item_request_array, res.data.circle_item_donate_array);
                            onFinish((itemList, null));
                        });
                    //チャット更新
                    CircleUtil.UpdateChat();
                    break;
                case TabStatus.Partner:
                    var partnerListReq = new CircleGetPostPartnerDataRequest();
                    //サークルではポーリング通信で通信をスタックできるので、明示的にロックと通信中表示を出す
                    Cute.Http.HttpManager.Instance.SendAfterLockUI(
                        partnerListReq,
                        (CircleGetPostPartnerDataResponse res) => 
                        {
                            List<WorkCircleChatData.PracticePartnerInfo> practicePartnerList = new List<WorkCircleChatData.PracticePartnerInfo>();
                            //練習パートナー共有の更新
                            for (int i = 0; i < res.data.circle_post_partner_array.Length; i++)
                            {
                                practicePartnerList.Add(new WorkCircleChatData.PracticePartnerInfo(res.data.circle_post_partner_array[i]));
                            }
                            //時間順でソート
                            practicePartnerList = practicePartnerList.OrderBy(info => info.Time).ToList();
                            onFinish((null, practicePartnerList));
                        },
                        (type, code) =>
                        {
                            onError?.Invoke((type, code));
                        });
                    break;
                default:
                    onFinish((null, null));
                    //チャット更新
                    CircleUtil.UpdateChat();
                    break;
            }


        }

        //アイテムリクエスト一覧レスポンスをチャットのメッセージリストに変換する
        private static List<WorkCircleChatData.ChatMessageInfo> ToChatMessageList(CircleItemRequest[] itemRequestDataArray, CircleItemDonate[] itemDonateDataArray)
        {
            //アイテムリクエスト有効時間を秒に変換
            var _expireTime = ServerDefine.ItemRequestExpire * 60 * 60;

            //チャットデータへの変換リスト
            var list = new List<WorkCircleChatData.ChatMessageInfo>();
            if (itemRequestDataArray == null)
                return list;

            //最新内容でワーク内部のアイテムリクエストを更新する
            WorkDataManager.Instance.CircleChatData.UpdateItemRequestList(itemRequestDataArray, itemDonateDataArray);

            //アイテムリクエストの一覧をメッセージの一覧に変換
            foreach(var data in WorkDataManager.Instance.CircleChatData.ItemRequestDic)
            {
                var itemReqInfo = data.Value;
                if (itemReqInfo.IsEnd)
                    continue;   //終わってるなら除外

                var startTimeStamp = TimeUtil.ToUnixTimeFromJstString(itemReqInfo.EndTime) - _expireTime;
                var messageInfo = new WorkCircleChatData.ChatMessageInfo(
                    WorkCircleChatData.MessageType.ITEM_REQUEST,
                    itemReqInfo.RequestId,// 102713 MessageIdはRequestIdで代用
                    itemReqInfo.ViewerId,
                    TimeUtil.GetDateTime(startTimeStamp),
                    itemRequestInfo: itemReqInfo);
                list.Add(messageInfo);
            }

            return list;
        }

        #endregion

        /// <summary>
        /// アイテムリクエストダイアログを開く
        /// </summary>
        public static void PushDialogItem()
        {
            if (CircleInternalDataManager.HasInstance() == false)
                return;

            PushDialog(TabStatus.Item);
        }

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void PushDialog(TabStatus tabStatus = TabStatus.Item, Action onClose = null, Action OnError = null)
        {
            SendGetListApi(tabStatus, onFinish:list => {
                GameObject instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CIRCLE_SHARE_ITEM_LIST));
                var component = instance.GetComponent<DialogCircleShareItemList>();
                var dialogData = component.CreateDialogData();
                switch (tabStatus)
                {
                    case TabStatus.Item:
                        dialogData.Title = TextId.Circle0233.Text();
                        break;
                    case TabStatus.Partner:
                        dialogData.Title = TextId.Circle249006.Text();
                        break;
                }
                dialogData.ContentsObject = instance;
                dialogData.CenterButtonText = TextId.Common0007.Text(); // 閉じる
                dialogData.CenterButtonCallBack = (dialog) =>
                {
                    onClose?.Invoke();
                };
                DialogManager.PushDialog(dialogData);
                component.Initialize(tabStatus, list.Item1, list.Item2);
            },
                onError:(onError) =>
            {
                // ウマ娘共有リストが移籍などで1→0になった
                if (onError.ResultCode == GallopResultCode.CIRCLE_PARTNER_LIST_COUNT_ZERO)
                {
                    var dialogData = new DialogCommon.Data();
                    var errorHeader = MasterDataManager.Instance.masterString.GetText(MasterString.Category.ErrorHeader, GallopResultCode.CIRCLE_PARTNER_LIST_COUNT_ZERO);
                    var errorMessage = MasterDataManager.Instance.masterString.GetText(MasterString.Category.ErrorMessage, GallopResultCode.CIRCLE_PARTNER_LIST_COUNT_ZERO);

                    dialogData.SetSimpleOneButtonMessage(
                        errorHeader,
                        errorMessage,
                        dialog =>
                        {
                            OnError?.Invoke();
                            onClose?.Invoke();
                        });
                    DialogManager.PushDialog(dialogData);
                }
                else
                {
                    onClose?.Invoke();
                }
            });
        }

        /// <summary>
        /// 初期化、最初の表示はダイアログ表示前にAPIが実行されるため初回表示はリストを外部からもらう
        /// </summary>
        private void Initialize(TabStatus defaultTab, List<WorkCircleChatData.ChatMessageInfo> listItem, List<WorkCircleChatData.PracticePartnerInfo> listPartner)
        {
            //デフォルトの表示内容を反映
            ApplyContent(defaultTab, listItem, listPartner);
        }

        private CircleChatScrollUI GetChatScroll(TabStatus tabStatus)
        {
            var index = (int)tabStatus;
            if (index < 0 || index >= _chatScrollList.Count)
                return null;

            return _chatScrollList[index];
        }

        /// <summary>
        /// 各オプションで表示する内容を反映する
        /// </summary>
        private void ApplyContent(TabStatus tab, List<WorkCircleChatData.ChatMessageInfo> listItem, List<WorkCircleChatData.PracticePartnerInfo> listPartner)
        {
            var tabIndex = (int)tab;
            for (int i = 0; i < (int)TabStatus.Max; ++i)
            {
                if (_contentObjectList.Count <= i)
                    break;
                if (_contentObjectList[i] == null)
                    continue;

                _contentObjectList[i].gameObject.SetActive(tabIndex == i);
            }

            if (tab == TabStatus.Partner)
            {
                var scrollCommon = _contentObjectList[(int) TabStatus.Partner].GetComponent<ScrollRectCommon>();
                var loopScroll = scrollCommon.content.gameObject.GetComponent<LoopScroll>();
                loopScroll.Setup<ShareItemLoopScrollItem>(listPartner.Count, (item) =>
                {
                    if (item != null)
                    {
                        var practicePartner = listPartner[item.ItemIndex];
                        item.UpdateItem(practicePartner);
                    }
                });
            }
            else
            {
                if (tab == TabStatus.Item)
                {
                    // アイテムリクエスト一括寄付ボタンの設定
                    SetupItemRequestMultiDonateButton(listItem);
                }

                var scroll = GetChatScroll(tab);
                if (scroll)
                {
                    scroll.CreateList(listItem, true, isChat: false);
                }
            }
        }

        /// <summary>
        /// 毎フレームの処理
        /// </summary>
        private void Update()
        {
            CheckMultiDonate();
        }

        #region 一括寄付

        /// <summary>
        /// アイテムリクエスト一括寄付ボタンの設定
        /// </summary>
        private void SetupItemRequestMultiDonateButton(List<WorkCircleChatData.ChatMessageInfo> listItem)
        {
            // アイテムリクエスト一覧
            // 自分のデータと最大数寄付済みのデータはあらかじめ除く
            _itemRequestInfoList = listItem
                .OrderBy(info => info.MessageId)
                .Where(info => info.ItemRequestInfo != null && info.ViewerId != Certification.ViewerId)
                .Select(info => info.ItemRequestInfo).ToList();

            // 一括寄付設定
            _multiDonateSetting.Setup();

            _itemRequestMultiDonateButton.SetOnClick(() => OnClickMultiDonateButton(listItem));
        }

        /// <summary>
        /// 一括寄付ボタンを押したときの処理
        /// </summary>
        private void OnClickMultiDonateButton(List<WorkCircleChatData.ChatMessageInfo> listItem)
        {
            var setupParamList = new List<PartsCircleItemRequestMultiConfirmListItem.SetupParameter>();

            var remainDonateCount = WorkDataManager.Instance.CircleData.RemainDonateCount;
            if (remainDonateCount == 0)
            {
                return;
            }

            var donateNumDict = new Dictionary<int, int>();// 寄付数計算用
            foreach (var chatMessageInfo in listItem)
            {
                if (chatMessageInfo.ItemRequestInfo == null
                || chatMessageInfo.ItemRequestInfo.IsMine()
                || chatMessageInfo.ItemRequestInfo.IsEnd)
                {
                    // 寄付できないアイテムリクエスト
                    continue;
                }

                var itemRequestInfo = chatMessageInfo.ItemRequestInfo;

                // 一括寄付設定の対象でないアイテムははじく
                if (!IsMatchMultiDonateSetting(itemRequestInfo.ItemId))
                {
                    continue;
                }

                // 寄付数を計算
                var donatedItemNum = donateNumDict.ContainsKey(itemRequestInfo.ItemId) ? donateNumDict[itemRequestInfo.ItemId] : 0;
                var itemNum = WorkDataManager.Instance.ItemData.GetHaveItemNum(itemRequestInfo.ItemId) - donatedItemNum;
                var donateNum = Mathf.Min(Mathf.Min(itemNum, ServerDefine.MaxDonateNumForRequest), itemRequestInfo.GetDonatableNum());
                if (donateNum <= 0)
                {
                    // アイテム数不足や寄付済みなどで寄付できない
                    continue;
                }

                var setupParam = new PartsCircleItemRequestMultiConfirmListItem.SetupParameter()
                {
                    UserName = chatMessageInfo.Name,
                    UserCharaId = chatMessageInfo.ChatCharaIds.CharaId,
                    UserDressId = chatMessageInfo.ChatCharaIds.DressId,
                    ItemRequestInfo = itemRequestInfo,
                    DonateNum = donateNum,
                };
                setupParamList.Add(setupParam);

                if (donateNumDict.ContainsKey(itemRequestInfo.ItemId))
                {
                    donateNumDict[itemRequestInfo.ItemId] += donateNum;
                }
                else
                {
                    donateNumDict.Add(itemRequestInfo.ItemId, donateNum);
                }

                // 残り寄付回数の更新
                remainDonateCount--;
                if (remainDonateCount == 0)
                {
                    // 寄付上限
                    break;
                }
            }

            if (setupParamList.Count == 0)
            {
                // 有効なアイテムリクエストがなかった(念のため)
                UIManager.Instance.ShowNotification(TextId.Circle400109.Text());
                return;
            }

            // 一括寄付設定を保存
            if (_multiDonateSetting.GetSelectIndex() != SaveDataManager.Instance.SaveLoader.CircleMultiDonateSettingTypeIndex)
            {
                var loader = SaveDataManager.Instance.SaveLoader;
                loader.CircleMultiDonateSettingTypeIndex = _multiDonateSetting.GetSelectIndex();
                loader.Save();
            }

            DialogCircleItemRequestMultiConfirm.Open(setupParamList, WorkDataManager.Instance.CircleData.RemainDonateCount);
        }

        /// <summary>
        /// 一括寄付ボタンのInteractive(内部で_itemRequestInfoListの変更あり)
        /// </summary>
        /// <returns></returns>
        private bool IsMultiDonateButtonInteractive()
        {
            if (_itemRequestInfoList == null)
            {
                return false;
            }

            if (WorkDataManager.Instance.CircleData.RemainDonateCount == 0)
            {
                // 寄付残数不足
                return false;
            }

            bool CanDonate(WorkCircleChatData.ItemRequestInfo info)
            {
                // 時間経過と自分の寄付で変動する分だけチェック
                return !info.IsEnd
                    && WorkDataManager.Instance.ItemData.GetHaveItemNum(info.ItemId) > 0
                    && !info.IsDonatedFromMe;
            }

            var hasRemovableData = false;
            void OnLoopEnd()
            {
                if (hasRemovableData)
                {
                    _itemRequestInfoList.RemoveAll(info => !CanDonate(info));
                }
            }

            for (int i = 0; i < _itemRequestInfoList.Count; i++)
            {
                // 一括寄付設定の対象ではない場合はチェックしない
                if (!IsMatchMultiDonateSetting(_itemRequestInfoList[i].ItemId))
                {
                    continue;
                }

                if (CanDonate(_itemRequestInfoList[i]))
                {
                    // _itemRequestInfoListには現在寄付可能な要素しか入れないので、基本的には1つめの要素でtrueになる想定
                    OnLoopEnd();
                    return true;
                }
                else
                {
                    hasRemovableData = true;
                }
            }

            OnLoopEnd();
            return false;
        }

        /// <summary>
        /// 一括寄付が有効かどうか
        /// </summary>
        private void CheckMultiDonate()
        {
            if (_itemRequestInfoList == null)
            {
                // 未初期化またはアイテムリクエストではない
                return;
            }
            
            if (!IsMultiDonateButtonInteractive())
            {
                _itemRequestMultiDonateButton.SetInteractable(false);
                _itemRequestMultiDonateButton.SetNotificationMessage(TextId.Circle400109.Text());
            }
            else
            {
                _itemRequestMultiDonateButton.SetInteractable(true);
                _itemRequestMultiDonateButton.SetNotificationMessage(string.Empty);
            }
        }

        /// <summary>
        /// 指定のアイテムが一括寄付設定の対象かどうか
        /// </summary>
        /// <param name="itemId"></param>
        /// <returns></returns>
        private bool IsMatchMultiDonateSetting(int itemId)
        {
            int groupId = MasterDataManager.Instance.masterItemGroup.Get(itemId).GroupId;
            switch ((MultiDonateSettingType)_multiDonateSetting.GetSelectIndex())
            {
                // レイのみ
                case MultiDonateSettingType.OnlyBlanket:
                    if (groupId != (int)ItemGroupId.Blanket) return false;
                    break;

                // シューズのみ
                case MultiDonateSettingType.OnlyShoes:
                    if (groupId != (int)ItemGroupId.Shoes) return false;
                    break;
            }

            return true;
        }

        #endregion
    }
}
