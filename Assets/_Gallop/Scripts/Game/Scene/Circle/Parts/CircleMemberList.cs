using UnityEngine;
using UnityEngine.UI;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using CodeStage.AntiCheat.ObscuredTypes;

namespace Gallop
{
    /// <summary>
    /// サークルメンバーリスト
    /// </summary>
    [AddComponentMenu("")]
    public class CircleMemberList : MonoBehaviour
    {
        #region メンバー情報クラス

        /// <summary>
        /// メンバー情報
        /// </summary>
        public class MemberData
        {
            public int circleId;
            public CircleUser circleUser;
            public UserInfoAtFriend userInfo;
            private long _cachedLoginTime = -1;
            private long _cachedJoinTime = -1;
            private long _cachedLastCheckTimeJoinRequest;
            public bool IsJoinRequest { get; private set; }
            public bool IsScout { get; private set; }
            //通常メンバー
            public MemberData(CircleUser circleUser, UserInfoAtFriend userInfo)
            {
                this.circleUser = circleUser;
                this.userInfo = userInfo;
                circleId = circleUser.circle_id;
            }
            //加入申請 or スカウトユーザー
            public MemberData(UserInfoAtFriend userInfo, int circleId, bool isJoinRequest = false, bool isScout = false, string joinRequestTime = null)
            {
                this.userInfo = userInfo;
                this.circleUser = userInfo.circle_user;
                this.circleId = circleId;
                IsJoinRequest = isJoinRequest;
                IsScout = isScout;
                if(isJoinRequest && !string.IsNullOrEmpty(joinRequestTime))
                {
                    JoinRequestTime = TimeUtil.ToUnixTimeFromJstString(joinRequestTime);
                    UpdateLastCheckTimeJoinRequest();
                }
            }
            public MemberData(UserInfoAtFriend userInfo)
            {
                this.userInfo = userInfo;
            }

            public MemberData() { }

            //最終ログイン時刻のタイムスタンプ
            public long LastLoginTime
            {
                get
                {
                    if(_cachedLoginTime == -1)
                    {
                        _cachedLoginTime = TimeUtil.ToUnixTimeFromJstString(userInfo.last_login_time);
                    }
                    return _cachedLoginTime;
                }
            }
            //加入時刻のタイムスタンプ
            public long JoinTime
            {
                get
                {
                    if (_cachedJoinTime == -1 && circleUser != null)
                    {
                        _cachedJoinTime = TimeUtil.ToUnixTimeFromJstString(circleUser.join_time);
                    }
                    return _cachedJoinTime;
                }
            }

            //加入希望時刻
            public long JoinRequestTime { get; private set; }

            //New出すか？
            public bool IsNew
            {
                get
                {
                    if(JoinRequestTime > 0)
                    {
                        return JoinRequestTime > _cachedLastCheckTimeJoinRequest;
                    }
                    return false;
                }
            }
            
            //役職
            public CircleDefine.Role Role => circleUser != null ? (CircleDefine.Role)circleUser.membership : CircleDefine.Role.None;
            
            //最後に加入希望を見た時刻を更新
            public void UpdateLastCheckTimeJoinRequest() { _cachedLastCheckTimeJoinRequest = WorkDataManager.Instance.LastCheckTime.Get(WorkLastCheckTime.LastCheckTimeType.CircleJoinRequest); }
        }

        #endregion

        #region 定義、定数

        //-----------------------------------------------------------------------------------------
        // 定義
        //-----------------------------------------------------------------------------------------

        /// <summary>
        /// 表示タイプ
        /// </summary>
        public enum DispType
        {
            Normal,         // 通常(クラブ詳細など)
            Own,            // 所属メンバー用(メンバー管理へ)
            JoinRequest,    // 承認待ち
            Scout,          // スカウト一覧
            Follow,   // フォロー一覧
        }

        #endregion

        #region 変数

        //-----------------------------------------------------------------------------------------
        // SerializeField
        //-----------------------------------------------------------------------------------------

        [SerializeField]
        private LoopScroll _loopScroll = null;
        [SerializeField]
        private GameObject _numObject = null;
        [SerializeField]
        private TextCommon _numTitleText = null;
        [SerializeField]
        private TextCommon _numText = null;

        [SerializeField]
        private TextCommon _emptyText = null;

        [SerializeField]
        private ScrollRectCommon _scrollView = null;

        [field: SerializeField, RenameField]
        public FlickableObject FlickableObject { get; private set; } = null;
        
        /// <summary> ソートボタン </summary>
        [SerializeField]
        private PartsListSortButton _sortButton = null;

        //-----------------------------------------------------------------------------------------
        // 変数
        //-----------------------------------------------------------------------------------------
        public List<MemberData> MemberDataList { get; private set; }
        public DispType DisplayType { get; private set; }
        public bool IsCreated { get; private set; } = false;    //一度でも作成されればtrueになるフラグ

        private bool _onCircleDetail;
        private bool _fromTrainerInfo;

        //フォロー中のユーザーがスカウト中かを判定するのにスカウトのリストを別途受け取る
        private List<MemberData> _scoutList = null;
        //スカウト成功時コールバック
        private Action<CircleMemberListItem> _onScoutSuccess = null;
        //フレンド情報が変わった時のコールバック
        private Action<UserFriend> _onUpdateUserFriend = null;

        #endregion

        #region リスト生成

        /// <summary>
        /// リスト生成
        /// </summary>
        /// <param name="displayType"></param>
        /// <param name="onCircleDetail">これがtrueならユーザー情報表示時にサークル詳細へ遷移させない</param>
        /// <param name="memberDataList"></param>
        /// <param name="scoutList"></param>
        /// <param name="onScoutSuccess"></param>
        /// <param name="fromTrainerInfo"></param>
        public void CreateList(
            List<MemberData> memberDataList,
            DispType displayType,
            bool onCircleDetail = false,
            List<MemberData> scoutList = null,
            Action<CircleMemberListItem> onScoutSuccess = null,
            bool fromTrainerInfo = false,
            Action<UserFriend> onUpdateUserFriend = null)
        {
            MemberDataList = memberDataList;
            DisplayType = displayType;
            _onCircleDetail = onCircleDetail;
            _fromTrainerInfo = fromTrainerInfo;
            _onUpdateUserFriend = onUpdateUserFriend;
            if (scoutList != null)
            {
                _scoutList = scoutList;
            }
            if (onScoutSuccess != null)
            {
                _onScoutSuccess = onScoutSuccess;
            }

            // 表示形式によって、リストアイテムのUIを切り替える
            switch (displayType)
            {
                case DispType.Normal:         // 通常リスト
                    _numObject.SetActive(true);
                    _numTitleText.text = TextId.Circle0175.Text();
                    _numText.text = MemberDataList.Count + " / " + ServerDefine.MaxCircleMemberNum;
                    break;
                case DispType.Own:           // 所属メンバー用
                    _numObject.SetActive(true);
                    _numTitleText.text = TextId.Circle0175.Text();
                    _numText.text = MemberDataList.Count + " / " + ServerDefine.MaxCircleMemberNum;
                    break;
                case DispType.Scout:         //スカウト
                    _numObject.SetActive(true);
                    _numTitleText.text = TextId.Circle0255.Text();
                    _numText.text = MemberDataList.Count + " / " + ServerDefine.MaxCircleScoutNum;
                    break;
                case DispType.JoinRequest:      //承認待ち
                    _numObject.SetActive(true);
                    _numTitleText.text = TextId.Circle0178.Text();
                    _numText.text = TextUtil.Format(TextId.Common0270.Text(), MemberDataList.Count);
                    break;
                case DispType.Follow:     //フォロー一覧
                    _numObject.SetActive(false);
                    break;
            }

            _emptyText.text = GetEmptyText(DisplayType);
            IsCreated = true;   //空でも作成済みにしておく

            if (MemberDataList == null || MemberDataList.Count == 0)
            {
                //ユーザーが存在しない
                _emptyText.gameObject.SetActive(true);
                _loopScroll.gameObject.SetActive(false);
                _scrollView.verticalScrollbar.SetActiveWithCheck(false);
                _sortButton.SetActiveWithCheck(false);
                IsCreated = true;
                return;
            }

            _emptyText.gameObject.SetActive(false);
            _loopScroll.gameObject.SetActive(true);
            _scrollView.verticalScrollbar.SetActiveWithCheck(true);

            // ソート設定
            SortFilterSetting.SaveTag saveTag;
            var isDisplayScoutRequest = displayType == DispType.JoinRequest || displayType == DispType.Scout || displayType == DispType.Follow;
            if (isDisplayScoutRequest)
            {
                saveTag = SortFilterSetting.SaveTag.CircleMemberLight;
            }
            else
            {
                saveTag = SortFilterSetting.SaveTag.CircleMember;
            }
            
            var sortFilterSetting = SortFilterSetting.Load(saveTag);

            //メンバー一覧作成
            CreateMemberList(sortFilterSetting.Sort, sortFilterSetting.IsAsc);

            //ソートボタン生成
            _sortButton.SetActiveWithCheck(true);
            _sortButton.Initialize(
                sortFilterSetting,
                () =>
                {
                    SortFilterSetting.Save(saveTag, _sortButton.Setting);
                    CreateMemberList(_sortButton.Setting.Sort, _sortButton.Setting.IsAsc);
                }
             );
        }

        private void CreateMemberList(SortMenu menu, bool asc)
        {
            MemberDataList = SortMenuEx.SortCircleMemberList(MemberDataList, menu, asc);
            _loopScroll.Setup(MemberDataList.Count, item => {
                var memberItem = item as CircleMemberListItem;
                var data = GetMemberData(item.ItemIndex);
                if (data != null)
                {
                    memberItem.Set(data, _onCircleDetail, _fromTrainerInfo, _onUpdateUserFriend);
                    //表示モードごとの出しわけ
                    switch (DisplayType)
                    {
                        case DispType.Own:
                            if (data.circleUser.viewer_id == Certification.ViewerId)
                                //自分で自分を管理はできない
                                break;

                            var work = WorkDataManager.Instance.CircleData;
                            if (work.CircleId == data.circleId && CircleUtil.IsAuthPermited(work.GetRole(), CircleDefine.RoleAuth.ManageMember))
                            {
                                //自分のサークルかつ管理者権限があるなら表示
                                memberItem.SetAdminMenu(MemberDataList);
                            }
                            break;
                        case DispType.JoinRequest:
                            memberItem.SetJoinRequestUser();
                            break;
                        case DispType.Scout:
                            memberItem.SetScoutingUser();
                            break;
                        case DispType.Follow:
                            memberItem.SetScout(_scoutList, _onScoutSuccess);
                            break;
                    }
                }
            });
        }

        private MemberData GetMemberData(int index)
        {
            if (MemberDataList == null)
                return null;

            if (index < 0 || MemberDataList.Count <= index)
                return null;

            return MemberDataList[index];
        }

        /// <summary>
        /// リストが空の時のテキスト取得
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private string GetEmptyText(CircleMemberList.DispType type)
        {
            switch (type)
            {
                case CircleMemberList.DispType.JoinRequest:
                    return string.Format(TextId.Circle0129.Text(), TextId.Friend0008.Text());
                case CircleMemberList.DispType.Scout:
                    return string.Format(TextId.Circle0129.Text(), TextId.Circle0128.Text());
                case DispType.Follow:
                case DispType.Normal:
                case DispType.Own:
                    return "";  //自分またはフレンドのサークルが空になることは想定してない。
            }

            Debug.LogWarning(type + ": 未対応のtype");
            return "";
        }

        #endregion

        #region リスト操作

        /// <summary>
        /// リストからユーザーを削除
        /// </summary>
        /// <param name="viewerId"></param>
        public void RemoveUser(int viewerId)
        {
            if (MemberDataList == null)
                return;

            var targetIndex = MemberDataList.FindIndex(d => d.userInfo != null && d.userInfo.viewer_id == viewerId);
            if (targetIndex < 0)
                return;

            MemberDataList.RemoveAt(targetIndex);
            CreateList(MemberDataList, DisplayType);
        }

        /// <summary>
        /// 表示・非表示
        /// </summary>
        /// <param name="visible"></param>
        public void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
                _sortButton.SetActiveWithCheck(visible);
        }

        #endregion
    }
}
