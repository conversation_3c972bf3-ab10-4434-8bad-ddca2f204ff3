using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using static Gallop.CraneGameDefines;

namespace Gallop
{
    /// <summary>
    /// ViewInfo
    /// </summary>
    [AddComponentMenu("")]
    public class CraneGameViewInfo : IViewInfo
    {
        public List<int> PrizeCharaList { get; }
        public CharaDressIdSet WipeCharaDressIdSet { get; }
        public Action OnGameEnd { get; }
        public Action<GameResult, CraneGameInfo.PrizeInfoSaveList, Action> OnResult { get; }
        public bool IsSingleMode { get; }

        /// <summary>
        /// クレーンゲーム開始に必要な情報
        /// </summary>
        /// <param name="prizeCharaList"></param>
        /// <param name="wipeCharaDressIdSet"></param>
        /// <param name="onResult">第３引数のActionは通信終了時などに呼んでもらうための関数</param>
        /// <param name="onGameEnd"></param>
        public CraneGameViewInfo(
            List<int> prizeCharaList, 
            CharaDressIdSet wipeCharaDressIdSet, 
            Action<GameResult, CraneGameInfo.PrizeInfoSaveList, Action> onResult = null, 
            Action onGameEnd = null,
            bool isSingleMode = true
        )
        {
            //景品キャラはサポートカードからも指定されるため、プレイアブルでない可能性がある
            //プレイアブルなキャラかつ、指定の数に満ちるまで
            PrizeCharaList = new List<int>();
            foreach(var chara in prizeCharaList)
            {
                if (MasterDataUtil.IsAvailableChara(chara))
                {
                    PrizeCharaList.Add(chara);
                }
            }
            //足りない場合は足りるまでランダムに追加する
            if(PrizeCharaList.Count < PRIZE_CHARA_NUM)
            {
                var allCharaList = MasterDataManager.Instance.masterCharaData.GetCharacterDataListShuffle(true);
                if (allCharaList.Count >= PRIZE_CHARA_NUM)
                {
                    var index = 0;
                    while (PrizeCharaList.Count < PRIZE_CHARA_NUM)
                    {
                        var chara = allCharaList[index];
                        if(PrizeCharaList.Contains(chara.Id) == false)
                        {
                            PrizeCharaList.Add(chara.Id);
                        }
                        index++;
                    }
                }
                else
                {
                    Debug.LogError("プレイアブルが少な過ぎる count = "+allCharaList + " (最低"+PRIZE_CHARA_NUM +")体要る");
                }
            }
            WipeCharaDressIdSet = wipeCharaDressIdSet;
            OnResult = onResult;
            OnGameEnd = onGameEnd;
            IsSingleMode = isSingleMode;
        }
    }
}
