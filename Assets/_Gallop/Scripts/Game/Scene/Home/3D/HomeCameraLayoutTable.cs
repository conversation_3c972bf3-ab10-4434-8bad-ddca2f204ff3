using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cinemachine;

namespace Gallop
{
    [System.Serializable]
    [AddComponentMenu("")]
    public class CameraLayoutVirtualCameraPair : KeyAndValue<HomeDefine.CameraPos, CinemachineVirtualCamera>
    {
        public CameraLayoutVirtualCameraPair(HomeDefine.CameraPos key, CinemachineVirtualCamera value) : base(key, value)
        {
        }

        public override KeyAndValue<HomeDefine.CameraPos, CinemachineVirtualCamera> CreateCopyInstance()
        {
            return new CameraLayoutVirtualCameraPair(Key, Value);
        }
    }

    [System.Serializable]
    public class HomeCameraLayoutTable : TableBase<HomeDefine.CameraPos, CinemachineVirtualCamera, CameraLayoutVirtualCameraPair> { }
}