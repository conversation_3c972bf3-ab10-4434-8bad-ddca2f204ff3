using System;

namespace Gallop
{
    /// <summary>
    /// ホーム：3次ベジエ曲線用の制御データ
    /// </summary>
    public class HomeCubicBezierPathAsset : CubicBezierPathAsset
    {
        /// <summary>
        /// メタ情報
        /// </summary>
        [System.Serializable]
        public class MetaInfo
        {
            /// <summary> 階段かどうか </summary>
            public bool IsStair;
        }

        /// <summary>
        /// パスのメタ情報
        /// </summary>
        public MetaInfo[] MetaInfoArray = null;
        
        /// <summary>
        /// パスを通れる最大人数
        /// </summary>
        public int MaxCharacter = 1;

        /// <summary>
        /// 食べ歩きできるルートか？
        /// </summary>
        public bool IsEnableEat = false;

        /// <summary>
        /// 距離に対応する配列インデックスを取得
        /// </summary>
        /// <param name="length"></param>
        /// <returns></returns>
        public int GetInfoArrayIndex(float length)
        {
            if (_runtimeDataArray == null)
            {
                // ランタイムデータがなければ生成
                CreateRuntimeData();
            }

            DebugUtils.Assert(_runtimeDataArray.Length == InfoArray.Length * SPLIT_COUNT + 1, "不正なアセット");

            if (length <= 0f)
            {
                return 0;
            }

            for (int index = 1; index < InfoArray.Length; index++)
            {
                var data = _runtimeDataArray[index * SPLIT_COUNT];
                if (length <= data.LengthFromStart)
                {
                    return index - 1;
                }
            }
            return InfoArray.Length - 1;
        }

        /// <summary>
        /// メタ情報配列を更新
        /// </summary>
        public void UpdateMetaInfoLength()
        {
            if (MetaInfoArray == null)
            {
                MetaInfoArray = new MetaInfo[0];
            }

            int infoLength = InfoArray.Length;
            int metaInfoLength = MetaInfoArray.Length;
            if (infoLength != metaInfoLength)
            {
                Array.Resize(ref MetaInfoArray, infoLength);
            }
            for (int index = 0; index < MetaInfoArray.Length; ++index)
            {
                if (MetaInfoArray[index] == null)
                {
                    MetaInfoArray[index] = new MetaInfo();
                }
            }
        }
    }
}