
namespace Gallop
{
    /// <summary>
    /// ホームイベント：フッターキャラのリップシンク開始イベント
    /// </summary>
    public class HomeStandModelLipSyncEventInfo
    {
        public HomeDefine.StandPos StandPos { get; }
        public CharacterBuildInfo BuildInfo { get; }
        public MasterCharacterSystemText.CharacterSystemText SystemText { get; }
        
        private HomeStandModelLipSyncEventInfo() { }

        public HomeStandModelLipSyncEventInfo(
            HomeDefine.StandPos standPos, 
            MasterCharacterSystemText.CharacterSystemText systemText,
            CharacterBuildInfo buildInfo)
        {
            StandPos = standPos;
            BuildInfo = buildInfo;
            SystemText = systemText;
        }
    }
}