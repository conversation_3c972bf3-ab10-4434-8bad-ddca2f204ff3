using UnityEngine;
using System;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// 課金アイテム一覧画面のアイテム要素(サポカ販売用)
    /// </summary>
    [AddComponentMenu("")]
    public class PartsBuyJewelListSupportCard : PartsBuyJewelListItem
    {
        #region SerializeField

        // サポカボタンベース
        [SerializeField]
        private CharacterButton _supportCardButtonBase;

        /// <summary>
        /// アイテムアイコンベース
        /// </summary>
        [SerializeField]
        private ItemIcon _itemIconBase;

        /// <summary>
        /// 購入回数制限アイコン
        /// </summary>
        [SerializeField]
        private GameObject _buyLimitIcon;

        /// <summary>
        /// サポカ所持状況確認ボタン
        /// </summary>
        [SerializeField]
        private ButtonCommon _supportCardStatusButton;

        #endregion

        #region const

        private readonly Vector2 TEXT_NUM_POSITION = new Vector2(104f, 94f);
        private readonly Vector2 TEXT_NUM_POSITION_ENPHASIZE = new Vector2(112f, 94f);

        #endregion

        #region Method

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="purchaseItem">商品アイテム情報</param>
        /// <param name="onComplete">完了時コールバック</param>
        /// <param name="evacuationCanvas">退避先のキャンバス</param>
        public override void Setup(PurchaseItem purchaseItem, Action onComplete, Canvas evacuationCanvas)
        {
            Debug.Assert(purchaseItem.IsItemPack, "セット販売以外のアイテムが渡されました");

            base.Setup(purchaseItem, onComplete, evacuationCanvas);

            // アイテムパックアイコンのセットアップ
            SetupItemPackIcon(purchaseItem);

            // 購入回数制限アイコンのセットアップ
            SetupBuyLimitIcon(purchaseItem);

            // サポカ商品のステータスボタンのコールバック設定
            _supportCardStatusButton.SetOnClick(() => { DialogSupportCardItemPackStatus.Open(purchaseItem); });
        }

        /// <summary>
        /// アイテムパックに含まれるアイテムアイコンを設定
        /// </summary>
        /// <param name="purchaseItem">商品アイテム情報</param>
        private void SetupItemPackIcon(PurchaseItem purchaseItem)
        {
            var itemPackArray = MasterDataManager.Instance.masterItemPack.GetListWithItemPackIdOrderByIdAsc(purchaseItem.ItemPackId).ToArray();

            SetupSupportCardButton(itemPackArray);
            SetupItemIcon(itemPackArray, purchaseItem.ChargedCount);
        }

        /// <summary>
        /// アイテムパックに含まれるサポカ商品のボタンを設定
        /// </summary>
        /// <param name="itemPackArray">ItemPack情報配列</param>
        private void SetupSupportCardButton(MasterItemPack.ItemPack[] itemPackArray)
        {
            // サポカ商品を取得
            var supportCardArray = itemPackArray.Where(x => x.ItemCategory == (int)GameDefine.ItemCategory.SUPPORT_CARD).ToArray();
            // 必要なサポカボタンを生成して取得
            var supportCardButtonArray = GetInstantiatedArray(_supportCardButtonBase, supportCardArray.Length);

            for (var i = 0; i < supportCardArray.Length; i++)
            {
                var itemPack = supportCardArray[i];
                var supportCardButton = supportCardButtonArray[i];
                var masterSupportCard = MasterDataManager.Instance.masterSupportCardData.Get(itemPack.ItemId);

                var info = new CharacterButtonInfo(itemPack.ItemId, CharacterButtonInfo.IdTypeEnum.Support) 
                {
                    IconSizeType = IconBase.SizeType.SupportCard_63,
                    EnableRarity = true,
                    EnableObtain = true,
                    EnableButton = true,
                    Rarity = masterSupportCard.Rarity,
                    OnLongTap = ShowSupportCardDetail
                };

                supportCardButton.Setup(info);

                var fontSize = TextFormat.FontSize.None;
                var fontColor = VerticalGradientColorType.None;
                var numPosition = TEXT_NUM_POSITION;
                if (itemPack.ItemNum == 1)
                {
                    // 1枚だけ獲得できるサポカは強調表示する
                    fontSize = TextFormat.FontSize.Size_36;
                    fontColor = VerticalGradientColorType.Emphasis;
                    numPosition = TEXT_NUM_POSITION_ENPHASIZE;
                }

                supportCardButton.SetNumText(itemPack.ItemNum, true, true, fontSize, fontColor);
                supportCardButton.SetNumPosition(numPosition);

                void ShowSupportCardDetail(CharacterButton button)
                {
                    var supportCardData = new WorkSupportCardData.SupportCardData(button.Id);
                    DialogSupportCardDetail.Open(supportCardData, enableMaxButton: true);
                }
            }
        }

        /// <summary>
        /// アイテムパックに含まれるサポカ以外商品のアイコンを設定
        /// </summary>
        /// <param name="itemPackArray">ItemPack情報配列</param>
        /// <param name="chargedCount">有償ジュエル付与数</param>
        private void SetupItemIcon(MasterItemPack.ItemPack[] itemPackArray, int chargedCount)
        {
            // サポカ以外の商品を取得
            var itemArray = itemPackArray.Where(x => x.ItemCategory != (int)GameDefine.ItemCategory.SUPPORT_CARD).ToArray();

            // 有償ジュエルが付与される場合は先頭に設定
            if (chargedCount > 0)
            {
                itemArray = new[] { new MasterItemPack.ItemPack(itemId: GameDefine.CALOTTE_STONE_ID, itemCategory: (int)GameDefine.ItemCategory.CARROT, itemNum: chargedCount) }
                    .Concat(itemArray)
                    .ToArray();
            }

            // 必要なアイテムアイコンを生成して取得
            var itemIconArray = GetInstantiatedArray(_itemIconBase, itemArray.Length);

            for (var i = 0; i < itemArray.Length; i++)
            {
                var itemPack = itemArray[i];
                var itemIcon = itemIconArray[i];

                itemIcon.SetData(
                    itemPack.ItemCategory,
                    itemPack.ItemId,
                    itemPack.ItemNum,
                    isInfoPop: true
                );
                itemIcon.SetButtonEnabled(true); // アイテムアイコン長押しでミニ詳細を出す
            }
        }

        /// <summary>
        /// 指定したオブジェクトを表示に必要な分生成して返却する
        /// </summary>
        /// <param name="baseObject">ベースオブジェクト</param>
        /// <param name="count">表示数</param>
        private T[] GetInstantiatedArray<T>(T baseObject, int count) where T : MonoBehaviour
        {
            var parent = baseObject.transform.parent;
            var objectArray = parent.GetComponentsInChildren<T>().Where(obj => obj != baseObject).ToArray();

            // 一旦全て非表示にする
            foreach (var obj in objectArray)
            {
                obj.SetActiveWithCheck(false);
            }

            for (var i = 0; i < count; i++)
            {
                if (i >= objectArray.Length)
                {
                    var obj = Instantiate(baseObject, parent);
                    obj.SetActiveWithCheck(true);
                }
                else
                {
                    objectArray[i].SetActiveWithCheck(true);
                }
            }

            return parent.GetComponentsInChildren<T>().Where(obj => obj.gameObject.activeSelf).ToArray();
        }

        /// <summary>
        /// 購入回数制限アイコンを設定
        /// </summary>
        /// <param name="purchaseItem">商品アイテム情報</param>
        private void SetupBuyLimitIcon(PurchaseItem purchaseItem)
        {
            _buyLimitIcon.SetActiveWithCheck(purchaseItem.HasLimitCount);
        }

        /// <summary>
        /// 購入確認ダイアログオープン
        /// </summary>
        protected override void OnPurchaseConfirm()
        {
            // 1枚のみのサポカ商品が含まれてる場合に誤認回避のため確認ダイアログを表示する
            DialogSupportCardItemPackConfirm.Open(_purchaseItem, () =>
            {
                DialogConfirmBuySupportCard.Open(_purchaseItem, OnPurchase, _evacuationCanvas);
            });
        }

        #endregion
    }
}
