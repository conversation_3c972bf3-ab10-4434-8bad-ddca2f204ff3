using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// サポカセット販売購入確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogConfirmBuySupportCard : DialogInnerBase
    {
        private const string DIALOG_CONFIRM_BUY_SUPPORT = ResourcePath.SHOP_PARTS_ROOT + "DialogConfirmBuySupportCard";

        private const float MESSAGE_TEXT_HEIGHT_LINE_1 = 36f; // 表示メッセージが1行の場合の高さ
        private const float MESSAGE_TEXT_OFFSET_LINE_2 = 47f; // 2行表示時の表示メッセージオフセット

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region SerializeFiel

        /// <summary> 本文 </summary>
        [SerializeField]
        private TextCommon _messageText;

        /// <summary> 提供期間 </summary>
        [SerializeField]
        private TextCommon _endDateText;

        /// <summary> 特商法ボタン </summary>
        [SerializeField]
        private ButtonCommon _tokuteishotorihikiButton;

        /// <summary> 商品詳細ボタン </summary>
        [SerializeField]
        private ButtonCommon _detailButton;

        #endregion

        /// <summary>
        /// ダイアログ表示
        /// </summary>
        public static void Open(PurchaseItem purchaseItem, Action onRight, Canvas evacuationCanvas = null)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(DIALOG_CONFIRM_BUY_SUPPORT));
            var component = instance.GetComponent<DialogConfirmBuySupportCard>();
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.Shop608003.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.AutoClose = true;
            dialogData.RightButtonText = TextId.Shop0100.Text();
            dialogData.RightButtonCallBack = (_) => { onRight?.Invoke(); };
            dialogData.EvacuationCanvas = evacuationCanvas;
            dialogData.EnableEvacuation = evacuationCanvas != null;
            component.Setup(purchaseItem);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// ダイアログデータ構築
        /// </summary>
        private void Setup(PurchaseItem purchaseItem)
        {
            var itemName = TextUtil.ReplaceSpace(purchaseItem.Name);
            itemName = TextUtil.Format(TextId.Shop608005.Text(), purchaseItem.FormattedPrice) + itemName;

            _messageText.text = TextUtil.Format(TextId.Shop497002.Text(), itemName);
            var preferredHeight = _messageText.preferredHeight;
            // 表示メッセージが2行以上になる場合は、メッセージのY座標を調整し「を購入します」部分を2行目に表示
            if (preferredHeight > MESSAGE_TEXT_HEIGHT_LINE_1)
            {
                _messageText.text = TextUtil.Format(TextId.Shop497001.Text(), itemName);
                AddMessageTextPosY(MESSAGE_TEXT_OFFSET_LINE_2);
            }

            _detailButton.SetOnClick(() =>
            {
                WebViewManager.Instance.OpenPurchaseItemDetail(purchaseItem);
            });
            _endDateText.SetActiveWithCheck(purchaseItem.HasLimitDate);
            _endDateText.text = TextUtil.Format(TextId.Gacha497001.Text(), TimeUtil.ToDispString(TimeUtil.FromUnixTimeToLocaleTime(purchaseItem.EndTime)));

            _tokuteishotorihikiButton.SetOnClick(PaymentUtility.OpenTokushoWebviewDialog);
        }

        /// <summary>
        /// 本文のY座標を加算
        /// </summary>
        private void AddMessageTextPosY(float addY)
        {
            var pos = _messageText.rectTransform.anchoredPosition;
            pos.y += addY;
            _messageText.rectTransform.anchoredPosition = pos;
        }
    }
}
