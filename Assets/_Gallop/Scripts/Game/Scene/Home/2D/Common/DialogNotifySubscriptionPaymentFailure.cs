#if UNITY_ANDROID || UNITY_IOS || (CYG_DEBUG && UNITY_EDITOR)
using UnityEngine;
using System;
using Cute.Payment;

namespace Gallop
{
    /// <summary>
    /// ウマスク関連決済失敗通知ダイアログ（AccountHold、多重決済可能性）
    /// </summary>
    [AddComponentMenu("")]
    public class DialogNotifySubscriptionPaymentFailure : DialogInnerBase
    {
        private const int SMALL_MAX_LINE_COUNT = 10; // ダイアログのサイズがSMALLになる最大行数

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType()
        {
            return _lineCount > SMALL_MAX_LINE_COUNT ? DialogCommonBase.FormType.MIDDLE_TWO_BUTTON : DialogCommonBase.FormType.SMALL_TWO_BUTTON;
        }

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region SerializeField

        [SerializeField]
        private TextCommon _text = null;

        #endregion

        /// <summary>
        /// 文言の行数
        /// </summary>
        private int _lineCount = 0;

        #region Method

        /// <summary>
        /// ダイアログ表示
        /// </summary>
        public static void Open(string title, string message, Action onFinish = null)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_NOTIFY_SUBSCRIPTION_PAYMENT_FAILURE));
            var component = instance.GetComponent<DialogNotifySubscriptionPaymentFailure>();
            var data = component.Setup(title, message, onFinish);
            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// ダイアログデータ構築
        /// </summary>
        private DialogCommon.Data Setup(string title, string message, Action onFinish)
        {
            _lineCount = TextUtil.GetCountChar(message, TextUtil.NEW_LINE_CHAR) + 1;
            TempData.Instance.ShopSubscriptionData.IsRestoreFromSubscriptionFailureDialog = false;
            var data = base.CreateDialogData();
            data.Title = title;
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = d => d.Close();
            data.DestroyCallBack = onFinish;
            data.EnableOutsideClick = false;
            data.OnPushBackKey = () => true; // バックキー無効
#if UNITY_IOS
            data.RightButtonText = TextId.Shop497006.Text(); // リストア
            data.RightButtonCallBack = d =>
            {
                TempData.Instance.ShopSubscriptionData.IsRestoreFromSubscriptionFailureDialog = true;
                d.Close();
                OnRestore();
            };
#elif UNITY_ANDROID || (CYG_DEBUG && UNITY_EDITOR)
            data.RightButtonText = TextId.Shop626008.Text(); // 定期購入管理へ
            data.RightButtonCallBack = d => Application.OpenURL(GameDefine.GOOGLE_PLAY_SUBSCRIPTIONS_URL);
#endif

            _text.text = message;

            return data;
        }

#if UNITY_IOS
        private void OnRestore()
        {
            UIManager.Instance.LockGameCanvas(); // リストア完了時にUnlockされる
            PaymentImpl.Instance.RefreshReceipt();
        }
#endif

        #endregion
    }
}
#endif
