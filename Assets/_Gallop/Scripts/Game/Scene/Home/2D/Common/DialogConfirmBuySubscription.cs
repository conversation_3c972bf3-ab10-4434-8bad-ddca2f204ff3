using System;
using UnityEngine;
using Cute.Payment;

namespace Gallop
{
    /// <summary>
    /// ウマスク購入確認ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogConfirmBuySubscription : DialogInnerBase
    {
        #region readonly

#if UNITY_IOS
        private static readonly Vector2 INFO_ROOT_POS = new Vector2(0, 0);
#else
        private static readonly Vector2 INFO_ROOT_POS = new Vector2(0, -70);
#endif

        #endregion

        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region SerializeField

        /// <summary> 情報のルート </summary>
        [SerializeField]
        private RectTransform _infoRootRect;

        /// <summary> 本文 </summary>
        [SerializeField]
        private TextCommon _messageText;

        /// <summary> 有効期間 </summary>
        [SerializeField]
        private TextCommon _availablePeriodText;

        /// <summary> 商品詳細ボタン </summary>
        [SerializeField]
        private ButtonCommon _detailButton;

        /// <summary> 警告文 </summary>
        [SerializeField]
        private RectTransform _warningTextTransform;

        /// <summary> リストアボタン </summary>
        [SerializeField]
        private ButtonCommon _restoreButton;

        /// <summary> 特商法ボタン </summary>
        [SerializeField]
        private ButtonCommon _tokuteishotorihikiButton;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログ表示
        /// </summary>
        public static void Open(PurchaseItem purchaseItem, Action onRight, Canvas evacuationCanvas = null)
        {
            var instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CONFIRM_BUY_SUBSCRIPTION));
            var component = instance.GetComponent<DialogConfirmBuySubscription>();
            var data = component.Setup(purchaseItem, onRight);
            data.EvacuationCanvas = evacuationCanvas;
            data.EnableEvacuation = evacuationCanvas != null;
            DialogManager.PushDialog(data);
        }

        /// <summary>
        /// ダイアログデータ構築
        /// </summary>
        private DialogCommon.Data Setup(PurchaseItem purchaseItem, Action onRight)
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Common626007.Text();
            data.LeftButtonText = TextId.Common0004.Text();
            data.LeftButtonCallBack = d => d.Close();

            var subscriptionData = WorkDataManager.Instance.PaymentSubscriptionData;
            if (!subscriptionData.IsEnableSubscription())
            {
                data.RightButtonText = TextId.Shop0100.Text();
                data.RightButtonCallBack = _ => onRight.Invoke();
            }
            else
            {
                data.RightButtonText = TextId.Shop0120.Text();
                data.RightButtonDefaultInteractable = false;
            }

            var itemName = TextUtil.ReplaceSpace(purchaseItem.Name);
            _messageText.text = TextUtil.Format(TextId.Shop497002.Text(), TextUtil.ReplaceSpace(itemName));
            _availablePeriodText.text = TextId.Shop626005.Text();

#if UNITY_IOS   // リストアはiOSのみ
            _restoreButton.SetOnClick(OnRestore);
#else
            _warningTextTransform.SetActiveWithCheck(false);
            _restoreButton.SetActiveWithCheck(false);
#endif
            _infoRootRect.anchoredPosition = INFO_ROOT_POS;
            _tokuteishotorihikiButton.SetOnClick(PaymentUtility.OpenTokushoWebviewDialog);
            _detailButton.SetOnClick(() => { WebViewManager.Instance.OpenPurchaseItemDetail(purchaseItem); });

            return data;
        }

#if UNITY_IOS
        private void OnRestore()
        {
            UIManager.Instance.LockGameCanvas(); // リストア完了時にUnlockされる
            PaymentImpl.Instance.RefreshReceipt();
        }
#endif

        #endregion
    }
}