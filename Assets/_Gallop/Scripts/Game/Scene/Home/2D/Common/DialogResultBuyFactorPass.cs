using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 因子系パスの購入完了ダイアログ
    /// 因子再獲得パス、指定因子パス
    /// </summary>
    [AddComponentMenu("")]
    public class DialogResultBuyFactorPass : DialogInnerBase
    {
        private const string DIALOG_RESULT_BUY_FACTOR_PASS = ResourcePath.SHOP_PARTS_ROOT + "DialogResultBuyFactorPass";
        
        #region SerializeField

        /// <summary> 画像 </summary>
        [SerializeField] private RawImageCommon _image;

        /// <summary> 通知文言 </summary>
        [SerializeField] private TextCommon _messageText;

        #endregion

        #region override

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();

            data.Title = TextId.Shop0083.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            data.CenterButtonColor = DialogCommon.ButtonColor.White;
            data.OpenSe = AudioId.SFX_UI_GET_01;

            return data;
        }

        #endregion

        /// <summary>
        /// 因子再獲得パス購入完了ダイアログを開く
        /// </summary>
        public static void OpenFactorPremiumPass(Action onClose)
        {
            Open(onClose).Setup(StaticVariableDefine.Payment.FACTOR_PREMIUM_PASS_ID, TextId.SingleMode539035.Text());
        }
        
        /// <summary>
        /// 指定因子パス購入完了ダイアログを開く
        /// </summary>
        public static void OpenFactorOrderPass(Action onClose)
        {
            Open(onClose).Setup(StaticVariableDefine.Payment.FACTOR_ORDER_PASS_ID, TextId.SingleMode194106.Text());
        }
        
        /// <summary>
        /// ダイアログを開く
        /// </summary>
        private static DialogResultBuyFactorPass Open(Action onClose)
        {
            var instance = LoadAndInstantiatePrefab<DialogResultBuyFactorPass>(DIALOG_RESULT_BUY_FACTOR_PASS);
            var data = instance.CreateDialogData();
            data.AddDestroyCallback(() => onClose?.Invoke());
            DialogManager.PushDialog(data);
            return instance;
        }


        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(int shopDataId, string shopDataName)
        {
            var path = ResourcePath.GetCarrotStoneIconPath(shopDataId);
            _image.texture = ResourceManager.LoadOnHash<Texture2D>(path, DialogHash);
            _messageText.text = TextId.Shop0121.Format(shopDataName);
        }
    }
}