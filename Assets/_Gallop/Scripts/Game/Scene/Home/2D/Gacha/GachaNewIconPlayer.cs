using System;
using AnimateToUnity;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャリザルトのNewアイコンのFlashPlayer
    /// </summary>
    public class GachaNewIconPlayer : IDisposable
    {
        #region Const

        /// <summary>
        /// イリ
        /// </summary>
        private const string IN_LABEL = "in_gr";

        /// <summary>
        /// イリ完了
        /// </summary>
        private const string IN_END_LABEL = "in_end_gr";

        /// <summary>
        /// ハケ
        /// </summary>
        private const string OUT_LABEL = "out_gr";

        /// <summary>
        /// 非表示
        /// </summary>
        private const string END_LABEL = "end_gr";

        #endregion

        #region Member

        /// <summary>
        /// Flashプレイヤー
        /// </summary>
        private readonly FlashPlayer _flashPlayer;

        /// <summary>
        /// イリ完了の
        /// </summary>
        private EventSubject<bool> InEndSubject { get; } = new EventSubject<bool>();

        #endregion

        #region Method

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="parent"></param>
        /// <param name="sortOffset"></param>
        public GachaNewIconPlayer(Transform parent, int sortOffset = 0)
        {
            const string PATH = ResourcePath.NEW_ICON_FLASH_PATH;
            _flashPlayer = FlashLoader.LoadOnView(PATH, parent);
            _flashPlayer.SortOffset = sortOffset;
            _flashPlayer.Init();
        }

        /// <summary>
        /// イリ再生
        /// </summary>
        public void Play()
        {
            _flashPlayer.RemoveOnlyAdditionalAction();
            _flashPlayer.SetActionCallBack(IN_END_LABEL, () => InEndSubject.OnNext(true), AnMotionActionTypes.Start);
            _flashPlayer.Play(IN_LABEL);
        }

        /// <summary>
        /// ハケ再生
        /// </summary>
        public void PlayOut()
        {
            _flashPlayer.Play(OUT_LABEL);
        }

        /// <summary>
        /// 非表示
        /// </summary>
        public void End()
        {
            _flashPlayer.Play(END_LABEL);
        }

        public void Dispose()
        {
            InEndSubject?.Dispose();
        }

        #endregion
    }
}
