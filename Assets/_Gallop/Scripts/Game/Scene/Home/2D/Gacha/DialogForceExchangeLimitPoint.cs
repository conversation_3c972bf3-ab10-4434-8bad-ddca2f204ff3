using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャ天井ポイント強制交換ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogForceExchangeLimitPoint : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// 確認メッセージ
        /// </summary>
        [SerializeField]
        private TextCommon _messageText;

        /// <summary>
        /// 開催期間
        /// </summary>
        [SerializeField]
        private TextCommon _termText;

        /// <summary>
        /// 交換ptルート
        /// </summary>
        [SerializeField]
        private GameObject _pointRoot;

        /// <summary>
        /// 交換ptタイトル
        /// </summary>
        [SerializeField]
        private TextCommon _pointTitle;

        /// <summary>
        /// 交換pt
        /// </summary>
        [SerializeField]
        private TextCommon _pointText;

        /// <summary>
        /// ガチャアイコン
        /// </summary>
        [SerializeField]
        private RawImageCommon _gachaIcon;

        #endregion

        #region Const

        private const float POINT_ROOT_X_SUPPORT_CARD = -65f;
        private const float POINT_ROOT_WIDTH_SUPPORT_CARD = 330f;
        private const float POINT_TEXT_X_SUPPORT_CARD = 180f;

        #endregion

        #region Variable, Property

        private Action _onCancel = null;
        private Action _onExchange = null;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログ表示
        /// </summary>
        /// <param name="gachaId">対象のガチャID</param>
        /// <param name="onCancel">処理キャンセル時に呼ぶコールバック（ダイアログを開く必要がない or 交換せず終了）</param>
        /// <param name="onExchange">交換を実行した際に呼ぶコールバック</param>
        public static void Open(int gachaId, Action onCancel = null, Action onExchange = null)
        {
            // 直前に強制交換していたガチャIDと一致する場合は交換ダイアログを即時開く
            if (TempData.Instance.GachaData.CurrentForceExchangeGachaId == gachaId)
            {
                OpenDialogGachaExchange(gachaId, onExchange, onCancel);
                return;
            }

            TempData.Instance.GachaData.CurrentForceExchangeGachaId = gachaId;
            var masterGachaData = MasterDataManager.Instance.masterGachaData.Get(gachaId);
            var cardType = Gacha.ConvertIntToCardType(masterGachaData.CardType);

            var content = LoadAndInstantiatePrefab<DialogForceExchangeLimitPoint>(ResourcePath.DIALOG_FORCE_EXCHANGE_LIMIT_POINT_PATH);
            var data = content.CreateDialogData(cardType);
            data.LeftButtonCallBack = dialog => onCancel?.Invoke();
            data.RightButtonCallBack = content.RightButtonCallBack;
            DialogManager.PushDialog(data);

            content.Initialize(masterGachaData, cardType, onCancel, onExchange);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        private DialogCommon.Data CreateDialogData(GameDefine.CardType cardType)
        {
            var data = base.CreateDialogData();
            data.Title = TextUtil.Format(
                TextId.Gacha608006.Text(),GachaLimitPointCount.GetExchangeTargetTextByCardType(cardType));
            data.LeftButtonText = TextId.Common0004.Text();
            data.RightButtonText = TextId.Common0003.Text();
            data.LeftButtonColor = DialogCommon.ButtonColor.White;
            data.IsFooterNotificationText = true;
            data.FooterText = TextId.Gacha608009.Text();
            return data;
        }

        /// <summary>
        /// 初期化処理
        /// </summary>
        /// <param name="masterGachaData"></param>
        /// <param name="cardType"></param>
        /// <param name="onCancel">処理キャンセル時に呼ぶコールバック（交換せず終了）</param>
        /// <param name="onExchange">交換を実行した際に呼ぶコールバック</param>
        private void Initialize(MasterGachaData.GachaData masterGachaData, GameDefine.CardType cardType, Action onCancel, Action onExchange)
        {
            // ガチャ開催期間を表示
            var startDate = TimeUtil.FromUnixTimeToLocaleTime(masterGachaData.StartDate);
            var endDate = TimeUtil.FromUnixTimeToLocaleTime(masterGachaData.EndDate);

            var startDateStr = TimeUtil.ToDispString(startDate);
            // 年を跨がない場合は終了期間の年表記は省略するが、年を跨ぐ場合は終了期間にも年を表記する
            var endDateStr = (startDate.Year == endDate.Year) ? TimeUtil.ToDispStringFromMonth(endDate) : TimeUtil.ToDispString(endDate);

            var term = TextUtil.Format(
                TextId.Heroes511114.Text(),
                startDateStr,
                endDateStr);
            _termText.text = term;
            _termText.UpdatePreferedWidth();

            // メッセージ内容の設定
            var limitPointText = GachaLimitPointCount.GetLimitPointTextByCardType(cardType);
            var exchangeTargetText = GachaLimitPointCount.GetExchangeTargetTextByCardType(cardType);

            var message = TextUtil.Format(
                TextId.Gacha608008.Text(),
                limitPointText,
                exchangeTargetText);
            _messageText.text = message;

            // 交換Ptを表示
            _pointText.text = TempData.Instance.GachaData.LimitPointCount.GetPointByGachaId(masterGachaData.Id).ToCommaSeparatedString();
            // サポートカードの場合はテキスト置き換え&タイトルの長さが変わるのでレイアウトを調整
            if (cardType == GameDefine.CardType.SupportCard)
            {
                _pointTitle.text = limitPointText;
                if (_pointRoot.transform is RectTransform rootTransform)
                {
                    rootTransform.SetLocalPositionX(POINT_ROOT_X_SUPPORT_CARD);
                    rootTransform.SetSizeDeltaX(POINT_ROOT_WIDTH_SUPPORT_CARD);
                }
                if (_pointText.transform is RectTransform textTransform)
                {
                    textTransform.SetLocalPositionX(POINT_TEXT_X_SUPPORT_CARD);
                }
            }

            // ガチャアイコンを設定
            var path = ResourcePath.GetGachaBannerPath(masterGachaData.Id);
            _gachaIcon.texture = ResourceManager.LoadOnHash<Texture2D>(path, DialogHash);

            _onCancel = onCancel;
            _onExchange = onExchange;
        }

        /// <summary>
        /// 右ボタンのコールバック
        /// </summary>
        /// <param name="dialog"></param>
        private void RightButtonCallBack(DialogCommon dialog)
        {
            var gachaId = TempData.Instance.GachaData.CurrentForceExchangeGachaId;

            // 交換ダイアログを開く
            OpenDialogGachaExchange(gachaId, _onExchange, _onCancel);
        }

        /// <summary>
        /// 天井交換ダイアログを開く
        /// </summary>
        /// <param name="gachaId">対象のガチャID</param>
        /// <param name="onExchange">交換を実行した際に呼ぶコールバック</param>
        /// <param name="onCancel">処理キャンセル時に呼ぶコールバック（ダイアログを開く必要がない or 交換せず終了）</param>
        private static void OpenDialogGachaExchange(int gachaId, Action onExchange, Action onCancel)
        {
            var masterGachaData = MasterDataManager.Instance.masterGachaData.Get(gachaId);
            var point = TempData.Instance.GachaData.LimitPointCount.GetPointByGachaId(gachaId);
            var gacha = new Gacha(masterGachaData, point);

            // 交換ダイアログを開く
            DialogGachaExchange.Open(gacha, onExchange, onCancel);
        }

        #endregion
    }
}
