using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    [AddComponentMenu("")]
    public class DialogGachaHistoryDetail : DialogInnerBase
    {
        #region DialogInnerBase

        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Inspector

        [SerializeField]
        private RectTransform _screollRect;

        [SerializeField]
        private LoopScroll _loopScroll;

        [SerializeField]
        private TextCommon _gachaName;
        
        [SerializeField]
        private TextCommon _execTimeText;
        
        [SerializeField]
        private HorizontalLayoutGroup _layoutGroup;
        
        [SerializeField]
        private GameObject _freeIconObj;
        
        [SerializeField]
        private ImageCommon _itemIconImage;
        
        [SerializeField]
        private RawImageCommon _itemIconRawImage;
        
        [SerializeField]
        private TextCommon _numText;

        #endregion


        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(GachaHistoryExecData execData)
        {
            if (execData?.RewardArray == null || execData.RewardArray.Length <= 0)
            {
                Debug.LogError("想定外のエラーです");
                return;
            }
            
            GameObject instance = Instantiate(ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_GACHA_HISTORY_DETAIL_PATH));
            var component = instance.GetComponent<DialogGachaHistoryDetail>();
            
            var dialogData = component.CreateDialogData();
            dialogData.Title = TextId.Gacha0113.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            if (execData.DrawNum > 1)
            {
                const float BOTTOM_ON_BIG_SIZE = 12f;
                dialogData.FormType = DialogCommonBase.FormType.BIG_ONE_BUTTON;
                var offsetMin = component._screollRect.offsetMin;
                component._screollRect.offsetMin = new Vector2(offsetMin.x, BOTTOM_ON_BIG_SIZE);
            }
            else
            {
                const float BOTTOM_ON_MIDDLE_SIZE = -28f;
                dialogData.FormType = DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;
                var offsetMin = component._screollRect.offsetMin;
                component._screollRect.offsetMin = new Vector2(offsetMin.x, BOTTOM_ON_MIDDLE_SIZE);
            }
            
            DialogManager.PushDialog(dialogData);
            component.Setup(execData);
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        private void Setup(GachaHistoryExecData execData)
        {
            SetupHeader(execData);
            SetupListUI(execData);
        }

        /// <summary>
        /// 上部UIのセットアップ
        /// </summary>
        private void SetupHeader(GachaHistoryExecData execData)
        {
            if (execData == null)
            {
                Debug.LogError("想定外のエラーです");
                return;
            }

            var masterGachaData = MasterDataManager.Instance.masterGachaData.Get(execData.GachaId);
            if (masterGachaData == null)
            {
                Debug.LogError($"不正なgacha_id({execData.GachaId}です");
                return;
            }
            
            _gachaName.text = masterGachaData.Name;
            var createDateTime = TimeUtil.ToDateTime(execData.CreateTime);
            _execTimeText.text = createDateTime.ToString(DialogGachaHistory.DATETIME_FORMAT);
            _numText.text = TextUtil.Format(TextId.Common0278.Text(), execData.CostCount);
            _numText.SetActiveWithCheck(false);
            _freeIconObj.SetActiveWithCheck(false);
            _itemIconImage.SetActiveWithCheck(false);
            _itemIconRawImage.SetActiveWithCheck(false);
            
            // 消費アイテムによってUIを変える
            var consumeType = DialogGachaHistory.GetConsumeTypeByExecData(execData);
            switch (consumeType)
            {
                case DialogGachaHistory.ConsumeType.ChargeStone:
                {
                    var itemType = GachaDefine.GachaConsumableItemType.ChargeStone;
                    var spriteName = PartsGachaButton.GetIconPathImage(itemType);
                    _itemIconImage.sprite = UIManager.CommonAtlas.GetSprite(spriteName);
                    _itemIconImage.SetActiveWithCheck(true);
                    _numText.SetActiveWithCheck(true);
                    break;
                }
                case DialogGachaHistory.ConsumeType.Stone:
                {
                    var itemType = GachaDefine.GachaConsumableItemType.Stone;
                    var spriteName = PartsGachaButton.GetIconPathImage(itemType);
                    _itemIconImage.sprite = UIManager.CommonAtlas.GetSprite(spriteName);
                    _itemIconImage.SetActiveWithCheck(true);
                    _numText.SetActiveWithCheck(true);
                    break;   
                }
                case DialogGachaHistory.ConsumeType.Ticket:
                {
                    var ticketIconPath = GachaDefine.GetTicketIconPath(execData.GachaId, execData.CostId);
                    _itemIconRawImage.texture = ResourceManager.LoadOnView<Texture2D>(ticketIconPath);
                    _itemIconRawImage.SetActiveWithCheck(true);
                    _numText.SetActiveWithCheck(true);
                    break;   
                }
                case DialogGachaHistory.ConsumeType.Free:
                {
                    _freeIconObj.SetActiveWithCheck(true);
                    break;
                }
                default:
                {
                    Debug.LogError("想定外のエラーです");
                    return;   
                }
            }
            
            // LayoutGroupが正しく動作するように強制更新を走らせる
            _layoutGroup.CalculateLayoutInputVertical();
            _layoutGroup.SetLayoutHorizontal();
            LayoutRebuilder.ForceRebuildLayoutImmediate(_layoutGroup.transform as RectTransform);
        }

        /// <summary>
        /// リストUIのセットアップ
        /// </summary>
        private void SetupListUI(GachaHistoryExecData execData)
        {
            var rewardArray = execData.RewardArray;
            int listNum = rewardArray.Length;
            _loopScroll.Setup(listNum, (item) =>
            {
                if (item is GachaHistoryDetailListItem listItem)
                {
                    int itemIndex = item.ItemIndex;
                    if (itemIndex < 0 || itemIndex >= listNum)
                    {
                        Debug.LogError($"不正なitemIndex(={itemIndex})です");
                        return;
                    }

                    listItem.UpdateItem(rewardArray[itemIndex]);
                }
            }, TextId.Gacha0112.Text());
        }
    }
}