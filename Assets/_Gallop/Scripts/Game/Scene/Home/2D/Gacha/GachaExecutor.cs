using System;
using Cute.Http;

namespace Gallop
{
    /// <summary>
    /// ガチャ実行クラス
    /// </summary>
    public class GachaExecutor
    {
        #region Const

        /// <summary>
        /// 指定なしの場合のアイテムID
        /// </summary>
        private const int UNSPECIFIED_ITEM_ID = 0;

        #endregion

        #region Member

        /// <summary>
        /// 実行するガチャ
        /// </summary>
        private GachaExecutableUnit _executable;

        /// <summary>
        /// APIリクエスト
        /// </summary>
        private GachaExecRequest _request;

        /// <summary>
        /// チケット
        /// </summary>
        public GachaTicketCounter Ticket { get; private set; }

        /// <summary>
        /// ガチャリクエスト実行可能か
        /// </summary>
        public bool IsExecutable { get; private set; }

        /// <summary>
        /// コスト不足か
        /// </summary>
        public bool IsCostShortage { get; private set; }

        #endregion

        #region Method

        /// <summary>
        /// プライベートコンストラクタ
        /// </summary>
        private GachaExecutor()
        {
            IsExecutable = false;
        }

        /// <summary>
        /// 生成
        /// </summary>
        /// <param name="executable"></param>
        /// <returns></returns>
        public static GachaExecutor Create(GachaExecutableUnit executable)
        {
            var executor = new GachaExecutor();
            executor.EstablishExecute(executable);
            return executor;
        }

        /// <summary>
        /// ガチャを引く回数を指定してGachaExecutor生成
        /// </summary>
        public static GachaExecutor CreateUseMultiTicket(GachaExecutableUnit executable, int drawCount)
        {
            var executor = new GachaExecutor();
            executor.EstablishExecute(executable, drawCount);
            return executor;
        }
        
        /// <summary>
        /// 確定枠を指定してGachaExecutor生成
        /// </summary>
        public static GachaExecutor CreateUseGuarantee(GachaExecutableUnit executable, int guaranteeCardId)
        {
            var executor = new GachaExecutor();
            executor.EstablishGuaranteeExecute(executable, guaranteeCardId);
            return executor;
        }

        /// <summary>
        /// ガチャ実行
        /// </summary>
        public void Execute(Action<GachaExecResponse> onSuccess, Action<ErrorType, int> onError)
        {
            if (!IsExecutable)
                return;

            HttpManager.Instance.Send<GachaExecRequest, GachaExecResponse>(_request,
                (res) =>
                {
                    TempData.Instance.GachaResult.Clear();
                    onSuccess?.Invoke(res);
                },
                (errorType, resultCode) =>
                {
                    // idpf:購入情報の同期が必要な場合は同期用APIを実行(ジュエル数が変わるのでそのまま実行はせず再度ガチャを引くボタンを押させる)
                    if (errorType == ErrorType.ResultCode && resultCode == GallopResultCode.IDPF_APPLY_PENDING_PRODUCT)
                    {
                        CygamesIdApply.Instance.ApplyPendingProduct();
                        return;
                    }
                    TempData.Instance.GachaResult.Clear();
                    onError?.Invoke(errorType, resultCode);
                }
            );
        }

        /// <summary>
        /// 実行可能状態を確立する
        /// </summary>
        /// <param name="executable"></param>
        /// <returns></returns>
        private void EstablishExecute(GachaExecutableUnit executable)
        {
            _executable = executable;

            if (executable.IsDrawEnd)
                return;

            if (executable.IsPointLimit)
                return;

            switch (executable.Consumption.ItemType)
            {
                case GachaDefine.GachaConsumableItemType.Free:
                    ExecuteByFree(executable);
                    break;
                case GachaDefine.GachaConsumableItemType.FreeStock:
                    ExecuteByFreeStock(executable);
                    break;
                case GachaDefine.GachaConsumableItemType.Stone:
                case GachaDefine.GachaConsumableItemType.FreeStone:
                    ExecuteByStone(executable);
                    break;
                case GachaDefine.GachaConsumableItemType.ChargeStone:
                    ExecuteByChargeStone(executable);
                    break;
                case GachaDefine.GachaConsumableItemType.SingleTicket:
                    ExecuteBySingleTicket(executable);
                    break;
                case GachaDefine.GachaConsumableItemType.MultiTicket:
                    ExecuteByMultiTicket(executable);
                    break;
                default:
                    return;
            }
        }


        /// <summary>
        /// 実行可能状態を確立する（ガチャを引く回数の指定付き）
        /// </summary>
        private void EstablishExecute(GachaExecutableUnit executable, int drawCount)
        {
            _executable = executable;

            if (executable.IsDrawEnd)
                return;

            if (executable.IsPointLimit)
                return;

            switch (executable.Consumption.ItemType)
            {
                case GachaDefine.GachaConsumableItemType.SingleTicket:
                    ExecuteBySingleTicket(executable, drawCount);
                    break;
                default:
                    Debug.LogError("単発チケット以外はまとめて使用できません");
                    return;
            }
        }
        
        /// <summary>
        /// 実行可能状態を確立する（確定枠のカードIDつき）
        /// </summary>
        private void EstablishGuaranteeExecute(GachaExecutableUnit executable, int guaranteeCardId)
        {
            _executable = executable;

            if (executable.IsDrawEnd)
                return;

            if (executable.IsPointLimit)
                return;

            switch (executable.Consumption.ItemType)
            {
                // 現状は有償限定
                case GachaDefine.GachaConsumableItemType.ChargeStone:
                    ExecuteByChargeStone(executable, guaranteeCardId);
                    break;
                default:
                    return;
            }
        }

        /// <summary>
        /// 無料で実行可能か
        /// </summary>
        /// <param name="executable"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void ExecuteByFree(GachaExecutableUnit executable)
        {
            _request = CreateRequest(executable, UNSPECIFIED_ITEM_ID, GachaDefine.FREE_COST, 0);
            IsExecutable = true;
        }
        
        /// <summary>
        /// 無料ガチャストックで実行可能か
        /// </summary>
        /// <param name="executable"></param>
        private void ExecuteByFreeStock(GachaExecutableUnit executable)
        {
            if (executable.StockCount <= 0)
                return;

            _request = CreateRequest(executable, UNSPECIFIED_ITEM_ID, GachaDefine.FREE_COST, executable.StockCount);
            IsExecutable = true;
        }

        /// <summary>
        /// 有償キャロットストーンで実行可能か
        /// </summary>
        /// <param name="executable"></param>
        /// <param name="guaranteeCardId"></param>
        private void ExecuteByChargeStone(GachaExecutableUnit executable, int guaranteeCardId = GachaDefine.INVALID_GUARANTEE_CARD_ID)
        {
            var currentChargeCoin = WorkDataManager.Instance.UserData.CarrotStone.ChargeCoin;
            IsCostShortage = currentChargeCoin < executable.Consumption.Cost;
            if (IsCostShortage)
                return;
            
            _request = CreateRequest(executable, UNSPECIFIED_ITEM_ID, currentChargeCoin, 0, guaranteeCardId: guaranteeCardId);
            IsExecutable = true;
        }

        /// <summary>
        /// キャロットストーンで実行可能か
        /// </summary>
        /// <param name="executable"></param>
        /// <param name="guaranteeCardId"></param>
        /// <returns></returns>
        private void ExecuteByStone(GachaExecutableUnit executable, int guaranteeCardId = GachaDefine.INVALID_GUARANTEE_CARD_ID)
        {
            var currentTotalCoin = WorkDataManager.Instance.UserData.CarrotStone.TotalCoin;
            IsCostShortage = currentTotalCoin < executable.Consumption.Cost;
            if (IsCostShortage)
                return;


            _request = CreateRequest(executable, UNSPECIFIED_ITEM_ID, currentTotalCoin, 0, guaranteeCardId: guaranteeCardId);
            IsExecutable = true;
        }

        /// <summary>
        /// 単発チケットで実行可能か
        /// </summary>
        /// <param name="executable"></param>
        /// <param name="drawCount"></param>
        /// <returns></returns>
        private void ExecuteBySingleTicket(GachaExecutableUnit executable, int? drawCount = null)
        {
            var masterData = MasterDataManager.Instance.masterGachaData.Get(executable.ParentGachaId);
            if (masterData == null)
                return;

            // 外部から引く回数が指定されていればそれを使う　指定がなければexecutableの値をそのまま使う
            var virtualDrawCount = drawCount ?? executable.DrawCount;

            Ticket = GachaTicketCounter.Count(masterData, WorkDataManager.Instance.ItemData.GetList());
            var singleTicketCount = Ticket.Single.Count;
            IsCostShortage = singleTicketCount < executable.Consumption.Cost * virtualDrawCount;
            if (IsCostShortage)
                return;

            if (!Ticket.Single.CanUse)
                return;

            _request = CreateRequest(executable, Ticket.Single.ItemId, singleTicketCount, 0, virtualDrawCount);
            IsExecutable = true;
        }

        /// <summary>
        /// 連チケットで実行可能か
        /// </summary>
        /// <param name="executable"></param>
        /// <returns></returns>
        private void ExecuteByMultiTicket(GachaExecutableUnit executable)
        {
            var masterData = MasterDataManager.Instance.masterGachaData.Get(executable.ParentGachaId);
            if (masterData == null)
                return;

            Ticket = GachaTicketCounter.Count(masterData, WorkDataManager.Instance.ItemData.GetList());
            var multiTicketCount = Ticket.Multi.Count;
            IsCostShortage = Ticket.Multi.Count < executable.Consumption.Cost;
            if (IsCostShortage)
                return;

            if (!Ticket.Multi.CanUse)
                return;

            _request = CreateRequest(executable, Ticket.Multi.ItemId, multiTicketCount, 0);
            IsExecutable = true;
        }

        /// <summary>
        /// リクエスト生成
        /// </summary>
        /// <param name="executable"></param>
        /// <param name="itemId"></param>
        /// <param name="currentNum"></param>
        /// <param name="stockCount"></param>
        /// <param name="drawNum"></param>
        /// <param name="guaranteeCardId"></param>
        /// <returns></returns>
        private static GachaExecRequest CreateRequest(GachaExecutableUnit executable, int itemId, int currentNum, int stockCount, int? drawNum = null, int guaranteeCardId = GachaDefine.INVALID_GUARANTEE_CARD_ID)
        {
            var drawCount = drawNum ?? executable.DrawCount;
            return new GachaExecRequest
            {
                gacha_id = executable.ParentGachaId,
                draw_type = (int) executable.Consumption.DrawType,
                draw_num = drawCount,
                item_id = itemId,
                current_num = currentNum,
                current_stock_num = stockCount,
                draw_guarantee_card_id = guaranteeCardId,
            };
        }

        #endregion
    }
}
