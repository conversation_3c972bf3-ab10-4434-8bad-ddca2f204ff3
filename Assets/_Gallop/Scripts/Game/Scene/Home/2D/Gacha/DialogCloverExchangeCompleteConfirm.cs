using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャ交換完了確認ダイアログ(クローバー交換用)
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCloverExchangeCompleteConfirm : DialogInnerBase
    {
        #region Nested Types

        /// <summary>
        /// UIパーツ
        /// </summary>
        [Serializable]
        private class CountParts
        {
            /// <summary>
            /// 所持数
            /// </summary>
            [SerializeField]
            public PartsBeforeAfterCount HavingCount;

            /// <summary>
            /// 交換Ptn
            /// </summary>
            [SerializeField]
            public PartsBeforeAfterCount PointCount;

            /// <summary>
            /// 残り交換回数
            /// </summary>
            [SerializeField]
            public PartsBeforeAfterCount ExchangeCount;

            /// <summary>
            /// 交換Ptテキスト
            /// </summary>
            [SerializeField]
            public TextCommon ExchangePointText;
        }

        #endregion

        #region SerializeField

        /// <summary>
        /// アイテムアイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _itemIcon;

        /// <summary>
        /// 所持数変動表示用
        /// </summary>
        [SerializeField]
        private CountParts _countParts;

        /// <summary>
        /// 交換確認メッセージ
        /// </summary>
        [SerializeField]
        private TextCommon _message;

        /// <summary>
        /// プレゼントボックスに送られたメッセージ
        /// </summary>
        [SerializeField]
        private TextCommon _sentPresentText;

        /// <summary>
        /// 変動個数表示のベースとなるルートノード
        /// </summary>
        [SerializeField]
        private GameObject _countRoot;

        #endregion

        #region Member

        /// <summary>
        /// 交換情報
        /// </summary>
        private GachaExchangeableUnit _exchangeable;

        /// <summary>
        /// 天井ポイント情報
        /// </summary>
        private GachaLimitPointResult _limitPointResult;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        /// <param name="onClose">クローズ時コールバック</param>
        /// <param name="beforeCount">交換前クローバー所持数</param>
        /// <param name="afterCount">交換後クローバー所持数</param>
        /// <param name="beforePoint">交換前交換Pt所持数</param>
        /// <param name="afterPoint">交換後交換Pt所持数</param>
        /// <param name="isSentPresent">プレゼントボックスに送られたかどうか</param>
        public static void Open(
            GachaExchangeableUnit exchangeableUnit,
            Action onClose,
            int beforeCount,
            int afterCount,
            int beforePoint,
            int afterPoint,
            bool isSentPresent)
        {
            const string PATH = ResourcePath.DIALOG_CLOVER_EXCHANGE_COMPLETE_CONFIRM_PATH;
            var prefab = ResourceManager.LoadOnView<GameObject>(PATH);
            var obj = Instantiate(prefab);
            var content = obj.GetComponent<DialogCloverExchangeCompleteConfirm>();
            var dialogData = content.CreateDialogData(onClose);

            content.Setup(exchangeableUnit, beforeCount, afterCount, beforePoint, afterPoint, isSentPresent);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <param name="onClose"></param>
        private DialogCommon.Data CreateDialogData(Action onClose)
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Shop0072.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();
            dialogData.CenterButtonColor = DialogCommon.ButtonColor.White;
            dialogData.CenterButtonCallBack = dialog =>
            {
                onClose?.Invoke();
            };

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        /// <param name="beforeCount">交換前クローバー所持数</param>
        /// <param name="afterCount">交換後クローバー所持数</param>
        /// <param name="beforePoint">交換前交換Pt所持数</param>
        /// <param name="afterPoint">交換後交換Pt所持数</param>
        /// <param name="isSentPresent">プレゼントボックスに送られたかどうか</param>
        public void Setup(
            GachaExchangeableUnit exchangeableUnit,
            int beforeCount,
            int afterCount,
            int beforePoint,
            int afterPoint,
            bool isSentPresent)
        {
            // 交換ポイント
            _countParts.PointCount.Setup(beforePoint, afterPoint);

            // 所持数
            _countParts.HavingCount.Setup(beforeCount, afterCount);

            // 交換回数
            _countParts.ExchangeCount.Setup(TextId.Common0099.Text());

            // アイコン
            var itemId = GameDefine.CALOTTE_SEED_ID;
            var itemData = MasterDataManager.Instance.masterItemData.Get(itemId);
            _itemIcon.SetData(itemData.ItemCategory, itemId, 0, false, isInfoPop: true);

            // メッセージ
            _message.text = TextUtil.Format(TextId.Gacha608012.Text(), itemData.Name);

            // 交換Ptの文言設定
            var gachaData = MasterDataManager.Instance.masterGachaData.Get(exchangeableUnit.ParentGachaId);
            var cardType = Gacha.ConvertIntToCardType(gachaData.CardType);
            var pointText = GachaLimitPointCount.GetLimitPointTextByCardType(cardType);
            _countParts.ExchangePointText.text = TextUtil.Format(TextId.Gacha608013.Text(), pointText);;

            // プレゼントボックスに送られたかどうか
            _sentPresentText.SetActiveWithCheck(isSentPresent);
            if (isSentPresent)
            {
                _sentPresentText.SetTextWithCustomTag(TextId.Shop0093.Text());
                // 注意文言が表示される分全体的にずらす
                const float ICON_POSITION_Y_WITH_NOTIFY_TEXT = 141.0f;
                const float MESSAGE_POSITION_Y_WITH_NOTIFY_TEXT = 3.5f;
                const float COUNT_ROOT_POSITION_Y_WITH_NOTIFY_TEXT = -120f;
                _itemIcon.transform.SetLocalPositionY(ICON_POSITION_Y_WITH_NOTIFY_TEXT);
                _message.transform.SetLocalPositionY(MESSAGE_POSITION_Y_WITH_NOTIFY_TEXT);
                _countRoot.transform.SetLocalPositionY(COUNT_ROOT_POSITION_Y_WITH_NOTIFY_TEXT);
            }
        }

        #endregion
    }
}
