using System.Linq;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャ交換の実行確認ダイアログ
    /// (既に所持済みと交換)
    /// </summary>
    public class DialogGachaExchangeConversionConfirm : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// 変換されるアイテムのアイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _itemIcon;

        /// <summary>
        /// メッセージ
        /// </summary>
        [SerializeField]
        private TextCommon _messageText;

        /// <summary>
        /// 所持汎用ピース
        /// </summary>
        [Header("所持")]
        [SerializeField]
        private PartsBeforeAfterCount _havingItemCount;

        /// <summary>
        /// 交換Pt
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _pointCount;

        /// <summary>
        /// 残り交換回数
        /// </summary>
        [SerializeField]
        private PartsBeforeAfterCount _exchangeCount;

        /// <summary>
        /// 警告テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _alertText;

        #endregion

        #region Member

        /// <summary>
        /// 交換情報
        /// </summary>
        private GachaExchangeableUnit _exchangeable;
        
        /// <summary>
        /// 成功コールバック
        /// </summary>
        private System.Action _onSuccessCallback;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        public static void Open(GachaExchangeableUnit exchangeableUnit , System.Action onSuccessCallback)
        {
            const string PATH = ResourcePath.DIALOG_GACHA_EXCHANGE_CONVERSION_CONFIRM_PATH;
            var prefab = ResourceManager.LoadOnView<GameObject>(PATH);
            var obj = Instantiate(prefab);
            var content = obj.GetComponent<DialogGachaExchangeConversionConfirm>();
            var dialogData = content.CreateDialogData();

            content.Setup(exchangeableUnit , onSuccessCallback);
            var dialog = DialogManager.PushDialog(dialogData);
            dialog.SetButtonSeType(DialogCommon.ButtonIndex.Right, ButtonCommon.ButtonSeType.DecideL02);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Shop0040.Text();
            dialogData.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = OnLeftButtonClick;
            dialogData.RightButtonText = TextId.Shop0043.Text();
            dialogData.RightButtonCallBack = OnExchangeConfirm;
            dialogData.AutoClose = false;

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        public void Setup(GachaExchangeableUnit exchangeableUnit , System.Action onSuccessCallback)
        {
            _exchangeable = exchangeableUnit;
            _onSuccessCallback = onSuccessCallback;

            SetupConversionItemIcon();
            SetupMessageText();
            SetupPointCount();
            SetupExchangeCount();
            SetupAlertText();
        }

        /// <summary>
        /// 変換されるアイテムアイコンをセットアップ
        /// </summary>
        private void SetupConversionItemIcon()
        {
            var conversionArray = MasterDataManager.Instance.masterGachaPiece.dictionary.Values
                .Where(conversion => conversion.Rarity == _exchangeable.Rarity)
                .ToArray();

            var item = conversionArray
                .FirstOrDefault(conversion => conversion.PieceType == (int) GachaDefine.GachaPieceType.Common);
            SetupItemIcon(item);
            SetupItemCount(item);
        }

        /// <summary>
        /// 汎用アイテムアイコンをセットアップ
        /// </summary>
        /// <param name="item"></param>
        private void SetupItemIcon(MasterGachaPiece.GachaPiece item)
        {
            if (item == null)
            {
                _itemIcon.SetActiveWithCheck(false);
                return;
            }

            _itemIcon.SetActiveWithCheck(true);
            _itemIcon.SetData(
                item.ItemCategory,
                item.ItemId,
                item.PieceNum,
                isInfoPop: true);
        }

        /// <summary>
        /// 汎用ピース所持数をセットアップ
        /// </summary>
        /// <param name="item"></param>
        private void SetupItemCount(MasterGachaPiece.GachaPiece item)
        {
            if (item == null)
            {
                _havingItemCount.SetActiveWithCheck(false);
                return;
            }

            _havingItemCount.SetActiveWithCheck(true);

            var itemCategory = (GameDefine.ItemCategory) item.ItemCategory;
            var beforeNum = GallopUtil.GetHaveItemNum(itemCategory, item.ItemId);
            var afterNum = beforeNum + item.PieceNum;
            _havingItemCount.Setup(beforeNum.ToCommaSeparatedString(), afterNum.ToCommaSeparatedString());
        }
        
        /// <summary>
        /// メッセージテキストをセットアップ
        /// </summary>
        private void SetupMessageText()
        {
            _messageText.text = string.Format(TextId.Shop0059.Text().Replace("\n", ""), TextId.Gacha0022.Text());
        }

        /// <summary>
        /// ポイントテキストをセットアップ
        /// </summary>
        private void SetupPointCount()
        {
            var beforePoint = _exchangeable.CurrentPoint;
            var afterPoint = beforePoint - _exchangeable.RequiredPoint;
            _pointCount.Setup(beforePoint, afterPoint);
        }

        /// <summary>
        /// 交換回数をセットアップ
        /// </summary>
        private void SetupExchangeCount()
        {
            _exchangeCount.Setup(TextId.Common0099.Text());
        }

        /// <summary>
        /// 警告テキストをセットアップ
        /// </summary>
        private void SetupAlertText()
        {
            _alertText.SetTextWithCustomTag(TextId.Gacha0068.Text());
        }

        /// <summary>
        /// 交換決定時のコールバック
        /// </summary>
        private void OnExchangeConfirm(DialogCommon dialog)
        {
            var executor = GachaExchangeExecutor.Create(_exchangeable);
            executor.ExecuteCardExchange(
                res => OnExchangeSuccess(res, dialog),
                null
            );
        }

        /// <summary>
        /// 交換成功時のコールバック
        /// </summary>
        /// <param name="res"></param>
        /// <param name="dialog"></param>
        private void OnExchangeSuccess(GachaLimitExchangeResponse res, DialogCommonBase dialog)
        {
            // ダイアログを閉じる
            GachaExchangeResultReceiver.OnSuccessReceiveCard(res, _exchangeable, false);
            dialog.Close();

            // 交換リストのダイアログを閉じる
            var frontDialog = DialogManager.Instance.GetForefrontDialog();
            frontDialog.Close();

            // 交換完了確認ダイアログを開く
            var result = TempData.Instance.GachaResult;
            DialogGachaExchangeConversionCompleteConfirm.Open(result, () =>
            {
                result.Clear();
                _onSuccessCallback?.Invoke();
            });
        }

        /// <summary>
        /// 左ボタン押下時
        /// </summary>
        /// <param name="dialog"></param>
        private static void OnLeftButtonClick(DialogCommon dialog)
        {
            dialog.Close();
        }

        #endregion
    }
}
