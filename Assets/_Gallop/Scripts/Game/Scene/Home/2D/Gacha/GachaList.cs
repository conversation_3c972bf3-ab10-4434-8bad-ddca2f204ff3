using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// ガチャのリスト
    /// </summary>
    public class GachaList : IReadOnlyCollection<Gacha>
    {
        /// <summary>
        /// ガチャリスト
        /// </summary>
        /// <returns></returns>
        private IList<Gacha> _gachaList = new List<Gacha>();

        /// <summary>
        /// ガチャの数
        /// </summary>
        public int Count => _gachaList.Count;

        /// <summary>
        /// ガチャIDからガチャを取得
        /// </summary>
        /// <param name="gachaId"></param>
        /// <returns></returns>
        public Gacha Get(int gachaId)
        {
            return _gachaList.FirstOrDefault(gacha => gacha.Id == gachaId);
        }

        /// <summary>
        /// ステップアップIDからガチャを取得
        /// </summary>
        /// <param name="stepupId"></param>
        /// <returns></returns>
        public Gacha GetByStepupId(int stepupId)
        {
            return _gachaList.FirstOrDefault(gacha => gacha.StepupId == stepupId);
        }

        /// <summary>
        /// GachaInfoListからガチャリストを更新(from API)
        /// </summary>
        /// <param name="gachaInfoArray"></param>
        public void UpdateList(IEnumerable<GachaInfoList> gachaInfoArray)
        {
            _gachaList = gachaInfoArray
                .Select(info => Tuple.Create(MasterDataManager.Instance.masterGachaData.Get(info.id), info))
                .Where(tuple => tuple.Item1 != null)
                .OrderByDescending(tuple => tuple.Item1.DispOrder) // DispOrderが優先
                .ThenByDescending(tuple => tuple.Item1.Type) // 特殊なガチャが優先
                .Select(tuple => new Gacha(tuple.Item1, tuple.Item2))
                .ToList();
        }

        public IList<Gacha> GetGachaList() => _gachaList;
        
        public IEnumerator<Gacha> GetEnumerator()
        {
            return _gachaList.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}
