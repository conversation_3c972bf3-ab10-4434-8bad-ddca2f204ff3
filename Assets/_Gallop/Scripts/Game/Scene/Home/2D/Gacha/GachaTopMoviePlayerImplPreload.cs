using System.Collections;
using System.Linq;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// ガチャトップのムービー再生・切り替え処理実装：事前に再生中＋左隣＋右隣の３つのムービーを読み込んでおく。
    /// </summary>
    //-------------------------------------------------------------------
    public sealed class GachaTopMoviePlayerImplPreload : GachaTopMoviePlayerImplBase
    {
        /// <summary>生成するGachaMovieの数。[再生中]+[左隣]+[右隣]の３つ。</summary>
        private const int MOVIE_PLAYER_MAX = 3;

        public GachaTopMoviePlayerImplPreload(PartsGachaSelectScrollList scrollList, GachaTopMovieScroller scroller) 
            : base(MOVIE_PLAYER_MAX, scrollList, scroller)
        {
        }

        protected override void InitializeDerived(int gachaId)
        {
            LoadMovies(gachaId);
        }

        protected override GachaMovie CreateGachaMovie()
        {
            // gacha_idをセットした時にムービーを読み込むのでtrue。
            return new GachaMovie(new FullScreenMoviePlayerEtoE(), true);
        }

        /// <summary>
        /// 全ムービーロード
        /// </summary>
        private void LoadMovies(int gachaId)
        {
            // gachaIdに隣り合うように配置される（スクロールで移動可能な）ガチャId取得。
            GetSideGachaId(gachaId, out int lhGachaId, out int rhGachaId);
            
            // gachaIdとそれに隣り合うガチャのムービーをロード。
            int laodCnt = 0;
            foreach (var id in new []{gachaId, lhGachaId, rhGachaId})
            {
                if (_playerArray.Any(x => x.GachaId == id))
                {
                    continue;   
                }
                _playerArray[laodCnt].SetGachaIdAndLoad(id);
                ++laodCnt;
            }
        }

        public override IEnumerator WaitUntilPlayStart(Gacha gacha)
        {
            _currentPlayer = Setup(gacha);
            var currentBg = _bgDataArray[CurrentIndex];
            
            yield return _currentPlayer.MoviePlayer.PlayAsync(() => {});
            OnMoviePlay.OnNext(currentBg);
        }

        protected override GachaMovie Setup(Gacha gacha)
        {
            _gacha = gacha;
            CreateGachaTopBgData(_gacha.ResourceId);
            CurrentIndex = 0;

            var currentBg = _bgDataArray[CurrentIndex];
            if (!_playerArray.Any(x => x.GachaId == currentBg.GachaId))
            {
                var player = _playerArray.FirstOrDefault(x => x != _currentPlayer);
                player.SetGachaIdAndLoad(gacha.ResourceId);
            }

            return _playerArray.FirstOrDefault(x => x.GachaId == gacha.ResourceId);
        }

        /// <summary>
        /// gachaIdの左右に配置されるガチャId取得。
        /// </summary>
        /// <param name="lhGachaId">画面向かって左隣のガチャId</param>
        /// <param name="rhGachaId">画面向かって右隣のガチャId</param>
        /// <remarks>
        /// 選択可能なガチャが２つ以下の時は重複する。
        /// 例えば、<1001>-<1002>という２つのガチャの場合、gachaIdが1001ならlhGachaIdは1002、rhGachaIdも1002。
        /// </remarks>
        private void GetSideGachaId(int gachaId, out int lhGachaId, out int rhGachaId)
        {
            lhGachaId = GACHA_ID_NULL;
            rhGachaId = GACHA_ID_NULL;

            var dispGachaList = TempData.Instance.GachaData.GachaDispGroupList.Select(dispGroup => dispGroup.CurrentSelectGacha).ToList();
            var targetGacha = dispGachaList.FirstOrDefault(x => x.ResourceId == gachaId);
            if (targetGacha == null)
            {
                Debug.LogError($"GachaId={gachaId}がTempData.GachaData.GachaListに見つからないためムービーのロードができない");
                return;
            }

            int targetGachaIndex = dispGachaList.IndexOf(targetGacha);
            int lhGachaIndex = targetGachaIndex - 1;
            int rhGachaIndex = targetGachaIndex + 1;
            if (lhGachaIndex < 0) lhGachaIndex = dispGachaList.Count - 1;
            if (rhGachaIndex >= dispGachaList.Count) rhGachaIndex = 0;

            var lhGacha = dispGachaList[lhGachaIndex];
            var rhGacha = dispGachaList[rhGachaIndex];

            lhGachaId = lhGacha.ResourceId;
            rhGachaId = rhGacha.ResourceId;
        }
        
        protected override IEnumerator PlayStart(GachaMovie currentPlayer, GachaMovie nextPlayer)
        {
            UIManager.Instance.LockGameCanvas();

            while(!nextPlayer.MoviePlayer.IsReady)
                yield return null;

            // #53885
            // シーク再生中にMovieManager.Playが呼ばれると、その後にMoviePlayerBase.UpdateSeekProcess内でMoviePlayer.Playが呼ばれて
            // 各種コールバック（playStartCallbackやplayEndCallback）が上書きされてしまうため、シーク再生終了を待つ。
            while (nextPlayer.MoviePlayer.IsSeekOn())
                yield return null;
            
            // 画像とムービープレイヤーのスクロール開始
            yield return nextPlayer.MoviePlayer.PlayAsync(() => {}, forceBegin:true);
            yield return StartScroll(currentPlayer, nextPlayer);
            // スクロールが完了したら停止して巻き戻し
            currentPlayer.MoviePlayer.Rewind(true);

            // 次に再生するかもしれないムービーのロード。
            {
                GetSideGachaId(nextPlayer.GachaId, out int lhGachaId, out int rhGachaId);
                int nextNextGachaId = currentPlayer.GachaId != lhGachaId ? lhGachaId : rhGachaId;
            
                // _playerArrayの中で「今まで再生(currentPlayer)していたGachaId」「今から再生(nextPlayer)するGachaId」「次に再生される可能性のあるGachaId」のどれでもないGachaIdが設定されているplayerは直近のスクロールでは再生されない。
                // そのため、そのplayerには「次に再生される可能性のあるGachaId」を設定してロードし、再生に備えさせる。
                // 例：
                // ①：(A)-<B>-(C)-D-E の5つのガチャがありBが画面で再生状態のとき、A/Cは再生待機状態である。この時B->Cに再生が変わったとすると、
                // ②：A-(B)-<C>-(D)-E Cが再生状態となり、B/Dは再生待機状態にしたい。（ちなみにcurrentPlayer:B、nextPlayer:Cである）
                // このとき、noUsePlayerとして取り出すのは①でAのGachaIdが設定されていたplayerである（Cからは直接スクロールされないため）。AのGachaIdが設定されていたplayerにDのGachaIdを設定するのがここの処理である。
                var noUsePlayer = _playerArray.FirstOrDefault(x => x.GachaId != currentPlayer.GachaId && x.GachaId != nextPlayer.GachaId && x.GachaId != nextNextGachaId);
                if (noUsePlayer != null)
                {
                    noUsePlayer.SetGachaIdAndLoad(nextNextGachaId);
                }
            }

            UIManager.Instance.UnlockGameCanvas();
        }
    }
}
