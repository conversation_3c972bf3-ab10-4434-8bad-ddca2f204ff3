using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// ガチャ詳細選択ダイアログ(ウマ娘)
    /// </summary>
    [AddComponentMenu("")]
    public class DialogGachaCardDetailSelector : DialogInnerBase
    {
        #region SerializeField

        /// <summary>
        /// ループスクロール
        /// </summary>
        [SerializeField]
        private LoopScroll _loopScroll;

        /// <summary>
        /// スクロールリストのルート
        /// </summary>
        [SerializeField]
        private RectTransform _listRoot;

        #endregion

        #region Member

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.MIDDLE_ONE_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(int[] pickupIdArray)
        {
            const string PATH = ResourcePath.DIALOG_GACHA_CARD_DETAIL_SELECTOR_PATH;
            var content = LoadAndInstantiatePrefab<DialogGachaCardDetailSelector>(PATH);
            var dialogData = content.CreateDialogData();

            content.Setup(pickupIdArray);
            DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Gacha0094.Text();
            dialogData.CenterButtonText = TextId.Common0007.Text();

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="pickupIdArray"></param>
        private void Setup(int[] pickupIdArray)
        {
            var buttonInfos = pickupIdArray.Select(CreateCharacterButtonInfo);
            CreateCharacterButton(buttonInfos);
        }

        /// <summary>
        /// CharacterButtonInfoを生成
        /// </summary>
        /// <param name="buttonInfos"></param>
        private void CreateCharacterButton(IEnumerable<CharacterButtonInfo> buttonInfos)
        {
            var list = buttonInfos.ToList();
            _loopScroll.Setup<PartsGachaCardDetailListItem>(list.Count, item => 
            {
                item.UpdateItem(list[item.ItemIndex]);
            });

            LayoutRebuilder.ForceRebuildLayoutImmediate(_listRoot);
        }

        /// <summary>
        /// CharacterButtonInfo生成
        /// </summary>
        /// <param name="pickupId"></param>
        /// <returns></returns>
        private static CharacterButtonInfo CreateCharacterButtonInfo(int pickupId)
        {
            var cardInfo = MasterDataManager.Instance.masterCardData.Get(pickupId);

            return new CharacterButtonInfo
            {
                IdType = CharacterButtonInfo.IdTypeEnum.Card,
                IconSizeType = IconBase.SizeType.Chara_63,
                Id = cardInfo.Id,
                EnableButton = true,
                Rarity = cardInfo.DefaultRarity,
                EnableRarity = true,
                OnTap = OnSelect,
                OnLongTap = OnSelect,
                Level = GameDefine.MIN_TALENT_LEVEL
            };
        }

        /// <summary>
        /// 選択時のコールバック
        /// </summary>
        /// <param name="button"></param>
        private static void OnSelect(CharacterButton button)
        {
            DialogCharacterCardDetail.Open(button.Info.MasterCardData.Id);
        }

        #endregion
    }
}
