using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャ交換の実行確認ダイアログ(クローバー交換用)
    /// </summary>
    [AddComponentMenu("")]
    public class DialogCloverExchangeConfirm : DialogInnerBase
    {
        #region Nested Types

        /// <summary>
        /// UIパーツ
        /// </summary>
        [Serializable]
        private class CountParts
        {
            /// <summary>
            /// 所持数
            /// </summary>
            [SerializeField]
            public PartsBeforeAfterCount HavingCount;

            /// <summary>
            /// 交換Ptn
            /// </summary>
            [SerializeField]
            public PartsBeforeAfterCount PointCount;

            /// <summary>
            /// 残り交換回数
            /// </summary>
            [SerializeField]
            public PartsBeforeAfterCount ExchangeCount;

            /// <summary>
            /// 交換Ptテキスト
            /// </summary>
            [SerializeField]
            public TextCommon ExchangePointText;
        }

        #endregion

        #region SerializeField

        /// <summary>
        /// アイテムアイコン
        /// </summary>
        [SerializeField]
        private ItemIcon _itemIcon;

        /// <summary>
        /// 所持数変動表示用
        /// </summary>
        [SerializeField]
        private CountParts _countParts;

        /// <summary>
        /// 交換確認メッセージ
        /// </summary>
        [SerializeField]
        private TextCommon _message;

        #endregion

        #region Member

        /// <summary>
        /// 交換情報
        /// </summary>
        private GachaExchangeableUnit _exchangeable;

        /// <summary>
        /// 成功コールバック
        /// </summary>
        private Action _onSuccessCallback;

        #endregion

        #region DialogInnerBase

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.SMALL_TWO_BUTTON;

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion

        #region Method

        /// <summary>
        /// ダイアログを開く
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        /// <param name="onSuccessCallback">交換成功時コールバック</param>
        public static void Open(GachaExchangeableUnit exchangeableUnit, Action onSuccessCallback)
        {
            var prefab = ResourceManager.LoadOnView<GameObject>(ResourcePath.DIALOG_CLOVER_EXCHANGE_CONFIRM_PATH);
            var obj = Instantiate(prefab);
            var content = obj.GetComponent<DialogCloverExchangeConfirm>();
            var dialogData = content.CreateDialogData();

            content.Setup(exchangeableUnit , onSuccessCallback);
            var dialog = DialogManager.PushDialog(dialogData);
            dialog.SetButtonSeType(DialogCommon.ButtonIndex.Right, ButtonCommon.ButtonSeType.DecideL02);
        }

        /// <summary>
        /// ダイアログデータ生成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var dialogData = base.CreateDialogData();
            dialogData.Title = TextId.Shop0040.Text();
            dialogData.DispStackType = DialogCommon.DispStackType.DialogOnDialog;
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.LeftButtonCallBack = OnLeftButtonClick;
            dialogData.RightButtonText = TextId.Shop0043.Text();
            dialogData.RightButtonCallBack = OnExchangeConfirm;
            dialogData.AutoClose = false;

            return dialogData;
        }

        /// <summary>
        /// セットアップ
        /// </summary>
        /// <param name="exchangeableUnit"></param>
        /// <param name="onSuccessCallback">交換成功時コールバック</param>
        private void Setup(GachaExchangeableUnit exchangeableUnit, Action onSuccessCallback)
        {
            _exchangeable = exchangeableUnit;
            _onSuccessCallback = onSuccessCallback;

            // 交換ポイント
            var beforePoint = _exchangeable.CurrentPoint;
            var afterPoint = 0;
            _countParts.PointCount.Setup(beforePoint, afterPoint);

            // 所持数
            var itemId = GameDefine.CALOTTE_SEED_ID;
            var beforeCount = WorkDataManager.Instance.ItemData.GetHaveItemNum(itemId);
            var afterCount = beforeCount + beforePoint;
            _countParts.HavingCount.Setup(beforeCount, afterCount);

            // 交換回数
            _countParts.ExchangeCount.Setup(TextId.Common0099.Text());

            // アイコン
            var itemData = MasterDataManager.Instance.masterItemData.Get(itemId);
            _itemIcon.SetData(itemData.ItemCategory, itemId, 0, false, isInfoPop: true);

            // 交換Ptの文言設定
            var gachaData = MasterDataManager.Instance.masterGachaData.Get(exchangeableUnit.ParentGachaId);
            var cardType = Gacha.ConvertIntToCardType(gachaData.CardType);
            var pointText = GachaLimitPointCount.GetLimitPointTextByCardType(cardType);
            _message.text = TextUtil.Format(TextId.Gacha608011.Text(), pointText, itemData.Name);
            _countParts.ExchangePointText.text = TextUtil.Format(TextId.Gacha608013.Text(), pointText);
        }

        /// <summary>
        /// 交換決定時のコールバック
        /// </summary>
        private void OnExchangeConfirm(DialogCommon dialog)
        {
            var req = new GachaLimitExchangeItemRequest
            {
                gacha_id = _exchangeable.ParentGachaId,
                current_num = _exchangeable.CurrentPoint,
                is_force_exchange = true,
            };
            req.Send(res =>
                {
                    OnExchangeSuccess(res, dialog);
                }
            );
        }

        /// <summary>
        /// 交換成功時のコールバック
        /// </summary>
        /// <param name="res"></param>
        /// <param name="dialog"></param>
        private void OnExchangeSuccess(GachaLimitExchangeItemResponse res, DialogCommonBase dialog)
        {
            // 交換リストを含め全ダイアログを閉じる
            DialogManager.RemoveAllDialog();

            // 交換前の個数を表示用に保持
            var itemId = GameDefine.CALOTTE_SEED_ID;
            var beforeCount = WorkDataManager.Instance.ItemData.GetHaveItemNum(itemId);
            var beforePoint = _exchangeable.CurrentPoint;

            // 交換後の個数を反映
            WorkDataUtil.SetRewardSummaryInfo(res.data.reward_summary_info);
            var afterCount = WorkDataManager.Instance.ItemData.GetHaveItemNum(itemId);
            var afterPoint = res.data.limit_item_info.num;
            TempData.Instance.GachaData.LimitPointCount.UpdatePoint(res.data.limit_item_info.gacha_id, afterPoint);

            // プレゼントボックスに送られたかどうか
            var isSentPresent = res.data.reward_summary_info.add_present_num > 0;

            // 交換完了確認ダイアログを開く
            var result = TempData.Instance.GachaResult;
            DialogCloverExchangeCompleteConfirm.Open(_exchangeable, () =>
            {
                result.Clear();
                _onSuccessCallback?.Invoke();
            }, beforeCount, afterCount, beforePoint, afterPoint, isSentPresent);
        }

        /// <summary>
        /// 左ボタン押下時
        /// </summary>
        /// <param name="dialog"></param>
        private static void OnLeftButtonClick(DialogCommon dialog)
        {
            dialog.Close();
        }

        #endregion
    }
}
