using System.Collections;
using Cute.Http;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ガチャのプライズ選択
    /// </summary>
    public static class GachaPrizeSelector
    {
        /// <summary>
        /// プライズ選択API
        /// </summary>
        /// <param name="gachaId"></param>
        /// <param name="cardId"></param>
        /// <param name="cardType"></param>
        /// <returns></returns>
        public static IEnumerator Select(int gachaId, int cardId, GameDefine.CardType cardType)
        {
            var req = new GachaSelectPrizeRequest
            {
                gacha_id = gachaId,
                card_id = cardId,
                card_type = (int) cardType
            };
            var isSuccess = false;
            HttpManager.Instance.Send<GachaSelectPrizeRequest, GachaSelectPrizeResponse>(
                req,
                res => isSuccess = true);
            yield return new WaitUntil(() => isSuccess);
        }
    }
}
