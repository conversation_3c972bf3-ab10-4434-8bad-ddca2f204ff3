using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ジュークボックス：いいね履歴の表示項目
    /// </summary>
    public class JukeboxLikeHistoryItem : LoopScrollItemBase
    {
        /// <summary>
        /// 表示する情報のタイプ
        /// </summary>
        public enum ItemType
        {
            CharaLikeInfo,
            TrainerLikeInfo,
            SongInfo
        }

        #region 定数
        /// <summary>
        /// トレーナーのアイコン
        /// </summary>
        private const string ICON_TRAINER = "utx_ico_story_chara_00";
        #endregion

        #region SerializeField
        /// <summary>
        /// 楽曲情報のルートオブジェクト
        /// </summary>
        [SerializeField]
        private GameObject _songInfo;

        /// <summary>
        /// いいね情報のルートオブジェクト
        /// </summary>
        [SerializeField]
        private GameObject _likeInfo;

        /// <summary>
        /// 楽曲情報：作詞・作曲/編曲
        /// </summary>
        [SerializeField]
        private TextCommon _authorText;

        /// <summary>
        /// 楽曲情報：曲名
        /// </summary>
        [SerializeField]
        private TextCommon _titleText;
        
        /// <summary>
        /// 楽曲情報：曲名（フォントの都合で表示できなくなった4つ点の「馬」を表示する用）
        /// </summary>
        [SerializeField]
        private RawImageCommon _titleImage = null;

        /// <summary>
        /// 楽曲情報：ジャケット画像
        /// </summary>
        [SerializeField]
        private RawImageCommon _jacketImage;

        /// <summary>
        /// いいね情報：テキスト
        /// </summary>
        [SerializeField]
        private TextCommon _likeText;

        /// <summary>
        /// いいね情報：キャラアイコン
        /// </summary>
        [SerializeField]
        private RawImageCommon _charaIcon;

        /// <summary>
        /// いいね情報：トレーナーアイコン
        /// </summary>
        [SerializeField]
        private ImageCommon _trainerIcon;
        #endregion

        #region Method
        /// <summary>
        /// 楽曲情報のアイテムセットアップ
        /// </summary>
        /// <param name="musicId"></param>
        public void SetupSongInfo(int musicId)
        {
            ShowInfo(ItemType.SongInfo);
            var resourceHash = (ResourceManager.ResourceHash) ResourceManager.GetViewLoadHash(SceneManager.Instance.GetCurrentViewId());
            JukeboxUtil.ShowSongTitle(musicId, _titleText, _titleImage, resourceHash);
            _authorText.text = TextUtil.GetMasterText(MasterString.Category.MasterLiveAuthor, musicId);
            var jacketImagePath = ResourcePath.GetJacketPath(musicId, alterJacket:true);
            _jacketImage.texture = ResourceManager.LoadOnView<Texture2D>(jacketImagePath);
        }

        /// <summary>
        /// キャラクターのいいね情報のアイテムセットアップ
        /// </summary>
        public void SetupCharaLikeInfo(int charaId)
        {
            ShowInfo(ItemType.CharaLikeInfo);
            SetupLikeText(TextUtil.GetMasterText(MasterString.Category.MasterCharaName, charaId));
            var path = ResourcePath.GetCharaThumbnailIconPath(charaId);
            _charaIcon.texture = ResourceManager.LoadOnView<Texture2D>(path);
        }

        /// <summary>
        /// トレーナーのいいね情報のアイテムセットアップ
        /// </summary>
        public void SetupTrainerLikeInfo(string name)
        {
            ShowInfo(ItemType.TrainerLikeInfo);
            SetupLikeText(TextUtil.Format(TextId.Home424020.Text(), name));
            _trainerIcon.sprite = AtlasUtil.GetSpriteByName(TargetAtlasType.Story, ICON_TRAINER);
        }

        /// <summary>
        /// いいね情報テキストのセットアップ
        /// </summary>
        /// <param name="name"></param>
        private void SetupLikeText(string name)
        {
            _likeText.text = name;
        }

        /// <summary>
        /// アイテムに表示する内容によるオブジェクトの表示/非表示切り替え
        /// </summary>
        /// <param name="itemType"></param>
        private void ShowInfo(ItemType itemType)
        {
            // 楽曲情報かいいね情報かで表示内容を変える
            var isSongInfo = (itemType == ItemType.SongInfo);
            _songInfo.SetActiveWithCheck(isSongInfo);
            _likeInfo.SetActiveWithCheck(!isSongInfo);

            // いいね情報だった場合、キャラかトレーナーかでアイコン変える
            if (!isSongInfo)
            {
                _trainerIcon.gameObject.SetActiveWithCheck(itemType == ItemType.TrainerLikeInfo);
                _charaIcon.gameObject.SetActiveWithCheck(itemType == ItemType.CharaLikeInfo);
            }
        }
        #endregion

    }
}