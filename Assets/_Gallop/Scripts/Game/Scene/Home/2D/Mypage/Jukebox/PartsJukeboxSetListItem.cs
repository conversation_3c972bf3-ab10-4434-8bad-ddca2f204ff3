using System;
using UnityEngine;

namespace Gallop
{
    public class PartsJukeboxSetListItemModel
    {
        public int Id { get; private set; }

        public string LogoPath { get; private set; }
        public string Title { get; private set; }
        public int MusicNum { get; private set; }
        public bool IsCursorActive { get; private set; }
        public Action OnClick { get; private set; }

        public PartsJukeboxSetListItemModel(MasterJukeboxSetlistData.JukeboxSetlistData master, Action<int> onClick)
        {
            Id = master.Id;

            LogoPath = ResourcePath.GetJukeboxSetlistLogo(master.Id);
            Title = master.Title;
            MusicNum = MasterDataManager.Instance.masterJukeboxSetlistMusicData
                .GetListWithSetlistIdOrderBySetOrderAsc(master.Id).Count;
            OnClick = () => onClick?.Invoke(Id);
        }

        public void SetIsCursorActive(int selectedId)
        {
            IsCursorActive = Id == selectedId;
        }
    }

    /// <summary>
    /// ジュークボックス：セットリスト一覧アイテム
    /// </summary>
    public class PartsJukeboxSetListItem : LoopScrollItemBase
    {
        #region SerializeField, member

        /// <summary>
        /// ボタン
        /// </summary>
        [SerializeField]
        private ButtonCommon _button = null;
        
        /// <summary>
        /// ロゴ
        /// </summary>
        [SerializeField]
        private RawImageCommon _logoImage = null;
        
        /// <summary>
        /// 公演名
        /// </summary>
        [SerializeField]
        private TextCommon _titleText = null;

        /// <summary>
        /// 曲数
        /// </summary>
        [SerializeField]
        private TextCommon _musicNumText = null;
        
        /// <summary>
        /// カーソル
        /// </summary>
        [SerializeField]
        private CursorCommon _cursor = null;

        private PartsJukeboxSetListItemModel _model;
        
        #endregion

        #region Method

        public void UpdateItem(PartsJukeboxSetListItemModel model, ResourceManager.ResourceHash resourceHash)
        {
            var oldId = _model?.Id;
            _model = model;

            _cursor.SetActiveWithCheck(model.IsCursorActive);

            if (oldId.HasValue && oldId == _model.Id)
            {
                // 前のデータと同じなら以下の処理は不要
                return;
            }
            _logoImage.texture = ResourceManager.LoadOnHash<Texture2D>(model.LogoPath, resourceHash);
            _titleText.text = model.Title;
            _musicNumText.text = TextId.SingleModeScenarioLive0031.Format(model.MusicNum);
            _button.SetOnClick(() => model.OnClick?.Invoke());
        }

        #endregion
    }
}