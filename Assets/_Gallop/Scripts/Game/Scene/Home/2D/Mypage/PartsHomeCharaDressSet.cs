using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ホーム：キャラと衣装設定用パーツ
    /// </summary>
    [AddComponentMenu("")]
    public class PartsHomeCharaDressSet : MonoBehaviour
    {
        #region SerializeField, Variable
        [SerializeField]
        private TextCommon _titleText = null;
        [SerializeField]
        private ImageCommon _titleBaseImage = null;
        [SerializeField]
        private CharacterButton _charaButton = null;
        [SerializeField]
        private DressButton _dressButton = null;
        #endregion

        #region Method

        private HomeDefine.StandPos _standPos = HomeDefine.StandPos.None;
        private Action<HomeDefine.StandPos> _onClickChara = null;
        private Action<HomeDefine.StandPos> _onClickDress = null;

        /// <summary>
        /// セットアップ
        /// </summary>
        public void Setup(
            HomeDefine.StandPos standPos,
            int charaId,
            int dressId,
            Action<HomeDefine.StandPos> onClickChara,
            Action<HomeDefine.StandPos> onClickDress)
        {
            _standPos = standPos;
            _onClickChara = onClickChara;
            _onClickDress = onClickDress;
            SetTitle(standPos);
            
            UpdateTexture(charaId, dressId);
        }

        /// <summary>
        /// ボタンテクスチャを更新
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="dressId"></param>
        public void UpdateTexture(int charaId, int dressId)
        {
            // キャラアイコン
            var buttonInfo = CharacterButtonInfo.CreateChara(charaId);
            buttonInfo.ForceEnableButton = true;
            buttonInfo.OnTap = (button) => { _onClickChara?.Invoke(_standPos); };

            // ランダムの場合は強制的にランダムアイコンにする
            if (charaId == HomeDefine.FavoriteRandomCharaId)
            {
                buttonInfo.SetUnselectParam(TextId.Common418001.Text(), ResourcePath.CharCommonRoundIconPath);
                buttonInfo.ForcedUseSe = true;
            }

            _charaButton.Setup(buttonInfo);


            // 衣装アイコン
            // ランダムの場合はアイコンを変更する
            if (dressId == HomeDefine.FavoriteRandomDressId)
            {
                _dressButton.InitAtCommon(TextId.Common418001.Text(), _ => _onClickDress?.Invoke(_standPos));
            }
            else
            {
                var charaData = MasterDataManager.Instance.masterCharaData.Get(charaId);
                var dressData = MasterDataManager.Instance.masterDressData.Get(dressId);

                var dressTexturePath = WorkDressData.GetDressIconPath(dressData, charaData?.Sex ?? 0);
                _dressButton.Init(dressTexturePath, _ => _onClickDress?.Invoke(_standPos));
            }
        }

        /// <summary>
        /// 位置に対応したテキストを設定
        /// </summary>
        /// <param name="standPos"></param>
        private void SetTitle(HomeDefine.StandPos standPos)
        {
            _titleText.text = standPos.Text();
            _titleBaseImage.SetDefaultColor(standPos.GetColor());
            _titleBaseImage.UpdateColor();
        }

        #endregion
    }
}