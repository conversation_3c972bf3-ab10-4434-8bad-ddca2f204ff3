using System;
using UnityEngine;
using System.Linq;
using DG.Tweening;

namespace Gallop
{
    /// <summary>
    /// ホーム：イベントストーリーボタン
    /// </summary>
    [AddComponentMenu("")]
    public class PartsStoryEventButton : MonoBehaviour
    {
        private enum State
        {
            None,
            IsInterm,
            IsEnding,
        }

        /// <summary> 「！」バッジの位置 </summary>
        private Vector3 BADGE_POS_OFFSET = new Vector3(205, 117, 0);

        [SerializeField]
        private FlickableButton _flickableButton;

        [SerializeField]
        private RawImageCommon _image;
        
        [SerializeField]
        private GameObject _remainTimeTextRoot;
        
        [SerializeField]
        private TextCommon _remainTimeText;
        
        [SerializeField]
        private GameObject _endingTextRoot;

        private State _state;

        /// <summary> 「！」バッジ </summary>
        private FlashPlayer _badge = null;

        /// <summary>イベントの開始日時</summary>
        public long StartDate => _startDate;
        private long _startDate = 0;

        /// <summary>
        /// ストーリーイベントボタンを生成すべきか
        /// </summary>
        private static bool IsNeedStoryEventButton()
        {
            if (Tutorial.TutorialManager.IsTutorialExecuting())
                return false; // チュートリアル中は生成しない

            int storyEventId = WorkDataManager.Instance.StoryEventData.StoryEventId;
            if (storyEventId <= 0)
                return false; // 開催期間外、または直近での開催予定が無い
            var storyEventData = MasterDataManager.Instance.masterStoryEventData.Get(storyEventId);
            if (storyEventData == null)
                return false;

            return true;
        }

        /// <summary>
        /// ストーリーイベントボタンを生成すべきなら生成と初期化を行う
        /// </summary>
        public static PartsStoryEventButton CreateAndSetupIfNeed(Transform parentTrans, FlickHandler flickHandler)
        {
            if (!IsNeedStoryEventButton())
                return null;

            var storyEventButtonObj = GameObject.Instantiate<GameObject>(ResourceManager.LoadOnView<GameObject>(ResourcePath.STORY_EVENT_BUTTON_PREFAB_PATH), parentTrans);
            if (storyEventButtonObj == null) return null;
            var storyEventButton = storyEventButtonObj.GetComponent<PartsStoryEventButton>();

            storyEventButton.Setup(flickHandler);

            return storyEventButton;
        }

        /// <summary>
        /// 画面イリ処理
        /// </summary>
        public void UpdateOnPlayIn()
        {
            UpdateState();
            UpdateUI();
            UpdateBadge();
        }

        /// <summary>
        /// badgeセットアップ
        /// </summary>
        public void Setup(FlickHandler flickHandler)
        {
            bool needBadge = StoryEventUtil.NeedStoryEventButtonBadge();

            if (_badge == null && needBadge)
            {
                _badge = CreateBadge();
            }

            this.gameObject.SetActiveWithCheck(false);

            var storyEventId = WorkDataManager.Instance.StoryEventData.StoryEventId;
            var storyEventData = MasterDataManager.Instance.masterStoryEventData.Get(storyEventId);
            if (storyEventData != null)
            {
                _startDate = storyEventData.StartDate;
            }

            _flickableButton.FlickHandler = flickHandler;
            _flickableButton.SetOnClick(OnClick);
            _flickableButton.InitializeCollision(true);
        }

        /// <summary>
        /// バッジを生成する
        /// </summary>
        private FlashPlayer CreateBadge()
        {
            var badge = UIUtil.CreateNotifyBadgeIconFlash(this.transform, UIManager.CanvasSoringOrder.Main, _image.canvasRenderer);
            badge.transform.localPosition = BADGE_POS_OFFSET;
            badge.SetLayer(UIManager.GameCanvas.gameObject.layer);
            return badge;
        }

        /// <summary>
        /// 開催状況を更新
        /// </summary>
        private void UpdateState()
        {
            // 開催状況を判定
            _state = State.None;
            if (WorkDataManager.Instance.StoryEventData.IsOpen())
            {
                var storyEventId = WorkDataManager.Instance.StoryEventData.StoryEventId;
                
                var masterEventData = MasterDataManager.Instance.masterStoryEventData.Get(storyEventId);
                if (masterEventData != null)
                {
                    if (TimeUtil.IsInTerm(TimeUtil.FromUnixTimeToLocaleTime(masterEventData.StartDate), TimeUtil.FromUnixTimeToLocaleTime(masterEventData.EndingDate)))
                    {
                        _state = State.IsInterm;
                    }
                    else if (TimeUtil.IsInTerm(TimeUtil.FromUnixTimeToLocaleTime(masterEventData.EndingDate), TimeUtil.FromUnixTimeToLocaleTime(masterEventData.EndDate)))
                    {
                        _state = State.IsEnding;
                    }
                }
            }
        }

        /// <summary>
        /// UI更新
        /// </summary>
        private void UpdateUI()
        {
            if (_state == State.None)
            {
                gameObject.SetActiveWithCheck(false);
                return;
            }
            
            var storyEventId = WorkDataManager.Instance.StoryEventData.StoryEventId;

            var eventLogoPath = ResourcePath.GetStoryEventLogoPath(storyEventId);
            if (!ResourceManager.IsExistAsset(eventLogoPath))
            {
                // リソースが無い場合は表示しない
                gameObject.SetActiveWithCheck(false);
                return;
            }

            gameObject.SetActiveWithCheck(true);
            _image.texture = ResourceManager.LoadOnView<Texture2D>(eventLogoPath);

            var masterEventData = MasterDataManager.Instance.masterStoryEventData.Get(storyEventId);
            if (masterEventData == null) return;
            
            _remainTimeTextRoot.SetActiveWithCheck(false);
            _endingTextRoot.SetActiveWithCheck(false);
            switch (_state)
            {
                case State.IsInterm:
                    _remainTimeTextRoot.SetActiveWithCheck(true);
                    _remainTimeText.text = GetRestTimeString(masterEventData.EndingDate);
                    break;
                case State.IsEnding:
                    _endingTextRoot.SetActiveWithCheck(true);
                    break;
            }
        }
        
        /// <summary>
        /// コールバック：ボタン押下
        /// </summary>
        private void OnClick()
        {
            if (SceneManager.Instance.GetCurrentViewId() == SceneDefine.ViewId.StoryEventHub)
            {
                // HubView内の遷移
                SceneManager.Instance.ChangeView(SceneDefine.ViewId.StoryEventTop);
            }
            else
            {
                StoryEventUtil.GotoStoryEventTop();
            }
        }
        
        /// <summary>
        /// 残り時間の文字列取得取得
        /// </summary>
        public static string GetRestTimeString(long limitTimeStamp)
        {
            string text = null;

            long restTime = limitTimeStamp - TimeUtil.GetServerTimeStamp();
            if (restTime >= 86400)
            {
                int day = (int)(restTime / 86400);
                text = TextUtil.Format(TextId.StoryEvent0041.Text(), day);
            }
            else if (restTime >= 3600)
            {
                int hour = (int)(restTime / 3600);
                text = TextUtil.Format(TextId.StoryEvent0042.Text(), hour);
            }
            else
            {
                int minute = (int)(restTime / 60);
                text = TextUtil.Format(TextId.StoryEvent0043.Text(), minute);
            }

            return text;
        }

        /// <summary>
        /// バッジの更新
        /// </summary>
        private void UpdateBadge()
        {
            bool needBadge = StoryEventUtil.NeedStoryEventButtonBadge();

            if (_badge == null && needBadge)
            {
                _badge = CreateBadge(); // プレゼントでルーレットコインを受け取った場合などにここに入る
            }

            // 表示の更新
            if (_badge != null)
            {
                _badge.SetActiveWithCheck(needBadge);

                if (needBadge)
                {
                    UIUtil.ReplayBadgeIconFlashText(_badge);
                }
            }
        }

        /// <summary>
        /// フリック機能の有効/無効を設定
        /// </summary>
        public void SetEnableFlick(bool isEnable)
        {
            _flickableButton.IsEnableFlick = isEnable;
        }
    }
}