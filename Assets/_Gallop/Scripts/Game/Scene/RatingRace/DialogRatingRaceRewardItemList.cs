using UnityEngine;
using System.Linq;

namespace Gallop
{
    /// <summary>
    /// マンスリーマッチ: 報酬一覧ダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public class DialogRatingRaceRewardItemList : DialogInnerBase
    {
        enum TabCategory
        {
            MAX_CLASS = 0,
            WEEKEND,
            FINALE
        }
        
        #region SerializeFields, Constant

        [Header("タブ")]
        [SerializeField] private FlickToggleGroupCommon _tab = null;
        [SerializeField] private FlickableObject _flickableObject = null;
        
        [Header("最高CLASS報酬")]
        [SerializeField] private GameObject _maxClassRoot = null;
        [SerializeField] private PartsRatingRaceRewardMaxClass _maxClass = null;
        
        [Header("ウィークエンド報酬")]
        [SerializeField] private GameObject _weekEndRoot = null;
        [SerializeField] private PartsRatingRaceRewardWeekEnd _weekEnd = null;

        [Header("フィナーレ報酬")]
        [SerializeField] private GameObject _finaleRoot = null;
        [SerializeField] private PartsRatingRaceRewardFinale _finale = null;

        #endregion
        
        private WorkRatingRaceData _workRatingRaceData => WorkDataManager.Instance.RatingRaceData;

        private bool _isInitMaxClass = false;
        private bool _isInitWeekEnd = false;
        private bool _isInitFinale = false;

        #region override

        /// <summary>
        /// DL登録
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownload(DownloadPathRegister register)
        {
            // 最高CLASS報酬
            PartsRatingRaceRewardMaxClass.RegisterDownload(register);
            // ウィークエンド報酬
            PartsRatingRaceRewardWeekEnd.RegisterDownload(register);
            // フィナーレ報酬
            PartsRatingRaceRewardFinale.RegisterDownload(register);
        }

        /// <summary>
        /// DialogCommonDataを作成
        /// </summary>
        /// <returns></returns>
        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = TextId.Race0260.Text();
            data.CenterButtonText = TextId.Common0007.Text();
            return data;
        }

        /// <summary>
        /// ダイアログの形態（大きさなど）タイプ取得
        /// </summary>
        /// <returns></returns>
        public override DialogCommonBase.FormType GetFormType()
        {
            return DialogCommonBase.FormType.BIG_ONE_BUTTON;
        }

        /// <summary>
        /// ダイアログの親オブジェクト位置タイプ取得
        /// </summary>
        /// <returns></returns>
        public override DialogCommon.Data.ObjectParentType GetParentType()
        {
            return DialogCommon.Data.ObjectParentType.Base;
        }

        #endregion


        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open()
        {
            // 必要なら「各週の最高到達値取得API」を送信
            RatingRaceUtil.SendRatingRaceMaxInfoApiIfNeeded(onComplete: () =>
            {
                var dialog = LoadAndInstantiatePrefab<DialogRatingRaceRewardItemList>(ResourcePath.DIALOG_RATING_RACE_REWARD_ITEM_LIST);
                dialog.InitTab();
                dialog.SetUp();

                var data = dialog.CreateDialogData();
                data.ContentsObject = dialog.gameObject;
                DialogManager.PushDialog(data);
            });
        }

        /// <summary>
        /// 初期化
        /// </summary>
        private void SetUp()
        {
            // タブ
            _tab.SetOnSelectCallback(OnSelectToggle);
            _flickableObject.SetFlickCallback(_tab.OnFlick);
            InitTab();
        }

        /// <summary>
        /// トグル選択時コールバック
        /// </summary>
        /// <param name="id"></param>
        private void OnSelectToggle(int id)
        {
            // タブによってそれぞれ表示・非表示
            bool isMaxClass = id == (int)TabCategory.MAX_CLASS;
            bool isWeekEnd = id == (int)TabCategory.WEEKEND;
            bool isFinale = id == (int)TabCategory.FINALE;
            
            _maxClassRoot.SetActiveWithCheck(isMaxClass);
            _weekEndRoot.SetActiveWithCheck(isWeekEnd);
            _finaleRoot.SetActiveWithCheck(isFinale);

            if (isMaxClass && !_isInitMaxClass)
            {
                _isInitMaxClass = true;

                // 最高CLASS報酬
                _maxClass.SetUp();
                _maxClass.InitScrollPos();
            }
            else if (isWeekEnd && !_isInitWeekEnd)
            {
                _isInitWeekEnd = true;

                // ウィークエンド報酬
                _weekEnd.Setup(_workRatingRaceData.CurrentWeek);
                _weekEnd.InitScrollPos();
            }
            else if (isFinale && !_isInitFinale)
            {
                _isInitFinale = true;

                // フィナーレ報酬
                var max = _workRatingRaceData.RaceRankInfoList.OrderByDescending(info => info.RatingRankMax)
                    .ThenByDescending(info => info.RatingScoreMax)
                    .ThenBy(info => info.RatingRaceCategory).FirstOrDefault();
                _finale.SetUp(max == null ? 0 : (int)max.RatingRankMax);
                _finale.InitScrollPos();
            }
        }

        /// <summary>
        /// タブの初期化とスクロールポジション設定
        /// </summary>
        private void InitTab()
        {
            if (_workRatingRaceData.Phase == RatingRaceDefine.Phase.WeekDay)
            {
                _tab.SelectWithoutNotify((int)TabCategory.MAX_CLASS);
                OnSelectToggle((int)TabCategory.MAX_CLASS);
            }
            else if (_workRatingRaceData.Phase == RatingRaceDefine.Phase.WeekEnd)
            {
                _tab.SelectWithoutNotify((int)TabCategory.WEEKEND);
                OnSelectToggle((int)TabCategory.WEEKEND);
            }
            else
            {
                _tab.SelectWithoutNotify((int)TabCategory.FINALE);
                OnSelectToggle((int)TabCategory.FINALE);
            }
        }
    }
}