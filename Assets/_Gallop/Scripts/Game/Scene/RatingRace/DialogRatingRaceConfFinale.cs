using UnityEngine;
using System.Linq;
using System.Collections.Generic;

namespace Gallop
{
    [AddComponentMenu("")]
    public class DialogRatingRaceConfFinale : DialogInnerBase
    {
        #region DialogCommonBase

        /// <summary> ダイアログの形態（大きさなど）タイプ取得 </summary>
        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        /// <summary> ダイアログの親オブジェクト位置タイプ取得 </summary>
        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        #endregion
        
        #region SerializeField
        
        [SerializeField]
        private TextCommon _confTextCommon = null;
        [SerializeField]
        private TextCommon _countTextCommon = null;
        [SerializeField]
        private TextCommon _nonMaxCountTextCommon = null;
        [SerializeField] 
        private TextCommon _nonMaxTextCommon = null; 
        
        [SerializeField] 
        private RatingRaceTopListItem _raceInfo;
        [SerializeField] 
        private GameObject _finaleRewardItemListRoot;
        [SerializeField] 
        private RatingRaceRewardItem _finaleRewardItem;
        
        #endregion
        
        #region 定数・定義
        
        private const int REWARD_DISP_NUM = 4;
        
        #endregion
        
        private void Setup(WorkRatingRaceData.RaceRankInfo workRaceRankInfo)
        {
            // レース詳細
            _raceInfo.OnItemUpdate(workRaceRankInfo, null, true, false);
            
            // 報酬一覧 1着～REWARD_DISP_NUM着まで
            var rewardGroupIdList = MasterDataManager.Instance.masterRatingRaceFinaleReward
                .GetListWithRatingRaceDataIdOrderByIdAsc(WorkDataManager.Instance.RatingRaceData.CurrentRatingRaceDataId)
                .Where(x => x.RatingRank == (int)workRaceRankInfo.RatingRank).ToList();
            SetUpItemList(rewardGroupIdList);
            
            // テキスト差し替え
            _confTextCommon.text = TextId.RatingRace600046.Text();
            
            // 最高CLASSじゃない場合の注意文言
            if (!IsMaxClass(workRaceRankInfo))
            {
                _nonMaxTextCommon.SetTextWithCustomTag(TextUtil.Format(TextId.RatingRace600047.Text()));
                _nonMaxCountTextCommon.SetTextWithCustomTag(TextUtil.Format(TextId.RatingRace600037.Text(), 1));
                _countTextCommon.SetActiveWithCheck(false);
            }
            // 最高CLASSの場合の注意文言
            else
            {
                _nonMaxTextCommon.SetActiveWithCheck(false);
                _nonMaxCountTextCommon.SetActiveWithCheck(false);
                _countTextCommon.SetTextWithCustomTag(TextUtil.Format(TextId.RatingRace600037.Text(), 1));
            }
        }
        
        /// <summary>
        /// 報酬表示を作成
        /// </summary>
        private void SetUpItemList(List<MasterRatingRaceFinaleReward.RatingRaceFinaleReward> rewardGroupIdList)
        {
            // まず、スクロービューの要素をクリアする
            foreach (Transform n in _finaleRewardItemListRoot.transform)
            {
                GameObject.Destroy(n.gameObject);
            }
            
            var count = rewardGroupIdList.Count;
            for (int i = 0; i < REWARD_DISP_NUM; i++)
            {
                // 報酬リストを取得
                var rewardList = MasterDataManager.Instance.masterRatingRaceRewardGroup.GetListWithRewardGroupIdOrderByIdAsc(rewardGroupIdList[i].RewardGroupId);
                // 報酬が無かったら、次のループへ
                if (rewardList.Count < 1)
                {
                    continue;
                }
                
                // これらをまとめてモデルを作成
                var model = new RatingRaceRewardItem.RatingRaceRewardItemModel(i + 1, (i + 1) >= REWARD_DISP_NUM ? count : RatingRaceDefine.INVALID_VALUE, rewardList, false);
                // 報酬表示を生成
                var finaleRewardItem = Instantiate(_finaleRewardItem, _finaleRewardItemListRoot.transform);
                finaleRewardItem.SetUpFinale(model);
            }
        }

        private bool IsMaxClass(WorkRatingRaceData.RaceRankInfo workRaceRankInfo)
        {
            var max = WorkDataManager.Instance.RatingRaceData.RaceRankInfoList.OrderByDescending(info => info.RatingRankMax).FirstOrDefault();
            return workRaceRankInfo.RatingRank >= max?.RatingRank;
        }

        /// <summary>
        /// ダイアログ開く
        /// </summary>
        public static void OpenDialog(WorkRatingRaceData.RaceRankInfo workRaceRankInfo, System.Action onClickButton)
        {
            // Dialog内オブジェクト生成
            var dialogObj = LoadAndInstantiatePrefab<DialogRatingRaceConfFinale>(ResourcePath.DIALOG_RATING_RACE_CONF_FINALE);
            // 初期化
            dialogObj.Setup(workRaceRankInfo);
            
            // DialogData取得
            var dialogData = dialogObj.CreateDialogData();
            dialogData.Title = TextId.Common0009.Text();
            dialogData.LeftButtonText = TextId.Common0004.Text();
            dialogData.RightButtonText = TextId.Common0003.Text();
            // OKボタン
            dialogData.RightButtonCallBack = (dialog) =>
            {
                dialog.Close(() =>
                {
                    onClickButton();
                });
            };

            DialogManager.PushDialog(dialogData);
        }
    }
}