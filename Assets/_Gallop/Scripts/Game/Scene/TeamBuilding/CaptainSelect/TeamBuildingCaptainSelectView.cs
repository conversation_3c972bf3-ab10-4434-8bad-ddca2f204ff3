using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// チーム作りイベント：キャプテン選択画面
    /// </summary>
    public class TeamBuildingCaptainSelectView : ViewBase
    {
        /// <summary> キャプテンキャラクター選択パーツ </summary>
        [SerializeField]
        private PartsRaceEntryCharacterSelect _partsCaptainSelect;
        public PartsRaceEntryCharacterSelect PartsCaptainSelect => _partsCaptainSelect;

        /// <summary>次レース情報パーツ</summary>
        [SerializeField]
        private PartsTeamBuildingRaceCondition _partsRaceCondition;
        public PartsTeamBuildingRaceCondition PartsRaceCondition => _partsRaceCondition;

        /// <summary>次レース情報パーツ</summary>
        [SerializeField]
        private PartsTeamBuildingChangeRank _partsChangeRank;
        public PartsTeamBuildingChangeRank PartsChangeRank => _partsChangeRank;

        /// <summary>「決定」ボタンのRectTransform</summary>
        [SerializeField]
        private RectTransform _decideButtonTransform;
        public RectTransform DecideButtonTransform => _decideButtonTransform;

        /// <summary>次走出走レースボタン</summary>
        [SerializeField]
        private ButtonCommon _nextRaceButton;

        public ButtonCommon NextRaceButton => _nextRaceButton;
        
        [Header("チーム評価点")]

        /// <summary>キャプテン変更前のチーム評価点テキスト</summary>
        [SerializeField]
        private TextCommon _beforeTeamScoreText;
        public TextCommon BeforeTeamScoreText => _beforeTeamScoreText;

        /// <summary>キャプテン変更後のチーム評価点テキスト</summary>
        [SerializeField]
        private TextCommon _afterTeamScoreText;
        public TextCommon AfterTeamScoreText => _afterTeamScoreText;

        /// <summary>キャプテン変更後に評価点が上昇する場合のチーム評価点テキスト</summary>
        [SerializeField]
        private BitmapTextCommon _afterTeamScoreUpBitmapText;
        public BitmapTextCommon AfterTeamScoreUpBitmapText => _afterTeamScoreUpBitmapText;

        /// <summary>キャプテン変更後に評価点が下がる場合のチーム評価点テキスト</summary>
        [SerializeField]
        private BitmapTextCommon _afterTeamScoreDownBitmapText;
        public BitmapTextCommon AfterTeamScoreDownBitmapText => _afterTeamScoreDownBitmapText;

        [Header("名簿登録 & チームスキル情報")]

        /// <summary> 名簿登録済で表示するチェックマーク </summary>
        [SerializeField]
        private ImageCommon _collectionCheckImage;
        public ImageCommon CollectionCheckImage => _collectionCheckImage;

        /// <summary> 図鑑登録状況テキスト </summary>
        [SerializeField]
        private TextCommon _collectionStatusText;
        public TextCommon CollectionStatusText => _collectionStatusText;

        /// <summary> スキル情報の親オブジェクト </summary>
        [SerializeField]
        private GameObject _skillInfoRoot;
        public GameObject SkillInfoRoot => _skillInfoRoot;

        /// <summary> スキルアイコン </summary>
        [SerializeField]
        private SkillIcon _skillIcon;
        public SkillIcon SkillIcon => _skillIcon;

        /// <summary> スキル獲得済チェックマーク </summary>
        [SerializeField]
        private ImageCommon _skillCheckImage;
        public ImageCommon SkillCheckImage => _skillCheckImage;

        /// <summary> スキル獲得状態テキスト </summary>
        [SerializeField]
        private TextCommon _skillStatusText;
        public TextCommon SkillStatusText => _skillStatusText;

        /// <summary> スキル組み合わせキャラ詳細 </summary>
        [SerializeField]
        private ButtonCommon _skillCharaGroupButton;
        public ButtonCommon SkillCharaGroupButton => _skillCharaGroupButton;
    }

    /// <summary>
    /// チーム作りイベント：キャプテン選択画面のViewController
    /// </summary>
    public class TeamBuildingCaptainSelectViewController : ViewControllerBase<TeamBuildingCaptainSelectView>
    {
        #region private変数

        private const float DECIDE_BUTTON_Y = 297.7f;
        private const float DECIDE_BUTTON_Y_WITHOUT_FOOTER = 156f;

        /// <summary> チーム作りイベントWorkData </summary>
        private WorkTeamBuildingData _workTeamBuilding = WorkDataManager.Instance.TeamBuildingData;

        /// <summary> 現在(キャプテン変更前)のチーム評価点 </summary>
        private int _currentTeamScore;

        /// <summary> キャプテンの所持殿堂入りウマ娘データ </summary>
        private WorkTrainedCharaData.TrainedCharaData _workCaptainTrainedData;

        public class ViewInfo : IViewInfo
        {
            /// <summary> 選択不可にするキャラIdの一覧 </summary>
            public List<int> UnselectableCharaList { set; get; }

            /// <summary> キャプテン入れ替え終了後に完了ダイアログを出すかどうか </summary>
            public bool ShowChangeCompleteDialog { set; get; } = false;

            /// <summary> 共通フッターを出すかどうか </summary>
            public bool? ShowCommonFooter { set; get; } = null;
        }

        #endregion

        #region override

        /// <summary>
        /// ダウンロード登録
        /// </summary>
        public override void RegisterDownload(DownloadPathRegister register)
        {
            // キャラクター選択パーツ
            var charaIdList = WorkDataManager.Instance.TrainedCharaData.List.Select(x => x.CharaId.GetDecrypted()).Distinct().ToList();
            PartsRaceEntryCharacterSelect.RegisterDownload(register, charaIdList, PartsRaceEntryCharacterSelect.ViewType.TeamBuilding);
            DialogSetRunStyle.RegisterDownloadAssets(register);
        }

        /// <summary>
        /// Viewイリ前の処理
        /// </summary>
        public override IEnumerator InitializeEachPlayIn()
        {
            _currentTeamScore = _workTeamBuilding.IsFirstTransition() ?
                TeamBuildingDefine.DEFAULT_TEAM_SCORE : _workTeamBuilding.MyTeamInfo.TeamScore.GetDecrypted();
            _view.PartsChangeRank.Initialize(_currentTeamScore, _currentTeamScore, showScore: false);
            _view.BeforeTeamScoreText.text = TextUtil.ToCommaSeparatedString(_currentTeamScore);

            _view.AfterTeamScoreUpBitmapText.LoadFont();
            _view.AfterTeamScoreDownBitmapText.LoadFont();

            _view.NextRaceButton.SetOnClick(OnClickNextRaceButton);

            yield return base.InitializeEachPlayIn();
        }

        /// <summary>
        /// 次走出走レースダイアログを開く
        /// </summary>
        private void OnClickNextRaceButton()
        {
            DialogTeamBuildingNextRace.Open();
        }

        /// <summary>
        /// 共通Footerを使うかどうかを上書き設定する
        /// </summary>
        public override bool? GetDynamicUseFooter()
        {
            if (GetViewInfo() is ViewInfo viewInfo)
            {
                return viewInfo.ShowCommonFooter;
            }

            return null;
        }

        /// <summary>
        /// Viewイリ処理
        /// </summary>
        public override IEnumerator PlayInView()
        {
            var myteamCaptainTrainedData = _workTeamBuilding.MyTeamInfo.GetCaptainMemberInfo()?.TrainedCharaData;

            if (myteamCaptainTrainedData != null)
            {
                // 二つ名を変更した処理が即座に反映されるように、所持している殿堂入りウマ娘一覧からキャプテンのデータを取得しなおす
                _workCaptainTrainedData = WorkDataManager.Instance.TrainedCharaData.Get(myteamCaptainTrainedData.Id);
            }

            if (_workCaptainTrainedData == null || !_workCaptainTrainedData.IsPlayer || !WorkDataManager.Instance.TrainedCharaData.HasData(_workCaptainTrainedData.Id))
            {
                Debug.LogWarningFormat("所持していない殿堂入りウマ娘がキャプテン登録されています");
                _workCaptainTrainedData = null;
            }

            var unselectabeleCharaIdList = _workTeamBuilding.MyTeamInfo.MemberInfoList
                .Where(member => member.MemberId != WorkTeamBuildingData.MemberInfo.CAPTAIN_MEMBER_ID && (member.IsTrainedChara() || member.IsUniqueCharaNpc()))
                .Select(member => (int)member.CharaId)
                .ToList();

            var viewInfo = GetViewInfo() as ViewInfo;
            if (viewInfo != null)
            {
                if (viewInfo.UnselectableCharaList != null)
                {
                    unselectabeleCharaIdList.AddRange(viewInfo.UnselectableCharaList);
                }
            }

            var trainedCharaList = WorkDataManager.Instance.TrainedCharaData.List;
            var unselectableTrainedCharaList = trainedCharaList
                .Where(trainedChara => unselectabeleCharaIdList.Exists(charaId => charaId == trainedChara.CharaId))
                .ToList();


            PartsRaceEntryCharacterSelect.SetupParam setupParam;
            if (_workTeamBuilding.IsFirstTransition() || _workCaptainTrainedData == null)
            {
                // 初回遷移のキャラクター選択パーツのセットアップ用パラメタ設定
                setupParam = new PartsRaceEntryCharacterSelect.SetupParam()
                {
                    ViewType = PartsRaceEntryCharacterSelect.ViewType.TeamBuilding,
                    TrainedCharaList = WorkDataManager.Instance.TrainedCharaData.List,
                    UnselectableCanLongTapTrainedCharaList = unselectableTrainedCharaList,
                    OnDecide = OnDecide,
                    OnSelect = OnSelectCaptain,
                };
            }
            else
            {
                unselectableTrainedCharaList.Add(_workCaptainTrainedData);
                // 通常遷移時のキャラクター選択パーツのセットアップ用パラメタ設定
                setupParam = new PartsRaceEntryCharacterSelect.SetupParam()
                {
                    ViewType = PartsRaceEntryCharacterSelect.ViewType.TeamBuilding,
                    TrainedCharaList = WorkDataManager.Instance.TrainedCharaData.List,
                    InitialSelectedList = new List<WorkTrainedCharaData.TrainedCharaData> { _workCaptainTrainedData },
                    UnselectableCanLongTapTrainedCharaList = unselectableTrainedCharaList,
                    OnDecide = OnDecide,
                    OnSelect = OnSelectCaptain,
                };
            }

            setupParam.CharacterButtonNoticeModelFactory = new CharacterButtonNoticeModelFactory();

            setupParam.GetUnselectableReason =
                selectChara => (selectChara.CharaId == _workCaptainTrainedData?.CharaId) ? TextId.TeamBuilding424117.Text() : TextId.TeamBuilding424118.Text();

            // キャラクター選択パーツセットアップ
            BGManager.Instance.SetBg(SceneDefine.BgId.Character);
            _view.PartsCaptainSelect.Setup(setupParam);
            _view.PartsCaptainSelect.PlaySelectedCharaVoice();

            _view.PartsCaptainSelect.AdjustSkillListHeightForTeamBuilding();

            // 直近のレース表示セットアップ
            _view.PartsRaceCondition.Setup(_workTeamBuilding.GetFirstRaceConditionInfo());

            // 決定ボタンの位置設定
            var decideButtonY = (viewInfo?.ShowCommonFooter ?? true) ? DECIDE_BUTTON_Y : DECIDE_BUTTON_Y_WITHOUT_FOOTER;
            _view.DecideButtonTransform.SetAnchoredPositionY(decideButtonY);

            yield return base.InitializeEachPlayIn();
        }

        /// <summary>
        /// Viewハケ処理
        /// </summary>
        public override IEnumerator PlayOutView()
        {
            _view.PartsCaptainSelect.PlayOut();
            yield return base.PlayOutView();
        }

        /// <summary>
        /// View終了処理(HubView内での遷移でも呼ばれる)
        /// </summary>
        public override IEnumerator EndView()
        {
            _workCaptainTrainedData = null;
            _view.PartsCaptainSelect.Release();
            yield return base.EndView();
        }

        /// <summary>
        /// 戻るボタン押下時処理
        /// </summary>
        public override void OnClickBackButton()
        {
            if (_workCaptainTrainedData != null)
            {
                // 殿堂入りウマ娘詳細ダイアログから二つ名が変更されている可能性があるので、チーム作りのデータにも反映
                _workTeamBuilding.MyTeamInfo.GetCaptainMemberInfo().TrainedCharaData.SetNickNameId(_workCaptainTrainedData.NickNameId);
            }

            GoToPreviousView();
        }

        /// <summary>
        /// OSのバックキー押下時処理
        /// </summary>
        public override void OnClickOsBackKey()
        {
            OnClickBackButton();
        }

        #endregion

        #region privateメソッド

        /// <summary>
        /// 名簿登録状況セットアップ
        /// </summary>
        private void SetupCollectionAndTeamSkillInfo(WorkTrainedCharaData.TrainedCharaData selectedChara)
        {
            // 名簿登録状況
            var collectionRegisteredInfo = TeamBuildingUtil.GetCollectionRegisteredInfo(selectedChara.CharaId);

            _view.CollectionCheckImage.SetActiveWithCheck(collectionRegisteredInfo.IsRegistered);
            _view.CollectionStatusText.text = collectionRegisteredInfo.StatusText;
            
            // チームスキル
            var charaGroup = TeamBuildingUtil.GetTeamSkillCharaGroupByCharaId(selectedChara.CharaId);

            if (charaGroup != null)
            {
                // チームスキル非表示
                _view.SkillInfoRoot.SetActiveWithCheck(true);
                
                // スキルが登録済みか
                bool isRegistered = WorkDataManager.Instance.TeamBuildingData.IsRegisteredCollectionCharaGroup(charaGroup.CharaGroupId);

                _view.SkillCheckImage.SetActiveWithCheck(isRegistered);
                _view.SkillStatusText.text = (isRegistered) ? TextId.TeamBuilding400115.Text() : TextId.TeamBuilding352001.Text();

                var eventId = WorkDataManager.Instance.TeamBuildingData.TeamBuildingEventId;
                // チームスキル獲得情報マスタ
                var masterSkillList = MasterDataManager.Instance.masterTeamBuildingCollectionSet
                    .GetListWithTeamBuildingEventIdOrderByCharaGroupIdAsc(eventId);

                if (!masterSkillList.IsNullOrEmpty())
                {
                    var masterCollectionSet = masterSkillList.FirstOrDefault(x => x.CharaGroupId == charaGroup.CharaGroupId);
                    if (masterCollectionSet != null)
                    {
                        _view.SkillIcon.Setup(masterCollectionSet.SkillId, false);
                        _view.SkillCharaGroupButton.SetOnClick(() =>
                        {
                            DialogTeamBuildingCollectionSetDetail.Open(masterCollectionSet.Id);
                        });
                    }
                }
            }
            else
            {
                // チームスキル非表示
                _view.SkillInfoRoot.SetActiveWithCheck(false);
            }
        }

        /// <summary>
        /// キャプテンのキャラボタンが選択された際の処理
        /// </summary>
        private void OnSelectCaptain(WorkTrainedCharaData.TrainedCharaData selectedChara)
        {
            SetupCollectionAndTeamSkillInfo(selectedChara);

            var newTeamScore = TeamBuildingUtil.GetNewTeamScoreWithChangeCaptain(selectedChara);
            _view.PartsChangeRank.SetAfterRankIcon(newTeamScore);

            if (newTeamScore == _currentTeamScore)
            {
                // チーム評価点に変動が無い場合
                _view.AfterTeamScoreText.VerticalGradientColor = VerticalGradientColorType.Brown;
                _view.AfterTeamScoreUpBitmapText.SetActiveWithCheck(false);
                _view.AfterTeamScoreDownBitmapText.SetActiveWithCheck(false);
            }
            else
            {
                // ContentSizeFitterで表示領域を確保するために必要なので、Activeにしたまま文字色をNoneにして不要な表示を消す
                var fontColorNone = TextFormat.GetFontColor(FontColorType.None);
                _view.AfterTeamScoreText.SetGradientVertical(fontColorNone, fontColorNone);
                _view.AfterTeamScoreText.VerticalGradientColor = VerticalGradientColorType.None;

                // チーム評価点が上昇するか下降するかで表示を分ける
                var isScoreUp = newTeamScore > _currentTeamScore;
                _view.AfterTeamScoreUpBitmapText.SetActiveWithCheck(isScoreUp);
                _view.AfterTeamScoreDownBitmapText.SetActiveWithCheck(!isScoreUp);

                var targetBitmapText = isScoreUp ? _view.AfterTeamScoreUpBitmapText : _view.AfterTeamScoreDownBitmapText;
                targetBitmapText.text = TextUtil.ToCommaSeparatedString(newTeamScore);
            }

            _view.AfterTeamScoreText.text = TextUtil.ToCommaSeparatedString(newTeamScore);
            _view.AfterTeamScoreText.UpdateColor();
            _view.AfterTeamScoreText.OnUpdate();
        }

        /// <summary>
        /// 前の画面に戻る
        /// （戻り先の画面が指定されていなければイベントTOPへ遷移）
        /// </summary>
        private void GoToPreviousView()
        {
            if (SceneManager.Instance.BackUsingStack())
            {
                return;
            }

            // 戻り先が指定されていないのでイベントTOPへ遷移
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.TeamBuildingTop);
        }

        /// <summary>
        /// 決定ボタンコールバック
        /// </summary>
        private void OnDecide(List<WorkTrainedCharaData.TrainedCharaData> selectedCharaList)
        {
            var selectedChara = selectedCharaList.FirstOrDefault();
            if (selectedChara == null)
            {
                return;    
            }

            var trainedId = selectedChara.Id;
            var currentRunStyle = selectedChara.RunningStyle;

            // 走法決定ダイアログを出す
            DialogSetRunStyle.PushDialog(selectedChara,　currentRunStyle,
                selectedRunStyle => 
                {
                    if (_workTeamBuilding.IsFirstTransition())
                    {
                        var newTeamScore = TeamBuildingUtil.GetNewTeamScoreWithChangeCaptain(selectedChara);
                        DialogTeamBuildingRegisterTeam.Open(selectedChara, selectedRunStyle, newTeamScore,
                            onCancel: () =>
                            {
                                // 二つ名が変更されている可能性があるのでUIを更新する
                                _view.PartsCaptainSelect.UpdateNameWithStatus();
                            });
                    }
                    else
                    {
                        // 走法が決定されたら、キャプテン変更変更確認してから通信
                        ConfirmChangeCaptain(selectedChara, onDecide => SendChangeCaptainApi(trainedId, selectedRunStyle, onDecide));
                    }
                });
        }

        /// <summary>
        /// キャプテン変更確認
        /// </summary>
        private void ConfirmChangeCaptain(WorkTrainedCharaData.TrainedCharaData newCaptainData, Action<Action/* onComplete */> onDecide)
        {
            var newTeamScore = TeamBuildingUtil.GetNewTeamScoreWithChangeCaptain(newCaptainData);
            DialogTeamBuildingConfirmChangeCaptain.Open(_currentTeamScore, newTeamScore,
                onDecide: dialog =>
                {
                    // 決定時のコールバックを実行し、コールバックの処理が終わったらダイアログを閉じる
                    onDecide?.Invoke(dialog.Close);
                });
        }

        /// <summary>
        /// 決定ボタンコールバック
        /// </summary>
        private void SendChangeCaptainApi(int newCaptainTrainedCharaId, RaceDefine.RunningStyle runStyle, System.Action onComplete)
        {
            var request = new TeamBuildingChangeCaptainRequest
            {
                trained_chara_id = newCaptainTrainedCharaId,
                running_style = (int)runStyle,
            };

            request.Send(response =>
            {
                onComplete?.Invoke();

                var beforeBestScore = _workTeamBuilding.MyTeamInfo.BestTeamScore;

                // チーム作りイベントのワークへ反映
                _workTeamBuilding.Update(response.data, newCaptainTrainedCharaId, runStyle);

                var afterBestScore = _workTeamBuilding.MyTeamInfo.BestTeamScore;

                // 必要ならランクアップ演出・キャプテン変更完了ダイアログを出してから戻る
                var rewardList = response.data.reward_array.ToList();
                TeamBuildingUtil.OpenRankUpCutinDialogIfNeeded(beforeBestScore, afterBestScore, rewardList, onBeginClose: () =>
                {
                    if (GetViewInfo() is ViewInfo viewInfo && viewInfo.ShowChangeCompleteDialog)
                    {
                        OpenCaptainChangeCompleteDialog(GoToPreviousView);
                        return;
                    }

                    GoToPreviousView();
                });

            });
        }

        /// <summary>
        /// キャプテン変更完了ダイアログを出す
        /// </summary>
        private void OpenCaptainChangeCompleteDialog(Action onDestroy)
        {
            var dialogData = new DialogCommon.Data().SetSimpleOneButtonMessage(TextId.TeamBuilding424121,TextId.TeamBuilding424122.Text());
            dialogData.DestroyCallBack = onDestroy;
            DialogManager.PushDialog(dialogData);
        }

        #endregion
    }

}
