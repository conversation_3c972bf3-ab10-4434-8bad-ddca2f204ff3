using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    [CreateAssetMenu(menuName = "ScriptableObject/Camera/CameraCharacterCorrectionPreset")]
    public class CameraCharacterCorrectionPreset : ScriptableObject
    {
        [System.Serializable]
        public class CorrectionPresetEntry
        {
            public SceneDefine.ViewId ViewId;
            public Vector2 LowCorrectionValue;
            public Vector2 NormalCorrectionValue;
            public Vector2 HeightCorrectionValue;
            public Vector3 CameraInitPosValue;
            public UIType ListUIType;
        }

        [System.Serializable]
        public class CorrectionPresetRaceUI : CorrectionPresetEntry {}

        public List<CorrectionPresetEntry> PresetList = new List<CorrectionPresetEntry>();
        public List<CorrectionPresetRaceUI> PresetRaceUIList = new List<CorrectionPresetRaceUI>();
    };
}

