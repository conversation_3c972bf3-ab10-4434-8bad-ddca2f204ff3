using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// バッファクリア用のカメラ
    /// 
    /// Game起動時に生成され、ゲーム終了時まで保持される
    /// _Bootからゲームを起動した場合は_Boot直下のカメラが、Directシーンから起動した場合は_Gaem直下のカメラが採用される
    /// </summary>
    public class BufferClearCamera : MonoSingleton<BufferClearCamera>
    {
        [SerializeField]
        private Camera _clearCamera = null;

        /// <summary>
        /// バッファクリア用カメラの初期化
        /// </summary>
        protected override void OnInitialize()
        {
            DontDestroyOnLoad(_clearCamera);
            _clearCamera.backgroundColor = Color.white;
        }

        /// <summary>
        /// バッファクリア用カメラの切り替え
        /// </summary>
        public static void SetClearCameraEnable(bool enable)
        {
            if( !HasInstance() ) { return; }
            Instance._clearCamera.enabled = enable;
        }
    }
}