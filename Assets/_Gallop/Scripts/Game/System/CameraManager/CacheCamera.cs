using UnityEngine;
using System.Collections;

namespace Gallop
{
    public class CacheCamera
    {
        private Camera _camera = null;
        public Camera camera { get { return _camera; } }

        private Transform _cacheTransform = null;
        public Transform cacheTransform { get { return _cacheTransform; } }

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="c"></param>
        public CacheCamera(Camera c)
        {
            Set(c);
        }

        /// <summary>
        /// 設定
        /// </summary>
        /// <param name="c"></param>
        public void Set(Camera c)
        {
            _camera = c;
            if (c == null)
            {
                _cacheTransform = null;
            }
            else
            {
                _cacheTransform = c.GetComponent<Transform>();
            }
        }
    }
}
