using UnityEngine;

namespace Gallop
{
    public class AutoOffsetResult
    {
        public bool IsOutOfFrame { get; set; } = false;
        public Vector3 Offset { get; set; }

        public void Clear()
        {
            IsOutOfFrame = false;
            Offset = Math.VECTOR3_ZERO;
        }
    }

    public class AutoOffsetLogic
    {
        private Matrix4x4 _localPositions;

        public AutoOffsetLogic(Vector3 lb, Vector3 rb, Vector3 lt, Vector3 rt)
        {
            _localPositions = new Matrix4x4(
                new Vector4(lb.x, lb.y, lb.z, 1),     // 左下
                new Vector4(rb.x, rb.y, rb.z, 1),     // 右下
                new Vector4(lt.x, lt.y, lt.z, 1),     // 左上
                new Vector4(rt.x, rt.y, rt.z, 1));    // 右上
        }

        public AutoOffsetResult CalcOffset(Matrix4x4 model, Matrix4x4 view, Matrix4x4 proj)
        {
            var result = new AutoOffsetResult();

            // 背景用の頂点をMVP変換してクリップ空間での座標を計算している
            // ワールド座標は最後に計算で使うので変数に格納している
            var worldPositions = model * _localPositions;
            var clipPositions = proj * view * worldPositions;

            // x,y,z 要素を w要素で除算して、-1 ~ 1 のクリップ空間座標に正規化する
            var divBym30 = 1f / clipPositions.m30;
            var divBym31 = 1f / clipPositions.m31;
            var divBym32 = 1f / clipPositions.m32;
            var divBym33 = 1f / clipPositions.m33;

            // 正規化されたクリップ空間座標
            //               y
            //(-1, 1)        ^           (1, 1)
            //   LT|         |          | RT
            // ----+---------+----------+----
            //     |         |(0,0)     |
            // ----+---------*----------+---->x
            //     |         |          |
            // ----+---------+----------+----
            //   LB|         |          | RB
            //(-1, -1)                   (1, -1)
            var LB = new Vector3(clipPositions.m00 * divBym30, clipPositions.m10 * divBym30, clipPositions.m20 * divBym30);
            var RB = new Vector3(clipPositions.m01 * divBym31, clipPositions.m11 * divBym31, clipPositions.m21 * divBym31);
            var LT = new Vector3(clipPositions.m02 * divBym32, clipPositions.m12 * divBym32, clipPositions.m22 * divBym32);
            var RT = new Vector3(clipPositions.m03 * divBym33, clipPositions.m13 * divBym33, clipPositions.m23 * divBym33);

            var offsetInClipSpace = Vector3.zero;
            // 上下のはみだし検出
            if (LT.y < 1f || RT.y < 1f)
            {
                // 上がはみ出ている
                offsetInClipSpace.y = 1f - Mathf.Min(LT.y, RT.y);
                result.IsOutOfFrame = true;
            }
            else if (LB.y > -1f || RB.y > -1f)
            {
                // 下がはみ出ている
                offsetInClipSpace.y = -1f - Mathf.Max(LT.y, RT.y);
                result.IsOutOfFrame = true;
            }
            else
            {
                // はみ出し無し
            }

            // 左右のはみだし検出
            if (LB.x > -1f || LT.x > -1f)
            {
                // 左がはみ出ている
                offsetInClipSpace.x = -1f - Mathf.Min(LT.x, RT.x);
                result.IsOutOfFrame = true;
            }
            else if (RB.x < 1f || RT.x < 1f)
            {
                // 右がはみ出ている
                offsetInClipSpace.x = 1f - Mathf.Max(LT.x, RT.x);
                result.IsOutOfFrame = true;
            }
            else
            {
                // はみ出し無し
            }

            // 補正不要ならここで処理終了
            if (!result.IsOutOfFrame)
            {
                return result;
            }

            // クリップ空間でのオフセット量をワールド空間に変換する
            // 今までやってきた処理を逆に行えば良い

            // クリップ空間における、オフセット適用後のLBの頂点座標を求める
            var lbPositionInClipSpace = new Vector4(
                (LB.x + offsetInClipSpace.x) * clipPositions.m30,
                (LB.y + offsetInClipSpace.y) * clipPositions.m30,
                (LB.z + offsetInClipSpace.z) * clipPositions.m30,
                clipPositions.m30);

            // LBの頂点座標をワールド空間に変換
            var newLBInWorldSpace = view.inverse * proj.inverse * lbPositionInClipSpace;

            // オリジナルのLB座標と、オフセット適用後のLB座標を比較し、ワールド空間でのオフセット量を計算
            var offsetInWorldSpace = worldPositions.GetColumn(0) - newLBInWorldSpace;
            result.Offset = offsetInWorldSpace;
            return result;
        }

        public bool IsOutOfFrame(Matrix4x4 model, Matrix4x4 view, Matrix4x4 proj)
        {
            Vector3 clipLT = new Vector3(-1f,  1f, 0f);
            Vector3 clipRT = new Vector3( 1f,  1f, 0f);
            Vector3 clipLB = new Vector3(-1f, -1f, 0f);
            Vector3 clipRB = new Vector3( 1f, -1f, 0f);

            // 背景用の頂点をMVP変換してクリップ空間での座標を計算している
            var worldPositions = model * _localPositions;
            var clipPositions = proj * view * worldPositions;

            // x,y,z 要素を w要素で除算して、-1 ~ 1 のクリップ空間座標に正規化する
            var divBym30 = 1f / clipPositions.m30;
            var divBym31 = 1f / clipPositions.m31;
            var divBym32 = 1f / clipPositions.m32;
            var divBym33 = 1f / clipPositions.m33;

            // 正規化されたクリップ空間座標
            //               y
            //(-1, 1)        ^           (1, 1)
            //   LT|         |          | RT
            // ----+---------+----------+----
            //     |         |(0,0)     |
            // ----+---------*----------+---->x
            //     |         |          |
            // ----+---------+----------+----
            //   LB|         |          | RB
            //(-1, -1)                   (1, -1)
            var LB = new Vector3(clipPositions.m00 * divBym30, clipPositions.m10 * divBym30, clipPositions.m20 * divBym30);
            var RB = new Vector3(clipPositions.m01 * divBym31, clipPositions.m11 * divBym31, clipPositions.m21 * divBym31);
            var LT = new Vector3(clipPositions.m02 * divBym32, clipPositions.m12 * divBym32, clipPositions.m22 * divBym32);
            var RT = new Vector3(clipPositions.m03 * divBym33, clipPositions.m13 * divBym33, clipPositions.m23 * divBym33);

            // いずれかの頂点が画面内にある＝隙間がある
            if (-1f < LT.x && LT.x < 1f && -1f < LT.y && LT.y < 1f) return true;
            if (-1f < RT.x && RT.x < 1f && -1f < RT.y && RT.y < 1f) return true;
            if (-1f < LB.x && LB.x < 1f && -1f < LB.y && LB.y < 1f) return true;
            if (-1f < RB.x && RB.x < 1f && -1f < RB.y && RB.y < 1f) return true;

            // 頂点全てが画面の領域外にある＝隙間がある（背景が一切表示されていない）
            if (LT.x < -1f && RT.x < -1f && LB.x < -1f && RB.x < -1f) return true;
            if (LT.x >  1f && RT.x >  1f && LB.x >  1f && RB.x >  1f) return true;
            if (LT.y < -1f && RT.y < -1f && LB.y < -1f && RB.y < -1f) return true;
            if (LT.y >  1f && RT.y >  1f && LB.y >  1f && RB.y >  1f) return true;

            // 線分が交差する＝隙間がある
            if (IsIntersecting(LT, RT, clipLT, clipRT)) return true;
            if (IsIntersecting(LT, RT, clipLT, clipLB)) return true;
            if (IsIntersecting(LT, RT, clipRT, clipRB)) return true;
            if (IsIntersecting(LT, RT, clipLB, clipRB)) return true;

            if (IsIntersecting(LT, LB, clipLT, clipRT)) return true;
            if (IsIntersecting(LT, LB, clipLT, clipLB)) return true;
            if (IsIntersecting(LT, LB, clipRT, clipRB)) return true;
            if (IsIntersecting(LT, LB, clipLB, clipRB)) return true;

            if (IsIntersecting(RT, RB, clipLT, clipRT)) return true;
            if (IsIntersecting(RT, RB, clipLT, clipLB)) return true;
            if (IsIntersecting(RT, RB, clipRT, clipRB)) return true;
            if (IsIntersecting(RT, RB, clipLB, clipRB)) return true;

            if (IsIntersecting(LB, RB, clipLT, clipRT)) return true;
            if (IsIntersecting(LB, RB, clipLT, clipLB)) return true;
            if (IsIntersecting(LB, RB, clipRT, clipRB)) return true;
            if (IsIntersecting(LB, RB, clipLB, clipRB)) return true;

            return false;
        }

        /// <summary>
        /// 線分が交差しているかどうか
        /// </summary>
        /// <param name="seg0start">線分0の始点</param>
        /// <param name="seg0end">線分0の終点</param>
        /// <param name="seg1start">線分1の始点</param>
        /// <param name="seg1end">線分1の終点</param>
        /// <returns></returns>
        private static bool IsIntersecting(Vector3 seg0start, Vector3 seg0end, Vector3 seg1start, Vector3 seg1end)
        {
            var seg0vec = seg0end - seg0start;
            var seg1vec = seg1end - seg1start;
            var cross = Cross(seg0vec, seg1vec);
            // 外積が0の場合は平行
            if (Math.IsFloatEqualLight(cross, 0f))
            {
                return false;
            }

            var startvec = seg1start - seg0start;
            var t1 = Cross(startvec, seg0vec) / cross;
            var t2 = Cross(startvec, seg1vec) / cross;

            return 0f <= t1 && t1 <= 1f && 0f <= t2 && t2 <= 1f;
        }

        /// <summary>
        /// XY平面での外積
        /// </summary>
        /// <param name="vec0"></param>
        /// <param name="vec1"></param>
        /// <returns></returns>
        private static float Cross(Vector3 vec0, Vector3 vec1)
        {
            vec0.z = 0f;
            vec1.z = 0f;
            return Vector3.Cross(vec0, vec1).z;
        }
    }
}
