using System;
using System.Collections;
using UnityEngine;
using DG.Tweening;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 背景UIの管理
    /// </summary>
    public sealed class BGManager : MonoBehaviour
    {
        #region 定数

        // Rectが同一として判定する誤差値
        private const float SIMILAR_RECT_THRESHOLD = float.Epsilon;

        #endregion

        #region SerializeField, Property

        /// <summary> UIManager下にあるインスタンス </summary>
        public static BGManager Instance => UIManager.BGManager;

        /// <summary> BG用のカメラ </summary>
        private static Camera _bgCamera => UIManager.BGCamera;

        /// <summary> 背景用Canvas </summary>
        private static Canvas _bgCanvas => UIManager.BgCanvas;
        
        /// <summary> BGカメラのデフォルトRectサイズ </summary>
        public static Rect BGCameraDefaultRect = new Rect(0f, 0f, 1f, 1f);

        /// <summary> BGPrefabRootのTransform</summary>
        public Transform BgPrefabRootTransform => _bgPrefabRoot.transform;

        //背景画像
        [SerializeField]
        private RawImageCommon _mainBg = null;
        public static RawImageCommon MainBg => Instance._mainBg;

        [SerializeField]
        private GameObject _bgPrefabRoot = null;

        /// <summary> 背景用Canvas </summary>
        [SerializeField]
        private CanvasScaler _bgCanvasScaler = null;

        /// <summary>
        /// 3Dキャラ+背景用
        /// </summary>
        public CharacterBg CharacterBg { get; private set; }

        #endregion

        #region private変数

        //現在の背景ID
        private SceneDefine.BgId _currentBgId = SceneDefine.BgId.Max;

        // BGカメラのRectが変更されたか
        // このフラグが立っている場合はシーン遷移時に1fのリサイズ待ちと、BGCanvasのスケール再更新を実行する
        private bool _needCanvasResizing = false;

        // 現在の背景画像の設定サイズ
        private int _currentBgWidth = 0;
        private int _currentBgHeight = 0;
        private bool _currentBgSetScale = true;

        // カメラのデフォルト設定
        private float _defaultBgCameraDepth = 10;
        private CameraClearFlags _defaultBgCameraClearFlags = CameraClearFlags.Color;

        private GenericBg _genericBg;

        #endregion

        #region リソースダウンロード

        /// <summary>
        /// 背景リソース読み込み
        /// </summary>
        /// <param name="register"></param>
        public static void RegisterDownloadBGResource(DownloadPathRegister register, SceneDefine.BgId bgId)
        {
            var bgData = SceneDefine.GetBgData(bgId);
            //プリインの背景でなければダウンロード
            if (!string.IsNullOrEmpty(bgData.TexturePath))
            {
                register.RegisterPathWithoutInfo(bgData.TexturePath);
            }
        }

        #endregion

        #region 初期化

        public void Initialize()
        {
            // bgCameraのカラーをWhiteにする
            _bgCamera.backgroundColor = Color.white;

            //デフォルトのBGカメラの設定をキャッシュしておく
            _defaultBgCameraClearFlags = _bgCamera.clearFlags;
            _defaultBgCameraDepth = _bgCamera.depth;
        }

        #endregion

        #region 背景処理

        /// <summary>
        /// 背景の操作
        /// </summary>
        /// <param name="bgId"></param>
        public void SetBg(SceneDefine.BgId bgId, bool isForced = false)
        {
            if (!isForced)
            {
                if (bgId == _currentBgId)
                {
                    return;
                }
            }

            if (bgId != SceneDefine.BgId.None)
            {
                // ---- 表示物の設定
                var bgData = SceneDefine.GetBgData(bgId);
                if (!string.IsNullOrEmpty(bgData.TexturePath))
                {
                    // テクスチャ名やテクスチャの有無で判断。シーン内で直接テクスチャを書き換えている場所があるので_currentBgIdだけでは判断できない
                    if (_mainBg.texture == null || bgData.TexturePath != _mainBg.texture.name)
                    {
                        if (bgData.IsVertical)
                        {
                            SetMainVerticalBg(bgData.TexturePath);
                        }
                        else if (bgData.PosData != null)
                        {
                            SetMainBg(bgData.TexturePath, bgData.PosData);
                        }
                        else
                        {
                            SetMainBg(bgData.TexturePath);
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(bgData.PrefabPath))
                {
                    SetPrefabBg(bgData.PrefabPath, bgId);
                }
                else
                {
                    Debug.LogError("BGDataが指定されているが、表示物が何も指定されていない。 ID = " + bgId.ToString());
                    // 全表示状態のクリア
                    SetShowStateExclusively(null);
                }
            }
            else
            {
                SetShowStateExclusively(null);
            }

            //現在の背景ID更新
            _currentBgId = bgId;
        }

        /// <summary>
        /// 背景Canvasのオンオフ
        /// </summary>
        /// <param name="enable"></param>
        public static void SetBgCanvasEnable(bool enable)
        {
            _bgCanvas.gameObject.SetActive(enable);
        }

        /// <summary>
        /// 背景Canvasのオンオフ
        /// </summary>
        /// <returns></returns>
        public static bool GetBgCanvasEnable()
        {
            if (_bgCanvas == null || _bgCanvas.gameObject == null) return false;

            return _bgCanvas.gameObject.activeSelf;
        }

        /// <summary>
        /// Bgカメラのオンオフ
        /// </summary>
        /// <param name="enable"></param>
        public static void SetBgCameraEnable(bool enable)
        {
            if (_bgCamera == null) return;
            
            _bgCamera.enabled = enable;
        }

        /// <summary>
        /// BgカメラのEnable取得
        /// </summary>
        /// <returns></returns>
        public static bool GetBgCameraEnable()
        {
            if (_bgCamera == null) return false;
            
            return _bgCamera.enabled;
        }

        /// <summary>
        /// メイン背景画像差し替え
        /// </summary>
        /// <param name="path">画像名</param>
        /// <param name="width">画像の横幅</param>
        /// <param name="height">画像の縦幅</param>
        public void SetMainBg(string path, int width = GameDefine.BG_TEXTURE_WIDTH, int height = GameDefine.BG_TEXTURE_HEIGHT, float posX = 0f, float posY = 0f, bool isSetScale = true)
        {
            ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.MainBG);

            if (string.IsNullOrEmpty(path))
            {
                SetMainBgTexture(null, 0, 0, isSetScale);
                SetShowStateExclusively(null); // 何も表示しない
                return;
            }

            Texture2D texture = ResourceManager.LoadOnHash<Texture2D>(path, ResourceManager.ResourceHash.MainBG);
            SetMainBgTexture(texture, width, height, isSetScale);
            SetMainBgLocalPos(new Vector2(posX, posY));
            SetShowStateExclusively(_mainBg.gameObject); // MainBGを表示状態に
        }

        /// <summary>
        /// メイン背景画像差し替え。EtoE対応独自、ポジション移動させる対応をする
        /// </summary>
        /// <param name="path">画像名</param>
        /// <param name="posData">配置情報</param>
        public void SetMainBg(string path, SceneDefine.BgData.PositionData posData)
        {
            SetMainBg(path, (int) posData.Size.x, (int) posData.Size.y, posData.Pos.x, posData.Pos.y, false);
        }

        /// <summary>
        /// 縦画面用メイン背景画像差し替え。通常の背景と画像サイズの縦横が入れ替わっている
        /// </summary>
        /// <param name="path">画像名</param>
        public void SetMainVerticalBg(string path)
        {
            SetMainBg(path, GameDefine.BG_TEXTURE_HEIGHT, GameDefine.BG_TEXTURE_WIDTH);
        }

        /// <summary>
        /// 縦画面用メイン背景画像差し替え。通常の背景と画像サイズの縦横が入れ替わっている
        /// </summary>
        /// <param name="path">画像名</param>
        public void SetMainVerticalBg(Texture texture)
        {
            SetMainBgTexture(texture, GameDefine.BG_TEXTURE_HEIGHT, GameDefine.BG_TEXTURE_WIDTH);
            SetShowStateExclusively(_mainBg.gameObject); // MainBGを表示状態に
        }

        /// <summary>
        /// メイン背景の右側がキャンバス内に切れてしまった場合、キャンバスの右側にぴったり合うようにオフセットをかける
        /// </summary>
        public void SetMainBgRightEdgeToCanvasRightEdge()
        {
            var bgRectTransform = (_mainBg.transform as RectTransform);
            var bgCorners = new Vector3[4];
            bgRectTransform.GetWorldCorners(bgCorners);

            var bgCanvasRectTransform = (_bgCanvas.transform as RectTransform);
            var canvasCorners = new Vector3[4];
            bgCanvasRectTransform.GetWorldCorners(canvasCorners);

            //もし背景画像の右上の点のX座標がキャンバスの右上の点の座標より小さい場合、オフセットをかける
            var rightEdgeOffset = bgCorners[2].x - canvasCorners[2].x;
            
            if(rightEdgeOffset < 0f)
            {
                var bgPos = bgRectTransform.position;

                bgPos.x += Mathf.Abs(rightEdgeOffset);

                var localPos = _bgCanvas.transform.InverseTransformPoint(bgPos);

                SetMainBgLocalPos(new Vector2(localPos.x, localPos.y));
            }
        }

        /// <summary>
        /// 現在のBGCanvasのサイズに合わせて背景のサイズを正しく設定しなおす。主にLowresolutionCameraの設定でRenderTextureが設定されてCanvasのサイズが変わった時に使用。
        /// </summary>
        public void RecalcBgSize()
        {
            SetBgSize(_mainBg.texture, _currentBgWidth, _currentBgHeight, _currentBgSetScale);
        }
        
        /// <summary>
        /// 現在のBGCanvasのサイズに合わせて背景のサイズを正しく設定しなおす。主にLowresolutionCameraの設定でRenderTextureが設定されてCanvasのサイズが変わった時に使用。
        /// </summary>
        public void RecalcBgSize(int renderTextureWidth , int renderTextureHeight)
        {
            float scale = CalcBgScale(_currentBgWidth, _currentBgHeight, renderTextureWidth, renderTextureHeight);
            _mainBg.transform.localScale = new Vector3(scale, scale , 1f);
        }

        /// <summary>
        /// 現在のBGCanvasのサイズに合わせて背景のサイズを設定する
        /// </summary>
        private void SetBgSize(Texture texture, int width, int height, bool isSetScale = true)
        {
            if (texture != null)
            {
                var keepActive = _bgCanvas.gameObject.activeSelf;
                _bgCanvas.gameObject.SetActive(true);

                if (isSetScale)
                {
                    float scale = CalcBgScale(width, height);
                    _mainBg.transform.localScale = new Vector3(scale, scale , 1f);
                }
                else
                {
                    _mainBg.transform.localScale = Math.VECTOR2_ONE;
                }

                _mainBg.rectTransform.sizeDelta = new Vector2(width, height);

                _bgCanvas.gameObject.SetActive(keepActive);
            }
        }

        /// <summary>
        /// メイン背景画像差し替え
        /// </summary>
        /// <param name="texture">画像</param>
        /// <param name="width">画像の横幅</param>
        /// <param name="height">画像の縦幅</param>
        public void SetMainBgTexture(Texture texture, int width, int height, bool isSetScale = true)
        {
            // 背景のサイズを調整する //
            SetBgSize(texture, width, height, isSetScale);

            _mainBg.texture = texture;
            _currentBgWidth = width;
            _currentBgHeight = height;
            _currentBgSetScale = isSetScale;
        }
        
        /// <summary>
        /// 素材サイズから、背景スケールを計算する
        /// </summary>
        private float CalcBgScale(int width, int height)
        {
            var targetTexture = _bgCamera.targetTexture;
            if (targetTexture == null)
            {
                //現在のターゲットがnullになるようなことがあったら従来通り、BgCanvasのサイズを使う。CanvasScalerの値が適用後じゃないと正しい値にならないので注意
                var bgCanvasRectTransform = _bgCanvas.transform as RectTransform;
                float screenAspect = GallopUtil.GetScreenAspect();
                float targetAspect = (float) width / (float) height;
                if (screenAspect < targetAspect)
                {
                    // 素材のほうが縦長。縦フィット
                    return bgCanvasRectTransform.rect.height / (float) height;
                }
                else
                {
                    // スクリーンのほうが縦長。横フィット
                    return bgCanvasRectTransform.rect.width / (float) width;
                }
            }
            
            //テクスチャがある場合はテクスチャを使って手動計算
            return CalcBgScale(width, height, targetTexture.width, targetTexture.height);
        }
        
        /// <summary>
        /// 素材サイズから、背景スケールを計算する
        /// </summary>
        public float CalcBgScale(int width, int height , int renderTextureWidth , int renderTextureHeight)
        {
            float screenAspect = GallopUtil.GetScreenAspect();
            float targetAspect = (float) width / (float) height;
            // 呼び出しタイミングではまだCanvas.scaleFactor(≒CanvasScaler)が正しく反映されていないので自前で計算する
            // float baseScale = 1 / _bgCanvas.scaleFactor;
            float baseScale = GetBgCanvasScalerBaseScale(renderTextureWidth, renderTextureHeight);
            
            if (screenAspect < targetAspect)
            {
                // 素材のほうが縦長。縦フィット
                return baseScale * renderTextureHeight / (float) height;
            }
            else
            {
                // 素材のほうが縦長。横フィット
                return baseScale * renderTextureWidth / (float) width;
            }
        }
        
        public float GetBgCanvasScalerBaseScale()
        {
            return GetBgCanvasScalerBaseScale(Screen.Width, Screen.Height);
        }
        
        public float GetBgCanvasScalerBaseScale(int renderTargetWidth, int renderTargetHeight)
        {
            float screenAspect = (float)renderTargetWidth / (float)renderTargetHeight;
            var referenceResolution = _bgCanvasScaler.referenceResolution;
            float baseAspect = referenceResolution.x / referenceResolution.y;
            
            // CanvasScalerのRectTransformのサイズを手動計算で出す。
            // referenceResolution.x or y / 端末の横 or 縦の解像度 * Cameraに刺さっているRenderTextureのSizeで出る。bgCanvasRectTransform.rect.height,widthを出す
            if (screenAspect < baseAspect)
            {
                //9:19など9:16より長い場合
                return referenceResolution.x / (float) renderTargetWidth;
            }
            else
            {
                //3:4など横長端末の場合
                return referenceResolution.y / (float) renderTargetHeight;
            }
        }

        /// <summary>
        /// BGCameraのRect操作に連動した必要な処理を実行する
        /// おもにSceneManager.ChangeViewで呼び出すことを想定
        /// </summary>
        public IEnumerator UpdateBGScaleOnChangeView()
        {
            if (!_needCanvasResizing)
            {
                yield break;
            } // 更新の必要なし

            // メインBGが非表示の時（正確にはBGを何も表示していない時）はBGCanvasのスケール再更新が走らないので次の機会までスキップ
            if (!_mainBg.IsActive())
            {
                yield break;
            }

            // 1f更新待ち
            yield return null;

            // BGCanvasのサイズが更新されたはず、なのでMainBGのスケールも再更新をかける
            if (_currentBgSetScale)
            {
                float scale = CalcBgScale(_currentBgWidth, _currentBgHeight);
                _mainBg.transform.localScale = new Vector3(scale, scale, 1f);
            }
            else
            {
                _mainBg.transform.localScale = Math.VECTOR2_ONE;
            }

            _mainBg.rectTransform.sizeDelta = new Vector2(_currentBgWidth, _currentBgHeight);
        }


        /// <summary>
        /// メイン背景スプライトの有効無効
        /// </summary>
        /// <param name="enable"></param>
        public void SetMainBgEnable(bool enable)
        {
            _mainBg.gameObject.SetActive(enable);
        }

        /// <summary>
        /// メイン背景のカラー設定
        /// </summary>
        /// <param name="color"></param>
        public void SetMainBgColor(Color color)
        {
            _mainBg.color = color;
        }

        /// <summary>
        /// MainBgの位置指定
        /// </summary>
        /// <param name="pos"></param>
        public void SetMainBgLocalPos(Vector2 pos)
        {
            _mainBg.transform.localPosition = pos;
        }

        /// <summary>
        /// MainBgのスケール
        /// </summary>
        /// <param name="pos"></param>
        public void SetMainBgScale(Vector3 scale)
        {
            _mainBg.transform.localScale = scale;
        }

        /// <summary>
        /// デフォルトのBGカメラの設定に戻す
        /// </summary>
        public void SetBgCameraDefaultSetting()
        {
            _bgCamera.clearFlags = _defaultBgCameraClearFlags;
            _bgCamera.depth = _defaultBgCameraDepth;
        }

        #endregion

        #region PrefabBG

        /// <summary>
        /// PrefabBGを設定
        /// </summary>
        /// <param name="path">画像名</param>
        /// <param name="bgId">背景ID</param>
        private void SetPrefabBg(string path, SceneDefine.BgId bgId)
        {
            if (string.IsNullOrEmpty(path))
            {
                return;
            }

            // 以前の表示物を破棄
            ClearPrefabBG();

            GameObject prefabResource = ResourceManager.LoadOnHash<GameObject>(path, ResourceManager.ResourceHash.PrefabBG);
            if (prefabResource.GetComponent<BackGroundBase>() == null)
            {
                Debug.LogError("BG Prefab として指定された " + path + " は BackGroundBaseを持っていないためBGとして配置できません。");
                SetShowStateExclusively(null); // 何も表示できない
                return;
            }

            GameObject bgPrefab = Instantiate(prefabResource, _bgPrefabRoot.transform);
            if (bgId == SceneDefine.BgId.General)
            {
                _genericBg = bgPrefab.GetComponent<GenericBg>();
            }

            if (bgId == SceneDefine.BgId.Character)
            {
                CharacterBg = bgPrefab.GetComponent<CharacterBg>();
            }

            SetShowStateExclusively(_bgPrefabRoot); // bgPrefabを表示状態に
        }

        /// <summary>
        /// PrefabBG関連の諸々を破棄する
        /// </summary>
        private void ClearPrefabBG()
        {
            // 読み込んでたリソースの破棄
            ResourceManager.UnloadAllTargetHashResources(ResourceManager.ResourceHash.PrefabBG);
            // InstantiateしたPrefabの破壊
            var childObjects = _bgPrefabRoot.GetChildren(true);
            foreach (var child in childObjects)
            {
                child.SetActive(false);
                Destroy(child);
            }
            _genericBg = null;
            CharacterBg = null;
            _bgPrefabRoot.SetActive(false);
        }

        #endregion

        #region BGカメラ

        /// <summary>
        /// BGカメラのRect設定
        /// </summary>
        /// <param name="rect"></param>
        public void SetCameraRect(Rect rect)
        {
            if (_bgCamera == null)
            {
                Debug.LogError("BGCamera is Null!");
                return;
            }

            // カメラがリサイズされた場合はリサイズフラグを設定する
            // ただし設定された値が一緒（近似）しているなら更新されていないとして扱い、フラグを立てない
            Rect originalRect = _bgCamera.rect;
            if (Mathf.Abs(originalRect.width - rect.width) > SIMILAR_RECT_THRESHOLD ||
                Mathf.Abs(originalRect.height - rect.height) > SIMILAR_RECT_THRESHOLD ||
                Mathf.Abs(originalRect.x - rect.x) > SIMILAR_RECT_THRESHOLD ||
                Mathf.Abs(originalRect.y - rect.y) > SIMILAR_RECT_THRESHOLD)
            {
                _needCanvasResizing = true;
            }

            _bgCamera.rect = rect;
        }

        /// <summary>
        /// GraphicSettingsから解像度が変更された時のコールバック
        /// </summary>
        public void OnChangeResolutionByGraphicsSettings()
        {
            _needCanvasResizing = true;
        }

        #endregion

        /// <summary>
        /// 各BG表示方式の表示状態を、排他的に制御する
        /// </summary>
        private void SetShowStateExclusively(GameObject activeObject = null)
        {
            // MainBG
            _mainBg.gameObject.SetActive(activeObject == _mainBg.gameObject);

            // BGPrefab
            if (_bgPrefabRoot == activeObject)
            {
                _bgPrefabRoot.SetActive(true);
            }
            else
            {
                // bgPrefabが表示状態でなくなったなら、表示していたprefabは一度すべて破棄する
                // 不要なgameObjectが残ったり溜まったりすることでメモリや処理に影響を与えるのを防ぐため
                ClearPrefabBG();
            }

            // 表示するものの有無でBGCanvasの有効・無効も切り替える
            if (activeObject == null)
            {
                SetBgCanvasEnable(false);
            }
            else
            {
                SetBgCanvasEnable(true);
            }
        }
    }
}