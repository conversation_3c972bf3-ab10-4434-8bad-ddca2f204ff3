using System;
using UnityEngine;
using System.Collections;
using System.Runtime.InteropServices;

namespace Gallop
{
    /// <summary>
    /// URLScheme起動管理するクラス
    /// </summary>
    public class URLScheme
    {
        private static string _process = "";
        private static string _result = "";
        private static string _scheme = "";
        //private static int short_udid = 0;
        private static string _udid = "";
        private static string _errorCode = "";
        private static int _schemeRoomId = 0;
        public static int SchemeRoomId => _schemeRoomId;
        private static int _schemeTournamentId = 0;
        public static int SchemeTournamentId => _schemeTournamentId;

        private static int _schemePartnerID = 0;
        public static int SchemePartnerId => _schemePartnerID;

        private static int _schemeCircleId = 0;
        public static int SchemeCircleId => _schemeCircleId;

        private const string SCHEME_ROOM_ID = "rid";
        private const string SCHEME_TOURNAMENT_ID = "tid";
        private const string SCHEME_PARTNER_ID = "pid";
        private const string SCHEME_CIRCLE_ID = "cid";

        private const string COMBINE = "combine";
        private const string MIGRATION = "migration";
        private const string OK = "ok";
        private const string NG = "ng";

        public const string EC_MIGRATION_CANCEL = "3070";
        public const string EC_COMBINE_CANCEL = "3060";
        public const string EC_MIGRATION_ALREADY_RECORDED = "3061";
        public const string EC_COMBINE_NO_URL = "3054";
        public const string EC_MIGRATION_NO_URL = "3063";
        public const string EC_COMBINE_EXEC_HAVE_RECORD = "3057";

        public static void URLSchemeStartAndroid(bool isOnBoot)
        {
            GetURLSchemeParamsAndroid(isOnBoot);
            ProcessSetting();
        }
        public static void URLSchemeStartiOS(string message, bool isOnBoot)
        {
            GetURLSchemeParamsiOS(message, isOnBoot);
            ProcessSetting();
        }
        public static bool IsCombineOK()
        {
            //TODO:@inada_taketo:データ連携に必要になったら戻す
            return false;
            //return (_process == COMBINE && _result == OK && short_udid == Certification.ShortUdid);
        }
        public static bool IsCombineNG()
        {
            return (_process == COMBINE && _result == NG && _errorCode != "");
        }
        public static bool IsMigrationOK()
        {
            return (_process == MIGRATION && _result == OK && _udid == Certification.Udid);
        }
        public static bool IsMigrationNG()
        {
            return (_process == MIGRATION && _result == NG && _errorCode != "");
        }
        public static bool IsMigrationFinished()
        {
            return (_process == MIGRATION && _result == NG && _errorCode == EC_MIGRATION_ALREADY_RECORDED);
        }
        public static void Clear()
        {
            _scheme = "";
            _process = "";
            _result = "";
            //short_udid = 0;
            _udid = "";
            _errorCode = "";
        }

        /// <summary>
        /// ルームIDの初期化用関数
        /// </summary>
        public static void ClearSchemeRoomId()
        {
            _schemeRoomId = 0;
        }

        /// <summary>
        /// 大会IDの初期化用関数
        /// </summary>
        public static void ClearSchemeTournament()
        {
            _schemeTournamentId = 0;
        }

        /// <summary>
        /// パートナーID初期化
        /// </summary>
        public static void ClearSchemePartnerID()
        {
            _schemePartnerID = 0;
        }

        /// <summary>
        /// 勧誘サークルID初期化
        /// </summary>
        public static void ClearSchemeCircleId()
        {
            _schemeCircleId = 0;
        }

        //private---------------------------------------------------
        /// <summary>
        /// Androidの仕組みです。
        /// </summary>
        static void GetURLSchemeParamsAndroid(bool isOnBoot)
        {
            //ブラウザ以外にもアプリ内のタイトル戻るボタンを押すときにここへ来るから、クリアする必要がある
            Clear();

#if UNITY_EDITOR
#if CYG_DEBUG
            Debug.Log("Android URL Scheme Scheme===============" + PlayerPrefs.GetString("androidUrlSchemeScheme", ""));//umamusumeprettyderbyを取る
            Debug.Log("Android URL Scheme Host===============" + PlayerPrefs.GetString("androidUrlSchemeHost", ""));//
#endif
            _scheme = PlayerPrefs.GetString("androidUrlSchemeScheme", "");
            string host = PlayerPrefs.GetString("androidUrlSchemeHost", "");
            //使用後削除します。
            PlayerPrefs.DeleteKey("androidUrlSchemeScheme");
            PlayerPrefs.DeleteKey("androidUrlSchemeHost");

            // CygamesIDにトークン情報を投げる
            if (!host.IsEmpty())
            {
                // CygamesIDのURLSchemeの場合、処理したらtrueを返してくるので以降の処理は不要
                return;
            }

            //「.」で分解
            AnalysisResult(host);

#elif UNITY_ANDROID

            AndroidJavaObject AJO = null;
            if (AJO == null)
            {
                AJO = new AndroidJavaObject("jp.co.cygames.urlscheme.IntentReceiveActivity");
            }

            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject context = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");

            _scheme      = AJO.Call<string>("getPreferenceString", new object[] { context, "androidUrlSchemeScheme" });
            string host = AJO.Call<string>("getPreferenceString", new object[] { context, "androidUrlSchemeHost" });

            

#if CYG_DEBUG
            Debug.Log("Android URL Scheme Scheme===============" + _scheme);//umamusumeprettyderbyを取る
            Debug.Log("Android URL Scheme Host===============" + host);//
#endif

            //使用後削除します。
            AJO.Call("deletePreferenceString", new object[] { context, "androidUrlSchemeScheme" });
            AJO.Call("deletePreferenceString", new object[] { context, "androidUrlSchemeHost" });

            // CygamesIDにトークン情報を投げる
            if (!host.IsEmpty() && CygamesID.CIDP.SNSLoginManager.GetInstance().OnURLSchemeActivated(host))
            {
                // CygamesIDのURLSchemeの場合、処理したらtrueを返してくるので以降の処理は不要
                return;
            }

            //「.」で分解
            AnalysisResult(host);

            // URL内の各IDを探して保存（全環境共通）
            SearchIdAndSave(host, isOnBoot);
#endif
            //_schemeRoomId = "0";
            //Cute.BootSystem.SetSkipBoot(true);

        }


        /// <summary>
        /// iOSの仕組み
        /// </summary>
        /// <param name="url"></param>
        /// <param name="isOnBoot"></param>
        static void GetURLSchemeParamsiOS(string url, bool isOnBoot)
        {
            Clear();
#if CYG_DEBUG
            Debug.Log("iOSURLSchemeCallBack=========" + url);
#endif      
            if (url == "")
            {
                url = PlayerPrefs.GetString("iOSUrlScheme", "");
                PlayerPrefs.DeleteKey("iOSUrlScheme");
#if CYG_DEBUG
                Debug.Log("iOSURLSchemeFromPlayerPrefs=========" + url);
#endif      
            }
            if (url.IndexOf("://") < 0)
                return;

            //パラメタ分解
            _scheme = url.Substring(0, url.IndexOf("://"));
            //「.」で分解
            AnalysisResult(url.Substring(url.IndexOf("://") + 3));

            // URL内の各IDを探して保存（全環境共通）
            SearchIdAndSave(url, isOnBoot);
        }

        /// <summary>
        /// URL内の各IDを探して保存（全環境共通）
        /// </summary>
        /// <param name="url"></param>
        /// <param name="isOnBoot"></param>
        public static void SearchIdAndSave(string url, bool isOnBoot)
        {
            // ルームマッチのルームID
            if (isOnBoot)
            {
                ParseScheme(url, SCHEME_ROOM_ID, ref _schemeRoomId);
                ParseScheme(url, SCHEME_PARTNER_ID, ref _schemePartnerID);
            }
            
            // カスタム大会ID
            ParseScheme(url, SCHEME_TOURNAMENT_ID, ref _schemeTournamentId);
            
            // 勧誘サークルID
            ParseScheme(url, SCHEME_CIRCLE_ID, ref _schemeCircleId);
        }

        /// <summary>
        /// urlにkeywardがあればresultに代入する
        /// </summary>
        private static void ParseScheme(string url, string keyward, ref int result)
        {
            if (!url.Contains(keyward)) return;
            url = url.Trim('+');
            string keywardStr = url.Substring(url.IndexOf(keyward) + keyward.Length);
            Int32.TryParse(keywardStr, out result);
#if CYG_DEBUG
            Debug.Log($"iOS URL {keyward} =============== {result}");
#endif
        }

        /// <summary>
        /// URLスキームによって処理を行う。
        /// </summary>
        static void ProcessSetting()
        {
#if CYG_DEBUG
            Debug.Log("ProcessSetting()==urlscheme==" + _scheme);
#endif
            if (_scheme == "")
            {
                // 無効なScheme.
                return;
            }

            //if (IsCombineOK())
            //{
            //    //引継ぎ設定完了
            //    DataMigration.CombineSucceed();
            //}
            //else if (IsCombineNG())
            //{
            //    DataMigration.CombineFailed();
            //}
            //else if (IsMigrationOK())
            //{
            //    DataMigration.MigrationSucceed();
            //}
            //else if (IsMigrationNG())
            //{
            //    //必要処理
            //    DataMigration.MigrationFailed();
            //}

        }

        /// <summary>
        /// 連携結果文字列を.でパースして反映.
        /// </summary>
        /// <param name="host"></param>
        private static void AnalysisResult(string host)
        {
            //TODO:@inada_taketo:データ連携に必要になったら戻す
            //string[] stArrayData = host.Split('.');
            //int stArrayDataLenght = stArrayData.Length;

            ////データ連携
            //string code = "";
            //for (int i = 0; i < stArrayDataLenght; i++)
            //{
            //    if (i == 0)
            //    {
            //        _process = stArrayData[0];
            //    }
            //    if (i == 1)
            //    {
            //        _result = stArrayData[1];
            //    }
            //    if (i == 2)
            //    {
            //        code = stArrayData[2];
            //    }

            //}

            //if (!string.IsNullOrEmpty(code))
            //{
            //    if (_result == OK)
            //    {
            //        if (_process == COMBINE)
            //        {
            //            short_udid = System.Convert.ToInt32(code);
            //        }
            //        else if (_process == MIGRATION)
            //        {
            //            _udid = (code);
            //        }
            //    }
            //    else if (_result == NG)
            //    {
            //        _errorCode = code;
            //    }
            //}
        }
    }
}