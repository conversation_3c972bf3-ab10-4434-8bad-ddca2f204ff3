using UnityEngine;
using Gallop.RenderPipeline;

namespace Gallop
{
    /// <summary>
    /// ILowResolutionCameraを継承するクラスの基底
    /// 各クラスにて共通の処理をここにまとめる
    /// staticで記述できるメソッドはLowResolutionCameraUtilにまとめているが
    /// メンバ変数への操作やOnDestroy時の処理はこちらに記述する
    /// </summary>
    [RequireComponent(typeof(Camera))]
    public class LowResolutionCameraBase : MonoBehaviour
    {
        #region 変数
        /// <summary>
        /// Fovを補正する時に使う係数（使わない時は１）
        /// </summary>
        [SerializeField]
        private float _fovFactor = LowResolutionCameraUtil.DEFAULT_FOV_FACTOR;

        public float FovFactor
        {
            get => _fovFactor;
            set
            {
                _fovFactor = value;
                if (_cameraData != null)
                {
                    _cameraData.FieldOfViewFactor = value;
                }
            }
        }

        /// <summary>
        /// 3D描画するカメラ（親）
        /// </summary>
        protected Camera _camera = null;

        protected CameraData _cameraData;

        public CameraData CameraData => _cameraData;

        #endregion 変数

        #region MonoBehaviour

        protected virtual void OnEnable()
        {
            //URP:置き換え対応
            /*
            // カメラの補正関数を登録
            if (CameraManager.HasInstance())
            {
                CameraManager.OnAspectFovCallback += AdjustFov;
            }
            */
        }

        protected virtual void OnDisable()
        {
            //URP:置き換え対応
            /*
            // カメラの補正関数を解除
            if (CameraManager.HasInstance())
            {
                CameraManager.OnAspectFovCallback -= AdjustFov;
            }
            */
        }

        protected virtual void OnDestroy()
        {
            //URP:置き換え対応
            /*
            // カメラの補正関数を解除
            if (CameraManager.HasInstance())
            {
                CameraManager.OnAspectFovCallback -= AdjustFov;
            }
            */
            _cameraData = null;
        }
        #endregion MonoBehaviour

        #region メソッド

        /*
        /// <summary>
        /// カメラの画角を調整する (OnAspectFovCallbackに登録して使う)
        /// </summary>
        public void AdjustFov(Camera camera)
        {
            if (camera != _camera)
            {
                // 違うカメラなら何もしない
                return;
            }

            // 保存しておいた係数を乗じる
            camera.fieldOfView *= _fovFactor;
        }
        */
        #endregion メソッド
    }
}
