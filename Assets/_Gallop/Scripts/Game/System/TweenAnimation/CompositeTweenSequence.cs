using System;
using DG.Tweening;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 複数Tweenを指定の間隔で再生するSequence
    /// </summary>
    public class CompositeTweenSequence : MonoBehaviour
    {
        #region Const

        private const float INTERVAL = 0.03f;

        #endregion

        #region Nested Types

        /// <summary>
        /// 結合タイプ
        /// </summary>
        public enum CombinedType
        {
            Append,
            Join,
            Insert,
            Prepend,
        }

        /// <summary>
        /// プリセット
        /// </summary>
        [Serializable]
        public class Preset
        {
            public GameObject Target;
            public TweenAnimation.PresetType PresetType;
            public CombinedType CombinedType;
            public bool UseDelay;
            public float Delay;
        }

        /// <summary>
        /// プリセットグループ
        /// </summary>
        [Serializable]
        public class PresetGroup
        {
            public Preset[] Presets;
            public CombinedType CombinedType;
            public float Interval = INTERVAL;
            public float Delay;
        }

        #endregion Nested Types

        #region SerializeField

        /// <summary>
        /// 設定
        /// </summary>
        [SerializeField]
        private PresetGroup[] _presetGroupArray;

        #endregion SerializeField

        #region Method

        /// <summary>
        /// シーケンスを生成
        /// </summary>
        public Sequence Create(bool autoKill = false)
        {
            var sequence = DOTween.Sequence();
            foreach (var presetGroup in _presetGroupArray)
            {
                var seq = CreateSequenceGroup(presetGroup);
                CombineSequence(sequence, seq, presetGroup.CombinedType, presetGroup.Delay);
            }
            sequence.SetAutoKill(autoKill);

            return sequence.Pause();
        }

        /// <summary>
        /// シーケンスグループからシーケンスを生成
        /// </summary>
        /// <param name="presetGroup"></param>
        /// <returns></returns>
        private static Sequence CreateSequenceGroup(PresetGroup presetGroup)
        {
            var sequence = DOTween.Sequence();
            for (int i = 0; i < presetGroup.Presets.Length; i++)
            {
                var preset = presetGroup.Presets[i];
                var seq = TweenAnimationBuilder.CreateSequence(preset.Target, preset.PresetType);
                var delay = preset.UseDelay ? preset.Delay : presetGroup.Interval * i;
                CombineSequence(sequence, seq, preset.CombinedType, delay);
            }

            return sequence;
        }

        /// <summary>
        /// シーケンスを結合する
        /// </summary>
        /// <param name="baseSequence"></param>
        /// <param name="sequence"></param>
        /// <param name="type"></param>
        /// <param name="delay"></param>
        /// <returns></returns>
        private static void CombineSequence(Sequence baseSequence, Tween sequence, CombinedType type, float delay)
        {
            switch (type)
            {
                case CombinedType.Append:
                    baseSequence.Append(sequence.SetDelay(delay));
                    break;
                case CombinedType.Join:
                    baseSequence.Join(sequence.SetDelay(delay));
                    break;
                case CombinedType.Insert:
                    baseSequence.Insert(delay, sequence);
                    break;
                case CombinedType.Prepend:
                    baseSequence.Prepend(sequence.SetDelay(delay));
                    break;
                default:
                    baseSequence.Append(sequence.SetDelay(delay));
                    break;
            }
        }

        #endregion
    }
}
