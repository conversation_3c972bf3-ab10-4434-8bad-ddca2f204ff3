using System;

namespace Gallop
{
    public static class IntExtensions
    {
        /// <summary>
        /// int型の値が0ならfalse、0以外ならTrueを返す
        /// </summary>
        /// <param name="self"></param>
        /// <returns></returns>
        public static bool ToBoolean(this int self)
        {
            return Convert.ToBoolean(self);
        }

        /// <summary>
        /// int?型の値が0 or nullならfalse、0以外ならTrueを返す
        /// </summary>
        /// <param name="self"></param>
        /// <returns></returns>
        public static bool ToBoolean(this int? self)
        {
            return (self ?? 0).ToBoolean();
        }
    }
}