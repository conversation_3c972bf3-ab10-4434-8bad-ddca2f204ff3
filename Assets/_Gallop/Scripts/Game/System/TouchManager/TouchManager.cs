#define USE_FLICK
#define USE_PINCH

using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections;
using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// ルームタッチ管理クラス
    /// </summary>
    public class TouchManager : MonoSingleton<TouchManager>
    {
        #region 定数系

        /// <summary>
        /// レイヤー
        /// </summary>
        public enum Layer
        {
            None = 0x00000000,
            UI = 0x00008020,
            All = 0x0000ffff,
        };

        /// <summary>
        /// フラグタイプ
        /// </summary>
        private enum Flag
        {
#if USE_PINCH
            Pinch,          // ピンチ中
#endif
#if USE_FLICK
            FlickLeft,      // フリック左
            FlickRight,     // フリック右
            FlickUp,        // フリック上
            FlickDown,      // フリック下
#endif
            Drag,           // ドラッグ
            PrevDrag,       // 前回ドラッグされたか IsUp以外は必ずtrue
            PrevDragNoTime, // 前回ドラッグされたか IsUp以外は必ずtrue　時間は関係なし
            LongPress,      // ロングプレス
            NotLongPress,   // ロングプレスを使用できないかどうか
            LayerMask,      // レイヤーマスクフラグ ※次回タップまで有効
#if UNITY_EDITOR || UNITY_STANDALONE
            IsMultiTap,     // マルチタップ中かどうか Editor上ではInput.multiTouchEnabledが必ずtrueになってしまう
            ChangeMultiTap, // マルチタップ切り替え
#endif
            Max,
        }

        /// <summary>
        /// タッチ
        /// </summary>
        public enum TouchType
        {
            None,       //なにもしていない
            Press,      //押している時
            Down,       //押した時
            Up,         //離した時
            Stay,       //押されているが動いていない時
            Max,
        };

        /// <summary>
        /// フリック
        /// </summary>
        public enum FlickType
        {
            NONE,
            LEFT,
            RIGHT,
            UP,
            BOTTOM,
        };

        /// <summary>
        /// タッチデータ
        /// </summary>
        public struct TouchData
        {
            /// <summary>
            /// タイプ
            /// </summary>
            public TouchType _type;

            /// <summary>
            /// 位置
            /// </summary>
            public Vector3 _pos;

            /// <summary>
            /// 前回位置
            /// </summary>
            public Vector3 _oldPos;

            /// <summary>
            /// フィンガーID
            /// </summary>
            public int _fingerID;
        }

        /// <summary>
        /// タッチ
        /// </summary>
        public class TouchParam
        {
            /// <summary>
            /// タッチ
            /// </summary>
            public bool isTouch = false;

            /// <summary>
            /// コライダー
            /// </summary>
            public BoxCollider collider = null;

            /// <summary>
            /// コールバック
            /// </summary>
            private Action callback = delegate { };

            /// <summary>
            /// 3DTouchにて除外したいGameObject
            /// </summary>
            public GameObject[] IgnoreGameObjctArray = null;

            /// <summary> ロングタップを許可するかどうか </summary>
            public bool IsEnableLongTap { get; private set; } = false;

            public TouchParam(BoxCollider setCollider, Action setCallback, bool isEnableLongTap)
            {
                collider = setCollider;
                callback = setCallback;
                IsEnableLongTap = isEnableLongTap;
            }

            /// <summary>
            /// コールバック実行
            /// ** GameCanvasのロック中もコールバックが飛ばないため、ダイアログの中などで使うと問題になる場合がある
            /// </summary>
            public void ExecCallback()
            {
                if (UIManager.Instance.IsLockAllCanvas() || UIManager.Instance.IsLockGameCanvas())
                {
                    return;
                }

                callback?.Invoke();
            }
        }

#if USE_FLICK
        /// <summary>
        /// フリック
        /// </summary>
        public class FlickParam
        {
            /// <summary>
            /// タッチ
            /// </summary>
            public bool isTouch = false;

            /// <summary>
            /// コライダー
            /// </summary>
            public BoxCollider collider = null;

            /// <summary>
            /// コールバック
            /// </summary>
            public Action<FlickType> callback = delegate { };

            public FlickParam(BoxCollider setCollider, Action<FlickType> setCallback)
            {
                collider = setCollider;
                callback = setCallback;
            }
        }

        /// <summary>
        /// フリック時間
        /// </summary>
        //private const float FLICK_SEC = 0.2f;
        public const float FLICK_SEC = 0.5f;

        /// <summary>
        /// フリックの距離
        /// </summary>
        public const float FLICK_THRESHOLD_X = 20.0f;
        public const float FLICK_THRESHOLD_Y = 30.0f;

        //各フリック判別する角度の定義
        public const float FLICK_RIGHT_UP_ANGLE = 45f;
        public const float FLICK_RIGHT_DOWN_ANGLE = 135f;
        public const float FLICK_LEFT_DOWN_ANGLE = 225f;
        public const float FLICK_LEFT_UP_ANGLE = 315f;
#endif

        /// <summary>
        /// ドラッグ時間
        /// ※前回ドラッグしていたか
        /// </summary>
        private const float PREV_DRAG_SEC = 6.0f;

        /// <summary>
        /// ロングプレス時間
        /// </summary>
        private const float LONG_PRESS_SEC = 0.5f;

        /// <summary>
        /// 最少ドラッグ距離
        /// ※これ以下はドラッグされてないと認識する
        /// </summary>
        private const float MIN_DRAG_LENGTH = 10.0f;

        /// <summary>
        /// 移動ベクトル距離
        /// ※これ以下だったら除外する
        /// </summary>
        private const float BAN_MOVE_VEC = 30.0f;
        /// <summary>
        /// 移動ベクトル距離(GetTouchVec用)
        /// ※これ以下だったら除外する
        /// </summary>
        private const float BAN_TOUCH_VEC = 1.0f;

        /// <summary>
        /// タッチ最大数
        /// </summary>
        public const int TOUCH_MAX = 2;

        /// <summary>
        /// NONE時位置
        /// </summary>
        private readonly Vector3 NONE_POS = new Vector3(-10000.0f, -10000.0f, 0.0f);

        /// <summary>
        /// タッチテーブル
        /// </summary>
        private readonly TouchType[] TOUCH_TYPE_ARRAY =
        {
            TouchType.Down,
            TouchType.Press,
            TouchType.Stay,
            TouchType.Up,
            TouchType.None,
        };
        
        // IOS13.1用
        // 3本指ジェスチャーが有効な場合稀に入力値が吹き飛ぶことがあるため、一定以上の値が来た場合は無視する
        private const float IGNORE_SCREEN_POSITION_THRESHOLD = 100000.0f;
        
        #endregion

        #region 変数

        /// <summary>
        /// 初期化フラグ
        /// </summary>
        private bool _init = false;

        /// <summary>
        /// タッチマネージャが有効かどうか
        /// </summary>
        private bool _isEnable = true;
        public bool IsEnable { get { return _isEnable; } set { _isEnable = value; } }

        /// <summary>
        /// ダイアログ上でも有効にするかどうか
        /// </summary>
        private bool _isEnableOnDialog = false;
        public bool IsEnableOnDialog { get { return _isEnableOnDialog; } set { _isEnableOnDialog = value; } }

        /// <summary>
        /// フラグ
        /// </summary>
        private BitArray _flag = new BitArray((int)Flag.Max);

        /// <summary>
        /// カメラ
        /// </summary>
        private static Camera targetCamera
        {
            get { return UIManager.UICamera; }
        }

        /// <summary>
        /// タッチパラメータ
        /// </summary>
        private TouchData[] _touchDatas = new TouchData[TOUCH_MAX];

        /// <summary>
        /// 入力開始位置
        /// </summary>
        private Vector3 _touchFirstPos = Vector3.one;

        /// <summary>
        /// タッチ数
        /// </summary>
        private int _touchCount = 0;
        public int TouchCount { get { return _touchCount; } }

        /// <summary>
        /// 前回のタッチ数
        /// </summary>
        private int _oldTouchCount = 0;

        /// <summary>
        /// レイヤーマスク
        /// </summary>
        private int _layerMask = (int)Layer.None;
        public int layerMask
        {
            get { return _layerMask; }
        }

        /// <summary>
        /// ドラック時間
        /// </summary>
        private float _draggingSec = 0;
        public float draggingSec
        {
            get { return _draggingSec; }
        }

        /// <summary>
        /// フリックリスト
        /// </summary>
        private List<FlickParam> flickList = new List<FlickParam>();
        public FlickParam AddFlick(BoxCollider setCollider, Action<FlickType> setCallback)
        {
            FlickParam param = new FlickParam(setCollider, setCallback);
            flickList.Add(param);
            return param;
        }
        public void RemoveFlick(FlickParam param)
        {
            flickList.Remove(param);
        }

        /// <summary>
        /// タッチリスト
        /// </summary>
        private List<TouchParam> touchList = new List<TouchParam>();
        public TouchParam AddTouch(BoxCollider setCollider, Action setCallback, bool isEnableLongtap)
        {
            TouchParam param = new TouchParam(setCollider, setCallback, isEnableLongtap);
            touchList.Add(param);
            return param;
        }
        public void RemoveTouch(TouchParam param)
        {
            touchList.Remove(param);
        }

        /// <summary>
        /// タッチリスト
        /// </summary>
        private List<TouchParam> touch3DList = new List<TouchParam>();

        public List<TouchParam> Touch3DList => touch3DList;
        public TouchParam AddTouch3D(BoxCollider setCollider, Action setCallback, bool isEnableLongtap)
        {
            TouchParam param = new TouchParam(setCollider, setCallback, isEnableLongtap);
            touch3DList.Add(param);
            return param;
        }
        public void RemoveTouch3D(TouchParam param)
        {
            touch3DList.Remove(param);
        }

        #endregion

        #region MonoBehaviour標準メソッド

        // 初期化処理
        protected override void OnInitialize()
        {
            // 初期化してなかったら
            if (_init == false)
            {
                _init = true;

                for (int i = 0; i < TOUCH_MAX; ++i)
                {
                    _touchDatas[i] = new TouchData();
                }
            }

            Reset();
            
            // デフォルトはマルチタッチ無効
            SetMultiTouch(false);
        }

        /// <summary>
        /// 更新
        /// </summary>
        private void Update()
        {
            if (UIManager.HasInstance() == false)
                return;
            if (!_isEnable)
                return;
            if (DialogManager.IsShowDialog && !_isEnableOnDialog)
            {
                Reset();
                return;
            }

#if UNITY_EDITOR || UNITY_STANDALONE && !DMM

            // マルチタップ切り替え中
            if (_flag.Get((int)Flag.ChangeMultiTap))
            {
                _flag.Set((int)Flag.ChangeMultiTap, false);
                return;
            }
#endif
            // タッチ
            Touch();

#if UNITY_EDITOR || UNITY_STANDALONE
            // マウス
            Mouse();
#endif

#if USE_FLICK
            // フリック
            Flick();
#endif
            // ドラッグ
            Drag();

            // レイヤーマスクチェック
            CheckLayerMask();

            // 指が離されたならタッチ判定をリセットする
            if (IsUp())
            {
                ResetTouchListTouchState();
            }
        }

        #endregion

        #region Function

        /// <summary>
        /// 初期化
        /// </summary>
        private void Reset()
        {
            // フラグ
            _flag.SetAll(false);

            // 位置
            for (int i = 0; i < TOUCH_MAX; ++i)
            {
                _touchDatas[i]._type = TouchType.None;
                _touchDatas[i]._pos = NONE_POS;
                _touchDatas[i]._oldPos = NONE_POS;
                _touchDatas[i]._fingerID = -1;
            }
            _touchFirstPos = NONE_POS;
            _touchCount = 0;
            _oldTouchCount = 0;
            _draggingSec = 0;
        }
        
        /// <summary>
        /// パラメータ初期化
        /// </summary>
        private void InitParam()
        {
#if USE_PINCH
            _flag.Set((int)Flag.Pinch, false);
#endif
#if USE_FLICK
            _flag.Set((int)Flag.FlickLeft, false);
            _flag.Set((int)Flag.FlickRight, false);
            _flag.Set((int)Flag.FlickUp, false);
            _flag.Set((int)Flag.FlickDown, false);
#endif
            _flag.Set((int)Flag.Drag, false);
            _flag.Set((int)Flag.PrevDrag, false);
            _flag.Set((int)Flag.PrevDragNoTime, false);
            _flag.Set((int)Flag.LongPress, false);
            _flag.Set((int)Flag.NotLongPress, false);

            // 位置
            for (int i = 0; i < TOUCH_MAX; ++i)
            {
                _touchDatas[i]._type = TouchType.None;
                _touchDatas[i]._pos = NONE_POS;
                _touchDatas[i]._oldPos = NONE_POS;
                _touchDatas[i]._fingerID = -1;
            }
            for (int i = 0; i < flickList.Count; ++i)
            {
                flickList[i].isTouch = false;
            }

            ResetTouchListTouchState();

            _touchFirstPos = NONE_POS;
        }

        /// <summary> TouchListのタッチ状態をリセットする </summary>
        private void ResetTouchListTouchState()
        {
            foreach (var touchObj in touchList)
            {
                touchObj.isTouch = false;
            }

            foreach (var touch3DObj in touch3DList)
            {
                touch3DObj.isTouch = false;
            }
        }

        /// <summary>
        /// マウス
        /// </summary>
        private void Mouse()
        {
            if (_touchCount != 0)
            {
                return;
            }

            _touchCount = 0;
#if USE_PINCH
            _flag.Set((int)Flag.Pinch, false);
#endif

            //押された瞬間
            if (Input.GetMouseButtonDown(0))
            {
                _touchCount++;

                _touchDatas[0]._pos = GallopInput.mousePosition();
                _touchDatas[0]._type = TouchType.Down;
                _touchFirstPos = _touchDatas[0]._pos;

                //タッチ開始共通処理
                OnTouchBegan(true, _touchDatas[0]._pos);
            }
            //離した瞬間
            else
                if (Input.GetMouseButtonUp(0))
            {
                _touchCount++;
                _touchDatas[0]._pos = GallopInput.mousePosition();
                _touchDatas[0]._type = TouchType.Up;
            }
            //押している間
            else
                    if (Input.GetMouseButton(0))
            {
                _touchCount++;
                _touchDatas[0]._pos = GallopInput.mousePosition();
                _touchDatas[0]._type = TouchType.Press;
            }

            // ホイール
            if (IsMultiTap() && Input.GetAxis("Mouse ScrollWheel") != 0)
            {
                _touchDatas[0]._type = TouchType.Down;
                _touchDatas[1]._type = TouchType.Down;

                _touchDatas[0]._pos = GallopInput.mousePosition();
                _touchDatas[1]._pos = GallopInput.mousePosition();

#if USE_PINCH
                _flag.Set((int)Flag.Pinch, true);
#endif
                _flag.Set((int)Flag.NotLongPress, true);
            }
        }

        /// <summary>
        /// タッチ
        /// </summary>
        public void Touch()
        {
            // 入力タイプ
            for (int i = 0; i < TOUCH_MAX; ++i)
            {
                _touchDatas[i]._type = TouchType.None;
            }

            // 前回の情報を保存
            _oldTouchCount = _touchCount;
            for (int i = 0; i < _oldTouchCount; ++i)
            {
                _touchDatas[i]._oldPos = _touchDatas[i]._pos;
            }

            _touchCount = 0;
#if USE_PINCH
            _flag.Set((int)Flag.Pinch, false);
#endif

            int usefingerID = -1;

            // タッチ情報取得
            for (int i = 0; i < TOUCH_MAX; ++i)
            {
                // まだ登録していない場合
                if (_touchDatas[i]._fingerID == -1)
                {
                    for (int j = 0; j < Input.touchCount; ++j)
                    {
                        Touch nowTouch = Input.GetTouch(j);
#if DMM
                        nowTouch.position = GallopInput.mousePosition();
#endif
                        // すでに使用されていた場合は回避
                        if (usefingerID == nowTouch.fingerId)
                        {
                            continue;
                        }

                        // 新規で押下の場合登録
                        if (nowTouch.phase == TouchPhase.Began)
                        {
                            _touchDatas[_touchCount]._type = TouchType.Down;
                            _touchDatas[_touchCount]._pos = nowTouch.position;
                            _touchDatas[_touchCount]._fingerID = nowTouch.fingerId;
                            usefingerID = nowTouch.fingerId;
                            // 初期タッチ位置を登録
                            if (_touchCount == 0)
                            {
                                _touchFirstPos = _touchDatas[_touchCount]._pos;
                            }
                            _touchCount++;
                            //タッチ開始共通処理
                            OnTouchBegan(false, nowTouch.position);
                            break;
                        }
                    }
                }
                // すでに登録していた場合
                else
                {
                    for (int j = 0; j < Input.touchCount; ++j)
                    {
                        Touch nowTouch = Input.GetTouch(j);
#if DMM
                        nowTouch.position = GallopInput.mousePosition();
#endif
                        // すでに使用されていた場合は回避
                        if (usefingerID == nowTouch.fingerId)
                        {
                            continue;
                        }

                        if (_touchDatas[_touchCount]._fingerID == nowTouch.fingerId)
                        {
                            _touchDatas[_touchCount]._type = TOUCH_TYPE_ARRAY[(int)nowTouch.phase];
                            _touchDatas[_touchCount]._pos = nowTouch.position;
                            usefingerID = nowTouch.fingerId;
                            _touchCount++;
                            break;
                        }
                    }
                }
            }

            // タッチが上限に達していたら、ピンチ状態。ロングタップは拒否
            if (_touchCount == TOUCH_MAX)
            {
#if USE_PINCH
                _flag.Set((int)Flag.Pinch, true);
#endif
                _flag.Set((int)Flag.NotLongPress, true);
            }
            else
            {   // ルーム時キャラつまみを離した際、NONE_POSで初期化された値が返ってくるのでコメントアウト
                // NONE_POSを使って判定している個所も特になく、フラグで制御しているので問題ない / 問題があったらまた別途対応
                // 位置を初期化
                //for (int i = _touchCount; i < TOUCH_MAX; ++i)
                //{
                //    _touchDatas[i]._pos = NONE_POS;
                //}
            }

            // UPになっていたらfingerIDを削除
            for (int i = 0; i < TOUCH_MAX; ++i)
            {
                if (_touchDatas[i]._type == TouchType.Up)
                {
                    _touchDatas[i]._fingerID = -1;
                }
            }

            // 詰める
            if (_touchDatas[0]._fingerID == -1 &&
                _touchDatas[1]._fingerID != -1)
            {
                _touchDatas[0] = _touchDatas[1];
                _touchDatas[1]._fingerID = -1;
            }
        }

        /// <summary>
        /// タッチ開始共通処理
        /// </summary>
        private void OnTouchBegan(bool isMouse, Vector3 touchPosition)
        {
            Ray tapRay = targetCamera.ScreenPointToRay(touchPosition);
            RaycastHit tapRaycastHit;
            if (Physics.Raycast(tapRay, out tapRaycastHit))
            {
                for (int k = 0; k < flickList.Count; ++k)
                {
                    if (flickList[k].isTouch == true)
                    {
                        continue;
                    }
                    if (tapRaycastHit.collider == (Collider)flickList[k].collider)
                    {
                        flickList[k].isTouch = true;
                    }
                    else
                    {
                        flickList[k].isTouch = false;
                    }
                }
                for (int k = 0; k < touchList.Count; ++k)
                {
                    if (touchList[k].isTouch == true && !isMouse)   //マルチタッチ考慮
                    {
                        continue;
                    }
                    if (tapRaycastHit.collider == (Collider)touchList[k].collider)
                    {
                        touchList[k].isTouch = true;
                    }
                    else
                    {
                        touchList[k].isTouch = false;
                    }
                }
            }

            var cameraList = CameraManager.GetCameraControllerList();
            var viewPort = new Vector3(touchPosition.x / Screen.Width, touchPosition.y / Screen.Height,0.0f);
            foreach (var camera in cameraList)
            {
                if (camera == null || camera.GetCamera() == null) continue;

                tapRay = camera.GetCamera().ViewportPointToRay(viewPort);
                if (Physics.Raycast(tapRay, out tapRaycastHit))
                {
                    for (int k = 0; k < touch3DList.Count; ++k)
                    {
                        if (touch3DList[k].isTouch == true && !isMouse)
                        {
                            continue;
                        }
                        if (tapRaycastHit.collider == (Collider)touch3DList[k].collider)
                        {
                            touch3DList[k].isTouch = true;
                        }
                        else
                        {
                            touch3DList[k].isTouch = false;
                        }
                    }
                }
            }
        }

#if USE_FLICK
        /// <summary>
        /// フリック
        /// </summary>
        private void Flick()
        {
            _flag.Set((int)Flag.FlickLeft, false);
            _flag.Set((int)Flag.FlickRight, false);
            _flag.Set((int)Flag.FlickUp, false);
            _flag.Set((int)Flag.FlickDown, false);

            float vecX = GetVec().x;
            float vecY = GetVec().y;
            float absX = Mathf.Abs(vecX);
            float absY = Mathf.Abs(vecY);

            if (IsUp())
            {
                // 時間と距離をもとにフリックの判定
                if (_draggingSec < FLICK_SEC)
                {
                    // フリック判定 - 横
                    if (absX > absY &&  FLICK_THRESHOLD_X < absX)
                    {
                        for (int i = 0, count = flickList.Count; i < count; ++i)
                        {
                            if (flickList[i].isTouch == false)
                            {
                                continue;
                            }
                            flickList[i].isTouch = false;
                            if (vecX > 0)
                            {
                                flickList[i].callback(FlickType.RIGHT);
                            }
                            else
                            {
                                flickList[i].callback(FlickType.LEFT);
                            }
                        }
                    }
                    //フリック判定 - 縦
                    else if(absX < absY && FLICK_THRESHOLD_Y < absY)
                    {
                        for (int i = 0, count = flickList.Count; i < count; ++i)
                        {
                            if (flickList[i].isTouch == false)
                            {
                                continue;
                            }
                            flickList[i].isTouch = false;
                            if (vecY > 0)
                            {
                                flickList[i].callback(FlickType.UP);
                            }
                            else
                            {
                                flickList[i].callback(FlickType.BOTTOM);
                            }
                        }
                    }
                    else if (absX <= FLICK_THRESHOLD_X && absY <= FLICK_THRESHOLD_Y)
                    {
                        // 2D
                        for (int i = 0, count = touchList.Count; i < count; ++i)
                        {
                            if (touchList[i].isTouch == false)
                            {
                                continue;
                            }
                            touchList[i].isTouch = false;
                            touchList[i].ExecCallback();
                        }

                        var isTouchUI = Instance.IsTouchFrontUI(out var touchObject);

                        // 3D
                        for (int i = 0, count = touch3DList.Count; i < count; ++i)
                        {
                            if (touch3DList[i].isTouch == false)
                            {
                                continue;
                            }
                            touch3DList[i].isTouch = false;

                            bool isTouchCallback = !isTouchUI;
                            if (isTouchUI && touch3DList[i].IgnoreGameObjctArray != null)
                            {
                                //除外オブジェクトだった場合は通過させる
                                if(Array.Exists(touch3DList[i].IgnoreGameObjctArray, (ignoreObj) => ignoreObj == touchObject))
                                {
                                    isTouchCallback = true;
                                }
                            }

                            //3DオブジェクトのタッチではUIを貫通させない
                            if (isTouchCallback)
                            {
                                touch3DList[i].ExecCallback();
                            }
                        }
                    }
                }

                // 移動量の多い方向で、時間と距離をもとにフリックの判定
                if (absX >= absY)
                {
                    // X軸方向のフリック判定
                    if (_draggingSec < FLICK_SEC && FLICK_THRESHOLD_X < absX)
                    {
                        if (vecX > 0)
                        {
                            _flag.Set((int)Flag.FlickRight, true);
                        }
                        else
                        {
                            _flag.Set((int)Flag.FlickLeft, true);
                        }
                    }
                }
                else
                {
                    // Y軸方向のフリック判定
                    if (_draggingSec < FLICK_SEC && FLICK_THRESHOLD_Y < absY)
                    {
                        if (vecY > 0)
                        {
                            _flag.Set((int)Flag.FlickUp, true);
                        }
                        else
                        {
                            _flag.Set((int)Flag.FlickDown, true);
                        }
                    }
                }
            }
        }
#endif

        /// <summary>
        /// ドラッグ
        /// </summary>
        private void Drag()
        {
            switch (_touchDatas[0]._type)
            {
                case TouchType.None:
                    _draggingSec = 0;
                    _flag.Set((int)Flag.Drag, false);
                    _flag.Set((int)Flag.PrevDrag, false);
                    _flag.Set((int)Flag.PrevDragNoTime, false);
                    _flag.Set((int)Flag.LongPress, false);
                    _flag.Set((int)Flag.NotLongPress, false);

                    break;
                case TouchType.Down:
                case TouchType.Press:
                case TouchType.Stay:

                    _draggingSec += Time.deltaTime;
                    _flag.Set((int)Flag.Drag, true);
                    if (GetFirstTouchLength() >= MIN_DRAG_LENGTH)
                    {
                        _flag.Set((int)Flag.NotLongPress, true);
                    }
                    if (!_flag.Get((int)Flag.NotLongPress) &&
                        GetFirstTouchLength() < MIN_DRAG_LENGTH &&
                        _draggingSec > LONG_PRESS_SEC)
                    {
                        _flag.Set((int)Flag.LongPress, true);
                    }
                    break;
                case TouchType.Up:
                    _draggingSec += Time.deltaTime;
                    _flag.Set((int)Flag.Drag, false);
                    if (GetFirstTouchLength() >= MIN_DRAG_LENGTH)
                    {
                        _flag.Set((int)Flag.NotLongPress, true);
                    }
                    if (!_flag.Get((int)Flag.NotLongPress) &&
                        GetFirstTouchLength() < MIN_DRAG_LENGTH &&
                        _draggingSec > LONG_PRESS_SEC)
                    {
                        // 2D
                        foreach (var touchObj in touchList)
                        {
                            if (!touchObj.IsEnableLongTap || !touchObj.isTouch)
                            {
                                continue;
                            }

                            touchObj.isTouch = false;
                            touchObj.ExecCallback();
                        }
                        // 3D
                        var isTouchUI = Instance.IsTouchFrontUI(out var touchObject);
                        foreach (var touch3DObj in touch3DList)
                        {
                            if (!touch3DObj.IsEnableLongTap || !touch3DObj.isTouch)
                            {
                                continue;
                            }

                            touch3DObj.isTouch = false;

                            bool isTouchCallback = !isTouchUI;
                            if (isTouchUI && touch3DObj.IgnoreGameObjctArray != null)
                            {
                                //除外オブジェクトだった場合は通過させる
                                if (Array.Exists(touch3DObj.IgnoreGameObjctArray, (ignoreObj) => ignoreObj == touchObject))
                                {
                                    isTouchCallback = true;
                                }
                            }

                            //3DオブジェクトのタッチではUIを貫通させない
                            if (isTouchCallback)
                            {
                                touch3DObj.ExecCallback();
                            }
                        }

                        _flag.Set((int)Flag.LongPress, true);
                    }
                    // 前回ドラッグ
                    if (GetFirstTouchLength() > MIN_DRAG_LENGTH)
                    {
                        _flag.Set((int)Flag.PrevDragNoTime, true);
                        if ((_draggingSec > PREV_DRAG_SEC * Time.deltaTime))
                        {
                            _flag.Set((int)Flag.PrevDrag, true);
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// レイヤーマスクチェック
        /// </summary>
        private void CheckLayerMask()
        {
            //なしの場合は処理しない
            if (_layerMask == (int)Layer.None)
            {
                return;
            }

#if UNITY_EDITOR || UNITY_STANDALONE
            //マウス
            if (Input.GetMouseButtonDown(0))
            {
                Ray s_Ray = targetCamera.ScreenPointToRay(GallopInput.mousePosition());
                if (Physics.Raycast(s_Ray, Mathf.Infinity, _layerMask))
                {
                    _flag.Set((int)Flag.LayerMask, true);
                }
            }
#endif
            // タッチ
            for (int i = 0; i < Input.touchCount; ++i)
            {
                Touch touch = Input.GetTouch(i);

                if (touch.phase != TouchPhase.Began)
                {
                    continue;
                }

                Ray s_Ray = targetCamera.ScreenPointToRay(touch.position);
                if (Physics.Raycast(s_Ray, Mathf.Infinity, _layerMask))
                {
                    _flag.Set((int)Flag.LayerMask, true);
                    break;
                }
            }

            // レイヤーマスクフラグ
            if (_flag.Get((int)Flag.LayerMask))
            {
                if (_touchCount == 0)
                {
                    _flag.Set((int)Flag.LayerMask, false);
                }

                InitParam();
            }
        }

        /// <summary>
        /// Active設定
        /// </summary>
        /// <param name="active">アクティブ</param>
        public void SetActive(bool active)
        {
            Reset();

            gameObject.SetActive(active);
        }

        /// <summary>
        /// レイヤーマスクの初期化
        /// </summary>
        public void InitLayerMask()
        {
            _layerMask = (int)Layer.None;
            _flag.Set((int)Flag.LayerMask, false);
        }

        /// <summary>
        /// レイヤーマスクの登録
        /// </summary>
        public void SetLayerMask(int layerMask)
        {
            _layerMask = layerMask;
            if (layerMask == (int)Layer.None)
            {
                _flag.Set((int)Flag.LayerMask, false);
            }
        }

        /// <summary>
        /// マルチタップON・OFF
        /// </summary>
        /// <param name="enable"></param>
        public void SetMultiTouch(bool enable)
        {
            if (enable)
            {
                SetMultiTouchOn();
            }
            else
            {
                SetMultiTouchOff();
            }
        }

        /// <summary>
        /// マルチタップON
        /// </summary>
        private void SetMultiTouchOn()
        {
            Reset();
            Input.multiTouchEnabled = true;
#if UNITY_EDITOR || UNITY_STANDALONE
            _flag.Set((int)Flag.IsMultiTap, true);
            _flag.Set((int)Flag.ChangeMultiTap, true);
#endif
        }

        /// <summary>
        /// マルチタップOFF
        /// </summary>
        private void SetMultiTouchOff()
        {
            Reset();
            Input.multiTouchEnabled = false;
#if UNITY_EDITOR || UNITY_STANDALONE
            _flag.Set((int)Flag.IsMultiTap, false);
            _flag.Set((int)Flag.ChangeMultiTap, true);
#endif
        }

        /// <summary>
        /// ベクトルを取得
        /// </summary>
        private Vector3 GetVec()
        {
            return _touchDatas[0]._pos - _touchFirstPos;
        }

        /// <summary>
        /// タッチ位置の距離を取得
        /// </summary>
        public float GetTouchLength()
        {
            return Vector3.Distance(_touchDatas[0]._pos, _touchDatas[1]._pos);
        }

        /// <summary>
        /// タッチ位置の距離を取得
        /// 前回との距離の変化量を返す
        /// </summary>
        public float GetTouchLengthEx()
        {
#if UNITY_EDITOR || UNITY_STANDALONE
            // ホイール
            if (IsMultiTap() &&
                Input.GetAxis("Mouse ScrollWheel") != 0)
            {
                return -Input.GetAxis("Mouse ScrollWheel") * 500.0f;
            }
#endif

            // タッチ数がタッチの最大数に達していなかったら回避
            if (_touchCount < TOUCH_MAX ||
                _oldTouchCount < TOUCH_MAX)
            {
                return 0.0f;
            }

            return Vector3.Distance(_touchDatas[0]._pos, _touchDatas[1]._pos) - Vector3.Distance(_touchDatas[0]._oldPos, _touchDatas[1]._oldPos);
        }

        /// <summary>
        /// 前回の位置と現在の位置の差分
        /// </summary>
        public Vector3 GetTouchVec()
        {
            // タッチ数がタッチの最大数に達していなかったら回避
            if (_touchCount != 1 ||
                _oldTouchCount != 1)
            {
                return Math.VECTOR3_ZERO;
            }
            float Length = Vector3.Distance(_touchDatas[0]._pos, _touchDatas[0]._oldPos);
            if (Length < BAN_TOUCH_VEC)
            {
                return Math.VECTOR3_ZERO;
            }
            else
            {
                return _touchDatas[0]._pos - _touchDatas[0]._oldPos;
            }
        }

        /// <summary>
        /// ダブルタップ時：2タップの合計移動ベクトル(PC時、中ホイールクリック)
        /// </summary>
        public Vector3 GetDoubleTouchVec()
        {
#if UNITY_EDITOR || UNITY_STANDALONE
            // ホイール
            if (IsMultiTap() && Input.GetMouseButton(2))
            {
                _touchDatas[0]._pos = GallopInput.mousePosition();
                return _touchDatas[0]._pos - _touchDatas[0]._oldPos;
            }
#endif
                if (_touchCount != 2 || _oldTouchCount != 2)
            {
                return Math.VECTOR3_ZERO;
            }
            float Length0 = Vector3.Distance(_touchDatas[0]._pos, _touchDatas[0]._oldPos);
            float Length1 = Vector3.Distance(_touchDatas[1]._pos, _touchDatas[1]._oldPos);
            if (Length0 < BAN_TOUCH_VEC || Length1 < BAN_TOUCH_VEC)
            {
                return Math.VECTOR3_ZERO;
            }
            else
            {
                var v0 = _touchDatas[0]._pos - _touchDatas[0]._oldPos;
                var v1 = _touchDatas[1]._pos - _touchDatas[1]._oldPos;
                return v0 + v1;
            }
        }

        /// <summary>
        /// 初回の位置と現在の位置の差分
        /// </summary>
        public Vector3 GetFirstTouchVec()
        {
            // タッチ数がタッチの最大数に達していなかったら回避
            if (_touchCount == 0)
            {
                return Math.VECTOR3_ZERO;
            }
            float Length = Vector3.Distance(_touchDatas[0]._pos, _touchFirstPos);
            if (Length < BAN_MOVE_VEC)
            {
                return Math.VECTOR3_ZERO;
            }
            else
            {
                return _touchDatas[0]._pos - _touchFirstPos;
            }
        }

        /// <summary>
        /// 初回の位置と現在の位置の差分
        /// </summary>
        public float GetFirstTouchLength()
        {
            // タッチ数がタッチの最大数に達していなかったら回避
            if (_touchCount == 0)
            {
                return 0.0f;
            }
            float Length = Vector3.Distance(_touchDatas[0]._pos, _touchFirstPos);
            if (Length < BAN_MOVE_VEC)
            {
                return 0.0f;
            }
            else
            {
                return Length;
            }
        }

        public void UpdateTouchPos()
        {
#if UNITY_EDITOR || UNITY_STANDALONE
            _touchDatas[0]._pos = GallopInput.mousePosition();;

#else
            if (Input.touchCount > 0)
            {
            Touch nowTouch = Input.touches[0];
            _touchDatas [0]._pos = nowTouch.position;
            }
#endif
        }

        /// <summary>
        /// タッチ位置を取得
        /// </summary>
        public Vector3 GetTouchPos(int index = 0)
        {
            return _touchDatas[index]._pos;
        }

        /// <summary>
        /// タッチ位置を取得
        /// </summary>
        public Vector3 GetTouchPosLowResolution(int index = 0)
        {
            float scale = (float)GameDefine.BASE_SCREEN_HEIGHT3D / GameDefine.BASE_SCREEN_HEIGHT;
            return _touchDatas[index]._pos * scale;
        }

        /// <summary>
        /// 現在タッチしてるオブジェクトがBG以外のUIならtrue
        /// </summary>
        /// <returns></returns>
        private bool IsTouchFrontUI(out GameObject touchObject)
        {
            touchObject = null;
            if (!UIManager.HasInstance())
            {
                return false;
            }

            var raycastResult = UIManager.Instance.RaycastAll(GallopInput.mousePosition());

            // 選択されたuGUIオブジェクトがあるか(=親がキャンバスか？)チェックする。
            // BGCanvasのみ無視
            for (int i = 0; i< raycastResult.Count; i++)
            {
                var result = raycastResult[i];
                if(result.module != null && result.module.gameObject != UIManager.BgCanvas.gameObject)
                {
                    touchObject = result.module.gameObject;
                    return true;
                }
            }
            return false;
        }

#endregion

#region 取得

        /// <summary>
        /// 状態取得
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public TouchType GetState(int index)
        {
            if (TOUCH_MAX <= index)
            {
                return TouchType.None;
            }
            return _touchDatas[index]._type;
        }

        /// <summary>
        /// なにもしていないかどうかを取得
        /// </summary>
        public bool IsNone()
        {
            if (_touchDatas[0]._type == TouchType.None && Input.touchCount == 0)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 押している間かどうかを取得
        /// </summary>
        public bool IsPress()
        {
            if (_touchDatas[0]._type == TouchType.Press)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 押された瞬間かどうかを取得
        /// </summary>
        public bool IsDown()
        {
            if (_touchDatas[0]._type == TouchType.Down)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 離した瞬間かどうかを取得
        /// </summary>
        public bool IsUp()
        {
            if (_touchDatas[0]._type == TouchType.Up)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 押されているが動いていないかどうかを取得
        /// </summary>
        public bool IsStay()
        {
            if (_touchDatas[0]._type == TouchType.Stay)
            {
                return true;
            }
            return false;
        }

#if USE_FLICK
        /// <summary>
        /// フリック(左)
        /// </summary>
        public bool IsFlickLeft()
        {
            return _flag.Get((int)Flag.FlickLeft);
        }

        /// <summary>
        /// フリック(右)
        /// </summary>
        public bool IsFlickRight()
        {
            return _flag.Get((int)Flag.FlickRight);
        }
        /// <summary>
        /// フリック(上)
        /// </summary>
        public bool IsFlickUp()
        {
            return _flag.Get((int)Flag.FlickUp);
        }
        /// <summary>
        /// フリック(下)
        /// </summary>
        public bool IsFlickDown()
        {
            return _flag.Get((int)Flag.FlickDown);
        }
#endif
#if USE_PINCH
        /// <summary>
        /// ピンチ
        /// </summary>
        public bool IsPinch()
        {
            return _flag.Get((int)Flag.Pinch);
        }
#endif

        /// <summary>
        /// ドラッグ
        /// </summary>
        public bool IsDrag()
        {
            return _flag.Get((int)Flag.Drag);
        }

        /// <summary>
        /// 前回ドラッグ
        /// </summary>
        /// <returns></returns>
        public bool IsPrevDrag()
        {
            return _flag.Get((int)Flag.PrevDrag);
        }

        /// <summary>
        /// 前回ドラッグ
        /// </summary>
        /// <returns></returns>
        public bool IsPrevDragNoTime()
        {
            return _flag.Get((int)Flag.PrevDragNoTime);
        }

        /// <summary>
        /// ロングプレス
        /// </summary>
        /// <returns>可否</returns>
        public bool IsLongPress()
        {
            return _flag.Get((int)Flag.LongPress);
        }



        /// <summary>
        /// マルチタップ中かどうか
        /// </summary>
        /// <returns>マルチタップ中かどうか</returns>
        public bool IsMultiTap()
        {
#if UNITY_EDITOR || UNITY_STANDALONE
            bool isMultiTap = _flag.Get((int)Flag.IsMultiTap);
            return isMultiTap;
#else
            return Input.multiTouchEnabled;
#endif

        }
        
        /// <summary>
        /// IOS13.1用 : iOS13では3本指同時タッチ時にmousePoitionが吹き飛ぶことがあるため値が異常値になっているかを判定
        /// </summary>
        /// <returns></returns>
        public static bool IsIgnoreTouchedScreenPosition(Vector3 screenPosition)
        {
            if (Mathf.Abs(screenPosition.x) > IGNORE_SCREEN_POSITION_THRESHOLD ||
                Mathf.Abs(screenPosition.y) > IGNORE_SCREEN_POSITION_THRESHOLD)
            {
                return true;
            }

            return false;
        }

#endregion
    }
}
