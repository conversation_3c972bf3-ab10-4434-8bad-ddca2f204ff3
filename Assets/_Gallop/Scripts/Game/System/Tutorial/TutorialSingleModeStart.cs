using UnityEngine;
using UnityEngine.UI;
using System;
using System.Collections;
using Cute.Core;
using DG.Tweening;

namespace Gallop 
{
    namespace Tutorial
    {
        /// <summary>
        /// 育成開始画面のガイド表示等をサポートする
        /// チュートリアルでSingleMode開始時に生成、終了時に破棄する
        /// </summary>
        public class TutorialSingleModeStart
        {
            public const int SINGLE_MODE_SCENARIO_ID = 1;    // シナリオ
            
            private const float DELAY_TUTORIAL = 0.06f;
            
            public const int STORY_ID_SINGLE_START_INTRO = 010002005;            // 育成チュートリアル導入
            public const int STORY_ID_SINGLE_START_CARD_SELECT = 010002006;      // 育成ウマ娘選択
            public const int STORY_ID_SINGLE_START_SUCCESSION_PRE = 010002007;   // 継承選択（初期遷移、ここで一度ChangeView)
            public const int STORY_ID_SINGLE_START_SUCCESSION_1ST = 010002009;   // 継承選択（左欄を選択）
            public const int STORY_ID_SINGLE_START_SUCCESSION_2ND = 010002010;   // 継承選択（自動編成を選ぶ）
            public const int STORY_ID_SINGLE_START_SUCCESSION_3RD = 010002011;   // 継承選択（決定）
            public const int STORY_ID_SINGLE_START_SUPPORT_SELECT = 010002012;   // サポカ自動編成

            // 継承ウマ娘が選択可能になるまでの待ち時間 (秒)
            private const float SUCCESSION_SELECT_WAIT_TIME = 0.2f;
            
            /// <summary>
            /// チュートリアルガイド：育成ルート選択
            /// </summary>
            public static void SetGuideOnSingleStartView(SingleModeStartStepRouteSelect routeSelect)
            {
                // ショートストーリー
                SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_INTRO, () =>
                {
                    // 機能別チュートリアル
                    DialogTutorialGuide.PushDialog(DialogTutorialGuide.TutorialGuideId.SingleModeScenario, () =>
                    {
                        // 次へボタンにフォーカス
                        TutorialManager.Instance.OpenGuide(routeSelect.NextButton.image.rectTransform,
                            routeSelect.NextButton, GuideArrowType.Down, string.Empty, () =>
                            {
                                // 育成カード選択へ進む
                            });
                    });
                });
            }

            /// <summary>
            /// チュートリアルガイド：育成カード選択
            /// </summary>
            public static void SetGuideOnSingleStartView(SingleModeStartStepCardSelect cardSelect)
            {
                // ショートストーリー
                SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_CARD_SELECT, () =>
                {
                    // 機能別チュートリアル
                    DialogTutorialGuide.PushDialog(DialogTutorialGuide.TutorialGuideId.SingleModeCardSelect, () =>
                    {
                        // 先頭のキャラクターボタンにフォーカス
                        var chooseButton = cardSelect.GetTutorialChooseCardButton();
                        chooseButton.MyButton.RemoveAllOnLongTap();
                        DisableLayoutGroup(chooseButton.transform.parent);
                        TutorialManager.Instance.OpenGuide(chooseButton.MyButton.transform as RectTransform, chooseButton.MyButton,
                            GuideArrowType.Down, string.Empty, () =>
                            {
                                // 次へボタンにフォーカス
                                TutorialManager.Instance.OpenGuide(cardSelect.NextButton.image.rectTransform,
                                    cardSelect.NextButton, GuideArrowType.Down, string.Empty, () =>
                                    {
                                        // 継承選択へ進む
                                    });
                            });
                    });
                });
            }

            /// <summary>
            /// 継承選択 : 左+選択
            /// </summary>
            public static void SetGuideOnSingleStartSuccession(SingleModeStartStepSuccessionSelect successionSelect)
            {
                var selectParts = successionSelect.SuccessionSelect;

                // ルート選択前半は、ショートストーリーを再生していったんストーリー画面に遷移する
                if (TutorialManager.Instance.CurrentPrologueState == PrologueStateSequencer.PrologueState.TrainingStartRouteSelect)
                {
                    SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_SUCCESSION_PRE,
                        () => { SceneManager.Instance.ChangeView(SceneDefine.ViewId.Story); });
                    return;
                }

                // ルート選択後半はショートストーリーを再生しつつ継承ウマ娘を選択してもらう
                SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_SUCCESSION_1ST, () =>
                {
                    // ＋ボタンにフォーカス
                    var plusButton = selectParts.FirstSelectButton;
                    TutorialManager.Instance.OpenGuide(plusButton.image.rectTransform, plusButton,
                        GuideArrowType.Down, string.Empty, () =>
                        {
                            // Local
                            IEnumerator PlayAfterPlusButton()
                            {
                                var detailParts = successionSelect.SuccessionDetail;
                                var scrollRect = detailParts.TutorialCharacterButton.GetComponentInParent<ScrollRectCommon>();

                                // 自動レイアウトの更新を待つ
                                yield return new WaitForSeconds(SUCCESSION_SELECT_WAIT_TIME);

                                // チュートリアルではスクロールバー自体表示されないはずなのでスクロールを強制的に収束させておく
                                scrollRect.verticalNormalizedPosition = 1f;
                                scrollRect.velocity = Vector2.zero;
                                // 収束後の1f待ち
                                yield return null;                                
                                
                                UIManager.Instance.UnlockGameCanvas();

                                SetGuideOnSingleStartSuccessionClickLeftPlus(successionSelect);
                            }

                            UIManager.Instance.LockGameCanvas();
                            UIManager.Instance.StartCoroutine(PlayAfterPlusButton());
                        });
                });
            }

            /// <summary>
            /// 継承選択 : 左+クリック後のキャラ選択
            /// </summary>
            /// <param name="successionSelect"></param>
            public static void SetGuideOnSingleStartSuccessionClickLeftPlus(SingleModeStartStepSuccessionSelect successionSelect)
            {
                var detailParts = successionSelect.SuccessionDetail;

                // 先頭のキャラクターボタンにフォーカス
                var headButton = detailParts.TutorialCharacterButton;
                headButton.MyButton.RemoveAllOnLongTap();
                DisableLayoutGroup(headButton.transform.parent);
                TutorialManager.Instance.OpenGuide(headButton.MyButton.transform as RectTransform,
                    headButton.MyButton, GuideArrowType.Down, string.Empty, () =>
                    {
                        SetGuideOnSingleStartSuccessionSelectCharacter(successionSelect);
                    });
            }

            /// <summary>
            /// 継承選択 : 左+クリック後のキャラ選択後の決定ボタンクリック
            /// </summary>
            /// <param name="successionSelect"></param>
            public static void SetGuideOnSingleStartSuccessionSelectCharacter(SingleModeStartStepSuccessionSelect successionSelect)
            {
                var detailParts = successionSelect.SuccessionDetail;
            
                // 決定ボタンにフォーカス
                var detailDecideButton = detailParts.DecideButton;
                TutorialManager.Instance.OpenGuide(detailDecideButton.image.rectTransform,
                    detailDecideButton, GuideArrowType.Down, string.Empty, () =>
                    {
                        // ショートストーリー
                        SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_SUCCESSION_2ND, () =>
                        {
                            SetGuidOnSingleStartSuccessionAutoSelect(successionSelect);
                        });
                    });
            }

            /// <summary>
            /// 継承選択 : 自動編成
            /// </summary>
            /// <param name="successionSelect"></param>
            public static void SetGuidOnSingleStartSuccessionAutoSelect(SingleModeStartStepSuccessionSelect successionSelect)
            {
                var selectParts = successionSelect.SuccessionSelect;

                // 自動編成ボタンにフォーカス。ボタン押下後の流れを制御したいので、コールバックを一旦乗っ取る
                var autoButton = selectParts.AutoSelectButton;
                autoButton.SetOnClick(() => { });
                
                // 「おまかせ編成」に対してガイド表示
                TutorialManager.Instance.OpenGuide(
                    autoButton.image.rectTransform, autoButton,
                    GuideArrowType.Down, string.Empty, () =>
                    {
                        SetGuidOnSingleStartSuccessionAutoSelectConfirmDialogOK(successionSelect);
                    });
            }
            
            /// <summary>
            /// 継承選択 : 継承選択確認ダイアログの「OK」
            /// </summary>
            /// <param name="successionSelect"></param>
            public static void SetGuidOnSingleStartSuccessionAutoSelectConfirmDialogOK(SingleModeStartStepSuccessionSelect successionSelect)
            {
                var selectParts = successionSelect.SuccessionSelect;
                
                // おまかせ編成確認ダイアログを開く
                selectParts.OpenAutoSelectConfirm();
                var dialogCommon = DialogManager.GetForeFrontDialog() as DialogCommon;
                var dialogData = dialogCommon.DialogData;
                
                // 次のチュートリアルイベントを発行するため、DestoryCallbackを乗っ取る
                dialogCommon.DialogData.AddDestroyCallback(() =>
                {
                    PlayShortStoryAndTipsAfterSuccessionAutoSelect(successionSelect);
                });

                dialogData.AddOpenCallback((dialog) =>
                {
                    // おまかせ編成確認ダイアログの「OK」ボタンガイドを表示
                    TutorialManager.Instance.OpenGuideForDialogButton(
                        dialog,
                        DialogCommon.ButtonIndex.Right,
                        GuideArrowType.Down,
                        string.Empty);
                });
            }

            /// <summary>
            /// 継承選択 : 継承おまかせ編成後の、ショートストーリー再生 ＆ Tips表示
            /// </summary>
            /// <param name="successionSelect"></param>
            public static void PlayShortStoryAndTipsAfterSuccessionAutoSelect(SingleModeStartStepSuccessionSelect successionSelect)
            {
                // ショートストーリー
                SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_SUCCESSION_3RD,
                    () =>
                    {
                        // 機能別チュートリアル
                        DialogTutorialGuide.PushDialog(
                            DialogTutorialGuide.TutorialGuideId.SingleModeSuccessionSelect,
                            () => { SetGuidOnSingleStartSuccessionToNext(successionSelect); });
                    });
            }
            
            /// <summary>
            /// 継承選択 : 最後の「次へ」
            /// </summary>
            /// <param name="successionSelect"></param>
            public static void SetGuidOnSingleStartSuccessionToNext(SingleModeStartStepSuccessionSelect successionSelect)
            {
                // 次へボタンにフォーカス
                var nextButton = successionSelect.NextButton;
                TutorialManager.Instance.OpenGuide(
                    nextButton.image.rectTransform,
                    nextButton, GuideArrowType.Down,
                    string.Empty, () =>
                    {
                        // 継承選択へ進む
                    });
            }

            /// <summary>
            /// チュートリアルガイド：サポート選択
            /// </summary>
            public static void SetGuideOnSingleStartView(SingleModeStartStepEquipSelect supportSelect)
            {
                // ショートストーリー
                SceneManager.Instance.PlayShortStory(STORY_ID_SINGLE_START_SUPPORT_SELECT, () =>
                {
                    // 機能別チュートリアル
                    DialogTutorialGuide.PushDialog(DialogTutorialGuide.TutorialGuideId.SingleModeSupportEdit, () =>
                    {
                        // 自動編成ボタンにフォーカス
                        var autoButton = supportSelect.SupportCardDeck.DeckList.FirstItem.AutoButton;
                        
                        // 親階層のLayoutを停止させる
                        var layoutGroup = autoButton.GetComponentInParent<LayoutGroup>();
                        if (layoutGroup)
                        {
                            layoutGroup.enabled = false;
                        }
                        
                        TutorialManager.Instance.OpenGuide(autoButton.image.rectTransform, autoButton,
                            GuideArrowType.Down, string.Empty, () =>
                            {
                                layoutGroup.enabled = true;                                
                                
                                // 育成開始ボタンにフォーカス
                                var startButton = supportSelect.NextButton;
                                TutorialManager.Instance.OpenGuide(startButton.transform as RectTransform, startButton,
                                    GuideArrowType.Down, string.Empty, () =>
                                    {
                                        // 育成開始確認ダイアログへ
                                    });
                            });
                    });
                });
            }

            /// <summary>
            /// チュートリアルガイド：開始確認ダイアログ
            /// </summary>
            /// <param name="dialog"></param>
            public static void SetGuideOnDialogSingleModeStartConfirmEntry(DialogCommon dialog, Action onClickNext)
            {
                // DialogのOpenCallbackだとボタン位置が正しく取得できないことがあるのでディレイをかける
                UIManager.Instance.LockGameCanvas();
                DOVirtual.DelayedCall(DELAY_TUTORIAL, () =>
                {
                    UIManager.Instance.UnlockGameCanvas();

                    // 育成開始ボタン（ダイアログ）にフォーカス
                    TutorialManager.Instance.OpenGuideForDialogButton(
                        dialog,
                        DialogCommon.ButtonIndex.Right,
                        GuideArrowType.Down,
                        string.Empty,
                        () =>
                        {
                            // 継承演出で中断した際はチュートリアルから再開してほしいので、チュートリアルの開始ステップまでスキップする
                            TutorialManager.Instance.SendTutorialStepRequest(
                                (int) TutorialSingleMode.Step.Turn1,
                                () =>
                                {
                                    TutorialManager.Instance.CloseAllGuide();
                                    dialog.Close();
                                    onClickNext?.Invoke();
                                });
                        });
                });
            }
            
            /// <summary>
            /// 自動レイアウトを停止
            /// </summary>
            /// <param name="target"></param>
            private static void DisableLayoutGroup(Transform target)
            {
                var layoutGroup = target.GetComponent<LayoutGroup>();
                if (layoutGroup == null)
                    return;
            
                layoutGroup.enabled = false;
            }

            public static SingleModeStartViewController.ViewInfo GetResumeBySuccessionViewInfo()
            {
                // 復帰用情報を作成
                var entryInfo = new SingleModeStartViewController.EntryInfo();
                entryInfo.ScenarioId = SINGLE_MODE_SCENARIO_ID;
                entryInfo.CardId = TutorialSingleMode.TRAINING_CARD_ID;
                
                SingleModeStartViewController.ViewInfo singleStartViewInfo = 
                    new SingleModeStartViewController.ViewInfo(SingleModeStartView.Step.SuccessionSelect, entryInfo);
                return singleStartViewInfo;
            }
        }
    }
}