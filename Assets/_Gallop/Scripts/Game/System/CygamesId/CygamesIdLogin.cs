using UnityEngine;
using System;
using System.Collections;

namespace Gallop
{
    /// <summary>
    ///  CygamesIdアカウント連携関連の処理
    /// </summary>
    public class CygamesIdLogin : MonoSingleton<CygamesIdLogin>
    {
        // SNSログインを再度実行できるようにするまでのウェイト時間（秒）
        private const float SNS_LOGIN_RESET_TIME = 0.5f;

        /// <summary>
        /// ログイン成功時に返却されるトークンのフォーマット
        /// </summary>
        class LoginToken
        {
            public string id_token;
            public string access_token;
            public string refresh_token;
        }

        /// <summary>
        /// CygamesIdアカウントのトークン
        /// </summary>
        public string Token { get; private set; }

        /// <summary>
        /// 最後にCygamesIdのサインインに使用したアカウント種別（AppleとGoogleのみ）
        /// </summary>
        public ACCOUNT_TYPE LastAccountType { get; private set; } = ACCOUNT_TYPE.NONE;

        /// <summary>
        /// 現在ログインを試みているアカウント種別（AppleとGoogleのみ）
        /// </summary>
        public ACCOUNT_TYPE ProcessingAccountType { get; private set; } = ACCOUNT_TYPE.NONE;

        /// <summary>
        /// SNSログイン処理中かどうか
        /// </summary>
        private bool _isSnsLoginProceed = false;

        /// <summary>
        /// SNSログイン処理中の表示ダイアログ
        /// </summary>
        private DialogCommon _snsLoginProceedDialog = null;

        /// <summary>
        /// SNSログインが実行可能な状態かどうか
        /// </summary>
        private bool _isValidSnsLogin = false;

        /// <summary>
        /// Ver2.11.5 CygamesID連携解放日時(ServerDefineのインスタンスはload通信後に作られるのでこちら側で管理する)
        /// </summary>
        private SystemOpenDate _cygamesIdOpenDate = new SystemOpenDate();
        public SystemOpenDate CygamesIdOpenDate => _cygamesIdOpenDate;
        public static bool IsEnableCygamesId
        {
            get
            {
                if (!HasInstance()) return false;
                return Instance._cygamesIdOpenDate.IsEnable;
            }
        }

        /// <summary>
        /// 退会用URL
        /// </summary>
        public string ResignUrl { get; set; }

        protected override void OnInitialize()
        {
            // AwsConfigの設定はサーバ受け取りに変更したのでここでは初期化しない
        }

        /// <summary>
        /// AwsConfigに渡すPlatformIdを取得する
        /// </summary>
        private int GetPlatformId()
        {
            // 1: iOS, 2: Android, 3: Windows
            // 現状はgallopで定義しているplatformIdと同じなのでそのまま値を返す
            return DeviceHelper.GetPlatform();
        }

        /// <summary>
        /// CygamesIdアカウントのGoogleアカウントによるログイン処理
        /// </summary>
        /// <param name="onSuccess">成功時コールバック</param>
        /// <param name="onError">失敗時コールバック</param>
        public void LoginToGoogle(Action onSuccess, Action onError)
        {
            if (!_isValidSnsLogin)
            {
                return;
            }

            OpenSnsLoginProceedDialog();
        }

        /// <summary>
        /// CygamesIdアカウントのAppleアカウントによるログイン処理
        /// </summary>
        /// <param name="onSuccess">成功時コールバック</param>
        /// <param name="onError">失敗時コールバック</param>
        public void LoginToApple(Action onSuccess, Action onError)
        {
            if (!_isValidSnsLogin)
            {
                return;
            }

            OpenSnsLoginProceedDialog();
        }

        /// <summary>
        /// ログイン処理コールバック共通処理
        /// </summary>
        /// <param name="result">ログイン処理に成功したかどうか</param>
        /// <param name="token">トークン情報</param>
        /// <param name="onSuccess">成功時コールバック</param>
        /// <param name="onError">失敗時コールバック</param>
        private void LoginCallback(bool result, string token, Action onSuccess, Action onError)
        {
            if (!result)
            {
                // 失敗したので処理を終了
                onError?.Invoke();
                return;
            }

            var json = JsonUtility.FromJson<LoginToken>(token);
            Token = json.id_token;
            Debug.Log("CygamesIdLogin Login token:" + Token);
            // 成功したのでコールバックを呼び出す
            onSuccess?.Invoke();
        }

        /// <summary>
        /// SNSログイン処理コールバック共通処理
        /// </summary>
        /// <param name="result">ログイン処理に成功したかどうか</param>
        /// <param name="token">トークン情報</param>
        /// <param name="onSuccess">成功時コールバック</param>
        /// <param name="onError">失敗時コールバック</param>
        private void SnsLoginCallback(bool result, string token, Action onSuccess, Action onError)
        {
            // 処理中でなければ無視
            if (!_isSnsLoginProceed)
            {
                return;
            }

            // ログイン処理中のダイアログは閉じる
            if (_snsLoginProceedDialog != null)
            {
                _snsLoginProceedDialog.Close();
            }

            LoginCallback(result, token, onSuccess, onError);
        }

        /// <summary>
        /// SNSログイン処理中のダイアログを開く
        /// </summary>
        private void OpenSnsLoginProceedDialog()
        {
            _isSnsLoginProceed = true;
            var dialogData = new DialogCommon.Data();
            dialogData.SetSimpleOneButtonMessage(TextId.AccoutDataLink608008.Text(), TextId.AccoutDataLink608009.Text());
            dialogData.CenterButtonText = TextId.Common0004.Text();
            dialogData.AddBeginCloseCallback(_ =>
            {
                _snsLoginProceedDialog = null;
                _isSnsLoginProceed = false;
                _isValidSnsLogin = false;
                StartCoroutine(ResetIsValidSnsLogin());
            });
            _snsLoginProceedDialog = DialogManager.PushDialog(dialogData);
        }

        /// <summary>
        /// SNSログインを再度有効にするまでのウェイト処理
        /// </summary>
        private IEnumerator ResetIsValidSnsLogin()
        {
            // 0.5秒待ってからフラグをリセット
            yield return new WaitForSeconds(SNS_LOGIN_RESET_TIME);;

            _isValidSnsLogin = true;
        }

        /// <summary>
        /// データ連携情報取得（Cygames ID関連の表示用）
        /// </summary>
        public void RequestDataLink(Action onSuccess)
        {
            var req = new DataLinkIndexRequest();
            req.Send(res =>
            {
                if (res.data == null)
                {
                    onSuccess?.Invoke();
                    return;
                }

                _cygamesIdOpenDate.SetOpenDate(res.data.open_date);
                GameDefine.UpdateEnableCygamesID();
                LastAccountType = (ACCOUNT_TYPE)res.data.last_account_type;
                // SDKのパラメータ初期化（念のため有効な値が入ってきている時に反映）
                if (res.data.app_id > 0)
                {
                    // ゲーム起動中2回目以降はインスタンス作成済みのため反映されないが基本運用中に更新するものではない
                    // 万が一更新する場合でもタイトル戻しとセットにすればソフトウェアリセットがかかるので問題ない
                    
                    _isValidSnsLogin = true;
                }

                onSuccess?.Invoke();
            });
        }

        /// <summary>
        /// 最後にCygamesIdのサインインに使用したアカウント種別を更新する
        /// </summary>
        /// <param name="linkAccountType">ゲームIDと連携したアカウント種別</param>
        /// <param name="lastAccountType">最後にCygamesIdのサインインに使用したアカウント種別</param>>
        public void UpdateLastAccountType(ACCOUNT_TYPE linkAccountType, ACCOUNT_TYPE lastAccountType)
        {
            if (linkAccountType != ACCOUNT_TYPE.CYGAMES_ID)
            {
                return;
            }

            LastAccountType = lastAccountType;
        }
    }
}
