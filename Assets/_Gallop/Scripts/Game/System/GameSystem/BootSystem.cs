using UnityEngine;
using System.Collections;
using Gallop.RenderPipeline;

namespace Gallop
{
    public class BootSystem : MonoBehaviour
    {
#if !CYG_PRODUCT
        private static bool _downloadLoginFilesToBeRemoved;

        public static void SetRemoveDownloadLoginFilesFlag()
        {
            _downloadLoginFilesToBeRemoved = true;
        }
#endif
        [SerializeField]
        private GameObject _bgCanvasObject = null;

        //URP:置き換え対応
        [SerializeField]
        private Camera _bootCamera = null;

#if !UNITY_EDITOR && UNITY_ANDROID
        /// <summary> Unity2022用に入れたAndroidのCutoutの処理を既に実行済みかどうか </summary>
        private static bool _isExecutedAndroidCutoutSetting = false;
#endif

#if CYG_DEBUG && ANDROID_PC
        public static bool IsBootHorizontal = false;
#endif

        private void Awake()
        {
            //URP:置き換え対応
            //直接画面へ描画する
            var cameraData = _bootCamera.GetCameraData();
            cameraData.Initialize();
            cameraData.RequestAntiAliasing = CameraData.AntiAliasLevel.None;

            var x = Cute.Core.Perf.Time.Instance;
            StartCoroutine(BootCoroutine());

#if DMM && !UNITY_EDITOR
            //前回の終了時に横画面だった場合を考慮に入れて反転しておく.
            Vector2 size = new Vector2( UnityEngine.Screen.width > UnityEngine.Screen.height ? UnityEngine.Screen.height : UnityEngine.Screen.width,
                                        UnityEngine.Screen.width > UnityEngine.Screen.height ? UnityEngine.Screen.width : UnityEngine.Screen.height
                                        );

            size = StandaloneWindowResize.GetChangedSize(size.x, size.y, true);
            if (Gallop.StandaloneWindowResize.CheckOverScreenSize(size.x, size.y))
            {
                StandaloneWindowResize.IsPreventReShape = false;
                //超えていたら現在の値縦横のみ入れ替えた現在の値を渡してkeepAspectで縮小維持.
                Gallop.StandaloneWindowResize.KeepAspectRatio(size.x, size.y);
                StandaloneWindowResize.IsPreventReShape = true;
            }
            else
            {
                //超えていなければユーザーの指定した値に変更
                Gallop.Screen.SetResolution((int)size.x, (int)size.y, false);
            }
#endif
        }

        private IEnumerator BootCoroutine()
        {
#if !UNITY_EDITOR && UNITY_ANDROID
            if (!_isExecutedAndroidCutoutSetting)
            {
                // Unityの不具合でAndroidのsafeAreaが正しく計算されない問題があった。2022.3.38f1で直る野を確認できたがバージョンアップはもうできないので自前で計算して設定する
                // https://issuetracker.unity3d.com/issues/android-screen-dot-safearea-returns-wrong-height-when-building-native-app-with-unity-as-a-library
                // refs #140201
                using(AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
                using (AndroidJavaObject activ = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity"))
                {
                    int safeAreaHeight = UnityEngine.Screen.height;
                    int safeAreaWidth = UnityEngine.Screen.width;
                    using (AndroidJavaObject window = activ.Call<AndroidJavaObject>("getWindow"))
                    {
                        using (AndroidJavaObject decorView = window.Call<AndroidJavaObject>("getDecorView"))
                        {
                            using (AndroidJavaObject windowInsets = decorView.Call<AndroidJavaObject>("getRootWindowInsets"))
                            {
                                if (windowInsets != null)
                                {
                                    // Android OS9.0以上からCutoutがサポートされておりそれ以下の端末では「getDisplayCutout」のメソッドが提供されておらずエラーが発生するため例外をキャッチする
                                    try
                                    {
                                        using (AndroidJavaObject displayCutout = windowInsets.Call<AndroidJavaObject>("getDisplayCutout"))
                                        {
                                            // 最初はactiv.Call<int>("GetScreenHeight")とかで取得した高さをUnity.Scree.Size.heightに入れていたがそれだと一部端末で想定よりも大きい解像度が設定されてしまうことがる。
                                            // 例えば「AQUOS R5G」とかはUnity.Screen.Size = (720, 1514)で渡ってくるのだがGetScreenHeight、GetScreenWidthで取ると(1080, 2273)になる。これによってRenderTextureのメモリが
                                            // 想定よりも多く確保されてしまうため今回Unity.Screen.heightをベースにしてそこからCutout分の領域を引くことにした
                                            if (displayCutout != null)
                                            {
                                                int safeInsetLeft = displayCutout.Call<int>("getSafeInsetLeft");
                                                int safeInsetTop = displayCutout.Call<int>("getSafeInsetTop");
                                                int safeInsetRight = displayCutout.Call<int>("getSafeInsetRight");
                                                int safeInsetBottom = displayCutout.Call<int>("getSafeInsetBottom");

                                                safeAreaHeight = UnityEngine.Screen.height - safeInsetTop - safeInsetBottom;
                                                safeAreaWidth = UnityEngine.Screen.width - safeInsetLeft - safeInsetRight;
                                                Debug.Log(
                                                    $"CutOut(top, bottom, left. right): ({safeInsetTop}, {safeInsetBottom}, {safeInsetLeft}, {safeInsetRight})");
                                            }
                                            else
                                            {
                                                Debug.LogWarning("DisplayCutout is null. Safe Area cannot be determined.");
                                            }
                                        }
                                    }
                                    catch (AndroidJavaException e)
                                    {
                                        // サポートされていないということはCutout自体機能していないということなので処理は実行せずエラーを握りつぶす
                                        if (e.Message.Contains("NoSuchMethodError"))
                                        {
                                            Debug.Log("DisplayCutout not supported");
                                        }
                                        else
                                        {
                                            // その他のエラーは握りつぶしたくないのでそのまま返す
                                            throw;
                                        }
                                    }
                                }
                                else
                                {
                                    Debug.LogWarning("WindowInsets is null. Safe Area cannot be determined.");
                                }
                            }
                        }
                        UnityEngine.Screen.SetResolution(safeAreaWidth, safeAreaHeight, true);
                        Debug.Log($"safe Area = {safeAreaWidth}, {safeAreaHeight}");

                        int i = 0;
                        while (UnityEngine.Screen.width != safeAreaWidth || UnityEngine.Screen.height != safeAreaHeight)
                        {
                            i++;
                            if (i >= 5) // 最大5フレームまで待つ
                            {
                                break;
                            }

                            yield return null;
                        }

                        Debug.Log($"screen size has been changed [{i}frames]: {UnityEngine.Screen.width}, {UnityEngine.Screen.height}");
                        // 設定し直す
                        Screen.Setup();
                    }
                }

                _isExecutedAndroidCutoutSetting = true;
            }
#endif

#if DMM && !UNITY_EDITOR || ANDROID_PC
            // リサイズが適応されるまで待たないと荒れる(横から縦への切り替えは1フレームでは済まない模様)
            // Window、もしくはUnity側からオートでAspect比を無視した強制リサイズが走ると0.1秒待ってもまだ足りない模様.
            const float RESIZE_WAIT_TIME = 0.3f;
            yield return new WaitForSeconds(RESIZE_WAIT_TIME);
#endif
            //初回だけ背景を表示する
            _bgCanvasObject.SetActive(GameSystem.Instance.State == GameSystem.SystemState.Booting);
            // Managerの初期化を実行
            yield return GameSystem.Instance.InitializeGame();
#if !CYG_PRODUCT
            //ファイル削除なので最速で行う
            if (_downloadLoginFilesToBeRemoved)
            {
                _downloadLoginFilesToBeRemoved = false;

                Debug.Log("Start deleting DownloadLogin files..");
                yield return AssetManager.LocalFile.DeleteFilesByAnyGroupAsync((int)Gallop.AssetBundle.AssetBundleGroup.DownloadLogin);
                Debug.Log("Deleted DownloadLogin files");
            }
#endif
            //スプラッシュに移る際に一瞬白背景が見えてしまうので、先んじてセットしておく
            BGManager.Instance.SetBg(SceneDefine.BgId.Splash);
            BGManager.SetBgCameraEnable(true);

#if DMM
#if !UNITY_EDITOR
            Gallop.StandaloneWindowResize.CreateInstance();
#endif
            //AnimateToUnityにも画面サイズの変更を伝える.
            AnimateToUnity.AnRootManager.Instance.ScreenRate = (float)Gallop.Screen.Width / (float)UnityEngine.Screen.width;
#endif

#if ANDROID_PC

            Debug.Log("yonekawa Begin UnityScreenHight = " + UnityEngine.Screen.currentResolution.height.ToString());

            // ディスプレイの横と縦幅から画面のアスペクト比求める.
            float aspectRatio = Gallop.Screen.virticalWidthRate;
            float widthPixes = (float)UnityEngine.Screen.height * aspectRatio; // 実横幅ピクセル数.
            Debug.Log("yonekawa Begin WidthPixel = " + widthPixes.ToString());
            Debug.Log("yonekawa Begin UnityEngine.Screen.width = " + UnityEngine.Screen.width.ToString());
            Debug.Log("yonekawa Begin UnityEngine.Screen.height = " + UnityEngine.Screen.height.ToString());

            Debug.Log("yonekawa Begin UnityEngine.Screen.currentResolution.width = " + UnityEngine.Screen.currentResolution.width.ToString());
            Debug.Log("yonekawa Begin UnityEngine.Screen.currentResolution.height = " + UnityEngine.Screen.currentResolution.height.ToString());

            //最終結果のカメラの描画範囲を変更する
            if (UIManager.HasInstance() && UIManager.Instance.UiToFrameBufferBlitCamera != null)
            {
                UIManager.Instance.UiToFrameBufferBlitCamera.rect = new Rect(
                    (1.0f - ((float) widthPixes / (float) UnityEngine.Screen.width)) * 0.5f, 0.0f,
                    (float) widthPixes / (float) UnityEngine.Screen.width, 1.0f);
            }
            
#if CYG_DEBUG 
            if( IsBootHorizontal )
            {
                // 縦画面時と違って、多くのオブジェクトの状態が縦前提でセットアップされてしまっているのでここで画面回転処理そのものを走らせる
                yield return Screen.ChangeScreenOrientationLandscapeAsync();
            }
            IsBootHorizontal = false;
#endif
            
#endif
            SceneManager.Instance.BootView();
        }
#if DMM && !UNITY_EDITOR
        private void LateUpdate()
        {
            StandaloneWindowResize.CheckAspectRatio();
        }
#endif

#if UNITY_ANDROID && !UNITY_EDITOR
        private void OnDestroy()
        {
            // Bootシーンが終わるまでSplashViewの表示が続ける、途中で削除すると画面が一瞬点滅するので、あえてOnDestroyで処理する。
            // Unity インスタンスを取得
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            // 現在のアクティビティを取得
            AndroidJavaObject activ = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            // SplashViewを削除
            activ.Call("RemoveSplashView");
        }
#endif
    }

}
