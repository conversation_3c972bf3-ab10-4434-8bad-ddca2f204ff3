using System;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// イベント購読サブジェクト
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public sealed class EventSubject<T> : IObserver<T>, IObservable<T>, IDisposable
    {
        #region Nested Types

        /// <summary>
        /// イベント購読オジェクト
        /// </summary>
        private class Subscription : IDisposable
        {
            private readonly object _lock = new object();

            private EventSubject<T> parent;

            private IObserver<T> unsubscribeTarget;

            public Subscription(EventSubject<T> parent, IObserver<T> unsubscribeTarget)
            {
                this.parent = parent;
                this.unsubscribeTarget = unsubscribeTarget;
            }

            public void Dispose()
            {
                lock (_lock)
                {
                    if (parent == null)
                        return;

                    lock (parent._lock)
                    {
                        parent._observerList.Remove(unsubscribeTarget);
                        unsubscribeTarget = null;
                        parent = null;
                    }
                }
            }
        }

        /// <summary>
        /// 空のDisposable
        /// </summary>
        private class EmptyDisposable : IDisposable
        {
            public static readonly EmptyDisposable Singleton = new EmptyDisposable();

            private EmptyDisposable()
            {
            }

            public void Dispose()
            {
            }
        }

        #endregion

        #region Member

        private readonly object _lock = new object();

        /// <summary>
        /// Dispose済みか
        /// </summary>
        private bool _isDisposed;

        /// <summary>
        /// 購読者リスト
        /// </summary>
        private readonly List<IObserver<T>> _observerList = new List<IObserver<T>>();

        /// <summary>
        /// 購読者がいるか
        /// </summary>
        public bool HasObservers => _observerList.Count > 0 && !_isDisposed;

        #endregion

        #region IObserver<T> Implementation

        public void OnNext(T value)
        {
            _observerList.ForEach(observer => observer.OnNext(value));
        }

        public void OnError(Exception error)
        {
            _observerList.ForEach(observer => observer.OnError(error));
            Dispose();
        }

        public void OnCompleted()
        {
            _observerList.ForEach(observer => observer.OnCompleted());
            Dispose();
        }

        #endregion

        #region IObservable<T> Implementation

        /// <summary>
        /// イベント購読
        /// </summary>
        /// <param name="onNext"></param>
        /// <param name="onError"></param>
        /// <param name="onComplete"></param>
        /// <returns></returns>
        public IDisposable Subscribe(Action<T> onNext, Action<Exception> onError = null, Action onComplete = null)
        {
            var observer = new EventObserver<T>(onNext, onError, onComplete);
            return Subscribe(observer);
        }

        /// <summary>
        /// イベント購読
        /// </summary>
        /// <param name="observer"></param>
        /// <returns></returns>
        public IDisposable Subscribe(IObserver<T> observer)
        {
            lock (_lock)
            {
                if (_isDisposed)
                {
                    observer.OnCompleted();
                    return EmptyDisposable.Singleton;
                }

                _observerList.Add(observer);

                return new Subscription(this, observer);
            }
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            lock (_lock)
            {
                _isDisposed = true;
                _observerList.Clear();
            }
        }

        #endregion
    }
}
