using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ゲーム内static変数定義：クレーンゲーム
    /// </summary>
    public partial class StaticVariableDefine
    {
        public class CraneGame
        {
            public static readonly Vector3 DEFAULT_BG_PROP_POS = new Vector3(-2.9f, 0.78f, 0.55f);
            
#if CYG_DEBUG
            //デバッグ用
            public static int[] FixOddsIdArray;
            public static int[] DebugCharaIdArray = null;
            public static int DebugWipeCharaId = -1;
            public static int DebugWipeDressId = -1;
            
            public static CraneGameDefines.DebugDress DebugDressType = CraneGameDefines.DebugDress.None;
            public static string DebugFace = null;
#endif
        }
    }
}
