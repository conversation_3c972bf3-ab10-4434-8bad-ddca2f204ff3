using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// ゲーム内static変数定義：収集イベント
    /// </summary>
    public partial class StaticVariableDefine
    {
        public class CollectRaid
        {
            public CollectRaid()
            {
                // static変数のメモリ確保のため生成だけ行う
                new CollectRaidHubViewController();
                new CollectRaidTopView();
                new CollectRaidMissionViewController();
                new PartsCollectRaidEventButtonMiniEntity();
            }
            
            public class CollectRaidHubViewController
            {
                /// <summary>
                /// 子ビュー定義
                /// </summary>
                public static readonly SceneDefine.ViewId[] VIEW_ID_ARRAY =
                {
                    SceneDefine.ViewId.CollectRaidTop,
                    SceneDefine.ViewId.CollectRaidMission,
                };            
            }
            
            public class CollectRaidTopView
            {
                /// <summary> ストーリーボタンnewの位置 </summary>
                public static readonly Vector3 STORY_NEW_ICON_POS = new Vector3(56, -10, 0);
            }
            
            public class CollectRaidMissionViewController
            {
                /// <summary>
                /// 使用するトリガーリスト
                /// </summary>
                public static List<CharacterSystemLotteryTrigger> VOICE_TRIGGER_LIST = new List<CharacterSystemLotteryTrigger>()
                {
                    CharacterSystemLotteryTrigger.CollectRaidMission,
                };
                
                /// <summary>
                /// タブの種類と数
                /// </summary>
                public static readonly int[] MISSION_TAB_ARRAY =
                {
                    (int)GameDefine.MissionType.CollectRaidDaily,
                    (int)GameDefine.MissionType.CollectRaidLimited,
                };
            }
            
            public class CollectRaidRewardButton
            {
                /// <summary> スタンプアニメーションののposition </summary>
                public static readonly Vector3 STAMP_FLASH_POS = new Vector3(-137.8f, 29.1f, 0f);
                public static readonly Vector3 STAMP_FLASH_ROTATION_EULER = new Vector3(0f, 0f, 15f);
            }
            
            public class PartsCollectRaidEventButtonMiniEntity
            {
                public static string BUTTON_ICON_TEXTURE_PATH;
            }

            public class PartsCollectRaidSingleModeResult
            {
                public static readonly Vector2 BITMAP_TEXT_PIVOT = new Vector2(1f, 0.5f);
            }
        }
    }
}
