namespace Gallop
{

    using static TeamStadiumDefine;

    /// <summary>
    /// Resourcesに関するパス定義を集約する。レース以外の定義は、ResourcePathRaceに集約されています。
    /// 文字列定義は可能な限りconstで定義してください。（readonlyは実行時に初期化されるため）
    /// </summary>
    public static partial class ResourcePath
    {
        #region GallopClient
        /// <summary>
        /// クラス報酬ダイアログプレファブパス
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_REWARD_ITEM_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumRewardItemList";

        /// <summary>
        /// デッキ編成変更確認ダイアログ
        /// <see cref="DialogTeamStadiumDeckEditConfirm"/>
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_DECK_EDIT_CONFIRM = UIPartsPath + "/TeamStadium/DialogTeamStadiumDeckEditConfirm";

        // デッキ編成、重複キャラ等の解除確認ダイアログ
        public const string DIALOG_TEAM_STADIUM_CONFIRM_RELEASE_CHARA = UIPartsPath + "/TeamStadium/DialogTeamStadiumConfirmReleaseChara";

        /// <summary>
        /// ランキングプレファブパス
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_RANKING_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumRankingList";

        /// <summary>
        /// ランキング情報ダイアログプレファブパス
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_RANKING_INFO_TEAM_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumRankingInfoTeamList";

        /// <summary>
        /// アイテム一覧ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_ITEM_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumItemList";

        /// <summary>
        /// ハイスコア更新ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_RACE_RESULT_HIGH_SCORE = UIPartsPath + "/TeamStadium/DialogTeamStadiumRaceResultHighScore";

        /// <summary>
        /// ドロップアイテムダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_DROP_ITEM = UIPartsPath + "/TeamStadium/DialogTeamStadiumDropItem";

        /// <summary>
        /// クラス変動ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_CLASS_FLUCTUATION = UIPartsPath + "/TeamStadium/DialogTeamStadiumClassFluctuation";

        /// <summary>
        /// レース中のスコア獲得演出のスコア名＋スコア値プレハブ。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_PLATE_PATH = UIPartsPath + "/Race/TeamStadiumScorePlate";
        /// <summary>
        /// レース中のスコア獲得演出のエフェクト制御。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_EFFECT_PATH = UIPartsPath + "/Race/TeamStadiumScoreEffect";
        /// <summary>
        /// スコア詳細ダイアログの１スコアを表示するプレート。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_DETAIL_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumScoreDetailPlate";
        /// <summary>
        /// スコア詳細ダイアログの１スコア内訳を表示するプレート。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_BREAKDOWN_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumScoreBreakDownPlate";
        /// <summary>
        /// スコア詳細ダイアログの１キャラ毎のスコア詳細プレハブ。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_CHARA_DETAIL_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumCharaScoreDetail";
        /// <summary>
        /// スコア詳細ダイアログのチーム毎のスコア詳細プレハブ。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_TEAM_DETAIL_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumTeamScoreDetail";
        /// <summary>
        /// スコア獲得ダイアログ。
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_SCORE_RESULT = UIPartsPath + "/Race/DialogTeamStadiumScoreResult";
        /// <summary>
        /// スコア詳細ダイアログ。
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_SCORE_DETAIL = UIPartsPath + "/Race/DialogTeamStadiumScoreDetail";
        /// <summary>
        /// スコア詳細のスコア長押しで出るポップアップ。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_DETAIL_POPUP = UIPartsPath + "/Race/PartsTeamStadiumScoreDetailLongTapInfoPop";
        /// <summary>
        /// グランドリザルトの戦績（キャラ毎のスコア獲得一覧）獲得ダイアログ。
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_CHARA_SCORE_RESULT_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumCharaScoreResultList";
        /// <summary>
        /// グランドリザルトの戦績（キャラ毎のスコア獲得一覧）で１キャラを表示するプレート。
        /// </summary>
        public const string TEAM_STADIUM_CHARA_SCORE_RESULT_PLATE = UIPartsPath + "/TeamStadium/PartsTeamStadiumCharaScoreResultPlate";
        /// <summary>
        /// レース情報ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_RACE_INFO = UIPartsPath + "/TeamStadium/DialogTeamStadiumRaceInfo";
        /// <summary>
        /// 着順表ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_RACE_RESULT_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumRaceResultList";
        /// <summary>
        /// チーム評価点更新ダイアログ
        /// </summary>
        public const string DIALOG_UPDATE_TEAM_EVALUATION_POINT = UIPartsPath + "/TeamStadium/DialogUpdateTeamEvaluationPoint";
        /// <summary>
        /// チーム評価ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_EVALUATION_POINT = UIPartsPath + "/TeamStadium/DialogTeamEvaluationPoint";
        /// <summary>
        /// チーム獲得報酬一覧ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_EVALUATION_REWARD_ITEM_LIST = UIPartsPath + "/TeamStadium/DialogTeamEvaluationRewardList";
        /// <summary>
        /// 全レース結果ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_ALL_RACE_RESULT_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumAllRaceResultList";
        /// <summary>
        /// サポカ応援ボーナスダイアログ
        /// </summary>
        public const string DIALOG_TEAM_SUPPORT_BONUS = UIPartsPath + "/TeamStadium/DialogTeamSupportBonus";

        /// <summary>
        /// コンテンツ解放ダイアログ
        /// </summary>
        public const string DIALOG_RELEASE_CONTENT = UIPartsPath + "/TeamStadium/DialogReleaseContent";

        /// <summary>
        /// チーム競技場の全レース終了ダイアログ（拠点の全レース演出後）
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_ALL_RACE_RESULT = UIPartsPath + "/TeamStadium/DialogTeamStadiumAllRaceResult";

        /// <summary>
        /// チーム競技場の出走履歴ダイアログ
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_RACE_HISTORY_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumRaceHistoryList";
        /// <summary>
        /// 全レース結果ダイアログ（出走履歴からの表示）
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_HISTORY_ALL_RACE_RESULT_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumHistoryAllRaceResultList";
        /// <summary>
        /// 着順表ダイアログ（出走履歴からの表示）
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_HISTORY_RACE_RESULT_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumHistoryRaceResultList";
        /// スコア情報ダイアログ（キャラ毎のスコア獲得一覧、出走履歴からの表示）
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_HISTORY_CHARA_SCORE_RESULT_LIST = UIPartsPath + "/TeamStadium/DialogTeamStadiumHistoryCharaScoreResultList";
        /// <summary>
        /// スコア情報ダイアログで１キャラを表示するプレート（出走履歴からの表示）
        /// </summary>
        public const string TEAM_STADIUM_CHARA_HISTORY_SCORE_RESULT_PLATE = UIPartsPath + "/TeamStadium/PartsTeamStadiumHistoryCharaScoreResultPlate";
        /// <summary>
        /// スコア詳細ダイアログ（出走履歴からの表示）
        /// </summary>
        public const string DIALOG_TEAM_STADIUM_HISTORY_SCORE_DETAIL = UIPartsPath + "/Race/DialogTeamStadiumHistoryScoreDetail";
        /// <summary>
        /// スコア詳細ダイアログのチーム毎のスコア詳細プレハブ（出走履歴からの表示）
        /// </summary>
        public const string TEAM_STADIUM_HISTORY_SCORE_TEAM_DETAIL_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumHistoryTeamScoreDetail";
        /// <summary>
        /// スコア詳細ダイアログの１スコアを表示するプレート（出走履歴からの表示）
        /// </summary>
        public const string TEAM_STADIUM_HISTORY_SCORE_DETAIL_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumHistoryScoreDetailPlate";
        /// <summary>
        /// スコア詳細ダイアログの１キャラ毎のスコア詳細プレハブ（出走履歴からの表示）
        /// </summary>
        public const string TEAM_STADIUM_HISTORY_SCORE_CHARA_DETAIL_PLATE_PATH = UIPartsPath + "/Race/PartsTeamStadiumHistoryCharaScoreDetail";
        /// <summary>
        /// スコア詳細のスコア長押しで出るポップアップ（出走履歴からの表示）
        /// </summary>
        public const string TEAM_STADIUM_HISTORY_SCORE_DETAIL_POPUP = UIPartsPath + "/Race/PartsTeamStadiumHistoryScoreDetailLongTapInfoPop";
        #endregion

        #region _GallopResources

        public const string TEAM_STADIUM_RESOURCES_PATH = "Race/TeamStadium";
        private const string TEAM_STADIUM_RANK_FRAME_FORMAT = TEAM_STADIUM_RESOURCES_PATH + "/tex_team_rank_frm_{0:D3}";
        private const string TEAM_STADIUM_RANK_ICON_FORMAT = TEAM_STADIUM_RESOURCES_PATH + "/tex_team_rank_icon_{0:D3}";
        private const string TEAM_STADIUM_RANK_FRAME_SIZE_M_FORMAT = TEAM_STADIUM_RESOURCES_PATH + "/tex_team_rank_frm_m_{0:D3}";
        private const string TEAM_STADIUM_RANK_ICON_SIZE_M_FORMAT = TEAM_STADIUM_RESOURCES_PATH + "/tex_team_rank_icon_m_{0:D3}";

        private const string CLASS_TEXT_SPRITE_FORMAT = "utx_txt_teamrace_class_{0:D2}";
        private const string CLASS_PROMOTION_SPRITE_FORMAT = "utx_txt_teamstadium_class_{0:D2}";
        private const string ROUND_RESULT_SPRITE_FORMAT = "utx_txt_result_{0}_00";
        private const string WINNING_STREAK_COUNT_SPRITE_FORMAT = "utx_txt_winbonus_now_{0:D2}";
        private const string WINNING_STREAK_BONUS_SPRITE_FORMAT = "utx_txt_winbonus_{0:D2}";
        private const string TEAM_FRAME_BOTTOM_LINE_SPRITE_FORMAT = "utx_frm_vs_select_label_{0:D2}_sl";

        private const string TEAM_STADIUM_3DBG_PATH = EffectRoot + "Race/TeamRace/pfb_eff_bg_team_{0}";
        private const string TEAM_STADIUM_3DEFFECT_PATH = EffectRoot + "Race/TeamRace/pfb_eff_bg_team_aula_{0}";
        public enum TeamStadium3DColor { Blue, Red, Glay, Orange }
        private static string[] TEAM_STADIUM_3D_COLOR_ARRAY = new string[] { "b", "r", "g", "o" };

        private const string TEAM_STADIUM_DECIDE_BG_PATH = "Bg/vertical_bg_team_vs_{0:D5}_{1:D2}{2:D1}10";
        private const string TEAM_STADIUM_GRANDRESULT_BG_PATH = "Bg/vertical_bg_team_result_{0:D5}_{1:D2}{2:D1}10";
        private const string TEAM_STADIUM_DECK_BG_PATH = "Bg/bg_{0:D4}_{1:D5}";

        /// <summary> Flashルートパス </summary>
        private const string TEAM_STADIUM_FLASH_ROOT = FLASH_ROOT + "TeamStadium/";
        /// <summary> 枠番確定Flashパス </summary>
        public const string TEAM_STADIUM_MOTIVATION_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_motivation00";
        /// <summary> レース終了Flashパス </summary>
        public const string TEAM_STADIUM_RACE_FINISH_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_txt_finishrace00";
        /// <summary> レース結果カットインUIパス </summary>
        public const string TEAM_STADIUM_RESULT_CUTIN_UI_PATH = "UI/Parts/TeamStadium/TeamStadiumResultCutinUI";
        /// <summary> レース結果カットインFlashパス </summary>
        public const string TEAM_STADIUM_RACE_RESULT_CUTIN_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_result_cutin00";
        /// <summary> レース結果カットイン背景Flashパス </summary>
        public const string TEAM_STADIUM_RACE_RESULT_CUTIN_BG_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_result_cutin_base00";
        /// <summary>ネームプレートFlashパス </summary>
        public const string TEAM_STADIUM_NAMEPLATE_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_trainer_name{0:D2}";
        /// <summary>レース拠点VSFlashパス </summary>
        public const string TEAM_STADIUM_RACE_LIST_VS_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_vs00";
        /// <summary>対戦相手アイテムFlashパス </summary>
        public const string TEAM_STADIUM_OPPONENT_ITEM_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_select_opponent_panel00";
        /// <summary>チーム評価点エンブレムFlashパス </summary>
        public const string TEAM_STADIUM_TEAM_EVALUATION_EMBLEM_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_evalution_emblem00";
        /// <summary>チーム評価点ゲージFlashパス </summary>
        public const string TEAM_STADIUM_TEAM_EVALUATION_GAUGE_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_evalution_gauge00";
        /// <summary>チーム評価点ランクFlashパス </summary>
        public const string TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_evalution_rank00";
        /// <summary>チーム評価点テキストFlashパス </summary>
        public const string TEAM_STADIUM_TEAM_EVALUATION_TEXT_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_evalution_text00";
        /// <summary> チーム競技場背景アニメーション </summary>
        public const string TEAM_STADIUM_TOP_BACKGROUND_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_top_bg000";
        /// <summary> ウイニング報酬確定アイコンFlashパス </summary>
        public const string TEAM_STADIUM_WIN_CONFIRM_ICON_FLASH_PATH = TEAM_STADIUM_FLASH_ROOT + "pf_fl_team_icon_winconfirm00";

        /// <summary> FlashActionルートパス </summary>
        public const string TEAM_STADIUM_FLASH_ACTION_ROOT = FLASH_COMBINE_ROOT + "Action/TeamStadium/";
        /// <summary> クラス変動FlashActionパス </summary>
        public const string TEAM_STADIUM_CLASS_CHANGE_FLASH_ACTION_PATH = TEAM_STADIUM_FLASH_ACTION_ROOT + "fa_team_txt_change_class00";
        /// <summary> ウイニング報酬テキストFlashパス </summary>
        public const string TEAM_STADIUM_WINNING_REWARD_TEXT_ACTION_FLASH_PATH = TEAM_STADIUM_FLASH_ACTION_ROOT + "fa_team_txt_winning_reward00";
        /// <summary> 最高チーム評価点更新FlashActionパス </summary>
        public const string TEAM_STADIUM_TEAM_EVALUATION_RANK_FLASH_ACTION_PATH = TEAM_STADIUM_FLASH_ACTION_ROOT + "fa_team_evalution_rank00";
        /// <summary> チーム編成の編成枠解放FlashActionパス </summary>
        public const string TEAM_STADIUM_UNLOCK_DECK_EDIT_FLASH_ACTION_PATH = TEAM_STADIUM_FLASH_ACTION_ROOT + "fa_team_icon_unlock00";
        /// <summary> ウイニング報酬ボックスカウンターFlashActionパス </summary>
        public const string TEAM_STADIUM_WIN_BOX_COUNT_FLASH_ACTION_PATH = TEAM_STADIUM_FLASH_ACTION_ROOT + "fa_team_box_count00";
        /// <summary> 連勝ボーナスFlashActionパス </summary>
        public const string TEAM_STADIUM_WIN_COUNT_BONUS_FLASH_ACTION_PATH = TEAM_STADIUM_FLASH_ACTION_ROOT + "fa_team_win_count_bonus00";

        /// <summary> FlashTimelineルートパス </summary>
        public const string TEAM_STADIUM_FLASH_TIMELINE_ROOT = FLASH_COMBINE_ROOT + "Timeline/TeamStadium/";
        /// <summary> ハイスコアFlashTimelineパス </summary>
        public const string TEAM_STADIUM_HIGH_SCORE_FLASH_TIMELINE_PATH = TEAM_STADIUM_FLASH_TIMELINE_ROOT + "tat_team_break_record00";
        /// <summary> 総合スコアFlashTimelineパス </summary>
        public const string TEAM_STADIUM_TOTAL_SCORE_FLASH_TIMELINE_PATH = TEAM_STADIUM_FLASH_TIMELINE_ROOT + "tat_team_eff_totalscore00";
        /// <summary> ウイニング報酬FlashTimelineパス </summary>
        public const string TEAM_STADIUM_WINNING_REWARD_FLASH_TIMELINE_PATH = TEAM_STADIUM_FLASH_TIMELINE_ROOT + "tat_team_winning_reward{0:D2}";

        /// <summary> チーム評価点パーティクルエフェクトパス </summary>
        public const string TEAM_STADIUM_TEAM_EVALUATION_GLITTER_PARTICLE_PATH = UI_EFFECT_ROOT +  "TeamStadium/" + "pfb_uieff_team_glitter_particle_00";


        /// <summary> チーム競技場UIエフェクトパス </summary>
        public const string TEAM_STADIUM_UI_EFFECT_ROOT = UI_EFFECT_ROOT + "TeamStadium/";
        /// <summary> チーム競技場TOPパーティクル </summary>
        public const string TEAM_STADIUM_TOP_PARTICLE = TEAM_STADIUM_UI_EFFECT_ROOT + "pfb_uieff_team_top_particle_00";
        /// <summary> チーム競技場TOPパーティクルマテリアル </summary>
        public const string TEAM_STADIUM_TOP_PARTICLE_MATERIAL = TEAM_STADIUM_UI_EFFECT_ROOT + "Materials/uieff_mat_TeamStadium_top_{0:D2}";
        /// <summary> 拠点画面でスコアが吸い込まれる時のパーティクル </summary>
        public const string TEAM_STADIUM_SCORE_TRAIL_EFFECT_PATH = TEAM_STADIUM_UI_EFFECT_ROOT + "pfb_uieff_team_score_trail_00";


        /// <summary> チーム競技場枠番背景画像パス </summary>
        public const string TEAM_STADIUM_BRACKET_PATH = TEAM_STADIUM_FLASH_ROOT + "Texture/utx_frm_bracket_long_00";

        ///<summary> チーム競技場フォントパス </summary>
        public const string TEAM_STADIUM_FONT_PATH = TEAM_STADIUM_RESOURCES_PATH + "/Font/";

        /// <summary>
        /// パス取得：チームランク用フレーム画像
        /// </summary>
        /// <param name="group"></param>
        /// <returns></returns>
        public static string GetTeamRankFrame(TeamRankGroup group)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(TEAM_STADIUM_RANK_FRAME_FORMAT, (int)group);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：チームランク用アイコン画像
        /// </summary>
        /// <param name="rank"></param>
        /// <returns></returns>
        public static string GetTeamRankIcon(TeamRank rank)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(TEAM_STADIUM_RANK_ICON_FORMAT, (int)rank);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：Mサイズチームランク用フレーム画像
        /// </summary>
        /// <param name="group"></param>
        /// <returns></returns>
        public static string GetTeamRankFrameSizeM(TeamRankGroup group)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(TEAM_STADIUM_RANK_FRAME_SIZE_M_FORMAT, (int)group);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：Mサイズチームランク用アイコン画像
        /// </summary>
        /// <param name="rank"></param>
        /// <returns></returns>
        public static string GetTeamRankIconSizeM(TeamRank rank)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(TEAM_STADIUM_RANK_ICON_SIZE_M_FORMAT, (int)rank);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：クラステキストスプライト
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        public static string GetClassTextSpritePath(int classId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CLASS_TEXT_SPRITE_FORMAT, classId);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：クラス昇級スプライト
        /// </summary>
        /// <param name="promotionType"></param>
        /// <returns></returns>
        public static string GetClassPromotionPath(int promotionType)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(CLASS_PROMOTION_SPRITE_FORMAT, promotionType);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：チームフレーム下部の帯スプライト
        /// </summary>
        /// <param name="group"></param>
        /// <returns></returns>
        public static string GetTeamFrameBottomLinePath(TeamRankGroup group)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(TEAM_FRAME_BOTTOM_LINE_SPRITE_FORMAT, (int)group);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：ラウンド勝敗スプライト
        /// </summary>
        /// <param name="resultType"></param>
        /// <param name="small"></param>
        /// <returns></returns>
        public static string GetRoundResultSpritetPath(RoundResultType resultType)
        {
            _stringBuilder.Length = 0;
            switch (resultType)
            {
                case RoundResultType.Win:
                    _stringBuilder.AppendFormat(ROUND_RESULT_SPRITE_FORMAT, "s_win");
                    break;
                case RoundResultType.Lose:
                    _stringBuilder.AppendFormat(ROUND_RESULT_SPRITE_FORMAT, "s_lose");
                    break;
                case RoundResultType.Draw:
                    _stringBuilder.AppendFormat(ROUND_RESULT_SPRITE_FORMAT, "s_draw");
                    break;
            }

            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：ウィニング報酬演出アニメーション
        /// </summary>
        /// <param name="winningRewardBoxType"></param>
        /// <returns></returns>
        public static string GetWinningRewardAnimationPath(WinningRewardBoxType winningRewardBoxType)
        {
            _stringBuilder.Length = 0;
            switch (winningRewardBoxType)
            {
                case WinningRewardBoxType.Rare:
                    _stringBuilder.AppendFormat(TEAM_STADIUM_WINNING_REWARD_FLASH_TIMELINE_PATH, 1);
                    break;
                case WinningRewardBoxType.Normal:
                    _stringBuilder.AppendFormat(TEAM_STADIUM_WINNING_REWARD_FLASH_TIMELINE_PATH, 0);
                    break;
            }
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// スコア獲得ダイアログのロゴの背景（競馬場スタンド）。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_RESULT_LOGO_BG_PATH = "Race/TeamStadium/Bg/teamrace_result_01_02_dialog";
        /// <summary>
        /// スコア獲得ダイアログのロゴのクラス表記。
        /// </summary>
        private const string TEAM_STADIUM_SCORE_RESULT_LOGO_CLASS_PATH = "Race/TeamStadium/Logo/tex_race_teamstadium_{0:00}";
        /// <summary>
        /// スコア獲得ダイアログのロゴの「チーム競技場」表記。
        /// </summary>
        public const string TEAM_STADIUM_SCORE_RESULT_LOGO_TITLE_PATH = "Race/TeamStadium/Logo/tex_race_teamstadium_txt_{0:00}";

        /// <summary>
        /// チーム競技場スコア獲得ダイアログのCLASSロゴテクスチャパス。
        /// </summary>
        public static string GetTeamStadiumScoreResultLogoClassPath(int teamClass)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(TEAM_STADIUM_SCORE_RESULT_LOGO_CLASS_PATH, teamClass);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チーム競技場タイトル「チーム競技場」テクスチャパス。
        /// </summary>
        public static string GetTeamStadiumScoreResultLogoTitlePath(int teamClass)
        {
            var masterTeamStadiumClass = MasterDataManager.Instance.masterTeamStadiumClass.Get(teamClass);
            if (masterTeamStadiumClass != null)
            {
                _stringBuilder.Length = 0;
                _stringBuilder.AppendFormat(TEAM_STADIUM_SCORE_RESULT_LOGO_TITLE_PATH, masterTeamStadiumClass.UnitMaxNum - 1);
                return _stringBuilder.ToString();
            }
            return null;
        }

        /// <summary>
        /// チーム競技場のスコア獲得演出のスコア名テクスチャパス。
        /// </summary>
        public static string GetTeamStadiumScoreNameTexturePath(int scoreNameId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append($"Race/TeamStadium/tex_race_tr_score_000_{scoreNameId:D3}");
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チーム競技場のスコア獲得演出のスコア名テクスチャパス。
        /// </summary>
        public static string GetTeamStadiumTeamScoreNameTexturePath(int rawScoreId)
        {
            _stringBuilder.Length = 0;
            _stringBuilder.Append($"Race/TeamStadium/teamstadium_race_teambonus_{rawScoreId:D4}");
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// チーム競技場レースタイトルフレームテクスチャパス。
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        public static string GetTeamStadiumRaceTitleFrameTexturePath(int classId)
        {
            _stringBuilder.Length = 0;
            const int offset = 8;
            return _stringBuilder.AppendFormat(RACE_TITLE_FRAME_PATH, classId + offset).ToString();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="classId"></param>
        /// <returns></returns>
        public static string GetScenarioTeamRaceTitleFrameTexturePath(int classId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TEAM_RACE_TITLE_FRAME_PATH, classId).ToString();
        }

        /// <summary>
        /// チーム競技場3D背景のパス
        /// </summary>
        /// <returns></returns>
        public static string GetTeamStadium3DBg(TeamStadium3DColor color)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TEAM_STADIUM_3DBG_PATH, TEAM_STADIUM_3D_COLOR_ARRAY[(int)color]).ToString();
        }

        /// <summary>
        /// チーム競技場3Dエフェクトのパス
        /// </summary>
        /// <returns></returns>
        public static string GetTeamStadium3DEffect(TeamStadium3DColor color)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TEAM_STADIUM_3DEFFECT_PATH, TEAM_STADIUM_3D_COLOR_ARRAY[(int)color]).ToString();
        }

        /// <summary>
        /// チームの背景（確定画面）
        /// </summary>
        /// <param name="bgId"></param>
        /// <param name="season"></param>
        /// <param name="weather"></param>
        /// <returns></returns>
        public static string GetTeamStadiumDecideBgPath( int bgId, int season, int weather )
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TEAM_STADIUM_DECIDE_BG_PATH, bgId, season, weather).ToString();
        }


        /// <summary>
        /// チームの背景（グランドリザルト）
        /// </summary>
        /// <param name="bgId"></param>
        /// <param name="season"></param>
        /// <param name="weather"></param>
        /// <returns></returns>
        public static string GetTeamStadiumGrandResultBgPath(int bgId, int season, int weather)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TEAM_STADIUM_GRANDRESULT_BG_PATH, bgId, season, weather).ToString();
        }

        /// <summary>
        /// チームの背景（チーム編成）
        /// </summary>
        /// <param name="bgId"></param>
        /// <param name="subId"></param>
        /// <returns></returns>
        public static string GetTeamStadiumDeckBgPath(int bgId, int subId)
        {
            _stringBuilder.Length = 0;
            return _stringBuilder.AppendFormat(TEAM_STADIUM_DECK_BG_PATH, bgId, subId).ToString();
        }

        /// <summary>
        /// パス取得：連勝カウントスプライト
        /// </summary>
        /// <param name="winningStreak">連勝数</param>
        /// <returns></returns>
        public static string GetTeamStadiumWinningStreakCountSpritePath(int winningStreak)
        {
            // 連勝カウントは連勝数が2（2連勝）から開始する。
            var index = winningStreak - 2;
            if (index < 0)
            {
                return string.Empty;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(WINNING_STREAK_COUNT_SPRITE_FORMAT, index);
            return _stringBuilder.ToString();
        }

        /// <summary>
        /// パス取得：連勝ボーナススプライト
        /// </summary>
        /// <param name="winningStreak">連勝数</param>
        /// <returns></returns>
        public static string GetTeamStadiumWinningStreakBonusSpritePath(int winningStreak)
        {
            // 連勝ボーナスは連勝数が2（2連勝）から開始する。
            var index = winningStreak - 2;
            if (index < 0)
            {
                return string.Empty;
            }
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat(WINNING_STREAK_BONUS_SPRITE_FORMAT, index);
            return _stringBuilder.ToString();
        }
        #endregion
    }
}
