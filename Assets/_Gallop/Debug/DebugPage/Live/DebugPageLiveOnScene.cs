#if CYG_DEBUG
using System.Collections.Generic;
using Gallop.Live;

namespace Gallop
{
    /// <summary>
    /// Live中のデバコマ
    /// </summary>
    [DebugPage(typeof(DebugPageTopMenuRoot))]
    public class DebugPageLiveOnScene : DebugPageButtonMenuBase
    {
        /// <summary>
        /// 初期メニュー
        /// </summary>
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
            {"スキップ", new DebugButtonDetail(SkipLive) },
            {"一時停止操作On", new DebugButtonDetail(EnablePause)},
            {"一時停止操作Off", new DebugButtonDetail(DisablePause)},
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }

        // =====================================================
        //  ボタン押下時コールバック
        // =====================================================
        private static void SkipLive()
        {
            if (Director.Instance == null)
                return;

            Director.Instance.IsFinished = true;
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.StopSong();
                Director.StopAllFlashSe();
            }
        }

        private static void EnablePause()
        {
            SetEnablePause(true);
        }

        private static void DisablePause()
        {
            SetEnablePause(false);
        }

        private static void SetEnablePause(bool enable)
        {
            if (Director.Instance == null)
                return;
            var view = UIManager.MainCanvas.GetComponentInChildren<LiveView>();
            if (view != null)
            {
                view.IsEnabledPause = enable;
            }
        }
    }
}
#endif