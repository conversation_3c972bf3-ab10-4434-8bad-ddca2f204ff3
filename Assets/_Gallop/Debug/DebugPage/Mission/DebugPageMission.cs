#if CYG_DEBUG

using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// ミッションデバッグページ
    /// </summary>
    [DebugPageAttribute(typeof(DebugPageTopMenuGame))]
    public class DebugPageMission : DebugPageBase
    {
        /// <summary>
        /// キャンペーンロゴ表示のデバッグ
        /// </summary>
        public enum CampaignLogoDebugType
        { 
            None,           // デバッグOFF
            RichOnly,       // リッチ画像のみ
            SimpleOnly,     // 汎用画像＋テキストのみ
            RichAndTerm,    // リッチ画像＋期間表示
            SimpleAndTerm,  // 汎用画像＋期間表示
        }

        private static readonly string[] CampaignLogoDebugTexts = new string[] 
            { 
                "デバッグOFF", 
                "リッチ画像のみ", "汎用のみ", 
                "リッチ画像＋期間表示", "汎用＋期間表示",
            };


        public static CampaignLogoDebugType CampaignLogoDebug { get; private set; } = CampaignLogoDebugType.None;

        private GameObject _root = null;


        protected override void BuildMenu()
        {
            var factory = _parentController.UIPartsFactory;
            _root = DebugPageContentFactory.AddVerticalLayoutRoot(_debugPageRoot, 10);

            // 期間限定ミッションのロゴ表示パターンを強制的に切り替える
            {
                var dropDown = _parentController.UIPartsFactory.AddDebugDropdown(_root, 0, CampaignLogoDebugTexts, 
                    selected => 
                    {
                        CampaignLogoDebug = (CampaignLogoDebugType)selected;
                    });

                // 表示が小さいので引き延ばし
                var rectTransform = dropDown.GetComponent<RectTransform>();
                rectTransform.pivot = new Vector2(0, 1);
                rectTransform.localScale = new Vector3(1.5f, 1.5f, 1);
            }
        }
    }

    public static class CampaignLogoDebugTypeExtensions
    {
        /// <summary>
        /// デバッグが有効か
        /// </summary>
        /// <param name="self"></param>
        /// <returns></returns>
        public static bool Enable(this DebugPageMission.CampaignLogoDebugType self) =>
            self != DebugPageMission.CampaignLogoDebugType.None;

        /// <summary>
        /// リッチ画像を使用するか
        /// </summary>
        public static bool UseRich(this DebugPageMission.CampaignLogoDebugType self) =>
            self == DebugPageMission.CampaignLogoDebugType.RichOnly || self == DebugPageMission.CampaignLogoDebugType.RichAndTerm;

        /// <summary>
        /// 期間表示を使用するか
        /// </summary>
        public static bool UseTerm(this DebugPageMission.CampaignLogoDebugType self) =>
            self == DebugPageMission.CampaignLogoDebugType.RichAndTerm || self == DebugPageMission.CampaignLogoDebugType.SimpleAndTerm;
    }
}

#endif