#if CYG_DEBUG
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// アオハル対戦開始演出用デバコマ
    /// </summary>
    [DebugPage(typeof(DebugPageSingleModeScenarioTeamRace))]
    public sealed class DebugPageSingleModeTeamRaceListView : DebugPageButtonMenuBase
    {
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
            {"レース短縮演出再生",new DebugButtonDetail(PlayRaceCutin,DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"タップスキップ無効時間調整", new DebugButtonDetail(() => 
            {
                 DebugManager.Instance.DebugPageController.OpenPage<DebugPageSingleModeTeamRaceListSubView>();
            }, DebugPageContentFactory.DebugButtonType.HierarchicalButton) },
            {"ゴルシに対して10000回カット抽選ロジック",new DebugButtonDetail(() => 
            {
                const int COUNT= 10000;
                int[] TargetIdArray = new int[] { 2, 4, 11, 14, 16, 20, 33 };

               for(int i = 0; i < COUNT; i++)
                {
                    var data = SingleModeScenarioTeamRaceUtils.GetTeamRaceResultCutinWinTypePattern(3, new int[] { 100701, 100701, 100701 }, new int[] { 1007, 1007, 1007 });

                    if(TargetIdArray.Any(x => x == data.cutPattern) == false)
                    {
                        Debug.LogWarning("抽選対象外のIdがあった, id ==  " + data.cutPattern);
                    }
                }

            },DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }

        /// <summary>
        /// 演出再生
        /// </summary>
        private static void PlayRaceCutin()
        {
            if (SceneManager.Instance.GetCurrentViewController().GetViewId() != SceneDefine.ViewId.SingleModeScenarioTeamRaceRaceList)
            {
                return;
            }

            SingleModeScenarioTeamRaceRaceListViewController viewController = SceneManager.Instance.GetCurrentViewController<SingleModeScenarioTeamRaceRaceListViewController>();

            viewController.DebugPlaySkipRaceAnimation();
        }
    }

    [DebugPage(typeof(DebugPageSingleModeTeamRaceListView))]
    public sealed class DebugPageSingleModeTeamRaceListSubView : DebugPageButtonMenuBase
    {
        private GameObject _verticalLayoutRoot;

        private Text _nowDelay = null;

        private InputFieldCommon _inputField = null;

        public override bool IsEnableTouchGameElement()
        {
            return false;
        }

        protected override void BuildMenu()
        {
            
            DebugPageContentFactory factory = _parentController.UIPartsFactory;

            // 縦並びになるLayoutGroup
            _verticalLayoutRoot = DebugPageContentFactory.AddVerticalLayoutRoot(_debugPageRoot, 10.0f);

            var instructionsTextObject = factory.AddTextLabel(_verticalLayoutRoot).GetComponent<Text>();
            instructionsTextObject.text =
                "タップスキップ無効時間を入力してください\n" +
                "確定ボタンで適用されます（0以下の数値は無効になります）\n";

            if (_nowDelay == null)
            {
                _nowDelay = factory.AddTextLabel(_verticalLayoutRoot).GetComponent<Text>();
            }

            _nowDelay.text = $"現在設定されている無効時間: {SingleModeScenarioTeamRaceRaceListViewController.DEBUG_CAN_SKIP_CUTIN_PLAY_DELAY}秒";

            if (_inputField == null)
            {
                var horizontal = DebugPageContentFactory.AddHorizontalLayoutRoot(_verticalLayoutRoot, 10f);

                var inputField = factory.AddInputFieldWithLabel(horizontal, "タップスキップ無効時間").GetComponentInChildren<InputFieldCommon>();

                _inputField = inputField;
                _inputField.SetDebugMenuInputType();
                _inputField.ChangeTextFlag = false;
            }

            factory.AddDebugButtonWithLayoutElement(_verticalLayoutRoot, "適用", () =>
            {
                if(string.IsNullOrEmpty(_inputField.text))
                {
                    return;
                }

                if(float.TryParse(_inputField.text, out var value))
                {
                    SingleModeScenarioTeamRaceRaceListViewController.DebugUpdateTapSkipDelay(value);

                    _nowDelay.text = $"現在設定されている無効時間: {SingleModeScenarioTeamRaceRaceListViewController.DEBUG_CAN_SKIP_CUTIN_PLAY_DELAY}秒";
                }

            });
        }
    }
}

#endif