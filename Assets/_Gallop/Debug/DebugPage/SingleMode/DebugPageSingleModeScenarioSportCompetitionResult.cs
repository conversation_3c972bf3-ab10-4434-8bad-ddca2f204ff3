#if CYG_DEBUG
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    [DebugPage(typeof(DebugPageSingleModeScenarioSport))]
    public class DebugPageSingleModeScenarioSportCompetitionResult : DebugPageButtonMenuBase
    {
        private VerticalLayoutGroup _verticalLayout;
        private Dropdown _dressDropDown;

        private int _playerCharaId;
        private int _playerDressId;

        private GameObject _playerRoot;

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return new Dictionary<string, DebugButtonDetail>
                {
                    {
                        "キャラモデルを更新", new DebugButtonDetail(DebugOverrideCharaModel)
                    },
                };
        }
        
        public override bool IsEnableTouchGameElement() => false;

        protected override void BuildMenu()
        {
            base.BuildMenu();

            _verticalLayout = DebugPageContentFactory.AddVerticalLayoutRoot(_debugPageRoot, 10.0f).GetComponent<VerticalLayoutGroup>();
            _verticalLayout.padding.left = 350;

            _playerRoot = DebugPageContentFactory.AddVerticalLayoutRoot(_verticalLayout.gameObject, 10.0f);
            _playerRoot.transform.SetParent(_verticalLayout.gameObject.transform);
            
            AddButton("スペシャルウィーク", 1001);
            AddButton("ニシノフラワー", 1051);
            AddButton("ヒシアケボノ", 1028);
            
            CreateText(_playerRoot, "その他のキャラモデル設定");
            CreateCharaDropDown(_playerRoot, selectedCharaId =>
            {
                _playerCharaId = selectedCharaId;
                // キャラに対応する衣装リストでドロップダウン作成
                if (_dressDropDown != null)
                {
                    GameObject.Destroy(_dressDropDown.gameObject);
                }
                _dressDropDown = CreateDressDropDown(_playerRoot, _playerCharaId, selectedDressId => { _playerDressId = selectedDressId; });
            });

            void AddButton(string text, int charaId)
            {
                _parentController.UIPartsFactory.AddDebugButtonWithLayoutElement(_playerRoot, text, () =>
                {
                    _playerCharaId = charaId;
                    _playerDressId = SingleModeScenarioArcUtils.GetRivalDressIdByCharaId(_playerCharaId);
                    DebugOverrideCharaModel();
                });
            }
        }
        private static bool IsInCompetitionResult()
        {
            return SceneManager.Instance.GetCurrentViewController() is
                SingleModeScenarioSportCompetitionResultViewController;
        }

        /// <summary>
        /// デバッグ用に大会結果画面キャラモデル強制更新
        /// </summary>
        private void DebugOverrideCharaModel()
        {
            // 大会結果画面にいないなら何もしない
            if (!IsInCompetitionResult())
            {
                UIManager.Instance.ShowNotification("このコマンドは大会リザルト画面でのみ使用可能です");
                return;
            }

            var register = DownloadManager.GetNewRegister();
            //ボイス
            AudioManager.Instance.RegisterDownloadByCharaIds(register, new List<int> { _playerCharaId }, CharacterSystemTextGroupExtension.Scene.Team);
            DownloadManager.Instance.FixDownloadList(ref register);
            //ダウンロード終了待ち
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                var viewController = SceneManager.Instance.GetCurrentViewController() as SingleModeScenarioSportCompetitionResultViewController;
                //開発用途なのでCardIdは無効値を渡して良い
                viewController?.DebugOverrideCharaModel(GameDefine.INVALID_CARD_ID, _playerCharaId, _playerDressId);
            });
        }

        #region デバッグメニュー
        
        /// <summary>
        /// キャラ選択ドロップダウン作成
        /// </summary>
        private Dropdown CreateCharaDropDown(GameObject root, Action<int/*CharaId*/> onSelectChara)
        {
            var charaMasterQuery = MasterDataManager.Instance.masterCharaData.dictionary.Values.Where(c => c.CharaCategory == 0);
            var charaNameArray = charaMasterQuery.Select(x => x.Name).ToArray();
            var charaIdArray = charaMasterQuery.Select(x => x.Id).ToArray();
            
            return CreateDropDown(root, charaNameArray, index =>
            {
                if (index < 0 || index > charaIdArray.Length) return;
                onSelectChara?.Invoke(charaIdArray[index]);
            });
        }

        /// <summary>
        /// 衣装選択ドロップダウン生成
        /// </summary>
        private Dropdown CreateDressDropDown(GameObject root, int charaId,  Action<int/*DressId*/> onSelectDress)
        {
            var dressMasterQuery = MasterDataManager.Instance.masterDressData.dictionary.Values.Where(x => (x.CharaId == 0 || x.CharaId == charaId) && x.UseRace == 1);
            var dressNameArray = dressMasterQuery.Select(x => x.Name).ToArray();
            var dressIdArray = dressMasterQuery.Select(x => x.Id).ToArray();

            return CreateDropDown(root, dressNameArray, index =>
            {
                if (index < 0 || index > dressIdArray.Length) return;
                onSelectDress?.Invoke(dressIdArray[index]);
            });
        }
        
        /// <summary>
        /// Text作成
        /// </summary>
        private Text CreateText(GameObject root, string message)
        {
            var text = _parentController.UIPartsFactory.AddTextLabel(root).GetComponent<Text>();
            text.text = message;
            return text;
        }

        /// <summary>
        /// DropDown作成
        /// </summary>
        private Dropdown CreateDropDown(GameObject root, string[] options, Action<int/*option index*/> onValueChanged)
        {
            var charaObj = _parentController.UIPartsFactory.AddDebugDropdown(root, 0, options, i => onValueChanged?.Invoke(i));
            charaObj.GetComponent<LayoutElement>().minWidth = 600;
            var dropdown = charaObj.GetComponent<Dropdown>();
            dropdown.RefreshShownValue();
            onValueChanged?.Invoke(0); // 初期値でいちどコールバックを呼んでおく

            return dropdown;
        }
        
        #endregion
    }
}
#endif
