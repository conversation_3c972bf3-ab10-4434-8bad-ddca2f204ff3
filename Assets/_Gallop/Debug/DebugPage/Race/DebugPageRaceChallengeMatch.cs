#if CYG_DEBUG
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// レース中のチャレンジマッチ関連デバッグ機能
    /// </summary>
    [DebugPage(typeof(DebugPageRaceTop))]
    public class DebugPageRaceChallengeMatch : DebugPageButtonMenuBase
    {
        // 右側デバッグ表示消す
        public override bool IsResidentTextEnable() { return false; }

        // 左上ボタンリスト
        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return new Dictionary<string, DebugButtonDetail>();
        }

        private static readonly Vector2 BUTTON_SIZE = new Vector2(200.0f, 65.0f);
        private static readonly Vector2 DROPDOWN_SIZE = new Vector2(400, 80);

        private HorizontalLayoutGroup _horizontalLayout;
        private int[] _rawPointIdArray;
        private int _selectRawPointId;

        protected override void BuildMenu()
        {
            base.BuildMenu();

            var rootLayoutObj = DebugPageContentFactory.AddVerticalLayoutRoot(_debugPageRoot);
            var rootLayout = rootLayoutObj.GetComponent<VerticalLayoutGroup>();
            rootLayout.childAlignment = TextAnchor.UpperLeft;
            rootLayout.childControlWidth = false;
            rootLayout.childControlHeight = false;
            rootLayout.childForceExpandWidth = false;
            rootLayout.childForceExpandHeight = false;
            rootLayout.padding.top = 100;

            { 
                _horizontalLayout = AddHorizontalLayoutGroup(rootLayoutObj).GetComponent<HorizontalLayoutGroup>();
                _horizontalLayout.GetComponent<RectTransform>().offsetMax = new Vector2(0, 100);

                var allRawPoint = MasterDataManager.Instance.masterChallengeMatchRawPoint.dictionary.Values
                    .Where(x => x.RacePointNameId != 0);
                var pointNameArray = allRawPoint
                    .Select(x => x.PointName)
                    .ToArray();
                _rawPointIdArray = allRawPoint
                    .Select(x => x.Id)
                    .ToArray();
                var pointObj = _parentController.UIPartsFactory.AddDebugDropdown(_horizontalLayout.gameObject, 0, pointNameArray, OnValueChanged_RawPoint);
                pointObj.GetComponent<RectTransform>().sizeDelta = DROPDOWN_SIZE;
                pointObj.GetComponent<Dropdown>().RefreshShownValue();

                _selectRawPointId = _rawPointIdArray.Length > 0 ? _rawPointIdArray[0] : 0;
            }
            
            // 再生ボタン生成。
            {
                var button = AddButton(rootLayoutObj, "選択中再生", PlaySelect).GetComponent<ButtonCommon>();
                button.interactable = true;

                button = AddButton(rootLayoutObj, "ランダム再生", PlayRandom).GetComponent<ButtonCommon>();
                button.interactable = true;
            }
        }

        /// <summary>
        /// 選択中のidを再生。
        /// </summary>
        private void PlaySelect()
        {
            PlayById(_selectRawPointId);
        }

        /// <summary>
        /// idランダムで再生。
        /// </summary>
        private void PlayRandom()
        {
            int index = UnityEngine.Random.Range(0, _rawPointIdArray.Length);
            int id = _rawPointIdArray[index];
            PlayById(id);
        }

        /// <summary>
        /// id指定でポイント獲得再生。
        /// </summary>
        private void PlayById(int rawPointId)
        {
            var masterRawPoint = MasterDataManager.Instance.masterChallengeMatchRawPoint.Get(rawPointId);
            if (masterRawPoint == null)
            {
                return;
            }
            RaceManager.Instance.RaceMainView.PlayTeamStadiumScore(masterRawPoint.Id, masterRawPoint.Point, 0, true);
        }

        /// <summary>
        /// id切り替え
        /// </summary>
        private void OnValueChanged_RawPoint(int index)
        {
            if (index < 0 || index > _rawPointIdArray.Length)
            {
                return;
            }

            _selectRawPointId = _rawPointIdArray[index];
        }

        private static GameObject AddHorizontalLayoutGroup(GameObject parent)
        {
            var layoutObj = DebugPageContentFactory.AddHorizontalLayoutRoot(parent);
            var layout = layoutObj.GetComponent<HorizontalLayoutGroup>();
            layout.childAlignment = TextAnchor.MiddleLeft;
            layout.childControlWidth = false;
            layout.childControlHeight = false;
            layout.childForceExpandWidth = false;
            layout.childForceExpandHeight = false;
            layoutObj.GetComponent<RectTransform>().sizeDelta = BUTTON_SIZE;
            return layoutObj;
        }

        private GameObject AddButton(GameObject parent, string name, UnityAction action)
        {
            var obj = _parentController.UIPartsFactory.AddDebugButton(parent, name, action);
            var buttonTrans = obj.GetComponent<RectTransform>();
            buttonTrans.anchorMin = new Vector2( 0, 1 );
            buttonTrans.anchorMax = new Vector2( 0, 1 );
            buttonTrans.sizeDelta = BUTTON_SIZE;

            return obj;
        }
    }
}
#endif
