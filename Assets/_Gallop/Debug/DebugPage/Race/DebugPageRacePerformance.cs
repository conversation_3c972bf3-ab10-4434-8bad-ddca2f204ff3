#if CYG_DEBUG
using System;
using System.Collections;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// レースメモリ・負荷計測関連デバッグページ
    /// </summary>
    [DebugPage(typeof(DebugPageRaceTop))]
    public class DebugPageRacePerformance : DebugPageButtonMenuBase
    {
        /// <summary> 計測対象のレース起動モード </summary>
        /// <remarks>
        /// もし今後レース起動モードごとにメモリを計測したいということがあれば変更できるようにする
        /// 現状、必要なさそうなので固定
        /// </remarks>
        public const RaceMainView.Mode BootMode = RaceMainView.Mode.Normal;
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
            {"情報", new DebugButtonDetail(() => { DebugManager.Instance.DebugPageController.OpenPage<DebugPageRacePerformanceInfo>(); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton) },
            { "メモリ:東京:縦", new DebugButtonDetail(() => RaceDebugger.SetupMemoryCheck(RaceDebugger.MemoryCheckAssetType.Tokyo_00, false)) },
            { "メモリ:中山:縦", new DebugButtonDetail(() => RaceDebugger.SetupMemoryCheck(RaceDebugger.MemoryCheckAssetType.Nakayama_00, false)) },
            { "メモリ:京都:縦", new DebugButtonDetail(() => RaceDebugger.SetupMemoryCheck(RaceDebugger.MemoryCheckAssetType.Kyoto_00, false)) },
            
            { "競馬場リソース解析ON", new DebugButtonDetail(() => RaceDebugger.IsAnalyzeCourseBgResource = true) },
            { "競馬場リソース解析OFF", new DebugButtonDetail(() => RaceDebugger.IsAnalyzeCourseBgResource = false) },
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }
    }
}
#endif