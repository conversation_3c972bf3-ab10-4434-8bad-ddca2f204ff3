#if CYG_DEBUG
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Gallop
{
    [DebugPage(typeof(DebugPageRaceJikkyo))]
    public class DebugPageRaceJikkyoPlay : DebugPageButtonMenuBase
    {
        // 右側デバッグ表示消す
        public override bool IsResidentTextEnable() { return false; }

        // 左上ボタンリスト
        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return new Dictionary<string, DebugButtonDetail>();
        }

        private static readonly Vector2 CellSize = new Vector2(200.0f, 65.0f);
        private static readonly Vector2 HalfButtonSize = new Vector2(100.0f, 65.0f);
        private static readonly Vector2 HalfButtonImageSize = new Vector2(104.0f, 88.0f);

        private static readonly string[] CheckModeStrings = { "Jikkyo", "Comment", "Base" };
        private static readonly string[] OutputModeStrings = { "実況履歴", "実況情報", "レース情報", "省略タグ", "呼び済みタグ", "キューシート" };

        private enum CheckMode
        {
            Message = 0,
            Comment,
            Base,
        }
        private CheckMode _checkMode = CheckMode.Message;

        private enum OutputMode
        {
            History = 0,
            Info,
            Race,
            OmitTag,
            CalledTag,
            CueSheet,
            Max,
        }
        private OutputMode _outputMode = OutputMode.History;

        private InputFieldCommon _inputField;
        private ButtonCommon _playT1Button;
        private Text _outputText;

        private int[] _targetIds; // 各再生モードでのID

        private void Init()
        {
            _targetIds = new int[EnumUtil.GetEnumElementCount<CheckMode>()];
            for (int i = 0, count = _targetIds.Length; i < count; ++i)
            {
                _targetIds[i] = 1;
            }
        }

        protected override void BuildMenu()
        {
            Init();

            base.BuildMenu();

            // 左下UIのルート
            var rootLayoutObj = DebugPageContentFactory.AddVerticalLayoutRoot(_debugPageRoot);
            var rootLayout = rootLayoutObj.GetComponent<VerticalLayoutGroup>();
            rootLayout.childAlignment = TextAnchor.LowerLeft;
            rootLayout.childControlWidth = false;
            rootLayout.childControlHeight = false;
            rootLayout.childForceExpandWidth = false;
            rootLayout.childForceExpandHeight = false;
            rootLayout.padding.bottom = 64;

            { // ID入力・再生ボタン
                var layoutObj = AddHorizontalLayoutGroup(rootLayoutObj, 0);

                _inputField = _parentController.UIPartsFactory.AddInputField(layoutObj).GetComponent<InputFieldCommon>();
                _inputField.transform.localScale = Math.VECTOR3_ONE;
                _inputField.GetComponent<RectTransform>().sizeDelta = CellSize;
                _inputField.ChangeTextFlag = false;
                _inputField.onValueChanged.AddListener(OnValueChanged_InputField);

                // DebugInputFieldのTextのcolorがデフォルト白になっていて見えないため、一括で黒に設定する。
                var textArray = _inputField.GetComponentsInChildren<Text>();
                foreach (var text in textArray)
                {
                    text.color = Color.black;
                }

                _playT1Button = AddButton(layoutObj, "再生", Play).GetComponent<ButtonCommon>();
                _playT1Button.interactable = true;
            }

            {
                var layoutObj = AddHorizontalLayoutGroup(rootLayoutObj, 1);

                AddHalfButton(layoutObj, "-1", () => { AddValue(-1); });
                AddHalfButton(layoutObj, "+1", () => { AddValue( 1); });
            }

            {
                var layoutObj = AddHorizontalLayoutGroup(rootLayoutObj, 2);

                AddHalfButton(layoutObj, "-10", () => { AddValue(-10); });
                AddHalfButton(layoutObj, "+10", () => { AddValue( 10); });
            }

            {
                var layoutObj = AddHorizontalLayoutGroup(rootLayoutObj, 3);

                AddHalfButton(layoutObj, "-100", () => { AddValue(-100); });
                AddHalfButton(layoutObj, "+100", () => { AddValue( 100); });
            }

            {
                var layoutObj = AddHorizontalLayoutGroup(rootLayoutObj, 4);

                AddButton(layoutObj, "レース停止・再開", PauseOrResume);
            }

            { // 実況・解説・Baseの切り替え
                var layoutObj = AddHorizontalLayoutGroup(rootLayoutObj, 5);

                var checkModeObj = _parentController.UIPartsFactory.AddDebugDropdown(layoutObj, (int) _checkMode, CheckModeStrings, OnValueChanged_CheckMode);
                checkModeObj.GetComponent<RectTransform>().sizeDelta = CellSize;
                checkModeObj.GetComponent<Dropdown>().RefreshShownValue();
            }

            { // テキスト出力
                var rootObj = DebugPageContentFactory.AddEmptyUIGameObject(_debugPageRoot, "DebugPageRoot");
                var rootRectTransform = rootObj.GetComponent<RectTransform>();
                rootRectTransform.anchorMin = Vector2.zero;
                rootRectTransform.anchorMax = Vector2.one;
                rootRectTransform.anchoredPosition = new Vector2(100.0f, 17.5f);
                rootRectTransform.sizeDelta = new Vector2(-200.0f, -165.0f);

                var image = rootObj.AddComponent<Image>();
                image.color = new Color(0.0f, 0.0f, 0.0f, 0.5f);

                var textObj = _parentController.UIPartsFactory.AddTextLabel(rootObj);
                _outputText = textObj.GetComponent<Text>();
                _outputText.fontSize = 22;

                var outputTypeObj = _parentController.UIPartsFactory.AddDebugDropdown(rootObj, (int)_outputMode, OutputModeStrings, OnValueChanged_OutputMode);
                var outputTypeRectTransform = outputTypeObj.GetComponent<RectTransform>();
                outputTypeRectTransform.anchorMin = Vector2.one;
                outputTypeRectTransform.anchorMax = Vector2.one;
                outputTypeRectTransform.anchoredPosition = new Vector2(-100.0f, 32.5f);
                outputTypeRectTransform.sizeDelta = CellSize;
                outputTypeObj.GetComponent<Dropdown>().RefreshShownValue();
            }
        }

        public override void UpdateMenu()
        {
            // テキスト出力
            var text = string.Empty;
            switch (_outputMode)
            {
                case OutputMode.History:
                    text = JikkyoDebugger.GetJikkyoHistory();
                    break;
                case OutputMode.Info:
                    text = JikkyoDebugger.DrawDebug();
                    break;
                case OutputMode.Race:
                    text = JikkyoDebugger.DrawDebugRace();
                    break;
                case OutputMode.OmitTag:
                    text = JikkyoDebugger.DrawDebugOmitTag();
                    break;
                case OutputMode.CalledTag:
                    text = JikkyoDebugger.DrawDebugCalledTag();
                    break;
                case OutputMode.CueSheet:
                    text = JikkyoDebugger.DrawDebugCueSheet();
                    break;
            }

            if (text != _outputText.text)
            {
                _outputText.text = text;
            }
        }

        /// <summary>
        /// 任意テンションでの再生
        /// </summary>
        private void Play()
        {
            var jikkyo = JikkyoDebugger.CurJikkyo;
            if (jikkyo == null)
            {
                return;
            }

            jikkyo.ClearDisplay();
            jikkyo.DbgClearReserve();

            int id = _targetIds[(int)_checkMode];

            switch (_checkMode)
            {
                case CheckMode.Message:
                    JikkyoDebugger.DebugPlayMessage(id);
                    break;
                case CheckMode.Comment:
                    JikkyoDebugger.DebugPlayComment(id);
                    break;
                case CheckMode.Base:
                    JikkyoDebugger.AddJikkyouReserve(id);
                    break;
            }
        }
        
        /// <summary>
        /// 再生するIDへの加算
        /// </summary>
        private void AddValue(int value)
        {
            _targetIds[(int)_checkMode] = Mathf.Max(_targetIds[(int)_checkMode] + value, 0);
            OnValueChanged_InputField(_targetIds[(int)_checkMode].ToString());
        }

        /// <summary>
        /// 一時停止・再開
        /// </summary>
        private static void PauseOrResume()
        {
            if (!RaceManager.HasInstance() || !RaceManager.Instance.IsInitialized) { return; }

            var raceManager = RaceManager.Instance;
            if (raceManager.IsPaused())
            {
                raceManager.ResumeRace();

                // 実況の選定を再開
                JikkyoDebugger.IsJikkyoExec = true;
            }
            else
            {
                raceManager.PauseRace();

                // 実況の選定を止める
                JikkyoDebugger.IsJikkyoExec = false;
            }
            // 実況側は強制的にポーズ解除
            raceManager.Jikkyo.Pause(false);
        }

        /// <summary>
        /// 再生モード切り替え
        /// </summary>
        private void OnValueChanged_CheckMode(int index)
        {
            if (index < 0 || index > (int)CheckMode.Base) { return; }
            if (index == (int)_checkMode) { return; }

            _checkMode = (CheckMode)index;
            OnValueChanged_InputField(_targetIds[(int)_checkMode].ToString());
        }

        /// <summary>
        /// テキスト出力モード切り替え
        /// </summary>
        private void OnValueChanged_OutputMode(int index)
        {
            if (index < 0 || index > (int)OutputMode.Max) { return; }
            if (index == (int)_outputMode) { return; }

            _outputMode = (OutputMode)index;
        }

        /// <summary>
        /// ID入力
        /// </summary>
        private void OnValueChanged_InputField(string changeText)
        {
            string inputText = GallopUtil.GetModifiedString(changeText, _inputField);
            if (int.TryParse(inputText, out var value))
            {
                // 数値更新
                _inputField.text = value.ToString();
                _targetIds[(int) _checkMode] = value;
            }
            else if (!string.IsNullOrEmpty(inputText))
            {
                // 余計な文字が入ったので上書き
                _inputField.text = _targetIds[(int)_checkMode].ToString();
            }
        }

#region UIパーツ追加
        private static GameObject AddHorizontalLayoutGroup(GameObject parent, int verticalIndex)
        {
            var layoutObj = DebugPageContentFactory.AddHorizontalLayoutRoot(parent);
            var layout = layoutObj.GetComponent<HorizontalLayoutGroup>();
            layout.childAlignment = TextAnchor.MiddleLeft;
            layout.childControlWidth = false;
            layout.childControlHeight = false;
            layout.childForceExpandWidth = false;
            layout.childForceExpandHeight = false;
            layout.padding.bottom = 130 * verticalIndex;
            return layoutObj;
        }

        private GameObject AddButton(GameObject parent, string name, UnityAction action)
        {
            var obj = _parentController.UIPartsFactory.AddDebugButton(parent, name, action);
            obj.GetComponent<RectTransform>().sizeDelta = CellSize;
            obj.GetComponent<ButtonCommon>().transition = Selectable.Transition.ColorTint;

            return obj;
        }

        private GameObject AddHalfButton(GameObject parent, string name, UnityAction action)
        {
            var obj = _parentController.UIPartsFactory.AddDebugButton(parent, name, action);
            obj.GetComponent<RectTransform>().sizeDelta = HalfButtonSize;
            obj.GetComponent<ButtonCommon>().transition = Selectable.Transition.ColorTint;

            return obj;
        }
#endregion UIパーツ追加

    }
}
#endif
