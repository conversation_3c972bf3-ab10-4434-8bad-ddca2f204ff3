#if CYG_DEBUG
using System.Collections.Generic;
using System.Text;

namespace Gallop
{
    [DebugPage(typeof(DebugPageTopMenuGame))]
    public class DebugPageiCloud : DebugPageButtonMenuBase
    {
        #region ボタン情報
        
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
#if IS_ADHOC && UNITY_IOS
            { "iCloud情報取得", new DebugButtonDetail(() => UpdateICloudInfo()) },
#if CYG_DEBUG
            { "iCloudユーザー情報削除", new DebugButtonDetail(() => iCloudController.RemoveiCloudUser()) },
#endif
#endif
            { 
                "ON/OFF確認済みフラグリセット", 
                new DebugButtonDetail(() => {
                    SaveDataManager.Instance.SaveLoader.IsNotifiediCloudDisable = false;
                    SaveDataManager.Instance.SaveLoader.IsConfirmiCloudBackupUserOverwrite = false;
                })
            },

            { "ダイアログ:iCloud無効確認" , new DebugButtonDetail(()=>{DialogNotifyiCloudDisable.Open((d) => { });}) },

            {
                "ダイアログ:バックアップ上書き確認" ,
                new DebugButtonDetail(() =>
                    {
                        DialogConfirmiCloudBackupUserOverwrite.Open(
                            Certification.ViewerId.ToString("#,0").Replace(",", " "),
                            "新米トレーナー",
                            OnAccept,
                            (d) => { }
                        );
                    }) 
            },
            
            {
                "ダイアログ:バックアップ引き継ぎ確認" ,
                new DebugButtonDetail(() =>
                {
                    DialogCheckDataTransitionFromiCloud.Open(
                        Certification.ViewerId.ToString("#,0").Replace(",", " "),
                        "新米トレーナー",
                        () => {},
                        () => {},
                        () => {}
                    );
                }) 
            },
            
            {
                "サポート・バックアップ設定確認" ,
                new DebugButtonDetail(() =>
                {
                    DialogConfirmiCloudBackupUserOverwrite.Open(
                        Certification.ViewerId.ToString("#,0").Replace(",", " "),
                        "新米トレーナー",
                        OnAccept,
                        (d) => { },
                        true
                    );
                }) 
            },
            
            {
                "サポート・バックアップ済み" ,
                new DebugButtonDetail(() =>
                {
                    DialogNotifyiCloudAlreadyBackup.Open();
                }) 
            },
            
            {
                "サポート・バックアップ無効" ,
                new DebugButtonDetail(() =>
                {
                    DialogNotifyiCloudDisable.Open(null, true);
                }) 
            },
        };
        
        #endregion

#if IS_ADHOC && UNITY_IOS
        public static string _residentText = "情報を表示するには、「iCloud情報取得」ボタンを押してください。";
        public static StringBuilder _residentTextBuilder = new StringBuilder();
#else
        public static string _residentText = "iCloud機能はAdhocビルドされたiOS端末以外では使用できません。";
#endif

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }
        
        private static void OnAccept(DialogCommon dialog)
        {
            dialog.AddDestroyCallBack(
                () =>
                {
                    DialogiCloudBackupComplete.Open((d) => { });
                }); 
        }
        
        // -----------
        // ResidentText
        // -----------
        public override bool IsResidentTextEnable() { return true; }
        public override string OverrideResidentText()
        {
            return _residentText;
        }
        
#if IS_ADHOC && UNITY_IOS
        // iCloud情報取得
        public static void UpdateICloudInfo()
        {
            _residentTextBuilder.Clear();

            bool isICloudAvaiable = iCloudController.IsiCloudAvailable();
            if (!isICloudAvaiable)
            {
                _residentTextBuilder.AppendLine("iCloud設定 : 無効");
                _residentText = _residentTextBuilder.ToString();
                return;
            }

            _residentTextBuilder.AppendLine("iCloud設定 : 有効");

            var iCloudUser = iCloudController.GetiCloudUser();
            _residentTextBuilder.AppendLine($"iCloudUser : {iCloudUser}");

            _residentText = _residentTextBuilder.ToString();
        }
#endif
    }
}

#endif