#if CYG_DEBUG
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Gallop
{
    /// <summary>
    /// トップページ
    /// </summary>
    [DebugPage(typeof(DebugPageTopMenuGame))]
    public class DebugPageMiniCharaTop : DebugPageButtonMenuBase
    {
        private const SceneDefine.ViewId CIRCLE = SceneDefine.ViewId.Circle;

        /// <summary>
        /// 初期メニュー
        /// </summary>
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
            {"デバッグ表示切替",  new DebugButtonDetail(SwitchDebugEnable, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"モーション再抽選",  new DebugButtonDetail(RequestCommandAll, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"表示ON/OFF",  new DebugButtonDetail(SwitchVisible, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"自キャラ変更",  new DebugButtonDetail(ChangeUserChara, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"キャラ追加",  new DebugButtonDetail(AddChara, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"キャラ削除",  new DebugButtonDetail(RemoveChara, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"モブ頭部調整", new DebugButtonDetail(OpenMobScaleDebug, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
            {"Canvas表示", new DebugButtonDetail(SwitchCanvasVisible, DebugPageContentFactory.DebugButtonType.FunctionalButton)},
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }

        //モーション再抽選
        private static void RequestCommandAll()
        {
            var director = GetDirector();
            if (director == null || director.CharaManager == null || director.State == MiniDirectorDefines.DirectorState.NonInitialized)
                return;

            director.CharaManager.RequestCurrentBgCharaCommand();
        }

        //デバッグ表示切替
        private static void SwitchDebugEnable()
        {
            MiniDirector.IsDebugMode = !MiniDirector.IsDebugMode;
            SetDebugEnable(MiniDirector.IsDebugMode);
        }

        //自キャラ変更
        private static void ChangeUserChara()
        {
            //画面ごとにキャラのデータ構造が異なるので処理分ける
            var viewId = SceneManager.Instance.GetCurrentViewId();

            //サークル
            if (viewId == CIRCLE)
            {
                var userData = CircleInternalDataManager.Instance.DirectorMemberList.FirstOrDefault(m => m.ViewerId == Certification.ViewerId);
                if (userData == null)
                    return;

                //適当にキャラ変えてみる
                userData.CharaId = userData.CharaId == 1001 ? 1002 : 1001;
            }
        }

        //キャラ追加
        private static void AddChara()
        {
            //演出クラスの取得
            var director = GetDirector();
            if (director == null)
                return;

            //既にいっぱいいるなら何もしない
            var currentCharaList = director.CharaManager.DebugGetCurrentBgCharaList();
            if (currentCharaList.Count == MiniDirectorDefines.CHARA_MAX_BY_BG)
                return;

            //画面ごとにキャラのデータ構造が異なるので処理分ける
            var viewId = SceneManager.Instance.GetCurrentViewId();

            //サークル
            if (viewId == CIRCLE)
            {
                if (CircleInternalDataManager.Instance.DirectorMemberList.Count == MiniDirectorDefines.CHARA_MAX)
                    return;

                //適当にキャラ追加
                var dummyId = UnityEngine.Random.Range(0, int.MaxValue);
                var dummy = CircleInternalDataManager.CreateDummyData(CircleInternalDataManager.Instance.DirectorMemberList.First(), new CircleDirectorTest.ModelData(1001, (int)ModelLoader.DressID.UniformSummer, new List<int>((int)ModelLoader.DressID.UniformSummer)), dummyId);
                CircleInternalDataManager.Instance.MemberDic.Add(dummyId, dummy);
                CircleInternalDataManager.Instance.CreateDirectorMemberList(false);
            }
        }

        //キャラ削除
        private static void RemoveChara()
        {
            //演出クラスの取得
            var director = GetDirector();
            if (director == null)
                return;

            //画面ごとにキャラのデータ構造が異なるので処理分ける
            var viewId = SceneManager.Instance.GetCurrentViewId();

            //現在背景から優先して除去
            var currentCharaList = director.CharaManager.DebugGetCurrentBgCharaList();
            long targetId = 0;

            //サークル
            if (viewId == CIRCLE)
            {
                //サークル
                if (CircleInternalDataManager.Instance.DirectorMemberList.Count <= 1)
                    return;

                //適当にキャラ削除
                for (int i = currentCharaList.Count - 1; i >= 0; i--)
                {
                    targetId = (int)currentCharaList[i].CharaData.UniqueId;
                    if (targetId == Certification.ViewerId)
                        continue;
                    else
                        break;  //ケツから探して一番最初に見つかった自分以外のIDを削除する
                }

                if (targetId == 0 || targetId == Certification.ViewerId)
                    return;

                CircleInternalDataManager.Instance.MemberDic.Remove(targetId);
                CircleInternalDataManager.Instance.CreateDirectorMemberList(false);
            }
        }

        private static void OpenMobScaleDebug()
        {
            if (DebugMiniHeadScale.Instance != null)
            {
                Object.Destroy(DebugMiniHeadScale.Instance.gameObject);
                return;
            }

            DebugMiniHeadScale.Create();
        }

        private static void SwitchCanvasVisible()
        {
            var canvas = UIManager.GameCanvas;
            if (canvas == null)
                return;

            var active = canvas.gameObject.activeSelf;
            canvas.gameObject.SetActive(!active);
        }

        /// <summary>
        /// デバッグ設定
        /// </summary>
        /// <param name="enable"></param>
        public static void SetDebugEnable(bool enable)
        {
            var director = GetDirector();
            if (director == null)
                return;

            director.SetDebugEnable(enable);
        }

        /// <summary>
        /// 表示非表示
        /// </summary>
        private static void SwitchVisible()
        {
            var dir = GetDirector();
            if (dir == null)
                return;

            _visible = !_visible;
            dir.SetVisible(_visible);
        }
        private static bool _visible = true;

        private static MiniDirector GetDirector()
        {
            return GameObject.FindObjectOfType<MiniDirector>();
        }
    }
}

#endif