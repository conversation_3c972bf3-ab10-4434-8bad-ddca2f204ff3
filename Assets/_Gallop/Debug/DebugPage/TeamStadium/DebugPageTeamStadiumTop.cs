#if CYG_DEBUG
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace Gallop
{
    /// <summary>
    /// トップページ
    /// </summary>
    [DebugPage(typeof(DebugPageTopMenuGame))]
    public sealed class DebugPageTeamStadiumTop : DebugPageButtonMenuBase
    {
        /// <summary>
        /// 総合結果の勝敗上書き。
        /// </summary>
        public enum GrandResultType
        {
            Null,
            Win,
            Lose,
            Draw,
            HighScore,
        }

        public static TeamStadiumDefine.RoundResultType CutinRoundResultType = TeamStadiumDefine.RoundResultType.Win;

        /// <summary>
        /// 初期メニュー
        /// </summary>
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
            {"勝ちカット再生",  new DebugButtonDetail(() => { CutinRoundResultType = TeamStadiumDefine.RoundResultType.Win;  DebugManager.Instance.DebugPageController.OpenPage<DebugPageTeamStadiumCutin>(); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"負けカット再生",  new DebugButtonDetail(() => { CutinRoundResultType = TeamStadiumDefine.RoundResultType.Lose;  DebugManager.Instance.DebugPageController.OpenPage<DebugPageTeamStadiumCutin>(); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"引き分けカット再生",  new DebugButtonDetail(() => { CutinRoundResultType = TeamStadiumDefine.RoundResultType.Draw;  DebugManager.Instance.DebugPageController.OpenPage<DebugPageTeamStadiumCutin>(); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            
            {"総合勝ち", new DebugButtonDetail(() => { SetGrandResultTypeAndOpenCharaPage(GrandResultType.Win); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton, ButtonOrder.Footer)},
            {"総合負け", new DebugButtonDetail(() => { SetGrandResultTypeAndOpenCharaPage(GrandResultType.Lose); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton, ButtonOrder.Footer)},
            {"総合引き分け", new DebugButtonDetail(() => { SetGrandResultTypeAndOpenCharaPage(GrandResultType.Draw); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton, ButtonOrder.Footer)},
            {"総合ハイスコア", new DebugButtonDetail(() => { SetGrandResultTypeAndOpenCharaPage(GrandResultType.HighScore); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton, ButtonOrder.Footer)},
        };


        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }

        /// <summary>
        /// 総合結果の上書き。GrandResultType.Null以外なら上書きされる。
        /// </summary>
        public static GrandResultType GrandResult { get; private set; } = GrandResultType.Null;

        /// <summary>
        /// 総合結果ボイスの再生要求。
        /// </summary>
        public static bool IsReqGrandResultVoice { get; set; }

        /// <summary>
        /// 開始時に5エースからボイス再生するキャラを抽選する時、このキャラが選ばれる。
        /// </summary>
        public static WorkTrainedCharaData.TrainedCharaData StartVoiceTrainedChara { get; private set; }

        /// <summary>
        /// 開始時ボイスの再生要求。
        /// </summary>
        public static bool IsReqStartVoice { get; set; }

        /// <summary>
        /// 前回のReBuildMenuで追加したボイス再生キャラ指定ボタン名のリスト。
        /// </summary>
        private static List<string> _addButtonNameList = new List<string>(5);
        
        protected override void ReBuildMenu()
        {
            base.ReBuildMenu();
            
            // 前回追加したボタンを一旦全削除。
            foreach (var buttonName in _addButtonNameList)
            {
                RemoveButton(buttonName);
            }
            
            // 開始時のボイス選択ボタンは編成されたエースによってキャラ名異なるので、動的に追加する必要がある。
            AddNewButtonAceTrainedCharaRaceStart();
        }

        /// <summary>
        /// 開始時のボイス再生キャラ選択ボタンを追加。
        /// </summary>
        private void AddNewButtonAceTrainedCharaRaceStart()
        {
            var aceTrainedCharaList = GetAceTrainedCharaList();
            for(int i = 0; i < aceTrainedCharaList.Count; ++i)
            {
                var trainedChara = aceTrainedCharaList[i];
                var addVoiceCharaIdList = DebugPageTeamStadiumAddVoice.CreateAddVoiceCharaIdList(trainedChara.CharaId);
                bool hasAddVoiceChara = addVoiceCharaIdList.Count > 0;
                
                var buttonName = "開始ボイス:\n" + trainedChara.Name;
                var buttonObj = AddNewButton(
                    buttonName,
                    () =>
                    {
                        StartVoiceTrainedChara = trainedChara;
                        if (hasAddVoiceChara)
                        {
                            // 掛け合いボイスが存在するキャラは掛け合い対象を選ぶページを開く。
                            DebugManager.Instance.DebugPageController.OpenPage<DebugPageTeamStadiumAddVoice>();
                        }
                        else
                        {
                            // 掛け合いボイスが存在しない場合、このキャラだけで直ちにボイス再生リクエスト。
                            IsReqStartVoice = true;
                        }
                    },
                    hasAddVoiceChara 
                        ? DebugPageContentFactory.DebugButtonType.HierarchicalButton // 掛け合いボイスが存在するキャラは白ボタン。
                        : DebugPageContentFactory.DebugButtonType.FunctionalButton,
                    ButtonOrder.Main);
                var buttonRect = buttonObj.transform as RectTransform;
                buttonRect.sizeDelta = new Vector2(320, 90);
                
                _addButtonNameList.Add(buttonName);
            }
        }

        /// <summary>
        /// エースに設定された育成キャラデータ取得。失敗したら空リスト。
        /// </summary>
        /// <returns></returns>
        public static List<WorkTrainedCharaData.TrainedCharaData> GetAceTrainedCharaList()
        {
            var status = WorkDataManager.Instance.TeamStadiumData.TeamStadiumStatus;
            if (status == null)
            {
                return new List<WorkTrainedCharaData.TrainedCharaData>();
            }
            
            var myDeck = status.MyDeckInfo;
            if (myDeck == null)
            {
                return new List<WorkTrainedCharaData.TrainedCharaData>();
            }

            return myDeck.GetMemberList()
                .Where(x => x.TrainedCharaData != null && x.IsAce)
                .Select(x => x.TrainedCharaData)
                .ToList();
        }
        
        /// <summary>
        /// 総合結果の上書き設定し、ボイス再生キャラ選択ページを開く。
        /// </summary>
        private static void SetGrandResultTypeAndOpenCharaPage(GrandResultType type)
        {
            GrandResult = type;

            // TeamStadiumAllRaceEndInfoが作られている場合は、FinalWinTypeも直接上書きする。
            var allRaceEndInfo = WorkDataManager.Instance.TeamStadiumData.TeamStadiumAllRaceEndInfo;
            if (allRaceEndInfo != null)
            {
                switch (GrandResult)
                {
                    case GrandResultType.Win: allRaceEndInfo.DbgSetFinalWinType((int)TeamStadiumDefine.RoundResultType.Win); break;
                    case GrandResultType.Lose: allRaceEndInfo.DbgSetFinalWinType((int)TeamStadiumDefine.RoundResultType.Lose); break;
                    case GrandResultType.Draw: allRaceEndInfo.DbgSetFinalWinType((int)TeamStadiumDefine.RoundResultType.Draw); break;
                }
            }

            DebugManager.Instance.DebugPageController.OpenPage<DebugPageTeamStadiumGrandResultChara>();
        }
    }

    /// <summary>
    /// 会話のページを飛ばすボタンを表示する
    /// </summary>
    [DebugPageAttribute(typeof(DebugPageTeamStadiumTop))]
    public class DebugPageTeamStadiumCutin : DebugPageButtonMenuBase
    {
        /// <summary>
        /// 初期メニュー
        /// </summary>
        private readonly Dictionary<string, DebugButtonDetail> _defaultMenuList = new Dictionary<string, DebugButtonDetail>()
        {
            {"1回戦",  new DebugButtonDetail(() => { PlayCutin( 1 ); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"2回戦",  new DebugButtonDetail(() => { PlayCutin( 2 ); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"3回戦",  new DebugButtonDetail(() => { PlayCutin( 3 ); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"4回戦",  new DebugButtonDetail(() => { PlayCutin( 4 ); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
            {"5回戦",  new DebugButtonDetail(() => { PlayCutin( 5 ); }, DebugPageContentFactory.DebugButtonType.HierarchicalButton)},
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return _defaultMenuList;
        }

        // =====================================================
        //  ボタン押下時コールバック
        // =====================================================

        /// <summary>
        /// シングル開始：デバッグ表示切替
        /// </summary>
        private static void PlayCutin(int round)
        {
            if( SceneManager.Instance.GetCurrentViewId() != SceneDefine.ViewId.TeamStadiumRaceList )
            {
                return;
            }

            TeamStadiumRaceListViewController viewController = SceneManager.Instance.GetCurrentViewController<TeamStadiumRaceListViewController>();
            
            // 再生したい条件を設定
            viewController.DebugCutinPlayResultType = DebugPageTeamStadiumTop.CutinRoundResultType;

            // 指定のラウンドを再生
            viewController.PlayCutin(round, null);
        }
    }
}
#endif
