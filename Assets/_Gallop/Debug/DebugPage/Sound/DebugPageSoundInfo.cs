#if CYG_DEBUG
using System.Collections;
using System.Collections.Generic;
using CriWare;
using CriWare.CriMana;
using UnityEngine;

namespace Gallop
{
    [DebugPageAttribute(typeof(DebugPageTopMenuSound))]
    public class DebugPageSoundInfo : DebugPageLabelBase 
    {
        public override bool IsResidentTextEnable() { return false; }
        public override bool IsEnableTouchGameElement() { return true; }
        
        private System.Text.StringBuilder _stringBuilder = new System.Text.StringBuilder(512);

        protected override void BuildMenu()
        {
            var textObj = _parentController.UIPartsFactory.AddTextLabelWithImageRug(_debugPageRoot);
            _text = textObj.GetComponentInChildren<UnityEngine.UI.Text>();
            _text.fontSize = 30;
        }

        public override void UpdateMenu()
        {
            _text.text = BuildText();
        }

        private string BuildText()
        {
            return BuildTextAtomInfo();
        }
        
        public string BuildTextAtomInfo()
        {
            _stringBuilder.Length = 0;
            _stringBuilder.AppendFormat("出力チャンネル数 : {0}\n", CriAtomPlugin.GetOutputChannels());
            _stringBuilder.AppendFormat("サンプリングレート : {0}\n", CriAtomPlugin.GetOutputSamplingRate());
            _stringBuilder.AppendLine("");
            var atomCpuInfo = CriAtomPlugin.GetCpuUsage();
            _stringBuilder.AppendLine( "【CPU情報】");
            _stringBuilder.AppendFormat("   Average: {0}\n", atomCpuInfo.average);
            _stringBuilder.AppendFormat("   Last: {0}\n", atomCpuInfo.last);
            _stringBuilder.AppendFormat("   Peak: {0}\n", atomCpuInfo.peak);

            return _stringBuilder.ToString();
        }
    }
}
#endif
