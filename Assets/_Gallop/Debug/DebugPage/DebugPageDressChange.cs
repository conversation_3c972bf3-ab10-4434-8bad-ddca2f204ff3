#if CYG_DEBUG
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// 衣装変更用デバコマ(拡張性が低かったらどこかと統合する)
    /// </summary>
    [DebugPage(typeof(DebugPageTopMenuGame))]
    public class DebugPageDressChange : DebugPageButtonMenuBase
    {
        /// <summary> 衣装変更モードを強制解除するかどうか </summary>
        public static bool IsForceCancelDressChange { get; private set; } = false;

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList()
        {
            return new Dictionary<string, DebugButtonDetail>()
            {
                {
                    $"衣装変更機能強制解除(現在の設定 : {IsForceCancelDressChange})", new DebugButtonDetail(ChangeForceCancelDressChangeSetting)
                },
            };
        }

        /// <summary>
        /// 衣装変更機能強制OFF切り替え
        /// </summary>
        private void ChangeForceCancelDressChangeSetting()
        {
            IsForceCancelDressChange = !IsForceCancelDressChange;
            // ボタンの表示テキストを更新する
            BuildMenu();
        }

    }
}
#endif // CYG_DEBUG //