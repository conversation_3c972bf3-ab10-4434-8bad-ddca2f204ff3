Shader "Debug/Unlit/TransparentColorProcedural"
{
    Properties
    {
        _Color("Main Color", Color) = (1,1,1,1)
    }

    SubShader
    {
        Tags { "Queue" = "Transparent" "RenderType" = "Transparent" }
        LOD 100

        //ZTest Off
        Blend SrcAlpha OneMinusSrcAlpha

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "../../../Resources/Shader/Common/ShaderCommon.hlsl"

            StructuredBuffer<float3> InputArray;

            struct v2f
            {
                float4 vertex : SV_POSITION;
            };

            CBUFFER_START(UnityPerMaterial)
                uniform float4 _Color;
            CBUFFER_END

            v2f vert (uint id : SV_VertexID)
            {
                v2f o;
                o.vertex = GallopObjectToClipPos(InputArray[id]);
                return o;
            }

            half4 frag (v2f i) : SV_Target
            {
                return _Color;
            }
            ENDHLSL
        }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
    }
}
