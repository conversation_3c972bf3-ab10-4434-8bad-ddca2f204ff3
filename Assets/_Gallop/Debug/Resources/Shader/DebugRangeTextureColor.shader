Shader "Gallop/3D/ShadowMapViewer/RangeTextureColor" 
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _RangeMin ("Range Min", float) = 0.0
        _RangeMax ("Range Max", float) = 1.0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "../../../Resources/Shader/Common/ShaderCommon.hlsl"
            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            float _RangeMin;
            float _RangeMax;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = GallopObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv);
                const float rangeScale = 1 / max(_RangeMax - _RangeMin, 0.0001);
                col = (col - _RangeMin) * rangeScale;
                return col;
            }
            ENDHLSL
        }
    }
}
