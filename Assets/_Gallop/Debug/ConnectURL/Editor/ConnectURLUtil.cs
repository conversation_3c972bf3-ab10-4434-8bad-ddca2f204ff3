#if CYG_DEBUG && UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    /// <summary>
    /// エディタから接続先の設定を参照・更新する機能を提供します
    /// エディタが非再生状態でも扱うことができます
    /// </summary>
    public class ConnectURLUtil
    {
        /// <summary>デバッグ接続先のキャッシュ</summary>
        private static int _currentConnectURLIndex;


        static ConnectURLUtil()
        {
#if UNITY_EDITOR_WIN
            if (!Application.isPlaying)
            {
                LibNative.Runtime.LoadWindowsDll();
            }
#endif
            RefreshCurrentConnectURLIndexFromSaveData();
        }


        /// <summary>
        /// 与えられたインデックスで接続先を更新する
        /// </summary>
        public static void UpdateConnectURLIndex(int newIndex)
        {

            if (!EditorApplication.isPlaying)
            {
                SaveDataManager.CreateInstanceForEditor();
                SaveDataManager.Instance.Load();
            }

            var withoutSoftwareReset = !GameSystem.HasInstance();
            if (newIndex == -1)
            {
                GameDefine.ResetApplicationServerURL(withoutSoftwareReset, false);
            }
            else
            {
                GameDefine.DebugApplicationServerUrl(
                    (GameDefine.DebugServer)(newIndex),
                    withoutSoftwareReset,
                    true,
                    false
                );
            }

            if (!Application.isPlaying)
            {
                SaveDataManager.Instance.TerminateSaveLoader();
                SaveDataManager.DestroyInstance();
            }

            RefreshCurrentConnectURLIndexFromSaveData();
        }

        /// <summary>
        /// 現在の接続先を取得する
        /// </summary>
        public static int GetCurrentConnectURLIndex()
        {
            return _currentConnectURLIndex;
        }

        /// <summary>
        /// 現在の接続先をセーブデータから取得する
        /// </summary>
        private static int GetCurrentConnectURLIndexFromSaveData()
        {
            var connectURLIndex = -1;

            if (!Application.isPlaying)
            {
                SaveDataManager.CreateInstanceForEditor();
                SaveDataManager.Instance.Load();
            }

            // refs #152015 Unity2022にバージョンアップしてからOnGUI等のイベント関数の呼ばれる頻度が上がったからか
            // インスタンス生成前にこの処理が走るようになってしまった。インスタンスを作って対応
            bool isNeedCreateInstance = !SaveDataManager.HasInstance();
            if (isNeedCreateInstance)
            {
                SaveDataManager.CreateInstance();
            }

            if (SaveDataManager.Instance.GetDebugSettingSaveData() is { } debugSaveData)
            {
                connectURLIndex = debugSaveData.ConnectURLIndex;
            }

            // UnityEditorは固有設定を保存
            EditorUniqueDevServerSetting.OverWriterEditorDevServer(ref connectURLIndex);

            if (!Application.isPlaying)
            {
                SaveDataManager.Instance.TerminateSaveLoader();
                SaveDataManager.DestroyInstance();
            }

            // 基本的には通常導線でインスタンスは作ってもらった方が良いので使い終わったらここで破棄しておく
            if (isNeedCreateInstance)
            {
                SaveDataManager.DestroyInstance();
            }

            return connectURLIndex;
        }

        /// <summary>
        /// 現在の接続先を取得してキャッシュする
        /// </summary>
        public static void RefreshCurrentConnectURLIndexFromSaveData()
        {
            _currentConnectURLIndex = GetCurrentConnectURLIndexFromSaveData();
        }
    }
}
#endif