#if UNITY_EDITOR && CYG_DEBUG
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// Homeで使うパス移動するキャラ
    /// </summary>
    public class HomePathWalkerPreview
    {
        #region 変数
        private HomeWalkModelController _chara = null;
        /// <summary>
        /// 歩く速度。m/sec
        /// </summary>
        public float Speed { get => _chara.Speed; set => _chara.Speed = value; }
        private HomeCubicBezierPathAsset _path = null;
        public HomeCubicBezierPathAsset Path { get => _path; set => SetPassAsset(value); }

        /// <summary>
        /// 動いていいか
        /// </summary>
        public bool IsEnableWalk { get; set; } = true;
        /// <summary>
        /// 歩いた距離。
        /// </summary>
        public float WalkLength { get; set; } = 0f;
        #endregion 変数

        #region 初期化
        /// <summary>
        /// 初期化
        /// </summary>
        public void Initialize(HomeWalkModelController chara)
        {
            _chara = chara;
        }
        #endregion 初期化

        #region 更新
        /// <summary>
        /// 更新
        /// </summary>
        public void Update(float dt)
        {
            if (_path == null)
            {
                return;
            }
            if (IsEnableWalk)
            {
                if (_chara.IsFinish)
                {
                    _chara.WalkLength = 0f;
                }
                _chara.AlterUpdate(dt);
            }
            else
            {
                _chara.WalkLength = WalkLength;
                _chara.AlterUpdate(0f);
            }
            _chara.AlterUpdate();
        }
        #endregion 更新

        private void SetPassAsset(HomeCubicBezierPathAsset path)
        {
            _path = path;
            _chara.SetWalkPath(new HomeWalkPath(path));
        }

    }
}
#endif // UNITY_EDITOR && CYG_DEBUG