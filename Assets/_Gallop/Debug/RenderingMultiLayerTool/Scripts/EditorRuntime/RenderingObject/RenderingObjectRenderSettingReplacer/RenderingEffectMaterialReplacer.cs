#if CYG_DEBUG && UNITY_EDITOR
using System.Collections.Generic;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// レンダリング対象のマテリアルを置き換える処理(エフェクト用)
    /// </summary>
    /// <remarks>エフェクトはParticleSystemで、SRPBatcherの対象外なのでMaterialPropertyBlockをそのまま使いまわしている</remarks>
    public class RenderingEffectMaterialReplacer : IRenderingObjectRenderSettingReplacer
    {
        private Dictionary<Renderer, Material> _backupMaterialDic = new Dictionary<Renderer, Material>();
        private Dictionary<Renderer, Material[]> _backupMultiMaterialDic = new Dictionary<Renderer, Material[]>();
        private Dictionary<Renderer, int> _backupRenderQueueDic = new Dictionary<Renderer, int>();
        private Dictionary<Renderer, int[]> _backupMultiRenderQueueDic = new Dictionary<Renderer, int[]>();

        private List<Material> _createdMaterialList = new List<Material>();
        private IRenderingObject _renderingObject;
        
        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="renderingObject">置き換え対象</param>
        /// <remarks>この時点で置き換え実行されるので注意</remarks>
        public RenderingEffectMaterialReplacer(IRenderingObject renderingObject)
        {
            _renderingObject = renderingObject;
            if (_renderingObject is IRenderingObjectCreateRuntime { CanRendering: false })
            {
                return;
            }
            
            foreach (var renderer in _renderingObject.RendererList)
            {
                if (renderer == null)
                {
                    // TODO [#118937] IRenderingObjectCreateRuntime.CanRendering=falseでもここに来ることがある
                    // 原因不明なのでとりあえずnullチェック
                    continue;
                }
                if (!renderer.enabled)
                { 
                    continue;
                }
                
                var newMaterial = FindReplacedMaterial(renderer.sharedMaterial);
                if (newMaterial == null)
                {
                    continue;
                }
                
                newMaterial.CopyPropertiesFromMaterial(renderer.sharedMaterial);
                _createdMaterialList.Add(newMaterial);

                if (renderer.sharedMaterials.Length > 1)
                {
                    var materials = new Material[renderer.sharedMaterials.Length];
                    _backupMultiMaterialDic[renderer] = renderer.sharedMaterials;
                    _backupMultiRenderQueueDic[renderer] = new int[_backupMultiMaterialDic[renderer].Length];

                    for (var index = 0; index < materials.Length; index++)
                    {
                        if (renderer.sharedMaterials[index] != null)
                        {
                            _backupMultiRenderQueueDic[renderer][index] = renderer.sharedMaterials[index].renderQueue;
                            materials[index] = newMaterial;
                        }
                    }
                    renderer.sharedMaterials = materials;
                }
                else
                {
                    _backupMaterialDic[renderer] = renderer.sharedMaterial;
                    _backupRenderQueueDic[renderer] = _backupMaterialDic[renderer].renderQueue;
                    renderer.sharedMaterial = newMaterial;
                }
            }
        }

        public void Reset()
        {
            if (_renderingObject is IRenderingObjectCreateRuntime { CanRendering: false })
            {
                return;
            }
            
            foreach (var renderer in _renderingObject.RendererList)
            {
                if (renderer == null)
                {
                    // TODO [#118937] IRenderingObjectCreateRuntime.CanRendering=falseでもここに来ることがある
                    // 原因不明なのでとりあえずnullチェック
                    continue;
                }
                
                if (_backupMultiMaterialDic.ContainsKey(renderer))
                {
                    for (var index = 0; index < _backupMultiMaterialDic[renderer].Length; index++)
                    {
                        if (_backupMultiMaterialDic[renderer][index] != null)
                        {
                            _backupMultiMaterialDic[renderer][index].renderQueue = _backupMultiRenderQueueDic[renderer][index];
                        }
                    }

                    renderer.sharedMaterials = _backupMultiMaterialDic[renderer];
                }
                
                if (_backupMaterialDic.ContainsKey(renderer))
                {
                    _backupMaterialDic[renderer].renderQueue = _backupRenderQueueDic[renderer];
                    renderer.sharedMaterial = _backupMaterialDic[renderer];
                }
            }

            foreach (var createdMaterial in _createdMaterialList)
            {
                Object.Destroy(createdMaterial);
            }
            _createdMaterialList.Clear();
            
            _backupMaterialDic.Clear();
            _backupMultiMaterialDic.Clear();
            _backupRenderQueueDic.Clear();
            _backupMultiRenderQueueDic.Clear();
        }
        
        /// <summary>
        /// シェーダを変更したマテリアルを作成する
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        /// <remarks>
        /// エフェクトのシェーダは諸事情あって専用のものに差し替える
        /// 詳細はRenderingToolEffectShaderExporter.cs参照
        /// </remarks>
        Material FindReplacedMaterial(Material source)
        {
            if (source == null)
            {
                return null;
            }
       
            var shaderName = source.shader.name;
            shaderName = shaderName.Replace("Cygames/Effect", "Hidden/Gallop/3D/RenderingTool/Effect");

            var shader = Shader.Find(shaderName);
            if (shader != null && source.shader != shader)
            {
                var newMaterial = new Material(source);
                newMaterial.shader = shader;
                return newMaterial;
            }

            return null;
        }
    }
}
#endif
