#if CYG_DEBUG && UNITY_EDITOR
using System.Collections.Generic;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// レンダリング対象のマテリアルを置き換える処理(陰レイヤーレンダリング用)
    /// </summary>
    /// <seealso cref="LayerRendererShade"/>
    public class RenderingObjectMaterialReplacerForShadeLayer : IRenderingObjectRenderSettingReplacer
    {
        private Dictionary<Renderer, Material> _backupMaterialDic = new Dictionary<Renderer, Material>();
        private Dictionary<Renderer, Material[]> _backupMultiMaterialDic = new Dictionary<Renderer, Material[]>();
        private Dictionary<Renderer, MaterialPropertyBlock> _backupMaterialPropertyBlockDic = new Dictionary<Renderer, MaterialPropertyBlock>();
        private Dictionary<Renderer, int> _backupRenderQueueDic = new Dictionary<Renderer, int>();
        private Dictionary<Renderer, int[]> _backupMultiRenderQueuesDic = new Dictionary<Renderer, int[]>();

        private List<Material> _createdMaterialList = new List<Material>();
        private IRenderingObject _renderingObject;
        
        private ShaderCodeAnalyticsForCharacter _shaderCodeAnalyticsForCharacter;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="renderingObject">置き換え対象</param>
        /// <param name="bodyMaterial">このマテリアルに置き換える</param>
        /// <remarks>この時点で置き換え実行されるので注意</remarks>
        public RenderingObjectMaterialReplacerForShadeLayer(ShaderCodeAnalyticsForCharacter shaderCodeAnalyticsForCharacter, IRenderingObject renderingObject, Material bodyMaterial, Material faceMaterial, Material hairMaterial)
        {
            _shaderCodeAnalyticsForCharacter = shaderCodeAnalyticsForCharacter;
            _renderingObject = renderingObject;
            
            var emptyPropertyBlock = new MaterialPropertyBlock();
            foreach (var renderer in _renderingObject.RendererList)
            {
                var propertyBlock = new MaterialPropertyBlock();
                
                // マテリアルだけの適用をするため、MaterialPropertyBlockは削除しておく(処理簡略化のため)
                if (renderer.HasPropertyBlock())
                {
                    renderer.GetPropertyBlock(propertyBlock);
                    _backupMaterialPropertyBlockDic[renderer] = propertyBlock;
                    renderer.SetPropertyBlock(emptyPropertyBlock);
                }

                // NOTE:マテリアルの種類はModelController.ShaderIndexに従った方がいいかもしれない 現状不具合出てないので特に見ていない
                
                if (renderer.sharedMaterials.Length > 1)
                {
                    var materials = new Material[renderer.sharedMaterials.Length];
                    _backupMultiMaterialDic[renderer] = renderer.sharedMaterials;
                    _backupMultiRenderQueuesDic[renderer] = new int[_backupMultiMaterialDic[renderer].Length];

                    var count = materials.Length;
                    for (var index = 0; index < count; index++)
                    {
                        _backupMultiRenderQueuesDic[renderer][index] = renderer.sharedMaterials[index].renderQueue;
                        materials[index] = CreateNewMaterial(renderer, renderer.sharedMaterials[index], bodyMaterial, faceMaterial, hairMaterial);
                    }
                    renderer.sharedMaterials = materials;
                }
                else
                {
                    _backupMaterialDic[renderer] = renderer.sharedMaterial;
                    _backupRenderQueueDic[renderer] = _backupMaterialDic[renderer].renderQueue;
                    renderer.sharedMaterial = CreateNewMaterial(renderer, renderer.sharedMaterial, bodyMaterial, faceMaterial, hairMaterial);
                }
            }
        }

        public void Reset()
        {
            foreach (var renderer in _renderingObject.RendererList)
            {
                if (_backupMultiMaterialDic.ContainsKey(renderer))
                {
                    var count = _backupMultiMaterialDic[renderer].Length;
                    for (var index = 0; index < count; index++)
                    {
                        _backupMultiMaterialDic[renderer][index].renderQueue = _backupMultiRenderQueuesDic[renderer][index];
                    }

                    renderer.sharedMaterials = _backupMultiMaterialDic[renderer];
                }
                
                if (_backupMaterialDic.ContainsKey(renderer))
                {
                    _backupMaterialDic[renderer].renderQueue = _backupRenderQueueDic[renderer];
                    renderer.sharedMaterial = _backupMaterialDic[renderer];
                }

                if (_backupMaterialPropertyBlockDic.ContainsKey(renderer))
                {
                    renderer.SetPropertyBlock(_backupMaterialPropertyBlockDic[renderer]);
                }
            }
            
            foreach (var createdMaterial in _createdMaterialList)
            {
                Object.Destroy(createdMaterial);
            }
            _createdMaterialList.Clear();
            
            _backupMaterialDic.Clear();
            _backupMultiMaterialDic.Clear();
            _backupMaterialPropertyBlockDic.Clear();
            _backupRenderQueueDic.Clear();
            _backupMultiRenderQueuesDic.Clear();
        }

        private Material CreateNewMaterial(Renderer renderer, Material originalMaterial, Material bodyMaterial,  Material faceMaterial, Material hairMaterial)
        {
            // 陰計算に必要なプロパティをコピーしておく
            // 頑張って必要なプロパティを洗い出す
            Material newMaterial;
            if (renderer.gameObject.name.Contains("Face"))
            {
                // 顔
                newMaterial = new Material(faceMaterial);
                if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceCenterPos)))
                {
                    var value = originalMaterial.GetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceCenterPos));
                    newMaterial.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceCenterPos), value);
                }

                if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceUp)))
                {
                    var value = originalMaterial.GetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceUp));
                    newMaterial.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceUp), value);
                }

                if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CylinderBlend)))
                {
                    var value = originalMaterial.GetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CylinderBlend));
                    newMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._CylinderBlend), value);
                }
            }
            else if (renderer.gameObject.name.Contains("Hair"))
            {
                // 髪の毛
                newMaterial = new Material(hairMaterial);
                if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._HairNormalBlend)))
                {
                    var value = originalMaterial.GetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._HairNormalBlend));
                    newMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._HairNormalBlend), value);
                }

                if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceCenterPos)))
                {
                    var value = originalMaterial.GetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceCenterPos));
                    newMaterial.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._FaceCenterPos), value);
                }
            }
            else
            {
                // その他(Body、Tail)
                newMaterial = new Material(bodyMaterial);
            }

            _createdMaterialList.Add(newMaterial);

            if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._TripleMaskMap)))
            {
                var value = originalMaterial.GetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._TripleMaskMap));
                newMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._TripleMaskMap), value);
            }

            if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ToonFeather)))
            {
                var value = originalMaterial.GetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ToonFeather));
                newMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ToonFeather), value);
            }

            if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ToonStep)))
            {
                var value = originalMaterial.GetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ToonStep));
                newMaterial.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ToonStep), value);
            }

            if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._UseOriginalDirectionalLight)))
            {
                var value = originalMaterial.GetInt(ShaderManager.GetPropertyId(ShaderManager.PropertyId._UseOriginalDirectionalLight));
                newMaterial.SetInt(ShaderManager.GetPropertyId(ShaderManager.PropertyId._UseOriginalDirectionalLight), value);
            }

            if (originalMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._OriginalDirectionalLightDir)))
            {
                var value = originalMaterial.GetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._OriginalDirectionalLightDir));
                newMaterial.SetVector(ShaderManager.GetPropertyId(ShaderManager.PropertyId._OriginalDirectionalLightDir), value);
            }
            
            var shaderCodeAnalytics = _shaderCodeAnalyticsForCharacter.Analysis(renderer.sharedMaterial.shader);
            if (shaderCodeAnalytics.UseCutout())
            {
                CopyMaterialPropertyUtility.CopyFloat(newMaterial, renderer.sharedMaterial, ShaderManager.GetPropertyId(ShaderManager.PropertyId._Cutoff));
                newMaterial.EnableKeyword(ShaderCodeAnalyticsForCharacter.TOON_CUTOUT);
            }

            return newMaterial;
        }
    }
}
#endif
