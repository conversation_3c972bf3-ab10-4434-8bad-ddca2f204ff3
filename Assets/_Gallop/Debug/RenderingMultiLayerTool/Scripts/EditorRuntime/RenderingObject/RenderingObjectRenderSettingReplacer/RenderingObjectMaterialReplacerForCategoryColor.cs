#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// レンダリング対象のマテリアルを置き換える処理
    /// </summary>
    /// <remarks><see cref="LayerRendererCategoryColor"/>専用</remarks>
    public class RenderingObjectMaterialReplacerForCategoryColor : IRenderingObjectRenderSettingReplacer, IRenderingObjectRenderSettingsReplaceForPreview
    {
        private Dictionary<Renderer, Material> _backupMaterialDic = new Dictionary<Renderer, Material>();
        private Dictionary<Renderer, Material[]> _backupMultiMaterialDic = new Dictionary<Renderer, Material[]>();
        private Dictionary<Renderer, MaterialPropertyBlock> _backupMaterialPropertyBlockDic = new Dictionary<Renderer, MaterialPropertyBlock>();
        private Dictionary<Renderer, int> _backupRenderQueueDic = new Dictionary<Renderer, int>();
        private Dictionary<Renderer, int[]> _backupMultiRenderQueueDic = new Dictionary<Renderer, int[]>();

        private List<Material> _createdMaterialList = new List<Material>();
        private IRenderingObject _renderingObject;
        public IRenderingObject TargetRenderingObject => _renderingObject;
        
        private Material _material;
        private bool _needMainTex;

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="renderingObject">置き換え対象</param>
        /// <param name="material">このマテリアルに置き換える</param>
        /// <param name="needMainTex">MainTexを引き継ぐかどうか</param>
        /// <remarks>この時点で置き換え実行されるので注意</remarks>
        public RenderingObjectMaterialReplacerForCategoryColor(IRenderingObject renderingObject, Material material, bool needMainTex = true)
        {
            _renderingObject = renderingObject;
            _material = material;
            _needMainTex = needMainTex;
            
            Replace();
        }

        public void Replace()
        {
            var newPropertyBlock = new MaterialPropertyBlock();
            foreach (var renderer in _renderingObject.RendererList)
            {
                var propertyBlock = new MaterialPropertyBlock();
                if (renderer.HasPropertyBlock())
                {
                    renderer.GetPropertyBlock(propertyBlock);
                    _backupMaterialPropertyBlockDic[renderer] = propertyBlock;
                    var outlineWidth = propertyBlock.GetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._OutlineWidth));
                    newPropertyBlock.SetFloat(ShaderManager.GetPropertyId(ShaderManager.PropertyId._OutlineWidth), outlineWidth);
                    renderer.SetPropertyBlock(newPropertyBlock);
                }

                var newMaterial = new Material(_material);
                _createdMaterialList.Add(newMaterial);
                if (_needMainTex &&
                    renderer.sharedMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex)) &&
                    newMaterial.HasProperty(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex)))
                {
                    var tex = renderer.sharedMaterial.GetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex));
                    newMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._MainTex), tex);
                }

                if (renderer.sharedMaterials.Length > 1)
                {
                    var materials = new Material[renderer.sharedMaterials.Length];
                    _backupMultiMaterialDic[renderer] = renderer.sharedMaterials;
                    _backupMultiRenderQueueDic[renderer] = new int[_backupMultiMaterialDic[renderer].Length];

                    var count = materials.Length;
                    for (var index = 0; index < count; index++)
                    {
                        _backupMultiRenderQueueDic[renderer][index] = renderer.sharedMaterials[index].renderQueue;
                        newMaterial.renderQueue = renderer.sharedMaterials[index].renderQueue;
                        materials[index] = newMaterial;
                    }

                    renderer.sharedMaterials = materials;
                }
                else
                {
                    _backupMaterialDic[renderer] = renderer.sharedMaterial;
                    _backupRenderQueueDic[renderer] = _backupMaterialDic[renderer].renderQueue;
                    newMaterial.renderQueue = renderer.sharedMaterial.renderQueue;
                    renderer.sharedMaterial = newMaterial;
                }
            }
        }

        public void Reset()
        {
            foreach (var renderer in _renderingObject.RendererList)
            {
                if (_backupMultiMaterialDic.ContainsKey(renderer))
                {
                    var count = _backupMultiMaterialDic[renderer].Length;
                    for (var index = 0; index < count; index++)
                    {
                        _backupMultiMaterialDic[renderer][index].renderQueue = _backupMultiRenderQueueDic[renderer][index];
                    }

                    renderer.sharedMaterials = _backupMultiMaterialDic[renderer];
                }
                
                if (_backupMaterialDic.ContainsKey(renderer))
                {
                    _backupMaterialDic[renderer].renderQueue = _backupRenderQueueDic[renderer];
                    renderer.sharedMaterial = _backupMaterialDic[renderer];
                }

                if (_backupMaterialPropertyBlockDic.ContainsKey(renderer))
                {
                    renderer.SetPropertyBlock(_backupMaterialPropertyBlockDic[renderer]);
                }
            }

            foreach (var createdMaterial in _createdMaterialList)
            {
                Object.Destroy(createdMaterial);
            }
            _createdMaterialList.Clear();
            
            _backupMaterialDic.Clear();
            _backupMultiMaterialDic.Clear();
            _backupMaterialPropertyBlockDic.Clear();
            _backupRenderQueueDic.Clear();
            _backupMultiRenderQueueDic.Clear();
        }
        
        /// <summary>
        /// プレビュー用にマテリアルの値をコピーする
        /// </summary>
        /// <param name="source"></param>
        /// <param name="copyMaterialPropertyCallback"></param>
        /// <remarks>プレビュー中にレイヤー設定が変えられるので、設定値をマテリアルに適用する必要がある</remarks>
        public void CopyMaterialPropertyForPreview(Material source, Action<Material, Material> copyMaterialPropertyCallback)
        {
            foreach (var material in _createdMaterialList)
            {
                material.CopyPropertiesFromMaterial(source);
                copyMaterialPropertyCallback?.Invoke(material, source);
            }
        }
        
        public void SetReplaceMaterial(Material material)
        {
            _material = material;
        }
    }
}
#endif
