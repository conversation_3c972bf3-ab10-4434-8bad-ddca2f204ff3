#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// レンダリング対象になるFlashオブジェクト
    /// </summary>
    [DebuggerDisplay("Name = {Name}")]
    public class RenderingFlashObject : IRenderingObject
    {
        private readonly string _name;
        public string Name => _name;

        public RenderingObjectVisibleSettingBase VisibleSettings => null;

        public bool HasVisibleSetting => false;
        
        private readonly List<Renderer> _rendererList;
        public IReadOnlyList<Renderer> RendererList => _rendererList;

        public RenderingFlashObject(GameObject gameObject)
        {
            _name = gameObject.name;
            _rendererList = gameObject.GetComponentsInChildren<Renderer>(true).ToList();
        }

        public void SetVisibleSettings(RenderingObjectVisibleSettingBase objectVisibleSetting)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 表示状態の設定を適用する
        /// </summary>
        /// <param name="currentFrame">シーンの現在のフレーム</param>
        public void ApplyVisibleSetting(int currentFrame)
        {
            // flashが何なのかよくわかってないのでとりあえず表示(使われてないっぽい？
            Show();
        }
        
        /// <summary>
        /// 表示状態を適用前の状態に戻す
        /// </summary>
        public void ResetVisibleSetting(int currentFrame)
        {
            // シーン側のトラックが取得できてないので未実装
        }
        
        /// <summary>
        /// 表示する
        /// </summary>
        private void Show()
        {
            foreach (var renderer in _rendererList)
            {
                renderer.enabled = true;
            }
        }

        /// <summary>
        /// 非表示にする
        /// </summary>
        private void Hide()
        {
            foreach (var renderer in _rendererList)
            {
                renderer.enabled = false;
            }
        }

        public bool Equals(IRenderingObject other)
        {
            if (!(other is RenderingFlashObject flashObject))
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }

            return _name == flashObject._name;
        }

        public override int GetHashCode()
        {
            return _name.GetHashCode();
        }
    }
}
#endif
