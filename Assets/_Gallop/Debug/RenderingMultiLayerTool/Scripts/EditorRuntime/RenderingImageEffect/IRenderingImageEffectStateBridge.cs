#if CYG_DEBUG && UNITY_EDITOR

namespace Gallop.RenderingTool
{
    /// <summary>
    /// レンダリングするシーンのImageEffectの状態を取得するためのインターフェース
    /// </summary>
    public interface IRenderingImageEffectStateBridge
    {
        /// <summary>
        /// シーン側の状態を取得する
        /// </summary>
        /// <param name="currentFrame">シーンの現在のフレーム</param>
        /// <returns></returns>
        bool GetEnableState(int currentFrame);
    }

    public interface IRenderingImageEffectStateBridgeGlobalFog : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeSunShafts : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeTiltShift : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeIndirectLightShafts : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeBloom : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeDoF : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeScreenOverlay : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeDiffusion : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeRadialBlur : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeFluctuation : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeLensDistortion : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeChromaticAberration : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeColorCorrection : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeColorGrading : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeToneCurve : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeExposure : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeVortex : IRenderingImageEffectStateBridge
    {
    }
    
    public interface IRenderingImageEffectStateBridgeTransmittedLight : IRenderingImageEffectStateBridge
    {
    }
}
#endif
