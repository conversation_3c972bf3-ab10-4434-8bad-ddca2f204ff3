#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop.RenderingTool
{
    public interface IReadOnlyRenderingSceneController
    {
        /// <summary>
        /// 現在のフレーム
        /// </summary>
        int CurrentFrame { get; }
        
        /// <summary>
        /// シーンの経過時間
        /// </summary>
        float ElapsedTime { get; }
    }
    
    /// <summary>
    /// レンダリング時にシーン側を操作するためのインターフェース
    /// </summary>
    public interface IRenderingSceneController : IReadOnlyRenderingSceneController
    {
        /// <summary>
        /// レンダリング設定を設定する
        /// </summary>
        void SetRenderingSetting(IReadOnlyRenderingSettingPerRenderingScene renderingSettingsPerRenderingScene);

        /// <summary>
        /// レンダリング前のシミュレーションを開始
        /// </summary>
        /// <param name="startFrame"></param>
        /// <returns></returns>
        public RenderingSimulationResult StartSimulation(int startFrame);

        /// <summary>
        /// レンダリング前のシミュレーションを終了
        /// </summary>
        public void EndSimulation();
        
        /// <summary>
        /// レンダリング開始
        /// </summary>
        /// <param name="startFrame"></param>
        void StartRendering(int startFrame);
        
        /// <summary>
        /// レンダリング終了
        /// </summary>
        void EndRendering();
        
        /// <summary>
        /// 次のフレームに進ませる
        /// </summary>
        void NextFrame();

        /// <summary>
        /// シーンを再生させてレンダリング開始
        /// </summary>
        /// <param name="startFrame"></param>
        void StartWhileScenePlayingRendering(int startFrame);
        
        /// <summary>
        /// シーンを停止してレンダリング終了
        /// </summary>
        void EndWhileScenePlayingRendering();
    }
    
    /// <summary>
    /// <see cref="IRenderingSceneController"/>からプレビュー機能を分離したインターフェース
    /// </summary>
    public interface IRenderingScenePreview : IDisposable
    {
        /// <summary>
        /// プレビュー中かどうか
        /// </summary>
        bool IsPreviewing { get; }

        /// <summary>
        /// プレビューを開始する
        /// </summary>
        /// <param name="renderingObjectList"></param>
        /// <param name="layerRendererSettings"></param>
        void StartPreview(IReadOnlyList<IRenderingObject> renderingObjectList, ICanPreviewLayerRendererSettings layerRendererSettings);
        
        /// <summary>
        /// プレビューを終了する
        /// </summary>
        void EndPreview();

        /// <summary>
        /// プレビューに設定を反映する
        /// </summary>
        /// <param name="layerRendererSettings"></param>
        void ApplySettingsToPreview(ICanPreviewLayerRendererSettings layerRendererSettings);
    }    
}
#endif
