#if CYG_DEBUG && UNITY_EDITOR
using System;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// <see cref="RenderingEffectObject"/>の表示非表示の設定を保持するクラス
    /// </summary>
    [Serializable]
    public class RenderingEffectObjectVisible : RenderingObjectVisibleSettingBase
    {
        [SerializeField]
        private RenderingEffectIdentifier _effectIdentifier;

        public RenderingEffectObjectVisible(RenderingEffectIdentifier effectIdentifier)
        {
            _effectIdentifier = effectIdentifier;
        }

        /// <summary>
        /// <paramref name="target"/>と同じオブジェクトの設定かどうか
        /// </summary>
        /// <param name="target">レンダリングオブジェクト</param>
        /// <returns></returns>
        public override bool IsSameObject(IRenderingObject target)
        {
            if (!(target is IRenderingEffectObject effectObject)) 
            {
                return false;
            }

            if (effectObject is RenderingEffectObjectForLive forLive)
            {
                return _effectIdentifier.EqualsForLive(forLive.EffectIdentifier);
            }

            return _effectIdentifier.Equals(effectObject.EffectIdentifier);
        }
        
        public override bool Equals(RenderingObjectVisibleSettingBase other)
        {
            if (!(other is RenderingEffectObjectVisible effectObjectVisible))
            {
                return false;
            }

            if (ReferenceEquals(this, other))
            {
                return true;
            }

            return _effectIdentifier.Equals(effectObjectVisible._effectIdentifier);
        }

        public override void DeepCopy(RenderingObjectVisibleSettingBase source)
        {
            if (!(source is RenderingEffectObjectVisible self))
            {
                return;
            }
            
            base.DeepCopy(source);
            _effectIdentifier.DeepCopy(self._effectIdentifier);
        }
                
        public override string GetDisplayName()
        {
            return _effectIdentifier.Name;
        }
    }
}
#endif
