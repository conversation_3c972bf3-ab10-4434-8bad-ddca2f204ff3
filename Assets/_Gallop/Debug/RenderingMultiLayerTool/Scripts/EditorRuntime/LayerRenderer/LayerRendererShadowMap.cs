#if CYG_DEBUG && UNITY_EDITOR
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// ShadowMapレイヤーをレンダリング
    /// </summary>
    /// <remarks>法線の方向をカラーでレンダリング</remarks>
    [LayerRenderer(typeof(LayerRendererSettingsShadowMap), 7, "ShadowMap")]
    public class LayerRendererShadowMap : ILayerRendererGeometry, INeedChangeLightSettingLayerRenderer, INeedRenderingSceneController, INeedForceRenderingObjectLayerRenderer
    {
        private const string ShaderNameCharacter = "Hidden/Gallop/3D/RenderingTool/ShadowMapCharacter";
        private const string ShaderNameCharacterWithoutSelfShadow = "Hidden/Gallop/3D/RenderingTool/ShadowMapCharacterWithoutSelfShadow";
        private const string ShaderNameBg = "Hidden/Gallop/3D/RenderingTool/ShadowMapBg";
        
        private IReadOnlyList<IRenderingObject> _renderingObjectList;

        private Material _materialBg;
        private Material _materialCharacter;
        private Material _materialCharacterWithoutSelfShadow;

        private bool _withoutCharacterSelfShadow;
        
        private RenderTexture _targetRenderTexture;
        private List<IRenderingObjectRenderSettingReplacer> _renderingObjectRenderSettingBackUpList = new List<IRenderingObjectRenderSettingReplacer>();
        
        private IReadOnlyRenderingSceneController _renderingSceneController;
        
        private Light _light;
        private LightShadowResolution _resolution;

        private ShaderCodeAnalyticsForCharacter _shaderCodeAnalyticsForCharacter = new ShaderCodeAnalyticsForCharacter();
        private ShaderCodeAnalyticsForBackGround _shaderCodeAnalyticsForBackGround = new ShaderCodeAnalyticsForBackGround();
        
        public LayerRendererShadowMap()
        {
            _materialCharacter = new Material(Shader.Find(ShaderNameCharacter)); 
            _materialCharacterWithoutSelfShadow = new Material(Shader.Find(ShaderNameCharacterWithoutSelfShadow)); 
            _materialBg = new Material(Shader.Find(ShaderNameBg));
        }

        public void SetRenderTarget(RenderTexture beforePostProcessColorRenderTexture, RenderTexture afterPostProcessColorRenderTexture)
        {
            _targetRenderTexture = beforePostProcessColorRenderTexture;
        }
        
        public void SetRenderingObject(IReadOnlyList<IRenderingObject> renderingObjectList)
        {
            _renderingObjectList = renderingObjectList;
        }
        
        /// <summary>
        /// <see cref="IReadOnlyRenderingSceneController"/>を設定する
        /// </summary>
        public void SetRenderingSceneController(IReadOnlyRenderingSceneController renderingScene)
        {
            _renderingSceneController = renderingScene;
        }

        public void SetupGeometry()
        {
            if (_targetRenderTexture == null)
            {
                return;
            }

            var characterMaterial = _withoutCharacterSelfShadow ? _materialCharacterWithoutSelfShadow : _materialCharacter;

            foreach (var renderingObject in _renderingObjectList)
            {
                if (renderingObject is RenderingCharacter ||
                    renderingObject is RenderingMiniCharacter ||
                    renderingObject is RenderingPropObject)
                {
                    _renderingObjectRenderSettingBackUpList.Add(new RenderingObjectMaterialReplacerForShadowMapLayerCharacter(_shaderCodeAnalyticsForCharacter, renderingObject, characterMaterial));
                }
                else if (renderingObject is RenderingBackGroundFurObject furObject)
                {
                    _renderingObjectRenderSettingBackUpList.Add(new RenderingBackGroundFurObjectMaterialReplacer(_shaderCodeAnalyticsForBackGround, furObject, _materialBg, _renderingSceneController.ElapsedTime));
                }
                else if (renderingObject is RenderingBackGroundObject)
                {
                    _renderingObjectRenderSettingBackUpList.Add(new RenderingBackGroundObjectMaterialReplacer(_shaderCodeAnalyticsForBackGround, renderingObject, _materialBg));
                }
                else if (renderingObject is IRenderingEffectObject ||
                         renderingObject is RenderingCharacterFakeShadow)
                {
                    _renderingObjectRenderSettingBackUpList.Add(new RenderingObjectVisibleReplacer(renderingObject, false));
                }
            }
        }

        public void ResetGeometry()
        {
            foreach (var replacer in _renderingObjectRenderSettingBackUpList)
            {
                replacer.Reset();
            }
            _renderingObjectRenderSettingBackUpList.Clear();
        }

        public Texture2D Rendering(int width, int height)
            => DownSamplingProcess.Execute(width, height, _targetRenderTexture);

        public void ApplySettings(LayerRendererSettingsBase rendererSetting)
        {
            if (!(rendererSetting is LayerRendererSettingsShadowMap settingsShadowMap))
            {
                return;
            }

            _withoutCharacterSelfShadow = settingsShadowMap.IgnoreCharacterSelfShadow;
        }

        public void SetLight(Light light)
        {
            if (light == null)
            {
                return;
            }
            
            _light = light;
            _resolution = _light.shadowResolution;
            
            _light.shadowResolution = LightShadowResolution.Low;
        }

        public void ResetLight()
        {
            if (_light == null)
            {
                return;
            }
            
            _light.shadowResolution = _resolution;
        }
        
        public void Dispose()
        {
            Object.DestroyImmediate(_materialBg, true);
            Object.DestroyImmediate(_materialCharacter, true);
            Object.DestroyImmediate(_materialCharacterWithoutSelfShadow, true);
        }

        /// <summary>
        /// レンダリング前に描画処理が必要なオブジェクトの準備処理
        /// </summary>
        /// <param name="renderingObject"></param>
        public void SetupGeometryNeedForceRenderingObject(INeedForceRenderingObject renderingObject)
        {
            if (renderingObject is RenderingImpostorAudienceObject audienceObject)
            {
                _renderingObjectRenderSettingBackUpList.Add(new RenderingImpostorAudienceVisibleReplacer(audienceObject, false));
            }
        }
    }
}
#endif
