#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// ImageEffectの表示制御UIの描画処理
    /// </summary>
    public class RenderingImageEffectEnableGUI : RenderingObjectVisibleGUI
    {
        private bool _isFoldout = true;
        
        private bool _isAllEnable;
        private bool _isAllVisible;

        public override void OnGUI(SerializedProperty visibleSettingProperty)
        {
            _isFoldout = EditorGUILayout.Foldout(_isFoldout, "ImageEffectの制御", true);

            if (!_isFoldout)
            {
                return;
            }

            EditorGUI.indentLevel++;
            var settingListProperty = visibleSettingProperty.FindPropertyRelative("_imageEffectSettingList");
            var arraySize = settingListProperty.arraySize;
            if (arraySize == 0)
            {
                EditorGUILayout.LabelField("ImageEffectが使われていません");
                EditorGUI.indentLevel--;
                return;
            }

            using (new EditorGUILayout.HorizontalScope())
            {
                _isAllEnable = MultiBoolValueGUI(_isAllEnable, settingListProperty, "_isEnableSetting");
                _isAllVisible = MultiBoolValueGUI(_isAllVisible, settingListProperty, "_isEnable");
            }

            for (int i = 0; i < arraySize; i++)
            {
                var property = settingListProperty.GetArrayElementAtIndex(i);
                var isEnable = property.FindPropertyRelative("_isEnableSetting");
                var isVisible = property.FindPropertyRelative("_isEnable");
                var instance = SerializedPropertyUtility.GetParent(isVisible) as RenderingImageEffectSettings;

                using (new EditorGUILayout.HorizontalScope())
                {
                    using (var check = new EditorGUI.ChangeCheckScope())
                    {
                        EditorGUILayout.PropertyField(isEnable, GUIContent.none, GUILayout.Width(15));
                        using (new EditorGUI.DisabledScope(!isEnable.boolValue))
                        {
                            EditorGUILayout.PropertyField(isVisible, GUIContent.none, GUILayout.Width(15));

                            if (check.changed)
                            {
                                property.serializedObject.ApplyModifiedProperties();
                            }
                        }
                    }

                    if (instance == null)
                    {
                        EditorGUILayout.LabelField("値が不正です。");
                        continue;
                    }

                    using (new EditorGUI.DisabledScope(!isEnable.boolValue))
                    {
                        EditorGUILayout.LabelField(instance.GetDisplayName());
                    }
                }
            }

            EditorGUI.indentLevel--;
        }

        public override void Dispose()
        {
        }

        public override void SetRenderingSceneType(RenderingSceneType type)
        {
        }

        public override void OnChangeRenderingSetting(SerializedProperty serializedProperty)
        {
            
        }
    }
}
#endif
