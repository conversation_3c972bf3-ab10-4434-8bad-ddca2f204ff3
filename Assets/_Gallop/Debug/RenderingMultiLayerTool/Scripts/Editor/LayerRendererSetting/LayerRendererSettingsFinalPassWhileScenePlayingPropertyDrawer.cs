#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// 最終描画結果のレイヤーをレンダリングする際の設定値用の<see cref="PropertyDrawer"/>
    /// </summary>
    /// <seealso cref="LayerRendererSettingsFinalPassWhileScenePlaying"/>
    [CustomPropertyDrawer(typeof(LayerRendererSettingsFinalPassWhileScenePlaying))]
    public class LayerRendererSettingsFinalPassWhileScenePlayingPropertyDrawer : LayerRendererSettingsFinalPassPropertyDrawer
    {
        protected override string DisplayName => "FinalPass(※揺れ物が一致しない可能性があります)";
    }
}
#endif