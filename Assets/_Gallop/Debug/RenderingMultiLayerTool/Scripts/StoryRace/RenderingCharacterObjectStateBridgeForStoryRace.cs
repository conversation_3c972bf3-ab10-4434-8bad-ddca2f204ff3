#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// キャラのStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingCharacterObjectStateBridgeForStoryRace : IRenderingCharacterStateBridge, IHasEyebrowRenderingStateBridge
    {
        private RaceCameraEventBase.Sequence<CourseCharacterTransformParam> _characterTransformSequence;
        private RaceCameraEventBase.Sequence<CourseCharacterFacialParamEyebrow> _eyebrowSequence;
        
        public RenderingCharacterObjectStateBridgeForStoryRace(RaceCameraEventBase.Sequence<CourseCharacterTransformParam> characterTransformSequence, RaceCameraEventBase.Sequence<CourseCharacterFacialParamEyebrow> eyebrowSequence)
        {
            _characterTransformSequence = characterTransformSequence;
            _eyebrowSequence = eyebrowSequence;
        }

        public bool GetVisibleState(int currentFrame)
        {
            var param = _characterTransformSequence.GetParamFromDistance(currentFrame);
            if (param == null)
            {
                return false;
            }

            return param.visible;
        }

        public bool IsEyebrowVisible(int currentFrame)
        {
            var param = _eyebrowSequence.GetParamFromDistance(currentFrame);
            if (param == null)
            {
                return true; // デフォルトは表示が普通のはず
            }
            
            return param.IsMayuVisible;
        }
    }

    /// <summary>
    /// キャラ影のStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingCharacterFakeShadowStateBridgeForStoryRace : IRenderingCharacterFakeShadowStateBridge
    {
        // NOTE 影制御 StoryRaceでは影を出してる状況がまだないっぽいので未対応
        public bool GetVisibleState(int currentFrame)
        {
            return true;
        }

        public void SetRenderer(Renderer renderer)
        {
        }
    }

    /// <summary>
    /// キャラ汗のStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingCharacterSweatStateBridgeForStoryRace : IRenderingCharacterSweatStateBridge
    {
        private RaceCameraEventBase.Sequence<CourseCharacterFacialParamEffect> _facialParamSequence;

        private GameObject _target;

        public RenderingCharacterSweatStateBridgeForStoryRace(RaceCameraEventBase.Sequence<CourseCharacterFacialParamEffect> facialParamSequence, GameObject target)
        {
            _facialParamSequence = facialParamSequence;
            _target = target;
        }

        public bool GetVisibleState(int currentFrame)
        {
            var param = _facialParamSequence.GetParamFromDistance(currentFrame);
            if (param == null)
            {
                return false;
            }

            // 1つでも表示設定になっていたら表示扱い
            var isEnable = false;
            foreach (var isShowSweat in param.IsShowSweat)
            {
                if (isShowSweat)
                {
                    isEnable = true;
                    break;
                }
            }

            return isEnable;
        }
    }

    /// <summary>
    /// キャラに紐づいているプロップのStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingPropObjectStateBridgeForStoryRace : IRenderingPropStateBridge
    {
        private RaceEpisodeCameraEvent.SequencePrefab _sequencePrefab;
        private IReadOnlyList<RenderingCharacter> _renderingCharacterList;

        public RenderingPropObjectStateBridgeForStoryRace(RaceEpisodeCameraEvent.SequencePrefab sequencePrefab, IReadOnlyList<RenderingCharacter> renderingCharacterList)
        {
            _sequencePrefab = sequencePrefab;
            _renderingCharacterList = renderingCharacterList;
        }

        public bool GetVisibleState(int currentFrame)
        {
            var param = _sequencePrefab.CurrentParam;
            if (param == null)
            {
                return false;
            }

            return param.visible;
        }

        /// <summary>
        /// このプロップのアタッチ対象になっている<see cref="IRenderingObject"/>を探す
        /// </summary>
        /// <param name="currentFrame"></param>
        /// <returns><see cref="IRenderingObject"/>の識別子 アタッチ対象がなければnull</returns>
        public IRenderingObjectIdentifier FindAttachedRenderingObjectIdentifier(int currentFrame)
        {
            // NOTE キャラ以外へのアタッチが考慮できてないけど、StoryRaceならそれで十分かもしれない
            if (!_sequencePrefab.CurrentParam.attachChara)
            {
                return RenderingCharacterIdentifier.Invalid;
            }

            // RaceManager使ってるのが汚いけどStoryRace専用だしまあいいかなって...
            var charaIndex = _sequencePrefab.CurrentParam.attachNo;
            var raceManager = RaceManager.Instance as RaceManagerStoryReplay;
            var model = raceManager.RaceView.GetModelController(charaIndex);

            return new RenderingCharacterIdentifier(model.GetCharaID(), model.GetDressId(), model.name, model.transform.GetHierarchyPath(), model.transform.GetSiblingIndex());
        }
    }

    /// <summary>
    /// 背景のStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingBackGroundStateBridgeForStoryRace : IRenderingBackGroundStateBridge
    {
        public bool GetVisibleState(int currentFrame)
        {
            return true;
        }
    }

    /// <summary>
    /// インポスター観客のStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingImpostorAudienceStateBridgeForStoryRace : IRenderingImpostorAudienceObjectStateBridge
    {
        public bool GetVisibleState(int currentFrame)
        {
            // インポスター観客はTimelineから制御されてないのみたいなので常にtrue
            return true;
        }
    }
    
    /// <summary>
    /// Fur(リッチ芝)のStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingBackGroundFurStateBridgeForStoryRace : IRenderingBackGroundStateBridge
    {
        private RaceCameraEventBase.Sequence<CourseGrassParam> _grassParam;

        public RenderingBackGroundFurStateBridgeForStoryRace(RaceCameraEventBase.Sequence<CourseGrassParam> grassParam)
        {
            _grassParam = grassParam;
        }

        public bool GetVisibleState(int currentFrame)
        {
            var param = _grassParam.CurrentParam;
            if (param == null)
            {
                return false;
            }
            
            return param.UseGrassFur;
        }
    }

    /// <summary>
    /// エフェクトのStoryRace側の状態を取得する処理
    /// </summary>
    public class RenderingEffectObjectStateBridgeForStoryRace : IRenderingEffectObjectStateBridge
    {
        private bool _isPlaying;
        public bool IsPlaying => _isPlaying;
        
        public float ElapsedTimeFromPlay { get; }
        
        private RaceEpisodeCameraEvent.SequencePrefab _sequencePrefab;

        public RenderingEffectObjectStateBridgeForStoryRace(RaceEpisodeCameraEvent.SequencePrefab sequencePrefab)
        {
            _sequencePrefab = sequencePrefab;
        }

        public bool GetVisibleState(int currentFrame)
        {
            if (_sequencePrefab == null)
            {
                // Timelineで制御されていないものは常時表示扱い
                return true;
            }

            var param = _sequencePrefab.CurrentParam;
            if (param == null)
            {
                return false;
            }

            _isPlaying = param.play;

            return param.visible;
        }
    }
    
    public class RenderingEffectObjectCreateRuntimeStateBridgeForStoryRace : IRenderingEffectCreateRuntimeStateBridge
    {
        private RaceEpisodeCameraEvent.SequencePrefab _sequencePrefab;
        private int _keyIndex;

        public RenderingEffectObjectCreateRuntimeStateBridgeForStoryRace(RaceEpisodeCameraEvent.SequencePrefab sequencePrefab, int keyIndex)
        {
            _sequencePrefab = sequencePrefab;
            _keyIndex = keyIndex;
        }

        public bool GetVisibleState(int currentFrame)
        {
            if (_sequencePrefab == null)
            {
                // Timelineで制御されていないものは常時表示扱い
                return true;
            }

            var prefabWork = _sequencePrefab.PrefabWorkInstance;
            if (prefabWork == null)
            {
                return false;
            }

            return !_sequencePrefab.PrefabWorkInstance.IsFinished;
        }

        public GameObject GetCreatedGameObject()
        {
            if (_sequencePrefab == null)
            {
                return null;
            }

            if (_keyIndex < _sequencePrefab.OneShotWorkList.Count)
            {
                var param = _sequencePrefab.OneShotWorkList[_keyIndex];
                return param.Instance;
            }

            return null;
        }
    }
    
    /// <summary>
    /// 影のStoryRace側の状態を取得する処理
    /// </summary>
    public class ShadowSettingsStateBridgeForStoryRace : IShadowSettingsStateBridge
    {
        private RenderingShadowSettings _shadowSettings = new RenderingShadowSettings();

        RaceCameraEventBase.Sequence<CourseGrassParam> _grassParam;

        public ShadowSettingsStateBridgeForStoryRace(RaceCameraEventBase.Sequence<CourseGrassParam> grassParam)
        {
            _grassParam = grassParam;
        }

        public RenderingShadowSettings GetSettingsState(int currentFrame)
        {
            // StoryRaceはTimeline上でShadowDistanceのみ制御している
            // 他の値は特に変更されないのでデフォルトの値を入れておく
            
            var param = _grassParam.GetParamFromDistance(currentFrame);
            _shadowSettings.ShadowDistance = param?.ShadowDistance ?? GameDefine.DEFAULT_SHADOW_DISTANCE;
            _shadowSettings.ShadowResolution = UnityEngine.Rendering.Universal.ShadowResolution._1024;
            _shadowSettings.UseShadowOffset = false;
            _shadowSettings.ShadowOffset = Vector2.zero;
            _shadowSettings.ShadowScale = 1;
            
            return _shadowSettings;
        }
    }
}
#endif
