#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop.RenderingTool
{
    /// <summary>
    /// 実際のレンダリング処理(StoryRace用)
    /// </summary>
    /// <remarks>
    /// シーンを通常再生して、毎フレームレンダリングする処理 (<see cref="ILayerRendererWhileScenePlaying"/>のレイヤーをレンダリングする用)
    /// 開始フレームから終了フレームまで一気に1レイヤー分レンダリングする
    /// </remarks>
    /// <seealso cref="RenderingProcessorForStoryRace"/>
    /// <seealso cref="RenderingProcessInternalForStoryRace"/>
    public class RenderingProcessInternalWhileSceneRenderingForStoryRace : IRenderingProcessInternalForStoryRace
    {
        private IRenderingScene _renderingScene;
        private IReadOnlyList<ILayerRendererWhileScenePlaying> _layerRendererRawRenderingList;
        private IReadOnlyRenderingSettingPerRenderingScene _renderingSettings;
        private IRenderingSceneController _sceneController;
        private IReadOnlyList<Light> _sceneLightList;
        private RenderingExportFilePath _exportFilePath;

        private Action _onFinishSubProcess;
        private Func<IReadOnlyRenderingSettingPerRenderingScene, int, int, bool> _onUpdateRenderingProgress;
        private Action<RenderingExportFilePath, RenderingFrameResult> _onFinishFrameRendering;
    
        private int _captureFrameRate;
        
        private RenderingFrameResult _currentRenderingFrame;
        
        private int _currentLayerIndex;
        private ILayerRenderer _currentRenderer;
    
        private int _renderingCount;

        public RenderingProcessInternalWhileSceneRenderingForStoryRace(IRenderingScene renderingScene, IRenderingSceneController sceneController, IReadOnlyList<ILayerRendererWhileScenePlaying> layerRendererRawRenderingList, IReadOnlyList<Light> sceneLightList)
        {
            _renderingScene = renderingScene;
            _sceneController = sceneController;
            _layerRendererRawRenderingList = layerRendererRawRenderingList;
            _sceneLightList = sceneLightList;
        }

        public void SetEvent(RenderingFrameEvent renderingFrameEvent, Action onFinishSubProcess)
        {
            _onUpdateRenderingProgress = renderingFrameEvent.OnUpdateRenderingProgress;
            _onFinishFrameRendering = renderingFrameEvent.OnFinishFrameRendering;
            _onFinishSubProcess = onFinishSubProcess;
        }

        public void StartRendering(IReadOnlyRenderingSettingPerRenderingScene renderingSettingPerRenderingScene, RenderingExportFilePath exportFilePath, int startFrame)
        {
            _renderingSettings = renderingSettingPerRenderingScene;
            _exportFilePath = exportFilePath;
            
            _currentRenderingFrame = new RenderingFrameResult(_renderingSettings.GetExportFrame(_renderingSettings.RenderingFrame.x));
            _currentLayerIndex = 0;
            _currentRenderer = _layerRendererRawRenderingList[_currentLayerIndex];
            
            _captureFrameRate = Time.captureFramerate;
            Time.captureFramerate = 30;
            
            _sceneController.StartWhileScenePlayingRendering(startFrame);
            
            _onUpdateRenderingProgress(_renderingSettings, _currentRenderingFrame.Frame, _renderingCount);
        }

        public void EndRenderingProcess()
        {
            _sceneController.EndWhileScenePlayingRendering();
            Time.captureFramerate = _captureFrameRate;
        }
    
        public void OnPreRendering()
        {
            if (_currentRenderer is INeedRenderingSceneForLayerRenderer needRenderingSceneLayerRenderer)
            {
                needRenderingSceneLayerRenderer.SetRenderingScene(_renderingScene);
            }
            
            if (_currentRenderer is IHasPrePostProcessLayerRenderer hasPrePostProcessRenderingLayerRenderer)
            {
                hasPrePostProcessRenderingLayerRenderer.PreProcess();
            }
    
            if (_currentRenderer is ILayerRendererGeometry rendererGeometry)
            {
                rendererGeometry.SetupGeometry();
            }
    
            if (_currentRenderer is INeedChangeLightSettingLayerRenderer lightSettingLayerRenderer &&
                _sceneLightList.Count > 0)
            {
                lightSettingLayerRenderer.SetLight(_sceneLightList[0]);
            }
        }
    
        public void OnPostRendering()
        {
            ExportInternal(_currentRenderer);
            
            if (_currentRenderer is ILayerRendererGeometry rendererGeometry)
            {
                rendererGeometry.ResetGeometry();
            }
    
            if (_currentRenderer is INeedChangeLightSettingLayerRenderer lightSettingLayerRenderer)
            {
                lightSettingLayerRenderer.ResetLight();
            }
    
            if (_currentRenderer is IHasPrePostProcessLayerRenderer hasPrePostProcessRenderingLayerRenderer)
            {
                hasPrePostProcessRenderingLayerRenderer.PostProcess();
            }
      
            _renderingCount++;
            _onFinishFrameRendering?.Invoke(_exportFilePath, _currentRenderingFrame);
            var isCancel = _onUpdateRenderingProgress(_renderingSettings, _currentRenderingFrame.Frame + 1, _renderingCount);

            if (isCancel || _sceneController.CurrentFrame >= _renderingSettings.RenderingFrame.y)
            {
                _currentLayerIndex++;
                if (_currentLayerIndex > _layerRendererRawRenderingList.Count - 1)
                {
                    // 全レイヤーの描画を終えたので終了
                    EndRenderingProcess();
                    _onFinishSubProcess?.Invoke();
                    return;
                }

                // 1レイヤーで全フレーム描画し終えたので、次のレイヤーに移行してまた最初のフレームからレンダリング
                _currentRenderer = _layerRendererRawRenderingList[_currentLayerIndex];
                _sceneController.EndWhileScenePlayingRendering();
                _sceneController.StartWhileScenePlayingRendering(_renderingSettings.RenderingFrame.x);
            }
        
            _currentRenderingFrame = new RenderingFrameResult(_renderingSettings.GetExportFrame(_sceneController.CurrentFrame));
        }
        
        /// <summary>
        /// レンダリング処理
        /// </summary>
        /// <param name="renderer"></param>
        private void ExportInternal(ILayerRenderer renderer)
        {
            var texture = renderer.Rendering(Screen.OriginalScreenWidth, Screen.OriginalScreenHeight);
            if (texture == null)
            {
                return;
            }

            var attribute = RenderingToolReflectionCache.GetLayerRendererAttribute(renderer);
            var result = new RenderingLayerResult(attribute.SettingOrder, attribute.LayerName, texture);
            _currentRenderingFrame.AddRenderingLayerResult(result);
        }
    }
}
#endif
