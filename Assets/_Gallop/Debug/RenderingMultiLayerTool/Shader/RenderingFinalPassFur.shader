Shader "Hidden/Gallop/3D/RenderingTool/FinalPassFur"
{
    Properties
    {
        // BgLightMapColorで使用するパラメータ
        _MainTex("_MainTex", 2D) = "white" {}
        _SubTex("Sub (RGB)", 2D) = "white" {}
        _MulColor0("_MulColor0", Color) = (1, 1, 1, 1)
        _LightTex("Lightmap", 2D) = "gray" {}
        _MultimapRate("Rate", Range(0,1)) = 0
        
        // Fur用
        _NoiseTex("NoiseTex",2D) = "white" {}
        _ShellAmountMax("ShellAmountMax", Int) = 1
        [IntRange] _ShellAmount("Shell Amount",Range(1,100)) = 16
        _ShellStep("Shell Step",Range(0.0,0.01)) = 0.001
        _ShellStepScale("ShellStepScale", Range(0,10)) = 1.0
        _AlphaCutout("Alpha Cutout",Range(0.0,1.0)) = 1.0
        _Occlusion("Occlusion", Range(0.0,1.0)) = 0.0
        _BaseMove("Base Move", Vector) = (0,0,0,0)
        _WindFreq("Wind Freq", Vector) = (0,0,0,0)
        _WindMove("Wind Move", Vector) = (0,0,0,0)
        _Brightness("Brightness", Color) = (0,0,0,0)
        _GrassFadeBeginDist("GrassFadeBeginDist", Float) = 50.0
        _GrassFadeEndDist("GrassFadeEndDist", Float) = 1000.0
        _GrassFadeOffsetPower("GrassFadeOffsetPower", Range(0,1)) = 0.1
        
        // 検証用でFur芝にDirShadowを乗せる為移植
        _ShadowFadeCenter("_ShadowFadeCenter", Vector) = (0,0,0)
        _ShadowFadeFront("_ShadowFadeFront", Vector) = (0,0,1)
        _ShadowFadeParam("_ShadowFadeParam", Vector) = (100, 100, 0, 1)
        _ShadowStartColor("_ShadowStartColor", Color) = (0,0,0,1)
        _ShadowEndColor("_ShadowEndColor", Color) = (0,0,0,1)

        _MulColor2("_MulColor2", Color) = (1, 1, 1, 1)
        
        [MaterialToggle]
        _IsBinarization("_IsBinarization", Int) = 0
        _DirShadowColor("_DirShadowColor", Color) = (0, 0, 0, 1)
        _DirShadowIntensity("_DirShadowIntensity", Range(0,1)) = 1.0
        
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
        //Stencil
        _StencilMask ("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("Stencil Operation",int) = 2    //UnityEngine.Rendering.StencilOp.Replace
    }
    SubShader
    {
        Tags {"Queue" = "Geometry" "RenderType" = "Opaque" "IgnoreProjector" = "True" }
        Cull [_Cull]
        LOD 100

        Pass
        {
            Name "FurTurf"
            Tags {"LightMode" = "UniversalForward"}
            Stencil
            {
                Ref [_StencilMask]
                Comp [_StencilComp]
                Pass [_StencilOp]
            }
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma fragmentoption ARB_precision_hint_fastest
            #pragma multi_compile_fragment _ USE_SHADOW_PARAM
            #pragma multi_compile _ USE_FRAGDEPTH

#if defined(BUILDIN_PIPELINE)
            #pragma multi_compile_fwdbase
#else
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
            #pragma multi_compile _ _ADDITIONAL_LIGHTS
            #define _SHADOWS_SOFT
            #pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS
#endif
            #define PASS PASS_MAIN_COLOR

            #define USE_DIR_SHADOW_COLOR
            #define USE_LIGHTMAP
            #define USE_FILLCOLOR
            #define USE_FOG
            #define USE_SUBCOLOR
            #define USE_LIGHTMAPCOLOR
            #define USE_LIGHTMAPDENSITY
            #define USE_FUR
            #define ENABLE_SHADOW
            #include "hlsl/FinalPassBgMain.hlsl"

            ENDHLSL
        }

        Pass
        {
            Name "SimpleMirrorCaster"
            Tags{ "LightMode" = "SimpleMirrorCaster" }

            Cull Back
            Blend One Zero, Zero One
            Offset 0, 0

            HLSLPROGRAM
#pragma target         3.0
#pragma vertex         vert
#pragma fragment       frag
#pragma fragmentoption ARB_precision_hint_fastest

#define PASS PASS_SIMPLE_MIRROR_CASTER
#define MIRROR_STAGE_BLEND

            #define USE_DIR_SHADOW_COLOR
            #define USE_LIGHTMAP
            #define USE_FILLCOLOR
            #define USE_FOG
            #define USE_SUBCOLOR
            #define USE_LIGHTMAPCOLOR
            #define USE_LIGHTMAPDENSITY
            #define USE_FUR
            #define ENABLE_SHADOW
            #include "hlsl/FinalPassBgMain.hlsl"

            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags{ "LightMode" = "ShadowCaster" }

            HLSLPROGRAM

#pragma vertex vert
#pragma fragment frag

#define PASS PASS_SHADOW_CASTER
//デプス収集やShadowmap生成時にはAndroid固有のデプス補正処理は行わない
#define DISABLE_LOG_DEPTH

#include "hlsl/FinalPassBgMain.hlsl"

            ENDHLSL
        }
    }
}