Shader "Hidden/Gallop/3D/RenderingTool/NormalColor"
{
    Properties
    {
        _MainTex("Tex", 2D) = "white" {}
        _TripleMaskMap("Tex", 2D) = "white" {}
        _Cutoff("_Cutoff", Range(0,1)) = 0.98
        _DepthCutOff("Depth Cut Off", Range(0, 1)) = 0.4
        
        [KeywordEnum(None, Front, Back)] _Cull("Culling", Int) = 2
    }
    
    SubShader
    {
        Cull[_Cull]
        
        Pass
        {
            Tags { "RenderType"="Transparent" "Queue" = "Transparent" }
            
            Blend SrcAlpha OneMinusSrcAlpha

            HLSLPROGRAM
            #pragma target 3.0
            #pragma multi_compile _ USE_CUTOFF
            #pragma multi_compile _ USE_ALPHACLIPING
            #pragma multi_compile _ TOON_CUTOUT
            #pragma multi_compile _ MOB_SHADOW
            #pragma multi_compile _ USE_DEPTH_CUTOUT


            #pragma vertex vert
            #pragma fragment frag
            #include "../../../Resources/Shader/Common/ShaderCommon.hlsl"

            TEXTURE2D_SAMPLER_TO(_MainTex);
            TEXTURE2D_SAMPLER_TO(_TripleMaskMap);

#if defined(USE_CUTOFF) || defined(TOON_CUTOUT)
            float _Cutoff;
#endif

#ifdef USE_DEPTH_CUTOUT
            float _DepthCutOff;
#endif                 

            struct appdata_t
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                float4 pos    : SV_POSITION;
                float4 color  : COLOR;
                float2 uv : TEXCOORD0;
            };

            v2f vert(appdata_t v)
            {
                v2f o;
                o.pos = GallopObjectToClipPos(v.vertex);
                float3 n = TransformObjectToWorldNormal(v.normal,true);
                o.color.rgb = n * 0.5f + 0.5f;
                o.color.a = 1;
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                return o;
            }
            
            float4 frag(v2f i) : COLOR
            {
                float4 baseColor = TEX2D_SAMPLE(_MainTex, i.uv);

#ifdef TOON_CUTOUT
                if (TEX2D_SAMPLE(_TripleMaskMap, i.uv).b < _Cutoff) discard;
#endif
                
#if defined(TOON_SHADING) || defined(TOON_SPECULAR) || defined(TOON_RIM) || defined(TOON_ENVIRONMENT) || defined(TOON_CUTOUT) || defined(TOON_FACE_SHADOW)
                half4 tripleMask = TEX2D_SAMPLE(_TripleMaskMap, i.uv);

#ifdef TOON_CUTOUT
                // カットオフマスク
                half cutoffMask = tripleMask.b;
                if (cutoffMask < _Cutoff) discard;
#ifdef ALPHA_TOON
                baseColor.a *= (cutoffMask - _Cutoff) / (1 - _Cutoff);
#endif
#endif
#endif
            
                float4 c = i.color;

                // MobShadow用
#if defined(MOB_SHADOW)
                clip(baseColor.a - 0.3);
#endif
                
                // 背景描画用(GallopBgMain.hlsl参照)
#if defined(USE_CUTOFF)
                clip(baseColor.a - 0.0001 - _Cutoff);
#endif

                // 背景描画用(GallopBgMain.hlsl参照)
#if defined(USE_ALPHACLIPING)
                if(baseColor.a <= .0001f) discard;
#endif

                // 背景描画用(GallopBgMain.hlsl参照)
#ifdef USE_DEPTH_CUTOUT
                // 色がのっていない部分はDepthを書き込まないようにする
                // エフェクトによってその程度は違うのでパラメータで調整する
                if (baseColor.a < _DepthCutOff) discard; // アルファマスクを使う場合はアルファチャンネルで判定
#endif //USE_DEPTH_CUTOUT                  
                
                c.a = 1 - step(baseColor.a, 0.00001f);
                return c;
            }

            ENDHLSL
        }
    }
}

