// RenderingToolのFinalPass描画用の背景シェーダ リッチ芝のレンダリング結果を一致させるために追加
// GallopBgMain.hlslをコピペしてFur(リッチ芝)の処理だけ変更
#ifndef _GALLOP_RENDERING_BGMAIN_HLSLINC_
#define _GALLOP_RENDERING_BGMAIN_HLSLINC_

//UNITY_SHADER_NO_UPGRADE

#include "Assets/_Gallop/Resources/Shader/Common/ShaderCommon.hlsl"
#include "Assets/_Gallop/Resources/Shader/Common/FogCommon.hlsl"
#include "Assets/_Gallop/Resources/Shader/3D/MirrorAndShadow/hlsl/SimpleMirrorCast.hlsl"
#include "Fur.hlsl"

#define LIGHTMAP_BIAS (4)//Maya上で生成されたLightMapは0.5が基準値となっているため、２倍する必要がある。テクスチャと頂点カラーぶんがあるので４倍となる。

#if defined(USE_WAVE)

#define SCALE_TIME (5)
#define BIAS_SCALE (2)
#define _WaveCurrentTime _AppTime

#endif

CBUFFER_START(UnityPerMaterial)

#if defined(USE_EMISSIVE)
TEXTURE2D_SAMPLER(_EmissiveTex);
uniform half4 _EmissiveColor;
#endif

TEXTURE2D_SAMPLER_TO(_MainTex);

#if defined(USE_SUBCOLOR) || defined(PASS_OPTION_DEFINE_SUBCOLOR)
TEXTURE2D_SAMPLER(_SubTex);
uniform half _MultimapRate;
#endif

#if defined(USE_GRASS) || defined(PASS_OPTION_DEFINE_GRASS)
uniform float _WindScale;       //力
uniform float4 _WindParam;      //風パラメータ
TEXTURE2D_SAMPLER(_WindDirTex); //風向きが入っているテクスチャ
#endif

#if defined(USE_LIGHTMAP)
TEXTURE2D_SAMPLER(_LightTex);
#endif

#if defined(USE_UVSCROLL)
float4 _ScrollSpeedUV;
#endif

#if defined(USE_UVANIME)
//x:横枚数　y:縦枚数　z:表示番号
float4    _AnimeNo;
#endif

#if defined(USE_PROJECTIONSHADOW)
TEXTURE2D_SAMPLER(_ProjectionShadowMap);
float4x4  _ProjectionViewProj;
#endif

#if defined(USE_SAND)
TEXTURE2D_SAMPLER(_SandSubTex);
#endif

#if defined(PASS_OPTION_DEFINE_MULCOLOR)

#if defined(ENABLE_SHADOW)
fixed4 _MulColor2;
#else
fixed4 _MulColor0;
#endif

#else

#if defined(USE_FILLCOLOR) || defined(PASS_OPTION_DEFINE_FILLCOLOR)

#if defined(ENABLE_SHADOW)
//影が落ちるものはProperty名を変える(_MulColor0だと既存のものに影響を与えるため)
fixed4 _MulColor2;
#else
fixed4 _MulColor0;
#endif

#endif

#endif

#if defined(USE_CUTOFF) || defined(PASS_OPTION_DEFINE_CUTOFF)
uniform float _Cutoff;
#endif

#if defined(USE_2NDFOG)

float4 _FogColor;
float4 _FogMinDistance;
float4 _FogMaxDistance;
float4 _FogWorld_Origin;
float _MaxDensity;
float _MaxHeight;

#endif

#ifdef USE_DEPTH_CUTOUT
fixed _DepthCutOff;
#endif

#if defined(ENABLE_SHADOW)
#include "Assets/_Gallop/Resources/Shader/3D/MirrorAndShadow/hlsl/MultiLightShadowProps.hlsl"
#endif

CBUFFER_END

#if defined(ENABLE_SHADOW)
//Shadow有効時にはサポート系の関数も提供する
#include "Assets/_Gallop/Resources/Shader/3D/MirrorAndShadow/hlsl/MultiLightShadow.hlsl"
#endif

//以下はGlobal変数
#if defined(USE_UVANIME)||defined(USE_LIGHTMAP)||defined(USE_WAVE) || defined(USE_UVSCROLL)
uniform float _AppTime;
#define _CurrentTime _AppTime
#endif

#if defined(USE_LIGHTMAPCOLOR)
float4 _Global_LightmapColor;
float4 _Global_LightmapShadowColor;
#endif

#if defined(USE_LIGHTMAPDENSITY)
float4 _Global_LightmapDensityAddColor;
float4 _Global_LightmapModulateColor;
#endif

#ifdef PASS_OPTION_SKIP_PROGRAM
    //関数などプログラム定義をスキップする
#else

struct appdata
{
    float3 vertex : POSITION;
    float2 uv: TEXCOORD0;
#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)
    float2 uv2: TEXCOORD1;
#endif

#if defined(USE_UVSCROLL_ALPHA)||defined(USE_GRASS)||defined(USE_FUR)
    float2 uv3 : TEXCOORD2;
#endif

#if defined(USE_WAVE)||defined(USE_FUR)
    float4 normal : NORMAL;
#endif

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)||defined(USE_WAVE)||defined(USE_FUR)
    float4 color : COLOR;
#endif
};

struct v2f
{
    float4 pos : SV_POSITION;
    float2 uv : TEXCOORD0;

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)||defined(USE_WAVE)||defined(USE_FUR)
    float4 color:TEXCOORD1;
#endif

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)
    float2 uv2 : TEXCOORD2;
#endif

#if defined(USE_FOG)
    float2 fogParam : TEXCOORD3;
#endif

#if defined(USE_PROJECTIONSHADOW)
    float4 uv_shadow : TEXCOORD4;
#endif

#if defined(USE_SAND)||defined(USE_FUR)
    float3 mixLevel : TEXCOORD5;
#endif

#if defined(USE_UVSCROLL_ALPHA)||defined(USE_FUR)
    float2 uv6 : TEXCOORD6;
#endif

#if defined(ENABLE_SHADOW)

#if defined(BUILDIN_PIPELINE)
    UNITY_SHADOW_COORDS(7)
#else
    float4 shadowCoord : TEXCOORD7;
#endif

    float3 posWorld : TEXCOORD8;

#endif

#if defined(USE_FRAGDEPTH) && !(UNITY_REVERSED_Z)
    float logZ : TEXCOORD9;
#endif
};

struct FragmentBufferOut
{
    fixed4 color : SV_Target;
#if defined(USE_FRAGDEPTH) && !(UNITY_REVERSED_Z)
    float depth : SV_Depth;
#endif
};

#ifdef USE_GRASS
//指定方向に向かせた時の位置を計算する
inline float3 LookAt(float3 pos,float3 diff)
{
    //向きを決める
    float length = (diff.x * diff.x) + (diff.y * diff.y) + (diff.z * diff.z);

    // 長さ0で正規化出来ない
    if (length <= 0.0000001)
        return pos;

    float3 zaxis = normalize(diff);
    float3 xaxis = normalize(cross(float3(0, 1, 0), zaxis));
    float3 yaxis = cross(zaxis, xaxis);
    float4x4 lookAtMatrix =
    {
        xaxis.x,yaxis.x,zaxis.x, 0,
        xaxis.y,yaxis.y,zaxis.y, 0,
        xaxis.z,yaxis.z,zaxis.z, 0,
              0,      0      ,0, 1,
    };

    //回転行列が既に符号を考慮した行列となっているので、absで+方向にする事で符号を考慮させる
    return pos + mul(lookAtMatrix, float4(abs(diff),1)).xyz;
}
#endif

v2f vertMain(in appdata v)
{
    v2f o = (v2f)0;

    float3 localPos = v.vertex;

#if defined(USE_GRASS)
    //ローカル空間内で計算する
    //XZをテクスチャ参照元にする
    float2 uv = (localPos.xz * _WindParam.xy) + _WindParam.zw;

    //カラー0.5を基準にする
    float4 ofsDiff = ((TEX2D_LOD(_WindDirTex, uv, 0) * 2.0) - 1.0) * _WindScale;
    float3 moveDiff = (ofsDiff.rgb * v.uv3.x);    //uv3に影響力が入っている

    //新しい向き方向に変更する
    localPos.xyz = LookAt(localPos.xyz, moveDiff);
#endif

    VertexPositionInputs vpi = GallopGetVertexPositionInputs(localPos);
    o.pos = vpi.positionCS;
    o.uv = TRANSFORM_TEX(v.uv, _MainTex).xy;


#if defined(USE_FUR)
    FurOutput furOutput = FurVert(vpi.positionCS, vpi.positionWS, v.normal, v.uv, v.uv3);
    o.pos = furOutput.vertex;
    o.mixLevel = furOutput.mixLevel;
    o.uv6 = furOutput.uv6;
#endif
    
#if defined(USE_UVANIME)
    float animeNo = _CurrentTime / _AnimeNo.z;
    //_AnimeNo.zから場所を求める
    int x_no = (int)fmod(animeNo, _AnimeNo.x);
    int y_no = -(int)(animeNo / _AnimeNo.x);

    fixed w = 1.0 / _AnimeNo.x;
    fixed h = 1.0 / _AnimeNo.y;

    o.uv.x = (o.uv.x * w) + (w * x_no);
    o.uv.y = ((o.uv.y - 1.0) * h) + (h * y_no);
#endif

#if defined(USE_UVSCROLL)
    o.uv = o.uv + (_ScrollSpeedUV.xy * _CurrentTime);
#endif

#if defined(USE_LIGHTMAP)
    o.uv2 = v.uv2;

#if defined(USE_UVSCROLL)
    o.uv2 = o.uv2 + (_ScrollSpeedUV.zw * _CurrentTime);
#endif
#endif

#if defined(USE_UVSCROLL_ALPHA)
    o.uv6 = v.uv3;
#endif

#if defined(USE_FOG)
    o.fogParam = CalcFogParamFromVertex(float4(v.vertex, 1.0));
#endif

#if defined(USE_LIGHTMAP)||defined(USE_VERTEXCOLOR)||defined(USE_FUR)
    o.color = v.color;
#endif

#if defined(USE_PROJECTIONSHADOW)
    float3 world = vpi.positionWS;
    float4 pos = mul(_ProjectionViewProj, float4(world, 1));
    o.uv_shadow = ComputeScreenPos(pos);
#endif

#if defined(USE_SAND)
    o.mixLevel = 0.0;
    if (v.vertex.y < 0.0)
    {
        o.mixLevel.x = -v.vertex.y * 6;
    }
#endif

#if defined(ENABLE_SHADOW)

#if defined(BUILDIN_PIPELINE)
    UNITY_TRANSFER_SHADOW(o, half2(0, 0));
#else
    o.shadowCoord = GetShadowCoord(vpi);
#endif

    o.posWorld = vpi.positionWS;

#endif

#if defined(USE_FRAGDEPTH) && !(UNITY_REVERSED_Z)
    //ラチ板のような長いポリゴンの場合、線形に補間されLog曲線を描かないので
    //fragment shader側でピクセルに対応したDepthを改めて書き込む
    o.logZ = (o.pos.w * LOG_DEPTH_SCALE) + 1;
#endif
    return o;
}

v2f vertDepthMain(in appdata v)
{
    //デプス収集パス
    v2f o = (v2f)0;
    float3 localPos = v.vertex;
    VertexPositionInputs vpi = GallopGetVertexPositionInputs(localPos);
    o.pos = vpi.positionCS;

    return o;
}

v2f vertSimpleMirror(in appdata v)
{
    v2f o = (v2f)0;

#ifdef PASS_OPTION_MIRROR_TRANSPARENT

    o = vertMain(v);

#else

    //軽量ミラー投影
    BindSimpleMirrorCastVertex bindMirror;
    bindMirror.PositionOS = v.vertex;
    bindMirror.UV = v.uv;
    float4 positionCS;
    float2 uv;
    SimpleMirrorVertex(bindMirror, _MainTex_ST, positionCS, uv);
    o.pos = positionCS;
    o.uv = uv;

#endif

    return o;
}

v2f vert(appdata v)
{
    v2f o = (v2f)0;
#if (PASS == PASS_SHADOW_CASTER || PASS == PASS_DEPTH_CASTER)

    o = vertDepthMain(v);

#elif (PASS == PASS_SIMPLE_MIRROR_CASTER)

    o = vertSimpleMirror(v);

#else

    o = vertMain(v);

#endif  //end PASS

    return o;
}

//メインフラグメント
FragmentBufferOut fragMain(v2f i)
{
#if defined(USE_FUR)
    const float layerRateInCur = i.mixLevel.y;      
    const float lodOffset = i.mixLevel.z;
    FurDiscard(i.mixLevel, i.color, i.uv6, layerRateInCur, lodOffset);
#endif
    
#if defined(USE_MIPMAPBIAS)
    float4 uv = float4(i.uv.x, i.uv.y, 0, -2);//mipmapかかりにくくする
    fixed4 originColor = TEX2D_BIAS(_MainTex, uv.xy, uv.w);//tex2DlodはOpenGLES2.0だと使えない
#else
    fixed4 originColor = TEX2D_SAMPLE(_MainTex, i.uv);
#endif


#if defined(USE_SAND)
    fixed4 colSandSub = TEX2D_SAMPLE(_SandSubTex, i.uv);
    originColor.rgb = (1.0 - i.mixLevel.x) * originColor.rgb + i.mixLevel.x * colSandSub.rgb;
#endif

#if defined(USE_VERTEXCOLOR)
    originColor *= i.color;
#endif
#if defined(USE_LIGHTMAP)
    //ライトマップ使用時、頂点カラーのaには半透明情報が入っているので、aだけ先に適応する（rgbにはライト強度の情報が入っている）
    originColor.a *= i.color.a;
#endif

#if defined(USE_CUTOFF)
    clip(originColor.a - 0.0001 - _Cutoff);
#endif

#if defined(USE_SUBCOLOR)
#if defined(USE_MIPMAPBIAS)
    fixed4 subColor = TEX2D_BIAS(_SubTex, uv.xy, uv.w);
    originColor = lerp(originColor, subColor, _MultimapRate);
#else
    fixed4 subColor = TEX2D_SAMPLE(_SubTex, i.uv);
    originColor = lerp(originColor, subColor, _MultimapRate);
#endif
#endif

#if defined(USE_LIGHTMAP)
#if defined(USE_MIPMAPBIAS)
    float4 uv2 = float4(i.uv2.x, i.uv2.y, 0, -1);
    fixed4 lightmap = TEX2D_BIAS(_LightTex, uv2.xy, uv2.w);
#else
    fixed4 lightmap = TEX2D_SAMPLE(_LightTex, i.uv2);
#endif

    float4 l = lightmap;

    l.rgb *= i.color.rgb;
    l.rgb *= LIGHTMAP_BIAS;

#if defined(USE_LIGHTMAPCOLOR)
    l.rgb = lerp(_Global_LightmapShadowColor, _Global_LightmapColor, l.r).rgb;
#endif

#if defined(USE_LIGHTMAPDENSITY)
    l.rgb = (l.rgb * _Global_LightmapModulateColor.rgb) + _Global_LightmapDensityAddColor.rgb;
#endif

    originColor.rgb *= l.rgb;
#endif

#if defined(USE_R_ALPHA)
    originColor.a = originColor.r;
#endif

#if defined(USE_G_COLOR)
    originColor.rgb = originColor.ggg;
#endif

#if defined(USE_FOG)
    originColor.rgb = ApplyFog(i.fogParam.x, i.fogParam.y, originColor).rgb;
#endif

#if defined(USE_PROJECTIONSHADOW)
    float2 xy = (i.uv_shadow / i.uv_shadow.w).xy;
    float _SilhouetteSize = 1.0;
    float wid = _SilhouetteSize / 568.0;
    float hei = _SilhouetteSize / 320.0;
    float edgeValue = 0.0;
    edgeValue += TEX2D_SAMPLE(_ProjectionShadowMap, xy + float2(wid, 0)).a;
    edgeValue += TEX2D_SAMPLE(_ProjectionShadowMap, xy + float2(-wid, 0)).a;
    edgeValue += TEX2D_SAMPLE(_ProjectionShadowMap, xy + float2(0, hei)).a;
    edgeValue += TEX2D_SAMPLE(_ProjectionShadowMap, xy + float2(0, -hei)).a;
    originColor.rgb *= 1.0 - (edgeValue * 0.25 * 0.2 * (1.0 - xy.y));//shadow.rgb;
#endif

#if defined(USE_FILLCOLOR)
#if defined(ENABLE_SHADOW)
    originColor *= _MulColor2;
#else
    originColor *= _MulColor0;
#endif
#endif

#if defined(USE_UVSCROLL_ALPHA)
    fixed4 alphaMap = TEX2D_SAMPLE(_MainTex, i.uv6);
    originColor *= alphaMap.a;
#endif

#if defined(ENABLE_SHADOW)

#if defined(BUILDIN_PIPELINE)

    float shadowRate = UNITY_SHADOW_ATTENUATION(i, i.posWorld);

#else

    //LightMapを使用する場合はSAMPLE_SHADOWMASK(input.lightmapUV)からShadowMaskを求める必要がある
    //無い場合はunity_ProbesOcclusionから取得
    half4 shadowMask = unity_ProbesOcclusion;
    Light lightData = GetMainLight(i.shadowCoord);
    float shadowRate = lightData.shadowAttenuation;

    //URPでは_ADDITIONAL_LIGHT_SHADOWSでまとめてライト情報が来る
#ifdef _ADDITIONAL_LIGHT_SHADOWS
    int lightCount = GetAdditionalLightsCount();
    for (int index = 0;index < lightCount;index++)
    {
        lightData = GetAdditionalLight(index, i.posWorld, shadowMask);
        shadowRate *= lightData.shadowAttenuation;
    }
    shadowRate = saturate(shadowRate);
#endif

#endif

    fixed3 shadowColor = GetShadowColor(i.posWorld, shadowRate);
    originColor.rgb = shadowColor * originColor.rgb;

#endif

    //発光
#if defined(USE_EMISSIVE)
    originColor.rgb += TEX2D_SAMPLE(_EmissiveTex, i.uv).rgb * _EmissiveColor.rgb;
#endif

    FragmentBufferOut o;
    o.color = originColor;
#if defined(USE_FRAGDEPTH) && !(UNITY_REVERSED_Z)
    //ラチ板のような長いポリゴンの場合、線形に補間されLog曲線を描かないので
    //fragment shader側でピクセルに対応したDepthを改めて書き込む
    o.depth = log(i.logZ) * (1.0 / _GlobalFarClipLog);
#endif

#if defined(USE_FUR)
    //根元を暗くする+カメラから離れるほどOcclusionを弱くする
    float occlusion = lerp(1.0 - saturate(_Occlusion-lodOffset), 1.0, layerRateInCur);
    o.color = saturate(lerp(originColor, (originColor * occlusion) + _Brightness, i.color.a));
#endif

#if defined(USE_ALPHACLIPING)
    // レースの木のふちで雪等がZTestに負けて消える問題解消用。
    if (o.color.a <= .0001f) discard;
#endif

#ifdef USE_DEPTH_CUTOUT
    // aがのっていない部分はDepthを書き込まないようにする
    if (o.color.a < _DepthCutOff) discard;
#endif //USE_DEPTH_CUTOUT

    return o;
}

//軽量ミラー投影
FragmentBufferOut fragSimpleMirror(in v2f i)
{
    FragmentBufferOut o = (FragmentBufferOut)0;

#ifdef PASS_OPTION_MIRROR_TRANSPARENT

    o = fragMain(i);

#else

#ifdef USE_EMISSIVE
    o.color = SimpleMirrorFragment(TEXTURE2D_ARGS(_MainTex, sampler_MainTex), i.uv, 1,
        TEXTURE2D_ARGS(_EmissiveTex, sampler_EmissiveTex), _EmissiveColor);
#else
    o.color = SimpleMirrorFragment(TEXTURE2D_ARGS(_MainTex, sampler_MainTex), i.uv, 1);
#endif

    //元々depth補正はかかっていないので何もしていない
#endif

    return o;
}

FragmentBufferOut frag(v2f i)
{
    FragmentBufferOut o;

#if (PASS == PASS_SHADOW_CASTER || PASS == PASS_DEPTH_CASTER)

    o = (FragmentBufferOut)0;

#elif (PASS == PASS_SIMPLE_MIRROR_CASTER)

    //軽量ミラー投影
    o = fragSimpleMirror(i);

#else

    o = fragMain(i);

#endif

    return o;
}

FragmentBufferOut fragUseAlphaDepth(v2f i)
{
    fixed4 originColor = TEX2D_SAMPLE(_MainTex, i.uv);

#if defined(USE_VERTEXCOLOR)
    originColor *= i.color;
#endif

#if defined(USE_LIGHTMAP)
    //ライトマップ使用時、頂点カラーのaには半透明情報が入っているので、aだけ先に適応する（rgbにはライト強度の情報が入っている）
    originColor.a *= i.color.a;
#endif

#if defined(USE_CUTOFF)
    clip(originColor.a - 0.0001 - _Cutoff);
#endif

#if defined(USE_R_ALPHA)
    originColor.a = originColor.r;
#endif

#if defined(USE_FILLCOLOR)
#if defined(ENABLE_SHADOW)
    originColor *= _MulColor2;
#else
    originColor *= _MulColor0;
#endif
#endif

#if defined(USE_UVSCROLL_ALPHA)
    fixed4 alphaMap = TEX2D_SAMPLE(_MainTex, i.uv6);
    originColor *= alphaMap.a;
#endif

    FragmentBufferOut o;
    o.color = originColor;
#if defined(USE_FRAGDEPTH) && !(UNITY_REVERSED_Z)
    //ラチ板のような長いポリゴンの場合、線形に補間されLog曲線を描かないので
    //fragment shader側でピクセルに対応したDepthを改めて書き込む
    o.depth = log(i.logZ) * (1.0 / _GlobalFarClipLog);
#endif


#if defined(USE_ALPHACLIPING)
    // レースの木のふちで雪等がZTestに負けて消える問題解消用。
    if(o.color.a <= .0001f) discard;
#endif

#ifdef USE_DEPTH_CUTOUT
    // 色がのっていない部分はDepthを書き込まないようにする
    // エフェクトによってその程度は違うのでパラメータで調整する
    if (o.color.a < _DepthCutOff) discard; // アルファマスクを使う場合はアルファチャンネルで判定
#endif //USE_DEPTH_CUTOUT

    return o;
}


#if defined(USE_WAVE)

//揺れシェーダ用の処理
v2f vertWave(appdata v)
{
    v2f o;

    //Wave
    float3 worldPos = TransformObjectToWorld(v.vertex);
    float3 pos = worldPos;

    fixed3 axis = v.normal.xyz;
    fixed3 move = cos((_WaveCurrentTime * SCALE_TIME) + (pos.x + pos.y + pos.z)) * v.color.a * axis;
    pos += move;
    pos = TransformWorldToObject(pos);

    //頂点
    o.pos = GallopObjectToClipPos(pos);

    //カラーテクスチャ
    o.uv = TRANSFORM_TEX(v.uv, _MainTex);

    //頂点カラー
    o.color = v.color;

#if defined(USE_FOG)
    float3 viewPos = TransformLocalToView(v.vertex);
    o.fogParam = CalcFogParam(worldPos.y, -viewPos.z, worldPos);
#endif

    return o;
}

fixed4 fragWave(v2f i) : COLOR
{
    //トータル
    fixed4 originColor = 1.0;

    //カラーテクスチャ
    originColor = TEX2D_SAMPLE(_MainTex, i.uv);

    //頂点カラーのRにブレンド率が入っている
#if defined(USE_LIGHTMAPCOLOR)
    originColor.rgb *= lerp(_Global_LightmapShadowColor, _Global_LightmapColor, i.color.rgb.r * BIAS_SCALE).rgb;
#else
    originColor.rgb *= i.color.rgb;
#endif

#if defined(USE_FILLCOLOR)
    originColor *= _MulColor0;
#endif

#if defined(USE_LIGHTMAPDENSITY)
    originColor.rgb = ((originColor * _Global_LightmapModulateColor) + _Global_LightmapDensityAddColor).rgb;
#endif

#if defined(USE_FOG)
    originColor.rgb = ApplyFog(i.fogParam.x, i.fogParam.y, originColor).rgb;
#endif

    return originColor;
}

#endif

//LensFlare処理
#if defined(USE_LENSFLARE)

//Instancingにて一度にまとめてレンダリングしている
struct VertexLayout
{
    float3 PositionOS : POSITION;
    UNITY_VERTEX_INPUT_INSTANCE_ID
};

struct Varying
{
    float4 PositionCS : SV_POSITION;
    float2 UV : TEXCOORD0;
    float4 Color : TEXCOORD1;
};

UNITY_INSTANCING_BUFFER_START(Props)
    UNITY_DEFINE_INSTANCED_PROP(float4, _Color)
    UNITY_DEFINE_INSTANCED_PROP(float4, _UV)
    UNITY_DEFINE_INSTANCED_PROP(float4, _Size)
UNITY_INSTANCING_BUFFER_END(Props)

void calcVertexElement(uint vid,out float2 outUV, out float2 outSize)
{
    float4 uv01 = UNITY_ACCESS_INSTANCED_PROP(Props, _UV);
    float4 size01 = UNITY_ACCESS_INSTANCED_PROP(Props, _Size);
    float2 uv;
    float2 size;
    if (vid == 0)
    {
        uv = uv01.zy;
        size = -size01.xy;
    }
    else if (vid == 1)
    {
        uv = uv01.xy;
        size.x = size01.y;
        size.y = -size01.x;
    }
    else if (vid == 2)
    {
        uv = uv01.xw;
        size = size01.xy;
    }
    else
    {
        uv = uv01.zw;
        size.x = -size01.y;
        size.y = size01.x;
    }

    outUV = uv;
    outSize = size;
}

Varying vertexLensFlare(VertexLayout v,uint vid : SV_VertexID)
{
    Varying o = (Varying)0;

    UNITY_SETUP_INSTANCE_ID(v);
    float2 uv;
    float2 size;
    calcVertexElement(vid, uv, size);
    //sizeがWorld上での位置になっているので直接計算する
    float3 position = TransformObjectToWorld(v.PositionOS);
    position.xy = position.xy + size;
    o.PositionCS = TransformWorldToHClip(position);
    o.PositionCS = GetLogDepthPosition(o.PositionCS);   //Depth補間
    o.UV = TRANSFORM_TEX(uv, _MainTex);
    o.Color = UNITY_ACCESS_INSTANCED_PROP(Props, _Color);

    return o;
}

half4 fragLensFlare(Varying i) : COLOR
{
    return TEX2D_SAMPLE(_MainTex,i.UV) * i.Color;
}

#endif

#endif  //end PASS_OPTION_SKIP_PROGRAM

#endif  //end _GALLOP_RENDERING_BGMAIN_HLSLINC_
