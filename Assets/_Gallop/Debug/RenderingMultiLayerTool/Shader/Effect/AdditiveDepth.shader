// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:10Z
shader "Hidden/Gallop/3D/RenderingTool/Effect/Additive/AdditiveDepth"
{
    Properties
    {
        _Color  ( "Color",       Color ) = ( 0.5, 0.5, 0.5, 0.5 )
        _MainTex( "Base (RGBA)", 2D )    = "white" {}
        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2
        _DepthCutOff("Depth Cut Off", Range(0, 1)) = 0.5

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }
    
    SubShader
    {
        Tags
        {
            "Queue"           = "AlphaTest"
            "RenderType"      = "TransparentCutout"
            "IgnoreProjector" = "True"
            "LightMode"       = "UniversalForward"
        }
        
        LOD 100

        Pass
        {
            Name "Additive"
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }

            Blend SrcAlpha One
            Cull[_CullMode]
            ZTest[_ZTestMode]
            Lighting Off
            ZWrite Off
            Fog { Color (0,0,0,0) }

            HLSLPROGRAM
            #pragma target 3.0
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest

            #define USE_TEXTURE0     // for texture
            #define USE_COLOR        // for color
            #define USE_VERTEX_COLOR // for vertex color

            #include "../../../../Resources/Shader/Common/Common.hlsl"
            #include "../../../../Resources/Shader/Common/Effect.hlsl"
            ENDHLSL
        }

        // DepthCasterのパス
        Pass
        {
            Name "DepthCaster"
            Tags
            {
                "LightMode"  = "ShadowCaster"
            }

            Cull Off
            ZWrite On

            HLSLPROGRAM
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest

            #define USE_TEXTURE0     // for texture
            #define USE_COLOR        // for color
            #define USE_VERTEX_COLOR // for vertex color
            #define DEPTH_CASTER     // Depth書き込み用

            #include "../../../../Resources/Shader/Common/Common.hlsl"
            #include "../../../../Resources/Shader/Common/Effect.hlsl"
            ENDHLSL
        }
    }
}
