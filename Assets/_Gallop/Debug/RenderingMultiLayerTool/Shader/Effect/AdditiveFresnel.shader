// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:10Z
Shader "Hidden/Gallop/3D/RenderingTool/Effect/Additive/AdditiveFresnel"
{
    Properties
    {
        _Color   ( "Color", Color )                      = ( 0.5, 0.5, 0.5, 0.5 )
        _MainTex ( "Base (RGBA)", 2D )                   = "white" {}
        _Strength("Strength", Range(0.0, 10.0)) = 1.0
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
            //[Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2
            [HideInInspector]_FillColor("Fill Color", Color) = (1,1,1,1)
            _StencilMask("Stencil Mask", int) = 0
            [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"           = "Transparent" 
            "RenderType"      = "Transparent"
            "IgnoreProjector" = "True"
        }
        
        //render state
        Blend SrcAlpha One
        Cull [_CullMode]
        //ZTest [_ZTestMode]  
        ZTest LEqual
        Lighting Off 
        ZWrite Off 
        Fog { Color (0,0,0,0) }
        LOD 100    
        
        HLSLINCLUDE
        #define USE_MAINTEX      // for texture
        #define USE_COLOR        // for color
        #define USE_VERTEX_COLOR // for vertex color
        #define USE_FRESNEL      // for fresnel
        #define USE_FILLCOLOR    // for fill color

        #include "../../../../Resources/Shader/Common/Common.hlsl"
        #include "../../../../Resources/Shader/Common/Effect.hlsl"
        ENDHLSL

        Pass    //半透明重なり対策
        {
            Zwrite On
            Lighting OFF

            Tags { "LightMode" = "PreUniversalForward" }

            HLSLPROGRAM
            float4 VS_Object(float4 positionOS : POSITION) : SV_POSITION
            {
                return VS_POSITION(positionOS);
            }
            half4 PS_Object() : COLOR { return 0; }
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }

        Pass 
        {
            Tags { "LightMode" = "UniversalForward" }

            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
    }
}
