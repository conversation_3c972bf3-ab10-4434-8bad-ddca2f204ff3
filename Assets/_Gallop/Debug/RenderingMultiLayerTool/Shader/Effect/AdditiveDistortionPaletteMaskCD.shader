// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:10Z
shader "Hidden/Gallop/3D/RenderingTool/Effect/AdditiveCustom/AdditiveDistortionPaletteMaskCD"
{
    Properties
    {
        _Color  ( "Color",       Color ) = ( 1, 1, 1, 1 )
        _MainTex( "Base (RGBA)", 2D )    = "white" {}
        _MaskTex( "Mask (RGBA)", 2D )    = "white" {}
        _DistortionTex( "Distortion (RGBA)", 2D )    = "white" {}
        _DeformTex( "DistortionMask (RGBA)", 2D )    = "white" {}
        _DistortionScrollU ( "DistortionScrollU", Range(-100.0, 100.0) )  = 0.0
        _DistortionScrollV ( "DistortionScrollV", Range(-100.0, 100.0) )  = 0.0
        //_DistortionScaleU ( "DistortionScaleU", Range(-1.0, 1.0) )  = 0.0
        //_DistortionScaleV ( "DistortionScaleV", Range(-1.0, 1.0) )  = 0.0
        _DistortionScaleP ( "DistortionMaskPosition", Range(-1.0, 1.0) )  = 0.0
        //_PaletteScroll("PaletteScroll", Range(-1.0,0.0)) = 0.0
        _ColorSelect("ColorSelect", Range(0.0,1.0)) = 0.0
        //_Hue ("H", Float) = 0
           //_Sat ("S", Float) = 1
           //_Val ("V", Float) = 1
        [HideInInspector]_FillColor ("Fill Color", Color) = (1,1,1,1)
        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }
    
    SubShader
    {
        Tags
        {
            "Queue"           = "Transparent" 
            "RenderType"      = "Transparent"
            "IgnoreProjector" = "True"
            "LightMode" = "UniversalForward"
        }
        
        //render state
        Blend SrcAlpha One
        Cull[_CullMode]
        ZTest[_ZTestMode]
        Lighting Off
        ZWrite Off
        LOD 100    
        
        HLSLINCLUDE
        #define USE_DISTORTIONMASK_CUSTOM    // for distortion
        #define USE_TEXTURE0       // for texture
        #define USE_MASKTEX      // for mask
        #define USE_COLOR         // for color
        #define USE_VERTEX_COLOR  // for vertex color
        #define USE_DEFAULT_RANGE // for default range
        #define USE_FILLCOLOR     // for fill color
        #define USE_PALETTE_CUSTOM2     // for color
        #define USE_HST_CUSTOM     // for color
        #define USE_CUSTOM     // for custumdata
        #define USE_CUSTOM2     // for custumdata

        #include "../../../../Resources/Shader/Common/Common.hlsl"
        #include "../../../../Resources/Shader/Common/Effect.hlsl"
        ENDHLSL
        
        Pass 
        {
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
    }
}
