// timestamp by EditorEffectShaderUpdateTimeStamp : 2020-02-27 14:44:10Z
shader "Hidden/Gallop/3D/RenderingTool/Effect/TransparentCustom/TransparentDistortionCD"
{
    Properties
    {
        _Color  ( "Color",       Color ) = ( 1, 1, 1, 1 )
        _MainTex( "Base (RGBA)", 2D )    = "white" {}
        _DistortionTex( "Distortion (RGBA)", 2D )    = "white" {}
        _DistortionScrollU ( "DistortionScrollU", Range(-100.0, 100.0) )  = 0.0
        _DistortionScrollV ( "DistortionScrollV", Range(-100.0, 100.0) )  = 0.0
        [Enum(UnityEngine.Rendering.CullMode)]_CullMode("Culling", Float) = 0
        [Enum(Off,2,On,0)]_ZTestMode("ZIgnore", Float) = 2     
        [HideInInspector]_FillColor ("Fill Color", Color) = (1,1,1,1)
        _StencilMask("Stencil Mask", int) = 0
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8    //UnityEngine.Rendering.CompareFunction.Always

        [HideInInspector] _Timer("_Timer",Float) = 0
        [HideInInspector] _WaveRate("_WaveRate",Float) = 0
    }
    
    SubShader
    {
        Tags
        {
            "Queue"           = "Transparent" 
            "RenderType"      = "Transparent"
            "IgnoreProjector" = "True"
            "LightMode"       = "UniversalForward"
        }
        
        //render state
        Blend SrcAlpha OneMinusSrcAlpha
        Cull [_CullMode]
        ZTest [_ZTestMode] 
        //Cull Off 
        Lighting Off 
        ZWrite Off
        LOD 100    
        
        HLSLINCLUDE
        #define USE_DISTORTION_CUSTOM   // for distortion
        #define USE_TEXTURE0       // for texture
        #define USE_COLOR         // for color
        #define USE_VERTEX_COLOR  // for vertex color
        #define USE_DEFAULT_RANGE // for default range
        #define USE_FILLCOLOR     // for fill color
        #define USE_CUSTOM    // for fill color

        #include "../../../../Resources/Shader/Common/Common.hlsl"
        #include "../../../../Resources/Shader/Common/Effect.hlsl"
        ENDHLSL
        
        Pass 
        {
            Stencil
            {
                Ref[_StencilMask]
                Comp[_StencilComp]
            }
            HLSLPROGRAM
            #pragma vertex   VS_Object
            #pragma fragment PS_Object
            #pragma fragmentoption ARB_precision_hint_fastest
            ENDHLSL
        }
    }
}
