Shader "Hidden/Gallop/3D/RenderingTool/ImpostorNormalColor" 
{
    Properties
    {
        _MainTex("MainTex", 2D) = "white" {}
        _AxisFramesX("AxisFramesX", Range(1, 32)) = 8
        _AxisFramesY("AxisFramesY", Range(1, 32)) = 8
        _UpperAngle("UpperAngle", Range(-90 , 90)) = 60
        _LowerAngle("LowerAngle", Range(-90 , 90)) = 30
        _LightmapColorRate("LightmapColorRate", Range(0 , 1)) = 1
        _Cutoff("_Cutoff", Range(0,1)) = 0.98
        
        [NoScaleOffset]_MaskColorTex("MaskColorTex", 2D) = "gray" {}
        _MaskColorR1("MaskColorR1", Color) = (1,1,1,1)
        _MaskColorG1("MaskColorG1", Color) = (1,1,1,1)        
    }

    SubShader
    {
        Tags { "RenderType"="Transparent" "Queue" = "Transparent" }

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            
            HLSLPROGRAM
            #pragma target         3.0
            #pragma vertex         VSMain
            #pragma fragment       PSMain
            #pragma multi_compile_instancing

            #include "../../../../Resources/Shader/Common/ShaderCommon.hlsl"
            
            struct appdata
            {
                float3 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                float4 color  : COLOR;
                float2 uv : TEXCOORD0;
            };


            CBUFFER_START(UnityPerMaterial)
                TEXTURE2D_SAMPLER_TO(_MainTex);
                int _AxisFramesX;
                int _AxisFramesY;
                float _UpperAngle;
                float _LowerAngle;
                float _Cutoff;
                float _LightmapColorRate;

                TEXTURE2D_SAMPLER(_MaskColorTex);
                uniform float4 _MaskColorR1;
                uniform float4 _MaskColorG1;

            CBUFFER_END        

            v2f VSMain(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);

                float3 n = TransformObjectToWorldNormal(v.normal,true);
                o.color.rgb = n * 0.5f + 0.5f;
                o.color.a = 1;

                // BgAudienceImpostors.shader参考
                // ビルボード。
                float3 upVector = float3(0.0, 1.0, 0.0);
                float3 objectCameraPos = TransformWorldToObject(_WorldSpaceCameraPos);
                float3 objectCameraDir = normalize(objectCameraPos);
                float3 cameraRight = normalize(cross(objectCameraDir, upVector));
                float3 cameraUp = cross(cameraRight, objectCameraDir);
                float3 objectVertex = cameraRight * v.vertex.x + cameraUp * v.vertex.y;
                o.vertex = GallopObjectToClipPos(objectVertex);

                // 真下からカメラまでの縦方向の角度[deg]
                float angleX = acos(dot(objectCameraDir, -upVector)) * INV_PI * 180.0;
                // 上下の角度制限
                angleX = min(max(angleX, 90.0 - _LowerAngle), 90.0 + _UpperAngle) - (90.0 - _LowerAngle);
                // 撮影カ所を中心にテクスチャを切り替えたいので半分ずらすための0.5。
                float vAngle = (angleX / (_UpperAngle + _LowerAngle)) * _AxisFramesY - 0.5;
                // 正面からカメラまでの横方向の角度[deg]
                float angleY = frac(atan2(-objectCameraDir.x, objectCameraDir.z) * INV_TWO_PI) * 360.0;
                // 撮影カ所を中心にテクスチャを切り替えたいので半分ずらすための0.5。
                float hAngle = (angleY / 360.0) * _AxisFramesX + 0.5;
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.uv.x = (o.uv.x + floor(hAngle)) / _AxisFramesX;
                // はみ出すと見た目がいきなり狂うので範囲チェック。
                o.uv.y = (o.uv.y + max(min(floor(vAngle), _AxisFramesY - 1), 0)) / _AxisFramesY;
                
                return o;
            }

            fixed4 PSMain(v2f i) : SV_Target
            {
                // マスクテクスチャのBにアルファ情報が入っている。
                half4 colorMask = TEX2D_SAMPLE(_MaskColorTex, i.uv);
                float cutoff = colorMask.b - _Cutoff;
                clip(cutoff);
                
                return i.color;
            }

            ENDHLSL
        }
    }
}
