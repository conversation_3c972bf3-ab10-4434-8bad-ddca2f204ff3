#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;

namespace Gallop
{
    /// <summary>
    /// CharaViewerParamDataのローダ(エディタ用)
    /// </summary>
    public class CharaViewerParamDataLoaderForEditor
    {
        private const string VIEWER_PARAM_PATH = ResourcePath.GallopResourcesAssetsPath + "Bundle/Debug/Resources/" + CharaViewer.VIEWER_PARAM_PATH + ".asset";

        public static CharaViewerParamData Load()
        {
            return AssetDatabase.LoadAssetAtPath<CharaViewerParamData>(VIEWER_PARAM_PATH);
        }
    }
}
#endif // CYG_DEBUG && UNITY_EDITOR