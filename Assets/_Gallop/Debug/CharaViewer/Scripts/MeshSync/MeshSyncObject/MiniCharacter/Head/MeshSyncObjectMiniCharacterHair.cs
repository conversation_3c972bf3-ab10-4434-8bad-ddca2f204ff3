#if UNITY_EDITOR && CYG_DEBUG && GALLOP_MESH_SYNC
using System;
using System.Collections.Generic;
using System.Linq;
using Gallop.Model.Component;
using UnityEngine;

namespace Gallop.MeshSync
{
    /// <summary>
    /// <see cref="MeshSyncObjectMiniCharacter"/>の髪パーツの制御処理
    /// </summary>
    public class MeshSyncObjectMiniCharacterHair : IMeshSyncCharacterParts 
    {
        private GameObject _rootObject;
        
        private GameObject _target;
        public GameObject Target => _target;

        private MeshSyncCharacterIdentifier _characterIdentifier;
        private IReadOnlyList<Renderer> _rendererList;

        private Material _material;
        private MeshSyncObjectMiniCharacterMaterialFinder _materialFinder = new MeshSyncObjectMiniCharacterMaterialFinder();
        private MaterialCache _materialCache;
        private RendererHolder _rendererHolder;
        
        public MeshSyncObjectMiniCharacterHair(GameObject rootObject, GameObject target, MeshSyncCharacterIdentifier characterIdentifier)
        {
            _rootObject = rootObject;
            
            _target = target;
            _characterIdentifier = characterIdentifier;
            
            // AssetHolderがないと動かないので探してコピー
            var assetHolder = target.AddComponent<AssetHolder>();
            var prefabPath = characterIdentifier.GetMiniHairPrefabAssetPath();
            var prefab = UnityEditor.AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            if (prefab == null)
            {
                throw new InvalidOperationException("髪Prefabが見つかりませんでした。");
            }
            var originalAssetHolder = prefab.GetComponent<AssetHolder>();
            MeshSyncCharacterObjectUtility.ReplaceAssetHolder(originalAssetHolder, assetHolder, rootObject);
            
            CollectRenderer();
            var materialDic = new Dictionary<string, Material[]>();
            foreach (var renderer in _rendererList)
            {
                materialDic[renderer.name] = new[] { _materialFinder.FindAndCreateHairMaterials(_characterIdentifier.CharaID, _characterIdentifier.HeadID, 1, 0, renderer.name) };
            }
            _materialCache = new MaterialCache(materialDic);
            _materialCache.ApplyOriginalMaterial(_rendererList);
        }
                
        public void Dispose()
        {
        }
        
        public static bool IsHairRenderer(Renderer renderer)
        {
            return renderer.name.Contains(CharaNodeName.M_Hair);
        }

        public void OnBeforeInitializeCharacter(CharaViewer.CharacterType characterType)
        {
            _materialCache.ApplyOriginalMaterial(_rendererList);
        }

        public void OnAfterInitializeCharacter(bool isSyncBone, bool isDummyBody, float characterScale)
        {
            CollectRenderer();
            _rendererHolder = _rootObject.GetComponent<MiniModelController>().RendererHolder;
            _materialCache.ApplyRendererHolderMaterial(_rendererHolder, _rendererList);
        }
        
        public bool HasMaterial()
        {
            return _materialCache.HasMaterial();
        }
        
        /// <summary>
        /// MeshSyncで更新があった
        /// </summary>
        /// <param name="updateStatus"></param>
        /// <param name="characterScale"></param>
        /// <remarks>DCCツールからSyncが来たときにここに来る</remarks>
        public void OnUpdateFromMeshSync(MeshSyncCharacterUpdateStatus updateStatus, float characterScale)
        {
            // Rendererの種類が変わっている可能性があるので都度集める
            CollectRenderer();
            _rendererHolder = _rootObject.GetComponent<MiniModelController>().RendererHolder;
            _materialCache.ApplyRendererHolderMaterial(_rendererHolder, _rendererList);
        }
        
        private void CollectRenderer()
        {
            _rendererList = _rootObject.GetComponentsInChildren<Renderer>(true).Where(IsHairRenderer).ToArray();
        }
    }
}

#endif
