#if UNITY_EDITOR && CYG_DEBUG && GALLOP_MESH_SYNC
using System;
using System.Collections.Generic;
using System.Linq;
using Gallop.Model.Component;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Gallop.MeshSync
{
    /// <summary>
    /// 体がないときに作られるダミーの体
    /// </summary>
    public class MeshSyncObjectMiniCharacterDummyBody : IMeshSyncMiniCharacterBody
    {
        private GameObject _gameObject;
        public GameObject Target => _gameObject;

        /// <summary>
        /// リファレンス表示かどうか
        /// </summary>
        public bool IsReference => false;

        private Transform _headRootNode;
        /// <summary>
        /// 頭のルートボーン
        /// </summary>
        public Transform HeadRootNode => _headRootNode;
        
        private Transform _positionNode;
        
        
        private CharacterBuildInfo _characterBuildInfo;
        private GameObject _rootObject;
        
        private AssetHolder _assetHolder;
        private RendererHolder _rendererHolder;
        
        private MeshSyncCharacterIdentifier _characterIdentifier;

        private IReadOnlyList<Renderer> _rendererList;
        private MaterialCache _materialCache;
        private List<Transform> _allNodes = new List<Transform>();
        
        private MeshSyncObjectCharacterTextureLoader _textureLoader = new MeshSyncObjectCharacterTextureLoader();
        private MeshSyncObjectMiniCharacterMaterialFinder _materialFinder = new MeshSyncObjectMiniCharacterMaterialFinder();

        public MeshSyncObjectMiniCharacterDummyBody(GameObject rootObject, MeshSyncCharacterIdentifier characterIdentifier, CharacterBuildInfo buildInfo)
        {
            _rootObject = rootObject;
            _characterBuildInfo = buildInfo;
            
            // ダミーは指定した汎用衣装をロードする
            var path  = ResourcePath.BundleResourcesAssetsPath + buildInfo.CharaBuildPathInfo.upBodyPrefabPath + ".prefab";
            var dummyPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
            var parent = rootObject.transform;

            var bodyObject = UnityEngine.Object.Instantiate(dummyPrefab);
            var bodyMesh = bodyObject.GetComponentInChildren<SkinnedMeshRenderer>();
            _positionNode = bodyObject.transform.Find(CharaNodeName.Position);
            var allNodes = GetAllNode(_positionNode);
            _headRootNode = allNodes.Find(x => x.name == CharaNodeName.Neck);
            _positionNode.transform.SetParent(parent);
            bodyMesh.transform.SetParent(parent);

            // _assetHolder = parent.gameObject.AddComponent<AssetHolder>();
            // var originalAssetHolder = bodyObject.GetComponent<AssetHolder>();
            // originalAssetHolder.CopyTo(_assetHolder);
            // MeshSyncCharacterObjectUtility.ReplaceAssetHolder(originalAssetHolder, _assetHolder, parent.gameObject);
            //
            _gameObject = bodyMesh.gameObject;
            UnityEngine.Object.DestroyImmediate(bodyObject);
            
            CollectRenderer();
            var materialDic = new Dictionary<string, Material[]>();
            foreach (var renderer in _rendererList)
            {
                var mat =  _materialFinder.FindAndCreateBodyMaterial(characterIdentifier.CharaID, characterIdentifier.HeadID, renderer.name);
                materialDic[renderer.name] = new[] { mat };
            }
            _materialCache = new MaterialCache(materialDic);
            _materialCache.ApplyOriginalMaterial(_rendererList);
        }

        public void Dispose()
        {
        }
        
        /// <summary>
        /// 体に頭をくっつける
        /// </summary>
        public void CompositeHead(MeshSyncObjectMiniCharacterHead head, bool isSyncBone)
        {
            var bodyNeck = _positionNode.GetComponentsInChildren<Transform>().First(x => x.name == CharaNodeName.Neck);
            var bodyHead = bodyNeck.Find(CharaNodeName.Head);

            head.CompositeToBody(bodyNeck, bodyHead, isSyncBone, true);
        }

        public void OnBeforeInitializeCharacter(CharaViewer.CharacterType characterType)
        {
            CollectRenderer();
            _materialCache.ApplyOriginalMaterial(_rendererList);
        }

        public void OnAfterInitializeCharacter(bool isSyncBone, bool isDummyBody, float characterScale)
        {
            var modelController = _rootObject.GetComponent<MiniModelController>();
            _textureLoader.LoadAndSetMiniDressTexture(_characterBuildInfo, modelController.GetModelComponent<CharaPartsHolder>());
            
            CollectRenderer();
            _rendererHolder = modelController.RendererHolder;
            _materialCache.ApplyRendererHolderMaterial(_rendererHolder, _rendererList);
        }

        /// <summary>
        /// MeshSyncで更新があった
        /// </summary>
        /// <param name="updateStatus"></param>
        /// <param name="characterScale"></param>
        /// <remarks>DCCツールからSyncが来たときにここに来る</remarks>
        public void OnUpdateFromMeshSync(MeshSyncCharacterUpdateStatus updateStatus, float characterScale)
        {
            // Rendererの種類が変わっている可能性があるので都度集める
            CollectRenderer();
            _rendererHolder = _rootObject.GetComponent<MiniModelController>().RendererHolder;
            _materialCache.ApplyRendererHolderMaterial(_rendererHolder, _rendererList);
        }

        public bool HasMaterial()
        {
            return _materialCache.HasMaterial();
        }

        private void CollectRenderer()
        {
            _rendererList = _gameObject.GetComponentsInChildren<Renderer>(true).Where(IsBodyRenderer).ToArray();
        }
        
        private bool IsBodyRenderer(Renderer renderer)
        {
            return renderer.name.Contains(CharaNodeName.M_Body);
        }
        
        private List<Transform> GetAllNode(Transform parent)
        {
            List<Transform> ret = new List<Transform>();
            FindChildren(parent, ret);

            return ret;
        }

        private void FindChildren(Transform parent, List<Transform> list)
        {
            foreach (Transform child in parent)
            {
                list.Add(child.transform);
                FindChildren(child.transform, list);
            }
        }

    }
}

#endif
