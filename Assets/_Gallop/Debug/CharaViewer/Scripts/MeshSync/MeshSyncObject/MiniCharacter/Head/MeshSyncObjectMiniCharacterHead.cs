#if UNITY_EDITOR && CYG_DEBUG && GALLOP_MESH_SYNC
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Animations;

namespace Gallop.MeshSync
{
    /// <summary>
    /// <see cref="MeshSyncObjectMiniCharacter"/>の髪パーツの制御処理<br/>
    /// ミニは顔と髪がバラバラだがくっつけてHead扱いにしてる
    /// </summary>
    public class MeshSyncObjectMiniCharacterHead : IMeshSyncCharacterParts 
    {
        private GameObject _rootObject;
        
        private GameObject _target;
        public GameObject Target => _target;

        private IReadOnlyList<Renderer> _rendererList;

        private IMeshSyncCharacterParts _hair = new MeshSyncObjectCharacterEmptyParts();
        private IMeshSyncCharacterParts _face = new MeshSyncObjectCharacterEmptyParts();

        public MeshSyncObjectMiniCharacterHead(GameObject rootObject, GameObject target, MeshSyncCharacterIdentifier characterIdentifier, CharacterBuildInfo characterBuildInfo, IReadOnlyList<Renderer> rendererList, bool isSyncBone)
        {
            _rootObject = rootObject;
            _target = target;
            _rendererList = rendererList;

            foreach (var renderer in rendererList)
            {
                if (MeshSyncObjectPartsUtility.IsEmptyParts(_hair) && MeshSyncObjectMiniCharacterHair.IsHairRenderer(renderer))
                {
                    _hair = new MeshSyncObjectMiniCharacterHair(rootObject, target, characterIdentifier);
                }
                else if (MeshSyncObjectPartsUtility.IsEmptyParts(_face) && MeshSyncObjectMiniCharacterFace.IsFaceRenderer(renderer))
                {
                    _face = new MeshSyncObjectMiniCharacterFace(rootObject, target, characterIdentifier);
                }
            }

            if (_face is MeshSyncObjectCharacterEmptyParts)
            {
                _face = new MeshSyncObjectMiniCharacterDummyFace(characterIdentifier, characterBuildInfo, rootObject.transform);
            }
            
            // 髪と顔をくっつける
            if (_face is MeshSyncObjectMiniCharacterDummyFace)
            {
                CompositeFaceAndHair(isSyncBone);
            }
        }

        public void OnBeforeInitializeCharacter(CharaViewer.CharacterType characterType)
        {
            _hair.OnBeforeInitializeCharacter(characterType);
            _face.OnBeforeInitializeCharacter(characterType);
        }

        public void OnAfterInitializeCharacter(bool isSyncBone, bool isDummyBody, float characterScale)
        {
            _hair.OnAfterInitializeCharacter(isSyncBone, isDummyBody, characterScale);
            _face.OnAfterInitializeCharacter(isSyncBone, isDummyBody, characterScale);
        }

        /// <summary>
        /// 体側にある頭のルートボーンにくっつける
        /// </summary>
        public void CompositeToBody(Transform bodyNeck, Transform bodyHead, bool isSyncBone, bool isDummyBody)
        {
            var headNeck = GetRootBone(isDummyBody);
            var headHead = headNeck.Find(CharaNodeName.Head);
            var positionOffset = -headHead.transform.localPosition;

            // 階層構造を変えるとDCC側とずれてMeshSyncできなくなるのでParentConstraintで座標を合わせる
            if (!headNeck.TryGetComponent<ParentConstraint>(out var neckParentConstraint))
            {
                neckParentConstraint = headNeck.gameObject.AddComponent<ParentConstraint>();
                var source = new ConstraintSource();
                source.sourceTransform = bodyNeck;
                source.weight = 1f;
                neckParentConstraint.AddSource(source);
                neckParentConstraint.constraintActive = true;
            }
            
            if (!headHead.TryGetComponent<ParentConstraint>(out var headParentConstraint))
            {
                headParentConstraint = headHead.gameObject.AddComponent<ParentConstraint>();
                var source = new ConstraintSource();
                source.sourceTransform = bodyHead;
                source.weight = 1f;
                headParentConstraint.AddSource(source);

                headParentConstraint.constraintActive = true;
            }

            foreach (var renderer in _rendererList)
            {
                if (!IsNeedParentConstraintRenderer(renderer))
                {
                    continue;
                }
                if (!renderer.TryGetComponent<ParentConstraint>(out var parentConstraint))
                {
                    // SyncBoneしてないときはMeshRendererなのでParentConstraintでメッシュとボーンの動きを合わせる
                    // 逆にSyncBoneしてるときはParentConstraintよりもボーンの動きが優先されるみたいなのでAddComponentして問題なさそう
                    parentConstraint = renderer.gameObject.AddComponent<ParentConstraint>();
                    var source = new ConstraintSource();
                    source.sourceTransform = bodyHead;
                    source.weight = 1f;
                    parentConstraint.AddSource(source);
                    parentConstraint.SetTranslationOffset(0, positionOffset);
                    parentConstraint.constraintActive = true;
                }
            }
        }
        
        public bool HasMaterial()
        {
            return _hair.HasMaterial()/* && _face.HasMaterial()*/;
        }

        /// <summary>
        /// MeshSyncで更新があった
        /// </summary>
        /// <param name="updateStatus"></param>
        /// <param name="characterScale"></param>
        /// <remarks>DCCツールからSyncが来たときにここに来る</remarks>
        public void OnUpdateFromMeshSync(MeshSyncCharacterUpdateStatus updateStatus, float characterScale)
        {
            _hair.OnUpdateFromMeshSync(updateStatus, characterScale);
            _face.OnUpdateFromMeshSync(updateStatus, characterScale);
        }
        
        public void Dispose()
        {
            _hair.Dispose();
            _face.Dispose();
        }
        
        private bool IsNeedParentConstraintRenderer(Renderer renderer)
        {
            return renderer.name == CharaNodeName.M_Face || renderer.name == CharaNodeName.M_Hair;
        }

        private Transform GetRootBone(bool isDummyBody)
        {
            if (isDummyBody)
            {
                var neckBones = _rootObject.GetComponentsInChildren<Transform>()
                    .FirstOrDefault(x => x.name == CharaNodeName.Neck &&
                                         x.parent == _rootObject.transform);
                return neckBones;
            }
            else
            {
                var positionNode = _rootObject.GetComponentsInChildren<Transform>().FirstOrDefault(x => x.name == CharaNodeName.Position);
                var rootBone = positionNode.GetComponentsInChildren<Transform>().FirstOrDefault(x => x.name == CharaNodeName.Neck);
                return rootBone;
            }
        }

        /// <summary>
        /// 顔と髪をくっつける
        /// </summary>
        /// <param name="isSyncBone"></param>
        private void CompositeFaceAndHair(bool isSyncBone)
        {
            if (isSyncBone)
            {
                // ModelLoaderと同じやり方でくっつける
                var copyMeshList = new List<Mesh>();
                var faceTrs = _face.Target.transform;
                var hairTrs = _hair.Target.transform;
                var faceChildren = faceTrs.GetComponentsInChildren<Transform>().ToArray();
                var faceRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_Face)?.gameObject;
                var cheekRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_Cheek);
                var mayuLRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_MAYU_L);
                var mayuRRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_MAYU_R);
                var eyeRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_EYE);
                var mouthRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_MOUTH);
                var headNeck = _rootObject.transform.Find(CharaNodeName.Neck);
                var headHead = headNeck.Find(CharaNodeName.Head);

                // 顔と髪の合成処理(ModelLoader.CompositeFaceHair参考
                ModelLoader.CompositeFaceHair(hairTrs.gameObject, headNeck, faceRenderObject, headNeck, null, copyMeshList);

                var parent = hairTrs.transform.parent;
                if (faceRenderObject) faceRenderObject.transform.SetParent(parent);
                if (cheekRenderObject) cheekRenderObject.transform.SetParent(headHead, false);
                if (mayuLRenderObject) mayuLRenderObject.transform.SetParent(headHead, false);
                if (mayuRRenderObject) mayuRRenderObject.transform.SetParent(headHead, false);
                if (eyeRenderObject) eyeRenderObject.transform.SetParent(headHead, false);
                if (mouthRenderObject) mouthRenderObject.transform.SetParent(headHead, false);
            }
            else
            {
                // ModelLoader.CompositeFaceHair()は髪と顔両方にSkinnedMeshRendererがついてるのが前提になってる
                // SyncBoneしてないときは髪がMeshRendererなので別処理
                var faceTrs = _face.Target.transform;
                var hairTrs = _hair.Target.transform;
                var faceChildren = faceTrs.GetComponentsInChildren<Transform>().ToArray();
                var faceRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_Face)?.gameObject;
                var cheekRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_Cheek);
                var mayuLRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_MAYU_L);
                var mayuRRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_MAYU_R);
                var eyeRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_EYE);
                var mouthRenderObject = faceChildren.FirstOrDefault(x => x.name == CharaNodeName.M_MOUTH);
                var headNeck = _rootObject.transform.Find(CharaNodeName.Neck);
                var headHead = headNeck.Find(CharaNodeName.Head);

                var parent = hairTrs.transform.parent;
                if (faceRenderObject) faceRenderObject.transform.SetParent(parent);
                if (cheekRenderObject) cheekRenderObject.transform.SetParent(headHead, false);
                if (mayuLRenderObject) mayuLRenderObject.transform.SetParent(headHead, false);
                if (mayuRRenderObject) mayuRRenderObject.transform.SetParent(headHead, false);
                if (eyeRenderObject) eyeRenderObject.transform.SetParent(headHead, false);
                if (mouthRenderObject) mouthRenderObject.transform.SetParent(headHead, false);
                var faceRenderer = faceRenderObject.GetComponent<SkinnedMeshRenderer>();
                faceRenderer.bones = new[] { headNeck, headHead };

                Object.Destroy(faceTrs.gameObject);
            }
        }

        public static bool IsHeadRenderer(Renderer renderer)
        {
            return renderer.name.Contains(CharaNodeName.M_Face) ||
                   renderer.name.Contains(CharaNodeName.M_Hair) ||
                   renderer.name.Contains(CharaNodeName.M_Mayu) ||
                   renderer.name.Contains(CharaNodeName.M_Cheek) ||
                   renderer.name.Contains(CharaNodeName.M_Line00) ||
                   renderer.name.Contains(CharaNodeName.M_Tear);
        }
    }
}

#endif
