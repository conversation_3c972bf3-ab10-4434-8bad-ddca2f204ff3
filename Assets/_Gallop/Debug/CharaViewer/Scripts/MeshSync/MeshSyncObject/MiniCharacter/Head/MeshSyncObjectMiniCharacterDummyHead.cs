#if UNITY_EDITOR && CYG_DEBUG && GALLOP_MESH_SYNC
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Gallop.MeshSync
{
    /// <summary>
    /// 頭がないときに作られるダミーの頭
    /// </summary>
    public class MeshSyncObjectMiniCharacterDummyHead : IMeshSyncCharacterParts
    {
        private GameObject _gameObject;
        public GameObject Target => _gameObject;

        private List<Renderer> _rendererList = new List<Renderer>();

        public MeshSyncObjectMiniCharacterDummyHead(MeshSyncCharacterIdentifier characterIdentifier, Transform parent)
        {
            var faceMesh = new GameObject(CharaNodeName.M_Face);
            _gameObject = faceMesh;
            _gameObject.transform.SetParent(parent);

            var positionNode = parent.Find(CharaNodeName.Position);
            var headNode = positionNode.GetComponentsInChildren<Transform>().FirstOrDefault(x => x.name == CharaNodeName.Head);

            // 存在しないとエラーになるオブジェクトを追加
            if (headNode != null)
            {
                // 耳のボーンが必要みたい
                var earNode1R = new GameObject("Ear_01_R");
                var earNode2R = new GameObject("Ear_02_R");
                earNode2R.transform.SetParent(earNode1R.transform);
                var earNode3R = new GameObject("Ear_03_R");
                earNode3R.transform.SetParent(earNode2R.transform);
                earNode1R.transform.SetParent(headNode.transform);
                
                var earNode1L = new GameObject("Ear_01_L");
                var earNode2L = new GameObject("Ear_02_L");
                earNode2L.transform.SetParent(earNode1L.transform);
                var earNode3L = new GameObject("Ear_03_L");
                earNode3L.transform.SetParent(earNode2L.transform);
                earNode1L.transform.SetParent(headNode.transform);
            }
            
            // AssetHolderをオリジナルの頭Prefabからとってくる
            var prefabPath = characterIdentifier.GetHeadPrefabAssetPath();
            var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
            if (prefab == null)
            {
                throw new InvalidOperationException("頭Prefabが見つかりませんでした。");
            }
            
            var assetHolder = _gameObject.AddComponent<AssetHolder>();
            var originalAssetHolder = prefab.GetComponent<AssetHolder>();
            originalAssetHolder.CopyTo(assetHolder);
            MeshSyncCharacterObjectUtility.ReplaceAssetHolder(originalAssetHolder, assetHolder, parent.gameObject);
        }

        public void Dispose()
        {
            Object.DestroyImmediate(_gameObject);
            
            _rendererList.Clear();
        }
        
        public void OnBeforeInitializeCharacter(CharaViewer.CharacterType characterType)
        {
        }

        public void OnAfterInitializeCharacter(bool isSyncBone, bool isDummyBody, float characterScale)
        {
        }

        /// <summary>
        /// MeshSyncで更新があった
        /// </summary>
        /// <param name="updateStatus"></param>
        /// <param name="characterScale"></param>
        /// <remarks>DCCツールからSyncが来たときにここに来る</remarks>
        public void OnUpdateFromMeshSync(MeshSyncCharacterUpdateStatus updateStatus, float characterScale)
        {
        }
        
        public bool HasMaterial()
        {
            return false;
        }
    }
}

#endif
