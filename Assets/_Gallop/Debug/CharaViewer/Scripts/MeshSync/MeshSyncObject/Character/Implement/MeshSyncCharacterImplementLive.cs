#if UNITY_EDITOR && CYG_DEBUG && GALLOP_MESH_SYNC
using System;
using System.Collections.Generic;
using System.Linq;
using Gallop.Live;
using Gallop.Live.Cutt;
using Unity.MeshSync;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Gallop.MeshSync
{
    /// <summary>
    /// MeshSyncのキャラクター制御のコア部分(ライブ用)
    /// </summary>
    public class MeshSyncCharacterImplementLive : IMeshSyncCharacterImplement, IMeshSyncCharacterPresenterLive
    {
        private bool _playingLive;

        /// <summary>
        /// ライブ再生中かどうか
        /// </summary>
        public bool IsPlayingLive => _playingLive;

        private bool _useFormationOffset = true;

        /// <summary>
        /// Formationを適用するかどうか
        /// </summary>
        public bool UseFormationOffset => _useFormationOffset;

        private float _liveTimeScale = 1.0f;

        /// <summary>
        /// ライブの再生速度
        /// </summary>
        public float LiveTimeScale => _liveTimeScale;

        private int _formationNumber = 0;

        /// <summary>
        /// Formation番号
        /// </summary>
        public int FormationNumber => _formationNumber;

        #region CySpring関連

        private MeshSyncCharacterCySpringImplement _cySpringImplement;

        /// <summary> CySpring有効フラグ </summary>
        public bool IsEnableCySpring
        {
            get { return _cySpringImplement.IsEnableCySpring; }
            set { _cySpringImplement.IsEnableCySpring = value; }
        }

        /// <summary> 風有効フラグ </summary>
        public bool IsCySpringWind
        {
            get { return _cySpringImplement.IsCySpringWind; }
            set { _cySpringImplement.IsCySpringWind = value; }
        }

        /// <summary> 疑似フラグ </summary>
        public bool IsFakeWind
        {
            get { return _cySpringImplement.IsFakeWind; }
            set { _cySpringImplement.IsFakeWind = value; }
        }

        /// <summary> プログラムで移動させるフラグ </summary>
        public bool IsCySpringWindMove
        {
            get { return _cySpringImplement.IsCySpringWindMove; }
            set { _cySpringImplement.IsCySpringWindMove = value; }
        }

        /// <summary> 風の強さ </summary>
        public float CySpringWindPower
        {
            get { return _cySpringImplement.CySpringWindPower; }
            set { _cySpringImplement.CySpringWindPower = value; }
        }

        /// <summary> 風の向き </summary>
        public Vector3 CySpringWindDir
        {
            get { return _cySpringImplement.CySpringWindDir; }
            set { _cySpringImplement.CySpringWindDir = value; }
        }

        /// <summary> 風のパラメータ（入力欄無し） </summary>
        public CySpringWindParam WindParam => _cySpringImplement.WindParam;
        #endregion

        private CharacterBuildInfo _characterBuildInfo;
        private TransformSnapshot _transformSnapshot;

        private Dictionary<int, Master3dLive.LiveSettings> _liveSettingsCutt;

        /// <summary> MusicIdとCutt名の組み合わせ </summary>
        public IReadOnlyDictionary<int, Live.Master3dLive.LiveSettings> LiveSettingsDic => _liveSettingsCutt;

        private int _currentLiveIndex = -1;

        private float _animationCurrentTime;
        private float _liveMaxTime;

        private CharacterObject _characterObject;
        private List<CharacterObject> _characterObjectList = new List<CharacterObject>();

        private LiveTimelineControl _liveTimelineControl;
        private LiveTimelineMotionSequence _liveTimelineMotionSequence;

        public MeshSyncCharacterImplementLive(CharacterBuildInfo characterBuildInfo, TransformSnapshot transformSnapshot, MeshSyncCharacterPartsSet partsSet, Dictionary<int, Master3dLive.LiveSettings> liveSettingsCutt)
        {
            _characterBuildInfo = characterBuildInfo;
            _transformSnapshot = transformSnapshot;
            _liveSettingsCutt = liveSettingsCutt;

            var liveModelController = partsSet.RootObject.AddComponent<LiveModelController>();
            var meshHandle = new CharaModelCache.MeshHandle(Array.Empty<Mesh>());
            var context = new ModelControllerBehaviour.SetupContext(characterBuildInfo, partsSet.RootObject, partsSet.GetHeadParts().Target, null, false, meshHandle);
            liveModelController.Setup(ref context);
            liveModelController.Init();
            MeshSyncCharacterObjectUtility.ImportClothData(liveModelController.ClothAsset, characterBuildInfo.ClothBuildPathInfo, characterBuildInfo.LoadHashKey, CySpringDataContainer.Category.Live);
            liveModelController.SetShaderFromGraphisSettings();
            liveModelController.SetShadowFromGraphisSettings();
            liveModelController.UpdateMaterialPropertyBlockOutline();
            liveModelController.UpdateGraphicSettings(0);
            
            _characterObject = partsSet.RootObject.AddComponent<CharacterObject>();
            _characterObject.SetModelObject(partsSet.RootObject, 0, CharacterObject.DRESS_MAIN_INDEX);
            _characterObjectList.Add(_characterObject);

            _cySpringImplement = new MeshSyncCharacterCySpringImplement(liveModelController);

            _currentLiveIndex = 0;

            // var settings = _liveSettingsCutt.First();
            // var cuttPath = ResourcePath.GetLiveCuttPrefabPath(settings.Value.ParamStringArray[0]);
            //
            // var cuttPrefab = ResourceManager.LoadOnScene<GameObject>(cuttPath);
            // _liveTimelineControl = cuttPrefab.GetComponent<LiveTimelineControl>();
            // var iPosition = _liveMotionPositionNo;
            // var seqDataIndex = _liveTimelineControl.data.characterSettings.motionSequenceIndices[iPosition];
            // _liveTimelineControl.SetCharactorLocator(iPosition, characterObject);
            // _liveTimelineMotionSequence = new LiveTimelineMotionSequence();
            // _liveTimelineMotionSequence.Initialize(characterObject, iPosition, seqDataIndex, _liveTimelineControl);
            // _liveTimelineMotionSequence.SetCurrentSheetIndex(LiveTimelineDefine.SheetIndex.MainLive);
            // _liveMaxTime = _liveTimelineControl.data.timeLength;
        }
        
        /// <summary>
        /// 各パーツのTransformを調整した後に行う初期化処理
        /// </summary>
        public void InitializeAfterAdjustTransform()
        {
            // 各パーツのサイズが変わって揺れものがおかしくなる可能性があるのでここで初期化
            foreach (var modelController in _characterObject.LiveModelControllerArray)
            {
                if (modelController == null)
                {
                    continue;
                }
                modelController.InitializeCySpring();
            }
        }

        public void Dispose()
        {
            foreach (var modelController in _characterObject.LiveModelControllerArray)
            {
                if (modelController)
                {
                    modelController.ResetCySpringNativeCloth();
                    modelController.ResetScale();
                    Object.DestroyImmediate(modelController.Animation);
                    Object.DestroyImmediate(modelController.LiveFaceController);
                    Object.DestroyImmediate(modelController);
                }
            }
            Object.DestroyImmediate(_characterObject);
            _characterObjectList.Clear();
        }
        
        /// <summary>
        /// 毎フレーム更新処理
        /// </summary>
        public void Update(float deltaTime)
        {
            // アニメーション更新 CharaViewier.UpdateLiveAnimation()参考
            if (_playingLive)
            {
                _animationCurrentTime += deltaTime * _liveTimeScale;
                UpdateLive();
            }

            _cySpringImplement.Update();
        }        

        /// <summary>
        /// Formationを適用するかどうかの値を設定する
        /// </summary>
        /// <param name="value"></param>
        public void SetUseFormationOffset(bool value)
        {
            _useFormationOffset = value;
        }

        /// <summary>
        /// ライブの再生速度を設定する
        /// </summary>
        /// <param name="timeScale"></param>
        public void SetLiveTimeScale(float timeScale)
        {
            _liveTimeScale = timeScale;
        }

        /// <summary>
        /// フォーメーション番号を設定する
        /// </summary>
        /// <param name="formationNumber"></param>
        public void SetFormationNumber(int formationNumber)
        {
            _formationNumber = formationNumber;

            var seqDataIndex = _liveTimelineControl.data.characterSettings.motionSequenceIndices[_formationNumber];
            _liveTimelineMotionSequence.Initialize(_characterObject, _formationNumber, seqDataIndex, _liveTimelineControl);
            _liveTimelineMotionSequence.SetCurrentSheetIndex(LiveTimelineDefine.SheetIndex.MainLive);

            _animationCurrentTime = 0; // 最初から再生しないと
            UpdateLive();
        }

        /// <summary>
        /// ライブの再生を一時停止する
        /// </summary>
        public void PauseLive()
        {
            _playingLive = false;
        }

        /// <summary>
        /// ライブの再生を再開する
        /// </summary>
        public void ResumeLive()
        {
            _playingLive = true;

            if (_liveTimelineControl == null)
            {
                SetupLive(_currentLiveIndex);
            }
        }

        /// <summary>
        /// ライブの経過時間を設定する
        /// </summary>
        /// <param name="normalizeTime">0-1の時間</param>
        public void SetLiveNormalizedTime(float normalizeTime)
        {
            if (_liveTimelineControl == null)
            {
                return;
            }

            _animationCurrentTime = normalizeTime * _liveMaxTime;
            UpdateLive();
        }

        /// <summary>
        /// 正規化されたライブの経過時間を取得する
        /// </summary>
        public float GetLiveElapsedNormalizeTime()
        {
            if (_liveMaxTime == 0f)
            {
                return 0f;
            }

            return _animationCurrentTime / _liveMaxTime;
        }

        /// <summary>
        /// 指定した<paramref name="motionIndex"/>のモーションを再生する
        /// </summary>
        public void PlayMotion(int motionIndex)
        {
            SetupLive(motionIndex);
            _playingLive = true;
        }

        /// <summary>
        /// MeshSyncの同期処理開始時に行う処理を記述
        /// </summary>
        public void OnStartSync()
        {
            // SyncBoneしてるときに揺れものが1フレーム動いた状態で同期されて見た目が正しく同期されないため止める
            foreach (var modelController in _characterObject.LiveModelControllerArray)
            {
                modelController.CySpringController.Pause();
            }
        }
        
        /// <summary>
        /// MeshSyncの同期処理終了時に行う処理を記述
        /// </summary>
        public void OnEndSync()
        {
            // 止めていた揺れものを再開
            foreach (var modelController in _characterObject.LiveModelControllerArray)
            {
                modelController.CySpringController.Resume();
            }
        }
        
        /// <summary>
        /// MeshSyncで更新があった
        /// </summary>
        /// <param name="meshSyncResultObjectInfo"></param>
        /// <param name="type"></param>
        /// <remarks>DCCツールからSyncが来たときにここに来る</remarks>
        public void OnUpdateFromMeshSync(IMeshSyncResultObjectInfo meshSyncResultObjectInfo, NetworkMessageType type)
        {
            foreach (var modelController in _characterObject.LiveModelControllerArray)
            {
                if (modelController == null)
                {
                    continue;
                }
                modelController.SetDefaultScale();
                
                // MeshSyncの設定でRendererの種類が変わっている可能性があるので、再取得
                modelController.RendererHolder.OnInitialize(); 
            
                modelController.SetShaderFromGraphisSettings();
                modelController.SetShadowFromGraphisSettings();
                modelController.UpdateGraphicSettings(0);
                modelController.ReflectMaterialPropertyToRenderer();
                
                if (meshSyncResultObjectInfo.IsSyncBone)
                {
                    modelController.InitializeCySpring();
                    modelController.ResetCyspring();
                }
            }
        }
        
        /// <summary>
        /// キャラのスケールを取得する
        /// </summary>
        public float GetScale()
        {
            if (_characterObject == null ||
                _characterObject.LiveModelControllerArray == null ||
                _characterObject.LiveModelControllerArray.Length == 0)
            {
                return 1f;
            }
            
            return _characterObject.LiveModelControllerArray[0].GetBodyScale();
        }
        
        /// <summary>
        /// アニメーションをリセットしてTポーズにする
        /// </summary>
        public void ResetAnimation()
        {
            _transformSnapshot.Apply(_characterObject.transform.Find(CharaNodeName.Position));
            _characterObject.transform.position = Math.VECTOR3_ZERO;
        }

        public void OnChangeCySpringSettings() => _cySpringImplement.OnChangeCySpringSettings();
        public void ResetCySpring() => _cySpringImplement.ResetCySpring();

        private void SetupLive(int motionIndex)
        {
            _currentLiveIndex = motionIndex;

            // アニメーションの準備 CharaViewer.OnLiveAnimChanged()参考
            var settings = _liveSettingsCutt.Values.ToArray()[_currentLiveIndex];
            var cuttPath = ResourcePath.GetLiveCuttPrefabPath(settings.ParamStringArray[0]);
            var cuttPrefab = ResourceManager.LoadOnScene<GameObject>(cuttPath);
            _liveTimelineControl = cuttPrefab.GetComponent<LiveTimelineControl>();
            var seqDataIndex = _liveTimelineControl.data.characterSettings.motionSequenceIndices[_formationNumber];
            _liveTimelineControl.SetCharactorLocator(_formationNumber, _characterObject);
            _liveTimelineMotionSequence = new LiveTimelineMotionSequence();
            _liveTimelineMotionSequence.Initialize(_characterObject, _formationNumber, seqDataIndex, _liveTimelineControl);
            _liveTimelineMotionSequence.SetCurrentSheetIndex(LiveTimelineDefine.SheetIndex.MainLive);
            _liveMaxTime = _liveTimelineControl.data.timeLength;

            _animationCurrentTime = 0f;
        }

        private void UpdateLive()
        {
            var frameRate = GameDefine.MAX_FRAME_RATE;
            var currentFrame = (int)(_animationCurrentTime * frameRate);
            _liveTimelineMotionSequence.AlterUpdate(currentFrame, _animationCurrentTime, 0f, frameRate);

            if (_useFormationOffset)
            {
                _liveTimelineControl.AlterUpdate_FormationOffsetForCharaViewer(_liveTimelineControl.data.GetWorkSheet(0), currentFrame, _characterObjectList);
            }
        }
    }
}

#endif
