#if CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// キャラビューアー：「アニメーション」のサブメニュー
    /// </summary>
    [AddComponentMenu("")]
    public class PartsCharaViewerAnimationMenu : MonoBehaviour
    {
        [field: Header("アニメーションリスト")]

        /// <summary>アニメーションリスト</summary>
        [SerializeField] private PartsCharaViewerTextList _animationList;
        /// <summary>検索インプットフィールド</summary>
        [SerializeField] private InputFieldCommon _searchInputField;


        [field: Header("基本操作項目")]

        [SerializeField] private GameObject _basicOperationsRoot;
        /// <summary>現在選択しているうアニメーション名</summary>
        [SerializeField] private TextCommon _currentAnimeText;
        /// <summary>変形版アニメーションのドロップダウン</summary>
        [SerializeField] private DropDownCommon _commandVariantDropDown;
        /// <summary>モーションSEのマテリアルIDのドロップダウン</summary>
        [SerializeField] private DropDownCommon _motionSeMaterialIdDropDown;
        /// <summary>「カメラを動かさない」チェックボックス</summary>
        [SerializeField] private ToggleWithText _isRequierAlwaysLockCameraCheckBox;
        /// <summary>「アニメーション再生」ボタン</summary>
        [SerializeField] private ButtonCommon _playAnimationButton;


        [field: Header("共通オプション項目")]

        [SerializeField] private GameObject _commonOptionsRoot;

        /// <summary>「モーションスピード」スライダー</summary>
        [SerializeField] private SliderCommon _animationSpeedSlider;
        [SerializeField] private TextCommon _animationSpeedSliderValueText;

        [SerializeField] private GameObject _commonOptionsLightRoot;

        /// <summary>「ライトを変更する」チェックボックス</summary>
        [SerializeField] private ToggleWithText _isLightOverrideCheckBox;
        /// <summary>「ライトX軸回転」スライダー</summary>
        [SerializeField] private SliderCommon _lightDirectionXSlider;
        [SerializeField] private TextCommon _lightDirectionXSliderValueText;
        /// <summary>「ライトY軸回転」スライダー</summary>
        [SerializeField] private SliderCommon _lightDirectionYSlider;
        [SerializeField] private TextCommon _lightDirectionYSliderValueText;


        [field: Header("会話シーン専用オプション項目")]

        [SerializeField] private GameObject _storyOptionsRoot;
        /// <summary>「ループのみ再生」チェックボックス</summary>
        [SerializeField] private ToggleWithText _loopMotionCheckBox;
        /// <summary>「涙の表示」チェックボックス</summary>
        [SerializeField] private ToggleWithText _isTearCheckBox;
        /// <summary>「リストフィルタリング」チェックボックス</summary>
        [SerializeField] private ToggleWithText _iKAnimationFilterCheckBox;
        /// <summary>「リストフィルタリング」ドロップダウン</summary>
        [SerializeField] private DropDownCommon _iKAnimationTypeDropDown;
        /// <summary>「衣装による入れ替えモーション」ドロップダウン</summary>
        [SerializeField] private DropDownCommon _swapMotionDropDown;


        [field: Header("レース専用オプション項目")]

        [SerializeField] private GameObject _raceOptionsRoot;
        /// <summary>「濡れ」チェックボックス</summary>
        [SerializeField] private ToggleWithText _isWetCheckBox;
        /// <summary>「汚れ」チェックボックス</summary>
        [SerializeField] private ToggleWithText _isDirtCheckBox;
        /// <summary>「汚れR」スライダー</summary>
        [SerializeField] private SliderCommon _dirtEnergyRSlider;
        /// <summary>「汚れG」スライダー</summary>
        [SerializeField] private SliderCommon _dirtEnergyGSlider;
        /// <summary>「汚れB」スライダー</summary>
        [SerializeField] private SliderCommon _dirtEnergyBSlider;


        [field: Header("ライブ専用オプション項目")]

        [SerializeField] private GameObject _liveOptionsRoot;
        /// <summary>「位置：〇」ボタン</summary>
        [SerializeField] private ButtonCommon _livePosition0Button;
        [SerializeField] private ButtonCommon _livePosition1Button;
        [SerializeField] private ButtonCommon _livePosition2Button;
        [SerializeField] private ButtonCommon _livePosition3Button;
        [SerializeField] private ButtonCommon _livePosition4Button;


        private CharaViewer.CharacterType _charaType = CharaViewer.CharacterType.Story;

        // アニメーションリスト関連
        private List<string> _originalAnimationNameList = new List<string>();
        private List<string> _seachedAnimationNameList = new List<string>();

        // 変形型アニメーションのドロップダウン関連
        private string[] _commandVariantArray = null;

        // モーションSEのマテリアルIDのドロップダウン関連
        private string[] _motionSeMaterialIdArray = null;

        private bool _isInitialized = false;

        private CharaViewer _charaViewer => CharaViewer.Instance;


        /// <summary>セットアップ処理</summary>
        public void Setup()
        {
            _charaType = _charaViewer.GetCharaViewerInfo().CharacterType;

            // アニメーションリスト
            SetupAnimationList();
            // 検索インプットフィールド
            SetupSearchInputField();

            // 基本操作項目
            SetupBasicOperations();

            // オプション項目
            SetupCommonOptions();
            SetupStoryOptions();
            SetupRaceOptions();
            SetupLiveOptions();

            _isInitialized = true;
        }

        /// <summary>アニメーションリストのセットアップ</summary>
        private void SetupAnimationList()
        {
            // 文字列一覧を取得
            string[] terminalAnimationNameArray = null;
            if (_charaType == CharaViewer.CharacterType.Story)
            {
                _charaViewer.Anim_CreateTerminalAnimationNameArrayVerStory();
                terminalAnimationNameArray = _charaViewer.TerminalAnimationNameArray;
            }
            else
            {
                terminalAnimationNameArray = _charaViewer.GetAnimationNames();
            }

            // 無効な一覧だった場合は補正
            if (terminalAnimationNameArray.Length == 1 && string.IsNullOrEmpty(terminalAnimationNameArray[0]))
            {
                terminalAnimationNameArray = new string[0];
            }

            // 変化しない場合はスキップ
            if (!_originalAnimationNameList.IsNullOrEmpty() && !terminalAnimationNameArray.IsNullOrEmpty() &&
                _originalAnimationNameList.SequenceEqual(terminalAnimationNameArray))
            {
                return;
            }

            _seachedAnimationNameList = _originalAnimationNameList = terminalAnimationNameArray.ToList();

            // セットアップ
            _animationList.Setup(_originalAnimationNameList, OnClickAnimationListItem);

            // 検索キーワードが入力されているなら適用
            OnSearchInputFieldEndEdit();
            _searchInputField.SetActiveWithCheck(!_originalAnimationNameList.IsNullOrEmpty());
        }

        /// <summary>アニメーションリストの項目を押した時の処理</summary>
        private void OnClickAnimationListItem(int itemIndex, string itemText)
        {
            int originalListIndex = _originalAnimationNameList.IndexOf(itemText);
            if (originalListIndex == -1)
            {
                Debug.LogWarning("アニメーション「" + itemText + "」は選択出来ませんでした");
                return;
            }

            _currentAnimeText.text = itemText;

            // 選択したモーションを再生する
            if (_charaType == CharaViewer.CharacterType.Story)
            {
                _charaViewer.Anim_OnClickAnimationListItemVerStory(originalListIndex);
            }
            else
            {
                _charaViewer.Anim_OnClickAnimationListItemCommon(originalListIndex);
            }

            // モーションが変わったので変形型アニメーションも変更
            SetupCommandVariantDropDown();
        }

        /// <summary>検索インプットフィールドのセットアップ</summary>
        private void SetupSearchInputField()
        {
            if (_isInitialized)
                return;

            _searchInputField.CurrentInputTextType = InputFieldCommon.InputTextType.UserName;
            _searchInputField.text = string.Empty;
            _searchInputField.SetOnEndEdit(OnSearchInputFieldEndEdit);
        }

        /// <summary>検索インプットフィールドへの入力が終わった時の処理</summary>
        private void OnSearchInputFieldEndEdit()
        {
            // 入力された文字列が含まれるアニメーションを検索する
            string keyward = _searchInputField.text;
            keyward = TextUtil.RemoveSpace(keyward); // 空白を取り除く

            // 1文字も入力されなかった場合
            if (keyward.Length <= 0)
            {
                _seachedAnimationNameList = _originalAnimationNameList;
            }
            // 入力された場合
            else
            {
                _seachedAnimationNameList = _originalAnimationNameList.Where(x => CharaViewerView.ContainsString(x, keyward)).ToList();
            }

            // アニメーションリストを更新
            _animationList.Refresh(_seachedAnimationNameList);
        }

        /// <summary>現在選択しているアニメ名のセットアップ</summary>
        private void SetupCurrentAnimeText()
        {
            // アクティブ/非アクティブ制御
            bool isActive = (_charaType != CharaViewer.CharacterType.Story);
            _currentAnimeText.SetActiveWithCheck(isActive);
        }

        /// <summary>変形版アニメーションドロップダウンの更新</summary>
        private void SetupCommandVariantDropDown()
        {
            // 一部のキャラタイプでしかアクティブにしない
            if (_charaType != CharaViewer.CharacterType.Story)
            {
                _commandVariantDropDown.SetActiveWithCheck(false);
                return;
            }

            var commandVariantArray = _charaViewer.TerminalCommandVariantArray;

            // 変化しない場合はスキップ
            if (!_commandVariantArray.IsNullOrEmpty() && !commandVariantArray.IsNullOrEmpty() &&
                _commandVariantArray.SequenceEqual(commandVariantArray))
            {
                return;
            }

            _commandVariantArray = commandVariantArray;

            // アクティブ/非アクティブ制御
            bool isActive = (!_commandVariantArray.IsNullOrEmpty());
            _commandVariantDropDown.SetActiveWithCheck(isActive);
            if (!isActive)
                return;

            // セットアップ
            _commandVariantDropDown.ClearOptions();
            foreach (var commandVariant in _commandVariantArray)
            {
                _commandVariantDropDown.options.Add(new Dropdown.OptionData(commandVariant));
            }
            _commandVariantDropDown.value = 0;
            _commandVariantDropDown.SyncScrollOffsetByValue();
            _commandVariantDropDown.RefreshShownValue();
            _commandVariantDropDown.onValueChanged.AddListener(index =>
            {
                _charaViewer.Anim_OnChangedCommandVariation(index);
            });
        }

        /// <summary>モーションSEのマテリアルIDドロップダウンのセットアップ</summary>
        private void SetupMotionSeMaterialIdDropDown()
        {
            // アクティブ/非アクティブ制御
            bool isActive = (_charaType == CharaViewer.CharacterType.Story);
            _motionSeMaterialIdDropDown.SetActiveWithCheck(isActive);

            if (_isInitialized)
                return;

            // モーションSEのマテリアルID一覧を取得
            _charaViewer.Anim_CreateTerminalMotionSeMaterialIdStrArray();
            _motionSeMaterialIdArray = _charaViewer.TerminalMotionSeMaterialIdStrArray;

            // セットアップ
            _motionSeMaterialIdDropDown.ClearOptions();
            foreach (var motionSeMaterialId in _motionSeMaterialIdArray)
            {
                _motionSeMaterialIdDropDown.options.Add(new Dropdown.OptionData(motionSeMaterialId));
            }
            _motionSeMaterialIdDropDown.value = 0;
            _motionSeMaterialIdDropDown.SyncScrollOffsetByValue();
            _motionSeMaterialIdDropDown.RefreshShownValue();
            _motionSeMaterialIdDropDown.onValueChanged.AddListener(index =>
            {
                _charaViewer.Anim_OnChangedMotionSeMaterialId(index);
            });
        }

        /// <summary>「カメラを動かさない」チェックボックスのセットアップ</summary>
        private void SetupIsRequierAlwaysLockCameraCheckBox()
        {
            if (!_isInitialized)
            {
                _isRequierAlwaysLockCameraCheckBox.Toggle.isOn = true;
                _isRequierAlwaysLockCameraCheckBox.Toggle.SetCallback(_charaViewer.OnChangedIsRequierAlwaysLockCamera);
            }

            _charaViewer.OnChangedIsRequierAlwaysLockCamera(_isRequierAlwaysLockCameraCheckBox.Toggle.isOn); // カメラ側の「動かさないフラグ」が、サブメニューを抜ける時に強制的にFALSEになるので、必要ならTRUEに戻す
        }

        /// <summary>基本操作項目のセットアップ</summary>
        private void SetupBasicOperations()
        {
            // アクティブ/非アクティブ制御
            bool isActive = !_originalAnimationNameList.IsNullOrEmpty();
            _basicOperationsRoot.SetActiveWithCheck(isActive);
            if (!isActive)
                return;

            // 現在選択しているアニメ名
            SetupCurrentAnimeText();

            // 変形版アニメーションドロップダウン
            SetupCommandVariantDropDown();

            // モーションSEのマテリアルIDドロップダウン
            SetupMotionSeMaterialIdDropDown();

            // 「カメラを動かさない」チェックボックス
            SetupIsRequierAlwaysLockCameraCheckBox();

            // 「アニメーション再生」ボタン
            _playAnimationButton.SetOnClick(() =>
            {
                _charaViewer.Anim_OnClickPlayAnimationButton(_commandVariantDropDown.value);
            });
        }

        /// <summary>共通オプション項目のセットアップ</summary>
        private void SetupCommonOptions()
        {
            // アクティブ/非アクティブ制御
            bool isActiveRoot = !_originalAnimationNameList.IsNullOrEmpty();
            _commonOptionsRoot.SetActiveWithCheck(isActiveRoot);

            bool isActiveLight = (_charaType != CharaViewer.CharacterType.Mini);
            _commonOptionsLightRoot.SetActiveWithCheck(isActiveLight);

            if (_isInitialized)
                return;

            // 「モーションスピード」スライダー
            _animationSpeedSlider.value = _charaViewer.TerminalAnimationSpeed;
            _animationSpeedSlider.onValueChanged.AddListener(sliderValue =>
            {
                _charaViewer.Anim_OnChangeMotionSpeedSlider(sliderValue);
                _animationSpeedSliderValueText.text = sliderValue.ToString("0.00");
            });
            _animationSpeedSliderValueText.text = _animationSpeedSlider.value.ToString("0.00");

            // 「ライトを変更する」チェックボックス
            _isLightOverrideCheckBox.Toggle.isOn = _charaViewer.IsLightOverride;
            _isLightOverrideCheckBox.Toggle.SetCallback(_charaViewer.Anim_OnChangedIsLightOverride);

            // 「ライトX軸回転」スライダー
            _lightDirectionXSlider.maxValue = 360f;
            _lightDirectionXSlider.minValue = 0f;
            _lightDirectionXSlider.value = _charaViewer.LightDirectionX;
            _lightDirectionXSlider.onValueChanged.AddListener(sliderValue =>
            {
                _charaViewer.Anim_OnChangeLightDirectionXSlider(sliderValue);
                _lightDirectionXSliderValueText.text = TextUtil.Format("{0}deg", sliderValue.ToString("0"));
            });
            _lightDirectionXSliderValueText.text = TextUtil.Format("{0}deg", _lightDirectionXSlider.value.ToString("0"));

            // 「ライトY軸回転」スライダー
            _lightDirectionYSlider.maxValue = 360f;
            _lightDirectionYSlider.minValue = 0f;
            _lightDirectionYSlider.value = _charaViewer.LightDirectionY;
            _lightDirectionYSlider.onValueChanged.AddListener(sliderValue =>
            {
                _charaViewer.Anim_OnChangeLightDirectionYSlider(sliderValue);
                _lightDirectionYSliderValueText.text = TextUtil.Format("{0}deg", sliderValue.ToString("0"));
            });
            _lightDirectionYSliderValueText.text = TextUtil.Format("{0}deg", _lightDirectionYSlider.value.ToString("0"));
        }

        /// <summary>会話専用オプション項目のセットアップ</summary>
        private void SetupStoryOptions()
        {
            // アクティブ/非アクティブ制御
            bool isActive = (_charaType == CharaViewer.CharacterType.Story);
            _storyOptionsRoot.SetActiveWithCheck(isActive);

            if (_isInitialized)
                return;

            // 「ループのみ再生」チェックボックス
            _loopMotionCheckBox.Toggle.isOn = _charaViewer.GetCharaViewerInfo().StoryModelDriver.LoopMotion;
            _loopMotionCheckBox.Toggle.SetCallback(_charaViewer.Anim_OnChangedLoopMotion);

            // 「涙の表示」チェックボックス
            _isTearCheckBox.Toggle.isOn = _charaViewer.TerminalIsTear;
            _isTearCheckBox.Toggle.SetCallback(_charaViewer.Anim_OnChangedIsTear);

            // 「リストフィルタリング」チェックボックス
            _iKAnimationFilterCheckBox.Toggle.isOn = _charaViewer.TerminalIKAnimationFilter;
            _iKAnimationFilterCheckBox.Toggle.SetCallback((isOn) =>
            {
                _charaViewer.Anim_OnChangedIKAnimationFilter(isOn);

                SetupAnimationList();
            });

            // 「リストフィルタリング」ドロップダウン
            {
                var strArray = Enum.GetNames(typeof(CharaViewer.TerminalIKMode));

                _iKAnimationTypeDropDown.ClearOptions();
                foreach (var str in strArray)
                {
                    _iKAnimationTypeDropDown.options.Add(new Dropdown.OptionData(str));
                }
                _iKAnimationTypeDropDown.value = 0;
                _iKAnimationTypeDropDown.SyncScrollOffsetByValue();
                _iKAnimationTypeDropDown.RefreshShownValue();
                _iKAnimationTypeDropDown.onValueChanged.AddListener(index =>
                {
                    _charaViewer.Anim_OnChangedIKAnimationType(index);

                    SetupAnimationList();
                });
            }

            // 「衣装による入れ替えモーション」ドロップダウン
            {
                string[] strArray = new string[2] { "無効", "衣装からモーションを決める" };

                _swapMotionDropDown.ClearOptions();
                foreach (var str in strArray)
                {
                    _swapMotionDropDown.options.Add(new Dropdown.OptionData(str));
                }
                _swapMotionDropDown.value = 0;
                _swapMotionDropDown.SyncScrollOffsetByValue();
                _swapMotionDropDown.RefreshShownValue();
                _swapMotionDropDown.onValueChanged.AddListener(index =>
                {
                    _charaViewer.Anim_OnChangedSwapMotion(index);
                });
            }
        }

        /// <summary>レース専用オプション項目のセットアップ</summary>
        private void SetupRaceOptions()
        {
            // アクティブ/非アクティブ制御
            bool isActive = (_charaType == CharaViewer.CharacterType.Race);
            _raceOptionsRoot.SetActiveWithCheck(isActive);

            if (_isInitialized)
                return;

            // 「濡れ」チェックボックス
            _isWetCheckBox.Toggle.isOn = _charaViewer.TerminalIsWet;
            _isWetCheckBox.Toggle.SetCallback(_charaViewer.Anim_OnChangedIsWet);

            // 「汚れ」チェックボックス
            _isDirtCheckBox.Toggle.isOn = _charaViewer.TerminalIsDirt;
            _isDirtCheckBox.Toggle.SetCallback(_charaViewer.Anim_OnChangedIsDirt);

            // 「汚れ量R」スライダー
            _dirtEnergyRSlider.value = _charaViewer.TerminalDirtEnergy[0];
            _dirtEnergyRSlider.onValueChanged.AddListener(_ =>
            {
                float sliderValue = _dirtEnergyRSlider.value;
                _charaViewer.Anim_OnChangeDirtEnergyRSlider(sliderValue);
            });

            // 「汚れ量G」スライダー
            _dirtEnergyGSlider.value = _charaViewer.TerminalDirtEnergy[1];
            _dirtEnergyGSlider.onValueChanged.AddListener(_ =>
            {
                float sliderValue = _dirtEnergyGSlider.value;
                _charaViewer.Anim_OnChangeDirtEnergyGSlider(sliderValue);
            });

            // 「汚れ量B」スライダー
            _dirtEnergyBSlider.value = _charaViewer.TerminalDirtEnergy[2];
            _dirtEnergyBSlider.onValueChanged.AddListener(_ =>
            {
                float sliderValue = _dirtEnergyBSlider.value;
                _charaViewer.Anim_OnChangeDirtEnergyBSlider(sliderValue);
            });
        }

        /// <summary>ライブ専用オプション項目のセットアップ</summary>
        private void SetupLiveOptions()
        {
            // アクティブ/非アクティブ制御
            bool isActive = (_charaType == CharaViewer.CharacterType.Live);
            _liveOptionsRoot.SetActiveWithCheck(isActive);

            if (_isInitialized)
                return;

            _livePosition0Button.SetOnClick(() => { _charaViewer.Anim_OnClickLivePositionButton(0); });
            _livePosition1Button.SetOnClick(() => { _charaViewer.Anim_OnClickLivePositionButton(1); });
            _livePosition2Button.SetOnClick(() => { _charaViewer.Anim_OnClickLivePositionButton(2); });
            _livePosition3Button.SetOnClick(() => { _charaViewer.Anim_OnClickLivePositionButton(3); });
            _livePosition4Button.SetOnClick(() => { _charaViewer.Anim_OnClickLivePositionButton(4); });
        }
    }
}
#endif