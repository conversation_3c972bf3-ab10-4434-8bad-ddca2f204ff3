#if CYG_DEBUG
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Gallop.Live.Cutt;
using System.IO;
using Gallop.Model.Component;
using System.Text.RegularExpressions;

namespace Gallop
{


    using static StoryTimelineMotionClipDataBase;

    /// <summary>
    /// キャラビューワのSceneControllerクラス
    /// </summary>
    public class CharaViewerSceneController : SceneControllerBase<CharaViewer>
    {
        private CharaViewerView _charaViewerView = null;
        private CharaViewerUI _charaViewerUI = null;
        private string _selectedMotion = null;

        public override IEnumerator InitializeScene()
        {
            yield return _scene.InitializeScene();

            if (CharaViewer.IsMotionCheckMode)
            {
                // 簡易モーションチェックのUIを初期化 (IsTerminal=trueの時にここにくる)
                // CharaViewrが持っているリストを利用するのでInitializeSceneの後に実行
                InitCharaViewerUI();
            }

            if (CharaViewer.IsTerminal)
            {
                // Viewを生成
                CreateCharaViewerView();
            }

            yield return base.InitializeScene();
        }

        public override void UpdateScene()
        {
            _scene.UpdateScene();

            UpdateCharaViewerView();

            base.UpdateScene();
        }

        public override void LateUpdateScene()
        {
            _scene.LateUpdateScene();
            base.LateUpdateScene();
        }

        public override void FinalizeScene()
        {
            _scene.FinalizeScane();
            base.FinalizeScene();
        }

        #region View制御

        /// <summary>Viewを生成する</summary>
        private void CreateCharaViewerView()
        {
            // CharaViewerViewのインスタンスを生成
            GameObject charaViewerViewInstance = UnityEngine.Object.Instantiate(_scene.CharaViewerViewPrefab, UIManager.MainCanvas.transform);
            charaViewerViewInstance.SetActive(true);
            _charaViewerView = charaViewerViewInstance.GetComponent<CharaViewerView>();

            // Viewを自前でロードした時はSafeArea対応も自分で行う
            _charaViewerView.AdjustSafeArea(UIManager.Instance);

            // Viewの初期化
            _charaViewerView.Initialize();
        }

        /// <summary>Viewの毎フレーム処理</summary>
        private void UpdateCharaViewerView()
        {
            if (_charaViewerView == null)
                return;

            _charaViewerView.UpdateCharaViewerView();
        }

        #endregion

        #region UI制御

        /// <summary>
        /// UIを初期化する
        /// </summary>
        private void InitCharaViewerUI()
        {
            // View側(UIManager配下)に作成
            var obj = UnityEngine.Object.Instantiate(_scene.CharaViewerUIPrefab, UIManager.MainCanvas.transform);
            obj.SetActive(true);
            _charaViewerUI = obj.GetComponent<CharaViewerUI>();

            // Viewを自前でロードした時はSafeArea対応も自分で行う
            _charaViewerUI.AdjustSafeArea(UIManager.Instance);

            // コールバック登録
            _charaViewerUI.ShowMenuButton.SetOnClick(OnClickShowMenu);
            _charaViewerUI.SelectMotionButton.SetOnClick(OnClickSelectMotion);
            _charaViewerUI.PlayMotionButton.SetOnClick(OnClickPlayMotion);
            _charaViewerUI.SelectCharaDropDown.OnClickCallback = _scene.Terminal_LockCameraControll;
            _charaViewerUI.SelectCharaDropDown.OnSelectCallback = _scene.Terminal_UnlockCameraControll;

            // リスト初期化
            _charaViewerUI.InitCharaDropDown(_scene.GetCharacterString(), OnSelectChara);
            _charaViewerUI.InitMotionScrollView(_scene.TerminalCharaId, OnSelectMotion);
        }

        /// <summary>
        /// モーションチェックのUIを消してOnGUIのメニューを出す
        /// </summary>
        private void OnClickShowMenu()
        {
            var dialogData = new DialogCommon.Data().SetSimpleTwoButtonMessage(
                "確認",
                "開発中のため詳細設定を開くと\nこの画面には戻りません\n詳細設定を開きますか？",
                (_) =>
                {
                    _charaViewerUI.gameObject.SetActive(false);
                    CharaViewer.IsMotionCheckMode = false;

                    // 設定したモーションは消しておく
                    _scene.GetCharaViewerInfo().ClearAllStoryCommands();

                    // カメラを回転出来るようにする
                    _scene.Terminal_UnlockCameraControll();
                });
            DialogManager.PushSystemDialog(dialogData);
        }

        /// <summary>
        /// モーション選択のスクロールビューを表示する
        /// </summary>
        private void OnClickSelectMotion()
        {
            var scrollView = _charaViewerUI.MotionScrollView.gameObject;
            var active = scrollView.activeSelf;
            scrollView.SetActive(!active);

            // ドラッグによるキャラ回転のOn/Offも切り替える
            if (active)
            {
                _scene.Terminal_UnlockCameraControll();
            }
            else
            {
                _scene.Terminal_LockCameraControll();
            }
        }

        /// <summary>
        /// モーション再生ボタンを押された時の処理
        /// </summary>
        private void OnClickPlayMotion()
        {
            if (string.IsNullOrEmpty(_selectedMotion))
            {
                return;
            }

            bool isPlaying = _scene.GetCharaViewerInfo().IsStoryAnimationPlaying;
            _charaViewerUI.PlayMotionButton.TargetText.text = isPlaying ? "モーション再生" : "停止";
            _scene.GetCharaViewerInfo().IsStoryAnimationPlaying = !isPlaying;
        }

        /// <summary>
        /// キャラが選択された時の処理
        /// </summary>
        private void OnSelectChara(int selectedModelIndex)
        {
            // モデル作成
            _scene.SetTerminalModelIndex(selectedModelIndex);
            _scene.BuildModel();

            // Hip追従にしているのでタイミングによってはカメラ角度が狂うのでリセット
            _scene.SetOnModelLoadAction(_scene.CameraReset, null);

            // リスト作り直し
            _charaViewerUI.InitMotionScrollView(_scene.TerminalCharaId, OnSelectMotion);

            // idle02やmot系などキャラによって持っていないモーションがあるので
            // モーションは選び直してもらう
            _scene.GetCharaViewerInfo().ClearAllStoryCommands();
            _charaViewerUI.SelectMotionButton.TargetText.text = "モーション選択";
        }

        /// <summary>
        /// モーションが選択された時の処理
        /// </summary>
        private void OnSelectMotion(CharaViewerUI.MotionInfo motionInfo)
        {
            _selectedMotion = motionInfo.MotionName;
            _scene.GetCharaViewerInfo().AddStoryCommandForMotionCheck(_selectedMotion);

            // 選択したらリストは非表示
            _charaViewerUI.MotionScrollView.gameObject.SetActive(false);

            // モーション再生状態になるのでテキスト変えておく
            _charaViewerUI.PlayMotionButton.TargetText.text = "停止";

            // chara_motion_set.csvで指定された表情も設定する
            _scene.SetFace(new FaceType(motionInfo.FaceName), 1.0f, FaceGroupSet.None);

            // カメラを回転出来るようにする
            _scene.Terminal_UnlockCameraControll();
        }
        #endregion UI制御
    }

    /// <summary>
    /// キャラビューワのSceneクラス
    /// </summary>
    public partial class CharaViewer : DirectSceneBase
    {
        #region 定数

        /// <summary>
        /// デフォルトの立ち位置
        /// </summary>
        public static Vector3[] DEFAULT_POSITION_ARRAY = new Vector3[CharaViewer.CHARACTER_MAX_COUNT];

        /// <summary>
        /// ストーリーのアニメーションにIKがついているもの
        /// </summary>
        public static readonly string[] IKAnimationStoryCommand = new string[]
        {
            "muneate01",
            "muneate10",
            "pain01",
            "koshiate05",
            "koshiate06",
            "shock07",
        };

        /// <summary>
        /// キャラの種類
        /// </summary>
        public enum CharacterType
        {
            Story = 0,
            Race,
            Training,
            Live,
            Home,
            CutIn,
            Mini,
        }

        /// <summary>
        /// モブキャラをビルドするときの種類
        /// </summary>
        public enum MobBuildType
        {
            ViewerParts, // キャラビューワーで指定されたモブパーツでモブモデルを構築。
            MobId, // CharacterBuldInfoに設定されたMobIdでモブモデルを構築。
        }

        /// <summary>
        /// Terminal起動の時だけ使う、通常はCharaViewerWindow側のパスで使われる
        /// </summary>
        public const string VIEWER_PARAM_PATH = "3d/CharaViewer/ast_charaviewer_param";
        private const string FLARE_COLLISION_CHECK_LENS_FLARE_PATH = "3d/CharaViewer/flare000/pfb_lens_flare_root";
        /// <summary>
        /// 初期化時のキャラID
        /// </summary>
        public const int INITIALIZE_CHARAID = 1001;

        /// <summary>
        /// 初期化時の服装（名前から探している）
        /// </summary>
        private const string DefaultClothName = "制服（夏）";

        /// <summary>
        /// 使用するキャラの最大数
        /// </summary>
        public const int CHARACTER_MAX_COUNT = 30;

        private const float CHARA_MAX_HEIGHT = 1.8f;
        private const float CHARA_MAX_WIDTH = 1.4f;
        private const float MINI_CHARA_MAX_HEIGHT = 1.3f;
        private const float MINI_CHARA_MAX_WIDTH = 0.8f;

        private const int GUI_WIDTH = 240;  //!< GUI横幅
        private const int GUI_HEIGHT = 64;  //!< GUI縦幅
        private const int GUI_OFFSET = 32;  //!< GUI幅オフセット

        /// <summary>
        /// Home、Miniキャラ用アニメーション情報クラス
        /// </summary>
        private class AnimationName
        {
            public string name;
            public int index;
            public int masterId;
            public string path;
        };

        /// <summary>
        /// 立ち位置のモーション識別に使うenum
        /// </summary>
        private enum StandPosMasterId
        {
            Start = -1,
            Act01 = -2,
            Act02 = -3,
            End = -4,
        }

        /// <summary>
        /// 入れ替えモーションの種類
        /// </summary>
        public enum EventSwapMotionType
        {
            None,       //無効
            Normal,     //通常(今着ている衣装から確定)
            Index,      //直接ID指定
        }

        /// <summary>
        /// 性別の種類文字列
        /// </summary>
        public readonly static string[] SEX_STRING_ARRAY =
        {
            "Boy",
            "Girl",
        };

        /// <summary>
        /// 番号の種類文字列
        /// </summary>
        public readonly static string[] NUMBER_STRING_ARRAY =
        {
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
        };

        /// <summary>
        /// 枠番の種類文字列
        /// </summary>
        public readonly static string[] FRAMENUMBER_STRING_ARRAY =
        {
            "1 - 白",
            "2 - 黒",
            "3 - 赤",
            "4 - 青",
            "5 - 黄",
            "6 - 緑",
            "7 - 橙",
            "8 - 桃",
        };

        /// <summary>
        /// ゼッケンの色種類文字列
        /// </summary>
        public readonly static string[] ZEKKENCOLOR_STRING_ARRAY =
        {
            "深青",
            "深赤",
            "深緑",
            "黒",
            "白",
        };

        /// <summary>
        /// 身長の種類文字列
        /// </summary>
        public readonly static string[] HEIGHT_STRING_ARRAY =
        {
            "SS",
            "M",
            "L",
        };

        /// <summary>
        /// 胸のサイズ種類文字列
        /// </summary>
        public readonly static string[] BUST_STRING_ARRAY =
        {
            "SS",
            "S",
            "M",
            "L",
            "LL",
        };

        /// <summary>
        /// 肌の種類文字列
        /// </summary>
        public readonly static string[] SKIN_STRING_ARRAY =
        {
            "黄色",
            "桃色",
            "褐色",
            "色白",
        };

        /// <summary>
        /// 靴下の種類文字列
        /// </summary>
        public readonly static string[] SOCKS_STRING_ARRAY =
        {
            "生足",
            "HighSocksWhite",
            "HighSocksBlack",
            "KneeSocksWhite",
            "KneeSocksBlack",
            "TightsWhite",
            "TightsBlack",
            "TightsBrown",
        };

        /// <summary>
        /// 性格の種類文字列
        /// </summary>
        public readonly static string[] PERSONALITY_STRING_ARRAY =
        {
            "お淑やか",
            "キャピキャピ",
            "スポーティ",
            "ロリータ",
            "クール",
        };

        /// <summary>
        /// キャラタイプの種類文字列
        /// </summary>
        public readonly static string[] CHARA_TYPE_STRING_ARRAY =
        {
            "会話",
            "レース",
            "育成",
            "ライブ",
            "ホーム",
            "カットイン",
            "ミニ",
        };

        /// <summary>
        /// 育成のカットの種類文字列
        /// </summary>
        public readonly static string[] TRAINING_TYPE_STRING_ARRAY =
        {
            "共通",
            "成功",
            "失敗",
        };

        /// <summary>
        /// 育成のリソースの名前に付ける拡張子的なもの
        /// </summary>
        public const string TRAINING_SPT_TRA_NAME = "tra";

        /// <summary>
        /// シェーダーの種類文字列
        /// </summary>
        public readonly static string[] SHADER_TYPE_STRING_ARRAY =
        {
            "Unlit",
            "Toon",
            "NolineToon",
            "DitherToon",
            "DitherNolineToon",
            "ToonColor",
            "ToonAlpha",
            "NolineToonAlpha",
            "NormalColor",
            "VertexColor",
            "MultiMapColor",
            "ToonMap",
            "OptionMaskMap",
            "MaskColor",
            "CreateAudienceImpostorsMaskColor",
        };

        /// <summary>
        /// マルチテクスチャーモードの種類文字列
        /// </summary>
        public readonly static string[] MULTI_TEXTURE_MODE_STRING_ARRAY =
        {
            "None",
            "Normal",
            "PixelPerfect",
        };

        /// <summary>
        /// レース用のモーション(文字列)
        /// </summary>
        public static readonly string[] RaceMotionStrArray =
        {
            "Stop",
            "Idle",
            "WarmingUp",
            "StartSet",
            "Ready",
            "StartGo",
            "RunBlend",
            "OverRun",
            "Result",
            "Start_Set_Go",
            "Sweat",
        };

        /// <summary>
        /// レース用モーション(enum)
        /// </summary>
        public static readonly RaceMotionId[] RaceMotionIdArray =
        {
            RaceMotionId.Stop,
            RaceMotionId.Idle,
            RaceMotionId.WarmingUp,
            RaceMotionId.StartSet,
            RaceMotionId.Ready,
            RaceMotionId.StartGo,
            RaceMotionId.RunBlend,
            RaceMotionId.OverRun01,
            RaceMotionId.Result,
            RaceMotionId.Start_Set_Go,
            RaceMotionId.Sweat,
        };

        /// <summary>
        /// チークの種類
        /// </summary>
        public static readonly string[] CheekTypeNameArray =
        {
            "非表示",
            "0",
            "1",
            "2",
        };

        #endregion 定数


        #region static

        /// <summary>
        /// 簡易モーション確認 (実機orアセバンモードで起動するとまずこのモードになっている)
        /// </summary>
        public static bool IsMotionCheckMode { get; set; } = IsTerminal;

        #endregion static

        #region 変数

        /// <summary>
        /// インスタンス
        /// </summary>
        private static CharaViewer _instance;
        public static CharaViewer Instance
        {
            get { return _instance; }
        }

        /// <summary>
        /// 実機orアセバンを使うならIsTerminalがtrueになってログインおよびリソースバージョンチェックの通信を行う
        /// しかしアセバンを使わないならその通信は不要
        /// </summary>
        /// <returns></returns>
        public override bool IsNeedLogin() => IsTerminal;

        /// <summary>
        /// ログインが必要かどうか（アセバン使用の有無で判断している）
        /// </summary>
        public static bool IsTerminal
        {
            get
            {
#if UNITY_EDITOR
                if (!AssetBundleHelper.UsingAssetBundleResource())
                {
                    return false;
                }
#endif
                return true;
            }
        }

        /// <summary>
        /// メインカメラ
        /// </summary>
        [SerializeField]
        private Camera _mainCamera = null;
        public Camera MainCamera { get { return _mainCamera; } }

        /// <summary>
        /// カメラコントローラ（MainCamera）
        /// </summary>
        [SerializeField]
        private CameraController _cameraController = null;

        /// <summary>
        /// DirectionalLight
        /// </summary>
        [SerializeField]
        private Light _directionalLight = null;

        /// <summary>
        /// 現在選択中のキャラ番号
        /// </summary>
        private int _currentCharaIndex = 0;
        public int CurrentCharaIndex { get { return _currentCharaIndex; } set { _currentCharaIndex = value; } }

        /// <summary>
        /// キャラクターの情報
        /// </summary>
        private CharaViewerInfo[] _characterViewerInfo = new CharaViewerInfo[CHARACTER_MAX_COUNT];
        public CharaViewerInfo GetCharaViewerInfo() { return _characterViewerInfo[_currentCharaIndex]; }
        public CharaViewerInfo GetCharaViewerInfo(int index) { return _characterViewerInfo[index]; }

        /// <summary>
        /// モデルロード完了イベント
        /// </summary>
        private Action _onModelLoadAction = null;

        /// <summary>
        /// モデルロード時にエラーが発生したとき呼び出される
        /// </summary>
        private Action _onModelLoadError = default;

        /// <summary>
        /// 背景等のプレハブ
        /// </summary>
        private List<GameObject> _prefabObjectList = new List<GameObject>();
        public List<GameObject> PrefabObjectList => _prefabObjectList;

        /// <summary>
        /// GUIが選択中のキャラの種類
        /// </summary>
        private CharacterType _selectedCharacterType = CharacterType.Story;

        /// <summary>
        /// ストーリーレース用のモデルかどうか。
        /// </summary>
        /// <remarks>_selectedCharacterTypeがCharacterType.Raceの時のみ使用する。</remarks>
        private bool _isStoryRaceModel = false;

        /// <summary>
        /// キャラクターテクスチャ（マルチマップ確認用）
        /// とりあえず、メインキャラにだけ対応している（そんなにたくさん確認できない）
        /// </summary>
        private List<Texture> _charaTexture = new List<Texture>();

        // 実機orアセバンモードにおけるUI制御
        // 最終的にOnGUIによるメニュー描画は全てこちらに移行する予定
        #region UI制御

        /// <summary>
        /// UI制御用のViewのprefab
        /// </summary>
        [field: SerializeField, RenameField]
        public GameObject CharaViewerViewPrefab { get; private set; }

        /// <summary>
        /// UI制御用のprefab
        /// </summary>
        [field: SerializeField, RenameField]
        public GameObject CharaViewerUIPrefab { get; private set; }

        #endregion UI制御

        #region フェイシャル自動撮影機能のUI

        [field: SerializeField, RenameField]
        public GameObject FacialCaptureUI { get; private set; }

        [field: SerializeField, RenameField]
        public TextCommon FacialCaptureNameLabel { get; private set; }

        #endregion

        [SerializeField]
        private Material _multiTextureMaterial = null;

        private int _multiTextureIndex;
        private int _multiTextureRGBType;
        private int _multiTextureMode;

        /// <summary>
        /// 目の可動域チェック開始前のカメラ位置
        /// </summary>
        private Vector3 _saveCameraPosition;

        /// <summary>
        /// 目の可動域チェック開始前のカメラ向き
        /// </summary>
        private Quaternion _saveCameraRotation;

        /// <summary>
        /// 全キャラ設定フラグ
        /// </summary>
        private bool _isAllCharacterSetting = false;
        public bool IsAllCharacterSetting { get { return _isAllCharacterSetting; } set { _isAllCharacterSetting = value; } }

        /// <summary>
        /// アニメーションの名前データリスト（選んだキャラタイプによって再生するアニメーションが違うので都度交換する）
        /// </summary>
        private List<AnimationName> _animationNameList;

        /// <summary>
        /// 上記のリストの名前文字列配列（選んだキャラタイプによって再生するアニメーションが違うので都度交換する）
        /// </summary>
        private string[] _animationNameArray;

        /// <summary>
        /// OutGameモーションの名前コレクション (キャラ固有モーションがあるのでキャラを変えたらClearが必要)
        /// </summary>
        private readonly Dictionary<OutGameMotionSubCategory, string[]> _outGameAnimNameDict = new Dictionary<OutGameMotionSubCategory, string[]>();

        /// <summary>
        /// OutGameモーションのAssetPathコレクション (csv管理されていないのでPathをキャッシュしておく)
        /// </summary>
        public Dictionary<string, string> OutGameAnimPathDict { get; } = new Dictionary<string, string>();

        /// <summary>
        /// フェイシャルアニメーションの名前データリスト（選んだキャラタイプによって再生するアニメーションが違うので都度交換する）
        /// </summary>
        private List<AnimationName> _facialAnimationNameList;

        #region DirectionalLight

        /// <summary>
        /// DirectionalLightの向き
        /// </summary>
        private Vector3 _lightDir = new Vector3(90.0f, 0.0f, 0.0f);
        public Vector3 LightDir { get { return _lightDir; } set { _lightDir = value; } }

        /// <summary>
        /// ライトをカメラに追従刺せる
        /// </summary>
        private bool _isLightCamera = false;
        public bool IsLightCamera { get { return _isLightCamera; } set { _isLightCamera = value; } }

        /// <summary>
        /// キャプチャー時に使用するカメラ（参照）
        /// </summary>
        private Camera _captureCamera = null;
        public Camera CaptureCamera { set { _captureCamera = value; } }

        /// <summary>
        /// ライトをのっとる
        /// </summary>
        private bool _isLightOverride = false;
        public bool IsLightOverride => _isLightOverride;

        #endregion 

        /// <summary>
        /// カメラコントローラー
        /// </summary>
        [SerializeField]
        private CharaViewer3DCameraController _viewerCameraController = null;
        public CharaViewer3DCameraController ViewerCameraController => _viewerCameraController;

        /// <summary>
        /// カメラのルート
        /// </summary>
        private Transform _cameraRoot = null;
        public Transform CameraRoot => _cameraRoot;

        private EventSwapMotionType _swapMotionType = EventSwapMotionType.None;

        public EventSwapMotionType EnableEventSwapMotion
        {
            get => _swapMotionType;
            set
            {
                void SetEventModelLoop(bool enable, int subIndex, bool isDebugSwapData)
                {
                    for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
                    {
                        ModelController controller = GetCharaViewerInfo(i).ModelController;
                        if (controller == null)
                            continue;
                        if (controller is EventTimelineModelController eventModel)
                        {
#if UNITY_EDITOR
                            if (!IsTerminal && enable)
                            {
                                if (isDebugSwapData)
                                {
                                    eventModel.ReplaceSwapMotionRuntimeData(EventSwapMotionRuntimeData.CreateForDebug());
                                }
                                else
                                {
                                    eventModel.ReplaceSwapMotionRuntimeData(EventSwapMotionRuntimeData.Create(eventModel.GetBuildInfo(), targetScene: EventSwapMotionData.SwapMotionScene.All));
                                }
                            }
                            // アニメ差分の番号指定のフラグを立てる
                            eventModel.IsIndexSpecifyIndex = (value == EventSwapMotionType.Index) ? true : false;
#endif
                            eventModel.EnableSwapMotion = enable;
                            if (subIndex > 0)
                                eventModel.SwapMotionSubIndex = subIndex;
                        }
                    }
                }

                if (_swapMotionType == value)
                    return;

                _swapMotionType = value;
                switch (value)
                {
                    case EventSwapMotionType.None:
                        SetEventModelLoop(false, 0, false);
                        break;
                    case EventSwapMotionType.Index:
                        SetEventModelLoop(true, -1, true);
                        break;
                    case EventSwapMotionType.Normal:
                        SetEventModelLoop(true, 0, false);
                        break;
                }
            }
        }

        /// <summary>
        /// キャラのリスト（マスターから作成される）
        /// </summary>
        private List<GallopUtil.DebugViewerCharaData> _charaList = null;

        public List<GallopUtil.DebugViewerCharaData> CharaList { get { return _charaList; } }

        /// <summary>
        /// モブ衣装リスト
        /// </summary>
        public List<int> MobDressIdList = null;

        /// <summary>
        /// モブ衣装名配列
        /// </summary>
        public string[] MobDressStringArray = null;

        /// <summary>
        /// ライブの設定データ
        /// </summary>
        private Dictionary<int, Live.Master3dLive.LiveSettings> _liveSettingsCutt;   //MusicIdとCutt名の組み合わせ

        /// <summary>
        /// キャラ生成を非同期で行う。
        /// </summary>
        [SerializeField]
        private bool _isCreateCharaAsync = false;

        /// <summary>
        /// ModelControllerのグラフィック関連更新フラグ
        /// インスペクターで値を変えたいときにオフにする
        /// </summary>
        [SerializeField]
        private bool _isUpdateModelGraphicSetting = true;

        /// <summary>
        /// 選択できる表情一覧（CharaViewerWindow側にないのは実機からの起動でも使用するため）
        /// </summary>
        private FaceType[] _drivenKeyFaceTypeArray;

        public FaceType[] DrivenKeyFaceType
        {
            get { return _drivenKeyFaceTypeArray; }
        }

        /// <summary>
        /// 選択できる耳表情一覧（CharaViewerWindow側にないのは実機からの起動でも使用するため）
        /// </summary>
        private EarType[] _drivenKeyEarTypeArray;

        public EarType[] DrivenKeyEarType
        {
            get { return _drivenKeyEarTypeArray; }
        }

        /// <summary>
        /// 表情名
        /// </summary>
        private string[] _drivenKeyFaceNameArray = null;

        /// <summary>
        /// 耳表情名
        /// </summary>
        private string[] _drivenKeyEarNameArray = null;

        /// <summary>
        /// 眉表情名
        /// </summary>
        private string[] _drivenKeyFaceEyebrowTypeNameArray = null;

        /// <summary>
        /// 目表情名
        /// </summary>
        private string[] _drivenKeyFaceEyeTypeNameArray = null;

        /// <summary>
        /// 口表情名
        /// </summary>
        private string[] _drivenKeyFaceMouthTypeNameArray = null;

        #region 位置調整系

        private enum PositionChangeTypeX
        {
            None, //
            Asc,        // 単純横並び
            FaceToFace,　// 顔接触
        }

        private enum PositionChangeTypeY
        {
            None,
            ZeroY,      // 0の位置
            HeadAlign,　// 頭の位置
        }

        /// <summary>
        /// 縦移動させたかどうか
        /// </summary>
        private PositionChangeTypeX _positionChangeX = PositionChangeTypeX.None;

        /// <summary>
        /// 横移動させたかどうか
        /// </summary>
        private PositionChangeTypeY _positionChangeY = PositionChangeTypeY.None;

        #endregion 位置調整系

        /// <summary>
        /// Head_heightの表示設定。
        /// </summary>
        private bool _isHeadHeightVisble = false;
        #endregion 変数

        #region Prefab

        /// <summary>
        /// プレハブを生成して登録する
        /// </summary>
        /// <param name="prefab"></param>
        /// <returns></returns>
        public GameObject SetPrefabObject(GameObject prefab, int index)
        {
            var obj = Instantiate(prefab);
            obj.name = obj.name.Replace("(Clone)", "");
            _prefabObjectList[index] = obj;
            return obj;
        }

        /// <summary>
        /// プレハブの削除
        /// </summary>
        /// <param name="index"></param>
        public void RemovePrefabObject(int index)
        {
            if (_prefabObjectList[index] != null)
            {
                Destroy(_prefabObjectList[index]);
                _prefabObjectList[index] = null;
            }
        }

        /// <summary>
        /// プレハブの削除
        /// //TODO:CV:違いが判らぬ
        /// </summary>
        /// <param name="index"></param>
        public void RemoveAtPrefabObject(int index)
        {
            if (_prefabObjectList[index] != null)
            {
                Destroy(_prefabObjectList[index]);
            }
            //項目ごと消す
            _prefabObjectList.RemoveAt(index);
        }

        #endregion

        public string[] GetMultiTextureName()
        {
            List<string> name = new List<string>();
            for (int i = 0; i < _charaTexture.Count; i++)
            {
                name.Add(_charaTexture[i].name);
            }
            return name.ToArray();
        }

        public void SetMultiTexture(int mode, int index, int rgbMode)
        {
            _multiTextureRGBType = rgbMode;
            _multiTextureIndex = index;
            _multiTextureMode = mode;
        }

        /// <summary>
        /// 目線のターゲットをカメラにするかどうか
        /// </summary>
        /// <param name="enable"></param>
        public void SetEyeTargetEnable(bool enable)
        {
#if CYG_DEBUG && UNITY_EDITOR
            if (_whileFacialAutoCapture)
            {
                // フェイシャル自動撮影モード中はスキップ
                return;
            }
#endif
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();
            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (!charaViewerInfoArray[c].IsEnableEyeTraceController)
                    continue;

                var eyeTrace = charaViewerInfoArray[c].EyeTraceController;

                //視線は常に有効にしておく(無効はデフォルト位置で表示する)
#if UNITY_EDITOR
                eyeTrace.IsDebugDrawEyeVector = enable;
#endif
                eyeTrace.IsEnable = true;
                if (enable)
                {
                    eyeTrace.IsDefault = false;
                }
                else
                {
                    eyeTrace.IsDefault = true;
                    eyeTrace.TargetTransform.localPosition = StaticVariableDefine.CG3D.EyeTraceController.EYETARGET_DEFAULT_LOCALPOSITION;
                }
            }

            _viewerCameraController.SetEyeTrackingMode(enable);
        }

        /// <summary>
        /// 目の可動域チェック開始時と終了時にカメラの位置をリセットする
        /// </summary>
        /// <param name="enable"></param>
        public void BackUpCameraPosition(bool enable)
        {
            if (enable)
            {
                // 現在のカメラのTransformを保存しておく。
                _saveCameraPosition = _mainCamera.transform.position;
                _saveCameraRotation = _mainCamera.transform.rotation;
            }
            else
            {
                // 保存していたTransformに戻す。
                _mainCamera.transform.position = _saveCameraPosition;
                _mainCamera.transform.rotation = _saveCameraRotation;
            }
        }


        /// <summary>
        /// 目線のUpdate
        /// </summary>
        /// <param name="horizontalAngle"></param>
        /// <param name="verticalAngle"></param>
        /// <param name="distance"></param>
        /// <param name="isSyncCamera"></param>
        public void UpdateEyeTargetObjectPosition(float horizontalAngle, float verticalAngle, float distance, bool isSyncCamera)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (!charaViewerInfoArray[c].IsEnableEyeTraceController)
                    continue;

                var modelController = charaViewerInfoArray[c].ModelController;

                var eyeTrace = charaViewerInfoArray[c].EyeTraceController;

                var headTransform = modelController.GetHeadTransform();
                var basePosition = headTransform.position;

                // 両目のロケーターの中間地点を基準の座標にする。
                var eyeLocatorLTransform = eyeTrace.EyeLocatorLTransform;
                var eyeLocatorRTransform = eyeTrace.EyeLocatorRTransform;
                if ((eyeLocatorLTransform != null) && (eyeLocatorRTransform != null))
                {
                    basePosition = (eyeLocatorLTransform.position + eyeLocatorRTransform.position) * 0.5f;
                }

                // どのスケールでもキャラと目線のターゲットオブジェクトまでの距離が同じ比率になるようにする。
                distance *= modelController.GetTotalScale();

                var rotation = Quaternion.AngleAxis(horizontalAngle, headTransform.up) * Quaternion.AngleAxis(verticalAngle, headTransform.right);
                eyeTrace.TargetTransform.position = basePosition + rotation * (headTransform.forward * distance);

                // 現在のSlotにカメラを合わせる
                if (charaViewerInfoArray[c].SlotNo == _currentCharaIndex)
                {
                    var cameraTransform = _mainCamera.transform;

                    if (isSyncCamera)
                    {
                        cameraTransform.position = eyeTrace.TargetTransform.position;
                        cameraTransform.LookAt(basePosition);
                    }
                    else
                    {
                        cameraTransform.position = basePosition + (headTransform.forward * distance);
                        cameraTransform.LookAt(basePosition);
                    }
                }
            }
        }

        /// <summary>
        /// キャラクターリスト作成
        /// キャラが持っている服装、頭などの一覧を作成する
        /// </summary>
        private void MakeCharacterList()
        {
            _charaList = GallopUtil.MakeDebugViewerCharaDataList();
            // モブ関連も作っておく。
            MobDressIdList = new List<int>();
            var dressStringList = new List<string>();
            var dressList = MasterDataManager.Instance.masterDressData.dictionary.Values.ToList();
            foreach (var dressElement in dressList)
            {
                // Mobが着れるのは共通衣装のみ。
                if (dressElement.CharaId == 0)
                {
                    MobDressIdList.Add(dressElement.Id);
                    dressStringList.Add(dressElement.Name);
                }
            }
            MobDressStringArray = dressStringList.ToArray();
        }

        /// <summary>
        /// GUI表示用にキャラ名一覧を取得
        /// </summary>
        /// <returns></returns>
        public string[] GetCharacterString()
        {
            return GallopUtil.GetDebugViewerCharacterNameList(_charaList);
        }

        /// <summary>
        /// GUI表示用に頭名一覧を取得
        /// </summary>
        /// <param name="modelIndex"></param>
        /// <returns></returns>
        public string[] GetHeadString(int modelIndex)
        {
            return GallopUtil.GetDebugViewerHeadNameList(_charaList, modelIndex);
        }

        /// <summary>
        /// GUI表示用に衣装名一覧を取得
        /// </summary>
        /// <param name="modelIndex"></param>
        /// <param name="isMob"></param>
        /// <returns></returns>
        public string[] GetDressString(int modelIndex, bool isMob)
        {
            if (isMob)
            {
                return MobDressStringArray;
            }
            else
            {
                return GallopUtil.GetDebugViewerDressNameList(_charaList, modelIndex);
            }
        }

        /// <summary>UI表示用に衣装ID一覧を取得</summary>
        /// <returns></returns>
        public List<int> GetDressIdList(int modelIndex, bool isMob = false)
        {
            if (isMob)
            {
                return MobDressIdList;
            }
            else
            {
                return _charaList[modelIndex].DressIdList;
            }
        }

        public int GetCharaId(int modelIndex)
        {
            return _charaList[modelIndex].CharaId;
        }

        public int TerminalCharaId => GetCharaId(_terminalModelIndex);

        /// <summary>
        /// ModelIndexを設定しBuildInfoのCardIdとCharaIdを更新する (頭IDや衣装IDは変更しない)
        /// </summary>
        public void SetTerminalModelIndex(int index)
        {
            _terminalModelIndex = index;
            SetCardIdToPersonality(GetCardID(_terminalModelIndex, _terminalHeadIndex));
            SetBuildCharaId(GetCharaId(_terminalModelIndex));
        }

        public int GetDressID(int modelIndex, int dressIndex, bool isMob)
        {
            var dress = (isMob) ? MobDressIdList : _charaList[modelIndex].DressIdList;
            //エラー回避のためにindexを超えてしまう場合は配列の一番最初のものを返す
            if (dress.Count <= dressIndex)
            {
                _terminalDressIndex = 0;
                return dress[0];
            }
            return dress[dressIndex];
        }

        private int FindCharaListFromCharaId(int charaId)
        {
            for (int i = 0, count = _charaList.Count; i < count; i++)
            {
                if (_charaList[i].CharaId == charaId)
                {
                    return i;
                }
            }
            return -1;
        }

        private int FindDressIdFromIndex(int charaIndex, int dressId)
        {
            if (charaIndex < 0 || charaIndex >= _charaList.Count)
            {
                return -1;
            }
            for (int i = 0, count = _charaList[charaIndex].DressIdList.Count; i < count; i++)
            {
                if (_charaList[charaIndex].DressIdList[i] == dressId)
                {
                    return i;
                }
            }
            return -1;
        }

        public int GetCardID(int modelIndex, int headIndex)
        {
            var card = _charaList[modelIndex].CardIdList;
            return card[Mathf.Min(headIndex, (card.Count - 1))];
        }

        /// <summary>
        /// キャラIDと頭IDからカードIDを取得する
        /// </summary>
        /// <param name="charaID"></param>
        /// <param name="headID"></param>
        /// <returns></returns>
        public int GetCardIDByCharaIDAndHeadID(int charaID, int headID)
        {
            var chara = _charaList.Find(x => x.CharaId == charaID);
            if (chara == null)
            {
                return ModelLoader.InvalidCardId;
            }

            var card = chara.CardIdList;
            var headIndex = chara.HeadIdList.FindIndex(x => x == headID);
            return card[Mathf.Min(headIndex, (card.Count - 1))];
        }

        /// <summary>
        /// キャラIDと頭IDからカードIDを取得する
        /// </summary>
        /// <param name="charaID"></param>
        /// <param name="headID"></param>
        /// <param name="cardID"></param>
        /// <returns></returns>
        public bool TryGetCardIDByCharaIDAndHeadID(int charaID, int headID, out int cardID)
        {
            var chara = _charaList.Find(x => x.CharaId == charaID);
            if (chara == null)
            {
                cardID = ModelLoader.InvalidCardId;
                return false;
            }

            var card = chara.CardIdList;
            var exists = chara.HeadIdList.Exists(x => x == headID);
            if (!exists)
            {
                cardID = ModelLoader.InvalidCardId;
                return false;
            }
            
            var headIndex = chara.HeadIdList.FindIndex(x => x == headID);
            cardID = card[Mathf.Min(headIndex, (card.Count - 1))];
            return true;
        }

        public int GetHeadModelSubId(int modelIndex, int headIndex)
        {
            return _charaList[modelIndex].HeadIdList[headIndex];
        }

        public int GetUniformDressIndex(int modelIndex, bool isMob)
        {
            var dress = (isMob) ? MobDressIdList : _charaList[modelIndex].DressIdList;
            for (int i = 0; i < dress.Count; i++)
            {
                if (dress[i] == (int)ModelLoader.DressID.UniformSummer)
                {
                    return i;
                }
            }

            return 0;
        }


        public string[] GetFaceTypeName()
        {
            if (_drivenKeyFaceNameArray == null)
            {
                List<string> names = new List<string>();
                for (int i = 0; i < _drivenKeyFaceTypeArray.Length; i++)
                {
                    names.Add(_drivenKeyFaceTypeArray[i].ToString());
                }

                _drivenKeyFaceNameArray = names.ToArray();
            }
            return _drivenKeyFaceNameArray;
        }


        public string[] GetEarTypeName()
        {
            if (_drivenKeyEarNameArray == null)
            {
                List<string> names = new List<string>();
                for (int i = 0; i < _drivenKeyEarTypeArray.Length; i++)
                {
                    names.Add(_drivenKeyEarTypeArray[i].ToString());
                }

                _drivenKeyEarNameArray = names.ToArray();
            }
            return _drivenKeyEarNameArray;
        }

        public string[] GetFaceEyebrowTypeName()
        {
            if (_drivenKeyFaceEyebrowTypeNameArray == null)
            {
                List<string> names = new List<string>();
                var typeList = EnumUtil.GetEnumArray<FaceEyebrowType>();
                for (int i = 0; i < typeList.Length; i++)
                {
                    names.Add(typeList[i].ToString());
                }

                _drivenKeyFaceEyebrowTypeNameArray = names.ToArray();
            }
            return _drivenKeyFaceEyebrowTypeNameArray;
        }

        public string[] GetFaceEyeTypeName()
        {
            if (_drivenKeyFaceEyeTypeNameArray == null)
            {
                List<string> names = new List<string>();
                var typeList = EnumUtil.GetEnumArray<FaceEyeType>();
                for (int i = 0; i < typeList.Length; i++)
                {
                    names.Add(typeList[i].ToString());
                }

                _drivenKeyFaceEyeTypeNameArray = names.ToArray();
            }
            return _drivenKeyFaceEyeTypeNameArray;
        }

        public string[] GetFaceMouthTypeName()
        {
            if (_drivenKeyFaceMouthTypeNameArray == null)
            {
                List<string> names = new List<string>();
                var typeList = EnumUtil.GetEnumArray<FaceMouthType>();
                for (int i = 0; i < typeList.Length; i++)
                {
                    names.Add(typeList[i].ToString());
                }

                _drivenKeyFaceMouthTypeNameArray = names.ToArray();
            }
            return _drivenKeyFaceMouthTypeNameArray;
        }

        public static string[] GetHighlightName()
        {
            return EyeHighlightController.Debug_GetHighlightAnimStateNameList();
        }

        /// <summary>
        /// シーンの初期化
        /// </summary>
        /// <returns></returns>
        public IEnumerator InitializeScene()
        {
#if UNITY_EDITOR && CYG_DEBUG
            //IKコリジョンの編集を許可するように設定
            IK.IKCollisionData.IsEditorCollision = true;
#endif

            // 必要なリソースのDL
            var fileList = new List<string>();
            ModelLoader.AddZekkenCompositeResourcePath(fileList);   //ゼッケン合成に必要なアセットDL
            fileList.Add(VIEWER_PARAM_PATH);
            fileList.Add(FLARE_COLLISION_CHECK_LENS_FLARE_PATH);

            bool isDownload = false;
            DownloadManager.Instance.Download(fileList, () => { isDownload = true; });

            while (!isDownload)
            {
                yield return null;
            }

            // DLが終わったら初期化
            Init();

            // インスタンスの参照設定
            _instance = this;

#if UNITY_EDITOR
            // エディタならビューワーウィンドウ表示する
            CharaViewerWindow.ShowWindow();
#endif
            yield return InitCharacter();

        }

        /// <summary>
        /// シーンの終了処理
        /// </summary>
        public void FinalizeScane()
        {
#if UNITY_EDITOR && CYG_DEBUG
            //IKコリジョンの編集許可を無効にする
            IK.IKCollisionData.IsEditorCollision = false;
#endif
        }

        /// <summary>
        /// 初期起動時のキャラの配置
        /// </summary>
        /// <returns></returns>
        private IEnumerator InitCharacter()
        {
#if UNITY_EDITOR
            var viewerWindow = CharaViewerWindow.Instance;

            // 138203: CharaViewerWindowを表示する時はそちらの衣装Indexを利用する
            if (viewerWindow != null) _terminalDressIndex = viewerWindow.GetViewerPageInfo(_currentCharaIndex).DressIndex;

            if (viewerWindow != null &&
                viewerWindow.IsUseStaringSetting)
            {
                // CharaViewerWindowの初期配置を利用する起動
                SetCharacterType(CharacterType.Story + viewerWindow.StartingMemberCharaType);
                for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
                {
                    if (viewerWindow.StartingMemberCharaIdArray[i] == GameDefine.INVALID_CHARA_ID)
                        continue;
                    if (viewerWindow.StartingMemberDressIdArray[i] == GameDefine.INVALID_DRESS_ID)
                        continue;
                    int modelIndex = FindCharaListFromCharaId(viewerWindow.StartingMemberCharaIdArray[i]);
                    if (modelIndex < 0)
                        continue;
                    int dressIndex = FindDressIdFromIndex(modelIndex, viewerWindow.StartingMemberDressIdArray[i]);
                    if (dressIndex < 0)
                        continue;

                    _currentCharaIndex = i;

                    SetCardIdToPersonality(GetCardID(modelIndex, _terminalHeadIndex));
                    SetBuildCharaId(viewerWindow.StartingMemberCharaIdArray[i]);
                    SetDressId(viewerWindow.StartingMemberDressIdArray[i]);
                    SetBackDancer(_terminalBackDancerId);
                    BuildModel();

                    while (_characterViewerInfo[i].IsLoading == true)
                    {
                        yield return null;
                    }

                    // GUIの設定も変更
                    viewerWindow.GetViewerPageInfo(i).CharaTypeIndex = viewerWindow.StartingMemberCharaType;
                    viewerWindow.GetViewerPageInfo(i).CharacterModelIndex = modelIndex;
                    viewerWindow.GetViewerPageInfo(i).DressIndex = dressIndex;
                    viewerWindow.GetViewerPageInfo(i).Scale = GetCharacterScale();
                    viewerWindow.ChangeCharacterEvent();
                    SetCharacterPosition(i, viewerWindow.GetViewerPageInfo(i).Position);

                    yield return null;
                }

                _currentCharaIndex = 0;
            }
            else
#endif
            {
                // アセバン起動（バイナリ含む） or 初期配置を利用しない起動
                SetCharacterType(CharacterType.Story + _terminalCharacterTypeIndex);
                SetCardIdToPersonality(GetCardID(_terminalModelIndex, _terminalHeadIndex));
                SetBuildCharaId(GetCharaId(_terminalModelIndex));
                SetDressId(GetDressID(_terminalModelIndex, _terminalDressIndex, _terminalMobBuildModel));
                SetBackDancer(_terminalBackDancerId);
                yield return BuildEventModel();

#if UNITY_EDITOR
                if (CharaViewerWindow.HasInstance)
                {
                    var pageInfo = CharaViewerWindow.Instance.GetViewerPageInfo(0);
                    if (pageInfo != null)
                    {
                        pageInfo.Scale = GetCharacterScale();
                    }
                }
#endif
            }
            CameraReset();
        }

        /// <summary>
        /// 破棄
        /// </summary>
        protected override void OnDestroyForTool()
        {
            ModelLoader.UnloadZekkenCompositeResource();
            if (!IsTerminal)
            {
#if UNITY_EDITOR
                if (CharaViewerWindow.HasInstance)
                {
                    CharaViewerWindow.Instance.Close();
                }
#endif
            }
        }


        /// <summary>
        /// 初期化
        /// </summary>
        /// <returns></returns>
        private void Init()
        {
            // 初期立ち位置のセット
            for (int i = 0; i < CharaViewer.CHARACTER_MAX_COUNT; i++)
            {
                CharaViewer.DEFAULT_POSITION_ARRAY[i] = new Vector3(-0.4f * i, 0.0f, 0.0f);
            }

            // 描画先の設定
            UIManager.Instance.SetCameraTargetFromUITexture(_mainCamera);

            // ゼッケンに必要なリソースの読み込み
            ModelLoader.LoadZekkenCompositeResource();

            // マスターの登録されているキャラの一覧を用意する
            MakeCharacterList();

            // フェイシャル系の一覧取得
            _drivenKeyFaceTypeArray = FaceTypeUtil.GetFaceTypeArray();
            _drivenKeyEarTypeArray = FaceTypeUtil.GetEarTypeArray();

            // カメラの初期化
            // 入力制限はつけない
            _viewerCameraController.inputLimit = new Rect(0.0f, GUI_HEIGHT, int.MaxValue, int.MaxValue);
            _viewerCameraController.rotateSensitivity = 1.0f * 0.5f;
            _viewerCameraController.moveSensitivity = 0.005f * 0.5f;
            _viewerCameraController.pinchSensitivity = 0.02f * 0.5f;
            _viewerCameraController.isSimpleMode = true;
            _viewerCameraController.isDebugScene = true;
            _viewerCameraController.viewCamera.minHeight = -999.0f;
            _viewerCameraController.viewCamera.maxHeight = 999.0f;
            _viewerCameraController.viewCamera.SetRotationRange(-180.0f, 180.0f);   //回転限界値を設定する
            _cameraRoot = _viewerCameraController.gameObject.transform;
            var cameraController = _mainCamera.GetComponent<CameraController>();
            if (cameraController != null)
                cameraController.FixProjection = 0.0f;
            _mainCamera.transform.LookAt(new Vector3(0.0f, 2.0f, 0.0f));
            _mainCamera.gameObject.SetActive(true);

            //ライブのCutt名に関する要素の構築を行う
            {
                _liveSettingsCutt = new Dictionary<int, Live.Master3dLive.LiveSettings>();
                var dict = MasterDataManager.Instance.masterLiveData.dictionary;
                foreach (var element in dict.Values)
                {
                    int musicId = element.MusicId;
                    if (!ResourceManager.IsExistAsset(ResourcePath.LiveSettingsAssetBundlePath, ResourcePath.GetLiveSettingsPath(musicId)))
                    {
                        continue;
                    }
                    var liveSettingsData = Live.Master3dLive.LiveSettings.LoadLiveSettings(ResourcePath.GetLiveSettingsPath(musicId));
                    var cuttData = Live.Master3dLive.LiveSettings.GetItem(liveSettingsData, Live.Master3dLive.LiveSettings.DataType.Cutt);
                    if (cuttData == null)
                    {
                        continue;
                    }
                    _liveSettingsCutt.Add(musicId, cuttData);
                }
            }

            // キャラの情報クラスの作成と初期化
            _currentCharaIndex = 0;
            for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
            {
                _characterViewerInfo[i] = new CharaViewerInfo();
                _characterViewerInfo[i].BuildInfo = new EditableCharacterBuildInfo();
                // キャラ作成パラメータをIDから初期化
                _characterViewerInfo[i].SlotNo = i;
                _characterViewerInfo[i].Personality = InitCharaBuildInfo(_characterViewerInfo[i].BuildInfo);
                _characterViewerInfo[i].MiniPlayMotionIndexList.Clear();
                _characterViewerInfo[i].MiniMotionSetPlayedIndex = 0;
            }

            SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.Story);
            _cameraController.UseFixProjection = true;

            if (IsTerminal)
            {
                // ２本指の操作可能に。
                TouchManager.Instance.SetMultiTouch(true);

                LoadDrivenKeyFaceType_Terminal();

                // カメラのターゲット指定をHip追従にする
                CameraFollowChara = CameraFollowTarget.Hip;

                _terminalViewerParamData = ResourceManager.LoadOnScene<CharaViewerParamData>(VIEWER_PARAM_PATH);
            }
            else
            {
                CameraFollowChara = CameraFollowTarget.Position;
            }

            _propList.Clear();
            _flareCollisionCheckLensFlareRoot = null;

            // 小物アタッチ一覧取得
            var boneNames = EnumUtil.GetEnumArray<StoryCharaProp.AttachBone>();
            _attachBoneNameArray = new string[boneNames.Length];
            for (int i = 0; i < boneNames.Length; i++)
            {
                _attachBoneNameArray[i] = boneNames[i].ToString();
            }

            _facialRaceCheck.Setup(this);
            _terminalMiniMob.Setup(this);
        }

        /// <summary>
        /// 単純にBuildInfoを初期化設定する関数
        /// </summary>
        /// <param name="buildInfo"></param>
        /// <returns></returns>
        private static int InitCharaBuildInfo(EditableCharacterBuildInfo buildInfo)
        {
            int personality;
            buildInfo.ZekkenNumber = 1;

            var cardElement = MasterDataManager.Instance.masterCardData.GetWithCharaIdOrderByIdAsc(INITIALIZE_CHARAID);
            if (cardElement != null)
            {
                buildInfo.CardId = cardElement.Id;
                buildInfo.DressId = (int)ModelLoader.DressID.UniformSummer;
                buildInfo.Rebuild();

                return (int)buildInfo.DefaultPersonalityType;
            }

            buildInfo.CharaId = INITIALIZE_CHARAID;
            personality = (int)MasterDataManager.Instance.masterCharaType.GetDefaultPersonality(INITIALIZE_CHARAID);

            // 初期化する衣装IDを探す
            var clothList = MasterDataManager.Instance.masterDressData.dictionary.Values.ToList();
            MasterDressData.DressData trackSuitElem = null;
            for (int i = 0; i < clothList.Count; ++i)
            {
                if (clothList[i].Name == DefaultClothName)
                {
                    trackSuitElem = clothList[i];
                }
            }
            if (trackSuitElem != null)
            {
                buildInfo.DressId = trackSuitElem.Id;
            }

            return personality;
        }

        private static bool AddAssetPath(string assetPath, List<string> nameList, List<string> pathList, Dictionary<string, string> fileList)
        {
#if UNITY_EDITOR
            if (!AssetBundleHelper.UsingAssetBundleResource())
            {
                // アセバンでない時はSystem.IOを使ってローカルにアセットがあるかをチェックする
                var assetName = System.IO.Path.GetFileName(assetPath);
                if (fileList.ContainsKey(assetName))
                {
                    //あったら登録
                    nameList.Add(System.IO.Path.GetFileName(assetPath));
                    pathList.Add(assetPath);
                    return true;
                }
                return false;
            }
#endif
            if (ResourceManager.IsExistAsset(assetPath))
            {
                //あったら登録
                nameList.Add(System.IO.Path.GetFileName(assetPath));
                pathList.Add(assetPath);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 育成用フェイシャルアニメーション名リストの作成
        /// </summary>
        /// <param name="nameList"></param>
        /// <param name="pathList"></param>
        public void AddTrainingFacialMotionList(List<string> nameList, List<string> pathList)
        {
            const int CommonType = 0;

            Dictionary<string, string> fileDictionary = new Dictionary<string, string>();

#if UNITY_EDITOR
            if (!AssetBundleHelper.UsingAssetBundleResource())
            {
                // アセバンでない時
                {
                    var traRootPath = string.Format(ResourcePath.BundleResourcesAssetsPath + ResourcePath.TRAINING_CUTIN_TRA_FACIAL_TYPE_MOTION_ROOT, CommonType);
                    var asset = UnityEditor.AssetDatabase.FindAssets("t:DrivenKeyAnimation", new[] { traRootPath });
                    for (int i = 0; i < asset.Length; i++)
                    {
                        var assetPath = UnityEditor.AssetDatabase.GUIDToAssetPath(asset[i]);
                        fileDictionary.Add(System.IO.Path.GetFileNameWithoutExtension(assetPath), assetPath);
                    }
                }

                {
                    int personality = (int)GetCharaViewerInfo().BuildInfo.DefaultPersonalityType;
                    var traRootPath = string.Format(ResourcePath.BundleResourcesAssetsPath + ResourcePath.TRAINING_CUTIN_TRA_FACIAL_TYPE_MOTION_ROOT, personality);
                    var asset = UnityEditor.AssetDatabase.FindAssets("t:DrivenKeyAnimation", new[] { traRootPath });
                    for (int i = 0; i < asset.Length; i++)
                    {
                        var assetPath = UnityEditor.AssetDatabase.GUIDToAssetPath(asset[i]);
                        fileDictionary.Add(System.IO.Path.GetFileNameWithoutExtension(assetPath), assetPath);
                    }
                }

                // 特定のフォルダ以下からface_driven系のアニメーションを全部出す
                List<string> fileList = new List<string>();
                var folderPath = ResourcePath.GetBundleResourcesFullPath() + ResourcePath.TRAINING_CUTIN_TRA_FACIAL_MOTION_ROOT;
                fileList.AddRange(Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories));
                int fileCount = fileList.Count;
                for (int i = 0; i < fileCount; i++)
                {
                    string fullPath = fileList[i];
                    if (fullPath.IndexOf("face_driven") >= 0)
                    {
                        if (Path.GetExtension(fullPath).IndexOf(".meta") < 0)
                        {
                            string path = fullPath.Replace(Path.GetFileName(fullPath), Path.GetFileNameWithoutExtension(fullPath));
                            int index = path.IndexOf("3d/");
                            path = path.Remove(0, index);
                            AddAssetPath(path, nameList, pathList, fileDictionary);
                        }
                    }
                }
            }
#endif
        }

        /// <summary>
        /// 育成用アニメーション名リストの作成
        /// </summary>
        /// <returns></returns>
        private List<string> GetTrainingAnimationNameList()
        {
            List<string> nameList = new List<string>();
#if UNITY_EDITOR
            if (!AssetBundleHelper.UsingAssetBundleResource())
            {// アセバンでない時
                //Type00
                int levelType = 0;
                if (_trainingCategory == 1)
                {
                    levelType = _trainingLevelCategory;
                }

                string filter = string.Format(ResourcePath.TRAINING_VIEWER_CUT_MOTION_NAME,
                    0,
                    _trainingBigCategory,
                    _trainingCategory, _trainingSubCategory,
                    levelType, 0, 0, 0, 0, 0, "", TRAINING_SPT_TRA_NAME).Substring(14, 7); // 不要な部分は0で。
                string filter2 = "_" + TRAINING_SPT_TRA_NAME + "_";

                List<string> fileList = new List<string>();
                var folderPath = ResourcePath.GetBundleResourcesFullPath() + ResourcePath.TRAINING_CUTIN_TRA_BODY_MOTION_ROOT;
                fileList.AddRange(Directory.GetFiles(folderPath, "*.anim", SearchOption.AllDirectories));
                int fileCount = fileList.Count;
                for (int i = 0; i < fileCount; i++)
                {
                    string fullPath = fileList[i];
                    string path = fullPath.Replace(Path.GetFileName(fullPath), Path.GetFileNameWithoutExtension(fullPath));
                    int index = path.IndexOf("3d/");
                    path = path.Remove(0, index);

                    if (path.Contains(filter) && path.Contains(filter2) && !nameList.Contains(path))
                    {
                        nameList.Add(path);
                    }
                }
            }
#endif
            return nameList;
        }

        /// <summary>
        /// フェイシャルアニメーションの名前配列の取得
        /// </summary>
        /// <returns></returns>
        public string[] GetFacialAnimationNames()
        {
            List<string> names = new List<string>();
            if (_facialAnimationNameList != null)
            {
                for (int i = 0; i < _facialAnimationNameList.Count; i++)
                {
                    names.Add(_facialAnimationNameList[i].name);
                }
                return names.ToArray();
            }

            List<string> path = new List<string>();
            _facialAnimationNameList = new List<AnimationName>();

            switch (GetCharaViewerInfo().CharacterType)
            {
                case CharacterType.Story:
                    //StoryDirectで見れるので一旦未対応
                    break;
                case CharacterType.Home:
                    foreach (KeyValuePair<int, MasterCharaMotionSet.CharaMotionSet> pair in MasterDataManager.Instance.masterCharaMotionSet.dictionary)
                    {
                        if (pair.Key >= 22 && pair.Key <= 29)
                        {
                            // 暫定処置
                            // 使うかどうかわからないマスターが残っている為スキップ
                            continue;
                        }

                        string motionName = "";
                        var buildInfo = GetCharaViewerInfo().BuildInfo;
                        if (SimpleModelController.GetFacialMotion(pair.Value, buildInfo, ref motionName))
                        {
                            string name = System.IO.Path.GetFileName(motionName);
                            names.Add(name);
                            path.Add(motionName);
                        }
                        else
                        {
                            continue;
                        }
                    }
                    break;
                case CharacterType.Live:
                    //ライブにフェイシャルはない
                    break;
                case CharacterType.Race:
                    {
                        var list = RaceModelController.GetMotionPathForCharaViewer(GetCharaViewerInfo().BuildInfo);
                        for (int i = 0; i < list.Count; i++)
                        {
                            string name = System.IO.Path.GetFileName(list[i]);
                            names.Add(name);
                            path.Add(list[i]);
                        }
                    }
                    break;
                case CharacterType.Training:
                    AddTrainingFacialMotionList(names, path);
                    break;
            }

            for (int i = 0; i < names.Count; i++)
            {
                var animInfo = new AnimationName();
                animInfo.name = names[i];
                animInfo.index = i;
                animInfo.path = path[i];
                _facialAnimationNameList.Add(animInfo);
            }

            //ソートする
            _facialAnimationNameList.Sort((p1, p2) => string.Compare(p1.name, p2.name));

            for (int i = 0; i < _facialAnimationNameList.Count; i++)
            {
                names[i] = _facialAnimationNameList[i].name;
            }

            return names.ToArray();
        }

        private int _playDefaultAnimationIndex;
        private int _defaultAnimationIndex = -1;

        public int GetDefaultAnimationIndex()
        {
            if (_defaultAnimationIndex == -1)
            {
                // デフォルトはidle*と名のついたモーション
                _defaultAnimationIndex = Array.IndexOf(GetAnimationNames(), GetAnimationNames()
                    .Where(name => !name.Contains("mirror"))
                    .Where(name => name.Equals(SimpleModelController.IdleBodyMotion, System.StringComparison.OrdinalIgnoreCase))
                    .FirstOrDefault());
            }
            return _defaultAnimationIndex;
        }

        public int GetPlayDefaultAnimationIndex()
        {
            return _playDefaultAnimationIndex;
        }

        public static string GetAnimationBaseName(string name) => name.Split('_')[0];

        public IEnumerable<string> GetAnimationsStartBaseName(string name)
        {
            var baseName = GetAnimationBaseName(name);
            return MasterDataManager.Instance.masterEventMotionData
                .GetAllAnimCommand()
                .Select(command => command.Name)
                .Where(commandName => commandName.IndexOf(baseName) == 0);
        }

        /// <summary>
        /// 作成したアニメーションリストの破棄
        /// モデルの生成したとき等に使用
        /// </summary>
        public void ClearAnimationNameList()
        {
            _animationNameList = null;
            _animationNameArray = null;
            _outGameAnimNameDict.Clear();
            OutGameAnimPathDict.Clear();
        }

        /// <summary>
        /// アニメーションの名前一覧の作成
        /// </summary>
        /// <remarks>
        /// キャラクタータイプに応じて利用可能なモーション名の配列を作成する.
        /// エディターと実機の両方から利用する関数.
        /// </remarks>
        public string[] GetAnimationNames()
        {
            if (_animationNameArray != null)
            {
                return _animationNameArray;
            }

            // モーション名
            List<string> nameList = new List<string>();

            // マスターのIDリスト
            List<int> masterIds = null;

            _animationNameList = new List<AnimationName>();

            bool isRequiredSort = true;
            switch (GetCharaViewerInfo().CharacterType)
            {
                case CharacterType.Story:
                    // 反転モーションを除いたすべてのBaseアニメーションコマンドをリストに追加
                    var motionList = new List<MasterEventMotionData.EventMotionData>();
                    for (var key = EventCharaAnimatorBase.AnimTemper.Chara; key < EventCharaAnimatorBase.AnimTemper.Max; key++)
                    {
                        var list = MasterDataManager.Instance.masterEventMotionData.GetListWithTypeOrderByIdAsc((int)key);
                        motionList.AddRange(list);
                    }
                    nameList.AddRange(motionList
                        .Select(masterData => masterData.CommandName)
                        .Where(cmdName => !cmdName.Contains("mirror"))
                        .ToArray());

                    // 接触モーションはAとBがあるので両方追加
                    var pairNameList = nameList.FindAll(name => name.StartsWith(EventTimelineModelDriver.PAIR_MOTION_PREFIX));
                    foreach (var pairName in pairNameList)
                    {
                        nameList.Add(pairName + EventTimelineModelDriver.PAIR_MOTION_SUFFIX_A);
                        nameList.Add(pairName + EventTimelineModelDriver.PAIR_MOTION_SUFFIX_B);
                        nameList.Remove(pairName);
                    }

                    break;
                case CharacterType.Home:
                    // まずはマスターに入っているモーションを登録
                    masterIds = new List<int>();
                    foreach (KeyValuePair<int, MasterCharaMotionSet.CharaMotionSet> pair in MasterDataManager.Instance.masterCharaMotionSet.dictionary)
                    {
                        // モーション名の登録
                        nameList.Add(pair.Value.BodyMotion + "_" + pair.Value.FaceType + "_" + pair.Value.EarMotion + "_" + pair.Value.TailMotion);
                        // マスターのIDを使用したいのでマスターIDを取っておく
                        masterIds.Add(pair.Key);
                    }

                    // 次に立ち位置モーションの登録(立ち位置モーションは今のところマスターに登録されていない固有のモーションなので・・・。)
                    for (HomeDefine.StandPos pos = HomeDefine.StandPos.Character; pos <= HomeDefine.StandPos.Race_Support; pos++)
                    {
                        // 立ち位置のモーションをそれぞれセット
                        // ちょっと無理やりだがマスターの番号で、開始とACTと終了を判別する

                        // 開始
                        nameList.Add(pos.ToString() + "_Start");
                        masterIds.Add((int)StandPosMasterId.Start);

                        // Act01
                        nameList.Add(pos.ToString() + "_Act01");
                        masterIds.Add((int)StandPosMasterId.Act01);

                        // Act02
                        nameList.Add(pos.ToString() + "_Act02");
                        masterIds.Add((int)StandPosMasterId.Act02);

                        // 終了
                        nameList.Add(pos.ToString() + "_End");
                        masterIds.Add((int)StandPosMasterId.End);
                    }

                    break;
                case CharacterType.Live:
                    {
                        masterIds = new List<int>();
                        var dict = MasterDataManager.Instance.masterLiveData.dictionary;
                        foreach (var element in dict.Values)
                        {
                            int musicId = element.MusicId;
                            Live.Master3dLive.LiveSettings settings;
                            if (!_liveSettingsCutt.TryGetValue(musicId, out settings))
                            {
                                continue;
                            }
                            MasterLiveData.LiveData liveData = MasterDataManager.Instance.masterLiveData.Get(musicId);
                            name = settings.ParamStringArray[0];
                            name = name.Replace("Cutt_", "");
                            nameList.Add(string.Format("{0:0000} {1} {2}", musicId, name, liveData.Title));
                            masterIds.Add(musicId);
                        }
                    }
                    break;
                case CharacterType.Race:
                    for (int i = 0; i < RaceMotionStrArray.Length; i++)
                    {
                        nameList.Add(RaceMotionStrArray[i]);
                    }
                    break;
                case CharacterType.Training:
                    if (IsTerminal)
                    {// リスト選択せず直接指定する。
                        nameList.Add("");
                    }
                    else
                    {
                        nameList.AddRange(GetTrainingAnimationNameList());
                    }
                    break;
                case CharacterType.Mini:
                    nameList.AddRange(MasterMiniMotionSet.GetMiniCommandNameArray());
                    break;
            }

            for (int i = 0; i < nameList.Count; i++)
            {
                var animInfo = new AnimationName();
                animInfo.name = nameList[i];
                animInfo.index = i;

                // ホーム：CharaMotionSetMasterを使うためマスターのid
                if (masterIds != null)
                {
                    animInfo.masterId = masterIds[i];
                }

                _animationNameList.Add(animInfo);
            }

            //ソートする
            if (isRequiredSort)
            {
                _animationNameList.Sort((p1, p2) => string.Compare(p1.name, p2.name));
            }

            // モーション未選択時の再生モーション決定
            _playDefaultAnimationIndex = 0;
            for (int i = 0; i < _animationNameList.Count; i++)
            {
                nameList[i] = _animationNameList[i].name;
                if (nameList[i].Equals(SimpleModelController.IdleBodyMotion, System.StringComparison.OrdinalIgnoreCase))
                {
                    _playDefaultAnimationIndex = i;
                }
            }
            _animationNameArray = nameList.ToArray();
            return _animationNameArray;
        }

        /// <summary>
        /// OutGameモーションの名前一覧を取得する
        /// </summary>
        public string[] GetAnimationNames_OutGame(OutGameMotionSubCategory subCategory)
        {
            if (!_outGameAnimNameDict.ContainsKey(subCategory))
            {
                MakeOutGameAnimationName(subCategory);
            }

            return _outGameAnimNameDict[subCategory];
        }

        /// <summary>
        /// ローカルDiskからOutGameモーションを探して名前とパスをキャッシュする
        /// </summary>
        private void MakeOutGameAnimationName(OutGameMotionSubCategory subCategory)
        {
            // Bodyモーションのフォルダ
            var rootPath = ResourcePath.GetBundleResourcesFullPath() + ResourcePath.OUTGAME_MOTION_ROOT_PATH + subCategory.ToString() + "/Body";
            Debug.Assert(Directory.Exists(rootPath), $"OutGameのフォルダ内に指定されたサブカテゴリのフォルダがありません: SubCategory={subCategory}");

            // AnimationClipを収集する
            var files = GetOutGameAnimationEnumerable(rootPath);
            Debug.Assert(files.Count() > 0, $"利用可能なBodyモーションがありません: SubCategory={subCategory}");

            var nameList = new List<string>();
            foreach (var file in files)
            {
                // Windowsだとセパレータがバックスラッシュなので置き換える
                var fullPath = TextUtil.Replace(file, "\\", "/");

                // ファイル名を追加
                var animName = Path.GetFileNameWithoutExtension(fullPath);
                nameList.Add(animName);

                // AnimationClipをロードするためのPathを保存 (サブカテゴリフォルダからの相対Pathを保存)
                int index = fullPath.IndexOf(subCategory.ToString());
                var path = fullPath.Substring(index).Replace(".anim", "");
                OutGameAnimPathDict[animName] = path;
            }

            _outGameAnimNameDict[subCategory] = nameList.ToArray();
        }

        /// <summary>
        /// 指定されたフォルダ内のAnimationClipを収集する
        /// </summary>
        private IEnumerable<string> GetOutGameAnimationEnumerable(string rootPath)
        {
            int charaId = GetCharaViewerInfo().ModelController.GetCharaID();
            var charaTag = string.Format("_chr{0:D4}", charaId);

            // 絞り込み用のローカル関数
            bool IsMatchMotionName(string s)
            {
                if (s.Contains(charaTag)) return true;                // キャラ別モーションならtrue
                if (s.Contains("_type00")) return !s.Contains("_L_"); // キャラ共通モーションの場合は身長差分モーションを除外する
                return false;
            }

            // 条件に一致するモーションに絞り込む
            return Directory.EnumerateFiles(rootPath, "*.anim", SearchOption.AllDirectories)
                .Where(path => IsMatchMotionName(path));
        }

        /// <summary>
        /// 入力されたDirectionalLightの向きの指定
        /// </summary>
        /// <param name="direction"></param>
        private void UpdateLightDirection()
        {
#if CYG_DEBUG && UNITY_EDITOR
            // 自動スクショ中はそちらでライトの方向を変更している
            if (_whileAutoCapture)
            {
                return;
            }
#endif
            
            if (_directionalLight != null)
            {
                if (_isLightOverride)
                {
                    return;
                }

                if (_isLightCamera)
                {
                    if (_captureCamera != null)
                    {
                        _directionalLight.transform.localRotation = _captureCamera.transform.rotation;
                    }
                    else
                    {
                        _directionalLight.transform.localRotation = _mainCamera.transform.rotation;
                    }
                }
                else
                {
                    _directionalLight.transform.localRotation = Quaternion.Euler(_lightDir);
                }
            }
        }

        /// <summary>
        /// 入力されたカードID情報を元に性格を調べる
        /// </summary>
        /// <param name="cardId"></param>
        public void SetCardIdToPersonality(int cardId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CardId = cardId;
            if (cardId != ModelLoader.InvalidCardId)
            {
                buildInfo.Rebuild();
            }

            GetCharaViewerInfo().Personality = (int)buildInfo.DefaultPersonalityType;
        }

        /// <summary>
        /// 入力されたキャラIDをBuildInfoにセット
        /// </summary>
        /// <param name="charaId"></param>
        public void SetBuildCharaId(int charaId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CharaId = charaId;
        }

        /// <summary>
        /// 入力されたモブIDをBuildInfoにセット
        /// </summary>
        /// <param name="mobId"></param>
        public void SetMobId(int mobId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.MobId = mobId;
        }

        /// <summary>
        /// 入力された観客IDをBuildInfoにセット
        /// </summary>
        public void SetAudienceId(int audienceId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.AudienceId = audienceId;
        }

        /// <summary>
        /// 入力された性格をセット
        /// </summary>
        /// <param name="personality"></param>
        public void SetPersonality(ModelLoader.RacePersonalityType personality)
        {
            GetCharaViewerInfo().Personality = (int)personality;
        }

        /// <summary>
        /// 入力された汚れ情報をセット
        /// </summary>
        /// <param name="dirtArray"></param>
        public void SetDirtRate(float[] dirtArray)
        {
            System.Array.Copy(dirtArray, GetCharaViewerInfo().DirtRateArray, GetCharaViewerInfo().DirtRateArray.Length);
        }

        /// <summary>
        /// 入力された性別をセット
        /// </summary>
        /// <param name="type"></param>
        public void SetSexType(ModelLoader.GenderType type)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.GenderType = type;
        }

        /// <summary>
        /// 入力されたゼッケンの番号をセット
        /// </summary>
        /// <param name="index"></param>
        public void SetNumber(int index)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.ZekkenNumber = index;
        }

        /// <summary>
        /// 入力された身長をセット
        /// </summary>
        /// <param name="type"></param>
        public void SetHeight(ModelLoader.HeightType type)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.HeightType = type;
        }

        /// <summary>
        /// 入力された体型をセット
        /// </summary>
        /// <param name="type"></param>
        public void SetBodySize(ModelLoader.BodySize size)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.BodySize = size;
        }

        /// <summary>
        /// 入力された胸の大きさをセット
        /// </summary>
        /// <param name="type"></param>
        public void SetBust(ModelLoader.BustType type)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.BustType = type;
        }

        /// <summary>
        /// 入力された肌をセット
        /// </summary>
        /// <param name="type"></param>
        public void SetSkin(ModelLoader.SkinType type)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.SkinType = type;
        }

        /// <summary>
        /// 入力された靴下をセット
        /// </summary>
        /// <param name="type"></param>
        public void SetSocks(ModelLoader.SocksType type)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.SocksType = type;
        }

        /// <summary>
        /// 入力された衣装をセット
        /// </summary>
        /// <param name="dressId"></param>
        public void SetDressId(int dressId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.DressId = dressId;
            var dressData = MasterDataManager.Instance.masterDressData.Get(dressId);
            if (dressData != null)
            {
                buildInfo.HeadModelSubId = dressData.HeadSubId;
            }
            // 衣装を設定したらモーションも同じものを設定。
            SetMotionDressId(dressId);
        }
        /// <summary>
        /// 衣装別モーション参照用IDを設定。
        /// </summary>
        public void SetMotionDressId(int dressId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.MotionDressId = dressId;
        }

        /// <summary>
        /// 入力された頭のサブIDをセット
        /// </summary>
        /// <param name="subId"></param>
        public void SetHeadModelSubId(int subId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.HeadModelSubId = subId;
        }

        //TODO:CV:?
        public void SetAutoConfig(bool autoConfig)
        {
            GetCharaViewerInfo().UseAutoConfig = autoConfig;
        }

        public void SetBackDancer(int id)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.BackDancerColorId = id;
        }

        /// <summary>
        /// 現在のGUIが選択しているキャラのシーン
        /// </summary>
        /// <param name="type"></param>
        public void SetCharacterType(CharacterType type)
        {
            _selectedCharacterType = type;
        }

        /// <summary>
        /// ストーリーレース用のモデルかどうかを設定する。
        /// </summary>
        /// <param name="isStoryRaceModel"></param>
        public void SetStoryRaceModel(bool isStoryRaceModel)
        {
            _isStoryRaceModel = isStoryRaceModel;
        }

        /// <summary>
        /// 入力されたカラーIDをセット
        /// </summary>
        public void SetCharaDressColorSet(int dressId)
        {
            var dressData = MasterDataManager.Instance.masterDressData.Get(dressId);
            if (dressData != null && dressData.IsAreaMapDress())
            {
                var buildInfo = GetCharaViewerInfo().BuildInfo;
                buildInfo.CharaDressColorSetId = 0;
                if (_terminalCharacterColorSetIndex != 0)
                {
                    var colorSetDataList = MasterDataManager.Instance.masterCharaDressColorSet.GetListWithDressId(dressId);
                    buildInfo.CharaDressColorSetId = colorSetDataList[_terminalCharacterColorSetIndex].Id;
                }
            }
        }

        /// <summary>
        /// アタッチメントのモデルIDを設定
        /// </summary>
        public void SetAttachmentModelId(int attachmentModelId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.AttachmentModelId = attachmentModelId;
        }

        /// <summary>
        /// 透過度を設定する
        /// </summary>
        /// <param name="alpha">透過度 0.0f~1.0f</param>
        public void SetAlpha(float alpha)
        {
            var info = GetCharaViewerInfo();
            info.CharaAlpha = alpha;
        }

        /// <summary>
        /// 育成会話かどうか設定する
        /// </summary>
        /// <param name="isSingleMode">育成会話かどうか</param>
        public void SetSingleMode(bool isSingleMode)
        {
            var info = GetCharaViewerInfo();
            info.IsSingleMode = isSingleMode;
        }

        /// <summary>
        /// master_chara_dress_color_setのIDを設定
        /// </summary>
        public void SetColorSetId(int colorSetId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CharaDressColorSetId = colorSetId;
        }

#if UNITY_EDITOR
        /// <summary>
        /// 既存のモデルのシェーダーを調べてGUIに渡す
        /// </summary>
        /// <returns></returns>
        public ModelLoader.ShaderType GetShaderType()
        {
            //TODO:CV:メインのモデルだけでいいの？
            //if (_modelController == null)
            //    return ModelLoader.ShaderType.Unlit;

            //return _modelController.ShaderType;

            if (GetCharaViewerInfo().ModelController == null)
            {
                return ModelLoader.ShaderType.Unlit;
            }

            return GetCharaViewerInfo().ModelController.ShaderType;
        }
#endif

        /// <summary>
        /// モブ制作に必要なデータの用意
        /// </summary>
        /// <returns></returns>
        private int GetCharaViewrMobId()
        {
            // このモブの体型をベースにしてMobBuildInfoでカスタマイズ可能な部分だけビューワーからの操作を受け付ける。
            var masterMob = MasterDataManager.Instance.masterMobData.dictionary.Values.FirstOrDefault();
            return masterMob != null ? masterMob.MobId : ModelLoader.MOB_ID_NULL;
        }

        /// <summary>
        /// モブモデル構築。ビューワーで指定されたMobBuildInfoの情報で構築される。
        /// </summary>
        public void BuildMobModel(MobBuildInfo mobInfo)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CharaId = ModelLoader.MOB_CHARA_ID;
            buildInfo.CardId = ModelLoader.InvalidCardId;
            buildInfo.MobId = GetCharaViewrMobId();
            buildInfo.MobInfo = mobInfo;

            BuildModel(MobBuildType.ViewerParts);
        }

        /// <summary>
        /// ミニモブビルド
        /// </summary>
        public void BuildMiniMobModel(CharacterBuildInfo.MiniMobParam param)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.SetMiniMobParam(param);

            BuildModel();
        }

        /// <summary>
        /// モブモデル構築。ビューワーで指定されたMobBuildInfoの情報は無視され、mobIdで構築される。
        /// </summary>
        /// <param name="mobId"></param>
        public void BuildMobModel(int mobId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CharaId = ModelLoader.MOB_CHARA_ID;
            buildInfo.CardId = ModelLoader.InvalidCardId;
            buildInfo.MobId = mobId; // MobIdに値を設定してRebuildすることで、mob_data.csvの内容をCharacterBuildInfoに設定させる。
            buildInfo.AudienceId = ModelLoader.AUDIENCE_ID_NULL;
            buildInfo.Rebuild();

            BuildModel(MobBuildType.MobId);
        }

        /// <summary>
        /// 観客IDを指定して観客作成
        /// </summary>
        public void BuildAudienceModel(int audienceId)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CharaId = ModelLoader.AUDIENCE_CHARA_ID;
            buildInfo.CardId = ModelLoader.InvalidCardId;
            buildInfo.MobId = ModelLoader.MOB_ID_NULL;
            buildInfo.AudienceId = audienceId;
            buildInfo.Rebuild();

            BuildModel(MobBuildType.MobId);
        }
        public void BuildAudienceModel(AudienceBuildInfo audienceInfo)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.CharaId = ModelLoader.AUDIENCE_CHARA_ID;
            buildInfo.CardId = ModelLoader.InvalidCardId;
            buildInfo.MobId = ModelLoader.MOB_ID_NULL;
            buildInfo.AudienceId = 1;
            buildInfo.AudienceInfo = audienceInfo;

            BuildModel(MobBuildType.ViewerParts);
        }

        /// <summary>
        /// モデルの生成
        /// </summary>
        /// <param name="mobBuildType"></param>
        public void BuildModel(MobBuildType mobBuildType = MobBuildType.ViewerParts)
        {
            // ロード中フラグ
            GetCharaViewerInfo().IsLoading = true;

            // モデル作り直しになる可能性あるのでカメラの追従をやめる
            FollowChara(CameraFollowTarget.None);

            // タイプ別の処理
            switch (_selectedCharacterType)
            {
                case CharacterType.Story: //会話
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.Story);
                    OnClickBuildEventModelButton(mobBuildType);
                    _cameraController.UseFixProjection = true;
                    break;
                case CharacterType.Race: //レース
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.Race);
                    OnClickBuildRaceModelButton(mobBuildType);
                    _cameraController.UseFixProjection = false;
                    break;
                case CharacterType.Training: //育成
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.SingleMode);
                    OnClickBuildTrainModelButton(mobBuildType);
                    _cameraController.UseFixProjection = false;
                    break;
                case CharacterType.Live: //ライブ
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.Live);
                    OnClickBuildLiveCharacterModel(mobBuildType);
                    _cameraController.UseFixProjection = false;
                    break;
                case CharacterType.Home:
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.Home);
                    OnClickBuildHomeCharacterModel(mobBuildType);
                    _cameraController.UseFixProjection = false;
                    break;
                case CharacterType.CutIn:
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.CampaignCutDirect);
                    OnClickBuildCutInModel(mobBuildType);
                    _cameraController.UseFixProjection = false;
                    break;
                case CharacterType.Mini:
                    SceneManager.Instance.SetDebugOverrideSceneId(SceneDefine.SceneId.Circle);
                    OnClickBuildMiniCharacterModel();
                    _cameraController.UseFixProjection = false;
                    break;
            }
        }

        /// <summary>
        /// モデルがロード完了したときに呼ばれる
        /// </summary>
        /// <param name="onComplete"></param>
        public void SetOnModelLoadAction(Action onComplete, Action onError)
        {
            _onModelLoadAction = onComplete;
            _onModelLoadError = onError;
        }

        /// <summary>
        /// 濡れ設定
        /// </summary>
        /// <param name="isWet"></param>
        public void SetWet(bool isWet)
        {
            GetCharaViewerInfo().IsWet = isWet;
        }

        /// <summary>
        /// 汚れ設定
        /// </summary>
        /// <param name="isDirt"></param>
        public void SetDirt(bool isDirt)
        {
            GetCharaViewerInfo().IsDirt = isDirt;
        }

        /// <summary>
        /// 汗設定
        /// </summary>
        /// <param name="isSweat"></param>
        public void SetSweat(bool isSweat)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                var modelController = charaViewerInfoArray[c].ModelController; 
                if (modelController == null)
                    continue;

                if (modelController.IsOmitSweat)
                    continue;

                var sweatController = modelController.GetModelComponent<Model.Component.GallopSweatController>();
                if (sweatController == null && !modelController.IsOmitSweat)
                {
                    // 汗はモデルコントローラによって必須、必要に応じてと分かれるのでなければ付ける形にしている
                    var sweatModel = ResourceManager.LoadOnScene<GameObject>(ResourcePath.GetSweatModelResourcePath(ResourcePath.DefaultSweatID));
                    var sweatLocator = ResourceManager.LoadOnScene<GameObject>(ResourcePath.GetSweatLocatorResourcePath(charaViewerInfoArray[c].BuildInfo));
                    if (sweatModel != null && sweatLocator != null)
                    {
                        sweatController = ModelLoader.AttachSweatToModelController(modelController, sweatLocator, sweatModel);
                    }
                }
                
                // #117139対応 汗の描画順を制御する必要が出てきた
                if (modelController is EventTimelineModelController eventTimelineModelController)
                {
                    eventTimelineModelController.SetSweatController(sweatController);
                }

                if (charaViewerInfoArray[c].IsSweat == isSweat)
                    continue;

                charaViewerInfoArray[c].IsSweat = isSweat;

                if (charaViewerInfoArray[c].IsSweat)
                {
                    sweatController?.ShowLocatorAll();
                }
                else
                {
                    sweatController?.HideLocatorAll();
                }
            }
        }

        public void SetHeadHeightVisible(bool isVisible)
        {
            _isHeadHeightVisble = isVisible;
        }

        //TODO:CV:?
        public void SetPopularity(int popularity)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.Popularity = popularity;
            buildInfo.GateInPopularity = popularity;
        }

        /// <summary>
        /// レースのオーバーランモーション確認用の着順設定
        /// </summary>
        /// <param name="finishOrder"></param>
        public void SetFinishOrder(int finishOrder)
        {
            GetCharaViewerInfo().FinishOrder = finishOrder;
        }

        /// <summary>
        /// レースのモーション確認用でレースのグレードを指定する
        /// </summary>
        /// <param name="grade"></param>
        public void SetGrade(RaceDefine.Grade grade)
        {
            GetCharaViewerInfo().Grade = grade;
        }

        /// <summary>
        /// 番号入力
        /// </summary>
        /// <param name="no"></param>
        public void SetGateInMotion(int no)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.GateInMotionNo = no;
        }

        public void SetGroundRight(bool right)
        {
            GetCharaViewerInfo().Rotation = right ? RaceDefine.Rotation.Right : RaceDefine.Rotation.Left;
        }

        public void SetPlayer(bool player)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.IsPlayer = player;
        }

        public void SetFrameNo(ModelLoader.TrackSuitColor color)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.FrameColor = color;
        }

        public void SetZekkenColor(ModelLoader.ZekkenColor color)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.ZekkenColor = color;
        }

        public void SetZekkenFontColor(ModelLoader.ZekkenFontColor color)
        {
            var buildInfo = GetCharaViewerInfo().BuildInfo;
            buildInfo.ZekkenFontColor = color;
        }

        public void SetMotionParameter(float cameraBlendRate, float blendRate, float motionSpeed)
        {
            var info = GetCharaViewerInfo();

            info.CameraBlendRate = cameraBlendRate;
            info.RunBlendRate = blendRate;
            info.RunMotionSpeed = motionSpeed;
            info.StoryMotionSpeed = motionSpeed;
            info.MiniMotionSpeed = motionSpeed;
            _terminalAnimationSpeed = motionSpeed;
        }
        public void SetRaceTimeScale(float timeScale)
        {
            //TODO:CV:全キャラ共通にする？

            if (GetCharaViewerInfo().ModelController == null)
            {
                return;
            }
            var raceModelController = GetCharaViewerInfo().ModelController as RaceModelController;
            if (raceModelController == null)
            {
                return;
            }
            raceModelController.SetAnimationSpeedScale(timeScale);
            raceModelController.SetCySpringTimeScale(timeScale);
        }

        public const float RUN_MOTION_SPEED_MAX = 1.3f; // RaceParamDefine.runMotionSpeedMaxで設定されている値。

        private int _trainingType = 1;
        private int _trainingNumber = 2; // 人数
        private int _trainingCuttDiff = 1; // 演出差分
        private int _trainingFolder = 0;
        //1から開始される
        private int _trainingBigCategory = 1;
        private int _trainingCategory = 1;
        private int _trainingSubCategory = 1;
        private int _trainingLevelCategory = 1;

        private int _trainingMotion = 1;
        private int _trainingMotionDiff = 0;
        private int _trainingSceneCut = 2;    //カット番号

        public void SetTrainingType(int type)
        {
            _trainingType = type;
        }

        public void SetTrainingNumber(int number)
        {
            _trainingNumber = number;
        }

        public void SetTrainingCuttDiff(int no)
        {
            _trainingCuttDiff = no;
        }

        public void SetTrainingFolder(int no)
        {
            _trainingFolder = no;
        }

        public void SetTrainingBigCategory(int no)
        {
            _trainingBigCategory = no;
        }

        public void SetTrainingCategory(int no)
        {
            //中カテゴリ、1から始まる
            _trainingCategory = no + 1;
        }

        public void SetTrainingSubCategory(int no)
        {
            //小カテゴリ、1から始まる
            _trainingSubCategory = no + 1;
        }

        public void SetTrainingLevelCategory(int level)
        {
            //トレーニングレベル
            _trainingLevelCategory = level + 1;
        }

        public void SetTrainingMotion(int no, int diffNo)
        {
            _trainingMotion = no;
            _trainingMotionDiff = diffNo;
        }

        public void SetTrainingCutt(int no)
        {
            _trainingSceneCut = no;
        }

        /// <summary>
        /// ビルド情報からキャラのモデルに必要なパスをリストで取得
        /// </summary>
        /// <param name="buildInfo"></param>
        /// <param name="isWet"></param>
        /// <param name="isDirt"></param>
        /// <param name="rotation"></param>
        /// <param name="liveSettingDic"></param>
        /// <param name="forceDownloadWetDirt">濡れ汚れをCSVの衣装設定によらず表示する</param>
        /// <returns></returns>
        public static List<string> GetDownloadFileList(EditableCharacterBuildInfo buildInfo, bool isWet = false, bool isDirt = false, RaceDefine.Rotation rotation = RaceDefine.Rotation.Left, Dictionary<int, Live.Master3dLive.LiveSettings> liveSettingDic = null, bool forceDownloadWetDirt = false)
        {
            var fileList = new List<string>();
            {
                var temp = buildInfo.CharaBuildPathInfo.GetDownloadFileList();
                for (int i = 0; i < temp.Count; i++)
                {
                    if (string.IsNullOrEmpty(temp[i]))
                    {
                        continue;
                    }
                    fileList.Add(temp[i]);
                }
            }

            {
                var temp = buildInfo.ClothBuildPathInfo.GetDownloadFileList();
                for (int i = 0; i < temp.Count; i++)
                {
                    if (string.IsNullOrEmpty(temp[i]))
                    {
                        continue;
                    }
                    fileList.Add(temp[i]);
                }
            }

            if (isWet)
            {
                ModelLoader.AddWetTextureResourcePath(fileList, buildInfo, forceDownloadWetDirt);
            }

            if (isDirt)
            {
                ModelLoader.AddDirtTextureResourcePath(fileList, buildInfo, forceDownloadWetDirt);
            }

            fileList.Add(ResourcePath.GetSweatLocatorResourcePath(buildInfo));
            fileList.Add(ResourcePath.GetSweatModelResourcePath(ResourcePath.DefaultSweatID));

            var builder = new System.Text.StringBuilder();

            //モデルタイプ別
            switch (buildInfo.ControllerType)
            {
                case ModelLoader.ControllerType.Paddock:
                case ModelLoader.ControllerType.EventTimeline:
                    {
                        var animCommandArray = MasterDataManager.Instance.masterEventMotionData.GetAllAnimCommand();
                        int num = animCommandArray.Length;
                        for (int i = 0; i < num; i++)
                        {
                            var command = animCommandArray[i];
                            int charaId = buildInfo.CharaId;
                            int subId = buildInfo.HeadModelSubId;
                            var pathList = StoryMotionInfo.GetClipPathList(builder, charaId, subId, command);
                            foreach (var clipName in pathList)
                            {
                                var seFilePath = ResourcePath.GetMotionSePathStoryTimeline(clipName);
                                if (AssetBundleHelper.UsingAssetBundleResource())
                                {
                                    if (ResourceManager.IsExistAsset(clipName))
                                    {
                                        fileList.Add(clipName);
                                    }
                                    if (ResourceManager.IsExistAsset(seFilePath))
                                    {
                                        fileList.Add(seFilePath);
                                    }
                                }
                                else
                                {
                                    fileList.Add(clipName);
                                    fileList.Add(seFilePath);
                                }
                            }
                        }

                        //アイドルモーションはコマンドの含まれない
                        {
                            int charaId = buildInfo.CharaId;
                            int sub = 0;
                            var prefix = ResourcePath.GetCharacterEventOriginalMotionPath(charaId, sub);
                            prefix = string.Format(ResourcePath.CharacterEventMotionPrefixFormat, prefix, charaId, sub);
                            for (int i = 0; i < ResourcePath.eventRequiredAnimStateSuffixes.Count; ++i)
                            {
                                string motionName = ResourcePath.eventRequiredAnimStateSuffixes[i];
                                var path = prefix + motionName;
                                if (AssetBundleHelper.UsingAssetBundleResource())
                                {
                                    if (ResourceManager.IsExistAsset(path))
                                    {
                                        fileList.Add(path);
                                    }
                                }
                                else
                                {
                                    fileList.Add(path);
                                }
                            }
                        }
                    }
                    break;

                case ModelLoader.ControllerType.Race:
                    {
                        ShadowController.RegisterDownload(fileList);
                        var bodyMotionPath = RaceModelController.GetBodyMotionPathFromCharaViewer(buildInfo, false, rotation);
                        fileList.AddRange(bodyMotionPath);

                        var facialMotionPath = RaceModelController.GetMotionPathForCharaViewer(buildInfo);
                        foreach (var facialPath in facialMotionPath)
                        {
                            if (AssetBundleHelper.UsingAssetBundleResource(facialPath))
                            {
                                if (ResourceManager.IsExistAsset(facialPath))
                                {
                                    fileList.Add(facialPath);
                                }
                            }
                            else
                            {
                                fileList.Add(facialPath);
                            }
                        }
                    }
                    break;

                case ModelLoader.ControllerType.HomeStand:
                    {
                        // マスターモーション
                        var motionSetFileList = new List<string>();
                        foreach (KeyValuePair<int, MasterCharaMotionSet.CharaMotionSet> pair in MasterDataManager.Instance.masterCharaMotionSet.dictionary)
                        {
                            if (pair.Key >= 22 && pair.Key <= 29)
                            {
                                // 暫定処置
                                // 使うかどうかわからないマスターが残っている為スキップ
                                continue;
                            }
                            SimpleModelController.GetAssetPathsMotion(buildInfo, pair.Value, ref motionSetFileList);
                        }
                        // キャラによっては存在しない（使われない）アニメも含まれているのでそれを削除。
                        foreach (var filePath in motionSetFileList)
                        {
                            if (AssetBundleHelper.UsingAssetBundleResource(filePath))
                            {
                                if (ResourceManager.IsExistAsset(filePath))
                                {
                                    fileList.Add(filePath);
                                }
                            }
                            else
                            {
                                fileList.Add(filePath);
                            }
                        }
                    }
                    break;

                case ModelLoader.ControllerType.Live:
                    {
                        //Cuttデータダウンロードしたら合わせてモーションもDLされるはず
                        foreach (var music in MasterDataManager.Instance.masterLiveData.dictionary)
                        {
                            Live.Master3dLive.LiveSettings settings;
                            if (!liveSettingDic.TryGetValue(music.Value.MusicId, out settings))
                            {
                                continue;
                            }
                            var cuttPath = ResourcePath.GetLiveCuttPrefabPath(settings.ParamStringArray[0]);
                            fileList.Add(cuttPath);
                        }
                        // フレアコリジョン
                        var masterCharaData = MasterDataUtil.MasterCharaAndMobAccessor.Create(buildInfo);
                        if (masterCharaData != null && buildInfo.DressElement != null)
                        {
                            var bodyFlarePath = ResourcePath.GetLiveBodyFlareCollisionParameterPath(buildInfo.DressElement,
                                (int)masterCharaData.Sex, (int)masterCharaData.Height, (int)masterCharaData.Shape, (int)masterCharaData.Bust);
                            fileList.Add(bodyFlarePath);
                            int hairId = masterCharaData.IsMob ? masterCharaData.MobCharaHairModel : -1;
                            var headFlarePath = ResourcePath.GetLiveHeadFlareCollisionParameterPath(buildInfo.CharaId, buildInfo.HeadModelSubId, hairId);
                            fileList.Add(headFlarePath);
                            var tailFlarePath = ResourcePath.GetLiveTailFlareCollisionParameterPath(buildInfo.CharaId, buildInfo.DressElement);
                            if (!string.IsNullOrEmpty(tailFlarePath))
                            {
                                fileList.Add(tailFlarePath);
                            }
                        }
                    }
                    break;

                case ModelLoader.ControllerType.Training:
                    {
                        //育成は分からないので再生時にDLする
                    }
                    break;
                case ModelLoader.ControllerType.Mini:
                    {
                        foreach (var elm in MasterMiniMotionSet.MiniMotionSetDic)
                        {
                            MiniModelController.GetAssetPathsMotion(buildInfo, elm.Value, ref fileList);
                        }
                    }
                    break;
            }

            return fileList;
        }

        private IEnumerator StartDownloadFromCharacterBuildInfo(EditableCharacterBuildInfo buildInfo)
        {
            var fileList = GetDownloadFileList(buildInfo, GetCharaViewerInfo().IsWet, GetCharaViewerInfo().IsDirt, GetCharaViewerInfo().Rotation, _liveSettingsCutt, forceDownloadWetDirt: true);

            bool isFinishDownload = false;
            DownloadManager.Instance.Download(fileList, () => { isFinishDownload = true; });

            while (true)
            {
                if (isFinishDownload)
                    break;

                yield return null;
            }
        }

        public void PlayFacialAnimation(int index)
        {
            if (_facialAnimationNameList == null)
                return;
            if (_facialAnimationNameList.Count <= index)
                return;

            var clip = ResourceManager.LoadOnScene<DrivenKeyAnimation>(_facialAnimationNameList[index].path);
            GetCharaViewerInfo().ModelController.PlayDrivenKey(clip);

            var earPath = _facialAnimationNameList[index].path.Replace("_face_", "_ear_");
            var earClip = ResourceManager.LoadOnScene<DrivenKeyAnimation>(earPath);
            GetCharaViewerInfo().ModelController.PlayEarDrivenKey(earClip);
        }

        public void PlayIKAnimation(int index, bool isAdd)
        {
            if (_animationNameList == null)
                return;
            if (_animationNameList.Count <= index)
                return;

            int animeIndex = _animationNameList[index].index;
            switch (GetCharaViewerInfo().CharacterType)
            {
                case CharacterType.Story:
                    OnEventAnimChanged(true, isAdd);
                    GetCharaViewerInfo().IsStoryAnimationPlaying = true;
                    break;

                default:
                    PlayAnimation(index);
                    break;
            }
        }

        public void PlayAnimation(int index)
        {
            if (_animationNameList == null)
                return;
            if (_animationNameList.Count <= index)
                return;

            int animeIndex = _animationNameList[index].index;
            switch (GetCharaViewerInfo().CharacterType)
            {
                case CharacterType.Story:
                    OnEventAnimChanged(false, false);
                    GetCharaViewerInfo().IsStoryAnimationPlaying = true;
                    break;
                case CharacterType.Home:
                    OnHomeAnimChanged(_animationNameList[index]);
                    GetCharaViewerInfo().IsStoryAnimationPlaying = true;
                    break;
                case CharacterType.Live:
                    OnLiveAnimChanged(animeIndex, _animationNameList[index].masterId);
                    break;
                case CharacterType.Race:
                    OnRaceAnimChanged(animeIndex);
                    break;
                case CharacterType.Training:
                    OnTrainAnimChanged(index);
                    break;
                case CharacterType.Mini:
                    OnMiniAnimChanged(_animationNameList[index]);
                    break;
            }
            
            if(GetCharaViewerInfo().ModelController is EventTimelineModelController eventTimelineModelController)
            {
                eventTimelineModelController.MotionSeController.SetFloorMaterialId(_terminalMotionSeMaterialId);
            }
        }

        public void PlayMiniCharaAnimation(int index)
        {
            if (GetCharaViewerInfo().MiniPlayMotionIndexList.Count <= index || _animationNameList == null)
            {
                return;
            }
            var nameIndex = GetCharaViewerInfo().MiniPlayMotionIndexList[index];
            if (_animationNameList.Count <= nameIndex)
            {
                return;
            }
            GetCharaViewerInfo().MiniMotionSetPlayedIndex = index;
            GetCharaViewerInfo().MiniModelController.DbgCharaViewerMotionEndCallback = (isAdd) =>
            {
                if (GetCharaViewerInfo().MiniPlayMotionIndexList.Count <= GetCharaViewerInfo().MiniMotionSetPlayedIndex)
                {
                    //再生中に削除された？
                    GetCharaViewerInfo().MiniModelController.DbgCharaViewerMotionEndCallback = null;
                    return;
                }

                var curIndex = GetCharaViewerInfo().MiniPlayMotionIndexList[GetCharaViewerInfo().MiniMotionSetPlayedIndex];
                var motionSetData = MasterDataManager.Instance.masterMiniMotionSet.Get(_animationNameList[curIndex].name);
                if (motionSetData == null)
                {
                    GetCharaViewerInfo().MiniModelController.DbgCharaViewerMotionEndCallback = null;
                    return;
                }
                if (motionSetData.IsAddMotion() != isAdd)
                {// 待ってるモーションのレイヤーが違うので無視。前回再生していたものが残って通知がくる場合がある。
                    return;
                }
                ++GetCharaViewerInfo().MiniMotionSetPlayedIndex;
                if (GetCharaViewerInfo().MiniMotionSetPlayedIndex < GetCharaViewerInfo().MiniPlayMotionIndexList.Count)
                {
                    var nextIndex = GetCharaViewerInfo().MiniPlayMotionIndexList[GetCharaViewerInfo().MiniMotionSetPlayedIndex];
                    GetCharaViewerInfo().MiniModelController.PlayMotion(_animationNameList[nextIndex].name, isForce: true);
                    if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
                    {
                        ResetCySpring();
                    }
                }
                else
                {// ここで終わり。
                    GetCharaViewerInfo().MiniModelController.DbgCharaViewerMotionEndCallback = null;
                }
            };
            GetCharaViewerInfo().MiniModelController.SetEnableFacialLocatorControll(true);
            GetCharaViewerInfo().MiniModelController.PlayMotion(_animationNameList[nameIndex].name, isForce: true);
            GetCharaViewerInfo().AnimationCurrentTime = 0f;
            GetCharaViewerInfo().IsAnimatoinPlaying = true;
            if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
            {
                ResetCySpring();
            }
        }
        public void PlayMiniEffect(int id)
        {
            if (GetCharaViewerInfo().MiniModelController == null)
            {
                return;
            }
            GetCharaViewerInfo().MiniModelController.SetEnableFacialLocatorControll(false);
            if (id <= 0)
            {
                GetCharaViewerInfo().MiniModelController.StopEffect();
            }
            else
            {
                GetCharaViewerInfo().MiniModelController.PlayEffect(id);
            }
        }

        private void OnHomeAnimChanged(AnimationName animationName)
        {
            if (GetCharaViewerInfo().ModelController == null)
                return;

            var homeModelController = GetCharaViewerInfo().ModelController as HomeStandModelController;
            if (homeModelController == null)
            {
                return;
            }
            
            switch (animationName.masterId)
            {
                case (int)StandPosMasterId.Start:
                    // こっちは立ち位置モーション：開始
                    homeModelController.InitByPosition((HomeDefine.StandPos)System.Enum.Parse(typeof(HomeDefine.StandPos), animationName.name.Replace("_Start", "")));
                    homeModelController.PlayStandPosStartMotion();
                    homeModelController.ResetCyspring();
                    break;

                case (int)StandPosMasterId.Act01:
                    // こっちは立ち位置モーション：Act01（放置モーション）
                    homeModelController.InitByPosition((HomeDefine.StandPos)System.Enum.Parse(typeof(HomeDefine.StandPos), animationName.name.Replace("_Act01", "")));
                    homeModelController.PlayStandPosActMotion(1);
                    break;

                case (int)StandPosMasterId.Act02:
                    // こっちは立ち位置モーション：Act02（放置モーション）
                    homeModelController.InitByPosition((HomeDefine.StandPos)System.Enum.Parse(typeof(HomeDefine.StandPos), animationName.name.Replace("_Act02", "")));
                    homeModelController.PlayStandPosActMotion(2);
                    break;

                case (int)StandPosMasterId.End:
                    // こっちは立ち位置モーション終了
                    homeModelController.InitByPosition((HomeDefine.StandPos)System.Enum.Parse(typeof(HomeDefine.StandPos), animationName.name.Replace("_End", "")));
                    homeModelController.PlayStandPosEndMotion();
                    break;

                default:
                    // こっちはマスターに登録されているモーション
                    MasterCharaMotionSet.CharaMotionSet master = MasterDataManager.Instance.masterCharaMotionSet.Get(animationName.masterId);
                    homeModelController.PlayMotion(master, isForce: true);
                    break;
            }
            
            if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
            {
                ResetCySpring();
            }
        }

        /// <summary>
        /// 涙（目元の表示）
        /// </summary>
        /// <param name="visible"></param>
        public void VisibleTear(bool visible)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                    continue;
                if (charaViewerInfoArray[c].ModelController.IsVisibleTear() == visible)
                    continue;

                charaViewerInfoArray[c].ModelController.SetVisibleTear(visible);
                if (charaViewerInfoArray[c].ModelController is CutInModelController)
                {
                    //フェイシャル制御だがここではフェイシャルないので、完全表示にする
                    charaViewerInfoArray[c].ModelController.SetTearAlpha(1.0f);
                }
            }
        }

        /// <summary>
        /// 流れる涙の再生
        /// </summary>
        /// <param name="id"></param>
        /// <param name="slot"></param>
        /// <param name="isReverse"></param>
        public void PlayTeardrop(int id, int slot, bool isReverse)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                {
                    continue;
                }
                if (id < 0)
                {
                    charaViewerInfoArray[c].ModelController.StopTeardrop(slot);
                }
                else
                {
                    charaViewerInfoArray[c].ModelController.PlayTeardrop(id, slot, isReverse);
                }
            }
        }

        /// <summary>
        /// 漫画目設定。
        /// </summary>
        public void SetMangame(int id)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                {
                    continue;
                }
                if (id < 0)
                {
                    charaViewerInfoArray[c].ModelController.SetFace(FaceType.Base);
                }
                else
                {
                    charaViewerInfoArray[c].ModelController.SetMangame(id);
                }
            }
        }

        /// <summary>
        /// 顔影の表示
        /// </summary>
        /// <param name="isVisible"></param>
        public void SetFaceShadowVisible(bool isVisible)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                {
                    continue;
                }
                charaViewerInfoArray[c].ModelController.SetFaceShadowVisible(isVisible);
            }
        }

        private void OnRaceAnimChanged(int index)
        {
            var info = GetCharaViewerInfo();
            if (info.ModelController == null)
                return;

            info.SerialRaceMotionArray.Clear();
            var animationType = RaceMotionIdArray[index];
            if (animationType == RaceMotionId.Stop)
            {
                info.IsRaceAnimationPlay = false;
                return;
            }

            if (info.ModelController != null)
            {
                info.IsAnimationStartFrame = true;
                info.IsRaceAnimationPlay = true;
                var raceModelController = info.ModelController as RaceModelController;
                if (raceModelController != null)
                {
                    info.IsRaceAnimationNextState = false;
                    raceModelController.DebugRacePlayableAnimation.OnLoopAction = (state) =>
                    {
                        info.OnRaceLoopAction(state);
                    };
                    if (animationType == RaceMotionId.OverRun01)
                    {
                        raceModelController.ReplaceOverRunMotion(info.FinishOrder - 1, info.Rotation, isStoryRace: false);
                        raceModelController.SetMotion(RaceMotionId.RunBlend, 1.0f, 1.0f);
                        info.SerialRaceMotionArray.Add(animationType);
                        info.SerialRaceMotionArray.Add(RaceMotionId.OverRunFinishOrder);
                    }
                    else if (animationType == RaceMotionId.Result)
                    {
                        // モブはリザルトモーションがない
                        if (info.BuildInfo.IsNormalChara())
                        {
                            var dress = MasterDataManager.Instance.masterDressData.Get(info.ModelController.GetDressId());
                            if (dress != null)
                            {
                                //-1は0が1位なので、そのオフセット
                                var charaId = info.ModelController.GetCharaID();
                                var cardId = info.ModelController.GetCardId();
                                var clip = RaceLoaderResultAnim.LoadResultMotionClip(charaId, cardId, dress,
                                    (ModelLoader.DefaultPersonalityType)
                                    RaceResultScene.GetMotionVariationId(info.BuildInfo.CharaId),
                                    info.FinishOrder - 1, info.Grade, RaceDefine.RaceType.Debug);
                                //本来Resultポーズはレースとは別のアニメーションクリップに存在するので、空いている箇所に無理やり当てはめる
                                raceModelController.PlayOneShotMotion(clip);
                            }
                        }
                    }
                    else if (animationType == RaceMotionId.Start_Set_Go)
                    {
                        raceModelController.SetMotion(RaceMotionId.WarmingUp, 1.0f, 0.0f);
                        info.SerialRaceMotionArray.Add(RaceMotionId.StartSet);
                        info.SerialRaceMotionArray.Add(RaceMotionId.StartGo);
                    }
                    else
                    {
                        raceModelController.SetMotion(animationType, info.RunMotionSpeed, 0.0f);
                    }
                }

                switch (animationType)
                {
                    case RaceMotionId.StartSet:
                        info.ModelController.SetVisible_Cheek(true, 0);
                        break;
                    case RaceMotionId.StartGo:
                        info.ModelController.SetVisible_Cheek(true, 1);
                        break;
                    case RaceMotionId.RunBlend:
                        info.ModelController.SetVisible_Cheek(true, 2);
                        break;
                    default:
                        info.ModelController.SetVisible_Cheek(false);
                        break;
                }
                if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
                {
                    ResetCySpring();
                }
            }
        }

        private void OnEventAnimChanged(bool isIkAnimation, bool isAddIkAnimation)
        {
            if (GetCharaViewerInfo().ModelController == null)
                return;

            GetCharaViewerInfo().StoryModelDriver.IsIKAnimationPlay = isIkAnimation;
            GetCharaViewerInfo().StoryModelDriver.IsAddIkAnimation = isAddIkAnimation;
            if (GetCharaViewerInfo().ModelController is EventTimelineModelController)
            {
                GetCharaViewerInfo().StartStoryAnimationAtIndex(0);
            }
            if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
            {
                ResetCySpring();
            }
        }

        private IEnumerator PlayTrainingMotion(int index)
        {
            string clipPath = "";
            string facialPath = "";
            if (IsTerminal)
            {
                GetTrainingMotionPath(out clipPath, out facialPath);
            }
            else
            {
                clipPath = _animationNameList[index].name;
                facialPath = clipPath.Replace("/Body/", "/Facial/") + "_face_driven";
            }
            var fileList = new List<string>();
            if (ResourceManager.IsExistAsset(clipPath))
            {
                fileList.Add(clipPath);
            }
            else
            {
                yield break;
            }

            if (ResourceManager.IsExistAsset(facialPath))
            {
                fileList.Add(facialPath);
            }
            else
            {
                yield break;
            }
            bool isFinishDownload = false;

            DownloadManager.Instance.Download(fileList, () => { isFinishDownload = true; });

            while (true)
            {
                if (isFinishDownload)
                    break;

                yield return null;
            }

            AnimationClip clip = ResourceManager.LoadOnScene<AnimationClip>(clipPath);

            var controller = GetCharaViewerInfo().ModelController as CutInModelController;
            if (controller != null)
            {
                var faceClip = ResourceManager.LoadOnScene<DrivenKeyAnimation>(facialPath);
                if (controller.TryGetModelComponent<CharaCutInAnimator>(out var animator))
                {
                    animator.IsEnable = true;
                    animator.SetClip(CutInPlayableAnimator.AnimationClipTableIndex.Common1, clip);
                    animator.Play(CutInPlayableAnimator.LayerIndex.Base, 0);
                }
                controller.PlayDrivenKey(faceClip);
                if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
                {
                    ResetCySpring();
                }
            }
        }
        public void GetTrainingMotionPath(out string clipPath, out string facialPath)
        {
            CutIn.Cutt.TimelineKeyMotionBase.TrainingMotionType motionType = CutIn.Cutt.TimelineKeyMotionBase.TrainingMotionType.Common;

            // GUIのプルダウンに合わせて表示するために入力されたIndexをenumに合わせている
            switch (_trainingType)
            {
                case 0:
                    motionType = CutIn.Cutt.TimelineKeyMotionBase.TrainingMotionType.Common;
                    break;
                case 1:
                    motionType = CutIn.Cutt.TimelineKeyMotionBase.TrainingMotionType.Success;
                    break;
                case 2:
                    motionType = CutIn.Cutt.TimelineKeyMotionBase.TrainingMotionType.Bad;
                    break;
                case 3:
                    motionType = CutIn.Cutt.TimelineKeyMotionBase.TrainingMotionType.GreatSuccess;
                    break;
            }

            int levelType = 0;
            if (_trainingCategory == 1)
            {
                levelType = _trainingLevelCategory;
            }

            //育成はパスの取得が整備されていないので、整備されるまで直指定。
            clipPath = string.Format(ResourcePath.TrainingViewerCutInBodyMotionPath,
                                        _trainingFolder,
                                        _trainingBigCategory,
                                        _trainingCategory, _trainingSubCategory,
                                        levelType,
                                        _trainingCuttDiff, _trainingNumber,
                                        _trainingMotion, _trainingMotionDiff,
                                        _trainingSceneCut, ResourcePath.GetTrainingCutInMotionSuffix(motionType), TRAINING_SPT_TRA_NAME);

            facialPath = string.Format(ResourcePath.TrainingViewerCutInFacialMotionPath,
                                        _trainingFolder,
                                        _trainingBigCategory,
                                        _trainingCategory, _trainingSubCategory,
                                        levelType,
                                        _trainingCuttDiff, _trainingNumber,
                                        _trainingMotion, _trainingMotionDiff,
                                        _trainingSceneCut, ResourcePath.GetTrainingCutInMotionSuffix(motionType), TRAINING_SPT_TRA_NAME);
        }

        private void OnTrainAnimChanged(int index)
        {
            if (GetCharaViewerInfo().ModelController == null)
                return;

            StartCoroutine(PlayTrainingMotion(index));
        }

        private const string ClipName = "default";

        public void SetLiveMotionPosition(int pos)
        {
            GetCharaViewerInfo().LiveMotionPositionNo = pos;
        }

        public float GetLiveMotionTime()
        {
            if (GetCharaViewerInfo().LiveMotionSequence == null)
                return 0f;

            return GetCharaViewerInfo().AnimationCurrentTime / GetCharaViewerInfo().LiveMaxTime;
        }

        public void SetLiveMotionTime(float normalizeTime)
        {
            if (GetCharaViewerInfo().LiveMotionSequence == null)
                return;

            GetCharaViewerInfo().AnimationCurrentTime = normalizeTime * GetCharaViewerInfo().LiveMaxTime;
        }

        public void SetLiveIK(bool isIk)
        {
            if (GetCharaViewerInfo().ModelController == null)
                return;

            if (GetCharaViewerInfo().ModelController.TryGetModelComponent<LiveIK>(out var result))
            {
                result.IsEnableIKCol = isIk;
                if (result.IsIKCollisition)
                {
                    //強制で設定が入るようにしておく
                    result.SetType(LiveIK.IKType.Limb);
                    result.SetType(LiveIK.IKType.None);
                }
                else
                {
                    //IKコリジョンない場合は通常
                    result.SetType(isIk ? LiveIK.IKType.Limb : LiveIK.IKType.None);
                }
            }
        }


        public bool IsPlayLiveMotion()
        {
            if (GetCharaViewerInfo().LiveMotionSequence == null)
                return false;

            return GetCharaViewerInfo().IsAnimatoinPlaying;
        }

        public void PauseLiveMotion()
        {
            if (GetCharaViewerInfo().LiveMotionSequence == null)
                return;

            GetCharaViewerInfo().IsAnimatoinPlaying = false;
        }

        public void ResumeLiveMotion()
        {
            if (GetCharaViewerInfo().LiveMotionSequence == null)
                return;

            GetCharaViewerInfo().IsAnimatoinPlaying = true;
        }

        private void OnLiveAnimChanged(int index, int musicId)
        {
            if (GetCharaViewerInfo().ModelController == null)
                return;

            if (GetCharaViewerInfo().LiveCharacterObjectList.Count == 0)
                return;
            var liveCharacterObject = GetCharaViewerInfo().LiveCharacterObjectList[0];

            Live.Master3dLive.LiveSettings settings;
            if (!_liveSettingsCutt.TryGetValue(musicId, out settings))
            {
                return;
            }

            var cuttPath = ResourcePath.GetLiveCuttPrefabPath(settings.ParamStringArray[0]);

            var cuttPrefab = ResourceManager.LoadOnScene<GameObject>(cuttPath);
            GetCharaViewerInfo().LiveTimelineControl = cuttPrefab.GetComponent<LiveTimelineControl>();
            var iPosition = GetCharaViewerInfo().LiveMotionPositionNo;
            var seqDataIndex = GetCharaViewerInfo().LiveTimelineControl.data.characterSettings.motionSequenceIndices[iPosition];
            GetCharaViewerInfo().LiveTimelineControl.SetCharactorLocator(iPosition, liveCharacterObject);
            GetCharaViewerInfo().LiveMotionSequence = new LiveTimelineMotionSequence();
            GetCharaViewerInfo().LiveMotionSequence.Initialize(liveCharacterObject, iPosition, seqDataIndex, GetCharaViewerInfo().LiveTimelineControl);
            GetCharaViewerInfo().LiveMotionSequence.SetCurrentSheetIndex(LiveTimelineDefine.SheetIndex.MainLive);
            GetCharaViewerInfo().LiveMaxTime = GetCharaViewerInfo().LiveTimelineControl.data.timeLength;
            GetCharaViewerInfo().AnimationCurrentTime = 0f;
            GetCharaViewerInfo().IsAnimatoinPlaying = true;

            if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
            {
                ResetCySpring();
            }
        }

        /// <summary>
        /// ライブのアニメーション更新
        /// </summary>
        private void LateUpdateLiveAnimation()
        {
            for (int c = 0; c < CHARACTER_MAX_COUNT; c++)
            {
                if (GetCharaViewerInfo(c).ModelController == null)
                {
                    continue;
                }
                // ライブ再生
                if (GetCharaViewerInfo(c).IsAnimatoinPlaying && GetCharaViewerInfo(c).LiveMotionSequence != null)
                {
                }
                else
                {
                    //一時停止中は無効にする
                    foreach (var character in GetCharaViewerInfo(c).LiveCharacterObjectList)
                    {
                        if (character == null || character.CurrentModelObject == null)
                            continue;

                        if (character.IsPositionNodePositionAddParent)
                            character.CurrentLiveModelController.PositionNode.localPosition = Math.VECTOR3_ZERO;
                    }
                }
            }
        }

        /// <summary>
        /// Liveのアニメーション更新
        /// </summary>
        private void UpdateLiveAnimation()
        {
            for (int c = 0; c < CHARACTER_MAX_COUNT; c++)
            {
                if (GetCharaViewerInfo(c).ModelController == null)
                {
                    continue;
                }
                if (!(GetCharaViewerInfo(c).ModelController is LiveModelController))
                {
                    GetCharaViewerInfo(c).IsAnimatoinPlaying = false;
                    GetCharaViewerInfo(c).LiveMotionSequence = null;
                }

                // ライブ再生
                if (GetCharaViewerInfo(c).IsAnimatoinPlaying && GetCharaViewerInfo(c).LiveMotionSequence != null)
                {
                    GetCharaViewerInfo(c).AnimationCurrentTime += Time.deltaTime * _terminalAnimationSpeed;
                    var frameRate = GameDefine.MAX_FRAME_RATE;
                    var currentFrame = (int)(GetCharaViewerInfo(c).AnimationCurrentTime * frameRate);
                    GetCharaViewerInfo(c).LiveMotionSequence.AlterUpdate(currentFrame, GetCharaViewerInfo(c).AnimationCurrentTime, 0f, frameRate);
                    if (GetCharaViewerInfo(c).LiveUseFormationOffsetTrack)
                    {
                        GetCharaViewerInfo(c).LiveTimelineControl.AlterUpdate_FormationOffsetForCharaViewer(GetCharaViewerInfo(c).LiveTimelineControl.data.GetWorkSheet(0), currentFrame, GetCharaViewerInfo(c).LiveCharacterObjectList);
                    }
                }
            }
        }

        private const int FLARE_COLLISION_CHECK_CAMERA_DEPTH = 100;
        private const int FLARE_COLLISION_NORMAL_CAMERA_DEPTH = 19;
        private GameObject _flareCollisionCheckLensFlareRoot = null;
        public bool IsFlareCollisionCheckMode { get => _flareCollisionCheckLensFlareRoot != null; }

        /// <summary>
        /// FlareCollision確認モードに変更。
        /// </summary>
        public void CheckFlareCollision()
        {
            if (_flareCollisionCheckLensFlareRoot != null)
            {
                return;
            }
            var go = ResourceManager.LoadOnScene<GameObject>(FLARE_COLLISION_CHECK_LENS_FLARE_PATH);
            _flareCollisionCheckLensFlareRoot = GameObject.Instantiate(go, MainCamera.transform);
            // GameViewにギズモを表示させたいためMainCameraでの描画を最終結果とする。
            // 見た目が本番相当でなくなるがコリジョン確認のみなので許容とする。
            MainCamera.targetTexture = null;
            MainCamera.depth = FLARE_COLLISION_CHECK_CAMERA_DEPTH;
        }
        public void ResetCheckFlareCollision()
        {
            if (_flareCollisionCheckLensFlareRoot == null)
            {
                return;
            }
            GameObject.Destroy(_flareCollisionCheckLensFlareRoot);
            _flareCollisionCheckLensFlareRoot = null;
            MainCamera.targetTexture = UIManager.Instance.UITexture;
            MainCamera.depth = FLARE_COLLISION_NORMAL_CAMERA_DEPTH;
        }

        private void OnMiniAnimChanged(AnimationName animationName)
        {
            if (GetCharaViewerInfo().MiniModelController == null)
            {
                return;
            }
            GetCharaViewerInfo().MiniModelController.SetEnableFacialLocatorControll(true);
            GetCharaViewerInfo().MiniModelController.PlayMotion(animationName.name, isForce: true);
            GetCharaViewerInfo().AnimationCurrentTime = 0f;
            GetCharaViewerInfo().IsAnimatoinPlaying = true;
            if (GetCharaViewerInfo().IsResetCySpringOnPlayAnimation)
            {
                ResetCySpring();
            }
        }

        public void SetShader(ModelLoader.ShaderType shaderType)
        {
            if (GetCharaViewerInfo().RootObj == null)
                return;
            GetCharaViewerInfo().ModelController.SetShader(shaderType);
        }

        public void ApplyCySpring()
        {
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.ApplyCySpring();
                GetCharaViewerInfo().CySpringSnapeShot = controller.BakeCySpringSnapshot();
                EnableCySpring(GetCharaViewerInfo().IsEnableCySpring);
            }
            else
            {
                MiniModelController miniCtrl = GetCharaViewerInfo().MiniModelController;
                
                if (miniCtrl != null)
                {
                    miniCtrl.ResumeCySpring();
                    GetCharaViewerInfo().CySpringSnapeShot = miniCtrl.BakeCySpringSnapshot();
                    EnableCySpring(GetCharaViewerInfo().IsEnableCySpring);
                }
            }
        }

        private const string facialExportPath = "Assets/_GallopExport/";

        [SerializeField]
        private Texture2D resultTex;
        // フェイシャルチェック用のイメージ生成
        public void OnClick_ExportFacialCheck()
        {
            if (GetCharaViewerInfo().ModelController == null)
                return;

            // レンダーテクスチャー
            RenderTexture renderTex = new RenderTexture(1024, 768, 24);
#if CYG_DEBUG
            renderTex.name = "CharaViewer.OnClick_ExportFacialCheck.renderTex";
#endif
            RenderTexture.active = renderTex;

            // カメラ設定
            GameObject cameraObject = new GameObject("FacialCamera");
            Camera camera = cameraObject.AddComponent<Camera>();
            camera.fieldOfView = 30.0f;
            camera.nearClipPlane = 0.1f;
            camera.farClipPlane = 100.0f;
            camera.clearFlags = CameraClearFlags.SolidColor;
            camera.targetTexture = renderTex;

            Vector3 cameraPosition = new Vector3(0.0f, 1.6f, 1.0f);
            cameraPosition.y = GetCharaViewerInfo().ModelController.GetHeight() - 0.2f;
            cameraObject.transform.position = cameraPosition;
            cameraObject.transform.rotation = Quaternion.Euler(0.0f, 180.0f, 0.0f);

            camera.Render();

            resultTex = new Texture2D(renderTex.width, renderTex.height);
            resultTex.ReadPixels(new Rect(0, 0, renderTex.width, renderTex.height), 0, 0);
            resultTex.Apply(false);
            System.IO.File.WriteAllBytes(facialExportPath + "_Front.png", resultTex.EncodeToPNG());

            RenderTexture.active = null;
            renderTex.Release();
            Destroy(cameraObject);
        }

#if UNITY_EDITOR
        /// <summary>
        /// CySpringのスケール計算の仕方を変更する
        /// </summary>
        public void SetCySpringScaleType(CySpringScaleCalcSetting scaleCalcSetting)
        {
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller == null)
            {
                return;
            }

            if (scaleCalcSetting == CySpringScaleCalcSetting.UseSettings)
            {
                controller.CySpringController.OverrideUserBustCorrectScaleForCharaViewerWithClothSetting();
            }
            else
            {
                controller.CySpringController.SetUseBustCorrectScaleForCharaViewer(scaleCalcSetting == CySpringScaleCalcSetting.On);
            }
            
            controller.ReconstructCySpring(controller.ClothCategory);
            SelectCharacter_Hierarchy(); // CharaViewerで見てるキャラをアクティブにしないとCySpringToolで見てるキャラとずれる可能性がある
        }
#endif

        public void EnableCySpring(bool enable)
        {
            CySpringSnapshotData[] snapShot;
            GetCharaViewerInfo().IsEnableCySpring = enable;
            snapShot = GetCharaViewerInfo().CySpringSnapeShot;

            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                if (enable)
                {
                    controller.ResumeCySpring();
                }
                else
                {
                    controller.PauseCySpring();
                    if (snapShot != null)
                    {
                        controller.ApplySnapshot(snapShot);
                    }
                    controller.ResetCySpringNativeCloth();
                    controller.ApplyCySpringTransform();
                }
            }
            else
            {
                MiniModelController miniCtrl = GetCharaViewerInfo().MiniModelController;
                if (miniCtrl != null)
                {
                    if (enable)
                    {
                        miniCtrl.ResumeCySpringForDebug();
                    }
                    else
                    {
                        miniCtrl.PauseCySpringForDebug();
                        if (snapShot != null)
                        {
                            miniCtrl.ApplySnapshot(snapShot);
                        }

                        miniCtrl.ResetCySpringNativeCloth();
                        miniCtrl.ApplyCySpringTransform();
                    }
                }
            }
        }

        public void EnableCySpringWindMove(bool enable)
        {
            Vector3 windDir = Math.VECTOR3_FORWARD;
            GetCharaViewerInfo().IsWindMove = enable;
            windDir = GetCharaViewerInfo().WindDir;
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.SetEnableCySpringDummyWind(!enable, windDir);
                for (var i = 0; i < controller.PropCount; i++)
                {
                    controller.PropCtrlArray[i].SetEnableCySpringDummyWind(!enable, windDir);
                }
            }
        }

        public void SetCySpringWindDir(Vector3 windDir)
        {
            bool isEnableDummyWind = false;
            GetCharaViewerInfo().WindDir = windDir;
            isEnableDummyWind = !GetCharaViewerInfo().IsWindMove;
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.SetEnableCySpringDummyWind(isEnableDummyWind, windDir);
                for (var i = 0; i < controller.PropCount; i++)
                {
                    controller.PropCtrlArray[i].SetEnableCySpringDummyWind(isEnableDummyWind, windDir);
                }
            }
        }

        public void EnableCySpringWind(bool enable)
        {
            Vector3 windDir = Math.VECTOR3_FORWARD;
            bool isEnableDummyWind = false;
            windDir = GetCharaViewerInfo().WindDir;
            isEnableDummyWind = !GetCharaViewerInfo().IsWindMove;

            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.SetEnableCySpringWind(enable);
                controller.SetEnableCySpringDummyWind(isEnableDummyWind, windDir);
                for (var i = 0; i < controller.PropCount; i++)
                {
                    controller.PropCtrlArray[i].SetEnableCySpringWind(enable);
                    controller.PropCtrlArray[i].SetEnableCySpringDummyWind(isEnableDummyWind, windDir);
                }
            }
        }

        public void SetCySpringWindPower(float value)
        {
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.SetWindPowerRate(value);
                for (var i = 0; i < controller.PropCount; i++)
                {
                    controller.PropCtrlArray[i].SetWindPowerRate(value);
                }
            }
        }

        /// <summary>
        /// 疑似風の再生
        /// </summary>
        /// <param name="param"></param>
        public void StartFakeWind(CySpringWindParam param)
        {
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.BeginWind(param);
            }
        }

        /// <summary>
        /// 疑似風の停止
        /// </summary>
        public void EndFakeWind()
        {
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.EndWind();
            }
        }

        /// <summary>
        /// 揺れ物をリセットしウォームアップする。
        /// </summary>
        public void ResetCySpring()
        {
            ModelController controller = GetCharaViewerInfo().ModelController;
            if (controller != null)
            {
                controller.ResetCyspring();
                controller.ReserveWarmingUpCySpring();
            }
            else
            {
                MiniModelController miniCtrl = GetCharaViewerInfo().MiniModelController;
                if (miniCtrl != null)
                {
                    miniCtrl.ResetCySpring();
                    miniCtrl.ReserveWarmingUpCySpring();
                }
            }
        }


        private float _moveInterval = 0.0f;
        private void UpdateWind()
        {
            // 風なびきを発生させるために微妙に移動させる
            _moveInterval += Time.deltaTime;
            bool reset = false;
            if (_moveInterval > 5.0f)
            {
                reset = true;
            }
            for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
            {
                CharaViewerInfo info = GetCharaViewerInfo(i);
                ModelController controller = info.ModelController;
                if (controller != null)
                {
                    bool isMove = info.IsEnableCySpring && controller.HasWind(CySpringController.Parts.Head) && info.IsWindMove;

                    var trs = controller.transform;
                    if (isMove)
                    {
                        if (reset)
                        {
                            info.WindPosition = Math.VECTOR3_ZERO;
                            trs.position = Math.VECTOR3_ZERO;
                        }
                        else
                        {
                            info.WindPosition += info.WindDir.normalized * 0.00005f;
                            trs.position = trs.position + info.WindPosition;
                        }
                    }
                }
            }
            if (_moveInterval > 5.0f)
                _moveInterval = 0.0f;
        }

        public void LateUpdateScene()
        {
            LateUpdateLiveAnimation();
        }

        /// <summary>
        /// Update
        /// </summary>
        public void UpdateScene()
        {
            if (IsTerminal)
            {
                //ターミナル専用処理
                if (_terminalRequestBuildModel)
                {
                    _terminalBuildModel = true;
                    SetCharacterType(CharacterType.Story + _terminalCharacterTypeIndex);
                    SetBackDancer(_terminalBackDancerId);

                    Debug.Log("RequestBuildModel: charaTypeIndex=" + _terminalCharacterTypeIndex + " backDancerTextureId=" + _terminalBackDancerId);

                    if (_terminalMobBuildModel)
                    {
                        SetDressId(GetDressID(_terminalModelIndex, _terminalDressIndex, _terminalMobBuildModel));
                        if (_isTerminalMobUseMobId)
                        {
                            BuildMobModel(_terminalMobId);
                        }
                        else
                        {
                            BuildMobModel(_terminalMobBuildInfo);
                            _terminalMobBuildInfo.DebugDump();
                        }
                    }
                    else if (_isTerminalAudienceBuildModel)
                    {
                        SetDressId(GetDressID(_terminalModelIndex, _terminalDressIndex, _isTerminalAudienceBuildModel));
                        if (_isTerminalAudienceUseMobId)
                        {
                            BuildAudienceModel(_terminalAudienceId);
                        }
                        else
                        {
                            BuildAudienceModel(_terminalAudienceBuildInfo);
                        }
                    }
                    else
                    {
                        SetOnModelLoadAction(() =>
                        {
                            SetCharacterPosition(_currentCharaIndex, DEFAULT_POSITION_ARRAY[_currentCharaIndex]);
                        }, null);
                        SetMobId(ModelLoader.MOB_ID_NULL);
                        SetAudienceId(ModelLoader.AUDIENCE_ID_NULL);
                        SetBuildCharaId(GetCharaId(_terminalModelIndex));
                        var dressId = GetDressID(_terminalModelIndex, _terminalDressIndex, _terminalMobBuildModel);
                        SetDressId(dressId);
                        SetCharaDressColorSet(dressId);
                        SetCardIdToPersonality(GetCardID(_terminalModelIndex, _terminalHeadIndex));
                        BuildModel();
                    }
                    _terminalRequestBuildModel = false;
                }
                else if (_terminalRequestPlayAnimation)
                {
                    if (GetCharaViewerInfo().CharacterType == CharacterType.Story)
                    {
                        switch (_terminalIKAnimationType)
                        {
                            case TerminalIKMode.IK:
                                PlayIKAnimation(_terminalAnimationIndex, false);
                                break;
                            case TerminalIKMode.IKAdd:
                                PlayIKAnimation(_terminalAnimationIndex, true);
                                break;
                            default:
                                PlayAnimation(_terminalAnimationIndex);
                                break;
                        }
                    }
                    else
                    {
                        PlayAnimation(_terminalAnimationIndex);
                    }
                    _terminalRequestPlayAnimation = false;
                }

                _facialRaceCheck.Update();

                switch (_terminateMode)
                {
                    case TerminateMode.MiniMobModel:
                        UpdateTerminalMiniMob();
                        break;
                }
            }
            else
            {
                _viewerCameraController.ControllLockFlags &= ~(int)CharaViewer3DCameraController.ControllType.All;

#if UNITY_EDITOR
                if (CharaViewerWindow.HasInstance)
                {
                    CharaViewerWindow.Instance.OnUpdate();
                }
#endif
            }

            UpdateLightDirection();
            UpdateLiveAnimation();

            //ミニモデルの更新
            for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
            {
                var info = GetCharaViewerInfo(i);
                var miniModelController = info.MiniModelController;
                if (miniModelController == null)
                {
                    continue;
                }
                //モーション時間の設定。
                if (info.IsAnimatoinPlaying)
                {
                    info.AnimationCurrentTime = miniModelController.GetCurrentMotionNormalizedTime();
#if UNITY_EDITOR
                    if (CharaViewerWindow.Instance != null)
                    {
                        // スライダー位置更新のためRepaint
                        CharaViewerWindow.Instance.Repaint();
                    }
#endif // UNITY_EDITOR
                    //モーションスピードの設定
                    miniModelController.SetAnimatorSpeedWithFacial(info.MiniMotionSpeed);
                }
                else
                {
                    //モーションスピードの設定
                    miniModelController.SetAnimatorSpeedWithFacial(0f);
                }
            }

            for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
            {
                var info = GetCharaViewerInfo(i);
                ModelController modelController = info.ModelController;
                if (modelController == null)
                    continue;

                if (modelController is RaceModelController)
                {
                    var raceModelController = modelController as RaceModelController;

                    if (info.SerialRaceMotionArray.Count > 0 && !info.IsAnimationStartFrame)
                    {
                        if (info.IsRaceAnimationNextState)
                        {
                            info.IsRaceAnimationNextState = false;
                            raceModelController.SetMotion(info.SerialRaceMotionArray[0], info.RunMotionSpeed, 0.0f);
                            info.SerialRaceMotionArray.Remove(info.SerialRaceMotionArray[0]);
                        }
                    }

                    if (raceModelController.TryGetModelComponent<DirtController>(out var dirtController))
                    {
                        dirtController.SetDirtRate(info.DirtRateArray);
                    }

                    if (info.IsRaceAnimationPlay)
                    {
                        raceModelController.SetCameraDistanceBlendRate(info.CameraBlendRate, false);
                        raceModelController.SetRunMotionBlendRate(info.RunBlendRate);
                        raceModelController.SetAnimationSpeed(info.RunMotionSpeed);
                    }

                    raceModelController.SetShaderFromGraphisSettings();
                    raceModelController.UpdateMotion(Time.deltaTime * (info.IsRaceAnimationPlay ? 1.0f : 0.0f));
                }
                else if (modelController is CutInModelController)
                {
                    //TrainingModelControllerもここに該当する
                    var cutInModel = modelController as CutInModelController;
                    if (cutInModel.TryGetModelComponent<DirtController>(out var dirtController))
                    {
                        dirtController.SetDirtRate(info.DirtRateArray);
                    }
                    if (cutInModel.TryGetModelComponent<CharaCutInAnimator>(out var animator))
                    {
                        animator.Speed = info.RunMotionSpeed;
                    }
                }
                else if (modelController is EventTimelineModelController eventTimelineModelController)
                {
                    info.UpdateStoryAnimation();
                    eventTimelineModelController.IsSingleMode = info.IsSingleMode;
                    eventTimelineModelController.FadeType = info.CharaAlpha < 1f ? StoryCharacterFade.FadeType.FadeIn : StoryCharacterFade.FadeType.None;
                    eventTimelineModelController.SetCharacterAlpha(info.CharaAlpha);
                }
                else
                {
                    modelController.SetAnimationSpeed(info.RunMotionSpeed);
                }

#if UNITY_EDITOR
                if (modelController.ShaderType == ModelLoader.ShaderType.MultiMapColor)
                {
                    switch (_multiTextureRGBType)
                    {
                        case 0: //RGB
                            modelController.SetShaderColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.white);
                            break;
                        case 1: //R
                            modelController.SetShaderColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.red);
                            break;
                        case 2: //G
                            modelController.SetShaderColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.green);
                            break;
                        case 3: //B
                            modelController.SetShaderColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.blue);
                            break;
                    }
                }
#endif

                if (_isUpdateModelGraphicSetting)
                {
                    modelController.UpdateGraphicSettingsForCharaViewer(_mainCamera, i + 1);
                }

                info.IsAnimationStartFrame = false;
            }

            TouchHandler.Instance.Update();

            // 風なびき確認用
            UpdateWind();
        }

        #region フェイシャル

        public void SetBlink(bool blink)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                    continue;

                charaViewerInfoArray[c].ModelController.SetEyeBlinkEnable(blink);
            }
        }

        /// <summary>
        /// 髪の毛表示切替
        /// </summary>
        /// <param name="isShow"></param>
        public void SetShowHair(bool isShow)
        {
            CharaPartsHolder partHolder = null;
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    partHolder = charaViewerInfoArray[c].ModelController.GetModelComponent<CharaPartsHolder>();
                }
                else if (charaViewerInfoArray[c].MiniModelController != null)
                {
                    partHolder = charaViewerInfoArray[c].MiniModelController.GetModelComponent<CharaPartsHolder>();
                }

                if (partHolder != null)
                {
                    partHolder.GetPartsData(out var partsData);
                    if (partsData.IsHair)
                        partsData.HairRenderer.enabled = isShow;
                }
            }
        }

        /// <summary>
        /// ランラム耳設定
        /// </summary>
        /// <param name="enable"></param>
        public void SetRandomEar(bool enable)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    if (charaViewerInfoArray[c].ModelController is SimpleModelController simpleModelController)
                    {
                        simpleModelController.SetEnableRandomEarMotionTime(enable);
                    }

                    if (charaViewerInfoArray[c].ModelController is EventTimelineModelController eventTimelineModelController)
                    {
                        eventTimelineModelController.SetEnableRandomMotion(enable);
                    }
                }
                else if (charaViewerInfoArray[c].MiniModelController != null)
                {
                    charaViewerInfoArray[c].MiniModelController.SetEnableRandomEarMotionTime(enable);
                }
            }
        }

        /// <summary>
        /// チーク設定
        /// </summary>
        /// <param name="val"></param>
        public void SetCheek(int index, float val)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                var modelController = charaViewerInfoArray[c].ModelController;
                if (modelController == null)
                    continue;

                if (modelController.IsOmitCheek)
                    continue;

                if (index <= 0)
                {
                    modelController.SetVisible_Cheek(false);
                }
                else
                {
                    modelController.SetVisible_Cheek(true, index - 1);
                    modelController.CheekCtrl.SetCheekRate(val);
                }
            }
        }

        public void SetFace(FaceType face, float value, FaceGroupSet faceGroup)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                    continue;

                charaViewerInfoArray[c].ModelController.SetFace(face, value, faceGroup);
            }
        }

        public void SetEar(EarType ear, float value)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    charaViewerInfoArray[c].ModelController.SetEar(ear, value);
                }
                else if (charaViewerInfoArray[c].MiniModelController != null)
                {
                    charaViewerInfoArray[c].MiniModelController.SetEnableFacialLocatorControll(false);
                    charaViewerInfoArray[c].MiniModelController.SetEar(ear, value);
                }
            }
        }

        public void SetEar(EarType ear, FaceGroupType earGroup, float value)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    charaViewerInfoArray[c].ModelController.SetEar(ear, earGroup, value);
                }
                else if (charaViewerInfoArray[c].MiniModelController != null)
                {
                    charaViewerInfoArray[c].MiniModelController.SetEnableFacialLocatorControll(false);
                    charaViewerInfoArray[c].MiniModelController.SetEar(ear, earGroup, value);
                }
            }
        }

        /// <summary>
        /// 顔のパーツごとの設定。
        /// </summary>
        public void SetFaceEyebrowType(FaceParts[] faceTypeList, FaceGroupType targetGroup)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    charaViewerInfoArray[c].ModelController.SetFaceEyebrowType(faceTypeList, targetGroup, 0.0f, false, false);
                }
            }
        }
        public void SetFaceEyebrowType(FaceType faceType, float weight, FaceGroupType targetGroup)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null && charaViewerInfoArray[c].ModelController.DrivenKeyComponent != null)
                {
                    charaViewerInfoArray[c].ModelController.DrivenKeyComponent.SetFaceEyebrowType(faceType, weight, targetGroup);
                }
            }
        }
        public void SetFaceEyeType(FaceParts[] faceTypeList, FaceGroupType targetGroup)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    charaViewerInfoArray[c].ModelController.SetFaceEyeType(faceTypeList, targetGroup, 0.0f, false, false);
                }
            }
        }

        /// <summary>
        /// モデルからFaceOverrideCtrlを取得
        /// </summary>
        public FaceOverrideController GetFaceOverrideCtrl()
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null &&
                    charaViewerInfoArray[c].ModelController.DrivenKeyComponent != null)
                {
                    return charaViewerInfoArray[c].ModelController.DrivenKeyComponent.FaceOverrideController;
                }
            }
            return null;
        }

        /// <summary>
        /// 表情の上書き機能が有効か
        /// </summary>
        public bool IsFaceOverrideEnabled()
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null &&
                    charaViewerInfoArray[c].ModelController.DrivenKeyComponent != null)
                {
                    return charaViewerInfoArray[c].ModelController.DrivenKeyComponent.IsFaceOverrideEnabled;
                }
            }
            return false;
        }

        /// <summary>
        /// ビューワー側から表情の上書き機能の有効設定
        /// </summary>
        public void SetFaceOverrideEnabled(bool overrideEnabled)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null &&
                    charaViewerInfoArray[c].ModelController.DrivenKeyComponent != null)
                {
                    charaViewerInfoArray[c].ModelController.DrivenKeyComponent.IsFaceOverrideEnabled = overrideEnabled;
                }
            }
        }

        public void SetFaceEyeType(FaceType faceType, float weight, FaceGroupType targetGroup)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null && charaViewerInfoArray[c].ModelController.DrivenKeyComponent != null)
                {
                    charaViewerInfoArray[c].ModelController.SetFaceGroupType(targetGroup, faceType);
                    charaViewerInfoArray[c].ModelController.DrivenKeyComponent.SetFaceEyeType(faceType, weight, targetGroup);
                }
            }
        }
        public void SetFaceMouthType(FaceParts[] faceTypeList, FaceGroupType targetGroup)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController != null)
                {
                    charaViewerInfoArray[c].ModelController.SetFaceMouthType(faceTypeList, true, faceTypeList, targetGroup, 0.0f, false, false);
                }
            }
        }


        public void PlayEyeHighlight(EyeHighlightController.HighlightAnimationId id, float inTime, float outTime)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].ModelController == null)
                    continue;

                //再生中の場合は停止にする
                if (charaViewerInfoArray[c].ModelController.IsPlayEyeHighlightAnimation(charaViewerInfoArray[c].EyeHighlightAnimationId))
                {
                    charaViewerInfoArray[c].ModelController.StopEyeHighlightAnimation();
                    charaViewerInfoArray[c].ModelController.ClearInterpolateOverwrite();
                    charaViewerInfoArray[c].EyeHighlightAnimationId = EyeHighlightController.HighlightAnimationId.NoAnim;

                    if (charaViewerInfoArray[c].EyeHighlightAnimationCoroutine != null)
                    {
                        StopCoroutine(charaViewerInfoArray[c].EyeHighlightAnimationCoroutine);
                        charaViewerInfoArray[c].EyeHighlightAnimationCoroutine = null;
                    }
                    continue;
                }

                if (id == EyeHighlightController.HighlightAnimationId.NoAnim)
                {
                    continue;
                }

                charaViewerInfoArray[c].EyeHighlightAnimationId = id;
                charaViewerInfoArray[c].ModelController.SetInterpolateOverwrite(inTime, outTime);
                charaViewerInfoArray[c].ModelController.PlayEyeHighlightAnimation(id);

                // 終了監視用のコルーチン起動
                charaViewerInfoArray[c].EyeHighlightAnimationCoroutine = StartCoroutine(WaitingHighlightAnimationFinish(charaViewerInfoArray[c]));
            }
        }

        /// <summary>
        /// ハイライトアニメーションの終了待ち
        /// </summary>
        private IEnumerator WaitingHighlightAnimationFinish(CharaViewerInfo charaViewerInfo)
        {
            while (true)
            {
                if (!GetCharaViewerInfo().ModelController.IsPlayEyeHighlightAnimation(charaViewerInfo.EyeHighlightAnimationId))
                {
                    // 再生終了している！
                    break;
                }
                // まだ再生中なので次のフレームへ
                yield return null;
            }

            charaViewerInfo.EyeHighlightAnimationId = EyeHighlightController.HighlightAnimationId.NoAnim;

            charaViewerInfo.EyeHighlightAnimationCoroutine = null;
        }

        public bool IsPlayEyeHighlightAnimation(CharaViewerInfo charaViewerInfo)
        {
            if (charaViewerInfo.ModelController == null)
                return false;

            if (charaViewerInfo.EyeHighlightAnimationId == EyeHighlightController.HighlightAnimationId.NoAnim)
                return false;

            return charaViewerInfo.ModelController.IsPlayEyeHighlightAnimation(charaViewerInfo.EyeHighlightAnimationId);
        }

        #endregion

        #region ミニフェイシャル
        public void SetMiniFacialEyebrow(int left, int right)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].MiniModelController == null)
                {
                    continue;
                }
                charaViewerInfoArray[c].MiniModelController.SetEnableFacialLocatorControll(false);
                charaViewerInfoArray[c].MiniModelController.FacialCtrl.SetEyebrow(left, right);
            }
        }
        public void SetMiniFacialEye(int left, int right)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].MiniModelController == null)
                {
                    continue;
                }
                charaViewerInfoArray[c].MiniModelController.SetEnableFacialLocatorControll(false);
                charaViewerInfoArray[c].MiniModelController.FacialCtrl.SetEye(left, right);
            }
        }
        public void SetMiniFacialMouth(int id)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].MiniModelController == null)
                {
                    continue;
                }
                charaViewerInfoArray[c].MiniModelController.SetEnableFacialLocatorControll(false);
                charaViewerInfoArray[c].MiniModelController.FacialCtrl.SetMouth(id);
            }
        }
        public void SetMiniCheek(int id)
        {
            CharaViewerInfo[] charaViewerInfoArray = GetCharaViewerInfoMulti();

            for (int c = 0; c < charaViewerInfoArray.Length; c++)
            {
                if (charaViewerInfoArray[c].MiniModelController == null)
                {
                    continue;
                }
                charaViewerInfoArray[c].MiniModelController.SetEnableFacialLocatorControll(false);
                if (id < 0)
                {
                    charaViewerInfoArray[c].MiniModelController.SetVisible_Cheek(false);
                }
                else
                {
                    charaViewerInfoArray[c].MiniModelController.SetVisible_Cheek(true, id);
                }
            }
        }
        #endregion // ミニフェイシャル

        #region キャプチャー
        public void StartScreenShot(float angleRange)
        {
            if (GetCharaViewerInfo().ModelController != null)
            {
                StartCoroutine(Capture(angleRange));
            }
            else if (GetCharaViewerInfo().MiniModelController != null)
            {
                StartCoroutine(CaptureMiniModel(angleRange));
            }
        }

        private IEnumerator Capture(float angle)
        {
            int captureNum = (int)(360.0f / angle);
            int charaId = GetCharaViewerInfo().ModelController.GetCharaID();
            int subid = GetCharaViewerInfo().ModelController.GetCharaID();
            int dressId = GetCharaViewerInfo().ModelController.GetDressId();
            var captureRoot = new GameObject("capture Root");
            captureRoot.transform.localPosition = Math.VECTOR3_ZERO;
            captureRoot.transform.localRotation = Math.QUATERNION_IDENTITY;

            var tempParent = new GameObject("capture Viewer");
            tempParent.transform.SetParent(captureRoot.transform);
            tempParent.transform.localPosition = _viewerCameraController.transform.localPosition;
            tempParent.transform.localRotation = _viewerCameraController.transform.localRotation;

            var captureCameraObj = new GameObject("capture camera");
            var captureCamera = captureCameraObj.AddComponent<Camera>();
            captureCamera.CopyFrom(_mainCamera);
            captureCamera.depth = _mainCamera.depth + 1;
            captureCamera.transform.SetParent(tempParent.transform, true);

            _captureCamera = captureCamera;

            yield return null;
            for (int i = 0; i < captureNum; i++)
            {
                float finalAngle = i * angle;
                captureRoot.transform.localRotation = Quaternion.Euler(0.0f, finalAngle, 0.0f);
                string fileName = string.Format("chr{0:D4}_{1:D2}_{2:D4}_{3:D3}_{4}.png", charaId, subid, dressId, (int)finalAngle, System.DateTime.Now.ToString("yyyy_MM_dd_hh_mm_ss"));

                // キャプチャ直前にライトの向きを確認する
                UpdateLightDirection();
                ScreenCapture.CaptureScreenshot(fileName);
                yield return null;
            }

            _captureCamera = null;

            Destroy(captureRoot);
        }

        private IEnumerator CaptureMiniModel(float angle)
        {
            int captureNum = (int)(360.0f / angle);
            int charaId = GetCharaViewerInfo().MiniModelController.GetCharaID();
            int subid = GetCharaViewerInfo().MiniModelController.GetCharaID();
            int dressId = GetCharaViewerInfo().MiniModelController.GetDressId();
            var captureRoot = new GameObject("capture Root");
            captureRoot.transform.localPosition = Math.VECTOR3_ZERO;
            captureRoot.transform.localRotation = Math.QUATERNION_IDENTITY;

            var tempParent = new GameObject("capture Viewer");
            tempParent.transform.SetParent(captureRoot.transform);
            tempParent.transform.localPosition = _viewerCameraController.transform.localPosition;
            tempParent.transform.localRotation = _viewerCameraController.transform.localRotation;

            var captureCameraObj = new GameObject("capture camera");
            var captureCamera = captureCameraObj.AddComponent<Camera>();
            captureCamera.CopyFrom(_mainCamera);
            captureCamera.depth = _mainCamera.depth + 1;
            captureCamera.transform.SetParent(tempParent.transform, true);

            _captureCamera = captureCamera;

            yield return null;
            for (int i = 0; i < captureNum; i++)
            {
                float finalAngle = i * angle;
                captureRoot.transform.localRotation = Quaternion.Euler(0.0f, finalAngle, 0.0f);
                string fileName = string.Format("mini_chr{0:D4}_{1:D2}_{2:D4}_{3:D3}_{4}.png", charaId, subid, dressId, (int)finalAngle, System.DateTime.Now.ToString("yyyy_MM_dd_hh_mm_ss"));

                // キャプチャ直前にライトの向きを確認する
                UpdateLightDirection();
                ScreenCapture.CaptureScreenshot(fileName);
                yield return null;
            }

            _captureCamera = null;

            Destroy(captureRoot);
        }
        #endregion キャプチャー

        #region 座標系

        public void SetCharacterPosition(Vector3 position)
        {
            SetCharacterPosition(_currentCharaIndex, position);
        }

        public void SetCharacterPosition(int index, Vector3 position)
        {
            CharaViewerInfo info = GetCharaViewerInfo(index);
            if (info.RootObj != null)
                info.RootObj.transform.localPosition = position;
        }

        /// <summary>
        /// 選択中のキャラクターのRotationを設定する
        /// </summary>
        /// <param name="rotation"></param>
        public void SetCharacterRotation(Vector3 rotation)
        {
            SetCharacterRotation(_currentCharaIndex, rotation);
        }

        /// <summary>
        /// キャラクターのRotationを設定する
        /// </summary>
        /// <param name="index"></param>
        /// <param name="rotation"></param>
        public void SetCharacterRotation(int index, Vector3 rotation)
        {
            var info = GetCharaViewerInfo(index);
            if (info.RootObj != null)
            {
                info.RootObj.transform.localRotation = Quaternion.Euler(rotation);
            }
        }

        /// <summary>
        /// 選択中のキャラクターの向きをリセットする
        /// </summary>
        public void ResetCharacterRotation()
        {
            ResetCharacterRotation(_currentCharaIndex);
        }

        /// <summary>
        /// キャラクターの向きをリセットする
        /// </summary>
        /// <param name="index"></param>
        public void ResetCharacterRotation(int index)
        {
            var info = GetCharaViewerInfo(index);
            if (info.RootObj != null)
            {
                info.RootObj.transform.localRotation = Quaternion.Euler(Math.VECTOR3_ZERO);
            }
        }

        public void SetAscPosition()
        {
            bool isRevert = CheckPositionChange(PositionChangeTypeX.Asc);

            for (int i = 0; i < CharaViewer.CHARACTER_MAX_COUNT; i++)
            {
                var info = GetCharaViewerInfo(i);
                Vector3 myPosition;
                if (info.ModelController != null)
                {
                    myPosition = info.ModelController.GetPosition();
                }
                else if (info.MiniModelController != null)
                {
                    myPosition = info.MiniModelController.CacheTransform.position;
                }
                else
                {
                    continue;
                }

                if (isRevert == false)
                {
                    info.PrevPosition.x = myPosition.x;

                    myPosition.x = -0.4f * i;
                }
                else
                {
                    myPosition = info.PrevPosition;
                }

                SetCharacterPosition(i, myPosition);

            }
        }

        private bool CheckPositionChange(PositionChangeTypeX type)
        {
            bool isRevert = false;
            if (_positionChangeX != PositionChangeTypeX.None)
            {
                if (_positionChangeX == type)
                {
                    // すでに変更済みなので戻す処理
                    isRevert = true;
                }
            }

            if (isRevert)
            {
                _positionChangeX = PositionChangeTypeX.None;
            }
            else
            {
                _positionChangeX = type;
            }

            return isRevert;
        }

        public void SetZeroY()
        {
            bool isRevert = CheckPositionChange(PositionChangeTypeY.ZeroY);

            for (int i = 1; i < CharaViewer.CHARACTER_MAX_COUNT; i++)
            {
                var info = GetCharaViewerInfo(i);
                Vector3 myPosition;
                // TODO:座標取得でここの分岐がいくつか見られるのでまとめられるならまとめる
                // CharaViewerInfoにGetPosition()関数を作るのが安いが今後通常キャラとミニキャラを同一視して扱いたい場合は増えると思う
                // その場合ModelControllerとMiniModelController両方を持ったラッパークラスがあると便利。
                // CharaIconCaptureCharacterが近いことをやっているがアイコンキャプチャの機能に特化しているので汎用的なものを作るかCharaViewerで特化させるかは要判断。
                if (info.ModelController != null)
                {
                    myPosition = info.ModelController.GetPosition();
                }
                else if (info.MiniModelController != null)
                {
                    myPosition = info.MiniModelController.CacheTransform.position;
                }
                else
                {
                    continue;
                }

                if (isRevert == false)
                {
                    info.PrevPosition.y = myPosition.y;
                    myPosition.y = 0;
                }
                else
                {
                    myPosition.y = info.PrevPosition.y;
                }
                SetCharacterPosition(i, myPosition);

            }
        }

        public void SetHeadAlign()
        {
            bool isRevert = CheckPositionChange(PositionChangeTypeY.HeadAlign);

            Vector3 headPosition1 = Math.VECTOR3_ZERO;
            Vector3 headPosition2 = Math.VECTOR3_ZERO;

            if (GetCharaViewerInfo(0).ModelController != null)
            {
                headPosition1 = GetCharaViewerInfo(0).ModelController.GetHeadPosition();
            }
            else if (GetCharaViewerInfo(0).MiniModelController != null)
            {
                headPosition1 = GetCharaViewerInfo(0).MiniModelController.FindTransform(CharaNodeName.Head).position;
            }
            else
            {
                return;
            }

            for (int i = 1; i < CharaViewer.CHARACTER_MAX_COUNT; i++)
            {
                var info = GetCharaViewerInfo(i);
                Vector3 myPosition;
                Vector3 myHeadPosition;
                if (info.ModelController != null)
                {
                    myPosition = info.ModelController.GetPosition();
                    myHeadPosition = info.ModelController.GetHeadPosition();
                }
                else if (info.MiniModelController != null)
                {
                    myPosition = info.MiniModelController.CacheTransform.position;
                    myHeadPosition = info.MiniModelController.FindTransform(CharaNodeName.Head).position;
                }
                else
                {
                    continue;
                }

                if (isRevert == false)
                {
                    info.PrevPosition.y = myPosition.y;
                    headPosition2 = myHeadPosition - myPosition;

                    float subY = headPosition1.y - headPosition2.y;
                    myPosition.y = subY;
                }
                else
                {
                    myPosition.y = info.PrevPosition.y;
                }
                SetCharacterPosition(i, myPosition);

            }
        }

        private bool CheckPositionChange(PositionChangeTypeY type)
        {
            bool isRevert = false;
            if (_positionChangeY != PositionChangeTypeY.None)
            {
                if (_positionChangeY == type)
                {
                    // すでに変更済みなので戻す処理
                    isRevert = true;
                }
            }

            if (isRevert)
            {
                _positionChangeY = PositionChangeTypeY.None;
            }
            else
            {
                _positionChangeY = type;
            }

            return isRevert;
        }

        public void SetFaceToFace()
        {
            const float SUB = 0.2f;
            float subTotal = 0.0f;

            bool isRevert = CheckPositionChange(PositionChangeTypeX.FaceToFace);
            Vector3 defaultPosition;
            if (GetCharaViewerInfo(0).ModelController != null)
            {
                defaultPosition = GetCharaViewerInfo(0).ModelController.GetPosition();
            }
            else if (GetCharaViewerInfo(0).MiniModelController != null)
            {
                defaultPosition = GetCharaViewerInfo(0).MiniModelController.CacheTransform.position;
            }
            else
            {
                return;
            }

            for (int i = 1; i < CHARACTER_MAX_COUNT; i++)
            {
                var info = GetCharaViewerInfo(i);
                Vector3 myPosition;
                if (info.ModelController != null)
                {
                    myPosition = info.ModelController.GetPosition();
                }
                else if (info.MiniModelController != null)
                {
                    myPosition = info.MiniModelController.CacheTransform.position;
                }
                else
                {
                    continue;
                }

                if (isRevert == false)
                {
                    info.PrevPosition.x = myPosition.x;
                    Vector3 position = defaultPosition;

                    subTotal -= SUB;
                    position.x += subTotal;
                    myPosition.x = position.x;
                }
                else
                {
                    myPosition.x = info.PrevPosition.x;
                }
                SetCharacterPosition(i, myPosition);
            }
        }

        /// <summary>
        /// キャラのスケール適用
        /// </summary>
        public void SetCharacterScale(float scale)
        {
            CharaViewerInfo info = GetCharaViewerInfo(_currentCharaIndex);
            if (info.ModelController != null)
            {
                info.ModelController.SetCySpringUpdateScale(true);
                info.ModelController.SetTotalScale(scale);

                // 目の可動域もスケールに合わせて拡縮させる。
                info.ResetEyeTraceScale();
            }
            // 2023/07/26 ミニキャラはスケール対応がまだない
        }

        /// <summary>
        /// 身長補正後のキャラのスケールの取得
        /// </summary>
        public float GetCharacterScale()
        {
            CharaViewerInfo info = GetCharaViewerInfo(_currentCharaIndex);
            if (info.ModelController != null)
            {
                return info.ModelController.GetTotalScale();
            }
            return 1.0f;
        }

        #endregion

        private void OnGUI()
        {
            // ターミナルかつモーションチェックModeでなければMenu表示
            if (IsTerminal && !IsMotionCheckMode)
            {
                OnGUI_Terminal();
                return;
            }

#if UNITY_EDITOR
            if (Event.current.isKey && Event.current.type == EventType.KeyDown)
            {
                if (Event.current.keyCode == KeyCode.O)
                {
                    //アウトラインシェーダを切り替える
                    var viewerWindow = CharaViewerWindow.Instance;
                    if (viewerWindow != null)
                    {
                        viewerWindow.SetOutline(!viewerWindow.IsOutline());
                    }
                    Event.current.Use();
                }
            }

            if (_multiTextureIndex < _charaTexture.Count && _multiTextureMode != 0)
            {
                var t = _charaTexture[_multiTextureIndex];
                Rect drawInfo = new Rect(80, 460, 128, 128);//縮小
                if (_multiTextureMode == 2)
                    drawInfo = new Rect(80, 0, t.width, t.height);//ピクセルパーフェクト

                switch (_multiTextureRGBType)
                {
                    case 0: //RGB
                        _multiTextureMaterial.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.white);
                        break;
                    case 1: //R
                        _multiTextureMaterial.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.red);
                        break;
                    case 2: //G
                        _multiTextureMaterial.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.green);
                        break;
                    case 3: //B
                        _multiTextureMaterial.SetColor(ShaderManager.GetPropertyId(ShaderManager.PropertyId._ClipColor), Color.blue);
                        break;
                }

                _multiTextureMaterial.SetTexture(ShaderManager.GetPropertyId(ShaderManager.PropertyId._TripleMaskMap), t);
                Graphics.DrawTexture(drawInfo, t, _multiTextureMaterial);
            }
#endif
        }

        #region 小物

        private class PropInfo
        {
            public string CurrentPropPath = "";
            public GameObject PropObject = null;
            public CharaPropController PropCtrl = null;
            public AnimatorOverrideController PropAnimator = null;
            public bool IsCameraTarget = false;
        }
        private List<PropInfo> _propList = new List<PropInfo>();
        public int PropCount => _propList.Count;
        private List<Vector3> _pointList = new List<Vector3>();
        /// <summary>
        /// propIndexに対応するPropInfoを取得する。なかったら作成して返す。
        /// </summary>
        private PropInfo GetCreatePropInfo(int propIndex, bool isCameraTarget = false)
        {
            if (propIndex < _propList.Count)
            {
                return _propList[propIndex];
            }
            // 足りない分は追加してから返す。
            for (int index = 0, count = propIndex - _propList.Count + 1; index < count; ++index)
            {
                _propList.Add(new PropInfo());
            }
            _propList[propIndex].IsCameraTarget = isCameraTarget;
            return _propList[propIndex];
        }
        /// <summary>
        /// カメラの注視対象になっているオブジェクトを取得する
        /// </summary>
        private PropInfo GetCameraTargetPropInfo()
        {
            foreach (var propInfo in _propList)
            {
                if (propInfo.IsCameraTarget)
                {
                    return propInfo;
                }
            }
            return null;
        }

        //小物生成
        public void CreateProp(int propIndex, string propResourcePath, bool isCameraTarget = false)
        {
            var propInfo = GetCreatePropInfo(propIndex, isCameraTarget);
            //同じものなら作らない
            if (propInfo.CurrentPropPath == propResourcePath && propInfo.PropObject != null)
            {
                return;
            }

            // モデル作り直しになる可能性あるのでカメラの追従をやめる
            FollowChara(CameraFollowTarget.None);

            DestoryPropInternal(propInfo);

            //小物生成
            var obj = ResourceManager.LoadOnScene<GameObject>(propResourcePath);
            if (obj == null)
            {
                Debug.LogWarning("小物の配置に失敗しました。リソース「" + propResourcePath + "」が見つかりませんでした。");
                return;
            }
            propInfo.PropObject = Instantiate(obj);
            propInfo.CurrentPropPath = propResourcePath;

            // ひとまずPositionの下に付けてスケールを適応させる。
            AttachProp(propIndex, CharaNodeName.Position);

            propInfo.PropCtrl = propInfo.PropObject.AddComponent<CharaPropController>();
            string fileName = Path.GetFileNameWithoutExtension(propInfo.CurrentPropPath);
            CharaPropController.CreateContext context = new CharaPropController.CreateContext();
            context._propId = CharaPropController.GetPropIdFromFileName(fileName);
            context._controller = ResourceManager.LoadOnScene<RuntimeAnimatorController>("Prop/charaviewer_prop_animator");
            if (GetCharaViewerInfo().ModelController != null)
            {
                context._charaId = GetCharaViewerInfo().ModelController.GetCharaID();
                context.LoadHash = GetCharaViewerInfo().ModelController.GetBuildInfo().LoadHashKey;
                context.OtherCharaNodeTransformFindAction = GetCharaViewerInfo().ModelController.TryFindTransform;
                propInfo.PropCtrl.Create(ref context);
                GetCharaViewerInfo().ModelController.SetPropCtrl(propInfo.PropCtrl);
            }
            else if (GetCharaViewerInfo().MiniModelController != null)
            {
                context._charaId = GetCharaViewerInfo().MiniModelController.GetBuildInfo().CharaId;
                context.LoadHash = GetCharaViewerInfo().MiniModelController.GetBuildInfo().LoadHashKey;
                context.OtherCharaNodeTransformFindAction = GetCharaViewerInfo().MiniModelController.TryFindTransform;
                propInfo.PropCtrl.Create(ref context);
                GetCharaViewerInfo().MiniModelController.SetPropCtrl(propInfo.PropCtrl);
            }
            propInfo.PropAnimator = propInfo.PropCtrl.AnimatorOverride;

            FollowChara(CameraFollowChara);
        }
        /// <summary>
        /// 小物を破棄。
        /// </summary>
        public void DeleteProp(int propIndex)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            _propList.RemoveAt(propIndex);
            if (propInfo.PropObject == null)
            {
                return;
            }
            FollowChara(CameraFollowTarget.None);
            DestoryPropInternal(propInfo);
            FollowChara(CameraFollowChara);
        }
        private void DestoryPropInternal(PropInfo propInfo)
        {
            var ctrl = GetCharaViewerInfo().GetModelControllerBehaviour();
            if (ctrl != null)
            {
                ctrl.RemovePropCtrl(propInfo.PropCtrl);
            }
            Destroy(propInfo.PropObject);
        }

        //キャラに小物アタッチ
        public void AttachProp(int propIndex, string attachBoneName, bool isCameraTarget = false)
        {
            var propInfo = GetCreatePropInfo(propIndex, isCameraTarget);
            if (propInfo.PropObject == null)
                return;
            ModelControllerBehaviour ctrl = GetCharaViewerInfo().GetModelControllerBehaviour();
            if (ctrl == null)
                return;
            //ボーンにつける
            var attachPos = ctrl.FindTransform(attachBoneName);
            if (attachPos != null)
            {
                propInfo.PropObject.transform.SetParent(attachPos, false);
            }
            propInfo.PropObject.transform.localPosition = Math.VECTOR3_ZERO;
            propInfo.PropObject.transform.localRotation = Math.QUATERNION_IDENTITY;

            if (propInfo.PropCtrl)
            {
                propInfo.PropCtrl.ClearAttachCheckFlg();
            }
        }

        //小物アニメ再生
        public void PlayPropAnim(int propIndex, string animPath)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            if (propInfo.PropObject == null)
                return;

            var clip = ResourceManager.LoadOnScene<AnimationClip>(animPath);
            if (clip == null)
                return;

            if (propInfo.PropAnimator == null)
            {
                var baseController = ResourceManager.LoadOnScene<RuntimeAnimatorController>("Prop/charaviewer_prop_animator");
                propInfo.PropAnimator = new AnimatorOverrideController()
                {
                    runtimeAnimatorController = baseController
                };

#if UNITY_EDITOR || CYG_DEBUG
                propInfo.PropAnimator.name = animPath;
#endif
            }

            var animator = propInfo.PropObject.GetComponent<Animator>();
            if (animator.runtimeAnimatorController == null)
            {
                animator.runtimeAnimatorController = propInfo.PropAnimator;
            }

            //１回終わらせてから再生
            animator.Play("end");

            this.WaitForEndFrame(() =>
            {
                var overrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                propInfo.PropAnimator.GetOverrides(overrides);  //テスト再生用なのでクリップは1個しかない
                propInfo.PropAnimator[overrides[0].Key] = clip;

                animator.Play("play");
            });
        }

        public void SetVisibleProp(int propIndex, bool visible)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            if (propInfo.PropCtrl)
            {
                propInfo.PropCtrl.SetVisible(visible);
            }
        }
        public void SetVisiblePropAll(bool visible)
        {
            foreach (var propInfo in _propList)
            {
                if (propInfo.PropCtrl)
                {
                    propInfo.PropCtrl.SetVisible(visible);
                }
            }
        }

        public void SetCameraTargetProp(int propIndex, bool isTarget)
        {
            for (int index = 0, count = _propList.Count; index < count; ++index)
            {
                var propInfo = _propList[index];
                propInfo.IsCameraTarget = false;
                if (index == propIndex)
                {
                    propInfo.IsCameraTarget = isTarget;
                }
            }
            FollowChara(CameraFollowChara);
        }

        public void SetPropLocalOffset(int propIndex, Vector3 localOffset)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            if (propInfo.PropObject == null)
                return;
            propInfo.PropObject.transform.localPosition = localOffset;
        }

        public void SetPropLocalRotation(int propIndex, Vector3 localOffset)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            if (propInfo.PropObject == null)
                return;
            propInfo.PropObject.transform.localEulerAngles = localOffset;
        }

        public void SetPropLocalScale(int propIndex, Vector3 localOffset)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            if (propInfo.PropObject == null)
                return;
            propInfo.PropObject.transform.localScale = localOffset;
        }

        public void SetOverridePropPriority(int propIndex,bool isOverride)
        {
            var propInfo = GetCreatePropInfo(propIndex);
            if (propInfo.PropCtrl == null)
                return;
            propInfo.PropCtrl.IsOverridePriority = isOverride;
        }

        #endregion

        #region アウトライン

        /// <summary>
        /// アウトラインパラメータのリセット
        /// </summary>
        private void ResetOutlineParameter(ModelController modelController)
        {
            modelController.SetOutlineParameter(ModelController.DEFAULT_OUTLINE_WIDTH, StaticVariableDefine.CG3D.ModelController.DEFAULT_OUTLINE_COLOR);
            modelController.UpdateMaterialPropertyBlockOutline();
        }

        #endregion アウトライン

        #region 端末用のGUI

        private enum TerminateMode
        {
            Top,
            Slot,
            Model,
            Animation,
            Facial,
            Cloth,
            MobModel,
            SimpleModel,
            CharacterType,
            AudienceModel,
            Prop,
            MiniMobModel,
        }

        public enum TerminalIKMode
        {
            Normal = 0,
            IK,
            IKAdd,
        }

        private TerminateMode _terminateMode = TerminateMode.Top;
        private int _terminateSubMode = 0;
        private bool _terminalRequestBuildModel = false;
        private bool _terminalMobBuildModel = false;
        private CharaViewerParamData _terminalViewerParamData;

        private string[] _terminalCharaName = null;
        private string[] _terminalDressNameArray = null;
        private string[] _terminalHeadName = null;
        private string[] _terminalCharacterTypeNameArray = null;

        private bool _terminalBuildModel = false;

        public bool IsViewActive = true;

        #region スロット
        private string[] _terminalSlotNameArray = new string[CHARACTER_MAX_COUNT];
        public string[] TerminalSlotNameArray => _terminalSlotNameArray;

        private Vector2 _terminalSlotScrollView = Math.VECTOR2_ZERO;
        #endregion スロット

        #region キャラクター種類
        private int _terminalCharacterTypeIndex = 0;

        private Vector2 _terminalCharaTypeScrollView = Vector2.zero;
        #endregion

        #region モデル番号
        private int _terminalModelIndex = 0;
        public int TerminalModelIndex => _terminalModelIndex;

        private Vector2 _terminalCharaScrollView = Vector2.zero;
        #endregion

        #region 衣装
        private int _terminalDressIndex = 0;
        public int TerminalDressIndex => _terminalDressIndex;
        private Vector2 _terminalDressScrollView = Math.VECTOR2_ZERO;
        
        private int _terminalBackDancerId = 0;
        public int TerminalBackDancerId => _terminalBackDancerId;

        #endregion

        #region キャラカラーセット
        private int _terminalCharacterColorSetIndex = 0;
        private Vector2 _terminalCharaColorSetScrollView = Vector2.zero;
        private List<string> _colorSetIdStringList = null;
        #endregion

        #region 頭
        private int _terminalHeadIndex = 0;
        private Vector2 _terminalHeadScrollView = Math.VECTOR2_ZERO;
        #endregion

        #region アニメーション

        private string[] _terminalAnimationNameArray = null;
        public string[] TerminalAnimationNameArray => _terminalAnimationNameArray;
        private int _terminalAnimationIndex = 0;
        private bool _terminalRequestPlayAnimation = false;
        private Vector2 _terminalAnimationScrollView = Vector2.zero;
        private Vector2 _terminalAnimation2ScrollView = Math.VECTOR2_ZERO;
        private Vector2 _terminalAnimationOptionScrollView = Vector2.zero;
        private TerminalIKMode _terminalIKAnimationType = TerminalIKMode.Normal;
        private bool _terminalIKAnimationFilter = false;
        public bool TerminalIKAnimationFilter => _terminalIKAnimationFilter;
        private EventSwapMotionType _terminalSwapMotionType = EventSwapMotionType.None;
        private string[] _terminalCommandVariantArray = null;
        public string[] TerminalCommandVariantArray => _terminalCommandVariantArray;
        private MotionSe.MaterialType _terminalMotionSeMaterialId = MotionSe.MaterialType.Flooring;
        private string[] _terminalMotionSeMaterialIdStrArray = null;
        public string[] TerminalMotionSeMaterialIdStrArray => _terminalMotionSeMaterialIdStrArray;
        private bool _terminalMotionSeSettingOn = false;
        private Vector2 _terminalAnimationMotionSEScrollView = Vector2.zero;
        #endregion

        #region 項目固有

        private int _terminalLivePositionIndex = 0;

        private int _terminalTrainingNumber = 2;　// 人数
        private int _terminalTrainingCuttDiff = 1;　// 演出差分
        private int _terminalTrainingType = 1;
        private int _terminalTrainingFolderNo;
        private int _terminalTrainingBigCategoryNo = 1;
        private int _terminalTrainingCategoryNo = 1;
        private int _terminalTrainingSubCategoryNo = 1;
        private int _terminalTrainingLevelNo = 1;
        private int _terminalTrainingMotionNo = 1;
        private int _terminalTrainingMotionDiffNo = 0;
        private int _terminalTrainingSceneCutNo = 2;    //カット番号



        private bool _terminalIsWet = false;
        public bool TerminalIsWet => _terminalIsWet;
        private bool _terminalIsDirt = false;
        public bool TerminalIsDirt => _terminalIsDirt;
        private bool _terminalIsTear = false;
        public bool TerminalIsTear => _terminalIsTear;

        private float[] _terminalDirtEnergy = new float[3];
        public float[] TerminalDirtEnergy => _terminalDirtEnergy;

        #endregion

        #region CySpring

        private bool _terminalCySpringEnable = false;
        public bool TerminalCySpringEnable => _terminalCySpringEnable;
        private bool _terminalCySpringWind = false;
        public bool TerminalCySpringWind => _terminalCySpringWind;
        private float _terminalCySpringWindPower = 0.5f;
        public float TerminalCySpringWindPower => _terminalCySpringWindPower;

        #endregion

        #region Facial

        private bool _terminalDrivenKeyFacialSelectMode = false;
        private bool _terminalDrivenKeyFacialSelectModeKeep = false;
        private int _terminalDrivenKeyFacialSelectIndex = 0;
        private System.Action<int> _terminalDrivenKeyFacialSelectFunc = null;
        private string[] _terminalDrivenKeyFacialTypeName = null;
        private string _terminalDrivenKeyFacialTypeLabel;
        private Vector2 _terminalDrivenKeyFacialTypeScrollView;

        private Vector2 _terminalDrivenKeyFacialScrollView = Vector2.zero;

        private int _terminalDrivenKeyFacialIndex = 0;
        public int TerminalDrivenKeyFacialIndex => _terminalDrivenKeyFacialIndex;
        private float _terminalDrivenKeyFacialSlider;

        private int _terminalDrivenKeyEarIndex;
        public int TerminalDrivenKeyEarIndex => _terminalDrivenKeyEarIndex;
        private float _terminalDrivenKeyEarSlider = 1f;
        public float TerminalDrivenKeyEarSlider => _terminalDrivenKeyEarSlider;

        private int _terminalDrivenKeyEyeBrowIndexL;
        private float _terminalDrivenKeyEyeBrowSliderL;
        private int _terminalDrivenKeyEyeBrowIndexR;
        private float _terminalDrivenKeyEyeBrowSliderR;

        private int _terminalDrivenKeyEyeIndexL;
        private float _terminalDrivenKeyEyeSliderL;
        private int _terminalDrivenKeyEyeIndexR;
        private float _terminalDrivenKeyEyeSliderR;
        private int _terminalDrivenKeyMouthIndex;
        private float _terminalDrivenKeyMouthSlider;

        private int _terminalDrivenKeyCheekIndex;
        public int TerminalDrivenKeyCheekIndex => _terminalDrivenKeyCheekIndex;
        private float _terminalDrivenKeyCheekSlider = 1f;
        public float TerminalDrivenKeyCheekSlider => _terminalDrivenKeyCheekSlider;

        private bool _terminalBlink = false;
        public bool TerminalBlink => _terminalBlink;

        // モブ
        private int _terminalMobFaceNo = 0;
        public int TerminalMobFaceNo => _terminalMobFaceNo;
        private int _terminalMobHairNo = 0;
        public int TerminalMobHairNo => _terminalMobHairNo;
        private int _terminalMobHairColorNo = 0;
        public int TerminalMobHairColorNo => _terminalMobHairColorNo;
        private int _terminalMobHairSkinNo = 0;
        public int TerminalMobHairSkinNo => _terminalMobHairSkinNo;
        private int _terminalMobDressColorSetId = 0;
        public int TerminalMobDressColorSetId => _terminalMobDressColorSetId;
        private float _terminalMobHairCutoff = 0.5f;
        public float TerminalMobHairCutoff => _terminalMobHairCutoff;
        private int _terminalMobAttachId = -1;
        public int TerminalMobAttachId => _terminalMobAttachId;
        private bool _isTerminalMobUseMobId = false;
        public bool IsTerminalMobUseMobId => _isTerminalMobUseMobId;
        private int _terminalMobId = 8000;
        public int TerminalMobId => _terminalMobId;
        private MobBuildInfo _terminalMobBuildInfo = new MobBuildInfo();

        // 観衆
        private bool _isTerminalAudienceUseMobId = false;
        public bool IsTerminalAudienceUseMobId => _isTerminalAudienceUseMobId;
        private int _terminalAudienceId = 1;
        public int TerminalAudienceId => _terminalAudienceId;
        private AudienceBuildInfo _terminalAudienceBuildInfo = StaticVariableDefine.CG3D.AudienceBuildInfo.DEFAULT_BUILD_INFO;
        public AudienceBuildInfo TerminalAudienceBuildInfo => _terminalAudienceBuildInfo;
        private int _terminalAudienceBustIndex = 0;
        public int TerminalAudienceBustIndex => _terminalAudienceBustIndex;
        private int _terminalAudienceSkinIndex = 0;
        public int TerminalAudienceSkinIndex => _terminalAudienceSkinIndex;
        private int _terminalAudienceShapeIndex = 0;
        public int TerminalAudienceShapeIndex => _terminalAudienceShapeIndex;
        private bool _isTerminalAudienceBuildModel = false;

        private float _terminalAnimationSpeed = 1.0f;
        public float TerminalAnimationSpeed => _terminalAnimationSpeed;
        private float _lightDirectionX = 0.0f;
        public float LightDirectionX => _lightDirectionX;

        private float _lightDirectionY = 180.0f;
        public float LightDirectionY => _lightDirectionY;

        private List<FaceParts> _terminalDrivenKeyFaceEyebrowTypeR = new List<FaceParts>();
        public List<FaceParts> TerminalDrivenKeyFaceEyebrowTypeR => _terminalDrivenKeyFaceEyebrowTypeR;
        private List<FaceParts> _terminalDrivenKeyFaceEyebrowTypeL = new List<FaceParts>();
        public List<FaceParts> TerminalDrivenKeyFaceEyebrowTypeL => _terminalDrivenKeyFaceEyebrowTypeL;
        private List<FaceParts> _terminalDrivenKeyFaceEyeTypeR = new List<FaceParts>();
        public List<FaceParts> TerminalDrivenKeyFaceEyeTypeR => _terminalDrivenKeyFaceEyeTypeR;
        private List<FaceParts> _terminalDrivenKeyFaceEyeTypeL = new List<FaceParts>();
        public List<FaceParts> TerminalDrivenKeyFaceEyeTypeL => _terminalDrivenKeyFaceEyeTypeL;
        private List<FaceParts> _terminalDrivenKeyFaceMouthType = new List<FaceParts>();
        public List<FaceParts> TerminalDrivenKeyFaceMouthType => _terminalDrivenKeyFaceMouthType;

        private int _terminalDrivenKeyEyeHighlightIndex = 0;
        public int TerminalDrivenKeyEyeHighlightIndex => _terminalDrivenKeyEyeHighlightIndex;
        private string[] _terminalHighlightNameArray = null;
        private int _terminalTeardropIndex = 0;
        public int TerminalTeardropIndex => _terminalTeardropIndex;
        public static readonly string[] TERMINAL_TEARDROP_NAME_ARRAY =
        {
            "非表示",
            "000",
            "001",
        };

        // ミニ用。
        private string[] _miniFacialEyebrowNameArray = null;
        public string[] MiniFacialEyebrowNameArray
        {
            get
            {
                if (_miniFacialEyebrowNameArray == null)
                {
                    _miniFacialEyebrowNameArray = new string[MiniFacialController.EYEBROW_MAX_PARTS_NUM];
                    for (int index = 0; index < _miniFacialEyebrowNameArray.Length; ++index)
                    {
                        _miniFacialEyebrowNameArray[index] = index.ToString();
                    }
                }
                return _miniFacialEyebrowNameArray;
            }
        }

        private string[] _miniFacialEyeNameArray = null;
        public string[] MiniFacialEyeNameArray
        {
            get
            {
                if (_miniFacialEyeNameArray == null)
                {
                    _miniFacialEyeNameArray = new string[MiniFacialController.EYE_MAX_PARTS_NUM];
                    for (int index = 0; index < _miniFacialEyeNameArray.Length; ++index)
                    {
                        _miniFacialEyeNameArray[index] = index.ToString();
                    }
                }
                return _miniFacialEyeNameArray;
            }
        }
        private string[] _miniFacialMouthNameArray = null;
        public string[] MiniFacialMouthNameArray
        {
            get
            {
                if (_miniFacialMouthNameArray == null)
                {
                    _miniFacialMouthNameArray = new string[MiniFacialController.MOUTH_MAX_PARTS_NUM];
                    for (int index = 0; index < _miniFacialMouthNameArray.Length; ++index)
                    {
                        _miniFacialMouthNameArray[index] = index.ToString();
                    }
                }
                return _miniFacialMouthNameArray;
            }
        }
        private string[] _miniCheekNameArray =
        {
            "非表示",
            "0",
            "1",
        };
        public string[] MiniCheekNameArray => _miniCheekNameArray;
        private int _miniFacialEyebrowLIndex = 0;
        public int MiniFacialEyebrowLIndex => _miniFacialEyebrowLIndex;
        private int _miniFacialEyebrowRIndex = 0;
        public int MiniFacialEyebrowRIndex => _miniFacialEyebrowRIndex;
        private int _miniFacialEyeLIndex = 0;
        public int MiniFacialEyeLIndex => _miniFacialEyeLIndex;
        private int _miniFacialEyeRIndex = 0;
        public int MiniFacialEyeRIndex => _miniFacialEyeRIndex;
        private int _miniFacialMouthIndex = 0;
        public int MiniFacialMouthIndex => _miniFacialMouthIndex;
        private int _miniCheekIndex = 0;
        public int MiniCheekIndex => _miniCheekIndex;
        private int _terminalMiniFacialIndex = 0;
        public int TerminalMiniFacialIndex => _terminalMiniFacialIndex;

        #endregion

        #region 部位を消す

        private bool _modelNodeIsHairInvisible = false;
        public bool ModelNodeIsHairInvisible => _modelNodeIsHairInvisible;

        private bool _modelNodeIsTailInvisible = false;
        public bool ModelNodeIsTailInvisible => _modelNodeIsTailInvisible;

        #endregion

        private GUILayoutOption TERMINAL_BUTTON_WIDTH = null;
        private GUILayoutOption TERMINAL_LIST_BUTTON_WIDTH = null;
        private GUILayoutOption TERMINAL_BUTTON_HEIGHT = null;
        private GUILayoutOption TERMINAL_SLIDER_WIDTH = null;
        private GUILayoutOption TERMINAL_TEXTAREA_WIDTH = null;
        private float _terminalButtonHeightFloat = 60.0f;

        private bool _isRequierAlwaysLockCamera = false;
        public bool IsRequierAlwaysLockCamera => _isRequierAlwaysLockCamera;

        public void Terminal_LockCameraControll()
        {
            _viewerCameraController.ControllLockFlags |= (int)CharaViewer3DCameraController.ControllType.All;
        }

        public void Terminal_UnlockCameraControll()
        {
            _viewerCameraController.ControllLockFlags &= ~(int)CharaViewer3DCameraController.ControllType.All;
        }

        private void SetTerminalMode(TerminateMode mode)
        {
            if (mode == TerminateMode.Top)
            {
                Terminal_UnlockCameraControll();
            }
            else
            {
                Terminal_LockCameraControll();
            }
            _terminateMode = mode;
            _terminateSubMode = 0;
        }

        private void OnGUI_Terminal_Top()
        {
            GUILayout.BeginVertical();
            {
                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10.0f);
                    var info = GetCharaViewerInfo();
                    string buttonText = GetCharaSlotButtonText(info);
                    if (info != null && GUILayout.Button(buttonText, _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.Slot);
                    }
                }
                GUILayout.EndHorizontal();
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("モデル", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.Model);
                    }

                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("簡易キャラ変更", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.SimpleModel);
                    }
                }

                GUILayout.EndHorizontal();
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("前のキャラへ", _terminalStyle_buttonMid20))
                    {
                        OnClickPrevCharaButton();
                    }

                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("次のキャラへ", _terminalStyle_buttonMid20))
                    {
                        OnClickNextCharaButton();
                    }
                }
                GUILayout.EndHorizontal();
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("アニメーション", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.Animation);
                    }

                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("キャラタイプ変更", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.CharacterType);
                    }
                }
                GUILayout.EndHorizontal();
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("フェイシャル", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.Facial);
                    }
                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("小物", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.Prop);
                    }
                }
                GUILayout.EndHorizontal();
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                {
                    GUILayout.Space(10.0f);
                    if (GUILayout.Button("クロス", _terminalStyle_buttonMid20))
                    {
                        SetTerminalMode(TerminateMode.Cloth);
                    }
                }
                GUILayout.EndHorizontal();
            }
            GUILayout.EndVertical();
        }

        /// <summary>左側のキャラスロットボタンのテキストを取得</summary>
        public string GetCharaSlotButtonText(CharaViewerInfo info)
        {
            if (info == null)
                return string.Empty;

            return TextUtil.Format("{0}:{1}", _currentCharaIndex, info.GetName());
        }

        /// <summary>左側の「前のキャラへ」ボタン押下時処理</summary>
        public void OnClickPrevCharaButton()
        {
            NextCharacter(false);
        }

        /// <summary>左側の「次のキャラへ」ボタン押下時処理</summary>
        public void OnClickNextCharaButton()
        {
            NextCharacter(true);
        }

        private void NextCharacter(bool isAdd)
        {
            int charaCount = _charaList.Count;

            _terminalModelIndex += isAdd ? 1 : -1;
            if (_terminalModelIndex < 0) _terminalModelIndex = charaCount - 1;
            else if (_terminalModelIndex >= charaCount) _terminalModelIndex = 0;

            _terminalRequestBuildModel = true;
            _terminalMobBuildModel = false;
            _isTerminalAudienceBuildModel = false;
            SetTerminalMode(TerminateMode.Top);
        }

        private void OnGUI_Terminal_Model_Back()
        {
            GUILayout.Space(10.0f);

            if (GUILayout.Button("戻る", _terminalStyle_button20, TERMINAL_BUTTON_HEIGHT))
            {
                if (_terminateSubMode == 0)
                {
                    SetTerminalMode(TerminateMode.Top);
                }
                else
                {
                    _terminateSubMode--;
                }
            }
        }

        public void OnGUI_Terminal_ButtonList(string[] item, ref Vector2 scrollView, System.Action<int> func, GUIStyle guiStyle = null , int prevIndex = -1 , GUIStyle selectedStyle = null)
        {
            const float SPACE_HEIGHT = 2f;
            var usingGuiStyle = guiStyle == null ? _terminalStyle_button20 : guiStyle;
            float scrollWidth = usingGuiStyle.fixedWidth + 48;
            float scrollHeight = (Screen.Height - TERMINAL_DEFAULT_SCREEN_HEIGHT_OFFSET) * 0.4f;//Gallop.Screen.Height * 0.4f;

            // スクロールビューのスクロールバーのサイズはスタイルで変更できないので全変更をかけて元に戻す（厳密にはこれでは変な見た目になるが手っ取り早く済ませている）
            float oldWidth = GUI.skin.verticalScrollbar.fixedWidth;
            float oldThumbWidth = GUI.skin.verticalScrollbarThumb.fixedWidth;
            if(guiStyle == null)
            {
                GUI.skin.verticalScrollbarThumb.fixedWidth = 48;
                GUI.skin.verticalScrollbar.fixedWidth = 48;
            }
            else
            {
                GUI.skin.verticalScrollbarThumb.fixedWidth = Mathf.Min(48f * guiStyle.fixedWidth / _terminalStyle_button20.fixedWidth, 48f);
                GUI.skin.verticalScrollbar.fixedWidth = Mathf.Min(48f * guiStyle.fixedWidth / _terminalStyle_button20.fixedWidth, 48f);
            }

            using (var scrollViewScope = new GUILayout.ScrollViewScope(scrollView, GUILayout.Width(scrollWidth), GUILayout.Height(scrollHeight)))
            {
                scrollView = scrollViewScope.scrollPosition;

                // GUILayout使って表示すると指定しているサイズとは別に余白があるようなのでその分
                // これが無いとスクロールを一番下まで持っていった時に（実際のスクロール位置と計算位置がずれて？）エラーが出ることがある。
                const float GUI_LAYOUT_OFFSET = 1f;
                float buttonHeight = SPACE_HEIGHT + _terminalButtonHeightFloat + GUI_LAYOUT_OFFSET;
                float totalHeight = buttonHeight * item.Length;

                // #94190対応
                // ①GUILayoutはRepaintが何度もよばれる
                // ②条件分岐次第でButtonなどの描画をしないとエラーがでる(position in a group with only 1 controls when doing repaint )
                // scrollViewは再利用されるため、項目が多いリストで利用後に少ないリストを表示すると
                // ボタンの描画をしないケースが発生するため②でエラーが出る
                // 表示配列に対してClampで丸めて対応する
                scrollView.y =Mathf.Clamp(scrollView.y, 0.0f, totalHeight - scrollHeight);
                if (totalHeight > (scrollHeight * 2f))
                {
                    // スクロールがかなり大きくなっているので負荷対策のため不要な部分を表示しないようにする。
                    int spaceButtonCount = (int)(scrollView.y / buttonHeight);
                    float spaceHight = spaceButtonCount * buttonHeight;
                    GUILayout.Space(spaceHight);
                    int buttonDrawCount = (int)(scrollHeight / buttonHeight) + 1; // 上下に見切れる部分用に１個追加。
                    if (spaceButtonCount + buttonDrawCount >= item.Length)
                    {
                        buttonDrawCount = item.Length - spaceButtonCount;
                    }
                    for (int i = 0; i < buttonDrawCount; i++)
                    {
                        int index = spaceButtonCount + i;
                        var buttonGuiStyle = usingGuiStyle;
                        if (prevIndex != -1 && selectedStyle != null)
                        {
                            if (index == prevIndex)
                            {
                                buttonGuiStyle = selectedStyle;
                            }
                        }
                        if (GUILayout.Button(item[index], buttonGuiStyle))
                        {
                            func(index);
                            break;
                        }
                    }
                    spaceHight = totalHeight - spaceHight - buttonHeight * buttonDrawCount;
                    if (spaceHight > 0f)
                    {
                        GUILayout.Space(spaceHight);
                    }
                }
                else
                {
                    for (int i = 0; i < item.Length; i++)
                    {
                        var buttonGuiStyle = usingGuiStyle;
                        if (prevIndex != -1 && selectedStyle != null)
                        {
                            if (i == prevIndex)
                            {
                                buttonGuiStyle = selectedStyle;
                            }
                        }

                        if (GUILayout.Button(item[i], buttonGuiStyle))
                        {
                            func(i);
                            break;
                        }
                    }
                }
            }

            // 元に戻す
            GUI.skin.verticalScrollbar.fixedWidth = oldWidth;
            GUI.skin.verticalScrollbarThumb.fixedWidth = oldThumbWidth;
        }

        private void OnGUI_Terminal_Model_Chara()
        {
            //横に3人ずつ並べる
            if (_terminalCharaName == null)
            {
                _terminalCharaName = GetCharacterString();
            }

            var charaNames = _terminalCharaName;
            OnGUI_Terminal_ButtonList(charaNames,
                ref _terminalCharaScrollView,
                (int index) =>
                {
                    _terminalModelIndex = index;
                    _terminateSubMode++;
                    _terminalDressNameArray = null;
                    _terminalDressScrollView = Math.VECTOR2_ZERO;
                    _terminalHeadName = null;
                    _terminalHeadScrollView = Math.VECTOR2_ZERO;
                    _terminalMobBuildModel = false;
                    _isTerminalAudienceBuildModel = false;
                }
            );

            GUILayout.Space(10.0f);
            if (GUILayout.Button("モブ", _terminalStyle_button20))
            {
                SetTerminalMode(TerminateMode.MobModel);
            }
            if (GUILayout.Button("ミニモブ", _terminalStyle_button20))
            {
                SetTerminalMode(TerminateMode.MiniMobModel);
            }
            if (GUILayout.Button("観客", _terminalStyle_button20))
            {
                SetTerminalMode(TerminateMode.AudienceModel);
            }

            OnGUI_Terminal_Model_Back();
        }

        /// <summary>モデル：一覧からキャラアイコンを押した時の処理</summary>
        public void Model_OnClickModelCharaButton(int index)
        {
            _terminalModelIndex = index;
            _terminateSubMode++;
            _terminalDressNameArray = null;
            _terminalDressScrollView = Math.VECTOR2_ZERO;
            _terminalHeadName = null;
            _terminalHeadScrollView = Math.VECTOR2_ZERO;
            _terminalMobBuildModel = false;
            _isTerminalAudienceBuildModel = false;
        }

        /// <summary>簡易キャラ選択：一覧からキャラアイコンをタップした時の処理</summary>
        public void Model_OnClickSimpleModelCharaButton(int index)
        {
            // キャラ決定
            _terminalModelIndex = index;
            _terminalDressNameArray = null;
            _terminalDressScrollView = Math.VECTOR2_ZERO;
            _terminalHeadName = null;
            _terminalHeadScrollView = Math.VECTOR2_ZERO;
            _terminalMobBuildModel = false;
            _isTerminalAudienceBuildModel = false;

            // ロード
            _terminalRequestBuildModel = true;
            SetTerminalMode(TerminateMode.Top);
        }

        /// <summary>モデル→モブ：「モブ」ダイアログの決定ボタンを押した時の処理</summary>
        public void Model_OnDecideMobDialog(DialogCharaViewerMob.Param param)
        {
            _isTerminalMobUseMobId = param.IsUseMobId;
            _terminalMobId = param.MobId;
            _terminalMobFaceNo = param.FaceNo;
            _terminalMobHairColorNo = param.HairColorNo;
            _terminalMobHairNo = param.HairNo;
            _terminalMobDressColorSetId = param.DressColorSetId;
            _terminalMobHairCutoff = param.HairCutoff;
            _terminalMobAttachId = param.AttachId;
            _terminalMobHairSkinNo = param.SkinNo;

            _terminalMobBuildInfo.faceKindId = _terminalMobFaceNo;
            _terminalMobBuildInfo.hairColorId = _terminalMobHairColorNo;
            _terminalMobBuildInfo.hairKindId = _terminalMobHairNo;
            _terminalMobBuildInfo.DressColorId = _terminalMobDressColorSetId;
            _terminalMobBuildInfo.HairCutoff = _terminalMobHairCutoff;
            SetAttachmentModelId(_terminalMobAttachId);
            SetSkin(ModelLoader.SkinType.White + _terminalMobHairSkinNo);
            _terminalMobBuildModel = true;
            _isTerminalAudienceBuildModel = false;
            _terminateSubMode++;
            _terminalDressNameArray = null;
        }

        /// <summary>ミニモブ：ミニモブの一覧から選択した時の処理</summary>
        public void MiniMob_OnClickMiniMobListItem(string itemText)
        {
            // 項目のテキストが数字になっているので、①テキストから空白を取り除き、②int値に変換、する
            itemText = Regex.Replace(itemText, @"\s", "");
            if (int.TryParse(itemText, out int value))
            {
                // 数字は mini_mob.csv の id
                int miniMobId = value;
                var masterData = MasterDataManager.Instance.masterMiniMob.Get(miniMobId);
                if (masterData != null)
                {
                    SetCharacterType(CharacterType.Mini);
                    BuildMiniMobModel(masterData.GetMiniMobParam());
                }
            }
        }

        private void OnGUI_Terminal_Model_CharacterType()
        {
            // タイプ名取得
            if (_terminalCharacterTypeNameArray == null)
            {
                _terminalCharacterTypeNameArray = CHARA_TYPE_STRING_ARRAY;
            }

            var charaTypeNames = _terminalCharacterTypeNameArray;
            OnGUI_Terminal_ButtonList(charaTypeNames,
                ref _terminalCharaScrollView,
                (int index) =>
                {
                    _terminalCharacterTypeIndex = index;
                    _terminateSubMode++;
                    _terminalDressNameArray = null;
                    _terminalDressScrollView = Math.VECTOR2_ZERO;
                    _terminalHeadName = null;
                    _terminalHeadScrollView = Math.VECTOR2_ZERO;
                    _terminalMobBuildModel = false;
                    _isTerminalAudienceBuildModel = false;
                }
            );

            GUILayout.Space(10.0f);

            OnGUI_Terminal_Model_Back();
        }

        /// <summary>キャラタイプ変更：「会話」や「ホーム」などのボタンの押下時処理</summary>
        public void OnClickCharaTypeListItem(int index)
        {
            // キャラタイプ決定
            _terminalCharacterTypeIndex = index;
            _terminalDressNameArray = null;
            _terminalDressScrollView = Math.VECTOR2_ZERO;
            _terminalHeadName = null;
            _terminalHeadScrollView = Math.VECTOR2_ZERO;
            _terminalMobBuildModel = false;
            _isTerminalAudienceBuildModel = false;

            // ロード
            _terminalRequestBuildModel = true;
            SetTerminalMode(TerminateMode.Top);
        }

        private void OnGUI_Terminal_Model_Head()
        {
            if (_terminalHeadName == null)
            {
                _terminalHeadName = GetHeadString(_terminalModelIndex);
            }
            var headName = _terminalHeadName;
            OnGUI_Terminal_ButtonList(headName,
                ref _terminalHeadScrollView,
                (int index) =>
                {
                    _terminalHeadIndex = index;
                    _terminateSubMode++;
                }
            );
            OnGUI_Terminal_Model_Back();
        }

        public void OnDecideModelHead(int headIndex)
        {
            if (_terminalHeadName == null)
            {
                _terminalHeadName = GetHeadString(_terminalModelIndex);
            }
            _terminalHeadIndex = headIndex;
            _terminateSubMode++;
        }

        private void OnGUI_Terminal_Model_Dress()
        {
            if (_terminalDressNameArray == null)
            {
                _terminalDressNameArray = GetDressString(_terminalModelIndex, _terminalMobBuildModel);
            }

            var dressName = _terminalDressNameArray;
            OnGUI_Terminal_ButtonList(dressName,
                ref _terminalDressScrollView,
                index =>
                {
                    _terminalDressIndex = index;
                    _terminateSubMode++;
                }
            );

            {
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("バックダンサーのテクスチャID：");
                var dancer = GUILayout.TextField(_terminalBackDancerId.ToString());
                if (int.TryParse(dancer, out var result))
                {
                    _terminalBackDancerId = result;
                }
                GUILayout.EndHorizontal();
            }

            OnGUI_Terminal_Model_Back();
        }

        /// <summary>モデル：「衣装選択」ダイアログで決定ボタンを押した時の処理</summary>
        public void Model_OnDecideDressSelectDialog(DialogCharaViewerDressSelect.Param param)
        {
            // パラメータ決定
            _terminalDressIndex = param.DressIndex;
            _terminalBackDancerId = param.BackDancerTextureId;
            _terminalCharacterTypeIndex = param.CharaTypeIndex;
            if (param.HeadIndex != -1)
            {
                if (_terminalHeadName == null)
                {
                    _terminalHeadName = GetHeadString(_terminalModelIndex);
                }
                _terminalHeadIndex = param.HeadIndex;
            }

            // モデルをロード
            _terminalRequestBuildModel = true;
            SetTerminalMode(TerminateMode.Top);
        }

        private void OnGUI_Terminal_Model_Mode()
        {
            OnGUI_Terminal_ButtonList(CHARA_TYPE_STRING_ARRAY,
                ref _terminalCharaTypeScrollView,
                index =>
                {
                    _terminalCharacterTypeIndex = index;
                    _terminateSubMode++;
                }
            );
            OnGUI_Terminal_Model_Back();
        }

        private void OnGUI_Terminal_Model_ColorSet()
        {
            var dressId = GetDressID(_terminalModelIndex, _terminalDressIndex, _terminalMobBuildModel);
            var dressData = MasterDataManager.Instance.masterDressData.Get(dressId);
            if (dressData == null || !dressData.IsAreaMapDress())
            {
                _terminateSubMode++;
                return;
            }

            var colorSetDataList = MasterDataManager.Instance.masterCharaDressColorSet.GetListWithDressId(dressId);
            if (colorSetDataList != null)
            {
                _colorSetIdStringList = colorSetDataList.Select(d => d.Id.ToString()).ToList();
                _colorSetIdStringList.Insert( 0, "0"); // デフォルト項目はCSVにはないので追加する
                OnGUI_Terminal_ButtonList(_colorSetIdStringList.ToArray(),
                    ref _terminalCharaColorSetScrollView,
                    index =>
                    {
                        _terminalCharacterColorSetIndex = index;
                        _terminateSubMode++;
                    }
                );
            }
            OnGUI_Terminal_Model_Back();
        }

        private void OnGUI_Terminal_Slot()
        {
            CreateTerminalSlotNameArray();

            OnGUI_Terminal_ButtonList(_terminalSlotNameArray,
                ref _terminalSlotScrollView,
                (int index) =>
                {
                    OnClickCharaSlotSubMenuButton(index);
                    SetTerminalMode(TerminateMode.Top);
                }
            );

            GUILayout.Space(10.0f);

            if (GUILayout.Button("戻る", _terminalStyle_button20))
            {
                SetTerminalMode(TerminateMode.Top);
            }
        }

        /// <summary>キャラスロット：リスト内のボタンのテキスト一覧を作成する</summary>
        public void CreateTerminalSlotNameArray()
        {
            for (int i = 0; i < CHARACTER_MAX_COUNT; i++)
            {
                _terminalSlotNameArray[i] = TextUtil.Format("{0}:{1}", i, GetCharaViewerInfo(i).GetName());
            }
        }

        /// <summary>キャラスロット：リスト内のボタンの押下時処理</summary>
        public void OnClickCharaSlotSubMenuButton(int index)
        {
            _currentCharaIndex = index;
        }

        private void OnGUI_Terminal_Model()
        {
            //キャラ、頭、衣装とする
            switch (_terminateSubMode)
            {
                case 0: //キャラ選択
                    OnGUI_Terminal_Model_Chara();
                    break;
                case 1: //頭
                    OnGUI_Terminal_Model_Head();
                    break;
                case 2: //衣装
                    OnGUI_Terminal_Model_Dress();
                    break;
                case 3: //モード選択
                    OnGUI_Terminal_Model_Mode();
                    break;
                case 4: //カラーセット選択
                    OnGUI_Terminal_Model_ColorSet();
                    break;
                case 5: //ロード
                    _terminalRequestBuildModel = true;
                    SetTerminalMode(TerminateMode.Top);
                    break;
            }
        }

        /// <summary>
        /// 簡易キャラ読み込み
        /// </summary>
        private void OnGUI_Terminal_SimpleModel()
        {
            //キャラ、頭、衣装とする
            switch (_terminateSubMode)
            {
                case 0: //キャラ選択
                    OnGUI_Terminal_Model_Chara();
                    break;
                case 1: //ロード
                    _terminalRequestBuildModel = true;
                    SetTerminalMode(TerminateMode.Top);
                    break;
            }
        }

        /// <summary>
        /// キャラタイプ読み込み
        /// </summary>
        private void OnGUI_Terminal_CharacterType()
        {
            switch (_terminateSubMode)
            {
                case 0: //キャラタイプ選択
                    OnGUI_Terminal_Model_CharacterType();
                    break;
                case 1: //ロード
                    _terminalRequestBuildModel = true;
                    SetTerminalMode(TerminateMode.Top);
                    break;
            }
        }

        private void OnGUI_terminal_SwapMotion()
        {
            var style = new GUIStyle(GUI.skin.button);
            style.fontSize = 24;
            style.fixedWidth = 120.0f;
            style.fixedHeight = 60.0f;
            var redStyle = new GUIStyle(GUI.skin.button);
            redStyle.normal.textColor = Color.red;
            redStyle.fontSize = 24;
            redStyle.fixedWidth = 120.0f;
            redStyle.fixedHeight = 60.0f;

            GUILayout.Label("衣装による入れ替えモーション", _terminalStyle_label32);

            if (GUILayout.Button("無効", _terminalSwapMotionType == EventSwapMotionType.None ? redStyle : style))
            {
                _terminalSwapMotionType = EventSwapMotionType.None;
            }
            if (GUILayout.Button("衣装からモーションを決める", _terminalSwapMotionType == EventSwapMotionType.Normal ? redStyle : style))
            {
                _terminalSwapMotionType = EventSwapMotionType.Normal;
            }
        }

        /// <summary>アニメーション：「衣装による入れ替えモーション」の設定が変更された時の処理</summary>
        public void Anim_OnChangedSwapMotion(int index)
        {
            _terminalSwapMotionType = (EventSwapMotionType)index;
        }

        private void OnGUI_terminal_IKAnimation()
        {
            var style = new GUIStyle(GUI.skin.button);
            style.fontSize = 24;
            style.fixedWidth = 120.0f;
            style.fixedHeight = 60.0f;
            var redStyle = new GUIStyle(GUI.skin.button);
            redStyle.normal.textColor = Color.red;
            redStyle.fontSize = 24;
            redStyle.fixedWidth = 120.0f;
            redStyle.fixedHeight = 60.0f;

            _terminalIKAnimationFilter = GUILayout.Toggle(_terminalIKAnimationFilter, "リストフィルタリング", _terminalStyle_label32);

            if (GUILayout.Button("通常", _terminalIKAnimationType == TerminalIKMode.Normal ? redStyle : style))
            {
                _terminalIKAnimationType = TerminalIKMode.Normal;
            }
            GUILayout.Space(16.0f);
            if (GUILayout.Button("IK", _terminalIKAnimationType == TerminalIKMode.IK ? redStyle : style))
            {
                _terminalIKAnimationType = TerminalIKMode.IK;
            }
            GUILayout.Space(16.0f);
            if (GUILayout.Button("IK 加算", _terminalIKAnimationType == TerminalIKMode.IKAdd ? redStyle : style))
            {
                _terminalIKAnimationType = TerminalIKMode.IKAdd;
            }
            GUILayout.Space(16.0f);
        }

        /// <summary>アニメーション：「リストフィルタリング」チェックボックスを切り替えた時の処理</summary>
        public void Anim_OnChangedIKAnimationFilter(bool isOn)
        {
            _terminalIKAnimationFilter = isOn;
        }

        /// <summary>アニメーション：「リストフィルタリング」の種類が変更された時の処理</summary>
        public void Anim_OnChangedIKAnimationType(int index)
        {
            _terminalIKAnimationType = (TerminalIKMode)index;
        }

        private void OnGUI_terminal_Tear()
        {
            var tear = GUILayout.Toggle(_terminalIsTear, "涙の表示", _terminalStyle_toggle20);
            if (_terminalIsTear != tear)
            {
                _terminalIsTear = tear;
                VisibleTear(_terminalIsTear);
            }
            GUILayout.Space(16.0f);
        }

        /// <summary>アニメーション：「涙の表示」を切り替えた時の処理</summary>
        public void Anim_OnChangedIsTear(bool isOn)
        {
            _terminalIsTear = isOn;
            VisibleTear(_terminalIsTear);
        }

        private bool OnGUI_terminal_WetDirt()
        {
            bool isReload = false;

            var isWet = GUILayout.Toggle(_terminalIsWet, "濡れ", _terminalStyle_toggle20);
            if (isWet != _terminalIsWet)
            {
                _terminalIsWet = isWet;
                //キャラクターを再度作り直す必要がある
                isReload = true;
                SetWet(isWet);
            }
            GUILayout.Space(16.0f);

            var isDirt = GUILayout.Toggle(_terminalIsDirt, "汚れ", _terminalStyle_toggle20);
            if (isDirt != _terminalIsDirt)
            {
                _terminalIsDirt = isDirt;
                //キャラクターを再度作り直す必要がある
                isReload = true;
                SetDirt(isDirt);
            }
            GUILayout.Space(16.0f);

            GUILayout.BeginHorizontal();
            GUILayout.Label("汚れ量：R", _terminalStyle_label32);
            _terminalDirtEnergy[0] = GUILayout.HorizontalSlider(_terminalDirtEnergy[0], 0.0f, 1.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
            GUILayout.EndHorizontal();
            GUILayout.Space(16.0f);

            GUILayout.BeginHorizontal();
            GUILayout.Label("汚れ量：G", _terminalStyle_label32);
            _terminalDirtEnergy[1] = GUILayout.HorizontalSlider(_terminalDirtEnergy[1], 0.0f, 1.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
            GUILayout.EndHorizontal();
            GUILayout.Space(16.0f);

            GUILayout.BeginHorizontal();
            GUILayout.Label("汚れ量：B", _terminalStyle_label32);
            _terminalDirtEnergy[2] = GUILayout.HorizontalSlider(_terminalDirtEnergy[2], 0.0f, 1.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
            GUILayout.EndHorizontal();
            GUILayout.Space(16.0f);

            SetDirtRate(_terminalDirtEnergy);

            return isReload;
        }

        /// <summary>アニメーション：「濡れ」チェックボックスを切り替えた時の処理</summary>
        public void Anim_OnChangedIsWet(bool isOn)
        {
            _terminalIsWet = isOn;

            //キャラクターを再度作り直す必要がある
            SetWet(isOn);
            _terminalRequestBuildModel = true;
        }

        /// <summary>アニメーション：「汚れ」チェックボックスを切り替えた時の処理</summary>
        public void Anim_OnChangedIsDirt(bool isOn)
        {
            _terminalIsDirt = isOn;

            //キャラクターを再度作り直す必要がある
            SetDirt(isOn);
            _terminalRequestBuildModel = true;
        }

        /// <summary>アニメーション：「汚れ量R」スライダーが切り替わった時の処理</summary>
        public void Anim_OnChangeDirtEnergyRSlider(float sliderValue)
        {
            _terminalDirtEnergy[0] = sliderValue;
            SetDirtRate(_terminalDirtEnergy);
        }

        /// <summary>アニメーション：「汚れ量G」スライダーが切り替わった時の処理</summary>
        public void Anim_OnChangeDirtEnergyGSlider(float sliderValue)
        {
            _terminalDirtEnergy[1] = sliderValue;
            SetDirtRate(_terminalDirtEnergy);
        }

        /// <summary>アニメーション：「汚れ量B」スライダーが切り替わった時の処理</summary>
        public void Anim_OnChangeDirtEnergyBSlider(float sliderValue)
        {
            _terminalDirtEnergy[2] = sliderValue;
            SetDirtRate(_terminalDirtEnergy);
        }


        private void OnGUI_terminal_Training()
        {
            Terminal_UnlockCameraControll();

            bool isReload = OnGUI_terminal_WetDirt();

            OnGUI_terminal_Tear();

            if (GUI.changed)
            {
                Terminal_LockCameraControll();
            }

            if (isReload)
            {
                _terminalRequestBuildModel = true;
            }
        }

        private void OnGUI_terminal_Race()
        {
            Terminal_UnlockCameraControll();

            //レース固有の項目
            bool isReload = OnGUI_terminal_WetDirt();

            if (GUI.changed)
            {
                Terminal_LockCameraControll();
            }

            if (isReload)
            {
                _terminalRequestBuildModel = true;
            }
        }

        private string[] MakeIKAnimationString(bool isFilter)
        {
            var ikNameArray = GetAnimationNames();
            if (!isFilter)
                return ikNameArray;

            var filterList = new List<string>();
            if (_terminalViewerParamData != null)
            {
                var commandFilterArray = _terminalViewerParamData.IKAnimationStoryCommandArray;
                foreach (var name in ikNameArray)
                {
                    if (Array.FindIndex(commandFilterArray, (cmdName) => cmdName.Equals(name)) >= 0)
                    {
                        filterList.Add(name);
                    }
                }
            }
            else
            {
                foreach (var name in ikNameArray)
                {
                    if (Array.FindIndex(CharaViewer.IKAnimationStoryCommand, (cmdName) => cmdName.Equals(name)) >= 0)
                    {
                        filterList.Add(name);
                    }
                }
            }
            return filterList.ToArray();
        }

        private void SetTerminalIKState()
        {
            switch (_terminalIKAnimationType)
            {
                case TerminalIKMode.IK:
                    OnEventAnimChanged(true, false);
                    break;
                case TerminalIKMode.IKAdd:
                    OnEventAnimChanged(true, true);
                    break;
                default:
                    OnEventAnimChanged(false, false);
                    break;
            }
        }

        private void OnGUI_terminal_Animation()
        {
            Terminal_UnlockCameraControll();

            bool isSelected = false;
            if (GetCharaViewerInfo().CharacterType == CharacterType.Story)
            {
                GUILayout.BeginHorizontal();

                Anim_CreateTerminalAnimationNameArrayVerStory();

                OnGUI_Terminal_ButtonList(_terminalAnimationNameArray, ref _terminalAnimationScrollView, Anim_OnClickAnimationListItemVerStory);

                GUILayout.Space(10f);

                if (Anim_IsNeedAnimationCommandVariantUI())
                {
                    OnGUI_Terminal_ButtonList(_terminalCommandVariantArray, ref _terminalAnimation2ScrollView,
                    index =>
                    {
                        Anim_OnChangedCommandVariation(index);
                        isSelected = true;
                    });
                }

                if (_terminalMotionSeSettingOn)
                {
                    //#105044 MotionSEのMaterialIDの設定を表示
                    {
                        GUILayout.BeginVertical();
                        var model = GetCharaViewerInfo().ModelController;
                        if (model != null)
                        {
                            Anim_CreateTerminalMotionSeMaterialIdStrArray();

                            OnGUI_Terminal_ButtonList(_terminalMotionSeMaterialIdStrArray, ref _terminalAnimationMotionSEScrollView, (index) =>
                            {
                                Anim_OnChangedMotionSeMaterialId(index);
                            }, guiStyle: _terminalStyle_buttonSmall20);
                        }
                        GUILayout.EndVertical();
                    }
                }

                GUILayout.EndHorizontal();

                _terminalAnimationIndex = 0; // StoryではAnimationは並べた順番で管理する
            }
            else
            {
                OnGUI_Terminal_ButtonList(GetAnimationNames(), ref _terminalAnimationScrollView,
                index =>
                {
                    _terminalAnimationIndex = index;
                    isSelected = true;
                });
            }

            GUILayout.BeginHorizontal();
            if(GUILayout.Button(_terminalMotionSeSettingOn ? "MaterialId On" : "MaterialId Off", _terminalStyle_button20))
            {
                _terminalMotionSeSettingOn = !_terminalMotionSeSettingOn;
            }

            var guiDefaultColor = GUI.color;
            GUI.color = Color.red;
            GUILayout.Label("MaterialId : " + _terminalMotionSeMaterialId, _terminalStyle_label32);
            GUI.color = guiDefaultColor;

            GUI.color = guiDefaultColor;
            GUILayout.EndHorizontal();

            float oldWidth = GUI.skin.verticalScrollbar.fixedWidth;
            float oldThumbWidth = GUI.skin.verticalScrollbarThumb.fixedWidth;
            float oldThumbHeight = GUI.skin.verticalScrollbarThumb.fixedHeight;
            GUI.skin.verticalScrollbarThumb.fixedWidth = 48;
            GUI.skin.verticalScrollbar.fixedWidth = 48;

            _terminalAnimationOptionScrollView = GUILayout.BeginScrollView(_terminalAnimationOptionScrollView, GUILayout.Width(Screen.Width * 0.7f));
            {

                GUILayout.Space(20f);
                {

                    GUILayout.Label("モーションスピード：", _terminalStyle_label32);
                    GUILayout.BeginHorizontal();
                    _terminalAnimationSpeed = GUILayout.HorizontalSlider(_terminalAnimationSpeed, 0.0f, 1.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
                    SetMotionParameter(0.0f, 0.0f, _terminalAnimationSpeed);

                    GUILayout.Space(48);
                    GUILayout.EndHorizontal();
                    _isLightOverride = GUILayout.Toggle(_isLightOverride, "ライトを変更できるように", _terminalStyle_toggle20);
                    GUILayout.Label("ライトX軸回転", _terminalStyle_label32);
                    _lightDirectionX = GUILayout.HorizontalSlider(_lightDirectionX, 0.0f, 360.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
                    GUILayout.Label("ライトY軸回転", _terminalStyle_label32);
                    _lightDirectionY = GUILayout.HorizontalSlider(_lightDirectionY, 0.0f, 360.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
                    Vector3 eulerAngles = _directionalLight.transform.eulerAngles;
                    eulerAngles.x = _lightDirectionX;
                    eulerAngles.y = _lightDirectionY;
                    _directionalLight.transform.eulerAngles = eulerAngles;
                }

                // ループのみ再生
                {
                    GetCharaViewerInfo().StoryModelDriver.LoopMotion = GUILayout.Toggle(GetCharaViewerInfo().StoryModelDriver.LoopMotion, "ループのみ再生", _terminalStyle_toggle20);
                }
                

                switch (GetCharaViewerInfo().CharacterType)
                {
                    case CharacterType.Training:
                        {
                            GUILayout.Label("育成専用設定", _terminalStyle_label32);
                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("Type番号", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingFolderNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingFolderNo = numberValue;
                                    SetTrainingFolder(numberValue);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("大カテゴリ", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingBigCategoryNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingBigCategoryNo = numberValue;
                                    SetTrainingBigCategory(numberValue - 1);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("中カテゴリ", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingCategoryNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingCategoryNo = numberValue;
                                    SetTrainingCategory(numberValue - 1);   //内部で+1されるので-1を渡す
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("小カテゴリ", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingSubCategoryNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingSubCategoryNo = numberValue;
                                    SetTrainingSubCategory(numberValue - 1);
                                }
                                GUILayout.EndHorizontal();
                            }

                            if (_terminalTrainingCategoryNo == 1)
                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("レベル", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingLevelNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingLevelNo = numberValue;
                                    SetTrainingLevelCategory(numberValue - 1);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("演出差分", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingCuttDiff.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingCuttDiff = numberValue;
                                    SetTrainingCuttDiff(_terminalTrainingCuttDiff);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("併せ馬数", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingNumber.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingNumber = numberValue;
                                    SetTrainingNumber(_terminalTrainingNumber);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("何番目のキャラか", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingMotionNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingMotionNo = numberValue;
                                    SetTrainingMotion(_terminalTrainingMotionNo, _terminalTrainingMotionDiffNo);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("キャラ差分", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingMotionDiffNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingMotionDiffNo = numberValue;
                                    SetTrainingMotion(_terminalTrainingMotionNo, _terminalTrainingMotionDiffNo);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();
                                GUILayout.Label("カット番号", _terminalStyle_label32);
                                var number = GUILayout.TextField(_terminalTrainingSceneCutNo.ToString(), _terminalStyle_textField24);
                                if (int.TryParse(number, out var numberValue))
                                {
                                    _terminalTrainingSceneCutNo = numberValue;
                                    SetTrainingCutt(_terminalTrainingSceneCutNo);
                                }
                                GUILayout.EndHorizontal();
                            }

                            {
                                GUILayout.BeginHorizontal();

                                var type = TRAINING_TYPE_STRING_ARRAY;

                                GUILayout.Label("パターン", _terminalStyle_label32);
                                _terminalTrainingType = GUILayout.SelectionGrid(_terminalTrainingType, type, type.Length);
                                SetTrainingType(_terminalTrainingType);

                                GUILayout.EndHorizontal();
                            }

                            {
                                GetTrainingMotionPath(out var clipPath, out var facialPath);
                                GUILayout.Label(clipPath);
                                if (!ResourceManager.IsExistAsset(clipPath))
                                {
                                    var style = new GUIStyle(GUI.skin.label);
                                    style.normal.textColor = Color.red;
                                    GUILayout.Label("存在しないアニメーションなので再生できません", style);
                                }
                            }
                            OnGUI_terminal_Training();
                        }
                        break;

                    case CharacterType.Live:
                        GUILayout.Label("ライブ専用設定", _terminalStyle_label32);
                        //ポジションは5か所
                        for (int i = 0; i < 5; i++)
                        {
                            if (GUILayout.Button("位置:" + i, _terminalStyle_button20))
                            {
                                _terminalLivePositionIndex = i;
                                SetLiveMotionPosition(_terminalLivePositionIndex);
                            }
                        }
                        break;

                    case CharacterType.Race:
                        GUILayout.Label("レース専用設定", _terminalStyle_label32);
                        OnGUI_terminal_Race();
                        break;

                    case CharacterType.Story:
                        GUILayout.Label("会話シーン専用設定：", _terminalStyle_label32);
                        OnGUI_terminal_Tear();
                        OnGUI_terminal_IKAnimation();
                        OnGUI_terminal_SwapMotion();
                        break;
                }
            }
            GUILayout.EndScrollView();
            // 元に戻す
            GUI.skin.verticalScrollbar.fixedWidth = oldThumbWidth;
            GUI.skin.verticalScrollbarThumb.fixedWidth = oldThumbWidth;
            GUI.skin.verticalScrollbarThumb.fixedHeight = oldThumbHeight;

            if (GUI.changed || _isRequierAlwaysLockCamera)
            {
                Terminal_LockCameraControll();
            }
            GUILayout.Space(30);
            _isRequierAlwaysLockCamera = GUILayout.Toggle(_isRequierAlwaysLockCamera, "カメラを動かさない", _terminalStyle_toggle20);
            GUILayout.Space(30);

            if (GUILayout.Button("アニメーション再生", _terminalStyle_button20) || isSelected)
            {
                _terminalRequestPlayAnimation = true;
            }

            if (GUI.changed)
            {
                Terminal_LockCameraControll();
            }

            OnGUI_Terminal_Model_Back();
        }

        /// <summary>アニメーション：会話のアニメーションリストで表示する文字列一覧を作成</summary>
        public void Anim_CreateTerminalAnimationNameArrayVerStory()
        {
            _terminalAnimationNameArray = GetAnimationNames();
            if (_terminalIKAnimationType != TerminalIKMode.Normal)
            {
                _terminalAnimationNameArray = MakeIKAnimationString(_terminalIKAnimationFilter);
            }
        }

        /// <summary>アニメーション：会話のアニメーションリストの項目をタップした時の処理</summary>
        public void Anim_OnClickAnimationListItemVerStory(int index)
        {
            //スワップモーションの状態をまとめて設定する
            EnableEventSwapMotion = _terminalSwapMotionType;
            if (GetCharaViewerInfo().GetStoryCommandCount() == 0)
            {
                GetCharaViewerInfo().AddStoryCommand(_terminalAnimationNameArray[index]);
            }
            else
            {
                GetCharaViewerInfo().SetStoryCommandAt(0, _terminalAnimationNameArray[index]);
            }
            GetCharaViewerInfo().StartStoryAnimationAtIndex(0);
            SetTerminalIKState();  //IK設定を反映する必要がある
            if (GetCharaViewerInfo().GetStoryCommandCount() > 0)
            {
                // 接触モーションはAとBがあるので両方追加
                var baseNameList = GetAnimationsStartBaseName(GetCharaViewerInfo().GetStoryCommandAt(0)).ToList();
                var pairNameList = baseNameList.FindAll(name => name.StartsWith(EventTimelineModelDriver.PAIR_MOTION_PREFIX));
                foreach (var pairName in pairNameList)
                {
                    if (pairName.EndsWith(ResourcePath.MirrorSuffix))
                    {
                        var nameIndex = pairName.LastIndexOf('_');
                        baseNameList.Add(pairName.Insert(nameIndex, EventTimelineModelDriver.PAIR_MOTION_SUFFIX_A));
                        baseNameList.Add(pairName.Insert(nameIndex, EventTimelineModelDriver.PAIR_MOTION_SUFFIX_B));
                    }
                    else
                    {
                        baseNameList.Add(pairName + EventTimelineModelDriver.PAIR_MOTION_SUFFIX_A);
                        baseNameList.Add(pairName + EventTimelineModelDriver.PAIR_MOTION_SUFFIX_B);
                    }
                    baseNameList.Remove(pairName);
                }
                _terminalCommandVariantArray = baseNameList.ToArray();
            }
        }

        /// <summary>アニメーション：会話以外のアニメーションリストの項目をタップした時の処理</summary>
        public void Anim_OnClickAnimationListItemCommon(int index)
        {
            _terminalAnimationIndex = index;

            _terminalRequestPlayAnimation = true;
        }

        /// <summary>アニメーション：変形型アニメーションUIの表示が必要か</summary>
        public bool Anim_IsNeedAnimationCommandVariantUI()
        {
            return (GetCharaViewerInfo().GetStoryCommandCount() > 0 && _terminalCommandVariantArray != null);
        }

        /// <summary>アニメーション：変形型アニメーションが変更された時の処理</summary>
        public void Anim_OnChangedCommandVariation(int index)
        {
            //スワップモーションの状態をまとめて設定する
            EnableEventSwapMotion = _terminalSwapMotionType;
            GetCharaViewerInfo().SetStoryCommandAt(0, _terminalCommandVariantArray[index]);
            GetCharaViewerInfo().StartStoryAnimationAtIndex(0);
            SetTerminalIKState();  //IK設定を反映する必要がある

            _terminalRequestPlayAnimation = true;
        }

        /// <summary>アニメーション：モーションSEマテリアルIDのUIで表示する文字列一覧を作成</summary>
        public void Anim_CreateTerminalMotionSeMaterialIdStrArray()
        {
            if (_terminalMotionSeMaterialIdStrArray == null)
            {
                _terminalMotionSeMaterialIdStrArray = Enum.GetNames(typeof(MotionSe.MaterialType));
            }
        }

        /// <summary>アニメーション：モーションSEマテリアルIDが変更された時の処理</summary>
        public void Anim_OnChangedMotionSeMaterialId(int index)
        {
            _terminalMotionSeMaterialId = (MotionSe.MaterialType)index;

            var model = GetCharaViewerInfo().ModelController;
            if (model is EventTimelineModelController eventTimelineModelController)
            {
                eventTimelineModelController.MotionSeController.SetFloorMaterialId(_terminalMotionSeMaterialId);
            }
        }

        /// <summary>「カメラを動かさない」チェックボックスが変更された時の処理</summary>
        public void OnChangedIsRequierAlwaysLockCamera(bool isOn)
        {
            _isRequierAlwaysLockCamera = isOn;

            if (isOn)
            {
                Terminal_LockCameraControll();
            }
            else
            {
                Terminal_UnlockCameraControll();
            }
        }

        /// <summary>アニメーション：「アニメーション再生」ボタンを押した時の処理</summary>
        public void Anim_OnClickPlayAnimationButton(int commandVariantIndex)
        {
            // キャラタイプが「会話」なら
            if (GetCharaViewerInfo().CharacterType == CharacterType.Story &&
                commandVariantIndex < _terminalCommandVariantArray.Length)
            {
                // スワップモーションの状態をまとめて設定する
                EnableEventSwapMotion = _terminalSwapMotionType;
                GetCharaViewerInfo().SetStoryCommandAt(0, _terminalCommandVariantArray[commandVariantIndex]);
                GetCharaViewerInfo().StartStoryAnimationAtIndex(0);
                SetTerminalIKState();  //IK設定を反映する必要がある
            }

            _terminalRequestPlayAnimation = true;
        }

        /// <summary>アニメーション：「モーションスピード」スライダーが切り替わった時の処理</summary>
        public void Anim_OnChangeMotionSpeedSlider(float sliderValue)
        {
            _terminalAnimationSpeed = sliderValue;
            SetMotionParameter(0.0f, 0.0f, _terminalAnimationSpeed);
        }

        /// <summary>アニメーション：「ライトを変更する」チェックボックスが変更された時の処理</summary>
        public void Anim_OnChangedIsLightOverride(bool isOn)
        {
            _isLightOverride = isOn;
            ApplyLightOverride();
        }

        /// <summary>アニメーション：「ライトX軸回転」スライダーが切り替わった時の処理</summary>
        public void Anim_OnChangeLightDirectionXSlider(float sliderValue)
        {
            _lightDirectionX = sliderValue;
            ApplyLightOverride();
        }

        /// <summary>アニメーション：「ライトY軸回転」スライダーが切り替わった時の処理</summary>
        public void Anim_OnChangeLightDirectionYSlider(float sliderValue)
        {
            _lightDirectionY = sliderValue;
            ApplyLightOverride();
        }

        private void ApplyLightOverride()
        {
            Vector3 eulerAngles = _directionalLight.transform.eulerAngles;
            eulerAngles.x = _lightDirectionX;
            eulerAngles.y = _lightDirectionY;
            _directionalLight.transform.eulerAngles = eulerAngles;
        }

        /// <summary>アニメーション：「ループのみ再生」チェックボックスが変更された時の処理</summary>
        public void Anim_OnChangedLoopMotion(bool isOn)
        {
            GetCharaViewerInfo().StoryModelDriver.LoopMotion = isOn;
        }

        /// <summary>アニメーション：ライブの「位置：〇」ボタン押下時処理</summary>
        public void Anim_OnClickLivePositionButton(int index)
        {
            _terminalLivePositionIndex = index;
            SetLiveMotionPosition(_terminalLivePositionIndex);
        }

        private void OnGUI_terminal_Cloth()
        {
            Terminal_UnlockCameraControll();

            GUILayout.BeginHorizontal(_terminalClothHorizontalAreaWidth);
            {
                bool isCySpring = GUILayout.Toggle(_terminalCySpringEnable, "CySpring有効", _terminalStyle_toggle20);
                if (isCySpring != _terminalCySpringEnable)
                {
                    OnChangeClothCySpringCheckBox(isCySpring);
                }
            }
            GUILayout.EndHorizontal();

            if (GetCharaViewerInfo().ModelController != null)
            {
                GUILayout.BeginHorizontal(_terminalClothHorizontalAreaWidth);
                {
                    // 通常のモデル。
                    GUILayout.Space(10.0f);
                    var enableWind = GUILayout.Toggle(_terminalCySpringWind, "風有効", _terminalStyle_toggle20);
                    if (enableWind != _terminalCySpringWind)
                    {
                        OnChangeClothWindCheckBox(enableWind);
                    }
                }
                GUILayout.EndHorizontal();

                GUILayout.BeginHorizontal(_terminalClothHorizontalAreaWidth);
                {
                    GUILayout.Space(10.0f);
                    float oldValue = _terminalCySpringWindPower;
                    GUILayout.Label("風の強さ", _terminalStyle_label20);
                    _terminalCySpringWindPower = GUILayout.HorizontalSlider(oldValue, 0.0f, 1.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
                    if (!Math.IsFloatEqual(oldValue, _terminalCySpringWindPower))
                    {
                        SetCySpringWindPower(_terminalCySpringWindPower);
                    }
                }
                GUILayout.EndHorizontal();
            }

            if (GUI.changed)
            {
                Terminal_LockCameraControll();
            }

            OnGUI_Terminal_Model_Back();
        }

        /// <summary>クロス：「CySpring有効」チェックボックスが切り替わった時の処理</summary>
        public void OnChangeClothCySpringCheckBox(bool isOn)
        {
            _terminalCySpringEnable = isOn;
            EnableCySpring(_terminalCySpringEnable);
        }

        /// <summary>クロス：「風有効」チェックボックスが切り替わった時の処理</summary>
        public void OnChangeClothWindCheckBox(bool isOn)
        {
            _terminalCySpringWind = isOn;
            EnableCySpringWind(_terminalCySpringWind);
        }

        /// <summary>クロス：「風の強さ」スライダーが切り替わった時の処理</summary>
        public void OnChangeClothWindPowerSlider(float sliderValue)
        {
            _terminalCySpringWindPower = sliderValue;
            SetCySpringWindPower(_terminalCySpringWindPower);
        }

        private bool OnGUI_terminal_Facial_SelectParts(string label, string[] partName, ref Vector2 scrollView)
        {
            bool result = false;
            GUILayout.Label(label, _terminalStyle_label20);
            _terminalDrivenKeyFacialSelectModeKeep = GUILayout.Toggle(_terminalDrivenKeyFacialSelectModeKeep, "選択後自動で戻らない", _terminalStyle_toggle20);

            OnGUI_Terminal_ButtonList(partName, ref scrollView, (i) => { _terminalDrivenKeyFacialSelectFunc(i); });

            if (GUILayout.Button("戻る", _terminalStyle_button20))
            {
                _terminalDrivenKeyFacialSelectModeKeep = false;
                result = true;
            }

            return result;
        }

        private bool OnGUI_terminal_Facial_PartItem(string name, string[] facialName, int partIndex, float slider, System.Action<float> onChange)
        {
            bool result = false;
            GUILayout.BeginHorizontal(_terminalFacialHorizontalAreaWidth);
            GUILayout.Label(name, _terminalStyle_label20, GUILayout.Width(80f));
            if (GUILayout.Button(facialName[partIndex], _terminalStyle_buttonSmall20))
            {
                result = true;
            }
            if (onChange != null)
            {
                var value = GUILayout.HorizontalSlider(slider, 0.0f, 1.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
                if (!Math.IsFloatEqual(slider, value))
                {
                    onChange(value);
                }
            }
            GUILayout.EndHorizontal();
            GUILayout.Space(8.0f);

            return result;
        }
        private bool OnGUI_terminal_FacePartsItem(List<FaceParts> partsTypeList, string title, string[] nameArray, System.Action onChange)
        {
            for (int typeIndex = 0; typeIndex < partsTypeList.Count; ++typeIndex)
            {
                var info = partsTypeList[typeIndex];
                GUILayout.BeginHorizontal(_terminalFacialHorizontalAreaWidth);
                GUILayout.Label(title + typeIndex + "：", _terminalStyle_label20, GUILayout.Width(80f));

                GUIStyle partsButtonStyle = new GUIStyle(GUI.skin.button);
                partsButtonStyle.fixedWidth = 100;
                partsButtonStyle.fixedHeight = _terminalButtonHeightFloat;
                partsButtonStyle.fontSize = 20;

                if (GUILayout.Button(nameArray[info._faceParts], partsButtonStyle))
                {
                    _terminalDrivenKeyFacialSelectMode = true;
                    _terminalDrivenKeyFacialSelectIndex = typeIndex;
                    _terminalDrivenKeyFacialTypeName = nameArray;
                    _terminalDrivenKeyFacialTypeLabel = title;
                    _terminalDrivenKeyFacialSelectFunc = (index) =>
                    {
                        var tmp = partsTypeList[_terminalDrivenKeyFacialSelectIndex];
                        tmp._faceParts = index;
                        tmp._weight = 1.0f;
                        partsTypeList[_terminalDrivenKeyFacialSelectIndex] = tmp;
                        onChange();
                    };
                }

                var value = GUILayout.HorizontalSlider(info._weight, 0.0f, 2.0f, _terminalStyle_horizontalSlider32, _terminalStyle_horizontalSliderThumb32);
                if (info._faceParts == 0)
                {// Baseは１固定
                    value = 1f;
                }
                if (!Math.IsFloatEqual(info._weight, value))
                {
                    info._weight = value;
                    partsTypeList[typeIndex] = info;
                    onChange();
                }

                GUIStyle miniButtonStyle = new GUIStyle(GUI.skin.button);
                miniButtonStyle.fixedWidth = 60;
                miniButtonStyle.fixedHeight = _terminalButtonHeightFloat;
                miniButtonStyle.fontSize = 20;


                if (typeIndex == 0)
                {// 追加ボタン。
                    if (GUILayout.Button("+", miniButtonStyle))
                    {// 配列の要素が変わるのでここでいったん終了。
                        var tmp = new FaceParts();
                        tmp._faceParts = 0;
                        tmp._weight = 1f;
                        partsTypeList.Add(tmp);
                        return false;
                    }
                }
                else
                {// 削除ボタン。
                    if (GUILayout.Button("-", miniButtonStyle))
                    {// 配列の要素が変わるのでここでいったん終了。
                        partsTypeList.RemoveAt(typeIndex);
                        onChange();
                        return false;
                    }
                }
                GUILayout.EndHorizontal();
            }
            GUILayout.Space(8.0f);
            return true;
        }

        private void OnGUI_terminal_Facial()
        {
            Terminal_UnlockCameraControll();

            // 全キャラ設定
            _isAllCharacterSetting = GUILayout.Toggle(_isAllCharacterSetting, "全キャラ", _terminalStyle_toggle20);

            if (_facialRaceCheck.IsActive)
            {
                _facialRaceCheck.OnGuiTerminal();
            }
            else if (_terminalDrivenKeyFacialSelectMode)
            {
                if (OnGUI_terminal_Facial_SelectParts(_terminalDrivenKeyFacialTypeLabel, _terminalDrivenKeyFacialTypeName, ref _terminalDrivenKeyFacialTypeScrollView))
                {
                    if (!_terminalDrivenKeyFacialSelectModeKeep)
                    {
                        _terminalDrivenKeyFacialSelectMode = false;
                    }
                }
            }
            else
            {
                if (GetCharaViewerInfo().ModelController != null)
                {
                    // #114040 戻るボタンが見切れるので上に出すように変更
                    GUILayout.BeginHorizontal(GUILayout.Width(200.0f));
                    // 通常のモデル。
                    var blink = GUILayout.Toggle(_terminalBlink, "目パチ", _terminalStyle_toggle20);
                    if (_terminalBlink != blink)
                    {
                        _terminalBlink = blink;
                        SetBlink(blink);
                    }
                    OnGUI_Terminal_Model_Back();
                    GUILayout.EndHorizontal();

                    _terminalDrivenKeyFacialTypeName = GetFaceTypeName();
                    GUILayout.BeginHorizontal(_terminalFacialHorizontalAreaWidth);
                    {
                        GUILayout.Label("全体：", _terminalStyle_label20, GUILayout.Width(80f));
                        if (GUILayout.Button(_terminalDrivenKeyFacialTypeName[_terminalDrivenKeyFacialIndex], _terminalStyle_buttonSmall20))
                        {
                            _terminalDrivenKeyFacialSelectMode = true;
                            _terminalDrivenKeyFacialTypeLabel = "FaceType";
                            _terminalDrivenKeyFacialSelectFunc = (index) =>
                            {
                                _terminalDrivenKeyFacialIndex = index;
                                LoadDrivenKeyFaceType_Terminal();
                            };
                        }

                        if (GUILayout.Button("次へ", _terminalStyle_buttonSmall20))
                        {
                            _terminalDrivenKeyFacialIndex++;
                            if (_terminalDrivenKeyFacialIndex >= _terminalDrivenKeyFacialTypeName.Length) _terminalDrivenKeyFacialIndex = _terminalDrivenKeyFacialTypeName.Length - 1;
                            LoadDrivenKeyFaceType_Terminal();
                        }
                        if (GUILayout.Button("前へ", _terminalStyle_buttonSmall20))
                        {
                            _terminalDrivenKeyFacialIndex--;
                            if (_terminalDrivenKeyFacialIndex < 0) _terminalDrivenKeyFacialIndex = 0;
                            LoadDrivenKeyFaceType_Terminal();
                        }
                    }
                    GUILayout.EndHorizontal();


                    if (!OnGUI_terminal_FacePartsItem(_terminalDrivenKeyFaceEyebrowTypeR, "右眉", GetFaceEyebrowTypeName(),
                        () =>
                        {
                            SetFaceEyebrowType(_terminalDrivenKeyFaceEyebrowTypeR.ToArray(), FaceGroupType.EyebrowR);
                        }))
                    {
                        return;
                    }
                    if (!OnGUI_terminal_FacePartsItem(_terminalDrivenKeyFaceEyebrowTypeL, "左眉", GetFaceEyebrowTypeName(),
                        () =>
                        {
                            SetFaceEyebrowType(_terminalDrivenKeyFaceEyebrowTypeL.ToArray(), FaceGroupType.EyebrowL);
                        }))
                    {
                        return;
                    }
                    if (!OnGUI_terminal_FacePartsItem(_terminalDrivenKeyFaceEyeTypeR, "右目", GetFaceEyeTypeName(),
                        () =>
                        {
                            SetFaceEyeType(_terminalDrivenKeyFaceEyeTypeR.ToArray(), FaceGroupType.EyeR);
                        }))
                    {
                        return;
                    }
                    if (!OnGUI_terminal_FacePartsItem(_terminalDrivenKeyFaceEyeTypeL, "左目", GetFaceEyeTypeName(),
                        () =>
                        {
                            SetFaceEyeType(_terminalDrivenKeyFaceEyeTypeL.ToArray(), FaceGroupType.EyeL);
                        }))
                    {
                        return;
                    }
                    if (!OnGUI_terminal_FacePartsItem(_terminalDrivenKeyFaceMouthType, "口", GetFaceMouthTypeName(),
                        () =>
                        {
                            SetFaceMouthType(_terminalDrivenKeyFaceMouthType.ToArray(), FaceGroupType.Mouth);
                        }))
                    {
                        return;
                    }

                    var earTypeNameArray = GetEarTypeName();
                    if (OnGUI_terminal_Facial_PartItem("耳：", earTypeNameArray,
                                                _terminalDrivenKeyEarIndex, _terminalDrivenKeyEarSlider,
                                                (slider) =>
                                                {
                                                    int index = _terminalDrivenKeyEarIndex;
                                                    _terminalDrivenKeyEarSlider = slider;
                                                    SetEar(_drivenKeyEarTypeArray[index], slider);
                                                }
                                                ))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;

                        _terminalDrivenKeyFacialTypeName = earTypeNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "耳";

                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _terminalDrivenKeyEarIndex = index;
                            _terminalDrivenKeyEarSlider = 1.0f;
                            var slider = _terminalDrivenKeyEarSlider;
                            SetEar(_drivenKeyEarTypeArray[index], slider);
                        };
                    }

                    if (OnGUI_terminal_Facial_PartItem("頬：", CheekTypeNameArray,
                                                _terminalDrivenKeyCheekIndex, _terminalDrivenKeyCheekSlider,
                                                (slider) =>
                                                {
                                                    _terminalDrivenKeyCheekSlider = slider;
                                                    SetCheek(_terminalDrivenKeyCheekIndex, _terminalDrivenKeyCheekSlider);
                                                }
                                                ))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;

                        _terminalDrivenKeyFacialTypeName = CheekTypeNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "頬";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _terminalDrivenKeyCheekIndex = index;
                            _terminalDrivenKeyCheekSlider = 1.0f;
                            SetCheek(_terminalDrivenKeyCheekIndex, _terminalDrivenKeyCheekSlider);
                        };
                    }

                    #region 瞳
                    if (_terminalHighlightNameArray == null)
                    {
                        _terminalHighlightNameArray = GetHighlightName();
                    }

                    if (OnGUI_terminal_Facial_PartItem("瞳", _terminalHighlightNameArray, _terminalDrivenKeyEyeHighlightIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _terminalHighlightNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "瞳";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _terminalDrivenKeyEyeHighlightIndex = index;
                            PlayEyeHighlight(EyeHighlightController.HighlightAnimationId.NoAnim + _terminalDrivenKeyEyeHighlightIndex, 0f, 0f);
                        };
                    }
                    #endregion 瞳

                    #region 涙
                    switch (GetCharaViewerInfo().CharacterType)
                    {
                        case CharaViewer.CharacterType.Story:
                            {
                                if (OnGUI_terminal_Facial_PartItem("涙を流す", TERMINAL_TEARDROP_NAME_ARRAY, _terminalTeardropIndex, 0f, null))
                                {
                                    _terminalDrivenKeyFacialSelectMode = true;
                                    _terminalDrivenKeyFacialTypeName = TERMINAL_TEARDROP_NAME_ARRAY;
                                    _terminalDrivenKeyFacialTypeLabel = "涙を流す";
                                    _terminalDrivenKeyFacialSelectFunc = (index) =>
                                    {
                                        _terminalTeardropIndex = index;
                                        PlayTeardrop(_terminalTeardropIndex - 1, 0, false);
                                    };
                                }
                            }
                            break;
                    }

                    #endregion 涙

                    #region レース表情
                    switch (GetCharaViewerInfo().CharacterType)
                    {
                        case CharaViewer.CharacterType.Race:
                        {
                            OnGUI_terminal_FacialMenuButton();
                        }
                            break;
                    }

                    #endregion レース表情

                }
                else if (GetCharaViewerInfo().MiniModelController != null)
                {
                    _terminalDrivenKeyFacialTypeName = MasterDataManager.Instance.masterMiniFaceTypeData.GetFaceTypeNameArray();

                    // ミニモデル。
                    GUILayout.BeginHorizontal(_terminalFacialHorizontalAreaWidth);
                    if (GUILayout.Button(_terminalDrivenKeyFacialTypeName[_terminalMiniFacialIndex], _terminalStyle_button20))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeLabel = "FaceType";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _terminalMiniFacialIndex = index;
                            LoadMiniFaceType_Terminal();
                        };
                    }
                    if (GUILayout.Button("次へ", _terminalStyle_button20))
                    {
                        _terminalMiniFacialIndex++;
                        if (_terminalMiniFacialIndex >= _terminalDrivenKeyFacialTypeName.Length) _terminalMiniFacialIndex = _terminalDrivenKeyFacialTypeName.Length - 1;
                        LoadMiniFaceType_Terminal();
                    }
                    if (GUILayout.Button("前へ", _terminalStyle_button20))
                    {
                        _terminalMiniFacialIndex--;
                        if (_terminalMiniFacialIndex < 0) _terminalMiniFacialIndex = 0;
                        LoadMiniFaceType_Terminal();
                    }
                    GUILayout.EndHorizontal();

                    if (_miniFacialEyebrowNameArray == null)
                    {
                        _miniFacialEyebrowNameArray = new string[MiniFacialController.EYEBROW_MAX_PARTS_NUM];
                        for (int index = 0; index < _miniFacialEyebrowNameArray.Length; ++index)
                        {
                            _miniFacialEyebrowNameArray[index] = index.ToString();
                        }
                    }
                    if (_miniFacialEyeNameArray == null)
                    {
                        _miniFacialEyeNameArray = new string[MiniFacialController.EYE_MAX_PARTS_NUM];
                        for (int index = 0; index < _miniFacialEyeNameArray.Length; ++index)
                        {
                            _miniFacialEyeNameArray[index] = index.ToString();
                        }
                    }
                    if (_miniFacialMouthNameArray == null)
                    {
                        _miniFacialMouthNameArray = new string[MiniFacialController.MOUTH_MAX_PARTS_NUM];
                        for (int index = 0; index < _miniFacialMouthNameArray.Length; ++index)
                        {
                            _miniFacialMouthNameArray[index] = index.ToString();
                        }
                    }

                    if (OnGUI_terminal_Facial_PartItem("左眉", _miniFacialEyebrowNameArray, _miniFacialEyebrowLIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _miniFacialEyebrowNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "左眉";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _miniFacialEyebrowLIndex = index;
                            SetMiniFacialEyebrow(_miniFacialEyebrowLIndex, -1);
                        };
                    }
                    if (OnGUI_terminal_Facial_PartItem("右眉", _miniFacialEyebrowNameArray, _miniFacialEyebrowRIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _miniFacialEyebrowNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "右眉";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _miniFacialEyebrowRIndex = index;
                            SetMiniFacialEyebrow(-1, _miniFacialEyebrowRIndex);
                        };
                    }
                    if (OnGUI_terminal_Facial_PartItem("左目", _miniFacialEyeNameArray, _miniFacialEyeLIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _miniFacialEyeNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "左目";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _miniFacialEyeLIndex = index;
                            SetMiniFacialEye(_miniFacialEyeLIndex, -1);
                        };
                    }
                    if (OnGUI_terminal_Facial_PartItem("右目", _miniFacialEyeNameArray, _miniFacialEyeRIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _miniFacialEyeNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "右目";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _miniFacialEyeRIndex = index;
                            SetMiniFacialEye(-1, _miniFacialEyeRIndex);
                        };
                    }
                    if (OnGUI_terminal_Facial_PartItem("口", _miniFacialMouthNameArray, _miniFacialMouthIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _miniFacialMouthNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "口";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _miniFacialMouthIndex = index;
                            SetMiniFacialMouth(_miniFacialMouthIndex);
                        };
                    }
                    if (OnGUI_terminal_Facial_PartItem("頬", _miniCheekNameArray, _miniCheekIndex, 0f, null))
                    {
                        _terminalDrivenKeyFacialSelectMode = true;
                        _terminalDrivenKeyFacialTypeName = _miniCheekNameArray;
                        _terminalDrivenKeyFacialTypeLabel = "頬";
                        _terminalDrivenKeyFacialSelectFunc = (index) =>
                        {
                            _miniCheekIndex = index;
                            SetMiniCheek(_miniCheekIndex - 1);
                        };
                    }
                }
            }

            if (GUI.changed || _isRequierAlwaysLockCamera)
            {
                Terminal_LockCameraControll();
            }

            GUILayout.Space(30);
            _isRequierAlwaysLockCamera = GUILayout.Toggle(_isRequierAlwaysLockCamera, "カメラを動かさない", _terminalStyle_toggle20);
        }

        /// <summary>フェイシャル：「全キャラ」チェックボックスが切り替わった時の処理</summary>
        public void Facial_OnChangedIsAllCharacterSetting(bool isOn)
        {
            _isAllCharacterSetting = isOn;
        }

        /// <summary>フェイシャル：「目パチ」チェックボックスが切り替わった時の処理</summary>
        public void Facial_OnChangedBlink(bool isOn)
        {
            if (_terminalBlink != isOn)
            {
                _terminalBlink = isOn;
                SetBlink(isOn);
            }
        }

        /// <summary>フェイシャル：「全体」の現在アニメを取得</summary>
        public string Facial_GetCurrentFacialAnimationName()
        {
            var faceTypeNameArray = GetFaceTypeName();
            int index = _terminalDrivenKeyFacialIndex;

            if (index >= faceTypeNameArray.Length)
                return string.Empty;

            return faceTypeNameArray[index];
        }

        /// <summary>フェイシャル：「全体」のアニメ一覧の中から選択された時の処理</summary>
        public void Facial_OnClickFacialAnimationListItem(int index)
        {
            _terminalDrivenKeyFacialTypeName = GetFaceTypeName();
            _terminalDrivenKeyFacialIndex = index;
            LoadDrivenKeyFaceType_Terminal();
        }

        /// <summary>フェイシャル：「全体」の「次へ」ボタンが押された時の処理</summary>
        public void Facial_OnClickFacialNextButton()
        {
            _terminalDrivenKeyFacialTypeName = GetFaceTypeName();
            _terminalDrivenKeyFacialIndex++;
            if (_terminalDrivenKeyFacialIndex >= _terminalDrivenKeyFacialTypeName.Length) _terminalDrivenKeyFacialIndex = _terminalDrivenKeyFacialTypeName.Length - 1;
            LoadDrivenKeyFaceType_Terminal();
        }

        /// <summary>フェイシャル：「全体」の「前へ」ボタンが押された時の処理</summary>
        public void Facial_OnClickFacialPrevButton()
        {
            _terminalDrivenKeyFacialTypeName = GetFaceTypeName();
            _terminalDrivenKeyFacialIndex--;
            if (_terminalDrivenKeyFacialIndex < 0) _terminalDrivenKeyFacialIndex = 0;
            LoadDrivenKeyFaceType_Terminal();
        }

        /// <summary>フェイシャル：右眉の設定が変更された時の処理</summary>
        public void Facial_OnChangedEyebrowR()
        {
            SetFaceEyebrowType(_terminalDrivenKeyFaceEyebrowTypeR.ToArray(), FaceGroupType.EyebrowR);
        }

        /// <summary>フェイシャル：左眉の設定が変更された時の処理</summary>
        public void Facial_OnChangedEyebrowL()
        {
            SetFaceEyebrowType(_terminalDrivenKeyFaceEyebrowTypeL.ToArray(), FaceGroupType.EyebrowL);
        }

        /// <summary>フェイシャル：右目の設定が変更された時の処理</summary>
        public void Facial_OnChangedEyeR()
        {
            SetFaceEyeType(_terminalDrivenKeyFaceEyeTypeR.ToArray(), FaceGroupType.EyeR);
        }

        /// <summary>フェイシャル：左目の設定が変更された時の処理</summary>
        public void Facial_OnChangedEyeL()
        {
            SetFaceEyeType(_terminalDrivenKeyFaceEyeTypeL.ToArray(), FaceGroupType.EyeL);
        }

        /// <summary>フェイシャル：口の設定が変更された時の処理</summary>
        public void Facial_OnChangedMouth()
        {
            SetFaceMouthType(_terminalDrivenKeyFaceMouthType.ToArray(), FaceGroupType.Mouth);
        }

        /// <summary>フェイシャル：「耳」の種類が変更された時の処理</summary>
        public void Facial_OnChangedEarType(int index)
        {
            _terminalDrivenKeyEarIndex = index;
            _terminalDrivenKeyEarSlider = 1.0f;
            var slider = _terminalDrivenKeyEarSlider;
            SetEar(_drivenKeyEarTypeArray[index], slider);
        }

        /// <summary>フェイシャル：「耳」のスライダーが変更された時の処理</summary>
        public void Facial_OnChangedEarSlider(float sliderValue)
        {
            int index = _terminalDrivenKeyEarIndex;
            _terminalDrivenKeyEarSlider = sliderValue;
            SetEar(_drivenKeyEarTypeArray[index], sliderValue);
        }

        /// <summary>フェイシャル：「頬」の種類が変更された時の処理</summary>
        public void Facial_OnChangedCheekType(int index)
        {
            _terminalDrivenKeyCheekIndex = index;
            _terminalDrivenKeyCheekSlider = 1.0f;
            SetCheek(_terminalDrivenKeyCheekIndex, _terminalDrivenKeyCheekSlider);
        }

        /// <summary>フェイシャル：「頬」のスライダーが変更された時の処理</summary>
        public void Facial_OnChangedCheekSlider(float sliderValue)
        {
            _terminalDrivenKeyCheekSlider = sliderValue;
            SetCheek(_terminalDrivenKeyCheekIndex, _terminalDrivenKeyCheekSlider);
        }

        /// <summary>フェイシャル：「瞳」の種類が変更された時の処理</summary>
        public void Facial_OnChangedEyeHilightType(int index)
        {
            _terminalDrivenKeyEyeHighlightIndex = index;
            PlayEyeHighlight(EyeHighlightController.HighlightAnimationId.NoAnim + _terminalDrivenKeyEyeHighlightIndex, 0f, 0f);
        }

        /// <summary>フェイシャル：「涙を流す」が変更された時の処理</summary>
        public void Facial_OnChangedTeardrop(int index)
        {
            _terminalTeardropIndex = index;
            PlayTeardrop(_terminalTeardropIndex - 1, 0, false);
        }

        /// <summary>
        /// FaceTypeを設定。
        /// </summary>
        private void LoadDrivenKeyFaceType_Terminal()
        {
            FaceType faceType = _drivenKeyFaceTypeArray[_terminalDrivenKeyFacialIndex];
            SetFace(faceType, 1.0f, FaceGroupSet.None);

            if (GetCharaViewerInfo().ModelController != null && GetCharaViewerInfo().ModelController.DrivenKeyComponent != null && GetCharaViewerInfo().ModelController.DrivenKeyComponent.FacePartsSetArray != null)
            {
                _terminalDrivenKeyFaceEyebrowTypeR.Clear();
                _terminalDrivenKeyFaceEyebrowTypeL.Clear();
                _terminalDrivenKeyFaceEyeTypeR.Clear();
                _terminalDrivenKeyFaceEyeTypeL.Clear();
                _terminalDrivenKeyFaceMouthType.Clear();
                var facePartsList = GetCharaViewerInfo().ModelController.DrivenKeyComponent.FacePartsSetArray;
                if (facePartsList.Length <= (int)faceType)
                {// 新しく追加されたFaceType.
                    _terminalDrivenKeyFaceEyebrowTypeR.Add(new FaceParts());
                    _terminalDrivenKeyFaceEyebrowTypeL.Add(new FaceParts());
                    _terminalDrivenKeyFaceEyeTypeR.Add(new FaceParts());
                    _terminalDrivenKeyFaceEyeTypeL.Add(new FaceParts());
                    _terminalDrivenKeyFaceMouthType.Add(new FaceParts());
                }
                else
                {
                    FacePartsSet facePartsSet = facePartsList[(int)faceType];
                    foreach (var parts in facePartsSet._eyebrowR)
                    {
                        _terminalDrivenKeyFaceEyebrowTypeR.Add(parts);
                    }
                    foreach (var parts in facePartsSet._eyebrowL)
                    {
                        _terminalDrivenKeyFaceEyebrowTypeL.Add(parts);
                    }
                    foreach (var parts in facePartsSet._eyeR)
                    {
                        _terminalDrivenKeyFaceEyeTypeR.Add(parts);
                    }
                    foreach (var parts in facePartsSet._eyeL)
                    {
                        _terminalDrivenKeyFaceEyeTypeL.Add(parts);
                    }
                    foreach (var parts in facePartsSet._mouth)
                    {
                        _terminalDrivenKeyFaceMouthType.Add(parts);
                    }
                }
            }
        }

        /// <summary>
        /// ミニ用FaceTypeの設定
        /// </summary>
        private void LoadMiniFaceType_Terminal()
        {
            var nameArray = MasterDataManager.Instance.masterMiniFaceTypeData.GetFaceTypeNameArray();
            var faceTypeData = MasterDataManager.Instance.masterMiniFaceTypeData.Get(nameArray[_terminalMiniFacialIndex]);
            _miniFacialEyebrowLIndex = faceTypeData.EyebrowL;
            _miniFacialEyebrowRIndex = faceTypeData.EyebrowR;
            _miniFacialEyeLIndex = faceTypeData.EyeL;
            _miniFacialEyeRIndex = faceTypeData.EyeR;
            _miniFacialMouthIndex = faceTypeData.Mouth;
            _miniCheekIndex = faceTypeData.Cheek;
            SetMiniFacialEyebrow(_miniFacialEyebrowLIndex, _miniFacialEyebrowRIndex);
            SetMiniFacialEye(_miniFacialEyeLIndex, _miniFacialEyeRIndex);
            SetMiniFacialMouth(_miniFacialMouthIndex);
            SetMiniCheek(_miniCheekIndex - 1);
        }

        /// <summary>ミニ用フェイシャル：「全体」の現在アニメを取得</summary>
        public string MiniFacial_GetCurrentFacialAnimationName()
        {
            var faceTypeNameArray = MasterDataManager.Instance.masterMiniFaceTypeData.GetFaceTypeNameArray();
            int index = _terminalMiniFacialIndex;

            if (index >= faceTypeNameArray.Length)
                return string.Empty;

            return faceTypeNameArray[index];
        }

        /// <summary>ミニ用フェイシャル：「全体」のアニメ一覧の中から選択された時の処理</summary>
        public void MiniFacial_OnClickFacialAnimationListItem(int index)
        {
            _terminalDrivenKeyFacialTypeName = MasterDataManager.Instance.masterMiniFaceTypeData.GetFaceTypeNameArray();
            _terminalMiniFacialIndex = index;
            LoadMiniFaceType_Terminal();
        }

        /// <summary>ミニ用フェイシャル：「全体」の「次へ」ボタンが押された時の処理</summary>
        public void MiniFacial_OnClickFacialNextButton()
        {
            _terminalDrivenKeyFacialTypeName = MasterDataManager.Instance.masterMiniFaceTypeData.GetFaceTypeNameArray();
            _terminalMiniFacialIndex++;
            if (_terminalMiniFacialIndex >= _terminalDrivenKeyFacialTypeName.Length) _terminalMiniFacialIndex = _terminalDrivenKeyFacialTypeName.Length - 1;
            LoadMiniFaceType_Terminal();
        }

        /// <summary>ミニ用フェイシャル：「全体」の「前へ」ボタンが押された時の処理</summary>
        public void MiniFacial_OnClickFacialPrevButton()
        {
            _terminalDrivenKeyFacialTypeName = MasterDataManager.Instance.masterMiniFaceTypeData.GetFaceTypeNameArray();
            _terminalMiniFacialIndex--;
            if (_terminalMiniFacialIndex < 0) _terminalMiniFacialIndex = 0;
            LoadMiniFaceType_Terminal();
        }

        /// <summary>ミニ用フェイシャル：左眉の設定が変更された時の処理</summary>
        public void MiniFacial_OnChangedEyebrowL(int index)
        {
            _miniFacialEyebrowLIndex = index;
            SetMiniFacialEyebrow(_miniFacialEyebrowLIndex, -1);
        }

        /// <summary>ミニ用フェイシャル：右眉の設定が変更された時の処理</summary>
        public void MiniFacial_OnChangedEyebrowR(int index)
        {
            _miniFacialEyebrowRIndex = index;
            SetMiniFacialEyebrow(-1, _miniFacialEyebrowRIndex);
        }

        /// <summary>ミニ用フェイシャル：左目の設定が変更された時の処理</summary>
        public void MiniFacial_OnChangedEyeL(int index)
        {
            _miniFacialEyeLIndex = index;
            SetMiniFacialEye(_miniFacialEyeLIndex, -1);
        }

        /// <summary>ミニ用フェイシャル：右目の設定が変更された時の処理</summary>
        public void MiniFacial_OnChangedEyeR(int index)
        {
            _miniFacialEyeRIndex = index;
            SetMiniFacialEye(-1, _miniFacialEyeRIndex);
        }

        /// <summary>ミニ用フェイシャル：口の設定が変更された時の処理</summary>
        public void MiniFacial_OnChangedMouth(int index)
        {
            _miniFacialMouthIndex = index;
            SetMiniFacialMouth(_miniFacialMouthIndex);
        }

        /// <summary>ミニ用フェイシャル：頬の設定が変更された時の処理</summary>
        public void MiniFacial_OnChangedCheek(int index)
        {
            _miniCheekIndex = index;
            SetMiniCheek(_miniCheekIndex - 1);
        }

        private void OnGUI_terminal_MobModel()
        {
            switch (_terminateSubMode)
            {
                case 0: //モデル選択
                    OnGUI_terminal_MobModel_Model();
                    break;
                case 1://衣装
                    OnGUI_Terminal_Model_Dress();
                    break;
                case 2: //モード選択
                    OnGUI_Terminal_Model_Mode();
                    break;
                case 3: //ロード
                    _terminalRequestBuildModel = true;
                    SetTerminalMode(TerminateMode.Top);
                    break;
            }
        }
        private void OnGUI_terminal_MobModel_Model()
        {
            Terminal_UnlockCameraControll();

            GUILayout.BeginVertical(TERMINAL_TEXTAREA_WIDTH);

            GUILayout.Space(10.0f);
            GUILayout.Label("モブIDを使用する：", _terminalStyle_label20);
            _isTerminalMobUseMobId = GUILayout.Toggle(_isTerminalMobUseMobId, "モブIDを使用する：", _terminalStyle_toggle20);
            if (_isTerminalMobUseMobId)
            {
                GUILayout.Space(16.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("モブID：", _terminalStyle_label20);
                var mobId = GUILayout.TextField(_terminalMobId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(mobId, out var result))
                {
                    _terminalMobId = result;
                }
                GUILayout.EndHorizontal();
            }
            else
            {
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("顔：", _terminalStyle_label20);
                var face = GUILayout.TextField(_terminalMobFaceNo.ToString(), _terminalStyle_textField24);
                if (int.TryParse(face, out var result))
                {
                    _terminalMobFaceNo = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("髪型：", _terminalStyle_label20);
                var hair = GUILayout.TextField(_terminalMobHairNo.ToString(), _terminalStyle_textField24);
                if (int.TryParse(hair, out result))
                {
                    _terminalMobHairNo = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("髪色：", _terminalStyle_label20);
                var hairColor = GUILayout.TextField(_terminalMobHairColorNo.ToString(), _terminalStyle_textField24);
                if (int.TryParse(hairColor, out result))
                {
                    _terminalMobHairColorNo = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("肌色：", _terminalStyle_label20);
                var skin = GUILayout.TextField(_terminalMobHairSkinNo.ToString(), _terminalStyle_textField24);
                if (int.TryParse(skin, out result))
                {
                    _terminalMobHairSkinNo = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("DressColorSetId：", _terminalStyle_label20);
                var dressColorSetId = GUILayout.TextField(_terminalMobDressColorSetId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(dressColorSetId, out result))
                {
                    _terminalMobDressColorSetId = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("HairCutoff：", _terminalStyle_label20);
                var hairCutoffText = GUILayout.TextField(_terminalMobHairCutoff.ToString(), _terminalStyle_textField24);
                float hairCutoff = 0.5f;
                if (float.TryParse(hairCutoffText, out hairCutoff))
                {
                    _terminalMobHairCutoff = hairCutoff;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("AttachModelId：", _terminalStyle_label20);
                var attachId = GUILayout.TextField(_terminalMobAttachId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(attachId, out result))
                {
                    _terminalMobAttachId = result;
                }
                GUILayout.EndHorizontal();
            }

            GUILayout.Space(10.0f);
            if (GUILayout.Button("決定", _terminalStyle_buttonMid20))
            {
                _terminalMobBuildInfo.faceKindId = _terminalMobFaceNo;
                _terminalMobBuildInfo.hairColorId = _terminalMobHairColorNo;
                _terminalMobBuildInfo.hairKindId = _terminalMobHairNo;
                _terminalMobBuildInfo.DressColorId = _terminalMobDressColorSetId;
                _terminalMobBuildInfo.HairCutoff = _terminalMobHairCutoff;
                SetAttachmentModelId(_terminalMobAttachId);
                SetSkin(ModelLoader.SkinType.White + _terminalMobHairSkinNo);
                _terminalMobBuildModel = true;
                _isTerminalAudienceBuildModel = false;
                _terminateSubMode++;
                _terminalDressNameArray = null;
            }

            GUILayout.Space(10.0f);
            if (GUILayout.Button("戻る", _terminalStyle_buttonMid20))
            {
                //戻り先はキャラ
                SetTerminalMode(TerminateMode.Model);
            }
            GUILayout.EndVertical();

            if (GUI.changed)
            {
                Terminal_LockCameraControll();
            }
        }
        private void OnGUI_terminal_AudienceModel()
        {
            switch (_terminateSubMode)
            {
                case 0: //モデル選択
                    OnGUI_terminal_AudienceModel_Model();
                    break;
                case 1://衣装
                    OnGUI_Terminal_Model_Dress();
                    break;
                case 2: //モード選択
                    OnGUI_Terminal_Model_Mode();
                    break;
                case 3: //ロード
                    _terminalRequestBuildModel = true;
                    SetTerminalMode(TerminateMode.Top);
                    break;
            }
        }
        private void OnGUI_terminal_AudienceModel_Model()
        {
            Terminal_UnlockCameraControll();

            GUILayout.BeginVertical(TERMINAL_TEXTAREA_WIDTH);

            GUILayout.Space(10.0f);
            GUILayout.Label("観客IDを使用する：", _terminalStyle_label20);
            _isTerminalAudienceUseMobId = GUILayout.Toggle(_isTerminalAudienceUseMobId, "観客IDを使用する：", _terminalStyle_toggle20);
            if (_isTerminalAudienceUseMobId)
            {
                GUILayout.Space(16.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("観客ID：", _terminalStyle_label20);
                var audienceId = GUILayout.TextField(_terminalAudienceId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(audienceId, out var result))
                {
                    _terminalAudienceId = result;
                }
                GUILayout.EndHorizontal();
            }
            else
            {
                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("顔：", _terminalStyle_label20);
                var face = GUILayout.TextField(_terminalAudienceBuildInfo.faceKindId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(face, out var result))
                {
                    _terminalAudienceBuildInfo.faceKindId = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("髪型：", _terminalStyle_label20);
                var hair = GUILayout.TextField(_terminalAudienceBuildInfo.hairKindId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(hair, out result))
                {
                    _terminalAudienceBuildInfo.hairKindId = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("髪色：", _terminalStyle_label20);
                var hairColor = GUILayout.TextField(_terminalAudienceBuildInfo.hairColorId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(hairColor, out result))
                {
                    _terminalAudienceBuildInfo.hairColorId = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("DressColorSetId：", _terminalStyle_label20);
                var dressColorSetId = GUILayout.TextField(_terminalAudienceBuildInfo.DressColorId.ToString(), _terminalStyle_textField24);
                if (int.TryParse(dressColorSetId, out result))
                {
                    _terminalAudienceBuildInfo.DressColorId = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("バスト：", _terminalStyle_label20);
                var bust = GUILayout.TextField(_terminalAudienceBustIndex.ToString(), _terminalStyle_textField24);
                if (int.TryParse(bust, out result))
                {
                    _terminalAudienceBustIndex = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("肌色：", _terminalStyle_label20);
                var skin = GUILayout.TextField(_terminalAudienceSkinIndex.ToString(), _terminalStyle_textField24);
                if (int.TryParse(skin, out result))
                {
                    _terminalAudienceSkinIndex = result;
                }
                GUILayout.EndHorizontal();

                GUILayout.Space(10.0f);
                GUILayout.BeginHorizontal();
                GUILayout.Label("体型：", _terminalStyle_label20);
                var shape = GUILayout.TextField(_terminalAudienceShapeIndex.ToString(), _terminalStyle_textField24);
                if (int.TryParse(shape, out result))
                {
                    _terminalAudienceShapeIndex = result;
                }
                GUILayout.EndHorizontal();
            }

            GUILayout.Space(10.0f);
            if (GUILayout.Button("決定", _terminalStyle_buttonMid20))
            {
                SetBust(ModelLoader.BustType.SS + _terminalAudienceBustIndex);
                SetSkin(ModelLoader.SkinType.White + _terminalAudienceSkinIndex);
                SetBodySize(ModelLoader.BodySize.M + _terminalAudienceShapeIndex);
                SetHeight(ModelLoader.HeightType.M);
                SetSexType(ModelLoader.GenderType.Male);
                _terminalMobBuildModel = false;
                _isTerminalAudienceBuildModel = true;
                _terminateSubMode++;
                _terminalDressNameArray = null;
            }

            GUILayout.Space(10.0f);
            if (GUILayout.Button("戻る", _terminalStyle_buttonMid20))
            {
                //戻り先はキャラ
                SetTerminalMode(TerminateMode.Model);
            }
            GUILayout.EndVertical();

            if (GUI.changed)
            {
                Terminal_LockCameraControll();
            }
        }

        /// <summary>モデル→観衆：「観衆」ダイアログの決定ボタンを押した時の処理</summary>
        public void Model_OnDecideAudienceDialog(DialogCharaViewerAudience.Param param)
        {
            _isTerminalAudienceUseMobId = param.IsUseAudienceId;
            _terminalAudienceId = param.AudienceId;
            _terminalAudienceBuildInfo.faceKindId = param.FaceNo;
            _terminalAudienceBuildInfo.hairKindId = param.HairNo;
            _terminalAudienceBuildInfo.hairColorId = param.HairColorNo;
            _terminalAudienceBuildInfo.DressColorId = param.DressColorSetId;
            _terminalAudienceBustIndex = param.BustNo;
            _terminalAudienceSkinIndex = param.SkinNo;
            _terminalAudienceShapeIndex = param.ShapeNo;

            SetBust(ModelLoader.BustType.SS + _terminalAudienceBustIndex);
            SetSkin(ModelLoader.SkinType.White + _terminalAudienceSkinIndex);
            SetBodySize(ModelLoader.BodySize.M + _terminalAudienceShapeIndex);
            SetHeight(ModelLoader.HeightType.M);
            SetSexType(ModelLoader.GenderType.Male);
            _terminalMobBuildModel = false;
            _isTerminalAudienceBuildModel = true;
            _terminateSubMode++;
            _terminalDressNameArray = null;
        }


        /// <summary>部位：「髪を消す」チェックボックスが変更された時の処理</summary>
        public void ModelNode_OnChangedIsHairInvisible(bool isOn)
        {
            _modelNodeIsHairInvisible = isOn;
            ModelNode_ApplyVisibirity();
        }

        /// <summary>部位：「尻尾を消す」チェックボックスが変更された時の処理</summary>
        public void ModelNode_OnChangedIsTailInvisible(bool isOn)
        {
            _modelNodeIsTailInvisible = isOn;
            ModelNode_ApplyVisibirity();
        }

        private void ModelNode_ApplyVisibirity()
        {
            var hairTrans = ModelNode_FindModelNodeTransform(CharaNodeName.M_Hair);
            if (hairTrans != null)
            {
                hairTrans.SetActiveWithCheck(!_modelNodeIsHairInvisible);
            }

            var tailTrans = ModelNode_FindModelNodeTransform(CharaNodeName.M_TAIL);
            if (tailTrans != null)
            {
                tailTrans.SetActiveWithCheck(!_modelNodeIsTailInvisible);
            }
        }

        private Transform ModelNode_FindModelNodeTransform(string nodeName)
        {
            var ctrl = GetCharaViewerInfo(0).GetModelControllerBehaviour();
            if (ctrl == null)
                return null;

            return ctrl.FindTransform(nodeName);
        }


        public enum CameraFollowTarget
        {
            None = 0,
            Position,
            Hip,
            Prop,
            Head,
        };
        public static readonly string[] CameraFollowTargetNameArray = new string[]
        {
            "追従しない","Position追従","Hip追従","Prop追従","Head追従"
        };
        public CameraFollowTarget CameraFollowChara = CameraFollowTarget.None;
        private Vector3 _cameraFollowPosition;

        private void OnGUI_terminal_CameraReset()
        {
            GUILayout.BeginArea(new Rect(_terminalDrawRect.width - _terminalStyle_buttonMid20.fixedWidth, 10f, _terminalStyle_buttonMid20.fixedWidth, 500f));
            if (GUILayout.Button("カメラリセット", _terminalStyle_buttonMid20))
            {
                OnClickCameraResetButton();
            }
            if (GUILayout.Button("カメラリセット(正面)", _terminalStyle_buttonMid20))
            {
                OnClickCameraResetFrontButton();
            }
            if (CameraFollowChara == CameraFollowTarget.None)
            {
                if (GUILayout.Button(CameraFollowTargetNameArray[(int)CameraFollowTarget.None], _terminalStyle_buttonMid20))
                {
                    OnClickFollowNoneButton();
                }
            }
            else if (CameraFollowChara == CameraFollowTarget.Position)
            {
                if (GUILayout.Button(CameraFollowTargetNameArray[(int)CameraFollowTarget.Position], _terminalStyle_buttonMid20))
                {
                    OnClickFollowPositionButton();
                }
            }
            else if (CameraFollowChara == CameraFollowTarget.Hip)
            {
                if (GUILayout.Button(CameraFollowTargetNameArray[(int)CameraFollowTarget.Hip], _terminalStyle_buttonMid20))
                {
                    OnClickFollowHipButton();
                }
            }
            else if (CameraFollowChara == CameraFollowTarget.Prop)
            {
                if (GUILayout.Button(CameraFollowTargetNameArray[(int)CameraFollowTarget.Prop], TERMINAL_BUTTON_HEIGHT))
                {
                    OnClickFollowPropButton();
                }
            }
            else if (CameraFollowChara == CameraFollowTarget.Head)
            {
                if (GUILayout.Button(CameraFollowTargetNameArray[(int)CameraFollowTarget.Head], TERMINAL_BUTTON_HEIGHT))
                {
                    OnClickFollowHeadButton();
                }
            }
            if (GUILayout.Button("横並び", _terminalStyle_buttonMid20))
            {
                OnClickHorizontalInlineButton();
            }
            if (GUILayout.Button("顔並べる", _terminalStyle_buttonMid20))
            {
                OnClickFaceInlineButton();
            }
            if (GUILayout.Button("近づける", _terminalStyle_buttonMid20))
            {
                OnClickCloseToButton();
            }
            GUILayout.EndArea();
        }

        /// <summary>「カメラリセット」ボタン押下時処理</summary>
        public void OnClickCameraResetButton()
        {
            if (_isRequierAlwaysLockCamera == false)
                CameraReset();
        }

        /// <summary>「カメラリセット(正面)」ボタン押下時処理</summary>
        public void OnClickCameraResetFrontButton()
        {
            if (_isRequierAlwaysLockCamera == false)
                CameraResetFront();
        }

        /// <summary>「追従しない」ボタン押下時処理</summary>
        public void OnClickFollowNoneButton()
        {
            CameraFollowChara = CameraFollowTarget.Position;
            CameraReset();
        }

        /// <summary>「Position追従」ボタン押下時処理</summary>
        public void OnClickFollowPositionButton()
        {
            CameraFollowChara = CameraFollowTarget.Hip;
            CameraReset();
        }

        /// <summary>「Hip追従」ボタン押下時処理</summary>
        public void OnClickFollowHipButton()
        {
            CameraFollowChara = CameraFollowTarget.Prop;
            CameraReset();
        }

        /// <summary>「Prop追従」ボタン押下時処理</summary>
        public void OnClickFollowPropButton()
        {
            CameraFollowChara = CameraFollowTarget.Head;
            CameraReset();
        }

        /// <summary>「Head追従」ボタン押下時処理</summary>
        public void OnClickFollowHeadButton()
        {
            CameraFollowChara = CameraFollowTarget.None;
            CameraReset();
        }

        /// <summary>「横並び」ボタン押下時処理</summary>
        public void OnClickHorizontalInlineButton()
        {
            SetAscPosition();
        }

        /// <summary>「顔並べる」ボタン押下時処理</summary>
        public void OnClickFaceInlineButton()
        {
            SetHeadAlign();
        }

        /// <summary>「近づける」ボタン押下時処理</summary>
        public void OnClickCloseToButton()
        {
            SetFaceToFace();
        }


        public void CameraReset()
        {
            FollowChara(CameraFollowChara);
            var charaViewerCamera = _mainCamera.GetComponent<CharaViewer3DCamera>();
            if (charaViewerCamera != null)
            {
                charaViewerCamera.offsetPosition = Math.VECTOR3_ZERO;
                charaViewerCamera.Reset();
                if (IsTerminal)
                {// 実機の場合初期位置で全身見えるように

                    bool isMini = CharaViewer.Instance.GetCharaViewerInfo(0).IsMini;
                    float distance = isMini ? -4f : -3f;
                    charaViewerCamera.CameraDistance(distance);
                }
            }
        }

        public void CameraResetFront()
        {
            FollowChara(CameraFollowChara);
            var charaViewerCamera = _mainCamera.GetComponent<CharaViewer3DCamera>();
            if (charaViewerCamera != null)
            {
#if UNITY_EDITOR
                // カメラリセット時の画角が変わらないようEditorでは従来の位置を設定する
                charaViewerCamera.offsetPosition = Math.VECTOR3_ZERO;
#else
                charaViewerCamera.offsetPosition = new Vector3(0f, -0.5f, 0f);
#endif
                charaViewerCamera.ResetFront();
                
                var dist = 0f;

#if UNITY_EDITOR
                var viewerWindow = CharaViewerWindow.Instance;
                var propInfo = GetCameraTargetPropInfo();
                if (propInfo != null && propInfo.PropObject != null)
                {
                    if (!viewerWindow.VisibleChara)
                    {
                        dist = GetPropDistanceOnResetFront(charaViewerCamera, propInfo);
                    }
                    else
                    {
                        dist = GetCharaAndPropDistanceOnResetFront(charaViewerCamera, propInfo);
                    }
                }
                else
#endif
                {
                    dist = GetCharaDistanceOnResetFront(charaViewerCamera);
                }

                charaViewerCamera.CameraDistance(dist);

                if (IsTerminal)
                {// 実機の場合初期位置で全身見えるように

                    bool isMini = CharaViewer.Instance.GetCharaViewerInfo(0).IsMini;
                    float distance = isMini ? -3f : -1f;
                    charaViewerCamera.CameraDistance(distance);
                }
            }
        }

        private float GetCharaWidth()
        {
            return GetCharaWidth(GetCharaViewerInfo(0).IsMini);
        }

        private static float GetCharaWidth(bool isMini) => isMini ? MINI_CHARA_MAX_WIDTH : CHARA_MAX_WIDTH;

        private float GetCharaHeight()
        {
            return GetCharaHeight(GetCharaViewerInfo(0).IsMini);
        }

        private static float GetCharaHeight(bool isMini) => isMini ? MINI_CHARA_MAX_HEIGHT : CHARA_MAX_HEIGHT;

        /// <summary>
        /// キャラがカメラ内に収まる距離
        /// </summary>
        private float GetCharaDistanceOnResetFront(CharaViewer3DCamera camera)
        {
            return GetCharaDistanceOnResetFront(camera, GetCharaViewerInfo(0).IsMini);
        }

        /// <summary>
        /// キャラがカメラ内に収まる距離 (外部からも利用したい時はこちら)
        /// </summary>
        public static float GetCharaDistanceOnResetFront(CharaViewer3DCamera camera, bool isMini)
        {
            Vector3 maxLength = Math.VECTOR3_ZERO;
            maxLength.x = GetCharaWidth(isMini) * 0.5f;
            maxLength.y = GetCharaHeight(isMini) * 0.5f;

            // キャラはZ方向はXYに比べると明らかに短いため考えなくていい

            // X方向に一番大きいポイントがカメラに収まる距離とY方向に一番大きいポイントがカメラに収まる距離を算出して大きい方を取得
            var dist = GetLongestHVDistance(camera, maxLength.x, maxLength.y, 0f, 0f);

            // 差分を返す
            return camera.Distance - dist;
        }

        /// <summary>
        /// 小物がカメラ内に収まる距離
        /// </summary>
        private float GetPropDistanceOnResetFront(CharaViewer3DCamera camera, PropInfo propInfo)
        {
            var dist = GetDistanceOnResetFront(camera, propInfo, false);

            return dist;
        }

        /// <summary>
        /// キャラと小物の両方ともカメラ内に収まる距離
        /// </summary>
        private float GetCharaAndPropDistanceOnResetFront(CharaViewer3DCamera camera, PropInfo propInfo)
        {
            var dist = GetDistanceOnResetFront(camera, propInfo, true);

            return dist;
        }

        private float GetDistanceOnResetFront(CharaViewer3DCamera camera, PropInfo propInfo, bool withChara)
        {
            var cameraScale = _cameraRoot.localScale.x;
            var center = GetCenterPosition(propInfo, withChara);
            Vector3 lookAt = (propInfo.PropObject.transform.TransformPoint(center) - _cameraFollowPosition) * cameraScale;
            camera.SetTargetLookAtPosition(lookAt, 1f);

            // 小物の回転値
            var q = propInfo.PropObject.transform.localRotation;

            var dist = GetCameraDistance(camera, propInfo, center, q, withChara);
            dist = camera.Distance - dist;

            return dist;
        }

        private float GetCameraDistance(CharaViewer3DCamera camera, PropInfo propInfo, Vector3 center, Quaternion q, bool withChara, float distOffset = 0.03f)
        {
            var rendererNum = propInfo.PropCtrl.GetRendererNum();
            var cameraScale = _cameraRoot.localScale.x;
            var lookAt = camera.TargetLookAtPosition;
            _pointList.Clear();

            for (int i = 0; i < rendererNum; i++)
            {
                var mesh = propInfo.PropCtrl.GetMesh(i);
                if (mesh == null)
                {
                    continue;
                }
                var vertexList = mesh.vertices.ToList();

                // 全頂点を小物の中心を原点とした座標系に変換
                foreach (var vertex in vertexList)
                {
                    var point = vertex;
                    point -= center;
                    point = q * point;
                    _pointList.Add(point);
                }
            }

            var distWidth = GetHorizontalDistance(camera);
            var distHeight = GetVerticalDistance(camera);
            var dist = Mathf.Max(distWidth, distHeight);

            // ワールド座標に変換
            var world = LocalToWorldPosition(new Vector3(0f, 0f, dist), propInfo, center, cameraScale);
            dist = world.z - lookAt.z;

            if (withChara)
            {
                var w = GetCharaWidth() * 0.5f;
                var distX = GetHorizontalDistance(camera, w, -w, 0f, 0f, lookAt.x, lookAt.z);

                var h = GetCharaHeight();
                var distY = GetVerticalDistance(camera, h, 0f, 0f, 0f, lookAt.y, lookAt.z);

                dist = Mathf.Max(dist, Mathf.Max(distX, distY));
            }

            // ニアクリップ内に入ってしまった分を調整
            var max = _pointList.OrderByDescending(vert => vert.z).ThenByDescending(vert => vert.y).ToList()[0];
            var maxDepth = LocalToWorldPosition(max, propInfo, center, cameraScale).z;
            dist = GetAdjustDistance(camera, dist, maxDepth - lookAt.z, distOffset);

            return dist;
        }

        private Vector3 LocalToWorldPosition(Vector3 local, PropInfo propInfo, Vector3 center, float cameraScale)
        {
            local = Quaternion.Inverse(propInfo.PropObject.transform.localRotation) * local + center;   // 元の座標系に戻す
            var world = (propInfo.PropObject.transform.TransformPoint(local) - _cameraFollowPosition) * cameraScale;

            return world;
        }

        /// <summary>
        /// ニアクリップ内に入ってしまった分を調整
        /// </summary>
        private float GetAdjustDistance(CharaViewer3DCamera camera, float baseDist, float lenZ, float distOffset)
        {
            var dist = baseDist;
            var diff = dist - lenZ;
            dist += distOffset;  // 若干見切れる場合があるので少しカメラを引く

            if (diff < camera.myCamera.nearClipPlane)
            {// ニアクリップ内に入ってる場合はその分カメラを引く
                dist += camera.myCamera.nearClipPlane - diff;        // ニアクリップ分ジャストだと表示されないのでさらに加算
            }

            return dist;
        }

        /// <summary>
        /// 横方向がすべて収まるカメラ距離と縦方向がすべて収まる距離を算出して大きい方を返す
        /// </summary>
        private static float GetLongestHVDistance(CharaViewer3DCamera camera, float lenX, float lenY, float hZ, float vZ)
        {
            var hDist = hZ;
            hDist += CalcHorizontalDistance(camera, lenX);

            var vDist = vZ;
            vDist += CalcVerticalDistance(camera, lenY);

            var dist = Mathf.Max(hDist, vDist);

            return dist;
        }

        /// <summary>
        /// 横方向がすべて収まるカメラ距離
        /// </summary>
        private static float CalcHorizontalDistance(CharaViewer3DCamera camera, float len)
        {
            var dist = 0f;
            if (!Math.IsFloatEqual(len, 0f))
            {
                var hfv = Camera.VerticalToHorizontalFieldOfView(camera.myCamera.fieldOfView, camera.myCamera.aspect);     // 横画角
                var tan = Mathf.Tan(Mathf.Deg2Rad * hfv * 0.5f);
                dist += len / tan;
            }
            return dist;
        }

        /// <summary>
        /// 横方向がすべて収まるカメラ距離(キャラ)
        /// </summary>
        private float GetHorizontalDistance(CharaViewer3DCamera camera, float maxX, float minX, float maxXZ, float minXZ, float centerX, float centerZ)
        {
            var len = Mathf.Abs(maxX - centerX);
            var dist1 = maxXZ - centerZ;
            dist1 += CalcHorizontalDistance(camera, len);

            len = Mathf.Abs(minX - centerX);
            var dist2 = minXZ - centerZ;
            dist2 += CalcHorizontalDistance(camera, len);

            return Mathf.Max(dist1, dist2);
        }

        /// <summary>
        /// 横方向がすべて収まるカメラ距離(プロップ)
        /// </summary>
        private float GetHorizontalDistance(CharaViewer3DCamera camera)
        {
            float dist = 0f;
            foreach (var point in _pointList)
            {
                var temp = CalcHorizontalDistance(camera, Mathf.Abs(point.x)) + point.z;
                dist = Mathf.Max(dist, temp);
            }
            return dist;
        }

        /// <summary>
        /// 縦方向がすべて収まるカメラ距離
        /// </summary>
        private static float CalcVerticalDistance(CharaViewer3DCamera camera, float len)
        {
            var dist = 0f;
            if (!Math.IsFloatEqual(len, 0f))
            {
                var vfv = camera.myCamera.fieldOfView;
                var tan = Mathf.Tan(Mathf.Deg2Rad * vfv * 0.5f);
                dist += len / tan;
            }
            return dist;
        }

        /// <summary>
        /// 縦方向がすべて収まるカメラ距離(キャラ)
        /// </summary>
        private float GetVerticalDistance(CharaViewer3DCamera camera, float maxY, float minY, float maxYZ, float minYZ, float centerY, float centerZ)
        {
            var len = Mathf.Abs(maxY - centerY);
            var dist1 = maxYZ - centerZ;
            dist1 += CalcVerticalDistance(camera, len);

            len = Mathf.Abs(minY - centerY);
            var dist2 = minYZ - centerZ;
            dist2 += CalcVerticalDistance(camera, len);

            return Mathf.Max(dist1, dist2);
        }

        /// <summary>
        /// 縦方向がすべて収まるカメラ距離(プロップ)
        /// </summary>
        private float GetVerticalDistance(CharaViewer3DCamera camera)
        {
            float dist = 0f;
            foreach (var point in _pointList)
            {
                var temp = CalcVerticalDistance(camera, Mathf.Abs(point.y)) + point.z;
                dist = Mathf.Max(dist, temp);
            }
            return dist;
        }

        /// <summary>
        /// 中心
        /// </summary>
        private Vector3 GetCenterPosition(PropInfo propInfo, bool withChara)
        {
            var rendererNum = propInfo.PropCtrl.GetRendererNum();
            var center = Math.VECTOR3_ZERO;
            var max = Math.VECTOR3_ZERO;
            var min = Math.VECTOR3_ZERO;

            for (int i = 0; i < rendererNum; i++)
            {
                var mesh = propInfo.PropCtrl.GetMesh(i);
                if (mesh == null)
                {
                    continue;
                }

                var vertexList = mesh.vertices.ToList();
                (max.x, min.x) = GetMinMaxPointX(vertexList, max.x, min.x);
                (max.y, min.y) = GetMinMaxPointY(vertexList, max.y, min.y);
                (max.z, min.z) = GetMinMaxPointZ(vertexList, max.z, min.z);
            }
            center.x = GetCenterX(max.x, min.x, withChara, propInfo.PropObject.transform.position.x);
            center.y = GetCenterY(max.y, min.y, withChara, propInfo.PropObject.transform.position.y);
            center.z = (max.z + min.z) * 0.5f;

            return center;
        }

        private (float, float) GetMinMaxPointX(List<Vector3> vertexList, float max, float min)
        {
            var tempMax = vertexList.OrderByDescending(vert => vert.x).ThenByDescending(vert => vert.z).ToList()[0];
            var tempMin = vertexList.OrderBy(vert => vert.x).ThenByDescending(vert => vert.z).ToList()[0];

            max = Mathf.Max(max, tempMax.x);
            min = Mathf.Min(min, tempMin.x);

            return (max, min);
        }
        private (float, float) GetMinMaxPointY(List<Vector3> vertexList, float max, float min)
        {
            var tempMax = vertexList.OrderByDescending(vert => vert.y).ThenByDescending(vert => vert.z).ToList()[0];
            var tempMin = vertexList.OrderBy(vert => vert.y).ThenByDescending(vert => vert.z).ToList()[0];

            max = Mathf.Max(max, tempMax.y);
            min = Mathf.Min(min, tempMin.y);

            return (max, min);
        }
        private (float, float) GetMinMaxPointZ(List<Vector3> vertexList, float max, float min)
        {
            var tempMax = vertexList.OrderByDescending(vert => vert.z).ThenByDescending(vert => vert.y).ToList()[0];
            var tempMin = vertexList.OrderBy(vert => vert.z).ThenByDescending(vert => vert.y).ToList()[0];

            max = Mathf.Max(max, tempMax.z);
            min = Mathf.Min(min, tempMin.z);

            return (max, min);
        }

        /// <summary>
        /// 中心点X
        /// </summary>
        private float GetCenterX(float maxPoint, float minPoint, bool withChara, float propPosition)
        {
            if (withChara)
            {
                var charaHalfWidth = GetCharaWidth() * 0.5f;
                maxPoint = Mathf.Max(maxPoint, charaHalfWidth - propPosition);
                minPoint = Mathf.Min(minPoint, -charaHalfWidth - propPosition);
            }

            return (maxPoint + minPoint) * 0.5f;
        }

        /// <summary>
        /// 中心点Y
        /// </summary>
        private float GetCenterY(float maxPoint, float minPoint, bool withChara, float propPosition)
        {
            if (withChara)
            {
                maxPoint = Mathf.Max(maxPoint, GetCharaHeight() - propPosition);
                minPoint = Mathf.Min(minPoint, -propPosition);
            }

            return (maxPoint + minPoint) * 0.5f;
        }

        private Transform ParentTransform(CameraFollowTarget target)
        {
            var ctrl = GetCharaViewerInfo(0).GetModelControllerBehaviour();
            if (ctrl == null)
            {
                return this.transform;
            }

            switch (target)
            {
                case CameraFollowTarget.None:
                    return this.transform;
                case CameraFollowTarget.Position:
                    {
                        return ctrl.FindTransform(CharaNodeName.Position);
                    }
                case CameraFollowTarget.Hip:
                    {
                        return ctrl.FindTransform(CharaNodeName.Hip);
                    }
                case CameraFollowTarget.Prop:
                    {
                        var propInfo = GetCameraTargetPropInfo();
                        if (propInfo != null && propInfo.PropObject != null)
                        {
                            return propInfo.PropObject.transform;
                        }
                        else
                        {
                            // 追従する場所をアタッチ先に応じて変更する
                            string attachBoneName = CharaNodeName.Hand_Attach_R;
                            if (_attachID < _attachBoneNameArray.Length)
                            {
                                attachBoneName = _attachBoneNameArray[_attachID];
                            }
                            return ctrl.FindTransform(attachBoneName);
                        }
                    }
                case CameraFollowTarget.Head:
                    {
                        return ctrl.FindTransform(CharaNodeName.Head);
                    }
            }

            return this.transform;
        }

        public void FollowChara(CameraFollowTarget target)
        {
            var charaViewerCamera = _mainCamera.GetComponent<CharaViewer3DCamera>();
            if (charaViewerCamera == null)
            {
                return;
            }

            // 1体目のキャラ（0番は必ずある想定）
            ModelController modelController = GetCharaViewerInfo(0).ModelController;
            MiniModelController miniModelController = GetCharaViewerInfo(0).MiniModelController;
            var parentTransform = ParentTransform(target);

            if (target == CameraFollowTarget.None)
            {
                Vector3 lookAtPos = (modelController != null) ? CharaViewer3DCamera.DEFAULT_TARGET_LOOK_AT_POSITION : Math.VECTOR3_ZERO;
                charaViewerCamera.SetTargetLookAtPosition(lookAtPos, 1f);
                _cameraRoot.SetParent(parentTransform, true);
                _cameraFollowPosition = this.transform.position;
            }
            else if (target == CameraFollowTarget.Position)
            {
                Vector3 lookAtPos = (modelController != null) ? CharaViewer3DCamera.DEFAULT_TARGET_LOOK_AT_POSITION : Math.VECTOR3_ZERO;

                if (modelController != null)
                {
                    lookAtPos.y = CHARA_MAX_HEIGHT * 0.5f;
                }
                else if (miniModelController != null)
                {
                    lookAtPos.y = MINI_CHARA_MAX_HEIGHT * 0.5f;
                }

                charaViewerCamera.SetTargetLookAtPosition(lookAtPos, 1f);
                var position = parentTransform;
                _cameraRoot.SetParent(position, true);
                _cameraFollowPosition = position.transform.position;
            }
            else if (target == CameraFollowTarget.Hip)
            {
                charaViewerCamera.SetTargetLookAtPosition(Math.VECTOR3_ZERO, 1f);
                var hip = parentTransform;
                _cameraRoot.SetParent(hip, true);
                _cameraFollowPosition = hip.transform.position;
            }
            else if (target == CameraFollowTarget.Prop)
            {
                charaViewerCamera.SetTargetLookAtPosition(Math.VECTOR3_ZERO, 1f);
                var propInfo = GetCameraTargetPropInfo();
                if (propInfo != null && propInfo.PropObject != null)
                {
                    _cameraRoot.SetParent(propInfo.PropObject.transform, true);
                    _cameraFollowPosition = propInfo.PropObject.transform.position;
                }
                else
                {
                    var attach = parentTransform;
                    if (attach != null)
                    {
                        _cameraRoot.SetParent(attach, true);
                        _cameraFollowPosition = attach.transform.position;
                    }
                    else
                    {
                        _cameraRoot.SetParent(this.transform, true);
                        _cameraFollowPosition = this.transform.position;
                    }
                }
                charaViewerCamera.ResetFront();
            }
            else if (target == CameraFollowTarget.Head)
            {
                charaViewerCamera.SetTargetLookAtPosition(Math.VECTOR3_ZERO, 1f);
                var Head = parentTransform;
                _cameraRoot.SetParent(Head, true);
                _cameraFollowPosition = Head.transform.position;
            }
        }

        private void OnGUI_terminal_ShowGUI()
        {
            GUILayout.BeginArea(new Rect(_terminalDrawRect.width - 200f, _terminalDrawRect.height - _terminalButtonHeightFloat, 200f, _terminalButtonHeightFloat));
            {
                if (GUILayout.Button(_isShowTerminalGUI ? "非表示" : "表示", TERMINAL_BUTTON_HEIGHT))
                {
                    OnClickShowButton();
                }
            }
            GUILayout.EndArea();
        }

        /// <summary>下部の「表示」ボタン/「非表示」ボタン押下時処理</summary>
        public void OnClickShowButton()
        {
            _isShowTerminalGUI = !_isShowTerminalGUI;
        }

        private void OnGUI_Terminal()
        {
            //モデル生成中はOnGUIを走らせない
            if (_terminalBuildModel || _terminalRequestBuildModel)
                return;
            //Viewがアクティブな時はOnGUIを走らせない
            if (IsViewActive)
                return;

            OnGUI_CreateTerminalStyle();

            GUILayout.BeginArea(_terminalDrawRect);

            if (_isShowTerminalGUI == true)
            {
                GUILayout.BeginArea(new Rect(0.0f, 0.0f, _terminalDrawRect.width - 200.0f, _terminalDrawRect.height));
                {
                    GUILayout.Space(10.0f);

                    switch (_terminateMode)
                    {
                        case TerminateMode.Top:
                            OnGUI_Terminal_Top();
                            break;
                        case TerminateMode.Slot:
                            OnGUI_Terminal_Slot();
                            break;
                        case TerminateMode.Model:
                            OnGUI_Terminal_Model();
                            break;
                        case TerminateMode.Animation:
                            OnGUI_terminal_Animation();
                            break;
                        case TerminateMode.Cloth:
                            OnGUI_terminal_Cloth();
                            break;
                        case TerminateMode.Facial:
                            OnGUI_terminal_Facial();
                            break;
                        case TerminateMode.MobModel:
                            OnGUI_terminal_MobModel();
                            break;
                        case TerminateMode.SimpleModel:
                            OnGUI_Terminal_SimpleModel();
                            break;
                        case TerminateMode.CharacterType:
                            OnGUI_Terminal_CharacterType();
                            break;
                        case TerminateMode.Prop:
                            OnGUI_Terminal_Prop();
                            break;
                        case TerminateMode.AudienceModel:
                            OnGUI_terminal_AudienceModel();
                            break;
                        case TerminateMode.MiniMobModel:
                            OnGUI_Terminal_MiniMobModel();
                            break;
                    }
                    OnGUI_Info();
                }
                GUILayout.EndArea();

                OnGUI_terminal_CameraReset();
            }

            OnGUI_terminal_ShowGUI();

            GUILayout.EndArea();
        }

        private const int TERMINAL_DEFAULT_SCREEN_WIDTH = 1080;
        private const int TERMINAL_DEFAULT_SCREEN_HEIGHT = 1920;
        private const int TERMINAL_DEFAULT_SCREEN_HEIGHT_OFFSET = 720;
        private GUILayoutOption _terminalFacialHorizontalAreaWidth = null;
        private GUILayoutOption _terminalClothHorizontalAreaWidth = null;
        private GUIStyle _terminalStyle_label32 = null;
        private GUIStyle _terminalStyle_label20 = null;
        private GUIStyle _terminalStyle_button20 = null;
        private GUIStyle _terminalStyle_buttonSmall20 = null;
        private GUIStyle _terminalStyle_buttonMid20 = null;
        private GUIStyle _terminalStyle_toggle20 = null;
        private GUIStyle _terminalStyle_verticalScrollbar50 = null;
        private GUIStyle _terminalStyle_horizontalSlider32 = null;
        private GUIStyle _terminalStyle_horizontalSliderThumb32 = null;
        private GUIStyle _terminalStyle_textField24 = null;

        private Rect _terminalDrawRect;
        private bool _isShowTerminalGUI = true;

        private void OnGUI_CreateTerminalStyle()
        {
            if (Screen.Width >= TERMINAL_DEFAULT_SCREEN_WIDTH && Screen.Height >= TERMINAL_DEFAULT_SCREEN_HEIGHT)
            {
                // 1080*1920を想定したサイズ。画面中央に1080*1920で表示する。
                _terminalDrawRect = new Rect((Screen.Width - TERMINAL_DEFAULT_SCREEN_WIDTH) / 2, (Screen.Height - TERMINAL_DEFAULT_SCREEN_HEIGHT) / 2 + TERMINAL_DEFAULT_SCREEN_HEIGHT_OFFSET, TERMINAL_DEFAULT_SCREEN_WIDTH, TERMINAL_DEFAULT_SCREEN_HEIGHT - TERMINAL_DEFAULT_SCREEN_HEIGHT_OFFSET);
                if (TERMINAL_BUTTON_WIDTH == null)
                {
                    TERMINAL_BUTTON_WIDTH = GUILayout.Width(160.0f);
                }
                if (TERMINAL_LIST_BUTTON_WIDTH == null)
                {
                    TERMINAL_LIST_BUTTON_WIDTH = GUILayout.Width(150.0f * 1.5f);
                }
                if (TERMINAL_BUTTON_HEIGHT == null)
                {
                    _terminalButtonHeightFloat = 50.0f * 1.5f;
                    TERMINAL_BUTTON_HEIGHT = GUILayout.Height(_terminalButtonHeightFloat);
                }
                if (TERMINAL_SLIDER_WIDTH == null)
                {
                    TERMINAL_SLIDER_WIDTH = GUILayout.Width(100.0f * 1.5f);
                }
                if (TERMINAL_TEXTAREA_WIDTH == null)
                {
                    TERMINAL_TEXTAREA_WIDTH = GUILayout.Width(200.0f * 1.5f);
                }
                if (_terminalFacialHorizontalAreaWidth == null)
                {
                    _terminalFacialHorizontalAreaWidth = GUILayout.Width(600.0f);
                }
                if (_terminalClothHorizontalAreaWidth == null)
                {
                    _terminalClothHorizontalAreaWidth = GUILayout.Width(600.0f);
                }
                if (_terminalStyle_label32 == null)
                {
                    _terminalStyle_label32 = new GUIStyle(GUI.skin.label);
                    _terminalStyle_label32.fontSize = 32;
                }
                if (_terminalStyle_label20 == null)
                {
                    _terminalStyle_label20 = new GUIStyle(GUI.skin.label);
                    _terminalStyle_label20.fontSize = 20;
                }
                if (_terminalStyle_button20 == null)
                {
                    _terminalStyle_button20 = new GUIStyle(GUI.skin.button);
                    _terminalStyle_button20.fontSize = 20;
                    _terminalStyle_button20.fixedWidth = 240.0f;
                    _terminalStyle_button20.fixedHeight = _terminalButtonHeightFloat;

                }
                if (_terminalStyle_buttonMid20 == null)
                {
                    _terminalStyle_buttonMid20 = new GUIStyle(GUI.skin.button);
                    _terminalStyle_buttonMid20.fontSize = 20;
                    _terminalStyle_buttonMid20.fixedWidth = 260.0f;
                    _terminalStyle_buttonMid20.fixedHeight = 60.0f;
                }
                if (_terminalStyle_toggle20 == null)
                {
                    _terminalStyle_toggle20 = new GUIStyle(GUI.skin.toggle);
                    _terminalStyle_toggle20.fontSize = 24;
                    _terminalStyle_toggle20.fixedHeight = 32;
                }
                if (_terminalStyle_verticalScrollbar50 == null)
                {
                    _terminalStyle_verticalScrollbar50 = new GUIStyle(GUI.skin.verticalScrollbar);
                    _terminalStyle_verticalScrollbar50.fixedWidth = 50f;
                }
                if (_terminalStyle_horizontalSlider32 == null)
                {
                    _terminalStyle_horizontalSlider32 = new GUIStyle(GUI.skin.horizontalSlider);
                    _terminalStyle_horizontalSlider32.fixedHeight = 32;
                }
                if (_terminalStyle_horizontalSliderThumb32 == null)
                {
                    _terminalStyle_horizontalSliderThumb32 = new GUIStyle(GUI.skin.horizontalSliderThumb);
                    _terminalStyle_horizontalSliderThumb32.fixedWidth = 32;
                    _terminalStyle_horizontalSliderThumb32.fixedHeight = 32;
                }
            }
            else
            {
                // 上記以下の720*1280を想定したサイズ。(iPhone7とか)
                _terminalDrawRect = new Rect(0, TERMINAL_DEFAULT_SCREEN_HEIGHT_OFFSET, Screen.Width, Screen.Height - TERMINAL_DEFAULT_SCREEN_HEIGHT_OFFSET);
                if (TERMINAL_BUTTON_WIDTH == null)
                {
                    TERMINAL_BUTTON_WIDTH = GUILayout.Width(130.0f);
                }
                if (TERMINAL_LIST_BUTTON_WIDTH == null)
                {
                    TERMINAL_LIST_BUTTON_WIDTH = GUILayout.Width(150.0f);
                }
                if (TERMINAL_BUTTON_HEIGHT == null)
                {
                    _terminalButtonHeightFloat = 50.0f;
                    TERMINAL_BUTTON_HEIGHT = GUILayout.Height(_terminalButtonHeightFloat);
                }
                if (TERMINAL_SLIDER_WIDTH == null)
                {
                    TERMINAL_SLIDER_WIDTH = GUILayout.Width(100.0f);
                }
                if (TERMINAL_TEXTAREA_WIDTH == null)
                {
                    TERMINAL_TEXTAREA_WIDTH = GUILayout.Width(200.0f);
                }
                if (_terminalFacialHorizontalAreaWidth == null)
                {
                    _terminalFacialHorizontalAreaWidth = GUILayout.Width(500.0f);
                }
                if (_terminalClothHorizontalAreaWidth == null)
                {
                    _terminalClothHorizontalAreaWidth = GUILayout.Width(500.0f);
                }

                if (_terminalStyle_label32 == null)
                {
                    _terminalStyle_label32 = new GUIStyle(GUI.skin.label);
                    _terminalStyle_label32.fontSize = 24;
                }
                if (_terminalStyle_label20 == null)
                {
                    _terminalStyle_label20 = new GUIStyle(GUI.skin.label);
                    _terminalStyle_label20.fontSize = 20;
                }
                if (_terminalStyle_button20 == null)
                {
                    _terminalStyle_button20 = new GUIStyle(GUI.skin.button);
                    _terminalStyle_button20.fontSize = 20;
                    _terminalStyle_button20.fixedWidth = 240.0f * 0.85f;
                    _terminalStyle_button20.fixedHeight = 60.0f * 0.85f;
                }
                if (_terminalStyle_buttonMid20 == null)
                {
                    _terminalStyle_buttonMid20 = new GUIStyle(GUI.skin.button);
                    _terminalStyle_buttonMid20.fontSize = 20;
                    _terminalStyle_buttonMid20.fixedWidth = 260.0f * 0.85f;
                    _terminalStyle_buttonMid20.fixedHeight = 60.0f * 0.85f;
                }
                if (_terminalStyle_toggle20 == null)
                {
                    _terminalStyle_toggle20 = new GUIStyle(GUI.skin.toggle);
                    _terminalStyle_toggle20.fontSize = 20;
                    _terminalStyle_toggle20.fixedHeight = 32;
                }
                if (_terminalStyle_verticalScrollbar50 == null)
                {
                    _terminalStyle_verticalScrollbar50 = new GUIStyle(GUI.skin.verticalScrollbar);
                    _terminalStyle_verticalScrollbar50.fixedWidth = 50f;
                }
                if (_terminalStyle_horizontalSlider32 == null)
                {
                    _terminalStyle_horizontalSlider32 = new GUIStyle(GUI.skin.horizontalSlider);
                    _terminalStyle_horizontalSlider32.fixedHeight = 32;
                }
                if (_terminalStyle_horizontalSliderThumb32 == null)
                {
                    _terminalStyle_horizontalSliderThumb32 = new GUIStyle(GUI.skin.horizontalSliderThumb);
                    _terminalStyle_horizontalSliderThumb32.fixedWidth = 32;
                    _terminalStyle_horizontalSliderThumb32.fixedHeight = 32;
                }
            }

            if (_terminalStyle_buttonSmall20 == null)
            {
                _terminalStyle_buttonSmall20 = new GUIStyle(GUI.skin.button);
                _terminalStyle_buttonSmall20.fontSize = 20;
                _terminalStyle_buttonSmall20.fixedWidth = 130.0f;
                _terminalStyle_buttonSmall20.fixedHeight = _terminalButtonHeightFloat;
            }
            if (_terminalStyle_textField24 == null)
            {
                _terminalStyle_textField24 = new GUIStyle(GUI.skin.textField);
                _terminalStyle_textField24.fontSize = 24;
                _terminalStyle_textField24.fixedHeight = _terminalButtonHeightFloat;
            }

            if (UIManager.HasInstance() == false)
            {
                return;
            }

            // SafeArea対応
            Rect safeAreaRect = UIManager.Instance.GetSafeArea();
            if (_terminalDrawRect.y < safeAreaRect.y)
            {
                _terminalDrawRect.y = safeAreaRect.y;
                _terminalDrawRect.height = _terminalDrawRect.height - safeAreaRect.y;
            }
            if (_terminalDrawRect.height > safeAreaRect.height)
            {
                _terminalDrawRect.height = safeAreaRect.height;
            }

        }

        // キャラ情報表示
        private void OnGUI_Info()
        {
            if (_charaList == null)
            {
                return;
            }

            GUILayout.BeginVertical();

            GUILayout.FlexibleSpace();

            string charaInfoText = GetAndUpdateCharaInfoText();
            GUILayout.Label(charaInfoText, _terminalStyle_label32);

            GUILayout.EndVertical();
        }

        /// <summary>キャラ情報テキストを更新して返す</summary>
        public string GetAndUpdateCharaInfoText()
        {
            // 名前
            if (_terminalCharaName == null)
            {
                _terminalCharaName = GetCharacterString();
            }
            string name = _terminalCharaName[_terminalModelIndex];
            if (_terminalMobBuildModel)
            {
                name = "モブ";
            }
            else if (_isTerminalAudienceBuildModel)
            {
                name = "観客";
            }

            // 種類
            string type = CHARA_TYPE_STRING_ARRAY[_terminalCharacterTypeIndex];

            // 服装
            if (_terminalDressNameArray == null)
            {
                _terminalDressNameArray = GetDressString(_terminalModelIndex, _terminalMobBuildModel);
            }
            string cloth = "";
            if (_terminalDressIndex < _terminalDressNameArray.Length)
            {
                cloth = _terminalDressNameArray[_terminalDressIndex];
            }
            if (_charaList[_terminalModelIndex].CharaId == GameDefine.TAZUNA_CHARA_ID)
            {
                cloth = "たづなさん衣装";
            }
            else if (_charaList[_terminalModelIndex].CharaId == GameDefine.RIJICHO_CHARA_ID)
            {
                cloth = "理事長衣装";
            }
            else if (_charaList[_terminalModelIndex].CharaId == GameDefine.REPORTER_CHARA_ID)
            {
                cloth = "記者衣装";
            }

            // 頭
            string head = string.Empty;
            if (!_terminalMobBuildModel && !_isTerminalAudienceBuildModel) // モブや観客なら表示しない
            {
                if (_terminalHeadName == null)
                {
                    _terminalHeadName = GetHeadString(_terminalModelIndex);
                }
                // #65659 頭差分があるキャラを表示した後に頭差分がないキャラを選ぶと領域外参照が起きるのでチェックする
                if (_terminalHeadIndex < _terminalHeadName.Length)
                {
                    head = _terminalHeadName[_terminalHeadIndex];
                }
            }

            // テキスト決定
            if (!string.IsNullOrEmpty(head))
            {
                return string.Format("{0}:{1}:{2}:頭{3}", name, type, cloth, head);
            }
            else
            {
                return string.Format("{0}:{1}:{2}", name, type, cloth);
            }
        }

        #endregion

        #region テスト

        public void DownloadFromCharacterBuildInfo(EditableCharacterBuildInfo buildInfo)
        {
            var fileList = GetDownloadFileList(buildInfo, GetCharaViewerInfo().IsWet, GetCharaViewerInfo().IsDirt, GetCharaViewerInfo().Rotation, _liveSettingsCutt);

            DownloadManager.Instance.Download(fileList, () => { });
        }

        #endregion

        /// <summary>
        /// 今操作中のキャラと同じタイプのキャラを返す
        /// </summary>
        /// <returns></returns>
        public CharaViewerInfo[] GetCharaViewerInfoMulti()
        {
            List<CharaViewerInfo> list = new List<CharaViewerInfo>();
            CharacterType type = GetCharaViewerInfo().CharacterType;
            bool isMini = GetCharaViewerInfo().IsMini;

            if (_isAllCharacterSetting)
            {
                // 全体

                for (int i = 0; i < _characterViewerInfo.Length; i++)
                {
                    if (_characterViewerInfo[i].IsLoaded == false)
                    {
                        continue;
                    }

                    if (_characterViewerInfo[i].IsMini != isMini)
                    {
                        continue;
                    }

                    if (_characterViewerInfo[i].CharacterType != type)
                    {
                        continue;
                    }
                    list.Add(_characterViewerInfo[i]);
                }
            }
            else
            {
                // 単体
                list.Add(GetCharaViewerInfo());
            }

            return list.ToArray();
        }

        /// <summary>
        /// ギズモ表示
        /// </summary>
        private void OnDrawGizmos()
        {
            if (_isHeadHeightVisble)
            {
                for (int index = 0; index < _characterViewerInfo.Length; ++index)
                {
                    var modelCtrl = _characterViewerInfo[index].ModelController;
                    if (modelCtrl == null)
                    {
                        continue;
                    }
                    var headHeight = modelCtrl.FindTransform(CharaNodeName.Head_height);
                    if (headHeight != null)
                    {
                        var pos = headHeight.position;
                        Gizmos.color = Color.red;
                        Gizmos.DrawLine(pos, pos + (Math.VECTOR3_RIGHT * 0.2f));
                        Gizmos.color = Color.blue;
                        Gizmos.DrawLine(pos, pos + (Math.VECTOR3_FORWARD * 0.2f));
                        Gizmos.color = Color.green;
                        Gizmos.DrawLine(pos, pos + (Math.VECTOR3_UP * 0.2f));
                    }
                }
            }
        }

        /// <summary>画面上に警告文を表示する</summary>
        public static void ShowWarningNotification(string message)
        {
#if UNITY_EDITOR
            Debug.LogWarning(message);
#else
            if (!UIManager.Instance.IsShowNotification())
            {
                UIManager.Instance.ShowNotification(message);
            }
#endif
        }
    }
}
#endif // CYG_DEBUG //
