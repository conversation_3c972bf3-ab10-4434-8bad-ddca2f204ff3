#if CYG_DEBUG
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// デバッグ：キャラビューアー：モブダイアログ
    /// </summary>
    [AddComponentMenu("")]
    public sealed class DialogCharaViewerMob : DialogInnerBase
    {
        public class Param
        {
            public bool IsUseMobId = false;

            public int MobId = 0;

            public int FaceNo = 0;
            public int HairNo = 0;
            public int HairColorNo = 0;
            public int SkinNo = 0;
            public int DressColorSetId = 0;
            public float HairCutoff = 0.5f;
            public int AttachId = -1;
        }


        /// <summary>「モブIDを使用する」チェックボックス</summary>
        [SerializeField] public ToggleWithText _useMobIdCheckBox;


        [Header("使用する場合")]

        /// <summary>「モブID」インプットフィールド</summary>
        [SerializeField] public InputFieldCommon _mobIdInputField;

        /// <summary>操作禁止パネル</summary>
        [SerializeField] public GameObject _useMobIdMenuLockPanel;


        [Header("使用しない場合")]

        /// <summary>「顔」ドロップダウン</summary>
        [SerializeField] public DropDownCommon _faceNoDropDown;

        /// <summary>「髪型」ドロップダウン</summary>
        [SerializeField] public DropDownCommon _hairNoDropDown;

        /// <summary>「髪色」ドロップダウン</summary>
        [SerializeField] public DropDownCommon _hairColorNoDropDown;

        /// <summary>「肌色」ドロップダウン</summary>
        [SerializeField] public DropDownCommon _skinNoDropDown;

        /// <summary>「DressColorSetId」インプットフィールド</summary>
        [SerializeField] public InputFieldCommon _dressColorSetIdInputField;

        /// <summary>「HairCutoff」スライダー</summary>
        [SerializeField] public SliderCommon _hairCutoffSlider;
        [SerializeField] public TextCommon _hairCutoffSliderValueText;

        /// <summary>「AttachModelId」インプットフィールド</summary>
        [SerializeField] public InputFieldCommon _attachIdInputField;

        /// <summary>操作禁止パネル</summary>
        [SerializeField] public GameObject _unsuseMobIdMenuLockPanel;


        /// <summary>決定ボタンを押した時に呼ばれる外部コールバック</summary>
        private System.Action<Param> _onDecide = null;


        public override DialogCommonBase.FormType GetFormType() => DialogCommonBase.FormType.BIG_TWO_BUTTON;

        public override DialogCommon.Data.ObjectParentType GetParentType() => DialogCommon.Data.ObjectParentType.Base;

        protected override DialogCommon.Data CreateDialogData()
        {
            var data = base.CreateDialogData();
            data.Title = "モブ";
            data.LeftButtonText = TextId.Common0004.Text();
            data.RightButtonText = TextId.Common0023.Text();
            data.AutoClose = false;
            return data;
        }


        /// <summary>
        /// ダイアログを開く
        /// </summary>
        public static void Open(Param initParam, System.Action<Param> onDecide = null)
        {
            var obj = Instantiate(ResourceManager.LoadOnView<GameObject>("DialogCharaViewerMob"));
            var component = obj.GetComponent<DialogCharaViewerMob>();
            var dialogData = component.CreateDialogData();
            dialogData.LeftButtonCallBack = component.OnClickLeftButton;
            dialogData.RightButtonCallBack = component.OnClickRightButton;

            DialogManager.PushDialog(dialogData);
            component.Setup(initParam, onDecide);
        }

        private void Setup(Param initParam, System.Action<Param> onDecide)
        {
            _onDecide = onDecide;

            _useMobIdCheckBox.Toggle.isOn = initParam.IsUseMobId;
            _useMobIdCheckBox.Toggle.SetCallback(OnChangeUseMobIdCheckBox);

            SetupUseMobIdMenu(initParam);

            SetupUnuseMobIdMenu(initParam);

            RefreshLockPanels(initParam.IsUseMobId);
        }

        /// <summary>モブIDを使用する場合のメニューをセットアップ</summary>
        private void SetupUseMobIdMenu(Param initParam)
        {
            // モブIDのインプットフィールド
            _mobIdInputField.CurrentInputTextType = InputFieldCommon.InputTextType.BirthMonth;
            _mobIdInputField.text = initParam.MobId.ToString();
        }

        /// <summary>モブIDを使用しない場合のメニューをセットアップ</summary>
        private void SetupUnuseMobIdMenu(Param initParam)
        {
            // 顔ドロップダウン
            {
                _faceNoDropDown.ClearOptions();
                var charaFaceModelList = MasterDataManager.Instance.masterMobData.GetCharaFaceModelList();
                foreach (var charaFaceModel in charaFaceModelList)
                {
                    string charaFaceModelText = charaFaceModel.ToString();
                    _faceNoDropDown.options.Add(new Dropdown.OptionData(charaFaceModelText));
                }

                _faceNoDropDown.value = charaFaceModelList.IndexOf(initParam.FaceNo);
                _faceNoDropDown.SyncScrollOffsetByValue();
                _faceNoDropDown.RefreshShownValue();
            }

            // 髪型ドロップダウン
            {
                _hairNoDropDown.ClearOptions();
                var charaHairModelList = MasterDataManager.Instance.masterMobData.GetCharaHairModelList();
                foreach (var charaHairModel in charaHairModelList)
                {
                    string charaHairModelText = charaHairModel.ToString();
                    _hairNoDropDown.options.Add(new Dropdown.OptionData(charaHairModelText));
                }

                _hairNoDropDown.value = charaHairModelList.IndexOf(initParam.HairNo);
                _hairNoDropDown.SyncScrollOffsetByValue();
                _hairNoDropDown.RefreshShownValue();
            }

            // 髪色ドロップダウン
            {
                _hairColorNoDropDown.ClearOptions();
                var charaHairColorList = MasterDataManager.Instance.masterMobData.GetCharaHairColorList();
                foreach (var charaHairColor in charaHairColorList)
                {
                    string charaHairColorText = charaHairColor.ToString();
                    _hairColorNoDropDown.options.Add(new Dropdown.OptionData(charaHairColorText));
                }

                _hairColorNoDropDown.value = charaHairColorList.IndexOf(initParam.HairColorNo);
                _hairColorNoDropDown.SyncScrollOffsetByValue();
                _hairColorNoDropDown.RefreshShownValue();
            }

            // 肌色ドロップダウン
            {
                _skinNoDropDown.ClearOptions();
                var charaSkinColorList = MasterDataManager.Instance.masterMobData.GetCharaSkinColorList();
                foreach (var charaSkinColor in charaSkinColorList)
                {
                    string charaSkinColorText = charaSkinColor.ToString();
                    _skinNoDropDown.options.Add(new Dropdown.OptionData(charaSkinColorText));
                }

                _skinNoDropDown.value = charaSkinColorList.IndexOf(initParam.SkinNo);
                _skinNoDropDown.SyncScrollOffsetByValue();
                _skinNoDropDown.RefreshShownValue();
            }

            // DressColorSetIdのインプットフィールド
            _dressColorSetIdInputField.CurrentInputTextType = InputFieldCommon.InputTextType.BirthMonth;
            _dressColorSetIdInputField.text = initParam.DressColorSetId.ToString();

            // HairCutoffスライダー
            _hairCutoffSlider.value = initParam.HairCutoff;
            _hairCutoffSlider.onValueChanged.AddListener(sliderValue =>
            {
                _hairCutoffSliderValueText.text = sliderValue.ToString("0.00");
            });
            _hairCutoffSliderValueText.text = _hairCutoffSlider.value.ToString("0.00");

            // AttachModelIdのインプットフィールド
            _attachIdInputField.CurrentInputTextType = InputFieldCommon.InputTextType.BirthMonth;
            _attachIdInputField.text = initParam.AttachId.ToString();
        }

        /// <summary>操作禁止パネルの更新</summary>
        private void RefreshLockPanels(bool isUseMobId)
        {
            _useMobIdMenuLockPanel.SetActiveWithCheck(!isUseMobId);
            _unsuseMobIdMenuLockPanel.SetActiveWithCheck(isUseMobId);
        }

        /// <summary>「モブIDを使用する」チェックボックスが切り替わった時の処理</summary>
        private void OnChangeUseMobIdCheckBox(bool isOn)
        {
            RefreshLockPanels(isOn);
        }

        /// <summary>「決定」ボタンが押された時の処理</summary>
        private void OnClickRightButton(DialogCommon dialog)
        {
            var outParam = new Param();

            outParam.IsUseMobId = _useMobIdCheckBox.Toggle.isOn;

            outParam.MobId = _mobIdInputField.GetTextInt();

            outParam.FaceNo = _faceNoDropDown.GetSelectTextInt();
            outParam.HairNo = _hairNoDropDown.GetSelectTextInt();
            outParam.HairColorNo = _hairColorNoDropDown.GetSelectTextInt();
            outParam.SkinNo = _skinNoDropDown.GetSelectTextInt();
            outParam.DressColorSetId = _dressColorSetIdInputField.GetTextInt();
            outParam.HairCutoff = _hairCutoffSlider.value;
            outParam.AttachId = _attachIdInputField.GetTextInt();

            // 外部コールバックを呼ぶ
            _onDecide?.Invoke(outParam);
        }

        /// <summary>
        /// 「キャンセル」ボタンが押された時の処理
        /// </summary>
        private void OnClickLeftButton(DialogCommon dialog)
        {
            dialog.Close();
        }
    }
}
#endif
