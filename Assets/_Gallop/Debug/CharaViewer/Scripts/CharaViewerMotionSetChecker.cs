using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
#if CYG_DEBUG
    /// <summary>
    /// モーションセットCSVの中身をチェックするツール
    /// </summary>
    public class CharaViewerMotionSetChecker : MonoBehaviour
    {
        //キャラ生成時の衣装ID
        private const int DEFAULT_DRESS_ID = 2;

        private static CharaViewerMotionSetChecker _instance = null;

        private SimpleModelController _model;
        private System.Action _onFinish;
        private MasterCharaMotionSet.CharaMotionSet _idleMotionSet;

        private int _currentMotionSetIndex;
        private int _currentCharaIndex;
        private int _currentDressIndex;

        private int _currentCharaId;
        private int _currentDressId;

        //マスターデータのListプール、一度作れば使いまわせるためstatic
        private static List<MasterCharaData.CharaData> _masterCharaList;
        private static List<MasterDressData.DressData> _masterDressList;
        private static List<MasterCharaMotionSet.CharaMotionSet> _masterMotionSetList;  //モーションセットマスターのリスト
        private static List<MasterCharaMotionSet.CharaMotionSet> _masterMotionSetDistinctMotionList;    //モーション名の被りが無いマスターのリスト
        private static List<MasterCharaMotionSet.CharaMotionSet> _motionSetList;    //実際に使用されるモーションセットのリスト、モードによってリストの中身を切り替える

        //キャラの生成関数、シーンによって専用の処理があるためシーンから渡してもらう
        private System.Func<int, int, SimpleModelController> _createCharaFunc = null;

        [SerializeField]
        private TextCommon _infoText = null;

        [SerializeField]
        private ToggleCommon _motionSetTab = null;

        [SerializeField]
        private ToggleCommon _motionTab = null;

        /// <summary>
        /// なければ作る
        /// </summary>
        /// <param name="model"></param>
        /// <param name="createCharaAction">キャラの生成関数</param>
        /// <param name="onFinish"></param>
        /// <returns></returns>
        public static CharaViewerMotionSetChecker Create(SimpleModelController model, System.Func<int, int, SimpleModelController> createCharaFunc = null, System.Action onFinish = null)
        {
            if (_instance != null)
                return _instance;

            var obj = Resources.Load<GameObject>("CharaViewer/MotionSetDebug");
            var objInstance = Instantiate(obj);
            _instance = objInstance.GetComponent<CharaViewerMotionSetChecker>();
            _instance.CreateInternal(model, createCharaFunc, onFinish);
            return _instance;
        }

        /// <summary>
        /// UI破棄
        /// </summary>
        public static void DestroyInstance()
        {
            if (_instance == null)
                return;

            _instance.OnFinish();
            Destroy(_instance.gameObject);
        }

        /// <summary>
        /// UI作成
        /// </summary>
        /// <param name="model"></param>
        /// <param name="onFinish"></param>
        /// <returns></returns>
        private void CreateInternal(SimpleModelController model, System.Func<int, int, SimpleModelController> createCharaFunc, System.Action onFinish)
        {
            _model = model;
            _onFinish = onFinish;
            _createCharaFunc = createCharaFunc;

            //モデルから現在情報の取得
            _currentCharaId = _model.GetCharaID();
            _currentDressId = _model.GetDressId();

            //マスター配列作成
            CacheMasterData();

            //キャラ、衣装の配列内のインデックス値取得
            _currentCharaIndex = _masterCharaList.FindIndex(d => d.Id == _currentCharaId);
            _currentDressIndex = _masterDressList.FindIndex(d => d.Id == _currentDressId);

            //タブの設定
            _motionSetTab.AddOnValueChangeIsOn(OnTabMotionSet);
            _motionTab.AddOnValueChangeIsOn(OnTabMotion);

            //デフォルトでモーション名被り無しで
            _motionTab.isOn = true;
            _motionSetList = _masterMotionSetList;

            //足りないモーションがあればダウンロードして初期値再生
            StartCoroutine(DownloadAssetsCoroutine(_currentCharaId, PlayCurrent));
        }

        /// <summary>
        /// マスターデータ配列作成
        /// </summary>
        private void CacheMasterData()
        {
            if (_masterCharaList != null && _masterDressList != null && _motionSetList != null)
                return;

            //CSVのフェイス指定がNoneになってることがあるので読み飛ばす
            var faceNone = "None";

            _masterCharaList = MasterDataManager.Instance.masterCharaData.dictionary.Values.ToList();
            _masterDressList = MasterDataManager.Instance.masterDressData.dictionary.Values.ToList();
            _masterMotionSetList = MasterDataManager.Instance.masterCharaMotionSet.dictionary.Values.Where(d => d != null && d.FaceType != faceNone).ToList();
            _masterMotionSetList.Sort((a, b) => a.Id - b.Id);

            //モーション名に被りが無いモーションセットのマスターリストを作成
            _masterMotionSetDistinctMotionList = new List<MasterCharaMotionSet.CharaMotionSet>();
            var motionNames = new List<string>();
            foreach(var motionSet in _masterMotionSetList)
            {
                if (!motionNames.Contains(motionSet.BodyMotion))
                {
                    _masterMotionSetDistinctMotionList.Add(motionSet);
                    motionNames.Add(motionSet.BodyMotion);
                }
            }

            //アイドルのマスター取得
            _idleMotionSet = _masterMotionSetList.FirstOrDefault(d => d.Id == SimpleModelController.IDLE_UNIQUE_MOTION_ID);

            //カードが存在しないキャラはNG
            for (int i = _masterCharaList.Count - 1; i >= 0; i--)
            {
                var chara = _masterCharaList[i];
                var cardList = MasterDataManager.Instance.masterCardData.GetListWithCharaIdOrderByIdAsc(chara.Id);
                if(chara.DebugDefaultCardId == GameDefine.INVALID_CHARA_ID)
                {
                    _masterCharaList.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// 足りないリソースのダウンロード
        /// </summary>
        /// <param name="charaId"></param>
        /// <param name="onFinish"></param>
        /// <param name="onCancel"></param>
        /// <returns></returns>
        private IEnumerator DownloadAssetsCoroutine(int charaId, System.Action onFinish, System.Action onCancel = null)
        {
            var currentSceneId = SceneManager.Instance.GetCurrentSceneId();
            var register = DownloadManager.GetNewRegister();
            register.SetupOnScene(currentSceneId);

            //モーション
            foreach (var master in _motionSetList)
            {
                SimpleModelController.RegisterAssetsMotion(register, _model.GetBuildInfo(), master);
            }

            //ボイス
            AudioManager.Instance.RegisterDownloadByCharaIds(register, new List<int> { charaId }, Gallop.CharacterSystemTextGroupExtension.Scene.Home, true);

            DownloadManager.Instance.FixDownloadList(ref register);

            //ダウンロード終了待ち
            bool isFinish = false;
            DownloadManager.Instance.DownloadFromRegister(() => isFinish = true, onCancel);
            yield return new WaitWhile(() => !isFinish);

            if(onFinish != null)
            {
                onFinish();
            }
        }

        /// <summary>
        /// モデルの作成
        /// </summary>
        private void CreateCharaModel()
        {
            if (_createCharaFunc == null)
            {
                Debug.LogWarning("キャラ、衣装の変更の場合はcreateCharaActionの指定が必須");
                return;
            }

            //リソースがないためキャラビルドに失敗することも考えられるためtry-catch
            try
            {
                //モデル生成
                _model = _createCharaFunc(_currentCharaId, _currentDressId);
                //アイドルを再生、何かしら再生してないとPlayLipSync内でエラーになってしまうため。
                _model.PlayMotion(_idleMotionSet);
                //モーション再生
                PlayCurrent();
            }
            catch
            {
                Debug.LogError("キャラの生成に失敗");
            }
        }

        /// <summary>
        /// モーションとボイスの再生
        /// </summary>
        private void PlayCurrent()
        {
            if (_model == null)
                return;

            //ダミーのシステムボイスを取得
            var dummySystemText = AudioManager.Instance.PlaySystemVoice_HomeStay(_model.GetCharaID(), _model.GetCardId(), _model.GetDressId(), new List<TimeUtil.SystemVoiceSeason>() { TimeUtil.SystemVoiceSeason.Spring }, RaceDefine.Time.Daytime);
            if (dummySystemText == null)
                return;

            //現在インデックスのモーションセット取得
            var motionSet = _motionSetList[_currentMotionSetIndex];
            if (motionSet == null)
                return;

            //再生
            _model.PlayLipSyncAndMotion(dummySystemText, nextMotion: _idleMotionSet, motionSet: motionSet);
            const string infoText = "CharaId: {0}\n DressId: {1}\n MotionSetId:{2}\nMotionName:{3}\nFace:{4}";
            _infoText.text = string.Format(infoText, _currentCharaId, _currentDressId, motionSet.Id, motionSet.BodyMotion, motionSet.FaceType);
        }

        private void ValidateMotionSetIndex()
        {
            if(_currentMotionSetIndex >= _motionSetList.Count)
            {
                _currentMotionSetIndex = 0;
            }
            else if(_currentMotionSetIndex < 0)
            {
                _currentMotionSetIndex = _motionSetList.Count - 1;
            }
        }

        private void ValidateCharaIndex()
        {
            if (_currentCharaIndex >= _masterCharaList.Count)
            {
                _currentCharaIndex = 0;
            }
            else if (_currentCharaIndex < 0)
            {
                _currentCharaIndex = _masterCharaList.Count - 1;
            }
        }

        private void ValidateDressIndex()
        { 
            if (_currentDressIndex >= _masterDressList.Count)
            {
                _currentDressIndex = 0;
            }
            else if (_currentDressIndex < 0)
            {
                _currentDressIndex = _masterDressList.Count - 1;
            }
        }

        /// <summary>
        /// 終了ボタンコールバック
        /// </summary>
        public void OnFinish()
        {
            if(_onFinish != null)
            {
                _onFinish();
            }
            _instance = null;
            Destroy(gameObject);
        }

        /// <summary>
        /// モーション：次へ
        /// </summary>
        public void OnMotionNext()
        {
            _currentMotionSetIndex++;
            ValidateMotionSetIndex();
            PlayCurrent();
        }

        /// <summary>
        /// モーション：戻る
        /// </summary>
        public void OnMotionBack()
        {
            _currentMotionSetIndex--;
            ValidateMotionSetIndex();
            PlayCurrent();
        }

        /// <summary>
        /// キャラ：次へ
        /// </summary>
        public void OnCharaNext()
        {
            //配列インデックス進める
            _currentCharaIndex++;
            ValidateCharaIndex();

            //キャラID取得
            var prevCharaId = _currentCharaId;
            _currentCharaId = _masterCharaList[_currentCharaIndex].Id;

            //ダウンロード開始
            StartCoroutine(DownloadAssetsCoroutine(_currentCharaId, 
                ()=>
                {
                    //衣装IDのリセット、キャラモデル生成
                    _currentDressId = DEFAULT_DRESS_ID;
                    _currentDressIndex = _masterDressList.FindIndex(d => d.Id == _currentDressId); 
                    CreateCharaModel();
                },
                () => {
                    _currentCharaIndex--;
                    ValidateCharaIndex();
                    _currentCharaId = prevCharaId;
            }));
        }

        /// <summary>
        /// キャラ：戻る
        /// </summary>
        public void OnCharaBack()
        {
            //配列インデックス進める
            _currentCharaIndex--;
            ValidateCharaIndex();

            //キャラID取得
            var prevCharaId = _currentCharaId;
            _currentCharaId = _masterCharaList[_currentCharaIndex].Id;

            //ダウンロード開始
            StartCoroutine(DownloadAssetsCoroutine(_currentCharaId, () =>
            {
                //衣装IDのリセット、キャラモデル生成
                _currentDressId = DEFAULT_DRESS_ID;
                _currentDressIndex = _masterDressList.FindIndex(d => d.Id == _currentDressId);
                CreateCharaModel();
            }, 
            () => {
                _currentCharaIndex++;
                ValidateCharaIndex();
                _currentCharaId = prevCharaId;
            }));
        }

        /// <summary>
        /// 衣装：次へ
        /// </summary>
        public void OnDressNext()
        {
            ChangeDress(() => _currentDressIndex++);
        }

        /// <summary>
        /// 衣装：戻る
        /// </summary>
        public void OnDressBack()
        {
            ChangeDress(() => _currentDressIndex--);
        }

        private void ChangeDress(System.Action counter)
        {
            MasterDressData.DressData dress = null;
            while (dress == null)
            {
                counter();
                ValidateDressIndex();
                dress = _masterDressList[_currentDressIndex];
                _currentDressId = dress.Id;
                //キャラ固有のものはスキップ
                if (dress.CharaId != 0 && dress.CharaId != _currentCharaId)
                {
                    dress = null;
                }
            }
            CreateCharaModel();
        }

        /// <summary>
        /// モーションセットのIDドリブンで再生するモードに切り替え
        /// </summary>
        public void OnTabMotionSet()
        {
            _motionSetList = _masterMotionSetList;
            _currentMotionSetIndex = 0;
            PlayCurrent();
        }

        /// <summary>
        /// モーション名ドリブンで再生するモードに切り替え
        /// </summary>
        public void OnTabMotion()
        {
            _motionSetList = _masterMotionSetDistinctMotionList;
            _currentMotionSetIndex = 0;
            PlayCurrent();
        }

        /// <summary>
        /// 再生
        /// </summary>
        public void OnPlay()
        {
            PlayCurrent();
        }
    }
#endif
}
