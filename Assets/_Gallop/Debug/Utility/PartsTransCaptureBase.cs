#if CYG_DEBUG && UNITY_EDITOR

using System;
using System.Collections;
using System.IO;
using Gallop.RenderPipeline;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Object = UnityEngine.Object;

namespace Gallop
{
    using static TransCaptureUtil;

    /// <summary>
    /// ダイレクトシーンで透過背景キャプチャの設定を行うパーツのベースクラス
    /// </summary>
    public abstract class PartsTransCaptureBase
    {
        #region 変数

        // 透過背景でキャプチャ
        protected bool _useCustomDirectory = false;
        // 録画機能と同じ出力先名を使うか
        protected bool _useRecordingOutputName = true;
        // ファイル名の末尾の連番をつけるか
        protected bool _useFileNameSequence = true;

        protected bool _transCaptureFoldOut; // TransCaptureは造語 (Capture With Transparent Bg が長いので略した)
        
        protected string _transCaptureDirectory = DUMMY_INPUT;
        protected string _customDirectory;
        protected string _transCaptureFileName = DUMMY_INPUT;

        protected DownSamplingUGUI _downSampling = new DownSamplingUGUI();
        protected bool _isRequestTransCapture;
        protected string _pngPath;
        protected RenderTexture _captureTexture;
        protected RenderTexture _tempTexture;

        protected TransCapturePostEffectController _postEffectController = new TransCapturePostEffectController();

        // デフォルト撮影機能
        protected TransDefaultCaptureController _defaultCaptureController = new TransDefaultCaptureController();

        /// <summary>
        /// キャプチャした画像の出力先ディレクトリ
        /// </summary>
        protected string OutputDirectory
        {
            get
            {
                return _useCustomDirectory ? _customDirectory : _transCaptureDirectory;
            }
        }

        // アルファチャンネルの復帰用
        protected RenderTexture _alphaTexture;
        protected Material _alphaCopyMaterial;
        
        // skillCuttDirectGahahaMaterial用
        protected bool _gachaCaptureFoldOut;

        #endregion

        private const string DEFAULT_USE_RECORDING_OUTPUT_NAME_LABEL = "録画機能と同じフォルダ名に設定する";

        /// <summary>
        /// コンストラクタ
        /// </summary>
        protected PartsTransCaptureBase()
        {
        }

        #region 透過背景でキャプチャ

        /// <summary>
        /// 透過背景で連続撮影する機能のGUIを表示
        /// </summary>
        /// <remarks><see cref="CharaViewerWindow.DrawCaptureWithTransparentBgGUI"/>と似ているが,
        /// 撮影開始オフセットが指定できるなど違う部分もあるので共通化はしていない.
        /// </remarks>
        public abstract void DrawCaptureWithTransparentBgGUI();

        protected void DrawOutputSizeGUI(Vector2Int defaultCaptureSize)
        {
            if (_downSampling.IsApplyDownSampling == false)
            {
                EditorGUILayout.LabelField("出力サイズ", $"{Screen.GameViewScreenWidth} x {Screen.GameViewScreenHeight} (GameViewのサイズと同じ; {defaultCaptureSize.x} x {defaultCaptureSize.y} で撮影して下さい)");
            }
        }

        /// <summary>
        /// 出力先設定の項目を描画
        /// </summary>
        /// <param name="drawDownSamplingGUI"></param>
        /// <param name="isApplyEditCaptureSize"></param>
        protected void DrawOutputSettingGUI(bool drawDownSamplingGUI = true, bool isApplyEditCaptureSize = false)
        {
            _useCustomDirectory = EditorGUILayout.Toggle("別のフォルダを使用する", _useCustomDirectory, GUILayout.Width(200));
            if (GUILayout.Button("出力先を開く"))
            {
                OpenOutputDirectory();
            }

            DrawOutputPathSettingGUI();

            if (drawDownSamplingGUI)
            {
                // ダウンサンプリングを利用するかどうかでオプションの表示を変える
                _downSampling.DrawGUI(x => _downSampling.SetupResolutionTypePopupInfo(x), isApplyEditCaptureSize: isApplyEditCaptureSize);
            }
        }

        /// <summary>
        /// 出力先設定の項目を描画(録画機能と同じ出力先名が使える場合)
        /// </summary>
        /// <param name="drawDownSamplingGUI"></param>
        /// <param name="isApplyEditCaptureSize"></param>
        /// <param name="useRecordingOutputNameLabel"></param>
        protected void DrawOutputSettingWithRecordingOutputNameGUI(bool drawDownSamplingGUI = true, bool isApplyEditCaptureSize = false, string useRecordingOutputNameLabel = DEFAULT_USE_RECORDING_OUTPUT_NAME_LABEL)
        {
            EditorGUILayout.BeginHorizontal();
            if (!_useRecordingOutputName)
            {
                _useCustomDirectory = EditorGUILayout.Toggle("別のフォルダを使用する", _useCustomDirectory, GUILayout.Width(200));
            }

            // ラベルの幅を広げる
            var originalLabelWidth = EditorGUIUtility.labelWidth;
            EditorGUIUtility.labelWidth = 200;
            _useRecordingOutputName = EditorGUILayout.Toggle(useRecordingOutputNameLabel, _useRecordingOutputName, GUILayout.Width(250));
            EditorGUIUtility.labelWidth = originalLabelWidth;

            if (GUILayout.Button("出力先を開く", GUILayout.Width(100)))
            {
                OpenOutputDirectory();
            }

            EditorGUILayout.EndHorizontal();

            // 録画機能と同じ出力先名を使う場合はフォルダ名の項目は不要
            if (!_useRecordingOutputName)
            {
                DrawOutputPathSettingGUI();
            }

            if (drawDownSamplingGUI)
            {
                // ダウンサンプリングを利用するかどうかでオプションの表示を変える
                _downSampling.DrawGUI(x => _downSampling.SetupResolutionTypePopupInfo(x), isApplyEditCaptureSize: isApplyEditCaptureSize);
            }
        }

        /// <summary>
        /// 出力先フォルダとファイル名の設定
        /// </summary>
        private void DrawOutputPathSettingGUI()
        {
            // 出力先フォルダの設定
            if (_useCustomDirectory)
            {
                EditorGUILayout.BeginHorizontal();
                _customDirectory = EditorGUILayout.TextField("出力先フォルダ", _customDirectory);
                if (GUILayout.Button("...", GUILayout.Width(30)))
                {
                    var directory = EditorUtility.OpenFolderPanel("出力先フォルダを選択", "", "");
                    _customDirectory = directory;
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                _transCaptureDirectory = EditorGUILayout.TextField("フォルダ名", _transCaptureDirectory);
            }

            // ファイル名の設定
            _transCaptureFileName = EditorGUILayout.TextField("ファイル接頭辞", _transCaptureFileName);

            var outputDirectory = _useCustomDirectory ? _customDirectory : $"Capture/{_transCaptureDirectory}";
            EditorGUILayout.LabelField("出力先", $"{outputDirectory}/{_transCaptureFileName}_xxx.png (xxxは連番; 同じファイルがある場合は上書きされます)");
        }

        /// <summary>
        /// 撮影ボタンを描画
        /// </summary>
        /// <param name="isValid"></param>
        /// <param name="onClickButton"></param>
        protected void DrawCaptureButton(bool isValid, Action onClickButton = null)
        {
            // 何らかの条件が満たされていない場合は撮影ボタンを無効化する
            EditorGUI.BeginDisabledGroup(!isValid);
            using (new EditorGUILayout.HorizontalScope())
            {
                EditorGUILayout.LabelField("", GUILayout.Width(13 * EditorGUI.indentLevel)); // ボタンはインデントが効かないので無理矢理開ける
                if (GUILayout.Button("撮影"))
                {
                    onClickButton?.Invoke();
                }
            }
            EditorGUI.EndDisabledGroup();
        }

        /// <summary>
        /// 出力先を検証
        /// </summary>
        /// <returns></returns>
        protected bool IsValidName()
        {
            return !string.IsNullOrWhiteSpace(OutputDirectory) && OutputDirectory != DUMMY_INPUT &&
                   !string.IsNullOrWhiteSpace(_transCaptureFileName) && _transCaptureFileName != DUMMY_INPUT;
        }

        /// <summary>
        /// 出力サイズを検証
        /// </summary>
        /// <returns></returns>
        protected bool IsValidSize()
        {
            var defaultCaptureSize = GetCaptureSize();

            bool isValidSize = Screen.GameViewScreenWidth == defaultCaptureSize.x &&
                               Screen.GameViewScreenHeight == defaultCaptureSize.y;

            // ダウンサンプリングする場合は出力サイズの設定を確認
            if (_downSampling.IsApplyDownSampling)
            {
                // 最終的に出力する画像のサイズ < ダウンサンプリング前のRTのサイズであることを確かめる
                isValidSize &= _downSampling.CaptureSize.x > 0 && _downSampling.CaptureSize.y > 0 &&
                               0 < _downSampling.CaptureSize.sqrMagnitude && _downSampling.CaptureSize.sqrMagnitude < _downSampling.TempCaptureSize.sqrMagnitude;
            }

            return isValidSize;
        }

        protected IEnumerator StartCaptureWithTransparentBg(float offset = 0f, float interval = 0f)
        {
            // 再生開始まで待機
            if (!IsPlaying())
            {
                yield return StartPlay();
            }

            // デフォルト撮影機能を使う場合は設定を読み込む
            if (_defaultCaptureController.IsEnableDefaultCapture)
            {
                _defaultCaptureController.Setup();
            }

            PreCaptureAction();

            // キャプチャしたファイルの置き場
            var basePath = _useCustomDirectory
                ? _customDirectory
                : CharaViewer.CHARA_VIEWER_AUTO_CAPTURE_EXPORT_ROOTPATH + $"/Capture/{_transCaptureDirectory}";
            if (!Directory.Exists(basePath))
            {
                Directory.CreateDirectory(basePath);
            }

            // 撮影用RTのサイズを決める
            // ダウンサンプリングを使用する場合はスケールを掛けた大きいサイズを設定する
            var size = _downSampling.IsApplyDownSampling ? _downSampling.TempCaptureSize : GetCaptureSize();

            // キャプチャ用カメラを準備
            var captureCamera = SetupCaptureCamera(size);

            // 処理落ち対策のためキャプチャ中のフレームレートを固定する
            Time.captureFramerate = GameDefine.NORMAL_FRAME_RATE;

            // カットの長さから撮影回数を算出
            int count = Mathf.RoundToInt(GetLength() * GameDefine.NORMAL_FRAME_RATE);

            // オフセット時間が指定されていた場合は待機する
            while (offset > Math.EPSILON)
            {
                offset -= Time.deltaTime;
                yield return null;
            }

            // 撮影実行
            yield return CaptureWithTransparentBgCoroutine(basePath, count, interval, () =>
            {
                if (captureCamera != null)
                {
                    // 参照を切る
                    captureCamera.targetTexture = null;
                    // 後始末
                    Object.Destroy(captureCamera.gameObject);
                }
                
                if (_alphaCopyMaterial != null)
                {
                    var idArray = _alphaCopyMaterial.GetTexturePropertyNameIDs();
                    _alphaCopyMaterial.SetTexture(idArray[0], null);
                    _alphaCopyMaterial.SetTexture(idArray[1], null);
                }

                if (_captureTexture != null)
                {
                    _captureTexture.Release();
                    Object.Destroy(_captureTexture);
                    _captureTexture = null;
                }
                if (_alphaTexture != null)
                {
                    _alphaTexture.Release();
                    Object.Destroy(_alphaTexture);
                    _alphaTexture = null;
                }
                if (_alphaCopyMaterial != null)
                {
                    Object.Destroy(_alphaCopyMaterial);
                    _alphaCopyMaterial = null;
                }
                if (_tempTexture != null)
                {
                    _tempTexture.Release();
                    Object.Destroy(_tempTexture);
                    _tempTexture = null;
                }

                // キャプチャ中のフレームレート固定を解除する
                Time.captureFramerate = 0;

                PostCaptureAction();

                if (!EditorUtility.DisplayDialog("透過背景キャプチャ", "キャプチャが終了しました。", "OK", "出力先を開く"))
                {
                    OpenOutputDirectory();
                }
            });

            yield return null;
        }

        /// <summary>
        /// キャプチャの出力先ディレクトリを開く
        /// </summary>
        private void OpenOutputDirectory()
        {
            var outputPath = $"{Environment.CurrentDirectory}\\Capture\\{_transCaptureDirectory}";
            if (Directory.Exists(outputPath))
            {
                System.Diagnostics.Process.Start(outputPath);
            }
        }

        /// <summary>
        /// キャプチャ結果をPNGに出力する
        /// </summary>
        protected void ExportPngOnAfterRendering(CustomRenderPass pass, ScriptableRenderContext context, ref RenderingData data)
        {
            if (!_isRequestTransCapture) return;
            _isRequestTransCapture = false;

            if (_downSampling.IsApplyDownSampling)
            {
                ExportPngProcessWithDownSampling(_captureTexture, _downSampling.CaptureSize, _pngPath);
            }
            else
            {
                ExportPngProcess(_captureTexture, _pngPath);
            }
        }

        /// <summary>
        /// 透過背景で連続撮影するコルーチン
        /// </summary>
        private IEnumerator CaptureWithTransparentBgCoroutine(string basePath, int count, float interval, Action onFinish)
        {
            // CreateCaptureCameraで作成したオブジェクトのTransform反映を1F待つ
            yield return null;

            for (int i = 0; i < count; i++)
            {
                // 進捗を表示
                var message = $"撮影中... ({i + 1}/{count})";
                var progress = (float)i / count;
                var isCancel = EditorUtility.DisplayCancelableProgressBar(_transCaptureFoldOut == true ? "透過背景でキャプチャ" : "ガチャ素材用でキャプチャ", message, progress);
                if (isCancel) break;

                // キャプチャ実行
                var filePath = $"{basePath}/{_transCaptureFileName}";
                _pngPath = _useFileNameSequence ? $"{filePath}_{i:D3}.png" : $"{filePath}.png";
                _isRequestTransCapture = true;
                yield return UIManager.WaitForEndOfFrame;

                // 撮影間隔が指定されている場合は待機する
                var waitTime = interval;
                while (waitTime > Math.EPSILON)
                {
                    waitTime -= Time.deltaTime;
                    yield return null;
                }

                // もうカットが終わっていたらループ抜ける
                if (!IsPlaying()) break;
            }

            onFinish.Invoke();
            EditorUtility.ClearProgressBar();
        }

        /// <summary>
        /// キャプチャサイズを取得する
        /// </summary>
        /// <returns></returns>
        protected abstract Vector2Int GetCaptureSize();

        /// <summary>
        /// キャプチャの長さを取得する
        /// </summary>
        /// <returns></returns>
        protected abstract float GetLength();

        /// <summary>
        /// 再生を開始し、再生状態になるのを待つ
        /// </summary>
        /// <returns></returns>
        protected abstract IEnumerator StartPlay();

        /// <summary>
        /// 再生中か
        /// </summary>
        /// <returns></returns>
        protected abstract bool IsPlaying();

        /// <summary>
        /// キャプチャ用カメラのセットアップ
        /// </summary>
        /// <param name="size"></param>
        protected abstract Camera SetupCaptureCamera(Vector2Int size);

        /// <summary>
        /// キャプチャ実行前の操作
        /// UIの非表示化など
        /// </summary>
        protected virtual void PreCaptureAction()
        {
        }

        /// <summary>
        /// キャプチャ後の後始末
        /// </summary>
        protected virtual void PostCaptureAction()
        {
        }

        /// <summary>
        /// ポストエフェクトが有効か
        /// </summary>
        /// <returns></returns>
        protected bool IsEnablePostEffect() => _postEffectController.IsEnablePostEffect ||
                                               _defaultCaptureController.IsEnableDefaultCapture;

        /// <summary>
        /// アルファチャンネルの復帰が必要か
        /// </summary>
        /// <returns></returns>
        protected bool NeedsKeepAlphaChannel() => _postEffectController.NeedsKeepAlphaChannel() ||
                                                  _defaultCaptureController.IsEnableDefaultCapture;

        #endregion
    }   
}

#endif // CYG_DEBUG && UNITY_EDITOR
