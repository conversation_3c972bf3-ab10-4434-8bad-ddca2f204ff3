#if UNITY_EDITOR && CYG_DEBUG

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using NUnit.Framework;
using UnityEngine.TestTools;

namespace Gallop
{
    namespace Test
    {
        /// <summary>
        /// <see cref="ShopTimeImpl"/>テスト
        /// </summary>
        public class TestShopTimeImpl : MonoBehaviour
        {
            /// <summary>
            /// ヘルパークラス
            /// </summary>
            public class ShopTimeImplTestCore : MonoBehaviour, IMonoBehaviourTest
            {
                public  ShopTimeImpl Impl { get; private set; }
                public bool IsTestFinished { get; private set; }
                public bool IsUpdated { get; private set; }
                public bool IsCompleted { get; private set; }

                public System.Action<ShopTimeImpl> OnUpdated { get; set; }
                public System.Action OnCompleted { get; set; }


                public void StartTimer(long timeSecond)
                {
                    Impl?.Dispose();

                    IsUpdated = false;
                    IsCompleted = false;

                    // EventTimerはSererTimeとの差分を求めるので、時間を設定するときは足し合わせる
                    timeSecond += TimeUtil.GetServerTimeStamp();

                    Impl = new ShopTimeImpl();
                    Impl.Start(timeSecond)
                        .Subscribe(_=> 
                            {
                                IsUpdated = true;

                                OnUpdated?.Invoke(Impl);
                            }, 
                            onComplete: () => 
                            {
                                IsCompleted = true;

                                OnCompleted?.Invoke();
                            });
                }

                /// <summary>
                /// 更新があるまで待機
                /// </summary>
                public IEnumerator WaitForUpdate()
                {
                    IsUpdated = false;

                    yield return new WaitUntil(() => IsUpdated || IsCompleted);
                }

                public void ForceFinish()
                {
                    IsTestFinished = true;
                }
            }

            private MonoBehaviourTest<ShopTimeImplTestCore> _testGameObj;
            private ShopTimeImplTestCore _core;


            [OneTimeSetUp]
            public void OneTimeSetUp()
            {
                if (!TempData.HasInstance())
                {
                    TempData.CreateInstance();
                }
            }

            [SetUp]
            public void SetUp()
            {
                _testGameObj = new MonoBehaviourTest<ShopTimeImplTestCore>(false);
                _core = _testGameObj.component;
            }


            [OneTimeTearDown]
            public void OneTimeTearDown()
            {
                TempData.DestroyInstance();
            }


            [UnityTest]
            public IEnumerator 正常に更新が走るか()
            {
                // EventTimerは 「通知 → 更新」という順番なので更新では前の値が返ってくることに注意

                // 60秒
                {
                    _core.StartTimer(60);

                    yield return _core.WaitForUpdate();

                    Assert.AreEqual(59, _core.Impl.RemSecondTime, 1, "一度目の更新処理では大きくても誤差１秒以内のはず");

                    yield return _core.WaitForUpdate();

                    Assert.AreNotEqual(60, _core.Impl.RemSecondTime, "二度目の更新時点では60秒ではないはず");
                }

                // 1秒
                {
                    _core.StartTimer(1);

                    while (!_core.IsCompleted)
                    {
                        yield return _core.WaitForUpdate();
                    }

                    Assert.True(_core.IsCompleted, "OnCompledが呼び出されて終了フラグがtrueになっているはず");
                }
            }

            [UnityTest]
            public IEnumerator Has系メソッドの機能テスト()
            {
                // 丁寧に処理したいのでTestCasesは使用しない

                // 一日以上
                {
                    _core.StartTimer(TimeUtil.DAY_SECOND);

                    Assert.True(_core.Impl.HasDay,       "１日以上の判定にはなる");
                    Assert.True(_core.Impl.HasHour,      "１時間以上の判定になる");
                    Assert.True(_core.Impl.HasMinute,    "１分以上の判定になる");
                    Assert.True(_core.Impl.HasSecond,    "１秒以上の判定になる");
                }

                // 一日から一秒引いてみる
                {
                    _core.StartTimer(TimeUtil.DAY_SECOND - 1);

                    Assert.False(_core.Impl.HasDay,      "１日以上の判定にならない");
                    Assert.True(_core.Impl.HasHour,      "１時間以上の判定になる");
                    Assert.True(_core.Impl.HasMinute,    "１分以上の判定になる");
                    Assert.True(_core.Impl.HasSecond,    "１秒以上の判定になる");
                }

                // 一時間
                {
                    _core.StartTimer(TimeUtil.HOUR_SECOND);

                    Assert.False(_core.Impl.HasDay,      "１日以上の判定にならない");
                    Assert.True(_core.Impl.HasHour,      "１時間以上の判定になる");
                    Assert.True(_core.Impl.HasMinute,    "１分以上の判定になる");
                    Assert.True(_core.Impl.HasSecond,    "１秒以上の判定になる");
                }

                // 一分
                {
                    _core.StartTimer(TimeUtil.MINUTE_SECOND);

                    Assert.False(_core.Impl.HasDay,      "１日以上の判定にならない");
                    Assert.False(_core.Impl.HasHour,     "１時間以上の判定にならない");
                    Assert.True(_core.Impl.HasMinute,    "１分以上の判定になる");
                    Assert.True(_core.Impl.HasSecond,    "１秒以上の判定になる");
                }

                // 一度更新処理が呼ばれてから確認
                {
                    _core.StartTimer(TimeUtil.MINUTE_SECOND);

                    yield return _core.WaitForUpdate();

                    Assert.False(_core.Impl.HasDay,      "１日以上の判定にならない");
                    Assert.False(_core.Impl.HasHour,     "１時間以上の判定にならない");
                    Assert.False(_core.Impl.HasMinute,   "１分以上の判定にならない");
                    Assert.True(_core.Impl.HasSecond,    "１秒以上の判定になる");
                }

                // 一秒
                {
                    _core.StartTimer(1);

                    Assert.False(_core.Impl.HasDay,      "１日以上の判定にならない");
                    Assert.False(_core.Impl.HasHour,     "１時間以上の判定にならない");
                    Assert.False(_core.Impl.HasMinute,   "１分以上の判定にならない");
                    Assert.True(_core.Impl.HasSecond,    "１秒以上の判定になる");
                }

                _core.ForceFinish();

                yield return _testGameObj;
            }

            [UnityTest]
            public IEnumerator 基準点が問題ないか()
            {
                long ConvertSeconds(int days, int hours, int minutes, int seconds)
                {
                    return
                        days * TimeUtil.DAY_SECOND +
                        hours * TimeUtil.HOUR_SECOND +
                        minutes * TimeUtil.MINUTE_SECOND +
                        seconds * 1;
                }

                // １日２時間３分４秒
                {
                    _core.StartTimer(ConvertSeconds(1, 2, 3, 4));

                    // 日基準 : １日２時間３分４秒
                    Assert.AreEqual(1, _core.Impl.Days(ShopTimeImpl.PointType.Day));
                    Assert.AreEqual(2, _core.Impl.Hours(ShopTimeImpl.PointType.Day));
                    Assert.AreEqual(3, _core.Impl.Minutes(ShopTimeImpl.PointType.Day));
                    Assert.AreEqual(4, _core.Impl.Seconds(ShopTimeImpl.PointType.Day));

                    // 時間基準 : ０日２６時間３分４秒
                    Assert.AreEqual(0, _core.Impl.Days(ShopTimeImpl.PointType.Hour));
                    Assert.AreEqual(26, _core.Impl.Hours(ShopTimeImpl.PointType.Hour));
                    Assert.AreEqual(3, _core.Impl.Minutes(ShopTimeImpl.PointType.Hour));
                    Assert.AreEqual(4, _core.Impl.Seconds(ShopTimeImpl.PointType.Hour));

                    // 分基準 : ０日０時間１５６３分４秒
                    Assert.AreEqual(0, _core.Impl.Days(ShopTimeImpl.PointType.Minutes));
                    Assert.AreEqual(0, _core.Impl.Hours(ShopTimeImpl.PointType.Minutes));
                    Assert.AreEqual(1563, _core.Impl.Minutes(ShopTimeImpl.PointType.Minutes));
                    Assert.AreEqual(4, _core.Impl.Seconds(ShopTimeImpl.PointType.Minutes));

                    // 秒基準 : ０日０時間０分９３７８４秒
                    Assert.AreEqual(0, _core.Impl.Days(ShopTimeImpl.PointType.Second));
                    Assert.AreEqual(0, _core.Impl.Hours(ShopTimeImpl.PointType.Second));
                    Assert.AreEqual(0, _core.Impl.Minutes(ShopTimeImpl.PointType.Second));
                    Assert.AreEqual(93784, _core.Impl.Seconds(ShopTimeImpl.PointType.Second));
                }

                _core.ForceFinish();

                yield return _testGameObj;
            }
        }
    }
}
#endif