#if UNITY_EDITOR && CYG_DEBUG

using NUnit.Framework;

namespace Gallop
{
    namespace Test
    {
        /// <summary>
        /// TimeUtil検証用
        /// </summary>
        public static class TestTimeUtil
        {
            [Category(TestDefine.TEST_CATEGORY_SYSTEM)]
            [TestCase("2019-08-01 10:00:15", 1564621215), 
                TestCase("2018-03-12 02:42:33", 1520790153), 
                TestCase("0000-00-00 00:00:00", 0), 
                TestCase(null, 0)]
            public static void StringToUnixTime(
                string strTime,             // 入力 : 時間を表す文字列
                long retValue)              // 出力 : 経過時間値
            {
                long convertedUnixTime = Gallop.TimeUtil.ToUnixTime(strTime);
                Assert.That(convertedUnixTime, Is.EqualTo(retValue));
            }
        }
    }
}

#endif