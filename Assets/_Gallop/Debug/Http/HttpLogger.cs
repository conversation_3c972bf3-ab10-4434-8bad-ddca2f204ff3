using System.Collections.Generic;
using System.Linq;
using System;

namespace Gallop
{
    static class HttpLogger
    {
        public const int QUEUE_SIZE = 30;
        private static Queue<string> _logs = new Queue<string>(QUEUE_SIZE);

        public static string[] GetLogArray()
        {
            return _logs.ToArray();
        }

        private static void AddLog(string log)
        {
            if (_logs.Count >= QUEUE_SIZE)
            {
                _logs.Dequeue();
            }
            
            _logs.Enqueue(log);
        }
        
        public static void OnRequest<T>(T request) where T : RequestCommon
        {
            string dateTimeText = DateTime.Now.ToString("HH:mm:ss.ff");
            AddLog( AddColorTag($"{dateTimeText} : Request - {request.GetType().FullName}", ColorTagType.Request) );
        }
        
        public static void OnResponse<T>(T response) where T : ResponseCommon
        {
            string dateTimeText = DateTime.Now.ToString("HH:mm:ss.ff");
            AddLog( AddColorTag($"{dateTimeText} : Response - {response.GetType().FullName}", ColorTagType.Response) );
        }

        enum ColorTagType
        {
            Request,
            Response,
        }

        private static string AddColorTag(string baseText, ColorTagType type)
        {
            switch (type)
            {
                case ColorTagType.Request:
                    return $"<color=lightblue>{baseText}</color>";
                case ColorTagType.Response:
                    return $"<color=lime>{baseText}</color>";
            }

            return baseText;
        }
    }
}