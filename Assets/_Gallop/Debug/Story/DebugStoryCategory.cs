#if CYG_DEBUG
using System;
using System.Collections.Generic;
namespace Gallop
{
    public static class DebugStoryCategory
    {
        /// <summary> カテゴリータイプ </summary>
        /// <remarks> 基本的にはstoryIdの先頭2桁が該当。今後追加される可能性もあり </remarks>
        public enum CategoryType
        {
            ShortStory = 0,               // ショートストーリー
            Tutorial = 1,                 // チュートリアル
            MainStory = 2,                // メインストーリー
            CharaStory = 4,               // ウマ娘ストーリー
            SingleModePrologue = 8,       // 育成プロローグ
            StoryEvent = 9,               // ストーリーイベント
            Campaign = 10,                // キャンペーン
            CampaignValentine = 11,       // キャンペーン(バレンタイン)
            CampaignRaffle = 12,          // キャンペーン(おみくじ)
            CampaignChristmas = 13,       // キャンペーン(クリスマス)
            SingleModeCharaCommon = 40,   // 育成(汎用)
            SingleModeCharaSpecific = 50, // 育成(キャラ固有)
            SingleModeSupportCommon = 80, // サポカイベント(汎用)
            SingleModeSupportR = 81,      // サポカイベント(R)。多分アセットとしては存在していない
            SingleModeSupportSR = 82,     // サポカイベント(SR)
            SingleModeSupportSSR = 83,    // サポカイベント(SSR)
        }

        private static StoryCategoryRuntimeInfo _storyCategoryInfo;

        public static StoryCategoryInfo.CategoryData GetCategoryData(int category)
        {
            if (_storyCategoryInfo == null)
            {
                _storyCategoryInfo = new StoryCategoryRuntimeInfo();
            }

            if (_storyCategoryInfo.Dict.TryGetValue(category, out var categoryInfo))
            {
                return categoryInfo;
            }

            return null;
        }

        /// <summary>
        /// カテゴリーIDから表示用のテキストを取得する
        /// </summary>
        /// <param name="categoryId"></param>
        /// <returns></returns>
        public static string GetCategoryTextFromCategoryId(int categoryId)
        {
            if (!Enum.IsDefined(typeof(CategoryType), categoryId))
            {
                Debug.LogError($"未定義のcategoryIdです: categoryId: {categoryId})");
                return string.Empty;
            }

            return ((CategoryType)categoryId).GetEnumDisplayName();
        }

        /// <summary>
        /// サポカイベントのカテゴリ集合
        /// </summary>
        private static readonly HashSet<CategoryType> SUPPORT_CARD_EVENTS = new HashSet<CategoryType>
        {
            CategoryType.SingleModeSupportCommon,
            CategoryType.SingleModeSupportR,
            CategoryType.SingleModeSupportSR,
            CategoryType.SingleModeSupportSSR,
        };

        /// <summary>
        /// カテゴリIDがサポカイベントに該当するか判定する
        /// </summary>
        /// <param name="categoryId">カテゴリID</param>
        /// <returns>サポカイベントなら true を返す。そうでなければ false を返す。</returns>
        public static bool IsSupportCardEvent(int categoryId)
        {
            if (!Enum.IsDefined(typeof(CategoryType), categoryId))
            {
                Debug.LogError($"未定義のcategoryIdです: {categoryId}");
                return false;
            }

            return SUPPORT_CARD_EVENTS.Contains((CategoryType)categoryId);
        }
    }
}
#endif
