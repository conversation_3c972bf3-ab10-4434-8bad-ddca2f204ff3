using System;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 衣装アイコンキャプチャ保存情報
    /// </summary>
    [Serializable]
    public class DressIconCaptureSetting : ScriptableObject
    {
        // カメラに関するもの
        public Vector3 CameraPos;
        public Quaternion CameraRot;
        public float CameraFOV;

        // Sceneに配置されたDirectionalLightに関するもの
        public Quaternion LightRot;
        public Vector3 LightPos;
        public Color LightColor;
        public LightType LightType;

        /// <summary>
        /// 肌色
        /// </summary>
        public Color SkinColor;
    }
}
