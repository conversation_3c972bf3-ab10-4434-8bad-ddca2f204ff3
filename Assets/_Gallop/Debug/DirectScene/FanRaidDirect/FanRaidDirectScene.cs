using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
#if CYG_DEBUG
    public class FanRaidDirectScene : DirectSceneBase
    {
        public ButtonCommon StartButton => _startButton;
        [SerializeField] private ButtonCommon _startButton;

        // top_data
        public InputFieldCommon TopGroupIdEventIdInput => _topGroupIdEventIdInput;
        [SerializeField] private InputFieldCommon _topGroupIdEventIdInput;
        public DropDownCommon TopGroupIdDropDown => _topGroupIdDropDown;
        [SerializeField] private DropDownCommon _topGroupIdDropDown;

        // 全体報酬達成ダイアログ
        public DropDownCommon GetAllFanRewardDropDown => _getAllFanRewardDropDown;
        [SerializeField] private DropDownCommon _getAllFanRewardDropDown;
        public ToggleCommon GetAllFanRewardToggle => _getAllFanRewardToggle;
        [SerializeField] private ToggleCommon _getAllFanRewardToggle;
    }

    public class FanRaidDirectSceneController : SceneControllerBase<FanRaidDirectScene>
    {
        private const string TOP_GROUP_NONE_TEXT = "指定なし";
        private const string GET_ALL_REWARD_NONE_TEXT = "表示しない";
        
        public override IEnumerator InitializeScene()
        {
            _scene.StartButton.SetOnClick(OnClickStartButton);
            InitializeTopGroupToggle();
            InitializeGetAllFanRewardDialog();
            
            return base.InitializeScene();
        }

        /// <summary>
        /// イベントトップへ
        /// </summary>
        private void OnClickStartButton()
        {
            var viewInfo = new FanRaidViewController.ViewInfo();
            
            // TopGroupIdの指定
            if (_scene.TopGroupIdDropDown.captionText.text != TOP_GROUP_NONE_TEXT)
            {
                viewInfo.DebugTopGroupId = Int32.Parse(_scene.TopGroupIdDropDown.captionText.text);
            }

            // 全体報酬獲得条件達成ダイアログ関連
            if (_scene.GetAllFanRewardDropDown.captionText.text != GET_ALL_REWARD_NONE_TEXT)
            {
                var itemNum = Int32.Parse(_scene.GetAllFanRewardDropDown.captionText.text);
                viewInfo.DebugGetAllFanRewardIdList = Enumerable.Range(1, itemNum).ToList();
                viewInfo.DebugIsGotAllFanLastReward = _scene.GetAllFanRewardToggle.isOn;
            }
            
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.FanRaid, viewInfo);
        }

        #region top_group_idの指定

        private void InitializeTopGroupToggle()
        {
            _scene.TopGroupIdEventIdInput.SetOnEndEdit(SetupDropDown);
            
            SetupDropDown();
        }

        private void SetupDropDown()
        {
            // 状態をクリアして指定なしを入れておく
            _scene.TopGroupIdDropDown.ClearOptions();
            _scene.TopGroupIdDropDown.AddOptions(new List<Dropdown.OptionData>()
            {
                new Dropdown.OptionData(TOP_GROUP_NONE_TEXT),
            });
            _scene.TopGroupIdDropDown.value = 0;
            
            // 対応するTopGroupIdを指定
            int eventId = 1001;
            if (!Int32.TryParse(_scene.TopGroupIdEventIdInput.text, out eventId))
            {
                // パース失敗
                Debug.LogWarning("数値を入力してください");
                return;
            }
            
            var topGroupIdList = MasterDataManager.Instance.masterFanRaidTopData
                .GetListWithFanRaidId(eventId)
                ?.Select(data => new Dropdown.OptionData(data.Id.ToString()))
                .ToList();

            if (topGroupIdList == null)
            {
                return;
            }

            _scene.TopGroupIdDropDown.AddOptions(topGroupIdList);
        }

        #endregion

        #region 全体報酬獲得条件達成ダイアログ関連

        private void InitializeGetAllFanRewardDialog()
        {
            _scene.GetAllFanRewardDropDown.ClearOptions();
            _scene.GetAllFanRewardDropDown.AddOptions(new List<Dropdown.OptionData>()
            {
                new Dropdown.OptionData(GET_ALL_REWARD_NONE_TEXT),
                new Dropdown.OptionData("1"),
                new Dropdown.OptionData("4"),
                new Dropdown.OptionData("5"),
                new Dropdown.OptionData("10"),
            });
        }

        #endregion
    }

#endif
}