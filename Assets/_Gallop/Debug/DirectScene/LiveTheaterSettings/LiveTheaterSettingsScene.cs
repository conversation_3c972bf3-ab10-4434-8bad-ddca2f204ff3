using System.Linq;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using static Gallop.StaticVariableDefine.LiveTheater.LiveTheaterViewController;

namespace Gallop
{
#if CYG_DEBUG

    /// <summary>
    /// ライブシアター：配置設定シーン
    /// </summary>
    public class LiveTheaterSettingsScene : DirectSceneBase
    {
        public ButtonCommon PlayButton;
        public ButtonCommon MovieDeleteButton;
        public ToggleGroupCommon ToggleGroup;
    }

    public class LiveTheaterSettingsSceneController : SceneControllerBase<LiveTheaterSettingsScene>
    {

        private Coroutine _deleteCoroutine;

        public override IEnumerator InitializeScene()
        {
            _scene.PlayButton.SetOnClick(ChangeSceneTheater);
            _scene.MovieDeleteButton.SetOnClick(DeleteDownloadedAllMovies);
            return base.InitializeScene();
        }

        private void ChangeSceneTheater()
        {
            if (_deleteCoroutine != null)
                return;

            //デバッグ設定で開始
            var index = _scene.ToggleGroup.GetOnIndex();
            switch (index)
            {
                case 0:
                    DebugLiveIdArray = MasterDataManager.Instance.masterLiveData.dictionary
                        .Where(d => d.Value.HasLive == (int)MasterLiveData.SongType.HasLive)
                        .Select(d => (int)d.Key).ToArray();
                    break;
                case 1:
                    DebugLiveIdArray = new int[] { 1006, 1008, 1009};
                    break;
                case 2:
                    DebugLiveIdArray = new int[] { 1006, 1008 };
                    break;
                case 3:
                    DebugLiveIdArray = new int[] { 1006 };
                    break;
            }
            
            SceneManager.Instance.ChangeView(SceneDefine.ViewId.LiveTheater);
        }

        //DL済みムービーの削除
        private void DeleteDownloadedAllMovies()
        {
            var pathList = new HashSet<string>();
            foreach(var data in MasterDataManager.Instance.masterLiveData.dictionary)
            {
                var path = ResourcePath.GetLiveSampleMoviePath(data.Key);
                path = ResourcePath.MovieLoadPathToDownloadPath(path);
                pathList.Add(path.ToLower());
                //音源は別ファイル
                var cueSheetPath = ResourcePath.GetSongSampleCueSheetPath(data.Key);
                var cueSheetPathList = AudioManager.GetCueSheetPathList(new List<string>() { cueSheetPath }, AudioManager.SubFolder.Live);
                foreach(var sheet in cueSheetPathList)
                {
                    pathList.Add(sheet.ToLower());
                }
            }

            if(_deleteCoroutine != null)
            {
                _scene.StopCoroutine(_deleteCoroutine);
            }
            _deleteCoroutine = _scene.StartCoroutine(RemoveABCoroutine(pathList));
        }

        private IEnumerator RemoveABCoroutine(IEnumerable<string> list)
        {
            yield return AssetManager.LocalFile.DeleteFilesAsync(list);
            _deleteCoroutine = null;
        }
    }


#endif
}