#if CYG_DEBUG
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using Gallop.CutIn;
using Gallop.CutIn.Cutt;
using Gallop.Model.Component;

namespace Gallop
{
    /// <summary>
    /// レジェンドレースダイレクト VS導入 VS結果
    /// </summary>
    public class LegendVersusDirectScene : DirectSceneBase
    {
        [field: SerializeField, RenameField]
        public Canvas Canvas { get; private set; } = null;
        [field: SerializeField, RenameField]
        public GameObject TextNodePrefab { get; private set; } = null;
        [field: SerializeField, RenameField]
        public RectTransform ContentsRoot { get; private set; } = null;
        [field: SerializeField, RenameField]
        public GameObject FlashRoot { get; private set; } = null;
        [field: SerializeField, RenameField]
        public GameObject UIRoot { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button BackButton { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Dropdown ModeDropdown { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Dropdown RaceResultDropdown { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button PlayButton { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button RandomCharaButton { get; private set; } = null;
        [field: SerializeField, RenameField]
        public GameObject CharaListObject { get; private set; } = null;
        [field: SerializeField, RenameField]
        public RectTransform CharaListContent { get; private set; } = null;
        [field: SerializeField, RenameField]
        public GameObject DressListObject { get; private set; } = null;
        [field: SerializeField, RenameField]
        public RectTransform DressListContent { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button LegendCharaButton { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button LegendDressButton { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button PlayerCharaButton { get; private set; } = null;
        [field: SerializeField, RenameField]
        public Button PlayerDressButton { get; private set; } = null;

    }

    public class LegendVersusDirectSceneController : SceneControllerBase<LegendVersusDirectScene>
    {
        #region 定義・定数
        /// <summary>
        /// キャラのセットアップ情報
        /// </summary>
        private class CharaData
        {
            private const int DEFAULT_DRESS_ID = (int)ModelLoader.DressID.SRCommon;
            public int CardId { get; private set; }
            public int CharaId { get; private set; }
            public int DressId { get; private set; }
            public Button CharaButton { get; private set; } = null;
            public Text CharaButtonText { get; private set; } = null;
            public Button DressButton { get; private set; } = null;
            public Text DressButtonText { get; private set; } = null;

            public void SetButton(Button charaButton, 
                                  System.Action<int> onClickCharaButton, 
                                  Button dressButton, 
                                  System.Action<int> onClickDressButton,
                                  int index)
            {                
                CharaButton = charaButton;
                if (CharaButton != null)
                {
                    CharaButton.onClick.RemoveAllListeners();
                    CharaButton.onClick.AddListener(() => onClickCharaButton(index));

                    CharaButtonText = CharaButton.GetComponentInChildren<Text>();
                }

                DressButton = dressButton;
                if (DressButton != null)
                {
                    DressButton.onClick.RemoveAllListeners();
                    DressButton.onClick.AddListener(() => onClickDressButton(index));

                    DressButtonText = DressButton.GetComponentInChildren<Text>();
                }
            }

            public void SetCardId(int cardId, bool isSetDefaultDress)
            {
                CardId = cardId;
                var cardData = MasterDataManager.Instance.masterCardData.Get(CardId);
                if( cardData != null)
                {
                    CharaId = cardData.CharaId;
                    CharaButtonText.text = cardData.Id.ToString() + " : " + cardData.Name;

                    if (isSetDefaultDress)
                    {
                        SetDressId(DEFAULT_DRESS_ID);
                    }
                    else
                    {
                        bool isEnabled = false;
                        var dressData = MasterDataManager.Instance.masterDressData.Get(DressId);
                        if (dressData != null)
                        {
                            isEnabled = (dressData.CharaId == 0) || (dressData.CharaId == CharaId);
                        }
                        // 今のキャラに着れない衣装が設定されている場合は衣装を変更する。
                        if (!isEnabled)
                        {
                            SetDressId(DEFAULT_DRESS_ID);
                        }                        
                    }
                }
            }

            public void SetDressId(int dressId)
            {
                DressId = dressId;

                if (DressButtonText != null)
                {
                    var dressData = MasterDataManager.Instance.masterDressData.Get(DressId);
                    if (dressData != null)
                    {
                        DressButtonText.text = dressData.Id.ToString() + " : " + dressData.Name;
                    }
                    else
                    {
                        DressButtonText.text = DressId.ToString();
                    }
                }
            }

        }

        private enum CharaType
        {
            Legend,
            Player,
            Max
        }

        private enum Mode
        {
            Intro,  // VS導入画面
            Result, // VS結果画面
            Max
        }

        private enum RaceResult
        {
            LegendWin,  // レジェンドの勝利
            PlayerWin,  // プレイヤーの勝利
            Draw,       // 引き分け
            Max
        }

        private enum UpdateStep
        {
            Initialize,  // 初期化
            Select,      // Idle
            IntroStart,  // VS導入セットアップ
            IntroPlay,   // VS導入再生
            ResultStart, // VS結果セットアップ
            ResultPlay,  // VS結果再生
            Max
        }

        private static readonly string[] MODE_NAME_ARRAY = new string[(int)Mode.Max]
        {
            "VS導入",
            "VS結果",
        };

        private static readonly string[] RACE_RESULT_NAME_ARRAY = new string[(int)RaceResult.Max]
        {
            "レジェンドの勝利",
            "プレイヤーの勝利",
            "引き分け"
        };

        /// <summary>
        /// レジェンドレースの結果順位 0が1位
        /// RaceResult.LegendWin => 0 , 1
        /// RaceResult.PlayerWin => 1 , 0
        /// RaceResult.Draw => 1 , 1
        /// </summary>
        private static readonly int[,] RACE_RESULT_ORDER_ARRAY = new int[(int)RaceResult.Max, (int)CharaType.Max]
            {
                { 0 , 1 } ,
                { 1 , 0 } ,
                { 1 , 1 } ,
            };

        private const int DEFAULT_CARD_ID_00 = 100101;
        private const int DEFAULT_CARD_ID_01 = 100201;
        private const int DUMMY_RACE_INSTANCE_ID = 100101;
        private const string CUT_PARENT_OBJECT_NAME = "CharacterParent";
        private const string CUT_UI_OUT_COMMAND = "End";

        #endregion 定義・定数
        #region メンバ変数・プロパティ

        private CutInHelper _vsCutinHelper = null;   // VS導入再生用
        private RaceUIVsCutt _raceUIVsCutt = null;   // VS導入再生用
        private RaceResultList _raceResult = null;   // VS結果再生用
        private CharaData[] _charaDataArray = null;
        private PaddockModelController[] _modelContollerArray = new PaddockModelController[(int)CharaType.Max];
        private GameObject _modelControllerParentObject = null;

        private int _modeValue = 0;
        private int _raceResultValue = 0;
        private int _editCharaIndex = 0;

        private bool _isRequestPlay = false;
        private bool _isRequestRandomChara = false;
        private bool _isPlayedResult = false;
        private UpdateStep _updateStep = UpdateStep.Initialize;

        private List<int> _shuffleCharaIdList = null;

        private GameObject _resultRankFlashPrefab = null;
        private RegisterInfo _resInfoResultRankFlashPrefab = null;

        #endregion メンバ変数・プロパティ
        #region メソッド
        #region override

        public override void RegisterDownload(DownloadPathRegister register)
        {
            base.RegisterDownload(register);
            RaceUIVsCutt.RegisterDownloadAssets(register);
            ShadowController.RegisterDownload(register);
            DialogSingleModeRaceConfirm.RegisterDownload(register);
            _resInfoResultRankFlashPrefab = register.RegisterPath(ResourcePath.RESULT_RANK_FLASH_PREFAB_PATH);
        }

        public override IEnumerator InitializeScene()
        {
            UIManager.Instance.AdjustContentsRootRect(_scene.ContentsRoot);
            DirectionalLightManager.Instance.SetEnable(true);

            _scene.Canvas.gameObject.SetActive(true);
            _scene.Canvas.worldCamera = UIManager.UICamera;
            _scene.Canvas.planeDistance = 0f;
            _scene.Canvas.sortingLayerName = UIManager.UI_SORTING_LAYER_NAME;

            _scene.BackButton.onClick.RemoveAllListeners();
            _scene.BackButton.onClick.AddListener(() =>
            {
                CloseListAll();
            });

            // 再生ボタン
            _scene.PlayButton.onClick.RemoveAllListeners();
            _scene.PlayButton.onClick.AddListener(() => _isRequestPlay = true);
            _scene.PlayButton.SetActiveWithCheck(true);
            _scene.PlayButton.interactable = false;

            // キャラランダム設定ボタン
            _scene.RandomCharaButton.onClick.RemoveAllListeners();
            _scene.RandomCharaButton.onClick.AddListener(() => _isRequestRandomChara = true);
            _scene.RandomCharaButton.SetActiveWithCheck(true);
            _scene.RandomCharaButton.interactable = false;

            // 候補リスト
            _scene.CharaListObject.SetActiveWithCheck(false);
            _scene.DressListObject.SetActiveWithCheck(false);
            
            // キャラセットアップ情報
            _charaDataArray = new CharaData[(int)CharaType.Max];
            for (int i = 0; i < _charaDataArray.Length; i++)
            {
                _charaDataArray[i] = new CharaData();
            }
            _charaDataArray[(int)CharaType.Legend].SetButton(_scene.LegendCharaButton, OnClickCharaButton, _scene.LegendDressButton, OnClickDressButton , 0);
            _charaDataArray[(int)CharaType.Player].SetButton(_scene.PlayerCharaButton, OnClickCharaButton, _scene.PlayerDressButton, OnClickDressButton , 1);
            _charaDataArray[(int)CharaType.Legend].SetCardId(DEFAULT_CARD_ID_00, true);
            _charaDataArray[(int)CharaType.Player].SetCardId(DEFAULT_CARD_ID_01, true);

            // マスターデータの順番のままのCharaIDリストを作成しておく。
            _shuffleCharaIdList = MasterDataManager.Instance.masterCardData.dictionary.Keys.ToList();

            // モードボタン初期化
            if (_scene.ModeDropdown != null)
            {
                _scene.ModeDropdown.options.Clear();
                for (int i = 0; i < (int)Mode.Max; ++i)
                {
                    _scene.ModeDropdown.options.Add(new Dropdown.OptionData(MODE_NAME_ARRAY[i]));
                }
                _scene.ModeDropdown.value = _modeValue;
                _scene.ModeDropdown.RefreshShownValue();
                _scene.ModeDropdown.onValueChanged.AddListener((value) =>{ _modeValue = value; });
            }

            // リザルトボタン初期化
            if (_scene.RaceResultDropdown != null)
            {
                _scene.RaceResultDropdown.options.Clear();
                for (int i = 0; i < (int)RaceResult.Max; ++i)
                {
                    _scene.RaceResultDropdown.options.Add(new Dropdown.OptionData(RACE_RESULT_NAME_ARRAY[i]));
                }
                _scene.RaceResultDropdown.value = _raceResultValue;
                _scene.RaceResultDropdown.RefreshShownValue();
                _scene.RaceResultDropdown.onValueChanged.AddListener((value) => { _raceResultValue = value; });

            }

            _resultRankFlashPrefab = _resInfoResultRankFlashPrefab.Load<GameObject>();

            return base.InitializeScene();
        }

        public override void UpdateScene()
        {
            base.UpdateScene();
            UpdateInternal();
        }

        public override void LateUpdateScene()
        {
            base.LateUpdateScene();
            LateUpdateInternal();
        }

        public override void FinalizeScene()
        {
            Destroy();
        }

        #endregion override
        #region private

        /// <summary>
        /// 候補リストの生成 及び ボタンアクション登録
        /// </summary>
        private void CreateCharaList()
        {
            var charaListTransform = _scene.CharaListContent;

            foreach (Transform child in charaListTransform)
            {
                Object.Destroy(child.gameObject);
            }

            var masterCardData = MasterDataManager.Instance.masterCardData;
            // キーによる昇順ソート
            var sorted = masterCardData.dictionary.OrderBy((x) => x.Key);
            foreach (var v in sorted)
            {
                var item = Object.Instantiate(_scene.TextNodePrefab, charaListTransform, false);
                item.SetActive(true);

                var text = item.GetComponentInChildren<Text>();
                text.text = v.Key.ToString() + " : " + v.Value.Name;

                var button = item.GetComponent<Button>();
                button.onClick.AddListener(() =>
                {
                    int cardId = v.Key;
                    SetCardId(_editCharaIndex, cardId);
                });
            }            
        }

        /// <summary>
        /// キャラボタン押下コールバック
        /// </summary>
        private void OnClickCharaButton(int index)
        {
            _editCharaIndex = index;
            _scene.CharaListObject.SetActiveWithCheck(true);
            _scene.DressListObject.SetActiveWithCheck(false);
        }

        /// <summary>
        /// 衣装ボタン押下コールバック
        /// </summary>
        private void OnClickDressButton(int index)
        {
            _editCharaIndex = index;
            _scene.CharaListObject.SetActiveWithCheck(false);
            _scene.DressListObject.SetActiveWithCheck(true);

            var charaData = _charaDataArray[index];
            var dressListTransform = _scene.DressListContent;

            foreach (Transform child in dressListTransform)
            {
                GameObject.Destroy(child.gameObject);
            }
            var masterDressData = MasterDataManager.Instance.masterDressData.dictionary.Where(pair => pair.IsDressAvailable(charaData.CharaId));
            // キーによる昇順ソート
            var sorted = masterDressData.OrderBy((x) => x.Key);
            foreach (var v in sorted)
            {
                var dressData = v.Value;              
                var item = Object.Instantiate(_scene.TextNodePrefab, dressListTransform, false);
                item.SetActive(true);

                var text = item.GetComponentInChildren<Text>();
                text.text = v.Key.ToString() + " : " + v.Value.Name;

                var button = item.GetComponent<Button>();
                button.onClick.AddListener(() =>
                {
                    int dressId = v.Key;
                    SetDressId(_editCharaIndex, dressId);
                });
            }
        }

        private void SetCardId(int index, int cardId)
        {
            if (_charaDataArray.Length < index)
            {
                return;
            }
            _charaDataArray[index].SetCardId(cardId, false);
        }

        private void SetDressId(int index, int dressId)
        {
            if (_charaDataArray.Length < index)
            {
                return;
            }
            _charaDataArray[index].SetDressId(dressId);
        }

        private void CloseListAll()
        {
            _scene.CharaListObject.SetActiveWithCheck(false);
            _scene.DressListObject.SetActiveWithCheck(false);
        }

        private void UpdateInternal()
        {
            switch (_updateStep)
            {
                case UpdateStep.Initialize:
                    CreateCharaList();
                    _updateStep = UpdateStep.Select;
                    _scene.UIRoot.SetActive(true);
                    _scene.FlashRoot.SetActive(false);
                    _scene.PlayButton.interactable = true;
                    _scene.RandomCharaButton.interactable = true;
                    break;
                case UpdateStep.Select:

                    if (_isRequestPlay)
                    {
                        _isRequestPlay = false;
                        _isRequestRandomChara = false;
                        _scene.UIRoot.SetActive(false);
                        _scene.FlashRoot.SetActive(true);
                        _updateStep = (_modeValue == (int)Mode.Intro) ? UpdateStep.IntroStart : UpdateStep.ResultStart;
                        break;
                    }

                    if (_isRequestRandomChara)
                    {
                        UpdateCharaIdRandom();
                        CloseListAll();
                        _isRequestRandomChara = false;
                    }
                    break;
                case UpdateStep.IntroStart:
                    {
                        SetupIntro();                        
                        _updateStep = UpdateStep.IntroPlay;
                    }
                    break;
                case UpdateStep.IntroPlay:
                    _vsCutinHelper.AlterUpdate();
                    break;
                case UpdateStep.ResultStart:
                    {
                        SetupResult();
                        _updateStep = UpdateStep.ResultPlay;
                    }
                    break;
                case UpdateStep.ResultPlay:
                    _raceResult.UpdateLegendRaceResultCutt();
                    if(_isPlayedResult && Input.GetMouseButtonDown(0))
                    {
                        OnClickResult();
                    }
                    break;
                default:
                    break;
            }
        }
        

        private void LateUpdateInternal()
        {
            switch (_updateStep)
            {
                case UpdateStep.IntroPlay:
                    _vsCutinHelper.AlterLateUpdate();
                    break;
                case UpdateStep.ResultPlay:
                    _raceResult.LateUpdateLegendRaceResultCutt();
                    break;
                default:
                    break;
            }
        }

        private void Destroy()
        {
            // VSカットイン用モデルは削除する。
            for (int i = 0; i < _modelContollerArray.Length; i++)
            {
                if (_modelContollerArray[i] != null)
                {
                    GameObject.Destroy(_modelContollerArray[i].gameObject);
                    _modelContollerArray[i] = null;
                }
            }
        }

        private void ResetScene()
        {
            _updateStep = UpdateStep.Select;
            _scene.UIRoot.SetActive(true);
            _scene.FlashRoot.SetActive(false);
            _scene.PlayButton.interactable = true;
            _scene.RandomCharaButton.interactable = true;
            _isPlayedResult = false;
        }

        /// <summary>
        /// キャラをランダムに設定する
        /// </summary>
        private void UpdateCharaIdRandom()
        {
            // CharaIDリストの中身をシャッフルする。
            GallopUtil.ListShuffleNonAlloc(_shuffleCharaIdList);

            for (int i = 0; i < _charaDataArray.Length; i++)
            {
                _charaDataArray[i].SetCardId(_shuffleCharaIdList[i], true);
            }
        }

        /// <summary>
        /// モデル生成 CutInHelperへ登録用
        /// </summary>
        private ModelController GetUserModelControllerAction(CutInCharacterCreateInfo info)
        {
            if( info._charaIndex >= _charaDataArray.Length)
            {
                return null;
            }
  
            if (_modelControllerParentObject == null)
            {
                _modelControllerParentObject = new GameObject(CUT_PARENT_OBJECT_NAME);
            }

            var buildInfo = new CharacterBuildInfo(_charaDataArray[info._charaIndex].CharaId, _charaDataArray[info._charaIndex].DressId, ModelLoader.ControllerType.Paddock);
            _modelContollerArray[info._charaIndex] = ModelLoader.CreateModel(buildInfo).GetComponent<PaddockModelController>();
            _modelContollerArray[info._charaIndex].SetTimelineParentObject(_modelControllerParentObject.transform);
            _modelContollerArray[info._charaIndex].CacheTransform.parent = _modelControllerParentObject.transform;
            _modelContollerArray[info._charaIndex].CacheTransform.localPosition = Math.VECTOR3_ZERO;
            _modelContollerArray[info._charaIndex].CacheTransform.localRotation = Math.QUATERNION_IDENTITY;
            _modelContollerArray[info._charaIndex].ApplyCySpring();
            _modelContollerArray[info._charaIndex].ResetCyspring();
            _modelContollerArray[info._charaIndex].ReserveWarmingUpCySpring();
            _modelContollerArray[info._charaIndex].SetEyeControllerLocalPositionMode(false);
            _modelContollerArray[info._charaIndex].SetShadow(ModelLoader.ShadowType.Normal);
            _modelContollerArray[info._charaIndex].UpdateNosePretenseThreshold(0f);

            return _modelContollerArray[info._charaIndex];
        }

        private void SetupIntro()
        {
            var rivalRaceCutInHelper = new RivalRaceCutInHelper();
            var context = new RivalRaceCutInHelper.Context();
            // Cutt上では相手側が0番目
            context.CharaIdList.Add(_charaDataArray[(int)CharaType.Legend].CharaId);
            context.CharaIdList.Add(_charaDataArray[(int)CharaType.Player].CharaId);
            context.DressIdList.Add(_charaDataArray[(int)CharaType.Legend].DressId);
            context.DressIdList.Add(_charaDataArray[(int)CharaType.Player].DressId);
            rivalRaceCutInHelper.Init(context);

            _vsCutinHelper = rivalRaceCutInHelper;
            _vsCutinHelper.GetUserModelControllerAction += GetUserModelControllerAction;
            _vsCutinHelper.OnEndAction += OnEndIntro;
            _vsCutinHelper.Play(ResourcePath.VS_CUTTIN_PATH, SceneManager.Instance.GetCurrentSceneController().GetSceneTransform());
            _vsCutinHelper.TimelineController.OnSimpleCommand += OnSimpleCommand;

            _raceUIVsCutt = GameObject.Instantiate<GameObject>(ResourceManager.LoadOnView<GameObject>(ResourcePath.VS_UI_PREFAB_PATH), _scene.ContentsRoot).GetComponent<RaceUIVsCutt>();            
            HorseDataDirect player = new HorseDataDirect(_charaDataArray[(int)CharaType.Player].CharaId, 0/*nicnameId*/);
            HorseDataDirect legend = new HorseDataDirect(_charaDataArray[(int)CharaType.Legend].CharaId, 0/*nicnameId*/);
            _raceUIVsCutt.LoadResources(player, legend);
            _raceUIVsCutt.Play();
        }

        private void SetupResult()
        {
            RaceInfo info = new RaceInfo(RaceDefine.RaceType.Legend, DUMMY_RACE_INSTANCE_ID);
            // #130909 レースのセットアップで参照するため予め設定しておく
            RaceManager.RaceInfo = info;
            _raceResult = RaceResultList.ShowList(
                info,
                _resultRankFlashPrefab,
                _scene.Canvas.transform,
                RaceDefine.RaceType.Legend);
            
            _raceResult.SetupLegendResultForDirect(
                _charaDataArray[(int)CharaType.Legend].CardId,
                _charaDataArray[(int)CharaType.Legend].DressId,
               RACE_RESULT_ORDER_ARRAY[_raceResultValue, (int)CharaType.Legend],
                _charaDataArray[(int)CharaType.Player].CardId,
                _charaDataArray[(int)CharaType.Player].DressId,
               RACE_RESULT_ORDER_ARRAY[_raceResultValue, (int)CharaType.Player],
                OnPlayResult);            
        }

        /// <summary>
        /// 終了コールバック CutInHelperに登録用
        /// </summary>
        private void OnEndIntro(CutInTimelineController timelineController)
        {
            // カットの削除
            GameObject.Destroy(timelineController.gameObject);
            timelineController = null;

            GameObject.Destroy(_raceUIVsCutt.gameObject);
            _raceUIVsCutt = null;

            // Introカットイン用モデルは削除する。
            for (int i = 0; i < _modelContollerArray.Length; i++)
            {
                if (_modelContollerArray[i] != null)
                {
                    GameObject.Destroy(_modelContollerArray[i].gameObject);
                    _modelContollerArray[i] = null;
                }
            }

            // モデルの親も削除
            GameObject.Destroy(_modelControllerParentObject);
            _modelControllerParentObject = null;

            ResetScene();
        }

        /// <summary>
        /// コマンドコールバック CutInHelperに登録用
        /// </summary>
        private void OnSimpleCommand(CuttEventParam_SimpleCommand param)
        {            
            if (param.StringParam.Equals(CUT_UI_OUT_COMMAND))
            {
                _raceUIVsCutt.Out();
            }
        }

        /// <summary>
        /// 準備完了後のコールバック RaceResultListに登録用
        /// </summary>
        private void OnPlayResult()
        {
            _scene.StartCoroutine(_raceResult.PlayLegendRaceResultForDirect(() =>{ _isPlayedResult = true; }));
        }

        /// <summary>
        /// 「次へ」が押された際のコールバック RaceResultListに登録用
        /// </summary>
        private void OnClickResult()
        {
            _raceResult.Release();
            GameObject.Destroy(_raceResult.gameObject);
            _raceResult = null;

            ResetScene();
        }
        #endregion private
        #endregion メソッド
    }
}
#endif
