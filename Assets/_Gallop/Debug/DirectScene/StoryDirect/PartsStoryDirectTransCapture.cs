#if CYG_DEBUG && UNITY_EDITOR

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Gallop.RenderPipeline;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using CameraData = Gallop.RenderPipeline.CameraData;

namespace Gallop
{
    using static TransCaptureUtil;

    /// <summary>
    /// StoryDirectで透過背景キャプチャの設定を行うパーツ
    /// </summary>
    public class PartsStoryDirectTransCapture : PartsTransCaptureBase
    {
        #region 定数

        // 解像度はFHD
        private const int STORY_CAPTURE_SIZE_X = 1920;
        private const int STORY_CAPTURE_SIZE_Y = 1080;

        // ダウンサンプリングのレンダリング解像度のデフォルト値
        private const DownSamplingResolutionScaleType DEFAULT_DOWN_SAMPLING_RESOLUTION_SCALE_TYPE = DownSamplingResolutionScaleType.x2;

        // ポストエフェクトのデフォルト設定
        public const TransCapturePostEffectController.PostEffectMode DEFAULT_POST_EFFECT_MODE = TransCapturePostEffectController.PostEffectMode.指定したものだけ有効;
        public const TransCapturePostEffectController.SupportedPostEffect DEFAULT_POST_EFFECT = TransCapturePostEffectController.SupportedPostEffect.Bloom;

        #endregion

        #region 変数

        private StoryDirectSceneController _direct;
        private TransCaptureEffectController _effectController = new TransCaptureEffectController();

        private bool _isUseStoryIdField = false;
        private int _selectedStoryId;

        #endregion

        /// <summary>
        /// StoryDirectSceneControllerへの参照を設定する
        /// </summary>
        /// <param name="directSceneController"></param>
        public void SetDirectSceneController(StoryDirectSceneController directSceneController)
        {
            _direct = directSceneController;

            _downSampling.InitializeDownSamplingSetting(isApplyDownSampling: true,
                DEFAULT_DOWN_SAMPLING_RESOLUTION_SCALE_TYPE, isApplyBiLinear: false);
            _downSampling.SetupResolutionTypePopupInfo(STORY_CAPTURE_SIZE_X, STORY_CAPTURE_SIZE_Y);
            _postEffectController.InitializeSetting(DEFAULT_POST_EFFECT_MODE, DEFAULT_POST_EFFECT);
        }

        /// <summary>
        /// 設定ウィンドウの描画
        /// </summary>
        public override void DrawCaptureWithTransparentBgGUI()
        {
            _transCaptureFoldOut = EditorGUILayout.Foldout(_transCaptureFoldOut, "透過背景でキャプチャ", true);
            if (!_transCaptureFoldOut)
            {
                return;
            }

            EditorGUI.indentLevel++;

            // 出力先の設定項目
            DrawOutputSettingGUI(isApplyEditCaptureSize: true);

            // デフォルト撮影機能
            _defaultCaptureController.DrawDefaultCaptureGUI();

            // ストーリーIDの設定項目
            var isUseStoryIdField = EditorGUILayout.Toggle("ストーリーIDを入力する", _isUseStoryIdField);
            if (isUseStoryIdField != _isUseStoryIdField)
            {
                _isUseStoryIdField = isUseStoryIdField;
            }

            if (_isUseStoryIdField)
            {
                _selectedStoryId = EditorGUILayout.IntField("ストーリーID", _selectedStoryId);
            }

            var defaultCaptureSize = GetCaptureSize();
            DrawOutputSizeGUI(defaultCaptureSize);

            // エフェクトの設定項目
            _effectController.DrawCaptureEffectSettingGUI();

            // ポストエフェクトの設定項目
            _postEffectController.DrawCapturePostEffectSettingGUI();

            // 出力先フォルダを検証
            var isValidName = IsValidName();
            // 出力サイズを検証
            var isValidSize = IsValidSize();
            // エフェクトの設定を検証
            var isValidEffect = _effectController.IsValidSetting();

            // 撮影ボタンを描画
            var isValid = isValidName && isValidSize && isValidEffect;
            DrawCaptureButton(isValid, () =>
            {
                Cute.Core.UpdateDispatcher.StartCoroutine(StartCaptureWithTransparentBg());
            });

            #region ボタンが押せない理由

            OnGUI_WarningName(isValidName, OutputDirectory, _transCaptureFileName);
            OnGUI_WarningSize(isValidSize, Screen.GameViewScreenWidth, Screen.GameViewScreenHeight, defaultCaptureSize.x, defaultCaptureSize.y);
            OnGUI_WarningEffect(isValidEffect);

            #endregion

            EditorGUI.indentLevel--;
        }

        /// <summary>
        /// キャプチャサイズを取得する
        /// </summary>
        /// <returns></returns>
        protected override Vector2Int GetCaptureSize()
        {
            // キャプチャサイズの項目で設定されているサイズ
            return new Vector2Int(_downSampling.CaptureSize.x, _downSampling.CaptureSize.y);
        }

        /// <summary>
        /// タイムラインの長さを取得する
        /// </summary>
        /// <returns></returns>
        protected override float GetLength()
        {
            return StoryManager.Instance.TimelineController.TimelineData.Length / (float) GameDefine.NORMAL_FRAME_RATE;
        }

        protected override IEnumerator StartPlay()
        {
            if (_isUseStoryIdField)
            {
                // 指定されたストーリーIDのストーリーを再生する
                _direct.StartStoryWithIdFromCaptureWindow(_selectedStoryId);
            }
            else
            {
                // ストーリーの再生を開始する
                _direct.StartStoryFromCaptureWindow();
            }

            // 自動再生を有効にする
            StoryManager.IsForcedAutoPlay = true;
            yield return new WaitUntil(() => StoryManager.Instance.TimelineController.IsPlaying);
        }

        /// <summary>
        /// タイムラインが再生中か
        /// </summary>
        /// <returns></returns>
        protected override bool IsPlaying()
        {
            // Story再生中はStoryDirectを離れるためStoryManagerから現在のTimelineControllerを参照する
            return StoryManager.Instance.TimelineController.IsPlaying;
        }

        /// <summary>
        /// キャプチャ用カメラのセットアップ
        /// </summary>
        /// <param name="size"></param>
        /// <returns></returns>
        protected override Camera SetupCaptureCamera(Vector2Int size)
        {
            // 背景カラーを透明にしたカメラを準備
            var timeline = StoryManager.Instance.TimelineController;
            var baseCamera = timeline.CameraController.Camera;
            const bool isMini = false;
            var captureCamera = CreateCaptureCamera(
                baseCamera,
                isMini,
                (CustomRenderPass pass, ScriptableRenderContext context, ref RenderingData renderingData) =>
                {
                    // PNGを出力する前にアルファ情報を復帰させる (Dofを使うとアルファ情報が失われる問題に対応)
                    _postEffectController.RestoreAlphaChannel(_captureTexture, _tempTexture, _alphaCopyMaterial, _defaultCaptureController.IsEnableDefaultCapture);
                    ExportPngOnAfterRendering(pass, context, ref renderingData);
                },
                size,
                out _captureTexture);
            captureCamera.transform.Initialize();
            // エフェクトがEFFECTレイヤーにあるので描画対象に含める
            captureCamera.cullingMask |= GraphicSettings.GetCullingLayer(GraphicSettings.LayerIndex.LayerEFFECT);

            // カメラデータ取得
            var cameraData = captureCamera.GetCameraData();
            var baseCameraData = baseCamera.GetCameraData();

            // 必要に応じてアルファチャンネルの復帰用RenderTextureとMaterialを作成する
            // 不要な場合は初期値nullのままでよい
            if (NeedsKeepAlphaChannel())
            {
                TransCapturePostEffectController.CreatePreEffectTexture(_captureTexture, out _alphaTexture,
                    out _alphaCopyMaterial, out _tempTexture);
            }

            // イメージエフェクトのセットアップ
            cameraData.ImageEffectParameter.IsEnable = IsEnablePostEffect();
            cameraData.ImageEffectParameter.TargetCamera = captureCamera;
            cameraData.ImageEffectParameter.PreEffectTexture = _alphaTexture;

            // キャラクター描画後の処理を登録
            RegisterCharacterRenderCallback(cameraData, StoryManager.StorySceneController.OnRenderCharacter);

            // 1フレーム目からポストエフェクトのパラメータを反映させるため、カメラ生成直後のタイミングでも同期する
            var modelControllerList = StoryManager
                .Instance
                .TimelineController.TimelineData
                .GetCharacterHeadTrackList()
                .Select(x => (ModelController)x.GetModelControllerFromCache())
                .Where(x => x != null)
                .ToList();
            _defaultCaptureController.ApplySetting(modelControllerList, cameraData);

            // キャプチャ前に実行する処理
            cameraData.BeginCameraRenderingCallback = (_, camera) =>
            {
                // FOVやImageEffectパラメータを同期する
                camera.fieldOfView = baseCamera.fieldOfView;
                _postEffectController.SyncPostEffectParam(baseCameraData, cameraData);

                // デフォルト撮影機能
                _defaultCaptureController.ApplySetting(modelControllerList, cameraData);

                // エフェクト系prefabは指定されたものだけキャプチャ対象となるようにレイヤー調整
                var effectObjectArray = GetActiveEffectObjectArray();
                _effectController.SetupObjectLayer(effectObjectArray);
            };

            // キャプチャ後に実行する処理
            cameraData.EndCameraRenderingCallback = (_, camera) =>
            {
                var effectObjectArray = GetActiveEffectObjectArray();
                // エフェクト系prefabのレイヤーを元に戻す
                _effectController.ResetObjectLayer(effectObjectArray);
            };

            // 撮影中にGameViewの見た目が狂うので解像度を再設定する
            var lowResolutionCamera = baseCamera.GetComponent<LowResolutionCameraFrameBuffer>();
            lowResolutionCamera.SetRenderTextureSize(size);

            return captureCamera;
        }

        /// <summary>
        /// キャプチャ前の操作
        /// </summary>
        protected override void PreCaptureAction()
        {
            // キャプチャに含める対象のエフェクト名の配列を作成する
            _effectController.SetupCaptureTargetEffectNameArray();
        }

        /// <summary>
        /// キャプチャ後の後始末
        /// </summary>
        protected override void PostCaptureAction()
        {
            // タイムラインが再生中なら中断する
            if (IsPlaying())
            {
                StoryManager.Instance.TimelineController.OnEndStory();
            }
        }

        /// <summary>
        /// キャラクター描画時のコールバック登録
        /// </summary>
        /// <param name="cameraData"></param>
        /// <param name="executeCallback"></param>
        private void RegisterCharacterRenderCallback(CameraData cameraData, CustomRenderPass.ExecuteCallback executeCallback)
        {
            ref var customRenderer = ref cameraData.GetCustomRenderParameter(out _);
            customRenderer.IsEnable = true;
            customRenderer.OnExecute = executeCallback;
            customRenderer.RenderPassEvent = RenderPassEvent.BeforeRenderingTransparents;
            customRenderer.SetPassName(executeCallback?.Method.Name);
        }

        /// <summary>
        /// 表示中のエフェクトオブジェクトの配列を取得する
        /// </summary>
        /// <returns></returns>
        private GameObject[] GetActiveEffectObjectArray()
        {
            // オブジェクトのキャッシュは初めのBlockのObjectControlTrackに登録される
            var objectHeadTrackList = StoryManager.Instance.TimelineController.TimelineData.BlockList[0].ObjectControlTrackList;

            // 各HeadTrackから表示中のオブジェクトを取得する
            var activeEffectArray = objectHeadTrackList
                .SelectMany(track => track.CacheObjectList.Values.Select(cache => cache.Object))
                .ToArray();

            return activeEffectArray;
        }
    }
}

#endif // CYG_DEBUG && UNITY_EDITOR
