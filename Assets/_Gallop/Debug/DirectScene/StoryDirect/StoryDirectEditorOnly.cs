#if CYG_DEBUG

using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using System.Text.RegularExpressions;

namespace Gallop
{
    /// <summary>
    /// 会話テスト用シーン / Editorのみの機能
    /// </summary>
    public partial class StoryDirectSceneController : SceneControllerBase<StoryDirect>
    {
#if UNITY_EDITOR
        private const int STORY_TIMELINE_DIGIT = 9;
        private const string EDITOR_PREFS_KEY_LAST_ACCESS_RECORD_IDS_TEXT_FILE_PATH = "StoryDirectLastAccessIdsTextPath";

        private const string INTERRUPTED_ID_MESSAGE_PREFIX = "前回の録画中断ID：";

        private string _lastAccessdRecordIdsTextFilePath = "";

        ///<summary>透過背景キャプチャウィンドウを開いているか</summary>
        private const string EDITOR_KEY_OPEN_CAPTURE_WINDOW = "uma_story_direct_open_capture_window";

        /// <summary>
        /// Editor限定機能の初期化
        /// </summary>
        private void InitEditorOnly()
        {
            InitRecordingIdsTextUI();
            InitTransCaptureWindowToggle();

            OpenTransCaptureWindow();
        }

        #region 再生ストーリーIDのテキスト入力機能

        /// <summary>
        /// 文字入力での録画リストUI初期化
        /// </summary>
        private void InitRecordingIdsTextUI()
        {
            var saveButton = _storyDirectView.RecordIdsTextSaveButton;
            var loadButton = _storyDirectView.RecordIdsTextLoadButton;
            var inputField = _storyDirectView.RecordIdsTextInputField;

            saveButton.SetOnClick(OnClickRecordIdsTextSaveButton);
            loadButton.SetOnClick(OnClickRecordIdsTextLoadButton);

            _lastAccessdRecordIdsTextFilePath = LoadPrefsValue();

            // デフォルト文字列
            inputField.text = "#入力サンプル\n"
                              + @"#をつけると、それ以降はコメント扱いになります
000000001  # チュートリアルストーリー
041002001  # スズカウマ娘ストーリー 

"
                              + @"# 「C:2桁」記載で、指定カテゴリを全て再生します。
C:81

"
                              + @"# *が使用できます。
010001*
*1004*

"
                              + @"# 存在しないIDを再生するとダイアログで通知されます
1234
000000000
999999999    
";
        }

        private void OnClickRecordIdsTextSaveButton()
        {
            string defaultDirectory = Application.dataPath;
            string defaultFileName = "";
            if (!string.IsNullOrEmpty(_lastAccessdRecordIdsTextFilePath))
            {
                defaultFileName = Path.GetFileName(_lastAccessdRecordIdsTextFilePath);
                defaultDirectory = _lastAccessdRecordIdsTextFilePath.Replace(defaultFileName, "");
            }

            string selectedFilePath =
                EditorUtility.SaveFilePanel("FileSelect", defaultDirectory, defaultFileName, "txt");
            if (string.IsNullOrEmpty(selectedFilePath))
            {
                return;
            }

            _lastAccessdRecordIdsTextFilePath = selectedFilePath;
            File.WriteAllText(_lastAccessdRecordIdsTextFilePath, _storyDirectView.RecordIdsTextInputField.text);
            SavePrefsValue(_lastAccessdRecordIdsTextFilePath);
        }

        private void OnClickRecordIdsTextLoadButton()
        {
            string defaultDirectory = Application.dataPath;
            if (!string.IsNullOrEmpty(_lastAccessdRecordIdsTextFilePath))
            {
                string defaultFileName = Path.GetFileName(_lastAccessdRecordIdsTextFilePath);
                defaultDirectory = _lastAccessdRecordIdsTextFilePath.Replace(defaultFileName, "");
            }

            string selectedFilePath = EditorUtility.OpenFilePanel("FileSelect", defaultDirectory, "txt");
            if (string.IsNullOrEmpty(selectedFilePath))
            {
                return;
            }

            _lastAccessdRecordIdsTextFilePath = selectedFilePath;
            _storyDirectView.RecordIdsTextInputField.text = File.ReadAllText(_lastAccessdRecordIdsTextFilePath);
            SavePrefsValue(_lastAccessdRecordIdsTextFilePath);
        }

        private string LoadPrefsValue()
        {
            return EditorPrefs.GetString(EDITOR_PREFS_KEY_LAST_ACCESS_RECORD_IDS_TEXT_FILE_PATH);
        }
        private void SavePrefsValue(string path)
        {
            EditorPrefs.SetString(EDITOR_PREFS_KEY_LAST_ACCESS_RECORD_IDS_TEXT_FILE_PATH, path);
        }

        /// <summary>
        /// 「全ストーリー再生」専用UIの初期化処理
        /// </summary>
        private void SetupRecordingAllOptionsUI()
        {
            if (!File.Exists(LAST_RECORDED_STORY_ID_IN_ALL_MODE))
            {
                _storyDirectView.InterruptedStoryIdText.text = INTERRUPTED_ID_MESSAGE_PREFIX + "────";
                _storyDirectView.ContinueRecordAllCheckBox.Toggle.isOn = false;
                _storyDirectView.ContinueRecordAllCheckBox.Toggle.interactable = false;
            }
            else
            {
                string interruptedId = File.ReadAllText(LAST_RECORDED_STORY_ID_IN_ALL_MODE);
                _storyDirectView.InterruptedStoryIdText.text =
                    INTERRUPTED_ID_MESSAGE_PREFIX + interruptedId.PadLeft(ResourcePath.STORY_ID_LENGTH, '0');
                _storyDirectView.ContinueRecordAllCheckBox.Toggle.interactable = true;
            }
        }

        // 指定されたIDのストーリーを全て再生
        private void StartRecordingSpecifiedIds()
        {
            // 全てのストーリーIDを抽出
            var info = LoadStoryIdInfo();
            if (info == null)
            {
                Debug.LogError(STORY_ID_INFO_PATH + "がありません");
                return;
            }

            HashSet<int> playIdList = new HashSet<int>();
            List<string> missingIdList = new List<string>(512);
            List<Regex> regexRules = new List<Regex>();

            // categoryIdからcategoryIndexへ変換
            Dictionary<int, int> categoryIdToIndex = new Dictionary<int, int>();
            for (int index = 0; index < _categoryIdList.Count; ++index)
            {
                categoryIdToIndex.Add(_categoryIdList[index], index);
            }

            // inputfieldの内容から対象ID一覧を抽出
            var inputText = _storyDirectView.RecordIdsTextInputField.text;
            string[] lines = inputText.Split(
                new string[] { "\r\n", "\r", "\n" },
                StringSplitOptions.None);
            foreach (string line in lines)
            {
                // コメントの除外
                string procLine = line;
                if (line.Contains("#"))
                {
                    string[] splittedLines = line.Split('#');
                    procLine = splittedLines[0];
                }

                // 行内の数値をIDとして抽出
                procLine = procLine.Trim();
                // 空行はスキップ
                if (string.IsNullOrEmpty(procLine))
                {
                    continue;
                }

                // 1. 数字以外の情報を含んでいたらIDとして利用できない
                if (!int.TryParse(procLine, out int intStoryId))
                {
                    // この文字列が正規表現として扱えるか判断
                    Regex regex = ConvertLineToRegex(procLine);
                    if (regex != null)
                    {
                        regexRules.Add(regex);
                    }
                    else
                    {
                        missingIdList.Add(procLine);
                    }
                    continue;
                }

                // 2. 桁数チェック
                if (procLine.Length != STORY_TIMELINE_DIGIT)
                {
                    missingIdList.Add(procLine);
                    continue;
                }

                // 3. ID存在チェック
                StoryTimelineData.ParseCategoryAndDirectoryId(intStoryId, out int categoryId, out int directoryId);
                if (!categoryIdToIndex.TryGetValue(categoryId, out int categoryIndex))
                {
                    // 指定カテゴリは存在しない
                    missingIdList.Add(procLine);
                    continue;
                }
                var categoryData = info.CategoryList[categoryIndex];
                var directoryData = categoryData.DirectoryList.Find((directory) => directory.DirectoryId == directoryId);
                if (directoryData == null)
                {
                    // directoryが見つからなかった
                    missingIdList.Add(procLine);
                    continue;
                }

                if (directoryData.StoryIdList.Contains(intStoryId))
                {
                    playIdList.Add(intStoryId);
                }
                else
                {
                    // StoryId一覧にヒットしなかった
                    missingIdList.Add(procLine);
                }
            }

            // 正規表現に該当するIDを全てリストに追加したい
            if (regexRules.Count > 0)
            {
                foreach (var category in info.CategoryList)
                {
                    foreach (var dict in category.DirectoryList)
                    {
                        foreach (var storyId in dict.StoryIdList)
                        {
                            foreach (var regex in regexRules)
                            {
                                if (regex.IsMatch(storyId.ToString()))
                                {
                                    playIdList.Add(storyId);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            if (missingIdList.Count > 0)
            {
                Debug.LogWarning("再生できなかったID一覧です。\n------------------------------\n" + string.Join("\n", missingIdList));

                DialogManager.PushErrorCommon(
                    "存在しないIDが指定されています。\n詳細はコンソールを確認してください。\n\nダイアログを閉じると自動録画が開始されます。",
                    "指定IDエラー",
                    PlayStorys,
                    GallopResultCode.OpenErrorPopupType.ShowOnly);
            }
            else
            {
                PlayStorys();
            }

            void PlayStorys()
            {
                // 収集したストーリーIDを再生
                if (playIdList.Count <= 0)
                {
                    return;
                }

                bool isOnlyNotRecordedStory = _storyDirectView.OnlyNotRecordedStoryCheckBox.Toggle.isOn;
                GameSystem.Instance.StartCoroutine(
                    RecordStoryIds(CreateViewInfoForRecording(), playIdList, GeneratePath, GetRecorderType(), isOnlyNotRecordedStory)
                );
            }
        }

        // 全ストーリー再生
        private void StartRecordingAllStory()
        {
            // 全てのストーリーIDを抽出
            var info = LoadStoryIdInfo();
            if (info == null)
            {
                Debug.LogError(STORY_ID_INFO_PATH + "がありません");
                return;
            }

            int skipStoryId = -1;
            if (_storyDirectView.ContinueRecordAllCheckBox.Toggle.isOn)
            {
                string interruptedId = File.ReadAllText(LAST_RECORDED_STORY_ID_IN_ALL_MODE);
                skipStoryId = int.Parse(interruptedId);
            }
            else
            {
                DeleteLastRecordedIdInAllMode();
            }

            bool isOnlyNotRecordedStory = _storyDirectView.OnlyNotRecordedStoryCheckBox.Toggle.isOn;
            var recordStoryIdList = CreateRecordStoryIdList();
            GameSystem.Instance.StartCoroutine(
                RecordStoryIds(CreateViewInfoForRecording(), recordStoryIdList, GeneratePath, GetRecorderType(), isOnlyNotRecordedStory)
            );

            List<int> CreateRecordStoryIdList()
            {
                List<int> storyIdList = new List<int>(8192);
                foreach (var category in info.CategoryList)
                {
                    foreach (var dict in category.DirectoryList)
                    {
                        foreach (var storyId in dict.StoryIdList)
                        {
                            // SkipStoryIdより前のIDは前回録画済みなのでスキップ
                            if (storyId <= skipStoryId)
                            {
                                continue;
                            }

                            storyIdList.Add(storyId);
                        }
                    }
                }
                return storyIdList;
            }
        }

        /// <summary>
        /// 保存先のパスを生成
        /// </summary>
        /// <param name="storyId"></param>
        /// <returns></returns>
        private string GeneratePath(int storyId)
        {
            var storyIdString = storyId.ToString(ResourcePath.STORY_ID_FORMAT);
            StoryTimelineData.ParseCategoryAndDirectoryId(storyId, out int categoryId, out int directoryId);
            // キャラ固有のストーリーは追加のディレクトリを作成する
            bool isExtendDirectory = false;
            switch (categoryId)
            {
                case (int)DebugStoryCategory.CategoryType.CharaStory:
                case (int)DebugStoryCategory.CategoryType.SingleModeCharaSpecific:
                    isExtendDirectory = true;
                    break;
            }

            var path = Path.Combine("Story", categoryId.ToString());
            if (isExtendDirectory)
            {
                path = Path.Combine(path, directoryId.ToString());
            }
            path = Path.Combine(path, storyIdString.Substring(0, 6), storyIdString);
            return path;
        }

        /// <summary>
        /// 文字列が正規表現として使えるものかを判定
        /// </summary>
        /// <param name="line"></param>
        /// <returns></returns>
        private Regex ConvertLineToRegex(string line)
        {
            // カテゴリ一括指定 「C:XX」 に該当するか判定
            var matchCategory = Regex.Match(line, @"C:(\d{2})$");
            if (matchCategory.Success)
            {
                return new Regex($"^{matchCategory.Groups[1]}.*");
            }

            // アスタリスクでの指定に該当するか判定
            if (Regex.IsMatch(line, @"^[*0-9]+$"))
            {
                return new Regex(line.Replace("*", "[0-9]+"));
            }

            return null;
        }

        #endregion

        #region 透過背景キャプチャ機能

        /// <summary>
        /// 透過背景キャプチャウィンドウを開くチェックボックスの初期化
        /// </summary>
        private void InitTransCaptureWindowToggle()
        {
            _storyDirectView.TransCaptureWindowCheckBox.SetActiveWithCheck(true);
            _storyDirectView.TransCaptureWindowCheckBox.Toggle.isOn =
                UnityEditor.EditorPrefs.GetBool(EDITOR_KEY_OPEN_CAPTURE_WINDOW, false);
            _storyDirectView.TransCaptureWindowCheckBox.Toggle.onValueChanged.AddListener(value =>
            {
                UnityEditor.EditorPrefs.SetBool(EDITOR_KEY_OPEN_CAPTURE_WINDOW, value);
                OpenTransCaptureWindow();
            });
        }

        /// <summary>
        /// 透過背景キャプチャウィンドウを開く
        /// </summary>
        private void OpenTransCaptureWindow()
        {
            if (!_storyDirectView.TransCaptureWindowCheckBox.Toggle.isOn)
            {
                return;
            }

            var window = UnityEditor.EditorWindow.GetWindow<StoryDirectCaptureWindow>();
            window.SetDirectSceneController(this);
        }

        #endregion
#endif
    }
}

#endif