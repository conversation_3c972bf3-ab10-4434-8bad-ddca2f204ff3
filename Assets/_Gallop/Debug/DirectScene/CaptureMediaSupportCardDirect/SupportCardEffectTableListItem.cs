using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// サポカ：練習効果一覧子要素
    /// </summary>
    [AddComponentMenu("")]
    public class SupportCardEffectTableListItem : LoopScrollItemBase
    {
        [SerializeField] private TextCommon _nameText;
        [SerializeField] private TextCommon _descText;

        public void SetupItem(MasterSupportCardEffectTable.SupportCardEffectTable masterSupportCardEffectTable)
        {
            SetupEffectTableName(masterSupportCardEffectTable);
            SetupEffectTableDesc(masterSupportCardEffectTable);
        }

        private void SetupEffectTableName(
            MasterSupportCardEffectTable.SupportCardEffectTable masterSupportCardEffectTable)
        {
            WorkSupportCardData.SupportCardData cardData = new WorkSupportCardData.SupportCardData(masterSupportCardEffectTable.LimitLv50);

            var effectType = masterSupportCardEffectTable.GetEffectType();
            var effectValue = masterSupportCardEffectTable.GetValueEffect(null, 50);

            _nameText.text = effectType.GetNameWithValue(effectValue);
        }

        private void SetupEffectTableDesc(
            MasterSupportCardEffectTable.SupportCardEffectTable masterSupportCardEffectTable)
        {
            var effectType = masterSupportCardEffectTable.GetEffectType();
            _descText.text = effectType.GetDescription();
        }
    }
}