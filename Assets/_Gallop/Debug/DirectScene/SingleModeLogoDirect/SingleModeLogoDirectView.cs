#if CYG_DEBUG
using System;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;

namespace Gallop
{
    public class SingleModeLogoDirectView : ViewBase
    {
#if UNITY_EDITOR

        private const string SINGLE_MODE_WIPE_KEYWORD = "育成プロローグのロゴ";

        private const string UI_SWITCH_BUTTON_ON = "UIオン";

        private const string UI_SWITCH_BUTTON_OFF = "UIオフ";

        /// <summary>
        /// 育成ロゴ動画のタイプを表示するプルダウン
        /// </summary>
        [field: RenameField, SerializeField]
        public DropDownCommon LogoTypeDropdown { get; private set; }

        /// <summary>
        /// 再生ボタン
        /// </summary>
        [field: RenameField, SerializeField]
        public ButtonCommon PlayButton { get; private set; }

        /// <summary>
        /// UI切り替えボタン
        /// </summary>
        [field: Ren<PERSON><PERSON><PERSON>, SerializeField]
        public Button UISwitchButton { get; private set; }

        /// <summary>
        /// 画面上部に表示するUIの親
        /// </summary>
        [field: RenameField, SerializeField]
        public GameObject TopArea { get; private set; }

        private StoryWipeDictionaryCsvForEditor _storyWipeDictionaryCsvForEditor;

        private StoryWipeDictionaryCsvForEditor.JoinedCsv _wipeCsv;

        private Action<StoryWipeDictionaryCsvForEditor.JoinedCsv> _onPlay;

        public static async void RegisterDownloadResourcePath(DownloadPathRegister register, Action onComplete)
        {
            var tmp = SynchronizationContext.Current;
            var csvEditor = new StoryWipeDictionaryCsvForEditor(StoryTimelineEditorPropertyWindow.CSV_BASE_PATH_MASTER, StoryTimelineEditorPropertyWindow.CSV_BASE_PATH, tmp);

            await Task.Run(() => csvEditor.LoadCsvAsync());

            foreach (var csvName in csvEditor.WipeNameList.Where(x => x.Contains(SINGLE_MODE_WIPE_KEYWORD)))
            {
                var csv = csvEditor.GetWipeCsv(csvName);

                register.RegisterPathWithoutInfo(ResourcePath.GetPrologueLogoMovieAssetBundlePath(csv.WipeId, csv.SubId));
                register.RegisterPathWithoutInfo(ResourcePath.GetPrologueLogoLoopMovieAssetBundlePath(csv.WipeId, csv.SubId));
            }

            onComplete?.Invoke();
        }

        public async void Setup(Action<StoryWipeDictionaryCsvForEditor.JoinedCsv> onPlay)
        {
            await SetupCSV();

            SetupView(onPlay);
        }

        private async Task SetupCSV()
        {
            if (_storyWipeDictionaryCsvForEditor == null)
            {
                var tmp = SynchronizationContext.Current;
                _storyWipeDictionaryCsvForEditor = new StoryWipeDictionaryCsvForEditor(StoryTimelineEditorPropertyWindow.CSV_BASE_PATH_MASTER, StoryTimelineEditorPropertyWindow.CSV_BASE_PATH, tmp);

                await Task.Run(() => _storyWipeDictionaryCsvForEditor.LoadCsvAsync());
            }
        }

        private void SetupView(Action<StoryWipeDictionaryCsvForEditor.JoinedCsv> onPlay)
        {
            #region UI

            //ドロップダウンを初期化
            LogoTypeDropdown.ClearOptions();

            List<Dropdown.OptionData> optionDatas = new List<Dropdown.OptionData>();
            var singleModeLogoWipeNameData = _storyWipeDictionaryCsvForEditor.WipeNameList.Where(x => x.Contains(SINGLE_MODE_WIPE_KEYWORD)).ToArray();
            foreach (var wipeName in singleModeLogoWipeNameData)
            {
                optionDatas.Add(new Dropdown.OptionData(wipeName));
            }

            LogoTypeDropdown.AddOptions(optionDatas);

            LogoTypeDropdown.onValueChanged.RemoveAllListeners();
            LogoTypeDropdown.onValueChanged.AddListener((val) =>
            {
                var wipeCSV = _storyWipeDictionaryCsvForEditor.GetWipeCsv(singleModeLogoWipeNameData[val]);

                OnWipeValueChange(wipeCSV);
            });

            //初期化の際に自動的に最初の動画データを設定
            OnWipeValueChange(_storyWipeDictionaryCsvForEditor.GetWipeCsv(singleModeLogoWipeNameData[0]));

            //再生ボタン
            _onPlay = onPlay;

            PlayButton.onClick.RemoveAllListeners();
            PlayButton.onClick.AddListener(() =>
            {
                OnClickPlayButton();
            });

            //表示UIの切り替え
            void OnSwitchUIEnable(bool enable)
            {
                //UISwitchButton.TargetText.text = enable ? UI_SWITCH_BUTTON_OFF : UI_SWITCH_BUTTON_ON;
            }
            UISwitchButton.onClick.RemoveAllListeners();
            UISwitchButton.onClick.AddListener(() =>
            {
                TopArea.SetActive(!TopArea.activeSelf);
                OnSwitchUIEnable(TopArea.activeSelf);
            });
            OnSwitchUIEnable(TopArea.activeSelf);
            #endregion
        }

        private void OnWipeValueChange(StoryWipeDictionaryCsvForEditor.JoinedCsv wipeCsv)
        {
            _wipeCsv = wipeCsv;
        }

        private void OnClickPlayButton()
        {
            _onPlay?.Invoke(_wipeCsv);
        }
#endif
    }
}

#endif