#if CYG_DEBUG
using System;
using System.Collections;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// 育成ロゴ動画を再生するためのダイレクトシーン
    /// </summary>
    [AddComponentMenu("")]
    public class SingleModeLogoDirectScene : DirectSceneBase
    {
        [field: Header("Viewに表示するオブジェクト")]
        [field: SerializeField, RenameField] public SingleModeLogoDirectView logoDirectView { get; protected set; }

        [field: Header("3D表示用カメラ")]
        [field: SerializeField, RenameField] public Camera EffectCamera { get; private set; }

        public override bool IsInvalidDayChange()
        {
            return true;
        }

        public override bool IsNeedLogin()
        {
            return false;
        }
    }

    public class SingleModeLogoDirectSceneController : SceneControllerBase<SingleModeLogoDirectScene>
    {
        private readonly string BG_CAMERA_BACKGROUND_COLOR_CODE = "#314D79";

#if UNITY_EDITOR
        private SingleModeLogoDirectView _view;

        private Color _bgCameraDefaultColor = Color.white;

        #region 動画再生

        private FullScreenMoviePlayerStory _moviePlayer = null;
        private Coroutine _movieLoadAndPlayCoroutine = null;

        #endregion
#endif

        public override IEnumerator InitializeScene()
        {
#if UNITY_EDITOR

            //153709 ダウンロードパスの登録が非同期なため、ロード中で独自にダウンロードをかける
            var register = DownloadManager.GetNewRegister();
            bool isWaitingRegisterDownloadpath = true;
            SingleModeLogoDirectView.RegisterDownloadResourcePath(register, () =>
            {
                isWaitingRegisterDownloadpath = false;
            });

            yield return new WaitWhile(() => isWaitingRegisterDownloadpath);

            bool isDownloading = true;
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                isDownloading = false;
            });

            yield return new WaitWhile(() => isDownloading);

            // Viewを自前でロード
            var viewInstance = GameObject.Instantiate(_scene.logoDirectView.gameObject, UIManager.MainCanvas.transform);
            _view = viewInstance.GetComponent<SingleModeLogoDirectView>();
            _view.AdjustSafeArea(UIManager.Instance); // Viewを自前でロードした時はSafeArea対応も自分で行う
            _view.Setup(OnPlayLogoAnimation);

#else
            UIManager.Instance.ShowDebugNotification("このダイレクトシーンはEditor上のみ動作するもの!", 60f);
#endif
            yield return base.InitializeScene();
        }

        public override void BeginScene()
        {
#if UNITY_EDITOR
            //背景カメラの背景色を設定
            _bgCameraDefaultColor = UIManager.BGCamera.backgroundColor;
            if (ColorUtility.TryParseHtmlString(BG_CAMERA_BACKGROUND_COLOR_CODE, out var bgColor))
            {
                UIManager.BGCamera.backgroundColor = bgColor;
                UIManager.BGManager.SetMainBgEnable(false);
            }

            UIManager.Instance.SetCameraTargetFromUITexture(_scene.EffectCamera);
#endif
            base.BeginScene();
        }

        public override void FinalizeScene()
        {
#if UNITY_EDITOR
            CleanUpMovie();
            UIManager.BGCamera.backgroundColor = _bgCameraDefaultColor;
            UIManager.BGManager.SetMainBgEnable(true);
            UIManager.Instance.ResetBgCameraDepth();
#endif
            base.FinalizeScene();
        }

#if UNITY_EDITOR
        private void OnPlayLogoAnimation(StoryWipeDictionaryCsvForEditor.JoinedCsv wipeCsv)
        {
            if (_moviePlayer != null)
            {
                CleanUpMovie();
            }

            _moviePlayer = new FullScreenMoviePlayerStory(true, _view.ContentsRoot, true);
            var moviePath = ResourcePath.GetPrologueLogoMoviePath(wipeCsv.WipeId, wipeCsv.SubId);
            var loopMoviePath = ResourcePath.GetPrologueLogoLoopMoviePath(wipeCsv.WipeId, wipeCsv.SubId);
            _movieLoadAndPlayCoroutine = Cute.Core.UpdateDispatcher.StartCoroutine(_moviePlayer.LoadRepeatMovieAndPlay(moviePath, loopMoviePath));
        }

        private void CleanUpMovie()
        {
            if (_movieLoadAndPlayCoroutine != null)
            {
                Cute.Core.UpdateDispatcher.StopCoroutine(_movieLoadAndPlayCoroutine);
                _movieLoadAndPlayCoroutine = null;
            }

            if (_moviePlayer == null)
            {
                return;
            }

            _moviePlayer.DestroyPlayer();
            _moviePlayer = null;
        }
#endif
    }
}

#endif