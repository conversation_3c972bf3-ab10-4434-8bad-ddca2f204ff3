#if CYG_DEBUG
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    using BonusType = MasterLoginBonusData.BonusType;

    /// <summary>
    /// ログインボーナスダイレクト
    /// </summary>
    public class LoginBonusDirectScene : DirectSceneBase
    {
        public class LoginBonusSetting
        {
            public bool IsOn { get; set; } = true;
            public int Index { get; set; } = 0;
            public int TotalCount { get; set; } = 1;
        }

        public static LoginBonusSetting CampaignLoginBonusSetting = new LoginBonusSetting();

        public static bool ReturnDirect = false;
        public static bool TimeScaleDelay = false;

        [SerializeField, RenameField]
        public ToggleCommon NormalToggle;

        [SerializeField, RenameField]
        public Dropdown NormalDropdown;
        
        [SerializeField, RenameField]
        public InputField NormalTotalCount;
        
        [SerializeField, RenameField]
        public ToggleCommon DisableNormalA2U;

        [SerializeField, RenameField]
        public ToggleCommon StartDashToggle;

        [SerializeField, RenameField]
        public Dropdown StartDashDropdown;
        
        [SerializeField, RenameField]
        public InputField StartDashTotalCount;

        [SerializeField, RenameField]
        public ToggleCommon CampaignToggle;

        [SerializeField, RenameField]
        public Dropdown CampaignDropdown;
        
        [SerializeField, RenameField]
        public InputField CampaignTotalCount;

        [SerializeField, RenameField]
        public ButtonCommon StartButton;

        [SerializeField, RenameField]
        public ToggleCommon ReturnDirectToggle;

        [SerializeField, RenameField]
        public ToggleCommon TimeScaleDelayToggle;

        public List<MasterLoginBonusData.LoginBonusData> NormalLoginBonusList { get; private set; } = new List<MasterLoginBonusData.LoginBonusData>();
        public List<MasterLoginBonusData.LoginBonusData> StartDashLoginBonusList { get; private set; } = new List<MasterLoginBonusData.LoginBonusData>();
        public List<MasterLoginBonusData.LoginBonusData> CampaignLoginBonusList { get; private set; } = new List<MasterLoginBonusData.LoginBonusData>();

        public void Init(Action onStart)
        {
            var loginBonusOptionList = new List<Dropdown.OptionData>();
            NormalLoginBonusList.Clear();
            foreach (var normal in MasterDataManager.Instance.masterLoginBonusData.GetListWithType((int) BonusType.Normal))
            {
                loginBonusOptionList.Add(new Dropdown.OptionData($"{normal.Id}: {normal.Name}"));
                NormalLoginBonusList.Add(normal);
            }

            NormalDropdown.ClearOptions();
            NormalDropdown.AddOptions(loginBonusOptionList);
            NormalToggle.isOn = false;
            NormalTotalCount.text = "1";

            loginBonusOptionList.Clear();
            StartDashLoginBonusList.Clear();
            foreach (var startDash in MasterDataManager.Instance.masterLoginBonusData.GetListWithType((int) BonusType.StartDash))
            {
                loginBonusOptionList.Add(new Dropdown.OptionData($"{startDash.Id}: {startDash.Name}"));
                StartDashLoginBonusList.Add(startDash);
            }

            StartDashDropdown.ClearOptions();
            StartDashDropdown.AddOptions(loginBonusOptionList);
            StartDashToggle.isOn = false;
            StartDashTotalCount.text = "1";

            loginBonusOptionList.Clear();
            CampaignLoginBonusList.Clear();
            foreach (var campaign in MasterDataManager.Instance.masterLoginBonusData.GetListWithType((int) BonusType.Campaign))
            {
                loginBonusOptionList.Add(new Dropdown.OptionData($"{campaign.Id}: {campaign.Name}"));
                CampaignLoginBonusList.Add(campaign);
            }

            // 複数段階あるログボもキャンペーンに入れる
            foreach (var campaign in MasterDataManager.Instance.masterLoginBonusData.GetListWithType((int)BonusType.MultipleStepBonus))
            {

                loginBonusOptionList.Add(new Dropdown.OptionData($"{campaign.Id}: {campaign.Name}"));
                CampaignLoginBonusList.Add(campaign);
            }

            CampaignDropdown.ClearOptions();
            CampaignDropdown.AddOptions(loginBonusOptionList);
            CampaignDropdown.value = CampaignLoginBonusSetting.Index;
            CampaignToggle.isOn = CampaignLoginBonusSetting.IsOn;
            CampaignTotalCount.text = CampaignLoginBonusSetting.TotalCount.ToString();

            ReturnDirect = ReturnDirectToggle.isOn;
            ReturnDirectToggle.onValueChanged.AddListener(b => ReturnDirect = b);

            TimeScaleDelayToggle.isOn = TimeScaleDelay;
            TimeScaleDelayToggle.onValueChanged.AddListener(b => TimeScaleDelay = b);

            StartButton.SetOnClick(() => onStart?.Invoke());
        }
    }

    public class LoginBonusDirectSceneController : SceneControllerBase<LoginBonusDirectScene>
    {
        public override void BeginScene()
        {
            Time.timeScale = 1;

            _scene.Init
            (
                () =>
                {
                    //タイムスケールデバッグ
                    if(LoginBonusDirectScene.TimeScaleDelay)
                    {
                        Time.timeScale = 0.2f;
                    }

                    bool needNormalLogin = false;
                    bool needSpecialLogin = false;

                    var loginBonusData = WorkDataManager.Instance.LoginBonusData;
                    var loginBonusInfoList = loginBonusData.LoginBonusInfoList;
                    loginBonusInfoList.Clear();
                    
                    if (_scene.NormalToggle.isOn)
                    {
                        if (!int.TryParse(_scene.NormalTotalCount.text, out var totalCount))
                        {
                            totalCount = 1;
                            Debug.LogWarning("獲得回数がintに変換できません");
                        }
                        
                        var bonusData = _scene.NormalLoginBonusList[_scene.NormalDropdown.value];
                        loginBonusInfoList.Add(new WorkLoginBonusData.LoginBonusInfo()
                        {
                            Id = bonusData.Id,
                            TotalCount = totalCount,
                            MasterBonusData = bonusData
                        });

                        loginBonusData.NormalDisableA2U = _scene.DisableNormalA2U.isOn;
                        needNormalLogin = true;
                    }
                    
                    if (_scene.StartDashToggle.isOn)
                    {
                        if (!int.TryParse(_scene.StartDashTotalCount.text, out var totalCount))
                        {
                            totalCount = 1;
                            Debug.LogWarning("獲得回数がintに変換できません");
                        }
                        
                        var bonusData = _scene.StartDashLoginBonusList[_scene.StartDashDropdown.value];
                        loginBonusInfoList.Add(new WorkLoginBonusData.LoginBonusInfo()
                        {
                            Id = bonusData.Id,
                            TotalCount = totalCount,
                            MasterBonusData = bonusData
                        });
                        needSpecialLogin = true;
                    }
                    
                    if (_scene.CampaignToggle.isOn)
                    {
                        if (!int.TryParse(_scene.CampaignTotalCount.text, out var totalCount))
                        {
                            totalCount = 1;
                            Debug.LogWarning("獲得回数がintに変換できません");
                        }
                        
                        var bonusData = _scene.CampaignLoginBonusList[_scene.CampaignDropdown.value];
                        loginBonusInfoList.Add(new WorkLoginBonusData.LoginBonusInfo()
                        {
                            Id = bonusData.Id,
                            TotalCount = totalCount,
                            MasterBonusData = bonusData
                        });
                        needSpecialLogin = true;
                        
                        LoginBonusDirectScene.CampaignLoginBonusSetting.IsOn = true;
                        LoginBonusDirectScene.CampaignLoginBonusSetting.Index = _scene.CampaignDropdown.value;
                        LoginBonusDirectScene.CampaignLoginBonusSetting.TotalCount = totalCount;
                    }

                    if (needSpecialLogin)
                    { 
                        SceneManager.Instance.ChangeView(SceneDefine.ViewId.SpecialLoginBonus);   
                    }
                    else if (needNormalLogin)
                    {
                        SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub, new HomeHubViewController.HomeHubViewInfo()
                        {
                            DefaultViewId = SceneDefine.ViewId.Home,
                            HomeViewInfo = new HomeViewInfo(HomeTopState.LoginBonus)
                        });   
                    }
                    else
                    {
                        SceneManager.Instance.ChangeView(SceneDefine.ViewId.HomeHub, new HomeHubViewController.HomeHubViewInfo()
                        {
                            DefaultViewId = SceneDefine.ViewId.Home,
                        });  
                    }
                }
            );

            base.BeginScene();
        }
    }
}

#endif