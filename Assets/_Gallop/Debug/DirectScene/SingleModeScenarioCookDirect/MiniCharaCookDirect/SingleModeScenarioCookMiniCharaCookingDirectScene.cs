#if CYG_DEBUG
using System;
using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class SingleModeScenarioCookMiniCharaCookingDirectScene : DirectSceneBase
    {
        [field: SerializeField, RenameField]
        public SingleModeScenarioCookMiniCharaCookingDirectView DirectView { get; protected set; }

        public override bool IsNeedLogin()
        {
            return false;
        }
    }   
}
#endif