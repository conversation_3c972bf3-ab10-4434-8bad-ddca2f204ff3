#if CYG_DEBUG
using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    using static SingleModeScenarioCookDefine;
    using static SingleModeScenarioCookCampaignTrainingCuttDirectSceneController;

    /// <summary>
    /// キャラ選択パーツをまとめる部分
    /// </summary>
    public class SingleModeScenarioCookCuttCharaSelectGroup : MonoBehaviour
    {
        /// <summary>
        /// 設定グループの設定中のデータ
        /// </summary>
        public class CookCuttDirectCharaSelectGroupSetting
        {
            //選択中のキャラIdリスト(育成キャラ以外)
            public Dictionary<int, int> CharaIdDictonary { get; set; } = new Dictionary<int, int>();

            //選択中のキャラ性格タイプ、「_charaIdDictonary」と一対一の関係を持つ
            //ただcharaIdDictonaryが空の状態でもこちらにデータが入っているため、countが一致しない時がある
            public Dictionary<int, SingleModeCookCuttPersonalityType> CharaPersonalityDictonary { get; set; } = new Dictionary<int, SingleModeCookCuttPersonalityType>();

            //利用中のメンバー数
            public int UsingMemberCount { get; set; } = CHARA_MAX_COUNT;

            public CookCuttDirectCharaSelectGroupSetting()
            {
                for (int i = 0; i < UsingMemberCount; i++)
                {
                    CharaIdDictonary.Add(i, GameDefine.INVALID_CHARA_ID);
                    CharaPersonalityDictonary.Add(i, SingleModeCookCuttPersonalityType.None);
                }
            }

            public bool IsValidSetting()
            {
                if (UsingMemberCount != CharaIdDictonary.Count)
                {
                    return false;
                }

                if (CharaIdDictonary.Any(data => data.Key == GameDefine.INVALID_CHARA_ID))
                {
                    return false;
                }

                return true;
            }
        }

        /// <summary> 表示するキャラカウンター（育成キャラを除外） </summary>
        private const int CHARA_MAX_COUNT = 12;

        /// <summary> メインキャラを全キャラにコピーするボタン </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon MainCharaCopyButton;

        /// <summary> 全キャラをランダム抽選 </summary>
        [field: SerializeField, RenameField]
        public ButtonCommon RandomButton;

        /// <summary> 最大12人選択可能 0番目はメインキャラ </summary>
        [field: SerializeField, RenameField]
        public SingleModeScenarioCookCuttDirectCharaSelectUI[] MemberCharaSelectUI;

        /// <summary>
        /// 設定中のデータ
        /// </summary>
        public CookCuttDirectCharaSelectGroupSetting SettingData = new CookCuttDirectCharaSelectGroupSetting();

        private bool _isFirstCutt = false;

        /// <summary>
        /// 育成キャラの設定値。外部から渡す
        /// </summary>
        private int _mainCharaId = GameDefine.INVALID_CHARA_ID;
        private SingleModeCookCuttPersonalityType _mainCharaCut1Personality = SingleModeCookCuttPersonalityType.None;
        private SingleModeCookCuttPersonalityType _mainCharaCut2Personality = SingleModeCookCuttPersonalityType.None;

        public void Setup(bool isFirstCutt)
        {
            RandomButton.SetOnClick(RandomSelectChara);
            MainCharaCopyButton.SetOnClick(CopyMainChara);
            foreach (var selectUI in MemberCharaSelectUI)
            {
                selectUI.Setup(UpdateSelectingCharaId, UpdateSelectingPersonality, isFirstCutt);
            }

            _isFirstCutt = isFirstCutt;
        }

        /// <summary>
        /// 保存された設定データを設定、必ずSetup実行した後で呼ぶ
        /// </summary>
        /// <param name="saveData"></param>
        public void ApplySettingData(CookCuttDirectCharaSelectGroupSetting saveData)
        {
            SettingData = saveData;
            for (int i = 0; i < SettingData.UsingMemberCount; i++)
            {
                var charaId = SettingData.CharaIdDictonary[i];
                var charaPersonality = SettingData.CharaPersonalityDictonary[i];
                MemberCharaSelectUI[i].ForceUpdateCharaId(charaId);
                MemberCharaSelectUI[i].ForceUpdateCharaPersonality(charaPersonality);
            }
        }

        /// <summary>
        /// 選択した料理に対応する性格タイプを表示
        /// </summary>
        /// <param name="nextMenuName"></param>
        public void UpdatePersonalityOptionData(string nextMenuName)
        {
            var isSpicyType = nextMenuName.Contains("麻婆豆腐");

            int materialGroupId = -1;
            var data = DEBUG_MENU_NAME_DICTONARY.FirstOrDefault(x => x.Value.Equals(nextMenuName));
            if (data.IsNull() == false)
            {
                materialGroupId = data.Key.Item2;
            }

            foreach (var charaSelectUI in MemberCharaSelectUI)
            {
                charaSelectUI.UpdatePersonalityDropdown(isSpicyType ? DEBUG_OTHER_CUTT_SPICY_TYPE_PERSONALITY_TYPE : DEBUG_OTHER_CUTT_PERSONALITY_TYPE, materialGroupId);
            }
        }

        /// <summary>
        /// 再生する演出によってメンバーキャラの表示数を更新して、
        /// 設定項目も調整
        /// </summary>
        public void UpdateMemberCount(int newCounter)
        {
            if (newCounter == SettingData.UsingMemberCount)
            {
                return;
            }

            if (_isFirstCutt)
            {
                //最初に再生するカットならキャラ数は11人となる
                SettingData.UsingMemberCount = CHARA_MAX_COUNT;
            }
            else
            {
                SettingData.UsingMemberCount = newCounter;
            }

            //アクティブを弄る場合自動レイアウトが正常に動作しないことがある
            //表示する要素数を調整
            for (int i = 0; i < CHARA_MAX_COUNT; i++)
            {
                MemberCharaSelectUI[i].SetEnable(i < SettingData.UsingMemberCount);
            }

            //除外しないキャラ設定を保存
            SettingData.CharaIdDictonary = SettingData.CharaIdDictonary.Where(data => data.Key < SettingData.UsingMemberCount).ToDictionary(data => data.Key, data => data.Value);

            var mainCharaPersonality = _isFirstCutt ? _mainCharaCut1Personality : _mainCharaCut2Personality;

            foreach (var memberSelectUI in MemberCharaSelectUI)
            {
                //非表示され要素はキャラ設定をリセット
                if (memberSelectUI.IsEnable == false)
                {
                    memberSelectUI.Reset();
                }
                else
                {
                    //無効化から再び有効になった
                    if (memberSelectUI.CharaId == GameDefine.INVALID_CHARA_ID && _mainCharaId != GameDefine.INVALID_CHARA_ID)
                    {
                        //メインキャラの設定をコピー
                        memberSelectUI.UpdateCharaDropDown(mainCharaPersonality);
                        memberSelectUI.ForceUpdateCharaId(_mainCharaId);
                        if (SettingData.CharaIdDictonary.ContainsKey(memberSelectUI.Index))
                        {
                            SettingData.CharaIdDictonary[memberSelectUI.Index] = _mainCharaId;
                        }
                        else
                        {
                            SettingData.CharaIdDictonary.Add(memberSelectUI.Index, _mainCharaId);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// メインキャラの設定を更新
        /// </summary>
        /// <param name="mainCharaId"></param>
        /// <param name="cut1Personality"></param>
        /// <param name="cut2Personality"></param>
        public void UpdateMainCharaSetting(int mainCharaId, SingleModeCookCuttPersonalityType cut1Personality, SingleModeCookCuttPersonalityType cut2Personality)
        {
            _mainCharaId = mainCharaId;
            _mainCharaCut1Personality = cut1Personality;
            _mainCharaCut2Personality = cut2Personality;
        }

        /// <summary>
        /// ランダムにキャラを選択する
        /// </summary>
        private void RandomSelectChara()
        {
            var charaData = MasterDataManager.Instance.masterCharaData.GetDictInTerm().Select(data => data.Value).ToArray();

            if (charaData.IsNullOrEmpty())
            {
                Debug.LogWarning("有効な育成ウマ娘データを取得できません！ランダム設定がスキップされました");
                return;
            }

            SettingData.CharaIdDictonary.Clear();
            //ランダム抽選
            for (int i = 0; i < SettingData.UsingMemberCount; i++)
            {
                var randomCharaId = charaData[UnityEngine.Random.Range(0, charaData.Length)].Id;
                SettingData.CharaIdDictonary.Add(i, randomCharaId);
                MemberCharaSelectUI[i].ForceUpdateCharaId(randomCharaId);
            }
        }

        /// <summary>
        /// 育成キャラを全キャラにコピー
        /// </summary>
        private void CopyMainChara()
        {

            if (_mainCharaId == GameDefine.INVALID_CHARA_ID)
            {
                Debug.LogWarning("メインキャラIdが設定されていません、コピーはスキップされました");
                return;
            }

            var mainCharaPersonality = _isFirstCutt ? _mainCharaCut1Personality : _mainCharaCut2Personality;

            SettingData.CharaIdDictonary.Clear();

            SettingData.CharaPersonalityDictonary.Clear();

            for (var i = 0; i < SettingData.UsingMemberCount; i++)
            {
                SettingData.CharaIdDictonary.Add(i, _mainCharaId);
                SettingData.CharaPersonalityDictonary.Add(i, mainCharaPersonality);

                MemberCharaSelectUI[i].ForceUpdateCharaPersonality(mainCharaPersonality);
                MemberCharaSelectUI[i].ForceUpdateCharaId(_mainCharaId);
            }
        }

        /// <summary>
        /// selectUIから呼び出すためのコールバック
        /// </summary>
        /// <param name="index"></param>
        /// <param name="newCharaId"></param>
        private void UpdateSelectingCharaId(int index, int newCharaId)
        {
            if (SettingData.CharaIdDictonary.ContainsKey(index))
            {
                SettingData.CharaIdDictonary[index] = newCharaId;
            }
            else
            {
                SettingData.CharaIdDictonary.Add(index, newCharaId);
            }
        }

        /// <summary>
        /// selectUIから呼び出すためのコールバック
        /// </summary>
        /// <param name="index"></param>
        /// <param name="newPsersonality"></param>
        private void UpdateSelectingPersonality(int index, SingleModeCookCuttPersonalityType newPsersonality)
        {
            if (SettingData.CharaPersonalityDictonary.ContainsKey(index))
            {
                SettingData.CharaPersonalityDictonary[index] = newPsersonality;
            }
            else
            {
                SettingData.CharaPersonalityDictonary.Add(index, newPsersonality);
            }
        }
    }
}
#endif