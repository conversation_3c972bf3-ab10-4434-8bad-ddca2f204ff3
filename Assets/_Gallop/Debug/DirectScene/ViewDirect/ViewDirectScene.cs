#if CYG_DEBUG
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// ビュー直接起動シーン：見た目、SerializeField
    /// </summary>
    public class ViewDirectScene : DirectSceneBase
    {
        [field: SerializeField, RenameField]
        public Canvas CanvasRoot { get; private set; }

        [field: SerializeField, RenameField]
        public Dropdown SelectViewDropDown { get; private set; }

        [field: SerializeField, RenameField]
        public Button JumpButton { get; private set; }

        [field: SerializeField, RenameField]
        public Camera Camera { get; private set; }
    }

    /// <summary>
    /// ビュー直接起動シーン：コントローラー
    /// </summary>
    public class ViewDirectSceneController : SceneControllerBase<ViewDirectScene>
    {
#if UNITY_EDITOR
        // 選択したビューを記憶するKey
        private const string PREFS_SAVE_KEY = "ViewDirectSelectedViewName";
#endif
        private string DEFAULT_VIEW_NAME => SceneDefine.ViewId.Splash.ToString();

        // 事前の通信や前提となる処理が必要で、直接ジャンプできないView
        private static readonly HashSet<SceneDefine.ViewId> IGNORE_VIEW_LIST = new HashSet<SceneDefine.ViewId>
        {
            SceneDefine.ViewId.None,
            SceneDefine.ViewId.Max,
        };

        private List<Dropdown.OptionData> _showViewList = new List<Dropdown.OptionData>((int)SceneDefine.ViewId.Max + 1);

        /// <summary>
        /// 連打対策フラグ
        /// </summary>
        private bool _runChangeView;

        /// <summary>
        /// シーンの初期化
        /// </summary>
        /// <returns></returns>
        public override IEnumerator InitializeScene()
        {
            UIManager.Instance.SetCameraTargetFromUITexture(_scene.Camera);

            // ドロップダウンリスト設定
            var viewIdList = EnumUtil.GetEnumArray<SceneDefine.ViewId>();
            for (int i = 0; i < viewIdList.Length; ++i)
            {
                if (IGNORE_VIEW_LIST.Contains(viewIdList[i]))
                {
                    // 無視リストに入っているなら表示対象にしない
                    continue;
                }
                _showViewList.Add(new Dropdown.OptionData(viewIdList[i].ToString()));
            }
            _scene.SelectViewDropDown.ClearOptions();         // Inspectorで設定された値は破棄
            _scene.SelectViewDropDown.AddOptions(_showViewList);

            // 最初の選択項目を変更
            string defaultSelectView = DEFAULT_VIEW_NAME;
#if UNITY_EDITOR
            // セーブデータはユーザーデータ削除で消える為、Editor実行時はEditorPrefsに保存しておく
            defaultSelectView = UnityEditor.EditorPrefs.GetString(PREFS_SAVE_KEY, DEFAULT_VIEW_NAME);
#endif

            int selectIndex = _showViewList.FindIndex((value) => { return value.text == defaultSelectView; });
            _scene.SelectViewDropDown.value = selectIndex;

            // ジャンプボタン設定
            _scene.JumpButton.onClick.AddListener(OnClickJumpButton);

            _scene.CanvasRoot.gameObject.SetActive(true);

            _runChangeView = false;

            yield break;
        }

        /// <summary>
        /// シーンジャンプ実行
        /// </summary>
        public void OnClickJumpButton()
        {
            string selectViewString = _showViewList[_scene.SelectViewDropDown.value].text;
            if (!EnumUtil.TryParse<SceneDefine.ViewId>(selectViewString, out var selectedViewID))
            {
                // Enum化できない謎の値が入っている場合
                selectedViewID = SceneDefine.ViewId.Splash;
                selectViewString = DEFAULT_VIEW_NAME;
            }

#if UNITY_EDITOR
            UnityEditor.EditorPrefs.SetString(PREFS_SAVE_KEY, selectViewString);
#endif
            if (_runChangeView)
                return;

            _runChangeView = true;
            SceneManager.Instance.ChangeView(selectedViewID);
        }
    }
}
#endif
