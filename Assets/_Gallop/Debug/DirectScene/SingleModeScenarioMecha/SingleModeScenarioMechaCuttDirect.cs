#if CYG_DEBUG
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

namespace Gallop
{
    public sealed class SingleModeScenarioMechaCuttDirectController : SceneControllerBase<SingleModeScenarioMechaCuttDirect>
    {
        private SingleModeScenarioMechaOverdriveCutInHelper _overdriveCutInHelper;
        private SingleModeScenarioMechaTuningCutInHelper _tuningCutInHelper;
        private SingleModeScenarioMechaUpgradeRaceCutInHelper _upgradeRaceCutInHelper;

        private PartsSingleModeScenarioMechaOverdriveCutIn _overdriveCutInParts;
        private PartsSingleModeScenarioMechaTuningCutIn _tuningCutInParts;

        // オーバードライブ設定
        private SingleModeScenarioMechaDefine.MechaDressPhase _overdriveMechaDressPhase = SingleModeScenarioMechaDefine.MechaDressPhase.First;
        private int _overdriveBoardId = 1;
        private bool _overdriveBurst;

        // チューニング設定
        private SingleModeScenarioMechaDefine.MechaDressPhase _tuningMechaDressPhase = SingleModeScenarioMechaDefine.MechaDressPhase.First;
        private int _tuningBoardId = 1;
        private bool _tuningBurst;

        // アップグレードレース設定
        private SingleModeScenarioMechaDefine.MechaDressPhase _upgradeRaceMechaDressPhase = SingleModeScenarioMechaDefine.MechaDressPhase.First;
        private int _upgradeRaceCharaId;
        private int _upgradeRaceDressId;

        public override void RegisterDownload(DownloadPathRegister register)
        {
            // 影
            Model.Component.ShadowController.RegisterDownload(register);
            var phaseList = EnumUtil.GetEnumArray<SingleModeScenarioMechaDefine.MechaDressPhase>();
            foreach (var mechaDressPhase in phaseList)
            {
                // カット
                foreach (var masterBoard in MasterDataManager.Instance.masterSingleMode09Board.dictionary.Values)
                {
                    register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaOverdriveCutPath(mechaDressPhase, masterBoard.Id));
                }
                register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_OVERDRIVE_BURST_CUT_PATH);
                register.RegisterPathWithoutInfo(ResourcePath.SINGLE_MODE_SCENARIO_MECHA_UPGRADE_RACE_CUT_PATH);
            }
            // チューニングカット
            foreach (var masterBoard in MasterDataManager.Instance.masterSingleMode09Board.dictionary.Values)
            {
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaTuningCutPath(masterBoard.Id, true));
                register.RegisterPathWithoutInfo(ResourcePath.GetSingleModeScenarioMechaTuningCutPath(masterBoard.Id, false));
            }
        }

        public override void BeginScene()
        {
            _overdriveCutInHelper = new SingleModeScenarioMechaOverdriveCutInHelper();
            _overdriveCutInHelper.Init();
            _tuningCutInHelper = new SingleModeScenarioMechaTuningCutInHelper();
            _tuningCutInHelper.Init();
            _upgradeRaceCutInHelper = new SingleModeScenarioMechaUpgradeRaceCutInHelper();
            _upgradeRaceCutInHelper.Init();

            UIManager.Instance.SetCameraTargetFromUITexture(_scene.Camera);
            _scene.OverdrivePlayButton.SetOnClick(PlayCutInOverdrive);
            _scene.OverdrivePartsPlayButton.SetOnClick(PlayCutInOverdriveParts);
            _scene.TuningPlayButton.SetOnClick(PlayCutInTuning);
            _scene.TuningPartsPlayButton.SetOnClick(PlayCutInTuningParts);
            _scene.UpgradeRacePlayButton.SetOnClick(PlayCutInUpgradeRace);
            _scene.OverdriveBurstToggle.SetCallback(flag => _overdriveBurst = flag);
            _scene.TuningBurstToggle.SetCallback(flag => _tuningBurst = flag);
            SetupDropdown();
        }

        private void SetupDropdown()
        {
            // メカウマ娘形態
            var mechaDressPathArray = EnumUtil.GetEnumArray<SingleModeScenarioMechaDefine.MechaDressPhase>();
            var mechaDressPathList = mechaDressPathArray.Select(x => new Dropdown.OptionData(x.ToString())).ToList();
            _scene.OverdriveMechaDressPhaseDropDown.options = mechaDressPathList;
            _scene.TuningMechaDressPhaseDropDown.options = mechaDressPathList;
            _scene.UpgradeMechaDressPhaseDropDown.options = mechaDressPathList;

            _scene.OverdriveMechaDressPhaseDropDown.onValueChanged.AddListener(index => _overdriveMechaDressPhase = mechaDressPathArray[index]);
            _scene.TuningMechaDressPhaseDropDown.onValueChanged.AddListener(index => _tuningMechaDressPhase = mechaDressPathArray[index]);
            _scene.UpgradeMechaDressPhaseDropDown.onValueChanged.AddListener(index => _upgradeRaceMechaDressPhase = mechaDressPathArray[index]);

            // カットパターン
            var boardBoardIdList = MasterDataManager.Instance.masterSingleMode09Board.dictionary.Values.Select(x => x.Id).ToList();
            var boardOptionDataList = boardBoardIdList.Select(x => new Dropdown.OptionData(x.ToString())).ToList();
            _scene.OverdriveCuttIndexDropDown.options = boardOptionDataList;
            _scene.TuningCuttIndexDropDown.options = boardOptionDataList;
            _scene.OverdriveCuttIndexDropDown.onValueChanged.AddListener(index => _overdriveBoardId = boardBoardIdList[index]);
            _scene.TuningCuttIndexDropDown.onValueChanged.AddListener(index => _tuningBoardId = boardBoardIdList[index]);

            // アップグレードレース
            SetupUpgradeRace();
        }

        /// <summary>
        /// アップグレードレースの設定
        /// </summary>
        private void SetupUpgradeRace()
        {
            SetupUpgradeRaceCharaSelect();// キャラ選択
            SetupUpgradeRaceDressSelect();// 衣装選択
            SetupUpgradeRaceCardSelect();// カード選択
        }

        /// <summary>
        /// アップグレードレース：キャラ選択
        /// </summary>
        private void SetupUpgradeRaceCharaSelect()
        {
            var charaDataList = MasterDataManager.Instance.masterCharaData.dictionary.Values.OrderBy(x => x.Id).ToList();
            _scene.UpgradeRaceCharaSelectButton.SetOnClick(() =>
            {
                //キャラ選択ダイアログ
                DebugUIPartsDialogSelectorCharaData.PushDialog(charaDataList, GetIndex(), selectData =>
                {
                    SetUpgradeRaceCharaId(selectData.Id);
                    SetUpgradeRaceDressId(GetDressDataList()[0].Id);
                });
            });

            // 上下ボタン
            _scene.UpgradeRaceCharaSelectUpButton.SetOnClick(() => SelectIndex(-1));
            _scene.UpgradeRaceCharaSelectDownButton.SetOnClick(() => SelectIndex(1));

            void SelectIndex(int addIndex)
            {
                var index = Mathf.Clamp(GetIndex() + addIndex, 0, charaDataList.Count - 1);
                SetUpgradeRaceCharaId(charaDataList[index].Id);
                SetUpgradeRaceDressId(GetDressDataList()[0].Id);
            }

            int GetIndex()
            {
                return charaDataList.FindIndex(x => x.Id == _upgradeRaceCharaId);
            }
        }

        /// <summary>
        /// アップグレードレース：衣装選択
        /// </summary>
        private void SetupUpgradeRaceDressSelect()
        {
            _scene.UpgradeRaceDressSelectButton.SetOnClick(() =>
            {
                // 衣装選択ダイアログ
                DebugUIPartsDialogSelectorDressData.PushDialog(GetDressDataList(), GetIndex(), selectData =>
                {
                    SetUpgradeRaceDressId(selectData.Id);
                });
            });

            // 上下ボタン
            _scene.UpgradeRaceDressSelectUpButton.SetOnClick(() => SelectIndex(-1));
            _scene.UpgradeRaceDressSelectDownButton.SetOnClick(() => SelectIndex(1));

            void SelectIndex(int addIndex)
            {
                var dataList = GetDressDataList();
                var index = Mathf.Clamp(GetIndex() + addIndex, 0, dataList.Count - 1);
                SetUpgradeRaceDressId(dataList[index].Id);
            }

            // 選択中衣装のINDEX
            int GetIndex()
            {
                return GetDressDataList().FindIndex(x => x.Id == _upgradeRaceDressId);
            }
        }

        /// <summary>
        /// 選択中キャラの衣装リスト取得
        /// </summary>
        private List<MasterDressData.DressData> GetDressDataList()
        {
            //私服除外したキャラ衣装リスト
            var dressDataList = MasterDataManager.Instance.masterDressData.GetListWithCharaIdOrderByIdAsc(_upgradeRaceCharaId).FindAll(x => !x.IsPrivateDress());
            var cardData = MasterDataManager.Instance.masterCardData.GetWithCharaIdOrderByIdAsc(_upgradeRaceCharaId);
            if (cardData != null)
            {
                // デフォがスターティングフューチャーなどのキャラは追加しておく
                if (!dressDataList.Exists(x => x.Id == cardData.DefaultRarityData.RaceDressId))
                {
                    dressDataList.Add(MasterDataManager.Instance.masterDressData.Get(cardData.DefaultRarityData.RaceDressId));
                }
            }
            return dressDataList;
        }

        private class DataModel : DebugUIPartsDialogSelector.IData
        {
            public MasterCardData.CardData MasterData;
            public string Name => MasterData.Name;
            public Texture2D Texture => ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetCardDefaultDressThumbnailIconPath(MasterData.Id));
        }

        /// <summary>
        /// アップグレードレース：カード（育成ウマ娘）選択
        /// </summary>
        private void SetupUpgradeRaceCardSelect()
        {
            var cardDataList = MasterDataManager.Instance.masterCardData.dictionary.Values.Where(x => !x.IsDummyCard).OrderBy(x => x.Id).ToList();
            _scene.UpgradeRaceCardSelectButton.SetOnClick(() =>
            {
                //カード選択ダイアログ
                DebugUIPartsDialogSelectorCardData.PushDialog(cardDataList, GetIndex(), SetUpgradeRaceCardData);
            });
            // 初期選択
            SetUpgradeRaceCardData(cardDataList[0]);


            // 上下ボタン
            _scene.UpgradeRaceCardSelectUpButton.SetOnClick(() => SelectIndex(-1));
            _scene.UpgradeRaceCardSelectDownButton.SetOnClick(() => SelectIndex(1));

            void SelectIndex(int addIndex)
            {
                var index = Mathf.Clamp(GetIndex() + addIndex, 0, cardDataList.Count - 1);
                SetUpgradeRaceCardData(cardDataList[index]);
            }

            // 選択中のキャラと衣装が一致するカードのINDEX
            int GetIndex()
            {
                var index = cardDataList.FindIndex(x =>
                    x.CharaId == _upgradeRaceCharaId &&
                    (x.DefaultRarityData.RaceDressId == _upgradeRaceDressId || //デフォ衣装 or ★５時衣装が一致
                     MasterDataManager.Instance.masterCardRarityData.GetWithCardIdAndRarity(x.Id, (int)GameDefine.CARD_RARITY_MAX).RaceDressId == _upgradeRaceDressId));

                return index;
            }

        }


        /// <summary>
        /// カード指定のキャラと衣装を設定
        /// </summary>
        private void SetUpgradeRaceCardData(MasterCardData.CardData data)
        {
            if (data == null) return;

            SetUpgradeRaceCharaId(data.CharaId);

            var rarityData = MasterDataManager.Instance.masterCardRarityData.GetWithCardIdAndRarity(data.Id, (int)GameDefine.CARD_RARITY_MAX);
            if (rarityData == null)
            {
                // 育成キャラとしてレアリティデータまでは存在してない場合の保険としてデフォルト勝負服探す
                var dressData = MasterDataManager.Instance.masterDressData.GetWithCharaIdAndCostumeType(data.CharaId, MasterDressData.DEFAULT_COSTUME_TYPE);
                if (dressData != null)
                {
                    SetUpgradeRaceDressId(dressData.Id);
                }
            }
            else
            {
                SetUpgradeRaceDressId(rarityData.RaceDressId);
            }
        }

        /// <summary>
        /// キャラ設定
        /// </summary>
        private void SetUpgradeRaceCharaId(int charaId)
        {
            _upgradeRaceCharaId = charaId;
            _scene.UpgradeRaceCharaText.text = MasterDataUtil.GetCharaNameByCharaId(charaId);
        }

        /// <summary>
        /// 衣装設定
        /// </summary>
        private void SetUpgradeRaceDressId(int dressId)
        {
            _upgradeRaceDressId = dressId;
            _scene.UpgradeRaceDressText.text = MasterDataManager.Instance.masterDressData.Get(dressId).Name;
        }

        public override void UpdateScene()
        {
            _overdriveCutInHelper?.AlterUpdate();
            _tuningCutInHelper?.AlterUpdate();
            _upgradeRaceCutInHelper?.AlterUpdate();
            if (_overdriveCutInParts != null)
            {
                _overdriveCutInParts?.UpdateView();
            }
            if (_tuningCutInParts != null)
            {
                _tuningCutInParts.UpdateView();
            }
        }

        public override void LateUpdateScene()
        {
            _overdriveCutInHelper?.AlterLateUpdate();
            _tuningCutInHelper?.AlterLateUpdate();
            _upgradeRaceCutInHelper?.AlterLateUpdate();
            if (_overdriveCutInParts != null)
            {
                _overdriveCutInParts.LateUpdateView();
            }
            if (_tuningCutInParts != null)
            {
                _tuningCutInParts.LateUpdateView();
            }
        }

        private void RegisterDownloadModel(DownloadPathRegister register, int charaId, int dressId)
        {
            var characterBuildInfo = new CharacterBuildInfo(
                charaId,
                dressId,
                ModelLoader.ControllerType.CutIn
            );
            characterBuildInfo.RegistDownloadAssetBundle(register);
        }

        /// <summary>
        /// オーバードライブカット再生
        /// </summary>
        private void PlayCutInOverdrive()
        {
            var context = new SingleModeScenarioMechaOverdriveCutInHelper.Context(_overdriveMechaDressPhase, _overdriveBoardId, _overdriveBurst);
            var register = DownloadManager.GetNewRegister();
            RegisterDownloadModel(register, context.CharaId, context.DressId);
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                OnStartCutIn();
                _overdriveCutInHelper.Play(context, () =>
                {
                    _overdriveCutInHelper.CleanUp();
                    _overdriveCutInHelper.CleanupPlaying();
                    OnFinishCutIn();
                });
            });
        }

        /// <summary>
        /// オーバードライブ演出をインゲームと同様の仕組みで再生
        /// </summary>
        private void PlayCutInOverdriveParts()
        {
            var context = new SingleModeScenarioMechaOverdriveCutInHelper.Context(_overdriveMechaDressPhase, _overdriveBoardId, _overdriveBurst);
            var register = DownloadManager.GetNewRegister();
            RegisterDownloadModel(register, context.CharaId, context.DressId);
            PartsSingleModeScenarioMechaOverdriveCutIn.RegisterDownload(register);
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                OnStartCutIn();
                _overdriveCutInParts = PartsSingleModeScenarioMechaOverdriveCutIn.CreateAndPlay(context, () =>
                {
                    _overdriveCutInParts.DestroyCutIn();
                    GameObject.Destroy(_overdriveCutInParts.gameObject);
                    _overdriveCutInParts = null;
                    OnFinishCutIn();
                });
            });
        }

        /// <summary>
        /// チューニングカット再生
        /// </summary>
        private void PlayCutInTuning()
        {
            var context = new SingleModeScenarioMechaTuningCutInHelper.Context(_tuningMechaDressPhase, _tuningBoardId, _tuningBurst);
            var register = DownloadManager.GetNewRegister();
            RegisterDownloadModel(register, context.CharaId, context.DressId);
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                OnStartCutIn();
                _tuningCutInHelper.Play(context, (_) =>
                {
                    _tuningCutInHelper.CleanUp();
                    _tuningCutInHelper.CleanupPlaying();
                    OnFinishCutIn();
                });
            });
        }

        /// <summary>
        /// チューニング演出をインゲームと同様の仕組みで再生
        /// </summary>
        private void PlayCutInTuningParts()
        {
            var context = new SingleModeScenarioMechaTuningCutInHelper.Context(_tuningMechaDressPhase, _tuningBoardId, _tuningBurst);
            var register = DownloadManager.GetNewRegister();
            RegisterDownloadModel(register, context.CharaId, context.DressId);
            PartsSingleModeScenarioMechaTuningCutIn.RegisterDownload(register);
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                OnStartCutIn();
                _tuningCutInParts = PartsSingleModeScenarioMechaTuningCutIn.CreateAndPlay(context, () =>
                {
                    _tuningCutInParts.DestroyCutIn();
                    GameObject.Destroy(_tuningCutInParts.gameObject);
                    _tuningCutInParts = null;
                    OnFinishCutIn();
                });
            });
        }

        /// <summary>
        /// アップグレードレースカット再生
        /// </summary>
        private void PlayCutInUpgradeRace()
        {
            var context = new SingleModeScenarioMechaUpgradeRaceCutInHelper.Context(_upgradeRaceMechaDressPhase, _upgradeRaceCharaId, _upgradeRaceDressId);
            var register = DownloadManager.GetNewRegister();
            RegisterDownloadModel(register, context.CharaId1, context.DressId1);
            RegisterDownloadModel(register, context.CharaId2, context.DressId2);
            DownloadManager.Instance.FixDownloadList(ref register);
            DownloadManager.Instance.DownloadFromRegister(() =>
            {
                OnStartCutIn();
                _upgradeRaceCutInHelper.Play(context, () =>
                {
                    _upgradeRaceCutInHelper.CleanUp();
                    _upgradeRaceCutInHelper.CleanupPlaying();
                    SingleModeScenarioMechaUtils.FadeInOnEndUgeCutt();
                    OnFinishCutIn();
                });
            });
        }

        private void OnStartCutIn()
        {
            _scene.Canvas.enabled = false;
            _scene.Camera.enabled = false;
        }
        private void OnFinishCutIn()
        {
            _scene.Canvas.enabled = true;
            _scene.Camera.enabled = true;
        }
    }

    public class SingleModeScenarioMechaCuttDirect : DirectSceneBase
    {
        [SerializeField] public Canvas Canvas;
        [SerializeField] public Camera Camera;

        [SerializeField] public DropDownCommon OverdriveMechaDressPhaseDropDown;
        [SerializeField] public DropDownCommon OverdriveCuttIndexDropDown;
        [SerializeField] public ToggleCommon OverdriveBurstToggle;
        [SerializeField] public ButtonCommon OverdrivePlayButton;
        [SerializeField] public ButtonCommon OverdrivePartsPlayButton;

        [SerializeField] public DropDownCommon TuningMechaDressPhaseDropDown;
        [SerializeField] public DropDownCommon TuningCuttIndexDropDown;
        [SerializeField] public ToggleCommon TuningBurstToggle;
        [SerializeField] public ButtonCommon TuningPlayButton;
        [SerializeField] public ButtonCommon TuningPartsPlayButton;

        [SerializeField] public TextCommon UpgradeRaceCharaText;
        [SerializeField] public TextCommon UpgradeRaceDressText;

        [SerializeField] public ButtonCommon UpgradeRaceCharaSelectButton;
        [SerializeField] public ButtonCommon UpgradeRaceCharaSelectUpButton;
        [SerializeField] public ButtonCommon UpgradeRaceCharaSelectDownButton;

        [SerializeField] public ButtonCommon UpgradeRaceDressSelectButton;
        [SerializeField] public ButtonCommon UpgradeRaceDressSelectUpButton;
        [SerializeField] public ButtonCommon UpgradeRaceDressSelectDownButton;

        [SerializeField] public ButtonCommon UpgradeRaceCardSelectButton;
        [SerializeField] public ButtonCommon UpgradeRaceCardSelectUpButton;
        [SerializeField] public ButtonCommon UpgradeRaceCardSelectDownButton;

        [SerializeField] public DropDownCommon UpgradeMechaDressPhaseDropDown;
        [SerializeField] public ButtonCommon UpgradeRacePlayButton;
    }
}

#endif