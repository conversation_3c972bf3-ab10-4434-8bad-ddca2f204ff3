#if CYG_DEBUG

using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.Linq;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeSupportCardCutInInfoSetter : MonoBehaviour
    {
        [Header("カットインタイプ選択")]
        [SerializeField] private ButtonCommon TagTrainingCutInButton = null;
        [SerializeField] private ImageCommon TagTrainingCutInButtonOn = null;
        [SerializeField] private ImageCommon TagTrainingCutInButtonOff = null;

        [SerializeField] private ButtonCommon TeamRaceMemberCutInButton = null;
        [SerializeField] private ImageCommon TeamRaceMemberCutInButtonOn = null;
        [SerializeField] private ImageCommon TeamRaceMemberCutInButtonOff = null;

        [SerializeField] private ButtonCommon TeamRaceMemberSoulCutInButton = null;
        [SerializeField] private ImageCommon TeamRaceMemberSoulCutInButtonOn = null;
        [SerializeField] private ImageCommon TeamRaceMemberSoulCutInButtonOff = null;

        [SerializeField] private ButtonCommon TeamRaceMemberSpSoulCutInButton = null;
        [SerializeField] private ImageCommon TeamRaceMemberSpSoulCutInButtonOn = null;
        [SerializeField] private ImageCommon TeamRaceMemberSpSoulCutInButtonOff = null;

        [Header("サポートカード選択")]
        [SerializeField] private Dropdown SupportCardIdDropdown = null;
        [SerializeField] private InputField SearchSupportCardInputField = null;
        [SerializeField] private ButtonCommon EntryCutInMemberButton = null;
        [SerializeField] private ButtonCommon EntryCutInMemberAllButton = null;
        [SerializeField] private ButtonCommon RemoveAllCutInMemberButton = null;

        private System.Action<SingleModeSupportCardCutInDefine.CutInType> _onClickCutInTypeButton;
        private System.Action<MasterSupportCardData.SupportCardData[]> _onClickEntryCutInMemberButton;
        private System.Action _onClickRemoveAllCutInMemberButton;


        public void Setup(
            System.Action<SingleModeSupportCardCutInDefine.CutInType> onClickCutInTypeButton, 
            System.Action<MasterSupportCardData.SupportCardData[]> onClickEntryCutInMemberButton,
            System.Action onClickRemoveAllCutInMemberButton)
        {
            _onClickCutInTypeButton = onClickCutInTypeButton;
            _onClickEntryCutInMemberButton = onClickEntryCutInMemberButton;
            _onClickRemoveAllCutInMemberButton = onClickRemoveAllCutInMemberButton;

            SetupCutInTypeButton();

            SetSupportCardDropDown(SingleModeSupportCardCutInModel.MasterSupportCardDataArray);
            SearchSupportCardInputField.onValueChanged.AddListener((text) => OnValueChangedSearchSupportCardInputField(text));

            EntryCutInMemberButton.SetOnClick(OnClickEntryCutInMemberButton);
            EntryCutInMemberAllButton.SetOnClick(OnClickEntryCutInMemberAllButton);
            RemoveAllCutInMemberButton.SetOnClick(OnClickRemoveAllCutInMemberButton);

            // 友情トレーニングを初期選択しておく
            OnClickTagTrainingCutInButton();
        }

        private void SetupCutInTypeButton()
        {
            TagTrainingCutInButton.SetOnClick(OnClickTagTrainingCutInButton);
            TeamRaceMemberCutInButton.SetOnClick(OnClickTeamRaceMemberCutInButton);
            TeamRaceMemberSoulCutInButton.SetOnClick(OnClickTeamRaceMemberSoulCutInButton);
            TeamRaceMemberSpSoulCutInButton.SetOnClick(OnClickTeamRaceMemberSpSoulCutInButton);
        }

        private void OnClickTagTrainingCutInButton()
        {
            _onClickCutInTypeButton?.Invoke(SingleModeSupportCardCutInDefine.CutInType.TagTraining);

            TagTrainingCutInButtonOn.SetActiveWithCheck(true);
            TagTrainingCutInButtonOff.SetActiveWithCheck(false);

            TeamRaceMemberCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberSoulCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberSoulCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberSpSoulCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberSpSoulCutInButtonOff.SetActiveWithCheck(true);
        }


        private void OnClickTeamRaceMemberCutInButton()
        {
            _onClickCutInTypeButton?.Invoke(SingleModeSupportCardCutInDefine.CutInType.TeamRaceMember);

            TagTrainingCutInButtonOn.SetActiveWithCheck(false);
            TagTrainingCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberCutInButtonOn.SetActiveWithCheck(true);
            TeamRaceMemberCutInButtonOff.SetActiveWithCheck(false);

            TeamRaceMemberSoulCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberSoulCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberSpSoulCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberSpSoulCutInButtonOff.SetActiveWithCheck(true);
        }

        private void OnClickTeamRaceMemberSoulCutInButton()
        {
            _onClickCutInTypeButton?.Invoke(SingleModeSupportCardCutInDefine.CutInType.TeamRaceMemberSoul);

            TagTrainingCutInButtonOn.SetActiveWithCheck(false);
            TagTrainingCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberSoulCutInButtonOn.SetActiveWithCheck(true);
            TeamRaceMemberSoulCutInButtonOff.SetActiveWithCheck(false);

            TeamRaceMemberSpSoulCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberSpSoulCutInButtonOff.SetActiveWithCheck(true);
        }

        private void OnClickTeamRaceMemberSpSoulCutInButton()
        {
            _onClickCutInTypeButton?.Invoke(SingleModeSupportCardCutInDefine.CutInType.TeamRaceMemberSpSoul);

            TagTrainingCutInButtonOn.SetActiveWithCheck(false);
            TagTrainingCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberSoulCutInButtonOn.SetActiveWithCheck(false);
            TeamRaceMemberSoulCutInButtonOff.SetActiveWithCheck(true);

            TeamRaceMemberSpSoulCutInButtonOn.SetActiveWithCheck(true);
            TeamRaceMemberSpSoulCutInButtonOff.SetActiveWithCheck(false);
        }


        /// <summary>
        /// サポートカード ドロップダウン設定
        /// </summary>
        private void SetSupportCardDropDown(MasterSupportCardData.SupportCardData[] masterSupportCardDataArray)
        {
            SupportCardIdDropdown.options.Clear();
            foreach (var masterSupportCardData in masterSupportCardDataArray)
            {
                SupportCardIdDropdown.options.Add(new Dropdown.OptionData(masterSupportCardData.Name));
            }
            SupportCardIdDropdown.value = 0;
            SupportCardIdDropdown.RefreshShownValue();
        }

        /// <summary>
        /// サポートカード絞り込み入力イベント
        /// </summary>
        /// <param name="text"></param>
        private void OnValueChangedSearchSupportCardInputField(string text)
        {
            var masterSupportCardDataArray = SingleModeSupportCardCutInModel.GetMasterSupportCardDataArray(text);
            SetSupportCardDropDown(masterSupportCardDataArray);
        }

        /// <summary>
        /// メンバー追加ボタンイベント
        /// </summary>
        private void OnClickEntryCutInMemberButton()
        {
            var masterSupportCardDataList = new List<MasterSupportCardData.SupportCardData>();
            masterSupportCardDataList.Add(DropDownSelectMasterSupportCardData);

            _onClickEntryCutInMemberButton?.Invoke(masterSupportCardDataList.ToArray());
        }

        /// <summary>
        /// まとめてメンバー追加ボタンイベント
        /// </summary>
        private void OnClickEntryCutInMemberAllButton()
        {
            var masterSupportCardDataList = new List<MasterSupportCardData.SupportCardData>();
            masterSupportCardDataList.Add(DropDownSelectMasterSupportCardData);
            masterSupportCardDataList.Add(DropDownSelectMasterSupportCardData);
            masterSupportCardDataList.Add(DropDownSelectMasterSupportCardData);
            masterSupportCardDataList.Add(DropDownSelectMasterSupportCardData);
            masterSupportCardDataList.Add(DropDownSelectMasterSupportCardData);

            _onClickEntryCutInMemberButton?.Invoke(masterSupportCardDataList.ToArray());
        }

        private MasterSupportCardData.SupportCardData DropDownSelectMasterSupportCardData
        {
            get
            {
                // ドロップダウンに羅列されているサポートカード情報リストを取得
                var searchText = SearchSupportCardInputField.text;
                var masterSupportCardDataArray = SingleModeSupportCardCutInModel.GetMasterSupportCardDataArray(searchText);
                if (masterSupportCardDataArray.IsNullOrEmpty()) return null;

                // ドロップダウンが選択しているサポートカード情報を取得
                return masterSupportCardDataArray[SupportCardIdDropdown.value];
            }
        }

        private void OnClickRemoveAllCutInMemberButton()
            => _onClickRemoveAllCutInMemberButton?.Invoke();

    }

    public static class SingleModeSupportCardCutInModel
    {
        public static MasterSupportCardData.SupportCardData[] MasterSupportCardDataArray
            => MasterDataManager.Instance.masterSupportCardData.dictionary.Values
                .OrderByDescending(masterSupportCardData => masterSupportCardData.Id) // ID降順にする
                .ToArray();


        public static MasterSupportCardData.SupportCardData[] GetMasterSupportCardDataArray(string text)
        {
            if (string.IsNullOrEmpty(text)) return MasterSupportCardDataArray; // 絞り込み指定が空なら全件返却

            // IDと名前でテキスト検索
            return GetMasterSupportCardDataArrayById(text).Union(GetMasterSupportCardDataArrayByName(text)).ToArray();
        }

        /// <summary>
        /// サポートカード名前検索
        /// </summary>
        private static MasterSupportCardData.SupportCardData[] GetMasterSupportCardDataArrayByName(string text)
        {
            var textMatchingHelper = new TextMatchingHelper();
            return MasterSupportCardDataArray
                .Where(masterSupportCardData => textMatchingHelper.IsMatch(text, masterSupportCardData.Name)).ToArray();
        }

        /// <summary>
        /// サポートカードID検索
        /// </summary>
        private static MasterSupportCardData.SupportCardData[] GetMasterSupportCardDataArrayById(string text)
        {
            var textMatchingHelper = new TextMatchingHelper(100);
            return MasterSupportCardDataArray.Where(masterSupportCardData =>
                textMatchingHelper.IsMatch(text, masterSupportCardData.Id.ToString())).ToArray();
        }

    }

}




#endif