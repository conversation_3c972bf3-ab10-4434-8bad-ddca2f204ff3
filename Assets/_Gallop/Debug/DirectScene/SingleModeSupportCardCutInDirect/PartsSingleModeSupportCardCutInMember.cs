#if CYG_DEBUG

using UnityEngine;

namespace Gallop
{
    [AddComponentMenu("")]
    public class PartsSingleModeSupportCardCutInMember : MonoBehaviour
    {
        [SerializeField] private TextCommon NameText = null;
        [SerializeField] private ButtonCommon RemoveButton = null;

        private MasterSupportCardData.SupportCardData _masterSupportCardData;
        private System.Action<MasterSupportCardData.SupportCardData> _onClickRemove;

        public void Setup(MasterSupportCardData.SupportCardData masterSupportCardData, System.Action<MasterSupportCardData.SupportCardData> onClickRemove)
        {
            _masterSupportCardData = masterSupportCardData;
            _onClickRemove = onClickRemove;

            NameText.text = _masterSupportCardData.Titlename + "\n" + _masterSupportCardData.Charaname;
            RemoveButton.SetOnClick(OnClickRemoveButton);
        }

        private void OnClickRemoveButton()
        {
            _onClickRemove?.Invoke(_masterSupportCardData);
        }

    }
}

#endif