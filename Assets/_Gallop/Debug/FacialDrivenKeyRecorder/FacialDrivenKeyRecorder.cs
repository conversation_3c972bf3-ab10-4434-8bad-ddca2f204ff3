#if (UNITY_EDITOR && CYG_DEBUG)
using System;
using System.Collections.Generic;
using System.IO;
using System.Xml;
using UnityEngine;
using Gallop;
using Gallop.Model.Component;

namespace FacialDrivenKeyRecorder
{
    /// ******************************************************************************************
    /// <summary>
    /// FacialDrivenKeyRecorderRecorder
    /// フェイシャルのドリブンキー情報を記録するコンポーネント
    /// </summary>
    /// ******************************************************************************************
    public class FacialDrivenKeyRecorder : MonoBehaviour
    {
        //======================================================================
        // 変数
        //======================================================================

        private RootInfo _rootInfo = null;

        //======================================================================
        // プロパティ
        //======================================================================

        public RootInfo rootInfo { get { return _rootInfo; } }

        //======================================================================
        // メソッド
        //======================================================================

        /// ----------------------------------------
        /// <summary>
        /// Start
        /// </summary>
        /// ----------------------------------------
        private void Start()
        {
            _rootInfo = new RootInfo(this.gameObject);
        }

        /// ----------------------------------------
        /// <summary>
        /// LateUpdate
        /// </summary>
        /// ----------------------------------------
        private void LateUpdate()
        {
            _rootInfo.Update();
        }

        /// ----------------------------------------
        /// <summary>
        /// レコーディング開始
        /// </summary>
        /// ----------------------------------------
        public void StartRecording()
        {
            _rootInfo.StartRecording();
        }

        /// ----------------------------------------
        /// <summary>
        /// ポーズとレジュームの切替え
        /// </summary>
        /// ----------------------------------------
        public void PauseAndResumeRecording()
        {
            _rootInfo.PauseAndResumeRecording();
        }

        /// ----------------------------------------
        /// <summary>
        /// レコーディングリセット
        /// </summary>
        /// ----------------------------------------
        public void ResetRecording()
        {
            _rootInfo.ResetRecording(true);
        }

        /// ----------------------------------------
        /// <summary>
        /// データ出力
        /// </summary>
        /// ----------------------------------------
        public void ExportData()
        {
            _rootInfo.ExportData();
        }

        /// ----------------------------------------
        /// <summary>
        /// 出力ディレクトリを開く
        /// </summary>
        /// ----------------------------------------
        public void OpenOutputDirectory()
        {
            _rootInfo.OpenOutputDirectory();
        }

        /// ******************************************************************************************
        /// <summary>
        /// ルート情報クラス
        /// </summary>
        /// ******************************************************************************************
        [System.Serializable]
        public class RootInfo
        {
            //======================================================================
            // 変数
            //======================================================================

            private bool _exist = false;

            private GameObject _targetObject = null;
            private DrivenKeyComponent _component = null;
            private CheekController _cheekCtrl = null;

            private List<BaseKeyInfo> _drivenKeyInfoList = null;

            private Dictionary<string, int> _eyeLLabelWeightIndexDict = null;
            private Dictionary<string, int> _eyeRLabelWeightIndexDict = null;
            private Dictionary<string, int> _eyeBrowLLabelWeightIndexDict = null;
            private Dictionary<string, int> _eyeBrowRLabelWeightIndexDict = null;
            private Dictionary<string, int> _mouthLabelWeightIndexDict = null;
            private Dictionary<string, int> _earL0LabelWeightIndexDict = null;
            private Dictionary<string, int> _earR0LabelWeightIndexDict = null;
            private Dictionary<string, int> _earL1LabelWeightIndexDict = null;
            private Dictionary<string, int> _earR1LabelWeightIndexDict = null;
            private Dictionary<string, int> _earL2LabelWeightIndexDict = null;
            private Dictionary<string, int> _earR2LabelWeightIndexDict = null;

            private string _outputName = null;
            private string _outputDirPath = null;

            [System.NonSerialized]
            private List<float> _timeList = null;
            
            private int _currentCount = 0;
            private float _elapsedTime = 0;

            private bool _isRecording = false;

            //======================================================================
            // プロパティ
            //======================================================================

            public string OutputName { get { return _outputName; } }
            public string OutputDirPath { get { return _outputDirPath; } }
            
            public int CurrentCount { get { return _currentCount; } }
            
            public float ElapsedTime { get { return _elapsedTime; } }

            public List<BaseKeyInfo> DrivenKeyInfoList { get { return _drivenKeyInfoList; } }

            public DrivenKeyComponent Component { get { return _component; } }

            public CheekController CheekCtrl { get { return _cheekCtrl; } }

            //======================================================================
            // コンストラクタ
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// デフォルト
            /// </summary>
            /// ----------------------------------------
            public RootInfo(GameObject targetObject)
            {
                _targetObject = targetObject;

                CreateInfo();
            }

            //======================================================================
            // メソッド
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// 情報作成
            /// </summary>
            /// ----------------------------------------
            private void CreateInfo()
            {
                _exist = false;

                if (_targetObject == null)
                {
                    return;
                }

                //下層のトランスフォームを取得
                ModelController modelController = _targetObject.GetComponent<ModelController>() as ModelController;

                if (modelController == null)
                {
                    return;
                }

                if (modelController.DrivenKeyComponent == null)
                {
                    return;
                }

                _component = modelController.DrivenKeyComponent;
                _cheekCtrl = modelController.CheekCtrl;

                // eye
                DrivenKeyComponent.EyeWeightGroup eyeWeightGroup = modelController.DrivenKeyComponent.EyeWeightForEditor;
                List<string> eyeTargetList = new List<string>();
                for (int i = (int)Gallop.FaceGroupType.EyeR; i < (int)Gallop.FaceGroupType.EyeL + 1; ++i)
                {
                    eyeTargetList.Add(((Gallop.FaceGroupType)i).ToString());
                }

                // eyebrow
                DrivenKeyComponent.EyebrowWeightGroup eyebrowWeightGroup = modelController.DrivenKeyComponent.EyebrowWeightForEditor;
                List<string> eyebrowTargetList = new List<string>();
                for (int i = (int)Gallop.FaceGroupType.EyebrowR; i < (int)Gallop.FaceGroupType.EyebrowL + 1; ++i)
                {
                    eyebrowTargetList.Add(((Gallop.FaceGroupType)i).ToString());
                }

                // mouth
                DrivenKeyComponent.MouthWeightGroup mouthWeightGroup = modelController.DrivenKeyComponent.MouthWeightForEditor;
                List<string> mouthTargetList = new List<string>();
                for (int i = (int)Gallop.FaceGroupType.Mouth; i < (int)Gallop.FaceGroupType.Mouth + 1; ++i)
                {
                    mouthTargetList.Add(((Gallop.FaceGroupType)i).ToString());
                }

                // ear
                DrivenKeyComponent.EarWeightGroup earWeightGroup = modelController.DrivenKeyComponent.EarWeightForEditor;
                List<string> earTargetList = new List<string>();
                for (int i = (int)Gallop.FaceGroupType.EarR; i < (int)Gallop.FaceGroupType.EarL + 1; ++i)
                {
                    earTargetList.Add(((Gallop.FaceGroupType)i).ToString() + "0");
                    earTargetList.Add(((Gallop.FaceGroupType)i).ToString() + "1");
                    earTargetList.Add(((Gallop.FaceGroupType)i).ToString() + "2");
                }

                _drivenKeyInfoList = new List<BaseKeyInfo>();

                _eyeLLabelWeightIndexDict = new Dictionary<string, int>();
                _eyeRLabelWeightIndexDict = new Dictionary<string, int>();
                _eyeBrowLLabelWeightIndexDict = new Dictionary<string, int>();
                _eyeBrowRLabelWeightIndexDict = new Dictionary<string, int>();
                _mouthLabelWeightIndexDict = new Dictionary<string, int>();
                _earL0LabelWeightIndexDict = new Dictionary<string, int>();
                _earR0LabelWeightIndexDict = new Dictionary<string, int>();
                _earL1LabelWeightIndexDict = new Dictionary<string, int>();
                _earR1LabelWeightIndexDict = new Dictionary<string, int>();
                _earL2LabelWeightIndexDict = new Dictionary<string, int>();
                _earR2LabelWeightIndexDict = new Dictionary<string, int>();

                SetLabelWeightIndexDict(eyeWeightGroup, eyeTargetList, (index) => ((FaceEyeType)index).ToString());
                SetLabelWeightIndexDict(eyebrowWeightGroup, eyebrowTargetList, (index) => ((FaceEyebrowType)index).ToString());
                SetLabelWeightIndexDict(mouthWeightGroup, mouthTargetList, (index) => ((FaceMouthType)index).ToString());
                SetLabelWeightIndexDict(earWeightGroup, earTargetList, (index) => ((EarType)index).ToString());

                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EyeL", eyeWeightGroup, _eyeLLabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EyeR", eyeWeightGroup, _eyeRLabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EyebrowL", eyebrowWeightGroup, _eyeBrowLLabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EyebrowR", eyebrowWeightGroup, _eyeBrowRLabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "Mouth", mouthWeightGroup, _mouthLabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EarL0", earWeightGroup, _earL0LabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EarR0", earWeightGroup, _earR0LabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EarL1", earWeightGroup, _earL1LabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EarR1", earWeightGroup, _earR1LabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EarL2", earWeightGroup, _earL2LabelWeightIndexDict));
                _drivenKeyInfoList.Add(new DrivenKeyInfo(this, "EarR2", earWeightGroup, _earR2LabelWeightIndexDict));

                _drivenKeyInfoList.Add(new CheekKeyInfo(this, "Cheek", _cheekCtrl));
                _drivenKeyInfoList.Add(new EyehiKeyInfo(this, "Eyehi", modelController));

                if (_drivenKeyInfoList.Count == 0)
                {
                    return;
                }

                //時間リストの作成
                _timeList = new List<float>();

                //出力名の確定
                _outputName = _targetObject.name;

                if (_targetObject.transform.parent != null)
                {
                    _outputName = _targetObject.transform.parent.name + "_" + _outputName;

                    if (_targetObject.transform.parent.parent != null)
                    {
                        _outputName = _targetObject.transform.parent.parent.name + "_" + _outputName;
                    }
                }

                //出力フォルダ
                _outputDirPath = Path.GetDirectoryName(Application.dataPath) + "/DrivenKeyRecorder/" + _outputName;

                _exist = true;
            }

            private void SetLabelWeightIndexDict(DrivenKeyComponent.WeightGroupBase weight, List<string> faceGroupTypeList, System.Func<int, string> getTypeAction)
            {
                var weightTable = weight.CommonWeightArray;
                var group = weight.WeightArray;
                for (int i = 0, length = group.Length; i < length; ++i)
                {
                    string label = getTypeAction(i);

                    var tableIndex = group[i].StartIndex;
                    for (int j = 0; j < group[i].Length; j++)
                    {
                        float w = weightTable[tableIndex + j];
                        string faceGroupTypeLabel = faceGroupTypeList[j];

                        if (faceGroupTypeLabel == "EyeL")
                        {
                            _eyeLLabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if(faceGroupTypeLabel == "EyeR")
                        {
                            _eyeRLabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EyebrowL")
                        {
                            _eyeBrowLLabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EyebrowR")
                        {
                            _eyeBrowRLabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "Mouth")
                        {
                            _mouthLabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EarL0")
                        {
                            _earL0LabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EarR0")
                        {
                            _earR0LabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EarL1")
                        {
                            _earL1LabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EarR1")
                        {
                            _earR1LabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EarL2")
                        {
                            _earL2LabelWeightIndexDict[label] = tableIndex + j;
                        }
                        else if (faceGroupTypeLabel == "EarR2")
                        {
                            _earR2LabelWeightIndexDict[label] = tableIndex + j;
                        }
                    }
                }
            }

            /// ----------------------------------------
            /// <summary>
            /// 更新
            /// </summary>
            /// ----------------------------------------
            public void Update()
            {
                if (!_exist)
                {
                    return;
                }

                //キー受付
                if (Input.GetKeyDown(KeyCode.J))
                {
                    StartRecording();
                }
                else if (Input.GetKeyDown(KeyCode.K))
                {
                    PauseAndResumeRecording();
                }
                else if (Input.GetKeyDown(KeyCode.L))
                {
                    ResetRecording(true);
                }
                else if (Input.GetKeyDown(KeyCode.N))
                {
                    ExportData();
                }
                else if (Input.GetKeyDown(KeyCode.M))
                {
                    OpenOutputDirectory();
                }

                if (!_isRecording)
                {
                    return;
                }

                //時間を記録
                if (_currentCount < _timeList.Count)
                {
                    _timeList[_currentCount] = _elapsedTime;
                }
                else
                {
                    _timeList.Add(_elapsedTime);
                }

                //ドリブンキー情報を記録
                foreach (BaseKeyInfo drivenKeyInfo in _drivenKeyInfoList)
                {
                    drivenKeyInfo.Update();
                }

                _elapsedTime += Time.deltaTime;
                _currentCount++;
            }

            /// ----------------------------------------
            /// <summary>
            /// レコーディング開始
            /// </summary>
            /// ----------------------------------------
            public void StartRecording()
            {
                if (!_exist)
                {
                    return;
                }

                Debug.Log("<color=yellow>Reset And Start Recording ! " + _outputName + "</color>");

                ResetRecording(false);

                _isRecording = true;
            }

            /// ----------------------------------------
            /// <summary>
            /// ポーズとレジュームの切替え
            /// </summary>
            /// ----------------------------------------
            public void PauseAndResumeRecording()
            {
                if (_isRecording)
                {
                    PauseRecording();
                }
                else
                {
                    if(_currentCount == 0)
                    {
                        StartRecording();
                    }
                    else
                    {
                        ResumeRecording();
                    }
                }
            }

            /// ----------------------------------------
            /// <summary>
            /// レコーディング再開
            /// </summary>
            /// ----------------------------------------
            public void ResumeRecording()
            {
                if (!_exist)
                {
                    return;
                }

                if (_isRecording)
                {
                    return;
                }

                if(_currentCount == 0)
                {
                    return;
                }

                Debug.Log("<color=orange>Resume Recording ! " + _outputName + "</color>");

                _isRecording = true;
            }

            /// ----------------------------------------
            /// <summary>
            /// レコーディング一時停止
            /// </summary>
            /// ----------------------------------------
            public void PauseRecording()
            {
                if (!_exist)
                {
                    return;
                }

                if (!_isRecording)
                {
                    return;
                }

                Debug.Log("<color=#ff8888>Pause Recording ! " + _outputName + "</color>");

                _isRecording = false;
            }

            /// ----------------------------------------
            /// <summary>
            /// レコーディングリセット
            /// </summary>
            /// ----------------------------------------
            public void ResetRecording(bool showLog)
            {
                if (!_exist)
                {
                    return;
                }

                if (showLog)
                {
                    Debug.Log("<color=red>Reset Recording ! " + _outputName + "</color>");
                }                

                _isRecording = false;
                
                _elapsedTime = 0;
                _currentCount = 0;
            }

            /// ----------------------------------------
            /// <summary>
            /// データ出力
            /// </summary>
            /// ----------------------------------------
            public void ExportData()
            {
#if UNITY_EDITOR
                if (!_exist)
                {
                    return;
                }

                PauseRecording();

                Debug.Log("<color=lime>Export Recording Data ! " + _outputName + "</color>");
                

                if (Directory.Exists(_outputDirPath))
                {
                    Directory.Delete(_outputDirPath, true);
                }

                Directory.CreateDirectory(_outputDirPath);               

                ExportTimeData();
                
                UnityEditor.EditorUtility.ClearProgressBar();

                int count = -1;
                foreach (BaseKeyInfo item in _drivenKeyInfoList)
                {
                    count++;

                    float progValue = (float)(count + 1) / (float)_drivenKeyInfoList.Count;
                    if (UnityEditor.EditorUtility.DisplayCancelableProgressBar(
                        "Export Recording Data",
                        string.Format("DrivenKey Info {0}/{1}", count + 1, _drivenKeyInfoList.Count),
                        progValue
                        ))
                    {
                        break;
                    }

                    item.ExportData();
                }
                
                UnityEditor.EditorUtility.ClearProgressBar();
#endif
            }

            /// ----------------------------------------
            /// <summary>
            /// 時間データを出力
            /// </summary>
            /// ----------------------------------------
            private void ExportTimeData()
            {
                string outputFileName = "TimeList.xml";
                string outputFilePath = _outputDirPath + "/" + outputFileName;

                if (File.Exists(outputFilePath))
                {
                    File.Delete(outputFilePath);
                }

                XmlDocument document = new XmlDocument();

                XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "UTF-8", null);
                XmlElement rootElement = document.CreateElement("RecordInfo");

                document.AppendChild(declaration);
                document.AppendChild(rootElement);

                XmlElement timeListElement = document.CreateElement("TimeList");

                rootElement.AppendChild(timeListElement);

                foreach (float time in _timeList)
                {
                    XmlElement timeElement = document.CreateElement("Time");

                    timeElement.SetAttribute("Time", (time - _timeList[0]).ToString());

                    timeListElement.AppendChild(timeElement);
                }

                document.Save(outputFilePath);
            }

            /// ----------------------------------------
            /// <summary>
            /// 出力ディレクトリを開く
            /// </summary>
            /// ----------------------------------------
            public void OpenOutputDirectory()
            {
                if (!_exist)
                {
                    return;
                }

                if (!Directory.Exists(_outputDirPath))
                {
                    Directory.CreateDirectory(_outputDirPath);
                }

                System.Diagnostics.Process.Start(_outputDirPath);
            }
        }

        /// ******************************************************************************************
        /// <summary>
        /// 基底の情報クラス
        /// </summary>
        /// ******************************************************************************************
        [System.Serializable]
        public class BaseKeyInfo
        {
            [System.NonSerialized]
            protected RootInfo _rootInfo = null;

            protected string _faceGroupTypeLabel = "";

            [System.NonSerialized]
            protected List<KeyInfo> _keyInfoList = null;

            [System.NonSerialized]
            protected KeyInfo _tempKeyInfo = KeyInfo.Default;

            //======================================================================
            // コンストラクタ
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// デフォルト
            /// </summary>
            /// ----------------------------------------
            public BaseKeyInfo(RootInfo rootInfo, string faceGroupTypeLabel)
            {
                _rootInfo = rootInfo;
                _faceGroupTypeLabel = faceGroupTypeLabel;
                _keyInfoList = new List<KeyInfo>();
            }

            //======================================================================
            // メソッド
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// 更新
            /// </summary>
            /// ----------------------------------------
            public virtual void Update()
            {
                return;
            }

            /// ----------------------------------------
            /// <summary>
            /// データを出力
            /// </summary>
            /// ----------------------------------------
            public void ExportData()
            {
                int thisIndex = _rootInfo.DrivenKeyInfoList.IndexOf(this);

                string outputFileName = string.Format("DrivenKeyInfo{0:D5}____{1}", thisIndex, _faceGroupTypeLabel) + ".xml";
                string outputFilePath = _rootInfo.OutputDirPath + "/" + outputFileName;

                if (File.Exists(outputFilePath))
                {
                    File.Delete(outputFilePath);
                }

                XmlDocument document = new XmlDocument();

                XmlDeclaration declaration = document.CreateXmlDeclaration("1.0", "UTF-8", null);
                XmlElement rootElement = document.CreateElement("RecordInfo");

                document.AppendChild(declaration);
                document.AppendChild(rootElement);

                XmlElement drivenKeyInfoElement = document.CreateElement("DrivenKeyInfo");

                drivenKeyInfoElement.SetAttribute("FaceGroupType", _faceGroupTypeLabel);

                rootElement.AppendChild(drivenKeyInfoElement);

                XmlElement keyInfoListElement = document.CreateElement("KeyInfoList");

                drivenKeyInfoElement.AppendChild(keyInfoListElement);

                int count = -1;
                foreach (KeyInfo keyInfo in _keyInfoList)
                {
                    count++;

                    if (count >= _rootInfo.CurrentCount)
                    {
                        break;
                    }

                    keyInfo.ExportData(keyInfoListElement);
                }

                document.Save(outputFilePath);
            }
        }

        /// ******************************************************************************************
        /// <summary>
        /// ドリブンキー情報クラス
        /// </summary>
        /// ******************************************************************************************
        [System.Serializable]
        public class DrivenKeyInfo : BaseKeyInfo
        {
            //======================================================================
            // 変数
            //======================================================================

            private DrivenKeyComponent.WeightGroupBase _weightGroup;
            private Dictionary<string, int> _labelWeightIndexDict;

            //======================================================================
            // コンストラクタ
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// デフォルト
            /// </summary>
            /// ----------------------------------------
            public DrivenKeyInfo(
                RootInfo rootInfo,
                string faceGroupTypeLabel,
                DrivenKeyComponent.WeightGroupBase weightGroup,
                Dictionary<string, int> labelWeightDict
            ) : base(rootInfo, faceGroupTypeLabel)
            {
                _weightGroup = weightGroup;
                _faceGroupTypeLabel = faceGroupTypeLabel;
                _labelWeightIndexDict = labelWeightDict;
            }

            //======================================================================
            // メソッド
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// 更新
            /// </summary>
            /// ----------------------------------------
            public override void Update()
            {
                if (_rootInfo.CurrentCount < _keyInfoList.Count)
                {
                    _tempKeyInfo = _keyInfoList[_rootInfo.CurrentCount];
                }
                else
                {
                    _tempKeyInfo = KeyInfo.Default;
                    _keyInfoList.Add(_tempKeyInfo);
                }

                _tempKeyInfo.Time = _rootInfo.ElapsedTime;

                foreach (KeyValuePair<string, int> kvp in _labelWeightIndexDict)
                {
                    _tempKeyInfo.LabelValueDict[kvp.Key] = _weightGroup.CommonWeightArray[kvp.Value];
                }

                _keyInfoList[_rootInfo.CurrentCount] = _tempKeyInfo;
            }
        }

        /// ******************************************************************************************
        /// <summary>
        /// チーク情報クラス
        /// </summary>
        /// ******************************************************************************************
        [System.Serializable]
        public class CheekKeyInfo : BaseKeyInfo
        {
            //======================================================================
            // 定数
            //======================================================================

            const string VISIBLE_KEY = "visible";
            const string INDEX_KEY = "index";

            //======================================================================
            // 変数
            //======================================================================

            private CheekController _cheekCtrl;

            //======================================================================
            // コンストラクタ
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// デフォルト
            /// </summary>
            /// ----------------------------------------
            public CheekKeyInfo(
                RootInfo rootInfo,
                string faceGroupTypeLabel,
                CheekController cheekCtrl
            ) : base(rootInfo, faceGroupTypeLabel)
            {
                _cheekCtrl = cheekCtrl;
            }

            //======================================================================
            // メソッド
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// 更新
            /// </summary>
            /// ----------------------------------------
            public override void Update()
            {
                if (_rootInfo.CurrentCount < _keyInfoList.Count)
                {
                    _tempKeyInfo = _keyInfoList[_rootInfo.CurrentCount];
                }
                else
                {
                    _tempKeyInfo = KeyInfo.Default;
                    _keyInfoList.Add(_tempKeyInfo);
                }

                _tempKeyInfo.Time = _rootInfo.ElapsedTime;

                if (_cheekCtrl.IsCheekVisible)
                {
                    _tempKeyInfo.LabelValueDict[VISIBLE_KEY] = 1;
                }
                else
                {
                    _tempKeyInfo.LabelValueDict[VISIBLE_KEY] = 0;
                }

                _tempKeyInfo.LabelValueDict[INDEX_KEY] = _cheekCtrl.CurrentCheekIndex;

                _keyInfoList[_rootInfo.CurrentCount] = _tempKeyInfo;
            }
        }

        /// ******************************************************************************************
        /// <summary>
        /// 瞳情報クラス
        /// </summary>
        /// ******************************************************************************************
        [System.Serializable]
        public class EyehiKeyInfo : BaseKeyInfo
        {
            //======================================================================
            // 定数
            //======================================================================

            const string TEARFUL_KEY = "tearful";

            //======================================================================
            // 変数
            //======================================================================

            private ModelController _modelController;

            //======================================================================
            // コンストラクタ
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// デフォルト
            /// </summary>
            /// ----------------------------------------
            public EyehiKeyInfo(
                RootInfo rootInfo,
                string faceGroupTypeLabel,
                ModelController modelController
            ) : base(rootInfo, faceGroupTypeLabel)
            {
                _modelController = modelController;
            }

            //======================================================================
            // メソッド
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// 更新
            /// </summary>
            /// ----------------------------------------
            public override void Update()
            {
                if (_rootInfo.CurrentCount < _keyInfoList.Count)
                {
                    _tempKeyInfo = _keyInfoList[_rootInfo.CurrentCount];
                }
                else
                {
                    _tempKeyInfo = KeyInfo.Default;
                    _keyInfoList.Add(_tempKeyInfo);
                }

                _tempKeyInfo.Time = _rootInfo.ElapsedTime;

                if (_modelController.IsVisibleTear())
                {
                    _tempKeyInfo.LabelValueDict[TEARFUL_KEY] = 1;
                }
                else
                {
                    _tempKeyInfo.LabelValueDict[TEARFUL_KEY] = 0;
                }

                foreach (EyeHighlightController.HighlightAnimationId Value in Enum.GetValues(typeof(EyeHighlightController.HighlightAnimationId)))
                {
                    string name = Enum.GetName(typeof(EyeHighlightController.HighlightAnimationId), Value);

                    if (_modelController.IsPlayEyeHighlightAnimation(Value))
                    {
                        _tempKeyInfo.LabelValueDict[name] = 1;
                    }
                    else
                    {
                        _tempKeyInfo.LabelValueDict[name] = 0;
                    }
                }

                _keyInfoList[_rootInfo.CurrentCount] = _tempKeyInfo;
            }
        }

        /// ******************************************************************************************
        /// <summary>
        /// キー情報クラス
        /// </summary>
        /// ******************************************************************************************
        public struct KeyInfo
        {
            //======================================================================
            // 変数
            //======================================================================

            public float Time;
            public Dictionary<string, float> LabelValueDict;

            //======================================================================
            // プロパティ
            //======================================================================

            public static KeyInfo Default
            {
                get
                {
                    KeyInfo defaultValue = new KeyInfo();

                    defaultValue.Time = -1;
                    defaultValue.LabelValueDict = new Dictionary<string, float>();

                    return defaultValue;
                }
            }

            //======================================================================
            // メソッド
            //======================================================================

            /// ----------------------------------------
            /// <summary>
            /// データを出力
            /// </summary>
            /// ----------------------------------------
            public void ExportData(XmlElement parentElement)
            {
                XmlElement element = parentElement.OwnerDocument.CreateElement("KeyInfo");

                foreach (KeyValuePair<string, float> kvp in LabelValueDict)
                {
                    element.SetAttribute(kvp.Key, kvp.Value.ToString());
                }

                parentElement.AppendChild(element);
            }
        }
    }
}
#endif // UNITY_EDITOR