#if CYG_DEBUG
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    public static class DebugUIPartsDialogSelectorDressData
    {
        /// <summary>
        /// データクラス定義
        /// </summary>
        private class DataModel : DebugUIPartsDialogSelector.IData
        {
            public MasterDressData.DressData MasterData;
            public string Name => MasterData.Name;
            public Texture2D Texture => ResourceManager.LoadOnView<Texture2D>(ResourcePath.GetDressIconPath(MasterData.Id, 0));
        }

        /// <summary>
        /// キャラ選択リストのダイアログを表示
        /// </summary>
        public static void PushDialog(List<MasterDressData.DressData> dataList, int firstIndex, Action<MasterDressData.DressData> onSelectItem)
        {
            var charaDataList = dataList.Select(x => new DataModel{ MasterData = x }).ToList();
            DebugUIPartsDialogSelector.PushDialog(charaDataList, firstIndex, iData => onSelectItem(iData.MasterData));
        }
    }
}

#endif