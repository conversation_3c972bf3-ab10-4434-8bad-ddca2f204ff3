#if UNITY_EDITOR
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// キャンペーンカットインのエディタ
    /// </summary>
    [AddComponentMenu("")]
    [DisallowMultipleComponent]
    public class CampaignCutInTimelineEditor : CutInTimelineEditor
    {
        #region 定数
        // ファイル名の接頭辞
        private const string FILE_NAME_PREFIX = "campaign_walking_";

        // カットデータのファイル名パターン
        private const string FILE_NAME_PATTERN = "^" + FILE_NAME_PREFIX + @"\d{3}_\d{2}$";

        /// <summary> お料理編のお食事カット </summary>
        private const string TRAINING_FILE_NAME_PREFIX = "campaign_training_";
        /// <summary> お食事カットタイプのファイル名パターン </summary>
        private const string TRAINING_FILE_NAME_PATTERN = "^" + TRAINING_FILE_NAME_PREFIX + @"\d{3}_\d{2}_\d{4}$";

        #endregion 定数

        #region 変数
        // ヘルパークラス
        private CampaignCutInHelper _cutInHelper;

        // 起動用の情報
        private CampaignCutInHelper.Context _context;

        // 起動時設定された性格名配列
        private string[] _personalityStrArray = null;

        public bool IsCampaignTrainingCutt => _cutInHelper is CampaignTrainingCutInHelper;

        #endregion 変数

        #region 初期化
        protected override void InitializeTimeline()
        {
            base.InitializeTimeline();

            if (_cutInHelper != null)
            {
                _cutInHelper.CleanupPlaying();
                _cutInHelper = null;
            }

            SetCampaignCutInHelper(_context);
            _cutInHelper.InitializeEditor(_timelineController, _context);
        }

        /// <summary>
        /// カット種類によってヘルパークラスを設定
        /// </summary>
        /// <param name="context"></param>
        private void SetCampaignCutInHelper(CampaignCutInHelper.Context context)
        {
            //お食事用カットはOptionIDを利用する
            if (context.OptionId >= 0)
            {
                _cutInHelper = new CampaignTrainingCutInHelper();
            }
            else
            {
                const int VALENTINE_CUT_ID = 4;
                if (context.Id == VALENTINE_CUT_ID)
                {
                    _cutInHelper = new ValentineCutinHelper();
                }
                else
                {
                    _cutInHelper = new CampaignCutInHelper();
                }
            }
        }
        #endregion 初期化

        #region 破棄
        protected override void OnDestroy()
        {
            base.OnDestroy();

            if (_cutInHelper == null) return;

            // ヘルパークラスを破棄する
            _cutInHelper.CleanupPlaying();
            _cutInHelper = null;
        }
        #endregion 破棄

        #region ロード処理
        /// <summary>
        /// タイムラインLoadボタン時の処理：派生先でカスタムしたい処理を記載
        /// </summary>
        public override void OnClickLoadTimeline(string assetPath)
        {
            OnClickTimelineCommon(assetPath);
        }

        /// <summary>
        /// タイムラインSelectボタン時の処理：派生先でカスタムしたい処理を記載
        /// </summary>
        public override void OnClickSelectTimeline(string assetPath)
        {
            OnClickTimelineCommon(assetPath);
        }

        /// <summary>
        /// ロード時の共通処理
        /// </summary>
        /// <remarks>ファイル名からContextのパラメータを作る</remarks>
        private void OnClickTimelineCommon(string assetPath)
        {
            // ファイル名がパターンに一致するかチェック
            if (!CheckFileFormat(assetPath, out var fileName))
            {
                EditorUtility.DisplayDialog(
                    "不正なファイル名",
                    $"ファイル名:\n{fileName}\n\n正しいファイル名:\ncampaign_walking_xxx_yy",
                    "OK");
                return;
            }

            // ファイル名からIDを抽出
            GetIdFromFileName(fileName, out int id, out int subId, out int optionId);

            if (optionId == -1)
            {

                // IDが一致するマスターデータを探す
                var data = MasterDataManager.Instance.masterCampaignWalkingLocation.dictionary.Values
                    .FirstOrDefault(d => d.CutId == id && d.CutSubId == subId);
                if (data == null)
                {
                    // 見つからなかった
                    EditorUtility.DisplayDialog(
                        "CSVエラー",
                        $"campaign_walking_location.csvに\nCutId={id}, CutSubId={subId}\nのデータがない",
                        "OK");
                    return;
                }
                // 見つかったマスターデータからContextを作成
                _context = new CampaignCutInHelper.Context(data, new int[] { }, MasterCampaignData.INVALID_CAMPAIGN_ID);
            }
            else
            {
                //お食事用データの作成
                _context = new CampaignCutInHelper.Context(id, subId, optionId, new int[] { });
            }
        }

        /// <summary>
        /// ファイル名が正しいか確認する
        /// </summary>
        public static bool CheckFileFormat(string assetPath, out string fileName)
        {
            fileName = Path.GetFileNameWithoutExtension(assetPath);
            var m = Regex.Match(fileName, FILE_NAME_PATTERN);
            var m_training = Regex.Match(fileName, TRAINING_FILE_NAME_PATTERN);
            return m.Success || m_training.Success;
        }

        /// <summary>
        /// ファイル名からカットIDを取得する
        /// </summary>
        public static void GetIdFromFileName(string fileName, out int id, out int subId, out int optionId)
        {
            if (fileName.Contains(TRAINING_FILE_NAME_PREFIX))
            {
                //お食事カットはOptionIdも利用
                string[] strArray = fileName.Replace(TRAINING_FILE_NAME_PREFIX, "").Split('_');
                id = int.Parse(strArray[0]);
                subId = int.Parse(strArray[1]);
                optionId = int.Parse(strArray[2]);
            }
            else
            {
                string[] strArray = fileName.Replace(FILE_NAME_PREFIX, "").Split('_');
                id = int.Parse(strArray[0]);
                subId = int.Parse(strArray[1]);
                //ウマさんぽとバレンタイン系のカットは「optionId」を利用しない
                optionId = -1;
            }
        }
        #endregion ロード処理

        /// <summary>
        /// Editor上で参照するカットに適用可能のアタッチキャラの性格タイプを取得
        /// </summary>
        /// <returns></returns>
        public string[] GetTargetCharaPersonalityNameArray()
        {
            if (IsCampaignTrainingCutt)
            {
                //育成お食事カットで参照すべきキャラ性格タイプ
                if (_context.OptionId == 0)
                {
                    //最初の13人表示カット
                    if (_context.Id == 0)
                    {
                        _personalityStrArray = new string[]
                        {
                            "通常",
                            "お淑やか",
                        };
                        return _personalityStrArray;
                    }

                }
                else
                {
                    //食べるカット
                    var cutt_id = $"{_context.Id:D3}_{_context.SubId:D2}_{_context.OptionId:D4}";
                    var data = MasterDataManager.Instance.masterSingleModeCookDishCutt.GetListWithCuttIdOrderByIdAsc(cutt_id).FirstOrDefault();
                    if (data != null)
                    {
                        if (data.MaterialGroupId == SingleModeScenarioCookDefine.COOK_CUTT_SPICY_MATERIAL_GROUP_ID_401
                            || data.MaterialGroupId == SingleModeScenarioCookDefine.COOK_CUTT_SPICY_MATERIAL_GROUP_ID_402)
                        {
                            //辛い料理時の表示項目一覧を出す
                            _personalityStrArray = new string[]
                            {
                                "普通＆激辛好き",
                                "おしとやか＆激辛好き",
                                "クール＆激辛好き",
                                "普通＆激辛苦手",
                                "おしとやか＆激辛苦手",
                                "クール＆激辛苦手",
                            };

                            return _personalityStrArray;
                        }

                    }
                }


                //マスターデータが見つかっていない場合デフォルトの表示をする
                _personalityStrArray = new string[]
                {
                    "通常",
                    "お淑やか",
                    "クール",
                };

                return _personalityStrArray;
            }
            else
            {
                _personalityStrArray = null;
                return null;
            }
        }

        /// <summary>
        /// Editor上で選択した性格名からintに変換
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public int GetTargetCharaPersonality(in string personalityName)
        {
            if (IsCampaignTrainingCutt)
            {
                int personality = 0;
                foreach (var personalityStr in _personalityStrArray)
                {
                    personality++;
                    if (personalityStr.Equals(personalityName))
                    {
                        break;
                    }
                }

                return personality;
            }

            return 0;
        }

        /// <summary>
        /// Editor上でPropに内包するエフェクトを制御する可能かの指定
        /// </summary>
        /// <returns></returns>
        public bool IsUsePropChildEffectControl()
        {
            return IsCampaignTrainingCutt;
        }
    }
}
#endif
