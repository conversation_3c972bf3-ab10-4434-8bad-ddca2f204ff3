#if CYG_DEBUG
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Gallop
{
    // タイトル ～ ゲームへの移行中にダウンロード必須なリソースの一覧を制御
    public static class AddGroupDownloadWithRealFanfare
    {
        public static bool CheckAssignDownloadWithRealFanfare(string assetBundleName)
        {
            // ファイル名指定に合致
            if (AssetBundleDownloadUnit.Contains(assetBundleName))
            {
                return true;
            }

            // ディレクトリ指定に合致
            var directoryArray = DownloadTargetDirectory;
            for (int i = directoryArray.Length - 1; i >= 0; --i)
            {
                if (assetBundleName.StartsWith(directoryArray[i], StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            // 正規表現指定に合致
            var regexArray = RegexArray;
            for (int i = regexArray.Length - 1; i >= 0; --i)
            {
                if (Regex.IsMatch(assetBundleName, regexArray[i]))
                {
                    return true;
                }
            }

            return false;
        }
        
        // パス直接指定
        public static readonly HashSet<string> AssetBundleDownloadUnit = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        // ディレクトリ指定、このパス以下のものは全て対象とする   
        public static string[] DownloadTargetDirectory = { };

        // 正規表現指定
        private static readonly string[] RegexArray = new string[]
        {
            $"{AssetBundleGroupDownloadLogin.REGEX_BGM_ROOT}(?!..*(_race_ls_)).*$".ToLower(),  // sound/b以下の、_race_ls_(レース2曲目)以外の曲
        };
    }
}

#endif
