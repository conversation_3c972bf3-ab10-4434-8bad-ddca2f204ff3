using UnityEngine;

#if CYG_DEBUG

namespace Gallop
{

    /// <summary>
    /// レース直起動シーン
    /// </summary>
    public class StoryRaceEditorScene : DirectSceneBase
    {
        public override bool IsNeedLogin() { return false; }
    }

    /// <summary>
    /// レース直起動シーンコントローラ
    /// </summary>
    public class StoryRaceEditorSceneController : SceneControllerBase<StoryRaceEditorScene>
    {
        #region class

        public class RaceContext
        {
            public string serializeAssetFilename;
        }

        #endregion

        public static RaceContext raceContext;  //StoryRaceEditorWindowから直接渡せないのでこのクラスを経由する
        public static string RaceAssetFileName { get; private set; } = string.Empty;
        // Replayデータで設定されている季節
        public static GameDefine.BgSeason ReplaySeason { get; private set; } = GameDefine.BgSeason.None;

        public override void UpdateScene()
        {
            if (raceContext != null)
            {
                GoToStoryRace(raceContext.serializeAssetFilename);
                raceContext = null;
            }
        }

        public static void GoToStoryRace(string raceAssetFileName)
        {
            SetupStoryRace(raceAssetFileName, true);
            RaceInitializer.GoToRace();
        }

        public static void SetupStoryRace(string raceAssetFileName, bool isStoryRaceEditor)
        {
            RaceDebugger.IsDebugSimulationMode = false;
            RaceDebugger.IsStoryRaceEditor = isStoryRaceEditor;  //RaceInitializer.LoadRaceDataより前に設定する必要がある

            var path = ResourcePath.GetStoryRacePathFromFileName(raceAssetFileName);
            var serializedParam = ResourceManager.LoadOnView<RaceSerializedParam>(path);
            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(serializedParam, RaceDefine.RaceType.Story);
            if (ResourcePath.TryGetStoryRaceIdFromFileName(raceAssetFileName, out var storyRaceId))
            {
                RaceAssetFileName = string.Empty;
            }
            else
            {
                // Replayデータのファイル名からストーリーレースIDの取得に失敗した場合、
                // Replayデータのファイル名からPerformanceデータのファイル名を取得するため保持しておく。
                RaceAssetFileName = raceAssetFileName;
            }
            TempData.Instance.EpisodeData.DebugStoryRaceId = storyRaceId;
            RaceInitializer.CreateRaceInfo(new RaceInitializer.LoadRaceInfo(ref buildParam));

            // Replayデータ（RaceSerializedParam）で設定されている季節を保持しておく。
            ReplaySeason = serializedParam.season;
        }
    }
}

#endif // CYG_DEBUG //
