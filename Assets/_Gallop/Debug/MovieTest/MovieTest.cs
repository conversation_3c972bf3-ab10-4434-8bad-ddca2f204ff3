#if CYG_DEBUG
using System.Collections;
using UnityEngine;
using Cute.Cri;

namespace Gallop
{
    /// <summary>
    /// ムービー再生
    /// </summary>
    public class MovieTest : DirectSceneBase
    {
        public ButtonCommon Play = null;
        public GameObject Target = null;
        public UnityEngine.UI.Slider Slider = null;
        public TextCommon ScaleText = null;
        public TextCommon Path = null;
    }

    public class MovieTestSceneController : SceneControllerBase<MovieTest>
    {
        private MoviePlayerHandle _handle;

        public override IEnumerator InitializeScene()
        {
            GetScene().Play.SetOnClick(() => Play(GetScene().Path.text));
            yield return base.InitializeScene();
        }

        public override void UpdateScene()
        {
            var root = _scene.Target.transform as RectTransform;
            var scale = Mathf.Lerp(0, 2, _scene.Slider.value);
            root.transform.localScale = scale * Math.VECTOR3_ONE;
            _scene.ScaleText.text = scale.ToString();
            base.UpdateScene();
        }

        private void Play(string path)
        {
            var dlPath = path.EndsWith(MovieManager.USM_EXTENSION) ? path : path + MovieManager.USM_EXTENSION;
            dlPath = dlPath.StartsWith(ResourcePath.MovieFolderPath) ? dlPath : ResourcePath.MovieFolderPath + dlPath;
            DownloadManager.Instance.Download(dlPath, () => PlayInternal(path));
        }

        private void PlayInternal(string path)
        {
            MovieManager.Instance.Clear(_handle);

            _handle = MovieManager.Instance.CreateMoviePlayer(MoviePlayerType.ForUI, GetScene().Target);
            MovieManager.Instance.Load(_handle, path, () => MovieManager.Instance.Play(_handle));
        }
    }
}
#endif
