#if UNITY_EDITOR && CYG_DEBUG
using UnityEngine;

namespace Gallop
{
    /// <summary>
    /// [Serializable]メタがついてるクラスであるため注意
    /// ここで追加されたメンバーは[NonSerializeabled]を付けましょう
    /// </summary>
    public partial class CySpringCollisionData
    {
        public void BindEditor(float legacyScale)
        {
            if (_component != null)
            {
                _component.BindEditor(this, legacyScale);
            }
        }

        // データの更新をコンポーネントに反映
        public void ApplyData()
        {
            if (_component == null)
                return;

            CySpringCollisionComponent comp = _component;
            if (comp == null)
            {
                return;
            }
            comp.CollisionType = _type;
            comp.IsInner = _isInner;
            comp.Radius = _radius;
            comp.Offset = _offset;
            comp.Offset2 = _offset2;
        }

        [System.NonSerialized]
        private CySpringCollisionComponent _component;

        // UI表示用
        [System.NonSerialized]
        private bool _fold;

        public CollisionType ColType
        {
            get { return _type; }
            set { _type = value; }
        }
        public bool IsInner
        {
            get { return _isInner; }
            set { _isInner = value; }
        }

        public string TargetObjectName
        {
            get { return _targetObjectName; }
            set { _targetObjectName = value; }
        }
        public bool IsOtherTarget
        {
            get { return _isOtherTarget; }
            set { _isOtherTarget = value; }
        }

        public Vector3 Offset
        {
            get { return _offset; }
            set { _offset = value; }
        }

        public Vector3 Offset2
        {
            get { return _offset2; }
            set { _offset2 = value; }
        }

        public float Radius
        {
            get { return _radius; }
            set { _radius = value; }
        }

        public float Distance
        {
            get { return _distance; }
            set { _distance = value; }
        }

        public Vector3 Normal
        {
            get { return _normal; }
            set { _normal = value; }
        }

        public bool Fold
        {
            get { return _fold; }
            set { _fold = value; }
        }

        public bool SymmetryCopy { get; set; }

        /// <summary>
        /// データを複製
        /// </summary>
        /// <returns></returns>
        public CySpringCollisionData Duplicate()
        {
            var newInstance = new CySpringCollisionData(
                this.ColType,
                this.IsInner,
                this.CollisionName,
                this.TargetObjectName,
                this.IsOtherTarget,
                this.Offset,
                this.Offset2,
                this.Radius,
                this.Distance,
                this.Normal);
            return newInstance;
        }
        
        /// <summary>
        /// パラメータをコピー
        /// </summary>
        /// <param name="other"></param>
        public void Copy(CySpringCollisionData other)
        {
            this.CollisionName = other.CollisionName;
            this.TargetObjectName = other.TargetObjectName;
            this.ColType = other.ColType;
            this.IsInner = other.IsInner;
            this.Offset = other.Offset;
            this.Offset2 = other.Offset2;
            this.Radius = other.Radius;
            this.Distance = other.Distance;
            this.Normal = other.Normal;
        }

        public CySpringCollisionComponent Component
        {
            get { return _component; }
        }
    }
}
#endif