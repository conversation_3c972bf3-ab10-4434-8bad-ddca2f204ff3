#if UNITY_EDITOR && CYG_DEBUG

using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cute.AssetBundle;
using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using Gallop;

namespace Gallop
{
    /// <summary>
    /// 実況のログ閲覧
    /// </summary>
    public class JikkyouLogTool : EditorWindow
    {
        public static JikkyouLogTool _instance = null;
        
        private int _prevLogElementNum = 0;

        private bool _isAutoSelect = false; // 最新の実況予約を選択する

        private string _searchBaseIdsString = string.Empty; // BaseId検索カンマ区切り
        private int[] _searchBaseIds = null; // BaseId検索

        private bool _isVisibleEmptyElement = false; // 選択できるものがなかった実況を表示する
        private bool _isVisibleMarkElement = false; // 非実況記録を表示する

        private static readonly char[] IdSeparator = {',', '|', '\n', '\t'};

        private System.Text.StringBuilder _stringBuilder = null;
        private const int StringBuilderSize = 512;

        // UI系
        private GUIStyle _styleDefault = null;
        private GUIStyle _styleSelected = null;
        private GUIStyle _styleOK = null;
        private GUIStyle _styleNG = null;
        private GUIStyle _styleUnchecked = null;
        private GUIStyle _styleSearch = null;

        private string[] _modeNames = null; // モード表示名
        private string[] _triggerCommandNames = null; // トリガコマンド表示名
        private string[] _triggerOperatorNames = null; // トリガ演算子表示名
        private string[] _triggerHorseNames = null; // トリガ対象ウマ表示名

        private Vector2 _leftScroll = Vector2.zero;
        private Vector2 _raceInfoScroll = Vector2.zero;
        private Vector2 _reservedScroll = Vector2.zero;
        private Vector2 _baseListScroll = Vector2.zero;
        private Vector2 _messageGroupScroll = Vector2.zero;

        private bool _raceInfoFoldout = true;
        private bool _reservedFoldout = true;
        private bool _baseListFoldout = true;
        private bool _messageGroupFoldout = true;
        private bool _triggerDetailFoldout = true;

        private bool _isVisibleRaceInfoTurnover = true;
        private bool _isVisibleRaceInfoDistance = true;
        private bool _isVisibleRaceInfoLaneDistance = true;
        private bool _isVisibleRaceInfoLaneInOut = true;
        private bool _isVisibleRaceInfoSpeed = true;
        private bool _isVisibleRaceInfoFrontBlock = true;
        private bool _isVisibleRaceInfoInOutBlock = true;
        private bool _isVisibleRaceInfoTarget = true;

        private int _selectedLogElementIndex = -1;
        private int _selectedBaseBranchIndex = -1;

        private JikkyoVoice _jikkyoVoice = null;
        private float _jikkyouTime = 0.0f; // 停止中でもボイスを順次再生させるためにTime.realtimeSinceStartupを使用する

        private bool _isInitialized = false;

        /// <summary>
        /// シチュエーション表示名定義
        /// </summary>
        private static Dictionary<int, string> _situationNameDic = null;

        [MenuItem("Gallop/レース/JikkyouLogTool" + Gallop.EditorMenuShortcut.CtrlShiftJ, false, Gallop.EditorMenuPriority.RACE)]
        public static void ShowWindow()
        {
            var editor = GetWindow<JikkyouLogTool>();
            editor.Init();
            editor.Show();
        }

        public void OnDestroy()
        {
            _instance = null;
        }

        private void Init()
        {
            _prevLogElementNum = 0;

            _selectedLogElementIndex = -1;
            _selectedBaseBranchIndex = -1;

            LoadSettings();

            InitStyles();
            InitModeNames();
            InitTriggerParamNames();

            _instance = this;

            _isInitialized = true;
        }

        private void InitStyles()
        {
            _styleDefault = new GUIStyle();
            _styleDefault.normal.textColor = new Color(0.7f, 0.7f, 0.7f);

            _styleSelected = new GUIStyle();
            _styleSelected.normal.textColor = new Color(0.29f, 0.48f, 0.8f);

            _styleOK = new GUIStyle();
            _styleOK.normal.textColor = new Color(0.0f, 1.0f, 0.0f);

            _styleNG = new GUIStyle();
            _styleNG.normal.textColor = new Color(1.0f, 0.0f, 0.0f);

            _styleUnchecked = new GUIStyle();
            _styleUnchecked.normal.textColor = new Color(0.3f, 0.3f, 0.3f);

            _styleSearch = new GUIStyle();
            _styleSearch.normal.textColor = new Color(1.0f, 0.0f, 0.0f);
        }

        private void InitModeNames()
        {
            _modeNames = new string[EnumUtil.GetEnumElementCount<Jikkyo.Mode>()];
            for (int i = 0; i < _modeNames.Length; ++i)
            {
                _modeNames[i] = ((Jikkyo.Mode)i).ToString();
            }
        }

        private void InitTriggerParamNames()
        {
            _triggerCommandNames = new string[EnumUtil.GetEnumElementCount<JikkyoTrigger.Command>()];
            for (int i = 0; i < _triggerCommandNames.Length; ++i)
            {
                _triggerCommandNames[i] = ((JikkyoTrigger.Command) i).ToString();
            }

            _triggerOperatorNames = new string[EnumUtil.GetEnumElementCount<JikkyoTrigger.Inequality>()];
            for (int i = 0; i < _triggerOperatorNames.Length; ++i)
            {
                _triggerOperatorNames[i] = ((JikkyoTrigger.Inequality)i).ToString();
            }

            _triggerHorseNames = new string[EnumUtil.GetEnumElementCount<JikkyoTrigger.HorseType>()];
            for (int i = 0; i < _triggerHorseNames.Length; ++i)
            {
                _triggerHorseNames[i] = ((JikkyoTrigger.HorseType)i).ToString();
            }
        }

        public void Update()
        {
            if (!JikkyouLogger.IsEnable()) { return; }

            if (_jikkyoVoice != null)
            {
                var nowTime = Time.realtimeSinceStartup;
                var deltaTime = nowTime - _jikkyouTime;
                _jikkyouTime = nowTime;
                _jikkyoVoice.Update(deltaTime);
            }

            var log = JikkyouLogger.GetLog();
            var logElementsCount = log.GetLogElementsCount();
            if (logElementsCount != _prevLogElementNum)
            {
                if (_isAutoSelect)
                {
                    // 最新のものを選択
                    _selectedLogElementIndex = GetLastElementIndex(_selectedLogElementIndex, log);
                    SelectActualBaseBranch(log, _selectedLogElementIndex);
                }

                Repaint();
                _prevLogElementNum = logElementsCount;
            }
        }

        public void OnGUI()
        {
            if (GallopUtil.IsCompiling())
            {
                GUILayout.Label("Updating...");
                return;
            }

            if (!_isInitialized)
            {
                Init();
            }

            if (_situationNameDic == null)
            {
                LoadJikkyoLogToolSettings(out _situationNameDic);
            }

            BeginWindows();

            try
            {
                var log = JikkyouLogger.GetLog();

                using (var hScope = new EditorGUILayout.HorizontalScope())
                {
                    float leftWidth = this.position.width * 0.4f;

                    using (var vScope = new EditorGUILayout.VerticalScope(GUILayout.Width(leftWidth)))
                    {
                        // ボタンパネル
                        OnGUI_ControlPanel(log);

                        using (var scrollScope = new EditorGUILayout.ScrollViewScope(_leftScroll,
                            GUILayout.Width(leftWidth), GUILayout.ExpandHeight(true)))
                        {
                            _leftScroll = scrollScope.scrollPosition;

                            // 実況ログ
                            OnGUI_MainLogView(log);

                            Space();
                        }
                    }

                    using (var vScope = new EditorGUILayout.VerticalScope(GUILayout.ExpandWidth(true)))
                    {
                        Space();

                        // レース状況
                        _raceInfoFoldout = EditorUtil.ShurikenFoldOut(_raceInfoFoldout, "レース状況");
                        if (_raceInfoFoldout)
                        {
                            OnGUI_RaceInfoView(log);
                        }

                        // 予約リスト
                        _reservedFoldout = EditorUtil.ShurikenFoldOut(_reservedFoldout, "予約された実況・解説");
                        if (_reservedFoldout)
                        {
                            OnGUI_ReservedView(log);
                        }

                        // Base選択肢
                        _baseListFoldout = EditorUtil.ShurikenFoldOut(_baseListFoldout, "JikkyoBase選択肢");
                        if (_baseListFoldout)
                        {
                            OnGUI_BaseListView(log);
                        }

                        // MessageGroup選択肢
                        _messageGroupFoldout = EditorUtil.ShurikenFoldOut(_messageGroupFoldout, "MessageGroup選択肢");
                        if (_messageGroupFoldout)
                        {
                            OnGUI_MessageGroupView(log);
                        }

                        // トリガー詳細
                        _triggerDetailFoldout = EditorUtil.ShurikenFoldOut(_triggerDetailFoldout, "トリガー詳細");
                        if (_triggerDetailFoldout)
                        {
                            OnGUI_TriggerDetailView(log);
                        }

                        Space();
                    }

                    Space();
                }
            }
            catch (System.Exception e)
            {
                if (e is ExitGUIException)
                {
                    throw e; // 無害なので再throw
                }
                else
                {
                    Debug.LogError(e.ToString());
                    UnityEngine.Debug.Break();
                }
            }

            EndWindows();

            CheckKeyEvent();
        }

        /// <summary>
        /// 操作パネル：ボタンとかレース情報とか
        /// </summary>
        private void OnGUI_ControlPanel(JikkyouLogger.Log log)
        {
            GUILayout.BeginVertical(GUI.skin.box, GUILayout.ExpandWidth(true));

            if (GUILayout.Button("ログ取り開始"))
            {
                JikkyouLogger.SetEnable(true);
            }

            using (var check = new EditorGUI.ChangeCheckScope())
            {
                using (new EditorGUILayout.HorizontalScope())
                {
                    _isAutoSelect = GUILayout.Toggle(_isAutoSelect, "自動追従");

                }
                using (new EditorGUILayout.HorizontalScope())
                {
                    _isVisibleEmptyElement = GUILayout.Toggle(_isVisibleEmptyElement, "空実況表示");
                    _isVisibleMarkElement = GUILayout.Toggle(_isVisibleMarkElement, "非実況記録表示");
                }

                if (check.changed)
                {
                    SaveSettings();
                }
            }

            using (new EditorGUILayout.HorizontalScope())
            {
                GUILayout.Label("BaseId 検索", GUILayout.Width(80.0f));
                using (var check = new EditorGUI.ChangeCheckScope())
                {
                    const string SearchBaseIdsFieldName = "SearchBaseIdsField";
                    GUI.SetNextControlName(SearchBaseIdsFieldName);
                    _searchBaseIdsString = GUILayout.TextField(_searchBaseIdsString, "SearchTextField",
                        GUILayout.Width(160.0f));
                    GUI.enabled = !string.IsNullOrEmpty(_searchBaseIdsString);
                    if (GUILayout.Button("Clear", "SearchCancelButton"))
                    {
                        _searchBaseIdsString = string.Empty;
                        GUI.FocusControl(SearchBaseIdsFieldName);
                    }
                    GUI.enabled = true;

                    if (check.changed)
                    {
                        OnChange_SearchBaseIds();
                    }
                }
            }

            using (new EditorGUILayout.HorizontalScope())
            {
                if (GUILayout.Button("BaseCacheViewer"))
                {
                    JikkyouBaseCacheViewer.ShowWindow();
                }
                if (GUILayout.Button("VoicePlayer"))
                {
                    JikkyouVoicePlayer.ShowWindow();
                }
                if (GUILayout.Button("MasterChecker"))
                {
                    JikkyouMasterChecker.ShowWindow();
                }
                if (GUILayout.Button("Clear"))
                {
                    JikkyouLogger.Clear();
                    Init();
                }
            }

            GUILayout.EndVertical();
        }

        /// <summary>
        /// 入力されたBaseId文字列をint[]に展開
        /// </summary>
        private void OnChange_SearchBaseIds()
        {
            if (string.IsNullOrEmpty(_searchBaseIdsString))
            {
                _searchBaseIds = null;
                return;
            }

            var inputList = _searchBaseIdsString.Split(IdSeparator);
            var idList = new List<int>();
            int id;
            for (int i = 0; i < inputList.Length; ++i)
            {
                var str = inputList[i].Trim();
                if (!int.TryParse(str, out id)) { continue; }
                idList.Add(id);
            }
            _searchBaseIds = idList.ToArray();
        }

        /// <summary>
        /// 出力されたログをリスト表示
        /// </summary>
        private void OnGUI_MainLogView(JikkyouLogger.Log log)
        {
            if (log == null || log.logElements == null) { return; }

            const string MainLogViewFormat = "{0:0000}:{1}{2}{3}{4}{5}";
            const string IdFormat = "[B:{0},{1}:{2}] ";
            const string InterruptFormat = "[割込 {0}] ";
            const string TensionFormat = "[T{0}] ";

            using (var vScope = new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true)))
            {
                var elements = log.logElements;
                for (int i = elements.Count - 1; i >= 0; --i)
                {
                    var element = elements[i];
                    if (element.texts.Count == 0) { continue; }

                    string label;
                    if (element.isMarkElement)
                    {
                        // 非実況記録
                        if (!_isVisibleMarkElement) { continue; }

                        label = string.Format(MainLogViewFormat, element.distance, string.Empty, string.Empty, string.Empty, element.text);
                    }
                    else
                    {
                        // 選択できるものがなかった実況
                        if (!_isVisibleEmptyElement && IsEmptyElement(element)) { continue; }

                        // 実況
                        string type = element._jikkyoType == Jikkyo.JikkyoType.Jikkyou ? "M" : "C";
                        string id = string.Format(IdFormat, element.selectedBaseId, type, element.selectedGroupId);
                        string interrupt = (element.interruptCommand != JikkyoTrigger.Command.None)
                            ? string.Format(InterruptFormat, _triggerCommandNames[(int)element.interruptCommand])
                            : string.Empty;
                        string tension = string.Format(TensionFormat, (int)element.tension);
                        string select = GetSelectText(element);
                        label = string.Format(MainLogViewFormat, element.distance, id, interrupt, tension, select, element.text);
                    }
                    GUIStyle style = (i != _selectedLogElementIndex)
                        ? _styleDefault
                        : _styleSelected;
                    if (_searchBaseIds != null && _searchBaseIds.Contains(element.selectedBaseId))
                    {
                        style = _styleSearch;
                    }

                    GUILayout.BeginHorizontal();
                    if (GUILayout.Button("■", style, GUILayout.Width(12)))
                    {
                        PlayLogElement(log, i);
                    }

                    if (GUILayout.Button(label, style))
                    {
                        OnClickLogElement(log, i);
                    }
                    GUILayout.EndHorizontal();
                }

            }
        }

        /// <summary>
        /// 選定できるものがなかった実況か否か
        /// </summary>
        /// <param name="element"></param>
        /// <returns></returns>
        private bool IsEmptyElement(JikkyouLogger.LogElement element)
        {
            return element.texts.Count == 1 && string.IsNullOrEmpty(element.texts[0]);
        }

        /// <summary>
        /// 選択タイプ文字列選択
        /// </summary>
        private string GetSelectText(JikkyouLogger.LogElement element)
        {
            if (MasterDataManager.HasInstance() && MasterDataManager.Instance.masterRaceJikkyoBase != null)
            {
                // マスタある版
                const string ModeFormat = "[M:{0}_{1}] ";
                const string SituationFormat = "[S:{0}_{1}] ";

                var jikkyoBase = MasterDataManager.Instance.masterRaceJikkyoBase.Get(element.selectedBaseId);
                if (jikkyoBase == null) { return string.Empty; }

                switch (element.selectType)
                {
                    case JikkyouLogger.LogElement.SelectType.Mode:
                        return String.Format(ModeFormat,
                            GetModeName(jikkyoBase.Mode),
                            jikkyoBase.SubMode);
                    case JikkyouLogger.LogElement.SelectType.Situation:
                        return String.Format(SituationFormat,
                            GetSituationName(jikkyoBase.Situation),
                            jikkyoBase.SubSituation);
                }
            }
            else
            {
                // マスタない版
                const string ModeNoMaster = "[M] ";
                const string SituationNoMaster = "[S] ";

                switch (element.selectType)
                {
                    case JikkyouLogger.LogElement.SelectType.Mode:
                        return ModeNoMaster;
                    case JikkyouLogger.LogElement.SelectType.Situation:
                        return SituationNoMaster;
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// モード名の取得
        /// </summary>
        private string GetModeName(int mode)
        {
            if (_modeNames != null && mode < _modeNames.Length)
            {
                return _modeNames[mode];
            }
            return mode.ToString();
        }

        /// <summary>
        /// シチュエーション名の取得
        /// </summary>
        private string GetSituationName(int situation)
        {
            string name = string.Empty;
            if (_situationNameDic != null && _situationNameDic.TryGetValue(situation, out name))
            {
                return name;
            }
            return situation.ToString();
        }

        /// <summary>
        /// リストからLogElementを選択した
        /// </summary>
        private void OnClickLogElement(JikkyouLogger.Log log, int index)
        {
            _selectedLogElementIndex = index;
            SelectActualBaseBranch(log, index);

            _isAutoSelect = false; // 手動で選んだら自動追従を切る

            Repaint();
        }

        /// <summary>
        /// 実況に使用されたBaseBranchを選択状態にする
        /// </summary>
        private void SelectActualBaseBranch(JikkyouLogger.Log log, int index)
        {
            if (index < 0 || log.logElements.Count <= index) { return; }

            var selectedBaseId = log.logElements[index].selectedBaseId;
            var baseBraches = log.logElements[index].baseBranches;
            int selectedIndex = -1;
            for (int i = 0, count = baseBraches.Count; i < count; ++i)
            {
                if (baseBraches[i].baseId != selectedBaseId) { continue; }

                selectedIndex = i;
                break;
            }
            _selectedBaseBranchIndex = selectedIndex;
        }

        /// <summary>
        /// 実況を再生
        /// </summary>
        private void PlayLogElement(JikkyouLogger.Log log, int index)
        {
            if (index < 0 || log.logElements.Count <= index) { return; }

            var logElement = log.logElements[index];
            if (logElement == null) { return; }

            if (_jikkyoVoice == null)
            {
                var paramDefine = ResourceManager.LoadOnScene<RaceParamDefine>(ResourcePath.RaceParamDefinePath);
                _jikkyoVoice = new JikkyoVoice();

                _jikkyoVoice.Init(paramDefine.Jikkyo.CreateCrossTimeArray());
                _jikkyouTime = Time.realtimeSinceStartup;
            }

            // まとめて登録されている実況・解説を交互に再生
            var voiceCommands = logElement.voices;
            var jikkyouType = logElement._jikkyoType;
            var tension = logElement.tension;
            _jikkyoVoice.PlayVoiceSequential(voiceCommands[0], jikkyouType, tension);
            for (int i = 1, count = logElement.voices.Count; i < count; ++i)
            {
                jikkyouType = jikkyouType == Jikkyo.JikkyoType.Jikkyou
                    ? Jikkyo.JikkyoType.Comment
                    : Jikkyo.JikkyoType.Jikkyou;
                _jikkyoVoice.PlayVoiceSequential(voiceCommands[i], jikkyouType, tension, true);
            }
        }

        /// <summary>
        /// レース状況表示
        /// </summary>
        private void OnGUI_RaceInfoView(JikkyouLogger.Log log)
        {
            // 表示トグル
            using (var hScope = new EditorGUILayout.HorizontalScope(GUILayout.ExpandWidth(true)))
            {
                const string styleName = "button";

                using (var check = new EditorGUI.ChangeCheckScope())
                {
                    _isVisibleRaceInfoTurnover = GUILayout.Toggle(_isVisibleRaceInfoTurnover, "順位変動", styleName);
                    _isVisibleRaceInfoDistance = GUILayout.Toggle(_isVisibleRaceInfoDistance, "距離", styleName);
                    _isVisibleRaceInfoLaneDistance =
                        GUILayout.Toggle(_isVisibleRaceInfoLaneDistance, "レーン距離", styleName);
                    EditorGUI.BeginDisabledGroup(!_isVisibleRaceInfoLaneDistance);
                    _isVisibleRaceInfoLaneInOut = GUILayout.Toggle(_isVisibleRaceInfoLaneInOut, "前比較の内外", styleName);
                    EditorGUI.EndDisabledGroup();
                    _isVisibleRaceInfoSpeed = GUILayout.Toggle(_isVisibleRaceInfoSpeed, "速度", styleName);
                    _isVisibleRaceInfoFrontBlock = GUILayout.Toggle(_isVisibleRaceInfoFrontBlock, "前方BLK", styleName);
                    _isVisibleRaceInfoInOutBlock = GUILayout.Toggle(_isVisibleRaceInfoInOutBlock, "内外BLK", styleName);
                    _isVisibleRaceInfoTarget = GUILayout.Toggle(_isVisibleRaceInfoTarget, "対象タグ", styleName);

                    if (check.changed)
                    {
                        SaveSettings();
                    }
                }
            }

            if (log == null || log.logElements == null) { return; }
            if (_selectedLogElementIndex < 0 ||
                log.logElements.Count <= _selectedLogElementIndex) { return; }

            const float height = 240.0f;
            const string orderFormat = "{0:00}{1}: ";
            const string orderUpStr = "▲";
            const string orderDownStr = "▼";
            const string orderSameStr = "→";
            const string playerFormat = "[PLAYER] ";
            const string temptationFormat = "[掛かり] ";
            const string distanceFormat = "[{0:0000.0}] ";
            const string laneFormat = "[Lane{0:0.00}{1}] ";
            const string inStr = "内";
            const string outStr = "外";
            const string midStr = "中";
            const float inoutThreshold = 0.025f;
            const string speedFormat = "[{0:0.0}m/s] ";
            const string frontBlockFormat = "[前BLK {0:00}] ";
            const string inBlockFormat = "[内BLK {0:00}] ";
            const string outBlockFormat = "[外BLK {0:00}] ";
            const string horse1Format = "[%horse1] ";
            const string horse2Format = "[%horse2] ";
            const string curHorseFormat = "[CurHorse] ";
            const string pickupHorseFormat = "[PickupHorse] ";

            using (var scrollScope = new EditorGUILayout.ScrollViewScope(_raceInfoScroll, GUI.skin.box, GUILayout.ExpandWidth(true), GUILayout.MaxHeight(height)))
            {
                _raceInfoScroll = scrollScope.scrollPosition;

                var builder = GetStringBuilder();
                var logElement = log.logElements[_selectedLogElementIndex];
                var horseInfo = logElement.horseInfo;
                var playerHorseIndex = log.playerHorseIndex;
                for (int i = 0, horseNum = horseInfo.Length; i < horseNum; ++i)
                {
                    var info = horseInfo[i];
                    var index = info.horseIndex;

                    // 順位
                    var turnover = string.Empty;
                    // 順位変動
                    if (_isVisibleRaceInfoTurnover)
                    {
                        // 非実況記録が表示状態なら直前、非表示なら前の実況との変動を表示する
                        int prevIndex = (_isVisibleMarkElement)
                            ? Mathf.Max(0, _selectedLogElementIndex - 1)
                            : GetPrevElementIndex(_selectedLogElementIndex, log);
                        var prevLogElement = log.logElements[prevIndex];
                        int prevOrder = GetOrderInLogElement(index, prevLogElement);
                        if (prevOrder > i) { turnover = orderUpStr; }
                        else if (prevOrder < i) { turnover = orderDownStr; }
                        else { turnover = orderSameStr; }
                    }

                    builder.Append(string.Format(orderFormat, i + 1, turnover));

                    // 現在距離
                    if (_isVisibleRaceInfoDistance)
                    {
                        builder.Append(string.Format(distanceFormat, info.distance));
                    }

                    // レーン距離
                    if (_isVisibleRaceInfoLaneDistance)
                    {
                        var inout = string.Empty;
                        // 内外表示（先頭は中を表示）
                        if (_isVisibleRaceInfoLaneInOut)
                        {
                            var prevLaneDistance = horseInfo[Mathf.Max(0, i - 1)].laneDistance;
                            var laneDiff = info.laneDistance - prevLaneDistance;
                            if (laneDiff < -inoutThreshold) { inout = inStr; }
                            else if (laneDiff > inoutThreshold) { inout = outStr; }
                            else { inout = midStr; }
                        }

                        builder.Append(string.Format(laneFormat, info.laneDistance, inout));
                    }

                    // 現在速度
                    if (_isVisibleRaceInfoSpeed)
                    {
                        builder.Append(string.Format(speedFormat, info.speed));
                    }

                    // 名前
                    if (index < log.horseNames.Length)
                    {
                        builder.Append(log.horseNames[index]);
                    }
                    else
                    {
                        builder.AppendFormat("{0:00} ", index);
                    }

                    // プレイヤ
                    if (index == playerHorseIndex)
                    {
                        builder.Append(playerFormat);
                    }

                    // 掛かり
                    if (info.isTemptation)
                    {
                        builder.Append(temptationFormat);
                    }

                    // 前方ブロック
                    if (_isVisibleRaceInfoFrontBlock && info.frontBlockOrder >= 0)
                    {
                        builder.Append(string.Format(frontBlockFormat, info.frontBlockOrder + 1));
                    }

                    // 内外ブロック
                    if (_isVisibleRaceInfoInOutBlock)
                    {
                        if (info.inBlockOrder >= 0)
                        {
                            builder.Append(string.Format(inBlockFormat, info.inBlockOrder + 1));
                        }
                        if (info.outBlockOrder >= 0)
                        {
                            builder.Append(string.Format(outBlockFormat, info.outBlockOrder + 1));
                        }
                    }

                    // タグ
                    if (_isVisibleRaceInfoTarget)
                    {
                        if (index == logElement.horse1Index)
                        {
                            builder.Append(horse1Format);
                        }
                        if (index == logElement.horse2Index)
                        {
                            builder.Append(horse2Format);
                        }
                        if (index == logElement.curHorseIndex)
                        {
                            builder.Append(curHorseFormat);
                        }
                        if (index == logElement.pickupHorseIndex)
                        {
                            builder.Append(pickupHorseFormat);
                        }
                    }

                    if (i != horseNum - 1) { builder.AppendLine(); } // 最後以外改行
                }

                GUILayout.Label(builder.ToString(), _styleDefault);
            }
        }

        /// <summary>
        /// ログ要素中の順位取得
        /// </summary>
        private int GetOrderInLogElement(int horseIndex, JikkyouLogger.LogElement logElement)
        {
            var horseInfo = logElement.horseInfo;
            for (int i = 0, num = horseInfo.Length; i < num; ++i)
            {
                if (horseInfo[i].horseIndex != horseIndex) { continue; }

                return i;
            }

            return -1;
        }

        /// <summary>
        /// 予約済みの実況・解説メッセージ表示
        /// </summary>
        private void OnGUI_ReservedView(JikkyouLogger.Log log)
        {
            if (log == null || log.logElements == null) { return; }
            if (_selectedLogElementIndex < 0 ||
                log.logElements.Count <= _selectedLogElementIndex) { return; }

            const float height = 52.0f;

            using (new EditorGUILayout.HorizontalScope(GUI.skin.box))
            {
                var element = log.logElements[_selectedLogElementIndex];

                using (var scrollScope = new EditorGUILayout.ScrollViewScope(_reservedScroll, GUILayout.ExpandWidth(true), GUILayout.MaxHeight(height)))
                {
                    _reservedScroll = scrollScope.scrollPosition;

                    // Discardフラグで消した予約
                    var discardBaseIds = element.discardBaseIds;
                    for (int i = 0; i < discardBaseIds.Count; ++i)
                    {
                        GUILayout.Label(string.Format("Discard : {0}", discardBaseIds[i]), _styleDefault);
                    }

                    // 新たな予約
                    var texts = element.texts;
                    for (int i = 0; i < texts.Count; ++i)
                    {
                        GUILayout.Label(texts[i], _styleDefault);
                    }

                    // 解説禁止距離ではじかれた解説Id
                    if (element.distanceDisableCommentId > 0)
                    {
                        GUILayout.Label(string.Format("[解説禁止距離による無効解説{0}]", element.distanceDisableCommentId), _styleDefault);
                    }
                }

                // 予約した実況解説をクリップボードにコピー
                if (GUILayout.Button("Copy"))
                {
                    var buf = new System.Text.StringBuilder();
                    var texts = element.texts;
                    for (int i = 0; i < texts.Count; ++i)
                    {
                        buf.AppendLine(texts[i]);
                    }

                    EditorGUIUtility.systemCopyBuffer = buf.ToString();
                }
            }
        }

        /// <summary>
        /// 選択肢となったJikkyoBaseリスト表示
        /// </summary>
        private void OnGUI_BaseListView(JikkyouLogger.Log log)
        {
            if (log == null || log.logElements == null) { return; }
            if (_selectedLogElementIndex < 0 ||
                log.logElements.Count <= _selectedLogElementIndex) { return; }

            using (var scrollScope = new EditorGUILayout.ScrollViewScope(_baseListScroll, GUI.skin.box, GUILayout.ExpandWidth(true)))
            {
                _baseListScroll = scrollScope.scrollPosition;

                const string idFormat = "{0}:";
                const string separator = "|";
                const string labelFormat = ":{0}{1}{2}{3}{4}{5}[{6}{7}択] [優先{8}] [抽選{9}] {10}";
                const string modeFormat = "[M:{0}_{1}]";
                const string situationFormat = "[S:{0}_{1}]";
                const string ok = "○";
                const string ng = "×";
                const string uncheck = "--";
                const string reuseFormat = "[再{0}] ";
                const string repeatFormat = "[連続禁止] ";
                const string disableReentrySituationFormat = "[ｼﾁｭｴｰｼｮﾝ不可] ";
                const string notPlayableFormat = "[ﾒｯｾｰｼﾞ不可] ";
                const string unknownCurHorseFormat = "[CurHorse不定] ";
                const string message = "実況";
                const string comment = "解説";
                const float idWidth = 36.0f;
                const float checkWidth = 8.0f;
                const float separatorWidth = 4.0f;

                var element = log.logElements[_selectedLogElementIndex];
                var baseBranches = element.baseBranches;
                bool[] triggerExists = new bool[Jikkyo.TriggerNum];
                for (int i = 0; i < baseBranches.Count; ++i)
                {
                    var baseBranch = baseBranches[i];

                    string text = String.Empty;
                    bool isJikkyou = true;
                    int count = 0;
                    int priority = 0;
                    int per = 0;
                    var jikkyoBase = GetBaseMaster(baseBranch.baseId);
                    GetBaseMasterInfo(jikkyoBase, out text, out isJikkyou, out count, out priority, out per, ref triggerExists);
                    var messageType = isJikkyou ? message : comment;

                    using (var hScope = new EditorGUILayout.HorizontalScope())
                    {
                        var styleId = (baseBranch.baseId != log.logElements[_selectedLogElementIndex].selectedBaseId)
                            ? _styleDefault
                            : _styleSelected;
                        GUILayout.Label(string.Format(idFormat, baseBranch.baseId), styleId, GUILayout.Width(idWidth));

                        for (int ti = 0; ti < Jikkyo.TriggerNum; ++ti)
                        {
                            switch (baseBranch.triggerChecks[ti])
                            {
                                case JikkyouLogger.BaseBranch.Check.OK:
                                    GUILayout.Label(ok, (triggerExists[ti]) ? _styleOK : _styleUnchecked, GUILayout.Width(checkWidth));
                                    break;
                                case JikkyouLogger.BaseBranch.Check.NG:
                                    GUILayout.Label(ng, _styleNG, GUILayout.Width(checkWidth));
                                    break;
                                case JikkyouLogger.BaseBranch.Check.Unchecked:
                                    GUILayout.Label(uncheck, (triggerExists[ti]) ? _styleDefault : _styleUnchecked, GUILayout.Width(checkWidth));
                                    break;
                                default:
                                    Debug.LogWarning("unknown check type : " + baseBranch.triggerChecks[ti]);
                                    break;
                            }

                            if (ti == Jikkyo.TriggerNum - 1) { break; }
                            GUILayout.Label(separator, _styleUnchecked, GUILayout.Width(separatorWidth));
                        }

                        var select = string.Empty;
                        if (jikkyoBase != null)
                        {
                            switch (element.selectType)
                            {
                                case JikkyouLogger.LogElement.SelectType.Mode:
                                    select = string.Format(modeFormat, GetModeName(jikkyoBase.Mode), jikkyoBase.SubMode);
                                    break;
                                case JikkyouLogger.LogElement.SelectType.Situation:
                                    select = string.Format(situationFormat, GetSituationName(jikkyoBase.Situation), jikkyoBase.SubSituation);
                                    break;
                            }
                        }

                        var reuse = string.Empty;
                        if (baseBranch.reuseCheck == JikkyouLogger.BaseBranch.Check.OK)
                        {
                            reuse = string.Format(reuseFormat, ok);
                        }
                        else if (baseBranch.reuseCheck == JikkyouLogger.BaseBranch.Check.NG)
                        {
                            reuse = string.Format(reuseFormat, ng);
                        }

                        var repeat = baseBranch.disableBaseRepeat
                            ? repeatFormat
                            : string.Empty;

                        var reentrySituation = baseBranch.disableSituationReentry
                            ? disableReentrySituationFormat
                            : string.Empty;

                        var notPlayable = string.Empty;
                        if (baseBranch.playableCheck == JikkyouLogger.BaseBranch.Check.NG)
                        {
                            notPlayable = notPlayableFormat;
                        }

                        var unknownCurHorse = (baseBranch.unknownCurHorse)
                            ? unknownCurHorseFormat
                            : string.Empty;

                        var styleLabel = (i != _selectedBaseBranchIndex)
                            ? _styleDefault
                            : _styleSelected;
                        if (GUILayout.Button(string.Format(labelFormat, select, reuse, repeat, reentrySituation, notPlayable, unknownCurHorse, messageType, count, priority, per, text), styleLabel))
                        {
                            OnClickBaseBranch(i);
                        }
                    }
                }

            }
        }

        /// <summary>
        /// 手動でJikkyoBase選択肢が選択された
        /// </summary>
        /// <param name="index"></param>
        private void OnClickBaseBranch(int index)
        {
            _selectedBaseBranchIndex = index;
            Repaint();
        }

        /// <summary>
        /// JikkyoBaseマスタ取得
        /// </summary>
        /// <param name="baseId"></param>
        /// <returns></returns>
        private MasterRaceJikkyoBase.RaceJikkyoBase GetBaseMaster(int baseId)
        {
            if (!MasterDataManager.HasInstance() || MasterDataManager.Instance.masterRaceJikkyoBase == null) { return null; }

            return MasterDataManager.Instance.masterRaceJikkyoBase.Get(baseId);
        }

        /// <summary>
        /// JikkyoBase情報をマスタから取得
        /// </summary>
        private void GetBaseMasterInfo(MasterRaceJikkyoBase.RaceJikkyoBase jikkyoBase, out string text, out bool isJikkyou, out int count, out int priority, out int per, ref bool[] triggerExists)
        {
            text = String.Empty;
            isJikkyou = true;
            count = 0;
            priority = 0;
            per = 0;

            if (jikkyoBase == null) { return; }
            if (!MasterDataManager.HasInstance() ||
                MasterDataManager.Instance.masterRaceJikkyoMessage == null ||
                MasterDataManager.Instance.masterRaceJikkyoComment == null) { return; }

            if (jikkyoBase.MessageGroup > 0)
            {
                var messageList =
                    MasterDataManager.Instance.masterRaceJikkyoMessage.GetListWithGroupId(jikkyoBase.MessageGroup);
                if (messageList != null && messageList.Count > 0)
                {
                    isJikkyou = true;
                    text = Jikkyo.InvalidateNewLine(messageList[0].messageStr);
                    count = messageList.Count;
                }
            }
            else if (jikkyoBase.CommentGroup > 0)
            {
                var commentList =
                    MasterDataManager.Instance.masterRaceJikkyoComment.GetListWithGroupId(jikkyoBase.CommentGroup);
                if (commentList != null && commentList.Count > 0)
                {
                    isJikkyou = false;
                    text = Jikkyo.InvalidateNewLine(commentList[0].messageStr);
                    count = commentList.Count;
                }
            }
            else if (jikkyoBase.Per > 0)
            {
                Debug.LogError("invalid MessageGroup and CommentGroup : JikkyoBase.Id = " + jikkyoBase.Id);
            }

            priority = jikkyoBase.Priority;
            per = jikkyoBase.Per;

            triggerExists[0] = jikkyoBase.Trigger0 > 0;
            triggerExists[1] = jikkyoBase.Trigger1 > 0;
            triggerExists[2] = jikkyoBase.Trigger2 > 0;
            triggerExists[3] = jikkyoBase.Trigger3 > 0;
            triggerExists[4] = jikkyoBase.Trigger4 > 0;
            triggerExists[5] = jikkyoBase.Trigger5 > 0;
            triggerExists[6] = jikkyoBase.Trigger6 > 0;
            triggerExists[7] = jikkyoBase.Trigger7 > 0;
            triggerExists[8] = jikkyoBase.Trigger8 > 0;
            triggerExists[9] = jikkyoBase.Trigger9 > 0;
        }

        /// <summary>
        /// MessageGroup選択肢表示
        /// </summary>
        private void OnGUI_MessageGroupView(JikkyouLogger.Log log)
        {
            if (!MasterDataManager.HasInstance() ||
                MasterDataManager.Instance.masterRaceJikkyoBase == null ||
                MasterDataManager.Instance.masterRaceJikkyoMessage == null ||
                MasterDataManager.Instance.masterRaceJikkyoComment == null) { return; }

            if (log == null || log.logElements == null) { return; }
            if (_selectedLogElementIndex < 0 ||
                log.logElements.Count <= _selectedLogElementIndex) { return; }

            const float height = 80.0f;
            const string labelFormat = "{0:0000}:{1}{2}{3}";
            const string weightFormat = "[重み{0}] ";
            const string commentFormat = "[解説{0}] ";
            const string messageFormat = "[実況{0}] ";

            using (var scrollScope = new EditorGUILayout.ScrollViewScope(_messageGroupScroll, GUI.skin.box, GUILayout.ExpandWidth(true), GUILayout.MaxHeight(height)))
            {
                _messageGroupScroll = scrollScope.scrollPosition;

                if (_selectedBaseBranchIndex < 0 ||
                    log.logElements[_selectedLogElementIndex].baseBranches.Count <= _selectedBaseBranchIndex)
                { return; }

                var baseBranch = log.logElements[_selectedLogElementIndex].baseBranches[_selectedBaseBranchIndex];
                var jikkyoBase = MasterDataManager.Instance.masterRaceJikkyoBase.Get(baseBranch.baseId);
                if (jikkyoBase == null) { return; }

                if (jikkyoBase.MessageGroup > 0)
                {
                    var jikkyoMessageList =
                        MasterDataManager.Instance.masterRaceJikkyoMessage.GetListWithGroupId(jikkyoBase.MessageGroup);
                    if (jikkyoMessageList == null) { return; }

                    for (int i = 0; i < jikkyoMessageList.Count; ++i)
                    {
                        var jikkyoMessage = jikkyoMessageList[i];
                        if (jikkyoMessage == null) { continue; }

                        var weightStr = (jikkyoMessage.Per > 0)
                            ? string.Format(weightFormat, jikkyoMessage.Per)
                            : string.Empty;
                        var commentStr = (jikkyoMessage.CommentGroup > 0)
                            ? string.Format(commentFormat, jikkyoMessage.CommentGroup)
                            : string.Empty;
                        var label = string.Format(labelFormat,
                            jikkyoMessage.Id,
                            weightStr,
                            commentStr,
                            Jikkyo.InvalidateNewLine(jikkyoMessage.messageStr));
                        GUILayout.Label(label, _styleDefault);
                    }
                }
                else if (jikkyoBase.CommentGroup > 0)
                {
                    var jikkyoCommentList =
                        MasterDataManager.Instance.masterRaceJikkyoComment.GetListWithGroupId(jikkyoBase.CommentGroup);
                    if (jikkyoCommentList == null) { return; }

                    for (int i = 0; i < jikkyoCommentList.Count; ++i)
                    {
                        var jikkyoComment = jikkyoCommentList[i];
                        if (jikkyoComment == null) { continue; }

                        var weightStr = (jikkyoComment.Per > 0)
                            ? string.Format(weightFormat, jikkyoComment.Per)
                            : string.Empty;
                        var messageStr = (jikkyoComment.MessageGroup > 0)
                            ? string.Format(messageFormat, jikkyoComment.MessageGroup)
                            : string.Empty;
                        var label = string.Format(labelFormat,
                            jikkyoComment.Id,
                            weightStr,
                            messageStr,
                            Jikkyo.InvalidateNewLine(jikkyoComment.messageStr));
                        GUILayout.Label(label, _styleDefault);
                    }
                }
            }
        }

        /// <summary>
        /// 選択中のJikkyoBase項目のトリガー詳細表示
        /// </summary>
        private void OnGUI_TriggerDetailView(JikkyouLogger.Log log)
        {
            if (!MasterDataManager.HasInstance() ||
                MasterDataManager.Instance.masterRaceJikkyoBase == null ||
                MasterDataManager.Instance.masterRaceJikkyoTrigger == null) { return; }

            if (log == null || log.logElements == null) { return; }
            if (_selectedLogElementIndex < 0 ||
                log.logElements.Count <= _selectedLogElementIndex) { return; }

            const float height = 96.0f;
            const string checkFormat = "[{0}] ";
            const string idFormat = "id: {0}";
            const string commandFormat = "cmd: {0}";
            const string operatorFormat = "ope: {0}";
            const string horse1Format = "horse1: {0}";
            const string horse2Format = "horse2: {0}";
            const string param1Format = "param1: {0}";
            const string param2Format = "param2: {0}";

            using (var hScope = new EditorGUILayout.HorizontalScope(GUI.skin.box, GUILayout.ExpandWidth(true), GUILayout.Height(height)))
            {
                if (_selectedBaseBranchIndex < 0 ||
                    log.logElements[_selectedLogElementIndex].baseBranches.Count <= _selectedBaseBranchIndex)
                {
                    GUILayout.Label(string.Empty); // 一定サイズを確保するため
                    return;
                }

                var baseBranch = log.logElements[_selectedLogElementIndex].baseBranches[_selectedBaseBranchIndex];
                var jikkyoBase = MasterDataManager.Instance.masterRaceJikkyoBase.Get(baseBranch.baseId);
                if (jikkyoBase == null) { return; }

                for (int i = 0; i < Jikkyo.TriggerNum; ++i)
                {
                    using (var vScope = new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true)))
                    {
                        var triggerId = GetTriggerId(jikkyoBase, i);
                        if (triggerId == 0)
                        {
                            GUILayout.Label(string.Format(idFormat, triggerId), _styleDefault);
                            continue;
                        }

                        using (var hLabelScope = new EditorGUILayout.HorizontalScope())
                        {
                            var styleCheck = GetCheckGUIStyle(baseBranch.triggerChecks[i]);
                            var checkStr = string.Format(checkFormat, baseBranch.triggerChecks[i].ToString());
                            GUILayout.Label(checkStr, styleCheck, GUILayout.ExpandWidth(false));
                            GUILayout.Label(string.Format(idFormat, triggerId), _styleDefault, GUILayout.ExpandWidth(false));
                        }

                        var jikkyoTrigger = MasterDataManager.Instance.masterRaceJikkyoTrigger.Get(triggerId);
                        if (jikkyoTrigger == null) { continue; }

                        var commandStr = _triggerCommandNames[jikkyoTrigger.Command];
                        var operatorStr = _triggerOperatorNames[jikkyoTrigger.Inequality];
                        GUILayout.Label(string.Format(commandFormat, commandStr), _styleDefault);
                        GUILayout.Label(string.Format(operatorFormat, operatorStr), _styleDefault);

                        var horse1Str = _triggerHorseNames[jikkyoTrigger.Horse1];
                        var horse2Str = _triggerHorseNames[jikkyoTrigger.Horse2];
                        GUILayout.Label(string.Format(horse1Format, horse1Str), _styleDefault);
                        GUILayout.Label(string.Format(horse2Format, horse2Str), _styleDefault);

                        string param1Str;
                        string param2Str;
                        JikkyoToolUtil.GetTriggerParamText(jikkyoTrigger, out param1Str, out param2Str);
                        GUILayout.Label(string.Format(param1Format, param1Str), _styleDefault);
                        GUILayout.Label(string.Format(param2Format, param2Str), _styleDefault);
                    }
                }
            }

        }

        private GUIStyle GetCheckGUIStyle(JikkyouLogger.BaseBranch.Check check)
        {
            switch (check)
            {
                case JikkyouLogger.BaseBranch.Check.OK:
                    return _styleOK;
                case JikkyouLogger.BaseBranch.Check.NG:
                    return _styleNG;
            }
            return _styleDefault;
        }

        private int GetTriggerId(MasterRaceJikkyoBase.RaceJikkyoBase jikkyoBase, int index)
        {
            switch (index)
            {
                case 0:
                    return jikkyoBase.Trigger0;
                case 1:
                    return jikkyoBase.Trigger1;
                case 2:
                    return jikkyoBase.Trigger2;
                case 3:
                    return jikkyoBase.Trigger3;
                case 4:
                    return jikkyoBase.Trigger4;
                case 5:
                    return jikkyoBase.Trigger5;
                case 6:
                    return jikkyoBase.Trigger6;
                case 7:
                    return jikkyoBase.Trigger7;
                case 8:
                    return jikkyoBase.Trigger8;
                case 9:
                    return jikkyoBase.Trigger9;
            }
            return 0;
        }

        /// <summary>
        /// キー入力操作
        /// </summary>
        private void CheckKeyEvent()
        {
            var log = JikkyouLogger.GetLog();
            if (log == null) { return; }

            var logElementsCount = log.GetLogElementsCount();
            if (logElementsCount <= 1) { return; }

            if (Event.current != null && Event.current.type == EventType.KeyDown &&
                (Event.current.keyCode == KeyCode.UpArrow ||
                 Event.current.keyCode == KeyCode.DownArrow ||
                 Event.current.keyCode == KeyCode.PageUp ||
                 Event.current.keyCode == KeyCode.PageDown))
            {
                switch (Event.current.keyCode)
                {
                    case KeyCode.UpArrow:
                        _selectedLogElementIndex = GetNextElementIndex(_selectedLogElementIndex, log);
                        break;
                    case KeyCode.DownArrow:
                        _selectedLogElementIndex = GetPrevElementIndex(_selectedLogElementIndex, log);
                        break;
                    case KeyCode.PageUp:
                        _selectedLogElementIndex = GetLastElementIndex(_selectedLogElementIndex, log);
                        break;
                    case KeyCode.PageDown:
                        _selectedLogElementIndex = GetFirstElementIndex(_selectedLogElementIndex, log);
                        break;
                }

                SelectActualBaseBranch(log, _selectedLogElementIndex);
                Repaint();
            }
        }

        /// <summary>
        /// ひとつ前の実況のインデクスを取得
        /// </summary>
        private int GetPrevElementIndex(int selectedIndex, JikkyouLogger.Log log)
        {
            var logElements = log.logElements;
            for (int i = selectedIndex - 1; i >= 0; --i)
            {
                if (!_isVisibleEmptyElement && IsEmptyElement(logElements[i])) { continue; }
                if (!_isVisibleMarkElement && logElements[i].isMarkElement) { continue; }
                return i;
            }
            return selectedIndex;
        }

        /// <summary>
        /// ひとつ後の実況のインデクスを取得
        /// </summary>
        private int GetNextElementIndex(int selectedIndex, JikkyouLogger.Log log)
        {
            var logElements = log.logElements;
            for (int i = selectedIndex + 1, max = log.GetLogElementsCount(); i < max; ++i)
            {
                if (!_isVisibleEmptyElement && IsEmptyElement(logElements[i])) { continue; }
                if (!_isVisibleMarkElement && logElements[i].isMarkElement) { continue; }
                return i;
            }
            return selectedIndex;
        }

        /// <summary>
        /// 最初の実況のインデクスを取得
        /// </summary>
        private int GetFirstElementIndex(int selectedIndex, JikkyouLogger.Log log)
        {
            var logElements = log.logElements;
            for (int i = 0, max = log.GetLogElementsCount(); i < max; ++i)
            {
                if (!_isVisibleEmptyElement && IsEmptyElement(logElements[i])) { continue; }
                if (!_isVisibleMarkElement && logElements[i].isMarkElement) { continue; }
                return i;
            }
            return selectedIndex;
        }

        /// <summary>
        /// 最後の実況のインデクスを取得
        /// </summary>
        private int GetLastElementIndex(int selectedIndex, JikkyouLogger.Log log)
        {
            var logElements = log.logElements;
            for (int i = log.GetLogElementsCount() - 1; i >= 0; --i)
            {
                if (!_isVisibleEmptyElement && IsEmptyElement(logElements[i])) { continue; }
                if (!_isVisibleMarkElement && logElements[i].isMarkElement) { continue; }
                return i;
            }
            return selectedIndex;
        }

        /// <summary>
        /// 空白
        /// </summary>
        private void Space(float pixels = 8)
        {
            GUILayout.Space(pixels);
        }

        /// <summary>
        /// StringBuilder取得
        /// </summary>
        private System.Text.StringBuilder GetStringBuilder()
        {
            if (_stringBuilder == null)
            {
                _stringBuilder = new System.Text.StringBuilder(StringBuilderSize);
            }

            _stringBuilder.Length = 0;
            return _stringBuilder;
        }

        #region EditorPrefs
        /// <summary>
        /// 設定をまとめて読み込む
        /// </summary>
        private void LoadSettings()
        {
            _isAutoSelect = LoadSettingBool("_isAutoSelect");
            _isVisibleEmptyElement = LoadSettingBool("_isVisibleEmptyElement");
            _isVisibleMarkElement = LoadSettingBool("_isVisibleMarkElement");

            _isVisibleRaceInfoTurnover = LoadSettingBool("_isVisibleRaceInfoTurnover");
            _isVisibleRaceInfoDistance = LoadSettingBool("_isVisibleRaceInfoDistance");
            _isVisibleRaceInfoLaneDistance = LoadSettingBool("_isVisibleRaceInfoLaneDistance");
            _isVisibleRaceInfoLaneInOut = LoadSettingBool("_isVisibleRaceInfoLaneInOut");
            _isVisibleRaceInfoSpeed = LoadSettingBool("_isVisibleRaceInfoSpeed");
            _isVisibleRaceInfoFrontBlock = LoadSettingBool("_isVisibleRaceInfoFrontBlock");
            _isVisibleRaceInfoInOutBlock = LoadSettingBool("_isVisibleRaceInfoInOutBlock");
            _isVisibleRaceInfoTarget = LoadSettingBool("_isVisibleRaceInfoTarget");
        }

        /// <summary>
        /// 設定をまとめて保存する
        /// </summary>
        private void SaveSettings()
        {
            SaveSettingBool("_isAutoSelect", _isAutoSelect);
            SaveSettingBool("_isVisibleEmptyElement", _isVisibleEmptyElement);
            SaveSettingBool("_isVisibleMarkElement", _isVisibleMarkElement);

            SaveSettingBool("_isVisibleRaceInfoTurnover", _isVisibleRaceInfoTurnover);
            SaveSettingBool("_isVisibleRaceInfoDistance", _isVisibleRaceInfoDistance);
            SaveSettingBool("_isVisibleRaceInfoLaneDistance", _isVisibleRaceInfoLaneDistance);
            SaveSettingBool("_isVisibleRaceInfoLaneInOut", _isVisibleRaceInfoLaneInOut);
            SaveSettingBool("_isVisibleRaceInfoSpeed", _isVisibleRaceInfoSpeed);
            SaveSettingBool("_isVisibleRaceInfoFrontBlock", _isVisibleRaceInfoFrontBlock);
            SaveSettingBool("_isVisibleRaceInfoInOutBlock", _isVisibleRaceInfoInOutBlock);
            SaveSettingBool("_isVisibleRaceInfoTarget", _isVisibleRaceInfoTarget);
        }

        /// <summary>
        /// エディタ設定を読み込む
        /// </summary>
        private bool LoadSettingBool(string name)
        {
            return EditorPrefs.GetBool(GetPrefsName(name));
        }

        /// <summary>
        /// エディタ設定を保存する
        /// </summary>
        private void SaveSettingBool(string name, bool value)
        {
            EditorPrefs.SetBool(GetPrefsName(name), value);
        }

        /// <summary>
        /// 保存名取得
        /// </summary>
        private string GetPrefsName(string name)
        {
            const string EditorPerfsPrefix = "JikkyouLogTool_";
            return EditorPerfsPrefix + name;
        }

#endregion

#region 設定ファイル
        /// <summary>
        /// 設定ファイルをロードして設定取得
        /// </summary>
        /// <param name="situationNameDic"></param>
        public static void LoadJikkyoLogToolSettings(out Dictionary<int, string> situationNameDic)
        {
            const string ToolSettingsPath = "Assets/_Gallop/Editor/JikkyoLogTool/Resources/JikkyouLogToolSettings.asset";

            situationNameDic = new Dictionary<int, string>();

            AssetDatabase.Refresh();
            var settings = AssetDatabase.LoadAssetAtPath<JikkyouLogToolSettings>(ToolSettingsPath);
            foreach (var situationName in settings.situationNames)
            {
                if (situationNameDic.ContainsKey(situationName.id)) { continue; }
                situationNameDic.Add(situationName.id, situationName.name);
            }
        }
#endregion

    }
}
#endif // UNITY_EDITOR && CYG_DEBUG