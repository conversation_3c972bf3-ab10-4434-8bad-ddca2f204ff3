#if CYG_DEBUG
using System.Linq;
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// エディタ拡張：お問い合わせ用に各種レースモードのレースを復元する：チーム競技場。
    /// </summary>
    //-------------------------------------------------------------------
    public partial class EditorRestoreRaceInfoFromResponse : EditorWindow
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public class TeamStadiumStartResponseDummy
        {
            [System.Serializable]
            public class CommonResponse
            {
                // public int[] use_item_id_array;
                public TeamStadiumRaceStartParamsDummy[] race_start_params_array;
                public TeamStadiumRaceResultDummy[] race_result_array;
            }
            public CommonResponse data;
        }
        [System.Serializable]
        public class TeamStadiumRaceStartParamsDummy
        {
            public int round;
            public int race_instance_id;
            public int season;
            public int weather;
            public int ground_condition;
            public int random_seed;
            public RaceHorseDataDummy[] race_horse_data_array;
            public int self_evaluate;
            public int opponent_evaluate;
        }
        [System.Serializable]
        public class TeamStadiumRaceResultDummy
        {
            public string race_scenario;
        }

     
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>BigQueryからDLしたjsonをロードしたもの。</summary>
        private BigQueryJsonRoot _teamStadiumJsonRoot; 
        /// <summary>チーム競技場の１セット（５レース）。</summary>
        private TeamStadiumStartResponseDummy.CommonResponse _teamStadiumJsonByEntry; 
        /// <summary>現在選択中のチーム競技場のセット。_teamStadiumJsonRoot.Arrayのインデックス。</summary>
        private int _selectedTeamStadiumEntryIndex;

        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        #region TeamStadium
        /// <summary>
        /// チーム競技場１レース分の復元。
        /// </summary>
        private void CreateTeamStadiumRaceInfo(TeamStadiumRaceStartParamsDummy raceStartParam, string raceScenario)
        {
            // _teamStadiumViewerIdで指定されたユーザーのキャラを、エディタ実行している環境でのユーザーキャラとするため、viewer_idを設定。
            int scoreCalcTeamId = HorseData.TEAM_ID_NULL;
            var horseDataArray = raceStartParam.race_horse_data_array;
            if (_viewerId != VIEWER_ID_NULL)
            {
                ReplaceViewerId(horseDataArray, _viewerId, Certification.ViewerId);
                scoreCalcTeamId = horseDataArray.FirstOrDefault(x => x.viewer_id == Certification.ViewerId).team_id;
            }
            
            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                raceStartParam.race_instance_id,
                horseDataArray.Select(x => x.ToRaceHorseData()).ToArray(),
                raceStartParam.random_seed,
                RaceDefine.RaceType.TeamStadium,
                (GameDefine.BgSeason)raceStartParam.season,
                (RaceDefine.Weather)raceStartParam.weather,
                (RaceDefine.GroundCondition)raceStartParam.ground_condition,
                raceScenario,
                raceProgramId:0,
                opponentEvaluate:raceStartParam.opponent_evaluate,
                selfEvaluate:raceStartParam.self_evaluate,
                scoreCalcTeamId:scoreCalcTeamId,
                supportCardScoreBonus:0); // team_stadium/startに含まれていない。
            var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
            RaceDirectScene._ApplyLoadRaceInfo(loadRaceInfo);
        }

        private void OutputTeamStadiumRaceInfoJson(TeamStadiumRaceStartParamsDummy raceStartParam, string raceScenario)
        {
            CreateTeamStadiumRaceInfo(raceStartParam, raceScenario);
            RaceSimulatorTool.OutputLoadRaceInfoJson(false);
        }

        /// <summary>
        /// チーム競技場レース復元機能描画。
        /// </summary>
        private void DrawTeamStadium()
        {
            // ヘッダ。
            using(new EditorGUILayout.VerticalScope(GUI.skin.box))
            {
                _viewerId = EditorGUILayout.LongField("ViewerId", _viewerId);
                DrawGUILoadJson((json) => _teamStadiumJsonRoot = json);
            }
            DrawSeparator();

            // 全レースセット。
            using (new EditorGUILayout.VerticalScope())
            {
                if (_teamStadiumJsonRoot != null)
                {
                    for (int i = 0; i < _teamStadiumJsonRoot.Array.Length; i++)
                    {
                        // 選択中のレースセットは赤で表示する。
                        var col = _selectedTeamStadiumEntryIndex == i ? Color.red : Color.white;
                        
                        using (new EditorUtil.ScopeBGColor(col))
                        using (new EditorGUILayout.HorizontalScope())
                        {
                            var json = _teamStadiumJsonRoot.Array[i];
                            if (GUILayout.Button(json.time, GUILayout.Width(150)))
                            {
                                _teamStadiumJsonByEntry = JsonUtility.FromJson(json.body, typeof(TeamStadiumStartResponseDummy.CommonResponse)) as TeamStadiumStartResponseDummy.CommonResponse;
                                _selectedTeamStadiumEntryIndex = i;
                            }
                        }
                    }
                }
            }
            DrawSeparator();

            // 選択されたレースセット。
            using(new EditorGUILayout.VerticalScope())
            {
                if (_teamStadiumJsonByEntry != null)
                {
                    for (int i = 0; i < _teamStadiumJsonByEntry.race_start_params_array.Length; i++)
                    {
                        var raceStartParam = _teamStadiumJsonByEntry.race_start_params_array[i];
                        var raceScenario = _teamStadiumJsonByEntry.race_result_array[i].race_scenario;
                        var raceName = GetRaceName(raceStartParam.race_instance_id);

                        using(new EditorGUILayout.HorizontalScope())
                        {
                            // 選択されたレスポンスの情報からLoadRaceInfoを生成し、RaceSimulatorToolに反映する。
                            if (GUILayout.Button($"{i+1}レース目をRaceSimulatorToolに設定", GUILayout.Width(250)))
                            {
                                CreateTeamStadiumRaceInfo(raceStartParam, raceScenario);
                            }

                            if (GUILayout.Button($"json出力", GUILayout.Width(OUTPUT_JSON_WIDTH)))
                            {
                                OutputTeamStadiumRaceInfoJson(raceStartParam, raceScenario);
                            }
                        }
                        
                        EditorGUI.indentLevel++;
                        {
                            // レース名表示。
                            EditorGUILayout.LabelField(raceName);

                            EditorGUI.indentLevel++;
                            {
                                // ViewerIdのユーザーのキャラ表示。
                                if (_viewerId != VIEWER_ID_NULL)
                                {
                                    var charaNameStr = GenerateCharaNameStr(raceStartParam.race_horse_data_array, _viewerId);
                                    EditorGUILayout.LabelField(charaNameStr);
                                }
                            }
                            EditorGUI.indentLevel--;
                        }
                        EditorGUI.indentLevel--;
                    }
                }
            }
        }
        #endregion
    }
}

#endif
