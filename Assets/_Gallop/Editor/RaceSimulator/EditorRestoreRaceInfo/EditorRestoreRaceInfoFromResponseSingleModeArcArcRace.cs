#if CYG_DEBUG
using System.Linq;
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// エディタ拡張：お問い合わせ用に各種レースモードのレースを復元する：育成 凱旋門賞(ArcArcRace)編：代表交流戦
    /// </summary>
    //-------------------------------------------------------------------
    public partial class EditorRestoreRaceInfoFromResponse : EditorWindow
    {
        //---------------------------------------------------------------
        // 定義。
        //---------------------------------------------------------------
        public sealed class SingleModeArcArcRaceRaceStartResponseDummy : ResponseCommon
        {
            [System.Serializable]
            public class CommonResponse
            {
                public SingleRaceStartInfoDummy race_start_info;
                public string race_scenario;
            }
            public CommonResponse data;
        }
        
     
        //---------------------------------------------------------------
        // 変数。
        //---------------------------------------------------------------
        /// <summary>BigQueryからDLしたjsonをロードしたもの。</summary>
        private BigQueryJsonRoot _singleModeArcArcRaceJsonRoot;
        /// <summary>育成凱旋門賞(Arc)編　代表交流戦１レース。</summary>
        private SingleModeArcArcRaceRaceStartResponseDummy.CommonResponse _selectedSingleModeArcArcRaceResponse;
        /// <summary>現在選択中の育成凱旋門賞(Arc)編　代表交流戦レース。_singleModeArcArcRaceJsonRoot.Arrayのインデックス。</summary>
        private int _selectedSingleModeArcArcRaceRaceIndex = -1;
        /// <summary>スクロール値。</summary>
        private Vector2 _singleModeArcArcRaceScroll;

        
        //---------------------------------------------------------------
        // 関数。
        //---------------------------------------------------------------
        #region SingleMode
        private void OutputSingleModeArcArcRaceRaceInfoJson()
        {
            CreateSingleModeArcArcRaceRaceInfo(_selectedSingleModeArcArcRaceResponse);
            RaceSimulatorTool.OutputLoadRaceInfoJson(false);
        }
            
        private void CreateSingleModeArcArcRaceRaceInfo(SingleModeArcArcRaceRaceStartResponseDummy.CommonResponse res)
        {
            var masterDataManager = MasterDataManager.Instance;
            if (masterDataManager == null)
            {
                Debug.LogError("MasterDataManager is null!");
                return;
            }

            var masterSingleModeProgram = masterDataManager.masterSingleModeProgram.Get(res.race_start_info.program_id);
            if (masterSingleModeProgram == null)
            {
                return;
            }
            
            // LoadRaceInfo/RaceInfo生成。
            var horseDataArray = res.race_start_info.race_horse_data;
            if (_viewerId != VIEWER_ID_NULL)
            {
                ReplaceViewerId(horseDataArray, _viewerId, Certification.ViewerId);
            }

            var buildParam = new RaceInitializer.LoadRaceInfo.BuildParam(
                masterSingleModeProgram.RaceInstanceId,
                horseDataArray.Select(x => x.ToRaceHorseData()).ToArray(),
                res.race_start_info.random_seed,
                RaceDefine.RaceType.Single,
                RaceUtil.GetSeason(masterSingleModeProgram.RaceInstanceId),
                (RaceDefine.Weather)res.race_start_info.weather,
                (RaceDefine.GroundCondition)res.race_start_info.ground_condition,
                res.race_scenario
            );
            var loadRaceInfo = new RaceInitializer.LoadRaceInfo(ref buildParam);
            RaceDirectScene._ApplyLoadRaceInfo(loadRaceInfo);
        }


        /// <summary>
        /// 育成凱旋門賞(Arc)編　代表交流戦　復元機能描画。
        /// </summary>
        private void DrawSingleModeArcArcRace()
        {
            // ヘッダ。
            using(new EditorGUILayout.VerticalScope(GUI.skin.box))
            {
                _viewerId = EditorGUILayout.LongField("ViewerId", _viewerId);
                DrawGUILoadJson((json) => _singleModeArcArcRaceJsonRoot = json);
                DrawSeparator();
                DrawGUIFilter(false);
            }
            DrawSeparator();

            using (var scrollView = new EditorGUILayout.ScrollViewScope(_singleModeArcArcRaceScroll))
            using(new EditorGUILayout.VerticalScope())
            {
                _singleModeArcArcRaceScroll = scrollView.scrollPosition;

                if (_singleModeArcArcRaceJsonRoot != null)
                {
                    for (int i = 0; i < _singleModeArcArcRaceJsonRoot.Array.Length; i++)
                    {
                        // 選択中のレースは赤で表示する。
                        var col = _selectedSingleModeArcArcRaceRaceIndex == i ? Color.red : Color.white;
                        
                        using (new EditorUtil.ScopeBGColor(col))
                        using (new EditorGUILayout.HorizontalScope())
                        {
                            var jsonBody = _singleModeArcArcRaceJsonRoot.Array[i].body;
                            var jsonTime = _singleModeArcArcRaceJsonRoot.Array[i].time;
                            
                            // jsonからレスポンスオブジェクトをデシリアライズ。
                            var res = JsonUtility.FromJson(jsonBody, typeof(SingleModeArcArcRaceRaceStartResponseDummy.CommonResponse)) as SingleModeArcArcRaceRaceStartResponseDummy.CommonResponse;
                            if (res == null || res.race_start_info == null)
                            {
                                continue;
                            }
                            var raceName = GetRaceNameByProgramId(res.race_start_info.program_id);

                            if (!FilterRaceName(raceName))
                            {
                                continue;
                            }
                            if (!FilterCharaName(res.race_start_info.race_horse_data))
                            {
                                continue;
                            }

                            using (new EditorGUILayout.HorizontalScope())
                            {
                                // 選択ボタン押したらRaceSimulatorToolに読み込ませる。
                                if (GUILayout.Button((i+1).ToString(), GUILayout.Width(50)))
                                {
                                    SelectSingleModeArcArcRaceRaceInfo(res, i);
                                    CreateSingleModeArcArcRaceRaceInfo(_selectedSingleModeArcArcRaceResponse);
                                }
                            
                                if (GUILayout.Button($"json出力", GUILayout.Width(OUTPUT_JSON_WIDTH)))
                                {
                                    SelectSingleModeArcArcRaceRaceInfo(res, i);
                                    OutputSingleModeArcArcRaceRaceInfoJson();
                                }
                            }
                            
                            // レスポンスのタイムスタンプ。
                            EditorGUILayout.LabelField(jsonTime);

                            // レース名。
                            EditorGUILayout.LabelField(raceName);
                            
                            // ViewerIdのユーザーのキャラ表示。
                            if (_viewerId != VIEWER_ID_NULL)
                            {
                                var charaNameStr = GenerateCharaNameStr(res.race_start_info.race_horse_data, _viewerId);
                                EditorGUILayout.LabelField(charaNameStr);
                            }
                        }                        
                    }
                }
            }
        }

        private void SelectSingleModeArcArcRaceRaceInfo(SingleModeArcArcRaceRaceStartResponseDummy.CommonResponse res, int index)
        {
            _selectedSingleModeArcArcRaceResponse = res;
            _selectedSingleModeArcArcRaceRaceIndex = index;
        }        
        #endregion
    }
}

#endif
