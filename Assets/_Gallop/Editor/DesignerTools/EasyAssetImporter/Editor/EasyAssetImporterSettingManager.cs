#if CYG_DEBUG && UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using JetBrains.Annotations;

namespace EasyAssetImporter
{
    //***********************************************************************************************************
    // AssetImporterSettingManager
    //***********************************************************************************************************

    public class EasyAssetImporterSettingManager : ScriptableObject
    {
        //=============================================================================================
        // 変数
        //=============================================================================================

        public string ManagerAssetPath { get; set; } = "";

        public List<SettingItem> _settingList = null;

        [System.NonSerialized]
        public Dictionary<string, string> _baseSettingDataPathDict = null;

        [System.NonSerialized] 
        private int _prevFrameCount = -1;

        public string MissingSettingText { get; private set; } = "";

        //=============================================================================================
        // メソッド
        //=============================================================================================
        
        /// -----------------------------------------
        /// <summary>
        /// インポート設定を取得
        /// </summary>
        /// -----------------------------------------
        public ImportSettingsInfo? GetImporterSettingsAndDetailInfo(string targetAssetPath, EasyAssetImporterSetting.ImportTypes importType)
        {            
            if(IsBaseSettingData(targetAssetPath))
            {
                return null;
            }

            List<SettingItem> _sortedSettingList = new List<SettingItem>();

            for (int p = 0; p < _settingList.Count; p++)
            {
                _sortedSettingList.Add(_settingList[p]);
            }

            _sortedSettingList.Sort((a, b) => b._priority - a._priority);

            for (int p = 0; p < _sortedSettingList.Count; p++)
            {
                SettingItem thisSetting = _sortedSettingList[p];

                if (thisSetting == null)
                {
                    continue;
                }

                EasyAssetImporterSetting thisAssetImporterSetting = thisSetting._assetImporterSetting;

                if (thisAssetImporterSetting == null)
                {
                    continue;
                }

                EasyAssetImporterSetting.ImporterSetting thisImporterSetting = thisAssetImporterSetting.GetImporterSetting(targetAssetPath, importType);

                if (thisImporterSetting == null)
                {
                    continue;
                }

                return new ImportSettingsInfo(thisSetting._info, thisImporterSetting);
            }

            return null;
        }        

        /// -----------------------------------------
        /// <summary>
        /// インポート設定を取得
        /// </summary>
        /// -----------------------------------------
        public EasyAssetImporterSetting.ImporterSetting GetImporterSetting(string targetAssetPath, EasyAssetImporterSetting.ImportTypes importType)
        {
            var result = GetImporterSettingsAndDetailInfo(targetAssetPath, importType);
            if (result == null) return null;

            return result.Value.ImporterSetting;
        }

        /// -----------------------------------------
        /// <summary>
        /// 初期化
        /// </summary>
        /// -----------------------------------------
        public void Initialize()
        {
            if(Time.frameCount == _prevFrameCount)
            {
                return;
            }
            MissingSettingText = "";
            
            if (_baseSettingDataPathDict == null)
            {
                _baseSettingDataPathDict = new Dictionary<string, string>();
            }
            _baseSettingDataPathDict.Clear();

            for (int p = 0; p < _settingList.Count; p++)
            {
                SettingItem thisSetting = _settingList[p];

                if (thisSetting == null)
                {
                    continue;
                }

                EasyAssetImporterSetting thisAssetImporterSetting = thisSetting._assetImporterSetting;

                if (thisAssetImporterSetting == null)
                {
                    MissingSettingText += $"{thisSetting._info}\n";
                    continue;
                }

                for (int q = 0; q < thisAssetImporterSetting._importerSettingList.Count; q++)
                {
                    EasyAssetImporterSetting.ImporterSetting thisImporterSetting = thisAssetImporterSetting._importerSettingList[q];

                    if (thisImporterSetting == null)
                    {
                        continue;
                    }

                    thisImporterSetting.Initialize();

                    UnityEngine.Object thisObject = thisAssetImporterSetting._importerSettingList[q]._baseSettingData;

                    if (thisObject == null)
                    {
                        continue;
                    }

                    string thisDataPath = AssetDatabase.GetAssetPath(thisObject);

                    if (thisDataPath == null)
                    {
                        continue;
                    }

                    if (!File.Exists(thisDataPath))
                    {
                        continue;
                    }

                    if (_baseSettingDataPathDict.ContainsKey(thisDataPath))
                    {
                        continue;
                    }

                    _baseSettingDataPathDict.Add(thisDataPath, Path.GetFileName(thisDataPath));
                }
            }
            
            _prevFrameCount = Time.frameCount;
        }

        /// -----------------------------------------
        /// <summary>
        /// ベース設定かどうか
        /// </summary>
        /// -----------------------------------------
        private bool IsBaseSettingData(string targetFilePath)
        {
            if (targetFilePath == null)
            {
                return false;
            }

            if(!File.Exists(targetFilePath))
            {
                return false;
            }

            if(!_baseSettingDataPathDict.ContainsKey(targetFilePath)) 
            {
                return false;
            }
             
            return true;
        }

        //***********************************************************************************************************
        // SettingItem
        //***********************************************************************************************************

        [System.Serializable]
        public class SettingItem
        {
            public string _info = null;

            public int _priority = 0;

            public EasyAssetImporterSetting _assetImporterSetting = null;
        }
    }
}
#endif // CYG_DEBUG && UNITY_EDITOR //