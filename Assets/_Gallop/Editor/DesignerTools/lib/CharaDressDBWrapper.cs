#if CYG_DEBUG && UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using System.IO;

using UnityEngine;

using Gallop;


namespace App.Editor.DesignerTools.Libs
{
    public class CharaDressDBWrapper
    {
        // Dress関連
        private List<(int, string)> _charaIdList;
        public int currentDressDataCharaId = ModelLoader.InvalidCardId;

        private DressCache _gerenalDressCache;
        private DressCache _spDressCache;
        private DressCache _margedDressCache;

        // キャラ一覧関連
        private List<(int, string)> _cachedCharaDataList;
        private string _charaLastFilter = "";

        public CharaDressDBWrapper()
        {
            _charaIdList = CollectCharaList();
        }

        /// <summary>
        /// キャラクター名一覧を取得する
        /// </summary>
        /// <returns>キャラクターの情報のリスト</returns>
        private List<(int, string)> CollectCharaList()
        {
            var result = new List<(int, string)>();

            if (!MasterDataManager.HasInstance())
            {
                if (Application.isPlaying)
                {
                    MasterDataManager.CreateInstance();
                }
                else
                {
                    bool bootResult = MasterDataManager.BootForEditor();
                    if (!bootResult)
                    {
                        Debug.LogWarning("【ChataTextureUpConverter】Master Dataの起動に失敗しました。\nChara viewerなどが起動していませんか？");
                        return result;
                    }
                }
            }

            MasterCharaData masterCharaData = MasterDataManager.Instance.masterCharaData;

            var charaDataList = GallopUtil.MakeDebugViewerCharaDataList();
            foreach (var charaData in charaDataList)
            {
                var thisItem = masterCharaData.dictionary[charaData.CharaId];
                result.Add((charaData.CharaId, thisItem.Name));
            }

            // DB破棄
            MasterDataManager.Instance.ForceResetDatabases();

            // モブ
            result.Add((ModelLoader.MOB_CHARA_ID, "モブウマ娘"));

            return result;
        }

        /// <summary>
        /// ドレス一覧取得時などにデフォルトで使うIDを更新する
        /// </summary>
        /// <param name="targetCharaId">切り替え先のID</param>
        public void UpdateSelectedCharaId(int targetCharaId)
        {
            currentDressDataCharaId = targetCharaId;
        }

        /// <summary>
        /// currentCharaIdで指定されているキャラクターの情報を帰す
        /// </summary>
        /// <returns>キャラの情報。取得が正しくできていない場合は(ModelLoader.InvalidCardId, "")が帰る</returns>
        public (int, string) GetSelectedCharaInfo()
        {
            foreach (var chara in _charaIdList)
            {
                if (chara.Item1 == currentDressDataCharaId)
                {
                    return (currentDressDataCharaId, chara.Item2);
                }
            }

            return (ModelLoader.InvalidCardId, "");
        }

        /// <summary>
        /// キャラ名一覧を指定されたキーワードでフィルターして該当するものを帰す
        /// </summary>
        /// <param name="searchKeyword">フィルターするのに利用するキーワード</param>
        /// <returns>マッチするキャラの情報のリスト</returns>
        public List<(int, string)> GetFilteredCharaList(string searchKeyword)
        {
            if (searchKeyword == "")
            {
                _charaLastFilter = searchKeyword;
                _cachedCharaDataList = _charaIdList;
                return _charaIdList;
            }

            // キャッシュに当たった場合はそのまま帰す
            if (_charaLastFilter == searchKeyword)
            {
                return _cachedCharaDataList;
            }

            List<(int, string)> filteredList = new List<(int, string)>();
            filteredList = _charaIdList.Where(charaId => charaId.Item1.ToString().Contains(searchKeyword) || charaId.Item2.Contains(searchKeyword)).ToList();

            // キャッシュ類のアップデート
            _charaLastFilter = searchKeyword;
            _cachedCharaDataList = filteredList;

            // キャラリストと不一致を防ぐためDress側は強制クリア
            currentDressDataCharaId = ModelLoader.InvalidCardId;
            ResetSpKindDressCache();

            return filteredList;
        }

        /// <summary>
        /// 現在currentCharaIdで設定されているChara IDに適用可能な衣装一覧を取得する
        /// </summary>
        /// <param name="isMini">検索対象はMiniか</param>
        /// <returns>Dressの情報を格納したリスト</returns>
        public List<DressData> GetCurrentCharaDressList(bool isMini)
        {
            return GetCharaDressList(currentDressDataCharaId, isMini);
        }

        /// <summary>
        /// 指定したCharaIdに設定可能な衣装一覧を取得する
        /// </summary>
        /// <param name="targetCharaId">対象のCharaId</param>
        /// <param name="isMini">検索対象はMiniか</param>
        /// <returns>衣装一覧</returns>
        public List<DressData> GetCharaDressList(int targetCharaId, bool isMini)
        {
            var result = new List<DressData>();
            if (targetCharaId == ModelLoader.InvalidCardId)
            {
                return result;
            }

            if (_margedDressCache != null && _margedDressCache.IsAvaliable(targetCharaId, isMini))
            {
                return _margedDressCache.Data;
            }

            result.AddRange(GetCharaSPDressList(targetCharaId, isMini));
            result.AddRange(GetGeneralDressList(isMini));

            // キャッシュ類の更新
            _margedDressCache = new DressCache(targetCharaId, isMini, result);

            return result;
        }

        /// <summary>
        /// 指定したCharaIdの特別衣装一覧を返す
        /// </summary>
        /// <param name="targetCharaId">検索対象のキャラID</param>
        /// <param name="isMini">ミニか</param>
        /// <returns>条件にマッチする衣装情報のリスト</returns>
        public List<DressData> GetCharaSPDressList(int targetCharaId, bool isMini)
        {
            var result = new List<DressData>();
            if (targetCharaId == ModelLoader.InvalidCardId)
            {
                return result;
            }

            bool bootResult = MasterDataManager.BootForEditor();
            if (!bootResult)
            {
                Debug.LogWarning("【CharaTextureUpconverter】Master Dataの起動に失敗しました。\nChara viewerなどが起動していませんか？");
                return result;
            }

            // SPDressのCacheが存在するか確認
            if (_spDressCache != null && _spDressCache.IsAvaliable(targetCharaId, isMini))
            {
                return _spDressCache.Data;
            }

            MasterDressData masterDress = MasterDataManager.Instance.masterDressData;
            var currentCharaDressList = masterDress.MaybeListWithCharaIdOrderByIdAsc(targetCharaId);

            // DB破棄
            MasterDataManager.Instance.ForceResetDatabases();
            if (currentCharaDressList == null)
            {
                Debug.Log("【CharaTextureUpconverter】指定されたCharaのDressが見つかりません");
            }
            else
            {
                foreach (var charaDress in currentCharaDressList)
                {
                    // Miniが対象の場合はMiniが存在するか確認する
                    if (isMini && !charaDress.HaveMini)
                    {
                        continue;
                    }

                    result.Add(new DressData(charaDress.Id, charaDress.Name, isMini, false));
                }
            }

            // 取得したデータをもとにキャッシュを作り直す
            _spDressCache = new DressCache(targetCharaId, isMini, result);

            return result;
        }

        /// <summary>
        /// 汎用衣装一覧を取得する
        /// </summary>
        /// <param name="isMini">ミニか</param>
        /// <returns>汎用衣装情報のリスト</returns>
        public List<DressData> GetGeneralDressList(bool isMini)
        {
            var result = new List<DressData>();
            bool bootResult = MasterDataManager.BootForEditor();
            if (!bootResult)
            {
                Debug.LogWarning("【CharaTextureUpconverter】Master Dataの起動に失敗しました。\nChara viewerなどが起動していませんか？");
                return result;
            }

            // GeneralDressのCacheが存在するか確認する
            if (_gerenalDressCache != null && _gerenalDressCache.IsAvaliable(ModelLoader.InvalidCardId, isMini))
            {
                return _gerenalDressCache.Data;
            }

            MasterDressData masterDress = MasterDataManager.Instance.masterDressData;
            var charaList = GallopUtil.MakeDebugViewerCharaDataList();
            var dressIdList = charaList[0].DressIdList;
            foreach (var dress in dressIdList)
            {
                var currentDress = masterDress.Get(dress);
                if (currentDress.CharaId == ModelLoader.InvalidCardId)
                {
                    // Miniが対象の場合はMiniが存在するか確認する
                    if (isMini && !currentDress.HaveMini)
                    {
                        continue;
                    }

                    result.Add(new DressData(currentDress.Id, currentDress.Name, isMini, true));
                }
            }

            // DB破棄
            MasterDataManager.Instance.ForceResetDatabases();

            // 取得したデータをもとにキャッシュ更新
            _gerenalDressCache = new DressCache(ModelLoader.InvalidCardId, isMini, result);

            return result;
        }

        /// <summary>
        /// 特別衣装情報周りをリセットする
        /// </summary>
        private void ResetSpKindDressCache()
        {
            _spDressCache = null;
            _margedDressCache = null;
        }

        /// <summary>
        /// 共通衣装か確認する
        /// </summary>
        /// <param name="dressId">検索対象の衣装ID</param>
        /// <returns>共通衣装か</returns>
        public bool IsGeneralDress(int dressId)
        {
            // 処理が走っておらずリストが作成されていない場合は手動作成
            if(_gerenalDressCache == null)
            {
                GetGeneralDressList(false);
            }

            return _gerenalDressCache.Data.Any(item => item.Id == dressId);
        }

        /// <summary>
        /// 表示に使うための衣装名文字列のリストを作成する
        /// </summary>
        /// <param name="dressModelIdSet">作成したいdressIDと関連情報のリスト</param>
        /// <returns>表示文字列のリスト</returns>
        public HashSet<string> GetDressNameStringsFromID(HashSet<(string, string, bool)> dressModelIdSet)
        {
            var dressDisplayNameStringSet = new HashSet<string>();

            bool bootResult = MasterDataManager.BootForEditor();
            if (!bootResult)
            {
                Debug.LogWarning("【CharaTextureUpconverter】Master Dataの起動に失敗しました。\nChara viewerなどが起動していませんか？");
                return dressDisplayNameStringSet;
            }
            MasterDressData masterDress = MasterDataManager.Instance.masterDressData;

            foreach (var dress in dressModelIdSet)
            {
                var dressId = GetDressIdFromModelId(int.Parse(dress.Item1), int.Parse(dress.Item2));
                if(dressId == 0)
                {
                    Debug.Log("【CharaTextureUpconverter】衣装の検出に失敗しました");
                    continue;
                }

                var matchDressData = masterDress.Get(dressId);
                if (dress.Item3)
                {
                    dressDisplayNameStringSet.Add("【ミニ】" + matchDressData.Name);
                }
                else
                {
                    dressDisplayNameStringSet.Add(matchDressData.Name);
                }
            }

            // DB破棄
            MasterDataManager.Instance.ForceResetDatabases();

            return dressDisplayNameStringSet;
        }

        public int GetDressIdFromModelId(int modelMainId, int modelSubId)
        {
            int result = 0;

            var matchItem = _charaIdList.Where(item => item.Item1 == modelMainId).ToArray();

            // 自身のIDがモブまたはマッチするキャラがいない場合は汎用からマッチするものを探す
            if (modelMainId == 1 || matchItem.Length == 0)
            {
                var generalDressList = GetGeneralDressList(false);
                foreach (var currentGeneralDress in generalDressList)
                {
                    // 仮のIDを入れて検索
                    var tempCharaId = 0;
                    CharacterBuildInfo characterBuildInfo = new CharacterBuildInfo(tempCharaId, currentGeneralDress.Id, ModelLoader.ControllerType.Default);
                    var currentPathInfo = characterBuildInfo.CharaBuildPathInfo;
                    var targetPrefabPath = characterBuildInfo.CharaBuildPathInfo.upBodyPrefabPath;
                    var targetPrefabName = Path.GetFileName(targetPrefabPath);
                    if (targetPrefabName.StartsWith($"pfb_bdy{modelMainId.ToString("D4")}_{modelSubId.ToString("D2")}"))
                    {
                        return currentGeneralDress.Id;
                    }
                }
            }

            // マッチするキャラクターがあった場合には特別衣装内にマッチするものがある
            var currentCharaSPDressList = GetCharaSPDressList(modelMainId, false);
            foreach (var currentSPDress in currentCharaSPDressList)
            {
                CharacterBuildInfo characterBuildInfo = new CharacterBuildInfo(modelMainId, currentSPDress.Id, ModelLoader.ControllerType.Default);
                var currentPathInfo = characterBuildInfo.CharaBuildPathInfo;
                var targetPrefabPath = characterBuildInfo.CharaBuildPathInfo.upBodyPrefabPath;
                var targetPrefabName = Path.GetFileName(targetPrefabPath);
                if (targetPrefabName.StartsWith($"pfb_bdy{modelMainId.ToString("D4")}_{modelSubId.ToString("D2")}"))
                {
                    return currentSPDress.Id;
                }
            }

            return result;
        }

        /// <summary>
        /// 表示に使うためのキャラ名文字列のリストを作成する
        /// </summary>
        /// <param name="charaIdSet">取得したいキャラクターの情報</param>
        /// <returns>表示文字列のリスト</returns>
        public HashSet<string> GetCharaNameStringsFromID(HashSet<(string, string, bool)> charaIdSet)
        {
            var headSet = new HashSet<string>();
            foreach (var head in charaIdSet)
            {
                var currentCharaId = int.Parse(head.Item1);
                var matchItem = _charaIdList.Where(item => item.Item1 == currentCharaId).ToArray();
                if (matchItem == null)
                {
                    continue;
                }
                else
                {
                    if (head.Item3)
                    {
                        headSet.Add("【ミニ】" + matchItem[0].Item2);
                    }
                    else
                    {
                        headSet.Add(matchItem[0].Item2);
                    }
                }
            }
            return headSet;
        }
    }

    public class DressData
    {
        public bool IsGeneral
        {
            get;
            private set;
        }

        public int Id
        {
            get;
            private set;
        }

        public bool IsMini
        {
            get;
            private set;
        }

        public string Name
        {
            get;
            private set;
        }

        // 引数なしコンストラクターは認めない
        protected DressData() { }

        public DressData(int dressId, string dressName, bool isMini, bool isGeneral)
        {
            Id = dressId;
            Name = dressName;
            IsMini = isMini;
            IsGeneral = isGeneral;
        }
    }

    /// <summary>
    /// ドレスの情報をキャッシュしておくための入れ物
    /// </summary>
    public class DressCache
    {
        public bool IsMini
        {
            get;
            private set;
        }

        public int CharaId
        {
            get;
            private set;
        }

        public List<DressData> Data;

        // 引数なしコンストラクターは認めない
        protected DressCache() { }

        public DressCache(int charaId, bool isMini, List<DressData> data)
        {
            CharaId = charaId;
            IsMini = isMini;
            Data = data;
        }

        public bool IsAvaliable(int charaId, bool isMini)
        {
            return ((CharaId == charaId) && (IsMini == isMini));
        }
    }
}
#endif // CYG_DEBUG && UNITY_EDITOR