#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace App.Editor.DesignerTools.DummyCreator
{
    /// ***********************************************************************************************************
    /// DummyCreatorSettingInspector
    /// ***********************************************************************************************************
    [CustomEditor( typeof( DummyCreatorSetting ) )]
    public class DummyCreatorSettingInspector : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            DummyCreatorSetting setting = Selection.activeObject as DummyCreatorSetting;

            if(setting != null)
            {
                for (int p = 0; p < setting._createSettingList.Count; p++)
                {
                    DummyCreatorSetting.CreateSetting thisCreateSetting = setting._createSettingList[p];

                    string buttonLabel = string.Format("{0} {1}", thisCreateSetting._info, "ダミー作成");

                    if (GUILayout.Button(buttonLabel, new GUILayoutOption[] { GUILayout.Height(30) }))
                    {
                        setting.CreateDummy(p);
                    }
                }
            }

            DrawDefaultInspector();

            serializedObject.ApplyModifiedProperties();
        }
    }
}
#endif // CYG_DEBUG && UNITY_EDITOR //