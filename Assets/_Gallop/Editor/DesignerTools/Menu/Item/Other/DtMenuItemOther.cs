#if CYG_DEBUG && UNITY_EDITOR
using UnityEditor;

namespace App.Editor.DesignerTools.Menu.Other
{
    public class DtMenuItemOther : DtMenuItemBase
    {
        private Utility.DtMenuItemUtility _utility;
        private Renamer.DtMenuItemRenamer _renamer;

        //=============================================================================================
        // コンストラクタ
        //=============================================================================================

        public DtMenuItemOther(EditorWindow window, DtMenuItemBase parent)
            : base( window, parent )
        {
            _toolTitle = "その他";
        }

        //=============================================================================================
        // メソッド
        //=============================================================================================

        /// -----------------------------------------
        /// <summary>
        /// UpdateInsideGUI
        /// </summary>
        /// -----------------------------------------
        public override void UpdateInsideGUI()
        {
            base.UpdateInsideGUI();

            if(_renamer == null)
            {
                _renamer = new Renamer.DtMenuItemRenamer( _window, this );
            }

            if( _utility == null )
            {
                _utility = new Utility.DtMenuItemUtility( _window, this );
            }

            _utility.UpdateGUI();
            _renamer.UpdateGUI();
        }
    }


    
}
#endif // CYG_DEBUG && UNITY_EDITOR //