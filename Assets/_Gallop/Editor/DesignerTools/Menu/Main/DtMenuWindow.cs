#if CYG_DEBUG && UNITY_EDITOR
using Gallop.Live.Cutt;
using UnityEditor;
using UnityEngine;
using App.Editor.DesignerTools.Menu;

namespace App.Editor.DesignerTools.Menu
{
    public class DtMenuWindow : EditorWindow
    {
        private Prefab.DtMenuItemPrefabNew _newPrefabEditor;

        private Effect.EffectEditor _effectEditor;

        private Other.DtMenuItemOther _otherMain;

        private ExtensionsAsset.DtMenuItemExtensionsAsset _extensionsAsset;

        private Vector2 _currentScrollPosition;

        /// -----------------------------------------
        /// <summary>
        /// Init
        /// </summary>
        /// -----------------------------------------
        [MenuItem("Gallop/デザイナーツール", false, 10000)]
        static void Init()
        {
            DtMenuWindow window = DtMenuWindow.GetWindow(typeof(DtMenuWindow)) as DtMenuWindow;

            window.titleContent.text = "デザイナーツール";
        }

        /// -----------------------------------------
        /// <summary>
        /// OnGUI
        /// </summary>
        /// -----------------------------------------
        private void Initialize()
        {
            if (_newPrefabEditor == null)
            {
                _newPrefabEditor = new Prefab.DtMenuItemPrefabNew(this, null);
            }

            if (_otherMain == null)
            {
                _otherMain = new Other.DtMenuItemOther(this, null);
            }

            if (_extensionsAsset == null)
            {
                _extensionsAsset = new ExtensionsAsset.DtMenuItemExtensionsAsset(this, null);
            }

            if (_effectEditor == null)
            {
                _effectEditor = new Effect.EffectEditor(this, null);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// OnGUI
        /// </summary>
        /// -----------------------------------------
        private void OnGUI()
        {
            Initialize();

            _currentScrollPosition = EditorGUILayout.BeginScrollView(_currentScrollPosition, false, false);

            _newPrefabEditor.UpdateGUI();

            _effectEditor.UpdateGUI();

            _extensionsAsset.UpdateGUI();

            _otherMain.UpdateGUI();

            GUILayout.Space(5);

            EditorGUILayout.BeginHorizontal();
            GUILayout.Box("", new GUILayoutOption[] { GUILayout.ExpandWidth(true), GUILayout.Height(1) });
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndScrollView();
        }
    }
}
#endif // CYG_DEBUG && UNITY_EDITOR //