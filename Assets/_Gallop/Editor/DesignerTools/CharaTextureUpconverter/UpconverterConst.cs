#if CYG_DEBUG && UNITY_EDITOR
using System.IO;
using UnityEngine;

using App.Editor.DesignerTools.Libs;
using Gallop;


namespace CharaTextureUpconverter
{
    public static class Constant
    {

        // GIT BASHのインストールされているべきパス
        public const string GIT_BASH_PATH = @"C:\Program Files\Git\bin\bash.exe";

        public static readonly string GALLOP_RESOURCES_PATH = Path.Combine(Application.dataPath, "_GallopResources");

        // Asset Importerのパス
        public const string ASSETIMPORTER_DATA_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/_Setting/AssetImporter/Data/";

        public const string BODY_BUNDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Body/";
        public const string BODY_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Body/";
        public const string HEAD_BUNDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Head/";
        public const string HEAD_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Head/";
        public const string TAIL_BANDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Tail/";
        public const string TAIL_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Tail/";
        public const string ATTACH_BANDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Attach/";
        public const string ATTACH_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Attach/";
        public const string MINI_BODY_BUNDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Mini/Body/";
        public const string MINI_BODY_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Mini/Body/";
        public const string MINI_HEAD_BUNDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Mini/Head/";
        public const string MINI_HEAD_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Mini/Head/";
        public const string MINI_TAIL_BANDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Mini/Tail/";
        public const string MINI_TAIL_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Mini/Tail/";
        public const string MINI_ATTACH_BANDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Mini/Attach/";
        public const string MINI_ATTACH_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Mini/Attach/";
        public const string CHARA_COMMON_BANDLE_PATH = "Assets/_GallopResources/Bundle/Resources/3d/Chara/Common/";
        public const string CHARA_COMMON_SOURCE_RESOURCE_PATH = "Assets/_GallopResources/SourceResources/3d/Chara/Common/";

        // プロップ関連のパス
        public static readonly string PROP_BUNDLE_PATH = Path.Combine(ResourcePath.BundleResourcesAssetsPath, ResourcePath.CharacterPropRoot);
        public static readonly string PROP_SOURCE_RESOURCE_PATH = Path.Combine(ResourcePath.SourceResourcesAssetsPath, ResourcePath.CharacterPropRoot);
        public static readonly string TOON_PROP_BUNDLE_PATH = Path.Combine(ResourcePath.BundleResourcesAssetsPath, ResourcePath.CharacterToonPropRoot);
        public static readonly string TOON_PROP_SOURCE_RESOURCE_PATH = Path.Combine(ResourcePath.SourceResourcesAssetsPath, ResourcePath.CharacterToonPropRoot);

        // コンバート処理を行うJSX
        public static readonly string EXPORT_JSX_ABS_PATH = PathConverter.AssetPathToAbsPath(EXPORT_JSX_UNITY_PATH);
        public const string EXPORT_JSX_UNITY_PATH = "Assets/_Gallop/Editor/DesignerTools/CharaTextureUpconverter/JSX/directConvert.jsx";

        // JSXとの情報のやり取りを行う際に利用するファイル関連
        public static readonly string APPDATA_FOLDER = System.Environment.GetEnvironmentVariable("APPDATA").Replace("\\", "/");
        public static readonly string CONNECT_FOLDER = Path.Combine(APPDATA_FOLDER, "Cygames/CharaTextureUpconverter").Replace("\\", "/");
        public static readonly string EXPORT_LOCK_FILE = Path.Combine(CONNECT_FOLDER, "export.lock").Replace("\\", "/");
        public static readonly string DATA_MESSANGER_SENDER_FILE = Path.Combine(CONNECT_FOLDER, "exportInfo.txt").Replace("\\", "/");
        public static readonly string MESSAGE_FROM_PS_FILE = Path.Combine(CONNECT_FOLDER, "psLog.txt").Replace("\\", "/");
        public static readonly string REQUEST_REIMPORT_FILE = Path.Combine(CONNECT_FOLDER, "needReimport.txt").Replace("\\", "/");

        // コンバートを行った衣装/キャラを識別するのに使うフォルダーのリスト
        public static readonly string[] IDENTIFY_DRESS_DIFF_FOLDER_ARRAY = new string[]
        {
            PathConverter.AssetPathToAbsPath(BODY_BUNDLE_PATH),
            PathConverter.AssetPathToAbsPath(BODY_SOURCE_RESOURCE_PATH),
            PathConverter.AssetPathToAbsPath(HEAD_BUNDLE_PATH),
            PathConverter.AssetPathToAbsPath(HEAD_SOURCE_RESOURCE_PATH),
            PathConverter.AssetPathToAbsPath(MINI_BODY_BUNDLE_PATH),
            PathConverter.AssetPathToAbsPath(MINI_BODY_SOURCE_RESOURCE_PATH),
            PathConverter.AssetPathToAbsPath(MINI_HEAD_BUNDLE_PATH),
            PathConverter.AssetPathToAbsPath(MINI_HEAD_SOURCE_RESOURCE_PATH),
        };

        // コンバートを行ったプロップを識別するのに使うフォルダーのリスト
        public static readonly string[] IDENTIFY_PROP_DIFF_FOLDER_ARRAY = new string[]
        {
            PathConverter.AssetPathToAbsPath(PROP_BUNDLE_PATH),
            PathConverter.AssetPathToAbsPath(PROP_SOURCE_RESOURCE_PATH),
            PathConverter.AssetPathToAbsPath(TOON_PROP_BUNDLE_PATH),
            PathConverter.AssetPathToAbsPath(TOON_PROP_SOURCE_RESOURCE_PATH),
        };

        // 出力対応解像度
        public static string[] ConvertResolutionArray = new string[]
        {
            "4096",
            "8192"
        };

    }
}
#endif //CYG_DEBUG && UNITY_EDITOR