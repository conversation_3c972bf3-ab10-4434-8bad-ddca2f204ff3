#if CYG_DEBUG && UNITY_EDITOR
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using Gallop;
using System;
using System.Reflection;
using Gallop.Live;

namespace ModelPrefabCreator
{
    /// ***********************************************************************************************************
    /// <summary>
    /// ModelPrefabCreatorSettingProcessMethod
    /// </summary>
    /// ***********************************************************************************************************
    public class ModelPrefabCreatorSettingProcessMethod
    {
        //=============================================================================================
        // 定数
        //=============================================================================================

        const string PREFAB_PREFIX = "pfb_";
        const string MATERIAL_PREFIX = "mtl_";

        const string FACE_MATERIAL_SUFFIX = "_face";
        const string HAIR_MATERIAL_SUFFIX = "_hair";

        const string LOCATOR_FACE_PREFIX = "Face";
        const string LOCATOR_HAIR_PREFIX = "Hair";
        const string LOCATOR_BODY_PREFIX = "Body";
        const string LOCATOR_TAIL_PREFIX = "Tail";
        const string LOCATOR_TOON_PROP_PREFIX = "ToonProp";

        const string LOCATOR_SPEC_SUFFIX = "_spec_info";
        const string LOCATOR_REFLECTION_ADD_SUFFIX = "_rfl_add_info";
        const string LOCATOR_REFLECTION_MUL_SUFFIX = "_rfl_mul_info";
        const string LOCATOR_REFLECTION_POW_SUFFIX = "_rfl_pow_info";

        const string PROPERTY_SPEC_COLOR = "_SpecularColor";
        const string PROPERTY_REFRECTION_ADD = "_ReflectionAddColor";
        const string PROPERTY_REFLECTION_MUL = "_ReflectionMulColor";
        const string PROPERTY_REFLECTION_POW = "_ReflectionPowVal";

        //=============================================================================================
        // 変数
        //=============================================================================================

        ModelPrefabCreatorMain _main = null;

        //=============================================================================================
        // コンストラクタ
        //=============================================================================================

        public ModelPrefabCreatorSettingProcessMethod(ModelPrefabCreatorMain main)
        {
            _main = main;
        }

        //=============================================================================================
        // メソッド
        //=============================================================================================

        /// -----------------------------------------
        /// <summary>
        /// プロセス実行
        /// </summary>
        /// -----------------------------------------
        public void Process(
            ModelPrefabCreatorSetting.CreateSetting createSetting,
            ModelPrefabCreatorSetting.ProcessMethodSettingItem.ProcessTypes processType
            )
        {
            if (createSetting == null)
            {
                return;
            }

            List<ModelPrefabCreatorSetting.ProcessMethodSettingItem> targetList = null;

            if (processType == ModelPrefabCreatorSetting.ProcessMethodSettingItem.ProcessTypes.Preprocess)
            {
                targetList = createSetting._preprocessMethodSettingList;
            }
            else if (processType == ModelPrefabCreatorSetting.ProcessMethodSettingItem.ProcessTypes.Postprocess)
            {
                targetList = createSetting._postprocessMethodSettingList;
            }
            else if (processType == ModelPrefabCreatorSetting.ProcessMethodSettingItem.ProcessTypes.Startprocess)
            {
                targetList = createSetting._startprocessMethodSettingList;
            }
            else if (processType == ModelPrefabCreatorSetting.ProcessMethodSettingItem.ProcessTypes.Endprocess)
            {
                targetList = createSetting._endprocessMethodSettingList;
            }

            if (targetList.Count == 0)
            {
                return;
            }

            for (int p = 0; p < targetList.Count; p++)
            {
                ModelPrefabCreatorSetting.ProcessMethodSettingItem item = targetList[p];

                if (item._methodLabel == null)
                {
                    continue;
                }

                if (item._methodLabel == "")
                {
                    continue;
                }

                switch (item._methodLabel)
                {
                    case "TestMethod":
                        TestMethod();
                        break;

                    case "NormalToTangent":
                        ApplyNormalToTangent();
                        break;

                    case "AssetHolder":
                        ApplyAssetHolder();
                        break;

                    case "SetBodyBounds":
                        SetBodyBounds();
                        break;

                    case "SetHeadBounds":
                        SetHeadBounds();
                        break;

                    case "SetHeadCenterOffset":
                        SetHeadCenterOffset();
                        break;

                    case "SetSpecularColor":
                        SetMaterialPropertyFromLocator(PROPERTY_SPEC_COLOR);
                        break;
                    case "SetReflectionProperty":
                        SetMaterialPropertyFromLocator(PROPERTY_REFRECTION_ADD);
                        SetMaterialPropertyFromLocator(PROPERTY_REFLECTION_MUL);
                        SetMaterialPropertyFromLocator(PROPERTY_REFLECTION_POW);
                        break;

                    case "SetUseSpring":
                        SetUseCySpring();
                        break;

                    case "KeepAssetTableValues":
                        KeepAssetTableValues(item._targetName);
                        break;

                    case "SetHeadSkinRootBone":
                        SetHeadSkinRootBone();
                        break;

                    case "SetPrefabBounds":
                        SetPrefabBounds();
                        break;

                    case "SetPropBounds":
                        SetPropBounds();
                        break;

                    case "SetJukeBox":
                        SetJukeBox(item._targetName);
                        break;

                    case "SetMiniJukeBox":
                        SetMiniJukeBox(item._targetName, item._targetList);
                        break;

                    case "SetHomeJukeBoxMeshNameObj":
                        SetHomeJukeBoxMeshNameObj(item._targetName, item._targetList);
                        break;

                    case "SetChildLayerSameParent":
                        SetChildLayerSameParent(item._targetName);
                        break;

                    case "KeepScriptComponent":
                        KeepScriptComponentsInChildGameObjects(item._targetPair.DataDic);
                        break;

                    default:
                        break;
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// TestMethod
        /// </summary>
        /// -----------------------------------------
        public void TestMethod()
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// ApplyNormalToTangent
        /// </summary>
        /// -----------------------------------------
        public void ApplyNormalToTangent()
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            UnityEngine.Object[] loadObjectList = AssetDatabase.LoadAllAssetsAtPath(_main.ModelFilePath);

            if (loadObjectList.Length == 0)
            {
                return;
            }

            string rootDirPath = Path.GetDirectoryName(_main.ModelFilePath);
            rootDirPath = Path.GetDirectoryName(rootDirPath);

            string meshDirPath = rootDirPath + "/Meshes/" + Path.GetFileNameWithoutExtension(_main.ModelFilePath);

            // 初回だったらMeshses/モデル名のフォルダを作る
            if (!Directory.Exists(meshDirPath))
            {
                Directory.CreateDirectory(meshDirPath);

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
            // 既存のメッシュアセットを一旦全部消す（削除されたメッシュアセットが残ってしまうのを防ぐ為）
            else
            {
                string[] existingMeshAssets = Directory.GetFiles(meshDirPath);
                foreach (string existingMeshAssetPath in existingMeshAssets)
                {
                    File.Delete(existingMeshAssetPath);
                }
            }

            List<Mesh> originalMeshList = new List<Mesh>();

            for (int p = 0; p < loadObjectList.Length; p++)
            {
                Mesh thisMesh = loadObjectList[p] as Mesh;

                if (thisMesh == null)
                {
                    continue;
                }

                originalMeshList.Add(thisMesh);
            }

            if (originalMeshList.Count == 0)
            {
                return;
            }

            SkinnedMeshRenderer[] skinnedMeshRendererList = _main.PrefabInstanceObject.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            MeshFilter[] meshFilterList = _main.PrefabInstanceObject.GetComponentsInChildren<MeshFilter>(true);

            // 各々のメッシュがどこに紐づいているのか取れる必要があるためDictでとっておく
            Dictionary<Mesh, string> meshPathDict = new Dictionary<Mesh, string>();

            for (int p = 0; p < originalMeshList.Count; p++)
            {
                Mesh thisMesh = originalMeshList[p] as Mesh;

                if (thisMesh == null)
                {
                    continue;
                }

                Mesh newMesh = new Mesh();

                newMesh.name = thisMesh.name;

                newMesh.vertices = thisMesh.vertices;
                newMesh.triangles = thisMesh.triangles;

                newMesh.boneWeights = thisMesh.boneWeights;
                newMesh.bindposes = thisMesh.bindposes;

                newMesh.normals = thisMesh.normals;

                // uvフィールドはアクセスする度にコピーされるため事前に取得
                var uv = thisMesh.uv;
                var uv2 = thisMesh.uv2;
                var uv3 = thisMesh.uv3;
                var uv4 = thisMesh.uv4;

                // Billboardメッシュはuv2, uv3, uv4に格納したUV情報を元メッシュのまま使う
                if (thisMesh.name.EndsWith("Billboard"))
                {
                    newMesh.uv = uv;
                    newMesh.uv2 = uv2;
                    newMesh.uv3 = uv3;
                    newMesh.uv4 = uv4;
                }
                else
                {
                    List<Vector4> tangentList = null;

                    if (uv3 != null && uv4 != null)
                    {
                        if (uv3.Length == uv4.Length)
                        {
                            tangentList = new List<Vector4>();

                            for (int q = 0; q < uv3.Length; q++)
                            {
                                Vector2 thisUV3 = uv3[q];
                                Vector2 thisUV4 = uv4[q];

                                Vector4 thisTangent = new Vector4(-thisUV3[0], thisUV3[1], thisUV4[0], 1);

                                tangentList.Add(thisTangent);
                            }
                        }
                    }

                    if (tangentList == null)
                    {
                        tangentList = new List<Vector4>();

                        for (int q = 0; q < thisMesh.normals.Length; q++)
                        {
                            Vector3 thisNormal = thisMesh.normals[q];

                            Vector4 thisTangent = new Vector4(thisNormal[0], thisNormal[1], thisNormal[2], 1);

                            tangentList.Add(thisTangent);
                        }
                    }

                    newMesh.tangents = tangentList.ToArray();

                    newMesh.uv = uv;

                    newMesh.uv2 = null;

                    if (uv2 != null)
                    {
                        int nonZeroUVCount = 0;
                        for (int q = 0; q < uv2.Length; q++)
                        {
                            Vector2 thisUV2 = uv2[q];

                            if (thisUV2.x != 0 || thisUV2.y != 0)
                            {
                                nonZeroUVCount += 1;
                            }

                            if (nonZeroUVCount > 4)
                            {
                                break;
                            }
                        }

                        if (nonZeroUVCount > 4)
                        {
                            newMesh.uv2 = uv2;
                        }
                    }

                    if (uv3 != null)
                    {
                        List<Vector2> uv3List = new List<Vector2>();

                        bool existUV3 = false;

                        for (int q = 0; q < uv3.Length; q++)
                        {
                            Vector2 thisUV3 = uv3[q];

                            Vector2 thisUV = new Vector2(0, 0);

                            if (thisUV3.x >= 2 && thisUV3.y >= 2)
                            {
                                thisUV.x = thisUV3.x - 2;
                                thisUV.y = thisUV3.y - 2;
                                existUV3 = true;
                            }

                            uv3List.Add(thisUV);
                        }

                        if (!existUV3)
                        {
                            uv3List.Clear();
                        }

                        if (uv3List.Count > 0)
                        {
                            newMesh.uv3 = uv3List.ToArray();
                        }
                    }

                    newMesh.subMeshCount = thisMesh.subMeshCount;

                    if (thisMesh.subMeshCount > 1)
                    {
                        for (int q = 0; q < thisMesh.subMeshCount; q++)
                        {
                            int[] subMeshIndices = thisMesh.GetIndices(q);

                            MeshTopology subMeshTopology = thisMesh.GetTopology(q);

                            int[] subMeshTriangles = thisMesh.GetTriangles(q);

                            newMesh.SetIndices(subMeshIndices, subMeshTopology, q);

                            newMesh.SetTriangles(subMeshTriangles, q);

                        }
                    }

                    //ボディのUVの3番目にテクスチャ参照番号データを追加
                    //また追加でアウトライン用のメッシュを追加
                    if (_main.PrefabInstanceObject.name.Contains("bdy"))
                    {
                        Vector2[] texcoordUVList = newMesh.uv.Clone() as Vector2[];
                        List<int> addTriangleList = new List<int>();

                        //初期化
                        for (int q = 0; q < texcoordUVList.Length; q++)
                        {
                            texcoordUVList[q] = Vector2.zero;
                        }

                        //作成
                        for (int q = 0; q < skinnedMeshRendererList.Length; q++)
                        {
                            SkinnedMeshRenderer thisRenderer = skinnedMeshRendererList[q];

                            if (thisMesh != thisRenderer.sharedMesh)
                            {
                                continue;
                            }

                            int targetMaterialCount = 0;

                            for (int r = 0; r < thisRenderer.sharedMaterials.Length; r++)
                            {
                                Material thisMaterial = thisRenderer.sharedMaterials[r];

                                if (thisMaterial.name.Contains("bdy0001_00_2"))
                                {
                                    continue;
                                }

                                if (r >= thisMesh.subMeshCount)
                                {
                                    break;
                                }

                                targetMaterialCount += 1;

                                int[] subMeshIndices = thisMesh.GetIndices(r);

                                for (int s = 0; s < subMeshIndices.Length; s++)
                                {
                                    texcoordUVList[subMeshIndices[s]].x = targetMaterialCount - 1;
                                }

                                addTriangleList.AddRange(subMeshIndices);
                            }

                            break;
                        }

                        newMesh.uv3 = texcoordUVList;

                        if (addTriangleList.Count > 0)
                        {
                            newMesh.subMeshCount++;
                            newMesh.SetTriangles(addTriangleList, newMesh.subMeshCount - 1);
                        }
                    }
                }

                newMesh.colors = thisMesh.colors;
                newMesh.colors32 = thisMesh.colors32;

                if (thisMesh.isReadable)
                {
                    newMesh.UploadMeshData(false);
                }
                else
                {
                    newMesh.UploadMeshData(true);
                }

                string meshFilePath = meshDirPath + "/" + newMesh.name + ".asset";

                string renamedMeshFilePath = MeshNameConflictAvoider(meshFilePath);

                AssetDatabase.CreateAsset(newMesh, renamedMeshFilePath);

                meshPathDict.Add(thisMesh, renamedMeshFilePath);
            }

            if (meshPathDict.Count == 0)
            {
                return;
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Dictionary<Mesh, Mesh> meshPairDict = new Dictionary<Mesh, Mesh>();

            foreach (KeyValuePair<Mesh, string> item in meshPathDict)
            {
                string thisFileName = Path.GetFileName(item.Value);

                if (thisFileName.Contains(".meta"))
                {
                    continue;
                }

                Mesh thisMesh = AssetDatabase.LoadAssetAtPath<Mesh>(item.Value);

                if (thisMesh == null)
                {
                    continue;
                }

                meshPairDict.Add(item.Key, thisMesh);
            }

            if (meshPairDict.Count == 0)
            {
                return;
            }

            for (int p = 0; p < skinnedMeshRendererList.Length; p++)
            {
                SkinnedMeshRenderer thisSkinnedMeshRenderer = skinnedMeshRendererList[p];

                Mesh hitMesh = null;

                if (meshPairDict.ContainsKey(thisSkinnedMeshRenderer.sharedMesh))
                {
                    hitMesh = meshPairDict[thisSkinnedMeshRenderer.sharedMesh];
                }

                if (hitMesh == null)
                {
                    continue;
                }

                thisSkinnedMeshRenderer.sharedMesh = hitMesh;
            }

            for (int p = 0; p < meshFilterList.Length; p++)
            {
                MeshFilter thisMeshFilter = meshFilterList[p];

                Mesh hitMesh = null;

                if (meshPairDict.ContainsKey(thisMeshFilter.sharedMesh))
                {
                    hitMesh = meshPairDict[thisMeshFilter.sharedMesh];
                }

                if (hitMesh == null)
                {
                    continue;
                }

                thisMeshFilter.sharedMesh = hitMesh;
            }
        }

        /// <summary>
        /// ファイル名が衝突しないようにファイル名の最後に数字をつけてずらす処理を加える
        /// </summary>
        /// <param name="basePath">衝突しないよう修正したいファイル名</param>
        /// <returns>衝突しないファイル名</returns>
        private string MeshNameConflictAvoider(string basePath)
        {
            // 名前がかぶっていない場合は即時return
            if (!System.IO.File.Exists(basePath))
            {
                return basePath;
            }

            string renamedPath;
            string baseDirName = System.IO.Path.GetDirectoryName(basePath);
            string baseFileName = System.IO.Path.GetFileNameWithoutExtension(basePath);
            string baseFileExt = System.IO.Path.GetExtension(basePath);

            // loopでカウントアップして末尾の数が衝突しなくなるまで続ける
            for (int i = 1; ; i++)
            {
                var currentFullPath = baseDirName + "/" + baseFileName + i + baseFileExt;

                if (!System.IO.File.Exists(currentFullPath))
                {
                    renamedPath = currentFullPath;
                    break;
                }
            }

            return renamedPath;
        }

        /// -----------------------------------------
        /// <summary>
        /// ApplyAssetHolder
        /// </summary>
        /// -----------------------------------------
        public void ApplyAssetHolder()
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            AssetHolder assetHolder = _main.PrefabInstanceObject.GetComponent<AssetHolder>();

            if (assetHolder == null)
            {
                return;
            }

            List<AssetHolder.StringObjectPair> removePairList = new List<AssetHolder.StringObjectPair>();

            for (int i = 0; i < assetHolder.ObjectList.Count; i++)
            {
                AssetHolder.StringObjectPair stringObjectPair = assetHolder.ObjectList[i];

                if (stringObjectPair == null)
                {
                    continue;
                }

                if (stringObjectPair.Key == null)
                {
                    continue;
                }

                string thisKey = stringObjectPair.Key;

                if (!thisKey.Contains("____"))
                {
                    continue;
                }

                string[] splitStr = thisKey.Split(new string[] { "____" }, System.StringSplitOptions.None);

                if (splitStr.Length != 2)
                {
                    continue;
                }

                string thisRealKey = splitStr[0];

                splitStr = splitStr[1].Split(new string[] { "__" }, System.StringSplitOptions.None);

                if (splitStr.Length == 0)
                {
                    continue;
                }

                string thisType = splitStr[0];
                string thisRange = splitStr[1];
                string thisName = splitStr[2];
                bool thisKeep = true;

                for (int p = 0; p < splitStr.Length; p++)
                {
                    string thisString = splitStr[p];
                    string[] thisSplit = thisString.Split('_');

                    if (thisSplit.Length < 2)
                    {
                        continue;
                    }

                    string thisFlag = thisSplit[0].ToLower();

                    int thisValueStartIndex = thisString.IndexOf('_');

                    string thisValue = thisString.Substring(thisValueStartIndex + 1).ToLower();

                    if (thisFlag == "keep")
                    {
                        if (thisValue == "0")
                        {
                            thisKeep = false;
                        }
                    }
                }

                bool fullMatch = false;

                if (thisName[thisName.Length - 1] == '!')
                {
                    fullMatch = true;
                    thisName = thisName.Substring(0, thisName.Length - 1);
                }

                //ファイルフラグ判定
                if (Regex.IsMatch(thisName, "<FILE>.*</FILE>"))
                {
                    string hitString = Regex.Match(thisName, "<FILE>.*</FILE>").Value;

                    hitString = Regex.Match(hitString, ">.*<").Value;

                    hitString = hitString.Substring(1, hitString.Length - 2);

                    string replaceString = "";

                    if (Regex.IsMatch(_main.PrefabFileName, hitString))
                    {
                        replaceString = Regex.Match(_main.PrefabFileName, hitString).Value;
                    }

                    thisName = Regex.Replace(thisName, "<FILE>.*</FILE>", replaceString);
                }

                string groupRegex = "<GROUP>.*</GROUP>";

                //グループフラグ判定
                if (Regex.IsMatch(thisName, groupRegex))
                {
                    string hitString = Regex.Match(thisName, groupRegex).Value;

                    hitString = Regex.Match(hitString, ">.*<").Value;

                    hitString = hitString.Substring(1, hitString.Length - 2);

                    string replaceString = "";

                    if (Regex.IsMatch(_main.PrefabFileName, hitString))
                    {
                        GroupCollection hitStringGroup = Regex.Match(_main.PrefabFileName, hitString).Groups;
                        for (var k = 1; k < hitStringGroup.Count; k++)
                        {
                            if (hitStringGroup[k].Success)
                            {
                                replaceString += hitStringGroup[k].Value;
                            }
                        }
                    }

                    thisName = Regex.Replace(thisName, "<GROUP>.*</GROUP>", replaceString);
                }

                string thisLowerName = thisName.ToLower();

                if (thisType == "object")
                {
                    if (thisRange == "self")
                    {
                        List<Transform> transformList = new List<Transform>(_main.PrefabInstanceObject.GetComponentsInChildren<Transform>(true));

                        Transform targetTransform = null;
                        for (int q = 0; q < transformList.Count; q++)
                        {
                            string thisTransformLowerName = transformList[q].name.ToLower();

                            if (!fullMatch)
                            {
                                if (thisTransformLowerName.Contains(thisLowerName))
                                {
                                    targetTransform = transformList[q];
                                    break;
                                }
                            }
                            else
                            {
                                if (thisTransformLowerName == thisLowerName)
                                {
                                    targetTransform = transformList[q];
                                    break;
                                }
                            }
                        }

                        if (targetTransform != null)
                        {
                            stringObjectPair.Value = targetTransform.gameObject;
                        }
                    }
                    if (thisRange == "local")
                    {
                        string parentDirPath = Path.GetDirectoryName(Path.GetDirectoryName(_main.ModelFilePath));

                        string[] filePathList = Directory.GetFiles(parentDirPath, "*", SearchOption.AllDirectories);

                        UnityEngine.Object targetObject = null;
                        for (int q = 0; q < filePathList.Length; q++)
                        {
                            string thisFileName = Path.GetFileName(filePathList[q]);
                            string thisFileLowerName = thisFileName.ToLower();

                            if (thisFileLowerName.Contains(thisLowerName))
                            {
                                UnityEngine.Object thisTex = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(filePathList[q]);

                                if (thisTex == null)
                                {
                                    continue;
                                }

                                targetObject = thisTex;
                                break;
                            }
                        }

                        if (targetObject != null)
                        {
                            stringObjectPair.Value = targetObject;
                        }
                    }
                    if (thisRange == "global")
                    {
                        var findAssetPath = AssetDatabase.GUIDToAssetPath(AssetDatabase.FindAssets(thisLowerName).FirstOrDefault());

                        var targetObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(findAssetPath);

                        if (targetObject != null)
                        {
                            stringObjectPair.Value = targetObject;
                        }
                    }
                    if (thisRange == "minilocal")
                    {
                        string parentDirPath = Path.GetDirectoryName(Path.GetDirectoryName(_main.ModelFilePath));
                        // Miniのpathをreplaceする
                        parentDirPath = parentDirPath.Replace("mchr", "chr");
                        parentDirPath = parentDirPath.Replace("mbdy", "bdy");
                        parentDirPath = parentDirPath.Replace("mtail", "tail");
                        parentDirPath = parentDirPath.Replace("\\Mini", "");

                        if (Directory.Exists(parentDirPath))
                        {
                            string[] filePathList = Directory.GetFiles(parentDirPath, "*", SearchOption.AllDirectories);

                            UnityEngine.Object targetObject = null;
                            for (int q = 0; q < filePathList.Length; q++)
                            {
                                string thisFileName = Path.GetFileName(filePathList[q]);
                                string thisFileLowerName = thisFileName.ToLower();

                                if (thisFileLowerName.Contains(thisLowerName))
                                {
                                    UnityEngine.Object thisTex = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(filePathList[q]);

                                    if (thisTex == null)
                                    {
                                        continue;
                                    }

                                    targetObject = thisTex;
                                    break;
                                }
                            }

                            if (targetObject != null)
                            {
                                stringObjectPair.Value = targetObject;
                            }

                        }
                    }
                }
                else if (thisType == "texture")
                {
                    if (thisRange == "local")
                    {
                        string parentDirPath = Path.GetDirectoryName(Path.GetDirectoryName(_main.ModelFilePath));

                        string[] filePathList = Directory.GetFiles(parentDirPath, "*", SearchOption.AllDirectories);

                        Texture targetTexture = null;
                        for (int q = 0; q < filePathList.Length; q++)
                        {
                            string thisFileName = Path.GetFileName(filePathList[q]);
                            string thisFileLowerName = thisFileName.ToLower();

                            if (thisFileLowerName.Contains(thisLowerName))
                            {
                                Texture thisTex = AssetDatabase.LoadAssetAtPath<Texture>(filePathList[q]);

                                if (thisTex == null)
                                {
                                    continue;
                                }

                                targetTexture = thisTex;
                                break;
                            }
                        }

                        if (targetTexture != null)
                        {
                            stringObjectPair.Value = targetTexture;
                        }
                    }
                }
                else if (thisType == "material")
                {
                    if (thisRange == "local")
                    {
                        string parentDirPath = Path.GetDirectoryName(Path.GetDirectoryName(_main.ModelFilePath));

                        string modelFileName = Path.GetFileNameWithoutExtension(_main.ModelFilePath);
                        string materialFilePrefixName = modelFileName.Replace("mdl_", "mtl_");
                        string materialFilePrefixLowerName = materialFilePrefixName;

                        string[] filePathList = Directory.GetFiles(parentDirPath, "*", SearchOption.AllDirectories);

                        Material targetMaterial = null;
                        for (int q = 0; q < filePathList.Length; q++)
                        {
                            string thisFileName = Path.GetFileNameWithoutExtension(filePathList[q]);
                            string thisFileLowerName = thisFileName.ToLower();

                            if (!thisFileLowerName.EndsWith(thisLowerName))
                            {
                                continue;
                            }

                            // マテリアル名がモデルファイル名のmdl_をmtl_に置換したものと一致する
                            // Race場の天候・時間対応
                            if (!thisFileLowerName.Contains(materialFilePrefixLowerName))
                            {
                                continue;
                            }

                            Material thisTex = AssetDatabase.LoadAssetAtPath<Material>(filePathList[q]);

                            if (thisTex == null)
                            {
                                continue;
                            }

                            targetMaterial = thisTex;
                            break;
                        }

                        if (targetMaterial != null)
                        {
                            stringObjectPair.Value = targetMaterial;
                        }
                    }
                }

                stringObjectPair.Key = thisRealKey;

                if (!thisKeep)
                {
                    if (stringObjectPair.Value == null)
                    {
                        removePairList.Add(stringObjectPair);
                    }
                }
            }

            for (int p = 0; p < removePairList.Count; p++)
            {
                assetHolder.ObjectList.Remove(removePairList[p]);
            }

            if (assetHolder.ValueList.Count == 0)
            {
                return;
            }

            // 値を処理する前のAssetHolder
            AssetHolder preAssetHolder = _main.PrePrefabInstanceObject.GetComponent<AssetHolder>();

            for (int i = 0; i < assetHolder.ValueList.Count; i++)
            {
                AssetHolder.StringValuePair stringValuePair = assetHolder.ValueList[i];
                if (stringValuePair == null || stringValuePair.Key == null)
                {
                    continue;
                }

                string thisKey = stringValuePair.Key;
                if (!thisKey.Contains("____"))
                {
                    continue;
                }

                string[] splitStr = thisKey.Split(new string[] { "____" }, System.StringSplitOptions.None);
                if (splitStr.Length != 2)
                {
                    continue;
                }

                string thisRealKey = splitStr[0];
                splitStr = splitStr[1].Split(new string[] { "__" }, System.StringSplitOptions.None);
                if (splitStr.Length != 2)
                {
                    continue;
                }

                string flagKey = splitStr[0];
                string flagValue = splitStr[1];

                if (flagKey == "keep" && flagValue == "1" && preAssetHolder != null)
                {
                    for (int j = 0; j < preAssetHolder.ValueList.Count; j++)
                    {
                        AssetHolder.StringValuePair preStringValuePair = preAssetHolder.ValueList[j];
                        if (preStringValuePair == null || preStringValuePair.Key == null)
                        {
                            continue;
                        }

                        string thisPreKey = preStringValuePair.Key;
                        if (thisPreKey == string.Empty)
                        {
                            continue;
                        }

                        if (thisRealKey != thisPreKey)
                        {
                            continue;
                        }

                        float thisPreValue = preStringValuePair.Value;
                        stringValuePair.Value = thisPreValue;
                    }
                }

                stringValuePair.Key = thisRealKey;
            }
        }

        public void SetHeadSkinRootBone()
        {
            SetSkinRootBone("^M_Mayu", "^Head");
            SetSkinRootBone("^M_Cheek", "^Head");
        }

        public void SetSkinRootBone(string targetMeshName, string targetBoneName)
        {
            GameObject targetMeshObject = null;
            GameObject targetBoneObject = null;

            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            GameObject[] gameObjects = _main.PrefabInstanceObject.GetChildren();
            foreach (GameObject gameObject in gameObjects)
            {
                if (targetMeshObject != null && targetBoneObject != null)
                {
                    break;
                }

                if (Regex.IsMatch(gameObject.name, targetMeshName))
                {
                    targetMeshObject = gameObject;
                    continue;
                }

                if (Regex.IsMatch(gameObject.name, targetBoneName))
                {
                    targetBoneObject = gameObject;
                    continue;
                }
            }

            if (targetMeshObject == null || targetBoneObject == null)
            {
                return;
            }

            SkinnedMeshRenderer targetMeshSkinRenderer = targetMeshObject.GetComponentInChildren<SkinnedMeshRenderer>();
            if (targetMeshSkinRenderer == null)
            {
                return;
            }

            if (Regex.IsMatch(targetMeshSkinRenderer.gameObject.name, targetBoneName))
            {
                return;
            }

            var diffPosition = targetMeshSkinRenderer.rootBone.position - targetBoneObject.transform.position;
            Bounds thisBounds = targetMeshSkinRenderer.localBounds;
            thisBounds.center = targetMeshSkinRenderer.bounds.center + diffPosition;
            targetMeshSkinRenderer.localBounds = thisBounds;
            targetMeshSkinRenderer.rootBone = targetBoneObject.transform;
        }

        /// -----------------------------------------
        /// <summary>
        /// SetBodyBounds
        /// </summary>
        /// -----------------------------------------
        public void SetBodyBounds()
        {
            SetBoundsBase("^M_Body$", new Vector3(float.MinValue, 0.1f, float.MinValue), new Vector3(1.5f, 2.0f, 5.0f));
        }

        /// -----------------------------------------
        /// <summary>
        /// SetHeadBounds
        /// </summary>
        /// -----------------------------------------
        public void SetHeadBounds()
        {
            SetBoundsBase("^M_Hair$", new Vector3(float.MinValue, float.MinValue, -0.3f), new Vector3(2.0f, 1.0f, 4.0f));
            SetBoundsBase("^M_Face$", new Vector3(float.MinValue, float.MinValue, float.MinValue), new Vector3(1.5f, 1.5f, 1.5f));
            SetBoundsBase("^M_Mayu$", new Vector3(float.MinValue, float.MinValue, float.MinValue), new Vector3(1.5f, 3.0f, 1.5f));
            SetBoundsBase("^M_Cheek$", new Vector3(float.MinValue, float.MinValue, float.MinValue), new Vector3(1.5f, 1.5f, 1.5f));
            SetBoundsBase("^M_Obj[0-9]$", new Vector3(float.MinValue, float.MinValue, float.MinValue), new Vector3(1.5f, 1.5f, 1.5f));
        }

        /// -----------------------------------------
        /// <summary>
        /// SetPropBounds
        /// </summary>
        /// -----------------------------------------
        public void SetPropBounds()
        {
            SetLongestExtentBounds("^M_Prop$", new Vector3(float.MinValue, float.MinValue, float.MinValue), 3.0f);
        }

        /// -----------------------------------------
        /// <summary>
        /// SetBoundsBase
        /// </summary>
        /// -----------------------------------------
        private void SetBoundsBase(string targetMeshName, Vector3 center, Vector3 scale)
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            SkinnedMeshRenderer[] skinMeshRendererList = _main.PrefabInstanceObject.GetComponentsInChildren<SkinnedMeshRenderer>(true);

            for (int p = 0; p < skinMeshRendererList.Length; p++)
            {
                SkinnedMeshRenderer thisRenderer = skinMeshRendererList[p];

                if (!Regex.IsMatch(thisRenderer.gameObject.name, targetMeshName))
                {
                    continue;
                }

                Bounds thisBounds = thisRenderer.localBounds;

                Vector3 resultCenter = thisBounds.center;
                Vector3 resultExtent = thisBounds.extents;

                if (center.x != float.MinValue)
                {
                    resultCenter.x = center.x;
                }

                if (center.y != float.MinValue)
                {
                    resultCenter.y = center.y;
                }

                if (center.z != float.MinValue)
                {
                    resultCenter.z = center.z;
                }

                if (scale.x != 1.0f)
                {
                    resultExtent.x = thisBounds.extents.x * scale.x;
                }

                if (scale.y != 1.0f)
                {
                    resultExtent.y = thisBounds.extents.y * scale.y;
                }

                if (scale.z != 1.0f)
                {
                    resultExtent.z = thisBounds.extents.z * scale.z;
                }

                thisBounds.center = resultCenter;
                thisBounds.extents = resultExtent;

                thisRenderer.localBounds = thisBounds;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// SetHeadCenterOffset
        /// </summary>
        /// -----------------------------------------
        public void SetHeadCenterOffset()
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            Transform[] allTransformList = _main.PrefabInstanceObject.GetComponentsInChildren<Transform>();
            AssetHolder assetHolder = _main.PrefabInstanceObject.GetComponent<AssetHolder>();

            if (allTransformList == null)
            {
                return;
            }

            if (assetHolder == null)
            {
                return;
            }

            // headCenterを示すロケーター名
            string headCenterLocatorName = "Head_center_offset";            // 髪の球状法線中心
            string headTubeCenterLocatorName = "Head_tube_center_offset";   // 顔の円柱状法線中心

            // AssetHolderから探してくるパラメーター
            string headCenterOffsetYKey = "head_center_offset_y";
            string headCenterOffsetZKey = "head_center_offset_z";
            string headTubeCenterOffsetZKey = "head_tube_center_offset_z";

            Transform headCenterLocator = null;
            Transform headTubeCenterLocator = null;

            // ロケーターのトランスフォームを取得
            for (int i = 0; i < allTransformList.Length; i++)
            {
                if (allTransformList[i].name == headCenterLocatorName)
                {
                    headCenterLocator = allTransformList[i];
                }
                else if (allTransformList[i].name == headTubeCenterLocatorName)
                {
                    headTubeCenterLocator = allTransformList[i];
                }
            }

            if (headCenterLocator == null && headTubeCenterLocator == null)
            {
                return;
            }

            // オフセット値をAssetHolderにセット
            for (int i = 0; i < assetHolder.ValueList.Count; i++)
            {
                AssetHolder.StringValuePair stringValuePair = assetHolder.ValueList[i];

                if (stringValuePair.Key == headCenterOffsetYKey && headCenterLocator != null)
                {
                    stringValuePair.Value = headCenterLocator.localPosition.y;
                }
                else if (stringValuePair.Key == headCenterOffsetZKey && headCenterLocator != null)
                {
                    stringValuePair.Value = headCenterLocator.localPosition.z;
                }
                else if (stringValuePair.Key == headTubeCenterOffsetZKey && headTubeCenterLocator != null)
                {
                    stringValuePair.Value = headTubeCenterLocator.localPosition.z;
                }
            }

            // ロケーターを削除
            if (headCenterLocator)
            {
                GameObject.DestroyImmediate(headCenterLocator.gameObject, true);
            }

            if (headTubeCenterLocator)
            {
                GameObject.DestroyImmediate(headTubeCenterLocator.gameObject, true);
            }

        }

        /// -----------------------------------------
        /// <summary>
        /// SetMaterialPropertyFromLocator
        /// </summary>
        /// -----------------------------------------
        public void SetMaterialPropertyFromLocator(string target_property)
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            GameObject[] allObjectArray = _main.PrefabInstanceObject.GetChildren();
            Transform[] allTransformArray = _main.PrefabInstanceObject.GetComponentsInChildren<Transform>();

            List<GameObject> destroyObjectList = new List<GameObject>();

            if (allObjectArray == null || allTransformArray == null)
            {
                return;
            }

            for (int i = 0; i < allObjectArray.Length; i++)
            {

                List<string[]> materialLocatorNamePairList = GetMaterialLocatorPairList(_main.PrefabInstanceObject, allObjectArray[i], target_property);

                if (materialLocatorNamePairList.Count == 0)
                {
                    continue;
                }

                for (int j = 0; j < materialLocatorNamePairList.Count; j++)
                {
                    string targetMaterialName = materialLocatorNamePairList[j][0];
                    string targetLocatorName = materialLocatorNamePairList[j][1];

                    Material targetMaterial = GetMaterialByName(allObjectArray[i], targetMaterialName);
                    Transform targetLocator = null;

                    if (targetMaterial == null)
                    {
                        continue;
                    }

                    for (int k = 0; k < allTransformArray.Length; k++)
                    {
                        if (allTransformArray[k].name == targetLocatorName)
                        {
                            targetLocator = allTransformArray[k];
                            break;
                        }
                    }

                    if (targetLocator)
                    {
                        // 値の設定後削除するので、削除リストに追加しておく
                        destroyObjectList.Add(targetLocator.gameObject);
                    }
                    else
                    {
                        continue;
                    }

                    if (targetMaterial.HasProperty(target_property))
                    {
                        if (target_property == PROPERTY_SPEC_COLOR)
                        {
                            Color locatorColor = new Color(1.0f, 1.0f, 1.0f);
                            locatorColor.r = Mathf.Clamp01(targetLocator.localScale.x);
                            locatorColor.g = Mathf.Clamp01(targetLocator.localScale.y);
                            locatorColor.b = Mathf.Clamp01(targetLocator.localScale.z);
                            targetMaterial.SetColor(target_property, locatorColor);
                        }
                        else if (target_property == PROPERTY_REFRECTION_ADD)
                        {
                            Color locatorColor = new Color(1.0f, 1.0f, 1.0f);
                            locatorColor.r = Mathf.Clamp01(targetLocator.localScale.x);
                            locatorColor.g = Mathf.Clamp01(targetLocator.localScale.y);
                            locatorColor.b = Mathf.Clamp01(targetLocator.localScale.z);
                            targetMaterial.SetColor(target_property, locatorColor);
                        }
                        else if (target_property == PROPERTY_REFLECTION_MUL)
                        {
                            Color locatorColor = new Color(1.0f, 1.0f, 1.0f);
                            locatorColor.r = Mathf.Clamp01(targetLocator.localScale.x);
                            locatorColor.g = Mathf.Clamp01(targetLocator.localScale.y);
                            locatorColor.b = Mathf.Clamp01(targetLocator.localScale.z);
                            targetMaterial.SetColor(target_property, locatorColor);
                        }
                        else if (target_property == PROPERTY_REFLECTION_POW)
                        {
                            // 指数をmaxで制限することにより0の0乗を回避する
                            var pow = Mathf.Max(0.0001f, targetLocator.localScale.x);
                            targetMaterial.SetFloat(target_property, pow);
                        }
                    }
                }
            }

            for (int i = 0; i < destroyObjectList.Count; i++)
            {
                GameObject.DestroyImmediate(destroyObjectList[i], true);
            }
        }

        /// <summary>
        /// SetUseSpring
        /// </summary>
        private void SetUseCySpring()
        {

            AssetHolder assetHolder = _main.PrefabInstanceObject.GetComponent<AssetHolder>();

            string setUseCySpringKey = "use_cyspring";

            string prefabDirPath = Path.GetDirectoryName(_main.PrefabFilePath);
            string clothDirPath = prefabDirPath + "\\Clothes";
            if (!Directory.Exists(clothDirPath))
            {
                return;
            }

            for (int i = 0; i < assetHolder.ValueList.Count; i++)
            {
                AssetHolder.StringValuePair stringValuePair = assetHolder.ValueList[i];

                if (stringValuePair.Key == setUseCySpringKey)
                {
                    stringValuePair.Value = 1;
                    break;
                }
            }

        }

        /// <summary>
        /// KeepAssetValues
        /// </summary>
        private void KeepAssetTableValues(string prefix)
        {
            AssetHolder assetHolder = _main.PrefabInstanceObject.GetComponent<AssetHolder>();
            AssetHolder prePrefabAssetHolder = _main.PrePrefabInstanceObject.GetComponent<AssetHolder>();

            if (prePrefabAssetHolder == null)
            {
                return;
            }

            for (int i = 0; i < prePrefabAssetHolder.ValueList.Count; i++)
            {
                AssetHolder.StringValuePair stringValuePair = prePrefabAssetHolder.ValueList[i];

                if (stringValuePair.Key.StartsWith(prefix))
                {
                    for (int y = 0; y < assetHolder.ValueList.Count; y++)
                    {
                        if (assetHolder.ValueList[y].Key == stringValuePair.Key)
                        {
                            assetHolder.ValueList.RemoveAt(y);
                            break;
                        }
                    }
                    assetHolder.ValueList.Add(stringValuePair);
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// GetMaterialSpecLocatorNamePairList
        /// </summary>
        /// -----------------------------------------
        private List<string[]> GetMaterialLocatorPairList(GameObject targetPrefab, GameObject targetObject, string target_property)
        {
            List<string[]> resultList = new List<string[]>(); ;

            string replaceBaseName = targetPrefab.name;
            string targetObjectName = targetObject.name;



            // モブと汎用衣装の差分IDをカット
            Match match = Regex.Match(replaceBaseName, "pfb_(bdy|chr)0[0-9]{3}_[0-9]{2}");

            if (match.Success)
            {
                replaceBaseName = match.Value;
            }

            // ロケーターの接尾語をプロパティー名から決定
            string locatorSuffix = null;

            if (target_property == PROPERTY_SPEC_COLOR)
            {
                locatorSuffix = LOCATOR_SPEC_SUFFIX;
            }
            else if (target_property == PROPERTY_REFRECTION_ADD)
            {
                locatorSuffix = LOCATOR_REFLECTION_ADD_SUFFIX;
            }
            else if (target_property == PROPERTY_REFLECTION_MUL)
            {
                locatorSuffix = LOCATOR_REFLECTION_MUL_SUFFIX;
            }
            else if (target_property == PROPERTY_REFLECTION_POW)
            {
                locatorSuffix = LOCATOR_REFLECTION_POW_SUFFIX;
            }

            // マテリアルのベースネームを決定
            string materialBaseName = replaceBaseName.Replace(PREFAB_PREFIX, MATERIAL_PREFIX);

            // メッシュに対応するマテリアル名とロケーター名を決定
            string targetMaterialName = null;
            string targetLocatorName = null;

            if (targetObjectName == "M_Face")
            {
                targetMaterialName = materialBaseName + FACE_MATERIAL_SUFFIX;
                targetLocatorName = LOCATOR_FACE_PREFIX + locatorSuffix;
            }
            else if (targetObjectName == "M_Hair")
            {
                targetMaterialName = materialBaseName + HAIR_MATERIAL_SUFFIX;
                targetLocatorName = LOCATOR_HAIR_PREFIX + locatorSuffix;
            }
            else if (targetObjectName == "M_Body")
            {
                targetMaterialName = materialBaseName;
                targetLocatorName = LOCATOR_BODY_PREFIX + locatorSuffix;
            }
            else if (targetObjectName == "M_Tail")
            {
                targetMaterialName = materialBaseName;
                targetLocatorName = LOCATOR_TAIL_PREFIX + locatorSuffix;
            }
            else if (targetObjectName == "M_ToonProp")
            {
                targetMaterialName = materialBaseName;
                targetLocatorName = LOCATOR_TOON_PROP_PREFIX + locatorSuffix;
            }

            if (targetMaterialName == null || targetLocatorName == null)
            {
                return resultList;
            }

            // namePairの作成とリストへの追加
            if (replaceBaseName.Contains("pfb_bdy0001_00"))
            {
                int materialCount = 3; // 体操服は_0, _1, _2 の3マテリアル

                for (int i = 0; i < materialCount; i++)
                {
                    string thisSuffix = "_" + i.ToString();
                    string[] materialLocatorNamePair = { targetMaterialName + thisSuffix, targetLocatorName + thisSuffix };
                    resultList.Add(materialLocatorNamePair);
                }
            }
            else
            {
                string[] materialLocatorNamePair = { targetMaterialName, targetLocatorName };
                resultList.Add(materialLocatorNamePair);
            }

            return resultList;
        }

        /// -----------------------------------------
        /// <summary>
        /// GetMaterialByName
        /// </summary>
        /// -----------------------------------------
        private Material GetMaterialByName(GameObject targetGameObject, string targetMaterialName)
        {
            Material resultMaterial = null;

            Renderer targetRenderer = targetGameObject.GetComponent<Renderer>();

            if (targetRenderer == null)
            {
                return null;
            }

            Material[] allMaterials = targetRenderer.sharedMaterials;

            if (allMaterials == null)
            {
                return null;
            }

            for (int i = 0; i < allMaterials.Length; i++)
            {
                if (allMaterials[i].name == targetMaterialName)
                {
                    resultMaterial = allMaterials[i];
                    break;
                }
            }

            return resultMaterial;
        }

        private void SetPrefabBounds()
        {
            GameObject targetMeshObject = null;
            GameObject targetBoneObject = null;

            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            GameObject[] gameObjects = _main.PrefabInstanceObject.GetChildren();

            GameObject defaultRootBone = null;
            foreach (GameObject gameObject in gameObjects)
            {
                SkinnedMeshRenderer skinnedMeshRenderer = gameObject.GetComponentInChildren<SkinnedMeshRenderer>();
                if (skinnedMeshRenderer == null)
                {
                    continue;
                }

                // デフォルトだとskinnedMeshRendererの最初のヒットが最浅骨のはず
                defaultRootBone = skinnedMeshRenderer.rootBone.gameObject;
                break;
            }

            if (defaultRootBone == null)
            {
                return;
            }

            GameObject[] defaultRootBoneChildren = defaultRootBone.GetChildren();
            foreach (GameObject defaultRootBoneChildObject in defaultRootBoneChildren)
            {
                string matchName = defaultRootBoneChildObject.name + "$";
                foreach (GameObject gameObject in gameObjects)
                {
                    SkinnedMeshRenderer skinnedMeshRenderer = gameObject.GetComponentInChildren<SkinnedMeshRenderer>();
                    if (skinnedMeshRenderer == null)
                    {
                        continue;
                    }

                    if (Regex.IsMatch(gameObject.name, matchName))
                    {
                        targetMeshObject = gameObject;
                        targetBoneObject = defaultRootBoneChildObject;

                        Vector3 boundsMax = skinnedMeshRenderer.localBounds.size;
                        var maxValue = GetMaxValueInVector(boundsMax, 0.10f);
                        Bounds bounds = new Bounds(Vector3.zero, new Vector3(maxValue, maxValue, maxValue) * 5);
                        skinnedMeshRenderer.localBounds = bounds;
                        skinnedMeshRenderer.rootBone = targetBoneObject.transform;
                    }
                }
            }
        }

        private float GetMaxValueInVector(Vector3 vector, float minValue)
        {
            float maxValue = float.MinValue;

            maxValue = maxValue < vector.x ? vector.x : maxValue;
            maxValue = maxValue < vector.y ? vector.y : maxValue;
            maxValue = maxValue < vector.z ? vector.z : maxValue;
            maxValue = maxValue < minValue ? minValue : maxValue;

            return maxValue;
        }

        /// -----------------------------------------
        /// <summary>
        /// SetLongestExtentBounds
        /// </summary>
        /// -----------------------------------------
        private void SetLongestExtentBounds(string targetMeshName, Vector3 center, float scale)
        {
            if (_main.PrefabInstanceObject == null)
            {
                return;
            }

            SkinnedMeshRenderer[] skinMeshRendererList = _main.PrefabInstanceObject.GetComponentsInChildren<SkinnedMeshRenderer>(true);

            for (int p = 0; p < skinMeshRendererList.Length; p++)
            {
                SkinnedMeshRenderer thisRenderer = skinMeshRendererList[p];

                if (!Regex.IsMatch(thisRenderer.gameObject.name, targetMeshName))
                {
                    continue;
                }

                Bounds thisBounds = thisRenderer.localBounds;
                Vector3 thisExtent = thisBounds.extents;

                float resultExtent = 0.0f;

                // 最長のextentにスケールを入れたものを最終的なextentにする
                if (thisExtent.x > resultExtent)
                {
                    resultExtent = thisExtent.x;
                }
                if (thisExtent.y > resultExtent)
                {
                    resultExtent = thisExtent.y;
                }
                if (thisExtent.z > resultExtent)
                {
                    resultExtent = thisExtent.z;
                }

                resultExtent = resultExtent * scale;

                Vector3 resultCenter = thisBounds.center;

                if (center.x != float.MinValue)
                {
                    resultCenter.x = center.x;
                }

                if (center.y != float.MinValue)
                {
                    resultCenter.y = center.y;
                }

                if (center.z != float.MinValue)
                {
                    resultCenter.z = center.z;
                }

                thisBounds.center = resultCenter;
                thisBounds.extents = new Vector3(resultExtent, resultExtent, resultExtent);

                thisRenderer.localBounds = thisBounds;
            }
        }


        /// -----------------------------------------
        /// <summary>
        /// MatchPrefabName
        /// </summary>
        /// -----------------------------------------
        private bool MatchPrefabName(string targetName)
        {
            var targetRegexList = targetName.Split(',');
            foreach (var targetRegex in targetRegexList)
            {
                if (Regex.IsMatch(_main.PrefabFileName, targetRegex))
                {
                    return true;
                }
            }

            return false;
        }


        /// -----------------------------------------
        /// <summary>
        /// SetChildLayerSameParent
        /// </summary>
        /// -----------------------------------------
        private void SetChildLayerSameParent(string targetName)
        {
            if (!MatchPrefabName(targetName))
            {
                return;
            }

            var activeLayer = _main.PrefabInstanceObject.layer;
            SetLayerRecursively(_main.PrefabInstanceObject, activeLayer);
        }

        private void SetLayerRecursively(GameObject gObj, int layer)
        {
            gObj.layer = layer;

            foreach (Transform n in gObj.transform)
            {
                SetLayerRecursively(n.gameObject, layer);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// SetJukeBox
        /// </summary>
        /// -----------------------------------------
        private void SetJukeBox(string targetName)
        {
            if (!MatchPrefabName(targetName) || !_main.PrePrefabInstanceObject || !_main.PrefabInstanceObject)
            {
                return;
            }

            HomeJukebox orgHomeJukeBoxComp = _main.PrePrefabInstanceObject.GetComponentInChildren<HomeJukebox>(true);
            HomeJukebox tgtHomeJukeBoxComp = _main.PrefabInstanceObject.GetComponentInChildren<HomeJukebox>(true);
            if (!orgHomeJukeBoxComp || !tgtHomeJukeBoxComp)
            {
                return;
            }

            GameObject[] targetObjectList = _main.PrefabInstanceObject.GetChildren();
            SetJukeBoxGameObject(
                tgtHomeJukeBoxComp,
                targetObjectList,
                orgHomeJukeBoxComp.BodyMesh != null ? orgHomeJukeBoxComp.BodyMesh.name : "",
                orgHomeJukeBoxComp.JacketMesh != null ? orgHomeJukeBoxComp.JacketMesh.name : "",
                orgHomeJukeBoxComp.ScrollEmissiveMesh != null ? orgHomeJukeBoxComp.ScrollEmissiveMesh.name : "",
                orgHomeJukeBoxComp.ScrollLedMesh != null ? orgHomeJukeBoxComp.ScrollLedMesh.name : "",
                orgHomeJukeBoxComp.ButtonLightMesh != null ? orgHomeJukeBoxComp.ButtonLightMesh.name : "",
                orgHomeJukeBoxComp.Animator != null ? orgHomeJukeBoxComp.Animator.name : ""
            );
        }

        /// -----------------------------------------
        /// <summary>
        /// SetMiniJukeBox
        /// </summary>
        /// -----------------------------------------
        private void SetMiniJukeBox(string targetName, List<string> targetPathList)
        {
            if (!MatchPrefabName(targetName))
            {
                return;
            }

            if (targetPathList.Count < 2)
            {
                return;
            }

            // ターゲットは現在のpfb_mini_jukebox
            var tgtMiniJukeBoxObject = AssetDatabase.LoadAssetAtPath<GameObject>(targetPathList[0]);
            if (!tgtMiniJukeBoxObject)
            {
                return;
            }
            HomeJukebox homeJukeboxComp = tgtMiniJukeBoxObject.GetComponent<HomeJukebox>();
            if (!homeJukeboxComp)
            {
                return;
            }

            // 対象のgameObject(メッシュを取得してセットする先)は更新後のaddprop003_000を利用する
            var tgtGameObject = AssetDatabase.LoadAssetAtPath<GameObject>(targetPathList[1]);
            if (!tgtGameObject)
            {
                return;
            }
            GameObject[] targetObjectList = tgtGameObject.GetChildren();

            HomeJukeBoxComponentName homeJukeBoxMeshComponentName = null;
            foreach (var obj in _main.TmpProcessObjList)
            {
                if (typeof(HomeJukeBoxComponentName).Equals(obj.GetType()))
                {
                    homeJukeBoxMeshComponentName = (HomeJukeBoxComponentName)obj;
                    break;
                }
            }
            if (homeJukeBoxMeshComponentName == null)
            {
                return;
            }
            SetJukeBoxGameObject(
                homeJukeboxComp,
                targetObjectList,
                homeJukeBoxMeshComponentName.BodyMesh,
                homeJukeBoxMeshComponentName.JacketMesh,
                homeJukeBoxMeshComponentName.ScrollEmissiveMesh,
                homeJukeBoxMeshComponentName.ScrollLedMesh,
                homeJukeBoxMeshComponentName.ButtonLightMesh,
                homeJukeBoxMeshComponentName.Animator
            );
        }


        /// -----------------------------------------
        /// <summary>
        /// SetJukeBoxGameObject
        /// </summary>
        /// -----------------------------------------
        private void SetJukeBoxGameObject(
            HomeJukebox homeJukebox,
            GameObject[] targetObjectList,
            string bodyMeshName,
            string jacketMeshName,
            string scrollEmissiveMeshName,
            string scrollLedMeshName,
            string buttonLightMeshName,
            string animatorName
        )
        {
            if (targetObjectList.IsNullOrEmpty())
            {
                return;
            }

            MeshRenderer tmpBodyMesh = GetSameNameMeshRenderer(targetObjectList, bodyMeshName);
            if (tmpBodyMesh)
            {
                homeJukebox.BodyMesh = tmpBodyMesh;
            }

            MeshRenderer tmpJacketMesh = GetSameNameMeshRenderer(targetObjectList, jacketMeshName);
            if (tmpJacketMesh)
            {
                homeJukebox.JacketMesh = tmpJacketMesh;
            }

            MeshRenderer tmpScrollEmissiveMesh = GetSameNameMeshRenderer(targetObjectList, scrollEmissiveMeshName);
            if (tmpScrollEmissiveMesh)
            {
                homeJukebox.ScrollEmissiveMesh = tmpScrollEmissiveMesh;
            }

            MeshRenderer tmpScrollLedMesh = GetSameNameMeshRenderer(targetObjectList, scrollLedMeshName);
            if (tmpScrollLedMesh)
            {
                homeJukebox.ScrollLedMesh = tmpScrollLedMesh;
            }

            MeshRenderer tmpButtonLightMesh = GetSameNameMeshRenderer(targetObjectList, buttonLightMeshName);
            if (tmpButtonLightMesh)
            {
                homeJukebox.ButtonLightMesh = tmpButtonLightMesh;
            }

            Animator tmpAnimator = GetSameNameAnimator(animatorName);
            if (tmpAnimator)
            {
                homeJukebox.Animator = tmpAnimator;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// GetSameNameMeshRenderer
        /// </summary>
        /// -----------------------------------------
        private MeshRenderer GetSameNameMeshRenderer(GameObject[] targetObjectList, string meshRendererName)
        {
            if (meshRendererName == "")
            {
                return null;
            }

            foreach (var targetObject in targetObjectList)
            {
                var targetObjectMeshRender = targetObject.GetComponent<MeshRenderer>();
                if (!targetObjectMeshRender || targetObjectMeshRender.name != meshRendererName)
                {
                    continue;
                }

                return targetObjectMeshRender;
            }
            return null;
        }

        private Animator GetSameNameAnimator(string targetName)
        {
            Animator animator = null;

            string[] animatorsGuidList = AssetDatabase.FindAssets(targetName);
            if (animatorsGuidList.Length > 0)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(animatorsGuidList[0]);
                GameObject gameObject = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                if (gameObject)
                {
                    animator = gameObject.GetComponent<Animator>();
                }
            }
            return animator;
        }

        /// -----------------------------------------
        /// <summary>
        /// SetHomeJukeBoxMeshNameObj
        /// </summary>
        /// -----------------------------------------
        private void SetHomeJukeBoxMeshNameObj(string targetName, List<string> targetPathList)
        {
            if (!MatchPrefabName(targetName))
            {
                return;
            }


            if (targetPathList.IsNullOrEmpty())
            {
                return;
            }

            GameObject pfbMiniJukeboxObj = AssetDatabase.LoadAssetAtPath<GameObject>(targetPathList[0]);
            if (!pfbMiniJukeboxObj)
            {
                return;
            }

            HomeJukebox homeJukeboxObj = pfbMiniJukeboxObj.GetComponent<HomeJukebox>();
            if (!homeJukeboxObj)
            {
                return;
            }

            HomeJukeBoxComponentName homeJukeBoxComponentName = new HomeJukeBoxComponentName(
                homeJukeboxObj.BodyMesh != null ? homeJukeboxObj.BodyMesh.name : "",
                homeJukeboxObj.JacketMesh != null ? homeJukeboxObj.JacketMesh.name : "",
                homeJukeboxObj.ScrollEmissiveMesh != null ? homeJukeboxObj.ScrollEmissiveMesh.name : "",
                homeJukeboxObj.ScrollLedMesh != null ? homeJukeboxObj.ScrollLedMesh.name : "",
                homeJukeboxObj.ButtonLightMesh != null ? homeJukeboxObj.ButtonLightMesh.name : "",
                homeJukeboxObj.Animator != null ? homeJukeboxObj.Animator.name : ""
            );

            _main.TmpProcessObjList = new List<object>() { homeJukeBoxComponentName };
        }

        private class HomeJukeBoxComponentName
        {
            public string BodyMesh = "";
            public string JacketMesh = "";
            public string ScrollEmissiveMesh = "";
            public string ScrollLedMesh = "";
            public string ButtonLightMesh = "";
            public string Animator = "";

            public HomeJukeBoxComponentName(string bodyMesh, string jacketMesh, string scrollEmissiveMesh, string scrollLedMesh, string buttonLightMesh, string animator)
            {
                BodyMesh = bodyMesh;
                JacketMesh = jacketMesh;
                ScrollEmissiveMesh = scrollEmissiveMesh;
                ScrollLedMesh = scrollLedMesh;
                ButtonLightMesh = buttonLightMesh;
                Animator = animator;
            }
        }

        public static IEnumerable<FieldInfo> GetAllFields(System.Type t)
        {
            if (t == null)
            {
                return Enumerable.Empty<FieldInfo>();
            }

            BindingFlags flags = BindingFlags.Public | BindingFlags.NonPublic |
                                    BindingFlags.Static | BindingFlags.Instance |
                                    BindingFlags.DeclaredOnly;
            return t.GetFields(flags).Concat(GetAllFields(t.BaseType));
        }

        private void KeepScriptComponentsInChildGameObjects(Dictionary<string, List<string>> targetDict)
        {
            Transform[] prePrefabTransforms = _main.PrePrefabInstanceObject.GetComponentsInChildren<Transform>();
            Transform[] prefabTransforms = _main.PrefabInstanceObject.GetComponentsInChildren<Transform>();
            if (prePrefabTransforms.Length < 1 || prefabTransforms.Length < 1)
            {
                return;
            }

            List<string> targetComponentTypeList = null;

            foreach (string targetName in targetDict.Keys)
            {
                if (_main.PrePrefabInstanceObject.name.Contains(targetName))
                {
                    targetComponentTypeList = targetDict[targetName];
                    break;
                }
            }

            if (targetComponentTypeList.IsNull())
            {
                return;
            }

            foreach (var prePrefabTransform in prePrefabTransforms)
            {

                GameObject srcGameObject = prePrefabTransform.gameObject;
                GameObject dstGameObject = null;
                foreach (var prefabTransfrom in prefabTransforms)
                {
                    var prefabGameObject = prefabTransfrom.gameObject;
                    if (srcGameObject.name == prefabGameObject.name)
                    {
                        dstGameObject = prefabGameObject;
                        break;
                    }
                }

                if (dstGameObject == null)
                {
                    continue;
                }

                Component[] components = srcGameObject.GetComponents<Component>();
                foreach (Component component in components)
                {
                    Type type = component.GetType();
                    Component targetComponent = null;
                    foreach (string targetComponentType in targetComponentTypeList)
                    {
                        if (type.ToString() == targetComponentType)
                        {
                            targetComponent = component;
                            break;
                        }
                    }

                    if (targetComponent == null)
                    {
                        continue;
                    }

                    var dst = dstGameObject.GetComponent(type);
                    if (!dst)
                    {
                        dstGameObject.AddComponent(type);
                    }

                    var fields = GetAllFields(type);
                    foreach (var field in fields)
                    {
                        if (field.IsStatic || !(field.IsPublic))
                        {
                            continue;
                        }

                        try
                        {
                            field.SetValue(dst, field.GetValue(component));
                        }
                        catch
                        {

                        }
                    }

                    var props = type.GetProperties();
                    foreach (var prop in props)
                    {
                        if (!prop.CanWrite || !prop.CanWrite || prop.Name == "name")
                        {
                            continue;
                        }

                        try
                        {
                            prop.SetValue(dst, prop.GetValue(component, null), null);
                        }
                        catch
                        {

                        }
                    }
                }
            }
        }
    }
}
#endif // CYG_DEBUG && UNITY_EDITOR //