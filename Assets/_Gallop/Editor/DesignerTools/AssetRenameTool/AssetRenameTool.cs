#if CYG_DEBUG && UNITY_EDITOR

using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using UnityEditor.IMGUI.Controls;
using static UnityEditor.IMGUI.Controls.MultiColumnHeaderState;

namespace App.Editor.DesignerTools.AssetRenameTool
{
    /// <summary>
    /// リネーム設定
    /// </summary>
    public class AssetRenameSettings
    {
        private RegexOptions _regexOptions;
        private bool _useRegex;
        private string _searchString;
        private string _replaceString;
        private string _searchPattern;
        private string _replacePattern;
        private bool _canReplace;

        public bool IsReplace { get; set; }

        public bool IgnoreCase
        {
            get => _regexOptions.HasFlag(RegexOptions.IgnoreCase);
            set => SetRegexOptions(RegexOptions.IgnoreCase, value);
        }

        public bool UseRegex
        {
            get => _useRegex;
            set
            {
                _useRegex = value;
                UpdatePatterns();
            }
        }

        public string SearchString
        {
            get => _searchString;
            set
            {
                _searchString = value;
                UpdatePatterns();
            }
        }

        public string ReplaceString
        {
            get => _replaceString;
            set
            {
                _replaceString = value;
                UpdatePatterns();
            }
        }

        private void UpdatePatterns()
        {
            _searchPattern = GetPattern(_searchString);
            _replacePattern = GetPattern(_replaceString);

            _canReplace = !String.IsNullOrEmpty(_searchPattern) && _replacePattern != null;
        }

        private string GetPattern(string str)
        {
            if (String.IsNullOrEmpty(str))
            {
                return String.Empty;
            }

            if (!_useRegex)
            {
                return Regex.Escape(str);
            }

            if (ValidateRegex(str))
            {
                return str;
            }

            return null;
        }

        private void SetRegexOptions(RegexOptions flag, bool on)
        {
            if (on)
            {
                _regexOptions |= flag;
            }
            else
            {
                _regexOptions &= ~flag;
            }
        }

        private static bool ValidateRegex(string pattern)
        {
            try
            {
                new Regex(pattern);
            }
            catch (ArgumentException)
            {
                return false;
            }

            return true;
        }

        public string Rename(string input)
        {
            var result = input;

            if (IsReplace && _canReplace)
            {
                result = Regex.Replace(result, _searchPattern, _replacePattern, _regexOptions);
            }

            return result;
        }
    }

    /// <summary>
    /// リネーム用アセットパス
    /// </summary>
    public class AssetPath
    {
        private AssetRenameSettings _settings;
        private UnityEngine.Object _targetObj = null;
        private bool _hasNoGuidObj = false;
        private string _guid;
        private string _path;
        private string _directory;
        private string _name;
        private string _newName;
        private bool _hasDifference;

        public string Directory => _directory;
        public string Name => _name;
        public string NewName => _newName;
        public bool HasDifference => _hasDifference;
        public bool Enabled { get; set; } = true;

        public AssetPath(string guid)
        {
            _guid = guid;
        }

        public AssetPath(UnityEngine.Object asset)
        {
            string guid = AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(asset));

            if (guid == "")
            {
                // ヒエラルキー上のオブジェクトなどはGUIDがないので直接リネーム対象のアセットを保持する
                _targetObj = asset;
                _hasNoGuidObj = true;
            }
            else
            {
                _guid = guid;
            }
        }

        public void Update(AssetRenameSettings settings)
        {
            _settings = settings;
            Update();
        }

        public void Update()
        {
            if (_hasNoGuidObj && _targetObj != null)
            {
                _path = "";
                _directory = "";
                _name = _targetObj.name;
            }
            else
            {
                _path = AssetDatabase.GUIDToAssetPath(_guid);
                _directory = Path.GetDirectoryName(_path);
                _name = Path.GetFileNameWithoutExtension(_path);
            }
            
            _newName = _settings != null ? _settings.Rename(_name) : _name;
            _hasDifference = !String.Equals(_name, _newName);
        }

        public void Rename()
        {
            Update();

            if (!Enabled)
            {
                return;
            }

            if (!_hasDifference)
            {
                return;
            }

            if (_hasNoGuidObj && _targetObj != null)
            {
                _targetObj.name = _newName;
            }
            else
            {
                AssetDatabase.RenameAsset(_path, _newName);
            }
        }
    }

    /// <summary>
    /// リネーム用アセットパスのTreeViewItem
    /// </summary>
    public class AssetPathTreeViewItem : TreeViewItem
    {
        public AssetPath Data { get; set; }
    }

    /// <summary>
    /// リネーム用アセットパスのTreeViewのヘッダー
    /// </summary>
    public class AssetRenameMultiColumnHeader : MultiColumnHeader
    {
        private bool _enabled = true;
        
        public Action<bool> EnabledChanged;

        private static readonly Column[] COLUMN_ARRAY = new[]
        {
            new Column
            {
                width = 16,
                autoResize = false,
                allowToggleVisibility = false,
            },
            new Column
            {
                headerContent = new GUIContent("現在の名前"),
                autoResize = true,
                allowToggleVisibility = false,
            },
            new Column
            {
                headerContent = new GUIContent("新しい名前"),
                autoResize = true,
                allowToggleVisibility = false,
            },
            new Column
            {
                headerContent = new GUIContent("フォルダ"),
                autoResize = true,
                allowToggleVisibility = false,
            },
        };

        public AssetRenameMultiColumnHeader() : base(new MultiColumnHeaderState(COLUMN_ARRAY))
        {
            height = 20;
            canSort = false;
            ResizeToFit();
        }

        protected override void ColumnHeaderGUI(Column column, Rect headerRect, int columnIndex)
        {
            if (columnIndex == 0)
            {
                using(var check = new EditorGUI.ChangeCheckScope())
                {
                    _enabled = EditorGUI.Toggle(headerRect, _enabled);

                    if (check.changed)
                    {
                        EnabledChanged?.Invoke(_enabled);
                    }
                }
                return;
            }
            
            base.ColumnHeaderGUI(column, headerRect, columnIndex);
        }

        // デフォルトのヘッダーメニューは不要なので空の関数でoverride
        protected override void AddColumnHeaderContextMenuItems(GenericMenu menu)
        {

        }
    }

    /// <summary>
    /// リネーム用アセットパスのTreeView
    /// </summary>
    public class AssetRenameTreeView : TreeView
    {
        private enum ColumnHeader
        {
            Enabled = 0,
            Name = 1,
            NewName = 2,
            Directory = 3,
        }

        public ReadOnlyCollection<AssetPath> _assets;
        private bool _filter;
        public GenericMenu _menu;

        public AssetRenameTreeView(ReadOnlyCollection<AssetPath> assets, bool filter) : base(new TreeViewState(), new AssetRenameMultiColumnHeader())
        {
            _assets = assets;
            _filter = filter;
            
            _menu = new GenericMenu();
            _menu.AddItem(new GUIContent("有効"), false, () => ChangeEnabled(true));
            _menu.AddItem(new GUIContent("無効"), false, () => ChangeEnabled(false));

            showAlternatingRowBackgrounds = true;

            ((AssetRenameMultiColumnHeader)multiColumnHeader).EnabledChanged += OnEnabledChanged;

            Reload();
        }

        protected override TreeViewItem BuildRoot()
        {
            var root = new TreeViewItem { id = -1, depth = -1 };

            root.children = (_filter ? _assets.Where(asset => asset.HasDifference) : _assets)
                .Select((asset, i) => new AssetPathTreeViewItem {id = i, depth = 0, Data = asset} as TreeViewItem)
                .ToList();

            return root;
        }

        protected override void RowGUI(RowGUIArgs args)
        {
            var item = args.item as AssetPathTreeViewItem;

            using(new EditorGUI.DisabledScope(!item.Data.HasDifference))
            {
                for (var i = 0; i < args.GetNumVisibleColumns(); i++)
                {
                    var cellRect = args.GetCellRect(i);
                    var column = args.GetColumn(i);

                    switch (column)
                    {
                        case (int)ColumnHeader.Enabled:
                            item.Data.Enabled = EditorGUI.Toggle(cellRect, item.Data.Enabled);
                            break;
                        case (int)ColumnHeader.Name:
                            EditorGUI.LabelField(cellRect, item.Data.Name);
                            break;
                        case (int)ColumnHeader.NewName:
                            EditorGUI.LabelField(cellRect, item.Data.NewName);
                            break;
                        case (int)ColumnHeader.Directory:
                            EditorGUI.LabelField(cellRect, item.Data.Directory);
                            break;
                    }
                }
            }
        }
        
        protected override void ContextClickedItem(int id)
        {
            _menu.ShowAsContext();
        }

        private void OnEnabledChanged(bool enabled)
        {
            foreach (var asset in _assets)
            {
                asset.Enabled = enabled;
            }
        }

        private void ChangeEnabled(bool enabled)
        {
            foreach (var item in FindRows(GetSelection()).OfType<AssetPathTreeViewItem>())
            {
                item.Data.Enabled = enabled;
            }
        }

        public void Update(bool filter)
        {
            _filter = filter;

            Reload();
        }
    }

    /// <summary>
    /// リネームツール本体
    /// </summary>
    public class AssetRenameTool : EditorWindow
    {
        private enum TargetSelectionMode
        {
            Selection,
            Hierarchy,
            Directory,
        }

        private static readonly string[] TARGET_SELECTION_MODE_ARRAY = new[]
        {
            "選択アセット",
            "ヒエラルキー内",
            "フォルダ指定",
        };

        private AssetRenameTreeView _treeView;
        private DefaultAsset _targetDirectory;
        private TargetSelectionMode _targetSelectionMode;
        private AssetRenameSettings _settings = new AssetRenameSettings();
        private List<AssetPath> _targetList = new List<AssetPath>();
        private bool _filter = false;
        private bool _isModifying;

        public static void ShowWindow()
        {
            EditorWindow.GetWindow<AssetRenameTool>("AssetRenameTool");
        }

        private void OnEnable()
        {
            _treeView = new AssetRenameTreeView(_targetList.AsReadOnly(), _filter);
            
            UpdateAssets();
        }

        private void OnSelectionChange()
        {
            if (_targetSelectionMode == TargetSelectionMode.Selection)
            {
                UpdateAssets();
            }
        }

        private void OnProjectChange()
        {
            if (!_isModifying)
            {
                UpdateAssets();
            }
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("リネーム対象");

            using (new EditorGUILayout.HorizontalScope())
            {
                using (var check = new EditorGUI.ChangeCheckScope())
                {
                    _targetSelectionMode = (TargetSelectionMode)GUILayout.SelectionGrid(
                        (int)_targetSelectionMode,
                        TARGET_SELECTION_MODE_ARRAY,
                        TARGET_SELECTION_MODE_ARRAY.Length,
                        EditorStyles.radioButton,
                        GUILayout.ExpandWidth(false));

                    if (check.changed)
                    {
                        UpdateAssets();
                    }
                }

                using (var check = new EditorGUI.ChangeCheckScope())
                {
                    _targetDirectory = EditorGUILayout.ObjectField(_targetDirectory, typeof(DefaultAsset), false) as DefaultAsset;
                    if (_targetDirectory != null)
                    {
                        var path = AssetDatabase.GetAssetPath(_targetDirectory);
                        if (!AssetDatabase.IsValidFolder(path))
                        {
                            _targetDirectory = null;
                        }
                    }

                    if (check.changed)
                    {
                        _targetSelectionMode = TargetSelectionMode.Directory;
                        UpdateAssets();
                    }
                }
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("リネーム設定");

            using (var check = new EditorGUI.ChangeCheckScope())
            {

                using (new EditorGUILayout.HorizontalScope())
                {
                    using (new EditorGUILayout.VerticalScope())
                    {
                        EditorGUILayout.LabelField("検索文字列", GUILayout.MinWidth(0));
                        _settings.SearchString = EditorGUILayout.TextField(_settings.SearchString);
                    }

                    using (new EditorGUILayout.VerticalScope())
                    {
                        EditorGUILayout.LabelField("置換文字列", GUILayout.MinWidth(0));
                        _settings.ReplaceString = EditorGUILayout.TextField(_settings.ReplaceString);
                    }
                }

                using (new EditorGUILayout.HorizontalScope())
                {
                    _settings.IsReplace = true;
                    var useRegexLabel = "正規表現を使用する";
                    var useRegexLabelSize = EditorStyles.toggle.CalcSize(new GUIContent(useRegexLabel));
                    _settings.UseRegex = EditorGUILayout.ToggleLeft("正規表現を使用する", _settings.UseRegex, GUILayout.Width(useRegexLabelSize.x), GUILayout.ExpandWidth(false));
                    EditorGUILayout.Space(16, false);
                    _settings.IgnoreCase = EditorGUILayout.ToggleLeft("大文字小文字を無視する", _settings.IgnoreCase, GUILayout.ExpandWidth(false));
                }

                if (check.changed)
                {
                    UpdateAssets(false);
                }
            }
            
            EditorGUILayout.Space();
            EditorGUILayout.Space();

            using (new EditorGUILayout.HorizontalScope())
            {
                var targetCount = 0;
                var displayCount = 0;
                if (_targetList != null)
                {
                    targetCount = _targetList.Count;
                    displayCount = _targetList.Count(asset => asset.Enabled && asset.HasDifference);
                }
                var assetsLabel = $"対象アセットリスト ({displayCount}/{targetCount})";
                var assetsLabelSize = EditorStyles.label.CalcSize(new GUIContent(assetsLabel));

                EditorGUILayout.LabelField(assetsLabel, GUILayout.Width(assetsLabelSize.x));

                EditorGUILayout.Space();
                
                using (var check = new EditorGUI.ChangeCheckScope())
                {
                    _filter = EditorGUILayout.ToggleLeft("置換できるアセットのみ表示する", _filter);

                    if (check.changed)
                    {
                        UpdateAssets(false, false);
                    }
                }

                GUILayout.FlexibleSpace();

                if (GUILayout.Button("リネーム実行"))
                {
                    Rename();
                }
            }

            _treeView.OnGUI(EditorGUILayout.GetControlRect(false, GUILayout.ExpandHeight(true)));
        }

        private void Rename()
        {
            _isModifying = true;

            foreach (var target in _targetList)
            {
                target.Rename();
            }

            _isModifying = false;

            UpdateAssets();
        }

        private void UpdateSettings()
        {
            foreach(var target in _targetList)
            {
                target.Update(_settings);
            }
        }

        private void UpdateTargets()
        {
            _targetList.Clear();

            switch (_targetSelectionMode)
            {
                case TargetSelectionMode.Selection:
                    _targetList.AddRange(Selection.objects
                        .Select(obj => new AssetPath(obj)));
                    break;

                case TargetSelectionMode.Hierarchy:
                    var scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
                    var rootObjects = scene.GetRootGameObjects();
                    var allTransforms = new List<Transform>();

                    foreach (var gameObject in rootObjects) {
                        var transforms = gameObject.GetComponentsInChildren<Transform>(true);
                        if (transforms != null && transforms.Length > 0)
                        {
                            allTransforms.AddRange(transforms);
                        }
                    }

                    var objs = allTransforms.Select(e => e.gameObject).ToArray();
                    _targetList.AddRange(objs.Select(obj => new AssetPath(obj)));
                    break;

                case TargetSelectionMode.Directory:
                    if (_targetDirectory == null)
                    {
                        break;
                    }

                    var directoryPath = AssetDatabase.GetAssetPath(_targetDirectory);
                    var directoryArray = new string[] {directoryPath};
                    _targetList.AddRange(AssetDatabase.FindAssets("", directoryArray)
                        .Select(path => new AssetPath(path)));
                    break;
            }
        }

        private void UpdateAssets(bool updateTargets = true, bool updateSettings = true, bool updateView = true)
        {
            if (updateTargets)
            {
                UpdateTargets();
            }
            if (updateSettings)
            {
                UpdateSettings();
            }
            if (updateView)
            {
                _treeView.Update(_filter);
            }
        }
    }
}

#endif // CYG_DEBUG && UNITY_EDITOR //
