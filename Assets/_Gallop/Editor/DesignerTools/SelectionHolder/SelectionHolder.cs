#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

using App.Editor.DesignerTools.Libs;

namespace SelectionHolder
{
    public class SelectionHolder : EditorWindow
    {
        #region ツール情報

        public const string TOOL_NAME = "SelectionHolder";
        public const string TOOL_VERSION = "21122701";

        #endregion

        #region アイテムボックス周りのstyle設定

        private const float AREA_MARGIN = 20;
        private const float HEADER_HEIGHT = 20;
        private const float FOOTER_HEIGHT = 90;
        private const float SHORT_MARGIN = 5;

        float AreaWidth
        {
            get { return position.width - (AREA_MARGIN * 2); }
        }

        Rect HeaderAreaRect
        {
            get
            {
                return new Rect(
                    AREA_MARGIN,
                    AREA_MARGIN,
                    AreaWidth,
                    HEADER_HEIGHT);
            }
        }

        Rect ItemBoxRect
        {
            get
            {
                return new Rect(
                    AREA_MARGIN,
                    AREA_MARGIN + HEADER_HEIGHT + SHORT_MARGIN,
                    AreaWidth,
                    position.height - ((AREA_MARGIN + HEADER_HEIGHT + SHORT_MARGIN) + (AREA_MARGIN + FOOTER_HEIGHT + SHORT_MARGIN))
                );
            }
        }

        Rect FooterAreaRect
        {
            get
            {
                return new Rect(
                    AREA_MARGIN,
                    position.height - (AREA_MARGIN + FOOTER_HEIGHT),
                    AreaWidth,
                    (AREA_MARGIN + FOOTER_HEIGHT));
            }
        }

        #endregion

        // アイテムボックスの実態部
        private CheckboxListView _cbListView = new CheckboxListView();
        /// -----------------------------------------
        /// <summary>
        /// UI周りの記述
        /// </summary>
        /// -----------------------------------------
        public void OnGUI()
        {
            #region ヘッダー部

            GUILayout.BeginArea(HeaderAreaRect);
            using(new EditorGUILayout.HorizontalScope())
            {
                GUILayout.Label("Asset list");
                GUILayout.FlexibleSpace();
                CreateButton("全てをチェック・チェック解除", _cbListView.SwitchCheckAtOnce);
            }
            GUILayout.EndArea();

            #endregion

            #region アイテムボックス部

            DragAndDropVisualMode visualMode = DragAndDropVisualMode.Generic;

            Event evt = Event.current;
            GUI.Box(ItemBoxRect, "");
            switch (evt.type)
            {
                case EventType.DragUpdated:
                case EventType.DragPerform:

                    // 範囲外の処理はリターン
                    if (!ItemBoxRect.Contains(evt.mousePosition)) break;

                    DragAndDrop.visualMode = visualMode;
                    if (evt.type == EventType.DragPerform)
                    {

                        DragAndDrop.AcceptDrag();
                        string[] dragAndDropPathArray = DragAndDrop.paths;
                        List<CheckboxItem> addedTempItemList = new List<CheckboxItem>();

                        foreach (string path in dragAndDropPathArray)
                        {
                            bool isDir = File.GetAttributes(path).HasFlag(FileAttributes.Directory);
                            if (isDir)
                            {
                                addedTempItemList.Add(new CheckboxItem(path.Replace('\\', '/'), true, true));
                            }
                            else
                            {
                                addedTempItemList.Add(new CheckboxItem(path.Replace('\\', '/'), true, false));
                            }
                        }

                        _cbListView.AddItems(addedTempItemList);
                    }

                    Event.current.Use();
                    break;
            }
            _cbListView.OnGUI(ItemBoxRect);

            #endregion

            #region フッター部

            GUILayout.BeginArea(FooterAreaRect);
            using (new EditorGUILayout.HorizontalScope())
            {
                CreateButton("選択しているアセットをリストへ追加", AddSelectedAsset);
                CreateButton("チェックしたアセットをリストから除外", _cbListView.RemoveCheckedItem);
            }
            EditorGUILayout.Space();

            // オペレーション
            CreateButton("チェックしているアセットを選択", SelectCheckedAsset);
            CreateButton("チェックしているアセットを現在選択しているフォルダーに移動", MoveCheckedAssetsToSelectionFolder);
            CreateButton("チェックしているアセットを現在選択しているフォルダーにコピー", CopyCheckedAssetsToSelectionFolder);
            GUILayout.EndArea();

            #endregion
        }

        #region ウィンドウイベント

        private void OnDisable()
        {
            string listViewData = JsonUtility.ToJson(_cbListView, false);
            EditorPrefs.SetString(TOOL_NAME, listViewData);
        }

        private void OnEnable()
        {
            string listViewData = EditorPrefs.GetString(TOOL_NAME, JsonUtility.ToJson(_cbListView, false));
            JsonUtility.FromJsonOverwrite(listViewData, _cbListView);
        }

        #endregion

        /// -----------------------------------------
        /// <summary>
        /// メソッド類
        /// </summary>
        /// -----------------------------------------
        private void AddSelectedAsset(object Args)
        {
            string[] selectedGuidArray = Selection.assetGUIDs;

            List<CheckboxItem> dragAndDropItemList = new List<CheckboxItem>();

            foreach (string guid in selectedGuidArray)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                bool isDir = File.GetAttributes(path).HasFlag(FileAttributes.Directory);
                if (isDir)
                {
                    dragAndDropItemList.Add(new CheckboxItem(path.Replace('\\', '/'), true, true));
                }
                else
                {
                    dragAndDropItemList.Add(new CheckboxItem(path.Replace('\\', '/'), true, false));
                }
            }
            _cbListView.AddItems(dragAndDropItemList);
        }

        private void SelectCheckedAsset(object Args)
        {
            var itemList = _cbListView.GetCheckedItems();
            UnityEngine.Object[] targetObjectArray = new UnityEngine.Object[itemList.Count + 1];
            for(int i = 0; i < itemList.Count; i++)
            {
                targetObjectArray[i] = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(itemList[i].AssetPath);
            }
            Selection.objects = targetObjectArray;
        }

        private void MoveCheckedAssetsToSelectionFolder(object Args)
        {
            if (!IsValidArgsForFileOp()) return;

            string[] selectedGuidArray = Selection.assetGUIDs;
            string destFolderPath = AssetDatabase.GUIDToAssetPath(selectedGuidArray[0]);

            List<CheckboxItem> willDeleteItemList = new List<CheckboxItem>();
            List<CheckboxItem> checkedItemList = _cbListView.GetCheckedItems();
            foreach(CheckboxItem checkedItem in checkedItemList)
            {
                string sourceAssetName = Path.GetFileName(checkedItem.AssetPath);
                if (sourceAssetName != null)
                {
                    var destAssetPath = destFolderPath + "/" + sourceAssetName;
                    string sourceAbsPath = Path.GetFullPath(checkedItem.AssetPath).Replace("/", "\\");
                    string destAbsPath = Path.GetFullPath(destAssetPath).Replace("/", "\\");

                    if(!IsPathPairValid(sourceAbsPath, destAbsPath))
                    {
                        continue;
                    }

                    try
                    {
                        Directory.Move(sourceAbsPath, destAbsPath);
                        Directory.Move(sourceAbsPath+".meta", destAbsPath + ".meta");
                        willDeleteItemList.Add(checkedItem);
                    }
                    catch (IOException e)
                    {
                        // Validation外のエラーをここでキャッチ
                        Debug.Log(e.Message);
                        continue;
                    }
                }
            }
            _cbListView.RemoveItems(willDeleteItemList);
            _cbListView.Draw();

            // Unityの機能を使わないで更新した場合は更新必須
            AssetDatabase.Refresh();

        }

        private void CopyCheckedAssetsToSelectionFolder(object Args)
        {
            if (!IsValidArgsForFileOp()) return;

            string[] selectedGuidArray = Selection.assetGUIDs;
            string destFolderPath = AssetDatabase.GUIDToAssetPath(selectedGuidArray[0]);

            List<CheckboxItem> willDeleteItemList = new List<CheckboxItem>();
            List<CheckboxItem> checkedItemList = _cbListView.GetCheckedItems();
            foreach (CheckboxItem checkedItem in checkedItemList)
            {
                string sourceAssetName = Path.GetFileName(checkedItem.AssetPath);
                if (sourceAssetName != null)
                {
                    string destAssetPath = destFolderPath + "/" + sourceAssetName;
                    string sourceAbsPath = Path.GetFullPath(checkedItem.AssetPath).Replace("/", "\\");
                    string destAbsPath = Path.GetFullPath(destAssetPath).Replace("/", "\\");

                    if (!IsPathPairValid(sourceAbsPath, destAbsPath))
                    {
                        continue;
                    }

                    if (!checkedItem.IsFolder)
                    {
                        try
                        {
                            CustomFileIO.FileCopy(sourceAbsPath, destAbsPath, false);
                        }
                        catch (Exception e)
                        {
                            // Validation外のエラーをここでキャッチ
                            Debug.LogError(e.Message);
                            continue;
                        }
                    }
                    else
                    {
                        try
                        {
                            CustomFileIO.RecursiveCopy(sourceAbsPath, destAbsPath, false);
                        }
                        catch (Exception e)
                        {
                            // 何らかの原因で移動に失敗時、エラーを出力しcontinue
                            Debug.LogError(e.Message);
                            continue;
                        }
                    }
                    willDeleteItemList.Add(checkedItem);
                }
            }
            _cbListView.RemoveItems(willDeleteItemList);
            _cbListView.Draw();

            // Unityの機能を使わないで更新した場合は更新必須
            AssetDatabase.Refresh();
        }

        private bool IsValidArgsForFileOp()
        {
            string[] selectedGuidArray = Selection.assetGUIDs;
            if (selectedGuidArray.Length != 1)
            {
                Debug.LogError("1つのアイテムを選択してください。");
                return false;
            }

            string targetFolderPath = AssetDatabase.GUIDToAssetPath(selectedGuidArray[0]);
            if (!File.GetAttributes(targetFolderPath).HasFlag(FileAttributes.Directory))
            {
                Debug.LogError("選択するアイテムはフォルダーである必要があります。");
                return false;
            }

            return true;
        }

        private bool IsPathPairValid(string SourcePath, string DistPath)
        {
            // 移動ないしはコピー先が適切かの検証
            if ((!System.IO.File.Exists(SourcePath) && !System.IO.Directory.Exists(SourcePath)) || !System.IO.File.Exists(SourcePath + ".meta"))
            {
                Debug.LogWarning("エラー:" + SourcePath + "は移動・コピー元のファイルが見つかりません");
                return false;
            }

            if((System.IO.File.Exists(DistPath) || System.IO.Directory.Exists(DistPath)) || System.IO.File.Exists(DistPath + ".meta"))
            {
                Debug.LogWarning("エラー:" + DistPath + "は移動・コピー先に同名のファイルがあるため実行できません");
                return false;
            }

            return true;
        }

        #region カスタムGUI関係

        /// -----------------------------------------
        /// <summary>
        /// ボタンを作成
        /// </summary>
        /// -----------------------------------------
        public void CreateButton(string label, System.Action<object> action, object actionArg = null, float buttonHeight = 20)
        {
            if (GUILayout.Button(label, GUILayout.Height(buttonHeight)))
            {
                if (action != null)
                {
                    action(actionArg);
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// Checkboxを格納するListView
        /// </summary>
        /// -----------------------------------------
        public class CheckboxListView
        {
            private Rect _guiRegion = new Rect();
            private Vector2 _scrollPosition = Vector2.zero;

            [SerializeField]
            private List<CheckboxItem> _checkboxItemList = new List<CheckboxItem>();


            public void OnGUI(Rect aGuiRegion)
            {
                _guiRegion = aGuiRegion;

                Draw();
            }

            public void Draw()
            {
                GUILayout.BeginArea(_guiRegion);
                _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
                for(var i = 0; i < _checkboxItemList.Count; i++)
                {
                    float labelWidth = EditorStyles.label.CalcSize(new GUIContent(_checkboxItemList[i].AssetPath)).x;
                    _checkboxItemList[i].IsChecked = EditorGUILayout.ToggleLeft(_checkboxItemList[i].AssetPath, _checkboxItemList[i].IsChecked, GUILayout.Width(labelWidth + 20));
                }
                EditorGUILayout.EndScrollView();
                GUILayout.EndArea();
            }

            public void AddItems(List<CheckboxItem> items)
            {
                foreach(var item in items)
                {
                    if (!GetPathList().Contains(item.AssetPath))
                    {
                        _checkboxItemList.Add(item);
                    }
                }
            }

            public void RemoveItems(List<CheckboxItem> items)
            {
                foreach(var item in items)
                {
                    _checkboxItemList.Remove(item);
                }
            }

            public void RemoveCheckedItem(object Args)
            {
                RemoveItems(GetCheckedItems());
            }

            public List<CheckboxItem> GetCheckedItems()
            {
                List<CheckboxItem> checkedItems = new List<CheckboxItem>();
                foreach(CheckboxItem item in _checkboxItemList)
                {
                    if (item.IsChecked)
                    {
                        checkedItems.Add(item);
                    }
                }
                return checkedItems;
            }

            private List<string> GetPathList()
            {
                List<string> pathList = new List<string>();
                foreach (var item in _checkboxItemList)
                {
                    pathList.Add(item.AssetPath);
                }
                return pathList;
            }

            public void SwitchCheckAtOnce(object Args)
            {
                // 全てがチェックされていない場合は全てチェックし、チェックされている場合は外す
                if(!(_checkboxItemList.Count == GetCheckedItems().Count))
                {
                    foreach(var item in _checkboxItemList)
                    {
                        item.IsChecked = true;
                    }
                }
                else
                {
                    foreach (var item in _checkboxItemList)
                    {
                        item.IsChecked = false;
                    }
                }
            }
        }

        #endregion

        #region チェックリストのデータ
        /// -----------------------------------------
        /// <summary>
        /// チェックリストのデータ
        /// </summary>
        /// -----------------------------------------
        [Serializable]
        public class CheckboxItem
        {
            public bool IsChecked;
            public string AssetPath;
            public bool IsFolder;

            //コンストラクタ
            public CheckboxItem(string aAssetPath, bool aIsChecked, bool aIsFolder){
                AssetPath = aAssetPath;
                IsChecked = aIsChecked;
                IsFolder = aIsFolder;
            }
        }

        #endregion

    }
}

#endif //CYG_DEBUG && UNITY_EDITOR