#if CYG_DEBUG
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace Gallop
{
    /// <summary>
    /// レースアニメーションの出力設定ウインドウ
    /// </summary>
    public class RaceAnimationRefreshSettingsWindow : EditorWindow
    {
        [MenuItem("Gallop/Tools/RaceAnimation Refresh Setting", false, Gallop.EditorMenuPriority.TOOLS)]
        public static void ShowWindow()
        {
            if (_window != null)
                return;

            var window = GetWindow<RaceAnimationRefreshSettingsWindow>();
            _window = window;

            window.Initialize();
        }

        private static RaceAnimationRefreshSettingsWindow _window;

        private RaceAnimationRefreshSetting _setting;

        private void Initialize()
        {
            _setting = AssetDatabase.LoadAssetAtPath<RaceAnimationRefreshSetting>(RaceAnimationRefreshSetting.SettingPath);
            if (_setting == null)
            {
                _setting = ScriptableObject.CreateInstance<RaceAnimationRefreshSetting>();
                _setting._modelObject = new List<GameObject>();
                AssetDatabase.CreateAsset(_setting, RaceAnimationRefreshSetting.SettingPath);
            }
        }

        private void OnDestroy()
        {
            if(_setting != null)
            {
                EditorUtility.SetDirty(_setting);
                AssetDatabase.SaveAssets();
            }
            _window = null;
        }

        private void OnGUI()
        {
            EditorGUILayout.BeginVertical();

            int deleteIndex = -1;
            for (int i = 0; i < _setting._modelObject.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();

                var obj = _setting._modelObject[i];
                _setting._modelObject[i] = (GameObject)EditorGUILayout.ObjectField(string.Format("{0:D2}", i), obj, typeof(GameObject),false);

                if (GUILayout.Button("-",GUILayout.Width(50)))
                {
                    deleteIndex = i;
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space();
            }

            if(deleteIndex != -1)
            {
                _setting._modelObject.RemoveAt(deleteIndex);
            }

            if(GUILayout.Button("+"))
            {
                _setting._modelObject.Add(null);
            }

            EditorGUILayout.EndVertical();
        }
    }

}
#endif