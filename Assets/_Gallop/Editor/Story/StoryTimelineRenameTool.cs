#if CYG_DEBUG && UNITY_EDITOR
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace Gallop
{
    public class StoryTimelineRenameTool : EditorWindow
    {
        private const string USAGE_TITLE = "使い方";

        private const string USAGE_MESSAGE =
            "1.変更前・後のStoryIdを列挙したcsvを用意\n" +
            "   Idはカンマで区切り半角数字\n" +
            "   変更前IDが存在しない場合は何もしない\n" +
            "2.変更後IDに「delete」と記載すると削除\n" +
            "   deleteは完全一致, ダブルクォートなし\n" +
            "3.csvをデスクトップなど任意の場所に配置\n" +
            "4.OKを押してダイアログからcsvを選択";

        private const string PANEL_TITLE = "一括処理に使うcsvを指定して下さい";
        private const string SUCCESS_MESSAGE = "処理が終了しました\nファイル差分をコミットして下さい";
        private const string ABORT_MESSAGE = "エラーが発生したため中断しました\n差分が出ていた場合はチェックアウトして下さい";
        private const string SHOW_ERROR = "エラー出力する";
        private const string CSV_EXTENSION = "csv";
        private const string DELETE_TAG = "delete";
        private const string SLASH = "/";
        private const string STORY_TIMELINE_DATA_FORMAT = "storytimeline_{0}.asset";

        private const char SEPARATOR = ',';

        private const int COLUMN_LENGTH = 2;
        private const int BEFORE_ID_COLUMN = 0;
        private const int AFTER_ID_COLUMN = 1;
        private const int STORY_ID_LENGTH = 9;
        private const int DIRECTORY_INDEX = 0;
        private const int DIRECTORY_LENGTH = 2;
        private const int SUB_DIRECTORY_INDEX = 2;
        private const int SUB_DIRECTORY_LENGTH = 4;

        /// <summary>
        /// 会話Timelineの一括リネームを行う
        /// </summary>
        [MenuItem("Gallop/会話/一括リネームと削除", false, StoryMenuPriority.EDITOR_TOOL)]
        public static void ShowWindow()
        {
            // 説明を表示
            if (!EditorUtility.DisplayDialog(USAGE_TITLE, USAGE_MESSAGE, TextId.Common0003.Text(), TextId.Common0004.Text()))
            {
                // キャンセルされたので処理を抜ける
                return;
            }

            // ファイル選択を開く
            string csvPath = EditorUtility.OpenFilePanel(PANEL_TITLE, string.Empty, CSV_EXTENSION);
            if (string.IsNullOrEmpty(csvPath))
            {
                // キャンセルされたので処理を抜ける
                return;
            }

            try
            {
                RenameStoryTimelineData(csvPath);

                // 成功したらメッセージを表示
                EditorUtility.DisplayDialog(TextId.Common0081.Text(), SUCCESS_MESSAGE, TextId.Common0003.Text());
            }
            catch (System.Exception e)
            {
                // 例外が出た時はメッセージを表示
                // コンソール出力を出すかそのまま閉じるか選べる
                if (EditorUtility.DisplayDialog(TextId.Common0071.Text(), ABORT_MESSAGE, SHOW_ERROR, TextId.Common0007.Text()))
                {
                    Debug.LogError(e.ToString());
                }
            }
        }

        private static void RenameStoryTimelineData(string csvPath)
        {
            using (var sr = new StreamReader(csvPath))
            {
                bool dirty = false;
                var sb = new StringBuilder();

                while (sr.Peek() >= 0)
                {
                    var line = sr.ReadLine();
                    if (string.IsNullOrEmpty(line))
                    {
                        continue;
                    }

                    var csv = line.Split(SEPARATOR);
                    if (csv.Length < COLUMN_LENGTH)
                    {
                        continue;
                    }

                    string beforeColumn = csv[BEFORE_ID_COLUMN];
                    if (!int.TryParse(beforeColumn, out int beforeId))
                    {
                        continue;
                    }

                    if (!ExistsStoryTimelineData(sb, beforeColumn))
                    {
                        continue;
                    }

                    string afterColumn = csv[AFTER_ID_COLUMN];
                    if (int.TryParse(afterColumn, out int afterId))
                    {
                        // 両カラムとも数値なら移動
                        // 必要なデータ更新も行う
                        dirty = true;
                        MoveStoryTimelineData(sb, afterColumn);
                    }
                    else if (string.Equals(afterColumn, DELETE_TAG, System.StringComparison.Ordinal))
                    {
                        // 変更後IDカラムが所定のタグなら削除
                        DeleteStoryTimelineData(sb);
                    }
                }

                if (dirty)
                {
                    // データ更新があったので保存
                    AssetDatabase.SaveAssets();
                }
            }
        }

        private static bool ExistsStoryTimelineData(StringBuilder sb, string storyId)
        {
            if (storyId.Length < STORY_ID_LENGTH)
            {
                return false;
            }

            SetPath(sb, storyId);

            return File.Exists(sb.ToString());
        }

        private static void SetPath(StringBuilder sb, string storyId)
        {
            // プロジェクトフォルダからの相対パスを設定
            sb.Length = 0;
            sb.Append(ResourcePath.BundleResourcesAssetsPath);
            sb.Append(ResourcePath.STORY_TIMELINE_BASE_PATH);
            sb.Append(storyId, DIRECTORY_INDEX, DIRECTORY_LENGTH).Append(SLASH);
            sb.Append(storyId, SUB_DIRECTORY_INDEX, SUB_DIRECTORY_LENGTH).Append(SLASH);
            sb.AppendFormat(STORY_TIMELINE_DATA_FORMAT, storyId);
        }

        private static void MoveStoryTimelineData(StringBuilder sb, string afterId)
        {
            string beforePath = sb.ToString();

            SetPath(sb, afterId);
            string afterPath = sb.ToString();

            string directory = Path.GetDirectoryName(afterPath);
            if (!Directory.Exists(directory))
            {
                // 新しいディレクトリを作成してインポートさせる
                Directory.CreateDirectory(directory);
                AssetDatabase.Refresh();
            }

            // 移動
            AssetDatabase.MoveAsset(beforePath, afterPath);

            // データ更新
            // SaveAssetsは最後にまとめて呼ぶ
            var data = AssetDatabase.LoadAssetAtPath<StoryTimelineData>(afterPath);
            if (data == null)
            {
                throw new System.Exception("ファイル読み込みエラー: " + afterId);
            }
            data.StoryId = afterId;
            EditorUtility.SetDirty(data);
        }

        private static void DeleteStoryTimelineData(StringBuilder sb)
        {
            AssetDatabase.DeleteAsset(sb.ToString());
        }
    }
}
#endif
