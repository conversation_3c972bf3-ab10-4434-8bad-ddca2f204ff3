#if CYG_DEBUG

using UnityEngine;
using UnityEditor;

namespace Gallop
{
    public static class StoryTimelineBatchInterface
    {
        private const int PROCESS_SPLIT_COUNT = 1000;
        
        // ----------------------------------
        // ストーリーアセットパス更新・Editorメニュー実行用
        // ----------------------------------
        [MenuItem("Gallop/会話/StoryAssetPath更新", false, StoryMenuPriority.EDITOR_TOOL)]
        public static void UpdateResourcePathInManual()
        {
            UpdateResourcePathInternal(Cute.Core.Device.GetPersistentDataPath() + "/master/");
        }

        // ----------------------------------
        // ストーリーアセットパス更新・BatchMode用
        // ----------------------------------
        public static void UpdateResourcePathBatchMode()
        {
            // 最適化設定
            {
                // GarbageCollectionの頻度を抑制するためサイズ拡張
                const int USE_MEMORY_SIZE_GB = 16;
                AssetBundle.BuildAssetBundleExtension.SetGarbageCollectionMemoryIncreaseThreshold(USE_MEMORY_SIZE_GB * 1024);

                StoryTimelineEditorWindow.ASSET_FLUSH_FREQUENCY = 2000;
            }

            var customLocalMasterPath = GallopUtil.GetCommandLineArgumentOrNull("-masterDataPath"); // LocalMasterDatanのパス
            // Batchで利用するマスターデータのパスを設定する
            if (string.IsNullOrEmpty(customLocalMasterPath))
            {
                Debug.Log("引数不足 : -masterDataPath");
                EditorApplication.Exit(1);
            }
            
            UpdateResourcePathInternal(customLocalMasterPath);
        }

        /// <summary>
        /// StoryAssetPath更新の対象ファイル一覧をリスト出力する
        /// 
        /// BatchBuildの引数は以下の通り
        /// [required]
        ///   -masterDataPath  : [In] masterDataを生成するディレクトリ
        ///   -changedFileList : [In] Gitで更新のあったファイルのリスト 
        ///   -fileListOutput  : [Out] 更新したファイルのリスト
        /// [option]
        ///   -sceneType       : [In] 対象シーンタイプ、指定がない場合は全シーンタイプが対象
        ///   -buildIndexBegin : [In] ビルド範囲の開始値を指定
        ///   -buildIndexEnd   : [In] ビルド範囲の終了値を指定
        /// </summary>
        private static void UpdateResourcePathInternal(string generateMasterDirectory)
        {
            // Masterを生成
            MasterDataManager.IsUseCustomLocalMasterPath = true;
            MasterDataManager.CustomLocalMasterPath = generateMasterDirectory + "/master.mdb";
            Gallop.DSLCoordinator.GenerateMasterMdb(outputMDBPath: generateMasterDirectory + "/");

            // master.mdbがない場合、おそらく生成に失敗している
            if (!System.IO.File.Exists(MasterDataManager.CustomLocalMasterPath))
            {
                Debug.Log("Master.mdbの生成に失敗、ＣＳＶエラーを確認してください");
                EditorApplication.Exit(1);
            }
            
            // ローカルマスターを生成し、使用を宣言
            GenerateLocalMaster(generateMasterDirectory);
            
            // StoryTimelineの本体処理を呼び出す
            StoryTimelineEditorWindow.UpdateResourcePathBatchMode();
        }

        /// <summary>
        /// StoryAssetPath更新の対象ファイル一覧をリスト出力する
        /// 
        /// BatchBuildの引数は以下の通り
        /// [required]
        ///   -masterDataPath : [Out] masterDataを生成するディレクトリ
        ///   -outUpdateFileListPath : [Out] 更新対象ファイル一覧の出力パス
        /// [option]
        ///   -sceneType : [In] 対象シーンタイプ、指定がない場合は全シーンタイプが対象
        /// </summary>
        private static void GenerateUpdateFileList()
        {
            var customLocalMasterPath = GallopUtil.GetCommandLineArgumentOrNull("-masterDataPath"); // LocalMasterDatanのパス
            // Batchで利用するマスターデータのパスを設定する
            if (string.IsNullOrEmpty(customLocalMasterPath))
            {
                Debug.Log("引数不足 : -masterDataPath");
                EditorApplication.Exit(1);
            }
            
            // ファイル分割単位
            int splitUnit = PROCESS_SPLIT_COUNT;
            var splitUnitString = GallopUtil.GetCommandLineArgumentOrNull("-splitUnit");
            // Batchで利用するマスターデータのパスを設定する
            if (   !string.IsNullOrEmpty(splitUnitString) 
                && int.TryParse(splitUnitString, out var parsedSplitUnit))
            {
                splitUnit = parsedSplitUnit;
            }
            
            // ローカルマスターを生成し、使用を宣言
            GenerateLocalMaster(customLocalMasterPath);
            
            // StoryTimelineの本体処理を呼び出す
            StoryTimelineEditorWindow.GenerateUpdateFileList(splitUnit);
        }

        private static void GenerateLocalMaster(string generatePath)
        {
            // Masterを生成
            MasterDataManager.IsUseCustomLocalMasterPath = true;
            MasterDataManager.CustomLocalMasterPath = generatePath + "/master.mdb";
            Gallop.DSLCoordinator.GenerateMasterMdb(outputMDBPath: generatePath + "/");

            // master.mdbがない場合、おそらく生成に失敗している
            if (!System.IO.File.Exists(MasterDataManager.CustomLocalMasterPath))
            {
                Debug.Log("Master.mdbの生成に失敗、ＣＳＶエラーを確認してください");
                EditorApplication.Exit(1);
            }
            
            // ローカルマスターを使用するよう宣言
            MasterDataManager.IsUseMasterMDBLocal = true;
        }
    }
}

#endif