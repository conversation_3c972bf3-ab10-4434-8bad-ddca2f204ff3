#if CYG_DEBUG && UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;

namespace NUnit.Framework
{
    /// <summary>
    /// NUnit.Assertを簡便に書く拡張メソッド
    /// </summary>
    /// <remarks>参考:https://neue.cc/2010/08/02_270.html</remarks>
    public static class AssertExtensions
    {
        public static void IsNull<T>(this T actual, string message = "")
        {
            Assert.IsNull(actual);
        }
        
        public static void Is<T>(this T actual, T expected, string message = "")
        {
            Assert.AreEqual(expected, actual, message);
        }

        public static void Is<T>(this T actual, Func<T, bool> expected, string message = "")
        {
            Assert.IsTrue(expected(actual), message);
        }

        public static void Is<T>(this IEnumerable<T> actual, IEnumerable<T> expected, string message = "")
        {
            CollectionAssert.AreEqual(expected.ToArray(), actual.ToArray(), message);
        }

        public static void Is<T>(this IEnumerable<T> actual, params T[] expected)
        {
            Is(actual, expected.AsEnumerable());
        }

        public static void Is<T>(this IEnumerable<T> actual, IEnumerable<Func<T, bool>> expected)
        {
            var count = 0;
            foreach (var cond in actual.Zip(expected, (v, pred) => pred(v)))
            {
                Assert.IsTrue(cond, "Index = " + count++);
            }
        }

        public static void Is<T>(this IEnumerable<T> actual, params Func<T, bool>[] expected)
        {
            Is(actual, expected.AsEnumerable());
        }
    }
}
#endif
