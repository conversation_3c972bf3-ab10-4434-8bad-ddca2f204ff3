#if CYG_DEBUG && UNITY_EDITOR
using System.Collections;
using NUnit.Framework;
using UnityEngine.TestTools;

namespace Gallop.Test
{
    /// <summary>
    /// MasterDataManagerをEditModeTestで使えるようにする
    /// </summary>
    public class InitMasterForEditModeTest : NUnitAttribute, IOuterUnityTestAction
    {
        private bool _isUseMasterMDBLocalBackup;
            
        public IEnumerator BeforeTest(NUnit.Framework.Interfaces.ITest test)
        {
            _isUseMasterMDBLocalBackup = MasterDataManager.IsUseMasterMDBLocal;
            MasterDataManager.IsUseMasterMDBLocal = true;
            MasterDataManager.BootForEditor();
                
            yield break;
        }

        public IEnumerator AfterTest(NUnit.Framework.Interfaces.ITest test)
        {
            MasterDataManager.Instance.ForceResetDatabases();
            MasterDataManager.IsUseMasterMDBLocal = _isUseMasterMDBLocalBackup;
                
            yield break;
        }
    }
    
    public class InitTempDataForEditModeTest : NUnitAttribute, IOuterUnityTestAction
    {
        public IEnumerator BeforeTest(NUnit.Framework.Interfaces.ITest test)
        {
            TempData.CreateInstance();
            yield break;
        }

        public IEnumerator AfterTest(NUnit.Framework.Interfaces.ITest test)
        {
            TempData.DestroyInstance();
            yield break;
        }
    }
    
    public class InitWorkDataForEditModeTest : NUnitAttribute, IOuterUnityTestAction
    {
        public IEnumerator BeforeTest(NUnit.Framework.Interfaces.ITest test)
        {
            WorkDataManager.CreateInstance();
            yield break;
        }

        public IEnumerator AfterTest(NUnit.Framework.Interfaces.ITest test)
        {
            WorkDataManager.DestroyInstance();
            yield break;
        }
    }
}
#endif
