#if CYG_DEBUG && UNITY_EDITOR

using NUnit.Framework;
using StateType = Gallop.StoryTimelineMotionClipDataBase.MotionStateType;

namespace Gallop
{
    namespace Test
    {
        public class TestStoryMotionInfo
        {
            private const int SUB_ID = 0;
            private const string ROOT_PATH = ResourcePath.CharacterEventMotionRoot;

            private const string MIRROR = "_mirror";

            #region 固有モーションのテスト

            private const string CHR_MOTION_FORMAT_START = ROOT_PATH + "Chara/chr{0:D4}_00/anm_eve_chr{0:D4}_00_{1}_S";
            private const string CHR_MOTION_FORMAT_LOOP = ROOT_PATH + "Chara/chr{0:D4}_00/anm_eve_chr{0:D4}_00_{1}_loop";
            private const string CHR_MOTION_FORMAT_END = ROOT_PATH + "Chara/chr{0:D4}_00/anm_eve_chr{0:D4}_00_{1}_E";
            private const string CHR_MOTION_FORMAT_POSE = ROOT_PATH + "Chara/chr{0:D4}_00/anm_eve_chr{0:D4}_00_{1}_pose";

            [Category(TestDefine.TEST_CATEGORY_STORY_TIMELINE)]
            [TestCase(CHR_MOTION_FORMAT_START, 1004, "idle01", StateType.Start)]
            [TestCase(CHR_MOTION_FORMAT_LOOP, 1003, "mot01", StateType.Loop)]
            [TestCase(CHR_MOTION_FORMAT_END, 1002, "mot02", StateType.End)]
            [TestCase(CHR_MOTION_FORMAT_POSE, 1001, "idle01", StateType.Pose)]
            public void TestGetMotionPathStoryTimelineChara(string format, int charaId, string motionName, StateType stateType)
            {
                // マスターデータ使うので起動
                MasterDataManager.BootForEditor();

                string path = StoryMotionInfo.GetMotionPathStoryTimeline(charaId, SUB_ID, motionName, stateType);

                // 使い終わったのでリセットかける
                MasterDataManager.Instance.ForceResetDatabases();

                // 期待する値
                string expected = string.Format(format, charaId, motionName);

                Assert.AreEqual(expected, path);
            }
            #endregion 固有モーションのテスト

            #region 共通モーションのテスト

            private const string CMN_MOTION_FORMAT_START = ROOT_PATH + "Type00/anm_eve_type00_{0}_S";
            private const string CMN_MOTION_FORMAT_LOOP = ROOT_PATH + "Type00/anm_eve_type00_{0}_loop";
            private const string CMN_MOTION_FORMAT_END = ROOT_PATH + "Type00/anm_eve_type00_{0}_E";
            private const string CMN_MOTION_FORMAT_ACT = ROOT_PATH + "Type00/anm_eve_type00_{0}";

            [Category(TestDefine.TEST_CATEGORY_STORY_TIMELINE)]
            [TestCase(CMN_MOTION_FORMAT_START, "bow06", StateType.Start)]
            [TestCase(CMN_MOTION_FORMAT_LOOP, "appeal02", StateType.Loop)]
            [TestCase(CMN_MOTION_FORMAT_END, "stretch10", StateType.End)]
            [TestCase(CMN_MOTION_FORMAT_ACT, "banzai01", StateType.Action)]
            public void TestGetMotionPathStoryTimelineCommon(string format, string motionName, StateType stateType)
            {
                // キャラIDは使わないので固定値
                const int charaId = 0;

                // マスターデータ使うので起動
                MasterDataManager.BootForEditor();

                string path = StoryMotionInfo.GetMotionPathStoryTimeline(charaId, SUB_ID, motionName, stateType);

                // 使い終わったのでリセットかける
                MasterDataManager.Instance.ForceResetDatabases();

                // 期待する値
                string expected = string.Format(format, motionName);

                Assert.AreEqual(expected, path);
            }
            #endregion 共通モーションのテスト
        }
    }
}

#endif
