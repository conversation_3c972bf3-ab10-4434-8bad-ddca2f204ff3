using UnityEngine.UI;
using UnityEditor;
using UnityEditor.UI;

namespace Gallop
{
    [CanEditMultipleObjects]
    [CustomEditor(typeof(RawImageCommon), true)]
    public class RawImageCommonEditor : RawImageEditor
    {
        private SerializedProperty _isIgnoreParentColor;
        protected override void OnEnable()
        {
            base.OnEnable();
            _isIgnoreParentColor = serializedObject.FindProperty("_isIgnoreParentColor");
        }
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            EditorGUI.BeginChangeCheck();
            EditorGUILayout.PropertyField(_isIgnoreParentColor);
            if ( EditorGUI.EndChangeCheck() )
            {
                serializedObject.ApplyModifiedProperties();
            }
        }

        [MenuItem("CONTEXT/RawImage/Change RawImageCommon")]
        public static void ChengeRawImageCommon(MenuCommand menuCommand)
        {
#if CYG_DEBUG
            var rawImage = menuCommand.context as RawImage;
            if (rawImage == null)
            {
                return;
            }
            var references = FindReferenceTool.FindReferencesTo(rawImage);
            var gameObject = rawImage.gameObject;

            var material = rawImage.material == rawImage.defaultMaterial ? null : rawImage.material;
            var color = rawImage.color;
            var raycastTarget = rawImage.raycastTarget;
            var onCullStateChanged = rawImage.onCullStateChanged;
            var texture = rawImage.texture;
            var uvRect = rawImage.uvRect;

            DestroyImmediate(rawImage);

            var rawImageCommon = gameObject.AddComponent<RawImageCommon>();
            if (rawImageCommon == null)
            {
                return;
            }

            rawImageCommon.material = material;
            rawImageCommon.color = color;
            rawImageCommon.raycastTarget = raycastTarget;
            rawImageCommon.onCullStateChanged = onCullStateChanged;
            rawImageCommon.texture = texture;
            rawImageCommon.uvRect = uvRect;

            //参照を付け直す
            foreach (var @ref in references)
            {
                @ref.Value.objectReferenceValue = rawImageCommon;
                @ref.Key.ApplyModifiedProperties();
            }
#endif
        }
    }
}