using UnityEngine.UI;
using UnityEditor;
using UnityEditor.UI;

namespace Gallop
{
    /// <summary>
    /// ScrollRectCommonのインスペクター拡張
    /// </summary>
    [CanEditMultipleObjects]
    [CustomEditor(typeof(ScrollRectCommon))]
    public class ScrollRectCommonEditor : ScrollRectEditor
    {
        private const float ELASTICITY = 0.1f;
        private const float DECELERATION_RATE = 0.135f;
        private const float SCROLL_SENSITIVITY = 1;

        private const int LOOPSCROLL_MERGIN_HORIZONTAL = 8;
        private const int LOOPSCROLL_MERGIN_VERTICAL = 8;

        private SerializedProperty _isForceBarAutoHide;
        private SerializedProperty _isSetScrollBarSize;
        private SerializedProperty _verticalScrollBarMargin;
        private SerializedProperty _isTransmitEvent;
        private SerializedProperty _raycaster;

        /// <summary>
        /// Enable
        /// </summary>
        protected override void OnEnable()
        {
            base.OnEnable();
            //設定周りを固定する
            ScrollRectCommon component = (ScrollRectCommon)target;

            component.elasticity = ELASTICITY;
            component.decelerationRate = DECELERATION_RATE;
            component.scrollSensitivity = SCROLL_SENSITIVITY;

            _isForceBarAutoHide = serializedObject.FindProperty("_isForceBarAutoHide");
            _isSetScrollBarSize = serializedObject.FindProperty("_isSetScrollBarSize");
            _verticalScrollBarMargin = serializedObject.FindProperty("_verticalScrollBarMargin");
            _isTransmitEvent = serializedObject.FindProperty("_isTransmitEvent");
            _raycaster = serializedObject.FindProperty("_raycaster");
        }

        /// <summary>
        /// Inspector表示
        /// </summary>
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            EditorGUILayout.PropertyField(_isForceBarAutoHide);
            EditorGUILayout.PropertyField(_isSetScrollBarSize);
            EditorGUI.BeginDisabledGroup(_isSetScrollBarSize.boolValue == false);
            EditorGUILayout.PropertyField(_verticalScrollBarMargin);
            EditorGUI.EndDisabledGroup();
            EditorGUILayout.PropertyField(_isTransmitEvent);
            EditorGUILayout.PropertyField(_raycaster);

            serializedObject.ApplyModifiedProperties();

            ScrollRectCommon component = (ScrollRectCommon)target;
            if (!component.gameObject.activeInHierarchy)
            {
                component.Update();
            }
        }

        [MenuItem("CONTEXT/ScrollRect/Change ScrollRectCommon")]
        public static void ChengeScrollRectCommon(MenuCommand menuCommand)
        {
#if CYG_DEBUG
            var scrollRect = menuCommand.context as ScrollRect;
            if (scrollRect == null)
            {
                return;
            }
            var references = FindReferenceTool.FindReferencesTo(scrollRect);
            var gameObject = scrollRect.gameObject;

            var content = scrollRect.content;
            var horizontal = scrollRect.horizontal;
            var vertical = scrollRect.vertical;
            var movementType = scrollRect.movementType;
            var elasticity = scrollRect.elasticity;
            var inertia = scrollRect.inertia;
            var decelerationRate = scrollRect.decelerationRate;
            var scrollSensitivity = scrollRect.scrollSensitivity;
            var viewport = scrollRect.viewport;
            var horizontalScrollbar = scrollRect.horizontalScrollbar;
            var verticalScrollbar = scrollRect.verticalScrollbar;
            var horizontalScrollbarVisibility = scrollRect.horizontalScrollbarVisibility;
            var verticalScrollbarVisibility = scrollRect.verticalScrollbarVisibility;
            var horizontalScrollbarSpacing = scrollRect.horizontalScrollbarSpacing;
            var verticalScrollbarSpacing = scrollRect.verticalScrollbarSpacing;
            var onValueChanged = scrollRect.onValueChanged;

            DestroyImmediate(scrollRect);

            var scrollRectCommon = gameObject.AddComponent<ScrollRectCommon>();
            if (scrollRectCommon == null)
            {
                return;
            }

            scrollRectCommon.content = content;
            scrollRectCommon.horizontal = horizontal;
            scrollRectCommon.vertical = vertical;
            scrollRectCommon.movementType = movementType;
            scrollRectCommon.elasticity = elasticity;
            scrollRectCommon.inertia = inertia;
            scrollRectCommon.decelerationRate = decelerationRate;
            scrollRectCommon.scrollSensitivity = scrollSensitivity;
            scrollRectCommon.viewport = viewport;
            scrollRectCommon.horizontalScrollbar = horizontalScrollbar;
            scrollRectCommon.verticalScrollbar = verticalScrollbar;
            scrollRectCommon.horizontalScrollbarVisibility = horizontalScrollbarVisibility;
            scrollRectCommon.verticalScrollbarVisibility = verticalScrollbarVisibility;
            scrollRectCommon.horizontalScrollbarSpacing = horizontalScrollbarSpacing;
            scrollRectCommon.verticalScrollbarSpacing = verticalScrollbarSpacing;
            scrollRectCommon.onValueChanged = onValueChanged;

            //参照を付け直す
            foreach (var @ref in references)
            {
                @ref.Value.objectReferenceValue = scrollRectCommon;
                @ref.Key.ApplyModifiedProperties();
            }
#endif
        }
    }
}
