#if UNITY_EDITOR
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using UnityEditor;
using UnityEngine;

namespace Gallop
{
    public partial class DSLCoordinator : EditorWindow
    {
        // --- パスについて ---
        // Application.dataPathからの相対パスで指定することもできるが
        // GallpではApplication.dataPathの外側に配置しているので
        // プロジェクトのルートディレクトリからの相対パスを指定すればよい

        private const string SCRIPT_PATH = "CodeGenerator/Scripts/main.rb";
        private const string DSL_PATH = "CodeGenerator/DSL/master.dsl.rb";
        private const string ERROR_REPORT_PATH = "CodeGenerator/Output/ErrorReport.html";
        private const string ERROR_REPORT_NUM_PATH_FORMAT = "CodeGenerator/Output/ErrorReport{0}.html";
        private const string STDOUT_NUM_PATH_FORMAT = "CodeGenerator/Output/stdout{0}.txt";
        private const string STDERR_NUM_PATH_FORMAT = "CodeGenerator/Output/stderr{0}.txt";

        // Application.dataPath (UNITY_EDITORなのでAssets固定)
        private const string DATA_PATH = "Assets";

        private const string CODE_OUTPUT_PATH = DATA_PATH + "/_Gallop/Scripts/Game/Data/Master/Base";
        private const string CSV_DATA_PATH = DATA_PATH + "/_GallopCsv";

        // rubyコマンドに渡す引数のフォーマット
        private const string GENERATE_MASTER_CLASS_FORMAT = "\"{0}\" -s \"{1}\" -c gen_client -o \"{2}\"";
        private const string VALIDATE_LOCAL_CSV_FORMAT = "\"{0}\" -s \"{1}\" -c import_csv -i \"{2}\" -e \"{3}\"";
        private const string GENERATE_MASTER_MDB_FORMAT = "\"{0}\" -s \"{1}\" -c import_csv -i \"{2}\" -o \"{3}\" -e \"{4}\"";
        private const string GENERATE_AND_VALIDATE_MASTER_MDB_FORMAT =
            "\"{0}\" -s \"{1}\" -c import_csv -i \"{2}\" -o \"{3}\" -e \"{4}\" --zzci_dynamic_only";
        private const string INCREMENTAL_GENERATE_AND_VALIDATE_MASTER_MDB_FORMAT =
            "\"{0}\" -s \"{1}\" -c import_csv -i \"{2}\" -o \"{3}\" -e \"{4}\" --zzci_dynamic_only --update";
        private const string VALIDATE_MULTI_PROCESS_FORMAT =
            "\"{0}\" -s \"{1}\" -c validate -i \"{2}\" -e \"{3}\" --zzci_hash_index {4} --zzci_hash_div {5} --zzci_csv_directory \"{6}\" --zzci_stdout \"{7}\" --zzci_stderr \"{8}\"";

        // CSVが格納されているパス (ユーザが設定可能)
        private string _masterCsvDirectoryPath = CSV_DATA_PATH;

        // .mdbの出力先
        // Application.persistentDataPathはScriptableObjectのコンストラクタでは参照できないので
        // 宣言時ではなく実行するタイミングで代入する.
        private string _outputMDBPath;
        
        // Rubyバージョン情報
        // 【変更をする場合】 GallopClient/BatchBuild/config.yml も編集してください！！
        private const string RUBY_VERSION = "3.2.8";

        // 環境変数
        public static EnvBuildParamString _homeDirPath = new EnvBuildParamString("USERPROFILE", "", "ユーザーのルートディレクトリ");
        
        // Ruby実行ファイルへのパス
        public const string RUBY_PATH_FORMAT = "{0}/rbenv_root/{1}-1/bin/ruby.exe";

        // EditorPrefs 保存キー
        public const string PREF_KEY_PREFIX = "DSLCoordinator_";
        public const string PREF_KEY_ENABLE_AUTO_PLAY_ON_GENERATED = PREF_KEY_PREFIX + "AutoPlayOnGenerated";
        public const string PREF_KEY_ENABLE_AUTO_GENERATE_ON_CSV_CHANGED = PREF_KEY_PREFIX + "EnableAutoGenerateOnCSVChanged";
        public const string PREF_KEY_ENABLE_INCREMENTAL_GENERATE = PREF_KEY_PREFIX + "EnableIncrementalGenerate";
        public const string PREF_KEY_ENABLE_BACKGROUND_VALIDATION = PREF_KEY_PREFIX + "EnableBackgroundValidation";
        public const string PREF_KEY_TABBED_WINDOW = PREF_KEY_PREFIX + "TabbedWindow";

        // バックグラウンドで実行中のプロセスID
        private static int[] RunningBackgroundProcessIds
        {
            get => DSLCoordinatorStaticValueHolder.instance.runningBackgroundProcessIds;
            set => DSLCoordinatorStaticValueHolder.instance.runningBackgroundProcessIds = value;
        }

        // バックグラウンドで実行中のプロセスが存在すればTrueを返す
        private static bool IsRunningBackgroundProcess
        {
            get => RunningBackgroundProcessIds.Any(id => id > 0);
        }

        // バックグラウンドで実行中のプロセスがあるときの Generate ボタンのラベル
        private static string _generateButtonLabel;

        [MenuItem("Cygames/CodeGeneration/Master Data Classes")]
        public static void Generate()
        {
            if (!CheckRbenvExist())
            {
                return;
            }
            
            using (Process p = new Process())
            {
                // 子プロセス実行
                string arguments = string.Format(GENERATE_MASTER_CLASS_FORMAT, SCRIPT_PATH, DSL_PATH, CODE_OUTPUT_PATH);
                RunRubyProcess(p, arguments);

                // 大量の出力でパイプが詰まるので処理
                ForwardConsoleLog(p, true);

                UnityEditor.AssetDatabase.Refresh();
            }
        }

        [MenuItem("Cygames/CodeGeneration/Validate Local CSV %#d")]
        public static void OpenValidatorWindow()
        {
            GetWindow();
        }

        private static DSLCoordinator GetWindow()
        {
            return GetWindowWithRect<DSLCoordinator>(new Rect(0, 0, 425, 235), !EditorPrefs.GetBool(PREF_KEY_TABBED_WINDOW, false), "DSL Coordinator");
        }

        private void ReopenWindow()
        {
            EditorApplication.delayCall += () =>
            {
                Close();
                GetWindow();
            };
        }

        public void OnGUI()
        {
            if (_masterCsvDirectoryPath == null)
            {
                // 規定値はAssets/_GallopCsvとする
                _masterCsvDirectoryPath = CSV_DATA_PATH;
            }

            // CSVの参照先設定
            GUI.Label(new Rect(5, 5, 150, 20), "Master CSV Path:");
            _masterCsvDirectoryPath = EditorGUI.TextField(new Rect(5, 25, 330, 20), _masterCsvDirectoryPath);
            // 参照ボタン
            if (GUI.Button(new Rect(340, 25, 80, 20), "Browse..."))
            {
                // フォルダ選択ダイアログ（パスを取得する）
                string strTempFile = EditorUtility.OpenFolderPanel("Master CSV Path", _masterCsvDirectoryPath, "");

                // パス情報が入力されていたら反映（キャンセルの場合は空文字で戻ってくる）
                if (strTempFile.Length != 0)
                {
                    _masterCsvDirectoryPath = strTempFile;
                }
            }

            const int ButtonY = 70, ButtonW = 80, ButtonWLonger = 120, ButtonH = 20, ButtonSpace = 5;
            int buttonX = ButtonSpace;
            if (GUI.Button(new Rect(buttonX, ButtonY, ButtonW, ButtonH), "Validate"))
            {
                if (!CheckRbenvExist())
                {
                    return;
                }
                
                using (Process p = new Process())
                {
                    // 子プロセス実行
                    string arguments = string.Format(VALIDATE_LOCAL_CSV_FORMAT, SCRIPT_PATH, DSL_PATH, _masterCsvDirectoryPath, ERROR_REPORT_PATH);
                    RunRubyProcess(p, arguments);

                    // 大量の出力でパイプが詰まるので処理
                    ForwardConsoleLog(p, false);
                }

                GUI.FocusControl("");
            }

            buttonX += ButtonSpace + ButtonW;
            EditorGUI.BeginDisabledGroup(IsRunningBackgroundProcess);
            if (GUI.Button(new Rect(buttonX, ButtonY, ButtonW, ButtonH), IsRunningBackgroundProcess ? _generateButtonLabel : "Generate"))
            {
                GenerateMasterMdbWithGUIParameter();

                GUI.FocusControl("");
            }
            EditorGUI.EndDisabledGroup();

            buttonX += ButtonSpace + ButtonW;
#if CYG_DEBUG
            bool isUseMasterMDBLocal = MasterDataManager.IsUseMasterMDBLocal;
            if (isUseMasterMDBLocal != GUI.Toggle(new Rect(buttonX, ButtonY, ButtonWLonger, ButtonH), isUseMasterMDBLocal, "Use Local MDB"))
            {
                EditorPrefs.SetBool(GameDefine.UseMasterMDBLocalPrefs, !isUseMasterMDBLocal);
            }
#endif

            var buttonY = 110;

            if (GUI.Button(new Rect(300, buttonY, ButtonWLonger, 20), "初期設定に戻す"))
            {
                EditorPrefs.DeleteKey(PREF_KEY_TABBED_WINDOW);
                EditorPrefs.DeleteKey(PREF_KEY_ENABLE_AUTO_GENERATE_ON_CSV_CHANGED);
                EditorPrefs.DeleteKey(PREF_KEY_ENABLE_AUTO_PLAY_ON_GENERATED);
                EditorPrefs.DeleteKey(PREF_KEY_ENABLE_INCREMENTAL_GENERATE);
                EditorPrefs.DeleteKey(PREF_KEY_ENABLE_BACKGROUND_VALIDATION);
                ReopenWindow();
            }

            GUI.Label(new Rect(5, buttonY, 420, 20), "便利機能");

            buttonY += ButtonH;
            var tabbedWindow = EditorPrefs.GetBool(PREF_KEY_TABBED_WINDOW, false);
            if (tabbedWindow !=
                GUI.Toggle(new Rect(10, buttonY, 420, 20), tabbedWindow, "タブ化可能なウィンドウで表示する"))
            {
                EditorPrefs.SetBool(PREF_KEY_TABBED_WINDOW, !tabbedWindow);
                ReopenWindow();
            }

#if CYG_DEBUG
            // 以下は Use Local MDB にチェックが入ってない場合は自動的にOFFになる
            EditorGUI.BeginDisabledGroup(!isUseMasterMDBLocal);

            buttonY += ButtonH;
            var enableAutoGenerateOnCSVChanged = EditorPrefs.GetBool(PREF_KEY_ENABLE_AUTO_GENERATE_ON_CSV_CHANGED);
            if (enableAutoGenerateOnCSVChanged !=
                GUI.Toggle(new Rect(10, buttonY, 420, 20), isUseMasterMDBLocal &&enableAutoGenerateOnCSVChanged, "CSV更新時に自動でGenerate実行 (※失敗時に自動で設定OFFになります)"))
            {
                if (isUseMasterMDBLocal) EditorPrefs.SetBool(PREF_KEY_ENABLE_AUTO_GENERATE_ON_CSV_CHANGED, !enableAutoGenerateOnCSVChanged);
            }

            buttonY += ButtonH;
            var enableAutoPlayOnGenerated = EditorPrefs.GetBool(PREF_KEY_ENABLE_AUTO_PLAY_ON_GENERATED);
            if (enableAutoPlayOnGenerated != GUI.Toggle(new Rect(10, buttonY, 420, 20), isUseMasterMDBLocal && enableAutoPlayOnGenerated, "Generate完了時に自動で再生"))
            {
                if (isUseMasterMDBLocal) EditorPrefs.SetBool(PREF_KEY_ENABLE_AUTO_PLAY_ON_GENERATED, !enableAutoPlayOnGenerated);
            }

            EditorGUI.EndDisabledGroup();
#endif

            buttonY += ButtonH;
            if (!EditorPrefs.HasKey(PREF_KEY_ENABLE_INCREMENTAL_GENERATE))
            {
                // 初期値が入っていない場合、設定する
                EditorPrefs.SetBool(PREF_KEY_ENABLE_INCREMENTAL_GENERATE, true);
            }
            var enableIncrementalGenerate = EditorPrefs.GetBool(PREF_KEY_ENABLE_INCREMENTAL_GENERATE);
            if (enableIncrementalGenerate !=
                GUI.Toggle(new Rect(10, buttonY, 420, 20), enableIncrementalGenerate, "Generate時、MDB更新は可能なら変更のあったCSVのみに絞り高速処理する"))
            {
                EditorPrefs.SetBool(PREF_KEY_ENABLE_INCREMENTAL_GENERATE, !enableIncrementalGenerate);
            }

            buttonY += ButtonH;
            if (!EditorPrefs.HasKey(PREF_KEY_ENABLE_BACKGROUND_VALIDATION))
            {
                // 初期値が入っていない場合、設定する
                EditorPrefs.SetBool(PREF_KEY_ENABLE_BACKGROUND_VALIDATION, true);
            }
            var enableBackgroundValidation = EditorPrefs.GetBool(PREF_KEY_ENABLE_BACKGROUND_VALIDATION);
            if (enableBackgroundValidation !=
                GUI.Toggle(new Rect(10, buttonY, 420, 20), enableBackgroundValidation, "Generate時、MDB更新完了時に待ちをすぐに解除し、整合性ﾁｪｯｸは裏でやる"))
            {
                EditorPrefs.SetBool(PREF_KEY_ENABLE_BACKGROUND_VALIDATION, !enableBackgroundValidation);
            }
        }

        /// <summary>
        /// 静的メソッドに実装された本体を呼び出すだけのインスタンスメソッド
        /// GUIのGenerateボタンを押された時など、GUIの入力値を反映して呼び出したいときにこのメソッドを使用する
        /// </summary>
        public void GenerateMasterMdbWithGUIParameter(bool enableAdditionalFeatures = true)
        {
            GenerateMasterMdb(_masterCsvDirectoryPath, _outputMDBPath, enableAdditionalFeatures);
        }

        public static void GenerateMasterMdb(string masterCsvDirectoryPath = CSV_DATA_PATH,
            string outputMDBPath = null, bool enableAdditionalFeatures = false)
        {
            if (!CheckRbenvExist())
            {
                return;
            }
            
            if (string.IsNullOrEmpty(outputMDBPath))
            {
                // MasterDataManager.SetupMasterGroupの設定.
                outputMDBPath = GameDefine.LOCAL_MDB_DIRECTRY;
            }

            // 直前に実行した Generate のバックグラウンドプロセスが残っている場合は、新たにGenerateは開始できない
            // ボタンをグレーアウトして防いでいるはずだが、万一ここに来た場合に弾く
            if (IsRunningBackgroundProcess)
            {
                UnityEngine.Debug.LogWarning("実行中のバックグラウンドプロセスがあるため新たにGenerateを開始できません");
                return;
            }

            // 実行中にGenerateしようとするとゲームがmaster.mdbを掴んでいるため操作できない
            // これをしようとした場合、エラーで沈むのは悲しいので確認を出す
            if (EditorApplication.isPlaying)
            {
                if (EditorUtility.DisplayDialog("DSL Coordinator", "実行中にGenerateすることはできません。\n実行を停止してGenerateを行いますか？", "Yes", "No"))
                {
                    // 終了命令投げてもすぐ終了しないので待ってから再度メソッドを呼び出して処理続行させる
                    EditorApplication.ExitPlaymode();
                    WaitForExit(() => GenerateMasterMdb(masterCsvDirectoryPath, outputMDBPath, enableAdditionalFeatures));
                    void WaitForExit(Action onExit)
                    {
                        if (EditorApplication.isPlaying)
                        {
                            EditorApplication.delayCall += () => WaitForExit(onExit);
                        }
                        else
                        {
                            onExit?.Invoke();
                        }
                    }
                }

                return;
            }

            if (!Directory.Exists(outputMDBPath))
            {
                UnityEngine.Debug.Log("<color=cyan>.mdbの出力先ディレクトリを作成しました。</color>");
                Directory.CreateDirectory(outputMDBPath);
            }
            
#if UNITY_EDITOR && CYG_DEBUG
            // MasterをGenerateしたらmaster_hashを削除しておく
            // これにより MasterDataManager.UseDbServer() を通過した時に必ずマスターが再展開される
            string regularMasterDirectory = MasterDataManager.GetMasterdataDirectory();
            if (Directory.Exists(regularMasterDirectory))
            {
                var extractedHashPath = MasterDataManager.GetExtractedHashPath(regularMasterDirectory);
                extractedHashPath = extractedHashPath.Replace("/", "\\");
                File.Delete(extractedHashPath);
            }
#endif
            
            var enableIncrementalGenerate = enableAdditionalFeatures && EditorPrefs.GetBool(PREF_KEY_ENABLE_INCREMENTAL_GENERATE);
            if (!enableIncrementalGenerate)
            {
                //ワーニングが出るので、先にローカルにあるmdbを削除してしまう
                File.Delete(Path.Combine(outputMDBPath, "master.mdb"));
            }

            var benchmark = new Stopwatch();
            benchmark.Start();

            var isError = GenerateMasterMdb_Parallel(masterCsvDirectoryPath, outputMDBPath, enableAdditionalFeatures);

            // エラーだったら処理打ち切り
            if (isError)
            {
                return;
            }

            benchmark.Stop();
            UnityEngine.Debug.Log($"<color=magenta>Generateのユーザー待ち時間 {benchmark.ElapsedMilliseconds / 1000.0:F03}秒</color>");

#if CYG_DEBUG
            var enableAutoPlayOnGenerated = enableAdditionalFeatures && EditorPrefs.GetBool(PREF_KEY_ENABLE_AUTO_PLAY_ON_GENERATED);
            if (enableAutoPlayOnGenerated && MasterDataManager.IsUseMasterMDBLocal) // 自動再生はUse Local MDB 有効時のみ発動（無効時にやる意味がない）
            {
                EditorApplication.EnterPlaymode();
            }
#endif
        }

        private static bool GenerateMasterMdb_Legacy(string masterCsvDirectoryPath, string outputMDBPath)
        {
            using (Process p = new Process())
            {
                // 子プロセス実行
                string arguments = string.Format(GENERATE_MASTER_MDB_FORMAT, SCRIPT_PATH, DSL_PATH,
                    masterCsvDirectoryPath, outputMDBPath, ERROR_REPORT_PATH);
                RunRubyProcess(p, arguments);

                // 大量の出力でパイプが詰まるので処理
                return ForwardConsoleLog(p, true);
            }
        }

        private static bool GenerateMasterMdb_Parallel(string masterCsvDirectoryPath, string outputMDBPath, bool enableAdditionalFeatures = true)
        {
            // まずGenerateそのものを実施する
            using (Process p = new Process())
            {
                // 子プロセス実行
                string arguments;

                var enableIncrementalGenerate = enableAdditionalFeatures && EditorPrefs.GetBool(PREF_KEY_ENABLE_INCREMENTAL_GENERATE);
                if (enableIncrementalGenerate)
                {
                    arguments = string.Format(INCREMENTAL_GENERATE_AND_VALIDATE_MASTER_MDB_FORMAT, SCRIPT_PATH, DSL_PATH,
                        masterCsvDirectoryPath, outputMDBPath, ERROR_REPORT_PATH);
                }
                else
                {
                    arguments = string.Format(GENERATE_AND_VALIDATE_MASTER_MDB_FORMAT, SCRIPT_PATH, DSL_PATH,
                        masterCsvDirectoryPath, outputMDBPath, ERROR_REPORT_PATH);
                }

                RunRubyProcess(p, arguments);

                // 大量の出力でパイプが詰まるので処理
                var isError = ForwardConsoleLog(p, true);

                if (isError) return true; // ここでエラー出てたら処理打ち切り
            }
            
            // 実はGenerateを押すと、自動でVaridateが走っていて時間を食っているのはこちら
            // これを工夫して高速化することにする
            var processNum = SystemInfo.processorCount; // 並列数はとりあえずコア数にしとく
            var rubyProcessArray = new RubyProcess[processNum];

            var enableRunValidationInBackground = enableAdditionalFeatures && EditorPrefs.GetBool(PREF_KEY_ENABLE_BACKGROUND_VALIDATION);
            if (enableRunValidationInBackground)
            {
                RunningBackgroundProcessIds = new int[processNum]; // 配列初期化しとく
            }

            var context = SynchronizationContext.Current;

            var finallyDispose = true;
            try
            {
                // 子プロセス実行
                for (int i = 0; i < processNum; i++)
                {
                    var arguments = string.Format(VALIDATE_MULTI_PROCESS_FORMAT, SCRIPT_PATH, DSL_PATH,
                        Path.Combine(outputMDBPath, "master.mdb"),
                        string.Format(ERROR_REPORT_NUM_PATH_FORMAT, i + 1), i + 1, processNum, masterCsvDirectoryPath,
                        string.Format(STDOUT_NUM_PATH_FORMAT, i + 1),
                        string.Format(STDERR_NUM_PATH_FORMAT, i + 1));
                    rubyProcessArray[i] = new RubyProcess(i + 1);
                    rubyProcessArray[i].Run(arguments);

                    // バックグラウンド実行の場合、プロセスID控えておく必要がある
                    if (enableRunValidationInBackground)
                    {
                        var index = i;
                        var rubyProcess = rubyProcessArray[index];
                        var processId = rubyProcess.GetProcessId();
                        RunningBackgroundProcessIds[i] = processId;
                        rubyProcessArray[i].SetExitHandler(() => BackgroundRubyProcessExited(index, context), true, context); // SetExitHandlerの中に終了処理も入っている
                    }
                }

                // バックグラウンド実行の場合、待ちのループには進まない
                if (enableRunValidationInBackground)
                {
                    RepaintBackgroundProcessProgress();
                    finallyDispose = false; // Dispose()すると仕込んだイベントもろとも破棄されちゃうのでfalseにする. ただし途中で何かあった場合Dispose()したいのでフラグを落とすタイミングはここ
                    return false;
                }

                // 待ちのループ
                for (int i = 0; i < processNum; i++)
                {
                    rubyProcessArray[i].WaitForExit(true); // WaitForExitと言いながら終了時処理も入ってるので、後続に書くことは無い
                }
            }
            finally
            {
                if (finallyDispose)
                {
                    // 子プロセスを明示的に破棄
                    for (int i = 0; i < processNum; i++)
                    {
                        rubyProcessArray[i].Dispose();
                    }
                }
            }

            return rubyProcessArray.Any(rubyProcess => rubyProcess.IsError); // IsErrorが1つでもあったらtrueを返す
        }

        /// <summary>
        /// rubyの子プロセスを実行する
        /// </summary>
        /// <param name="p">プロセス</param>
        /// <param name="arguments">rubyコマンドに渡すパラメータ</param>
        private static void RunRubyProcess(Process p, string arguments)
        {
            p.StartInfo.FileName = GetRubyPath();
            p.StartInfo.CreateNoWindow = true;
            p.StartInfo.UseShellExecute = false;
            p.StartInfo.RedirectStandardOutput = true;
            p.StartInfo.RedirectStandardError = true;
            p.StartInfo.Arguments = arguments;

            UnityEngine.Debug.Log("<color=cyan>処理を開始します。</color>\nruby " + arguments);
            p.Start();
        }

        /// <summary>
        /// Rubyのパスを取得
        /// </summary>
        /// <returns></returns>
        private static string GetRubyPath()
        {
#if UNITY_EDITOR_WIN            
            // WindowsはSetup.batでインストールされたpathのruby.exeをたたく
            _homeDirPath.InitializeValue();
            return string.Format(RUBY_PATH_FORMAT, (string)_homeDirPath, RUBY_VERSION);
#else
            // Macではrbenvを使ってインストールされたものを使う (Linuxも一応こっち)
            // バージョンまではチェックしない
            return $"{Environment.GetEnvironmentVariable("HOME")}/.rbenv/shims/ruby";
#endif
        }

        /// <summary>
        /// Rbenvがインストールされているかを確認
        /// </summary>
        /// <returns></returns>
        private static bool CheckRbenvExist()
        {
            var rubyPath = GetRubyPath();
            if (File.Exists(rubyPath)) return true;

#if UNITY_EDITOR_WIN
            EditorUtility.DisplayDialog(
                "Rubyインストールエラー",
                "必要なRubyバージョンがインストールされていません。\ntools/setup.batを実行してください。\n\nbatの完了後、再度同じ手順を実行してください",
                "OK");
#else
            EditorUtility.DisplayDialog(
                "Rubyインストールエラー",
                "rbenvがセットアップされていません。\nコンフルの手順に従って構築して下さい。\n\n対応後、再度同じ手順を実行してください",
                "OK");
#endif
            return false;
        }

        // エラーが発生していたら true を返す
        private static bool ForwardConsoleLog(Process p, bool withGenerate)
        {
            ForwardConsoleLog_SetHandler(p, out var output, out var error);

            // 子プロセスの処理を待つ
            p.WaitForExit();

            return ForwardConsoleLog_AfterExit(p, withGenerate, output, error);
        }

        private static void ForwardConsoleLog_SetHandler(Process p, out StringBuilder out_output, out StringBuilder out_error)
        {
            // 標準出力された時のハンドラーを宣言
            out_output = new StringBuilder();
            var output = out_output; // outで返す値をローカル関数内で使えないため_をつけてローカル変数に落とす
            void OutputHander(object sender, DataReceivedEventArgs args)
            {
                if (!string.IsNullOrEmpty(args.Data))
                {
                    output.AppendLine(args.Data);
                }
            }

            // エラー出力された時のハンドラーを宣言
            out_error = new StringBuilder();
            var error = out_error; // outで返す値をローカル関数内で使えないため_をつけてローカル変数に落とす
            void ErrorHander(object sender, DataReceivedEventArgs args)
            {
                if (!string.IsNullOrEmpty(args.Data))
                {
                    // nullをチェックしないでAppendLineすると空行が追加されるてしまい
                    // エラーがないばずなのにOutputErrorAndWarning()でエラーがありますと表示される.
                    error.AppendLine(args.Data);
                }
            }

            // ハンドラーを登録
            p.OutputDataReceived += OutputHander;
            p.ErrorDataReceived += ErrorHander;

            // 子プロセスの出力読み込み開始
            p.BeginOutputReadLine();
            p.BeginErrorReadLine();
        }

        private static bool ForwardConsoleLog_AfterExit(Process p, bool withGenerate, StringBuilder output, StringBuilder error, string errorReportPath = ERROR_REPORT_PATH, SynchronizationContext context = null)
        {
            // 子プロセスを破棄
            p.Dispose();

            // 標準出力をコンソールに流す
            UnityEngine.Debug.Log(output);

            // エラー出力をコンソールに流す
            OutputErrorAndWarning(error, withGenerate, out var isError, errorReportPath, context);

            return isError;
        }

        private static void OutputErrorAndWarning(StringBuilder sb, bool withGenerate, out bool isError, string errorReportPath = ERROR_REPORT_PATH, SynchronizationContext context = null)
        {
            isError = false;

            if (sb.Length == 0)
            {
                UnityEngine.Debug.Log("<color=cyan>エラーや警告はありません。</color>");
                return;
            }

            using (StringReader reader = new StringReader(sb.ToString()))
            {
                StringBuilder warning = new StringBuilder();
                StringBuilder error = new StringBuilder();
                bool isCriticalWarning = false;

                string line;
                while ( (line = reader.ReadLine()) != null)
                {
                    if (line.Contains("[WARN]"))
                    {
                        warning.AppendLine(line);
                    }
                    else if (line.Contains("[ERROR]") && line.Contains("0 errors,"))
                    {
                        // DSLの制約条件でas_warningを使うと[ERROR]付きでコンソール出力されてしまう
                        // UnityEditorでCSVビルドする人が「MDBが出力されていない」と勘違いするのを回避するため警告表示にする
                        // 単純にwarningに含めてしまうとas_warning系の重要な警告を見逃してしまうので通常のwarningとは別扱いにする
                        isCriticalWarning = true;
                    }
                    else
                    {
                        error.AppendLine(line);
                    }
                }

                if (warning.Length > 0)
                {
                    // TODO:@takahashi_reo: 形骸化しているので修正したい
                    // 「ignored empty reference」系の警告は
                    // ・unique_reference等のカラム名指定が間違っている
                    // ・サーバー側にしかMDBがない
                    // のどちらかなので、前者だけ引っかかるようにしたい
                    UnityEngine.Debug.LogWarning(warning);
                    UnityEngine.Debug.Log("<color=yellow>警告があります。</color>");
                }

                if (error.Length > 0)
                {
                    // Unityのルートディレクトリ取得
                    var projectDirectory = System.IO.Directory.GetCurrentDirectory();

                    UnityEngine.Debug.LogError(error);
                    UnityEngine.Debug.Log("<color=red>エラーがあります。</color>");
                    UnityEngine.Debug.LogError("詳細は以下を参照してください。\n" + $"{projectDirectory}/{errorReportPath}");
                    
                    OpenURL($"file://{projectDirectory}/{errorReportPath}");

                    isError = true;
                }
                else if (isCriticalWarning)
                {
                    // エラーもなくて重要度の高い警告のみの場合
                    var projectDirectory = System.IO.Directory.GetCurrentDirectory();
                    var description = withGenerate
                        ? "MDBは出力されましたが<color=yellow>重要度の高い警告があります。機能担当者にご連絡ください</color>"
                        : "<color=yellow>重要度の高い警告があります。機能担当者にご連絡ください</color>";
                    UnityEngine.Debug.LogWarning(description);
                    UnityEngine.Debug.LogWarning("<color=yellow>詳細は以下を参照してください</color>\n" + $"{projectDirectory}/{errorReportPath}");
                    OpenURL($"file://{projectDirectory}/{errorReportPath}");
                }
            }

            void OpenURL(string url)
            {
                if (context != null)
                {
                    // Unityメインスレッドでしか動かせないので指示を送る
                    context.Post(_ => Application.OpenURL(url), null);
                }
                else
                {
                    Application.OpenURL(url);
                }
            }
        }

        // 並列実行時の各プロセス管理用クラス
        class RubyProcess: IDisposable
        {
            private Process _p;
            private string _errorReportPath;
            private int _divIndex;
            public bool IsError { get; set; }

            public RubyProcess(int divIndex)
            {
                _p = new Process();
                _divIndex = divIndex;
                _errorReportPath = string.Format(ERROR_REPORT_NUM_PATH_FORMAT, divIndex);
            }
            
            public RubyProcess(int divIndex, int processId, out bool result) : this(divIndex)
            {
                result = false;

                try
                {
                    _p = Process.GetProcessById(processId);
                }
                catch (ArgumentException)
                {
                    // プロセスIDが見つからなかった場合
                    return;
                }

                // 念のため ruby かどうか調べる
                if (_p.ProcessName.Contains("ruby"))
                {
                    result = true;
                }
            }

            public void Run(string arguments)
            {
                RunRubyProcess(_p, arguments);
            }

            public void WaitForExit(bool withGenerate)
            {
                _p.WaitForExit();
                AfterExit(withGenerate);
            }

            public void AfterExit(bool withGenerate, SynchronizationContext context = null)
            {
                IsError = ForwardConsoleLog_AfterExit(_p, withGenerate, GetOutputStringBuilder(), GetErrorStringBuilder(), _errorReportPath, context);
            }

            public void Dispose()
            {
                if (_p != null)
                {
                    _p.Dispose();
                    _p = null;
                }
            }

            public int GetProcessId()
            {
                return _p.Id;
            }

            public void SetExitHandler(Action exitHandler, bool withGenerate, SynchronizationContext context)
            {
                _p.EnableRaisingEvents = true;
                _p.Exited += (sender, args) =>
                {
                    AfterExit(withGenerate, context);
                    exitHandler();
                    Dispose();
                };
            }

            private StringBuilder GetStringBuilderFromFile(string path)
            {
                // 後続処理を使いまわすためテキストをStringBuilderに詰めて返す
                var sb = new StringBuilder();
                foreach (var line in File.ReadLines(path))
                {
                    sb.AppendLine(line);
                }
                return sb;
            }

            private StringBuilder GetOutputStringBuilder()
            {
                return GetStringBuilderFromFile(string.Format(STDOUT_NUM_PATH_FORMAT, _divIndex));
            }

            private StringBuilder GetErrorStringBuilder()
            {
                return GetStringBuilderFromFile(string.Format(STDERR_NUM_PATH_FORMAT, _divIndex));
            }
        }

        public static void BackgroundRubyProcessExited(int index, SynchronizationContext context = null)
        {
            RunningBackgroundProcessIds[index] = 0;
            RepaintBackgroundProcessProgress(context);
        }

        static void RepaintBackgroundProcessProgress(SynchronizationContext context = null)
        {
            // ボタンのラベル文字列を進捗％に更新
            var all = RunningBackgroundProcessIds.Length;
            var exited = RunningBackgroundProcessIds.Count(id => id == 0);
            var completed = exited * 100.0 / all;
            _generateButtonLabel = $"ﾁｪｯｸ {completed:F1}%";

            // Generateボタンの表示を更新するために再描画する
            if (context != null)
            {
                //  Unityメインスレッドでしか動かせないので指示を送る
                context.Post(_ => DoRepaint(), null);
            }
            else
            {
                DoRepaint();
            }

            void DoRepaint()
            {
                if (HasOpenInstances<DSLCoordinator>())
                {
                    GetWindow().Repaint();
                }
            }
        }

        /// <summary>
        /// ゲーム実行時などスクリプトがリロードされた時にstaticメンバの値が破棄されてしまう
        /// そのためプロセスIDを退避しそれを元にProcessオブジェクトを再度作り、イベントハンドラを復元する
        /// </summary>
        [UnityEditor.Callbacks.DidReloadScripts]
        static void OnReloadScripts()
        {
            RepaintBackgroundProcessProgress();
            RestoreRunningBackgroundProcessesExitedHandler();
        }

        /// <summary>
        /// ScriptableSingletonの値はリロード時に破棄されないため、staticメンバで残したい値はここにセットする
        /// </summary>
        class DSLCoordinatorStaticValueHolder : ScriptableSingleton<DSLCoordinatorStaticValueHolder>
        {
            public int[] runningBackgroundProcessIds = new int[0];
        }

        static void RestoreRunningBackgroundProcessesExitedHandler()
        {
            var context = SynchronizationContext.Current;
            for (int i = 0; i < RunningBackgroundProcessIds.Length; i++)
            {
                var index = i;
                var processId = RunningBackgroundProcessIds[i];
                if (processId > 0)
                {
                    var rubyProcess = new RubyProcess(i + 1, processId, out var result);
                    if (!result)
                    {
                        // 実行中だったはずのプロセスが取れないときは終了してるので終了処理を直ちにする
                        rubyProcess.AfterExit(true);
                        BackgroundRubyProcessExited(index);
                        continue;
                    }

                    rubyProcess.SetExitHandler(() => BackgroundRubyProcessExited(index, context), true, context);
                }
            }
        }

        /// <summary>
        /// UnityがCSVの変更を検知したときに呼び出される
        /// </summary>
        public static void OnCSVUpdated()
        {
#if CYG_DEBUG
            var enableAutoGenerateOnCSVChanged = EditorPrefs.GetBool(PREF_KEY_ENABLE_AUTO_GENERATE_ON_CSV_CHANGED);
            if (enableAutoGenerateOnCSVChanged && MasterDataManager.IsUseMasterMDBLocal) // 自動GenerateはUse Local MDB 有効時のみ発動（無効時にやる意味がない）
            {
                try
                {
                    // ウィンドウが開いているならウィンドウに設定された値でGenerateしたいのでインスタンスメソッド呼出
                    // ウィンドウが開いてないなら静的メソッド呼出
                    if (HasOpenInstances<DSLCoordinator>())
                    {
                        GetWindow().GenerateMasterMdbWithGUIParameter();
                    }
                    else
                    {
                        GenerateMasterMdb();
                    }
                }
                catch
                {
                    // 万一こけたときこけ続けるのは避けたいので、何らかの例外が飛んだら自動更新はOFFにする
                    EditorPrefs.SetBool(PREF_KEY_ENABLE_AUTO_GENERATE_ON_CSV_CHANGED, false);
                    UnityEngine.Debug.LogWarning("CSV更新時に自動的にGenerateしたらエラーが発生したため、設定はOFFになりました");
                    throw;
                }
            }
#endif
        }
    }

    /// <summary>
    /// UnityにCSVの変更を通知してもらうためのクラス
    /// </summary>
    public class CSVUpdateDetector : AssetPostprocessor
    {
        private static readonly System.Text.RegularExpressions.Regex Pattern = new System.Text.RegularExpressions.Regex(@"^Assets/_GallopCsv/.+/.+\.csv$");
        private static readonly Func<string, bool> IsCSVAsset = path => Pattern.Match(path).Success;
        
        /// <summary>
        /// Unityから呼ばれるイベント関数
        /// 更新されたアセットの中にCSVが含まれていれば、後続のイベント関数を呼び出す
        /// </summary>
        /// <param name="importedAssetPaths"></param>
        /// <param name="deletedAssetPaths"></param>
        /// <param name="movedAssetPaths"></param>
        /// <param name="movedFromAssetPaths"></param>
        private static void OnPostprocessAllAssets(string[] importedAssetPaths, string[] deletedAssetPaths, string[] movedAssetPaths, string[] movedFromAssetPaths)
        {
            if (IsIncludedCSVUpdate(importedAssetPaths, deletedAssetPaths, movedAssetPaths, movedFromAssetPaths))
            {
                // いったんイベントをハードコードで呼び出し
                // CSV更新時に他に呼びたいイベント関数が増えたときはとりあえずここに書く
                DSLCoordinator.OnCSVUpdated();
            }
        }

        /// <summary>
        /// 更新されたアセットの情報を入力してCSV更新が含まれるかどうか調べます
        /// </summary>
        /// <param name="importedAssetPaths"></param>
        /// <param name="deletedAssetPaths"></param>
        /// <param name="movedAssetPaths"></param>
        /// <param name="movedFromAssetPaths"></param>
        /// <returns></returns>
        private static bool IsIncludedCSVUpdate(string[] importedAssetPaths, string[] deletedAssetPaths, string[] movedAssetPaths, string[] movedFromAssetPaths)
        {
            return importedAssetPaths.Any(IsCSVAsset) || deletedAssetPaths.Any(IsCSVAsset) || movedAssetPaths.Any(IsCSVAsset) || movedFromAssetPaths.Any(IsCSVAsset);
        }
    }
}
#endif // UNITY_EDITOR
