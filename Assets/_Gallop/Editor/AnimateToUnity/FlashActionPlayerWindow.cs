#if UNITY_EDITOR && CYG_DEBUG
using UnityEngine;
using UnityEditor;
using Gallop;

public class FlashActionPlayerWindow : EditorWindow
{
    [MenuItem("Gallop/UI/Flash Action Player Window", false, EditorMenuPriority.UI)]
    public static void ShowWindow()
    {
        ShowWindow(null);
    }

    public static void ShowWindow(FlashActionPlayer actionPlayer = null)
    {
        var window = GetWindow<FlashActionPlayerWindow>();
        window.minSize = WINDOW_MIN_SIZE;
        if (actionPlayer != null) window._actionPlayer = actionPlayer;
    }

    private static readonly Vector2 WINDOW_MIN_SIZE = new Vector2(600, 300);// ウインドウ最小サイズ
    private const string FILTER = "fa_";// FlashActionファイル名フィルタ

    private GameObject _guiFlashActionPrefab;
    private bool _guiFlashActionPrefabPicker;
    private FlashActionPlayer _actionPlayer;

    /// <summary>
    /// GUI
    /// </summary>
    public void OnGUI()
    {
        GUILayout.Label("FlashActionPlayerの再生を確認するツール");

        GUILayout.Space(10);

        GUILayout.Label("FlashActionPlayerのコンポーネントが含まれるプレファブを選択してください");
        using (new EditorGUILayout.HorizontalScope())
        {
            _guiFlashActionPrefab = (GameObject) EditorGUILayout.ObjectField("FlashActionPrefab", _guiFlashActionPrefab, typeof(GameObject), true);

            if (GUILayout.Button("選択"))
            {
                int controlID = GUIUtility.GetControlID(FocusType.Passive);
                EditorGUIUtility.ShowObjectPicker<GameObject>(null, false, FILTER, controlID);
                _guiFlashActionPrefabPicker = true;
            }

            if (Event.current.commandName == "ObjectSelectorClosed" && _guiFlashActionPrefabPicker)
            {
                _guiFlashActionPrefab = EditorGUIUtility.GetObjectPickerObject() as GameObject;
                _guiFlashActionPrefabPicker = false;
            }
        }

        if (UIManager.HasInstance() == false)
        {
            GUILayout.Label("ゲームを起動してください");
            return;
        }

        if (_guiFlashActionPrefab != null)
        {
            GUILayout.Space(10);

            using (new EditorGUILayout.HorizontalScope())
            {
                if (GUILayout.Button("シーン上に生成する"))
                {
                    CreateActionPlayer();
                }
                if (_actionPlayer != null && _actionPlayer.FlashPlayer != null)
                {
                    using (new EditorUtil.ScopeBGColor(Color.yellow))
                    {
                        if (GUILayout.Button("再適用", GUILayout.Width(80)))
                        {
                            _actionPlayer.EditorApplay();
                        }
                    }
                }
            }
        }

        if (_actionPlayer != null && _actionPlayer.FlashPlayer != null)
        {
            GUILayout.Label("下記のパラメータを再生できます");
            foreach (var actionParam in _actionPlayer.EditorActionParameterList)
            {
                if (GUILayout.Button($"{actionParam.MotionObjectName} > {actionParam.MotionLabel}"))
                {
                    _actionPlayer.EditorPlay(actionParam);
                }
            }
        }
    }

    /// <summary>
    /// FlashActionPlayer生成
    /// </summary>
    private void CreateActionPlayer()
    {
        // 生成済み破棄
        if (_actionPlayer != null)
        {
            Destroy(_actionPlayer.gameObject);
            _actionPlayer = null;
        }

        var flashPrefabInstance = GameObject.Instantiate(_guiFlashActionPrefab, UIManager.MainCanvas.transform);
        _actionPlayer = flashPrefabInstance.GetComponent<FlashActionPlayer>();
        if (_actionPlayer == null)
        {
            ShowNotification(new GUIContent("対象のプレファブにはFlashActionPlayerコンポーネントが含まれていません"));
        }
        else
        {
            Selection.activeObject = _actionPlayer.gameObject;
            ShowNotification(new GUIContent("生成しました"));
            _actionPlayer.LoadFlashPlayer();
        }        
    }
}
#endif