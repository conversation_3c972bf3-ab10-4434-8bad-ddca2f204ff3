#if CYG_DEBUG && UNITY_EDITOR
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace FacialDrivenKeyRecorder
{
    /// ******************************************************************************************
    /// <summary>
    /// FacialDrivenKeyRecorderInspector
    /// FacialDrivenKeyRecorderのインスペクタ
    /// </summary>
    /// ******************************************************************************************
    [CustomEditor(typeof(FacialDrivenKeyRecorder))]
    [CanEditMultipleObjects]
    public class FacialDrivenKeyRecorderInspector : UnityEditor.Editor
    {
        //======================================================================
        // 変数
        //======================================================================

        private List<FacialDrivenKeyRecorder> _targetList = null;

        //======================================================================
        // メソッド
        //======================================================================

        /// ----------------------------------------
        /// <summary>
        /// OnInspectorGUI
        /// </summary>
        /// ----------------------------------------
        public override void OnInspectorGUI()
        {
            CreateTargetList();

            if (_targetList.Count == 0)
            {
                return;
            }

            serializedObject.Update();

            if (GUILayout.Button("Start\n(Shortcut J)", new GUILayoutOption[] { GUILayout.Height(30) }))
            {
                StartRecording();
            }

            if (GUILayout.Button("Pause And Resume\n(Shortcut K)", new GUILayoutOption[] { GUILayout.Height(30) }))
            {
                PauseAndResumeRecording();
            }

            if (GUILayout.Button("Reset\n(Shortcut L)", new GUILayoutOption[] { GUILayout.Height(30) }))
            {
                ResetRecording();
            }

            if (GUILayout.Button("Export\n(Shortcut N)", new GUILayoutOption[] { GUILayout.Height(30) }))
            {
                ExportData();
            }

            if (GUILayout.Button("Open Output Folder\n(Shortcut M)", new GUILayoutOption[] { GUILayout.Height(30) }))
            {
                OpenOutputDirectory();
            }

            DrawDefaultInspector();

            serializedObject.ApplyModifiedProperties();
        }

        /// ----------------------------------------
        /// <summary>
        /// 対象リスト作成
        /// </summary>
        /// ----------------------------------------
        private void CreateTargetList()
        {
            if (_targetList == null)
            {
                _targetList = new List<FacialDrivenKeyRecorder>();
            }

            _targetList.Clear();

            foreach (Object target in targets)
            {
                FacialDrivenKeyRecorder info = target as FacialDrivenKeyRecorder;

                if (info == null)
                {
                    continue;
                }

                _targetList.Add(info);
            }
        }

        /// ----------------------------------------
        /// <summary>
        /// レコーディング開始
        /// </summary>
        /// ----------------------------------------
        private void StartRecording()
        {
            foreach (FacialDrivenKeyRecorder target in _targetList)
            {
                target.StartRecording();
            }
        }

        /// ----------------------------------------
        /// <summary>
        /// ポーズとレジュームの切替え
        /// </summary>
        /// ----------------------------------------
        private void PauseAndResumeRecording()
        {
            foreach (FacialDrivenKeyRecorder target in _targetList)
            {
                target.PauseAndResumeRecording();
            }
        }

        /// ----------------------------------------
        /// <summary>
        /// レコーディングリセット
        /// </summary>
        /// ----------------------------------------
        private void ResetRecording()
        {
            foreach (FacialDrivenKeyRecorder target in _targetList)
            {
                target.ResetRecording();
            }
        }

        /// ----------------------------------------
        /// <summary>
        /// データ出力
        /// </summary>
        /// ----------------------------------------
        private void ExportData()
        {
            foreach (FacialDrivenKeyRecorder target in _targetList)
            {
                target.ExportData();
            }
        }

        /// ----------------------------------------
        /// <summary>
        /// 出力ディレクトリを開く
        /// </summary>
        /// ----------------------------------------
        private void OpenOutputDirectory()
        {
            foreach (FacialDrivenKeyRecorder target in _targetList)
            {
                target.OpenOutputDirectory();

                break;
            }
        }
    }
}
#endif