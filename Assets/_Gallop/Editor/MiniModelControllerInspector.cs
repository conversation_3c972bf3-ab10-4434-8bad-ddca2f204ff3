#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Gallop.Model.Component;

namespace Gallop
{
    /// <summary>
    /// MiniModelControllerのInspector拡張
    /// </summary>
    [CustomEditor(typeof(MiniModelController))]
    public class MiniModelControllerInspector : ModelControllerBehaviourInspector
    {
        private bool _cySpringFoldOut = false;
        private bool _drivenKeyFoldOut = false;
        private int _systemVoiceCharaId = 1001;
        private int _systemVoiceId = 20021;

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            var ctrl = target as MiniModelController;
            if (ctrl == null)
            {
                return;
            }
#if CYG_DEBUG
            _cySpringFoldOut = EditorGUILayout.Foldout(_cySpringFoldOut, "CySpring");
            if (_cySpringFoldOut)
            {
                EditorGUILayout.BeginVertical(GUI.skin.box);

                var isDrawGizmos = EditorGUILayout.Toggle("DrawGizmos", ctrl.IsDrawGizmos);
                if (ctrl.IsDrawGizmos != isDrawGizmos)
                {
                    // Setter内部で色々呼んでるので、変化がある時のみ反映
                    ctrl.IsDrawGizmos = isDrawGizmos;
                }

                var cySpringController = ctrl.GetCySpringControllerForEditor();
                if (cySpringController != null)
                {
                    cySpringController.OnInspectorGUI();
                }

                EditorGUILayout.EndVertical();
            }
#endif
            _drivenKeyFoldOut = EditorGUILayout.Foldout(_drivenKeyFoldOut, "DrivenKey");
            if (_drivenKeyFoldOut)
            {
                EditorGUILayout.BeginVertical(GUI.skin.box);
                var drivenKey = ctrl.DrivenKeyComponent;
                if (drivenKey != null)
                {
                    drivenKey.OnInspectorGUI();
                }
                EditorGUILayout.EndVertical();
            }

            GUILayout.Label("システムボイステスト");
            _systemVoiceCharaId = EditorGUILayout.IntField("_systemVoiceCharaId", _systemVoiceCharaId);
            _systemVoiceId = EditorGUILayout.IntField("_systemVoiceId", _systemVoiceId);
            if (GUILayout.Button("システムテキストから再生"))
            {
                var data = MasterDataManager.Instance.masterCharacterSystemText.Get(_systemVoiceCharaId, _systemVoiceId);
                AudioManager.Instance.PlaySystemVoiceByElement(data);
                ctrl.PlaySystemText(data);
            }
            if (ctrl.TryGetModelComponent<MiniLipSync>(out var lipSync))
            {
                lipSync.OpenVolumeSingle = EditorGUILayout.IntField("OpenVolumeSingle", lipSync.OpenVolumeSingle);
                lipSync.OpenVowelA = EditorGUILayout.Slider("OpenVowelA", lipSync.OpenVowelA, 0f, 1f);
                lipSync.OpenVowelI = EditorGUILayout.Slider("OpenVowelI", lipSync.OpenVowelI, 0f, 1f);
                lipSync.OpenVowelU = EditorGUILayout.Slider("OpenVowelU", lipSync.OpenVowelU, 0f, 1f);
                lipSync.OpenVowelE = EditorGUILayout.Slider("OpenVowelE", lipSync.OpenVowelE, 0f, 1f);
                lipSync.OpenVowelO = EditorGUILayout.Slider("OpenVowelO", lipSync.OpenVowelO, 0f, 1f);
            }
        }
    }
}
#endif