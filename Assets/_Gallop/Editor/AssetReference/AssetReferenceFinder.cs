using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class AssetReferenceFinder : EditorWindow
{
    private const string PrefsName = "AssetReferenceFinder_extensions";

    // 検索対象のオブジェクト
    private Object _targetObject;
    // 結果表示のスクロールポジション
    private Vector2 _scrollPos;
    // 該当したアセットのパスを格納するリスト
    private List<string> _assetPaths = new List<string>();

    // 対象とする拡張子
    private static readonly string[] Extensions = new string[]
    {
        "*.mat",
        "*.prefab",
        "*.unity",
        "*.asset",
        "*.controller",
    };

    // 各拡張子の有効・無効
    private bool[] _enableArray = null;

    private System.Text.StringBuilder _builder = null;

    [MenuItem ("Gallop/Tools/Asset Reference/AssetReferenceFinder", false, Gallop.EditorMenuPriority.TOOLS)]
    static void ShowWindow()
    {
        EditorWindow.GetWindow<AssetReferenceFinder>();
    }

    /// <summary>
    /// UI
    /// </summary>
    private void OnGUI()
    {
        if (_enableArray == null)
        {
            LoadPrefs();
        }

        EditorGUILayout.LabelField("Assetの参照元ファイルを検索します。");
        _targetObject = EditorGUILayout.ObjectField("Asset", _targetObject, typeof(Object), false);

        using (var check = new EditorGUI.ChangeCheckScope())
        {
            // 拡張子ごとのチェックボックス
            for (int i = 0; i < Extensions.Length; ++i)
            {
                _enableArray[i] = GUILayout.Toggle(_enableArray[i], Extensions[i]);
            }

            if (check.changed)
            {
                SavePrefs();
            }
        }

        // 検索ボタン
        if (GUILayout.Button("検索"))
        {
            OnClickSearchButton();
        }

        GUILayout.Box("", GUILayout.Width(position.width), GUILayout.Height(1));

        EditorGUILayout.LabelField("検索結果");

        _scrollPos = EditorGUILayout.BeginScrollView(_scrollPos, GUI.skin.box);
        {
            ShowResult();
        }

        EditorGUILayout.EndScrollView();
    }

    /// <summary>
    /// 検索ボタンをクリックした際の挙動です
    /// </summary>
    private void OnClickSearchButton()
    {
        if (_targetObject == null)
        {
            ShowIndicateErrorDialog();
            return;
        }

        Search();
        ShowResultDialog();

        OnGUI();
    }

    /// <summary>
    /// 検索
    /// </summary>
    private void Search()
    {
        _assetPaths.Clear();

        string path = AssetDatabase.GetAssetPath(_targetObject);
        string guid = AssetDatabase.AssetPathToGUID(path);

        List<string> extensions = new List<string>(Extensions.Length);
        for (int i = 0; i < _enableArray.Length; ++i)
        {
            if (!_enableArray[i]) { continue; }
            extensions.Add(Extensions[i]);
        }
        int extMax = extensions.Count;
        int extCount = 0;

        foreach (string extension in extensions)
        {
            string[] files = Directory.GetFiles(Application.dataPath, extension, SearchOption.AllDirectories);

            extCount++;
            int fileMax = files.Length;
            int fileCount = 0;

            foreach (string file in files)
            {
                fileCount++;
                string display = string.Format("AssetReferenceFinder: ext ({0} / {1} : {4}) file ({2} / {3})", extCount, extMax, fileCount, fileMax, extension);
                float progress = (float)fileCount / (float)fileMax;
                EditorUtility.DisplayProgressBar(display, "検索中...", progress);

                if (File.ReadAllText(file).IndexOf(guid) >= 0)
                {
                    _assetPaths.Add(file);
                }
            }
        }
        EditorUtility.ClearProgressBar();
    }

    /// <summary>
    /// EditorPrefsから各拡張子の有効・無効を復帰
    /// </summary>
    private void LoadPrefs()
    {
        _enableArray = new bool[Extensions.Length];

        var prefsString = EditorPrefs.GetString(PrefsName);
        if (string.IsNullOrEmpty(prefsString)) { return; }

        var prefsExt = prefsString.Split(new char[] {'\n'});

        var extensionList = Extensions.ToList();
        for (int i = 0; i < prefsExt.Length; ++i)
        {
            var index = extensionList.IndexOf(prefsExt[i]);
            if (index < 0) { continue; }

            _enableArray[index] = true;
        }
    }

    /// <summary>
    /// EditorPrefsに各拡張子の有効・無効を保存
    /// </summary>
    private void SavePrefs()
    {
        if (_builder == null)
        {
            _builder = new System.Text.StringBuilder();
        }
        _builder.Length = 0;

        for (int i = 0; i < Extensions.Length; ++i)
        {
            if (!_enableArray[i]) { continue; }

            _builder.Append(Extensions[i]);
            _builder.Append("\n");
        }
        
        EditorPrefs.SetString(PrefsName, _builder.ToString());
    }

    /// <summary>
    /// 結果表示
    /// </summary>
    private void ShowResult()
    {
        foreach(string path in _assetPaths)
        {
            string localPath = path.Substring(path.IndexOf("Assets"));
            // ObjectFieldの副作用を利用して、結果をクリックした際に該当のディレクトリを開いている
            // 第一引数に直接結果を代入しているので、中身は書き換わらないようにしている
            EditorGUILayout.ObjectField(AssetDatabase.LoadMainAssetAtPath(localPath), typeof(Object), false);
        }
    }

    /// <summary>
    /// 何も選択されていない時のエラーメッセージ
    /// </summary>
    private void ShowIndicateErrorDialog()
    {
        EditorUtility.DisplayDialog(
            "エラー",
            "ファイルが選択されていません",
            "OK"
        );
    }

    /// <summary>
    /// 結果表示
    /// </summary>
    private void ShowResultDialog()
    {
        EditorUtility.DisplayDialog(
            "検索完了",
            _assetPaths.Count + "件のデータが見つかりました",
            "OK"
        );
    }
}
