#if CYG_DEBUG
using UnityEngine;
using UnityEditor;


/// <summary>
/// カラーを取得するためのウインドウ
/// 主にプランナーが画面の特定の色番号を知りたい時に使用する
/// </summary>
public class ColorWindow : EditorWindow
{

    [MenuItem("Gallop/Tools/Color Window",false,Gallop.EditorMenuPriority.TOOLS)]
    public static void ShowWindow()
    {
        GetWindow<ColorWindow>();
    }

    private Color _color = Color.white;

    private void OnGUI()
    {
        _color = EditorGUILayout.ColorField("選択した色",_color);
    }
}
#endif