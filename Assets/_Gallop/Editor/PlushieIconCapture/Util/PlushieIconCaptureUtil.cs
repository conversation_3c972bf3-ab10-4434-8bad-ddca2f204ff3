#if UNITY_EDITOR && CYG_DEBUG
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Gallop
{
    public static class PlushieIconCaptureUtil
    {
        public static void GetAllPlushieIconSetting(Action<IReadOnlyList<PlushieIconSetting>> onCompleted)
            => GetAllPlushieIconSetting(DateTime.Now, onCompleted);
        
        /// <summary>
        /// 全てのぬいぐるみ設定情報を作成する
        /// </summary>
        public static void GetAllPlushieIconSetting(DateTime selectTime, Action<IReadOnlyList<PlushieIconSetting>> onCompleted)
        {
            // UNIXエポックを表すDateTimeオブジェクトを取得
            var UNIX_EPOCH = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);

            var targetTime = selectTime
                .ToUniversalTime();
            var currentTime = (long)(targetTime - UNIX_EPOCH)
                .TotalSeconds;

            var settingList = MasterDataManager
                .Instance
                .masterCharaData
                .GetDictInTerm(false, currentTime)
                .Select(x => new PlushieIconSetting(x.Value.Id))
                .ToArray();

            onCompleted.Call(settingList);
        }
        
        /// <summary>
        /// ディレクトリ上にモデル情報が存在するか確認する
        /// </summary>
        public static bool ExistTargetMiniCharaDressResources(int charaId, int dressId)
        {
            var buildInfo = new CharacterBuildInfo(charaId, dressId, ModelLoader.ControllerType.Mini);
            var path = buildInfo.CharaBuildPathInfo.upBodyPrefabPath;

            return ResourceManager.IsExistAsset(path);
        }
    }
}
#endif