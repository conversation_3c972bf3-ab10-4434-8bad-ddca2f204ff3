using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;


namespace Gallop
{
    /// <summary>
    /// EffectParamインスペクタ拡張
    /// ノード名をポップアップ選択式にするために用意する
    /// </summary>
    [CustomEditor(typeof(EffectParam))]
    public class EffectParamInspector : Editor
    {
        struct TransformParamProperty
        {
            public SerializedProperty _objProp;
            public SerializedProperty _positionNodeNameProp;
            public SerializedProperty _positionOffsetPorp;
            public SerializedProperty _enableRotXPorp;
            public SerializedProperty _enableRotYProp;
            public SerializedProperty _enableRotZProp;
            public SerializedProperty _rotationNodeNameProp;
            public SerializedProperty _rotationOffsetProp;
        }

        struct BillboardParamProperty
        {
            public SerializedProperty _objProp;
            public SerializedProperty _typeProp;
        }

        struct CamOffsetProperty
        {
            public SerializedProperty _objProp;
            public SerializedProperty _lengthProp;
        }

        private List<TransformParamProperty> _transformParamPropList = null;
        private List<BillboardParamProperty> _billboardParamPropList = null;
        private List<CamOffsetProperty> _camOffsetPropList = null;

        private SerializedProperty _transformParamProp = null;
        private SerializedProperty _billboardParamProp = null;
        private SerializedProperty _camOffsetProp = null;
        private SerializedProperty _lightProbeRendererProp = null;
        private SerializedProperty _rightFootProp = null;
        private SerializedProperty _leftFootProp = null;

        private SerializedProperty _charaScaleProp = null;
        private SerializedProperty _seIdProp = null;

        // ノード名リスト
        private static readonly string[] NodeNames =
        {
            "Position",
            "Root",
            "Hip",
            "Waist",
            "Spine",
            "Chest",
            "Bust",
            "Knee_L",
            "Ankle_L",
            "Knee_R",
            "Ankle_R",
        };

        // 折り畳み状態
        private List<bool> _transformParamFoldoutList = null;
        private List<bool> _billboardParamFoldoutList = null;
        private List<bool> _camOffsetFoldoutList = null;
        private bool _transformParamFoldout = true;
        private bool _billboardParamFoldout = true;
        private bool _camOffsetFoldout = true;
        private bool _lightProbeRendererTargetFoldout = true;

        /// <summary>
        /// SerializedProperty初期化
        /// </summary>
        private void OnEnable()
        {
            _transformParamProp = serializedObject.FindProperty("_transFormParam");
            if (_transformParamProp != null && _transformParamProp.isArray)
            {
                _transformParamPropList = new List<TransformParamProperty>(_transformParamProp.arraySize);
                _transformParamFoldoutList = new List<bool>(_transformParamProp.arraySize);
                for (int i = 0; i < _transformParamProp.arraySize; ++i)
                {
                    _transformParamPropList.Add(GetTransformParamProperty(_transformParamProp.GetArrayElementAtIndex(i)));
                    _transformParamFoldoutList.Add(true);
                }
            }

            _billboardParamProp = serializedObject.FindProperty("_billboardParam");
            if (_billboardParamProp != null && _billboardParamProp.isArray)
            {
                _billboardParamPropList = new List<BillboardParamProperty>(_billboardParamProp.arraySize);
                _billboardParamFoldoutList = new List<bool>(_billboardParamProp.arraySize);
                for (int i = 0; i < _billboardParamProp.arraySize; ++i)
                {
                    _billboardParamPropList.Add(GetBillboardParamProperty(_billboardParamProp.GetArrayElementAtIndex(i)));
                    _billboardParamFoldoutList.Add(true);
                }
            }

            _camOffsetProp = serializedObject.FindProperty("_camOffset");
            if (_camOffsetProp != null && _camOffsetProp.isArray)
            {
                _camOffsetPropList = new List<CamOffsetProperty>(_camOffsetProp.arraySize);
                _camOffsetFoldoutList = new List<bool>(_camOffsetProp.arraySize);
                for (int i = 0; i < _camOffsetProp.arraySize; ++i)
                {
                    _camOffsetPropList.Add(GetCamOffsetProperty(_camOffsetProp.GetArrayElementAtIndex(i)));
                    _camOffsetFoldoutList.Add(true);
                }
            }

            _lightProbeRendererProp = serializedObject.FindProperty("_lightProbeTargetRenderer");
            _rightFootProp = serializedObject.FindProperty("_rightFoot");
            _leftFootProp = serializedObject.FindProperty("_leftFoot");
            _charaScaleProp = serializedObject.FindProperty("_isCharaScale");
            _seIdProp = serializedObject.FindProperty("_seId");
        }

        private TransformParamProperty GetTransformParamProperty(SerializedProperty parent)
        {
            TransformParamProperty param = new TransformParamProperty();
            param._objProp = parent.FindPropertyRelative("_obj");
            param._positionNodeNameProp = parent.FindPropertyRelative("_positionNodeName");
            param._positionOffsetPorp = parent.FindPropertyRelative("_positionOffset");
            param._enableRotXPorp = parent.FindPropertyRelative("_enable_rotX");
            param._enableRotYProp = parent.FindPropertyRelative("_enable_rotY");
            param._enableRotZProp = parent.FindPropertyRelative("_enable_rotZ");
            param._rotationNodeNameProp = parent.FindPropertyRelative("_rotationNodeName");
            param._rotationOffsetProp = parent.FindPropertyRelative("_rotationOffset");
            return param;
        }

        private BillboardParamProperty GetBillboardParamProperty(SerializedProperty parent)
        {
            BillboardParamProperty param = new BillboardParamProperty();
            param._objProp = parent.FindPropertyRelative("_obj");
            param._typeProp = parent.FindPropertyRelative("_type");
            return param;
        }

        private CamOffsetProperty GetCamOffsetProperty(SerializedProperty parent)
        {
            CamOffsetProperty param = new CamOffsetProperty();
            param._objProp = parent.FindPropertyRelative("_obj");
            param._lengthProp = parent.FindPropertyRelative("_length");
            return param;
        }

        /// <summary>
        /// インスペクタ表示
        /// </summary>
        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            //base.OnInspectorGUI();

            _transformParamFoldout = EditorGUILayout.Foldout(_transformParamFoldout, "Transform Param");
            if (_transformParamFoldout)
            {
                EditorGUI.indentLevel++;
                for (int i = 0; i < _transformParamPropList.Count; ++i)
                {
                    _transformParamFoldoutList[i] = EditorGUILayout.Foldout(_transformParamFoldoutList[i], "Transform Param " + i);
                    if (_transformParamFoldoutList[i])
                    {
                        EditorGUI.indentLevel++;
                        OnInspectorGUITransformParam(_transformParamPropList[i]);
                        EditorGUI.indentLevel--;
                    }
                }

                using (var scope = new EditorGUILayout.HorizontalScope())
                {
                    EditorGUILayout.Space();
                    if (GUILayout.Button("+", EditorStyles.miniButtonLeft, GUILayout.Width(20)))
                    {
                        AddTransformParam();
                    }

                    bool isZero = _transformParamProp.arraySize == 0;
                    if (isZero)
                    {
                        EditorGUI.BeginDisabledGroup(true);
                    }

                    if (GUILayout.Button("-", EditorStyles.miniButtonRight, GUILayout.Width(20)))
                    {
                        SubTransformParam();
                    }

                    if (isZero)
                    {
                        EditorGUI.EndDisabledGroup();
                    }
                }

                EditorGUI.indentLevel--;
            }

            _billboardParamFoldout = EditorGUILayout.Foldout(_billboardParamFoldout, "Billboard Param");
            if (_billboardParamFoldout)
            {
                EditorGUI.indentLevel++;
                for (int i = 0; i < _billboardParamPropList.Count; ++i)
                {
                    _billboardParamFoldoutList[i] = EditorGUILayout.Foldout(_billboardParamFoldoutList[i], "Billboard Param " + i);
                    if (_billboardParamFoldoutList[i])
                    {
                        EditorGUI.indentLevel++;
                        OnInspectorGUIBillboardParam(_billboardParamPropList[i]);
                        EditorGUI.indentLevel--;
                    }
                }

                using (var scope = new EditorGUILayout.HorizontalScope())
                {
                    EditorGUILayout.Space();
                    if (GUILayout.Button("+", EditorStyles.miniButtonLeft, GUILayout.Width(20)))
                    {
                        AddBillboardParam();
                    }

                    bool isZero = _billboardParamProp.arraySize == 0;
                    if (isZero)
                    {
                        EditorGUI.BeginDisabledGroup(true);
                    }

                    if (GUILayout.Button("-", EditorStyles.miniButtonRight, GUILayout.Width(20)))
                    {
                        SubBillboardParam();
                    }

                    if (isZero)
                    {
                        EditorGUI.EndDisabledGroup();
                    }
                }

                EditorGUI.indentLevel--;
            }

            _camOffsetFoldout = EditorGUILayout.Foldout(_camOffsetFoldout, "Cam Offset");
            if (_camOffsetFoldout)
            {
                EditorGUI.indentLevel++;
                for (int i = 0; i < _camOffsetPropList.Count; ++i)
                {
                    _camOffsetFoldoutList[i] = EditorGUILayout.Foldout(_camOffsetFoldoutList[i], "Cam Offset " + i);
                    if (_camOffsetFoldoutList[i])
                    {
                        EditorGUI.indentLevel++;
                        OnInspectorGUICamOffset(_camOffsetPropList[i]);
                        EditorGUI.indentLevel--;
                    }
                }

                using (var scope = new EditorGUILayout.HorizontalScope())
                {
                    EditorGUILayout.Space();
                    if (GUILayout.Button("+", EditorStyles.miniButtonLeft, GUILayout.Width(20)))
                    {
                        AddCamOffset();
                    }

                    bool isZero = _camOffsetProp.arraySize == 0;
                    if (isZero)
                    {
                        EditorGUI.BeginDisabledGroup(true);
                    }

                    if (GUILayout.Button("-", EditorStyles.miniButtonRight, GUILayout.Width(20)))
                    {
                        SubCamOffset();
                    }

                    if (isZero)
                    {
                        EditorGUI.EndDisabledGroup();
                    }
                }

                EditorGUI.indentLevel--;
            }

            _lightProbeRendererTargetFoldout = EditorGUILayout.Foldout(_lightProbeRendererTargetFoldout, "LightProbe Target Renderer");
            if (_lightProbeRendererTargetFoldout)
            {
                EditorGUI.indentLevel++;
                for (int i = 0; i < _lightProbeRendererProp.arraySize; ++i)
                {
                    EditorGUI.indentLevel++;
                    EditorGUILayout.PropertyField(_lightProbeRendererProp.GetArrayElementAtIndex(i));
                    EditorGUI.indentLevel--;
                }

                using (var scope = new EditorGUILayout.HorizontalScope())
                {
                    EditorGUILayout.Space();
                    if (GUILayout.Button("+", EditorStyles.miniButtonLeft, GUILayout.Width(20)))
                    {
                        AddLightProbeTargetRenderer();
                    }

                    bool isZero = _lightProbeRendererProp.arraySize == 0;
                    if (isZero)
                    {
                        EditorGUI.BeginDisabledGroup(true);
                    }

                    if (GUILayout.Button("-", EditorStyles.miniButtonRight, GUILayout.Width(20)))
                    {
                        SubLightProbeTargetRenderer();
                    }

                    if (isZero)
                    {
                        EditorGUI.EndDisabledGroup();
                    }
                }

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.PropertyField(_rightFootProp);
            EditorGUILayout.PropertyField(_leftFootProp);

            EditorGUILayout.PropertyField(_charaScaleProp);
            EditorGUILayout.PropertyField(_seIdProp);

            serializedObject.ApplyModifiedProperties();
        }

        private void OnInspectorGUITransformParam(TransformParamProperty param)
        {
            EditorGUILayout.PropertyField(param._objProp);

            int posIndex = EditorGUILayout.Popup("Position Node", Mathf.Max(System.Array.IndexOf(NodeNames, param._positionNodeNameProp.stringValue), 0), NodeNames);
            param._positionNodeNameProp.stringValue = NodeNames[posIndex];

            EditorGUILayout.PropertyField(param._positionOffsetPorp);

            int rotIndex = EditorGUILayout.Popup("Rotation Node", Mathf.Max(System.Array.IndexOf(NodeNames, param._rotationNodeNameProp.stringValue), 0), NodeNames);
            param._rotationNodeNameProp.stringValue = NodeNames[rotIndex];

            EditorGUILayout.PropertyField(param._rotationOffsetProp);

            using (var scope = new EditorGUILayout.HorizontalScope())
            {
                EditorGUILayout.LabelField("Enable Rot", GUILayout.MinWidth(100));
                param._enableRotXPorp.boolValue = EditorGUILayout.ToggleLeft("X", param._enableRotXPorp.boolValue, GUILayout.MinWidth(40));
                param._enableRotYProp.boolValue = EditorGUILayout.ToggleLeft("Y", param._enableRotYProp.boolValue, GUILayout.MinWidth(40));
                param._enableRotZProp.boolValue = EditorGUILayout.ToggleLeft("Z", param._enableRotZProp.boolValue, GUILayout.MinWidth(40));
            }
        }

        private void OnInspectorGUIBillboardParam(BillboardParamProperty param)
        {
            EditorGUILayout.PropertyField(param._objProp);
            EditorGUILayout.PropertyField(param._typeProp);
        }

        private void OnInspectorGUICamOffset(CamOffsetProperty param)
        {
            EditorGUILayout.PropertyField(param._objProp);
            EditorGUILayout.PropertyField(param._lengthProp);
        }

        /// <summary>
        /// TransformParam増やす
        /// </summary>
        private void AddTransformParam()
        {
            UnityEngine.Debug.Assert(_transformParamPropList.Count == _transformParamProp.arraySize);
            UnityEngine.Debug.Assert(_transformParamPropList.Count == _transformParamFoldoutList.Count);

            _transformParamProp.InsertArrayElementAtIndex(_transformParamProp.arraySize);
            _transformParamPropList.Add(GetTransformParamProperty(_transformParamProp.GetArrayElementAtIndex(_transformParamProp.arraySize - 1)));
            _transformParamFoldoutList.Add(true);
        }

        /// <summary>
        /// TransformParam減らす
        /// </summary>
        private void SubTransformParam()
        {
            UnityEngine.Debug.Assert(_transformParamPropList.Count == _transformParamProp.arraySize);
            UnityEngine.Debug.Assert(_transformParamPropList.Count == _transformParamFoldoutList.Count);

            if (_transformParamProp.arraySize == 0)
            {
                return;
            }

            _transformParamProp.DeleteArrayElementAtIndex(_transformParamProp.arraySize - 1);
            _transformParamPropList.RemoveAt(_transformParamPropList.Count - 1);
            _transformParamFoldoutList.RemoveAt(_transformParamFoldoutList.Count - 1);
        }

        /// <summary>
        /// BillboardParam増やす
        /// </summary>
        private void AddBillboardParam()
        {
            UnityEngine.Debug.Assert(_billboardParamPropList.Count == _billboardParamProp.arraySize);
            UnityEngine.Debug.Assert(_billboardParamPropList.Count == _billboardParamFoldoutList.Count);

            _billboardParamProp.InsertArrayElementAtIndex(_billboardParamProp.arraySize);
            _billboardParamPropList.Add(GetBillboardParamProperty(_billboardParamProp.GetArrayElementAtIndex(_billboardParamProp.arraySize - 1)));
            _billboardParamFoldoutList.Add(true);
        }

        /// <summary>
        /// BillboardParam減らす
        /// </summary>
        private void SubBillboardParam()
        {
            UnityEngine.Debug.Assert(_billboardParamPropList.Count == _billboardParamProp.arraySize);
            UnityEngine.Debug.Assert(_billboardParamPropList.Count == _billboardParamFoldoutList.Count);

            if (_billboardParamProp.arraySize == 0)
            {
                return;
            }

            _billboardParamProp.DeleteArrayElementAtIndex(_billboardParamProp.arraySize - 1);
            _billboardParamPropList.RemoveAt(_billboardParamPropList.Count - 1);
            _billboardParamFoldoutList.RemoveAt(_billboardParamFoldoutList.Count - 1);
        }

        /// <summary>
        /// CamOffset増やす
        /// </summary>
        private void AddCamOffset()
        {
            UnityEngine.Debug.Assert(_camOffsetPropList.Count == _camOffsetProp.arraySize);
            UnityEngine.Debug.Assert(_camOffsetPropList.Count == _camOffsetFoldoutList.Count);

            _camOffsetProp.InsertArrayElementAtIndex(_camOffsetProp.arraySize);
            _camOffsetPropList.Add(GetCamOffsetProperty(_camOffsetProp.GetArrayElementAtIndex(_camOffsetProp.arraySize - 1)));
            _camOffsetFoldoutList.Add(true);
        }

        /// <summary>
        /// CamOffset減らす
        /// </summary>
        private void SubCamOffset()
        {
            UnityEngine.Debug.Assert(_camOffsetPropList.Count == _camOffsetProp.arraySize);
            UnityEngine.Debug.Assert(_camOffsetPropList.Count == _camOffsetFoldoutList.Count);

            if (_camOffsetProp.arraySize == 0)
            {
                return;
            }

            _camOffsetProp.DeleteArrayElementAtIndex(_camOffsetProp.arraySize - 1);
            _camOffsetPropList.RemoveAt(_camOffsetPropList.Count - 1);
            _camOffsetFoldoutList.RemoveAt(_camOffsetFoldoutList.Count - 1);
        }

        private void AddLightProbeTargetRenderer()
        {
            _lightProbeRendererProp.InsertArrayElementAtIndex(_lightProbeRendererProp.arraySize);
        }

        /// <summary>
        /// CamOffset減らす
        /// </summary>
        private void SubLightProbeTargetRenderer()
        {
            if (_lightProbeRendererProp.arraySize == 0)
            {
                return;
            }

            _lightProbeRendererProp.DeleteArrayElementAtIndex(_lightProbeRendererProp.arraySize - 1);
        }
    }
}