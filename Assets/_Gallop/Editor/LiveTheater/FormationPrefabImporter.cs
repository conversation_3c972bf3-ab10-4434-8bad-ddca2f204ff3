using UnityEditor;
using UnityEngine;
using System.Collections.Generic;

namespace Gallop
{
    public class FormationPrefabImporter : AssetPostprocessor
    {
        // Live/Theater以下
        private const string TARGET_DIRECTORY = "Assets/_GallopResources/Bundle/Resources/Live/Theater/";
        // PreInのパス
        private const string PREIN_ASSET_ROOT = "Assets/_Gallop/Resources/";

        /// <summary>
        /// アセットインポート時コールバック
        /// </summary>
        public static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets,
            string[] movedFromAssetsPath)
        {
            bool isDirted = false;

            foreach (string asset in importedAssets)
            {
                if (!asset.Contains(".prefab"))
                {
                    continue;
                }

                if (!asset.StartsWith(TARGET_DIRECTORY))
                {
                    continue;
                }

                GameObject targetPrefab = AssetDatabase.LoadAssetAtPath(asset, typeof(GameObject)) as GameObject;
                if (targetPrefab == null)
                {
                    Debug.LogWarning($"LiveTheaterのPrefabロード失敗。 path : {asset}");
                    continue;
                }

                bool isAnyChanged = false;
                var imageCommons = targetPrefab.GetComponentsInChildren(typeof(ImageCommon));
                for (int i = 0; i < imageCommons.Length; i++)
                {
                    ImageCommon targetImage = imageCommons[i] as ImageCommon;
                    if (targetImage == null) continue;
                    if (targetImage.sprite == null) continue;
                    
                    string atlasPath = UnityEditor.AssetDatabase.GetAssetPath(targetImage.sprite);
                    if (atlasPath.Contains(PREIN_ASSET_ROOT))
                    {
                        targetImage.sprite = null;
                        isAnyChanged = true;
                    }
                }
                
                if (isAnyChanged)
                {
                    EditorUtility.SetDirty(targetPrefab);
                    isDirted = true;
                }
            }
            
            // 変更があった場合だけ保存する
            if (isDirted) { AssetDatabase.SaveAssets(); }
        }
    }
}