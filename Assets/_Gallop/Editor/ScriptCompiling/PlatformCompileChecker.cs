using System.IO;
using UnityEditor;
using UnityEditor.Build.Player;
using UnityEngine;

namespace Gallop
{
    public static class PlaormCompileChecker
    {
        // UnityEidtor用のテストインターフェース
        [MenuItem("Gallop/Tools/CompileCheck/iOS")]
        public static void CompileTestForiOS()
        {
            CompileTest(BuildTarget.iOS, BuildTargetGroup.iOS, true);
        }
        [MenuItem("Gallop/Tools/CompileCheck/Android")]
        public static void CompileTestForAndroid()
        {
            CompileTest(BuildTarget.Android, BuildTargetGroup.Android, true);
        }

        // Jenkinsのプルリク用にテストを実行
        public static void CompileTestForPullRequest(BuildTarget targetPlatform, BuildTargetGroup targetPlatformGroup)
        {
            string platformString = targetPlatform.ToString();
            Debug.Log($"Jenkins Pull Request Check - {platformString} Start");
            CompileTest(targetPlatform, targetPlatformGroup);
            Debug.Log($"Jenkins Pull Request Check - {platformString} Finish");
        }

        public static void CompileTestForPullRequestAndroid()
        {
            CompileTestForPullRequest(BuildTarget.Android, BuildTargetGroup.Android);
        }
        
        public static void CompileTestForPullRequestiOS()
        {
            CompileTestForPullRequest(BuildTarget.iOS, BuildTargetGroup.iOS);
        }
        
        public static void CompileTestForPullRequestLinux()
        {
            CompileTestForPullRequest(BuildTarget.StandaloneLinux64, BuildTargetGroup.Standalone);
        }
        
        public static void CompileTestForPullRequestWindows()
        {
            CompileTestForPullRequest(BuildTarget.StandaloneWindows64, BuildTargetGroup.Standalone);
        }        
        
        public static void CompileTestForPullRequestOSX()
        {
            CompileTestForPullRequest(BuildTarget.StandaloneOSX, BuildTargetGroup.Standalone);
        }        
        
        /// <summary>
        /// コンパイルチェックを行う
        /// </summary>
        public static void CompileTest(BuildTarget buildTarget, BuildTargetGroup buildTargetGroup, bool fromEditorMenu = false)
        {
            if (fromEditorMenu)
            {
                Gallop.UnityConsole.ClearLog();        // 最初にログを全て削除しておく
                EditorUtility.DisplayProgressBar($"コンパイルテスト : {buildTarget}", "コンパイル中...", 0.5f);
            }

            var tempBuildPath = "Temp/CompileTest";

            var option = new ScriptCompilationSettings();
            option.target = buildTarget;
            option.group = buildTargetGroup;
            option.options = ScriptCompilationOptions.None;

            Debug.Log($"Compile Check Running - BuildTarget: {option.target}");

            // ビルド実行
            var result = PlayerBuildInterface.CompilePlayerScripts(option, tempBuildPath);
            // エラー・ワーニングは都度コンソール出力されているのでここでは成否のみ出力する
            if (result.assemblies != null && result.assemblies.Count != 0 && result.typeDB != null)
            {
                Debug.Log($"Compile Test Success!");
            }
            else
            {
                Debug.Log($"Compile Test Filure!");
            }
            
            if( fromEditorMenu ) { EditorUtility.ClearProgressBar(); }
            
            // ビルド時に出力されたファイル群を削除しておく
            if (Directory.Exists(tempBuildPath))
            {
                Directory.Delete(tempBuildPath, true);
            }
        } 
    }
}