// Upgrade NOTE: replaced 'mul(UNITY_MATRIX_MVP,*)' with 'UnityObjectToClipPos(*)'

// Upgrade NOTE: replaced '_Object2World' with 'unity_ObjectToWorld'

// Unlit shader. Simplest possible colored shader.
// - no lighting
// - no lightmap support
// - no texture

Shader "CuttEditor/CameraCP" {
Properties {
    _Color ("Main Color", Color) = (1,1,1,1)
}

SubShader {
    Tags { "RenderType"="Opaque" }
    LOD 100
    
    Pass {  
        CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fog
            
            #include "UnityCG.cginc"

            struct appdata_t {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                float3 normal : NORMAL;
                float3 viewT : TEXCOORD0;
                //UNITY_FOG_COORDS(0)
            };

            CBUFFER_START(UnityPerMaterial)
                fixed4 _Color;
            CBUFFER_END
            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.normal = normalize(mul(unity_ObjectToWorld, float4(v.normal, 0.0)));
                o.viewT = normalize(WorldSpaceViewDir(v.vertex));
                return o;
            }
            
            fixed4 frag (v2f i) : COLOR
            {
                fixed diff = max (0, dot (i.normal, i.viewT));
                fixed4 col = _Color * diff;
                //UNITY_APPLY_FOG(i.fogCoord, col);
                UNITY_OPAQUE_ALPHA(col.a);
                return col;
            }
        ENDCG
    }
      Pass
        {
            Name "Projector"
            Tags{ "LightMode" = "Projector" }

            HLSLPROGRAM
            #pragma vertex vertexProjector
            #pragma fragment fragmentProjector
            float4 vertexProjector(float4 vertex: POSITION) : SV_POSITION { return 0; }
            half4 fragmentProjector() : SV_TARGET { return 0; }

            ENDHLSL
        }
}

}
