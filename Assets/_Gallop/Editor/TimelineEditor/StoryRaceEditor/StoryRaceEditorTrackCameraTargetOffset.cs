#if UNITY_EDITOR && CYG_DEBUG

using UnityEngine;
using UnityEditor;

namespace Gallop
{
    public class CameraTargetOffsetTrack : TrackBase
    {
        public const StoryRaceEditorKeyTrackBridge.TimelineLayers TIMELINE_LAYER = StoryRaceEditorKeyTrackBridge.TimelineLayers.MainCamera_TargetOffset;

        private int _cameraIndex; //カメラ番号（==0:メインカメラ, >=1:マルチカメラ）

        private CourseCameraTargetOffsetParam _param; // カメラパラメタ

        public override void SetBaseParam(CourseBaseParam param)
        {
            _param = param as CourseCameraTargetOffsetParam;
            base.SetBaseParam(param);
        }

        public CourseCameraTargetOffsetParam Param
        {
            get { return _param; }
            set
            {
                SetBaseParam(value);
            }
        }

        private CameraTargetOffsetTrack() { }

        public CameraTargetOffsetTrack(StoryRaceEditorWindow parent, int cameraIndex) : base(parent)
        {
            _cameraIndex = cameraIndex;
        }

        public CameraTargetOffsetTrack(CameraTargetOffsetTrack src) : base(src)
        {
            _cameraIndex = src._cameraIndex;
        }

        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return TIMELINE_LAYER + StoryRaceEditorKeyTrackBridge.CAMERA_LAYER_NUM * _cameraIndex;
        }

        public override bool IsInterpolated()
        {
            return _param.IsEnabledInterpolation;
        }

        public override TrackBase Clone()
        {
            var result = new CameraTargetOffsetTrack(this)
            {
                Param = new CourseCameraTargetOffsetParam(Param)
            };
            return result;
        }

        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as CameraTargetOffsetTrack;
            return ValueEquals(_param, track._param);
        }
    }

    public class StoryRaceEditorCameraTargetOffsetKeyTrackBridge : StoryRaceEditorKeyTrackBridge
    {
        private int _cameraIndex = 0;

        public StoryRaceEditorCameraTargetOffsetKeyTrackBridge(int cameraIndex = 0)
        {
            _cameraIndex = cameraIndex;
        }

        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new CameraTargetOffsetTrack(parent, _cameraIndex);
        }

        public override TimelineLayers GetTimelineLayer()
        {
            return CameraTargetOffsetTrack.TIMELINE_LAYER + CAMERA_LAYER_NUM * _cameraIndex;
        }

        public override CourseBaseParam CreateParam(StoryRaceEditorWindow parent, int index, float startDistance, TimelineTrack track, CourseBaseParam param)
        {
            param = CreateParam<CourseCameraTargetOffsetParam>(param);
            var sequence = parent._raceCameraEvent.GetSequenceCameraTargetOffset(_cameraIndex);
            ((CameraTargetOffsetTrack)track).Param = sequence.Insert(index, startDistance, (CourseCameraTargetOffsetParam)param);
            return param;
        }

        public override void SetParamToTrack(StoryRaceEditorWindow parent, TimelineTrack track, CourseBaseParam param)
        {
            ((CameraTargetOffsetTrack)track).Param = (CourseCameraTargetOffsetParam)param;
        }

        public override CourseBaseParam GetParam(StoryRaceEditorWindow parent, int index)
        {
            var sequence = parent._raceCameraEvent.GetSequenceCameraTargetOffset(_cameraIndex);
            return sequence.GetParam(index);
        }

        public override CourseBaseParam GetParamFromDistance(StoryRaceEditorWindow parent, float distance)
        {
            var sequence = parent._raceCameraEvent.GetSequenceCameraTargetOffset(_cameraIndex);
            return sequence.GetParamFromDistance(distance);
        }

        public override void RemoveParam(StoryRaceEditorWindow parent, int index)
        {
            parent._raceCameraEvent.GetSequenceCameraTargetOffset(_cameraIndex).RemoveAt(index);
        }

        public override void SortKey(StoryRaceEditorWindow parent)
        {
            var sequence = parent._raceCameraEvent.GetSequenceCameraTargetOffset(_cameraIndex);
            var layer = GetTimelineLayer();
            var selectTrack = parent._selectedTrack[(int)layer] as CameraTargetOffsetTrack;
            parent.SortParameter<CourseCameraTargetOffsetParam>(
                layer,
                sequence.param,
                selectTrack.Param,
                (track, param) =>
                {
                    ((CameraTargetOffsetTrack)track).Param = param;
                });
        }

        public override CourseBaseParam CloneParamFromTrack(TimelineTrack track)
        {
            var srcTrack = track as CameraTargetOffsetTrack;
            if (srcTrack == null)
                return null;

            return srcTrack.Param.Clone();
        }

        public override CourseBaseParam[] GetAllParam(StoryRaceEditorWindow parent)
        {
            var sequence = parent._raceCameraEvent.GetSequenceCameraTargetOffset(_cameraIndex);
            return sequence.param;
        }

        #region GUI
        private void DrawBezierGUI(StoryRaceEditorWindow parent, CourseCameraTargetOffsetParam param)
        {
            if (param.IsEnabledInterpolation)
            {
                GUI.enabled = param.IsAddBezier();
                if (GUILayout.Button("AddCP"))
                {
                    param.AddBezier();
                }
                GUI.enabled = true;

                if (param.HasBezier())
                {
                    int removeIndex = -1;
                    int num = param.BezierPointArray.Length;
                    for (int i = 0; i < num; i++)
                    {
                        using (new EditorGUILayout.HorizontalScope())
                        {
                            int index = i;
                            parent.GUI_Vector3ColorField("", ref param.BezierPointArray[i], param,
                                (p, x, y, z) =>
                                {
                                    if (x) p.BezierPointArray[index].x = param.BezierPointArray[index].x;
                                    if (y) p.BezierPointArray[index].y = param.BezierPointArray[index].y;
                                    if (z) p.BezierPointArray[index].z = param.BezierPointArray[index].z;
                                }
                                , nameof(param.BezierPointArray)
                            );

                            if (GUILayout.Button("-", GUILayout.Width(24)))
                            {
                                removeIndex = index;
                            }
                        }
                    }

                    if (removeIndex != -1)
                    {
                        param.RemoveBezier(removeIndex);
                    }
                }
            }
        }

        public override bool OnDrawGUI(TimelineTrack track, StoryRaceEditorWindow parent, float width)
        {
            var param = ((CameraTargetOffsetTrack)track).Param;

            bool needsUpdate = false;

            needsUpdate |= OnDrawGUI_CourseBaseParamInterp(track, parent, param);

            using (new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.Width(width)))
            {
                using (var check = new EditorGUI.ChangeCheckScope())
                {
                    parent.GUI_Vector3ColorField("", ref param.TargetOffset, param,
                        (p, x, y, z) =>
                        {
                            if (x) p.TargetOffset.x = param.TargetOffset.x;
                            if (y) p.TargetOffset.y = param.TargetOffset.y;
                            if (z) p.TargetOffset.z = param.TargetOffset.z;
                        }
                        , nameof(param.TargetOffset));
                    DrawBezierGUI(parent, param);

                    bool isEnabled = false;
                    bool isCurrentFrameEnabled = false;
                    CourseCharacterTransformParam characterTransformCurrentParam = null;
                    CourseCharacterTransformParam characterTransformCurrentFrameParam = null;
                    {
                        // カレントのCameraTypeParamとCameraTargetInfoParamを取得しておく。
                        var cameraTypeTrackBridge = parent._storyRaceEditorTimelineKeyTrackBridge[(int)TimelineLayers.MainCamera_Type];
                        var cameraTypeCurrentParam = cameraTypeTrackBridge.GetParamFromDistance(parent, param._startDistance) as CourseCameraTypeParam;
                        var cameraTargetInfoTrackBridge = parent._storyRaceEditorTimelineKeyTrackBridge[(int)TimelineLayers.MainCamera_TargetInfo];
                        var cameraTargetInfoCurrentParam = cameraTargetInfoTrackBridge.GetParamFromDistance(parent, param._startDistance) as CourseCameraTargetInfoParam;

                        if ((cameraTypeCurrentParam != null) && ((cameraTypeCurrentParam.ControlType == CourseCameraControlType.Pan) ||
                                                                 (cameraTypeCurrentParam.ControlType == CourseCameraControlType.Truck_Standard)) &&
                            (cameraTargetInfoCurrentParam != null) && (cameraTargetInfoCurrentParam.TargetType == CourseCameraTargetType.IndexDirect) &&
                                                                      (cameraTargetInfoCurrentParam.TargetIndexDirect >= 0))
                        {
                            int arrayIndex = (int)TimelineLayers.Character0_Transform + CHARA_LAYER_BASE_NUM * cameraTargetInfoCurrentParam.TargetIndexDirect;
                            var characterTransformTrackBridge = parent._storyRaceEditorTimelineKeyTrackBridge[arrayIndex];
                            characterTransformCurrentParam = characterTransformTrackBridge.GetParamFromDistance(parent, param._startDistance) as CourseCharacterTransformParam;

                            if (characterTransformCurrentParam != null)
                            {
                                isEnabled = true;
                            }

                            characterTransformCurrentFrameParam = characterTransformTrackBridge.GetParamFromDistance(parent, parent.GetCurrentFrame()) as CourseCharacterTransformParam;
                            isCurrentFrameEnabled = characterTransformCurrentFrameParam != null;
                        }
                    }
                    GUI.enabled = isCurrentFrameEnabled && !IsLockTrack(track);
                    if (GUILayout.Button("注視キャラのオフセット位置と同期(カレントフレームから検索)"))
                    {
                        parent.UndoRedoSnapshot(param);
                        param.TargetOffset.x = -characterTransformCurrentFrameParam.positionOffset.x;
                        param.TargetOffset.z = -characterTransformCurrentFrameParam.positionOffset.z;
                    }

                    GUI.enabled = isEnabled && !IsLockTrack(track);
                    if (GUILayout.Button("注視キャラのオフセット位置と同期(キー位置から検索)"))
                    {
                        parent.UndoRedoSnapshot(param);
                        param.TargetOffset.x = -characterTransformCurrentParam.positionOffset.x;
                        param.TargetOffset.z = -characterTransformCurrentParam.positionOffset.z;
                    }

                    GUI.enabled = !IsLockTrack(track);

                    needsUpdate |= check.changed;
                }
            }

            return needsUpdate;
        }
        #endregion
    }
}

#endif
