#if UNITY_EDITOR && CYG_DEBUG

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    public class CharacterFacialTrack<T> : TrackBase
        where T : CourseBaseParam
    {
        private readonly int _characterIndex;
        private readonly StoryRaceEditorKeyTrackBridge.TimelineLayers _baseLayer;
        protected T _param;

        public T Param
        {
            get => _param;
            set => SetBaseParam(value);
        }

        private CharacterFacialTrack() { }

        public CharacterFacialTrack(StoryRaceEditorWindow parent, int characterIndex, StoryRaceEditorKeyTrackBridge.TimelineLayers baseLayer) : base(parent)
        {
            _characterIndex = characterIndex;
            _baseLayer = baseLayer;
        }

        public CharacterFacialTrack(CharacterFacialTrack<T> src) : base(src)
        {
            _characterIndex = src._characterIndex;
            _baseLayer = src._baseLayer;
        }

        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return _baseLayer + StoryRaceEditorKeyTrackBridge.CHARA_LAYER_FACIAL_NUM * _characterIndex;
        }

        public override bool IsInterpolated()
        {
            return false;
        }

        public override void SetBaseParam(CourseBaseParam param)
        {
            _param = param as T;
            base.SetBaseParam(param);
        }

        public override TrackBase Clone()
        {
            var result = new CharacterFacialTrack<T>(this);
            result.Param = (T)typeof(T).GetConstructor(new System.Type[] { typeof(T) }).Invoke(new object[] { Param });
            return result;
        }

        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as CharacterFacialTrack<T>;
            return ValueEquals(_param, track._param);
        }
    }


    public class CharacterFacialTrackInterp<T> : CharacterFacialTrack<T>
        where T : CourseBaseParamInterp
    {
        public CharacterFacialTrackInterp(StoryRaceEditorWindow parent, int characterIndex, StoryRaceEditorKeyTrackBridge.TimelineLayers baseLayer)
            : base(parent, characterIndex, baseLayer)
        {
        }

        public CharacterFacialTrackInterp(CharacterFacialTrack<T> src) : base(src)
        {
        }

        public override bool IsInterpolated()
        {
            return _param.IsEnabledInterpolation;
        }
    }


    public abstract class StoryRaceEditorCharacterFacialBaseKeyTrackBridge : StoryRaceEditorKeyTrackBridge
    {
        private static readonly Color DefaultBackgroundColor = new Color(0.29f, 0.34f, 0.31f, 1.0f);

        protected readonly int _characterIndex;

        public StoryRaceEditorCharacterFacialBaseKeyTrackBridge(int index)
        {
            _characterIndex = index;
        }

        public override Texture2D GetBackgroundColor(TimelineLayer layer)
        {
            if (_backgroundTexture == null)
            {
                _backgroundTexture = new Texture2D(1, 1);
                _backgroundTexture.SetPixel(0, 0, DefaultBackgroundColor);
                _backgroundTexture.Apply();
            }

            return _backgroundTexture;
        }

        public override bool IsVisible()
        {
            if (!RaceManager.HasInstance())
                return true;

            if (RaceManager.Instance.GetHorseNumber() <= _characterIndex)
                return false;

            return true;
        }

        #region GUI
        private void GetFacePartsTypeGroupIndex(FaceGroupType faceGroupType, int facePartsType, out int groupIndex, out int faceIndex)
        {
            groupIndex = 0;
            faceIndex = 0;

            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                case FaceGroupType.EyeR:
                    DrivenKeyComponent.GetFaceEyeTypeGroupIndex(facePartsType, out groupIndex, out faceIndex);
                    break;
                case FaceGroupType.EyebrowL:
                case FaceGroupType.EyebrowR:
                    DrivenKeyComponent.GetFaceEyebrowTypeGroupIndex(facePartsType, out groupIndex, out faceIndex);
                    break;
                case FaceGroupType.Mouth:
                    DrivenKeyComponent.GetFaceMouthTypeGroupIndex(facePartsType, out groupIndex, out faceIndex);
                    break;
                default:
                    break;
            }
        }

        private int GetFaceParts(FaceGroupType faceGroupType, int groupIndex, int faceIndex)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                case FaceGroupType.EyeR:
                    return DrivenKeyComponent.GetFaceEyeType(groupIndex, faceIndex);
                case FaceGroupType.EyebrowL:
                case FaceGroupType.EyebrowR:
                    return DrivenKeyComponent.GetFaceEyebrowType(groupIndex, faceIndex);
                case FaceGroupType.Mouth:
                    return DrivenKeyComponent.GetFaceMouthType(groupIndex, faceIndex);
                default:
                    break;
            }
            return 0;
        }

        private string[] GetFacePartsTypeGroupName(FaceGroupType faceGroupType)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                case FaceGroupType.EyeR:
                    return DrivenKeyComponent.GetFaceEyeTypeGroupName();
                case FaceGroupType.EyebrowL:
                case FaceGroupType.EyebrowR:
                    return DrivenKeyComponent.GetFaceEyebrowTypeGroupName();
                case FaceGroupType.Mouth:
                    return DrivenKeyComponent.GetFaceMouthTypeGroupName();
                default:
                    break;
            }
            return null;
        }

        private string[] GetFacePartsTypeGroupPartsName(FaceGroupType faceGroupType, int groupIndex)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                case FaceGroupType.EyeR:
                    return DrivenKeyComponent.GetFaceEyeTypeGroupPartsName(groupIndex);
                case FaceGroupType.EyebrowL:
                case FaceGroupType.EyebrowR:
                    return DrivenKeyComponent.GetFaceEyebrowTypeGroupPartsName(groupIndex);
                case FaceGroupType.Mouth:
                    return DrivenKeyComponent.GetFaceMouthTypeGroupPartsName(groupIndex);
                default:
                    break;
            }
            return null;
        }

        private FacePartsString[] GetPartsArray(FaceGroupType faceGroupType, CourseCharacterFacialParamBase param)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                    return param.FacePartsStringSet._eyeL;
                case FaceGroupType.EyeR:
                    return param.FacePartsStringSet._eyeR;
                case FaceGroupType.EyebrowL:
                    return param.FacePartsStringSet._eyebrowL;
                case FaceGroupType.EyebrowR:
                    return param.FacePartsStringSet._eyebrowR;
                case FaceGroupType.Mouth:
                    return param.FacePartsStringSet._mouth;
            }

            return null;
        }

        private string GetFacePartsArrayUndoRedoName(FaceGroupType faceGroupType,CourseCharacterFacialParamBase param,int index)
        {
            switch (faceGroupType)
            {
                case FaceGroupType.EyeL:
                    return StoryRaceEditorWindow.GetUndoRedoDifferentMemberName(nameof(param.FacePartsStringSet), StoryRaceEditorWindow.GetUndoRedoDifferentArrayIndexName(nameof(param.FacePartsStringSet._eyeL), index));
                case FaceGroupType.EyeR:
                    return StoryRaceEditorWindow.GetUndoRedoDifferentMemberName(nameof(param.FacePartsStringSet), StoryRaceEditorWindow.GetUndoRedoDifferentArrayIndexName(nameof(param.FacePartsStringSet._eyeR), index));
                case FaceGroupType.EyebrowL:
                    return StoryRaceEditorWindow.GetUndoRedoDifferentMemberName(nameof(param.FacePartsStringSet), StoryRaceEditorWindow.GetUndoRedoDifferentArrayIndexName(nameof(param.FacePartsStringSet._eyebrowL), index));
                case FaceGroupType.EyebrowR:
                    return StoryRaceEditorWindow.GetUndoRedoDifferentMemberName(nameof(param.FacePartsStringSet), StoryRaceEditorWindow.GetUndoRedoDifferentArrayIndexName(nameof(param.FacePartsStringSet._eyebrowR), index));
                case FaceGroupType.Mouth:
                    return StoryRaceEditorWindow.GetUndoRedoDifferentMemberName(nameof(param.FacePartsStringSet), StoryRaceEditorWindow.GetUndoRedoDifferentArrayIndexName(nameof(param.FacePartsStringSet._mouth), index));
            }

            return string.Empty;
        }

        private string GetUndoRedoLabel(string label,
            StoryRaceEditorWindow parent, 
            FaceGroupType faceType, CourseCharacterFacialParamBase param,int index,
            System.Func<FacePartsString, FacePartsString, bool> checkFunc)
        {
            return parent.GetUndoRedoDifferentLabelName(label, param, GetFacePartsArrayUndoRedoName(faceType, param, index),
                (p) =>
                {
                    var paramBase = p as CourseCharacterFacialParamBase;
                    var groupArray = GetPartsArray(faceType, paramBase);
                    var paramGroupArray = GetPartsArray(faceType, param);
                    if (groupArray.Length != paramGroupArray.Length)
                    {
                        return false;
                    }
                    return checkFunc(groupArray[index], paramGroupArray[index]);
                });
        }

        protected void OnDrawGUI_FacePartsList(
            CourseCharacterFacialParamBase param, 
            StoryRaceEditorWindow parent, 
            FaceGroupType faceGroupType)
        {
            var facePartsList = param.GetFacePartsList(faceGroupType);
            if (facePartsList == null)
                return;

            var count = facePartsList.Count;
            for (int i = 0; i < count; i++)
            {
                int index = i;
                FaceParts faceParts = facePartsList[index]; // 構造体なので参照ではなくコピーしている。

                GetFacePartsTypeGroupIndex(faceGroupType, faceParts._faceParts, out int groupIndex, out int faceIndex);

                using (new GUILayout.VerticalScope("box"))
                {
                    using (new EditorGUILayout.HorizontalScope())
                    {
                        {
                            var value = groupIndex;
                            var label = GetUndoRedoLabel("グループ:", parent, faceGroupType, param, index, (p1, p2) => p1._faceParts != p2._faceParts);
                            groupIndex = EditorGUILayout.Popup(label, value, GetFacePartsTypeGroupName(faceGroupType));
                            if (groupIndex != value)
                            {
                                parent.UndoRedoSnapshot(param);

                                faceIndex = 0;

                                faceParts._faceParts = GetFaceParts(faceGroupType, groupIndex, faceIndex);
                                facePartsList[index] = faceParts; // 構造体なので直に格納する。
                                param.UpdateFacePartsStringSet(faceGroupType);

                                parent.MultiSelectAction(param, (p) =>
                                {
                                    var list = p.GetFacePartsList(faceGroupType);
                                    if ((list != null) && (index < list.Count))
                                    {
                                        var parts = list[index];
                                        parts._faceParts = faceParts._faceParts;
                                        list[index] = parts; // 構造体なので直に格納する。
                                        p.UpdateFacePartsStringSet(faceGroupType);
                                    }
                                });
                            }
                        }

                        //追加ボタン
                        if (index <= 0)
                        {
                            if (GUILayout.Button("+", GUILayout.Width(32f)))
                            {
                                parent.UndoRedoSnapshot(param);

                                facePartsList.Add(new FaceParts(true));
                                param.UpdateFacePartsStringSet(faceGroupType);

                                parent.MultiSelectAction(param, (p) =>
                                {
                                    var list = p.GetFacePartsList(faceGroupType);
                                    if (list != null)
                                    {
                                        list.Add(new FaceParts(true));
                                        p.UpdateFacePartsStringSet(faceGroupType);
                                    }
                                });

                                return;
                            }
                        }
                        //削除ボタン
                        else
                        {
                            if (GUILayout.Button("-", GUILayout.Width(32f)))
                            {
                                parent.UndoRedoSnapshot(param);

                                facePartsList.RemoveAt(index);
                                param.UpdateFacePartsStringSet(faceGroupType);

                                parent.MultiSelectAction(param, (p) =>
                                {
                                    var list = p.GetFacePartsList(faceGroupType);
                                    if (list != null)
                                    {
                                        list.RemoveAt(index);
                                        p.UpdateFacePartsStringSet(faceGroupType);
                                    }
                                });

                                return;
                            }
                        }
                    }

                    {
                        var value = faceIndex;
                        var label = GetUndoRedoLabel("種類:", parent, faceGroupType, param, index, (p1, p2) => p1._faceParts != p2._faceParts);
                        faceIndex = EditorGUILayout.Popup(label, value, GetFacePartsTypeGroupPartsName(faceGroupType, groupIndex));
                        if (faceIndex != value)
                        {
                            parent.UndoRedoSnapshot(param);

                            faceParts._faceParts = GetFaceParts(faceGroupType, groupIndex, faceIndex);
                            facePartsList[index] = faceParts; // 構造体なので直に格納する。
                            param.UpdateFacePartsStringSet(faceGroupType);

                            parent.MultiSelectAction(param, (p) =>
                            {
                                var list = p.GetFacePartsList(faceGroupType);
                                if ((list != null) && (index < list.Count))
                                {
                                    var parts = list[index];
                                    parts._faceParts = faceParts._faceParts;
                                    list[index] = parts; // 構造体なので直に格納する。
                                    p.UpdateFacePartsStringSet(faceGroupType);
                                }
                            });
                        }
                    }

                    //Weight
                    {
                        var result = faceParts._weight;
                        var label = GetUndoRedoLabel("Weight", parent, faceGroupType, param, index, (p1, p2) => p1._weight != p2._weight);
                        parent.GUI_FloatField(label, ref result, param, (item) => { }, string.Empty);
                        if (result != faceParts._weight)
                        {
                            faceParts._weight = result;
                            facePartsList[index] = faceParts; // 構造体なので直に格納する。
                            param.UpdateFacePartsStringSet(faceGroupType);

                            parent.MultiSelectAction(param, (p) =>
                            {
                                var list = p.GetFacePartsList(faceGroupType);
                                if ((list != null) && (index < list.Count))
                                {
                                    var parts = list[index];
                                    parts._weight = faceParts._weight;
                                    list[index] = parts; // 構造体なので直に格納する。
                                    p.UpdateFacePartsStringSet(faceGroupType);
                                }
                            });
                        }
                    }
                }
            }
        }
        #endregion
    }
}

#endif