#if UNITY_EDITOR && CYG_DEBUG

using UnityEngine;
using UnityEditor;

namespace Gallop
{
    public class TiltShiftTrack : TrackBase
    {
        public const StoryRaceEditorKeyTrackBridge.TimelineLayers TimelineLayer = StoryRaceEditorKeyTrackBridge.TimelineLayers.TiltShift;

        protected CourseTiltShiftParam _param;

        public override void SetBaseParam(CourseBaseParam param)
        {
            _param = param as CourseTiltShiftParam;
            base.SetBaseParam(param);
        }

        public CourseTiltShiftParam Param
        {
            get => _param;
            set => SetBaseParam(value);
        }

        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return TimelineLayer;
        }

        private TiltShiftTrack()
        {
        }

        public TiltShiftTrack(StoryRaceEditorWindow parent) : base(parent)
        {
        }

        public TiltShiftTrack(TiltShiftTrack src) : base(src)
        {
        }

        public override bool IsInterpolated()
        {
            return _param.IsEnabledInterpolation;
        }

        public override TrackBase Clone()
        {
            var result = new TiltShiftTrack(this);
            result.Param = new CourseTiltShiftParam(Param);
            return result;
        }
        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as TiltShiftTrack;
            return ValueEquals(_param, track._param);
        }
    }

    public class StoryRaceEditorTiltShiftKeyTrackBridge : StoryRaceEditorKeyTrackBridge
    {
        private bool _foldout = true;

        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new TiltShiftTrack(parent);
        }

        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return TiltShiftTrack.TimelineLayer;
        }

        public override CourseBaseParam CreateParam(StoryRaceEditorWindow parent, int index, float startDistance, TimelineTrack track, CourseBaseParam param)
        {
            param = CreateParam<CourseTiltShiftParam>(param);
            var sequence = GetSequence(parent);
            ((TiltShiftTrack)track).Param = sequence.Insert(index, startDistance, (CourseTiltShiftParam)param);
            return param;
        }

        public override void SetParamToTrack(StoryRaceEditorWindow parent, TimelineTrack track, CourseBaseParam param)
        {
            ((TiltShiftTrack)track).Param = (CourseTiltShiftParam)param;
        }

        public override CourseBaseParam GetParam(StoryRaceEditorWindow parent, int index)
        {
            return GetSequence(parent).GetParam(index);
        }

        public override void RemoveParam(StoryRaceEditorWindow parent, int index)
        {
            GetSequence(parent).RemoveAt(index);
        }

        public override void SortKey(StoryRaceEditorWindow parent)
        {
            var sequence = GetSequence(parent);
            var layer = GetTimelineLayer();
            var selectTrack = parent._selectedTrack[(int)layer] as TiltShiftTrack;
            parent.SortParameter<CourseTiltShiftParam>(layer, sequence.param, selectTrack.Param,
                (track, param) => { ((TiltShiftTrack)track).Param = param; }
                );
        }

        public override CourseBaseParam CloneParamFromTrack(TimelineTrack track)
        {
            TiltShiftTrack srcTrack = track as TiltShiftTrack;
            if (srcTrack == null)
                return null;

            return srcTrack.Param.Clone();
        }

        public override CourseBaseParam[] GetAllParam(StoryRaceEditorWindow parent)
        {
            return GetSequence(parent).param;
        }
        
        protected RaceCameraEventBase.Sequence<CourseTiltShiftParam> GetSequence(StoryRaceEditorWindow parent)
        {
            var layer = GetTimelineLayer();
            switch (layer)
            {
                case TimelineLayers.TiltShift:
                    return parent._raceCameraEvent.GetSequenceTiltShift();
                case TimelineLayers.MainCamera_TiltShift:
                    return parent._raceCameraEvent.GetSequenceCameraTiltShift(0);
                case TimelineLayers.FadeCamera_TiltShift:
                    return parent._raceCameraEvent.GetSequenceCameraTiltShift(RaceEpisodeCameraEvent.FADE_CAMERA_INDEX);
                case TimelineLayers.MultiCamera0_TiltShift:
                    return parent._raceCameraEvent.GetSequenceMultiCameraTiltShift(RaceEpisodeCameraEvent.MULTI_CAMERA0_INDEX);
                case TimelineLayers.MultiCamera1_TiltShift:
                    return parent._raceCameraEvent.GetSequenceMultiCameraTiltShift(RaceEpisodeCameraEvent.MULTI_CAMERA1_INDEX);
                case TimelineLayers.MultiCamera2_TiltShift:
                    return parent._raceCameraEvent.GetSequenceMultiCameraTiltShift(RaceEpisodeCameraEvent.MULTI_CAMERA2_INDEX);
                case TimelineLayers.MultiCamera3_TiltShift:
                    return parent._raceCameraEvent.GetSequenceMultiCameraTiltShift(RaceEpisodeCameraEvent.MULTI_CAMERA3_INDEX);
                case TimelineLayers.MultiCamera4_TiltShift:
                    return parent._raceCameraEvent.GetSequenceMultiCameraTiltShift(RaceEpisodeCameraEvent.MULTI_CAMERA4_INDEX);
                default:
                    return null;
            }
        }

        #region GUI

        public override bool OnDrawGUI(TimelineTrack track, StoryRaceEditorWindow parent, float width)
        {
            CourseTiltShiftParam param = ((TiltShiftTrack)track).Param;
            bool needsUpdate = false;

            needsUpdate |= OnDrawGUI_CourseBaseParamInterp(track, parent, param);

            using (new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.Width(width)))
            {
                _foldout = FoldOut("TiltShift Parameter", _foldout);
                if (_foldout)
                {
                    EditorGUI.indentLevel++;
                    needsUpdate |= DrawEffectParamGUI_TiltShift(parent, track, param);
                    EditorGUI.indentLevel--;
                }
            }

            return needsUpdate;
        }

        private bool DrawEffectParamGUI_TiltShift(StoryRaceEditorWindow parent, TimelineTrack track, CourseTiltShiftParam param)
        {
            bool needsUpdate = false;
            using (var check = new EditorGUI.ChangeCheckScope())
            {
                {
                    System.Enum value = param.Mode;
                    parent.GUI_EnumPopup("Mode", ref value, param, (p) => { p.Mode = (ImageEffect.TiltShiftParam.TiltShiftMode)value; }, nameof(param.Mode));
                    param.Mode = (ImageEffect.TiltShiftParam.TiltShiftMode)value;

                }

                if (param.Mode != ImageEffect.TiltShiftParam.TiltShiftMode.None)
                {
                    {
                        System.Enum value = param.Quality;
                        parent.GUI_EnumPopup("Quality", ref value, param, (p) => { p.Quality = (ImageEffect.TiltShiftParam.TiltShiftQuality)value; }, nameof(param.Quality));
                        param.Quality = (ImageEffect.TiltShiftParam.TiltShiftQuality)value;
                    }

                    parent.GUI_Slider("Blur area", ref param.BlurArea, 0.0f, 100.0f, param, (p) => { p.BlurArea = param.BlurArea; }, nameof(param.BlurArea));

                    parent.GUI_Slider("Max blur size", ref param.MaxBlurSize, 0.0f, 25.0f, param, (p) => { p.MaxBlurSize = param.MaxBlurSize; }, nameof(param.MaxBlurSize));

                    parent.GUI_IntSlider("Downsample", ref param.Downsample, 0, 1, param, (p) => { p.Downsample = param.Downsample; }, nameof(param.Downsample));

                    parent.GUI_Vector2Field("Offset", ref param.Offset, param,
                        (p, x, y) =>
                        {
                            if (x) p.Offset.x = param.Offset.x;
                            if (y) p.Offset.y = param.Offset.y;
                        }, nameof(param.Offset));

                    parent.GUI_Slider("Roll", ref param.Roll, -180.0f, 180.0f, param, (p) => { p.Roll = param.Roll; }, nameof(param.Roll));
                }

                needsUpdate |= check.changed;
            }

            return needsUpdate;
        }

        #endregion GUI
    }
    
    public class TiltShiftTrack_CameraPostEffect : TiltShiftTrack
    {
        private StoryRaceEditorKeyTrackBridge.TimelineLayers _baseLayerType;
        public TiltShiftTrack_CameraPostEffect(StoryRaceEditorWindow parent, StoryRaceEditorKeyTrackBridge.TimelineLayers layerType) : base(parent)
        {
            _baseLayerType = layerType;
        }
        
        public TiltShiftTrack_CameraPostEffect(TiltShiftTrack_CameraPostEffect src) : base(src)
        {
            _baseLayerType = src._baseLayerType;
        }
        
        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return base.GetTimelineLayer() - (int)StoryRaceEditorKeyTrackBridge.TimelineLayers.Dof + (int)_baseLayerType + 1;
        }
        
        public override TrackBase Clone()
        {
            var result = new TiltShiftTrack_CameraPostEffect(this);
            result.Param = new CourseTiltShiftParam(Param);
            return result;
        }
        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as TiltShiftTrack_CameraPostEffect;
            return ValueEquals(_param, track._param);
        }
    }
    
    public class StoryRaceEditorTiltShiftKeyTrackBridge_CameraPostEffect : StoryRaceEditorTiltShiftKeyTrackBridge
    {
        private readonly TimelineLayers _baseLayerType;
        public StoryRaceEditorTiltShiftKeyTrackBridge_CameraPostEffect(TimelineLayers layerType)
        {
            _baseLayerType = layerType;
        }
        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new TiltShiftTrack_CameraPostEffect(parent, _baseLayerType);
        }
        public override TimelineLayers GetTimelineLayer()
        {
            return base.GetTimelineLayer() - (int)TimelineLayers.Dof + (int)_baseLayerType + 1;
        }
        
        #region GUI
        private bool _controlPostEffectFoldout = true;
        public override bool OnDrawGUI(TimelineTrack track, StoryRaceEditorWindow parent, float width)
        {
            CourseTiltShiftParam param = ((TiltShiftTrack)track).Param;
            bool needsUpdate = false;

            needsUpdate |= OnDrawGUI_CourseBaseParamInterp(track, parent, param);
            needsUpdate |= base.OnDrawGUI(track, parent, width);

            using (var scope = new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.Width(width)))
            {
                _controlPostEffectFoldout = FoldOut("PostEffect制御", _controlPostEffectFoldout);
                if (_controlPostEffectFoldout)
                {
                    EditorGUI.indentLevel++;
                    needsUpdate |= DrawEffectParamGUI_TiltShift(parent, track, param);
                    EditorGUI.indentLevel--;
                }
            }

            return needsUpdate;
        }

        private bool DrawEffectParamGUI_TiltShift(StoryRaceEditorWindow parent, TimelineTrack track, CourseTiltShiftParam param)
        {
            bool needsUpdate = false;
            using (var check = new EditorGUI.ChangeCheckScope())
            {
                parent.GUI_Toggle("共通のPostEffectの上書き", ref param.IsOverrideCommonParam, param, (p) => { p.IsOverrideCommonParam = param.IsOverrideCommonParam; }, nameof(param.IsOverrideCommonParam));
                needsUpdate |= check.changed;
            }

            return needsUpdate;
        }
        #endregion
    }
}

#endif // UNITY_EDITOR && CYG_DEBUG
