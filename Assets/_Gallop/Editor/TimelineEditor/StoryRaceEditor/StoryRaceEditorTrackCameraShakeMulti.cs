#if UNITY_EDITOR && CYG_DEBUG

namespace Gallop
{
    public class StoryRaceEditorCameraShakeMultiKeyTrackBridge : StoryRaceEditorCameraShakeKeyTrackBridge
    {
        private readonly int _multiCameraIndex;
        private readonly int _cameraIndex;

        public StoryRaceEditorCameraShakeMultiKeyTrackBridge(int multiCameraIndex)
        {
            _multiCameraIndex = multiCameraIndex;
            _cameraIndex = multiCameraIndex + 1; // MainCameraからのカメラ番号なので+1。
        }

        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new CameraShakeTrack(parent, _cameraIndex);
        }

        public override TimelineLayers GetTimelineLayer()
        {
            // CameraFovTrack.TimelineLayerはメインカメラのレイヤーを指しているので、_cameraIndexでレイヤーを算出する。
            return CameraShakeTrack.TIMELINE_LAYER + StoryRaceEditorKeyTrackBridge.CAMERA_LAYER_NUM * _cameraIndex;
        }

        public override CourseBaseParam CreateParam(StoryRaceEditorWindow parent, int index, float startDistance, TimelineTrack track, CourseBaseParam param)
        {
            param = CreateParam<CourseCameraShakeParam>(param);
            var sequence = parent._raceCameraEvent.GetSequenceMultiCameraShake(_multiCameraIndex);
            ((CameraShakeTrack)track).Param = sequence.Insert(index, startDistance, (CourseCameraShakeParam)param);
            return param;
        }

        public override void SetParamToTrack(StoryRaceEditorWindow parent, TimelineTrack track, CourseBaseParam param)
        {
            ((CameraShakeTrack)track).Param = (CourseCameraShakeParam)param;
        }

        public override CourseBaseParam GetParam(StoryRaceEditorWindow parent, int index)
        {
            var sequence = parent._raceCameraEvent.GetSequenceMultiCameraShake(_multiCameraIndex);
            return sequence.GetParam(index);
        }

        public override void RemoveParam(StoryRaceEditorWindow parent, int index)
        {
            parent._raceCameraEvent.GetSequenceMultiCameraShake(_multiCameraIndex).RemoveAt(index);
        }

        public override void SortKey(StoryRaceEditorWindow parent)
        {
            var sequence = parent._raceCameraEvent.GetSequenceMultiCameraShake(_multiCameraIndex);
            var layer = GetTimelineLayer();
            var selectTrack = parent._selectedTrack[(int)layer] as CameraShakeTrack;
            parent.SortParameter<CourseCameraShakeParam>(
                layer,
                sequence.param,
                selectTrack.Param,
                (track, param) =>
                {
                    ((CameraShakeTrack)track).Param = param;
                });
        }

        public override CourseBaseParam[] GetAllParam(StoryRaceEditorWindow parent)
        {
            var sequence = parent._raceCameraEvent.GetSequenceMultiCameraShake(_multiCameraIndex);
            return sequence.param;
        }
    }
}

#endif