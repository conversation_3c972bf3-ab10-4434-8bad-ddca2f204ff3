#if UNITY_EDITOR && CYG_DEBUG

using UnityEngine;
using UnityEditor;

namespace Gallop
{
    public class ColorCollectionTrack : TrackBase
    {
        public const StoryRaceEditorKeyTrackBridge.TimelineLayers TimelineLayer = StoryRaceEditorKeyTrackBridge.TimelineLayers.ColorCollection;

        protected CourseCollectionParam _param; // カメラパラメタ

        public override void SetBaseParam(CourseBaseParam param)
        {
            _param = param as CourseCollectionParam;
            base.SetBaseParam(param);
        }

        public CourseCollectionParam Param
        {
            set
            {
                SetBaseParam(value);
            }

            get
            {
                return _param;
            }
        }


        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return TimelineLayer;
        }

        private ColorCollectionTrack()
        {
        }

        public ColorCollectionTrack(StoryRaceEditorWindow parent) : base(parent)
        {
        }

        public ColorCollectionTrack(ColorCollectionTrack src) : base(src)
        {
        }

        public override bool IsInterpolated()
        {
            return _param.IsEnabledInterpolation;
        }

        public override TrackBase Clone()
        {
            var result = new ColorCollectionTrack(this);
            result.Param = new CourseCollectionParam(Param);
            return result;
        }
        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as ColorCollectionTrack;
            return ValueEquals(_param, track._param);
        }
    }

    public class StoryRaceEditorColorCollectionKeyTrackBridge : StoryRaceEditorKeyTrackBridge
    {
        private bool _colorCorrectionFoldout = true;

        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new ColorCollectionTrack(parent);
        }

        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return ColorCollectionTrack.TimelineLayer;
        }

        public override CourseBaseParam CreateParam(StoryRaceEditorWindow parent, int index, float startDistance, TimelineTrack track, CourseBaseParam param)
        {
            param = CreateParam<CourseCollectionParam>(param);
            var sequence = GetSequence(parent);
            ((ColorCollectionTrack)track).Param = sequence.Insert(index, startDistance, (CourseCollectionParam)param);
            return param;
        }

        public override void SetParamToTrack(StoryRaceEditorWindow parent, TimelineTrack track, CourseBaseParam param)
        {
            ((ColorCollectionTrack)track).Param = (CourseCollectionParam)param;
        }

        public override CourseBaseParam GetParam(StoryRaceEditorWindow parent, int index)
        {
            return GetSequence(parent).GetParam(index);
        }

        public override void RemoveParam(StoryRaceEditorWindow parent, int index)
        {
            GetSequence(parent).RemoveAt(index);
        }

        public override void SortKey(StoryRaceEditorWindow parent)
        {
            var sequence = GetSequence(parent);
            var layer = GetTimelineLayer();
            var selectTrack = parent._selectedTrack[(int)layer] as ColorCollectionTrack;
            parent.SortParameter<CourseCollectionParam>(layer, sequence.param, selectTrack.Param,
                (track, param) => { ((ColorCollectionTrack)track).Param = param; }
                );
        }

        public override CourseBaseParam CloneParamFromTrack(TimelineTrack track)
        {
            var cameraTrack = track as ColorCollectionTrack;
            if (cameraTrack == null)
                return null;

            return cameraTrack.Param.Clone();
        }

        public override CourseBaseParam[] GetAllParam(StoryRaceEditorWindow parent)
        {
            return GetSequence(parent).param;
        }
        
        protected RaceCameraEventBase.Sequence<CourseCollectionParam> GetSequence(StoryRaceEditorWindow parent)
        {
            var layer = GetTimelineLayer();
            switch (layer)
            {
                case TimelineLayers.ColorCollection:
                    return parent._raceCameraEvent.GetSequenceColorCollection();
                case TimelineLayers.MainCamera_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceCameraColorCollection2(0);
                case TimelineLayers.FadeCamera_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceCameraColorCollection2(RaceEpisodeCameraEvent.FADE_CAMERA_INDEX);
                case TimelineLayers.MultiCamera0_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceMultiCameraColorCollection2(RaceEpisodeCameraEvent.MULTI_CAMERA0_INDEX);
                case TimelineLayers.MultiCamera1_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceMultiCameraColorCollection2(RaceEpisodeCameraEvent.MULTI_CAMERA1_INDEX);
                case TimelineLayers.MultiCamera2_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceMultiCameraColorCollection2(RaceEpisodeCameraEvent.MULTI_CAMERA2_INDEX);
                case TimelineLayers.MultiCamera3_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceMultiCameraColorCollection2(RaceEpisodeCameraEvent.MULTI_CAMERA3_INDEX);
                case TimelineLayers.MultiCamera4_ColorCollection2:
                    return parent._raceCameraEvent.GetSequenceMultiCameraColorCollection2(RaceEpisodeCameraEvent.MULTI_CAMERA4_INDEX);
                default:
                    return null;
            }
        }

        #region GUI

        public override bool OnDrawGUI(TimelineTrack track, StoryRaceEditorWindow parent, float width)
        {
            CourseCollectionParam param = ((ColorCollectionTrack)track).Param;
            bool needsUpdate = false;

            needsUpdate |= OnDrawGUI_CourseBaseParamInterp(track, parent, param);

            using (var scope = new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.Width(width)))
            {
                _colorCorrectionFoldout = FoldOut("ColorCollection Parameter", _colorCorrectionFoldout);
                if (_colorCorrectionFoldout)
                {
                    EditorGUI.indentLevel++;
                    using (var check = new EditorGUI.ChangeCheckScope())
                    {
                        DrawEffectParamGUI_ColorCorrection(parent, param);
                        needsUpdate |= check.changed;
                    }
                    EditorGUI.indentLevel--;
                }
            }

            return needsUpdate;
        }

        private string GetUndoRedoLabelNameFromAnimationCurve(string label, StoryRaceEditorWindow parent, CourseCollectionParam param, System.Func<CourseCollectionParam, AnimationCurve> getCurveFunc)
        {
            return parent.GetUndoRedoDifferentLabelName(label, param, null, (p) =>
            {
                var undoKey = p as CourseCollectionParam;
                var curveKey1 = getCurveFunc(undoKey);
                var curveKey2 = getCurveFunc(param);
                if (curveKey1.length != curveKey2.length)
                    return true;

                for (int i = 0; i < curveKey1.length; i++)
                {
                    if (curveKey1.keys[i].time != curveKey2.keys[i].time)
                        return true;
                    if (curveKey1.keys[i].outTangent != curveKey2.keys[i].outTangent)
                        return true;
                    if (curveKey1.keys[i].outWeight != curveKey2.keys[i].outWeight)
                        return true;
                    if (curveKey1.keys[i].value != curveKey2.keys[i].value)
                        return true;
                    if (curveKey1.keys[i].inTangent != curveKey2.keys[i].inTangent)
                        return true;
                    if (curveKey1.keys[i].inWeight != curveKey2.keys[i].inWeight)
                        return true;
                    if (curveKey1.keys[i].weightedMode != curveKey2.keys[i].weightedMode)
                        return true;
                }

                return false;
            });
        }

        private void DrawEffectParamGUI_ColorCorrection(StoryRaceEditorWindow parent, CourseCollectionParam param)
        {
            parent.GUI_Toggle("Use ColorCorrection", ref param.useColorCorrection, param, (p) => { p.useColorCorrection = param.useColorCorrection; }, nameof(param.useColorCorrection));

            //----------------------------------------------------
            parent.GUI_FloatField("Saturation", ref param.ColorCorrectionSaturation, param, (p) => { p.ColorCorrectionSaturation = param.ColorCorrectionSaturation; }, nameof(param.ColorCorrectionSaturation));

            //----------------------------------------------------
            using (var scope = new EditorGUILayout.HorizontalScope())
            {
                var label = GetUndoRedoLabelNameFromAnimationCurve("R", parent, param, (p) => p.colorCorrectionRedChannel);
                EditorGUILayout.LabelField(label);
                parent.GUI_CurveField("", ref param.colorCorrectionRedChannel, param,
                    (p) => { p.colorCorrectionRedChannel = new AnimationCurve(param.colorCorrectionRedChannel.keys); }, nameof(param.colorCorrectionRedChannel));
            }
            using (var scope = new EditorGUILayout.HorizontalScope())
            {
                var label = GetUndoRedoLabelNameFromAnimationCurve("G", parent, param, (p) => p.colorCorrectionGreenChannel);
                EditorGUILayout.LabelField(label);
                parent.GUI_CurveField("", ref param.colorCorrectionGreenChannel, param,
                    (p) => { p.colorCorrectionGreenChannel = new AnimationCurve(param.colorCorrectionGreenChannel.keys); }, nameof(param.colorCorrectionGreenChannel));
            }
            using (var scope = new EditorGUILayout.HorizontalScope())
            {
                var label = GetUndoRedoLabelNameFromAnimationCurve("B", parent, param, (p) => p.colorCorrectionBlueChannel);
                EditorGUILayout.LabelField(label);
                parent.GUI_CurveField("", ref param.colorCorrectionBlueChannel, param,
                    (p) => { p.colorCorrectionBlueChannel = new AnimationCurve(param.colorCorrectionBlueChannel.keys); }, nameof(param.colorCorrectionBlueChannel));
            }

            //----------------------------------------------------
            if (param is CourseBaseParamInterp interpParam)
            {
                // 実際に補間する場合のみチェックボックスを表示する。
                if (interpParam.InterpolationType != CourseInterpolationType.None)
                {
                    parent.GUI_Toggle("Blend Prev RGB", ref param.IsBlendColorChannel, param, (p) => { p.IsBlendColorChannel = param.IsBlendColorChannel; }, nameof(param.IsBlendColorChannel));
                }
            }
        }

        #endregion
    }

    public class ColorCollectionTrack_CameraPostEffect : ColorCollectionTrack
    {
        private StoryRaceEditorKeyTrackBridge.TimelineLayers _baseLayerType;
        public ColorCollectionTrack_CameraPostEffect(StoryRaceEditorWindow parent, StoryRaceEditorKeyTrackBridge.TimelineLayers layerType) : base(parent)
        {
            _baseLayerType = layerType;
        }

        public ColorCollectionTrack_CameraPostEffect(ColorCollectionTrack_CameraPostEffect src) : base(src)
        {
            _baseLayerType = src._baseLayerType;
        }
        
        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return base.GetTimelineLayer() - (int)StoryRaceEditorKeyTrackBridge.TimelineLayers.Dof + (int)_baseLayerType + 1;
        }
        
        public override TrackBase Clone()
        {
            var result = new ColorCollectionTrack_CameraPostEffect(this);
            result.Param = new CourseCollectionParam(Param);
            return result;
        }
        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as ColorCollectionTrack_CameraPostEffect;
            return ValueEquals(_param, track._param);
        }
    }
    
    public class StoryRaceEditorColorCollectionKeyTrackBridge_CameraPostEffect : StoryRaceEditorColorCollectionKeyTrackBridge
    {
        private readonly TimelineLayers _baseLayerType;
        public StoryRaceEditorColorCollectionKeyTrackBridge_CameraPostEffect(TimelineLayers layerType)
        {
            _baseLayerType = layerType;
        }
        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new ColorCollectionTrack_CameraPostEffect(parent, _baseLayerType);
        }
        public override TimelineLayers GetTimelineLayer()
        {
            return base.GetTimelineLayer() - (int)TimelineLayers.Dof + (int)_baseLayerType + 1;
        }
        
        #region GUI
        private bool _controlPostEffectFoldout = true;
        public override bool OnDrawGUI(TimelineTrack track, StoryRaceEditorWindow parent, float width)
        {
            CourseCollectionParam param = ((ColorCollectionTrack)track).Param;
            bool needsUpdate = false;

            needsUpdate |= OnDrawGUI_CourseBaseParamInterp(track, parent, param);
            needsUpdate |= base.OnDrawGUI(track, parent, width);

            using (var scope = new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.Width(width)))
            {
                _controlPostEffectFoldout = FoldOut("PostEffect制御", _controlPostEffectFoldout);
                if (_controlPostEffectFoldout)
                {
                    EditorGUI.indentLevel++;
                    needsUpdate |= DrawEffectParamGUI_Collection(parent, track, param);
                    EditorGUI.indentLevel--;
                }
            }

            return needsUpdate;
        }

        private bool DrawEffectParamGUI_Collection(StoryRaceEditorWindow parent, TimelineTrack track, CourseCollectionParam param)
        {
            bool needsUpdate = false;
            using (var check = new EditorGUI.ChangeCheckScope())
            {
                parent.GUI_Toggle("共通のPostEffectの上書き", ref param.IsOverrideCommonParam, param, (p) => { p.IsOverrideCommonParam = param.IsOverrideCommonParam; }, nameof(param.IsOverrideCommonParam));
                needsUpdate |= check.changed;
            }

            return needsUpdate;
        }
        #endregion
    }
}

#endif