#if UNITY_EDITOR && CYG_DEBUG

using UnityEngine;
using UnityEditor;

namespace Gallop
{
    public class TimescaleTrack : TrackBase
    {
        public const StoryRaceEditorKeyTrackBridge.TimelineLayers TimelineLayer = StoryRaceEditorKeyTrackBridge.TimelineLayers.Timescale;

        private CourseTimescaleParam _param;

        public override void SetBaseParam(CourseBaseParam param)
        {
            _param = param as CourseTimescaleParam;
            base.SetBaseParam(param);
        }

        public CourseTimescaleParam Param
        {
            set
            {
                SetBaseParam(value);
            }

            get
            {
                return _param;
            }
        }

        private TimescaleTrack() { }

        public TimescaleTrack(StoryRaceEditorWindow parent) : base(parent)
        {
        }

        public TimescaleTrack(TimescaleTrack src) : base(src)
        {
        }

        public override StoryRaceEditorKeyTrackBridge.TimelineLayers GetTimelineLayer()
        {
            return TimelineLayer;
        }

        public override bool IsInterpolated()
        {
            return false;
        }

        public override TrackBase Clone()
        {
            var result = new TimescaleTrack(this);
            result.Param = new CourseTimescaleParam(Param);
            return result;
        }
        public override bool IsCompareTrack(TimelineTrack otherTrack)
        {
            var track = otherTrack as TimescaleTrack;
            return ValueEquals(_param, track._param);
        }
    }

    public class StoryRaceEditorTimescaleKeyTrackBridge : StoryRaceEditorKeyTrackBridge
    {
        private bool _timescaleFoldOut = true;

        public StoryRaceEditorTimescaleKeyTrackBridge()
        {
        }

        public override TimelineTrack CreateTrack(StoryRaceEditorWindow parent)
        {
            return new TimescaleTrack(parent);
        }

        public override TimelineLayers GetTimelineLayer()
        {
            return TimescaleTrack.TimelineLayer;
        }

        public override void SetParamToTrack(StoryRaceEditorWindow parent, TimelineTrack track, CourseBaseParam param)
        {
            ((TimescaleTrack)track).Param = (CourseTimescaleParam)param;
        }

        public override CourseBaseParam CreateParam(StoryRaceEditorWindow parent, int index, float startDistance, TimelineTrack track, CourseBaseParam param)
        {
            param = CreateParam<CourseTimescaleParam>(param);
            var sequence = parent._raceCameraEvent.GetSequenceTimescale();
            ((TimescaleTrack)track).Param = sequence.Insert(index, startDistance, (CourseTimescaleParam)param);
            return param;
        }

        public override CourseBaseParam GetParam(StoryRaceEditorWindow parent, int index)
        {
            var sequence = parent._raceCameraEvent.GetSequenceTimescale();
            return sequence.GetParam(index);
        }

        public override void RemoveParam(StoryRaceEditorWindow parent, int index)
        {
            var sequence = parent._raceCameraEvent.GetSequenceTimescale();
            sequence.RemoveAt(index);
        }

        public override void SortKey(StoryRaceEditorWindow parent)
        {
            var sequence = parent._raceCameraEvent.GetSequenceTimescale();
            var layer = GetTimelineLayer();
            var selectTrack = parent._selectedTrack[(int)layer] as TimescaleTrack;
            parent.SortParameter<CourseTimescaleParam>(layer, sequence.param, selectTrack.Param,
                (track, param) => { ((TimescaleTrack)track).Param = param; }
                );
        }

        public override CourseBaseParam CloneParamFromTrack(TimelineTrack track)
        {
            TimescaleTrack cameraTrack = track as TimescaleTrack;
            if (cameraTrack == null)
                return null;

            return cameraTrack.Param.Clone();
        }

        public override CourseBaseParam[] GetAllParam(StoryRaceEditorWindow parent)
        {
            var sequence = parent._raceCameraEvent.GetSequenceTimescale();
            return sequence.param;
        }

        #region GUI
        public override bool OnDrawGUI(TimelineTrack track, StoryRaceEditorWindow parent, float width)
        {
            var param = ((TimescaleTrack)track).Param;
            bool needsUpdate = false;

            needsUpdate |= OnDrawGUI_StartDistance(track, parent, param);

            using (new EditorGUILayout.VerticalScope(GUI.skin.box, GUILayout.Width(width)))
            {
                _timescaleFoldOut = FoldOut("Timescale Parameter", _timescaleFoldOut);
                if (_timescaleFoldOut)
                {
                    EditorGUI.indentLevel++;

                    using (var check = new EditorGUI.ChangeCheckScope())
                    {
                        parent.GUI_FloatField("Timescale", ref param.timescale, param, (p) => { p.timescale = param.timescale; }, nameof(param.timescale));

                        needsUpdate |= check.changed;
                    }

                    EditorGUI.indentLevel--;
                }
            }

            return needsUpdate;
        }

        #endregion

    }
}

#endif