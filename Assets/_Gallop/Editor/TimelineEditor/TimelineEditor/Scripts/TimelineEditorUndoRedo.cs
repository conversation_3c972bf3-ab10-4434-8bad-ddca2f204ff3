#if UNITY_EDITOR && CYG_DEBUG

using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

namespace Gallop
{
    /// <summary>
    /// Undoの実行コマンド
    /// </summary>
    public class TimelineEditorUndoRedo
    {
        /// <summary>
        /// ループスタック
        /// </summary>
        /// <typeparam name="T"></typeparam>
        public class DropOutStack<T>
            where T : class
        {
            private T[] ItemArray;
            private int Top = 0;
            private int Capacity = 0;
            public int Count = 0;

            public DropOutStack(int capacity)
            {
                ItemArray = new T[capacity];
                Capacity = capacity;
            }

            public void Push(T item)
            {
                ItemArray[Top] = item;
                Top = (Top + 1) % Capacity;
                Count = System.Math.Min((Count + 1), Capacity);
            }

            public T Pop()
            {
                Top = (Capacity + Top - 1) % Capacity;
                Count = System.Math.Max((Count - 1), 0);
                var item = ItemArray[Top];
                ItemArray[Top] = null;
                return item;
            }

            public T At(int index)
            {
                if (index < Count)
                {
                    int head = (Capacity + Top - 1) % Capacity;
                    index = (index > head) ? Capacity + (head - index) : head - index;
                    return ItemArray[index];
                }
                return null;
            }

            public void Clear()
            {
                for (int i = 0; i < Capacity; i++)
                {
                    ItemArray[i] = null;
                }
                Top = 0;
                Count = 0;
            }
        }

        /// <summary>
        /// コマンド基底
        /// </summary>
        public abstract class OpeCmd
        {
            public abstract bool Do();
            public abstract OpeCmd CreateUndoOpeCmd();
            public abstract bool Validate();
        }

        /// <summary>
        /// 複数コマンドを一つのUndoで実行するためのグループクラス
        /// </summary>
        public class OpeCmdGroup : OpeCmd
        {
            private List<OpeCmd> _opeCmdList = new List<OpeCmd>();

            public void AddOpeCmd(OpeCmd cmd)
            {
                _opeCmdList.Add(cmd);
            }

            public override bool Do()
            {
                foreach (var ope in _opeCmdList)
                {
                    if (!ope.Do())
                    {
                        return false;
                    }
                }
                return true;
            }

            public override OpeCmd CreateUndoOpeCmd()
            {
                var cmd = new OpeCmdGroup();
                //逆順でUndo用コマンドを追加していく
                for (int i = _opeCmdList.Count - 1; i >= 0; i--)
                {
                    cmd._opeCmdList.Add(_opeCmdList[i].CreateUndoOpeCmd());
                }
                return cmd;
            }

            public override bool Validate()
            {
                foreach (var ope in _opeCmdList)
                {
                    if (!ope.Validate())
                    {
                        return false;
                    }
                }
                return true;
            }
        }

        public class OperationCommander
        {
            private const int DEFAULT_STACK_DEPTH = 128;

            //Stack深度（＝Undo回数上限）
            private int _stackDepth = DEFAULT_STACK_DEPTH;
            public int StackDepth
            {
                get { return _stackDepth; }
                set
                {
                    if (_stackDepth != value)
                    {
                        _undoStack = new DropOutStack<OpeCmd>(_stackDepth);
                        _doStack = new DropOutStack<OpeCmd>(_stackDepth);
                    }
                    _stackDepth = value;
                }
            }

            //Undo/Redoスタック
            private DropOutStack<OpeCmd> _undoStack = new DropOutStack<OpeCmd>(DEFAULT_STACK_DEPTH);
            private DropOutStack<OpeCmd> _doStack = new DropOutStack<OpeCmd>(DEFAULT_STACK_DEPTH);

            public bool CanUndo()
            {
                return _undoStack.Count > 0;
            }
            public bool CanRedo()
            {
                return _doStack.Count > 0;
            }
            public string GetInfo()
            {
                return string.Format("Undo:{0}, Redo:{1}, Limit:{2}", _undoStack.Count, _doStack.Count, _stackDepth);
            }
            public void Clear()
            {
                _undoStack.Clear();
                _doStack.Clear();
            }

            public bool RunCmd(OpeCmd cmd)
            {
                if (cmd.Validate() == false || cmd.Do() == false)
                {
                    Debug.LogWarning("TimelineEditor RunCmd failed. " + cmd.ToString());
                    return false;
                }
                var undoCmd = cmd.CreateUndoOpeCmd();
                _undoStack.Push(undoCmd);
                _doStack.Clear();
                return true;
            }

            public void Redo()
            {
                if (_doStack.Count == 0)
                {
                    Debug.LogWarning("TimelineEditor Command.Do Stack is Empty");
                    return;
                }
                var cmd = _doStack.Pop();
                if (cmd.Validate() == false || cmd.Do() == false)
                {
                    Debug.LogWarning("TimelineEditor Command.Do failed. " + cmd.ToString());
                    return;
                }
                _undoStack.Push(cmd.CreateUndoOpeCmd());
            }

            public void Undo()
            {
                if (_undoStack.Count == 0)
                {
                    Debug.LogWarning("TimelineEditor Command.Undo Stack is Empty");
                    return;
                }
                var cmd = _undoStack.Pop();
                if (cmd.Validate() == false || cmd.Do() == false)
                {
                    Debug.LogWarning("TimelineEditor Command.Undo failed. " + cmd.ToString());
                    return;
                }
                _doStack.Push(cmd.CreateUndoOpeCmd());
            }
        }
    }
}

#endif