using UnityEngine;
using UnityEditor;
using UnityEditor.Callbacks;
#if UNITY_IOS
using UnityEditor.iOS.Xcode;
using UnityEditor.iOS.Xcode.Extensions;
#endif
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using static Definitions;

internal static class Definitions {

    internal class SigningConfig {

        public Dictionary<string, string> RewriteItems { private set; get; }

        public SigningConfig(string codeSign, string provisioningProfile)
        {
            RewriteItems = new Dictionary<string, string>
            {
                {"CODE_SIGN_IDENTITY", codeSign},
                {"PROVISIONING_PROFILE_SPECIFIER", provisioningProfile},
            };
        }

    }

    internal static readonly string AppExtensionRoot = "AppExtension-iOS";
    internal static readonly string[] Configurations = {"Debug", "Release", "ReleaseForProfiling", "ReleaseForRunning"};

    internal static readonly string BuildConfigKeyDevelopmentTeam = "DEVELOPMENT_TEAM";
    internal static readonly string BuildConfigValueDevelopmentTeam = "52U5LN3279";
    internal static readonly string BuildConfigKeyDeploymentTarget = "IPHONEOS_DEPLOYMENT_TARGET";

    internal static readonly Dictionary<string, SigningConfig> SigningConfigsForApplication = new Dictionary<string, SigningConfig>() {
        { "Debug", new SigningConfig("iPhone Developer", "Omotenashi Unity5 (Dev)") },
        { "Release", new SigningConfig("iPhone Distribution", "Omotenashi Unity5 (In-House)") },
        { "ReleaseForProfiling", new SigningConfig("iPhone Distribution", "Omotenashi Unity5 (In-House)") },
        { "ReleaseForRunning", new SigningConfig("iPhone Distribution", "Omotenashi Unity5 (In-House)") },
    };

    internal static readonly string[] SynchronizePlistKey = {"CFBundleShortVersionString", "CFBundleVersion"};
}
#if UNITY_IOS
internal static class PBXProjectExtension
{
    private static string GetUnityAppTargetGuid(this PBXProject project)
    {
        return project.GetUnityMainTargetGuid();
    }

    [SuppressMessage("ReSharper", "UnusedParameter.Local")]
    private static string GetUnityAppBundleIdentifier(this PBXProject project)
    {
        return PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.iOS);
    }

    /// <summary>
    /// AppExtension をプロジェクトに追加します。
    /// </summary>
    /// <remarks>
    /// 追加したい AppExtension は AppExtensionRoot 以下に設置している必要があります
    /// </remarks>
    /// <param name="project"></param>
    /// <param name="outputRoot">プロジェクトファイルのルートフォルダ</param>
    /// <param name="name">ターゲット名。フォルダと一致している必要があります</param>
    /// <param name="dependencies">依存するフレームワーク</param>
    /// <param name="appInfoPlist">アプリケーションの Info.plist ファイル</param>
    public static void AddAppExtension(
        this PBXProject project, string outputRoot, string name, string[] dependencies, PlistDocument appInfoPlist)
    {
        string mainGuid = project.GetUnityMainTargetGuid();
        string outputExtensionRoot = Path.Combine(outputRoot, name);

        // ファイルを追加
        FileUtil.CopyFileOrDirectory(
            Path.Combine(Definitions.AppExtensionRoot, name), outputExtensionRoot);

        // ターゲットを追加
        Func<string, bool> isInfoPlist = path => Path.GetFileName(path) == "Info.plist";
        string infoPlistFilePath = Directory.GetFiles(outputExtensionRoot).Single(isInfoPlist);
        string bundleIdentifier = string.Format("{0}.{1}", project.GetUnityAppBundleIdentifier(), name);
        string appExtensionGuid = project.AddAppExtension(
            mainGuid, name, bundleIdentifier, infoPlistFilePath);

        // Xcode 10 以降 (New Build System) でビルドする場合はファイルの追加操作が重複するとエラーとなる
        // Info.plist は AddAppExtension 経由でバイナリに含められるため、 AddFileToBuild で追加する対象からは除外する
        var filesAddToBuild = Directory.GetFiles(outputExtensionRoot).Where(path => !isInfoPlist(path));
        foreach (string srcPath in filesAddToBuild) {
            string extension = Path.GetExtension(srcPath);
            if (extension.Equals(".meta") || extension.Equals(".h"))
            {
                continue;
            }
            string relativePath = Path.Combine(name, Path.GetFileName(srcPath));
            string fileGuid = project.AddFile(srcPath, relativePath);
            project.AddFileToBuild(appExtensionGuid, fileGuid);
        }

        // 各種ビルド設定を変更
        string teamId = System.Environment.GetEnvironmentVariable("IPA_TEAM_ID") ?? PlayerSettings.iOS.appleDeveloperTeamID;
        System.Console.WriteLine("teamId = " + teamId);
        project.SetBuildProperty(appExtensionGuid, Definitions.BuildConfigKeyDevelopmentTeam, teamId);
        project.SetBuildProperty(appExtensionGuid, Definitions.BuildConfigKeyDeploymentTarget, "10.0"); // AppExtension の最低 Deployment Target は 10.0
        project.SetBuildProperty(appExtensionGuid, "ARCHS", "$(ARCHS_STANDARD)"); // armv7 だけになってしまうので arm64 を含むよう対応
        project.SetBuildProperty(appExtensionGuid, "ALWAYS_SEARCH_USER_PATHS", "NO"); // ビルドワーニング対応

        string provisioning = System.Environment.GetEnvironmentVariable("IPA_EXTENSION_PROVISIONING") ?? "";
        System.Console.WriteLine("provisioning = " + provisioning);
        if (!string.IsNullOrEmpty(provisioning))
        {
            provisioning = provisioning.Replace(".mobileprovision", "");
            Dictionary<string, SigningConfig> SigningConfigsForExtension = new Dictionary<string, SigningConfig>() {
                { "Debug", new SigningConfig("iPhone Distribution", provisioning) },
                { "Release", new SigningConfig("iPhone Distribution", provisioning) },
                { "ReleaseForProfiling", new SigningConfig("iPhone Distribution", provisioning) },
                { "ReleaseForRunning", new SigningConfig("iPhone Distribution", provisioning) },
            };

            foreach (string configuration in Definitions.Configurations)
            {
                string configurationGuid = project.BuildConfigByName(appExtensionGuid, configuration);
                foreach (var pair in SigningConfigsForExtension[configuration].RewriteItems)
                {
                    project.SetBuildPropertyForConfig(configurationGuid, pair.Key, pair.Value);
                }
            }
        }

        // 必要なフレームワークを追加
        foreach (string dependency in dependencies) {
            project.AddFrameworkToProject(appExtensionGuid, dependency, false);
        }

        // Info.plist のバージョンをアプリケーションのものと揃える
        // 揃っていない場合、バイナリアップロード時に警告 (ITMS-90473) が表示される
        var plistExtension = new PlistDocument();
        plistExtension.ReadFromFile(infoPlistFilePath);
        foreach (string key in Definitions.SynchronizePlistKey)
        {
            string stringValue = appInfoPlist.root.values.Single(pair => pair.Key == key).Value.AsString();
            plistExtension.root.SetString(key, stringValue);
        }
        // TODO: Unity2017 で AppExtension のキーが消える問題に対応 (Number を PlistDocument で読むと消えてしまう)
        plistExtension.root
            .values["NSExtension"].AsDict()
            .values["NSExtensionAttributes"].AsDict()
            .SetString("UNNotificationExtensionInitialContentSizeRatio", "0.01");
        plistExtension.WriteToFile(infoPlistFilePath);
    }
}
#endif

/// <summary>
/// 出力後に追加処理を行うためのスクリプトの見本です。
/// </summary>
public class OmoPostBuildProcessor : MonoBehaviour
{

    private static readonly string OmotePlistFileName = "AppOmotenashi.plist";
    private static readonly string OmotePlistFilePath = "Libraries/" + OmotePlistFileName;

    /// <summary>
    /// 出力後の追加処理を行います。
    /// </summary>
    /// <param name="buildTarget">Target（iPhone/Androidなど）</param>
    /// <param name="path">出力先のパス（ビルド時に指定するパス）</param>
    [PostProcessBuild]
    public static void OnPostprocessBuild(BuildTarget buildTarget, string path)
    {
        // 操作によっては BuildTarget の値とコンパイル時定数が矛盾する場合があるので、その場合はエラーにする
        switch (buildTarget)
        {
            case BuildTarget.Android:
#if UNITY_ANDROID
                OnPostprocessBuildForAndroid();
                break;
#else
                throw new InvalidOperationException("UNITY_ANDROID not defined. Switching plagform and running again may solve the problem.");
#endif

            case BuildTarget.iOS:
#if UNITY_IOS
                OnPostprocessBuildForIOS(path);
                break;
#else
                throw new InvalidOperationException("UNITY_IOS not defined. Switching plagform and running again may solve the problem.");
#endif

#if !DMM
            default:
                throw new NotImplementedException(
                    string.Format("BuildTarget not supported: {0}", Enum.GetName(typeof(BuildTarget), buildTarget)));
#endif
        }
    }

    [SuppressMessage("ReSharper", "UnusedMember.Local")]
    private static void OnPostprocessBuildForAndroid()
    {
        Debug.Log("No operation in post process build for Android.");
    }
#if UNITY_IOS
    private static void OnPostprocessBuildForIOS(string path)
    {
        string pbxProjectPath = PBXProject.GetPBXProjectPath(path);
        var project = new PBXProject();
        project.ReadFromString(File.ReadAllText(pbxProjectPath));

        string targetGUID = project.GetUnityFrameworkTargetGuid();

        // フレームワークの追加 (Omotenashiでは、CoreTelephony が必須)
        project.AddFrameworkToProject(targetGUID, "CoreTelephony.framework", false);

        //昔のコードでは追加していたが、不要になった？不明なのでコメントアウト
//        project.AddFrameworkToProject(target, "Security.framework", false);
//        project.AddFrameworkToProject(target, "AssetsLibrary.framework", false);

        // ファイルの追加
        // AppOmotenashi.plist は プロジェクトにスクリプトで追加する必要があります。
        File.Copy(Path.Combine("Assets/Plugins/iOS", OmotePlistFileName), Path.Combine(path, OmotePlistFilePath));
        project.AddFileToBuild(project.GetUnityMainTargetGuid(), project.AddFile(OmotePlistFilePath, OmotePlistFilePath));

        // 従来のようにアプリ起動時にプッシュ通知確認ダイアログを表示させる場合は、下記の一行を有効にします。
        // proj.AddBuildProperty(targetGUID, "GCC_PREPROCESSOR_DEFINITIONS", "OMOTENASHI_USE_LEGACY_INTERNAL_REGISTER");
        // Unityが自動判別で UNITY_USES_REMOTE_NOTIFICATIONS を 0 にしてしまうことがあるので手作業で 1 にする
        // 1にしないとリモート通知が使えなくなる
        string preprocessorPath = Path.Combine(path, "Classes/Preprocessor.h");
        string text = File.ReadAllText(preprocessorPath);
        text = text.Replace("UNITY_USES_REMOTE_NOTIFICATIONS 0", "UNITY_USES_REMOTE_NOTIFICATIONS 1");
        File.WriteAllText(preprocessorPath, text);

        // ローカル通知用 AppExtension の追加
        var plist = new PlistDocument();
        plist.ReadFromFile(Path.Combine(path, "Info.plist"));
        project.AddAppExtension(path, "DefaultNotificationContent", new [] {"UIKit.framework"}, plist);

        // プッシュ通知の有効化
#if IS_ADHOC
        EnablePushNotification(project, path, targetGUID);
#endif

        // 署名を指定されたものに置き換え
        //foreach (string configuration in Definitions.Configurations) {
        //    string configurationGuid = project.BuildConfigByName(targetGUID, configuration);
        //    project.SetBuildPropertyForConfig(
        //        configurationGuid, Definitions.BuildConfigKeyDevelopmentTeam, Definitions.BuildConfigValueDevelopmentTeam);
        //    foreach (var pair in Definitions.SigningConfigsForApplication[configuration].RewriteItems) {
        //        project.SetBuildPropertyForConfig(configurationGuid, pair.Key, pair.Value);
        //    }
        //}
        // プロジェクトファイルの書き出し
        // プロジェクトの内容に変更を加えたらこれをしないと適用されない
        File.WriteAllText(pbxProjectPath, project.WriteToString());
    }

    /// <summary>
    /// プッシュ通知を有効化します
    /// </summary>
    /// <param name="project">出力されるプロジェクト</param>
    /// <param name="path">出力先のパス</param>
    /// <param name="targetGuid">設定対象のターゲット</param>
    private static void EnablePushNotification(PBXProject project, string path, string targetGuid)
    {
        string entitlementFileName = "OmoUnity5.entitlements";
        string entitlementFilePath = Path.Combine(path, entitlementFileName);

        var capabilityManager = new ProjectCapabilityManager(
            PBXProject.GetPBXProjectPath(path), entitlementFilePath, null, targetGuid);
        // TODO: 本来は Configuration 別に development, production の設定ができるのがよいが、プロジェクト毎にしか設定できないので一旦 development にしておく
        capabilityManager.AddPushNotifications(true);
        capabilityManager.AddBackgroundModes(BackgroundModesOptions.RemoteNotifications);
        capabilityManager.WriteToFile();

        project.AddCapability(targetGuid, PBXCapabilityType.PushNotifications, entitlementFilePath);
    }
#endif
}
