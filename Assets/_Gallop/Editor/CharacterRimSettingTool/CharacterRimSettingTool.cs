#if CYG_DEBUG
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEditor;
using Gallop;

namespace Gallop
{

    /// <summary>
    /// キャラモデルのリムまわりの設定を試すエディタツール
    /// キャラモデル(ModelController)と平行光(Light)を設定してパラメタをいじると適用されます。
    /// </summary>
    public class CharacterRimSettingTool : EditorWindow
    {
        private ModelController _model = null;
        private Light _light = null;

        private Color _rimColor = Color.white;
        private float _rimStep = 0.0f;
        private float _rimFeather = 0.0f;
        private float _rimSpecRate = 0.0f;

        private Color _lightColor = Color.white;
        private Vector3 _lightDirection = Vector3.down;

        private StringBuilder _builder = new StringBuilder();
        private string _outputText = string.Empty;

        [MenuItem("Gallop/Tools/CharacterRimSettingTool", false, Gallop.EditorMenuPriority.TOOLS)]
        public static void ShowWindow()
        {
            CharacterRimSettingTool tool = GetWindow<CharacterRimSettingTool>();
            tool.Show();
        }

        private void OnGUI()
        {
            bool needsUpdateOutput = false;

            EditorGUILayout.LabelField("Target Objects");

            using (var check = new EditorGUI.ChangeCheckScope())
            {
                _model = (ModelController)EditorGUILayout.ObjectField("Chara Model", _model, typeof(ModelController), true);

                needsUpdateOutput |= check.changed;
            }

            using (var check = new EditorGUI.ChangeCheckScope())
            {
                _light = (Light)EditorGUILayout.ObjectField("Directional Light", _light, typeof(Light), true);

                needsUpdateOutput |= check.changed;

                if (check.changed)
                {
                    _lightColor = _light.color;
                    _lightDirection = _light.transform.rotation.eulerAngles;
                }
            }

            HorizontalSplitter();

            EditorGUILayout.LabelField("Rim Settings");

            using (var check = new EditorGUI.ChangeCheckScope())
            {
                _rimColor = EditorGUILayout.ColorField("Rim Color", _rimColor);
                _rimStep = EditorGUILayout.FloatField("Rim Step", _rimStep);
                _rimFeather = EditorGUILayout.FloatField("Rim Feather", _rimFeather);
                _rimSpecRate = EditorGUILayout.FloatField("Rim Spec Rate", _rimSpecRate);

                needsUpdateOutput |= check.changed;

                if (check.changed && _model != null)
                {
                    _model.SetRimParameter(_rimColor, _rimStep, _rimFeather, _rimSpecRate);
                }
            }

            HorizontalSplitter();

            EditorGUILayout.LabelField("Directional Light Settings");

            using (var check = new EditorGUI.ChangeCheckScope())
            {
                _lightColor = EditorGUILayout.ColorField("Light Color", _lightColor);
                _lightDirection = EditorGUILayout.Vector3Field("Light Direction", _lightDirection);

                needsUpdateOutput |= check.changed;

                if (check.changed && _light != null)
                {
                    _light.color = _lightColor;
                    _light.transform.rotation = Quaternion.Euler(_lightDirection);
                }
            }

            HorizontalSplitter();

            EditorGUILayout.LabelField("Output");

            if (needsUpdateOutput)
            {
                _outputText = GetOutputText();
            }

            EditorGUILayout.SelectableLabel(_outputText, GUILayout.Height(160));

            if (GUILayout.Button("Copy To Clipboard"))
            {
                GUIUtility.systemCopyBuffer = _outputText;
            }
        }

        private string GetOutputText()
        {
            _builder.Length = 0;

            _builder.AppendFormat("Rim Color (float): {0}\n", _rimColor.ToString());
            _builder.AppendFormat("Rim Color (int): {0:###}, {1:###}, {2:###}, {3:###}\n",
                _rimColor.r * 255, _rimColor.g * 255, _rimColor.b * 255, _rimColor.a * 255);
            _builder.AppendFormat("Rim Step : {0}\n", _rimStep);
            _builder.AppendFormat("Rim Feather : {0}\n", _rimFeather);
            _builder.AppendFormat("Rim Spec Rate : {0}\n", _rimSpecRate);
            _builder.Append("---\n");
            _builder.AppendFormat("Light Color (float): {0}\n", _lightColor.ToString());
            _builder.AppendFormat("Light Color (int): {0:###}, {1:###}, {2:###}, {3:###}\n",
                _lightColor.r * 255, _lightColor.g * 255, _lightColor.b * 255, _lightColor.a * 255);
            _builder.AppendFormat("Light Direction (euler): {0}\n", _lightDirection.ToString());
            _builder.AppendFormat("Light Direction (quat): {0}\n", Quaternion.Euler(_lightDirection).ToString());

            return _builder.ToString();
        }

        private void HorizontalSplitter()
        {
            GUILayout.Box("", GUILayout.Height(1), GUILayout.MaxHeight(1), GUILayout.MinHeight(1), GUILayout.ExpandWidth(true));
        }

    }
}

#endif