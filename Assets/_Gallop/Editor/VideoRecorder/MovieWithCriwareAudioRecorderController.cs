using System;
using System.IO;
using UnityEditor;
using UnityEditor.Recorder;
using UnityEditor.Recorder.Input;
using UnityEngine;
using Object = UnityEngine.Object;
using System.Collections;

namespace Gallop
{
    /// <summary>
    /// UnityのStandardVideoRecorderで録画
    /// </summary>
    public class MovieWithCriwareAudioRecorderController : UnityVideoRecorderControllerBase
    {
        #region Constant
        
        private const MovieRecorderSettings.VideoRecorderOutputFormat DEFAULT_VIDEO_FORMAT =
            MovieRecorderSettings.VideoRecorderOutputFormat.MP4;

        private const VideoBitrateMode DEFAULT_VIDEO_BITRATE = VideoBitrateMode.High;

        private const int DEFAULT_RESOLUTION_WIDTH = 1920;
        private const int DEFAULT_RESOLUTION_HEIGHT = 1080;
        
        private static readonly string DEFAULT_OUTPUT_DIRECTORY =
            Path.Combine(Application.dataPath, "..", "..", "Recording");

        private const string DEFAULT_OUTPUT_FILE_NAME = "Movie";
        
        private static readonly string DEFAULT_OUTPUT_FILE_PATH = string.Format("{0}/{1}{2}",
            DEFAULT_OUTPUT_DIRECTORY, DEFAULT_OUTPUT_FILE_NAME, DefaultWildcard.Take);
        
        #endregion
        
        #region property
        
        private MovieWithCriWareAudioRecorderSettings _recorderSettings;
        
        #endregion
        
        #region Accessor
        
        public override MovieRecorderSettings.VideoRecorderOutputFormat VideoFormat
        {
            get => !Initialized ? default : _recorderSettings.OutputFormat;
            set
            {
                Debug.Assert(CanChangeSettings, ERROR_MESSAGE_CHANGE_SETTINGS_WHILE_RECORDING);
                if (!CanChangeSettings)
                {
                    return;
                }

                _recorderSettings.OutputFormat = value;
            }
        }        
        
        public override VideoBitrateMode BitrateMode
        {
            get => !Initialized ? default : _recorderSettings.VideoBitRateMode;
            set
            {
                Debug.Assert(CanChangeSettings, ERROR_MESSAGE_CHANGE_SETTINGS_WHILE_RECORDING);
                if (!CanChangeSettings)
                {
                    return;
                }

                _recorderSettings.VideoBitRateMode = value;
            }
        }
        
        public override Vector2Int OutputResolution
        {
            get => !Initialized
                ? Vector2Int.zero
                : new Vector2Int(_recorderSettings.ImageInputSettings.OutputWidth,
                    _recorderSettings.ImageInputSettings.OutputHeight);
            set
            {
                Debug.Assert(CanChangeSettings, ERROR_MESSAGE_CHANGE_SETTINGS_WHILE_RECORDING);
                if (!CanChangeSettings)
                {
                    return;
                }

                // Unity RecorderのMP4設定では奇数の解像度は許可していない。
                // GameViewより大きいサイズの解像度を指定しても出力された動画の見栄えには影響しないため、奇数の場合は解像度を増やす
                _recorderSettings.ImageInputSettings.OutputWidth = value.x % 2 == 0 ? value.x : value.x + 1;
                _recorderSettings.ImageInputSettings.OutputHeight = value.y % 2 == 0 ? value.y : value.y + 1;
            }
        }

        #endregion
        
        /// <summary>
        /// メインのレコーダーを取得
        /// </summary>
        /// <returns></returns>
        protected override RecorderSettings GetMainRecorderSettings()
        {
            return _recorderSettings;
        }
        
        /// <summary>
        /// 録レコーダーオブジェクトを登録する
        /// </summary>
        /// <param name="recorderSettings"></param>
        protected override void RegisterRecorderSettings(RecorderControllerSettings controllerSettings)
        {
            // Video
            _recorderSettings = ScriptableObject.CreateInstance<MovieWithCriWareAudioRecorderSettings>();
            _recorderSettings.name = "Gallop Video Recorder";
            _recorderSettings.Enabled = true;

            _recorderSettings.OutputFormat = DEFAULT_VIDEO_FORMAT;
            _recorderSettings.VideoBitRateMode = DEFAULT_VIDEO_BITRATE;
            _recorderSettings.FrameRatePlayback = FrameRatePlayback.Variable;

            _recorderSettings.ImageInputSettings = new GameViewInputSettings
            {
                OutputWidth = DEFAULT_RESOLUTION_WIDTH,
                OutputHeight = DEFAULT_RESOLUTION_HEIGHT
            };

            _recorderSettings.AudioInputSettings.PreserveAudio = true;

            _recorderSettings.OutputFile = DEFAULT_OUTPUT_FILE_PATH;
            
            controllerSettings.AddRecorderSettings(_recorderSettings);
        }
        
        /// <summary>
        /// レコーダーオブジェクトを破棄する
        /// </summary>
        /// <returns></returns>
        protected override void DisposeRecorderSettings()
        {
            Object.Destroy(_recorderSettings);
        }
    }
}
