#if CYG_DEBUG && UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace Gallop
{
    //-------------------------------------------------------------------
    /// <summary>
    /// エディタ拡張：コースカメラプレハブのデータエラー検出。
    /// </summary>
    //-------------------------------------------------------------------
    public class EditorCourseCameraCheck : EditorWindow
    {
        // リソース検索対象フォルダ。
        private const string SEARCH_PATH = "Assets/_GallopResources/Bundle/Resources/Race/Course";

        class ResourceCheckResult
        {
            public GameObject _object = null;
            public string _path = string.Empty;

            public bool _isErrorImageEffectParamSizeZero = false;
            public bool _isErrorFlipParamsSizeZero = false;
            public bool _isErrorStartGateHide = false;

            public bool HasError
            {
                get
                {
                    return _isErrorImageEffectParamSizeZero || _isErrorFlipParamsSizeZero || _isErrorStartGateHide;
                }
            }
        }

        private List<ResourceCheckResult> _foundErrors = new List<ResourceCheckResult>();
        private string _searchPath = "";
        private Vector2 _scrollPosition = Vector2.zero;
        private int _selectIndex = -1;


        //---------------------------------------------------------------
        [MenuItem("Gallop/レース/CourseCameraCheck")]
        static void Open()
        {
            EditorWindow.GetWindow<EditorCourseCameraCheck>("CourseCameraCheck");
        }

        //---------------------------------------------------------------
        void OnGUI()
        {
            UpdateSearch();
            UpdateResult();
        }


        //---------------------------------------------------------------
        private void UpdateSearch()
        {
            //-----------------------------------------------------------
            // GUI：検索対象フォルダ表示。
            //-----------------------------------------------------------
            if (_searchPath.Length <= 0)
            {
                _searchPath = SEARCH_PATH;
            }
            _searchPath = EditorGUILayout.TextField( "Search Folder:", _searchPath );

            //-----------------------------------------------------------
            // Checkボタン押下時処理。
            //-----------------------------------------------------------
            if (GUILayout.Button("Check"))
            {
                _selectIndex = -1;
                _scrollPosition = Vector2.zero;

                //-----------------------------------------------------------
                // 検索条件に一致するオブジェクトのリストを構築する。
                //-----------------------------------------------------------
                string[] astrAllFilePath = System.IO.Directory.GetFiles(_searchPath, "*", System.IO.SearchOption.AllDirectories);
                _foundErrors.Clear();
                for (int i = 0; i < astrAllFilePath.Length; ++i)
                {
                    //-------------------------------------------------------
                    // プレハブ以外は無視。
                    //-------------------------------------------------------
                    if (System.IO.Path.GetExtension(astrAllFilePath[i]) != ".prefab")
                    {
                        continue;
                    }

                    //-------------------------------------------------------
                    // Resources/以降のファイルパスを取得。
                    //-------------------------------------------------------
                    var path = astrAllFilePath[i].Replace("\\", "/");
                    {
                        string[] separator = new string[] { "/Resources/" };
                        var paths = path.Split(separator, System.StringSplitOptions.None);
                        if (paths.Length < 2)
                        {
                            continue;
                        }
                        path = paths[1];
                        var ext = Path.GetExtension(path);
                        path = path.Replace(ext, "");
                    }

                    //-------------------------------------------------------
                    // プレハブロード。
                    //-------------------------------------------------------
                    var prefab = Resources.Load(path, typeof(GameObject)) as GameObject;
                    if (null == prefab)
                    {
                        continue;
                    }
                    var component = prefab.GetComponent<CourseCameraTable>();
                    if (null == component)
                    {
                        continue;
                    }

                    //-------------------------------------------------------
                    // エラー検出処理。
                    //-------------------------------------------------------
                    var checkInfo = new ResourceCheckResult();
                    checkInfo._object = prefab;
                    checkInfo._path = Path.GetFileName( path );
                
                    var serializedObject = new SerializedObject(component);

                    // ImageEffectParamsのSizeが0かどうかの検知
                    {
                        var prop = serializedObject.FindProperty("imageEffectParams");
                        if( null != prop )
                        {
                            checkInfo._isErrorImageEffectParamSizeZero = ( prop.arraySize == 0 );
                        }
                    }
                    // FlipParamsのSizeが0かどうかの検知
                    {
                        var prop = serializedObject.FindProperty("flipParams");
                        if( null != prop )
                        {
                            checkInfo._isErrorFlipParamsSizeZero = ( prop.arraySize == 0 );
                        }
                    }
                    // StartGateHideが未設定かどうかの検知
                    {
                        // デフォルトエラーとし、StartGateHideが設定されているのを見つけたらfalseにする。
                        checkInfo._isErrorStartGateHide = true;

                        var prop = serializedObject.FindProperty("cameraParams");
                        if( null != prop )
                        {
                            if( prop.arraySize > 0 )
                            {
                                for( int i_p = 0; i_p < prop.arraySize; ++i_p )
                                {
                                    var propCameraParams = prop.GetArrayElementAtIndex( i_p );
                                    var propStartGateHide = propCameraParams.FindPropertyRelative( "_startGateHide" );
                                    if( propStartGateHide.boolValue )
                                    {
                                        checkInfo._isErrorStartGateHide = false;
                                        break;
                                    }
                                }
                            }
                        }
                    }

                    if( checkInfo.HasError )
                    {
                        _foundErrors.Add(checkInfo);
                    }
                }


                //-----------------------------------------------------------
                // 検索終了後処理。
                //-----------------------------------------------------------
                if (0 == _foundErrors.Count)
                {
                    // 見つからなかった。処理終了。
                    Debug.LogError( "Error not found. Folder=" + _searchPath);
                }
            }
        }

        //---------------------------------------------------------------
        private void UpdateResult()
        {
            if (_foundErrors.Count <= 0)
            {
                return;
            }

            EditorGUILayout.BeginVertical();
            {
                //-----------------------------------------------------------
                // エラー件数表示。
                //-----------------------------------------------------------
                EditorGUILayout.LabelField( string.Format("Found {0} objects.", _foundErrors.Count) );

                EditorGUILayout.Separator();
                GUILayout.Box("", GUILayout.ExpandWidth(true), GUILayout.Height(1));

                //-----------------------------------------------------------
                // タイトル。
                //-----------------------------------------------------------
                EditorGUILayout.BeginHorizontal();
                {
                    EditorGUILayout.LabelField("ファイル名");
                    EditorGUILayout.LabelField("ImageEffectParamsのSize0");
                    EditorGUILayout.LabelField("FlipParamsのSize0");
                    EditorGUILayout.LabelField("StartGateHideが未設定");
                }
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Separator();

                //-----------------------------------------------------------
                // 検索結果。
                //-----------------------------------------------------------
                EditorGUILayout.BeginVertical();
                _scrollPosition = EditorGUILayout.BeginScrollView( _scrollPosition );
                {
                    var styleNormal = EditorStyles.label;
                    var styleSelected = new GUIStyle();
                    styleSelected.border = styleNormal.border;
                    styleSelected.contentOffset = styleNormal.contentOffset;            
                    styleSelected.normal.background = styleNormal.normal.background;
                    styleSelected.padding = styleNormal.padding;
                    styleSelected.normal.textColor = Color.red;

                    for (int i = 0; i < _foundErrors.Count; i++)
                    {
                        EditorGUILayout.BeginHorizontal();
                        {
                            var result = _foundErrors[i];

                            // ファイルパスをボタンで表示。押下したらプロジェクト内で選択。
                            if( GUILayout.Button(result._path) )
                            {
                                Object[] newSelectionArray = { result._object as Object };
                                Selection.objects = newSelectionArray;
                                _selectIndex = i;
                            }

                            var style = ( _selectIndex == i ) ? styleSelected : styleNormal;
                            EditorGUILayout.LabelField( GetErrorText( result._isErrorImageEffectParamSizeZero ), style );
                            EditorGUILayout.LabelField( GetErrorText( result._isErrorFlipParamsSizeZero ), style );
                            EditorGUILayout.LabelField( GetErrorText( result._isErrorStartGateHide ), style );
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                }
                EditorGUILayout.EndVertical();
                EditorGUILayout.EndScrollView();
            }
            EditorGUILayout.EndVertical();
        }

        private string GetErrorText( bool isError )
        {
            return isError ? "エラー" : "---";
        }
    }
}
#endif
