using UnityEditor;
using UnityEngine;

namespace Gallop
{
    [CustomEditor(typeof(HomeCameraLayoutHolder))]
    public class HomeCameraLayoutHolderInspector : Editor
    {
        private bool _isAreaDataFoldOut = false;
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();

            var holder = target as HomeCameraLayoutHolder;
            _isAreaDataFoldOut = EditorGUILayout.Foldout(_isAreaDataFoldOut, "ミラー設定");
            if (_isAreaDataFoldOut)
            {
                int size = EditorGUILayout.DelayedIntField("Size", holder.AreaMirrorBlendDataArray.Length);
                if (size != holder.AreaMirrorBlendDataArray.Length)
                {
                    var newBuffer = new HomeCameraLayoutHolder.MirrorBlendData[size];
                    int copySize = size > holder.AreaMirrorBlendDataArray.Length ? size : holder.AreaMirrorBlendDataArray.Length;
                    System.Array.Copy(holder.AreaMirrorBlendDataArray, newBuffer, copySize);
                    holder.SetAreaMirrorBlendDataArray(newBuffer);
                }

                for (int i = 0; i < holder.AreaMirrorBlendDataArray.Length; i++)
                {
                    var data = holder.AreaMirrorBlendDataArray[i];
                    EditorGUILayout.LabelField(((HomeDefine.HomeArea)(i)).ToString());
                    EditorGUI.indentLevel++;
                    data.BlendRate = EditorGUILayout.FloatField("Blend Rate", data.BlendRate);
                    data.BlendColor = EditorGUILayout.ColorField("Blend Color", data.BlendColor);
                    EditorGUI.indentLevel--;
                }

                if(GUI.changed)
                {
                    EditorUtility.SetDirty(holder);
                }
            }
        }
    }
}
