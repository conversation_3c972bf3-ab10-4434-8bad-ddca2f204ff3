Shader "Gallop/3D/Chara/Toon/S"
{
    Properties
    {
        [NoScaleOffset]_MainTex("Diffuse Map", 2D) = "white" {}

        [NoScaleOffset]_TripleMaskMap("_TripleMaskMap", 2D) = "white" {}
        [NoScaleOffset]_OptionMaskMap("_OptionMaskMap", 2D) = "black" {}
        [MaterialToggle]_UseOptionMaskMap("_UseOptionMaskMap", int) = 0

        [Header(Specular)]
        //[NoScaleOffset]_SpecularMap("_SpecularMap", 2D) = "black" {}
        _SpecularColor("_SpecularColor", Color) = (1, 1, 1, 1)
        _SpecularPower("_SpecularPower", Range(0, 1)) = 0

        [Header(Toon)]
        [NoScaleOffset]_ToonMap("_ToonMap", 2D) = "white" {}
        _ToonStep("_ToonStep", Range(0, 1)) = 0.5
        _ToonFeather("_ToonFeather", Range(0.0001, 1)) = 0.0001

        [Header(Environment)]
        [NoScaleOffset]_EnvMap("_EnvMap", 2D) = "black" {}
        _EnvRate("_EnvRate",Range(0,1)) = 0.5
        _EnvBias("_EnvBias",Range(0,8)) = 1

        [Header(Rim)]
        //[NoScaleOffset]_RimMask("_RimMask", 2D) = "black" {}
        _RimStep("_RimStep", Range(0,1)) = 0.5
        _RimFeather("_RimFeather", Range(0.0001, 1)) = 0.3
        _RimColor("_RimColor",Color) = (1,1,1,0.3922)
        _RimSpecRate("_RimSpecRate", Range(0,1)) = 0
        _RimShadow("_RimShadow", Range(0,2)) = 0

        // アウトライン
        [Header(Outline)]
        _OutlineWidth("_OutlineWidth",Range(0.01,5)) = 1
        _OutlineColor("_OutlineColor",Color) = (0.125,0.047,0,0.196)

        // 汚し
        [Header(Dirt)]
        [NoScaleOffset]_DirtTex("_DirtTex", 2D) = "black" {}
        // 汚れ倍率
        _DirtScale("_DirtScale",Range(0,1)) = 1

        // エミッシブ
        [Header(Emissive)]
        [NoScaleOffset]_EmissiveTex("_EmissiveTex", 2D) = "black" {}
        _EmissiveColor("_EmissiveColor", Color) = (1,1,1,1)

        [Header(Dither)]
        [NoScaleOffset]_DitherTex("_DitherTex",2D) = "white" {}
        _DitherCutt("_DitherCutt",Range(0,1)) = 0.5

        [Header(Other)]
        _CharaColor("_CharaColor", Color) = (1,1,1,1)
        _Silhouette("_Silhouette", Range(0,1)) = 0
        _Saturation("_Saturation", Range(0,1)) = 1
        _ToonBrightColor("_ToonBrightColor", Color) = (1,1,1,0)
        _ToonDarkColor("_ToonDarkColor", Color) = (1,1,1,0)
        _StencilMask ("Stencil Mask", int) = 100
        [Enum(UnityEngine.Rendering.CompareFunction)]_StencilComp("Stencil Comparison", int) = 8	//UnityEngine.Rendering.CompareFunction.Disable
        [Enum(UnityEngine.Rendering.StencilOp)]_StencilOp("Stencil Operation",int) = 2	//UnityEngine.Rendering.StencilOp.Keep

        _LightProbeColor("_LightProbeColor", Color) = (1,1,1,1)

        _Cutoff("_Cutoff",Range(0,1)) = 0.5

        //ライト方向指定
        _UseOriginalDirectionalLight("_UseOriginalDirectionalLight", int) = 0
        _OriginalDirectionalLightDir("_OriginalDirectionalLightDir", Vector) = (1,1,1,1)

        //高さライト
        [HideInInspector] _HightLightParam("_HightLightParam",Vector) = (0,1,0,0)	//StartY,MaxHightの順番
        [HideInInspector] _HightLightColor("_HightLightColor",Color) = (0,0,0,0)

        // 法線を正規化するかどうか（==0:正規化しない, ==1:正規化する）
        [HideInInspector] _NormalizeNormal("_NormalizeNormal", Range(0,1)) = 0

        // マスクカラー
        [Header(MaskColor)]
        [NoScaleOffset]_MaskColorTex("MaskColorTex", 2D) = "gray" {}
        _MaskColorR1("MaskColorR1", Color) = (1,1,1,1)
        _MaskColorR2("MaskColorR2", Color) = (1,1,1,1)
        _MaskColorG1("MaskColorG1", Color) = (1,1,1,1)
        _MaskColorG2("MaskColorG2", Color) = (1,1,1,1)
        _MaskColorB1("MaskColorB1", Color) = (1,1,1,1)
        _MaskColorB2("MaskColorB2", Color) = (1,1,1,1)
        _MaskToonColorR1("MaskToonColorR1", Color) = (1,1,1,1)
        _MaskToonColorR2("MaskToonColorR2", Color) = (1,1,1,1)
        _MaskToonColorG1("MaskToonColorG1", Color) = (1,1,1,1)
        _MaskToonColorG2("MaskToonColorG2", Color) = (1,1,1,1)
        _MaskToonColorB1("MaskToonColorB1", Color) = (1,1,1,1)
        _MaskToonColorB2("MaskToonColorB2", Color) = (1,1,1,1)

        [HideInInspector] _FaceCenterPos("_FaceCenterPos",Vector) = (0,0,0,0)
        [HideInInspector] _DirtRate("_DirtRate",Vector) = (0,0,0,0)
        [HideInInspector] _RimShadowRate("_RimShadowRate",float) = 0
        [HideInInspector] _RimHorizonOffset("_RimHorizonOffset",float) = 0
        [HideInInspector] _RimVerticalOffset("_RimHorizonOffset",float) = 0
        [HideInInspector] _RimStep2("_RimStep2",float) = 0.1
        [HideInInspector] _RimFeather2("_RimFeather2",float) = 0.01
        [HideInInspector] _RimColor2("_RimColor2",Vector) = (1,1,1,1)
        [HideInInspector] _RimSpecRate2("_RimSpecRate2",float) = 0.5
        [HideInInspector] _RimHorizonOffset2("_RimHorizonOffset2",float) = 0.0
        [HideInInspector] _RimVerticalOffset2("_RimVerticalOffset2",float) = 0.0
        [HideInInspector] _RimShadowRate2("_RimShadowRate2",float) = 0.0
        [HideInInspector] _VertexColorToonPower("_VertexColorToonPower",float) = 0.0

#shaderLab_code_enable USE_UV_EMISSIVE
        // UVスクロールエミッシヴ
        [Header(UVEmissive)]
        _UVEmissiveTex("_UVEmissiveTex",2D) = "black" {}
        _UVEmissiveMaskTex("_UVEmissiveMaskTex",2D) = "black" {}
        _UVEmissiveRange("_UVEmissiveRange",Vector) = (0,0,1,1)
        [HideInInspector] _UVEmissiveScroll("_UVEmissiveScroll",Vector) = (0,0,0,0)
        [HideInInspector] _UVEmissivePower("_UVEmissivePower",float) = 0
#shaderLab_code_enable_end
    }

    SubShader
    {
        Tags{
            "RenderType" = "Opaque"
            "Queue" = "Geometry-1"//    キャラを優先的に書きたいので-1しておく
        }
        LOD 100

        Pass
        {
            Name "Toon"
            Tags{//DirectionalLightの情報を取得するために必須
                "LightMode" = "UniversalForward"
            }

            Cull Back
            ZWrite On
            ZTest LEqual

            Stencil
            {
                Ref [_StencilMask]
                Comp [_StencilComp]
                Pass [_StencilOp]
            }

            HLSLPROGRAM
#pragma vertex vert
#pragma fragment frag
#pragma target 3.0

#pragma multi_compile_fragment _ USE_MASK_COLOR
#pragma multi_compile _ _MAIN_LIGHT_SHADOWS
#pragma multi_compile _ _ADDITIONAL_LIGHTS
#define _SHADOWS_SOFT
#pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS

#define PASS PASS_MAIN_COLOR

#define TOON_SHADING
#define TOON_SPECULAR
#define TOON_ENVIRONMENT
#define TOON_RIM
#define TOON_RIM_CONTROLL
#define TOON_CUTOUT
#define USE_DIRT
#define USE_FOG
#define USE_FIXPROJECTION
#define USE_DITHER
#define USE_HIGHT_LIGHT
#define USE_UV_EMISSIVE
#include "hlsl/GallopCharaToon.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "Outline"
            Tags{//DirectionalLightの情報を取得するために必須
                "LightMode" = "Outline"
            }

            Cull Front
            ZTest Less
            /*UNUSE_ALPHA*/Blend One Zero
            /*USE_ALPHA*/Blend SrcAlpha OneMinusSrcAlpha
            Offset 1, -1    //    メインパスがアウトラインに負けないように変更

            Stencil
            {
                Ref [_StencilMask]
                Comp [_StencilComp]
                Pass [_StencilOp]
            }

            HLSLPROGRAM
#pragma multi_compile_fragment _ USE_MASK_COLOR
#pragma target 3.0
#pragma vertex vert
#pragma fragment frag
#pragma fragmentoption ARB_precision_hint_fastest

#define PASS PASS_OUTLINE
#define USE_OUTLINE_MAINTEX

#define TOON_SHADING
#define TOON_SPECULAR
#define TOON_ENVIRONMENT
#define TOON_RIM
#define TOON_RIM_CONTROLL
#define TOON_CUTOUT
#define USE_DIRT
#define USE_FOG
#define USE_FIXPROJECTION
#define USE_DITHER
#define USE_HIGHT_LIGHT
#define USE_UV_EMISSIVE
#include "hlsl/GallopCharaToon.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags{ "LightMode" = "ShadowCaster" }

            // depthを書き込んだ際に隙間ができてしまう問題への対処
            // * 法線をスムーズにしている場合は起きず、ハードエッジにしたときにメッシュが分割されて発生する
            // * xxxxx発生していないのはメイン法線がアウトライン法線がであり、その法線はスムーズであるため
            // * Gallopはメイン法線がトゥーン法線（ハードエッジ）
            // * 解決方法はメイン法線をアウトライン法線に変えるか、DOF時のカリング設定を両面にする
            // * メイン法線を変更するのは作業コストが発生するので避けたい
            Cull Off

            HLSLPROGRAM
#pragma vertex vert
#pragma fragment frag

#define PASS PASS_SHADOW_CASTER

//デプス収集やShadowmap生成時にはAndroid固有のデプス補正処理は行わない
#define DISABLE_LOG_DEPTH

#define TOON_SHADING
#define TOON_SPECULAR
#define TOON_ENVIRONMENT
#define TOON_RIM
#define TOON_RIM_CONTROLL
#define TOON_CUTOUT
#define USE_DIRT
#define USE_FOG
#define USE_FIXPROJECTION
#define USE_HIGHT_LIGHT
#define USE_UV_EMISSIVE
#include "hlsl/GallopCharaToon.hlsl"

            ENDHLSL
        }

        #shaderLab_variant TOON_SHADING,TOON_SPECULAR,TOON_ENVIRONMENT,TOON_RIM,TOON_RIM_CONTROLL,TOON_CUTOUT,USE_DIRT,USE_FOG,USE_FIXPROJECTION,USE_DITHER,USE_HIGHT_LIGHT,USE_UV_EMISSIVE

        #shaderLab_projector
        #shaderLab_blendprojector
        #shaderLab_mulprojector
        #shaderLab_addprojector
    }
}
