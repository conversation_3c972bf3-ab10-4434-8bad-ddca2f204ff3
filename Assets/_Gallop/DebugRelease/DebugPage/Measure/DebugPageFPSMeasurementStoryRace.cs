#if !CYG_PRODUCT

using System.Collections.Generic;

namespace Gallop
{
    [DebugPageAttribute(typeof(DebugPageMeasurementRoot))]
    public class DebugPageFPSMeasurementStoryRace : DebugPageFPSMeasurement
    {
        public override bool IsResidentTextEnable() { return true; }

        public override string OverrideResidentText()
        {
            return "計測するストーリーレースのIDを選択";
        }

        /// <summary>
        /// メニューリスト
        /// </summary>
        private static readonly Dictionary<string, DebugButtonDetail> DEFAULT_MENU_DIC = new Dictionary<string, DebugButtonDetail>()
        {
            {"20001092", new DebugButtonDetail( () => ChangeMeasurementStoryRace(20001092) ) },
            {"20006172", new DebugButtonDetail( () => ChangeMeasurementStoryRace(20006172) ) },
            {"20003132", new DebugButtonDetail( () => ChangeMeasurementStoryRace(20003132) ) },
            {"20202032", new DebugButtonDetail( () => ChangeMeasurementStoryRace(20202032) ) },
            {"20202062", new DebugButtonDetail( () => ChangeMeasurementStoryRace(20202062) ) },
            {"20202132", new DebugButtonDetail( () => ChangeMeasurementStoryRace(20202132) ) },
        };

        protected override Dictionary<string, DebugButtonDetail> DefaultMenuList() { return DEFAULT_MENU_DIC; }

        /// <summary>
        /// 指定された楽曲でLiveのFPS計測画面に遷移
        /// </summary>
        private static void ChangeMeasurementStoryRace(int storyRaceId)
        {
            MeasurementDirectUtil.StoryRaceId = storyRaceId;
            ChangeMeasurementView(FPSMeasurementDirectSceneController.MeasurementType.StoryRace);
        }
    }
}
#endif  // !CYG_PRODUCT //
