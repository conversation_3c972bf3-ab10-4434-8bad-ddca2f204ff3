#if !CYG_PRODUCT
using UnityEngine.UI;

namespace Gallop
{
    /// <summary>
    /// 
    /// 文字表示デバッグメニュー基底
    ///
    /// 　文字を表示する機能のみが必要な場合、DebugPageBaseの代わりにこのクラスを継承することで、実装を簡略化できます。
    /// 
    /// [使い方]
    /// 
    ///  1. DebugPageLabelBaseを継承したクラスを作成します。
    ///
    ///  2. UpdateMenu() を継承・実装します。
    ///  　 文字列ができたら、_text.textに値を設定します。
    /// 
    ///  3. DebugPageAttributeをクラスに付与します。
    /// 　　[DebugPageAttribute()]
    /// 　　
    /// Confulenceのページも併せてご参照下さい。
    /// https://xxxxxxxxxx/pages/viewpage.action?pageId=7243489
    ///  
    /// </summary>
    public abstract class DebugPageLabelBase : DebugPageBase
    {
        /// <summary>
        /// 本メニューで文字列表示に使用するTextComponent
        /// 継承先でこのコンポーネントの値を書き換えてください
        /// </summary>
        protected Text _text;

        public override bool IsResidentTextEnable() { return false; }

        /// <summary>
        /// ページ内容の構築
        /// </summary>
        protected override void BuildMenu()
        {
            var textObj = _parentController.UIPartsFactory.AddTextLabel(_debugPageRoot);
            _text = textObj.GetComponent<Text>();
        }
    }
}
#endif  // !CYG_PRODUCT //