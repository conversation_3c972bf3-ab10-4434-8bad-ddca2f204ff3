using System;
using System.IO;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;
using Cyan.Utility;

namespace Cyan.Manifest
{
    public static class ManifestLoader
    {
        public class ManifestLoaderQuery : ManifestDBAdapter
        {
            readonly PreparedQuery idQuery;
            readonly PreparedQuery idByCategoryQuery;
            readonly PreparedQuery deleteByIdQuery;
            readonly PreparedQuery hnameStateByIdQuery;
            readonly PreparedQuery diffStateQuery;
            readonly PreparedQuery hnameStateQuery;
            readonly PreparedQuery updateEntryQuery;
            readonly PreparedQuery insertEntryQuery;
            readonly PreparedQuery insertDependencyQuery;

            internal ManifestLoaderQuery(ManifestDB db) : base(db)
            {
                idQuery = PreparedQuery("SELECT `i` FROM `a` WHERE `n` = ?");
                idByCategoryQuery = PreparedQuery("SELECT `i` FROM `a` WHERE `m` = ?");
                deleteByIdQuery = PreparedQuery("DELETE FROM `a` WHERE `i` = ?");
                diffStateQuery = PreparedQuery("SELECT `i`, `c`, `h`, `s`, `g`, `p`, `d` FROM `a` WHERE `n` = ?");
                hnameStateQuery = PreparedQuery("SELECT `h`, `s` FROM `a` WHERE `n` = ?");
                hnameStateByIdQuery = PreparedQuery("SELECT `h`, `s` FROM `a` WHERE `i` = ?");

                updateEntryQuery = PreparedQuery("UPDATE `a` SET `d`=?, `g`=?, `l`=?, `c`=?, `h`=?, `m`=?, `k`=?, `s`=?, `p`=?  WHERE `n` = ?");
                insertEntryQuery = PreparedQuery("INSERT INTO `a` (`n`,`d`,`g`,`l`,`c`,`h`,`m`,`k`,`s`,`p`) VALUES(?,?,?,?,?,?,?,?,?,?)");
                insertDependencyQuery = PreparedQuery("INSERT INTO `r` (`f`, `t`, `d`) VALUES (?, ?, ?)");
            }

            #region Manifest Update
            /// <summary>
            /// マニフェスト記載項目とDB上の行を比較して、差分を検出する
            /// </summary>
            /// <param name="item">マニフェストエントリ</param>
            /// <param name="id">DB上のプライマリキー</param>
            /// <param name="checksum">DB上のチェックサム</param>
            /// <param name="hname">DB上のhname。ファイル内容の差分があり、かつ所持している場合以外はnull</param>
            /// <param name="state">DB上のstate</param>
            /// <param name="rowChanged">行Updateが必要かどうか</param>
            /// <returns>item.nameがDB上に登録されていればtrue</returns>
            public bool GetDiffState(in ManifestEntry item, out int id, out ulong checksum, out string hname, out int state, out bool rowChanged)
            {
                var name = item.name;
                diffStateQuery.BindText(1, name);
                id = 0;
                checksum = 0;
                hname = null;
                state = 0;
                rowChanged = false;

                bool ret;

                if (ret = diffStateQuery.Step())
                {
                    id = diffStateQuery.GetInt(0);
                    checksum = unchecked((ulong)diffStateQuery.GetLong(1));
                    state = diffStateQuery.GetInt(3);
                    
                    // OPTIMIZE: Updateが重いので、行に実際に差分が入った時だけ行うために更新対象を一番重いdepsの比較まで段階的にチェックしていく - ykst
                    rowChanged = item.checksum != checksum;

                    if (!rowChanged)
                    {
                        // groupのみの変更の検出
                        var group = diffStateQuery.GetInt(4);
                        rowChanged = item.group != group;
                    }

                    if (!rowChanged)
                    {
                        // priorityのみの変更の検出
                        var priority = diffStateQuery.GetInt(5);
                        rowChanged = item.priority != priority;
                    }

                    if (!rowChanged)
                    {
                        // depsのみの変更の検出:
                        //   DB上の依存関係カラムの文字列ポインタを取り出して、エントリのbyte[]とローレベルな比較を行う。
                        var depsPtr = diffStateQuery.GetTextPtr(6);
                        var len = item.deps?.Length ?? 0;

                        if (depsPtr == IntPtr.Zero)
                        {
                            rowChanged = len != 0;
                        }
                        else
                        {
                            unsafe
                            {
                                byte* dbDepsPtr = (byte*)depsPtr;

                                fixed (byte* manifestDepsPtr = item.deps)
                                {
                                    for (int i = 0; i < len; ++i)
                                    {
                                        if (dbDepsPtr[i] == 0 || manifestDepsPtr[i] != dbDepsPtr[i])
                                        {
                                            rowChanged = true;
                                            break;
                                        }
                                    }

                                    if (!rowChanged)
                                    {
                                        rowChanged = dbDepsPtr[len] != 0;
                                    }
                                }
                            }
                        }
                    }

                    // OPTIMIZE: hnameの取り出しではstringが生成されるので、本当に必要な場合のみ取り出す。
                    if (state == ManifestDB.EntryState.Exists && (checksum != item.checksum || item.size == 0))
                    {
                        hname = diffStateQuery.GetText(2);
                    }
                }

                diffStateQuery.Reset();

                return ret;
            }

            public bool GetHnameState(byte[] name, out string hname, out int state)
            {
                hnameStateQuery.BindText(1, name);

                hname = null;
                state = 0;
                bool ret;

                if (ret = hnameStateQuery.Step())
                {
                    hname = hnameStateQuery.GetText(0);
                    state = hnameStateQuery.GetInt(1);
                }

                hnameStateQuery.Reset();

                return ret;
            }

            public bool GetDownloadInfo(int id, out ulong checksum, out ulong size)
            {
                bool ret;

                checksum = 0;
                size = 0;

                using (var query = Query($"SELECT `c`,`l` FROM `a` WHERE `i` = {id}"))
                {
                    if (ret = query.Step())
                    {
                        checksum = unchecked((ulong)query.GetLong(0));
                        size = unchecked((ulong)query.GetLong(1));
                    }
                }

                return ret;
            }

            public void DeleteAllFromCategoryExcept(string name, string category)
            {
                Guard.True(Exec($"DELETE FROM `a` WHERE `m` = '{category}' AND `n` <> '{name}'"));
            }

            public void UpdateRow(byte[] category, int kind, in ManifestEntry entry, int state)
            {
                updateEntryQuery.BindText(1, entry.deps);
                updateEntryQuery.BindInt(2, unchecked((int)entry.group));
                updateEntryQuery.BindLong(3, unchecked((long)entry.size));
                updateEntryQuery.BindLong(4, unchecked((long)entry.checksum));
                updateEntryQuery.BindText(5, entry.hname);
                updateEntryQuery.BindText(6, category);
                updateEntryQuery.BindInt(7, kind);
                updateEntryQuery.BindInt(8, state);
                updateEntryQuery.BindInt(9, unchecked((int)entry.priority));
                updateEntryQuery.BindText(10, entry.name);

                Guard.True(updateEntryQuery.Exec());
                updateEntryQuery.Reset();
            }

            public void InsertRow(byte[] category, int kind, in ManifestEntry entry, int state)
            {
                insertEntryQuery.BindText(1, entry.name);
                insertEntryQuery.BindText(2, entry.deps);
                insertEntryQuery.BindInt(3, unchecked((int)entry.group));
                insertEntryQuery.BindLong(4, unchecked((long)entry.size));
                insertEntryQuery.BindLong(5, unchecked((long)entry.checksum));
                insertEntryQuery.BindText(6, entry.hname);
                insertEntryQuery.BindText(7, category);
                insertEntryQuery.BindInt(8, kind);
                insertEntryQuery.BindInt(9, state);
                insertEntryQuery.BindInt(10, unchecked((int)entry.priority));

                Guard.True(insertEntryQuery.Exec());
                insertEntryQuery.Reset();
            }

            public List<string> GetUpdatingAssetManifestsNames()
            {
                var list = new List<string>();

                using (var query = Query($@"
                    SELECT `a`.`n` FROM `a` 
                    LEFT JOIN `c` ON `a`.`n` = `c`.`n` 
                    WHERE `a`.`m` = '{Manifest.ManifestCategory.ASSET_MANIFEST}' 
                        AND (`a`.`s` = {ManifestDB.EntryState.Missing} OR `c`.`n` IS NULL OR `a`.`h` <> `c`.`h`)"))
                {
                    while (query.Step())
                    {
                        list.Add(query.GetText(0));                    
                    }
                }

                return list;
            }

            public void GetCategoryIds(HashSet<int> ids, byte[] category)
            {
                idByCategoryQuery.BindText(1, category);
                while (idByCategoryQuery.Step())
                {
                    ids.Add(idByCategoryQuery.GetInt(0));
                }
                idByCategoryQuery.Reset();
            }

            public void UpdateAssetManifestCache()
            {
                using (var query = Query($"SELECT `n`, `h` FROM `a` WHERE `m` = '{Manifest.ManifestCategory.ASSET_MANIFEST}' AND `s` = {ManifestDB.EntryState.Exists}"))
                using (var updater = PreparedQuery("INSERT OR REPLACE INTO `c` (`n`, `h`) VALUES (?, ?)"))
                {
                    while (query.Step())
                    {
                        updater.BindText(1, query.GetText(0));
                        updater.BindText(2, query.GetText(1));
                        Guard.True(updater.Exec());
                        updater.Reset();
                    }
                }
            }

            public string GetManifestCache(string name)
            {
                using (var query = Query($"SELECT `h` FROM `c` WHERE `n` = '{name}'"))
                {
                    if (query.Step())
                    {
                        return query.GetText(0);
                    }
                }

                return null;
            }

            public void UpdateManifestCache(string name, string hname)
            {
                using (var updator = Query($"INSERT OR REPLACE INTO `c` (`n`, `h`) VALUES ('{name}', '{hname}')"))
                {
                    Guard.True(updator.Exec());
                }
            }
            #endregion

            #region Garbage Collection
            public void RemoveRow(int id)
            {
                deleteByIdQuery.BindInt(1, id);
                Guard.True(deleteByIdQuery.Exec());
                deleteByIdQuery.Reset();
            }

            public List<string> GetExistingHnamesByIds(HashSet<int> ids)
            { 
                List<string> hashes = new List<string>(ids.Count);

                foreach (var id in ids)
                {
                    hnameStateByIdQuery.BindInt(1, id);
                    Guard.True(hnameStateByIdQuery.Step());
                    if (hnameStateByIdQuery.GetInt(1) == ManifestDB.EntryState.Exists)
                    {
                        var h = hnameStateByIdQuery.GetText(0);
                        hashes.Add(h);
                    }
                    hnameStateByIdQuery.Reset();
                }

                return hashes;
            }
            #endregion

            #region Dependency Solver
            public int GetId(string name)
            {
                idQuery.BindText(1, name);
                Guard.True(idQuery.Step(), "Unknown asset '" + name + "'");
                var result = idQuery.GetInt(0);
                idQuery.Reset();
                return result;
            }

            public void InsertDependency(int id, int depId, int depth)
            {
                insertDependencyQuery.BindInt(1, id);
                insertDependencyQuery.BindInt(2, depId);
                insertDependencyQuery.BindInt(3, depth);
                Guard.True(insertDependencyQuery.Exec());
                insertDependencyQuery.Reset();
            }

            public IEnumerable<(int id, string name, string deps)> GetIdsWithDeps()
            {
                return QueryIterator("SELECT `i`,`n`,`d` FROM `a` WHERE `d` IS NOT NULL AND d <> ''",
                    q => (q.GetInt(0), q.GetText(1), q.GetText(2)));
            }

            public IEnumerable<int> GetIdsWithoutDeps()
            {
                return QueryIterator("SELECT `i` FROM `a` WHERE `d` IS NULL OR d = ''", 
                    q => q.GetInt(0));
            }

            public int CountEntriesWithDeps()
            {
                int result;
                using (var countQuery = Query("SELECT COUNT(*) FROM `a` WHERE `d` IS NOT NULL AND d <> ''"))
                {
                    Guard.True(countQuery.Step());
                    result = countQuery.GetInt(0);
                }
                return result;
            }

            public void ClearDependencyTable()
            {
                Guard.True(Exec("DELETE FROM `r`"));
            }
            #endregion
        }

        public interface IEntryLoader : IEnumerable<ManifestEntry >
        {
            string GetCategory();
        }

        /// <summary>
        /// BSVを回す代わりに一ファイルだけマニフェストを処理する
        /// </summary>
        public class ManifestEntryLoader : IEntryLoader
        {
            readonly string category;
            ManifestEntry  entry;
            public ManifestEntryLoader(string category, string name, ulong size = 0, ulong checksum = 0)
            {
                this.category = category;

                using (var sha1 = new System.Security.Cryptography.SHA1CryptoServiceProvider())
                {
                    entry = new ManifestEntry
                    {
                        name = System.Text.Encoding.UTF8.GetBytes(ManifestDB.MANIFEST_NAME_PREFIX + name),
                        hname = ManifestEntry.CalcHNameString(sha1, checksum, size, System.Text.Encoding.UTF8.GetBytes(name)),
                        size = size,
                        checksum = checksum,
                    };
                }
            }

            public string GetCategory() => category;

            public IEnumerator<ManifestEntry > GetEnumerator()
            {
                yield return entry;
            }

            System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
            {
                return GetEnumerator();
            }
        }

        public class BsvEntryLoader : IEntryLoader
        {
            readonly ManifestEntry.Format format;
            readonly string category;
            readonly string bsvPath;
            readonly bool isManifestCategory;

            /// <summary>
            /// カテゴリ別マニフェストを展開し、一アイテムずつデータを取り出す
            /// </summary>
            /// <param name="db"></param>
            /// <param name="category">このマニフェストのカテゴリ。
            /// 取り出されたマニフェストの内のcategory(ManifestDB上での`m`)として付記される。マニフェストDB上にもリソースとしてこの名前でエントリが登録されていることが前提となる。</param>
            /// <param name="basePath">マニフェストが格納されているディレクトリのベースパス。DBから取り出した情報からこれに相対パスをくっつけて実際のファイルを特定する。</param>
            public BsvEntryLoader(ManifestLoaderQuery db, ManifestEntry.Format format, string category, string name, string basePath)
            {
                this.category = category;
                this.format = format;

                isManifestCategory = category == Manifest.ManifestCategory.ASSET_MANIFEST
                    || category == Manifest.ManifestCategory.PLATFORM_MANIFEST;

                var dbEntryNameForCategoryManifest = Encoding.UTF8.GetBytes(ManifestDB.MANIFEST_NAME_PREFIX + name);

                db.GetHnameState(dbEntryNameForCategoryManifest, out string hname, out int state);

                if (state != ManifestDB.EntryState.Exists)
                {
                    throw new Exception($"manifest file for category '{category}' is not recognized by ManifestDB");
                }

                bsvPath = System.IO.Path.Combine(basePath, hname.Substring(0, 2), hname);
            }

            public string GetCategory() => category;

            public IEnumerator<ManifestEntry > GetEnumerator()
            {
                using (var bsvReader = Cyan.Utility.BSVReader.Init(bsvPath, Cyan.Utility.BSVReader.ReadMode.Memory, isCompressed: true))
                {
                    Cyan.Utility.BSVReader.ILineParser<ManifestEntry> bsvParser = ManifestEntry.GetBsvParser(format, bsvReader);

                    ManifestEntry item = new ManifestEntry();
                    using (var sha1 = new System.Security.Cryptography.SHA1CryptoServiceProvider())
                    {
                        while (bsvReader.ReadLine(bsvParser, ref item))
                        {
                            item.hname = ManifestEntry.CalcHNameString(sha1, item.checksum, item.size, item.name);

                            // マニフェスト名はDB上でアセットと区別するために"//"を先頭に付けて管理するので、ここで摩り替える。
                            // ダウンロードや保存先を決定するhnameは元の名前で指定する。
                            if (isManifestCategory)
                            {
                                item.name = ManifestDB.MANIFEST_NAME_PREFIX_BYTES.Concat(item.name).ToArray();
                            }

                            yield return item;
                        }
                    }
                }
            }

            System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
            {
                return GetEnumerator();
            }
        }


        /// <summary>
        /// カテゴリ1つを対象としてマニフェストDBの更新を行う。この際に不要ファイル検出と削除のガベージコレクションも同時に行われる。
        /// </summary>
        /// <param name="db"></param>
        /// <param name="localBasePath">このパス配下にあるファイルに対してガベージコレクションを行う</param>
        /// <param name="provider">このカテゴリのマニフェスト情報を取り出すイテレータ</param>
        public static void UpdateManifestDBForCategory(ManifestLoaderQuery adapter, Dictionary<string, int> kindMap, string localBasePath, ManifestLoader.IEntryLoader provider, HashSet<string> deletePaths = null)
        {
            UpdateManifestDBForCategory(adapter, kindMap, localBasePath, new List<ManifestLoader.IEntryLoader> { provider }, deletePaths);
        }

        /// <summary>
        /// 複数カテゴリを対象としてマニフェストDBの更新を行う。この際に不要ファイル検出と削除のガベージコレクションも同時に行われる。
        /// </summary>
        /// <param name="adapter">マニフェストDBにアクセスするアダプター</param>
        /// <param name="kindMap">カテゴリ名をint値に変換する辞書</param>
        /// <param name="localBasePath">ここで対象とするマニフェスト中のファイルが置かれている親ディレクトリ</param>
        /// <param name="providers">マニフェスト項目を提供するイテレータ集合。要素は一つのマニフェストカテゴリに相当する</param>
        /// <param name="deletePaths">削除ファイルを登録するセット。nullを指定した場合はここの中で即座に削除する</param>
        /// <param name="onProgress">一つマニフェストカテゴリを処理する度に叩かれる進捗報告用コールバック</param>
        public static void UpdateManifestDBForCategory(ManifestLoaderQuery adapter, Dictionary<string, int> kindMap, string localBasePath, List<ManifestLoader.IEntryLoader> providers, HashSet<string> deletePaths = null, Action onProgress = null)
        {
            var obsoleteHnames = new HashSet<string>();
            var updatedHnames = new HashSet<string>();
            var obsoleteIds = new HashSet<int>();

            foreach (var provider in providers)
            {
                string categoryStr = provider.GetCategory();
                byte[] category = Encoding.UTF8.GetBytes(categoryStr);
                int kind = kindMap.TryGetValue(categoryStr, out int _kind) ? _kind : 0;

                // 登録済みのIDを全て不要ID候補として一度マークする
                adapter.GetCategoryIds(obsoleteIds, category);

                foreach (ManifestEntry item in provider)
                {
                    if (adapter.GetDiffState(in item, out int id, out ulong checksum, out string hname, out int state, out bool rowChanged))
                    {
                        // マニフェストに記載されていてDB登録済みのものは不要ID集合から除外する
                        obsoleteIds.Remove(id);

                        bool hashMismatch = (item.size == 0) || (item.checksum != checksum);
                        int newState;

                        // マニフェストDB上にsize = 0で登録されているデータは実体が分からないので、常時更新対象にする。
                        if (state == ManifestDB.EntryState.Exists)
                        {
                            if (hashMismatch)
                            {
                                obsoleteHnames.Add(hname);

                                if (item.size != 0)
                                {
                                    updatedHnames.Add(item.hname);
                                }
                                newState = ManifestDB.EntryState.Missing;
                            }
                            else
                            {
                                newState = ManifestDB.EntryState.Exists;
                            }

                        }
                        else if (state == ManifestDB.EntryState.Missing)
                        {
                            if (hashMismatch)
                            {
                                updatedHnames.Add(item.hname);
                            }

                            newState = ManifestDB.EntryState.Missing;
                        }
                        else
                        {
                            throw new Exception($"Unexpected state {state}: category={Encoding.UTF8.GetString(category)}, name={Encoding.UTF8.GetString(item.name)}");
                        }

                        if (state != newState || rowChanged)
                        {
                            adapter.UpdateRow(category, kind, in item, newState);
                        }
                    }
                    else
                    {
                        updatedHnames.Add(item.hname);
                        adapter.InsertRow(category, kind, in item, ManifestDB.EntryState.Missing);
                    }
                }

                onProgress?.Invoke();
            }

            obsoleteHnames.UnionWith(adapter.GetExistingHnamesByIds(obsoleteIds));

            foreach (string hname in obsoleteHnames)
            {
                // 同一ハッシュのファイルが移動した時に消してしまわないようにする。
                // これは全く同じファイルのカテゴリだけを変更した時に起きる
                if (updatedHnames.Contains(hname))
                {
                    Debug.Log($"File moved. Did someone change its category?: {hname}");
                }
                else
                {
                    string localPath = System.IO.Path.Combine(localBasePath, hname.Substring(0, 2), hname);
#if UNITY_EDITOR
                    // 削除対象ファイルが存在しなかった場合はデバッグ警告を出す。無くてもFile.Deleteで例外は飛ばないので、実機ではstatを省略する
                    if (!File.Exists(localPath))
                    {
                        Debug.LogWarning($"Potential file inconsistency: obsolete {localPath} does not exist");
                    }
#endif
                    // 削除セットを渡された場合は後で消せるようにパスを登録する
                    // 少量のデータであることが分かっている場合などでこれがnullの場合は、その場で消す。
                    if (deletePaths != null)
                    { 
                        deletePaths.Add(localPath);
                    }
                    else
                    {
                        IOUtil.DeleteFile(localPath);
                    }
                }
            }

            foreach (var id in obsoleteIds)
            {
                adapter.RemoveRow(id);
            }
        }

        static Dictionary<int, int> SolveRecursiveDeps(ManifestLoaderQuery db, Dictionary<string, string> depsDic, Dictionary<string, int> revIdDic, Dictionary<int, int> parentIds, Stack<string> stack)
        {
            string name = stack.Peek();
            int depth = stack.Count();

            if (depsDic.TryGetValue(name, out string depsStr))
            {
                Dictionary<int, int> ids = null;

                foreach (var dep in depsStr.Split(';'))
                {
                    ids = ids ?? new Dictionary<int, int>(16);
                    if (!stack.Contains(dep))
                    {
                        if (!revIdDic.TryGetValue(dep, out int id))
                        {
                            id = db.GetId(dep);
                            revIdDic[dep] = id;
                        }

                        ids[id] = depth;

                        stack.Push(dep);
                        SolveRecursiveDeps(db, depsDic, revIdDic, ids, stack);
                        stack.Pop();
                    }
                }

                if (parentIds != null && ids != null)
                {
                    foreach (var kv in ids)
                    {
                        // 親が既に知っているよりも深いdepthが見つかれば置き換える。
                        // これにより、アセットのロード時にdepthの降順でソートすることで子供から安全に解決することが出来る - ykst
                        if (!parentIds.TryGetValue(kv.Key, out int parentDepth) || (parentDepth < kv.Value))
                        {
                            parentIds[kv.Key] = kv.Value;
                        }
                    }
                }

                return ids;
            }

            return null;
        }

        public static void CacheRecursiveDeps(ManifestLoaderQuery db)
        {
            db.ClearDependencyTable();

            int capacity = db.CountEntriesWithDeps();

            Dictionary<string, string> depsDic = new Dictionary<string, string>(capacity);
            Dictionary<string, int> revIdDic = new Dictionary<string, int>();

            // GetTextで取ってきたStringのオブジェクトをそのまま使いたいので、配列に取り置いておく。
            string[] targetNames = new string[capacity];
            int i = 0;

            foreach ((int id, string name, string deps) in db.GetIdsWithDeps())
            {
                revIdDic[name] = id;
                targetNames[i++] = name;
                depsDic[name] = deps;
            }

            Stack<string> stack = new Stack<string>(32);
            foreach (var name in targetNames)
            {
                var id = revIdDic[name];

                stack.Push(name);
                foreach (var idDepth in SolveRecursiveDeps(db, depsDic, revIdDic, null, stack))
                {
                    db.InsertDependency(id, idDepth.Key, idDepth.Value);
                }
                // 自分自身も依存関係に追加
                db.InsertDependency(id, id, 0);
                stack.Pop();
            }

            foreach (int id in db.GetIdsWithoutDeps())
            {  
                db.InsertDependency(id, id, 0);
            }
        }
    }
}