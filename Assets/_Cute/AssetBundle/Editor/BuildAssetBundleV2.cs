#define DUPLICATED_ASSETBUNDLE_BUILD
#define DEBUG_LOG_IN_DETAIL
//#define REFRESH_ALL_DEPENDENCY              // dependencyDictionaryを再利用せず全てリフレッシュする

using UnityEngine;
using UnityEditor;
using System.IO;
using Cute.Core;
using System.Collections.Generic;
using System.Linq;

using Path = System.IO.Path;
using System;
using System.Reflection;
using System.Text.RegularExpressions;
using AssetBundleList = System.Collections.Generic.Dictionary<string, Cute.AssetBundle.AssetBundleName>;
using DependencyDict = UnityEngine.Rendering.SerializedDictionary<string, Cute.AssetBundle.DependencyAssetList>;
using SortedStringDict = System.Collections.Generic.SortedDictionary<string, string>;

namespace Cute.AssetBundle
{
#if !DUPLICATED_ASSETBUNDLE_BUILD
    /// <summary>カテゴリビルド引数用</summary>
    public class AssetBundleBuildCategory
    {
        /// <summary>カテゴリ名</summary>
        public string category;
        /// <summary>ビルドするファイルパスのリスト</summary>
        public string[] buildFiles;
    }

    public class AssetBundleName
    {
        public string assetPath;
        public string assetBundleName;
        public string baseAssetPath;
        public string guid;
        public bool fix;
        public bool auto;
        /// <summary>このアセットに依存してるアセットの数</summary>
        public int dependencyCount;

        public List<string> parentAssetPath = new List<string>();
        public List<string> childAssetPath = new List<string>();
    }

    public class VariantInfo
    {
        public string assetPath;
        public string baseAssetPath;
        public string guid;
    }

    public delegate string CustomFunction(string path, string assetBundleName);
    public delegate bool EncryptionCheckFunction(string assetBundleName);
    public delegate byte[] EncryptionFunction(byte[] src);
    public delegate int GroupFunction(string assetBundleName, out int downloadPriority);
    public delegate bool FilterFunction(string path);
#endif
    public delegate string[] GetAssetBundleTargetArrayFunction();

#if OLD_PROCESS
    /// <summary>
    /// AssetBundleをビルド
    /// README.mdに使い方が書いてあります
    /// </summary>
    [ProjectPrefsAttr(AssetBundleVariantsDirectory, "Variants対象ディレクトリ", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleCustomization, "AssetBundle化ルールカスタマイズ関数", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleEncryption, "AssetBundle暗号化関数", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleEncryptionCheck, "AssetBundle暗号化判定関数", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleGroup, "AssetBundleグループ指定関数", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleFilter, "AssetBundle化対象判定関数", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleOutputPath, "AssetBundle出力先 ", "[AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleManifestOutputPath, "Manifest出力先", "[AssetBundle]")]
#if GALLOP
    [ProjectPrefsAttr(AssetBundlePlaneManifestOutputPath, "PlaneManifest出力先", "[Gallop.AssetBundle]")]
    [ProjectPrefsAttr(AssetBundleGenericManifestOutputPath, "GenericManifest出力先", "[Gallop.AssetBundle]")]
    [ProjectPrefsAttr(RawAssetCategoryName, "ビルド中のRawAssetカテゴリ(Sound/Movie)", "[Gallop.AssetBundle]")]
    [ProjectPrefsAttr(RawAssetBundleOutputPath, "RawAssetBundle出力先", "[Gallop.AssetBundle]")]
    [ProjectPrefsAttr(RawAssetBundleOutputPath, "RawAssetBundle出力先", "[Gallop.AssetBundle]")]
#endif
#endif
    [ProjectPrefsAttr(PrefsKeyGetAssetBundleBuildTargetFunc, "AssetBundleビルド対象取得", "[Gallop.AssetBundle]")]
    public class BuildAssetBundleV2
    {
        public class Perf : IDisposable
        {
            private string name;
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();

            public Perf(string name)
            {
                this.name = name;
                DateTime start = DateTime.Now;
                System.Console.WriteLine("Perf start " + name + ":" + start.ToString());
                sw.Start();
            }

            public void Dispose()
            {
                sw.Stop();
                Debug.Log("Perf end " + name + ":" + sw.Elapsed);
            }
        }

        public const string AssetBundleVariantsDirectory = "AssetBundleVariantsDirectory";
        public const string AssetBundleCustomization = "AssetBundleCustomization";
        public const string AssetBundleEncryption = "AssetBundleEncryption";
        public const string AssetBundleEncryptionCheck = "AssetBundleEncryptionCheck";
        public const string AssetBundleGroup = "AssetBundleGroup";
        public const string AssetBundleFilter = "AssetBundleFilter";
        public const string AssetBundleOutputPath = "AssetBundleOutputPath";
        public const string AssetBundleManifestOutputPath = "AssetBundleManifestOutputPath";
#if GALLOP
        // GallopModify : Gallop固有のマニフェスト管理に関する定義
        public const string AssetBundlePlaneManifestOutputPath = "AssetBundlePlaneManifestOutputPath";          // 平文マニフェスト(通常アセット）の出力パス。 ex) manifestwork/Platform/plane_XXX.manifest
        public const string AssetBundleGenericManifestOutputPath = "AssetBundleGenericManifestOutputPath";      // 平文マニフェスト(Rawアセット）の出力パス。 ex) manifestwork/Generic/plane_XXX.manifest
        public const string RawAssetCategoryName = "RawAssetCategoryName";      // 平文マニフェスト(Rawアセット）の出力パス。 ex) manifestwork/Generic/plane_XXX.manifest
        public const string RawAssetBundleOutputPath = "RawAssetBundleOutputPath";
        public const string PrefsKeyGetAssetBundleBuildTargetFunc = "GetAssetBundleBuildTarget";
#endif

        // ディレクトリ名とAssetBundle名が同じだとエラーになるので拡張子をつける
        private const string assetBundleExtension = ".a";
#if UNITY_EDITOR_WIN
        // for local test
        const string DefaultAssetBundleOutputPath = "C:/nginx-1.8.1/html/AssetBundles";
        const string DefaultManifestOutputPath = "C:/nginx-1.8.1/html/manifests";
#if GALLOP
        // GallopModify: Rawアセット出力用パス追加
        const string DefaultRawAssetBundleOutputPath = DefaultAssetBundleOutputPath;
#endif
#else
        const string DefaultAssetBundleOutputPath = "assetbundles";
        const string DefaultManifestOutputPath = "manifests";
#if GALLOP
        // GallopModify: Rawアセット出力用パス追加
        const string DefaultRawAssetBundleOutputPath = "assetbundles";
#endif
#endif
        const string assetbundleManifestName = "manifest.asset";

#if GALLOP
        // GallopModify : JenkinsBuild環境を全PFで使いまわす都合上、キャッシュをプラットフォームごとに分けて管理したい
        const string assetBundleCacheBaseDirectory = "AssetBundleCache/";
        const string assetBundleCacheDirectoryiOS = "iOS/";
        const string assetBundleCacheDirectoryAndroid = "Android/";
        const string assetBundleCacheDirectoryWindows = "Win64/";
        const string assetBundleCacheDirectoryMac = "Mac/";
        const string assetBundleCacheDirectoryUnknown = "Unknown/";

        // cuteで元々定義されている assetBundleCacheDirectory は Build() 関数内で宣言。 Platformごとに値が変わるため。

        // targetから対応するキャッシュパスを取得
        private static string GetAssetBundleCacheDirectory(BuildTarget target)
        {
            switch (target)
            {
                case BuildTarget.iOS:
                    return assetBundleCacheBaseDirectory + assetBundleCacheDirectoryiOS;
                case BuildTarget.Android:
                    return assetBundleCacheBaseDirectory + assetBundleCacheDirectoryAndroid;
                case BuildTarget.StandaloneWindows64:
                    return assetBundleCacheBaseDirectory + assetBundleCacheDirectoryWindows;
                case BuildTarget.StandaloneOSX:
                    return assetBundleCacheBaseDirectory + assetBundleCacheDirectoryMac;
                default:
                    return assetBundleCacheBaseDirectory + assetBundleCacheDirectoryUnknown;
            }
        }
#else
        const string assetBundleCacheDirectory = "AssetBundleCache/";
#endif

        // シーンが参照してるAsset名が通常Assetの名前と衝突しないようのprefix
        private const string sceneAssetNamePrefix = "s/";

        private const string buildFileKey = "-file";

        private const string fileIdPattern = @"fileIDToRecycleName:\n(\s+[0-9]+:\s.+\n)+";


        // AssetBundleビルド中のエラー出力判定用
        static string error = null;

        // AssetBundle化ルールカスタマイズ関数
        static CustomFunction customFunction;

        // variant元ファイル退避ディレクトリ
        static string tempDirectoryPath = Application.dataPath + "/../TempAssetBundleVariant";

        public static string ALL_BUILD_CATEGORY = "all";

        private static FilterFunction FILTER_FUNCTION_CACHE = null;

        /// <summary>
        /// AssetBundleManager.AssetBundleRootDirectoryで指定されたディレクトリ内のファイルを全てAssetBundle化
        /// カテゴリ名は「all」
        /// </summary>
        [MenuItem("Cute/AssetBundle/Build AssetBundle")]
        public static void Build()
        {
            // ビルド対象ファイル取得（nullの場合は全てビルド)
            var buildFiles = GetBuildFilesFromCommandLineArgs();
            //Build(new AssetBundleBuildCategory[] { new AssetBundleBuildCategory() { category = "all", buildFiles = buildFiles } });
        }

        /// <summary>
        /// 単カテゴリのみをビルドする、Gallopでは基本的にこちらのみ使用する想定
        /// </summary>
        /// <param name="buildFiles">ビルドするファイル</param>
        public static void FullBuild()
        {
            DiffBuildParameter.Enable = false;

            customFunction = GetCustomizeFunction();

            // ファイルリストを差分として扱うか、フルビルドとして扱うか
            BuildTarget target = EditorUserBuildSettings.activeBuildTarget;
            CleanBuild(target);

            using (new Perf("FinalizeAssets"))
            {
                FinalizeAssets(target);
            }
        }

        /// <summary>
        /// 単カテゴリのみをビルドする、Gallopでは基本的にこちらのみ使用する想定
        /// </summary>
        /// <param name="buildFiles">ビルドするファイル</param>
        /// <returns>ビルド対象が存在し、成果物が出力された場合はtrue</returns>
        public static bool DiffBuild()
        {
            DiffBuildParameter.Enable = true;

            customFunction = GetCustomizeFunction();

            // ファイルリストを差分として扱うか、フルビルドとして扱うか
            BuildTarget target = EditorUserBuildSettings.activeBuildTarget;
            DiffBuild(target);

        #if GALLOP
            // AssetBundleCacheのコピーが行われたならキャッシュのアップロード等の処理も行いたい
            var assetBundleCacheCopyEnvParam = System.Environment.GetEnvironmentVariable("ASSET_BUNDLE_CACHE_COPY");
            var isAssetBundleCacheCopy = (assetBundleCacheCopyEnvParam != null && assetBundleCacheCopyEnvParam == "true");
            if (isAssetBundleCacheCopy)
            {
                Gallop.BatchModeCommunicator.AddLog($"アセバンキャッシュのコピーが行われたのでビルド対象の差分の有無に関わらず後処理を行います");
            }
            if (DiffBuildParameter.BuildBundleSet.Count <= 0 && !isAssetBundleCacheCopy)
        #else
            if (DiffBuildParameter.BuildBundleSet.Count <= 0)
        #endif
            {
                // ビルド対象が存在しなかった場合はPlaneManifestの再出力だけ行う
                if (DiffBuildParameter.DeleteBundleList.Count <= 0)
                {
                    // 何も差分がなかった
                    Gallop.BatchModeCommunicator.AddLog("ビルド対象の差分が存在しなかったためビルドをスキップしました。");
                    Gallop.BatchModeCommunicator.ExitWithCode(Gallop.BatchModeCommunicator.ExitCode.NotBuiltAssetBundle);
                    return false;
                }
                
                OutputPlaneManifest(null);
                return true;
            }

            using (new Perf("FinalizeAssets"))
            {
                FinalizeAssets(target);
            }

            return true;
        }


        /// <summary>
        /// 指定カテゴリの一括Finalizeを実行
        /// </summary>
        public static void FinalizeTargetCategories(string[] categories)
        {
            BuildTarget target = EditorUserBuildSettings.activeBuildTarget;

            /*
            using (new Perf("Create Bundle"))
            {
                CreateAssetBundles(target);
            }
            */

            using (new Perf("FinalizeAssets"))
            {
                FinalizeAssets(target);
            }
        }

        /// <summary>
        /// 完全な再ビルドを実行する
        /// </summary>
        /// <param name="buildFiles"></param>
        /// <param name="target"></param>
        private static void CleanBuild(BuildTarget target)
        {
            // Menuからビルドした場合、前回失敗した履歴が残るので初期化し直す
            error = null;

            Application.logMessageReceived += LogMessageReceived;

            customFunction = GetCustomizeFunction();

            // AssetBundle化対象ファイルリストを生成
            string[] buildFiles = GetFuncGetAssetBundleBuildTarget()();
            DependencyDict dependencyDict = new DependencyDict();
            
            // buildFilesがnullの場合は全ファイルをビルド対象とする
            string rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
            if( Directory.Exists(rootDirectory) )
            {
                buildFiles ??= Directory.GetFiles(rootDirectory, "*.*", SearchOption.AllDirectories);
            }
            
        #if GALLOP
            // バージョンタグチェック
            buildFiles = BuildAssetBundle.GetFilteredBuildFiles(buildFiles);
        #endif
            
            var allAssetList = GetAssetBundleList(buildFiles, ref dependencyDict);
            var serializableBuilds = CreateSerializableBuildList(allAssetList);

            // 差分ビルドで用いる中間情報ファイルを出力しておく
            ScriptableBundleBuildList scriptableBundleBuildList = ScriptableObject.CreateInstance(typeof(ScriptableBundleBuildList)) as ScriptableBundleBuildList;
            scriptableBundleBuildList.buildList = serializableBuilds;
            scriptableBundleBuildList.dependencyList = dependencyDict;
            scriptableBundleBuildList.Save();

            Application.logMessageReceived -= LogMessageReceived;

            // AssetBundle生成
            using (new Perf("Create Bundle"))
            {
                List<AssetBundleBuild> builds = new List<AssetBundleBuild>();
                builds.AddRange(scriptableBundleBuildList.GetUnityAssetBundleBuildV2());
                CreateAssetBundles(builds, target);
            }
        }

        /// <summary>
        /// Git差分ファイルを読み込み行ごとにリストに
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        private static List<string> LoadGitDiffFile(string path)
        {
            List<string> readLines = new List<string>(128);

            StreamReader reader = new StreamReader(path);
            string nextline = reader.ReadLine();
            while (nextline != null)
            {
                readLines.Add(nextline);
                nextline = reader.ReadLine();
            }

            return readLines;
        }


        /// <summary>
        /// Git差分ファイルをregexに変換したリストに
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        private static List<Regex> LoadForceBuildFiles(string path)
        {
            List<Regex> regexList = new List<Regex>(128);

            StreamReader reader = new StreamReader(path);
            string nextline = reader.ReadLine();
            while (nextline != null)
            {
                string processingLine = nextline.Trim();
                nextline = reader.ReadLine();

                if (string.IsNullOrEmpty(processingLine)) continue;

                // *指定がある場合は正規表現用に .+ に変換
                string regexPattern = processingLine.Replace("*", ".+");
                regexList.Add(new Regex(regexPattern, RegexOptions.IgnoreCase));
            }

            return regexList;
        }

        /// <summary>
        /// 差分ビルド
        /// </summary>
        /// <param name="buildFiles"></param>
        /// <param name="target"></param>
        private static void DiffBuild(BuildTarget target)
        {
            // プラットフォームごとにアセバンキャッシュパスを変更する
            string assetBundleCacheDirectory = GetAssetBundleCacheDirectory(target);

            // AssetBundleビルドファイル一覧
            string[] buildFiles = GetFuncGetAssetBundleBuildTarget()();     // Assets/~~/XXX.xxx の形式
#if DEBUG_LOG_IN_DETAIL
            Array.ForEach(buildFiles, x => Debug.Log($"[buildFiles] {x}"));
#endif
            
            
            // =======
            // 1. 前回のビルドファイルをロード
            // =======

            var perf = new Perf("Load BundleBuildList / CreateDict");

            var scriptableBundleList = ScriptableBundleBuildList.Load();
            var loadedBuildList = scriptableBundleList.buildList;
            var dependencyList = scriptableBundleList.dependencyList;

            // 必要なDictの構築
            // AssetBundleName : AssetInfo
            // AssetsPath : AssetBundleName (AssetsPathは Assets/_GallopResources/～ 形式)
            Dictionary<string, SerializableAssetBundleBuild> bundleDictByBundleName = new Dictionary<string, SerializableAssetBundleBuild>(4096);
            Dictionary<string, string> bundleNameDictByAssetPath = new Dictionary<string, string>(4096);
            for (int i = loadedBuildList.Count - 1; i >= 0; --i)
            {
                Debug.Log($"[Log] Add BundleName : {loadedBuildList[i].assetBundleName}");
                bundleDictByBundleName.Add(loadedBuildList[i].assetBundleName, loadedBuildList[i]);

                foreach (var assetPath in loadedBuildList[i].assetNames)
                {
                    Debug.Log($"[Log] Add AssetPath : {assetPath} => {loadedBuildList[i].assetBundleName}");
                    bundleNameDictByAssetPath.Add(assetPath, loadedBuildList[i].assetBundleName);
                }
                foreach (var assetPath in loadedBuildList[i].autoReferencedAssetNames)
                {
                    Debug.Log($"[Log] Add AutoReferencedAssetPath : {assetPath} => {loadedBuildList[i].assetBundleName}");
                    bundleNameDictByAssetPath.Add(assetPath, loadedBuildList[i].assetBundleName);
                }
            }

            perf.Dispose();

            // =======
            // 2. Gitの差分情報を取得
            // =======

            // client
            var clientGitDiffPath = System.Environment.GetEnvironmentVariable(BuildParameterBridge.ENV_KEY_CLIENT_GIT_DIFF_PATH);
            if (clientGitDiffPath == null) clientGitDiffPath = BuildParameterBridge.CLIENT_GIT_DIFF_PATH_DEBUG;
            List<string> clientDiffList = LoadGitDiffFile(clientGitDiffPath);

            // resources
            var resourcesGitDiffPath = System.Environment.GetEnvironmentVariable(BuildParameterBridge.ENV_KEY_RESOURCES_GIT_DIFF_PATH);
            if (resourcesGitDiffPath == null) resourcesGitDiffPath = BuildParameterBridge.RESOURCES_GIT_DIFF_PATH_DEBUG;
            List<string> resourcesDiffList = LoadGitDiffFile(resourcesGitDiffPath);

            // 強制ビルドファイル指定
            var forceBuildFilesPath = System.Environment.GetEnvironmentVariable(BuildParameterBridge.ENV_KEY_FORCE_BUILD_FILES_PATH);
            List<Regex> forceBuildTargetRegexList = LoadForceBuildFiles(forceBuildFilesPath);

            // =======
            // 3. GitのA/D/Mに応じたビルド対象ファイルを決定する
            // =======

            perf = new Perf("Load Git Diff List");

            // TODO. ビルドリストの再構築も必要最低限にできればより速度改善できる
            #region optimize_plan
#if false
            // 3. Gitの A/D/M に応じてビルド対象ファイルを決定する
            //    3の詳細は後述
            
            // =======
            // 3. GitのA/D/Mに応じたビルド対象ファイルを決定する
            // =======
            
            perf = new Perf("Decide Build Targets");
            
            //// 直接ビルド対象になるAssetBundleの名前
            //HashSet<string> buildTargetBundleName = new HashSet<string>();
            
            // Bundle構成の再更新をかける可能性があるAssetBundleの一覧
            HashSet<string> regenerateCandidateBundleList = new HashSet<string>();
            
            // Cute.GetAssetBundleList() に投げ込むアセット一覧
            HashSet<string> buildTargetAssetList = new HashSet<string>();
            
            // 削除対象アセット一覧
            HashSet<string> deleteBundleNameList = new HashSet<string>();
            
            foreach (var line in resourcesDiffList)
            {
                var splittedLine = line.Split();
                var type = splittedLine[0];
                var filePath = splittedLine[1];

                var assetsFilePath = $"Assets/_GallopResources/{filePath}";
                var bundleName = bundleNameDictByAssetPath[assetsFilePath];
                //var bundleInfo = bundleDictByBundleName[bundleName];
                
                switch (type)
                {
                    case "M":
                        // 変更差分
                        regenerateCandidateBundleList.Add(bundleName);
                        break;
                    
                    case "A":
                        // 追加差分
                        buildTargetAssetList.Add($"Assets/_GallopResources/{filePath}");

                        break;
                    
                    case "D":
                        // 削除差分
                        // 単独アセットなら削除すればいいが、参照されているアセットなら参照アセットを再構築の対象にする
                        // （本来なら参照元のアセット自体が Modify 差分があることを期待したいのでエラーにするのもあり？)
                        if (bundleInfo.)                        
                        break;
                }
            }            
            perf.Dispose();
#endif
            #endregion
            bool isNeedRegenerateBundleList = false;
            DiffBuildParameter.BuildBundleSet.Clear();
            DiffBuildParameter.DeleteBundleList.Clear();
            DiffBuildParameter.BuildSkipAssetPathSet.Clear();

            // Client
            foreach (var line in clientDiffList)
            {
                var splittedLine = line.Split();
                var type = splittedLine[0];
                var filePath = splittedLine[1];
                var extension = Path.GetExtension(filePath);

                // clientからビルドされるものは一部のみ対象
                if (!(filePath.Contains(".shader") || filePath.Contains(".hlsl")))
                {
                    continue;
                }

                // 拡張子がmetaだった場合は本体ファイルに変更が入っているとして扱う
                if (extension.Contains(".meta"))
                {
                    filePath = filePath.Replace(".meta", "");
                }

                switch (type)
                {
                    case "M":
                        // Shaderに更新が入ってるのでShaderをビルド対象に。
                        DiffBuildParameter.BuildBundleSet.Add("shader.a");
                        break;

                    case "A":
                    case "D":
                    default:
                        // 追加/削除、ビルドリストの更新を必須とする
                        // TODO : Shader内アセバンのリスト更新を走らせて、差分がある場合のみリスト作り直すとかでもいけそう
                        isNeedRegenerateBundleList = true;
                        // hlslのみ追加・削除があった場合はshaderの内部構成が変わらないのでここで明示的に追加する
                        // ... ということを考えたが、hlslのみ追加・削除した場合はshaderにも変更が入ってないとおかしいはず
                        // DiffBuildParameter.BuildBundleSet.Add("shader.a");
                        break;
                }
            }

            // Resources
            foreach (var line in resourcesDiffList)
            {
                Debug.Log($"[Log] ResourcesDiffList : {line}");
                var splittedLine = line.Split();
                var type = splittedLine[0];
                // ここで取れるパスはgitのパスなので Bundle/Resources/～ の形式
                var filePath = splittedLine[1];

                // .meta更新時の対応、本体ファイルに変更が入っているとして扱う
                var extension = Path.GetExtension(filePath);
                if (extension.Contains(".meta"))
                {
                    filePath = filePath.Replace(".meta", "");
                }

                // Assets/～ のパスを扱う場合用
                var assetsFilePath = $"Assets/_GallopResources/{filePath}";

                switch (type)
                {
                    case "M":
                        // 一部のパス名はgitとパス名が一致しないので変換する必要がある  
                        if (filePath.StartsWith("Bundle/Debug/"))
                        {
                            // Bundle/Debug～のファイルはBundle/～に移動しているのでチェックするパスも変換してから
                            assetsFilePath = assetsFilePath.Replace("Bundle/Debug/Resources/", "Bundle/Resources/");
                        }

                        // ファイルが前回ビルドしているかチェックする
                        // SourceResourcesの場合参照がなかったらビルド対象にならないので、変更差分だがビルドリストに含まれてない可能性がある
                        // また、Product環境ではDebug/～もビルド対象にならないので同様のケースが起こり得る
                        if (bundleNameDictByAssetPath.TryGetValue(assetsFilePath, out string bundleName))
                        {
                            var bundleInfo = bundleDictByBundleName[bundleName];
                            if (bundleInfo.childAssetList.Count <= 0)
                            {
                                // 依存関係がないアセットならBundle構造の更新は不要で、直接このバンドルをビルドするだけでいい
                                DiffBuildParameter.BuildBundleSet.Add(bundleName);
                            }
                            else
                            {
                                // 依存関係があるアセットの場合はBundle構造の再構築が必要
                                // TODO : 依存関係が変わっていないならここの処理はスキップできるはず。
                                //        追加したdependenciesリストを使えないか試す。
                                isNeedRegenerateBundleList = true;

                                DiffBuildParameter.BuildBundleSet.Add(bundleName);
                            }
                        }

                        // dependencyListの更新を行う
                        // 未追加の場合は GetAssetBundleList() での追加に任せる
                        if (dependencyList.ContainsKey(assetsFilePath))
                        {
                            var dependencies = GetDependencyAssets(assetsFilePath);
                            dependencyList[assetsFilePath].AssetList.Clear();                   // 前回の依存情報が残らないようクリアする
                            dependencyList[assetsFilePath].AssetList.AddRange(dependencies);
                        }
                        break;

                    case "A":
                        // Bundle/Resourcesに追加があった場合のみビルド必須にする
                        if (filePath.StartsWith("Bundle/Resources")
                            || filePath.StartsWith("Bundle/Debug/"))
                        {
                            isNeedRegenerateBundleList = true;
                        }
                        break;

                    case "D":
                        // 追加/削除、ビルドリストの更新を必須とする
                        // ビルドリストのいずこかに含まれているならリストの更新を必須にする
                        if (filePath.StartsWith("Bundle/Debug"))
                        {
                            assetsFilePath = assetsFilePath.Replace("/Debug/", "/");
                        }

                        if (!bundleNameDictByAssetPath.TryGetValue(assetsFilePath, out var deleteBundleName))
                        {
                            continue;
                        }

                        // 過去のリストに存在する = 削除がビルド結果に影響するので再構築
                        isNeedRegenerateBundleList = true;

                        // 依存関係のリストからファイルを取り除き、また取り除いたファイルの依存関係も更新しなおしておく
                        dependencyList.Remove(assetsFilePath);
                        RecollectDependencyList(deleteBundleName, bundleDictByBundleName, ref dependencyList);
                        break;
                }
            }
            perf.Dispose();

            // =======
            // ForceBuildに指定されたファイルをビルド対象とする
            // =======

            perf = new Perf("Calc ForceBuild Files");

            int regexCount = forceBuildTargetRegexList.Count;
            if (regexCount > 0)
            {
                // ファイル × 正規表現の数分ループしてマッチしたファイルをビルド対象としたい
                // この時チェック対象とするファイル名は前回ビルド時にビルド対象となっていたアセットの一覧となる
                // そこに含まれていない場合は、 Add差分として出ていることを期待する
                var prevBuildAssetPathList = bundleNameDictByAssetPath.Keys;
                foreach (var assetsFilePath in prevBuildAssetPathList)
                {
                    for (int i = 0; i < regexCount; ++i)
                    {
                        if (!forceBuildTargetRegexList[i].IsMatch(assetsFilePath)) continue;

#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"match force build : {assetsFilePath}");
#endif

                        // 指定された条件にマッチ、gitの "M" ファイルと同じ処理を行う
                        if (bundleNameDictByAssetPath.TryGetValue(assetsFilePath, out string bundleName))
                        {
                            var bundleInfo = bundleDictByBundleName[bundleName];
                            if (bundleInfo.childAssetList.Count <= 0)
                            {
                                // 依存関係がないアセットならBundle構造の更新は不要で、直接このバンドルをビルドするだけでいい
                                DiffBuildParameter.BuildBundleSet.Add(bundleName);
                            }
                            else
                            {
                                // 依存関係があるアセットの場合はBundle構造の再構築が必要
                                isNeedRegenerateBundleList = true;
                                DiffBuildParameter.BuildBundleSet.Add(bundleName);
                            }

                            // dependencyListの更新を行う
                            // 未追加の場合は GetAssetBundleList() での追加に任せる
                            if (dependencyList.ContainsKey(assetsFilePath))
                            {
                                var dependencies = GetDependencyAssets(assetsFilePath);
                                dependencyList[assetsFilePath].AssetList.Clear();                   // 前回の依存情報が残らないようクリアする
                                dependencyList[assetsFilePath].AssetList.AddRange(dependencies);
                            }
                        }
                        break;
                    }
                }
            }
            
        #if GALLOP
            // =======
            // バージョンタグチェック
            // =======
            Debug.Log(" --------- Start Version Tag Check ----------");
            var resourcesVersion = int.Parse(System.Environment.GetEnvironmentVariable(BuildParameterBridge.ENV_KEY_RESOURCES_VERSION));
#if DEBUG_LOG_IN_DETAIL
            Debug.Log($"[Log] Resources Version: {resourcesVersion}");
#endif
            // buildFilesがnullの場合は全ファイルをビルド対象とする
            string rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
            if( Directory.Exists(rootDirectory) )
            {
                // 差分ビルドでここを踏むことは想定していないので、ログに残す
                Debug.Log($"[Warning] This step is not expected in diff build!");
                buildFiles ??= Directory.GetFiles(rootDirectory, "*.*", SearchOption.AllDirectories);
            }
            
            foreach (var assetPath in buildFiles)
            {
                // ドットで始まるファイルは無視する
                if (Path.GetFileName(assetPath).StartsWith("."))
                {
                    continue;
                }
                // metaファイルとunityファイルは無視する
                if (Path.GetExtension(assetPath) == ".meta" || Path.GetExtension(assetPath) == ".unity")
                {
                    continue;
                }
                
                // 前回ビルド成果物に含まれていたかチェックする
                var fullPath = Directory.GetCurrentDirectory() + "/" + assetPath;   // /Users/<USER>/~~/Assets/~~/.png など
                bool isExistInPrevBuild = bundleNameDictByAssetPath.TryGetValue(assetPath, out var bundleName); // 前回ビルド成果物にない場合はbundleNameはnull
                if (isExistInPrevBuild)
                {
                    // 前回ビルド成果物に含まれていた場合、
                    // １．今回のビルド成果物に含まない場合はアセバン再構成が必要になる
                    // ２・今回のビルド成果物にも含まれる場合は特に何もしない
                    if (!BuildAssetBundle.FilterVersionTag(fullPath, resourcesVersion))
                    {
                        // 前回ビルドしたアセバンを除外したいので、アセバンの再構成を必要とする
                        isNeedRegenerateBundleList = true;

                        // ビルド対象から除き、また除外対象としても積んでおく
                        DiffBuildParameter.BuildBundleSet.Remove(bundleName);
                        DiffBuildParameter.BuildSkipAssetPathSet.Add(assetPath);
                        
                        // 依存関係のリストからファイルを取り除き、また取り除いたファイルの依存関係も更新しなおしておく
                        dependencyList.Remove(assetPath);
                        RecollectDependencyList(bundleName, bundleDictByBundleName, ref dependencyList);
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"[Log] This asset is included in prev build, but will not be included in this time: {bundleName}");
#endif
                    }
                    else
                    {
                        // 何もしない
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"[Log] This asset is included in prev build, and also will be included in this time: {bundleName}");
#endif
                    }
                }
                else
                {
                    // 前回ビルド成果物に含まれていなかった場合、
                    // １．今回のビルド成果物にも含まない場合はアセバン再構成が不要
                    // ２・今回のビルド成果物に含まれる場合はアセバン再構成が必要になる
                    if (!BuildAssetBundle.FilterVersionTag(fullPath, resourcesVersion))
                    {
                        // memo: アセバンの再構成は不要
                        // 　　　　また、前回成果物に含まれていないので、BuildBundleSetやDependencyListにも積まれていないはず
                        
                        // 除外対象としては一応積んでおく
                        DiffBuildParameter.BuildSkipAssetPathSet.Add(assetPath);
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"[Log] This asset is not included in prev build, and also will be not included in this time: {bundleName}");
#endif
                    }
                    else
                    {
                        // 前回ビルドされていないアセバンをビルドしたいので、アセバンの再構成を必要とする
                        // BuildBundleSetやDependencyListへの追加はアセバン再構成時に行われるはず
                        isNeedRegenerateBundleList = true;
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"[Log] This asset is not included in prev build, but will be included in this time: {bundleName}");
#endif
                    }
                }
            }
#if DEBUG_LOG_IN_DETAIL
            Debug.Log(" --------- End Version Tag Check ----------");
#endif
        #endif


        #if GALLOP
            // AssetBundleCacheのコピーが行われたなら強制的にビルドし直してアセバンキャッシュをアップロードする
            var assetBundleCacheCopyEnvParam = System.Environment.GetEnvironmentVariable("ASSET_BUNDLE_CACHE_COPY");
            var isAssetBundleCacheCopy = (assetBundleCacheCopyEnvParam != null && assetBundleCacheCopyEnvParam == "true");
            if (isAssetBundleCacheCopy)
            {
                isNeedRegenerateBundleList = true;
            }
        #endif
            perf.Dispose();

#if DEBUG_LOG_IN_DETAIL
            Debug.Log("===========================================================");
            Debug.Log($" isNeedRegenerateBundleList = {isNeedRegenerateBundleList} ");
            Debug.Log(" --------- BuildBundleSet List ----------");
            foreach (var bundleName in DiffBuildParameter.BuildBundleSet)
            {
                Debug.Log($" {bundleName}");
            }
            Debug.Log("===========================================================");
#endif

            // AssetBundleListの再構築が必要になった？
            if (isNeedRegenerateBundleList)
            {
                // AssetBundle化対象ファイルリストを生成
                
#if REFRESH_ALL_DEPENDENCY
                dependencyList = new DependencyDict();
#endif
                // バージョンタグによってビルド対象から除外されているファイルを除外したリストを生成し、以後の処理に流す
                var filteredBuildFiles = buildFiles.Where(x => !DiffBuildParameter.BuildSkipAssetPathSet.Contains(x)).ToArray();
#if DEBUG_LOG_IN_DETAIL
                // ログ用
                var removedBuildFiles = buildFiles.Where(x => DiffBuildParameter.BuildSkipAssetPathSet.Contains(x)).ToArray();
                foreach (var file in removedBuildFiles)
                {
                    Debug.Log($"[Log] Skip GetAssetBundleList by version tag: {file} ");
                }
#endif
                var newAllAssetList = GetAssetBundleList(filteredBuildFiles, ref dependencyList);
                var newSerializableBuilds = CreateSerializableBuildList(newAllAssetList);

                // 差分ビルドで用いる中間情報ファイルを出力しておく
                ScriptableBundleBuildList newScriptableBundleBuildList = ScriptableObject.CreateInstance(typeof(ScriptableBundleBuildList)) as ScriptableBundleBuildList;
                newScriptableBundleBuildList.buildList = newSerializableBuilds;
                newScriptableBundleBuildList.dependencyList = dependencyList;
                newScriptableBundleBuildList.Save();
                
#if DEBUG_LOG_IN_DETAIL
                Debug.Log($"[LOG] newScriptableBundleBuildList.buildList");
                foreach (var b in newScriptableBundleBuildList.buildList)
                {
                
                    Debug.Log($"[LOG] build list file: {b}");
                }
#endif

                // 前回のビルド結果と差分が出ているものをすべてビルド対象にする
                using (new Perf("Check Changed Bundle"))
                {
                    // TODO : 重そう
                    // 出力されたビルド一覧をぶん回して、内部情報等に差分があるアセバンがあったらリストに残す
                    var newBuildList = newScriptableBundleBuildList.buildList;
                    Dictionary<string, SerializableAssetBundleBuild> newBundleDictByBundleName =
                        new Dictionary<string, SerializableAssetBundleBuild>();
                    foreach (var newBuildInfo in newBuildList)
                    {
                        newBundleDictByBundleName.Add(newBuildInfo.assetBundleName, newBuildInfo);
#if DEBUG_LOG_IN_DETAIL
                        newBuildInfo.assetNames.ForEach(x=>Debug.Log($"[NewBundleDictByBundleName] {newBuildInfo.assetBundleName} => list: {x}"));
#endif
                        if (bundleDictByBundleName.TryGetValue(newBuildInfo.assetBundleName, out var oldBundleInfo))
                        {
                            if (!oldBundleInfo.IsSame(newBuildInfo))
                            {
#if DEBUG_LOG_IN_DETAIL
                                Debug.Log($" change in files = {newBuildInfo.assetBundleName} ");
#endif
                                // [変更] 内容が一致していないのでビルド対象
                                DiffBuildParameter.BuildBundleSet.Add(newBuildInfo.assetBundleName);
                            }
#if DEBUG_LOG_IN_DETAIL
                            else
                            {
                                Debug.Log($" NO change in files = {newBuildInfo.assetBundleName} ");
                            }
#endif
                        }
                        else
                        {
#if DEBUG_LOG_IN_DETAIL
                            Debug.Log($"  new bundle = {newBuildInfo.assetBundleName} ");
#endif
                            // [追加] 新規追加になったアセバンなのでビルド対象
                            DiffBuildParameter.BuildBundleSet.Add(newBuildInfo.assetBundleName);
                        }
                    }

                    // [削除] oldをベースにExceptで残ったものなので削除差分
                    DiffBuildParameter.DeleteBundleList = bundleDictByBundleName.Keys.Except(newBundleDictByBundleName.Keys).ToList();

#if DEBUG_LOG_IN_DETAIL
                    Debug.Log(" --------- Deleted Bundle List ----------");
                    foreach (var bundleName in DiffBuildParameter.DeleteBundleList)
                    {
                        Debug.Log($" {bundleName}");
                    }
#endif
                    
#if DEBUG_LOG_IN_DETAIL
                    Debug.Log(" --------- Referencing Deleted Bundle ----------");
#endif
                    // 差分元バージョンで今回削除されたバンドルを含んでいたものに関してもリビルドの対象にする
                    // 対象のファイルもリネーム等されていた場合はここでキャッチアップできないが、その場合そもそも前提処理でリストに積まれているはず
                    var tmpDeleteBundleList = DiffBuildParameter.DeleteBundleList;
                    foreach (var bundleName in tmpDeleteBundleList)
                    {
                        Debug.Log($"check bundle name : {bundleName}");
                        
                        // ないと思うが一応見つからない場合をケア
                        if (!bundleDictByBundleName.ContainsKey(bundleName))
                        {
                            //Debug.Log($"not contains old dict.");
                            continue;
                        }
                        
                        // 削除対象バンドルを参照していたバンドルを全てビルド対象に追加する（新規ビルドリスト内に同名アセバンがある場合のみ）
                        var deleteBundleInfo = bundleDictByBundleName[bundleName];
                        foreach (var parentBundleName in deleteBundleInfo.parentAssetList)
                        {
                            if (!newBundleDictByBundleName.ContainsKey(parentBundleName))
                            {
                                //Debug.Log($"not containes new dict");
                                continue;
                            }

#if DEBUG_LOG_IN_DETAIL
                            Debug.Log($"      rebuild : {parentBundleName}");
#endif
                            DiffBuildParameter.BuildBundleSet.Add(parentBundleName);
                        }
                    }
                    
#if DEBUG_LOG_IN_DETAIL
                    Debug.Log(" --------- Referencing Build Target Bundle ----------");
#endif
                    // ビルド対象になったバンドルの依存先バンドルもビルド対象に加える
                    var tmpBuildBundleList = DiffBuildParameter.BuildBundleSet.ToList();
                    foreach (var bundleName in tmpBuildBundleList)
                    {
                        // RegenerateBundleList の結果、bundleNameのバンドルが消えている可能性がある
                        if (!newBundleDictByBundleName.ContainsKey(bundleName)) continue;
                        
                        // 依存先バンドル情報収集
                        Debug.Log($"[Log] Add by Recollection : {bundleName}");
                        CollectChildAndParentBundles(ref DiffBuildParameter.BuildBundleSet, bundleName, newBundleDictByBundleName);
                    }

                    // 古いDictを破棄し、新しいリストで更新
                    bundleDictByBundleName = newBundleDictByBundleName;
                }
            }
            else
            {
                // 差分ビルドの場合、dependencyListの更新だけは行いたいのでSaveを行う
                ScriptableBundleBuildList newScriptableBundleBuildList = ScriptableObject.CreateInstance(typeof(ScriptableBundleBuildList)) as ScriptableBundleBuildList;
                newScriptableBundleBuildList.buildList = loadedBuildList;
                newScriptableBundleBuildList.dependencyList = dependencyList;
                newScriptableBundleBuildList.Save();
            }

            // 再利用しないキャッシュをすべて削除
            var buildPath = assetBundleCacheDirectory + "All/build/";
            DeleteUnusedCacheFilesForDiffBuild(buildPath);

            // ビルド対象が存在しなかった
        #if GALLOP
            if (DiffBuildParameter.BuildBundleSet.Count <= 0 && !isNeedRegenerateBundleList)
        #else
            if (DiffBuildParameter.BuildBundleSet.Count <= 0)
        #endif
            {
                return;
            }

            // バンドルビルド
            using (new Perf("Create Asset Bundle"))
            {
                List<AssetBundleBuild> builds = new List<AssetBundleBuild>();
                foreach (var bundleName in DiffBuildParameter.BuildBundleSet)
                {
                    if (bundleDictByBundleName.TryGetValue(bundleName, out SerializableAssetBundleBuild buildInfo))
                    {
                        var unityAssetBundleBuild = buildInfo.GetUnityAssetBundleBuild();
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"[GetUnityAssetBundle]  {bundleName} => {String.Join(", ", unityAssetBundleBuild.assetNames)}");
#endif
                        builds.Add(unityAssetBundleBuild);
                    }
                    else
                    {
                        // 結果的にバンドルが消えているケースがあるので、Dictにない場合はデバッグログだけ出してない場合はビルド対象にしない
                        Gallop.BatchModeCommunicator.AddLog($"指定のバンドル名 [{bundleName}] が見つかりませんでした。構成変更で消えた可能性があります。");
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"指定のバンドル名 [{bundleName}] が見つかりませんでした。構成変更で消えた可能性があります。");
#endif
                    }
                }

                // 通常版ビルド
                BuildAllWithVariant(assetBundleCacheDirectory + "All", builds, new List<AssetBundleBuild>(), null, target);
            }
        }

        /// <summary>
        /// 依存関係にある親と子のアセットもビルド対象に含める
        /// </summary>
        /// <param name="retBundleNameSet"></param>
        /// <param name="baseBundleName"></param>
        /// <param name="bundleDictByBundleName"></param>
        private static void CollectChildAndParentBundles(ref HashSet<string> retBundleNameSet,
            string baseBundleName,
            Dictionary<string, SerializableAssetBundleBuild> bundleDictByBundleName)
        {
            var childBundleList = bundleDictByBundleName[baseBundleName].childAssetList;
            foreach (var childBundleName in childBundleList)
            {
                if (retBundleNameSet.Add(childBundleName))      // 循環参照は塞いでおく
                {
                    CollectChildAndParentBundles(ref retBundleNameSet, childBundleName, bundleDictByBundleName);
                }
            }

            var parentBundleList = bundleDictByBundleName[baseBundleName].parentAssetList;
            foreach (var parentBundleName in parentBundleList)
            {
                if (retBundleNameSet.Add(parentBundleName))     // 循環参照は塞いでおく
                {
                    CollectChildAndParentBundles(ref retBundleNameSet, parentBundleName, bundleDictByBundleName);
                }
            }
        }

        /// <summary>
        /// 指定アセットに参照を持っていたファイル」の依存関係を更新し直す
        /// </summary>
        /// <param name="bundleDictByBundleName"></param>
        /// <param name="bundleName"></param>
        /// <param name="dependencyList"></param>
        private static void RecollectDependencyList( string bundleName,
            Dictionary<string, SerializableAssetBundleBuild> bundleDictByBundleName,
            ref DependencyDict dependencyList)
        {
            // 「このファイルに参照を持っていたファイル」の依存関係も更新しなおしておく必要がある
            // こうしないと本来削除されて存在しなくなったファイルがなぜかアセットバンドルとしてビルドされるという現象が発生する
            if (!bundleDictByBundleName.TryGetValue(bundleName, out var deleteBundleInfo)) return;
            foreach (var parentBundleName in deleteBundleInfo.parentAssetList)
            {
                if (!bundleDictByBundleName.TryGetValue(parentBundleName, out var parentBundleInfo)) continue;
                foreach (var parentAssetName in parentBundleInfo.assetNames)
                {
                    if (!dependencyList.ContainsKey(parentAssetName))
                    {
                        continue;
                    }
#if DEBUG_LOG_IN_DETAIL
                    Debug.Log($"[Log] DependencyList remove: {bundleName}");
#endif
                    var dependencies = GetDependencyAssets(parentAssetName);
                    dependencyList[parentAssetName].AssetList.Clear();
                    dependencyList[parentAssetName].AssetList.AddRange(dependencies);
                }
            }
        }

        /// <summary>
        /// ビルド
        /// </summary>
        /// <param name="buildFiles"></param>
        /// <param name="target"></param>
        private static void Build(string[] buildFiles, BuildTarget target)
        {
            var scripableBundleList = ScriptableBundleBuildList.Load();

            /*
            DateTime start = DateTime.Now;

            // Menuからビルドした場合、前回失敗した履歴が残るので初期化し直す
            error = null;

            Application.logMessageReceived += LogMessageReceived;

            customFunction = GetCustomizeFunction();

            // AssetBundle化対象ファイルリストを生成
            var allAssetList = GetAssetBundleList(buildFiles);
            var serializableBuilds = CreateSerializableBuildList(allAssetList);
            
            ScriptableBundleBuildList scriptableBundleBuildList =  new ScriptableBundleBuildList();
            scriptableBundleBuildList.buildList = serializableBuilds;
            scriptableBundleBuildList.Save();

#if GALLOP
            // GallopModify : GallopではSceneビルド, Variantを利用していない
#else
            // AssetBundle化対象シーンリストを生成
            var allSceneList = GetSceneList(buildFiles);
            var sceneBuilds = CreateBuildList(allSceneList);

            // variantリスト生成
            var variants = CreateVariantInfoList(allAssetList, allSceneList);
#endif
            
#if GALLOP
            // GallopModify : プラットフォームごとにアセバンキャッシュパスを変更する
            string assetBundleCacheDirectory = GetAssetBundleCacheDirectory(target);
#endif
            Application.logMessageReceived -= LogMessageReceived;

            DateTime end = DateTime.Now;
            Debug.Log("Build AssetBundle:" + new DateTime(0).Add(end - start).ToString("HH時間mm分ss秒"));
        */
        }

#if OLD_PROCESS
        /// <summary>
        /// カテゴリ別にビルドする
        /// この関数ではキャッシュまでしか生成しない
        /// 最終出力はFinalizeAssetsで行う
        /// </summary>
        /// <param name="category">カテゴリ名</param>
        /// <param name="buildFiles">ビルド対象ファイルリスト</param>
        /// <param name="target">ビルドするプラットフォーム</param>
        private static void BuildCategory(string[] buildFiles, BuildTarget target)
        {
            DateTime start = DateTime.Now;

            // Menuからビルドした場合、前回失敗した履歴が残るので初期化し直す
            error = null;

            Application.logMessageReceived += LogMessageReceived;

            customFunction = GetCustomizeFunction();

            // AssetBundle化対象ファイルリストを生成
            (var allAssetList, var dependencyList) = GetAssetBundleList(buildFiles);
            var serializableBuilds = CreateSerializableBuildList(allAssetList);
            
            ScriptableBundleBuildList scriptableBundleBuildList =  new ScriptableBundleBuildList();
            scriptableBundleBuildList.buildList = serializableBuilds;
            scriptableBundleBuildList.Save();

#if GALLOP
            // GallopModify : GallopではSceneビルド, Variantを利用していない
#else
            // AssetBundle化対象シーンリストを生成
            var allSceneList = GetSceneList(buildFiles);
            var sceneBuilds = CreateBuildList(allSceneList);

            // variantリスト生成
            var variants = CreateVariantInfoList(allAssetList, allSceneList);
#endif
            
#if GALLOP
            // GallopModify : プラットフォームごとにアセバンキャッシュパスを変更する
            string assetBundleCacheDirectory = GetAssetBundleCacheDirectory(target);
#endif
            Application.logMessageReceived -= LogMessageReceived;

            DateTime end = DateTime.Now;
            Debug.Log("Build AssetBundle:" + new DateTime(0).Add(end - start).ToString("HH時間mm分ss秒"));
        }
#endif

        private static void CreateAssetBundles(List<AssetBundleBuild> buildList, BuildTarget target)
        {
#if GALLOP
            // GallopModify : プラットフォームごとにアセバンキャッシュパスを変更する
            string assetBundleCacheDirectory = GetAssetBundleCacheDirectory(target);
#endif

            // 使わなくなった前回のビルドファイルが邪魔でビルドに失敗することがあるので削除
            DeleteUnusedFiles(assetBundleCacheDirectory + "All/build/", buildList);

            // 通常版ビルド
            BuildAllWithVariant(assetBundleCacheDirectory + "All", buildList, new List<AssetBundleBuild>(), null, target);
        }

        /// <summary>
        /// カテゴリビルドされたファイルを移動＆統合マニフェスト作成
        /// RawAssetのコピーもここで行う
        /// </summary>
        private static void FinalizeAssets(BuildTarget target)
        {
            string outputPath = ProjectPrefs.GetString(AssetBundleOutputPath, DefaultAssetBundleOutputPath);
            string scriptableObjectManifestPath = AssetDatabase.GenerateUniqueAssetPath("Assets/" + assetbundleManifestName);

            // 変更がないファイルは残したほうが早いかもしれないが、大した差はなさそうなので毎回クリア
            if (Directory.Exists(outputPath))
            {
                DeleteDirectory(outputPath);
            }
            Directory.CreateDirectory(outputPath);

#if GALLOP
            // GallopModify : RawAssetの出力先変更, 平文マニフェスト出力処理追加にともなうディレクトリ準備

            // RawAsset
            string rawassetOutputPath = ProjectPrefs.GetString(RawAssetBundleOutputPath, DefaultRawAssetBundleOutputPath);
            if (Directory.Exists(rawassetOutputPath))
            {
                Directory.Delete(rawassetOutputPath, true);
            }
            Directory.CreateDirectory(rawassetOutputPath);

            // 平文マニフェスト（ビルドしているプラットフォームとGeneric）
            string outputPlaneManifestDirectory = ProjectPrefs.GetString(AssetBundlePlaneManifestOutputPath, DefaultManifestOutputPath);
            string outputGenericManifestDirectory = ProjectPrefs.GetString(AssetBundleGenericManifestOutputPath, DefaultManifestOutputPath);
            if (Directory.Exists(outputPlaneManifestDirectory))
            {
                Directory.Delete(outputPlaneManifestDirectory, true);
            }
            Directory.CreateDirectory(outputPlaneManifestDirectory);
            if (Directory.Exists(outputGenericManifestDirectory))
            {
                Directory.Delete(outputGenericManifestDirectory, true);
            }
            Directory.CreateDirectory(outputGenericManifestDirectory);
#endif

            // RawAssetのvariantリスト
            var variants = GetRawAssetVariants();
#if GALLOP
            // GallopModify : プラットフォームごとにアセバンキャッシュパスを変更する
            string assetBundleCacheDirectory = GetAssetBundleCacheDirectory(target);
#endif

#if GALLOP
            // GallopModify : Sound/Movieビルド時は、この時点で通常バンドルの成果物（ディレクトリ）がない可能性があるのでその場合に空配列を入れるように対応
            string[] categoryDirectories = null;
            categoryDirectories = Directory.Exists(assetBundleCacheDirectory) ? Directory.GetDirectories(assetBundleCacheDirectory) : new string[0];
#else
            // ビルド済みのvariantディレクトリをリストアップ
            var categoryDirectories = Directory.GetDirectories(assetBundleCacheDirectory);
#endif
            foreach (var categoryDirectory in categoryDirectories)
            {
                var variantDirectories = Directory.GetDirectories(categoryDirectory);
                foreach (var d in variantDirectories)
                {
                    // ファイル名ではないがちょうどいいので
                    var directory = Path.GetFileName(d);
                    if (directory.IndexOf(".") < 0)
                    {
                        variants.Add("");
                    }
                    else
                    {
                        variants.Add(directory.Split('.')[1]);
                    }
                }
            }

            HashSet<string> basePaths = null;
            foreach (var variant in variants)
            {
                //string outputManifestPath;
                string rawAssetRootDirectory;

                if (variant != "")
                {
                    rawAssetRootDirectory = ProjectPrefs.GetString(RawAssetManager.RawAssetVariantDirectory) + variant + "/";
                }
                else
                {
                    rawAssetRootDirectory = ProjectPrefs.GetString(RawAssetManager.RawAssetRootDirectory);
                }

                foreach (var categoryDirectory in categoryDirectories)
                {
                    var buildPath = categoryDirectory + "/build";
                    var sceneBuildPath = categoryDirectory + "/scene_build";
                    if (variant != "")
                    {
                        buildPath += "." + variant;
                        sceneBuildPath += "." + variant;
                    }
                    var category = Path.GetFileName(categoryDirectory);

                    CreateManifestAndCopyAssetBundle(category, buildPath, scriptableObjectManifestPath);
                }

                // RawAssetを出力
                if (Directory.Exists(rawAssetRootDirectory))
                {
#if GALLOP
                    var scriptableObjectManifest = CreateManifestAndRawAssetCopy(rawAssetRootDirectory, rawassetOutputPath, scriptableObjectManifestPath, basePaths);
#else
                    var scriptableObjectManifest = CreateManifestAndRawAssetCopy(rawAssetRootDirectory, outputPath, scriptableObjectManifestPath, basePaths);
#endif

                    if (variant == "")
                    {
                        basePaths = new HashSet<string>();
                        // variantなしRawAssetsマニフェスト取得
                        foreach (var r in scriptableObjectManifest.rawAssets)
                        {
                            basePaths.Add(r.name);
                        }
                    }
                }
            }

            // PlaneManifestが生成されてないカテゴリをチェック、もしあれば空のマニフェスト生成する
            // マニフェストマージには対象のカテゴリファイルが必要なため
            //
            // これは主に最低保証リソースバージョンのビルドで発生する
            // (例えば CLI:release/10100 で StoryEventカテゴリが新規追加される場合、
            //  ペアバージョンの RES : release/10000100 にはStoryEventディレクトリが存在するが
            //  最低保証バージョンの RES : release/10000000 にはStoryEventのデータが存在しない )
            /*
            if (finalizeCategories != null)
            {
                foreach (string cateory in finalizeCategories)
                {
                    string planeManifestPath =
                        $"{ProjectPrefs.GetString(AssetBundlePlaneManifestOutputPath)}/plane_{cateory}.manifest";
                    if( File.Exists(planeManifestPath) ) continue;

                    // ファイルがない場合は空ファイルを保存
                    StreamWriter streamWriter = new StreamWriter(planeManifestPath, false, new System.Text.UTF8Encoding(false));
                    streamWriter.Close();
                }

            }
        */
        }

        private static UnityEngine.AssetBundleManifest GetAssetBundleManifest(string manifestPath)
        {
            var assetbundle = UnityEngine.AssetBundle.LoadFromFile(manifestPath);
            var manifest = assetbundle.LoadAllAssets()[0] as UnityEngine.AssetBundleManifest;
            assetbundle.Unload(false);
            return manifest;
        }

        private static string[] GetBuildFilesFromCommandLineArgs()
        {
            var args = System.Environment.GetCommandLineArgs();
            var keyIndex = System.Array.FindIndex(args, arg => arg == buildFileKey);
            if (keyIndex < 0)
            {
                return null;
            }
            if (keyIndex == args.Length - 1)
            {
                throw new ArgumentException("パスが指定されていません");
            }

            string pathArg = args[keyIndex + 1];

            return pathArg.Split(';');
        }

        private static void LogMessageReceived(string condition, string stackTrace, LogType type)
        {
            // BuildPipeline.BuildAssetBundlesでエラーが起きてもExceptionが発生しないので、エラーログの発生で失敗扱いする
            if (type != LogType.Error)
            {
                return;
            }

            // キャッシュサーバーへの接続エラーは無視する
            if (condition.Contains("connect "))
            {
                return;
            }

            // エラーじゃないのにエラーメッセージを出すことがある
            if (condition.Contains("(No errors.)"))
            {
                return;
            }

            // ここでいきなり終了させるとログがでないので、フラグを立ててあとで終了させる. 複数ログが出ることもあるので連結.
            error += condition;
        }

        /// <summary>ファイルを一時退避させる（移動）</summary>
        private static void MoveAssetToTempDirectory(string src, Dictionary<string, string> movedAssets)
        {
            while (true)
            {
                string tempFileName = tempDirectoryPath + Path.GetRandomFileName();
                if (File.Exists(tempFileName))
                {
                    continue;
                }
                File.Move(src, tempFileName);
                if (movedAssets.ContainsKey(src))
                {
                    throw new Exception("Base asset is duplicated: " + src);
                }
                movedAssets.Add(src, tempFileName);
                return;
            }
        }

        /// <summary>ファイルを一時退避させる（コピー）</summary>
        private static void CopyAssetToTempDirectory(string src, Dictionary<string, string> movedAssets)
        {
            while (true)
            {
                string tempFileName = tempDirectoryPath + Path.GetRandomFileName();
                if (File.Exists(tempFileName))
                {
                    continue;
                }
                File.Copy(src, tempFileName);
                movedAssets.Add(src, tempFileName);
                return;
            }
        }

        /// <summary>variant設定ごとにビルドする</summary>
        private static void BuildAllWithVariant(string buildDirectory, List<AssetBundleBuild> builds, List<AssetBundleBuild> sceneBuilds, string variant, BuildTarget target)
        {
            string buildPath;
            string sceneBuildPath;

            if (variant != null)
            {
                buildPath = buildDirectory + "/build." + variant + "/";
                sceneBuildPath = buildDirectory + "/scene_build." + variant + "/";
            }
            else
            {
                buildPath = buildDirectory + "/build/";
                sceneBuildPath = buildDirectory + "/scene_build/";
            }

            // シェーダーとサポートカードを再読込
            // tex_support_card_XXXのサポカ画像はPF毎にNP2設定を変えるため、ビルド毎に再読込が必要
            var reimportArray = builds
                .Where(build =>
                    build.assetBundleName == "shader" + assetBundleExtension || 
                    build.assetBundleName.Contains("tex_support_card_"))
                .SelectMany(build => build.assetNames).ToArray();

            if (reimportArray.Length > 0)
            {
                AssetDatabase.StartAssetEditing();
                try
                {
                    foreach (var build in reimportArray)
                    {
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log("Reimport Asset : " + build);
#endif
                        AssetDatabase.ImportAsset(build);
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }
                AssetDatabase.Refresh();
            }

            /*// cgincだけ更新した際にAssetBundleがビルドされない対策
            DeleteFileIfExists(buildPath + "shader" + assetBundleExtension + ".manifest");
            DeleteFileIfExists(sceneBuildPath + "shader" + assetBundleExtension + ".manifest");*/

            BuildAssetBundles(buildPath, builds, target);

            // BuildPipeline.BuildAssetBundles()のコール間隔を100ms以上開ける必要がある。
            System.Threading.Thread.Sleep(100);

            BuildAssetBundles(sceneBuildPath, sceneBuilds, target);
        }

        private static void DeleteFileIfExists(string path)
        {
            if (File.Exists(path))
            {
                File.Delete(path);
            }
        }

        /// <summary>ビルド対象ごとにAssetBundle作成</summary>
        private static void BuildAssetBundles(string buildPath, List<AssetBundleBuild> builds, BuildTarget target)
        {
            if (builds.Count == 0)
            {
                return;
            }

            if (!Directory.Exists(buildPath))
            {
                Directory.CreateDirectory(buildPath);
            }

            // 空のディレクトリを削除してビルド成功率をあげる
            DeleteEmptyDirectory(buildPath);
            
            Debug.Log($"[LOG] BuildAssetBundles: {buildPath}");
            foreach (var b in builds)
            {
                
                Debug.Log($"[LOG] build: {b}");
            }

            BuildPipeline.BuildAssetBundles(buildPath, builds.ToArray(), BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle, target);

            if (error != null)
            {
                throw new Exception(error);
            }
        }

        /// <summary>
        /// AssetBundle化対象シーンのリストを生成
        /// shaderを別AssetBundle化すると表示がおかしくなるので、共有データはTextureのみとする
        /// </summary>
        private static Dictionary<string, AssetBundleName> GetSceneList(string[] buildFiles)
        {
            using (new Perf("GetSceneList"))
            {
                string rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
                string sceneDirectory = rootDirectory + "Scene";

                Dictionary<string, AssetBundleName> assetList = new Dictionary<string, AssetBundleName>();

#if GALLOP
                // GallopModify :　Directoryがない場合は空のAssetListを返して終了。 Sound/Movieビルド時は _GallopResourcesが存在しないため。
                if (!Directory.Exists(rootDirectory))
                {
                    return assetList;
                }
#endif

                var filterFunction = GetFilterFunction();

                // 1ファイル＝1AssetBundle
                var files = buildFiles != null ? buildFiles : Directory.GetFiles(rootDirectory, "*.unity", SearchOption.AllDirectories);
                List<string> allDependencies = new List<string>();
                foreach (var f in files)
                {
                    var path = f.Replace("\\", "/");
                    if (!IsValidFile(path))
                    {
                        continue;
                    }

                    if (!path.StartsWith(sceneDirectory))
                    {
                        continue;
                    }

                    if (!path.EndsWith(".unity"))
                    {
                        continue;
                    }

                    assetList.Add(path, new AssetBundleName()
                    {
                        assetPath = path,
                        assetBundleName = path.Replace(Path.GetExtension(path), "").Replace(rootDirectory, ""),
                        baseAssetPath = path,
                        guid = AssetDatabase.AssetPathToGUID(path),
                        auto = false,
                    });

                    var dependencies = AssetDatabase.GetDependencies(path)
                        .Where(d =>
                        {
                            // 自身は含まない
                            if (d == path) return false;

                            if (!filterFunction(d))
                            {
                                return false;
                            }
                            // テクスチャのみ個別AssetBundle化
                            return IsTexture(Path.GetExtension(d));
                        });
                    allDependencies.AddRange(dependencies);
                }

                foreach (var dependency in allDependencies)
                {
                    AddAssetBundleNameForScene(dependency, assetList, rootDirectory);
                }

                return assetList;
            }
        }

        /// <summary>テクスチャファイルかどうか</summary>
        static bool IsTexture(string extension)
        {
            // TextureImporter.GetAtPathしてTextureかどうか見たほうが確実だが、こちらのほうが早い
            switch (extension)
            {
                case ".png":
                case ".tga":
                case ".jpg":
                case ".gif":
                case ".psd":
                case ".tif":
                case ".tiff":
                case ".bmp":
                case ".iff":
                case ".pict":
                    return true;
            }
            return false;
        }

        /// <summary>シーンが参照してるTextureをAssetBundle化対象に振り分ける</summary>
        private static void AddAssetBundleNameForScene(string path, Dictionary<string, AssetBundleName> assetList, string rootDirectory)
        {
            var pathExtension = Path.GetExtension(path);
            if (string.IsNullOrEmpty(pathExtension))
            {
                throw new Exception("拡張子がないファイル、ディレクトリはビルドできません:" + path);
            }
            if (path.Replace(pathExtension, "").EndsWith(" "))
            {
                throw new Exception("ファイル名の末尾に空白が含まれています:" + path);
            }

            AssetBundleName asset = null;
            if (!assetList.TryGetValue(path, out asset))
            {
                asset = new AssetBundleName() { assetPath = path, baseAssetPath = path, guid = AssetDatabase.AssetPathToGUID(path), assetBundleName = null, auto = true };
                // まだ名前設定されていないので、とりあえず親と同じAssetBundle
                assetList.Add(path, asset);
                return;
            }

            // 他のAssetからも参照されてるので、テクスチャ毎にAssetBundle化
            asset.assetBundleName = sceneAssetNamePrefix + path.Replace(pathExtension, "").Replace(rootDirectory, "");
            asset.auto = false;
        }

        /// <summary>AssetBundle化対象ファイルリストを生成</summary>
        private static AssetBundleList GetAssetBundleList(string[] buildFiles, ref DependencyDict dependencyDict)
        {
            using (new Perf("GetAssetBundleList"))
            {
                string rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
                string sceneDirectory = rootDirectory + "Scene";

                Dictionary<string, AssetBundleName> assetList = new Dictionary<string, AssetBundleName>();

#if GALLOP
                // GallopModify :　Directoryがない場合は空のAssetListを返して終了。 Sound/Movieビルド時は _GallopResourcesが存在しないため。
                if (!Directory.Exists(rootDirectory))
                {
                    return assetList;
                }

                // GallopModify : Shaderアセバンビルド
                if (buildFiles == null)
                {
                    // 全ビルドの時だけシェーダをアセットバンドル化する
                    // シェーダーの名前付け
                    string[] shaderFiles = GetBuildShaderPaths();
                    foreach (var f in shaderFiles)
                    {
                        var path = f.Replace("\\", "/");

                        // ドットで始まるファイルは無視する
                        if (Path.GetFileName(path).StartsWith("."))
                        {
                            continue;
                        }
                        if (Path.GetExtension(path) == ".meta" || Path.GetExtension(path) == ".unity")
                        {
                            continue;
                        }

                        // 拡張子を付ける
                        path = string.Format("{0}.shader", path);
                        AddShaderName(path, assetList);
                    }
                }
#endif
                // ---------- 1ファイル＝1AssetBundle となるようなアセットの処理
                List<KeyValuePair<string, string>> allDependencies = new List<KeyValuePair<string, string>>();
                foreach (var f in buildFiles)
                {
                    var path = f.Replace("\\", "/");
#if DEBUG_LOG_IN_DETAIL   
                    Debug.Log($"Check File: {path}");
#endif
                    if (!IsValidFile(path))
                    {
                        continue;
                    }

                    if (path.StartsWith(sceneDirectory))
                    {
#if DEBUG_LOG_IN_DETAIL   
                        Debug.Log($"This is Starts with: {path}");
#endif
                        continue;
                    }

                    if (path.EndsWith(".unity"))
                    {
                        continue;
                    }

                    AddAssetBundleName(path, "", assetList, rootDirectory, sceneDirectory);

                    // ---------- 依存関係アセットの収集

                    // dictionaryにアセットが存在しない場合、ここで新規に依存情報を列挙する
                    if (!dependencyDict.ContainsKey(path))
                    {
                        var dependencies = GetDependencyAssets(path);
                        dependencyDict.Add(path, new DependencyAssetList());
                        dependencyDict[path].AssetList.AddRange(dependencies);
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"Dependency Check: {path} => {String.Join(", ", dependencyDict[path].AssetList)}");
#endif
                    }

                    var dependencyAssetList = dependencyDict[path].AssetList;
                    foreach (var d in dependencyAssetList)
                    {
                        allDependencies.Add(new KeyValuePair<string, string>(d, path));
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"Dependency Add: {d}, {path}");
#endif
                    }
                }

                foreach (var dependency in allDependencies)
                {
                    AddAssetBundleName(dependency.Key, dependency.Value, assetList, rootDirectory, sceneDirectory);
                }

                return assetList;
            }
        }

        /// <summary>
        /// そのアセットをどのAssetBundleにパッケージングするか決める処理
        /// 単一参照のSourceResourcesは参照先に含まれ、複数参照のSourceResourcesは単独でビルドされる
        /// </summary>
        private static void AddAssetBundleName(string path, string parentPath, Dictionary<string, AssetBundleName> assetList, string rootDirectory, string sceneDirectory)
        {
            var pathExtension = Path.GetExtension(path);
            if (string.IsNullOrEmpty(pathExtension))
            {
                if (Directory.Exists(path))
                {
                    // ディレクトリに依存関係が持てるのでExceptionにはしない
                    return;
                }
                else
                {
                    throw new Exception("拡張子がないファイルはビルドできません:" + path);
                }
            }
            if (path.Replace(pathExtension, "").EndsWith(" "))
            {
                throw new Exception("ファイル名の末尾に空白が含まれています:" + path);
            }

            // Scene内はGetSceneListで処理済みなのでここでは無視する
            if (path.Contains(sceneDirectory))
            {
                return;
            }

#if GALLOP
            // GallopModify : MaskはUnityのバージョン次第ではビルドできるようになった
            if (pathExtension == ".giparams")
#else
            if (pathExtension == ".mask" || pathExtension == ".giparams")
#endif
            {
                return;
            }

            AssetBundleName asset = null;
            if (!assetList.TryGetValue(path, out asset))
            {
                // ----- 新規アセット
                asset = new AssetBundleName() { assetPath = path, baseAssetPath = path, guid = AssetDatabase.AssetPathToGUID(path) };

                if (string.IsNullOrEmpty(parentPath))
                {
                    // 親がいない = 直接ビルドターゲットになったアセット
                    var assetBundleName = path.Replace(Path.GetExtension(path), "").Replace(rootDirectory, "");
                    if (customFunction == null)
                    {
                        asset.assetBundleName = assetBundleName;
                    }
                    else
                    {
                        assetBundleName = customFunction(path, assetBundleName);
                        if (string.IsNullOrEmpty(assetBundleName))
                        {
                            return;
                        }
                        else
                        {
                            asset.assetBundleName = assetBundleName;
                        }
                    }
                    // 元々AssetBundle化対象のため、以降のプロセスで名前を変えない
                    asset.fix = true;
                    asset.auto = false;
                }
                else
                {
                    // 親が存在する（何かしらのアセットから依存で読み込まれている）
                    if (!assetList.TryGetValue(parentPath, out AssetBundleName parentAssetBundleName))
                    {
                        // 親の名前が登録されてないのでAssetBundle化対象外
#if DEBUG_LOG_IN_DETAIL
                        Debug.Log($"This is not target: {asset.assetBundleName}");
#endif
                        return;
                    }

                    asset.assetBundleName = parentAssetBundleName.assetBundleName;
                    asset.auto = true;

                    // 将来的に辞書を引きたいので、親子関係を記憶する
                    asset.parentAssetPath.Add(parentPath);
                    parentAssetBundleName.childAssetPath.Add(path);
                }

                // パッキングタグのあるスプライトはパッキング名のアセットバンドルに格納される
                // どこからも依存していないパッキングするスプライトがまとめられないのでここで指定する
                if (IsSetSpritePackingTag(path, ref asset.assetBundleName))
                {
                    asset.auto = false;
                }

                assetList.Add(path, asset);
                return;
            }
#if DEBUG_LOG_IN_DETAIL
            else
            {
                Debug.Log($"Not found asset: {path}");
            }
#endif
            // 非参照カウントアップ
            asset.dependencyCount++;

            if (assetList.TryGetValue(parentPath, out AssetBundleName parentBundle))
            {
                // 将来的に辞書を引きたいので、親子関係を記憶する
                asset.parentAssetPath.Add(parentPath);
                parentBundle.childAssetPath.Add(path);
            }

            if (asset.fix)
            {
                return;
            }

            // 複数個所から参照されていることが確定したので、明示的にAssetBundleに含める
            asset.auto = false;

            if (pathExtension == ".shader")
            {
                // shaderは全て1AssetBundleにまとめる
                asset.assetBundleName = "shader";
                return;
            }

            // AnimatorControllerが参照しているAnimationClipもAssetBundle化する
            var parentPathExtension = Path.GetExtension(parentPath);
            if (pathExtension == ".anim" && (parentPathExtension == ".controller" || parentPathExtension == ".overrideController"))
            {
                // 他のAssetからも参照されてるので、ディレクトリ毎にAssetBundle化
                asset.assetBundleName = Path.GetDirectoryName(path);
                return;
            }

            // 複数AssetCreateSerializableBuildList で共有されてるマテリアルは別AssetBundleにする
            if (pathExtension == ".mat")
            {
                // 他のAssetからも参照されてるので、マテリアル毎にAssetBundle化
#if GALLOP                
                asset.assetBundleName = GetAssetBundleName(path, rootDirectory);
#else
                asset.assetBundleName = path.Replace(pathExtension, "").Replace(rootDirectory, "");
#endif

                // テクスチャもマテリアルと同じAssetBundleに入れる
                var materialDependencies = AssetDatabase.GetDependencies(path);
                foreach (var texture in materialDependencies.Where(d => !d.EndsWith(".shader")))
                {
                    // URPアセットは.csファイルの参照を含んでいるためここで弾く
                    if (texture.EndsWith(".cs")) continue;

                    AssetBundleName textureAsset = null;
                    if (!assetList.TryGetValue(texture, out textureAsset))
                    {
                        textureAsset = new AssetBundleName() { assetPath = texture, baseAssetPath = texture, guid = AssetDatabase.AssetPathToGUID(texture), auto = false };
                        assetList.Add(texture, textureAsset);
                    }

#if GALLOP
                    if (!textureAsset.fix)    // 既に独立しているテクスチャ（Bundle以下のもの）は独立したままにしたいので、fixしていない場合だけ
                    {
                        // 複数マテリアルから参照されてる場合に問題になるが、マテリアルが1個余計に読まれるだけなので無視して大丈夫そう
                        textureAsset.assetBundleName = asset.assetBundleName;
                    }
#else
                    // 複数マテリアルから参照されてる場合に問題になるが、マテリアルが1個余計に読まれるだけなので無視して大丈夫そう
                    textureAsset.assetBundleName = asset.assetBundleName;
#endif
                }
                return;
            }

            if (IsSetSpritePackingTag(path, ref asset.assetBundleName))
            {
                return;
            }

            // その他のAsset
#if GALLOP                
            asset.assetBundleName = GetAssetBundleName(asset.assetPath, rootDirectory);
#else
            asset.assetBundleName = path.Replace(Path.GetExtension(path), "").Replace(rootDirectory, "");
#endif
        }

#if GALLOP
        // GallopModify : アセバン名の取得処理を共通化
        private static string GetAssetBundleName(string filePath, string rootDirectory)
        {
            if (customFunction != null)
            {
                var retAssetBundleName = customFunction(filePath, "");
                if (!string.IsNullOrEmpty(retAssetBundleName))
                {
                    return retAssetBundleName;
                }
            }

            return filePath.Replace(Path.GetExtension(filePath), "").Replace(rootDirectory, "");
        }

        // GallopModify : Shaderアセバンビルド

        /// <summary>
        /// シェーダーファイルにアセバン名を付ける
        /// </summary>
        /// <param name="path"></param>
        /// <param name="assetList"></param>
        private static void AddShaderName(string path, Dictionary<string, AssetBundleName> assetList)
        {
            var pathExtension = Path.GetExtension(path);
            if (string.IsNullOrEmpty(pathExtension))
            {
                throw new Exception("拡張子がないファイル、ディレクトリはビルドできません:" + path);
            }
            if (path.Replace(pathExtension, "").EndsWith(" "))
            {
                throw new Exception("ファイル名の末尾に空白が含まれています:" + path);
            }
            //BuildPipelineで失敗するため無視
            if (pathExtension == ".mask" || pathExtension == ".giparams")
            {
                return;
            }
            AssetBundleName asset = null;
            if (!assetList.TryGetValue(path, out asset))
            {
                asset = new AssetBundleName() { assetPath = path, baseAssetPath = path, guid = AssetDatabase.AssetPathToGUID(path) };
                string assetBundleName = "shader";
                asset.assetBundleName = assetBundleName;
                assetList.Add(path, asset);
                return;
            }
        }
#endif

        private static bool IsSetSpritePackingTag(string path, ref string assetBundleName)
        {
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                if (!string.IsNullOrEmpty(importer.spritePackingTag))
                {
                    // ResourcesがAssetBundle対象ディレクトリになっているとここは通るとまずい
                    if (path.Contains("/Resources/"))
                    {
                        throw new System.Exception("Resourcesディレクトリにパックするスプライトをおいてはいけない:" + path);
                    }

                    // アトラス化されたテクスチャ毎にAssetBundle化
                    assetBundleName = "atlas/" + importer.spritePackingTag;
                    return true;
                }
            }
            return false;
        }

        private static List<AssetBundleBuild> CreateBuildList(Dictionary<string, AssetBundleName> assetList)
        {
            using (new Perf("CreateBuildList"))
            {
                var builds = new List<AssetBundleBuild>();

                var list = assetList.Values.Where(a => a.assetBundleName != null && !a.auto)
                    .OrderByDescending(name => name.dependencyCount)
                    .ToList();

                foreach (var asset in list)
                {
                    var index = builds.FindIndex(b => b.assetBundleName == asset.assetBundleName + assetBundleExtension);
                    if (index < 0)
                    {
                        builds.Add(new AssetBundleBuild()
                        {
                            assetBundleName = asset.assetBundleName + assetBundleExtension,
                            assetBundleVariant = "",
                            assetNames = new string[] { asset.baseAssetPath },
                        });
                    }
                    else
                    {
                        var newBuilds = builds[index];
                        var tmpList = new List<string>(newBuilds.assetNames);
                        tmpList.Add(asset.baseAssetPath);
                        newBuilds.assetNames = tmpList.ToArray();
                        builds[index] = newBuilds;
                    }
                }

                return builds;
            }
        }

#if GALLOP 
        private static List<SerializableAssetBundleBuild> CreateSerializableBuildList(AssetBundleList assetList)
        {
            using (new Perf("CreateSerializableBuildList"))
            {
                // AssetBundleName.autoが立っているアセットを一覧化(List.A)
                // これは明示的にAssetBundleに記載されず、Unityが自動参照で引っ張られるもの
                Dictionary<string, List<AssetBundleName>> autoReferencedAssetDict = new Dictionary<string, List<AssetBundleName>>();
                var autoRefAssetEnumeratable = assetList.Values.Where(a => a.assetBundleName != null && a.auto);
                foreach (var ABNameObj in autoRefAssetEnumeratable)
                {
                    var outputAssetBundleName = ABNameObj.assetBundleName + assetBundleExtension;
                    if (!autoReferencedAssetDict.ContainsKey(outputAssetBundleName))
                    {
                        autoReferencedAssetDict.Add(outputAssetBundleName, new List<AssetBundleName>());
                    }

                    autoReferencedAssetDict[outputAssetBundleName].Add(ABNameObj);
                }

                // AssetBundleName.autoが立っていないアセットを一覧化 (List.B)
                // これが実際にUnityに投げ込むビルドリストになる
                var list = assetList.Values.Where(a => a.assetBundleName != null && !a.auto)
                    .OrderByDescending(name => name.dependencyCount)
                    .ToList();

                // List.Bを回してアセバンビルドの基礎リストを作成
                var convertedParentAssetHashSet = new HashSet<string>();
                var convertedChildtAssetHashSet = new HashSet<string>();

                // key : バンドルフルパス(～.a) , value : Unityにバンドルビルドに渡すビルド設定情報
                var buildDict = new Dictionary<string, SerializableAssetBundleBuild>();

                foreach (var asset in list)
                {
                    string fullBundleName = asset.assetBundleName + assetBundleExtension;
                    if (!buildDict.TryGetValue(fullBundleName, out var buildData))
                    {
                        convertedParentAssetHashSet.Clear();
                        convertedChildtAssetHashSet.Clear();
                        foreach (var path in asset.parentAssetPath)
                        {
                            if (assetList.TryGetValue(path, out AssetBundleName retBundleName))
                            {
                                convertedParentAssetHashSet.Add(retBundleName.assetBundleName + assetBundleExtension);
                            }
                        }
                        foreach (var path in asset.childAssetPath)
                        {
                            if (assetList.TryGetValue(path, out AssetBundleName retBundleName))
                            {
                                convertedChildtAssetHashSet.Add(retBundleName.assetBundleName + assetBundleExtension);
                            }
                        }

                        buildData = new SerializableAssetBundleBuild()
                        {
                            assetBundleName = fullBundleName,
                            assetBundleVariant = "",
                            assetNames = new List<string> { asset.baseAssetPath },
                            autoReferencedAssetNames = new List<string>(),
                            parentAssetList = convertedParentAssetHashSet.ToList(),
                            childAssetList = convertedChildtAssetHashSet.ToList()
                        };
                        buildDict.Add(fullBundleName, buildData);
                    }
                    else
                    {
                        buildData.assetNames.Add(asset.baseAssetPath);
                    }
                }

                // List.Aのリストとマッチングして、autoReferenceAssetNameのリストを追加
                foreach (var assetBundleBuild in buildDict.Values)
                {
                    // List.Aに同名アセバンがあるかチェック
                    List<AssetBundleName> autoRefAssetList = null;
                    if (autoReferencedAssetDict.TryGetValue(assetBundleBuild.assetBundleName, out autoRefAssetList))
                    {
                        foreach (var autoRefAsset in autoRefAssetList)
                        {
                            assetBundleBuild.autoReferencedAssetNames.Add(autoRefAsset.baseAssetPath);
                        }
                    }
                }

                return buildDict.Values.ToList();
            }
        }
#endif        

        /// <summary>variant情報収集</summary>
        private static Dictionary<string, List<VariantInfo>> CreateVariantInfoList(Dictionary<string, AssetBundleName> allAssetList, Dictionary<string, AssetBundleName> allSceneList)
        {
            using (new Perf("CreateVariantInfoList"))
            {
                string rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
                string variantsDirectory = ProjectPrefs.GetString(AssetBundleVariantsDirectory);

                var allVariants = new Dictionary<string, List<VariantInfo>>();

                if (string.IsNullOrEmpty(variantsDirectory))
                {
                    return allVariants;
                }

                Dictionary<string, AssetBundleName> baseAssetList = new Dictionary<string, AssetBundleName>(allAssetList);
                foreach (var asset in allSceneList)
                {
                    // guidは変わらないはずなので上書きOK
                    baseAssetList[asset.Key] = asset.Value;
                }

                Dictionary<string, AssetBundleName> baseAssetListWithoutExtension = new Dictionary<string, AssetBundleName>();
                foreach (var asset in baseAssetList)
                {
                    // キーが重複するケースがあるが一旦無視
                    baseAssetListWithoutExtension[Core.Path.GetPathWithoutExtension(asset.Key)] = asset.Value;
                }

                //Dictionary<string, AssetBundleName> baseAssetListFileName = new Dictionary<string, AssetBundleName>();
                //foreach (var asset in baseAssetList)
                //{
                //    // キーが重複するケースがあるが一旦無視
                //    baseAssetListFileName[Path.GetFileName(asset.Key)] = asset.Value;
                //}

                var variantsDirectories = Directory.GetDirectories(variantsDirectory);
                foreach (var d in variantsDirectories)
                {
                    List<VariantInfo> variantList = new List<VariantInfo>();

                    var variantDirectory = d.Replace("\\", "/");

                    string variantName = variantDirectory.Replace(variantsDirectory, "").Replace("/", "");
                    var files = Directory.GetFiles(variantDirectory, "*.*", SearchOption.AllDirectories);
                    foreach (var f in files)
                    {
                        var path = f.Replace("\\", "/");

                        if (!IsValidFile(path))
                        {
                            continue;
                        }

                        if (baseAssetList.ContainsKey(path))
                        {
                            // variant化対象ファイルがすでにビルドリスト入してたらエラーにする
                            throw new Exception("variant対象ファイルがビルドリストに含まれてます. ビルド対象ファイルから直接参照されてる可能性が高いです. : " + path);
                        }

                        string baseAssetPath = path.Replace(variantDirectory + "/", rootDirectory);
                        AssetBundleName baseAsset = null;
                        if (!baseAssetList.TryGetValue(baseAssetPath, out baseAsset))
                        {
                            string baseAssetPathWithoutExtension = Core.Path.GetPathWithoutExtension(baseAssetPath);
                            if (!baseAssetListWithoutExtension.TryGetValue(baseAssetPathWithoutExtension, out baseAsset))
                            {
                                // 見つからなかったのでファイル名が一致してるものも検索
                                //string baseAssetName = Path.GetFileName(baseAssetPath);
                                //if (!baseAssetListFileName.TryGetValue(baseAssetName, out baseAsset))
                                {
                                    // それでも見つからなかったらwarningを吐いておく
                                    //throw new System.Exception("base asset is not found:" + f);
                                    Debug.LogWarning("base asset is not found:" + f);
                                    continue;
                                }
                            }
                        }
                        variantList.Add(new VariantInfo() { assetPath = path, baseAssetPath = baseAsset.assetPath, guid = baseAsset.guid });
                    }

                    allVariants.Add(variantName, variantList);
                }

                return allVariants;
            }
        }

        private static HashSet<string> GetRawAssetVariants()
        {
            using (new Perf("GetRawAssetVariants"))
            {
                string rawAssetVaiantDirectory = ProjectPrefs.GetString(RawAssetManager.RawAssetVariantDirectory);

                HashSet<string> variants = new HashSet<string>();
                // RawAssetRootDirectory用
                variants.Add("");

                if (string.IsNullOrEmpty(rawAssetVaiantDirectory))
                {
                    return variants;
                }

                var variantsDirectories = Directory.GetDirectories(rawAssetVaiantDirectory);
                foreach (var d in variantsDirectories)
                {
                    var variantDirectory = d.Replace("\\", "/");

                    string variantName = variantDirectory.Replace(rawAssetVaiantDirectory, "").Replace("/", "");

                    variants.Add(variantName);
                }

                return variants;
            }
        }

        private static List<AssetBundleBuild> CreateBuildListFromVariants(List<AssetBundleBuild> buildList, List<AssetBundleName> baseAssetList, List<VariantInfo> variantList)
        {
            using (new Perf("CreateBuildListFromVariants"))
            {
                // variantで更新するファイルリスト
                List<string> baseAssets = new List<string>(variantList.Count);
                foreach (var v in variantList)
                {
                    baseAssets.Add(v.baseAssetPath);
                }

                // variantで更新するAssetBundle名リスト
                List<string> variantAssetList = new List<string>();
                foreach (var a in baseAssetList)
                {
                    if (baseAssets.Contains(a.assetPath))
                    {
                        variantAssetList.Add(a.assetBundleName + assetBundleExtension);
                    }
                }

                // Buildリストから更新があるものを選択
                List<AssetBundleBuild> list = new List<AssetBundleBuild>();
                foreach (var b in buildList)
                {
                    if (variantAssetList.Contains(b.assetBundleName))
                    {
                        list.Add(b);
                    }
                }

                return list;
            }
        }

        private static void SafeDelete(string path)
        {
            if (File.Exists(path))
            {
                File.Delete(path);
            }
        }

        /// <summary>不要ファイル削除</summary>
        private static void DeleteUnusedCacheFilesForDiffBuild(string buildPath)
        {
            HashSet<string> prevBuildFiles = new HashSet<string>(
                Directory.GetFiles(buildPath, "*" + assetBundleExtension + ".manifest", SearchOption.AllDirectories)
                    .Select(f => f.Replace(buildPath, "").Replace("\\", "/").Replace(".manifest", ""))
            );

            // 削除対象ファイルのキャッシュを削除
            foreach (var deleteFile in DiffBuildParameter.DeleteBundleList)
            {
                if (prevBuildFiles.Contains(deleteFile))
                {
                    File.Delete(buildPath + "/" + deleteFile);
                    File.Delete(buildPath + "/" + deleteFile + ".manifest");
                }
            }

            DeleteOnlyManifestFiles(buildPath);
        }

        /// <summary>不要ファイル削除</summary>
        private static void DeleteUnusedFiles(string buildPath, List<AssetBundleBuild> builds)
        {
            if (!Directory.Exists(buildPath))
            {
                // キャッシュディレクトリがそもそも存在しない
                return;
            }

            var prevBuildFiles = Directory.GetFiles(buildPath, "*" + assetBundleExtension + ".manifest", SearchOption.AllDirectories)
                .Select(f => f.Replace(buildPath, "").Replace("\\", "/").Replace(".manifest", ""));
            var notUsedFiles = prevBuildFiles.Except(builds.Select(a => a.assetBundleName.ToLower()));
            foreach (var f in notUsedFiles)
            {
#if GALLOP                
                // 差分ビルドが可能な場合はStoryTimeline系データは前回ビルド分を流用するためキャッシュ削除しない
                if (BuildParameterBridge.EnableStoryDiffBuild)
                {
                    if (f.StartsWith("story/data/"))
                    {
                        string fileWithoutExtension = f.Replace(Path.GetExtension(f), "");
                        // 削除対象でない限り、削除処理をスキップ
                        if (!BuildParameterBridge.RemoveStoryTimelinePaths.Contains(fileWithoutExtension))
                        {
                            continue;
                        }
                    }
                }
#endif
                File.Delete(buildPath + "/" + f);
                File.Delete(buildPath + "/" + f + ".manifest");
            }

            DeleteOnlyManifestFiles(buildPath);
        }

        /// <summary>
        /// ビルドエラーによりmanifestのみが残ってしまったファイルの削除
        /// </summary>
        private static void DeleteOnlyManifestFiles(string buildPath)
        {
            // ビルド中にエラーがおきるとmanifestだけが残って本体がなくなることがある
            // 再ビルド時に出力し直して欲しいので、manifestだけの場合は削除する
            var assetBundles = Directory.GetFiles(buildPath, "*" + assetBundleExtension, SearchOption.AllDirectories);
            var manifests = Directory.GetFiles(buildPath, "*" + assetBundleExtension + ".manifest", SearchOption.AllDirectories);
            var noAssetBundleManifests = manifests.Except(assetBundles.Select(s => s + ".manifest"));
            foreach (var f in noAssetBundleManifests)
            {
                File.Delete(f);
            }
        }

        /// <summary>不要なディレクトリを削除</summary>
        private static void DeleteEmptyDirectory(string rootDirectory)
        {
            foreach (var directory in Directory.GetDirectories(rootDirectory))
            {
                DeleteEmptyDirectory(directory);

                if (Directory.GetFiles(directory, "*.*", SearchOption.AllDirectories).Length == 0)
                {
                    Directory.Delete(directory);
                }
            }
        }

        /// <summary>マニフェスト生成＆AssetBundleをリネームしつつコピー</summary>
        private static void CreateManifestAndCopyAssetBundle(string categoryName, string buildPath, string scriptableObjectManifestPath)
        {
            string manifestPath = buildPath + "/" + Path.GetFileName(buildPath);
            if (!File.Exists(manifestPath))
            {
                return;
            }

            var manifest = GetAssetBundleManifest(manifestPath);
            var scriptableObjectManifest = GetScriptableObjectManifest(scriptableObjectManifestPath);

            var isEncryptionFunction = GetEncryptionCheckFunction();
            //var encryptionFunction = GetEncryptionFunction();
            var groupFunction = GetGroupFunction();

            //AssetDatabase.StartAssetEditing();

            var category = scriptableObjectManifest.GetCategory(categoryName);

            List<ScriptableObjectManifest.Asset> assetList = new List<ScriptableObjectManifest.Asset>(category.assets);
            List<ScriptableObjectManifest.Asset> cryptedAssetList =
                new List<ScriptableObjectManifest.Asset>(category.encryptedAssets);

            var assetBundleNames = manifest.GetAllAssetBundles();

            foreach (var assetBundleName in assetBundleNames)
            {
                ScriptableObjectManifest.Asset asset = new ScriptableObjectManifest.Asset();

                var path = buildPath + "/" + assetBundleName;

                var assetName = assetBundleName.ToLower();

                asset.name = assetName.Substring(0, assetName.Length - assetBundleExtension.Length);
                var fileInfo = new FileInfo(path);
                asset.size = (int)fileInfo.Length;
                asset.group = groupFunction(asset.name, out asset.downloadPriority);
                asset.hash = "";
#if GALLOP
                asset.path = path;
#endif

                if (path.Contains(buildPath + "atlas"))
                {
                    // Atlas化されたSpriteはひとまとめに扱う
                }
                else
                {
                    asset.dependencies = manifest.GetDirectDependencies(assetName)
                        .Select(d => d.Substring(0, d.Length - assetBundleExtension.Length))
                        .ToArray();
                }

                assetList.Add(asset);
            }

            // PlaneManifestの生成
            OutputPlaneManifest(assetList);
        }

#if GALLOP
        // PlaneManifest出力
        private static void OutputPlaneManifest(List<ScriptableObjectManifest.Asset> assetList)
        {
            SortedStringDict writeDataDict = new SortedStringDict();

            // 今回書き込むファイルをリストに追加
            if (assetList != null)
            {
                var resourcesVersion = int.Parse(System.Environment.GetEnvironmentVariable(BuildParameterBridge.ENV_KEY_RESOURCES_VERSION));
                foreach (var assetinfo in assetList)
                {
                    // バージョンタグチェック
                    var fullPath = Directory.GetCurrentDirectory() + "/" + assetinfo.path;
                    if(!BuildAssetBundle.FilterVersionTag(fullPath, resourcesVersion))
                    {
                        Debug.Log($"[Log] Skip write plane manifest by version tag, filename = {assetinfo.name}");
                        continue;
                    }
                    
                    Debug.Log($"[Log] Write plane manifest, filename = {assetinfo.name}");
                    writeDataDict.Add(assetinfo.name, assetinfo.ToPlaneManifestLine());
                }
            }
            
            // 差分ビルドありの場合は、前回の差分を可能な限り再利用する
            if (DiffBuildParameter.Enable)
            {
                var deleteBundleListLower = DiffBuildParameter.DeleteBundleList
                    .Select(f => f.Replace(".a", "").ToLower()).ToList();
                var buildBundleListLower = DiffBuildParameter.BuildBundleSet
                    .Select(f => f.Replace(".a", "").ToLower()).ToList();

                string[] prevPlaneManifestArray = Directory.GetFiles(BuildParameterBridge.PrevPlaneManifestRoot);
                foreach (var prevPlaneManifestPath in prevPlaneManifestArray)
                {
                    Debug.Log($"parse prev manifest, filename = {prevPlaneManifestPath}");

                    // 異常な拡張子のファイルが存在することがある(.swpなど）ためmanifestのみを対象とする
                    var extension = Path.GetExtension(prevPlaneManifestPath);
                    if (extension != ".manifest") continue;

                    // 前回分のマニフェスト情報をリストに追加
                    using (StreamReader prevManifestReader =
                           new StreamReader($"{prevPlaneManifestPath}"))
                    {
                        string nextLine = prevManifestReader.ReadLine();
                        while (nextLine != null)
                        {
                            string line = nextLine;
                            nextLine = prevManifestReader.ReadLine();

                            // 削除対象の場合は再利用対象から除外
                            string lineFilePath = line.Split(',')[0];

                            // 重複書き込み対策。既に同じファイルの情報が書き込まれているならスキップする
                            if (writeDataDict.ContainsKey(lineFilePath))
                            {
                                continue;
                            }

                            if (deleteBundleListLower.Contains(lineFilePath)
                                || buildBundleListLower.Contains(lineFilePath))
                            {
                                // 削除対象はそもそも書かない、ビルド対象は後から別途書き込む
                                Debug.Log($"[Log] Skip write prev manifest into plane manifest, filename = {lineFilePath}");
                                continue;
                            }

                            Debug.Log($"[Log] Write prev manifest into plane manifest, filename = {lineFilePath}");
                            writeDataDict.Add(lineFilePath, line);
                        }
                    }
                }
            }
            else
            {
                // Fullビルド
                // Story差分ビルドを行う場合は一部のみ再利用する
                if (BuildParameterBridge.EnableStoryDiffBuild)
                {
                    // 差分ビルドをする場合、差分のないStoryアセットはビルドから除外されているはず
                    // そのためここで前回のマニフェストからサルベージする
                    StreamReader prevManifestReader = null;

                    // 解析する対象のマニフェストは、比較元がV1かV2かで変わる

                    string planeAllFilePath = $"{BuildParameterBridge.PrevPlaneManifestRoot}/plane_All.manifest";
                    string planeStoryFilePath =
                        $"{BuildParameterBridge.PrevPlaneManifestRoot}/plane_Story.manifest";
                    // Allが内容を含んでいる場合はV2なのでAllから情報を取り出す
                    if (File.Exists(planeAllFilePath))
                    {
                        FileInfo fileInfo = new FileInfo(planeAllFilePath);
                        if (fileInfo.Length > 10)
                        {
                            prevManifestReader = new StreamReader(planeAllFilePath);
                        }
                    }

                    // Allから情報がとれなかったらV1なのでstoryから情報を取り出す
                    if (File.Exists(planeStoryFilePath))
                    {
                        prevManifestReader = new StreamReader(planeStoryFilePath);
                    }

                    string nextLine = prevManifestReader.ReadLine();
                    while (nextLine != null)
                    {
                        string line = nextLine;
                        nextLine = prevManifestReader.ReadLine();

                        // story/data以外は対象外
                        if (!line.StartsWith("story/data")) continue;

                        // 削除対象の場合は再利用対象から除外
                        string lineFilePath = line.Split(',')[0];
                        if (BuildParameterBridge.RemoveStoryTimelinePaths.Contains(lineFilePath)) continue;

                        // 更新対象ファイルの場合も、後から別途追記されるため再利用はしない
                        if (BuildParameterBridge.ModifyStoryTimelinePaths.Contains(lineFilePath)) continue;

                        writeDataDict.Add(lineFilePath, line);
                    }
                }
            }

            // ファイル名からカテゴリごとに分類
            SortedDictionary<string, List<string>> categoryBaseWriteDataDict =
                new SortedDictionary<string, List<string>>();
            foreach (var writeData in writeDataDict)
            {
                string category = GetCategoryFromBundleName(writeData.Key);
                if (!categoryBaseWriteDataDict.ContainsKey(category))
                {
                    categoryBaseWriteDataDict.Add(category, new List<string>());
                }

                categoryBaseWriteDataDict[category].Add(writeData.Value);
            }
            // 分類したカテゴリごとのplane_manifestとして出力
            foreach (var categoryBaseWriteData in categoryBaseWriteDataDict)
            {
                string category = categoryBaseWriteData.Key;
                string planeManifestPath =
                    $"{ProjectPrefs.GetString(AssetBundlePlaneManifestOutputPath)}/plane_{category}.manifest";

                using (StreamWriter streamWriter =
                       new StreamWriter(planeManifestPath, false, new System.Text.UTF8Encoding(false)))
                {
                    foreach (var writeData in categoryBaseWriteData.Value)
                    {
                        streamWriter.WriteLine(writeData);
                    }
                }
            }
        }
#endif

        private static bool IsEncryption(string assetBundleName, EncryptionCheckFunction isEncryptionFunction)
        {
            if (isEncryptionFunction == null)
            {
                return false;
            }

            return isEncryptionFunction(assetBundleName);
        }

        /// <summary>マニフェスト生成＆RawAssetをリネームしつつコピー</summary>
        private static ScriptableObjectManifest CreateManifestAndRawAssetCopy(string rootDirectory, string outputPath, string manifestPath, HashSet<string> basePaths)
        {
            if (string.IsNullOrEmpty(rootDirectory))
            {
                return null;
            }

            // rawassetの場合manifestは毎回再生成する
            string manifestFullPath = Application.dataPath.Replace("Assets", "") + manifestPath;
            if (File.Exists(manifestFullPath))
            {
                File.Delete(manifestFullPath);
            }
            var scriptableObjectManifest = GetScriptableObjectManifest(manifestPath);

            var groupFunction = GetGroupFunction();

            List<ScriptableObjectManifest.RawAsset> assetList;
            if (scriptableObjectManifest.rawAssets == null)
            {
                assetList = new List<ScriptableObjectManifest.RawAsset>();
            }
            else
            {
                assetList = new List<ScriptableObjectManifest.RawAsset>(scriptableObjectManifest.rawAssets);
            }

#if GALLOP
            // GallopModify : 平文マニフェストも出力
            string rawAssetCategory = ProjectPrefs.GetString(RawAssetCategoryName);
            string rawAssetManifestOutputDir = Cute.Core.ProjectPrefs.GetString(Cute.AssetBundle.BuildAssetBundle.AssetBundleGenericManifestOutputPath);
            string genericManifestPath = $"{rawAssetManifestOutputDir}/plane_{rawAssetCategory}.manifest";

            System.IO.StreamWriter streamWriter = new System.IO.StreamWriter(genericManifestPath, false, new System.Text.UTF8Encoding(false));
            bool isFirstLine = true;
#endif

            var filterFunction = GetFilterFunction();

            var assetBundleFiles = Directory.GetFiles(rootDirectory, "*.*", SearchOption.AllDirectories);
            foreach (var f in assetBundleFiles)
            {
                var path = f.Replace("\\", "/");

                if (!IsValidFile(path))
                {
                    continue;
                }

                var name = path.Replace(rootDirectory, "").ToLower(); // AssetBundle側の仕様に合わせて小文字化しておく

                // variantなし版に含まれないファイルは無視する
                if (basePaths != null && !basePaths.Contains(name))
                {
                    continue;
                }

                if (!filterFunction(path))
                {
                    continue;
                }

                ScriptableObjectManifest.RawAsset asset = new ScriptableObjectManifest.RawAsset();

                asset.name = name;
                asset.hash = "";
                var fileInfo = new FileInfo(path);
                asset.size = (int)fileInfo.Length;
                asset.group = groupFunction(asset.name, out asset.downloadPriority);
                assetList.Add(asset);
#if GALLOP
                // GallopModify : 平文Manifest出力

                // 1行名以外は改行する
                string writeLine = "";
                if (!isFirstLine)
                {
                    writeLine += "\n";
                }
                isFirstLine = false;

                // 平文の中身記述(rawassetの場合)
                // path,hash,size,group
                writeLine += asset.name + "," + asset.downloadPriority + "," + asset.size + "," + asset.group;
                streamWriter.Write(writeLine);
            }
            streamWriter.Close();
#else
            }
#endif
            scriptableObjectManifest.rawAssets = assetList.ToArray();

            EditorUtility.SetDirty(scriptableObjectManifest);

            return scriptableObjectManifest;
        }

        /// <summary>
        /// ScriptableObjectManifestを取得する
        /// BuildAssetBundleをすると参照が切れるので、都度取得すること
        /// </summary>
#if GALLOP
        // GallopModify : GallopではBuildAssetBundleの外でScriptableObjectManifestを生成するので外からアクセスできるようにする
        public static ScriptableObjectManifest GetScriptableObjectManifest(string manifestPath)
#else
        private static ScriptableObjectManifest GetScriptableObjectManifest(string manifestPath)
#endif
        {
            Debug.Log($"GetScriptableObjectManifest : {manifestPath}");

            var scriptableObjectManifest = AssetDatabase.LoadAssetAtPath<ScriptableObjectManifest>(manifestPath);
            if (scriptableObjectManifest == null)
            {
                Debug.Log("scriptableObjectManifest is null");

                scriptableObjectManifest = ScriptableObject.CreateInstance<ScriptableObjectManifest>();
                scriptableObjectManifest.categories = new ScriptableObjectManifest.Category[0];
                AssetDatabase.CreateAsset(scriptableObjectManifest, manifestPath);
            }
            return scriptableObjectManifest;
        }

        /// <summary>
        /// AssetBundle化ルールカスタマイズ関数を取得
        /// 関数名の指定は、「(Namespace.)ClassName.FunctionName」の書式で記述すること
        /// </summary>
        /// <returns>ファイルパスとAssetBundle名を引数にとり、AssetBundle名を返す関数</returns>
        private static CustomFunction GetCustomizeFunction()
        {
            return GetProjectPrefsFunction<CustomFunction>(AssetBundleCustomization);
        }

        /// <summary>
        /// AssetBundle暗号化判定関数を取得
        /// 関数名の指定は、「(Namespace.)ClassName.FunctionName」の書式で記述すること
        /// </summary>
        /// <returns>ファイルパスとAssetBundle名を引数にとり、暗号化するかどうかを返す関数</returns>
        private static EncryptionCheckFunction GetEncryptionCheckFunction()
        {
            return GetProjectPrefsFunction<EncryptionCheckFunction>(AssetBundleEncryptionCheck);
        }

        private static GroupFunction _groupFunction = null;

        /// <summary>
        /// Group取得関数を設定
        /// </summary>
        public static void SetGroupGetterFunction(GroupFunction func)
        {
            _groupFunction = func;
            Cute.AssetBundle.BuildAssetBundle.SetGroupGetterFunction(func);
        }

        /// <summary>
        /// AssetBundleグループ指定関数を取得
        /// 関数名の指定は、「(Namespace.)ClassName.FunctionName」の書式で記述すること
        /// </summary>
        /// <returns>ファイルパスとAssetBundle名を引数にとり、グループを返す関数</returns>
        private static GroupFunction GetGroupFunction()
        {
            Debug.Assert(_groupFunction != null, "グループ取得関数未設定 : BuildAssetBundleExtensionで設定されているはず");
            return _groupFunction;
        }

        /// <summary>
        /// AssetBundle化対象判定関数を取得
        /// 関数名の指定は、「(Namespace.)ClassName.FunctionName」の書式で記述すること
        /// </summary>
        /// <returns>ファイルパスを引数にとり、AssetBundle化するかどうかを返す関数</returns>
        private static FilterFunction GetFilterFunction()
        {
            if (FILTER_FUNCTION_CACHE != null) { return FILTER_FUNCTION_CACHE; }

            var func = GetProjectPrefsFunction<FilterFunction>(AssetBundleFilter);
            if (func == null)
            {
                func = p => true;
            }
            FILTER_FUNCTION_CACHE = func;
            return func;
        }

        /// <summary>
        /// AssetBundleビルド対象一覧の取得関数を取得
        /// </summary>
        /// <returns>AssetBundleビルド対象一覧を取得する関数</returns>
        private static GetAssetBundleTargetArrayFunction GetFuncGetAssetBundleBuildTarget()
        {
            return GetProjectPrefsFunction<GetAssetBundleTargetArrayFunction>(PrefsKeyGetAssetBundleBuildTargetFunc);
        }

        /// <summary>ProjectPrefsの設定から関数を取得する</summary>
        private static T GetProjectPrefsFunction<T>(string prefsName) where T : class
        {
            var fullName = ProjectPrefs.GetString(prefsName);
            if (string.IsNullOrEmpty(fullName))
            {
                return null;
            }

            var separetIndex = fullName.LastIndexOf(".");
            if (separetIndex < 0)
            {
                return null;
            }

            var className = fullName.Substring(0, separetIndex);
            var functionName = fullName.Substring(separetIndex + 1);

            Type type = GetType(className);
            if (type == null)
            {
                throw new Exception("関数のクラスが見つかりません：" + className);
            }

            var methodInfo = type.GetMethod(functionName);
            if (methodInfo == null)
            {
                throw new Exception("関数が見つかりません. 名前があってるか確認してください：" + functionName);
            }

            Delegate function = Delegate.CreateDelegate(typeof(T), methodInfo);
            if (function == null || !IsSameMethod(function, methodInfo))
            {
                throw new Exception("関数が生成できませんでした. 引数の型があってるか確認してください：" + functionName);
            }

            return function as T;
        }

        /// <summary>デリゲートの引数があってるか</summary>
        private static bool IsSameMethod(Delegate function, MethodInfo method)
        {
            var invoke = function.GetType().GetMethod("Invoke");
            var p1 = invoke.GetParameters();
            var p2 = method.GetParameters();
            if (p1.Length != p2.Length)
            {
                return false;
            }

            for (int i = 0; i < p1.Length; i++)
            {
                if (p1[i].GetType() != p2[i].GetType())
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>クラス名からTypeを取得する</summary>
        private static Type GetType(string typeName)
        {
            foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                foreach (Type type in assembly.GetTypes())
                {
                    if (type.FullName == typeName)
                    {
                        return type;
                    }
                }

                foreach (Type type in assembly.GetTypes())
                {
                    if (type.Name == typeName)
                    {
                        return type;
                    }
                }
            }
            return null;
        }

        /// <summary>ビルド対象ファイルか判定</summary>
        private static bool IsValidFile(string path)
        {
            // ドットで始まるファイルは無視する
            if (Path.GetFileName(path).StartsWith("."))
            {
                return false;
            }

            // ドットで始まるフォルダに含まれていたら無視する
            if (IsDotStartDirectory(path))
            {
                return false;
            }

            if (path.EndsWith(".meta"))
            {
                return false;
            }

#if UNITY_EDITOR_WIN
            if (path.Equals("Thumbs.db"))
            {
                return false;
            }
#endif

            return true;
        }

        /// <summary>ドットで始まるフォルダが含まれているか判定</summary>
        private static bool IsDotStartDirectory(string path)
        {
            var directories = path.Split('/');
            foreach (var d in directories)
            {
                if (d.StartsWith("."))
                {
                    return true;
                }
            }
            return false;
        }

        private static void DeleteDirectory(string directory)
        {
            DirectoryInfo di = new DirectoryInfo(directory);

            RemoveReadonlyAttribute(di);

            di.Delete(true);
        }

        private static void RemoveReadonlyAttribute(DirectoryInfo directoryInfo)
        {
            if ((directoryInfo.Attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
            {
                directoryInfo.Attributes = FileAttributes.Normal;
            }
            foreach (FileInfo fi in directoryInfo.GetFiles())
            {
                if ((fi.Attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
                {
                    fi.Attributes = FileAttributes.Normal;
                }
            }
            foreach (DirectoryInfo di in directoryInfo.GetDirectories())
            {
                RemoveReadonlyAttribute(di);
            }
        }

        /// <summary>単一キーに対して複数の値をリストにして保持できるクラス</summary>
        class MultiValueMap<T> : Dictionary<string, List<T>>
        {
            public MultiValueMap() : base() { }
            public MultiValueMap(int capacity) : base(capacity) { }
            public void Add(string key, T value)
            {
                List<T> values;
                if (!TryGetValue(key, out values))
                {
                    values = new List<T>();
                    base.Add(key, values);
                }
                values.Add(value);
            }
        }

        /// <summary>Localized Asset の BaseAsset ファイルリストを生成</summary>
        private static MultiValueMap<AssetBundleName> GetBaseAssetList()
        {
            using (new Perf("GetBaseAssetList"))
            {
                var rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
                var filesInRootDirectory = Directory.GetFiles(rootDirectory, "*.*", SearchOption.AllDirectories);
                var dependencies = AssetDatabase.GetDependencies(Array.FindAll(filesInRootDirectory, p => IsValidFile(p)));
                System.Console.WriteLine("alldependencies count:" + dependencies.Length);
                var assetList = new MultiValueMap<AssetBundleName>(dependencies.Length);
                foreach (var dependency in dependencies)
                {
                    assetList.Add(Path.GetFileNameWithoutExtension(dependency), new AssetBundleName() { assetPath = dependency, baseAssetPath = dependency });
                }
                return assetList;
            }
        }

        /// <summary>variant情報収集</summary>
        private static Dictionary<string, MultiValueMap<VariantInfo>> CreateVariantInfoList(MultiValueMap<AssetBundleName> baseAssetDict)
        {
            using (new Perf("CheckVariantInfoList"))
            {
                string rootDirectory = ProjectPrefs.GetString(AssetBundleManager.AssetBundleRootDirectory);
                string variantsDirectory = ProjectPrefs.GetString(AssetBundleVariantsDirectory);

                var allVariants = new Dictionary<string, MultiValueMap<VariantInfo>>();

                if (string.IsNullOrEmpty(variantsDirectory))
                {
                    return allVariants;
                }

                var variantsDirectories = Directory.GetDirectories(variantsDirectory);
                foreach (var variantDirectory in variantsDirectories)
                {
                    var variantList = new MultiValueMap<VariantInfo>();

                    string variantName = variantDirectory.Replace(variantsDirectory, "").Replace("/", "");
                    var files = Directory.GetFiles(variantDirectory, "*.*", SearchOption.AllDirectories);
                    foreach (var f in files)
                    {
                        var path = f.Replace("\\", "/");
                        if (!IsValidFile(path)) continue;

                        string baseAssetPath = path.Replace(variantDirectory + "/", rootDirectory);
                        string baseAssetPathWithoutExtension = Core.Path.GetPathWithoutExtension(baseAssetPath);
                        List<AssetBundleName> baseAssetList;
                        if (!baseAssetDict.TryGetValue(Path.GetFileNameWithoutExtension(path), out baseAssetList))
                        {
                            //System.Console.WriteLine("Base asset is not found:" + path);
                            continue;
                        }
                        string baseAssetName = Path.GetFileName(baseAssetPath);
                        var baseAsset = baseAssetList.Find(a => a.assetPath == baseAssetPath);
                        if (baseAsset == null)
                        {
                            baseAsset = baseAssetList.Find(a => Core.Path.GetPathWithoutExtension(a.assetPath) == baseAssetPathWithoutExtension);
                            if (baseAsset == null)
                            {
                                baseAsset = baseAssetList.Find(a => Path.GetFileName(a.assetPath) == baseAssetName);
                                if (baseAsset == null)
                                {
                                    System.Console.WriteLine("Warning base asset is not found:" + path);
                                    continue;
                                }
                                System.Console.WriteLine("Warning base asset is assumed from file name:" + path);
                            }
                            else
                            {
                                System.Console.WriteLine("Warning base asset is assumed from no file extension:" + path);
                            }
                        }
                        variantList.Add(baseAsset.assetPath, new VariantInfo() { assetPath = path, baseAssetPath = baseAsset.assetPath });
                    }
                    allVariants.Add(variantName, variantList);
                }
                return allVariants;
            }
        }

        /// <summary>
        /// 「Base asset is duplicated」例外の事前チェック
        /// </summary>
        public static void CheckDuplicateBaseAsset()
        {
            // AssetBundle化対象ファイルリストを生成
            var allAssetList = GetBaseAssetList();
            System.Console.WriteLine("allAssetList count:" + allAssetList.Count);

            // variantリスト生成
            var variants = CreateVariantInfoList(allAssetList);

            foreach (var variant in variants)
            {
                foreach (var assets in variant.Value)
                {
                    // 重複してるものを出力
                    if (assets.Value.Count > 1)
                    {
                        var log = "Duplicated Base Asset:" + assets.Key + "\n";
                        foreach (var asset in assets.Value)
                        {
                            log += "        Variant Asset:" + asset.assetPath + "\n";
                        }
                        System.Console.WriteLine(log);
                    }
                }
            }
        }

#if GALLOP
        // GallopModify : Build時の動作を外部から設定できるように
        private static string[] _buildShaderFilePaths = null;

        private static Func<string, string> _bundleNameToCategoryFunc = null;

        // ---------------------------------------------
        //  アセバンをビルド時に必要なパラメータの設定
        //  Gallop.BuildAssetBundleExtensionの各関数から設定する想定
        // ---------------------------------------------

        /// <summary>
        /// ビルド対象のShaderリストを設定する
        /// </summary>
        /// <param name="shaderPaths"></param>
        public static void SetBuildShaderPaths(string[] shaderPaths)
        {
            _buildShaderFilePaths = shaderPaths;
            Cute.AssetBundle.BuildAssetBundle.SetBuildShaderPaths(shaderPaths);
        }

        /// <summary>
        /// ファイルパスをカテゴリ名に変換するための関数を登録
        /// </summary>
        public static void SetBundleNameToCategoryConvertFunction(System.Func<string, string> func)
        {
            _bundleNameToCategoryFunc = func;
        }

        /// <summary>
        /// ファイルパスからアセバンカテゴリ名を取得
        /// </summary>
        /// <param name="directory"></param>
        /// <returns></returns>
        public static string GetCategoryFromBundleName(string bundleName)
        {
            if (_bundleNameToCategoryFunc == null) return "";
            return _bundleNameToCategoryFunc(bundleName);
        }

        /// <summary>
        /// ビルド対象のShaderリストを取得
        /// </summary>
        /// <returns></returns>
        public static string[] GetBuildShaderPaths()
        {
            return _buildShaderFilePaths;
        }
#endif

        /// <summary>
        /// 依存関係アセットの取得処理を関数化
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public static string[] GetDependencyAssets(string path)
        {
            var dependencies = AssetDatabase.GetDependencies(path);
            return dependencies.Where(d =>
            {
                // 自身は含まない
                if (d == path) return false;
                // スクリプトはAssetBundleに含まれない
                var extension = Path.GetExtension(d);
                return (extension != ".cs" && extension != ".js" && GetFilterFunction()(d));
            }).ToArray();
        }
    }
}
