using UnityEngine;
using System.Collections;
using System;
using System.Collections.Generic;

namespace Cute.Payment
{
#if UNITY_ANDROID
    /// <summary>
    /// C#からJarファイルの各関数呼び出すの実装
    /// </summary>
    public class GoogleIABBinding
    {
        private static AndroidJavaObject _plugin;

        /// <summary>
        /// Constructor
        /// </summary>
        static GoogleIABBinding()
        {
            if (_plugin != null)
                return;
            using (var pluginClass = new AndroidJavaObject("jp.co.cygames.iabplugin.IABPlugin"))
                _plugin = pluginClass.Call<AndroidJavaObject>("instance");
        }

        /// <summary>
        /// STEP1 InAppBillingの初期化。
        /// </summary>
        public static void Init(string publicKey, Action onSuccess, Action<int, string> onError)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            _plugin.Call("init", new object[]{
                publicKey,
                new InitCallback(
                    onSuccess,
                    onError
                )
            });
        }

        /// <summary>
        /// STEP2 商品情報取得リクエスト
        /// </summary>
        public static void QueryInventory(string[] skus, Action<string> onSuccess, Action<int, string> onError, bool isSbus)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            if (isSbus)
            {
                _plugin.Call("querySubscription", new object[]
                {
                    skus,
                    new QueryInventoryCallback(
                        onSuccess,
                        onError
                    )
                });
            }
            else
            {
                _plugin.Call("queryInventory", new object[]
             {
                skus,
                new QueryInventoryCallback(
                    onSuccess,
                    onError
                )
             });
            }
        }

        /// <summary>
        /// STEP3 商品購入リクエスト
        /// </summary>
        public static void PurchaseProduct(string sku, Action<string> onSuccess, Action onCancel, Action<int, string> onError)
        {
            PurchaseProduct(sku, string.Empty, onSuccess, onCancel, onError);
        }

        public static void PurchaseProduct(string sku, string developerPayload, Action<string> onSuccess, Action onCancel, Action<int, string> onError)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            _plugin.Call("purchaseProduct", new object[] {
                 sku,
                 developerPayload,
                 new PurchaseCallback(
                    onSuccess,
                    onCancel,
                    onError
                 )
            });
        }

        /// <summary>
        /// STEP4 商品消費リクエスト
        /// 商品一回購入したら再度購入できない
        /// 商品消費リクエスト送信してGooglePlayStoreでの商品状態変更、再度購入を可能です
        /// </summary>
        public static void ConsumeProduct(string sku, Action<string> onSuccess, Action<int, string> onError)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            _plugin.Call("consumeProduct", new object[] {
                 sku,
                 new ConsumeCallback(
                    onSuccess,
                    onError
                 )
            });
        }

        /// <summary>
        /// STEP4 全商品消費リクエスト
        /// </summary>
        public static void ConsumeProducts(string[] skus, Action<string> onSuccess, Action<int, string> onError)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            _plugin.Call("consumeProducts", new object[] {
                 skus,
                 new ConsumeCallback(
                    onSuccess,
                    onError
                 )
            });
        }

        /// <summary>
        /// STEP5 Unbindサービス
        /// </summary>
        public static void UnbindService()
        {
            if (Application.platform != RuntimePlatform.Android)
                return;
            _plugin.Call("unbindService");
        }

        /// <summary>
        /// 未消費商品の取得
        /// </summary>
        public static void GetUnfinishPurchaseProcess( Action<string> onSuccess, Action onCancel, Action<int, string> onError)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            _plugin.Call("queryPurchases", new object[]
            {
               // skus,
                new PurchaseCallback(
                    onSuccess,
                    onCancel,
                    onError
                )
            });
        }

        /// <summary>
        /// PluginのLog出力のコントローラ
        /// UseDebugLog(true)ならPluginのLogを出力する。
        /// </summary>
        public static void UseDebugLog(bool useDebugLog)
        {
            if (Application.platform != RuntimePlatform.Android)
            {
                return;
            }
            _plugin.Call("useDebugLog", useDebugLog);
        }
    }
#endif
}
