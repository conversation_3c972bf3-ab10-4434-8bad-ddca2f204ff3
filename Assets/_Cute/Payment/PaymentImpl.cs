// ネイティブ関連コードを有効化します。
//#define CODE_DEVELOPMENT

#if !UNITY_EDITOR || CODE_DEVELOPMENT
#define NATIVE_ENVIRONMENT
#endif

#if UNITY_ANDROID && NATIVE_ENVIRONMENT
#define ANDROID_NATIVE_ENVIRONMENT
#endif

#if UNITY_IOS && NATIVE_ENVIRONMENT
#define IOS_NATIVE_ENVIRONMENT
#endif

using UnityEngine;
using System.Collections.Generic;
using Cute.Core;
using System;
using System.Collections;

namespace Cute.Payment
{
    /// <summary>
    /// ユーザー用AndroidとiOS統一API
    /// </summary>

    [ProjectPrefsAttr(PublicKey, "Google InAppBilling検証キー", "[Payment]")]
    public class PaymentImpl
    {

        public const string PublicKey = "PublicKey";
        
        private static PaymentImpl instance = null;
        public static PaymentImpl Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new PaymentImpl();
                }
                return instance;
            }
        }

#if ANDROID_NATIVE_ENVIRONMENT
        private enum IABErrorCode
        {
            IABErrorItemUnavailable = 4,            //Requested product is not available for purchase
            IABErrorDeveloperError = 5,             //Invalid arguments provided to the API.
            IABErrorFatalError = 6,                 //Fatal error during the API action.
            IABErrorItemAlreadyOwned = 7,           //Failure to purchase since item is already owned
            IABErrorRemoteException = -1001,
            IABErrorBadResult = -1002,
            IABErrorVerificationFailed = -1003,
            IABErrorSendIntentFailed = -1004,
            IABErrorUserCancelled = -1005,
            IABErrorUnKnownPurchaseResponse = -1006,
            IABErrorMissingToken = -1007,
            IABErrorUnknownError = -1008,
            IABErrorSubscriptionsNotAvailable = -1009,
            IABErrorInvalidConsumption = -1010,
        }
#elif UNITY_IOS
        private enum SKErrorCode
        {
            SKErrorUnknown = 0,
            SKErrorClientInvalid,               // client is not allowed to issue the request, etc.
            SKErrorPaymentCancelled,            // Objective C側で処理完了 ここに投げない
            SKErrorPaymentInvalid,              // purchase identifier was invalid, etc.
            SKErrorPaymentNotAllowed,           // this device is not allowed to make the payment
            SKErrorStoreProductNotAvailable     // Product is not available in the current storefront
        }
#endif

        private enum PaymentErrorCode
        {
            PaymentErrorUnknown = 0,
            PaymentErrorClientInvalid,                  // client is not allowed to issue the request, etc.
            PaymentErrorPaymentCancelled,               // user cancel
            PaymentErrorPaymentInvalid,                 // purchase identifier was invalid, etc.
            PaymentErrorPaymentNotAllowed,              // this device is not allowed to make the payment
            PaymentErrorStoreProductNotAvailable = 5,   // Product is not available in the current storefront
            PaymentErrorRemoteException,
            PaymentErrorBadResult,
            PaymentErrorVerificationFailed,
            PaymentErrorSendIntentFailed,
            PaymentErrorUnKnownPurchaseResponse = 10,
            PaymentErrorMissingToken,
            PaymentErrorSubscriptionsNotAvailable,
            PaymentErrorInvalidConsumption,
            PaymentErrorItemAlreadyOwned,
            PaymentErrorItemUnavailable = 15,           //Requested product is not available for purchase
            PaymentErrorDeveloperError,                 //Invalid arguments provided to the API.
            PaymentErrorFatalError,                     //Fatal error during the API action.
        }

        public int ErrorCodeHandler(int ErrorCode)
        {
            int errorCode = -1;
#if ANDROID_NATIVE_ENVIRONMENT
            switch (ErrorCode)
            {
                case (int)IABErrorCode.IABErrorRemoteException:
                    errorCode = (int)PaymentErrorCode.PaymentErrorRemoteException;
                    break;
                case (int)IABErrorCode.IABErrorBadResult:
                    errorCode = (int)PaymentErrorCode.PaymentErrorBadResult;
                    break;
                case (int)IABErrorCode.IABErrorVerificationFailed:
                    errorCode = (int)PaymentErrorCode.PaymentErrorVerificationFailed;
                    break;
                case (int)IABErrorCode.IABErrorSendIntentFailed:
                    errorCode = (int)PaymentErrorCode.PaymentErrorSendIntentFailed;
                    break;
                case (int)IABErrorCode.IABErrorUserCancelled:
                    errorCode = (int)PaymentErrorCode.PaymentErrorPaymentCancelled;
                    break;
                case (int)IABErrorCode.IABErrorUnKnownPurchaseResponse:
                    errorCode = (int)PaymentErrorCode.PaymentErrorUnKnownPurchaseResponse;
                    break;
                case (int)IABErrorCode.IABErrorMissingToken:
                    errorCode = (int)PaymentErrorCode.PaymentErrorMissingToken;
                    break;
                case (int)IABErrorCode.IABErrorSubscriptionsNotAvailable:
                    errorCode = (int)PaymentErrorCode.PaymentErrorSubscriptionsNotAvailable;
                    break;
                case (int)IABErrorCode.IABErrorInvalidConsumption:
                    errorCode = (int)PaymentErrorCode.PaymentErrorInvalidConsumption;
                    break;
                case (int)IABErrorCode.IABErrorUnknownError:
                    errorCode = (int)PaymentErrorCode.PaymentErrorUnknown;
                    break;
                case (int)IABErrorCode.IABErrorItemAlreadyOwned:
                    errorCode = (int)PaymentErrorCode.PaymentErrorItemAlreadyOwned;
                    break;
                case (int)IABErrorCode.IABErrorDeveloperError:
                    errorCode = (int)PaymentErrorCode.PaymentErrorDeveloperError;
                    break;
                case (int)IABErrorCode.IABErrorFatalError:
                    errorCode = (int)PaymentErrorCode.PaymentErrorFatalError;
                    break;
                case (int)IABErrorCode.IABErrorItemUnavailable:
                    errorCode = (int)PaymentErrorCode.PaymentErrorItemUnavailable;
                    break;
                default:
                    errorCode = ErrorCode;
                    break;
            }

#elif UNITY_IOS
            switch(ErrorCode)
            {
                case (int)SKErrorCode.SKErrorUnknown:
                    errorCode = (int)PaymentErrorCode.PaymentErrorUnknown;
                    break;
                case (int)SKErrorCode.SKErrorClientInvalid:
                    errorCode = (int)PaymentErrorCode.PaymentErrorClientInvalid;
                    break;
                case (int)SKErrorCode.SKErrorPaymentCancelled:
                    errorCode = (int)PaymentErrorCode.PaymentErrorPaymentCancelled;
                    break;
                case (int)SKErrorCode.SKErrorPaymentInvalid:
                    errorCode = (int)PaymentErrorCode.PaymentErrorPaymentInvalid;
                    break;
                case (int)SKErrorCode.SKErrorPaymentNotAllowed:
                    errorCode = (int)PaymentErrorCode.PaymentErrorPaymentNotAllowed;
                    break;
                case (int)SKErrorCode.SKErrorStoreProductNotAvailable:
                    errorCode = (int)PaymentErrorCode.PaymentErrorStoreProductNotAvailable;
                    break;
                default:
                    errorCode = ErrorCode;
                    break;
            }
#endif
            return errorCode;
        }

        public GameObject GetGameObject()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.gameObject;
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.gameObject;
#else
            return new GameObject("Payment");
#endif
        }

        public void InitializePurchaseSupport()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            if (string.IsNullOrEmpty(ProjectPrefs.GetString(PublicKey)))
            {
#if DEBUG
                Debug.LogWarning("Cute ProjectPrefsにPublic Keyを設定してください");
#endif
                return;
            }
            IABManager.Instance.CommonCallback.StartTimeCount();
            GoogleIABBinding.Init(ProjectPrefs.GetString(PublicKey), () =>
            {
                IABManager.Instance.BillingSupported("");
            }
            , (code, message) =>
            {
                IABManager.Instance.BillingNotSupported(errorMessage:message);
            });
#elif IOS_NATIVE_ENVIRONMENT
            StoreKitManager.Instance.CommonCallback.StartTimeCount();
            if (StoreKitBinding.CanMakePayments())
            {
                StoreKitManager.Instance.PaymentSupported();
            }
            else
            {
                StoreKitManager.Instance.PaymentNotSupported();
            }
#endif
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="productIDs">商品Idリスト</param>
        /// <param name="isSubs">subscriptonであるか</param>
        public void RequestItemDataFromStore(string[] productIDs, bool isSbus = false)
        {
            if (productIDs == null || productIDs.Length == 0)
            {
#if ANDROID_NATIVE_ENVIRONMENT
                if(IABManager.Instance.CommonCallback != null)
                {
                    IABManager.Instance.CommonCallback.OnGetProductListFailed((int)ResponseCode.ITEM_UNAVAILABLE, "商品IDリストは無効です");
                }
#elif IOS_NATIVE_ENVIRONMENT
                StoreKitManager.Instance.CommonCallback.OnGetProductListFailed((int)ResponseCode.ITEM_UNAVAILABLE,"商品IDリストは無効です");
#endif
                return;
            }
            
#if ANDROID_NATIVE_ENVIRONMENT
            if(IABManager.Instance.CommonCallback != null)
            {
                IABManager.Instance.CommonCallback.StartTimeCount();
            }
            GoogleIABBinding.QueryInventory(productIDs,
                (skus) =>
                {
                    //現在iabplugin-release.aarのUnitySendMessageより"QueryInventorySucceeded"を呼び出ししてる
                    IABManager.Instance.QueryInventorySucceeded(skus);
                },
                (code, message) =>
                {
                    IABManager.Instance.QueryInventoryFailed(errorMessage: message);
                },
                isSbus
            );
#elif IOS_NATIVE_ENVIRONMENT
            StoreKitManager.Instance.CommonCallback.StartTimeCount();
            StoreKitBinding.RequestProductData(productIDs);
#endif
        }

        public void PurchaseProduct(string productID)
        {
            if (string.IsNullOrEmpty(productID))
            {
#if ANDROID_NATIVE_ENVIRONMENT
                IABManager.Instance.CommonCallback.OnPurchaseFailed("商品IDは無効です");
#elif IOS_NATIVE_ENVIRONMENT
                StoreKitManager.Instance.CommonCallback.OnPurchaseFailed("商品IDは無効です");
#endif
                return;
            }
#if ANDROID_NATIVE_ENVIRONMENT
            IABManager.Instance.CommonCallback.StartTimeCount();
            GoogleIABBinding.PurchaseProduct(productID,
                (googlePurchase) =>
                {
                    IABManager.Instance.PurchaseSucceeded(googlePurchase);
                },
                () =>
                {
                    IABManager.Instance.PurchaseCancelled();
                },
                (code, message) =>
                {
                    IABManager.Instance.PurchaseFailed(message);
                }
            );
#elif IOS_NATIVE_ENVIRONMENT
            StoreKitManager.Instance.TransactionInitialize();
            StoreKitManager.Instance.CommonCallback.StartTimeCount();
            StoreKitBinding.purchaseProduct(productID);
#endif
        }
        /// <summary>
        ///  
        /// </summary>
        /// <param name="id">iOS用TransactionId</param>
        /// <param name="productId">Android用</param>
        public void FinishPurchase(string transactionId, string productId )
        {
            Debug.Log("FinishPurchase, productId:" + productId+", transactionId:" + transactionId);
#if ANDROID_NATIVE_ENVIRONMENT
            if (string.IsNullOrEmpty(productId))
            {
                IABManager.Instance.CommonCallback.OnConsumePurchaseFailed((int)ResponseCode.ERROR, "商品IDは無効です");
                return;
            }
            IABManager.Instance.CommonCallback.StartTimeCount();
            GoogleIABBinding.ConsumeProduct(
                productId,
                (purchase) =>
                {
                    IABManager.Instance.ConsumePurchaseSucceeded(purchase);
                },
                (code, message) =>
                {
                    IABManager.Instance.ConsumePurchaseFailed(message);
                }
            );
#elif IOS_NATIVE_ENVIRONMENT
            //サブスクの場合、リストアによる新しく複数のトランザクションIDsが発行される。
            //それらのトランザクションIDsをfinishする
            //パラメタのtransactionIdが過去のものです。新しく発行したトランザクションIDも過去のものも同じサブスク決済である
            string[] tranIds = StoreKitManager.Instance.GetTransIds().ToArray();
            if (tranIds.Length > 0)
            {
                StoreKitManager.Instance.StartCoroutine(FinishPendingTransactionById(tranIds));
            }
            else
            {
                //iOS FinishCallbackないので、ここはいらない
                //StoreKitManager.Instance.CommonCallback.StartTimeCount();
                if (!string.IsNullOrEmpty(transactionId))
                {
                    StoreKitBinding.FinishPendingTransactionById(transactionId);
                }
                else
                {
                    StoreKitManager.Instance.CommonCallback.OnConsumePurchaseFailed((int)ResponseCode.ERROR, "商品IDは無効です");
                    return;
                }
            }
            StoreKitManager.Instance.TransactionInitialize();
#endif
        }

        public void UseDebugLog(bool useDebugLog)
        {
#if ANDROID_NATIVE_ENVIRONMENT
            GoogleIABBinding.UseDebugLog(useDebugLog);
#elif IOS_NATIVE_ENVIRONMENT
            StoreKitBinding.UseDebugLog(useDebugLog);
#endif
        }

        public string GetTrasactionId()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.BuyingProductId;
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetTransactionId();
#else
            return string.Empty;
#endif
        }

        public int GetTransactionCount()
        {
#if IOS_NATIVE_ENVIRONMENT
            return StoreKitBinding.GetTransactionCount();
#else
            return 0;
#endif
        }

        public void GetUnfinishTransaction()
        {
#if IOS_NATIVE_ENVIRONMENT
            StoreKitBinding.GetUnfinishTransaction();
#endif
        }

        public void ForceFinishPendingTransaction()
        {
#if IOS_NATIVE_ENVIRONMENT
            StoreKitBinding.ForceFinishPendingTransaction();
#endif
        }



#if IOS_NATIVE_ENVIRONMENT
        public IEnumerator FinishPendingTransactionById(string[] transactionIds)
        {
            foreach (string transId in transactionIds)
            {
                //ちょっとずらさないと大量のできた場合、PFからレスポンス戻れなくなる
                yield return new WaitForSeconds(0.1f);
                UnityEngine.Debug.Log(string.Format("FinishPendingTransactionById time: {0:MM/dd/yyy HH:mm:ss.fff}",
                      System.DateTime.Now));
                StoreKitBinding.FinishPendingTransactionById(transId);
            }
        }
#endif


        public void RefreshReceipt()
        {
#if IOS_NATIVE_ENVIRONMENT
            StoreKitBinding.RefreshReceipt();
#endif
        }

        public void Initialize(IPaymentCommonCallback commonCallback)
        {
#if ANDROID_NATIVE_ENVIRONMENT
            IABManager.Instance.CommonCallback = commonCallback;
#elif IOS_NATIVE_ENVIRONMENT
            StoreKitManager.Instance.CommonCallback = commonCallback;
#endif
        }

        public void Deinitialize()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            GoogleIABBinding.UnbindService();
#endif
        }

        public string GetBuyingProductId()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.BuyingProductId;
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.BuyingProductId;
#else
            return string.Empty;
#endif
        }

        public void SetBuyingProductId(string productId)
        {
#if ANDROID_NATIVE_ENVIRONMENT
            IABManager.Instance.BuyingProductId = productId;
#elif IOS_NATIVE_ENVIRONMENT
            StoreKitManager.Instance.BuyingProductId = productId;
#endif
        }

        public string GetPrice(string productId)
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.GetPrice(productId);
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetPrice(productId);
#else
            return string.Empty;
#endif
        }

        public string GetPrice()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.GetPrice(IABManager.Instance.BuyingProductId);
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetPrice(StoreKitManager.Instance.BuyingProductId);
#else
            return string.Empty;
#endif
        }

        public string GetCurrencyCode(string productId)
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.GetCurrencyCode(productId);
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetCurrencyCode(productId);
#else
            return string.Empty;
#endif
        }

        public string GetCurrencyCode()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.GetCurrencyCode(IABManager.Instance.BuyingProductId);
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetCurrencyCode(StoreKitManager.Instance.BuyingProductId);
#else
            return string.Empty;
#endif
        }

        public string GetReceipt()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.GetReceipt();
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetReceipt();
#else
            return string.Empty;
#endif
        }

        public string GetSignature()
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.GetSignature();
#elif IOS_NATIVE_ENVIRONMENT
            return StoreKitManager.Instance.GetSignature();
#else
            return string.Empty;
#endif
        }

        /// <summary>
        /// 商品IDからgoogle reward(GooglePlayPoint)であるかを判別します。
        /// </summary>
        /// <param name="productId">string</param>
        /// <returns></returns>  google rewardであればtrue
        public bool IsGoogleReward(string productId)
        {
#if ANDROID_NATIVE_ENVIRONMENT
            return IABManager.Instance.IsGoogleReward(productId);
#else
            return false;
#endif
        }
    }

}