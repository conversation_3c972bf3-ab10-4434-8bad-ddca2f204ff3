using UnityEngine;
using System;
using System.Collections;
using Gallop;
using System.Collections.Generic;

namespace Sweat
{
    public class SweatParticle : MonoBehaviour
    {
        private const string LayerName = "TransparentFX";

        /// <summary>
        /// ループ再生時の再生タイミングで呼び出されるコールバック
        /// コールバックを設定した場合は再生のPlayも呼び出し側で行う必要がある
        /// </summary>
        public delegate void LoopAction();
        public LoopAction loopAction;

        protected GameObject _sweatParticleObject;
        protected ParticleSystem _sweatParticle;

        protected float _loopDeltaTime = 0.0f;
        protected bool _loop = false;
        protected float _loopMin = 0.0f;
        protected float _loopMax = 0.0f;
        protected float _loopTime = 0.0f;

        /// <summary>
        /// パーティクルオブジェクトの取得
        /// Initialize後で有効になる
        /// </summary>
        public ParticleSystem sweatParticle
        {
            get
            {
                return _sweatParticle;
            }
        }

        /// <summary>
        /// 最小のループ再生時間
        /// </summary>
        public float loopMin
        {
            get{ return _loopMin; }
            set { _loopMin = value; }
        }

        /// <summary>
        /// 最大のループ再生時間
        /// </summary>
        public float loopMax
        {
            get { return _loopMax; }
            set { _loopMax = value; }
        }

        /// <summary>
        /// ループ再生を行うか
        /// </summary>
        public bool loop
        {
            get { return _loop; }
            set { _loop = value; }
        }

        /// <summary>
        /// エフェクト再生中か
        /// </summary>
        public bool isPlay
        {
            get
            {
                if (_sweatParticle != null)
                {
                    return _sweatParticle.isPlaying;
                }
                return false;
            }
        }

        /// <summary>
        /// 初期化
        /// </summary>
        /// <param name="parentObject"></param>
        /// <param name="srcParticle"></param>
        public virtual void Initialize(GameObject sweatObjectPrefab)
        {
            if (_sweatParticle == null)
            {
                _sweatParticleObject = Instantiate<GameObject>(sweatObjectPrefab);
                SetLayer(_sweatParticleObject, LayerMask.NameToLayer(LayerName));
                _sweatParticle = _sweatParticleObject.GetComponent<ParticleSystem>();
            }
        }

        /// <summary>
        /// オブジェクトをアタッチする
        /// </summary>
        /// <param name="parentObject"></param>
        public void AttachTo(Transform parentObject)
        {
            if(_sweatParticleObject != null)
            {
                _sweatParticleObject.transform.SetParent(parentObject,false);
            }
        }

        /// <summary>
        /// エフェクトを無効にする
        /// </summary>
        public void Disable()
        {
            if(_sweatParticleObject != null)
            {
                _sweatParticleObject.SetActive(false);
                enabled = false;
            }
        }

        /// <summary>
        /// エフェクトを有効にする
        /// </summary>
        public void Enable()
        {
            if (_sweatParticleObject != null)
            {
                _sweatParticleObject.SetActive(true);
                enabled = true;
            }
        }

        /// <summary>
        /// エフェクトの再生
        /// </summary>
        public void Play()
        {
            _Play();
        }

        /// <summary>
        /// エフェクトの停止
        /// </summary>
        public void Stop()
        {
            _Stop();
        }

        /// <summary>
        /// 更新処理など
        /// </summary>
        protected virtual void Update()
        {
            if (_sweatParticleObject == null)
                return;

            if (loop)
            {
                _loopDeltaTime += Time.deltaTime;
                if (_loopDeltaTime >= _loopTime)
                {
                    if(loopAction != null)
                    {
                        loopAction();
                    }
                    else
                    {
                        Play();
                    }
                    //loopActionでPlayされなかった場合はloopTimeが更新されない
                    if (_loopTime > 0.0f)
                    {
                        _loopDeltaTime = _loopDeltaTime % _loopTime;
                    }
                }
            }
        }

        /// <summary>
        /// 内部エフェクト再生
        /// </summary>
        protected virtual void _Play()
        {
            if (_sweatParticle != null)
            {
                _sweatParticle.Play(true);
                _loopTime = UnityEngine.Random.Range(loopMin, loopMax);
            }
        }

        /// <summary>
        /// 内部エフェクト停止
        /// </summary>
        protected virtual void _Stop()
        {
            if (_sweatParticle != null)
            {
                _sweatParticle.Stop(true);
            }
        }

        /// <summary>
        /// 破棄など
        /// </summary>
        protected virtual void OnDestroy()
        {
            if (_sweatParticleObject != null)
            {
                Destroy(_sweatParticleObject);
                _sweatParticleObject = null;
                _sweatParticle = null;
            }
        }

        /// <summary>
        /// 子のレイヤーを一括で設定する
        /// </summary>
        /// <param name="gameObj"></param>
        /// <param name="layer"></param>
        protected void SetLayer(GameObject gameObj,int layer)
        {
            gameObj.layer = layer;
            for (int i=0;i< gameObj.transform.childCount;i++)
            {
                var child = gameObj.transform.GetChild(i);
                SetLayer(child.gameObject, layer);
            }
        }
    }
}