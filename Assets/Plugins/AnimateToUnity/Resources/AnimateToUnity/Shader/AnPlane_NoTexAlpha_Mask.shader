Shader "AnimateToUnity/Plane_NoTexAlpha_Mask" 
{	
	Properties
	{
		_MainTex("Color Texture", 2D) = "" {}
		_StencilComp("Stencil Comparison", Float) = 8
        _Stencil("Stencil ID", Float) = 0
        _StencilOp("Stencil Operation", Float) = 0
        _StencilWriteMask("Stencil Write Mask", Float) = 255
        _StencilReadMask("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 0
	}

	CGINCLUDE
	#define USE_STENCIL

	#include "UnityCG.cginc"
	#include "AnCommon.cginc"
	ENDCG


	SubShader 
	{
		Tags { "Queue" = "Transparent" "IgnoreProjector"="True" "RenderType"="Transparent"}
        Lighting Off
		Cull Off
        ZWrite On
		Offset 0, -10

		Stencil
		{
			Ref [_Stencil]
			Comp [_StencilComp]
			Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
		}

		ColorMask [_ColorMask] 

		Pass
		{
			CGPROGRAM
			#pragma vertex         UIVS
			#pragma fragment       UIPS
			#pragma fragmentoption ARB_precision_hint_fastest
			ENDCG 
		}
	}

	Fallback off
}