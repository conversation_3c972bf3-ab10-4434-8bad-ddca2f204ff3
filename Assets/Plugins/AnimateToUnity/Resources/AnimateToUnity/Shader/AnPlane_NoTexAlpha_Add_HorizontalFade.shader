Shader "AnimateToUnity/Plane_NoTexAlpha_Add_HorizontalFade"
{
	Properties
	{
		_MainTex("Color Texture", 2D) = "white" {}
		_uvColorInfo("_uvColorInfo", Vector) = (1, 1, 0, 0)
		_uvAlphaInfo("_uvAlphaInfo", Vector) = (1, 1, 0, 0)

		_StencilComp("Stencil Comparison", Float) = 8
		_Stencil("Stencil ID", Float) = 0
		_StencilOp("Stencil Operation", Float) = 0
		_StencilWriteMask("Stencil Write Mask", Float) = 255
		_StencilReadMask("Stencil Read Mask", Float) = 255

		_ColorMask("Color Mask", Float) = 15
		_AlphaParam("AlphaParam", Vector) = (0,0,0,0)
	}

		CGINCLUDE
#define USE_COLOROFFSET
#define USE_UVOFFSET
#define USE_TEXMAIN	
#define USE_VTXCOLOR
#define USE_STENCIL
#define USE_HORIZONTAL_SCROLL_FADE

#include "UnityCG.cginc"
#include "AnCommon.cginc"
			ENDCG

			SubShader
		{
			Tags {"Queue" = "Transparent" "IgnoreProjector" = "True" "RenderType" = "Transparent"}
			Lighting Off
			Blend SrcAlpha One, Zero One
			Cull Off
			ZWrite Off

			Stencil
			{
				Ref[_Stencil]
				Comp[_StencilComp]
				Pass[_StencilOp]
				ReadMask[_StencilReadMask]
				WriteMask[_StencilWriteMask]
			}

			ColorMask[_ColorMask]

			Pass
			{
				CGPROGRAM
				#pragma vertex         UIVS
				#pragma fragment       UIPS
				#pragma fragmentoption ARB_precision_hint_fastest
				ENDCG
			}
		}

			Fallback off
}
