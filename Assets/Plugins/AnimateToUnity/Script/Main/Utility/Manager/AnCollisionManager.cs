using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;

namespace AnimateToUnity.Utility
{
    public class AnCollisionManager
    {
        private const int COLLISION_LIST_NUM = 8;
        //=============================================================================================
        // Variables 
        //=============================================================================================

        private bool _exist = false;

        private Hashtable _rootTable = null;
        private Hashtable _objectTable = null;

        private List<object> _tempObjectList = null;

        private int _maxHitCount = 20;

        private RaycastHit[] _tempRaycastHitList = null;

        private List<RaycastHit> _tempObjectRaycastHitList = null;
        private List<AnObjectBase> _tempObjectRayHitObjectList = null;

        private List<RaycastHit> _tempCameraRaycastHitList = null;
        private List<AnObjectBase> _tempCameraRayHitObjectList = null;

        private Camera _tempTargetCamera = null;
        private Vector3 _tempNearPosition = Vector3.zero;
        private Vector3 _tempFarPosition = Vector3.zero;
        private Vector3 _tempDirection = Vector3.zero;
        private Vector3 _tempOrthogonalProjectionVector = Vector3.zero;
        private Vector3 _tempFixColliderPosition = Vector3.zero;

        //=============================================================================================
        // Method
        //=============================================================================================

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _Initialize()
        {
            _exist = false;

            _rootTable = new Hashtable(COLLISION_LIST_NUM);
            _objectTable = new Hashtable(COLLISION_LIST_NUM);

            _tempObjectList = new List<object>(COLLISION_LIST_NUM);

            _tempRaycastHitList = new RaycastHit[_maxHitCount];

            _tempObjectRaycastHitList = new List<RaycastHit>(COLLISION_LIST_NUM);
            _tempObjectRayHitObjectList = new List<AnObjectBase>(COLLISION_LIST_NUM);

            _tempCameraRaycastHitList = new List<RaycastHit>(COLLISION_LIST_NUM);
            _tempCameraRayHitObjectList = new List<AnObjectBase>(COLLISION_LIST_NUM);

            _exist = true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _AddRoot(AnRoot root)
        {
            if (root == null)
            {
                return;
            }

            if (root.ColliderTable.Count == 0)
            {
                return;
            }

            if (root.ObjectList.Count == 0)
            {
                return;
            }

            if (_ExistRoot(root))
            {
                return;
            }

            _rootTable.Add(root, root);

            foreach (AnObjectBase obj in root.ObjectList)
            {
                _AddObject(obj);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _AddObject(AnObjectBase targetObject)
        {
            if (targetObject.Collider == null)
            {
                return;
            }

            if (_ExistObject(targetObject))
            {
                return;
            }

            if (_objectTable.ContainsKey(targetObject.Collider))
            {
                return;
            }

            _objectTable.Add(targetObject.Collider, targetObject);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public bool _ExistRoot(AnRoot targetRoot)
        {
            if (_rootTable.ContainsKey(targetRoot))
            {
                return true;
            }

            return false;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public bool _ExistObject(AnObjectBase targetObject)
        {
            if (_objectTable.ContainsKey(targetObject.Collider))
            {
                return true;
            }

            return false;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _OptimizeAll()
        {
            if(!_exist)
            {
                return;
            }

            _Optimize();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _Optimize()
        {
            _tempObjectList.Clear();
            foreach (AnRoot root in _rootTable.Values)
            {
                if (root == null)
                {
                    continue;
                }

                if (root.gameObject == null)
                {
                    continue;
                }

                _tempObjectList.Add(root);
            }

            _rootTable.Clear();
            foreach (AnRoot root in _tempObjectList)
            {
                _rootTable.Add(root, root);
            }

            _tempObjectList.Clear();
            foreach (AnObjectBase objBase in _objectTable.Values)
            {
                if (objBase == null)
                {
                    continue;
                }

                if (objBase.GameObject == null)
                {
                    continue;
                }

                if (objBase.Collider == null)
                {
                    continue;
                }

                _tempObjectList.Add(objBase);
            }

            _objectTable.Clear();
            foreach (AnObjectBase obj in _tempObjectList)
            {
                _objectTable.Add(obj.Collider, obj);
            }

            _tempObjectList.Clear();
        }

        //************************************************
        // 
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _GetHitObjectListWithObjectRay(Vector3 objectRayPosition, Vector3 objectRayDirection, Vector3 objectRayUpDirection, float distance, int layerMask, float radius, bool useCameraRay, ref List<AnObjectBase> hitObjectList)
        {
            if (hitObjectList == null)
            {
                hitObjectList = new List<AnObjectBase>();
            }

            hitObjectList.Clear();

            _UpdateRaycastHitList(objectRayPosition, objectRayDirection, objectRayUpDirection, distance, layerMask, radius, ref _tempObjectRaycastHitList);

            if (!useCameraRay)
            {
                _GetHitObjectListFromRaycastHitList(_tempObjectRaycastHitList, ref hitObjectList);

                return;
            }

            for (int i = 0; i < _tempObjectRaycastHitList.Count; i++)
            {
                RaycastHit thisRaycastHit = _tempObjectRaycastHitList[i];

                if (thisRaycastHit.collider == null)
                {
                    continue;
                }

                if(!thisRaycastHit.collider.enabled)
                {
                    continue;
                }

                if (thisRaycastHit.collider.gameObject == null)
                {
                    continue;
                }

                if (!thisRaycastHit.collider.gameObject.activeInHierarchy)
                {
                    continue;
                }

                AnRootManager.Instance.UIManager.CameraManager._GetTargetCamera(thisRaycastHit.collider.gameObject, ref _tempTargetCamera);

                if(_tempTargetCamera == null)
                {
                    continue;
                }

                AnUtilityVector.GetOrthogonalProjectionVector(objectRayDirection, thisRaycastHit.collider.transform.position - objectRayPosition, ref _tempOrthogonalProjectionVector);

                _tempFixColliderPosition = objectRayPosition + _tempOrthogonalProjectionVector;

                _GetHitObjectListWithCameraRay(_tempTargetCamera, _tempFixColliderPosition, false, layerMask, ref _tempObjectRayHitObjectList);

                if (_tempObjectRayHitObjectList.Count == 0)
                {
                    continue;
                }

                AnObjectBase thisHitObject = _GetFirstHitObjectFromHitObjectList(_tempObjectRayHitObjectList, false);

                if (thisHitObject == null)
                {
                    continue;
                }

                hitObjectList.Add(thisHitObject);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _GetHitObjectListWithCameraRay(Camera targetCamera ,Vector3 targetPosition, bool targetPositionIsScreen, int layerMask, ref List<AnObjectBase> hitObjectList)
        {
            if (hitObjectList == null)
            {
                hitObjectList = new List<AnObjectBase>();
            }

            hitObjectList.Clear();

            if (targetCamera == null)
            {
                return;
            }

            if (!targetCamera.enabled)
            {
                return;
            }

            if (!targetCamera.gameObject.activeInHierarchy)
            {
                return;
            }

            _tempNearPosition = targetPosition;
            _tempFarPosition = targetPosition;

            if (targetPositionIsScreen)
            {
                _tempNearPosition.z = targetCamera.nearClipPlane;
                _tempNearPosition = targetCamera.ScreenToWorldPoint(_tempNearPosition);

                _tempFarPosition.z = targetCamera.farClipPlane;
                _tempFarPosition = targetCamera.ScreenToWorldPoint(_tempFarPosition);
            }
            else
            {
                _tempNearPosition = targetCamera.WorldToScreenPoint(_tempNearPosition);
                _tempNearPosition.z = targetCamera.nearClipPlane;
                _tempNearPosition = targetCamera.ScreenToWorldPoint(_tempNearPosition);
            }

            _tempDirection = _tempFarPosition - _tempNearPosition;
            _tempDirection.Normalize();

            _UpdateRaycastHitList(_tempNearPosition, _tempDirection, Vector3.up, float.MaxValue, layerMask, 0.0f, ref _tempCameraRaycastHitList);

            _GetHitObjectListFromRaycastHitList(_tempCameraRaycastHitList, ref _tempCameraRayHitObjectList);

            if (_tempCameraRayHitObjectList == null)
            {
                return;
            }

            if (_tempCameraRayHitObjectList.Count == 0)
            {
                return;
            }

            hitObjectList.AddRange(_tempCameraRayHitObjectList);

            _SortHitObjectList(ref hitObjectList);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _GetHitObjectListWithCameraRay(Vector3 targetPosition, bool targetPositionIsScreen, ref List<AnObjectBase> hitObjectList)
        {
            if (hitObjectList == null)
            {
                hitObjectList = new List<AnObjectBase>();
            }

            hitObjectList.Clear();
            
            // #91511 のnanが入っていることがある対応（ScreenToWorldPointにnanが入っている状態で渡すとエラーになる）
            if (float.IsNaN(targetPosition.x) || float.IsNaN(targetPosition.y))
            {
                return;
            }
            
            for (int i = 0; i < AnRootManager.Instance.UIManager.CameraManager.ActiveCameraList.Count; i++)
            {
                Camera targetCamera = AnRootManager.Instance.UIManager.CameraManager.ActiveCameraList[i];

                _tempNearPosition = targetPosition;
                _tempFarPosition = targetPosition;

                if (targetPositionIsScreen)
                {
                    _tempNearPosition.z = targetCamera.nearClipPlane;
                    _tempNearPosition = targetCamera.ScreenToWorldPoint(_tempNearPosition);

                    _tempFarPosition.z = targetCamera.farClipPlane;
                    _tempFarPosition = targetCamera.ScreenToWorldPoint(_tempFarPosition);
                }
                else
                {
                    _tempNearPosition = targetCamera.WorldToScreenPoint(_tempNearPosition);
                    _tempNearPosition.z = targetCamera.nearClipPlane;
                    _tempNearPosition = targetCamera.ScreenToWorldPoint(_tempNearPosition);
                }

                _tempDirection = _tempFarPosition - _tempNearPosition;
                _tempDirection.Normalize();

                _UpdateRaycastHitList(_tempNearPosition, _tempDirection, Vector3.up, float.MaxValue, targetCamera.cullingMask, 0.0f, ref _tempCameraRaycastHitList);

                _GetHitObjectListFromRaycastHitList(_tempCameraRaycastHitList, ref _tempCameraRayHitObjectList);

                if (_tempCameraRayHitObjectList == null)
                {
                    continue;
                }

                if (_tempCameraRayHitObjectList.Count == 0)
                {
                    continue;
                }

                hitObjectList.AddRange(_tempCameraRayHitObjectList);
            }

            _SortHitObjectList(ref hitObjectList);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateRaycastHitList(Vector3 position, Vector3 direction, Vector3 upVector, float distance, int layerMask, float radius, ref List<RaycastHit> raycastHitList)
        {
            if (raycastHitList == null)
            {
                raycastHitList = new List<RaycastHit>();
            }

            raycastHitList.Clear();

            if (layerMask == 0)
            {
                return;
            }

            _UpdateRaycastHitList(position, direction, upVector, distance, 0.0f, layerMask, ref raycastHitList);

            if (radius > 0)
            {
                _UpdateRaycastHitList(position, direction, upVector + direction, distance, radius, layerMask, ref raycastHitList);
                _UpdateRaycastHitList(position, direction, upVector + direction, distance, -radius, layerMask, ref raycastHitList);
            }

            raycastHitList.Sort((t1,t2) => _CompareFuncForRaycastHit(t1,t2));
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateRaycastHitList(Vector3 position, Vector3 direction, Vector3 upVector, float distance, float upDistance, int layerMask, ref List<RaycastHit> raycastHitList)
        {
            if (raycastHitList == null)
            {
                raycastHitList = new List<RaycastHit>();
            }

            raycastHitList.Clear();

            if(layerMask == 0)
            {
                return;
            }

            direction.Normalize();
            upVector.Normalize();

            int hitCount = 0;
            if (layerMask > 0)
            {
                hitCount = Physics.RaycastNonAlloc(position + upVector * upDistance, direction, _tempRaycastHitList, distance, layerMask);
            }
            else if (layerMask < 0)
            {
                hitCount = Physics.RaycastNonAlloc(position + upVector * upDistance, direction, _tempRaycastHitList, distance);
            }

            if (hitCount == 0)
            {
                return;
            }

            for (int i = 0; i < hitCount; i++)
            {
                RaycastHit thisHit = _tempRaycastHitList[i];

                if (thisHit.collider == null)
                {
                    continue;
                }

                if (!thisHit.collider.enabled)
                {
                    continue;
                }

                if (thisHit.collider.gameObject == null)
                {
                    continue;
                }

                if (!thisHit.collider.gameObject.activeInHierarchy)
                {
                    continue;
                }

                raycastHitList.Add(thisHit);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private static int _CompareFuncForRaycastHit(RaycastHit first, RaycastHit second)
        {
            if (first.collider == null)
            {
                if (second.collider == null)
                {
                    return -1;
                }

                return 1;
            }
            else
            {
                if (second.collider == null)
                {
                    return -1;
                }
            }

            if (first.distance < second.distance)
            {
                return -1;
            }
            else if (first.distance > second.distance)
            {
                return 1;
            }

            return 0;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _GetHitObjectListFromRaycastHitList(List<RaycastHit> raycastHitList, ref List<AnObjectBase> hitObjectList)
        {
            if (hitObjectList == null)
            {
                hitObjectList = new List<AnObjectBase>();
            }

            hitObjectList.Clear();

            for (int i = 0; i < raycastHitList.Count; i++)
            {
                RaycastHit thisHit = raycastHitList[i];

                if (thisHit.collider == null)
                {
                    continue;
                }

                if (!thisHit.collider.enabled)
                {
                    continue;
                }

                if (thisHit.collider.gameObject == null)
                {
                    continue;
                }

                if (!thisHit.collider.gameObject.activeInHierarchy)
                {
                    continue;
                }

                if (!_objectTable.ContainsKey(thisHit.collider))
                {
                    continue;
                }

                AnObjectBase tempObj = _objectTable[thisHit.collider] as AnObjectBase;

                if (tempObj == null)
                {
                    continue;
                }

                hitObjectList.Add(tempObj);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _SortHitObjectList(ref List<AnObjectBase> hitObjectList)
        {
            if (hitObjectList == null)
            {
                return;
            }

            if (hitObjectList.Count <= 1)
            {
                return;
            }

            hitObjectList.Sort((p1,p2) => _CompareFuncForHitObject(p1,p2));
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private static int _CompareFuncForHitObject(AnObjectBase first, AnObjectBase second)
        {
            if (first == null)
            {
                if (second == null)
                {
                    return -1;
                }

                return 1;
            }
            else
            {
                if (second == null)
                {
                    return -1;
                }
            }

            int layerDif = AnRootManager.Instance.UIManager.CameraManager._GetLayerPriority(1 << second.GameObject.layer) - AnRootManager.Instance.UIManager.CameraManager._GetLayerPriority(1 << first.GameObject.layer);

            if (layerDif != 0)
            {
                return layerDif;
            }

            if (first.SortLayerName == null)
            {
                if (second.SortLayerName == null)
                {
                    return 0;
                }

                return 1;
            }
            else
            {
                if (second.SortLayerName == null)
                {
                    return -1;
                }
            }

            int sortLayerSort = AnRootManager.Instance._GetSortingLayerIndex(second.SortLayerName) - AnRootManager.Instance._GetSortingLayerIndex(first.SortLayerName);

            if (sortLayerSort != 0)
            {
                return sortLayerSort;
            }

            int sortOffsetDif = second.SortOrder - first.SortOrder;

            if (sortOffsetDif != 0)
            {
                return sortOffsetDif;
            }

            float posZDif = second.GameObject.transform.position.z - first.GameObject.transform.position.z;

            if (posZDif > 0)
            {
                return -1;
            }

            return 1;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnUIBase _GetUIFromHitObject(AnObjectBase hitObject)
        {
            if (hitObject == null)
            {
                return null;
            }

            for (int i = 0; i < AnRootManager.Instance.UIManager.UIBaseManager._UIBaseList.Count; i++)
            {
                AnUIBase thisUI = AnRootManager.Instance.UIManager.UIBaseManager._UIBaseList[i] as AnUIBase;

                if (thisUI == null)
                {
                    continue;
                }

                if (thisUI.HitAreaObject != hitObject)
                {
                    continue;
                }

                return thisUI;
            }

            return null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _GetUIListFromHitObjectList(List<AnObjectBase> hitObjectList, ref List<AnUIBase> resultUIList)
        {
            if (resultUIList == null)
            {
                resultUIList = new List<AnUIBase>();
            }

            resultUIList.Clear();

            if (hitObjectList == null)
            {
                return;
            }

            for (int i = 0; i < hitObjectList.Count; i++)
            {
                AnUIBase thisUI = _GetUIFromHitObject(hitObjectList[i]);

                if(thisUI == null)
                {
                    continue;
                }

                resultUIList.Add(thisUI);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnUIBase _GetFirstUIListFromHitObjectList(List<AnObjectBase> hitObjectList, bool useSubCollider)
        {
            if (hitObjectList == null)
            {
                return null;
            }

            AnObjectBase hitObject = _GetFirstHitObjectFromHitObjectList(hitObjectList, useSubCollider);

            if (hitObject == null)
            {
                return null;
            }

            AnUIBase targetUI = _GetUIFromHitObject(hitObject);

            if (targetUI == null)
            {
                return null;
            }

            return targetUI;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnObjectBase _GetFirstHitObjectFromHitObjectList(List<AnObjectBase> hitObjectList, bool useSubCollider)
        {
            if (hitObjectList == null)
            {
                return null;
            }

            for (int i = 0; i < hitObjectList.Count; i++)
            {
                AnObjectBase hitObject = hitObjectList[i];

                if (hitObject.Collider == null)
                {
                    continue;
                }

                if (!hitObject.Collider.enabled)
                {
                    continue;
                }

                if (hitObject.ColliderThrough)
                {
                    continue;
                }

                if (!useSubCollider)
                {
                    return hitObject;
                }

                if (hitObject.ExistSubCollider == 0)
                {
                    return hitObject;
                }
                else if (hitObject.ExistSubCollider == 1)
                {
                    if (hitObject.SubCollider == null)
                    {
                        continue;
                    }

                    if (!hitObject.SubCollider.enabled)
                    {
                        continue;
                    }

                    AnObjectBase subHitObject = _GetHitObjectFromHitObjectListByCollider(hitObject.SubCollider, hitObjectList);

                    if (subHitObject == null)
                    {
                        continue;
                    }

                    return hitObject;
                }
            }

            return null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnObjectBase _GetHitObjectFromHitObjectListByCollider(Collider targetCollider, List<AnObjectBase> hitObjectList)
        {
            if (hitObjectList == null)
            {
                return null;
            }

            if (hitObjectList.Count == 0)
            {
                return null;
            }

            if (targetCollider == null)
            {
                return null;
            }

            if (!targetCollider.enabled)
            {
                return null;
            }

            for (int i = 0; i < hitObjectList.Count; i++)
            {
                AnObjectBase hitObject = hitObjectList[i];

                if (hitObject.Collider == null)
                {
                    continue;
                }

                if (!hitObject.Collider.enabled)
                {
                    continue;
                }

                if (targetCollider == hitObject.Collider)
                {
                    if (hitObject.SubCollider != null)
                    {
                        AnObjectBase subHit = _GetHitObjectFromHitObjectListByCollider(hitObject.SubCollider, hitObjectList);

                        if (subHit == null)
                        {
                            continue;
                        }
                    }

                    return hitObject;
                }

                if (hitObject.ColliderThrough)
                {
                    continue;
                }

                if (hitObject.SubCollider != null)
                {
                    continue;
                }

                break;
            }

            return null;
        }
    }
}
