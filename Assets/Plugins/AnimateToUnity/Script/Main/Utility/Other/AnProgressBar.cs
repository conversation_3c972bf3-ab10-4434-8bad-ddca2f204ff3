using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;

namespace AnimateToUnity.Utility
{
    public class AnProgressBar : AnUIBase
    {
        //=============================================================================================
        // Variables
        //=============================================================================================

        protected string _barMotionPath = "";
        protected AnMotion _barMotion = null;

        protected float _value = 0;

        protected float _minValue = 0;
        protected float _maxValue = 100;

        protected float _blendTime = 0.5f;

        protected AnBlendValue _blendValue = null;

        protected float _prevValue = int.MinValue;
        
        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read only
        /// </summary>
        public AnProgressBarComponent Component
        {
            get { return _component as AnProgressBarComponent; }
        }

        /// <summary>
		/// Read only
		/// </summary>
		public AnMotion BarMotion
        {
            get { return _barMotion; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float Value
        {
            get { return _value; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float CurrentValue
        {
            get { return _blendValue.CurrentValue; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float MinValue
        {
            get { return _minValue; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float MaxValue
        {
            get { return _maxValue; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float BlendTime
        {
            get { return _blendTime; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public AnBlendValue BlendValue
        {
            get { return _blendValue; }
        }

        public Action ActionValueChangeStart { get; set; }
        public Action ActionValueChangeLoop { get; set; }
        public Action ActionValueChangeEnd { get; set; }

        public AnAction FlActionValueChangeStart { get; protected set; }
        public AnAction FlActionValueChangeLoop { get; protected set; }
        public AnAction FlActionValueChangeEnd { get; protected set; }

        //=============================================================================================
        // Constructor
        //=============================================================================================

        public AnProgressBar() : base()
        {
            _logTitle = "UI ProgressBar";
        }

        //=============================================================================================
        // Method
        //=============================================================================================

        //************************************************
        // Initialize
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetOtherPath(string barMotionPath)
        {
            AnUtilityString.ReplaceString(barMotionPath, ref _barMotionPath);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override bool _InitializeThisData()
        {
            base._InitializeThisData();

            _barMotion = null;

            if (!AnUtilityString.IsEmptyString(_barMotionPath))
            {
                _barMotion = _root.Find<AnMotion>(_motion.GameObject, _barMotionPath);
            }

            if (_barMotion == null)
            {
                _barMotion = _motion;
            }

            _barMotion.SetResetModeType(AnMotion.ResetModeTypes.None);
            _barMotion.SetMotionStop();
            _barMotion.SetMotionPause(0);

            return true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _InitializeThisData_PostProcess()
        {
            base._InitializeThisData_PostProcess();

            _blendValue = new AnBlendValue(0, 0, 0.5f, AnBlendBase.BlendTypes.Down);

            FlActionValueChangeStart = _AddAction();
            FlActionValueChangeLoop = _AddAction();
            FlActionValueChangeEnd = _AddAction();
        }

        //************************************************
        // Release
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// Do not use
        /// </summary>
        /// -----------------------------------------
        public override void _Release()
        {
            if (!_exist)
            {
                return;
            }

            _blendValue = null;

            base._Release();
        }
        
        //************************************************
        //
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _InitializeValueChange()
        {
            base._InitializeValueChange();

            if (_animationFlag)
            {
                _blendValue.SetStartValue(_blendValue.CurrentValue);
            }
            else
            { 
                _blendValue.SetStartValue(_value);
            }

            _blendValue.SetEndValue(_value);
            _blendValue.SetBlendTime(_blendTime);
            _blendValue.Reset();

            _ResetPrevValue();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateValueChange()
        {
            base._UpdateValueChange();

            if (_minValue >= _maxValue)
            {
                _minValue = _maxValue;
            }

            AnUtilityValue.LimitValue(ref _value, _minValue, _maxValue);

            if (_animationFlag)
            {
                if (_updateFlag)
                {
                    _blendValue.Update(AnRootManager.Instance._currentDeltaTime);
                }
                else
                {
                    _blendValue.Update(0);
                }

                if (_minValue != _maxValue)
                {
                    _barMotion.SetMotionPause((_blendValue.CurrentValue - _minValue) / (_maxValue - _minValue) * _barMotion.CurrentLabelTimeLength);
                }
                else
                {
                    _barMotion.SetMotionPause(_barMotion.CurrentLabelTimeLength);
                }

                if (_blendValue.CurrentBlendValue >= 1.0f)
                {
                    _value = _blendValue.CurrentValue;

                    _animationFlag = false;
                }
            }
            else
            {
                if (_value == _prevValue)
                {
                    return;
                }

                if (_minValue != _maxValue)
                {
                    _barMotion.SetMotionPause((_value - _minValue) / (_maxValue - _minValue) * _barMotion.CurrentLabelTimeLength);
                }
                else
                {
                    _barMotion.SetMotionPause(_barMotion.CurrentLabelTimeLength);
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdatePrevValueChange()
        {
            base._UpdatePrevValueChange();

            _prevValue = _value;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _ResetPrevValue()
        {
            base._ResetPrevValue();
#if GALLOP
            _prevValue = int.MinValue;
#endif
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateValueChangeStart()
        {
            base._UpdateValueChangeStart();

            _ExecuteAction(ActionValueChangeStart, FlActionValueChangeStart);

            _SetLog(AnLogTypes.ValueChangeStart);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateValueChangeLoop()
        {
            base._UpdateValueChangeLoop();

            _ExecuteAction(ActionValueChangeLoop, FlActionValueChangeLoop);

            if(!_animationFlag)
            {
                _currentValueCnageState = AnCommonStateTypes.End;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateValueChangeEnd()
        {
            base._UpdateValueChangeEnd();
            
            _ExecuteAction(ActionValueChangeEnd, FlActionValueChangeEnd);

            _SetLog(AnLogTypes.ValueChangeEnd);
        }

        //************************************************
        // 
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetValue(float value)
        {
            SetValue(value, false, false);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetValue(float value, bool animation)
        {
            SetValue(value, animation, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetValue(float value, bool animation, bool executeAction)
        {
            _value = value;

            _animationFlag = animation;

            _initializeValueChangeFlag = true;

            _executeValueChangeActionFlag = executeAction;

            _ResetPrevValue();

            _UpdateForce();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetRange(float minValue, float maxValue)
        {
            _minValue = minValue;
            _maxValue = maxValue;

            _ResetPrevValue();

            _UpdateForce();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetBlendTime(float time)
        {
            _blendTime = time;

            _ResetPrevValue();

            _UpdateForce();
        }
    }
}
