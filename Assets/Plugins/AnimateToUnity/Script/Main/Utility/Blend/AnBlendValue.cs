using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace AnimateToUnity.Utility
{
    public class AnBlendValue : AnBlendBase
    {
        //=============================================================================================
        // Variables
        //=============================================================================================

        private float _startValue = 0;
        private float _endValue = 0;
        private float _currentValue = 0;

        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read only
        /// </summary>
        public float StartValue
        {
            get { return _startValue; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float EndValue
        {
            get { return _endValue; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float CurrentValue
        {
            get
            {
                UpdateCurrentValue();
                return _currentValue;
            }
        }

        //=============================================================================================
        // Constructor
        //=============================================================================================

        public AnBlendValue(float startValue, float endValue, float blendTime, BlendTypes blendModeType)
        {
            _startValue = startValue;
            _endValue = endValue;
            _blendTime = blendTime;
            _blendType = blendModeType;

            Reset();
        }

        //=============================================================================================
        // Method
        //=============================================================================================

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void Reset()
        {
            base.Reset();

            _currentValue = _startValue;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void Update(float deltaTime)
        {
            base.Update(deltaTime);

            UpdateCurrentValue();

            if (_pause)
            {
                return;
            }

            if (_currentBlendTime <= _blendTime)
            {
                _currentBlendTime += deltaTime;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void UpdateCurrentValue()
        {
            if (_startValue == _endValue)
            {
                _currentBlendValue = 1.0f;
                _currentFixBlendValue = 1.0f;

                _currentValue = _endValue;
                return;
            }

            if (_blendTime == 0)
            {
                _currentBlendValue = 1.0f;
                _currentFixBlendValue = 1.0f;

                _currentValue = _endValue;
                return;
            }

            if (_currentBlendTime > _blendTime + 0.2f || _currentBlendTime < 0.0f)
            {
                _currentBlendValue = 1.0f;
                _currentFixBlendValue = 1.0f;

                _currentValue = _endValue;
                return;
            }

            if (_pause)
            {
                return;
            }

            _currentValue = _startValue + (_endValue - _startValue) * _currentFixBlendValue;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void SetStartValue(float startValue)
        {
            _startValue = startValue;
        }

        /// -----------------------------------------
        /// <summary>
        ///
        /// </summary>
        /// -----------------------------------------
        public virtual void SetEndValue(float endValue)
        {
            _endValue = endValue;
        }
    }
}
