using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;

namespace AnimateToUnity.Utility
{
    public class AnCheckButtonComponent : AnComponentBase
    {
        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read only
        /// </summary>
        public AnCheckButton CheckButton
        {
            get { return _uiBase as AnCheckButton; }
        }
    }
}
