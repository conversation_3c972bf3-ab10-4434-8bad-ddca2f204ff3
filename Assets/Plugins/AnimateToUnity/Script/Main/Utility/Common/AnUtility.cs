using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;

namespace AnimateToUnity.Utility
{
    public class AnUtility
    {
        //=============================================================================================
        // Method
        //=============================================================================================

        // --------------------------------------------------------------------
        /// <summary>
        /// Get root
        /// </summary>
        /// --------------------------------------------------------------------
        public static AnRoot GetRoot(GameObject rootObject, bool fromChildren = false)
        {
            AnRoot root = null;

            if (!fromChildren)
            {
                root = rootObject.GetComponentInParent<AnRoot>();

                if (root != null)
                {
                    return root;
                }

                root = rootObject.GetComponentInChildren<AnRoot>();

                return root;
            }

            root = rootObject.GetComponentInChildren<AnRoot>();

            if (root != null)
            {
                return root;
            }

            root = rootObject.GetComponentInParent<AnRoot>();

            return root;
        }

        // --------------------------------------------------------------------
        /// <summary>
        /// Find gameobject
        /// </summary>
        /// <example>
        /// Find(obj,"MOT_object00/PLN_object00",false);
        /// </example>
        /// --------------------------------------------------------------------
        public static GameObject Find(GameObject rootObject, string path, bool fullMatch = false)
        {
            if (path == "" || path == null)
            {
                return rootObject;
            }

            string[] pathSplit = path.Split(new string[1] { "/" }, System.StringSplitOptions.None);

            GameObject rootGameObject = rootObject;

            for (int i = 0; i < pathSplit.Length; i++)
            {
                string pathName = pathSplit[i];

                rootGameObject = FindLoop(rootGameObject, pathName, 10, fullMatch);

                if (rootGameObject == null)
                {
                    break;
                }
            }

            return rootGameObject;
        }

        /// --------------------------------------------------------------------
        /// <summary>
        /// Find gameobject with type
        /// </summary>
        /// <example>
        /// Find&lt;transform&gt;(obj,"MOT_object00/PLN_object00",false);
        /// </example>
        /// --------------------------------------------------------------------
        public static T Find<T>(GameObject rootObject, string path, bool fullMatch = false) where T : Component
        {
            if (path == "" || path == null)
            {
                T comp = rootObject.GetComponent<T>();

                return comp;
            }

            string[] pathSplit = path.Split(new string[1] { "/" }, System.StringSplitOptions.None);

            GameObject rootGameObject = rootObject;

            for (int i = 0; i < pathSplit.Length; i++)
            {
                string pathName = pathSplit[i];

                rootGameObject = FindLoop(rootGameObject, pathName, 10, fullMatch);

                if (rootGameObject == null)
                {
                    break;
                }
            }

            if (rootGameObject == null)
            {
                return null;
            }

            return rootGameObject.GetComponent<T>();
        }

        /// --------------------------------------------------------------------
        /// <summary>
        /// Find flatout class with type
        /// </summary>
        /// <example>
        /// Find&lt;FlMotion&gt;(obj,"MOT_object00/MOT_object10",false);
        /// </example>
        /// --------------------------------------------------------------------
        public static T FindUI<T>(AnRoot flRoot, GameObject rootObject, string path, bool fullMatch = false) where T : AnBase
        {
            GameObject target = Find(rootObject, path, fullMatch);

            if (target == null)
            {
                return null;
            }

            if (!flRoot.DataTable.ContainsKey(target))
            {
                return null;
            }

            T result = flRoot.DataTable[target] as T;

            if (result == null)
            {
                return null;
            }

            return result;
        }

        /// --------------------------------------------------------------------
        /// <summary>
        /// Find loop
        /// </summary>
        /// --------------------------------------------------------------------
        private static GameObject FindLoop(GameObject rootObject, string name, int searchDepth, bool fullMatch)
        {
            GameObject result = null;
            bool findTarget = false;

            if (searchDepth < 0)
            {
                return null;
            }

            if (!fullMatch)
            {
                if (rootObject.name.Contains(name))
                {
                    return rootObject;
                }
            }
            else
            {
                if (rootObject.name == name)
                {
                    return rootObject;
                }
            }

            foreach (Transform trans in rootObject.transform)
            {
                if (trans == rootObject.transform)
                {
                    continue;
                }

                if (!fullMatch)
                {
                    if (trans.name.Contains(name))
                    {
                        result = trans.gameObject;
                        findTarget = true;
                        break;
                    }
                }
                else
                {
                    if (trans.name == name)
                    {
                        result = trans.gameObject;
                        findTarget = true;
                        break;
                    }
                }
            }

            if (findTarget)
            {
                return result;
            }

            foreach (Transform trans in rootObject.transform)
            {
                if (rootObject.transform == trans)
                {
                    continue;
                }

                result = FindLoop(trans.gameObject, name, searchDepth - 1, fullMatch);

                if (result != null)
                {
                    break;
                }
            }

            return result;
        }

        /// --------------------------------------------------------------------
        /// <summary>
        /// Get object path
        /// </summary>
        /// --------------------------------------------------------------------
        public static string GetObjectPath(GameObject target, GameObject rootObject, bool withoutUIObj = false, bool start = true)
        {
            if (target == null)
            {
                return "";
            }

            string result = target.name;

            if (target.transform.parent != null)
            {
                //Search parent hierarachy
                GameObject parentObject = target.transform.parent.gameObject;

                string frontName = target.name;
                if (target.name.Length > 5)
                {
                    frontName = target.name.Substring(0, target.name.Length - 3);
                }

                bool existSame = false;
                foreach (Transform trans in parentObject.transform)
                {
                    if (trans == parentObject.transform)
                    {
                        continue;
                    }

                    if (trans == target.transform)
                    {
                        continue;
                    }

                    if (trans.name.IndexOf(frontName) == 0)
                    {
                        existSame = true;
                        break;
                    }
                }

                string parentName = GetObjectPath(parentObject, rootObject, withoutUIObj, false);
                string objName = "/" + target.name;

                string objectTarget = AnValue.ObjectPrefix;
                if (!withoutUIObj)
                {
                    objectTarget += "object";
                }

                if (target.name == AnValue.RootName)
                {
                    parentName = "";
                    objName = "";
                }
                else if (rootObject == target)
                {
                    parentName = "";
                    objName = "";
                }
                else if (target.name == AnValue.ObjectOffsetName)
                {
                    if (!start)
                    {
                        objName = "";
                    }
                }
                else if (target.name.IndexOf(objectTarget) == 0)
                {
                    if (!start && !existSame)
                    {
                        objName = "";
                    }
                }

                result = parentName + objName;
            }

            if (result.IndexOf("/") == 0)
            {
                result = result.Substring(1);
            }

            return result;
        }
    }
}