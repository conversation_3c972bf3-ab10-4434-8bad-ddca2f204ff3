using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity.Utility;

namespace AnimateToUnity
{
    public class AnUtilityValue
    {
        /// -----------------------------------------
        /// <summary>
        /// Get Limit Value
        /// </summary>
        /// -----------------------------------------
        public static void LimitValue(ref float value, float min, float max)
        {
            if (value > max)
            {
                value = max;
                return;
            }

            if (value < min)
            {
                value = min;
                return;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// Get Limit Value
        /// </summary>
        /// -----------------------------------------
        public static void LimitValue(ref int value, int min, int max)
        {
            if (value > max)
            {
                value = max;
                return;
            }

            if (value < min)
            {
                value = min;
                return;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// Get Limit Value
        /// </summary>
        /// -----------------------------------------
        public static float GetLimitValue(float value, float min, float max)
        {
            LimitValue(ref value, min, max);

            return value;
        }

        /// -----------------------------------------
        /// <summary>
        /// Get Limit Value
        /// </summary>
        /// -----------------------------------------
        public static int GetLimitValue(int value, int min, int max)
        {
            LimitValue(ref value, min, max);

            return value;
        }

        /// -----------------------------------------
        /// <summary>
        /// Get Abs Value
        /// </summary>
        /// -----------------------------------------
        public static float GetAbsValue(float value)
        {
            if (value < 0)
            {
                return value *= -1;
            }

            return value;
        }

        /// --------------------------------------------------------------------
		/// <summary>
		/// Get Sign
		/// </summary>
		/// --------------------------------------------------------------------
		public static float GetSign(float value)
        {
            if (value < 0)
            {
                return -1;
            }

            return 1;
        }

        /// --------------------------------------------------------------------
		/// <summary>
		/// Get digit
		/// </summary>
		/// --------------------------------------------------------------------
		public static int GetDigit(float value)
        {
            float absValue = AnUtilityValue.GetAbsValue(value);

            if (absValue < 1)
            {
                return 1;
            }

            return (int)Mathf.Log10(absValue) + 1;
        }
    }
}
