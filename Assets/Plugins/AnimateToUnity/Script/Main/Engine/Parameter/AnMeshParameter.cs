using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;

namespace AnimateToUnity
{
    public class AnMeshParameter : AnScriptableObject
    {
        //=============================================================================================
        // Variables 
        //=============================================================================================

        public List<AnCustomMeshInfoParameter> _customMeshInfoParameterList = null;
        public List<AnMeshInfoParameterGroup> _meshParameterGroupList = null;

        private Hashtable _customMeshInfoParameterTable = null;

        [NonSerialized]
        private bool _initialized = false;
        
        //URP:置き換え対応
        private List<int> _rootInstanceTable = null;

        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read only
        /// </summary>
        public List<AnMeshInfoParameterGroup> MeshParameterGroupList
        {
            get { return _meshParameterGroupList; }
            set { _meshParameterGroupList = value; }
        }

        /// <summary>
		/// Read only
		/// </summary>
		public List<AnCustomMeshInfoParameter> CustomMeshInfoParameterList
        {
            get { return _customMeshInfoParameterList; }
            set { _customMeshInfoParameterList = value; }
        }

        //=============================================================================================
        // Method
        //=============================================================================================

        //URP:置き換え対応
        public void AddRoot(AnRoot root)
        {
            if(_rootInstanceTable == null)
            {
                _rootInstanceTable = new List<int>();
            }

            var instanceId = root.GetInstanceID();
            if (_rootInstanceTable.Contains(instanceId))
                return;

            _rootInstanceTable.Add(instanceId);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _Initialize(bool isWriteAlphaChannel = false)
        {
            if(_initialized)
            {
                return;
            }

            for (int i = 0; i < _meshParameterGroupList.Count; i++)
            {
                _meshParameterGroupList[i].MeshParameter = this;

                _meshParameterGroupList[i]._Initialize(isWriteAlphaChannel);
            }

            _customMeshInfoParameterTable = new Hashtable();

            for (int i = 0; i < _customMeshInfoParameterList.Count; i++)
            {
                AnCustomMeshInfoParameter customMeshInfoParam = _customMeshInfoParameterList[i];

                if (_customMeshInfoParameterTable.ContainsKey(customMeshInfoParam.TextureName))
                {
                    continue;
                }

                customMeshInfoParam.MeshParameter = this;

                _customMeshInfoParameterTable.Add(customMeshInfoParam.TextureName, customMeshInfoParam);
            }

            _initialized = true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public bool _CreateMesh
            (
                string textureName,
                List<Mesh> meshList,
                bool useCustomMesh,
                ref AnMeshInfoParameter meshInfo,
                ref Mesh mesh
            )
        {
            for (int i = 0; i < _meshParameterGroupList.Count; i++)
            {
                AnMeshInfoParameterGroup meshInfoParameterGroup = _meshParameterGroupList[i];

                if (meshInfoParameterGroup._CreateMesh(textureName, meshList, useCustomMesh, ref meshInfo, ref mesh))
                {
                    return true;
                }
            }

            return false;
        }

        /// -----------------------------------------
		/// <summary>
        /// 
		/// </summary>
		/// -----------------------------------------
        public AnCustomMeshInfoParameter _GetCustomMeshInfoParam(string textureName)
        {
            if (!_customMeshInfoParameterTable.ContainsKey(textureName))
            {
                return null;
            }

            return _customMeshInfoParameterTable[textureName] as AnCustomMeshInfoParameter;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public bool _GetMaterial
            (
                string textureName,
                AnShaderTypes shaderType,
                int stencilRef,
                int baseStencilRef,
                AnStencilCompareFuncTypes stencilCompareFunc,
                bool useCustomMesh,
                ref Material material
            )
        {
            for (int i = 0; i < _meshParameterGroupList.Count; i++)
            {
                AnMeshInfoParameterGroup meshInfoParameterGroup = _meshParameterGroupList[i];

                if (meshInfoParameterGroup._GetMaterial(textureName, shaderType, stencilRef, baseStencilRef, stencilCompareFunc, useCustomMesh, ref material))
                {
                    return true;
                }
            }

            return false;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _Destroy(AnRoot root)
        {
            //URP:置き換え対応
            var instanceId = root.GetInstanceID();
            //参照しているAnRootがいなくなったら破棄する
            _rootInstanceTable.Remove(instanceId);
            if (_rootInstanceTable.Count <= 0)
            {
                if (_meshParameterGroupList != null)
                {
                    for (int i = 0; i < _meshParameterGroupList.Count; i++)
                    {
                        AnMeshInfoParameterGroup meshInfoParameterGroup = _meshParameterGroupList[i];

                        if (meshInfoParameterGroup == null)
                        {
                            continue;
                        }

                        meshInfoParameterGroup._Destroy();
                    }
                }
                _initialized = false;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public bool _SearchMesh(string textureName, ref AnMeshInfoParameterGroup meshInfoParamGroup, ref AnMeshInfoParameter meshInfoParam)
        {
            if (Application.isPlaying)
            {
                _Initialize();
            }

            for (int i = 0; i < _meshParameterGroupList.Count; i++)
            {
                AnMeshInfoParameterGroup meshInfoParameterGroup = _meshParameterGroupList[i];

                if (meshInfoParameterGroup._SearchMesh(textureName, ref meshInfoParam))
                {
                    meshInfoParamGroup = meshInfoParameterGroup;

                    return true;
                }
            }

            return false;
        }
    }
}