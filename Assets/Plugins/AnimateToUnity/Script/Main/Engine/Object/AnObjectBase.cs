using UnityEngine;
using System;
using System.Collections.Generic;

namespace AnimateToUnity
{
    public class AnObjectBase : AnBase
    {
        //=============================================================================================
        // Variables 
        //=============================================================================================

        //***************************************************************
        // General
        //***************************************************************

        public AnMotion _parentMotion = null;

        public AnObjectParameterBase _parameter = null;

        protected int _objectIndex = 0;

        protected GameObject _offsetObject = null;
        protected Transform _offsetTransform = null;
        protected bool _existOffsetObject = false;        

        protected AnBlendModeTypes _blendModeType = AnBlendModeTypes.Normal;

        //***************************************************************
        // Collider
        //***************************************************************

        protected int _existCollider = 0;
        protected Collider _collider = null;
        protected Collider2D _collider2D = null;

        protected int _existSubCollider = 0;
        protected Collider _subCollider = null;

        //***************************************************************
        // PlaceAnchor
        //***************************************************************

        protected Vector3 _basePlaceOffset = Vector3.zero;
        protected Vector3 _placeOffset = Vector3.zero;
        protected Vector3 _placeScale = Vector3.one;

        //***************************************************************
        // Transform
        //***************************************************************

        protected Vector3 _currentPosition = Vector3.zero;
        protected Vector3 _currentPositionOffset = Vector3.zero;
        protected Vector3 _currentRotate = Vector3.zero;
        protected Vector3 _currentScale = Vector3.zero;
        protected Vector2 _currentShear = Vector3.zero;

        protected Vector3 _prevPosition = AnValue.Vector3Max;
        protected Vector3 _prevPositionOffset = AnValue.Vector3Max;
        protected Vector3 _prevRotate = AnValue.Vector3Max;
        protected Vector3 _prevScale = AnValue.Vector3Max;
        protected Vector2 _prevShear = AnValue.Vector2Max;

        protected Vector4 _currentShearCosSin = Vector4.one;

        protected bool _positionChanged = false;
        protected bool _positionOffsetChanged = false;
        protected bool _rotateChanged = false;
        protected bool _scaleChanged = false;
        protected bool _shearChanged = false;

        protected int[] _positionKeyIndex = null;
        protected int[] _positionOffsetKeyIndex = null;
        protected int[] _rotateKeyIndex = null;
        protected int[] _scaleKeyIndex = null;
        protected int[] _shearKeyIndex = null;

        protected float _localDepthOffset = 0;
        protected float _fixLocalDepthOffset = 0;

        //***************************************************************
        // Color
        //***************************************************************

        protected Color _baseColor = Color.white;
        protected Color _baseColorOffset = AnValue.ColorZero;

        protected bool _colorChanged = false;
        protected bool _colorOffsetChanged = false;

        protected int[] _colorKeyIndex = null;
        protected int[] _colorOffsetKeyIndex = null;

        //***************************************************************
        // Blur
        //***************************************************************

        protected bool _blurChanged = false;

        protected int[] _blurValueKeyIndex = null;

        //=============================================================================================
        // Property
        //=============================================================================================

        //***************************************************************
        // General
        //***************************************************************

        /// <summary>
        /// Read only
        /// </summary>
        public AnObjectParameterBase Parameter
        {
            get { return _parameter; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public AnMotion ParentMotion
        {
            get { return _parentMotion; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public GameObject OffsetObject
        {
            get { return _offsetObject; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public bool ExistOffsetObject
        {
            get { return _existOffsetObject; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public AnBlendModeTypes BlendModeType
        {
            get { return _blendModeType; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public int ObjectIndex
        {
            get { return _objectIndex; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Vector3 CurrentPosition
        {
            get { return _currentPosition; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Vector3 CurrentPositionOffset
        {
            get { return _currentPositionOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Vector3 CurrentRotate
        {
            get { return _currentRotate; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Vector3 CurrentScale
        {
            get { return _currentScale; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Vector2 CurrentShear
        {
            get { return _currentShear; }
        }

        //***************************************************************
        // Color
        //***************************************************************

        /// <summary>
        /// Read Only
        /// </summary>
        public Color BaseColor
        {
            get { return _baseColor; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float BaseAlpha
        {
            get { return _baseColor.a; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public Color BaseColorOffset
        {
            get { return _baseColorOffset; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float BaseAlphaOffset
        {
            get { return _baseColorOffset.a; }
        }

        //***************************************************************
        // Collider
        //***************************************************************

        /// <summary>
        /// Read only
        /// </summary>
        public Collider Collider
        {
            get { return _collider; }
            set { _collider = value; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Collider2D Collider2D
        {
            get { return _collider2D; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Collider SubCollider
        {
            get { return _subCollider; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public int ExistSubCollider
        {
            get { return _existSubCollider; }
        }

        //=============================================================================================
        // Constructor
        //=============================================================================================

        public AnObjectBase(GameObject gameObject)
        {
            _gameObject = gameObject;
            _transform = gameObject.transform;
            _id = _gameObject.GetInstanceID().ToString();
        }

        //=============================================================================================
        // Method
        //=============================================================================================

        //***************************************************************
        //
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void _CreateEditorData(AnObjectParameterBase parameter, AnMotion parentMotion)
        {
            _root = parentMotion.Root;
            _parameter = parameter;
            _parentMotion = parentMotion;

            _offsetObject = _gameObject;
            _offsetTransform = _transform;
            if (_transform.childCount != 0)
            {
                if (_transform.GetChild(0).name == AnValue.ObjectOffsetName)
                {
                    _offsetObject = _gameObject.transform.GetChild(0).gameObject;
                    _offsetTransform = _offsetObject.transform;
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void _ApplyData(AnObjectParameterBase parameter, AnMotion parentMotion)
        {
            _root = parentMotion.Root;
            _parameter = parameter;
            _parentMotion = parentMotion;
            _parentMotion.ObjectList.Add(this);

            _existOffsetObject = false;
            _offsetObject = _gameObject;
            _offsetTransform = _transform;
            if (_transform.childCount != 0)
            {
                if (_transform.GetChild(0).name == AnValue.ObjectOffsetName)
                {
                    _existOffsetObject = true;
                    _offsetObject = _gameObject.transform.GetChild(0).gameObject;
                    _offsetTransform = _offsetObject.transform;
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _CreateData()
        {
            base._CreateData();

            _visible = true;

            //Transform
            _positionKeyIndex = new int[3] { 0, 0, 0 };
            _positionOffsetKeyIndex = new int[3] { 0, 0, 0 };
            _rotateKeyIndex = new int[3] { 0, 0, 0 };
            _scaleKeyIndex = new int[3] { 0, 0, 0 };
            _shearKeyIndex = new int[2] { 0, 0 };
            _blurValueKeyIndex = new int[2] { 0, 0 };

            //Shear
            _currentShear = _parameter.Shear;

            //Object And Blend
            _objectType = _parameter.ObjectType;
            _blendModeType = _parameter.BlendModeType;

            //Color
            _multiplyColor = new Color(1, 1, 1, 1);
            _colorOffset = new Color(0, 0, 0, 0);

            _baseColor = _parameter._color;
            _baseColorOffset = _parameter._colorOffset;

            _colorKeyIndex = new int[4] { 0, 0, 0, 0 };
            _colorOffsetKeyIndex = new int[4] { 0, 0, 0, 0 };

            //Stencil
            _stencilRef = 0;

            //Blur
            _blurQuality = _parameter.BlurQuality;
            _blurPrecision = _parameter.BlurPrecision;
            _blurValue = _parameter.BlurValue;

            //Stencil
            if (_parameter.StencilRef == 0)
            {
                _localStencilRefOffset = 0;
            }
            else
            {
                if (!_parentMotion.ExistStencilRefCountUp)
                {
                    if (_root.StencilRefCount == -1)
                    {
                        _root.StencilRefCount = 0;
                    }
                    else
                    {
                        _root.StencilRefCount += _root.StencilRefInterval;
                    }

                    _parentMotion.ExistStencilRefCountUp = true;
                }

                _localStencilRefOffset = _parameter.StencilRef + _root.StencilRefCount;
            }

            if (_localStencilRefOffset > 0)
            {
                _stencilRef = _root.DefaultStencilRefOffset + _localStencilRefOffset;
            }
            else
            {
                _stencilRef = _root.DefaultStencilRefOffset;
            }

            //Stencil Compare Func
            if (_parameter.StencilCompareFunc != AnStencilCompareFuncTypes.None)
            {
                _localStencilCompareFunc = _parameter.StencilCompareFunc;
            }

            if (_localStencilCompareFunc != AnStencilCompareFuncTypes.None)
            {
                _stencilCompareFunc = _localStencilCompareFunc;
            }
            else
            {
                _stencilCompareFunc = _root.DefaultStencilCompareFunc;
            }

            //Layer
            _layerName = _parentMotion.Root.Parameter.LayerName;
            _layerIndex = _parentMotion.Root.Parameter.LayerIndex;
            if (_parameter.LayerName != "")
            {
                _layerName = _parameter.LayerName;
                _layerIndex = AnUtilityObject.GetLayerIndex(_layerName);
            }

            _gameObject.layer = _layerIndex;
            _offsetObject.layer = _layerIndex;
            
            //Sort offset
            _sortOffset = 0;
            _localSortOffset = 0;
            if (_parameter._sortOffset != 0)
            {
                _localSortOffset = _parameter._sortOffset;
            }

            //Sort layer
            _sortLayerName = _parentMotion.Root.Parameter.SortLayerName;
            if (_parameter.SortLayerName != "")
            {
                _sortLayerName = _parameter.SortLayerName;
            }

            //Check Type
            if (_parameter.ObjectType == AnObjectTypes.Mask)
            {
                _objectType = AnObjectTypes.Mask;
            }
            else if (_parameter.ObjectType == AnObjectTypes.AlphaMask)
            {
                _objectType = AnObjectTypes.AlphaMask;
            }
            else if (_parameter.ObjectType == AnObjectTypes.StencilMask)
            {
                _objectType = AnObjectTypes.StencilMask;
            }
            else if (_parameter.ObjectType == AnObjectTypes.StencilAlphaMask)
            {
                _objectType = AnObjectTypes.StencilAlphaMask;
            }
            else if (_parameter.ObjectType == AnObjectTypes.Opaque)
            {
                _objectType = AnObjectTypes.Opaque;
            }
            else if (_parameter.ObjectType == AnObjectTypes.ObjectMask)
            {
                _objectType = AnObjectTypes.ObjectMask;
            }
            else if (_parameter.ObjectType == AnObjectTypes.ObjectAlphaMask)
            {
                _objectType = AnObjectTypes.ObjectAlphaMask;
            }

            //Grayscale
            _isGrayscale = false;

            //Time mode type
            _timeModeType = _parameter.TimeModeType;

            //Chack parent
            _CheckParentMotion();

            //Check collision
            _CheckCollision();

            //Reset value
            _ResetPrevValue();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CheckCollision()
        {
            _existCollider = 0;
            _existSubCollider = 0;
            _enableCollider = true;
            _colliderThrough = false;
            _collider = null;
            _collider2D = null;
            _subCollider = null;

            if (_parameter.CollisionParamList.Length == 0)
            {
                return;
            }

            _colliderThrough = _parameter.CollisionParamList[0].Through;

            _collider = _parentMotion.Root.ColliderTable[_offsetObject] as Collider;

            if (_collider != null)
            {
                _existCollider = 1;

                return;
            }

            _collider2D = _parentMotion.Root.ColliderTable[_offsetObject] as Collider2D;

            if (_collider2D != null)
            {
                _existCollider = 2;

                return;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CheckParentMotion()
        {
            if (!_parentMotion.ExistParentObject)
            {
                return;
            }

            //Blend
            if (_parentMotion.ParentObject.BlendModeType != AnBlendModeTypes.Normal)
            {
                _blendModeType = _parentMotion.ParentObject.BlendModeType;
            }

            //ObjectType
            if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.Mask)
            {
                _objectType = AnObjectTypes.Mask;
            }
            else if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.AlphaMask)
            {
                _objectType = AnObjectTypes.AlphaMask;
            }
            else if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.StencilMask)
            {
                _objectType = AnObjectTypes.StencilMask;
            }
            else if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.StencilAlphaMask)
            {
                _objectType = AnObjectTypes.StencilAlphaMask;
            }
            else if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.Opaque)
            {
                _objectType = AnObjectTypes.Opaque;
            }
            else if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.ObjectMask)
            {
                _objectType = AnObjectTypes.ObjectMask;
            }
            else if (_parentMotion.ParentObject.ObjectType == AnObjectTypes.ObjectAlphaMask)
            {
                _objectType = AnObjectTypes.ObjectAlphaMask;
            }

            //Layer
            if (_parentMotion.ParentObject.LayerName != _parentMotion.Root.Parameter.LayerName)
            {
                _layerName = _parentMotion.ParentObject.LayerName;
                _layerIndex = _parentMotion.ParentObject.LayerIndex;

                _gameObject.layer = _layerIndex;
                _offsetObject.layer = _layerIndex;
            }

            //Local Sort offset
            if (_parentMotion.ParentObject.LocalSortOffset != 0)
            {
                _localSortOffset = _parentMotion.ParentObject.LocalSortOffset;
            }

            //Local Stencil offset
            if (_parentMotion.ParentObject.LocalStencilRefOffset != 0)
            {
                _localStencilRefOffset = _parentMotion.ParentObject.LocalStencilRefOffset;
            }

            //Local Stencil Compare Func
            if (_parentMotion.ParentObject.LocalStencilCompareFunc != AnStencilCompareFuncTypes.None)
            {
                _localStencilCompareFunc = _parentMotion.ParentObject.LocalStencilCompareFunc;
            }

            //Sort layer
            if (_parentMotion.ParentObject.SortLayerName != _parentMotion.Root.Parameter.SortLayerName)
            {
                _sortLayerName = _parentMotion.ParentObject.SortLayerName;
            }

            //Sync time
            if (_parentMotion.ParentObject.TimeModeType == AnTimeModeTypes.Sync)
            {
                if (_parameter.TimeModeType != AnTimeModeTypes.Normal)
                {
                    _timeModeType = AnTimeModeTypes.Sync;
                }
            }
            else if (_parentMotion.ParentObject.TimeModeType == AnTimeModeTypes.Normal)
            {
                _timeModeType = AnTimeModeTypes.Normal;
            }

            //Blur
            if(_parentMotion.ParentObject.BlurQuality != 0)
            {
                _blurQuality = _parentMotion.ParentObject.BlurQuality;
            }

            if (_parentMotion.ParentObject.BlurPrecision != 0)
            {
                _blurPrecision = _parentMotion.ParentObject.BlurPrecision;
            }

            if (_parentMotion.ParentObject.BlurValue != Vector2.zero)
            {
                _blurValue = _parentMotion.ParentObject.BlurValue;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _ResetPrevValue()
        {
            base._ResetPrevValue();

            _prevPosition = AnValue.Vector3Max;
            _prevPositionOffset = AnValue.Vector3Max;
            _prevRotate = AnValue.Vector3Max;
            _prevScale = AnValue.Vector3Max;
            _prevShear = AnValue.Vector2Max;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _ResetTime()
        {
            base._ResetTime();

            if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Position) != 0)
            {
                for (int i = 0; i < _positionKeyIndex.Length; i++)
                {
                    _positionKeyIndex[i] = 0;
                }
            }

            if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionOffset) != 0)
            {
                for (int i = 0; i < _positionOffsetKeyIndex.Length; i++)
                {
                    _positionOffsetKeyIndex[i] = 0;
                }
            }

            if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Rotate) != 0)
            {
                for (int i = 0; i < _rotateKeyIndex.Length; i++)
                {
                    _rotateKeyIndex[i] = 0;
                }
            }

            if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Scale) != 0)
            {
                for (int i = 0; i < _scaleKeyIndex.Length; i++)
                {
                    _scaleKeyIndex[i] = 0;
                }
            }

            if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Shear) != 0)
            {
                for (int i = 0; i < _shearKeyIndex.Length; i++)
                {
                    _shearKeyIndex[i] = 0;
                }
            }

            if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.Color) != 0)
            {
                for (int i = 0; i < _colorKeyIndex.Length; i++)
                {
                    _colorKeyIndex[i] = 0;
                }
            }

            if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorOffset) != 0)
            {
                for (int i = 0; i < _colorOffsetKeyIndex.Length; i++)
                {
                    _colorOffsetKeyIndex[i] = 0;
                }
            }

            if ((_parameter._blurAnimationFlag & AnBlurAnimationFlags.Blur) != 0)
            {
                for (int i = 0; i < _blurValueKeyIndex.Length; i++)
                {
                    _blurValueKeyIndex[i] = 0;
                }
            }

            _ResetPrevValue();
        }

        //***************************************************************
        //
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _FixData()
        {
            base._FixData();

            _UpdateSortOrder();

            _UpdateSortLayer();

            _UpdateStencilRef(false);

            _UpdateBasePlaceOffset();

            _SetDepth();

            _UpdateShear();

            _CheckUI();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _FinalizeData()
        {
            base._FinalizeData();

            _CreateUI();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateBasePlaceOffset()
        {
            _basePlaceOffset = Vector3.zero;
            
            if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.Default)
            {
                return;
            }

            if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.TopLeft)
            {
                _basePlaceOffset.x = _transform.position.x - _root._screenBaseLeftPosition.x;
                _basePlaceOffset.y = _transform.position.y - _root._screenBaseRightPosition.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.TopCenter)
            {
                _basePlaceOffset.x = _transform.position.x;
                _basePlaceOffset.y = _transform.position.y - _root._screenBaseRightPosition.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.TopRight)
            {
                _basePlaceOffset.x = _transform.position.x - _root._screenBaseRightPosition.x;
                _basePlaceOffset.y = _transform.position.y - _root._screenBaseRightPosition.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.MiddleLeft)
            {
                _basePlaceOffset.x = _transform.position.x - _root._screenBaseLeftPosition.x;
                _basePlaceOffset.y = _transform.position.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.MiddleCenter)
            {
                _basePlaceOffset.x = _transform.position.x;
                _basePlaceOffset.y = _transform.position.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.MiddleRight)
            {
                _basePlaceOffset.x = _transform.position.x - _root._screenBaseRightPosition.x;
                _basePlaceOffset.y = _transform.position.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.BottomLeft)
            {
                _basePlaceOffset.x = _transform.position.x - _root._screenBaseLeftPosition.x;
                _basePlaceOffset.y = _transform.position.y - _root._screenBaseLeftPosition.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.BottomCenter)
            {
                _basePlaceOffset.x = _transform.position.x;
                _basePlaceOffset.y = _transform.position.y - _root._screenBaseLeftPosition.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.BottomRight)
            {
                _basePlaceOffset.x = _transform.position.x - _root._screenBaseRightPosition.x;
                _basePlaceOffset.y = _transform.position.y - _root._screenBaseLeftPosition.y;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdatePlaceOffset()
        {
            _placeOffset = Vector3.zero;
            _placeScale = Vector3.one;
            
            if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.Default)
            {
                return;
            }

            _tempVector3_0 = Vector3.zero;
            _tempVector3_1 = Vector3.zero;
            _tempVector3_2 = Vector3.zero;
            _tempVector3_3 = Vector3.zero;
            _tempVector3_4 = Vector3.one;

            //Edge
            if (_root._fitScreen)
            {
                if (_parameter._placeAnchorAttachType == AnPlaceAnchorAttachTypes.Default)
                {
                    _tempVector3_1.x = _root._screenLeftPosition.x;
                    _tempVector3_1.y = _root._screenLeftPosition.y;

                    _tempVector3_2.x = _root._screenRightPosition.x;
                    _tempVector3_2.y = _root._screenRightPosition.y;

                    _tempVector3_3.x = _root._screenOffset.x * 0.5f;
                    _tempVector3_3.y = _root._screenOffset.y * 0.5f;

                    if (_parameter._placeAnchorScaleType == AnPlaceAnchorScaleTypes.Default)
                    {
                        _tempVector3_4.x = _root._screenScale;
                        _tempVector3_4.y = _root._screenScale;
                    }
                }
                else if (_parameter._placeAnchorAttachType == AnPlaceAnchorAttachTypes.Edge)
                {
                    _tempVector3_1.x = _root._screenEdgeLeftPosition.x;
                    _tempVector3_1.y = _root._screenEdgeLeftPosition.y;

                    _tempVector3_2.x = _root._screenEdgeRightPosition.x;
                    _tempVector3_2.y = _root._screenEdgeRightPosition.y;
                }
                else if (_parameter._placeAnchorAttachType == AnPlaceAnchorAttachTypes.Margin)
                {
                    _tempVector3_1.x = _root._screenMarginLeftPosition.x;
                    _tempVector3_1.y = _root._screenMarginLeftPosition.y;

                    _tempVector3_2.x = _root._screenMarginRightPosition.x;
                    _tempVector3_2.y = _root._screenMarginRightPosition.y;

                    _tempVector3_3.x = _root._screenMarginOffset.x * 0.5f;
                    _tempVector3_3.y = _root._screenMarginOffset.y * 0.5f;

                    if (_parameter._placeAnchorScaleType == AnPlaceAnchorScaleTypes.Default)
                    {
                        _tempVector3_4.x = _root._screenScale;
                        _tempVector3_4.y = _root._screenScale;
                    }
                }
            }
            else
            {
                _tempVector3_1.x = _root._screenBaseLeftPosition.x;
                _tempVector3_1.y = _root._screenBaseLeftPosition.y;

                _tempVector3_2.x = _root._screenBaseRightPosition.x;
                _tempVector3_2.y = _root._screenBaseRightPosition.y;
            }

            //Position
            if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.TopLeft)
            {
                _tempVector3_0.x = _tempVector3_1.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_2.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.TopCenter)
            {
                _tempVector3_0.x = _tempVector3_3.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_2.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.TopRight)
            {
                _tempVector3_0.x = _tempVector3_2.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_2.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.MiddleLeft)
            {
                _tempVector3_0.x = _tempVector3_1.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_3.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.MiddleCenter)
            {
                _tempVector3_0.x = _tempVector3_3.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_3.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.MiddleRight)
            {
                _tempVector3_0.x = _tempVector3_2.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_3.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.BottomLeft)
            {
                _tempVector3_0.x = _tempVector3_1.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_1.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.BottomCenter)
            {
                _tempVector3_0.x = _tempVector3_3.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_1.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            else if (_parameter.PlaceAnchorType == AnPlaceAnchorTypes.BottomRight)
            {
                _tempVector3_0.x = _tempVector3_2.x + _basePlaceOffset.x * _tempVector3_4.x;
                _tempVector3_0.y = _tempVector3_1.y + _basePlaceOffset.y * _tempVector3_4.y;
            }
            
            _transform.position = _tempVector3_0;

            //Place Offset And Place Scale
            _placeOffset.x = _transform.localPosition.x - _parameter._position.x;
            _placeOffset.y = _transform.localPosition.y - _parameter._position.y;

            _placeScale.x = _tempVector3_4.x;
            _placeScale.y = _tempVector3_4.y;

            //Update Transform
            _UpdateTransform(true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _SetDepth()
        {
            _localDepthOffset = _parameter._depthOffset;
            _fixLocalDepthOffset = 0;

            if (_localDepthOffset != 0)
            {
                float thisDefaultDepth = _transform.localPosition.z;

                _transform.position = new Vector3
                    (
                        _transform.position.x,
                        _transform.position.y,
                        _transform.position.z + _localDepthOffset
                    );

                _fixLocalDepthOffset = _transform.localPosition.z - thisDefaultDepth;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CheckUI()
        {
            if (_parameter.UIParameter == null)
            {
                return;
            }

            if (_parameter.UIParameter.UIType == AnUITypes.None)
            {
                return;
            }

            _root.FinalizeTargetDataList.Add(this);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateUI()
        {
            if (_parameter.UIParameter == null)
            {
                return;
            }

            _parameter.UIParameter._CreateData(this);
        }

        //***************************************************************
        //
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateFirst()
        {
            base._UpdateFirst();

            _UpdateVisible();

            if (_root._initializeFlag)
            {
                _visibleInHierarchy = true;
            }

            if (_visibleInHierarchy)
            {
                _UpdateColor();
            }

            if (_root._initializeFlag)
            {
                _visibleByAlpha = true;
            }

            if (!_visibleInHierarchy || !_visibleByAlpha)
            {
                _UpdateEnableCollision(false);
                return;
            }

            _UpdateEnableCollision(true);

            _UpdateTransform(false);

            _UpdateBlurValue();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateSecond()
        {
            base._UpdateSecond();
            
            _prevPosition = _currentPosition;
            _prevPositionOffset = _currentPositionOffset;
            _prevRotate = _currentRotate;
            _prevScale = _currentScale;
            _prevShear = _currentShear;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected virtual void _UpdateVisible()
        {
            //Check time range
            if (_parentMotion._objectTime < _parameter._timeRange.x)
            {
                _isInTimeRange = false;
                return;
            }

            if (_parentMotion._objectTime >= _parameter._timeRange.y)
            {
                _isInTimeRange = false;
                return;
            }

            //Reset Time
            if (!_isInTimeRange && !_root._initializeFlag)
            {
                _isResetTime = true;
                _ResetTime();
                _isResetTime = false;

                _isInTimeRange = true;
            }

            //Check parent
            if (!_parentMotion._visibleInHierarchy)
            {
                return;
            }

            //Check visible
            if (!_visible)
            {
                return;
            }

            //Check active
            if (!_gameObject.activeInHierarchy)
            {
                return;
            }

            _visibleInHierarchy = true;

        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected virtual void _UpdateColor()
        {
            _colorChanged = false;
            _colorOffsetChanged = false;

            if (_parameter._colorAnimationFlag != 0)
            {
                //Color
                if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.Color) != 0)
                {
                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorR) != 0)
                    {
                        _currentColor.r = _parameter._colorKeyParamList[0]._GetValue(_baseColor.r, _parentMotion, ref _colorKeyIndex[0]);
                    }

                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorG) != 0)
                    {
                        _currentColor.g = _parameter._colorKeyParamList[1]._GetValue(_baseColor.g, _parentMotion, ref _colorKeyIndex[1]);
                    }

                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorB) != 0)
                    {
                        _currentColor.b = _parameter._colorKeyParamList[2]._GetValue(_baseColor.b, _parentMotion, ref _colorKeyIndex[2]);
                    }

                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorA) != 0)
                    {
                        _currentColor.a = _parameter._colorKeyParamList[3]._GetValue(_baseColor.a, _parentMotion, ref _colorKeyIndex[3]);
                    }
                }

                //Color offset
                if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorOffset) != 0)
                {
                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorOffsetR) != 0)
                    {
                        _currentColorOffset.r = _parameter._colorOffsetKeyParamList[0]._GetValue(_baseColorOffset.r, _parentMotion, ref _colorOffsetKeyIndex[0]);
                    }

                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorOffsetG) != 0)
                    {
                        _currentColorOffset.g = _parameter._colorOffsetKeyParamList[1]._GetValue(_baseColorOffset.g, _parentMotion, ref _colorOffsetKeyIndex[1]);
                    }

                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorOffsetB) != 0)
                    {
                        _currentColorOffset.b = _parameter._colorOffsetKeyParamList[2]._GetValue(_baseColorOffset.b, _parentMotion, ref _colorOffsetKeyIndex[2]);
                    }

                    if ((_parameter._colorAnimationFlag & AnColorAnimationFlags.ColorOffsetA) != 0)
                    {
                        _currentColorOffset.a = _parameter._colorOffsetKeyParamList[3]._GetValue(_baseColorOffset.a, _parentMotion, ref _colorOffsetKeyIndex[3]);
                    }
                }
            }

            AnUtilityColor.MultiplyColor(ref _currentColor, _multiplyColor);
            AnUtilityColor.AddColor(ref _currentColorOffset, _colorOffset);

            AnUtilityColor.MultiplyColor(ref _currentColor, _parentMotion._currentColor);

            AnUtilityColor.MultiplyColor(ref _currentColorOffset, _parentMotion._currentColor);
            AnUtilityColor.AddColor(ref _currentColorOffset, _parentMotion._currentColorOffset);
            

            if (_currentColor.a + _currentColorOffset.a <= AnValue.MinAlphaValue)
            {
                return;
            }

            if (!AnUtilityColor.IsSameColor(_currentColor, _prevColor))
            {
                _colorChanged = true;
            }

            if (!AnUtilityColor.IsSameColor(_currentColorOffset, _prevColorOffset))
            {
                _colorOffsetChanged = true;
            }

            _visibleByAlpha = true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected virtual void _UpdateTransform(bool forceUpdate)
        {
            _currentPosition = _parameter._position;
            _currentPosition.x += _placeOffset.x;
            _currentPosition.y += _placeOffset.y;
            _currentPosition.z += _placeOffset.z + _localDepthOffset;

            _currentPositionOffset = _parameter._positionOffset;

            _currentRotate = _parameter._rotate;

            _currentScale = _parameter._scale;
            _currentScale.x *= _placeScale.x;
            _currentScale.y *= _placeScale.y;
            _currentScale.z *= _placeScale.z;

            _currentShear = _parameter._shear;            

            if (forceUpdate)
            {
                _positionChanged = true;
                _positionOffsetChanged = true;
                _rotateChanged = true;
                _scaleChanged = true;
                _shearChanged = true;
            }
            else
            {
                _positionChanged = false;
                _positionOffsetChanged = false;
                _rotateChanged = false;
                _scaleChanged = false;
                _shearChanged = false;
            }

            if (_parameter._transformAnimationFlag != 0)
            {
                //Position
                if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Position) != 0)
                {
                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionX) != 0)
                    {
                        _currentPosition.x = _parameter._positionKeyParamList[0]._GetValue(_parameter._position.x, _parentMotion, ref _positionKeyIndex[0]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionY) != 0)
                    {
                        _currentPosition.y = _parameter._positionKeyParamList[1]._GetValue(_parameter._position.y, _parentMotion, ref _positionKeyIndex[1]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionZ) != 0)
                    {
                        _currentPosition.z = _parameter._positionKeyParamList[2]._GetValue(_parameter._position.z, _parentMotion, ref _positionKeyIndex[2]);
                    }

                    if (!AnUtilityVector.IsSameVector(_currentPosition, _prevPosition))
                    {
                        _transform.localPosition = new Vector3(_currentPosition.x, _currentPosition.y, _currentPosition.z + _fixLocalDepthOffset) + _placeOffset;
                        _positionChanged = true;
                    }
                }

                //Position Offset
                if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionOffset) != 0)
                {
                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionOffsetX) != 0)
                    {
                        _currentPositionOffset.x = _parameter._positionOffsetKeyParamList[0]._GetValue(_parameter._positionOffset.x, _parentMotion, ref _positionOffsetKeyIndex[0]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionOffsetY) != 0)
                    {
                        _currentPositionOffset.y = _parameter._positionOffsetKeyParamList[1]._GetValue(_parameter._positionOffset.y, _parentMotion, ref _positionOffsetKeyIndex[1]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.PositionOffsetZ) != 0)
                    {
                        _currentPositionOffset.z = _parameter._positionOffsetKeyParamList[2]._GetValue(_parameter._positionOffset.z, _parentMotion, ref _positionOffsetKeyIndex[2]);
                    }

                    if (!AnUtilityVector.IsSameVector(_currentPositionOffset, _prevPositionOffset))
                    {
                        _offsetTransform.localPosition = new Vector3(_currentPositionOffset.x, _currentPositionOffset.y, _currentPositionOffset.z);
                        _positionOffsetChanged = true;
                    }
                }

                //Rotate
                if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Rotate) != 0)
                {
                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.RotateX) != 0)
                    {
                        _currentRotate.x = _parameter._rotateKeyParamList[0]._GetValue(_parameter._rotate.x, _parentMotion, ref _rotateKeyIndex[0]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.RotateY) != 0)
                    {
                        _currentRotate.y = _parameter._rotateKeyParamList[1]._GetValue(_parameter._rotate.y, _parentMotion, ref _rotateKeyIndex[1]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.RotateZ) != 0)
                    {
                        _currentRotate.z = _parameter._rotateKeyParamList[2]._GetValue(_parameter._rotate.z, _parentMotion, ref _rotateKeyIndex[2]);
                    }

                    if (!AnUtilityVector.IsSameVector(_currentRotate, _prevRotate))
                    {
                        _transform.localRotation = Quaternion.Euler(_currentRotate);
                        _rotateChanged = true;
                    }
                }

                //Scale
                if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Scale) != 0)
                {
                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.ScaleX) != 0)
                    {
                        _currentScale.x = _parameter._scaleKeyParamList[0]._GetValue(_parameter._scale.x, _parentMotion, ref _scaleKeyIndex[0]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.ScaleY) != 0)
                    {
                        _currentScale.y = _parameter._scaleKeyParamList[1]._GetValue(_parameter._scale.y, _parentMotion, ref _scaleKeyIndex[1]);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.ScaleZ) != 0)
                    {
                        _currentScale.z = _parameter._scaleKeyParamList[2]._GetValue(_parameter._scale.z, _parentMotion, ref _scaleKeyIndex[2]);
                    }

                    if (!AnUtilityVector.IsSameVector(_currentScale, _prevScale))
                    {
                        _scaleChanged = true;
                    }
                }

                //Shear
                if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.Shear) != 0)
                {
                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.ShearX) != 0)
                    {
                        _currentShear.x = _parameter._shearKeyParamList[0]._GetValue(_parameter._shear.x, _parentMotion, ref _shearKeyIndex[0], _parameter._isStabilizeRotation);
                    }

                    if ((_parameter._transformAnimationFlag & AnTransformAnimationFlags.ShearY) != 0)
                    {
                        _currentShear.y = _parameter._shearKeyParamList[1]._GetValue(_parameter._shear.y, _parentMotion, ref _shearKeyIndex[1], _parameter._isStabilizeRotation);
                    }

                    if (!AnUtilityVector.IsSameVector(_currentShear, _prevShear))
                    {
                        _UpdateShear();

                        _shearChanged = true;
                    }
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected void _UpdateShear()
        {
            _currentShearCosSin.x = Mathf.Cos(Mathf.Deg2Rad * _currentShear.x);
            _currentShearCosSin.y = Mathf.Sin(Mathf.Deg2Rad * _currentShear.x);
            _currentShearCosSin.z = Mathf.Cos(Mathf.Deg2Rad * _currentShear.y);
            _currentShearCosSin.w = Mathf.Sin(Mathf.Deg2Rad * _currentShear.y);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected virtual void _UpdateBlurValue()
        {
            _blurChanged = false;

            if (_parentMotion.CurrentBlurQuality > 0)
            {
                if (_blurQuality <= 0)
                {
                    _currentBlurQuality = _parentMotion.CurrentBlurQuality;
                }
                else
                {
                    _currentBlurQuality = _blurQuality;
                }
            }
            else
            {
                _currentBlurQuality = _blurQuality;
            }

            if(_currentBlurQuality <= 0)
            {
                return;
            }

            if (_parentMotion.CurrentBlurPrecision > 0)
            {
                if (_blurPrecision <= 0)
                {
                    _currentBlurPrecision = _parentMotion.CurrentBlurPrecision;
                }
                else
                {
                    _currentBlurPrecision = _blurPrecision;
                }
            }
            else
            {
                _currentBlurPrecision = _blurPrecision;
            }

            _currentBlurValue = _blurValue;

            if (_parameter._blurAnimationFlag != 0)
            {
                if ((_parameter._blurAnimationFlag & AnBlurAnimationFlags.Blur) != 0)
                {
                    if ((_parameter._blurAnimationFlag & AnBlurAnimationFlags.BlurX) != 0)
                    {
                        _currentBlurValue.x = _parameter._blurKeyParamList[0]._GetValue(_blurValue.x, _parentMotion, ref _blurValueKeyIndex[0]);
                    }

                    if ((_parameter._blurAnimationFlag & AnBlurAnimationFlags.BlurX) != 0)
                    {
                        _currentBlurValue.y = _parameter._blurKeyParamList[1]._GetValue(_blurValue.y, _parentMotion, ref _blurValueKeyIndex[1]);
                    }
                }
            }

            if(_parentMotion.CurrentBlurValue.x != 0 || _parentMotion.CurrentBlurValue.y != 0)
            {
                _currentBlurValue += _parentMotion.CurrentBlurValue;
            }

            if (!AnUtilityVector.IsSameVector(_currentBlurValue, _prevBlurValue))
            {
                _blurChanged = true;
            }
        }

        //***************************************************************
        //
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected virtual void _UpdateEnableCollision(bool enable)
        {
            if (_existCollider == 0)
            {
                return;
            }

            if (_existCollider == 1)
            {
                if (enable)
                {
                    if (!_collider.enabled)
                    {
                        if (_enableCollider)
                        {
                            _collider.enabled = true;
                        }
                    }

                    return;
                }

                if (_collider.enabled)
                {
                    _collider.enabled = false;
                }
            }
            else if (_existCollider == 2)
            {
                if (enable)
                {
                    if (!_collider2D.enabled)
                    {
                        if (_enableCollider)
                        {
                            _collider2D.enabled = true;
                        }
                    }

                    return;
                }

                if (_collider2D.enabled)
                {
                    _collider2D.enabled = false;
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateSortOrder()
        {
            base._UpdateSortOrder();

            if (!_root.DrawTextLater)
            {
                _sortOrder = _root.SortOrderCount - _sortOrderIndex + _sortOffset + _root.DefaultSortOffset + _localSortOffset;
            }
            else
            {
                _sortOrder = _root.SortOrderCountForDrawTextLater - _sortOrderIndexForDrawTextLater + _sortOffset + _root.DefaultSortOffset + _localSortOffset;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateSortLayer()
        {
            base._UpdateSortLayer();

            if (_sortLayerName != "")
            {
                return;
            }

            _sortLayerName = _parentMotion.Root.Parameter.SortLayerName;
        }

        //***************************************************************
        //
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetSortOffset(int sortOffset)
        {
            base.SetSortOffset(sortOffset);

            _UpdateSortOrder();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetSortLayer(string sortLayerName)
        {
            base.SetSortLayer(sortLayerName);

            _UpdateSortLayer();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetColliderThrough(bool through, bool affectChildren)
        {
            base.SetColliderThrough(through, affectChildren);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetColliderThicknessOffset(float thicknessOffset, bool affectChildren)
        {
            base.SetColliderThicknessOffset(thicknessOffset, affectChildren);

            _UpdateColliderThickness(false);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateColliderThickness(bool affectChildren)
        {
            base._UpdateColliderThickness(affectChildren);

            if (_existCollider == 1)
            {
                BoxCollider boxCollider = _collider as BoxCollider;

                if (boxCollider == null)
                {
                    return;
                }

                boxCollider.size = new Vector3(boxCollider.size.x, boxCollider.size.y, _root.DefaultColliderThickness + _colliderThicknessOffset);
            }
            else if (_existCollider == 2)
            {

            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetEnableCollider(bool enable, bool affectChildren)
        {
            base.SetEnableCollider(enable, affectChildren);

            if (_existCollider == 1)
            {
                if (_visibleInHierarchy)
                {
                    _collider.enabled = enable;
                }
                else
                {
                    _collider.enabled = false;
                }
            }
            else if (_existCollider == 2)
            {
                if (_visibleInHierarchy)
                {
                    _collider2D.enabled = enable;
                }
                else
                {
                    _collider2D.enabled = false;
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetSubCollider(Collider subCollider, bool affectChildren)
        {
            base.SetSubCollider(subCollider, affectChildren);

            _existSubCollider = 0;

            if (_existCollider != 1)
            {
                return;
            }

            if (_collider == null)
            {
                return;
            }

            if (subCollider == null)
            {
                return;
            }

            _existSubCollider = 1;
            _subCollider = subCollider;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public string GetFlagValue(int flagNo)
        {
            if (!_parameter.FlagTable.Contains(flagNo))
            {
                return "";
            }

            return _parameter.FlagTable[flagNo] as string;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void SetBaseColor(Color value)
        {
            _baseColor = new Color(value.r, value.g, value.b, _baseColor.a);

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void SetBaseAlpha(float alpha)
        {
            _baseColor.a = alpha;

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void SetBaseColorOffset(Color value)
        {
            _baseColorOffset = new Color(value.r, value.g, value.b, _baseColorOffset.a);

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public virtual void SetBaseAlphaOffset(float value)
        {
            _baseColorOffset.a = value;

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetBlurQuality(int blurQuality, int blurPrecision, bool affectChildren)
        {
            base.SetBlurQuality(blurQuality, blurPrecision, affectChildren);

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetBlurValue(Vector2 blurValue, bool affectChildren)
        {
            base.SetBlurValue(blurValue, affectChildren);

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateScreenSize()
        {
            base._UpdateScreenSize();

            _UpdatePlaceOffset();
        }
    }
}
