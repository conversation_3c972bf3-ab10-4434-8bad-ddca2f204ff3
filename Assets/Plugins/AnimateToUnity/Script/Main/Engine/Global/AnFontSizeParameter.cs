using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;

namespace AnimateToUnity
{
    [Serializable]
    public class AnFontSizeParameter
    {
        //=============================================================================================
        // Variables 
        //=============================================================================================

        [SerializeField]
        private int _fontSize = 0;

        [SerializeField]
        private float _leftAlignOffset = 0;

        [SerializeField]
        private float _centerAlignOffset = 0;

        [SerializeField]
        private float _rightAlignOffset = 0;

        [SerializeField]
        private float _upperAnchorOffset = 0;

        [SerializeField]
        private float _middleAnchorOffset = 0;

        [SerializeField]
        private float _lowerAnchorOffset = 0;

        [SerializeField]
        private float _lineSpaceOffset = 0;

        [SerializeField]
        private int _sizeOffset = 0;

        [SerializeField]
        private Vector2 _iconOffset = Vector2.zero;

        [SerializeField]
        private float _iconSizeOffset = 0;

        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read only
        /// </summary>
        public int FontSize
        {
            get { return _fontSize; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float UpperAnchorOffset
        {
            get { return _upperAnchorOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float MiddleAnchorOffset
        {
            get { return _middleAnchorOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float LowerAnchorOffset
        {
            get { return _lowerAnchorOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float LeftAlignOffset
        {
            get { return _leftAlignOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float CenterAlignOffset
        {
            get { return _centerAlignOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float RightAlignOffset
        {
            get { return _rightAlignOffset; }
        }

        /// <summary>
		/// Read only
		/// </summary>
		public float LineSpaceOffset
        {
            get { return _lineSpaceOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public int SizeOffset
        {
            get { return _sizeOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Vector2 IconOffset
        {
            get { return _iconOffset; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float IconSizeOffset
        {
            get { return _iconSizeOffset; }
        }
    }
}