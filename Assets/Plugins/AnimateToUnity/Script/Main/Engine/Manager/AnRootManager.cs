using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity.Utility;

namespace AnimateToUnity
{
    public sealed class AnRootManager : AnMonoSingleton<AnRootManager>
    {
        //=============================================================================================
        // Inner class
        //=============================================================================================
        //URP:置き換え対応
        /*
        private class SharedMaterialInfo
        {
            public int _refCount;
            public Material _material;
        }
        */

        //=============================================================================================
        // Variables
        //=============================================================================================

        private string _unityVersion;

        private string _deviceModel;

        private AnUIManager _uiManager;

        private List<AnRoot> _rootList;
        private Hashtable _rootTable;
        private List<AnRoot> _tempRootList;

        private Hashtable _rootParameterTable;
        private List<AnRootParameter> _tempRootParameterList;

        private Hashtable _meshParameterTable;
        private List<AnMeshParameter> _tempMeshParameterList;

        private Hashtable _planeShaderTable;
        private List<string> _planeShaderPathList;
        private List<AnShaderTypes> _planeShaderTypeList;

        private Hashtable _planeA8ShaderTable;

        private Hashtable _planeNoAlphaTexShaderTable;
        private Dictionary<AnShaderVariantTypes, Dictionary<Shader, Shader>> _planeShaderVariantTables;

        private string _localizeTarget = null;

        private Hashtable _fontShaderTable = null;
        private Hashtable _fontIconShaderTable = null;

        //URP:置き換え対応
        /*
        private Hashtable _fontMaterialTable = null;
        private Hashtable _fontIconMaterialTable = null;
        */

        private Material _tempMaterial00 = null;
        private Material _tempMaterial01 = null;

        private int _maxLayerCount = 32;
        private List<int> _layerBitFlagList = null;
        private List<string> _layerNameList = null;
        private Hashtable _layerTableByBitFlagKey = null;
        private Hashtable _layerTableByNameKey = null;
        private List<int> _activeLayerBitFlagList = null;
        private Hashtable _activeLayerTableByBitFlagKey = null;
        
        private List<int> _sortingLayerIndexList = null;
        private List<string> _sortingLayerNameList = null;
        private Hashtable _sortingLayerTableByIndexKey = null;
        private Hashtable _sortingLayerTableByNameKey = null;

        private Hashtable _gaussianBlurValueTable = null;

        //URP:置き換え対応
        /*
        private Hashtable _sharedMaterialTable = null;
        */

        private AnGlobalData _globalData;
        private bool _existGlobalData;

        [HideInInspector]
        public float _screenWidth = 0;

        [HideInInspector]
        public float _screenHeight = 0;

        [HideInInspector]
        public float _displayWidth = 0;

        [HideInInspector]
        public float _displayHeight = 0;

        [HideInInspector]
        public Rect _screenSafeArea = Rect.zero;

        [HideInInspector]
        public float _screenTopMarginPercent = 0;

        [HideInInspector]
        public float _screenBottomMarginPercent = 0;

        [HideInInspector]
        public float _screenLeftMarginPercent = 0;

        [HideInInspector]
        public float _screenRightMarginPercent = 0;

        [HideInInspector]
        public Vector2 _screenMaxWideSize = Vector2.zero;

        [HideInInspector]
        public float _screenMaxWideAspect = 0;

        [HideInInspector]
        public Vector2 _screenMaxNarrowSize = Vector2.zero;

        [HideInInspector]
        public float _screenMaxNarrowAspect = 0;

        [HideInInspector]
        public AnScreenSizeParameter _targetScreenSizeParameter = null;

        private float _prevScreenWidth = 0;
        private float _prevScreenHeight = 0;

        [HideInInspector]
        public float _currentScreenAspect = 0;

        [HideInInspector]
        public bool _screenSizeChangeFlag = false;

        [HideInInspector]
        public float _currentTime;
        private float _prevTime;

        [HideInInspector]
        public float _currentOneFrameTime;
        private int _currentTargetFrameRate = 0;
        private float _prevTargetFrameRate = int.MinValue;

        [HideInInspector]
        public float _currentDeltaTime;
        private float _customTimeScale;

        private List<string> _horizontalAxisNameList;
        private List<string> _verticalAxisNameList;
        private List<string> _subumitButtonNameList;
        private List<string> _cancelButtonNameList;
        
        private bool _useDebugComponent = false;
        private bool _useDebugLog = false;

        // 画面比率(今のUnityEngine.Screenのサイズにこれを欠けると想定の画面サイズになるように).
        private float _screenRate = 1.0f;

        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read only
        /// </summary>
        public string UnityVersion
        {
            get { return _unityVersion; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public string DeviceModel
        {
            get { return _deviceModel; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public AnUIManager UIManager
        {
            get { return _uiManager; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Hashtable PlaneShadarTable
        {
            get { return _planeShaderTable; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Hashtable PlaneA8ShadarTable
        {
            get { return _planeA8ShaderTable; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Hashtable PlaneNoAlphaTexShadarTable
        {
            get { return _planeNoAlphaTexShaderTable; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public string LocalizeTarget
        {
            get { return _localizeTarget; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Hashtable FontShadarTable
        {
            get { return _fontShaderTable; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public Hashtable FontIconShadarTable
        {
            get { return _fontIconShaderTable; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public AnGlobalData GlobalData
        {
            get { return _globalData; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public bool ExistGlobalData
        {
            get { return _existGlobalData; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float ScreenWidth
        {
            get { return _screenWidth; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float ScreenHeight
        {
            get { return _screenHeight; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float CurrentDeltaTime
        {
            get { return _currentDeltaTime; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float CurrentTime
        {
            get { return _currentTime; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public float PrevTime
        {
            get { return _prevTime; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public bool UseDebugComponent
        {
            get { return _useDebugComponent; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public bool UseDebugLog
        {
            get { return _useDebugLog; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public List<int> LayerBitFlagList
        {
            get { return _layerBitFlagList; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public List<string> LayerNameList
        {
            get { return _layerNameList; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public List<int> ActiveLayerBitFlagList
        {
            get { return _activeLayerBitFlagList; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public List<int> SortingLayerIndexList
        {
            get { return _sortingLayerIndexList; }
        }

        /// <summary>
        /// Read only
        /// </summary>
        public List<string> SortingLayerNameList
        {
            get { return _sortingLayerNameList; }
        }


        /// <summary>
        /// 両方とも外に開放するのであまり意味はないが口をしぼるため用意.
        /// </summary>
        public float ScreenRate
        {
            get { return _screenRate; }
            set { _screenRate = value; }
        }

        //=============================================================================================
        // Method
        //=============================================================================================

        //***********************************************************
        // MonoBehaviour
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// Update
        /// </summary>
        /// -----------------------------------------
        private void Update()
        {
            _currentTargetFrameRate = Application.targetFrameRate;

            if (_currentTargetFrameRate != _prevTargetFrameRate)
            {
                _currentOneFrameTime = 1.0f / (float)_currentTargetFrameRate;
            }

            _currentTime = Time.realtimeSinceStartup;

            if (Time.deltaTime <= float.Epsilon)
            {
                _currentDeltaTime = (_currentTime - _prevTime) * _customTimeScale;
            }
            else
            {
                _currentDeltaTime = Time.deltaTime;
            }

            _UpdateScreenSize();

            if (_rootList != null)
            {
                for (int i = 0; i < _rootList.Count; i++)
                {
                    _rootList[i]._UpdateRoot(true);
                }
            }

            _uiManager._Update();

            _prevTime = _currentTime;
            _prevTargetFrameRate = _currentTargetFrameRate;
        }

        //***********************************************************
        // Override
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _OnInitialize()
        {
            base._OnInitialize();

            AnLog._Log(AnLogTypes.Initialize, AnLogColorTypes.color_aaaaaaff, AnLogTitleTypes.FlRootManager, this.gameObject);

            this.name = "_FlRootManager";

            _unityVersion = Application.unityVersion.ToLower();

#if UNITY_IOS && !UNITY_EDITOR
            _deviceModel = UnityEngine.iOS.Device.generation.ToString().ToLower();

            if(_deviceModel.Contains("unknown"))
            {
                _deviceModel = SystemInfo.deviceModel.ToLower();
            }
#else
            _deviceModel = SystemInfo.deviceModel.ToLower();
#endif

            _uiManager = new AnUIManager();
            _uiManager._Initilaize();

            if (_planeShaderVariantTables == null)
            {
                _planeShaderVariantTables = new Dictionary<AnShaderVariantTypes, Dictionary<Shader, Shader>>();
            }

            _customTimeScale = 1.0f;
            _prevTime = 0.0f;
            
            _horizontalAxisNameList = new List<string>();
            _horizontalAxisNameList.Add("Horizontal");

            _verticalAxisNameList = new List<string>();
            _verticalAxisNameList.Add("Vertical");

            _subumitButtonNameList = new List<string>();
            _subumitButtonNameList.Add("Submit");

            _cancelButtonNameList = new List<string>();
            _cancelButtonNameList.Add("Cancel");

            _CreatePlaneMainShaderTable();
            _CreatePlaneA8ShaderTable();
            _CreatePlaneNoTexAlphaShaderTable();

            _LoadGlobalData();

            _InitializeScreenSize();
            _UpdateScreenSize();

            _UpdateLayerTable();
            _UpdateSortingLayerTable();
            _OptimizeActiveLayerTable();

            _LoadEditorSetting();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _OnLoaded()
        {
            base._OnLoaded();

            AnLog._Log(AnLogTypes.Loaded, AnLogColorTypes.color_aaaaaaff, AnLogTitleTypes.FlRootManager, this.gameObject);

            OptimizeAll();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _OnFinalize()
        {
            base._OnFinalize();

            AnLog._Log(AnLogTypes.Finalize, AnLogColorTypes.color_aaaaaaff, AnLogTitleTypes.FlRootManager, this.gameObject);
        }

        //***********************************************************
        // Optimize
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void OptimizeAll()
        {
            _UpdateScreenSize();

            _UpdateLayerTable();
            _UpdateSortingLayerTable();

            _OptimizeRootList();

            _OptimizeRootParameterTable();
            _OptimizeMeshParameterTable();

            _OptimizeActiveLayerTable();

            _uiManager._OptimizeAll();
        }

        //***********************************************************
        // Root 
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _AddRoot(AnRoot target)
        {
            if (_rootTable == null)
            {
                _rootTable = new Hashtable();
            }

            if (_rootList == null)
            {
                _rootList = new List<AnRoot>();
            }

            if (_ExistRoot(target))
            {
                return;
            }

            _rootList.Add(target);
            _rootTable.Add(target, target);

            _AddActiveLayerTable(target.gameObject);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _RemoveRoot(AnRoot target)
        {
            if (_rootTable == null)
            {
                _rootTable = new Hashtable();
            }

            if (_rootList == null)
            {
                _rootList = new List<AnRoot>();
            }

            if (!_ExistRoot(target))
            {
                return;
            }

            _rootList.Remove(target);
            _rootTable.Remove(target);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private bool _ExistRoot(AnRoot target)
        {
            if (_rootTable.ContainsKey(target))
            {
                return true;
            }

            return false;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _OptimizeRootList()
        {
            if (_rootTable == null)
            {
                _rootTable = new Hashtable();
            }

            if (_rootList == null)
            {
                _rootList = new List<AnRoot>();
            }

            if (_tempRootList == null)
            {
                _tempRootList = new List<AnRoot>();
            }

            _tempRootList.Clear();
            for (int i = 0; i < _rootList.Count; i++)
            {
                AnRoot root = _rootList[i];

                if (root == null)
                {
                    continue;
                }

                if (root.gameObject == null)
                {
                    continue;
                }

                _tempRootList.Add(root);
            }

            _rootList.Clear();
            _rootTable.Clear();
            for (int i = 0; i < _tempRootList.Count; i++)
            {
                AnRoot root = _tempRootList[i];

                _rootList.Add(root);
                _rootTable.Add(root, root);
            }

            _tempRootList.Clear();
        }

        //***********************************************************
        // Root Parameter
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnRootParameter _GetRootParameter(AnRootParameter rootParameter)
        {
            if (_rootParameterTable == null)
            {
                _rootParameterTable = new Hashtable();
            }

            if (_rootParameterTable.ContainsKey(rootParameter.ID))
            {
                AnRootParameter thisParam = _rootParameterTable[rootParameter.ID] as AnRootParameter;

                if (thisParam != null)
                {
                    return thisParam;
                }
                else
                {
                    _rootParameterTable.Remove(rootParameter.ID);
                }
            }

            _rootParameterTable.Add(rootParameter.ID, rootParameter);

            rootParameter._Initialize();

            return rootParameter;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _OptimizeRootParameterTable()
        {
            if (_rootTable == null)
            {
                return;
            }

            if (_rootParameterTable == null)
            {
                return;
            }

            if (_tempRootParameterList == null)
            {
                _tempRootParameterList = new List<AnRootParameter>();
            }

            _tempRootParameterList.Clear();
            for (int p = 0; p < _rootList.Count; p++)
            {
                if (_rootList[p] == null)
                {
                    continue;
                }

                if (_rootList[p].Parameter == null)
                {
                    continue;
                }

                bool exist = false;
                for (int q = 0; q < _tempRootParameterList.Count; q++)
                {
                    if (_tempRootParameterList[q] == _rootList[p].Parameter)
                    {
                        exist = true;
                        break;
                    }
                }

                if (exist)
                {
                    continue;
                }

                _tempRootParameterList.Add(_rootList[p].Parameter);
            }

            _rootParameterTable.Clear();
            for (int i = 0; i < _tempRootParameterList.Count; i++)
            {
                if (_rootParameterTable.ContainsKey(_tempRootParameterList[i].ID))
                {
                    continue;
                }

                _rootParameterTable.Add(_tempRootParameterList[i].ID, _tempRootParameterList[i]);
            }

            _tempRootParameterList.Clear();
        }

        //***********************************************************
        // Mesh Parameter
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnMeshParameter _GetMeshParameter(AnMeshParameter meshParameter, bool isWriteAlpha=false)
        {
            if (_meshParameterTable == null)
            {
                _meshParameterTable = new Hashtable();
            }

            if (_meshParameterTable.ContainsKey(meshParameter.ID))
            {
                AnMeshParameter thisParam = _meshParameterTable[meshParameter.ID] as AnMeshParameter;

                if (thisParam != null)
                {
                    //URP:置き換え対応
                    //Destroyタイミングで破棄されても
                    //テーブルには残り続けるので必要に応じて初期化する必要がある
                    thisParam._Initialize();
                    return thisParam;
                }
                else
                {
                    _meshParameterTable.Remove(meshParameter.ID);
                }
            }

            _meshParameterTable.Add(meshParameter.ID, meshParameter);

            meshParameter._Initialize(isWriteAlpha);

            return meshParameter;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _OptimizeMeshParameterTable()
        {
            if (_rootTable == null)
            {
                return;
            }

            if (_meshParameterTable == null)
            {
                return;
            }

            if (_tempMeshParameterList == null)
            {
                _tempMeshParameterList = new List<AnMeshParameter>();
            }

            _tempMeshParameterList.Clear();

            for (int p = 0; p < _rootList.Count; p++)
            {
                AnRoot root = _rootList[p];

                if (root == null)
                {
                    continue;
                }

                if (root.MeshParameterGroup == null)
                {
                    continue;
                }

                if (root.MeshParameterGroup.MeshParameterList == null)
                {
                    continue;
                }

                for (int q = 0; q < root.MeshParameterGroup._meshParameterList.Count; q++)
                {
                    AnMeshParameter meshParam = root.MeshParameterGroup._meshParameterList[q];

                    if (meshParam == null)
                    {
                        continue;
                    }

                    bool exist = false;
                    for (int r = 0; r < _tempMeshParameterList.Count; r++)
                    {
                        if (_tempMeshParameterList[r] == meshParam)
                        {
                            exist = true;
                            break;
                        }
                    }

                    if (exist)
                    {
                        continue;
                    }

                    _tempMeshParameterList.Add(meshParam);
                }
            }

            _meshParameterTable.Clear();
            for (int p = 0; p < _tempMeshParameterList.Count; p++)
            {
                _GetMeshParameter(_tempMeshParameterList[p]);
            }

            _tempMeshParameterList.Clear();
        }

        //***********************************************************
        // Other
        //***********************************************************

        //***********************************************************
        // Plane Main Shader
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreatePlaneMainShaderTable()
        {
            if (_planeShaderTable != null)
            {
                return;
            }

            _planeShaderTable = new Hashtable();

            _planeShaderPathList = new List<string>();

            _planeShaderPathList.Add(AnValue.ShaderNormalPath);
            _planeShaderPathList.Add(AnValue.ShaderAddPath);
            _planeShaderPathList.Add(AnValue.ShaderSubPath);
            _planeShaderPathList.Add(AnValue.ShaderMultiplyPath);
            _planeShaderPathList.Add(AnValue.ShaderHardLightPath);
            _planeShaderPathList.Add(AnValue.ShaderInvertPath);
            _planeShaderPathList.Add(AnValue.ShaderOpaquePath);
            _planeShaderPathList.Add(AnValue.ShaderGrayscalePath);
            _planeShaderPathList.Add(AnValue.ShaderMaskPath);
            _planeShaderPathList.Add(AnValue.ShaderAlphaMaskPath);
            _planeShaderPathList.Add(AnValue.ShaderAlphaMaskMultiplyPath);
			_planeShaderPathList.Add(AnValue.ShaderStencilMaskPath);
            _planeShaderPathList.Add(AnValue.ShaderStencilAlphaMaskPath);
            _planeShaderPathList.Add(AnValue.ShaderObjectMaskPath);
            _planeShaderPathList.Add(AnValue.ShaderObjectAlphaMaskPath);
            _planeShaderPathList.Add(AnValue.ShaderNormal3DPath);
            _planeShaderPathList.Add(AnValue.ShaderAdd3DPath);
            _planeShaderPathList.Add(AnValue.ShaderNormalBlurPath);
            _planeShaderPathList.Add(AnValue.ShaderAddBlurPath);
            _planeShaderPathList.Add(AnValue.ShaderMultiplyBlurPath);
            _planeShaderPathList.Add(AnValue.ShaderGrayscaleBlurPath);
            _planeShaderPathList.Add(AnValue.ShaderNormalHorizontalFadePath);
            _planeShaderPathList.Add(AnValue.ShaderNormalVerticalFadePath);

            _planeShaderTypeList = new List<AnShaderTypes>();

            _planeShaderTypeList.Add(AnShaderTypes.Normal);
            _planeShaderTypeList.Add(AnShaderTypes.Add);
            _planeShaderTypeList.Add(AnShaderTypes.Sub);
            _planeShaderTypeList.Add(AnShaderTypes.Multiply);
            _planeShaderTypeList.Add(AnShaderTypes.HardLight);
            _planeShaderTypeList.Add(AnShaderTypes.Invert);
            _planeShaderTypeList.Add(AnShaderTypes.Opaque);
            _planeShaderTypeList.Add(AnShaderTypes.Grayscale);
            _planeShaderTypeList.Add(AnShaderTypes.Mask);
            _planeShaderTypeList.Add(AnShaderTypes.AlphaMask);
            _planeShaderTypeList.Add(AnShaderTypes.AlphaMaskMultiply);
			_planeShaderTypeList.Add(AnShaderTypes.StencilMask);
            _planeShaderTypeList.Add(AnShaderTypes.StencilAlphaMask);
            _planeShaderTypeList.Add(AnShaderTypes.ObjectMask);
            _planeShaderTypeList.Add(AnShaderTypes.ObjectAlphaMask);
            _planeShaderTypeList.Add(AnShaderTypes.Normal3D);
            _planeShaderTypeList.Add(AnShaderTypes.Add3D);
            _planeShaderTypeList.Add(AnShaderTypes.NormalBlur);
            _planeShaderTypeList.Add(AnShaderTypes.AddBlur);
            _planeShaderTypeList.Add(AnShaderTypes.MultiplyBlur);
            _planeShaderTypeList.Add(AnShaderTypes.GrayscaleBlur);
            _planeShaderTypeList.Add(AnShaderTypes.NormalHorizontalFade);
            _planeShaderTypeList.Add(AnShaderTypes.NormalVerticalFade);

            for (int i = 0; i < _planeShaderPathList.Count; i++)
            {
                Shader thisShader = Shader.Find(_planeShaderPathList[i]);

                _planeShaderTable.Add(_planeShaderTypeList[i], thisShader);

                AddPlaneAlphaFadeShaderTable(_planeShaderPathList[i], thisShader);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public Shader _GetPlaneMainShader(AnShaderTypes targetShader = AnShaderTypes.Normal)
        {
            _CreatePlaneMainShaderTable();

            if (_planeShaderTable.ContainsKey(targetShader))
            {
                return _planeShaderTable[targetShader] as Shader;
            }

            return _planeShaderTable[AnShaderTypes.Normal] as Shader;
        }

        //***********************************************************
        // Plane A8 Shader
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreatePlaneA8ShaderTable()
        {
            if (_planeA8ShaderTable != null)
            {
                return;
            }

            if (_planeShaderPathList == null)
            {
                return;
            }

            if (_planeShaderTypeList == null)
            {
                return;
            }

            _planeA8ShaderTable = new Hashtable();

            List<string> shaderPathList = new List<string>();
            List<AnShaderTypes> shaderTypeList = new List<AnShaderTypes>();

            for (int i = 0; i < _planeShaderPathList.Count; i++)
            {
				shaderPathList.Add(_planeShaderPathList[i].Replace(AnValue.ShaderMainString, AnValue.ShaderA8String));

				shaderTypeList.Add(_planeShaderTypeList[i]);
            }

            for (int i = 0; i < shaderPathList.Count; i++)
            {
                Shader thisShader = Shader.Find(shaderPathList[i]);

                if (thisShader == null)
                {
                    thisShader = Shader.Find(shaderPathList[0]);

                    if (thisShader == null)
                    {
                        continue;
                    }
                }

                _planeA8ShaderTable.Add(shaderTypeList[i], thisShader);
            }

            shaderPathList = null;
            shaderTypeList = null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public Shader _GetPlaneA8Shader(AnShaderTypes targetShader = AnShaderTypes.Normal)
        {
            _CreatePlaneA8ShaderTable();

            if (_planeA8ShaderTable.ContainsKey(targetShader))
            {
                return _planeA8ShaderTable[targetShader] as Shader;
            }

            return _planeA8ShaderTable[AnShaderTypes.Normal] as Shader;
        }

        //***********************************************************
        // Plane NoAlphaTex Shader
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreatePlaneNoTexAlphaShaderTable()
        {
            if (_planeNoAlphaTexShaderTable != null)
            {
                return;
            }

            if (_planeShaderPathList == null)
            {
                return;
            }

            if (_planeShaderTypeList == null)
            {
                return;
            }

            _planeNoAlphaTexShaderTable = new Hashtable();

            List<string> shaderPathList = new List<string>();
            List<AnShaderTypes> shaderTypeList = new List<AnShaderTypes>();

            for (int i = 0; i < _planeShaderPathList.Count; i++)
            {
                shaderPathList.Add(_planeShaderPathList[i].Replace(AnValue.ShaderMainString, AnValue.ShaderNoTexAlphaString));
                shaderTypeList.Add(_planeShaderTypeList[i]);
            }

            for (int i = 0; i < shaderPathList.Count; i++)
            {
                Shader thisShader = Shader.Find(shaderPathList[i]);

                if (thisShader == null)
                {
                    thisShader = Shader.Find(shaderPathList[0]);

                    if (thisShader == null)
                    {
                        continue;
                    }
                }

                _planeNoAlphaTexShaderTable.Add(shaderTypeList[i], thisShader);

                AddPlaneAlphaFadeShaderTable(shaderPathList[i], thisShader);
            }

            shaderPathList = null;
            shaderTypeList = null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public Shader _GetPlaneNoTexAlphaShader(AnShaderTypes targetShader = AnShaderTypes.Normal)
        {
            _CreatePlaneNoTexAlphaShaderTable();

            if (_planeNoAlphaTexShaderTable.ContainsKey(targetShader))
            {
                return _planeNoAlphaTexShaderTable[targetShader] as Shader;
            }

            return _planeNoAlphaTexShaderTable[AnShaderTypes.Normal] as Shader;
        }

        //***********************************************************
        // Text Main Shader
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateTextMainShaderTable()
        {
            if (_fontShaderTable != null)
            {
                return;
            }

            _fontShaderTable = new Hashtable();

            List<string> shaderPathList = new List<string>();

            shaderPathList.Add(AnValue.ShaderTextNormalPath);
            shaderPathList.Add(AnValue.ShaderTextAddPath);
            shaderPathList.Add(AnValue.ShaderTextMultiplyPath);
            shaderPathList.Add(AnValue.ShaderTextGrayscalePath);
            shaderPathList.Add(AnValue.ShaderTextStencilAlphaMaskPath);
            shaderPathList.Add(AnValue.ShaderTextNormalGradationPath);
            shaderPathList.Add(AnValue.ShaderTextAddGradationPath);
            shaderPathList.Add(AnValue.ShaderTextMultiplyGradationPath);
            shaderPathList.Add(AnValue.ShaderTextGrayscaleGradationPath);

            List<AnShaderTypes> shaderTypeList = new List<AnShaderTypes>();

            shaderTypeList.Add(AnShaderTypes.Normal);
            shaderTypeList.Add(AnShaderTypes.Add);
            shaderTypeList.Add(AnShaderTypes.Multiply);
            shaderTypeList.Add(AnShaderTypes.Grayscale);
            shaderTypeList.Add(AnShaderTypes.StencilAlphaMask);
            shaderTypeList.Add(AnShaderTypes.NormalGradation);
            shaderTypeList.Add(AnShaderTypes.AddGradation);
            shaderTypeList.Add(AnShaderTypes.MultiplyGradation);
            shaderTypeList.Add(AnShaderTypes.GrayscaleGradation);

            for (int i = 0; i < shaderPathList.Count; i++)
            {
                Shader thisShader = Shader.Find(shaderPathList[i]);

                _fontShaderTable.Add(shaderTypeList[i], thisShader);
            }

            shaderPathList = null;
            shaderTypeList = null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public Shader _GetTextMainShader(AnShaderTypes targetShader = AnShaderTypes.Normal)
        {
            _CreateTextMainShaderTable();

            if (_fontShaderTable.ContainsKey(targetShader))
            {
                return _fontShaderTable[targetShader] as Shader;
            }

            return _fontShaderTable[AnShaderTypes.Normal] as Shader;
        }

        //***********************************************************
        // Text Icon Shader
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateTextIconShaderTable()
        {
            if (_fontIconShaderTable != null)
            {
                return;
            }

            _fontIconShaderTable = new Hashtable();

            List<string> shaderPathList = new List<string>();

            shaderPathList.Add(AnValue.ShaderTextIconNormalPath);
            shaderPathList.Add(AnValue.ShaderTextIconAddPath);
            shaderPathList.Add(AnValue.ShaderTextIconMultiplyPath);
            shaderPathList.Add(AnValue.ShaderTextIconGrayscalePath);

            List<AnShaderTypes> shaderTypeList = new List<AnShaderTypes>();

            shaderTypeList.Add(AnShaderTypes.Normal);
            shaderTypeList.Add(AnShaderTypes.Add);
            shaderTypeList.Add(AnShaderTypes.Multiply);
            shaderTypeList.Add(AnShaderTypes.Grayscale);

            for (int i = 0; i < shaderPathList.Count; i++)
            {
                Shader thisShader = Shader.Find(shaderPathList[i]);

                _fontIconShaderTable.Add(shaderTypeList[i], thisShader);
            }

            shaderPathList = null;
            shaderTypeList = null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public Shader _GetTextIconShader(AnShaderTypes targetShader = AnShaderTypes.Normal)
        {
            _CreateTextIconShaderTable();

            if (_fontIconShaderTable.ContainsKey(targetShader))
            {
                return _fontIconShaderTable[targetShader] as Shader;
            }

            return _fontIconShaderTable[AnShaderTypes.Normal] as Shader;
        }


        //***********************************************************
        // Variant
        //***********************************************************

        /// <summary>
        /// Fade処理を行うシェーダーの追加
        /// </summary>
        /// <remarks>
        /// 現在はFadeのみの対応だが他に追加する場合はFadeによらず拡張できる形にする事
        /// </remarks>
        private void AddPlaneAlphaFadeShaderTable(string baseShaderName, Shader key)
        {
            AddShaderVariantTable(AnShaderVariantTypes.HorizontalFade, key, baseShaderName + AnValue.ShaderHorizontalFadeString);
            AddShaderVariantTable(AnShaderVariantTypes.VerticalFade, key, baseShaderName + AnValue.ShaderVerticalFadeString);
        }

        /// <summary>
        /// バリエーションとして辞書に登録する
        /// </summary>
        private void AddShaderVariantTable(AnShaderVariantTypes variantType, Shader key, string shaderName)
        {
            var shader = Shader.Find(shaderName);
            if (shader == null) return; // 存在しない場合は追加しない

            if (!_planeShaderVariantTables.TryGetValue(variantType, out var dict))
            {
                dict = new Dictionary<Shader, Shader>();
                _planeShaderVariantTables.Add(variantType, dict);
            }

            dict.Add(key, shader);
        }

        /// <summary>
        /// VariantTypeからShaderペアを取得し、内部に持っていたら入れ替える
        /// </summary>
        public bool TryGetPlaneVariantShader(AnShaderVariantTypes variantType, Shader target, out Shader shader)
        {
            shader = target;

            if (!_planeShaderVariantTables.TryGetValue(variantType, out var dict))
            {
                return false;
            }

            if (!dict.TryGetValue(target, out var result))
            {
                return false;
            }

            shader = result;

            return true;
        }

        //***********************************************************
        // Font
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public Font _GetFont(string fontName, bool fromCommon)
        {
            if (fontName == null)
            {
                return null;
            }

            if (!_existGlobalData)
            {
                return null;
            }

            Font font = null;

            if(fromCommon)
            {
                font = _globalData._GetFontFromCommon(fontName) as Font;
            }
            else
            {
                font = _globalData._GetFont(fontName) as Font;
            }

            if (font == null)
            {
                return null;
            }

            return font;
        }

        public bool _GetFontMaterial(Hashtable hashTable, string fontName, AnShaderTypes shaderType, int stencilRef, int baseStencilRef, AnStencilCompareFuncTypes stencilCompareFunc, bool useCommon, ref Material fontMaterial)
        {
            if (!_existGlobalData)
            {
                return false;
            }

            _CreateFontMaterial(hashTable, fontName, shaderType, stencilRef, baseStencilRef, stencilCompareFunc, useCommon, ref fontMaterial);

            if (fontMaterial == null)
            {
                return false;
            }

            return true;
        }

#if GALLOP
        /// -----------------------------------------
        /// <summary>
        /// フォントを置き換える
        /// </summary>
        /// <param name="fontName"></param>
        /// <param name="font"></param>
        /// -----------------------------------------
        public void _ReplaceFont(string fontName, Font font)
        {
            if (string.IsNullOrEmpty(fontName))
            {
                return;
            }

            if (!_existGlobalData)
            {
                return;
            }

            _globalData._ReplaceFont(fontName, font);
        }
#endif

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateFontMaterial(Hashtable hashTable,string fontName, AnShaderTypes shaderType, int stencilRef, int baseStencilRef, AnStencilCompareFuncTypes stencilCompareFunc, bool useCommon, ref Material fontMaterial)
		{
			if (!_existGlobalData)
			{
				return;
			}

            //URP:置き換え対応
            /*
            if (_fontMaterialTable == null)
            {
                _fontMaterialTable = new Hashtable();
            }
            */

            Font font = null;

            if(useCommon)
            {
                font = _globalData._GetFontFromCommon(fontName);
            }
            else
            {
                font = _globalData._GetFont(fontName);
            }

            if (font == null)
            {
                return;
            }

            AnStencilCompareFuncTypes thisStencilCompareFunc = AnUtilityMaterial.GetStencilCompareType(shaderType, stencilRef, baseStencilRef, stencilCompareFunc);
            
            string fontMatKey = fontName + "_" + font.name + "_" + AnUtilityMaterial.GetMaterialKey(shaderType, stencilRef, thisStencilCompareFunc);

            //URP:置き換え対応
            /*
            SharedMaterialInfo info;
            if (_fontMaterialTable.ContainsKey(fontMatKey))
			{
                info = _fontMaterialTable[fontMatKey] as SharedMaterialInfo;
                fontMaterial = info._material;

                if(fontMaterial != null)
                {
                    //引数で渡された方に未登録であれば登録する
                    if(!hashTable.ContainsKey(fontMatKey))
                    {
                        info._refCount++;
                        hashTable.Add(fontMatKey, fontMatKey);
                    }
                    return;
                }

                _fontMaterialTable.Remove(fontMatKey);
			}

            info = new SharedMaterialInfo() { _refCount = 1, _material = new Material(font.material) };

            fontMaterial = info._material;
            */

            if(hashTable.ContainsKey(fontMatKey))
            {
                fontMaterial = hashTable[fontMatKey] as Material;
                if(fontMaterial != null)
                {
                    //既に共有に登録済みなのでそのまま返す
                    return;
                }

                //fontMaterialがnullで死んでいるのでキーは削除しておく
                hashTable.Remove(fontMatKey);
            }

            fontMaterial = new Material(font.material);

			fontMaterial.name = fontMatKey;

			fontMaterial.shader = _GetTextMainShader(shaderType);

			fontMaterial.SetFloat(AnValue.ShaderParamStencilRef, stencilRef);

            fontMaterial.SetFloat(AnValue.ShaderParamStencilComp, (int)thisStencilCompareFunc);
            
            //URP:置き換え対応
			//_fontMaterialTable.Add(fontMatKey, info);

            //引数で渡されたものにも登録する
            hashTable.Add(fontMatKey, fontMaterial);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
		public bool _CloneFontMaterial(Hashtable hashTable, Material baseFontMaterial, string id, ref Material fontMaterial)
        {
            if (baseFontMaterial == null)
            {
                return false;
            }

            //URP:置き換え対応
            /*
            if (_fontMaterialTable == null)
            {
                _fontMaterialTable = new Hashtable();
            }
            */

            string thisKey = baseFontMaterial.name + AnValue.CloneString + id;

            //URP:置き換え対応
            /*
            SharedMaterialInfo info;
            if (_fontMaterialTable.ContainsKey(thisKey))
            {
                info = _fontMaterialTable[thisKey] as SharedMaterialInfo;
                fontMaterial = info._material;

                if (fontMaterial != null)
                {
                    if(!hashTable.ContainsKey(thisKey))
                    {
                        info._refCount++;
                        hashTable.Add(thisKey, fontMaterial);
                    }
                    return true;
                }

                _fontMaterialTable.Remove(thisKey);
            }

            info = new SharedMaterialInfo() { _refCount = 1, _material = new Material(baseFontMaterial) };
            fontMaterial = info._material;
            fontMaterial.name = thisKey;
            */

            if(hashTable.ContainsKey(thisKey))
            {
                fontMaterial = hashTable[thisKey] as Material;
                //既に登録されている場合はCloneしない
                if (fontMaterial != null)
                    return true;

                //キーに登録されているのにnullという事は何かあったので破棄しておく
                hashTable.Remove(thisKey);
            }

            fontMaterial = new Material(baseFontMaterial);
            fontMaterial.name = thisKey;

            //URP:置き換え対応
            /*
            _fontMaterialTable.Add(thisKey, info);
            */
            hashTable.Add(thisKey, fontMaterial);

            return true;
        }

        public void RemoveFontMaterial(string materialKey)
        {
            //URP:置き換え対応
            /*
            if (_fontMaterialTable == null)
            {
                return;
            }

            if (!_fontMaterialTable.ContainsKey(materialKey))
            {
                return;
            }

            var info = _fontMaterialTable[materialKey] as SharedMaterialInfo;
            info._refCount--;
            if(info._refCount <= 0)
            {
                Material.Destroy(info._material);
                _fontMaterialTable.Remove(materialKey);
            }
            */
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void AddFont(Font font)
        {
            if (!_existGlobalData)
            {
                return;
            }

            _globalData._AddFontToAddFontTable(font);
        }

        //***********************************************************
        // FontIcon
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _CloneTextIconMaterialList(Hashtable hashTable,string id, AnShaderTypes shaderType, int stencilRef, int baseStencilRef, AnStencilCompareFuncTypes stencilCompareFunc, ref Material[] materialList)
        {
            if (!_existGlobalData)
            {
                if (materialList == null)
                {
                    materialList = new Material[1];
                }

                return;
            }

            if (materialList == null)
            {
                materialList = new Material[AnRootManager.Instance.GlobalData.FontIconParameterList.Count + 1];
            }

            _tempMaterial00 = null;
            _tempMaterial01 = null;

            for(int i = 0; i < _globalData.FontIconParameterList.Count; i++)
            {
                AnFontIconParameter fontIconParam = _globalData.FontIconParameterList[i];

                if (fontIconParam == null)
                {
                    return;
                }

                if (fontIconParam.ColorTexture == null)
                {
                    return;
                }

                if (fontIconParam.AlphaTexture == null)
                {
                    return;
                }

                _GetFontIconMaterial(hashTable, fontIconParam.ColorTexture.name, shaderType, stencilRef, baseStencilRef, stencilCompareFunc, ref _tempMaterial00);
                
                _CloneFontIconMaterial(hashTable, _tempMaterial00, id, ref _tempMaterial01);

                materialList[i + 1] = _tempMaterial01;
            }
            _tempMaterial00 = null;
            _tempMaterial01 = null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private bool _GetFontIconMaterial(Hashtable hashTable, string fontIconName, AnShaderTypes shaderType, int stencilRef, int baseStencilRef, AnStencilCompareFuncTypes stencilCompareFunc, ref Material fontIconMaterial)
        {
            if (fontIconName == null)
            {
                return false;
            }

            if (!_existGlobalData)
            {
                return false;
            }

            _CreateFontIconMaterial(hashTable, fontIconName, shaderType, stencilRef, baseStencilRef, stencilCompareFunc, ref fontIconMaterial);

            if (fontIconMaterial == null)
            {
                return false;
            }

            return true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateFontIconMaterial(Hashtable hashTable,string fontIconName, AnShaderTypes shaderType, int stencilRef, int baseStencilRef, AnStencilCompareFuncTypes stencilCompareFunc, ref Material fontIconMaterial)
        {
            //URP:置き換え対応
            /*
            if (_fontIconMaterialTable == null)
            {
                _fontIconMaterialTable = new Hashtable();
            }
            */

            if (!_existGlobalData)
            {
                return;
            }

            AnFontIconParameter fontIconParam = _globalData._GetFontIconParameter(fontIconName);

            if (fontIconParam == null)
            {
                return;
            }

            if (fontIconParam.ColorTexture == null)
            {
                return;
            }

            if (fontIconParam.AlphaTexture == null)
            {
                return;
            }

            AnStencilCompareFuncTypes thisStencilCompareFunc = AnUtilityMaterial.GetStencilCompareType(shaderType, stencilRef, baseStencilRef, stencilCompareFunc);
            
            string fontIconMatKey = fontIconName + "_" + AnUtilityMaterial.GetMaterialKey(shaderType, stencilRef, thisStencilCompareFunc);

            //URP:置き換え対応
            /*
            SharedMaterialInfo info;
            if (_fontIconMaterialTable.ContainsKey(fontIconMatKey))
            {
                info = _fontIconMaterialTable[fontIconMatKey] as SharedMaterialInfo;
                fontIconMaterial = info._material;

                if (fontIconMaterial != null)
                {
                    if (!hashTable.ContainsKey(fontIconMatKey))
                    {
                        info._refCount++;
                        hashTable.Add(fontIconMatKey, fontIconMaterial);
                    }
                    return;
                }

                _fontIconMaterialTable.Remove(fontIconMatKey);
            }

            info = new SharedMaterialInfo() { _refCount = 1, _material = new Material(_GetTextIconShader(shaderType)) };
            fontIconMaterial = info._material;

            fontIconMaterial.name = fontIconMatKey;

            fontIconMaterial.SetTexture(AnValue.ShaderParamMainTex, fontIconParam.ColorTexture);
            fontIconMaterial.SetTexture(AnValue.ShaderParamAlphaTex, fontIconParam.AlphaTexture);

            fontIconMaterial.SetFloat(AnValue.ShaderParamStencilRef, stencilRef);

            fontIconMaterial.SetFloat(AnValue.ShaderParamStencilComp, (int)thisStencilCompareFunc);
            
            _fontIconMaterialTable.Add(fontIconMatKey, info);
            */

            if(hashTable.ContainsKey(fontIconMatKey))
            {
                fontIconMaterial = hashTable[fontIconMatKey] as Material;
                if (fontIconMaterial != null)
                    return;

                //登録されていたがnullになっているので既に死亡している
                hashTable.Remove(fontIconMatKey);
            }

            fontIconMaterial = new Material(_GetTextIconShader(shaderType));
            fontIconMaterial.name = fontIconMatKey;

            fontIconMaterial.SetTexture(AnValue.ShaderParamMainTex, fontIconParam.ColorTexture);
            fontIconMaterial.SetTexture(AnValue.ShaderParamAlphaTex, fontIconParam.AlphaTexture);

            fontIconMaterial.SetFloat(AnValue.ShaderParamStencilRef, stencilRef);

            fontIconMaterial.SetFloat(AnValue.ShaderParamStencilComp, (int)thisStencilCompareFunc);

            hashTable.Add(fontIconMatKey, fontIconMaterial);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private bool _CloneFontIconMaterial(Hashtable hashTable, Material baseFontIconMaterial, string id, ref Material fontIconMaterial)
        {
            if (baseFontIconMaterial == null)
            {
                return false;
            }

            //URP:置き換え対応
            /*
            if (_fontIconMaterialTable == null)
            {
                _fontIconMaterialTable = new Hashtable();
            }
            */

            string thisKey = baseFontIconMaterial.name + AnValue.CloneString + id;

            //URP:置き換え対応
            /*
            SharedMaterialInfo info;
            if (_fontIconMaterialTable.ContainsKey(thisKey))
            {
                info = _fontIconMaterialTable[thisKey] as SharedMaterialInfo;
                fontIconMaterial = info._material;

                if (fontIconMaterial != null)
                {
                    if(!hashTable.ContainsKey(thisKey))
                    {
                        info._refCount++;
                        hashTable.Add(thisKey, fontIconMaterial);
                    }
                    return true;
                }

                _fontIconMaterialTable.Remove(thisKey);
            }

            info = new SharedMaterialInfo() { _refCount = 1, _material = new Material(baseFontIconMaterial) };

            fontIconMaterial = info._material;
            fontIconMaterial.name = thisKey;

            _fontIconMaterialTable.Add(thisKey, fontIconMaterial);
            */
            if(hashTable.ContainsKey(thisKey))
            {
                fontIconMaterial = hashTable[thisKey] as Material;
                if (fontIconMaterial != null)
                    return true;

                hashTable.Remove(thisKey);
            }

            fontIconMaterial = new Material(baseFontIconMaterial);
            fontIconMaterial.name = thisKey;

            hashTable.Add(thisKey, fontIconMaterial);

            return true;
        }

        public void RemoveFontIconMaterial(string materialKey)
        {
            //URP:置き換え対応
            /*
            if(_fontIconMaterialTable == null)
            {
                return;
            }

            if(!_fontIconMaterialTable.ContainsKey(materialKey))
            {
                return;
            }

            var info = _fontIconMaterialTable[materialKey] as SharedMaterialInfo;
            info._refCount--;
            if(info._refCount <= 0)
            {
                Material.Destroy(info._material);
                _fontIconMaterialTable.Remove(materialKey);
            }
            */
        }

        //***********************************************************
        // Global Data
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _LoadGlobalData()
        {
            _existGlobalData = false;
            AnGlobalDataMediator globalDataMediator = Resources.Load<AnGlobalDataMediator>(AnValue.GlobalDataMediatorPath);

            if (globalDataMediator == null)
            {
                return;
            }

            _globalData = globalDataMediator.GlobalData;

            if(_globalData == null)
            {
                return;
            }

            _existGlobalData = true;

            _globalData._Initialize();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetLocalizeTaget(string localizeTarget)
        {
            _localizeTarget = localizeTarget;

            if (!_existGlobalData)
            {
                return;
            }

            _globalData._UpdateFontTable();
        }

        //***********************************************************
        // Screen
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _InitializeScreenSize()
        {
            _prevScreenWidth = float.MinValue;
            _prevScreenHeight = float.MinValue;

            _targetScreenSizeParameter = null;

            if (_existGlobalData)
            {
                _targetScreenSizeParameter = _globalData._GetScreenSizeParameter(_deviceModel);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateScreenSize()
        {
            _screenSizeChangeFlag = false;

            _screenWidth = (float)Screen.width;
            _screenHeight = (float)Screen.height;

            if (_screenWidth == _prevScreenWidth && _screenHeight == _prevScreenHeight)
            {
                return;
            }

            _displayWidth = (float)Display.displays[0].systemWidth;
            _displayHeight = (float)Display.displays[0].systemHeight;

            _screenSafeArea = SafeAreaResolver.SafeArea;

            _currentScreenAspect = _screenWidth / _screenHeight;

            _prevScreenWidth = _screenWidth;
            _prevScreenHeight = _screenHeight;

            _screenTopMarginPercent = 0;
            _screenBottomMarginPercent = 0;
            _screenLeftMarginPercent = 0;
            _screenRightMarginPercent = 0;

            _screenMaxWideSize.x = 1000;
            _screenMaxWideSize.y = 1;

            _screenMaxNarrowSize.x = 1;
            _screenMaxNarrowSize.y = 1000;

            if (_screenSafeArea.x > 0 || _screenSafeArea.xMax < _displayWidth)
            {
                _screenLeftMarginPercent = _screenSafeArea.x / _displayWidth;
                _screenRightMarginPercent = (_displayWidth - _screenSafeArea.xMax) / _displayWidth;
            }

            if (_screenSafeArea.y > 0 || _screenSafeArea.yMax < _displayHeight)
            {
                _screenTopMarginPercent = (_displayHeight - _screenSafeArea.yMax) / _displayHeight;
                _screenBottomMarginPercent = _screenSafeArea.y / _displayHeight;
            }

            if (_targetScreenSizeParameter != null)
            {
                if (_targetScreenSizeParameter.ScreenSize.x > 0)
                {
                    _screenLeftMarginPercent = _targetScreenSizeParameter.LeftMargin / _targetScreenSizeParameter.ScreenSize.x;
                    _screenRightMarginPercent = _targetScreenSizeParameter.RightMargin / _targetScreenSizeParameter.ScreenSize.x;
                }

                if (_targetScreenSizeParameter.ScreenSize.y > 0)
                {
                    _screenTopMarginPercent = _targetScreenSizeParameter.TopMargin / _targetScreenSizeParameter.ScreenSize.y;
                    _screenBottomMarginPercent = _targetScreenSizeParameter.BottomMargin / _targetScreenSizeParameter.ScreenSize.y;
                }

                if(_targetScreenSizeParameter.MaxWideSize.x > 0 && _targetScreenSizeParameter.MaxWideSize.y > 0)
                {
                    _screenMaxWideSize.x = _targetScreenSizeParameter.MaxWideSize.x;
                    _screenMaxWideSize.y = _targetScreenSizeParameter.MaxWideSize.y;
                }

                if (_targetScreenSizeParameter.MaxNarrowSize.x > 0 && _targetScreenSizeParameter.MaxNarrowSize.y > 0)
                {
                    _screenMaxNarrowSize.x = _targetScreenSizeParameter.MaxNarrowSize.x;
                    _screenMaxNarrowSize.y = _targetScreenSizeParameter.MaxNarrowSize.y;
                }
            }

            _screenMaxWideAspect = _screenMaxWideSize.x / _screenMaxWideSize.y;
            _screenMaxNarrowAspect = _screenMaxNarrowSize.x / _screenMaxNarrowSize.y;

            _screenSizeChangeFlag = true;
        }

        //***********************************************************
        // GameObject
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public T _GetFlBaseFromGameObject<T>(GameObject targetObject) where T : AnBase
        {
            if (_rootTable == null)
            {
                return null;
            }

            foreach (AnRoot root in _rootTable.Values)
            {
                if (!root.DataTable.ContainsKey(targetObject))
                {
                    continue;
                }

                return root.DataTable[targetObject] as T;
            }

            return null;
        }

        //***********************************************************
        // Update Group 
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetUpdateGroup(int interval)
        {
            if (interval == 0)
            {
                return 0;
            }

            List<int> updateGroupNum = new List<int>();

            for (int i = 0; i < interval + 1; i++)
            {
                updateGroupNum.Add(0);
            }

            for (int i = 0; i < _rootList.Count; i++)
            {
                AnRoot root = _rootList[i];

                if (root == null)
                {
                    continue;
                }

                if (root.gameObject == null)
                {
                    continue;
                }

                if (root.UpdateInterval != interval)
                {
                    continue;
                }

                updateGroupNum[root.UpdateGroup % (interval + 1)] += 1;
            }

            int minGroupIndex = 0;
            int minGroupNum = int.MaxValue;
            for (int i = 0; i < updateGroupNum.Count; i++)
            {
                if (updateGroupNum[i] < minGroupNum)
                {
                    minGroupIndex = i;
                    minGroupNum = updateGroupNum[i];
                }
            }

            updateGroupNum = null;

            return minGroupIndex;
        }

        //***********************************************************
        // 
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetCustomTimeScale(float timeScale)
        {
            _customTimeScale = timeScale;
        }

        //***********************************************************
        // 
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetDefaultLongTouchTime()
        {
            if (_existGlobalData)
            {
                return _globalData.DefaultLongTouchTime;
            }

            return 1.0f;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetKeyInputChangeStartDelayTime()
        {
            if (_existGlobalData)
            {
                return _globalData.KeyInputChangeStartDelayTime;
            }

            return 0.3f;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetKeyInputChangeDelayTime()
        {
            if (_existGlobalData)
            {
                return _globalData.KeyInputChangeDelayTime;
            }

            return 0.1f;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetRayInputSubmitDelay()
        {
            if (_existGlobalData)
            {
                return _globalData.RayInputSubmitDelay;
            }

            return 3.0f;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public List<string> _GetHorizontalAxisNameList(int playerIndex)
        {
            if (_existGlobalData)
            {
                AnPlayerSetting setting = _globalData._GetPlayerSetting(playerIndex);

                if(setting != null)
                {
                    return setting.RuntimeKeyInputHorizontalNameList;
                }
            }

            return _horizontalAxisNameList;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public List<string> _GetVerticalAxisNameList(int playerIndex)
        {
            if (_existGlobalData)
            {
                AnPlayerSetting setting = _globalData._GetPlayerSetting(playerIndex);

                if (setting != null)
                {
                    return setting.RuntimeKeyInputVerticalNameList;
                }
            }

            return _verticalAxisNameList;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public List<string> _GetSubmitButtonNameList(int playerIndex)
        {
            if (_existGlobalData)
            {
                AnPlayerSetting setting = _globalData._GetPlayerSetting(playerIndex);

                if (setting != null)
                {
                    return setting.RuntimeKeyInputSubmitNameList;
                }
            }

            return _subumitButtonNameList;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public List<string> _GetCancelButtonNameList(int playerIndex)
        {
            if (_existGlobalData)
            {
                AnPlayerSetting setting = _globalData._GetPlayerSetting(playerIndex);

                if (setting != null)
                {
                    return setting.RuntimeKeyInputCancelNameList;
                }
            }

            return _cancelButtonNameList;
        }

        //***********************************************************
        // 
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetTextSortOderRoundValue()
        {
            if (!_existGlobalData)
            {
                return 200;
            }

            return _globalData.TextSortOderRoundValue;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public AnFontLocalizeParameter _GetFontLocalizeParam(string fontName, bool useCommon)
        {
            if (!_existGlobalData)
            {
                return null;
            }

            AnFontLocalizeParameter targetParam = null;

            if (useCommon)
            {
                targetParam = _globalData._GetFontLocalizeParamFromCommon(fontName);
            }
            else
            {
                targetParam = _globalData._GetFontLocalizeParam(fontName);
            }

            return targetParam;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetTextOutlineQualityForMinFontSize(AnFontLocalizeParameter _localizeParam)
        {
            if (!_existGlobalData)
            {
                return 20;
            }

            if (_localizeParam == null)
            {
                if (_globalData.TextOutlineQualityForMinFontSize <= 0)
                {
                    return 20;
                }

                return _globalData.TextOutlineQualityForMinFontSize;
            }

            if (_localizeParam.TextOutlineQualityForMinFontSize <= 0)
            {
                return _globalData.TextOutlineQualityForMinFontSize;
            }

            return _localizeParam.TextOutlineQualityForMinFontSize;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetTextOutlineQualityMinFontSize(AnFontLocalizeParameter _localizeParam)
        {
            if (!_existGlobalData)
            {
                return 50;
            }

            if(_localizeParam == null)
            {
                if(_globalData.TextOutlineQualityMinFontSize <= 0)
                {
                    return 50;
                }

                return _globalData.TextOutlineQualityMinFontSize;
            }

            if(_localizeParam.TextOutlineQualityMinFontSize <= 0)
            {
                return _globalData.TextOutlineQualityMinFontSize;
            }

            return _localizeParam.TextOutlineQualityMinFontSize;
        }        

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetTextOutlineQualityForMinOffset(AnFontLocalizeParameter _localizeParam)
        {
            if (!_existGlobalData)
            {
                return 16;
            }

            if (_localizeParam == null)
            {
                if (_globalData.TextOutlineQualityForMinOffset <= 0)
                {
                    return 16;
                }

                return _globalData.TextOutlineQualityForMinOffset;
            }

            if (_localizeParam.TextOutlineQualityForMinOffset <= 0)
            {
                return _globalData.TextOutlineQualityForMinOffset;
            }

            return _localizeParam.TextOutlineQualityForMinOffset;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetTextOutlineQualityMinOffset(AnFontLocalizeParameter _localizeParam)
        {
            if (!_existGlobalData)
            {
                return 5;
            }

            if (_localizeParam == null)
            {
                if (_globalData.TextOutlineQualityMinOffset <= 0)
                {
                    return 5;
                }

                return _globalData.TextOutlineQualityMinOffset;
            }

            if (_localizeParam.TextOutlineQualityMinOffset <= 0)
            {
                return _globalData.TextOutlineQualityMinOffset;
            }

            return _localizeParam.TextOutlineQualityMinOffset;
        }        

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetStencilMaskInterval()
		{
			if (!_existGlobalData)
			{
				return 3;
			}

			return _globalData.StencilMaskInterval;
		}

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetBaseScreenWidth()
        {
            if (!_existGlobalData)
            {
                return 1920;
            }

            return _globalData.BaseScreenWidth;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetScrollStartPixel()
        {
            if (!_existGlobalData)
            {
                return 5.0f;
            }

            return _globalData.ScrollStartPixel;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetScrollSpeedValue()
        {
            if (!_existGlobalData)
            {
                return 0.02f;
            }

            return _globalData.ScrollSpeedValue;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetScrollAccelValue()
        {
            if (!_existGlobalData)
            {
                return 0.05f;
            }

            return _globalData.ScrollAccelValue;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public float _GetScrollIncrementValue()
        {
            if (!_existGlobalData)
            {
                return 3f;
            }

            return _globalData.ScrollIncrementValue;
        }

        //***********************************************************
        // Layer
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateLayerTable()
        {
            if(_layerTableByBitFlagKey == null)
            {
                _layerTableByBitFlagKey = new Hashtable();
            }

            if (_layerTableByNameKey == null)
            {
                _layerTableByNameKey = new Hashtable();
            }

            if(_layerBitFlagList == null)
            {
                _layerBitFlagList = new List<int>();
            }

            if(_layerNameList == null)
            {
                _layerNameList = new List<string>();
            }            

            _layerTableByBitFlagKey.Clear();
            _layerTableByNameKey.Clear();

            _layerBitFlagList.Clear();
            _layerNameList.Clear();

            for (int i = 0; i < _maxLayerCount; i++)
            {
                string layerName = LayerMask.LayerToName(i);

                if (layerName == null)
                {
                    continue;
                }

                if (layerName == AnValue.TextEmpty)
                {
                    continue;
                }

                if(_layerTableByNameKey.Contains(layerName))
                {
                    continue;
                }

                int layerBitFlag = 1 << i;

                _layerTableByNameKey.Add(layerName, layerBitFlag);
                _layerTableByBitFlagKey.Add(layerBitFlag, layerName);

                _layerBitFlagList.Add(layerBitFlag);
                _layerNameList.Add(layerName);
            }
        }        

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetLayerBitFlag(string layerName)
        {
            if(!_layerTableByNameKey.Contains(layerName))
            {
                return -1;
            }

            return (int)_layerTableByNameKey[layerName];
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public string _GetLayerName(int layerBitFlag)
        {
            if (!_layerTableByBitFlagKey.Contains(layerBitFlag))
            {
                return null;
            }

            return (string)_layerTableByBitFlagKey[layerBitFlag];
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _AddActiveLayerTable(GameObject gameObject)
        {
            if (_activeLayerBitFlagList == null)
            {
                _activeLayerBitFlagList = new List<int>();
            }

            if (_activeLayerTableByBitFlagKey == null)
            {
                _activeLayerTableByBitFlagKey = new Hashtable();
            }

            int thisBitFlag = 1 << gameObject.layer;

            if (_activeLayerTableByBitFlagKey.ContainsKey(thisBitFlag))
            {
                return;
            }

            string thisLayerName = _GetLayerName(thisBitFlag);

            _activeLayerTableByBitFlagKey.Add(thisBitFlag, thisLayerName);
            _activeLayerBitFlagList.Add(thisBitFlag);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _OptimizeActiveLayerTable()
        {
            if (_activeLayerTableByBitFlagKey == null)
            {
                _activeLayerTableByBitFlagKey = new Hashtable();
            }

            if (_activeLayerBitFlagList == null)
            {
                _activeLayerBitFlagList = new List<int>();
            }

            _activeLayerTableByBitFlagKey.Clear();
            _activeLayerBitFlagList.Clear();

            if (_rootList != null)
            {
                for (int p = 0; p < _rootList.Count; p++)
                {
                    _AddActiveLayerTable(_rootList[p].gameObject);
                }
            }
        }

        //***********************************************************
        // Sorting Layer
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateSortingLayerTable()
        {
            if (_sortingLayerTableByIndexKey == null)
            {
                _sortingLayerTableByIndexKey = new Hashtable();
            }

            if (_sortingLayerTableByNameKey == null)
            {
                _sortingLayerTableByNameKey = new Hashtable();
            }

            if(_sortingLayerIndexList == null)
            {
                _sortingLayerIndexList = new List<int>();
            }

            if (_sortingLayerNameList == null)
            {
                _sortingLayerNameList = new List<string>();
            }

            _sortingLayerTableByIndexKey.Clear();
            _sortingLayerTableByNameKey.Clear();

            _sortingLayerIndexList.Clear();
            _sortingLayerNameList.Clear();

            for (int i = 0; i < SortingLayer.layers.Length; i++)
            {
                SortingLayer thisSortingLayer = SortingLayer.layers[i];
                
                if (_sortingLayerTableByNameKey.Contains(thisSortingLayer.name))
                {
                    continue;
                }

                _sortingLayerTableByIndexKey.Add(i, thisSortingLayer.name);
                _sortingLayerTableByNameKey.Add(thisSortingLayer.name, i);

                _sortingLayerIndexList.Add(i);
                _sortingLayerNameList.Add(thisSortingLayer.name);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public int _GetSortingLayerIndex(string sortingLayerName)
        {
            if (!_sortingLayerTableByNameKey.Contains(sortingLayerName))
            {
                return -1;
            }

            return (int)_sortingLayerTableByNameKey[sortingLayerName];
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public string _GetSortingLayerName(int sortingLayerIndex)
        {
            if (!_sortingLayerTableByIndexKey.Contains(sortingLayerIndex))
            {
                return null;
            }

            return (string)_sortingLayerTableByIndexKey[sortingLayerIndex];
        }

        //URP:置き換え対応
        /*
        public void AddSharedMaterial(string materialKey,Material material)
        {
            if(material == null)
            {
                return;
            }

            if(_sharedMaterialTable == null)
            {
                _sharedMaterialTable = new Hashtable();
            }

            if (_sharedMaterialTable.ContainsKey(materialKey))
            {
                var info = _sharedMaterialTable[materialKey] as SharedMaterialInfo;
                info._refCount++;
#if UNITY_EDITOR
                if(info._material != material)
                {
                    //登録されているものと違うので更新する
                    Debug.LogWarning("AnRootManager:登録マテリアルが入れ替わりました:" + info._material.name + "->" + material.name);
                    info._material = material;
                }
#endif
                return;
            }

            _sharedMaterialTable[materialKey] = new SharedMaterialInfo() { _refCount = 1, _material = material };
        }

        public void RemoveSharedMaterial(string materialKey)
        {
            if(_sharedMaterialTable == null)
            {
                return;
            }

            if(!_sharedMaterialTable.ContainsKey(materialKey))
            {
                return;
            }

            var info = _sharedMaterialTable[materialKey] as SharedMaterialInfo;
            info._refCount--;
            if (info._refCount <= 0)
            {
                Material.DestroyImmediate(info._material);
                _sharedMaterialTable.Remove(materialKey);
            }
        }
        */

        //***********************************************************
        // Gaussian Blur
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public List<float[]> _GetGaussianBlurValue(int quality, int precision)
        {
            if(_gaussianBlurValueTable == null)
            {
                _gaussianBlurValueTable = new Hashtable();
            }

            AnUtilityValue.LimitValue(ref quality, 1, 3);
            AnUtilityValue.LimitValue(ref precision, 1, 3);

            int thisKey = quality * 10 + precision;
            
            if (_gaussianBlurValueTable.ContainsKey(thisKey))
            {
                return _gaussianBlurValueTable[thisKey] as List<float[]>;
            }

            List<float[]> thisValueList = new List<float[]>();

            float[] thisOffsetXList = null;
            float[] thisOffsetYList = null;
            float[] thisWeightList = null;

            float amount = 0;

            if(quality == 1)
            {
                amount = 1;
            }
            else if (quality == 2)
            {
                amount = 1.2f;
            }
            else if (quality == 3)
            {
                amount = 1.4f;
            }
            else if (quality == 4)
            {
                amount = 1.6f;
            }
            else if (quality == 5)
            {
                amount = 1.8f;
            }
            else if (quality == 6)
            {
                amount = 2;
            }
            else if (quality == 7)
            {
                amount = 2;
            }

            AnUtilityMaterial.ComputeGaussianBlurList(quality, amount, precision, ref thisOffsetXList, ref thisOffsetYList, ref thisWeightList);

            thisValueList.Add(thisOffsetXList);
            thisValueList.Add(thisOffsetYList);
            thisValueList.Add(thisWeightList);

            _gaussianBlurValueTable.Add(thisKey, thisValueList);

            return thisValueList;
        }

        //***********************************************************
        // Setting
        //***********************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _LoadEditorSetting()
        {
#if UNITY_EDITOR

            _useDebugComponent = UnityEditor.EditorPrefs.GetBool(AnEditorKeyTypes.FlUseDebugComponent.ToString(), false);
                        
            _useDebugLog = UnityEditor.EditorPrefs.GetBool(AnEditorKeyTypes.FlUseDebugLog.ToString(), false);

            _UpdateDebugComponent();
#endif
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateDebugComponent()
        {
#if UNITY_EDITOR

            if (_rootList == null)
            {
                return;
            }           

            for (int i = 0; i < _rootList.Count; i++)
            {
                if (_useDebugComponent)
                {
                    _rootList[i]._ApplyDebugComponent();
                }
                else
                {
                    _rootList[i]._RemoveDebugComponent();
                }
            }
#endif
        }
    }
}
