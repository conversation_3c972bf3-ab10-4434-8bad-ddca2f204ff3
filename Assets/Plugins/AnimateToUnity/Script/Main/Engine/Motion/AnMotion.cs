using UnityEngine;
using System;
using System.Collections;
using System.Collections.Generic;

namespace AnimateToUnity
{
    public class AnMotion : AnBase
    {
        //=============================================================================================
        // Enum
        //=============================================================================================

        public enum ResetModeTypes
        {
            ResetAll,
            None,
            ResetLabel,
        }

        public enum StateTypes
        {
            Playing,
            Pause,
        }

        //=============================================================================================
        // Variables 
        //=============================================================================================

        private Hashtable _childGameObjectTable = null;

        private AnMotionParameter _parameter = null;
        private List<AnObjectBase> _objectList = null;
        private AnObject _parentObject = null;

        private bool _existParentObject = false;

        private ResetModeTypes _resetModeType = ResetModeTypes.ResetAll;

        private StateTypes _currentStateType = StateTypes.Pause;

        public string _currentLabelName = "";
        public int _currentLabelIndex = 0;
        public Vector2 _currentLabelTimeRange = Vector2.zero;
        public string _nextLabelName = "";
        public int _nextLabelIndex = 0;
        
        public List<List<AnObjectControlInfo>> _allObjectControlInfoList = null;
        public List<AnObjectControlInfo> _currentObjectControlInfoList = null;

        public Action _labelActionStart = null;
        public Action _labelActionLoop = null;
        public Action _labelActionEnd = null;

        public bool _existLabelActionStart = false;
        public bool _existLabelActionLoop = false;
        public bool _existLabelActionEnd = false;

        public AnAction _labelFlActionStart = null;
        public AnAction _labelFlActionLoop = null;
        public AnAction _labelFlActionEnd = null;

        public bool _existLabelFlActionStart = false;
        public bool _existLabelFlActionLoop = false;
        public bool _existLabelFlActionEnd = false;

        public float _currentTime = 0;
        public float _prevTime = -1;
        public float _objectTime = 0;
        public float _objectTimeWithoutLastFrame = 0;
        public float _fixObjectTime = 0;
        private float _restCurrentTime = 0;

        private bool _updateLowerFlag = false;

        private MeshRenderer[] _meshRenderList = null;
        private Collider[] _colliderList = null;
        private Collider2D[] _collider2DList = null;
        private Transform[] _tempTransformList = null;
        private List<AnBase> _tempChildBaseList = null;
        private AnBase[] _childBaseList = null;

        private bool _existStencilRefCountUp = false;

        //=============================================================================================
        // Property
        //=============================================================================================

        /// <summary>
        /// Read Only
        /// </summary>
        public AnMotionParameter Parameter
        {
            get { return _parameter; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public AnObject ParentObject
        {
            get { return _parentObject; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public bool ExistParentObject
        {
            get { return _existParentObject; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public string CurrentLabelName
        {
            get { return _currentLabelName; }
            set { _currentLabelName = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public int CurrentLabelIndex
        {
            get { return _currentLabelIndex; }
            set { _currentLabelIndex = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public Vector2 CurrentLabelTimeRange
        {
            get { return _currentLabelTimeRange; }
            set { _currentLabelTimeRange = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float CurrentLabelTimeLength
        {
            get { return _currentLabelTimeRange.y - _currentLabelTimeRange.x; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float CurrentLabelTime
        {
            get { return AnUtilityValue.GetLimitValue(_currentTime - _currentLabelTimeRange.x, 0, _currentLabelTimeRange.y - _currentLabelTimeRange.x); }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public int CurrentLabelFrame
        {
            get { return Mathf.FloorToInt(CurrentLabelTime * _root.Parameter.BaseFrameRate); }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float CurrentLabelNormalizeTime
        {
            get { return (_currentTime - _currentLabelTimeRange.x) / (_currentLabelTimeRange.y - _currentLabelTimeRange.x); }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public string NextLabelName
        {
            get { return _nextLabelName; }
            set { _nextLabelName = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public int NextLabelIndex
        {
            get { return _nextLabelIndex; }
            set { _nextLabelIndex = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public Action LabelActionStart
        {
            get { return _labelActionStart; }
            set { _labelActionStart = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public Action LabelActionLoop
        {
            get { return _labelActionLoop; }
            set { _labelActionLoop = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public Action LabelActionEnd
        {
            get { return _labelActionEnd; }
            set { _labelActionEnd = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public bool ExistLabelActionStart
        {
            get { return _existLabelActionStart; }
            set { _existLabelActionStart = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public bool ExistLabelActionLoop
        {
            get { return _existLabelActionLoop; }
            set { _existLabelActionLoop = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public bool ExistLabelActionEnd
        {
            get { return _existLabelActionEnd; }
            set { _existLabelActionEnd = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public ResetModeTypes ResetModeType
        {
            get { return _resetModeType; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public StateTypes CurrentState
        {
            get { return _currentStateType; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float CurrentTime
        {
            get { return _currentTime; }
            set { _currentTime = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float PrevTime
        {
            get { return _prevTime; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float ObjectTime
        {
            get { return _objectTime; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float ObjectTimeWithoutLastFrame
        {
            get { return _objectTimeWithoutLastFrame; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float FixObjectTime
        {
            get { return _fixObjectTime; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public float MotionSpeed
        {
            get { return _motionSpeed; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public bool ExistStencilRefCountUp
        {
            get { return _existStencilRefCountUp; }
            set { _existStencilRefCountUp = value; }
        }

        /// <summary>
        /// Read Only
        /// </summary>
        public List<AnObjectBase> ObjectList
        {
            get { return _objectList; }
        }

        //=============================================================================================
        // Constructor
        //=============================================================================================

        public AnMotion(GameObject gameObject)
        {
            _gameObject = gameObject;
            _transform = gameObject.transform;
            _id = _gameObject.GetInstanceID().ToString();
        }

        //=============================================================================================
        // Method
        //=============================================================================================

        //***************************************************************
        //
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _CreateEditorData(AnMotionParameter parameter, AnObject parentObject, AnRoot root)
        {
            _root = root;
            _parameter = parameter;

            if (_parentObject != null)
            {
                _parentObject = parentObject;
                _parentObject.ChildMotion = this;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void _ApplyData(AnMotionParameter parameter, AnObject parentObject, AnRoot root)
        {
            _root = root;
            _parameter = parameter;
            _objectList = new List<AnObjectBase>();

            _root.MotionList.Add(this);

            _existParentObject = false;
            if (parentObject != null)
            {
                _parentObject = parentObject;
                _parentObject.ChildMotion = this;
                _existParentObject = true;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _CreateData()
        {
            base._CreateData();

            _visible = true;
            _updateLowerFlag = true;
            _motionSpeed = 1f;
            _currentTime = 0f;
            _prevTime = float.MaxValue;
            _currentStateType = StateTypes.Playing;
            _resetModeType = ResetModeTypes.ResetAll;

            if (_existParentObject)
            {
                if (_parentObject.ObjectParameter.MotionResetModeType != ResetModeTypes.ResetAll)
                {
                    _resetModeType = _parentObject.ObjectParameter.MotionResetModeType;
                }
            }

            //lable
            _currentLabelName = _parameter.LabelParamList[0].Name;
            _currentLabelTimeRange = _parameter.LabelParamList[0].TimeRange;
            _nextLabelName = _parameter.LabelParamList[0].NextLabel;

            //layer
            _layerName = _root.Parameter.LayerName;
            _layerIndex = _root.Parameter.LayerIndex;

            if (_existParentObject)
            {
                if (_parentObject.LayerName != "")
                {
                    _layerName = _parentObject.LayerName;
                    _layerIndex = _parentObject.LayerIndex;
                }
            }

            _gameObject.layer = _layerIndex;

            //sort
            _sortOrderIndex = _root.SortOrderCount;
            _sortOrderIndexForDrawTextLater = _root.SortOrderCountForDrawTextLater;
            _sortOffset = 0;
            _localSortOffset = 0;
            _sortLayerName = _root._parameter._sortLayerName;
            if (_existParentObject)
            {
                _localSortOffset = _parentObject.LocalSortOffset;                
                _sortLayerName = _parentObject._sortLayerName;
            }

            //grayscale
            _isGrayscale = false;
            if (_existParentObject)
            {
                _isGrayscale = _parentObject.IsGrayscale;
            }

            //Stencil
            _existStencilRefCountUp = false;
            if (_existParentObject)
            {
                _localStencilRefOffset = _parentObject.LocalStencilRefOffset;
            }

            //Sync time
            _timeModeType = AnTimeModeTypes.Normal;
            if (_existParentObject)
            {
                _timeModeType = _parentObject.TimeModeType;
            }

            //Blur
            _currentBlurValue = Vector2.zero;
            if(_existParentObject)
            {
                _currentBlurValue = _parentObject.CurrentBlurValue;
            }

            _currentBlurQuality = 0;
            if (_existParentObject)
            {
                _currentBlurQuality = _parentObject.CurrentBlurPrecision;
            }

            //Color
            _multiplyColor = new Color(1, 1, 1, 1);
            _colorOffset = new Color(0, 0, 0, 0);
        }

        //***************************************************************
        // Fix data
        //***************************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _FixData()
        {
            base._FixData();

            _CheckLowerObjects();

            _UpdateSortOrder();

            _UpdateSortLayer();

            _UpdateStencilRef(false);

            _parameter._CreateAllObjectControlInfoList(this);
            
            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase uiObj = _objectList[i];
                uiObj._FixData();
            }

            _updateLowerFlag = true;
        }

        //************************************************
        // Child game object table
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public GameObject _GetChildGameObject(string gameObjectName)
        {
            if (_childGameObjectTable == null)
            {
                _CreateChildGameObjectTable();
            }

            if (!_childGameObjectTable.ContainsKey(gameObjectName))
            {
                return null;
            }

            return _childGameObjectTable[gameObjectName] as GameObject;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateChildGameObjectTable()
        {
            _childGameObjectTable = new Hashtable();

            foreach (Transform trans in _transform)
            {
                if (trans == _transform)
                {
                    continue;
                }

                _childGameObjectTable.Add(trans.name, trans.gameObject);
            }
        }

        //************************************************
        // Update
        //************************************************

        // -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateFirst()
        {
            base._UpdateFirst();

            _UpdateVisible();

            if (_root._initializeFlag)
            {
                _visibleInHierarchy = true;
            }

            if (_visibleInHierarchy)
            {
                _UpdateColor();
            }

            if (_root._initializeFlag)
            {
                _visibleByAlpha = true;
            }

            if (!_visibleInHierarchy)
            {
                _UpdateLowerObjects(false);
            }

            if (_updateFlag)
            {
                _UpdateState();
            }

            _UpdateTime();

            _UpdateBlurValue();
            
            _UpdateChildren();

            _UpdateObjectControlInfoList();
        }

        // -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateSecond()
        {
            base._UpdateSecond();

            _prevTime = _currentTime;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateVisible()
        {
            if (_existParentObject)
            {
                if (!_parentObject._visibleInHierarchy)
                {
                    return;
                }
            }

            if (!_root._visibleInHierarchy)
            {
                return;
            }

            if (!_visible)
            {
                return;
            }

            if (!_gameObject.activeInHierarchy)
            {
                return;
            }

            _visibleInHierarchy = true;
            _updateLowerFlag = true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateColor()
        {
            if (_existParentObject)
            {
                _currentColor = _multiplyColor * _parentObject._currentColor;
                _currentColorOffset = _colorOffset + ParentObject._currentColorOffset;
            }
            else
            {
                _currentColor = _multiplyColor;
                _currentColorOffset = _colorOffset;
            }

            if (_currentColor.a + _currentColorOffset.a <= AnValue.MinAlphaValue)
            {
                return;
            }

            _visibleByAlpha = true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateState()
        {
            switch (_currentStateType)
            {
                case StateTypes.Playing:

                    _parameter._UpdateMotionTime(this);

                    if (_timeModeType != AnTimeModeTypes.Sync || _currentLabelIndex != _nextLabelIndex)
                    {
                        _currentTime += _root._deltaTime * _motionSpeed;
                    }

                    break;

                case StateTypes.Pause:

                    break;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateTime()
        {
            if (_currentTime == _prevTime)
            {
                return;
            }

            _restCurrentTime = _currentTime % AnRootManager.Instance._currentOneFrameTime;

            if (_restCurrentTime < 0.001f && _restCurrentTime > 0)
            {
                _currentTime -= _restCurrentTime;
            }
            else if (_restCurrentTime > AnRootManager.Instance._currentOneFrameTime - 0.001f && _restCurrentTime < AnRootManager.Instance._currentOneFrameTime)
            {
                _currentTime += AnRootManager.Instance._currentOneFrameTime - _restCurrentTime;
            }

            AnUtilityValue.LimitValue(ref _currentTime, _currentLabelTimeRange.x, _currentLabelTimeRange.y);

            _objectTime = _currentTime;
            AnUtilityValue.LimitValue(ref _objectTime, _currentLabelTimeRange.x + AnValue.ObjectTimeAddValue, _currentLabelTimeRange.y - AnValue.ObjectTimeAddValue);

            _objectTimeWithoutLastFrame = _currentTime;
            if (_root != null && _root._parameter != null)
            {
                AnUtilityValue.LimitValue(ref _objectTimeWithoutLastFrame, _currentLabelTimeRange.x + AnValue.ObjectTimeAddValue, _currentLabelTimeRange.y - _root._parameter._oneFrameTime);
            }

            _fixObjectTime = _objectTimeWithoutLastFrame;
            if (_nextLabelIndex != -1)
            {
                if (_root != null && _root._parameter != null)
                {
                    if (_nextLabelIndex > _currentLabelIndex && _currentLabelTimeRange.y - _currentLabelTimeRange.x >= _root._parameter._oneFrameTime + AnValue.ObjectTimeAddValue)
                    {
                        _fixObjectTime = _objectTime;
                    }
                }
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateObjectControlInfoList()
        {
            for (int p = 0; p < _currentObjectControlInfoList.Count; p++)
            {
                AnObjectControlInfo thisObjectControlInfo = _currentObjectControlInfoList[p];

                if (_objectTime < thisObjectControlInfo._startTime)
                {
                    continue;
                }

                if (thisObjectControlInfo._isActive)
                {
                    continue;
                }

                thisObjectControlInfo._targetObject.ChildMotion._SetMotionPlayBase(thisObjectControlInfo._fixTargetTime, thisObjectControlInfo._targetIsStop, false);

                thisObjectControlInfo._isActive = true;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateBlurValue()
        {
            if (_existParentObject)
            {
                _currentBlurQuality = _parentObject.CurrentBlurQuality;
                _currentBlurPrecision = _parentObject.CurrentBlurPrecision;
                _currentBlurValue = _parentObject.CurrentBlurValue;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateChildren()
        {
            if (_updateFlag)
            {
                for (int i = 0; i < _objectList.Count; i++)
                {
                    _objectList[i]._Update();
                }

                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i]._UpdateForce();
            }
        }


        //************************************************
        // Reset
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _ResetTime()
        {
            base._ResetTime();

            if (_resetModeType == ResetModeTypes.None)
            {
                return;
            }            

            if (!_isResetTime)
            {
                if (_existParentObject)
                {
                    if (_parentObject._isResetTime)
                    {
                        _isResetTime = true;
                    }
                    else if (_parentObject._parentMotion._currentTime <= _parentObject._parameter._timeRange.x + AnValue.ObjectTimeAddValue)
                    {
                        _isResetTime = true;
                    }
                    else if (_parentObject._parentMotion._currentTime >= _parentObject._parameter._timeRange.y - AnValue.ObjectTimeAddValue)
                    {
                        _isResetTime = true;
                    }
                }
            }

            if (_isResetTime)
            {
                float time = 0;

                if (_resetModeType == ResetModeTypes.ResetLabel)
                {
                    time = _currentLabelTimeRange.x;
                }
                else
                {
                    time = -0.00001f;
                }

                _parameter._SetCurrentLabel(this, time);

                _prevTime = float.MaxValue;

                _UpdateTime();
            }

            _currentStateType = StateTypes.Playing;

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i]._isResetTime = _isResetTime;
                _objectList[i]._ResetTime();
                _objectList[i]._isResetTime = false;
            }

            _isResetTime = false;
        }

        //************************************************
        // Motion
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetResetModeType(ResetModeTypes resetModeType)
        {
            _resetModeType = resetModeType;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPlay()
        {
            _currentStateType = StateTypes.Playing;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPlay(string labelName)
        {
            _parameter._SetCurrentLabel(this, labelName);

            _SetMotionPlayBase(_currentLabelTimeRange.x, false, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPlay(string labelName, float timeOffset)
        {
            _parameter._SetCurrentLabel(this, labelName);

            float time = _currentLabelTimeRange.x + timeOffset;

            _SetMotionPlayBase(time, false, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPlay(string labelName, int frameOffset)
        {
            _parameter._SetCurrentLabel(this, labelName);

            float time = _currentLabelTimeRange.x + ((float)frameOffset + 0.001f) / _root.Parameter.BaseFrameRate;

            _SetMotionPlayBase(time, false, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPlay(float time)
        {
            _SetMotionPlayBase(time, false, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPlay(int frame)
        {
            float time = ((float)frame + 0.001f) / _root.Parameter.BaseFrameRate;

            _SetMotionPlayBase(time, false, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPause()
        {
            _currentStateType = StateTypes.Pause;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPause(string labelName)
        {
            _parameter._SetCurrentLabel(this, labelName);

            _SetMotionPlayBase(_currentLabelTimeRange.x, true, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPause(string labelName, float timeOffset)
        {
            _parameter._SetCurrentLabel(this, labelName);

            float time = _currentLabelTimeRange.x + timeOffset;

            _SetMotionPlayBase(time, true, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPause(string labelName, int frameOffset)
        {
            _parameter._SetCurrentLabel(this, labelName);

            float time = _currentLabelTimeRange.x + ((float)frameOffset + 0.001f) / _root.Parameter.BaseFrameRate;

            _SetMotionPlayBase(time, true, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPause(float time)
        {
            _SetMotionPlayBase(time, true, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionPause(int frame)
        {
            float time = ((float)frame + 0.002f) / _root.Parameter.BaseFrameRate;

            _SetMotionPlayBase(time, true, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionReset()
        {
            float time = _currentTime;

            if (_resetModeType == ResetModeTypes.None)
            {
                return;
            }
            else if (_resetModeType == ResetModeTypes.ResetLabel)
            {
                time = _currentLabelTimeRange.x;
            }
            else if (_resetModeType == ResetModeTypes.ResetAll)
            {
                time = -0.00001f;
            }

            _SetMotionPlayBase(time, false, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void SetMotionStop()
        {
            _SetMotionPlayBase(0.0f, true, true);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _SetMotionPlayBase(float time, bool pause, bool resetByStartLabel)
        {
            _currentStateType = StateTypes.Playing;

            if (pause)
            {
                _currentStateType = StateTypes.Pause;
            }

            _parameter._SetCurrentLabel(this, time);

            _prevTime = float.MaxValue;

            _UpdateTime();

            _isResetTime = false;

            if (resetByStartLabel)
            {
                if (time <= _parameter._labelParamList[0]._timeRange.x)
                {
                    _isResetTime = true;
                }
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i]._isResetTime = _isResetTime;
                _objectList[i]._ResetTime();
                _objectList[i]._isResetTime = false;
            }

            _isResetTime = false;

            if (AnUtilityObject.CheckParentVisibleInHierarchy(this))
            {
                _UpdateForce();
            }
        }

        //************************************************
        // Lower objects
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CheckLowerObjects()
        {
            _meshRenderList = _gameObject.GetComponentsInChildren<MeshRenderer>(true);
            _colliderList = _gameObject.GetComponentsInChildren<Collider>(true);
            _collider2DList = _gameObject.GetComponentsInChildren<Collider2D>(true);

            _tempTransformList = _gameObject.GetComponentsInChildren<Transform>(true);

            if (_tempChildBaseList == null)
            {
                _tempChildBaseList = new List<AnBase>();
            }

            _tempChildBaseList.Clear();

            for (int i = 0; i < _tempTransformList.Length; i++)
            {
                Transform childTrans = _tempTransformList[i];

                if (childTrans == _transform)
                {
                    continue;
                }

                AnBase childBase = _root.DataTable[childTrans.gameObject] as AnBase;

                if (childBase == null)
                {
                    continue;
                }

                _tempChildBaseList.Add(childBase);
            }

            _childBaseList = _tempChildBaseList.ToArray();

            _tempChildBaseList = null;
            _tempTransformList = null;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateLowerObjects(bool visible)
        {
            if (!_updateLowerFlag)
            {
                return;
            }

            _UpdateChildVisible(visible);
            _UpdateEnableRenderer(visible);
            _UpdateEnableCollider(visible);

            _updateLowerFlag = false;
        }

        /// -----------------------------------------
		/// <summary>
        /// 
		/// </summary>
		/// -----------------------------------------
		private void _UpdateChildVisible(bool visible)
        {
            for (int i = 0; i < _childBaseList.Length; i++)
            {
                _childBaseList[i]._visibleInHierarchy = visible;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateEnableRenderer(bool enable)
        {
            for (int i = 0; i < _meshRenderList.Length; i++)
            {
                _meshRenderList[i].enabled = enable;
            }
        }


        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateEnableCollider(bool enable)
        {
            for (int i = 0; i < _colliderList.Length; i++)
            {
                _colliderList[i].enabled = enable;

                if (!_visible)
                {
                    _colliderList[i].enabled = false;
                }
            }

            for (int i = 0; i < _collider2DList.Length; i++)
            {
                _collider2DList[i].enabled = enable;

                if (!_visible)
                {
                    _collider2DList[i].enabled = false;
                }
            }
        }

        //************************************************
        // Other
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateSortOrder()
        {
            base._UpdateSortOrder();
            
            if (!_root.DrawTextLater)
            {
                _sortOrder = _root.SortOrderCount - _sortOrderIndex + _sortOffset + _root.DefaultSortOffset + _localSortOffset;
            }
            else
            {
                _sortOrder = _root.SortOrderCountForDrawTextLater - _sortOrderIndexForDrawTextLater + _sortOffset + _root.DefaultSortOffset + _localSortOffset;
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _UpdateSortLayer()
        {
            base._UpdateSortLayer();

            if (_sortLayerName != "")
            {
                return;
            }

            _sortLayerName = _root.Parameter.SortLayerName;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateStencilRef(bool affectChildren)
        {
            base._UpdateStencilRef(affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase objBase = _objectList[i];

                objBase._UpdateStencilRef(affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateStencilCompareFunc(bool affectChildren)
        {
            base._UpdateStencilCompareFunc(affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase objBase = _objectList[i];

                objBase._UpdateStencilCompareFunc(affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _SetGrayscaleBase(bool enable)
        {
            base._SetGrayscaleBase(enable);

            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase objBase = _objectList[i];

                objBase.SetGrayscale(enable);
            }
        }

        //************************************************
        // Action
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// コールバック設定（上書きされる）
        /// </summary>
        /// -----------------------------------------
        public void SetAction(string labelName, Action action, AnMotionActionTypes actionType)
        {
            // ActionTableに追加される
            AnLabelParameter labelParam = _parameter._GetLabel(labelName);

            if (labelName != labelParam.Name)
            {
                return;
            }

            if (actionType == AnMotionActionTypes.Start)
            {
                if (!labelParam.ActionStartTable.ContainsKey(this))
                {
                    labelParam.ActionStartTable.Add(this, action);
                }
                else
                {
                    labelParam.ActionStartTable[this] = action;
                }
            }
            else if (actionType == AnMotionActionTypes.Loop)
            {
                if (!labelParam.ActionLoopTable.ContainsKey(this))
                {
                    labelParam.ActionLoopTable.Add(this, action);
                }
                else
                {
                    labelParam.ActionLoopTable[this] = action;
                }
            }
            else if (actionType == AnMotionActionTypes.End)
            {
                if (!labelParam.ActionEndTable.ContainsKey(this))
                {
                    labelParam.ActionEndTable.Add(this, action);
                }
                else
                {
                    labelParam.ActionEndTable[this] = action;
                }
            }

            _parameter._SetMotionAction(this, labelName);
        }

        /// -----------------------------------------
        /// <summary>
        /// コールバック追加（上書きされない）
        /// </summary>
        /// -----------------------------------------
        public void AddAction(string labelName, AnMotionActionTypes actionType, Action<object> action, object value, int id = -1)
        {
            // FlActionTableに追加される
            AnLabelParameter labelParam = _parameter._GetLabel(labelName);

            if (labelName != labelParam.Name)
            {
                return;
            }

            if (actionType == AnMotionActionTypes.Start)
            {
                _AddActionBase(labelParam.FlActionStartTable, action, value, id);
            }
            else if (actionType == AnMotionActionTypes.Loop)
            {
                _AddActionBase(labelParam.FlActionLoopTable, action, value, id);
            }
            else if (actionType == AnMotionActionTypes.End)
            {
                _AddActionBase(labelParam.FlActionEndTable, action, value, id);
            }

            _parameter._SetMotionAction(this, labelName);
        }

        /// <summary>
        /// コールバックを全削除
        /// </summary>
        /// <remarks> SetActionとAddActionどちらも削除したい場合はこちら </remarks>
        /// <param name="labelName"></param>
        /// <param name="actionType"></param>
        public void RemoveAction(string labelName, AnMotionActionTypes actionType)
        {
            AnLabelParameter labelParam = _parameter._GetLabel(labelName);

            if (labelName != labelParam.Name)
            {
                return;
            }

            if (actionType == AnMotionActionTypes.Start)
            {
                if (labelParam.ActionStartTable.ContainsKey(this))
                {
                    labelParam.ActionStartTable.Remove(this);
                }
                
                _RemoveActionBase(labelParam.FlActionStartTable);
            }
            else if (actionType == AnMotionActionTypes.Loop)
            {
                if (labelParam.ActionLoopTable.ContainsKey(this))
                {
                    labelParam.ActionLoopTable.Remove(this);
                }
                
                _RemoveActionBase(labelParam.FlActionLoopTable);
            }
            else if (actionType == AnMotionActionTypes.End)
            {
                if (labelParam.ActionEndTable.ContainsKey(this))
                {
                    labelParam.ActionEndTable.Remove(this);
                }
                
                _RemoveActionBase(labelParam.FlActionEndTable);
            }

            _parameter._SetMotionAction(this, labelName);
        }
        
        /// <summary>
        /// コールバックを全削除
        /// </summary>
        /// <remarks> SetActionとAddActionどちらも削除したい場合はこちら </remarks>
        public void RemoveAllAction()
        {
            for (int i = 0; i < _parameter.LabelParamList.Length; i++)
            {
                AnLabelParameter labelParam = _parameter.LabelParamList[i];
                RemoveAction(labelParam.Name, AnMotionActionTypes.Start);
                RemoveAction(labelParam.Name, AnMotionActionTypes.Loop);
                RemoveAction(labelParam.Name, AnMotionActionTypes.End);
            }
        }

        /// <summary>
        /// コールバック削除
        /// </summary>
        /// <remarks> AddActionしたもののみ削除</remarks>
        /// <param name="labelName"></param>
        /// <param name="actionType"></param>
        public void RemoveOnlyAdditionalAction(string labelName, AnMotionActionTypes actionType)
        {
            AnLabelParameter labelParam = _parameter._GetLabel(labelName);

            if (labelName != labelParam.Name)
            {
                return;
            }

            if (actionType == AnMotionActionTypes.Start)
            {
                _RemoveActionBase(labelParam.FlActionStartTable);
            }
            else if (actionType == AnMotionActionTypes.Loop)
            {
                _RemoveActionBase(labelParam.FlActionLoopTable);
            }
            else if (actionType == AnMotionActionTypes.End)
            {
                _RemoveActionBase(labelParam.FlActionEndTable);
            }

            _parameter._SetMotionAction(this, labelName);
        }

        /// <summary>
        /// コールバック削除
        /// </summary>
        /// <remarks> AddActionしたものを削除 </remarks>
        public void RemoveOnlyAdditionalAction()
        {
            for (int i = 0; i < _parameter.LabelParamList.Length; i++)
            {
                AnLabelParameter labelParam = _parameter.LabelParamList[i];

                RemoveOnlyAdditionalAction(labelParam.Name, AnMotionActionTypes.Start);
                RemoveOnlyAdditionalAction(labelParam.Name, AnMotionActionTypes.Loop);
                RemoveOnlyAdditionalAction(labelParam.Name, AnMotionActionTypes.End);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void RemoveActionFromID(string labelName, AnMotionActionTypes actionType, int id)
        {
            AnLabelParameter labelParam = _parameter._GetLabel(labelName);

            if (labelName != labelParam.Name)
            {
                return;
            }

            if (actionType == AnMotionActionTypes.Start)
            {
                _RemoveActionBase(labelParam.FlActionStartTable, id);
            }
            else if (actionType == AnMotionActionTypes.Loop)
            {
                _RemoveActionBase(labelParam.FlActionLoopTable, id);
            }
            else if (actionType == AnMotionActionTypes.End)
            {
                _RemoveActionBase(labelParam.FlActionEndTable, id);
            }

            _parameter._SetMotionAction(this, labelName);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public void RemoveActionFromIndex(string labelName, AnMotionActionTypes actionType, int index)
        {
            AnLabelParameter labelParam = _parameter._GetLabel(labelName);

            if (labelName != labelParam.Name)
            {
                return;
            }

            if (actionType == AnMotionActionTypes.Start)
            {
                _RemoveActionBase(labelParam.FlActionStartTable, -1, index);
            }
            else if (actionType == AnMotionActionTypes.Loop)
            {
                _RemoveActionBase(labelParam.FlActionLoopTable, -1, index);
            }
            else if (actionType == AnMotionActionTypes.End)
            {
                _RemoveActionBase(labelParam.FlActionEndTable, -1, index);
            }

            _parameter._SetMotionAction(this, labelName);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _AddActionBase(Hashtable targetTable, Action<object> action, object value, int id = -1)
        {
            if (targetTable == null)
            {
                return;
            }

            if (!targetTable.ContainsKey(this))
            {
                AnAction newFlAction = new AnAction();
                targetTable.Add(this, newFlAction);
            }

            AnAction flAction = targetTable[this] as AnAction;

            if (flAction == null)
            {
                return;
            }

            flAction.AddAction(action, value, id);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _RemoveActionBase(Hashtable targetTable, int id = -1, int index = -1)
        {
            if (targetTable == null)
            {
                return;
            }

            if (!targetTable.ContainsKey(this))
            {
                return;
            }

            AnAction flAction = targetTable[this] as AnAction;

            if (flAction == null)
            {
                return;
            }

            if (id >= 0 && index < 0)
            {
                flAction.RemoveActionFromID(id);
            }
            else if (id < 0 && index >= 0)
            {
                flAction.RemoveActionFromIndex(index);
            }
            else
            {
                flAction.RemoveAllAction();
            }

            if (flAction.ActionList.Count <= 0)
            {
                targetTable[this] = null;
                targetTable.Remove(this);
            }
        }

        //************************************************
        // 
        //************************************************

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetSortOffset(int sortOffset)
        {
            base.SetSortOffset(sortOffset);

            _UpdateSortOrder();

            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase objBase = _objectList[i];

                objBase.SetSortOffset(sortOffset);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetSortLayer(string sortLayerName)
        {
            base.SetSortLayer(sortLayerName);

            _UpdateSortLayer();

            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase objBase = _objectList[i];

                objBase.SetSortLayer(sortLayerName);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetTimeModeType(AnTimeModeTypes timeModeType, bool children)
        {
            base.SetTimeModeType(timeModeType, children);

            if (!children)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                AnObjectBase objBase = _objectList[i];

                objBase.SetTimeModeType(timeModeType, children);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetMotionSpeed(float speed, bool children)
        {
            base.SetMotionSpeed(speed, children);

            if (!children)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetMotionSpeed(speed, children);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetColliderThrough(bool through, bool affectChildren)
        {
            base.SetColliderThrough(through, affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetColliderThrough(through, affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetColliderThicknessOffset(float thicknessOffset, bool affectChildren)
        {
            base.SetColliderThicknessOffset(thicknessOffset, affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetColliderThicknessOffset(thicknessOffset, affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateColliderThickness(bool affectChildren)
        {
            base._UpdateColliderThickness(affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i]._UpdateColliderThickness(affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetEnableCollider(bool enable, bool affectChildren)
        {
            base.SetEnableCollider(enable, affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetEnableCollider(enable, affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetSubCollider(Collider subCollider, bool affectChildren)
        {
            base.SetSubCollider(subCollider, affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetSubCollider(subCollider, affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetBlurQuality(int blurRadius, int blurQuality, bool affectChildren)
        {
            base.SetBlurQuality(blurRadius, blurQuality, affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetBlurQuality(blurRadius, blurQuality, affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void SetBlurValue(Vector2 blurValue, bool affectChildren)
        {
            base.SetBlurValue(blurValue, affectChildren);

            if (!affectChildren)
            {
                return;
            }

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i].SetBlurValue(blurValue, affectChildren);
            }
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        public override void _UpdateScreenSize()
        {
            base._UpdateScreenSize();

            for (int i = 0; i < _objectList.Count; i++)
            {
                _objectList[i]._UpdateScreenSize();
            }
        }
    }
}
