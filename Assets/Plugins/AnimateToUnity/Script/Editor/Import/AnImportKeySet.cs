using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using AnimateToUnity;
using System.Xml;

namespace AnimateToUnity.Editor.Importer
{
	public class AnImportKeySet
	{
		//=============================================================================================
		// Property
		//=============================================================================================

		public List<AnImportKey> KeyList { get; set; }

		//=============================================================================================
		// Constructor
		//=============================================================================================

		public AnImportKeySet()
		{
			KeyList = new List<AnImportKey>();
		}

		//=============================================================================================
		// Method
		//=============================================================================================

		/// -----------------------------------------
		/// <summary>
		/// ReadData
		/// </summary>
		/// -----------------------------------------
		public void ReadData(XmlNode rootNode)
		{
			if (rootNode == null)
			{
				return;
			}

			if (rootNode.ChildNodes.Count == 0)
			{
				return;
			}

			float prevKey = 0;
			float prevValue = 0;

			prevKey *= 1;
			prevValue *= 1;

			foreach (XmlNode node in rootNode.ChildNodes)
			{
				float keyValue = AnEditorUtilityString.GetFloatValue(AnEditorUtilityXml.GetXmlNodeValue(node, AnEditorValue.AnXmlNameTypes.Time.ToString()), 0, AnEditorValue.FloatAccuracy);
				float value = AnEditorUtilityString.GetFloatValue(AnEditorUtilityXml.GetXmlNodeValue(node, AnEditorValue.AnXmlNameTypes.Value.ToString()), 0);

				AnImportKey key = new AnImportKey(this);
				key.Time = keyValue;
				key.Value = value;

				KeyList.Add(key);

				prevKey = key.Time;
				prevValue = key.Value;
			}
		}

		/// -----------------------------------------
		/// <summary>
		/// CreateData
		/// </summary>
		/// -----------------------------------------
		public AnKeyParameter CreateData()
		{
			if (KeyList.Count <= 1)
			{
				return null;
			}

			bool sameValue = true;
			float initialValue = KeyList[0].Value;
			foreach (AnImportKey key in KeyList)
			{
				if (key.Value != initialValue)
				{
					sameValue = false;
					break;
				}
			}

			if (sameValue)
			{
				return null;
			}

			List<AnImportKey> newKeyList = OptimizeKey(KeyList);

			AnKeyParameter keyData = new AnKeyParameter();
			keyData.KeyList = new List<Vector2>();

			foreach (AnImportKey key in newKeyList)
			{
				Vector2 newKey = new Vector2(key.Time, key.Value);

				keyData.KeyList.Add(newKey);
			}

			return keyData;
		}

		/// -----------------------------------------
		/// <summary>
		/// ExistData
		/// </summary>
		/// -----------------------------------------
		public bool ExistData()
		{
			if (KeyList.Count <= 1)
			{
				return false;
			}

			bool sameValue = true;
			float initialValue = KeyList[0].Value;
			foreach (AnImportKey key in KeyList)
			{
				if (key.Value != initialValue)
				{
					sameValue = false;
					break;
				}
			}

			if (sameValue)
			{
				return false;
			}

			return true;
		}

		/// -----------------------------------------
		/// <summary>
		/// OptimizeKey
		/// </summary>
		/// -----------------------------------------
		public List<AnImportKey> OptimizeKey(List<AnImportKey> keyList)
		{
			if (keyList.Count <= 1)
			{
				return keyList;
			}

			var newKeyList = new List<AnImportKey>();
			var skipIndexList = new List<int>();

			float prevSlopeValue = -100000000000;
			var prevIndex = 0;
			for (var id = 1; id < keyList.Count; id++)
			{
				AnImportKey currentKey = keyList[id];
				AnImportKey prevKey = keyList[prevIndex];

				float currentTime = currentKey.Time;
				float prevTime = prevKey.Time;

				if (currentTime <= prevTime)
				{
					skipIndexList.Add(id);
					continue;
				}

				float currentSlopeValue = (currentKey.Value - prevKey.Value) / (currentTime - prevTime);
				if (Mathf.Approximately(currentSlopeValue, prevSlopeValue))
				{
					skipIndexList.Add(prevIndex);
				}

				prevIndex = id;
				prevSlopeValue = currentSlopeValue;
			}

			for (int i = 0; i < keyList.Count; ++i)
			{
				if (skipIndexList.Contains(i))
				{
					continue;
				}

				var thisKey = keyList[i];
				var newKey = new AnImportKey(thisKey.KeySet);

				newKey.Time = thisKey.Time;
				newKey.Value = thisKey.Value;

				newKeyList.Add(newKey);
			}

			return newKeyList;
		}
	}
}
