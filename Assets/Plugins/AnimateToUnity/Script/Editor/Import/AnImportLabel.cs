using UnityEngine;
using System.Collections;
using AnimateToUnity;
using System.Xml;

namespace AnimateToUnity.Editor.Importer
{
	public class AnImportLabel
	{
		//=============================================================================================
		// Property
		//=============================================================================================

		public string Name { get; set; }

		public float StartTime { get; set; }

		public float EndTime { get; set; }

		public string NextLabel { get; set; }

		//=============================================================================================
		// Cunstructor
		//=============================================================================================

		public AnImportLabel()
		{

		}

		//=============================================================================================
		// Method
		//=============================================================================================

		/// -----------------------------------------
		/// <summary>
		/// ReadData
		/// </summary>
		/// -----------------------------------------
		public void ReadData( XmlNode rootNode )
		{
			Name = AnEditorUtilityXml.GetXmlNodeValue( rootNode, AnEditorValue.AnXmlNameTypes.Name.ToString() );

			StartTime = AnEditorUtilityString.GetFloatValue(AnEditorUtilityXml.GetXmlNodeValue( rootNode, AnEditorValue.AnXmlNameTypes.StartTime.ToString() ), 0, AnEditorValue.FloatAccuracy );
			EndTime = AnEditorUtilityString.GetFloatValue(AnEditorUtilityXml.GetXmlNodeValue( rootNode, AnEditorValue.AnXmlNameTypes.EndTime.ToString() ), 0, AnEditorValue.FloatAccuracy );

			NextLabel = AnEditorUtilityXml.GetXmlNodeValue( rootNode, AnEditorValue.AnXmlNameTypes.NextLabel.ToString() );
		}
	}
}
