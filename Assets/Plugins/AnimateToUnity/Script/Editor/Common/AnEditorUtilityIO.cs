using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using AnimateToUnity.Editor.Window;
using AnimateToUnity;

namespace AnimateToUnity.Editor
{
    public class AnEditorUtilityIO
    {
        /// -----------------------------------------
        /// <summary>
        /// Open Explorer
        /// </summary>
        /// -----------------------------------------
        public static void OpenExplorer(string dataPath)
        {
            string openDirPath = "";

            if (File.Exists(dataPath))
            {
                openDirPath = Path.GetDirectoryName(dataPath);
            }
            else if (Directory.Exists(dataPath))
            {
                openDirPath = dataPath;
            }

            if (openDirPath == "")
            {
                return;
            }

            if (!Directory.Exists(openDirPath))
            {
                return;
            }

            EditorUtility.RevealInFinder(openDirPath);
        }

        /// -----------------------------------------
        /// <summary>
        /// Get All Data Path List
        /// </summary>
        /// -----------------------------------------
        public static List<string> GetAllDataPathList(string targetDirPath)
        {
            List<string> allDataPathList = new List<string>();

            string rootPath = targetDirPath;

            if (!Directory.Exists(rootPath))
            {
                return allDataPathList;
            }

            string[] allCurrentDirPathList = Directory.GetDirectories(rootPath, "*", SearchOption.AllDirectories);

            for (int i = 0; i < allCurrentDirPathList.Length; i++)
            {
                allDataPathList.Add(allCurrentDirPathList[i].Replace("\\", "/"));
            }

            string[] allCurrentFilePathList = Directory.GetFiles(rootPath, "*", SearchOption.AllDirectories);

            for (int i = 0; i < allCurrentFilePathList.Length; i++)
            {
                string thisExt = Path.GetExtension(allCurrentFilePathList[i]).ToLower();

                if (thisExt == AnEditorValue.MetaExt)
                {
                    continue;
                }

                allDataPathList.Add(allCurrentFilePathList[i].Replace("\\", "/"));
            }

            return allDataPathList;
        }

        /// -----------------------------------------
		/// <summary>
        /// Copy Directory
		/// </summary>
		/// -----------------------------------------
		public static void CopyDirectory(string srcDirPath, string dstDirPath, bool overWrite, bool containMeta)
        {
            if (!Directory.Exists(srcDirPath))
            {
                return;
            }

            if (!Directory.Exists(dstDirPath))
            {
                Directory.CreateDirectory(dstDirPath);
                overWrite = true;
            }

            string[] fileList = Directory.GetFiles(srcDirPath, "*", SearchOption.TopDirectoryOnly);

            if (fileList.Length > 0)
            {
                foreach (string filePath in fileList)
                {
                    if (!containMeta)
                    {
                        if (filePath.ToLower().Contains(".meta"))
                        {
                            continue;
                        }
                    }

                    string copyPath = Path.Combine(dstDirPath, Path.GetFileName(filePath));

                    if (!overWrite)
                    {
                        if (File.Exists(copyPath))
                        {
                            continue;
                        }
                    }

                    File.Copy(filePath, copyPath, true);
                }
            }

            string[] dirList = Directory.GetDirectories(srcDirPath, "*", SearchOption.TopDirectoryOnly);

            if (dirList.Length > 0)
            {
                foreach (string dirPath in dirList)
                {
                    if (!containMeta)
                    {
                        if (dirPath.ToLower().Contains(".meta"))
                        {
                            continue;
                        }
                    }

                    string copyPath = Path.Combine(dstDirPath, Path.GetFileName(dirPath));
                    CopyDirectory(dirPath, copyPath, overWrite, containMeta);
                }
            }
        }

        /// -----------------------------------------
		/// <summary>
		/// 
		/// </summary>
		/// -----------------------------------------
		public static bool IsEmptyDirectory(string targetDirPath)
        {
            if (!Directory.Exists(targetDirPath))
            {
                return true;
            }

            string[] childFileList = Directory.GetFiles(targetDirPath, "*", SearchOption.TopDirectoryOnly);
            string[] childDirPathList = Directory.GetDirectories(targetDirPath, "*", SearchOption.TopDirectoryOnly);

            if (childFileList.Length == 0 && childDirPathList.Length == 0)
            {
                return true;
            }

            return false;
        }

        /// -----------------------------------------
		/// <summary>
		/// 
		/// </summary>
		/// -----------------------------------------
		public static void DeleteEmptyDirectory(string targetPath)
        {
            if (!Directory.Exists(targetPath))
            {
                return;
            }

            string[] childFileList = Directory.GetFiles(targetPath, "*", SearchOption.TopDirectoryOnly);
            string[] childDirPathList = Directory.GetDirectories(targetPath, "*", SearchOption.TopDirectoryOnly);

            if (childDirPathList.Length == 0 && childFileList.Length == 0)
            {
                try
                {
                    AssetDatabase.DeleteAsset(targetPath);
                }
                catch
                {

                }

                return;
            }

            foreach (string childDirPath in childDirPathList)
            {
                string[] tempChildFileList = Directory.GetFiles(childDirPath, "*", SearchOption.TopDirectoryOnly);
                string[] tempChildDirList = Directory.GetDirectories(childDirPath, "*", SearchOption.TopDirectoryOnly);

                if (tempChildDirList.Length != 0)
                {
                    foreach (string tempDir in tempChildDirList)
                    {
                        DeleteEmptyDirectory(tempDir);
                    }

                    tempChildDirList = Directory.GetDirectories(childDirPath, "*", SearchOption.TopDirectoryOnly);
                }

                if (tempChildDirList.Length != 0 || tempChildFileList.Length != 0)
                {
                    continue;
                }

                try
                {
                    AssetDatabase.DeleteAsset(childDirPath);
                }
                catch
                {

                }
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        /// -----------------------------------------
		/// <summary>
        /// 
		/// </summary>
		/// -----------------------------------------
		public static bool ComparePath(string srcPath, string dstPath)
        {
            srcPath = srcPath.Replace("\\", "/").ToLower();
            dstPath = dstPath.Replace("\\", "/").ToLower();

            if (srcPath == dstPath)
            {
                return true;
            }

            return false;
        }

        /// -----------------------------------------
		/// <summary>
        /// 
		/// </summary>
		/// -----------------------------------------
		public static string GetPathFromObject(UnityEngine.Object target, bool isDirectory = true)
        {
            string targetPath = AssetDatabase.GetAssetPath(target);

            if (isDirectory)
            {
                if (Directory.Exists(targetPath))
                {
                    return targetPath;
                }
            }

            if (File.Exists(targetPath))
            {
                return targetPath;
            }

            return null;
        }

        /// -----------------------------------------
		/// <summary>
		/// 
		/// </summary>
		/// -----------------------------------------
		public static string GetSingleFilePath(string parentDirectoryPath, string containString)
        {
            if (!Directory.Exists(parentDirectoryPath))
            {
                return null;
            }

            string[] targetFileList = Directory.GetFiles(parentDirectoryPath, "*" + containString + "*", SearchOption.AllDirectories);

            if (targetFileList.Length == 0)
            {
                return null;
            }

            return targetFileList[0].Replace("\\", "/");
        }

        /// -----------------------------------------
		/// <summary>
		/// Delete All Data
		/// </summary>
		/// -----------------------------------------
		public static void DeleteAllData(string targetDirPath, List<string> escapePathList = null)
        {
            if (escapePathList == null)
            {
                escapePathList = new List<string>();
            }

            if (targetDirPath == null)
            {
                return;
            }

            if (!Directory.Exists(targetDirPath))
            {
                return;
            }

            string[] childFileList = Directory.GetFiles(targetDirPath, "*", SearchOption.AllDirectories);
            foreach (string filePath in childFileList)
            {
                string fixFilePath = filePath.Replace("\\", "/").ToLower();

                if (!File.Exists(fixFilePath))
                {
                    continue;
                }

                bool exist = false;
                foreach (string escapePath in escapePathList)
                {
                    string fixEscapePath = escapePath.Replace("\\", "/").ToLower();

                    if (fixEscapePath == fixFilePath)
                    {
                        exist = true;
                        break;
                    }
                }

                if (exist)
                {
                    continue;
                }

                try
                {
                    AssetDatabase.DeleteAsset(fixFilePath);
                }
                catch
                {

                }
            }

            string[] childDirList = Directory.GetDirectories(targetDirPath, "*", SearchOption.AllDirectories);
            foreach (string dirPath in childDirList)
            {
                string fixDirPath = dirPath.Replace("\\", "/").ToLower();

                if (!Directory.Exists(fixDirPath))
                {
                    continue;
                }

                string[] thisChildFileList = Directory.GetFiles(fixDirPath, "*", SearchOption.AllDirectories);

                if (thisChildFileList.Length != 0)
                {
                    continue;
                }

                try
                {
                    AssetDatabase.DeleteAsset(fixDirPath);

                }
                catch
                {

                }
            }

            childFileList = Directory.GetFiles(targetDirPath, "*", SearchOption.AllDirectories);
            childDirList = Directory.GetDirectories(targetDirPath, "*", SearchOption.AllDirectories);

            if (childFileList.Length == 0 && childDirList.Length == 0)
            {
                try
                {
                    AssetDatabase.DeleteAsset(targetDirPath);
                }
                catch
                {

                }
            }

            AssetDatabase.Refresh();
        }

        /// -----------------------------------------
        /// <summary>
        /// Get String List By Reading File
        /// </summary>
        /// -----------------------------------------
        public static List<string> GetStringListByReadingFile(string targetFilePath)
        {
            if (!File.Exists(targetFilePath))
            {
                return null;
            }

            StreamReader sr = new StreamReader(targetFilePath);
            List<string> resultStringList = new List<string>();

            try
            {
                string line = null;
                while ((line = sr.ReadLine()) != null)
                {
                    resultStringList.Add(line);
                }
            }
            catch
            {

            }
            finally
            {
                if (sr != null)
                {
                    try
                    {
                        sr.Close();
                    }
                    catch
                    {

                    }
                }
            }

            return resultStringList;
        }

        /// -----------------------------------------
        /// <summary>
        /// Get String By Reading File
        /// </summary>
        /// -----------------------------------------
        public static string GetStringByReadingFile(string targetFilePath)
        {
            List<string> resultStringList = GetStringListByReadingFile(targetFilePath);

            if (resultStringList == null)
            {
                return "";
            }

            string resultString = "";
            for (int p = 0; p < resultStringList.Count; p++)
            {
                resultString += resultStringList[p];

                if (p < resultStringList.Count - 1)
                {
                    resultString += "\n";
                }
            }

            resultStringList = null;

            return resultString;
        }

        /// -----------------------------------------
		/// <summary>
		/// Read Binary
		/// </summary>
		/// -----------------------------------------
        public static byte[] ReadBinary(string targetFilePath)
        {
            if (!File.Exists(targetFilePath))
            {
                return null;
            }

            FileStream fileStream = new FileStream(targetFilePath, FileMode.Open, FileAccess.Read);

            BinaryReader br = new BinaryReader(fileStream);

            byte[] values = null;

            try
            {
                values = br.ReadBytes((int)br.BaseStream.Length);
            }
            catch
            {

            }
            finally
            {
                if (br != null)
                {
                    try
                    {
                        br.Close();
                        fileStream.Close();
                    }
                    catch
                    {

                    }
                }
            }

            return values;
        }

        /// -----------------------------------------
		/// <summary>
		/// Get Dir Size
		/// </summary>
		/// -----------------------------------------
        public static long GetDirSize(string targetDirPath, bool containMeta)
        {
            if (!Directory.Exists(targetDirPath)) 
            {
                return 0;
            }

            string[] allFilePathList = Directory.GetFiles(targetDirPath, "*", SearchOption.AllDirectories);

            if (allFilePathList.Length == 0)
            {
                return 0;
            }

            long allSize = 0;

            for (int p = 0; p < allFilePathList.Length; p++)
            {
                string thisFilePath = allFilePathList[p];

                if (!containMeta)
                {
                    if (thisFilePath.Contains(".meta"))
                    {
                        continue;
                    }
                }

                FileInfo thisFileInfo = new FileInfo(allFilePathList[p]);

                allSize += thisFileInfo.Length;
            }

            return allSize;
        }
    }
}