using UnityEngine;
using UnityEditor;
using UnityEditorInternal;
using System.IO;
using System.Collections.Generic;
using System.Reflection;
using AnimateToUnity;
using AnimateToUnity.Utility;
using AnimateToUnity.Debuger;

namespace AnimateToUnity.Editor.ComponentInspector
{
    [CustomEditor(typeof(AnObjectScrollListComponent))]
    public class AnObjectScrollListComponentInspector : AnDebugComponentBaseInspector
    {
        //=============================================================================================
        // Variables
        //=============================================================================================

        private AnObjectScrollListComponent _component = null;

        private GameObject _itemPrefabObject0 = null;
        private GameObject _itemPrefabObject1 = null;
        private GameObject _itemPrefabObject2 = null;
        private GameObject _itemPrefabObject3 = null;

        private int _itemObjectCount = 10;

        private int _itemInfoCount = 50;

        private int _targetIndex = 0;
        private bool _animation = true;

        private bool _itemStop = false;
        private bool _itemStopOneByOne = false;

        //=============================================================================================
        // Method
        //=============================================================================================

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override bool _Initialize()
        {
            if(!base._Initialize())
            {
                return false;
            }

            _component = target as AnObjectScrollListComponent;

            if (_component == null)
            {
                return false;
            }

            if(_component.ScrollList == null)
            {
                return false;
            }

            _itemStop = _component.ScrollList.IsItemStop;
            _itemStopOneByOne = _component.ScrollList.ItemStopOneByOne;
            
            return true;
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _DrawInspector()
        {
            _itemPrefabObject0 = AnEditorUtilityGUI.DrawObjectAndButton("Item Prefab 0", "SET", _itemPrefabObject0, _CreateObject) as GameObject;
            _itemPrefabObject1 = AnEditorUtilityGUI.DrawObjectAndButton("Item Prefab 1", "SET", _itemPrefabObject1, _CreateObject) as GameObject;
            _itemPrefabObject2 = AnEditorUtilityGUI.DrawObjectAndButton("Item Prefab 2", "SET", _itemPrefabObject2, _CreateObject) as GameObject;
            _itemPrefabObject3 = AnEditorUtilityGUI.DrawObjectAndButton("Item Prefab 2", "SET", _itemPrefabObject3, _CreateObject) as GameObject;

            _itemObjectCount = (int)AnEditorUtilityGUI.DrawSliderAndButton("Item Instance Count", "SET", _itemObjectCount, 1, 20, _CreateObject, true);

            AnEditorUtilityGUI.DrawSplitter();

            _itemInfoCount = (int)AnEditorUtilityGUI.DrawSliderAndButton("Item Info Count", "SET", _itemInfoCount, 0, 1000, _UpdateInfo, true);

            AnEditorUtilityGUI.DrawSplitter();

            _targetIndex = (int)AnEditorUtilityGUI.DrawSliderAndButton("Index", "SET", _targetIndex, 0, _itemInfoCount - 1, _SelectIndex, true);
            _animation = AnEditorUtilityGUI.DrawToggleAndButton("Animation", "SET", _animation, _SelectIndex);

            AnEditorUtilityGUI.DrawSplitter();

            _itemStop = AnEditorUtilityGUI.DrawToggleAndButton("Item Stop", "SET", _itemStop, _SetItemStop);
            _itemStopOneByOne = AnEditorUtilityGUI.DrawToggleAndButton("Item Stop One By One", "SET", _itemStopOneByOne, _SetItemStop);

            base._DrawInspector();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        protected override void _DrawStatusInspector()
        {
            base._DrawStatusInspector();

            AnEditorUtilityGUI.DrawObject("Range Start", _component.ScrollList.RangeStartObject);
            AnEditorUtilityGUI.DrawObject("Range End", _component.ScrollList.RangeEndObject);

            AnEditorUtilityGUI.DrawSplitter();

            AnEditorUtilityGUI.DrawObject("ScrollBar", _component.ScrollList.ScrollBar);

            AnEditorUtilityGUI.DrawSplitter();

            AnEditorUtilityGUI.DrawObject("CheckButtonList", _component.ScrollList.CheckButtonList);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _CreateObject()
        {
            _component.CreateItemObject<AnScrollItemObject>(_itemPrefabObject0, _itemObjectCount, 1000, "DebugItemRootObject", null);
            _component.AddItemObject<AnScrollItemObject>(_itemPrefabObject1, 1, _itemObjectCount, 1000);
            _component.AddItemObject<AnScrollItemObject>(_itemPrefabObject2, 2, _itemObjectCount, 1000);
            _component.AddItemObject<AnScrollItemObject>(_itemPrefabObject2, 3, _itemObjectCount, 1000);

            _UpdateInfo();
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _UpdateInfo()
        {
            if(_component.ItemObjectList == null)
            {
                return;
            }

            if (_component.ItemObjectList.Count == 0)
            {
                return;
            }

            int idMax = 1;
            
            if(_itemPrefabObject1 != null)
            {
                idMax = 2;
            }

            if (_itemPrefabObject2 != null)
            {
                idMax = 3;
            }

            if (_itemPrefabObject3 != null)
            {
                idMax = 4;
            }

            List<AnScrollItemInfo> newInfoList = new List<AnScrollItemInfo>();

            for(int i = 0; i < _itemInfoCount; i++)
            {
                AnScrollItemInfo info = new AnScrollItemInfo();

                info.SetObjectID(Random.Range(0, idMax));

                newInfoList.Add(info);
            }

            _component.SetItemInfoList<AnScrollItemInfo>(newInfoList);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _SelectIndex()
        {
            _component.ScrollList.SetScrollPositionFromItemIndex(_targetIndex, _animation);
        }

        /// -----------------------------------------
        /// <summary>
        /// 
        /// </summary>
        /// -----------------------------------------
        private void _SetItemStop()
        {
            _component.ScrollList.SetItemStop(_itemStop, _itemStopOneByOne);
        }
    }
}