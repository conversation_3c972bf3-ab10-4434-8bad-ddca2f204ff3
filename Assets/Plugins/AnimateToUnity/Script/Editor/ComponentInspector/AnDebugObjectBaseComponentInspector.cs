using UnityEngine;
using UnityEditor;
using UnityEditorInternal;
using System.IO;
using System.Collections.Generic;
using System.Reflection;
using AnimateToUnity.Debuger;

namespace AnimateToUnity.Editor.ComponentInspector
{
	[CustomEditor( typeof( AnDebugObjectBaseComponent ) )]
	public class AnDebugObjectBaseComponentInspector : AnDebugBaseComponentInspector
    {
		//=============================================================================================
		// Variables
		//=============================================================================================
        
		//=============================================================================================
		// Method
		//=============================================================================================
	}
}