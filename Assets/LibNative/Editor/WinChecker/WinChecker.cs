using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace LibNative
{
    /// <summary>
    /// Windows版ファイル改ざんチェッククラス
    /// </summary>
    public static class WinChecker
    {
        #region バレないように暗号化する必要な文字列

        private const string CheckFileFolder = "_Data\\\\StreamingAssets\\\\";

        private const string CheckFileName = "sun";

        /// <summary> 単純なHash計算だと解析されるので、適当な文字列を混ぜる. ビルドする毎に変化すると良い </summary>
        private const string Salt = "fh8X7ehMmb78";

        private const string PluginFolder = "_Data\\\\Plugins\\\\x86_64\\\\";

        /// <summary>
        /// システムファイルのルート証明書の組織名
        /// </summary>
        private const string RootCertificateOrgName = "Microsoft Corporation";

        /// <summary>
        /// 署名ありのシステムDLLファイルリスト
        /// </summary>
        static List<string> signedSystemFileList = new List<string>
        {
            "ntdll.dll",
            "kernel32.dll",
            "KernelBase.dll",
            "wintrust.dll",
            "msvcrt.dll",
            "rpcrt4.dll",
            "user32.dll",
            "win32u.dll",
            "gdi32.dll",
            "gdi32full.dll",
            "msvcp_win.dll",
            "ucrtbase.dll",
            "ole32.dll",
            "version.dll",
            "combase.dll",
            "shlwapi.dll",
            "setupapi.dll",
            "cfgmgr32.dll",
            "bcrypt.dll",
            "advapi32.dll",
            "sechost.dll",
            "shell32.dll",
            "oleaut32.dll",
            "winmm.dll",
            "imm32.dll",
            "crypt32.dll",
            "IPHLPAPI.DLL",
            "ws2_32.dll",
            "winhttp.dll",
            "msasn1.dll",
            "cryptsp.dll",
            "rsaenh.dll",
            "cryptbase.dll",
            "bcryptprimitives.dll",
            "imagehlp.dll",
            "gpapi.dll",
            "nsi.dll",
            "profapi.dll",
            "kernel.appcore.dll",
            "SHCore.dll",
            "windows.storage.dll",
            "wldp.dll",
            "mswsock.dll",
            "dnsapi.dll",
            "WindowsCodecs.dll",
            "msctf.dll",
            "d3d11.dll",
            "dxgi.dll",
            "ResourcePolicyClient.dll",
            "drvstore.dll",
            "devobj.dll",
            "DXCore.dll",
            "clbcatq.dll",
            "userenv.dll",
            "MMDevAPI.dll",
            "AudioSes.dll",
            "powrprof.dll",
            "umpdc.dll",
            "avrt.dll",
            "dcomp.dll",
            "dwmapi.dll",
            "TextInputFramework.dll",
            "CoreUIComponents.dll",
            "CoreMessaging.dll",
            "ntmarta.dll",
            "WinTypes.dll",
            "mfplat.dll",
            "propsys.dll",
            "RTWorkQ.dll",
            "Windows.UI.dll",
            "InputHost.dll",
            "twinapi.appcore.dll",
            "msmpeg2vdec.dll",
            "mfperfhelper.dll"
        };
        private const string Tag = "Tag: ";
        private const string TagExe = "exe";
        private const string TagPlugin = "plugin";

        /// <summary>
        /// 署名なしのシステムDLLファイルリスト
        /// </summary>
        static List<string> unsigedSystemFileList = new List<string>
        {
            "opengl32.dll",
            "glu32.dll",
            "hid.dll",
            "uxtheme.dll",
            "dhcpcsvc6.dll",
            "dhcpcsvc.dll",            
            "IconCodecService.dll",
            "wbem\\wbemprox.dll",
            "wbemcomn.dll",
            "wbem\\wbemsvc.dll",
            "wbem\\fastprox.dll",
            "amsi.dll",
            "XInput9_1_0.dll",
            "Wldap32.dll",
            "normaliz.dll",
            "FWPUCLNT.DLL",
            "rasadhlp.dll"
        };
        #endregion

        #region テンプレートで置き換える必要な文字列

        /// <summary>
        /// 無視するファイルリスト
        /// 例："\"file1\", \"file2\""
        /// </summary>
        private const string IgnoreFileTempMark = "%IGNORE_FILES%";

        /// <summary>
        /// wincheckerを実行するためのstruct
        /// 名前は何でもいいのでランダムで生成する
        /// </summary>
        private const string StructNameTempMark = "%STRUCT_NAME%";

        /// <summary>
        /// wincheckerを実行するためのstructタイプのグローバル変数
        /// main関数が呼ばれる前にstructのコンストラクタが呼ばれる
        /// 実際にどこでも使ってない変数なので、名前はランダムで生成する
        /// </summary>
        private const string GlobalVariableNameTempMark = "%GLOBAL_VARIABLE_NAME%";

        /// <summary>
        /// MSのルート証明書の発行者の名前
        /// </summary>
        private const string RootCertificateTempMark = "%ROOT_CERT_ORG_NAME%";

        /// <summary>
        /// ロードされたシステムDLLファイルの中でデジタル署名が付いてるファイルのリスト
        /// </summary>
        private const string SignedSystemFilesTempMark = "%SIGNED_SYSTEM_FILES%";

        /// <summary>
        /// ロードされたシステムDLLファイルの中でデジタル署名が付いてないファイルのリスト
        /// </summary>
        private const string UnsignedSystemFilesTempMark = "%UNSIGNED_SYSTEM_FILES%";

        /// <summary>
        /// チェックファイルのフォルダ
        /// </summary>
        private const string CheckFileFolderTempMark = "%CHECK_FILE_FOLDER%";

        /// <summary>
        /// チェックファイルの名前
        /// </summary>
        private const string CheckFileNameTempMark = "%CHECK_FILE_NAME%";

        /// <summary>
        /// ソルト
        /// </summary>
        private const string SaltTempMark = "%SALT%";

        /// <summary>
        /// プロジェクト名
        /// </summary>
        private const string ProjectNameTempMark = "%PROJECT_NAME%";

        /// <summary>
        /// プラグインフォルダ
        /// </summary>
        private const string PluginFolderTempMark = "%PLUGIN_FOLDER%";

        private const string TagMark = "%TAG%";
        private const string TagExeMark = "%TAG_EXE%";
        private const string TagPluginMark = "%TAG_PLUGIN%";
        #endregion

        /// <summary>
        /// ランダムで文字列を生成する用
        /// </summary>
        private const string letters = "abcdefghijklmnopqrstuvwxyz";

        private static readonly byte[] SaltBytes = Encoding.UTF8.GetBytes(Salt);

        /// <summary>
        /// ランダムで文字列を生成する
        /// </summary>
        /// <param name="random"></param>
        /// <param name="minLength"></param>
        /// <param name="maxLength"></param>
        /// <returns></returns>
        public static string GenerateRandomIdentifier(System.Random random, int minLength = 5, int maxLength = 15)
        {
            int length = random.Next(minLength, maxLength + 1);
            StringBuilder identifier = new StringBuilder(length);

            for (int i = 0; i < length; i++)
            {
                identifier.Append(letters[random.Next(letters.Length)]);
            }
            return identifier.ToString();
        }

        /// <summary>
        /// テンプレートファイルのテンプレートの部分を置き換える処理
        /// </summary>
        /// <param name="projectFolder"></param>
        /// <param name="fileName"></param>
        /// <param name="replaceDict"></param>
        private static void UpdateTemplateFile(string projectFolder, string fileName, Dictionary<string, string> replaceDict)
        {
            var filePath = Path.Combine(projectFolder, fileName);
            if (!File.Exists(filePath))
            {
                UnityEngine.Debug.LogError($"File {filePath} does not exist!");
                return;
            }
            if (replaceDict == null || replaceDict.Count == 0)
            {
                return;
            }
            var fileContent = File.ReadAllText(filePath);
            foreach (var pair in replaceDict)
            {
                fileContent = fileContent.Replace(pair.Key, pair.Value);
            }
            File.WriteAllText(filePath, fileContent);
        }

        /// <summary>
        /// 複号用C++コードを生成する
        /// </summary>
        /// <param name="encryptTargetString"></param>
        /// <param name="isUnicode"></param>
        /// <returns></returns>
        private static string GetDecryptCode(string encryptTargetString, bool isUnicode)
        {
            if (isUnicode)
                return $"Util::DecryptWString(L\"{EncryptString(encryptTargetString)}\").c_str()";
            else
                return $"Util::DecryptString(\"{EncryptString(encryptTargetString)}\").c_str()";
        }

        /// <summary>
        /// すべてのテンプレートファイルを更新する
        /// </summary>
        /// <param name="vsSolutionPath"></param>
        private static void UpdateTemplateFiles(string vsSolutionPath)
        {
            var productName = UnityEditor.PlayerSettings.productName;
            var projectFolder = Path.Combine(vsSolutionPath, productName);

            // Main.cpp
            var random = new System.Random();
            var structName = GenerateRandomIdentifier(random).ToUpper();
            var variableName = GenerateRandomIdentifier(random).ToLower();
            var replaceDict = new Dictionary<string, string>() 
            {
                { IgnoreFileTempMark, "" },
                { StructNameTempMark, structName },
                { GlobalVariableNameTempMark, variableName }
            };
            UpdateTemplateFile(projectFolder, "Main.cpp", replaceDict);

            // MyWinTrust.cpp
            replaceDict = new Dictionary<string, string>()
            {
                { RootCertificateTempMark, GetDecryptCode(RootCertificateOrgName, true) }
            };
            UpdateTemplateFile(projectFolder, "MyWinTrust.cpp", replaceDict);

            // Util.cpp
            replaceDict.Clear();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < signedSystemFileList.Count; i++)
            {
                var fileName = signedSystemFileList[i];
                if (i == signedSystemFileList.Count - 1)
                {
                    sb.Append($"        {GetDecryptCode(fileName, true)}");
                }
                else
                {
                    sb.AppendLine($"        {GetDecryptCode(fileName, true)},");
                }
            }
            replaceDict.Add(SignedSystemFilesTempMark, sb.ToString());
            sb.Clear();
            for (int i = 0; i < unsigedSystemFileList.Count; i++)
            {
                var fileName = unsigedSystemFileList[i];
                if (i == unsigedSystemFileList.Count - 1)
                {
                    sb.Append($"        {GetDecryptCode(fileName, true)}");
                }
                else
                {
                    sb.AppendLine($"        {GetDecryptCode(fileName, true)},");
                }
            }
            replaceDict.Add(UnsignedSystemFilesTempMark, sb.ToString());
            UpdateTemplateFile(projectFolder, "Util.cpp", replaceDict);

            // WinChecker.cpp
            replaceDict = new Dictionary<string, string>()
            {
                { CheckFileFolderTempMark, GetDecryptCode(CheckFileFolder, false) },
                { CheckFileNameTempMark, GetDecryptCode(CheckFileName, false) },
                { SaltTempMark, GetDecryptCode(Salt, false) },
                { ProjectNameTempMark, GetDecryptCode(productName, false)},
                { PluginFolderTempMark, GetDecryptCode(PluginFolder, false) },
                { TagMark, GetDecryptCode(Tag, false) },
                { TagExeMark, GetDecryptCode(TagExe, false) },
                { TagPluginMark, GetDecryptCode(TagPlugin, false)}
            };
            UpdateTemplateFile(projectFolder, "WinChecker.cpp", replaceDict);
        }

        /// <summary>
        /// テンプレートファイル名から元のファイル名に復元する
        /// </summary>
        /// <param name="tempFilePath"></param>
        /// <returns></returns>
        private static string RestoreFileName(string tempFilePath)
        {
            var fileName = Path.GetFileName(tempFilePath);
            fileName = fileName.Substring(0, fileName.Length - 4);
            return fileName;
        }

        /// <summary>
        /// プロジェクト設定を変更する
        /// </summary>
        /// <param name="vsSolutionPath"></param>
        /// <param name="configuration"></param>
        /// <param name="productName"></param>
        /// <param name="cppFileNames"></param>
        /// <param name="headerFileNames"></param>
        private static void ChangeProjectSettings(string vsSolutionPath, string configuration, string productName, IEnumerable<string> cppFileNames, IEnumerable<string> headerFileNames)
        {
            var projectFilePath = Path.Combine(vsSolutionPath, productName, $"{productName}.vcxproj");
            if (!File.Exists(projectFilePath))
            {
                UnityEngine.Debug.LogError($"File {projectFilePath} does not exist!");
                return;
            }
            var content = File.ReadAllText(projectFilePath);

            var cppFilesSettingString = new StringBuilder();
            foreach (var cppFileName in cppFileNames)
            {
                if (cppFileName == "Main.cpp")
                {
                    continue;
                }
                cppFilesSettingString.Append($"\n    <ClCompile Include=\"{cppFileName}\" />");
            }

            var headerFilesSettingString = new StringBuilder();
            foreach(var headerFileName in headerFileNames)
            {
                headerFilesSettingString.Append($"\n    <ClInclude Include=\"{headerFileName}\" />");
            }

            content = content.Replace("<ClCompile Include=\"Main.cpp\" />", $"<ClCompile Include=\"Main.cpp\" />{cppFilesSettingString.ToString()}")
                             .Replace("<ClInclude Include=\"PrecompiledHeader.h\" />", $"<ClInclude Include=\"PrecompiledHeader.h\" />{headerFilesSettingString.ToString()}");
            content = content.Replace("<LargeAddressAware>true</LargeAddressAware>",
                                      "<LargeAddressAware>true</LargeAddressAware>\n<DelayLoadDLLs Condition=\"'$(Configuration)|$(Platform)'=='" + configuration + "|x64'\">UnityPlayer.dll</DelayLoadDLLs>");
            File.WriteAllText(projectFilePath, content);
        }

        /// <summary>
        /// WinChecker関連のファイルをプロジェクトに追加する
        /// </summary>
        /// <param name="vsSolutionPath"></param>
        /// <param name="configuration"></param>
        private static void AddWinCheckerFilesToProject(string vsSolutionPath, string configuration)
        {
            var cppTempFolderPath = Path.Combine(Application.dataPath, "LibNative", "Editor", "WinChecker", "CppTemplates");
            var productName = UnityEditor.PlayerSettings.productName;

            var cppFiles = Directory.GetFiles(cppTempFolderPath, "*.cpptemp");
            var headerFiles = Directory.GetFiles(cppTempFolderPath, "*.htemp");

            var allFiles = cppFiles.Concat(headerFiles);
            foreach (var file in allFiles)
            {
                var fileName = RestoreFileName(file);
                var destination = Path.Combine(vsSolutionPath, productName, fileName);
                File.Copy(file, destination, true);
            }

            var cppFileNames = cppFiles.Select(p => {
                return RestoreFileName(p);
            });

            var headerFileNames = headerFiles.Select(p => {
                return RestoreFileName(p);
            });

            ChangeProjectSettings(vsSolutionPath, configuration, productName, cppFileNames, headerFileNames);
        }

        /// <summary>
        /// WinCheckerのハッシュ値チェック処理をUnityMain関数が呼ばれる前に移動する
        /// UnityMain関数が呼ばれるとUnityPlayer.dll及びほとんどのDLLファイルがロードされるので、Hookでチェック処理を無効化することができる
        /// </summary>
        public static void AddWinCheckerToCppProject(string vsSolutionPath, string configuration)
        {
#if !DISABLE_WINCHECKER
            // winchecker関連のファイルをプロジェクトに追加する
            AddWinCheckerFilesToProject(vsSolutionPath, configuration);

            // 各ファイル設定
            UpdateTemplateFiles(vsSolutionPath);
#endif // ENABLE_CRACK_PROOF
        }

        /// <summary>
        /// Windows版の改ざんチェック用ファイルを出力する
        /// 全てのビルドが完了した後に呼ぶこと
        /// CrackProofのような処理をかける場合は、CrackProofの処理後にこの関数を呼ぶこと
        /// ファイル名もチェックしてるので、この後ファイル名の変更してはいけない
        /// </summary>
        public static void PostBuild()
        {
            string[] args = System.Environment.GetCommandLineArgs();
            if (args.Length <= 0)
            {
                Debug.LogError("##############################################################################################\n\n");
                Debug.LogError("OutputPath　 引数が指定されていません。");
                Debug.LogError("\n\n##############################################################################################");
                EditorApplication.Exit(1);
            }

            string outputPath = string.Empty;
            for (int i = 0; i < args.Length; ++i)
            {
                // -executeMethodの引数を探す
                if (args[i].Contains("-executeMethod"))
                {
                    outputPath = args[i + 2];
                    break;
                }
            }

            ComputeAndSaveHash(outputPath);
        }

        private static void ComputeAndSaveHash(string outputPath)
        {
            using var sha256 = SHA256.Create();
            List<byte> bytes = new List<byte>();

            var res = ComputeHash(outputPath, sha256, TagExe);
            bytes.AddRange(res);

            var pluginDiretories = Directory.GetDirectories(outputPath, "Plugins", SearchOption.AllDirectories);
            if (pluginDiretories.Length == 0)
            {
                UnityEngine.Debug.LogError("Plugins folder not found.");
                return;
            }
            var pluginDirectory = pluginDiretories[0];
            var x64PluginDirectory = System.IO.Path.Combine(pluginDirectory, "x86_64");
            res = ComputeHash(x64PluginDirectory, sha256, TagPlugin);
            bytes.AddRange(res);

            var rootDirectory = Directory.GetDirectories(outputPath, "StreamingAssets", SearchOption.AllDirectories)[0];
            File.WriteAllBytes($"{rootDirectory}/{CheckFileName}", bytes.ToArray());
            UnityEngine.Debug.Log($"The file '{CheckFileName}' has been saved to the StreamingAssets folder.");
        }

        private static List<byte> ComputeHash(string directoryPath, SHA256 sha256, string fileTag)
        {
            List<byte> bytes = new List<byte>();
            // .dll、.exeと._(crackproof)のみ処理する
            var files = Directory.GetFiles(directoryPath).Where(file => file.EndsWith(".dll", StringComparison.OrdinalIgnoreCase) 
                                                                        || file.EndsWith(".exe", StringComparison.OrdinalIgnoreCase) 
                                                                        || file.EndsWith("._"));
            foreach (string file in files)
            {
                var keyHash = sha256.ComputeHash(Encoding.UTF8.GetBytes(Tag + fileTag + " " + System.IO.Path.GetFileName(file)).Concat(SaltBytes).ToArray());
                var hash = sha256.ComputeHash(File.ReadAllBytes(file).Concat(SaltBytes).ToArray());
                bytes.AddRange(keyHash);
                bytes.AddRange(hash);
            }
            return bytes;
        }

        /// <summary>
        /// 文字列を暗号化する
        /// 複号処理はC++側で行う
        /// </summary>
        /// <param name="original"></param>
        /// <returns></returns>
        private static string EncryptString(string original)
        {
            if (original == null)
            {
                return string.Empty;
            }

            const int shift = 5; // Caesar Cipher shift value
            const byte xorKey = 0xAA; // XOR key
                                      // Convert string to char array
            char[] temp = new char[256];
            int lengthToCopy = Math.Min(original.Length, temp.Length - 1);
            original.CopyTo(0, temp, 0, lengthToCopy);
            temp[lengthToCopy] = '\0'; // Ensure null-termination
                                       // Caesar Cipher Encryption
            CaesarCipher(temp, shift);
            // XOR Encryption
            XOREncryption(temp, (char)xorKey);
            // Convert encrypted data to hex string
            StringBuilder hexString = new StringBuilder();
            for (int i = 0; i < temp.Length && temp[i] != '\0'; ++i)
            {
                hexString.AppendFormat("{0:x2}", (byte)temp[i]);
            }
            return hexString.ToString();
        }

        private static void CaesarCipher(char[] data, int shift)
        {
            for (int i = 0; i < data.Length && data[i] != '\0'; ++i)
            {
                if (char.IsLetter(data[i]))
                {
                    char baseChar = char.IsLower(data[i]) ? 'a' : 'A';
                    data[i] = (char)(((data[i] - baseChar + shift) % 26) + baseChar);
                }
            }
        }

        private static void XOREncryption(char[] data, char key)
        {
            for (int i = 0; i < data.Length && data[i] != '\0'; ++i)
            {
                data[i] ^= key;
            }
        }

        [MenuItem("WinChecker/GenerateHashFile")]
        public static void GenerateHashFile()
        {
            string folder = EditorUtility.OpenFolderPanel("Select Folder", "", "");
            if (!string.IsNullOrEmpty(folder))
            {
                Debug.Log("Selected folder: " + folder);
                ComputeAndSaveHash(folder);
            }
            else
            {
                Debug.Log("No folder selected");
            }
        }
    }
}
