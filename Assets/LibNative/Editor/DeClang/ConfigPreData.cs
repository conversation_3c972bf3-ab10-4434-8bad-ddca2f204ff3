using System;
using System.Collections.Generic;
using UnityEngine;

namespace DeClang
{
    [CreateAssetMenu(fileName = "DeClangConfigPreData", menuName = "DeClang/ConfigPreData", order = 1)]
    public class ConfigPreData : ScriptableObject
    {
        // jsonにコンバートするため、変数名が小文字で始まる
#pragma warning disable IDE1006
        public const string BUILD_SEED_DEFAULT = "hello, i am seed";

        [Serializable]
        public class Flatten
        {
            [Tooltip("関数名（正規表現）。空文字不可。「クラス名_関数名」の形でクラス名の指定も可能。")]
            public string name;

            [Tooltip("難読化の際に使用するシード値。空の場合はランダムで生成する。")]
            public string seed;

            [Tooltip("基本ブロックを分割するレベル（０〜１０）。")]
            [Range(0, 10)]
            public int split_level = 3;
        }

        [SerializeField]
        [Tooltip("flattenのseedをランダムで生成する際に利用されるシード。デフォルトはランダムで生成する。")]
        string build_seed = BUILD_SEED_DEFAULT;

#pragma warning disable CS0414
        [SerializeField]
        [Tooltip("コード全体に適用される簡易難読化の強さ（０〜１００）。実際に値を調整してみたが、調整前後の変化が少ない。")]
        [Range(0, 100)]
        int overall_obfuscation = 0;
#pragma warning restore CS0414

        [SerializeField]
        int enable_obfuscation = 1;

        [SerializeField]
        [Tooltip("制御フロー平坦化のconfig情報リスト")]
        List<Flatten> flatten = new List<Flatten>();

        string build_seed_prevalue;

        int additionalFlattenSize = 0;
#pragma warning restore IDE1006

        public bool SetBuildSeedWithDefaultValue
        {
            set
            {
                if (value)
                {
                    build_seed_prevalue = build_seed;
                    build_seed = BUILD_SEED_DEFAULT;
                }
                else
                {
                    build_seed = build_seed_prevalue;
                }
            }
            get
            {
                return build_seed == BUILD_SEED_DEFAULT;
            }
        }

        public bool EnableObfuscation
        {
            set
            {
                enable_obfuscation = value ? 1 : 0;
            }
            get
            {
                return enable_obfuscation == 1;
            }
        }

        /// <summary>
        /// nameが空の場合はエラーが出る
        /// </summary>
        public bool EmptyNameExists
        {
            get
            {
                return flatten.Exists(e => string.IsNullOrEmpty(e.name));
            }
        }

        public void ReGenerateBuildSeed()
        {
            build_seed = Guid.NewGuid().ToString("N").Substring(0, 16);
        }

        /// <summary>
        /// 制御フロー平坦化のconfig情報リストをマージする
        /// </summary>
        /// <param name="newFlattens"></param>
        public void MergeFlattens(List<Flatten> newFlattens)
        {
            flatten.AddRange(newFlattens);
            additionalFlattenSize =+ newFlattens.Count;
        }

        /// <summary>
        /// 制御フロー平坦化のconfig情報リストをマージされる前の状態に復元する
        /// </summary>
        public void RestoreFlattens()
        {
            var index = flatten.Count - additionalFlattenSize;
            flatten.RemoveRange(index, additionalFlattenSize);
        }
    }
}
