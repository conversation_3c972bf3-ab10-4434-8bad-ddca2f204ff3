using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Text.RegularExpressions;
using System.IO;
using Newtonsoft.Json;
using System.Text;
using System;
using System.Linq;
using UnityEditor;

namespace DeClang
{
    public class PostProcessBuild
    {
        /// <summary>
        /// 一部のAssemblyをDeClangで再ビルドする
        /// </summary>
        /// <param name="vsSolutionPath"></param>
        /// <param name="msBuildPath"></param>
        /// <param name="il2cppOutputProjectPath"></param>
        /// <param name="buildConfig"></param>
        /// <param name="encoding"></param>
        /// <param name="deClangHome"></param>
        /// <param name="config"></param>

        public static void BuildAssembliesWithDeClang(string vsSolutionPath, string msBuildPath, string il2cppOutputProjectPath, string buildConfig, System.Text.Encoding encoding, string deClangHome, string config)
        {
#if !DISABLE_DECLANG && UNITY_2021_2_OR_NEWER
            // DeClangでAssembly-CSharp.cppを再ビルドする
            var beeDagJsonPath = System.IO.Path.Combine(vsSolutionPath, @"build\obj\il2cppOutputProject\x64\" + config + @"\buildstate", "bee.dag.json");
            var clangClPath = System.IO.Path.Combine(deClangHome, @".DeClang\compiler\bin", "clang-cl.exe");

            ReBuildAssembliesWithDeClang(deClangHome, beeDagJsonPath);

            var result = BuildUtil.ExecuteShell(msBuildPath, $"\"{il2cppOutputProjectPath}\" {buildConfig} -t:Build", null, encoding);
            UnityEngine.Debug.Log("result: " + result);
#endif
        }

        /// <summary>
        /// 一部のAssemblyをDeClangで再ビルドする
        /// </summary>
        /// <param name="deClangHome"></param>
        /// <param name="beeDagJsonPath"></param>
        static void ReBuildAssembliesWithDeClang(string deClangHome, string beeDagJsonPath)
        {
            // clang-cl.exeのパスを取得
            var clangClPath = System.IO.Path.Combine(deClangHome, @".DeClang\compiler\bin", "clang-cl.exe");

            var workingDirectory = Path.GetDirectoryName(beeDagJsonPath);

            // 「Assebly名＋.cpp」のファイル名リストを生成
            var assemblyNames = AppDomain.CurrentDomain.GetAssemblies().Where(a => !LibNative.EncryptionCommon.ExcludeTheAssembly(a)).Select(a => a.GetName().Name);

            // bee.dag.jsonファイルを読み込み
            var beeDagJsonText = File.ReadAllText(beeDagJsonPath);
            Il2CppBeeDag beeDag = JsonConvert.DeserializeObject<Il2CppBeeDag>(beeDagJsonText);
            foreach (var node in beeDag.Nodes)
            {
                if (!string.IsNullOrEmpty(node.Action))
                {
                    var cmd = Regex.Replace(node.Action, "\".*cl.exe\"", "");

                    // ワーニング解消
                    cmd = cmd + " -Wno-invalid-token-paste -Wno-unused-but-set-variable -Wno-unused-label -Wno-missing-declarations " +
                        "-Wno-unused-command-line-argument -Wno-unused-variable -Wno-invalid-offsetof -Wno-reorder-ctor";

                    if (Regex.IsMatch(node.Action, "/Yc.*pch-cpp.hpp"))
                    {
                        // declangビルド用のpchに変更
                        cmd = cmd.Replace(".pch", "_declang.pch").Replace(".obj", "_declang.obj");
                    }
                    else
                    {
                        bool isAppend = false;
                        foreach (var name in assemblyNames)
                        {
                            foreach (var input in node.Inputs)
                            {
                                if (Regex.IsMatch(input, name + @"(__\d+)?\.cpp"))
                                {
                                    // declangビルド用のpchに変更
                                    cmd = cmd.Replace(".pch", "_declang.pch");
                                    isAppend = true;
                                    break;
                                }
                            }
                            if (isAppend)
                            {
                                break;
                            }
                        }

                        if (!isAppend)
                        {
                            continue;
                        }
                    }
                    
                }
            }
        }

        public static void OnPostProcessBuildForAndroid(string declangHome)
        {
#if !DISABLE_DECLANG && UNITY_EDITOR_OSX
            // ndk_unset.sh実行
            string unityExecPath = System.Environment.GetCommandLineArgs()[0];
            string unityApplicationPath = new System.Text.RegularExpressions.Regex(@"^(.+\.app)/.*").Replace(unityExecPath, "$1");
            string unityRootPath = Directory.GetParent(unityApplicationPath).FullName;
            var ndkPath = Path.Combine(unityRootPath, "PlaybackEngines", "AndroidPlayer", "NDK");

            var unsetFilePath = Path.Combine(declangHome, ".DeClang", "script", "ndk_unset.sh");
            Coneshell.BuildUtil.ExecuteShell("bash", $"\"{unsetFilePath}\" \"{ndkPath}\"");
#endif
        }

        public static void OnPostProcessBuildForIOS(string outputPath, string declangHome, string xcodeAppPath)
        {
#if !DISABLE_DECLANG
            // xcode_setup.sh実行
            var setupFilePath = Path.Combine(declangHome, ".DeClang", "script", "xcode_setup.sh");
            var xcodeProjPath = Path.Combine(outputPath, "Unity-iPhone.xcodeproj");
            Coneshell.BuildUtil.ExecuteShell("bash", $"\"{setupFilePath}\" -x \"{xcodeAppPath}\" -p \"{xcodeProjPath}\"");
#endif
        }
    }
}
