using System.Collections.Generic;
using UnityEngine;
using System.IO;
using UnityEditor;
using static DeClang.ConfigPreData;
using System;
using System.Reflection;

namespace DeClang
{
    public class PreProcessBuild
    {
        /// <summary>
        /// config.jsonファイルを生成する処理
        /// </summary>
        /// <param name="declangHome"></param>
        /// <param name="configPreDataFilePath"></param>
        public static void OnPreProcessBuildGenConfig(string declangHome, string configPreDataFilePath)
        {
#if !DISABLE_DECLANG
            // config.pre.jsonを生成
            var configPreData = AssetDatabase.LoadAssetAtPath<ConfigPreData>(configPreDataFilePath);
            if (configPreData != null)
            {
                if (configPreData.EmptyNameExists)
                {
                    UnityEngine.Debug.LogError("flattenにnameが定義されていない項目がある。");
                    return;
                }

                configPreData.MergeFlattens(GetFlattenListFromAttributes());
                var json = JsonUtility.ToJson(configPreData, true);
                configPreData.RestoreFlattens();
                UnityEngine.Debug.Log("json: " + json);
                var configPreFilePath = Path.Combine(declangHome, ".DeClang", "config.pre.json");
                if (File.Exists(configPreFilePath))
                {
                    File.Delete(configPreFilePath);
                }
                File.WriteAllText(configPreFilePath, json);
            }
            else
            {
                UnityEngine.Debug.LogError("configPreData is null!");
                return;
            }

            // genconfigを実行
#if UNITY_EDITOR_OSX
            var genConfigShellPath = Path.Combine(declangHome, ".DeClang", "gen_config.sh");
            var result = Coneshell.BuildUtil.ExecuteShell("bash", $"\"{genConfigShellPath}\" -path \"{declangHome}/.DeClang/\"");
#elif UNITY_EDITOR_WIN
            var genConfigExePath = Path.Combine(declangHome, ".DeClang", "gen_config", "bin", "Windows", "gen_config.exe");
            var result = Coneshell.BuildUtil.ExecuteShell(genConfigExePath, $"-path \"{declangHome}\\.DeClang\"");
#endif
#endif
        }

        /// <summary>
        /// Androidビルドの事前処理
        /// </summary>
        /// <param name="declangHome"></param>
        public static void OnPreProcessBuildForAndroid(string declangHome)
        {
#if UNITY_EDITOR_OSX
            string unityExecPath = System.Environment.GetCommandLineArgs()[0];
            string unityApplicationPath = new System.Text.RegularExpressions.Regex(@"^(.+\.app)/.*").Replace(unityExecPath, "$1");
            string unityRootPath = Directory.GetParent(unityApplicationPath).FullName;
            var ndkPath = Path.Combine(unityRootPath, "PlaybackEngines", "AndroidPlayer", "NDK");

            // リセット
            var unsetFilePath = Path.Combine(declangHome, ".DeClang", "script", "ndk_unset.sh");
            if (File.Exists(unsetFilePath))
            {
                Coneshell.BuildUtil.ExecuteShell("bash", $"\"{unsetFilePath}\" \"{ndkPath}\"");
            }

#if !DISABLE_DECLANG
            // キャッシュ削除
            var unityProjPath = Path.GetDirectoryName(Application.dataPath);
            var unityLibraryPath = Path.Combine(unityProjPath, "Library");
            var subDirs = Directory.GetDirectories(unityLibraryPath);
            foreach (var dir in subDirs)
            {
                var dirName = Path.GetFileName(dir);

                // 少し古いバージョンのUnityならこちら
                if (dirName.StartsWith("il2cpp_android_"))
                {
                    UnityEngine.Debug.Log($"delete {dir}");
                    Directory.Delete(dir, true);
                }

                if (dirName == "Il2cppBuildCache")
                {
                    UnityEngine.Debug.Log($"delete {dir}");
                    Directory.Delete(dir, true);
                }

                // 新しいバージョンのUnityならこちら
                if (dirName == "Bee")
                {
                    UnityEngine.Debug.Log($"delete {dir}");
                    Directory.Delete(dir, true);
                }
            }

            // 一部Unityビルドエラー対応
            UnityEditor.PlayerSettings.SetAdditionalIl2CppArgs(@"--compiler-flags=""-Wno-error=implicit-function-declaration""");

            // ndk_setup.sh実行
            var setupFilePath = Path.Combine(declangHome, ".DeClang", "script", "ndk_setup.sh");
            Coneshell.BuildUtil.ExecuteShell("bash", $"\"{setupFilePath}\" \"{ndkPath}\"");
#endif
#endif
        }

        /// <summary>
        /// 制御フロー平坦化Attributeを読み込んで、平坦化関連のconfig情報リストを取得する
        /// </summary>
        /// <returns></returns>
        private static List<Flatten> GetFlattenListFromAttributes()
        {
            var flattens = new List<Flatten>();
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                Utility.GetFlattenListFromAttributes(flattens, assembly);
            }
            return flattens;
        }
    }
}