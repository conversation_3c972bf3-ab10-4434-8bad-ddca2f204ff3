using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using Mono.Cecil;
using Mono.Cecil.Cil;
using UnityEngine;

namespace LibNative
{
    public class EncryptionCommon
    {
        /// <summary>
        /// 新しい関数名を取得する
        /// </summary>
        /// <param name="random">関数名の長さをランダムで決める</param>
        /// <param name="functionName">古い関数名</param>
        /// <param name="minLength"></param>
        /// <param name="maxLength"></param>
        /// <returns></returns>
        public static string GetNewFunctionName(System.Random random, string functionName, int minLength, int maxLength)
        {
            if (random == null)
            {
                return null;
            }

            // saltを加える
            var saltLength = random.Next(10, 20);
            var salt = new byte[saltLength];
            random.NextBytes(salt);

            var nameBytes = Encoding.UTF8.GetBytes(functionName);
            var nameWithSaltBytes = new byte[nameBytes.Length + salt.Length];
            Buffer.BlockCopy(nameBytes, 0, nameWithSaltBytes, 0, nameBytes.Length);
            Buffer.BlockCopy(salt, 0, nameWithSaltBytes, nameBytes.Length, salt.Length);

            int prefixLength = random.Next(minLength, maxLength);

            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(nameWithSaltBytes);
                StringBuilder hashString = new StringBuilder();
                foreach (var b in hashBytes)
                {
                    hashString.Append(b.ToString("x2"));
                }
                string hashPrefix = hashString.ToString().Substring(0, prefixLength);
                StringBuilder validFunctionName = new StringBuilder(hashPrefix);
                // 一番最初の文字が数字の場合は'_'を挿入する
                if (Char.IsDigit(validFunctionName[0]))
                {
                    validFunctionName.Insert(0, '_');
                }
                // 数字とアルファベット以外文字を'_'で書き換える
                for (int i = 0; i < validFunctionName.Length; i++)
                {
                    if (!Char.IsLetterOrDigit(validFunctionName[i]))
                    {
                        validFunctionName[i] = '_';
                    }
                }
                return validFunctionName.ToString();
            }
        }

        /// <summary>
        /// Application.versionでSeedを生成する
        /// </summary>
        /// <returns></returns>
        public static int GenerateSeed()
        {
            int seed = 0;
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] inputBytes = System.Text.Encoding.UTF8.GetBytes("Version = " + UnityEngine.Application.version + " ; seed");
                byte[] hashBytes = sha256.ComputeHash(inputBytes);
                seed = System.BitConverter.ToInt32(hashBytes, 0);
                seed = System.Math.Abs(seed) % int.MaxValue;
            }
            Debug.Log($"seed: {seed}");
            return seed;
        }

        /// <summary>
        /// 難読化処理で除外したいAssemblyか否か
        /// </summary>
        /// <param name="assembly"></param>
        /// <returns></returns>
        public static bool ExcludeTheAssembly(Assembly assembly)
        {
            try
            {
                // ScriptAsssembliesフォルダのAssemblyのみ処理
                var scriptAssembliesPath = System.IO.Path.Combine("Library", "ScriptAssemblies");
                if (!assembly.Location.Contains(scriptAssembliesPath))
                {
                    return true;
                }
                // Editor用のAssemblyをスキップ
                if (assembly.GetName().Name.EndsWith(".Editor"))
                {
                    return true;
                }
                // UnityとSystemのAssemblyをスキップ
                if (assembly.GetName().Name.StartsWith("Unity.") || assembly.GetName().Name.StartsWith("UnityEngine.") ||
                    assembly.GetName().Name.StartsWith("UnityEditor.") || assembly.GetName().Name.StartsWith("System."))
                {
                    return true;
                }
                // 空文字の場合はスキップ
                if (string.IsNullOrEmpty(assembly.Location))
                {
                    return true;
                }
                return false;
            }
            catch
            {
                return true;
            }
        }

        /// <summary>
        /// ラムダ式の匿名メソッドを取得する
        /// </summary>
        /// <param name="methodDefinition"></param>
        /// <returns></returns>
        public static List<MethodDefinition> GetAnomymousMethods(MethodDefinition methodDefinition)
        {
            List<MethodDefinition> result = new List<MethodDefinition>();

            foreach (var instruction in methodDefinition.Body.Instructions)
            {
                if (instruction.OpCode == OpCodes.Ldftn && instruction.Operand is MethodReference methodRef)
                {
                    if (methodRef.Name.Contains("<"))
                    {
                        Debug.Log($"{methodDefinition} => " + methodRef.Name);
                        var methodDef = methodRef.Resolve();
                        result.Add(methodDef);
                    }
                }
            }

            return result;
        }
    }
}
