#if !DISABLE_CONESHELL_WIN_INLINE_GLOBAL_METADATA || (!DISABLE_DECLANG && UNITY_2021_2_OR_NEWER) || !DISABLE_ENCRYPT_IL2CPP_API_NAME || !DISABLE_ENCRYPT_FUNCTION_NAME || !DISABLE_WINCHECKER
#define ENABLE_POST_VSSOLUTION_CREATED_PROCESS
#endif

using UnityEngine;
using UnityEditor;
using System.IO;
using System.Linq;

namespace LibNative
{
    public class PostVSSolutionCreatedOptions
    {
        public string DeClangHome { get; set; }
        public string PythonName { get; set; }
        public string MSBuildPath { get; set; }
        public string WindowsTargetPlatformVersion { get; set; }
        public string PlatformToolset { get; set; }
        public string MSBuildMsgEncoding { get; set; }

        public PostVSSolutionCreatedOptions()
        {
            DeClangHome = DeClang.Utility.GetDefaultDeClangHome();
            PythonName = "python";
            MSBuildPath = GetMSBuildPath();
            WindowsTargetPlatformVersion = "10.0";
            PlatformToolset = GetPlatformToolset();
            MSBuildMsgEncoding = "shift-jis";
        }

        /// <summary>
        /// MSBuild.exeパスを取得
        /// </summary>
        /// <returns></returns>
        static string GetMSBuildPath()
        {
            string programFilesX86 = System.Environment.ExpandEnvironmentVariables("%ProgramFiles(x86)%");
            if (Directory.Exists(programFilesX86))
            {
                /*string vswherePath = $@"{programFilesX86}\Microsoft Visual Studio\Installer\vswhere.exe";
                if (File.Exists(vswherePath))
                {
                    var res = BuildUtil.ExecuteShell(vswherePath, @"-latest -prerelease -products * -requires Microsoft.Component.MSBuild -find MSBuild\**\Bin\MSBuild.exe");
                    res = res.Replace("\n", "").Replace("\r", "");
                    if (File.Exists(res))
                    {
                        return res;
                    }
                }*/
            }
            return null;
        }

        /// <summary>
        /// PlatformToolsetを取得
        /// </summary>
        /// <returns></returns>
        static string GetPlatformToolset()
        {
            string programFilesX86 = System.Environment.ExpandEnvironmentVariables("%ProgramFiles(x86)%");
            if (Directory.Exists(programFilesX86))
            {
                string vswherePath = $@"{programFilesX86}\Microsoft Visual Studio\Installer\vswhere.exe";
                if (File.Exists(vswherePath))
                {
                    /*var vspath = BuildUtil.ExecuteShell(vswherePath, @"-latest -requires Microsoft.Component.MSBuild -property installationPath");
                    if (!string.IsNullOrEmpty(vspath))
                    {
                        vspath = vspath.Replace("\n", "").Replace("\r", "");
                        var vsPropsPath = $@"{vspath}\VC\Auxiliary\Build\Microsoft.VCToolsVersion.default.txt";
                        if (File.Exists(vsPropsPath))
                        {
                            var content = File.ReadAllText(vsPropsPath);
                            content = content.Replace(".", "");
                            return $"v{content.Substring(0, 3)}";
                        }
                    }*/
                }
            }
            return null;
        }
    }

    /// <summary>
    /// Visual Studio Solutionが作成された後の処理
    /// </summary>
    public class PostVSSolutionCreated
    {
        /// <summary>
        /// Visual Studio Solutionが作成された後の処理
        /// </summary>
        /// <param name="buildTarget"></param>
        /// <param name="outputPath"></param>
        /// <param name="deClangHome"></param>
        /// <param name="pythonName"></param>
        /// <param name="msBuildPath"></param>
        /// <param name="windowsTargetPlatformVersion"></param>
        /// <param name="platformToolset"></param>
        /// <param name="msBuildMsgEncoding"></param>
        public static void OnPostVSSolutionCreated(BuildTarget buildTarget, string outputPath, PostVSSolutionCreatedOptions options = null)
        {
#if ENABLE_POST_VSSOLUTION_CREATED_PROCESS
            Debug.Log("==== [Win] Post Visual Studio Solution Created ====");

            if (options == null)
            {
                options = new PostVSSolutionCreatedOptions();
            }

            // ---------------------
            // ビルド環境チェック

            // IL2CPPが必須
            var scriptingBackend = PlayerSettings.GetScriptingBackend(BuildTargetGroup.Standalone);
            if (scriptingBackend != ScriptingImplementation.IL2CPP)
            {
                Debug.LogError("Scripting Backedn must be IL2CPP.");
                return;
            }

            // Create Visual Studio Solutionが必須
            var createSolution = EditorUserBuildSettings.GetPlatformSettings("Standalone", "CreateSolution");
            if (createSolution != "true")
            {
                Debug.LogError("Create Visual Studio Solution must be ON.");
                return;
            }

            // x64が必須（x86はLibNativeがサポートしてない）
            if (buildTarget != BuildTarget.StandaloneWindows64)
            {
                Debug.LogError("Build Target must be StandaloneWindows64."); ;
            }


            // ------------------------
            // ビルドに必要な情報を設定

            // VSSolutionのルートパスを取得
            var vsSolutionPath = Path.GetDirectoryName(outputPath) + "\\";

            // UnityProjectのルートパスを取得
            var unityProjPath = Path.GetDirectoryName(Application.dataPath);

            // Solution Nameを取得
            var solutionFilePath = System.IO.Directory.GetFiles(vsSolutionPath).First(i => i.EndsWith(".sln"));
            Debug.Log("solutionFilePath: " + solutionFilePath);
            var solutionName = Path.GetFileNameWithoutExtension(solutionFilePath);
            Debug.Log("solutionName: " + solutionName);

            // build configを設定
            var config = PlayerSettings.GetIl2CppCompilerConfiguration(BuildTargetGroup.Standalone).ToString();
            var buildConfig = $"/p:Configuration={config};Platform=x64;SolutionDir=\"{vsSolutionPath}\\\";" +
                $"WindowsTargetPlatformVersion={options.WindowsTargetPlatformVersion};PlatformToolset={options.PlatformToolset}";

            // ---------------------------
            // ビルドする前の準備

            // Il2CppOutput objフォルダ削除
            var objDirectoryPath = Path.Combine(vsSolutionPath, "build", "obj", "il2cppOutputProject");
            if (Directory.Exists(objDirectoryPath))
            {
                Directory.Delete(objDirectoryPath, true);
            }

            // encodingを生成する
            var encoding = System.Text.Encoding.GetEncoding(options.MSBuildMsgEncoding);

            // コマンド実行結果
            string result;

            // !DISABLE_ENCRYPT_FUNCTION_NAME
            // 関数名難読化
            Il2CppGlobalMetaData.GlobalMetadata.EncryptFunctionName(outputPath);

            // !DISABLE_CONESHELL_WIN_INLINE_GLOBAL_METADATA
            // global-metadata.dat -> dat.c
            // MetadataLoader.cpp更新
            // global-metadata.dat削除
            //global::Coneshell.PostProcessBuild.InlineGlobalMetadataForWin(vsSolutionPath, unityProjPath, options.PythonName);

            // !DISABLE_ENCRYPT_IL2CPP_API_NAME
            // il2cpp api関数名の難読化
            Il2CppFunctionNameEncryptor.Encrypt(outputPath, config);

            // Il2CppOutputProjectビルド
            var il2cppOutputProjectPath = Path.Combine(vsSolutionPath, "Il2CppOutputProject", "Il2CppOutputProject.vcxproj");
            //result = BuildUtil.ExecuteShell(options.MSBuildPath, $"\"{il2cppOutputProjectPath}\" {buildConfig} -t:Clean,Build", encoding:encoding);
            //Debug.Log($"result: {result}");

            // !DISABLE_DECLANG && UNITY_2021_2_OR_NEWER
            // DeClangでAssembly-CSharp.cppを再ビルドする
            DeClang.PostProcessBuild.BuildAssembliesWithDeClang(vsSolutionPath, options.MSBuildPath, il2cppOutputProjectPath, buildConfig, encoding, options.DeClangHome, config);

            // UnityPlayerStubビルド
            var stubProjectPath = Path.Combine(vsSolutionPath, "UnityPlayerStub", "UnityPlayerStub.vcxproj");
            //result = BuildUtil.ExecuteShell(options.MSBuildPath, $"\"{stubProjectPath}\" {buildConfig} -t:Clean,Build", encoding:encoding);
            //Debug.Log($"result: {result}");

            // !DISABLE_WINCHECKER
            // WinChecker処理
            WinChecker.AddWinCheckerToCppProject(vsSolutionPath, config);

            // 本体Projectビルド
            var vsProjectPath = Path.Combine(vsSolutionPath, solutionName, solutionName + ".vcxproj");
            //result = BuildUtil.ExecuteShell(options.MSBuildPath, $"\"{vsProjectPath}\" {buildConfig} -t:Clean,Build", encoding: encoding);
            //Debug.Log($"result: {result}");
#endif
        }

        public static void CopyFilesToBin(string path)
        {
#if ENABLE_POST_VSSOLUTION_CREATED_PROCESS
            // ビルドした結果をbinフォルダにコピー
            var exeFileName = Path.GetFileName(path);
            var vsProjPath = Path.GetDirectoryName(path);
            Debug.Log($"exeFileName is {exeFileName}");
            var destinationPath = Path.Combine(vsProjPath, "bin");
            if (Directory.Exists(destinationPath))
            {
                Directory.Delete(destinationPath, true);
            }
            Directory.CreateDirectory(destinationPath);
            Debug.Log($"Create Folder {destinationPath}");

            var binPath = Path.Combine(vsProjPath, "build", "bin");
            var dirs = Directory.GetDirectories(binPath, "*_Data");
            if (dirs.Length > 0)
            {
                CopyDirectory(dirs[0], Path.Combine(destinationPath, Path.GetFileName(dirs[0])), true);
            }
            var config = PlayerSettings.GetIl2CppCompilerConfiguration(BuildTargetGroup.Standalone);
            var outputPath = Path.Combine(binPath, "x64", config.ToString());
            if (Directory.Exists(outputPath))
            {
                var exeFiles = Directory.GetFiles(outputPath, "*.exe");
                foreach (var item in exeFiles)
                {
                    File.Copy(item, Path.Combine(destinationPath, Path.GetFileName(item)), true);
                }
                var dllFiles = Directory.GetFiles(outputPath, "*.dll");
                foreach (var item in dllFiles)
                {
                    if (config == Il2CppCompilerConfiguration.Master && Path.GetFileNameWithoutExtension(item) == "WinPixEventRuntime")
                    {
                        continue;
                    }
                    File.Copy(item, Path.Combine(destinationPath, Path.GetFileName(item)), true);
                }
            }
            // x64なのでx86フォルダを削除する
            var pluginsDirectories = Directory.GetDirectories(destinationPath, "Plugins", SearchOption.AllDirectories);
            if (pluginsDirectories.Length > 0)
            {
                var pluginsDirectory = pluginsDirectories[0];
                if (Directory.Exists(Path.Combine(pluginsDirectory, "x86")))
                {
                    Directory.Delete(Path.Combine(pluginsDirectory, "x86"), true);
                }
            }
#endif
        }

        /// <summary>
        /// フォルダをコピーする
        /// </summary>
        /// <param name="sourceDir"></param>
        /// <param name="destinationDir"></param>
        /// <param name="recursive"></param>
        static void CopyDirectory(string sourceDir, string destinationDir, bool recursive)
        {
            var dirInfo = new DirectoryInfo(sourceDir);
            if (!dirInfo.Exists)
                Debug.LogError($"Source directory not found: {dirInfo.FullName}");

            Directory.CreateDirectory(destinationDir);

            foreach (var file in dirInfo.GetFiles())
            {
                string targetFilePath = Path.Combine(destinationDir, file.Name);
                file.CopyTo(targetFilePath);
            }

            if (recursive)
            {
                foreach (var subDir in dirInfo.GetDirectories())
                {
                    string newDestinationDir = Path.Combine(destinationDir, subDir.Name);
                    CopyDirectory(subDir.FullName, newDestinationDir, true);
                }
            }
        }

        public static void RemoveBuildFiles(string path)
        {
#if ENABLE_POST_VSSOLUTION_CREATED_PROCESS
            var vsProjPath = Path.GetDirectoryName(path);
            var binDirectoryPath = Path.Combine(vsProjPath, "bin");
            CopyDirectory(binDirectoryPath, vsProjPath + "_temp", true);
            Directory.Delete(vsProjPath, true);
            Directory.Move(vsProjPath + "_temp", vsProjPath);
#endif
        }
    }
}
