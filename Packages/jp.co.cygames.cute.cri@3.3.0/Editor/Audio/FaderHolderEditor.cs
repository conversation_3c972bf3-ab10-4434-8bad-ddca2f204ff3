using UnityEngine;
using UnityEditor;

namespace Cute.Cri
{
    /// <summary>
    /// FaderHolderEditorのカスタムエディター
    /// </summary>
    [CustomEditor(typeof(FaderHolder))]
    public class FaderHolderEditor : Editor
    {
        private FaderHolder faderHolder = null;
        private bool showBusVolumeFader = true;
        private bool showCategoryAisacControl = true;

        private void OnEnable()
        {
            faderHolder = target as FaderHolder;
        }

        public override void OnInspectorGUI()
        {
            if (faderHolder == null)
            {
                return;
            }

            showBusVolumeFader = EditorGUILayout.Foldout(showBusVolumeFader, "BusVolumeFaderList");
            if (showBusVolumeFader)
            {
                foreach (var fader in faderHolder.BusVolumeFaderDict)
                {
                    using (new EditorGUILayout.VerticalScope(GUI.skin.box))
                    {
                        EditorGUILayout.TextField("BusName", fader.Value.BusName);
                        EditorGUILayout.TextField("FaderStatus", fader.Value.Status.ToString());
                        EditorGUILayout.Slider("SendLevel", fader.Value.ElapsedLevel, 0.0f, 1.0f);
                    }
                }
            }

            showCategoryAisacControl = EditorGUILayout.Foldout(showCategoryAisacControl, "CategoryAisacControlFaderList");
            if (showCategoryAisacControl)
            {
                foreach (var fader in faderHolder.CategoryAisacControlList)
                {
                    using (new EditorGUILayout.VerticalScope(GUI.skin.box))
                    {
                        EditorGUILayout.TextField("CategoryName", fader.CategoryName);
                        EditorGUILayout.TextField("ControlName", fader.ControlName);
                        EditorGUILayout.TextField("FaderStatus", fader.Status.ToString());
                        EditorGUILayout.Slider("ControlValue", fader.ElapsedValue, 0.0f, 1.0f);
                    }
                }
            }

            EditorUtility.SetDirty(faderHolder);
        }
    }
}
