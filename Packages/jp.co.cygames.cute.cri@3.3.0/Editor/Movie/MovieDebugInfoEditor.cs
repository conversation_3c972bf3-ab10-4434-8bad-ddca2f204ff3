using UnityEngine;
using UnityEditor;

namespace Cute.Cri
{
    [CustomEditor(typeof(MovieDebugInfo))]
    public class MovieDebugInfoEditor : Editor
    {
#if CUTE_DEBUG
        private MovieDebugInfo movieDebugInfo = null;

        private void OnEnable()
        {
            movieDebugInfo = target as MovieDebugInfo;
        }

        public override void OnInspectorGUI()
        {
            if (movieDebugInfo == null)
            {
                return;
            }

            var playerList = movieDebugInfo.PlayerDic;

            foreach (var player in playerList.Values)
            {
                using (new EditorGUILayout.VerticalScope(GUI.skin.box))
                {
                    EditorGUILayout.LongField("PlayerId", player.MoviePlayerHandle.PlayerId);
                    EditorGUILayout.ObjectField("MoviePlayer", player.Transform, typeof(Transform), false);

                    EditorGUILayout.FloatField("AtAwakeTime", player.AtAwakeTime);

                    EditorGUILayout.TextField("MoviePath", player.MoviePath);
#if CUTE_DEBUG
                    EditorGUILayout.TextField("MovieAssetPath", player.MovieAssetPath);
#endif
                    EditorGUILayout.TextField("RepeatMoviePath", player.RepeatMoviePath);

                    EditorGUILayout.Vector2Field("ScreenSize", player.ScreenSize);
                    EditorGUILayout.RectField("ImageUvRect", player.ImageUvRect);

                    EditorGUILayout.Toggle("Loop", player.Loop);
                    EditorGUILayout.Toggle("IsPrepareAfterLoading", player.IsPrepareAfterLoading);
                    EditorGUILayout.Toggle("IsTargetForReycast", player.IsTargetForReycast);

                    EditorGUILayout.TextField("SortingLayerName", player.SortingLayerName);
                    EditorGUILayout.IntField("SortingOrder", player.SortingOrder);

                    EditorGUILayout.FloatField("Duration(frame)", player.GetDurationFrame());
                    EditorGUILayout.FloatField("Duration(sec)", player.GetDurationTime());
                    EditorGUILayout.FloatField("Seek Position(frame)", player.GetSeekPositionFrame());
                    EditorGUILayout.FloatField("Seek Position(sec)", player.GetSeekPositionTime());
                    EditorGUILayout.FloatField("PlayTime(sec)", player.GetPlayTime());
                    EditorGUILayout.IntField("LoopedCount", (int)player.GetLoopedCount());

                    EditorGUILayout.TextField("CriMana Player Status", player.Player.status.ToString());

                    EditorGUILayout.TextField("Proc Status", player.ProcStatus.ToString());

                    if (player.MovieInfo != null)
                    {
                        using (new EditorGUILayout.VerticalScope(GUI.skin.box))
                        {
                            EditorGUILayout.LabelField("MovieInfo");

                            EditorGUILayout.IntField("width", (int)player.MovieInfo.width);
                            EditorGUILayout.IntField("height", (int)player.MovieInfo.height);
                            EditorGUILayout.IntField("dispWidth", (int)player.MovieInfo.dispWidth);
                            EditorGUILayout.IntField("dispHeight", (int)player.MovieInfo.dispHeight);
                            float framerate = (float)player.MovieInfo.framerateN / player.MovieInfo.framerateD;
                            EditorGUILayout.FloatField("framerate", framerate);
                            EditorGUILayout.IntField("totalFrames", (int)player.MovieInfo.totalFrames);

                            EditorGUILayout.Toggle("hasAlpha", player.MovieInfo.hasAlpha);
                            EditorGUILayout.EnumPopup("codecType", player.MovieInfo.codecType);
                            EditorGUILayout.EnumPopup("alphaCodecType", player.MovieInfo.alphaCodecType);

                            EditorGUILayout.IntField("maxChunkSize", (int)player.MovieInfo.maxChunkSize);

                            for (int i = 0; i < player.MovieInfo.audioPrm.Length; i++)
                            {
                                if (player.MovieInfo.audioPrm[i].numChannels > 0)
                                {
                                    using (new EditorGUILayout.VerticalScope(GUI.skin.box))
                                    {
                                        EditorGUILayout.IntField("audioTrack", i);
                                        EditorGUILayout.IntField("samplingRate", (int)player.MovieInfo.audioPrm[i].samplingRate);
                                        EditorGUILayout.IntField("numChannels", (int)player.MovieInfo.audioPrm[i].numChannels);
                                        EditorGUILayout.IntField("totalSamples", (int)player.MovieInfo.audioPrm[i].totalSamples);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            EditorUtility.SetDirty(movieDebugInfo);
        }
#endif
    }
}
