/****************************************************************************
 *
 * Copyright (c) 2019 CRI Middleware Co., Ltd.
 *
 ****************************************************************************/
#if UNITY_2019_3_OR_NEWER
#define CRI_ENABLE_LIPS_DEFORMER
#endif

using UnityEngine;

/**
 * \addtogroup CRILIPS_UNITY_COMPONENT
 * @{
 */

namespace CriWare {

/**
 * \deprecated
 * Unity2019.3 〜 では削除予定の非推奨APIです。
 * CriLipsDeformer コンポーネントの使用を検討してください。
 * <summary>LipSync解析結果をブレンドシェイプへ流し込むためのコンポーネントです。</summary>
 * <remarks>
 * <para header='説明'>LipSync解析結果をブレンドシェイプへ流し込むためのコンポーネントです。<br/>
 * エディタ上で設定したブレンドシェイプへ解析結果を渡す基本クラスです。<br/>
 * 本クラスを継承することで、サウンドの入力方法に応じてLipSync解析結果を反映することが可能です。<br/>
 * 本コンポーネントはLipSync解析結果をブレンドシェイプへ渡すだけなので、貼り付けて使用しても何も表示されません。<br/>
 * 通常は継承先の、CriLipsShapeForAtomSource コンポーネントを使用してください。<br/></para>
 * <para header='注意'>本クラスでは、設定されたブレンドシェイプの組み合わせのみシェイプさせます。<br/>
 * 複数のLipSync解析結果を組み合わせてブレンドシェイプの制御を行う場合は、
 * 直接解析結果からブレンドシェイプを操作してください。<br/></para>
 * </remarks>
 */
#if CRI_ENABLE_LIPS_DEFORMER
[System.Obsolete("Use CriLipsDeformer Component")]
#else
[AddComponentMenu("CRIWARE/CriLipsShape")]
#endif
public class CriLipsShape : CriMonoBehaviour
{
	#region Properties
	public enum MorphingTargetType {
		BlendShape = 0,
		Animation,
	}
	public MorphingTargetType morphingTargetType {
		get {
			return _morphingTargetType;
		}
		set {
			_morphingTargetType = value;
		}
	}

	/**
	 * <summary>LipSync解析結果適用先ブレンドシェイプタイプ</summary>
	 */
	public enum BlendShapeType {
		WidthHeight = 0,    /**< 縦横タイプ */
		JapaneseAIUEO,      /**< 日本語5母音タイプ */
	}

	public BlendShapeType blendShapeType {
		get {
			return _blendShapeType;
		}
		set {
			_blendShapeType = value;
		}
	}

	/**
	 * <summary>解析結果改変用デリゲート</summary>
	 */
	public delegate void UserModifyDelegateFunction(ref CriLipsMouth.Info info, ref CriLipsMouth.MorphTargetBlendAmountAsJapanese morph, ICriLipsAnalyzeModule analyzeModule);

	public UserModifyDelegateFunction UserModifyDelegate = null;

	#endregion

	#region Internal Variables
	[SerializeField]
	private MorphingTargetType _morphingTargetType = MorphingTargetType.BlendShape;
	[SerializeField]
	private BlendShapeType _blendShapeType = CriLipsShape.BlendShapeType.WidthHeight;
	[SerializeField]
	public SkinnedMeshRenderer skinnedMeshRenderer;
	[SerializeField]
	public CriLipsMeshMorph.BlendShapeNameMapping nameMapping;
	[SerializeField]
	public Animator animator;
	[SerializeField]
	public CriLipsMeshMorph.BlendShapeNameMapping animationStateNameMapping;
	protected CriLipsMeshMorph meshMorphing = null;
	protected CriLipsMouth.Info info;
	protected CriLipsMouth.MorphTargetBlendAmountAsJapanese blendAmount;
	protected ICriLipsAnalyzeModule analyzeModule = null;
	#endregion

	#region Functions
	/**
	 * <summary>SkinnedMeshRendererからモーフィング用クラスを生成します。</summary>
	 * <remarks>
	 * <para header='説明'>インスペクタ上で設定されたSkinnedMeshRendererからモーフィング用クラスを生成します。<br/></para>
	 * </remarks>
	 */
	protected virtual void StartForMorphing(CriLipsMouth.Info silenceInfo) {
		if (skinnedMeshRenderer == null &&
			animator == null) {
			return;
		}
		if (meshMorphing != null) {
			Debug.LogError("[CRIWARE] There is already existed CriLipsMeshMorph instance.");
			return;
		}
		switch (morphingTargetType) {
			case MorphingTargetType.BlendShape:
				if (skinnedMeshRenderer == null) {
					Debug.LogError("[CRIWARE] skinnedMeshRenderer is not found.");
					return;
				}
				this.meshMorphing = new CriLipsMeshMorph(skinnedMeshRenderer, nameMapping, silenceInfo.lipWidth);
				break;
			case MorphingTargetType.Animation:
				if (animator == null) {
					Debug.LogError("[CRIWARE] animator is not found.");
					return;
				}
				this.meshMorphing = new CriLipsMeshMorph(animator, animationStateNameMapping, silenceInfo.lipWidth);
				break;
			default:
				break;
		}
	}

	/**
	 * <summary>縦横タイプでモーフィングを行います。</summary>
	 * <remarks>
	 * <para header='説明'>ブレンドシェイプを縦横タイプでモーフィングさせます。<br/>
	 * ブレンドシェイプ先は CriLipsShape.StartForMorphing が呼ばれた時点での情報を使用します。<br/></para>
	 * </remarks>
	 */
	protected virtual void UpdateLipsParamerterForBelndShape(ref CriLipsMouth.Info info) {
		if (meshMorphing == null) {
			return;
		}
		meshMorphing.Update(ref info);
	}

	/**
	 * <summary>日本語5母音タイプでモーフィングを行います。</summary>
	 * <remarks>
	 * <para header='説明'>ブレンドシェイプを日本語5母音タイプでモーフィングさせます。<br/>
	 * ブレンドシェイプ先は CriLipsShape.StartForMorphing が呼ばれた時点での情報を使用します。<br/></para>
	 * </remarks>
	 */
	protected virtual void UpdateLipsParamerterForBelndShape(ref CriLipsMouth.MorphTargetBlendAmountAsJapanese blendAmount) {
		if (meshMorphing == null) {
			return;
		}
		meshMorphing.Update(ref blendAmount);
	}

	protected virtual void UpdateLipsParameter() {
		if (UserModifyDelegate != null && analyzeModule != null) {
			UserModifyDelegate(ref info, ref blendAmount, analyzeModule);
		}

		switch (blendShapeType) {
			case CriLipsShape.BlendShapeType.WidthHeight: {
				UpdateLipsParamerterForBelndShape(ref info);
			}
			break;
			case CriLipsShape.BlendShapeType.JapaneseAIUEO: {
				UpdateLipsParamerterForBelndShape(ref blendAmount);
			}
			break;
			default:
				break;
		}
	}

	public override void CriInternalUpdate() { }
	public override void CriInternalLateUpdate() { }

	#endregion
}

} //namespace CriWare
/**
 * @}
 */

/* end of file */
